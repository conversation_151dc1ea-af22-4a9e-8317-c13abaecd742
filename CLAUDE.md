# Braintrust Project Guide for Claude

## Project Overview

This is a monorepo for Braintrust, an AI/ML evaluation and observability platform. The project uses pnpm workspaces to manage multiple packages.

## Key Technologies

- **Package Manager**: pnpm (NOT npm or yarn)
- **Frontend Framework**: Next.js (React)
- **Language**: TypeScript
- **Monorepo Structure**: pnpm workspaces

## Project Structure

```
/
├── app/                    # Main Next.js web application
├── api-ts/                 # TypeScript API
├── sdk/                    # SDKs for various languages
├── pnpm-workspace.yaml     # Workspace configuration
└── ...other packages
```

## Important Commands

When working in this codebase, always:

1. Use `pnpm` instead of `npm` or `yarn`
2. Navigate to the correct workspace directory before installing packages
3. For the main app: `cd app && pnpm add <package>`

## Adding New Features to the Data Editor

The data editor (`app/ui/data-text-editor.tsx`) supports multiple viewing modes for different data types. To add a new viewer:

1. **Update Parse Options** (`app/utils/parse.ts`):

   - Add the new mode to `renderOptionNames` array
   - Add configuration to `renderOptions` object with:
     - `name`: Display name
     - `filter`: Function to determine if data can use this mode
     - `guess`: Function to auto-select this mode
     - `readOnly`: Whether the mode is read-only
     - `serialize`/`deserialize`: Optional data transformation functions

2. **Update UI Component** (`app/ui/data-text-editor.tsx`):

   - Import necessary components and styles
   - Add rendering logic in the main switch statement
   - Update `isCollapsible` condition if the mode shouldn't support folding
   - Update cursor and error styling conditions for read-only modes

3. **Type Safety**: Ensure proper TypeScript types are used, especially when passing data to third-party components

## Code Style Guidelines

- The codebase avoids unnecessary comments (DO NOT add comments unless specifically requested)
- Use existing patterns and conventions found in neighboring code
- Always check if a library is already used before adding it
- Follow the existing import structure and naming conventions

### TypeScript and Linting

- Do not use `any`, prefer specific types or `unknown`. Each package has `eslint` rules to enforce this.
- Always run linting before submitting changes: `pnpm lint` in the relevant directory
- Common lint rules to follow:
  - Avoid type assertions (`as`) - use proper type guards instead
  - Remove unused variables from catch blocks (use `catch {}` instead of `catch (e) {}`)
  - Use proper type annotations for function parameters and return types
  - Prefer `const` over `let` when variables aren't reassigned

## Common Gotchas

1. **Workspace Dependencies**: When adding packages, you might see "ERR_PNPM_ADDING_TO_ROOT". Navigate to the specific workspace directory first.

2. **Type Checking**: Run `pnpm tsc --noEmit` in the app directory to check for TypeScript errors.

3. **Linting**: The project has ESLint configured. Some files may have eslint-disable comments - don't remove these without understanding why they're there.

4. **Read-Only Modes**: Some viewing modes (like markdown, html, tree, jsondive) are read-only. Ensure UI properly reflects this.

## Testing Changes

Before committing:

1. Run type checking: `pnpm tsc --noEmit` in the relevant directory
2. Run linting: `pnpm lint` in the relevant directory
3. Fix any lint errors before finalizing changes
4. **DO NOT** attempt to run the web server (`pnpm dev`, `pnpm start`, etc.) - the user will handle this
5. Focus on static analysis: type checking, linting, and code review
6. The user will test the UI manually to ensure the feature works as expected
7. **NEVER** run `pnpm build` in the root directory - it's too slow and builds all packages. Always cd to the specific package directory first

## Type Checking and Linting

### TypeScript Type Checking

Always verify your code passes TypeScript type checking:

```bash
# Check all files in the app directory
cd app && pnpm tsc --noEmit

# Check specific files only
pnpm tsc --noEmit path/to/file.ts path/to/another.tsx
```

### ESLint

Always ensure your code passes ESLint without errors:

```bash
# Run ESLint on specific files
npx eslint path/to/file.ts --max-warnings=0

# Run ESLint on multiple files
npx eslint file1.ts file2.tsx file3.ts --max-warnings=0
```

### Common Type Issues and Solutions

1. **Event Listener Types**: When working with WebSocket or DOM event listeners, use the appropriate event types:

   - Use `MessageEvent` for WebSocket message handlers
   - Use `CloseEvent` for WebSocket close handlers
   - Use `Event` for generic event handlers

2. **Type Assertions**: Avoid type assertions (`as`) when possible. ESLint will flag these with `@typescript-eslint/consistent-type-assertions`. Instead:

   - Use proper typing from the start
   - Use type guards or type predicates
   - If absolutely necessary, use `eslint-disable-next-line` with justification

3. **Any Types**: Avoid using `any`. If unavoidable, use `eslint-disable-next-line @typescript-eslint/no-explicit-any` with a comment explaining why.

4. **React Hook Dependencies**: Always include all dependencies in React hooks. If you need to exclude a dependency, use `eslint-disable-next-line react-hooks/exhaustive-deps` with justification.

### Build Verification

The project uses Next.js build process which includes type checking. If you encounter build errors:

1. First run `pnpm tsc --noEmit` to see TypeScript errors clearly
2. Fix all TypeScript errors
3. Then run `npx eslint <files> --max-warnings=0` to fix linting issues
4. The build should pass after fixing both TypeScript and ESLint errors

## Example: Adding JSONDive Viewer

As an example, here's what was done to add the JSONDive viewer:

1. Added package: `cd app && pnpm add @jsondive/viewer`
2. Updated `renderOptionNames` and `renderOptions` in parse.ts
3. Added import and rendering logic in data-text-editor.tsx
4. Handled type requirements (JSONDive expects Record<string, unknown>)
5. Updated UI conditions for read-only behavior

This pattern can be followed for adding other viewer types in the future.
