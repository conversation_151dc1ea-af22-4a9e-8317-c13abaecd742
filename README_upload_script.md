# CSV Batch Upload <PERSON><PERSON><PERSON> for Braintrust

This script uploads large CSV files to Braintrust datasets in manageable batches to handle memory constraints and API limits.

## Features

- **Batch Processing**: Uploads records in configurable batch sizes (default: 25 records)
- **J<PERSON><PERSON> Parsing**: Automatically parses JSON strings in CSV fields
- **Error Handling**: Continues processing even if individual rows fail
- **Progress Tracking**: Shows progress with batch numbers and total records processed
- **Flexible Configuration**: Customizable field names and batch sizes
- **Rate Limiting**: Built-in delays between batches to avoid overwhelming the API

## Requirements

```bash
pip install braintrust
```

## Usage

### Basic Usage

```bash
python upload_csv_batch.py "My Project" "My Dataset"
```

This will upload `longmemeval_m_full.csv` to a dataset named "My Dataset" in project "My Project" using batches of 25 records.

### Advanced Usage

```bash
# Custom batch size
python upload_csv_batch.py "My Project" "My Dataset" --batch-size 50

# Custom CSV file
python upload_csv_batch.py "My Project" "My Dataset" --csv-file "my_data.csv"

# Custom field names
python upload_csv_batch.py "My Project" "My Dataset" \
  --input-field "question" \
  --expected-field "answer" \
  --metadata-field "context"
```

### Command Line Arguments

- `project_name`: Braintrust project name (required)
- `dataset_name`: Braintrust dataset name (required)
- `--csv-file`: Path to CSV file (default: `longmemeval_m_full.csv`)
- `--batch-size`: Records per batch (default: 25)
- `--input-field`: Input column name (default: `input`)
- `--expected-field`: Expected output column name (default: `expected`)
- `--metadata-field`: Metadata column name (default: `metadata`)

## CSV Format

The script expects a CSV file with at least three columns:
- **input**: The input data for your model/evaluation
- **expected**: The expected output/answer
- **metadata**: Additional metadata (should be JSON format)

Example CSV structure:
```csv
input,expected,metadata
"What is 2+2?","4","{""difficulty"": ""easy"", ""topic"": ""math""}"
"Explain gravity","Gravity is a force...","{""difficulty"": ""medium"", ""topic"": ""physics""}"
```

## Environment Setup

Make sure you have your Braintrust API key set up:

```bash
export BRAINTRUST_API_KEY="your-api-key-here"
```

Or set it in your environment however you prefer.

## Output

The script provides detailed progress information:

```
Initializing dataset 'My Dataset' in project 'My Project'...
CSV columns found: ['input', 'expected', 'metadata']
Processing CSV in batches of 25 records...
Processing batch 1 (rows 1-25)...
✓ Batch 1 uploaded successfully. Total processed: 25
Processing batch 2 (rows 26-50)...
✓ Batch 2 uploaded successfully. Total processed: 50
...
🎉 Upload complete! Total records processed: 500
```

## Error Handling

- If individual rows fail to process, the script logs the error and continues
- The script validates that required columns exist in the CSV
- Keyboard interruption (Ctrl+C) is handled gracefully

## Performance Considerations

- **Batch Size**: Larger batches are more efficient but use more memory. 25 is a good default for large files.
- **Rate Limiting**: The script includes a 500ms delay between batches to avoid API rate limits
- **Memory Usage**: The script processes one batch at a time to minimize memory usage

## Troubleshooting

### Common Issues

1. **Missing columns**: Ensure your CSV has the required column names
2. **JSON parsing errors**: Check that metadata fields contain valid JSON
3. **API rate limits**: Increase the delay between batches if you encounter rate limiting
4. **Memory issues**: Reduce batch size if you encounter memory problems

### Large Files

For very large files (like the 2.5GB `longmemeval_m_full.csv`):
- Use the default batch size of 25 or smaller
- Monitor memory usage during upload
- Consider running the script in a screen/tmux session for long uploads

## Example for longmemeval_m_full.csv

```bash
# Upload the longmemeval file to a new dataset
python upload_csv_batch.py "LongMemEval" "Full Dataset" --batch-size 25

# This will process all 500 records in batches of 25
# Total time: approximately 10-15 minutes depending on network speed
```
