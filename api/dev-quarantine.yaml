### THIS IS AN AUTO GENERATED FILE. IF YOU EDIT IT, YOUR CHANGES WILL BE OVERWRITTEN ###

Parameters:
  QuarantinePrivateSubnet1AZ:
    Default: ""
    Description: Availability zone for the private subnet (defaults to 1th AZ)
    Type: String
  QuarantinePrivateSubnet1CIDR:
    Default: **********/24
    Description: CIDR for the private subnet
    Type: String
  QuarantinePrivateSubnet2AZ:
    Default: ""
    Description: Availability zone for the private subnet (defaults to 2th AZ)
    Type: String
  QuarantinePrivateSubnet2CIDR:
    Default: **********/24
    Description: CIDR for the private subnet
    Type: String
  QuarantinePrivateSubnet3AZ:
    Default: ""
    Description: Availability zone for the private subnet (defaults to 3th AZ)
    Type: String
  QuarantinePrivateSubnet3CIDR:
    Default: **********/24
    Description: CIDR for the private subnet
    Type: String
  QuarantinePublicSubnet1AZ:
    Default: ""
    Description: Availability zone for the public subnet (defaults to 0th AZ)
    Type: String
  QuarantinePublicSubnet1CIDR:
    Default: **********/24
    Description: CIDR for the public subnet
    Type: String
  QuarantineVPCCIDR:
    Default: ""
    Description: CIDR for the VPC
    Type: String
Conditions:
  HasPrivateSubnet1AZ:
    Fn::Not:
      - Fn::Equals:
          - Ref: QuarantinePrivateSubnet1AZ
          - ""
  HasPrivateSubnet2AZ:
    Fn::Not:
      - Fn::Equals:
          - Ref: QuarantinePrivateSubnet2AZ
          - ""
  HasPrivateSubnet3AZ:
    Fn::Not:
      - Fn::Equals:
          - Ref: QuarantinePrivateSubnet3AZ
          - ""
  HasPublicSubnet1AZ:
    Fn::Not:
      - Fn::Equals:
          - Ref: QuarantinePublicSubnet1AZ
          - ""
  HasQuarantine:
    Fn::Not:
      - Fn::Equals:
          - Ref: QuarantineVPCCIDR
          - ""
Resources:
  APIHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - QuarantineUser
                  - Arn
            Sid: ""
        Version: "2012-10-17"
      Policies:
        - PolicyDocument:
            Statement:
              - Action:
                  - lambda:CreateFunction
                  - lambda:PublishVersion
                Effect: Allow
                Sid: QuarantinePublish
                Resource:
                  - Fn::Sub: arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*
              - Action:
                  - lambda:CreateFunction
                  - lambda:PublishVersion
                Effect: Deny
                Sid: EnforceQuarantineVPC
                Resource: "*"
                Condition:
                  StringNotEquals:
                    lambda:VpcIds:
                      - Ref: QuarantinepubPrivateVPC
              - Action:
                  - lambda:TagResource
                Effect: Allow
                Sid: TagQuarantine
                Resource: "*"
                Condition:
                  StringEquals:
                    aws:ResourceTag/BraintrustQuarantine: "true"
              - Action:
                  - lambda:DeleteFunction
                  - lambda:UpdateFunctionCode
                  - lambda:UpdateFunctionConfiguration
                  - lambda:GetFunctionConfiguration
                Effect: Allow
                Resource: "*"
                Condition:
                  StringEquals:
                    aws:ResourceTag/BraintrustQuarantine: "true"
              - Action: iam:PassRole
                Effect: Allow
                Resource:
                  - Fn::GetAtt:
                      - QuarantineFunctionRole
                      - Arn
              - Action: s3:*
                Effect: Allow
                Resource:
                  - Fn::Sub: arn:aws:s3:::${CodeBundleBucket}
                  - Fn::Sub: arn:aws:s3:::${CodeBundleBucket}/*
              - Action:
                  - ec2:DescribeSecurityGroups
                  - ec2:DescribeSubnets
                  - ec2:DescribeVpcs
                Effect: Allow
                Resource: "*"
            Version: "2012-10-17"
          PolicyName: APIHandlerRolePolicy
  CodeBundleBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
  QuarantineInvokeRole:
    Type: AWS::IAM::Role
    Condition: HasQuarantine
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - APIHandlerRole
                  - Arn
      Policies:
        - PolicyDocument:
            Statement:
              - Action: lambda:InvokeFunction
                Effect: Allow
                Resource: "*"
                Condition:
                  StringEquals:
                    aws:ResourceTag/BraintrustQuarantine: "true"
            Version: "2012-10-17"
          PolicyName: QuarantineInvokeRolePolicy
  QuarantineFunctionRole:
    Type: AWS::IAM::Role
    Condition: HasQuarantine
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Sid: ""
        Version: "2012-10-17"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
  QuarantineUser:
    Type: AWS::IAM::User
    Properties:
      UserName: QuarantineUser
  QuarantineUserAccessKey:
    Type: AWS::IAM::AccessKey
    Properties:
      UserName:
        Ref: QuarantineUser
  QuarantinepubPrivateVPC:
    Condition: HasQuarantine
    Properties:
      CidrBlock:
        Ref: QuarantineVPCCIDR
      Tags:
        - Key: Name
          Value:
            Ref: AWS::StackName
    Type: AWS::EC2::VPC
  QuarantinepublicSubnet1:
    Condition: HasQuarantine
    Properties:
      AvailabilityZone:
        Fn::If:
          - HasPublicSubnet1AZ
          - Ref: QuarantinePublicSubnet1AZ
          - Fn::Select:
              - 0
              - Fn::GetAZs: ""
      CidrBlock:
        Ref: QuarantinePublicSubnet1CIDR
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value:
            Fn::Join:
              - "-"
              - - Ref: AWS::StackName
                - Quarantine
                - public-subnet-a
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::Subnet
  QuarantineprivateSubnet1:
    Condition: HasQuarantine
    Properties:
      AvailabilityZone:
        Fn::If:
          - HasPrivateSubnet1AZ
          - Ref: QuarantinePrivateSubnet1AZ
          - Fn::Select:
              - 0
              - Fn::GetAZs: ""
      CidrBlock:
        Ref: QuarantinePrivateSubnet1CIDR
      MapPublicIpOnLaunch: false
      Tags:
        - Key: Name
          Value:
            Fn::Join:
              - "-"
              - - Ref: AWS::StackName
                - Quarantine
                - private-subnet-a
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::Subnet
  QuarantineprivateSubnet2:
    Condition: HasQuarantine
    Properties:
      AvailabilityZone:
        Fn::If:
          - HasPrivateSubnet2AZ
          - Ref: QuarantinePrivateSubnet2AZ
          - Fn::Select:
              - 1
              - Fn::GetAZs: ""
      CidrBlock:
        Ref: QuarantinePrivateSubnet2CIDR
      MapPublicIpOnLaunch: false
      Tags:
        - Key: Name
          Value:
            Fn::Join:
              - "-"
              - - Ref: AWS::StackName
                - Quarantine
                - private-subnet-b
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::Subnet
  QuarantineprivateSubnet3:
    Condition: HasQuarantine
    Properties:
      AvailabilityZone:
        Fn::If:
          - HasPrivateSubnet3AZ
          - Ref: QuarantinePrivateSubnet3AZ
          - Fn::Select:
              - 2
              - Fn::GetAZs: ""
      CidrBlock:
        Ref: QuarantinePrivateSubnet3CIDR
      MapPublicIpOnLaunch: false
      Tags:
        - Key: Name
          Value:
            Fn::Join:
              - "-"
              - - Ref: AWS::StackName
                - Quarantine
                - private-subnet-c
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::Subnet
  QuarantineinternetGateway:
    Condition: HasQuarantine
    Properties:
      Tags:
        - Key: Name
          Value:
            Fn::Join:
              - "-"
              - - Ref: AWS::StackName
                - Quarantine
                - gateway
    Type: AWS::EC2::InternetGateway
  QuarantinegatewayToInternet:
    Condition: HasQuarantine
    Properties:
      InternetGatewayId:
        Ref: QuarantineinternetGateway
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::VPCGatewayAttachment
  QuarantinepublicRouteTable:
    Condition: HasQuarantine
    Properties:
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::RouteTable
  QuarantinepublicRoute:
    Condition: HasQuarantine
    DependsOn: QuarantinegatewayToInternet
    Properties:
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId:
        Ref: QuarantineinternetGateway
      RouteTableId:
        Ref: QuarantinepublicRouteTable
    Type: AWS::EC2::Route
  QuarantinepublicSubnet1RouteTableAssociation:
    Condition: HasQuarantine
    Properties:
      RouteTableId:
        Ref: QuarantinepublicRouteTable
      SubnetId:
        Ref: QuarantinepublicSubnet1
    Type: AWS::EC2::SubnetRouteTableAssociation
  QuarantinenatGateway:
    Condition: HasQuarantine
    DependsOn: QuarantinenatPublicIP
    Properties:
      AllocationId:
        Fn::GetAtt:
          - QuarantinenatPublicIP
          - AllocationId
      SubnetId:
        Ref: QuarantinepublicSubnet1
    Type: AWS::EC2::NatGateway
  QuarantinenatPublicIP:
    Condition: HasQuarantine
    DependsOn: QuarantinepubPrivateVPC
    Properties:
      Domain: vpc
    Type: AWS::EC2::EIP
  QuarantineprivateRouteTable:
    Condition: HasQuarantine
    Properties:
      VpcId:
        Ref: QuarantinepubPrivateVPC
    Type: AWS::EC2::RouteTable
  QuarantineprivateRoute:
    Condition: HasQuarantine
    Properties:
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId:
        Ref: QuarantinenatGateway
      RouteTableId:
        Ref: QuarantineprivateRouteTable
    Type: AWS::EC2::Route
  QuarantineprivateSubnet1RouteTableAssociation:
    Condition: HasQuarantine
    Properties:
      RouteTableId:
        Ref: QuarantineprivateRouteTable
      SubnetId:
        Ref: QuarantineprivateSubnet1
    Type: AWS::EC2::SubnetRouteTableAssociation
  QuarantineprivateSubnet2RouteTableAssociation:
    Condition: HasQuarantine
    Properties:
      RouteTableId:
        Ref: QuarantineprivateRouteTable
      SubnetId:
        Ref: QuarantineprivateSubnet2
    Type: AWS::EC2::SubnetRouteTableAssociation
  QuarantineprivateSubnet3RouteTableAssociation:
    Condition: HasQuarantine
    Properties:
      RouteTableId:
        Ref: QuarantineprivateRouteTable
      SubnetId:
        Ref: QuarantineprivateSubnet3
    Type: AWS::EC2::SubnetRouteTableAssociation
Outputs:
  QuarantineUserAccessKeyId:
    Value:
      Ref: QuarantineUserAccessKey
  QuarantineUserSecretAccessKey:
    Value:
      Fn::GetAtt:
        - QuarantineUserAccessKey
        - SecretAccessKey
  QuarantinepubPrivateVPCId:
    Description: ID of the QuarantinepubPrivateVPC
    Value:
      Ref: QuarantinepubPrivateVPC
    Condition: HasQuarantine
  QuarantinepubPrivateVPCDefaultSecurityGroup:
    Description: Default Security Group ID of the QuarantinepubPrivateVPC
    Value:
      Fn::GetAtt:
        - QuarantinepubPrivateVPC
        - DefaultSecurityGroup
    Condition: HasQuarantine
  QuarantineprivateSubnet1Id:
    Description: ID of QuarantineprivateSubnet1
    Value:
      Ref: QuarantineprivateSubnet1
    Condition: HasQuarantine
  QuarantineprivateSubnet2Id:
    Description: ID of QuarantineprivateSubnet2
    Value:
      Ref: QuarantineprivateSubnet2
    Condition: HasQuarantine
  QuarantineprivateSubnet3Id:
    Description: ID of QuarantineprivateSubnet3
    Value:
      Ref: QuarantineprivateSubnet3
    Condition: HasQuarantine
  QuarantineFunctionRoleArn:
    Description: ARN of the QuarantineFunctionRole
    Value:
      Fn::GetAtt:
        - QuarantineFunctionRole
        - Arn
    Condition: HasQuarantine
  APIHandlerRoleArn:
    Description: ARN of the APIHandlerRole
    Value:
      Fn::GetAtt:
        - APIHandlerRole
        - Arn
    Condition: HasQuarantine
  QuarantineInvokeRoleArn:
    Description: ARN of the QuarantineInvokeRole
    Value:
      Fn::GetAtt:
        - QuarantineInvokeRole
        - Arn
    Condition: HasQuarantine
