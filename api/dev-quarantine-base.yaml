Parameters:
Conditions:
Resources:
  APIHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - QuarantineUser
                  - Arn
            Sid: ""
        Version: "2012-10-17"
      Policies:
        - PolicyDocument:
            Statement:
              - Action:
                  - lambda:CreateFunction
                  - lambda:PublishVersion
                Effect: Allow
                Sid: QuarantinePublish
                Resource:
                  - !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*"
              # https://aws.amazon.com/blogs/compute/using-aws-lambda-iam-condition-keys-for-vpc-settings/
              - Action:
                  - lambda:CreateFunction
                  - lambda:PublishVersion
                Effect: Deny
                Sid: EnforceQuarantineVPC
                Resource: "*"
                Condition:
                  StringNotEquals:
                    lambda:VpcIds:
                      - !Ref QuarantinepubPrivateVPC
              - Action:
                  - lambda:TagResource
                Effect: Allow
                Sid: TagQuarantine
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/BraintrustQuarantine": "true"
              - Action:
                  - lambda:DeleteFunction
                  - lambda:UpdateFunctionCode
                  - lambda:UpdateFunctionConfiguration
                  - lambda:GetFunctionConfiguration
                Effect: Allow
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/BraintrustQuarantine": "true"
              - Action: iam:PassRole
                Effect: Allow
                Resource:
                  - !GetAtt QuarantineFunctionRole.Arn
              - Action: s3:*
                Effect: Allow
                Resource:
                  - Fn::Sub: arn:aws:s3:::${CodeBundleBucket}
                  - Fn::Sub: arn:aws:s3:::${CodeBundleBucket}/*
              # These cannot be scoped to the VPC, so we need to allow all access
              - Action:
                  - ec2:DescribeSecurityGroups
                  - ec2:DescribeSubnets
                  - ec2:DescribeVpcs
                Effect: Allow
                Resource: "*"

            Version: "2012-10-17"
          PolicyName: APIHandlerRolePolicy

  CodeBundleBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256

  QuarantineInvokeRole:
    Type: AWS::IAM::Role
    Condition: HasQuarantine
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - APIHandlerRole
                  - Arn
      Policies:
        - PolicyDocument:
            Statement:
              - Action: lambda:InvokeFunction
                Effect: Allow
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/BraintrustQuarantine": "true"
            Version: "2012-10-17"
          PolicyName: QuarantineInvokeRolePolicy

  QuarantineFunctionRole:
    Type: AWS::IAM::Role
    Condition: HasQuarantine
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Sid: ""
        Version: "2012-10-17"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole

  QuarantineUser:
    Type: AWS::IAM::User
    Properties:
      UserName: QuarantineUser

  QuarantineUserAccessKey:
    Type: AWS::IAM::AccessKey
    Properties:
      UserName: !Ref QuarantineUser

Outputs:
  QuarantineUserAccessKeyId:
    Value: !Ref QuarantineUserAccessKey
  QuarantineUserSecretAccessKey:
    Value: !GetAtt QuarantineUserAccessKey.SecretAccessKey
  QuarantinepubPrivateVPCId:
    Description: ID of the QuarantinepubPrivateVPC
    Value: !Ref QuarantinepubPrivateVPC
    Condition: HasQuarantine
  QuarantinepubPrivateVPCDefaultSecurityGroup:
    Description: Default Security Group ID of the QuarantinepubPrivateVPC
    Value: !GetAtt QuarantinepubPrivateVPC.DefaultSecurityGroup
    Condition: HasQuarantine
  QuarantineprivateSubnet1Id:
    Description: ID of QuarantineprivateSubnet1
    Value: !Ref QuarantineprivateSubnet1
    Condition: HasQuarantine
  QuarantineprivateSubnet2Id:
    Description: ID of QuarantineprivateSubnet2
    Value: !Ref QuarantineprivateSubnet2
    Condition: HasQuarantine
  QuarantineprivateSubnet3Id:
    Description: ID of QuarantineprivateSubnet3
    Value: !Ref QuarantineprivateSubnet3
    Condition: HasQuarantine
  QuarantineFunctionRoleArn:
    Description: ARN of the QuarantineFunctionRole
    Value: !GetAtt QuarantineFunctionRole.Arn
    Condition: HasQuarantine
  APIHandlerRoleArn:
    Description: ARN of the APIHandlerRole
    Value: !GetAtt APIHandlerRole.Arn
    Condition: HasQuarantine
  QuarantineInvokeRoleArn:
    Description: ARN of the QuarantineInvokeRole
    Value: !GetAtt QuarantineInvokeRole.Arn
    Condition: HasQuarantine
