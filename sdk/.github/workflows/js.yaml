name: js

on:
  pull_request:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest]
        node-version: [20, 22]

    env:
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY}}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY}}

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: "https://registry.npmjs.org"
      - uses: pnpm/action-setup@v4
      - name: "verify ci"
        shell: bash
        run: make js-verify-ci
