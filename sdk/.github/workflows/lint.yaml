# Source: https://github.com/marketplace/actions/pre-commit
name: lint

on:
  pull_request:
  push:
    branches: [main]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch full history for proper diff
      - uses: actions/setup-python@v3
      - uses: pre-commit/action@v3.0.0
        with:
          extra_args: --from-ref origin/${{ github.base_ref || 'main' }} --to-ref HEAD
