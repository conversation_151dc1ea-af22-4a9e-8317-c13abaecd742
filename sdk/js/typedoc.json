{"compilerOptions": {"skipLibCheck": true}, "tsconfig": "./tsconfig.json", "excludeExternals": true, "externalPattern": ["**/node_modules/**"], "exclude": ["**/examples/**"], "plugin": ["typedoc-plugin-markdown"], "out": "../../app/content/docs/reference/libs/nodejs", "publicPath": "/docs/libs/nodejs/", "githubPages": false, "disableSources": true, "excludePrivate": true, "hideBreadcrumbs": true, "excludeInternal": true, "theme": "markdown", "groupOrder": ["Functions", "*"]}