import {
  beforeAll,
  expect,
  describe,
  test,
  beforeEach,
  afterEach,
  vi,
} from "vitest";
import {
  defaultErrorScoreH<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>l<PERSON>corer,
  runEvaluator,
} from "./framework";
import { _exportsForTestingOnly } from "./logger";
import { configureNode } from "./node";
import { BarProgressReporter, type ProgressReporter } from "./progress";
import { InternalAbortError } from "./util";

beforeAll(() => {
  configureNode();
});

class NoopProgressReporter implements ProgressReporter {
  public start() {}
  public stop() {}
  public increment() {}
}

test("meta (write) is passed to task", async () => {
  const metadata = {
    bar: "baz",
    foo: "bar",
  };

  const out = await runEvaluator(
    null,
    {
      projectName: "proj",
      evalName: "eval",
      data: [{ input: 1, metadata }],
      task: async (input: number, { meta }) => {
        meta({
          foo: "barbar",
        });
        return input * 2;
      },
      scores: [],
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );

  // @ts-expect-error metadata is not typed if the experiment is missing
  expect(out.results[0].metadata).toEqual({
    bar: "baz",
    foo: "barbar",
  });
});

test("metadata (read/write) is passed to task", async () => {
  const metadata = {
    bar: "baz",
    foo: "bar",
  };

  let passedIn: Record<string, unknown> | null = null;

  const out = await runEvaluator(
    null,
    {
      projectName: "proj",
      evalName: "eval",
      data: [{ input: 1, metadata }],
      task: async (input: number, { metadata: m }) => {
        passedIn = { ...m };

        // modify the metadata object
        m.foo = "barbar";

        return input * 2;
      },
      scores: [],
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );

  expect(passedIn).toEqual(metadata);

  // @ts-expect-error metadata is not typed if the experiment is missing
  expect(out.results[0].metadata).toEqual({
    bar: "baz",
    foo: "barbar",
  });
});

test("expected (read/write) is passed to task", async () => {
  const expected = {
    bar: "baz",
    foo: "bar",
  };

  let passedIn: Record<string, unknown> | null = null;

  const out = await runEvaluator(
    null,
    {
      projectName: "proj",
      evalName: "eval",
      data: [{ input: 1, expected }],
      task: async (input: number, { expected: e }) => {
        passedIn = { ...e };

        // modify the expected object
        e.foo = "barbar";

        return input * 2;
      },
      scores: [],
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );

  expect(passedIn).toEqual({
    bar: "baz",
    foo: "bar",
  });

  // @ts-expect-error metadata is not typed if the experiment is missing
  expect(out.results[0].expected).toEqual({
    bar: "baz",
    foo: "barbar",
  });
});

function makeTestScorer(
  name: string,
  willError?: boolean,
): EvalScorer<any, any, any, any> {
  return () => {
    if (willError) {
      throw new Error("scorer errored");
    }
    return {
      name,
      score: 1,
    };
  };
}

describe("runEvaluator", () => {
  describe("errors", () => {
    test("task errors generate no scores", async () => {
      const out = await runEvaluator(
        null,
        {
          projectName: "proj",
          evalName: "eval",
          data: [{ input: 1 }],
          task: async () => {
            throw new Error("test error");
          },
          scores: Array.from({ length: 3 }, (_, i) =>
            makeTestScorer(`scorer_${i}`),
          ),
        },
        new NoopProgressReporter(),
        [],
        undefined,
      );

      expect(out.results.every((r) => Object.keys(r.scores).length === 0)).toBe(
        true,
      );
    });

    describe("errorScoreHandler", () => {
      describe("default function", () => {
        test("task errors generate 0 scores for all scorers", async () => {
          const out = await runEvaluator(
            null,
            {
              projectName: "proj",
              evalName: "eval",
              data: [{ input: 1 }],
              task: async () => {
                throw new Error("test error");
              },
              scores: Array.from({ length: 3 }, (_, i) =>
                makeTestScorer(`scorer_${i}`),
              ),
              errorScoreHandler: defaultErrorScoreHandler,
            },
            new NoopProgressReporter(),
            [],
            undefined,
          );

          expect(
            out.results.every(
              (r) =>
                Object.keys(r.scores).length === 3 &&
                Object.values(r.scores).every((v) => v === 0),
            ),
          ).toBe(true);
        });

        test("scorer errors generate 0 scores for all errored scorers", async () => {
          const out = await runEvaluator(
            null,
            {
              projectName: "proj",
              evalName: "eval",
              data: [{ input: 1 }],
              task: async () => {
                return "valid output";
              },
              scores: Array.from({ length: 3 }, (_, i) =>
                makeTestScorer(`scorer_${i}`, i === 0),
              ),
              errorScoreHandler: defaultErrorScoreHandler,
            },
            new NoopProgressReporter(),
            [],
            undefined,
          );

          expect(
            out.results.every(
              (r) =>
                Object.keys(r.scores).length === 3 &&
                r.scores.scorer_0 === 0 &&
                r.scores.scorer_1 === 1 &&
                r.scores.scorer_2 === 1,
            ),
          ).toBe(true);
        });
      });

      describe("custom function", () => {
        test("noop function generates no scores", async () => {
          const out = await runEvaluator(
            null,
            {
              projectName: "proj",
              evalName: "eval",
              data: [{ input: 1 }],
              task: async () => {
                throw new Error("test error");
              },
              scores: Array.from({ length: 3 }, (_, i) =>
                makeTestScorer(`scorer_${i}`),
              ),
              errorScoreHandler: () => undefined,
            },
            new NoopProgressReporter(),
            [],
            undefined,
          );

          expect(
            out.results.every((r) => Object.keys(r.scores).length === 0),
          ).toBe(true);
        });

        test("function can generate arbitrary scores", async () => {
          const out = await runEvaluator(
            null,
            {
              projectName: "proj",
              evalName: "eval",
              data: [{ input: 1 }],
              task: async () => {
                throw new Error("test error");
              },
              scores: Array.from({ length: 3 }, (_, i) =>
                makeTestScorer(`scorer_${i}`),
              ),
              errorScoreHandler: () => ({ error_score: 1 }),
            },
            new BarProgressReporter(),
            [],
            undefined,
          );

          expect(
            out.results.every(
              (r) =>
                Object.keys(r.scores).length === 1 &&
                r.scores.error_score === 1,
            ),
          ).toBe(true);
        });
      });
    });
  });

  describe("aborts", () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.clearAllTimers();
      vi.useRealTimers();
    });

    test("runEvaluator rejects on timeout and kills remaining tasks", async () => {
      const taskStarts: Set<number> = new Set();
      const taskCompletions: Set<number> = new Set();

      const runExpect = expect(
        runEvaluator(
          null,
          {
            projectName: "proj",
            evalName: "eval",
            data: Array.from({ length: 10 }, (_, i) => ({
              input: i,
              expected: i * 2,
            })),
            task: async (input: number) => {
              taskStarts.add(input);
              if (input > 2) {
                await new Promise((r) => setTimeout(r, 100));
              }
              taskCompletions.add(input);
              return input * 2;
            },
            scores: [],
            timeout: 10,
            maxConcurrency: 1,
          },
          new NoopProgressReporter(),
          [],
          undefined,
        ),
      ).rejects.toThrow(new InternalAbortError("Evaluator timed out"));

      await vi.advanceTimersByTimeAsync(10);
      await runExpect;

      // first 3 tasks complete and 4th task was started but not completed before timeout
      expect(taskStarts).toEqual(new Set([0, 1, 2, 3]));
      expect(taskCompletions).toEqual(new Set([0, 1, 2]));

      await vi.advanceTimersByTimeAsync(200);

      // no other tasks are started after evaluator is aborted and the 4th in-flight task completes
      expect(taskStarts).toEqual(new Set([0, 1, 2, 3]));
      expect(taskCompletions).toEqual(new Set([0, 1, 2, 3]));
      expect(vi.getTimerCount()).toBe(0);
    });

    test("runEvaluator rejects on abort signal and kills remaining tasks", async () => {
      const taskStarts: Set<number> = new Set();
      const taskCompletions: Set<number> = new Set();

      const abortController = new AbortController();

      const runExpect = expect(
        runEvaluator(
          null,
          {
            projectName: "proj",
            evalName: "eval",
            data: Array.from({ length: 10 }, (_, i) => ({
              input: i,
              expected: i * 2,
            })),
            task: async (input: number) => {
              taskStarts.add(input);
              if (input > 2) {
                await new Promise((r) => setTimeout(r, 100));
              }
              taskCompletions.add(input);
              return input * 2;
            },
            scores: [],
            signal: abortController.signal,
            maxConcurrency: 1,
          },
          new NoopProgressReporter(),
          [],
          undefined,
        ),
      ).rejects.toThrow(new InternalAbortError("Evaluator aborted"));

      await vi.advanceTimersByTimeAsync(10);
      abortController.abort();
      await runExpect;

      // first 3 tasks complete and 4th task was started but not completed before abort
      expect(taskStarts).toEqual(new Set([0, 1, 2, 3]));
      expect(taskCompletions).toEqual(new Set([0, 1, 2]));

      await vi.advanceTimersByTimeAsync(200);

      // no other tasks are started after evaluator is aborted and the 4th in-flight task completes
      expect(taskStarts).toEqual(new Set([0, 1, 2, 3]));
      expect(taskCompletions).toEqual(new Set([0, 1, 2, 3]));
      expect(vi.getTimerCount()).toBe(0);
    });

    test("runEvaluator works with no timeout or abort signal", async () => {
      const run = runEvaluator(
        null,
        {
          projectName: "proj",
          evalName: "eval",
          data: [{ input: 1, expected: 2 }],
          task: async (input: number) => {
            await new Promise((r) => setTimeout(r, 100));
            return input * 2;
          },
          scores: [],
        },
        new NoopProgressReporter(),
        [],
        undefined,
      );

      await vi.advanceTimersByTimeAsync(100);
      await run;
      expect(vi.getTimerCount()).toBe(0);
    });
  });
});

test("trialIndex is passed to task", async () => {
  const trialIndices: number[] = [];

  const { results } = await runEvaluator(
    null,
    {
      projectName: "proj",
      evalName: "eval",
      data: [{ input: 1, expected: 2 }],
      task: async (input: number, { trialIndex }) => {
        trialIndices.push(trialIndex);
        return input * 2;
      },
      scores: [],
      trialCount: 3,
    },
    new NoopProgressReporter(),
    [],
  );

  // Should have 3 results (one for each trial)
  expect(results).toHaveLength(3);

  // Should have captured 3 trial indices
  expect(trialIndices).toHaveLength(3);
  expect(trialIndices.sort()).toEqual([0, 1, 2]);

  // All results should be correct
  results.forEach((result) => {
    expect(result.input).toBe(1);
    expect(result.expected).toBe(2);
    expect(result.output).toBe(2);
    expect(result.error).toBeUndefined();
  });
});

test("trialIndex with multiple inputs", async () => {
  const trialData: Array<{ input: number; trialIndex: number }> = [];

  const { results } = await runEvaluator(
    null,
    {
      projectName: "proj",
      evalName: "eval",
      data: [
        { input: 1, expected: 2 },
        { input: 2, expected: 4 },
      ],
      task: async (input: number, { trialIndex }) => {
        trialData.push({ input, trialIndex });
        return input * 2;
      },
      scores: [],
      trialCount: 2,
    },
    new NoopProgressReporter(),
    [],
  );

  // Should have 4 results total (2 inputs × 2 trials)
  expect(results).toHaveLength(4);
  expect(trialData).toHaveLength(4);

  // Group by input to verify trial indices
  const input1Trials = trialData
    .filter((d) => d.input === 1)
    .map((d) => d.trialIndex)
    .sort();
  const input2Trials = trialData
    .filter((d) => d.input === 2)
    .map((d) => d.trialIndex)
    .sort();

  // Each input should have been run with trial indices 0 and 1
  expect(input1Trials).toEqual([0, 1]);
  expect(input2Trials).toEqual([0, 1]);
});

test("Eval with noSendLogs: true runs locally without creating experiment", async () => {
  const memoryLogger = _exportsForTestingOnly.useTestBackgroundLogger();

  const result = await Eval(
    "test-no-logs",
    {
      data: () => [
        { input: "hello", expected: "hello world" },
        { input: "test", expected: "test world" },
      ],
      task: (input) => input + " world",
      scores: [
        (args) => ({
          name: "exact_match",
          score: args.output === args.expected ? 1 : 0,
        }),
        () => ({ name: "simple_scorer", score: 0.8 }),
      ],
    },
    { noSendLogs: true },
  );

  // Verify it returns results
  expect(result.results).toHaveLength(2);
  expect(result.results[0].input).toBe("hello");
  expect(result.results[0].output).toBe("hello world");
  expect(result.results[0].scores.exact_match).toBe(1);
  expect(result.results[0].scores.simple_scorer).toBe(0.8);

  expect(result.results[1].input).toBe("test");
  expect(result.results[1].output).toBe("test world");
  expect(result.results[1].scores.exact_match).toBe(1);
  expect(result.results[1].scores.simple_scorer).toBe(0.8);

  // Verify it builds a local summary (no experimentUrl means local run)
  expect(result.summary.projectName).toBe("test-no-logs");
  expect(result.summary.experimentUrl).toBeUndefined();
  expect(result.summary.scores.exact_match.score).toBe(1);
  expect(result.summary.scores.simple_scorer.score).toBe(0.8);

  // Most importantly: verify that no logs were sent
  await memoryLogger.flush();
  expect(await memoryLogger.drain()).toHaveLength(0);
});

test("tags can be appended and logged to root span", async () => {
  await _exportsForTestingOnly.simulateLoginForTests();
  const memoryLogger = _exportsForTestingOnly.useTestBackgroundLogger();
  const experiment =
    _exportsForTestingOnly.initTestExperiment("js-tags-append");

  const initialTags = ["cookies n cream"];
  const appendedTags = ["chocolate", "vanilla", "strawberry"];
  const expectedTags = [
    "cookies n cream",
    "chocolate",
    "vanilla",
    "strawberry",
  ];

  const result = await runEvaluator(
    experiment,
    {
      projectName: "proj",
      evalName: "js-tags-append",
      data: [{ input: "hello", expected: "hello world", tags: initialTags }],
      task: (input, hooks) => {
        for (const t of appendedTags) hooks.tags.push(t);
        return input;
      },
      scores: [() => ({ name: "simple_scorer", score: 0.8 })],
      summarizeScores: false,
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );
  expect(result.results[0].tags).toEqual(expectedTags);

  await memoryLogger.flush();
  const logs = await memoryLogger.drain();
  const rootSpans = logs.filter((l: any) => !l["span_parents"]);
  expect(rootSpans).toHaveLength(1);
  expect((rootSpans[0] as any).tags).toEqual(expectedTags);
});

test.each([
  {
    title: "undefined list returns undefined for tags",
    providedTags: undefined,
    expectedTags: undefined,
  },
  {
    title: "empty list returns undefined for tags",
    providedTags: [],
    expectedTags: undefined,
  },
  {
    title: "tags can be set to a list",
    providedTags: ["chocolate", "vanilla", "strawberry"],
    expectedTags: ["chocolate", "vanilla", "strawberry"],
  },
])("$title", async ({ providedTags, expectedTags }) => {
  await _exportsForTestingOnly.simulateLoginForTests();
  const memoryLogger = _exportsForTestingOnly.useTestBackgroundLogger();
  const experiment = _exportsForTestingOnly.initTestExperiment("js-tags-list");

  const result = await runEvaluator(
    experiment,
    {
      projectName: "proj",
      evalName: "js-tags-list",
      data: [{ input: "hello", expected: "hello world" }],
      task: (input, hooks) => {
        hooks.tags = providedTags;
        return input;
      },
      scores: [() => ({ name: "simple_scorer", score: 0.8 })],
      summarizeScores: false,
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );
  expect(result.results[0].tags).toEqual(expectedTags);

  await memoryLogger.flush();
  const logs = await memoryLogger.drain();
  const rootSpans = logs.filter((l: any) => !l["span_parents"]);
  expect(rootSpans).toHaveLength(1);
  expect((rootSpans[0] as any).tags).toEqual(expectedTags);
});

test("tags are persisted with a failing scorer", async () => {
  await _exportsForTestingOnly.simulateLoginForTests();
  const memoryLogger = _exportsForTestingOnly.useTestBackgroundLogger();
  const experiment = _exportsForTestingOnly.initTestExperiment("js-tags-list");

  const expectedTags = ["chocolate", "vanilla", "strawberry"];

  const result = await runEvaluator(
    experiment,
    {
      projectName: "proj",
      evalName: "js-tags-list",
      data: [{ input: "hello", expected: "hello world" }],
      task: (input, hooks) => {
        hooks.tags = expectedTags;
        return input;
      },
      scores: [
        () => ({ name: "simple_scorer", score: 0.8 }),
        () => {
          throw new Error("test error");
        },
      ],
      summarizeScores: false,
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );
  expect(result.results[0].tags).toEqual(expectedTags);

  await memoryLogger.flush();
  const logs = await memoryLogger.drain();
  const rootSpans = logs.filter((l: any) => !l["span_parents"]);
  expect(rootSpans).toHaveLength(1);
  expect((rootSpans[0] as any).tags).toEqual(expectedTags);
});

test("tags remain empty when not set", async () => {
  await _exportsForTestingOnly.simulateLoginForTests();
  const memoryLogger = _exportsForTestingOnly.useTestBackgroundLogger();
  const experiment =
    _exportsForTestingOnly.initTestExperiment("js-tags-append");

  const result = await runEvaluator(
    experiment,
    {
      projectName: "proj",
      evalName: "js-tags-append",
      data: [{ input: "hello", expected: "hello world" }],
      task: (input, hooks) => {
        return input;
      },
      scores: [() => ({ name: "simple_scorer", score: 0.8 })],
      summarizeScores: false,
    },
    new NoopProgressReporter(),
    [],
    undefined,
  );
  expect(result.results[0].tags).toEqual(undefined);

  await memoryLogger.flush();
  const logs = await memoryLogger.drain();
  const rootSpans = logs.filter((l: any) => !l["span_parents"]);
  expect(rootSpans).toHaveLength(1);
  expect((rootSpans[0] as any).tags).toEqual(undefined);
});
