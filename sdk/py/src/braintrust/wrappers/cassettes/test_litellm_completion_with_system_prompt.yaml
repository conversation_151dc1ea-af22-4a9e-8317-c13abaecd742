interactions:
  - request:
      body:
        '{"messages": [{"role": "system", "content": "You are a helpful assistant
        that only responds with numbers."}, {"role": "user", "content": "What''s 12
        + 12?"}], "model": "gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "181"
        content-type:
          - application/json
        cookie:
          - __cf_bm=LhJr7iTKhj3HkuIqsYYQzHnLMYlAsrPO_8DONvOoa1s-1752097448-*******-3QZBiExLtV3A83cyPK4CeYqjpSygQ6uYDZ0v7kYNlmEHZIOldYiM088zk99s107ShpZgiHzSJwcw0Fviy3roAk.Ifv1OH0UzhIx2VTAKaS4;
            _cfuvid=1FpkTfHv2KC0I5jKg65yXzjPU21dBkiuyT323kob89c-1752097448243-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb9swDIXv/hUCz/GQOG695Fj0trUo0GA7DIWhSrSjVhZViR5WFPnv
          g+w0droN2MUHfnzU4zPfMiHAaNgKUHvJqvM2vwrf4/3trnrdfb152pX8rcWX+7tLvLv+Uj3DIino
          8QkVv6s+Keq8RTbkRqwCSsY0dVVdFMtNVV6WA+hIo02y1nNeUt4ZZ/JiWZT5sspXn4/qPRmFEbbi
          RyaEEG/DN/l0Gn/BViwX75UOY5QtwvbUJAQEsqkCMkYTWTqGxQQVOUY3WC/KOQjY9FEmc663dgak
          c8QyLTdYejiSw8mEpdYHeowfpNAYZ+K+DigjufRgZPIw0EMmxMOwbH/mH3ygznPN9IzDc8VmHAdT
          xBNcHRkTSzuV18d8zofVGlkaG2dZgZJqj3pSTsHKXhuagWy28p9e/jZ7XNu49n/GT0Ap9Iy69gG1
          Uef7Tm0B0/39q+0U8WAYIoafRmHNBkP6DRob2dvxKiC+RsauboxrMfhgxtNofL0u5UUpcbNWkB2y
          3wAAAP//AwBNbrh5KAMAAA==
      headers:
        CF-RAY:
          - 95cb00a28da22ac0-LAX
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Wed, 09 Jul 2025 21:44:25 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "324"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "329"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999978"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_37ef8069103aa00e519f87fb02a7f31e
      status:
        code: 200
        message: OK
version: 1
