interactions:
  - request:
      body:
        '{"messages":[{"role":"user","content":"What''s the weather in New York
        and the time in Tokyo?"}],"model":"gpt-4o-mini","stream":false,"stream_options":null,"temperature":0,"tools":[{"type":"function","function":{"name":"get_weather","description":"Get
        the weather for a location","parameters":{"type":"object","properties":{"location":{"type":"string","description":"The
        location to get weather for"}},"required":["location"]}}},{"type":"function","function":{"name":"get_time","description":"Get
        the current time for a timezone","parameters":{"type":"object","properties":{"timezone":{"type":"string","description":"The
        timezone to get time for"}},"required":["timezone"]}}}]}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "676"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA7RU32/aMBB+z19h3TNsQIMoeVt/TFOrVa2GWrZRRa5zJCaOndmXUkD871OSlgTa
          bX1ZHiLnPn9333c5e+MxBjKCgIFIOIksV92TLH2is2Q9EJ9P7k5/nT6K8/T69Hpy8W2tFtApGeZh
          gYJeWB+EyXKFJI2uYWGRE5ZZ+6Nhbzwcj0ejCshMhKqkxTl1fdPNpJbdQW/gd3ujbv/4mZ0YKdBB
          wH56jDG2qd6lTh3hEwSs13mJZOgcjxGC3SbGwBpVRoA7Jx1xTdBpQGE0oS6l60KpFkDGqFBwpZrC
          9bNprZtmcaXCxZBuM3989nVyNZ2u3HnqLqJkcela9erUq7wSNC+02DWphe/iwUExxkDzrOLGSOES
          OSVoD+iMAbdxkaGmUjpsZqCM4GXCGQRsBle4ZN+NTWewhT3mtvW17bzLMuaJ+nF5Q+mXk3Xvzt3I
          VCyn54b/L8skM/y333LX2mis/X5ykn+cmHRl/u54t75vjYHFeeG4ej0fXGtDVVerAbn3DvoGysS5
          NQ/ugApzqaVLQovcVX7bk+a9CKkkQLE3zJBbk+UUkkmxKjr266TQnLcG9I+fQTLEVRPv+4POG+nC
          CInLath350twkWDUUJtzxotImhbgtay/VvNW7tq+1PF70jeAEJgTRmFuMZJi33GzzWJ5Hf1p267J
          lWBwaB+lwJAk2vJ3RDjnhaovCXArR5iFc6ljtLmV1U0B8zw88vnQ5zg+EuBtvd8AAAD//wMA/xPD
          hjcFAAA=
      headers:
        CF-RAY:
          - 955e85f4bc84c598-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 26 Jun 2025 17:46:18 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=ImsddYvCWfxjvgCkc.PQteneAt1.05OrnpctX.H9.6Q-1750959978-*******-nNSwp_XWJ00JfcdLpEJJIkWuFwKp_G8OsvkqQJ1Y.t3LIgdq_FjD6D6k6xGKWUB1YmzkYT5DEps7_NG5XU.HYhGXfOrQ_qYymycHaizxQ64;
            path=/; expires=Thu, 26-Jun-25 18:16:18 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=QM6hDu5SUhMw7byvxEvE9lxA0zi2uq0mxJsxgQgHJZQ-1750959978886-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "905"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "911"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999984"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_ce9634fb6e411458288df8bbff2c6949
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"messages":[{"role":"user","content":"What''s the weather in New York
        and the time in Tokyo?"}],"model":"gpt-4o-mini","stream":false,"stream_options":null,"temperature":0,"tools":[{"type":"function","function":{"name":"get_weather","description":"Get
        the weather for a location","parameters":{"type":"object","properties":{"location":{"type":"string","description":"The
        location to get weather for"}},"required":["location"]}}},{"type":"function","function":{"name":"get_time","description":"Get
        the current time for a timezone","parameters":{"type":"object","properties":{"timezone":{"type":"string","description":"The
        timezone to get time for"}},"required":["timezone"]}}}]}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "676"
        content-type:
          - application/json
        cookie:
          - __cf_bm=ImsddYvCWfxjvgCkc.PQteneAt1.05OrnpctX.H9.6Q-1750959978-*******-nNSwp_XWJ00JfcdLpEJJIkWuFwKp_G8OsvkqQJ1Y.t3LIgdq_FjD6D6k6xGKWUB1YmzkYT5DEps7_NG5XU.HYhGXfOrQ_qYymycHaizxQ64;
            _cfuvid=QM6hDu5SUhMw7byvxEvE9lxA0zi2uq0mxJsxgQgHJZQ-1750959978886-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//tFRNc9owEL37V2j2jFswBDA3kqbJTNo0ZTKdZkrGI+S1UZAlR5L5CMN/
          71h82JC0zaU+eOR9ervvrVdae4QAj2FAgE2pZVku/PNstpxfPgXf8Cb8+ixv21dKNJfncjT8Li00
          SoaaPCGze9YHprJcoOVKbmGmkVoss7Z6Z83wLAx7oQMyFaMoaWlu/Y7yMy65HzSDjt/s+a3+jj1V
          nKGBAfnlEULI2r1LnTLGJQxIs7GPZGgMTREGh02EgFaijAA1hhtLd5p3IFPSoiyly0KIGmCVEhGj
          QlSFt8+6tq6aRYWIllej1vV9oL8MFxemKy+fb35MUPdkrd429Sp3gpJCskOTavghPjgpRghImjlu
          ijZaILVT1Cd0QoDqtMhQ2lI6rMcgFKNlwjEMyBhucUEelJ6NYQNHzE3ta9N4l+Xk88PPeXuldKv7
          wkfD6+Ti012he/x/WbY8w3/7LXe9KIlbv0PD6cd7NVupvzs+rB9rY6AxKQwVr+eDSqms66obkEfv
          pG8gVJprNTEnVEi45GYaaaTG+a1PmrcX4iRAcTTMkGuV5TayaoauaNjZJoXqvFVgp78DrbJUVPFW
          J2i8kS6K0VLuhv1wvhhlU4wranXOaBFzVQO8mvXXat7KvbXPZfqe9BXAGOYW4yjXGHN27LjaprG8
          jv607dBkJxgM6jlnGFmOuvwdMSa0ELuLzayMxSxKuExR55q7mwKSPOoGNGjTfgsT8DbebwAAAP//
          AwA/i1dUNwUAAA==
      headers:
        CF-RAY:
          - 955e85fc49a8c598-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 26 Jun 2025 17:46:20 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "1353"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "1375"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999984"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_d7ad22f31a644826179f4d68275a2f4e
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"messages":[{"role":"user","content":"What''s the weather in New York
        and the time in Tokyo?"}],"model":"gpt-4o-mini","stream":true,"stream_options":{"include_usage":true},"temperature":0,"tools":[{"type":"function","function":{"name":"get_weather","description":"Get
        the weather for a location","parameters":{"type":"object","properties":{"location":{"type":"string","description":"The
        location to get weather for"}},"required":["location"]}}},{"type":"function","function":{"name":"get_time","description":"Get
        the current time for a timezone","parameters":{"type":"object","properties":{"timezone":{"type":"string","description":"The
        timezone to get time for"}},"required":["timezone"]}}}]}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "693"
        content-type:
          - application/json
        cookie:
          - __cf_bm=ImsddYvCWfxjvgCkc.PQteneAt1.05OrnpctX.H9.6Q-1750959978-*******-nNSwp_XWJ00JfcdLpEJJIkWuFwKp_G8OsvkqQJ1Y.t3LIgdq_FjD6D6k6xGKWUB1YmzkYT5DEps7_NG5XU.HYhGXfOrQ_qYymycHaizxQ64;
            _cfuvid=QM6hDu5SUhMw7byvxEvE9lxA0zi2uq0mxJsxgQgHJZQ-1750959978886-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string:
          'data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"role":"assistant","content":null},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"id":"call_WY2R9D3MQrwbUTvQFtua91zx","type":"function","function":{"name":"get_weather","arguments":""}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\"lo"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"catio"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"n\":
          \"N"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"ew
          Y"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"ork\"}"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"id":"call_ZwNK54BJyVcJ7sbNZzNOaHWW","type":"function","function":{"name":"get_time","arguments":""}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"{\"ti"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"mezon"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"e\":
          \"A"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"sia/"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"Tokyo"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"\"}"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"tool_calls"}],"usage":null}


          data: {"id":"chatcmpl-BmkxxakFMg9fugkFzAcDoNWSCaHLW","object":"chat.completion.chunk","created":1750959981,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[],"usage":{"prompt_tokens":94,"completion_tokens":48,"total_tokens":142,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}


          data: [DONE]


          '
      headers:
        CF-RAY:
          - 955e860739ffc598-EWR
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Thu, 26 Jun 2025 17:46:22 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "1073"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "1081"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999984"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_9b29e4d9569098cfbdd2d6afe87df7ba
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"messages":[{"role":"user","content":"What''s the weather in New York
        and the time in Tokyo?"}],"model":"gpt-4o-mini","stream":true,"stream_options":{"include_usage":true},"temperature":0,"tools":[{"type":"function","function":{"name":"get_weather","description":"Get
        the weather for a location","parameters":{"type":"object","properties":{"location":{"type":"string","description":"The
        location to get weather for"}},"required":["location"]}}},{"type":"function","function":{"name":"get_time","description":"Get
        the current time for a timezone","parameters":{"type":"object","properties":{"timezone":{"type":"string","description":"The
        timezone to get time for"}},"required":["timezone"]}}}]}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "693"
        content-type:
          - application/json
        cookie:
          - __cf_bm=ImsddYvCWfxjvgCkc.PQteneAt1.05OrnpctX.H9.6Q-1750959978-*******-nNSwp_XWJ00JfcdLpEJJIkWuFwKp_G8OsvkqQJ1Y.t3LIgdq_FjD6D6k6xGKWUB1YmzkYT5DEps7_NG5XU.HYhGXfOrQ_qYymycHaizxQ64;
            _cfuvid=QM6hDu5SUhMw7byvxEvE9lxA0zi2uq0mxJsxgQgHJZQ-1750959978886-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string:
          'data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"role":"assistant","content":null},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"id":"call_jTy9LzkVyn6cPzokhRZK3wac","type":"function","function":{"name":"get_weather","arguments":""}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\"lo"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"catio"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"n\":
          \"N"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"ew
          Y"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"ork\"}"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"id":"call_kHgzyiyayXAQ2OBYC1r5QSRg","type":"function","function":{"name":"get_time","arguments":""}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"{\"ti"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"mezon"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"e\":
          \"A"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"sia/"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"Tokyo"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","usage":null,"choices":[{"index":0,"delta":{"tool_calls":[{"index":1,"function":{"arguments":"\"}"}}]},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"tool_calls"}],"usage":null}


          data: {"id":"chatcmpl-BmkxyGlaywPrIMWVV0sU4x39kgJtA","object":"chat.completion.chunk","created":1750959982,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[],"usage":{"prompt_tokens":94,"completion_tokens":48,"total_tokens":142,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}


          data: [DONE]


          '
      headers:
        CF-RAY:
          - 955e86124a90c598-EWR
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Thu, 26 Jun 2025 17:46:23 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "970"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "981"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999984"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_62275476d620d1e6433d08dca31bbde0
      status:
        code: 200
        message: OK
version: 1
