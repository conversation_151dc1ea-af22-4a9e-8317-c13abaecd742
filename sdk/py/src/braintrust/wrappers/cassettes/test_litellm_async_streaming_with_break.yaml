interactions:
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s 12 + 12?"}], "model":
        "gpt-4o-mini", "stream": true, "stream_options": {"include_usage": true}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "145"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string:
          'data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          +"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          ="},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"24"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}],"usage":null}


          data: {"id":"chatcmpl-BrWsXRHqtbdAy3XAlgLYEcLkF9afc","object":"chat.completion.chunk","created":1752097469,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[],"usage":{"prompt_tokens":14,"completion_tokens":8,"total_tokens":22,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}


          data: [DONE]


          '
      headers:
        Access-Control-Expose-Headers:
          - X-Request-ID
        CF-RAY:
          - 95cb00bc980d2f73-LAX
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Wed, 09 Jul 2025 21:44:29 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=daiLzX2aYvlq5_YLmg3N1AnJtc_FLNWvnAW9q6vHTi4-1752097469-1.0.1.1-7cAT44X65S9TwWGkYsHcwz2GRSWjx2Hird1GW_UqCrFV3FwcuSXqo1FY52f628XVHwcggSuA3Hw7yKHclS0MQVybDyzJkEKeGlkpRZwkSqw;
            path=/; expires=Wed, 09-Jul-25 22:14:29 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=IDIm7EZoj1C5auaId89G.uB0_3ZN0yKcSazXH87PkI8-1752097469267-0.0.1.1-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "303"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "306"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_a199be75e7c67047dd45593efd7f15ea
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s 12 + 12?"}], "model":
        "gpt-4o-mini", "stream": true, "stream_options": {"include_usage": true}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "145"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: ""
      headers:
        Access-Control-Expose-Headers:
          - X-Request-ID
        CF-RAY:
          - 95cb00bc980d2f73-LAX
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Wed, 09 Jul 2025 21:44:29 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=daiLzX2aYvlq5_YLmg3N1AnJtc_FLNWvnAW9q6vHTi4-1752097469-1.0.1.1-7cAT44X65S9TwWGkYsHcwz2GRSWjx2Hird1GW_UqCrFV3FwcuSXqo1FY52f628XVHwcggSuA3Hw7yKHclS0MQVybDyzJkEKeGlkpRZwkSqw;
            path=/; expires=Wed, 09-Jul-25 22:14:29 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=IDIm7EZoj1C5auaId89G.uB0_3ZN0yKcSazXH87PkI8-1752097469267-0.0.1.1-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "303"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "306"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_a199be75e7c67047dd45593efd7f15ea
      status:
        code: 200
        message: OK
version: 1
