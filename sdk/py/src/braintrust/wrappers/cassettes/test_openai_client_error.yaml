interactions:
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"non-existent-model"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "87"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA0yOQQ6DMBAD77zCyrn0Abyj9xCRbYkUdmmyQUWIv1faHsrRY1v20QGAo1KkuAGH
          SUML1Rpe5Aa4x0xYJFLGyMI9fVJVYu2NjYhCFSwKMyAFuzREMTaHjRCmiWqFCpLe3e0/ovtqC4m3
          kFP0hd6Nqvrfn0twDSUsbgC3nC94kmh9e+JZ1D+lcXSWOLuz+wIAAP//AwDwJ9T24AAAAA==
      headers:
        CF-RAY:
          - 9472caf6acdb42e9-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json; charset=utf-8
        Date:
          - Thu, 29 May 2025 03:09:25 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=tw5RpvNcilfwjUjOiCvIStwbXJbeRE_xXCqPZ.4RX7I-1748488165-*******-FMTa0ZJqNJbuNSSA6DphhFklanxkVYlP.JqvtvujJtr4sYPh9SpE3tA0RGm.xS6wbQPTr3XYwQizdw4hssI3nii8ASbX11MhrVl9x0YOMrE;
            path=/; expires=Thu, 29-May-25 03:39:25 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=2B8C1K9X3JEAGR.xZd9FRJAYUg1T1ogs5tZIZzvxNiI-1748488165031-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        vary:
          - Origin
        x-request-id:
          - req_51b61b333398d569debb6e253ba8335a
      status:
        code: 404
        message: Not Found
version: 1
