interactions:
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "80"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb9QwEIXv+RXWXNlUm5B2o71BhVQBggNHVEWuPUkMjse1J9VW1f53
          5GS7SUuRuOQw37zJe+N5yoQAo2EvQPWS1eBt/lF9ezjckP56bW4eP3+4/t7/6NvdYffp8kt9D5uk
          oLtfqPhZdaFo8BbZkJuxCigZ09RiV9VVXRdXlxMYSKNNss5zXlE+GGfycltW+XaXF/VJ3ZNRGGEv
          fmZCCPE0fZNPp/EAe7HdPFcGjFF2CPtzkxAQyKYKyBhNZOkYNgtU5BjdZL0oxTtRlALvR2mjKKuL
          dWPAdowymXWjtSsgnSOWKexk8fZEjmdTljof6C6+kkJrnIl9E1BGcslAZPIw0WMmxO0UfnyRB3yg
          wXPD9Bun3xXVPA6WlS+wPjEmlnYpl+XmjWGNRpbGxtXuQEnVo16Uy6LlqA2tQLaK/LeXt2bPsY3r
          /mf8ApRCz6gbH1Ab9TLv0hYw3eO/2s4rngxDxPBgFDZsMKRn0NjK0c5XAvExMg5Na1yHwQczn0rr
          m6tSlu9lXWAL2TH7AwAA//8DAJIFUrc4AwAA
      headers:
        CF-RAY:
          - 9472caf81ef08186-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:25 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=1jp7iu46cC7LTzqfTym6.GB7A38C8xsrwWCjGlyW.Zs-1748488165-*******-HUMCsywTN7YO6EKTIJZBdpCNIKJbjNNrSu76iNiGcTdFQkL91RARv0WBpbFFLhM0V.ZRhO6vrlHps9b3sW9wPC03ieo_Omlh6SDn4Sr1g0k;
            path=/; expires=Thu, 29-May-25 03:39:25 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=7HAy0drHIm.KI.NGoj.O5wWMpmoB_UvJpQNPf0GUxSY-1748488165900-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "650"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "654"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_1372de4ff197f2ab5467d8fa1aa9ba33
      status:
        code: 200
        message: OK
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "80"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb9swDIXv/hUCr42L2PNSN8cht2LBDgMGbCgMRaIdpbKkiXSxrsh/
          H+Sksdt1wC4+8OOj36P4nAkBRsNagNpLVn2w+Se1fXw6HL5vN/T7rg0b+3mz/fqNbr90hwcFi6Tw
          uwMqflFdK98Hi2y8O2EVUTKmqcVNVVd1XaxWI+i9RptkXeC88nlvnMnLZVnly5u8qM/qvTcKCdbi
          RyaEEM/jN/l0Gn/BWiwXL5UeiWSHsL40CQHR21QBSWSIpWNYTFB5x+hG60UprkRRCvw5SEuirK7n
          jRHbgWQy6wZrZ0A651mmsKPF+zM5XkxZ34Xod/RGCq1xhvZNREneJQPEPsBIj5kQ92P44VUeCNH3
          gRv2Dzj+rqhO42Ba+QTrM2PP0k7lsly8M6zRyNJYmu0OlFR71JNyWrQctPEzkM0i/+3lvdmn2MZ1
          /zN+AkphYNRNiKiNep13aouY7vFfbZcVj4aBMD4ahQ0bjOkZNLZysKcrAXoixr5pjeswhmhOp9KG
          5mOFu2qnV7cfIDtmfwAAAP//AwAHOUbKOAMAAA==
      headers:
        CF-RAY:
          - 9472cafdb8d5429a-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:26 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=2tnlCSvS2cImTavzOy04_sA9uLoEZqsYBxshBl0yKeI-1748488166-*******-FhGNAXq.70IxLpdlPsUUkGWLHBbuBNGktxhq7YkqzDJ3lvBvOSdSCBeJ0Nrw7CicajhN6AQUCFcUJsxtUrNayxIJeVI9uewXP1kiftTII2g;
            path=/; expires=Thu, 29-May-25 03:39:26 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=xICitKMNXIOl.kF9pYQEx_CxCHvrsE7JnXnPdFpUGUo-1748488166544-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "377"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "382"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_597e214218a53799245347330be413a9
      status:
        code: 200
        message: OK
version: 1
