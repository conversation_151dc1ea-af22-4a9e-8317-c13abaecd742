interactions:
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s the weather in New York?"}],
        "model": "gpt-4o-mini", "temperature": 0, "tools": [{"type": "function", "function":
        {"name": "get_weather", "description": "Get the weather for a location", "parameters":
        {"type": "object", "properties": {"location": {"type": "string", "description":
        "The location to get weather for"}}, "required": ["location"]}}}]}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "397"
        content-type:
          - application/json
        cookie:
          - __cf_bm=LhJr7iTKhj3HkuIqsYYQzHnLMYlAsrPO_8DONvOoa1s-1752097448-*******-3QZBiExLtV3A83cyPK4CeYqjpSygQ6uYDZ0v7kYNlmEHZIOldYiM088zk99s107ShpZgiHzSJwcw0Fviy3roAk.Ifv1OH0UzhIx2VTAKaS4;
            _cfuvid=1FpkTfHv2KC0I5jKg65yXzjPU21dBkiuyT323kob89c-1752097448243-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA4xTwW7bMAy9+ysEnuMhSZ1k8W1FgV26YkXnrcVSGIpM22plSZDktEGQfy8sJ7aT
          ZsB8MAQ+vsdHUtoFhADPICbASupYpUV4bf7YJHl63OjfD/W32f31XZ7cJO+3L/eJ5TBqGGr9gswd
          WV+YqrRAx5VsYWaQOmxUJ4vZdLxcRPO5ByqVoWhohXZhpMKKSx5Ox9MoHC/CydcDu1ScoYWY/A0I
          IWTn/41PmeE7xGQ8OkYqtJYWCHGXRAgYJZoIUGu5dVQ6GPUgU9KhbKzLWogB4JQSKaNC9IXbbzc4
          98OiQqTmNindr0ex3XL94+dE25v1w2b+vRjUa6W32hvKa8m6IQ3wLh6fFSMEJK08t0CXviF1JZoz
          OiFATVFXKF1jHXYrEIrRRnAF8Qru8I08KfO6gj2cEPfBpfPzYCYG89pS8XlYVErlfAk/recDsu8W
          I1ShjVrbMyrkXHJbpgap9f0Oxx4cjXgLUJ9sFrRRlXapU6/oi86WrSj0l68HJ7MD6JSjoo8votEF
          tTRDR7lffHfXGGUlZj2zv3O0zrgaAMGg889mLmm33XNZ/I98DzCG2mGWaoMZZ6cN92kGm6f5r7Ru
          xt4wWDQbzjB1HE2zjQxzWov2wYDdWodVmnNZoNGG+1cDuU6vIjqLKC6vGAT74AMAAP//AwBOf92H
          QwQAAA==
      headers:
        CF-RAY:
          - 95cb00ae49452ac0-LAX
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Wed, 09 Jul 2025 21:44:27 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "591"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "604"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999990"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_da2ecab7819e8df314992a8787e2fff0
      status:
        code: 200
        message: OK
version: 1
