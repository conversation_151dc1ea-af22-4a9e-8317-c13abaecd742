interactions:
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s 12 + 12?"}], "model":
        "gpt-4o-mini", "stream": true, "stream_options": {"include_usage": true}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "145"
        content-type:
          - application/json
        cookie:
          - __cf_bm=LhJr7iTKhj3HkuIqsYYQzHnLMYlAsrPO_8DONvOoa1s-1752097448-*******-3QZBiExLtV3A83cyPK4CeYqjpSygQ6uYDZ0v7kYNlmEHZIOldYiM088zk99s107ShpZgiHzSJwcw0Fviy3roAk.Ifv1OH0UzhIx2VTAKaS4;
            _cfuvid=1FpkTfHv2KC0I5jKg65yXzjPU21dBkiuyT323kob89c-1752097448243-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string:
          'data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          +"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          equals"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"24"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}],"usage":null}


          data: {"id":"chatcmpl-BrWsECewLnZpwYk2y6TeJIT6H7F48","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[],"usage":{"prompt_tokens":14,"completion_tokens":8,"total_tokens":22,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}


          data: [DONE]


          '
      headers:
        CF-RAY:
          - 95cb00484b3214f6-LAX
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Wed, 09 Jul 2025 21:44:10 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "327"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "332"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_82e49575f67bda7dced7355056a8aef0
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s 12 + 12?"}], "model":
        "gpt-4o-mini", "stream": true, "stream_options": {"include_usage": true}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "145"
        content-type:
          - application/json
        cookie:
          - __cf_bm=LhJr7iTKhj3HkuIqsYYQzHnLMYlAsrPO_8DONvOoa1s-1752097448-*******-3QZBiExLtV3A83cyPK4CeYqjpSygQ6uYDZ0v7kYNlmEHZIOldYiM088zk99s107ShpZgiHzSJwcw0Fviy3roAk.Ifv1OH0UzhIx2VTAKaS4;
            _cfuvid=1FpkTfHv2KC0I5jKg65yXzjPU21dBkiuyT323kob89c-1752097448243-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string:
          'data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          +"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          equals"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"24"},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}],"usage":null}


          data: {"id":"chatcmpl-BrWsEMMFl7DBEVA7ID65Tklpkbnbs","object":"chat.completion.chunk","created":1752097450,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_62a23a81ef","choices":[],"usage":{"prompt_tokens":14,"completion_tokens":8,"total_tokens":22,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}


          data: [DONE]


          '
      headers:
        CF-RAY:
          - 95cb004baed214f6-LAX
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Wed, 09 Jul 2025 21:44:11 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "203"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "208"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_6a6ea838af6542b8572e83b621c03f90
      status:
        code: 200
        message: OK
version: 1
