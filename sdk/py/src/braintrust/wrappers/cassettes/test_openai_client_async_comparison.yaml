interactions:
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"gpt-4o-mini","seed":42,"temperature":0}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "106"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb9QwEIXv+RXWXLupEhORsMce2iIBQogDCFWRa0+ybh3b2BNaqPa/
          IyfbTQqt1EsO882bvDeeh4wx0Aq2DOROkBy8yc/kp7sqfPzz/tv9Z3759a64/X75RVUX52f8w1vY
          JIW7vkFJj6pT6QZvkLSzM5YBBWGaWtZVUzVNWfMJDE6hSbLeU165fNBW57zgVV7Uedkc1DunJUbY
          sh8ZY4w9TN/k0yq8hy0rNo+VAWMUPcL22MQYBGdSBUSMOpKwBJsFSmcJ7WS95OyElZzhz1GYyHh1
          um4M2I1RJLN2NGYFhLWORAo7Wbw6kP3RlHG9D+46/iOFTlsdd21AEZ1NBiI5DxPdZ4xdTeHHJ3nA
          Bzd4asnd4vS7sprHwbLyBTYHRo6EWcqcb54Z1iokoU1c7Q6kkDtUi3JZtBiVdiuQrSL/7+W52XNs
          bfvXjF+AlOgJVesDKi2f5l3aAqZ7fKntuOLJMEQMv7TEljSG9AwKOzGa+Uog/o6EQ9tp22PwQc+n
          0vn2HcfiTV0XTQnZPvsLAAD//wMAdPse5DgDAAA=
      headers:
        CF-RAY:
          - 9472cb1dff31728a-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:32 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=ACvanrvDDdClYpufGDkvW3WdIRGVOem0UIhBjCRp4HE-1748488172-*******-EbrqjqvlfugpDE5yBVbLhKNcI5siYyYyO1_gfUyFH19kF.u4BAL5vgjZ1YR_NXBIlbzchla2gDsOD8lMbVoeZzL977owwr85YNr6LjdeJkg;
            path=/; expires=Thu, 29-May-25 03:39:32 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=NfWilA6YB4UOd_P4xU1o9cre4ute9B0ASs9R0fnJpKQ-1748488172895-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "426"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "429"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_001a33a36414defefecd0088fcc9672e
      status:
        code: 200
        message: OK
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"gpt-4o-mini","seed":42,"temperature":0}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "106"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBT9wwEIXv+RXWXNmgjUnZdI+IS4taFS49IBR57UnW4NjGntBWaP87
          crK7CRQkLjnMN2/y3nieM8ZAK1gzkFtBsvMmv5A//3y5+n3xXd/YR7U8v3n6el3+uPz26zpsrmCR
          FG5zj5IOqlPpOm+QtLMjlgEFYZparMqqrKpidTaAzik0SdZ6ykuXd9rqnC95mS9XeVHt1VunJUZY
          s9uMMcaeh2/yaRX+hTVbLg6VDmMULcL62MQYBGdSBUSMOpKwBIsJSmcJ7WC94OyEFZzhYy9MZLw8
          nTcGbPooklnbGzMDwlpHIoUdLN7tye5oyrjWB7eJb6TQaKvjtg4oorPJQCTnYaC7jLG7IXz/Kg/4
          4DpPNbkHHH5XlOM4mFY+wWrPyJEwU5nzxTvDaoUktImz3YEUcotqUk6LFr3SbgayWeT/vbw3e4yt
          bfuZ8ROQEj2hqn1ApeXrvFNbwHSPH7UdVzwYhojhSUusSWNIz6CwEb0ZrwTiv0jY1Y22LQYf9Hgq
          ja/PueBnoiqwgWyXvQAAAP//AwAEquOGOAMAAA==
      headers:
        CF-RAY:
          - 9472cb291a25d826-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:33 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=YVSQNhGU5D_XZKKHlGiF2ASuyzAJ6dCDpQHNZ0PK45Y-1748488173-*******-6Fwa3GoGwXMRDtobRN38cHAxxT.1JgwMWTJ8NyXBuRGzdJEkZt7GiwmbWmxIXXzJe0nsxMGCcP7gxTKgOw_ZmAwoh_dRaKeQqI3tYMpKZgQ;
            path=/; expires=Thu, 29-May-25 03:39:33 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=qt53dwZTdoU.J1LuD_WqqiwsjRgIE6Lh9ziMs5KTO0s-1748488173691-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "605"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "608"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_454870f19e34ada2f4d3403f7a46ba71
      status:
        code: 200
        message: OK
version: 1
