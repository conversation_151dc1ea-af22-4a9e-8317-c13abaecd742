interactions:
  - request:
      body:
        '{"max_tokens":300,"messages":[{"role":"user","content":"what is 2+2? (just
        the number)"}],"model":"claude-3-haiku-20240307","stream":true}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        anthropic-version:
          - "2023-06-01"
        connection:
          - keep-alive
        content-length:
          - "138"
        content-type:
          - application/json
        host:
          - api.anthropic.com
        user-agent:
          - Anthropic/Python 0.52.1
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 0.52.1
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
        x-stainless-stream-helper:
          - messages
        x-stainless-timeout:
          - NOT_GIVEN
      method: POST
      uri: https://api.anthropic.com/v1/messages
    response:
      body:
        string: 'event: message_start

          data: {"type":"message_start","message":{"id":"msg_019YzLeF9bnSTwHRpHPNEbid","type":"message","role":"assistant","model":"claude-3-haiku-20240307","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":19,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":3,"service_tier":"standard"}}        }


          event: content_block_start

          data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}  }


          event: ping

          data: {"type": "ping"}


          event: content_block_delta

          data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"4"}        }


          event: content_block_stop

          data: {"type":"content_block_stop","index":0          }


          event: message_delta

          data: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":5}     }


          event: message_stop

          data: {"type":"message_stop" }


          '
      headers:
        CF-RAY:
          - 9472c8a1fea943f1-EWR
        Cache-Control:
          - no-cache
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Thu, 29 May 2025 03:07:50 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Robots-Tag:
          - none
        anthropic-organization-id:
          - 27796668-7351-40ac-acc4-024aee8995a5
        anthropic-ratelimit-input-tokens-limit:
          - "8000000"
        anthropic-ratelimit-input-tokens-remaining:
          - "8000000"
        anthropic-ratelimit-input-tokens-reset:
          - "2025-05-29T03:07:50Z"
        anthropic-ratelimit-output-tokens-limit:
          - "1500000"
        anthropic-ratelimit-output-tokens-remaining:
          - "1500000"
        anthropic-ratelimit-output-tokens-reset:
          - "2025-05-29T03:07:50Z"
        anthropic-ratelimit-requests-limit:
          - "10000"
        anthropic-ratelimit-requests-remaining:
          - "9999"
        anthropic-ratelimit-requests-reset:
          - "2025-05-29T03:07:50Z"
        anthropic-ratelimit-tokens-limit:
          - "9500000"
        anthropic-ratelimit-tokens-remaining:
          - "9500000"
        anthropic-ratelimit-tokens-reset:
          - "2025-05-29T03:07:50Z"
        cf-cache-status:
          - DYNAMIC
        request-id:
          - req_011CPb5rAezEMDtahaZ57coa
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        via:
          - 1.1 google
      status:
        code: 200
        message: OK
version: 1
