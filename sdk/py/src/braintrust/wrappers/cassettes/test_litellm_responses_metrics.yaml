interactions:
  - request:
      body:
        '{"model": "gpt-4o-mini", "input": "What''s 12 + 12?", "instructions": "Just
        the number please"}'
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "94"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - litellm/1.74.0.post1
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//dFRbbuMwDPzPKQR9NwtHsfM6Qq9QLAxaphNtZdGQqKJFkbsvLD9i76Y/
          QTJDDskhle+NENLU8iKkx9CVh9MBUYGGJstPu3N1OmUKq/O+LtRR6eaQ1fu6OGuNVbNXRXOUL70A
          VX9Q8yRCLuCAa4/AWJfQc7tjobLzMS9U4gIDx9DnaGo7i4z1kFSBfr96iq7vqgEbMMHoPXl5ES5a
          mwDjpsSyRgZjw5oN7KNmQy4VeY2BBd9QuNhW6EVnEaY2W/gsKXIXuWR6R7cS6kkmsqUGuy7RUo22
          1752vM1p2xpntipT+TY7bnen0ZqkKy/ibSOEEN/pc/a8DdfR8kpleaGT5epwQGyyXIFqMK+eWp40
          +KvDpIIhwBUfxE/eJlKTY3SPlpZtrWQnS/CT5+wUAM4Rw2Tt2+8VaenaeaqeMEnoIqTK5Qzfx29z
          pPRkU3UIwQQGx0NwH5iCZAcerEW73gr7ONxJ5/HDUAzldIplsnremkcI5Iy7yss4tsSmIc+LoN7C
          2Lbgv0ZwI8R9uFr0H0ZjyQb7Y5Q1NhDt4I8MTB6XvTC2HXrgmODdr2xEkw9j8YZ8C4/fC/9T3Dz8
          UH+Y+UZGDyZFJjkTD88lU1cuNpHNYLdsxEen0x7TKCZAZadXGNNFzV0at3odSr38jy+e4TyLBn3D
          +pGYDfOM2f8+OvUMfyY7L/EnZSYGuxDOZwdjwNXfSIsMNTD08vfN/S8AAAD//wMAgZcEoxEFAAA=
      headers:
        CF-RAY:
          - 95cb0056befb2b4d-LAX
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Wed, 09 Jul 2025 21:44:18 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=.a_i4x2pM4E8VvtpvgDLS_gUCW9V1maiL8fgpHihVmc-1752097458-*******-NuvCq9dUlaAS6wrbxXXsbkVjtdhZHxmHmrDnpmLFfkeonCyyDg3gXv7U7HIUHm7YlvL0vt8yVErOl38C3yx5I0vZv9nT05Q3baFTj2uv1DA;
            path=/; expires=Wed, 09-Jul-25 22:14:18 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=p0pkm7Hdk9Et0Ip8OlwrOSFo07x.aFcl0AxUMV_mRBc-1752097458702-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "6054"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_93c2e083b4fc6c5a0dcea290411430d4
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"model": "gpt-4o-mini", "input": "What''s 12 + 12?", "instructions": "Just
        the number please"}'
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "94"
        content-type:
          - application/json
        cookie:
          - __cf_bm=.a_i4x2pM4E8VvtpvgDLS_gUCW9V1maiL8fgpHihVmc-1752097458-*******-NuvCq9dUlaAS6wrbxXXsbkVjtdhZHxmHmrDnpmLFfkeonCyyDg3gXv7U7HIUHm7YlvL0vt8yVErOl38C3yx5I0vZv9nT05Q3baFTj2uv1DA;
            _cfuvid=p0pkm7Hdk9Et0Ip8OlwrOSFo07x.aFcl0AxUMV_mRBc-1752097458702-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - litellm/1.74.0.post1
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RUW47bMAz8zykEfW8KW3nYyRF6hUVh0DKdVVcWDYla7GKRuxeWH7Hb9CdIZsgh
          OaTyvRNCmkZehfQY+upcnhFVrXRZZGV+gfp8aorigEV+OOQKiwxPdX25QHtR56zOT/JlEKD6N2qe
          RcgFHHHtERibCgYuL04quxTHU5m4wMAxDDmaut4iYzMm1aDfb56iG7pqwQZMMHpPXl6Fi9YmwLg5
          sWqQwdiwZQP7qNmQS0V+xsCC31C42NXoRW8R5jY7+Kwoch+5YnpHtxEaSCaylQa7LdFRg3bQvvW8
          P9K+M87sVaaO+6zY5+VkTdKVV/G6E0KI7/S5eN6F22z54dKcy8HyEo4K26bNlD432QGeWp40+KvH
          pIIhwA0fxP+8TaQmx+geLa3b2sjOluAnL9kpAJwjhtna118b0tKt91Q/YZLQVUh1lAt8n74tkdKT
          TdUhBBMYHI/BQ2AKkj14sBbtdivs43gnvccPQzFU8ylWyeplax4hkDPuJq/T2BLbljyvggYLY9eB
          /5rAnRD38WrRfxiNFRscjlE22EK0oz8yMHlc98LY9eiBY4LzH9mEJh+m4i35Dh6/V/6nuGX4sf44
          8xsZPZoUmeRCPDyXTH212kS2gP26ER+dTntMo5gAtZ1fYUwXtXRp3OZ1KPXyL756hsssGvQbNo/E
          bJxnyv770aln+DPZZYn/U2ZisCvh4+JgDLj5G+mQoQGGQf6+u/8BAAD//wMAYrV2IREFAAA=
      headers:
        CF-RAY:
          - 95cb007d0bd42b4d-LAX
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Wed, 09 Jul 2025 21:44:19 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "998"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_be3af56d2d9472ef83e71f8638878b53
      status:
        code: 200
        message: OK
version: 1
