interactions:
  - request:
      body: '{"messages":[{"role":"user","content":"What''s 12 + 12?"}],"model":"gpt-4o-mini","stream":true}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "94"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string:
          'data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          +"},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"12"},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          equals"},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"
          "},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"24"},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{"content":"."},"logprobs":null,"finish_reason":null}]}


          data: {"id":"chatcmpl-BcNw7ndvnSncW8DxhS1idxONpgqxU","object":"chat.completion.chunk","created":1748488175,"model":"gpt-4o-mini-2024-07-18","service_tier":"default","system_fingerprint":"fp_34a54ae93c","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}


          data: [DONE]


          '
      headers:
        CF-RAY:
          - 9472cb393cde1492-EWR
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Thu, 29 May 2025 03:09:36 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=h1vL7TCxyZQWYLiVATU5khuFWAfl7e7DD1hCduxzE8U-1748488176-1.0.1.1-sJaNE8XFsJQdiSrAjR90nA7rr9yBOdYJcgr.dqonlOei8.cltJKLgEqEf98WWTEAjzZY_3NkDCTKZ6BAoprnwscA9Xg3RSagZKs2syOsiqc;
            path=/; expires=Thu, 29-May-25 03:39:36 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=SNSxlGXUc7grIzRg7NgjFZuLpm8RWoGQ8CcpSyF99Wo-1748488176143-0.0.1.1-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "462"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "481"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_574eb3b1e3585ef7a72808f9c33a6bcb
      status:
        code: 200
        message: OK
version: 1
