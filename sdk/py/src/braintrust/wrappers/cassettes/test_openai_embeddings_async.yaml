interactions:
  - request:
      body: '{"input":"This is a test","model":"text-embedding-ada-002","encoding_format":"base64"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "86"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/embeddings
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//VHrJsrJMt+b8v4o33in1h4hILr8Z0kmbidKoFRUVYIOgqDSZQJ44936C
          vStOVU0cYBqYzXq6lf/xrz9//n7y6nbp//7z5++r7Pq//2N+ds367O8/f/7nv/78+fPnP34+/7+R
          tzq/Xa/lu/gZ/vNl+b7exr///BH/+8n/HfTPn7+OejJYQNq87cI9ytaRLMlUKu5TPvjXUwjx+8EZ
          UZMX6sVE1hSMVJ/lh2tRTWnyEQF3z4QZVbflfXkOAZJyecKEmmM13DL0XAvS0yVGDILXu8vWQJt+
          N+HpPpJ2DKvuCeW4eDNrlbB2CA/1E4kghiwbzm8+bO3ShU8jceIfvlU+HdPeUFalnbBTUpOq9Q+i
          ADQ5tUxtcJUPj2CIQVRVkfmvwtRHicgFrKZ7g6Wn2aJp+zpLqNzXW3qR07KiI18foIk/BTPMbx+N
          7112AcplzrTQ2nqirhuTYlS7gnLH+EQsFn0fnROvI+apcSLuHGWMIvljsrN131Z8/X4KEDVVhYf7
          ttMH1d4baJlnX2IoH5kz534zkLOIRgzysebjrR2fIL31Cut3wB57SlyEk9PGVAreXT7Qq2Og/RDv
          iV2Irde/4qQD+qoC5u3Cbz49X+4HXsvLDad8UHK6i/JYCXS9J/YiOXpT1VkZKCtOKfT2W5+Mu3iB
          42W5IjvLLvIpUM4qoCcX8GP92VbL3XZvgX5szyyIwgF15iY9oIP6TjC/C2nbbZ0khfd2u2NBH5kt
          XT5OHxQuyZ24HccRFXTNgtrsS+KeNwPqz8LW3WTC8CLWvt1E122OS6X4ZpQZQkC9gextDOfqNBAH
          1/dofEb2CXr5e2cme66qyQ7cAbVCTZn7GbataErbG5Tj5k2Xjgx8OCrWCZmhORDvdWT6tHFgAvJl
          O+IPzEBD+1WNzet1Ctjejop2MCXnhtoiyoi513b5ENx3FLbEKJjfO200NX5TwFppgDloRaLhI10y
          UIc1xbz1J/0trrMTbEG902JcfSt+xYW4eeY8wu24XbcPd1lZINXTnTlf48ZZuPgaaK2MNyoJZY34
          a4AYNPdkkuiOvnzkFz7AIz0JlNbRMqetj2qoTVYS9a7u0FIxug5QKRFmXOy93h+9RYPOBqzppnZy
          b5jrFXn24LBb9i6jeT19EJzGI7uFI0aNv30paD7/xDRFD43luVEVZGkTXQqnrppGMT+Btt6puNvl
          O94tTd1aVGoI+NYDqqblp7+gh4gs4j8Ur1p9X/cLOFpU4tX+/Kn6fvkEiDzxw3bOoEVcG8IUgsc5
          Yn5fC22fuOcDFKtOZ97h/vXGZ3S+oPM4pcTDchvRx5h1YJvugBfGkebd3uMKnKv0jsuN1PEhuPVP
          5ITPFfHTLtSpXr9d6NrMI+bl0OZP6DjA4pW/iev2PhpEH0toknKFLqiV8GE92SGcPs6FqU2n8H61
          D0TIr2dCAnhn7URVeCrHTLaJJ3uGx8uNf0KP9ErwFFZi3u+vLwuNbnOjYxVg3mDRvqGuPXmMqPwa
          jcmeGrDfKxkeKj2uRtxYGDy3l4mZmKLXySqR0Dw/puVU42Oyv2rANYjIFV4HvZNlRUDVJvLwFDs9
          7w7G4MIyj09MZ1ip+Iw3IMjVGm9K/dH2y7TF6GrWJXFlQ2qf8/zWFzfBbLvpUzS8tOgCixcWmZpf
          JMTvTlwgmoQOls/ekw9b5xoDj9QnCUT0bscrv99QVlz3RNvGYtvP9YnUorTJzsVrzi/J8gOUSyWd
          pDDKafRlohIs6JuYbVFXdM/SEN5774vlgyrp3NvL3U/9seDL4opejbqG/HovmD7jZz2JywKO68uD
          HapTn7NVH53AhI9NjKqvvcnfXkQwjIuEx3gL+iS+lRAG8N94Mbae3hIrkpR5P5g28wc/PbIC+eZw
          pmuUFznD5rqDZ756UuhdUo2Ja/uIfNcK8dLHWNFwWYsgLcM383dZ1Y5nAWII7WRktjWmEedrJ0Z+
          6izYNn91aFTPawzOYj/S44yX7fqdK7CTuIvTy8VsR1iFMbi55DK8tGqv1+ONgNJtRJiWs7c3IF43
          wAqk01cRc047S1Xgo2wS/F1cnu20/AglWiRuxVy9SKpBEr4ZXF9oxayT1Hn8kjyfSKqlgc14rk9E
          LJ9AuVjSaSMn+WQUZoluobxjxns4oElJKaDbw1Lwy5TEiJbN2gD/GT5++Yb/4FddZleCoxt49JQK
          NmJlu6DKTd1UzfqyKkFxDE5MZXDbgV63FtqgWGLuTb1WnSUdCmVXNx9iCO42WjE/8eHrpIhs159H
          O3XWHuCAhx2L5/3s6JMKqFh/JxJEYcg5PcQdHC8s+eFnb5m45xAWr8gk2/X3xacBWRZYeTgRd2cR
          bzyZzEB5+4mYyR2f8yseZTius5DpE2domPZPH9YBGoh5CTW+PKTfGGZ+ZcapytEYVssPRJ68xsl2
          0nNmtQt/3TuiRjDJPH3S79sQYWwEJI6vWjTdV1sbLqt9QDBQW1+9BkiRbZIt0elQ6SMIvQ1TAjoz
          lLfY9o97eNnM/EMXatroNC5zDe1LM8CDUX6qQZnOMx7mEdseBeZ9uiY5AfkihXi7fVBNnEYy0OQG
          eAFKg7i4ri6wbHuBbsZj2tYhVQFO0mVkmrF65FwpvhiU9+X8w5/6iBuMFVVTVZIyuufjan+TYVd/
          PmRneb0+fu33AKq+vuGh6RQ0+NdHDOyQIbwZ0ylqdy+/hHt3Y8xG7FqNr7iekBu8LIxSs2z7qUoy
          9FGQgefz0Y786oso8hYu2U53FPXR8VKgmc+Yk9BHPnwlqYCjf9tjuMqVPtX31+G3nhfRy2mnb+S7
          6Aef3C9ee80x1QA9z4JF1FHu+fi2hhSl23yk0h43+hg+74OyuXQ+cxNkR+MrvnaQURoT+7G/5bzQ
          HhJE3nRk1p7QiNk3Dps3e9+JtlntdX5+QIP82A+ZXj2qfIrLSNvIzIiYl5paNSwNRUVEVi9YkQ2p
          oo51MqB80IqydZJUQxHvAUDABpWvQZCP7jJT0QCJQ9dnUeGfaieL6M1uG/zS9gudFdppgPXFFemg
          Pozoc3lLEhKk2qX15fJqJ/1+SGHBS4eoRmlXrDsJMQI9WNIhPzf6UO1kCbY9z/EwygH/mR967DY7
          otF37/VVZrjQCjefWZeLWQ2J/NFQaF0i5mWWxfnNDg/IzUWXYZK1sx5qKWq3BWaWuXmg8TKuDeAL
          SSVqWT/yafvaS7962V61frsK/CjdcE3RmPPI5ZYd35UF4VNxmW1xAY1q5h7gK7kntpWMoZ31VwaJ
          cVhQttw/2/HW5RQub82Z/cCDD9xdl2tdCqZfPdVcN+UEW6ISYq6CK2d3p6/BW71qSqPbxeOb+Oyi
          Gb9J0EevahDucgfFaoiJLp3MmQ+mEJp4GbHgVoicpldM4QdfbLSvvbHKEh+07JKyHZN9bwyGoFMO
          N+VLF9Hr206IHFyAbWgRtxMP1QBdoyDc1QmVTnJcTebZmeDotwYF+Wgh5kd3C7bEHomlKE80PLfr
          GN3hoRKyTte8U21V3cz1xcixuc3+Qp3WVnvUmdHGFWfTQlRhJ40umfmZj/fXQ0PEwwumV1WLuPXc
          xwjF2YkZKzXlox4/FZiks8Ls51nNJf3uHH78CFXcRtVXg3m+oE1vTmyX5KM37Znpw8uuB7qZ9czv
          7z9+mLF7mWmIczX1f+qdqeu7V3H0iE9gremXwi2ePB5x7sLMP3S8f9btJJWxAnz8YqoMJ71aDp9p
          2DDNt8l5ceu95ocvIm84EuNN3Zx/npoLx9ZCTJOwi4ZFcZDgkd4J/nS6yxlZnFQ49GlBJ2PtcA7Z
          NCBy/kjE+6DCGztxPaCT07+Ydk+Kmc+3tx+8w/LnlrQTWRodnKvHgRmFk+Sc+Vcfgn2iEW176PTO
          lLYX8E1qE1ziwhsqe6vAtBtDEpQWjjroSgXqeGdjlFaZNwnaWoO2cGXiXE8eGoR3UMAdKpVCdnAq
          nlCjA108vYhmbxRE15dFgezkLP6el67aDRLij5PLzrhe5P0hVTJoi31Gdk+85UuT7EooXKUmhvKO
          22lQNhpcX+sVlfaByWkbGJ/NIt0MRA/ZUedX/BGh1e8bWiubsZ3WCRpgxluGYYwR3XaLDhQb9uSm
          OpU3vvENo/BlbXB5FyEaFgWAMutH9vN/3zP+/ehvzOr7yeOiEtTrVLCBnQ5bJWpCOu9VQUPmDPib
          T1akHdB7I1VMbdq0Gt2lKmzau2AQU6KlPnpUzqBD39l/7Hz0XR6DFE5H+NJpI/l8vHXaZ/Ojj+Xn
          uYgoRqsT2CMEuCMG9QboShksX0TM+2aIDzsmK+Cbnc1Mha9yamVYRSfpNuJFfWlz9hqqA/jTZ8fM
          alXk4yGdTuAGWUTcnenyMVsNNShL4UFHyTp7Az4qihLo2/4Xn6j4ng6w4BQY8TbvdupVJ1OCBcqo
          pChPPu0p2Gh7LY9Mk4K6GvnVEKE/xS67ZHsz4gclnpSmsj94lWQ+outEfcIyvyl4eepGNH0OIWw6
          PD6YkwYbj1+JK/z6p4/Wv6JRCrc1Oq7LiQpLq9Y/5+471/9ig4Vbd6zGFi1LUIfRJ44mV/wbfB1Q
          6ti06UsZmnb4srBBM17hFVu9UdNZogHCWRLm/c3QIAlhDHZUv8kP37IsX7rws/+Wdh5Qw7YphUdd
          FTQwtGXbnbtp1lOHiXlppXhMplYNhvHYkV3x+eRTvwvsFezbjqnGc/R6I5jwRstah5mnXvK6ad/5
          KJKn9+wP4nYcywaD/pKveMVvZitN4qYEHnkW23Elbicioga5wdvCi6P/zqf63ocwnzfM3y8x4kJC
          byhgTcrcIfP1yd29Mgi0tUdITkS9/eGD5XqhMQzDUPEs7wz0zJdPYglCwqeQ2gLCyLoyclv2LS3b
          yEcNP+a4z64fnVXSniL+yFymz3nFNPtL9AzOPR3G5Vaf+QHDmFGG5VVj5+Nn1xZoADtl8bYu2n7l
          rDWQN4XLdp/DgU8bp6JwOoo7to1QGY1jebZRrQs182+p0nail39gxkf8fnbnqBfGSUDB/jBS9D3x
          qNeiTwNTqsXMFETU8tGqJ2Vc720WiEqVD/xwz9CMtwTP54M113UN5FzGVNGNuh1dWMjomvQGwUvz
          geiMh0pyKHS2e4ZNNJXTDaC7ejvmuau31yMhNmDOV8jWYKxqfvzWzL/kOedvrZBcM5Qc6piQRnvy
          4YG3p809EE0sWdsJ0Vm/ozbUUuJoa1tfBX53QmkvOPgliKhqv9eihOvTaJi/u3Rev9P8AzBjVzNr
          72/bJaweJ4S4rjFP9p76oGlqCfT1ZcQKPmE7gePUsMHVGQtzXsHrMb2AejgmTFUfRs5fg2P/4B/e
          wLOf/UwtgHEAgbjXxwMNn6USghh6KeU/+Flok6Ls9TbFy0uooSlQkI0E5+P95G1o5isX4nfFseS8
          Kv6rR1Nhf8PodSR6n8nbD/CIHImanxtv6JpFAYtXkrJARLtqOqYXFR1bx2fOeZoi/hraA+jHt00c
          /JTQj39UBPloMT1fb/OxX0oq3JXBwM0hsnJa3y8YFFtRyQ5VCupbtCnhmjR81vPbaOUdWhlw161I
          /pOHCLpr/azPb/7KlwqjYEcXk50XF6NdHT+Bj7SPEtDNmGCPf5/LFKZdfWXezT2iUXY1C5h2OP/m
          WfxcBh1UvXWa81ZWDfRZC8o+GuUZ3zo0ON/******************************/3/lewTFuS+
          Qg2nnYDm9WeajQ7RXN8HRXnXCA939T37272gTDseMne1eOit2V4tBEy8kW1eGdEvPph3BfAiqnvU
          yfIkQN4uLaYarySnGCUhpD04LKy/VO8aNg1KsZJeRJeyoGVCYqRokS4GHG4sJx8eOLuhn/z2PJC+
          6s9CG8J1uoU0v1IbTctH2KCv45+p4I1PNJJKu2x2dbdmeLlr8nF5NGc9997/4kmTOZOMjutTSDTl
          gPh4SB+p0sRcY3a31fKltbVVcML+TH/O2/LorRq0igZGLtnBabkxkQnugZLS4b71vTX+5ipkxYv/
          6odePX9cJJ3eHX2jSuFDbL1roFWn4tXVOEbNoGxUZYCjw9TltfImtd3HCIZdhcWqt/S+Xwoa+p5f
          C+aCqfDf/HYA/Cb+Lt/N/i4MN7dFMTBX1rA+rvaBhL7vlU65oO705TZb3tDF9TvmFIvv7MeuPrIn
          gxEHybzqusaIwQx3A97IpRmJc14K+HblLJDLVzSJ75MNJsV7jHaHPZ/Hp9DwJGea/2j5ePQWH7DW
          3ZfKOINqzOWMwk/+bSZm7M35gADFiurMcHBZjck+UWHaNZSp9BBHE1vLw4//octT21evN04xmEDP
          dO0WJhq3uVX+5inSxwE0brPuhIpzfMXyqu2qDnXqBCE/HZj78sO5/9AUylUMfGLxIcvZpyq0jXJO
          VgS/JM0bw+dxgJn/8Pb+WVeTUaRP9MzHiGBvcvTukbxDpCzlkAV97KAeVmGK2sKW536DHq2upLfA
          OAgCm/PMnEtdgcEXXxLZNssQzfjSQGwjBwtj4Vd8d1MnQHwXUX46rr1JvztzPhkJhKhp43UH3xCQ
          cZAuBI/lUh+ReZRRvJxWTKs3Zdv+5GtfB5+JGVyC2S/LMejH75nYWm/mk5IaIsz9Cbo0l6Eu9Yvh
          AJ9L2v7wuz6dyDf+1a+zv2ql9LJsNrfFe0sMll0jqXp+CrQqDYPcYBT5JJUXGdr9WmTmG5YV887v
          BnZ1fWTu443y8Sw4LsTH8x7XsVhVv/rve/yEzMWH0hurMzc2h/5AWCAKcTSce89GC/5pWdT6B280
          cJz++pUsPa6ioUZFB+vu+J75z2qH/mlI8FHWxpznP9GcRzawLw4qIR7o+RIW5ICuYnQlurNLvClz
          viKCQT8Q9wx+OzlWaG2UM0nw3Tgd2kGZ9jEU38uCBKk8of5jBw3M+oAFhiF49LqxG5iOywfR/c6N
          hvHEBDTzP7nCM+DjRtbLn/pn2pxfDuYmDYFagviLt0NGSYY0PxGJi4d1O1yKUIBAkxSiB86m6iTh
          lIII8oPYZ/+r93M+g374/JKxC+fZBWO4dxfGIsfsqvHWbURl7g8xO0AF4sxDFnrf45ztm+XAWQln
          H9xrP+c9tpp3X3b6wE/9p5fLq/q+4nqAGT+I/ZR4y7WVgOG9ESviXLM4GhNX9RU0CSPB5b7jVHyH
          7sZaN6c5L8/QsjyfAOJXkxDn827b0V3agGY/Retnefe6paFoUD5WJhXW7krvatAkCNPnEktzP4od
          3+Aiyx+aGc90rxeTQd0sA+dF12eeVtOgVMJastMP0QzZnPPYzEdzPjjzQ4Ded8e7AB9bzHaW86wm
          byfcfvCAqevbLvp2gGswqb9ngbrdR1NyyUu4PcgFgy42sz/4ij/9KrJt5GvVI+FiKapm7H/PG3tK
          SIKreVHZ9VZeozl/loBrskb0SRT16bopB7hpxYNoxvr7m09t/Gn60tVVj/QhMtsUGQ+s0aVENW8K
          lMZAwR4XzOsWe33VnS4+2u+FlhhKHeZ9HH5FSEbrQuzPdR/VIx8PYN43ASGk2npLA3sHhfKFQcfp
          dpzfv+/AN6fzr1/gp0f1BH1XrvAnEIKWV5L62STl6kS2/inhU9lq7uacOB0h0etb8XdrKj/5EB7t
          qKiYuK5uMPdH8PLChHaIzCxEVm6HJPj2vs77/VSDZ08OI6WczXmb36BLdh8IvjVs1nPN5ycvxKsk
          33vtJrYtCNjTZFr4lKuu/drWb74fBc6mpa41+fA90gf54Y+O7MsYrDbRiSvert7U+GUBP/0LT+9O
          0YgET4Pv+X4lmo2mnG3yhapETToQ7wNGPp8XgO+xe+DNQXzwSa+ZCzvbIORHn0+fsyT+4q/9sz6h
          ekmhuwgxC8bz3esWhTPBrL8xEO3VjtFR+mw61BKcHeKmHa4PwwZ7clRiXOxRH16adoKffqp+R1/E
          pO6DwTDaLwkMLamGeLFtwLqaC6bfkYOWDiQDyGpHmfty7ZmvnwIQ7/igI83d6Lc/8pWsHVGj0dan
          BXwyZIzZkg5G1fHh3CkWrEo3YdacBwzHaf2En3z3J88e7GHpAo+0J/HEGqN5PoOyzoUtBWIeoumn
          PxM+hXz2E6tqXEy+9oPPzLK2Bz5+1UECOUQPtrPeF/0nD0Z/f24F/Oe//vz5Xz83DOrP9faaLwb0
          t7H/939fFfh3ds3+LYrSv5n0exOBdllx+/vP/7mE8Pfbfupv/7/7z/P27v7+80f+vW3wt//02ev/
          efyv+V3/+a//AgAA//8DAHFvCBnhIAAA
      headers:
        CF-RAY:
          - 9472cb0bbc8ff9a9-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:28 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=84OzWlNXgYMGAt5FSILjYkBxQnLfxRM9T6sTPr7E2N0-1748488168-*******-TJpRZ290t0Z5Uq9dhD15juux6xjityCLsOqmqy5zY6YxJMmg5ab2pYLgStF8FDqF4rP7Mjaj19wS_rQnFmSjwZzzddTv0dUkyS8jlLNceCY;
            path=/; expires=Thu, 29-May-25 03:39:28 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=o9N.JCQtfm8gFHjK9_H7B8hekt9k6wXQI4CCCLcBspw-1748488168605-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-allow-origin:
          - "*"
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-model:
          - text-embedding-ada-002-v2
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "54"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        via:
          - envoy-router-568fcbbc46-l4b5w
        x-envoy-upstream-service-time:
          - "56"
        x-ratelimit-limit-requests:
          - "10000"
        x-ratelimit-limit-tokens:
          - "10000000"
        x-ratelimit-remaining-requests:
          - "9999"
        x-ratelimit-remaining-tokens:
          - "9999996"
        x-ratelimit-reset-requests:
          - 6ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_35cb99ebdd92a80fa815ff1c733b51fb
      status:
        code: 200
        message: OK
  - request:
      body: '{"input":"This is a test","model":"text-embedding-ada-002","encoding_format":"base64"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "86"
        content-type:
          - application/json
        cookie:
          - __cf_bm=84OzWlNXgYMGAt5FSILjYkBxQnLfxRM9T6sTPr7E2N0-1748488168-*******-TJpRZ290t0Z5Uq9dhD15juux6xjityCLsOqmqy5zY6YxJMmg5ab2pYLgStF8FDqF4rP7Mjaj19wS_rQnFmSjwZzzddTv0dUkyS8jlLNceCY;
            _cfuvid=o9N.JCQtfm8gFHjK9_H7B8hekt9k6wXQI4CCCLcBspw-1748488168605-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/embeddings
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA1R6ybKyTLfm/L+KN94p9YeISC6/GdJJm4nSqBUVFWCDoKg0mUCeOPd+gr0rTlVN
          HGAamM16upX/8a8/f/5+8up26f/+8+fvq+z6v/9jfnbN+uzvP3/+57/+/Pnz5z9+Pv+/kbc6v12v
          5bv4Gf7zZfm+3sa///wR//vJ/x30z5+/jnoyWEDavO3CPcrWkSzJVCruUz7411MI8fvBGVGTF+rF
          RNYUjFSf5YdrUU1p8hEBd8+EGVW35X15DgGScnnChJpjNdwy9FwL0tMlRgyC17vL1kCbfjfh6T6S
          dgyr7gnluHgza5WwdggP9ROJIIYsG85vPmzt0oVPI3HiH75VPh3T3lBWpZ2wU1KTqvUPogA0ObVM
          bXCVD49giEFUVZH5r8LUR4nIBayme4Olp9miafs6S6jc11t6kdOyoiNfH6CJPwUzzG8fje9ddgHK
          Zc600Np6oq4bk2JUu4Jyx/hELBZ9H50TryPmqXEi7hxljCL5Y7Kzdd9WfP1+ChA1VYWH+7bTB9Xe
          G2iZZ19iKB+ZM+d+M5CziEYM8rHm460dnyC99Qrrd8Aee0pchJPTxlQK3l0+0KtjoP0Q74ldiK3X
          v+KkA/qqAubtwm8+PV/uB17Lyw2nfFByuovyWAl0vSf2Ijl6U9VZGSgrTin09lufjLt4geNluSI7
          yy7yKVDOKqAnF/Bj/dlWy912b4F+bM8siMIBdeYmPaCD+k4wvwtp222dJIX3drtjQR+ZLV0+Th8U
          LsmduB3HERV0zYLa7EvinjcD6s/C1t1kwvAi1r7dRNdtjkul+GaUGUJAvYHsbQzn6jQQB9f3aHxG
          9gl6+XtnJnuuqskO3AG1Qk2Z+xm2rWhK2xuU4+ZNl44MfDgq1gmZoTkQ73Vk+rRxYALyZTviD8xA
          Q/tVjc3rdQrY3o6KdjAl54baIsqIudd2+RDcdxS2xCiY3zttNDV+U8BaaYA5aEWi4SNdMlCHNcW8
          9Sf9La6zE2xBvdNiXH0rfsWFuHnmPMLtuF23D3dZWSDV0505X+PGWbj4GmitjDcqCWWN+GuAGDT3
          ZJLojr585Bc+wCM9CZTW0TKnrY9qqE1WEvWu7tBSMboOUCkRZlzsvd4fvUWDzgas6aZ2cm+Y6xV5
          9uCwW/Yuo3k9fRCcxiO7hSNGjb99KWg+/8Q0RQ+N5blRFWRpE10Kp66aRjE/gbbeqbjb5TveLU3d
          WlRqCPjWA6qm5ae/oIeILOI/FK9afV/3CzhaVOLV/vyp+n75BIg88cN2zqBFXBvCFILHOWJ+Xwtt
          n7jnAxSrTmfe4f71xmd0vqDzOKXEw3Ib0ceYdWCb7oAXxpHm3d7jCpyr9I7LjdTxIbj1T+SEzxXx
          0y7UqV6/XejazCPm5dDmT+g4wOKVv4nr9j4aRB9LaJJyhS6olfBhPdkhnD7OhalNp/B+tQ9EyK9n
          QgJ4Z+1EVXgqx0y2iSd7hsfLjX9Cj/RK8BRWYt7vry8LjW5zo2MVYN5g0b6hrj15jKj8Go3Jnhqw
          3ysZHio9rkbcWBg8t5eJmZii18kqkdA8P6blVONjsr9qwDWIyBVeB72TZUVA1Sby8BQ7Pe8OxuDC
          Mo9PTGdYqfiMNyDI1RpvSv3R9su0xehq1iVxZUNqn/P81hc3wWy76VM0vLToAosXFpmaXyTE705c
          IJqEDpbP3pMPW+caA4/UJwlE9G7HK7/fUFZc90TbxmLbz/WJ1KK0yc7Fa84vyfIDlEslnaQwymn0
          ZaISLOibmG1RV3TP0hDee++L5YMq6dzby91P/bHgy+KKXo26hvx6L5g+42c9icsCjuvLgx2qU5+z
          VR+dwISPTYyqr73J315EMIyLhMd4C/okvpUQBvDfeDG2nt4SK5KUeT+YNvMHPz2yAvnmcKZrlBc5
          w+a6g2e+elLoXVKNiWv7iHzXCvHSx1jRcFmLIC3DN/N3WdWOZwFiCO1kZLY1phHnaydGfuos2DZ/
          dWhUz2sMzmI/0uOMl+36nSuwk7iL08vFbEdYhTG4ueQyvLRqr9fjjYDSbUSYlrO3NyBeN8AKpNNX
          EXNOO0tV4KNsEvxdXJ7ttPwIJVokbsVcvUiqQRK+GVxfaMWsk9R5/JI8n0iqpYHNeK5PRCyfQLlY
          0mkjJ/lkFGaJbqG8Y8Z7OKBJSSmg28NS8MuUxIiWzdoA/xk+fvmG/+BXXWZXgqMbePSUCjZiZbug
          yk3dVM36sipBcQxOTGVw24FetxbaoFhi7k29Vp0lHQplVzcfYgjuNloxP/Hh66SIbNefRzt11h7g
          gIcdi+f97OiTCqhYfycSRGHIOT3EHRwvLPnhZ2+ZuOcQFq/IJNv198WnAVkWWHk4EXdnEW88mcxA
          efuJmMkdn/MrHmU4rrOQ6RNnaJj2Tx/WARqIeQk1vjyk3xhmfmXGqcrRGFbLD0SevMbJdtJzZrUL
          f907okYwyTx90u/bEGFsBCSOr1o03VdbGy6rfUAwUFtfvQZIkW2SLdHpUOkjCL0NUwI6M5S32PaP
          e3jZzPxDF2ra6DQucw3tSzPAg1F+qkGZzjMe5hHbHgXmfbomOQH5IoV4u31QTZxGMtDkBngBSoO4
          uK4usGx7gW7GY9rWIVUBTtJlZJqxeuRcKb4YlPfl/MOf+ogbjBVVU1WSMrrn42p/k2FXfz5kZ3m9
          Pn7t9wCqvr7hoekUNPjXRwzskCG8GdMpancvv4R7d2PMRuxaja+4npAbvCyMUrNs+6lKMvRRkIHn
          89GO/OqLKPIWLtlOdxT10fFSoJnPmJPQRz58JamAo3/bY7jKlT7V99fht54X0ctpp2/ku+gHn9wv
          XnvNMdUAPc+CRdRR7vn4toYUpdt8pNIeN/oYPu+Dsrl0PnMTZEfjK752kFEaE/uxv+W80B4SRN50
          ZNae0IjZNw6bN3vfibZZ7XV+fkCD/NgPmV49qnyKy0jbyMyImJeaWjUsDUVFRFYvWJENqaKOdTKg
          fNCKsnWSVEMR7wFAwAaVr0GQj+4yU9EAiUPXZ1Hhn2oni+jNbhv80vYLnRXaaYD1xRXpoD6M6HN5
          SxISpNql9eXyaif9fkhhwUuHqEZpV6w7CTECPVjSIT83+lDtZAm2Pc/xMMoB/5kfeuw2O6LRd+/1
          VWa40Ao3n1mXi1kNifzRUGhdIuZllsX5zQ4PyM1Fl2GStbMeailqtwVmlrl5oPEyrg3gC0klalk/
          8mn72ku/etletX67Cvwo3XBN0ZjzyOWWHd+VBeFTcZltcQGNauYe4Cu5J7aVjKGd9VcGiXFYULbc
          P9vx1uUULm/Nmf3Agw/cXZdrXQqmXz3VXDflBFuiEmKugitnd6evwVu9akqj28Xjm/jsohm/SdBH
          r2oQ7nIHxWqIiS6dzJkPphCaeBmx4FaInKZXTOEHX2y0r72xyhIftOySsh2TfW8MhqBTDjflSxfR
          69tOiBxcgG1oEbcTD9UAXaMg3NUJlU5yXE3m2Zng6LcGBfloIeZHdwu2xB6JpShPNDy36xjd4aES
          sk7XvFNtVd3M9cXIsbnN/kKd1lZ71JnRxhVn00JUYSeNLpn5mY/310NDxMMLpldVi7j13McIxdmJ
          GSs15aMePxWYpLPC7OdZzSX97hx+/AhV3EbVV4N5vqBNb05sl+SjN+2Z6cPLrge6mfXM7+8/fpix
          e5lpiHM19X/qnanru1dx9IhPYK3pl8Itnjwece7CzD90vH/W7SSVsQJ8/GKqDCe9Wg6fadgwzbfJ
          eXHrveaHLyJvOBLjTd2cf56aC8fWQkyTsIuGRXGQ4JHeCf50ussZWZxUOPRpQSdj7XAO2TQgcv5I
          xPugwhs7cT2gk9O/mHZPipnPt7cfvMPy55a0E1kaHZyrx4EZhZPknPlXH4J9ohFte+j0zpS2F/BN
          ahNc4sIbKnurwLQbQxKUFo466EoF6nhnY5RWmTcJ2lqDtnBl4lxPHhqEd1DAHSqVQnZwKp5QowNd
          PL2IZm8URNeXRYHs5Cz+npeu2g0S4o+Ty864XuT9IVUyaIt9RnZPvOVLk+xKKFylJobyjttpUDYa
          XF/rFZX2gclpGxifzSLdDEQP2VHnV/wRodXvG1orm7Gd1gkaYMZbhmGMEd12iw4UG/bkpjqVN77x
          DaPwZW1weRchGhYFgDLrR/bzf98z/v3ob8zq+8njohLU61SwgZ0OWyVqQjrvVUFD5gz4m09WpB3Q
          eyNVTG3atBrdpSps2rtgEFOipT56VM6gQ9/Zf+x89F0egxROR/jSaSP5fLx12mfzo4/l57mIKEar
          E9gjBLgjBvUG6EoZLF9EzPtmiA87Jivgm53NTIWvcmplWEUn6TbiRX1pc/YaqgP402fHzGpV5OMh
          nU7gBllE3J3p8jFbDTUoS+FBR8k6ewM+KooS6Nv+F5+o+J4OsOAUGPE273bqVSdTggXKqKQoTz7t
          Kdhoey2PTJOCuhr51RChP8Uuu2R7M+IHJZ6UprI/eJVkPqLrRH3CMr8peHnqRjR9DiFsOjw+mJMG
          G49fiSv8+qeP1r+iUQq3NTquy4kKS6vWP+fuO9f/YoOFW3esxhYtS1CH0SeOJlf8G3wdUOrYtOlL
          GZp2+LKwQTNe4RVbvVHTWaIBwlkS5v3N0CAJYQx2VL/JD9+yLF+68LP/lnYeUMO2KYVHXRU0MLRl
          2527adZTh4l5aaV4TKZWDYbx2JFd8fnkU78L7BXs246pxnP0eiOY8EbLWoeZp17yumnf+SiSp/fs
          D+J2HMsGg/6Sr3jFb2YrTeKmBB55FttxJW4nIqIGucHbwouj/86n+t6HMJ83zN8vMeJCQm8oYE3K
          3CHz9cndvTIItLVHSE5Evf3hg+V6oTEMw1DxLO8M9MyXT2IJQsKnkNoCwsi6MnJb9i0t28hHDT/m
          uM+uH51V0p4i/shcps95xTT7S/QMzj0dxuVWn/kBw5hRhuVVY+fjZ9cWaAA7ZfG2Ltp+5aw1kDeF
          y3afw4FPG6eicDqKO7aNUBmNY3m2Ua0LNfNvqdJ2opd/YMZH/H5256gXxklAwf4wUvQ98ajXok8D
          U6rFzBRE1PLRqidlXO9tFohKlQ/8cM/QjLcEz+eDNdd1DeRcxlTRjbodXVjI6Jr0BsFL84HojIdK
          cih0tnuGTTSV0w2gu3o75rmrt9cjITZgzlfI1mCsan781sy/5Dnnb62QXDOUHOqYkEZ78uGBt6fN
          PRBNLFnbCdFZv6M21FLiaGtbXwV+d0JpLzj4JYioar/XooTr02iYv7t0Xr/T/AMwY1cza+9v2yWs
          HieEuK4xT/ae+qBpagn09WXECj5hO4Hj1LDB1RkLc17B6zG9gHo4JkxVH0bOX4Nj/+Af3sCzn/1M
          LYBxAIG418cDDZ+lEoIYeinlP/hZaJOi7PU2xctLqKEpUJCNBOfj/eRtaOYrF+J3xbHkvCr+q0dT
          YX/D6HUkep/J2w/wiByJmp8bb+iaRQGLV5KyQES7ajqmFxUdW8dnznmaIv4a2gPox7dNHPyU0I9/
          VAT5aDE9X2/zsV9KKtyVwcDNIbJyWt8vGBRbUckOVQrqW7Qp4Zo0fNbz22jlHVoZcNetSP6Thwi6
          a/2sz2/+ypcKo2BHF5OdFxejXR0/gY+0jxLQzZhgj3+fyxSmXX1l3s09olF2NQuYdjj/5ln8XAYd
          VL11mvNWVg30WQvKPhrlGd86NDjf9QledrslrtuoHvO9woVIpimGLNx63x/9/5XsExbkvkINp52A
          5vVnmo0O0VzfB0V51wgPd/U9+9u9oEw7HjJ3tXjordleLQRMvJFtXhnRLz6YdwXwIqp71MnyJEDe
          Li2mGq8kpxglIaQ9OCysv1TvGjYNSrGSXkSXsqBlQmKkaJEuBhxuLCcfHji7oZ/89jyQvurPQhvC
          dbqFNL9SG03LR9igr+OfqeCNTzSSSrtsdnW3Zni5a/JxeTRnPffe/+JJkzmTjI7rU0g05YD4eEgf
          qdLEXGN2t9XypbW1VXDC/kx/ztvy6K0atIoGRi7ZwWm5MZEJ7oGS0uG+9b01/uYqZMWL/+qHXj1/
          XCSd3h19o0rhQ2y9a6BVp+LV1ThGzaBsVGWAo8PU5bXyJrXdxwiGXYXFqrf0vl8KGvqeXwvmgqnw
          3/x2APwm/i7fzf4uDDe3RTEwV9awPq72gYS+75VOuaDu9OU2W97QxfU75hSL7+zHrj6yJ4MRB8m8
          6rrGiMEMdwPeyKUZiXNeCvh25SyQy1c0ie+TDSbFe4x2hz2fx6fQ8CRnmv9o+Xj0Fh+w1t2XyjiD
          aszljMJP/m0mZuzN+YAAxYrqzHBwWY3JPlFh2jWUqfQQRxNby8OP/6HLU9tXrzdOMZhAz3TtFiYa
          t7lV/uYp0scBNG6z7oSKc3zF8qrtqg516gQhPx2Y+/LDuf/QFMpVDHxi8SHL2acqtI1yTlYEvyTN
          G8PncYCZ//D2/llXk1GkT/TMx4hgb3L07pG8Q6Qs5ZAFfeygHlZhitrClud+gx6trqS3wDgIApvz
          zJxLXYHBF18S2TbLEM340kBsIwcLY+FXfHdTJ0B8F1F+Oq69Sb87cz4ZCYSoaeN1B98QkHGQLgSP
          5VIfkXmUUbycVkyrN2Xb/uRrXwefiRlcgtkvyzHox++Z2Fpv5pOSGiLM/Qm6NJehLvWL4QCfS9r+
          8Ls+ncg3/tWvs79qpfSybDa3xXtLDJZdI6l6fgq0Kg2D3GAU+SSVFxna/Vpk5huWFfPO7wZ2dX1k
          7uON8vEsOC7Ex/Me17FYVb/673v8hMzFh9IbqzM3Nof+QFggCnE0nHvPRgv+aVnU+gdvNHCc/vqV
          LD2uoqFGRQfr7vie+c9qh/5pSPBR1sac5z/RnEc2sC8OKiEe6PkSFuSArmJ0JbqzS7wpc74igkE/
          EPcMfjs5VmhtlDNJ8N04HdpBmfYxFN/LggSpPKH+YwcNzPqABYYhePS6sRuYjssH0f3OjYbxxAQ0
          8z+5wjPg40bWy5/6Z9qcXw7mJg2BWoL4i7dDRkmGND8RiYuHdTtcilCAQJMUogfOpuok4ZSCCPKD
          2Gf/q/dzPoN++PySsQvn2QVjuHcXxiLH7Krx1m1EZe4PMTtABeLMQxZ63+Oc7ZvlwFkJZx/caz/n
          Pbaad192+sBP/aeXy6v6vuJ6gBk/iP2UeMu1lYDhvREr4lyzOBoTV/UVNAkjweW+41R8h+7GWjen
          OS/P0LI8nwDiV5MQ5/Nu29Fd2oBmP0XrZ3n3uqWhaFA+ViYV1u5K72rQJAjT5xJLcz+KHd/gIssf
          mhnPdK8Xk0HdLAPnRddnnlbToFTCWrLTD9EM2Zzz2MxHcz4480OA3nfHuwAfW8x2lvOsJm8n3H7w
          gKnr2y76doBrMKm/Z4G63UdTcslLuD3IBYMuNrM/+Io//SqybeRr1SPhYimqZux/zxt7SkiCq3lR
          2fVWXqM5f5aAa7JG9EkU9em6KQe4acWDaMb6+5tPbfxp+tLVVY/0ITLbFBkPrNGlRDVvCpTGQMEe
          F8zrFnt91Z0uPtrvhZYYSh3mfRx+RUhG60Lsz3Uf1SMfD2DeNwEhpNp6SwN7B4XyhUHH6Xac37/v
          wDen869f4KdH9QR9V67wJxCClleS+tkk5epEtv4p4VPZau7mnDgdIdHrW/F3ayo/+RAe7aiomLiu
          bjD3R/DywoR2iMwsRFZuhyT49r7O+/1Ug2dPDiOlnM15m9+gS3YfCL41bNZzzecnL8SrJN977Sa2
          LQjY02Ra+JSrrv3a1m++HwXOpqWuNfnwPdIH+eGPjuzLGKw20Ykr3q7e1PhlAT/9C0/vTtGIBE+D
          7/l+JZqNppxt8oWqRE06EO8DRj6fF4DvsXvgzUF88EmvmQs72yDkR59Pn7Mk/uKv/bM+oXpJobsI
          MQvG893rFoUzway/MRDt1Y7RUfpsOtQSnB3iph2uD8MGe3JUYlzsUR9emnaCn36qfkdfxKTug8Ew
          2i8JDC2phnixbcC6mgum35GDlg4kA8hqR5n7cu2Zr58CEO/4oCPN3ei3P/KVrB1Ro9HWpwV8MmSM
          2ZIORtXx4dwpFqxKN2HWnAcMx2n9hJ989yfPHuxh6QKPtCfxxBqjeT6Dss6FLQViHqLppz8TPoV8
          9hOralxMvvaDz8yytgc+ftVBAjlED7az3hf9Jw9Gf39uBfznv/78+V8/Nwzqz/X2mi8G9Lex//d/
          XxX4d3bN/i2K0r+Z9HsTgXZZcfv7z/+5hPD3237qb/+/+8/z9u7+/vNH/r1t8Lf/9Nnr/3n8r/ld
          //mv/wIAAP//AwBxbwgZ4SAAAA==
      headers:
        CF-RAY:
          - 9472cb0e498df9a9-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:28 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-allow-origin:
          - "*"
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-model:
          - text-embedding-ada-002-v2
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "53"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        via:
          - envoy-router-dc9d5f6f7-bfg9n
        x-envoy-upstream-service-time:
          - "56"
        x-ratelimit-limit-requests:
          - "10000"
        x-ratelimit-limit-tokens:
          - "10000000"
        x-ratelimit-remaining-requests:
          - "9999"
        x-ratelimit-remaining-tokens:
          - "9999996"
        x-ratelimit-reset-requests:
          - 6ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_9392a0a1e391f86ea94c557497f4903a
      status:
        code: 200
        message: OK
version: 1
