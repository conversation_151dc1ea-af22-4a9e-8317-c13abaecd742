interactions:
- request:
    body: '{"contents": [{"parts": [{"text": "What''s the weather in San Francisco?"}],
      "role": "user"}], "systemInstruction": {"parts": [{"text": "You are a helpful
      weather assistant. Use the get_weather tool to answer questions about weather.\n\nYou
      are an agent. Your internal name is \"weather_agent\"."}], "role": "user"},
      "tools": [{"functionDeclarations": [{"description": "Get the weather for a location.",
      "name": "get_weather", "parameters": {"properties": {"location": {"type": "STRING"}},
      "required": ["location"], "type": "OBJECT"}}]}], "generationConfig": {}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '561'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - google-genai-sdk/1.31.0 gl-python/3.9.21 google-adk/1.14.1 gl-python/3.9.21
      x-goog-api-client:
      - google-genai-sdk/1.31.0 gl-python/3.9.21 google-adk/1.14.1 gl-python/3.9.21
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61SXU+DMBR9768gfR4LMofG100TP4hTyWJijLnChTWWFttuy1z23y1fG0x9ExJS
        7jk9596ebonj0BhEwhIwqOmF82IrjrOtviUmhUFhLNCWbLEAZQ7c+tl21paSLkVsmBQT4Ly3ucEF
        5GjrNEPztkYwC1R0cEwClelfNluEyxhK+VLiCYRzpUDETMeSHnF35K+/w/r1YEyV5FVfuUyQt2K7
        lkBTJphePCLoxju6n+37prDK7mRWKPletu2eDwPf98bBaeD59h2fjND1AtKaV7Z0qSHDEA3YAGA/
        LLUieWEi+YFiIpdVAOOz2qiTVw8PGthIA7yPjAY/VPXUejLejbGTsB0fODObcsbo8jnqZGP1e021
        Z0Q6R3nc4j+ZBX0v0iRThzVHpZsbkWFuc3L9oeemHPSiEqQKdSGFxuuk5Ez9NITwFqfhav1p9EzG
        XzebB4+SHfkGVtQS/hUDAAA=
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 18 Sep 2025 20:09:51 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=530
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
- request:
    body: '{"contents": [{"parts": [{"text": "What''s the weather in San Francisco?"}],
      "role": "user"}, {"parts": [{"functionCall": {"args": {"location": "San Francisco"},
      "name": "get_weather"}}], "role": "model"}, {"parts": [{"functionResponse":
      {"name": "get_weather", "response": {"location": "San Francisco", "temperature":
      "72\u00b0F", "condition": "sunny", "humidity": "45%", "wind": "5 mph NW"}}}],
      "role": "user"}], "systemInstruction": {"parts": [{"text": "You are a helpful
      weather assistant. Use the get_weather tool to answer questions about weather.\n\nYou
      are an agent. Your internal name is \"weather_agent\"."}], "role": "user"},
      "tools": [{"functionDeclarations": [{"description": "Get the weather for a location.",
      "name": "get_weather", "parameters": {"properties": {"location": {"type": "STRING"}},
      "required": ["location"], "type": "OBJECT"}}]}], "generationConfig": {}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '881'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - google-genai-sdk/1.31.0 gl-python/3.9.21 google-adk/1.14.1 gl-python/3.9.21
      x-goog-api-client:
      - google-genai-sdk/1.31.0 gl-python/3.9.21 google-adk/1.14.1 gl-python/3.9.21
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61R0U7bQBB891esTuobiS5OHELfqqaRUIEisACprdA2Xsen2nfmbl2IovwT38CX
        cefUwaGv9YO12pnbGc1sIgCxRJ2pDJmc+Ajf/QZg0/4DZjSTZg90K7+s0fIbd/dterOnMD2FRyIt
        CB4JuSALSsM1alhY1EvllgaUA9dovYZHxQUgMFU1WeTGEpgcjuOX58UQwomiqVSmeB2eTJIP4B0D
        h9PKD36XQFUXcHE7/KFFz8h2P/88erNvTUnBW2UyKjv6tiOIXGnliitCZ3SgXaffLsUexT+rM7Oq
        rfkVEhjIoZQymU1HUo6n8iSeTpKTWRx14q2saByu6JwYfci4j1L4I1XNqflN+rNp2pBnk51Qr5MD
        fNzhbBjLA2g0mh39c9fNvaoq+2X1evQBYOlTbYv6cpeKXkh8aKtLKeqF+d7kfxIbvxOL/paz6+uG
        rFO7YlZU+aoG8VAO8hJd0V4UllxttKPTLHDmcX6Op+nXi3v19MDu8j6npPkkRbSNXgFas32N/AIA
        AA==
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 18 Sep 2025 20:09:52 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=612
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
version: 1
