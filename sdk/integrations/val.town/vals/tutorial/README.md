# Build better AI products with Braintrust

[![Open Val Town Template](https://stevekrouse-badge.web.val.run/?3)](https://www.val.town/v/braintrust/sdk)

[Braintrust](https://www.braintrust.dev/) helps you evaluate and ship AI products with confidence. Whether you're building a new AI application or improving an existing one, Braintrust gives you the tools to iterate faster and deploy with confidence.

## What you can do with this SDK

The Braintrust SDK enables you to:

- Log experiments and production data to understand your AI system's behavior
- Run comprehensive evaluations using the `Eval` framework
- Track and improve your model's performance over time

This template helps you get started with the Braintrust SDK. It's based on our [official GitHub repository](https://github.com/braintrustdata/braintrust-sdk).

## Getting started

1. Click **Fork** on this val to create your own copy
2. Get your API key from the [Braintrust settings page](https://www.braintrust.dev/app/settings?subroute=api-keys)
3. Add your API key to your project's [Environment Variables](https://www.val.town/settings/environment-variables) as `BRAINTRUST_API_KEY`
4. Click **Run** on the `tutorial` val to start experimenting

Want to learn more? Visit our [documentation](https://www.braintrust.dev/docs) or sign up for a [free account](https://www.braintrust.dev/).
