# Braintrust Val Town examples

A collection of example vals showing how to use Braintrust with Val Town.

## Examples

### Tutorial

[![Open Val Town Template](https://stevekrouse-badge.web.val.run/?3)](https://esm.town/v/braintrust/sdk)

A simple example showing how to use the Braintrust SDK to evaluate an AI function:

- Sets up a basic evaluation using the `Eval` framework
- Uses the Levenshtein scorer to compare outputs
- Shows how to structure test cases

## Running the examples

1. Navigate to the example you want to try (e.g., `vals/tutorial`)
2. Follow the setup instructions in the example's README
3. Fork and run the val to see it in action

## Resources

- [Braintrust documentation](https://www.braintrust.dev/docs)
- [Val Town documentation](https://docs.val.town)
