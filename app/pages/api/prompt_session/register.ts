import type { NextApiRequest, NextApiResponse } from "next";
import { invokeServiceRoleSupabaseUdf } from "../_invoke_supabase_udf";
import { promptSessionSchema } from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = z.object({
  org_name: z.string(),
  project_name: z.string(),
  session_name: z.string(),
});

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_prompt_session", {
    removeFields: ["update"],
    paramsSchema,
    outputSchema: promptSessionSchema,
    argnames: ["auth_id", "org_name", "project_name", "session_name"],
  });
}
