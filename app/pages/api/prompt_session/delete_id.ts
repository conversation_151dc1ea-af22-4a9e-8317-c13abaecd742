import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  setDeletedAtObjects,
  extractSingularRow,
} from "../_object_crud_util";
import { promptSessionSchema } from "@braintrust/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) =>
      extractSingularRow({
        rows: await setDeletedAtObjects({
          authLookup,
          permissionInfo: {
            aclObjectType: "prompt_session",
          },
          filters: {
            id: [params.id],
          },
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema: idParamSchema,
      outputSchema: promptSessionSchema,
    },
  );
}
