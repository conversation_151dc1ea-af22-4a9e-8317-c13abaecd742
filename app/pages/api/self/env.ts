import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { z } from "zod";

export default async function getEnv(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    () =>
      Promise.resolve({
        env: process.env.NODE_ENV,
      }),
    {
      paramsSchema: z.object({}).nullish(),
      outputSchema: z.object({
        env: z.string().nullish(),
      }),
    },
  );
}
