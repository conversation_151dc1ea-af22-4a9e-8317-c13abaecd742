import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { deriveErrorContext } from "#/utils/derive-error-context";
import { errorContextSchema } from "@braintrust/local";
import { z } from "zod";

const paramsSchema = z.object({}).nullish();

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (_params, authLookup) => deriveErrorContext(authLookup),
    {
      paramsSchema,
      outputSchema: errorContextSchema,
    },
  );
}
