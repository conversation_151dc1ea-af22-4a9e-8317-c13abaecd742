import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import { endpointSchemas } from "@braintrust/local/app-schema";
import type { z } from "zod";

const { input: paramsSchema, output: outputSchema } =
  endpointSchemas.self_auth_token_ids;

async function helper(
  _params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  return {
    user_id: authLookup.user_id,
    org_id: authLookup.org_id,
  };
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema,
  });
}
