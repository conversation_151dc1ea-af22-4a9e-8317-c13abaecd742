import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { meQuery, meSchema } from "#/app-db-views/me";
import { z } from "zod";
import { KV } from "#/utils/cache";
import { sha1 } from "#/utils/hash";
import { waitUntil } from "@vercel/functions";
import { isAllowedSysadmin } from "#/utils/derive-error-context";

const paramsSchema = z
  .object({
    check_sysadmin: z.boolean().optional(),
  })
  .nullish();

export function makeCacheToken(auth_id: string) {
  const authIdHash = `${sha1(auth_id)}`;
  return `me_query_${authIdHash}`;
}

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const key = makeCacheToken(authLookup.auth_id);
  const kv = new KV();
  let result: unknown[] | null = null;
  {
    const resp = await kv.get(key, z.array(z.unknown()));
    if (resp.status === "found") {
      result = resp.value;
    }
  }

  if (result === null) {
    const { query, queryParams } = meQuery({ auth_id: authLookup.auth_id });
    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(query, queryParams.params);
    result = rows;
    waitUntil(
      kv.set(
        key,
        result,
        // Cache for 60s
        { ex: 60 },
      ),
    );
  }

  if (!result || result.length === 0) {
    throw new HTTPError(400, "User does not exist");
  }
  const ret: Record<string, unknown> = z.record(z.unknown()).parse(result[0]);
  if (params?.check_sysadmin) {
    const isSysadmin = await isAllowedSysadmin(authLookup);
    ret.is_sysadmin = isSysadmin;
  }
  return ret;
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: meSchema,
  });
}
