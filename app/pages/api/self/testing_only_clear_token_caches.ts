// Testing-only endpoint for clearing out certain caches that interfere with
// certain integration tests. Ideally we would clear caches optimistically, but
// this is not always easy / high-priority.

import type { NextApiRequest, NextApiResponse } from "next";
import { getAuthTokenAndOrgName, runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import { HTTPError } from "#/utils/server-util";
import { z } from "zod";
import { KV } from "#/utils/cache";
import { makeCacheToken as meQueryMakeCacheToken } from "./me";
import { makeCacheKey as lookupApiKeyMakeCacheKey } from "../_lookup_api_key";

const paramsSchema = z.object({}).nullish();

async function helper(
  _params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
  extraArgs?: {
    req: NextApiRequest;
    res: NextApiResponse;
  },
) {
  if (!process.env.ALLOW_TESTING_ONLY_CACHE_OPS) {
    throw new HTTPError(403, "Forbidden");
  }
  const kv = new KV();

  // Clears the cache used for app/pages/api/self/me.ts.
  {
    const key = meQueryMakeCacheToken(authLookup.auth_id);
    await kv.del(key);
  }

  // Clears the cache used for app/pages/api/_lookup_api_key.ts:lookupApiKey.
  if (extraArgs) {
    const { token, org_name } = getAuthTokenAndOrgName(extraArgs.req);
    if (token) {
      const key = lookupApiKeyMakeCacheKey({ key: token, org_name });
      await kv.del(key);
    }
  }

  return null;
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: z.null(),
  });
}
