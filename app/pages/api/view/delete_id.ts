import type { NextApiRequest, NextApiResponse } from "next";
import { type AuthLookup } from "../_lookup_api_key";
import { runJsonRequest } from "../_request_util";
import { setDeletedAtObjects, extractSingularRow } from "../_object_crud_util";
import { viewIdParamsSchema, makeViewFullResultsQuery } from "./_util";
import { viewMetadataSchema } from "@braintrust/local/app-schema";
import { type z } from "zod";

async function helper(
  params: z.infer<typeof viewIdParamsSchema>,
  authLookup: AuthLookup,
) {
  const { id, object_type, object_id } = params;

  const { viewFullResultsQuery, queryParams, notFoundErrorMessage } =
    makeViewFullResultsQuery({
      authLookup,
      object_type,
      object_id,
      aclPermission: "update",
      id: [id],
    });

  return extractSingularRow({
    rows: await setDeletedAtObjects({
      fullResultsQueryOverride: viewFullResultsQuery,
      baseTableOverride: "view",
      startingParams: queryParams,
      fullResultsSize: 1,
      notFoundErrorMessage,
    }),
    notFoundErrorMessage: undefined,
  });
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema: viewIdParamsSchema,
    outputSchema: viewMetadataSchema,
  });
}
