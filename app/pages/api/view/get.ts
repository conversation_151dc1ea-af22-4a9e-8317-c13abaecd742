import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import {
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { makeViewFullResultsQuery, viewFilterParamsSchema } from "./_util";
import { aclObjectParamsSchema } from "../acl/_util";
import { viewMetadataSchema } from "@braintrust/local/app-schema";
import { type z } from "zod";

const paramsSchema = paginationParamsSchema
  .merge(aclObjectParamsSchema)
  .merge(viewFilterParamsSchema);

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const { paginationParams, filters: paramsRest } =
    splitPaginationParams(params);
  const { viewFullResultsQuery, queryParams } = makeViewFullResultsQuery({
    authLookup,
    aclPermission: "read",
    ...paramsRest,
  });

  return await getObjects({
    fullResultsQueryOverride: viewFullResultsQuery,
    startingParams: queryParams,
    paginationParams,
    fullResultsSize: undefined,
  });
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: viewMetadataSchema.array(),
    renameFields: { view_name: "name" },
  });
}
