import { z } from "zod";
import type { NextApiRequest, NextApiResponse } from "next";
import { patchViewSchema as patchViewSchemaOrig } from "@braintrust/typespecs";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import { extractSingularRow, patchObjects } from "../_object_crud_util";
import { makeViewFullResultsQuery, viewIdParamsSchema } from "./_util";
import { viewMetadataSchema } from "@braintrust/local/app-schema";

// `view_data` is stored in the API `views` table instead of supabase.
const paramsSchema = patchViewSchemaOrig
  .omit({
    object_type: true,
    object_id: true,
    view_data: true,
  })
  .extend({
    view_data_id: z.string().uuid().nullish(),
  })
  .merge(viewIdParamsSchema);

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const { id, object_type, object_id, ...paramsRest } = params;

  const { viewFullResultsQuery, queryParams, notFoundErrorMessage } =
    makeViewFullResultsQuery({
      authLookup,
      object_type,
      object_id,
      aclPermission: "update",
      id: [id],
    });

  return extractSingularRow({
    rows: await patchObjects({
      fullResultsQueryOverride: viewFullResultsQuery,
      baseTableOverride: "view",
      startingParams: queryParams,
      patchValueParams: paramsRest,
      fullResultsSize: 1,
      notFoundErrorMessage,
    }),
    notFoundErrorMessage: undefined,
  });
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: viewMetadataSchema,
  });
}
