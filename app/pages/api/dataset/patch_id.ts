import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import { datasetSchema, patchDatasetSchema } from "@braintrust/typespecs";

const paramsSchema = idParamSchema.merge(patchDatasetSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, ...paramsRest }, authLookup) =>
      extractSingularRow({
        rows: await patchObjects({
          authLookup,
          permissionInfo: {
            aclObjectType: "dataset",
          },
          filters: {
            id: [id],
          },
          patchValueParams: paramsRest,
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema,
      outputSchema: datasetSchema,
    },
  );
}
