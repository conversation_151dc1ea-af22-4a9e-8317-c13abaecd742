import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import {
  createDatasetSchema,
  datasetSchema,
  projectSchema,
} from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = createDatasetSchema
  .omit({ name: true, project_id: true })
  .extend({
    dataset_name: createDatasetSchema.shape.name.nullish(),
    project_name: z.string().nullish(),
    project_id: createDatasetSchema.shape.project_id.nullish(),
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

const outputSchema = z.object({
  project: projectSchema,
  dataset: datasetSchema,
  found_existing: z.boolean().optional(),
});

export type RegisterDatasetOutput = z.infer<typeof outputSchema>;

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_dataset", {
    paramsSchema,
    outputSchema,
    argnames: [
      "auth_id",
      "org_id",
      "project_id",
      "project_name",
      "dataset_name",
      "description",
      "update",
      "metadata",
    ],
  });
}
