import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  deleteObjects,
  extractSingularRow,
} from "../_object_crud_util";
import { projectAutomationSchema } from "@braintrust/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const result = await extractSingularRow({
        rows: await deleteObjects({
          authLookup,
          priorObjectTables: ["project_automation"],
          permissionInfo: {
            aclObjectType: "project",
            aclPermission: "update",
          },
          filters: {
            id: [params.id],
          },
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      });

      // attempt to clean up any associated webhook (like a Zap we should help it turn off)
      const parse = projectAutomationSchema.safeParse(result);
      const webhookUrl =
        parse.success && "action" in parse.data.config
          ? parse.data.config.action.url
          : null;

      if (webhookUrl) {
        await fetch(webhookUrl, {
          method: "DELETE",
          signal: AbortSignal.timeout(1000),
        }).catch(console.error);
      }

      return result;
    },
    {
      paramsSchema: idParamSchema,
      postprocessOutput: (row) => {
        // strip the generated columns from the table
        return projectAutomationSchema.parse(row);
      },
      outputSchema: projectAutomationSchema,
    },
  );
}
