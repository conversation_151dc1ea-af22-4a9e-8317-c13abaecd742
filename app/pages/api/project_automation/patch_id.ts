import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import {
  projectAutomationSchema,
  patchProjectAutomationSchema,
} from "@braintrust/typespecs";

const paramsSchema = idParamSchema.merge(patchProjectAutomationSchema).extend({
  config: projectAutomationSchema.shape.config.nullish(),
  user_id: projectAutomationSchema.shape.user_id.nullish(),
});

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, ...paramsRest }, authLookup) =>
      extractSingularRow({
        rows: await patchObjects({
          authLookup,
          priorObjectTables: ["project_automation"],
          permissionInfo: {
            aclObjectType: "project",
            aclPermission: "update",
          },
          filters: {
            id,
          },
          patchValueParams: paramsRest,
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema,
      postprocessOutput: (row) => {
        // strip the generated columns from the table
        return projectAutomationSchema.parse(row);
      },
      outputSchema: projectAutomationSchema,
    },
  );
}
