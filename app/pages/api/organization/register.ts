import type { NextApiRequest, NextApiResponse } from "next";
import { invokeServiceRoleSupabaseUdf } from "../_invoke_supabase_udf";
import { organizationSchema } from "@braintrust/typespecs";
import { z } from "zod";
import {
  getOrCreateOrbCustomer,
  maybeCreateOrbSubscription,
} from "#/utils/billing/utils";
import { waitUntil } from "@vercel/functions";
import { deriveErrorContext } from "#/utils/derive-error-context";
import { getPlanId, PLAN_SLUGS } from "#/app/app/[org]/settings/billing/plans";
import { updatePlanId } from "#/app/app/[org]/settings/billing/actions";

const paramsSchema = z.object({ org_name: z.string().nullish() });

function isQaWolfTestOrg(orgName: string) {
  return orgName.startsWith("qawolf-onboarding-");
}

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const result = await invokeServiceRoleSupabaseUdf(req, res, "register_org", {
    removeFields: ["update"],
    paramsSchema,
    outputSchema: organizationSchema.shape.id,
    argnames: ["auth_id", "org_name"],
  });

  if (!result) {
    return;
  }

  waitUntil(
    (async () => {
      const { out: orgId, authLookup: authLookupRaw } = result;
      const freePlanId = getPlanId(PLAN_SLUGS.FREE);

      const { userEmail, orgName } = await deriveErrorContext({
        ...authLookupRaw,
        org_id: orgId,
      });

      if (!userEmail) {
        throw new Error("User does not have an email");
      }

      // Do not put orgs created in QA Wolf tests into Orb
      // But still set the plan id to free so the UI works as expected
      if (orgName && isQaWolfTestOrg(orgName)) {
        await updatePlanId({ orgId, planId: freePlanId });
        return;
      }

      const orbCustomer = await getOrCreateOrbCustomer({
        orgId,
        orgName: orgName ?? orgId,
        userEmail,
      });

      await maybeCreateOrbSubscription({
        orbCustomerId: orbCustomer.id,
        purchasePlanId: freePlanId,
      });

      await updatePlanId({
        orgId,
        planId: freePlanId,
      });
    })().catch((e) => {
      console.error("Error creating orb customer", e);
    }),
  );
}
