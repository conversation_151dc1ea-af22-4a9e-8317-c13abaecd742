import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  patchOrganizationMembersSchema,
  patchOrganizationMembersOutputSchema,
  organizationSchema,
} from "@braintrust/typespecs";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { addMembers, removeMembers } from "./member_actions";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const supabasePool = getServiceRoleSupabase();
      // Run the additions and removals in a single transaction for atomicity.
      const client = await supabasePool.connect();
      await client.query("begin");
      try {
        let orgId = authLookup.org_id;
        if (params.org_id) {
          if (!orgId) {
            orgId = params.org_id;
          }
          if (orgId !== params.org_id) {
            throw new HTTPError(
              400,
              "Explicitly-specified org_id does not match the API key's organization",
            );
          }
        }
        if (params.org_name) {
          const { rows } = await client.query(
            "select id from organizations where name=$1",
            [params.org_name],
          );
          const paramsOrgId = organizationSchema.shape.id
            .nullish()
            .parse(rows[0]?.id);
          if (!paramsOrgId) {
            throw new HTTPError(400, "Invalid org_name");
          }
          if (!orgId) {
            orgId = paramsOrgId;
          }
          if (orgId !== paramsOrgId) {
            throw new HTTPError(
              400,
              "Explicitly-specified org_name does not match the API key's organization",
            );
          }
        }
        if (!orgId) {
          throw new HTTPError(
            400,
            "You must provide an organization-scoped API key or an explicit org_id or org_name",
          );
        }
        const send_email_error = await (async (): Promise<
          string | null | undefined
        > => {
          if (
            !(
              params.invite_users?.ids?.length ||
              params?.invite_users?.emails?.length
            )
          ) {
            return undefined;
          }
          return (
            await addMembers(
              { orgId, users: params.invite_users },
              authLookup,
              { client },
            )
          ).send_email_error;
        })();
        if (
          params.remove_users?.ids?.length ||
          params.remove_users?.emails?.length
        ) {
          await removeMembers(
            { orgId, users: params.remove_users },
            authLookup,
            { client },
          );
        }
        await client.query("commit");
        return { status: "success", org_id: orgId, send_email_error };
      } catch (e) {
        await client.query("rollback");
        throw e;
      } finally {
        await client.end();
      }
    },
    {
      paramsSchema: patchOrganizationMembersSchema,
      outputSchema: patchOrganizationMembersOutputSchema,
    },
  );
}
