import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { organizationSchema } from "@braintrust/typespecs";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    org_name: true,
    id: true,
  }),
);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        permissionInfo: {
          aclObjectType: "organization",
        },
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: organizationSchema.array(),
    },
  );
}
