import { type NextApiRequest, type NextApiResponse } from "next";
import * as React from "react";
import type { AuthLookup } from "#/utils/server-util";
import { HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { SqlQueryParams } from "#/utils/sql-query-params";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import { z } from "zod";
import {
  patchOrganizationMembersSchema,
  patchOrganizationMembersOutputSchema,
  organizationSchema,
  userSchema,
} from "@braintrust/typespecs";
import { type BtPgClient } from "@braintrust/local/bt-pg";
import InviteUser from "#/ui/email-templates/invite-user";
import { resend } from "#/lib/resend";
import { formatNotFoundErrorMessage } from "@braintrust/local";
import { processUdfError } from "#/pages/api/_invoke_supabase_udf";
import { isObject } from "#/utils/object";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

const _addMembersInputSchema = z.object({
  orgId: organizationSchema.shape.id,
  users: patchOrganizationMembersSchema.shape.invite_users.unwrap().unwrap(),
});

const addMembersOutputSchema = z.object({
  added_users: z
    .object({
      id: userSchema.shape.id,
      email: userSchema.shape.email,
      api_key: z.string().nullish(),
    })
    .array(),
  send_email_error: patchOrganizationMembersOutputSchema.shape.send_email_error,
});

export type AddMembersInput = z.infer<typeof _addMembersInputSchema>;
export type AddMembersOutput = z.infer<typeof addMembersOutputSchema>;

export async function addMembers(
  { orgId, users }: AddMembersInput,
  authLookupRaw?: AuthLookup,
  opts?: {
    client?: BtPgClient;
  },
): Promise<AddMembersOutput> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const queryParams = new SqlQueryParams();
  const orgIdParam = queryParams.add(orgId);
  const userIdParam = queryParams.add(authLookup.user_id);
  const userIdsParam = (users.ids ?? [])
    .map((x) => queryParams.add(x))
    .join(", ");
  const userEmailsParam = (users.emails ?? [])
    .map((x) => queryParams.add(x))
    .join(", ");
  const serviceAccountNamesParam = (users.service_accounts ?? [])
    .map((x) => queryParams.add(x.name))
    .join(",");
  const serviceAccountTokenNamesParam = (users.service_accounts ?? [])
    .map((x) => queryParams.add(x.token_name))
    .join(",");

  const groupIdsParam = (users.group_ids ?? []).concat(
    users.group_id ? [users.group_id] : [],
  );
  const groupNamesParam = (users.group_names ?? []).concat(
    users.group_name ? [users.group_name] : [],
  );

  const { query: groupQuery } = makeFullResultSetQuery({
    authLookup,
    startingParams: queryParams,
    permissionInfo: {
      aclObjectType: "group",
      aclPermission: "update",
    },
    filters: {
      org_id: orgId,
      ...(groupIdsParam?.length
        ? { id: [groupIdsParam[0], ...groupIdsParam.slice(1)] }
        : {}),
      ...(groupNamesParam?.length
        ? { name: [groupNamesParam[0], ...groupNamesParam.slice(1)] }
        : {}),
    },
    filterOpts: {
      orSets:
        groupIdsParam?.length && groupNamesParam?.length
          ? [["id", "name"]]
          : undefined,
    },
  });

  const numGroupSpecs =
    (groupIdsParam?.length ?? 0) + (groupNamesParam?.length ?? 0);
  const query = `
    with
    group_ids_to_add as (
      select coalesce(array_agg(t.id), array [] ::uuid[]) ids
      from (${groupQuery}) "t"
      where ${numGroupSpecs ? "true" : "false"}
    ),
    has_permissions as (
        select (
            has_under_organization_acl(
                'org_member', ${orgIdParam}, ${orgIdParam}, 'create',
                ${userIdParam})
            and ((select cardinality(ids) from group_ids_to_add) = ${queryParams.add(numGroupSpecs)})
        ) value
    ),
    emails_to_process as (
        select unnest(array [${userEmailsParam}]::text[]) email
    ),
    service_accounts_to_create as (
        select unnest(array [${serviceAccountNamesParam}]::text[]) name,
        unnest(array [${serviceAccountTokenNamesParam}]::text[]) token_name
    ),
    new_users as (
        insert into users (email, given_name, user_type)
        select email, given_name, user_type
        from (
            select email, NULL as given_name, 'user'::user_type_enum as user_type from emails_to_process
            union
            select CONCAT('bt::sp::custom::', name, '-', uuid_generate_v4()) as email, name as given_name, 'service_account'::user_type_enum as user_type
              from service_accounts_to_create
        ) invites join has_permissions on has_permissions.value
        on conflict (email) do update set email = excluded.email
        returning id, auth_id, email, user_type, given_name
    ),
    added_user_ids as (
        select
            add_member_to_org_unchecked(
                users_to_add.id,
                ${orgIdParam},
                group_ids_to_add.ids) user_id
        from
            (
                select unnest(array [${userIdsParam}]::uuid[]) id
                union
                select id from new_users
            ) users_to_add
            join has_permissions on has_permissions.value
            left join group_ids_to_add on true
    ),
    added_users as (
        select coalesce(users.id, new_users.id) id, coalesce(users.email, new_users.email) email,
          CASE WHEN new_users.user_type = 'service_account' and service_accounts_to_create.token_name is not null
          THEN create_api_key_full(new_users.auth_id, ${orgIdParam}, service_accounts_to_create.token_name, true)->'api_key'->>'key'
          ELSE NULL END as api_key
        from
            added_user_ids
            left join users on added_user_ids.user_id = users.id
            left join new_users on added_user_ids.user_id = new_users.id
            left join service_accounts_to_create on new_users.given_name = service_accounts_to_create.name
        where users.id is not null or new_users.id is not null
    ),
    added_users_json as (
        select coalesce(jsonb_agg(to_jsonb(added_users)), '[]'::jsonb) val
        from added_users
    )
    select
        has_permissions.value has_permissions,
        added_users_json.val added_users
    from
        has_permissions join added_users_json on true
  `;

  const supabase = opts?.client ?? getServiceRoleSupabase();
  //console.log("Running query\n", substituteParamsDebug(query, queryParams.params));
  const row = await (async () => {
    try {
      return extractSingularRow({
        rows: (await supabase.query(query, queryParams.params)).rows,
        notFoundErrorMessage: undefined,
      });
    } catch (error) {
      if (isObject(error) && error.code === "23503") {
        throw new HTTPError(400, `Foreign key violation: user does not exist`);
      } else {
        throw error;
      }
    }
  })();
  const { has_permissions, added_users } = z
    .object({
      has_permissions: z.boolean(),
      added_users: addMembersOutputSchema.shape.added_users,
    })
    .parse(row);
  if (!has_permissions) {
    const notFoundErrorMessage = formatNotFoundErrorMessage({
      permission: "create org_member",
      objectType: "organization",
      objectIds: [orgId],
    });
    const groupSpecs = (groupIdsParam ?? []).concat(groupNamesParam ?? []);
    throw new HTTPError(
      403,
      `${notFoundErrorMessage}${groupSpecs.length ? ` (groups ${JSON.stringify(groupSpecs)})` : ""}`,
    );
  }

  const send_email_error = await (async () => {
    if (!(users.send_invite_emails && added_users.length)) {
      return undefined;
    }
    try {
      await sendInviteEmail(
        {
          emails: added_users.reduce(
            (acc, x) => (x.email ? acc.concat([x.email]) : acc),
            new Array<string>(),
          ),
          orgId,
        },
        authLookup,
      );
      return undefined;
    } catch (e) {
      if (e instanceof HTTPError) {
        return `${e}`;
      } else {
        console.error(e);
        return "Internal error";
      }
    }
  })();

  return {
    added_users,
    send_email_error,
  };
}

const _removeMembersInputSchema = z.object({
  orgId: organizationSchema.shape.id,
  users: patchOrganizationMembersSchema.shape.remove_users.unwrap().unwrap(),
});

export type RemoveMembersInput = z.infer<typeof _removeMembersInputSchema>;

export async function removeMembers(
  { orgId, users }: RemoveMembersInput,
  authLookupRaw?: AuthLookup,
  opts?: {
    client?: BtPgClient;
  },
): Promise<null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const queryParams = new SqlQueryParams();
  const userIdsParam = (users.ids ?? [])
    .map((x) => queryParams.add(x))
    .join(", ");
  const userEmailsParam = (users.emails ?? [])
    .map((x) => queryParams.add(x))
    .join(", ");
  const query = `
  with
  user_ids as (
    select id
    from users
    where
        id = any(array [${userIdsParam}]::uuid[])
        or email = any(array [${userEmailsParam}]::text[])
  )
  select remove_member_from_org(
      user_to_remove_id => user_ids.id,
      organization_id => ${queryParams.add(orgId)},
      actor_auth_id => ${queryParams.add(authLookup.auth_id)})
  from user_ids
  `;

  const supabase = opts?.client ?? getServiceRoleSupabase();
  try {
    await supabase.query(query, queryParams.params);
    return null;
  } catch (e) {
    console.error("Failed to remove member from an organization", e);
    throw processUdfError(e);
  }
}

async function sendInviteEmail(
  {
    emails,
    orgId,
  }: {
    emails: string[];
    orgId: string;
  },
  authLookup: AuthLookup,
) {
  if (!resend) {
    return;
  }

  // Make sure the user has permission to invite org members.
  const queryParams = new SqlQueryParams();
  const orgIdParam = queryParams.add(orgId);
  const userIdParam = queryParams.add(authLookup.user_id);
  const query = `
  with
  inviting_user as (
    select id, email
    from users
    where id = ${userIdParam}
  )
  select
      organizations.name org_name,
      inviting_user.email inviting_user_email
  from organizations join inviting_user on true
  where
    organizations.id = ${orgIdParam}
    and has_under_organization_acl(
      'org_member', organizations.id, organizations.id, 'create', inviting_user.id)
  `;
  const supabase = getServiceRoleSupabase();
  const row = extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage: {
      permission: "create org_member",
      objectType: "organization",
      objectIds: [orgId],
    },
  });
  const { org_name, inviting_user_email } = z
    .object({
      org_name: organizationSchema.shape.name,
      inviting_user_email: userSchema.shape.email,
    })
    .parse(row);
  const batchEmails = emails.map((email) => ({
    from: "Braintrust <<EMAIL>>",
    to: email,
    subject: "Join your team on Braintrust!",
    react: React.createElement(
      InviteUser,
      {
        email,
        invitedByEmail: inviting_user_email,
        organizationName: org_name,
      },
      null,
    ),
  }));

  const { data, error } = await resend.batch.send(batchEmails);

  if (error) {
    throw new HTTPError(500, error.message);
  }

  return data;
}

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
