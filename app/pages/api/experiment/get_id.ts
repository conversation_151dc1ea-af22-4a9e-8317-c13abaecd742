// Retained for legacy API servers. New code should use `/get` with an `ids`
// filter.

import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  getObjects,
  extractSingularRow,
} from "../_object_crud_util";
import { additionalProjections } from "./_constants";
import { experimentSchema } from "@braintrust/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) =>
      extractSingularRow({
        rows: await getObjects({
          authLookup,
          permissionInfo: {
            aclObjectType: "experiment",
          },
          filters: {
            id: [params.id],
          },
          finalResultSetAdditionalProjections: additionalProjections,
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema: idParamSchema,
      outputSchema: experimentSchema,
    },
  );
}
