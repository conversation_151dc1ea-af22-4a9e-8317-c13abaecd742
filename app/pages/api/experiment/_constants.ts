import { type NextApiRequest, type NextApiResponse } from "next";
export const additionalProjections = (baseTableName: string) => [
  `has_under_project_acl('experiment', ${baseTableName}.id, ${baseTableName}.project_id, 'read', anon_user_id()) as "public"`,
];

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
