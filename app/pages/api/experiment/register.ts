import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import {
  createExperimentSchema,
  experimentSchema,
  projectSchema,
} from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = createExperimentSchema
  .omit({ name: true, project_id: true, ensure_new: true })
  .extend({
    experiment_name: createExperimentSchema.shape.name,
    project_name: z.string().nullish(),
    project_id: createExperimentSchema.shape.project_id.nullish(),
    base_experiment: z.string().nullish(),
    ancestor_commits: z.string().array().nullish(),
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_experiment", {
    paramsSchema,
    outputSchema: z.object({
      project: projectSchema,
      experiment: experimentSchema,
    }),
    argnames: [
      "auth_id",
      "org_id",
      "project_id",
      "project_name",
      "experiment_name",
      "description",
      "update",
      "repo_info",
      "base_exp_id",
      "base_experiment",
      "ancestor_commits",
      "dataset_id",
      "dataset_version",
      "public",
      "metadata",
      "tags",
    ],
  });
}
