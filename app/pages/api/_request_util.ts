import type { NextApiRequest, NextApiResponse } from "next";
import { isObject } from "#/utils/object";
import { z } from "zod";
import { ExtraFieldsError, parseNoStrip } from "braintrust/util";
import {
  getRequestIdNextApiRequest,
  HTTPError,
  httpErrorToNextApiResponse,
} from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { type AuthLookup } from "./_lookup_api_key";
import { getAuthorizationToken, loginToAuthId } from "./_login_to_auth_id";
import { findOrgId, lookupUserId } from "./_lookup_api_key";
import { ANON_AUTH_ID } from "#/utils/constants";
import { formatErrorMessageTags } from "@braintrust/local";

export function getAuthTokenAndOrgName(req: NextApiRequest): {
  token: string | undefined;
  org_name: string | undefined;
} {
  const org_name = (() => {
    try {
      return z.string().nullish().parse(req.body?.["org_name"]) ?? undefined;
    } catch (e) {
      if (e instanceof z.ZodError) {
        throw new HTTPError(400, "org_name must be a string");
      } else {
        throw e;
      }
    }
  })();
  return {
    token: getAuthorizationToken((x) => req.headers[x]),
    org_name,
  };
}

export async function getSessionAuthInfo(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<AuthLookup> {
  const { token, org_name } = getAuthTokenAndOrgName(req);
  const authLookup = await loginToAuthId({
    token,
    org_name,
  });
  let auth_id: string = authLookup.auth_id;
  let user_id: string = authLookup.user_id;
  let org_id: string | null = authLookup.org_id;

  if (auth_id === ANON_AUTH_ID) {
    const session = await getServerAuthSession({ req, res });
    auth_id = session.authId ?? ANON_AUTH_ID;
    user_id = await lookupUserId(auth_id);
  }

  if (!org_id && org_name) {
    org_id = await findOrgId({
      org_name,
      auth_id,
      conn: getServiceRoleSupabase(),
    });
  }

  return { auth_id, user_id, org_id };
}

export type GetRequestParamsOpts = {
  renameFields?: Record<string, string>;
  removeFields?: string[];
  allowExtraFields?: boolean;
};

export function getRequestParams<T extends z.ZodType>(
  body: unknown,
  schema: T,
  opts?: GetRequestParamsOpts,
): z.infer<T> {
  if (!isObject(body)) {
    throw new HTTPError(400, "Request body must be object");
  }
  const params = { ...body };
  const renameFields = opts?.renameFields ?? {};
  renameFields["ids"] = "id";
  for (const [k, v] of Object.entries(renameFields)) {
    if (k in params) {
      const val = params[k];
      delete params[k];
      params[v] = val;
    }
  }
  if (opts?.removeFields) {
    for (const k of opts.removeFields) {
      if (k in params) {
        delete params[k];
      }
    }
  }

  try {
    try {
      return parseNoStrip(schema, params);
    } catch (e) {
      if (e instanceof ExtraFieldsError && opts?.allowExtraFields) {
        console.warn("Extra fields error", { e });
        return schema.parse(params);
      } else {
        throw e;
      }
    }
  } catch (e) {
    if (e instanceof z.ZodError) {
      throw new HTTPError(400, JSON.stringify(e.errors, null, 2));
    } else {
      throw new HTTPError(400, `${e}`);
    }
  }
}

export async function httpHandleError({
  error,
  authLookup,
  req,
  res,
}: {
  error: unknown;
  authLookup: AuthLookup | undefined;
  req: NextApiRequest;
  res: NextApiResponse;
}) {
  const extraTags = {
    request_id: getRequestIdNextApiRequest(req),
  };
  if (error instanceof HTTPError) {
    await httpErrorToNextApiResponse({
      error,
      authLookup,
      res,
      extraTags,
    });
  } else {
    const tagsStr = formatErrorMessageTags(extraTags);
    console.error(`Internal error${tagsStr}:\n`, error);
    res.status(500).send(`Internal error${tagsStr}`);
  }
}

export async function runJsonRequest<T extends z.ZodType>(
  req: NextApiRequest,
  res: NextApiResponse,
  f: (
    params: z.infer<T>,
    authLookup: AuthLookup,
    extraArgs?: {
      req: NextApiRequest;
      res: NextApiResponse;
    },
  ) => Promise<unknown>,
  opts: {
    paramsSchema: T;
    postprocessOutput?: (x: unknown) => unknown;
    outputSchema: z.ZodType;
  } & Partial<GetRequestParamsOpts>,
) {
  const { paramsSchema, outputSchema, ...getRequestParamsOpts } = opts;
  let authLookup: AuthLookup | undefined = undefined;
  try {
    authLookup = await getSessionAuthInfo(req, res);
    const paramObj = getRequestParams(
      req.body,
      paramsSchema,
      getRequestParamsOpts,
    );
    const rawOutput = await f(paramObj, authLookup, { req, res });
    const postprocessedOutput = opts.postprocessOutput
      ? opts.postprocessOutput(rawOutput)
      : rawOutput;
    res.json(outputSchema.parse(postprocessedOutput));
  } catch (error) {
    await httpHandleError({ error, authLookup, req, res });
  }
  res.end();
}

export function wrapZodError<T>(f: () => T, opts?: { errmsg?: string }): T {
  try {
    return f();
  } catch (e) {
    if (e instanceof z.ZodError) {
      throw new HTTPError(
        400,
        JSON.stringify({
          error: opts?.errmsg ?? "Invalid request",
          errors: e.errors,
        }),
      );
    } else if (e instanceof ExtraFieldsError) {
      throw new HTTPError(400, e.message);
    } else {
      throw e;
    }
  }
}

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
