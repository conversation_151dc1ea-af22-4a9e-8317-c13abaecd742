import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
} from "../_invoke_supabase_udf";
import { createApiKeyOutputSchema } from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = z
  .object({ name: z.string().nullish() })
  .merge(udfOrgIdSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "create_api_key", {
    paramsSchema,
    postprocessOutput: (data: unknown) => ({ key: data }),
    outputSchema: createApiKeyOutputSchema.pick({ key: true }),
    argnames: ["auth_id", "org_id", "name"],
  });
}
