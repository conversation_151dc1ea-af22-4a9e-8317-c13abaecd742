import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import {
  createSpanIframeSchema,
  spanIframeSchema,
} from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = createSpanIframeSchema
  .omit({ name: true })
  .extend({
    span_iframe_name: createSpanIframeSchema.shape.name,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_span_iframe", {
    paramsSchema,
    outputSchema: z.object({
      span_iframe: spanIframeSchema,
      found_existing: z.boolean(),
    }),
    argnames: [
      "auth_id",
      "project_id",
      "span_iframe_name",
      "description",
      "url",
      "post_message",
      "update",
    ],
  });
}
