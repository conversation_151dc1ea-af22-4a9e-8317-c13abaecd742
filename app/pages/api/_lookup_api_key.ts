import { type NextApiRequest, type NextApiResponse } from "next";
import { HTTPError } from "#/utils/server-util";
import { Ratelimit, KV, rateLimitSlidingWindow } from "#/utils/cache";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { sha1 } from "#/utils/hash";
import { type BtPg } from "@braintrust/local/bt-pg";
import { z } from "zod";
import { otelWrapTraced } from "#/utils/tracing";
import { waitUntil } from "@vercel/functions";

export const authLookupSchema = z.object({
  auth_id: z.string(),
  user_id: z.string(),
  org_id: z.string().nullable(),
});

export type AuthLookup = z.infer<typeof authLookupSchema>;

function makeAuthTokenHash({
  key,
  org_name,
}: {
  key: string;
  org_name?: string | null;
}) {
  return `${sha1(key)}_${org_name || ""}`;
}

export function makeCacheKey({
  key,
  org_name,
}: {
  key: string;
  org_name?: string | null;
}) {
  return `auth_lookup_v2_${makeAuthTokenHash({ key, org_name })}`;
}

export async function lookupApiKey({
  key,
  org_name,
}: {
  key: string;
  org_name?: string;
}): Promise<AuthLookup> {
  const authTokenHash = makeAuthTokenHash({ key, org_name });
  const cacheKey = makeCacheKey({ key, org_name });
  const kv = new KV();
  {
    const resp = await kv.get(cacheKey, authLookupSchema);
    if (resp.status === "found") {
      return resp.value;
    }
  }

  await checkRateLimit(authTokenHash);

  const conn = getServiceRoleSupabase();
  key = cleanApiKey(key);
  let result = null;
  try {
    // Note: if we ever need to rip out pgsodium, we could try pushing the `key`
    // hashing logic into the application layer. We can use libsodium.js
    // (https://www.npmjs.com/package/libsodium) to run the `crypto_generichash`
    // function in JS. We would need to do this in the API key creation code as
    // well.
    result = await conn.query(
      `
        SELECT auth_id, api_keys.user_id, api_keys.org_id
        FROM api_keys JOIN users ON api_keys.user_id = users.id
        WHERE key_hash = pgsodium.crypto_generichash(decode(regexp_replace($1::text, '^(sk-)|(bt-st-)', ''), 'base64'))
    `,
      [key],
    );
  } catch (e) {
    throw new HTTPError(
      400,
      `Failed to decode API key (${redactedAuthToken(key)}). Make sure it does not contain extraneous characters: ${e}`,
    );
  }

  const parseResult = z
    .object({
      auth_id: z.string(),
      user_id: z.string(),
      org_id: z.string().nullish(),
    })
    .safeParse(result.rows[0]);
  if (!parseResult.success) {
    throw new HTTPError(401, "Invalid API Key");
  }
  const { auth_id, user_id } = parseResult.data;
  let org_id = parseResult.data.org_id ?? null;
  if (!org_id && org_name) {
    // No org_id associated with this API key, but we have explicitly specified
    // an org name to disambiguate.
    org_id = await findOrgId({ org_name, auth_id, conn });
    if (!org_id) {
      throw new HTTPError(
        403,
        "API key does not belong to the org specified in org_name",
      );
    }
  }

  waitUntil(
    kv.set<AuthLookup>(
      cacheKey,
      { auth_id, user_id, org_id },
      // Cache for 60s
      { ex: 60 },
    ),
  );

  return {
    auth_id,
    user_id,
    org_id,
  };
}

const QUOTES = ['"', "'"];

function cleanApiKey(s: string): string {
  s = s.trim();
  for (const q of QUOTES) {
    if (s[0] === q && s[s.length - 1] === q) {
      return s.slice(1, -1);
    }
  }
  return s;
}

export function isApiKey(s: string): boolean {
  return cleanApiKey(s).startsWith("sk-");
}

export function isServiceToken(s: string): boolean {
  return cleanApiKey(s).startsWith("bt-st-");
}

export function redactedAuthToken(s: string): string {
  s = cleanApiKey(s);
  if (s.length <= 8) {
    return "*".repeat(s.length);
  }
  let prefix = "";
  if (isApiKey(s)) {
    prefix = s.slice(0, 2);
  } else if (isServiceToken(s)) {
    prefix = s.slice(0, 6);
  }

  return prefix + "*".repeat(s.length - prefix.length - 2) + s.slice(-2);
}

async function checkRateLimit(key: string): Promise<void> {
  const ratelimit = new Ratelimit({
    // rate limit to 50 requests per 10 seconds (assuming each request takes ~100ms, that's a good
    // test if you're looping)
    limiter: rateLimitSlidingWindow(50, "10s"),
  });

  const result = await ratelimit.limit(`ratelimit_${key}`);
  if (result.status === "unavailable") {
    return;
  }
  const { success, limit, reset, remaining } = result.response;
  if (!success) {
    const message = `Request rate limit exceeded. Contact <EMAIL> if you believe this was in error or need an increase.`;
    const headers = {
      "X-RateLimit-Limit": limit.toString(),
      "X-RateLimit-Remaining": remaining.toString(),
      "X-RateLimit-Reset": reset.toString(),
    };
    console.error(message, headers);
    throw new HTTPError(429, message, headers);
  }
}

export async function findOrgId({
  org_name,
  auth_id,
  conn,
}: {
  org_name: string;
  auth_id: string;
  conn?: BtPg;
}): Promise<string | null> {
  if (!conn) {
    conn = getServiceRoleSupabase();
  }
  try {
    const result = await conn.query(
      `
          select organizations.id
          from
            organizations
            join members on organizations.id = members.org_id
            join users on members.user_id = users.id
          where organizations.name = $1 and users.auth_id = $2
        `,
      [org_name, auth_id],
    );
    const resultParsed = z
      .object({ id: z.string() })
      .nullish()
      .parse(result.rows[0]);
    return resultParsed?.id ?? null;
  } catch (e) {
    console.error("Failed to look up org", e);
    throw new HTTPError(400, "Failed to look up org specified in org_name");
  }
}

// Although we could make this an LRU cache, we're expecting that the number of auth
// ids is bounded (no more than 100,000) and this app runs in a serverless environment
// so it shouldn't grow too large before being reset.
const authIdCache = new Map<string, string>();

export const lookupUserId = otelWrapTraced(
  "lookupUserId",
  async (auth_id: string): Promise<string> => {
    const authIdHash = `${sha1(auth_id)}`;
    const key = `auth_id_to_user_id_${authIdHash}`;
    const kv = new KV();

    if (authIdCache.has(authIdHash)) {
      return authIdCache.get(authIdHash)!;
    }

    {
      const resp = await kv.get(key, z.string());
      if (resp.status === "found") {
        return resp.value;
      }
    }

    // Leave out the rate limit check because we should only be calling this
    // function after obtaining a valid auth_id. So unauthorized callers should
    // not get to this point.
    // await checkRateLimit(auth_id);

    const conn = getServiceRoleSupabase();
    const { rows } = await conn.query(
      `
        SELECT users.id user_id FROM users WHERE auth_id = $1
    `,
      [auth_id],
    );

    const parseResult = z
      .object({
        user_id: z.string(),
      })
      .safeParse(rows[0]);
    if (!parseResult.success) {
      throw new HTTPError(401, "Invalid auth_id");
    }
    const { user_id } = parseResult.data;

    authIdCache.set(authIdHash, user_id);
    waitUntil(
      kv.set<string>(
        key,
        user_id,
        // Cache for 60s
        { ex: 60 },
      ),
    );

    return user_id;
  },
);

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
