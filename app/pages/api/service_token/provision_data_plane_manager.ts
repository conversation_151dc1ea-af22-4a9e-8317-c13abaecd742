import type { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { extractSingularRow } from "../_object_crud_util";
import { runJsonRequest } from "../_request_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { isAllowedSysadmin } from "#/utils/derive-error-context";
import { HTTPError } from "#/utils/http_error";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { isBraintrustDataPlane } from "#/utils/user-types";
import { addMembers } from "../organization/member_actions";
import { adminFetchOrgContextInfo } from "#/app/admin/actions";
import { getOrganization, isOrganizationOwner } from "#/app/app/actions";
import { batchUpdateHelper } from "#/pages/api/acl/batch_update";

const DATA_PLANE_MANAGER_NAME = "data_plane_manager";
const DATA_PLANE_MANAGER_SERVICE_TOKEN = "bt_data_plane_service_token";

const test_allowProvisionAsSelfHostedDataPlane =
  process.env.TESTING_ONLY_ALLOW_SELF_HOSTED_DATA_PLANE === "true";

const paramsSchema = z.object({
  org_id: z.string(),
  name: z.string().optional(),
  force_recreate_token: z.boolean().nullish(),
  _bt_test_provision_as_self_hosted_data_plane: z.boolean().nullish(),
});

const dataPlaneManagerSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  error: z.string().optional(),
});
const dataPlaneManagerWithAuthIdSchema = dataPlaneManagerSchema.extend({
  auth_id: z.string(),
});
const dataPlaneManagerWithAuthIdAndGroupSchema =
  dataPlaneManagerWithAuthIdSchema.extend({
    has_permissions: z.boolean(),
  });

const dataPlaneServiceTokenSchema = z.object({
  id: z.string(),
  name: z.string(),
  preview_name: z.string(),
  key: z.string().nullish(),
  created: z.boolean(),
});

export type DataPlaneManager = z.infer<typeof dataPlaneManagerSchema>;
export type DataPlaneServiceToken = z.infer<typeof dataPlaneServiceTokenSchema>;

const outputSchema = z.object({
  data_plane_manager: dataPlaneManagerSchema,
  data_plane_service_token: dataPlaneServiceTokenSchema,
});

/*
 * RBAC_DISCLAIMER:
 * On the Braintrust data plane this endpoint is only accessible to sysadmins. It will:
 * - create a service account with sysadmin privileges
 * - create an unscoped service token for the service account
 *
 * On self-hosted data planes this endpoint is only accessible to an org owner. It will:
 * - create a service account in the specified org with an org_project:read ACL grant
 * - create an unscoped service token for the service account
 *
 * Note that for self-hosted data planes with multiple orgs customers will have to invite
 * the provisioned data plane manager to their additional orgs.
 */
export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const isSysadmin = await isAllowedSysadmin(authLookup);
      const isOrgOwner = await isOrganizationOwner(
        { org_id: params.org_id },
        authLookup,
      );
      if (!isSysadmin && !isOrgOwner) {
        throw new HTTPError(401, "Unauthorized");
      }

      let apiUrl: string | null | undefined = undefined;
      if (isSysadmin) {
        const orgContext = await adminFetchOrgContextInfo(
          { org_id: params.org_id },
          authLookup,
        );
        if (!orgContext) {
          throw new HTTPError(400, "Failed to fetch organization");
        }
        apiUrl = orgContext.api_url;
      } else if (isOrgOwner) {
        const org = await getOrganization(
          { org_id: params.org_id },
          authLookup,
        );
        if (!org) {
          throw new HTTPError(400, "Failed to fetch organization");
        }
        apiUrl = org.api_url;
      }

      // NOTE: we allow overriding the data plane type in tests
      const test_spoofSelfHosted =
        test_allowProvisionAsSelfHostedDataPlane &&
        params._bt_test_provision_as_self_hosted_data_plane;

      const selfHosted =
        (apiUrl && !isBraintrustDataPlane(apiUrl)) || test_spoofSelfHosted;
      if ((!selfHosted && !isSysadmin) || (selfHosted && !isOrgOwner)) {
        throw new HTTPError(401, "Unauthorized");
      }

      const supabase = getServiceRoleSupabase();
      let dataPlaneManager: DataPlaneManager | null = null;
      let dataPlaneManagerAuthId: string | null = null;
      let dataPlaneServiceToken: DataPlaneServiceToken | null = null;
      try {
        if (!selfHosted) {
          const accountQuery = `
            select id, auth_id, given_name as name, email from users
            where user_type = 'service_account' and id = bt_service_account_user_id() and auth_id = bt_service_account_auth_id() and email = bt_service_account_email()
          `;

          const row = extractSingularRow({
            rows: (await supabase.query(accountQuery)).rows,
            notFoundErrorMessage: "Failed to find data plane manager",
          });
          const parsedRow = dataPlaneManagerWithAuthIdSchema.safeParse(row);
          if (!parsedRow.success) {
            console.error(
              "Failed to parse data plane manager response",
              parsedRow.error,
            );
            throw new HTTPError(
              400,
              "Failed to parse data plane manager response",
            );
          }
          dataPlaneManager = {
            id: parsedRow.data.id,
            email: parsedRow.data.email,
            name: parsedRow.data.name,
          };
          dataPlaneManagerAuthId = parsedRow.data.auth_id;
        } else {
          let grantOrgProjectReadAcl = false;

          const queryParams = new SqlQueryParams();
          const orgIdParam = queryParams.add(params.org_id);
          const { rows } = await supabase.query(
            `with
            org_members as (
              select user_id from members
              where org_id = ${orgIdParam}
            )
            select users.id, users.auth_id, users.given_name as name, users.email as email, has_under_organization_acl('org_project', ${orgIdParam}, ${orgIdParam}, 'read', users.id) as has_permissions
            from users
            join org_members on users.id = org_members.user_id
            where users.user_type = 'service_account' and users.email like 'bt::sp::custom::${DATA_PLANE_MANAGER_NAME}-%'
          `,
            queryParams.params,
          );
          if (rows.length === 1) {
            const parsedRow =
              dataPlaneManagerWithAuthIdAndGroupSchema.safeParse(rows[0]);
            if (!parsedRow.success) {
              console.error(
                "Failed to parse data plane manager response",
                parsedRow.error,
              );
              throw new HTTPError(
                400,
                "Failed to parse data plane manager response",
              );
            }
            dataPlaneManager = {
              id: parsedRow.data.id,
              email: parsedRow.data.email,
              name: parsedRow.data.name,
            };
            dataPlaneManagerAuthId = parsedRow.data.auth_id;

            if (!parsedRow.data.has_permissions) {
              grantOrgProjectReadAcl = true;
            }
          } else if (rows.length > 1) {
            throw new HTTPError(
              400,
              "Multiple data plane managers found for org",
            );
          } else {
            const output = await addMembers(
              {
                orgId: params.org_id,
                users: {
                  service_accounts: [
                    {
                      name: DATA_PLANE_MANAGER_NAME,
                      token_name: null, // disable service token creation here so we can provision an unscoped token below
                    },
                  ],
                  group_names: [],
                },
              },
              authLookup,
              {
                client: supabase,
              },
            );
            if (output.added_users.length !== 1) {
              throw new HTTPError(
                400,
                `Failed to provision data plane manager for org ${params.org_id}`,
              );
            }
            const row = extractSingularRow({
              rows: (
                await supabase.query(
                  `select id, auth_id, given_name as name, email from users where id = $1`,
                  [output.added_users[0].id],
                )
              ).rows,
              notFoundErrorMessage:
                "Data plane manager deleted after creation, please retry provisioning.",
            });

            const parsedRow = dataPlaneManagerWithAuthIdSchema.safeParse(row);
            if (!parsedRow.success) {
              console.error(
                "Failed to parse data plane manager response",
                parsedRow.error,
              );
              throw new HTTPError(
                400,
                "Failed to parse data plane manager response",
              );
            }
            dataPlaneManager = {
              id: parsedRow.data.id,
              email: parsedRow.data.email,
              name: parsedRow.data.name,
            };
            dataPlaneManagerAuthId = parsedRow.data.auth_id;

            grantOrgProjectReadAcl = true;
          }

          if (grantOrgProjectReadAcl) {
            const resp = await batchUpdateHelper(
              {
                add_acls: [
                  {
                    object_type: "org_project",
                    object_id: params.org_id,
                    user_id: dataPlaneManager.id,
                    permission: "read",
                  },
                ],
              },
              authLookup,
            );
            if (resp?.added_acls.length !== 1) {
              console.error(
                "Failed to grant org_project:read ACL on data plane manager",
              );
              throw new HTTPError(
                400,
                "Failed to grant org_project:read ACL on data plane manager",
              );
            }
          }
        }

        const forceRecreateToken = params.force_recreate_token || false;
        const queryParams = new SqlQueryParams();
        const tokenName = params.name || DATA_PLANE_MANAGER_SERVICE_TOKEN;
        const tokenNameParam = queryParams.add(tokenName);
        const dataPlaneManagerIdParam = queryParams.add(dataPlaneManager.id);
        const dataPlaneManagerAuthIdParam = queryParams.add(
          dataPlaneManagerAuthId,
        );
        const query = `
        with
        deleted_data_plane_service_token as (
          delete from api_keys
          where user_id = ${dataPlaneManagerIdParam} and name = ${tokenNameParam} and org_id is null
          and ${forceRecreateToken ? "true" : "false"}
          returning id
        ),
        data_plane_service_token as (
          select id, name, preview_name, null as key, false as created from api_keys
          where user_id = ${dataPlaneManagerIdParam} and name = ${tokenNameParam} and org_id is null
        ),
        created_data_plane_service_token as (
          select (api_key->>'id')::uuid as id, api_key->>'name' as name, api_key->>'preview_name' as preview_name, api_key->>'key' as key, true as created from (
            select create_api_key_unchecked(${dataPlaneManagerAuthIdParam}, null, ${tokenNameParam}, true)->'api_key' as api_key
            where not exists (select 1 from data_plane_service_token) or ${forceRecreateToken ? "true" : "false"}
          ) st
        )
        select
          coalesce(created_data_plane_service_token.id, data_plane_service_token.id) as id,
          coalesce(created_data_plane_service_token.name, data_plane_service_token.name) as name,
          coalesce(created_data_plane_service_token.preview_name, data_plane_service_token.preview_name) as preview_name,
          coalesce(created_data_plane_service_token.key, data_plane_service_token.key) as key,
          coalesce(created_data_plane_service_token.created, data_plane_service_token.created) as created
        from
          created_data_plane_service_token full outer join data_plane_service_token on true
        `;

        const row = extractSingularRow({
          rows: (await supabase.query(query, queryParams.params)).rows,
          notFoundErrorMessage: "Failed to provision data plane service token",
        });
        dataPlaneServiceToken = dataPlaneServiceTokenSchema.parse(row);

        return {
          data_plane_manager: dataPlaneManager,
          data_plane_service_token: dataPlaneServiceToken,
        };
      } catch (e) {
        console.error(
          "Failed to provision data plane manager",
          e,
          dataPlaneManager,
          dataPlaneServiceToken,
        );
        throw e;
      }
    },
    {
      paramsSchema,
      outputSchema,
    },
  );
}
