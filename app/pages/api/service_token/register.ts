import type { NextApiRequest, NextApiResponse } from "next";
import {
  createServiceTokenSchema,
  createServiceTokenOutputSchema,
} from "@braintrust/typespecs";
import { z } from "zod";
import { extractSingularRow } from "../_object_crud_util";
import { runJsonRequest } from "../_request_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { transformServiceToken } from "./_util";
import { makeIsOrgOwnerCTE } from "../_special_queries";
import { HTTPError } from "#/utils/server-util";

const paramsSchema = createServiceTokenSchema
  .omit({
    name: true,
  })
  .merge(
    z.object({
      service_token_name: z.string().min(1, "Name is required"),
      // newer data planes will not send this param but we need to drop it to support older ones
      update: z.unknown().transform((_x) => null),
    }),
  );

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      if (!authLookup.org_id) {
        throw new HTTPError(
          400,
          "You must provide an organization-scoped API key or an explicit org_name",
        );
      }

      const { query: isOrgOwnerCTE, queryParams } = makeIsOrgOwnerCTE({
        userId: authLookup.user_id,
        orgId: authLookup.org_id,
      });
      const userIdParam = queryParams.add(params.service_account_id);
      const orgIdParam = queryParams.add(authLookup.org_id);
      const nameParam = queryParams.add(params.service_token_name);
      const query = `
        ${isOrgOwnerCTE},
        user_auth_id as (
          select auth_id from users where id = ${userIdParam}::uuid
          and user_type = 'service_account'::user_type_enum
        )
        select create_api_key_full(
          user_auth_id.auth_id,
          ${orgIdParam},
          ${nameParam},
          true
        )->'api_key' as api_key
        from user_auth_id
        join is_org_owner on is_org_owner.exists
      `;

      const supabase = getServiceRoleSupabase();
      const row = extractSingularRow({
        rows: (await supabase.query(query, queryParams.params)).rows,
        notFoundErrorMessage: {
          permission: "create service_token",
          objectType: "service_account",
          objectIds: [params.service_account_id],
        },
      });

      return { service_token: transformServiceToken(row.api_key) };
    },
    {
      paramsSchema,
      outputSchema: z.object({ service_token: createServiceTokenOutputSchema }),
    },
  );
}
