import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import {
  commonFilterParamsSchema,
  genQueryFilters,
  nullishSingleOrArraySchema,
  paginationParamsSchema,
  makeFullResultSetQuery,
  getObjects,
} from "../_object_crud_util";
import { cleanupAclJson } from "./_util";
import { z } from "zod";
import { aclSchema } from "@braintrust/typespecs";
import { HTTPError } from "#/utils/http_error";

const paramsSchema = commonFilterParamsSchema
  .pick({ id: true })
  .merge(paginationParamsSchema)
  .merge(
    nullishSingleOrArraySchema(
      z.object({
        object_type: aclSchema.shape.object_type,
        object_id: aclSchema.shape.object_id,
        user_id: aclSchema.shape.user_id.unwrap().unwrap(),
        group_id: aclSchema.shape.group_id.unwrap().unwrap(),
        permission: aclSchema.shape.permission.unwrap().unwrap(),
        restrict_object_type: aclSchema.shape.restrict_object_type
          .unwrap()
          .unwrap(),
        role_id: aclSchema.shape.role_id.unwrap().unwrap(),
      }),
    ),
  );

export async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const authLookupOrgId = authLookup.org_id;
  if (!authLookupOrgId) {
    throw new HTTPError(
      400,
      "Must query org-wide ACLs with an org-scoped API key or an explicitly-specified org_name",
    );
  }

  const {
    object_type,
    object_id,
    user_id,
    group_id,
    permission,
    restrict_object_type,
    role_id,
    id,
    ...paginationParams
  } = params;

  const { query: fullResultsSubquery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "read_acls",
      overrideRestrictObjectType: "null",
    },
    filters: {
      id: [authLookupOrgId],
    },
  });

  const fullResultsQueryOverride = `
    select * from acls
    where
        _object_org_id = ${queryParams.add(authLookupOrgId)}
        and exists(select 1 from (${fullResultsSubquery}) "t")
        and ${genQueryFilters({ tableName: "acls" }, queryParams, {
          id,
          object_type,
          object_id,
          user_id,
          group_id,
          permission,
          restrict_object_type,
          role_id,
        })}
  `;

  const rows = await getObjects({
    fullResultsQueryOverride,
    startingParams: queryParams,
    paginationParams,
    fullResultsSize: undefined,
  });
  return rows.map(cleanupAclJson);
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: aclSchema.array(),
  });
}
