import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { extractSingularRow } from "../_object_crud_util";
import { batchUpdateHelper } from "./batch_update";
import { aclSchema, aclItemSchema } from "@braintrust/typespecs";
import { z } from "zod";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const res = await batchUpdateHelper({ add_acls: [params] }, authLookup);
      const row = extractSingularRow({
        rows: res ? res.added_acls : [],
        notFoundErrorMessage: undefined,
      });
      return { acl: row };
    },
    {
      paramsSchema: aclItemSchema,
      outputSchema: z.object({ acl: aclSchema }),
      removeFields: ["update"],
    },
  );
}
