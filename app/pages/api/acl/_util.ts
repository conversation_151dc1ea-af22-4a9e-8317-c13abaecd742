import { type NextApiRequest, type NextApiResponse } from "next";
import { z } from "zod";

import { type Acl, aclSchema } from "@braintrust/typespecs";
import { filterObject } from "#/utils/object";

export const aclObjectParamsSchema = z.object({
  object_type: aclSchema.shape.object_type,
  object_id: aclSchema.shape.object_id,
});

export function cleanupAclJson(item: Record<string, unknown>): Acl {
  return aclSchema.parse(
    filterObject(item, (k, _) => {
      return !(
        [
          "user_object_type",
          "grant_object_type",
          "_foreign_key_user_id",
        ].includes(k) ||
        (k === "user_id" && item["user_object_type"] === "group") ||
        (k === "group_id" && item["user_object_type"] === "user") ||
        (k === "permission" && item["grant_object_type"] === "role") ||
        (k === "role_id" && item["grant_object_type"] === "permission")
      );
    }),
  );
}

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
