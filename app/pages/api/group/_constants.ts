import { type NextApiRequest, type NextApiResponse } from "next";
export const additionalProjections = (baseTableName: string) => [
  `(select coalesce(array_agg(user_id)::uuid[], '{}') member_users from group_users where group_users.group_id = ${baseTableName}.id)`,
  `(select coalesce(array_agg(member_group_id)::uuid[], '{}') member_groups from group_members join groups "_joined_groups" on group_members.member_group_id = "_joined_groups".id where "_joined_groups".deleted_at isnull and group_members.group_id = ${baseTableName}.id)`,
];

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
