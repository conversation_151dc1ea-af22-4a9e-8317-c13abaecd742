import type { NextApiRequest, NextApiResponse } from "next";
import { resend } from "#/lib/resend";
import Welcome from "#/ui/email-templates/welcome";
import React from "react";

type Data = {
  success?: boolean;
  error?: string;
};

const RESEND_MARKETING_AUDIENCE_ID = "df79b5f9-d84b-47e6-94ae-1a26ff7f748b";

// TODO(manu): move this to use runJsonRequest.
export default async function subscribe(
  req: NextApiRequest,
  res: NextApiResponse<Data>,
) {
  const { email } = JSON.parse(req.body);
  if (!email) {
    res.status(401).json({ error: "Email is required" });
    return;
  }

  if (!resend) {
    res.status(500).json({ error: "Resend not configured" });
    return;
  }

  try {
    // Add contact to Resend audience
    const { data: _contactData, error: contactError } =
      await resend.contacts.create({
        email,
        audienceId: RESEND_MARKETING_AUDIENCE_ID,
      });

    if (contactError) {
      // If contact already exists, that's okay - we'll still consider it successful
      if (contactError.message?.includes("already exists")) {
        res.status(200).json({ success: true });
        return;
      }
      console.error(contactError.message);
    }

    const { error: emailError } = await resend.emails.send({
      from: "Braintrust <<EMAIL>>",
      to: email,
      subject: "Get started with Braintrust",
      react: React.createElement(Welcome, {}, null),
    });

    if (emailError) {
      console.error("Failed to send welcome email:", emailError);
      // Don't fail the subscription if welcome email fails
    }

    res.status(200).json({ success: true });
  } catch (e) {
    console.error("Subscription error:", e);
    res.status(500).json({
      error: "Something went wrong, please try again later.",
    });
  }
}
