import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  deleteObjects,
  extractSingularRow,
} from "../_object_crud_util";
import { projectTagSchema } from "@braintrust/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) =>
      extractSingularRow({
        rows: await deleteObjects({
          authLookup,
          priorObjectTables: ["project_tag"],
          permissionInfo: {
            aclObjectType: "project",
            aclPermission: "update",
          },
          filters: {
            id: [params.id],
          },
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema: idParamSchema,
      outputSchema: projectTagSchema,
    },
  );
}
