import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import {
  createProjectTagSchema,
  projectTagSchema,
} from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = createProjectTagSchema
  .omit({ name: true })
  .extend({
    project_tag_name: createProjectTagSchema.shape.name,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_project_tag", {
    paramsSchema,
    outputSchema: z.object({
      project_tag: projectTagSchema,
      found_existing: z.boolean(),
    }),
    argnames: [
      "auth_id",
      "project_id",
      "project_tag_name",
      "description",
      "color",
      "update",
    ],
  });
}
