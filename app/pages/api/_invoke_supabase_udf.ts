import type { NextApiRequest, NextApiResponse } from "next";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { doubleQuote } from "#/utils/sql-utils";
import { isArray } from "#/utils/object";
import {
  type GetRequestParamsOpts,
  getRequestParams,
  getSessionAuthInfo,
  httpHandleError,
} from "./_request_util";
import { z } from "zod";
import {
  formatNotFoundErrorMessage,
  formatNotFoundErrorMessageInputSchema,
  WAS_UDF_CACHED_HEADER,
  TESTING_ONLY_KV_INJECT_TIMEOUT_SLEEP_HEADER,
} from "@braintrust/local";
import { type AuthLookup } from "./_lookup_api_key";
import { getCurrentSpan, otelWrapTraced } from "#/utils/tracing";
import { DebugError } from "#/utils/error";
import { waitUntil } from "@vercel/functions";
import { KV } from "#/utils/cache";

// Commonly, API endpoints that invoke a UDF accept both an org_name and an
// org_id and coalesce them into an org_id for the UDF.
export const udfOrgIdSchema = z.object({
  org_id: z.string().nullish(),
  org_name: z.string().nullish(),
});

// Object registration UDF endpoints often accept an update parameter.
export const registerUdfUpdateSchema = z.object({
  update: z.boolean().nullish(),
});

const sqlUdfNotFoundErrorMessageSchema = z.union([
  formatNotFoundErrorMessageInputSchema.extend({
    kind: z.literal("not-found"),
  }),
  z.object({
    kind: z.literal("http-error"),
    code: z.number(),
    message: z.string(),
  }),
]);

export function processUdfError(error: unknown): unknown {
  const customErrorMessage = (() => {
    if (
      !(
        error instanceof Object &&
        "message" in error &&
        typeof error.message === "string"
      )
    ) {
      return null;
    }
    try {
      return sqlUdfNotFoundErrorMessageSchema.parse(JSON.parse(error.message));
    } catch (e) {
      return null;
    }
  })();
  if (customErrorMessage) {
    if (customErrorMessage.kind === "not-found") {
      throw new HTTPError(400, formatNotFoundErrorMessage(customErrorMessage));
    } else if (customErrorMessage.kind === "http-error") {
      throw new HTTPError(customErrorMessage.code, customErrorMessage.message);
    } else {
      const x: never = customErrorMessage;
      console.error("Unknown custom error message", x);
    }
  }
  throw error;
}

// Executes the given SQL user-defined function using the service role postgres
// client. Requires the call to be authenticated, and provides parameters
// 'auth_id' and 'org_id' if specified in argnames and available.
export const invokeServiceRoleSupabaseUdf = otelWrapTraced(
  "invokeServiceRoleSupabaseUdf",
  async function invokeServiceRoleSupabaseUdf<
    T extends z.ZodType,
    O extends z.ZodType,
  >(
    req: NextApiRequest,
    res: NextApiResponse,
    udf_name: string,
    opts: {
      argnames: string[];
      paramsSchema: T;
      postprocessOutput?: (x: unknown) => unknown;
      outputSchema: O;
      processError?: ({
        error,
        authLookup,
        req,
        res,
      }: {
        error: unknown;
        authLookup: AuthLookup | undefined;
        req: NextApiRequest;
        res: NextApiResponse;
      }) => Promise<void>;
      makeCacheKey?: (params: z.infer<T>) => string;
      getCacheKeyObjectId?: (rawRow: unknown) => string | null;
    } & GetRequestParamsOpts,
  ): Promise<void | {
    authLookup: AuthLookup;
    out: z.infer<O>;
  }> {
    const {
      argnames,
      paramsSchema,
      outputSchema,
      postprocessOutput,
      processError = httpHandleError,
      makeCacheKey,
      getCacheKeyObjectId,
      ...getRequestParamOpts
    } = opts;

    const span = getCurrentSpan();
    const kv = new KV({
      injectTimeoutSleep: Boolean(
        process.env.ALLOW_TESTING_ONLY_CACHE_OPS &&
          req.headers[TESTING_ONLY_KV_INJECT_TIMEOUT_SLEEP_HEADER],
      ),
    });

    let authLookupRaw: AuthLookup | undefined = undefined;
    try {
      const authLookup = await getSessionAuthInfo(req, res);
      authLookupRaw = authLookup;

      const isSingle = !isArray(req.body);
      const bodyItemsRaw: unknown[] = isSingle ? [req.body] : req.body;

      if (req.headers["x-bt-debug-udf-input-error"]) {
        throw new DebugError("udf-input-error");
      }

      const bodyItems = bodyItemsRaw.map((item) =>
        getRequestParams(item, paramsSchema, getRequestParamOpts),
      );

      let rowsRaw: unknown[] | null = null;

      if (makeCacheKey && bodyItems.length === 1) {
        rowsRaw = await checkCached({
          args: bodyItems[0],
          orgId: authLookup.org_id,
          authId: authLookup.auth_id,
          makeCacheKey,
          kv,
        });
        if (rowsRaw !== null) {
          res.setHeader(WAS_UDF_CACHED_HEADER, "true");
        }
      }

      if (rowsRaw === null) {
        const udfArgSets = bodyItems.map((bodyItem) => {
          const udfArgs = { ...bodyItem };
          udfArgs["auth_id"] = udfArgs["auth_id"] ?? authLookup.auth_id;
          udfArgs["org_id"] = udfArgs["org_id"] ?? authLookup.org_id;
          return udfArgs;
        });
        span?.setAttribute("udf_name", udf_name);
        span?.setAttribute("user_id", authLookup.user_id);
        if (udfArgSets.length > 1) {
          span?.setAttribute("udf_arg_sets_json", JSON.stringify(udfArgSets));
        } else if (udfArgSets.length === 1) {
          span?.setAttributes(udfArgSets[0]);
        } else {
          throw new DebugError("udf-arg-sets-empty");
        }

        const supabase = getServiceRoleSupabase();

        const argVals: unknown[] = [];
        const udfCalls: string[] = [];
        for (const udfArgs of udfArgSets) {
          const singleArgVals = argnames.map((argname) => udfArgs[argname]);
          const argStr = Array.from({ length: singleArgVals.length })
            .map(
              (_, idx) =>
                `${doubleQuote((argnames ?? [])[idx])} => $${
                  argVals.length + idx + 1
                }`,
            )
            .join(",");
          argVals.push(...singleArgVals);
          udfCalls.push(`SELECT ${udf_name}(${argStr}) result`);
        }

        if (req.headers["x-bt-debug-udf-runtime-error"]) {
          throw new DebugError("udf-runtime-error");
        }

        rowsRaw = (
          await (async () => {
            try {
              const { rows } = await supabase.query(
                udfCalls.join(" UNION ALL "),
                argVals,
              );
              return rows;
            } catch (error) {
              throw processUdfError(error);
            }
          })()
        ).map((row) => row.result);
        if (
          makeCacheKey &&
          getCacheKeyObjectId &&
          bodyItems.length === 1 &&
          rowsRaw.length === 1
        ) {
          const objectId = getCacheKeyObjectId(rowsRaw[0]);
          if (objectId) {
            setCached({
              args: bodyItems[0],
              objectId,
              orgId: authLookup.org_id,
              authId: authLookup.auth_id,
              makeCacheKey,
              value: rowsRaw,
              kv,
            });
          }
        }
      }
      const rows = rowsRaw.map((rowRaw) => {
        if (req.headers["x-bt-debug-udf-output-error"]) {
          throw new DebugError("udf-output-error");
        }

        const row = (postprocessOutput ?? ((x: unknown) => x))(rowRaw);
        return outputSchema.parse(row);
      });
      const out = isSingle ? rows[0] : rows;
      res.json(out);
      return {
        authLookup,
        out,
      };
    } catch (e) {
      await processError({
        error: e,
        authLookup: authLookupRaw,
        req,
        res,
      });
    }
    res.end();
  },
);

function makeFullCacheKey<T>({
  args,
  orgId,
  authId,
  makeCacheKey,
}: {
  args: T;
  orgId: string | null;
  authId: string;
  makeCacheKey: (args: T) => string;
}) {
  const cacheKey = makeCacheKey(args);
  return `bt:udf:${orgId ?? "<noorg>"}:${authId}:${cacheKey}`;
}

async function checkCached<T>({
  args,
  orgId,
  authId,
  makeCacheKey,
  kv,
}: {
  args: T;
  orgId: string | null;
  authId: string;
  makeCacheKey: (args: T) => string;
  kv: KV;
}): Promise<unknown[] | null> {
  const cacheKey = makeFullCacheKey({ args, orgId, authId, makeCacheKey });
  const resp = await kv.get(cacheKey, z.array(z.unknown()));
  if (resp.status === "found") {
    return resp.value;
  } else {
    return null;
  }
}

function setCached<T>({
  args,
  objectId,
  orgId,
  authId,
  makeCacheKey,
  value,
  kv,
}: {
  args: T;
  objectId: string;
  orgId: string | null;
  authId: string;
  makeCacheKey: (args: T) => string;
  value: unknown[];
  kv: KV;
}) {
  waitUntil(
    Promise.all([
      kv.set(makeFullCacheKey({ args, orgId, authId, makeCacheKey }), value, {
        ex: 3600, // 1 hour
      }),
      kv.sadd(
        `keys-${objectId}`,
        makeFullCacheKey({ args, orgId, authId, makeCacheKey }),
      ),
      kv.expire(`keys-${objectId}`, 3600),
    ]),
  );
}

export async function flushCache({ objectId }: { objectId: string }) {
  const kv = new KV();
  const resp = await kv.smembers(`keys-${objectId}`);
  if (resp.status !== "ok") {
    return;
  }
  if (resp.members.length > 0) {
    await kv.del(...resp.members);
  }
}

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
