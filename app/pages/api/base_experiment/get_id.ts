import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import {
  type IdParam,
  idParamSchema,
  makeFullResultSetQuery,
  fromTablesClause,
  aclCheckConditions,
  isActiveChecks,
  extractSingularRow,
} from "../_object_crud_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  type TableInfoObjectType,
  aclSpecs,
} from "@braintrust/local/app-schema";
import { experimentSchema } from "@braintrust/typespecs";
import { z } from "zod";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

const { parentAclObjectTypes } = aclSpecs.experiment;
const tableInfoObjectTypes: TableInfoObjectType[] = [
  "experiment",
  ...parentAclObjectTypes,
];

const outputSchema = experimentSchema
  .pick({
    id: true,
    project_id: true,
    name: true,
    base_exp_id: true,
  })
  .merge(
    z.object({
      base_exp_name: experimentSchema.shape.name,
    }),
  );

async function helper(params: IdParam, authLookup: AuthLookup) {
  const {
    query: fullResultsSubquery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "experiment",
      aclPermission: "read",
    },
    filters: {
      id: [params.id],
    },
  });

  const query = `
    with
    branch_experiments as (${fullResultsSubquery}),
    branch_experiment_base_exp as (
    select experiments.id
    from
        ${fromTablesClause(tableInfoObjectTypes)}
        join branch_experiments on true
        join projects branch_experiments_projects on branch_experiments.project_id = branch_experiments_projects.id
    where
        ${isActiveChecks(tableInfoObjectTypes)}
        and exists (
            select 1 from _expanded_acls where
            ${aclCheckConditions({
              queryParams,
              user_id: authLookup.user_id,
              allowAnonAccess: true,
              permission: "read",
              baseObjectType: "experiment",
              parentObjectTypes: parentAclObjectTypes,
            })}
        )
        and experiments.id = coalesce((branch_experiments_projects.settings->>'baseline_experiment_id')::uuid, branch_experiments.base_exp_id)
    ),
    branch_experiments_augmented as (
    select
        branch_experiments.id,
        branch_experiments.project_id,
        branch_experiments.name,
        coalesce(
            branch_experiment_base_exp.id,
            (
                select experiments.id
                from ${fromTablesClause(tableInfoObjectTypes)}
                where
                    ${isActiveChecks(tableInfoObjectTypes)}
                    and exists (
                        select 1 from _expanded_acls where
                        ${aclCheckConditions({
                          queryParams,
                          user_id: authLookup.user_id,
                          allowAnonAccess: false,
                          permission: "read",
                          baseObjectType: "experiment",
                          parentObjectTypes: parentAclObjectTypes,
                        })}
                    )
                    and experiments.project_id = branch_experiments.project_id
                    and experiments.id <> branch_experiments.id
                    and experiments.created <= branch_experiments.created
                order by experiments.created desc
                limit 1
            )) base_exp_id
      from
          branch_experiments
          left join branch_experiment_base_exp on true
    )
    select
        branch_experiments_augmented.*,
        base_experiments.name as base_exp_name
    from
        branch_experiments_augmented
        join experiments base_experiments on branch_experiments_augmented.base_exp_id = base_experiments.id
  `;
  //console.log("Running query\n", substituteParamsDebug(query, queryParams.params));
  const supabase = getServiceRoleSupabase();
  return extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage: notFoundErrorMessage,
  });
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema: idParamSchema,
    outputSchema,
  });
}
