import { runJsonRequest } from "../_request_util";

import type { NextApiRequest, NextApiResponse } from "next";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { signAsync } from "@noble/ed25519";
import { z } from "zod";
import { HTTPError } from "#/utils/http_error";
import { KV } from "#/utils/cache";
import { sha1 } from "#/utils/hash";
import { waitUntil } from "@vercel/functions";

// RBAC_DISCLAIMER: checking Brainstore license is public.
async function run({
  nonce,
  license,
  version,
}: {
  nonce: string;
  license: string;
  version: number;
}) {
  if (!process.env.BRAINSTORE_AUTHORIZE_PRIVATE_KEY) {
    console.error("BRAINSTORE_AUTHORIZE_PRIVATE_KEY is not set.");
    throw new HTTPError(500, "internal error");
  }

  if (version !== 1) {
    throw new HTTPError(400, "unsupported version");
  }

  const cacheKey = `brainstore_license_key_lookup_2_${sha1(license)}`;
  const kv = new KV();
  let orgId: string | undefined = undefined;
  let orgName: string | undefined = undefined;
  {
    const resp = await kv.get(cacheKey, licenseKeyLookupSchema);
    if (resp.status === "found") {
      orgId = resp.value.org_id;
      orgName = resp.value.org_name;
    }
  }

  if (!orgId) {
    const supabase = getServiceRoleSupabase();
    const result = await supabase.query(
      "select org_id, organizations.name AS org_name from brainstore_licenses join organizations on brainstore_licenses.org_id = organizations.id where license = $1",
      [license.replace(/^brainstore-/, "")],
    );

    if (result.rows.length > 0) {
      orgId = z.string().parse(result.rows[0].org_id);
      orgName = z.string().parse(result.rows[0].org_name);

      waitUntil(
        kv.set<LicenseKeyLookup>(
          cacheKey,
          { org_id: orgId, org_name: orgName },
          // Cache for 60s
          { ex: 60 },
        ),
      );
    }
  }

  if (!orgId) {
    throw new HTTPError(401, "unauthorized");
  }

  const payload = { nonce, authorized: !!orgId };

  // The logic to construct the signed payload should match the logic in Brainstore at
  // //brainstore/cli/src/main.rs.
  const p = Buffer.from(JSON.stringify(payload, Object.keys(payload).sort()));
  const k = Buffer.from(process.env.BRAINSTORE_AUTHORIZE_PRIVATE_KEY, "base64");
  const signature = Buffer.from(await signAsync(p, k)).toString("base64");

  return {
    ...payload,
    signature,
    org_id: orgId ?? undefined,
    org_name: orgName ?? undefined,
  };
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, run, {
    paramsSchema: z.object({
      version: z.number().int().min(1),
      nonce: z.string(),
      license: z.string(),
    }),
    outputSchema: z.object({
      nonce: z.string(),
      authorized: z.boolean(),
      signature: z.string(),
      org_id: z.string().optional(),
      org_name: z.string().optional(),
    }),
  });
}

export const licenseKeyLookupSchema = z.object({
  org_id: z.string(),
  org_name: z.string(),
});

export type LicenseKeyLookup = z.infer<typeof licenseKeyLookupSchema>;
