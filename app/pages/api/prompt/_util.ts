import { type NextApiRequest, type NextApiResponse } from "next";
import {
  promptSchema,
  promptBaseSchema,
  patchPromptSchema,
} from "@braintrust/typespecs";

export const promptSchemaApp = promptSchema
  .pick({
    id: true,
    project_id: true,
    slug: true,
    created: true,
  })
  .extend({
    deleted_at: promptBaseSchema.shape.deleted_at,
    user_id: promptBaseSchema.shape.user_id,
  });

export const patchPromptSchemaApp = patchPromptSchema
  .pick({
    name: true,
    slug: true,
    description: true,
    prompt_data: true,
    tags: true,
  })
  .extend({
    deleted_at: promptBaseSchema.shape.deleted_at,
    user_id: promptBaseSchema.shape.user_id,
  });

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
