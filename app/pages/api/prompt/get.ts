import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  getObjects,
  nullishSingleOrArraySchema,
  paginationParamsSchema,
  splitPaginationParams,
} from "../_object_crud_util";
import { promptSchemaApp } from "./_util";
import { z } from "zod";

const paramsSchema = paginationParamsSchema
  .merge(
    commonFilterParamsSchema.pick({
      project_name: true,
      project_id: true,
      org_name: true,
      org_id: true,
      id: true,
    }),
  )
  .merge(
    nullishSingleOrArraySchema(
      z.object({
        slug: z.string(),
      }),
    ),
  );

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        permissionInfo: {
          aclObjectType: "prompt",
        },
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: promptSchemaApp.array(),
      removeFields: ["version"],
    },
  );
}
