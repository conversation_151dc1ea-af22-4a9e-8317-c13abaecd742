import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import {
  aclCheckAdditionalRowsQuery,
  additionalProjections,
} from "./_constants";
import { roleSchema } from "@braintrust/typespecs";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    org_name: true,
    org_id: true,
    id: true,
  }),
);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        permissionInfo: {
          aclObjectType: "role",
          aclCheckAdditionalRowsQuery,
        },
        ...splitPaginationParams(params),
        finalResultSetAdditionalProjections: additionalProjections,
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: roleSchema.array(),
      renameFields: { role_name: "name" },
    },
  );
}
