import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import { sanitizeAISecret } from "./_util";
import { createAISecretSchema, aiSecretSchema } from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = createAISecretSchema
  .omit({ name: true })
  .extend({
    org_secret_name: createAISecretSchema.shape.name,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_org_secret", {
    paramsSchema,
    renameFields: {
      ai_secret_name: "org_secret_name",
    },
    postprocessOutput: (x) => {
      const { org_secret, ...rest } = z
        .object({
          org_secret: z.unknown(),
        })
        .passthrough()
        .parse(x);
      return {
        ai_secret: sanitizeAISecret(org_secret),
        ...rest,
      };
    },
    outputSchema: z.object({
      ai_secret: aiSecretSchema,
      found_existing: z.boolean(),
    }),
    argnames: [
      "auth_id",
      "org_id",
      "org_secret_name",
      "type",
      "metadata",
      "secret",
      "update",
    ],
  });
}
