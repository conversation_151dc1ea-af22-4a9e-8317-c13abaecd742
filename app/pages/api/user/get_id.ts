// Retained for legacy API servers. New code should use `/get` with an
// `ids` filter.

import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { idParamSchema, extractSingularRow } from "../_object_crud_util";
import { getHelperStripUserType } from "./_util";
import { userSchema } from "@braintrust/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) =>
      extractSingularRow({
        rows: await getHelperStripUserType(
          { id: [params.id], user_type: ["user"] },
          authLookup,
        ),
        notFoundErrorMessage: {
          permission: "read",
          objectType: "user",
          objectIds: [params.id],
        },
      }),
    {
      paramsSchema: idParamSchema,
      outputSchema: userSchema,
    },
  );
}
