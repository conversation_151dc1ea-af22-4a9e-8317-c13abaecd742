import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
  nullishSingleOrArraySchema,
} from "../_object_crud_util";
import { projectScoreSchema } from "@braintrust/typespecs";

const paramsSchema = paginationParamsSchema
  .merge(
    commonFilterParamsSchema.pick({
      name: true,
      project_name: true,
      project_id: true,
      org_name: true,
      org_id: true,
      id: true,
    }),
  )
  .merge(
    nullishSingleOrArraySchema(projectScoreSchema.pick({ score_type: true })),
  );

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        priorObjectTables: ["project_score"],
        permissionInfo: {
          aclObjectType: "project",
          aclPermission: "read",
        },
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: projectScoreSchema.array(),
      renameFields: { project_score_name: "name" },
    },
  );
}
