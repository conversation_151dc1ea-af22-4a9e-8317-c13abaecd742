import { type NextApiRequest, type NextApiResponse } from "next";
import { projectScoreSchema } from "@braintrust/typespecs";
import { isArray } from "#/utils/object";
import { type z } from "zod";

// The "categories" parameter is a JSON array, which node-pg will try to
// serialize as a postgres array rather than as a JSON object.
export function transformCategories(
  x: z.infer<typeof projectScoreSchema.shape.categories>,
) {
  return isArray(x) ? JSON.stringify(x) : x;
}

export const categoriesParamSchema =
  projectScoreSchema.shape.categories.transform(transformCategories);

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
