import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import { categoriesParamSchema } from "./util";
import {
  createProjectScoreSchema,
  projectScoreSchema,
} from "@braintrust/typespecs";
import { z } from "zod";

const paramsSchema = createProjectScoreSchema
  .omit({ name: true, categories: true })
  .extend({
    project_score_name: createProjectScoreSchema.shape.name,
    categories: categoriesParamSchema,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_project_score", {
    paramsSchema,
    outputSchema: z.object({
      project_score: projectScoreSchema,
      found_existing: z.boolean(),
    }),
    argnames: [
      "auth_id",
      "project_id",
      "project_score_name",
      "description",
      "score_type",
      "categories",
      "config",
      "update",
    ],
  });
}
