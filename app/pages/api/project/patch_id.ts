import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import { projectSchema, patchProjectSchema } from "@braintrust/typespecs";
import { flushCache } from "../_invoke_supabase_udf";

const paramsSchema = idParamSchema.merge(patchProjectSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, ...paramsRest }, authLookup) => {
      try {
        return extractSingularRow({
          rows: await patchObjects({
            authLookup,
            permissionInfo: {
              aclObjectType: "project",
            },
            filters: {
              id,
            },
            patchValueParams: paramsRest,
            fullResultsSize: 1,
          }),
          notFoundErrorMessage: undefined,
        });
      } finally {
        await flushCache({ objectId: id });
      }
    },
    {
      paramsSchema,
      outputSchema: projectSchema,
    },
  );
}
