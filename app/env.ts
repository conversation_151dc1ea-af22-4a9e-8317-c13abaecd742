import { z } from "zod";

const boolean = (def: boolean) =>
  z
    .string()
    .default(def ? "true" : "false")
    .transform((v) =>
      ["y", "yes", "true", "1", "on"].includes(v.toLowerCase()),
    );

// use a function to make testing easier
export const env = () => ({
  ...z
    .object({
      ...z
        .object({
          NODE_ENV: z.string().default("development"),
        })
        .describe("System env vars").shape,
      ...z
        .object({
          CSP_ENABLE_STRICT: boolean(false).describe(
            "Whether we should use 'strict-dynamic' and nonces.",
          ),
          CSP_ENABLED_ORG_NAMES: z
            .string()
            .default("")
            .describe(
              "A regex string of org names to enable CSP for. An optimization to avoid calling our db per request.",
            ),
          CSP_GLOBAL_POLICY: z
            .string()
            .default("")
            .describe(
              "CSP that is enforced. May be overridden by org-specific policies.",
            ),
          CSP_GLOBAL_REPORT_ONLY_POLICY: z
            .string()
            .default("")
            .describe(
              "CSP that is report-only. May be overridden by org-specific policies.",
            ),
          ENABLE_X_FRAME_OPTIONS: boolean(false).describe(
            "Whether we should enable X-Frame-Options.",
          ),
        })
        .describe("Security Policies").shape,
    })
    .parse(process.env),
  // temp overrides due to limited vercel env vars
  // ...{
  //   CSP_ENABLE_STRICT: true,
  //   CSP_GLOBAL_POLICY: `script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' {{#nonce}}'strict-dynamic' 'nonce-{{nonce}}'{{/nonce}} {{^nonce}}'unsafe-inline' https http{{/nonce}} *.js.stripe.com js.stripe.com maps.googleapis.com clerk.braintrust.dev *.clerk.accounts.dev {{^nonce}}*.youtube.com api.segment.io cdn.segment.com cdn.jsdelivr.net vercel.live tag.unifyintent.com *.googletagmanager.com buttons.github.io challenges.cloudflare.com googleads.g.doubleclick.net{{/nonce}}; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/****************/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=development&sentry_release=11;`,
  //   CSP_GLOBAL_REPORT_ONLY_POLICY: "",
  // },
});

// to make startup/runtime errors easier to debug
env();
