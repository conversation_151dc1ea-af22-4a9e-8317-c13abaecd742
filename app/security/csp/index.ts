import { type NextRequest } from "next/server";
import {
  type ContentSecurityPolicy,
  contentSecurityPolicySchema,
  type Policies,
} from "./types";
import parseContentSecurityPolicy from "content-security-policy-parser";
import buildContentSecurityPolicy from "content-security-policy-builder";
import mustache from "mustache";
import { headers } from "next/headers";
import { env } from "#/env";

export function isCspEnabled(orgName: string) {
  const enabledOrgs = (env().CSP_ENABLED_ORG_NAMES || "").trim();
  if (!enabledOrgs) {
    return false;
  }

  try {
    return new RegExp(enabledOrgs, "i").test(orgName);
  } catch (e) {
    console.error("Invalid RegExp in CSP_ENABLED_ORG_NAMES", e);
  }

  return enabledOrgs.includes(orgName);
}

export const getCSP = (policy: string, directive: string): string[] => {
  return parseContentSecurityPolicy(policy).get(directive) || [];
};

export const setCSP = (policy: string, directive: string, value: string[]) => {
  const parsedPolicy = parseContentSecurityPolicy(policy);
  parsedPolicy.set(directive, [
    ...(parsedPolicy.get(directive) ?? []),
    ...(value ?? []),
  ]);
  return buildContentSecurityPolicy({ directives: parsedPolicy });
};

export const addReportingEndpoints = ({
  csp,
  cspReportOnly,
}: Policies): Policies => {
  const policyReportURIs = getCSP(csp, "report-uri");
  const policyReportOnlyURIs = getCSP(cspReportOnly, "report-uri");

  const uniqueReportingURIs = Object.fromEntries(
    [...new Set([...policyReportURIs, ...policyReportOnlyURIs])].map(
      (uri, i) => [uri, `csp-endpoint-${i}`],
    ),
  );

  if (policyReportURIs.length) {
    csp = setCSP(
      csp,
      "report-to",
      // only one value allowed.. let's assume the last one should win
      // https://w3c.github.io/webappsec-csp/#directive-report-to
      [policyReportURIs.map((uri) => uniqueReportingURIs[uri]).slice(-1)[0]],
    );
  }

  if (policyReportOnlyURIs.length) {
    cspReportOnly = setCSP(
      cspReportOnly,
      "report-to",
      // only one value allowed.. let's assume the last one should win
      // https://w3c.github.io/webappsec-csp/#directive-report-to
      [
        policyReportOnlyURIs
          .map((uri) => uniqueReportingURIs[uri])
          .slice(-1)[0],
      ],
    );
  }

  const hasReportingEndpoints =
    policyReportURIs.length || policyReportOnlyURIs.length;

  const reportingEndpoints =
    hasReportingEndpoints &&
    Object.entries(uniqueReportingURIs)
      .map(([uri, endpoint]) => `${endpoint}="${uri}"`)
      .join(", ");

  return {
    csp,
    cspReportOnly,
    ...(reportingEndpoints && { reportingEndpoints }),
  };
};

export const getPolicies = async (
  getter: () => ContentSecurityPolicy | Promise<ContentSecurityPolicy>,
): Promise<Policies> => {
  const data = await getter();
  const validatedData = contentSecurityPolicySchema.parse(data);
  return addReportingEndpoints({
    csp: cleanPolicy(validatedData.policy),
    cspReportOnly: cleanPolicy(validatedData.policy_report_only),
  });
};

const mergeDirectives = (...directives: string[]) => {
  // TODO: let the last one win for now
  return directives.filter(Boolean).slice(-1)[0] || "";
};

export const mergePolicies = (...policies: Policies[]) => {
  return policies.reduce(
    (acc, curr) => ({
      csp: mergeDirectives(acc.csp, curr.csp),
      cspReportOnly: mergeDirectives(acc.cspReportOnly, curr.cspReportOnly),
      reportingEndpoints: mergeDirectives(
        acc.reportingEndpoints || "",
        curr.reportingEndpoints || "",
      ),
    }),
    { csp: "", cspReportOnly: "", reportingEndpoints: "" },
  );
};

const cleanPolicy = (policy: string) => {
  return policy.replace(/\s+/g, " ").trim();
};

export const getGlobalPolicies = (): ContentSecurityPolicy => {
  return {
    policy: env().CSP_GLOBAL_POLICY || "",
    policy_report_only: env().CSP_GLOBAL_REPORT_ONLY_POLICY || "",
  };
};

const getOrgPolicies = async (req: NextRequest) => {
  const orgName = decodeURIComponent(
    req.nextUrl.pathname.match(/^\/app\/([^\/]+)/)?.[1] ?? "",
  );

  const policies = await (async () => {
    if (!isCspEnabled(orgName)) {
      return;
    }

    const url = new URL(req.url);
    const baseUrl = `${url.protocol}//${url.host}`;

    // It is terrible to look up the auth token from the request cookies, but we can't call Clerk's auth() function from
    // within the middleware because auth() requires Clerk's middleware to already be initialized.
    const authToken = req.cookies.get("__session")?.value;

    try {
      const response = await fetch(
        `${baseUrl}/api/actions/getOrganizationContentSecurityPolicies`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
          },
          body: JSON.stringify({ function_args: { org_name: orgName } }),
        },
      );

      if (!response.ok) {
        console.error(
          `Failed to fetch organization CSP report only for ${orgName}: ${response.statusText}`,
        );
        return;
      }

      return await response.json();
    } catch (e) {
      console.error(
        `Failed to fetch organization CSP report only for ${orgName}: ${e}`,
      );
      return;
    }
  })();

  return {
    policy: "",
    policy_report_only: "",
    ...policies,
  };
};

const hasVariable = (template: string | undefined, variable: string) => {
  return !!template?.includes(`{{${variable}}}`);
};

const maybeGetNonce = (combined: Policies) => {
  return env().CSP_ENABLE_STRICT &&
    (hasVariable(combined.csp, "nonce") ||
      hasVariable(combined.cspReportOnly, "nonce"))
    ? Buffer.from(crypto.randomUUID()).toString("base64")
    : undefined;
};

const tryRender = (template: string, variables: Record<string, unknown>) => {
  try {
    return mustache.render(template, variables);
  } catch (error) {
    console.error(`Failed to render CSP template: ${error}`);
    return template;
  }
};

const renderPolicies = (
  combined: Policies,
  variables: {
    nonce: string | undefined;
    is_production: boolean;
  },
) => {
  return {
    ...combined,
    csp: tryRender(combined.csp, variables),
    cspReportOnly: tryRender(combined.cspReportOnly, variables),
  };
};

export const addCSP = async (req: NextRequest, res: Response) => {
  const global = await getPolicies(getGlobalPolicies);
  const org = await getPolicies(() => getOrgPolicies(req));
  const rawCombined = mergePolicies(global, org);
  const nonce = maybeGetNonce(rawCombined);

  const combined = renderPolicies(rawCombined, {
    nonce,
    is_production: env().NODE_ENV === "production",
  });

  if (nonce) {
    res.headers.set("x-nonce", nonce);
  }

  if (combined.csp) {
    res.headers.set("Content-Security-Policy", combined.csp);
  }

  if (combined.cspReportOnly) {
    res.headers.set(
      "Content-Security-Policy-Report-Only",
      combined.cspReportOnly,
    );
  }

  if (combined.reportingEndpoints) {
    res.headers.set("Reporting-Endpoints", combined.reportingEndpoints);
  }
};

export const getNonce = async () => {
  "use server";
  return (await headers()).get("x-nonce") ?? undefined;
};
