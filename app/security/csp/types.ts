import { z } from "zod";

export const reportToSchema = z.object({
  policy: z.string(),
  endpoints: z.string(),
});

export type ReportTo = z.infer<typeof reportToSchema>;

export const contentSecurityPolicySchema = z.object({
  policy: z.string(),
  policy_report_only: z.string(),
  reportingEndpoints: z.string().nullish(),
});

export type ContentSecurityPolicy = z.infer<typeof contentSecurityPolicySchema>;

export const organizationContentSecurityPoliciesSchema =
  contentSecurityPolicySchema.extend({
    org_id: z.string().uuid(),
  });

export type OrganizationContentSecurityPolicies = z.infer<
  typeof organizationContentSecurityPoliciesSchema
>;

export type Policies = {
  csp: string;
  cspReportOnly: string;
  reportingEndpoints?: string;
};
