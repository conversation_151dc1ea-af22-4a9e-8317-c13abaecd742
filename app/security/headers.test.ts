import { NextRequest, NextResponse } from "next/server";
import { describe, expect, test, beforeEach, afterEach, vi } from "vitest";
import { addXFrameOptions } from "./headers";

const originalEnv = { ...process.env };

beforeEach(() => {
  delete process.env.ENABLE_X_FRAME_OPTIONS;
});

afterEach(() => {
  process.env = { ...originalEnv };
  vi.resetAllMocks();
});

describe("addXFrameOptions", () => {
  test("adds X-Frame-Options header only when enabled", async () => {
    const req = new NextRequest("https://example.com");
    const res = new NextResponse();

    await addXFrameOptions(req, res);

    expect(res.headers.get("X-Frame-Options")).toBe(null);

    process.env.ENABLE_X_FRAME_OPTIONS = "true";
    await addXFrameOptions(req, res);

    expect(res.headers.get("X-Frame-Options")).toBe("SAMEORIGIN");
  });
});
