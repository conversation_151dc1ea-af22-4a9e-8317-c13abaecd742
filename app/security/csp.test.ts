import { describe, expect, test, beforeEach, afterEach, vi } from "vitest";
import { isCspEnabled, addCSP } from "./csp";
import { type Policies } from "./csp/types";

// Import the internal functions
import { mergePolicies, getPolicies, getGlobalPolicies } from "./csp";
import { NextRequest, NextResponse } from "next/server";

// Store original environment variables to restore after tests
const originalEnv = { ...process.env };

// Setup and teardown for environment variables
beforeEach(() => {
  // Clear any environment variables that might affect tests
  delete process.env.CSP_ENABLED_ORG_NAMES;
  delete process.env.CSP_GLOBAL_POLICY;
  delete process.env.CSP_GLOBAL_REPORT_ONLY_POLICY;
  delete process.env.CSP_REPORT_TO;
});

afterEach(() => {
  // Restore original environment variables
  process.env = { ...originalEnv };
  vi.resetAllMocks();
});

describe("isCspEnabled", () => {
  test("returns false when CSP_ENABLED_ORG_NAMES is not set", () => {
    expect(isCspEnabled("test-org")).toBe(false);
  });

  test("returns false when org is not in CSP_ENABLED_ORG_NAMES", () => {
    process.env.CSP_ENABLED_ORG_NAMES = "org1|org2|org3";
    expect(isCspEnabled("test-org")).toBe(false);
  });

  test("returns true when org is in CSP_ENABLED_ORG_NAMES", () => {
    process.env.CSP_ENABLED_ORG_NAMES = "org1|test-org|org3";
    expect(isCspEnabled("test-org")).toBe(true);
  });

  test("handles whitespace in CSP_ENABLED_ORG_NAMES", () => {
    process.env.CSP_ENABLED_ORG_NAMES = "org1|test-org|org3";
    expect(isCspEnabled("test-org")).toBe(true);
  });

  test("handles invalid expressions", () => {
    process.env.CSP_ENABLED_ORG_NAMES = "org1|(a";
    expect(isCspEnabled("org1")).toBe(true);
  });
});

describe("mergePolicies", () => {
  test("merges multiple policies correctly", () => {
    const policy1: Policies = {
      csp: "default-src 'self'",
      cspReportOnly:
        "default-src 'none'; report-uri https://example1.com/reports",
    };
    const policy2: Policies = {
      csp: "default-src 'unsafe-inline'",
      cspReportOnly: "default-src 'unsafe-eval'",
      reportingEndpoints: "csp-reporting-1=https://example2.com/reports",
    };
    expect(mergePolicies(policy1, policy2)).toEqual(policy2);
  });
});

describe("getPolicies", () => {
  test("processes valid ContentSecurityPolicy object", async () => {
    const result = await getPolicies(() => ({
      policy: "default-src 'self'",
      policy_report_only: "default-src 'none'",
    }));

    expect(result).toEqual({
      csp: "default-src 'self'",
      cspReportOnly: "default-src 'none'",
    });
  });
});

describe("getGlobalPolicies", () => {
  test("returns empty policies when environment variables are not set", () => {
    const result = getGlobalPolicies();
    expect(result).toEqual({
      policy: "",
      policy_report_only: "",
    });
  });

  test("returns policies from environment variables", () => {
    process.env.CSP_GLOBAL_POLICY = "default-src 'self'";
    process.env.CSP_GLOBAL_REPORT_ONLY_POLICY = "default-src 'none'";

    const result = getGlobalPolicies();
    expect(result).toEqual({
      policy: "default-src 'self'",
      policy_report_only: "default-src 'none'",
    });
  });
});

describe("addCSP", () => {
  test("adds CSP headers to response when policies are present", async () => {
    process.env.CSP_GLOBAL_POLICY = "default-src 'self'";
    process.env.CSP_GLOBAL_REPORT_ONLY_POLICY = "default-src 'none'";

    const req = new NextRequest("https://example.com");
    const res = new NextResponse();

    await addCSP(req, res);

    expect(res.headers.get("Content-Security-Policy")).toBe(
      "default-src 'self'",
    );
    expect(res.headers.get("Content-Security-Policy-Report-Only")).toBe(
      "default-src 'none'",
    );
    expect(res.headers.has("Report-To")).toBe(false);
    expect(res.headers.has("Reporting-Endpoints")).toBe(false);
  });

  test("does not add CSP headers when policies are empty", async () => {
    process.env.CSP_GLOBAL_POLICY = "";
    process.env.CSP_GLOBAL_REPORT_ONLY_POLICY = "";

    const req = new NextRequest("https://example.com");
    const res = new NextResponse();

    await addCSP(req, res);

    expect(res.headers.has("Content-Security-Policy")).toBe(false);
    expect(res.headers.has("Content-Security-Policy-Report-Only")).toBe(false);
    expect(res.headers.has("Reporting-Endpoints")).toBe(false);
  });

  test("extracts reporting endpoints with multiple entries", async () => {
    process.env.CSP_GLOBAL_POLICY =
      "default-src 'self'; report-uri http://example.com/policy";
    process.env.CSP_GLOBAL_REPORT_ONLY_POLICY =
      "default-src 'none'; report-uri http://example.com/report";

    const req = new NextRequest("https://example.com");
    const res = new NextResponse();

    await addCSP(req, res);

    expect(res.headers.get("Content-Security-Policy")).toBe(
      "default-src 'self'; report-uri http://example.com/policy; report-to csp-endpoint-0",
    );
    expect(res.headers.get("Content-Security-Policy-Report-Only")).toBe(
      "default-src 'none'; report-uri http://example.com/report; report-to csp-endpoint-1",
    );
    expect(res.headers.get("Reporting-Endpoints")).toBe(
      'csp-endpoint-0="http://example.com/policy", csp-endpoint-1="http://example.com/report"',
    );
  });

  test("extracts reporting endpoints with multiple entries", async () => {
    process.env.CSP_GLOBAL_POLICY =
      "default-src 'self'; report-uri http://example.com/policy http://example.com/report";
    process.env.CSP_GLOBAL_REPORT_ONLY_POLICY =
      "default-src 'none'; report-uri http://example.com/report";

    const req = new NextRequest("https://example.com");
    const res = new NextResponse();

    await addCSP(req, res);

    expect(res.headers.get("Content-Security-Policy")).toBe(
      "default-src 'self'; report-uri http://example.com/policy http://example.com/report; report-to csp-endpoint-1",
    );
    expect(res.headers.get("Content-Security-Policy-Report-Only")).toBe(
      "default-src 'none'; report-uri http://example.com/report; report-to csp-endpoint-1",
    );
    expect(res.headers.get("Reporting-Endpoints")).toBe(
      'csp-endpoint-0="http://example.com/policy", csp-endpoint-1="http://example.com/report"',
    );
  });
});
