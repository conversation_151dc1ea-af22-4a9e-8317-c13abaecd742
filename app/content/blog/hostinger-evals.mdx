---
title: "How <PERSON><PERSON> evaluates AI applications with Braintrust"
description: "<PERSON><PERSON><PERSON>, Senior Data Scientist on the AI team @ Hostinger, provides an overview of how she leverages Braintrust to accelerate <PERSON><PERSON>'s AI development process and automate over 40% of customer support chat conversations."
author: "<PERSON>"
date: "27 February 2024"
image: "/blog/meta/hostinger-evals/opengraph-image.png"
twimage: "/blog/meta/hostinger-evals/twitter-image.png"
---

import { YouTubeEmbed } from "#/ui/blog/youtube-embed";
import { Blog<PERSON>uthor } from "#/ui/blog/blog-author";

# How <PERSON><PERSON> evaluates AI applications with Braintrust

<BlogAuthor author="<PERSON>" date="27 February 2024" />

[<PERSON><PERSON>](https://www.hostinger.com/) is a leading provider of web hosting solutions, serving over a million creators from 150+ countries.
At Braintrust, we've had the opportunity to work closely with <PERSON><PERSON> as they work on exciting AI applications,
like an AI Customer Support chatbot that now **handles over 40% of support chat conversations.**

In the video below, <PERSON><PERSON><PERSON> (Senior Data Scientist on the AI team @ <PERSON>inger) explains how she approaches AI evaluations.

<br />

<div>
  <YouTubeEmbed videoId="l68_L79zKkA" />
</div>

## <PERSON><PERSON>'s Approach to Evaluations (0:10)

As <PERSON>cija explains in the video, the Hostinger team has 3 main goals when they run LLM evaluations:

1. Evaluate how offline changes impact application behavior during development, including running evaluations as part of the CI/CD process
2. Continually assess the performance of live AI applications
3. Identify and prioritize emerging issues to further improve the application

## What Evaluations does Hostinger Run? (1:47)

The Hostinger team runs a variety of evaluations. Some examples include:

- Component checks (URLs, correct doc)
- Automated comparison vs. expected answers (factuality, truthfulness)
- Automated evaluations without expected answers (sentiment analysis, safety)
- Internal human review (thumbs up/down, comments)
- Live user feedback (thumbs up/down, comments)

## How Braintrust Supports Hostinger (2:40)

After a quick setup process (Liucija got started by herself!), the Hostinger team now leverages Braintrust to run evaluations,
track progress using a visual dashboard, drill down into specific improvements / regressions, manage / version datasets, and
log production data. Braintrust saves the Hostinger team hours of manual evaluation a day, allowing Hostinger to work on **3x
more AI features with high quality assurance.**

<br />

<b>
  If you are building AI applications and want to see how Braintrust can help,
  [check out our docs](https://www.braintrustdata.com/docs) or [get in
  touch](https://www.braintrustdata.com/contact)!
</b>
