---
title: "Open sourcing the AI proxy"
description: "The Braintrust AI Proxy is now open source! We also added support for Azure OpenAI and provider load
balancing."
author: "<PERSON><PERSON><PERSON> Goyal"
date: "27 November 2023"
image: "/blog/meta/open-sourcing-proxy/opengraph-image.png"
twimage: "/blog/meta/open-sourcing-proxy/twitter-image.png"
---

import { StarGithubButton } from "#/ui/github-button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";

# Open sourcing the AI proxy

<BlogAuthor author="Ankur Goyal" date="27 November 2023" />

Last week, we released the [Braintrust AI Proxy](/blog/ai-proxy), a new, free way to access LLaMa2, Mistral,
OpenAI, Anthropic, and many [other models](/docs/guides/proxy#supported-models) behind the OpenAI protocol with
built-in caching and API key management.

<PERSON><PERSON> immediately started reaching out about running the proxy in production. We firmly believe
that code on the critical path to production should be open source, so we're excited to announce that the
proxy's source code is now available on [GitHub](https://github.com/braintrustdata/braintrust-proxy) under the
[MIT license](https://github.com/braintrustdata/braintrust-proxy).

<StarGithubButton repo="braintrust-proxy" />

## Deployment options

You can continue to access the proxy, for free, by using the hosted version at `https://braintrustproxy.com`. It's hosted
on [Cloudflare workers](https://workers.cloudflare.com/) and end-to-end encrypts cached data using 256-bit AES-GCM encryption.
For more details, see the [documentation](/docs/guides/proxy) or [source code](https://github.com/braintrustdata/braintrust-proxy/tree/main/apis/cloudflare).

The repository also contains [instructions](https://github.com/braintrustdata/braintrust-proxy/blob/main/README.md#deploying) for deploying the
proxy to [Vercel Edge Functions](https://vercel.com/docs/functions/edge-functions), [Cloudflare workers](https://workers.cloudflare.com/),
[AWS Lambda](https://aws.amazon.com/lambda/), or as a plain-old [Express server](https://expressjs.com/).

### Benchmarks

I did some quick benchmarks, from my in-laws' place in California and an EC2 machine (US East N. Virginia) to compare performance across options ([code](https://gist.github.com/ankrgyl/eefb0940399f89aa69e5b0b3145a373e)).
The AWS Lambda functions are deployed in `us-east-1`. `aws-pc` is AWS Lambda with [provisioned concurrency](https://docs.aws.amazon.com/lambda/latest/dg/provisioned-concurrency.html).

<p>
  <span className="nx-font-semibold">In-laws (CA)</span>
</p>

```bash
$ python proxy_benchmark.py -n 100
cloudflare: AVG: 57.98ms,   MIN: 42.39ms,   MAX: 258.04ms
vercel:     AVG: 82.05ms,   MIN: 54.65ms,   MAX: 326.60ms
aws:        AVG: 131.95ms,  MIN: 103.64ms,  MAX: 722.90ms
aws-pc:     AVG: 145.10ms,  MIN: 109.22ms,  MAX: 1704.05ms
```

<p>
  <span className="nx-font-semibold">EC2 (US East N. Virginia)</span>
</p>

```bash
$ python proxy_benchmark.py -n 100
cloudflare: AVG: 32.23ms,   MIN: 20.15ms,   MAX: 283.90ms
vercel:     AVG: 55.72ms,   MIN: 25.03ms,   MAX: 512.94ms
aws:        AVG: 43.91ms,   MIN: 22.20ms,   MAX: 130.78ms
aws-pc:     AVG: 68.13ms,   MIN: 24.46ms,   MAX: 973.50ms
```

As you can see, Cloudflare and Vercel are consistently very fast, and AWS Lambda in US East suffers (as expected) when measured from CA.
I was surprised that AWS Lambda with provisioned concurrency was slower than without. Perhaps I misconfigured something...

## Additional features

Along with the open source release, the proxy contains a number of useful built-in features.

### Caching

The proxy automatically caches responses from the model provider if you set a `seed` value or `temperature=0`.
Seeds are a new feature in the OpenAI API that allows you to create reproduceable results, but most model providers
do not yet support them. The proxy automatically handles that for you.

### API key management

You can add API keys across providers as [secrets in Braintrust](/app/settings?subroute=secrets), and use a single
API key to access all of them. This is a great way to manage your API keys in one place, and share them with your team.

### Load balancing

You can now add multiple keys and organizations as [secrets in Braintrust](/app/settings?subroute=secrets),
and the proxy will automatically load balance across them for you. This is a simple way to add resiliency across OpenAI accounts
or providers (e.g. OpenAI and Azure).

### Azure OpenAI

You can access Azure's OpenAI endpoints through the proxy, with vanilla OpenAI drivers, by configuring Azure endpoints in
[Braintrust](/app/settings?subroute=secrets). If you configure both OpenAI and Azure endpoints,
the proxy will automatically load balance between them.

![Configure secrets](/blog/img/secrets-endpoint-config.gif)

## What's next

We have an exciting roadmap ahead for the proxy, including more advanced load balancing/resiliency features, support for
more models/providers, and deeper integrations into Braintrust.

If you have any feedback or want to collaborate, [send us an email](/contact) or join
our [Discord](https://discord.gg/6G8s47F44X).
