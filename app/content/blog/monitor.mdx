---
title: "New monitor page for easy analytics"
description: "More visibility into performance across logs and experiments."
author: "<PERSON>nell<PERSON> Altunyan"
date: "18 December 2024"
image: "/blog/img/monitor-page.png"
---

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";
import Image from 'next/image';
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";

# New monitor page for easy analytics

<BlogAuthor author="Ornella Altunyan" date="18 December 2024" />

We're launching a new monitor page that provides comprehensive performance and usage patterns across both logs and experiments. This tool is useful for:
- Debugging performance issues by correlating different metrics
- Optimizing costs by identifying expensive patterns
- Tracking the impact of prompt changes on model performance
- Monitoring production stability through response times

![Monitor page](/docs/guides/monitor/monitor-basic.png)

The monitor page aggregates the key metrics that matter for LLM applications:
* Latency
* Token counts
* Time to first token
* Cost
* Request volume
* Model performance scores

What makes this particularly powerful is the ability to analyze these metrics across both your logs and experiments, giving you a complete picture of your application's behavior in both production and testing environments.

## Flexible data analysis

Understanding that LLM applications often have complex metadata structures, we've built the monitor page with flexibility in mind. You can:

* Group your data by any metadata field, including custom fields you've defined

![Monitor page with group by](/docs/guides/monitor/monitor-group-by.png)

* Apply filters to individual charts to focus on specific segments of your data

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/monitor/filtersposter.png">
  <source src="/docs/guides/monitor/seriesfilter.mp4" type="video/mp4" />
</video>

* Analyze data across different timeframes to understand trends and patterns

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/monitor/timerangeposter.png">
  <source src="/docs/guides/monitor/timerange.mp4" type="video/mp4" />
</video>

* Drill down into specific datapoints to view the corresponding traces

![Monitor page click to view traces](/docs/guides/monitor/monitor-click.png)

## Getting started

The monitor page is now available in all projects. To begin using it, just navigate to **Monitor** in the top navigation of any project. If you have requests for other metrics you'd like to see, [let us know](mailto:<EMAIL>)!
