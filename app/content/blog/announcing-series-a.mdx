---
title: "Announcing our $36M Series A"
description: "We’re thrilled to announce that we've raised $36 million to advance the future of AI software engineering, bringing our total funding to $45 million."
author: "<PERSON><PERSON><PERSON> Goyal"
date: "8 October 2024"
image: "/blog/meta/series-a/series-a.jpg"
---

import { Twitter } from "#/ui/docs/twitter";
import { Blog<PERSON>uthor } from "#/ui/blog/blog-author";
import Image from 'next/image';

# Announcing our $36 million Series A

<BlogAuthor author="Ankur Goyal" date="8 October 2024" />

<iframe
  src="https://www.youtube.com/embed/PR9XKoNiwqc"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
  className="rounded-md w-full my-6 aspect-video"
/>

Today, we’re thrilled to announce that we've raised $36 million to advance the future of AI software engineering, bringing our total funding to $45 million. Through our work with top AI engineering and product teams from Notion, Stripe, Vercel, Airtable, Instacart, Zapier, Coda, The Browser Company, and many others, we’ve had a front-row seat to what it takes to build world-class AI products. Along the way, we’ve learned a few key lessons:

- Crafting effective prompts requires active iteration.
- Evaluations are crucial for systematically improving quality over time.
- Production logs provide a vital feedback loop, generating new data points that drive better evaluations.

This funding round was led by Martin Casado at Andreessen Horowitz, with participation from existing investors Elad Gil, Greylock, and Basecase, as well as Datadog, Databricks Ventures, Guillermo Rauch (Vercel), Simon Last (Notion), Bryan Helmig (Zapier), Greg Brockman (OpenAI), and Arthur Mensch (Mistral). This support will allow us to double down on solving these challenges.

Non-deterministic models and unpredictable natural language inputs make building robust LLM applications difficult. At Braintrust, our mission is to empower teams to build LLM-enabled applications with confidence. Over the past year, we've discovered that successful AI implementation is driven by collaboration between product engineers, designers, and product managers. The key to their success is continuous experimentation — the average team using Braintrust runs more than 10 experiments a day!

Running evaluations is just the first step in building robust AI apps, though. A cross-functional team needs to be able to seamlessly move back and forth between their codebase, where their prompts and code live, and our UI, where they can visualize evals, search for logs, and experiment with prompts. That's why we're also excited to introduce **[functions](/blog/functions)**, available today, a powerful new feature that unlocks this developer experience. Functions are general purpose primitives that allow you to create tools, prompts, and scorers directly in your codebase, then upload them to Braintrust with a simple command: `braintrust push`. They're available throughout our UI and API, so you can prototype, experiment, and deploy with ease—whether you're building a RAG app or testing new prompts. Behind the scenes, we take care of automatically bundling, versioning, deploying, and sandboxing your code in either our cloud or yours. This means more time for experimentation, less time wrestling with infrastructure.

Functions are just the beginning. We are working towards a future where you can seamlessly share code between your dev environment, production deployment, and Braintrust to enable faster experimentation, automatic optimization, and the full potential of AI. If you're curious about how Braintrust can help you build production-grade AI software, [try it for free today](https://www.braintrust.dev/signup) or [reach out for a demo](https://www.braintrust.dev/contact). And if you agree, [come work with us](https://www.braintrust.dev/careers).
