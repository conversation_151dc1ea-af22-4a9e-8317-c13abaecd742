---
title: "Logging with attachments"
description: "Observability for advanced AI applications."
author: "Ornella Altunyan"
date: "24 October 2024"
image: "/blog/meta/attachments/attachments.png"
---

import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Twitter } from "#/ui/docs/twitter";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";
import Image from 'next/image';

# Logging with attachments

<BlogAuthor author="Ornella Altunyan" date="24 October 2024" />

AI applications are increasingly expanding beyond text input and output to accept and generate images, audio, video, and more. At Braintrust, we are committed to supporting these advanced capabilities. Today, we’re excited to share that you can now upload, download, and contextually view attachments in Braintrust.

Until today, you could log text, structured data, and images (base64 or URLs) to Braintrust. Now, you can upload any type of binary attachment. Once the upload is complete, you can view images, audio, video, and PDF files right inside your traces.

<figure>
<video className="w-full rounded-md aspect-auto" controls poster="/blog/img/attachments/attachments-demo-poster.png">
  <source src="/blog/img/attachments/attachments-demo.mp4" type="video/mp4" />
  <a href="/blog/img/attachments/attachments-demo.mp4">Video</a>
</video>
  <figcaption style={{ textAlign: 'center' }}>_Watch with sound!_</figcaption>
</figure>


## Uploading an attachment

To [upload an attachment](/docs/guides/tracing#uploading-attachments), create a new `Attachment` object to represent the file path or in-memory buffer that you want to upload:

<CodeTabs items={["TypeScript"]}>
```typescript
import { Attachment, initLogger } from "braintrust";

const logger = initLogger();

logger.log({
  input: {
    question: "What is this?",
    context: new Attachment({
      data: "path/to/input_image.jpg",
      filename: "user_input.jpg",
      contentType: "image/jpeg",
    }),
  },
  output: "Example response.",
});
```
</CodeTabs>

You can place the `Attachment` anywhere in a log, dataset, or feedback log.

Behind the scenes, the [Braintrust SDK](/docs/reference/libs/nodejs/classes/Attachment) automatically detects and uploads attachments in the background, in parallel to the original logs. This ensures that the latency of your logs isn’t affected by any additional processing.

Your files are securely stored in an object store and associated with the uploading user’s organization. Only you can access your attachments.

## Viewing an attachment in a trace

If your trace has any attachments, they show up as an additional list under the data viewer in the trace pane. You can preview most images, audio files, videos, or PDFs in the Braintrust UI. You can also download any file to view it locally.

<img src="/docs/guides/traces/attachment-list-one-image.png" alt="Screenshot of attachment list in Braintrust" width="625" height="313" />

## What’s next

Uploading, downloading, and viewing attachments not only simplifies debugging, but also provides observability for multimodal and complex applications. You can use it to log:

- The user’s input images to multimodal models like [GPT-4o](https://platform.openai.com/docs/guides/vision) and [Claude 3](https://docs.anthropic.com/en/docs/build-with-claude/vision).
- Input and output audio from a speech-to-speech model such as [GPT-4o Audio Preview](https://platform.openai.com/docs/guides/audio/faq).
- Raw documents, including PDFs, text files, Word documents, and spreadsheets, before your application processes them into a model input.


[Try out attachments today](/docs/guides/tracing#uploading-attachments) and [let us know what you think](https://www.braintrust.dev/contact). And if you have strong opinions on how complex evals should be implemented, [come work with us](https://www.braintrust.dev/careers).
