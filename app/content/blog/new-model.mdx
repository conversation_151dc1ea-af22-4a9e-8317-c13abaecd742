---
title: "What to do when a new AI model comes out"
description: "How to decide if you should use a new model in production."
author: "<PERSON><PERSON><PERSON>"
date: "4 December 2024"
image: "/blog/img/new-model.png"
---

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";
import Image from 'next/image';
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";

# What to do when a new AI model comes out

<BlogAuthor author="<PERSON><PERSON><PERSON>" date="4 December 2024" />

Every week, it seems like another AI provider releases a state-of-the-art model. These announcements come with impressive benchmarks, but those benchmarks rarely reflect real-world use cases. So, how do you know if the new model is worth deploying in your app?

## Develop a baseline

To gauge if a particular model will improve your application, it’s first worth understanding how well your app is currently performing. In AI applications, performance is measured using evaluations that consider the accuracy or quality of the LLM outputs. Setting up a baseline is simple— the easiest way is to run an eval with a set of data, the AI function you want to test, and a scoring function.


After you [run your first evaluation](https://www.braintrust.dev/docs/start/eval-sdk), you can adapt it to run against more models.

## Benchmark against real data

The best way to evaluate a new AI model is by testing it against the actual data your app handles in production. Generic benchmarks might give a sense of performance, but only your data can reveal how well a model works in your product. To do this in Braintrust, start by pulling [real logs from your app](https://www.braintrust.dev/docs/guides/logs/write) and organizing them into a dataset. Consider choosing a set of logs that are underperforming to see if the new model makes an impact on the scores.

<video className="w-full rounded-md aspect-auto" controls autoPlay muted poster="/blog/img/add-to-dataset-poster.png">
  <source src="/blog/img/add-to-dataset.mp4" type="video/mp4" />
  <a href="/blog/img/add-to-dataset.mp4">Video</a>
</video>

Then, use the dataset to run an evaluation using the new model and directly compare the performance (and other factors like cost, tokens, and more) against the one you’re already using.

![Comparing evals](/blog/img/eval-comparison.png)

Importantly, you should also closely monitor your overall scores to ensure you are not regressing in any areas. To do so, you can use the **Group by** menu in the **Experiments** pane to see the results of your evals sorted by model.

![Group by model](/blog/img/group-by-model.png)

You can also run further evaluations on a more extensive set of logs and check out the eval summaries.

## Swap your model in production

If the results show that the new model outperforms your current one, update it in production. If you make your LLM calls in production using the [AI proxy](https://www.braintrust.dev/docs/guides/proxy), you can do this with just a one-line code change.

<video className="w-full rounded-md aspect-auto" controls autoPlay muted poster="/blog/img/change-model-poster.png">
  <source src="/blog/img/change-model.mp4" type="video/mp4" />
  <a href="/blog/img/change-model.mp4">Video</a>
</video>

## Monitor your application

After shipping the new model in production, you can keep tabs on its performance on the **Monitor** page. To focus on your model change, select **Group by model** and tighten the timeline to when you made the changes.

![Monitor page](/blog/img/monitor.png)

## What’s next

When the next AI model comes out, you won’t need to guess if it’s better for your app. By testing it with your actual data and swapping models quickly, you’ll know for sure, and be able to make a confident decision every time.
