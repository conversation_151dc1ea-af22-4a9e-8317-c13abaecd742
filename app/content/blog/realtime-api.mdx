---
title: "Building serverless apps with the OpenAI Realtime API"
description: "No server setup or configuration necessary."
author: "<PERSON><PERSON><PERSON>, <PERSON>"
date: "4 November 2024"
image: "/blog/meta/realtime-api/realtime-og.png"
---

import { Twitter } from "#/ui/docs/twitter";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";
import Image from "next/image";
import { CodeTabs, TSTab, PYTab, CurlTab } from "#/ui/docs/code-tabs";

# Building secure and scalable production apps with OpenAI’s Realtime API

<BlogAuthor author="<PERSON><PERSON><PERSON>, <PERSON>" date="4 November 2024" />

In early October, OpenAI released the [Realtime API](https://platform.openai.com/docs/guides/realtime), designed for building advanced multimodal conversational experiences. This API enables rich AI applications with features like speech-to-speech interaction and simultaneous multimodal output. However, there are three key pain points that need to be solved before you can use the API to build secure and scalable production applications:
- User-facing credentials
- Logging
- Evaluations

At Braintrust, we want building AI applications with the most cutting-edge models to be a simple, wonderful developer experience. Today, we’re excited to announce support for the Realtime API via the Braintrust [AI proxy](/docs/guides/proxy), and solutions to these specific pain points.

<figure>
  <video className="w-full box-content rounded-md aspect-video" controls poster="/blog/img/realtime-api/realtime-demo-poster.jpg">
    <source src="/blog/img/realtime-api/realtime-demo.mp4" type="video/mp4" />
    <a href="/blog/img/realtime-api/realtime-demo.mp4">Video</a>
  </video>
  <figcaption className="text-center text-primary-500 text-xs">Watch with sound!</figcaption>
</figure>

## Infrastructure challenges

The OpenAI Realtime API is built on WebSockets to enable a responsive user experience. However, if you’re using a serverless backend like Vercel or AWS Lambda, which do not support WebSockets, it’s impossible to connect to the API without hosting a separate server somewhere else.

The API also currently lacks client-side authentication, making it insecure to connect to the API directly from the user’s browser.

<Image unoptimized src="/blog/img/realtime-api/before.png" alt="OpenAI infrastructure" width="1600" height="800" />

The architecture requires developers to solve these problems by [setting up a separate, long-running Node.js relay server][realtime-console-readme]. The relay server runs the provided [`relay.js`](https://github.com/openai/openai-realtime-console/blob/main/relay-server/lib/relay.js) code and holds an OpenAI API key to handle Realtime API connections. Running a separate server complicates your architecture, but the only alternative—storing your API key in the frontend—isn’t secure for production.

[realtime-console-readme]: https://github.com/openai/openai-realtime-console/blob/6ea4dba795fee868c60ea9e8e7eba7469974b3e9/README.md#using-a-relay-server

Because all Realtime API calls need to pass through the relay, it also has to scale up quickly to handle your app’s traffic without impacting responsiveness. We believe that developers would rather focus on the features that make their app unique, rather than infrastructure scaling.

## Using the AI proxy

To address this operational complexity, we rearchitected a solution using our existing [AI proxy](/docs/guides/proxy). This way, you won’t need to embed your OpenAI API key directly into your backend, and you can continue using whichever serverless platform you’re used to using for building AI applications.

<Image unoptimized src="/blog/img/realtime-api/after.png" alt="Braintrust infrastructure" width="1600" height="800" />

The AI proxy securely manages your OpenAI API key, issuing [**temporary credentials**](/docs/guides/proxy#temporary-credentials-for-end-user-access) to your backend and frontend. The frontend sends any voice data from your app to the proxy, which handles secure communication with OpenAI’s Realtime API. This offloads the infrastructure burden to us, and allows you to focus on building your app.

## Configuring your app

To access the Realtime API through the Braintrust proxy, change the proxy URL when instantiating the `RealtimeClient` to `https://braintrustproxy.com/v1/realtime`.

As an example, we [forked the sample app](https://github.com/braintrustdata/openai-realtime-console) from OpenAI and hooked it up to our proxy with just a [couple of lines of code](https://github.com/braintrustdata/openai-realtime-console/pull/1):

<CodeTabs items={["TypeScript"]}>
<TSTab>
```typescript
import { RealtimeClient } from "@openai/realtime-api-beta";

const LOCAL_RELAY_SERVER_URL =
  process.env.REACT_APP_LOCAL_RELAY_SERVER_URL ||
  "https://braintrustproxy.com/v1/realtime";
const apiKey = process.env.OPENAI_API_KEY;

const client = new RealtimeClient({
  url: LOCAL_RELAY_SERVER_URL || undefined,
  apiKey,
  dangerouslyAllowAPIKeyInBrowser: true,
});
```
</TSTab>
</CodeTabs>

Next, generate a [Braintrust temporary credential](/docs/guides/proxy#temporary-credentials-for-end-user-access), to be used instead of the OpenAI API key. This means your OpenAI API key will not be exposed to the client.

<Callout>
  You can also use our proxy with an AI provider’s API key, but you will not have access to other Braintrust features, like logging.
</Callout>

<CodeTabs items={["TypeScript", "Python", "cURL"]}>
<TSTab>
```typescript
const PROXY_URL =
  process.env.BRAINTRUST_PROXY_URL || "https://braintrustproxy.com/v1";
// Braintrust API key starting with `sk-...`.
const BRAINTRUST_API_KEY = process.env.BRAINTRUST_API_KEY;

async function main() {
  const response = await fetch(`${PROXY_URL}/credentials`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${BRAINTRUST_API_KEY}`,
    },
    body: JSON.stringify({
      // Leave undefined to allow all models.
      model: "gpt-4o-realtime-preview-2024-10-01",
      // TTL for starting the request. Once started, the request can stream
      // for as long as needed.
      ttl_seconds: 60 * 10, // 10 minutes.
      logging: {
        project_name: "My project",
      },
    }),
    cache: "no-store",
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to request temporary credentials: ${error}`);
  }

  const { key: tempCredential } = await response.json();
  console.log(`Authorization: Bearer ${tempCredential}`);
}

main();
```
</TSTab>

<PYTab>
```python
import os

import requests

PROXY_URL = os.getenv("BRAINTRUST_PROXY_URL", "https://braintrustproxy.com/v1")
# Braintrust API key starting with `sk-...`.
BRAINTRUST_API_KEY = os.getenv("BRAINTRUST_API_KEY")


def main():
    response = requests.post(
        f"{PROXY_URL}/credentials",
        headers={
            "Authorization": f"Bearer {BRAINTRUST_API_KEY}",
        },
        json={
            # Leave unset to allow all models.
            "model": "gpt-4o-realtime-preview-2024-10-01",
            # TTL for starting the request. Once started, the request can stream
            # for as long as needed.
            "ttl_seconds": 60 * 10,  # 10 minutes.
            "logging": {
                "project_name": "My project",
            },
        },
    )

    if response.status_code != 200:
        raise Exception(f"Failed to request temporary credentials: {response.text}")

    temp_credential = response.json().get("key")
    print(f"Authorization: Bearer {temp_credential}")


if __name__ == "__main__":
    main()
```
</PYTab>

<CurlTab>
```bash
curl -X POST "${BRAINTRUST_PROXY_URL:-https://api.braintrust.dev/v1/proxy}/credentials" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${BRAINTRUST_API_KEY}" \
  --data '{
    "model": "gpt-4o-realtime-preview-2024-10-01",
    "ttl_seconds": 600,
    "logging": {
      "project_name": "My project"
    }
  }'
```
</CurlTab>
</CodeTabs>

## Logging

In addition to client-side authentication, you’ll also get the other benefits of building with Braintrust, like logging, built in. We support logging audio, as well as text, structured data, and images. When you connect to the Realtime API, a log will begin generating, and when your session is closed, the log will be ready to view. Each LLM and tool call will be contained in its own span inside of the trace. And most importantly, all multimodal content is now able to be uploaded and viewed as an [attachment](/blog/attachments) in your trace. This means that you won’t have to exit the UI to double-click on an LLM call and view the input and output, no matter what type of format its in.

<Image unoptimized className="box-content" src="/blog/img/realtime-api/realtime-trace.png" alt="Realtime trace with audio attachment" width={1858 / 2} height={1336 / 2} />

## What’s next

You can use this [open-source repo](https://github.com/braintrustdata/openai-realtime-console) as a starting point for any projects using the Realtime API. Because your app will automatically generate logs in Braintrust, you will have data in exactly the right format to run [evaluations](/docs/guides/evals).

[Try building with the OpenAI Realtime API](/docs/guides/proxy#websocket-based-models) today and [let us know](/contact) what you create!
