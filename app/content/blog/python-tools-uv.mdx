---
title: "Python tool functions: powered by uv"
description: "How we used the uv library to build Python tools."
author: "Ornella Altunyan"
date: "13 November 2024"
image: "/blog/img/astral.png"
---

import { Twitter } from "#/ui/docs/twitter";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";
import Image from 'next/image';
import { Bubble, Score, Tool } from '#/app/(landing)/shapes'

# Support for Python tool functions

<BlogAuthor author="Ornella Altunyan" date="13 November 2024" />

<Image src="/blog/img/astral.png" alt="Supporting Python with Astral's uv" width={1200} height={630} />

Today, we’re announcing support for Python-based [tools](/docs/guides/functions/tools): custom general-purpose code blocks that LLMs can invoke to add complex logic or external operations. Tools are defined in code and sync with the UI using `braintrust push`. If you import libraries or dependencies in your codebase, `braintrust push` also bundles and uploads them alongside your code.

![Tool function in UI](/blog/img/tool-function.png)

Initially, we only supported defining tools in TypeScript because tools like [`esbuild`](https://esbuild.github.io/) and the JavaScript bundling ecosystem made it fairly straightforward. As we built out support for Python-based tools, it was difficult to compile dependencies into one artifact, and many dependencies (like `numpy`, `NLTK`, and `scikit-learn`) ship as platform-specific binaries due to their use of native code.

To address this challenge, we leveraged [uv](https://github.com/astral-sh/uv), the Python package manager from [Astral](https://astral.sh/). uv supports cross-bundling virtual environments by resolving and installing Python dependencies based on a specified target platform and Python version. In Python, `braintrust push` now leverages uv to automatically create a Linux-compatible virtual environment with your choice of dependencies and syncs it to Braintrust with your tool code. Braintrust takes care of the rest— sandboxing, auth, and invoking it from within the platform.

Tools are reusable and composable, making it straightforward to iterate on assistant-style agents and more advanced applications. For example, you can use tools to create simple and composable agents that perform tasks like web-scraping, retrieval augmented generation (RAG), or API execution. Once you build a tool, you can use prompts to deploy it across the UI and API. And thanks to uv, we now support creating tools in both TypeScript and Python.

To learn more about tools, read the [tools guide](/docs/guides/functions/tools) and try building one today.
