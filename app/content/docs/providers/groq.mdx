---
title: "Groq"
description: "Groq model provider configuration and integration guide"
---

import { ModelCapabilities } from "./model-capabilities";
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";
import ModuleInstaller from "#/ui/docs/module-installer";

# Groq

Groq provides ultra-fast inference for open-source language models including Llama, Mixtral, and Gemma models. Braintrust integrates seamlessly with Groq through direct API access, wrapper functions for automatic tracing, and proxy support.

## Setup

To use Groq models, configure your Groq API key in Braintrust.

1. Get a Groq API key from [Groq Console](https://console.groq.com/keys)
2. Add the Groq API key to your organization's [AI providers](/app/settings/secrets)
3. Set the Groq API key and your Braintrust API key as environment variables

```bash title=".env"
GROQ_API_KEY=<your-groq-api-key>
BRAINTRUST_API_KEY=<your-braintrust-api-key>

# If you are self-hosting Braintrust, set the URL of your hosted dataplane
# BRAINTRUST_API_URL=<your-braintrust-api-url-here>
```

<Callout type="info">
API keys are encrypted using 256-bit AES-GCM encryption and are not stored or logged by Braintrust.
</Callout>

## Use Groq with Braintrust AI proxy

The [Braintrust AI Proxy](/docs/guides/proxy) allows you to access Groq models through a unified OpenAI-compatible interface.

Install the `braintrust` and `openai` packages.

<ModuleInstaller packageNames="braintrust openai" />

Then, initialize the client and make a request to a Groq model via the Braintrust AI Proxy.

<CodeTabs>
<TSTab>
```typescript title="groq_proxy.ts" #skip-compile
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const response = await client.chat.completions.create({
  model: "openai/gpt-oss-120b",
  messages: [{ role: "user", content: "Hello, world!" }],
});
```
</TSTab>
<PYTab>
```python title="groq_proxy.py" #skip-compile
import os

from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="openai/gpt-oss-120b",
    messages=[{"role": "user", "content": "Hello, world!"}],
)
```
</PYTab>
</CodeTabs>

## Trace logs with Groq

[Trace](/docs/guides/traces) your Groq LLM calls for observability and monitoring.

When using the Braintrust AI Proxy, API calls are automatically logged to the specified project.

<CodeTabs>
<TSTab>
```typescript title="groq_trace.ts" #skip-compile
import { OpenAI } from "openai";
import { initLogger } from "braintrust";

initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// All API calls are automatically logged
const result = await client.chat.completions.create({
  model: "openai/gpt-oss-120b",
  messages: [{ role: "user", content: "What is machine learning?" }],
});
```
</TSTab>
<PYTab>
```python title="groq_trace.py" #skip-compile
import os

from braintrust import init_logger
from openai import OpenAI

init_logger(project="My Project")

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

# All API calls are automatically logged
result = client.chat.completions.create(
    model="openai/gpt-oss-120b",
    messages=[{"role": "user", "content": "What is machine learning?"}],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
The Braintrust AI Proxy is not required to trace Groq API calls. For more control, learn how to [customize traces](/docs/guides/traces/customize).
</Callout>

## Evaluate with Groq

Evaluations distill the non-deterministic outputs of Groq models into an effective feedback loop that enables you to ship more reliable, higher quality products. Braintrust `Eval` is a simple function composed of a dataset of user inputs, a task, and a set of scorers. To learn more about evaluations, see the [Experiments](/docs/guides/experiments) guide.

<CodeTabs>
<TSTab>
```typescript title="groq_eval.ts" #skip-compile
import { Eval } from "braintrust";
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

Eval("Groq Evaluation", {
  data: () => [
    { input: "What is 2+2?", expected: "4" },
    { input: "What is the capital of France?", expected: "Paris" },
  ],
  task: async (input) => {
    const response = await client.chat.completions.create({
      model: "openai/gpt-oss-120b",
      messages: [{ role: "user", content: input }],
    });
    return response.choices[0].message.content;
  },
  scores: [
    {
      name: "accuracy",
      scorer: (args) => (args.output === args.expected ? 1 : 0),
    },
  ],
});
```
</TSTab>
<PYTab>
```python title="groq_eval.py" #skip-compile
import os

from braintrust import Eval
from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)


def task(input):
    response = client.chat.completions.create(
        model="openai/gpt-oss-120b",
        messages=[{"role": "user", "content": input}],
    )
    return response.choices[0].message.content


def accuracy_scorer(output, expected, **kwargs):
    return 1 if output == expected else 0


Eval(
    "Groq Evaluation",
    data=[
        {"input": "What is 2+2?", "expected": "4"},
        {"input": "What is the capital of France?", "expected": "Paris"},
    ],
    task=task,
    scores=[accuracy_scorer],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
To learn more about tool use, multimodal support, attachments, and masking sensitive data with Groq, visit the [customize traces](/docs/guides/traces/customize) guide.
</Callout>

## Models and capabilities

<ModelCapabilities provider="groq" />
