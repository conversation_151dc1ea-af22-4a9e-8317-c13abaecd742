---
title: "Fireworks"
description: "Fireworks AI model provider configuration and integration guide"
---

import { ModelCapabilities } from "./model-capabilities";
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";
import ModuleInstaller from "#/ui/docs/module-installer";

# Fireworks

Fireworks AI provides fast inference for open-source language models including Llama, Mixtral, Code Llama, and other state-of-the-art models. Braintrust integrates seamlessly with Fireworks through direct API access, wrapper functions for automatic tracing, and proxy support.

## Setup

To use Fireworks models, configure your Fireworks API key in Braintrust.

1. Get a Fireworks API key from [Fireworks AI Console](https://fireworks.ai/account/api-keys)
2. Add the Fireworks API key to your organization's [AI providers](/app/settings/secrets)
3. Set the Fireworks API key and your Braintrust API key as environment variables

```bash title=".env"
FIREWORKS_API_KEY=<your-fireworks-api-key>
BRAINTRUST_API_KEY=<your-braintrust-api-key>

# If you are self-hosting Braintrust, set the URL of your hosted dataplane
# BRAINTRUST_API_URL=<your-braintrust-api-url-here>
```

<Callout type="info">
API keys are encrypted using 256-bit AES-GCM encryption and are not stored or logged by Braintrust.
</Callout>

## Use Fireworks with Braintrust AI proxy

The [Braintrust AI Proxy](/docs/guides/proxy) allows you to access Fireworks models through a unified OpenAI-compatible interface.

Install the `braintrust` and `openai` packages.

<ModuleInstaller packageNames="braintrust openai" />

Then, initialize the client and make a request to a Fireworks model via the Braintrust AI Proxy.

<CodeTabs>
<TSTab>
```typescript title="fireworks_proxy.ts" #skip-compile
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const response = await client.chat.completions.create({
  model: "fireworks/llama-3.1-70b-instruct",
  messages: [{ role: "user", content: "Hello, world!" }],
});
```
</TSTab>
<PYTab>
```python
import os

from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="fireworks/llama-3.1-70b-instruct",
    messages=[{"role": "user", "content": "Hello, world!"}],
)
```
</PYTab>
</CodeTabs>

## Trace logs with Fireworks

[Trace](/docs/guides/traces) your Fireworks LLM calls for observability and monitoring.

When using the Braintrust AI Proxy, API calls are automatically logged to the specified project.

<CodeTabs>
<TSTab>
```typescript title="fireworks_trace.ts" #skip-compile
import { OpenAI } from "openai";
import { initLogger } from "braintrust";

initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// All API calls are automatically logged
const result = await client.chat.completions.create({
  model: "fireworks/llama-3.1-70b-instruct",
  messages: [{ role: "user", content: "What is machine learning?" }],
});
```
</TSTab>
<PYTab>
```python
import os

from braintrust import init_logger
from openai import OpenAI

init_logger(project="My Project")

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

# All API calls are automatically logged
result = client.chat.completions.create(
    model="fireworks/llama-3.1-70b-instruct",
    messages=[{"role": "user", "content": "What is machine learning?"}],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
The Braintrust AI Proxy is not required to trace Fireworks API calls. For more control, learn how to [customize traces](/docs/guides/traces/customize).
</Callout>

## Evaluate with Fireworks

Evaluations distill the non-deterministic outputs of Fireworks models into an effective feedback loop that enables you to ship more reliable, higher quality products. Braintrust `Eval` is a simple function composed of a dataset of user inputs, a task, and a set of scorers. To learn more about evaluations, see the [Experiments](/docs/guides/experiments) guide.

<CodeTabs>
<TSTab>
```typescript title="fireworks_eval.ts" #skip-compile
import { Eval } from "braintrust";
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

Eval("Fireworks Evaluation", {
  data: () => [
    { input: "What is 2+2?", expected: "4" },
    { input: "What is the capital of France?", expected: "Paris" },
  ],
  task: async (input) => {
    const response = await client.chat.completions.create({
      model: "fireworks/llama-3.1-70b-instruct",
      messages: [{ role: "user", content: input }],
    });
    return response.choices[0].message.content;
  },
  scores: [
    {
      name: "accuracy",
      scorer: (args) => (args.output === args.expected ? 1 : 0),
    },
  ],
});
```
</TSTab>
<PYTab>
```python title="fireworks_eval.py" #skip-compile
import os

from braintrust import Eval
from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)


def task(input):
    response = client.chat.completions.create(
        model="fireworks/llama-3.1-70b-instruct",
        messages=[{"role": "user", "content": input}],
    )
    return response.choices[0].message.content


def accuracy_scorer(output, expected, **kwargs):
    return 1 if output == expected else 0


Eval(
    "Fireworks Evaluation",
    data=[
        {"input": "What is 2+2?", "expected": "4"},
        {"input": "What is the capital of France?", "expected": "Paris"},
    ],
    task=task,
    scores=[accuracy_scorer],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
To learn more about tool use, multimodal support, attachments, and masking sensitive data with Fireworks, visit the [customize traces](/docs/guides/traces/customize) guide.
</Callout>

## Models and capabilities

<ModelCapabilities provider="fireworks" />
