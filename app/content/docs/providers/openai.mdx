---
title: "OpenAI"
description: "OpenAI model provider configuration and integration guide"
---

import { ModelCapabilities } from "./model-capabilities";
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";
import ModuleInstaller from "#/ui/docs/module-installer";

# OpenAI

OpenAI provides access to GPT models including GPT-5 and other cutting-edge language models. Braintrust integrates seamlessly with OpenAI through direct API access, `wrapOpenAI` wrapper functions for automatic tracing, and proxy support.

## Setup

To use OpenAI with Braintrust, you'll need an OpenAI API key.

1. Visit [OpenAI's API platform](https://platform.openai.com/api-keys) and create a new API key
2. Add the OpenAI API key to your organization's [AI providers](/app/settings/secrets)
3. Set the OpenAI API key and your Braintrust API key as environment variables

```bash title=".env"
OPENAI_API_KEY=<your-openai-api-key>
BRAINTRUST_API_KEY=<your-braintrust-api-key>

# If you are self-hosting Braintrust, set the URL of your hosted dataplane
# BRAINTRUST_API_URL=<your-braintrust-api-url>
```

<Callout type="info">
API keys are encrypted using 256-bit AES-GCM encryption and are not stored or logged by Braintrust.
</Callout>

Install the `braintrust` and `openai` packages.

<ModuleInstaller packageNames="braintrust openai" />

## Trace with OpenAI

[Trace](/docs/guides/traces) your OpenAI LLM calls for observability and monitoring.

Using the OpenAI Agents SDK? See the [OpenAI Agents SDK](/docs/start/frameworks/openai-agents-sdk) framework docs.

### Trace automatically with `wrapOpenAI`

Braintrust provides `wrapOpenAI` (TypeScript) and `wrap_openai` (Python) functions that automatically log OpenAI API calls. To use them, initialize the logger and pass the OpenAI client to the `wrapOpenAI` function.

<Callout type="info">
`wrapOpenAI` is a convenience function that wraps the OpenAI client with the Braintrust logger. For more control, learn how to [customize traces](/docs/guides/traces/customize).
</Callout>

<CodeTabs>
<TSTab>
```typescript #skip-compile
import { initLogger, wrapOpenAI } from "braintrust";
import OpenAI from "openai";

// Initialize the Braintrust logger
const logger = initLogger({
  projectName: "My Project", // Your project name
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Wrap the OpenAI client with wrapOpenAI
const client = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);

// All API calls are automatically logged
const result = await client.chat.completions.create({
  model: "gpt-5",
  messages: [
    { role: "system", content: "You are a helpful assistant." },
    { role: "user", content: "What is machine learning?" },
  ],
});
```
</TSTab>
<PYTab>
```python #skip-compile
import os

from braintrust import init_logger, wrap_openai
from openai import OpenAI

logger = init_logger(project="My Project")
client = wrap_openai(OpenAI(api_key=os.environ["OPENAI_API_KEY"]))

# All API calls are automatically logged
result = client.chat.completions.create(
    model="gpt-5-mini",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is machine learning?"},
    ],
)
```
</PYTab>
</CodeTabs>

### Stream OpenAI responses

`wrap_openai`/`wrapOpenAI` can automatically log metrics like `prompt_tokens`, `completion_tokens`, and `tokens` for streaming LLM calls if the LLM API returns them. Set `include_usage` to `true` in the `stream_options` parameter to receive these metrics from OpenAI.

<CodeTabs>
<TSTab>
```typescript #skip-compile
const result = await client.chat.completions.create({
  model: "gpt-5-mini",
  messages: [{ role: "user", content: "Count to 10" }],
  stream: true, // [!code highlight]
  stream_options: {
    // [!code highlight]
    include_usage: true, // Required for token metrics // [!code highlight]
  }, // [!code highlight]
});

for await (const chunk of result) {
  // [!code highlight]
  process.stdout.write(chunk.choices[0]?.delta?.content || ""); // [!code highlight]
} // [!code highlight]
```
</TSTab>
<PYTab>
```python #skip-compile
result = client.chat.completions.create(
    model="gpt-5-mini",
    messages=[{"role": "user", "content": "Count to 10"}],
    stream=True,
    stream_options={"include_usage": True},  # Required for token metrics
)

for chunk in result:
    print(chunk.choices[0].delta.content or "", end="")
```
</PYTab>
</CodeTabs>

## Evaluate with OpenAI

Evaluations help you distill the non-deterministic outputs of OpenAI models into an effective feedback loop that enables you to ship more reliable, higher quality products. Braintrust `Eval` is a simple function composed of a dataset of user inputs, a task, and a set of scorers. To learn more about evaluations, see the [Experiments](/docs/guides/experiments) guide.

### Basic OpenAI eval setup

Evaluate the outputs of OpenAI models with Braintrust.

<CodeTabs>
<TSTab>
```typescript title="eval_openai.ts" #skip-compile
import { Eval } from "braintrust";
import { OpenAI } from "openai";

const client = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

Eval("OpenAI Evaluation", {
  // An array of user inputs and expected outputs
  data: () => [
    { input: "What is 2+2?", expected: "4" },
    { input: "What is the capital of France?", expected: "Paris" },
  ],
  task: async (input) => {
    // Your OpenAI LLM call
    const response = await client.chat.completions.create({
      model: "gpt-5-mini",
      messages: [{ role: "user", content: input }],
    });
    return response.choices[0].message.content;
  },
  scores: [
    {
      name: "accuracy",
      // A simple scorer that returns 1 if the output matches the expected output, 0 otherwise
      scorer: (args) => (args.output === args.expected ? 1 : 0),
    },
  ],
});
```
</TSTab>
<PYTab>
```python title="eval_openai.py" #skip-compile
import os

from braintrust import Eval
from openai import OpenAI

client = OpenAI(api_key=os.environ["OPENAI_API_KEY"])


def task(input):
    response = client.chat.completions.create(
        model="gpt-5-mini",
        messages=[{"role": "user", "content": input}],
    )
    return response.choices[0].message.content


def accuracy_scorer(output, expected, **kwargs):
    return 1 if output == expected else 0


Eval(
    "OpenAI Evaluation",
    data=[
        {"input": "What is 2+2?", "expected": "4"},
        {"input": "What is the capital of France?", "expected": "Paris"},
    ],
    task=task,
    scores=[accuracy_scorer],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
Learn more about eval [data](/docs/guides/experiments/write#data) and [scorers](/docs/guides/experiments/write#scorers).
</Callout>

### Use OpenAI as an LLM judge

You can use OpenAI models to score the outputs of other AI systems. This example uses the `LLMClassifierFromSpec` scorer to score the relevance of the outputs of an AI system.

Install the `autoevals` package to use the `LLMClassifierFromSpec` scorer.

<ModuleInstaller packageNames="autoevals" />

Create a scorer that uses the `LLMClassifierFromSpec` scorer to score the relevance of the outputs of an AI system. You can then include `relevanceScorer` as a scorer in your `Eval` function (see above).

<CodeTabs>
<TSTab>
```typescript title="eval_openai.ts" #skip-compile
import { LLMClassifierFromSpec } from "autoevals";

const relevanceScorer = LLMClassifierFromSpec("Relevance", {
  choice_scores: { Relevant: 1, Irrelevant: 0 },
  model: "gpt-5-mini",
  use_cot: true,
});
```
</TSTab>
<PYTab>
```python title="eval_openai.py" #skip-compile
from autoevals import LLMClassifierFromSpec

relevance_scorer = LLMClassifierFromSpec(
    "Relevance",
    choice_scores={"Relevant": 1, "Irrelevant": 0},
    model="gpt-5-mini",
    use_cot=True,
)
```
</PYTab>
</CodeTabs>

## Additional features

### Structured outputs

OpenAI's structured outputs are supported with the wrapper functions.

<CodeTabs>
<TSTab>
```typescript title="eval_openai.ts" #skip-compile
import { z } from "zod";

// Define a Zod schema for the response // [!code highlight]
const ResponseSchema = z.object({
  // [!code highlight]
  name: z.string(), // [!code highlight]
  age: z.number(), // [!code highlight]
}); // [!code highlight]

const completion = await client.beta.chat.completions.parse({
  model: "gpt-5-mini",
  messages: [
    { role: "system", content: "Extract the person's name and age." },
    { role: "user", content: "My name is John and I'm 30 years old." },
  ],
  response_format: {
    type: "json_schema",
    json_schema: {
      name: "person",
      // The Zod schema for the response // [!code highlight]
      schema: ResponseSchema, // [!code highlight]
    },
  },
});
```
</TSTab>
<PYTab>
```python title="eval_openai.py" #skip-compile
from pydantic import BaseModel


class Person(BaseModel):
    name: str
    age: int


completion = client.beta.chat.completions.parse(
    model="gpt-5-mini",
    messages=[
        {"role": "system", "content": "Extract the person's name and age."},
        {"role": "user", "content": "My name is John and I'm 30 years old."},
    ],
    response_format=Person,
)
```
</PYTab>
</CodeTabs>

### Function calling and tools

Braintrust supports OpenAI function calling for building AI agents with tools.

<CodeTabs>
<TSTab>
```typescript title="eval_openai.ts" #skip-compile
const tools = [
  {
    type: "function" as const,
    function: {
      name: "get_weather",
      description: "Get current weather for a location",
      parameters: {
        type: "object",
        properties: {
          location: { type: "string" },
        },
        required: ["location"],
      },
    },
  },
];

const response = await client.chat.completions.create({
  model: "gpt-5-mini",
  messages: [{ role: "user", content: "What's the weather in San Francisco?" }],
  tools, // [!code highlight]
});
```
</TSTab>
<PYTab>
```python title="eval_openai.py" #skip-compile
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get current weather for a location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {"type": "string"},
                },
                "required": ["location"],
            },
        },
    }
]

response = client.chat.completions.create(
    model="gpt-5-mini",
    messages=[{"role": "user", "content": "What's the weather in San Francisco?"}],
    tools=tools,
)
```
</PYTab>
</CodeTabs>

### Multimodal content, attachments, errors, and masking sensitive data

To learn more about these topics, check out the [customize traces](/docs/guides/traces/customize) guide.

## Use OpenAI with Braintrust AI proxy

You can also access OpenAI models through the [Braintrust AI Proxy](/docs/guides/proxy), which provides a unified interface for multiple providers.

<CodeTabs>
<TSTab>
```typescript #skip-compile
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  // [!code highlight]
  apiKey: process.env.BRAINTRUST_API_KEY, // [!code highlight]
});

const response = await client.chat.completions.create({
  model: "gpt-5-mini",
  messages: [{ role: "user", content: "What is a proxy?" }],
  seed: 1, // A seed activates the proxy's cache
});
```
</TSTab>
<PYTab>
```python #skip-compile
import os

from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="gpt-5-mini",
    messages=[{"role": "user", "content": "What is a proxy?"}],
    seed=1,  # A seed activates the proxy's cache
)
```
</PYTab>
</CodeTabs>

## Cookbooks

- [Evaluating audio with the OpenAI Realtime API](/docs/cookbook/recipes/Realtime-OpenAI-API)
- [Using Python functions to extract text from images](/docs/cookbook/recipes/ToolOCR)
- [Using functions to build a RAG agent](/docs/cookbook/recipes/ToolRAG)

## Models and capabilities

<ModelCapabilities provider="openai" />
