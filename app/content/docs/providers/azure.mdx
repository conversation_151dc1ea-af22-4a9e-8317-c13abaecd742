---
title: "Azure OpenAI"
description: "Configure Azure OpenAI Service to access OpenAI models on Azure"
---

# Azure OpenAI

Configure Azure OpenAI Service to access OpenAI models deployed on Microsoft Azure through Braintrust.

## Authentication

Choose between two authentication methods:

- **API Key**: Use an Azure OpenAI API key for authentication
- **Entra API (Azure AD)**: Use Azure Active Directory (Entra ID) for authentication

## Configuration

| Field | Description |
|-------|-------------|
| **API base**<br />URL String | Required. Your Azure OpenAI service endpoint URL in the format `https://{resource-name}.openai.azure.com`. [Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/reference#rest-api-versioning) |
| **Authentication type**<br />`api_key` \| `entra_api` | Optional. Choose between API key or Entra API authentication. Default is `api_key`. [Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/reference#authentication) |
| **API version**<br />String | Optional. The API version to use for requests. Default is `2023-07-01-preview`. [Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/reference#rest-api-versioning) |
| **Deployment**<br />String | Optional. The deployment name for your model (if using named deployments). [Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/how-to/create-resource) |
| **No named deployment**<br />Boolean | Optional. Whether to skip using deployment names in the request path. Default is `false`. If true, the deployment name will not be used in the request path. |

## Models

Azure OpenAI provides access to OpenAI models including:
- GPT-4o `gpt-4o`
- GPT-4 `gpt-4`
- GPT-3.5 Turbo `gpt-35-turbo`
- DALL-E 3 `dall-e-3`
- Whisper `whisper`

**Note**: Model availability varies by region and requires deployment through the Azure portal.

## Setup requirements

1. **Azure OpenAI Resource**: Create an Azure OpenAI service resource in the Azure portal
2. **Model Deployment**: Deploy the models you want to use through the Azure portal
3. **API Access**: Obtain your API key or configure Entra ID authentication
4. **Regional Availability**: Ensure your chosen region supports the models you need

## Additional resources

- [Azure OpenAI Documentation](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/)
- [Model Availability](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/concepts/models)
- [Azure OpenAI Pricing](https://azure.microsoft.com/en-us/pricing/details/cognitive-services/openai-service/)
- [Quickstart Guide](https://docs.microsoft.com/en-us/azure/cognitive-services/openai/quickstart)
