---
title: "Anthropic"
description: "Anthropic model provider configuration and integration guide"
---

import { ModelCapabilities } from "./model-capabilities";
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";
import ModuleInstaller from "#/ui/docs/module-installer";

# Anthropic

Anthropic provides access to Claude models including Claude 4 Sonnet, Claude 4.1 Opus, and other cutting-edge language models. Braintrust integrates seamlessly with Anthropic through direct API access, `wrapAnthropic` wrapper functions for automatic tracing, and proxy support.

## Setup

To use Anthropic with Braintrust, you'll need an Anthropic API key.

1. Visit [Anthropic's Console](https://console.anthropic.com/settings/keys) and create a new API key
2. Add the Anthropic API key to your organization's [AI providers](/app/settings/secrets)
3. Set the Anthropic API key and your Braintrust API key as environment variables

```bash title=".env"
ANTHROPIC_API_KEY=<your-anthropic-api-key>
BRAINTRUST_API_KEY=<your-braintrust-api-key>

# If you are self-hosting Braintrust, set the URL of your hosted dataplane
# BRAINTRUST_API_URL=<your-braintrust-api-url>
```

<Callout type="info">
API keys are encrypted using 256-bit AES-GCM encryption and are not stored or logged by Braintrust.
</Callout>

Install the `braintrust` and `@anthropic-ai/sdk` packages.

<ModuleInstaller packageNames="braintrust @anthropic-ai/sdk" />

## Trace with Anthropic

[Trace](/docs/guides/traces) your Anthropic LLM calls for observability and monitoring.

### Trace automatically with `wrapAnthropic`

Braintrust provides `wrapAnthropic` (TypeScript) and `wrap_anthropic` (Python) functions that automatically log Anthropic API calls. Braintrust handles streaming, metric collection (including cached tokens), and other details.

Initialize the logger and pass the Anthropic client to the `wrapAnthropic` function.

<Callout type="info">
`wrapAnthropic` is a convenience function that wraps the Anthropic client with the Braintrust logger. For more control, learn how to [customize traces](/docs/guides/traces/customize).
</Callout>

<CodeTabs>
<TSTab>
```typescript title="trace_anthropic.ts" #skip-compile
import Anthropic from "@anthropic-ai/sdk";
import { wrapAnthropic, initLogger } from "braintrust";

// Initialize the Braintrust logger
const logger = initLogger({
  projectName: "My Project", // Your project name
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Wrap the Anthropic client with the Braintrust logger
const client = wrapAnthropic(
  new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY }),
);

// All API calls are automatically logged
const result = await client.messages.create({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 1024,
  messages: [{ role: "user", content: "What is machine learning?" }],
});
```
</TSTab>
<PYTab>
```python title="trace_anthropic.py" #skip-compile
import os

import anthropic
from braintrust import init_logger, wrap_anthropic

# Initialize the Braintrust logger
logger = init_logger(project="My Project")

# Wrap the Anthropic client with the Braintrust logger
client = wrap_anthropic(anthropic.Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"]))

# All API calls are automatically logged
result = client.messages.create(
    model="claude-3-5-sonnet-20241022",
    max_tokens=1024,
    messages=[{"role": "user", "content": "What is machine learning?"}],
)
```
</PYTab>
</CodeTabs>

## Evaluate with Anthropic

Evaluations distill the non-deterministic outputs of Anthropic models into an effective feedback loop that enables you to ship more reliable, higher quality products. The Braintrust `Eval` function is composed of a dataset of user inputs, a task, and a set of scorers. To learn more about evaluations, see the [Experiments](/docs/guides/experiments) guide.

### Basic Anthropic eval setup

Evaluate the outputs of Anthropic models with Braintrust.

<CodeTabs>
<TSTab>
```typescript title="eval_anthropic.ts" #skip-compile
import { Eval } from "braintrust";
import Anthropic from "@anthropic-ai/sdk";

const client = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

Eval("Anthropic Evaluation", {
  // An array of user inputs and expected outputs
  data: () => [
    { input: "What is 2+2?", expected: "4" },
    { input: "What is the capital of France?", expected: "Paris" },
  ],
  task: async (input) => {
    // Your Anthropic LLM call
    const response = await client.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1024,
      messages: [{ role: "user", content: input }],
    });
    return response.content[0].text;
  },
  scores: [
    {
      name: "accuracy",
      // A simple scorer that returns 1 if the output matches the expected output, 0 otherwise
      scorer: (args) => (args.output === args.expected ? 1 : 0),
    },
  ],
});
```
</TSTab>
<PYTab>
```python title="eval_anthropic.py" #skip-compile
import os

import anthropic
from braintrust import Eval

client = anthropic.Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"])


def task(input):
    response = client.messages.create(
        model="claude-3-5-sonnet-20241022",
        max_tokens=1024,
        messages=[{"role": "user", "content": input}],
    )
    return response.content[0].text


def accuracy_scorer(output, expected, **kwargs):
    return 1 if output == expected else 0


Eval(
    "Anthropic Evaluation",
    data=[
        {"input": "What is 2+2?", "expected": "4"},
        {"input": "What is the capital of France?", "expected": "Paris"},
    ],
    task=task,
    scores=[accuracy_scorer],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
Learn more about eval [data](/docs/guides/experiments/write#data) and [scorers](/docs/guides/experiments/write#scorers).
</Callout>

### Use Anthropic as an LLM judge

You can use Anthropic models to score the outputs of other AI systems. This example uses  the `LLMClassifierFromSpec` scorer to score the relevance of the outputs of an AI system.

Install the `autoevals` package to use the `LLMClassifierFromSpec` scorer.

<ModuleInstaller packageNames="autoevals" />

Create a scorer that uses the `LLMClassifierFromSpec` scorer to score the relevance of the output. You can then include `relevanceScorer` as a scorer in your `Eval` function (see above).

<CodeTabs>
<TSTab>
```typescript title="anthropic_llm_judge.ts" #skip-compile
import { LLMClassifierFromSpec } from "autoevals";

const relevanceScorer = LLMClassifierFromSpec("Relevance", {
  choice_scores: { Relevant: 1, Irrelevant: 0 },
  model: "claude-3-5-sonnet-20241022",
  use_cot: true,
});
```
</TSTab>
<PYTab>
```python title="anthropic_llm_judge.py" #skip-compile
from autoevals import LLMClassifierFromSpec

relevance_scorer = LLMClassifierFromSpec(
    "Relevance",
    choice_scores={"Relevant": 1, "Irrelevant": 0},
    model="claude-3-5-sonnet-20241022",
    use_cot=True,
)
```
</PYTab>
</CodeTabs>

## Additional features

### Tool use

Anthropic's tool use (function calling) is fully supported:

<CodeTabs>
<TSTab>
```typescript title="anthropic_tool_use.ts" #skip-compile
const tools = [
  {
    name: "get_weather",
    description: "Get current weather for a location",
    input_schema: {
      type: "object",
      properties: {
        location: { type: "string", description: "City name" },
      },
      required: ["location"],
    },
  },
];

const response = await client.messages.create({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 1024,
  messages: [{ role: "user", content: "What's the weather in San Francisco?" }],
  tools, // [!code highlight]
});
```
</TSTab>
<PYTab>
```python title="anthropic_tool_use.py" #skip-compile
tools = [
    {
        "name": "get_weather",
        "description": "Get current weather for a location",
        "input_schema": {
            "type": "object",
            "properties": {
                "location": {"type": "string", "description": "City name"},
            },
            "required": ["location"],
        },
    }
]

response = client.messages.create(
    model="claude-3-5-sonnet-20241022",
    max_tokens=1024,
    messages=[{"role": "user", "content": "What's the weather in San Francisco?"}],
    tools=tools,  # [!code highlight]
)
```
</PYTab>
</CodeTabs>

### System prompts

Anthropic models support system prompts for better instruction following.

<CodeTabs>
<TSTab>
```typescript title="anthropic_system_prompt.ts" #skip-compile
const response = await client.messages.create({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 1024,
  system: "You are a helpful assistant that responds in JSON format.", // [!code highlight]
  messages: [{ role: "user", content: "What is the capital of France?" }],
});
```
</TSTab>
<PYTab>
```python title="anthropic_system_prompt.py" #skip-compile
response = client.messages.create(
    model="claude-3-5-sonnet-20241022",
    max_tokens=1024,
    system="You are a helpful assistant that responds in JSON format.",  # [!code highlight]
    messages=[{"role": "user", "content": "What is the capital of France?"}],
)
```
</PYTab>
</CodeTabs>

### Cached tokens

Anthropic supports prompt caching to reduce costs and latency for repeated content.

<CodeTabs>
<TSTab>
```typescript title="anthropic_cached_tokens.ts" #skip-compile
const response = await client.messages.create({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 1024,
  system: [
    {
      type: "text",
      text: "You are an AI assistant analyzing the following document...",
      cache_control: { type: "ephemeral" }, // [!code highlight]
    },
  ],
  messages: [{ role: "user", content: "Summarize the key points." }],
});
```
</TSTab>
<PYTab>
```python title="anthropic_cached_tokens.py" #skip-compile
response = client.messages.create(
    model="claude-3-5-sonnet-20241022",
    max_tokens=1024,
    system=[
        {
            "type": "text",
            "text": "You are an AI assistant analyzing the following document...",
            "cache_control": {"type": "ephemeral"},  # [!code highlight]
        }
    ],
    messages=[{"role": "user", "content": "Summarize the key points."}],
)
```
</PYTab>
</CodeTabs>

### Multimodal content, attachments, errors, and masking sensitive data

To learn more about these topics, check out the [customize traces](/docs/guides/traces/customize) guide.

## Use Anthropic with Braintrust AI proxy

You can also access Anthropic models through the [Braintrust AI Proxy](/docs/guides/proxy), which provides a unified, OpenAI-compatible interface for multiple providers.

<CodeTabs>
<TSTab>
```typescript title="anthropic_proxy.ts" #skip-compile
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const response = await client.chat.completions.create({
  model: "claude-3-5-sonnet-20241022",
  messages: [{ role: "user", content: "What is a proxy?" }],
  seed: 1, // A seed activates the proxy's cache
});
```
</TSTab>
<PYTab>
```python title="anthropic_proxy.py" #skip-compile
import os

from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="claude-3-5-sonnet-20241022",
    messages=[{"role": "user", "content": "What is a proxy?"}],
    seed=1,  # A seed activates the proxy's cache
)
```
</PYTab>
</CodeTabs>

## Models and capabilities

<ModelCapabilities provider="anthropic" />
