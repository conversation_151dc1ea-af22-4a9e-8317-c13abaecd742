---
title: "AI providers"
description: "AI provider configuration"
---

import { IntegrationCard } from "../card";
import { OpenAI, Anthropic, Meta, Gemini, Mistral, Together, Fireworks, Perplexity, XAI, Groq, Lepton, Cerebras, Ollama, Replicate, Baseten, Amazon, GoogleCloud, Azure, Databricks } from "#/ui/icons/providers";
import { PlusIcon } from "lucide-react";
import { Callout } from "fumadocs-ui/components/callout";

# AI providers

Configure model and cloud providers to run AI models in playgrounds, experiments, and online scores.

<Callout type="info">
Braintrust supports a wide range of model providers out of the box via the [Braintrust API Proxy](/docs/guides/proxy). This allows you to add custom providers to work with any AI service. Braintrust provides the logging, evaluation, and observability tools you need regardless of which models you choose. Learn more about [custom providers](/docs/providers/custom).
</Callout>

## Proxy AI providers

Braintrust comes with several pre-configured providers that you can use to interact with different AI language models.

<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
  <IntegrationCard href="/docs/providers/openai" title="OpenAI">
    <OpenAI size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/anthropic" title="Anthropic">
    <Anthropic size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/gemini" title="Gemini">
    <Gemini size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/mistral" title="Mistral">
    <Mistral size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/together" title="Together">
    <Together size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/fireworks" title="Fireworks">
    <Fireworks size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/perplexity" title="Perplexity">
    <Perplexity size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/xai" title="xAI">
    <XAI size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/groq" title="Groq">
    <Groq size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/lepton" title="Lepton">
    <Lepton size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/cerebras" title="Cerebras">
    <Cerebras size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/replicate" title="Replicate">
    <Replicate size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/baseten" title="Baseten">
    <Baseten size={64} />
  </IntegrationCard>
  {/* <IntegrationCard href="/docs/providers/meta" title="Meta">
    <Meta size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/providers/ollama" title="Ollama">
    <Ollama size={64} />
  </IntegrationCard> */}
  <IntegrationCard href="/docs/providers/custom" title="Custom">
    <PlusIcon size={64} className="text-primary-400" strokeWidth={1.5} />
  </IntegrationCard>
</div>

## Proxy cloud providers

Braintrust also supports several cloud providers by default.

<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
  <IntegrationCard href="/docs/providers/bedrock" title="AWS Bedrock">
    <Amazon size={64} />
  </IntegrationCard>
   <IntegrationCard href="/docs/providers/google" title="Vertex AI">
    <GoogleCloud size={64} />
  </IntegrationCard>
   <IntegrationCard href="/docs/providers/azure" title="Azure">
    <Azure size={64} />
  </IntegrationCard>
   <IntegrationCard href="/docs/providers/databricks" title="Databricks">
    <Databricks size={64} />
  </IntegrationCard>
</div>
