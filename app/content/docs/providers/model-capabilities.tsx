"use client";

import { modelsByProvider } from "#/ui/prompts/models";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "#/ui/basic-table";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { formatPriceValue } from "#/ui/type-formatters/metrics";
import { CheckCircle2, X } from "lucide-react";

export function ModelCapabilities({ provider }: { provider: string }) {
  const models = modelsByProvider[provider] ?? [];
  const rows = models.flatMap((m) => [
    { ...m, __isChild: false as const },
    ...((m.children ?? []).map((c) => ({ ...c, __isChild: true as const })) ||
      []),
  ]);

  const yesNo = (value?: boolean | null) =>
    value ? (
      <CheckCircle2 className="size-4 text-good-600" />
    ) : (
      <X className="size-4 text-primary-400" />
    );

  const asPerMil = (v?: number | null, perToken?: number | null) => {
    const value = v ?? (perToken != null ? perToken * 1_000_000 : null);
    return value != null ? formatPriceValue(value) : <NullFormatter />;
  };

  return (
    <Table containerClassName="mt-4">
      <TableHeader className="border-0">
        <TableRow className="border-0">
          <TableHead className="w-44">Model</TableHead>
          <TableHead>Multimodal</TableHead>
          <TableHead>Reasoning</TableHead>
          <TableHead>Max input</TableHead>
          <TableHead>Max output</TableHead>
          <TableHead>Input $/1M</TableHead>
          <TableHead>Output $/1M</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {rows.map((m) => (
          <TableRow key={m.modelName} className="border-0 tabular-nums">
            <TableCell className="w-44 font-mono break-all">
              {m.modelName}
            </TableCell>
            <TableCell>{yesNo(m.multimodal)}</TableCell>
            <TableCell>{yesNo(m.reasoning)}</TableCell>
            <TableCell>
              {m.max_input_tokens != null ? (
                m.max_input_tokens.toLocaleString()
              ) : (
                <NullFormatter />
              )}
            </TableCell>
            <TableCell>
              {m.max_output_tokens != null ? (
                m.max_output_tokens.toLocaleString()
              ) : (
                <NullFormatter />
              )}
            </TableCell>
            <TableCell>
              {asPerMil(m.input_cost_per_mil_tokens, m.input_cost_per_token)}
            </TableCell>
            <TableCell>
              {asPerMil(m.output_cost_per_mil_tokens, m.output_cost_per_token)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
