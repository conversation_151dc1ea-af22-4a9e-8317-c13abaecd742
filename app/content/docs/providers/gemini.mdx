---
title: "Gemini"
description: "Google Gemini model provider configuration and integration guide"
---

import { ModelCapabilities } from "./model-capabilities";
import { <PERSON>Tabs, TSTab, PYTab, CurlTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";
import ModuleInstaller from "#/ui/docs/module-installer";

# Gemini

Google's Gemini models include Gemini 2.0 Flash, Gemini 2.5 Pro, and other advanced multimodal language models. Braintrust integrates seamlessly with Gemini through direct API access, wrapper functions for automatic tracing, and proxy support.

## Setup

To use Gemini models, configure your Gemini API key in Braintrust.

1. Get a Gemini API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Add the Gemini API key to your organization's [AI providers](/app/settings/secrets)
3. Set the Gemini API key and your Braintrust API key as environment variables

```bash title=".env"
GEMINI_API_KEY=<your-gemini-api-key>
BRAINTRUST_API_KEY=<your-braintrust-api-key>

# If you are self-hosting Braintrust, set the URL of your hosted dataplane
# BRAINTRUST_API_URL=<your-braintrust-api-url-here>
```

<Callout type="info">
API keys are encrypted using 256-bit AES-GCM encryption and are not stored or logged by Braintrust.
</Callout>

## Use Gemini with Braintrust AI proxy

The [Braintrust AI Proxy](/docs/guides/proxy) allows you to access Gemini models through a unified OpenAI-compatible interface.

Install the `braintrust` and `openai` packages.

<ModuleInstaller packageNames="braintrust openai" />

Then, initialize the client and make a request to a Gemini model via the Braintrust AI Proxy.

<CodeTabs>
<TSTab>
```typescript title="gemini_proxy.ts" #skip-compile
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const response = await client.chat.completions.create({
  model: "gemini-2.0-flash",
  messages: [{ role: "user", content: "Hello, world!" }],
});
```
</TSTab>
<PYTab>
```python title="gemini_proxy.py" #skip-compile
import os

from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="gemini-2.0-flash",
    messages=[{"role": "user", "content": "Hello, world!"}],
)
```
</PYTab>
</CodeTabs>

## Trace logs with Gemini

[Trace](/docs/guides/traces) your Gemini LLM calls for observability and monitoring.

When using the Braintrust AI Proxy, API calls are automatically logged to the specified project.

<CodeTabs>
<TSTab>
```typescript title="gemini_trace.ts" #skip-compile
import { OpenAI } from "openai";
import { initLogger } from "braintrust";

initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// All API calls are automatically logged
const result = await client.chat.completions.create({
  model: "gemini-2.0-flash",
  messages: [{ role: "user", content: "What is machine learning?" }],
});
```
</TSTab>
<PYTab>
```python title="gemini_trace.py" #skip-compile
import os

from braintrust import init_logger
from openai import OpenAI

init_logger(project="My Project")

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

# All API calls are automatically logged
result = client.chat.completions.create(
    model="gemini-2.0-flash",
    messages=[{"role": "user", "content": "What is machine learning?"}],
)
```
</PYTab>
</CodeTabs>

<Callout type="info">
The Braintrust AI Proxy is not required to trace Gemini API calls. For more control, learn how to [customize traces](/docs/guides/traces/customize).
</Callout>

### Stream Gemini responses

Gemini models support streaming:

<CodeTabs>
<TSTab>
```typescript title="gemini_stream.ts" #skip-compile
const stream = await client.chat.completions.create({
  model: "gemini-2.0-flash",
  messages: [{ role: "user", content: "Count to 10" }],
  stream: true,
});

for await (const chunk of stream) {
  process.stdout.write(chunk.choices[0]?.delta?.content || "");
}
```
</TSTab>
<PYTab>
```python title="gemini_stream.py" #skip-compile
stream = client.chat.completions.create(
    model="gemini-2.0-flash",
    messages=[{"role": "user", "content": "Count to 10"}],
    stream=True,
)

for chunk in stream:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```
</PYTab>
</CodeTabs>

## Evaluate with Gemini

Evaluations distill the non-deterministic outputs of Gemini models into an effective feedback loop that enables you to ship more reliable, higher quality products. Braintrust `Eval` is a simple function composed of a dataset of user inputs, a task, and a set of scorers. To learn more about evaluations, see the [Experiments](/docs/guides/experiments) guide.

<CodeTabs>
<TSTab>
```typescript title="gemini_eval.ts" #skip-compile
import { Eval } from "braintrust";
import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

Eval("Gemini Evaluation", {
  data: () => [
    { input: "What is 2+2?", expected: "4" },
    { input: "What is the capital of France?", expected: "Paris" },
  ],
  task: async (input) => {
    const response = await client.chat.completions.create({
      model: "gemini-2.0-flash",
      messages: [{ role: "user", content: input }],
    });
    return response.choices[0].message.content;
  },
  scores: [
    {
      name: "accuracy",
      scorer: (args) => (args.output === args.expected ? 1 : 0),
    },
  ],
});
```
</TSTab>
<PYTab>
```python title="gemini_eval.py" #skip-compile
import os

from braintrust import Eval
from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)


def task(input):
    response = client.chat.completions.create(
        model="gemini-2.0-flash",
        messages=[{"role": "user", "content": input}],
    )
    return response.choices[0].message.content


def accuracy_scorer(output, expected, **kwargs):
    return 1 if output == expected else 0


Eval(
    "Gemini Evaluation",
    data=[
        {"input": "What is 2+2?", "expected": "4"},
        {"input": "What is the capital of France?", "expected": "Paris"},
    ],
    task=task,
    scores=[accuracy_scorer],
)
```
</PYTab>
</CodeTabs>

## Gemini reasoning model support

Gemini reasoning models support unified reasoning parameters.

<CodeTabs>
<TSTab>
```typescript title="gemini_reasoning.ts" #skip-compile
import { OpenAI } from "openai";
import "@braintrust/proxy/types"; // for type safety

const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const response = await client.chat.completions.create({
  model: "gemini-2.5-flash-preview-05-20",
  reasoning_enabled: true,
  reasoning_budget: 1024,
  messages: [{ role: "user", content: "How many rs in 'ferrocarril'?" }],
});

console.log(response.choices[0].reasoning); // Access reasoning steps
```
</TSTab>
<PYTab>
```python title="gemini_reasoning.py" #skip-compile
import os

from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.environ["BRAINTRUST_API_KEY"],
)

response = client.chat.completions.create(
    model="gemini-2.5-flash-preview-05-20",
    reasoning_enabled=True,
    reasoning_budget=1024,
    messages=[{"role": "user", "content": "How many rs in 'ferrocarril'?"}],
)

print(response.choices[0].reasoning)  # Access reasoning steps
```
</PYTab>
</CodeTabs>

<Callout type="info">
To learn more about tool use, multimodal support, attachments, and masking sensitive data with Gemini, visit the [customize traces](/docs/guides/traces/customize) guide.
</Callout>


## Models and capabilities

<ModelCapabilities provider="google" />
