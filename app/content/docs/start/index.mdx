---
title: "Get started"
---

# Get started with Braintrust

Braintrust is an end-to-end platform for building AI applications. It makes software development with large language models (LLMs) robust and iterative.

<div className="flex flex-col text-balance">
  <div className="flex md:gap-6 flex-col md:flex-row">
    <div className="flex-1">
      ### Iterative experimentation
      Rapidly prototype with different prompts<br />and models in the [playground](/docs/guides/playground)
    </div>
    <div className="flex-1">
      ### Performance insights
      Built-in tools to [evaluate](/docs/guides/evals) how models and prompts are performing in production, and dig into specific examples
    </div>
  </div>

  <div className="flex md:gap-6 flex-col md:flex-row">
    <div className="flex-1">
      ### Real-time monitoring
      [Log](/docs/guides/logging), monitor, and take action on real-world interactions with robust and flexible monitoring
    </div>
    <div className="flex-1">
     ### Data management
     [Manage](/docs/guides/datasets) and [review](/docs/guides/human-review) data to store and version<br />your test sets centrally
    </div>
  </div>
</div>


![Developer workflow](./developer-workflow.png)

What makes Braintrust powerful is how these tools work together. With Braintrust, developers can move faster, run more experiments, and ultimately build better AI products.
