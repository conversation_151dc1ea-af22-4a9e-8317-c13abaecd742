---
title: "Eval via UI"
---

import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Callout } from "fumadocs-ui/components/callout";
import Link from "fumadocs-core/link";

# Evaluate via UI

The following steps require access to a Braintrust organization, which represents a company or a team. [Sign up](https://www.braintrust.dev/signup) to create an organization for free.

<Steps>
<Step>
### Configure your API keys

Navigate to the [AI providers](/app/settings?subroute=secrets) page in your settings and configure at least one API key. For this quickstart, be sure to add your OpenAI API key. After completing this initial setup, you can access models from many providers through a single, unified API.

<Callout>
For more advanced use cases where you want to use custom models or avoid plugging your API key into Braintrust, you may want to check out the [SDK](/docs/start/eval-sdk) quickstart.
</Callout>

</Step>

<Step>
### Create a new project

For every AI feature your organization is building, the first thing you'll do is create a project.
</Step>

<Step>
### Create a new prompt

Navigate to **Library** in the top menu bar, then select **Prompts**. Create a new prompt in your project called "movie matcher". A prompt is the input you provide to the model to generate a response. Choose `GPT 4o` for your model, and type this for your system prompt:

```
Based on the following description, identify the movie title. In your response, provide the name of the movie.
```

Select the **\+ Message** button below the system prompt, and enter a user message:

```
{{input}}
```

Prompts can use [mustache](https://mustache.github.io/mustache.5.html) templating syntax to refer to variables. In this case, the input corresponds to the movie description given by the user.

![First prompt](./movie-matcher-prompt.png)

Select **Save as custom prompt** to save your prompt.
</Step>

<Step>
### Explore the prompt playground

Scroll to the bottom of the prompt viewer, and select **Create playground with prompt**. This will open the prompt you just created in the [prompt playground](https://www.braintrust.dev/docs/guides/playground), a tool for exploring, comparing, and evaluating prompts. In the prompt playground, you can evaluate prompts with data from your [datasets](https://www.braintrust.dev/docs/guides/datasets).

![Prompt playground](./prompt-playground.png)
</Step>

<Step>
### Importing a dataset

Open this [sample dataset](https://gist.githubusercontent.com/ornellaaltunyan/28972d2566ddf64bc171922d0f0564e2/raw/838d220eea620a2390427fe1ec35d347f2b798bd/gistfile1.csv), and right-click to select **Save as...** and download it. It is a `.csv` file with two columns, **Movie Title** and **Original Description**. Inside your playground, select **Dataset**, then **Upload dataset**, and upload the CSV file. Using drag and drop, assign the CSV columns to dataset fields. The input column corresponds to Original Description, and the expected column should be Movie Title. Then, select **Import**.

![Upload dataset](./upload-dataset.png)

</Step>

<Step>
### Choosing a scorer

A scoring function allows you to compare the expected output of a task to the actual output and produce a score between 0 and 1. Inside your playground, select **Scorers** to choose from several types of scoring functions. There are two main types of scoring functions: heuristics are great for well-defined criteria, while LLM-as-a-judge is better for handling more complex, subjective evaluations. You can also create a custom scorer. For this example, since there is a clear correct answer, we can choose **ExactMatch**.

</Step>

<Step>
### Running your first evaluation

From within the playground, select **+ Experiment** to set up your first evaluation. To run an eval, you need three things:

- **Data**: a set of examples to test your application on
- **Task**: the AI function you want to test (any function that takes in an input and returns an output)
- **Scores**: a set of scoring functions that take an input, output, and optional expected value and compute a score

In this example, the Data is the dataset you uploaded, the Task is the prompt you created, and Scores is the scoring function we selected.

![Create experiment](./create-experiment.png)

Creating an experiment from the playground will automatically log your results to Braintrust.
</Step>

<Step>
### Interpreting your results

Navigate to the **Experiments** page to view your evaluation. Examine the exact match scores and other feedback generated by your evals. If you notice that some of your outputs did not match what was expected, you can tweak your prompt directly in the UI until it consistently produces high-quality outputs. If changing the prompt doesn't yield the desired results, consider experimenting with different models.
![Experiment](./experiment.png)

As you iterate on your prompt, you can run more experiments and compare results.

</Step>

</Steps>

## Next steps

- Now that you've run your first evaluation, learn how to [write your own eval script](/docs/start/eval-sdk).
- Check out more examples and sample projects in the [Braintrust Cookbook](/docs/cookbook).
- Explore the [guides](/docs/guides) to read more about evals, logging, and datasets.
