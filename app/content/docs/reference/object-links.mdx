---
title: Sharing via URL
---

import { Step, Steps } from "fumadocs-ui/components/steps";

# Sharing via URL

Braintrust supports a convenient method for generating permanent links (permalinks) to experiment and dataset objects in your workspace by using their unique object IDs. This allows you to share, bookmark, or automate navigation to specific resources.

## How it works

When you access a URL in this format, Braintrust will automatically redirect you to the canonical page for the object, regardless of its organization or project:

```
https://www.braintrust.dev/app/braintrustdata.com/object?object_type=<object_type>&object_id=<object_id>
```

- **object_type**: The type of object (`experiment` or `dataset`).
- **object_id**: The unique identifier for the object.

For example, if you have an experiment with the ID `dc877d29-fc32-438f-bd62-169967c817f0`, use:

```
https://www.braintrust.dev/app/braintrustdata.com/object?object_type=experiment&object_id=dc877d29-fc32-438f-bd62-169967c817f0
```

### Redirection behavior

When you visit the above URL, you will be automatically redirected to the standard page for the object, following the canonical URL structure for that object type:

```
https://www.braintrust.dev/app/<your org>/p/<your project>/<object_collection>/<object_slug>
```

For the experiment example above, the redirect might look like:

```
https://www.braintrust.dev/app/braintrustdata.com/p/pedro-project1/experiments/dc877d29-fc32-438f-bd62-169967c817f0
```

### Supported object types

This method works for any object type that has a unique ID. Set the `object_type` parameter to one of the supported types:

- `experiment`
- `dataset`

## Generating links via SDK

The SDK also supports methods for generating permalinks in both [TypeScript](/docs/reference/libs/nodejs#permalink) and [Python](https://www.braintrust.dev/docs/reference/libs/python#permalink).

## Use cases
You can use object permalinks for:
- Bookmarking important experiments or datasets
- Automating navigation in documentation or scripts
- Sharing direct links with collaborators
