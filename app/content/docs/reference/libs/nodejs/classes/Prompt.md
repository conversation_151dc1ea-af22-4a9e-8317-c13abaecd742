---
title: Prompt
---

# Class: Prompt\<HasId, <PERSON><PERSON><PERSON><PERSON>\>

## Type parameters

| Name         | Type                       |
| :----------- | :------------------------- |
| `HasId`      | extends `boolean` = `true` |
| `HasVersion` | extends `boolean` = `true` |

## Accessors

### id

• `get` **id**(): `HasId` extends `true` ? `string` : `undefined` \| `string`

#### Returns

`HasId` extends `true` ? `string` : `undefined` \| `string`

---

### name

• `get` **name**(): `string`

#### Returns

`string`

---

### options

• `get` **options**(): `Object`

#### Returns

`Object`

| Name        | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| :---------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `model?`    | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| `params?`   | `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> |
| `position?` | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

---

### projectId

• `get` **projectId**(): `undefined` \| `string`

#### Returns

`undefined` \| `string`

---

### prompt

• `get` **prompt**(): `undefined` \| `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }

#### Returns

`undefined` \| `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }

---

### promptData

• `get` **promptData**(): `Object`

#### Returns

`Object`

| Name              | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| :---------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `options?`        | `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } |
| `origin?`         | `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `parser?`         | `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| `prompt?`         | `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `tool_functions?` | `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |

---

### slug

• `get` **slug**(): `string`

#### Returns

`string`

---

### version

• `get` **version**(): `HasId` extends `true` ? `string` : `undefined` \| `string`

#### Returns

`HasId` extends `true` ? `string` : `undefined` \| `string`

## Constructors

### constructor

• **new Prompt**\<`HasId`, `HasVersion`\>(`metadata`, `defaults`, `noTrace`): [`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`HasId`, `HasVersion`\>

#### Type parameters

| Name         | Type                       |
| :----------- | :------------------------- |
| `HasId`      | extends `boolean` = `true` |
| `HasVersion` | extends `boolean` = `true` |

#### Parameters

| Name       | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :--------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `metadata` | [`PromptRowWithId`](/docs/libs/nodejs#promptrowwithid)\<`HasId`, `HasVersion`\> \| \{ `_pagination_key?`: `null` \| `string` ; `_xact_id`: `string` ; `completion?`: `unknown` ; `created`: `string` ; `function_data?`: `unknown` ; `id`: `string` ; `object_data?`: `unknown` ; `project_id`: `string` ; `prompt_data?`: `unknown` ; `prompt_session_data?`: `unknown` ; `prompt_session_id`: `string` ; `tags?`: `null` \| `string`[] }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| `defaults` | `Partial`\<`Omit`\<`undefined` \| `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `"use_cache"`\> & \{ `model`: `string` } & \{ `frequency_penalty?`: `number` ; `function_call?`: `"auto"` \| `"none"` \| \{ `name`: `string` } ; `maxOutputTokens?`: `number` ; `max_completion_tokens?`: `number` ; `max_tokens`: `number` ; `max_tokens_to_sample?`: `number` ; `n?`: `number` ; `presence_penalty?`: `number` ; `reasoning_effort?`: `"high"` \| `"low"` \| `"medium"` ; `response_format?`: `null` \| \{ `type`: `"json_object"` } \| \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` } \| \{ `type`: `"text"` } ; `stop?`: `string`[] ; `stop_sequences?`: `string`[] ; `temperature?`: `number` ; `tool_choice?`: `"auto"` \| `"none"` \| `"required"` \| \{ `function`: \{ `name`: `string` } ; `type`: `"function"` } ; `topK?`: `number` ; `topP?`: `number` ; `top_k?`: `number` ; `top_p?`: `number` ; `use_cache?`: `boolean` } & [`ChatPrompt`](/docs/libs/nodejs#chatprompt) & [`CompletionPrompt`](/docs/libs/nodejs#completionprompt)\> |
| `noTrace`  | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |

#### Returns

[`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`HasId`, `HasVersion`\>

## Methods

### build

▸ **build**\<`Flavor`\>(`buildArgs`, `options?`): [`CompiledPrompt`](/docs/libs/nodejs#compiledprompt)\<`Flavor`\>

Build the prompt with the given formatting options. The args you pass in will
be forwarded to the mustache template that defines the prompt and rendered with
the `mustache-js` library.

#### Type parameters

| Name     | Type                                          |
| :------- | :-------------------------------------------- |
| `Flavor` | extends `"completion"` \| `"chat"` = `"chat"` |

#### Parameters

| Name                | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | Description                                   |
| :------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :-------------------------------------------- |
| `buildArgs`         | `unknown`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | Args to forward along to the prompt template. |
| `options`           | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | -                                             |
| `options.flavor?`   | `Flavor`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | -                                             |
| `options.messages?` | (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] | -                                             |
| `options.strict?`   | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | -                                             |

#### Returns

[`CompiledPrompt`](/docs/libs/nodejs#compiledprompt)\<`Flavor`\>

---

### fromPromptData

▸ **fromPromptData**(`name`, `promptData`): [`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`false`, `false`\>

#### Parameters

| Name                         | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| :--------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `name`                       | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| `promptData`                 | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| `promptData.options?`        | `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } |
| `promptData.origin?`         | `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `promptData.parser?`         | `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| `promptData.prompt?`         | `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `promptData.tool_functions?` | `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |

#### Returns

[`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`false`, `false`\>

---

### isPrompt

▸ **isPrompt**(`data`): data is Prompt\<boolean, boolean\>

#### Parameters

| Name   | Type      |
| :----- | :-------- |
| `data` | `unknown` |

#### Returns

data is Prompt\<boolean, boolean\>

---

### renderPrompt

▸ **renderPrompt**(`«destructured»`): \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }

#### Parameters

| Name                  | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| :-------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `«destructured»`      | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| › `buildArgs`         | `unknown`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| › `options`           | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| › `options.messages?` | (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[]                                                                                                               |
| › `options.strict?`   | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| › `prompt`            | \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } |

#### Returns

\{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }
