---
title: TestBackgroundLogger
---

# Class: TestBackgroundLogger

## Implements

- `BackgroundLogger`

## Constructors

### constructor

• **new TestBackgroundLogger**(): [`TestBackgroundLogger`](/docs/libs/nodejs/classes/TestBackgroundLogger.md)

#### Returns

[`TestBackgroundLogger`](/docs/libs/nodejs/classes/TestBackgroundLogger.md)

## Methods

### drain

▸ **drain**(): `Promise`\<`BackgroundLogEvent`[]\>

#### Returns

`Promise`\<`BackgroundLogEvent`[]\>

---

### flush

▸ **flush**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>

#### Implementation of

BackgroundLogger.flush

---

### log

▸ **log**(`items`): `void`

#### Parameters

| Name    | Type                                                                            |
| :------ | :------------------------------------------------------------------------------ |
| `items` | [`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`BackgroundLogEvent`\>[] |

#### Returns

`void`

#### Implementation of

BackgroundLogger.log
