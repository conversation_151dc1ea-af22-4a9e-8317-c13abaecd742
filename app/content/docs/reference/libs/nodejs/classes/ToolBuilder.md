---
title: Tool<PERSON>uilder
---

# Class: ToolBuilder

## Constructors

### constructor

• **new ToolBuilder**(`project`): [`ToolBuilder`](/docs/libs/nodejs/classes/ToolBuilder.md)

#### Parameters

| Name      | Type                                              |
| :-------- | :------------------------------------------------ |
| `project` | [`Project`](/docs/libs/nodejs/classes/Project.md) |

#### Returns

[`ToolBuilder`](/docs/libs/nodejs/classes/ToolBuilder.md)

## Methods

### create

▸ **create**\<`Input`, `Output`, `Fn`\>(`opts`): [`CodeFunction`](/docs/libs/nodejs/classes/CodeFunction.md)\<`Input`, `Output`, `Fn`\>

#### Type parameters

| Name     | Type                                           |
| :------- | :--------------------------------------------- |
| `Input`  | `Input`                                        |
| `Output` | `Output`                                       |
| `Fn`     | extends `GenericFunction`\<`Input`, `Output`\> |

#### Parameters

| Name   | Type                                                                |
| :----- | :------------------------------------------------------------------ |
| `opts` | [`CodeOpts`](/docs/libs/nodejs#codeopts)\<`Input`, `Output`, `Fn`\> |

#### Returns

[`CodeFunction`](/docs/libs/nodejs/classes/CodeFunction.md)\<`Input`, `Output`, `Fn`\>
