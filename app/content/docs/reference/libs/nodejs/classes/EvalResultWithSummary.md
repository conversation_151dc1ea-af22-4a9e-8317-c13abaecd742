---
title: Eval<PERSON><PERSON>ultWithSummary
---

# Class: EvalResultWithSummary\<Input, Output, Expected, Metadata\>

## Type parameters

| Name       | Type                                                                                                                      |
| :--------- | :------------------------------------------------------------------------------------------------------------------------ |
| `Input`    | `Input`                                                                                                                   |
| `Output`   | `Output`                                                                                                                  |
| `Expected` | `Expected`                                                                                                                |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = [`DefaultMetadataType`](/docs/libs/nodejs#defaultmetadatatype) |

## Constructors

### constructor

• **new EvalResultWithSummary**\<`Input`, `Output`, `Expected`, `Metadata`\>(`summary`, `results`): [`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`Input`, `Output`, `Expected`, `Metadata`\>

#### Type parameters

| Name       | Type                                                              |
| :--------- | :---------------------------------------------------------------- |
| `Input`    | `Input`                                                           |
| `Output`   | `Output`                                                          |
| `Expected` | `Expected`                                                        |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = `void` |

#### Parameters

| Name      | Type                                                                                        |
| :-------- | :------------------------------------------------------------------------------------------ |
| `summary` | [`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)                    |
| `results` | [`EvalResult`](/docs/libs/nodejs#evalresult)\<`Input`, `Output`, `Expected`, `Metadata`\>[] |

#### Returns

[`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`Input`, `Output`, `Expected`, `Metadata`\>

## Methods

### toJSON

▸ **toJSON**(): `Object`

#### Returns

`Object`

| Name      | Type                                                                                        |
| :-------- | :------------------------------------------------------------------------------------------ |
| `results` | [`EvalResult`](/docs/libs/nodejs#evalresult)\<`Input`, `Output`, `Expected`, `Metadata`\>[] |
| `summary` | [`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)                    |

---

### toString

▸ **toString**(): `string`

#### Returns

`string`

## Properties

### results

• **results**: [`EvalResult`](/docs/libs/nodejs#evalresult)\<`Input`, `Output`, `Expected`, `Metadata`\>[]

---

### summary

• **summary**: [`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)
