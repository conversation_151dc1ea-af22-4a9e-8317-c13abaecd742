---
title: BraintrustStream
---

# Class: BraintrustStream

A Braintrust stream. This is a wrapper around a ReadableStream of `BraintrustStreamChunk`,
with some utility methods to make them easy to log and convert into various formats.

## Constructors

### constructor

• **new BraintrustStream**(`baseStream`, `opts?`): [`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

#### Parameters

| Name           | Type                             |
| :------------- | :------------------------------- |
| `baseStream`   | `ReadableStream`\<`Uint8Array`\> |
| `opts?`        | `Object`                         |
| `opts.signal?` | `AbortSignal`                    |

#### Returns

[`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

• **new BraintrustStream**(`stream`, `opts?`): [`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

#### Parameters

| Name           | Type                         |
| :------------- | :--------------------------- |
| `stream`       | `ReadableStream`\<`string`\> |
| `opts?`        | `Object`                     |
| `opts.signal?` | `AbortSignal`                |

#### Returns

[`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

• **new BraintrustStream**(`stream`, `opts?`): [`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

#### Parameters

| Name           | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `stream`       | `ReadableStream`\<\{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }\> |
| `opts?`        | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `opts.signal?` | `AbortSignal`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |

#### Returns

[`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

## Methods

### [asyncIterator]

▸ **[asyncIterator]**(): `AsyncIterator`\<\{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }, `any`, `undefined`\>

Returns an async iterator for the BraintrustStream.
This allows for easy consumption of the stream using a for-await...of loop.

#### Returns

`AsyncIterator`\<\{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }, `any`, `undefined`\>

An async iterator that yields BraintrustStreamChunk objects.

---

### copy

▸ **copy**(): [`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

Copy the stream. This returns a new stream that shares the same underlying
stream (via `tee`). Since streams are consumed in Javascript, use `copy()` if you
need to use the stream multiple times.

#### Returns

[`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md)

A new stream that you can independently consume.

---

### finalValue

▸ **finalValue**(): `Promise`\<`unknown`\>

Get the final value of the stream. The final value is the concatenation of all
the chunks in the stream, deserialized into a string or JSON object, depending on
the value's type.

This function returns a promise that resolves when the stream is closed, and
contains the final value. Multiple calls to `finalValue()` will return the same
promise, so it is safe to call this multiple times.

This function consumes the stream, so if you need to use the stream multiple
times, you should call `copy()` first.

#### Returns

`Promise`\<`unknown`\>

A promise that resolves with the final value of the stream or `undefined` if the stream is empty.

---

### toReadableStream

▸ **toReadableStream**(): `ReadableStream`\<\{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }\>

Get the underlying ReadableStream.

#### Returns

`ReadableStream`\<\{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }\>

The underlying ReadableStream<BraintrustStreamChunk>.

---

### parseRawEvent

▸ **parseRawEvent**(`event`): \{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }

#### Parameters

| Name    | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| :------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `event` | \{ `data`: `string` ; `event`: `"text_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"reasoning_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"json_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"progress"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"error"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"console"` ; `id?`: `string` } \| \{ `data`: `""` ; `event`: `"start"` ; `id?`: `string` } \| \{ `data`: `""` ; `event`: `"done"` ; `id?`: `string` } |

#### Returns

\{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` }

---

### serializeRawEvent

▸ **serializeRawEvent**(`event`): \{ `data`: `string` ; `event`: `"text_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"reasoning_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"json_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"progress"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"error"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"console"` ; `id?`: `string` } \| \{ `data`: `""` ; `event`: `"start"` ; `id?`: `string` } \| \{ `data`: `""` ; `event`: `"done"` ; `id?`: `string` }

#### Parameters

| Name    | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| :------ | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `event` | \{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` } |

#### Returns

\{ `data`: `string` ; `event`: `"text_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"reasoning_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"json_delta"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"progress"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"error"` ; `id?`: `string` } \| \{ `data`: `string` ; `event`: `"console"` ; `id?`: `string` } \| \{ `data`: `""` ; `event`: `"start"` ; `id?`: `string` } \| \{ `data`: `""` ; `event`: `"done"` ; `id?`: `string` }
