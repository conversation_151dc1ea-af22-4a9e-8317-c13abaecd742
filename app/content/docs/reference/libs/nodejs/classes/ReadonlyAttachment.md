---
title: ReadonlyAttachment
---

# Class: ReadonlyAttachment

A readonly alternative to `Attachment`, which can be used for fetching
already-uploaded Attachments.

## Constructors

### constructor

• **new ReadonlyAttachment**(`reference`, `state?`): [`ReadonlyAttachment`](/docs/libs/nodejs/classes/ReadonlyAttachment.md)

Construct a ReadonlyAttachment.

#### Parameters

| Name        | Type                                                                                                                                                                                                                   | Description                                                                       |
| :---------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------- |
| `reference` | \{ `content_type`: `string` ; `filename`: `string` ; `key`: `string` ; `type`: `"braintrust_attachment"` } \| \{ `content_type`: `string` ; `filename`: `string` ; `type`: `"external_attachment"` ; `url`: `string` } | The `AttachmentReference` that should be read by the `ReadonlyAttachment` object. |
| `state?`    | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)                                                                                                                                                      | (Optional) For internal use.                                                      |

#### Returns

[`ReadonlyAttachment`](/docs/libs/nodejs/classes/ReadonlyAttachment.md)

The new `ReadonlyAttachment` object.

## Methods

### data

▸ **data**(): `Promise`\<`Blob`\>

The attachment contents. This is a lazy value that will read the attachment
contents from the object store on first access.

#### Returns

`Promise`\<`Blob`\>

---

### metadata

▸ **metadata**(): `Promise`\<\{ `downloadUrl`: `string` ; `status`: \{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` } = attachmentStatusSchema }\>

Fetch the attachment metadata, which includes a downloadUrl and a status.
This will re-fetch the status each time in case it changes over time.

#### Returns

`Promise`\<\{ `downloadUrl`: `string` ; `status`: \{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` } = attachmentStatusSchema }\>

---

### status

▸ **status**(): `Promise`\<\{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` }\>

Fetch the attachment upload status. This will re-fetch the status each time
in case it changes over time.

#### Returns

`Promise`\<\{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` }\>

## Properties

### reference

• `Readonly` **reference**: \{ `content_type`: `string` ; `filename`: `string` ; `key`: `string` ; `type`: `"braintrust_attachment"` } \| \{ `content_type`: `string` ; `filename`: `string` ; `type`: `"external_attachment"` ; `url`: `string` }

Attachment metadata.
