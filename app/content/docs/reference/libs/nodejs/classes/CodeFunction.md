---
title: CodeFunction
---

# Class: CodeFunction\<Input, Output, Fn\>

## Type parameters

| Name     | Type                                           |
| :------- | :--------------------------------------------- |
| `Input`  | `Input`                                        |
| `Output` | `Output`                                       |
| `Fn`     | extends `GenericFunction`\<`Input`, `Output`\> |

## Constructors

### constructor

• **new CodeFunction**\<`Input`, `Output`, `Fn`\>(`project`, `opts`): [`CodeFunction`](/docs/libs/nodejs/classes/CodeFunction.md)\<`Input`, `Output`, `Fn`\>

#### Type parameters

| Name     | Type                                           |
| :------- | :--------------------------------------------- |
| `Input`  | `Input`                                        |
| `Output` | `Output`                                       |
| `Fn`     | extends `GenericFunction`\<`Input`, `Output`\> |

#### Parameters

| Name      | Type                                                                                                                                                                                                   |
| :-------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `project` | [`Project`](/docs/libs/nodejs/classes/Project.md)                                                                                                                                                      |
| `opts`    | `Omit`\<[`CodeOpts`](/docs/libs/nodejs#codeopts)\<`Input`, `Output`, `Fn`\>, `"name"` \| `"slug"`\> & \{ `name`: `string` ; `slug`: `string` ; `type`: `"llm"` \| `"tool"` \| `"scorer"` \| `"task"` } |

#### Returns

[`CodeFunction`](/docs/libs/nodejs/classes/CodeFunction.md)\<`Input`, `Output`, `Fn`\>

## Methods

### key

▸ **key**(): `string`

#### Returns

`string`

## Properties

### description

• `Optional` `Readonly` **description**: `string`

---

### handler

• `Readonly` **handler**: `Fn`

---

### ifExists

• `Optional` `Readonly` **ifExists**: `"error"` \| `"replace"` \| `"ignore"`

---

### name

• `Readonly` **name**: `string`

---

### parameters

• `Optional` `Readonly` **parameters**: `ZodType`\<`Input`, `ZodTypeDef`, `Input`\>

---

### project

• `Readonly` **project**: [`Project`](/docs/libs/nodejs/classes/Project.md)

---

### returns

• `Optional` `Readonly` **returns**: `ZodType`\<`Output`, `ZodTypeDef`, `Output`\>

---

### slug

• `Readonly` **slug**: `string`

---

### type

• `Readonly` **type**: `"llm"` \| `"tool"` \| `"scorer"` \| `"task"`
