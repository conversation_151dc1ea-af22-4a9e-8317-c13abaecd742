---
title: BraintrustState
---

# Class: BraintrustState

## Constructors

### constructor

• **new BraintrustState**(`loginParams`): [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

#### Parameters

| Name          | Type                                                           |
| :------------ | :------------------------------------------------------------- |
| `loginParams` | [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) |

#### Returns

[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

## Methods

### apiConn

▸ **apiConn**(): `HTTPConnection`

#### Returns

`HTTPConnection`

---

### appConn

▸ **appConn**(): `HTTPConnection`

#### Returns

`HTTPConnection`

---

### bgLogger

▸ **bgLogger**(): `BackgroundLogger`

#### Returns

`BackgroundLogger`

---

### copyLoginInfo

▸ **copyLoginInfo**(`other`): `void`

#### Parameters

| Name    | Type                                                              |
| :------ | :---------------------------------------------------------------- |
| `other` | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) |

#### Returns

`void`

---

### disable

▸ **disable**(): `void`

#### Returns

`void`

---

### httpLogger

▸ **httpLogger**(): `HTTPBackgroundLogger`

#### Returns

`HTTPBackgroundLogger`

---

### login

▸ **login**(`loginParams`): `Promise`\<`void`\>

#### Parameters

| Name          | Type                                                                                           |
| :------------ | :--------------------------------------------------------------------------------------------- |
| `loginParams` | [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) & \{ `forceLogin?`: `boolean` } |

#### Returns

`Promise`\<`void`\>

---

### loginReplaceApiConn

▸ **loginReplaceApiConn**(`apiConn`): `void`

#### Parameters

| Name      | Type             |
| :-------- | :--------------- |
| `apiConn` | `HTTPConnection` |

#### Returns

`void`

---

### proxyConn

▸ **proxyConn**(): `HTTPConnection`

#### Returns

`HTTPConnection`

---

### resetLoginInfo

▸ **resetLoginInfo**(): `void`

#### Returns

`void`

---

### serialize

▸ **serialize**(): `Object`

#### Returns

`Object`

| Name                   | Type                                                                                                                                                                                                                        |
| :--------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `apiUrl`               | `string`                                                                                                                                                                                                                    |
| `appPublicUrl`         | `string`                                                                                                                                                                                                                    |
| `appUrl`               | `string`                                                                                                                                                                                                                    |
| `gitMetadataSettings?` | `null` \| \{ `collect`: `"some"` \| `"none"` \| `"all"` ; `fields?`: (`"dirty"` \| `"tag"` \| `"commit"` \| `"branch"` \| `"author_name"` \| `"author_email"` \| `"commit_message"` \| `"commit_time"` \| `"git_diff"`)[] } |
| `loginToken`           | `string`                                                                                                                                                                                                                    |
| `orgId?`               | `null` \| `string`                                                                                                                                                                                                          |
| `orgName`              | `string`                                                                                                                                                                                                                    |
| `proxyUrl`             | `string`                                                                                                                                                                                                                    |

---

### setFetch

▸ **setFetch**(`fetch`): `void`

#### Parameters

| Name    | Type                                                                                                                                                                             |
| :------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `fetch` | (`input`: `URL` \| `RequestInfo`, `init?`: `RequestInit`) => `Promise`\<`Response`\>(`input`: `string` \| `URL` \| `Request`, `init?`: `RequestInit`) => `Promise`\<`Response`\> |

#### Returns

`void`

---

### setOverrideBgLogger

▸ **setOverrideBgLogger**(`logger`): `void`

#### Parameters

| Name     | Type                         |
| :------- | :--------------------------- |
| `logger` | `null` \| `BackgroundLogger` |

#### Returns

`void`

---

### deserialize

▸ **deserialize**(`serialized`, `opts?`): [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

#### Parameters

| Name         | Type                                                           |
| :----------- | :------------------------------------------------------------- |
| `serialized` | `unknown`                                                      |
| `opts?`      | [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) |

#### Returns

[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

## Properties

### apiUrl

• **apiUrl**: `null` \| `string` = `null`

---

### appPublicUrl

• **appPublicUrl**: `null` \| `string` = `null`

---

### appUrl

• **appUrl**: `null` \| `string` = `null`

---

### currentExperiment

• **currentExperiment**: `undefined` \| [`Experiment`](/docs/libs/nodejs/classes/Experiment.md)

---

### currentLogger

• **currentLogger**: `undefined` \| [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`false`\>

---

### currentParent

• **currentParent**: `IsoAsyncLocalStorage`\<`string`\>

---

### currentSpan

• **currentSpan**: `IsoAsyncLocalStorage`\<[`Span`](/docs/libs/nodejs/interfaces/Span.md)\>

---

### fetch

• **fetch**: (`input`: `URL` \| `RequestInfo`, `init?`: `RequestInit`) => `Promise`\<`Response`\>(`input`: `string` \| `URL` \| `Request`, `init?`: `RequestInit`) => `Promise`\<`Response`\> = `globalThis.fetch`

#### Type declaration

▸ (`input`, `init?`): `Promise`\<`Response`\>

[MDN Reference](https://developer.mozilla.org/docs/Web/API/fetch)

##### Parameters

| Name    | Type                   |
| :------ | :--------------------- |
| `input` | `URL` \| `RequestInfo` |
| `init?` | `RequestInit`          |

##### Returns

`Promise`\<`Response`\>

▸ (`input`, `init?`): `Promise`\<`Response`\>

##### Parameters

| Name    | Type                           |
| :------ | :----------------------------- |
| `input` | `string` \| `URL` \| `Request` |
| `init?` | `RequestInit`                  |

##### Returns

`Promise`\<`Response`\>

---

### gitMetadataSettings

• `Optional` **gitMetadataSettings**: `Object`

#### Type declaration

| Name      | Type                                                                                                                                                 |
| :-------- | :--------------------------------------------------------------------------------------------------------------------------------------------------- |
| `collect` | `"some"` \| `"none"` \| `"all"`                                                                                                                      |
| `fields?` | (`"dirty"` \| `"tag"` \| `"commit"` \| `"branch"` \| `"author_name"` \| `"author_email"` \| `"commit_message"` \| `"commit_time"` \| `"git_diff"`)[] |

---

### id

• **id**: `string`

---

### loggedIn

• **loggedIn**: `boolean` = `false`

---

### loginToken

• **loginToken**: `null` \| `string` = `null`

---

### orgId

• **orgId**: `null` \| `string` = `null`

---

### orgName

• **orgName**: `null` \| `string` = `null`

---

### promptCache

• **promptCache**: `PromptCache`

---

### proxyUrl

• **proxyUrl**: `null` \| `string` = `null`
