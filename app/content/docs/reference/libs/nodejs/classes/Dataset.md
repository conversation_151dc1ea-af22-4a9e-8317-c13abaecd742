---
title: Dataset
---

# Class: Dataset\<IsLegacyDataset\>

A dataset is a collection of records, such as model inputs and expected outputs, which represent
data you can use to evaluate and fine-tune models. You can log production data to datasets,
curate them with interesting examples, edit/delete records, and run evaluations against them.

You should not create `Dataset` objects directly. Instead, use the `braintrust.initDataset()` method.

## Type parameters

| Name              | Type                                                   |
| :---------------- | :----------------------------------------------------- |
| `IsLegacyDataset` | extends `boolean` = typeof `DEFAULT_IS_LEGACY_DATASET` |

## Hierarchy

- `ObjectFetcher`\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>

  ↳ **`Dataset`**

## Accessors

### id

• `get` **id**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

#### Overrides

ObjectFetcher.id

---

### name

• `get` **name**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

---

### project

• `get` **project**(): `Promise`\<[`ObjectMetadata`](/docs/libs/nodejs/interfaces/ObjectMetadata.md)\>

#### Returns

`Promise`\<[`ObjectMetadata`](/docs/libs/nodejs/interfaces/ObjectMetadata.md)\>

## Constructors

### constructor

• **new Dataset**\<`IsLegacyDataset`\>(`state`, `lazyMetadata`, `pinnedVersion?`, `legacy?`, `_internal_btql?`): [`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>

#### Type parameters

| Name              | Type                        |
| :---------------- | :-------------------------- |
| `IsLegacyDataset` | extends `boolean` = `false` |

#### Parameters

| Name              | Type                                                                              |
| :---------------- | :-------------------------------------------------------------------------------- |
| `state`           | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)                 |
| `lazyMetadata`    | [`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`ProjectDatasetMetadata`\> |
| `pinnedVersion?`  | `string`                                                                          |
| `legacy?`         | `IsLegacyDataset`                                                                 |
| `_internal_btql?` | `Record`\<`string`, `unknown`\>                                                   |

#### Returns

[`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>

#### Overrides

ObjectFetcher\&lt;DatasetRecord\&lt;IsLegacyDataset\&gt;\&gt;.constructor

## Methods

### [asyncIterator]

▸ **[asyncIterator]**(): `AsyncIterator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>, `any`, `undefined`\>

#### Returns

`AsyncIterator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>, `any`, `undefined`\>

#### Inherited from

ObjectFetcher.[asyncIterator]

---

### clearCache

▸ **clearCache**(): `void`

#### Returns

`void`

#### Inherited from

ObjectFetcher.clearCache

---

### close

▸ **close**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

**`Deprecated`**

This function is deprecated. You can simply remove it from your code.

---

### delete

▸ **delete**(`id`): `string`

#### Parameters

| Name | Type     |
| :--- | :------- |
| `id` | `string` |

#### Returns

`string`

---

### fetch

▸ **fetch**(): `AsyncGenerator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>, `any`, `unknown`\>

#### Returns

`AsyncGenerator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>, `any`, `unknown`\>

#### Inherited from

ObjectFetcher.fetch

---

### fetchedData

▸ **fetchedData**(): `Promise`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>[]\>

#### Returns

`Promise`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<[`DatasetRecord`](/docs/libs/nodejs#datasetrecord)\<`IsLegacyDataset`\>\>[]\>

#### Inherited from

ObjectFetcher.fetchedData

---

### flush

▸ **flush**(): `Promise`\<`void`\>

Flush any pending rows to the server.

#### Returns

`Promise`\<`void`\>

---

### getState

▸ **getState**(): `Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

#### Returns

`Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

#### Overrides

ObjectFetcher.getState

---

### insert

▸ **insert**(`event`): `string`

Insert a single record to the dataset. The record will be batched and uploaded behind the scenes. If you pass in an `id`,
and a record with that `id` already exists, it will be overwritten (upsert).

#### Parameters

| Name              | Type                            | Description                                                                                                                                                                                                                                                                                                                                                                                              |
| :---------------- | :------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `event`           | `Object`                        | The event to log.                                                                                                                                                                                                                                                                                                                                                                                        |
| `event.expected?` | `unknown`                       | The output of your application, including post-processing (an arbitrary, JSON serializable object).                                                                                                                                                                                                                                                                                                      |
| `event.id?`       | `string`                        | (Optional) a unique identifier for the event. If you don't provide one, Braintrust will generate one for you.                                                                                                                                                                                                                                                                                            |
| `event.input?`    | `unknown`                       | The argument that uniquely define an input case (an arbitrary, JSON serializable object).                                                                                                                                                                                                                                                                                                                |
| `event.metadata?` | `Record`\<`string`, `unknown`\> | (Optional) a dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings. |
| `event.output?`   | `unknown`                       | -                                                                                                                                                                                                                                                                                                                                                                                                        |
| `event.tags?`     | `string`[]                      | (Optional) a list of strings that you can use to filter and group records later.                                                                                                                                                                                                                                                                                                                         |

#### Returns

`string`

The `id` of the logged record.

---

### summarize

▸ **summarize**(`options?`): `Promise`\<[`DatasetSummary`](/docs/libs/nodejs/interfaces/DatasetSummary.md)\>

Summarize the dataset, including high level metrics about its size and other metadata.

#### Parameters

| Name                     | Type      |
| :----------------------- | :-------- |
| `options`                | `Object`  |
| `options.summarizeData?` | `boolean` |

#### Returns

`Promise`\<[`DatasetSummary`](/docs/libs/nodejs/interfaces/DatasetSummary.md)\>

`DatasetSummary`

A summary of the dataset.

---

### update

▸ **update**(`event`): `string`

Update fields of a single record in the dataset. The updated fields will be batched and uploaded behind the scenes.
You must pass in an `id` of the record to update. Only the fields provided will be updated; other fields will remain unchanged.

#### Parameters

| Name              | Type                            | Description                                                                                                                                             |
| :---------------- | :------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `event`           | `Object`                        | The fields to update in the record.                                                                                                                     |
| `event.expected?` | `unknown`                       | (Optional) The new expected output value for the record (an arbitrary, JSON serializable object).                                                       |
| `event.id`        | `string`                        | The unique identifier of the record to update.                                                                                                          |
| `event.input?`    | `unknown`                       | (Optional) The new input value for the record (an arbitrary, JSON serializable object).                                                                 |
| `event.metadata?` | `Record`\<`string`, `unknown`\> | (Optional) A dictionary to update the metadata of the record. The values in `metadata` can be any JSON-serializable type, but its keys must be strings. |
| `event.tags?`     | `string`[]                      | (Optional) A list of strings to update the tags of the record.                                                                                          |

#### Returns

`string`

The `id` of the updated record.

---

### version

▸ **version**(): `Promise`\<`undefined` \| `string`\>

#### Returns

`Promise`\<`undefined` \| `string`\>

#### Inherited from

ObjectFetcher.version

---

### isDataset

▸ **isDataset**(`data`): data is Dataset\<false\>

#### Parameters

| Name   | Type      |
| :----- | :-------- |
| `data` | `unknown` |

#### Returns

data is Dataset\<false\>
