---
title: CodePrompt
---

# Class: CodePrompt

## Constructors

### constructor

• **new CodePrompt**(`project`, `prompt`, `toolFunctions`, `opts`, `functionType?`): [`CodePrompt`](/docs/libs/nodejs/classes/CodePrompt.md)

#### Parameters

| Name                     | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| :----------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `project`                | [`Project`](/docs/libs/nodejs/classes/Project.md)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| `prompt`                 | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| `prompt.options?`        | `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } |
| `prompt.origin?`         | `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `prompt.parser?`         | `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| `prompt.prompt?`         | `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `prompt.tool_functions?` | `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| `toolFunctions`          | (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` } \| `GenericCodeFunction`)[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| `opts`                   | `Omit`\<[`PromptOpts`](/docs/libs/nodejs#promptopts)\<`false`, `false`, `false`, `false`\>, `"name"` \| `"slug"`\> & \{ `name`: `string` ; `slug`: `string` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `functionType?`          | `"llm"` \| `"tool"` \| `"scorer"` \| `"task"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

#### Returns

[`CodePrompt`](/docs/libs/nodejs/classes/CodePrompt.md)

## Properties

### description

• `Optional` `Readonly` **description**: `string`

---

### functionType

• `Optional` `Readonly` **functionType**: `"llm"` \| `"tool"` \| `"scorer"` \| `"task"`

---

### id

• `Optional` `Readonly` **id**: `string`

---

### ifExists

• `Optional` `Readonly` **ifExists**: `"error"` \| `"replace"` \| `"ignore"`

---

### name

• `Readonly` **name**: `string`

---

### project

• `Readonly` **project**: [`Project`](/docs/libs/nodejs/classes/Project.md)

---

### prompt

• `Readonly` **prompt**: `Object`

#### Type declaration

| Name              | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| :---------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `options?`        | `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } |
| `origin?`         | `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `parser?`         | `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| `prompt?`         | `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `tool_functions?` | `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |

---

### slug

• `Readonly` **slug**: `string`

---

### toolFunctions

• `Readonly` **toolFunctions**: (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` } \| `GenericCodeFunction`)[]
