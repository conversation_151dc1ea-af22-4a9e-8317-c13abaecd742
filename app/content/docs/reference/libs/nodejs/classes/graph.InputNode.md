---
title: graph
---

# Class: InputNode

[graph](/docs/libs/nodejs/modules/graph.md).InputNode

## Hierarchy

- `BaseNode`

  ↳ **`InputNode`**

## Implements

- [`Node`](/docs/libs/nodejs/interfaces/graph.Node.md)

## Constructors

### constructor

• **new InputNode**(`graph`, `id`): [`InputNode`](/docs/libs/nodejs/classes/graph.InputNode.md)

#### Parameters

| Name    | Type                                                              |
| :------ | :---------------------------------------------------------------- |
| `graph` | [`GraphBuilder`](/docs/libs/nodejs/classes/graph.GraphBuilder.md) |
| `id`    | `string`                                                          |

#### Returns

[`InputNode`](/docs/libs/nodejs/classes/graph.InputNode.md)

#### Overrides

BaseNode.constructor

## Methods

### addDependency

▸ **addDependency**(`dependency`): `void`

#### Parameters

| Name         | Type         |
| :----------- | :----------- |
| `dependency` | `Dependency` |

#### Returns

`void`

#### Implementation of

[Node](/docs/libs/nodejs/interfaces/graph.Node.md).[addDependency](/docs/libs/nodejs/interfaces/graph.Node.md#adddependency)

#### Inherited from

BaseNode.addDependency

---

### build

▸ **build**(`context`): `Promise`\<\{ `description?`: `null` \| `string` ; `function`: \{ `[k: string]`: `unknown`; } ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"function"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"input"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"output"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"literal"` ; `value?`: `unknown` } \| \{ `description?`: `null` \| `string` ; `expr`: `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"btql"` } \| \{ `condition?`: `null` \| `string` ; `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"gate"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"aggregator"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `prompt`: \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `type`: `"prompt_template"` }\>

#### Parameters

| Name      | Type                                                                 |
| :-------- | :------------------------------------------------------------------- |
| `context` | [`BuildContext`](/docs/libs/nodejs/interfaces/graph.BuildContext.md) |

#### Returns

`Promise`\<\{ `description?`: `null` \| `string` ; `function`: \{ `[k: string]`: `unknown`; } ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"function"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"input"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"output"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"literal"` ; `value?`: `unknown` } \| \{ `description?`: `null` \| `string` ; `expr`: `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"btql"` } \| \{ `condition?`: `null` \| `string` ; `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"gate"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"aggregator"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `prompt`: \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `type`: `"prompt_template"` }\>

#### Implementation of

[Node](/docs/libs/nodejs/interfaces/graph.Node.md).[build](/docs/libs/nodejs/interfaces/graph.Node.md#build)

#### Overrides

BaseNode.build

## Properties

### \_\_type

• `Readonly` **\_\_type**: `"node"`

#### Implementation of

[Node](/docs/libs/nodejs/interfaces/graph.Node.md).[\_\_type](/docs/libs/nodejs/interfaces/graph.Node.md#__type)

#### Inherited from

BaseNode.\_\_type

---

### dependencies

• **dependencies**: `Dependency`[] = `[]`

#### Inherited from

BaseNode.dependencies

---

### graph

• `Protected` **graph**: [`GraphBuilder`](/docs/libs/nodejs/classes/graph.GraphBuilder.md)

#### Inherited from

BaseNode.graph

---

### id

• `Readonly` **id**: `string`

#### Implementation of

[Node](/docs/libs/nodejs/interfaces/graph.Node.md).[id](/docs/libs/nodejs/interfaces/graph.Node.md#id)

#### Inherited from

BaseNode.id
