---
title: BaseAttachment
---

# Class: BaseAttachment

## Hierarchy

- **`BaseAttachment`**

  ↳ [`Attachment`](/docs/libs/nodejs/classes/Attachment.md)

  ↳ [`ExternalAttachment`](/docs/libs/nodejs/classes/ExternalAttachment.md)

## Constructors

### constructor

• **new BaseAttachment**(): [`BaseAttachment`](/docs/libs/nodejs/classes/BaseAttachment.md)

#### Returns

[`BaseAttachment`](/docs/libs/nodejs/classes/BaseAttachment.md)

## Methods

### data

▸ **data**(): `Promise`\<`Blob`\>

#### Returns

`Promise`\<`Blob`\>

---

### debugInfo

▸ **debugInfo**(): `Record`\<`string`, `unknown`\>

#### Returns

`Record`\<`string`, `unknown`\>

---

### upload

▸ **upload**(): `Promise`\<\{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` }\>

#### Returns

`Promise`\<\{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` }\>

## Properties

### reference

• `Readonly` **reference**: \{ `content_type`: `string` ; `filename`: `string` ; `key`: `string` ; `type`: `"braintrust_attachment"` } \| \{ `content_type`: `string` ; `filename`: `string` ; `type`: `"external_attachment"` ; `url`: `string` }
