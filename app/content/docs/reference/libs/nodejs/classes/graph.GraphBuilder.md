---
title: graph
---

# Class: GraphBuilder

[graph](/docs/libs/nodejs/modules/graph.md).GraphBuilder

## Constructors

### constructor

• **new GraphBuilder**(): [`GraphBuilder`](/docs/libs/nodejs/classes/graph.GraphBuilder.md)

#### Returns

[`GraphBuilder`](/docs/libs/nodejs/classes/graph.GraphBuilder.md)

## Methods

### addEdge

▸ **addEdge**(`«destructured»`): `void`

#### Parameters

| Name             | Type                                                      |
| :--------------- | :-------------------------------------------------------- |
| `«destructured»` | `Object`                                                  |
| › `expr?`        | `string`                                                  |
| › `purpose`      | `"data"` \| `"messages"` \| `"control"`                   |
| › `source`       | [`NodeLike`](/docs/libs/nodejs/modules/graph.md#nodelike) |
| › `sourceVar?`   | `string`                                                  |
| › `target`       | [`NodeLike`](/docs/libs/nodejs/modules/graph.md#nodelike) |
| › `targetVar?`   | `string`                                                  |

#### Returns

`void`

---

### aggregator

▸ **aggregator**(): [`AggregatorNode`](/docs/libs/nodejs/classes/graph.AggregatorNode.md)

#### Returns

[`AggregatorNode`](/docs/libs/nodejs/classes/graph.AggregatorNode.md)

---

### build

▸ **build**(`context`): `Promise`\<\{ `edges`: `Record`\<`string`, \{ `purpose`: `"data"` \| `"messages"` \| `"control"` ; `source`: \{ `node`: `string` ; `variable`: `string` } ; `target`: \{ `node`: `string` ; `variable`: `string` } }\> ; `nodes`: `Record`\<`string`, \{ `description?`: `null` \| `string` ; `function`: \{ `[k: string]`: `unknown`; } ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"function"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"input"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"output"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"literal"` ; `value?`: `unknown` } \| \{ `description?`: `null` \| `string` ; `expr`: `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"btql"` } \| \{ `condition?`: `null` \| `string` ; `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"gate"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"aggregator"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `prompt`: \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `type`: `"prompt_template"` }\> ; `type`: `"graph"` }\>

#### Parameters

| Name      | Type                                                                 |
| :-------- | :------------------------------------------------------------------- |
| `context` | [`BuildContext`](/docs/libs/nodejs/interfaces/graph.BuildContext.md) |

#### Returns

`Promise`\<\{ `edges`: `Record`\<`string`, \{ `purpose`: `"data"` \| `"messages"` \| `"control"` ; `source`: \{ `node`: `string` ; `variable`: `string` } ; `target`: \{ `node`: `string` ; `variable`: `string` } }\> ; `nodes`: `Record`\<`string`, \{ `description?`: `null` \| `string` ; `function`: \{ `[k: string]`: `unknown`; } ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"function"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"input"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"output"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"literal"` ; `value?`: `unknown` } \| \{ `description?`: `null` \| `string` ; `expr`: `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"btql"` } \| \{ `condition?`: `null` \| `string` ; `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"gate"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `type`: `"aggregator"` } \| \{ `description?`: `null` \| `string` ; `position?`: `null` \| \{ `x`: `number` ; `y`: `number` } ; `prompt`: \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `type`: `"prompt_template"` }\> ; `type`: `"graph"` }\>

---

### gate

▸ **gate**(`options`): [`GateNode`](/docs/libs/nodejs/classes/graph.GateNode.md)

#### Parameters

| Name                | Type     |
| :------------------ | :------- |
| `options`           | `Object` |
| `options.condition` | `string` |

#### Returns

[`GateNode`](/docs/libs/nodejs/classes/graph.GateNode.md)

---

### literal

▸ **literal**\<`T`\>(`value`): [`LiteralNode`](/docs/libs/nodejs/classes/graph.LiteralNode.md)\<`T`\>

#### Type parameters

| Name |
| :--- |
| `T`  |

#### Parameters

| Name    | Type |
| :------ | :--- |
| `value` | `T`  |

#### Returns

[`LiteralNode`](/docs/libs/nodejs/classes/graph.LiteralNode.md)\<`T`\>

---

### promptTemplate

▸ **promptTemplate**(`options`): [`PromptTemplateNode`](/docs/libs/nodejs/classes/graph.PromptTemplateNode.md)

#### Parameters

| Name             | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| :--------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `options`        | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| `options.prompt` | \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } |

#### Returns

[`PromptTemplateNode`](/docs/libs/nodejs/classes/graph.PromptTemplateNode.md)

---

### resolveNode

▸ **resolveNode**(`node`): [[`Node`](/docs/libs/nodejs/interfaces/graph.Node.md), `string`[]]

#### Parameters

| Name   | Type                                                      |
| :----- | :-------------------------------------------------------- |
| `node` | [`NodeLike`](/docs/libs/nodejs/modules/graph.md#nodelike) |

#### Returns

[[`Node`](/docs/libs/nodejs/interfaces/graph.Node.md), `string`[]]

## Properties

### IN

• `Readonly` **IN**: [`InputNode`](/docs/libs/nodejs/classes/graph.InputNode.md)

---

### OUT

• `Readonly` **OUT**: [`OutputNode`](/docs/libs/nodejs/classes/graph.OutputNode.md)
