---
title: Attachment
---

# Class: Attachment

Represents an attachment to be uploaded and the associated metadata.
`Attachment` objects can be inserted anywhere in an event, allowing you to
log arbitrary file data. The SDK will asynchronously upload the file to
object storage and replace the `Attachment` object with an
`AttachmentReference`.

## Hierarchy

- [`BaseAttachment`](/docs/libs/nodejs/classes/BaseAttachment.md)

  ↳ **`Attachment`**

## Constructors

### constructor

• **new Attachment**(`param`): [`Attachment`](/docs/libs/nodejs/classes/Attachment.md)

Construct an attachment.

#### Parameters

| Name    | Type                                                                   | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| :------ | :--------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `param` | [`AttachmentParams`](/docs/libs/nodejs/interfaces/AttachmentParams.md) | A parameter object with: `data`: A string representing the path of the file on disk, or a `Blob`/`ArrayBuffer` with the file's contents. The caller is responsible for ensuring the file/blob/buffer is not modified until upload is complete. `filename`: The desired name of the file in Braintrust after uploading. This parameter is for visualization purposes only and has no effect on attachment storage. `contentType`: The MIME type of the file. `state`: (Optional) For internal use. |

#### Returns

[`Attachment`](/docs/libs/nodejs/classes/Attachment.md)

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[constructor](/docs/libs/nodejs/classes/BaseAttachment.md#constructor)

## Methods

### data

▸ **data**(): `Promise`\<`Blob`\>

The attachment contents. This is a lazy value that will read the attachment contents from disk or memory on first access.

#### Returns

`Promise`\<`Blob`\>

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[data](/docs/libs/nodejs/classes/BaseAttachment.md#data)

---

### debugInfo

▸ **debugInfo**(): `Record`\<`string`, `unknown`\>

A human-readable description for logging and debugging.

#### Returns

`Record`\<`string`, `unknown`\>

The debug object. The return type is not stable and may change in
a future release.

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[debugInfo](/docs/libs/nodejs/classes/BaseAttachment.md#debuginfo)

---

### upload

▸ **upload**(): `Promise`\<\{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` }\>

On first access, (1) reads the attachment from disk if needed, (2)
authenticates with the data plane to request a signed URL, (3) uploads to
object store, and (4) updates the attachment.

#### Returns

`Promise`\<\{ `error_message?`: `string` ; `upload_status`: `"error"` \| `"done"` \| `"uploading"` }\>

The attachment status.

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[upload](/docs/libs/nodejs/classes/BaseAttachment.md#upload)

## Properties

### reference

• `Readonly` **reference**: `Object`

The object that replaces this `Attachment` at upload time.

#### Type declaration

| Name           | Type                      |
| :------------- | :------------------------ |
| `content_type` | `string`                  |
| `filename`     | `string`                  |
| `key`          | `string`                  |
| `type`         | `"braintrust_attachment"` |

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[reference](/docs/libs/nodejs/classes/BaseAttachment.md#reference)
