---
title: LazyValue
---

# Class: LazyValue\<T\>

## Type parameters

| Name |
| :--- |
| `T`  |

## Accessors

### hasSucceeded

• `get` **hasSucceeded**(): `boolean`

#### Returns

`boolean`

## Constructors

### constructor

• **new LazyValue**\<`T`\>(`callable`): [`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`T`\>

#### Type parameters

| Name |
| :--- |
| `T`  |

#### Parameters

| Name       | Type                   |
| :--------- | :--------------------- |
| `callable` | () => `Promise`\<`T`\> |

#### Returns

[`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`T`\>

## Methods

### get

▸ **get**(): `Promise`\<`T`\>

#### Returns

`Promise`\<`T`\>

---

### getSync

▸ **getSync**(): `Object`

#### Returns

`Object`

| Name       | Type               |
| :--------- | :----------------- |
| `resolved` | `boolean`          |
| `value`    | `undefined` \| `T` |
