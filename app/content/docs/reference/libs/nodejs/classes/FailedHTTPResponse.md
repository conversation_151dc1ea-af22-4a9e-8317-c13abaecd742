---
title: FailedHTTPResponse
---

# Class: FailedHTTPResponse

## Hierarchy

- `Error`

  ↳ **`FailedHTTPResponse`**

## Constructors

### constructor

• **new FailedHTTPResponse**(`status`, `text`, `data`): [`FailedHTTPResponse`](/docs/libs/nodejs/classes/FailedHTTPResponse.md)

#### Parameters

| Name     | Type     |
| :------- | :------- |
| `status` | `number` |
| `text`   | `string` |
| `data`   | `string` |

#### Returns

[`FailedHTTPResponse`](/docs/libs/nodejs/classes/FailedHTTPResponse.md)

#### Overrides

Error.constructor

## Properties

### data

• **data**: `string`

---

### status

• **status**: `number`

---

### text

• **text**: `string`
