---
title: Project
---

# Class: Project

## Constructors

### constructor

• **new Project**(`args`): [`Project`](/docs/libs/nodejs/classes/Project.md)

#### Parameters

| Name   | Type       |
| :----- | :--------- |
| `args` | `NameOrId` |

#### Returns

[`Project`](/docs/libs/nodejs/classes/Project.md)

## Properties

### id

• `Optional` `Readonly` **id**: `string`

---

### name

• `Optional` `Readonly` **name**: `string`

---

### prompts

• **prompts**: [`PromptBuilder`](/docs/libs/nodejs/classes/PromptBuilder.md)

---

### scorers

• **scorers**: [`ScorerBuilder`](/docs/libs/nodejs/classes/ScorerBuilder.md)

---

### tools

• **tools**: [`ToolBuilder`](/docs/libs/nodejs/classes/ToolBuilder.md)
