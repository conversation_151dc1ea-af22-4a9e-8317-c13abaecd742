---
title: ExternalAttachment
---

# Class: ExternalAttachment

Represents an attachment that resides in an external object store and the associated metadata.

`ExternalAttachment` objects can be inserted anywhere in an event, similar to
`Attachment` objects, but they reference files that already exist in an external
object store rather than requiring upload. The SDK will replace the `ExternalAttachment`
object with an `AttachmentReference` during logging.

## Hierarchy

- [`BaseAttachment`](/docs/libs/nodejs/classes/BaseAttachment.md)

  ↳ **`ExternalAttachment`**

## Constructors

### constructor

• **new ExternalAttachment**(`param`): [`ExternalAttachment`](/docs/libs/nodejs/classes/ExternalAttachment.md)

Construct an external attachment.

#### Parameters

| Name    | Type                                                                                   | Description                                                                                                                                                                                                                                                                                                                                          |
| :------ | :------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `param` | [`ExternalAttachmentParams`](/docs/libs/nodejs/interfaces/ExternalAttachmentParams.md) | A parameter object with: `url`: The fully qualified URL of the file in the external object store. `filename`: The desired name of the file in Braintrust after uploading. This parameter is for visualization purposes only and has no effect on attachment storage. `contentType`: The MIME type of the file. `state`: (Optional) For internal use. |

#### Returns

[`ExternalAttachment`](/docs/libs/nodejs/classes/ExternalAttachment.md)

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[constructor](/docs/libs/nodejs/classes/BaseAttachment.md#constructor)

## Methods

### data

▸ **data**(): `Promise`\<`Blob`\>

The attachment contents. This is a lazy value that will read the attachment contents from the external object store on first access.

#### Returns

`Promise`\<`Blob`\>

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[data](/docs/libs/nodejs/classes/BaseAttachment.md#data)

---

### debugInfo

▸ **debugInfo**(): `Record`\<`string`, `unknown`\>

A human-readable description for logging and debugging.

#### Returns

`Record`\<`string`, `unknown`\>

The debug object. The return type is not stable and may change in
a future release.

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[debugInfo](/docs/libs/nodejs/classes/BaseAttachment.md#debuginfo)

---

### upload

▸ **upload**(): `Promise`\<\{ `upload_status`: `"done"` }\>

For ExternalAttachment, this is a no-op since the data already resides
in the external object store. It marks the attachment as already uploaded.

#### Returns

`Promise`\<\{ `upload_status`: `"done"` }\>

The attachment status, which will always indicate success.

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[upload](/docs/libs/nodejs/classes/BaseAttachment.md#upload)

## Properties

### reference

• `Readonly` **reference**: `Object`

The object that replaces this `ExternalAttachment` at upload time.

#### Type declaration

| Name           | Type                    |
| :------------- | :---------------------- |
| `content_type` | `string`                |
| `filename`     | `string`                |
| `type`         | `"external_attachment"` |
| `url`          | `string`                |

#### Overrides

[BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md).[reference](/docs/libs/nodejs/classes/BaseAttachment.md#reference)
