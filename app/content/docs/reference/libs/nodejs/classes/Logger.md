---
title: Logger
---

# Class: Logger\<IsAsyncFlush\>

## Type parameters

| Name           | Type              |
| :------------- | :---------------- |
| `IsAsyncFlush` | extends `boolean` |

## Implements

- [`Exportable`](/docs/libs/nodejs/interfaces/Exportable.md)

## Accessors

### asyncFlush

• `get` **asyncFlush**(): `undefined` \| `IsAsyncFlush`

#### Returns

`undefined` \| `IsAsyncFlush`

---

### id

• `get` **id**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

---

### org_id

• `get` **org_id**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

---

### project

• `get` **project**(): `Promise`\<[`ObjectMetadata`](/docs/libs/nodejs/interfaces/ObjectMetadata.md)\>

#### Returns

`Promise`\<[`ObjectMetadata`](/docs/libs/nodejs/interfaces/ObjectMetadata.md)\>

## Constructors

### constructor

• **new Logger**\<`IsAsyncFlush`\>(`state`, `lazyMetadata`, `logOptions?`): [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>

#### Type parameters

| Name           | Type              |
| :------------- | :---------------- |
| `IsAsyncFlush` | extends `boolean` |

#### Parameters

| Name           | Type                                                                          |
| :------------- | :---------------------------------------------------------------------------- |
| `state`        | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)             |
| `lazyMetadata` | [`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`OrgProjectMetadata`\> |
| `logOptions`   | [`LogOptions`](/docs/libs/nodejs/interfaces/LogOptions.md)\<`IsAsyncFlush`\>  |

#### Returns

[`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>

## Methods

### export

▸ **export**(): `Promise`\<`string`\>

Return a serialized representation of the logger that can be used to start subspans in other places.

See [Span.startSpan](/docs/libs/nodejs/interfaces/Span.md#startspan) for more details.

#### Returns

`Promise`\<`string`\>

#### Implementation of

[Exportable](/docs/libs/nodejs/interfaces/Exportable.md).[export](/docs/libs/nodejs/interfaces/Exportable.md#export)

---

### flush

▸ **flush**(): `Promise`\<`void`\>

#### Returns

`Promise`\<`void`\>

---

### log

▸ **log**(`event`, `options?`): [`PromiseUnless`](/docs/libs/nodejs#promiseunless)\<`IsAsyncFlush`, `string`\>

Log a single event. The event will be batched and uploaded behind the scenes if `logOptions.asyncFlush` is true.

#### Parameters

| Name                                | Type                               | Description                                                                                                           |
| :---------------------------------- | :--------------------------------- | :-------------------------------------------------------------------------------------------------------------------- |
| `event`                             | `Readonly`\<`StartSpanEventArgs`\> | The event to log.                                                                                                     |
| `options?`                          | `Object`                           | Additional logging options                                                                                            |
| `options.allowConcurrentWithSpans?` | `boolean`                          | in rare cases where you need to log at the top level separately from spans on the logger elsewhere, set this to true. |

#### Returns

[`PromiseUnless`](/docs/libs/nodejs#promiseunless)\<`IsAsyncFlush`, `string`\>

The `id` of the logged event.

---

### logFeedback

▸ **logFeedback**(`event`): `void`

Log feedback to an event. Feedback is used to save feedback scores, set an expected value, or add a comment.

#### Parameters

| Name    | Type                                                           |
| :------ | :------------------------------------------------------------- |
| `event` | [`LogFeedbackFullArgs`](/docs/libs/nodejs#logfeedbackfullargs) |

#### Returns

`void`

---

### startSpan

▸ **startSpan**(`args?`): [`Span`](/docs/libs/nodejs/interfaces/Span.md)

Lower-level alternative to `traced`. This allows you to start a span yourself, and can be useful in situations
where you cannot use callbacks. However, spans started with `startSpan` will not be marked as the "current span",
so `currentSpan()` and `traced()` will be no-ops. If you want to mark a span as current, use `traced` instead.

See [traced](/docs/libs/nodejs#traced) for full details.

#### Parameters

| Name    | Type                                               |
| :------ | :------------------------------------------------- |
| `args?` | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) |

#### Returns

[`Span`](/docs/libs/nodejs/interfaces/Span.md)

---

### traced

▸ **traced**\<`R`\>(`callback`, `args?`): [`PromiseUnless`](/docs/libs/nodejs#promiseunless)\<`IsAsyncFlush`, `R`\>

Create a new toplevel span underneath the logger. The name defaults to "root".

See [Span.traced](/docs/libs/nodejs/interfaces/Span.md#traced) for full details.

#### Type parameters

| Name |
| :--- |
| `R`  |

#### Parameters

| Name       | Type                                                                                                    |
| :--------- | :------------------------------------------------------------------------------------------------------ |
| `callback` | (`span`: [`Span`](/docs/libs/nodejs/interfaces/Span.md)) => `R`                                         |
| `args?`    | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg) |

#### Returns

[`PromiseUnless`](/docs/libs/nodejs#promiseunless)\<`IsAsyncFlush`, `R`\>

---

### updateSpan

▸ **updateSpan**(`event`): `void`

Update a span in the experiment using its id. It is important that you only update a span once the original span has been fully written and flushed,
since otherwise updates to the span may conflict with the original span.

#### Parameters

| Name    | Type                                                                                                 | Description                                                                                                                                                   |
| :------ | :--------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `event` | `Omit`\<`Partial`\<`ExperimentEvent`\>, `"id"`\> & `Required`\<`Pick`\<`ExperimentEvent`, `"id"`\>\> | The event data to update the span with. Must include `id`. See [Experiment.log](/docs/libs/nodejs/classes/Experiment.md#log) for a full list of valid fields. |

#### Returns

`void`

## Properties

### kind

• **kind**: `"logger"`
