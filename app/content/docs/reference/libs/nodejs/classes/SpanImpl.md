---
title: SpanImpl
---

# Class: SpanImpl

Primary implementation of the `Span` interface. See [Span](/docs/libs/nodejs/interfaces/Span.md) for full details on each method.

We suggest using one of the various `traced` methods, instead of creating Spans directly. See [Span.startSpan](/docs/libs/nodejs/interfaces/Span.md#startspan) for full details.

## Implements

- [`Span`](/docs/libs/nodejs/interfaces/Span.md)

## Accessors

### id

• `get` **id**(): `string`

Row ID of the span.

#### Returns

`string`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[id](/docs/libs/nodejs/interfaces/Span.md#id)

---

### rootSpanId

• `get` **rootSpanId**(): `string`

Root span ID of the span.

#### Returns

`string`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[rootSpanId](/docs/libs/nodejs/interfaces/Span.md#rootspanid)

---

### spanId

• `get` **spanId**(): `string`

Span ID of the span.

#### Returns

`string`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[spanId](/docs/libs/nodejs/interfaces/Span.md#spanid)

---

### spanParents

• `get` **spanParents**(): `string`[]

Parent span IDs of the span.

#### Returns

`string`[]

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[spanParents](/docs/libs/nodejs/interfaces/Span.md#spanparents)

## Constructors

### constructor

• **new SpanImpl**(`args`): [`SpanImpl`](/docs/libs/nodejs/classes/SpanImpl.md)

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :----- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ `defaultRootType?`: `"function"` \| `"llm"` \| `"tool"` \| `"task"` \| `"score"` \| `"eval"` ; `parentComputeObjectMetadataArgs`: `undefined` \| `Record`\<`string`, `any`\> ; `parentObjectId`: [`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`string`\> ; `parentObjectType`: `SpanObjectTypeV3` ; `parentSpanIds`: `undefined` \| `ParentSpanIds` \| `MultiParentSpanIds` ; `spanId?`: `string` ; `state`: [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) } & `Omit`\<[`StartSpanArgs`](/docs/libs/nodejs#startspanargs), `"parent"`\> |

#### Returns

[`SpanImpl`](/docs/libs/nodejs/classes/SpanImpl.md)

## Methods

### \_link

▸ **\_link**(`orgName`): `string`

#### Parameters

| Name      | Type     |
| :-------- | :------- |
| `orgName` | `string` |

#### Returns

`string`

---

### close

▸ **close**(`args?`): `number`

Alias for `end`.

#### Parameters

| Name    | Type                                           |
| :------ | :--------------------------------------------- |
| `args?` | [`EndSpanArgs`](/docs/libs/nodejs#endspanargs) |

#### Returns

`number`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[close](/docs/libs/nodejs/interfaces/Span.md#close)

---

### end

▸ **end**(`args?`): `number`

Log an end time to the span (defaults to the current time). Returns the logged time.

Will be invoked automatically if the span is constructed with `traced`.

#### Parameters

| Name    | Type                                           |
| :------ | :--------------------------------------------- |
| `args?` | [`EndSpanArgs`](/docs/libs/nodejs#endspanargs) |

#### Returns

`number`

The end time logged to the span metrics.

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[end](/docs/libs/nodejs/interfaces/Span.md#end)

---

### export

▸ **export**(): `Promise`\<`string`\>

Serialize the identifiers of this span. The return value can be used to
identify this span when starting a subspan elsewhere, such as another
process or service, without needing to access this `Span` object. See the
parameters of [Span.startSpan](/docs/libs/nodejs/interfaces/Span.md#startspan) for usage details.

Callers should treat the return value as opaque. The serialization format
may change from time to time. If parsing is needed, use
`SpanComponentsV3.fromStr`.

#### Returns

`Promise`\<`string`\>

Serialized representation of this span's identifiers.

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[export](/docs/libs/nodejs/interfaces/Span.md#export)

---

### flush

▸ **flush**(): `Promise`\<`void`\>

Flush any pending rows to the server.

#### Returns

`Promise`\<`void`\>

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[flush](/docs/libs/nodejs/interfaces/Span.md#flush)

---

### link

▸ **link**(): `string`

Format a link to the Braintrust application for viewing this span.

Links can be generated at any time, but they will only become viewable
after the span and its root have been flushed to the server and ingested.

There are some conditions when a Span doesn't have enough information
to return a stable link (e.g. during an unresolved experiment). In this case
or if there's an error generating link, we'll return a placeholder link.

#### Returns

`string`

A link to the span.

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[link](/docs/libs/nodejs/interfaces/Span.md#link)

---

### log

▸ **log**(`event`): `void`

Incrementally update the current span with new data. The event will be batched and uploaded behind the scenes.

#### Parameters

| Name    | Type                                                                     |
| :------ | :----------------------------------------------------------------------- |
| `event` | [`ExperimentLogPartialArgs`](/docs/libs/nodejs#experimentlogpartialargs) |

#### Returns

`void`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[log](/docs/libs/nodejs/interfaces/Span.md#log)

---

### logFeedback

▸ **logFeedback**(`event`): `void`

Add feedback to the current span. Unlike `Experiment.logFeedback` and `Logger.logFeedback`, this method does not accept an id parameter, because it logs feedback to the current span.

#### Parameters

| Name    | Type                                                                             |
| :------ | :------------------------------------------------------------------------------- |
| `event` | `Omit`\<[`LogFeedbackFullArgs`](/docs/libs/nodejs#logfeedbackfullargs), `"id"`\> |

#### Returns

`void`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[logFeedback](/docs/libs/nodejs/interfaces/Span.md#logfeedback)

---

### permalink

▸ **permalink**(): `Promise`\<`string`\>

Format a permalink to the Braintrust application for viewing this span.

Links can be generated at any time, but they will only become viewable
after the span and its root have been flushed to the server and ingested.

This function can block resolving data with the server. For production
applications it's preferable to call [Span.link](/docs/libs/nodejs/interfaces/Span.md#link) instead.

#### Returns

`Promise`\<`string`\>

A promise which resolves to a permalink to the span.

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[permalink](/docs/libs/nodejs/interfaces/Span.md#permalink)

---

### setAttributes

▸ **setAttributes**(`args`): `void`

Set the span's name, type, or other attributes after it's created.

#### Parameters

| Name   | Type                                                                    |
| :----- | :---------------------------------------------------------------------- |
| `args` | `Omit`\<[`StartSpanArgs`](/docs/libs/nodejs#startspanargs), `"event"`\> |

#### Returns

`void`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[setAttributes](/docs/libs/nodejs/interfaces/Span.md#setattributes)

---

### setSpanParents

▸ **setSpanParents**(`parents`): `void`

#### Parameters

| Name      | Type       |
| :-------- | :--------- |
| `parents` | `string`[] |

#### Returns

`void`

---

### startSpan

▸ **startSpan**(`args?`): [`Span`](/docs/libs/nodejs/interfaces/Span.md)

Lower-level alternative to `traced`. This allows you to start a span yourself, and can be useful in situations
where you cannot use callbacks. However, spans started with `startSpan` will not be marked as the "current span",
so `currentSpan()` and `traced()` will be no-ops. If you want to mark a span as current, use `traced` instead.

See [Span.traced](/docs/libs/nodejs/interfaces/Span.md#traced) for full details.

#### Parameters

| Name    | Type                                               |
| :------ | :------------------------------------------------- |
| `args?` | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) |

#### Returns

[`Span`](/docs/libs/nodejs/interfaces/Span.md)

The newly-created `Span`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[startSpan](/docs/libs/nodejs/interfaces/Span.md#startspan)

---

### startSpanWithParents

▸ **startSpanWithParents**(`spanId`, `spanParents`, `args?`): [`Span`](/docs/libs/nodejs/interfaces/Span.md)

Start a span with a specific id and parent span ids.

#### Parameters

| Name          | Type                                               |
| :------------ | :------------------------------------------------- |
| `spanId`      | `string`                                           |
| `spanParents` | `string`[]                                         |
| `args?`       | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) |

#### Returns

[`Span`](/docs/libs/nodejs/interfaces/Span.md)

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[startSpanWithParents](/docs/libs/nodejs/interfaces/Span.md#startspanwithparents)

---

### state

▸ **state**(): [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

#### Returns

[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[state](/docs/libs/nodejs/interfaces/Span.md#state)

---

### traced

▸ **traced**\<`R`\>(`callback`, `args?`): `R`

Create a new span and run the provided callback. This is useful if you want to log more detailed trace information beyond the scope of a single log event. Data logged over several calls to `Span.log` will be merged into one logical row.

Spans created within `traced` are ended automatically. By default, the span is marked as current, so they can be accessed using `braintrust.currentSpan`.

#### Type parameters

| Name |
| :--- |
| `R`  |

#### Parameters

| Name       | Type                                                                                                    | Description                                    |
| :--------- | :------------------------------------------------------------------------------------------------------ | :--------------------------------------------- |
| `callback` | (`span`: [`Span`](/docs/libs/nodejs/interfaces/Span.md)) => `R`                                         | The function to be run under the span context. |
| `args?`    | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg) | -                                              |

#### Returns

`R`

The result of running `callback`.

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[traced](/docs/libs/nodejs/interfaces/Span.md#traced)

## Properties

### kind

• **kind**: `"span"`

#### Implementation of

[Span](/docs/libs/nodejs/interfaces/Span.md).[kind](/docs/libs/nodejs/interfaces/Span.md#kind)
