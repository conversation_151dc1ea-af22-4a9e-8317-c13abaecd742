---
title: <PERSON><PERSON><PERSON><PERSON>xperiment
---

# Class: ReadonlyExperiment

A read-only view of an experiment, initialized by passing `open: true` to `init()`.

## Hierarchy

- `ObjectFetcher`\<`ExperimentEvent`\>

  ↳ **`ReadonlyExperiment`**

## Accessors

### id

• `get` **id**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

#### Overrides

ObjectFetcher.id

---

### name

• `get` **name**(): `Promise`\<`string`\>

#### Returns

`Promise`\<`string`\>

## Constructors

### constructor

• **new ReadonlyExperiment**(`state`, `lazyMetadata`): [`ReadonlyExperiment`](/docs/libs/nodejs/classes/ReadonlyExperiment.md)

#### Parameters

| Name           | Type                                                                                 |
| :------------- | :----------------------------------------------------------------------------------- |
| `state`        | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)                    |
| `lazyMetadata` | [`LazyValue`](/docs/libs/nodejs/classes/LazyValue.md)\<`ProjectExperimentMetadata`\> |

#### Returns

[`ReadonlyExperiment`](/docs/libs/nodejs/classes/ReadonlyExperiment.md)

#### Overrides

ObjectFetcher\&lt;ExperimentEvent\&gt;.constructor

## Methods

### [asyncIterator]

▸ **[asyncIterator]**(): `AsyncIterator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<`ExperimentEvent`\>, `any`, `undefined`\>

#### Returns

`AsyncIterator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<`ExperimentEvent`\>, `any`, `undefined`\>

#### Inherited from

ObjectFetcher.[asyncIterator]

---

### asDataset

▸ **asDataset**\<`Input`, `Expected`\>(): `AsyncGenerator`\<[`EvalCase`](/docs/libs/nodejs#evalcase)\<`Input`, `Expected`, `void`\>, `any`, `unknown`\>

#### Type parameters

| Name       |
| :--------- |
| `Input`    |
| `Expected` |

#### Returns

`AsyncGenerator`\<[`EvalCase`](/docs/libs/nodejs#evalcase)\<`Input`, `Expected`, `void`\>, `any`, `unknown`\>

---

### clearCache

▸ **clearCache**(): `void`

#### Returns

`void`

#### Inherited from

ObjectFetcher.clearCache

---

### fetch

▸ **fetch**(): `AsyncGenerator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<`ExperimentEvent`\>, `any`, `unknown`\>

#### Returns

`AsyncGenerator`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<`ExperimentEvent`\>, `any`, `unknown`\>

#### Inherited from

ObjectFetcher.fetch

---

### fetchedData

▸ **fetchedData**(): `Promise`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<`ExperimentEvent`\>[]\>

#### Returns

`Promise`\<[`WithTransactionId`](/docs/libs/nodejs#withtransactionid)\<`ExperimentEvent`\>[]\>

#### Inherited from

ObjectFetcher.fetchedData

---

### getState

▸ **getState**(): `Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

#### Returns

`Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

#### Overrides

ObjectFetcher.getState

---

### version

▸ **version**(): `Promise`\<`undefined` \| `string`\>

#### Returns

`Promise`\<`undefined` \| `string`\>

#### Inherited from

ObjectFetcher.version
