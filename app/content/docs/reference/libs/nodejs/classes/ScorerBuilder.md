---
title: ScorerBuilder
---

# Class: ScorerBuilder

## Constructors

### constructor

• **new ScorerBuilder**(`project`): [`ScorerBuilder`](/docs/libs/nodejs/classes/ScorerBuilder.md)

#### Parameters

| Name      | Type                                              |
| :-------- | :------------------------------------------------ |
| `project` | [`Project`](/docs/libs/nodejs/classes/Project.md) |

#### Returns

[`ScorerBuilder`](/docs/libs/nodejs/classes/ScorerBuilder.md)

## Methods

### create

▸ **create**\<`Output`, `Input`, `Params`, `Returns`, `Fn`\>(`opts`): `void`

#### Type parameters

| Name      | Type                                                                                           |
| :-------- | :--------------------------------------------------------------------------------------------- |
| `Output`  | `Output`                                                                                       |
| `Input`   | `Input`                                                                                        |
| `Params`  | `Params`                                                                                       |
| `Returns` | `Returns`                                                                                      |
| `Fn`      | extends `GenericFunction`\<`Exact`\<`Params`, `ScorerArgs`\<`Output`, `Input`\>\>, `Returns`\> |

#### Parameters

| Name   | Type                                                                                         |
| :----- | :------------------------------------------------------------------------------------------- |
| `opts` | [`ScorerOpts`](/docs/libs/nodejs#scoreropts)\<`Output`, `Input`, `Params`, `Returns`, `Fn`\> |

#### Returns

`void`
