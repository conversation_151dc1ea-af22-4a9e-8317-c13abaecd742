---
title: PromptBuilder
---

# Class: PromptBuilder

## Constructors

### constructor

• **new PromptBuilder**(`project`): [`PromptBuilder`](/docs/libs/nodejs/classes/PromptBuilder.md)

#### Parameters

| Name      | Type                                              |
| :-------- | :------------------------------------------------ |
| `project` | [`Project`](/docs/libs/nodejs/classes/Project.md) |

#### Returns

[`PromptBuilder`](/docs/libs/nodejs/classes/PromptBuilder.md)

## Methods

### create

▸ **create**\<`HasId`, `HasVersion`\>(`opts`): [`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`HasId`, `HasVersion`\>

#### Type parameters

| Name         | Type                        |
| :----------- | :-------------------------- |
| `HasId`      | extends `boolean` = `false` |
| `HasVersion` | extends `boolean` = `false` |

#### Parameters

| Name   | Type                                                                                  |
| :----- | :------------------------------------------------------------------------------------ |
| `opts` | [`PromptOpts`](/docs/libs/nodejs#promptopts)\<`HasId`, `HasVersion`, `true`, `true`\> |

#### Returns

[`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`HasId`, `HasVersion`\>
