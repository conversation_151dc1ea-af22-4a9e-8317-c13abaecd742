---
title: Exportable
---

# Interface: Exportable

## Hierarchy

- **`Exportable`**

  ↳ [`Span`](/docs/libs/nodejs/interfaces/Span.md)

## Implemented by

- [`Experiment`](/docs/libs/nodejs/classes/Experiment.md)
- [`Logger`](/docs/libs/nodejs/classes/Logger.md)

## Methods

### export

▸ **export**(): `Promise`\<`string`\>

Return a serialized representation of the object that can be used to start subspans in other places. See [Span.traced](/docs/libs/nodejs/interfaces/Span.md#traced) for more details.

#### Returns

`Promise`\<`string`\>
