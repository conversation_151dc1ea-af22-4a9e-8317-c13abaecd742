---
title: ExperimentSummary
---

# Interface: ExperimentSummary

Summary of an experiment's scores and metadata.

## Properties

### comparisonExperimentName

• `Optional` **comparisonExperimentName**: `string`

The experiment scores are baselined against.

---

### experimentId

• `Optional` **experimentId**: `string`

ID of the experiment. May be `undefined` if the eval was run locally.

---

### experimentName

• **experimentName**: `string`

Name of the experiment.

---

### experimentUrl

• `Optional` **experimentUrl**: `string`

URL to the experiment's page in the Braintrust app.

---

### metrics

• `Optional` **metrics**: `Record`\<`string`, [`MetricSummary`](/docs/libs/nodejs/interfaces/MetricSummary.md)\>

---

### projectId

• `Optional` **projectId**: `string`

---

### projectName

• **projectName**: `string`

Name of the project that the experiment belongs to.

---

### projectUrl

• `Optional` **projectUrl**: `string`

URL to the project's page in the Braintrust app.

---

### scores

• **scores**: `Record`\<`string`, [`ScoreSummary`](/docs/libs/nodejs/interfaces/ScoreSummary.md)\>

Summary of the experiment's scores.
