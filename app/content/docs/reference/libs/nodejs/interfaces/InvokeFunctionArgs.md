---
title: InvokeFunctionArgs
---

# Interface: InvokeFunctionArgs\<Input, Output, Stream\>

Arguments for the `invoke` function.

## Type parameters

| Name     | Type                        |
| :------- | :-------------------------- |
| `Input`  | `Input`                     |
| `Output` | `Output`                    |
| `Stream` | extends `boolean` = `false` |

## Properties

### function_id

• `Optional` **function_id**: `string`

The ID of the function to invoke.

---

### globalFunction

• `Optional` **globalFunction**: `string`

The name of the global function to invoke.

---

### input

• **input**: `Input`

The input to the function. This will be logged as the `input` field in the span.

---

### messages

• `Optional` **messages**: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[]

Additional OpenAI-style messages to add to the prompt (only works for llm functions).

---

### metadata

• `Optional` **metadata**: `Record`\<`string`, `unknown`\>

Additional metadata to add to the span. This will be logged as the `metadata` field in the span.
It will also be available as the {{metadata}} field in the prompt and as the `metadata` argument
to the function.

---

### mode

• `Optional` **mode**: `"auto"` \| `"parallel"`

The mode of the function. If "auto", will return a string if the function returns a string,
and a JSON object otherwise. If "parallel", will return an array of JSON objects with one
object per tool call.

---

### parent

• `Optional` **parent**: `string` \| [`Exportable`](/docs/libs/nodejs/interfaces/Exportable.md)

The parent of the function. This can be an existing span, logger, or experiment, or
the output of `.export()` if you are distributed tracing. If unspecified, will use
the same semantics as `traced()` to determine the parent and no-op if not in a tracing
context.

---

### projectName

• `Optional` **projectName**: `string`

The name of the project containing the function to invoke.

---

### promptSessionFunctionId

• `Optional` **promptSessionFunctionId**: `string`

The ID of the function in the prompt session to invoke.

---

### promptSessionId

• `Optional` **promptSessionId**: `string`

The ID of the prompt session to invoke the function from.

---

### schema

• `Optional` **schema**: `Stream` extends `true` ? `never` : `ZodType`\<`Output`, `ZodTypeDef`, `Output`\>

A Zod schema to validate the output of the function and return a typed value. This
is only used if `stream` is false.

---

### slug

• `Optional` **slug**: `string`

The slug of the function to invoke.

---

### state

• `Optional` **state**: [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)

(Advanced) This parameter allows you to pass in a custom login state. This is useful
for multi-tenant environments where you are running functions from different Braintrust
organizations.

---

### stream

• `Optional` **stream**: `Stream`

Whether to stream the function's output. If true, the function will return a
`BraintrustStream`, otherwise it will return the output of the function as a JSON
object.

---

### strict

• `Optional` **strict**: `boolean`

Whether to use strict mode for the function. If true, the function will throw an error
if the variable names in the prompt do not match the input keys.

---

### tags

• `Optional` **tags**: `string`[]

Tags to add to the span. This will be logged as the `tags` field in the span.

---

### version

• `Optional` **version**: `string`

The version of the function to invoke.
