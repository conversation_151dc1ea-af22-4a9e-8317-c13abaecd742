---
title: ReporterBody
---

# Interface: Reporter<PERSON><PERSON>\<EvalReport\>

## Type parameters

| Name         |
| :----------- |
| `EvalReport` |

## Methods

### reportEval

▸ **reportEval**(`evaluator`, `result`, `opts`): `EvalReport` \| `Promise`\<`EvalReport`\>

A function that takes an evaluator and its result and returns a report.

#### Parameters

| Name        | Type                                                                                                        |
| :---------- | :---------------------------------------------------------------------------------------------------------- |
| `evaluator` | [`EvaluatorDef`](/docs/libs/nodejs#evaluatordef)\<`any`, `any`, `any`, `any`, `any`\>                       |
| `result`    | [`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`any`, `any`, `any`, `any`\> |
| `opts`      | `ReporterOpts`                                                                                              |

#### Returns

`EvalReport` \| `Promise`\<`EvalReport`\>

---

### reportRun

▸ **reportRun**(`reports`): `boolean` \| `Promise`\<`boolean`\>

A function that takes all evaluator results and returns a boolean indicating
whether the run was successful. If you return false, the `braintrust eval`
command will exit with a non-zero status code.

#### Parameters

| Name      | Type           |
| :-------- | :------------- |
| `reports` | `EvalReport`[] |

#### Returns

`boolean` \| `Promise`\<`boolean`\>
