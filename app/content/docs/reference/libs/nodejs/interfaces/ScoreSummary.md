---
title: ScoreSummary
---

# Interface: ScoreSummary

Summary of a score's performance.

## Properties

### diff

• `Optional` **diff**: `number`

Difference in score between the current and reference experiment.

---

### improvements

• **improvements**: `number`

Number of improvements in the score.

---

### name

• **name**: `string`

Name of the score.

---

### regressions

• **regressions**: `number`

Number of regressions in the score.

---

### score

• **score**: `number`

Average score across all examples.
