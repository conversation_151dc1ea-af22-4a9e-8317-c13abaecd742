---
title: <PERSON><PERSON><PERSON><PERSON>s
---

# Interface: <PERSON><PERSON><PERSON><PERSON><PERSON>\<Expected, Metadata, Parameters\>

## Type parameters

| Name         | Type                                                         |
| :----------- | :----------------------------------------------------------- |
| `Expected`   | `Expected`                                                   |
| `Metadata`   | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata)     |
| `Parameters` | extends [`EvalParameters`](/docs/libs/nodejs#evalparameters) |

## Properties

### expected

• **expected**: `Expected`

The expected output for the current evaluation.

---

### meta

• **meta**: (`info`: `Metadata`) => `void`

#### Type declaration

▸ (`info`): `void`

##### Parameters

| Name   | Type       |
| :----- | :--------- |
| `info` | `Metadata` |

##### Returns

`void`

**`Deprecated`**

Use `metadata` instead.

---

### metadata

• **metadata**: `Metadata` extends `void` ? `Record`\<`string`, `unknown`\> : `Metadata`

The metadata object for the current evaluation. You can mutate this object to add or remove metadata.

---

### parameters

• **parameters**: `InferParameters`\<`Parameters`\>

The current parameters being used for this specific task execution.
Array parameters are converted to single values.

---

### reportProgress

• **reportProgress**: (`progress`: `TaskProgressEvent`) => `void`

#### Type declaration

▸ (`progress`): `void`

Report progress that will show up in the playground.

##### Parameters

| Name       | Type                |
| :--------- | :------------------ |
| `progress` | `TaskProgressEvent` |

##### Returns

`void`

---

### span

• **span**: [`Span`](/docs/libs/nodejs/interfaces/Span.md)

The task's span.
