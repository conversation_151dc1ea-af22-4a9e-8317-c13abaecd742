---
title: LoginOptions
---

# Interface: LoginOptions

Options for logging in to Braintrust.

## Properties

### apiKey

• `Optional` **apiKey**: `string`

The API key to use. If the parameter is not specified, will try to use the `BRAINTRUST_API_KEY` environment variable.

---

### appUrl

• `Optional` **appUrl**: `string`

The URL of the Braintrust App. Defaults to https://www.braintrust.dev. You should not need
to change this unless you are doing the "Full" deployment.

---

### fetch

• `Optional` **fetch**: (`input`: `URL` \| `RequestInfo`, `init?`: `RequestInit`) => `Promise`\<`Response`\>(`input`: `string` \| `URL` \| `Request`, `init?`: `RequestInit`) => `Promise`\<`Response`\>

#### Type declaration

▸ (`input`, `init?`): `Promise`\<`Response`\>

A custom fetch implementation to use.

##### Parameters

| Name    | Type                   |
| :------ | :--------------------- |
| `input` | `URL` \| `RequestInfo` |
| `init?` | `RequestInit`          |

##### Returns

`Promise`\<`Response`\>

▸ (`input`, `init?`): `Promise`\<`Response`\>

A custom fetch implementation to use.

##### Parameters

| Name    | Type                           |
| :------ | :----------------------------- |
| `input` | `string` \| `URL` \| `Request` |
| `init?` | `RequestInit`                  |

##### Returns

`Promise`\<`Response`\>

---

### noExitFlush

• `Optional` **noExitFlush**: `boolean`

By default, the SDK installs an event handler that flushes pending writes on the `beforeExit` event.
If true, this event handler will _not_ be installed.

---

### onFlushError

• `Optional` **onFlushError**: (`error`: `unknown`) => `void`

#### Type declaration

▸ (`error`): `void`

Calls this function if there's an error in the background flusher.

##### Parameters

| Name    | Type      |
| :------ | :-------- |
| `error` | `unknown` |

##### Returns

`void`

---

### orgName

• `Optional` **orgName**: `string`

The name of a specific organization to connect to. Since API keys are scoped to organizations, this parameter is usually
unnecessary unless you are logging in with a JWT.
