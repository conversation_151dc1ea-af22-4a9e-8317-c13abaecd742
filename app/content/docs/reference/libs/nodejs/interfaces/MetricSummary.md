---
title: MetricSummary
---

# Interface: MetricSummary

Summary of a metric's performance.

## Properties

### diff

• `Optional` **diff**: `number`

Difference in metric between the current and reference experiment.

---

### improvements

• **improvements**: `number`

Number of improvements in the metric.

---

### metric

• **metric**: `number`

Average metric across all examples.

---

### name

• **name**: `string`

Name of the metric.

---

### regressions

• **regressions**: `number`

Number of regressions in the metric.

---

### unit

• **unit**: `string`

Unit label for the metric.
