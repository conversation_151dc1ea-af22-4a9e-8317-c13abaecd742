---
title: TypeScript
description: TypeScript reference for Braintrust's core SDK.
---

# braintrust

An isomorphic JS library for working with [Braintrust](https://braintrust.dev/). This library contains functionality
for running evaluations, logging completions, loading and invoking functions, and more.

`braintrust` is distributed as a [library on NPM](https://www.npmjs.com/package/braintrust).
It is also open source and available on [GitHub](https://github.com/braintrustdata/braintrust-sdk/tree/main/js).

### Quickstart

Install the library with npm (or yarn).

```bash
npm install braintrust
```

Then, create a file like `hello.eval.ts` with the following content:

```javascript
import { Eval } from "braintrust";

function isEqual({ output, expected }: { output: string; expected?: string }) {
  return { name: "is_equal", score: output === expected ? 1 : 0 };
}

Eval("Say Hi Bot", {
  data: () => {
    return [
      {
        input: "Foo",
        expected: "Hi Foo",
      },
      {
        input: "Bar",
        expected: "Hello Bar",
      },
    ]; // Replace with your eval dataset
  },
  task: (input: string) => {
    return "Hi " + input; // Replace with your LLM call
  },
  scores: [isEqual],
});
```

Finally, run the script with `npx braintrust eval hello.eval.ts`.

```bash
BRAINTRUST_API_KEY=<YOUR_BRAINTRUST_API_KEY> npx braintrust eval hello.eval.ts
```

## Classes

- [Attachment](/docs/libs/nodejs/classes/Attachment.md)
- [BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md)
- [BraintrustState](/docs/libs/nodejs/classes/BraintrustState.md)
- [BraintrustStream](/docs/libs/nodejs/classes/BraintrustStream.md)
- [CodeFunction](/docs/libs/nodejs/classes/CodeFunction.md)
- [CodePrompt](/docs/libs/nodejs/classes/CodePrompt.md)
- [Dataset](/docs/libs/nodejs/classes/Dataset.md)
- [EvalResultWithSummary](/docs/libs/nodejs/classes/EvalResultWithSummary.md)
- [Experiment](/docs/libs/nodejs/classes/Experiment.md)
- [ExternalAttachment](/docs/libs/nodejs/classes/ExternalAttachment.md)
- [FailedHTTPResponse](/docs/libs/nodejs/classes/FailedHTTPResponse.md)
- [LazyValue](/docs/libs/nodejs/classes/LazyValue.md)
- [Logger](/docs/libs/nodejs/classes/Logger.md)
- [NoopSpan](/docs/libs/nodejs/classes/NoopSpan.md)
- [Project](/docs/libs/nodejs/classes/Project.md)
- [Prompt](/docs/libs/nodejs/classes/Prompt.md)
- [PromptBuilder](/docs/libs/nodejs/classes/PromptBuilder.md)
- [ReadonlyAttachment](/docs/libs/nodejs/classes/ReadonlyAttachment.md)
- [ReadonlyExperiment](/docs/libs/nodejs/classes/ReadonlyExperiment.md)
- [ScorerBuilder](/docs/libs/nodejs/classes/ScorerBuilder.md)
- [SpanImpl](/docs/libs/nodejs/classes/SpanImpl.md)
- [TestBackgroundLogger](/docs/libs/nodejs/classes/TestBackgroundLogger.md)
- [ToolBuilder](/docs/libs/nodejs/classes/ToolBuilder.md)

## Interfaces

- [AttachmentParams](/docs/libs/nodejs/interfaces/AttachmentParams.md)
- [BackgroundLoggerOpts](/docs/libs/nodejs/interfaces/BackgroundLoggerOpts.md)
- [DataSummary](/docs/libs/nodejs/interfaces/DataSummary.md)
- [DatasetSummary](/docs/libs/nodejs/interfaces/DatasetSummary.md)
- [EvalHooks](/docs/libs/nodejs/interfaces/EvalHooks.md)
- [Evaluator](/docs/libs/nodejs/interfaces/Evaluator.md)
- [ExperimentSummary](/docs/libs/nodejs/interfaces/ExperimentSummary.md)
- [Exportable](/docs/libs/nodejs/interfaces/Exportable.md)
- [ExternalAttachmentParams](/docs/libs/nodejs/interfaces/ExternalAttachmentParams.md)
- [InvokeFunctionArgs](/docs/libs/nodejs/interfaces/InvokeFunctionArgs.md)
- [LogOptions](/docs/libs/nodejs/interfaces/LogOptions.md)
- [LoginOptions](/docs/libs/nodejs/interfaces/LoginOptions.md)
- [MetricSummary](/docs/libs/nodejs/interfaces/MetricSummary.md)
- [ObjectMetadata](/docs/libs/nodejs/interfaces/ObjectMetadata.md)
- [ParentExperimentIds](/docs/libs/nodejs/interfaces/ParentExperimentIds.md)
- [ParentProjectLogIds](/docs/libs/nodejs/interfaces/ParentProjectLogIds.md)
- [ReporterBody](/docs/libs/nodejs/interfaces/ReporterBody.md)
- [ScoreSummary](/docs/libs/nodejs/interfaces/ScoreSummary.md)
- [Span](/docs/libs/nodejs/interfaces/Span.md)

## Namespaces

- [default](/docs/libs/nodejs/modules/default.md)
- [graph](/docs/libs/nodejs/modules/graph.md)

## Functions

### BaseExperiment

▸ **BaseExperiment**\<`Input`, `Expected`, `Metadata`\>(`options?`): [`BaseExperiment`](/docs/libs/nodejs#baseexperiment)\<`Input`, `Expected`, `Metadata`\>

Use this to specify that the dataset should actually be the data from a previous (base) experiment.
If you do not specify a name, Braintrust will automatically figure out the best base experiment to
use based on your git history (or fall back to timestamps).

#### Type parameters

| Name       | Type                                                              |
| :--------- | :---------------------------------------------------------------- |
| `Input`    | `unknown`                                                         |
| `Expected` | `unknown`                                                         |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = `void` |

#### Parameters

| Name            | Type     | Description                                                                                                                                                         |
| :-------------- | :------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `options`       | `Object` |                                                                                                                                                                     |
| `options.name?` | `string` | The name of the base experiment to use. If unspecified, Braintrust will automatically figure out the best base using your git history (or fall back to timestamps). |

#### Returns

[`BaseExperiment`](/docs/libs/nodejs#baseexperiment)\<`Input`, `Expected`, `Metadata`\>

---

### Eval

▸ **Eval**\<`Input`, `Output`, `Expected`, `Metadata`, `EvalReport`, `Parameters`\>(`name`, `evaluator`, `reporterOrOpts?`): `Promise`\<[`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`Input`, `Output`, `Expected`, `Metadata`\>\>

#### Type parameters

| Name         | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `Input`      | `Input`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| `Output`     | `Output`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| `Expected`   | `void`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| `Metadata`   | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = `void`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `EvalReport` | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| `Parameters` | extends `Record`\<`string`, \{ `default?`: \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] } ; `description?`: `string` ; `type`: `"prompt"` } \| `ZodType`\<`unknown`, `ZodTypeDef`, `unknown`\>\> = `Record`\<`string`, \{ `default?`: \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] } ; `description?`: `string` ; `type`: `"prompt"` } \| `ZodType`\<`unknown`, `ZodTypeDef`, `unknown`\>\> |

#### Parameters

| Name              | Type                                                                                                                |
| :---------------- | :------------------------------------------------------------------------------------------------------------------ |
| `name`            | `string`                                                                                                            |
| `evaluator`       | [`Evaluator`](/docs/libs/nodejs/interfaces/Evaluator.md)\<`Input`, `Output`, `Expected`, `Metadata`, `Parameters`\> |
| `reporterOrOpts?` | `string` \| `ReporterDef`\<`EvalReport`\> \| `EvalOptions`\<`EvalReport`, `Parameters`\>                            |

#### Returns

`Promise`\<[`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`Input`, `Output`, `Expected`, `Metadata`\>\>

---

### Reporter

▸ **Reporter**\<`EvalReport`\>(`name`, `reporter`): `ReporterDef`\<`EvalReport`\>

#### Type parameters

| Name         |
| :----------- |
| `EvalReport` |

#### Parameters

| Name       | Type                                                                           |
| :--------- | :----------------------------------------------------------------------------- |
| `name`     | `string`                                                                       |
| `reporter` | [`ReporterBody`](/docs/libs/nodejs/interfaces/ReporterBody.md)\<`EvalReport`\> |

#### Returns

`ReporterDef`\<`EvalReport`\>

---

### buildLocalSummary

▸ **buildLocalSummary**(`evaluator`, `results`): [`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)

#### Parameters

| Name        | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| :---------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `evaluator` | [`EvaluatorDef`](/docs/libs/nodejs#evaluatordef)\<`any`, `any`, `any`, `any`, `Record`\<`string`, \{ `default?`: \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] } ; `description?`: `string` ; `type`: `"prompt"` } \| `ZodType`\<`unknown`, `ZodTypeDef`, `unknown`\>\>\> |
| `results`   | [`EvalResult`](/docs/libs/nodejs#evalresult)\<`any`, `any`, `any`, `any`\>[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |

#### Returns

[`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)

---

### createFinalValuePassThroughStream

▸ **createFinalValuePassThroughStream**\<`T`\>(`onFinal`, `onError`): `TransformStream`\<`T`, [`BraintrustStreamChunk`](/docs/libs/nodejs#braintruststreamchunk)\>

Create a stream that passes through the final value of the stream. This is
used to implement `BraintrustStream.finalValue()`.

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :--- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `string` \| \{ `data`: `string` ; `type`: `"text_delta"` } \| \{ `data`: `string` ; `type`: `"reasoning_delta"` } \| \{ `data`: `string` ; `type`: `"json_delta"` } \| \{ `data`: `string` ; `type`: `"error"` } \| \{ `data`: \{ `message`: `string` ; `stream`: `"stderr"` \| `"stdout"` } = sseConsoleEventDataSchema; `type`: `"console"` } \| \{ `data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` } = sseProgressEventDataSchema; `type`: `"progress"` } \| \{ `data`: `string` ; `type`: `"start"` } \| \{ `data`: `string` ; `type`: `"done"` } \| `Uint8Array` |

#### Parameters

| Name      | Type                            | Description                                            |
| :-------- | :------------------------------ | :----------------------------------------------------- |
| `onFinal` | (`result`: `unknown`) => `void` | A function to call with the final value of the stream. |
| `onError` | (`error`: `unknown`) => `void`  | -                                                      |

#### Returns

`TransformStream`\<`T`, [`BraintrustStreamChunk`](/docs/libs/nodejs#braintruststreamchunk)\>

A new stream that passes through the final value of the stream.

---

### currentExperiment

▸ **currentExperiment**(`options?`): [`Experiment`](/docs/libs/nodejs/classes/Experiment.md) \| `undefined`

Returns the currently-active experiment (set by [init](/docs/libs/nodejs#init)). Returns undefined if no current experiment has been set.

#### Parameters

| Name       | Type               |
| :--------- | :----------------- |
| `options?` | `OptionalStateArg` |

#### Returns

[`Experiment`](/docs/libs/nodejs/classes/Experiment.md) \| `undefined`

---

### currentLogger

▸ **currentLogger**\<`IsAsyncFlush`\>(`options?`): [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\> \| `undefined`

Returns the currently-active logger (set by [initLogger](/docs/libs/nodejs#initlogger)). Returns undefined if no current logger has been set.

#### Type parameters

| Name           | Type              |
| :------------- | :---------------- |
| `IsAsyncFlush` | extends `boolean` |

#### Parameters

| Name       | Type                                                   |
| :--------- | :----------------------------------------------------- |
| `options?` | `AsyncFlushArg`\<`IsAsyncFlush`\> & `OptionalStateArg` |

#### Returns

[`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\> \| `undefined`

---

### currentSpan

▸ **currentSpan**(`options?`): [`Span`](/docs/libs/nodejs/interfaces/Span.md)

Return the currently-active span for logging (set by one of the `traced` methods). If there is no active span, returns a no-op span object, which supports the same interface as spans but does no logging.

See [Span](/docs/libs/nodejs/interfaces/Span.md) for full details.

#### Parameters

| Name       | Type               |
| :--------- | :----------------- |
| `options?` | `OptionalStateArg` |

#### Returns

[`Span`](/docs/libs/nodejs/interfaces/Span.md)

---

### defaultErrorScoreHandler

▸ **defaultErrorScoreHandler**(`args`): `undefined` \| `void` \| `Record`\<`string`, `number`\>

#### Parameters

| Name                   | Type                                                            |
| :--------------------- | :-------------------------------------------------------------- |
| `args`                 | `Object`                                                        |
| `args.data`            | [`EvalCase`](/docs/libs/nodejs#evalcase)\<`any`, `any`, `any`\> |
| `args.rootSpan`        | [`Span`](/docs/libs/nodejs/interfaces/Span.md)                  |
| `args.unhandledScores` | `string`[]                                                      |

#### Returns

`undefined` \| `void` \| `Record`\<`string`, `number`\>

---

### deserializePlainStringAsJSON

▸ **deserializePlainStringAsJSON**(`s`): \{ `error`: `undefined` = undefined; `value`: `any` } \| \{ `error`: `unknown` = e; `value`: `string` = s }

#### Parameters

| Name | Type     |
| :--- | :------- |
| `s`  | `string` |

#### Returns

\{ `error`: `undefined` = undefined; `value`: `any` } \| \{ `error`: `unknown` = e; `value`: `string` = s }

---

### devNullWritableStream

▸ **devNullWritableStream**(): `WritableStream`

#### Returns

`WritableStream`

---

### flush

▸ **flush**(`options?`): `Promise`\<`void`\>

Flush any pending rows to the server.

#### Parameters

| Name       | Type               |
| :--------- | :----------------- |
| `options?` | `OptionalStateArg` |

#### Returns

`Promise`\<`void`\>

---

### getSpanParentObject

▸ **getSpanParentObject**\<`IsAsyncFlush`\>(`options?`): [`Span`](/docs/libs/nodejs/interfaces/Span.md) \| [`Experiment`](/docs/libs/nodejs/classes/Experiment.md) \| [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>

Mainly for internal use. Return the parent object for starting a span in a global context.

#### Type parameters

| Name           | Type              |
| :------------- | :---------------- |
| `IsAsyncFlush` | extends `boolean` |

#### Parameters

| Name       | Type                                                   |
| :--------- | :----------------------------------------------------- |
| `options?` | `AsyncFlushArg`\<`IsAsyncFlush`\> & `OptionalStateArg` |

#### Returns

[`Span`](/docs/libs/nodejs/interfaces/Span.md) \| [`Experiment`](/docs/libs/nodejs/classes/Experiment.md) \| [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>

---

### init

▸ **init**\<`IsOpen`\>(`options`): `InitializedExperiment`\<`IsOpen`\>

Log in, and then initialize a new experiment in a specified project. If the project does not exist, it will be created.

#### Type parameters

| Name     | Type                        |
| :------- | :-------------------------- |
| `IsOpen` | extends `boolean` = `false` |

#### Parameters

| Name      | Type                                                                             | Description                     |
| :-------- | :------------------------------------------------------------------------------- | :------------------------------ |
| `options` | `Readonly`\<[`FullInitOptions`](/docs/libs/nodejs#fullinitoptions)\<`IsOpen`\>\> | Options for configuring init(). |

#### Returns

`InitializedExperiment`\<`IsOpen`\>

The newly created Experiment.

▸ **init**\<`IsOpen`\>(`project`, `options?`): `InitializedExperiment`\<`IsOpen`\>

Legacy form of `init` which accepts the project name as the first parameter,
separately from the remaining options. See `init(options)` for full details.

#### Type parameters

| Name     | Type                        |
| :------- | :-------------------------- |
| `IsOpen` | extends `boolean` = `false` |

#### Parameters

| Name       | Type                                                                     |
| :--------- | :----------------------------------------------------------------------- |
| `project`  | `string`                                                                 |
| `options?` | `Readonly`\<[`InitOptions`](/docs/libs/nodejs#initoptions)\<`IsOpen`\>\> |

#### Returns

`InitializedExperiment`\<`IsOpen`\>

---

### initDataset

▸ **initDataset**\<`IsLegacyDataset`\>(`options`): [`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>

Create a new dataset in a specified project. If the project does not exist, it will be created.

#### Type parameters

| Name              | Type                        |
| :---------------- | :-------------------------- |
| `IsLegacyDataset` | extends `boolean` = `false` |

#### Parameters

| Name      | Type                                                        | Description                            |
| :-------- | :---------------------------------------------------------- | :------------------------------------- |
| `options` | `Readonly`\<`FullInitDatasetOptions`\<`IsLegacyDataset`\>\> | Options for configuring initDataset(). |

#### Returns

[`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>

The newly created Dataset.

▸ **initDataset**\<`IsLegacyDataset`\>(`project`, `options?`): [`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>

Legacy form of `initDataset` which accepts the project name as the first
parameter, separately from the remaining options.

See `initDataset(options)` for full details.

#### Type parameters

| Name              | Type                        |
| :---------------- | :-------------------------- |
| `IsLegacyDataset` | extends `boolean` = `false` |

#### Parameters

| Name       | Type                                                    |
| :--------- | :------------------------------------------------------ |
| `project`  | `string`                                                |
| `options?` | `Readonly`\<`InitDatasetOptions`\<`IsLegacyDataset`\>\> |

#### Returns

[`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>

---

### initExperiment

▸ **initExperiment**\<`IsOpen`\>(`options`): `InitializedExperiment`\<`IsOpen`\>

Alias for init(options).

#### Type parameters

| Name     | Type                        |
| :------- | :-------------------------- |
| `IsOpen` | extends `boolean` = `false` |

#### Parameters

| Name      | Type                                                                     |
| :-------- | :----------------------------------------------------------------------- |
| `options` | `Readonly`\<[`InitOptions`](/docs/libs/nodejs#initoptions)\<`IsOpen`\>\> |

#### Returns

`InitializedExperiment`\<`IsOpen`\>

▸ **initExperiment**\<`IsOpen`\>(`project`, `options?`): `InitializedExperiment`\<`IsOpen`\>

Alias for init(project, options).

#### Type parameters

| Name     | Type                        |
| :------- | :-------------------------- |
| `IsOpen` | extends `boolean` = `false` |

#### Parameters

| Name       | Type                                                                     |
| :--------- | :----------------------------------------------------------------------- |
| `project`  | `string`                                                                 |
| `options?` | `Readonly`\<[`InitOptions`](/docs/libs/nodejs#initoptions)\<`IsOpen`\>\> |

#### Returns

`InitializedExperiment`\<`IsOpen`\>

---

### initFunction

▸ **initFunction**(`options`): (`input`: `any`) => `Promise`\<`any`\>

Creates a function that can be used as a task or scorer in the Braintrust evaluation framework.
The returned function wraps a Braintrust function and can be passed directly to Eval().

When used as a task:

```ts
const myFunction = initFunction({projectName: "myproject", slug: "myfunction"});
await Eval("test", {
  task: myFunction,
  data: testData,
  scores: [...]
});
```

When used as a scorer:

```ts
const myScorer = initFunction({ projectName: "myproject", slug: "myscorer" });
await Eval("test", {
  task: someTask,
  data: testData,
  scores: [myScorer],
});
```

#### Parameters

| Name                  | Type     | Description                                                  |
| :-------------------- | :------- | :----------------------------------------------------------- |
| `options`             | `Object` | Options for the function.                                    |
| `options.projectName` | `string` | The project name containing the function.                    |
| `options.slug`        | `string` | The slug of the function to invoke.                          |
| `options.version?`    | `string` | Optional version of the function to use. Defaults to latest. |

#### Returns

`fn`

A function that can be used as a task or scorer in Eval().

▸ (`input`): `Promise`\<`any`\>

##### Parameters

| Name    | Type  |
| :------ | :---- |
| `input` | `any` |

##### Returns

`Promise`\<`any`\>

---

### initLogger

▸ **initLogger**\<`IsAsyncFlush`\>(`options?`): [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>

Create a new logger in a specified project. If the project does not exist, it will be created.

#### Type parameters

| Name           | Type                       |
| :------------- | :------------------------- |
| `IsAsyncFlush` | extends `boolean` = `true` |

#### Parameters

| Name      | Type                                                | Description                                |
| :-------- | :-------------------------------------------------- | :----------------------------------------- |
| `options` | `Readonly`\<`InitLoggerOptions`\<`IsAsyncFlush`\>\> | Additional options for configuring init(). |

#### Returns

[`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>

The newly created Logger.

---

### invoke

▸ **invoke**\<`Input`, `Output`, `Stream`\>(`args`): `Promise`\<[`InvokeReturn`](/docs/libs/nodejs#invokereturn)\<`Stream`, `Output`\>\>

Invoke a Braintrust function, returning a `BraintrustStream` or the value as a plain
Javascript object.

#### Type parameters

| Name     | Type                        |
| :------- | :-------------------------- |
| `Input`  | `Input`                     |
| `Output` | `Output`                    |
| `Stream` | extends `boolean` = `false` |

#### Parameters

| Name   | Type                                                                                                                                                                                                       | Description                                                                                                                     |
| :----- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------ |
| `args` | [`InvokeFunctionArgs`](/docs/libs/nodejs/interfaces/InvokeFunctionArgs.md)\<`Input`, `Output`, `Stream`\> & [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) & \{ `forceLogin?`: `boolean` } | The arguments for the function (see [InvokeFunctionArgs](/docs/libs/nodejs/interfaces/InvokeFunctionArgs.md) for more details). |

#### Returns

`Promise`\<[`InvokeReturn`](/docs/libs/nodejs#invokereturn)\<`Stream`, `Output`\>\>

The output of the function.

---

### loadPrompt

▸ **loadPrompt**(`options`): `Promise`\<[`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`true`, `true`\>\>

Load a prompt from the specified project.

#### Parameters

| Name      | Type                | Description                           |
| :-------- | :------------------ | :------------------------------------ |
| `options` | `LoadPromptOptions` | Options for configuring loadPrompt(). |

#### Returns

`Promise`\<[`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`true`, `true`\>\>

The prompt object.

**`Throws`**

If the prompt is not found.

**`Throws`**

If multiple prompts are found with the same slug in the same project (this should never happen).

**`Example`**

```javascript
const prompt = await loadPrompt({
  projectName: "My Project",
  slug: "my-prompt",
});
```

---

### log

▸ **log**(`event`): `string`

Log a single event to the current experiment. The event will be batched and uploaded behind the scenes.

#### Parameters

| Name    | Type                                                               | Description                                                                                           |
| :------ | :----------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------- |
| `event` | [`ExperimentLogFullArgs`](/docs/libs/nodejs#experimentlogfullargs) | The event to log. See [Experiment.log](/docs/libs/nodejs/classes/Experiment.md#log) for full details. |

#### Returns

`string`

The `id` of the logged event.

---

### logError

▸ **logError**(`span`, `error`): `void`

#### Parameters

| Name    | Type                                           |
| :------ | :--------------------------------------------- |
| `span`  | [`Span`](/docs/libs/nodejs/interfaces/Span.md) |
| `error` | `unknown`                                      |

#### Returns

`void`

---

### login

▸ **login**(`options?`): `Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

Log into Braintrust. This will prompt you for your API token, which you can find at
https://www.braintrust.dev/app/token. This method is called automatically by `init()`.

#### Parameters

| Name      | Type                                                                                           | Description                      |
| :-------- | :--------------------------------------------------------------------------------------------- | :------------------------------- |
| `options` | [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) & \{ `forceLogin?`: `boolean` } | Options for configuring login(). |

#### Returns

`Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

---

### loginToState

▸ **loginToState**(`options?`): `Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

#### Parameters

| Name      | Type                                                           |
| :-------- | :------------------------------------------------------------- |
| `options` | [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) |

#### Returns

`Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>

---

### newId

▸ **newId**(): `string`

#### Returns

`string`

---

### parseCachedHeader

▸ **parseCachedHeader**(`value`): `number` \| `undefined`

#### Parameters

| Name    | Type                              |
| :------ | :-------------------------------- |
| `value` | `undefined` \| `null` \| `string` |

#### Returns

`number` \| `undefined`

---

### permalink

▸ **permalink**(`slug`, `opts?`): `Promise`\<`string`\>

Format a permalink to the Braintrust application for viewing the span
represented by the provided `slug`.

Links can be generated at any time, but they will only become viewable after
the span and its root have been flushed to the server and ingested.

If you have a `Span` object, use [Span.link](/docs/libs/nodejs/interfaces/Span.md#link) instead.

#### Parameters

| Name            | Type                                                              | Description                                                                               |
| :-------------- | :---------------------------------------------------------------- | :---------------------------------------------------------------------------------------- |
| `slug`          | `string`                                                          | The identifier generated from [Span.export](/docs/libs/nodejs/interfaces/Span.md#export). |
| `opts?`         | `Object`                                                          | Optional arguments.                                                                       |
| `opts.appUrl?`  | `string`                                                          | The app URL to use. If not provided, the app URL will be inferred from the state.         |
| `opts.orgName?` | `string`                                                          | The org name to use. If not provided, the org name will be inferred from the state.       |
| `opts.state?`   | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) | The login state to use. If not provided, the global state will be used.                   |

#### Returns

`Promise`\<`string`\>

A permalink to the exported span.

---

### promptDefinitionToPromptData

▸ **promptDefinitionToPromptData**(`promptDefinition`, `rawTools?`): `PromptData`

#### Parameters

| Name                       | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| :------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `promptDefinition`         | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| `promptDefinition.model`   | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| `promptDefinition.params?` | `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> |
| `rawTools?`                | \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |

#### Returns

`PromptData`

---

### renderMessage

▸ **renderMessage**\<`T`\>(`render`, `message`): `T`

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| :--- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends \{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` } |

#### Parameters

| Name      | Type                               |
| :-------- | :--------------------------------- |
| `render`  | (`template`: `string`) => `string` |
| `message` | `T`                                |

#### Returns

`T`

---

### renderPromptParams

▸ **renderPromptParams**(`params`, `args`, `options`): `ModelParams` \| `undefined`

#### Parameters

| Name              | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| :---------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `params`          | `undefined` \| `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> |
| `args`            | `Record`\<`string`, `unknown`\>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| `options`         | `Object`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| `options.strict?` | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |

#### Returns

`ModelParams` \| `undefined`

---

### reportFailures

▸ **reportFailures**\<`Input`, `Output`, `Expected`, `Metadata`\>(`evaluator`, `failingResults`, `«destructured»`): `void`

#### Type parameters

| Name       | Type                                                     |
| :--------- | :------------------------------------------------------- |
| `Input`    | `Input`                                                  |
| `Output`   | `Output`                                                 |
| `Expected` | `Expected`                                               |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) |

#### Parameters

| Name             | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `evaluator`      | [`EvaluatorDef`](/docs/libs/nodejs#evaluatordef)\<`Input`, `Output`, `Expected`, `Metadata`, `Record`\<`string`, \{ `default?`: \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] } ; `description?`: `string` ; `type`: `"prompt"` } \| `ZodType`\<`unknown`, `ZodTypeDef`, `unknown`\>\>\> |
| `failingResults` | [`EvalResult`](/docs/libs/nodejs#evalresult)\<`Input`, `Output`, `Expected`, `Metadata`\>[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| `«destructured»` | `ReporterOpts`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

#### Returns

`void`

---

### runEvaluator

▸ **runEvaluator**(`experiment`, `evaluator`, `progressReporter`, `filters`, `stream`, `parameters?`): `Promise`\<[`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`any`, `any`, `any`, `any`\>\>

#### Parameters

| Name               | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| :----------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `experiment`       | `null` \| [`Experiment`](/docs/libs/nodejs/classes/Experiment.md)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| `evaluator`        | [`EvaluatorDef`](/docs/libs/nodejs#evaluatordef)\<`any`, `any`, `any`, `any`, `any`\>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| `progressReporter` | `ProgressReporter`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| `filters`          | `Filter`[]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| `stream`           | `undefined` \| (`data`: \{ `data`: `string` ; `event`: `"start"` \| `"error"` \| `"text_delta"` \| `"reasoning_delta"` \| `"json_delta"` \| `"console"` \| `"progress"` \| `"done"` ; `format`: `"code"` \| `"global"` \| `"graph"` \| `"llm"` ; `id`: `string` ; `name`: `string` ; `object_type`: `"prompt"` \| `"tool"` \| `"scorer"` \| `"task"` \| `"agent"` ; `origin?`: `null` \| \{ `_xact_id?`: `null` \| `string` ; `created?`: `null` \| `string` ; `id`: `string` ; `object_id`: `string` ; `object_type`: `"function"` \| `"prompt"` \| `"experiment"` \| `"dataset"` \| `"prompt_session"` \| `"project_logs"` } ; `output_type`: `"completion"` \| `"score"` \| `"any"` }) => `void`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| `parameters?`      | `InferParameters`\<`Record`\<`string`, \{ `default?`: \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] } ; `description?`: `string` ; `type`: `"prompt"` } \| `ZodType`\<`unknown`, `ZodTypeDef`, `unknown`\>\>\> |

#### Returns

`Promise`\<[`EvalResultWithSummary`](/docs/libs/nodejs/classes/EvalResultWithSummary.md)\<`any`, `any`, `any`, `any`\>\>

---

### setFetch

▸ **setFetch**(`fetch`): `void`

Set the fetch implementation to use for requests. You can specify it here,
or when you call `login`.

#### Parameters

| Name    | Type                                                                                                                                                                             | Description                                                       |
| :------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------- |
| `fetch` | (`input`: `URL` \| `RequestInfo`, `init?`: `RequestInit`) => `Promise`\<`Response`\>(`input`: `string` \| `URL` \| `Request`, `init?`: `RequestInit`) => `Promise`\<`Response`\> | [MDN Reference](https://developer.mozilla.org/docs/Web/API/fetch) |

#### Returns

`void`

---

### spanComponentsToObjectId

▸ **spanComponentsToObjectId**(`«destructured»`): `Promise`\<`string`\>

#### Parameters

| Name             | Type                                                              |
| :--------------- | :---------------------------------------------------------------- |
| `«destructured»` | `Object`                                                          |
| › `components`   | `SpanComponentsV3`                                                |
| › `state?`       | [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) |

#### Returns

`Promise`\<`string`\>

---

### startSpan

▸ **startSpan**\<`IsAsyncFlush`\>(`args?`): [`Span`](/docs/libs/nodejs/interfaces/Span.md)

Lower-level alternative to `traced`. This allows you to start a span yourself, and can be useful in situations
where you cannot use callbacks. However, spans started with `startSpan` will not be marked as the "current span",
so `currentSpan()` and `traced()` will be no-ops. If you want to mark a span as current, use `traced` instead.

See [traced](/docs/libs/nodejs#traced) for full details.

#### Type parameters

| Name           | Type                       |
| :------------- | :------------------------- |
| `IsAsyncFlush` | extends `boolean` = `true` |

#### Parameters

| Name    | Type                                                                                                        |
| :------ | :---------------------------------------------------------------------------------------------------------- |
| `args?` | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) & `AsyncFlushArg`\<`IsAsyncFlush`\> & `OptionalStateArg` |

#### Returns

[`Span`](/docs/libs/nodejs/interfaces/Span.md)

---

### summarize

▸ **summarize**(`options?`): `Promise`\<[`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)\>

Summarize the current experiment, including the scores (compared to the closest reference experiment) and metadata.

#### Parameters

| Name                              | Type      | Description                                                                                                      |
| :-------------------------------- | :-------- | :--------------------------------------------------------------------------------------------------------------- |
| `options`                         | `Object`  | Options for summarizing the experiment.                                                                          |
| `options.comparisonExperimentId?` | `string`  | The experiment to compare against. If None, the most recent experiment on the origin's main branch will be used. |
| `options.summarizeScores?`        | `boolean` | Whether to summarize the scores. If False, only the metadata will be returned.                                   |

#### Returns

`Promise`\<[`ExperimentSummary`](/docs/libs/nodejs/interfaces/ExperimentSummary.md)\>

A summary of the experiment, including the scores (compared to the closest reference experiment) and metadata.

---

### traceable

▸ **traceable**\<`F`, `IsAsyncFlush`\>(`fn`, `args?`): `IsAsyncFlush` extends `false` ? (...`args`: `Parameters`\<`F`\>) => `Promise`\<`Awaited`\<`ReturnType`\<`F`\>\>\> : `F`

A synonym for `wrapTraced`. If you're porting from systems that use `traceable`, you can use this to
make your codebase more consistent.

#### Type parameters

| Name           | Type                                  |
| :------------- | :------------------------------------ |
| `F`            | extends (...`args`: `any`[]) => `any` |
| `IsAsyncFlush` | extends `boolean` = `true`            |

#### Parameters

| Name    | Type                                                                                                                                        |
| :------ | :------------------------------------------------------------------------------------------------------------------------------------------ |
| `fn`    | `F`                                                                                                                                         |
| `args?` | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg) & `AsyncFlushArg`\<`IsAsyncFlush`\> |

#### Returns

`IsAsyncFlush` extends `false` ? (...`args`: `Parameters`\<`F`\>) => `Promise`\<`Awaited`\<`ReturnType`\<`F`\>\>\> : `F`

---

### traced

▸ **traced**\<`IsAsyncFlush`, `R`\>(`callback`, `args?`): [`PromiseUnless`](/docs/libs/nodejs#promiseunless)\<`IsAsyncFlush`, `R`\>

Toplevel function for starting a span. It checks the following (in precedence order):

- Currently-active span
- Currently-active experiment
- Currently-active logger

and creates a span under the first one that is active. Alternatively, if `parent` is specified, it creates a span under the specified parent row. If none of these are active, it returns a no-op span object.

See [Span.traced](/docs/libs/nodejs/interfaces/Span.md#traced) for full details.

#### Type parameters

| Name           | Type                       |
| :------------- | :------------------------- |
| `IsAsyncFlush` | extends `boolean` = `true` |
| `R`            | `void`                     |

#### Parameters

| Name       | Type                                                                                                                                                             |
| :--------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `callback` | (`span`: [`Span`](/docs/libs/nodejs/interfaces/Span.md)) => `R`                                                                                                  |
| `args?`    | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg) & `AsyncFlushArg`\<`IsAsyncFlush`\> & `OptionalStateArg` |

#### Returns

[`PromiseUnless`](/docs/libs/nodejs#promiseunless)\<`IsAsyncFlush`, `R`\>

---

### updateSpan

▸ **updateSpan**(`«destructured»`): `void`

Update a span using the output of `span.export()`. It is important that you only resume updating
to a span once the original span has been fully written and flushed, since otherwise updates to
the span may conflict with the original span.

#### Parameters

| Name             | Type                                                                                              |
| :--------------- | :------------------------------------------------------------------------------------------------ |
| `«destructured»` | \{ `exported`: `string` } & `Omit`\<`Partial`\<`ExperimentEvent`\>, `"id"`\> & `OptionalStateArg` |

#### Returns

`void`

---

### withCurrent

▸ **withCurrent**\<`R`\>(`span`, `callback`, `state?`): `R`

Runs the provided callback with the span as the current span.

#### Type parameters

| Name |
| :--- |
| `R`  |

#### Parameters

| Name       | Type                                                                             | Default value |
| :--------- | :------------------------------------------------------------------------------- | :------------ |
| `span`     | [`Span`](/docs/libs/nodejs/interfaces/Span.md)                                   | `undefined`   |
| `callback` | (`span`: [`Span`](/docs/libs/nodejs/interfaces/Span.md)) => `R`                  | `undefined`   |
| `state`    | `undefined` \| [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) | `undefined`   |

#### Returns

`R`

---

### withDataset

▸ **withDataset**\<`R`, `IsLegacyDataset`\>(`project`, `callback`, `options?`): `R`

#### Type parameters

| Name              | Type                        |
| :---------------- | :-------------------------- |
| `R`               | `R`                         |
| `IsLegacyDataset` | extends `boolean` = `false` |

#### Parameters

| Name       | Type                                                                                       |
| :--------- | :----------------------------------------------------------------------------------------- |
| `project`  | `string`                                                                                   |
| `callback` | (`dataset`: [`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`IsLegacyDataset`\>) => `R` |
| `options`  | `Readonly`\<`InitDatasetOptions`\<`IsLegacyDataset`\>\>                                    |

#### Returns

`R`

**`Deprecated`**

Use [initDataset](/docs/libs/nodejs#initdataset) instead.

---

### withExperiment

▸ **withExperiment**\<`R`\>(`project`, `callback`, `options?`): `R`

#### Type parameters

| Name |
| :--- |
| `R`  |

#### Parameters

| Name       | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `project`  | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| `callback` | (`experiment`: [`Experiment`](/docs/libs/nodejs/classes/Experiment.md)) => `R`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| `options`  | `Readonly`\<[`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) & \{ `forceLogin?`: `boolean` } & \{ `baseExperiment?`: `string` ; `baseExperimentId?`: `string` ; `dataset?`: [`AnyDataset`](/docs/libs/nodejs#anydataset) ; `description?`: `string` ; `experiment?`: `string` ; `gitMetadataSettings?`: \{ `collect`: `"some"` \| `"none"` \| `"all"` ; `fields?`: (`"dirty"` \| `"tag"` \| `"commit"` \| `"branch"` \| `"author_name"` \| `"author_email"` \| `"commit_message"` \| `"commit_time"` \| `"git_diff"`)[] } ; `isPublic?`: `boolean` ; `metadata?`: `Record`\<`string`, `unknown`\> ; `projectId?`: `string` ; `repoInfo?`: \{ `author_email?`: `null` \| `string` ; `author_name?`: `null` \| `string` ; `branch?`: `null` \| `string` ; `commit?`: `null` \| `string` ; `commit_message?`: `null` \| `string` ; `commit_time?`: `null` \| `string` ; `dirty?`: `null` \| `boolean` ; `git_diff?`: `null` \| `string` ; `tag?`: `null` \| `string` } ; `setCurrent?`: `boolean` ; `state?`: [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) ; `update?`: `boolean` } & `InitOpenOption`\<`false`\> & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg)\> |

#### Returns

`R`

**`Deprecated`**

Use [init](/docs/libs/nodejs#init) instead.

---

### withLogger

▸ **withLogger**\<`IsAsyncFlush`, `R`\>(`callback`, `options?`): `R`

#### Type parameters

| Name           | Type                        |
| :------------- | :-------------------------- |
| `IsAsyncFlush` | extends `boolean` = `false` |
| `R`            | `void`                      |

#### Parameters

| Name       | Type                                                                                                                                                                                                                                                                                                                                                                                                                  |
| :--------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `callback` | (`logger`: [`Logger`](/docs/libs/nodejs/classes/Logger.md)\<`IsAsyncFlush`\>) => `R`                                                                                                                                                                                                                                                                                                                                  |
| `options`  | `Readonly`\<[`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) & \{ `forceLogin?`: `boolean` } & \{ `orgProjectMetadata?`: `OrgProjectMetadata` ; `projectId?`: `string` ; `projectName?`: `string` ; `setCurrent?`: `boolean` ; `state?`: [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) } & `AsyncFlushArg`\<`IsAsyncFlush`\> & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg)\> |

#### Returns

`R`

**`Deprecated`**

Use [initLogger](/docs/libs/nodejs#initlogger) instead.

---

### withParent

▸ **withParent**\<`R`\>(`parent`, `callback`, `state?`): `R`

#### Type parameters

| Name |
| :--- |
| `R`  |

#### Parameters

| Name       | Type                                                                             | Default value |
| :--------- | :------------------------------------------------------------------------------- | :------------ |
| `parent`   | `string`                                                                         | `undefined`   |
| `callback` | () => `R`                                                                        | `undefined`   |
| `state`    | `undefined` \| [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) | `undefined`   |

#### Returns

`R`

---

### wrapAISDKModel

▸ **wrapAISDKModel**\<`T`\>(`model`): `T`

Wrap an ai-sdk model (created with `.chat()`, `.completion()`, etc.) to add tracing. If Braintrust is
not configured, this is a no-op

#### Type parameters

| Name | Type             |
| :--- | :--------------- |
| `T`  | extends `object` |

#### Parameters

| Name    | Type |
| :------ | :--- |
| `model` | `T`  |

#### Returns

`T`

The wrapped object.

---

### wrapAnthropic

▸ **wrapAnthropic**\<`T`\>(`anthropic`): `T`

Wrap an `Anthropic` object (created with `new Anthropic(...)`) to add tracing. If Braintrust is
not configured, nothing will be traced. If this is not an `Anthropic` object, this function is
a no-op.

Currently, this only supports the `v4` API.

#### Type parameters

| Name | Type             |
| :--- | :--------------- |
| `T`  | extends `object` |

#### Parameters

| Name        | Type |
| :---------- | :--- |
| `anthropic` | `T`  |

#### Returns

`T`

The wrapped `Anthropic` object.

---

### wrapOpenAI

▸ **wrapOpenAI**\<`T`\>(`openai`): `T`

Wrap an `OpenAI` object (created with `new OpenAI(...)`) to add tracing. If Braintrust is
not configured, nothing will be traced. If this is not an `OpenAI` object, this function is
a no-op.

Currently, this only supports the `v4` API.

#### Type parameters

| Name | Type             |
| :--- | :--------------- |
| `T`  | extends `object` |

#### Parameters

| Name     | Type |
| :------- | :--- |
| `openai` | `T`  |

#### Returns

`T`

The wrapped `OpenAI` object.

---

### wrapOpenAIv4

▸ **wrapOpenAIv4**\<`T`\>(`openai`): `T`

#### Type parameters

| Name | Type                 |
| :--- | :------------------- |
| `T`  | extends `OpenAILike` |

#### Parameters

| Name     | Type |
| :------- | :--- |
| `openai` | `T`  |

#### Returns

`T`

---

### wrapTraced

▸ **wrapTraced**\<`F`, `IsAsyncFlush`\>(`fn`, `args?`): `IsAsyncFlush` extends `false` ? (...`args`: `Parameters`\<`F`\>) => `Promise`\<`Awaited`\<`ReturnType`\<`F`\>\>\> : `F`

Wrap a function with `traced`, using the arguments as `input` and return value as `output`.
Any functions wrapped this way will automatically be traced, similar to the `@traced` decorator
in Python. If you want to correctly propagate the function's name and define it in one go, then
you can do so like this:

```ts
const myFunc = wrapTraced(async function myFunc(input) {
  const result = await client.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: input }],
  });
  return result.choices[0].message.content ?? "unknown";
});
```

Now, any calls to `myFunc` will be traced, and the input and output will be logged automatically.
If tracing is inactive, i.e. there is no active logger or experiment, it's just a no-op.

If you're using NextJS or another framework that minifies your code before deployment, the function name will be obfuscated when deployed. To trace the name properly, you can specify it in the span-level arguments for `wrapTraced` like so.

```ts
const myFunc = wrapTraced(
  async function myFunc(input) {
    const result = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: input }],
    });
    return result.choices[0].message.content ?? "unknown";
  },
  { name: "myFunc" },
);
```

#### Type parameters

| Name           | Type                                  |
| :------------- | :------------------------------------ |
| `F`            | extends (...`args`: `any`[]) => `any` |
| `IsAsyncFlush` | extends `boolean` = `true`            |

#### Parameters

| Name    | Type                                                                                                                                        | Description                                                            |
| :------ | :------------------------------------------------------------------------------------------------------------------------------------------ | :--------------------------------------------------------------------- |
| `fn`    | `F`                                                                                                                                         | The function to wrap.                                                  |
| `args?` | [`StartSpanArgs`](/docs/libs/nodejs#startspanargs) & [`SetCurrentArg`](/docs/libs/nodejs#setcurrentarg) & `AsyncFlushArg`\<`IsAsyncFlush`\> | Span-level arguments (e.g. a custom name or type) to pass to `traced`. |

#### Returns

`IsAsyncFlush` extends `false` ? (...`args`: `Parameters`\<`F`\>) => `Promise`\<`Awaited`\<`ReturnType`\<`F`\>\>\> : `F`

The wrapped function.

## Type Aliases

### AnyDataset

Ƭ **AnyDataset**: [`Dataset`](/docs/libs/nodejs/classes/Dataset.md)\<`boolean`\>

---

### BaseExperiment

Ƭ **BaseExperiment**\<`Input`, `Expected`, `Metadata`\>: `Object`

#### Type parameters

| Name       | Type                                                                                                                      |
| :--------- | :------------------------------------------------------------------------------------------------------------------------ |
| `Input`    | `Input`                                                                                                                   |
| `Expected` | `Expected`                                                                                                                |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = [`DefaultMetadataType`](/docs/libs/nodejs#defaultmetadatatype) |

#### Type declaration

| Name        | Type                              |
| :---------- | :-------------------------------- |
| `_phantom?` | [`Input`, `Expected`, `Metadata`] |
| `_type`     | `"BaseExperiment"`                |
| `name?`     | `string`                          |

---

### BaseMetadata

Ƭ **BaseMetadata**: `Record`\<`string`, `unknown`\> \| `void`

---

### BraintrustStreamChunk

Ƭ **BraintrustStreamChunk**: `z.infer`\<typeof [`braintrustStreamChunkSchema`](/docs/libs/nodejs#braintruststreamchunkschema)\>

A chunk of data from a Braintrust stream. Each chunk type matches
an SSE event type.

---

### ChatPrompt

Ƭ **ChatPrompt**: `Object`

#### Type declaration

| Name       | Type              |
| :--------- | :---------------- |
| `messages` | `OpenAIMessage`[] |
| `tools?`   | `any`[]           |

---

### CodeOpts

Ƭ **CodeOpts**\<`Params`, `Returns`, `Fn`\>: `Partial`\<`BaseFnOpts`\> & \{ `handler`: `Fn` } & `Schema`\<`Params`, `Returns`\>

#### Type parameters

| Name      | Type                                             |
| :-------- | :----------------------------------------------- |
| `Params`  | `Params`                                         |
| `Returns` | `Returns`                                        |
| `Fn`      | extends `GenericFunction`\<`Params`, `Returns`\> |

---

### CommentEvent

Ƭ **CommentEvent**: [`IdField`](/docs/libs/nodejs#idfield) & \{ `_audit_metadata?`: `Record`\<`string`, `unknown`\> ; `_audit_source`: `Source` ; `comment`: \{ `text`: `string` } ; `created`: `string` ; `origin`: \{ `id`: `string` } } & [`ParentExperimentIds`](/docs/libs/nodejs/interfaces/ParentExperimentIds.md) \| [`ParentProjectLogIds`](/docs/libs/nodejs/interfaces/ParentProjectLogIds.md) \| `ParentPlaygroundLogIds`

---

### CompiledPrompt

Ƭ **CompiledPrompt**\<`Flavor`\>: [`CompiledPromptParams`](/docs/libs/nodejs#compiledpromptparams) & \{ `span_info?`: \{ `metadata`: \{ `prompt`: \{ `id`: `string` ; `project_id`: `string` ; `variables`: `Record`\<`string`, `unknown`\> ; `version`: `string` } } ; `name?`: `string` ; `spanAttributes?`: `Record`\<`any`, `any`\> } } & `Flavor` extends `"chat"` ? [`ChatPrompt`](/docs/libs/nodejs#chatprompt) : `Flavor` extends `"completion"` ? [`CompletionPrompt`](/docs/libs/nodejs#completionprompt) : {}

#### Type parameters

| Name     | Type                               |
| :------- | :--------------------------------- |
| `Flavor` | extends `"chat"` \| `"completion"` |

---

### CompiledPromptParams

Ƭ **CompiledPromptParams**: `Omit`\<`NonNullable`\<`PromptData`[``"options"``]\>[``"params"``], `"use_cache"`\> & \{ `model`: `NonNullable`\<`NonNullable`\<`PromptData`[``"options"``]\>[``"model"``]\> }

---

### CompletionPrompt

Ƭ **CompletionPrompt**: `Object`

#### Type declaration

| Name     | Type     |
| :------- | :------- |
| `prompt` | `string` |

---

### CreateProjectOpts

Ƭ **CreateProjectOpts**: `NameOrId`

---

### DatasetRecord

Ƭ **DatasetRecord**\<`IsLegacyDataset`\>: `IsLegacyDataset` extends `true` ? `LegacyDatasetRecord` : `NewDatasetRecord`

#### Type parameters

| Name              | Type                                                   |
| :---------------- | :----------------------------------------------------- |
| `IsLegacyDataset` | extends `boolean` = typeof `DEFAULT_IS_LEGACY_DATASET` |

---

### DefaultMetadataType

Ƭ **DefaultMetadataType**: `void`

---

### DefaultPromptArgs

Ƭ **DefaultPromptArgs**: `Partial`\<[`CompiledPromptParams`](/docs/libs/nodejs#compiledpromptparams) & `AnyModelParam` & [`ChatPrompt`](/docs/libs/nodejs#chatprompt) & [`CompletionPrompt`](/docs/libs/nodejs#completionprompt)\>

---

### EndSpanArgs

Ƭ **EndSpanArgs**: `Object`

#### Type declaration

| Name       | Type     |
| :--------- | :------- |
| `endTime?` | `number` |

---

### EvalCase

Ƭ **EvalCase**\<`Input`, `Expected`, `Metadata`\>: \{ `_xact_id?`: `TransactionId` ; `created?`: `string` \| `null` ; `id?`: `string` ; `input`: `Input` ; `tags?`: `string`[] ; `upsert_id?`: `string` } & `Expected` extends `void` ? `object` : \{ `expected`: `Expected` } & `Metadata` extends `void` ? `object` : \{ `metadata`: `Metadata` }

#### Type parameters

| Name       |
| :--------- |
| `Input`    |
| `Expected` |
| `Metadata` |

---

### EvalParameterSerializedSchema

Ƭ **EvalParameterSerializedSchema**: `z.infer`\<typeof `evalParametersSerializedSchema`\>

---

### EvalParameters

Ƭ **EvalParameters**: `z.infer`\<typeof `evalParametersSchema`\>

---

### EvalResult

Ƭ **EvalResult**\<`Input`, `Output`, `Expected`, `Metadata`\>: [`EvalCase`](/docs/libs/nodejs#evalcase)\<`Input`, `Expected`, `Metadata`\> & \{ `error`: `unknown` ; `origin?`: `ObjectReference` ; `output`: `Output` ; `scores`: `Record`\<`string`, `number` \| `null`\> }

#### Type parameters

| Name       | Type                                                                                                                      |
| :--------- | :------------------------------------------------------------------------------------------------------------------------ |
| `Input`    | `Input`                                                                                                                   |
| `Output`   | `Output`                                                                                                                  |
| `Expected` | `Expected`                                                                                                                |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = [`DefaultMetadataType`](/docs/libs/nodejs#defaultmetadatatype) |

---

### EvalScorer

Ƭ **EvalScorer**\<`Input`, `Output`, `Expected`, `Metadata`\>: (`args`: [`EvalScorerArgs`](/docs/libs/nodejs#evalscorerargs)\<`Input`, `Output`, `Expected`, `Metadata`\>) => `OneOrMoreScores` \| `Promise`\<`OneOrMoreScores`\>

#### Type parameters

| Name       | Type                                                                                                                      |
| :--------- | :------------------------------------------------------------------------------------------------------------------------ |
| `Input`    | `Input`                                                                                                                   |
| `Output`   | `Output`                                                                                                                  |
| `Expected` | `Expected`                                                                                                                |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = [`DefaultMetadataType`](/docs/libs/nodejs#defaultmetadatatype) |

#### Type declaration

▸ (`args`): `OneOrMoreScores` \| `Promise`\<`OneOrMoreScores`\>

##### Parameters

| Name   | Type                                                                                              |
| :----- | :------------------------------------------------------------------------------------------------ |
| `args` | [`EvalScorerArgs`](/docs/libs/nodejs#evalscorerargs)\<`Input`, `Output`, `Expected`, `Metadata`\> |

##### Returns

`OneOrMoreScores` \| `Promise`\<`OneOrMoreScores`\>

---

### EvalScorerArgs

Ƭ **EvalScorerArgs**\<`Input`, `Output`, `Expected`, `Metadata`\>: [`EvalCase`](/docs/libs/nodejs#evalcase)\<`Input`, `Expected`, `Metadata`\> & \{ `output`: `Output` }

#### Type parameters

| Name       | Type                                                                                                                      |
| :--------- | :------------------------------------------------------------------------------------------------------------------------ |
| `Input`    | `Input`                                                                                                                   |
| `Output`   | `Output`                                                                                                                  |
| `Expected` | `Expected`                                                                                                                |
| `Metadata` | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = [`DefaultMetadataType`](/docs/libs/nodejs#defaultmetadatatype) |

---

### EvalTask

Ƭ **EvalTask**\<`Input`, `Output`, `Expected`, `Metadata`, `Parameters`\>: (`input`: `Input`, `hooks`: [`EvalHooks`](/docs/libs/nodejs/interfaces/EvalHooks.md)\<`Expected`, `Metadata`, `Parameters`\>) => `Promise`\<`Output`\> \| (`input`: `Input`, `hooks`: [`EvalHooks`](/docs/libs/nodejs/interfaces/EvalHooks.md)\<`Expected`, `Metadata`, `Parameters`\>) => `Output`

#### Type parameters

| Name         | Type                                                         |
| :----------- | :----------------------------------------------------------- |
| `Input`      | `Input`                                                      |
| `Output`     | `Output`                                                     |
| `Expected`   | `Expected`                                                   |
| `Metadata`   | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata)     |
| `Parameters` | extends [`EvalParameters`](/docs/libs/nodejs#evalparameters) |

---

### EvaluatorDef

Ƭ **EvaluatorDef**\<`Input`, `Output`, `Expected`, `Metadata`, `Parameters`\>: \{ `evalName`: `string` ; `projectName`: `string` } & [`Evaluator`](/docs/libs/nodejs/interfaces/Evaluator.md)\<`Input`, `Output`, `Expected`, `Metadata`, `Parameters`\>

#### Type parameters

| Name         | Type                                                                                                                      |
| :----------- | :------------------------------------------------------------------------------------------------------------------------ |
| `Input`      | `Input`                                                                                                                   |
| `Output`     | `Output`                                                                                                                  |
| `Expected`   | `Expected`                                                                                                                |
| `Metadata`   | extends [`BaseMetadata`](/docs/libs/nodejs#basemetadata) = [`DefaultMetadataType`](/docs/libs/nodejs#defaultmetadatatype) |
| `Parameters` | extends [`EvalParameters`](/docs/libs/nodejs#evalparameters) = [`EvalParameters`](/docs/libs/nodejs#evalparameters)       |

---

### EvaluatorDefinition

Ƭ **EvaluatorDefinition**: `z.infer`\<typeof [`evaluatorDefinitionSchema`](/docs/libs/nodejs#evaluatordefinitionschema)\>

---

### EvaluatorDefinitions

Ƭ **EvaluatorDefinitions**: `z.infer`\<typeof [`evaluatorDefinitionsSchema`](/docs/libs/nodejs#evaluatordefinitionsschema)\>

---

### EvaluatorFile

Ƭ **EvaluatorFile**: `Object`

#### Type declaration

| Name         | Type                                                                                                                                                                                                                                                                                            |
| :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `evaluators` | \{ `[evalName: string]`: \{ `evaluator`: [`EvaluatorDef`](/docs/libs/nodejs#evaluatordef)\<`unknown`, `unknown`, `unknown`, [`BaseMetadata`](/docs/libs/nodejs#basemetadata), [`EvalParameters`](/docs/libs/nodejs#evalparameters)\> ; `reporter?`: `ReporterDef`\<`unknown`\> \| `string` }; } |
| `functions`  | [`CodeFunction`](/docs/libs/nodejs/classes/CodeFunction.md)\<`unknown`, `unknown`, `GenericFunction`\<`unknown`, `unknown`\>\>[]                                                                                                                                                                |
| `prompts`    | [`CodePrompt`](/docs/libs/nodejs/classes/CodePrompt.md)[]                                                                                                                                                                                                                                       |
| `reporters`  | \{ `[reporterName: string]`: `ReporterDef`\<`unknown`\>; }                                                                                                                                                                                                                                      |

---

### EvaluatorManifest

Ƭ **EvaluatorManifest**: `Record`\<`string`, [`EvaluatorDef`](/docs/libs/nodejs#evaluatordef)\<`unknown`, `unknown`, `unknown`, [`BaseMetadata`](/docs/libs/nodejs#basemetadata)\>\>

---

### ExperimentLogFullArgs

Ƭ **ExperimentLogFullArgs**: `Partial`\<`Omit`\<[`OtherExperimentLogFields`](/docs/libs/nodejs#otherexperimentlogfields), `"output"` \| `"scores"`\>\> & `Required`\<`Pick`\<[`OtherExperimentLogFields`](/docs/libs/nodejs#otherexperimentlogfields), `"output"` \| `"scores"`\>\> & `Partial`\<[`InputField`](/docs/libs/nodejs#inputfield)\> & `Partial`\<[`IdField`](/docs/libs/nodejs#idfield)\>

---

### ExperimentLogPartialArgs

Ƭ **ExperimentLogPartialArgs**: `Partial`\<[`OtherExperimentLogFields`](/docs/libs/nodejs#otherexperimentlogfields)\> & `Partial`\<[`InputField`](/docs/libs/nodejs#inputfield)\>

---

### FullInitOptions

Ƭ **FullInitOptions**\<`IsOpen`\>: \{ `project?`: `string` } & [`InitOptions`](/docs/libs/nodejs#initoptions)\<`IsOpen`\>

#### Type parameters

| Name     | Type              |
| :------- | :---------------- |
| `IsOpen` | extends `boolean` |

---

### FullLoginOptions

Ƭ **FullLoginOptions**: [`LoginOptions`](/docs/libs/nodejs/interfaces/LoginOptions.md) & \{ `forceLogin?`: `boolean` }

---

### IdField

Ƭ **IdField**: `Object`

#### Type declaration

| Name | Type     |
| :--- | :------- |
| `id` | `string` |

---

### InitOptions

Ƭ **InitOptions**\<`IsOpen`\>: [`FullLoginOptions`](/docs/libs/nodejs#fullloginoptions) & \{ `baseExperiment?`: `string` ; `baseExperimentId?`: `string` ; `dataset?`: [`AnyDataset`](/docs/libs/nodejs#anydataset) ; `description?`: `string` ; `experiment?`: `string` ; `gitMetadataSettings?`: `GitMetadataSettings` ; `isPublic?`: `boolean` ; `metadata?`: `Record`\<`string`, `unknown`\> ; `projectId?`: `string` ; `repoInfo?`: `RepoInfo` ; `setCurrent?`: `boolean` ; `state?`: [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md) ; `update?`: `boolean` } & `InitOpenOption`\<`IsOpen`\>

#### Type parameters

| Name     | Type              |
| :------- | :---------------- |
| `IsOpen` | extends `boolean` |

---

### InputField

Ƭ **InputField**: `Object`

#### Type declaration

| Name    | Type      |
| :------ | :-------- |
| `input` | `unknown` |

---

### InvokeReturn

Ƭ **InvokeReturn**\<`Stream`, `Output`\>: `Stream` extends `true` ? [`BraintrustStream`](/docs/libs/nodejs/classes/BraintrustStream.md) : `Output`

The return type of the `invoke` function. Conditionally returns a `BraintrustStream`
if `stream` is true, otherwise returns the output of the function using the Zod schema's
type if present.

#### Type parameters

| Name     | Type              |
| :------- | :---------------- |
| `Stream` | extends `boolean` |
| `Output` | `Output`          |

---

### LogCommentFullArgs

Ƭ **LogCommentFullArgs**: [`IdField`](/docs/libs/nodejs#idfield) & \{ `_audit_metadata?`: `Record`\<`string`, `unknown`\> ; `_audit_source`: `Source` ; `comment`: \{ `text`: `string` } ; `created`: `string` ; `origin`: \{ `id`: `string` } } & [`ParentExperimentIds`](/docs/libs/nodejs/interfaces/ParentExperimentIds.md) \| [`ParentProjectLogIds`](/docs/libs/nodejs/interfaces/ParentProjectLogIds.md)

---

### LogFeedbackFullArgs

Ƭ **LogFeedbackFullArgs**: [`IdField`](/docs/libs/nodejs#idfield) & `Partial`\<`Omit`\<[`OtherExperimentLogFields`](/docs/libs/nodejs#otherexperimentlogfields), `"output"` \| `"metrics"` \| `"datasetRecordId"`\> & \{ `comment`: `string` ; `source`: `Source` }\>

---

### OtherExperimentLogFields

Ƭ **OtherExperimentLogFields**: `Object`

#### Type declaration

| Name                     | Type                                        |
| :----------------------- | :------------------------------------------ |
| `_async_scoring_control` | `AsyncScoringControl`                       |
| `_merge_paths`           | `string`[][]                                |
| `_skip_async_scoring`    | `boolean`                                   |
| `datasetRecordId`        | `string`                                    |
| `error`                  | `unknown`                                   |
| `expected`               | `unknown`                                   |
| `metadata`               | `Record`\<`string`, `unknown`\>             |
| `metrics`                | `Record`\<`string`, `unknown`\>             |
| `origin`                 | `z.infer`\<typeof `objectReferenceSchema`\> |
| `output`                 | `unknown`                                   |
| `scores`                 | `Record`\<`string`, `number` \| `null`\>    |
| `span_attributes`        | `Record`\<`string`, `unknown`\>             |
| `tags`                   | `string`[]                                  |

---

### PromiseUnless

Ƭ **PromiseUnless**\<`B`, `R`\>: `B` extends `true` ? `R` : `Promise`\<`Awaited`\<`R`\>\>

#### Type parameters

| Name |
| :--- |
| `B`  |
| `R`  |

---

### PromptContents

Ƭ **PromptContents**: `z.infer`\<typeof [`promptContentsSchema`](/docs/libs/nodejs#promptcontentsschema)\>

---

### PromptDefinition

Ƭ **PromptDefinition**: `z.infer`\<typeof [`promptDefinitionSchema`](/docs/libs/nodejs#promptdefinitionschema)\>

---

### PromptDefinitionWithTools

Ƭ **PromptDefinitionWithTools**: `z.infer`\<typeof [`promptDefinitionWithToolsSchema`](/docs/libs/nodejs#promptdefinitionwithtoolsschema)\>

---

### PromptOpts

Ƭ **PromptOpts**\<`HasId`, `HasVersion`, `HasTools`, `HasNoTrace`\>: `Partial`\<`Omit`\<`BaseFnOpts`, `"name"`\>\> & \{ `name`: `string` } & `HasId` extends `true` ? `PromptId` : `Partial`\<`PromptId`\> & `HasVersion` extends `true` ? `PromptVersion` : `Partial`\<`PromptVersion`\> & `HasTools` extends `true` ? `Partial`\<`PromptTools`\> : {} & `HasNoTrace` extends `true` ? `Partial`\<`PromptNoTrace`\> : {} & [`PromptDefinition`](/docs/libs/nodejs#promptdefinition)

#### Type parameters

| Name         | Type                       |
| :----------- | :------------------------- |
| `HasId`      | extends `boolean`          |
| `HasVersion` | extends `boolean`          |
| `HasTools`   | extends `boolean` = `true` |
| `HasNoTrace` | extends `boolean` = `true` |

---

### PromptRowWithId

Ƭ **PromptRowWithId**\<`HasId`, `HasVersion`\>: `Omit`\<`PromptRow`, `"log_id"` \| `"org_id"` \| `"project_id"` \| `"id"` \| `"_xact_id"`\> & `Partial`\<`Pick`\<`PromptRow`, `"project_id"`\>\> & `HasId` extends `true` ? `Pick`\<`PromptRow`, `"id"`\> : `Partial`\<`Pick`\<`PromptRow`, `"id"`\>\> & `HasVersion` extends `true` ? `Pick`\<`PromptRow`, `"_xact_id"`\> : `Partial`\<`Pick`\<`PromptRow`, `"_xact_id"`\>\>

#### Type parameters

| Name         | Type                       |
| :----------- | :------------------------- |
| `HasId`      | extends `boolean` = `true` |
| `HasVersion` | extends `boolean` = `true` |

---

### ScorerOpts

Ƭ **ScorerOpts**\<`Output`, `Input`, `Params`, `Returns`, `Fn`\>: [`CodeOpts`](/docs/libs/nodejs#codeopts)\<`Exact`\<`Params`, `ScorerArgs`\<`Output`, `Input`\>\>, `Returns`, `Fn`\> \| `ScorerPromptOpts`

#### Type parameters

| Name      | Type                                                                                           |
| :-------- | :--------------------------------------------------------------------------------------------- |
| `Output`  | `Output`                                                                                       |
| `Input`   | `Input`                                                                                        |
| `Params`  | `Params`                                                                                       |
| `Returns` | `Returns`                                                                                      |
| `Fn`      | extends `GenericFunction`\<`Exact`\<`Params`, `ScorerArgs`\<`Output`, `Input`\>\>, `Returns`\> |

---

### SerializedBraintrustState

Ƭ **SerializedBraintrustState**: `z.infer`\<typeof `loginSchema`\>

---

### SetCurrentArg

Ƭ **SetCurrentArg**: `Object`

#### Type declaration

| Name          | Type      |
| :------------ | :-------- |
| `setCurrent?` | `boolean` |

---

### SpanContext

Ƭ **SpanContext**: `Object`

#### Type declaration

| Name          | Type                                                  |
| :------------ | :---------------------------------------------------- |
| `NOOP_SPAN`   | typeof [`NOOP_SPAN`](/docs/libs/nodejs#noop_span)     |
| `currentSpan` | typeof [`currentSpan`](/docs/libs/nodejs#currentspan) |
| `startSpan`   | typeof [`startSpan`](/docs/libs/nodejs#startspan)     |
| `withCurrent` | typeof [`withCurrent`](/docs/libs/nodejs#withcurrent) |

---

### StartSpanArgs

Ƭ **StartSpanArgs**: `Object`

#### Type declaration

| Name               | Type                     |
| :----------------- | :----------------------- |
| `event?`           | `StartSpanEventArgs`     |
| `name?`            | `string`                 |
| `parent?`          | `string`                 |
| `propagatedEvent?` | `StartSpanEventArgs`     |
| `spanAttributes?`  | `Record`\<`any`, `any`\> |
| `startTime?`       | `number`                 |
| `type?`            | `SpanType`               |

---

### ToolFunctionDefinition

Ƭ **ToolFunctionDefinition**: `z.infer`\<typeof [`toolFunctionDefinitionSchema`](/docs/libs/nodejs#toolfunctiondefinitionschema)\>

---

### WithTransactionId

Ƭ **WithTransactionId**\<`R`\>: `R` & \{ `_xact_id`: `TransactionId` }

#### Type parameters

| Name |
| :--- |
| `R`  |

## Variables

### ERR_PERMALINK

• `Const` **ERR_PERMALINK**: `"https://braintrust.dev/error-generating-link"`

---

### INTERNAL_BTQL_LIMIT

• `Const` **INTERNAL_BTQL_LIMIT**: `1000`

---

### LEGACY_CACHED_HEADER

• `Const` **LEGACY_CACHED_HEADER**: `"x-cached"`

---

### NOOP_SPAN

• `Const` **NOOP_SPAN**: [`NoopSpan`](/docs/libs/nodejs/classes/NoopSpan.md)

---

### NOOP_SPAN_PERMALINK

• `Const` **NOOP_SPAN_PERMALINK**: `"https://braintrust.dev/noop-span"`

---

### X_CACHED_HEADER

• `Const` **X_CACHED_HEADER**: `"x-bt-cached"`

---

### \_exportsForTestingOnly

• `Const` **\_exportsForTestingOnly**: `Object`

#### Type declaration

| Name                        | Type                                                                                                                               |
| :-------------------------- | :--------------------------------------------------------------------------------------------------------------------------------- |
| `clearTestBackgroundLogger` | () => `void`                                                                                                                       |
| `deepCopyEvent`             | \<T\>(`event`: `T`) => `T`                                                                                                         |
| `extractAttachments`        | (`event`: `Record`\<`string`, `any`\>, `attachments`: [`BaseAttachment`](/docs/libs/nodejs/classes/BaseAttachment.md)[]) => `void` |
| `simulateLoginForTests`     | () => `Promise`\<[`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)\>                                               |
| `simulateLogoutForTests`    | () => [`BraintrustState`](/docs/libs/nodejs/classes/BraintrustState.md)                                                            |
| `useTestBackgroundLogger`   | () => [`TestBackgroundLogger`](/docs/libs/nodejs/classes/TestBackgroundLogger.md)                                                  |

---

### braintrustStreamChunkSchema

• `Const` **braintrustStreamChunkSchema**: `ZodUnion`\<[`ZodObject`\<\{ `data`: `ZodString` ; `type`: `ZodLiteral`\<``"text_delta"``\> }, ``"strip"``, `ZodTypeAny`, \{ `data`: `string` ; `type`: ``"text_delta"`` }, \{ `data`: `string` ; `type`: ``"text_delta"`` }\>, `ZodObject`\<\{ `data`: `ZodString` ; `type`: `ZodLiteral`\<``"reasoning_delta"``\> }, ``"strip"``, `ZodTypeAny`, \{ `data`: `string` ; `type`: ``"reasoning_delta"`` }, \{ `data`: `string` ; `type`: ``"reasoning_delta"`` }\>, `ZodObject`\<\{ `data`: `ZodString` ; `type`: `ZodLiteral`\<``"json_delta"``\> }, ``"strip"``, `ZodTypeAny`, \{ `data`: `string` ; `type`: ``"json_delta"`` }, \{ `data`: `string` ; `type`: ``"json_delta"`` }\>]\>

---

### evaluatorDefinitionSchema

• `Const` **evaluatorDefinitionSchema**: `ZodObject`\<\{ `parameters`: `ZodOptional`\<`ZodRecord`\<`ZodString`, `ZodUnion`\<[`ZodObject`\<\{ `default`: `ZodOptional`\<`ZodObject`\<\{ `options`: `ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `model`: `ZodOptional`\<`ZodString`\> ; `params`: `ZodOptional`\<`ZodUnion`\<[`ZodObject`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>]\>\> ; `position`: `ZodOptional`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` }, \{ `model?`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` }\>\>\> ; `origin`: `ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `project_id`: `ZodOptional`\<`ZodString`\> ; `prompt_id`: `ZodOptional`\<`ZodString`\> ; `prompt_version`: `ZodOptional`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }, \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }\>\>\> ; `parser`: `ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `choice_scores`: `ZodRecord`\<`ZodString`, `ZodNumber`\> ; `type`: `ZodLiteral`\<`"llm_classifier"`\> ; `use_cot`: `ZodBoolean` }, `"strip"`, `ZodTypeAny`, \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }, \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }\>\>\> ; `prompt`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodString` ; `type`: `ZodLiteral`\<`"completion"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `string` ; `type`: `"completion"` }, \{ `content`: `string` ; `type`: `"completion"` }\>, `ZodObject`\<\{ `messages`: `ZodArray`\<`ZodUnion`\<[`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"system"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"system"` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `ZodObject`\<\{ `image_url`: `ZodObject`\<\{ `detail`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"low"``\>, `ZodLiteral`\<``"high"``\>]\>\> ; `url`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }\> ; `type`: `ZodLiteral`\<`"image_url"`\> }, `"strip"`, `ZodTypeAny`, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }\>]\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"user"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` }, \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` }\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodString`, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\>\>\> ; `function_call`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\>\>\>, `undefined` \| \{ `arguments`: `string` ; `name`: `string` }, `undefined` \| `null` \| \{ `arguments`: `string` ; `name`: `string` }\> ; `name`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `reasoning`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `content`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `id`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `string` ; `id?`: `string` }, \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }\>, `"many"`\>\>\>, `undefined` \| \{ `content?`: `string` ; `id?`: `string` }[], `undefined` \| `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[]\> ; `role`: `ZodLiteral`\<`"assistant"`\> ; `tool_calls`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `function`: `ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\> ; `id`: `ZodString` ; `type`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }\>, `"many"`\>\>\>, `undefined` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[], `undefined` \| `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `role`: `ZodLiteral`\<`"tool"`\> ; `tool_call_id`: `ZodDefault`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` }\>, `ZodObject`\<\{ `content`: `ZodNullable`\<`ZodString`\> ; `name`: `ZodString` ; `role`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }\>]\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodString`\>\> ; `role`: `ZodEnum`\<[``"model"``]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` ; `role`: `"model"` }, \{ `content?`: `null` \| `string` ; `role`: `"model"` }\>]\>, `"many"`\> ; `tools`: `ZodOptional`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"chat"`\> }, `"strip"`, `ZodTypeAny`, \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }, \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }\>]\>\>\> ; `tool_functions`: `ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `id`: `ZodString` ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `id`: `string` ; `type`: ``"function"`` }, \{ `id`: `string` ; `type`: ``"function"`` }\>, `ZodObject`\<\{ `name`: `ZodString` ; `type`: `ZodLiteral`\<``"global"``\> }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` ; `type`: ``"global"`` }, \{ `name`: `string` ; `type`: ``"global"`` }\>]\>, `"many"`\>\>\> }, `"strip"`, `ZodTypeAny`, \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] }, \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] }\>\> ; `description`: `ZodOptional`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"prompt"`\> }, `"strip"`, `ZodTypeAny`, \{ `default?`: \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] } ; `description?`: `string` ; `type`: `"prompt"` }, \{ `default?`: \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] } ; `description?`: `string` ; `type`: `"prompt"` }\>, `ZodObject`\<\{ `default`: `ZodOptional`\<`ZodUnknown`\> ; `description`: `ZodOptional`\<`ZodString`\> ; `schema`: `ZodRecord`\<`ZodString`, `ZodUnknown`\> ; `type`: `ZodLiteral`\<`"data"`\> }, `"strip"`, `ZodTypeAny`, \{ `default?`: `unknown` ; `description?`: `string` ; `schema`: `Record`\<`string`, `unknown`\> ; `type`: `"data"` }, \{ `default?`: `unknown` ; `description?`: `string` ; `schema`: `Record`\<`string`, `unknown`\> ; `type`: `"data"` }\>]\>\>\> }, `"strip"`, `ZodTypeAny`, \{ `parameters?`: `Record`\<`string`, \{ type: "prompt"; default?: \{ prompt?: \{ type: "completion"; content: string; } \| \{ type: "chat"; messages: (\{ content: (string \| \{ type: "text"; text: string; cache_control?: \{ type: "ephemeral"; } \| undefined; }[]) & (string \| ... 1 more ... \| undefined); role: "system"; name?: string \| undefined; } \| ... 4 more .... \| \{ type: "data"; schema: Record\<string, unknown\>; default?: unknown; description?: string \| undefined; }\> }, \{ `parameters?`: `Record`\<`string`, \{ type: "prompt"; default?: \{ prompt?: \{ type: "completion"; content: string; } \| \{ type: "chat"; messages: (\{ role: "system"; content?: string \| \{ type: "text"; text?: string \| undefined; cache_control?: \{ ...; } \| undefined; }[] \| undefined; name?: string \| undefined; } \| ... 4 more ... \| \{ ...; })[]; tools?: stri... \| \{ type: "data"; schema: Record\<string, unknown\>; default?: unknown; description?: string \| undefined; }\> }\>

---

### evaluatorDefinitionsSchema

• `Const` **evaluatorDefinitionsSchema**: `ZodRecord`\<`ZodString`, `ZodObject`\<\{ `parameters`: `ZodOptional`\<`ZodRecord`\<`ZodString`, `ZodUnion`\<[`ZodObject`\<\{ `default`: `ZodOptional`\<`ZodObject`\<\{ `options`: `ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `model`: `ZodOptional`\<`ZodString`\> ; `params`: `ZodOptional`\<`ZodUnion`\<[`ZodObject`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>]\>\> ; `position`: `ZodOptional`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` }, \{ `model?`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` }\>\>\> ; `origin`: `ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `project_id`: `ZodOptional`\<`ZodString`\> ; `prompt_id`: `ZodOptional`\<`ZodString`\> ; `prompt_version`: `ZodOptional`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }, \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` }\>\>\> ; `parser`: `ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `choice_scores`: `ZodRecord`\<`ZodString`, `ZodNumber`\> ; `type`: `ZodLiteral`\<`"llm_classifier"`\> ; `use_cot`: `ZodBoolean` }, `"strip"`, `ZodTypeAny`, \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }, \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` }\>\>\> ; `prompt`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodString` ; `type`: `ZodLiteral`\<`"completion"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `string` ; `type`: `"completion"` }, \{ `content`: `string` ; `type`: `"completion"` }\>, `ZodObject`\<\{ `messages`: `ZodArray`\<`ZodUnion`\<[`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"system"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"system"` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `ZodObject`\<\{ `image_url`: `ZodObject`\<\{ `detail`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"low"``\>, `ZodLiteral`\<``"high"``\>]\>\> ; `url`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }\> ; `type`: `ZodLiteral`\<`"image_url"`\> }, `"strip"`, `ZodTypeAny`, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }\>]\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"user"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` }, \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` }\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodString`, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\>\>\> ; `function_call`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\>\>\>, `undefined` \| \{ `arguments`: `string` ; `name`: `string` }, `undefined` \| `null` \| \{ `arguments`: `string` ; `name`: `string` }\> ; `name`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `reasoning`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `content`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `id`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `string` ; `id?`: `string` }, \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }\>, `"many"`\>\>\>, `undefined` \| \{ `content?`: `string` ; `id?`: `string` }[], `undefined` \| `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[]\> ; `role`: `ZodLiteral`\<`"assistant"`\> ; `tool_calls`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `function`: `ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\> ; `id`: `ZodString` ; `type`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }\>, `"many"`\>\>\>, `undefined` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[], `undefined` \| `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `role`: `ZodLiteral`\<`"tool"`\> ; `tool_call_id`: `ZodDefault`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` }\>, `ZodObject`\<\{ `content`: `ZodNullable`\<`ZodString`\> ; `name`: `ZodString` ; `role`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }\>]\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodString`\>\> ; `role`: `ZodEnum`\<[``"model"``]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` ; `role`: `"model"` }, \{ `content?`: `null` \| `string` ; `role`: `"model"` }\>]\>, `"many"`\> ; `tools`: `ZodOptional`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"chat"`\> }, `"strip"`, `ZodTypeAny`, \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }, \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` }\>]\>\>\> ; `tool_functions`: `ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `id`: `ZodString` ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `id`: `string` ; `type`: ``"function"`` }, \{ `id`: `string` ; `type`: ``"function"`` }\>, `ZodObject`\<\{ `name`: `ZodString` ; `type`: `ZodLiteral`\<``"global"``\> }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` ; `type`: ``"global"`` }, \{ `name`: `string` ; `type`: ``"global"`` }\>]\>, `"many"`\>\>\> }, `"strip"`, `ZodTypeAny`, \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] }, \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] }\>\> ; `description`: `ZodOptional`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"prompt"`\> }, `"strip"`, `ZodTypeAny`, \{ `default?`: \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] } ; `description?`: `string` ; `type`: `"prompt"` }, \{ `default?`: \{ `options?`: `null` \| \{ `model?`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> ; `position?`: `string` } ; `origin?`: `null` \| \{ `project_id?`: `string` ; `prompt_id?`: `string` ; `prompt_version?`: `string` } ; `parser?`: `null` \| \{ `choice_scores`: `Record`\<`string`, `number`\> ; `type`: `"llm_classifier"` ; `use_cot`: `boolean` } ; `prompt?`: `null` \| \{ `content`: `string` ; `type`: `"completion"` } \| \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] ; `tools?`: `string` ; `type`: `"chat"` } ; `tool_functions?`: `null` \| (\{ `id`: `string` ; `type`: `"function"` } \| \{ `name`: `string` ; `type`: `"global"` })[] } ; `description?`: `string` ; `type`: `"prompt"` }\>, `ZodObject`\<\{ `default`: `ZodOptional`\<`ZodUnknown`\> ; `description`: `ZodOptional`\<`ZodString`\> ; `schema`: `ZodRecord`\<`ZodString`, `ZodUnknown`\> ; `type`: `ZodLiteral`\<`"data"`\> }, `"strip"`, `ZodTypeAny`, \{ `default?`: `unknown` ; `description?`: `string` ; `schema`: `Record`\<`string`, `unknown`\> ; `type`: `"data"` }, \{ `default?`: `unknown` ; `description?`: `string` ; `schema`: `Record`\<`string`, `unknown`\> ; `type`: `"data"` }\>]\>\>\> }, `"strip"`, `ZodTypeAny`, \{ `parameters?`: `Record`\<`string`, \{ type: "prompt"; default?: \{ prompt?: \{ type: "completion"; content: string; } \| \{ type: "chat"; messages: (\{ content: (string \| \{ type: "text"; text: string; cache_control?: \{ type: "ephemeral"; } \| undefined; }[]) & (string \| ... 1 more ... \| undefined); role: "system"; name?: string \| undefined; } \| ... 4 more .... \| \{ type: "data"; schema: Record\<string, unknown\>; default?: unknown; description?: string \| undefined; }\> }, \{ `parameters?`: `Record`\<`string`, \{ type: "prompt"; default?: \{ prompt?: \{ type: "completion"; content: string; } \| \{ type: "chat"; messages: (\{ role: "system"; content?: string \| \{ type: "text"; text?: string \| undefined; cache_control?: \{ ...; } \| undefined; }[] \| undefined; name?: string \| undefined; } \| ... 4 more ... \| \{ ...; })[]; tools?: stri... \| \{ type: "data"; schema: Record\<string, unknown\>; default?: unknown; description?: string \| undefined; }\> }\>\>

---

### projects

• `Const` **projects**: `ProjectBuilder`

---

### promptContentsSchema

• `Const` **promptContentsSchema**: `ZodUnion`\<[`ZodObject`\<\{ `prompt`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `prompt`: `string` }, \{ `prompt`: `string` }\>, `ZodObject`\<\{ `messages`: `ZodArray`\<`ZodUnion`\<[`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"system"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"system"` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `ZodObject`\<\{ `image_url`: `ZodObject`\<\{ `detail`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"low"``\>, `ZodLiteral`\<``"high"``\>]\>\> ; `url`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }\> ; `type`: `ZodLiteral`\<`"image_url"`\> }, `"strip"`, `ZodTypeAny`, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }\>]\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"user"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` }, \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` }\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodString`, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\>\>\> ; `function_call`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\>\>\>, `undefined` \| \{ `arguments`: `string` ; `name`: `string` }, `undefined` \| `null` \| \{ `arguments`: `string` ; `name`: `string` }\> ; `name`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `reasoning`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `content`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `id`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `string` ; `id?`: `string` }, \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }\>, `"many"`\>\>\>, `undefined` \| \{ `content?`: `string` ; `id?`: `string` }[], `undefined` \| `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[]\> ; `role`: `ZodLiteral`\<`"assistant"`\> ; `tool_calls`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `function`: `ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\> ; `id`: `ZodString` ; `type`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }\>, `"many"`\>\>\>, `undefined` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[], `undefined` \| `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `role`: `ZodLiteral`\<`"tool"`\> ; `tool_call_id`: `ZodDefault`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` }\>, `ZodObject`\<\{ `content`: `ZodNullable`\<`ZodString`\> ; `name`: `ZodString` ; `role`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }\>]\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodString`\>\> ; `role`: `ZodEnum`\<[``"model"``]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` ; `role`: `"model"` }, \{ `content?`: `null` \| `string` ; `role`: `"model"` }\>]\>, `"many"`\> }, `"strip"`, `ZodTypeAny`, \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] }, \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] }\>]\>

---

### promptDefinitionSchema

• `Const` **promptDefinitionSchema**: `ZodIntersection`\<`ZodUnion`\<[`ZodObject`\<\{ `prompt`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `prompt`: `string` }, \{ `prompt`: `string` }\>, `ZodObject`\<\{ `messages`: `ZodArray`\<`ZodUnion`\<[`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"system"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"system"` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `ZodObject`\<\{ `image_url`: `ZodObject`\<\{ `detail`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"low"``\>, `ZodLiteral`\<``"high"``\>]\>\> ; `url`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }\> ; `type`: `ZodLiteral`\<`"image_url"`\> }, `"strip"`, `ZodTypeAny`, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }\>]\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"user"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` }, \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` }\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodString`, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\>\>\> ; `function_call`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\>\>\>, `undefined` \| \{ `arguments`: `string` ; `name`: `string` }, `undefined` \| `null` \| \{ `arguments`: `string` ; `name`: `string` }\> ; `name`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `reasoning`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `content`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `id`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `string` ; `id?`: `string` }, \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }\>, `"many"`\>\>\>, `undefined` \| \{ `content?`: `string` ; `id?`: `string` }[], `undefined` \| `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[]\> ; `role`: `ZodLiteral`\<`"assistant"`\> ; `tool_calls`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `function`: `ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\> ; `id`: `ZodString` ; `type`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }\>, `"many"`\>\>\>, `undefined` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[], `undefined` \| `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `role`: `ZodLiteral`\<`"tool"`\> ; `tool_call_id`: `ZodDefault`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` }\>, `ZodObject`\<\{ `content`: `ZodNullable`\<`ZodString`\> ; `name`: `ZodString` ; `role`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }\>]\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodString`\>\> ; `role`: `ZodEnum`\<[``"model"``]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` ; `role`: `"model"` }, \{ `content?`: `null` \| `string` ; `role`: `"model"` }\>]\>, `"many"`\> }, `"strip"`, `ZodTypeAny`, \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] }, \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] }\>]\>, `ZodObject`\<\{ `model`: `ZodString` ; `params`: `ZodOptional`\<`ZodUnion`\<[`ZodObject`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>]\>\> }, `"strip"`, `ZodTypeAny`, \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> }, \{ `model`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> }\>\>

---

### promptDefinitionWithToolsSchema

• `Const` **promptDefinitionWithToolsSchema**: `ZodIntersection`\<`ZodIntersection`\<`ZodUnion`\<[`ZodObject`\<\{ `prompt`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `prompt`: `string` }, \{ `prompt`: `string` }\>, `ZodObject`\<\{ `messages`: `ZodArray`\<`ZodUnion`\<[`ZodUnion`\<[`ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"system"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"system"` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodUnion`\<[`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `ZodObject`\<\{ `image_url`: `ZodObject`\<\{ `detail`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"low"``\>, `ZodLiteral`\<``"high"``\>]\>\> ; `url`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }, \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` }\> ; `type`: `ZodLiteral`\<`"image_url"`\> }, `"strip"`, `ZodTypeAny`, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }, \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` }\>]\>, `"many"`\>]\> ; `name`: `ZodOptional`\<`ZodString`\> ; `role`: `ZodLiteral`\<`"user"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` }, \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` }\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodString`, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\>\>\> ; `function_call`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\>\>\>, `undefined` \| \{ `arguments`: `string` ; `name`: `string` }, `undefined` \| `null` \| \{ `arguments`: `string` ; `name`: `string` }\> ; `name`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `reasoning`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `content`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> ; `id`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodString`\>\>, `undefined` \| `string`, `undefined` \| `null` \| `string`\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `string` ; `id?`: `string` }, \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }\>, `"many"`\>\>\>, `undefined` \| \{ `content?`: `string` ; `id?`: `string` }[], `undefined` \| `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[]\> ; `role`: `ZodLiteral`\<`"assistant"`\> ; `tool_calls`: `ZodEffects`\<`ZodOptional`\<`ZodNullable`\<`ZodArray`\<`ZodObject`\<\{ `function`: `ZodObject`\<\{ `arguments`: `ZodString` ; `name`: `ZodString` }, `"strip"`, `ZodTypeAny`, \{ `arguments`: `string` ; `name`: `string` }, \{ `arguments`: `string` ; `name`: `string` }\> ; `id`: `ZodString` ; `type`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }, \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }\>, `"many"`\>\>\>, `undefined` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[], `undefined` \| `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }, \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] }\>, `ZodObject`\<\{ `content`: `ZodUnion`\<[`ZodDefault`\<`ZodString`\>, `ZodArray`\<`ZodObject`\<\{ `cache_control`: `ZodOptional`\<`ZodObject`\<\{ `type`: `ZodEnum`\<[``"ephemeral"``]\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"ephemeral"` }, \{ `type`: `"ephemeral"` }\>\> ; `text`: `ZodDefault`\<`ZodString`\> ; `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }, \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }\>, `"many"`\>]\> ; `role`: `ZodLiteral`\<`"tool"`\> ; `tool_call_id`: `ZodDefault`\<`ZodString`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` }, \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` }\>, `ZodObject`\<\{ `content`: `ZodNullable`\<`ZodString`\> ; `name`: `ZodString` ; `role`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }, \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` }\>]\>, `ZodObject`\<\{ `content`: `ZodOptional`\<`ZodNullable`\<`ZodString`\>\> ; `role`: `ZodEnum`\<[``"model"``]\> }, `"strip"`, `ZodTypeAny`, \{ `content?`: `null` \| `string` ; `role`: `"model"` }, \{ `content?`: `null` \| `string` ; `role`: `"model"` }\>]\>, `"many"`\> }, `"strip"`, `ZodTypeAny`, \{ `messages`: (\{ `content`: {} ; `name?`: `string` ; `role`: `"system"` } \| \{ `content`: {} ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text`: `string` ; `type`: `"text"` }[] ; `function_call?`: \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `string` ; `reasoning?`: \{ `content?`: `string` ; `id?`: `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content`: {} ; `role`: `"tool"` ; `tool_call_id`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] }, \{ `messages`: (\{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `name?`: `string` ; `role`: `"system"` } \| \{ `content?`: `string` \| (\{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` } \| \{ `image_url`: \{ `detail?`: `"auto"` \| `"high"` \| `"low"` ; `url`: `string` } ; `type`: `"image_url"` })[] ; `name?`: `string` ; `role`: `"user"` } \| \{ `content?`: `null` \| `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `function_call?`: `null` \| \{ `arguments`: `string` ; `name`: `string` } ; `name?`: `null` \| `string` ; `reasoning?`: `null` \| \{ `content?`: `null` \| `string` ; `id?`: `null` \| `string` }[] ; `role`: `"assistant"` ; `tool_calls?`: `null` \| \{ `function`: \{ `arguments`: `string` ; `name`: `string` } ; `id`: `string` ; `type`: `"function"` }[] } \| \{ `content?`: `string` \| \{ `cache_control?`: \{ `type`: `"ephemeral"` } ; `text?`: `string` ; `type`: `"text"` }[] ; `role`: `"tool"` ; `tool_call_id?`: `string` } \| \{ `content`: `null` \| `string` ; `name`: `string` ; `role`: `"function"` } \| \{ `content?`: `null` \| `string` ; `role`: `"model"` })[] }\>]\>, `ZodObject`\<\{ `model`: `ZodString` ; `params`: `ZodOptional`\<`ZodUnion`\<[`ZodObject`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>, `ZodObject`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `"passthrough"`, `ZodTypeAny`, `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>, `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\>\>]\>\> }, `"strip"`, `ZodTypeAny`, \{ `model`: `string` ; `params?`: `objectOutputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectOutputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> }, \{ `model`: `string` ; `params?`: `objectInputType`\<\{ `frequency_penalty`: `ZodOptional`\<`ZodNumber`\> ; `function_call`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\>]\>\> ; `max_completion_tokens`: `ZodOptional`\<`ZodNumber`\> ; `max_tokens`: `ZodOptional`\<`ZodNumber`\> ; `n`: `ZodOptional`\<`ZodNumber`\> ; `presence_penalty`: `ZodOptional`\<`ZodNumber`\> ; `reasoning_effort`: `ZodOptional`\<`ZodEnum`\<[``"low"``, ``"medium"``, ``"high"``]\>\> ; `response_format`: `ZodOptional`\<`ZodNullable`\<`ZodUnion`\<[`ZodObject`\<\{ `type`: `ZodLiteral`\<`"json_object"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"json_object"` }, \{ `type`: `"json_object"` }\>, `ZodObject`\<\{ `json_schema`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `schema`: `ZodOptional`\<`ZodUnion`\<[`ZodRecord`\<`ZodString`, `ZodUnknown`\>, `ZodString`]\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"json_schema"`\> }, `"strip"`, `ZodTypeAny`, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }, \{ `json_schema`: \{ `description?`: `string` ; `name`: `string` ; `schema?`: `string` \| `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"json_schema"` }\>, `ZodObject`\<\{ `type`: `ZodLiteral`\<`"text"`\> }, `"strip"`, `ZodTypeAny`, \{ `type`: `"text"` }, \{ `type`: `"text"` }\>]\>\>\> ; `stop`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `tool_choice`: `ZodOptional`\<`ZodUnion`\<[`ZodLiteral`\<``"auto"``\>, `ZodLiteral`\<``"none"``\>, `ZodLiteral`\<``"required"``\>, `ZodObject`\<\{ `function`: `ZodObject`\<\{ `name`: `ZodString` }, ``"strip"``, `ZodTypeAny`, \{ `name`: `string` }, \{ `name`: `string` }\> ; `type`: `ZodLiteral`\<``"function"``\> }, ``"strip"``, `ZodTypeAny`, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }, \{ `function`: \{ `name`: `string` } ; `type`: ``"function"`` }\>]\>\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `max_tokens`: `ZodNumber` ; `max_tokens_to_sample`: `ZodOptional`\<`ZodNumber`\> ; `stop_sequences`: `ZodOptional`\<`ZodArray`\<`ZodString`, `"many"`\>\> ; `temperature`: `ZodNumber` ; `top_k`: `ZodOptional`\<`ZodNumber`\> ; `top_p`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `maxOutputTokens`: `ZodOptional`\<`ZodNumber`\> ; `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `topP`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `temperature`: `ZodOptional`\<`ZodNumber`\> ; `topK`: `ZodOptional`\<`ZodNumber`\> ; `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> \| `objectInputType`\<\{ `use_cache`: `ZodOptional`\<`ZodBoolean`\> }, `ZodTypeAny`, `"passthrough"`\> }\>\>, `ZodObject`\<\{ `tools`: `ZodOptional`\<`ZodArray`\<`ZodObject`\<\{ `function`: `ZodObject`\<\{ `description`: `ZodOptional`\<`ZodString`\> ; `name`: `ZodString` ; `parameters`: `ZodOptional`\<`ZodRecord`\<`ZodString`, `ZodUnknown`\>\> ; `strict`: `ZodOptional`\<`ZodNullable`\<`ZodBoolean`\>\> }, `"strip"`, `ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }, \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` }\> ; `type`: `ZodLiteral`\<`"function"`\> }, `"strip"`, `ZodTypeAny`, \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }, \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }\>, `"many"`\>\> }, `"strip"`, `ZodTypeAny`, \{ `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] }, \{ `tools?`: \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `null` \| `boolean` } ; `type`: `"function"` }[] }\>\>

---

### toolFunctionDefinitionSchema

• `Const` **toolFunctionDefinitionSchema**: `z.ZodObject`\<\{ `function`: `z.ZodObject`\<\{ `description`: `z.ZodOptional`\<`z.ZodString`\> ; `name`: `z.ZodString` ; `parameters`: `z.ZodOptional`\<`z.ZodRecord`\<`z.ZodString`, `z.ZodUnknown`\>\> ; `strict`: `z.ZodOptional`\<`z.ZodNullable`\<`z.ZodBoolean`\>\> }, `"strip"`, `z.ZodTypeAny`, \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `boolean` \| `null` }, \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `boolean` \| `null` }\> ; `type`: `z.ZodLiteral`\<`"function"`\> }, `"strip"`, `z.ZodTypeAny`, \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `boolean` \| `null` } ; `type`: `"function"` }, \{ `function`: \{ `description?`: `string` ; `name`: `string` ; `parameters?`: `Record`\<`string`, `unknown`\> ; `strict?`: `boolean` \| `null` } ; `type`: `"function"` }\>
