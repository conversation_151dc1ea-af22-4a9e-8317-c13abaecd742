---
title: default
---

# Namespace: default

## References

### AnyDataset

Re-exports [AnyDataset](/docs/libs/nodejs#anydataset)

---

### Attachment

Re-exports [Attachment](/docs/libs/nodejs/classes/Attachment.md)

---

### AttachmentParams

Re-exports [AttachmentParams](/docs/libs/nodejs/interfaces/AttachmentParams.md)

---

### BackgroundLoggerOpts

Re-exports [BackgroundLoggerOpts](/docs/libs/nodejs/interfaces/BackgroundLoggerOpts.md)

---

### BaseAttachment

Re-exports [BaseAttachment](/docs/libs/nodejs/classes/BaseAttachment.md)

---

### BaseExperiment

Re-exports [BaseExperiment](/docs/libs/nodejs#baseexperiment)

---

### BaseMetadata

Re-exports [BaseMetadata](/docs/libs/nodejs#basemetadata)

---

### BraintrustState

Re-exports [BraintrustState](/docs/libs/nodejs/classes/BraintrustState.md)

---

### BraintrustStream

Re-exports [BraintrustStream](/docs/libs/nodejs/classes/BraintrustStream.md)

---

### BraintrustStreamChunk

Re-exports [BraintrustStreamChunk](/docs/libs/nodejs#braintruststreamchunk)

---

### ChatPrompt

Re-exports [ChatPrompt](/docs/libs/nodejs#chatprompt)

---

### CodeFunction

Re-exports [CodeFunction](/docs/libs/nodejs/classes/CodeFunction.md)

---

### CodeOpts

Re-exports [CodeOpts](/docs/libs/nodejs#codeopts)

---

### CodePrompt

Re-exports [CodePrompt](/docs/libs/nodejs/classes/CodePrompt.md)

---

### CommentEvent

Re-exports [CommentEvent](/docs/libs/nodejs#commentevent)

---

### CompiledPrompt

Re-exports [CompiledPrompt](/docs/libs/nodejs#compiledprompt)

---

### CompiledPromptParams

Re-exports [CompiledPromptParams](/docs/libs/nodejs#compiledpromptparams)

---

### CompletionPrompt

Re-exports [CompletionPrompt](/docs/libs/nodejs#completionprompt)

---

### CreateProjectOpts

Re-exports [CreateProjectOpts](/docs/libs/nodejs#createprojectopts)

---

### DataSummary

Re-exports [DataSummary](/docs/libs/nodejs/interfaces/DataSummary.md)

---

### Dataset

Re-exports [Dataset](/docs/libs/nodejs/classes/Dataset.md)

---

### DatasetRecord

Re-exports [DatasetRecord](/docs/libs/nodejs#datasetrecord)

---

### DatasetSummary

Re-exports [DatasetSummary](/docs/libs/nodejs/interfaces/DatasetSummary.md)

---

### DefaultMetadataType

Re-exports [DefaultMetadataType](/docs/libs/nodejs#defaultmetadatatype)

---

### DefaultPromptArgs

Re-exports [DefaultPromptArgs](/docs/libs/nodejs#defaultpromptargs)

---

### ERR_PERMALINK

Re-exports [ERR_PERMALINK](/docs/libs/nodejs#err_permalink)

---

### EndSpanArgs

Re-exports [EndSpanArgs](/docs/libs/nodejs#endspanargs)

---

### Eval

Re-exports [Eval](/docs/libs/nodejs#eval)

---

### EvalCase

Re-exports [EvalCase](/docs/libs/nodejs#evalcase)

---

### EvalHooks

Re-exports [EvalHooks](/docs/libs/nodejs/interfaces/EvalHooks.md)

---

### EvalParameterSerializedSchema

Re-exports [EvalParameterSerializedSchema](/docs/libs/nodejs#evalparameterserializedschema)

---

### EvalParameters

Re-exports [EvalParameters](/docs/libs/nodejs#evalparameters)

---

### EvalResult

Re-exports [EvalResult](/docs/libs/nodejs#evalresult)

---

### EvalResultWithSummary

Re-exports [EvalResultWithSummary](/docs/libs/nodejs/classes/EvalResultWithSummary.md)

---

### EvalScorer

Re-exports [EvalScorer](/docs/libs/nodejs#evalscorer)

---

### EvalScorerArgs

Re-exports [EvalScorerArgs](/docs/libs/nodejs#evalscorerargs)

---

### EvalTask

Re-exports [EvalTask](/docs/libs/nodejs#evaltask)

---

### Evaluator

Re-exports [Evaluator](/docs/libs/nodejs/interfaces/Evaluator.md)

---

### EvaluatorDef

Re-exports [EvaluatorDef](/docs/libs/nodejs#evaluatordef)

---

### EvaluatorDefinition

Re-exports [EvaluatorDefinition](/docs/libs/nodejs#evaluatordefinition)

---

### EvaluatorDefinitions

Re-exports [EvaluatorDefinitions](/docs/libs/nodejs#evaluatordefinitions)

---

### EvaluatorFile

Re-exports [EvaluatorFile](/docs/libs/nodejs#evaluatorfile)

---

### EvaluatorManifest

Re-exports [EvaluatorManifest](/docs/libs/nodejs#evaluatormanifest)

---

### Experiment

Re-exports [Experiment](/docs/libs/nodejs/classes/Experiment.md)

---

### ExperimentLogFullArgs

Re-exports [ExperimentLogFullArgs](/docs/libs/nodejs#experimentlogfullargs)

---

### ExperimentLogPartialArgs

Re-exports [ExperimentLogPartialArgs](/docs/libs/nodejs#experimentlogpartialargs)

---

### ExperimentSummary

Re-exports [ExperimentSummary](/docs/libs/nodejs/interfaces/ExperimentSummary.md)

---

### Exportable

Re-exports [Exportable](/docs/libs/nodejs/interfaces/Exportable.md)

---

### ExternalAttachment

Re-exports [ExternalAttachment](/docs/libs/nodejs/classes/ExternalAttachment.md)

---

### ExternalAttachmentParams

Re-exports [ExternalAttachmentParams](/docs/libs/nodejs/interfaces/ExternalAttachmentParams.md)

---

### FailedHTTPResponse

Re-exports [FailedHTTPResponse](/docs/libs/nodejs/classes/FailedHTTPResponse.md)

---

### FullInitOptions

Re-exports [FullInitOptions](/docs/libs/nodejs#fullinitoptions)

---

### FullLoginOptions

Re-exports [FullLoginOptions](/docs/libs/nodejs#fullloginoptions)

---

### INTERNAL_BTQL_LIMIT

Re-exports [INTERNAL_BTQL_LIMIT](/docs/libs/nodejs#internal_btql_limit)

---

### IdField

Re-exports [IdField](/docs/libs/nodejs#idfield)

---

### InitOptions

Re-exports [InitOptions](/docs/libs/nodejs#initoptions)

---

### InputField

Re-exports [InputField](/docs/libs/nodejs#inputfield)

---

### InvokeFunctionArgs

Re-exports [InvokeFunctionArgs](/docs/libs/nodejs/interfaces/InvokeFunctionArgs.md)

---

### InvokeReturn

Re-exports [InvokeReturn](/docs/libs/nodejs#invokereturn)

---

### LEGACY_CACHED_HEADER

Re-exports [LEGACY_CACHED_HEADER](/docs/libs/nodejs#legacy_cached_header)

---

### LazyValue

Re-exports [LazyValue](/docs/libs/nodejs/classes/LazyValue.md)

---

### LogCommentFullArgs

Re-exports [LogCommentFullArgs](/docs/libs/nodejs#logcommentfullargs)

---

### LogFeedbackFullArgs

Re-exports [LogFeedbackFullArgs](/docs/libs/nodejs#logfeedbackfullargs)

---

### LogOptions

Re-exports [LogOptions](/docs/libs/nodejs/interfaces/LogOptions.md)

---

### Logger

Re-exports [Logger](/docs/libs/nodejs/classes/Logger.md)

---

### LoginOptions

Re-exports [LoginOptions](/docs/libs/nodejs/interfaces/LoginOptions.md)

---

### MetricSummary

Re-exports [MetricSummary](/docs/libs/nodejs/interfaces/MetricSummary.md)

---

### NOOP_SPAN

Re-exports [NOOP_SPAN](/docs/libs/nodejs#noop_span)

---

### NOOP_SPAN_PERMALINK

Re-exports [NOOP_SPAN_PERMALINK](/docs/libs/nodejs#noop_span_permalink)

---

### NoopSpan

Re-exports [NoopSpan](/docs/libs/nodejs/classes/NoopSpan.md)

---

### ObjectMetadata

Re-exports [ObjectMetadata](/docs/libs/nodejs/interfaces/ObjectMetadata.md)

---

### OtherExperimentLogFields

Re-exports [OtherExperimentLogFields](/docs/libs/nodejs#otherexperimentlogfields)

---

### ParentExperimentIds

Re-exports [ParentExperimentIds](/docs/libs/nodejs/interfaces/ParentExperimentIds.md)

---

### ParentProjectLogIds

Re-exports [ParentProjectLogIds](/docs/libs/nodejs/interfaces/ParentProjectLogIds.md)

---

### Project

Re-exports [Project](/docs/libs/nodejs/classes/Project.md)

---

### PromiseUnless

Re-exports [PromiseUnless](/docs/libs/nodejs#promiseunless)

---

### Prompt

Re-exports [Prompt](/docs/libs/nodejs/classes/Prompt.md)

---

### PromptBuilder

Re-exports [PromptBuilder](/docs/libs/nodejs/classes/PromptBuilder.md)

---

### PromptContents

Re-exports [PromptContents](/docs/libs/nodejs#promptcontents)

---

### PromptDefinition

Re-exports [PromptDefinition](/docs/libs/nodejs#promptdefinition)

---

### PromptDefinitionWithTools

Re-exports [PromptDefinitionWithTools](/docs/libs/nodejs#promptdefinitionwithtools)

---

### PromptOpts

Re-exports [PromptOpts](/docs/libs/nodejs#promptopts)

---

### PromptRowWithId

Re-exports [PromptRowWithId](/docs/libs/nodejs#promptrowwithid)

---

### ReadonlyAttachment

Re-exports [ReadonlyAttachment](/docs/libs/nodejs/classes/ReadonlyAttachment.md)

---

### ReadonlyExperiment

Re-exports [ReadonlyExperiment](/docs/libs/nodejs/classes/ReadonlyExperiment.md)

---

### Reporter

Re-exports [Reporter](/docs/libs/nodejs#reporter)

---

### ReporterBody

Re-exports [ReporterBody](/docs/libs/nodejs/interfaces/ReporterBody.md)

---

### ScoreSummary

Re-exports [ScoreSummary](/docs/libs/nodejs/interfaces/ScoreSummary.md)

---

### ScorerBuilder

Re-exports [ScorerBuilder](/docs/libs/nodejs/classes/ScorerBuilder.md)

---

### ScorerOpts

Re-exports [ScorerOpts](/docs/libs/nodejs#scoreropts)

---

### SerializedBraintrustState

Re-exports [SerializedBraintrustState](/docs/libs/nodejs#serializedbraintruststate)

---

### SetCurrentArg

Re-exports [SetCurrentArg](/docs/libs/nodejs#setcurrentarg)

---

### Span

Re-exports [Span](/docs/libs/nodejs/interfaces/Span.md)

---

### SpanContext

Re-exports [SpanContext](/docs/libs/nodejs#spancontext)

---

### SpanImpl

Re-exports [SpanImpl](/docs/libs/nodejs/classes/SpanImpl.md)

---

### StartSpanArgs

Re-exports [StartSpanArgs](/docs/libs/nodejs#startspanargs)

---

### TestBackgroundLogger

Re-exports [TestBackgroundLogger](/docs/libs/nodejs/classes/TestBackgroundLogger.md)

---

### ToolBuilder

Re-exports [ToolBuilder](/docs/libs/nodejs/classes/ToolBuilder.md)

---

### ToolFunctionDefinition

Re-exports [ToolFunctionDefinition](/docs/libs/nodejs#toolfunctiondefinition)

---

### WithTransactionId

Re-exports [WithTransactionId](/docs/libs/nodejs#withtransactionid)

---

### X_CACHED_HEADER

Re-exports [X_CACHED_HEADER](/docs/libs/nodejs#x_cached_header)

---

### \_exportsForTestingOnly

Re-exports [\_exportsForTestingOnly](/docs/libs/nodejs#_exportsfortestingonly)

---

### braintrustStreamChunkSchema

Re-exports [braintrustStreamChunkSchema](/docs/libs/nodejs#braintruststreamchunkschema)

---

### buildLocalSummary

Re-exports [buildLocalSummary](/docs/libs/nodejs#buildlocalsummary)

---

### createFinalValuePassThroughStream

Re-exports [createFinalValuePassThroughStream](/docs/libs/nodejs#createfinalvaluepassthroughstream)

---

### currentExperiment

Re-exports [currentExperiment](/docs/libs/nodejs#currentexperiment)

---

### currentLogger

Re-exports [currentLogger](/docs/libs/nodejs#currentlogger)

---

### currentSpan

Re-exports [currentSpan](/docs/libs/nodejs#currentspan)

---

### defaultErrorScoreHandler

Re-exports [defaultErrorScoreHandler](/docs/libs/nodejs#defaulterrorscorehandler)

---

### deserializePlainStringAsJSON

Re-exports [deserializePlainStringAsJSON](/docs/libs/nodejs#deserializeplainstringasjson)

---

### devNullWritableStream

Re-exports [devNullWritableStream](/docs/libs/nodejs#devnullwritablestream)

---

### evaluatorDefinitionSchema

Re-exports [evaluatorDefinitionSchema](/docs/libs/nodejs#evaluatordefinitionschema)

---

### evaluatorDefinitionsSchema

Re-exports [evaluatorDefinitionsSchema](/docs/libs/nodejs#evaluatordefinitionsschema)

---

### flush

Re-exports [flush](/docs/libs/nodejs#flush)

---

### getSpanParentObject

Re-exports [getSpanParentObject](/docs/libs/nodejs#getspanparentobject)

---

### graph

Re-exports [graph](/docs/libs/nodejs/modules/graph.md)

---

### init

Re-exports [init](/docs/libs/nodejs#init)

---

### initDataset

Re-exports [initDataset](/docs/libs/nodejs#initdataset)

---

### initExperiment

Re-exports [initExperiment](/docs/libs/nodejs#initexperiment)

---

### initFunction

Re-exports [initFunction](/docs/libs/nodejs#initfunction)

---

### initLogger

Re-exports [initLogger](/docs/libs/nodejs#initlogger)

---

### invoke

Re-exports [invoke](/docs/libs/nodejs#invoke)

---

### loadPrompt

Re-exports [loadPrompt](/docs/libs/nodejs#loadprompt)

---

### log

Re-exports [log](/docs/libs/nodejs#log)

---

### logError

Re-exports [logError](/docs/libs/nodejs#logerror)

---

### login

Re-exports [login](/docs/libs/nodejs#login)

---

### loginToState

Re-exports [loginToState](/docs/libs/nodejs#logintostate)

---

### newId

Re-exports [newId](/docs/libs/nodejs#newid)

---

### parseCachedHeader

Re-exports [parseCachedHeader](/docs/libs/nodejs#parsecachedheader)

---

### permalink

Re-exports [permalink](/docs/libs/nodejs#permalink)

---

### projects

Re-exports [projects](/docs/libs/nodejs#projects)

---

### promptContentsSchema

Re-exports [promptContentsSchema](/docs/libs/nodejs#promptcontentsschema)

---

### promptDefinitionSchema

Re-exports [promptDefinitionSchema](/docs/libs/nodejs#promptdefinitionschema)

---

### promptDefinitionToPromptData

Re-exports [promptDefinitionToPromptData](/docs/libs/nodejs#promptdefinitiontopromptdata)

---

### promptDefinitionWithToolsSchema

Re-exports [promptDefinitionWithToolsSchema](/docs/libs/nodejs#promptdefinitionwithtoolsschema)

---

### renderMessage

Re-exports [renderMessage](/docs/libs/nodejs#rendermessage)

---

### renderPromptParams

Re-exports [renderPromptParams](/docs/libs/nodejs#renderpromptparams)

---

### reportFailures

Re-exports [reportFailures](/docs/libs/nodejs#reportfailures)

---

### runEvaluator

Re-exports [runEvaluator](/docs/libs/nodejs#runevaluator)

---

### setFetch

Re-exports [setFetch](/docs/libs/nodejs#setfetch)

---

### spanComponentsToObjectId

Re-exports [spanComponentsToObjectId](/docs/libs/nodejs#spancomponentstoobjectid)

---

### startSpan

Re-exports [startSpan](/docs/libs/nodejs#startspan)

---

### summarize

Re-exports [summarize](/docs/libs/nodejs#summarize)

---

### toolFunctionDefinitionSchema

Re-exports [toolFunctionDefinitionSchema](/docs/libs/nodejs#toolfunctiondefinitionschema)

---

### traceable

Re-exports [traceable](/docs/libs/nodejs#traceable)

---

### traced

Re-exports [traced](/docs/libs/nodejs#traced)

---

### updateSpan

Re-exports [updateSpan](/docs/libs/nodejs#updatespan)

---

### withCurrent

Re-exports [withCurrent](/docs/libs/nodejs#withcurrent)

---

### withDataset

Re-exports [withDataset](/docs/libs/nodejs#withdataset)

---

### withExperiment

Re-exports [withExperiment](/docs/libs/nodejs#withexperiment)

---

### withLogger

Re-exports [withLogger](/docs/libs/nodejs#withlogger)

---

### withParent

Re-exports [withParent](/docs/libs/nodejs#withparent)

---

### wrapAISDKModel

Re-exports [wrapAISDKModel](/docs/libs/nodejs#wrapaisdkmodel)

---

### wrapAnthropic

Re-exports [wrapAnthropic](/docs/libs/nodejs#wrapanthropic)

---

### wrapOpenAI

Re-exports [wrapOpenAI](/docs/libs/nodejs#wrapopenai)

---

### wrapOpenAIv4

Re-exports [wrapOpenAIv4](/docs/libs/nodejs#wrapopenaiv4)

---

### wrapTraced

Re-exports [wrapTraced](/docs/libs/nodejs#wraptraced)
