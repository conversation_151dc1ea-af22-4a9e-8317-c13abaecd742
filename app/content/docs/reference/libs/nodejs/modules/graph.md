---
title: graph
---

# Namespace: graph

## Classes

- [AggregatorNode](/docs/libs/nodejs/classes/graph.AggregatorNode.md)
- [GateNode](/docs/libs/nodejs/classes/graph.GateNode.md)
- [GraphBuilder](/docs/libs/nodejs/classes/graph.GraphBuilder.md)
- [InputNode](/docs/libs/nodejs/classes/graph.InputNode.md)
- [LiteralNode](/docs/libs/nodejs/classes/graph.LiteralNode.md)
- [OutputNode](/docs/libs/nodejs/classes/graph.OutputNode.md)
- [PromptNode](/docs/libs/nodejs/classes/graph.PromptNode.md)
- [PromptTemplateNode](/docs/libs/nodejs/classes/graph.PromptTemplateNode.md)

## Interfaces

- [BuildContext](/docs/libs/nodejs/interfaces/graph.BuildContext.md)
- [Node](/docs/libs/nodejs/interfaces/graph.Node.md)

## Functions

### createGraph

▸ **createGraph**(): [`GraphBuilder`](/docs/libs/nodejs/classes/graph.GraphBuilder.md)

#### Returns

[`GraphBuilder`](/docs/libs/nodejs/classes/graph.GraphBuilder.md)

---

### escapePath

▸ **escapePath**(`parts`): `string` \| `undefined`

#### Parameters

| Name    | Type       |
| :------ | :--------- |
| `parts` | `string`[] |

#### Returns

`string` \| `undefined`

---

### unescapePath

▸ **unescapePath**(`path`): `string`[]

#### Parameters

| Name   | Type     |
| :----- | :------- |
| `path` | `string` |

#### Returns

`string`[]

## Type Aliases

### LazyGraphNode

Ƭ **LazyGraphNode**: `Object`

#### Type declaration

| Name   | Type     |
| :----- | :------- |
| `id`   | `string` |
| `type` | `"lazy"` |

---

### NodeLike

Ƭ **NodeLike**: [`Node`](/docs/libs/nodejs/interfaces/graph.Node.md) \| [`Prompt`](/docs/libs/nodejs/classes/Prompt.md)\<`boolean`, `boolean`\> \| [`ProxyVariable`](/docs/libs/nodejs/modules/graph.md#proxyvariable)

---

### ProxyVariable

Ƭ **ProxyVariable**: `Object`

#### Index signature

▪ [key: `string`]: [`ProxyVariable`](/docs/libs/nodejs/modules/graph.md#proxyvariable)

---

### TransformFn

Ƭ **TransformFn**: (`input`: [`ProxyVariable`](/docs/libs/nodejs/modules/graph.md#proxyvariable)) => [`Node`](/docs/libs/nodejs/interfaces/graph.Node.md)

#### Type declaration

▸ (`input`): [`Node`](/docs/libs/nodejs/interfaces/graph.Node.md)

##### Parameters

| Name    | Type                                                                |
| :------ | :------------------------------------------------------------------ |
| `input` | [`ProxyVariable`](/docs/libs/nodejs/modules/graph.md#proxyvariable) |

##### Returns

[`Node`](/docs/libs/nodejs/interfaces/graph.Node.md)
