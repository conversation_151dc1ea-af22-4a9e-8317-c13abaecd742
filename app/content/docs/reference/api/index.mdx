---
title: API
description: Braintrust API reference
---

import { Card, Cards } from 'fumadocs-ui/components/card';
import sections from "./sections.json";
import sectionDescriptions from "./section-descriptions.json";

# API reference

This section contains the full API specification for Braintrust's data plane.
The API is hosted globally at https://api.braintrust.dev or in your own
environment. The API allows you to access all of the core objects in Braintrust,
including experiments, datasets, prompts, users, groups, roles, and more. It also
enables you to access Braintrust from languages other than TypeScript and Python.

You can access the full OpenAPI spec for this API at https://github.com/braintrustdata/braintrust-openapi.

<Cards>
  {
    sections.map((section) => (
      <Card
        key={section}
        href={`/docs/reference/api/${section}`}
        title={section}
        description={sectionDescriptions[section] ?? ""}
      />
    ))
  }
</Cards>

## API wrappers

Through Stainless, we have language-specific wrappers over the bare REST API for
a variety of languages. Note that unlike our custom-built
[Python](/docs/reference/libs/python) or
[TypeScript](/docs/reference/libs/nodejs) SDKs, these libraries map essentially
1:1 with the REST API:

{/* update meta.json in the sdk section when making changes here */}
- [Python](https://github.com/braintrustdata/braintrust-api-py)
- [TypeScript](https://github.com/braintrustdata/braintrust-api-js)
- [Ruby](https://github.com/braintrustdata/braintrust-ruby)
- [Java](https://github.com/braintrustdata/braintrust-java)
- [Go](https://github.com/braintrustdata/braintrust-go)
- [Kotlin](https://github.com/braintrustdata/braintrust-kotlin)
