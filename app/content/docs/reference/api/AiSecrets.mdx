---
title: Ai Secrets
full: true
_openapi:
  toc:
    - depth: 2
      title: List ai_secrets
      url: '#list-ai_secrets'
    - depth: 2
      title: Create ai_secret
      url: '#create-ai_secret'
    - depth: 2
      title: Delete single ai_secret
      url: '#delete-single-ai_secret'
    - depth: 2
      title: Create or replace ai_secret
      url: '#create-or-replace-ai_secret'
    - depth: 2
      title: Get ai_secret
      url: '#get-ai_secret'
    - depth: 2
      title: Partially update ai_secret
      url: '#partially-update-ai_secret'
    - depth: 2
      title: Delete ai_secret
      url: '#delete-ai_secret'
  structuredData:
    headings:
      - content: List ai_secrets
        id: list-ai_secrets
      - content: Create ai_secret
        id: create-ai_secret
      - content: Delete single ai_secret
        id: delete-single-ai_secret
      - content: Create or replace ai_secret
        id: create-or-replace-ai_secret
      - content: Get ai_secret
        id: get-ai_secret
      - content: Partially update ai_secret
        id: partially-update-ai_secret
      - content: Delete ai_secret
        id: delete-ai_secret
    contents:
      - content: >-
          List out all ai_secrets. The ai_secrets are sorted by creation date,
          with the most recently-created ai_secrets coming first
        heading: list-ai_secrets
      - content: >-
          Create a new ai_secret. If there is an existing ai_secret with the
          same name as the one specified in the request, will return the
          existing ai_secret unmodified
        heading: create-ai_secret
      - content: Delete a single ai_secret
        heading: delete-single-ai_secret
      - content: >-
          Create or replace ai_secret. If there is an existing ai_secret with
          the same name as the one specified in the request, will replace the
          existing ai_secret with the provided fields
        heading: create-or-replace-ai_secret
      - content: Get an ai_secret object by its id
        heading: get-ai_secret
      - content: >-
          Partially update an ai_secret object. Specify the fields to update in
          the payload. Any object-type fields will be deep-merged with existing
          content. Currently we do not support removing fields or setting them
          to null.
        heading: partially-update-ai_secret
      - content: Delete an ai_secret object by its id
        heading: delete-ai_secret
---

import jsonSpec from "@braintrust/openapi/spec.json";

<APIPage document={jsonSpec} operations={[{"method":"get","path":"/v1/ai_secret"},{"method":"post","path":"/v1/ai_secret"},{"method":"delete","path":"/v1/ai_secret"},{"method":"put","path":"/v1/ai_secret"},{"method":"get","path":"/v1/ai_secret/{ai_secret_id}"},{"method":"patch","path":"/v1/ai_secret/{ai_secret_id}"},{"method":"delete","path":"/v1/ai_secret/{ai_secret_id}"}]} hasHead={true} />
