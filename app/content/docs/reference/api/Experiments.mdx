---
title: Experiments
full: true
_openapi:
  toc:
    - depth: 2
      title: List experiments
      url: '#list-experiments'
    - depth: 2
      title: Create experiment
      url: '#create-experiment'
    - depth: 2
      title: Get experiment
      url: '#get-experiment'
    - depth: 2
      title: Partially update experiment
      url: '#partially-update-experiment'
    - depth: 2
      title: Delete experiment
      url: '#delete-experiment'
    - depth: 2
      title: Insert experiment events
      url: '#insert-experiment-events'
    - depth: 2
      title: Fetch experiment (GET form)
      url: '#fetch-experiment-get-form'
    - depth: 2
      title: Fetch experiment (POST form)
      url: '#fetch-experiment-post-form'
    - depth: 2
      title: Feedback for experiment events
      url: '#feedback-for-experiment-events'
    - depth: 2
      title: Summarize experiment
      url: '#summarize-experiment'
  structuredData:
    headings:
      - content: List experiments
        id: list-experiments
      - content: Create experiment
        id: create-experiment
      - content: Get experiment
        id: get-experiment
      - content: Partially update experiment
        id: partially-update-experiment
      - content: Delete experiment
        id: delete-experiment
      - content: Insert experiment events
        id: insert-experiment-events
      - content: Fetch experiment (GET form)
        id: fetch-experiment-get-form
      - content: Fetch experiment (POST form)
        id: fetch-experiment-post-form
      - content: Feedback for experiment events
        id: feedback-for-experiment-events
      - content: Summarize experiment
        id: summarize-experiment
    contents:
      - content: >-
          List out all experiments. The experiments are sorted by creation date,
          with the most recently-created experiments coming first
        heading: list-experiments
      - content: >-
          Create a new experiment. If there is an existing experiment in the
          project with the same name as the one specified in the request, will
          return the existing experiment unmodified
        heading: create-experiment
      - content: Get an experiment object by its id
        heading: get-experiment
      - content: >-
          Partially update an experiment object. Specify the fields to update in
          the payload. Any object-type fields will be deep-merged with existing
          content. Currently we do not support removing fields or setting them
          to null.
        heading: partially-update-experiment
      - content: Delete an experiment object by its id
        heading: delete-experiment
      - content: Insert a set of events into the experiment
        heading: insert-experiment-events
      - content: >-
          Fetch the events in an experiment. Equivalent to the POST form of the
          same path, but with the parameters in the URL query rather than in the
          request body. For more complex queries, use the `POST /btql` endpoint.
        heading: fetch-experiment-get-form
      - content: >-
          Fetch the events in an experiment. Equivalent to the GET form of the
          same path, but with the parameters in the request body rather than in
          the URL query. For more complex queries, use the `POST /btql`
          endpoint.
        heading: fetch-experiment-post-form
      - content: Log feedback for a set of experiment events
        heading: feedback-for-experiment-events
      - content: Summarize experiment
        heading: summarize-experiment
---

import jsonSpec from "@braintrust/openapi/spec.json";

<APIPage document={jsonSpec} operations={[{"method":"get","path":"/v1/experiment"},{"method":"post","path":"/v1/experiment"},{"method":"get","path":"/v1/experiment/{experiment_id}"},{"method":"patch","path":"/v1/experiment/{experiment_id}"},{"method":"delete","path":"/v1/experiment/{experiment_id}"},{"method":"post","path":"/v1/experiment/{experiment_id}/insert"},{"method":"get","path":"/v1/experiment/{experiment_id}/fetch"},{"method":"post","path":"/v1/experiment/{experiment_id}/fetch"},{"method":"post","path":"/v1/experiment/{experiment_id}/feedback"},{"method":"get","path":"/v1/experiment/{experiment_id}/summarize"}]} hasHead={true} />
