---
title: Service tokens
full: true
_openapi:
  toc:
    - depth: 2
      title: List service_tokens
      url: '#list-service_tokens'
    - depth: 2
      title: Create service_token
      url: '#create-service_token'
    - depth: 2
      title: Get service_token
      url: '#get-service_token'
    - depth: 2
      title: Delete service_token
      url: '#delete-service_token'
  structuredData:
    headings:
      - content: List service_tokens
        id: list-service_tokens
      - content: Create service_token
        id: create-service_token
      - content: Get service_token
        id: get-service_token
      - content: Delete service_token
        id: delete-service_token
    contents:
      - content: >-
          List all service_tokens. service_tokens are sorted by creation date descending.
        heading: list-service_tokens
      - content: >-
          Create a new service_token. Use a unique name, as this endpoint doesn't automatically de-duplicate.
        heading: create-service_token
      - content: Get a service_token object by its id
        heading: get-service_token
      - content: Delete a service_token object by its id
        heading: delete-service_token
---

import jsonSpec from "@braintrust/openapi/spec.json";

<APIPage document={jsonSpec} operations={[{"method":"get","path":"/v1/service_token"},{"method":"post","path":"/v1/service_token"},{"method":"get","path":"/v1/service_token/{service_token_id}"},{"method":"delete","path":"/v1/service_token/{service_token_id}"}]} hasHead={true} />
