{"Acls": "Manage role-based access controls for the organization", "AiSecrets": "Manage external AI provider secrets scoped to the organization", "ApiKeys": "Management of API keys for Braintrust users", "CrossObject": "Events and feedback across object types", "Datasets": "Collections of data used to run evaluations and track improvements over time", "EnvVars": "Environment variables that your scoring functions and tools can access", "Experiments": "A set of traces run to test the behavior of code", "Evals": "Run evaluations through the API", "Functions": "Invokable custom logic for LLM applications", "Groups": "Permission management for collections of users", "Logs": "Log real-world interactions in your application", "Organizations": "The billing unit of Braintrust. Can represent a company or team", "Other": "'Hello world' endpoint for testing the API", "ProjectScores": "Scores scoped to a project to be used in experiment code and human review", "ProjectTags": "Tags used to track various kinds of data across your application, and track how they change over time", "Projects": "A single AI feature. The container for experiments, logs, datasets, prompts, and playgrounds", "Prompts": "Versioned prompt that can be referenced in your code", "Proxy": "An OpenAI-protocol compatible proxy that supports multiple model formats, caching, and secret management", "Roles": "Collections of permissions that can be assigned to users or groups", "ServiceTokens": "Management of automated Service tokens", "SpanIframes": "Custom iframe URLs that can be rendered in spans", "Users": "Braintrust users", "Views": "Table configurations that can be saved for quick access"}