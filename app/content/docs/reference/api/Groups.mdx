---
title: Groups
full: true
_openapi:
  toc:
    - depth: 2
      title: List groups
      url: '#list-groups'
    - depth: 2
      title: Create group
      url: '#create-group'
    - depth: 2
      title: Create or replace group
      url: '#create-or-replace-group'
    - depth: 2
      title: Get group
      url: '#get-group'
    - depth: 2
      title: Partially update group
      url: '#partially-update-group'
    - depth: 2
      title: Delete group
      url: '#delete-group'
  structuredData:
    headings:
      - content: List groups
        id: list-groups
      - content: Create group
        id: create-group
      - content: Create or replace group
        id: create-or-replace-group
      - content: Get group
        id: get-group
      - content: Partially update group
        id: partially-update-group
      - content: Delete group
        id: delete-group
    contents:
      - content: >-
          List out all groups. The groups are sorted by creation date, with the
          most recently-created groups coming first
        heading: list-groups
      - content: >-
          Create a new group. If there is an existing group with the same name
          as the one specified in the request, will return the existing group
          unmodified
        heading: create-group
      - content: >-
          Create or replace group. If there is an existing group with the same
          name as the one specified in the request, will replace the existing
          group with the provided fields
        heading: create-or-replace-group
      - content: Get a group object by its id
        heading: get-group
      - content: >-
          Partially update a group object. Specify the fields to update in the
          payload. Any object-type fields will be deep-merged with existing
          content. Currently we do not support removing fields or setting them
          to null.
        heading: partially-update-group
      - content: Delete a group object by its id
        heading: delete-group
---

import jsonSpec from "@braintrust/openapi/spec.json";

<APIPage document={jsonSpec} operations={[{"method":"get","path":"/v1/group"},{"method":"post","path":"/v1/group"},{"method":"put","path":"/v1/group"},{"method":"get","path":"/v1/group/{group_id}"},{"method":"patch","path":"/v1/group/{group_id}"},{"method":"delete","path":"/v1/group/{group_id}"}]} hasHead={true} />
