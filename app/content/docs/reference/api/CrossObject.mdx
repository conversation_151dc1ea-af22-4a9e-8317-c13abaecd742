---
title: Cross Object
full: true
_openapi:
  toc:
    - depth: 2
      title: Cross-object insert
      url: '#cross-object-insert'
  structuredData:
    headings:
      - content: Cross-object insert
        id: cross-object-insert
    contents:
      - content: Insert events and feedback across object types
        heading: cross-object-insert
---

import jsonSpec from "@braintrust/openapi/spec.json";

<APIPage document={jsonSpec} operations={[{"method":"post","path":"/v1/insert"}]} hasHead={true} />
