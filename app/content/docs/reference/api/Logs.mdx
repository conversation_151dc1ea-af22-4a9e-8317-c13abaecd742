---
title: Logs
full: true
_openapi:
  toc:
    - depth: 2
      title: Insert project logs events
      url: '#insert-project-logs-events'
    - depth: 2
      title: Fetch project logs (GET form)
      url: '#fetch-project-logs-get-form'
    - depth: 2
      title: Fetch project logs (POST form)
      url: '#fetch-project-logs-post-form'
    - depth: 2
      title: Feedback for project logs events
      url: '#feedback-for-project-logs-events'
  structuredData:
    headings:
      - content: Insert project logs events
        id: insert-project-logs-events
      - content: Fetch project logs (GET form)
        id: fetch-project-logs-get-form
      - content: Fetch project logs (POST form)
        id: fetch-project-logs-post-form
      - content: Feedback for project logs events
        id: feedback-for-project-logs-events
    contents:
      - content: Insert a set of events into the project logs
        heading: insert-project-logs-events
      - content: >-
          Fetch the events in a project logs. Equivalent to the POST form of the
          same path, but with the parameters in the URL query rather than in the
          request body. For more complex queries, use the `POST /btql` endpoint.
        heading: fetch-project-logs-get-form
      - content: >-
          Fetch the events in a project logs. Equivalent to the GET form of the
          same path, but with the parameters in the request body rather than in
          the URL query. For more complex queries, use the `POST /btql`
          endpoint.
        heading: fetch-project-logs-post-form
      - content: Log feedback for a set of project logs events
        heading: feedback-for-project-logs-events
---

import jsonSpec from "@braintrust/openapi/spec.json";

<APIPage document={jsonSpec} operations={[{"method":"post","path":"/v1/project_logs/{project_id}/insert"},{"method":"get","path":"/v1/project_logs/{project_id}/fetch"},{"method":"post","path":"/v1/project_logs/{project_id}/fetch"},{"method":"post","path":"/v1/project_logs/{project_id}/feedback"}]} hasHead={true} />
