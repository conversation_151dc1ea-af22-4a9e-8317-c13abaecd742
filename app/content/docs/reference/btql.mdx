---
title: "BTQL query syntax"
---

import { CodeTabs, CurlTab } from "#/ui/docs/code-tabs";
import Link from "fumadocs-core/link";
import { Callout } from "fumadocs-ui/components/callout";
import { Step, Steps } from "fumadocs-ui/components/steps";

# BTQL query syntax

Braintrust Query Language (BTQL) is a precise, SQL-like syntax for querying your experiments, logs, and datasets. You can use BTQL to filter and run more complex queries to analyze your data.

## Why use BTQL?

BTQL gives you precise control over your AI application data. You can:

- Filter and search for relevant logs and experiments
- Create consistent, reusable queries for monitoring
- Build automated reporting and analysis pipelines
- Write complex queries to analyze model performance

## Query structure

BTQL queries follow a familiar SQL-like structure that lets you define what data you want, how you want it returned, and how to analyze it:

```sql #btql
select: *                           -- Fields to retrieve
from: project_logs('<PROJECT_ID>')  -- Data source (identifier or function call)
filter: scores.Factuality > 0.8     -- Filter conditions
sample: 25%                         -- Random sampling (optional)
sort: created desc                  -- Sort order
limit: 100                          -- Result size limit
cursor: '<CURSOR>'                  -- Pagination token
```

Each clause serves a specific purpose:
- `select`: choose which fields to retrieve
- `from`: specify the data source - can be an identifier (like `project_logs`) or a function call (like `experiment("id")`). Has an optional designator for the shape of the data: `spans`, `traces`, `summary`. If not specified, defaults to `spans`.
- `filter`: define conditions to filter the data
- `sample`: randomly sample a subset of the filtered data (rate or count-based)
- `sort`: set the order of results (`asc` or `desc`)
- `limit` and `cursor`: control result size and enable pagination

You can also use `dimensions`, `measures`, and `pivot` instead of `select` for aggregation queries.

### Data source shapes

When you query traces (experiments and logs) with BTQL, you can choose between three different shapes
that determine how the data is returned. To specify this explicitly, add the shape after the data source:

```sql #btql
select: *
from: project_logs('my-project-id') spans -- Default shape, returns individual spans
limit: 10
```

```sql #btql
select: *
from: project_logs('my-project-id') traces -- Returns all spans from traces that contain at least one matching span
limit: 10
```

```sql #btql
select: *
from: project_logs('my-project-id') summary -- Returns one row per trace with aggregated metrics across all spans in that trace
preview_length: 1024 -- Optional, controls truncation of preview fields. Default is 124.
limit: 10
```

#### Using `summary`

The `summary` shape provides a high-level overview of your experiment results, aggregating metrics across all spans in a trace.
It returns one row per trace, making it ideal for analyzing overall performance and comparing results across experiments.
`summary` includes aggregated metrics like total tokens, total cost, and average scores, while also providing previews of the `input`, `output`, `expected`, and `metadata` fields.
This allows faster loading of data and easier comparison between experiments.

When using `summary`, scores are averaged across all spans in the trace, and metrics like tokens and costs are summed.
The duration metrics are calculated from the span timings.
The preview fields (input, output, expected, error) show only data from the root span and are truncated to a default length of 124 characters, although you can change the length with the `preview_length` clause.
Set `preview_length: -1` to disable truncation entirely.

```sql #btql
select: *
from: project_logs('my-project-id') summary
preview_length: 1024 -- Optional, controls truncation of preview fields. Default is 124.
limit: 10
```

{/* include an example output here (can be truncated) */}

### Available operators

Here are the operators you can use in your queries:

```sql
-- Comparison operators
=           -- Equal to (alias for 'eq')
!=          -- Not equal to (alias for 'ne', can also use '<>')
>           -- Greater than (alias for 'gt')
<           -- Less than (alias for 'lt')
>=          -- Greater than or equal (alias for 'ge')
<=          -- Less than or equal (alias for 'le')
IN          -- Check if value exists in a list of values

-- Null operators
IS NULL     -- Check if value is null
IS NOT NULL -- Check if value is not null
ISNULL      -- Unary operator to check if null
ISNOTNULL   -- Unary operator to check if not null

-- Text matching
LIKE        -- Case-sensitive pattern matching with SQL wildcards
NOT LIKE    -- Negated case-sensitive pattern matching
ILIKE       -- Case-insensitive pattern matching with SQL wildcards
NOT ILIKE   -- Negated case-insensitive pattern matching
MATCH       -- Full-word semantic search (faster but requires exact word matches, e.g. 'apple' won't match 'app')
NOT MATCH   -- Negated full-word semantic search

-- Array operators
INCLUDES    -- Check if array/object contains value (alias: CONTAINS)
NOT INCLUDES -- Check if array/object does not contain value

-- Logical operators
AND         -- Both conditions must be true
OR          -- Either condition must be true
NOT         -- Unary operator to negate condition

-- Arithmetic operators
+           -- Addition (alias: add)
-           -- Subtraction (alias: sub)
*           -- Multiplication (alias: mul)
/           -- Division (alias: div)
%           -- Modulo (alias: mod)
-x          -- Unary negation (alias: neg)
```


### Available functions

Here are all the functions you can use in any context (select, filter, dimensions, measures):

```sql
-- Date/time functions
second(timestamp)          -- Extract second from timestamp
minute(timestamp)         -- Extract minute from timestamp
hour(timestamp)          -- Extract hour from timestamp
day(timestamp)           -- Extract day from timestamp
week(timestamp)          -- Extract week from timestamp
month(timestamp)         -- Extract month from timestamp
year(timestamp)          -- Extract year from timestamp
current_timestamp()      -- Get current timestamp (alias: now())
current_date()          -- Get current date

-- String functions
lower(text)                       -- Convert text to lowercase
upper(text)                       -- Convert text to uppercase
concat(text1, text2, ...)         -- Concatenate strings

-- Array functions
len(array)                        -- Get length of array
contains(array, value)            -- Check if array contains value (alias: includes)

-- Null handling functions
coalesce(val1, val2, ...)        -- Return first non-null value
nullif(val1, val2)               -- Return null if val1 equals val2
least(val1, val2, ...)           -- Return smallest non-null value
greatest(val1, val2, ...)        -- Return largest non-null value

-- Type conversion
round(number, precision)          -- Round to specified precision

-- Cast functions
to_string(value)                 -- Cast value to string
to_boolean(value)                -- Cast value to boolean
to_integer(value)                -- Cast value to integer
to_number(value)                 -- Cast value to number
to_date(value)                   -- Cast value to date
to_datetime(value)               -- Cast value to datetime
to_interval(value)               -- Cast value to interval

-- Aggregate functions (only in measures)
count(expr)                       -- Count number of rows
sum(expr)                        -- Sum numeric values
avg(expr)                        -- Calculate mean of numeric values
min(expr)                        -- Find minimum value
max(expr)                        -- Find maximum value
percentile(expr, p)              -- Calculate percentile (p between 0 and 1)
```

### Field access

BTQL provides flexible ways to access nested data in arrays and objects:

```sql
-- Object field access
metadata.model             -- Access nested object field  e.g. {"metadata": {"model": "value"}}
metadata."field name"      -- Access field with spaces	  e.g. {"metadata": {"field name": "value"}}
metadata."field-name"      -- Access field with hyphens   e.g. {"metadata": {"field-name": "value"}}
metadata."field.name"      -- Access field with dots	  e.g. {"metadata": {"field.name": "value"}}

-- Array access (0-based indexing)
tags[0]                    -- First element
tags[-1]                   -- Last element

-- Combined array and object access
metadata.models[0].name    -- Field in first array element
responses[-1].tokens       -- Field in last array element
spans[0].children[-1].id   -- Nested array traversal
```

<Callout type="info">
Array indices are 0-based, and negative indices count from the end (-1 is the last element).
</Callout>

## Select clause

The `select` clause determines which fields appear in your results. You can select specific fields, compute values, or use `*` to get everything:

```sql #btql
-- Get specific fields
select:
  metadata.model as model,
  scores.Factuality as score,
  created as timestamp
from: project_logs('my-project-id')
```

### Working with expressions

Transform your data directly in the select clause:

```sql #btql
select:
  -- Simple field access
  metadata.model,

  -- Computed values
  metrics.tokens > 1000 as is_long_response,

  -- Conditional logic
  (scores.Factuality > 0.8 ? "high" : "low") as quality
from: project_logs('my-project-id')
```

### Using functions

Transform values and create meaningful aliases for your results:

```sql #btql
select:
  -- Date/time functions
  day(created) as date,
  hour(created) as hour,

  -- Numeric calculations
  round(scores.Factuality, 2) as rounded_score
from: project_logs('my-project-id')
```

## Dimensions and measures

Instead of `select`, you can use `dimensions` and `measures` to group and aggregate data:

```sql #btql
-- Analyze model performance over time
dimensions:
  metadata.model as model,
  day(created) as date
measures:
  count(1) as total_calls,
  avg(scores.Factuality) as avg_score,
  percentile(latency, 0.95) as p95_latency
from: project_logs('my-project-id')
```

### Aggregate functions

Common aggregate functions for measures:
```sql #btql
dimensions: metadata.model as model
measures:
  count(1) as total_rows,
  sum(metrics.tokens) as total_tokens,
  avg(scores.Factuality) as avg_score,
  min(metrics.latency) as min_latency,
  max(metrics.latency) as max_latency,
  percentile(metrics.latency, 0.95) as p95
from: project_logs('my-project-id')
```

### Pivot results

The `pivot` clause transforms your results to make comparisons easier by converting rows into columns. This is especially useful when comparing metrics across different categories or time periods.

Syntax:
```sql
pivot: <measure1>, <measure2>, ...
```

Here are some examples:

```sql #btql
-- Compare model performance metrics across models
dimensions: day(created) as date
measures:
  avg(scores.Factuality) as avg_factuality,
  avg(metrics.tokens) as avg_tokens,
  count(1) as call_count
from: project_logs('my-project-id')
pivot: avg_factuality, avg_tokens, call_count

-- Results will look like:
-- {
--   "date": "2024-01-01",
--   "gpt-4_avg_factuality": 0.92,
--   "gpt-4_avg_tokens": 150,
--   "gpt-4_call_count": 1000,
--   "gpt-3.5-turbo_avg_factuality": 0.85,
--   "gpt-3.5-turbo_avg_tokens": 120,
--   "gpt-3.5-turbo_call_count": 2000
-- }
```

```sql #btql
-- Compare metrics across time periods
dimensions: metadata.model as model
measures:
  avg(scores.Factuality) as avg_score,
  percentile(latency, 0.95) as p95_latency
from: project_logs('my-project-id')
pivot: avg_score, p95_latency

-- Results will look like:
-- {
--   "model": "gpt-4",
--   "0_avg_score": 0.91,
--   "0_p95_latency": 2.5,
--   "1_avg_score": 0.89,
--   "1_p95_latency": 2.8,
--   ...
-- }
```

```sql #btql
-- Compare tag distributions across models
dimensions: tags[0] as primary_tag
measures: count(1) as tag_count
from: project_logs('my-project-id')
pivot: tag_count

-- Results will look like:
-- {
--   "primary_tag": "quality",
--   "gpt-4_tag_count": 500,
--   "gpt-3.5-turbo_tag_count": 300
-- }
```

<Callout type="info">
Pivot columns are automatically named by combining the dimension value and measure name. For example, if you pivot by `metadata.model` and a model named "gpt-4" to measure `avg_score`, the name becomes `gpt-4_avg_score`.
</Callout>

### Unpivot

The `unpivot` clause transforms columns into rows, which is useful when you need to analyze arbitrary scores and metrics without specifying each score name. This is particularly helpful when working with dynamic sets of metrics or when you need to know all possible score names in advance.

```sql #btql
-- Convert wide format to long format for arbitrary scores
dimensions: created as date
measures: count(1) as count
from: project_logs('my-project-id')
unpivot: count as (score_name, score_value)

-- Results will look like:
-- {
--   "date": "2024-01-01",
--   "score_name": "Factuality",
--   "score_value": 0.92
-- },
-- {
--   "date": "2024-01-01",
--   "score_name": "Coherence",
--   "score_value": 0.88
-- }
```

### Conditional expressions

BTQL supports conditional logic using the ternary operator (`? :`):

```sql #btql
-- Basic conditions
select:
  (scores.Factuality > 0.8 ? "high" : "low") as quality,
  (error IS NOT NULL ? -1 : metrics.tokens) as valid_tokens
from: project_logs('my-project-id')
```

```sql #btql
-- Nested conditions
select:
  (scores.Factuality > 0.9 ? "excellent" :
   scores.Factuality > 0.7 ? "good" :
   scores.Factuality > 0.5 ? "fair" : "poor") as rating
from: project_logs('my-project-id')
```

```sql #btql
-- Use in calculations
select:
  (metadata.model = "gpt-4" ? metrics.tokens * 2 : metrics.tokens) as adjusted_tokens,
  (error IS NULL ? metrics.latency : 0) as valid_latency
from: project_logs('my-project-id')
```

### Time intervals

BTQL supports intervals for time-based operations:

```sql #btql
-- Basic intervals
select: *
from: project_logs('my-project-id')
filter: created > now() - interval 1 day
```

```sql #btql
-- Multiple time conditions
select: *
from: project_logs('my-project-id')
filter:
  created > now() - interval 1 hour and
  created < now()
```

```sql #btql
-- Examples with different units
select: *
from: project_logs('my-project-id')
filter:
  created > now() - interval 7 day and    -- Last week
  created > now() - interval 1 month      -- Last month
```

## Filter clause

The `filter` clause lets you specify conditions to narrow down results. It supports a wide range of operators and functions:

```sql
filter:
  -- Simple comparisons
  scores.Factuality > 0.8 and
  metadata.model = "gpt-4" and

  -- Array operations
  tags includes "triage" and

  -- Text search
  input ILIKE '%question%' and

  -- Date ranges
  created > '2024-01-01' and

  -- Complex conditions
  (
    metrics.tokens > 1000 or
    metadata.is_production = true
  )
```

<Callout type="warning">
Note: Negative filters on tags (e.g., `NOT tags includes "resolved"`) may not work as expected. Since tags are only applied to the root span of a trace, and queries return complete traces, negative tag filters will match child spans (which don't have tags) and return the entire trace. We recommend using positive tag filters instead.
</Callout>

## Sort clause

The `sort` clause determines the order of results:

```sql
-- Sort by single field
sort: created desc

-- Sort by multiple fields
sort: scores.Factuality desc, created asc

-- Sort by computed values
sort: len(tags) desc
```

## Limit and cursor

Control result size and implement pagination:

```sql
-- Basic limit
limit: 100
```

```sql #btql
-- Pagination using cursor (only works without sort)
select: *
from: project_logs('<PROJECT_ID>')
limit: 100
cursor: '<CURSOR_TOKEN>'  -- From previous query response
```

<Callout type="info">
Cursors are automatically returned in BTQL responses. A default limit is applied in a query without a limit clause, and the number of returned results can be overridden by using an explicit `limit`. In order to implement pagination, after an initial query, provide the subsequent cursor token returned in the results in the `cursor` clause in follow-on queries. When a cursor has reached the end of the result set, the `data` array will be empty, and no cursor token will be returned by the query.

Cursors can only be used for pagination when no `sort` clause is specified. If you need sorted results, you'll need to implement offset-based pagination by using the last value from your sort field as a filter in the next query, as shown in the example above.
</Callout>


```sql #btql
-- Offset-based pagination with sorting
-- Page 1 (first 100 results)
select: *
from: project_logs('<PROJECT_ID>')
sort: created desc
limit: 100
```

```sql #btql
-- Page 2 (next 100 results)
select: *
from: project_logs('<PROJECT_ID>')
filter: created < '2024-01-15T10:30:00Z'  -- Last created timestamp from previous page
sort: created desc
limit: 100
```

## Sampling

The `sample` clause allows you to randomly sample a subset of your data for analysis, which is useful for working with large datasets.

### Sample methods

BTQL supports two types of sampling:

#### Rate sampling
Sample a percentage of rows using the `%` suffix:

```sql #btql
-- Sample approximately 25% of the data
select: *
from: project_logs('my-project-id')
sample: 25%
limit: 1000
```

#### Count sampling
Sample an approximate number of rows:

```sql #btql
-- Sample approximately 100 rows
select: *
from: project_logs('my-project-id')
sample: 100
```

#### Deterministic sampling with seed
For reproducible results, you can specify a seed value:

```sql #btql
-- Deterministic sampling - same seed always produces same results (on the same underlying data)
select: *
from: project_logs('my-project-id')
sample: 25% seed 42
```

### Combining sampling options

You can combine sampling with other BTQL clauses:

```sql #btql
-- Sampling with filtering and limiting
select: *
from: project_logs('my-project-id')
filter: created > now() - interval 7 day
sample: 25%
limit: 1000
```

### Sampling use cases

**Representative analysis**: Get random samples for statistical analysis:
```sql #btql
-- Get random sample for statistical analysis
select: *
from: project_logs('my-project-id')
sample: 5%
filter: created > now() - interval 30 day
```

**Unique entity analysis**: Focus on analysis with controlled sampling:
```sql #btql
-- Sample data for user experience analysis with controlled size
select: metadata.user_id, metrics.latency, scores.Factuality
from: project_logs('my-project-id')
sample: 1000
filter: metadata.user_id IS NOT NULL
```

<Callout type="info">
When querying with `traces` or `summary` shapes, sampling automatically deduplicates by `root_span_id` to ensure you get unique traces in your sample, not duplicate spans from the same trace.
</Callout>

<Callout type="warning">
Sampling is applied after filtering but before sorting and limiting. The sample size represents the target after filtering, so if your filters are very restrictive, you may get fewer results than requested.
</Callout>

<Callout type="warning">
Sampling is only supported for `select:` queries, not yet for aggregation queries using `dimensions:` and `measures:`.
</Callout>

### Sampling and pagination

When combining sampling with pagination, **you must provide a seed value** to get reliable results. Without a seed, each paginated query applies sampling independently to the cursor-filtered data, leading to inconsistent results.

```sql #btql
-- Correct: First page with seed
select: *
from: project_logs('my-project-id')
sample: 10% seed 42
limit: 100
```

```sql #btql
-- Correct: Subsequent pages with same seed
select: *
from: project_logs('my-project-id')
sample: 10% seed 42
limit: 100
cursor: '<CURSOR_FROM_PREVIOUS_PAGE>'
```

```sql #btql
-- Incorrect: No seed specified
select: *
from: project_logs('my-project-id')
sample: 10%  -- Will produce different results per page
limit: 100
cursor: '<CURSOR>'
```

Without a seed, sampling is re-applied to each page's filtered data with a random seed, which means examples that were already sampled out may get spuriously included.

## API access

Access BTQL programmatically through our API:

<CodeTabs items={["cURL"]}>
<CurlTab>
```bash
curl -X POST https://api.braintrust.dev/btql \
  -H "Authorization: Bearer <YOUR_API_KEY>" \
  -H "Content-Type: application/json" \
  -d '{"query": "select: * | from: project_logs('"'<YOUR_PROJECT_ID>'"') | filter: tags includes '"'triage'"'"}'
```
</CurlTab>
</CodeTabs>

The API accepts these parameters:
- `query` (required): your BTQL query string
- `fmt`: response format (`json` or `parquet`, defaults to `json`)
- `tz_offset`: timezone offset in minutes for time-based operations
- `audit_log`: include audit log data

<Callout type="info">
For correct day boundaries, set `tz_offset` to match your timezone. For example, use `480` for US Pacific Standard Time.
</Callout>

## Examples

Let's look at some real-world examples:

### Tracking token usage

This query helps you monitor token consumption across your application:

```sql #btql
from: project_logs('<YOUR_PROJECT_ID>')
filter: created > '<ISO_8601_TIME>'
dimensions: day(created) as time
measures:
  sum(metrics.total_tokens) as total_tokens,
  sum(metrics.prompt_tokens) as input_tokens,
  sum(metrics.completion_tokens) as output_tokens
sort: time asc
```

The response shows daily token usage:
```json
{
  "time": "2024-11-09T00:00:00Z",
  "total_tokens": 100000,
  "input_tokens": 50000,
  "output_tokens": 50000
}
```

### Model quality monitoring

Track model performance across different versions and configurations:

```sql #btql
-- Compare factuality scores across models
dimensions:
  metadata.model as model,
  day(created) as date
measures:
  avg(scores.Factuality) as avg_factuality,
  percentile(scores.Factuality, 0.05) as p05_factuality,
  percentile(scores.Factuality, 0.95) as p95_factuality,
  count(1) as total_calls
filter: created > '2024-01-01'
sort: date desc, model asc
```

```sql #btql
-- Find potentially problematic responses
select: *
from: project_logs('<PROJECT_ID>')
filter:
  scores.Factuality < 0.5 and
  metadata.is_production = true and
  created > now() - interval 1 day
sort: scores.Factuality asc
limit: 100
```

```sql #btql
-- Compare performance across specific models
select: *
from: project_logs('<PROJECT_ID>')
filter:
  metadata.model IN ["gpt-4", "gpt-4-turbo", "claude-3-opus"] and
  scores.Factuality IS NOT NULL and
  created > now() - interval 7 day
sort: scores.Factuality desc
limit: 500
```

### Error analysis

Identify and investigate errors in your application:

```sql #btql
-- Error rate by model
dimensions:
  metadata.model as model,
  hour(created) as hour
measures:
  count(1) as total,
  count(error) as errors,
  count(error) / count(1) as error_rate
filter: created > now() - interval 1 day
sort: error_rate desc
```

```sql #btql
-- Find common error patterns
dimensions:
  error.type as error_type,
  metadata.model as model
measures:
  count(1) as error_count,
  avg(metrics.latency) as avg_latency
filter:
  error IS NOT NULL and
  created > now() - interval 7 day
sort: error_count desc
```

```sql #btql
-- Exclude known error types from analysis
select: *
from: project_logs('<PROJECT_ID>')
filter:
  error IS NOT NULL and
  error.type NOT IN ["rate_limit", "timeout", "network_error"] and
  metadata.is_production = true and
  created > now() - interval 1 day
sort: created desc
limit: 100
```

### Latency analysis

Monitor and optimize response times:

```sql #btql
-- Track p95 latency by endpoint
dimensions:
  metadata.endpoint as endpoint,
  hour(created) as hour
measures:
  percentile(metrics.latency, 0.95) as p95_latency,
  percentile(metrics.latency, 0.50) as median_latency,
  count(1) as requests
filter: created > now() - interval 1 day
sort: hour desc, p95_latency desc
```

```sql #btql
-- Find slow requests
select:
  metadata.endpoint,
  metrics.latency,
  metrics.tokens,
  input,
  created
from: project_logs('<PROJECT_ID>')
filter:
  metrics.latency > 5000 and  -- Requests over 5 seconds
  created > now() - interval 1 hour
sort: metrics.latency desc
limit: 20
```

### Prompt analysis

Analyze prompt effectiveness and patterns:

```sql #btql
-- Track prompt token efficiency
dimensions:
  metadata.prompt_template as template,
  day(created) as date
measures:
  avg(metrics.prompt_tokens) as avg_prompt_tokens,
  avg(metrics.completion_tokens) as avg_completion_tokens,
  avg(metrics.completion_tokens) / avg(metrics.prompt_tokens) as token_efficiency,
  avg(scores.Factuality) as avg_factuality
filter: created > now() - interval 7 day
sort: date desc, token_efficiency desc
```

```sql #btql
-- Find similar prompts
select: *
from: project_logs('<PROJECT_ID>')
filter:
  input MATCH 'explain the concept of recursion' and
  scores.Factuality > 0.8
sort: created desc
limit: 10
```

### Tag-based analysis

Use tags to track and analyze specific behaviors:

```sql #btql
-- Monitor feedback patterns
dimensions:
  tags[0] as primary_tag,
  metadata.model as model
measures:
  count(1) as feedback_count,
  avg(scores.Factuality > 0.8 ? 1 : 0) as high_quality_rate
filter:
  tags includes 'feedback' and
  created > now() - interval 30 day
sort: feedback_count desc
```

```sql #btql
-- Track issue resolution
select:
  created,
  tags,
  metadata.model,
  scores.Factuality,
  response
from: project_logs('<PROJECT_ID>')
filter:
  tags includes 'needs-review' and
  NOT tags includes 'resolved' and
  created > now() - interval 1 day
sort: scores.Factuality asc
```

## BTQL sandbox

To test BTQL with autocomplete, validation, and a table of results, try the **BTQL sandbox** in the dashboard.
