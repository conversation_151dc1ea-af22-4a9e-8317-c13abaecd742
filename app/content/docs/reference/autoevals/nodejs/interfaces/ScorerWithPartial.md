---
title: ScorerWithPartial
---

# Interface: ScorerWithPartial\<Output, Extra\>

## Type parameters

| Name     |
| :------- |
| `Output` |
| `Extra`  |

## Hierarchy

- `Scorer`\<`Output`, `Extra`\>

  ↳ **`ScorerWithPartial`**

## Callable

### ScorerWithPartial

▸ **ScorerWithPartial**(`args`): `Score` \| `Promise`\<`Score`\>

#### Parameters

| Name   | Type                              |
| :----- | :-------------------------------- |
| `args` | `ScorerArgs`\<`Output`, `Extra`\> |

#### Returns

`Score` \| `Promise`\<`Score`\>

#### Defined in

node_modules/.pnpm/@braintrust+core@0.0.8/node_modules/@braintrust/core/dist/index.d.ts:21

## Properties

### partial

• **partial**: \<T\>(`args`: \{ [K in string \| number \| symbol]: Extra[K] }) => `Scorer`\<`Output`, `Omit`\<`Extra`, `T`\> & `Partial`\<`Pick`\<`Extra`, `T`\>\>\>

#### Type declaration

▸ \<`T`\>(`args`): `Scorer`\<`Output`, `Omit`\<`Extra`, `T`\> & `Partial`\<`Pick`\<`Extra`, `T`\>\>\>

##### Type parameters

| Name | Type                                     |
| :--- | :--------------------------------------- |
| `T`  | extends `string` \| `number` \| `symbol` |

##### Parameters

| Name   | Type                                             |
| :----- | :----------------------------------------------- |
| `args` | \{ [K in string \| number \| symbol]: Extra[K] } |

##### Returns

`Scorer`\<`Output`, `Omit`\<`Extra`, `T`\> & `Partial`\<`Pick`\<`Extra`, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
