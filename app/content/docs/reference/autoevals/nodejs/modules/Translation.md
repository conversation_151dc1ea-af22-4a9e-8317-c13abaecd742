---
title: Translation
---

# Namespace: Translation

Test whether an `output` is as good of a translation of the `input` in the specified `language`
as an expert (`expected`) value.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` ; `language`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` ; `language`: `string` }\>, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                                    |
| :--- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"useCoT"` \| `"input"` \| `"language"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                              |
| :----- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "useCoT" \| "input" \| "language"]: LLMClassifierArgs\<Object\>[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` ; `language`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` ; `language`: `string` }\>, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
