---
title: AnswerRelevancy
---

# Namespace: AnswerRelevancy

Scores the relevancy of the generated answer to the given question.
Answers with incomplete, redundant or unnecessary information are penalized.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `embeddingModel?`: `string` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `strictness?`: `number` ; `temperature?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `embeddingModel?`: `string` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `strictness?`: `number` ; `temperature?`: `number` }, `T`\>\>\>

#### Type parameters

| Name | Type                                    |
| :--- | :-------------------------------------- |
| `T`  | extends keyof `RagasEmbeddingModelArgs` |

#### Parameters

| Name   | Type                                                 |
| :----- | :--------------------------------------------------- |
| `args` | \{ [K in keyof RagasEmbeddingModelArgs]: Object[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `embeddingModel?`: `string` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `strictness?`: `number` ; `temperature?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `embeddingModel?`: `string` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `strictness?`: `number` ; `temperature?`: `number` }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
