---
title: NumericDiff
---

# Namespace: NumericDiff

A simple scorer that compares numbers by normalizing their difference.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`number`, `Omit`\<{}, `T`\> & `Partial`\<`Pick`\<{}, `T`\>\>\>

#### Type parameters

| Name | Type            |
| :--- | :-------------- |
| `T`  | extends `never` |

#### Parameters

| Name   | Type                         |
| :----- | :--------------------------- |
| `args` | \{ [K in never]: Object[K] } |

#### Returns

`Scorer`\<`number`, `Omit`\<{}, `T`\> & `Partial`\<`Pick`\<{}, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
