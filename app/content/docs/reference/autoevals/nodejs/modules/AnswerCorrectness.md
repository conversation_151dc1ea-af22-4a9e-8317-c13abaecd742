---
title: AnswerCorrectness
---

# Namespace: AnswerCorrectness

Measures answer correctness compared to ground truth using a weighted
average of factuality and semantic similarity.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<\{ `answerSimilarity?`: `Scorer`\<`string`, `object`\> ; `answerSimilarityWeight?`: `number` ; `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `factualityWeight?`: `number` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `temperature?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `answerSimilarity?`: `Scorer`\<`string`, `object`\> ; `answerSimilarityWeight?`: `number` ; `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `factualityWeight?`: `number` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `temperature?`: `number` }, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                                                                                                   |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"context"` \| `"input"` \| `"factualityWeight"` \| `"answerSimilarityWeight"` \| `"answerSimilarity"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                                                                    |
| :----- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "context" \| "input" \| "factualityWeight" \| "answerSimilarityWeight" \| "answerSimilarity"]: Object[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<\{ `answerSimilarity?`: `Scorer`\<`string`, `object`\> ; `answerSimilarityWeight?`: `number` ; `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `factualityWeight?`: `number` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `temperature?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `answerSimilarity?`: `Scorer`\<`string`, `object`\> ; `answerSimilarityWeight?`: `number` ; `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `factualityWeight?`: `number` ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `temperature?`: `number` }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
