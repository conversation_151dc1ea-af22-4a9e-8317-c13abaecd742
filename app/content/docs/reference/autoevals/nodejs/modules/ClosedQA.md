---
title: ClosedQA
---

# Namespace: ClosedQA

Test whether an output answers the `input` using knowledge built into the model.
You can specify `criteria` to further constrain the answer.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `criteria`: `any` ; `input`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `criteria`: `any` ; `input`: `string` }\>, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                                    |
| :--- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"useCoT"` \| `"input"` \| `"criteria"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                              |
| :----- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "useCoT" \| "input" \| "criteria"]: LLMClassifierArgs\<Object\>[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `criteria`: `any` ; `input`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `criteria`: `any` ; `input`: `string` }\>, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
