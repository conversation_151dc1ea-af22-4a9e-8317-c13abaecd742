---
title: ContextRelevancy
---

# Namespace: ContextRelevancy

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<`RagasArgs`, `T`\> & `Partial`\<`Pick`\<`RagasArgs`, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                     |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"context"` \| `"input"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                               |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "context" \| "input"]: RagasArgs[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<`RagasArgs`, `T`\> & `Partial`\<`Pick`\<`RagasArgs`, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
