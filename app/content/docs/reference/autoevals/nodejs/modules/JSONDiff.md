---
title: JSO<PERSON>iff
---

# Namespace: JSOND<PERSON>

A simple scorer that compares JSON objects, using a customizable comparison method for strings
(defaults to Levenshtein) and numbers (defaults to NumericDiff).

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`any`, `Omit`\<\{ `numberScorer?`: `Scorer`\<`number`, `object`\> ; `preserveStrings?`: `boolean` ; `stringScorer?`: `Scorer`\<`string`, `object`\> }, `T`\> & `Partial`\<`Pick`\<\{ `numberScorer?`: `Scorer`\<`number`, `object`\> ; `preserveStrings?`: `boolean` ; `stringScorer?`: `Scorer`\<`string`, `object`\> }, `T`\>\>\>

#### Type parameters

| Name | Type                                                                |
| :--- | :------------------------------------------------------------------ |
| `T`  | extends `"stringScorer"` \| `"numberScorer"` \| `"preserveStrings"` |

#### Parameters

| Name   | Type                                                                         |
| :----- | :--------------------------------------------------------------------------- |
| `args` | \{ [K in "stringScorer" \| "numberScorer" \| "preserveStrings"]: Object[K] } |

#### Returns

`Scorer`\<`any`, `Omit`\<\{ `numberScorer?`: `Scorer`\<`number`, `object`\> ; `preserveStrings?`: `boolean` ; `stringScorer?`: `Scorer`\<`string`, `object`\> }, `T`\> & `Partial`\<`Pick`\<\{ `numberScorer?`: `Scorer`\<`number`, `object`\> ; `preserveStrings?`: `boolean` ; `stringScorer?`: `Scorer`\<`string`, `object`\> }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
