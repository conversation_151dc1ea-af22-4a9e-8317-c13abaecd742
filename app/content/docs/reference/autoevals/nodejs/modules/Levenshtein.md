---
title: <PERSON><PERSON><PERSON>ein
---

# Namespace: Lev<PERSON><PERSON><PERSON>

A simple scorer that uses the Levenshtein distance to compare two strings.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<{}, `T`\> & `Partial`\<`Pick`\<{}, `T`\>\>\>

#### Type parameters

| Name | Type            |
| :--- | :-------------- |
| `T`  | extends `never` |

#### Parameters

| Name   | Type                         |
| :----- | :--------------------------- |
| `args` | \{ [K in never]: Object[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<{}, `T`\> & `Partial`\<`Pick`\<{}, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
