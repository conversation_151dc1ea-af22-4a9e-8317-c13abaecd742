---
title: Possible
---

# Namespace: Possible

Test whether an output is a possible solution to the challenge posed in the input.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` }\>, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                    |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"useCoT"` \| `"input"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "useCoT" \| "input"]: LLMClassifierArgs\<Object\>[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `input`: `string` }\>, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
