---
title: Battle
---

# Namespace: Battle

Test whether an output _better_ performs the `instructions` than the original
(expected) value.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `instructions`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `instructions`: `string` }\>, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                           |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"useCoT"` \| `"instructions"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                       |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "useCoT" \| "instructions"]: LLMClassifierArgs\<Object\>[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `instructions`: `string` }\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<\{ `instructions`: `string` }\>, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
