---
title: ExactMatch
---

# Namespace: ExactMatch

A simple scorer that tests whether two values are equal. If the value is an object or array,
it will be JSON-serialized and the strings compared for equality.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`unknown`, `Omit`\<{}, `T`\> & `Partial`\<`Pick`\<{}, `T`\>\>\>

#### Type parameters

| Name | Type            |
| :--- | :-------------- |
| `T`  | extends `never` |

#### Parameters

| Name   | Type                         |
| :----- | :--------------------------- |
| `args` | \{ [K in never]: Object[K] } |

#### Returns

`Scorer`\<`unknown`, `Omit`\<{}, `T`\> & `Partial`\<`Pick`\<{}, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
