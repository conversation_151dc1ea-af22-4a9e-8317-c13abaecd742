---
title: ContextEntityRecall
---

# Namespace: ContextEntityRecall

Estimates context recall by estimating TP and FN using annotated answer and
retrieved context.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `pairwiseScorer?`: `Scorer`\<`string`, `object`\> ; `temperature?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `pairwiseScorer?`: `Scorer`\<`string`, `object`\> ; `temperature?`: `number` }, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                                                           |
| :--- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"context"` \| `"input"` \| `"pairwiseScorer"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                                |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "context" \| "input" \| "pairwiseScorer"]: Object[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `pairwiseScorer?`: `Scorer`\<`string`, `object`\> ; `temperature?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `context?`: `string` \| `string`[] ; `input?`: `string` ; `maxTokens?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `pairwiseScorer?`: `Scorer`\<`string`, `object`\> ; `temperature?`: `number` }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
