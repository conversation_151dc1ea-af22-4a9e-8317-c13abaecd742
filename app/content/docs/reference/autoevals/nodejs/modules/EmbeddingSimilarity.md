---
title: EmbeddingSimilarity
---

# Namespace: EmbeddingSimilarity

A scorer that uses cosine similarity to compare two strings.

**`Param`**

**`Param`**

A prefix to prepend to the prompt. This is useful for specifying the domain of the inputs.

**`Param`**

The model to use for the embedding distance. Defaults to "text-embedding-ada-002".

**`Param`**

The minimum expected score. Defaults to 0.7. Values below this will be scored as 0, and
values between this and 1 will be scaled linearly.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `expectedMin?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `prefix?`: `string` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `expectedMin?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `prefix?`: `string` }, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                      |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"prefix"` \| `"expectedMin"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                 |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "prefix" \| "expectedMin"]: Object[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `expectedMin?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `prefix?`: `string` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `expectedMin?`: `number` ; `model?`: `string` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `prefix?`: `string` }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
