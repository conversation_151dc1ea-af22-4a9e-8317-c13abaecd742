---
title: ListContains
---

# Namespace: ListContains

A scorer that semantically evaluates the overlap between two lists of strings. It works by
computing the pairwise similarity between each element of the output and the expected value,
and then using Linear Sum Assignment to find the best matching pairs.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`[], `Omit`\<\{ `allowExtraEntities?`: `boolean` ; `pairwiseScorer?`: `Scorer`\<`string`, {}\> }, `T`\> & `Partial`\<`Pick`\<\{ `allowExtraEntities?`: `boolean` ; `pairwiseScorer?`: `Scorer`\<`string`, {}\> }, `T`\>\>\>

#### Type parameters

| Name | Type                                                 |
| :--- | :--------------------------------------------------- |
| `T`  | extends `"allowExtraEntities"` \| `"pairwiseScorer"` |

#### Parameters

| Name   | Type                                                            |
| :----- | :-------------------------------------------------------------- |
| `args` | \{ [K in "allowExtraEntities" \| "pairwiseScorer"]: Object[K] } |

#### Returns

`Scorer`\<`string`[], `Omit`\<\{ `allowExtraEntities?`: `boolean` ; `pairwiseScorer?`: `Scorer`\<`string`, {}\> }, `T`\> & `Partial`\<`Pick`\<\{ `allowExtraEntities?`: `boolean` ; `pairwiseScorer?`: `Scorer`\<`string`, {}\> }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
