---
title: <PERSON><PERSON><PERSON><PERSON><PERSON>
---

# Namespace: ValidJSO<PERSON>

A binary scorer that evaluates the validity of JSON output, optionally validating against a
JSON Schema definition (see https://json-schema.org/learn/getting-started-step-by-step#create).

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`any`, `Omit`\<\{ `schema?`: `any` }, `T`\> & `Partial`\<`Pick`\<\{ `schema?`: `any` }, `T`\>\>\>

#### Type parameters

| Name | Type               |
| :--- | :----------------- |
| `T`  | extends `"schema"` |

#### Parameters

| Name   | Type                            |
| :----- | :------------------------------ |
| `args` | \{ [K in "schema"]: Object[K] } |

#### Returns

`Scorer`\<`any`, `Omit`\<\{ `schema?`: `any` }, `T`\> & `Partial`\<`Pick`\<\{ `schema?`: `any` }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
