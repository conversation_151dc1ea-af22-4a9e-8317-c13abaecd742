---
title: Moderation
---

# Namespace: Moderation

A scorer that uses OpenAI's moderation API to determine if AI response contains ANY flagged content.

**`Param`**

**`Param`**

Optional. Threshold to use to determine whether content has exceeded threshold. By
default, it uses OpenAI's default. (Using `flagged` from the response payload.)

**`Param`**

Optional. Specific categories to look for. If not set, all categories will
be considered.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `threshold?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `threshold?`: `number` }, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                         |
| :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"threshold"` |

#### Parameters

| Name   | Type                                                                                                                                                                                        |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "threshold"]: Object[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `threshold?`: `number` }, `T`\> & `Partial`\<`Pick`\<\{ `azureOpenAi?`: `AzureOpenAiAuth` ; `client?`: `OpenAI` ; `openAiApiKey?`: `string` ; `openAiBaseUrl?`: `string` ; `openAiDangerouslyAllowBrowser?`: `boolean` ; `openAiDefaultHeaders?`: `Record`\<`string`, `string`\> ; `openAiOrganizationId?`: `string` ; `threshold?`: `number` }, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
