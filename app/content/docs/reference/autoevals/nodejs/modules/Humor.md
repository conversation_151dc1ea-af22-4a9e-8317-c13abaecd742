---
title: Humor
---

# Namespace: Humor

Test whether an output is funny.

## Functions

### partial

▸ **partial**\<`T`\>(`args`): `Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<{}\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<{}\>, `T`\>\>\>

#### Type parameters

| Name | Type                                                                                                                                                                                                                                       |
| :--- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `T`  | extends `"openAiApiKey"` \| `"client"` \| `"model"` \| `"openAiOrganizationId"` \| `"openAiBaseUrl"` \| `"openAiDefaultHeaders"` \| `"openAiDangerouslyAllowBrowser"` \| `"azureOpenAi"` \| `"temperature"` \| `"maxTokens"` \| `"useCoT"` |

#### Parameters

| Name   | Type                                                                                                                                                                                                                                                     |
| :----- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `args` | \{ [K in "openAiApiKey" \| "client" \| "model" \| "openAiOrganizationId" \| "openAiBaseUrl" \| "openAiDefaultHeaders" \| "openAiDangerouslyAllowBrowser" \| "azureOpenAi" \| "temperature" \| "maxTokens" \| "useCoT"]: LLMClassifierArgs\<Object\>[K] } |

#### Returns

`Scorer`\<`string`, `Omit`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<{}\>, `T`\> & `Partial`\<`Pick`\<[`LLMClassifierArgs`](/docs/autoevals/nodejs#llmclassifierargs)\<{}\>, `T`\>\>\>

#### Defined in

[autoevals/js/partial.ts:5](https://github.com/braintrustdata/autoevals/blob/38c710b/js/partial.ts#L5)
