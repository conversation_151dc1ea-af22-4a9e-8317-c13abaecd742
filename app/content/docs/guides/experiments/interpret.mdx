---
title: "Interpret evals"
metaTitle: "Visualize and interpret evaluations"
---

import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Callout } from "fumadocs-ui/components/callout";
import Image from "next/image";

# Visualize and interpret eval results

## View results in the UI

Running an eval from the API or SDK will return a link to the corresponding experiment in Braintrust's UI. When you open the link, you'll land on a detailed view of the eval run that you selected. The detailed view includes:

- **Diff mode toggle** - Allows you to compare eval runs to each other. If you click the toggle, you will see the results of your current eval compared to the results of the baseline.
- **Filter bar** - Allows you to focus in on a subset of test cases. You can filter by typing natural language or [BTQL](/docs/reference/btql).
- **Column visibility** - Allows you to toggle column visibility. You can also order columns by regressions to hone in on problematic areas.
- **Table** - Shows the data for every test case in your eval run.

![One eval run](/docs/guides/experiments/eval-run.png)

### Experiment summaries

When you select an experiment, you'll get a summary of the comparisons, scorers, datasets, and metadata.
![Experiment summary](./experiment-summary.png)

You can also view and copy the experiment ID from the bottom of the summary pane.
![Experiment ID](./experiment-id.png)

### Table header summaries

Summaries will appear for score and metric columns. To find test cases to focus on, use column header summaries to filter by improvements or regressions (test cases that decreased in score). This allows you to see the scorers with the biggest issues. You can also group the table to view summaries across metadata fields or inputs. For example, if you use separate datasets for distinct types of usecases, you can group by dataset to see which usecases are having the biggest issues.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/column-grouping-poster.png">
  <source src="/docs/guides/experiments/column-grouping.mov" type="video/mp4" />
</video>

## Group summaries

By default, group rows will show one experiment's summary data, and you can switch between them by selecting your desired aggregation.

![Summary experiment aggregations](/docs/guides/experiments/summary-experiment-aggregation.png)

If you would like to view the summary data for all experiments, select **Include comparisons in group**.

![Include comparisons in group](/docs/guides/experiments/include-comparisons-group.png)

Within a grouped table, you can also sort rows by regressions of a specific score relative to a comparison experiment.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/sort-by-regression-poster.png">
  <source src="/docs/guides/experiments/sort-by-regression.mov" type="video/mp4" />
</video>

Now that you've narrowed your test cases, you can view a test case in detail by selecting a row.

### Trace view

Selecting a row will open the trace view. Here you can see all of the data for the trace for this test case, including input, output, metadata, and metrics for each span inside the trace.

Look at the scores and the output and decide whether the scores seem "right". Do good scores correspond to a good output? If not, you'll want to improve your evals by updating [scorers](/docs/guides/experiments/write#scorers) or [test cases](/blog/eval-feedback-loops).

![Trace view](/docs/guides/experiments/trace.png)

### Create custom columns

You can create custom columns to extract values from the root span.
To do this, use the **Add custom column** option at the bottom of the **Columns** dropdown or select the **+** icon at the end of the table headers.

![Create column action](/docs/guides/experiments/create-column.png)

After naming your custom column, you can either choose from the inferred fields in the dropdown or enter a custom [BTQL](/docs/reference/btql) statement.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/create-column-dialog-poster.png">
  <source src="/docs/guides/experiments/create-column-dialog.mp4" type="video/mp4" />
</video>

Once created, you can filter and sort the table using your custom columns.

## Interpreting results

### How metrics are calculated

Along with the scores you track, Braintrust tracks a number of metrics about your LLM calls that help you assess and understand performance. For example, if you're trying to figure out why the average duration increased substantially when you change a model,
it's useful to look at both duration and token metrics to diagnose the underlying issue.

Wherever possible, metrics are computed on the `task` subspan, so that LLM-as-a-judge calls are excluded. Specifically:

* `Duration` is the duration of the `"task"` span.
* `Offset` is the time elapsed since the trace start time.
* `Prompt tokens`, `Completion tokens`, `Total tokens`, `LLM duration`, and `Estimated LLM cost` are averaged over every span
  that is not marked with `span_attributes.purpose = "scorer"`, which is set automatically in autoevals.

If you are using the logging SDK, or API, you will need to follow these conventions to ensure that metrics are computed correctly.

<Callout type="info">
To compute LLM metrics (like token counts), make sure you [wrap your LLM calls](/docs/guides/traces/customize#wrapping-llm-clients).
</Callout>

### Diff mode

When you run multiple experiments, Braintrust will automatically compare the results of experiments to each other. This allows you to
quickly see which test cases improved or regressed across experiments.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/sort-by-comparison-poster.png">
  <source src="/docs/guides/experiments/sort-by-comparison.mov" type="video/mp4" />
</video>

You can also select any individual row in an experiment to see diffs for each field in a span.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/diff-poster.png">
  <source src="/docs/guides/experiments/diff.mov" type="video/mp4" />
</video>

#### How rows are matched

By default, Braintrust considers two test cases to be the same if they have the same `input` field. This is used both to match test cases across experiments
and to bucket equivalent cases together in a [trial](./write#trials).

### Viewing data across trials

To group by [trials](./write#trials), or multiple rows with the same `input` value, select **Input** from the **Group** dropdown menu.
This will consolidate each trial for a given input and display aggregate data, showing comparisons for each unique input across all experiments.

If Braintrust detects that any rows have the same `input` value within the same experiment, diff mode will show a **Trials** column where you can select matching trials in your comparison experiments.
You can also step through the relevant trial rows in your comparison experiment by selecting a specific trace.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/trials-comparison-poster.png">
  <source src="/docs/guides/experiments/trials-comparison.mp4" type="video/mp4" />
</video>

### Customizing the comparison key

However, sometimes your `input` may include additional data, and you need to use a different
expression to match test cases. You can configure the comparison key in your project's **Configuration** page.

<Image unoptimized className="box-content" src="/docs/guides/projects/comparison-key.png" alt="Create comparison key" width={1552 / 2} height={282 / 2} />

### Experiment view layouts

#### Grid layout

When you run multiple experiments, you can also compare experiment outputs side-by-side in the table by selecting the **Grid layout**. In the grid layout, select which fields to display in cells by selecting from the **Fields** dropdown menu.

#### Summary layout

The **Summary layout** summarizes scores and metrics across the base experiment and all comparison experiments, in a reporting-friendly format with large type. Both summary and grid layouts respect all view filters.

### Aggregate (weighted) scores

It's often useful to compute many, even hundreds, of scores in your experiments, but when reporting on an experiment, or comparing
experiments over time, it's often useful to have a single score that represents the experiment as a whole.

Braintrust allows you to do this with aggregate scores, which are formulas that combine multiple scores. To create an aggregate score, go to your project's **Configuration** page,
and select **Add aggregate score**.

<Image unoptimized className="box-content" src="/docs/guides/experiments/add-aggregate-score.png" alt="Add aggregate score" width={1136 / 2} height={1012 / 2} />

Braintrust currently supports three types of aggregate scores:

- **Weighted average** - A weighted average of selected scores.
- **Minimum** - The minimum value among the selected scores.
- **Maximum** - The maximum value among the selected scores.

## Analyze across experiments

Braintrust allows you to analyze data across experiments to, for example, compare the performance of different models.

### Bar chart

On the Experiments page, you can view your scores as a bar chart by selecting **Score comparison** from the X axis selector:

{/* video from here https://www.braintrust.dev/app/braintrustdata.com/p/Foundation%20Model%20(preview)/experiments?tg=false&search={%22filter%22:[%22id%2520%253D%2520%2718be7b12-6813-4d23-a1a4-2c77fef6dca9%27%2520OR%2520%250Aid%2520%253D%2520%27e1d35207-78c9-4484-8f93-8643643b73c5%27%2520OR%2520%250Aid%2520%253D%2520%27b23c459e-c7fd-4e26-95d6-e546829a2f27%27%2520OR%2520%250Aid%2520%253D%2520%276beb1651-ad4f-43da-bbc1-ce27ddbae1b9%27%2520OR%2520%250Aid%2520%253D%2520%2747b16150-4046-463b-9872-38f5143baa77%27%2520OR%2520%250Aid%2520%253D%2520%2725c60614-da63-41c9-aeec-da108990db7e%27%2520OR%2520%250Aid%2520%253D%2520%276f47a0c8-8c39-4b6b-bedc-e4494ddc2477%27%22]}&ye=score|Question:+Do+the+two+answers+basically+match?+(anthropic/claude-3-5-sonnet-20240620),score|Question:+Do+the+two+answers+basically+match?+(openai/gpt-4o),score|Question:+Does+the+answer+avoid+using+refs?+(anthropic/claude-3-5-sonnet-20240620),score|Question:+Does+the+answer+avoid+using+refs?+(openai/gpt-4o),score|Question:+Does+the+answer+extend+beyond+connected+apps+if+specified?+(anthropic/claude-3-5-sonnet-20240620),score|Question:+Does+the+answer+extend+beyond+connected+apps+if+specified?+(openai/gpt-4o),score|Question:+Does+the+answer+only+use+available+API+documentation?+(anthropic/claude-3-5-sonnet-20240620),score|Question:+Does+the+answer+only+use+available+API+documentation?+(openai/gpt-4o),score|Question:+Does+the+answer+stick+to+connected+apps+where+appropriate?+(anthropic/claude-3-5-sonnet-20240620),score|Question:+Does+the+answer+stick+to+connected+apps+where+appropriate?+(openai/gpt-4o),score|Question:+Does+the+answer+use+{{mustache}}+templates+correctly?+(anthropic/claude-3-5-sonnet-20240620),score|Question:+Does+the+answer+use+{{mustache}}+templates+correctly?+(openai/gpt-4o),metric|duration,metric|llm_duration,metric|prompt_tokens,metric|completion_tokens,metric|total_tokens,metadata|[%22timestamp%22]&x=none|__bt_score_comparison */}
<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/bar-score-comparison-poster.png">
  <source src="/docs/guides/experiments/bar-score-comparison.mp4" type="video/mp4" />
</video>


You can also select the metadata fields you want to group by to create bar charts:

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/group-by-dataset-poster.png">
  <source src="/docs/guides/experiments/group-by-dataset.mov" type="video/mp4" />
</video>

### Scatter plot

Select a metric on the x-axis to construct a scatter plot. Here's an example comparing the relationship between accuracy and duration.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/experiments/scatterplot-poster.png">
  <source src="/docs/guides/experiments/scatterplot.mov" type="video/mp4" />
</video>

## Export experiments

### UI

To export an experiment's results, open the menu next to the experiment name. You can export as `CSV` or `JSON`, and choose if you'd like to download all fields.

![Export experiments](/docs/guides/experiments/exporting-experiments.png)

### API

To fetch the events in an experiment via the API, see [Fetch experiment (POST form)](/docs/api/spec#fetch-experiment-post-form) or [Fetch experiment (GET form)](/docs/api/spec#fetch-experiment-get-form).

### SDK

If you need to access the data from a previous experiment, you can pass the `open` flag into
`init()` and then just iterate through the experiment object:

<CodeTabs>

<TSTab>
```typescript
import { init } from "braintrust";

async function openExperiment() {
  const experiment = init(
    "Say Hi Bot", // Replace with your project name
    {
      experiment: "my-experiment", // Replace with your experiment name
      open: true,
    },
  );
  for await (const testCase of experiment) {
    console.log(testCase);
  }
}
```

</TSTab>

<PYTab>

```python
import braintrust


def open_experiment():
    experiment = braintrust.init(
        project="Say Hi Bot",  # Replace with your project name
        experiment="my-experiment",  # Replace with your experiment name
        open=True,
    )
    for test in experiment:
        print(test_case)
```

</PYTab>

</CodeTabs>

You can use the the `asDataset()`/`as_dataset()` function to automatically convert the experiment into the same
fields you'd use in a dataset (`input`, `expected`, and `metadata`).

<CodeTabs>

<TSTab>

```typescript
import { init } from "braintrust";

async function openExperiment() {
  const experiment = init(
    "Say Hi Bot", // Replace with your project name
    {
      experiment: "my-experiment", // Replace with your experiment name
      open: true,
    },
  );

  for await (const testCase of experiment.asDataset()) {
    console.log(testCase);
  }
}
```

</TSTab>

<PYTab>

```python
import braintrust


def open_experiment():
    experiment = braintrust.init(
        project="Say Hi Bot",  # Replace with your project name
        experiment="my-experiment",  # Replace with your experiment name
        open=True,
    )
    for test in experiment.as_dataset():
        print(test_case)
```

</PYTab>

</CodeTabs>

For a more advanced overview of how to reuse experiments as datasets, see [Hill climbing](/docs/guides/experiments/write#hill-climbing).
