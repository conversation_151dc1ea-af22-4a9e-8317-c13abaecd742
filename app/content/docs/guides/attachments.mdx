---
title: "Attachments"
---

import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";

# Attachments

You can log arbitrary binary data, like images, audio, video, and PDFs, as attachments.
Attachments are useful for building multimodal evaluations, and can enable advanced scenarios like summarizing visual content or analyzing document metadata.

## Uploading attachments

You can upload attachments from either your code or the UI. Your files are securely stored in an object store and associated with the uploading user’s organization. Only you can access your attachments.

### Via code

To [upload an attachment](/docs/guides/tracing#uploading-attachments), create a new `Attachment` object to represent the file path or in-memory buffer that you want to upload:

<CodeTabs>
<TSTab>
```typescript
import { Attachment, initLogger } from "braintrust";

const logger = initLogger();

logger.log({
  input: {
    question: "What is this?",
    context: new Attachment({
      data: "path/to/input_image.jpg",
      filename: "user_input.jpg",
      contentType: "image/jpeg",
    }),
  },
  output: "Example response.",
});
```
</TSTab>
<PYTab>
```python
from braintrust import Attachment, init_logger

logger = init_logger()

logger.log(
    {
        "input": {
            "question": "What is this?",
            "context": Attachment(
                data="path/to/input_image.jpg",
                filename="user_input.jpg",
                content_type="image/jpeg",
            ),
        },
        "output": "Example response.",
    }
)
```
</PYTab>
</CodeTabs>

You can place the `Attachment` anywhere in a log, dataset, or feedback log.

Behind the scenes, the [Braintrust SDK](/docs/reference/libs/nodejs/classes/Attachment) automatically detects and uploads attachments in the background, in parallel to the original logs. This ensures that the latency of your logs isn’t affected by any additional processing.

### Using external files as attachments

Braintrust also supports references to files in external object stores with the `ExternalAttachment` object. You can use this anywhere you would use an `Attachment`. Currently S3 is the only supported option for external files.

[attach-ts]: /docs/reference/libs/nodejs/classes/ExternalAttachment
[attach-py]: /docs/reference/libs/python#externalattachment-objects

<CodeTabs>
<TSTab>
```typescript
import { ExternalAttachment, initLogger } from "braintrust";

const logger = initLogger({ projectName: "ExternalAttachment Example" });

logger.log({
  input: {
    question: "What is this?",
    additional_context: new ExternalAttachment({
      url: "s3://an_existing_bucket/path/to/file.pdf",
      filename: "file.pdf",
      contentType: "application/pdf",
    }),
  },
  output: "Example response.",
});
```
</TSTab>
<PYTab>
```python
from braintrust import ExternalAttachment, init_logger

logger = init_logger("ExternalAttachment Example")

logger.log(
    input={
        "question": "What is this?",
        "additional_context": ExternalAttachment(
            url="s3://an_existing_bucket/path/to/file.pdf",
            filename="file.pdf",
            content_type="application/pdf",
        ),
    },
    output="Example response.",
)
```
</PYTab>
</CodeTabs>

Just like attachments uploaded to Braintrust, external attachments can be previewed and downloaded for local viewing.

### In the UI

You can upload attachments directly through the UI for any editable span field. This includes:

* Any dataset fields, including datasets in playgrounds
* Log span fields
* Experiment span fields

You can also include attachments in prompt messages when using models that support multimodal inputs.

## Inline attachments

Sometimes your attachments are pre-hosted files which you do not want to upload explicitly, but would like
to display as if they were attachments. Inline attachments allow you to do this, by specifying the URL and content
type of the file. Create a JSON object anywhere in the log data with `type: "inline_attachment"` and `src` and
`content_type` fields. The `filename` field is optional.

```json
{
  "file": {
    "type": "inline_attachment",
    "src": "https://robohash.org/example",
    "content_type": "image/png",
    "filename": "A robot"
  }
}
```

<img src="/docs/guides/inline-attachment.png" alt="Screenshot of inline attachment" width="625" height="313" />

## Viewing attachments

You can preview most images, audio files, videos, or PDFs in the Braintrust UI. You can also download any file to view it locally.
We provide built-in support to preview attachments directly in playground input cells and traces.

In the playground, you can preview attachments in an inline embedded view for easy visual verification during experimentation:

<img src="/docs/guides/attachment-in-playground.png" alt="Screenshot of attachment inline in a playground" width="625" height="313" />

In the trace pane, attachments appear as an additional list under the data viewer:

<img src="/docs/guides/traces/attachment-list-one-image.png" alt="Screenshot of attachment list in Braintrust" width="625" height="313" />
