---
title: "Agents"
---

import { Callout } from "fumadocs-ui/components/callout";

# Agents

Agents in Braintrust allow you to chain together two or more prompts. You can create or edit agents in the playground, and view and execute them from the library.

<Callout type="warn">
Agents are in beta. They currently only work in the playground UI, and are limited to prompt chaining functionality. If you are on a hybrid deployment, agents are available starting with `v0.0.66`.

Control flow with loops is coming soon, along with full SDK support.
</Callout>

## Creating an agent in the playground

To create an agent, navigate to a playground and select **+Agent**.
Start by creating the base prompt or selecting one from your library.
Then, create or select another prompt by selecting the **+** icon in the comparison agent pane.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/agents-poster.png">
  <source src="/docs/guides/agents.mp4" type="video/mp4" />
</video>

The prompts will chain together, and they will automatically run consecutively.

## Variables

When building agents, the first prompt node and subsequent prompt nodes have slightly different templating behavior. To refer to variables, you can use [mustache](https://mustache.github.io/mustache.5.html) templating syntax.

### Using `dataset`
<Callout type="info">
If you are on a hybrid deployment, the `dataset` variable is available starting with `v1.1.1`.
</Callout>

The `dataset` variable is globally available. You can use it in any agent prompt node to access `input`, `expected`, and `metadata`. For example, to access `metadata.foo`, use `{{dataset.metadata.foo}}`.

### In the first agent prompt

The first prompt node can access dataset variables directly by using `{{input}}`, `{{expected}}`, and `{{metadata}}`, for convenience. Using `{{dataset}}` is useful for consistent access patterns with later prompts, or if you use this prompt in a later position in the future, since all agent prompts can access `{{dataset}}`.

### Later prompts

Subsequent prompts can access the output of the previous node by using `{{input}}`.
  * If the previous node [outputs structured data](/docs/guides/functions/prompts#structured-outputs), use dot notation. For example, `{{input.bar}}`.
  * If the previous node outputs text or unschematized JSON, you can only use `{{input}}`. If you're using JSON outputs, consider switching to structured outputs to enable accessing nested output variables with linting and autocomplete.

## Viewing and executing agents

You can view and execute single runs of agents from your agent library, but you will not be able to edit them or see them run.

![Agent library](/docs/guides/agent-library.png)
