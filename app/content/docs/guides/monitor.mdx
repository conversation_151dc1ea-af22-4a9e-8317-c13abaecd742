---
title: "Monitor"
metaTitle: "Monitor logs and experiments"
---

# Monitor page

The **Monitor** page shows aggregate metrics data for both the logs and experiments in a given project. The included preset charts show values related to the selected time period for request count, latency, token count, time to first token, cost, scores, and tools. Custom charts can also be created.

![Monitor page](/docs/guides/monitor/monitor-overview.png)


## Filter and group data

Select filter and group by options on the top of the page to apply to all charts.

![Monitor page](/docs/guides/monitor/monitor-filter-group.png)

## Create custom charts

Open the chart editor by clicking '+ Chart' button on top right, or by the pencil icon on any chart header. Measures and filters correspond to the btql options of the same name. Group by option is a btql dimension.

![Monitor page](/docs/guides/monitor/monitor-custom-chart-editor.png)

![Monitor page](/docs/guides/monitor/monitor-custom-chart-editor-2.png)


## Select a timeframe

Select a timeframe from the given options to see the data associated with that time period. Or click and drag on a chart to select a fixed timeframe to zoom in on. Double click a chart to zoom out.

![Monitor page](/docs/guides/monitor/monitor-timeframe.png)


## View management

State is stored in the corresponding view selected in the upper left. Views are specific to the project and project type you have selected.

## Click to view traces

To see specific traces, click on a data point on any chart. It will redirect you to the logs or experiments page filtered to the corresponding time bucket and series.
