---
title: "Tracing"
---

import { <PERSON><PERSON><PERSON>s, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Callout } from "fumadocs-ui/components/callout";
import Link from "fumadocs-core/link";

# Tracing

Tracing is an invaluable tool for exploring the sub-components of your program which produce
each top-level input and output. We currently support tracing in
[logging](/docs/guides/logging) and [evaluations](/docs/guides/evals).

![Trace Screenshot](./trace.png)

## Anatomy of a trace
A trace represents a single independent request, and is made up of several _spans_.

![Anatomy of a trace](./trace-anatomy.png)

A span represents a unit of work, with a start and end time, and optional fields like
input, output, metadata, scores, and metrics (the same fields you can log in an
[experiment](/docs/guides/evals)). Each span contains one or more children that are usually run within their parent span, like for example, a nested function call.
Common examples of spans include LLM calls, vector searches, the steps of an
agent chain, and model evaluations.

Each trace can be expanded to view all of the spans inside. Well-designed traces make it
easy to understand the flow of your application, and to debug issues when they
arise. The tracing API works the same way whether you are logging online (production
logging) or offline (evaluations).

## Where to go from here

Learn more about tracing in Braintrust:
* [Wrapping LLM clients (OpenAI and others)](/docs/guides/traces/customize#wrapping-openai)
* [OpenTelemetry and other popular library integrations](/docs/guides/traces/integrations)
* [Troubleshooting](/docs/guides/traces/customize#tuning-parameters)
* [Viewing traces](/docs/guides/traces/view)
