---
title: Automations
---

# Automations

Automations let you trigger actions based on specific events in Braintrust. This makes it easier for you to execute common actions and integrate Braintrust with your existing tools and workflows.

## Automation types

Braintrust currently supports the following types of automations:

- [Alerts](/docs/guides/automations/alerts): Send a JSON payload to a specified webhook URL when a condition on your logs is met
- [S3 export](/docs/guides/automations/data-management#s3-export): Export data to an AWS S3 bucket in `JSONL` or Parquet format
- [Data retention](/docs/guides/automations/data-management#data-retention): Define time-based retention policies on logs in your project
