---
title: "Remote evals"
---

import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";

# Remote evals

If you have existing infrastructure for running evaluations that isn't easily adaptable to the Braintrust Playground, you can use remote evals to expose a remote endpoint. This lets you run evaluations directly in the playground, iterate quickly across datasets, run scorers, and compare results with other tasks. You can also run multiple instances of your remote eval side-by-side with different parameters and compare results. Parameters defined in the remote eval will be exposed in the playground UI.

## Expose remote `Eval`

To expose an `Eval` running at a remote URL or your local machine, pass in the `--dev` flag.

<CodeTabs>
<TSTab>
Run `npx braintrust eval parameters.eval.ts --dev` to start the dev server and expose `http://localhost:8300`.
</TSTab>
<PYTab>
Run `braintrust eval parameters_eval.py --dev` to start the dev server and expose `http://localhost:8300`.
</PYTab>
</CodeTabs>

The dev host and port can also be configured:

- `--dev-host DEV_HOST`: The host to bind the dev server to. Defaults to localhost. Set to 0.0.0.0 to bind to all interfaces.
- `--dev-port DEV_PORT`: The port to bind the dev server to. Defaults to 8300.

<CodeTabs>
<TSTab>
```typescript parameters.eval.ts
import { Levenshtein } from "autoevals";
import { Eval, initDataset, wrapOpenAI } from "braintrust";
import OpenAI from "openai";
import { z } from "zod";

const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));

Eval("Simple eval", {
  data: initDataset("local dev", { dataset: "sanity" }), // Datasets are currently ignored
  task: async (input, { parameters }) => {
    const completion = await client.chat.completions.create(
      parameters.main.build({
        input: `${parameters.prefix}:${input}`,
      }),
    );
    return completion.choices[0].message.content ?? "";
  },
  // These scores will be used along with any that you configure in the UI
  scores: [Levenshtein],
  parameters: {
    main: {
      type: "prompt",
      name: "Main prompt",
      description: "This is the main prompt",
      default: {
        messages: [
          {
            role: "user",
            content: "{{input}}",
          },
        ],
        model: "gpt-4o",
      },
    },
    another: {
      type: "prompt",
      name: "Another prompt",
      description: "This is another prompt",
      default: {
        messages: [
          {
            role: "user",
            content: "{{input}}",
          },
        ],
        model: "gpt-4o",
      },
    },
    include_prefix: z
      .boolean()
      .default(false)
      .describe("Include a contextual prefix"),
    prefix: z
      .string()
      .describe("The prefix to include")
      .default("this is a math problem"),
    array_of_objects: z
      .array(
        z.object({
          name: z.string(),
          age: z.number(),
        }),
      )
      .default([
        { name: "John", age: 30 },
        { name: "Jane", age: 25 },
      ]),
  },
});
```
</TSTab>
<PYTab>
```python parameters_eval.py
from typing import List

import openai
from autoevals import Levenshtein
from braintrust import Eval, init_dataset, wrap_openai
from pydantic import BaseModel, Field

client = wrap_openai(openai.AsyncOpenAI())


# Define Pydantic models for validation (optional)
# These single-field models act as validators for individual parameters
class Person(BaseModel):
    name: str
    age: int


class BooleanParam(BaseModel):
    value: bool = Field(default=False, description="Include a contextual prefix")


class StringParam(BaseModel):
    value: str = Field(default="this is a math problem", description="The prefix to include")


class ArrayParam(BaseModel):
    value: List[Person] = Field(
        default=[
            Person(name="John", age=30),
            Person(name="Jane", age=25),
        ],
        description="List of people",
    )


async def task(input, hooks):
    parameters = hooks.parameters

    # Access parameters directly, matching TypeScript structure
    prefix = parameters.get("prefix", "this is a math problem")
    if parameters.get("include_prefix", False):
        prompt_input = f"{prefix}:{input}"
    else:
        prompt_input = input

    # Build and execute the prompt using the main prompt parameter
    completion = await client.chat.completions.create(**parameters["main"].build(input=prompt_input))
    return completion.choices[0].message.content or ""


Eval(
    "Simple eval",
    data=init_dataset("local dev", "sanity"),  # Datasets are currently ignored
    task=task,
    scores=[Levenshtein],  # These scores will be used along with any configured in the UI
    parameters={
        "main": {
            "type": "prompt",
            "description": "This is the main prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        "another": {
            "type": "prompt",
            "description": "This is another prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        # Optional: Add validators for individual parameters
        # When using single-field Pydantic models, the SDK automatically
        # unwraps the value, so you access parameters["prefix"] directly
        "include_prefix": BooleanParam,
        "prefix": StringParam,
        "array_of_objects": ArrayParam,
    },
)
```
</PYTab>
</CodeTabs>

## Running a remote eval from a playground

To run a remote eval from a playground, select **+ Remote** from the Task pane and choose from the evals exposed in localhost or remote sources.

![Remote eval in playground](/docs/guides/remote-eval-in-playground.png)

## Configure remote eval sources

To configure remote eval source URLs for a project, navigate to **Configuration** > **Remote evals**. Then, select **+ Remote eval source** to configure a new remote eval source for your project.

![Configure remote eval](/docs/guides/configure-remote-eval.png)

## Language considerations

When implementing remote evals, be aware of these language-specific patterns:

| Feature | TypeScript | Python |
|---------|------------|---------|
| **Parameter validation** | Zod schemas (e.g., `z.string()`, `z.boolean()`) | Optional: Pydantic models with single `value` field |
| **Parameter access** | Direct access (e.g., `parameters.prefix`) | Dictionary access (e.g., `parameters["prefix"]` or `parameters.get("prefix")`) |
| **Validation approach** | Automatic via Zod | Optional via Pydantic validators or manual in task |
| **Prompt format** | `messages` array | Nested `prompt` and `options` objects |
| **Async handling** | `async`/`await` with promises | `async`/`await` with coroutines |

## Limitations

- The dataset defined in your remote eval will be ignored. Scorers defined in remote evals will be concatenated with playground scorers.
- Remote evals are supported in both TypeScript and Python.
