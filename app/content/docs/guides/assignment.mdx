---
title: "Assignment and mentions"
---

import { Callout } from "fumadocs-ui/components/callout";

# Assignment and mentions

Assignment in Braintrust allows you to assign rows to team members for review, analysis, or follow-up action. You can assign specific logs, experiment rows, or dataset entries to individuals and track progress by quickly filtering for annotation workflows.

## Assigning rows

You can assign any row in Braintrust to a team member across logs, experiments, or datasets. To assign a row, select the assignment column or use the row actions menu, and select the team member.

## Filtering by assignment

Use assignment filters to focus on relevant work. Filter by **Assigned to me** to see your current assignments, or select specific team members to view their workload. You can also filter by assignment status: assigned, unassigned, or completed rows. When entering human review mode, only the rows in the specified view will be shown.

## Mentions in comments

Mention team members in comments by typing `@` followed by their name and selecting from the autocomplete dropdown. Mentioned users receive email notifications with direct links to the specific row and comment.

## Email notifications

Braintrust automatically sends email notifications when rows are assigned to you or when someone mentions you in comments.

## Assignment workflows

Assignment works particularly well with [human review](./human-review) workflows, where you can assign specific rows that need human evaluation and distribute review work across multiple team members. You can also use assignment for quality assurance processes by assigning low-scoring results for investigation and tracking resolution of identified issues.

For collaborative evaluation, assignment enables coordination between engineering and product teams by assigning rows to subject matter experts for specialized review and ensuring comprehensive coverage of evaluation datasets.
