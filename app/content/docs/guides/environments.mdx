---
title: "Environments"
description: "Manage different versions of prompts across development, staging, and production"
---

import Image from 'next/image'
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";

# Environments

Environments in Braintrust allow you to manage different versions of prompts across your development lifecycle. You can pin specific versions of prompts to environments like development, staging, and production, enabling controlled deployment and testing workflows.

## Overview

An environment is a named collection that associates specific versions of prompts with a deployment context. This enables you to:

- **Maintain version control**: Pin stable prompt versions to production while testing new versions in development
- **Enable staged deployments**: Promote prompt versions through dev/staging/production pipelines
- **Support A/B testing**: Compare different prompt versions across environments
- **Isolate changes**: Test prompt modifications without affecting production systems

Currently, environments work with **prompts only**.

## Create environments

Environments are configured through the Braintrust UI in your organization settings. Each environment has:

- **Name**: A human-readable name (e.g., "Development", "Production")
- **Slug**: A unique identifier used in API calls (e.g., "dev", "prod")
- **Description**: Optional details about the environment's purpose

To create an environment:

1. Navigate to your organization settings
2. Go to the **Environments** section
3. Click **Add Environment**
4. Enter the name, slug, and optional description
5. Save the environment

## Associate prompts with environments

![Environments](/docs/guides/environments/environments.png)

Once you have environments set up, you can associate specific versions of prompts with them. This creates a mapping that tells Braintrust which version of a prompt to return when queried with an environment parameter.

You can make the association using the activity tab on the prompt view.

## Load prompts with environments

### Using the SDK

The Braintrust SDK supports loading prompts with environment parameters:

<CodeTabs>
<PYTab>

```python
import braintrust

# Load a prompt from a specific environment
prompt = braintrust.load_prompt(project="my-project", slug="my-prompt-slug", environment="production")
```

</PYTab>
<TSTab>

```typescript
import { loadPrompt } from "braintrust";

// Load a prompt from a specific environment
(async () => {
  const prompt = await loadPrompt({
    projectId: "my-project",
    slug: "my-prompt-slug",
    environment: "production",
  });
})();
```

</TSTab>
</CodeTabs>

### Using the REST API

You can load prompts with environment parameters directly via HTTP:

```javascript
(async () => {
  // TODO: Fill these in as needed
  const promptSlug = "";
  const apiKey = "";
  const projectId = "";
  const promptId = "";

  // Load by project id and prompt slug
  const r1 = await fetch(
    `https://api.braintrust.dev/v1/prompt?slug=${promptSlug}&project_id=${projectId}&environment=production`,
    {
      headers: {
        Authorization: "Bearer " + apiKey,
      },
    },
  );

  // Load by prompt ID + environment
  const r2 = await fetch(
    `https://api.braintrust.dev/v1/prompt/${promptId}?environment=production`,
    {
      headers: {
        Authorization: "Bearer " + apiKey,
      },
    },
  );
})();
```
