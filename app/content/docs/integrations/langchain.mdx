---
title: "<PERSON><PERSON><PERSON><PERSON>"
---

import { CodeTabs, PYTab, TSTab } from "#/ui/docs/code-tabs";

# Lang<PERSON>hain

Trace your LangChain applications by configuring a LangChain callback handler.

<CodeTabs>

<TSTab>

```typescript title="trace-langchain.ts" #skip-compile
import { BraintrustCallbackHandler } from "@braintrust/langchain-js";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";

initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const handler = new BraintrustCallbackHandler();

async function main() {
  const model = new ChatOpenAI({ modelName: "gpt-4o-mini" });

  await model.invoke("What is the capital of France?", {
    callbacks: [handler],
  });
}

main();
```

Learn more about [Lang<PERSON>hain callbacks](https://js.langchain.com/docs/how_to/#callbacks) in their documentation.

</TSTab>

<PYTab>

```python title="trace-langchain.py"
import asyncio
import os

from braintrust import init_logger
from braintrust_langchain import BraintrustCallbackHandler, set_global_handler
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI


async def main():
    init_logger(project="My Project", api_key=os.environ.get("BRAINTRUST_API_KEY"))

    handler = BraintrustCallbackHandler()
    set_global_handler(handler)

    # Initialize your LangChain components
    prompt = ChatPromptTemplate.from_template("What is 1 + {number}?")
    model = ChatOpenAI()

    # Create a simple chain
    chain = prompt | model

    # Use LangChain as normal - all calls will be logged to Braintrust
    response = await chain.ainvoke({"number": "2"})


if __name__ == "__main__":
    asyncio.run(main())
```

Learn more about [LangChain callbacks](https://python.langchain.com/docs/how_to/#callbacks) in their documentation.

</PYTab>

</CodeTabs>
