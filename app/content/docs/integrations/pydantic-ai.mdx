---
title: "Pydantic AI"
---

import ModuleInstaller from "#/ui/docs/module-installer";

# Pydantic AI

[Pydantic AI](https://ai.pydantic.dev) is a Python agent framework.

To configure tracing with Braintrust, first [configure the Braintrust Python SDK with OpenTelemetry support](/docs/integrations/opentelemetry#python-sdk-configuration).

Then configure OpenTelemetry and enable instrumentation on your agent.

<ModuleInstaller languages={["py"]} packageNames="braintrust[otel] pydantic-ai" />

```python
from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import Tracer<PERSON><PERSON>ider
from pydantic_ai.agent import Agent

# Configure the global OTel tracer provider
provider = TracerProvider()
trace.set_tracer_provider(provider)

# Send spans to Braintrust.
provider.add_span_processor(BraintrustSpanProcessor())

# Enable instrumentation on all agents
Agent.instrument_all()

agent = Agent(...)
```

This will automatically send interactions, tool calls, and performance metrics to Braintrust for monitoring and evaluation.
