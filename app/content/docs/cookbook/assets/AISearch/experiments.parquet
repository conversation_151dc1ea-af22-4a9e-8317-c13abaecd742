PAR1 ��,   ��   $   319a87d5-5750-4143-ae99-09e0541ef124$(�685f02f-a5c8-41ac-a9ab-50c2ac701bcc$   a764c0f2-82b6-45bc-a2bc-073965742a9f$   bf06e4d0-541d-47a6-a950-761399896ac1$   af3452d6-69c5-443d-a628-dbbffe4f880ex��8df07e96-f1b6-4ed1-b3a3-70acd1d3a426$   0f746214-de8d-4b09-a5ae-aabeee764421$   11bd9026-76fb-4675-b5c4-58a84e0aa8ac$   97c08e62-9a9d-477e-838d-b34370b4ae22$   f10866e1-3433-48cb-a5e6-569c6d22f58e ��,   ��e      Test   Baseline   Post-coffee break   Try alternative dataset   No RAG Final   TopK=2 
  After CoR   F*d experiment for demo   AlBe  (V P with previous grader ��,   ��      2024-01-01T01:41:52.332Z  > 9:57.379J  50:00.840Z 3.643B (2:06:59.226J 19:12.30Fp $2:24:17.74Z 48.151JT 38:16.23Fp ,3:13:03.331ZPTL   (�$   00b0b550-c99d-4ba1-8a78-208288707520 ,  	     ��L   ��>�   {"id" : "fac36c53-c882-458b-bf80-60d06c3e8a0d", "given_name7null,=mily_: email&`"<EMAIL>QHavatar_url" : null} ,  	     ��L   ���  {"tag": null, "dirty": true, "branch": "austin/ai-search", "commit": "0b6a3097e429f4db640095bb6c8030d7ceed0f39", "author_name": "Austin Moehle", "commit_time": "2023-12-31T18:36:14-07:00", "author_email": "<EMAIL>", "
Xmessag[<add notebooks"}ffals��82e150b87d8ec42cc1f2a4895a174a23e9981c59", "a	� n�,9:37:56-07:0�-update5  again"}f t�'�ff18ec5f1a1e1b8c2e31d5495f5a698dd1b4313�20:00:1�'U'Z ,  
0    ��L   ��@;   {"rag": false, "fruit": "apple", "grader": "gpt-3.5-turbo"}4 j? banana"B@ 4"}=Jw grae 48 condition�4caffeinated"}!�A }:%tru�� O � t:> f� , j�   BS >� }(~$ (3.5-turbo"} .2,  X     ��,   �
�I   ~   Test 0b6a3097e429f4db640095bb6c8030d7ceed0f39 austin/ai-search A�F Moehle add notebooks dirty false "apple" "gpt-3.5-turbo"{   Baseline 0�� �� banana"	�X4"�   Post-coffee break��!@4" "caffeinated"�!�Pry alternative datase����	�<v   No RAG Final�
�
 �opK=2�t rt trub� ��After CI�n	�:�6�   )�Hexperiment for demo�� �� I�   AJ��>82e150b87d8ec42cc1f2a4895a174a23e9981c59 austin/ai-search Austi�<update�?, again clean-%	�� V� �with previous grader ff18ec5f1a1e1b8c2e31d5495f5a698dd1b431��V� ��)�4gpt-3.5-turbo"�5 
duckdb_schema %id%  %name%  %created%  %
project_id%  %user%  %	repo_info%  %metadata%  %search_text%  �&  id��&<$f10866e1-3433-48cb-a5e6-569c6d22f58e$0f746214-de8d-4b09-a5ae-aabeee764421 ($f10866e1-3433-48cb-a5e6-569c6d22f58e$0f746214-de8d-4b09-a5ae-aabeee764421   &  name��&�<Try alternative datasetAlternative dataset (Try alternative datasetAlternative dataset   &  created��&�	<2024-01-01T03:13:03.331Z2024-01-01T01:41:52.332Z (2024-01-01T03:13:03.331Z2024-01-01T01:41:52.332Z   & % 
project_id��&�
&�$00b0b550-c99d-4ba1-8a78-208288707520$00b0b550-c99d-4ba1-8a78-208288707520 $00b0b550-c99d-4ba1-8a78-208288707520$00b0b550-c99d-4ba1-8a78-208288707520   & % user��&�&�
�{"id" : "fac36c53-c882-458b-bf80-60d06c3e8a0d", "given_name" : null, "family_name" : null, "email" : "<EMAIL>", "avatar_url" : null}�{"id" : "fac36c53-c882-458b-bf80-60d06c3e8a0d", "given_name" : null, "family_name" : null, "email" : "<EMAIL>", "avatar_url" : null} �{"id" : "fac36c53-c882-458b-bf80-60d06c3e8a0d", "given_name" : null, "family_name" : null, "email" : "<EMAIL>", "avatar_url" : null}�{"id" : "fac36c53-c882-458b-bf80-60d06c3e8a0d", "given_name" : null, "family_name" : null, "email" : "<EMAIL>", "avatar_url" : null}   & % 	repo_info�
�&�&��{"tag": null, "dirty": true, "branch": "austin/ai-search", "commit": "ff18ec5f1a1e1b8c2e31d5495f5a698dd1b43139", "author_name": "Austin Moehle", "commit_time": "2023-12-31T20:00:14-07:00", "author_email": "<EMAIL>", "commit_message": "update notebook again"}�{"tag": null, "dirty": false, "branch": "austin/ai-search", "commit": "82e150b87d8ec42cc1f2a4895a174a23e9981c59", "author_name": "Austin Moehle", "commit_time": "2023-12-31T19:37:56-07:00", "author_email": "<EMAIL>", "commit_message": "update notebook again"} �{"tag": null, "dirty": true, "branch": "austin/ai-search", "commit": "ff18ec5f1a1e1b8c2e31d5495f5a698dd1b43139", "author_name": "Austin Moehle", "commit_time": "2023-12-31T20:00:14-07:00", "author_email": "<EMAIL>", "commit_message": "update notebook again"}�{"tag": null, "dirty": false, "branch": "austin/ai-search", "commit": "82e150b87d8ec42cc1f2a4895a174a23e9981c59", "author_name": "Austin Moehle", "commit_time": "2023-12-31T19:37:56-07:00", "author_email": "<EMAIL>", "commit_message": "update notebook again"}   & % metadata��&�&� {"rag": true, "grader": "gpt-4"};{"rag": false, "fruit": "apple", "grader": "gpt-3.5-turbo"}  {"rag": true, "grader": "gpt-4"};{"rag": false, "fruit": "apple", "grader": "gpt-3.5-turbo"}   &  search_text��&�<�Try alternative dataset 0b6a3097e429f4db640095bb6c8030d7ceed0f39 austin/ai-search Austin Moehle add notebooks dirty false "gpt-4"�Alternative dataset 82e150b87d8ec42cc1f2a4895a174a23e9981c59 austin/ai-search Austin Moehle update notebook again clean true "gpt-4" (�Try alternative dataset 0b6a3097e429f4db640095bb6c8030d7ceed0f39 austin/ai-search Austin Moehle add notebooks dirty false "gpt-4"�Alternative dataset 82e150b87d8ec42cc1f2a4895a174a23e9981c59 austin/ai-search Austin Moehle update notebook again clean true "gpt-4"    & (DuckDB m
  PAR1