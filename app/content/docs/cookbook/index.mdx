---
title: "Cookbook"
---

import { CookbookWithFilter } from "#/ui/docs/cookbook-client-wrapper";
import recipes from "./recipes.json";

# Cookbook

This cookbook, inspired by [OpenAI's cookbook](https://cookbook.openai.com/), is a collection of recipes for common
use cases of [Braintrust](/). Each recipe is an open source self-contained example, hosted on
[GitHub](https://github.com/braintrustdata/braintrust-cookbook). We welcome community contributions
and aspire for the cookbook to be a collaborative, living, breathing collection of best practices for
building high quality AI products.

<CookbookWithFilter recipes={recipes} />
