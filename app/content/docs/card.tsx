import Link from "next/link";

export const IntegrationCard = ({
  title,
  description,
  href,
  children,
}: {
  title: string;
  description?: string;
  href: string;
  children: React.ReactNode;
}) => {
  return (
    <Link
      href={href}
      // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
      className="not-prose flex flex-col items-center rounded-md border bg-primary-50 p-4 py-8 text-primary-900 no-underline! transition-colors hover:bg-primary-100 hover:text-primary-950"
    >
      {children}
      <span className="mt-2 text-center leading-snug">{title}</span>
    </Link>
  );
};
