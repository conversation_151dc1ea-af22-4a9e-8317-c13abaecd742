import { clerkMiddleware } from "@clerk/nextjs/server";
import {
  NextResponse,
  type NextFetchEvent,
  type NextRequest,
} from "next/server";
import { API_KEY_AUTH } from "./utils/auth/constants";
import { type NextMiddlewareResult } from "next/dist/server/web/types";
import { addContentTypeNoSniff, addCSP, addXFrameOptions } from "./security";

// This is a proposed fix (https://braintrustdata.slack.com/archives/C07BXC7G1B3/p1726318088850869)
// for the redirect caching issues we're seeing.
const maybeFixRedirectCaching = (res?: NextMiddlewareResult) => {
  const redirect = res?.headers.get("location") || "";
  if (
    redirect.startsWith("https://clerk.") ||
    redirect.includes(".clerk.accounts.dev")
  ) {
    res!.headers.set("cache-control", "no-store");
  }
};

const clerkMiddlewareHandler = async (
  req: NextRequest,
  evt: NextFetchEvent,
) => {
  const clerkHandler = clerkMiddleware();
  const res = await clerkHandler(req, evt);
  maybeFixRedirectCaching(res);

  return res;
};

export default async function middleware(
  req: NextRequest,
  evt: NextFetchEvent,
) {
  const res =
    (await (async () => {
      if (API_KEY_AUTH) {
        return;
      }
      return await clerkMiddlewareHandler(req, evt);
    })()) ?? NextResponse.next();

  await addCSP(req, res);
  await addXFrameOptions(req, res);
  await addContentTypeNoSniff(req, res);

  return res;
}

// NOTE: This is a WIP from Clerk (https://braintrustdata.slack.com/archives/C07BXC7G1B3/p1721330380252939?thread_ts=**********.522989&cid=C07BXC7G1B3)
// Colin provided a useful framework for how this works and when we might need to modify it
// (https://braintrustdata.slack.com/archives/C07BXC7G1B3/p1721449939478729?thread_ts=1721427168.037839&cid=C07BXC7G1B3)
//
// > The main thing you need to know is, if you ever want to authenticate a route with an extension (e.g. braintrust.dev/data/example.json),
// > you may need to change the regex so that it's not skipped.  I used .json as an example because it's the most debatable inclusion in the
// > matcher we shared - the rest of the extensions in there are more likely static files that you intentionally want to skip.
//
// > Unfortunately, nextjs middleware doesn't have context on the what lays within the app router, so we can't just say "skip static files."
// > That regex is a heuristic that should carry you, but it may need some tweaking as you go.
export const config = {
  matcher: [
    // Skip all static files, unless found in search params
    `/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff?|woff2?|ico|docx?|xlsx?|zip|webmanifest)).*)`,

    // Note: This is just an explicit line, but it does nothing.
    // Default behavior of middleware is to run on all routes, and
    // we need to have exclusions here (or in the middleware with conditionals)
    `/(api|trpc)(.*)`,
  ],
};
