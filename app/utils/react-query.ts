import {
  type DefaultError,
  type DefinedInitialDataOptions,
  type DefinedUseQueryResult,
  type QueryClient,
  type UndefinedInitialDataOptions,
  type UseQueryOptions,
  useQuery,
  useQueryClient,
  type UseQueryResult,
  type QueryKey,
  type QueryFilters,
  type QueryState,
} from "@tanstack/react-query";
import {
  type ServerActionSignature,
  type ServerActionArg,
  type ServerActionRet,
  invokeServerAction,
} from "#/utils/invoke-server-action";
import { useCallback } from "react";
import { useAuth } from "@clerk/nextjs";
import { useQueryMemo } from "./duckdb";

declare module "@tanstack/react-query" {
  interface Register {
    queryMeta: {
      onRemoveFromCache?: <T>(
        queryState: QueryState<T>,
        queryKey: QueryKey,
      ) => void;
      disableGlobalErrorCallback?: boolean;
      disableGlobalErrorToast?: boolean;
      disableGlobalErrorConsole?: boolean;
      globalErrorSentryContext?: Record<string, unknown>;
    };
  }
}

interface CombinedResult<T> {
  data: T[];
  isPending: boolean;
  isLoading: boolean;
  isFetching: boolean;
  fetching: boolean[];
  pending: boolean[];
  errors: Error[];
  isPlaceholderData: boolean[];
}

// https://github.com/TanStack/query/issues/6369
const EMPTY_RESULT = {
  data: [],
  isPending: true,
  isLoading: true,
  isFetching: true,
  fetching: [],
  pending: [],
  errors: [],
  isPlaceholderData: [],
};

// need to use this in useQueries calls to prevent infinite rerenders
// https://github.com/TanStack/query/issues/5137
export const combineResults = <T>(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  results: UseQueryResult<T, any>[],
): CombinedResult<T> => {
  if (results.length === 0) return EMPTY_RESULT;

  return {
    data: results.filter((r) => !r.isError && r.data).map((r) => r.data!),
    isPending: results.some((r) => r.isPending),
    isLoading: results.some((r) => r.isLoading),
    isFetching: results.some((r) => r.isFetching),
    fetching: results.map((r) => r.isFetching),
    pending: results.map((r) => r.isPending),
    errors: results.flatMap((r) => (r.error ? [r.error] : [])),
    isPlaceholderData: results.map((r) => r.isPlaceholderData),
  };
};

export function invalidateId(
  queryClient: QueryClient,
  id: string | undefined | null,
  queryName?: string | null,
) {
  if (!id) {
    return;
  }
  queryClient.invalidateQueries({
    predicate: ({ queryKey }) => {
      const firstQueryKey = queryKey[0];
      const hasId = Array.isArray(firstQueryKey)
        ? firstQueryKey.some((k) => k === id)
        : firstQueryKey === id;
      if (!hasId) {
        return false;
      }

      if (queryName) {
        const secondQueryKey = queryKey[1];
        return secondQueryKey === queryName;
      }

      return true;
    },
  });
}

export function keepPreviousQueries<T>(
  queryClient: QueryClient,
  queryFilters: QueryFilters,
  valuePredicate?: (value: T) => boolean,
) {
  return () => {
    return getExistingQueryData({
      queryClient,
      queryFilters,
      valuePredicate,
    }).latestValue;
  };
}

export function getExistingQueryData<T>({
  queryClient,
  queryFilters,
  valuePredicate,
}: {
  queryClient: QueryClient;
  queryFilters: QueryFilters;
  valuePredicate?: (value: T) => boolean;
}) {
  const previous = queryClient.getQueriesData<T>(queryFilters);
  const latest = previous.reduce(
    (
      latest: {
        latestUpdatedAt: number;
        latestValue: T | undefined;
        queryKey: QueryKey;
      },
      [queryKey, value],
    ) => {
      const state = queryClient.getQueryState(queryKey);
      if (
        state?.dataUpdatedAt &&
        state.dataUpdatedAt > latest.latestUpdatedAt &&
        value &&
        (!valuePredicate || valuePredicate(value))
      ) {
        return {
          latestUpdatedAt: state.dataUpdatedAt,
          latestValue: value,
          queryKey,
        };
      }
      return latest;
    },
    { latestUpdatedAt: 0, latestValue: undefined, queryKey: [] },
  );
  return latest;
}

export function useQueryFunc<
  F extends ServerActionSignature,
  TError = DefaultError,
  TData = ServerActionRet<F>,
>(args: {
  fName: string;
  args: ServerActionArg<F>;
  serverData: ServerActionRet<F>;
  queryOptions?: Omit<
    DefinedInitialDataOptions<ServerActionRet<F>, TError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >;
  queryClient?: QueryClient;
  keyArgs?: QueryKey;
}): DefinedUseQueryResult<TData, TError> & {
  invalidate: () => Promise<void>;
  setData: (data: TData) => void;
};

export function useQueryFunc<
  F extends ServerActionSignature,
  TError = DefaultError,
  TData = ServerActionRet<F>,
>(args: {
  fName: string;
  args: ServerActionArg<F>;
  serverData?: undefined;
  queryOptions?: Omit<
    UndefinedInitialDataOptions<ServerActionRet<F>, TError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >;
  queryClient?: QueryClient;
  keyArgs?: QueryKey;
}): UseQueryResult<TData, TError> & {
  invalidate: () => Promise<void>;
  setData: (data: TData) => void;
};

export function useQueryFunc<
  F extends ServerActionSignature,
  TError = DefaultError,
  // https://github.com/TanStack/query/issues/7560
  TData = ServerActionRet<F>,
>({
  // Name of the function. Together, the function name and JSON-serialized
  // arguments form a unique key that is used for client-side caching of the
  // result. The name must match the server API route defined at
  // `app/app/api/actions/{fName}/route.ts`. You can use the
  // `./scripts/generate_server_action_route.py` script to generate a route for
  // your server action function.
  fName,
  // Argument object to pass to the function. Should be JSON-serializable.
  args,
  // any initial data populated at server time. Workaround for some weird issues
  // with initial data being specified in some subset of queries that share the same queryKey
  serverData,
  // Additional query options to the react-query invocation.
  queryOptions,
  // Optional QueryClient if you're calling outside of a provider
  queryClient: initialQueryClient,
  // Specify additional parameters used to invalidate the query
  keyArgs,
}: {
  fName: string;
  args: ServerActionArg<F>;
  serverData?: ServerActionRet<F>;
  // https://github.com/TanStack/query/discussions/3227
  queryOptions?: Omit<
    UseQueryOptions<ServerActionRet<F>, TError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >;
  queryClient?: QueryClient;
  keyArgs?: QueryKey;
}) {
  const { getToken } = useAuth();
  const queryClient = useQueryClient(initialQueryClient);
  const queryKey = useQueryMemo(queryFuncKey({ fName, args, keyArgs }));

  if (queryClient?.getQueryData(queryKey) === undefined && serverData) {
    // https://tanstack.com/query/latest/docs/framework/react/guides/prefetching#manually-priming-a-query
    // hack in case other queries with the same queryKey started a query but:
    // 1. didn't provide initialData
    // 2. didn't finish loading yet
    // https://github.com/TanStack/query/issues/6240#issuecomment-1781155970
    // https://github.com/TanStack/query/discussions/1331#discussioncomment-4864764
    queryClient?.setQueryData(queryKey, () => serverData);
  }

  const queryResult = useQuery(
    {
      queryKey,
      queryFn: ({ signal }: { signal: AbortSignal }) =>
        invokeServerAction<F>({ fName, args, getToken, signal }),
      ...queryOptions,
      initialData: serverData,
    },
    queryClient,
  );
  // mimic the bound mutate function from swr
  // https://swr.vercel.app/docs/mutation#bound-mutate
  const invalidate = useCallback(() => {
    return queryClient.invalidateQueries({ queryKey });
  }, [queryClient, queryKey]);

  const setData = useCallback(
    (data: TData) => {
      queryClient.setQueryData(queryKey, data);
    },
    [queryClient, queryKey],
  );

  return {
    ...queryResult,
    invalidate,
    setData,
  };
}

export function queryFuncKey<F extends ServerActionSignature>({
  fName,
  args,
  keyArgs,
}: {
  fName: string;
  args: ServerActionArg<F>;
  keyArgs?: QueryKey;
}) {
  return [fName, args, ...(keyArgs ?? [])];
}
