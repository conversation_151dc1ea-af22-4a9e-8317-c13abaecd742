import { atom } from "jotai";
import { useMemo } from "react";
import type { Environment, EnvironmentObject } from "@braintrust/typespecs";

// Base atoms for environments data
export const environmentsAtom = atom<Environment[]>([]);

export const environmentAssociationsAtom = atom<EnvironmentObject[]>([]);

// Derived atom to get environments by their slugs
export const environmentsBySlugAtom = atom((get) => {
  const environments = get(environmentsAtom);
  return Object.fromEntries(environments.map((env) => [env.slug, env]));
});

// Parameterized atom factory to get environment associations for a specific object
export const useEnvironmentAssociationsForObjectAtom = (
  objectType: string,
  objectId: string,
) => {
  return useMemo(
    () =>
      atom((get) => {
        if (!objectType || !objectId) return [];
        const associations = get(environmentAssociationsAtom);
        return associations.filter(
          (assoc) =>
            assoc.object_type === objectType && assoc.object_id === objectId,
        );
      }),
    [objectType, objectId],
  );
};

// Parameterized atom factory to get environments for a specific version
export const useEnvironmentsForVersionAtom = (
  objectType: string,
  objectId: string,
  versionId: string,
) => {
  return useMemo(
    () =>
      atom((get) => {
        if (!objectType || !objectId || !versionId) return [];
        const associations = get(environmentAssociationsAtom);
        const environmentsBySlug = get(environmentsBySlugAtom);

        return associations
          .filter(
            (assoc) =>
              assoc.object_type === objectType &&
              assoc.object_id === objectId &&
              assoc.object_version === versionId,
          )
          .map((assoc) => environmentsBySlug[assoc.environment_slug])
          .filter(Boolean);
      }),
    [objectType, objectId, versionId],
  );
};

// Parameterized atom factory to get environment slugs for a specific version
export const useEnvironmentSlugsForVersionAtom = (
  objectType: string,
  objectId: string,
  versionId: string,
) => {
  return useMemo(
    () =>
      atom((get) => {
        if (!objectType || !objectId || !versionId) return [];
        const associations = get(environmentAssociationsAtom);

        return associations
          .filter(
            (assoc) =>
              assoc.object_type === objectType &&
              assoc.object_id === objectId &&
              assoc.object_version === versionId,
          )
          .map((assoc) => assoc.environment_slug);
      }),
    [objectType, objectId, versionId],
  );
};
