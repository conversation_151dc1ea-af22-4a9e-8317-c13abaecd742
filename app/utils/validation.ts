import { type ZodError, type ZodIssue } from "zod";

function zodIssueToString(issue: ZodIssue, indent: number): string {
  const indentText = " ".repeat(indent);
  switch (issue.code) {
    case "invalid_union":
      return (
        `\n${indentText}Expected one of:\n` +
        issue.unionErrors
          .map((e) => `${zodErrorToString(e, indent + 2, true)}`)
          .join(`\n${indentText}  --or\n`)
      );
    case "invalid_type":
      return `Expected ${issue.expected}`;
    case "invalid_literal":
      return `${issue.expected}`;
    default:
      return issue.message;
  }
}

export function zodErrorToString(
  e: ZodError,
  indent: number,
  includePath: boolean,
): string {
  return e.issues
    .map((issue) => {
      return `${" ".repeat(indent)}${
        includePath ? issue.path.join(".") + ": " : ""
      }${zodIssueToString(issue, indent + 2)}`;
    })
    .join("\n");
}
