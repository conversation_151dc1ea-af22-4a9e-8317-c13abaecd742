/**
 * Utility functions for analytics tracking
 */

/**
 * Extract meaningful page names from URL paths
 * Converts full paths like "/app/orgname/p/projectname/logs" to clean names like "logs"
 * Handles server-side rendering by checking if window is available
 */
export function getPageName(pathname?: string): string {
  // Handle server-side rendering
  if (typeof window === "undefined") {
    return "";
  }

  // Use provided pathname or fall back to current location
  const currentPathname = pathname || window.location.pathname;

  // Extract meaningful page names from paths
  if (currentPathname.includes("/logs")) return "logs";
  if (currentPathname.includes("/playgrounds")) return "playgrounds";
  if (currentPathname.includes("/experiments")) return "experiments";
  if (currentPathname.includes("/datasets")) return "datasets";
  if (currentPathname.includes("/prompts")) return "prompts";
  if (currentPathname.includes("/functions")) return "functions";
  if (currentPathname.includes("/scorers")) return "scorers";
  if (currentPathname.includes("/settings/billing")) return "billing";
  if (currentPathname.includes("/settings/team")) return "team-settings";
  if (currentPathname.includes("/settings")) return "settings";
  if (currentPathname.includes("/p/")) return "project";
  if (currentPathname.includes("/app/")) return "dashboard";
  return "unknown";
}
