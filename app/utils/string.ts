export const alphaNumericRegex = /^\w+$/;

export const isAlphaNumeric = (val: string) => {
  return alphaNumericRegex.test(val);
};

export const truncateMiddle = (str: string, maxLength = 100) => {
  if (str.length <= maxLength) {
    return str;
  }

  const halfLength = Math.floor((maxLength - 3) / 2);
  const start = str.slice(0, halfLength);
  const end = str.slice(-halfLength);

  return `${start}...${end}`;
};

export const capitalize = (str: string) => {
  if (str.length < 1) {
    return str;
  }
  if (str.length === 1) {
    return str.toUpperCase();
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const stringify = (value: unknown) => {
  if (typeof value === "object") {
    try {
      return JSON.stringify(value);
    } catch (e) {}
  }
  return value?.toString();
};

export const parse = (value: unknown) => {
  if (typeof value === "string") {
    try {
      return JSON.parse(value);
    } catch (e) {}
  }
  return value;
};
