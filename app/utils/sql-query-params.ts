import { singleQuote } from "#/utils/sql-utils";

// Utility for managing query params when building SQL queries.
export class SqlQueryParams {
  private params_: unknown[];

  constructor(startingParams?: unknown[]) {
    this.params_ = startingParams ?? [];
  }

  public get params(): unknown[] {
    return this.params_;
  }

  public add(p: unknown) {
    this.params_.push(p);
    return `$${this.params.length}`;
  }
}

// Interpolates a set of non-complex serializable params into the query string,
// doing direct string substitution for the keywords '$1', '$2', ..., '$n'.
// Mainly useful for quick-and-dirty debugging.
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function substituteParamsDebug(queryStr: string, queryParams: any[]) {
  // Interpolate in reverse order so that multi-digit replacements don't get
  // partially-interpolated by single-digit replacements.
  const reversedParams = [...queryParams];
  reversedParams.reverse();
  return reversedParams.reduce((queryStr, param, reversedIdx) => {
    let interp: string = "";
    if (param === null || param === undefined) {
      interp = "null";
    } else if (typeof param === "object") {
      throw new Error(
        `Substitution not supported for param ${JSON.stringify(param)}`,
      );
    } else if (typeof param === "string") {
      interp = singleQuote(param);
    } else {
      interp = String(param);
    }
    const idx = queryParams.length - reversedIdx;
    return queryStr.replaceAll(`$${idx}`, interp);
  }, queryStr);
}
