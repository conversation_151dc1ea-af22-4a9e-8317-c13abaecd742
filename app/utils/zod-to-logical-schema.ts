import { type z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";
import {
  jsonSchemaObjectSchema,
  type LogicalSchema,
} from "@braintrust/btql/schema";

export function zodToLogicalSchema(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  schema: z.ZodObject<any, any>,
): LogicalSchema {
  return jsonSchemaObjectSchema.parse(zodToJsonSchema(schema));
}
