import { test, expect } from "vitest";

/*

Write a bunch of tests for

export function parseDateWithMicroseconds(dateString: string) {
  // Parse the date up to milliseconds
  const date = new Date(dateString.slice(0, 23) + "Z"); // Standard ISO format with milliseconds
  if (Number.isNaN(date.getTime())) {
    return new Date(dateString).getTime();
  }

  const milliseconds = date.getTime(); // Get milliseconds since Unix epoch

  // Extract microseconds part and convert to milliseconds
  const microsecondPart = dateString.slice(23, 26); // Get the last three digits for microseconds
  const fractionalMilliseconds = parseInt(microsecondPart, 10) / 1000;

  // Combine and return the full timestamp
  return (
    milliseconds +
    (Number.isNaN(fractionalMilliseconds) ? 0 : fractionalMilliseconds)
  );
}
*/

import { parseDateWithMicroseconds } from "./duckdb-helpers";

test("parseDateWithMicroseconds", () => {
  expect(parseDateWithMicroseconds("2021-01-01T00:00:00.123456Z")).toBe(
    new Date("2021-01-01T00:00:00.123Z").getTime() + 0.456,
  );
  expect(parseDateWithMicroseconds("2021-01-01T00:00:00.123Z")).toBe(
    new Date("2021-01-01T00:00:00.123Z").getTime(),
  );
  expect(parseDateWithMicroseconds("2021-01-01T00:00:00Z")).toBe(
    new Date("2021-01-01T00:00:00Z").getTime(),
  );
  expect(parseDateWithMicroseconds("2021-01-01T00:00:00.123456Z")).toBe(
    new Date("2021-01-01T00:00:00.123Z").getTime() + 0.456,
  );
  expect(parseDateWithMicroseconds("2021-01-01T00:00:00.1234567Z")).toBe(
    new Date("2021-01-01T00:00:00.123Z").getTime() + 0.456,
  );
});
