import "@tanstack/react-table";
import { type DuckDBJSONType } from "./schema";

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      BRAINTRUST_VERSION: string;
    }
  }
}

declare module "*.wasm" {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const value: any;
  export default value;
}

declare module "@tanstack/react-table" {
  interface ColumnMeta<TData extends RowData, TValue> {
    name: string;
    header: React.ComponentType<HeaderContext<TData, TValue>>;
    type: Field<TData[keyof TData]>["type"];
    typeHint?: DuckDBJSONType;
    isNumeric?: boolean;
    isTimestamp?: boolean;
    internalType?: "checkbox" | "row_comparison" | "star";
    path: string[];
    parseValue?: (
      value: unknown,
      metaType: DataType,
      typeHint?: DuckDBJSONType,
    ) => string;
    moreText?: boolean;
    ignoreMultilineRendering?: boolean;
    pinnedColumnIndex?: number;
    colSpan?: boolean;
    customColumnId?: string;
    isGridLayout?: boolean;
    allowHoverableTooltip?: boolean;
    groupColumnName?: string;
    fetchFullContent?: (
      rowId: string,
      value: unknown,
      previewLength?: number,
    ) => Promise<unknown>;
  }
}
