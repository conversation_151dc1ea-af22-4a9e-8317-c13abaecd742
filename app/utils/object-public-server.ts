import { ANON_USER_ID } from "#/utils/constants";
import type { Permission } from "@braintrust/typespecs";
import { aclSchema } from "@braintrust/typespecs";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { helper as getAcls } from "#/pages/api/acl/get";
import { type PublicObjectType } from "./object-public-client";

export type GetIsPublicInput = {
  objectType: PublicObjectType;
  objectId: string;
  // The set of permissions the user has on this object. This should usually
  // come from querying `getObjectAclPermissions` (defined in
  // #/utils/object-acl-permissions).
  objectPermissions: Permission[];
};

export type GetIsPublicOutput =
  | { status: "found"; value: boolean }
  | { status: "missing_permission" };

export async function getIsPublic(
  { objectType, objectId, objectPermissions }: GetIsPublicInput,
  authLookupRaw?: AuthLookup,
): Promise<GetIsPublicOutput> {
  if (!objectPermissions.includes("read_acls")) {
    return { status: "missing_permission" };
  }
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const schemas = aclSchema.array().parse(
    await getAcls(
      {
        object_type: objectType,
        object_id: objectId,
        user_id: [ANON_USER_ID],
        permission: ["read"],
      },
      authLookup,
    ),
  );

  return { status: "found", value: schemas.length > 0 };
}

export async function getIsPublicPartial(
  input: Omit<GetIsPublicInput, "objectId" | "objectPermissions"> &
    Partial<Pick<GetIsPublicInput, "objectId" | "objectPermissions">>,
  authLookup?: AuthLookup,
): Promise<GetIsPublicOutput | { status: "missing_inputs" }> {
  if (!(input.objectId && input.objectPermissions)) {
    return { status: "missing_inputs" };
  }
  return await getIsPublic(
    {
      ...input,
      objectId: input.objectId,
      objectPermissions: input.objectPermissions,
    },
    authLookup,
  );
}
