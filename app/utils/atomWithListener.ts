import { useCallback, useLayoutEffect } from "react";
import { atom, type Getter, type Setter, type SetStateAction } from "jotai";
import { useAtomCallback } from "jotai/utils";

export type AtomListenerCallback<Value> = (
  get: Getter,
  set: Setter,
  newVal: Value,
  prevVal: Value,
) => void;

export function atomWithListeners<Value>(
  initialValue: Value,
  onSet?: (get: Getter, set: Setter, newVal: Value, prevVal: Value) => void,
) {
  const baseAtom = atom(initialValue);
  const listeners: AtomListenerCallback<Value>[] = [];
  const anAtom = atom(
    (get) => get(baseAtom),
    (get, set, arg: SetStateAction<Value>) => {
      const prevVal = get(baseAtom);
      set(baseAtom, arg);
      const newVal = get(baseAtom);
      onSet?.(get, set, newVal, prevVal);
      listeners.forEach((callback) => {
        callback(get, set, newVal, prevVal);
      });
    },
  );

  const useListener = (callback?: AtomListenerCallback<Value>) => {
    const initialSetter = useAtomCallback(
      useCallback(
        (get, set) => {
          const prevVal = get(baseAtom);
          const newVal = get(baseAtom);
          callback?.(get, set, newVal, prevVal);
        },
        [callback],
      ),
    );
    useLayoutEffect(() => {
      if (callback) {
        listeners.push(callback);
        initialSetter();
      }
      return () => {
        if (callback) listeners.splice(listeners.indexOf(callback), 1);
      };
    }, [callback, initialSetter]);
  };
  return [anAtom, useListener] as const;
}
