import "server-only";

import * as <PERSON><PERSON> from "@sentry/nextjs";

/**
 * Be careful about sending server-side errors to Sentry
 * Before calling, ensure that this is not an on-prem customer or that the error does
 * not contain customer data.
 *
 * @param error The error to send to <PERSON>try
 * @param tags Optional tags to add to the error
 */
export async function captureSentryServerException({
  error,
  tags,
}: {
  error: Error | string;
  tags?: Record<string, string>;
}) {
  Sentry.captureException(error, {
    extra: {
      isSafeForServerSide: true,
    },
    tags,
  });
}

/**
 * Be careful about sending server-side messages to Sentry
 * Before calling, ensure that this is not an on-prem customer or that the message does
 * not contain customer data.
 *
 * @param message The message to send to Sentry
 * @param tags Optional tags to add to the message
 */
export async function captureSentryServerMessage({
  message,
  tags,
}: {
  message: string;
  tags?: Record<string, string>;
}) {
  Sentry.captureMessage(message, {
    extra: {
      isSafeForServerSide: true,
    },
    tags,
  });
}
