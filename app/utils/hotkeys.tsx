import { type Keys } from "react-hotkeys-hook";

export const renderHotkey = (hotkey: Keys): string => {
  const keys = `${hotkey}`.split("+").map((key) => key.trim());

  const isMac = navigator.platform.toUpperCase().indexOf("MAC") >= 0;

  const symbols: { [key: string]: string } = {
    Mod: isMac ? "⌘" : "Cmd",
    Ctrl: isMac ? "^" : "Ctrl",
    Shift: isMac ? "⇧" : "Shift",
    Alt: isMac ? "⌥" : "Alt",
    enter: "⏎",
    backspace: "⌫",
    tab: "⇥",
    up: "↑",
    down: "↓",
    left: "←",
    right: "→",
    delete: "Del",
    escape: "Esc",
    esc: "Esc",
    space: "Space",
  };

  return (
    keys
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      .map((key) => symbols[key as string] || key.toUpperCase())
      .join("")
  );
};
