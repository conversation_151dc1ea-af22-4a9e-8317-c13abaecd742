import {
  type BtPg,
  PoolingBtPg,
  NonPoolingBtPg,
} from "@braintrust/local/bt-pg";
import { Pool as NodePgPool } from "pg";

let _conn: BtPg | null = null;

export function getServiceRoleSupabase(): BtPg {
  if (!_conn) {
    if (process.env.VERCEL) {
      // For serverless environments, use the NonPoolingBtPgClient, since we
      // assume we also are connecting to a server-side connection pooler (e.g.
      // supavisor). In this case, the complexity of managing client-side
      // connection pools and properly closing them is not worth it.
      _conn = new NonPoolingBtPg({
        connectionString: process.env.SUPABASE_PG_URL!,
      });
    } else {
      // In long-running servers, we use client-side connection pooling, where
      // the pool can live as long as the server is around.
      _conn = new PoolingBtPg(
        new NodePgPool({
          connectionString: process.env.SUPABASE_PG_URL!,
          // This is the default, but setting it explicitly for clarity.
          max: 10,
          // The default is 10000 (ms), which causes problems in the dev server
          // environment, which seems to accumulate a lot of idle connections and
          // overloads the backend postgres server.
          idleTimeoutMillis: 1000,
        }),
      );
    }

    _conn.onError((err, client) => {
      console.error("Received error on client", client, "\n", err);
    });
  }
  return _conn;
}
