import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export function usePreventNavigation(isDirty: boolean) {
  const [showNavigationConfirmation, setShowNavigationConfirmation] =
    useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<
    (() => void) | null
  >(null);
  const router = useRouter();

  const handleAnchorClick = useCallback(
    (e: MouseEvent) => {
      // allow opening in new tab
      if (
        e.button !== 0 ||
        e.metaKey ||
        e.ctrlKey ||
        !(e.currentTarget instanceof HTMLAnchorElement)
      ) {
        return;
      }

      const targetUrl = e.currentTarget.href;
      const currentUrl = window.location.href;
      if (targetUrl !== currentUrl) {
        e.preventDefault();
        setShowNavigationConfirmation(true);
        setPendingNavigation(() => () => {
          router.push(targetUrl);
        });
      }
    },
    [router],
  );

  const addAnchorListeners = useCallback(() => {
    const anchorElements = document.querySelectorAll("a[href]");
    anchorElements.forEach(
      (anchor) =>
        anchor instanceof HTMLAnchorElement &&
        anchor.addEventListener("click", handleAnchorClick),
    );
  }, [handleAnchorClick]);

  useEffect(() => {
    if (!isDirty) {
      return;
    }

    const mutationObserver = new MutationObserver(addAnchorListeners);
    mutationObserver.observe(document.body, { childList: true, subtree: true });
    addAnchorListeners();

    return () => {
      mutationObserver.disconnect();
      const anchorElements = document.querySelectorAll("a[href]");
      anchorElements.forEach(
        (anchor) =>
          anchor instanceof HTMLAnchorElement &&
          anchor.removeEventListener("click", handleAnchorClick),
      );
    };
  }, [addAnchorListeners, handleAnchorClick, isDirty]);

  useEffect(() => {
    if (!isDirty) {
      return;
    }

    const beforeUnloadHandler = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = ""; // required for Chrome
    };

    // TODO: make popstate work to prevent back navigation
    // const handlePopState = (e: PopStateEvent) => {
    //   console.log("popstate", e);
    //   const confirmLeave = window.confirm(
    //     "You have unsaved changes. Are you sure you want to leave?",
    //   );
    //   if (!confirmLeave) {
    //     e.preventDefault();
    //     window.history.pushState(null, "", window.location.href);
    //   }
    // };

    window.addEventListener("beforeunload", beforeUnloadHandler);
    // window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("beforeunload", beforeUnloadHandler);
      //   window.removeEventListener("popstate", handlePopState);
    };
  }, [isDirty]);

  const handleConfirmNavigation = () => {
    setShowNavigationConfirmation(false);
    if (pendingNavigation) {
      pendingNavigation();
    }
    setPendingNavigation(null);
  };

  const handleCancelNavigation = () => {
    setShowNavigationConfirmation(false);
    setPendingNavigation(null);
  };

  return {
    showNavigationConfirmation,
    handleConfirmNavigation,
    handleCancelNavigation,
  };
}
