import React from "react";
import * as Sentry from "@sentry/nextjs";

export class ErrorBoundary extends React.Component<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  React.PropsWithoutRef<any>,
  { hasError: boolean }
> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  componentDidCatch(error: Error) {
    // hack to help debug resizable panel bugs
    if (error.message.match(/^No layout data found for index \d+$/)) {
      try {
        const storageKey = `react-resizable-panels:${this.props.autoSaveId}`;
        const item = localStorage.getItem(storageKey);

        if (Sentry) {
          Sentry.captureException(error, {
            extra: {
              layoutData: item,
              props: this.props,
            },
          });
        }
        localStorage.removeItem(storageKey);
        return;
      } catch {}
    }

    this.setState({ hasError: true });

    if (this.props.fallback) {
      if (Sentry) {
        Sentry.captureException(error, {
          tags: {
            page: "boundary-error",
          },
          extra: {
            props: this.props,
          },
        });
      }

      return;
    }

    throw error;
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}
