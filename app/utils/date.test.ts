import { test, expect } from "vitest";

import { formatFullDateTime } from "./date";

test("formatFullDateTime basic", () => {
  for (const validValue of [
    null,
    0,
    1000,
    "2024-07-16T04:01:11.961Z",
    "Mon Jul 15 2024 21:01:09 GMT-0700 (Pacific Daylight Time)",
  ]) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    expect(formatFullDateTime(new Date(validValue as any))).not.toBe("");
  }

  for (const invalidValue of [
    undefined,
    "bad date",
    "2024-07-16T04:01:11.961ZY",
  ]) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    expect(formatFullDateTime(new Date(invalidValue as any))).toBe("");
  }
});
