import { useCallback, useEffect, useRef, useState } from "react";
import { type TransactionId } from "./duckdb";
import { isEmpty } from "./object";

export interface OptimisticUpdateProps<
  T,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  F extends (...args: any[]) => Promise<TransactionId | null>,
> {
  xactId: TransactionId | null;
  value: T;
  save: F;
  rowKey?: string;
  alwaysUpdate?: boolean;
  onUpdatedValue?: (value: T) => void;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export interface OptimisticState<T, A extends Array<any>> {
  value: T;
  save: (...args: A) => Promise<void>;
}

// useOptimisticState is a utility to help de-jitter updates to a backing store. It
// is useful for two-way communication scenarios, where the app can send and receive
// new versions of the state to/from the backing store, while separately maintaining
// an optimistically-updated instance of the state on the frontend. We don't want the
// client-side state to jitter back to an old version temporarily as the backend sends
// intermediate updates. useOptimisticState solves this by only propagating forth
// backend states which are newer than any of the state updates sent through it. Use
// the returned save function to send updates to the backend. The returned value object
// will only update when all pending saves have completed and the latest backend state
// is newer than any completed saves.
export function useOptimisticState<
  T,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  F extends (...args: any[]) => Promise<TransactionId | null>,
>({
  xactId,
  value: initialState,
  save: saveProp,
  rowKey,
  onUpdatedValue,
}: OptimisticUpdateProps<T, F>): OptimisticState<T, Parameters<F>> {
  const updateTicket = useRef<number>(0);
  const lastUpdateXactId = useRef<TransactionId | null>(
    xactId ?? counterToXactId(0),
  );
  const lastRowKey = useRef<string | undefined>(rowKey);
  const lastNewValue = useRef<T>(initialState);
  const lastXactId = useRef<TransactionId | null>(xactId);

  const [value, setValue] = useState<T>(initialState);

  const maybeUpdateWithExternalState = useCallback(
    (newValue: T, xactId: TransactionId | null) => {
      if (
        // If the row key changes, then reset the value, independent of any transactional updates
        rowKey !== lastRowKey.current ||
        // Or, if we
        //  a) Are not in the middle of a write (which is when lastUpdateXactId.current is null)
        //  b) Know the transaction id of the incoming data (xactId)
        //  c) The incoming transaction id is greater than or equal to the last transaction id we wrote
        // Then we can safely update the value
        (!isEmpty(lastUpdateXactId.current) &&
          !isEmpty(xactId) &&
          xactId >= lastUpdateXactId.current) ||
        // Or if no xactId is provided, then we always update the value in response to external changes
        (isEmpty(lastXactId.current) && isEmpty(xactId))
      ) {
        setValue(newValue);
        onUpdatedValue?.(newValue);
        lastRowKey.current = rowKey;
      }
      lastNewValue.current = newValue;
      lastXactId.current = xactId;
    },
    [rowKey, onUpdatedValue],
  );

  const save = useCallback(
    async (...args: Parameters<F>) => {
      lastUpdateXactId.current = null;
      const ticket = ++updateTicket.current;

      const newXactId = await saveProp(...args);
      if (ticket === updateTicket.current) {
        lastUpdateXactId.current = newXactId;

        // NOTE: We should probably change saveProp() to actually return the value
        // it saved. However, in the meantime, we use the lastNewValue ref to capture
        // the "latest" new value that we've seen, and lastXactId to capture the latest
        // transaction. We may have seen that transaction _while_ the current save operation
        // is running, and newXactId was null. Now that it's not null, we can simply re-run the
        // effect with those values, and it will update the state if it's at least as new as
        // the current operation.
        maybeUpdateWithExternalState(lastNewValue.current, lastXactId.current);
      }
    },
    [maybeUpdateWithExternalState, saveProp],
  );

  useEffect(() => {
    maybeUpdateWithExternalState(initialState, xactId);
  }, [maybeUpdateWithExternalState, initialState, xactId]);

  return {
    save,
    value,
  };
}

function counterToXactId(counter: number): TransactionId {
  return counter.toString().padStart(19, "0");
}

export function xactIdToCounter(xactId: string): number {
  return Number(BigInt(xactId));
}
