export interface AutoCreateEntityOptions<T> {
  /**
   * Function to create the entity with the given name
   */
  createEntity: (
    name: string,
  ) => Promise<{ data: T | null; error: string | null }>;

  /**
   * List of existing entity names to check for conflicts
   */
  existingNames?: string[];

  /**
   * Maximum number of attempts to find a unique name (default: 10)
   */
  maxAttempts?: number;

  /**
   * Custom suffix pattern for name conflicts (default: " copy")
   */
  copySuffix?: string;

  /**
   * Function to extract the entity's final name from the creation response
   */
  extractName: (data: T) => string;

  /**
   * Function to extract the entity's ID from the creation response
   */
  extractId: (data: T) => string;
}

export interface AutoCreateEntityResult<T> {
  name: string;
  id: string;
  data: T;
}

/**
 * Auto-creates entities with automatic name collision handling.
 * If the provided name is taken, it will automatically append " copy", " copy 2", etc.
 * until it finds a unique name or reaches the maximum number of attempts.
 */
export async function autoCreateEntity<T>(
  baseName: string,
  {
    createEntity,
    existingNames = [],
    maxAttempts = 10,
    copySuffix = " copy",
    extractName,
    extractId,
  }: AutoCreateEntityOptions<T>,
): Promise<AutoCreateEntityResult<T>> {
  const existingNamesSet = new Set(existingNames);
  let currentName = baseName;
  let attempts = 0;

  // If the name exists, find a unique variation
  while (attempts < maxAttempts) {
    currentName =
      attempts === 0
        ? baseName
        : attempts === 1
          ? `${baseName}${copySuffix}`
          : `${baseName}${copySuffix} ${attempts}`;

    if (!existingNamesSet.has(currentName)) {
      const { data, error } = await createEntity(currentName);

      if (error) {
        throw new Error(error);
      }

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      if (data && !(data as any).found_existing) {
        return {
          name: extractName(data),
          id: extractId(data),
          data,
        };
      }
    }

    attempts++;
  }

  throw new Error(`Failed to create entity after ${maxAttempts} attempts`);
}
