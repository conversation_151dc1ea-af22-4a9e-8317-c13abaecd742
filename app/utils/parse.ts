import * as JS<PERSON> from "@braintrust/local/yaml";
import {
  deserializePlainStringAsJSON,
  isEmpty,
  normalizeArrowForJSON,
} from "#/utils/object";
import { isXml } from "#/ui/tree";
import { parseLLMSpanPart } from "#/ui/LLMView";
import { z } from "zod";
import { getDiffRight } from "./diffs/diff-objects";

// This is sorted in precedence order
export const renderOptionNames = [
  "llm",
  "llm-raw",
  "html",
  "markdown",
  "text",
  "schema-builder",
  "yaml",
  "json",
  "tree",
] as const;
export type RenderOption = (typeof renderOptionNames)[number];

export const renderOptionNamesSchema = z.enum(renderOptionNames);

/**
 * @param renderOption
 * @returns true if the renderOption supports any data type, otherwise (ie option only supports strings) false
 */
export const isAllDataRenderOption = (renderOption: RenderSpec) =>
  renderOption.filter({ pass: true });

export interface RenderSpec {
  name: string;
  filter: (v: unknown) => boolean;
  guess: (v: unknown) => boolean;
  serialize?: (v: unknown) => string | null;
  deserialize?: (v: string) => unknown;
  readOnly: boolean;
}

export const jsonToYaml = (json: unknown) => {
  return JSY.dump(json, {
    lineWidth: -1,
    noRefs: true,
    noCompatMode: true,
    sortKeys: true,
  }).replace(/\n$/, ""); // Remove the last newline character
};

export const truncateJson = (json: unknown, maxChars: number) => {
  let remainingChars = maxChars;

  function inner(json: unknown): unknown {
    if (remainingChars <= 0) {
      return null;
    }

    if (Array.isArray(json)) {
      const result = [];
      for (const item of json) {
        const processed = inner(item);
        if (processed !== null) {
          result.push(processed);
        }
        if (remainingChars <= 0) break;
      }
      return result;
    } else if (typeof json === "object" && json !== null) {
      const result: Record<string, unknown> = {};
      for (const key of Object.keys(json)) {
        // @ts-ignore
        const processed = inner(json[key]);
        if (processed !== null) {
          result[key] = processed;
        }
        if (remainingChars <= 0) break;
      }
      return result;
    } else if (typeof json === "string") {
      if (json.length <= remainingChars) {
        remainingChars -= json.length;
        return json;
      } else {
        const truncated = json.substring(0, remainingChars) + "...";
        remainingChars = 0;
        return truncated;
      }
    } else if (!!json) {
      // For numbers, booleans, etc.
      const stringified = String(json);
      if (stringified.length <= remainingChars) {
        remainingChars -= stringified.length;
        return json;
      } else {
        remainingChars = 0;
        return null;
      }
    } else {
      return json;
    }
  }

  return inner(json);
};

export const renderOptions: {
  [name in RenderOption]: RenderSpec;
} = {
  yaml: {
    name: "YAML",
    filter: (v: unknown) => true,
    guess: (v: unknown) => true,
    serialize: (v: unknown) => jsonToYaml(v),
    deserialize: (v: string) => {
      try {
        return JSY.load(v, {
          onWarning: (warn) => {
            console.warn(warn);
          },
        });
      } catch (e) {
        throw new Error(`Invalid YAML: ${e}`);
      }
    },
    readOnly: false,
  },
  json: {
    name: "JSON",
    filter: (v: unknown) => true,
    guess: (v: unknown) => true,
    serialize: (v: unknown) => JSON.stringify(v, normalizeArrowForJSON, 2),
    deserialize(v: string) {
      const jsonDeserialized = deserializePlainStringAsJSON(v);
      if (jsonDeserialized.error) {
        throw new Error(`Invalid JSON: ${jsonDeserialized.error}`);
      }
      return jsonDeserialized.value;
    },
    readOnly: false,
  },
  "schema-builder": {
    name: "Schema builder",
    filter: (v: unknown) => true,
    guess: (v: unknown) => true,
    serialize: (v: unknown) => JSON.stringify(v, normalizeArrowForJSON, 2),
    deserialize(v: string) {
      const jsonDeserialized = deserializePlainStringAsJSON(v);
      if (jsonDeserialized.error) {
        throw new Error(`Invalid JSON: ${jsonDeserialized.error}`);
      }
      return jsonDeserialized.value;
    },
    readOnly: false,
  },
  tree: {
    name: "Tree",
    filter: (v: unknown) => true,
    guess: (v: unknown) => true,
    readOnly: true,
  },
  text: {
    name: "Text",
    filter: (v: unknown) => isEmpty(v) || typeof v === "string",
    guess: (v: unknown) => typeof v === "string" && !isXml(v),
    serialize: (v: unknown) => {
      const parsed = z.string().nullable().safeParse(v);
      return parsed.success ? (parsed.data ?? "null") : "null";
    },
    deserialize: (v: string) => {
      return v === "null" ? null : v;
    },
    readOnly: false,
  },
  markdown: {
    name: "Markdown",
    filter: (v: unknown) => isEmpty(v) || typeof v === "string",
    // Use the same filter as text, so that it's always preferred
    guess: (v: unknown) => typeof v === "string" && !isXml(v),
    readOnly: true,
  },
  html: {
    name: "HTML",
    filter: (v: unknown) => isEmpty(v) || typeof v === "string",
    guess: (v: unknown) =>
      typeof v === "string" &&
      /<!DOCTYPE\s+HTML|<(?:html|body|head|title|div|span|br|p|iframe)\b/i.test(
        v,
      ),
    readOnly: true,
  },
  "llm-raw": {
    name: "LLM Raw",
    filter: (v: unknown) => !!parseLLMSpanPart(v),
    guess: (v: unknown) => !!parseLLMSpanPart(v),
    readOnly: true,
  },
  llm: {
    name: "LLM",
    filter: (v: unknown) => !!parseLLMSpanPart(v),
    guess: (v: unknown) => !!parseLLMSpanPart(v),
    readOnly: true,
  },
};

export const deserializeValue = (
  valueString: string,
  renderOption: RenderOption,
) => {
  const deserialize = renderOptions[renderOption].deserialize;
  return deserialize ? deserialize(valueString) : null;
};

const tagsSchema = z.array(z.string());

export const parseTags = (value: unknown) => {
  const valueRaw = getDiffRight(value);

  let result: string[] | null = null;

  // We can't really trust the data coming through here, so try to parse it as the right type
  // and fall back to null if it doesn't work.
  const parsed = tagsSchema.safeParse(
    typeof valueRaw === "string"
      ? JSON.parse(valueRaw)
      : valueRaw && typeof valueRaw === "object" && valueRaw.toArray
        ? valueRaw.toArray()
        : valueRaw,
  );

  if (parsed.success && parsed.data.length > 0) {
    result = parsed.data;
  }

  return result;
};
