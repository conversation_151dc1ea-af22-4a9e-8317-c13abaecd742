import { context, trace, type Span } from "@opentelemetry/api";

export const tracer = trace.getTracer("www.braintrust.dev");

export async function otelTraced<T>(
  name: string,
  fn: (span: Span) => Promise<T>,
): Promise<T> {
  return tracer.startActiveSpan(name, async (span) => {
    try {
      return await fn(span);
    } finally {
      span.end();
    }
  });
}

export function otelWrapTraced<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  F extends (...args: any[]) => Promise<any>,
>(name: string, fn: F): F {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return ((...args: Parameters<F>) => otelTraced(name, () => fn(...args))) as F;
}

// Get the current active span
export function getCurrentSpan(): Span | undefined {
  return trace.getSpan(context.active());
}
