import { type Virtualizer } from "@tanstack/react-virtual";
import { useEffect } from "react";

// https://tanstack.com/virtual/latest/docs/framework/react/examples/infinite-scroll
export function useInfiniteFetch({
  virtualizer,
  hasNextPage,
  fetchNextPage,
  fetchPreviousPage,
  hasPreviousPage,
  totalRowCount,
  isFetching,
}: {
  virtualizer?: Virtualizer<HTMLDivElement, Element>;
  hasNextPage: boolean;
  fetchNextPage: () => void;
  fetchPreviousPage?: () => void;
  hasPreviousPage?: boolean;
  totalRowCount: number;
  isFetching: boolean;
}) {
  const lastItemIndex = virtualizer?.getVirtualIndexes().at(-1);
  const firstItem = fetchPreviousPage
    ? virtualizer?.getVirtualItems().at(0)
    : undefined;
  const hasReachedEnd =
    lastItemIndex != null && lastItemIndex >= totalRowCount - 1;
  useEffect(() => {
    if (hasReachedEnd && hasNextPage && !isFetching && fetchNextPage) {
      fetchNextPage();
    }

    if (
      firstItem?.index === 0 &&
      hasPreviousPage &&
      !isFetching &&
      fetchPreviousPage
    ) {
      fetchPreviousPage();
    }
  }, [
    hasNextPage,
    fetchNextPage,
    totalRowCount,
    isFetching,
    hasReachedEnd,
    hasPreviousPage,
    fetchPreviousPage,
    firstItem?.index,
    // since the first item index could change as data is loading at the top of the list
    // without any indexes changing, we need to run this effect if the first item changes
    firstItem?.key,
  ]);
}
