import "server-only";

import Analytics from "@segment/analytics-node";
import { waitUntil } from "@vercel/functions";

let serverAnalytics: Analytics | undefined = undefined;

if (process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY) {
  serverAnalytics = new Analytics({
    writeKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,

    // This ensures that that tracked events are sent immediately
    flushAt: 1,
  });
}

function isSegmentEnabled() {
  return serverAnalytics !== undefined;
}

export async function trackSegmentEvent({
  userId,
  event,
  properties,
}: {
  userId: string;
  event: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  properties: Record<string, any>;
}) {
  if (!isSegmentEnabled()) {
    return;
  }

  waitUntil(
    new Promise<void>((resolve) => {
      serverAnalytics?.track(
        {
          userId,
          event,
          properties,
        },
        (err) => {
          if (err) {
            // If the event fails to track, we don't want to block rendering of any components
            // so we just log the error and resolve the promise.
            console.error(`Failed to track event: ${event}`, err);
          }
          resolve();
        },
      );
    }),
  );
}

export { serverAnalytics };
