import { describe, expect, test } from "vitest";
import {
  Diff<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Diff<PERSON><PERSON><PERSON>ield,
  flattenDiffObjects,
} from "./diff-objects";

describe("flattenDiffObjects", () => {
  test("no-ops null", () => {
    expect(flattenDiffObjects(null)).toBe(null);
  });

  test("no-ops empty object", () => {
    expect(flattenDiffObjects({})).toEqual({});
  });

  test("no-ops flat object", () => {
    const obj = {
      _xact_id: "1000192642576358004",
      expected: '"8"',
      id: "308b93d8-d25e-4d12-939f-21ca966bc17d",
      input: `{"question":"What was <PERSON>'s rank?"}`,
      metadata: `{"id":27,"gt_sql":"SELECT MIN(\\"Rank Each wrestlers total number of days as champion are ranked highest to lowest; wrestlers with the same number mean that they are tied for that certain rank.\\") FROM \\"table\\" WHERE \\"Wrestler\\" ILIKE 'Go Shiozaki'","prompt":"\\nPrint a SQL query (over a table named \\"table\\" quoted with double quotes) that answers the following question:\\nWhat was Go Shiozaki's rank?\\n\\nSQL: ","output_sql":"\\nSELECT \\"Rank\\" \\nFROM \\"table\\" \\nWHERE Name=\\"Go Shiozaki\\""}`,
      output:
        '"FAILED: Binder Error: Referenced column \\"Name\\" not found in FROM clause!\\nCandidate bindings: \\"table.Wrestler\\""',
      scores: { answer: 0, query: 0.1919642857142857 },
      tags: "[]",
    };
    expect(flattenDiffObjects(obj)).toEqual(obj);
  });

  test("converts all nested diff objects", () => {
    expect(
      flattenDiffObjects({
        id: {
          [DiffLeftField]: "left_ID",
          [DiffRightField]: "right_ID",
        },
        nestedObject: {
          key1: {
            [DiffLeftField]: "left_val1",
            [DiffRightField]: "right_val1",
          },
          key2: {
            [DiffLeftField]: "left_val2",
            [DiffRightField]: "right_val2",
          },
          subnested: {
            [DiffLeftField]: {
              subnestedKey1: "left_subnested_val1",
              subnestedKey2: "left_subnested_val2",
            },
            [DiffRightField]: {
              subnestedKey1: "right_subnested_val1",
              subnestedKey2: "right_subnested_val2",
            },
          },
        },
      }),
    ).toEqual({
      id: "right_ID",
      nestedObject: {
        key1: "right_val1",
        key2: "right_val2",
        subnested: {
          subnestedKey1: "right_subnested_val1",
          subnestedKey2: "right_subnested_val2",
        },
      },
    });
  });
});
