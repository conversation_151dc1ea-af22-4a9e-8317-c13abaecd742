export const BROWSER_EXTENSION_ERROR_MESSAGE =
  "Something went wrong. Using Google Translate or another browser extension? Please disable the extension and try again.";

export const GENERAL_ERROR_MESSAGE =
  "Something went wrong. Our team has been notified and we're looking into the issue.";

export function isBrowserExtensionError(error: Error) {
  return (
    error.message.includes(
      "Failed to execute 'removeChild' on 'Node': The node to be removed is not a child of this node.",
    ) ||
    error.message.includes(
      "Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.",
    )
  );
}
