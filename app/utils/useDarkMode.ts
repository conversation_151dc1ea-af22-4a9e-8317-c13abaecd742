import { useState, useEffect } from "react";

export function useDarkMode() {
  const [isDarkMode, setIsDarkMode] = useState<boolean | null>(null);

  useEffect(() => {
    const html = document.querySelector("html");
    if (html) {
      setIsDarkMode(html.classList.contains("dark"));
    } else {
      return;
    }
    // mutation observer
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "attributes") {
          setIsDarkMode(html.classList.contains("dark"));
        }
      });
    });

    observer.observe(html, {
      attributes: true,
    });

    return () => observer.disconnect();
  }, []);

  return isDarkMode;
}
