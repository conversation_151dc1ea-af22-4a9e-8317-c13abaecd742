import { ConfirmationDialog } from "#/ui/dialogs/confirmation";

export interface ContinueExecutionDialogProps {
  onAllow: (allow: boolean) => void;
}

export function ContinueExecutionDialog({
  onAllow,
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
} & ContinueExecutionDialogProps) {
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      onConfirm={() => onAllow(true)}
      onCancel={() => onAllow(false)}
      title="Continue Execution"
      description="The optimizer has run out of iterations, but you can let it continue if you'd like."
      confirmText="Continue"
    />
  );
}
