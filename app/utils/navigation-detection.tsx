"use client";
import {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
  type ReactNode,
} from "react";
import { usePathname } from "next/navigation";

type NavigationType = "soft" | "hard" | null;

interface NavigationContextType {
  navigationType: NavigationType;
}

const NavigationContext = createContext<NavigationContextType | undefined>(
  undefined,
);

export function useNavigationType() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error(
      "useNavigationType must be used within a NavigationDetectionProvider",
    );
  }
  return context.navigationType;
}

interface NavigationDetectionProviderProps {
  children: ReactNode;
}

// https://github.com/vercel/next.js/issues/58672 doesn't seem to work so track it ourselves
export default function NavigationDetectionProvider({
  children,
}: NavigationDetectionProviderProps) {
  const [navigationType, setNavigationType] = useState<NavigationType>(null);
  const initialRender = useRef(true);
  const pathname = usePathname();

  // https://github.com/vercel/next.js/discussions/49824#discussioncomment-8453802
  useEffect(() => {
    if (!initialRender.current) {
      setNavigationType("soft");
    } else {
      setNavigationType("hard");
      initialRender.current = false;
    }
  }, [pathname]);

  return (
    <NavigationContext.Provider value={{ navigationType }}>
      {children}
    </NavigationContext.Provider>
  );
}
