import { DataType, type Field, type Schema } from "apache-arrow";

export type PathTree = PathLeaf | PathNode;
export const MAX_COLUMNS = 10000;

export interface PathLeaf {
  type: "leaf";
}

export interface PathNode {
  type: "node";
  children: Record<string, PathTree>;
}

export function computeDisplayPaths(
  schema: Schema | null,
  projectedPaths: string[],
  comparisonSchemas?: Schema[],
): PathNode {
  const fieldsByName = [
    ...(schema?.fields ?? []),
    ...(comparisonSchemas?.flatMap((s) => s.fields) ?? []),
  ].reduce((acc: Record<string, Field>, f) => {
    if (
      !acc[f.name] ||
      (!DataType.isStruct(acc[f.name].type) && DataType.isStruct(f.type))
    ) {
      acc[f.name] = f;
    }
    return acc;
  }, {});
  const projectedFields = projectedPaths.flatMap((p) => {
    if (!fieldsByName[p]) {
      return [];
    }
    return [fieldsByName[p]];
  });
  const branchFactor = computeBranchFactorFields(projectedFields);
  return computeLeafPaths(projectedFields, branchFactor, MAX_COLUMNS);
}

interface BranchFactor {
  total: number;
  fields: { [key: string]: BranchFactor };
}

function computeBranchFactorFields(all_fields: Field[]): BranchFactor {
  const fields: { [key: string]: BranchFactor } = {};
  for (const field of all_fields) {
    const bf = computeBranchFactor(field);
    fields[field.name] = bf;
  }
  return {
    total: all_fields.length,
    fields,
  };
}

function computeBranchFactor(field: Field): BranchFactor {
  if (DataType.isStruct(field.type)) {
    return computeBranchFactorFields(field.type.children);
  } else {
    return {
      total: 1,
      fields: {},
    };
  }
}

function computeBudgets(
  fields: Field[],
  branchFactor: BranchFactor,
  budget: number,
): Record<string, number> {
  let remaining_budget = budget;
  const budgets: Record<string, number> = fields
    .map((f) => f.name)
    .reduce((acc, name) => ({ ...acc, [name]: 0 }), {});
  const ignore: Record<string, boolean> = fields
    .map((f) => f.name)
    .reduce((acc, name) => ({ ...acc, [name]: false }), {});

  let safeIter = 1000;
  while (
    remaining_budget > 0 &&
    Object.values(ignore).some((v) => !v) &&
    safeIter > 0
  ) {
    while (remaining_budget > 0 && safeIter > 0) {
      const initial_budget = remaining_budget;
      for (const field of fields) {
        const bf = branchFactor.fields[field.name];
        if (budgets[field.name] < bf.total && !ignore[field.name]) {
          budgets[field.name] += 1;
          remaining_budget -= 1;
        }
      }

      if (remaining_budget === initial_budget) {
        return budgets;
      }

      safeIter -= 1;
    }

    for (const field of fields) {
      const bf = branchFactor.fields[field.name];
      if (budgets[field.name] < bf.total) {
        ignore[field.name] = true;
        remaining_budget += budgets[field.name] - 1;
        budgets[field.name] = 1;
      }
    }
    safeIter -= 1;
  }
  if (safeIter === 0) {
    console.error("Failed to compute leaf paths.");
  }
  return budgets;
}

function computeLeafPaths(
  fields: Field[],
  branchFactor: BranchFactor,
  budget: number,
): PathNode {
  const ret: PathNode = {
    type: "node",
    children: {},
  };

  const budgets = computeBudgets(fields, branchFactor, budget);

  for (const field of fields) {
    if (
      DataType.isStruct(field.type) &&
      budgets[field.name] >= field.type.children.length
    ) {
      ret.children[field.name] = computeLeafPaths(
        field.type.children,
        branchFactor.fields[field.name],
        budgets[field.name],
      );
    } else {
      ret.children[field.name] = {
        type: "leaf",
      };
    }
  }

  return ret;
}
