import { describe, it, expect } from "vitest";
import { parseIdentPath, escapeIdentPath } from "./path-helpers";

describe("path helpers", () => {
  describe("parseIdentPath", () => {
    it("parses simple identifier", () => {
      expect(parseIdentPath("foo")).toEqual(["foo"]);
    });

    it("parses dot notation path", () => {
      expect(parseIdentPath("foo.bar")).toEqual(["foo", "bar"]);
    });

    it("parses array index", () => {
      expect(parseIdentPath("foo[0]")).toEqual(["foo", 0]);
    });

    it("parses complex path with dots and array indices", () => {
      expect(parseIdentPath("foo.bar[0].baz")).toEqual([
        "foo",
        "bar",
        0,
        "baz",
      ]);
    });

    it("handles multiple array indices", () => {
      expect(parseIdentPath("foo[0][1][2]")).toEqual(["foo", 0, 1, 2]);
    });

    it("handles quoted identifiers", () => {
      expect(parseIdentPath("`foo bar`.baz")).toEqual(["foo bar", "baz"]);
    });

    it("returns single element array for invalid path", () => {
      expect(parseIdentPath("foo[")).toEqual(["foo["]);
    });

    it("handles 'Answer error' path", () => {
      expect(parseIdentPath("Answer error")).toEqual(["Answer error"]);
    });

    it("handles 'Answer error' path", () => {
      expect(parseIdentPath('"Answer error"')).toEqual(["Answer error"]);
    });

    it("handles 'Answer error' path", () => {
      expect(parseIdentPath('metadata."Answer error"')).toEqual([
        "metadata",
        "Answer error",
      ]);
    });

    it("handles empty string", () => {
      expect(parseIdentPath("")).toEqual([""]);
    });

    it("handles whitespace", () => {
      expect(parseIdentPath("  foo  .  bar  ")).toEqual(["foo", "bar"]);
    });
  });

  describe("roundtrip", () => {
    it("roundtrips simple paths", () => {
      const paths = [
        "foo",
        "foo.bar",
        "foo[0]",
        "foo.bar[0].baz",
        "foo[0][1][2]",
        '"foo bar".baz',
      ];

      for (const path of paths) {
        expect(escapeIdentPath(parseIdentPath(path))).toEqual(path);
      }
    });
  });
});
