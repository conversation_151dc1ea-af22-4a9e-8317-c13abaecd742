import { test, expect } from "vitest";
import { parseBtqlSchema } from "./btql";
import { type ResponseSchema } from "@braintrust/btql/binder";
import { createArrowTableFromRecords } from "#/utils/arrow";

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
const response = {
  data: [
    {
      experiment_id: "0c3e2331-d33a-4d03-bea1-56040debf6df",
      count: 146,
      last_updated: "2024-04-26 20:01:50",
      avg: 0.6550387596899225,
      scores: {
        AutoSearch: {
          count: 45,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.7666666666666667,
        },
        filter: {
          count: 32,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.8,
        },
        functionChoice: {
          count: 45,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.8888888888888888,
        },
        sort: {
          count: 32,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.9230769230769231,
        },
        Filter: {
          count: 28,
          last_updated: "2024-04-26 20:08:27",
          avg: 0.5555555555555556,
        },
        Sort: {
          count: 28,
          last_updated: "2024-04-26 20:08:27",
          avg: 0.8333333333333334,
        },
        Accuracy: {
          count: 40,
          last_updated: "2024-04-27 17:43:26",
          avg: 0.625,
        },
      },
    },
    {
      experiment_id: "a56ad6fe-72e8-4fee-b982-ed6e50599d5d",
      count: 174,
      last_updated: "2024-04-26 20:08:27",
      avg: 0.6571428571428571,
      scores: {
        AutoSearch: {
          count: 45,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.7666666666666667,
        },
        filter: {
          count: 32,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.8,
        },
        functionChoice: {
          count: 45,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.8888888888888888,
        },
        sort: {
          count: 32,
          last_updated: "2024-04-27 17:43:34",
          avg: 0.9230769230769231,
        },
        Filter: {
          count: 28,
          last_updated: "2024-04-26 20:08:27",
          avg: 0.5555555555555556,
        },
        Sort: {
          count: 28,
          last_updated: "2024-04-26 20:08:27",
          avg: 0.8333333333333334,
        },
        Accuracy: {
          count: 40,
          last_updated: "2024-04-27 17:43:26",
          avg: 0.625,
        },
      },
    },
  ],
  schema: {
    type: "array",
    items: {
      type: "object",
      properties: {
        experiment_id: {
          type: "string",
          format: "uuid",
          description: "Unique identifier for the experiment",
        },
        count: {
          type: "integer",
        },
        last_updated: {
          type: "string",
          format: "date-time",
          description: "The timestamp the experiment event was created",
        },
        avg: {
          type: ["number", "null"],
        },
        scores: {
          type: "object",
          additionalProperties: {
            type: "object",
            properties: {
              count: {
                type: "integer",
              },
              last_updated: {
                type: "string",
                format: "date-time",
                description: "The timestamp the experiment event was created",
              },
              avg: {
                type: ["number", "null"],
              },
            },
          },
          propertyNames: {
            enum: [
              "AutoSearch",
              "filter",
              "functionChoice",
              "sort",
              "Filter",
              "Sort",
              "Accuracy",
            ],
          },
        },
      },
    },
  },
} as {
  data: unknown[];
  schema: ResponseSchema;
};

test("btql/arrow-parsing", () => {
  const arrowSchema = parseBtqlSchema(null, response.schema);
  console.log(
    arrowSchema.fields.find((f) => f.name === "scores")?.type.children,
  );
  const table = createArrowTableFromRecords(response.data, arrowSchema);
  expect(table?.numRows).toBe(2);
});
