import { fetchInferBtql } from "#/utils/btql/btql";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import * as Query from "#/utils/btql/query-builder";
import { useQuery } from "@tanstack/react-query";

export function useInferQuery({
  objectType,
  objectId,
  fieldName,
  supportedFields,
}: {
  objectType?: string | null;
  objectId?: string | null;
  fieldName: string;
  supportedFields?: Set<string>;
}) {
  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const schemaInference = useIsFeatureEnabled("schemaInference");
  const { getOrRefreshToken } = useSessionToken();

  const hasSchemaInference = schemaInference;

  const enabled =
    !!objectType &&
    !!objectId &&
    hasSchemaInference &&
    (!supportedFields || supportedFields?.has(fieldName));
  const query = useQuery({
    queryKey: ["btqlInferQuery", objectType, objectId, fieldName],
    queryFn: async ({ signal }: { signal: AbortSignal }) =>
      objectId
        ? await fetchInferBtql({
            args: {
              query: {
                from: Query.from(String(objectType), [objectId]),
                infer: [{ op: "ident", name: [fieldName] }],
                limit: 500,
              },
              brainstoreRealtime: true,
            },
            btqlFlags,
            apiUrl: org.api_url,
            getOrRefreshToken,
            signal,
          })
        : null,
    enabled,
    gcTime: 1000 * 30,
  });
  return { query, enabled };
}
