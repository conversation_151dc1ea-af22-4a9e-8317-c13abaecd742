/**
 * Utilities for converting BTQL expressions into user-friendly display labels
 */

import {
  TIME_RANGE_OPTIONS,
  TIME_RANGE_TO_INTERVAL,
} from "#/app/app/[org]/monitor/time-controls/time-range";
import { getTimeRangeCustomString } from "#/ui/time-range-select/get-time-range-custom-string";
import { type Expr as ParsedExpr } from "@braintrust/btql/parser";

const INTERVAL_TO_OPTION_VALUE: Record<string, string> = Object.fromEntries(
  Object.entries(TIME_RANGE_TO_INTERVAL).map(([value, interval]) => [
    interval.toLowerCase(),
    value,
  ]),
);

function getRelativeLabel(interval: string): string | null {
  const optionValue = INTERVAL_TO_OPTION_VALUE[interval.toLowerCase()];
  if (!optionValue) return null;
  const option = TIME_RANGE_OPTIONS.find((o) => o.value === optionValue);
  if (!option) return null;
  return `the past ${option.label.toLowerCase()}`;
}

/**
 * Converts BTQL expressions containing 'created' time filters into user-friendly display labels.
 * e.g. "created >= NOW() - INTERVAL 14 day" into "the past 2 weeks".
 */
export function getCreatedTimeRangeLabelFromBtql(
  btqlExprs: ParsedExpr[] | undefined | null,
): string | null {
  if (!btqlExprs || btqlExprs.length === 0) return "all time";

  for (const expr of btqlExprs) {
    if (!("btql" in expr)) continue;
    const raw = expr.btql;
    if (!raw || !/created/i.test(raw)) continue;

    // Relative: e.g. created >= NOW() - INTERVAL 14 day
    const relativeMatch = raw.match(
      /created\s*>?=\s*now\(\)\s*-\s*interval\s+(\d+\s+(?:hour|day|\d+h|\d+d|\d+\s+hour|\d+\s+day))/i,
    );
    if (relativeMatch) {
      const normalized = relativeMatch[1]
        .replace(/\s+/g, " ")
        .trim()
        .toLowerCase();
      const label = getRelativeLabel(normalized);
      if (label) return label;
    }

    // Absolute: e.g. created >= 'ISO' AND created <= 'ISO'
    const absoluteMatch = raw.match(
      /created\s*>?=\s*'([^']+)'\s+and\s+created\s*<=\s*'([^']+)'/i,
    );
    if (absoluteMatch) {
      const from = absoluteMatch[1];
      const to = absoluteMatch[2];
      return getTimeRangeCustomString({ value: { from, to } });
    }
  }
  return null;
}
