import { type Expr } from "@braintrust/btql/parser";
import { type TimeRangeFilter } from "#/utils/view/use-view";
import { TIME_RANGE_TO_INTERVAL } from "#/app/app/[org]/monitor/time-controls/time-range";
import { parseDateString } from "#/ui/time-range-select/parse-date-string";
import { endOfDay } from "date-fns";

/**
 * Converts a TimeRangeFilter to BTQL filter expressions
 * @param timeRangeFilter - The time range filter from the UI
 * @param objectType - The object type being queried (e.g., "project_logs")
 * @returns Array of BTQL expressions to filter by time range
 */
export function timeRangeFilterToBtqlExprs(
  timeRangeFilter: TimeRangeFilter | undefined,
  objectType: string,
): Expr[] {
  if (
    objectType !== "project_logs" ||
    !timeRangeFilter ||
    timeRangeFilter === "all"
  ) {
    return [];
  }

  if (
    typeof timeRangeFilter === "string" &&
    TIME_RANGE_TO_INTERVAL[timeRangeFilter]
  ) {
    return [
      {
        btql: `created >= NOW() - INTERVAL ${TIME_RANGE_TO_INTERVAL[timeRangeFilter]}`,
      },
    ];
  }

  if (typeof timeRangeFilter === "object") {
    return [
      {
        btql: `created >= '${parseDateString(timeRangeFilter.from).toISOString()}' AND created <= '${endOfDay(parseDateString(timeRangeFilter.to)).toISOString()}'`,
      },
    ];
  }

  return [];
}
