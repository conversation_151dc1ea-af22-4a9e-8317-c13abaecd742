import { Parser } from "@braintrust/btql/parser";
import { doubleQuote } from "@braintrust/btql/planner";

export function parseIdentPath(path: string): (string | number)[] {
  const parser = new Parser(path);
  try {
    const expr = parser.parseIdent({ allowInitialDoubleQuoted: true });
    if (expr.op === "ident" && parser.finished()) {
      return expr.name;
    }
  } catch {}
  return [path];
}

export function parseStringOnlyIdentPath(path: string): string[] {
  const components = parseIdentPath(path);
  if (components.every((c) => typeof c === "string")) {
    return components;
  } else {
    return [path];
  }
}

export function needsEscaping(component: string): boolean {
  if (component.includes(".")) {
    return true;
  }
  const parser = new Parser(component);
  try {
    const expr = parser.parseIdent({ allowInitialDoubleQuoted: true });
    return !(expr.op === "ident" && parser.finished());
  } catch {
    return true;
  }
}

export function escapeIdentPath(path: (string | number)[]): string {
  let ret = "";
  for (const [i, p] of path.entries()) {
    if (typeof p === "string") {
      if (i > 0) {
        ret += ".";
      }
      ret += needsEscaping(p) ? doubleQuote(p) : p;
    } else {
      ret += `[${p}]`;
    }
  }
  return ret;
}
