# Server-Side Analytics Framework

This framework provides a type-safe, extensible way to log server-side events in Braintrust. It's designed to be used for tracking important user actions and system events that happen on the server side.

## Overview

The framework consists of several components:

1. **Server Analytics Core** (`server-analytics.ts`) - The main utility class for logging events
2. **Event Schemas** (`analytics/events.ts`) - Type-safe event definitions
3. **Domain-Specific Utilities** (`playground-analytics.ts`) - Convenience functions for specific features
4. **Segment Integration** (`segment/segment.ts`) - Direct server-side SDK integration

## Architecture

This framework uses a **direct server-side approach**:
- **No HTTP endpoints** for server-side events (security best practice)
- **Direct Segment SDK calls** from server-side code
- **Type-safe schemas** with Zod validation
- **Automatic auth context** retrieval from server session
- **Non-blocking execution** using `waitUntil` for background processing

## Enhanced Analytics Capabilities

The playground run analytics now include comprehensive information about scorers and datasets:

### Scorer Analytics

The analytics now track detailed scorer information to help understand evaluation patterns:

- **`numScorers`** - Total number of scorers applied to the playground run
- **`scorers`** - Array of detailed scorer information:
  - `id` - Unique identifier for the scorer
  - `name` - Human-readable scorer name (e.g., "ExactMatch", "CustomAccuracy")
  - `type` - Scorer type: "global" (built-in autoevals) or "function" (custom scorers)
  - `isCustom` - Boolean indicating if it's a custom scorer

This helps answer questions like:
- How many users are using built-in vs custom scorers?
- Which scorers are most popular?
- What's the average number of scorers per playground run?

### Dataset Analytics

Enhanced dataset tracking provides insights into data usage patterns:

- **`numDatasetRows`** - Number of rows in the dataset (already existed, now more prominent)
- **`hasDataset`** - Whether a dataset was used (boolean)
- **`datasetId`** - Unique identifier for the dataset

This helps answer questions like:
- What's the typical dataset size for playground runs?
- How often do users run playgrounds with vs without datasets?
- Which datasets are most commonly used?

### Example Enhanced Event

```typescript
{
  event: "playgroundRun",
  properties: {
    projectId: "proj_123",
    playgroundId: "pg_456",
    outcome: "success",
    model: "gpt-4o",
    provider: "openai",
    temperature: 0.7,
    
    // Enhanced scorer information
    numScorers: 2,
    scorers: [
      {
        id: "scorer_1",
        name: "ExactMatch",
        type: "global",
        isCustom: false
      },
      {
        id: "scorer_2",
        name: "CustomAccuracy",
        type: "function", 
        isCustom: true
      }
    ],
    
    // Enhanced dataset information
    numDatasetRows: 50,
    hasDataset: true,
    datasetId: "dataset_123",
    
    // Existing comparison task information
    comparisonTasksCount: 2,
    comparisonTasks: [...]
  }
}
```

## Quick Start

### 1. Define an Event Schema

Add your event schema to `app/analytics/events.ts`:

```typescript
const myEvent = z.object({
  // Your event properties
  action: z.string(),
  result: z.enum(["success", "error"]),
  // Common server-side fields are automatically added
  orgId: z.string().optional(),
  source: z.literal("server"),
  timestamp: z.string().optional(),
});

export const eventSchemas = {
  // ... existing events
  my_event: myEvent,
} as const;
```

### 2. Add Server-Side Schema

Add the corresponding server-side schema to `app/utils/server-analytics.ts`:

```typescript
export const myEventSchema = baseServerEventSchema.extend({
  event: z.literal("my_event"),
  properties: z.object({
    action: z.string(),
    result: z.enum(["success", "error"]),
  }),
});

export const serverEventSchemas = {
  // ... existing schemas
  my_event: myEventSchema,
} as const;
```

### 3. Create Domain-Specific Utility

Create a utility in `app/utils/my-feature-analytics.ts`:

```typescript
"use server";

import { trackServerEvent } from "#/utils/server-analytics";

export async function trackMyEvent(
  event: {
    action: string;
    result: "success" | "error";
    // Add other properties as needed
  },
  authLookupRaw?: AuthLookup,
): Promise<{ success: true }> {
  await trackServerEvent("my_event", event, {
    authLookup: authLookupRaw,
  });
  return { success: true };
}
```

### 4. Use in Your Code

#### Server-Side Usage (Recommended)

```typescript
import { trackMyEvent } from "#/utils/my-feature-analytics";

// In a server action or API route
await trackMyEvent({
  action: "user_action",
  result: "success",
  // Add other properties as needed
});
```

## Playground Run Example

The framework includes a complete example for tracking playground runs:

### Event Properties

- `projectId` - The project ID
- `playgroundId` - The playground (prompt session) ID
- `outcome` - "success", "error", or "cancelled"
- `errorMessage` - Error details (for error outcomes)
- `numPrompts` - Number of prompts in the playground
- `numDatasetRows` - Number of dataset rows
- `maxConcurrency` - Maximum concurrency setting
- `strict` - Whether strict mode was enabled
- `hasDataset` - Whether a dataset was used
- `durationMs` - Execution time in milliseconds
- `datasetId` - The dataset ID (if a dataset was used)
- `numScorers` - Number of scorers applied
- `scorers` - Array of scorer information including:
  - `id` - Scorer ID
  - `name` - Scorer name
  - `type` - "global" (built-in) or "function" (custom)
  - `isCustom` - Whether it's a custom scorer
- `model` - The primary model used
- `provider` - The AI provider (e.g., "openai", "anthropic")
- `temperature` - Model temperature setting
- `comparisonTasksCount` - Number of comparison tasks
- `comparisonTasks` - Array of comparison task configurations

### Usage Examples

#### Track a Successful Run

```typescript
import { trackPlaygroundRunSuccess } from "#/utils/playground-analytics";

await trackPlaygroundRunSuccess({
  projectId: "proj_123",
  playgroundId: "play_456",
  numPrompts: 3,
  numDatasetRows: 100,
  maxConcurrency: 5,
  strict: false,
  hasDataset: true,
  durationMs: 2500,
  numScorers: 2,
  scorers: [
    {
      id: "scorer_1",
      name: "ExactMatch",
      type: "global",
      isCustom: false
    }
  ],
  model: "gpt-4o",
  provider: "openai",
  temperature: 0.7,
});
```

#### Track an Error

```typescript
import { trackPlaygroundRunError } from "#/utils/playground-analytics";

await trackPlaygroundRunError({
  projectId: "proj_123",
  playgroundId: "play_456",
  errorMessage: "API rate limit exceeded",
  numPrompts: 3,
  hasDataset: true,
  durationMs: 500,
  model: "gpt-4o",
  provider: "openai",
});
```

#### Track Cancellation

```typescript
import { trackPlaygroundRunCancelled } from "#/utils/playground-analytics";

await trackPlaygroundRunCancelled({
  projectId: "proj_123",
  playgroundId: "play_456",
  numPrompts: 3,
  hasDataset: true,
  durationMs: 1200,
  model: "gpt-4o",
  provider: "openai",
});
```

## Integration with Existing Code

### Direct Integration

```typescript
// In your existing function
const startTime = Date.now();

try {
  // Your existing logic
  await runPrompts();

  // Track success
  await trackPlaygroundRunSuccess({
    projectId,
    playgroundId,
    durationMs: Date.now() - startTime,
    numScorers: savedScorers.length,
    scorers: savedScorers.map(scorer => ({
      id: scorer.id,
      name: scorer.name,
      type: scorer.type,
      isCustom: scorer.type === "function"
    })),
    numDatasetRows: datasetRows?.length || 0,
    hasDataset: !!datasetId,
    datasetId,
    model,
    provider,
    temperature,
    // ... other metadata
  });
} catch (error) {
  // Track error
  await trackPlaygroundRunError({
    projectId,
    playgroundId,
    durationMs: Date.now() - startTime,
    errorMessage: error.message,
    numScorers: savedScorers.length,
    numDatasetRows: datasetRows?.length || 0,
    hasDataset: !!datasetId,
    datasetId,
    model,
    provider,
    // ... other metadata
  });
  throw error;
}
```

## Best Practices

1. **Don't Block Main Functionality** - Analytics errors should never break the main feature
2. **Use Type Safety** - Always use the provided schemas and types
3. **Include Relevant Metadata** - Add context that will be useful for analysis
4. **Handle Errors Gracefully** - Log errors but don't let them propagate
5. **Use Consistent Naming** - Follow the existing naming conventions
6. **Test Your Events** - Verify that events are being tracked correctly
7. **Use Server-Side Only** - Don't expose analytics endpoints to clients
8. **Include Scorer Information** - Always track scorer usage for better insights
9. **Track Dataset Usage** - Include dataset information when available

## Error Handling

The framework includes comprehensive error handling:

- Invalid event data is logged but doesn't break functionality
- Network errors are caught and logged
- Missing auth information is handled gracefully
- All errors are logged with context for debugging
- Events are sent using `waitUntil` for non-blocking execution

## Monitoring

Events are sent to Segment and can be monitored in:

- Segment dashboard
- Data warehouse (Snowflake, etc.)
- Analytics platforms (Mixpanel, Amplitude, etc.)

## Extending the Framework

To add new event types:

1. Add the schema to `analytics/events.ts`
2. Add the server schema to `server-analytics.ts`
3. Create domain-specific utilities
4. Update this documentation

## Troubleshooting

### Events Not Appearing

1. Check server logs for errors
2. Verify Segment configuration
3. Ensure auth information is available
4. Check that events are being called from server-side code

### Type Errors

1. Verify schema definitions match
2. Check that all required fields are provided
3. Ensure event names match between schemas

### Performance Issues

1. Analytics calls are non-blocking using `waitUntil`
2. Events are sent directly to Segment
3. Consider using sampling for high-volume events

## Security

- **No client-exposed endpoints** for server-side analytics
- **Direct server-side SDK** integration only
- **Automatic auth context** retrieval
- **Type-safe validation** prevents injection attacks
