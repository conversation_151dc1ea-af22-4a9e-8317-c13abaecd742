import {
  isEmpty,
  TRANSACTION_ID_FIELD,
  type TransactionId,
} from "braintrust/util";
import { type Draft, produce } from "immer";
import { type Atom, atom, useSetAtom } from "jotai";
import { useAtomCallback } from "jotai/utils";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";

const UPDATING = Symbol("updating");

interface SyncedStateValue {
  [TRANSACTION_ID_FIELD]: TransactionId;
  id: string;
}
interface BaseSyncedStateProps<T extends SyncedStateValue> {
  children: React.ReactNode;
  /** Callback to persist synced state to the server.When omitted, disables state syncing behavior. This means that state will be initialized with `externalValue`, then ignore subsequent updates to external value. All state updates will be local only. */
  save?: ({ id, val }: { id: string; val: T }) => Promise<TransactionId | null>;
}

interface SingleObjectWithRowKeySyncedStateProps<T extends SyncedStateValue>
  extends BaseSyncedStateProps<T> {
  externalValue: T;
  rowKey?: string;
  getRowKey?: never;
}

interface SingleObjectWithGetRowKeySyncedStateProps<T extends SyncedStateValue>
  extends BaseSyncedStateProps<T> {
  externalValue: T;
  rowKey?: never;
  getRowKey?: (item: T) => string;
}

export interface ArraySyncedStateProps<T extends SyncedStateValue>
  extends BaseSyncedStateProps<T> {
  externalValue: T[];
  getRowKey?: (item: T) => string;
}

export interface SyncedState<T> {
  /** Read-only atom config that specifies the state */
  atom: Atom<T>;
  /** Callback to update the atom by mutating an immer draft */
  setValue: (updater: (draft: Draft<T>) => void) => void;
  /** Callback to persist the atom value to the server. */
  save: () => Promise<TransactionId | null>;
}

export interface ArraySyncedState<T> {
  /** Read-only atom config that specifies the state */
  atom: Atom<T[]>;
  /** Callback to update the atom by mutating an immer draft */
  setValue: (updater: (draft: Draft<T[]>) => void) => void;
  /** Callback to update the record in the atom with the given id by mutating an immer draft */
  setValueAtId: (id: string, updater: (draft: Draft<T>) => void) => void;
  /** Callback to persist the value of the record with the given id stored in the atom to the corresponding server record. */
  save: (id: string) => Promise<TransactionId | null>;
}

export function createSyncedObjects<T extends SyncedStateValue>() {
  const SyncedObjectsContext = createContext<ArraySyncedState<T> | null>(null);

  const FALLBACK_CONTEXT: ArraySyncedState<T> = {
    atom: atom<T[]>([]),
    setValue: () => {},
    setValueAtId: () => {},
    save: () => Promise.resolve(null),
  };

  function SyncedObjectsProvider({
    externalValue,
    save: saveProp,
    getRowKey,
    children,
  }: ArraySyncedStateProps<T>) {
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps -- create an atom with the initial state, then handle updates using the effect
    const syncedStateAtom = useMemo(() => atom<T[]>(externalValue), []);
    const setValueRaw = useSetAtom(syncedStateAtom);

    const getItemRowKey = useCallback(
      (item: T): string => {
        if (getRowKey) {
          return getRowKey(item);
        } else {
          return item.id;
        }
      },
      [getRowKey],
    );

    const updateQueues = useRef<Record<string, (() => Promise<void>)[]>>({});
    const isProcessingQueue = useRef<Record<string, boolean>>({});
    const lastUpdateXactId = useRef<
      Record<string, TransactionId | typeof UPDATING>
    >(
      Object.fromEntries(
        externalValue.map((val) => [val.id, val[TRANSACTION_ID_FIELD]]),
      ),
    );
    const itemRowKeys = useRef<Record<string, string>>(
      Object.fromEntries(
        externalValue.map((val) => [val.id, getItemRowKey(val)]),
      ),
    );

    const maybeUpdateWithExternalState = useAtomCallback(
      useCallback(
        (get, set, incomingValue: T[]) => {
          const currentValue = get(syncedStateAtom);
          const newSyncedState = [...currentValue];
          let hasChanges = false;
          const incomingIds = new Set<string>();

          incomingValue.forEach((item) => {
            const itemId = item.id;
            incomingIds.add(itemId);
            const itemXactId = item[TRANSACTION_ID_FIELD];
            const currentRowKey = getItemRowKey(item);
            const previousRowKey = itemRowKeys.current[itemId];
            const lastXactId = lastUpdateXactId.current[itemId];

            const shouldUpdate =
              // Row key changed for this item
              previousRowKey !== currentRowKey ||
              // No last transaction ID for this item (it's new to us)
              isEmpty(lastXactId) ||
              // Or we're not in the middle of a write for this item
              (lastXactId !== UPDATING &&
                // And the incoming transaction ID is valid
                !isEmpty(itemXactId) &&
                // And the incoming transaction ID is newer than what we have
                itemXactId > lastXactId);

            if (shouldUpdate) {
              const existingItemIndex = newSyncedState.findIndex(
                (i) => i.id === itemId,
              );

              if (existingItemIndex >= 0) {
                newSyncedState[existingItemIndex] = item;
              } else {
                newSyncedState.push(item);
              }

              itemRowKeys.current[itemId] = currentRowKey;
              lastUpdateXactId.current[itemId] = itemXactId;
              hasChanges = true;
            }
          });

          const deletedIndices = [];
          for (let i = 0; i < newSyncedState.length; i++) {
            if (!incomingIds.has(newSyncedState[i].id)) {
              deletedIndices.unshift(i); // Add to front so we delete from end to start
            }
          }

          deletedIndices.forEach((index) => {
            const deletedId = newSyncedState[index].id;
            newSyncedState.splice(index, 1);
            delete itemRowKeys.current[deletedId];
            delete lastUpdateXactId.current[deletedId];
            hasChanges = true;
          });

          if (hasChanges) {
            set(syncedStateAtom, newSyncedState);
          }
        },
        [syncedStateAtom, getItemRowKey],
      ),
    );

    const hasSave = !!saveProp;
    useEffect(() => {
      if (hasSave) {
        maybeUpdateWithExternalState(externalValue);
      }
    }, [maybeUpdateWithExternalState, externalValue, hasSave]);

    const setValue = useCallback(
      (updater: (draft: Draft<T[]>) => void) => {
        setValueRaw((value) => produce(value, updater));
      },
      [setValueRaw],
    );

    const setValueAtId = useCallback(
      (id: string, updater: (draft: Draft<T>) => void) => {
        setValue((draft) => {
          const item = draft.find((v) => v.id === id);
          if (item) {
            updater(item);
          }
        });
      },
      [setValue],
    );

    const processQueue = useCallback(async (id: string) => {
      isProcessingQueue.current[id] = true;

      while (updateQueues.current[id] && updateQueues.current[id].length > 0) {
        const nextTask = updateQueues.current[id][0];
        try {
          await nextTask();
        } catch (error) {
          console.error(
            `Error processing SyncedObjects queue for ID ${id}:`,
            error,
          );
        }
        updateQueues.current[id].shift();
      }

      isProcessingQueue.current[id] = false;
    }, []);

    const save: (id: string) => Promise<TransactionId | null> = useAtomCallback(
      useCallback(
        async (get, _set, id: string) => {
          if (!saveProp) {
            return null;
          }

          if (!updateQueues.current[id]) {
            updateQueues.current[id] = [];
          }

          // Create a promise that will be resolved when this update is processed
          let resolvePromise: (value: TransactionId | null) => void;
          const resultPromise = new Promise<TransactionId | null>((resolve) => {
            resolvePromise = resolve;
          });

          updateQueues.current[id].push(async () => {
            lastUpdateXactId.current[id] = UPDATING;
            const val = get(syncedStateAtom);
            const selectedVal = val.find((v) => v.id === id);

            if (selectedVal == null) {
              console.warn(`Selected value not found: ${id}`);
              resolvePromise(null);
              return;
            }

            const rowKey = getItemRowKey(selectedVal);
            itemRowKeys.current[id] = rowKey;

            try {
              const newXactId = await saveProp({
                id,
                val: selectedVal,
              });

              if (newXactId) {
                lastUpdateXactId.current[id] = newXactId;
                setValueAtId(id, (draft) => {
                  draft[TRANSACTION_ID_FIELD] = newXactId;
                });
              }

              resolvePromise(newXactId);
            } catch (error) {
              console.error(`Error saving value for ID ${id}:`, error);
              resolvePromise(null);
            }
          });

          if (!isProcessingQueue.current[id]) {
            processQueue(id);
          }

          return resultPromise;
        },
        [syncedStateAtom, saveProp, setValueAtId, processQueue, getItemRowKey],
      ),
    );

    const value = useMemo(
      () => ({
        atom: syncedStateAtom,
        setValue,
        setValueAtId,
        save,
      }),
      [syncedStateAtom, setValue, setValueAtId, save],
    );

    return (
      <SyncedObjectsContext.Provider value={value}>
        {children}
      </SyncedObjectsContext.Provider>
    );
  }

  function useSyncedObjects(): ArraySyncedState<T> {
    const context = useContext(SyncedObjectsContext);
    return context ?? FALLBACK_CONTEXT;
  }

  return {
    SyncedObjectsProvider,
    useSyncedObjects,
  };
}

export function createSyncedObject<T extends SyncedStateValue>() {
  const { SyncedObjectsProvider, useSyncedObjects } = createSyncedObjects<T>();

  function SyncedObjectProvider({
    externalValue,
    save,
    children,
    rowKey,
    getRowKey: getRowKeyProp,
  }:
    | SingleObjectWithRowKeySyncedStateProps<T>
    | SingleObjectWithGetRowKeySyncedStateProps<T>) {
    const externalValueArray = useMemo(() => [externalValue], [externalValue]);
    const getRowKey = useCallback(
      (item: T) => {
        if (rowKey != null) {
          return rowKey;
        } else if (getRowKeyProp != null) {
          return getRowKeyProp(item);
        } else {
          return item.id;
        }
      },
      [rowKey, getRowKeyProp],
    );

    return (
      <SyncedObjectsProvider
        externalValue={externalValueArray}
        save={save}
        getRowKey={getRowKey}
      >
        {children}
      </SyncedObjectsProvider>
    );
  }

  function useSyncedObject(): SyncedState<T> {
    const context = useSyncedObjects();
    const syncedObjectAtom = useMemo(
      () => atom<T>((get) => get(context.atom)[0]),
      [context.atom],
    );

    return {
      atom: syncedObjectAtom,
      setValue: useAtomCallback(
        useCallback(
          (get, _set, updater: (draft: Draft<T>) => void) => {
            context.setValueAtId(get(syncedObjectAtom).id, updater);
          },
          [context, syncedObjectAtom],
        ),
      ),
      save: useAtomCallback(
        useCallback(
          (get) => context.save(get(syncedObjectAtom).id),
          [context, syncedObjectAtom],
        ),
      ),
    };
  }

  return {
    SyncedObjectProvider,
    useSyncedObject,
  };
}
