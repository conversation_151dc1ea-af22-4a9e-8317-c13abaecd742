import { HTTPError } from "#/utils/http_error";

export type ServerActionRetType = string | number | boolean | null | object;
export type ServerActionSignature = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  ...args: any[]
) => Promise<ServerActionRetType>;
export type ServerActionArg<F extends ServerActionSignature> = Parameters<F>[0];
export type ServerActionRet<F extends ServerActionSignature> = Awaited<
  ReturnType<F>
>;

// Invokes a server action function, which has been trampolined through the
// /api/actions family of routes. This utility provides a convenient type-safe
// wrapper for invoking the function.
//
// NOTE: Any function that is invoked with this wrapper must have a
// corresponding server API route defined at
// `app/app/api/actions/{fName}/route.ts`. You can use the
// `./scripts/generate_server_action_route.py` script to generate a route for
// your server action function.
export async function invokeServerAction<F extends ServerActionSignature>({
  fName,
  args,
  getToken,
  signal,
}: {
  fName: string;
  args: ServerActionArg<F>;
  getToken: () => Promise<string | null>;
  signal?: AbortSignal;
}): Promise<ServerActionRet<F>> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  // Ensure Clerk cookie is refreshed before making any requests
  const token = await getToken();
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const resp = await fetch(`/api/actions/${fName}`, {
    method: "POST",
    headers,
    body: JSON.stringify({
      function_args: args,
    }),
    signal,
  });
  if (!resp.ok) {
    throw new HTTPError(resp.status, await resp.text());
  }
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return (await resp.json()) as Awaited<ReturnType<F>>;
}
