export const LAST_VISITED_ORG_COOKIE = "last-visited-org";

// Client-side function to set the last visited org cookie
export function setLastVisitedOrg(orgName: string): void {
  const d = new Date();
  d.setTime(d.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
  const expires = "expires=" + d.toUTCString();

  // set the domain so that it applies also to preview links
  const domainPart = window.location.hostname?.includes("braintrust.dev")
    ? "domain=braintrust.dev;"
    : "";
  document.cookie = `${LAST_VISITED_ORG_COOKIE}=${encodeURIComponent(orgName)};${expires};${domainPart}path=/`;
}
