import { useEffect, useMemo, useRef, useState } from "react";
import { makeChannelUrl, type ChannelSpec } from "./duckdb";
import { useOrg, useUser } from "./user";
import { RealtimeChannel, type Roster } from "./realtime-data";
import { apiFetchGet } from "./btapi/fetch";
import { useSessionToken } from "./auth/session-token";
import { Mutex } from "async-mutex";
import { CancelledError } from "@tanstack/react-query";

export function useRealtimeChannel({
  spec,
  onEvent,
}: {
  spec: ChannelSpec | null;
  onEvent: (event: unknown) => void | Promise<void>;
}) {
  const { user } = useUser();
  const org = useOrg();
  const apiUrl = org.api_url;
  const channelUrl = useMemo(
    () => (spec ? makeChannelUrl({ apiUrl, spec }) : null),
    [apiUrl, spec],
  );
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [roster, setRoster] = useState<Roster | null>(null);
  const { getOrRefreshToken } = useSessionToken();

  // Since channel initialization is run in a fire-and-forget fashion
  // (asynchronously from the render loop), it is possible for re-renders to
  // kick off redundant channel initialization for the same object, which slows
  // things down significantly. Thus we enforce one channel initialization at a
  // time across renders using a mutex.
  const initializeChannelMutex = useRef<Mutex>(new Mutex());

  const onEventRef = useRef(onEvent);
  useEffect(() => {
    onEventRef.current = onEvent;
  }, [onEvent]);

  useEffect(() => {
    if (!channelUrl || channel) {
      return;
    }
    const updateChannel = async () => {
      const sessionToken = await getOrRefreshToken();
      const channelName = await (
        await apiFetchGet(channelUrl, sessionToken)
      ).json();

      const channel = await new Promise<RealtimeChannel>((resolve, reject) => {
        const channel = new RealtimeChannel(
          channelName.channel,
          channelName.token,
          user
            ? {
                user_id: user.id,
                email: user.email,
                avatar_url: user.avatar_url,
              }
            : null,
          org,
          () => {
            resolve(channel);
            return true;
          },
          (payload) => {
            Promise.resolve(onEventRef.current(payload)).catch((e) => {
              if (e instanceof CancelledError) {
                return; // Silently ignore cancellation of any queries
              }
              console.error("realtime error:", e);
              return;
            });
          },
          setRoster,
        );
      });
      setChannel(channel);
    };
    initializeChannelMutex.current
      .runExclusive(updateChannel)
      .catch(console.error);
  }, [channelUrl, channel, getOrRefreshToken, org, user]);

  return { channel, roster };
}
