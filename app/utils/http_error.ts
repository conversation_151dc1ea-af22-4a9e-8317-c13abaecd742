// HTTPError is an error type which holds a message and a status code, and can
// be written out to various response formats.
export class HTTPError extends Error {
  code: number;
  message: string;
  headers: Record<string, string> = {};

  constructor(code: number, message: string, headers?: Record<string, string>) {
    super(message);

    this.code = code;
    this.message = message;
    this.headers = headers ?? {};
  }

  toString(): string {
    return `HTTPError (code ${this.code}): ${this.message}`;
  }
}
