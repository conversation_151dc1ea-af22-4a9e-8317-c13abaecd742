"use server";

import { type ClientOptions } from "openai";
import { type ChatCompletion } from "openai/resources/index.mjs";

import { getOpenAI } from "#/utils/ai/openai";
import {
  type AISearchType,
  type AISearchResult,
  extractAISearchResult,
  eventsSearchCompletionArgs,
} from "@braintrust/local";
import { NOOP_SPAN, wrapOpenAI } from "braintrust";
import { makeSafeLogger, shouldLog } from "#/utils/observability";

const aiSearchLogger = makeSafeLogger("ai-search");

export async function runAISearch({
  openAIOpts,
  apiUrl,
  orgName,
  query,
  searchType,
  aiSchemaColumns,
  scoreFields,
}: {
  openAIOpts: ClientOptions;
  apiUrl: string;
  orgName: string | undefined;
  query: string;
  searchType: AISearchType;
  aiSchemaColumns: string[];
  scoreFields?: string[];
}): Promise<AISearchResult> {
  const parentLogger =
    aiSearchLogger && shouldLog(apiUrl) ? aiSearchLogger : NOOP_SPAN;
  return await parentLogger.traced(async (span) => {
    span.log({
      input: query,
      metadata: {
        searchType,
        aiSchemaColumns,
        scoreFields,
        orgName,
      },
    });
    const args = eventsSearchCompletionArgs({
      query,
      searchType,
      aiSchemaColumns,
      scoreFields: scoreFields ?? [],
      btql: true,
    });

    let completion: ChatCompletion | null = null;
    try {
      const openAI = wrapOpenAI(getOpenAI(openAIOpts));
      completion = await openAI.chat.completions.create(args);
    } catch (e) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      throw new Error(`OpenAI API error: ${(e as Error)?.message}`);
    }

    try {
      const ret = extractAISearchResult(completion);
      span.log({ output: ret });
      return ret;
    } catch (e) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      throw new Error(`Internal error: ${(e as Error)?.message}`);
    }
  });
}
