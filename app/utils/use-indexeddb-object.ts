import { useCallback, useEffect, useRef, useState } from "react";
import { loadBtCacheDB } from "./btapi/load-bt-cache-db";

interface UseIndexedDBObjectOptionsWithDefault<T> {
  store: string;
  key?: string;
  defaultValue: T;
}

interface UseIndexedDBObjectOptionsWithoutDefault {
  store: string;
  key?: string;
  defaultValue?: never;
}

type UseIndexedDBObjectOptions<T> =
  | UseIndexedDBObjectOptionsWithDefault<T>
  | UseIndexedDBObjectOptionsWithoutDefault;

export function useIndexedDBObject<T>(
  options: UseIndexedDBObjectOptionsWithDefault<T>,
): [T, (value: T | ((prev: T) => T)) => void, VoidFunction];

export function useIndexedDBObject<T>(
  options: UseIndexedDBObjectOptionsWithoutDefault,
): [
  T | undefined,
  (value: T | ((prev: T | undefined) => T)) => void,
  VoidFunction,
];

export function useIndexedDBObject<T>({
  store,
  key = "default",
  defaultValue,
}: UseIndexedDBObjectOptions<T>): [
  T | undefined,
  (value: T | ((prev: T | undefined) => T)) => void,
  VoidFunction,
] {
  const [state, setState] = useState<T | undefined>(defaultValue);
  const [isLoaded, setIsLoaded] = useState(false);
  const dbRef = useRef<IDBDatabase | null>(null);

  useEffect(() => {
    const loadInitialValue = async () => {
      try {
        const db = await loadBtCacheDB();
        dbRef.current = db;

        const transaction = db.transaction([store], "readwrite");
        const objectStore = transaction.objectStore(store);
        const request = objectStore.get(key);

        request.onsuccess = () => {
          const result = request.result;
          if (result !== undefined) {
            setState(result);
          }
          setIsLoaded(true);
        };

        request.onerror = () => {
          console.error("Failed to load from IndexedDB:", request.error);
          setIsLoaded(true);
        };
      } catch (error) {
        console.error("Failed to initialize IndexedDB:", error);
        setIsLoaded(true);
      }
    };

    loadInitialValue();
  }, [store, key]);

  const saveToIndexedDB = useCallback(
    async (newValue: T) => {
      if (!dbRef.current || !isLoaded) return;

      try {
        const transaction = dbRef.current.transaction([store], "readwrite");
        const objectStore = transaction.objectStore(store);
        objectStore.put(newValue, key);
      } catch (error) {
        console.error("Failed to save to IndexedDB:", error);
      }
    },
    [store, key, isLoaded],
  );

  const setValue = useCallback(
    (value: T | ((prev: T | undefined) => T)) => {
      setState((prev) => {
        const newValue =
          typeof value === "function"
            ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              (value as (prev: T | undefined) => T)(prev)
            : value;

        saveToIndexedDB(newValue);

        return newValue;
      });
    },
    [saveToIndexedDB],
  );

  const clearValue = useCallback(() => {
    setState(undefined);
    if (dbRef.current && isLoaded) {
      const transaction = dbRef.current.transaction([store], "readwrite");
      const objectStore = transaction.objectStore(store);
      objectStore.delete(key);
    }
  }, [store, key, isLoaded]);

  return [state, setValue, clearValue];
}
