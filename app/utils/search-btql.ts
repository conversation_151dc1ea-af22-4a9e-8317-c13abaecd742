import {
  Parser,
  type Expr as ParsedExpr,
  type Expr,
} from "@braintrust/btql/parser";
import {
  type CheckResult,
  checkTag,
  type Clause,
  type Clause<PERSON>he<PERSON>,
  makeSimpleMatchFilters,
  makeSimpleTagFilters,
  type Search,
  type SearchSpec,
  type ClauseSpec,
  type ClauseType,
} from "./search/search";
import {
  type JSONSchemaObject,
  type LogicalSchema,
  type PhysicalSchema,
} from "@braintrust/btql/schema";
import {
  type BindContext,
  bindExpr,
  bindSortItem,
  type BoundExpr,
  traverseExpr,
} from "@braintrust/btql/binder";
import {
  planExpr,
  planSortItem,
  transformAddCoercions,
  type ToSQL,
} from "@braintrust/btql/planner";
import {
  applyComputedFields,
  DUCKDB_PHYSICAL_SCHEMA,
  type DuckDBTableName,
  type TopLevelField,
} from "@braintrust/local/api-schema";
import { extractLogicalSchemaItemsObject } from "@braintrust/local/query";
import { isEmpty } from "./object";

import { type Dispatch, type SetStateAction, useMemo, useState } from "react";
import { type CustomColumnDefinition } from "./custom-columns/use-custom-columns";
import { z } from "zod";

export interface BTQLTableDefinition {
  logical: LogicalSchema;
  physical: PhysicalSchema["tables"][string];
}

export function bindBTQLExpr({
  table,
  expr,
  topLevelFields,
  btqlFields,
  tableAlias,
}: {
  table: DuckDBTableName | BTQLTableDefinition;
  expr: Expr;
  topLevelFields: TopLevelField[];
  btqlFields?: {
    field: string;
    btql: string;
    typeOverride?: JSONSchemaObject;
  }[];
  tableAlias?: string;
}): { bound: BoundExpr; sql: ToSQL } {
  let logicalSchema;
  let physicalSchema;
  if (typeof table === "string") {
    logicalSchema = extractLogicalSchemaItemsObject(table);
    physicalSchema = DUCKDB_PHYSICAL_SCHEMA.tables[table];
  } else {
    logicalSchema = table.logical;
    physicalSchema = table.physical;
  }

  const bindCtx: BindContext = {
    schema: logicalSchema,
    scope: {},
    queryText: undefined,
    bindingMeasures: false,
  };

  applyComputedFields({
    bindCtx,
    schema: logicalSchema,
    topLevelFields,
    btqlFields,
  });

  const planCtx = {
    schema: DUCKDB_PHYSICAL_SCHEMA,
    table: {
      alias: tableAlias ?? "base",
      schema: physicalSchema,
    },
  };

  const boundFilter = bindExpr(bindCtx, expr);
  // See "necessary evil" comment below
  transformAddCoercions(boundFilter);

  const sqlFilter = planExpr(planCtx, boundFilter);

  return { bound: boundFilter, sql: sqlFilter };
}
const checkBTQLBoundClause = ({
  clause,
  table: logicalSchema,
  topLevelFields,
}: {
  clause: ClauseSpec<ClauseType>;
  table: LogicalSchema;
  topLevelFields: TopLevelField[];
}): CheckResult<ClauseType> => {
  const parser = new Parser(clause.text);

  const bindCtx: BindContext = {
    schema: logicalSchema,
    scope: {},
    queryText: clause.text,
    bindingMeasures: false,
  };

  applyComputedFields({ bindCtx, schema: logicalSchema, topLevelFields });

  try {
    switch (clause.type) {
      case "filter":
        const parsedFilter = parser.parseExpr();
        // Check for bare field names without operators (common mistake)
        if (
          !parsedFilter.op ||
          parsedFilter.op === "ident" ||
          parsedFilter.op === "literal"
        ) {
          return {
            type: "error",
            error:
              "Invalid filter expression. Use comparison operators like =, >, <, CONTAINS, etc.",
          };
        }

        const boundFilter = bindExpr(bindCtx, parsedFilter);

        return {
          type: "checked",
          extraFields: {
            btql: { parsed: { btql: clause.text }, bound: boundFilter },
          },
        };

      case "sort":
        const parsedSort = parser.parseSortExpr();
        const boundSort = bindSortItem(bindCtx, parsedSort);

        return {
          type: "checked",
          extraFields: {
            btql: { parsed: { btql: clause.text }, bound: boundSort },
          },
        };

      case "tag":
        return checkTag({ text: clause.text });
      case "match":
        return { type: "checked", extraFields: {} };
    }
  } catch (e) {
    return { type: "error", error: `${e}` };
  }
};

export const checkBTQLClause = ({
  clause,
  table,
  topLevelFields,
  customColumnsSchema,
}: {
  clause: ClauseSpec<ClauseType>;
  table: DuckDBTableName | BTQLTableDefinition;
  topLevelFields: TopLevelField[];
  customColumnsSchema?: JSONSchemaObject | null;
}): CheckResult<ClauseType> => {
  const parser = new Parser(clause.text);
  let logicalSchema: BTQLTableDefinition["logical"];
  let physicalSchema: BTQLTableDefinition["physical"];
  if (typeof table === "string") {
    logicalSchema = extractLogicalSchemaItemsObject(table);
    physicalSchema = DUCKDB_PHYSICAL_SCHEMA.tables[table];
  } else {
    logicalSchema = table.logical;
    physicalSchema = table.physical;
  }

  const bindCtx: BindContext = {
    schema: customColumnsSchema
      ? { properties: { ...logicalSchema.properties, ...customColumnsSchema } }
      : logicalSchema,
    scope: {},
    queryText: clause.text,
    bindingMeasures: false,
  };

  applyComputedFields({ bindCtx, schema: logicalSchema, topLevelFields });

  const customColumnSchemaEntries =
    customColumnsSchema &&
    Object.entries(customColumnsSchema).map(([field, schema]) => [
      field,
      { ...schema, path: [field], type: { type: "varchar" } },
    ]);

  const planCtx = {
    schema: DUCKDB_PHYSICAL_SCHEMA,
    table: {
      alias: "base",
      schema: customColumnSchemaEntries
        ? {
            ...physicalSchema,
            columns: {
              ...physicalSchema.columns,
              ...Object.fromEntries(customColumnSchemaEntries),
            },
          }
        : physicalSchema,
    },
  };
  try {
    switch (clause.type) {
      case "filter":
        const parsedFilter = parser.parseExpr();
        const boundFilter = bindExpr(bindCtx, parsedFilter);

        // This is a necessary evil, to add converisions _away_ from unknown types
        // into the SQL planner.
        transformAddCoercions(boundFilter);

        const sqlFilter = planExpr(planCtx, boundFilter);

        if (parser.finished()) {
          return {
            type: "checked",
            extraFields: {
              btql: {
                parsed: { btql: clause.text },
                bound: boundFilter,
                sql: sqlFilter,
              },
            },
          };
        } else {
          return { type: "error", error: "Extra characters after parsing" };
        }
      case "sort":
        // As far as I can tell (XXX confirm with Austin), the sort
        // format is compatible between BTQL and SQL
        const parsedSort = parser.parseSortExpr();
        const boundSort = bindSortItem(bindCtx, parsedSort);
        const sqlSort = planSortItem(planCtx, boundSort);

        if (parser.finished()) {
          return {
            type: "checked",
            extraFields: {
              btql: {
                parsed: { btql: clause.text },
                bound: boundSort,
                sql: sqlSort,
              },
            },
          };
        } else {
          return { type: "error", error: "Extra characters after parsing" };
        }
      case "tag":
        return checkTag({ text: clause.text });
      case "match":
        return { type: "checked", extraFields: {} };
    }
  } catch (e) {
    return { type: "error", error: `${e}` };
  }
};

export function makeBtqlTagsFilter(s: SearchSpec): ParsedExpr[] {
  if (isEmpty(s.tag)) {
    return [];
  }

  const value = s.tag
    .filter((tag) => tag.text.startsWith("+"))
    .map((tag) => tag.text.slice(1));
  if (value.length === 0) {
    return [];
  }

  return [
    {
      op: "includes",
      haystack: {
        op: "ident",
        name: ["tags"],
      },
      needle: {
        op: "literal",
        value,
      },
    },
  ];
}

// DEPRECATION_NOTICE: This is really hacky because we have the testSearch() functionality
// that only uses the ClauseSpec, not the Clause, which needs to call this code-path as long
// as we still build SQL queries to test. Once we stop doing that, we can just make this accept
// Clause<T>'s and simplify.
export function compileClauseToSQL<T extends ClauseType>(
  clause: Clause<T> | ClauseSpec<T>,
): string {
  if ("btql" in clause && clause.btql && clause.btql.sql) {
    return clause.btql.sql.toPlainStringQuery();
  } else {
    return clause.text;
  }
}

const scoresOrMetrics: (string | number)[] = ["scores", "metrics"];

export function makeFilterClauses(
  clauses: Clause<"filter">[],
  customColumnDefinitions?: CustomColumnDefinition[],
): {
  plain: string[];
  postAggregation: string[];
  comments: string[];
  customColumns: string[];
  tagFilters: string[];
} {
  const plain = [];
  const postAggregation = [];
  const comments = [];
  const customColumns = [];
  const customColumnNames: (string | number)[] | undefined =
    customColumnDefinitions?.map(({ name }) => name);
  const tagFilters = [];
  for (const clause of clauses) {
    if (clause.btql) {
      const tagArray = hackyParseTagsFilter(clause.btql.bound);
      if (tagArray) {
        tagFilters.push(...tagArray);
        continue;
      }
      let filterType = null;
      traverseExpr(clause.btql.bound, (expr: BoundExpr) => {
        if (expr.op === "field" && scoresOrMetrics.includes(expr.name[0])) {
          filterType = "scoresOrMetrics";
          return false;
        } else if (expr.op === "field" && expr.name[0] === "comment") {
          filterType = "comments";
          return false;
        } else if (
          expr.op === "field" &&
          customColumnNames?.includes(expr.name[0])
        ) {
          filterType = "customColumns";
          return false;
        } else {
          return true;
        }
      });
      if (filterType === "scoresOrMetrics") {
        postAggregation.push(clause.btql.sql.toPlainStringQuery());
      } else if (filterType === "comments") {
        comments.push(clause.btql.sql.toPlainStringQuery());
      } else if (filterType === "customColumns") {
        customColumns.push(clause.btql.sql.toPlainStringQuery());
      } else {
        plain.push(clause.btql.sql.toPlainStringQuery());
      }
    } else {
      plain.push(compileClauseToSQL(clause));
    }
  }
  return { plain, postAggregation, comments, customColumns, tagFilters };
}

export type UseClauseCheckerProps = {
  clauseChecker: ClauseChecker | null;
  setTopLevelFields: Dispatch<SetStateAction<TopLevelField[]>>;
  setCustomColumnsSchema: Dispatch<
    SetStateAction<JSONSchemaObject | null | undefined>
  >;
};

export function useBtqlClauseChecker(
  table: LogicalSchema | null,
): Omit<UseClauseCheckerProps, "setCustomColumnsSchema"> {
  const [topLevelFields, setTopLevelFields] = useState<TopLevelField[]>([]);
  const clauseChecker: ClauseChecker | null = useMemo(() => {
    return table
      ? async (clause: ClauseSpec<ClauseType>) =>
          checkBTQLBoundClause({
            clause,
            table,
            topLevelFields,
          })
      : null;
  }, [table, topLevelFields]);

  return { clauseChecker, setTopLevelFields };
}

export function useClauseChecker(
  table:
    | keyof (typeof DUCKDB_PHYSICAL_SCHEMA)["tables"]
    | BTQLTableDefinition
    | null,
  fetchCustomColumns?: boolean,
): UseClauseCheckerProps {
  const [topLevelFields, setTopLevelFields] = useState<TopLevelField[]>([]);
  const [customColumnsSchema, setCustomColumnsSchema] = useState<
    JSONSchemaObject | null | undefined
  >(fetchCustomColumns ? undefined : null);

  const btqlChecker: ClauseChecker | null = useMemo(() => {
    return table && customColumnsSchema !== undefined
      ? async (clause: ClauseSpec<ClauseType>) =>
          checkBTQLClause({
            clause,
            table,
            topLevelFields,
            customColumnsSchema: customColumnsSchema?.properties,
          })
      : null;
  }, [table, topLevelFields, customColumnsSchema]);

  return {
    clauseChecker: btqlChecker,
    setTopLevelFields,
    setCustomColumnsSchema,
  };
}

export function buildDefaultWhere(
  s: SearchSpec | Search,
  fullTextSearchPaths: string[],
): string {
  const filters = s.filter?.length
    ? s.filter.map((f) => compileClauseToSQL(f))
    : [];
  filters.push(...makeSimpleTagFilters(s));
  filters.push(...makeSimpleMatchFilters(s, fullTextSearchPaths));
  return filters.length ? filters.map((f) => `(${f})`).join(" AND ") : "true";
}

export function replaceFieldWithCustomColumn(
  initialExpr: ParsedExpr,
  customColumnDefinitions: { name: string; expr: string }[],
) {
  try {
    const expr = initialExpr;
    if ("btql" in expr) {
      const match = customColumnDefinitions.find((col) =>
        expr.btql.includes(col.name),
      );
      if (match) {
        return {
          ...expr,
          btql: expr.btql.replaceAll(match.name, match.expr),
        };
      }
    }
    if (expr.op === "ident") {
      const customCol = customColumnDefinitions?.find(
        ({ name }) => name === expr.name[0],
      );
      if (expr.name.length === 1 && customCol) {
        const parser = new Parser(customCol.expr);
        return parser.parseExpr();
      }
      return expr;
    }

    const result = { ...expr };

    if ("left" in result && result.left && typeof result.left === "object") {
      result.left = replaceFieldWithCustomColumn(
        result.left,
        customColumnDefinitions,
      );
    }
    if ("right" in result && result.right && typeof result.right === "object") {
      result.right = replaceFieldWithCustomColumn(
        result.right,
        customColumnDefinitions,
      );
    }
    if ("expr" in result && result.expr && typeof result.expr === "object") {
      result.expr = replaceFieldWithCustomColumn(
        result.expr,
        customColumnDefinitions,
      );
    }
    if ("args" in result && Array.isArray(result.args)) {
      result.args = result.args.map((arg) => {
        if (arg && typeof arg === "object") {
          return replaceFieldWithCustomColumn(arg, customColumnDefinitions);
        }
        return arg;
      });
    }

    return result;
  } catch {}

  return initialExpr;
}

function hackyParseTagsFilter(expr: BoundExpr): string[] | undefined {
  let notIncludes = false;
  let hasTags = false;
  let hasIncludes = false;
  let tagArray: string[] = [];
  traverseExpr(expr, (expr: BoundExpr) => {
    if (expr.op === "includes") {
      hasIncludes = true;

      if (expr.haystack.op === "field" && expr.haystack.name[0] === "tags") {
        hasTags = true;
      }

      if (expr.needle.op === "literal") {
        const stringArray = z.array(z.string()).safeParse(expr.needle.value);
        if (stringArray.success) {
          tagArray = stringArray.data;
        }
      }

      return false;
    } else if (expr.op === "not" && expr.expr.op === "includes") {
      notIncludes = true;
      return true;
    }
    return true;
  });

  if (!(hasTags && hasIncludes && tagArray.length > 0)) {
    return undefined;
  } else if (notIncludes) {
    return tagArray.map((t) => `-${t}`);
  } else {
    return tagArray.map((t) => `+${t}`);
  }
}
