import {
  isArray,
  isObject,
  removeTrailingZ<PERSON>s,
  safeDeserializePlainStringAsJSON,
  updatePathMut,
} from "./object";
import { expect, test } from "vitest";

test("remove trailing zeros", async () => {
  const cases = [
    ["0", "0"],
    ["-42", "-42"],
    ["99", "99"],
    ["1.5", "1.5"],
    ["60000", "60000"],
    ["500.000", "500"],
    ["-43.000", "-43"],
    ["3999.990", "3999.99"],
    ["-90.300", "-90.3"],
    ["-9239487.398428", "-9239487.398428"],
    ["9007199254740991.0", "9007199254740991"],
    ["-9007199254740991.0", "-9007199254740991"],
  ];

  for (const [s, expected] of cases) {
    const actual = removeTrailingZeros(s);
    expect(actual).toEqual(expected);
  }
});

test("safeDeserialize nulls", async () => {
  const cases = ["null", "", " ", "\t", "\n", "\r", "\r\n"];

  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    expect(value).toEqual(null);
  }
});

test("safeDeserialize plain strings", async () => {
  const cases = [
    "+",
    "-",
    "a42",
    "foo",
    "0f",
    "two words",
    "'single quoted'",
    "`backticks`",
    "undefined",
  ];

  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    expect(value).toEqual(s);
  }
});

test("safeDeserialize double quoted strings", async () => {
  const cases = ['""', '"double quoted"'];

  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    expect(value).not.toEqual(s);
    expect(value).toEqual(JSON.parse(s));
  }
});

test("safeDeserialize invalid and unsafe values", async () => {
  const cases = [
    "0o123",
    "0b101",
    "0xabc",
    "1e3",
    "+3",
    "4.",
    "Infinity",
    "-Infinity",
    "NaN",
    '{ "foo": "bar" ',
    "2, 3, 5",
    "9007199254740992", // Number.MAX_SAFE_INTEGER + 1
    "-9007199254740992", // Number.MIN_SAFE_INTEGER - 1
    "True",
    "False",
  ];

  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    expect(typeof value).toEqual("string");
    expect(value.toString()).toEqual(s);
  }
});

test("safeDeserialize valid objects and arrays", async () => {
  const cases = [
    "{}",
    '{ "foo": "bar" }',
    '{ "foo": 1111111111111 }',
    '{ "foo": 1.5, "bar": [true] }',
    "[]",
    "[1, 2, 3]",
    "[1, 2, 3, {}]",
    '[1, 2, 3, { "foo": "bar" }]',
  ];

  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    if (!isObject(value) && !isArray(value)) {
      throw new Error(`Expected object or array, got ${value}`);
    }
    expect(value).toEqual(JSON.parse(s));
  }
});

test("safeDeserialize valid numbers and booleans", async () => {
  const cases = [
    "0",
    "1",
    "1.5",
    "-42",
    "60000",
    "-500",
    "500.0000",
    "-43.0000",
    "3999.9900",
    "-9.30",
    "-9239487.3984280",
    "9007199254740991", // Number.MAX_SAFE_INTEGER
    "9007199254740991.000", // Number.MAX_SAFE_INTEGER
    "-9007199254740991", // Number.MIN_SAFE_INTEGER
    "-9007199254740991.000", // Number.MIN_SAFE_INTEGER
  ];

  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    expect(typeof value).toEqual("number");
    const normalizedS = removeTrailingZeros(s);
    expect(value.toString()).toEqual(normalizedS);
  }
});

test("safeDeserialize valid booleans", async () => {
  const cases = ["true", "false"];
  for (const s of cases) {
    const value = safeDeserializePlainStringAsJSON(s);
    expect(typeof value).toEqual("boolean");
    const normalizedS = removeTrailingZeros(s);
    expect(value.toString()).toEqual(normalizedS);
  }
});

test("updatePathMut - basic functionality", async () => {
  const row = { a: 1 };
  const result = updatePathMut(row, ["b"], 2);
  expect(result).toEqual({ a: 1, b: 2 });
  expect(result).toBe(row); // Should mutate the original object
});

test("updatePathMut - deep nested path", async () => {
  const row = { a: 1 };
  const result = updatePathMut(row, ["b", "c", "d", "e"], 5);
  expect(result).toEqual({ a: 1, b: { c: { d: { e: 5 } } } });
});

test("updatePathMut - existing object structure", async () => {
  const row = { a: 1, b: { x: 10 } };
  const result = updatePathMut(row, ["b", "c"], 3);
  expect(result).toEqual({ a: 1, b: { x: 10, c: 3 } });
});

test("updatePathMut - empty path returns value", async () => {
  const row = { a: 1 };
  const result = updatePathMut(row, [], "test");
  expect(result).toEqual("test");
});

test("updatePathMut - replace non-object with nested structure", async () => {
  const row = { foo: 42 };
  const result = updatePathMut(row, ["foo", "bar"], "value");
  expect(result).toEqual({ foo: { bar: "value" } });
});

test("updatePathMut - keepExistingValue option", async () => {
  const row = { foo: { bar: "existing" } };
  const result = updatePathMut(row, ["foo", "bar"], "new", {
    keepExistingValue: true,
  });
  expect(result).toEqual({ foo: { bar: "existing" } });
});

test("updatePathMut - keepExistingValue option with non-existing path", async () => {
  const row = { foo: { x: 1 } };
  const result = updatePathMut(row, ["foo", "bar"], "new", {
    keepExistingValue: true,
  });
  expect(result).toEqual({ foo: { x: 1, bar: "new" } });
});

test("updatePathMut - keepExistingValue option with sub-path", async () => {
  const row = { foo: { x: 1 } };
  const result = updatePathMut(row, ["foo"], "new", {
    keepExistingValue: true,
  });
  expect(result).toEqual({ foo: { x: 1 } });
});

test("updatePathMut - keepExistingValue option with deep sub-path", async () => {
  const row = { foo: { x: { y: { z: 1 } } } };
  const result = updatePathMut(row, ["foo", "x", "y"], "new", {
    keepExistingValue: true,
  });
  expect(result).toEqual({ foo: { x: { y: { z: 1 } } } });
});

test("updatePathMut - keepExistingValue option with empty object", async () => {
  const row = { foo: {} };
  const result = updatePathMut(row, ["foo", "bar"], "new", {
    keepExistingValue: true,
  });
  expect(result).toEqual({ foo: { bar: "new" } });
});

test("updatePathMut - complex nested replacement", async () => {
  const row = {
    level1: {
      level2: "string",
      level2b: { existing: true },
    },
  };
  const result = updatePathMut(row, ["level1", "level2", "level3"], "deep");
  expect(result).toEqual({
    level1: {
      level2: { level3: "deep" },
      level2b: { existing: true },
    },
  });
});
