import {
  autoCreateEntity,
  type AutoCreateEntityResult,
} from "./auto-create-entity";
import { createDataset } from "#/app/app/[org]/p/[project]/datasets/[dataset]/createDataset";
import { useMutation, type UseMutationOptions } from "@tanstack/react-query";
import { type RegisterDatasetOutput } from "#/pages/api/dataset/register";

export interface AutoCreateDatasetOptions {
  orgId: string;
  projectName: string;
  existingNames?: string[];
}

export async function autoCreateDataset(
  baseName: string,
  { orgId, projectName, existingNames }: AutoCreateDatasetOptions,
) {
  return autoCreateEntity(baseName, {
    createEntity: async (name: string) => {
      return createDataset({
        orgId,
        projectName,
        datasetName: name,
      });
    },
    existingNames,
    extractName: (data: RegisterDatasetOutput) => data.dataset?.name || "",
    extractId: (data: RegisterDatasetOutput) => data.dataset?.id || "",
  });
}

export const useAutoCreateDataset = (
  options: AutoCreateDatasetOptions,
  mutationOptions?: UseMutationOptions<
    AutoCreateEntityResult<RegisterDatasetOutput>,
    Error,
    string
  >,
) => {
  return useMutation({
    mutationKey: ["autoCreateDataset", options],
    mutationFn: async (baseName: string) =>
      await autoCreateDataset(baseName, options),
    ...mutationOptions,
  });
};
