import { type FetchOrgUsersOutput } from "#/utils/org-users";

const userIdRegex = "[a-zA-Z0-9-]*";
export const mentionRegex = new RegExp(`@${userIdRegex}`);
export const mentionRegexGlobal = new RegExp(`@${userIdRegex}`, "g");
export const mentionRegexCapture = new RegExp(`@(${userIdRegex})`);
export const mentionRegexCaptureGlobal = new RegExp(`@(${userIdRegex})`, "g");

export const userIdMatchRegex =
  /[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12}/;

export function parseMentions({
  text,
  formatMention,
  orgUsers,
}: {
  text: string;
  formatMention?: (mention: string) => string;
  orgUsers: FetchOrgUsersOutput;
}) {
  return text.replace(mentionRegexCaptureGlobal, (match, userId) => {
    const user = orgUsers[userId];
    if (user) {
      const displayName =
        user.given_name && user.family_name
          ? `${user.given_name} ${user.family_name}`
          : user.email || userId;
      return formatMention
        ? formatMention(`@${displayName}`)
        : `@${displayName}`;
    } else if (userIdMatchRegex.test(match)) {
      return "@Unknown";
    }
    return match;
  });
}
