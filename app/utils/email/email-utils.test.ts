import { describe, expect, test } from "vitest";
import {
  getAssignmentText,
  getSubjectLine,
  getUserName,
} from "#/utils/email/email-utils";

describe("getSubjectLine", () => {
  test.each([
    ["Org", "Project", undefined, undefined, "Re: Org/Project"],
    ["Org", "Project", null, "Anything", "Re: Org/Project"],
    ["Org", "Project", "logs", "Ignored", "Re: Org/Project - Project logs"],
    ["Org", "Project", "ticket", "ABC-123", "Re: Org/Project - ABC-123"],
    ["Org", "Project", "ticket", undefined, "Re: Org/Project"],
    ["Org", "Project", "", "ABC-123", "Re: Org/Project"],
    ["Org", undefined, undefined, undefined, "Re: Org"],
    ["Org", null, undefined, undefined, "Re: Org"],
  ])(
    "getSubjectLine(%p, %p, %p, %p)",
    (orgName, projectName, entityType, entityName, expected) => {
      expect(getSubjectLine(orgName, projectName, entityType, entityName)).toBe(
        expected,
      );
    },
  );
});

describe("getUserName", () => {
  test.each([
    [undefined, "Someone"],
    [null, "Someone"],
    [{}, "Someone"],
    [{ given_name: "Test", family_name: "User" }, "Test User"],
    [{ email: "<EMAIL>" }, "<EMAIL>"],
  ])("getUserName(%p)", (user, expected) => {
    expect(getUserName(user)).toBe(expected);
  });
});

describe("getAssignmentText", () => {
  const assigner = "Alice";

  test.each([
    [
      true,
      "Feature X",
      null,
      assigner,
      "experiment",
      `${assigner} assigned you to a trace`,
    ],
    [
      false,
      "Feature X",
      undefined,
      assigner,
      "experiment",
      `${assigner} assigned you to a trace`,
    ],
    [
      true,
      "Feature X",
      "Proj",
      assigner,
      null,
      `${assigner} assigned you to a trace`,
    ],
    [
      false,
      "Feature X",
      "Proj",
      assigner,
      undefined,
      `${assigner} assigned you to a trace`,
    ],
    [
      true,
      "Feature X",
      "Proj",
      assigner,
      "",
      `${assigner} assigned you to a trace`,
    ],
    [
      true,
      "N/A",
      "Apollo",
      assigner,
      "logs",
      `${assigner} assigned you to a trace in **Apollo** logs`,
    ],
    [
      false,
      "N/A",
      "Apollo",
      assigner,
      "logs",
      `${assigner} assigned you to a trace in Apollo logs`,
    ],
    [
      false,
      "ABC-123",
      "Voyager",
      assigner,
      "experiment",
      `${assigner} assigned you to a trace in an experiment`,
    ],
    [
      false,
      "Outage-9",
      "Voyager",
      assigner,
      "dataset",
      `${assigner} assigned you to a trace in a dataset`,
    ],
    [
      false,
      "CPU-High",
      "Voyager",
      assigner,
      "features",
      `${assigner} assigned you to a trace in a feature`,
    ],
    [
      true,
      "ABC-123",
      "Voyager",
      assigner,
      "experiment",
      `${assigner} assigned you to a trace in experiment **ABC-123**`,
    ],
    [
      true,
      "Outage-9",
      "Voyager",
      assigner,
      "dataset",
      `${assigner} assigned you to a trace in dataset **Outage-9**`,
    ],
    [
      true,
      "CPU-High",
      "Voyager",
      assigner,
      "features",
      `${assigner} assigned you to a trace in feature **CPU-High**`,
    ],
    [
      true,
      null,
      "Voyager",
      assigner,
      "experiment",
      `${assigner} assigned you to a trace in Voyager`,
    ],
    [
      false,
      undefined,
      "Voyager",
      assigner,
      "dataset",
      `${assigner} assigned you to a trace in Voyager`,
    ],
    [
      true,
      "Run #1",
      "Big Project",
      assigner,
      "logs",
      `${assigner} assigned you to a trace in **Big Project** logs`,
    ],
  ])(
    "formatText(emailBody=%p, entityName=%p, projectName=%p, assigner=%p, entityType=%p)",
    (
      emailBody,
      entityName,
      projectName,
      assignerName,
      entityType,
      expected,
    ) => {
      expect(
        getAssignmentText(
          emailBody,
          entityName,
          projectName,
          assignerName,
          entityType,
        ),
      ).toBe(expected);
    },
  );

  test("bolding behavior for email body", () => {
    expect(
      getAssignmentText(false, "Dark Mode", "Proj", "Bob", "experiment"),
    ).toBe("Bob assigned you to a trace in an experiment");

    expect(
      getAssignmentText(true, "Dark Mode", "Proj", "Bob", "experiment"),
    ).toBe("Bob assigned you to a trace in experiment **Dark Mode**");
  });
});
