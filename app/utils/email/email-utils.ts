export const getUserName = (
  user:
    | {
        given_name?: string | null | undefined;
        family_name?: string | null | undefined;
        email?: string | null | undefined;
      }
    | undefined
    | null,
) => {
  return user?.given_name && user?.family_name
    ? `${user.given_name} ${user.family_name}`
    : (user?.email ?? "Someone");
};

export const getSubjectLine = (
  orgName: string,
  projectName?: string | null,
  entityType?: string | null,
  entityName?: string | null,
) => {
  const project = projectName ?? "";
  const base = `Re: ${orgName}${project ? `/${project}` : ""}`;

  if (!entityType) return base;

  if (entityType === "logs") {
    return `${base} - Project logs`;
  }

  return entityName ? `${base} - ${entityName}` : base;
};

export const getAssignmentText = (
  emailBody: boolean,
  entityName: string | null | undefined,
  projectName: string | null | undefined,
  assignerName: string,
  entityType: string | null | undefined,
) =>
  !entityType || !projectName
    ? `${assignerName} assigned you to a trace`
    : entityType === "logs"
      ? `${assignerName} assigned you to a trace in ${emailBody ? `**${projectName}**` : projectName} logs`
      : entityName
        ? `${assignerName} assigned you to a trace in ${emailBody ? "" : `${/[aeiouAEIOU]/.test(entityType.charAt(0)) ? "an" : "a"} `}${entityType.charAt(entityType.length - 1) === "s" ? entityType.slice(0, entityType.length - 1) : entityType}${emailBody ? ` **${entityName}**` : ""}`
        : `${assignerName} assigned you to a trace in ${projectName}`;

export const getFromAddress = () => `Braintrust <<EMAIL>>`;
