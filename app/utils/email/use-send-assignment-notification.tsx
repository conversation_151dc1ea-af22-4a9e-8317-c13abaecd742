import {
  useActivePage,
  isExperimentPage,
  isDatasetPage,
  isPlaygroundPage,
} from "#/app/app/[org]/pathname-checker";
import { useAuth } from "@clerk/nextjs";
import { useCallback, useContext, useMemo } from "react";
import { useParams, usePathname, useSearchParams } from "next/navigation";
import { type sendAssignmentNotification } from "#/utils/assignment";
import { OrgUsersContext } from "#/utils/org-users-context";
import { useOrg, useUser } from "#/utils/user";
import { toast } from "sonner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { getUserName } from "#/utils/email/email-utils";
import { decodeURIComponentPatched } from "#/utils/url";

export const useSendAssignmentNotification = (assignedUsers: string[]) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = useParams<{
    dataset?: string;
    experiment?: string;
    playground?: string;
  }>();

  const link = `${pathname}${searchParams ? `?${searchParams}` : ""}`;
  const org = useOrg();
  const { user } = useUser();
  const { orgUsers } = useContext(OrgUsersContext);
  const { projectName } = useContext(ProjectContext);
  const datasetName = decodeURIComponentPatched(params?.dataset || "");
  const experimentName = decodeURIComponentPatched(params?.experiment || "");
  const playgroundName = decodeURIComponentPatched(params?.playground || "");

  const assignee = getUserName(user);

  const activePage = useActivePage();
  const { getToken } = useAuth();

  const pageContext = useMemo(() => {
    if (!pathname) {
      return;
    }

    if (isExperimentPage(pathname)) {
      return { pageType: "experiment", entityName: experimentName };
    }

    if (isDatasetPage(pathname)) {
      return { pageType: "dataset", entityName: datasetName };
    }

    if (isPlaygroundPage(pathname)) {
      return { pageType: "playground", entityName: playgroundName };
    }

    return { pageType: activePage, entityName: "" };
  }, [pathname, activePage, experimentName, datasetName, playgroundName]);

  const sendAssignNotification = useCallback(
    async (newUsers: string[]) => {
      const oldUsers = new Set(assignedUsers);
      const assignedUserIds = newUsers.filter((user) => !oldUsers.has(user));
      if (assignedUserIds.length === 0) {
        return;
      }
      const emails = assignedUserIds
        .map((userId) => orgUsers[userId]?.email)
        .filter((email): email is string => !!email);
      if (emails.length === 0) {
        return;
      }
      try {
        await invokeServerAction<typeof sendAssignmentNotification>({
          fName: "sendAssignmentNotification",
          args: {
            orgId: org.id,
            orgName: org.name,
            assignerName: assignee,
            link: link,
            emails: emails,
            entityType: pageContext?.pageType,
            entityName: pageContext?.entityName,
            projectName: projectName,
          },
          getToken,
        });
      } catch (error) {
        console.error("Failed to send assignment notifications:", error);
        toast.error(`Failed to send assignment notifications: ${error}`);
      }
    },
    [
      org.id,
      org.name,
      assignee,
      link,
      pageContext,
      getToken,
      assignedUsers,
      orgUsers,
      projectName,
    ],
  );

  return {
    sendAssignNotification,
  };
};
