import { useSessionToken } from "#/utils/auth/session-token";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useOrg } from "#/utils/user";
import { makeError } from "@braintrust/local";
import { loadBtCacheDB } from "./load-bt-cache-db";

import { useFeatureFlags } from "#/lib/feature-flags";
import { apiFetchGet } from "./fetch";

interface GetRequestOptions {
  disableCache?: boolean;
  apiUrl?: string;
}

export function useGetRequest<T>(
  endpoint: string | null,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  params?: any,
  options?: GetRequestOptions,
) {
  const { disableCache: disableCacheProp } = options ?? {};
  const org = useOrg();

  const {
    flags: { disableObjectCache },
  } = useFeatureFlags();

  const disableCache = disableCacheProp || disableObjectCache;

  const { getOrRefreshToken } = useSessionToken();
  // Remove the leading slash from endpoint if it exists
  const fullUrl = useMemo(() => {
    const requestUrl =
      endpoint &&
      `${options?.apiUrl ?? org.api_url}/${endpoint.replace(/^\//, "")}`;
    if (params) {
      const searchParams = new URLSearchParams(params);
      return `${requestUrl}?${searchParams.toString()}`;
    } else {
      return requestUrl;
    }
  }, [endpoint, options?.apiUrl, org.api_url, params]);

  const [data, setData] = useState<T | undefined>(undefined);
  const [error, setError] = useState<Error | undefined>(undefined);
  const [errorCode, setErrorCode] = useState<number | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  const refresh = useCallback(() => setRefreshCounter((v) => v + 1), []);

  useEffect(() => {
    (async () => {
      if (data !== undefined || disableCache) {
        return;
      }

      let btCacheDB = null;
      try {
        btCacheDB = await loadBtCacheDB();
      } catch (e) {
        console.error("Failed to open data cache", e);
        return;
      }

      const endpointStore = btCacheDB
        .transaction("btGets", "readonly")
        .objectStore("btGets");

      const request = endpointStore.get(JSON.stringify({ endpoint, params }));
      request.onsuccess = function (_event) {
        if (request.result) {
          setData(request.result);
          refresh();
        }
      };
    })();
  }, [data, endpoint, params, refresh, disableCache]);

  const [refreshCounter, setRefreshCounter] = useState<number>(0);
  const lastRefreshed = useRef<number>(0);

  useEffect(() => {
    (async () => {
      const sessionToken = await getOrRefreshToken();
      if (
        !fullUrl ||
        (refreshCounter === lastRefreshed.current && data !== undefined)
      ) {
        return;
      }

      setIsLoading(true);
      let btCacheDB = null;
      try {
        btCacheDB = await loadBtCacheDB();
      } catch (e) {
        console.error(e);
        // Assume the error is logged in the other effect
      }

      try {
        const resp = await apiFetchGet(fullUrl, sessionToken);
        const json = await resp.json();

        if (resp.ok) {
          const endpointStore =
            btCacheDB &&
            btCacheDB
              .transaction(["btGets"], "readwrite")
              .objectStore("btGets");
          if (endpointStore && !disableCache) {
            endpointStore.put(json, JSON.stringify({ endpoint, params }));
          }
          setData(json);
          setError(undefined);
          setErrorCode(undefined);
        } else {
          setError(new Error(JSON.stringify(json)));
          setErrorCode(resp.status);
        }
        lastRefreshed.current = refreshCounter;
      } catch (error) {
        setError(makeError(error));
        setErrorCode(500);
      } finally {
        setIsLoading(false);
      }
    })();
  }, [
    data,
    endpoint,
    fullUrl,
    params,
    refreshCounter,
    disableCache,
    getOrRefreshToken,
  ]);

  return { data, error, errorCode, refresh, isLoading };
}
