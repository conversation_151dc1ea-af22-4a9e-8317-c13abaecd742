import {
  type LoadedBtSessionToken,
  sessionFetchProps,
} from "#/utils/auth/session-token";
import { type SavedScorer } from "#/utils/scorers";
import { z } from "zod";
import { apiPostCors } from "#/utils/btapi/fetch";
import { _urljoin } from "braintrust/util";
import { normalizeProxyUrlBase } from "#/utils/user-types";
import { parseQuery } from "@braintrust/btql/parser";
import { fetchBtql } from "#/utils/btql/btql";

/**
 * Builds a BTQL filter from logIds and optional btqlFilter
 * Validates logIds as UUIDs for security
 */
function buildBtqlFilter(btqlFilter?: string, logIds?: string[]): string {
  if (!logIds || logIds.length === 0) {
    return btqlFilter || "true";
  }

  // Validate all logIds as UUIDs (security + no escaping needed)
  const uuidSchema = z.string().uuid();
  const validatedLogIds = logIds.map((id) => {
    try {
      return uuidSchema.parse(id);
    } catch {
      throw new Error(`Invalid UUID format: ${id}`);
    }
  });

  const logIdsFilter = `(${validatedLogIds.map((id) => `id = "${id}"`).join(" OR ")})`;

  return btqlFilter ? `(${btqlFilter}) AND ${logIdsFilter}` : logIdsFilter;
}

/**
 * Queries logs using BTQL and returns the raw log data
 */
async function queryLogs({
  apiUrl,
  sessionToken,
  projectId,
  btqlFilter,
  limit,
  applyToRootSpan,
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  projectId: string;
  btqlFilter: string;
  limit: number;
  applyToRootSpan: boolean;
}): Promise<Record<string, unknown>[]> {
  const queryStr = `
    select: * |
    from: project_logs("${projectId}") spans |
    filter: ${btqlFilter} ${applyToRootSpan ? "AND is_root" : ""} |
    limit: ${limit}
  `;

  const query = parseQuery(queryStr);

  const result = await fetchBtql({
    args: {
      query,
      brainstoreRealtime: true,
    },
    btqlFlags: {
      brainstore: true,
      brainstore_realtime: true,
      brainstore_skip_backfill_check: false,
      queryDiagnostics: false,
      customColumnsV2: false,
    },
    apiUrl,
    getOrRefreshToken: async () => sessionToken,
  });

  if (!("data" in result)) {
    throw new Error("BTQL response missing data");
  }

  return result.data;
}

/**
 * Invokes a single scorer on a single log row
 */
async function invokeScorerForRow({
  sessionToken,
  proxyUrl,
  orgName,
  scorer,
  row,
}: {
  sessionToken: LoadedBtSessionToken;
  proxyUrl: string;
  orgName: string;
  scorer: SavedScorer;
  row: Record<string, unknown>;
}): Promise<{
  kind: "success" | "error";
  function_id?: string;
  global_function?: string;
  result?: unknown;
  error?: string;
}> {
  const functionId =
    scorer.type === "function"
      ? { function_id: scorer.id }
      : { global_function: scorer.name };

  try {
    // Extract the fields needed for scoring (same as backend)
    const invokeInput = {
      input: row.input,
      output: row.output,
      expected: row.expected,
      metadata: row.metadata,
    };

    const payload = {
      ...functionId,
      api_version: 1,
      input: invokeInput,
      parent: undefined, // skip logging parent
      update_score: {
        object_type: "project_logs",
        object_id: row.project_id,
        row_id: row.id,
        token: crypto.randomUUID(),
      },
      org_name: orgName,
      stream: false,
    };

    const result = await apiPostCors({
      url: _urljoin(normalizeProxyUrlBase(proxyUrl), "function", "invoke"),
      sessionToken,
      payload: payload,
      alreadySerialized: false,
      headers: {
        "x-bt-org-name": orgName,
      },
    });

    if (!result.ok) {
      const errorText = await result.text();
      throw new Error(`HTTP ${result.status}: ${errorText}`);
    }

    const scoreResult = await result.json();

    return {
      kind: "success",
      ...functionId,
      result: scoreResult,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      kind: "error",
      ...functionId,
      error: errorMessage,
    };
  }
}

/**
 * Aggregates scoring results from multiple operations
 */
function aggregateScoringResults(
  results: Array<{
    row: Record<string, unknown>;
    results: Array<{
      kind: "success" | "error";
      function_id?: string;
      global_function?: string;
      result?: unknown;
      error?: string;
    }>;
  }>,
): {
  processed_count: number;
  success_count: number;
  error_count: number;
  results: typeof results;
} {
  let successCount = 0;
  let errorCount = 0;

  results.forEach(({ results: rowResults }) => {
    rowResults.forEach((result) => {
      if (result.kind === "success") {
        successCount++;
      } else if (result.kind === "error") {
        errorCount++;
      }
    });
  });

  return {
    processed_count: results.length,
    success_count: successCount,
    error_count: errorCount,
    results,
  };
}

// Response type for applying scorers to logs
export type ApplyScorersResponse =
  | {
      kind: "success";
      payload: {
        processed_count: number;
        success_count: number;
        error_count: number;
        results: unknown[];
      };
    }
  | {
      kind: "error";
      message: string;
    };

export const MAX_LOGS_SCORING_LIMIT = 50;

export async function applyScorersToLogs({
  apiUrl,
  sessionToken,
  proxyUrl,
  orgName,
  projectId,
  scorers,
  btqlFilter,
  logIds,
  limit = MAX_LOGS_SCORING_LIMIT,
}: {
  apiUrl: string | undefined;
  sessionToken: LoadedBtSessionToken;
  proxyUrl: string;
  orgName: string;
  projectId: string;
  scorers: SavedScorer[];
  btqlFilter?: string;
  logIds?: string[];
  limit?: number;
}): Promise<ApplyScorersResponse> {
  if (!apiUrl) {
    return {
      kind: "error",
      message: "Not logged in (yet)",
    };
  }
  if (!scorers || scorers.length === 0) {
    return {
      kind: "error",
      message:
        "No scorers provided. Please select at least one scorer to apply.",
    };
  }

  // Convert logIds to BTQL filter if provided
  let finalBtqlFilter: string;
  try {
    finalBtqlFilter = buildBtqlFilter(btqlFilter, logIds);
  } catch (error) {
    return {
      kind: "error",
      message: error instanceof Error ? error.message : "Invalid log ID format",
    };
  }

  try {
    // Query logs using BTQL
    const logs = await queryLogs({
      apiUrl,
      sessionToken,
      projectId,
      btqlFilter: finalBtqlFilter,
      limit,
      applyToRootSpan: true,
    });

    if (logs.length === 0) {
      return {
        kind: "error",
        message:
          "No matching rows found for the provided filters. Adjust the filter to apply scorers again.",
      };
    }

    // Apply scorers to each log row
    const results = await Promise.all(
      logs.map(async (row) => {
        const rowResults = await Promise.all(
          scorers.map((scorer) =>
            invokeScorerForRow({
              sessionToken,
              proxyUrl,
              orgName,
              scorer,
              row,
            }),
          ),
        );

        return {
          row,
          results: rowResults,
        };
      }),
    );

    // Aggregate results
    const aggregatedResults = aggregateScoringResults(results);

    return {
      kind: "success",
      payload: aggregatedResults,
    };
  } catch (error) {
    return {
      kind: "error",
      message:
        error instanceof Error ? error.message : "Failed to apply scorers",
    };
  }
}

export type AutomationTestResponse =
  | { kind: "success"; payload: Record<string, unknown> }
  | { kind: "error"; message: string };

export async function performAutomationTest({
  apiUrl,
  sessionToken,
  projectId,
  row,
  errorContext = "automation",
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  projectId: string;
  row: Record<string, unknown>;
  errorContext?: string;
}): Promise<AutomationTestResponse> {
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const resp = await fetch(`${apiUrl}/test-automation`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    body: JSON.stringify({
      project_id: projectId,
      ...row,
    }),
    ...sessionExtraFetchProps,
  });

  if (!resp.ok) {
    const errorData = await resp.json().catch(() => ({}));
    return {
      kind: "error",
      message:
        errorData.message ||
        `Error while testing ${errorContext}: HTTP ${resp.status}`,
    };
  }

  return await resp.json();
}
