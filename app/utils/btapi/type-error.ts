import { makeError } from "@braintrust/local";

// API Gateway times out requests after 30s.
const DATAPLANE_TIMEOUT = 30 * 1000;

export function prettifyDataplaneError({
  error,
  name,
  context,
  startTimeMs,
}: {
  error: unknown;
  name: string;
  context: Record<string, string>;
  startTimeMs: number;
}): Error {
  if (error instanceof TypeError) {
    // Type errors are mysterious and hard to understand (on "purpose"). So we
    // use the context we have available to make them more understandable.
    const duration = Date.now() - startTimeMs;
    let errorBase: string;
    if (duration >= DATAPLANE_TIMEOUT * 0.9) {
      errorBase = `${name} timed out after ${duration}ms.`;
    } else {
      errorBase = `${name} failed in ${duration}ms. Your browser returned an unhandled error. The console may contain more information.`;
    }
    const errorMessage =
      errorBase +
      (Object.keys(context).length > 0
        ? ` [${Object.entries(context)
            .map(([key, value]) => `${key}=${value}`)
            .join(", ")}]`
        : "");
    return makeError(new Error(errorMessage));
  } else {
    return makeError(error);
  }
}

export function withErrorTiming<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  F extends (...args: any[]) => Promise<any>,
>(f: F, name: string, context: Record<string, string>): F {
  const wrapped = async (...args: Parameters<F>) => {
    const startTimeMs = Date.now();
    try {
      return await f(...args);
    } catch (error) {
      throw prettifyDataplaneError({
        error,
        name,
        context,
        startTimeMs,
      });
    }
  };
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return wrapped as F;
}
