import { type AttachmentReference } from "@braintrust/typespecs";
import { BraintrustState, Attachment } from "braintrust";
import { type OrgContextT } from "#/utils/user-types";
import {
  sessionFetchProps,
  type LoadedBtSessionToken,
} from "#/utils/auth/session-token";

export async function uploadAttachment({
  file,
  sessionToken,
  org,
}: {
  file: File;
  sessionToken: LoadedBtSessionToken;
  org: OrgContextT;
}): Promise<AttachmentReference> {
  if (sessionToken === "unauthenticated") {
    throw new Error("You must be logged in to upload a file");
  }

  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const customFetch = (
    input: string | URL | globalThis.Request,
    init?: RequestInit,
  ): Promise<Response> =>
    fetch(input, {
      ...init,
      headers: {
        ...init?.headers,
        ...sessionHeaders,
      },
      ...sessionExtraFetchProps,
    });

  const loginData = {
    appUrl: window.location.host,
    appPublicUrl: window.location.host,
    orgName: org.name,
    apiUrl: org.api_url,
    proxyUrl: org.proxy_url,
    loginToken: sessionToken.btAuthToken,
    orgId: org.id,
  };
  const state = BraintrustState.deserialize(loginData, {
    fetch: customFetch,
  });
  const buffer = await file.arrayBuffer();
  const attachment = new Attachment({
    data: buffer,
    filename: file.name,
    contentType: file.type,
    state,
  });
  const uploadResult = await attachment.upload();
  if (uploadResult.upload_status === "error") {
    throw new Error(uploadResult.error_message);
  }
  return attachment.reference;
}
