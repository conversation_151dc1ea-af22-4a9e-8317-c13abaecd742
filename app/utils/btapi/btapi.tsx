import { useOrg } from "#/utils/user";
import { useEffect, useMemo, useRef, useState } from "react";
import { tableFromIPC, type Table, type Schema } from "apache-arrow";
import { useSetRefreshing } from "#/utils/refresh-data";
import { v4 as uuidv4 } from "uuid";
import { makeError } from "@braintrust/local";
import { sha1 } from "#/utils/hash";
import { type Filter } from "#/utils/search/simple-tree";
import { isEmpty, isObject } from "#/utils/object";
import { useFeatureFlags } from "#/lib/feature-flags";
import type {
  AliasExpr,
  Expr as ParsedExpr,
  Shape,
  SortExpr,
} from "@braintrust/btql/parser";
import { createArrowTableFromRecords } from "#/utils/arrow";
import { useSessionToken } from "#/utils/auth/session-token";
import { filterObject } from "@braintrust/local/query";
import { apiFetchBinary, apiFetchBtqlObject, extractPlan } from "./fetch";
import { dataObjectTypes, loadBtCacheDB } from "./load-bt-cache-db";
import { prettifyDataplaneError } from "./type-error";

export function newId() {
  return uuidv4();
}

export function parseArrowTable<SchemaData>(
  responseData: ArrayBuffer,
  parseData: (data: unknown) => { data: unknown[]; schema: SchemaData },
  schema: Schema | ((row: unknown, schema: SchemaData) => Schema),
) {
  let fullData = null;
  try {
    fullData = JSON.parse(new TextDecoder().decode(responseData));
  } catch (e) {
    // DEPRECATION_NOTICE: This is the legacy API, we should remove it once all endpoints are updated
    // https://linear.app/braintrustdata/issue/BRA-745
    // Old APIs will still return arrow data directly.
    return tableFromIPC(responseData);
  }

  const { data, schema: schemaData } = parseData(fullData);

  const _schema =
    typeof schema === "function" ? schema(data && data[0], schemaData) : schema;

  if (isObject(data) && "Code" in data && "Message" in data) {
    throw new Error(`API error: ${data.Code} - ${data.Message}`);
  }
  return createArrowTableFromRecords(data, _schema);
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function defaultParseData(data: any) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return { data, schema: null as any };
}

export function useArrowAPI<SchemaData = unknown>({
  endpoint,
  params,
  schema,
  name,
  parseData = defaultParseData,
}: {
  endpoint: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  params: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  schema: Schema | ((row: any, schema: SchemaData) => Schema);
  name: string;
  parseData?: (data: unknown) => { data: unknown[]; schema: SchemaData };
}) {
  const org = useOrg();

  const [data, setData] = useState<Table | null>(null);
  const [error, setError] = useState<Error | undefined>(undefined);
  const [loading, setLoading] = useState(false);

  const { getOrRefreshToken } = useSessionToken();
  const url = `${org.api_url}/${endpoint}`;

  const { startRefreshing, stopRefreshing } = useSetRefreshing();

  useEffect(() => {
    (async () => {
      if (data !== null) {
        return;
      }

      let btCacheDB = null;
      try {
        btCacheDB = await loadBtCacheDB();
      } catch (e) {
        console.error("Failed to open data cache", e);
        return;
      }

      const endpointStore = btCacheDB
        .transaction("arrowAPI", "readonly")
        .objectStore("arrowAPI");

      const request = endpointStore.get(
        JSON.stringify({ orgId: org.id, endpoint, params }),
      );
      request.onsuccess = function (event) {
        if (request.result) {
          setData(parseArrowTable(request.result, parseData, schema));
          setError(undefined);
        }
      };
    })();
  }, [data, endpoint, org.id, params, parseData, schema]);

  // Because apiFetchBinary maps to two requests under the hood, you might run this
  // effect twice in parallel (e.g. the first request fails its first fetch, then an
  // effect dependency changes, and another one starts and finishes, and then the first
  // effect's second result comes back.
  const ticket = useRef<number>(0);

  useEffect(() => {
    (async () => {
      if (endpoint && url) {
        startRefreshing();
        setLoading(true);

        let btCacheDB = null;
        try {
          btCacheDB = await loadBtCacheDB();
        } catch (e) {
          // Assume the error is logged in the other effect
        }

        const myTicket = ++ticket.current;
        const idToken = await getOrRefreshToken();
        const startTimeMs = Date.now();
        try {
          const response = await apiFetchBinary(url, idToken, params);
          if (response.status !== 200) {
            throw new Error(await response.text());
          }
          if (response.headers.get("x-bt-query-plan")) {
            console.log(extractPlan(response));
          }

          if (myTicket !== ticket.current) {
            return;
          }

          const responseData = await response.arrayBuffer();

          const arrowTable = parseArrowTable(responseData, parseData, schema);

          const endpointStore =
            btCacheDB &&
            btCacheDB
              .transaction(["arrowAPI"], "readwrite")
              .objectStore("arrowAPI");
          if (endpointStore) {
            endpointStore.put(
              responseData,
              JSON.stringify({ orgId: org.id, endpoint, params }),
            );
          }
          setData(arrowTable);
          setError(undefined);
        } catch (error) {
          if (myTicket !== ticket.current) {
            return;
          }
          if (
            error instanceof Error &&
            error.message.includes("Query too costly")
          ) {
            console.warn(
              "Certain UI features will be disabled (e.g. charts) because you do not have Brainstore running.",
            );
          } else {
            console.error(error);
          }
          setError(
            prettifyDataplaneError({
              error,
              startTimeMs,
              name,
              context: {
                endpoint,
                request_type: "binary",
              },
            }),
          );
        } finally {
          if (myTicket === ticket.current) {
            setLoading(false);
          }
          stopRefreshing();
        }
      }
    })();
  }, [
    url,
    endpoint,
    params,
    startRefreshing,
    stopRefreshing,
    schema,
    parseData,
    org.id,
    getOrRefreshToken,
    name,
  ]);

  return { data, error, loading };
}

export type DataObjectType = (typeof dataObjectTypes)[number];
export function isDataObjectType(
  objectType: string,
): objectType is DataObjectType {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return (dataObjectTypes as readonly string[]).includes(objectType);
}

export interface DataObjectSearch {
  id: string;

  max_xact_id?: string;
  max_root_span_id?: string;
  limit?: number;
  preview_length?: number;
  custom_columns?: AliasExpr[];

  filters?: {
    sql: Filter[];
    btql: ParsedExpr[];
  };
  sort?: SortExpr[];

  btqlCursor?: string;
  btqlRelaxedSearchMode?: boolean;
  replace?: Record<string, ParsedExpr>;
  shape?: Shape;

  audit_log?: boolean;
  version?: string;

  // For now not used in the API, but used to generate a unique search key
  objectType?: DataObjectType;
}

// The search key is a string or search for backwards compatibility -- there are a few components
// that assume that the file key == the object id, and this protects against that.
export function buildSearchKey(
  search:
    | string
    | DataObjectSearch
    | { key: string; search: DataObjectSearch }
    | null
    | undefined,
) {
  if (typeof search === "string") {
    return search;
  } else if (search && typeof search === "object" && "key" in search) {
    return search.key;
  } else {
    /* Coalesce undefined and null into null */
    return sha1(JSON.stringify(search || null));
  }
}

export function getIdAndParams(
  search: string | DataObjectSearch | { key: string; search: DataObjectSearch },
) {
  if (typeof search === "string") {
    return { id: search, params: undefined };
  } else if (search && typeof search === "object" && "search" in search) {
    const { id, audit_log, ...params } = search.search;
    return { id, audit_log, params };
  } else {
    const { id, audit_log, ...params } = search;
    return { id, audit_log, params };
  }
}

export function useDataObject({
  objectType,
  search,
  disableCache,
}: {
  objectType: DataObjectType;
  search:
    | string
    | DataObjectSearch
    | { key: string; search: DataObjectSearch }
    | null
    | undefined;
  disableCache?: boolean;
}) {
  const searches = useMemo(() => (search ? [search] : []), [search]);
  const {
    data: multiData,
    error,
    refreshed,
    dirty,
  } = useDataObjects({ objectType, searches, disableCache });

  const searchKey = useMemo(() => buildSearchKey(search), [search]);

  const [isLoadingInitial, setIsLoadingInitial] = useState(true);
  const isDirty = useMemo(
    () => dirty[searchKey] ?? undefined,
    [dirty, searchKey],
  );

  useEffect(() => {
    if (isDirty) return;
    setIsLoadingInitial(false);
  }, [isDirty]);

  return {
    data: search ? multiData[searchKey] : null,
    error,
    refreshed: search ? refreshed[searchKey] : 0,
    isDirty,
    isLoadingInitial,
  };
}

// DEPRECATION_NOTICE: We should remove this once all clients have upgraded to include
// the audit log. We can also remove the isAudit map below.
export let IS_AUDIT_LOG_AVAILABLE = true;

export type APIVersion = 1 | 2;

export interface VersionedResponse {
  response: Response;
  cursor?: string;
  api_version: APIVersion;
  plan?: string;
}

export interface VersionedData {
  buf: ArrayBuffer;
  api_version: APIVersion;
  cursor?: string;
  objectType: DataObjectType;
}

type DataObjectsState = {
  data: Record<string, VersionedData>;
  error: Error | undefined;
  refreshed: Record<string, number>;
  // A row is considered dirty when it's loaded from the cache before we get the API response.
  dirty: Record<string, boolean>;
};

export function useDataObjects({
  objectType,
  searches,
  disableCache: disableCacheProp,
}: {
  objectType: DataObjectType;
  searches: (
    | string
    | DataObjectSearch
    | { key: string; search: DataObjectSearch }
  )[];
  disableCache?: boolean;
}) {
  const { getOrRefreshToken } = useSessionToken();

  const org = useOrg();
  const apiUrl = org.api_url;

  const [state, setState] = useState<DataObjectsState>({
    data: {},
    error: undefined,
    refreshed: {},
    dirty: {},
  });
  const downloadedObjects = useRef<Record<string, boolean>>({});
  const { startRefreshing, stopRefreshing } = useSetRefreshing();

  const {
    flags: {
      brainstore,
      brainstore_realtime,
      brainstore_skip_backfill_check,
      columnstoreSearch,
      disableColumnstoreSearch,
      btqlMatchFilters,
      disableObjectCache,
      queryDiagnostics,
    },
  } = useFeatureFlags();

  const disableCache = disableCacheProp || disableObjectCache;

  useEffect(() => {
    (async () => {
      if (disableCache) {
        return;
      }
      let btCacheDB = null;
      try {
        btCacheDB = await loadBtCacheDB();
      } catch (e) {
        console.error("Failed to open data cache", e);
        return;
      }

      const objectStore = btCacheDB
        .transaction([objectType], "readonly")
        .objectStore(objectType);

      for (const search of searches) {
        const key =
          typeof search === "object" && "key" in search
            ? search.key
            : buildSearchKey(search);
        if (state.data[key]) {
          continue;
        }

        const request = objectStore.get(key);
        request.onsuccess = function () {
          if (request.result) {
            setState((state) => ({
              ...state,
              data: {
                // The cache should _not_ overwrite data we obtained through the API.
                [key]: request.result,
                ...state.data,
              },
              refreshed: {
                [key]: (state.refreshed[key] || 0) + 1,
                ...state.refreshed,
              },
              dirty: {
                [key]: true,
                ...state.dirty,
              },
            }));
          }
        };
        request.onerror = function (e) {
          console.error("Failed to get data object from cache", e);
        };
      }
    })();
  }, [disableCache, objectType, searches, state.data]);

  useEffect(() => {
    (async () => {
      const sessionToken = await getOrRefreshToken();

      let btCacheDB = null;
      try {
        btCacheDB = await loadBtCacheDB();
      } catch (e) {
        // Assume the error is logged in the other effect
      }

      const newObjectPromises: Record<string, Promise<VersionedResponse>> = {};
      const isAudit: Record<string, boolean> = {};
      for (const search of searches) {
        const key =
          typeof search === "object" && "key" in search
            ? search.key
            : buildSearchKey(search);
        if (!downloadedObjects.current[key]) {
          downloadedObjects.current[key] = true;

          const s =
            typeof search === "object" && "search" in search
              ? search.search
              : search;
          const { id, audit_log, params } = getIdAndParams(s);

          newObjectPromises[key] = apiFetchBtqlObject({
            apiUrl,
            objectType,
            objectId: id,
            audit_log,
            sessionToken,
            use_brainstore: brainstore,
            brainstore_realtime,
            brainstore_skip_backfill_check,
            use_columnstore:
              columnstoreSearch &&
              !isEmpty(params?.filters?.btql) &&
              params.filters.btql.length > 0
                ? true
                : disableColumnstoreSearch
                  ? false
                  : undefined,
            use_match_search_index: btqlMatchFilters,
            filters: params?.filters?.btql,
            replace: params?.replace,
            shape: params?.shape,
            cursor: params?.btqlCursor,
            limit: params?.limit,
            version: params?.version,
            relaxed_search_mode: params?.btqlRelaxedSearchMode,
            preview_length: params?.preview_length,
            custom_columns: params?.custom_columns,
            include_plan: queryDiagnostics,
          });

          isAudit[key] = !!audit_log;
        }
      }
      if (Object.keys(newObjectPromises).length === 0) {
        setState((state) => ({ ...state, error: undefined }));
        return;
      }

      startRefreshing();

      const newObjects: Record<string, VersionedData> = {};
      const deltaRefreshed: Record<string, number> = {};
      for (const [key, versionedResponseP] of Object.entries(
        newObjectPromises,
      )) {
        let versionedResponse = null;
        try {
          versionedResponse = await versionedResponseP;
        } catch (error) {
          if (isAudit[key]) {
            console.warn(
              "Audit log functionality not available in your API service. Please upgrade to enable it.",
            );
            IS_AUDIT_LOG_AVAILABLE = false;
          }
          continue;
        }

        if (versionedResponse.response.status !== 200) {
          const error = new Error(await versionedResponse.response.text());
          setState((state) => ({ ...state, error }));
          downloadedObjects.current[key] = false;
          stopRefreshing();
          return;
        }

        if (versionedResponse.plan) {
          console.log(versionedResponse.plan);
        }

        try {
          const buf = await versionedResponse.response.arrayBuffer();
          newObjects[key] = {
            buf,
            cursor: versionedResponse.cursor,
            api_version: versionedResponse.api_version,
            objectType,
          };
          deltaRefreshed[key] = 1;

          if (!disableCache) {
            const objectStore =
              btCacheDB &&
              btCacheDB
                .transaction([objectType], "readwrite")
                .objectStore(objectType);
            if (objectStore) {
              objectStore.put(newObjects[key], key);
            }
          }
        } catch (error) {
          setState((state) => ({
            ...state,
            error: makeError(error),
          }));
          downloadedObjects.current[key] = false;
          stopRefreshing();
          return;
        }
      }

      stopRefreshing();
      setState((state) => ({
        data: {
          ...state.data,
          ...newObjects,
        },
        refreshed: {
          // Here, we use a callback to make sure we're updating the latest
          // counters, and initialize the new counters to the delta (1) if they
          // do not exist, but update them if they do.
          ...deltaRefreshed,
          ...Object.fromEntries(
            Object.entries(state.refreshed).map(([k, v]) => [
              k,
              v + (deltaRefreshed[k] ?? 0),
            ]),
          ),
        },
        dirty: {
          ...state.dirty,
          ...Object.fromEntries(Object.keys(newObjects).map((k) => [k, false])),
        },
        error: undefined,
      }));
    })();
  }, [
    searches,
    startRefreshing,
    stopRefreshing,
    objectType,
    apiUrl,
    disableCache,
    columnstoreSearch,
    btqlMatchFilters,
    disableColumnstoreSearch,
    brainstore,
    brainstore_realtime,
    brainstore_skip_backfill_check,
    queryDiagnostics,
    getOrRefreshToken,
  ]);

  // Filter out stale keys from previous searches. Technically this isn't necessary
  // because we also apply search filters on the frontend, but it's better not to keep
  // extraneous rows around.
  return useMemo(() => {
    const searchKeys = searches.map(buildSearchKey);
    const keyFilter = (k: string) => searchKeys.includes(k);
    return {
      data: filterObject(state.data, keyFilter),
      error: state.error,
      refreshed: filterObject(state.refreshed, keyFilter),
      dirty: filterObject(state.dirty, keyFilter),
    };
  }, [searches, state]);
}
