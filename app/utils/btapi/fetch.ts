import {
  isAuthenticatedSession,
  sessionFetchProps,
} from "#/utils/auth/session-token";
import * as semver from "semver";
import type { LoadedBtSessionToken } from "#/utils/auth/session-token";
import { doubleQuote } from "#/utils/sql-utils";
import { batchItems, constructJsonArray, mergeRowBatch } from "braintrust/util";
import { makeSummaryBtqlReplace } from "@braintrust/local/query";
import type { DataObjectType } from "./btapi";
import type {
  AliasExpr,
  Expr as ParsedExpr,
  ParsedQ<PERSON>y,
  Shape,
} from "@braintrust/btql/parser";
import * as Query from "#/utils/btql/query-builder";
import type { VersionedResponse } from "./btapi";

import { BTQL_API_VERSION } from "#/utils/btql/constants";

// DEPRECATION_NOTICE: This basically tells the backend not to use the
// old "output" field for datasets in the API. We can remove it once we
// know that everyone is sing a sufficiently new version of the API.
const LATEST_API_VERSION = 2;

async function fallbackFetch(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  fns: ((...args: any[]) => Promise<Response>)[],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  ...args: any[]
) {
  let lastError = null;
  let lastResponse = null;
  for (const fn of fns) {
    try {
      lastResponse = await fn(...args);
      if (lastResponse.ok || lastResponse.status === 400) {
        return lastResponse;
      }
      lastError = null;
    } catch (e) {
      lastError = e;
    }
  }

  if (lastError) {
    throw lastError;
  } else {
    return lastResponse!;
  }
}

export async function apiFetchGetCors(
  url: string,
  sessionToken: LoadedBtSessionToken,
  signal?: AbortSignal,
): Promise<Response> {
  // Default options are marked with *

  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const response = await fetch(url, {
    method: "GET", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    ...sessionExtraFetchProps,
    signal,
  });
  return response; // parses JSON response into native JavaScript objects
}

async function apiFetchGetNew(
  url: string,
  sessionToken: LoadedBtSessionToken,
  signal?: AbortSignal,
): Promise<Response> {
  // Add token to the url parameters
  const urlObj = new URL(url);

  if (isAuthenticatedSession(sessionToken) && sessionToken.bearerToken) {
    throw new Error("Cannot use 'new' requests with custom bearer tokens");
  }

  urlObj.searchParams.append(
    "token",
    isAuthenticatedSession(sessionToken) ? sessionToken.btAuthToken : "",
  );

  const response = await fetch(urlObj.toString(), {
    method: "GET", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "text/plain",
    },
    signal,
  });
  return response; // parses JSON response into native JavaScript objects
}

export async function apiFetchGet(
  url: string,
  sessionToken: LoadedBtSessionToken,
  signal?: AbortSignal,
): Promise<Response> {
  return fallbackFetch(
    [apiFetchGetNew, apiFetchGetCors],
    url,
    sessionToken,
    signal,
  );
}

export async function apiPostCors({
  url,
  sessionToken,
  payload,
  alreadySerialized,
  headers,
}: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  payload: unknown;
  alreadySerialized: boolean;
  headers?: Record<string, string>;
}): Promise<Response> {
  if (alreadySerialized && typeof payload !== "string") {
    throw new Error("Payload is not a string");
  }
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const response = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "application/json",
      ...headers,
      ...sessionHeaders,
    },
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    body: alreadySerialized ? (payload as string) : JSON.stringify(payload),
    ...sessionExtraFetchProps,
  });
  return response; // parses JSON response into native JavaScript objects
}

async function apiPostNew({
  url,
  sessionToken,
  payload,
  alreadySerialized,
}: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  payload: unknown;
  alreadySerialized: boolean;
}): Promise<Response> {
  if (alreadySerialized && typeof payload !== "string") {
    throw new Error("Payload is not a string");
  }
  if (isAuthenticatedSession(sessionToken) && sessionToken.bearerToken) {
    throw new Error("Cannot use 'new' requests with custom bearer tokens");
  }

  const body = alreadySerialized
    ? `{"token": ${
        isAuthenticatedSession(sessionToken)
          ? doubleQuote(sessionToken.btAuthToken)
          : "null"
      }, "data": ${payload}}`
    : JSON.stringify({
        token: isAuthenticatedSession(sessionToken)
          ? sessionToken.btAuthToken
          : null,
        data: payload,
      });

  const response = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "text/plain",
    },
    body,
  });
  return response; // parses JSON response into native JavaScript objects
}

export async function apiPost({
  url,
  sessionToken,
  payload,
  alreadySerialized,
}: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  payload: unknown;
  alreadySerialized: boolean;
}): Promise<Response> {
  return fallbackFetch([apiPostNew, apiPostCors], {
    url,
    sessionToken,
    payload,
    alreadySerialized,
  });
}

async function apiFetch({
  url,
  sessionToken,
  method,
  payload,
  alreadySerialized,
}: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  method: "PATCH" | "DELETE" | "PUT";
  payload: unknown;
  alreadySerialized: boolean;
}): Promise<Response> {
  if (alreadySerialized && typeof payload !== "string") {
    throw new Error("Payload is not a string");
  }
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const response = await fetch(url, {
    method,
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    body: alreadySerialized ? (payload as string) : JSON.stringify(payload),
    ...sessionExtraFetchProps,
  });
  return response; // parses JSON response into native JavaScript objects
}

export async function apiPatch(args: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  payload: unknown;
  alreadySerialized: boolean;
}): Promise<Response> {
  return apiFetch({ ...args, method: "PATCH" });
}

export async function apiDelete(args: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  payload: unknown;
  alreadySerialized: boolean;
}): Promise<Response> {
  return apiFetch({ ...args, method: "DELETE" });
}

export async function apiPut(args: {
  url: string;
  sessionToken: LoadedBtSessionToken;
  payload: unknown;
  alreadySerialized: boolean;
}): Promise<Response> {
  return apiFetch({ ...args, method: "PUT" });
}

// NOTE: We may want to make these parameters configurable, in case you deploy to an API with different limits.
const BATCH_SIZE = 100; // Same ask the SDK
const MAX_REQUEST_SIZE = 6 * 1024 * 1024; // 6 MB for the AWS lambda gateway (from our own testing).

export async function apiPostObject({
  apiUrl,
  sessionToken,
  data,
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any[];
}): Promise<Response[]> {
  // Perform the same merging/batching that we do in the SDK.
  const allItems = mergeRowBatch(data);
  const allItemsStr = allItems.map((bucket) =>
    bucket.map((item) => JSON.stringify(item)),
  );
  const batchSets = batchItems({
    items: allItemsStr,
    batchMaxNumItems: BATCH_SIZE,
    batchMaxNumBytes: MAX_REQUEST_SIZE / 2,
  });

  const allResponses: Response[] = [];
  for (const batchSet of batchSets) {
    const batchResponses: Promise<Response>[] = [];
    for (const batch of batchSet) {
      batchResponses.push(
        (async () => {
          const payload = `{
            "rows": ${constructJsonArray(batch)},
            "api_version": ${LATEST_API_VERSION}
          }`;
          return await apiPost({
            url: `${apiUrl}/logs3`,
            sessionToken,
            payload,
            alreadySerialized: true,
          });
        })(),
      );
    }
    allResponses.push(...(await Promise.all(batchResponses)));
  }
  return allResponses;
}

// DEPRECATION_NOTICE: This is the legacy API, we should remove it once all endpoints are updated
async function apiFetchBinaryCors(
  url: string,
  sessionToken: LoadedBtSessionToken,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  signal?: AbortSignal,
): Promise<Response> {
  // Default options are marked with *
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const response = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "application/json",
      Accept: "application/octet-stream",
      ...sessionHeaders,
    },
    body: JSON.stringify(data), // body data type must match "Content-Type" header
    ...sessionExtraFetchProps,
    signal,
  });
  return response; // parses JSON response into native JavaScript objects
}

async function apiFetchBinaryNew(
  url: string,
  sessionToken: LoadedBtSessionToken,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  signal?: AbortSignal,
): Promise<Response> {
  if (isAuthenticatedSession(sessionToken) && sessionToken.bearerToken) {
    throw new Error("Cannot use 'new' requests with custom bearer tokens");
  }
  const body = JSON.stringify({
    token: isAuthenticatedSession(sessionToken)
      ? sessionToken.btAuthToken
      : null,
    data,
  });
  // Default options are marked with *
  const response = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "text/plain",
      Accept: "application/octet-stream",
    },
    body,
    signal,
  });
  return response; // parses JSON response into native JavaScript objects
}

export async function apiFetchBinary(
  url: string,
  sessionToken: LoadedBtSessionToken,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  signal?: AbortSignal,
): Promise<Response> {
  return fallbackFetch(
    [apiFetchBinaryNew, apiFetchBinaryCors],
    url,
    sessionToken,
    data,
    signal,
  );
}

async function apiFetchJsonCors(
  url: string,
  sessionToken: LoadedBtSessionToken,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  signal?: AbortSignal,
): Promise<Response> {
  // Default options are marked with *
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const response = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "application/json",
      "Accept-Encoding": "gzip",
      ...sessionHeaders,
    },
    body: JSON.stringify(data), // body data type must match "Content-Type" header
    ...sessionExtraFetchProps,
    signal,
  });
  return response; // parses JSON response into native JavaScript objects
}

async function apiFetchJsonNew(
  url: string,
  sessionToken: LoadedBtSessionToken,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  signal?: AbortSignal,
): Promise<Response> {
  if (isAuthenticatedSession(sessionToken) && sessionToken.bearerToken) {
    throw new Error("Cannot use 'new' requests with custom bearer tokens");
  }
  const body = JSON.stringify({
    token: isAuthenticatedSession(sessionToken)
      ? sessionToken.btAuthToken
      : null,
    data,
  });
  // Default options are marked with *
  const response = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    headers: {
      "Content-Type": "text/plain",
      "Accept-Encoding": "gzip",
    },
    body,
    signal,
  });
  return response; // parses JSON response into native JavaScript objects
}

export async function apiFetchJson(
  url: string,
  sessionToken: LoadedBtSessionToken,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  signal?: AbortSignal,
): Promise<Response> {
  return fallbackFetch(
    [apiFetchJsonNew, apiFetchJsonCors],
    url,
    sessionToken,
    data,
    signal,
  );
}

// Preview mode is a way to fetch just the data needed for the tabular preview
// of an object, without fetching the full content of all spans (which are
// loaded lazily upon request). This significantly speeds up the load time for
// large objects.
//
// The main caveat is that UI-based filtering no longer works over the data,
// because the UI only has access to the preview fields. So any functionality
// that requires UI-based re-filtering will not work in preview mode:
// * "relaxed search mode", which might return rows that do not match from the backend
// * realtime, because we would need to re-run the filters in the UI on just
//   the preview data.
//
// We therefore:
// * Disable realtime if any filters are active.
// * Do not run plain filters in the UI (other than full text search, which we will be removing soon too)
export function makePreviewBtqlReplace({
  objectType,
  relaxedSearchMode,
  hasIsRootField,
  version,
}: {
  objectType: DataObjectType;
  relaxedSearchMode: boolean;
  hasIsRootField: boolean;
  version: string;
}): Record<string, ParsedExpr> {
  if (relaxedSearchMode || !hasIsRootField) {
    return {};
  }
  // There is an incredibly annoying bug in the old Clickhouse code that
  // causes it to crash when we alias the `metadata` field. This allows us
  // to avoid hitting that bug.
  const hasNewClickhouseCode = semver.gte(version, "0.0.62");
  return makeSummaryBtqlReplace(objectType, !hasNewClickhouseCode);
}

export async function apiFetchBtqlObject({
  apiUrl,
  sessionToken,

  objectType,
  objectId,

  cursor,
  filters,
  replace,
  shape,
  version,
  limit,
  sort,
  preview_length,
  audit_log,
  use_columnstore,
  use_match_search_index,
  relaxed_search_mode,
  custom_columns,

  use_brainstore,
  brainstore_realtime,
  brainstore_skip_backfill_check,
  include_plan,

  fmt,
  select,
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;

  objectType: DataObjectType;
  objectId: string;

  cursor?: string;
  filters?: ParsedExpr[];
  replace?: Record<string, ParsedExpr>;
  shape?: Shape;
  version?: string;
  limit?: number;
  sort?: ParsedQuery["sort"];
  preview_length?: ParsedQuery["preview_length"];
  custom_columns?: AliasExpr[];

  audit_log: boolean | undefined;
  use_brainstore?: boolean;
  brainstore_realtime?: boolean;
  brainstore_skip_backfill_check?: boolean;
  use_columnstore?: boolean;
  use_match_search_index: boolean;
  relaxed_search_mode?: boolean;
  include_plan?: boolean;

  fmt?: string;
  select?: ParsedQuery["select"];
}): Promise<VersionedResponse> {
  const query: ParsedQuery = {
    from: Query.from(objectType, [objectId], shape),
    select: select ?? [{ op: "star", replace }],
    filter: Query.and(...(filters || [])),
    limit,
    cursor,
    sort,
    preview_length,
    custom_columns,
  };
  const response = await apiFetchBinary(`${apiUrl}/btql`, sessionToken, {
    query,
    use_columnstore,
    use_match_search_index,
    use_brainstore,
    brainstore_default_traces: true, // This is a back-compat flag that defaults to span->trace expansion
    brainstore_realtime,
    brainstore_skip_backfill_check,
    api_version: BTQL_API_VERSION,
    fmt: fmt ?? "parquet",
    disable_limit: true,
    audit_log,
    version,
    relaxed_search_mode,
    include_plan,
  });
  return {
    response,
    cursor: extractCursor(response) ?? undefined,
    api_version: LATEST_API_VERSION,
    plan: extractPlan(response),
  };
}

function extractCursor(response: Response) {
  return (
    response.headers.get("x-bt-cursor") ??
    response.headers.get("x-amz-meta-bt-cursor")
  );
}

export function extractPlan(response: Response) {
  const planBytes = response.headers.get("x-bt-query-plan") ?? undefined;
  if (!planBytes) {
    return undefined;
  }
  return Buffer.from(planBytes, "base64").toString("utf-8");
}
