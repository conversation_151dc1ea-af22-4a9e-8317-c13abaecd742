//check if a string is a valid JSON string without using try/catch
export const isJSON = (input: string): boolean => {
  if (!isString(input)) return false;

  // Remove leading and trailing whitespace
  input = input.trim();

  // <PERSON>le escaped quotes and newlines
  input = input.replace(/\\n/g, "").replace(/\\"/g, '"');

  if (input.startsWith('"') && input.endsWith('"')) {
    return true; // Literal string JSON
  }

  input = input.replace(/\s/g, "").replace(/\n|\r/, "");

  if (/^\{(.*?)\}$/.test(input)) return /"(.*?)":(.*?)/g.test(input);

  if (/^\[(.*?)\]$/.test(input)) {
    return input
      .replace(/^\[/, "")
      .replace(/\]$/, "")
      .replace(/},{/g, "}\n{")
      .split(/\n/)
      .map((s) => {
        return isJSON(s);
      })
      .reduce(function (prev, curr) {
        return !!curr;
      });
  }

  return false;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const isString = (input: unknown): input is string => {
  return Object.prototype.toString.call(input) === "[object String]";
};
