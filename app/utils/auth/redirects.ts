export function signInPath(args?: { redirectPath?: string | null }) {
  const params = new URLSearchParams();
  if (args?.redirectPath) {
    params.set("redirect_url", args.redirectPath);
  }
  const paramString = params.toString();
  return `/signin${paramString ? `?${paramString}` : ""}`;
}

export function signUpPath(args?: { redirectPath?: string | null }) {
  const params = new URLSearchParams();
  if (args?.redirectPath) {
    params.set("redirect_url", args.redirectPath);
  }
  const paramString = params.toString();
  return `/signup${paramString ? `?${paramString}` : ""}`;
}
