import { auth, currentUser, getAuth, clerkClient } from "@clerk/nextjs/server";
import { isEmpty } from "#/utils/object";
import { type NextApiRequest, type NextApiResponse } from "next";
import { auth as nextAuth } from "#/auth";
import { API_KEY_AUTH } from "./constants";
import { otelWrapTraced } from "#/utils/tracing";
import { isAdminEmail } from "./admins";

export type BtSession = {
  loggedIn: boolean;
  authId: string | null;
  authTokenProvider: string | null;

  // This session token is only set if using the legacy (fallback) API
  // key based auth, issued through next-auth.
  legacy_sessionToken: string | null;

  isAdmin: boolean;
};

export const getServerAuthSession = otelWrapTraced(
  "getServerAuthSession",
  async (args?: {
    req: NextApiRequest;
    res: NextApiResponse;
  }): Promise<BtSession> => {
    if (!API_KEY_AUTH) {
      const clerkSession = args?.req ? getAuth(args.req) : await auth();
      if (clerkSession.userId !== null) {
        let authId: string | null =
          clerkSession.sessionClaims.publicMetadata?.bt_auth_id ?? null;
        if (isEmpty(authId)) {
          // For a brief period of time after the user signs up for the first time,
          // their auth id is not set in Clerk. During that time, we query the user's
          // auth id from the backend API
          let userObject;
          if (args?.req) {
            const client = await clerkClient();
            userObject = await client.users.getUser(clerkSession.userId);
          } else {
            userObject = await currentUser();
          }
          authId =
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            (userObject?.publicMetadata.bt_auth_id as
              | string
              | null
              | undefined) ?? null;
        }

        const email = clerkSession.sessionClaims.primaryEmail ?? null;
        const customProvider = email?.endsWith("@netflix.com")
          ? "oauth_custom_netflix"
          : email?.endsWith("@netflixcontractors.com")
            ? "oauth_custom_netflix_contractors"
            : null;

        return {
          loggedIn: true,
          authId,
          authTokenProvider: customProvider,
          legacy_sessionToken: null,
          isAdmin: isAdminEmail(email ?? ""),
        };
      }
    }

    const nextAuthSession = await (args
      ? nextAuth(args.req, args.res)
      : nextAuth());
    if (!isEmpty(nextAuthSession?.accountId)) {
      return {
        loggedIn: nextAuthSession.loggedIn,
        authId: nextAuthSession.accountId,
        authTokenProvider: null,
        legacy_sessionToken:
          nextAuthSession.idToken ?? nextAuthSession.apiKey ?? null,
        isAdmin: isAdminEmail(nextAuthSession.user?.email ?? ""),
      };
    }

    return {
      loggedIn: false,
      authId: null,
      authTokenProvider: null,
      legacy_sessionToken: null,
      isAdmin: false,
    };
  },
);
