"use client";

import { useAuth, useClerk } from "@clerk/nextjs";
import { signOut as signOutNextAuth } from "next-auth/react";
import { useUser } from "#/utils/user";
import { useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";

export function useSignOutCallback() {
  const { signOut: signOutClerk } = useClerk();
  const { session } = useUser();
  const clerkAuth = useAuth();

  const queryClient = useQueryClient();

  const redirectUrl = session?.legacy_sessionToken
    ? "/auth/cognito-signout"
    : "/";

  return useCallback(async () => {
    window.analytics?.reset();
    await signOutNextAuth(
      clerkAuth.userId
        ? {
            redirect: false,
          }
        : {
            redirect: true,
            callbackUrl: redirectUrl,
          },
    );
    if (clerkAuth.userId) {
      await signOutClerk({ redirectUrl });
      queryClient.clear();
    }
  }, [redirectUrl, signOut<PERSON><PERSON><PERSON>, clerkAuth.userId, queryClient]);
}
