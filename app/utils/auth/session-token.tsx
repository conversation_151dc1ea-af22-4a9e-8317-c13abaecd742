"use client";

import { useAuth } from "@clerk/nextjs";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { isEmpty } from "#/utils/object";
import { useUser } from "#/utils/user";
import {
  ACCESS_TOKEN_REVALIDATION_WINDOW,
  accessTokenResponseSchema,
} from "./constants";
import useEvent from "react-use-event-hook";

// XXX Refactor with local
export const BRAINTRUST_AUTH_TOKEN_HEADER = "x-bt-auth-token";

// The token itself is configured ot have a 60 minute lifespan, so if we give
// ourselves 30 minutes of leeway, that should be enough time to get a new token.
const LEEWAY_IN_SECONDS = 30 * 60;
const REFRESH_INTERVAL = (LEEWAY_IN_SECONDS + 10) * 1000;

// Make sure the JWT we use to talk to the data plane lasts for at least 30 minutes,
// to be able to run long experiments.
const MIN_SERVER_TOKEN_AGE = 30 * 60 * 1000;

export interface BtSessionTokenValue {
  // This is the token that we issue, which is either:
  //  1) Issued by Clerk
  //  2) A Braintrust API key
  //  3) (Historically) issued by Cognito
  btAuthToken: string;

  // Certain orgs have custom requirements for the bearer token to pierce
  // through their firewall, so we allow that token to be set separately. If
  // this is specified, we set `x-bt-auth-token` to the braintrust token
  // and `Authorization: Bearer <bearer token>` to the one they use. Then, the
  // data plane uses the `x-bt-auth-token` to authenticate requests back to
  // the data plane.
  bearerToken: string | null;
}

export type BtSessionToken =
  | "loading"
  | "unauthenticated"
  | BtSessionTokenValue;

export type LoadedBtSessionToken = Exclude<BtSessionToken, "loading">;
export type FetchCachedBtSessionTokenFn = () => Promise<LoadedBtSessionToken>;

const BtSessionTokenContext = createContext<{
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
}>({
  getOrRefreshToken: () => Promise.resolve("unauthenticated"),
});

export function BtSessionTokenProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const sessionToken = useSessionTokenRoot();

  return (
    <BtSessionTokenContext.Provider value={sessionToken}>
      {children}
    </BtSessionTokenContext.Provider>
  );
}

export function useSessionToken(): {
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
} {
  return useContext(BtSessionTokenContext);
}

export function useSessionTokenRoot() {
  const auth = useAuth();
  const { session } = useUser();
  const sessionToken = useRef<BtSessionToken>("loading");
  const lastRefreshTime = useRef<number>(0);
  const wasOffline = useRef(false);

  // Clerk's `auth` object is not stable enough to use as a dependency itself, so
  // we memoize the fields we actually access to avoid unnecessary re-renders.
  const authGetToken = auth.getToken;

  const refreshToken = useEvent(
    async ({
      skipCache,
    }: {
      skipCache: boolean;
    }): Promise<LoadedBtSessionToken> => {
      if (!isEmpty(session?.legacy_sessionToken) && session.loggedIn) {
        return {
          btAuthToken: session.legacy_sessionToken,
          bearerToken: null,
        };
      }

      // This function caches the token, minus the leeway, so we don't have to keep making
      // network calls to get a new token.
      const sessionToken = await authGetToken({
        template: "data_plane",
        skipCache,
      });
      if (sessionToken === null) {
        return "unauthenticated";
      }

      let bearerToken = null;
      if (session?.authTokenProvider) {
        const response = await fetch(
          `/api/access-token?provider=${encodeURIComponent(session.authTokenProvider)}`,
          {
            next: {
              revalidate: ACCESS_TOKEN_REVALIDATION_WINDOW,
            },
          },
        );
        const data = accessTokenResponseSchema.parse(await response.json());
        bearerToken = data.accessToken;
      }
      return {
        btAuthToken: sessionToken,
        bearerToken,
      };
    },
  );

  const updateSessionToken = useCallback(
    async ({ skipCache }: { skipCache: boolean }) => {
      const t = await refreshToken({ skipCache });
      sessionToken.current = t;
      lastRefreshTime.current = Date.now();
      return t;
    },
    [refreshToken],
  );

  // Token refresh logic
  // 1. The token is refreshed via setTimeout every ~30 minutes
  // 2. If the tab is hidden for more than ~30 minutes or if the tab has been offline for more than ~30 minutes we refresh the token
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    const scheduleNextUpdate = () => {
      timeoutId = setTimeout(() => {
        updateSessionToken({ skipCache: true });
        scheduleNextUpdate();
      }, REFRESH_INTERVAL);
    };

    const refreshIfNecessary = () => {
      const currentTime = Date.now();
      const timeSinceLastRefresh = currentTime - lastRefreshTime.current;

      if (timeSinceLastRefresh >= REFRESH_INTERVAL) {
        updateSessionToken({ skipCache: true });
        clearTimeout(timeoutId);
        scheduleNextUpdate();
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshIfNecessary();
      }
    };

    const handleOffline = () => {
      wasOffline.current = true;
    };

    const handleOnline = () => {
      if (wasOffline.current) {
        wasOffline.current = false;
        refreshIfNecessary();
      }
    };

    // Initial token update and scheduling
    updateSessionToken({ skipCache: false });
    scheduleNextUpdate();

    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("offline", handleOffline);
    window.addEventListener("online", handleOnline);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("offline", handleOffline);
      window.removeEventListener("online", handleOnline);
    };
  }, [updateSessionToken]);

  const getOrRefreshToken =
    useCallback(async (): Promise<LoadedBtSessionToken> => {
      if (
        sessionToken.current === "loading" ||
        lastRefreshTime.current + MIN_SERVER_TOKEN_AGE < Date.now()
      ) {
        return await updateSessionToken({
          skipCache: sessionToken.current !== "loading",
        });
      }
      return Promise.resolve(sessionToken.current);
    }, [updateSessionToken, sessionToken]);

  return useMemo(() => ({ getOrRefreshToken }), [getOrRefreshToken]);
}

export function sessionFetchProps(sessionToken: LoadedBtSessionToken): {
  sessionHeaders: {
    [BRAINTRUST_AUTH_TOKEN_HEADER]?: string;
    Authorization?: string;
  };
  sessionExtraFetchProps: Record<string, string>;
} {
  const sessionExtraFetchProps = { credentials: "include" };
  if (sessionToken === "unauthenticated") {
    return {
      sessionHeaders: { Authorization: "Bearer null" },
      sessionExtraFetchProps,
    };
  } else if (sessionToken.bearerToken === null) {
    return {
      sessionHeaders: { Authorization: `Bearer ${sessionToken.btAuthToken}` },
      sessionExtraFetchProps,
    };
  } else {
    return {
      sessionHeaders: {
        Authorization: `Bearer ${sessionToken.bearerToken}`,
        [BRAINTRUST_AUTH_TOKEN_HEADER]: sessionToken.btAuthToken,
      },
      sessionExtraFetchProps,
    };
  }
}

export function isAuthenticatedSession(
  sessionToken: BtSessionToken,
): sessionToken is BtSessionTokenValue {
  return sessionToken !== "loading" && sessionToken !== "unauthenticated";
}
