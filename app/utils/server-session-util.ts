import { isEmpty } from "#/utils/object";
import { getServerAuthSession } from "./auth/server-session";
import { ANON_AUTH_ID, ANON_USER_ID } from "./constants";
import type { AuthLookup } from "./server-util";
import { lookupUserId } from "#/pages/api/_lookup_api_key";
import { otelWrapTraced } from "./tracing";

// Grab auth_id from the current server session.
export const getServerSessionAuthLookup = otelWrapTraced(
  "getServerSessionAuthLookup",
  async (): Promise<AuthLookup> => {
    const auth_id = (await getServerAuthSession()).authId;
    return isEmpty(auth_id)
      ? { auth_id: ANON_AUTH_ID, user_id: ANON_USER_ID, org_id: null }
      : { auth_id, user_id: await lookupUserId(auth_id), org_id: null };
  },
);
