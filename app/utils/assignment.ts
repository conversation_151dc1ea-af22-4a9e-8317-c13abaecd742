"use server";

import { resend } from "#/lib/resend";
import { createElement } from "react";

import AssignmentNotification from "#/ui/email-templates/user-assignment";
import { getFromAddress, getSubjectLine } from "#/utils/email/email-utils";
import { checkOrgUpdatePermission } from "#/utils/billing/utils";

export async function sendAssignmentNotification({
  orgId,
  orgName,
  assignerName,
  link,
  emails,
  projectName,
  entityType,
  entityName,
}: {
  orgId: string | undefined;
  orgName: string;
  assignerName: string;
  link: string;
  emails: string[];
  projectName?: string;
  entityType?: string | null;
  entityName?: string | null;
}): Promise<null> {
  if (!resend || emails.length === 0 || !orgId) {
    return null;
  }

  await checkOrgUpdatePermission(
    orgId,
    "Unauthorized to send assignment notifications.",
  );

  const subject = getSubjectLine(orgName, projectName, entityType, entityName);
  const fromAddress = getFromAddress();
  const batchEmails = emails.map((email) => ({
    from: fromAddress,
    to: email,
    subject,
    react: createElement(
      AssignmentNotification,
      {
        assignerName,
        projectName,
        entityType,
        entityName,
        link,
      },
      null,
    ),
  }));

  const resp = await resend.batch.send(batchEmails, {
    // Prevents duplicate emails from being sent. Resend will not send the email if the idempotencyKey has been used within the last 24 hours.
    idempotencyKey: `${emails.join("-")}-${assignerName}-${projectName}-${entityType}-${link}`,
  });
  if (resp.error) {
    throw new Error(resp.error.message);
  }

  return null;
}
