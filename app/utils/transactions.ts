/**
 * Implemented as a decoder from function defined here: api-ts/src/xact_id.ts.
 */

export function parseTransactionBigInt(xactIdStr: string): {
  date: Date;
  globalId: number;
} {
  // Parse the transaction ID string as a BigInt
  const xactId: bigint = BigInt(xactIdStr);
  const removedFlag: bigint = xactId & BigInt(0x0000ffffffffffff);

  // Extract the timestamp and global ID
  const timestamp: number = Number(
    (removedFlag >> BigInt(16)) & BigInt(0xffffffffffff),
  );
  const globalId: number = Number(removedFlag & BigInt(0xffff));
  // Convert the timestamp to a Date object
  const date: Date = new Date(timestamp * 1000); // JavaScript uses milliseconds

  // Return the result
  return { date, globalId };
}
