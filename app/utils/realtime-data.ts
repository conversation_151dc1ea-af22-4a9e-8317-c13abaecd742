import { useOrg, useUser } from "#/utils/user";
import {
  type OpenedRow,
  type OpenedRowMessage,
  realtimeMessageSchema,
  type JoinMessage,
  type UserInfo,
} from "@braintrust/realtime/types";
import { useEffect, useMemo, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { decryptMessage } from "@braintrust/proxy/utils";
import { type OrgContextT } from "./user-types";
import { _urljoin } from "braintrust/util";

export function useChannel({
  channelName,
  token,
  onData,
}: {
  channelName: string;
  token: string;
  onData: (data: unknown) => void;
}) {
  const currentWebSocket = useRef<RealtimeChannel | null>(null);
  const syncCounter = useRef(0);
  const sessionId = useMemo(() => uuidv4(), []);
  const [roster, setRoster] = useState<Roster>([]);
  const [ready] = useState(false);

  const user = useUser();
  const org = useOrg();

  useEffect(() => {
    if (!user.user) {
      return;
    }

    syncCounter.current++;

    const channel = new RealtimeChannel(
      channelName,
      token,
      {
        user_id: user.user.id,
        email: user.user.email,
        avatar_url: user.user.avatar_url,
      },
      org,
      () => {
        syncCounter.current -= 1;
        if (syncCounter.current > 0) {
          return false;
        } else {
          currentWebSocket.current = channel;
          return true;
        }
      },
      onData,
      setRoster,
    );

    return () => {
      currentWebSocket.current?.disconnect();
      currentWebSocket.current = null;
    };
  }, [channelName, onData, org, sessionId, token, user]);

  return {
    ready,
    roster,
  };
}

export type RosterMember = Omit<UserInfo, "id" | "row"> & {
  sessions: { id: string; row?: OpenedRow }[];
};
export type Roster = RosterMember[];
export type SetRosterFn = (roster: Roster) => void;

export class RealtimeChannel {
  id = uuidv4();
  private users: UserInfo[] = [];
  private ws: WebSocket | null = null;

  constructor(
    private channelName: string,
    private token: string,
    private user: Omit<UserInfo, "id"> | null,
    private org: OrgContextT,
    // Returns false if the channel should be disconnected.
    private onReady: () => boolean = () => true,
    private onData: (data: unknown) => void = () => {},
    private setRoster: SetRosterFn = () => {},
    private onRejoin?: () => void,
  ) {
    this.join();
  }

  public join() {
    const ws = new WebSocket(
      _urljoin(this.org.realtime_url, "channel", this.channelName, "websocket"),
    );
    this.ws = ws;
    let rejoined = false;
    const startTime = Date.now();

    const rejoin = async () => {
      if (!rejoined) {
        rejoined = true;

        // Clear the roster.
        this.users = [];

        // Don't try to reconnect too rapidly.
        const timeSinceLastJoin = Date.now() - startTime;
        if (timeSinceLastJoin < 1000) {
          // Less than 1 second elapsed since last join. Pause a bit.
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 - timeSinceLastJoin),
          );
        }

        // Sometimes, especially in Chrome, browsers will not connect to
        // the WSS URL until an ordinary XHR request is made against the
        // domain. So run a quick `fetch`
        await fetch(wssToHTTPUrl(this.org.realtime_url), {
          method: "GET",
        });

        // OK, reconnect now!
        this.join();
        this.onRejoin?.();
      }
    };

    ws.addEventListener("open", () => {
      if (!this.onReady()) {
        this.disconnect();
        return;
      }
      const message: JoinMessage = {
        type: "join",
        user: {
          id: this.id,
          user_id: this.id, // This will overridden if this.user is set.
          ...this.user,
        },
      };

      // Send user info message.
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    });

    ws.addEventListener("message", async (event) => {
      const message = realtimeMessageSchema.safeParse(JSON.parse(event.data));
      if (!message.success) {
        console.error("Invalid message received", message.error);
        return;
      }
      const data = message.data;
      switch (data.type) {
        case "join":
          console.warn("Unexpected join message");
          break;
        case "joined":
          const user = data.user;
          this.users = this.users.filter((u) => u.id !== user.id).concat(user);
          this.onRosterChange();
          break;
        case "quit":
          const id = data.id;
          this.users = this.users.filter((u) => u.id !== id);
          this.onRosterChange();
          break;
        case "ready":
          break;
        case "data":
          const msg = await decryptMessage(
            this.token,
            data.value.iv,
            data.value.data,
          );
          if (msg) {
            const payload = JSON.parse(msg);
            this.onData(payload);
          }
          break;
        case "openedRow":
          for (const user of this.users) {
            if (user.id === data.sessionId) {
              user.row = data.row;
            }
          }
          this.onRosterChange();
          break;
        default:
          const neverData: never = data;
          console.error("Unknown message type", neverData);
      }
    });

    ws.addEventListener("close", (event) => {
      console.log("WebSocket closed, reconnecting:", event.code, event.reason);
      rejoin();
    });
    ws.addEventListener("error", async (event) => {
      console.log("WebSocket error, reconnecting:", event);

      rejoin();
    });
  }

  public openRow(row: OpenedRow) {
    if (!this.ws) {
      return;
    }
    const message: OpenedRowMessage = {
      type: "openedRow",
      sessionId: this.id,
      row,
    };

    if (this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }

  private onRosterChange() {
    const roster: Record<string, RosterMember> = {};
    for (const user of this.users) {
      const existing = roster[user.user_id];
      if (existing) {
        existing.sessions.push({ id: user.id, row: user.row });
      } else {
        const { id: _id, ...rest } = user;
        roster[user.user_id] = {
          ...rest,
          sessions: [{ id: user.id, row: user.row }],
        };
      }
    }
    this.setRoster(Object.values(roster));
  }
}

function wssToHTTPUrl(wssUrl: string): string {
  return wssUrl.replace(/^wss:/, "https:").replace(/^ws:/, "http:");
}
