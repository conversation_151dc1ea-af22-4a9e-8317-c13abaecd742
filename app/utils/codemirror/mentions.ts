import {
  autocompletion,
  type Completion,
  type CompletionContext,
  type CompletionResult,
} from "@codemirror/autocomplete";
import {
  Prec,
  RangeSetBuilder,
  StateField,
  type Extension,
} from "@codemirror/state";
import { type FetchOrgUsersOutput } from "#/utils/org-users";
import {
  Decoration,
  type DecorationSet,
  EditorView,
  keymap,
  WidgetType,
} from "@codemirror/view";
import {
  mentionRegex,
  mentionRegexCaptureGlobal,
} from "#/utils/email/mentions-utils";

class MentionWidget extends WidgetType {
  constructor(
    private userName: string,
    private userId: string,
    private email: string,
  ) {
    super();
  }

  toDOM() {
    const span = document.createElement("span");
    span.className = "cm-mention";
    span.textContent = `@${this.userName}`;
    span.title = `${this.email}`;
    return span;
  }

  eq(other: MentionWidget) {
    return this.userName === other.userName && this.userId === other.userId;
  }
}

export function mentionsSupport(
  orgUsers: FetchOrgUsersOutput,
  onEscape: () => boolean,
  onInvite: (text: string) => void,
): Extension[] {
  const userOptions = Object.values(orgUsers || {}).reduce<Completion[]>(
    (acc, user) => {
      if (user.user_type === "service_account") {
        return acc;
      }
      const name =
        user.given_name && user.family_name
          ? `${user.given_name} ${user.family_name}`
          : (user.email ?? user.id);

      acc.push({
        label: name,
        detail: user.email ?? undefined,
        apply: `@${user.id} `,
        type: "user",
      });
      return acc;
    },
    [],
  );

  const mentionField = StateField.define<DecorationSet>({
    create() {
      return Decoration.none;
    },
    update(_decorations, tr) {
      const builder = new RangeSetBuilder<Decoration>();
      const text = tr.newDoc.toString();

      for (const match of text.matchAll(mentionRegexCaptureGlobal)) {
        const [fullMatch, userId] = match;
        const user = orgUsers?.[userId];

        if (user) {
          const userName =
            user.given_name && user.family_name
              ? `${user.given_name} ${user.family_name}`
              : (user.email ?? userId);

          const widget = new MentionWidget(userName, userId, user.email ?? "");
          builder.add(
            match.index,
            match.index + fullMatch.length,
            Decoration.replace({ widget }),
          );
        }
      }

      return builder.finish();
    },
    provide: (field) => EditorView.decorations.from(field),
  });

  return [
    Prec.high(
      keymap.of([
        {
          key: "Escape",
          run: onEscape,
        },
        {
          key: "Backspace",
          run: (view: EditorView) => {
            const { state } = view;
            const { selection } = state;
            const { main } = selection;

            if (!main.empty) {
              const selectedText = state.doc.sliceString(main.from, main.to);
              if (mentionRegex.test(selectedText)) {
                view.dispatch({
                  changes: { from: main.from, to: main.to, insert: "" },
                  selection: { anchor: main.from },
                });
                return true;
              }
              return false;
            }

            if (main.head > 0) {
              const doc = state.doc;
              const lineStart = doc.lineAt(main.head).from;
              const beforeCursor = doc.sliceString(lineStart, main.head);
              const mentionMatch = beforeCursor.match(/@([\w.-]+)$/);

              if (mentionMatch && orgUsers[mentionMatch[1]]) {
                const mentionStart = main.head - mentionMatch[0].length;
                const mentionEnd = main.head;

                view.dispatch({
                  selection: { anchor: mentionStart, head: mentionEnd },
                });
                return true;
              }
            }

            return false;
          },
        },
      ]),
    ),
    mentionField,
    autocompletion({
      activateOnTyping: true,
      override: [
        async (
          context: CompletionContext,
        ): Promise<CompletionResult | null> => {
          const mention = /@[a-zA-Z0-9-\s]*/;
          const fallbackWordMatch = context.matchBefore(mention);

          if (fallbackWordMatch && fallbackWordMatch.text.startsWith("@")) {
            if (
              !fallbackWordMatch ||
              (fallbackWordMatch.from === fallbackWordMatch.to &&
                !context.explicit)
            ) {
              return null;
            }

            const searchTerm = fallbackWordMatch.text
              .toLowerCase()
              .slice(1)
              .toLowerCase();

            const keywordSuggestions = userOptions.filter(
              (user) =>
                !!user.label.toLowerCase().includes(searchTerm) ||
                !!user.detail?.toLowerCase()?.includes(searchTerm),
            );

            if (keywordSuggestions.length === 0) {
              if (onInvite) {
                const inviteOption: Completion = {
                  label: `Invite \"${searchTerm}\"`,
                  detail: "Invite a member",
                  apply(view: EditorView) {
                    onInvite(searchTerm);
                  },
                  type: "class",
                };
                return {
                  from: fallbackWordMatch.from,
                  options: [inviteOption],
                  validFor: mentionRegex,
                  filter: false,
                };
              }
              return null;
            }

            return {
              from: fallbackWordMatch.from,
              options: keywordSuggestions,
              validFor: mentionRegex,
              filter: false,
            };
          }

          return null;
        },
      ],
    }),
  ];
}
