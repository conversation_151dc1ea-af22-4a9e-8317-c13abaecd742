import slugifyLib from "slugify";
import { v4 as uuidv4 } from "uuid";

export function slugify(
  s: string,
  options?: { lower?: boolean; strict?: boolean; unique?: boolean },
) {
  const { lower = true, strict = true, unique = true } = options || {};
  const suffix = unique ? "-" + uuidv4().slice(-4) : "";
  return slugifyLib(s, { lower, strict }) + suffix;
}

export function nextObject(
  objectName: string,
  numObjects: number,
  conflicts?: string[],
) {
  const conflictsSet = new Set(conflicts || []);
  for (let i = 0; i < 10; i++) {
    const name = `${objectName} ${numObjects + i + 1}`;
    if (!conflictsSet.has(name)) {
      return name;
    }
  }
  return `${objectName} ${uuidv4().slice(-4)}`;
}
