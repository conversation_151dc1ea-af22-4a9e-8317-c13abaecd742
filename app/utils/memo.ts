import { useEffect, useState, useRef } from "react";

/**
 * A React hook that memoizes a value based on its JSON string representation.
 * Only updates the memoized value when the JSON string of the new value differs
 * from the previous value. This is useful for preventing unnecessary re-renders
 * when dealing with objects/arrays that are semantically equal but referentially
 * different.
 *
 * @param value The value to memoize
 * @returns The memoized value
 */

export function useJSONMemo<T>(value: T, onSet?: (value: T) => void) {
  const [memoizedValue, setMemoizedValue] = useState<T>(value);
  const currMemoizedValueRef = useRef<T>(value);

  useEffect(() => {
    if (
      JSON.stringify(currMemoizedValueRef.current) !== JSON.stringify(value)
    ) {
      currMemoizedValueRef.current = value;
      setMemoizedValue(value);
      onSet?.(value);
    }
  }, [value, onSet]);

  return memoizedValue;
}
