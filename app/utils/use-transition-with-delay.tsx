import { useState, useEffect, useTransition } from "react";

export function useTransitionWithDelay(delay = 250) {
  const [isPending, startTransition] = useTransition();
  const [isDelayedPending, setDelayedPending] = useState(false);

  useEffect(() => {
    if (isPending) {
      const timer = setTimeout(() => {
        setDelayedPending(true);
      }, delay);
      return () => clearTimeout(timer);
    } else {
      setDelayedPending(false);
    }
  }, [isPending, delay]);

  return { isDelayedPending, isPending, startTransition };
}
