import { isObject } from "braintrust/util";

export {
  filterObject,
  mapObject,
  lexicographicalComparison,
} from "@braintrust/local/query";

export {
  isObject,
  isArray,
  isObjectOrArray,
  isEmpty,
  notEmpty,
  isNumber,
} from "braintrust/util";

import { deserializePlainStringAsJSON } from "braintrust";
export { deserializePlainStringAsJSON };

export function deserializeJSONStringAsString(s: string) {
  const deserialized = deserializePlainStringAsJSON(s).value;
  return typeof deserialized === "string"
    ? deserialized
    : JSON.stringify(deserialized, null, 2);
}

/**
 * @example
 * removeTrailingZeros("400.000") // "400"
 * removeTrailingZeros("-40.230") // "-40.23"
 * removeTrailingZeros("200")     // "200"
 * removeTrailingZeros("0")       // "0"
 */
export function removeTrailingZeros(s: string) {
  return s.replace(/(\.\d*?[1-9])0+$|\.0+$/, "$1");
}

// Deserialize a JSON object/array or convert to Number/Boolean if appropriate.
// Leave numbers as strings if they're out of the safe integer range.
export function safeDeserializePlainStringAsJSON(s: string) {
  const value = deserializePlainStringAsJSON(s).value;

  if (typeof value === "number" && Number.isInteger(value)) {
    // Check that the parsed integer is within the safe range and compare it
    // to the original string to ensure it hasn't been rounded or truncated.
    // If it's not a valid conversion, just return the original string.
    if (
      value > Number.MAX_SAFE_INTEGER ||
      value < Number.MIN_SAFE_INTEGER ||
      value.toString() !== removeTrailingZeros(s)
    ) {
      return s;
    }
  }

  return value;
}

export function safeDeserializeUnknown(s: unknown) {
  if (typeof s === "string") {
    return safeDeserializePlainStringAsJSON(s);
  }
  return s;
}

export function updatePathMut(
  row: Record<string, unknown>,
  path: string[],
  value: unknown,
  options?: {
    // if a value exists at the designated path, do not mutate it
    keepExistingValue?: boolean;
  },
) {
  if (path.length === 0) {
    return value;
  }
  let curr = row;
  for (let i = 0; i < path.length - 1; i++) {
    // If the current path segment doesn't exist, or exists but is not an object (and we need to traverse further)
    if (
      !isObject(curr) ||
      !(path[i] in curr) ||
      (!isObject(curr[path[i]]) && i < path.length - 1)
    ) {
      curr[path[i]] = {};
    }
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    curr = curr[path[i]] as Record<string, unknown>;
  }

  const lastKey = path[path.length - 1];
  // Only set the value if keepExistingValue is false or the value doesn't exist yet
  if (isObject(curr) && (!options?.keepExistingValue || !(lastKey in curr))) {
    curr[lastKey] = value;
  }

  return row;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
(BigInt.prototype as any).toJSON = function () {
  const strValue = this.toString();
  const numValue = parseInt(strValue);
  if (BigInt(numValue) === this) {
    return numValue;
  } else {
    return strValue;
  }
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function normalizeArrowForJSON(k: string, v: any) {
  if (ArrayBuffer.isView(v)) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    return Array.from(v as any);
  }

  return v;
}

// DEVNOTE: This function is (partially) duplicated in sdk/js/src/framework.ts and should be kept in sync
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function serializeJSONWithPlainString(v: any) {
  if (typeof v === "string") {
    return v;
  } else {
    return JSON.stringify(v, normalizeArrowForJSON, 2);
  }
}

// Returns true if two JSON-serializable objects (or undefined) are equal,
// recursing deeply into object-type values.
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function jsonSerializableDeepEqual(lhs: any, rhs: any): boolean {
  if (lhs === rhs) {
    return true;
  }
  if (lhs && rhs && typeof lhs === "object" && typeof rhs === "object") {
    const lhs_entries = Object.entries(lhs);
    if (lhs_entries.length !== Object.keys(rhs).length) {
      return false;
    }
    return lhs_entries.every(
      ([lhs_key, lhs_val]) =>
        lhs_key in rhs && jsonSerializableDeepEqual(lhs_val, rhs[lhs_key]),
    );
  }
  return false;
}

export function strMax(a: string, b: string) {
  return a.localeCompare(b) > 0 ? a : b;
}

/**
 * @returns a typed array of the Record's keys
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function getRecordKeys<T extends Record<string | number | symbol, any>>(
  obj: T,
): Array<keyof T> {
  return Object.keys(obj);
}
