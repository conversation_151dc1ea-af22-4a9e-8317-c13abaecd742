/* Tokenizer for mustache tag matching, taken from https://github.com/GrumpTech/lezer-mustache/blob/main/src/tokens.js */

import { ExternalTokenizer, ContextTracker, type InputStream } from "@lezer/lr";
import {
  Content,
  StartTag,
  StartSectionTag,
  StartCloseSectionTag,
  StartMismatchedCloseSectionTag,
  StartCommentTag,
  StartUnescapedTag,
  StartAmpersandTag,
  StartPartialTag,
  EndTag,
  EndUnescapedTag,
  TagKey,
  InvalidTagKey,
  Comment,
  SectionTag,
  MissingCloseSectionTag,
  DelimiterTag,
  InvalidDelimiterTag,
  Element,
  EndCommentTag,
} from "./parser.terms.js";

function tagKeyChar(ch: number) {
  return (
    ch === 45 ||
    ch === 46 ||
    (ch >= 48 && ch <= 57) ||
    (ch >= 65 && ch <= 90) ||
    ch === 95 ||
    (ch >= 97 && ch <= 122)
  );
}

function delimiterChar(ch: number) {
  return ch !== 32 && ch !== 61 && ch > 0;
}

let cachedKey: string | null = null;
let cachedInput: InputStream | null = null;
let cachedPos = 0;
function tagKeyAfter(input: InputStream, offset: number, token: string) {
  const pos = input.pos + offset;
  if (cachedInput === input && cachedPos === pos) return cachedKey;
  while (input.peek(offset) === 32 /* ' ' */) offset++;
  const first = token.charCodeAt(0);
  let key = "";
  for (;;) {
    const next = input.peek(offset);
    if (!tagKeyChar(next)) break;
    if (next === first) {
      let len = 1;
      for (let l = token.length; len < l; len++)
        if (input.peek(offset + len) !== token.charCodeAt(len)) break;
      if (len === token.length) break;
    }
    key += String.fromCharCode(next);
    offset++;
  }
  cachedInput = input;
  cachedPos = pos;
  return (cachedKey = key || null);
}

let cachedDelimiters: [string, string] | null = null;
let cachedDelimiterInput: InputStream | null = null;
let cachedDelimiterPos = 0;
function delimitersAfter(input: InputStream, offset: number) {
  const pos = input.pos + offset;
  if (cachedDelimiterInput === input && cachedDelimiterPos === pos)
    return cachedDelimiters;
  let delimiter = "";
  let startDelimiter = "";
  let endDelimiter = "";
  for (;;) {
    const next = input.peek(offset);
    if (!delimiterChar(next)) {
      if (next === 32 /* ' ' */ && startDelimiter === "") {
        startDelimiter = delimiter;
        delimiter = "";
        offset++;
        continue;
      } else if (next === 61 /* '=' */ && startDelimiter !== "") {
        endDelimiter = delimiter;
        break;
      } else if (next < 0) {
        return;
      }
    }
    delimiter += String.fromCharCode(next);
    offset++;
  }
  if (startDelimiter === "" || endDelimiter === "") return null;
  cachedDelimiterInput = input;
  cachedDelimiterPos = pos;
  return (cachedDelimiters = [startDelimiter, endDelimiter] || null);
}

class Section {
  constructor(
    public key: string,
    public parent: Section | null,
  ) {}
}

class MustacheContext {
  constructor(
    public section: Section | null,
    public delimiters: [string, string],
  ) {}
}

export const mustacheContext = new ContextTracker({
  start: new MustacheContext(null, ["{{", "}}"]),
  shift(context, term, stack, input) {
    return term === StartSectionTag
      ? new MustacheContext(
          new Section(
            tagKeyAfter(input, 3, context.delimiters[1]) || "",
            context.section,
          ),
          context.delimiters,
        )
      : term === DelimiterTag
        ? new MustacheContext(
            context.section,
            delimitersAfter(input, context.delimiters[1].length + 1) ??
              context.delimiters,
          )
        : context;
  },
  reduce(context, term) {
    return term === Element && context.section
      ? new MustacheContext(context.section?.parent, context.delimiters)
      : context;
  },
  reuse(context, node, _stack, input) {
    const type = node.type.id;
    return type === StartSectionTag || type === SectionTag
      ? new MustacheContext(
          new Section(
            tagKeyAfter(input, 3, context.delimiters[1]) || "",
            context.section,
          ),
          context.delimiters,
        )
      : context;
  },
  strict: false,
});

export const content = new ExternalTokenizer(
  (input, stack) => {
    if (scanToToken(input, stack.context.delimiters[0])) {
      input.acceptToken(Content);
    }
  },
  { contextual: true },
);

export const startTag = new ExternalTokenizer(
  (input, stack) => {
    const startDelimiter = stack.context.delimiters[0];
    if (scanToken(input, startDelimiter)) {
      const next = input.next;
      if (next === 47 /* '/' */) {
        input.advance();
        const key = tagKeyAfter(input, 0, startDelimiter);
        if (!key) return input.acceptToken(StartCloseSectionTag);
        if (stack.context && key === stack.context.section?.key)
          return input.acceptToken(StartCloseSectionTag);
        for (let s = stack.context.section; s; s = s.parent)
          if (s.key === key) {
            return input.acceptToken(
              MissingCloseSectionTag,
              -1 - startDelimiter.length,
            );
          }
        input.acceptToken(StartMismatchedCloseSectionTag);
      } else if (next === 35 /* '#' */ || next === 94 /* '^' */) {
        input.advance();
        input.acceptToken(StartSectionTag);
      } else if (next === 33 /* '!' */) {
        input.advance();
        input.acceptToken(StartCommentTag);
      } else if (next === 123 /* '{' */) {
        input.advance();
        input.acceptToken(StartUnescapedTag);
      } else if (next === 61 /* '=' */) {
        input.advance();
        input.acceptToken(
          scanDelimiterTag(input, stack.context.delimiters[1])
            ? DelimiterTag
            : InvalidDelimiterTag,
        );
      } else if (next === 38 /* '&' */) {
        input.advance();
        input.acceptToken(StartAmpersandTag);
      } else if (next === 62 /* '>' */) {
        input.advance();
        input.acceptToken(StartPartialTag);
      } else input.acceptToken(StartTag);
    }
  },
  { contextual: true },
);

export const tagKey = new ExternalTokenizer(
  (input, stack) => {
    if (input.next === 32 /* ' ' */) {
      while (input.next === 32 /* ' ' */) input.advance(); // skip all spaces
    }
    const key = tagKeyAfter(input, 0, stack.context.delimiters[1]);
    const len = key?.length ?? 0;

    if (len === 0) {
      input.acceptToken(InvalidTagKey);
      return;
    }

    for (let i = 0; i < len; i++) input.advance();
    if (
      len > 1 &&
      (key?.startsWith(".") || (key?.indexOf("..") && key.indexOf("..") >= 0))
    )
      input.acceptToken(InvalidTagKey);
    else if (len) input.acceptToken(TagKey);
  },
  { contextual: true },
);

export const comment = new ExternalTokenizer(
  (input, stack) => {
    if (scanToToken(input, stack.context.delimiters[1])) {
      input.acceptToken(Comment);
    }
  },
  { contextual: true },
);

export const endCommentTag = new ExternalTokenizer(
  (input, stack) => {
    if (scanToken(input, stack.context.delimiters[1])) {
      input.acceptToken(EndCommentTag);
    }
  },
  { contextual: true },
);

export const endTag = new ExternalTokenizer(
  (input, stack) => {
    if (scanToken(input, stack.context.delimiters[1]))
      input.acceptToken(EndTag);
  },
  { contextual: true },
);

export const endUnescapedTag = new ExternalTokenizer(
  (input, stack) => {
    if (scanToken(input, "}" + stack.context.delimiters[1]))
      input.acceptToken(EndUnescapedTag);
  },
  { contextual: true },
);

function scanToken(input: InputStream, token: string) {
  for (let i = 0, l = token.length; i < l; i++) {
    if (input.peek(i) !== token.charCodeAt(i)) return false;
  }
  for (let i = 0, l = token.length; i < l; i++) input.advance();
  return true;
}

function scanToToken(input: InputStream, token: string) {
  let len = 0;
  const first = token.charCodeAt(0);
  scan: for (; ; input.advance(), len++) {
    if (input.next < 0) break;
    if (input.next === first) {
      for (let i = 1; i < token.length; i++)
        if (input.peek(i) !== token.charCodeAt(i)) continue scan;
      break;
    }
  }
  return len > 0;
}

function scanDelimiterTag(input: InputStream, endToken: string) {
  if (input.next === 32 /* ' ' */) return false;
  while (delimiterChar(input.next)) input.advance();
  if (input.next === 32 /* ' ' */) input.advance();
  else return false;
  while (delimiterChar(input.next)) input.advance();
  input.advance();
  return scanToken(input, endToken);
}
