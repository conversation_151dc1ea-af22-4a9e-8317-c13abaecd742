@top Document { entity* }

entity {
  Content |
  Tag |
  PartialTag |
  CommentTag |
  UnescapedTag |
  DelimiterTag |
  InvalidDelimiterTag |
  MismatchedCloseSectionTag |
  Element
}

Element {
  SectionTag entity* (CloseSectionTag | MissingCloseSectionTag)
}

tag<type> { type (space* (TagKey | InvalidTagKey))? space* EndTag }

Tag { tag<StartTag> }
SectionTag[closedBy="CloseSectionTag"] { tag<StartSectionTag> }
CloseSectionTag[openedBy="SectionTag"] { tag<StartCloseSectionTag> }
PartialTag { tag<StartPartialTag> }
MismatchedCloseSectionTag { tag<StartMismatchedCloseSectionTag> }

UnescapedTag { tag<StartAmpersandTag> | (StartUnescapedTag (space* (TagKey | InvalidTagKey))? space* EndUnescapedTag) }
CommentTag { StartCommentTag Comment? EndCommentTag }

@context mustacheContext from "./tokens.ts"

@external tokens content from "./tokens.ts" {
  Content
}

@external tokens startTag from "./tokens.ts" {
  StartTag[closedBy="EndTag"]
  StartAmpersandTag[closedBy="EndTag"]
  StartPartialTag[closedBy="EndTag"]
  StartSectionTag[closedBy="EndTag"]
  StartCloseSectionTag[closedBy="EndTag"]
  StartMismatchedCloseSectionTag[closedBy="EndTag"]
  MissingCloseSectionTag
  StartCommentTag[closedBy="EndCommentTag"]
  StartUnescapedTag[closedBy="EndUnescapedTag"]
  DelimiterTag
  InvalidDelimiterTag
}

@external tokens tagKey from "./tokens.ts" {
  TagKey
  InvalidTagKey
}

@external tokens comment from "./tokens.ts" {
  Comment
}

@external tokens endTag from "./tokens.ts" {
  EndTag[openedBy="StartTag StartAmpersandTag StartPartialTag StartSectionTag StartCloseSectionTag StartMismatchedCloseSectionTag StartCommentTag"]
}

@external tokens endCommentTag from "./tokens.ts" {
  EndCommentTag[openedBy="StartCommentTag"]
}

@external tokens endUnescapedTag from "./tokens.ts" {
  EndUnescapedTag[openedBy="StartUnescapedTag"]
}

@tokens {
  space { @whitespace+ }
}

@external propSource mustacheHighlighting from "./highlight"

@detectDelim
