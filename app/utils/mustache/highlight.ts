import { styleTags, tags as t } from "@lezer/highlight";

export const mustacheHighlighting = styleTags({
  "StartTag StartSectionTag StartCloseSectionTag MismatchedStartCloseSectionTag StartPartialTag StartUnescapedTag StartAmpersandTag EndTag EndUnescapedTag DelimiterTag InvalidDelimiterTag":
    t.definition(t.propertyName),
  "StartCommentTag Comment EndCommentTag": t.lineComment,
  TagKey: t.definition(t.propertyName),
});
