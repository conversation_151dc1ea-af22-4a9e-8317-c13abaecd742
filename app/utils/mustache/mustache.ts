// This is inspired by https://github.com/codemirror/lang-yaml/blob/main/src/yaml.ts
import { parser } from "./parser";
import { LRLanguage, LanguageSupport } from "@codemirror/language";

export const mustacheLanguage = LRLanguage.define({
  name: "mustache",
  parser: parser.configure({}),
});

/// Language support for YAML.
export function mustache() {
  return new LanguageSupport(mustacheLanguage);
}
