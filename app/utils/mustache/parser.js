// This file was generated by lezer-generator. You probably shouldn't edit it.
import { LRParser } from "@lezer/lr";
import {
  content,
  startTag,
  tagKey,
  comment,
  endTag,
  endCommentTag,
  endUnescapedTag,
  mustacheContext,
} from "./tokens.ts";
import { mustacheHighlighting } from "./highlight";
export const parser = LRParser.deserialize({
  version: 14,
  states:
    ",xQQOROOOrO$YO'#C|OOOR'#Cp'#CpO!QO$YO'#DOOOOR'#Cq'#CqO!`OxO'#CrO!hO$YO'#DPOOOR'#Cs'#CsO!vO%[O'#CsO#UO$YO'#DQOOOR'#Ct'#CtO#dO$YO'#DROOOR'#Cv'#CvO#rORO'#CuOOOR'#C{'#C{OOOR'#Cx'#CxQQOROOOOO!v'#Cy'#CyO#|O$YO,59hOOOR,59h,59hO$SO$UO,59hO$[O$YO,59jOOOR,59j,59jO$bO$UO,59jOOOR,59^,59^O$jOpO,59^O$oO$YO,59kOOOR,59k,59kO$uO$UO,59kO$}O%[O,59_OOOR,59_,59_O%TO%WO,59_O%]O$YO,59lOOOR,59l,59lO%cO$UO,59lO%kO$YO,59mOOOR,59m,59mO%qO$UO,59mO%yO$YO'#DSOOOR'#Cw'#CwOOOR,59a,59aO&XORO,59aOOOR-E6v-E6vOOO!v-E6w-E6wOOOR1G/S1G/SO&cO$UO1G/SO&cO$UO1G/SOOOR1G/U1G/UO&kO$UO1G/UO&kO$UO1G/UOOOR1G.x1G.xOOOR1G/V1G/VO&sO$UO1G/VO&sO$UO1G/VOOOR1G.y1G.yO&{O%WO1G.yO&{O%WO1G.yOOOR1G/W1G/WO'TO$UO1G/WO'TO$UO1G/WOOOR1G/X1G/XO']O$UO1G/XO']O$UO1G/XO'eO$YO,59nOOOR,59n,59nO'kO$UO,59nOOOR1G.{1G.{O'sO$UO7+$nOOOR7+$n7+$nO'{O$UO7+$pOOOR7+$p7+$pO(TO$UO7+$qOOOR7+$q7+$qO(]O%WO7+$eOOOR7+$e7+$eO(eO$UO7+$rOOOR7+$r7+$rO(mO$UO7+$sOOOR7+$s7+$sOOOR1G/Y1G/YO(uO$UO1G/YO(uO$UO1G/YOOOR<<HY<<HYOOOR<<H[<<H[OOOR<<H]<<H]OOOR<<HP<<HPOOOR<<H^<<H^OOOR<<H_<<H_O(}O$UO7+$tOOOR7+$t7+$tOOOR<<H`<<H`",
  stateData:
    ")V~OP^OQPORUOSROTZOVXOXTOYWOZ^O[^O~O]dO^dO`cOqaO~O]gO^gO`fOqaO~O_iOahO~O]lO^lO`kOqaO~O]oO^oObnOqaO~O]rO^rO`qOqaO~O]uO^uO`tOqaO~OUvOWxO~PQO]}O^}O`|OqaO~O]!QO^!QO`!POqaO~Oa!SO~O]!UO^!UO`!TOqaO~O]!XO^!XOb!WOqaO~O]![O^![O`!ZOqaO~O]!_O^!_O`!^OqaO~O]!cO^!cO`!bOqaO~OUvOW!dO~PQO`!fOqaO~O`!hOqaO~O`!jOqaO~Ob!lOqaO~O`!nOqaO~O`!pOqaO~O]!rO^!rO`!qOqaO~O`!tOqaO~O`!uOqaO~O`!vOqaO~Ob!wOqaO~O`!xOqaO~O`!yOqaO~O`!{OqaO~O`!|OqaO~O",
  goto: "%YwPPPPPPPPPPPPPPPPPPPPxxxxxx!O!U![!fP$`$fP$l$r$x%O%UX^O]`yX]O]`yQx]R!dyQ`OQy]Tz`yQbPQeRQjUQmWQpXQsZz{bejmps!O!R!V!Y!]!`!a!e!g!i!k!m!o!s!zQ!OdQ!RgQ!VlQ!YoQ!]rQ!`uQ!avQ!e}Q!g!QQ!i!UQ!k!XQ!m![Q!o!_Q!s!cR!z!rX_O]`yXQO]`yXSO]`yXVO]`yXYO]`yX[O]`yTw]y",
  nodeNames:
    "⚠ Content StartTag StartAmpersandTag StartPartialTag StartSectionTag StartCloseSectionTag StartMismatchedCloseSectionTag MissingCloseSectionTag StartCommentTag StartUnescapedTag DelimiterTag InvalidDelimiterTag TagKey InvalidTagKey Comment EndTag EndCommentTag EndUnescapedTag Document Tag PartialTag CommentTag UnescapedTag MismatchedCloseSectionTag Element SectionTag CloseSectionTag",
  maxTerm: 38,
  context: mustacheContext,
  nodeProps: [
    [
      "closedBy",
      -6,
      2,
      3,
      4,
      5,
      6,
      7,
      "EndTag",
      9,
      "EndCommentTag",
      10,
      "EndUnescapedTag",
      26,
      "CloseSectionTag",
    ],
    [
      "openedBy",
      16,
      "StartTag StartAmpersandTag StartPartialTag StartSectionTag StartCloseSectionTag StartMismatchedCloseSectionTag StartCommentTag",
      17,
      "StartCommentTag",
      18,
      "StartUnescapedTag",
      27,
      "SectionTag",
    ],
  ],
  propSources: [mustacheHighlighting],
  skippedNodes: [0],
  repeatNodeCount: 2,
  tokenData:
    "!f~RYX^qpqq#y#zq$f$gq#BY#BZq$IS$I_q$I|$JOq$JT$JUq$KV$KWq&FU&FVq~vYq~X^qpqq#y#zq$f$gq#BY#BZq$IS$I_q$I|$JOq$JT$JUq$KV$KWq&FU&FVq",
  tokenizers: [
    content,
    startTag,
    tagKey,
    comment,
    endTag,
    endCommentTag,
    endUnescapedTag,
    0,
  ],
  topRules: { Document: [0, 19] },
  tokenPrec: 0,
});
