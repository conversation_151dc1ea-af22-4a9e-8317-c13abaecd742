import { useAnalytics } from "#/ui/use-analytics";
import { useCallback } from "react";
import { useUser } from "./user";
import { toast } from "sonner";

export function useSubscribeFn() {
  const { session } = useUser();
  const { analytics } = useAnalytics();

  const subscribeEmail = useCallback(
    async (email?: string, showConfirmation: boolean | undefined = true) => {
      if (!email) {
        toast.error("Enter an email address to subscribe");
        return;
      }
      const res = await fetch("/api/subscribe", {
        method: "POST",
        body: JSON.stringify({ email }),
      });
      const data = await res.json();

      if (showConfirmation) {
        if (data.success) {
          toast.success("Subscribed!", {
            description: "You're subscribed to the Braintrust newsletter",
          });
        } else {
          toast.error("Failed to subscribe", {
            description: data.error,
          });
        }
      }

      const accountId = session?.authId || analytics?.user?.().anonymousId();

      if (accountId) {
        analytics?.identify(accountId, {
          email,
        });
      }
    },
    [analytics, session?.authId],
  );

  return { subscribeEmail };
}
