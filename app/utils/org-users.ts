"use server";

import { type User, userSchema } from "@braintrust/typespecs";
import { getHelper, type UserType, userTypeEnum } from "#/pages/api/user/_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";

export type FetchOrgUsersOutput = Record<
  string,
  User & { user_type: UserType }
>;

export async function fetchOrgUsers(
  {
    orgName,
    userType,
  }: {
    orgName: string;
    userType?: UserType;
  },
  authLookupRaw?: AuthLookup,
): Promise<FetchOrgUsersOutput> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const users = userSchema
    .extend({
      user_type: userTypeEnum,
    })
    .array()
    .parse(
      await getHelper(
        { org_name: [orgName], ...(userType ? { user_type: [userType] } : {}) },
        authLookup,
      ),
    );
  return Object.fromEntries(users.map((user) => [user.id, user]));
}
