import {
  savedFunctionIdSchema,
  type FunctionId,
  type SavedFunctionId,
} from "@braintrust/typespecs";

export const savedScoreSchema = savedFunctionIdSchema;

export type SavedScorer = SavedFunctionId;

export function savedScorerToFunctionId(scorer: SavedScorer): FunctionId {
  if (scorer.type === "function") {
    return { function_id: scorer.id };
  }
  return { global_function: scorer.name };
}
