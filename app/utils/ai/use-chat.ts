// Copied from https://github.com/vercel-labs/ai/blob/9012e17c5f7dc1bed5cba32dd85c7522bdaf268e/packages/core/react/use-chat.ts

import { useCallback, useEffect, useId, useRef, useState } from "react";
import { nanoid, createChunkDecoder } from "ai";

import type {
  ChatRequest,
  CreateMessage,
  Message,
  UseChatOptions,
  ChatRequestOptions,
} from "ai";
import { type ChatCompletionMessage } from "openai/resources/index.mjs";
export type { Message, CreateMessage, UseChatOptions };

export type UseChatHelpers = {
  /** Current messages in the chat */
  messages: Message[];
  /** The error object of the API request */
  error: undefined | Error;
  /**
   * Append a user message to the chat list. This triggers the API call to fetch
   * the assistant's response.
   * @param message The message to append
   * @param options Additional options to pass to the API call
   */
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  /**
   * Reload the last AI chat response for the given chat history. If the last
   * message isn't from the assistant, it will request the API to generate a
   * new response.
   */
  reload: (
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  /**
   * Abort the current request immediately, keep the generated tokens if any.
   */
  stop: () => void;
  /**
   * Update the `messages` state locally. This is useful when you want to
   * edit the messages on the client, and then trigger the `reload` method
   * manually to regenerate the AI response.
   */
  setMessages: (messages: Message[]) => void;
  /** The current value of the input */
  input: string;
  /** setState-powered method to update the input value */
  setInput: React.Dispatch<React.SetStateAction<string>>;
  /** An input/textarea-ready onChange handler to control the value of the input */
  handleInputChange: (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>,
  ) => void;
  /** Form submission handler to automattically reset input and append a user message  */
  handleSubmit: (
    e: React.FormEvent<HTMLFormElement>,
    chatRequestOptions?: ChatRequestOptions,
  ) => void;
  metadata?: object;
  /** Whether the API request is in progress */
  isLoading: boolean;
};

const getStreamedResponse = async (
  api: string,
  chatRequest: ChatRequest,
  setChatMessages: (m: Message[]) => void,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  extraMetadataRef: React.RefObject<any>,
  messagesRef: React.RefObject<Message[]>,
  abortControllerRef: React.RefObject<AbortController | null>,
  onFinish?: (message: Message) => void,
  onResponse?: (response: Response) => void | Promise<void>,
  sendExtraMessageFields?: boolean,
) => {
  // Do an optimistic update to the chat state to show the updated messages
  // immediately.
  const previousMessages = messagesRef.current;
  setChatMessages(chatRequest.messages);

  const res = await fetch(api, {
    method: "POST",
    body: JSON.stringify({
      messages: sendExtraMessageFields
        ? chatRequest.messages
        : chatRequest.messages.map(
            ({ role, content, name, function_call }) => ({
              role,
              content,
              ...(name !== undefined && { name }),
              ...(function_call !== undefined && {
                function_call: function_call,
              }),
            }),
          ),
      ...extraMetadataRef.current.body,
      ...chatRequest.options?.body,
      ...(chatRequest.functions !== undefined && {
        functions: chatRequest.functions,
      }),
      ...(chatRequest.function_call !== undefined && {
        function_call: chatRequest.function_call,
      }),
    }),
    credentials: extraMetadataRef.current.credentials,
    headers: {
      ...extraMetadataRef.current.headers,
      ...chatRequest.options?.headers,
    },
    ...(abortControllerRef.current !== null && {
      signal: abortControllerRef.current.signal,
    }),
  }).catch((err) => {
    // Restore the previous messages if the request fails.
    setChatMessages(previousMessages);
    throw err;
  });

  if (onResponse) {
    try {
      await onResponse(res);
    } catch (err) {
      throw err;
    }
  }

  if (!res.ok) {
    // Restore the previous messages if the request fails.
    setChatMessages(previousMessages);
    throw new Error((await res.text()) || "Failed to fetch the chat response.");
  }

  if (!res.body) {
    throw new Error("The response body is empty.");
  }

  let streamedResponse = "";
  const createdAt = new Date();
  const replyId = nanoid();
  const reader = res.body.getReader();
  const decode = createChunkDecoder();

  const responseMessage: Message = {
    id: replyId,
    createdAt,
    content: "",
    role: "assistant",
  };

  while (true) {
    const { done, value } = await reader.read();
    if (done) {
      break;
    }
    // Update the chat state with the new message tokens.
    streamedResponse += decode(value);

    if (streamedResponse.startsWith('{"function_call":')) {
      // While the function call is streaming, it will be a string.
      responseMessage["function_call"] = streamedResponse;
    } else {
      responseMessage["content"] = streamedResponse;
    }

    setChatMessages([...chatRequest.messages, { ...responseMessage }]);

    // The request has been aborted, stop reading the stream.
    if (abortControllerRef.current === null) {
      reader.cancel();
      break;
    }
  }

  if (streamedResponse.startsWith('{"function_call":')) {
    // Once the stream is complete, the function call is parsed into an object.
    const parsedFunctionCall: ChatCompletionMessage.FunctionCall =
      JSON.parse(streamedResponse).function_call;

    responseMessage["function_call"] = parsedFunctionCall;

    setChatMessages([...chatRequest.messages, { ...responseMessage }]);
  }

  if (onFinish) {
    onFinish(responseMessage);
  }

  return responseMessage;
};

export function useChat({
  api = "/api/chat",
  id,
  initialMessages = [],
  initialInput = "",
  sendExtraMessageFields,
  experimental_onFunctionCall,
  onResponse,
  onFinish,
  onError,
  credentials,
  headers,
  body,
}: UseChatOptions = {}): UseChatHelpers {
  // Generate a unique id for the chat if not provided.
  const hookId = useId();
  const chatId = id || hookId;

  const [messageData, setMessageData] = useState<Record<string, Message[]>>({
    [chatId]: initialMessages,
  });
  const messages = messageData[chatId];
  const messagesRef = useRef<Message[]>(messages);
  const setMessages = useCallback(
    (v: Message[]) => {
      setMessageData((old) => ({
        ...old,
        [chatId]: v,
      }));

      messagesRef.current = v;
    },
    [chatId, setMessageData],
  );

  // Keep the latest messages in a ref.
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {},
  );
  const isLoading = !!loadingStates[chatId];
  const setIsLoading = useCallback(
    (v: boolean) => {
      setLoadingStates((old) => ({
        ...old,
        [chatId]: v,
      }));
    },
    [chatId, setLoadingStates],
  );

  // Abort controller to cancel the current API call.
  const abortControllerRef = useRef<AbortController | null>(null);

  const extraMetadataRef = useRef({
    credentials,
    headers,
    body,
  });
  useEffect(() => {
    extraMetadataRef.current = {
      credentials,
      headers,
      body,
    };
  }, [credentials, headers, body]);

  // Actual mutation hook to send messages to the API endpoint and update the
  // chat state.
  const [error, setError] = useState<undefined | Error>();

  // BRAINTRUST_CHANGE: Changed to "useCallback"
  const triggerRequest = useCallback(
    async (chatRequest: ChatRequest) => {
      try {
        setIsLoading(true);
        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        while (true) {
          const streamedResponseMessage = await getStreamedResponse(
            api,
            chatRequest,
            setMessages,
            extraMetadataRef,
            messagesRef,
            abortControllerRef,
            onFinish,
            onResponse,
            sendExtraMessageFields,
          );

          if (
            streamedResponseMessage.function_call === undefined ||
            typeof streamedResponseMessage.function_call === "string"
          ) {
            break;
          }

          // Streamed response is a function call, invoke the function call handler if it exists.
          if (experimental_onFunctionCall) {
            const functionCall = streamedResponseMessage.function_call;

            // User handles the function call in their own functionCallHandler.
            // The "arguments" key of the function call object will still be a string which will have to be parsed in the function handler.
            // If the "arguments" JSON is malformed due to model error the user will have to handle that themselves.

            const functionCallResponse: ChatRequest | void =
              await experimental_onFunctionCall(
                messagesRef.current,
                functionCall,
              );

            // If the user does not return anything as a result of the function call, the loop will break.
            if (functionCallResponse === undefined) break;

            // A function call response was returned.
            // The updated chat with function call response will be sent to the API in the next iteration of the loop.
            chatRequest = functionCallResponse;
          }
        }

        abortControllerRef.current = null;

        return null;
      } catch (err) {
        // Ignore abort errors as they are expected.
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        if ((err as any).name === "AbortError") {
          abortControllerRef.current = null;
          return null;
        }

        if (onError && err instanceof Error) {
          onError(err);
        }

        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [
      api,
      experimental_onFunctionCall,
      setMessages,
      setIsLoading,
      onError,
      onFinish,
      onResponse,
      sendExtraMessageFields,
    ],
  );

  const append = useCallback(
    async (
      message: Message | CreateMessage,
      { options, functions, function_call }: ChatRequestOptions = {},
    ) => {
      if (!message.id) {
        message.id = nanoid();
      }

      const chatRequest: ChatRequest = {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        messages: messagesRef.current.concat(message as Message),
        options,
        ...(functions !== undefined && { functions }),
        ...(function_call !== undefined && { function_call }),
      };

      return triggerRequest(chatRequest);
    },
    [triggerRequest],
  );

  const reload = useCallback(
    async ({ options, functions, function_call }: ChatRequestOptions = {}) => {
      if (messagesRef.current.length === 0) return null;

      // Remove last assistant message and retry last user message.
      const lastMessage = messagesRef.current[messagesRef.current.length - 1];
      if (lastMessage.role === "assistant") {
        const chatRequest: ChatRequest = {
          messages: messagesRef.current.slice(0, -1),
          options,
          ...(functions !== undefined && { functions }),
          ...(function_call !== undefined && { function_call }),
        };

        return triggerRequest(chatRequest);
      }

      const chatRequest: ChatRequest = {
        messages: messagesRef.current,
        options,
        ...(functions !== undefined && { functions }),
        ...(function_call !== undefined && { function_call }),
      };

      return triggerRequest(chatRequest);
    },
    [triggerRequest],
  );

  const stop = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Input state and handlers.
  const [input, setInput] = useState(initialInput);

  const handleSubmit = useCallback(
    (
      e: React.FormEvent<HTMLFormElement>,
      { options, functions, function_call }: ChatRequestOptions = {},
      metadata?: object,
    ) => {
      if (metadata) {
        extraMetadataRef.current = {
          ...extraMetadataRef.current,
          ...metadata,
        };
      }

      e.preventDefault();
      if (!input) return;

      append(
        {
          content: input,
          role: "user",
          createdAt: new Date(),
        },
        { options, functions, function_call },
      );
      setInput("");
    },
    [input, append],
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const handleInputChange = (e: any) => {
    setInput(e.target.value);
  };

  return {
    messages,
    error,
    append,
    reload,
    stop,
    setMessages,
    input,
    setInput,
    handleInputChange,
    handleSubmit,
    isLoading,
  };
}
