import { type ModelSpec } from "@braintrust/proxy/schema";

export async function getWindowAIModel(): Promise<ModelSpec | null> {
  if (
    typeof window !== "undefined" &&
    globalThis.ai &&
    (await globalThis.ai.canCreateGenericSession()) &&
    (await globalThis.ai.canCreateTextSession())
  ) {
    return {
      format: "window",
      flavor: "completion",
      multimodal: false,
      input_cost_per_mil_tokens: 0,
      output_cost_per_mil_tokens: 0,
      displayName: "window.ai",
    };
  } else {
    return null;
  }
}
