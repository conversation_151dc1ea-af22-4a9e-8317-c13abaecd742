import { type ModelFormat } from "@braintrust/proxy/schema";

export const CompletionPlaceholders = {
  openai: "Enter instructions",
  anthropic: undefined,
  google: "Enter instructions",
  js: "Enter some text or variables to echo back",
  window: "What can my browser do?",
  converse: "Enter instructions",
};

export const ChatPlaceholders: {
  [name in ModelFormat]: { [messageType: string]: string };
} = {
  openai: {
    user: "Enter user text message",
    system: "You are a helpful assistant",
    assistant: "Enter assistant message",
  },
  anthropic: {
    human: "Enter human message",
    assistant: "Enter assistant message",
  },
  google: {
    human: "Enter human message",
    model: "Enter model message",
  },
  js: {},
  window: {},
  converse: {
    user: "Enter user text message",
    system: "You are a helpful assistant",
    assistant: "Enter assistant message",
  },
};
