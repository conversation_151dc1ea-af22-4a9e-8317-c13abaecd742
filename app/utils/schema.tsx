import { DataType } from "apache-arrow";
import type { DataObjectType } from "./btapi/btapi";
import { type AuditLogRow } from "@braintrust/local/api-schema";
import { deserializePlainStringAsJSON } from "./object";
import { type IdentifiableRow } from "@braintrust/local";
import { type AuditLogOrigin } from "@braintrust/local/api-schema";
export { extractIdentifiableRow } from "@braintrust/local";
export type { IdentifiableRow } from "@braintrust/local";

export const JSONType = "JSON";
export type DuckDBJSONType = string | DuckDBJSONStruct;
export type DuckDBJSONStruct = { [name: string]: DuckDBJSONType };

export type IdentifiableRowWithOrigin = IdentifiableRow & {
  origin: AuditLogOrigin;
};

export function getDuckDBTypeChild(
  schema: DuckDBJSONType,
  name: string,
): DuckDBJSONType | undefined {
  if (schema === JSONType) {
    return JSONType;
  } else if (typeof schema === "string") {
    return undefined;
  } else {
    return schema[name];
  }
}

export function isDuckDBJSONType(
  arrowType?: DataType,
  duckDBType?: DuckDBJSONType,
): boolean {
  return DataType.isUtf8(arrowType) && duckDBType === JSONType;
}

const AuditFields = ["origin", "audit_data", "comment"] as const;

const ExperimentFields = [
  "input",
  "output",
  "error",
  "expected",
  "scores",
  "metadata",
  "output_vs_expected",
  "span_parents",
  "span_attributes",
  "tags",
  "metrics",
  "context",
].concat(AuditFields);

const PromptFields = ["prompt_data", "tags", "metadata"].concat(AuditFields);
const FunctionFields = [
  ...PromptFields,
  "function_data",
  "origin",
  "function_schema",
];
export const PROMPT_LOG_ID = "p";

const ProjectExperimentsFields = ["metadata", "creator", "source"];

type DuckDBObjectType = DataObjectType | "project_experiments";

// This is a hard-coded schematic assumption. Ideally, we can transmit this through
// parquet somehow
export const DuckDBSchemaHints: { [key in DuckDBObjectType]: string[] } = {
  dataset: ["input", "expected", "metadata", "tags"].concat(AuditFields),
  experiment: ExperimentFields,
  project_logs: ExperimentFields,
  prompt_session: [
    "prompt_session_data",
    "prompt_data",
    "function_data",
    "object_data",
    "completion",
    "tags",
  ].concat(AuditFields),
  playground_logs: ExperimentFields,
  project_prompts: PromptFields,
  org_prompts: PromptFields,
  project_functions: FunctionFields,
  org_functions: FunctionFields,
  project_experiments: ProjectExperimentsFields,
};

function mapToJSONType(fields: string[]): DuckDBJSONStruct {
  return Object.fromEntries(fields.map((f) => [f, JSONType]));
}

export const DuckDBTypeHints: Record<DuckDBObjectType, DuckDBJSONStruct> = {
  org_functions: mapToJSONType(DuckDBSchemaHints.org_functions),
  dataset: mapToJSONType(DuckDBSchemaHints.dataset),
  experiment: mapToJSONType(DuckDBSchemaHints.experiment),
  project_logs: mapToJSONType(DuckDBSchemaHints.project_logs),
  prompt_session: mapToJSONType(DuckDBSchemaHints.prompt_session),
  playground_logs: mapToJSONType(DuckDBSchemaHints.playground_logs),
  project_prompts: mapToJSONType(DuckDBSchemaHints.project_prompts),
  org_prompts: mapToJSONType(DuckDBSchemaHints.org_prompts),
  project_functions: mapToJSONType(DuckDBSchemaHints.project_functions),
  project_experiments: mapToJSONType(DuckDBSchemaHints.project_experiments),
};

// Given an object whose values are stringified, converts the appropriate values
// to JSON.
export function parseShallowStringJSON(
  jsonFields: DuckDBJSONStruct,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  object: any,
) {
  if (object.toJSON) {
    object = object.toJSON();
  }
  return Object.fromEntries(
    Object.entries(object).map(([k, v]) => [
      k,
      v && typeof v === "string" && jsonFields[k] === "JSON"
        ? deserializePlainStringAsJSON(v).value
        : v,
    ]),
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function parseObjectJSON(objectType: DuckDBObjectType, object: any) {
  return parseShallowStringJSON(DuckDBTypeHints[objectType], object);
}

export function stringifyObjectJSON(
  objectType: DuckDBObjectType,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  object: any,
  skipKeys?: string[],
) {
  const jsonFields = DuckDBTypeHints[objectType];
  return Object.fromEntries(
    Object.entries(object).map(([k, v]) =>
      skipKeys?.includes(k)
        ? [k, v]
        : [k, jsonFields[k] === "JSON" ? JSON.stringify(v ?? undefined) : v],
    ),
  );
}

const AuditLogSchemaJSONFields: (keyof AuditLogRow)[] = [
  "origin",
  "metadata",
  "comment",
  "audit_data",
];
export const AuditLogSchemaHints = mapToJSONType(AuditLogSchemaJSONFields);
