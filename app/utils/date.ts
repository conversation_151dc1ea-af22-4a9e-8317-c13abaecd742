export function formatFullDateTime(date: Date, tzUTC: boolean = false): string {
  if (!date || !date.valueOf || isNaN(date.valueOf())) {
    console.assert(false, "Invalid date", date);
    return "";
  }
  if (tzUTC) {
    return date.toLocaleString(undefined, {
      timeZoneName: "short",
      timeZone: "UTC",
    });
  }
  return date.toLocaleString(undefined, {
    timeZoneName: "short",
  });
}
