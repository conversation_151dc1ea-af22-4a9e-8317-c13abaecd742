import { dbQ<PERSON>y } from "#/utils/duckdb";
import { Metadata<PERSON>ield, type NestedField } from "@braintrust/local/query";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";

export async function metadataFieldsQuery({
  conn,
  abortSignal,
  query,
  parse,
  whereClause,
  limit = 500,
}: {
  conn: AsyncDuckDBConnection;
  abortSignal: AbortSignal;
  query: string | null;
  parse?: boolean;
  whereClause?: string;
  limit?: number;
}) {
  const metadataStructure = await dbQuery(
    conn,
    abortSignal,
    query
      ? `
      SELECT
        json_group_structure(${parseJSONExpr(MetadataField, parse)}) as metadata,
      FROM (
        SELECT * FROM (${query})
        ${whereClause ? `WHERE ${whereClause}` : ""}
        LIMIT ${limit}
      ) "t"`
      : null,
  );

  const row = metadataStructure?.toArray()?.[0] ?? {};
  return parseJSONInfo(row, MetadataField);
}

function parseJSONExpr(field: string, parse?: boolean) {
  return parse ? `json(${field})` : field;
}

export function parseJSONInfo(
  row: Record<string, string>,
  field: string,
  unfoldArray?: boolean,
) {
  if (!row[field]) {
    return null;
  }
  const info = JSON.parse(row[field]);
  // json_group_structure returns
  // "NULL" when values are NULL
  // "JSON" when values are empty objects {}
  if (info === "NULL" || info === "JSON" || info === null) {
    return [];
  } else {
    return unfoldNestedFields([], info, unfoldArray);
  }
}

export function unfoldNestedFields(
  parentPath: NestedField,
  field: unknown,
  unfoldArray?: boolean,
): NestedField[] {
  if (field && field instanceof Object) {
    if (!Array.isArray(field)) {
      return Object.entries(field).flatMap(([k, v]) =>
        unfoldNestedFields(parentPath.concat(k), v, unfoldArray),
      );
    } else if (unfoldArray) {
      return field.flatMap((item, index) =>
        unfoldNestedFields(parentPath.concat(`[${index}]`), item, unfoldArray),
      );
    }
  }
  return [parentPath];
}
