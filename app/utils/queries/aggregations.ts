import { doubleQuote } from "#/utils/sql-utils";
import { z } from "zod";

export type AggregationFieldType = "scores" | "metrics";

export const aggregationTypeEnumSchema = z.enum([
  "avg",
  "min",
  "max",
  "sum",
  "median",
]);
export type AggregationType = z.infer<typeof aggregationTypeEnumSchema>;

export function aggregationValue({
  groupAggregationTypes,
  field,
  type,
  relation,
  diffObjectKey,
}: {
  groupAggregationTypes: Record<AggregationFieldType, AggregationType>;
  field: string;
  type: AggregationFieldType;
  relation?: string;
  diffObjectKey?: string;
}) {
  const aggregationFn =
    groupAggregationTypes?.[type] ?? (type === "scores" ? "AVG" : "SUM");
  const relationStr = relation ? `${relation}.` : "";
  const suffix = diffObjectKey ? `.${diffObjectKey}` : "";

  return `${aggregationFn}(${relationStr}${type}.${doubleQuote(field)}${suffix})`;
}
