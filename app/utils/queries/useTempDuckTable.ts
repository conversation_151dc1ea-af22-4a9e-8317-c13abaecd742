import { type SetStateAction, useCallback, useMemo, useState } from "react";
import {
  dbQ<PERSON>y,
  duck<PERSON><PERSON><PERSON>,
  type DuckDBInsertionCategory,
  insertRecords,
  InsertRecordsResult,
  useDBQuery,
} from "#/utils/duckdb";
import { doubleQuote } from "#/utils/sql-utils";
import { sha1 } from "#/utils/hash";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { type Schema } from "apache-arrow";
import { useAtomEffect } from "#/utils/atoms";
import { useCallbackOne as useStableCallback } from "use-memo-one";
import { allTempTableNamesAtom, type TempTableNamesData } from "./atoms";
import { atom } from "jotai";
import { useAtomCallback } from "jotai/utils";

export function useTempDuckTable({
  tableNameHashKey,
  scan,
  primaryKey,
  signals,
}: {
  tableNameHashKey: string | null;
  scan: string | null;
  primaryKey?: string;
  signals: number[];
}) {
  const [materializedTableName, setMaterializedTableName] = useState<
    string | null
  >();

  const abortCleanup = useAbortCleanup();
  const queryFn = useMemo(() => {
    if (!scan || !tableNameHashKey) {
      return null;
    }
    return async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      const tableName = await createTempTable(conn, abortSignal, {
        tableNameHashKey,
        scan,
        beforeCreateFn: (t) => abortCleanup(tableNameHashKey, t),
        primaryKey,
      });

      if (tableName) {
        setMaterializedTableName(tableName);
      }

      return null;
    };
  }, [
    tableNameHashKey,
    scan,
    setMaterializedTableName,
    primaryKey,
    abortCleanup,
  ]);

  const { executions: ready } = useDBQuery(queryFn, signals);
  useCleanupTempTables(
    tableNameHashKey ?? "useTempDuckTable",
    materializedTableName,
  );

  return {
    tableName: materializedTableName ?? null,
    ready,
  };
}

export const stableSignals = [1];
export function useTempDuckDataTable({
  tableNameHashKey,
  tableDefinition,
  data,
  schema,
  insertionCategory,
}: {
  tableNameHashKey: string | null;
  tableDefinition: string;
  data: unknown[];
  schema: Schema | null;
  insertionCategory: DuckDBInsertionCategory;
}) {
  const [materializedTableName, setMaterializedTableName] = useState<
    string | null
  >();
  const queryFn = useMemo(() => {
    if (!tableNameHashKey || !schema) {
      return null;
    }
    return async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      const tableName = sha1(tableNameHashKey);
      await conn.query(
        `CREATE OR REPLACE TABLE ${doubleQuote(tableName)} ${tableDefinition}`,
      );

      const result = await insertRecords(
        conn,
        schema,
        {},
        tableName,
        data,
        insertionCategory,
      );
      if (result != InsertRecordsResult.SUCCESS) {
        console.error("Failed to insert records to", tableName, result);
      }

      if (tableName) {
        setMaterializedTableName(tableName);
      }

      return null;
    };
  }, [tableNameHashKey, tableDefinition, schema, data, insertionCategory]);

  const { executions: ready } = useDBQuery(queryFn, stableSignals);
  useCleanupTempTables(
    tableNameHashKey ?? "useTempDuckDataTable",
    materializedTableName,
  );

  return {
    tableName: materializedTableName ?? null,
    ready,
  };
}

export async function createTempTable(
  conn: AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  {
    // tableNameHashKey is a guaranteed unique key to identify the corresponding scan
    tableNameHashKey,
    scan,
    beforeCreateFn,
    primaryKey,
  }: {
    tableNameHashKey: string;
    scan: string;
    beforeCreateFn?: (tableName: string) => void;
    primaryKey?: string;
  },
) {
  const tableName = sha1(tableNameHashKey);
  beforeCreateFn?.(tableName);
  const data = await dbQuery(
    conn,
    abortSignal,
    `CREATE OR REPLACE TABLE ${doubleQuote(tableName)} AS ${scan};
    ${
      // alter this table in the same query so we don't run into race conditions in js land,
      // where the same table is getting created in parallel and the ALTER TABLE commands run at the same time:
      // which would cause an error (Catalog Error: an index with that name already exists for this table)
      // e.g.
      // render 1: (1) await CREATE TABLE -> (3) await ALTER TABLE
      // render 2: (2) await CREATE TABLE -> (4) await ALTER TABLE
      primaryKey
        ? `ALTER TABLE ${doubleQuote(tableName)} ADD PRIMARY KEY (${primaryKey});`
        : ""
    }`,
  );

  return data != null ? tableName : null;
}

// Call this to imperatively abort any cleanup effect that was created in useCleanupTempTables.
// This is a somewhat hacky secondary function to cover a race condition where table cleanup
// and table creation happen at the same time.
// (e.g. un-selecting and then re-selecting a comparison experiment at exactly 30s)
export function useAbortCleanup() {
  return useAtomCallback(
    useCallback((get, set, key: string, tableName: string) => {
      const allTempTableNames = get(allTempTableNamesAtom);
      const tempTableNames = allTempTableNames?.[key];
      if (!tempTableNames) {
        return;
      }
      const v = tempTableNames[tableName];
      if (!v) {
        return;
      }
      v.abortSignal.abort();
      clearTimeout(v.timeoutId);
      set(allTempTableNamesAtom, {
        ...allTempTableNames,
        [key]: {
          ...tempTableNames,
          [tableName]: null,
        },
      });
      return;
    }, []),
  );
}

export function useCleanupTempTables(
  // used to distinguish callsites from one another since the atom is globalized (so it can persist through navigation)
  // but the cleanup effect is localized to the callsites
  key: string,
  _tempTableNames: string | (string | undefined | null)[] | undefined | null,
  entityType: "TABLE" | "INDEX" = "TABLE",
) {
  const tempTableNames = useMemo(() => {
    if (!_tempTableNames) {
      return [];
    }
    if (typeof _tempTableNames === "string") {
      return [_tempTableNames];
    }
    return _tempTableNames.filter(
      (t: string | undefined | null): t is string => !!t,
    );
  }, [_tempTableNames]);

  // Create a derived atom so that cleanup for one table name doesn't
  // trigger the same effect for all other callsites
  const tempTableNamesAtom = useMemo(() => {
    return atom(
      (get) => {
        return get(allTempTableNamesAtom)?.[key] ?? {};
      },
      (get, set, newValue: SetStateAction<TempTableNamesData>) => {
        const prevValue: TempTableNamesData =
          get(allTempTableNamesAtom)?.[key] ?? {};
        const nextValue: TempTableNamesData =
          typeof newValue === "function" ? newValue(prevValue) : newValue;

        const { [key]: _, ...rest } = get(allTempTableNamesAtom);
        if (Object.keys(nextValue).length === 0) {
          set(allTempTableNamesAtom, rest);
          return;
        }
        set(allTempTableNamesAtom, {
          ...rest,
          [key]: nextValue,
        });
      },
    );
  }, [key]);

  // This effect will track all table names using a null value in the map.
  // When the cleanup function is called, any null values will be assigned the timeout function for table deletion.
  // If the effect is run with new table names, any existing timeout functions will be cancelled/aborted
  useAtomEffect(
    useStableCallback(
      (get, set) => {
        const v = get(tempTableNamesAtom);
        tempTableNames.forEach((t) => {
          if (!v?.[t]) {
            return;
          }
          v[t].abortSignal.abort();
          clearTimeout(v[t].timeoutId);
        });

        // track table names for cleanup and unmounting
        set(tempTableNamesAtom, (prev) => {
          return {
            ...prev,
            ...Object.fromEntries(tempTableNames.map((t) => [t, null])),
          };
        });

        return () => {
          const newAtomValues = Object.fromEntries(
            tempTableNames
              // find all table names that have a null value (i.e. tracked but aren't scheduled for deletion yet)
              .filter((t) => !get(tempTableNamesAtom)?.[t])
              .map((t) => {
                const abortSignal = new AbortController();
                const timeoutId = setTimeout(async () => {
                  const duck = get(duckDBAtom);
                  const conn = await duck?.connect();
                  if (abortSignal.signal.aborted) {
                    return;
                  }
                  await conn?.query(
                    `DROP ${entityType} IF EXISTS ${doubleQuote(t)}`,
                  );

                  set(tempTableNamesAtom, (prev) => {
                    const { [t]: _, ...rest } = prev;
                    return rest;
                  });
                }, 1000 * 30);
                return [
                  t,
                  {
                    timeoutId,
                    abortSignal,
                  },
                ];
              }),
          );

          if (Object.keys(newAtomValues).length > 0) {
            set(tempTableNamesAtom, (prev) => {
              return {
                ...prev,
                ...newAtomValues,
              };
            });
          }
        };
      },
      [tempTableNames, tempTableNamesAtom, entityType],
    ),
  );
}
