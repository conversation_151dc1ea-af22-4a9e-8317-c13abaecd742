import { atom } from "jotai";

export type TempTableNamesData = Record<
  // tableName
  string,
  {
    timeoutId: ReturnType<typeof setTimeout>;
    abortSignal: AbortController;
  } | null
>;

// If this atom is defined in the same file as where it's used,
// fast-refresh can wipe out the atom data when other code changes.
// So define the atoms in a separate file
export const allTempTableNamesAtom = atom<
  Record<
    // key
    string,
    TempTableNamesData
  >
>({});
