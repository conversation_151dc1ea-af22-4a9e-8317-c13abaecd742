"use client";

import { createContext, useContext } from "react";

const IncidentStatusContext = createContext<string | undefined>(undefined);

export function IncidentStatusProvider({
  children,
  value,
}: {
  children: React.ReactNode;
  value?: string;
}) {
  return (
    <IncidentStatusContext.Provider value={value}>
      {children}
    </IncidentStatusContext.Provider>
  );
}

export function useIncidentStatus() {
  return useContext(IncidentStatusContext);
}
