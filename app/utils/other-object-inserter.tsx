import type { DataObjectType } from "./btapi/btapi";
import { type SetStateAction, type Dispatch, useEffect, useState } from "react";
import { useParquetView } from "#/utils/duckdb";
import { useMutableObject } from "#/utils/mutable-object";
import { type SavingState } from "#/ui/saving";
import { toast } from "sonner";

export interface OtherObjectInserterInfo {
  objectInfo: {
    id: string;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  getRows: () => Promise<any[]>;
  onSuccess?: (numRows: number) => void;
  onFailure?: () => void;
  cleanup?: () => void;
}

// Exposes an interface to insert rows from some
// caller-provided source into the object of their choosing.
export function useOtherObjectInserter({
  objectType,
  setSavingState,
}: {
  objectType: DataObjectType;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
}) {
  const [otherObjectInserterInfo, setOtherObjectInserterInfo] = useState<
    OtherObjectInserterInfo | undefined
  >(undefined);
  const { refreshed, scan, channel } = useParquetView({
    objectType,
    search: otherObjectInserterInfo?.objectInfo.id,
  });
  const otherDml = useMutableObject({
    scan,
    objectType,
    channel,
    setSavingState,
  });

  useEffect(() => {
    if (!otherObjectInserterInfo || !refreshed) {
      return;
    }
    const inserterInfo = otherObjectInserterInfo;
    setOtherObjectInserterInfo(undefined);
    (async () => {
      try {
        const rows = await inserterInfo.getRows();
        const preparedRows = await otherDml.prepareUpserts(rows);
        await otherDml.upsert(preparedRows);
        inserterInfo.onSuccess?.(preparedRows.length);
      } catch (e) {
        toast.error("Failed to copy rows", {
          description: `${e}`,
        });
        inserterInfo.onFailure?.();
      } finally {
        inserterInfo.cleanup?.();
      }
    })();
  }, [objectType, otherDml, otherObjectInserterInfo, refreshed]);

  return setOtherObjectInserterInfo;
}
