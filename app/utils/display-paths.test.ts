import { describe, expect, test } from "vitest";
import { Field, Utf8, Schema, Struct, Float, Precision } from "apache-arrow";
import { computeDisplayPaths } from "./display-paths";

describe("computeDisplayPaths", () => {
  test("no-ops empty schema", () => {
    expect(computeDisplayPaths(null, ["field1", "field2"])).toEqual({
      type: "node",
      children: {},
    });
  });

  test("only generates fields for projected paths", () => {
    expect(
      computeDisplayPaths(
        new Schema([
          new Field("field1", new Utf8()),
          new Field("field2", new Utf8()),
        ]),
        ["field1"],
      ),
    ).toEqual({
      type: "node",
      children: {
        field1: { type: "leaf" },
      },
    });
  });

  describe("no comparisons", () => {
    test("builds display path with no structs", () => {
      const schema = new Schema([
        new Field("id", new Utf8()),
        new Field("field1", new Utf8()),
        new Field("field2", new Utf8()),
      ]);
      expect(
        computeDisplayPaths(
          schema,
          schema.fields.map((f) => f.name),
        ),
      ).toEqual({
        type: "node",
        children: {
          id: { type: "leaf" },
          field1: { type: "leaf" },
          field2: { type: "leaf" },
        },
      });
    });

    test("builds display path with nested structs", () => {
      const schema = new Schema([
        new Field("id", new Utf8()),
        new Field(
          "scores",
          new Struct([
            new Field("score1", new Float(Precision.DOUBLE)),
            new Field("score2", new Float(Precision.DOUBLE)),
          ]),
        ),
        new Field(
          "field",
          new Struct([
            new Field(
              "subfield1",
              new Struct([new Field("nested1", new Utf8())]),
            ),
            new Field(
              "subfield2",
              new Struct([new Field("nested2", new Utf8())]),
            ),
          ]),
        ),
      ]);
      expect(
        computeDisplayPaths(
          schema,
          schema.fields.map((f) => f.name),
        ),
      ).toEqual({
        type: "node",
        children: {
          id: { type: "leaf" },
          scores: {
            type: "node",
            children: {
              score1: { type: "leaf" },
              score2: { type: "leaf" },
            },
          },
          field: {
            type: "node",
            children: {
              subfield1: {
                type: "node",
                children: {
                  nested1: {
                    type: "leaf",
                  },
                },
              },
              subfield2: {
                type: "node",
                children: {
                  nested2: {
                    type: "leaf",
                  },
                },
              },
            },
          },
        },
      });
    });
  });

  describe("with comparisons", () => {
    test("ignores duplicate non-struct comparison fields", () => {
      const schema = new Schema([
        new Field("id", new Utf8()),
        new Field(
          "field1",
          new Struct([
            new Field("subfield1", new Utf8()),
            new Field("subfield2", new Utf8()),
          ]),
        ),
      ]);
      expect(
        computeDisplayPaths(
          schema,
          schema.fields.map((f) => f.name),
          [
            new Schema([
              new Field("id", new Float(Precision.DOUBLE)),
              new Field("field1", new Float(Precision.DOUBLE)),
            ]),
          ],
        ),
      ).toEqual({
        type: "node",
        children: {
          id: { type: "leaf" },
          field1: {
            type: "node",
            children: {
              subfield1: { type: "leaf" },
              subfield2: { type: "leaf" },
            },
          },
        },
      });
    });

    test("combines fields from comparison schemas", () => {
      const schema = new Schema([new Field("field1", new Utf8())]);
      expect(
        computeDisplayPaths(
          schema,
          ["field1", "field2", "field3"],
          [
            new Schema([new Field("field2", new Utf8())]),
            new Schema([new Field("field3", new Utf8())]),
          ],
        ),
      ).toEqual({
        type: "node",
        children: {
          field1: { type: "leaf" },
          field2: { type: "leaf" },
          field3: { type: "leaf" },
        },
      });
    });

    test("replaces top-level non-struct fields with struct fields from comparison schema", () => {
      const schema = new Schema([
        new Field("replaced1", new Utf8()),
        new Field("replaced2", new Utf8()),
      ]);
      expect(
        computeDisplayPaths(
          schema,
          schema.fields.map((f) => f.name),
          [
            new Schema([
              new Field(
                "replaced1",
                new Struct([new Field("subfield1", new Utf8())]),
              ),
            ]),
            new Schema([
              new Field(
                "replaced2",
                new Struct([new Field("subfield2", new Utf8())]),
              ),
            ]),
          ],
        ),
      ).toEqual({
        type: "node",
        children: {
          replaced1: {
            type: "node",
            children: {
              subfield1: { type: "leaf" },
            },
          },
          replaced2: {
            type: "node",
            children: {
              subfield2: { type: "leaf" },
            },
          },
        },
      });
    });

    test("does not replace struct fields with comparison struct fields", () => {
      const schema = new Schema([
        new Field(
          "non_replaced_field",
          new Struct([
            new Field("subfield1", new Utf8()),
            new Field("subfield2", new Utf8()),
          ]),
        ),
      ]);
      expect(
        computeDisplayPaths(
          schema,
          schema.fields.map((f) => f.name),
          [
            new Schema([
              new Field(
                "non_replaced_field",
                new Struct([new Field("new_subfield", new Utf8())]),
              ),
            ]),
          ],
        ),
      ).toEqual({
        type: "node",
        children: {
          non_replaced_field: {
            type: "node",
            children: {
              subfield1: { type: "leaf" },
              subfield2: { type: "leaf" },
            },
          },
        },
      });
    });

    // in the future we can add functionality to replace nested struct fields,
    // but for now let's keep things simple
    test("does not replace nested struct fields with struct fields from comparison schema", () => {
      const schema = new Schema([
        new Field(
          "non_replaced",
          new Struct([new Field("non_replaced_subfield", new Utf8())]),
        ),
      ]);
      expect(
        computeDisplayPaths(
          schema,
          schema.fields.map((f) => f.name),
          [
            new Schema([
              new Field(
                "non_replaced",
                new Struct([
                  new Field(
                    "non_replaced_subfield",
                    new Struct([new Field("new_subfield", new Utf8())]),
                  ),
                ]),
              ),
            ]),
          ],
        ),
      ).toEqual({
        type: "node",
        children: {
          non_replaced: {
            type: "node",
            children: {
              non_replaced_subfield: {
                type: "leaf",
              },
            },
          },
        },
      });
    });
  });
});
