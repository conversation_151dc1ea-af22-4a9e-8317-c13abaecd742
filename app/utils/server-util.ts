import type { NextApiRequest, NextApiResponse } from "next";
import { NextResponse } from "next/server";
import { types } from "pg";
import { HTTPError } from "./http_error";
import { augmentWithErrorContext } from "@braintrust/local";
import type { AuthLookup } from "#/pages/api/_lookup_api_key";
import { deriveErrorContext } from "#/utils/derive-error-context";

// Re-export HTTPError and AuthLookup from this file for convenience.
export { HTTPError };
export type { AuthLookup };

// Our zod validation is fairly strict about datetime strings, so we try to
// parse it into a date and return an ISO string. If that fails, we just return
// the string unmodified.
types.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => {
  const d = new Date(val);
  return isNaN(d.getTime()) ? val : d.toISOString();
});

export async function httpErrorToNextApiResponse({
  error,
  authLookup,
  res,
  extraTags,
}: {
  error: HTTPError;
  authLookup: AuthLookup | undefined;
  res: NextApiResponse;
  extraTags?: Record<string, string | undefined>;
}): Promise<void> {
  for (const [k, v] of Object.entries(error.headers)) {
    res.setHeader(k, v);
  }
  res
    .status(error.code)
    .send(
      augmentWithErrorContext(
        error.message,
        await deriveErrorContext(authLookup),
        extraTags,
      ),
    );
}

export async function httpErrorToNextResponse({
  error,
  authLookup,
  extraTags,
}: {
  error: HTTPError;
  authLookup: AuthLookup | undefined;
  extraTags?: Record<string, string | undefined>;
}): Promise<NextResponse> {
  const message = authLookup
    ? augmentWithErrorContext(
        error.message,
        await deriveErrorContext(authLookup),
        extraTags,
      )
    : error.message;
  return new NextResponse(message, {
    status: error.code,
    headers: error.headers,
  });
}

export function getRequestIdNextApiRequest(
  req: NextApiRequest,
): string | undefined {
  const requestIdHeader = req.headers["x-vercel-id"];
  return Array.isArray(requestIdHeader)
    ? requestIdHeader.join(",")
    : requestIdHeader;
}

export function getRequestIdNextRequest(req: Request): string | undefined {
  const requestIdHeader = req.headers.get("x-vercel-id");
  return requestIdHeader
    ? Array.isArray(requestIdHeader)
      ? requestIdHeader.join(",")
      : requestIdHeader
    : undefined;
}
