import {
  type ErrorContext,
  errorContextSchema,
  EMPTY_ERROR_CONTEXT,
} from "@braintrust/local";
import type { AuthLookup } from "#/pages/api/_lookup_api_key";
import { KV } from "#/utils/cache";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { sha1 } from "#/utils/hash";
import { isAdminEmail } from "./auth/admins";
import { waitUntil } from "@vercel/functions";

// Note: the ErrorContext caching done here is analogous to the caching done in
// api-ts/src/error_context_cache.ts. In fact the data plane invokes this
// control plane logic, so we end up hitting caches on both fronts.
export async function deriveErrorContext(
  authLookup: AuthLookup | undefined,
): Promise<ErrorContext> {
  if (!authLookup) {
    return EMPTY_ERROR_CONTEXT;
  }
  const authLookupHash = `${sha1(authLookup.auth_id)}_${authLookup.org_id || ""}`;
  const kv = new KV();

  {
    const resp = await kv.get(
      `error_context_${authLookupHash}`,
      errorContextSchema,
    );
    if (resp.status === "found") {
      return resp.value;
    }
  }

  const conn = getServiceRoleSupabase();
  const errorContext = await (async (): Promise<ErrorContext> => {
    try {
      const { rows } = await conn.query(
        `
          with
          user_id_email as (
            select id, email from users where auth_id = $1
          ),
          org_name as (
            select name from organizations where id = $2
          )
          select user_id_email.id "userId", user_id_email.email "userEmail", org_name.name "orgName"
          from user_id_email left join org_name on true
      `,
        [authLookup.auth_id, authLookup.org_id],
      );
      if (rows.length !== 1) {
        return EMPTY_ERROR_CONTEXT;
      }
      return errorContextSchema.parse(rows[0]);
    } catch (e) {
      console.error("Failed to fetch error context\n", e);
      return EMPTY_ERROR_CONTEXT;
    }
  })();

  waitUntil(
    kv.set(
      `error_context_${authLookupHash}`,
      errorContext,
      // Cache for 60s
      { ex: 60 },
    ),
  );

  return errorContext;
}

export async function isAllowedSysadmin(
  authLookup: AuthLookup | undefined,
): Promise<boolean> {
  if (!authLookup) {
    return false;
  }
  const userEmail = (await deriveErrorContext(authLookup)).userEmail;
  if (!userEmail) {
    return false;
  }
  return isAdminEmail(userEmail);
}
