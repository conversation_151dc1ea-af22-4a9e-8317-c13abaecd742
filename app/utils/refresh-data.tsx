import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";

const SetRefreshContext = createContext<{
  startRefreshing: () => void;
  stopRefreshing: () => void;
}>({
  startRefreshing: () => {},
  stopRefreshing: () => {},
});

const ReadRefreshContext = createContext<{
  refreshingCounter: number;
}>({
  refreshingCounter: 0,
});

export function RefreshProvider({ children }: { children: React.ReactNode }) {
  const [refreshingCounter, setRefreshingCounter] = useState(0);

  const startRefreshing = useCallback(() => {
    setRefreshingCounter((prev) => prev + 1);
  }, []);

  const stopRefreshing = useCallback(() => {
    setRefreshingCounter((prev) => prev - 1);
  }, []);

  return (
    <SetRefreshContext.Provider
      value={useMemo(
        () => ({
          startRefreshing,
          stopRefreshing,
        }),
        [startRefreshing, stopRefreshing],
      )}
    >
      <ReadRefreshContext.Provider
        value={useMemo(() => ({ refreshingCounter }), [refreshingCounter])}
      >
        {children}
      </ReadRefreshContext.Provider>
    </SetRefreshContext.Provider>
  );
}

export function useSetRefreshing() {
  if (!SetRefreshContext) {
    throw new Error("RefreshContext is not defined");
  }
  return useContext(SetRefreshContext);
}

export function useReadRefreshing() {
  if (!ReadRefreshContext) {
    throw new Error("ReadRefreshContext is not defined");
  }
  return useContext(ReadRefreshContext);
}
