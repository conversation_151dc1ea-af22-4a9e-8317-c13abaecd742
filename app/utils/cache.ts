// Instead of directly importing stuff from upstash/vercel/kv, use the wrappers
// in this file. These wrappers only work in server-side components.

import { kv } from "@vercel/kv";
import {
  Ratelimit as UpstashRatelimit,
  type RatelimitConfig as UpstashRatelimitConfig,
} from "@upstash/ratelimit";
import { type z } from "zod";

// Whether we can use the `@vercel/kv` cache.
function canUseCache() {
  return process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN;
}

const OPERATION_TIMEOUT_MS = 1000;

export type KVTestingOpts = {
  injectTimeoutSleep?: boolean;
};

async function wrapKvOpInner<T, U>(
  op: () => Promise<T>,
  abortedValue: U,
  testingOpts: KVTestingOpts | undefined,
): Promise<T | U> {
  if (!canUseCache()) {
    return abortedValue;
  }

  const wrappedOp =
    process.env.ALLOW_TESTING_ONLY_CACHE_OPS && testingOpts?.injectTimeoutSleep
      ? async () => {
          await new Promise((resolve) => {
            setTimeout(() => {
              resolve(null);
            }, OPERATION_TIMEOUT_MS * 2);
          });
          return await op();
        }
      : op;

  // In case the cache is down, we time out each operation after a fixed period of
  // time. This is implemented by racing a timeout with the operation.
  const res = await Promise.race([
    wrappedOp(),
    new Promise<U>((resolve) => {
      setTimeout(() => {
        resolve(abortedValue);
      }, OPERATION_TIMEOUT_MS);
    }),
  ]);
  return res;
}

export class KV {
  private _testingOpts: KVTestingOpts | undefined;

  constructor(testingOpts?: KVTestingOpts) {
    this._testingOpts = testingOpts;
  }

  public async get<T extends z.ZodType>(
    key: string,
    paramsSchema: T,
  ): Promise<{ status: "found"; value: z.infer<T> } | { status: "not_found" }> {
    return await this.wrapKvOp(
      async () => {
        const rawValue = await kv.get<unknown>(key);
        if (rawValue === null) {
          return { status: "not_found" };
        }
        const parsedValue = paramsSchema.safeParse(rawValue);
        if (!parsedValue.success) {
          console.error("Failed to parse value from cache", parsedValue.error);
          return { status: "not_found" };
        }
        const value: z.infer<T> = parsedValue.data;
        return { status: "found", value };
      },
      { status: "not_found" },
    );
  }

  public async set<T>(
    key: string,
    value: T,
    testingOpts?: KVSetCommandOptions,
  ): Promise<"ok" | "aborted"> {
    return await this.wrapKvOp(async () => {
      await kv.set(key, value, testingOpts);
      return "ok" as const;
    }, "aborted");
  }

  public async sadd(
    key: string,
    ...members: unknown[]
  ): Promise<"ok" | "aborted"> {
    return await this.wrapKvOp(async () => {
      await kv.sadd(key, ...members);
      return "ok" as const;
    }, "aborted");
  }

  public async expire(key: string, seconds: number): Promise<"ok" | "aborted"> {
    return await this.wrapKvOp(async () => {
      await kv.expire(key, seconds);
      return "ok" as const;
    }, "aborted");
  }

  public async smembers(
    key: string,
  ): Promise<{ status: "ok"; members: string[] } | { status: "aborted" }> {
    return await this.wrapKvOp(
      async () => {
        const members = await kv.smembers(key);
        return { status: "ok", members };
      },
      { status: "aborted" },
    );
  }

  public async del(...args: string[]): Promise<"ok" | "aborted"> {
    return await this.wrapKvOp(async () => {
      await kv.del(...args);
      return "ok" as const;
    }, "aborted");
  }

  private async wrapKvOp<T, U>(
    op: () => Promise<T>,
    abortedValue: U,
  ): Promise<T | U> {
    return await wrapKvOpInner(op, abortedValue, this._testingOpts);
  }
}

export class Ratelimit {
  private _ratelimit: UpstashRatelimit;

  constructor(config: Omit<UpstashRatelimitConfig, "redis">) {
    this._ratelimit = new UpstashRatelimit({
      ...config,
      redis: kv,
    });
  }

  public async limit(key: string) {
    return await wrapKvOpInner(
      async () => {
        const response = await this._ratelimit.limit(key);
        return { status: "ok" as const, response };
      },
      { status: "unavailable" as const },
      undefined,
    );
  }
}

export const rateLimitSlidingWindow = UpstashRatelimit.slidingWindow;

type KVSetArgs = Parameters<typeof kv.set>;
type KVSetCommandOptions = KVSetArgs[2];
