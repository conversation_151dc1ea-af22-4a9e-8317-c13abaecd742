import { csvFormat } from "d3-dsv";
import { serializeJSONWithPlainString } from "./object";
import { stringifyObjectJSON } from "./schema";

export function downloadAsCSV(filename: string, data: unknown[]) {
  const rows = data.map((r) => stringifyObjectJSON("experiment", r));
  const csvData = csvFormat(rows);
  const blob = new Blob([csvData], {
    type: "text/csv;charset=utf-8;",
  });
  const withExtension = filename.endsWith(".csv")
    ? filename
    : `${filename}.csv`;
  return downloadBlob(blob, withExtension);
}

export const downloadAsJSON = (
  filename: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any[],
) => {
  const blob = new Blob([serializeJSONWithPlainString(data)], {
    type: "application/json",
  });
  const withExtension = filename.endsWith(".json")
    ? filename
    : `${filename}.json`;
  return downloadBlob(blob, withExtension);
};

export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
