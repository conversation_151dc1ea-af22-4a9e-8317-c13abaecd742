"use client";

import { useCallback, useEffect, useRef } from "react";

let globalHookId = 0;
export const globalSpecHashSubscriptions: Map<
  string,
  Map<number, () => void>
> = new Map();
function subscribeToSpecHash(
  hookId: number,
  specHash: string,
  updateRefreshed: (specHash: string) => void,
) {
  if (!globalSpecHashSubscriptions.has(specHash)) {
    globalSpecHashSubscriptions.set(specHash, new Map());
  }
  const updateFn = () => updateRefreshed(specHash);
  let existingSubscriptions = globalSpecHashSubscriptions.get(specHash);
  if (!existingSubscriptions) {
    existingSubscriptions = new Map();
    globalSpecHashSubscriptions.set(specHash, existingSubscriptions);
  }
  existingSubscriptions.set(hookId, updateFn);
}

function unsubscribeFromSpecHash(hookId: number, specHash: string) {
  if (!globalSpecHashSubscriptions.has(specHash)) {
    return;
  }
  const existingSubscriptions = globalSpecHashSubscriptions.get(specHash);
  if (!existingSubscriptions) {
    return;
  }
  existingSubscriptions.delete(hookId);
}

export function useUpdateLogSubscriptions({
  updateRefreshed,
}: {
  updateRefreshed: (specHash: string) => void;
}) {
  const hookId = useRef(0);
  if (hookId.current === 0) {
    hookId.current = ++globalHookId;
  }
  const subscriptions = useRef<Set<string>>(new Set());

  useEffect(() => {
    return () => {
      // We specifically _do not_ want to capture this variable, and instead
      // dynamically reference its current value when the effect unmounts.
      const currentSubscriptions = subscriptions.current;
      for (const specHash of currentSubscriptions) {
        unsubscribeFromSpecHash(hookId.current, specHash);
      }
      subscriptions.current = new Set();
    };
  }, []);

  const subscribe = useCallback(
    (specHash: string) => {
      subscribeToSpecHash(hookId.current, specHash, updateRefreshed);
      subscriptions.current.add(specHash);
    },
    [updateRefreshed],
  );

  return { subscribe };
}
