import { useCallback, useEffect, useRef, useState } from "react";

export const useDebouncedCallback = <
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  T extends (...args: any) => unknown | Promise<unknown>,
>(
  callback: T,
  delay: number,
  clearOnUnmount = true,
) => {
  const timeoutRef = useRef<number | null>(null);
  const callbackRef = useRef(callback);
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const debouncedCallback = useCallback(
    async (...args: Parameters<T>) => {
      const { current: currentCallback } = callbackRef;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      return new Promise((resolve) => {
        timeoutRef.current = window.setTimeout(async () => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
          const result = await currentCallback(...(args as any));
          resolve(result);
        }, delay);
      });
    },
    [delay],
  );
  useEffect(() => {
    return () => {
      if (timeoutRef.current && clearOnUnmount) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [clearOnUnmount]);
  return debouncedCallback;
};

export function useDebounce<T>(value: T, delay: number) {
  const activeValue = useRef(value);
  const [, forceUpdate] = useState({});
  const debounced = useDebouncedCallback(
    useCallback(
      (value: T) => {
        activeValue.current = value;
        forceUpdate({});
      },
      [forceUpdate],
    ),
    delay,
  );
  const previousValue = useRef(value);

  // eslint-disable-next-line react-compiler/react-compiler
  if (previousValue.current !== value) {
    debounced(value);
    // eslint-disable-next-line react-compiler/react-compiler
    previousValue.current = value;
  }

  // eslint-disable-next-line react-compiler/react-compiler, @typescript-eslint/consistent-type-assertions
  return activeValue.current as T;
}
