import {
  DataType,
  type Field,
  type Schema,
  Table,
  tableFromJSON,
  builderThroughIterable,
  Bool,
  Int,
  Float,
  Utf8,
  Timestamp,
  List,
  Struct,
  Map_,
} from "apache-arrow";
import { type LogicalSchema } from "@braintrust/btql/schema";
import { parseDateWithMicroseconds } from "./duckdb-helpers";
import { isObject } from "./object";
export type { TransactionId } from "braintrust/util";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function normalizeField(v: any, field: Field): any {
  // This should be solved within arrow, but it isn't
  if (
    DataType.isInt(field.type) &&
    field.type.bitWidth == 64 &&
    typeof v == "number"
  ) {
    // This helps to squash bugs in the backend where we expect integers but might returns a float.
    return BigInt(Number.isInteger(v) ? v : v.toFixed(0));
  } else if (DataType.isTimestamp(field.type) && typeof v == "string") {
    return parseDateWithMicroseconds(v);
  } else if (DataType.isStruct(field.type) && isObject(v)) {
    return field.type.children.reduce(
      (acc, childField) => {
        acc[childField.name] = normalizeField(v[childField.name], childField);
        return acc;
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      {} as Record<string, any>,
    );
  } else if (DataType.isMap(field.type) && isObject(v)) {
    const [key, value] = field.type.children[0].type.children;
    return Object.fromEntries(
      Object.entries(v).map(([k, v]) => [
        normalizeField(k, key),
        normalizeField(v, value),
      ]),
    );
  } else if (DataType.isList(field.type) && Array.isArray(v)) {
    return v.map((v) => normalizeField(v, field.type.children[0]));
  } else if (DataType.isUtf8(field.type) && (isObject(v) || Array.isArray(v))) {
    return JSON.stringify(v);
  } else {
    return v;
  }
}

export function createArrowTableFromRecords(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  records: any[] | null,
  schema: Schema | undefined,
): Table | null {
  if (records === null) {
    return null;
  }
  if (records.length === 0) {
    return schema ? new Table(schema) : null;
  }
  if (!schema) {
    return tableFromJSON(records);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const vecs: Record<string, any> = {};
  for (const field of schema.fields) {
    const builder = builderThroughIterable({
      type: field.type,
      nullValues: [null, undefined],
    });
    const [vec] = builder(
      records.map((r) => r[field.name]).map((v) => normalizeField(v, field)),
    );
    vecs[field.name] = vec;
  }
  return new Table(vecs);
}

export function arrowTypeToLogicalSchema(type: DataType): LogicalSchema {
  if (type instanceof Bool) {
    return { type: "boolean" };
  }
  if (type instanceof Int) {
    return { type: "number" };
  }
  if (type instanceof Float) {
    return { type: "number" };
  }
  if (type instanceof Utf8) {
    return { type: "string" };
  }
  if (type instanceof Timestamp) {
    return { type: "string", format: "date-time" };
  }
  if (type instanceof List) {
    return {
      type: "array",
      items: arrowTypeToLogicalSchema(type.children[0].type),
    };
  }
  if (type instanceof Struct) {
    const properties: Record<string, LogicalSchema> = {};
    for (const field of type.children) {
      properties[field.name] = arrowTypeToLogicalSchema(field.type);
    }
    return {
      type: "object",
      properties,
    };
  }
  if (type instanceof Map_) {
    return {
      type: "object",
      additionalProperties: arrowTypeToLogicalSchema(type.valueType),
    };
  }

  // Default to string for unknown types
  return { type: "string" };
}
