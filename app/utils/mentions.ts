import { resend } from "#/lib/resend";
import CommentMention from "#/ui/email-templates/comment-mention";
import { createElement } from "react";
import { getFromAddress, getSubjectLine } from "#/utils/email/email-utils";
import { checkOrgUpdatePermission } from "#/utils/billing/utils";

export async function sendCommentMention({
  orgId,
  orgName,
  commentText,
  mentionerName,
  emails,
  projectName,
  commentId,
  commentLink,
  entityType,
  entityName,
}: {
  orgId: string | undefined;
  orgName: string;
  commentText: string;
  mentionerName: string;
  emails: string[];
  projectName?: string;
  commentId: string;
  commentLink: string;
  entityType?: string | null;
  entityName?: string | null;
}): Promise<null> {
  if (!resend || emails.length === 0 || !orgId) {
    return null;
  }

  await checkOrgUpdatePermission(
    orgId,
    "Unauthorized to send assignment notifications.",
  );

  const subject = getSubjectLine(orgName, projectName, entityType, entityName);
  const fromAddress = getFromAddress();
  const batchEmails = emails.map((email) => ({
    from: fromAddress,
    to: email,
    subject,
    react: createElement(
      CommentMention,
      {
        mentionerName,
        projectName,
        entityType,
        entityName,
        comment: commentText,
        commentLink,
      },
      null,
    ),
  }));

  const resp = await resend.batch.send(batchEmails, {
    // Prevents duplicate emails from being sent. Resend will not send the email if the idempotencyKey has been used within the last 24 hours.
    idempotencyKey: `${emails.join("-")}-${mentionerName}-${commentId}`,
  });
  if (resp.error) {
    throw new Error(resp.error.message);
  }

  return null;
}
