import { useState } from "react";
import { createApiKey } from "#/app/app/[org]/settings/api-keys/api-key";
import { toast } from "sonner";

interface UseApiKeyGenerationOptions {
  keyNamePrefix?: string;
}

export function useApiKeyGeneration(options: UseApiKeyGenerationOptions = {}) {
  const [generatedApiKey, setGeneratedApiKey] = useState<string | null>(null);
  const [creating, setCreating] = useState(false);

  const createKey = async (orgId: string | undefined, customName?: string) => {
    if (!orgId) {
      toast.error("Failed to create API key", {
        description: "No org ID",
      });
      return null;
    }

    setCreating(true);
    try {
      const keyName =
        customName || `${options.keyNamePrefix || "generated"}-key`;
      const key = await createApi<PERSON>ey({ name: keyName, orgId });
      setGeneratedApiKey(key);
      return key;
    } catch (e) {
      toast.error("Failed to create API key", {
        description: String(e),
      });
      return null;
    } finally {
      setCreating(false);
    }
  };

  const resetKey = () => {
    setGeneratedApiKey(null);
  };

  return {
    generatedApiKey,
    creating,
    createKey,
    resetKey,
  };
}
