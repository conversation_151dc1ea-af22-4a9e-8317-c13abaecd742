import { createDataset } from "#/app/app/[org]/p/[project]/datasets/[dataset]/createDataset";
import { newObjectName } from "#/utils/metadata";
import { useCallback, useRef, useState } from "react";
import { OneLineTextPrompt } from "./one-line-text-prompt";
import { toast } from "sonner";
import { type Span } from "#/ui/trace/graph";

interface CreateDatasetDialogProps {
  orgId?: string | null;
  projectName?: string | null;
  onClose: () => void;
  onSuccessfulCreate: (params: {
    datasetName: string;
    datasetId: string;
    projectName: string;
  }) => void;
  defaultValue?: string;
  open?: boolean;
}

const CreateDatasetDialog = ({
  orgId,
  projectName,
  defaultValue,
  open,
  onClose,
  onSuccessfulCreate,
}: CreateDatasetDialogProps) => {
  return (
    <OneLineTextPrompt
      title="Create dataset"
      fieldName="Name"
      onSubmit={async (name: string) => {
        if (!orgId || !projectName) {
          throw new Error(
            `Missing required inputs: orgId=${orgId}, projectName=${projectName}`,
          );
        }
        const { data, error } = await createDataset({
          orgId,
          projectName,
          datasetName: name,
        });
        onClose();
        const dataset_id: string | null = data?.dataset?.id;
        const datasetName: string | null = data?.dataset?.name;
        if (!dataset_id || !datasetName) {
          toast.error(`Failed to create dataset ${name}`, {
            description: `${error}`,
          });
        } else {
          onSuccessfulCreate({
            datasetName,
            datasetId: dataset_id,
            projectName,
          });
        }
      }}
      onOpenChange={onClose}
      open={!!open}
      defaultValue={defaultValue}
      submitLabel="Create"
    />
  );
};

export const useCreateDatasetDialog = ({
  orgId,
  projectName,
  onSuccessfulCreate,
}: {
  orgId?: string | null;
  projectName?: string | null;
  onSuccessfulCreate: (params: {
    datasetName: string;
    datasetId: string;
    projectName: string;
    getRows?: () => Promise<Span[]>;
  }) => void;
}) => {
  const [newDatasetDialogData, setNewDatasetDialogData] = useState<
    string | null
  >(null);
  const getRowsRef = useRef<(() => Promise<Span[]>) | undefined>(undefined);

  const modal = (
    <CreateDatasetDialog
      orgId={orgId}
      projectName={projectName}
      defaultValue={newDatasetDialogData || newObjectName("Dataset")}
      open={newDatasetDialogData !== null}
      onClose={() => {
        setNewDatasetDialogData(null);
      }}
      onSuccessfulCreate={(params) => {
        const getRows = getRowsRef.current;
        onSuccessfulCreate({
          ...params,
          getRows,
        });
        getRowsRef.current = undefined;
      }}
    />
  );
  const openDialog = useCallback(
    (name: string, getRows?: () => Promise<Span[]>) => {
      setNewDatasetDialogData(name);
      getRowsRef.current = getRows;
    },
    [],
  );
  return {
    modal,
    open: openDialog,
  };
};
