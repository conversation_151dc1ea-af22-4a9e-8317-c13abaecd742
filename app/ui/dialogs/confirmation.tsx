import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import React, { useState } from "react";
import { Button } from "#/ui/button";

export const ConfirmationDialog = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText,
  confirmSecondaryText,
  onConfirm,
  onConfirmSecondary,
  onCancel,
  children,
  keepOpenOnConfirm = false,
}: React.PropsWithChildren<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: React.ReactNode;
  description?: React.ReactNode;
  confirmText: string;
  confirmSecondaryText?: string;
  onConfirm: () => Promise<void> | void;
  onConfirmSecondary?: () => Promise<void> | void;
  onCancel?: () => void;
  keepOpenOnConfirm?: boolean;
}>) => {
  const [confirming, setConfirming] = useState<"primary" | "secondary" | null>(
    null,
  );
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[540px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        {children}
        <DialogFooter>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              onCancel?.();
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          {confirmSecondaryText && (
            <Button
              size="sm"
              variant="ghost"
              onClick={async () => {
                setConfirming("secondary");
                await onConfirmSecondary?.();
                setConfirming(null);
                if (keepOpenOnConfirm) {
                  return;
                }
                onOpenChange(false);
              }}
            >
              {confirmSecondaryText}
            </Button>
          )}
          <Button
            size="sm"
            variant="primary"
            isLoading={confirming === "primary"}
            onClick={async () => {
              setConfirming("primary");
              await onConfirm();
              setConfirming(null);
              if (keepOpenOnConfirm) {
                return;
              }
              onOpenChange(false);
            }}
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
