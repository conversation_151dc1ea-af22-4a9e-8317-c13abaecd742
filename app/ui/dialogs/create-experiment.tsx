import { useState } from "react";
import { OneLineTextPrompt } from "./one-line-text-prompt";
import { newObjectName } from "#/utils/metadata";
import { createExperiment } from "#/app/app/[org]/p/[project]/experiments/[experiment]/createExperiment";
import { toast } from "sonner";

interface CreateExperimentDialogProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  getRegisterExperimentArgs: (experimentName: string) => Record<any, any>;
  onClose: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  onSuccessfulCreate: (newExperiment: Record<any, any>) => void;
  defaultValue?: string;
  open?: boolean;
}

const CreateExperimentDialog = ({
  getRegisterExperimentArgs,
  onClose,
  onSuccessfulCreate,
  defaultValue,
  open,
}: CreateExperimentDialogProps) => {
  return (
    <OneLineTextPrompt
      title="Create experiment"
      fieldName="Name"
      onSubmit={async (name: string) => {
        const registerExperimentArgs = getRegisterExperimentArgs(name);
        const { data, error } = await createExperiment(registerExperimentArgs);
        onClose();
        if (error) {
          toast.error(`Failed to create experiment ${name}`, {
            description: JSON.stringify(error),
          });
        } else {
          onSuccessfulCreate(data["experiment"]);
        }
      }}
      onOpenChange={onClose}
      open={!!open}
      defaultValue={defaultValue}
      submitLabel="Create"
    />
  );
};

export const useCreateExperimentDialog = ({
  getRegisterExperimentArgs,
  onSuccessfulCreate,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  getRegisterExperimentArgs: (experimentName: string) => Record<any, any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  onSuccessfulCreate: (newExperiment: Record<any, any>) => void;
}) => {
  const [newExperimentDialogData, setNewExperimentDialogData] = useState<
    string | null
  >(null);

  const modal = (
    <CreateExperimentDialog
      getRegisterExperimentArgs={getRegisterExperimentArgs}
      onClose={() => setNewExperimentDialogData(null)}
      onSuccessfulCreate={onSuccessfulCreate}
      defaultValue={newExperimentDialogData || newObjectName("Experiment")}
      open={newExperimentDialogData !== null}
    />
  );
  return {
    modal,
    open: (name: string) => setNewExperimentDialogData(name),
  };
};
