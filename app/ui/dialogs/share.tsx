import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useOrg } from "#/utils/user";
import { useState } from "react";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { MultiTenantApiURL } from "#/utils/user-types";
import { RadioGroup, RadioGroupItem } from "#/ui/radio";
import { Copy, Globe2, Lock } from "lucide-react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

export function ShareModal({
  onSubmit,
  name,
  initialValue,
  open,
  onOpenChange,
  orgName,
  onCopyPublicToPrivate,
}: {
  onSubmit: (pub: boolean) => void;
  name: string;
  initialValue: boolean;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orgName: string;
  onCopyPublicToPrivate?: () => void;
}) {
  const [isPublic, setIsPublic] = useState(initialValue);
  const [
    isConfirmingIrreversableMakePublic,
    setIsConfirmingIrreversableMakePublic,
  ] = useState(false);

  const org = useOrg();
  const privateDisabled =
    (initialValue &&
      org.resources?.forbid_toggle_experiment_public_to_private) ??
    undefined;
  const publicDisabled = !(
    org.api_url === MultiTenantApiURL || org.name === "braintrustdata.com"
  );

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <VisuallyHidden>
          <DialogTitle />
          <DialogDescription />
        </VisuallyHidden>
        <DialogContent className="sm:max-w-[480px]">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              // If we are transitioning from private to public and our resource
              // setting prevents us from going back, get an extra confirmation
              // from the user.
              if (
                !initialValue &&
                isPublic &&
                org.resources?.forbid_toggle_experiment_public_to_private
              ) {
                setIsConfirmingIrreversableMakePublic(true);
              } else {
                onSubmit(isPublic);
                onOpenChange(false);
              }
            }}
          >
            <DialogHeader className="mb-6">
              <DialogTitle>Share {name}</DialogTitle>
            </DialogHeader>
            <RadioGroup
              value={isPublic ? "public" : "private"}
              onValueChange={(v) => setIsPublic(v === "public")}
              className="mb-6 flex flex-col gap-5"
            >
              <label className="flex items-center gap-3">
                <RadioGroupItem value="private" disabled={privateDisabled} />
                <div>
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Lock className="size-3" />
                    Private
                  </div>
                  <div className="text-xs font-normal text-primary-600">
                    Only accessible to members of {orgName}
                  </div>
                </div>
              </label>
              <label className="flex items-center gap-3">
                <RadioGroupItem value="public" disabled={publicDisabled} />
                <div>
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Globe2 className="size-3" />
                    Public
                  </div>
                  <div className="text-xs font-normal text-primary-600">
                    Accessible to anyone with the link
                  </div>
                </div>
              </label>
            </RadioGroup>
            <DialogFooter>
              <CopyToClipboardButton
                size="xs"
                variant="ghost"
                textToCopy={window.location.href}
              >
                <Copy className="size-3" /> Copy link
              </CopyToClipboardButton>
              {privateDisabled && onCopyPublicToPrivate && (
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    onCopyPublicToPrivate();
                    onOpenChange(false);
                  }}
                >
                  Copy to private experiment
                </Button>
              )}
              <Button type="submit" variant="primary" size="xs">
                Save
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      <ConfirmationDialog
        open={isConfirmingIrreversableMakePublic}
        onOpenChange={setIsConfirmingIrreversableMakePublic}
        title={"Confirm make public"}
        description={`Are you sure you want to mark ${name} public? It cannot be reverted back to private.`}
        confirmText={"Confirm"}
        onConfirm={() => {
          onSubmit(isPublic);
          onOpenChange(false);
        }}
      />
    </>
  );
}
