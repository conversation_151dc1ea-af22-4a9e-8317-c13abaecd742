import { OneLineTextPrompt } from "./one-line-text-prompt";

export function ChangeNameDialog(props: {
  onSubmit: (newValue: string) => void;
  title?: string;
  description?: string;
  fieldName: React.ReactNode;
  defaultValue?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const { title, ...otherProps } = props;
  return (
    <OneLineTextPrompt
      title={title || "Rename"}
      submitLabel="Save"
      {...otherProps}
    />
  );
}
