"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "#/ui/button";
import { TriangleAlert } from "lucide-react";
import Link from "next/link";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { useAPIVersion } from "./check-api-version";
import { useOrg } from "#/utils/user";
import { MultiTenantApiURL } from "#/utils/user-types";
import { useIsClient } from "#/utils/use-is-client";

export const Healthcheck = () => {
  const org = useOrg();
  const { isLoading, error } = useAPIVersion();

  const isClient = useIsClient();
  const [closed, setClosed] = useState(false);

  if (!isClient || closed || isLoading || !error) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="xs"
          className="mr-1 h-8 gap-1.5 border-bad-50 bg-bad-50/50 text-bad-700 hover:bg-bad-50 hover:text-bad-700"
          Icon={TriangleAlert}
        >
          Connection failed
        </Button>
      </PopoverTrigger>
      <PopoverContent className="text-xs" align="end">
        <div className="mb-2">
          Your browser is unable to connect to the data plane at{" "}
          <span className="font-mono">{org.api_url}</span>.
          {org.api_url === MultiTenantApiURL ? (
            <div>
              <p>
                Please check your internet connection and reach out to{" "}
                <Link href="mailto:<EMAIL>">
                  <EMAIL>
                </Link>{" "}
                if you are still having issues.
              </p>
            </div>
          ) : org.name === "Netflix" ? (
            <div>
              {/* Eventually we should generalize this so users can control it */}
              <p>
                Please check your internet connection or VPN (if applicable),  try refreshing the page,
                and reach out to the team on #braintrust-netflix in Slack if you
                are still having issues.
              </p>
            </div>
          ) : (
            <div>
              <p>
                Please check your internet connection or VPN (if applicable),
                and reach out to your Braintrust administrator if you are still
                having issues.
              </p>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            size="xs"
            onClick={() => {
              setClosed(true);
            }}
          >
            Ignore
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
