// Should eventually match the version in api-ts/src/api.ts.
//
// Typically the release process where feature flagging new functionality is involved:
// Update api-ts version AND include webapp code that checks this version (via feature flags)
// Deploy UI, make sure nothing breaks
// Deploy data plane, make sure nothing breaks
// Increment version in the UI
const versionString = process.env.BRAINTRUST_VERSION ?? "v0.0.0";
export const LATEST_VERSION = versionString.startsWith("v")
  ? versionString.slice(1)
  : versionString;
