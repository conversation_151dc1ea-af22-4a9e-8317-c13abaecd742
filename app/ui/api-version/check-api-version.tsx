"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from "react";

import {
  useEntityStorage,
  useEntityStorageAccess,
} from "#/lib/clientDataStorage";
import { apiFetchGet } from "#/utils/btapi/fetch";
import * as semver from "semver";
import { Button, buttonVariants } from "#/ui/button";
import { TriangleAlert } from "lucide-react";
import { LATEST_VERSION } from "./api-version";
import { MultiTenantApiURL } from "#/utils/user-types";
import Link from "next/link";
import { useOrg } from "#/utils/user";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { useSessionToken } from "#/utils/auth/session-token";
import { z } from "zod";
import { useWarmupCodeExecution } from "./warmup-code-execution";
import { useAnalytics } from "#/ui/use-analytics";
import { useQuery } from "@tanstack/react-query";

export const apiVersionSchema = z.object({
  version: z.string(),
  commit: z.string().optional(),
  deployment_mode: z.string().optional(),
  universal: z.boolean().default(false),
  code_execution: z.boolean().default(false),
  brainstore_default: z.boolean().default(false),
  brainstore_can_contain_row_refs: z.boolean().default(false),
  has_logs2: z.boolean().default(false),
});
export type APIVersionInfo = z.infer<typeof apiVersionSchema>;

// Timeout (in milliseconds) for the API version fetch.
// Keep this as a single source of truth so it can be reused both for the
// AbortController timeout and the resulting error message.
const API_VERSION_FETCH_TIMEOUT_MS = 10_000;

export function useAPIVersion() {
  const org = useOrg();
  // Get non-reactive access to the API version in local storage
  const { get, set } = useEntityStorageAccess<"org", "apiVersion3">({
    entityType: "org",
    entityIdentifier: org.id || "",
  });
  // Get the initial value of the API version from local storage
  const initialLocalApiVersion = useMemo(() => get("apiVersion3"), [get]);

  const { getOrRefreshToken } = useSessionToken();
  const { analytics } = useAnalytics();

  const {
    data: serverApiVersion,
    isLoading,
    refetch,
    error,
  } = useQuery({
    queryKey: ["apiVersion3", org.api_url, org.id],
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();

      const controller = new AbortController();
      const timeout = setTimeout(
        () => controller.abort(),
        API_VERSION_FETCH_TIMEOUT_MS,
      );
      let response: Response;
      try {
        response = await apiFetchGet(
          `${org.api_url}/version`,
          sessionToken,
          controller.signal,
        );
      } catch (err) {
        // If the request was aborted, surface a clearer timeout error.
        if (err instanceof Error && err.name === "AbortError") {
          throw new Error(
            `Fetching API version timed out after ${
              API_VERSION_FETCH_TIMEOUT_MS / 1000
            }s. The API is likely unreachable.`,
          );
        }
        throw err;
      } finally {
        clearTimeout(timeout);
      }
      if (!response.ok) {
        throw new Error(
          `Failed to fetch API version (${response.status} ${response.statusText}): ${await response.text()}`,
        );
      }
      const ret = apiVersionSchema.parse(await response.json());

      analytics?.track("api-version-check", {
        api_version: ret,
        org_name: org.name,
        api_url: org.api_url,
        webapp_version: LATEST_VERSION,
      });

      set("apiVersion3", ret);

      return ret;
    },
    staleTime: Infinity, // this can stay cached for the duration of the session
    meta: {
      disableGlobalErrorToast: true,
    },
  });

  const apiVersion = serverApiVersion ?? initialLocalApiVersion;

  const refresh = useCallback(async () => {
    refetch();
  }, [refetch]);

  return { ...apiVersion, refresh, isLoading, error };
}

const PATCH_VERSION_DRIFT = 4;

export const CheckApiVersion = () => {
  const org = useOrg();
  const [storedWebappVersion, setStoredWebappVersion] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.id || "",
    key: "webappVersion",
  });
  const [closed, setClosed] = useState(storedWebappVersion == LATEST_VERSION);

  const { version: apiVersion, isLoading } = useAPIVersion();

  const [loaded, setLoaded] = useState(false);
  useEffect(() => {
    setLoaded(true);
  }, []);

  // This warms up code execution. It's cheap if it's already done, but also cached.
  useWarmupCodeExecution();

  // Check if API version is more than 4 minor versions behind
  const shouldShowWarning = useMemo(() => {
    if (
      !apiVersion ||
      !semver.valid(apiVersion) ||
      !semver.valid(LATEST_VERSION)
    ) {
      return false;
    }

    const apiMajor = semver.major(apiVersion);
    const apiMinor = semver.minor(apiVersion);
    const apiPatch = semver.patch(apiVersion);
    const latestMajor = semver.major(LATEST_VERSION);
    const latestMinor = semver.minor(LATEST_VERSION);
    const latestPatch = semver.patch(LATEST_VERSION);

    // If major versions differ, always show warning
    if (apiMajor < latestMajor) {
      return true;
    } else if (apiMajor > latestMajor) {
      return false;
    }

    if (apiMinor < latestMinor) {
      return true;
    } else if (apiMinor > latestMinor) {
      return false;
    }

    // If same major and minor, check patch version
    return latestPatch - apiPatch > PATCH_VERSION_DRIFT;
  }, [apiVersion]);

  if (
    closed ||
    !loaded ||
    org.api_url === MultiTenantApiURL ||
    !shouldShowWarning ||
    isLoading
  ) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="xs"
          className="mr-1 h-8 gap-1.5 border-amber-200 bg-amber-50 hover:bg-amber-100 dark:border-amber-900 dark:bg-amber-950 dark:hover:bg-amber-900"
        >
          <TriangleAlert size={14} />
          Update API
        </Button>
      </PopoverTrigger>
      <PopoverContent className="text-xs" align="end">
        <div className="mb-2">
          Your API version <span className="font-mono">{apiVersion}</span> is
          behind webapp version{" "}
          <span className="font-mono">{LATEST_VERSION}</span>. Please update
          your API to the latest version.
        </div>
        <div className="flex gap-2">
          <Link
            className={buttonVariants({ size: "xs" })}
            href="/docs/self-hosting"
            target="_blank"
          >
            View docs
          </Link>
          <Button
            size="xs"
            onClick={() => {
              setStoredWebappVersion(LATEST_VERSION);
              setClosed(true);
            }}
          >
            Ignore
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
