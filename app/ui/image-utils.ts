import { chatCompletionContentPartImageSchema } from "@braintrust/typespecs";
import { anthropicContentPartImageSchema } from "@braintrust/proxy/types";
import { isBase64Image } from "@braintrust/local/functions";

export function getImageString(data: unknown): string {
  if (typeof data === "string") {
    return data;
  }
  const parseResult = chatCompletionContentPartImageSchema.safeParse(data);
  if (parseResult.success) {
    return parseResult.data.image_url.url;
  }

  const anthropicParseResult = anthropicContentPartImageSchema.safeParse(data);
  if (anthropicParseResult.success) {
    if (anthropicParseResult.data.source.type === "base64") {
      return anthropicParseResult.data.source.data;
    }
    if (anthropicParseResult.data.source.type === "url") {
      return anthropicParseResult.data.source.url;
    }
  }

  throw new Error("Unable to parse image data");
}

const baseImageUrlPattern =
  /https?:\/\/.*\.(?:jpg|jpeg|png|gif|bmp|webp)(?:\?.*)?/;
const imageUrlPattern = new RegExp(`^${baseImageUrlPattern.source}$`, "i");
export const imageUrlSplitPattern = new RegExp(
  `(\\b${baseImageUrlPattern.source}\\b)`,
  "i",
);

export function isImageURL(s: string): boolean {
  return imageUrlPattern.test(s);
}

// Being as specific as possible about allowable characters and avoiding greedy matching
// helps avoid catastrophic backtracking: https://github.com/braintrustdata/braintrust/pull/4831
const base64ContentTypePattern =
  /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9+.-]+);base64,/;
export function getContentType(s: string): string | undefined {
  const parts = s.match(base64ContentTypePattern);
  return parts?.[1].toLocaleLowerCase() ?? undefined;
}

export { isBase64Image };
