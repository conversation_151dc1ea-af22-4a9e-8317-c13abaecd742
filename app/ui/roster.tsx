import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { type Roster } from "#/utils/realtime-data";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Avatar } from "./avatar";

export function RosterAvatars({
  roster,
  context,
}: {
  roster: Roster;
  context: string;
}) {
  return (
    <div className="flex flex-row gap-1">
      {roster.map((e) => (
        <Tooltip key={e.user_id}>
          <TooltipTrigger asChild>
            <div>
              <Avatar key={e.user_id} imgUrl={e.avatar_url} />
            </div>
          </TooltipTrigger>
          {e.email && (
            <TooltipPortal>
              <TooltipContent className="text-sm">
                {e.email} is currently viewing this {context}
              </TooltipContent>
            </TooltipPortal>
          )}
        </Tooltip>
      ))}
    </div>
  );
}
