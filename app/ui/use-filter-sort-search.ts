import {
  type Clause<PERSON><PERSON><PERSON>,
  type ClauseSpec,
  type ClauseType,
  type Search,
  addClause,
  makeBubble,
  remove<PERSON>lause,
  noopChe<PERSON>,
} from "#/utils/search/search";

import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
} from "react";
import { toast } from "sonner";
import useEvent from "react-use-event-hook";

export type ApplySearch = (
  query: string | null,
  clauseToRemove?: ClauseSpec<ClauseType>,
  opts?: {
    originType?: "form" | "btql";
    label?: string;
  },
) => Promise<void>;

const useFilterSortBarSearch = ({
  clauseChecker,
  setSearch,
}: {
  clauseChecker: ClauseChecker | null;
  setSearch?: Dispatch<SetStateAction<Search>>;
}) => {
  const disableFilters =
    clauseChecker === null || clauseChecker === noopChecker;
  const registerClause = useMemo(
    () =>
      clauseChecker
        ? async (
            newClause: ClauseSpec<ClauseType>,
            clauseToRemove?: ClauseSpec<ClauseType>,
          ) => {
            const checkResult = await clause<PERSON>hecker(newClause);
            if (checkResult.type === "checked") {
              setSearch?.((s) => {
                let newSearch = { ...s };
                if (clauseToRemove) {
                  newSearch = removeClause(newSearch, clauseToRemove);
                }
                const updated = addClause(newSearch, {
                  ...newClause,
                  ...checkResult.extraFields,
                  bubble: makeBubble({ clause: newClause, setSearch }),
                });
                return updated;
              });
            }
            return checkResult;
          }
        : null,
    [clauseChecker, setSearch],
  );

  const isValidBTQL = useCallback(
    async (query: string | null, restrictType?: string) => {
      if (!query || !clauseChecker) {
        return { valid: false, error: "No query provided" };
      }

      const filterResult = await clauseChecker({
        type: "filter",
        text: query,
      });

      if (
        restrictType &&
        restrictType === "filter" &&
        filterResult.type === "error"
      ) {
        return { valid: false, error: filterResult.error };
      }
      if (filterResult.type === "checked") {
        return { valid: true, type: "filter" };
      }

      const sortResult = await clauseChecker({
        type: "sort",
        text: query,
      });
      if (sortResult.type === "checked") {
        return { valid: true, type: "sort" };
      }

      const tagResult = await clauseChecker({
        type: "tag",
        text: query,
      });
      if (tagResult.type === "checked") {
        return { valid: true, type: "tag" };
      }

      return {
        valid: false,
        error: "Invalid BTQL expression",
      };
    },
    [clauseChecker],
  );

  const applySearch = useEvent(
    async (
      query: string | null,
      clauseToRemove?: ClauseSpec<ClauseType>,
      opts?: {
        originType?: "form" | "btql";
        label?: string;
        comparison?: {
          experimentId: string;
        };
      },
    ) => {
      if (!query || !registerClause) {
        return;
      }

      const checkFilterResult = await registerClause(
        {
          type: "filter",
          text: query,
          label: opts?.label,
          originType: opts?.originType,
          comparison: opts?.comparison,
        },
        clauseToRemove,
      );
      if (checkFilterResult.type !== "error") {
        // If the original query is already a valid SQL filter,
        // then we don't need to run AI search.
        return;
      }

      const checkTagResult = await registerClause(
        {
          type: "tag",
          text: query,
          label: opts?.label,
          originType: opts?.originType,
          comparison: opts?.comparison,
        },
        clauseToRemove,
      );
      if (checkTagResult.type !== "error") {
        // If the original query is already a valid SQL tag,
        // then we don't need to run AI search.
        return;
      }

      // Show error for invalid BTQL queries - no AI fallback
      toast.error(checkFilterResult.error);
      return;
    },
  );

  return {
    applySearch,
    isValidBTQL,
    disableFilters,
  };
};

export default useFilterSortBarSearch;
