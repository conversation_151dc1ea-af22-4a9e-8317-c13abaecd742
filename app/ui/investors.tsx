const investors = [
  { name: "<PERSON>", title: "CEO", company: "<PERSON><PERSON><PERSON>" },
  /* { name: "<PERSON><PERSON><PERSON>" }, */
  { name: "<PERSON><PERSON><PERSON>", title: "COO", company: "Notion" },
  { name: "<PERSON>", title: "Founder", company: "<PERSON><PERSON><PERSON> Dettmer" },
  {
    name: "<PERSON><PERSON>",
    title: "Co-founder & Chief Architect",
    company: "Ironclad",
  },
  { name: "Clem Delangue", title: "CEO", company: "HuggingFace" },
  { name: "<PERSON>", title: "Head of AI product", company: "Coda" },
  { name: "<PERSON>", title: "<PERSON><PERSON>", company: "Stripe" },
  { name: "<PERSON>", title: "CEO", company: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", title: "CEO", company: "Instacart" },
  {
    name: "<PERSON>",
    title: "Co-founder and President",
    company: "OpenAI",
  },
  { name: "<PERSON><PERSON>", title: "CEO", company: "Airtable" },
  { name: "<PERSON>", title: "CEO", company: "<PERSON><PERSON><PERSON>" },
  {
    name: "<PERSON>",
    title: "Head of Strategic Accounts",
    company: "OpenAI",
  },
  /* { name: "<PERSON><PERSON>" }, */
  { name: "<PERSON>", title: "COO", company: "<PERSON>ip<PERSON>" },
  { name: "<PERSON> <PERSON>", title: "CEO", company: "Alteryx" },
  { name: "Merci Victoria <PERSON>", title: "CEO", company: "Panobi" },
  { name: "<PERSON><PERSON>", title: "VP of AI", company: "Repl.it" },
  { name: "Mike Knoop", title: "Co-founder and Head of AI", company: "Zapier" },
  { name: "Olivier Pomel", title: "CEO", company: "Datadog" },
  { name: "Nik Koblov", title: "Head of Engineering", company: "Ramp" },
  { name: "Nikita Shamgunov", title: "CEO", company: "Neon" },
  { name: "Paul Copplestone", title: "CEO", company: "Supabase" },
  { name: "Scott Belsky", title: "Founder", company: "Behance" },
  { name: "Qasar Younis", title: "CEO", company: "Applied Intuition" },

  /* { name: "Zach Frankel", title: "CEO", company: "Ramp" }, */
];
export default function Investors() {
  return (
    <div className="mt-6 grid grid-cols-3 gap-4">
      {investors.map((investor, i) => (
        <Investor key={i} {...investor} />
      ))}
    </div>
  );
}

export function Investor({
  name,
  title,
  company,
}: {
  name: string;
  title?: string;
  company?: string;
}) {
  return (
    <div className="flex flex-col text-sm md:h-12">
      <div>
        <div className="font-bold">{name}</div>
        {title && company && (
          <div className="text-sm">
            {title}, {company}
          </div>
        )}
      </div>
    </div>
  );
}
