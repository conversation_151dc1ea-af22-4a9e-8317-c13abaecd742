import { isEmpty } from "#/utils/object";
import {
  type CopilotCurrentCellContext,
  type CopilotEditorContext,
  slimmedFunctionSchema,
} from "@braintrust/local/copilot";
import { type UIFunction } from "#/ui/prompts/schema";

export type SlimmedUIFunctionArg = Pick<
  UIFunction,
  "id" | "name" | "metadata" | "prompt_data" | "function_data"
>;

export const FUNCTION_BUFFER_SIZE = 2;

export type MakeCopilotContextFn = (
  cellCtx: CopilotCurrentCellContext,
) => CopilotEditorContext;

export abstract class CopilotContextBuilder {
  private recentFunctions: CopilotEditorContext["recentFunctions"] = [];

  constructor() {
    if (typeof window !== "undefined") {
      // @ts-ignore
      window.copilotContext = this;
    }
  }

  public visitPrompt(fn: SlimmedUIFunctionArg): boolean {
    const parsed = slimmedFunctionSchema.safeParse(fn);
    if (!parsed.success) {
      console.error("Invalid prompt", parsed.error);
    }
    if (
      !parsed.success ||
      (fn.function_data.type === "code" &&
        fn.function_data.data.type === "bundle") ||
      (fn.function_data.type === "global" && isEmpty(fn.prompt_data?.prompt))
    ) {
      // Nothing to display here
      return this.recentFunctions.length >= FUNCTION_BUFFER_SIZE;
    }

    this.recentFunctions = [...this.recentFunctions]
      .filter((p) => p.id !== parsed.data.id)
      .concat([parsed.data])
      .slice(-FUNCTION_BUFFER_SIZE);

    return this.recentFunctions.length >= FUNCTION_BUFFER_SIZE;
  }

  protected getRecentFunctions(): CopilotEditorContext["recentFunctions"] {
    return [...this.recentFunctions];
  }

  public abstract makeCopilotContext(
    context: CopilotCurrentCellContext,
  ): CopilotEditorContext;
}
