import { use<PERSON>emo } from "react";
import { type Span, type SpanData, type LoadedTrace } from "#/ui/trace/graph";
import {
  type CopilotCurrentCellContext,
  type CopilotEditorContext,
} from "@braintrust/local/copilot";
import { Copilot<PERSON>ontextBuilder } from "./context";

interface TraceCopilotContext {
  rootSpanId: string;
  spanContexts: Record<string, SpanCopilotContext>;
  idToName: Record<string, string>;
  lastVisitedSpanId: string;
}

interface SpanCopilotContext {
  data: SpanData;
}

// NOTE: This is just a heuristic. We should probably use a more sophisticated buffer based on the size
const TRACE_BUFFER_SIZE = 3;

export class MultiTraceContext extends CopilotContextBuilder {
  private traceContexts: TraceCopilotContext[] = [];
  private objectType: string | null = null;
  private objectName: string | null = null;

  setObjectType(objectType: string) {
    this.objectType = objectType;
  }

  setObjectName(objectName: string) {
    this.objectName = objectName;
  }

  public visitTrace(trace: LoadedTrace) {
    // Compute the span contexts
    const spanCtx = bfsSpans(trace.root, new Set());

    const newContexts = [...this.traceContexts]
      .filter((ctx) => ctx.rootSpanId !== trace.root.id)
      .concat([spanCtx])
      .slice(-TRACE_BUFFER_SIZE);

    this.traceContexts = newContexts;
  }

  public visitSpan(span: Span) {
    for (const ctx of this.traceContexts) {
      if (ctx.spanContexts[span.id]) {
        ctx.spanContexts[span.id] = {
          data: span.data,
        };
        ctx.lastVisitedSpanId = span.id;
        break;
      }
    }
  }

  public makeCopilotContext(
    context: CopilotCurrentCellContext,
  ): CopilotEditorContext {
    const ret: CopilotEditorContext = {
      recentRows: [],
      recentFunctions: [],
      currentCell: context,
    };

    if (ret.currentCell.type === "row") {
      ret.currentCell = {
        ...ret.currentCell,
        objectType: this.objectType,
        objectName: this.objectName,
        row: null,
      };
    }

    // The other fields from the highest priority span are the most important
    // Followed by the same field from other equivalent spans in other traces
    // Followed by other fields in other traces
    const currentTrace = this.traceContexts[this.traceContexts.length - 1];
    if (!currentTrace) {
      return ret;
    }

    const currentSpanName =
      currentTrace.idToName[currentTrace.lastVisitedSpanId];
    const currentSpan =
      currentSpanName && currentTrace.spanContexts[currentSpanName];
    if (currentSpan && ret.currentCell.type === "row") {
      ret.currentCell.row = extractAutocompleteFields(currentSpan.data);
    }

    if (currentSpanName) {
      for (const ctx of this.traceContexts.slice(0, -1)) {
        const span = ctx.spanContexts[currentSpanName];
        ret.recentRows.push(extractAutocompleteFields(span.data));
      }
    }

    return ret;
  }
}

export function useTraceCopilotContext({
  objectType,
  objectName,
}: {
  objectType: string;
  objectName: string;
}) {
  // TODO: We may want to save this in local storage instead
  const multiTraceContext = useMemo<MultiTraceContext>(
    () => new MultiTraceContext(),
    [],
  );
  multiTraceContext.setObjectType(objectType);
  multiTraceContext.setObjectName(objectName);

  return multiTraceContext;
}

function bfsSpans(root: Span, seen: Set<string>): TraceCopilotContext {
  const names: Record<string, number> = {};
  const idToName: Record<string, string> = {};

  const queue: { span: Span; level: number }[] = [{ span: root, level: 0 }];
  const spanContexts: { name: string; data: unknown; level: number }[] = [];
  let maxLevel = 0;
  while (queue.length > 0) {
    const curr = queue.shift();
    if (!curr) break;

    const { span, level } = curr;
    if (seen.has(span.id)) continue;

    seen.add(span.id);

    let name = span.data.span_attributes.name ?? "";
    if (names[name] === undefined) {
      names[name] = 0;
    } else {
      names[name]++;
      name = `${name}_${names[name]}`;
    }
    idToName[span.id] = name;

    spanContexts.push({
      name,
      data: span.data,
      level,
    });
    maxLevel = Math.max(maxLevel, level);
    queue.push(
      ...span.children.map((child) => ({
        span: child,
        level: level + 1,
      })),
    );
  }

  return {
    rootSpanId: root.id,
    lastVisitedSpanId: root.id,
    spanContexts: Object.fromEntries(
      spanContexts.map((spanContext) => [
        spanContext.name,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        {
          data: spanContext.data,
        } as SpanCopilotContext,
      ]),
    ),
    idToName,
  };
}

const AUTOCOMPLETE_FIELDS = [
  "input",
  "output",
  "error",
  "expected",
  "metadata",
  "span_attributes",
  "tags",
];

function extractAutocompleteFields(
  row: Record<string, unknown>,
): Record<string, unknown> {
  return Object.fromEntries(
    Object.entries(row).filter(([key, _]) => AUTOCOMPLETE_FIELDS.includes(key)),
  );
}
