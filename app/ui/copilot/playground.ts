import {
  type CopilotCurrentCellContext,
  type CopilotEditorContext,
} from "@braintrust/local/copilot";
import { useMemo } from "react";
import { CopilotContextBuilder } from "./context";

const DATA_ROW_BUFFER_SIZE = 3;

export class PlaygroundCopilotContext extends CopilotContextBuilder {
  private recentRows: CopilotEditorContext["recentRows"] = [];

  public visitDataRow(row: Record<string, unknown>): boolean {
    this.recentRows = [...this.recentRows]
      .filter((row) => row.id !== row.id)
      .concat([row])
      .slice(-DATA_ROW_BUFFER_SIZE);
    return this.recentRows.length >= DATA_ROW_BUFFER_SIZE;
  }

  public makeCopilotContext(
    context: CopilotCurrentCellContext,
  ): CopilotEditorContext {
    const recentFunctions = this.getRecentFunctions();
    const currentCell: CopilotCurrentCellContext =
      context.type === "function"
        ? {
            type: "function",
            functionType: context.functionType,
            function: recentFunctions.pop(),
          }
        : context;
    return {
      recentRows: [...this.recentRows],
      recentFunctions,
      currentCell,
    };
  }
}

export function usePlaygroundCopilotContext(): PlaygroundCopilotContext {
  const playgroundContext = useMemo<PlaygroundCopilotContext>(
    () => new PlaygroundCopilotContext(),
    [],
  );

  return playgroundContext;
}
