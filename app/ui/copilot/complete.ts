import {
  isAuthenticatedSession,
  sessionFetchProps,
  useSessionToken,
} from "#/utils/auth/session-token";
import { proxyV1Url } from "#/utils/url";
import { useOrg, useUser } from "#/utils/user";
import { useCallback } from "react";
import {
  type AutoCompleteArgs,
  type AutoCompleteContext,
  type AutocompleteResponse,
  runAutocomplete as runAutocompleteFull,
} from "@braintrust/local/copilot";
import { shouldLog } from "#/utils/observability";
import { type FeedbackArgs } from "#/app/api/copilot/util";
import { useAvailableModels } from "#/ui/prompts/models";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useFeatureFlags } from "#/lib/feature-flags";

export const PRIORITIZED_MODELS = [
  "gpt-4o-2024-08-06",
  "claude-3-5-sonnet-20240620",
  "gpt-4o",
];

export function deriveCopilotModel(
  model: string | undefined,
  availableModels: ReturnType<typeof useAvailableModels>["allAvailableModels"],
) {
  if (model && availableModels[model]) {
    return model;
  }
  for (const model of PRIORITIZED_MODELS) {
    if (availableModels[model]) {
      return model;
    }
  }
  return null;
}

export function useCopilotState() {
  const user = useUser();
  const org = useOrg();
  const proxyUrl = org.proxy_url;
  const { getOrRefreshToken } = useSessionToken();

  const [copilotModel, _setCopilotModel] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.name,
    key: "copilotModel",
  });
  const { allAvailableModels, noConfiguredSecrets } = useAvailableModels({
    orgName: org.name,
  });
  const selectedModel = deriveCopilotModel(copilotModel, allAvailableModels);

  const {
    flags: { copilot: copilotEnabled },
  } = useFeatureFlags();

  const runAutocomplete = useCallback(
    async (context: AutoCompleteContext): Promise<AutocompleteResponse> => {
      if (
        !isAuthenticatedSession(await getOrRefreshToken()) ||
        !selectedModel ||
        noConfiguredSecrets ||
        !copilotEnabled
      ) {
        return { completion: "" };
      }
      const { sessionHeaders } = sessionFetchProps(await getOrRefreshToken());
      const args: AutoCompleteArgs = {
        context,
        credentials: {
          userId: user.user?.id ?? null,
          email: user.user?.email ?? null,
          orgName: org.name,
          baseUrl: proxyV1Url(proxyUrl),
          authHeaders: sessionHeaders,
          model: selectedModel,
        },
      };
      const completionPromise = shouldLog(org.api_url)
        ? (async () => {
            const result = await fetch("/api/copilot/complete", {
              method: "POST",
              body: JSON.stringify(args),
            });
            return await result.json();
          })()
        : runAutocompleteFull(args);
      return await completionPromise;
    },
    [
      copilotEnabled,
      noConfiguredSecrets,
      org.api_url,
      org.name,
      proxyUrl,
      selectedModel,
      user.user?.email,
      user.user?.id,
      getOrRefreshToken,
    ],
  );

  return {
    runAutocomplete,
  };
}

export async function saveFeedback(args: FeedbackArgs) {
  await fetch("/api/copilot/feedback", {
    method: "POST",
    body: JSON.stringify(args),
  });
}
