import { type ReactNode, forwardRef, useEffect, useRef } from "react";
import { cn } from "#/utils/classnames";

export const Checkbox = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: ReactNode;
    inputClassName?: string;
    textClassName?: string;
  }
>(function Checkbox(props, ref) {
  const { className, label, inputClassName, textClassName, ...otherProps } =
    props;

  // Avoid a warning from React when providing a 'checked' prop, but
  // onChange is undefined and neither readOnly nor defaultChecked are provided
  let readOnly = otherProps.readOnly;
  if (
    otherProps.checked !== undefined &&
    otherProps.onChange === undefined &&
    otherProps.defaultChecked === undefined &&
    readOnly === undefined
  ) {
    readOnly = true;
  }

  return (
    <label className={cn("flex items-center text-sm font-medium", className)}>
      <input
        className={cn(
          "h-4 w-4 rounded-sm border-primary-300 bg-primary-100 text-accent-500 transition-colors checked:border-accent-600 checked:bg-accent-600 hover:border-primary-400 checked:hover:border-accent-800",
          "cursor-pointer indeterminate:border-accent-500 indeterminate:bg-accent-500 indeterminate:hover:border-accent-800 focus:ring-2 focus:ring-accent-400",
          otherProps.disabled && "pointer-events-none opacity-50",
          inputClassName,
        )}
        type="checkbox"
        {...otherProps}
        readOnly={readOnly}
        ref={ref}
      />
      {label ? (
        <span className={cn("pl-2", textClassName)}>{label}</span>
      ) : null}
    </label>
  );
});

export const IndeterminateCheckbox = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: string;
    state?: "checked" | "unchecked" | "indeterminate";
  }
>(function IndeterminateCheckbox(props, ref) {
  const { label, state, checked, ...otherProps } = props;
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!inputRef?.current) return;
    switch (state) {
      case "checked":
        inputRef.current.indeterminate = false;
        inputRef.current.checked = true;
        break;
      case "unchecked":
        inputRef.current.indeterminate = false;
        inputRef.current.checked = false;
        break;
      case "indeterminate":
        inputRef.current.indeterminate = true;
        break;
      case undefined:
        inputRef.current.indeterminate = false;
        inputRef.current.checked = checked || false;
        break;
      default:
        break;
    }
  }, [state, checked, inputRef]);

  return <Checkbox label={label} ref={inputRef} {...otherProps} />;
});
