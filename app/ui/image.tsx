import { cn } from "#/utils/classnames";
import { ImageOff } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip";
import {
  type AttachmentReference,
  attachmentReferenceSchema,
} from "@braintrust/typespecs";
import { type Key } from "react";
import { getImageString, isImageURL, isBase64Image } from "./image-utils";
import { AttachmentItem } from "./attachment/attachment-list";

export function safeParseImage({
  data,
  alt,
  key,
}: {
  data: unknown;
  alt?: string;
  key?: Key;
}) {
  try {
    return parseImage({ data, alt, key });
  } catch {
    return (
      <div className="flex items-center gap-2 rounded-md border p-2" key={key}>
        <ImageOff className="size-3 text-primary-400" />
      </div>
    );
  }
}

export function parseImage({
  data,
  alt,
  key,
}: {
  data: unknown;
  alt?: string;
  key?: Key;
}) {
  const imageString = getImageString(data);
  const src = getImageSrc(imageString);

  if (src.type === "attachment") {
    return <AttachmentItem mode="preview" extracted={src.attachment} />;
  } else {
    return makeImageElement({
      src: src.url,
      alt: alt ?? (typeof data === "string" ? data : JSON.stringify(data)),
      key,
    });
  }
}

type ImageSource =
  | {
      type: "url";
      url: string;
    }
  | {
      type: "attachment";
      attachment: AttachmentReference;
    };

function getImageSrc(image: string): ImageSource {
  if (isImageURL(image)) {
    return { type: "url", url: image };
  }
  /*
  Only permit base64 images for now
  if (isBase64Data(image)) {
    return `data:image/jpeg;base64,${image}`;
  }
  */
  if (isBase64Image(image)) {
    return { type: "url", url: image };
  }

  try {
    const parsed = attachmentReferenceSchema.parse(JSON.parse(image));
    return { type: "attachment", attachment: parsed };
  } catch {
    // fall through
  }

  throw new Error("Invalid image string");
}

export function makeImageElement({
  src,
  alt,
  key,
}: {
  src: string;
  alt?: string;
  key?: Key;
}) {
  // Maybe add a placeholder image in case it's slow to load or invalid
  return (
    <div className="inline-flex align-top" key={key}>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <img
            src={src}
            alt={alt}
            className={`mx-0 my-1 h-16 max-w-32 self-center`}
          />
        </TooltipTrigger>
        <TooltipContent side="top" className={cn("border-none bg-background")}>
          <img
            src={src}
            alt={alt}
            className={`m-0 max-h-[800px] max-w-[800px] self-center`}
          />
        </TooltipContent>
      </Tooltip>
    </div>
  );
}
