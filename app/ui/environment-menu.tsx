import React, { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Button } from "#/ui/button";
import { Layers, Settings2 } from "lucide-react";
import {
  useUpsertEnvironmentAssociationMutation,
  useDeleteEnvironmentAssociationMutation,
} from "#/utils/environments/use-environment-associations";
import { useAtomValue } from "jotai/react";
import {
  environmentsAtom,
  useEnvironmentSlugsForVersionAtom,
} from "#/utils/environments/atoms";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { toast } from "sonner";
import { type Environment } from "@braintrust/typespecs";
import Link from "next/link";
import { getOrgSettingsLink } from "#/app/app/[org]/getOrgLink";
import { useOrg } from "#/utils/user";
import { BasicTooltip } from "./tooltip";

export function EnvironmentMenu({
  versionId,
  objectId,
  objectType,
  versionEnvironments,
}: {
  versionId: string;
  objectId: string;
  objectType: string;
  versionEnvironments: Environment[];
}) {
  const org = useOrg();
  const [open, setOpen] = useState<boolean>(false);
  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");

  // Get data from atoms
  const environments = useAtomValue(environmentsAtom);
  const environmentSlugsAtom = useEnvironmentSlugsForVersionAtom(
    objectType,
    objectId,
    versionId,
  );
  const currentEnvironmentSlugs = useAtomValue(environmentSlugsAtom);

  const upsertMutation = useUpsertEnvironmentAssociationMutation({
    objectType,
    objectId,
  });
  const deleteMutation = useDeleteEnvironmentAssociationMutation({
    objectType,
    objectId,
  });

  // Don't render if environments are disabled
  if (!isEnvironmentsEnabled) {
    return null;
  }

  const handleAssignEnvironment = async (environmentSlug: string) => {
    try {
      await upsertMutation.mutateAsync({
        environmentSlug,
        object_version: versionId,
      });
    } catch (error) {
      toast.error("Failed to assign environment tag", {
        description: `${error}`,
      });
    }
  };

  const handleUnassignEnvironment = async (environmentSlug: string) => {
    try {
      await deleteMutation.mutateAsync(environmentSlug);
    } catch (error) {
      toast.error("Failed to unassign environment tag", {
        description: `${error}`,
      });
    }
  };

  return (
    <div className="flex items-center gap-1">
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger onClick={(e) => e.stopPropagation()} asChild>
          <Button
            Icon={Layers}
            className="size-5 min-w-0 text-primary-400 [&>svg]:size-3"
            size="xs"
            variant="ghost"
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuLabel>Environment tags</DropdownMenuLabel>
          {environments.length === 0 && (
            <DropdownMenuItem disabled>None</DropdownMenuItem>
          )}
          {environments.map((env) => {
            const isAssigned = currentEnvironmentSlugs.includes(env.slug);
            return (
              <BasicTooltip
                key={env.id}
                side="right"
                tooltipContent={env.description}
              >
                <div>
                  <DropdownMenuCheckboxItem
                    checked={isAssigned}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (isAssigned) {
                        handleUnassignEnvironment(env.slug);
                      } else {
                        handleAssignEnvironment(env.slug);
                      }
                    }}
                  >
                    <span className="flex flex-1 items-center justify-between gap-2">
                      {env.name}
                      <span className="font-mono text-xs text-primary-500">
                        {env.slug}
                      </span>
                    </span>
                  </DropdownMenuCheckboxItem>
                </div>
              </BasicTooltip>
            );
          })}
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Link
              href={`${getOrgSettingsLink({
                orgName: org.name,
              })}/environments`}
              className="flex items-center gap-2"
            >
              <Settings2 className="size-3" />
              Configure environments
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {versionEnvironments.length > 0 &&
        versionEnvironments.map((env) => (
          <BasicTooltip
            key={env.id}
            tooltipContent={
              <span className="flex flex-col text-primary-600">
                <span className="block font-medium text-primary-800">
                  {env.name}
                </span>
                {env.description}
              </span>
            }
          >
            <span className="rounded-xs bg-good-100 px-1.5 font-mono text-xs text-good-900">
              {env.slug}
            </span>
          </BasicTooltip>
        ))}
    </div>
  );
}
