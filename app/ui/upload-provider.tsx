"use client";
import { pluralizeWithCount } from "#/utils/plurals";
import {
  createContext,
  type Dispatch,
  useEffect,
  useState,
  type SetStateAction,
  useContext,
} from "react";
import { Progress } from "./progress";
import { toast } from "sonner";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useQueryClient } from "@tanstack/react-query";

export const UploadContext = createContext<{
  importProgress: ImportProgressType;
  setImportProgress?: Dispatch<SetStateAction<ImportProgressType>>;
  executeImport: (params: {
    data: Record<string, unknown>[];
    setImportProgress: (params: Partial<ImportProgressType>) => void;
    insertRows: (rows: Record<string, unknown>[]) => Promise<void>;
  }) => Promise<void>;
}>({
  importProgress: {
    currentState: "not started",
    successfullyImportedRowsNumber: 0,
    totalRows: 0,
    rowsFailedToImportNumber: 0,
  },
  executeImport: async () => {},
});

export function UploadProvider({ children }: { children: React.ReactNode }) {
  const [importProgress, setImportProgress] = useState<ImportProgressType>({
    currentState: "not started",
    successfullyImportedRowsNumber: 0,
    totalRows: 0,
    rowsFailedToImportNumber: 0,
  });

  const { mutateDatasets } = useContext(ProjectContext);
  const queryClient = useQueryClient();

  useEffect(() => {
    const renderFailedIfExist = () =>
      importProgress.rowsFailedToImportNumber > 0 ? (
        <div>
          Failed to import{" "}
          {pluralizeWithCount(
            importProgress.rowsFailedToImportNumber,
            "row",
            "rows",
          )}
        </div>
      ) : null;

    // Show or update toast based on import state
    if (importProgress.currentState === "in progress") {
      toast.loading("Importing CSV data", {
        id: "import-progress",
        duration: Infinity,
        description: (
          <div className="w-full">
            <Progress
              className="h-2"
              value={
                ((importProgress.successfullyImportedRowsNumber +
                  importProgress.rowsFailedToImportNumber) /
                  importProgress.totalRows) *
                100
              }
            />
            <div className="mt-1 tabular-nums">
              {importProgress.successfullyImportedRowsNumber} of{" "}
              {pluralizeWithCount(importProgress.totalRows, "row", "rows")}{" "}
              imported. Please do not close or refresh the page.
            </div>
            {renderFailedIfExist()}
          </div>
        ),
      });
    } else if (importProgress.currentState === "finished") {
      mutateDatasets();
      toast.dismiss("import-progress");
      toast.dismiss("import-failed");
      toast.success(
        `Successfully imported ${pluralizeWithCount(
          importProgress.successfullyImportedRowsNumber,
          "row",
          "rows",
        )}`,
        {
          id: "import-complete",
          description: renderFailedIfExist(),
          duration: 3000,
        },
      );
      setImportProgress({
        ...importProgress,
        currentState: "closed",
      });
      queryClient.invalidateQueries({
        queryKey: ["summaryPaginatedObjectViewer"],
      });
    } else if (importProgress.currentState === "closed") {
      toast.dismiss("import-progress");
    } else if (importProgress.currentState === "failed") {
      toast.dismiss("import-progress");
      toast.error("Dataset import failed", {
        id: "import-failed",
        description: (
          <>
            Please contact{" "}
            <a href="mailto:<EMAIL>" className="underline">
              <EMAIL>
            </a>{" "}
            for help
          </>
        ),
        duration: 3000,
      });
      setImportProgress({
        ...importProgress,
        currentState: "closed",
      });
    }
  }, [importProgress, mutateDatasets, queryClient]);

  return (
    <UploadContext.Provider
      value={{ importProgress, setImportProgress, executeImport }}
    >
      {children}
    </UploadContext.Provider>
  );
}

const executeImport = async (params: {
  data: Record<string, unknown>[];
  setImportProgress: (params: Partial<ImportProgressType>) => void;
  insertRows: (rows: Record<string, unknown>[]) => Promise<void>;
}) => {
  // The beforeUnloadHandler is here to help during the import process.
  // Since the import happens in the browser, a user might close the tab by mistake.
  // This handler asks the user to double-check before closing, stopping any accidental interruptions.
  // It's a safety net that makes the import process safer.

  const beforeUnloadHandler = (e: BeforeUnloadEvent) => {
    e.preventDefault();
    e.returnValue = "";
  };
  window.addEventListener("beforeunload", beforeUnloadHandler);

  const { data, setImportProgress, insertRows } = params;

  const ROW_SPLIT_GROUP_NUMBER = 10;
  const splittedRows = data.reduce<Record<string, unknown>[][]>(
    (acc, row) => {
      const lastGroup = acc[acc.length - 1];
      if (lastGroup.length >= ROW_SPLIT_GROUP_NUMBER) {
        acc.push([row]);
      } else {
        lastGroup.push(row);
      }
      return acc;
    },
    [[]],
  );
  let inserted = 0;
  setImportProgress({
    currentState: "in progress",
    successfullyImportedRowsNumber: 0,
    totalRows: data.length,
    rowsFailedToImportNumber: 0,
  });
  try {
    for (const group of splittedRows) {
      await insertRows(group);
      inserted += group.length;
      setImportProgress({
        currentState: "in progress",
        successfullyImportedRowsNumber: inserted,
      });
    }
    setImportProgress({
      currentState: "finished",
    });
  } catch (e) {
    console.error(e);
    setImportProgress({
      currentState: "failed",
    });
  }
  window.removeEventListener("beforeunload", beforeUnloadHandler);
};

export type ImportStatus =
  | "not started"
  | "in progress"
  | "finished"
  | "error"
  | "closed"
  | "failed";

export interface ImportProgressType {
  currentState: ImportStatus;
  successfullyImportedRowsNumber: number;
  totalRows: number;
  rowsFailedToImportNumber: number;
}
