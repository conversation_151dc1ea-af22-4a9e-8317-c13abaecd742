import { TIME_RANGE_OPTIONS } from "#/app/app/[org]/monitor/time-controls/time-range";

export interface TimeRangeOption {
  value: string;
  label: string;
  isLive: boolean;
}

export const ALL_TIME_RANGE = "all";

const ALL_TIME_OPTION: TimeRangeOption = {
  value: ALL_TIME_RANGE,
  label: "All time",
  isLive: false,
};

export const getTimeRangeOptions = ({
  allowAllTime = false,
}: {
  allowAllTime?: boolean;
}): TimeRangeOption[] => {
  if (allowAllTime) {
    return [...TIME_RANGE_OPTIONS, ALL_TIME_OPTION];
  }
  return [...TIME_RANGE_OPTIONS];
};
