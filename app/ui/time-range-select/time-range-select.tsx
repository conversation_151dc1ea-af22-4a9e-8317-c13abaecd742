import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { Calendar } from "#/ui/calendar";
import { addMinutes, endOfDay, startOfDay } from "date-fns";
import { PLAN_SLUGS, useOrgPlan } from "#/app/app/[org]/settings/billing/plans";
import { type TimeRangeFilter as TimeRangeFilterType } from "#/utils/view/use-view";
import { isEmpty } from "#/utils/object";
import { TimeRangePill } from "./time-range-pill";
import { ALL_TIME_RANGE, getTimeRangeOptions } from "./get-time-range-options";
import {
  DEFAULT_TIME_RANGE,
  TIME_RANGE_TO_MILLISECONDS,
} from "#/app/app/[org]/monitor/time-controls/time-range";
import { getTimeRangeCustomString } from "./get-time-range-custom-string";
import { useCallback, useMemo } from "react";
import { type DateRange } from "react-day-picker";
import { parseDateString } from "./parse-date-string";
import { UTCDate } from "@date-fns/utc";
import { type TimeRangeOption } from "./get-time-range-options";

export const FREE_LOOKBACK_LIMIT = 14 * 24 * 60 * 60 * 1000; // two weeks

export const TimeRangeSelect = ({
  context,
  allowAllTime,
  value,
  setValue,
  isUTC,
  setIsUTC,
  isLiveOverride,
  onDisableLiveOverride,
}: {
  context: "monitor" | "logs";
  allowAllTime?: boolean;
  value: TimeRangeFilterType;
  setValue: (value: TimeRangeFilterType) => void;
  isUTC?: boolean;
  setIsUTC?: (isUTC: boolean) => void;
  isLiveOverride?: boolean;
  onDisableLiveOverride?: () => void;
}) => {
  const orgPlan = useOrgPlan();
  const isFreeOrg = orgPlan === PLAN_SLUGS.FREE;
  const isCustom = typeof value === "object";

  const availableOptions = getTimeRangeOptions({ allowAllTime });
  const selectedDefaultRange = !isCustom
    ? availableOptions.find((r) => r.value === value)
    : undefined;

  const isLogsContext = context === "logs";
  const restrictLookback = isFreeOrg && isLogsContext; // free on logs page is restricted
  const isLive = isLogsContext && selectedDefaultRange?.isLive;
  const isUTCEnabled = !isEmpty(isUTC) && !isEmpty(setIsUTC);
  const isLiveToggleEnabled = isLiveOverride && !isEmpty(onDisableLiveOverride);

  const dropdownItems = useMemo(() => {
    {
      return availableOptions.map((r) => (
        <TimeRangeSelectItem
          key={r.value}
          option={r}
          onSelect={setValue}
          isUTC={isUTC}
          isLogsContext={isLogsContext}
          isFreeOrg={isFreeOrg}
          allowAllTime={!!allowAllTime}
          restrictLookback={restrictLookback}
        />
      ));
    }
  }, [
    availableOptions,
    isFreeOrg,
    restrictLookback,
    isLogsContext,
    allowAllTime,
    isUTC,
    setValue,
  ]);

  const calOnSelect = useCallback(
    (dateRange?: DateRange) => {
      if (!dateRange) {
        return;
      }
      const { from, to } = dateRange;

      if (!from) {
        return;
      }

      const offset = isUTC ? -new Date().getTimezoneOffset() : 0;

      const fromDate = isUTC ? new UTCDate(from) : new Date(from);
      const fromStartOfDay = startOfDay(
        addMinutes(fromDate, offset),
      ).toISOString();

      const toDate = isUTC ? new UTCDate(to ?? from) : new Date(to ?? from);
      const toEndOfDay = endOfDay(addMinutes(toDate, offset)).toISOString();

      setValue({ from: fromStartOfDay, to: toEndOfDay });
    },
    [setValue, isUTC],
  );

  const calSelectedValue = useMemo(() => {
    if (!isCustom) {
      return undefined;
    }

    const offset = isUTC ? new Date().getTimezoneOffset() : 0;

    const fromDate = parseDateString(value.from);
    const toDate = parseDateString(value.to);

    const fromStartDay = startOfDay(isUTC ? new UTCDate(fromDate) : fromDate);
    const toEndDay = endOfDay(isUTC ? new UTCDate(toDate) : toDate);

    return {
      from: addMinutes(new Date(fromStartDay), offset),
      to: addMinutes(new Date(toEndDay), offset),
    };
  }, [isCustom, value, isUTC]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown>
          {isCustom ? (
            getTimeRangeCustomString({ value, isUTC })
          ) : selectedDefaultRange ? (
            <>
              <TimeRangePill isLive={isLive || isLiveOverride}>
                {selectedDefaultRange.value === ALL_TIME_RANGE
                  ? "All"
                  : selectedDefaultRange.value}
              </TimeRangePill>
              {selectedDefaultRange.label === "All time" ? "" : "Past "}
              {selectedDefaultRange.label}
            </>
          ) : (
            <>
              <TimeRangePill isLive={isLive}>
                {DEFAULT_TIME_RANGE!.value}
              </TimeRangePill>
              Past {DEFAULT_TIME_RANGE!.label}
            </>
          )}
          {isUTC && <span className="text-[10px] text-primary-500">UTC</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="max-w-xs!">
        {dropdownItems}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>Custom</DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <Calendar
              mode="range"
              numberOfMonths={2}
              selected={calSelectedValue}
              onSelect={calOnSelect}
              disabled={{
                before: restrictLookback
                  ? startOfDay(new Date(Date.now() - FREE_LOOKBACK_LIMIT))
                  : undefined,
                after: endOfDay(new Date()),
              }}
            />
          </DropdownMenuSubContent>
        </DropdownMenuSub>
        {restrictLookback && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>
              Data retention is limited to 14 days for organizations on the Free
              plan
            </DropdownMenuLabel>
          </>
        )}
        {isLiveToggleEnabled && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem
              checked={isLiveOverride}
              onCheckedChange={onDisableLiveOverride}
              className="gap-1"
            >
              <TimeRangePill isLive /> updates
            </DropdownMenuCheckboxItem>
          </>
        )}
        {isUTCEnabled && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuCheckboxItem
              checked={isUTC}
              onCheckedChange={setIsUTC}
              onSelect={(e) => {
                e.preventDefault();
              }}
            >
              Use UTC time
            </DropdownMenuCheckboxItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const TimeRangeSelectItem = ({
  option,
  onSelect,
  isUTC,
  isLogsContext,
  isFreeOrg,
  allowAllTime,
  restrictLookback,
}: {
  option: TimeRangeOption;
  onSelect: (value: TimeRangeFilterType) => void;
  isUTC: boolean | undefined;
  isLogsContext: boolean;
  isFreeOrg: boolean;
  allowAllTime: boolean;
  restrictLookback: boolean;
}) => {
  return (
    <DropdownMenuItem
      key={option.value}
      onClick={() => onSelect(option.value)}
      className="group"
      disabled={
        (restrictLookback &&
          (TIME_RANGE_TO_MILLISECONDS[option.value] > FREE_LOOKBACK_LIMIT ||
            option.value === ALL_TIME_RANGE)) ||
        (isFreeOrg && !allowAllTime && option.value === ALL_TIME_RANGE)
      }
    >
      <TimeRangePill isLive={isLogsContext && option.isLive}>
        {option.value === ALL_TIME_RANGE ? "All" : option.value}
      </TimeRangePill>
      <span className="flex-1">
        {option.label === "All time" ? "" : "Past "}
        {option.label}
      </span>
      {isUTC && (
        <span className="flex-none text-[10px] text-primary-500">UTC</span>
      )}
    </DropdownMenuItem>
  );
};
