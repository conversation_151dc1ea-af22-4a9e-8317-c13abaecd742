import { describe, test, expect, vi, afterEach, beforeEach } from "vitest";
import { getTimeRangeCustomString } from "./get-time-range-custom-string";

// Mock the parseDateString function to return predictable dates
vi.mock("./parse-date-string", () => ({
  parseDateString: (dateString: string) => new Date(dateString),
}));

describe("getTimeRangeCustomString", () => {
  // 1. Enable fake timers before each test
  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2023-12-27T10:00:00Z"));
  });

  // 3. Restore real timers after each test
  afterEach(() => {
    vi.useRealTimers();
  });

  // --- Test Suite 1: Short duration, within the same day ---
  describe("Short duration (same day)", () => {
    const value = {
      from: "2023-10-27T10:00:00Z",
      to: "2023-10-27T14:30:00Z",
    };

    test("should format correctly for local time (am/pm)", () => {
      // Note: The exact output of 'p' can vary by environment/locale.
      // This test assumes a US-style locale.
      const result = getTimeRangeCustomString({ value });
      expect(result).toMatch(/Oct 27 (10:00 AM|6:00 AM) - (2:30 PM|10:30 AM)/); // Accommodates different local timezones
    });

    test("should format correctly for UTC time (24hr)", () => {
      const result = getTimeRangeCustomString({ value, isUTC: true });
      expect(result).toBe("Oct 27 10:00 - 14:30");
    });
  });

  // --- Test Suite 2: Medium duration, includes time but not year ---
  describe("Medium duration (< 7 days)", () => {
    const value = {
      from: "2023-10-27T22:00:00Z",
      to: "2023-10-29T12:00:00Z",
    };

    test("should format with date and time for local time", () => {
      const result = getTimeRangeCustomString({ value });
      expect(result).toMatch(
        /Oct 27 (10:00 PM|6:00 PM) - Oct 29 (12:00 PM|8:00 AM)/,
      );
    });

    test("should format with date and time for UTC time", () => {
      const result = getTimeRangeCustomString({ value, isUTC: true });
      expect(result).toBe("Oct 27 22:00 - Oct 29 12:00");
    });
  });

  // --- Test Suite 3: Long duration, excludes time but includes year ---
  describe("Long duration (> 30 days)", () => {
    const value = {
      from: "2023-10-27T10:00:00Z",
      to: "2023-12-25T17:00:00Z",
    };

    test("should format with full date and year for local time", () => {
      const result = getTimeRangeCustomString({ value });
      expect(result).toBe("Oct 27, 2023 - Dec 25, 2023");
    });

    test("should format with full date and year for UTC time", () => {
      const result = getTimeRangeCustomString({ value, isUTC: true });
      expect(result).toBe("Oct 27, 2023 - Dec 25, 2023");
    });
  });

  // --- Test Suite 4: Intermediate duration, date only, no year/time ---
  describe("Intermediate duration (between 7 and 30 days)", () => {
    const value = {
      from: "2023-10-20T10:00:00Z",
      to: "2023-11-15T17:00:00Z",
    };

    test("should format with date only, no year or time", () => {
      const result = getTimeRangeCustomString({ value });
      expect(result).toBe("Oct 20 - Nov 15");
    });
  });

  // --- Test Suite 5: Edge case with midnight times ---
  describe("Midnight Times (00:00)", () => {
    const value = {
      from: "2023-11-01T00:00:00", // Using local time for this test
      to: "2023-11-03T00:00:00", // Using local time for this test
    };

    test("should hide time for local time when hours/minutes are zero", () => {
      const result = getTimeRangeCustomString({ value, isUTC: false });
      expect(result).toBe("Nov 1 - Nov 2");
    });

    test("should still show time for UTC even if hours/minutes are zero", () => {
      const utcValue = {
        from: "2023-11-01T00:00:00Z",
        to: "2023-11-03T00:00:00Z",
      };
      const result = getTimeRangeCustomString({ value: utcValue, isUTC: true });
      expect(result).toBe("Nov 1 00:00 - Nov 2 23:59");
    });
  });
});
