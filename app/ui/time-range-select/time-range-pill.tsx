import { cn } from "#/utils/classnames";

export const TimeRangePill = ({
  children,
  isLive,
}: {
  children?: React.ReactNode;
  isLive?: boolean;
}) => {
  return (
    <span
      className={cn(
        "mr-0.5 w-8 rounded-[3px] bg-primary-200 py-px text-center text-[11px] font-normal text-primary-600 transition-colors group-hover:bg-primary-300",
        {
          "bg-accent-100 text-[10px] text-accent-700 group-hover:bg-accent-200":
            isLive,
        },
      )}
    >
      {isLive ? "LIVE" : children}
    </span>
  );
};
