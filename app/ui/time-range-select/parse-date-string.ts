import { endOfDay, parse, parseISO } from "date-fns";

export const parseDateString = (dateString: string, wholeDayEnd?: boolean) => {
  if (!dateString) return new Date(NaN);
  if (dateString.includes("T")) {
    const d = parseISO(dateString);
    if (!Number.isNaN(d.getTime())) return d;
  }
  const day = parse(dateString, "yyyy-MM-dd", new Date());

  // for cases where using an end date
  // use the end of the parsed whole day
  if (wholeDayEnd) {
    return endOfDay(day);
  }

  return day;
};
