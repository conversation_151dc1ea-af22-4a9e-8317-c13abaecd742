import { UTCDate } from "@date-fns/utc";
import { parseDateString } from "./parse-date-string";
import { format, getYear, subMilliseconds, startOfDay } from "date-fns";
import {
  SHOW_TIME_LIMIT,
  SHOW_YEAR_LIMIT,
} from "#/app/app/[org]/monitor/time-controls/time-controls.constants";

const isHMZero = (date: Date) =>
  date.getHours() === 0 && date.getMinutes() === 0;

export const getTimeRangeCustomString = ({
  value,
  isUTC = false,
}: {
  value: { from: string; to: string };
  isUTC?: boolean;
}): string => {
  const fromDate = parseDateString(value.from);
  const parsedToDate = parseDateString(value.to);

  // if to is at the boundary of a day, sub one ms for display
  const normedToDate = isUTC ? new UTCDate(parsedToDate) : parsedToDate;
  const isAtDayBoundary =
    startOfDay(normedToDate).getTime() === normedToDate.getTime();
  const toDate = isAtDayBoundary
    ? subMilliseconds(parsedToDate, 1)
    : parsedToDate;

  const durationMs = toDate.getTime() - fromDate.getTime();

  // Check if both times are 00:00 (only hide time for non-UTC)
  const bothTimesAreZero =
    !isUTC && isHMZero(fromDate) && isHMZero(parsedToDate);

  // include time if below limit and not all hours and minutes zero
  const includeTime = durationMs <= SHOW_TIME_LIMIT && !bothTimesAreZero;

  // show year if span long enough or start at different year from current
  const showYear =
    durationMs >= SHOW_YEAR_LIMIT || getYear(new Date()) !== getYear(fromDate);

  // enforce 24hr for UTC but otherwise use local
  const fmtStringTimeOnly = isUTC ? "HH:mm" : "p";
  const fmtStringDate = showYear ? "MMM d, yyyy" : "MMM d";
  const fmtStringLong = includeTime
    ? `${fmtStringDate} ${fmtStringTimeOnly}`
    : fmtStringDate;

  const fmt = (d: Date, fmtString: string) => {
    if (isUTC) {
      return format(new UTCDate(d), fmtString);
    }
    return format(d, fmtString);
  };

  const formattedFrom = fmt(fromDate, fmtStringLong);
  const formattedTo = fmt(toDate, fmtStringLong);

  // if formatted from and to are the same, only show one
  if (bothTimesAreZero && formattedFrom === formattedTo) {
    return formattedFrom;
  }

  // Check if dates are on the same day
  const fmtStringDateOnly = "yyyy-MM-dd";
  const fromDateOnly = fmt(fromDate, fmtStringDateOnly);
  const toDateOnly = fmt(toDate, fmtStringDateOnly);
  const isSameDay = fromDateOnly === toDateOnly;

  // if the same day and showing time, only show time for to portion
  const shortFormattedTo =
    isSameDay && includeTime ? fmt(toDate, fmtStringTimeOnly) : formattedTo;

  return `${formattedFrom} - ${shortFormattedTo}`;
};
