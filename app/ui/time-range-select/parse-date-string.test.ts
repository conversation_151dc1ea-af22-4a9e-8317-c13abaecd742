import { describe, test, expect, beforeAll, afterAll, vi } from "vitest";
import { parseDateString } from "./parse-date-string"; // Adjust the import path as needed

// Mock the current date to ensure tests are deterministic
const MOCK_DATE = new Date("2023-10-27T10:00:00.000Z");

describe("parseDateString", () => {
  beforeAll(() => {
    vi.useFakeTimers();
    vi.setSystemTime(MOCK_DATE);
  });

  afterAll(() => {
    vi.useRealTimers();
  });

  // Test case 1: Handles valid ISO 8601 date-time strings
  test('should correctly parse a valid ISO 8601 string with "T"', () => {
    const dateString = "2023-05-15T14:30:00.000Z";
    const expectedDate = new Date(dateString);
    expect(parseDateString(dateString)).toEqual(expectedDate);
  });

  // Test case 2: Handles valid "yyyy-MM-dd" date strings
  test('should correctly parse a valid "yyyy-MM-dd" string', () => {
    const dateString = "2023-08-21";
    // parse will create a date at the beginning of the day in the local timezone
    const expectedDate = new Date(2023, 7, 21); // Month is 0-indexed
    const result = parseDateString(dateString);

    // Compare year, month, and day to avoid timezone issues in the test runner
    expect(result.getFullYear()).toBe(expectedDate.getFullYear());
    expect(result.getMonth()).toBe(expectedDate.getMonth());
    expect(result.getDate()).toBe(expectedDate.getDate());
  });

  // Test case 3: Handles null or undefined input
  test("should return an invalid date for null or undefined input", () => {
    // @ts-ignore to test runtime behavior with invalid types
    expect(parseDateString(null).getTime()).toBeNaN();
    // @ts-ignore
    expect(parseDateString(undefined).getTime()).toBeNaN();
  });

  // Test case 4: Handles empty string input
  test("should return an invalid date for an empty string", () => {
    expect(parseDateString("").getTime()).toBeNaN();
  });

  // Test case 5: Handles malformed "T" strings that are not valid ISO
  test('should fall back to yyyy-MM-dd parsing for malformed "T" strings', () => {
    const dateString = "2023-10-T-Invalid";
    // This will be parsed as an invalid date by both parseISO and parse
    const result = parseDateString(dateString);
    expect(result.getTime()).toBeNaN();
  });

  // Test case 6: Handles completely invalid or garbage strings
  test("should return an invalid date for a garbage string", () => {
    const dateString = "this is not a date";
    const result = parseDateString(dateString);
    expect(result.getTime()).toBeNaN();
  });

  // Test case 7: Handles "yyyy-MM-dd" strings with incorrect components
  test("should return an invalid date for an invalid yyyy-MM-dd string", () => {
    const dateString = "2023-99-99"; // Invalid month and day
    const result = parseDateString(dateString);
    expect(result.getTime()).toBeNaN();
  });
});
