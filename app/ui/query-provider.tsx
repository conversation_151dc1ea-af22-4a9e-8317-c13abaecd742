"use client";
import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
} from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import * as Sentry from "@sentry/nextjs";

// You can add more options as needed to be consistent with your original usage
function makeQueryClient() {
  return new QueryClient({
    queryCache: new QueryCache({
      onError: (err, query) => {
        // https://tkdodo.eu/blog/react-query-error-handling#the-global-callbacks
        if (query.meta?.disableGlobalErrorCallback) {
          return;
        }
        if (!query.meta?.disableGlobalErrorToast) {
          toast.error("Error", { description: err.message });
        }

        if (!query.meta?.disableGlobalErrorConsole) {
          console.error("Failed to run query", err);
        }
        if (query.meta?.globalErrorSentryContext) {
          Sentry.captureException(err, query.meta.globalErrorSentryContext);
        }
      },
    }),
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000,
        retry: false,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        placeholderData: (prev: any) => prev,
      },
    },
  });
}

// UseState assures a stable instance per page session
export function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(() => makeQueryClient());
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
