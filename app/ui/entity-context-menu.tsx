import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import {
  Bug,
  Edit3,
  ShieldCheck,
  Trash,
  FileJson,
  FileSpreadsheet,
  CylinderIcon,
  Clipboard,
  Ellipsis,
} from "lucide-react";
import { type ReactNode, useMemo, useState } from "react";
import { ObjectPermissionsDialog } from "#/app/app/[org]/p/[project]/permissions/object-permissions-dialog";
import { type AclObjectType } from "@braintrust/typespecs";
import { downloadAsCSV, downloadAsJSON } from "#/utils/download";
import { toast } from "sonner";
import { backfillableObjectTypeSchema } from "@braintrust/local/app-schema";
import { BrainstoreObjectConfiguration } from "#/app/app/[org]/p/[project]/brainstore/[object]/brainstore-object-configuration";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useBrainstoreBackfillState } from "./query-parameters";
import { type GetRowsForExportFn } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/table-queries";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { cn } from "#/utils/classnames";

export const EntityContextMenu = ({
  objectName,
  objectId,
  objectType,
  orgName,
  projectName,
  handleEdit,
  handleDelete,
  handleCopyId,
  handleCopyName,
  extraOptions,
  buttonClassName,
  renderDropdownItemsOnly,
  excludePermissions,
  getRowsForExport,
  getRawData,
  exportName,
  onlyExportingLoadedRows,
  refetchDataQueryFn,
  isFiltering,
  columnVisibility,
  isReadOnly,
}: {
  objectName: string;
  objectId: string;
  objectType: AclObjectType;
  orgName: string;
  projectName: string;
  handleEdit?: VoidFunction;
  handleDelete?: VoidFunction;
  handleCopyId?: VoidFunction;
  handleCopyName?: VoidFunction;
  buttonClassName?: string;
  extraOptions?: {
    label: ReactNode;
    disabled?: boolean;
    onClick: (() => void) | (() => Promise<void>);
  }[];
  renderDropdownItemsOnly?: boolean;
  excludePermissions?: boolean;
  getRowsForExport?: GetRowsForExportFn;
  getRawData?: () => Promise<unknown[] | undefined>;
  exportName?: string;
  onlyExportingLoadedRows?: boolean;
  refetchDataQueryFn?: (
    rowIds?: string[],
    withFilter?: boolean,
  ) => ParsedQuery | undefined;
  isFiltering?: boolean;
  columnVisibility?: Record<string, boolean>;
  isReadOnly?: boolean;
}) => {
  const [isPermissionsOpen, setIsPermissionsOpen] = useState(false);
  const [isBackfillStatusOpen, setIsBackfillStatusOpen] =
    useBrainstoreBackfillState();

  const backfillableObjectType = useMemo(() => {
    const ordinaryObjectType =
      objectType === "project_log"
        ? "project_logs"
        : objectType === "prompt_session"
          ? "playground_logs"
          : objectType;
    return backfillableObjectTypeSchema.safeParse(ordinaryObjectType);
  }, [objectType]);

  const {
    flags: { brainstore },
  } = useFeatureFlags();

  const label =
    objectType === "prompt_session"
      ? "playground"
      : objectType === "project_log"
        ? "project"
        : objectType;
  const items = (
    <>
      {!isReadOnly && handleEdit && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleEdit();
          }}
        >
          <Edit3 className="size-3" /> Rename {label}
        </DropdownMenuItem>
      )}
      {!isReadOnly && getRowsForExport && (
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <>
              <FileSpreadsheet className="size-3" />
              {onlyExportingLoadedRows
                ? "Download loaded rows"
                : "Download rows"}
            </>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {[
              {
                title: "Download as CSV",
                columnVisibility,
                downloadFn: downloadAsCSV,
                IconComponent: FileSpreadsheet,
              },
              {
                title: "Download as JSON",
                columnVisibility,
                downloadFn: downloadAsJSON,
                IconComponent: FileJson,
              },
              ...(Object.keys(columnVisibility ?? {}).length > 0
                ? [
                    {
                      title: "Download as CSV (all fields)",
                      downloadFn: downloadAsCSV,
                      IconComponent: FileSpreadsheet,
                    },
                    {
                      title: "Download as JSON (all fields)",
                      downloadFn: downloadAsJSON,
                      IconComponent: FileJson,
                    },
                  ]
                : []),
            ].map((item, i) => (
              <DropdownMenuItem
                key={i}
                onClick={() => {
                  toast.promise(
                    async () => {
                      try {
                        const refetchQuery = refetchDataQueryFn
                          ? refetchDataQueryFn(undefined, isFiltering)
                          : undefined;
                        const data = await getRowsForExport({
                          refetchDataQuery: refetchQuery,
                          columnVisibility: item.columnVisibility,
                        });
                        if (!data) {
                          toast.error(`Failed to download ${objectType}`);
                          return;
                        }
                        item.downloadFn(
                          exportName ?? objectName ?? objectType,
                          data,
                        );
                      } catch (error) {
                        console.error(error);
                        throw error;
                      }
                    },
                    {
                      loading: `Downloading ${exportName}`,
                      success: "Download complete",
                      error: `Failed to download ${objectType}`,
                    },
                  );
                }}
              >
                <>
                  <item.IconComponent className="size-3" />
                  {item.title}
                </>
              </DropdownMenuItem>
            ))}
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      )}
      {!isReadOnly && getRawData && (
        <DropdownMenuItem
          onClick={async () => {
            const data = await getRawData();
            if (!data) return;
            downloadAsJSON(
              (exportName ?? objectName ?? objectType) + ".json",
              data,
            );
          }}
        >
          <>
            <Bug className="size-3" /> Download debug data
          </>
        </DropdownMenuItem>
      )}
      {extraOptions?.map((option, idx) => (
        <DropdownMenuItem
          key={idx}
          disabled={option.disabled}
          onClick={(e) => {
            e.stopPropagation();
            option.onClick();
          }}
        >
          {option.label}
        </DropdownMenuItem>
      ))}
      {handleCopyId && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleCopyId();
          }}
        >
          <Clipboard className="size-3" />
          Copy {label} ID
        </DropdownMenuItem>
      )}
      {handleCopyName && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleCopyName();
          }}
        >
          <Clipboard className="size-3" />
          Copy {label} name
        </DropdownMenuItem>
      )}
      {!isReadOnly && !excludePermissions && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            setIsPermissionsOpen(true);
          }}
        >
          <ShieldCheck className="size-3" />
          Permissions
        </DropdownMenuItem>
      )}
      {!isReadOnly && brainstore && backfillableObjectType.success && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            setIsBackfillStatusOpen(true);
          }}
        >
          <CylinderIcon className="size-3" /> Backfill status
        </DropdownMenuItem>
      )}
      {!isReadOnly && handleDelete && (
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleDelete();
          }}
        >
          <Trash className="size-3" /> Delete {label}
        </DropdownMenuItem>
      )}
    </>
  );
  return (
    <>
      {renderDropdownItemsOnly ? (
        items
      ) : (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              size="xs"
              variant="ghost"
              className={cn("px-1", buttonClassName)}
            >
              <Ellipsis className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent hideWhenDetached align="start">
            {items}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {isBackfillStatusOpen && backfillableObjectType.success && (
        <BrainstoreObjectConfiguration
          onOpenChange={setIsBackfillStatusOpen}
          objectId={`${backfillableObjectType.data}:${objectId}`}
        />
      )}
      {!excludePermissions && (
        <ObjectPermissionsDialog
          open={isPermissionsOpen}
          onOpenChange={setIsPermissionsOpen}
          orgName={orgName}
          projectName={projectName}
          objectId={objectId}
          objectName={objectName}
          objectType={objectType}
        />
      )}
    </>
  );
};
