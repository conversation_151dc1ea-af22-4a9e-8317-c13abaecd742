"use client";

import * as SelectPrimitive from "@radix-ui/react-select";
import * as React from "react";

import { cn } from "#/utils/classnames";
import { Check, ChevronDown, XIcon } from "lucide-react";

const Select = SelectPrimitive.Root;

const SelectGroup = SelectPrimitive.Group;

const SelectValue = SelectPrimitive.Value;

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    onClear?: () => void;
    innerClassName?: string;
  }
>(({ className, children, onClear, innerClassName, ...props }, ref) => {
  const mergedClassName = cn(
    "flex h-10 w-full items-center justify-between",
    "rounded-md border border-border bg-transparent px-3 py-2",
    "text-sm ring-offset-background placeholder:text-muted-foreground",
    "focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-hidden",
    "disabled:cursor-not-allowed disabled:opacity-50",
    className,
  );
  return (
    <SelectPrimitive.Trigger
      ref={ref}
      className={mergedClassName}
      asChild
      {...props}
    >
      <div className={cn("flex items-center gap-1", innerClassName)}>
        <span className="flex-auto truncate">{children}</span>
        {onClear ? (
          <SelectPrimitive.Icon asChild>
            <button
              onPointerDown={(e) => {
                // hack: I believe because the Trigger captures mouseDown and onClick events
                e.stopPropagation();
              }}
              onClick={() => {
                onClear();
              }}
            >
              <XIcon className="ml-2 size-3 flex-none text-primary-500" />
            </button>
          </SelectPrimitive.Icon>
        ) : (
          <SelectPrimitive.Icon asChild>
            <ChevronDown className="size-3 flex-none text-primary-500" />
          </SelectPrimitive.Icon>
        )}
      </div>
    </SelectPrimitive.Trigger>
  );
});
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(
  (
    { className, children, position = "popper", sideOffset = 4, ...props },
    ref,
  ) => (
    <SelectPrimitive.Portal>
      <SelectPrimitive.Content
        sideOffset={sideOffset}
        ref={ref}
        className={cn(
          "relative z-50 min-w-32 overflow-hidden",
          "rounded-md bg-popover text-popover-foreground shadow-md",
          "border animate-in fade-in-80",
          className,
        )}
        position={position}
        {...props}
      >
        <SelectPrimitive.Viewport
          className={cn(
            "p-1",
            position === "popper" &&
              "h-(--radix-select-trigger-height) w-full min-w-(--radix-select-trigger-width)",
            "max-h-[420px] overflow-y-auto",
          )}
        >
          {children}
        </SelectPrimitive.Viewport>
      </SelectPrimitive.Content>
    </SelectPrimitive.Portal>
  ),
);
SelectContent.displayName = SelectPrimitive.Content.displayName;

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("py-1.5 pr-2 pl-8 text-sm font-semibold", className)}
    {...props}
  />
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {
    hideIndicator?: boolean;
  }
>(({ className, children, hideIndicator = false, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none",
      "items-center rounded-md py-1.5 pr-2 text-xs outline-hidden",
      hideIndicator ? "pl-2" : "pl-8",
      "focus:bg-primary-100",
      "aria-selected:bg-primary-100",
      "data-disabled:pointer-events-none data-disabled:opacity-50",
      className,
    )}
    {...props}
  >
    {!hideIndicator && (
      <span className="absolute left-2 flex size-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <Check className="size-3" />
        </SelectPrimitive.ItemIndicator>
      </span>
    )}

    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
SelectItem.displayName = SelectPrimitive.Item.displayName;

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
));
SelectSeparator.displayName = SelectPrimitive.Separator.displayName;

export {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
};
