import { AddNewMember } from "#/app/app/[org]/settings/team/clientpage";
import { extractGmailStyleEmail } from "#/app/app/[org]/settings/team/invite-members-form";
import { OrgUsersContext } from "#/utils/org-users-context";
import { useOrg } from "#/utils/user";
import { useState, useMemo, useCallback, useContext } from "react";

export function useInviteNewMemberModal() {
  const [isInviteMemberModalOpen, setIsInviteMemberModalOpen] = useState(false);
  const [initialSearchText, setInitialSearchText] = useState<string>("");
  const { orgUsers, refreshOrgUsers } = useContext(OrgUsersContext);
  const org = useOrg();

  const showInviteNewMemberModal = useCallback((text: string = "") => {
    setIsInviteMemberModalOpen(true);
    setInitialSearchText(text);
  }, []);

  const findExistingEmail = useCallback(
    (text: string) => {
      const emailsToFind = text
        .split(",")
        .map((t) => t.trim())
        .map(extractGmailStyleEmail)
        .filter(Boolean);
      return emailsToFind.find((e) =>
        Object.values(orgUsers).find((m) => m.email === e),
      );
    },
    [orgUsers],
  );

  const inviteNewMemberModal = useMemo(() => {
    if (!org) {
      return null;
    }
    return (
      <>
        {isInviteMemberModalOpen && (
          <AddNewMember
            onClose={() => {
              setIsInviteMemberModalOpen(false);
              setInitialSearchText("");
            }}
            initialInputText={initialSearchText}
            refreshMembersView={() => {
              refreshOrgUsers();
              setIsInviteMemberModalOpen(false);
              setInitialSearchText("");
            }}
            findExistingEmail={findExistingEmail}
          />
        )}
      </>
    );
  }, [
    initialSearchText,
    isInviteMemberModalOpen,
    org,
    refreshOrgUsers,
    findExistingEmail,
  ]);

  return { inviteNewMemberModal, showInviteNewMemberModal };
}
