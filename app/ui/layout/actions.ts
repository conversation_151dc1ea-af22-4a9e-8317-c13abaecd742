"use server";

import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";

export type SubmitUserFeedbackInput = {
  content?: string;
  emoji?: string;
  page?: string;
};

// RBAC_DISCLAIMER: Any authenticated user can submit feedback.
export async function submitUserFeedback(
  input: SubmitUserFeedbackInput,
): Promise<void> {
  const authLookup = await getServerSessionAuthLookup();

  const supabase = getServiceRoleSupabase();
  try {
    const { rows } = await supabase.query(
      `
      with
      rows_to_insert as (
          select $1::uuid, $2::text, $3::text, $4::text
          where $1 is not null
      )
      insert into user_feedback(user_id, content, emoji, page)
      select * from rows_to_insert
      returning id
    `,
      [authLookup.user_id, input.content, input.emoji, input.page],
    );
    if (rows.length !== 1) {
      throw new Error("Unauthorized");
    }
  } catch (e) {
    console.error("Failed to submit user feedback", e);
    throw new Error("Failed to submit user feedback");
  }
}
