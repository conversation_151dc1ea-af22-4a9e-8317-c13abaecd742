"use client";

import { getOrgLink } from "#/app/app/[org]/getOrgLink";
import { useUser } from "#/utils/user";
import { Check, Clipboard, ListFilter, Plus, Search } from "lucide-react";
import Link from "next/link";
import { useMemo, useRef, useState } from "react";
import { Button, type ButtonProps, buttonVariants } from "#/ui/button";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "#/ui/tooltip";
import {
  getProjectConfigurationLink,
  getProjectLink,
} from "#/app/app/[org]/p/[project]/getProjectLink";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import {
  type ProjectSummary,
  type getProjectSummary,
} from "#/app/app/[org]/org-actions";
import { Input } from "#/ui/input";
import { useCreateProjectDialog } from "#/ui/dialogs/create-project";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { EntityContextMenu } from "#/ui/entity-context-menu";
import { useEntityContextActions } from "#/app/app/[org]/p/useEntityContextActions";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { smartDateFormat } from "#/ui/date";
import { pluralizeWithCount } from "#/utils/plurals";
import { Skeleton } from "#/ui/skeleton";
import { cn } from "#/utils/classnames";
import { usePathname } from "next/navigation";
import {
  isDatasetPage,
  isExperimentPage,
  isMonitorPage,
  isPlaygroundPage,
  isProjectConfigurationPage,
  isProjectPage,
} from "#/app/app/[org]/pathname-checker";

export const HeaderMenu = ({
  type,
  orgName,
  orgId,
  selectedProject,
  fullWidth,
  hideTooltip,
  onSelectOrg,
  triggerVariant = "ghost",
  disabled,
  className,
}: {
  type: "orgs" | "projects";
  orgName: string;
  orgId: string;
  selectedProject?: ProjectSummary;
  fullWidth?: boolean;
  hideTooltip?: boolean;
  onSelectOrg?: (orgId: string | undefined) => void;
  triggerVariant?: ButtonProps["variant"];
  disabled?: boolean;
  className?: string;
}) => {
  const { orgs } = useUser();
  const pathname = usePathname();

  const projectName = selectedProject?.project_name ?? "";

  const [orgQuery, setOrgQuery] = useState("");
  const [projectQuery, setProjectQuery] = useState("");
  const [isDropdownOpen, setDropdownOpen] = useState(false);

  const firstItemRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const {
    data: projects,
    invalidate,
    isLoading: isLoadingProjects,
  } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: orgName,
    },
  });

  const projectId = projects?.find(
    (p) => p.project_name === projectName,
  )?.project_id;

  const [headerMenuProjectsSort, setHeaderMenuProjectsSort] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "",
    key: "headerMenuProjectsSort",
  });

  const organizations = useMemo(() => Object.values(orgs), [orgs]);

  const { modal: createProjectModal, open: setCreateProjectOpen } =
    useCreateProjectDialog({
      onSuccessfulCreate: ({ projectName }) => {
        invalidate();
        const projectLink = getProjectLink({ orgName: orgName, projectName });
        toast.success(`Project created`, {
          action: (
            <Link href={projectLink} className={buttonVariants({ size: "xs" })}>
              Go to project
            </Link>
          ),
        });
      },
      orgId: orgId,
    });

  const { actions: projectActions, modals: projectActionModals } =
    useEntityContextActions({
      entityType: "project",
      onUpdate: invalidate,
      reloadPageOnUpdateArgs: {
        getEditedEntityLink: (projectName) =>
          getProjectConfigurationLink({ orgName: orgName, projectName }),
        getDeletedEntityLink: () => getOrgLink({ orgName: orgName }),
      },
    });

  const filteredOrgs = organizations
    ?.filter((o) => o.name.toLowerCase().includes(orgQuery.toLowerCase()))
    ?.sort((a, b) => a.name.localeCompare(b.name));

  const filteredProjects =
    projects
      ?.filter((p) =>
        p.project_name.toLowerCase().includes(projectQuery.toLowerCase()),
      )
      .sort((a, b) => {
        if (headerMenuProjectsSort === "alphabetical") {
          return a.project_name.localeCompare(b.project_name);
        }
        const aDate = a.project_created_at
          ? new Date(a.project_created_at)
          : null;
        const bDate = b.project_created_at
          ? new Date(b.project_created_at)
          : null;
        return (bDate?.getTime() ?? 0) - (aDate?.getTime() ?? 0);
      }) ?? [];

  if (!selectedProject && type === "projects") {
    return <Skeleton className="h-8 w-full" />;
  }

  return (
    <>
      <Tooltip delayDuration={800}>
        <DropdownMenu
          open={isDropdownOpen}
          onOpenChange={(open) => {
            setDropdownOpen(open);
            if (!open) {
              setProjectQuery("");
              setOrgQuery("");
            }
          }}
        >
          <DropdownMenuTrigger asChild>
            <div className="max-w-full truncate">
              <TooltipTrigger asChild>
                <Button
                  className={cn(
                    "h-8 max-w-full flex-none gap-2 px-2 hover:bg-primary-200/60",
                    {
                      "w-full justify-start text-left": fullWidth,
                    },
                    className,
                  )}
                  isDropdown
                  variant={triggerVariant}
                  disabled={disabled}
                >
                  <span className="flex-1 truncate text-primary-900">
                    {type === "orgs" ? orgName : projectName}
                  </span>
                </Button>
              </TooltipTrigger>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            hideWhenDetached
            align="start"
            className="flex overflow-hidden p-0"
          >
            {type === "orgs" && (
              <div className="flex flex-col">
                <DropdownMenuGroup className="flex-none px-2 py-1">
                  <div
                    className="relative"
                    onKeyDown={(e) => e.stopPropagation()}
                  >
                    <Search className="pointer-events-none absolute top-[10px] left-2 size-3 text-primary-500" />
                    <Input
                      placeholder="Find organization"
                      autoFocus
                      ref={inputRef}
                      className="h-8 border-0 bg-transparent! pr-1.5 pl-7 text-xs ring-0 outline-hidden focus-visible:border-0 focus-visible:ring-0"
                      style={{ boxShadow: "none" }}
                      onChange={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setOrgQuery(e.target.value);
                      }}
                      onKeyDown={(event) => {
                        if (event.key === "ArrowDown") {
                          event.preventDefault();
                          firstItemRef.current?.focus();
                        }
                      }}
                    />
                  </div>
                </DropdownMenuGroup>
                <DropdownMenuSeparator className="mx-2 my-0 flex-none" />
                <DropdownMenuGroup className="flex-1 overflow-auto p-2">
                  <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
                    Organizations
                  </DropdownMenuLabel>
                  {filteredOrgs.length === 0 && (
                    <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-400">
                      No organizations found
                    </DropdownMenuLabel>
                  )}
                  {filteredOrgs.map((organization, i) => {
                    const orgName =
                      organizations?.find((o) => o.id === organization.id)
                        ?.name ?? "";
                    return (
                      <DropdownMenuItem
                        key={organization.id}
                        asChild
                        onSelect={() => {
                          onSelectOrg?.(organization.id);
                        }}
                        ref={i === 0 ? firstItemRef : undefined}
                        onKeyDown={(event) => {
                          if (event.key === "ArrowUp" && i === 0) {
                            event.preventDefault();
                            inputRef.current?.focus();
                          }
                        }}
                      >
                        <Link
                          href={
                            onSelectOrg
                              ? "#"
                              : getOrgHref({ orgName, projectName })
                          }
                        >
                          {organization.id === orgId ? (
                            <Check className="size-3" />
                          ) : (
                            <span className="size-3" />
                          )}
                          {organization.name}
                        </Link>
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuGroup>
                <DropdownMenuSeparator className="mx-2 my-0 flex-none" />
                <DropdownMenuGroup className="flex-none p-2">
                  <DropdownMenuItem asChild>
                    <Link
                      href={
                        pathname === "/app/create-playground"
                          ? "/app/setup?referrer=playground"
                          : "/app/setup"
                      }
                    >
                      <Plus className="size-3" />
                      Create organization
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </div>
            )}
            {type === "projects" && (
              <div className="flex flex-col">
                <DropdownMenuGroup className="flex-none px-2 py-1">
                  <div
                    className="relative flex items-center gap-2"
                    onKeyDown={(e) => e.stopPropagation()}
                  >
                    <Search className="pointer-events-none absolute top-[10px] left-2 size-3 text-primary-500" />
                    <Input
                      placeholder="Find project"
                      autoFocus
                      ref={inputRef}
                      className="h-8 flex-1 border-0 bg-transparent! pr-1.5 pl-7 text-xs ring-0 outline-hidden focus-visible:border-0 focus-visible:ring-0"
                      style={{ boxShadow: "none" }}
                      onChange={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setProjectQuery(e.target.value);
                      }}
                      onKeyDown={(event) => {
                        if (event.key === "ArrowDown") {
                          event.preventDefault();
                          firstItemRef.current?.focus();
                        }
                      }}
                    />
                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger
                        hideChevron
                        className="bg-transparent p-0 focus:bg-transparent data-[state=open]:bg-transparent"
                      >
                        <Button size="xs" Icon={ListFilter} variant="ghost" />
                      </DropdownMenuSubTrigger>
                      <DropdownMenuSubContent>
                        <DropdownMenuLabel>Sort projects</DropdownMenuLabel>
                        <DropdownMenuCheckboxItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setHeaderMenuProjectsSort("alphabetical");
                          }}
                          checked={headerMenuProjectsSort === "alphabetical"}
                        >
                          Alphabetically
                        </DropdownMenuCheckboxItem>
                        <DropdownMenuCheckboxItem
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setHeaderMenuProjectsSort("createdAt");
                          }}
                          checked={headerMenuProjectsSort === "createdAt"}
                        >
                          Created date
                        </DropdownMenuCheckboxItem>
                      </DropdownMenuSubContent>
                    </DropdownMenuSub>
                  </div>
                </DropdownMenuGroup>
                <DropdownMenuSeparator className="mx-2 my-0 flex-none" />
                <DropdownMenuGroup className="flex-1 overflow-auto p-2">
                  <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
                    Projects
                  </DropdownMenuLabel>
                  {isLoadingProjects && (
                    <div className="flex flex-col gap-1">
                      <Skeleton className="h-8" />
                      <Skeleton className="h-8 opacity-90" />
                      <Skeleton className="h-8 opacity-80" />
                      <Skeleton className="h-8 opacity-70" />
                    </div>
                  )}
                  {filteredProjects.length === 0 && (
                    <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-400">
                      No projects found
                    </DropdownMenuLabel>
                  )}
                  {filteredProjects.map((p, i) =>
                    p.project_name === projectName ? (
                      <DropdownMenuSub key={p.project_id}>
                        <DropdownMenuSubTrigger
                          ref={i === 0 ? firstItemRef : undefined}
                          onKeyDown={(event) => {
                            if (event.key === "ArrowUp" && i === 0) {
                              event.preventDefault();
                              inputRef.current?.focus();
                            }
                          }}
                        >
                          <Check className="size-3" />
                          {p.project_name}
                        </DropdownMenuSubTrigger>
                        <DropdownMenuSubContent>
                          <EntityContextMenu
                            renderDropdownItemsOnly
                            excludePermissions
                            objectType="project"
                            objectId={p.project_id}
                            objectName={projectName}
                            orgName={orgName}
                            projectName={projectName}
                            buttonClassName="w-8 h-8"
                            handleEdit={() =>
                              projectActions.editEntityName({
                                entityId: p.project_id,
                                entityName: projectName,
                                trackAnalytics: {
                                  source: "project_page_overflow_control",
                                },
                              })
                            }
                            handleDelete={() =>
                              projectActions.deleteEntity({
                                entityId: p.project_id,
                                entityName: projectName,
                                trackAnalytics: {
                                  source: "project_page_overflow_control",
                                },
                              })
                            }
                            handleCopyId={() =>
                              projectActions.copyEntityId({
                                entityId: p.project_id,
                              })
                            }
                          />
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>
                    ) : (
                      <BasicTooltip
                        key={p.project_id}
                        side="right"
                        align="start"
                        className="text-primary-600"
                        tooltipContent={
                          <>
                            <div>
                              Created{" "}
                              {p.project_created_at
                                ? smartDateFormat(
                                    new Date(p.project_created_at).getTime(),
                                  )
                                : "(unknown)"}{" "}
                              by {p.created_by_name ?? "unknown"}
                            </div>
                            <div className="flex gap-2 pt-0.5 text-[11px] text-primary-500">
                              <span>
                                {pluralizeWithCount(
                                  p.num_playgrounds,
                                  "playground",
                                )}
                              </span>
                              <span>
                                {pluralizeWithCount(
                                  p.num_experiments,
                                  "experiment",
                                )}
                              </span>
                              <span>
                                {pluralizeWithCount(p.num_datasets, "dataset")}
                              </span>
                            </div>
                          </>
                        }
                      >
                        <DropdownMenuItem
                          asChild
                          onSelect={() => {}}
                          ref={i === 0 ? firstItemRef : undefined}
                          onKeyDown={(event) => {
                            if (event.key === "ArrowUp" && i === 0) {
                              event.preventDefault();
                              inputRef.current?.focus();
                            }
                          }}
                        >
                          <Link
                            href={getProjectHref({
                              orgName,
                              projectName: p.project_name,
                              projectId: p.project_id,
                              pathname,
                            })}
                          >
                            <span className="size-3" />
                            {p.project_name}
                          </Link>
                        </DropdownMenuItem>
                      </BasicTooltip>
                    ),
                  )}
                </DropdownMenuGroup>
                <DropdownMenuSeparator className="mx-2 my-0 flex-none" />
                <DropdownMenuGroup className="flex-none p-2">
                  <DropdownMenuItem
                    onSelect={() => {
                      setCreateProjectOpen({
                        name: `Project ${(projects ?? []).length + 1}`,
                        entryPoint: "projectsSidebarDropdown",
                      });
                    }}
                  >
                    <Plus className="size-3" />
                    Create project
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </div>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <TooltipPortal>
          {!isDropdownOpen && !hideTooltip && (
            <TooltipContent align="start">
              {type === "orgs" && (
                <>
                  <div className="text-xs text-primary-500">Organization</div>
                  <div className="flex items-center justify-between gap-4">
                    {orgName}
                    {orgId && (
                      <Button
                        onClick={(e) => {
                          e.preventDefault();
                          navigator.clipboard.writeText(orgId ?? "");
                          toast("Organization ID copied to clipboard");
                        }}
                        size="inline"
                        className="border-0 px-1 text-xs font-normal text-primary-500"
                        IconRight={Clipboard}
                      >
                        Copy organization ID
                      </Button>
                    )}
                  </div>
                </>
              )}
              {type === "projects" && selectedProject && (
                <>
                  <div className="text-xs text-primary-500">Project</div>
                  <div className="flex items-center justify-between gap-4">
                    {projectName}
                    {projectId && (
                      <Button
                        onClick={(e) => {
                          e.preventDefault();
                          navigator.clipboard.writeText(projectId ?? "");
                          toast("Project ID copied to clipboard");
                        }}
                        size="inline"
                        className="border-0 px-1 text-xs font-normal text-primary-500"
                        IconRight={Clipboard}
                      >
                        Copy project ID
                      </Button>
                    )}
                  </div>
                </>
              )}
            </TooltipContent>
          )}
        </TooltipPortal>
      </Tooltip>
      {createProjectModal}
      {projectActionModals}
    </>
  );
};

const getOrgHref = ({
  orgName,
  projectName,
}: {
  orgName: string;
  projectName?: string;
}) => {
  if (projectName)
    return `${getProjectLink({ orgName, projectName })}?redirectToOrgIfNotFound=true`;
  return getOrgLink({ orgName });
};

export const getProjectHref = ({
  orgName,
  projectName,
  projectId,
  pathname,
}: {
  orgName: string;
  projectName: string;
  projectId: string;
  pathname: string | null;
}) => {
  const base = getProjectLink({ orgName, projectName });

  const projectConfigurationPage = isProjectConfigurationPage(pathname ?? "");
  if (projectConfigurationPage) {
    return `${base}/configuration/${projectConfigurationPage.params.page}`;
  }

  if (isExperimentPage(pathname ?? "")) return `${base}/experiments`;
  if (isPlaygroundPage(pathname ?? "")) return `${base}/playgrounds`;
  if (isDatasetPage(pathname ?? "")) return `${base}/datasets`;
  if (isMonitorPage(pathname ?? ""))
    return `${getOrgLink({ orgName })}/monitor?projectId=${projectId}`;

  const projectPage = isProjectPage(pathname ?? "");
  if (projectPage) {
    return `${base}/${projectPage.params.page}`;
  }

  return base;
};
