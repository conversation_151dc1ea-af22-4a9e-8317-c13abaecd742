"use client";

import { getOrgSettingsLink } from "#/app/app/[org]/getOrgLink";
import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { useOrg, useUser } from "#/utils/user";
import Link from "next/link";
import { Avatar } from "#/ui/avatar";
import { Laptop, LogOut, Moon, SlidersHorizontal, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { useSignOutCallback } from "#/utils/auth/signout";
import { signInPath } from "#/utils/auth/redirects";
import { useRouter } from "next/navigation";
import {
  orgSettingsItems,
  personalSettingsItems,
} from "#/app/app/[org]/settings/sidebar-nav";
import { useRedirectPath } from "#/utils/use-redirect-path";
import { useIsFeatureEnabled } from "#/lib/feature-flags";

export const ProfileMenu = ({ align = "end" }: { align?: "start" | "end" }) => {
  const user = useUser();
  const org = useOrg();

  const { theme, themes, setTheme } = useTheme();

  const formattedName =
    user.user?.given_name && user.user?.family_name
      ? `${user.user?.given_name} ${user.user?.family_name}`
      : user.user?.given_name;

  const signOut = useSignOutCallback();
  const router = useRouter();
  const redirectPath = useRedirectPath();

  const settingsLink = getOrgSettingsLink({ orgName: org.name });

  const shouldShowOrgSettings = org.name !== "";

  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="rounded-full border-2 border-transparent p-0 transition-colors hover:border-primary-400"
        >
          <Avatar imgUrl={user.user?.avatar_url} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} className="w-56">
        {user.status === "unauthenticated" ? (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => router.push(signInPath({ redirectPath }))}
          >
            Sign in
          </DropdownMenuItem>
        ) : (
          <>
            <DropdownMenuGroup className="px-2 py-1">
              {formattedName && (
                <div className="truncate text-sm font-medium">
                  {formattedName}
                </div>
              )}
              <div className="truncate text-xs text-primary-500">
                {user.user?.email}
              </div>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            {shouldShowOrgSettings && (
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <SlidersHorizontal className="size-3" />
                  Settings
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  <DropdownMenuGroup>
                    <DropdownMenuLabel>Organization</DropdownMenuLabel>
                    {orgSettingsItems.map((item) => {
                      if (
                        item.href === "environments" &&
                        !isEnvironmentsEnabled
                      ) {
                        return null;
                      }
                      return (
                        <DropdownMenuItem key={item.href} asChild>
                          <Link href={`${settingsLink}/${item.href}`}>
                            <item.Icon className="size-3 flex-none" />
                            {item.title}
                          </Link>
                        </DropdownMenuItem>
                      );
                    })}
                    <DropdownMenuLabel>Personal</DropdownMenuLabel>
                    {personalSettingsItems.map((item) => (
                      <DropdownMenuItem key={item.href} asChild>
                        <Link href={`${settingsLink}/${item.href}`}>
                          <item.Icon className="size-3 flex-none" />
                          {item.title}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            )}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                {getAppearanceIcon(theme ?? "light", "size-3")}
                Appearance
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                <DropdownMenuGroup>
                  {themes.map((t, idx) => (
                    <DropdownMenuCheckboxItem
                      key={idx}
                      className="gap-2 capitalize"
                      onSelect={() => setTheme(t)}
                      checked={theme === t}
                    >
                      {getAppearanceIcon(t, "size-3")}
                      {t}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuSubContent>
            </DropdownMenuSub>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="cursor-pointer" onClick={signOut}>
              <LogOut className="size-3" />
              Sign out
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const getAppearanceIcon = (
  theme: string,
  className: string = "size-4",
) => {
  return theme === "system" ? (
    <Laptop className={className} />
  ) : theme === "dark" ? (
    <Moon className={className} />
  ) : (
    <Sun className={className} />
  );
};
