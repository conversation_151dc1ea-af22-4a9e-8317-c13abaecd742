import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "#/ui/dialog";
import { But<PERSON> } from "#/ui/button";
import { useState, useMemo, useEffect } from "react";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { Spinner } from "#/ui/icons/spinner";
import { useGetRequest } from "#/utils/btapi/get";
import { zodErrorToString } from "#/utils/validation";
import { ObjectLookupResult } from "#/ui/layout/admin-object-finder";
import { useAuth } from "@clerk/nextjs";
import { useHotkeys } from "react-hotkeys-hook";
import {
  type adminFindObject,
  type FindObjectResult,
} from "#/app/admin/actions";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { brainstoreSegmentInfoSchema } from "@braintrust/local/api-schema";

export function AdminSegmentFinder({
  orgName,
  apiUrl,
}: {
  orgName: string;
  apiUrl: string;
}) {
  const [open, setOpen] = useState(false);
  const [segmentId, setSegmentId] = useState("");
  const [objectEntry, setObjectEntry] = useState<FindObjectResult | null>(null);
  const [objectEntryError, setObjectEntryError] = useState<string | null>(null);
  const [isLoadingObjectEntry, setIsLoadingObjectEntry] = useState(false);
  const { getToken } = useAuth();

  const {
    data: segmentData,
    error: requestError,
    isLoading: isLoadingSegment,
    refresh: refreshSegment,
  } = useGetRequest(
    segmentId ? `/brainstore/segment/${encodeURIComponent(segmentId)}` : null,
    undefined,
    {
      apiUrl,
    },
  );

  const [parsedData, parseError] = useMemo(() => {
    if (!segmentData) {
      return [null, null];
    }

    const status = brainstoreSegmentInfoSchema.safeParse(segmentData);
    if (status.success) {
      if (status.data.object_id) {
        return [status.data.object_id, null];
      } else {
        return [null, "Segment not found"];
      }
    } else {
      return [null, zodErrorToString(status.error, 2, true)];
    }
  }, [segmentData]);

  useEffect(() => {
    (async () => {
      if (!parsedData) {
        setObjectEntry(null);
        setObjectEntryError(null);
        return;
      }
      try {
        setIsLoadingObjectEntry(true);
        const entry = await invokeServerAction<typeof adminFindObject>({
          fName: "adminFindObject",
          args: {
            objectId: parsedData,
          },
          getToken,
        });
        setObjectEntry(entry);
        setObjectEntryError(null);
      } catch (error) {
        setObjectEntry(null);
        setObjectEntryError(
          error instanceof Error ? error.message : "Unknown error",
        );
      } finally {
        setIsLoadingObjectEntry(false);
      }
    })();
  }, [parsedData, getToken]);

  const error = requestError
    ? requestError.message
    : (parseError ?? objectEntryError);
  const isLoading = (segmentId && isLoadingSegment) || isLoadingObjectEntry;

  useHotkeys(
    ["meta+shift+s", "ctrl+shift+s"],
    () => {
      setOpen(true);
    },
    [setOpen],
  );

  const handleSegmentSearch = useDebouncedCallback((value: string) => {
    setSegmentId(value);
    refreshSegment();
  }, 500);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    handleSegmentSearch(value);
  };

  return (
    <>
      <Button size="xs" onClick={() => setOpen(true)}>
        Segment finder
      </Button>

      {open && (
        <Dialog open onOpenChange={setOpen}>
          <DialogContent
            className="flex flex-col pb-0 sm:min-h-[600px] sm:min-w-[600px]"
            hideCloseButton
          >
            <DialogHeader>
              <DialogTitle>Segment finder</DialogTitle>
              <DialogDescription>
                {orgName
                  ? `Searching in org: ${orgName}`
                  : "Searching across all orgs"}
              </DialogDescription>
              <div className="flex flex-col gap-2">
                <div className="flex flex-1 flex-row">
                  <PlainInput
                    placeholder="Enter segment ID to find object"
                    onChange={handleInputChange}
                    className="h-7 flex-1 border-0 bg-transparent pl-7 outline-hidden transition-all hover:bg-primary-100 focus:bg-primary-100"
                  />
                  {isLoading && <Spinner className="size-3" />}
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {objectEntry &&
                  (objectEntry.type === "object" ? (
                    <ObjectLookupResult objectLookup={objectEntry.object} />
                  ) : (
                    <div> Unknown object lookup result </div>
                  ))}
              </div>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
