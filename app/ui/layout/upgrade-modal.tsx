import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "#/ui/dialog";
// import Calendly from "#/ui/calendly";

export default function UpgradeModal({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[600px] lg:max-w-[1200px]">
        <DialogHeader>
          <DialogTitle>Get in touch to upgrade your plan</DialogTitle>
        </DialogHeader>
        <div className="pt-4">{/* <Calendly /> */}</div>
      </DialogContent>
    </Dialog>
  );
}
