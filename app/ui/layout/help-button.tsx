import { <PERSON><PERSON> } from "lucide-react";

import { Book<PERSON><PERSON>, MessageSquare, RssIcon } from "lucide-react";

import { DropdownMenuItem } from "#/ui/dropdown-menu";

import { HelpCircle } from "lucide-react";
import { Button, buttonVariants } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import Link from "next/link";
import { DISCORD } from "#/utils/links";
import { ChangelogDialog } from "./changelog-dialog";
import { useUser } from "#/utils/user";
import { useState } from "react";
import { Feedback } from "./feedback";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";

export const HelpButton = () => {
  const { user } = useUser();

  const [feedbackOpen, setFeedbackOpen] = useState(false);
  const [changelogO<PERSON>, setChangelogOpen] = useState(false);

  return (
    <>
      <BasicTooltip tooltipContent="Documentation">
        <Link
          href="/docs"
          target="_blank"
          className={cn(
            buttonVariants({ variant: "ghost", size: "icon" }),
            "size-8 p-0 text-primary-500",
          )}
          title="Documentation"
        >
          <BookOpen className="size-3.5" />
        </Link>
      </BasicTooltip>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="size-8 p-0 text-primary-500">
            <HelpCircle className="size-3.5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuGroup>
            <DropdownMenuItem onSelect={() => setChangelogOpen(true)}>
              <RssIcon className="size-3" />
              Changelog
            </DropdownMenuItem>
            {user?.id && (
              <DropdownMenuItem onSelect={() => setFeedbackOpen(true)}>
                <MessageSquare className="size-3" />
                Share feedback
              </DropdownMenuItem>
            )}
            <DropdownMenuItem>
              <Link
                href={DISCORD}
                target="_blank"
                className="flex items-center gap-2"
              >
                <Bot className="size-3" />
                Join us on Discord
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <ChangelogDialog open={changelogOpen} onOpenChange={setChangelogOpen} />
      {user?.id && <Feedback open={feedbackOpen} setOpen={setFeedbackOpen} />}
    </>
  );
};
