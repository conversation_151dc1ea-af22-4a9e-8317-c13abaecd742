"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useMemo } from "react";
import { cn } from "#/utils/classnames";
import { decodeURIComponentPatched } from "#/utils/url";
import { ChevronRight } from "lucide-react";

const pathsToRename: Record<
  string,
  { label: string; tab?: string; page?: string }
> = {
  p: { label: "Projects", tab: "projects" },
  prompt: { label: "Prompts", page: "prompts" },
  prompts: { label: "Prompts", page: "prompts" },
  experiments: { label: "Experiments", page: "experiments" },
  d: { label: "Datasets", tab: "Datasets" },
  datasets: { label: "Datasets", page: "datasets" },
  playgrounds: { label: "Playgrounds", page: "playgrounds" },
  logs: { label: "Logs", page: "logs" },
  agents: { label: "Agents", page: "agents" },
  scorers: { label: "Scorers", page: "scorers" },
  configuration: { label: "Project configuration", page: "configuration" },
};

export const DefaultBreadcrumbSection = ({
  omitProjects,
  omitLeaf,
  className,
  children,
  includeOrg,
}: {
  omitProjects?: boolean;
  omitLeaf?: boolean;
  className?: string;
  includeOrg?: boolean;
  children?: React.ReactNode;
}) => {
  const pathname = usePathname();
  const links = useMemo(() => {
    if (!pathname) return [];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const result = pathname
      .split("/")
      .slice(3)
      .map((segment, index) => {
        const decodedSegment = decodeURIComponentPatched(segment);
        const segmentPath = pathname
          .split("/")
          .slice(0, index + 3)
          .join("/");

        const renamed = pathsToRename[decodedSegment];
        const url = index === 1 ? `${segmentPath}/${segment}` : segmentPath;

        return {
          link: renamed?.tab
            ? `${url}?t=${renamed.tab}`
            : renamed?.page
              ? `${url}/${renamed.page}`
              : url,
          label: renamed ? renamed.label : decodedSegment,
        };
      })
      .filter((e): e is { label: string; link: string } => !!e)
      .slice(omitProjects ? 1 : 0);

    if (includeOrg) {
      const org = pathname.split("/")[2];
      result.unshift({
        label: decodeURIComponentPatched(org),
        link: `/app/${org}`,
      });
    }

    if (omitLeaf) {
      result.pop();
    }

    return result;
  }, [pathname, omitProjects, omitLeaf, includeOrg]);

  if (links.length === 0) {
    // render children as a fallback
    return children;
  }

  return (
    <BreadcrumbsList className={className} list={links} omitLeaf={omitLeaf} />
  );
};

export const BreadcrumbsList = ({
  list,
  className,
  omitLeaf,
}: {
  list: {
    label: string;
    link?: string;
    hidden?: boolean;
    className?: string;
  }[];
  className?: string;
  omitLeaf?: boolean;
}) => {
  const filteredList = list.filter((item) => !item.hidden);
  return (
    <div
      className={cn(
        "flex flex-wrap items-center text-sm text-primary-700",
        className,
      )}
    >
      {filteredList.map((item, i) => {
        const isLeaf = i === filteredList.length - 1;
        return (
          <React.Fragment key={i}>
            <BreadcrumbItem
              label={item.label}
              link={item.link}
              isLeaf={isLeaf && !omitLeaf}
              className={item.className}
            />
            {!isLeaf && (
              <span className="mx-1 opacity-40">
                <ChevronRight className="size-3" />
              </span>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

const BreadcrumbItem = ({
  label,
  link,
  isLeaf,
  className,
}: {
  label: string;
  link?: string;
  isLeaf?: boolean;
  className?: string;
}) => {
  return link && !isLeaf ? (
    <Link href={link} className="flex-none hover:text-primary-900">
      {label}
    </Link>
  ) : (
    <span
      className={cn(
        "flex-none",
        {
          "font-medium": isLeaf,
        },
        className,
      )}
    >
      {label}
    </span>
  );
};
