import { describe, expect, test } from "vitest";
import { normalizeAdditionalActions } from "./combobox-command";

describe("normalizeAdditionalActions", () => {
  test.each([
    [[], []],
    [["a"], [["a"]]],
    [[["a"]], [["a"]]],
    [["a", "b"], [["a", "b"]]],
    [
      ["a", "b", ["c", "d"], "e"],
      [["a", "b"], ["c", "d"], ["e"]],
    ],
  ])("normalizes %s to %s", (input, expected) => {
    const actions = createActions(input);

    expect(normalizeAdditionalActions(actions)).toEqual(
      createActions(expected),
    );
  });
});

function createActions(input: (string | string[])[]) {
  return input.map((v) =>
    Array.isArray(v)
      ? v.map((v2) => createAdditionalAction(v2))
      : createAdditionalAction(v),
  );
}

const onSelect = () => {};
function createAdditionalAction(label: string) {
  return { label, onSelect };
}
