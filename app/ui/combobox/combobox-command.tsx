import * as React from "react";

import { cn } from "#/utils/classnames";
import { Check, Plus } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "#/ui/command";
import { Tooltip, TooltipTrigger, TooltipContent } from "#/ui/tooltip";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { useVirtualizer } from "@tanstack/react-virtual";
import Fuse from "fuse.js";
import DraggableColumnMenuItem from "#/ui/table/draggable-column-menu-item";
import {
  closestCorners,
  type DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import StyledDndContext from "#/ui/styled-dnd-context";

type AdditionalAction = {
  className?: string;
  hidden?: boolean;
  disabled?: boolean;
  selected?: boolean;
  label: React.ReactNode;
  onSelect: () => void;
};

export type AdditionalActions = (AdditionalAction | AdditionalAction[])[];

export type ComboboxCommandProps<T extends { value: string; label: string }> = {
  className?: string;
  options: T[] | { label: string; options: T[] }[];
  selectedValue?: string;
  selectedValues?: string[];
  onChange: (value: string | undefined, option: T) => void;
  searchPlaceholder?: string;
  noResultsLabel?: string | React.ReactElement;
  noSearch?: boolean;
  bottomActions?: AdditionalActions;
  bottomMessage?: React.ReactNode;
  clearable?: boolean;
  renderOptionLabel?: (
    option: T,
    className?: string,
    options?: {
      onClose?: () => void;
    },
  ) => React.ReactNode;
  onSearchChange?: (value: string) => void;
  onKeyDown?: React.KeyboardEventHandler;
  itemLabelClassName?: string;
  renderOptionTooltip?: (option: T) => React.ReactNode;
  handleDragEnd?: (event: DragEndEvent) => void;
  onRequestClose?: () => void;
};

const defaultLabelRenderer = (
  option: { label: string },
  className?: string,
) => (
  <span className={cn("overflow-hidden break-words", className)}>
    {option.label}
  </span>
);

function isGroupedOptions<T>(
  options: T[] | { label: string; options: T[] }[],
): options is { label: string; options: T[] }[] {
  return (
    options.length > 0 &&
    !!options[0] &&
    typeof options[0] === "object" &&
    "label" in options[0] &&
    "options" in options[0]
  );
}

/**
 * Turns an array of actions like `[a, b, [c, d], e]` into `[[a,b], [c, d], [e]]`
 *
 * export for testing
 **/
export function normalizeAdditionalActions(
  actions: AdditionalActions,
): AdditionalAction[][] {
  const result: AdditionalAction[][] = [];
  let currentGroup: AdditionalAction[] = [];

  for (const action of actions) {
    if (Array.isArray(action)) {
      if (currentGroup.length > 0) {
        result.push(currentGroup);
        currentGroup = [];
      }

      result.push(action);
    } else {
      currentGroup.push(action);
    }
  }

  if (currentGroup.length > 0) {
    result.push(currentGroup);
  }

  return result;
}

type ComboboxOption<T> =
  | { type: "group"; label: string }
  | { type: "row"; option: T };

const createGroup = <T,>(label: string): ComboboxOption<T> => ({
  type: "group",
  label,
});
const createRow = <T,>(option: T): ComboboxOption<T> => ({
  type: "row",
  option,
});

export function ComboboxCommand<
  T extends {
    value: string;
    label: string;
    disabled?: boolean;
    isCreate?: boolean;
    aliases?: string[];
  },
>({
  className,
  options,
  selectedValue,
  selectedValues,
  searchPlaceholder,
  noResultsLabel,
  noSearch,
  bottomActions,
  bottomMessage,
  clearable,
  renderOptionLabel = defaultLabelRenderer,
  onSearchChange,
  onChange,
  onKeyDown,
  itemLabelClassName,
  renderOptionTooltip,
  handleDragEnd,
  onRequestClose,
}: React.PropsWithChildren<ComboboxCommandProps<T>>) {
  const [searchValue, setSearchValue] = React.useState("");
  const parentRef = React.useRef<HTMLDivElement>(null);

  const isGrouped = isGroupedOptions(options);
  const isReorderable = !!handleDragEnd;

  const flattenedOptions: ComboboxOption<T>[] = React.useMemo(() => {
    if (isGrouped) {
      return options.flatMap(({ label, options }) => [
        createGroup(label),
        ...options.map((option) => createRow(option)),
      ]);
    }

    return options.map((option) => createRow(option));
  }, [isGrouped, options]);

  const fuse = React.useMemo(
    () =>
      new Fuse(flattenedOptions, {
        keys: [
          {
            name: "label",
            getFn: (opt) => ("option" in opt ? opt.option?.label : ""),
          },
          {
            name: "value",
            getFn: (opt) => ("option" in opt ? opt.option?.value : ""),
          },
          {
            name: "aliases",
            getFn: (opt) =>
              "option" in opt ? (opt.option?.aliases ?? []) : [],
          },
          { name: "type", getFn: (opt) => opt.type },
        ],
        threshold: 0.0,
        ignoreLocation: true,
        shouldSort: false,
      }),
    [flattenedOptions],
  );

  const filteredOptions: ComboboxOption<T>[] = React.useMemo(() => {
    if (!searchValue) return flattenedOptions;
    const results = fuse.search({
      $or: [
        { type: "group" },
        {
          $or: [
            { label: searchValue },
            { value: searchValue },
            { aliases: searchValue },
          ],
        },
      ],
    });
    return results.reduce((acc: ComboboxOption<T>[], result, i) => {
      const item = result.item;
      if (
        !(
          item.type === "group" &&
          (!results[i + 1] || results[i + 1].item.type === "group")
        )
      ) {
        acc.push(item);
      }
      return acc;
    }, []);
  }, [fuse, searchValue, flattenedOptions]);

  const rowVirtualizer = useVirtualizer({
    count: filteredOptions.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 28,
    overscan: 10,
  });

  const onValueChange = React.useCallback(
    (value: string) => {
      setSearchValue(value);
      onSearchChange?.(value);
    },
    [onSearchChange],
  );

  const inner = (
    <Command
      className={cn("flex flex-1 flex-col", className)}
      onKeyDown={onKeyDown}
      loop
      shouldFilter={false}
    >
      {!noSearch && (
        <CommandInput
          className="h-9 flex-none text-xs"
          placeholder={searchPlaceholder}
          value={searchValue}
          onValueChange={onValueChange}
        />
      )}
      <CommandList>
        <div
          ref={parentRef}
          className="flex flex-1 flex-col overflow-x-hidden overflow-y-auto p-1"
        >
          <CommandEmpty
            className={cn("px-3 py-2 text-xs text-primary-500", {
              hidden: noSearch || !noResultsLabel,
            })}
          >
            {noResultsLabel}
          </CommandEmpty>
          {filteredOptions.length > 0
            ? rowVirtualizer.getVirtualItems().map((virtualRow) => {
                const itemOption = filteredOptions[virtualRow.index];
                if (itemOption.type === "group") {
                  return (
                    <div
                      className="flex h-[28px] items-center px-2 py-1.5 text-xs text-muted-foreground"
                      key={virtualRow.key}
                    >
                      {itemOption.label}
                    </div>
                  );
                } else {
                  const option = itemOption.option;

                  return (
                    <TooltipWrapper<T>
                      key={virtualRow.key}
                      option={option}
                      renderOptionTooltip={renderOptionTooltip}
                    >
                      <DraggableColumnMenuItem
                        id={option.value}
                        disabled={
                          !!option.disabled ||
                          searchValue.length > 0 ||
                          !isReorderable
                        }
                        renderer={(handle) => (
                          <CommandItem
                            keywords={[option.label]}
                            onSelect={() => {
                              onChange(
                                calculateNewValue({
                                  clearable,
                                  selectedValue,
                                  optionValue: option.value,
                                }),
                                option,
                              );
                            }}
                            value={option.value}
                            {...(option.disabled ? { disabled: true } : {})}
                            className="w-full"
                          >
                            {isGrouped ? (
                              <CheckItem
                                isSelected={isValueSelected({
                                  selectedValue,
                                  selectedValues,
                                  optionValue: option.value,
                                })}
                              >
                                {renderOptionLabel(option, itemLabelClassName, {
                                  onClose: onRequestClose,
                                })}
                              </CheckItem>
                            ) : (
                              <>
                                <span>
                                  {option.isCreate ? (
                                    <Plus className="mr-2 size-3 flex-none" />
                                  ) : (
                                    <Check
                                      className={cn(
                                        "mr-2 size-3",
                                        isValueSelected({
                                          selectedValue,
                                          selectedValues,
                                          optionValue: option.value,
                                        })
                                          ? "opacity-100"
                                          : "opacity-0",
                                      )}
                                    />
                                  )}
                                </span>
                                {renderOptionLabel(option, itemLabelClassName, {
                                  onClose: onRequestClose,
                                })}
                              </>
                            )}
                            {handle}
                          </CommandItem>
                        )}
                      />
                    </TooltipWrapper>
                  );
                }
              })
            : null}
        </div>
      </CommandList>
      {
        // Important - do not render these fixed actions inside the command list
        <BottomActions actions={bottomActions} />
      }
      {bottomMessage && (
        <div className="flex-none border-t px-3 py-2">{bottomMessage}</div>
      )}
    </Command>
  );

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  if (!isReorderable) return inner;

  return (
    <StyledDndContext
      collisionDetection={closestCorners}
      sensors={sensors}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={flattenedOptions
          .filter((option) => option.type === "row")
          .map(({ option }) => option.value)}
        strategy={verticalListSortingStrategy}
      >
        {inner}
      </SortableContext>
    </StyledDndContext>
  );
}

function BottomActions({
  actions,
}: {
  actions: AdditionalActions | undefined;
}) {
  if (actions == null) return null;

  const normalizedActions = normalizeAdditionalActions(actions);
  return normalizedActions.map((actions, i) => (
    <BottomActionsGroup key={i} actions={actions} />
  ));
}

function BottomActionsGroup({ actions }: { actions: AdditionalAction[] }) {
  return (
    actions.filter((action) => !action.hidden).length > 0 && (
      <CommandGroup className="flex-none border-t" forceMount>
        {actions
          .filter((action) => !action.hidden)
          .map((action, index) => (
            <CommandItem
              key={index}
              className={action.className}
              disabled={action.disabled}
              onSelect={() => {
                action.onSelect();
              }}
            >
              {"selected" in action ? (
                <CheckItem isSelected={action.selected}>
                  {action.label}
                </CheckItem>
              ) : (
                action.label
              )}
            </CommandItem>
          ))}
      </CommandGroup>
    )
  );
}

function calculateNewValue({
  clearable,
  selectedValue,
  optionValue,
}: {
  clearable?: boolean;
  selectedValue?: string;
  optionValue: string;
}) {
  const shouldClear =
    clearable &&
    selectedValue &&
    optionValue.toLowerCase() === selectedValue.toLowerCase();
  return shouldClear ? undefined : optionValue;
}

function isValueSelected({
  selectedValue,
  selectedValues,
  optionValue,
}: {
  selectedValue?: string;
  selectedValues?: string[];
  optionValue: string;
}) {
  return (
    selectedValue === optionValue ||
    selectedValues?.some((v) => v === optionValue)
  );
}

function TooltipWrapper<
  T extends {
    value: string;
    label: string;
    disabled?: boolean;
    isCreate?: boolean;
  },
>({
  children,
  option,
  renderOptionTooltip,
}: React.PropsWithChildren<{
  option: T;
  renderOptionTooltip?: (option: T) => React.ReactNode;
}>) {
  if (!renderOptionTooltip) return <>{children}</>;

  const tooltipContent = renderOptionTooltip(option);
  if (tooltipContent === null) return <>{children}</>;

  return (
    <Tooltip delayDuration={200}>
      <TooltipTrigger className="block w-full text-left">
        {children}
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent side="right" align="start" className="max-w-sm">
          {tooltipContent}
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
}

export function CheckItem({
  isSelected,
  children,
}: {
  isSelected?: boolean;
  children?: React.ReactNode;
}) {
  return (
    <>
      <span>
        <Check
          className={cn("mr-2 size-3", {
            "opacity-100": isSelected,
            "opacity-0": !isSelected,
          })}
        />
      </span>
      {children}
    </>
  );
}
