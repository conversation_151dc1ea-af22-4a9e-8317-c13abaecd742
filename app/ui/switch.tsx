"use client";

import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
} from "react";
import * as SwitchPrimitives from "@radix-ui/react-switch";

import { cn } from "#/utils/classnames";

const Switch = forwardRef<
  ElementRef<typeof SwitchPrimitives.Root>,
  ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-[16px] w-[30px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent",
      "transition-colors disabled:cursor-not-allowed disabled:opacity-50",
      "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background focus-visible:outline-hidden",
      "data-[state=checked]:bg-emerald-500",
      "data-[state=unchecked]:bg-primary-300",
      className,
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-3 w-3 rounded-full bg-background shadow-lg ring-0",
        "transition-transform data-[state=checked]:translate-x-[14px] data-[state=unchecked]:translate-x-[0px]",
      )}
    />
  </SwitchPrimitives.Root>
));
Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch };
