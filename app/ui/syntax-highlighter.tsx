"use client";
import React, { type JSX, useLayoutEffect, useState } from "react";
import { cn } from "#/utils/classnames";
import { type BundledLanguage, highlight } from "./highlight";

interface SyntaxHighlightProps {
  language?: BundledLanguage;
  className?: string;
  inline?: boolean;
  inlineClassName?: string;
  content: string;
}

const SyntaxHighlightComponent: React.FC<SyntaxHighlightProps> = ({
  language = "typescript",
  className,
  inline = false,
  inlineClassName,
  content,
}) => {
  const [nodes, setNodes] = useState<JSX.Element | string>(content);

  useLayoutEffect(() => {
    let isMounted = true;
    void highlight({
      code: content,
      lang: language,
    }).then((result) => {
      if (isMounted) {
        setNodes(result);
      }
    });
    return () => {
      isMounted = false;
    };
  }, [content, language]);

  const containerClassName = cn(
    "bg-primary-50 font-mono text-xs",
    inline ? "overflow-x-auto" : "whitespace-pre-wrap",
    className,
  );

  return (
    <div className={containerClassName}>
      <pre
        // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
        className={cn("not-prose", inline && "no-scrollbar overflow-x-auto")}
      >
        <code
          // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
          className={cn("shiki not-prose", !inline && "whitespace-pre-wrap")}
        >
          {nodes}
        </code>
      </pre>
      {inline && (
        <div
          className={cn(
            "pointer-events-none absolute inset-y-0 right-0 w-16 scale-95 bg-linear-to-l from-background",
            inlineClassName,
          )}
        />
      )}
    </div>
  );
};

export const SyntaxHighlight = React.memo(
  SyntaxHighlightComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.language === nextProps.language &&
      prevProps.className === nextProps.className &&
      prevProps.content === nextProps.content
    );
  },
);
