import * as snippet from "@segment/snippet";
import Script from "#/ui/script";

export function renderAnalyticsSnippet() {
  if (!process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY) {
    return;
  }
  const opts = {
    apiKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
    // note: the page option only covers SSR tracking.
    page: false,
  };

  if (process.env.NODE_ENV === "development") {
    return snippet.max(opts);
  }

  return snippet.min(opts);
}

export function AnalyticsSnippet() {
  const snippet = renderAnalyticsSnippet();
  return snippet ? (
    <Script
      dangerouslySetInnerHTML={{ __html: snippet }}
      id="segmentScript"
      suppressHydrationWarning
    />
  ) : null;
}

export default function AnalyticsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <AnalyticsSnippet />
      {children}
    </>
  );
}

export function GoogleAnalytics() {
  const measurementId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
  if (!measurementId) {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
      />
      <Script id="google-analytics">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', '${measurementId}');
        `}
      </Script>
    </>
  );
}
