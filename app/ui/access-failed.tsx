"use client";

import { MainContentWrapper } from "./layout/main-content-wrapper";
import { useUser } from "#/utils/user";
import { TableEmptyState } from "./table/TableEmptyState";
import { ShieldAlert } from "lucide-react";
import { But<PERSON> } from "./button";
import { useRouter } from "next/navigation";
import { signInPath } from "#/utils/auth/redirects";
import { useRedirectPath } from "#/utils/use-redirect-path";
import { BodyWrapper } from "#/app/app/body-wrapper";

export function AccessFailed({
  className,
  objectType,
  objectName,
}: {
  className?: string;
  objectType: string;
  objectName: string;
}) {
  const { status } = useUser();
  const router = useRouter();
  const redirectPath = useRedirectPath();

  return (
    <BodyWrapper innerClassName="overflow-auto">
      <MainContentWrapper role="alert">
        {status === "authenticated" ? (
          <TableEmptyState
            className={className}
            Icon={ShieldAlert}
            label={
              <span>
                {objectType} <span className="font-medium">{objectName}</span>{" "}
                does not exist or you do not have access to it. Please contact
                your organization administrator for assistance.
              </span>
            }
          />
        ) : (
          <TableEmptyState
            className={className}
            Icon={ShieldAlert}
            label="This page is restricted or does not exist. Sign in to verify permissions and access this resource."
          >
            <Button
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                router.push(signInPath({ redirectPath }));
              }}
            >
              Sign in
            </Button>
          </TableEmptyState>
        )}
      </MainContentWrapper>
    </BodyWrapper>
  );
}
