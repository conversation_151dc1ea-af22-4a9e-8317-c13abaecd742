// eslint-disable-next-line no-restricted-imports -- this file defines the wrapper which should be used in place of DndContext
import { DndContext } from "@dnd-kit/core";
import { useId, type ComponentProps } from "react";
import useEvent from "react-use-event-hook";

const GRABBING_CLASS = "cursor-grabbing-global";

/**
 * Wrapped version of dnd-kit's DndContext which includes default grabbing cursor styles.
 */
export default function StyledDndContext({
  onDragStart,
  onDragEnd,
  children,
  ...rest
}: ComponentProps<typeof DndContext>) {
  const id = useId();
  return (
    <DndContext
      id={id}
      onDragStart={useEvent((e) => {
        onDragStart?.(e);
        document.body.classList.add(GRABBING_CLASS);
      })}
      onDragEnd={useEvent((e) => {
        onDragEnd?.(e);
        document.body.classList.remove(GRABBING_CLASS);
      })}
      {...rest}
    >
      {children}
    </DndContext>
  );
}
