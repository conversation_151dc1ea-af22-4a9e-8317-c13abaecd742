import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Button, type ButtonProps } from "./button";
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip";
import { useCallback, useState, useRef, useEffect } from "react";
import { Check, Clipboard } from "lucide-react";
import { Spinner } from "./icons/spinner";
import { toast } from "sonner";

// TODO: Investigate flaky bug. "copy to ..." text sometimes reappears after copying and hovering away
export const CopyToClipboardButton = ({
  textToCopy,
  getTextToCopy,
  copyMessage = "Copy to clipboard",
  copiedMessage = "Copied!",
  children,
  disableTooltip,
  prependIcon,
  ...buttonProps
}: {
  textToCopy: string;
  getTextToCopy?: () => Promise<string>;
  copyMessage?: string;
  copiedMessage?: string;
  disableTooltip?: boolean;
  prependIcon?: boolean;
} & ButtonProps) => {
  const [didCopy, setDidCopy] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(
    undefined,
  );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getText = useCallback(async () => {
    // if no async function to get text to copy provided, use textToCopy
    if (!getTextToCopy) {
      return textToCopy;
    }

    // set isLoading while running async function to get text to copy
    setIsLoading(true);
    const asyncText = await getTextToCopy();
    setIsLoading(false);
    return asyncText;
  }, [textToCopy, getTextToCopy]);

  const copyToClipboard = async (e: React.MouseEvent) => {
    e.preventDefault();
    const text = await getText();

    try {
      await navigator.clipboard.writeText(text);
      setDidCopy(true);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      // Reset state after 2 seconds
      timeoutRef.current = setTimeout(() => {
        setDidCopy(false);
      }, 2000);
    } catch (error) {
      toast.error("Failed to copy to clipboard");
    }
  };

  const button = (
    <Button
      {...buttonProps}
      type="button"
      disabled={buttonProps.disabled ?? isLoading}
      onClick={copyToClipboard}
      Icon={
        isLoading
          ? Spinner
          : children && !prependIcon
            ? undefined
            : didCopy
              ? Check
              : Clipboard
      }
    >
      {children}
    </Button>
  );

  if (disableTooltip) return button;

  return (
    <Tooltip
      disableHoverableContent={didCopy}
      open={didCopy ? true : undefined}
      delayDuration={300}
    >
      <TooltipTrigger asChild>{button}</TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          className="text-xs"
          onPointerDownOutside={(e) => {
            e.preventDefault();
          }}
        >
          {didCopy ? copiedMessage : copyMessage}
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
};
