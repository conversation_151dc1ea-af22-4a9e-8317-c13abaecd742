import { useDarkMode } from "#/utils/useDarkMode";
import { EditorView } from "@codemirror/view";
import { useMemo } from "react";
import { MONO_FONT_STACK } from "#/ui/fontstack";

export const useCodemirrorTheme = ({
  isMonospace,
  styled,
}: {
  isMonospace: boolean;
  styled?: boolean;
}) => {
  const dark = useDarkMode();

  const editorTheme = useMemo(() => {
    return EditorView.theme(
      {
        ".cm-scroller": {
          fontFamily: !isMonospace ? "var(--font-inter)" : MONO_FONT_STACK,
        },
        ".cm-foldPlaceholder": {
          backgroundColor: "rgb(var(--primary-200))",
          color: "rgb(var(--primary-800))",
          border: 0,
          paddingLeft: "4px",
          paddingRight: "4px",
        },
        ".cm-placeholder": {
          fontFamily: "var(--font-inter)",
          color: "rgb(var(--primary-500))",
        },
        ".cm-tooltip": {
          borderRadius: "6px",
        },
        ".cm-diagnostic-error": {
          borderLeft: "none",
        },
        ".cm-underline": {
          textDecoration: "underline",
        },
        ".cm-gutters": styled
          ? {
              gapX: "0.125rem",
              color: "rgb(var(--primary-400))",
              backgroundColor: "transparent",
              borderColor: "transparent",
            }
          : {
              width: "12px",
              background: "transparent",
              borderColor: "transparent",
            },
        ".cm-content": styled
          ? {
              padding: "0.5rem !important",
            }
          : {},
        ".cm-hyper-link-icon svg": {
          width: "12px",
          height: "12px",
          fill: "rgb(var(--accent-500))",
        },
      },
      { dark: dark === null ? undefined : dark },
    );
  }, [dark, styled, isMonospace]);
  return editorTheme;
};
