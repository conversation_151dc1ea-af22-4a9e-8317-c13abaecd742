import { acceptCompletion, closeBrackets } from "@codemirror/autocomplete";
import { useMergeViewExtension } from "#/ui/codemirror/use-mergeview-extension";
import { keymap, placeholder, tooltips } from "@codemirror/view";
import {
  cursorLineDown,
  cursorLineUp,
  defaultKeymap,
  history,
  historyKeymap,
} from "@codemirror/commands";
import { search } from "@codemirror/search";
import {
  StateField,
  type Extension,
  type ReactCodeMirrorRef,
} from "@uiw/react-codemirror";
import { hyperLink } from "@uiw/codemirror-extensions-hyper-link";
import { EditorView } from "@codemirror/view";
import { type RefObject, useCallback, useMemo } from "react";
import "#/ui/TextEditor.css";
import { type SetValue } from "#/lib/clientDataStorage";
import { foldTracker } from "./codemirror-fold";
import { type EditorAutoCompleteContextFn } from "#/ui/text-editor";
import useEvent from "react-use-event-hook";

export const useCodemirrorExtensions = ({
  editorRef,
  diffValue,
  placeholderProp,
  extensionsProp,
  readOnly,
  getAutoCompleteContext,
  onMetaEnter,
  jump,
  tabAutocomplete,
  wrap,
  spellcheck,
  setFoldState,
  editorId,
}: {
  editorRef: RefObject<ReactCodeMirrorRef | null>;
  diffValue?: string | null;
  placeholderProp?: string;
  extensionsProp?: Extension[];
  readOnly?: boolean;
  getAutoCompleteContext?: EditorAutoCompleteContextFn;
  onMetaEnter?: (type: "shift" | "cmd") => void;
  jump?: (up: boolean) => void;
  tabAutocomplete?: boolean;
  wrap?: boolean;
  spellcheck?: boolean;
  setFoldState?: SetValue<Record<string, boolean>>;
  editorId?: string;
}) => {
  const hasOnMetaEnter = !!onMetaEnter;
  const stableOnMetaEnter = useEvent((type: "shift" | "cmd") =>
    onMetaEnter?.(type),
  );
  const keyMap = useMemo(() => {
    let keyMap = [...defaultKeymap, ...historyKeymap];
    if (hasOnMetaEnter) {
      keyMap = [
        {
          key: "Ctrl-Enter",
          mac: "Cmd-Enter",
          run: (target: EditorView) => {
            stableOnMetaEnter("cmd");
            return true;
          },
        },
        {
          key: "Shift-Enter",
          run: (target: EditorView) => {
            stableOnMetaEnter("shift");
            return true;
          },
        },
        ...keyMap,
      ];
    }

    if (jump) {
      keyMap = [
        {
          key: "ArrowUp",
          run: (target: EditorView) => {
            const didMoveUp = cursorLineUp(target);
            if (!didMoveUp) {
              jump(true);
            }
            return true;
          },
        },
        {
          key: "ArrowDown",
          run: (target: EditorView) => {
            const didMoveDown = cursorLineDown(target);
            if (!didMoveDown) {
              jump(false);
            }
            return true;
          },
        },
        ...keyMap,
      ];
    }

    if (tabAutocomplete) {
      keyMap = [
        {
          key: "Tab",
          run: (target: EditorView) => acceptCompletion(target),
        },
        ...keyMap,
      ];
    }
    return keyMap;
  }, [jump, stableOnMetaEnter, tabAutocomplete, hasOnMetaEnter]);

  const getHasFocus = useCallback(
    () => !!editorRef.current?.view?.hasFocus,
    [editorRef],
  );

  /*
    const lastCompletionSpanId = useRef<string | null>(null);
    const copilotState = useCopilotState();

    const isCopilotEnabled = !!getAutoCompleteContext && !readOnly;
    const runCopilot = useCallback(
      async (prefix: string, suffix: string) => {
        const shouldNotRun = () =>
          !isCopilotEnabled ||
          !getHasFocus() ||
          // If the user hasn't typed anything, and the cursor is at the beginning of
          // the editor, then we shouldn't print a suggestion.
          (!changed.current && prefix === "");
        if (shouldNotRun()) {
          return "";
        }
        const editorCtx = await getAutoCompleteContext!();
        const completion = await copilotState.runAutocomplete({
          prefix,
          suffix,
          editorCtx,
        });
        lastCompletionSpanId.current = completion.spanId ?? null;

        if (shouldNotRun()) {
          return "";
        }

        return completion.completion;
      },
      [isCopilotEnabled, getHasFocus, getAutoCompleteContext, copilotState],
    );
    */

  const hasFocus = getHasFocus();
  const placeholderExt = useMemo(() => {
    if (placeholderProp && !readOnly && !(hasFocus && getAutoCompleteContext)) {
      return placeholder(placeholderProp);
    }
    return null;
  }, [placeholderProp, readOnly, hasFocus, getAutoCompleteContext]);

  const { extension: diffExtension, error: unifiedMergeViewError } =
    useMergeViewExtension(editorRef.current?.view, diffValue);

  const extensions = useMemo(
    () =>
      [
        keymap.of(keyMap),
        // https://github.com/codemirror/basic-setup/blob/main/src/codemirror.ts
        history(),
        closeBrackets(),
        hyperLink,
        search({ top: true }),
        tooltips({
          position: "absolute",
          parent: document.body,
        }),
      ]
        // This doesn't seem fixed yet? It seems like placeholders cause the cursor to
        // stretch across lines
        // https://github.com/codemirror/dev/issues/1142
        .concat(readOnly ? [EditorView.editable.of(false)] : [])
        .concat(wrap ? [EditorView.lineWrapping] : [])
        .concat(
          spellcheck
            ? [EditorView.contentAttributes.of({ spellcheck: "true" })]
            : [],
        )
        /*
          .concat(
            isCopilotEnabled
              ? [
                  inlineCopilot(runCopilot, 250, false, () => {
                    if (lastCompletionSpanId.current) {
                      saveFeedback({
                        spanId: lastCompletionSpanId.current,
                        accepted: true,
                      });
                    }
                  }),
                ]
              : [],
          )
              */
        .concat(placeholderExt ? [placeholderExt] : [])
        .concat(diffExtension)
        .concat(extensionsProp || [])
        .concat(setFoldState ? [foldTracker({ setFoldState })] : [])
        .concat(
          editorId
            ? [
                StateField.define({
                  create: () => editorId,
                  toJSON: (id) => id,
                  fromJSON: (id) => id,
                  update: (id) => id,
                }),
              ]
            : [],
        ),
    [
      extensionsProp,
      keyMap,
      wrap,
      spellcheck,
      setFoldState,
      //runCopilot,
      //isCopilotEnabled,
      placeholderExt,
      readOnly,
      diffExtension,
      editorId,
    ],
  );

  return { extensions, unifiedMergeViewError };
};
