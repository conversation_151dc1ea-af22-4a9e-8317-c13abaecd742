import { useMemo, useEffect, useState } from "react";
import { type EditorView } from "@codemirror/view";
import { type Extension, Compartment, ChangeSet } from "@codemirror/state";
import { isEmpty } from "#/utils/object";
import {
  unifiedMergeView,
  getOriginalDoc,
  originalDocChangeEffect,
} from "@codemirror/merge";

// this automatically adds the extension to the view if it's not already there
// and updates it if it is when the deps change
export function useMergeViewExtension(
  view: EditorView | undefined,
  diffValue?: string | null,
) {
  const [error, setError] = useState<unknown>();
  const { compartment, extension } = useMemo(() => {
    const compartment = new Compartment();
    return { compartment, extension: compartment.of([]) };
  }, []);

  useEffect(() => {
    if (!view) {
      return;
    }
    const exists = extensionExists(compartment.get(view.state));
    try {
      if (!exists) {
        if (isEmpty(diffValue)) return;

        // initialize extension
        const extension = unifiedMergeView({
          gutter: true,
          mergeControls: false,
          original: diffValue,
        });
        view.dispatch({ effects: compartment.reconfigure(extension) });
      } else {
        const originalDoc = getOriginalDoc(view.state);
        view.dispatch({
          effects: originalDocChangeEffect(
            view.state,
            ChangeSet.of(
              {
                from: 0,
                to: originalDoc.length,
                insert: diffValue || "",
              },
              originalDoc.length,
            ),
          ),
        });
      }
      setError(undefined);
    } catch (err) {
      setError(err);
    }
  }, [view, diffValue, compartment]);

  return { extension, error };
}

function extensionExists(e: Extension | undefined) {
  if (Array.isArray(e)) {
    return e.length > 0;
  }
  return !!e;
}
