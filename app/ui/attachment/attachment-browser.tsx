"use client";

import { AttachmentItem } from "./attachment-list";
import { useMemo, useState } from "react";
import { But<PERSON> } from "#/ui/button";
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Download,
  X,
} from "lucide-react";
import useEvent from "react-use-event-hook";
import { useAttachmentBrowser } from "#/ui/query-parameters";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import {
  extractAttachmentReferences,
  useExtractedAttachment,
} from "./use-extracted-attachment";
import { Skeleton } from "#/ui/skeleton";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const AttachmentBrowser = ({ spanData }: { spanData: any }) => {
  const [_, setAttachmentBrowserOpen] = useAttachmentBrowser();

  const [index, setIndex] = useState(0);

  const extractedAttachments = useMemo(() => {
    try {
      return [
        ...(spanData.input
          ? extractAttachmentReferences(JSON.parse(spanData.input))[0]
          : []),
        ...(spanData.output
          ? extractAttachmentReferences(JSON.parse(spanData.output))[0]
          : []),
        ...(spanData.expected
          ? extractAttachmentReferences(JSON.parse(spanData.expected))[0]
          : []),
        ...(spanData.metadata
          ? extractAttachmentReferences(JSON.parse(spanData.metadata))[0]
          : []),
      ];
    } catch (err) {
      console.error(err);
      return [];
    }
  }, [spanData]);

  const onClose = useEvent(() => {
    setIndex(0);
    setAttachmentBrowserOpen(null);
  });

  const onPrev = useEvent(() => setIndex((prev) => prev - 1));
  const onNext = useEvent(() => setIndex((prev) => prev + 1));

  const isEmpty = extractedAttachments.length === 0;
  const selected = extractedAttachments[index];

  const config = useExtractedAttachment({
    extracted: selected,
  });

  return (
    <>
      <div className="flex flex-none items-center gap-1 px-4 py-3 text-xs">
        {config?.status.error_message && (
          <AlertTriangle className="size-3 flex-none text-bad-700" />
        )}
        <span className="flex-1 truncate">
          {config?.filename ?? "Attachments"}
        </span>
        {!isEmpty && (
          <>
            <span className="text-primary-400">
              {index + 1} of {extractedAttachments.length}
            </span>
            <Button
              Icon={ArrowLeft}
              disabled={index === 0}
              variant="ghost"
              onClick={onPrev}
              size="xs"
            />
            <Button
              Icon={ArrowRight}
              disabled={index === extractedAttachments.length - 1}
              variant="ghost"
              onClick={onNext}
              size="xs"
            />
            <Button
              size="xs"
              className="flex-none"
              variant="ghost"
              disabled={!config?.isLoaded || !config?.downloadUrl}
              Icon={Download}
              onClick={() =>
                config?.downloadUrl &&
                window.location.assign(config.downloadUrl)
              }
            />
          </>
        )}
        <Button Icon={X} variant="ghost" size="xs" onClick={onClose} />
      </div>
      <div className="flex flex-1 overflow-hidden p-4 pt-0">
        {isEmpty ? (
          <TableEmptyState
            className="flex-1 justify-center"
            labelClassName="text-sm text-center"
            label="There are no attachments in this span"
          />
        ) : selected ? (
          <AttachmentItem mode="full" extracted={selected} />
        ) : (
          <Skeleton className="flex-1" />
        )}
      </div>
    </>
  );
};
