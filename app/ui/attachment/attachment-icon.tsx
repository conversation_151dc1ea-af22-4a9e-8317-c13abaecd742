import {
  FileArchiveIcon,
  FileCode2Icon,
  FileDigitIcon,
  FileIcon,
  FileImageIcon,
  FileJson2Icon,
  FileSpreadsheetIcon,
  FileTextIcon,
  FileVideo2Icon,
  FileVolumeIcon,
} from "lucide-react";

export function AttachmentIcon({
  contentType,
  className,
}: {
  contentType: string;
  className?: string;
}) {
  const Icon = (() => {
    if (contentType === "application/json") {
      return FileJson2Icon;
    } else if (
      contentType === "application/xml" ||
      contentType === "text/xml" ||
      contentType === "text/html" ||
      contentType.startsWith("application/xhtml")
    ) {
      return FileCode2Icon;
    } else if (contentType === "application/octet-stream") {
      return FileDigitIcon;
    } else if (
      contentType === "text/csv" ||
      contentType === "application/vnd.ms-excel" ||
      contentType ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      return FileSpreadsheetIcon;
    } else if (
      contentType.startsWith("application/bzip") ||
      contentType === "application/gzip" ||
      contentType === "application/x-gzip" ||
      contentType === "application/vnd.rar" ||
      contentType === "application/x-tar" ||
      contentType === "application/zip" ||
      contentType === "application/x-zip-compressed" ||
      contentType === "application/x-7z-compressed"
    ) {
      return FileArchiveIcon;
    } else if (contentType.startsWith("image/")) {
      return FileImageIcon;
    } else if (contentType.startsWith("audio/")) {
      return FileVolumeIcon;
    } else if (contentType.startsWith("video/")) {
      return FileVideo2Icon;
    } else if (
      contentType === "application/pdf" ||
      contentType.startsWith("text/")
    ) {
      return FileTextIcon;
    } else {
      return FileIcon;
    }
  })();

  return <Icon className={className} />;
}
