"use client";

import type { AttachmentStatus } from "@braintrust/typespecs";
import type { CSSProperties, Dispatch, SetStateAction } from "react";
import { useEffect, useState } from "react";
import { Loading } from "#/ui/loading";
import { cn } from "#/utils/classnames";

export function AttachmentViewer({
  status,
  src,
  filename,
  contentType,
  setAudioPlaying,
  id,
  className,
  style,
}: {
  status: AttachmentStatus;
  src?: string;
  filename: string;
  contentType: string;
  setAudioPlaying?: Dispatch<SetStateAction<boolean>>;
  id?: string;
  className?: string;
  style?: CSSProperties;
}) {
  const commonProps = { id, className, style, src };

  if (status.upload_status !== "done" || !src) {
    return <Loading className={className} />;
  }

  if (contentType.startsWith("image/")) {
    return (
      <img
        {...commonProps}
        className={cn("mx-auto object-contain", className)}
        alt={filename}
      />
    );
  } else if (contentType.startsWith("audio/")) {
    return (
      <audio
        {...commonProps}
        controls
        preload="none"
        className={cn("w-full", className)}
        onPlay={() => setAudioPlaying?.(true)}
        onPause={() => setAudioPlaying?.(false)}
      />
    );
  } else if (contentType.startsWith("video/")) {
    return <video {...commonProps} controls />;
  } else if (contentType === "application/pdf") {
    return <iframe {...commonProps} className={cn(className, "rounded-md")} />;
  } else if (contentType === "text/plain") {
    return <TextFileViewer src={src} className={className} />;
  } else {
    return (
      <p className="text-center text-sm text-primary-500">
        Preview not available for this file type
      </p>
    );
  }
}

function TextFileViewer({
  src,
  className,
}: {
  src: string;
  className?: string;
}) {
  const [content, setContent] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetch(src, {
      mode: "cors",
      cache: "no-cache",
    })
      .then((res) => {
        if (!res.ok) throw new Error("Failed to load text file");
        return res.text();
      })
      .then(setContent)
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, [src]);

  if (loading) return <Loading className={className} />;
  if (error)
    return <p className="text-center text-sm text-red-500">Error: {error}</p>;

  return (
    <pre
      className={cn(
        "max-h-96 overflow-auto rounded-md bg-primary-100 p-4 text-xs break-words whitespace-pre-wrap",
        className,
      )}
    >
      {content}
    </pre>
  );
}
