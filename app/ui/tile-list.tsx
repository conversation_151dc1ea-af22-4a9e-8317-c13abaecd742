import { cn } from "#/utils/classnames";
import Link from "next/link";
import { type Mouse<PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";

export const TileList = ({
  children,
  className,
}: React.PropsWithChildren<{ className?: string }>) => {
  return (
    <div className={cn("flex flex-row flex-wrap gap-2", className)}>
      {children}
    </div>
  );
};
export const TileListItem = ({
  title,
  subtitle,
  href,
  onClick,
  className,
  subtitleClassName,
  prefetch,
}: {
  title: React.ReactNode;
  subtitle?: React.ReactNode;
  href: string;
  onClick?: MouseEventHandler<HTMLAnchorElement>;
  className?: string;
  subtitleClassName?: string;
  prefetch?: boolean;
}) => {
  return (
    <Link
      href={href}
      onClick={onClick}
      prefetch={prefetch}
      className={cn(
        "flex size-48 flex-col gap-1 rounded-lg bg-primary-100 p-4 hover:bg-primary-200",
        className,
      )}
    >
      <div className="line-clamp-2 flex-none font-medium break-words">
        {title}
      </div>
      {subtitle && (
        <div className="flex flex-1 items-end">
          <div
            className={cn(
              "truncate text-xs text-primary-600",
              subtitleClassName,
            )}
          >
            {subtitle}
          </div>
        </div>
      )}
    </Link>
  );
};
