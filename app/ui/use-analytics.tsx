"use client";

import { useParams, usePathname } from "next/navigation";
import { useEffect, useRef, useState } from "react";

import { useUser } from "#/utils/user";

function isBrowser() {
  return typeof window !== "undefined";
}

interface PageInfo {
  category: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  props?: { [key: string]: any };
}

export type Analytics = typeof window.analytics | undefined;

export function useAnalytics({ page }: { page?: PageInfo | null } = {}): {
  analytics: Analytics;
} {
  const [ready, setReady] = useState(false);
  useEffect(() => {
    if (!isBrowser()) {
      return;
    }
    window.analytics?.ready(() => {
      setReady(true);
    });
  });

  const analytics = ready ? window.analytics : undefined;

  const pathname = usePathname();
  const params = useParams();
  const lastPathname = useRef<string | null>(null);
  useEffect(() => {
    if (!page || !analytics) {
      return;
    }

    if (pathname !== lastPathname.current) {
      lastPathname.current = pathname;
      analytics.page(
        page.category,
        pathname || undefined,
        { params, ...page.props },
        undefined,
        undefined,
      );
    }
  }, [analytics, page, params, pathname]);

  return {
    analytics,
  };
}

export function PageTracker({
  category,
  children,
}: {
  category: string;
  children: React.ReactNode;
}) {
  useAnalytics({ page: { category } });
  return <>{children}</>;
}

export function IdentifyUser({ children }: { children: React.ReactNode }) {
  const { user } = useUser();
  const { analytics } = useAnalytics();

  useEffect(() => {
    if (analytics && !analytics?.user?.().id() && user?.id) {
      analytics.identify(user.id, {
        email: user.email,
      });
    }
  }, [analytics, user]);

  return <>{children}</>;
}
