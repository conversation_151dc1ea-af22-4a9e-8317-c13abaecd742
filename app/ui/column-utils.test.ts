import { describe, expect, test } from "vitest";
import {
  resolveColumnOrder,
  resolveOrderByRegressionColumnOrder,
} from "./column-utils";

describe("resolveColumnOrder", () => {
  describe("basic ids", () => {
    test.each([
      [["a"], ["a"], ["a"]],
      [["a", "b"], ["a"], ["a", "b"]],
      [["a", "b"], ["b"], ["a", "b"]],
      [["a", "b"], ["c"], ["a", "b"]],
      [
        ["a", "b", "c", "d", "e", "f", "g"],
        ["g"],
        ["a", "b", "c", "d", "e", "f", "g"],
      ],
      [
        ["b", "c", "a"],
        ["a", "b"],
        ["a", "c", "b"],
      ],

      [["a"], ["a", "b"], ["a"]],
      [["b"], ["a", "b"], ["b"]],
      [["c"], ["a", "b"], ["c"]],
      [["g"], ["a", "b", "c", "d", "e", "f", "g"], ["g"]],

      [
        ["a", "b"],
        ["b", "c", "a"],
        ["b", "a"],
      ],
    ])(
      "(%j, %j) => %j",
      (
        computedColumnOrder: string[],
        savedColumnOrder: string[],
        expectedColumnOrder: string[],
      ) => {
        expect(
          resolveColumnOrder(
            computedColumnOrder.map((c) => ({ id: c })),
            savedColumnOrder,
          ),
        ).toEqual(expectedColumnOrder);
      },
    );
  });

  describe("pinned columns", () => {
    test.each([
      [[{ id: "a" }, { id: "b", pinnedColumnIndex: 0 }], ["a"], ["b", "a"]],
      [
        [{ id: "a" }, { id: "b", pinnedColumnIndex: 0 }],
        ["a", "b"],
        ["b", "a"],
      ],
      [
        [
          { id: "a" },
          { id: "b", pinnedColumnIndex: 1 },
          { id: "c", pinnedColumnIndex: 0 },
        ],
        ["a"],
        ["c", "b", "a"],
      ],
      [
        [{ id: "a" }, { id: "b", pinnedColumnIndex: 0 }],
        ["a", "b", "c"],
        ["b", "a"],
      ],
    ])(
      "(%j, %j) => %j",
      (
        computedColumnOrder: { id: string; pinnedColumnIndex?: number }[],
        savedColumnOrder: string[],
        expectedColumnOrder: string[],
      ) => {
        expect(
          resolveColumnOrder(computedColumnOrder, savedColumnOrder),
        ).toEqual(expectedColumnOrder);
      },
    );
  });
});

describe("resolveOrderByRegressionColumnOrder", () => {
  describe("prepends fixed columns", () => {
    test.each([
      [
        [{ id: "a" }, { id: "b", pinnedColumnIndex: 0 }, { id: "c" }],
        ["b", "a", "c"],
      ],
      [
        [
          { id: "a", internalType: "internal" },
          { id: "b", pinnedColumnIndex: 0 },
          { id: "c" },
        ],
        ["a", "b", "c"],
      ],
      [
        [{ id: "a" }, { id: "b" }, { id: "span_type_info" }, { id: "input" }],
        ["span_type_info", "input", "a", "b"],
      ],
    ])("(%j, %j) => %j", (computedColumnOrder, expectedColumnOrder) => {
      expect(
        resolveOrderByRegressionColumnOrder(computedColumnOrder, undefined),
      ).toEqual(expectedColumnOrder);
    });
  });

  describe("groups scores and metrics", () => {
    test.each([
      [
        [
          { id: "a" },
          { id: "b", pinnedColumnIndex: 0 },
          { id: "scores.a" },
          { id: "metrics.a" },
        ],
        ["b", "scores.a", "metrics.a", "a"],
      ],
    ])("(%j, %j) => %j", (computedColumnOrder, expectedColumnOrder) => {
      expect(
        resolveOrderByRegressionColumnOrder(computedColumnOrder, undefined),
      ).toEqual(expectedColumnOrder);
    });
  });
});
