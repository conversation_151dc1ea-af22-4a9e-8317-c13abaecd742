import { cn } from "#/utils/classnames";
import { InfoIcon, X } from "lucide-react";
import { Button } from "./button";

export const InfoBanner = ({
  children,
  className,
  onClose,
  iconClassName,
}: {
  children: React.ReactNode;
  className?: string;
  onClose?: () => void;
  iconClassName?: string;
}) => {
  return (
    <div
      className={cn(
        "my-2 flex items-center gap-2 rounded-md border border-accent-50 bg-accent-50/50 p-2 text-xs text-accent-700",
        className,
      )}
    >
      <InfoIcon className={cn("size-3 flex-none", iconClassName)} />
      <div className="flex-1">{children}</div>
      {onClose && (
        <Button variant="ghost" size="inline" onClick={onClose}>
          <X className="size-3 flex-none" />
        </Button>
      )}
    </div>
  );
};
