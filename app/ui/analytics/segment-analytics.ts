import { useFeatureFlags } from "#/lib/feature-flags";
import { useAnalytics as useSegmentAnalytics } from "#/ui/use-analytics";
import { useOrg } from "#/utils/user";
import {
  type EventName,
  type EventProps,
  eventSchemas,
} from "#/analytics/events";

const EVENT_SOURCE = "web";

export function useAppAnalytics() {
  const { analytics } = useSegmentAnalytics();
  const { isLoading, flags } = useFeatureFlags();
  const org = useOrg();

  function track<K extends EventName>(event: K, properties: EventProps<K>) {
    const schema = eventSchemas[event];
    const result = schema.safeParse(properties);
    if (!result.success) {
      console.error(`Invalid event properties for ${event}:`, result.error);
      return;
    }

    if (analytics) {
      analytics.track(event, {
        ...result.data,
        orgId: org?.id,
        source: EVENT_SOURCE,
        flags: isLoading ? null : JSON.stringify(flags),
      });
    }
  }

  return { track };
}

export function useAnalytics() {
  const { analytics } = useSegmentAnalytics();

  function track<K extends EventName>(event: K, properties: EventProps<K>) {
    const schema = eventSchemas[event];
    const result = schema.safeParse(properties);
    if (!result.success) {
      console.error(`Invalid event properties for ${event}:`, result.error);
      return;
    }

    if (analytics) {
      analytics.track(event, {
        ...result.data,
        source: EVENT_SOURCE,
      });
    }
  }

  return { track };
}
