"use client";

import React, { forwardRef } from "react";
import { cn } from "#/utils/classnames";

const DiffText = forwardRef<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  any,
  React.PropsWithoutRef<React.HTMLAttributes<HTMLSpanElement | HTMLElement>> & {
    mode: "red" | "neutral" | "green";
    className?: string;
    children: React.ReactNode;
    code?: boolean;
  }
>(function DiffText({ mode, className, children, code, ...props }, ref) {
  const Node = code ? "code" : "span";

  return (
    <Node
      className={cn(
        "font-inter tabular-nums",
        {
          "text-bad-700": mode === "red",
          "text-good-700": mode === "green",
        },
        className,
      )}
      {...props}
      ref={ref}
    >
      {children}
    </Node>
  );
});

DiffText.displayName = "DiffText";
export { DiffText };
