"use client";
import {
  /*
  SandpackCodeEditor,
  SandpackFileExplorer,
  */
  SandpackLayout,
  SandpackPreview,
  SandpackProvider,
} from "@codesandbox/sandpack-react";
import { memo } from "react";

export const SandpackViewer = memo(
  ({ value, className }: { value: unknown; className?: string }) => {
    const files = { "index.html": `${value}` };
    return (
      <SandpackProvider files={files} template="static">
        <SandpackLayout
          className={className}
          style={{ height: "600px", border: 0, borderRadius: 6 }}
        >
          {/*<SandpackFileExplorer style={{ height: "100%" }} />
        <SandpackCodeEditor closableTabs showTabs style={{ height: "100%" }} /> */}
          <SandpackPreview
            style={{ height: "100%" }}
            showOpenInCodeSandbox={false}
            showOpenNewtab={false}
          />
        </SandpackLayout>
      </SandpackProvider>
    );
  },
);

SandpackViewer.displayName = "SandpackViewer";
