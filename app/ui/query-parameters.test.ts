import { describe, test, expect } from "vitest";
import type { RowId } from "#/utils/diffs/diff-objects";

import {
  activeRowParserParse,
  activeRowParserSerialize,
  DiffModeOptions,
  parseDiffModeState,
} from "./query-parameters";

function activeRowParserRoundtrip(value: RowId): RowId | null {
  return activeRowParserParse(activeRowParserSerialize(value));
}

describe("activeRowParser", () => {
  for (const value of [
    "hello",
    "318d3dfb-5480-4621-bb88-e04a2d65d041",
    { _bt_internal_right: "bar" },
    { _bt_internal_left: "foo" },
    { _bt_internal_left: "foo", _bt_internal_right: "bar" },
    {
      _bt_internal_left: "foo",
      _bt_internal_right: "bar",
      extra_field: "baz",
    },
    // Try strings which parse to valid JSON values.
    "123456",
    "null",
    "true",
    '"hello"',
    JSON.stringify([1, 2, 3, 4]),
    JSON.stringify({ foo: 12, bar: 44 }),
    "",
    JSON.stringify({}),
  ]) {
    test(`activeRowParser basic - ${value instanceof Object ? JSON.stringify(value) : `"${value}"`}`, () => {
      expect(activeRowParserRoundtrip(value)).toStrictEqual(value);
    });
  }

  for (const disallowedValue of [, {}]) {
    test(`activeRowParser disallowed - ${JSON.stringify(disallowedValue)}`, () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      expect(activeRowParserRoundtrip(disallowedValue as any)).toBe("");
    });
  }
});

describe("parseDiffModeState", () => {
  describe("parse", () => {
    test.each([
      ["", { enabled: false, enabledValue: "between_experiments" }] as const,
      ...DiffModeOptions.options.map(
        (value) =>
          [
            value,
            {
              enabled: value !== "off",
              enabledValue: value === "off" ? "between_experiments" : value,
            },
          ] as const,
      ),
    ])("(%j) => %j", (input, expected) =>
      expect(parseDiffModeState.parse(input)).toStrictEqual(expected),
    );
  });

  test.each([
    ...DiffModeOptions.options.map((value) => [
      { enabled: false, enabledValue: value },
      {
        enabled: false,
        enabledValue: value === "off" ? "between_experiments" : value,
      },
    ]),
    ...DiffModeOptions.options.map((value) => [
      { enabled: true, enabledValue: value },
      {
        enabled: value !== "off",
        enabledValue: value === "off" ? "between_experiments" : value,
      },
    ]),
  ])("(%j) => %j", (input, expected) =>
    expect(
      parseDiffModeState.parse(parseDiffModeState.serialize(input)),
    ).toStrictEqual(expected),
  );
});
