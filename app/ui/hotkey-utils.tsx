// copied from https://github.com/JohannesKlauss/react-hotkeys-hook/blob/main/src/validators.ts

export function isKeyboardEventTriggeredByInput(ev: KeyboardEvent): boolean {
  return isHotkeyEnabledOnTag(ev, ["input", "textarea", "select"]);
}

function isCustomElement(element: HTMLElement): boolean {
  // We just do a basic check w/o any complex RegEx or validation against the list of legacy names containing a hyphen,
  // as none of them is likely to be an event target, and it won't hurt anyway if we miss.
  // see: https://html.spec.whatwg.org/multipage/custom-elements.html#prod-potentialcustomelementname
  return (
    !!element.tagName &&
    !element.tagName.startsWith("-") &&
    element.tagName.includes("-")
  );
}

function isReadonlyArray(value: unknown): value is readonly unknown[] {
  return Array.isArray(value);
}

function isHotkeyEnabledOnTag(
  event: KeyboardEvent,
  enabledOnTags: readonly string[] | boolean = false,
): boolean {
  const { target, composed } = event;

  let targetTagName: string | null = null;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  if (isCustomElement(target as HTMLElement) && composed) {
    targetTagName =
      event.composedPath()[0] &&
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      (event.composedPath()[0] as HTMLElement).tagName;
  } else {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    targetTagName = target && (target as HTMLElement).tagName;
  }

  if (isReadonlyArray(enabledOnTags)) {
    return Boolean(
      targetTagName &&
        enabledOnTags &&
        enabledOnTags.some(
          (tag) => tag.toLowerCase() === targetTagName?.toLowerCase(),
        ),
    );
  }

  return Boolean(targetTagName && enabledOnTags && enabledOnTags);
}
