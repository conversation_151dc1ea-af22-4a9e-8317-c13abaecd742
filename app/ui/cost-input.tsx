import { forwardRef } from "react";
import { Input, type InputProps } from "./input";

export function formatCurrency(value: number) {
  const parts = value.toString().split(".");
  return `$${value}${
    parts.length > 1 ? (parts[1].length === 1 ? "0" : "") : ".00"
  }`;
}

export const CostInput = forwardRef<HTMLInputElement, InputProps>(
  ({ className, onFocus, onBlur, ...props }, ref) => {
    return (
      <Input
        ref={ref}
        className={className}
        onFocus={(e) => {
          const newValue = e.target.value.replace(/[^0-9.]/g, "");
          e.target.value = newValue;
          onFocus && onFocus(e);
        }}
        onBlur={(e) => {
          const newValue = parseFloat(e.target.value.replaceAll("$", ""));
          if (isNaN(newValue)) {
            e.target.value = "";
          } else {
            e.target.value = formatCurrency(newValue);
          }
          onBlur && onBlur(e);
        }}
        {...props}
      />
    );
  },
);
CostInput.displayName = "CostInput";
