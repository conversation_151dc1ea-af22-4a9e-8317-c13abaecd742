import { MONO_FONT_STACK } from "#/ui/fontstack";

export const theme = {
  BASE_FONT_FAMILY: MONO_FONT_STACK,
  BASE_FONT_SIZE: "12px",
  BASE_LINE_HEIGHT: 1.4,

  // BASE_BACKGROUND_COLOR: "white",
  BASE_COLOR: "black",

  OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES: 10,
  OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES: 5,
  OBJECT_NAME_COLOR: "#6f42c1",
  OBJECT_VALUE_NULL_COLOR: "#444444",
  OBJECT_VALUE_UNDEFINED_COLOR: "#444444",
  OBJECT_VALUE_REGEXP_COLOR: "#e36209",
  OBJECT_VALUE_STRING_COLOR: "#032f62",
  OBJECT_VALUE_STRING_SECONDARY_COLOR: "#757575",
  OBJECT_VALUE_SYMBOL_COLOR: "#e36209",
  OBJECT_VALUE_NUMBER_COLOR: "#005cc5",
  OBJECT_VALUE_BOOLEAN_COLOR: "#e36209",
  OBJECT_VALUE_FUNCTION_PREFIX_COLOR: "rgb(13, 34, 170)",

  HTML_TAG_COLOR: "rgb(168, 148, 166)",
  HTML_TAGNAME_COLOR: "rgb(136, 18, 128)",
  HTML_TAGNAME_TEXT_TRANSFORM: "lowercase",
  HTML_ATTRIBUTE_NAME_COLOR: "rgb(153, 69, 0)",
  HTML_ATTRIBUTE_VALUE_COLOR: "rgb(26, 26, 166)",
  HTML_COMMENT_COLOR: "rgb(35, 110, 37)",
  HTML_DOCTYPE_COLOR: "rgb(192, 192, 192)",

  ARROW_COLOR: "#6e6e6e",
  ARROW_MARGIN_RIGHT: 3,
  ARROW_FONT_SIZE: 12,
  ARROW_ANIMATION_DURATION: "0",

  TREENODE_FONT_FAMILY: MONO_FONT_STACK,
  TREENODE_FONT_SIZE: "12px",
  TREENODE_LINE_HEIGHT: 1.3,
  TREENODE_PADDING_LEFT: 12,

  TABLE_BORDER_COLOR: "#aaa",
  TABLE_TH_BACKGROUND_COLOR: "#eee",
  TABLE_TH_HOVER_COLOR: "hsla(0, 0%, 90%, 1)",
  TABLE_SORT_ICON_COLOR: "#6e6e6e",
  TABLE_DATA_BACKGROUND_IMAGE:
    "linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))",
  TABLE_DATA_BACKGROUND_SIZE: "128px 32px",
};
