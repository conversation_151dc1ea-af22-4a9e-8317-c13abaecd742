import { MONO_FONT_STACK } from "#/ui/fontstack";

// @eden this file has been modified to use custom styles
export const theme = {
  BASE_FONT_FAMILY: MONO_FONT_STACK,
  BASE_FONT_SIZE: "12px",
  BASE_LINE_HEIGHT: 1.4,

  // BASE_BACKGROUND_COLOR: "rgb(36, 36, 36)",
  BASE_COLOR: "rgb(213, 213, 213)",

  OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES: 2,
  OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES: 5,
  OBJECT_NAME_COLOR: "#d2a8ff",
  OBJECT_VALUE_NULL_COLOR: "#888888",
  OBJECT_VALUE_UNDEFINED_COLOR: "#888888",
  OBJECT_VALUE_REGEXP_COLOR: "#79c0ff",
  OBJECT_VALUE_STRING_COLOR: "#a5d6ff",
  OBJECT_VALUE_STRING_SECONDARY_COLOR: "#909090",
  OBJECT_VALUE_SYMBOL_COLOR: "#79c0ff",
  OBJECT_VALUE_NUMBER_COLOR: "#79c0ff",
  OBJECT_VALUE_BOOLEAN_COLOR: "#ffab70",
  OBJECT_VALUE_FUNCTION_PREFIX_COLOR: "rgb(85, 106, 242)",

  HTML_TAG_COLOR: "rgb(93, 176, 215)",
  HTML_TAGNAME_COLOR: "rgb(93, 176, 215)",
  HTML_TAGNAME_TEXT_TRANSFORM: "lowercase",
  HTML_ATTRIBUTE_NAME_COLOR: "rgb(155, 187, 220)",
  HTML_ATTRIBUTE_VALUE_COLOR: "rgb(242, 151, 102)",
  HTML_COMMENT_COLOR: "rgb(137, 137, 137)",
  HTML_DOCTYPE_COLOR: "rgb(192, 192, 192)",

  ARROW_COLOR: "rgb(145, 145, 145)",
  ARROW_MARGIN_RIGHT: 3,
  ARROW_FONT_SIZE: 12,
  ARROW_ANIMATION_DURATION: "0",

  TREENODE_FONT_FAMILY: MONO_FONT_STACK,
  TREENODE_FONT_SIZE: "12px",
  TREENODE_LINE_HEIGHT: 1.3,
  TREENODE_PADDING_LEFT: 12,

  TABLE_BORDER_COLOR: "rgb(85, 85, 85)",
  TABLE_TH_BACKGROUND_COLOR: "rgb(44, 44, 44)",
  TABLE_TH_HOVER_COLOR: "rgb(48, 48, 48)",
  TABLE_SORT_ICON_COLOR: "black", //'rgb(48, 57, 66)',
  TABLE_DATA_BACKGROUND_IMAGE:
    "linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))",
  TABLE_DATA_BACKGROUND_SIZE: "128px 32px",
};
