export const DEFAULT_ROOT_PATH = "$";

const WILDCARD = "*";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function hasChildNodes(data: any, dataIterator: any) {
  return !dataIterator(data).next().done;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const wildcardPathsFromLevel = (level: any) => {
  // i is depth
  return Array.from({ length: level }, (_, i) =>
    [DEFAULT_ROOT_PATH].concat(Array.from({ length: i }, () => "*")).join("."),
  );
};

export const getExpandedPaths = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  path: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  foldPath: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  dataIterator: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  expandPaths: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  expandLevel: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  prevExpandedPaths: any,
): {
  foldPaths: Record<string, boolean>;
  expandedPaths: Record<string, boolean>;
} => {
  const rootPath = path || DEFAULT_ROOT_PATH;
  const rootFoldPath = foldPath || [];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const wildcardPaths: any[] = []
    //@ts-ignore
    .concat(wildcardPathsFromLevel(expandLevel))
    .concat(expandPaths)
    .filter((path) => typeof path === "string"); // could be undefined

  const expandedPaths: string[] = [];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const foldPaths: any[] = [];
  wildcardPaths.forEach((wildcardPath) => {
    const keyPaths = wildcardPath.split(".");
    //@ts-ignore
    const populatePaths = (curData, curFoldPath, curPath, depth) => {
      if (depth === keyPaths.length) {
        expandedPaths.push(curPath);
        foldPaths.push(curFoldPath);
        return;
      }
      const key = keyPaths[depth];
      if (depth === 0) {
        if (
          hasChildNodes(curData, dataIterator) &&
          (key === DEFAULT_ROOT_PATH || key === WILDCARD)
        ) {
          populatePaths(curData, rootFoldPath, rootPath, depth + 1);
        }
      } else {
        if (key === WILDCARD) {
          for (const { name, data } of dataIterator(curData)) {
            if (hasChildNodes(data, dataIterator)) {
              const childPath = Array.isArray(curData)
                ? `${curPath}[${name}]`
                : `${curPath}.${name}`;
              const childFoldPath = curFoldPath.concat(
                Array.isArray(curData) ? [[name]] : name,
              );
              populatePaths(data, childFoldPath, childPath, depth + 1);
            }
          }
        } else {
          const value = curData[key];
          if (hasChildNodes(value, dataIterator)) {
            populatePaths(
              value,
              curFoldPath.concat(key),
              `${curPath}.${key}`,
              depth + 1,
            );
          }
        }
      }
    };

    populatePaths(data, [], "", 0);
  });

  return {
    foldPaths: Object.fromEntries([
      ...foldPaths.map((path) => [JSON.stringify(path), true]),
      ...Object.entries(prevExpandedPaths.foldPaths),
    ]),
    expandedPaths: expandedPaths.reduce(
      (obj, path) => {
        obj[path] = !!(path && path.split(".").length < 2);
        return obj;
      },
      { ...prevExpandedPaths.expandedPaths },
    ),
  };
};
