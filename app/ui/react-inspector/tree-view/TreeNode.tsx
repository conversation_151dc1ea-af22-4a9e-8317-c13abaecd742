/* eslint-disable @typescript-eslint/no-empty-function */
import React, { Children, type FC, memo } from "react";
import { useStyles } from "#/ui/react-inspector/styles";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const Arrow: FC<any> = ({ expanded, styles }) => (
  <span
    style={{
      ...styles.base,
      ...(expanded ? styles.expanded : styles.collapsed),
    }}
  >
    ▶
  </span>
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const TreeNode: FC<any> = memo((props) => {
  props = {
    expanded: true,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    nodeRenderer: ({ name }: any) => <span>{name}</span>,
    onClick: () => {},
    shouldShowArrow: false,
    shouldShowPlaceholder: true,
    ...props,
  };
  const {
    expanded,
    onClick,
    children,
    node<PERSON><PERSON>er,
    title,
    shouldShowArrow,
    shouldShowPlaceholder,
  } = props;

  const styles = useStyles("TreeNode");
  const NodeRenderer = nodeRenderer;

  return (
    <li
      aria-expanded={expanded}
      role="treeitem"
      aria-selected="false"
      style={styles.treeNodeBase}
      title={title}
    >
      <div style={styles.treeNodePreviewContainer} onClick={onClick}>
        {shouldShowArrow || Children.count(children) > 0 ? (
          <Arrow expanded={expanded} styles={styles.treeNodeArrow} />
        ) : (
          shouldShowPlaceholder && (
            <span style={styles.treeNodePlaceholder}>&nbsp;</span>
          )
        )}
        <NodeRenderer {...props} />
      </div>

      <ol role="group" style={styles.treeNodeChildNodesContainer}>
        {expanded ? children : undefined}
      </ol>
    </li>
  );
});
TreeNode.displayName = "TreeNode";

// TreeNode.propTypes = {
//   name: PropTypes.string,
//   data: PropTypes.any,
//   expanded: PropTypes.bool,
//   shouldShowArrow: PropTypes.bool,
//   shouldShowPlaceholder: PropTypes.bool,
//   nodeRenderer: PropTypes.func,
//   onClick: PropTypes.func,
// };
