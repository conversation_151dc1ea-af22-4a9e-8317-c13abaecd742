import React, {
  useContext,
  useCallback,
  useLayoutEffect,
  useState,
  memo,
  useRef,
  useImperativeHandle,
} from "react";
import { ExpandedPathsContext } from "./ExpandedPathsContext";
import { TreeNode } from "./TreeNode";
import {
  DEFAULT_ROOT_PATH,
  hasChildNodes,
  getExpandedPaths,
} from "./pathUtils";

import { useStyles } from "#/ui/react-inspector/styles";
import { isAlphaNumeric } from "#/utils/string";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const ConnectedTreeNode = memo<any>((props: any) => {
  const {
    data: parentData,
    dataIterator,
    path,
    foldPath,
    foldState,
    setFoldState,
    isCollapsed,
    depth,
    nodeRenderer,
  } = props;
  const [{ expandedPaths }, setExpandedPaths] =
    useContext(ExpandedPathsContext);
  const nodeHasChildNodes = hasChildNodes(parentData, dataIterator);

  const expanded = isCollapsed
    ? !isCollapsed && nodeHasChildNodes
    : foldState
      ? !foldState[JSON.stringify(foldPath)]
      : !!expandedPaths[path];

  const handleClick = useCallback(() => {
    if (!nodeHasChildNodes) {
      return;
    }

    if (setFoldState) {
      setFoldState((prev: Record<string, boolean>) => {
        const key = JSON.stringify(foldPath);
        return {
          ...prev,
          [key]: !prev[key],
        };
      });
      return;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    setExpandedPaths((prevExpandedPaths: any) => ({
      ...prevExpandedPaths,
      expandedPaths: {
        ...prevExpandedPaths.expandedPaths,
        [path]: !expanded,
      },
    }));
  }, [
    nodeHasChildNodes,
    setExpandedPaths,
    path,
    foldPath,
    setFoldState,
    expanded,
  ]);

  return (
    <TreeNode
      expanded={expanded}
      onClick={handleClick}
      // show arrow anyway even if not expanded and not rendering children
      shouldShowArrow={nodeHasChildNodes}
      // show placeholder only for non root nodes
      shouldShowPlaceholder={depth > 0}
      // Render a node from name and data (or possibly other props like isNonenumerable)
      nodeRenderer={nodeRenderer}
      {...props}
    >
      {
        // only render if the node is expanded
        expanded
          ? [...dataIterator(parentData)].map(
              ({ name, data, ...renderNodeProps }) => {
                const escapedName = isAlphaNumeric(name) ? name : `"${name}"`;
                const childPath = Array.isArray(parentData)
                  ? `${path}[${escapedName}]`
                  : `${path}.${escapedName}`;
                const childFoldPath = foldPath.concat(
                  Array.isArray(parentData) ? [[name]] : name,
                );
                return (
                  <ConnectedTreeNode
                    name={name}
                    data={data}
                    depth={depth + 1}
                    path={childPath}
                    foldPath={childFoldPath}
                    foldState={foldState}
                    setFoldState={setFoldState}
                    key={name}
                    dataIterator={dataIterator}
                    nodeRenderer={nodeRenderer}
                    onApplySearch={props.onApplySearch}
                    {...renderNodeProps}
                  />
                );
              },
            )
          : null
      }
    </TreeNode>
  );
});
ConnectedTreeNode.displayName = "ConnectedTreeNode";

// ConnectedTreeNode.propTypes = {
//   name: PropTypes.string,
//   data: PropTypes.any,
//   dataIterator: PropTypes.func,
//   depth: PropTypes.number,
//   expanded: PropTypes.bool,
//   nodeRenderer: PropTypes.func,
// };

export const TreeView = ({
  name,
  path,
  data,
  dataIterator,
  nodeRenderer,
  expandPaths,
  expandLevel,
  onApplySearch,
  foldPath,
  foldState,
  setFoldState,
  isCollapsed,
  treeRef,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
}: any) => {
  const styles = useStyles("TreeView");
  const stateAndSetter = useState({
    expandedPaths: {},
    foldPaths: {},
  });
  const [expandedPaths, setExpandedPaths] = stateAndSetter;

  const initializedExpansion = useRef<boolean>(false);
  useLayoutEffect(
    () => {
      if (initializedExpansion.current) {
        return;
      }
      setExpandedPaths((prevExpandedPaths) => {
        const paths = getExpandedPaths(
          data,
          path,
          foldPath,
          dataIterator,
          expandPaths,
          expandLevel,
          prevExpandedPaths,
        );
        return paths;
      });
      initializedExpansion.current = true;
    },
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data, dataIterator, expandPaths, expandLevel, setExpandedPaths],
  );

  useImperativeHandle(
    treeRef,
    () => ({
      getAllFoldPaths: () => {
        return Object.keys(expandedPaths.foldPaths);
      },
    }),
    [expandedPaths],
  );

  return (
    <ExpandedPathsContext.Provider value={stateAndSetter}>
      <ol role="tree" style={styles.treeViewOutline}>
        <ConnectedTreeNode
          //@ts-ignore
          name={name}
          data={data}
          dataIterator={dataIterator}
          depth={0}
          path={path || DEFAULT_ROOT_PATH}
          foldPath={[]}
          foldState={foldState}
          setFoldState={setFoldState}
          isCollapsed={isCollapsed}
          nodeRenderer={nodeRenderer}
          onApplySearch={onApplySearch}
          expandLevel={expandLevel}
        />
      </ol>
    </ExpandedPathsContext.Provider>
  );
};
TreeView.displayName = "TreeView";

// TreeView.propTypes = {
//   name: PropTypes.string,
//   data: PropTypes.any,
//   dataIterator: PropTypes.func,
//   nodeRenderer: PropTypes.func,
//   expandPaths: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
//   expandLevel: PropTypes.number,
// };
