import React from "react";
import { ObjectValue } from "#/ui/react-inspector/object/ObjectValue";

import { hasOwnProperty } from "#/ui/react-inspector/utils/objectPrototype";

import { useStyles } from "#/ui/react-inspector/styles";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const DataContainer = ({ rows, columns, rowsData }: any) => {
  const styles = useStyles("TableInspectorDataContainer");
  const borderStyles = useStyles("TableInspectorLeftBorder");

  return (
    <div style={styles.div}>
      <table style={styles.table}>
        <colgroup />
        <tbody>
          {/* eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION */}
          {rows.map((row: any, i: any) => (
            <tr key={row} style={styles.tr}>
              <td style={{ ...styles.td, ...borderStyles.none }}>{row}</td>

              {/* eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION */}
              {columns.map((column: any) => {
                const rowData = rowsData[i];
                // rowData could be
                //  object -> index by key
                //    array -> index by array index
                //    null -> pass
                //  boolean -> pass
                //  string -> pass (hasOwnProperty returns true for [0..len-1])
                //  number -> pass
                //  function -> pass
                //  symbol
                //  undefined -> pass
                if (
                  typeof rowData === "object" &&
                  rowData !== null &&
                  hasOwnProperty.call(rowData, column)
                ) {
                  return (
                    <td
                      key={column}
                      style={{ ...styles.td, ...borderStyles.solid }}
                    >
                      <ObjectValue object={rowData[column]} />
                    </td>
                  );
                } else {
                  return (
                    <td
                      key={column}
                      style={{ ...styles.td, ...borderStyles.solid }}
                    />
                  );
                }
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
