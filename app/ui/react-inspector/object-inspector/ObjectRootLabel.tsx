import React, { type FC } from "react";
import { ObjectName } from "#/ui/react-inspector/object/ObjectName";
import { ObjectPreview } from "./ObjectPreview";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const ObjectRootLabel: FC<any> = ({ name, data }) => {
  if (typeof name === "string") {
    return (
      <span>
        <ObjectName name={name} />
        <span>: </span>
        <ObjectPreview data={data} />
      </span>
    );
  } else {
    return <ObjectPreview data={data} />;
  }
};
