import React, { type ReactNode, type FC } from "react";

import { ObjectValue } from "#/ui/react-inspector/object/ObjectValue";
import { ObjectName } from "#/ui/react-inspector/object/ObjectName";

import { useStyles } from "#/ui/react-inspector/styles";

import { hasOwnProperty } from "#/ui/react-inspector/utils/objectPrototype";
import { getPropertyValue } from "#/ui/react-inspector/utils/propertyUtils";
import { truncateMiddle } from "#/utils/string";

/* intersperse arr with separator */
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function intersperse(arr: any[], sep: string) {
  if (arr.length === 0) {
    return [];
  }

  return arr.slice(1).reduce((xs, x) => xs.concat([sep, x]), [arr[0]]);
}

/**
 * A preview of the object
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const ObjectPreview: FC<any> = ({ data }) => {
  const styles = useStyles("ObjectPreview");
  const object = data;

  if (
    typeof object !== "object" ||
    object === null ||
    object instanceof Date ||
    object instanceof RegExp
  ) {
    return (
      <ObjectValue object={object} ObjectPreviewComponent={ObjectPreview} />
    );
  }

  if (Array.isArray(object)) {
    const maxProperties = styles.arrayMaxProperties;
    const previewArray = object
      .slice(0, maxProperties)
      .map((element, index) => (
        <ObjectValue
          key={index}
          object={element}
          ObjectPreviewComponent={ObjectPreview}
        />
      ));
    if (object.length > maxProperties) {
      previewArray.push(<span key="ellipsis">…</span>);
    }
    const arrayLength = object.length;
    return (
      <React.Fragment>
        <span style={styles.objectDescription}>
          {arrayLength === 0 ? `` : `(${arrayLength})\xa0`}
        </span>
        <span style={styles.preview}>[{intersperse(previewArray, ", ")}]</span>
      </React.Fragment>
    );
  } else {
    const maxProperties = styles.objectMaxProperties;
    const propertyNodes: ReactNode[] = [];
    for (const propertyName in object) {
      if (hasOwnProperty.call(object, propertyName)) {
        let ellipsis;
        if (
          propertyNodes.length === maxProperties - 1 &&
          Object.keys(object).length > maxProperties
        ) {
          ellipsis = <span key={"ellipsis"}>…</span>;
        }

        const propertyValue = getPropertyValue(object, propertyName);
        propertyNodes.push(
          <span key={propertyName}>
            <ObjectName name={propertyName || `""`} secondary />
            :&nbsp;
            <ObjectValue
              object={
                typeof propertyValue === "string"
                  ? truncateMiddle(propertyValue)
                  : propertyValue
              }
              isPreview
              ObjectPreviewComponent={ObjectPreview}
            />
            {ellipsis}
          </span>,
        );
        if (ellipsis) break;
      }
    }

    const objectConstructorName = object.constructor
      ? object.constructor.name
      : "Object";

    return (
      <React.Fragment>
        <span style={styles.objectDescription}>
          {objectConstructorName === "Object"
            ? ""
            : `${objectConstructorName} `}
        </span>
        <span style={styles.preview}>
          {"{"}
          {intersperse(propertyNodes, ", ")}
          {"}"}
        </span>
      </React.Fragment>
    );
  }
};
