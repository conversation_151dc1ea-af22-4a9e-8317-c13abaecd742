import React, { type FC } from "react";
import { ObjectName } from "#/ui/react-inspector/object/ObjectName";
import { ObjectValue } from "#/ui/react-inspector/object/ObjectValue";
import { ObjectPreview } from "./ObjectPreview";

/**
 * if isNonenumerable is specified, render the name dimmed
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const ObjectLabel: FC<any> = ({
  name,
  data,
  isNonenumerable = false,
  expanded,
}) => {
  const object = data;

  return (
    <span>
      {typeof name === "string" ? (
        <ObjectName name={name} dimmed={isNonenumerable} />
      ) : (
        <ObjectPreview data={name} />
      )}
      <span>: </span>
      <ObjectValue
        object={object}
        expanded={expanded}
        ObjectPreviewComponent={ObjectPreview}
      />
    </span>
  );
};

// ObjectLabel.propTypes = {
//   /** Non enumerable object property will be dimmed */
//   isNonenumerable: PropTypes.bool,
// };
