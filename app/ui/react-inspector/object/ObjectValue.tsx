import React, { type FC } from "react";

import { useStyles } from "#/ui/react-inspector/styles";
import { urlMatcherRegexp } from "#/utils/url";

/**
 * A short description of the object values.
 * Can be used to render tree node in ObjectInspector
 * or render objects in TableInspector.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const ObjectValue: FC<any> = ({
  object,
  styles,
  isPreview = false,
  expanded,
  ObjectPreviewComponent,
}) => {
  const themeStyles = useStyles("ObjectValue");

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const mkStyle = (key: any) => ({ ...themeStyles[key], ...styles });

  switch (typeof object) {
    case "bigint":
      return (
        <span style={mkStyle("objectValueNumber")}>{String(object)}n</span>
      );
    case "number":
      return <span style={mkStyle("objectValueNumber")}>{String(object)}</span>;
    case "string":
      // eslint-disable-next-line react-compiler/react-compiler
      urlMatcherRegexp.lastIndex = 0;
      const parts = object.split(urlMatcherRegexp).filter(Boolean);

      const value = parts.map((part, i) => {
        urlMatcherRegexp.lastIndex = 0;
        return urlMatcherRegexp.test(part) ? (
          <a
            key={i}
            style={mkStyle("objectValueLink")}
            href={part}
            target="_blank"
          >
            {part}
          </a>
        ) : (
          <span key={i} style={mkStyle("objectValueString")}>
            {part}
          </span>
        );
      });

      return <>&quot;{value}&quot;</>;
    case "boolean":
      return (
        <span style={mkStyle("objectValueBoolean")}>{String(object)}</span>
      );
    case "undefined":
      return <span style={mkStyle("objectValueUndefined")}>undefined</span>;
    case "object":
      if (object === null) {
        return <span style={mkStyle("objectValueNull")}>null</span>;
      }
      if (object instanceof Date) {
        return <span>{object.toString()}</span>;
      }
      if (object instanceof RegExp) {
        return (
          <span style={mkStyle("objectValueRegExp")}>{object.toString()}</span>
        );
      }
      if (Array.isArray(object)) {
        return <span>{`Array(${object.length})`}</span>;
      }
      if (!object.constructor) {
        return <span>Object</span>;
      }
      if (
        typeof object.constructor.isBuffer === "function" &&
        object.constructor.isBuffer(object)
      ) {
        return <span>{`Buffer[${object.length}]`}</span>;
      }

      return !expanded ? (
        <span>
          {!isPreview && ObjectPreviewComponent ? (
            <ObjectPreviewComponent data={object} />
          ) : (
            object.constructor.name
          )}
        </span>
      ) : null;
    case "function":
      return (
        <span>
          <span style={mkStyle("objectValueFunctionPrefix")}>ƒ&nbsp;</span>
          <span style={mkStyle("objectValueFunctionName")}>
            {object.name}()
          </span>
        </span>
      );
    case "symbol":
      return (
        <span style={mkStyle("objectValueSymbol")}>{object.toString()}</span>
      );
    default:
      return <span />;
  }
};

// ObjectValue.propTypes = {
//   // the object to describe
//   object: PropTypes.any,
// };
