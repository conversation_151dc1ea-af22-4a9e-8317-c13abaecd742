import React, { type FC } from "react";
import { useStyles } from "#/ui/react-inspector/styles";

/**
 * A view for object property names.
 *
 * If the property name is enumerable (in Object.keys(object)),
 * the property name will be rendered normally.
 *
 * If the property name is not enumerable (`Object.prototype.propertyIsEnumerable()`),
 * the property name will be dimmed to show the difference.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const ObjectName: FC<any> = ({
  name,
  dimmed = false,
  secondary = false,
  styles = {},
}) => {
  const themeStyles = useStyles("ObjectName");
  const appliedStyles = {
    ...themeStyles.base,
    ...(dimmed ? themeStyles["dimmed"] : {}),
    ...(secondary ? themeStyles["secondary"] : {}),
    ...styles,
  };

  return <span style={appliedStyles}>{name}</span>;
};

// ObjectName.propTypes = {
//   /** Property name */
//   name: PropTypes.string,
//   /** Should property name be dimmed */
//   dimmed: PropTypes.bool,
// };
