import React, { type FC, useContext, useLayoutEffect, useRef } from "react";

import { DOMNodePreview } from "./DOMNodePreview";
import { ConnectedTreeNode } from "#/ui/react-inspector/tree-view/TreeView";

import { shouldInline } from "./shouldInline";
import { themeAcceptor } from "#/ui/react-inspector/styles";
import { ExpandedPathsContext } from "#/ui/react-inspector/tree-view/ExpandedPathsContext";
import { getExpandedPaths } from "#/ui/react-inspector/tree-view/pathUtils";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const domIterator = function* (data: any) {
  if (data && data.childNodes) {
    const textInlined = shouldInline(data);

    if (textInlined) {
      return;
    }

    for (let i = 0; i < data.childNodes.length; i++) {
      const node = data.childNodes[i];

      if (
        node.nodeType === Node.TEXT_NODE &&
        node.textContent.trim().length === 0
      )
        continue;

      yield {
        name: `${node.tagName}[${i}]`,
        data: node,
      };
    }

    // at least 1 child node
    if (data.tagName) {
      yield {
        name: "CLOSE_TAG",
        data: {
          tagName: data.tagName,
        },
        isCloseTag: true,
      };
    }
  }
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const DOMInspector: FC<any> = (props) => {
  const [_, setExpandedPaths] = useContext(ExpandedPathsContext);

  const initializedExpansion = useRef<boolean>(false);
  useLayoutEffect(() => {
    if (initializedExpansion.current) {
      return;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    setExpandedPaths((prevExpandedPaths: any) => {
      const paths = getExpandedPaths(
        props.data,
        props.path,
        props.foldPath,
        domIterator,
        undefined,
        props.expandLevel,
        prevExpandedPaths,
      );
      return paths;
    });
    initializedExpansion.current = true;
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.data, props.expandLevel, setExpandedPaths]);

  return (
    <ConnectedTreeNode
      nodeRenderer={DOMNodePreview}
      dataIterator={domIterator}
      {...props}
    />
  );
};

// DOMInspector.propTypes = {
//   // The DOM Node to inspect
//   data: PropTypes.object.isRequired,
// };

const themedDOMInspector = themeAcceptor(DOMInspector);

export {
  themedDOMInspector as DOMInspector,
  DOMInspector as UnthemedDOMInspector,
};
