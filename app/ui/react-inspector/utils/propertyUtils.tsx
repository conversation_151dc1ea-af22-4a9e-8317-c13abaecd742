// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function getPropertyValue(object: any, propertyName: any) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const propertyDescriptor: any = Object.getOwnPropertyDescriptor(
    object,
    propertyName,
  );
  if (propertyDescriptor.get) {
    try {
      return propertyDescriptor.get();
    } catch {
      return propertyDescriptor.get;
    }
  }

  return object[propertyName];
}
