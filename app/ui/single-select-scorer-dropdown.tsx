import {
  type getProjectSummary,
  type ProjectSummary,
} from "#/app/app/[org]/org-actions";
import { AUTOEVALS } from "#/app/app/[org]/prompt/[prompt]/scorers/scorers-dropdown";
import { useQueryFunc } from "#/utils/react-query";
import { type SavedScorer } from "#/utils/scorers";
import { useOrg } from "#/utils/user";
import { DropdownMenuCheckboxItem } from "#/ui/dropdown-menu";
import { Tooltip, TooltipTrigger, TooltipContent } from "#/ui/tooltip";
import { Percent } from "lucide-react";
import { useState, useMemo, forwardRef, type HTMLAttributes } from "react";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { type UIFunction } from "#/ui/prompts/schema";

export const ScorerDropdown = ({
  projectName,
  projectId,
  selectedScorer,
  updateScorers,
  functions,
  children,
}: {
  projectName: string;
  projectId: string;
  selectedScorer: SavedScorer | null;
  updateScorers: (scorer: SavedScorer) => Promise<null>;
  functions: Record<string, UIFunction>;
  children: React.ReactNode;
}) => {
  const [open, setOpen] = useState(false);
  const org = useOrg();

  // Get project information for grouping
  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: org.name,
    },
  });

  const projectsById = useMemo(() => {
    if (!projects) return undefined;
    return Object.fromEntries(
      projects.map((p: ProjectSummary) => [p.project_id, p]),
    );
  }, [projects]);

  // Get the currently selected scorer
  const selectedScorerId = selectedScorer
    ? selectedScorer.type === "function"
      ? selectedScorer.id
      : selectedScorer.name
    : undefined;

  const handleSelectScorer = (scorer: SavedScorer) => {
    // Single select - replace any existing selection
    updateScorers(scorer);
    setOpen(false); // Close dropdown on selection
  };
  // Group scorers by project
  const groupedScorers = useMemo(() => {
    const scorersByProject = Object.values(functions)
      .filter((f) => f.function_type === "scorer")
      .reduce<{
        [projectId: string]: {
          projectId: string;
          projectName?: string;
          scorers: UIFunction[];
        };
      }>((acc, func) => {
        const { project_id } = func;
        if (!acc[project_id]) {
          acc[project_id] = {
            projectId: project_id,
            projectName: projectsById?.[project_id]?.project_name,
            scorers: [],
          };
        }
        acc[project_id].scorers.push(func);
        return acc;
      }, {});

    return Object.values(scorersByProject).sort((a, b) =>
      a.projectName === projectName
        ? -1
        : b.projectName === projectName
          ? 1
          : (a.projectName ?? "").localeCompare(b.projectName ?? ""),
    );
  }, [functions, projectsById, projectName]);

  // Prepare dropdown data with project scorers and AutoEvals
  const dropdownData = useMemo(() => {
    const mainItems =
      groupedScorers[0]?.projectName === projectName
        ? groupedScorers[0].scorers.sort((a, b) =>
            (a.name ?? "").localeCompare(b.name ?? ""),
          )
        : undefined;

    const otherProjects = groupedScorers
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ projectName, scorers }) =>
        scorers
          .map((s) => ({ ...s, projectName }))
          .sort((a, b) => (a.name ?? "").localeCompare(b.name ?? "")),
      );

    const subGroups = [
      ...(otherProjects.length > 0
        ? [
            {
              groupLabel: "Other projects",
              items: otherProjects,
            },
          ]
        : []),
      {
        groupLabel: "AutoEvals",
        items: AUTOEVALS.filter(({ template }) => template), // Only show AutoEvals with templates
      },
    ];

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [groupedScorers, projectName]);

  const ScorerMenuItem = forwardRef<
    HTMLDivElement,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    { item: any } & HTMLAttributes<HTMLDivElement>
  >(({ item, ...rest }, ref) => {
    // Check if it's a function scorer or AutoEval
    const isFunction = "id" in item;
    const isChecked = isFunction
      ? item.id === selectedScorerId
      : item.method.name === selectedScorerId;

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenuCheckboxItem
            {...rest}
            ref={ref}
            onSelect={() => {
              if (isFunction) {
                handleSelectScorer({ type: "function", id: item.id });
              } else {
                handleSelectScorer({ type: "global", name: item.method.name });
              }
            }}
            checked={isChecked}
          >
            <span className="flex w-full flex-1 items-center gap-2">
              <Percent className="size-3 text-good-600" />
              <span className="flex-1">
                {isFunction ? item.name : item.method.name}
              </span>
              <span className="text-xs text-primary-500">
                {item.function_data?.type === "prompt"
                  ? "LLM Judge"
                  : item.function_data?.type === "code"
                    ? "Code"
                    : "AutoEvals"}
              </span>
            </span>
          </DropdownMenuCheckboxItem>
        </TooltipTrigger>
        <TooltipContent side="right" align="start" className="max-w-sm text-xs">
          <div className="text-sm font-medium">
            {isFunction ? item.name : item.method.name}
          </div>
          {item.description && <div>{item.description}</div>}
          {isFunction && item.slug && (
            <div className="font-mono text-[10px] text-primary-600">
              {item.slug}
            </div>
          )}
          {!isFunction && (
            <div className="mt-1 text-primary-600">
              This scorer comes from the AutoEvals collection
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    );
  });
  ScorerMenuItem.displayName = "ScorerMenuItem";

  return (
    <NestedDropdown
      objectType="scorer"
      open={open}
      setOpen={setOpen}
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      DropdownItemComponent={ScorerMenuItem}
      filterItems={(search, opts) =>
        opts.filter((opt) => {
          const name = "id" in opt ? opt.name : opt.method?.name;
          return name?.toLocaleLowerCase().includes(search.toLocaleLowerCase());
        })
      }
    >
      {children}
    </NestedDropdown>
  );
};
