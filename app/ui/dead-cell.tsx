import { cn } from "#/utils/classnames";
import { type TdHTMLAttributes } from "react";

interface DeadCellProps {
  isHeader?: boolean;
  isGroupRow?: boolean;
  hideBorder?: boolean;
  className?: string;
  onClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  onAuxClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
}

export const DeadCell = ({
  className,
  onClick,
  onAuxClick,
  isHeader = false,
  isGroupRow = false,
  hideBorder = false,
}: DeadCellProps) => {
  const cellProps: TdHTMLAttributes<HTMLElement> = {
    className: cn("min-w-px grow py-2", className, {
      "cursor-pointer": onClick,
      "border-primary-200 py-3": isGroupRow,
    }),
    style: {
      maxWidth: "99%",
    },
    onClick,
    onAuxClick,
  };

  return isHeader || isGroupRow ? (
    <div {...cellProps}>
      <div
        className={cn("h-full", {
          "border-l": !hideBorder,
        })}
      />
    </div>
  ) : (
    <div {...cellProps} />
  );
};
