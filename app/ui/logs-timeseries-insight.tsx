"use client";

import { MonitorCard } from "#/app/app/[org]/monitor/card/monitor-card";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
  useState,
} from "react";
import { type Clause } from "#/utils/search/search";
import { type DataObjectSearch } from "#/utils/btapi/btapi";
import { type MonitorCardConfig } from "#/app/app/[org]/monitor/card/monitor-card-config.types";
import { type CardState } from "#/app/app/[org]/monitor/monitor-cards";
import { type TimeRangeFilter } from "#/utils/view/use-view";
import { TIME_RANGE_TO_MILLISECONDS } from "#/app/app/[org]/monitor/time-controls/time-range";
import { ALL_TIME_RANGE } from "./time-range-select/get-time-range-options";
import { parseDateString } from "./time-range-select/parse-date-string";
import { getRoundedTimeFrame } from "#/app/app/[org]/monitor/time-controls/get-rounded-time-frame";

interface LogsTimeseriesInsightProps {
  projectId: string;
  filters: DataObjectSearch["filters"] | undefined;
  title?: string;
  onBucketClick: (bucket: Clause<"filter">) => void;
  timeRangeFilter?: TimeRangeFilter;
  setTimeRangeFilter?: Dispatch<SetStateAction<TimeRangeFilter>>;
}

const EMPTY_EXPERIMENT_IDS: string[] = [];

export function LogsTimeseriesInsight({
  projectId,
  filters,
  timeRangeFilter,
  setTimeRangeFilter,
}: LogsTimeseriesInsightProps) {
  const [_cardState, setCardState] = useState<CardState>({
    isLoading: false,
    hasData: false,
    hasError: false,
  });

  const cardConfig = useMemo(
    (): MonitorCardConfig => ({
      name: "logs-timeseries",
      idName: "logs",
      vizType: "bars",
      measures: [
        {
          alias: "spans",
          displayName: "Spans",
          btql: "count(1)",
        },
      ],
      unitType: "count",
    }),
    [],
  );

  const chartTimeFrame = useMemo(() => {
    const now = Date.now();

    if (!timeRangeFilter) {
      // Default to 7 days if no filter provided
      return {
        start: now - TIME_RANGE_TO_MILLISECONDS["7d"],
        end: now,
      };
    }

    if (typeof timeRangeFilter === "string") {
      // Handle predefined time ranges like "3d", "7d", etc.
      const duration =
        TIME_RANGE_TO_MILLISECONDS[
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          timeRangeFilter as keyof typeof TIME_RANGE_TO_MILLISECONDS
        ];
      if (duration) {
        return {
          start: now - duration,
          end: now,
        };
      }
      // for all time show at least 90 days, allTimeQuery can extend this
      if (timeRangeFilter === ALL_TIME_RANGE) {
        return {
          start: now - TIME_RANGE_TO_MILLISECONDS["90d"],
          end: now,
        };
      }

      // Fallback to 7 days
      return {
        start: now - TIME_RANGE_TO_MILLISECONDS["7d"],
        end: now,
      };
    }

    // Handle custom date range
    const fromDate = parseDateString(timeRangeFilter.from);
    const toDate = parseDateString(timeRangeFilter.to);
    return {
      start: fromDate.getTime(),
      end: toDate.getTime(),
    };
  }, [timeRangeFilter]);

  const handleBrush = (range: [number, number] | null) => {
    if (!range || !setTimeRangeFilter) {
      return;
    }

    const [start, end] = range;
    const rounded = getRoundedTimeFrame({ start, end });

    const fromString = new Date(rounded.start).toISOString();
    const toString = new Date(rounded.end).toISOString();

    const timeRangeFilter: TimeRangeFilter = {
      from: fromString,
      to: toString,
    };

    setTimeRangeFilter(timeRangeFilter);
  };

  const onTimeBucketClick = useCallback(
    ({ start, end }: { start: number; end: number }) => {
      const rounded = getRoundedTimeFrame({ start, end });
      setTimeRangeFilter?.({
        from: new Date(rounded.start).toISOString(),
        to: new Date(rounded.end).toISOString(),
      });
    },
    [setTimeRangeFilter],
  );

  const projectIds = useMemo(() => (projectId ? [projectId] : []), [projectId]);

  return (
    <MonitorCard
      cardConfig={cardConfig}
      setCardState={setCardState}
      chartTimeFrame={chartTimeFrame}
      from="project_logs"
      projectIds={projectIds}
      experimentIds={EMPTY_EXPERIMENT_IDS}
      onBrush={handleBrush}
      filters={filters?.btql}
      allTimeQuery={timeRangeFilter === ALL_TIME_RANGE}
      onTimeBucketClick={onTimeBucketClick}
      standaloneLayoutMode
      clickMessage={""}
      overrides={{
        hasBottomLegend: false,
        hasYAxis: false,
      }}
    />
  );
}
