import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useDurableObjectByIds } from "#/utils/mutable-object";
import { useOrg } from "#/utils/user";
import { type Dispatch, type SetStateAction, useContext, useMemo } from "react";
import { type SavingState } from "#/ui/saving";
import { type FunctionObjectType } from "@braintrust/typespecs";
import { type Expr } from "@braintrust/btql/parser";

export function useFunctions({
  functionObjectType,
  setSavingState,
  filters,
  version,
  requireFilters,
  projectId: projectIdOverride,
}: {
  functionObjectType: FunctionObjectType;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
  filters?: Expr[];
  version?: string;
  requireFilters?: boolean;
  projectId?: string;
}) {
  const org = useOrg();
  const { projectId: projectIdFromContext } = useContext(ProjectContext);
  const projectId = projectIdOverride ?? projectIdFromContext;

  const orgPromptObjectType =
    functionObjectType === "prompt" ? "org_prompts" : "org_functions";
  const projectPromptObjectType =
    functionObjectType === "prompt" ? "project_prompts" : "project_functions";

  const orgScorers = useDurableObjectByIds({
    ids: undefined,
    filters,
    version,
    objectType: orgPromptObjectType,
    objectId: requireFilters ? (filters ? org.id : undefined) : org.id,
    setSavingState,
  });

  const projectPromptId = useMemo(
    () =>
      (orgScorers.status === "error" || projectIdOverride) && projectId
        ? projectId
        : undefined,
    [orgScorers.status, projectId, projectIdOverride],
  );

  const projectScorers = useDurableObjectByIds({
    ids: undefined,
    filters,
    version,
    objectType: projectPromptObjectType,
    objectId: requireFilters
      ? filters
        ? projectPromptId
        : undefined
      : projectPromptId,
    setSavingState,
  });

  const promptObjects = useMemo(() => {
    if (projectPromptId) {
      return projectScorers;
    } else {
      return orgScorers;
    }
  }, [projectPromptId, orgScorers, projectScorers]);

  return promptObjects;
}
