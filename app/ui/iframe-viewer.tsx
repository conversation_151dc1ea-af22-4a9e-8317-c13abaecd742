import { use<PERSON>allback, useEffect, useMemo, useRef } from "react";
import { z } from "zod";
import Mustache from "mustache";
import { cn } from "#/utils/classnames";
import { toast } from "sonner";
import { zodErrorToString } from "#/utils/validation";
import * as Sentry from "@sentry/nextjs";
import { deserializePlainStringAsJSON } from "#/utils/object";
import { RefreshCw } from "lucide-react";
import { Button } from "./button";
import { type SpanIFrame } from "@braintrust/typespecs";

export const dataMessageSchema = z.object({
  type: z.literal("data"),
  data: z.string(),
});

export const iframeUpdateMessageSchema = z.object({
  type: z.literal("update"),
  field: z.string(),
  data: z.any(),
});

export const settingsMessageSchema = z.object({
  type: z.literal("settings"),
  settings: z.object({
    theme: z.enum(["light", "dark"]),
    readOnly: z.boolean(),
  }),
});

export const messageSchema = z.union([
  dataMessageSchema,
  settingsMessageSchema,
]);

export type Message = z.infer<typeof messageSchema>;

export function IFrameViewer({
  urlTemplate,
  value,
  postMessage,
  className,
  onUpdate,
  iframeRef,
}: {
  urlTemplate: string;
  value: unknown;
  postMessage: boolean;
  className?: string;
  onUpdate: (field: string, data: unknown) => void;
  iframeRef?: React.RefObject<HTMLIFrameElement | null>;
}) {
  const internalIframeRef = useRef<HTMLIFrameElement>(null);
  const ref = iframeRef ?? internalIframeRef;
  const url = useMemo(
    () => Mustache.render(urlTemplate, value),
    [value, urlTemplate],
  );

  const sendSpanData = useCallback(() => {
    ref.current?.contentWindow?.postMessage(
      {
        type: "data",
        data: value,
      },
      url,
    );
  }, [value, url, ref]);

  const handleMessageFromIframe = useCallback(
    (event: MessageEvent) => {
      if (event.source !== ref.current?.contentWindow) {
        return;
      }
      try {
        const iframeData =
          typeof event.data === "string"
            ? deserializePlainStringAsJSON(event.data).value
            : event.data;

        if (iframeData?.__v0_remote__) {
          // ignore v0 parent iframe messages
          return;
        }

        if (iframeData?.type === "request-data") {
          sendSpanData();
          return;
        }

        // Handle update message
        const message = iframeUpdateMessageSchema.safeParse(iframeData);
        if (!message.success) {
          const formattedError = zodErrorToString(message.error, 2, true);
          toast.error("Received invalid message from iframe", {
            description: formattedError,
          });
          Sentry.captureMessage("Received invalid message from iframe", {
            extra: {
              message: formattedError,
            },
          });
          return;
        }
        onUpdate(message.data.field, message.data.data);
      } catch (error) {
        toast.error("Received invalid message from iframe", {
          description: `${error}`,
        });
        Sentry.captureException("Failed to parse message from iframe", {
          extra: {
            error,
          },
        });
      }
    },
    [onUpdate, ref, sendSpanData],
  );

  useEffect(() => {
    const iframe = ref.current;
    if (iframe && iframe.contentWindow && postMessage) {
      iframe.addEventListener("load", sendSpanData);
      window.addEventListener("message", handleMessageFromIframe);

      return () => {
        iframe.removeEventListener("load", sendSpanData);
        window.removeEventListener("message", handleMessageFromIframe);
      };
    }
  }, [postMessage, handleMessageFromIframe, ref, sendSpanData]);

  return (
    <iframe
      ref={ref}
      className={cn(
        "h-80 w-full overflow-hidden rounded-md border border-primary-100 bg-primary-50",
        className,
      )}
      allowFullScreen
      // @ts-ignore -- allowtransparency is not a valid attribute
      allowtransparency="true"
      src={url}
    />
  );
}

export const RefreshIframeButton = ({
  spanIframe,
  expandedFrameRef,
  parsedSpanData,
  variant = "default",
}: {
  spanIframe: SpanIFrame;
  expandedFrameRef: React.RefObject<HTMLIFrameElement | null>;
  parsedSpanData?: Record<string, unknown>;
  variant?: "default" | "border";
}) => {
  if (!spanIframe.post_message) {
    return null;
  }
  return (
    <Button
      size="xs"
      variant={variant}
      className="text-primary-500"
      onClick={() => {
        expandedFrameRef.current?.contentWindow?.postMessage(
          {
            type: "data",
            data: parsedSpanData,
          },
          spanIframe.url,
        );
        toast.success("Span iframe refreshed");
      }}
      Icon={RefreshCw}
    />
  );
};
