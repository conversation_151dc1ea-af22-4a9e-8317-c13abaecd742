import { clsx } from "clsx";
import { forwardRef } from "react";
import { cn } from "#/utils/classnames";

const plainInputCls = `border rounded-md border-primary-200 text-sm py-2 px-3
focus:border-primary-300 bg-primary-50
focus:outline-hidden focus:ring-2 focus:ring-primary-400
placeholder:text-primary-500`;

export const PlainInput = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement>
>(function PlainInput(props, ref) {
  const { className, ...otherProps } = props;
  return (
    <input
      ref={ref}
      className={cn(plainInputCls, className)}
      {...otherProps}
      data-1p-ignore
    />
  );
});

export const PlainText = forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>(function PlainText(props, ref) {
  const { className, ...otherProps } = props;
  return (
    <textarea
      ref={ref}
      className={clsx(plainInputCls, className)}
      {...otherProps}
    />
  );
});
