import { LoaderCircle } from "lucide-react";
import React from "react";
import { cn } from "#/utils/classnames";

export const Spinner = ({
  className,
  "aria-label": ariaLabel = "Loading",
}: {
  className?: string;
  "aria-label"?: string;
}) => (
  <LoaderCircle
    className={cn(
      "size-4 animate-smooth-spin will-change-transform",
      "transition-opacity",
      className,
    )}
    aria-label={ariaLabel}
    role="status"
    aria-live="polite"
  />
);
