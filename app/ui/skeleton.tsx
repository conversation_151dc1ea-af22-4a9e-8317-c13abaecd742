import { cn } from "#/utils/classnames";

function Skeleton({
  className,
  isLoaded,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { isLoaded?: boolean }) {
  return (
    <div
      className={cn(
        { "animate-pulse rounded-md bg-primary-100": !isLoaded },
        className,
      )}
      {...props}
    >
      <div className={cn({ "opacity-0": !isLoaded })}>{children}</div>
    </div>
  );
}

export { Skeleton };
