"use client";
import { useEffect, useState } from "react";

const getSize = (size: number, containerWidth?: number) => {
  if (typeof window === "undefined") {
    return 0;
  }
  const width = containerWidth ?? window.innerWidth;
  // resize panel truncates to 9 decimal points for some reason which can cause some weirdness,
  // so let's just use 5 decimal points
  return Math.round((size / width) * 100 * 100000) / 100000;
};

// Resizable only accepts 1-100 as the min/max for panels
// (percent of viewport width). This converts a desired pixel value to
// percent.
export const usePanelSize = (sizeInPx: number, observer?: Element) => {
  const [size, setSize] = useState(getSize(sizeInPx));

  useEffect(() => {
    const handleResize = (entries: ResizeObserverEntry[]) => {
      if (entries.length === 0) return;
      setSize(getSize(sizeInPx, entries[0].contentRect.width));
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(observer ?? document.body);
    return () => {
      resizeObserver.disconnect();
    };
  }, [sizeInPx, observer]);

  return size;
};
