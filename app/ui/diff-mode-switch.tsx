import { useEffect, useMemo, type Dispatch, type SetStateAction } from "react";
import { cn } from "#/utils/classnames";
import { Combobox } from "./combobox/combobox";
import { type DiffMode, type DiffModeState } from "./query-parameters";
import { buttonVariants } from "./button";
import { Switch } from "./switch";
import { type Schema } from "apache-arrow";

export function DiffModeSwitch({
  diffModeState,
  diffModeOptions,
  setDiffMode,
}: {
  diffModeState: DiffModeState | null;
  diffModeOptions: DiffMode[];
  setDiffMode: Dispatch<SetStateAction<DiffModeState>>;
}) {
  if (diffModeOptions.length === 0) {
    return null;
  }

  return (
    <div className="flex">
      {diffModeState?.enabled && (
        <Combobox<{ label: string; value: DiffMode }>
          align="start"
          disabled={diffModeOptions.length <= 1}
          noSearch
          buttonClassName="rounded-r-none disabled:bg-primary-200/50"
          buttonSize="xs"
          contentWidth={200}
          renderOptionTooltip={({ value }) => (
            <span className="text-xs leading-snug text-primary-600">
              {value === "between_experiments"
                ? "Compare two experiments"
                : "Compare output field with expected field within a single experiment"}
            </span>
          )}
          options={[
            {
              label: "Select diff mode",
              options: [
                {
                  label: "Experiments",
                  value: "between_experiments",
                },
                {
                  label: "Output vs. expected",
                  value: "expected_output",
                },
              ],
            },
          ]}
          selectedValue={diffModeState?.enabledValue}
          onChange={(_, { value }) =>
            setDiffMode({ enabled: true, enabledValue: value })
          }
          variant="button"
          buttonVariant="default"
          placeholderLabel="Select diff mode"
          renderComboboxDisplayLabel={({ label }) => label}
        />
      )}
      <a
        onClick={() => {
          setDiffMode((prev) => {
            const diffModeAvailable = diffModeOptions.find(
              (d) => d === prev?.enabledValue,
            );
            return {
              enabled: !prev?.enabled,
              enabledValue:
                diffModeAvailable ??
                diffModeOptions[0] ??
                "between_experiments",
            };
          });
        }}
        className={cn(
          buttonVariants({
            size: "xs",
            variant: diffModeState?.enabled ? "default" : "ghost",
          }),
          "flex cursor-pointer items-center gap-1 pr-2",
          {
            "rounded-l-none border-l border-primary-200":
              diffModeState?.enabled,
          },
        )}
      >
        Diff <Switch className="scale-90" checked={diffModeState?.enabled} />
      </a>
    </div>
  );
}

export function useDiffModeOptions({
  diffModeState,
  diffModeOptionParams,
  setDiffMode,
}: {
  diffModeState: DiffModeState | null;
  diffModeOptionParams: {
    loading: boolean | undefined;
    comparisonExperimentsCount: number;
    experimentSchema: Schema | null;
  };
  setDiffMode: (diffMode: DiffModeState) => void;
}) {
  const { loading, comparisonExperimentsCount, experimentSchema } =
    diffModeOptionParams;

  const diffModeOptions: DiffMode[] = useMemo(() => {
    const options: DiffMode[] = [];

    if (comparisonExperimentsCount > 0) {
      options.push("between_experiments");
    }

    let hasOutput = false;
    let hasExpected = false;
    for (const field of experimentSchema?.fields ?? []) {
      hasOutput = hasOutput || field.name === "output";
      hasExpected = hasExpected || field.name === "expected";
    }
    if (hasOutput && hasExpected) {
      options.push("expected_output");
    }

    return options;
  }, [comparisonExperimentsCount, experimentSchema]);

  useEffect(() => {
    if (loading || !diffModeState || !diffModeState.enabled) {
      return;
    }

    if (!diffModeOptions.some((d) => d === diffModeState?.enabledValue)) {
      // for whatever reason if the diff mode is enabled but not valid, then disable it on a valid diff mode
      setDiffMode({
        enabled: false,
        enabledValue: diffModeOptions[0] ?? "between_experiments",
      });
    }
  }, [loading, diffModeState, diffModeOptions, setDiffMode]);

  return {
    diffModeOptions,
  };
}
