import { Command, CommandGroup, CommandItem, CommandList } from "#/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";
import { cn } from "#/utils/classnames";

export default function PlainDropdown<T extends string>({
  selectedOption,
  setSelectedOption,
  options,
  defaultOption,
  itemClassName,
  className,
  disabled,
}: {
  selectedOption?: T;
  setSelectedOption: (model: T) => void;
  options: T[];
  defaultOption?: T;
  itemClassName?: string;
  className?: string;
  disabled?: boolean;
}) {
  const [selectorOpen, setSelectorOpen] = useState(false);
  useEffect(() => {
    if (selectedOption === undefined) {
      setSelectedOption(defaultOption || options[0]);
    }
  }, [defaultOption, options, selectedOption, setSelectedOption]);

  return (
    <Popover open={selectorOpen} onOpenChange={setSelectorOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            "inline-flex items-center rounded-md text-sm",
            "focus:outline-hidden focus-visible:ring-2",
            "cursor-pointer",
            {
              "pointer-events-none": disabled,
            },
            className,
          )}
        >
          <span className="pr-1">{selectedOption}</span>
          {!disabled && (
            <span className={cn("text-primary-400")}>
              <ChevronDown className="size-3" />
            </span>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="min-w-min p-0">
        <Command>
          <CommandList>
            <CommandGroup className="max-h-[400px] overflow-y-auto">
              {options.map((o) => (
                <CommandItem
                  key={o}
                  className={cn("gap-x-1.5", itemClassName)}
                  onSelect={() => {
                    setSelectedOption(o);
                    setSelectorOpen(false);
                  }}
                >
                  {o}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
