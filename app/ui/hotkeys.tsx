"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "#/ui/dialog";
import React, { useTransition } from "react";
import { useState } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { type Hotkey } from "react-hotkeys-hook/dist/types";

// This is re-exported because react-hotkeys-hook does not have a "use client" annotation
export { HotkeysProvider } from "react-hotkeys-hook";

export enum HotkeyScope {
  GlobalKeyboardShortcuts = "global-keyboard-shortcuts",
  PromptModal = "prompt-modal",
  ConfirmationModal = "confirmation-modal",
  ExpandedFrame = "expanded-frame",
  ExpandedTrace = "expanded-trace",
  HumanReview = "human-review",
}

export function HotKeysHelper({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const [_, startTransition] = useTransition();
  const { hotkeys, enableScope, disableScope } = useHotkeysContext();
  useHotkeys(
    "shift+?",
    () => {
      setOpen(true);
      enableScope("global-keyboard-shortcuts");
    },
    [setOpen],
    { scopes: ["global"], description: "Show keyboard shortcuts" },
  );

  return (
    <>
      <Dialog
        open={open}
        onOpenChange={(o) => {
          if (!o) {
            // use transition so that the escape key doesn't propagate to closing the trace panel as well
            startTransition(() => disableScope("global-keyboard-shortcuts"));
          }
          setOpen(o);
        }}
      >
        <DialogContent
          className="min-w-[600px] sm:max-w-xl"
          aria-describedby={undefined}
        >
          <DialogHeader>
            <DialogTitle>Keyboard shortcuts</DialogTitle>
          </DialogHeader>
          <div>
            {hotkeys.map((hotkey, idx) => (
              <HotkeyDescription key={idx} hotkey={hotkey} />
            ))}
          </div>
        </DialogContent>
      </Dialog>
      {children}
    </>
  );
}

export type KeyboardModifiers = {
  alt?: boolean;
  ctrl?: boolean;
  meta?: boolean;
  shift?: boolean;
  mod?: boolean;
};
function HotkeyDescription({ hotkey }: { hotkey: Hotkey }) {
  return (
    <div className="mb-1.5 flex gap-3">
      <div className="flex w-1/3 justify-end">
        <div className="inline-flex h-5 items-center rounded-xs border px-2 text-xs text-primary-600">
          {hotkey.alt && "Alt + "}
          {hotkey.ctrl && "Ctrl + "}
          {hotkey.meta && "Meta + "}
          {hotkey.shift && "Shift + "}
          {hotkey.mod && "Mod + "}
          {hotkey.keys?.join(" + ")}
        </div>
      </div>
      <div className="grow text-sm">{hotkey.description}</div>
    </div>
  );
}
