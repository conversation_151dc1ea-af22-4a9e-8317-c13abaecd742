import {
  scoreNameForSummary,
  type ScoreSummaryExperiment,
  SUMMARY_COLLATOR,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type ColumnOrderState } from "@tanstack/react-table";
import { type ScoreSummary } from "@braintrust/local/query";
import { tupleSort } from "#/ui/table/grouping/sorting";

export const resolveColumnOrder = (
  computedColumnOrder: Array<{
    id: string;
    pinnedColumnIndex?: number;
  }>,
  savedColumnOrder: ColumnOrderState,
): ColumnOrderState => {
  const result = [...savedColumnOrder];
  const savedColumnSet = new Set(savedColumnOrder);

  const pinnedColumns: Record<string, number> = {};
  computedColumnOrder.forEach(({ id, pinnedColumnIndex }, i) => {
    if (pinnedColumnIndex != null) {
      pinnedColumns[id] = pinnedColumnIndex;
    }

    if (savedColumnSet.has(id)) {
      return;
    }

    result.splice(i, 0, id);
  });

  const computedColumnSet = new Set(computedColumnOrder.map(({ id }) => id));
  const filteredResult = result.filter(
    (col) => computedColumnSet.has(col) && pinnedColumns[col] == null,
  );
  Object.entries(pinnedColumns)
    .toSorted((a, b) => a[1] - b[1])
    .forEach(([id, index]) => filteredResult.splice(index, 0, id));

  return filteredResult;
};

const UNSORTABLE_COLUMNS = new Set([
  "span_type_info",
  "input",
  "output",
  "expected",
]);
export const resolveOrderByRegressionColumnOrder = (
  computedColumnOrder: Array<{
    id: string;
    pinnedColumnIndex?: number;
    internalType?: string;
  }>,
  baseSummary:
    | {
        scores: ScoreSummary;
        experiment: ScoreSummaryExperiment;
      }
    | undefined,
): ColumnOrderState => {
  const { fixedColumns, sortableColumns, unsortableColumns } =
    computedColumnOrder.reduce(
      (
        acc: {
          fixedColumns: string[];
          sortableColumns: string[];
          unsortableColumns: string[];
        },
        col,
      ) => {
        const id = col.id;
        if (
          col.pinnedColumnIndex != null ||
          !!col?.internalType ||
          UNSORTABLE_COLUMNS.has(id)
        ) {
          acc.fixedColumns.push(id);
        } else if (id.startsWith("scores.") || id.startsWith("metrics.")) {
          acc.sortableColumns.push(id);
        } else {
          acc.unsortableColumns.push(id);
        }
        return acc;
      },
      {
        fixedColumns: [],
        sortableColumns: [],
        unsortableColumns: [],
      },
    );

  const sortedColumns = sortableColumns.sort((a, b) => {
    const nameA = scoreNameForSummary(a);
    const nameB = scoreNameForSummary(b);
    const summaryA = baseSummary?.scores?.[nameA];
    const summaryB = baseSummary?.scores?.[nameB];
    const diffSort = tupleSort(
      [
        summaryA?.regressions?.length ?? 0,
        summaryA?.diffAvg,
        summaryA?.improvements?.length ?? 0,
      ],
      [
        summaryB?.regressions?.length ?? 0,
        summaryB?.diffAvg,
        summaryB?.improvements?.length ?? 0,
      ],
      [true, false, true],
      (_a, _b) => _a - _b,
    );
    return diffSort !== 0 ? diffSort : SUMMARY_COLLATOR.compare(nameA, nameB);
  });

  return [...fixedColumns, ...sortedColumns, ...unsortableColumns];
};
