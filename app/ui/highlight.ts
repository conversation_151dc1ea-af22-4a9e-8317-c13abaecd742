import { toJsxRuntime } from "hast-util-to-jsx-runtime";
import { Fragment } from "react";

import { jsx, jsxs } from "react/jsx-runtime";

export async function highlight({
  code,
  lang,
}: {
  code: string;
  lang: BundledLanguage;
}) {
  const shiki = await import("shiki/bundle/web");
  const out = await shiki.codeToHast(code, {
    lang: bundledLanguages.includes(lang) ? lang : "bash",
    themes: {
      light: "github-light",
      dark: "github-dark",
    },
    structure: "inline",
  });

  return toJsxRuntime(out, {
    Fragment,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    jsx: jsx as any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    jsxs: jsxs as any,
  });
}

export const bundledLanguages = [
  "angular-html",
  "angular-ts",
  "astro",
  "bash",
  "blade",
  "c",
  "c++",
  "coffee",
  "coffeescript",
  "cpp",
  "css",
  "glsl",
  "gql",
  "graphql",
  "haml",
  "handlebars",
  "hbs",
  "html",
  "html-derivative",
  "http",
  "imba",
  "jade",
  "java",
  "javascript",
  "jinja",
  "jison",
  "jl",
  "js",
  "json",
  "json5",
  "jsonc",
  "jsonl",
  "jsx",
  "julia",
  "less",
  "lit",
  "markdown",
  "marko",
  "md",
  "mdc",
  "mdx",
  "php",
  "postcss",
  "pug",
  "py",
  "python",
  "r",
  "regex",
  "regexp",
  "sass",
  "scss",
  "sh",
  "shell",
  "shellscript",
  "sql",
  "styl",
  "stylus",
  "svelte",
  "ts",
  "ts-tags",
  "tsx",
  "typescript",
  "vue",
  "vue-html",
  "wasm",
  "wgsl",
  "xml",
  "yaml",
  "yml",
  "zsh",
] as const;

export type BundledLanguage = (typeof bundledLanguages)[number];
