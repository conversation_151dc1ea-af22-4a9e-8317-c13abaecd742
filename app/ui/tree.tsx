import { isEmpty } from "#/utils/object";
import { forwardRef, memo, useImperativeHandle, useMemo, useRef } from "react";
import {
  ObjectInspector,
  ObjectLabel,
  ObjectName,
  ObjectPreview,
  ObjectRootLabel,
} from "./react-inspector";
import { UnthemedDOMInspector } from "./react-inspector/dom-inspector/DOMInspector";
import { useDarkMode } from "#/utils/useDarkMode";
import { TextNode, parse as nodeXMLParse } from "node-html-parser";
import { parseImage } from "./image";
import { type ApplySearch } from "./use-filter-sort-search";
import { type SetValue } from "#/lib/clientDataStorage";
import { Button } from "#/ui/button";
import { ListFilter } from "lucide-react";
import { toast } from "sonner";
import { hasChildNodes } from "#/ui/react-inspector/tree-view/pathUtils";

export interface TreeViewerProps {
  value: unknown;
  onApplySearch?: ApplySearch;
  foldState?: Record<string, boolean>;
  setFoldState?: SetValue<Record<string, boolean>>;
  isCollapsed?: boolean;
}
export interface TreeViewerHandle {
  getAllFoldPaths: () => string[];
}

const DEFAULT_EXPAND_LEVEL = 10;

export const TreeViewer = forwardRef<TreeViewerHandle, TreeViewerProps>(
  ({ value, onApplySearch, foldState, setFoldState, isCollapsed }, ref) => {
    const isDark = useDarkMode();

    const treeRef = useRef<TreeViewerHandle>(null);
    useImperativeHandle(
      ref,
      () => ({
        getAllFoldPaths: () => {
          return treeRef.current?.getAllFoldPaths() ?? [];
        },
      }),
      [],
    );

    return (
      <ObjectInspector
        data={value}
        nodeRenderer={NodeRendererWithFilter}
        expandLevel={DEFAULT_EXPAND_LEVEL}
        theme={isDark ? "chromeDark" : "chromeLight"}
        onApplySearch={onApplySearch}
        foldState={foldState}
        setFoldState={setFoldState}
        isCollapsed={isCollapsed}
        treeRef={treeRef}
      />
    );
  },
);
TreeViewer.displayName = "TreeViewer";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const NodeRenderer = memo((props: any) => {
  const {
    depth,
    name,
    data,
    isNonenumerable,
    path,
    foldPath,
    foldState,
    setFoldState,
    expanded,
  } = props;
  const imageData = useMemo(() => {
    try {
      return parseImage({ data });
    } catch {}
  }, [data]);

  const xmlData = useMemo(() => {
    if (typeof data !== "string") {
      return;
    }
    try {
      return parseXml(data);
    } catch {}
  }, [data]);

  if (!isEmpty(imageData)) {
    return (
      <span>
        {depth === 0 ? (
          imageData
        ) : (
          <>
            {typeof name === "string" ? (
              <ObjectName name={name} dimmed={isNonenumerable} />
            ) : (
              <ObjectPreview data={name} />
            )}
            <span>: </span>
            <>{imageData}</>
          </>
        )}
      </span>
    );
  }

  if (!isEmpty(xmlData)) {
    const props = {
      path,
      data: xmlData,
      depth,
      expandLevel: Math.max(DEFAULT_EXPAND_LEVEL - depth, 0),
      foldPath,
      foldState,
      setFoldState,
    };
    return (
      <span>
        {depth === 0 ? (
          <ol>
            <UnthemedDOMInspector {...props} />
          </ol>
        ) : (
          <>
            {typeof name === "string" ? (
              <ObjectName name={name} dimmed={isNonenumerable} />
            ) : (
              <ObjectPreview data={name} />
            )}
            <span>: </span>
            <ol className="pl-[12px]">
              <UnthemedDOMInspector {...props} />
            </ol>
          </>
        )}
      </span>
    );
  }

  return depth === 0 ? (
    <ObjectRootLabel name={name} data={data} />
  ) : (
    <ObjectLabel
      name={name}
      data={data}
      isNonenumerable={isNonenumerable}
      expanded={expanded}
    />
  );
});
NodeRenderer.displayName = "NodeRenderer";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const NodeRendererWithFilter = memo((props: any) => {
  const { data, dataIterator } = props;
  return (
    <>
      <NodeRenderer {...props} />
      {props.onApplySearch && !hasChildNodes(data, dataIterator) && (
        <Button
          transparent
          className="ml-1 inline-flex size-[14px] items-center text-primary-500 opacity-0 transition-all group-hover:opacity-100 hover:text-primary-900 dark:text-primary-600"
          size="inline"
          onClick={() => {
            const query = `${props.path} ${data === null ? "is" : "="} ${
              typeof data === "string" ? `"${data}"` : data
            }`;
            toast.promise(
              props.onApplySearch(query, undefined, {
                originType: "btql",
                label: query,
              }),
              {
                loading: "Applying filter",
                success: "Filter applied",
                error: "Failed to apply filter",
              },
            );
          }}
        >
          <ListFilter size={10} />
        </Button>
      )}
    </>
  );
});
NodeRendererWithFilter.displayName = "NodeRendererWithFilter";

export function parseXml(xmlString: string) {
  const parsed = nodeXMLParse(xmlString);
  if (
    parsed.childNodes.length === 1 &&
    parsed.childNodes[0] instanceof TextNode
  ) {
    throw new Error("Plain text");
  } else if (parsed.childNodes.length === 0) {
    throw new Error("Empty");
  } else if (parsed.childNodes.length === 1) {
    return parsed.childNodes[0];
  } else {
    return parsed;
  }
}

export function isXml(s: string) {
  try {
    parseXml(s);
    return true;
  } catch {
    return false;
  }
}
