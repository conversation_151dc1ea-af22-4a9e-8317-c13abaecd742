import type { FieldValue } from "./schema-node";
import { InfoBanner } from "./info-banner";
import { type ReactNode } from "react";

export const MAX_NESTING_LIMIT = 6;

export const detectUnsupportedPatterns = (value: FieldValue) => {
  const issues: ReactNode[] = [];

  const checkValue = (
    val: FieldValue,
    path: string = "root",
    level: number = 0,
  ) => {
    if (level > MAX_NESTING_LIMIT) {
      issues.push(<>Maximum nesting level of {MAX_NESTING_LIMIT} exceeded</>);
      return;
    }

    if (typeof val === "object" && val !== null && !Array.isArray(val)) {
      if (Object.keys(val).length === 0) return;

      if (
        !("type" in val) ||
        val.type === undefined ||
        val.type === null ||
        val.type === ""
      ) {
        const propertyName = path.split(".").pop() || "root";
        issues.push(
          <>
            Missing or empty type field on{" "}
            <span className="font-mono">{propertyName}</span>
          </>,
        );
      }

      if ("type" in val && Array.isArray(val.type)) {
        const propertyName = path.split(".").pop() || "root";
        issues.push(
          <>
            Multiple types (type: [{val.type.join(", ")}]) are not supported on{" "}
            <span className="font-mono">{propertyName}</span>
          </>,
        );
      }

      const unsupportedFeatures = [
        { key: "allOf", name: "Conditional schemas (allOf, anyOf, oneOf)" },
        { key: "anyOf", name: "Conditional schemas (allOf, anyOf, oneOf)" },
        { key: "oneOf", name: "Conditional schemas (allOf, anyOf, oneOf)" },
        { key: "not", name: "Negation schemas (not)" },
        { key: "if", name: "Conditional validation (if/then/else)" },
        { key: "then", name: "Conditional validation (if/then/else)" },
        { key: "else", name: "Conditional validation (if/then/else)" },
        { key: "dependencies", name: "Property dependencies" },
        { key: "patternProperties", name: "Pattern properties" },
        { key: "unevaluatedProperties", name: "Unevaluated properties/items" },
        { key: "unevaluatedItems", name: "Unevaluated properties/items" },
      ];

      unsupportedFeatures.forEach(({ key, name }) => {
        if (key in val) {
          issues.push(
            <>
              <span className="font-mono">{path}</span>: {name} are not
              supported
            </>,
          );
        }
      });

      if (
        "additionalItems" in val &&
        typeof val.additionalItems !== "boolean"
      ) {
        issues.push(
          <>
            <span className="font-mono">{path}</span>: Complex additionalItems
            schemas are not supported
          </>,
        );
      }

      if (
        "properties" in val &&
        typeof val.properties === "object" &&
        val.properties !== null
      ) {
        Object.entries(val.properties).forEach(([key, propVal]) => {
          checkValue(propVal, `${path}.properties.${key}`, level + 1);
        });
      }

      if ("items" in val && val.items) {
        checkValue(val.items, `${path}.items`, level + 1);
      }
    }
  };

  checkValue(value);
  return issues;
};

export const UnsupportedSchemaBlock = ({ issues }: { issues: ReactNode[] }) => (
  <InfoBanner className="mr-1">
    <span className="font-medium">
      This schema contains features that are unsupported by the schema builder.
    </span>{" "}
    Please switch to the JSON or YAML view to edit this schema.
    <ul className="mt-2 space-y-1">
      {issues.map((issue, index) => (
        <li key={index} className="ml-3 list-disc">
          {issue}
        </li>
      ))}
    </ul>
  </InfoBanner>
);
