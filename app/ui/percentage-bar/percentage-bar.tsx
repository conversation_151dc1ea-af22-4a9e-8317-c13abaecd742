"use client";

import { forwardRef } from "react";
import { Progress } from "#/ui/progress";
import { useDarkMode } from "#/utils/useDarkMode";
import { cn } from "#/utils/classnames";

const PercentageBar = forwardRef<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  any,
  React.ComponentPropsWithoutRef<typeof Progress> & {
    className?: string;
    value: number;
    comparisonValue?: number | null;
    showComparisonText?: boolean;
  }
>(function PercentageBar(
  {
    indicatorClassName,
    className,
    value,
    comparisonValue,
    showComparisonText,
    ...props
  },
  ref,
) {
  const isDarkMode = useDarkMode();
  return (
    <div className="relative">
      <Progress
        {...props}
        className={cn("h-1.5", className)}
        indicatorClassName={cn("bg-primary-500", indicatorClassName)}
        value={value}
        ref={ref}
      />
      {comparisonValue != null && (
        <div
          className="absolute w-0.5 rounded-full bg-comparison-600 pt-1"
          style={{
            boxShadow: isDarkMode
              ? "1px 0 4px rgb(0 0 0 / 40%)"
              : "1px 0 4px rgb(255 255 255 / 40%)",
            left: `${comparisonValue}%`,
            transform: "translateX(-1px)",
            height: 13,
            top: -3,
          }}
        />
      )}
      {comparisonValue != null && showComparisonText && (
        <div className="mt-1 text-sm text-primary-500">
          Compared to{" "}
          <span className="font-medium text-comparison-600">
            {comparisonValue.toFixed(2)}%
          </span>
        </div>
      )}
    </div>
  );
});

PercentageBar.displayName = "PercentageBar";
export { PercentageBar };
