"use client";

import { Copy } from "lucide-react";
import { CopyToClipboardButton } from "./copy-to-clipboard-button";

export default function Token({ token }: { token?: string }) {
  return (
    <div className="flex items-center justify-between rounded-md border-2 border-accent-200 bg-accent-50 p-3">
      <span className="font-mono font-semibold">{token || "Loading..."}</span>
      {token && (
        <CopyToClipboardButton textToCopy={token} variant="primary">
          <Copy className="size-4" />
        </CopyToClipboardButton>
      )}
    </div>
  );
}
