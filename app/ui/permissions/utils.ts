import { OBJECT_TYPE_LABELS } from "./items";
import { type PermissionsCheckboxGroups } from "./permissions-types";

export function getInheritedGroups(permissionsData: PermissionsCheckboxGroups) {
  return Array.from(
    new Set(
      Object.values(permissionsData).flatMap((checkboxGroup) =>
        Object.values(checkboxGroup).flatMap(
          (checkbox) => checkbox.inherited_groups,
        ),
      ),
    ),
  );
}

export function getInheritedParentObjects(
  permissionsData: PermissionsCheckboxGroups,
) {
  return Array.from(
    new Set(
      Object.values(permissionsData).flatMap((checkboxGroup) =>
        Object.values(checkboxGroup).flatMap(
          (checkbox) => checkbox.inherited_parent_objects,
        ),
      ),
    ),
  ).map((obj) => OBJECT_TYPE_LABELS[obj]);
}
