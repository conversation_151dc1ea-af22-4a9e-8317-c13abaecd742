import {
  type PermissionsRequirement,
  type UserObjectType,
} from "./permissions-types";
import {
  type AclBatchUpdateRequest,
  aclBatchUpdateResponseSchema,
} from "@braintrust/typespecs";
import {
  type LoadedBtSessionToken,
  sessionFetchProps,
} from "#/utils/auth/session-token";
import { HTTPError } from "#/utils/http_error";

export type UpdatePermissionsRequirementItem = {
  requirement: PermissionsRequirement;
  objectId: string;
  userObjectType: UserObjectType;
  userGroupId: string;
  action: "add" | "remove";
};

export type UpdatePermissionsRequirementsInput = {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  requirements: UpdatePermissionsRequirementItem[];
};

function permissionsRequirementItemToAclReq({
  requirement,
  objectId,
  userObjectType,
  userGroupId,
}: UpdatePermissionsRequirementItem) {
  return requirement.permissions.map((p) => ({
    object_type: requirement.objectType,
    object_id: objectId,
    ...(userObjectType === "user"
      ? { user_id: userGroupId }
      : { group_id: userGroupId }),
    permission: p.permission,
    restrict_object_type: p.restrictObjectType,
  }));
}

export async function updatePermissionsRequirements({
  apiUrl,
  sessionToken,
  requirements,
}: UpdatePermissionsRequirementsInput): Promise<void> {
  const url = `${apiUrl}/api/acl/batch_update`;
  const body: AclBatchUpdateRequest = {
    add_acls: requirements
      .filter((r) => r.action === "add")
      .flatMap(permissionsRequirementItemToAclReq),
    remove_acls: requirements
      .filter((r) => r.action === "remove")
      .flatMap(permissionsRequirementItemToAclReq),
  };
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const headers = {
    Accept: "application/json",
    "Content-Type": "application/json",
    ...sessionHeaders,
  };
  const resp = await fetch(url, {
    method: "POST",
    body: JSON.stringify(body),
    headers,
    ...sessionExtraFetchProps,
  });
  if (!resp.ok) {
    throw new HTTPError(resp.status, await resp.text());
  }
  const data = await (async () => {
    try {
      return aclBatchUpdateResponseSchema.nullable().parse(await resp.json());
    } catch (e) {
      console.error("Invalid response:\n", e);
      throw new HTTPError(500, "Internal error");
    }
  })();
  if (data === null) {
    console.error("Should not be invoking empty permissions request");
    return;
  }
}
