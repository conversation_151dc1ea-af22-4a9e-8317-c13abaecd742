import { type AclObjectType } from "@braintrust/typespecs";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import {
  type PermissionsRequirement,
  type UserObjectType,
} from "#/ui/permissions/permissions-types";
import { aclSpecs } from "@braintrust/local/app-schema";
import {
  fromTablesClause,
  resolvePossiblyVirtualTableInfo,
} from "#/pages/api/_object_crud_util";

export function permissionsCheckboxQuery({
  requirement,
  objectIdSubquery,
  userObjectType,
  userGroupIdParam,
}: {
  requirement: PermissionsRequirement;
  objectIdSubquery: string;
  userObjectType: UserObjectType;
  userGroupIdParam: string;
}) {
  const expandedAclsSubquery = expandedAclsForPermissionsRequirementsQuery({
    requirement,
    objectIdSubquery,
    userObjectType,
    userGroupIdParam,
  });
  return expandedAclsToPermissionsCheckboxQuery({
    expandedAclsSubquery,
    requiredPermissions: requirement.permissions,
    userObjectType,
    userGroupIdParam,
    leafObjectType: requirement.objectType,
  });
}

function permissionMatchesExpandedAcl(
  p: PermissionsRequirement["permissions"][0],
  expandedAclsTable: string,
  opts?: { onlyCheckRestrictObjectTypeNull?: boolean },
) {
  return `
    (${expandedAclsTable}.permission = ${singleQuote(p.permission)}
     and (
         ${expandedAclsTable}.restrict_object_type is null
         ${
           p.restrictObjectType && !opts?.onlyCheckRestrictObjectTypeNull
             ? `or ${expandedAclsTable}.restrict_object_type = ${singleQuote(
                 p.restrictObjectType,
               )}`
             : ""
         }
    ))
  `;
}

function expandedAclsToPermissionsCheckboxQuery({
  expandedAclsSubquery,
  requiredPermissions,
  userObjectType,
  userGroupIdParam,
  leafObjectType,
}: {
  expandedAclsSubquery: string;
  requiredPermissions: PermissionsRequirement["permissions"];
  userObjectType: UserObjectType;
  userGroupIdParam: string;
  leafObjectType: AclObjectType;
}): string {
  return `
    with
    selected_expanded_acls as (
        ${expandedAclsSubquery}
    ),
    permissions_checks as (
    select
        (${requiredPermissions
          .map(
            (p) => `
            (exists(
                select 1 from selected_expanded_acls
                where ${permissionMatchesExpandedAcl(
                  p,
                  "selected_expanded_acls",
                )}))
          `,
          )
          .join(" and ")}) has_full_permissions,
        (${requiredPermissions
          .map(
            (p) => `
            (exists(
                select 1 from selected_expanded_acls
                where ${permissionMatchesExpandedAcl(
                  p,
                  "selected_expanded_acls",
                )}))
          `,
          )
          .join(" or ")}) has_permissions_overlap,
        (${requiredPermissions
          .map((p) =>
            p.restrictObjectType
              ? `(exists(
                  select 1 from selected_expanded_acls
                  where ${permissionMatchesExpandedAcl(
                    p,
                    "selected_expanded_acls",
                    { onlyCheckRestrictObjectTypeNull: true },
                  )}))`
              : "false",
          )
          .join(" or ")}) unrestricted_grant_for_restricted_permission
    ),
    aggregated_items as (
    select
        coalesce(array_agg(distinct roles.name) filter (where roles.name is not null),
                 '{}'::text[]) roles,
        coalesce(array_agg(distinct groups.name) filter (where groups.name is not null${
          userObjectType === "group"
            ? ` and groups.id <> ${userGroupIdParam}`
            : ""
        }),
                 '{}'::text[]) inherited_groups,
        coalesce(array_agg(distinct acls.object_type) filter (where acls.object_type <> ${singleQuote(
          leafObjectType,
        )}),
                 '{}'::acl_object_type[]) inherited_parent_objects
    from
        (${expandedAclsSubquery}) "_selected_expanded_acls"
        join acls on "_selected_expanded_acls".acl_id = acls.id
        left join roles on acls.role_id = roles.id
        left join groups on acls.group_id = groups.id
    ),
    final_elements as (
    select
        has_full_permissions is_checked,
        (
            -- Either has all the permissions or none.
            (has_full_permissions or (not has_permissions_overlap))
            and (cardinality(roles) = 0)
            and (cardinality(inherited_groups) = 0)
            and (cardinality(inherited_parent_objects) = 0)
            and (not unrestricted_grant_for_restricted_permission)
        ) is_mutable,
        ((not has_full_permissions) and has_permissions_overlap) has_partial_permissions,
        roles,
        inherited_groups,
        inherited_parent_objects,
        unrestricted_grant_for_restricted_permission
    from
        permissions_checks join aggregated_items on true
    )
    select to_jsonb(final_elements) checkbox
    from final_elements
    `;
}

function expandedAclsForPermissionsRequirementsQuery({
  requirement,
  objectIdSubquery,
  userObjectType,
  userGroupIdParam,
}: {
  requirement: PermissionsRequirement;
  objectIdSubquery: string;
  userObjectType: UserObjectType;
  userGroupIdParam: string;
}): string {
  const { parentAclObjectTypes } = aclSpecs[requirement.objectType];
  const baseTableInfo = resolvePossiblyVirtualTableInfo(requirement.objectType);
  const allObjectTypes = [requirement.objectType, ...parentAclObjectTypes];
  return `
    select _expanded_acls.*
    from
        ${fromTablesClause(allObjectTypes)}
        join _expanded_acls on true
    where
        ${doubleQuote(baseTableInfo.tableName)}.id = (${objectIdSubquery})
        and (${allObjectTypes
          .map(
            (objectType) =>
              `
                _expanded_acls.object_type = ${singleQuote(objectType)}
                and _expanded_acls.object_id = ${doubleQuote(
                  resolvePossiblyVirtualTableInfo(objectType).tableName,
                )}.id
                and _expanded_acls.user_object_type = ${singleQuote(
                  userObjectType,
                )}
                and _expanded_acls.user_group_id = ${userGroupIdParam}
                and (${requirement.permissions
                  .map((p) => permissionMatchesExpandedAcl(p, "_expanded_acls"))
                  .join(" or ")})
            `,
          )
          .join(" or ")})
  `;
}

export function userGroupWithObjectPermissionsQuery({
  objectType,
  objectIdSubquery,
  skipRestrictObjectTypeCheck,
}: {
  objectType: AclObjectType;
  objectIdSubquery: string;
  skipRestrictObjectTypeCheck?: boolean;
}): string {
  const expandedAclsSubquery = expandedAclsForUserGroupItemsQuery({
    objectType,
    objectIdSubquery,
    skipRestrictObjectTypeCheck,
  });
  return expandedAclsToUserGroupItemsQuery({
    expandedAclsSubquery,
    leafObjectType: objectType,
  });
}

function expandedAclsToUserGroupItemsQuery({
  expandedAclsSubquery,
  leafObjectType,
}: {
  expandedAclsSubquery: string;
  leafObjectType: AclObjectType;
}): string {
  return `
    with
    _selected_expanded_acls as (${expandedAclsSubquery}),
    raw_elements0 as (
    select
        "_selected_expanded_acls".user_object_type,
        "_selected_expanded_acls".user_group_id,
        coalesce(bool_or(true) filter (where
            acls.object_type = ${singleQuote(leafObjectType)}
            and acls.user_object_type = "_selected_expanded_acls".user_object_type
            and coalesce(acls.user_id, acls.group_id) = "_selected_expanded_acls".user_group_id
            and acls.grant_object_type = 'permission'
            and acls.permission = "_selected_expanded_acls".permission
            and acls.restrict_object_type is not null
        ), false) has_direct_permission,
        coalesce(array_agg(distinct roles.name) filter (where roles.name is not null),
                 '{}'::text[]) roles,
        coalesce(
            array_agg(distinct groups.name) filter (
                where groups.name is not null
                and ("_selected_expanded_acls".user_object_type = 'user' or
                     "_selected_expanded_acls".user_group_id <> groups.id)
            ),
            '{}'::text[]) inherited_groups,
        coalesce(array_agg(distinct acls.object_type) filter (where acls.object_type <> ${singleQuote(
          leafObjectType,
        )}),
                 '{}'::acl_object_type[]) inherited_parent_objects,
        coalesce(bool_or(true) filter (where "_selected_expanded_acls".restrict_object_type is null), false)
            has_unrestricted_permissions
    from
        "_selected_expanded_acls"
        join acls on "_selected_expanded_acls".acl_id = acls.id
        left join roles on acls.role_id = roles.id
        left join groups on acls.group_id = groups.id
    group by
        "_selected_expanded_acls".user_object_type,
        "_selected_expanded_acls".user_group_id
    ),
    raw_elements1 as (
    select
        user_object_type,
        user_group_id,
        has_direct_permission,
        roles,
        inherited_groups,
        inherited_parent_objects,
        has_unrestricted_permissions
    from raw_elements0
    )
    select to_jsonb(raw_elements1) item
    from raw_elements1
    `;
}

function expandedAclsForUserGroupItemsQuery({
  objectType,
  objectIdSubquery,
  skipRestrictObjectTypeCheck,
}: {
  objectType: AclObjectType;
  objectIdSubquery: string;
  skipRestrictObjectTypeCheck?: boolean;
}): string {
  const { parentAclObjectTypes } = aclSpecs[objectType];
  const baseTableInfo = resolvePossiblyVirtualTableInfo(objectType);
  const allObjectTypes = [objectType, ...parentAclObjectTypes];
  return `
    select _expanded_acls.*
    from
        ${fromTablesClause(allObjectTypes)}
        join _expanded_acls on true
    where
        ${doubleQuote(baseTableInfo.tableName)}.id = (${objectIdSubquery})
        and (${allObjectTypes
          .map(
            (checkObjectType) =>
              `
                _expanded_acls.object_type = ${singleQuote(checkObjectType)}
                and _expanded_acls.object_id = ${doubleQuote(
                  resolvePossiblyVirtualTableInfo(checkObjectType).tableName,
                )}.id
                ${
                  skipRestrictObjectTypeCheck
                    ? ""
                    : `and (
                        _expanded_acls.restrict_object_type is null or
                        _expanded_acls.restrict_object_type = ${singleQuote(
                          objectType,
                        )}
                    )`
                }
            `,
          )
          .join(" or ")})
  `;
}
