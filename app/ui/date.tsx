import { TooltipTrigger, Tooltip, TooltipContent } from "./tooltip";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import humanizeDuration, { type HumanizerOptions } from "humanize-duration";
import { cn } from "#/utils/classnames";

export function DateWithTooltip({
  className,
  dateMs,
  relativeTimeOptions,
  disableSmartDate,
  withSeconds,
}: {
  className?: string;
  dateMs: number;
  relativeTimeOptions?: SmartTimeFormatOptions;
  disableSmartDate?: boolean;
  withSeconds?: boolean;
}) {
  const date = new Date(dateMs);
  return (
    <Tooltip>
      <TooltipTrigger className={cn(className)} asChild>
        <span>
          {!disableSmartDate ? (
            smartTimeFormat(dateMs, relativeTimeOptions)
          ) : (
            <time>
              {date.toLocaleString(undefined, {
                timeZoneName: "short",
                second: withSeconds ? "2-digit" : undefined,
              })}
            </time>
          )}
        </span>
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent>
          <DateTooltipContent date={date} withSeconds={withSeconds} />
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
}

export const DateTooltipContent = ({
  date,
  withSeconds,
}: {
  date: Date;
  withSeconds?: boolean;
}) => (
  <div className="divide-y">
    <div className="mb-1 text-xs text-primary-500">Time conversion</div>
    {[Intl.DateTimeFormat().resolvedOptions().timeZone, "UTC"].map(
      (tz, i, arr) => (
        <DateRow
          key={i}
          className={cn(i === arr.length - 1 ? "pt-2" : "py-2")}
          dateMs={date.getTime()}
          timeZone={tz}
          withSeconds={withSeconds}
        />
      ),
    )}
  </div>
);

function DateRow({
  className,
  dateMs,
  timeZone,
  withSeconds,
}: {
  className?: string;
  dateMs: number;
  timeZone: string;
  withSeconds?: boolean;
}) {
  const date = new Date(dateMs);

  return (
    <div className={cn("flex items-baseline text-sm tabular-nums", className)}>
      <div className="min-w-10 text-xs font-medium">
        {new Intl.DateTimeFormat(undefined, {
          timeZone,
          timeZoneName: "short",
        })
          .formatToParts(date)
          .find((p) => p.type === "timeZoneName")?.value ?? ""}
      </div>
      <div className={withSeconds ? "min-w-32" : "min-w-20"}>
        {new Intl.DateTimeFormat(undefined, {
          timeZone,
          hour: "numeric",
          minute: "2-digit",
          ...(withSeconds
            ? { second: "2-digit", fractionalSecondDigits: 2 }
            : {}),
        }).format(date)}
      </div>
      <div className="min-w-32">
        {new Intl.DateTimeFormat(undefined, {
          timeZone,
          weekday: "short",
          month: "short",
          day: "numeric",
          year: "numeric",
        }).format(date)}
      </div>
    </div>
  );
}

const shortEnglishHumanizer = humanizeDuration.humanizer({
  language: "shortEn",
  languages: {
    shortEn: {
      y: () => "y",
      mo: () => "mo",
      w: () => "w",
      d: () => "d",
      h: () => "h",
      m: () => "m",
      s: () => "s",
      ms: () => "ms",
    },
  },
  delimiter: " ",
  spacer: "",
});

function relativeTime(dateMs: number, options: HumanizerOptions = {}) {
  return relativeTimeMs(Date.now() - dateMs, options) + " ago";
}

export function relativeTimeMs(
  relativeMs: number,
  options: HumanizerOptions = {},
) {
  return shortEnglishHumanizer(relativeMs, {
    largest: 1,
    ...options,
  });
}

export type SmartTimeFormatOptions = HumanizerOptions & {
  includeTimeZone?: boolean;
  includeSeconds?: boolean;
};

export const conciseTimeFormat = (dateMs: number) => {
  const currentDate = new Date();
  const inputDate = new Date(dateMs);

  const isThisYear = currentDate.getFullYear() === inputDate.getFullYear();

  const timeFormat = inputDate.toLocaleTimeString(undefined, {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
  });

  if (!isThisYear) {
    return `${inputDate.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    })} ${timeFormat}`; // Format: Dec 8, 2023 8:42:01 AM
  }

  return `${inputDate.toLocaleDateString(undefined, {
    month: "short",
    day: "numeric",
  })} ${timeFormat}`; // Format: Apr 25 4:23:01 PM
};

export const smartTimeFormat = (
  dateMs: number,
  options?: SmartTimeFormatOptions,
) => {
  const currentDate = new Date();
  const inputDate = new Date(dateMs);

  const isThisYear = currentDate.getFullYear() === inputDate.getFullYear();

  const timeFormat = inputDate.toLocaleTimeString(undefined, {
    hour: "numeric",
    minute: "2-digit",
    ...(options?.includeSeconds ? { second: "2-digit" } : {}),
    ...(options?.includeTimeZone ? { timeZoneName: "short" } : {}),
  });

  if (!isThisYear) {
    return `${inputDate.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    })} ${timeFormat}`; // Format: Dec 8, 2023 8:42 AM
  }

  const isOlderThanYesterday =
    currentDate.getTime() - dateMs > 24 * 60 * 60 * 1000;
  if (isOlderThanYesterday) {
    return `${inputDate.toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
    })} ${timeFormat}`; // Format: Apr 25 4:23 PM
  }

  const isYesterday = currentDate.getDate() - inputDate.getDate() === 1;
  if (isYesterday) return `Yesterday ${timeFormat}`; // Format: Yesterday 1:22 PM

  const isLessThanMin = currentDate.getTime() - dateMs < 60 * 1000;
  if (isLessThanMin) return `just now`; // Format: just now

  return relativeTime(dateMs, options); // Format: 4m ago
};

export const smartDateFormat = (dateMs: number, options?: HumanizerOptions) => {
  const currentDate = new Date();
  const inputDate = new Date(dateMs);

  const isThisYear = currentDate.getFullYear() === inputDate.getFullYear();

  if (!isThisYear) {
    return inputDate.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    }); // Format: Dec 8, 2023
  }

  const isOlderThan1Week =
    currentDate.getTime() - dateMs > 7 * 24 * 60 * 60 * 1000;

  if (isOlderThan1Week) {
    return inputDate.toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
    }); // Format: Apr 25
  }

  const isLessThanMin = currentDate.getTime() - dateMs < 60 * 1000;
  if (isLessThanMin) return `just now`; // Format: just now

  return relativeTime(dateMs, options); // Format: 4h ago
};

export const isValidTimestamp = (timestamp: string) => {
  const timestampPattern =
    /^\d{4}-\d{2}-\d{2} \d{1,2}:\d{2}:\d{2}\.\d+\+\d{2}$/;
  return timestampPattern.test(timestamp);
};
