"use client";

import { useUser } from "#/utils/user";
import { useEffect } from "react";
import { IdentifyUser } from "./use-analytics";
import { signInPath } from "#/utils/auth/redirects";
import { useRouter } from "next/navigation";
import { useRedirectPath } from "#/utils/use-redirect-path";
import { Spinner } from "./icons/spinner";

export default function SessionRoot({
  children,
  loginRequired,
}: {
  children: React.ReactNode;
  loginRequired: boolean;
}) {
  return (
    <LoginRequired loginRequired={loginRequired}>
      <SessionizedRoot>{children}</SessionizedRoot>
    </LoginRequired>
  );
}

export function LoginRequired({
  children,
  loginRequired,
}: {
  children: React.ReactNode;
  loginRequired: boolean;
}) {
  const { status, session } = useUser();
  const router = useRouter();
  const redirectPath = useRedirectPath();

  useEffect(() => {
    if (!loginRequired || status !== "unauthenticated") return;

    // Redirect to sign in page if login is required and user is not authenticated
    router.push(signInPath({ redirectPath }));
  }, [loginRequired, session, router, status, redirectPath]);

  if (status === "authenticated" || (status !== "loading" && !loginRequired)) {
    return <>{children}</>;
  }

  // Never return null here, which will render a blank screen
  return <Loading />;
}

export function SessionizedRoot({ children }: { children: React.ReactNode }) {
  const { status } = useUser();

  if (status === "loading") {
    return <Loading />;
  }

  return <IdentifyUser>{children}</IdentifyUser>;
}

const Loading = () => {
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <Spinner />
    </div>
  );
};
