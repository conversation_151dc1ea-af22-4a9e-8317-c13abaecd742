import { useRef } from "react";
import { But<PERSON>, type ButtonProps } from "./button";

export const FileInputButton = ({
  onChange,
  accept,
  ...buttonProps
}: {
  onChange: (file?: File) => void;
  accept?: string;
} & Omit<ButtonProps, "onChange">) => {
  const input = useRef<HTMLInputElement>(null);
  return (
    <>
      <input
        type="file"
        ref={input}
        className="hidden"
        onChange={(e) => onChange(e.target.files?.[0])}
        accept={accept}
      />
      <Button {...buttonProps} onClick={() => input.current?.click()}>
        {buttonProps.children}
      </Button>
    </>
  );
};
