import { useOrg } from "#/utils/user";
import { LexoRank } from "lexorank";
import { useAvailableModels } from "./models";
import { convertMessages, translatePromptModelParams } from "./prompt-utils";
import { useMemo } from "react";

export const useDefaultPromptData = ({
  orgName: orgNameProp,
}: { orgName?: string } = {}) => {
  const org = useOrg();
  const { allAvailableModels, configuredModelsByProvider } = useAvailableModels(
    {
      orgName: orgNameProp ?? org.name,
    },
  );

  const fallbackModel = Object.keys(allAvailableModels)[0];
  let model = fallbackModel;

  // Prefer OpenAI and Anthropic models if available, otherwise use the first model from the first configured provider, otherwise fallback to the default model.
  if (configuredModelsByProvider["openai"]?.length > 0) {
    model = configuredModelsByProvider["openai"][0].modelName;
  } else if (configuredModelsByProvider["anthropic"]?.length > 0) {
    model = configuredModelsByProvider["anthropic"][0].modelName;
  } else if (Object.keys(configuredModelsByProvider).length > 0) {
    model = Object.values(configuredModelsByProvider)[0][0].modelName;
  }

  return useMemo(() => {
    const params = translatePromptModelParams({
      newModel: model,
      allAvailableModels,
    });
    const prompt = convertMessages({
      newModel: model,
      allAvailableModels,
    });

    return {
      prompt,
      options: {
        position: LexoRank.middle().toString(),
        model: model,
        params,
      },
    };
  }, [allAvailableModels, model]);
};
