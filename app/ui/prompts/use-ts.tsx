"use client";

import { useState } from "react";
import { useEffect } from "react";
// import ts from "typescript";
import {
  // createDefaultMapFromCDN,
  type VirtualTypeScriptEnvironment,
} from "@typescript/vfs";

export const useTsEnv = () => {
  const [tsEnv, _setTsEnv] = useState<VirtualTypeScriptEnvironment | null>(
    null,
  );

  useEffect(() => {
    const createEnv = async () => {
      // const fsMap = await createDefaultMapFromCDN(
      //   { target: ts.ScriptTarget.ES2022 },
      //   "5.5.4",
      //   true,
      //   ts,
      // );
      // const system = createSystem(fsMap);
      // const env = createVirtualTypeScriptEnvironment(system, [], ts, {});
      // setTsEnv(env);
    };
    createEnv();
  }, []);

  return { tsEnv };
  // return { tsEnv: null };
};
