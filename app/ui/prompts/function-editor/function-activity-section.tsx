import { CollapsibleSection } from "#/ui/collapsible-section";
import { Activity } from "#/ui/trace/activity";
import { type CommentData } from "@braintrust/local/api-schema";
import { newId } from "#/utils/btapi/btapi";
import { type ActivityProps, type ObjectType } from "./types";
import { type UIFunction } from "#/ui/prompts/schema";
import { type FunctionObjectType } from "@braintrust/typespecs";
import { useRowAuditLog } from "#/ui/trace/query";
import { useCallback } from "react";

export function FunctionActivitySection({
  rowId,
  commentOn,
  deleteComment,
  setPromptVersion,
  selectedVersion,
  auditLogReady,
  auditLogScan,
  objectType,
  initialFunction,
  type,
}: ActivityProps & {
  initialFunction?: UIFunction | null;
  objectType: ObjectType;
  type: FunctionObjectType;
}) {
  const { auditLogData } = useRowAuditLog({
    auditLogScan,
    auditLogReady,
    rowId,
    dynamicObjectId: null,
    objectType,
  });

  const addComment = useCallback(
    async (comment: CommentData) => {
      if (!initialFunction) {
        throw new Error("No prompt loaded");
      }
      const commentId = newId();
      const transactionId = await commentOn!([
        {
          id: commentId,
          row: initialFunction,
          comment,
        },
      ]);
      return { transactionId, commentId };
    },
    [commentOn, initialFunction],
  );

  return (
    <CollapsibleSection title="Activity">
      <Activity
        auditLog={auditLogData}
        // Omit comment section when commentOn isn't provided
        addComment={commentOn ? addComment : undefined}
        deleteComment={deleteComment}
        objectName={type}
        onSelectVersion={setPromptVersion}
        selectedVersion={selectedVersion}
        className="mx-0"
        entityName={initialFunction?.name}
        functionType={type}
        objectId={initialFunction?.id}
      />
    </CollapsibleSection>
  );
}
