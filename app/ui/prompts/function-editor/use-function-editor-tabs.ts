import { useState } from "react";
import { type FunctionTab } from "./types";
import { type UIFunction } from "#/ui/prompts/schema";

export function useFunctionEditorTabs({
  functionData,
}: {
  functionData?: UIFunction["function_data"];
}) {
  const defaultTab = getFunctionEditorTab(functionData);
  const [_activeTab, setActiveTab] = useState<FunctionTab>();
  const activeTab = _activeTab ?? defaultTab;

  return { activeTab, setActiveTab };
}

export function getFunctionEditorTab(
  functionData?: UIFunction["function_data"],
): FunctionTab {
  return functionData?.type === "code"
    ? functionData?.data.runtime_context.runtime === "node"
      ? "ts"
      : "py"
    : "llm";
}
