import { type CommentFn, type DeleteCommentFn } from "#/utils/mutable-object";
import { type TransactionId } from "braintrust/util";
import { type UIFunction } from "#/ui/prompts/schema";
import { type RefObject, type Dispatch, type SetStateAction } from "react";
import { type Message, type FunctionObjectType } from "@braintrust/typespecs";
import { type JSONStructure } from "#/ui/prompts/hooks";
import { type CopilotContextBuilder } from "#/ui/copilot/context";

export type DBProps = {
  rowId: string | null;
  scan: string | null;
  scanReady: number[];
};

export type ActivityProps = {
  rowId: string;
  setPromptVersion?: Dispatch<SetStateAction<TransactionId | null>>;
  commentOn?: CommentFn;
  deleteComment?: DeleteCommentFn;
  auditLogScan: string | null;
  auditLogReady: number[];
  selectedVersion: TransactionId | null;
};

export type ModeType = "create" | "update" | "view_saved" | "view_unsaved";

export type CreateUpdateMode = {
  type: "create" | "update";
  onDuplicate?: (func: UIFunction) => void;
  onDelete?: VoidFunction;
  upsert: (
    prompt: UIFunction,
    updateSlug: boolean,
  ) => Promise<TransactionId | null>;
};

export type Mode =
  | CreateUpdateMode
  | {
      type: "view_saved";
    }
  | {
      type: "view_unsaved";
      upsert: (prompt: UIFunction) => Promise<TransactionId | null>;
    };

export type ObjectType =
  | "project_prompts"
  | "project_functions"
  | "prompt_session";

export type FunctionTab = "llm" | "py" | "ts";

export type FunctionEditorContext = "functions" | "playground" | "try_prompt";

export type MetaFields = {
  name?: string | null;
  slug?: string | null;
  description?: string | null;
  metadata?: Record<string, unknown> | null;
};

export type FunctionEditorProps = {
  identifier: string;
  orgName: string;
  projectId: string;
  projectName: string;
  type: FunctionObjectType;
  objectType: ObjectType;
  mode: Mode;
  activityProps?: ActivityProps;
  initialFunction: UIFunction | null;
  variableData?: Record<string, unknown>;
  title?: string;
  context: FunctionEditorContext;
  jsonStructure?: JSONStructure | null;
  outputNames?: string[];
  copilotContext?: CopilotContextBuilder;
  status: string;
  isDirtyRef: RefObject<boolean>;
  extraMessages?: Message[];
};
