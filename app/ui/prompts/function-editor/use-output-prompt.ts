import { useCallback } from "react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type MetaFields } from "./types";

type Args = {
  coercedFunction: UIFunction;
  metaFields: <PERSON>a<PERSON>ields;
};

/** Combines meta fields and prompt editor/code editor atom state to construct the output prompt. Returns a callback which returns this output prompt when invoked with the current prompt state. */
export function useOutputPrompt({ coercedFunction, metaFields }: Args) {
  return useCallback(
    (promptState: SyncedPlaygroundBlock): UIFunction => {
      const prompt: UIFunction = {
        ...promptState,
        ...metaFields,
        name: metaFields.name ?? coercedFunction.name,
        slug: metaFields.slug ?? coercedFunction.slug,
        tags: coercedFunction.tags,
        project_id: coercedFunction.project_id,
        function_type: coercedFunction.function_type,
      };

      if (prompt.function_data.type === "code") {
        delete prompt.prompt_data;
      }

      return prompt;
    },
    [
      metaFields,
      coercedFunction.name,
      coercedFunction.slug,
      coercedFunction.tags,
      coercedFunction.project_id,
      coercedFunction.function_type,
    ],
  );
}
