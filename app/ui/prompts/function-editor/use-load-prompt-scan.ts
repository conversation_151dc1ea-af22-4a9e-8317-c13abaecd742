import { dbQuery, useDuckDB } from "#/utils/duckdb";
import { singleQuote } from "@braintrust/local/query";
import { type DBProps, type ObjectType } from "./types";
import { parseObjectJSON } from "#/utils/schema";
import { promptSchema, type UIFunction } from "#/ui/prompts/schema";
import { useQuery } from "@tanstack/react-query";

export function useLoadPromptScan({
  rowId,
  scan,
  scanReady,
  objectType,
  shouldSkip = false,
}: DBProps & { objectType: ObjectType; shouldSkip?: boolean }) {
  const duck = useDuckDB();
  const isEnabled = !!rowId && !!scan && !!duck && !shouldSkip;
  const isCreating = rowId === "new";

  const {
    data: prompt,
    isLoading,
    isPlaceholderData,
    error,
  } = useQuery<UIFunction | null>({
    queryKey: ["prompt", rowId, scan, scanReady, objectType],
    queryFn: async ({ signal }) => {
      if (isCreating) {
        return null;
      }

      const conn = await duck!.connect();

      const rowData = await dbQuery(
        conn,
        signal,
        `SELECT * FROM (${scan}) WHERE id = ${singleQuote(rowId!)}`,
      );
      if (!rowData || rowData.numRows === 0) return null;

      const parsedPrompt = promptSchema.safeParse(
        parseObjectJSON(objectType, rowData.toArray()[0].toJSON()),
      );
      if (!parsedPrompt.success) {
        // This will show up as a toast
        throw new Error(
          `Invalid prompt. You must fix this prompt through the API to view and use it.`,
        );
      }

      return parsedPrompt.success ? parsedPrompt.data : null;
    },
    enabled: isEnabled,
    // Avoid showing a loading state when changing versions by using the previous data if the rowId is the same
    placeholderData: (previousData, previousQuery) => {
      if (previousData?.id === rowId || previousQuery?.queryKey[1] === "new") {
        return previousData;
      }

      return undefined;
    },
  });

  return {
    status: error
      ? "error"
      : isPlaceholderData
        ? "placeholder"
        : (isLoading || !isEnabled) && !isCreating && !shouldSkip
          ? "loading"
          : "loaded",
    prompt: prompt ?? null,
    error,
  };
}
