import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  use<PERSON>allback,
  useMemo,
  useRef,
} from "react";
import { Field, Schema, Utf8 } from "apache-arrow";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
  promptSchema,
} from "#/ui/prompts/schema";
import { useAvailableModels } from "#/ui/prompts/models";
import { produce } from "immer";
import { type ZodError } from "zod";
import {
  makeFunctionEditorFunctionId,
  usePromptExtensions,
} from "#/ui/prompts/hooks";
import { type InvokeFunctionRequest } from "@braintrust/typespecs";
import { type TextEditorHandle } from "#/ui/text-editor";

import { toast } from "sonner";
import { zodErrorToString } from "#/utils/validation";
import {
  FunctionDescriptionField,
  FunctionMetadataField,
  FunctionNameField,
  FunctionSlugField,
} from "./function-meta-fields";
import { type SetValue } from "#/lib/clientDataStorage";
import { LibraryItemLinks } from "#/app/app/[org]/p/[project]/library/library-item-links";
import { type CompletionBlockHandle } from "#/app/app/[org]/prompt/[prompt]/completion-block";
import { FunctionRunSection } from "./function-run-section";
import { type FunctionEditorProps, type MetaFields } from "./types";
import { FunctionActivitySection } from "./function-activity-section";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { FunctionEditor } from "./function-editor";
import { useUpsellContext } from "#/app/playground/upsell-dialog";
import useEvent from "react-use-event-hook";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { NoAISecrets } from "#/ui/prompts/empty";
import { useHotkeys } from "react-hotkeys-hook";

export const emptyPromptMetaSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "search_text", type: new Utf8() }),
]);

const DEFAULT_OUTPUT_NAMES: string[] = [];

export function FunctionDialogBody({
  orgName,
  projectId,
  projectName,
  objectType,
  variableData,
  type,
  context,
  jsonStructure = null,
  outputNames = DEFAULT_OUTPUT_NAMES,
  copilotContext,
  mode,
  isSlugTouchedRef,
  metaFields,
  setMetaFields,
  setError,
  initialFunction,
  getOutputPrompt,
  pyEditorRef,
  jsEditorRef,
  activityProps,
  dataEditorValue,
  setDataEditorValue,
}: Omit<
  FunctionEditorProps,
  "title" | "status" | "isDirtyRef" | "identifier"
> & {
  metaFields: MetaFields;
  setMetaFields: Dispatch<SetStateAction<MetaFields>>;
  isSlugTouchedRef: RefObject<boolean>;
  setError: Dispatch<SetStateAction<string | null>>;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  pyEditorRef: RefObject<TextEditorHandle<string> | null>;
  jsEditorRef: RefObject<TextEditorHandle<string> | null>;
  dataEditorValue: Record<string, unknown>;
  setDataEditorValue: SetValue<Record<string, unknown>>;
}) {
  const isUpdate = mode.type === "update";
  const isReadOnly = mode.type === "view_saved" || mode.type === "view_unsaved";
  const hasSavedVersions = mode.type === "update" || mode.type === "view_saved";

  const { onUpsell } = useUpsellContext();
  const { noConfiguredSecrets } = useAvailableModels({ orgName });
  const showNoConfiguredSecretsMessage = noConfiguredSecrets && !onUpsell;

  const { sortedSyncedPromptsAtom_ROOT, addMessage } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );
  const hasPrompt = !!prompt.prompt_data.prompt;

  const cellRef = useRef<CompletionBlockHandle>(null);
  const runPrompt = useEvent(async () => {
    if (onUpsell) {
      onUpsell();
      return;
    }

    if (!cellRef.current) {
      return;
    }

    const outputPrompt = getOutputPrompt(prompt);
    const functionId = makeFunctionEditorFunctionId(outputPrompt, isUpdate);
    if (!functionId) {
      toast.warning(`No ${type} to run`);
      return;
    }

    const parent: InvokeFunctionRequest["parent"] | undefined =
      context === "functions"
        ? {
            object_type: "project_logs",
            object_id: projectId,
          }
        : undefined;

    cellRef.current.submit(
      {
        type: "function",
        functionId,
        input: dataEditorValue ?? {},
        parent,
      },
      orgName,
    );
  });

  const { extensions } = usePromptExtensions({
    jsonStructure,
    outputNames,
    expandInputVariables: false,
  });

  const onTabChange = useCallback(() => {
    setError(null);
  }, [setError]);

  const onRun = useCallback(() => {
    if (showNoConfiguredSecretsMessage) {
      toast("No configured secrets", {
        description: <NoAISecrets orgName={orgName} />,
      });
      return;
    }
    runPrompt();
  }, [orgName, runPrompt, showNoConfiguredSecretsMessage]);

  // Rather than only enabling this keybinding when message blocks have focus, disable that behavior by
  // omitting FunctionEditor's onRun prop and handling all mod+enter behavior for the dialog here.
  useHotkeys("mod+enter", onRun, {
    enableOnFormTags: true,
    enableOnContentEditable: true,
  });

  return (
    <>
      {mode.type !== "view_unsaved" && (
        <div className="flex w-full gap-4">
          <FunctionNameField
            className="flex-1"
            name={metaFields.name ?? ""}
            isUpdate={isUpdate}
            isReadOnly={isReadOnly}
            onChange={({ name, slug }) => {
              setMetaFields(
                produce((prev) => {
                  prev.name = name;
                  if (slug && !isSlugTouchedRef.current) {
                    prev.slug = slug;
                  }
                }),
              );
            }}
          />
          <FunctionSlugField
            className="flex-1"
            slug={metaFields.slug ?? ""}
            isReadOnly={isReadOnly}
            onChange={(newSlug) => {
              setMetaFields(
                produce((prev) => {
                  prev.slug = newSlug;
                }),
              );
              // eslint-disable-next-line react-compiler/react-compiler
              isSlugTouchedRef.current = true;
            }}
          />
        </div>
      )}
      <FunctionEditor
        orgName={orgName}
        type={type}
        copilotContext={copilotContext}
        modeType={mode.type}
        onTabChange={onTabChange}
        pyEditorRef={pyEditorRef}
        jsEditorRef={jsEditorRef}
        extensions={extensions}
        rowData={dataEditorValue}
      />
      <div>
        <CollapsibleSection title="Run" keepContentsMounted>
          <FunctionRunSection
            showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
            copilotContext={copilotContext}
            dataEditorValue={dataEditorValue}
            setDataEditorValue={setDataEditorValue}
            promptData={prompt.prompt_data}
            initialFunction={initialFunction}
            runPrompt={runPrompt}
            variableData={variableData}
            ref={cellRef}
            onAddMessageToPrompt={
              hasPrompt
                ? (message) => {
                    addMessage({
                      id: prompt.id,
                      message,
                    });
                  }
                : undefined
            }
          />
        </CollapsibleSection>
      </div>
      {mode.type !== "view_unsaved" && (
        <>
          <FunctionDescriptionField
            description={metaFields.description ?? ""}
            isReadOnly={isReadOnly}
            onChange={(description) => {
              setMetaFields(
                produce((prev) => {
                  prev.description = description;
                }),
              );
            }}
          />
          <FunctionMetadataField
            metadata={metaFields.metadata}
            isReadOnly={isReadOnly}
            onChange={async (metadata) => {
              // TODO: debounce parsing?
              setMetaFields(
                produce((prev) => {
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                  prev.metadata = metadata as Record<string, unknown> | null;
                }),
              );
              try {
                promptSchema.shape.metadata.parse(metadata);
                setError(null);
              } catch (error) {
                setError(
                  "Invalid metadata: " +
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    zodErrorToString(error as ZodError, 0, false),
                );
              }
              return prompt._xact_id;
            }}
            functionId={prompt.id}
          />
        </>
      )}
      {context !== "playground" && hasSavedVersions && (
        <div>
          <LibraryItemLinks
            projectName={projectName}
            objectType={type}
            objectId={prompt.id}
            objectName={metaFields.name ?? undefined}
            objectSlug={metaFields.slug ?? undefined}
          />
        </div>
      )}
      {activityProps && (
        <div>
          <FunctionActivitySection
            objectType={objectType}
            initialFunction={initialFunction}
            type={type}
            {...activityProps}
          />
        </div>
      )}
    </>
  );
}
