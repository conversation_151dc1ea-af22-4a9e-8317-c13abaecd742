export const PLACEHOLDER_TS = `// Enter handler function that returns a numeric score between 0 and 1,
// or null to skip scoring
function handler({
  input,
  output,
  expected,
  metadata,
}: {
  input: any;
  output: any;
  expected: any;
  metadata: Record<string, any>;
}): number | null {
  if (expected === null) return null;
  return output === expected ? 1 : 0;
}`;

export const PLACEHOLDER_PY = `from typing import Any
# Enter handler function that returns a numeric score between 0 and 1,
# or None to skip scoring
def handler(
  input: Any,
  output: Any,
  expected: Any,
  metadata: dict[str, Any]
) -> float | None:
  if expected is None:
    return None
  return 1.0 if output == expected else 0.0`;
