import {
  <PERSON>s<PERSON>ield,
  TransactionIdField,
  useDB<PERSON><PERSON>y,
  useMaterializedRecords,
  useParquetView,
} from "#/utils/duckdb";
import { useOrg } from "#/utils/user";
import { useCallback, useContext, useMemo, useState } from "react";
import { useTagsFormatter } from "#/ui/trace/tags";
import { type SearchSpec, buildDefaultOrderBy } from "#/utils/search/search";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { Field, Schema, Utf8 } from "apache-arrow";
import { type UIFunction } from "./schema";
import { type promptInfo } from "./prompt-actions";
import { z } from "zod";
import { sha1 } from "#/utils/hash";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import {
  type BTQLTableDefinition,
  buildDefaultWhere,
  use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "#/utils/search-btql";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

import { useQueryFunc } from "#/utils/react-query";
import type { FormatterMap } from "#/ui/field-to-column";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { CreatorFormatter } from "#/ui/table/formatters/creator-formatter";
import { type FunctionObjectType } from "@braintrust/typespecs";
import { zodToLogicalSchema } from "#/utils/zod-to-logical-schema";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { functionObjectTypeToFunctionType } from "./hooks";
import type { Clause } from "#/utils/search/search";
import { Bubble } from "#/ui/table/bubble";

export const emptyFunctionMetaSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "search_text", type: new Utf8() }),
]);

export const functionsListMetadata: BTQLTableDefinition = {
  logical: zodToLogicalSchema(
    z.strictObject({
      id: z.string(),
      name: z.string(),
      slug: z.string(),
      description: z.string(),
      creator: z.record(z.any()),
      tags: z.array(z.string()),
    }),
  ),
  physical: {
    columns: {
      id: { path: ["id"], type: { type: "varchar" } },
      name: { path: ["name"], type: { type: "varchar" } },
      slug: { path: ["slug"], type: { type: "varchar" } },
      description: { path: ["description"], type: { type: "varchar" } },
      creator: { path: ["creator"], type: { type: "json" } },
      tags: { path: ["tags"], type: { type: "json" } },
    },
  },
};

export function useFunctionsListQuery({
  functionObjectType,
  textSearch,
}: {
  functionObjectType: FunctionObjectType;
  textSearch?: string;
}) {
  const org = useOrg();
  const { projectId, config } = useContext(ProjectContext);

  if (!projectId) {
    throw new Error("Cannot instantiate PromptsViewer outside project");
  }

  const { clauseChecker } = useClauseChecker(functionsListMetadata);
  const viewParams: ViewParams = {
    objectType: "project",
    objectId: projectId,
    viewType: "prompts",
  };
  const pageIdentifier = functionObjectType + "-" + projectId;
  const viewProps = useViewStates({
    viewParams,
    clauseChecker,
    pageIdentifier,
  });

  // Merge internal search state with text search for the query
  const search = useMemo(() => {
    const baseSearch = viewProps.search;

    // If there's text search, add it as a match clause
    if (textSearch && textSearch.trim()) {
      const matchClause: Clause<"match"> = {
        type: "match",
        text: textSearch,
        label: "",
        originType: "form",
        // This won't ever be shown but the Clause type requires it
        bubble: new Bubble({
          type: "match",
          label: textSearch,
          text: textSearch,
          hidden: true,
          clear: () => {},
        }),
      };

      return {
        ...baseSearch,
        match: [...(baseSearch.match ?? []), matchClause],
      };
    }

    return baseSearch;
  }, [textSearch, viewProps.search]);

  const { data: promptMetadata, isPending: promptMetadataLoading } =
    useQueryFunc<typeof promptInfo>({
      fName: "promptInfo",
      args: { orgId: org.id, projectId },
    });

  const {
    refreshed: promptMetaReady,
    name: promptMetaTable,
    hasLoaded: promptMetaLoaded,
  } = useMaterializedRecords(
    `prompts_${functionObjectType}_${sha1(projectId ?? org.id ?? "unknown")}`,
    promptMetadata ?? null,
    emptyFunctionMetaSchema,
    "search_text",
  );

  const objectType: DataObjectType =
    functionObjectType === "prompt" ? "project_prompts" : "project_functions";

  const objectSearch = useMemo(
    () => ({ id: projectId, objectType }),
    [projectId, objectType],
  );
  const {
    refreshed: promptsReady,
    schema: promptsSchema,
    scan: promptsScan,
    channel: _promptsChannel,
    error,
  } = useParquetView({
    objectType,
    search: objectSearch,
  });

  const isUnsupported = error && `${error}`.includes("InternalServerError");

  const functionType = useMemo((): UIFunction["function_type"] | null => {
    return functionObjectTypeToFunctionType(functionObjectType);
  }, [functionObjectType]);

  const projectedPaths = useMemo(
    () =>
      [
        TransactionIdField,
        "name",
        "slug",
        "description",
        "creator",
        TagsField,
        "prompt_data",
        "metadata",
        "id",
      ]
        .concat(objectType === "project_functions" ? ["function_data"] : [])
        .filter(
          (f) =>
            f === "creator" || promptsSchema?.fields.find((n) => n.name === f),
        ),
    [objectType, promptsSchema?.fields],
  );

  const buildPromptsQuery = useCallback(
    (s: SearchSpec) => {
      const where = buildDefaultWhere(s, projectedPaths);
      const orderBy = buildDefaultOrderBy(s);

      // This filters anything other than a simple prompt from the list of prompts.
      const functionTypeFilters: string[] = ["TRUE"];
      if (promptsSchema?.fields.find((f) => f.name === "origin")) {
        functionTypeFilters.push(
          `e1.origin IS NULL or NOT COALESCE(json_extract_string(e1.origin, '$.internal')::boolean, false)`,
        );
      }

      if (promptsSchema?.fields.find((f) => f.name === "function_type")) {
        if (functionType) {
          functionTypeFilters.push(
            `e1.function_type = ${singleQuote(functionType)}`,
          );
        } else if (functionObjectType === "agent") {
          // TODO: We may want to make a clearer distinction that these are agents via the function_type, but
          // since a graph could be theoretically used as a scorer, we don't want to abuse that field.
          functionTypeFilters.push(
            `json_extract_string(e1.function_data, '$.type') = 'graph'`,
          );
        } else if (objectType === "project_prompts") {
          functionTypeFilters.push(
            `e1.function_type IS NULL AND json_extract_string(e1.prompt_data, '$.parser') IS NULL`,
          );
        }
      }

      return promptsScan && promptMetaTable && promptsSchema?.fields
        ? `SELECT
          *
        FROM (
          SELECT
            ${projectedPaths.join(", ")}
            FROM (
              SELECT *,
              (SELECT user FROM ${doubleQuote(
                promptMetaTable,
              )} WHERE id = "e1".id) AS creator
              FROM (${promptsScan}) "e1"
              WHERE ${functionTypeFilters.map((s) => `(${s})`).join(" AND ")}
            )
            ${orderBy ? `ORDER BY ${orderBy}` : ""}
          ) base
            WHERE ${where}
        `
        : null;
    },
    [
      projectedPaths,
      promptsSchema?.fields,
      promptsScan,
      promptMetaTable,
      functionType,
      functionObjectType,
      objectType,
    ],
  );

  const promptsSignals = useMemo(
    () => [promptsReady, promptMetaReady],
    [promptsReady, promptMetaReady],
  );

  const baseQuery = useMemo(
    () => buildPromptsQuery(search),
    [buildPromptsQuery, search],
  );

  const tagsFormatter = useTagsFormatter({
    tagConfig: config.tags,
  });

  const tableQuery = useMemo(() => {
    const formatters: FormatterMap = makeFormatterMap({
      [TagsField]: {
        cell: tagsFormatter,
      },
      creator: {
        cell: CreatorFormatter,
      },
    });

    return {
      query: baseQuery,
      signals: promptsSignals,
      formatters: formatters,
    };
  }, [baseQuery, promptsSignals, tagsFormatter]);

  return {
    isUnsupported,
    objectType,
    functionType,
    tableQuery,
    projectedPaths,
    // viewProps without text search for filters UI and URL.
    viewProps,
    // viewProps with (optional) text search for VizQuery table.
    viewPropsWithSearch: { ...viewProps, search },
    isLoading: promptMetadataLoading || !promptMetaLoaded,
  };
}

export const useFunctionsList = ({
  functionObjectType,
  limit,
}: {
  functionObjectType: FunctionObjectType;
  limit?: number;
}) => {
  const { tableQuery, isLoading } = useFunctionsListQuery({
    functionObjectType,
  });

  const [error, setError] = useState<string | null>(null);

  const { data, hasLoaded } = useDBQuery(
    tableQuery.query
      ? `SELECT * FROM (${tableQuery.query}) ${limit ? `LIMIT ${limit}` : ""}`
      : null,
    tableQuery.signals,
    {
      setError,
    },
  );

  return { loading: isLoading || !hasLoaded, data, error };
};
