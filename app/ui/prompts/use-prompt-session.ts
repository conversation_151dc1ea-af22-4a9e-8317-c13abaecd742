import { useDurableObjectByIds } from "#/utils/mutable-object";
import { useMemo } from "react";

export function usePromptSession({
  promptSessionId,
  id,
  version,
}: {
  promptSessionId: string | undefined;
  id: string | undefined;
  version?: string;
}) {
  const durableObjects = useDurableObjectByIds({
    ids: useMemo(() => (id ? [id] : undefined), [id]),
    version,
    objectType: "prompt_session",
    objectId: id ? promptSessionId : undefined,
  });

  const promptSession = useMemo(
    () => durableObjects.objects?.[0] && durableObjects.objects?.[0],
    [durableObjects.objects],
  );

  return {
    promptSession,
    loading: durableObjects.status === "loading",
    error: durableObjects.status === "error",
    ...durableObjects,
  };
}
