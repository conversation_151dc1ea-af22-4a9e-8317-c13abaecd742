import { newId } from "#/utils/btapi/btapi";
import {
  LogId<PERSON>ield,
  OrgIdField,
  ProjectIdField,
  TransactionIdField,
} from "#/utils/duckdb";
import { type DML } from "#/utils/mutable-object";
import {
  functionTypeEnum,
  type PromptData,
  promptDataSchema,
  functionSchema as rawFunctionSchema,
  sseConsoleEventDataSchema,
} from "@braintrust/typespecs";
import { z } from "zod";
import { type deletePromptSlugs } from "./prompt-actions";
import {
  deletePrompt,
  insertSlug,
  updateSlug as action_updateSlug,
} from "./prompt-client-actions";
import { IS_MERGE_FIELD, TRANSACTION_ID_FIELD } from "braintrust/util";
import { PROMPT_LOG_ID } from "#/utils/schema";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { isEmpty } from "#/utils/object";

export type { PromptData, PromptBlockData } from "@braintrust/typespecs";

// We must mark certain fields optional that are present in the prompts returned
// by the backend, but not possible to specify in new prompts created in the
// webapp. Ideally we could use the `createPromptSchema` schema from the
// typespecs for new prompts generated in the webapp, but other parts of the
// prompt code want to use the same type for both created prompt objects and
// received prompts, so this is not currently possible.
export const promptSchema = rawFunctionSchema
  .omit({
    org_id: true,
    log_id: true,
    created: true,
    metadata: true,
    slug: true,
    name: true,
    function_data: true,
    function_type: true,
  })
  .merge(
    z.object({
      org_id: rawFunctionSchema.shape.org_id.nullish(),
      log_id: rawFunctionSchema.shape.log_id.nullish(),
      metadata: rawFunctionSchema.shape.metadata.nullish(),
      slug: z.string().nullish(),
      name: z.string().nullish(),
      function_data: rawFunctionSchema.shape.function_data.nullish().transform(
        (data) =>
          data ??
          ({
            type: "prompt",
          } as const),
      ),
      function_type: rawFunctionSchema.shape.function_type.nullish(),
    }),
  );

export type UIFunction = z.infer<typeof promptSchema>;
export type UIFunctionData = UIFunction["function_data"];

export const playgroundBlockSchema = z.object({
  prompt_data: promptDataSchema,
  function_data: rawFunctionSchema.shape.function_data.nullish().transform(
    (data) =>
      data ??
      ({
        type: "prompt",
      } as const),
  ),
  function_type: functionTypeEnum.nullish(),
});
export type PlaygroundBlock = z.infer<typeof playgroundBlockSchema>;
export type PlaygroundBlockWithId = PlaygroundBlock & { id: string };

export const syncedPlaygroundBlockSchema = playgroundBlockSchema.extend({
  id: z.string(),
  [TRANSACTION_ID_FIELD]: z.string(),
});
export const syncedPlaygroundBlockArraySchema = z.array(
  syncedPlaygroundBlockSchema,
);
export type SyncedPlaygroundBlock = z.infer<typeof syncedPlaygroundBlockSchema>;

export const promptSessionStateSchema = z.record(
  promptSchema.shape.id,
  playgroundBlockSchema,
);
export type PromptSessionState = z.infer<typeof promptSessionStateSchema>;

export function newPrompt(
  projectId: string,
  promptId?: string,
): Omit<UIFunction, "prompt_data"> & {
  prompt_data: PromptData;
} {
  return {
    id: promptId ?? newId(),
    project_id: projectId,
    [TransactionIdField]: "0",
    name: null,
    slug: null,
    description: null,
    function_data: { type: "prompt" },
    prompt_data: { options: {} },
    tags: null,
  };
}

export const allCompletionsStreamingCellSchema = z.object({
  completion: z.string(),
  error: z.string().optional(),
  isJSON: z.boolean(),
  consoleMessages: z.array(sseConsoleEventDataSchema).optional(),
  streamingStatus: z.string().optional(),
  reasoning: z.string().optional(),
  isTaskDone: z.boolean().optional(),
});

export type AllCompletionsStreamingCell = z.infer<
  typeof allCompletionsStreamingCellSchema
>;

export const allCompletionsStringsSchema = z.record(
  // datasetRowId
  z.string(),
  z.record(
    // generationId
    z.string(),
    allCompletionsStreamingCellSchema,
  ),
);
export type AllCompletionsStrings = z.infer<typeof allCompletionsStringsSchema>;

export async function createOrUpdatePrompt({
  dml,
  orgId,
  update,
  updateSlug,
  prompt: promptArg,
  onOptimisticUpdate,
}: {
  dml: DML;
  orgId: string;
  update: boolean;
  updateSlug: boolean;
  prompt: UIFunction;
  onOptimisticUpdate?: VoidFunction;
}) {
  // We should probably build this into the library, but for now we'll implement
  // "REPLACE" here. The idea is:
  //  1. If selectedRowId is set, this is an update, and we should prepare an update,
  //     and then just replace the row's values with our own.
  //  2. If it's an insert, then we should just insert the row with the correct additional
  //     fields needed.

  // In case the prompt has a transaction id, we need to remove it
  const { [TransactionIdField]: _, ...promptNoXact } = promptArg;
  const prompt = {
    ...promptNoXact,
    [OrgIdField]: orgId, // DEPRECATION_NOTICE: Once everyone upgrades to >= 0.0.38, we can remove this
    [LogIdField]: PROMPT_LOG_ID,
    [IS_MERGE_FIELD]: false,
  };

  if (prompt.function_data.type !== "prompt") {
    delete prompt.prompt_data;
  }

  if (update) {
    // NOTE: Pedantically speaking, we should use the PATCH endpoint in the data plane, but
    // there are a couple reasons not to:
    // 1. We can ship this feature without requiring a data plane upgrade (versions 0.0.54 onwards should be fine)
    // 2. We will lose out on the optimistic update machinery of the DML class
    //
    // The risk with the code as-written is that we could update the prompt in the control plane, but not the data plane,
    // which if it occurs, we at least can repair on behalf of the customer.
    if (updateSlug) {
      if (!prompt.slug) {
        throw new Error("Slug cannot be empty");
      }
      try {
        await action_updateSlug({
          promptId: prompt.id,
          slug: prompt.slug,
        });
      } catch (e) {
        console.error("Failed to update slug\n", e);
        throw new Error(
          "Failed to update slug: a slug with this name already exists",
        );
      }
    }

    onOptimisticUpdate?.();

    return await dml.update(
      [prompt],
      Object.entries(prompt)
        .filter(
          ([k]) =>
            !["id", TransactionIdField, ProjectIdField, OrgIdField].includes(k),
        )
        .map(([k, v]) => ({
          path: [k],
          newValue: v,
        })),
    );
  } else {
    if (isEmpty(prompt.name) || prompt.name.trim() === "") {
      throw new Error("Prompt name cannot be empty");
    }
    if (isEmpty(prompt.slug) || prompt.slug.trim() === "") {
      throw new Error("Prompt slug cannot be empty");
    }
    if (
      prompt.prompt_data?.parser &&
      Object.keys(prompt.prompt_data?.parser.choice_scores).length == 0
    ) {
      throw new Error("At least one choice score must be provided");
    }

    // First, try to reserve a slug
    const { prompt_id: insertedPromptId, found_existing } = await insertSlug({
      orgId,
      projectId: prompt.project_id,
      promptId: prompt.id,
      slug: prompt.slug,
    });
    if (found_existing) {
      throw new Error(`Slug '${prompt.slug}' already exists`);
    }
    try {
      return await dml.upsert([prompt], {
        onOptimisticUpdate,
      });
    } catch (e) {
      // The upsert failed. Delete the prompt.
      try {
        await deletePrompt({ id: insertedPromptId });
      } catch (innerE) {
        console.error(
          "Failed to delete prompt after backend insert failed\n",
          innerE,
        );
      }
      throw e;
    }
  }
}

export async function deletePrompts({
  dml,
  orgId,
  projectId,
  rowsToDelete,
  getToken,
}: {
  dml: DML;
  orgId: string;
  projectId: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  rowsToDelete: any[];
  getToken: () => Promise<string | null>;
}) {
  const btApiDelete = (async () => {
    const preparedDeletes = await dml.prepareDeletes(rowsToDelete);
    await dml.upsert(preparedDeletes);
  })();

  const metadataDelete = (async () => {
    await invokeServerAction<typeof deletePromptSlugs>({
      fName: "deletePromptSlugs",
      args: {
        orgId: orgId,
        projectId: projectId,
        promptIds: rowsToDelete.map((r) => r.id),
      },
      getToken,
    });
  })();

  await Promise.all([btApiDelete, metadataDelete]);
}
