"use server";

import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  makeFullResultSetQuery,
  setDeletedAtObjects,
} from "#/pages/api/_object_crud_util";
import { sanitizeAISecret } from "#/pages/api/ai_secret/_util";
import { type AISecret } from "@braintrust/typespecs";
import { z } from "zod";
import { otelTraced, otelWrapTraced } from "#/utils/tracing";

export async function deletePromptSlugs(
  {
    orgId,
    projectId,
    promptIds: promptIdsRaw,
  }: {
    orgId: string;
    projectId: string;
    promptIds: string[];
  },
  authLookupRaw?: AuthLookup,
): Promise<null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!promptIdsRaw.length) {
    return null;
  }
  const promptIds: [string, ...string[]] = [
    promptIdsRaw[0],
    ...promptIdsRaw.slice(1),
  ];

  await setDeletedAtObjects({
    authLookup,
    permissionInfo: {
      aclObjectType: "prompt",
      aclPermission: "delete",
    },
    filters: {
      org_id: orgId,
      project_id: projectId,
      id: promptIds,
    },
    fullResultsSize: promptIds.length,
  });
  return null;
}

const promptInfoSchema = z.strictObject({
  id: z.string(),
  user: z.string(),
});

export type PromptInfo = z.infer<typeof promptInfoSchema>;

export async function promptInfo(
  {
    orgId,
    projectId,
  }: {
    orgId?: string;
    projectId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<PromptInfo[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!orgId) {
    return [];
  }

  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "prompt",
      aclPermission: "read",
    },
    filters: {
      org_id: orgId,
      project_id: projectId,
    },
  });
  const query = `
    with full_results as (${fullResultSetQuery})
    select
        prompts.id,
        json_build_object(
            'id', users.id,
            'given_name', users.given_name,
            'family_name', users.family_name,
            'email', users.email,
            'avatar_url', users.avatar_url
        )::text user
    from
      full_results prompts left join users on prompts.user_id = users.id
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return promptInfoSchema.array().parse(rows);
}

export const queryPromptModels = otelWrapTraced(
  "queryPromptModels",
  async (
    {
      org_name,
    }: {
      org_name: string;
    },
    authLookupRaw?: AuthLookup,
  ): Promise<AISecret[]> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
    const { query, queryParams } = makeFullResultSetQuery({
      authLookup,
      permissionInfo: "org-membership",
      priorObjectTables: ["ai_secret", "organization"],
      filters: {
        org_name,
      },
    });
    const supabase = getServiceRoleSupabase();
    const { rows } = await otelTraced("run sql query", () =>
      supabase.query(query, queryParams.params),
    );
    return rows.map(sanitizeAISecret);
  },
);
