import { type TransactionId } from "braintrust/util";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type RefObject, useMemo } from "react";
import { usePreventNavigation } from "#/utils/use-prevent-navigation";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";

export const FunctionDetailConfirmSaveDialog = ({
  isDirty,
  getOutputPrompt,
  upsert,
  isSlugTouchedRef,
}: {
  isDirty: boolean;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  upsert: (
    prompt: UIFunction,
    updateSlug: boolean,
  ) => Promise<TransactionId | null>;
  isSlugTouchedRef: RefObject<boolean>;
}) => {
  const {
    showNavigationConfirmation,
    handleConfirmNavigation,
    handleCancelNavigation,
  } = usePreventNavigation(isDirty);
  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  return (
    <ConfirmationDialog
      open={showNavigationConfirmation}
      onOpenChange={() => handleCancelNavigation()}
      title="Unsaved changes"
      description="You have unsaved changes. Are you sure you want to leave?"
      confirmText="Leave"
      confirmSecondaryText="Save and leave"
      onConfirm={handleConfirmNavigation}
      onConfirmSecondary={async () => {
        const outputPrompt = getOutputPrompt(prompt);
        const result = await upsert(outputPrompt, isSlugTouchedRef.current);
        // Only navigate if the upsert was successful
        if (result !== null) {
          handleConfirmNavigation();
        }
      }}
    />
  );
};
