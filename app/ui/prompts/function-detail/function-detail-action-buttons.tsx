import { CopyAIProxyCode } from "#/app/app/[org]/p/[project]/prompts/copy-ai-proxy-code";
import { <PERSON><PERSON> } from "#/ui/button";
import { type FunctionObjectType } from "@braintrust/typespecs";
import { Alert<PERSON>riangle, Code, Copy, Trash2 } from "lucide-react";
import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  useMemo,
  useState,
} from "react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { type CreateUpdateMode } from "#/ui/prompts/function-editor/types";
import { CreatePlaygroundButton } from "#/ui/prompts/function-editor/create-playground";
import { FunctionEditorSubmitButton } from "#/ui/prompts/function-editor/function-editor-submit-button";
import { BasicTooltip } from "#/ui/tooltip";
import { type RunData } from "#/ui/prompts/chat/prompt-chat";

export function FunctionDetailActionButtons({
  mode,
  type,
  getOutputPrompt,
  isSlugTouchedRef,
  setIsDirty,
  error,
  setError,
  hasLintErrors,
  initialDirtyFunctionComparisonBase,
  orgName,
  projectId,
  projectName,
  promptName,
  dataEditorValue,
  extraMessagesPath,
  children,
  savedPromptVersion,
}: {
  mode: CreateUpdateMode;
  type: FunctionObjectType;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  isSlugTouchedRef: RefObject<boolean>;
  setIsDirty: Dispatch<SetStateAction<boolean>>;
  error: string | null;
  setError: Dispatch<SetStateAction<string | null>>;
  hasLintErrors: boolean;
  initialDirtyFunctionComparisonBase: UIFunction | null;
  orgName: string;
  projectId: string;
  projectName: string;
  promptName?: string;
  dataEditorValue: RunData;
  extraMessagesPath?: string;
  children: React.ReactNode;
  savedPromptVersion?: string;
}) {
  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );
  const [updating, setUpdating] = useState(false);

  return (
    <div className="flex items-center gap-1">
      {error && (
        <div className="flex items-center gap-1.5 px-2 text-xs font-medium text-bad-700">
          <AlertTriangle className="size-3 flex-none" />
          {error}
        </div>
      )}
      {children}
      {mode.type !== "create" && (
        <CreatePlaygroundButton
          orgName={orgName}
          projectName={projectName}
          type={type}
          datasetRows={
            Object.keys(dataEditorValue).length > 0
              ? [dataEditorValue]
              : undefined
          }
          prompt={prompt}
          promptName={promptName}
          projectId={projectId}
          mode={mode}
          getOutputPrompt={getOutputPrompt}
          extraMessagesPath={extraMessagesPath}
          savedPromptVersion={savedPromptVersion}
        />
      )}
      {type === "prompt" && prompt.prompt_data && (
        <CopyAIProxyCode promptData={prompt.prompt_data}>
          <Button size="xs" variant="ghost" Icon={Code} />
        </CopyAIProxyCode>
      )}
      {mode.type === "update" && !!mode.onDuplicate && (
        <BasicTooltip tooltipContent="Duplicate">
          <Button
            size="xs"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              const outputPrompt = getOutputPrompt(prompt);
              mode.onDuplicate?.(outputPrompt);
            }}
            Icon={Copy}
          />
        </BasicTooltip>
      )}
      {mode.type === "update" && !!mode.onDelete && (
        <BasicTooltip tooltipContent="Delete">
          <Button
            Icon={Trash2}
            size="xs"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              mode.onDelete?.();
            }}
            disabled={updating}
          />
        </BasicTooltip>
      )}

      <FunctionEditorSubmitButton
        className="ml-1"
        mode={mode}
        type={type}
        getOutputPrompt={getOutputPrompt}
        prompt={prompt}
        isSlugTouchedRef={isSlugTouchedRef}
        setError={setError}
        upsert={mode.upsert}
        hasLintErrors={hasLintErrors}
        initialDirtyFunctionComparisonBase={initialDirtyFunctionComparisonBase}
        updating={updating}
        setUpdating={setUpdating}
        setIsDirty={setIsDirty}
        orgName={orgName}
        projectName={projectName}
      />
    </div>
  );
}
