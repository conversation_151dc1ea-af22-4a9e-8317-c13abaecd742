import { useParquetView } from "#/utils/duckdb";
import {
  type Dispatch,
  type SetStateAction,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { useMutableObject } from "#/utils/mutable-object";
import { type DataObjectSearch } from "#/utils/btapi/btapi";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

import { useLoadPromptScan } from "./function-editor/use-load-prompt-scan";
import { type SavingState } from "#/ui/saving";
import { useAtom } from "jotai";
import { draftFunctionAtom } from "./atoms";

export function useFunction({
  objectType,
  functionId,
  setSavingState,
  promptVersion,
}: {
  objectType: "project_prompts" | "project_functions";
  functionId: string | null;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
  promptVersion: string | null;
}) {
  const { projectId } = useContext(ProjectContext);
  const [draftFunction, setDraftFunction] = useAtom(draftFunctionAtom);

  const {
    refreshed: promptsReady,
    schema: promptsSchema,
    scan: promptsScan,
    channel: promptsChannel,
    error,
  } = useParquetView({
    objectType,
    search: projectId ?? undefined,
  });

  const versionedPromptSearch = useMemo(() => {
    const id = projectId;
    if (!id || !promptVersion || functionId === "new") {
      return undefined;
    }
    const ret: DataObjectSearch = {
      id,
      version: promptVersion,
      filters: {
        sql: [
          {
            type: "path_lookup",
            path: ["id"],
            value: functionId,
          },
        ],
        btql: [
          {
            op: "eq",
            left: { op: "ident", name: ["id"] },
            right: { op: "literal", value: functionId },
          },
        ],
      },
    };
    return ret;
  }, [projectId, promptVersion, functionId]);

  const { refreshed: versionedPromptReady, scan: versionedPromptScan } =
    useParquetView({
      objectType,
      search: versionedPromptSearch,
    });

  const { status, prompt } = useLoadPromptScan({
    rowId: functionId,
    scan: !!promptVersion ? versionedPromptScan : promptsScan,
    scanReady: !!promptVersion ? [versionedPromptReady] : [promptsReady],
    objectType,
  });

  const initialFunction =
    functionId === "new" ? (draftFunction ?? null) : prompt;

  const auditLogSearch = useMemo(
    () =>
      projectId
        ? {
            id: projectId,
            audit_log: true,
          }
        : undefined,
    [projectId],
  );

  const { refreshed: auditLogReady, scan: auditLogScan } = useParquetView({
    objectType,
    search: auditLogSearch,
  });

  const dml = useMutableObject({
    scan: promptsScan,
    objectType,
    channel: promptsChannel,
    setSavingState,
  });

  // Clear the draft function when the component unmounts
  useEffect(
    () => () => {
      setDraftFunction(undefined);
    },
    [setDraftFunction],
  );

  return {
    dml,
    initialFunction,
    status,
    prompt,
    promptsReady,
    promptsSchema,
    promptsScan,
    auditLogReady: [auditLogReady],
    auditLogScan,
    error,
  };
}
