import {
  getPromptAutocompletion,
  getPromptLinter,
  isEmptyPromptData,
} from "#/app/app/[org]/prompt/[prompt]/helpers";
import { usePromptTheme } from "#/app/app/[org]/prompt/[prompt]/highlight";
import { mustache } from "#/utils/mustache/mustache";
import { isObject } from "#/utils/object";
import {
  type PromptData,
  type FunctionId,
  type FunctionObjectType,
  type Message,
} from "@braintrust/typespecs";
import { type Diagnostic } from "@codemirror/lint";
import { type Extension } from "@codemirror/state";
import { useMemo } from "react";
import { type UIFunction } from "./schema";
import { TransactionIdField } from "@braintrust/local/query";

export function isDatasetReservedName(name: string) {
  return (
    name === "input" ||
    name === "expected" ||
    name === "metadata" ||
    name === "output"
  );
}

export type JSONStructure = Record<string, unknown>;

export type PromptExtensionsParams = {
  jsonStructure: JSONStructure | null;
  outputNames: string[];
  expandInputVariables?: boolean;
  disablePromptLinter?: boolean;
  onCreateDataset?: VoidFunction;
  onFixWithLoop?: (message: string) => void;
  onLint?: (editorId: string, diagnostics: Diagnostic[]) => void;
  getMissingVariableMessage?: (variableName: string) => string;
  /** Used by scorers in the playground. More explanation above `makePromptInputArg` in api-ts/src/proxy/call.ts */
  hoistReservedNames?: boolean;
};

export function usePromptExtensions({
  jsonStructure,
  outputNames,
  expandInputVariables,
  disablePromptLinter,
  onCreateDataset,
  onFixWithLoop,
  onLint,
  getMissingVariableMessage,
  hoistReservedNames = false,
}: PromptExtensionsParams) {
  const promptTheme = usePromptTheme();

  const argStructure = useMemo<Record<string, unknown> | null>(() => {
    if (!jsonStructure) {
      return null;
    }
    return Object.fromEntries([
      ...(jsonStructure
        ? Object.entries(jsonStructure).flatMap(([k, v]) => {
            // Double input variables in global namespace
            if (k === "input" && isObject(v) && expandInputVariables) {
              return [
                [k, v],
                ...Object.entries(v).filter(([key]) =>
                  hoistReservedNames ? true : !isDatasetReservedName(key),
                ),
              ];
            }

            return [[k, v]];
          })
        : []),
      ...outputNames.map((n) => [n, {}]),
    ]);
  }, [expandInputVariables, jsonStructure, outputNames, hoistReservedNames]);

  const extensions = useMemo<Extension[]>(() => {
    return [promptTheme, mustache()].concat(
      argStructure
        ? !disablePromptLinter
          ? [
              getPromptLinter({
                argStructure,
                onCreateDataset,
                onLint,
                getMissingVariableMessage,
                onFixWithLoop,
              }),
              getPromptAutocompletion(argStructure),
            ]
          : [getPromptAutocompletion(argStructure)]
        : [],
    );
  }, [
    promptTheme,
    argStructure,
    disablePromptLinter,
    onCreateDataset,
    onLint,
    getMissingVariableMessage,
    onFixWithLoop,
  ]);

  return {
    extensions,
    argStructure,
  };
}

export function convertJSONSchemaToStructure(
  schema: string | Record<string, unknown> | undefined,
): Record<string, unknown> {
  if (schema == undefined) {
    return {};
  }
  if (typeof schema === "string") {
    return { input: "VARCHAR" };
  }
  return { input: convertType(schema) };
}

function convertType(
  schema: Record<string, unknown>,
): string | Record<string, unknown> | unknown[] {
  const type = Array.isArray(schema.type) ? schema.type[0] : schema.type;

  switch (type) {
    case "object":
      if (!schema.properties) {
        return "VARCHAR"; // Object without properties becomes VARCHAR
      }

      const result: Record<string, unknown> = {};
      for (const [propertyName, propertySchema] of Object.entries(
        schema.properties,
      )) {
        result[propertyName] = convertType(propertySchema);
      }
      return result;

    case "array":
      if (!schema.items) {
        return "VARCHAR"; // Array without items becomes VARCHAR
      }
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      return [convertType(schema.items as Record<string, unknown>)];

    case "null":
      return "NULL";

    case "string":
    case "number":
    case "integer":
    case "boolean":
    default:
      return "VARCHAR";
  }
}

export function functionObjectTypeToFunctionType(
  functionObjectType: FunctionObjectType,
): UIFunction["function_type"] {
  switch (functionObjectType) {
    case "prompt":
    case "agent":
      return null;
    case "tool":
      return "tool";
    case "scorer":
      return "scorer";
    case "task":
      return "task";
    default:
      const _unreachable: never = functionObjectType;
      throw new Error(`Unreachable: ${_unreachable}`);
  }
}

export function makePlaygroundTaskFunctionId(
  func: Partial<UIFunction> & { _xact_id: string; id: string },
  promptSessionId: string,
  useInlinePrompts: boolean,
  validToolIds: Set<string>,
): FunctionId {
  if (!(useInlinePrompts || func.function_data?.type === "remote_eval")) {
    return {
      version: func[TransactionIdField],
      prompt_session_function_id: func.id,
      prompt_session_id: promptSessionId,
    };
  }

  if (
    func?.function_data?.type === "code" &&
    func.function_data.data.type === "bundle"
  ) {
    return {
      function_id: func.prompt_data?.origin?.prompt_id ?? "",
      version: func.prompt_data?.origin?.prompt_version,
    };
  }

  if (
    func?.function_data?.type === "code" &&
    func.function_data.data.type === "inline"
  ) {
    return {
      inline_context: func.function_data.data.runtime_context,
      code: func.function_data.data.code,
    };
  }

  if (func?.function_data?.type === "global") {
    return {
      global_function: func?.function_data.name,
    };
  }

  return {
    inline_prompt: {
      ...(func.prompt_data ?? {}),
      tool_functions: (func.prompt_data?.tool_functions ?? []).filter(
        (t) => t.type !== "function" || validToolIds.has(t.id),
      ),
    },
    inline_function: func.function_data ?? {},
    prompt_session_id: promptSessionId,
    function_type: func.function_type ?? undefined,
  };
}

export function makeFunctionEditorFunctionId(
  prompt: UIFunction,
  isUpdate: boolean,
): FunctionId | null {
  if (
    prompt.function_data.type === "prompt" &&
    prompt.prompt_data &&
    !isEmptyPromptData(prompt.prompt_data?.prompt)
  ) {
    return makeInlinePromptFunction(prompt.prompt_data, "Inline prompt");
  } else if (prompt.function_data.type === "global") {
    return {
      global_function: prompt.function_data.name,
    };
  } else if (
    prompt.function_data.type === "code" &&
    prompt.function_data.data.type === "inline"
  ) {
    return {
      inline_context: prompt.function_data.data.runtime_context,
      code: prompt.function_data.data.code,
    };
  } else if (prompt[TransactionIdField] !== "0" && isUpdate) {
    return {
      function_id: prompt.id,
      version: prompt[TransactionIdField],
    };
  } else {
    return null;
  }
}

export function makeInlinePromptFunction(
  prompt: PromptData,
  name: string | null | undefined,
): FunctionId {
  return {
    inline_prompt: {
      prompt: prompt.prompt,
      options: {
        params: prompt.options?.params,
        model: prompt.options?.model,
      },
      parser: prompt.parser,
      tool_functions: prompt.tool_functions,
    },
    name: name ?? undefined,
  };
}

export function extractExtraPromptMessages(
  llmSpanPromptData: PromptData,
  savedFunction: UIFunction,
): Message[] {
  if (
    !(
      llmSpanPromptData.prompt?.type === "chat" &&
      savedFunction.function_data.type === "prompt" &&
      savedFunction.prompt_data?.prompt?.type === "chat"
    )
  ) {
    return [];
  }
  const llmSpanPromptDataMessages = llmSpanPromptData.prompt?.messages;
  const savedMessages = savedFunction.prompt_data?.prompt?.messages;
  return [...llmSpanPromptDataMessages.slice(savedMessages.length)];
}
