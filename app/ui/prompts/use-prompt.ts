import { usePromptSession } from "#/ui/prompts/use-prompt-session";
import { useContext, useMemo } from "react";
import {
  createOrUpdatePrompt,
  promptSchema,
  type UIFunction,
} from "#/ui/prompts/schema";
import { useFunctions } from "#/ui/functions/use-functions";
import { type Expr } from "@braintrust/btql/parser";
import { useLoadPromptScan } from "./function-editor/use-load-prompt-scan";
import { useOrg } from "#/utils/user";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";

export function usePrompt({
  promptInfoFromSpan,
  originVersionOverride,
}: {
  promptInfoFromSpan?: {
    id?: string | null | undefined;
    project_id?: string | null | undefined;
    prompt_session_id?: string | null | undefined;
    version?: string | null | undefined;
  } | null;
  originVersionOverride?: string | null;
}) {
  const {
    objects,
    loading: promptSessionLoading,
    error: promptSessionError,
  } = usePromptSession({
    promptSessionId: promptInfoFromSpan?.prompt_session_id ?? undefined,
    id: promptInfoFromSpan?.id ?? undefined,
    version: promptInfoFromSpan?.version ?? undefined,
  });

  const promptSessionPrompt = useMemo(() => {
    const promptSessionPrompt = promptSchema.safeParse(objects?.[0]);
    if (promptSessionPrompt.success) {
      return promptSessionPrompt.data;
    }
  }, [objects]);

  const promptSearchParams = useMemo(() => {
    if (!promptInfoFromSpan?.prompt_session_id) {
      return {
        promptId: promptInfoFromSpan?.id,
        promptVersion: promptInfoFromSpan?.version,
      };
    }
    return {
      promptId: promptSessionPrompt?.prompt_data?.origin?.prompt_id,
      promptVersion: promptSessionPrompt?.prompt_data?.origin?.prompt_version,
    };
  }, [
    promptInfoFromSpan?.prompt_session_id,
    promptInfoFromSpan?.id,
    promptInfoFromSpan?.version,
    promptSessionPrompt?.prompt_data?.origin?.prompt_id,
    promptSessionPrompt?.prompt_data?.origin?.prompt_version,
  ]);

  const promptSearch:
    | {
        filters: Expr[];
        version?: string;
        projectId?: string;
      }
    | undefined = useMemo(
    () =>
      promptSearchParams.promptId
        ? {
            filters: [
              {
                op: "eq",
                left: { op: "ident", name: ["id"] },
                right: { op: "literal", value: promptSearchParams.promptId },
              },
            ],
            version:
              originVersionOverride ??
              promptSearchParams.promptVersion ??
              undefined,
            projectId: promptInfoFromSpan?.project_id ?? undefined,
          }
        : undefined,
    [
      originVersionOverride,
      promptInfoFromSpan?.project_id,
      promptSearchParams.promptId,
      promptSearchParams.promptVersion,
    ],
  );

  const {
    scan: promptScan,
    signals: promptSignals,
    status: promptStatus,
    auditLogScan,
    auditLogSignals,
    dml,
  } = useFunctions({
    functionObjectType: "prompt",
    requireFilters: true,
    ...promptSearch,
  });

  const {
    status: projectPromptStatus,
    prompt: projectPrompt,
    error: projectPromptError,
  } = useLoadPromptScan({
    rowId: promptSearchParams?.promptId ?? null,
    scan: promptScan,
    scanReady: promptSignals,
    objectType: "project_prompts",
    shouldSkip: promptSearch == null,
  });

  const hasOriginVersionOverride = !!originVersionOverride;
  const status = useMemo(() => {
    // If the version has been overridden, we're toggling through projectPrompt versions,
    // so don't display loading status while we're showing placeholder data
    if (projectPromptStatus === "placeholder" && hasOriginVersionOverride) {
      return "placeholder";
    }

    const promptScanLoading = !!promptSearch && promptSignals?.[0] === 0;
    const promptLoading = projectPromptStatus === "loading";
    if (promptSessionError || promptStatus === "error" || projectPromptError) {
      return "error";
    }
    if (promptSessionLoading || promptScanLoading || promptLoading) {
      return "loading";
    }
    if (
      (promptSessionPrompt?.prompt_data &&
        !promptSessionPrompt?.prompt_data.origin) ||
      (promptSessionPrompt?.prompt_data?.origin &&
        Object.keys(promptSessionPrompt?.prompt_data.origin).length === 0)
    ) {
      return "unsaved_prompt";
    }
    if (
      promptInfoFromSpan?.prompt_session_id &&
      !promptSessionPrompt?.prompt_data
    ) {
      return "prompt_not_found";
    }
    return "loaded";
  }, [
    hasOriginVersionOverride,
    promptSearch,
    promptSignals,
    projectPromptStatus,
    promptSessionError,
    promptStatus,
    projectPromptError,
    promptSessionLoading,
    promptSessionPrompt?.prompt_data,
    promptInfoFromSpan?.prompt_session_id,
  ]);

  const prompt = useMemo(() => {
    if (promptSessionPrompt && projectPrompt && !originVersionOverride) {
      // TODO: should we have some sort of UI indication that the prompt has diverged from its origin version but is unsaved?
      // If both are present and a version hasn't been explicitly selected, override the saved prompt data with the prompt session prompt data.
      // This ensure that if a saved prompt was run from the playground after being edited but not saved, the latest edits are shown.
      return { ...projectPrompt, prompt_data: promptSessionPrompt.prompt_data };
    }
    return !!projectPrompt ? projectPrompt : promptSessionPrompt;
  }, [promptSessionPrompt, projectPrompt, originVersionOverride]);

  const { id: orgId } = useOrg();
  const { projectName } = useContext(ProjectContext);

  const createPrompt = async (
    promptToSave: UIFunction,
    updateSlug: boolean,
  ) => {
    if (!orgId) {
      toast.error("Unauthorized");
      return null;
    }
    if (!projectName) {
      toast.error("No project selected");
      return null;
    }

    return createOrUpdatePrompt({
      prompt: promptToSave,
      orgId: orgId,
      dml,
      update: !!prompt?.name,
      updateSlug: updateSlug,
    });
  };

  return {
    createPrompt,
    prompt,
    status,
    auditLogScan,
    auditLogSignals,
  };
}
