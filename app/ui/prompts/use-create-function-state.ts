import { type TransactionId } from "#/utils/duckdb";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useState,
} from "react";
import { type UIFunction, newPrompt } from "./schema";
import { type CopilotContextBuilder } from "#/ui/copilot/context";

export interface CreateFunctionState {
  prompt: UIFunction;
  setPrompt: Dispatch<SetStateAction<UIFunction>>;
  promptVersion: TransactionId | null;
  setPromptVersion: Dispatch<SetStateAction<TransactionId | null>>;
  error: string | null;
  setError: Dispatch<SetStateAction<string | null>>;
  reset: (transform?: (draft: UIFunction) => UIFunction) => void;
}

export function useCreateFunctionState({
  projectId,
  promptId,
  copilotContext,
  functionType,
  initialPromptVersion,
}: {
  projectId: string;
  promptId?: string;
  copilotContext?: CopilotContextBuilder;
  functionType: UIFunction["function_type"];
  initialPromptVersion?: string;
}): CreateFunctionState {
  const newPromptWrapper = useCallback(
    (transform?: (draft: UIFunction) => UIFunction) => {
      let p: UIFunction = {
        ...newPrompt(projectId, promptId),
        function_type: functionType,
      };
      if (p.function_data.type === "prompt" && functionType === "scorer") {
        p.prompt_data = {
          ...p.prompt_data,
          parser: {
            type: "llm_classifier",
            use_cot: true,
            choice_scores: {},
          },
        };
      }
      if (transform) {
        p = transform(p);
      }
      return p;
    },
    [functionType, projectId, promptId],
  );

  // _once_ from the database here (vs. other places where we optimistically update) each time
  // the database prompt changes.
  const [prompt, _setPrompt] = useState<UIFunction>(newPromptWrapper());

  const setPrompt: Dispatch<SetStateAction<UIFunction>> = useCallback(
    (p: UIFunction | ((prevState: UIFunction) => UIFunction)) => {
      if (typeof p === "function") {
        _setPrompt((currentPrompt) => {
          const newPrompt = p(currentPrompt);
          copilotContext?.visitPrompt(newPrompt);
          return newPrompt;
        });
      } else {
        copilotContext?.visitPrompt(p);
        _setPrompt(p);
      }
    },
    [copilotContext, _setPrompt],
  );

  const [promptVersion, setPromptVersion] = useState<TransactionId | null>(
    initialPromptVersion ?? null,
  );
  const [error, setError] = useState<string | null>(null);

  const reset = useCallback(
    (transform?: (draft: UIFunction) => UIFunction) => {
      setPrompt(newPromptWrapper(transform));
      setPromptVersion(null);
      setError(null);
    },
    [newPromptWrapper, setPrompt],
  );

  return {
    prompt,
    setPrompt,
    promptVersion,
    setPromptVersion,
    error,
    setError,
    reset,
  };
}
