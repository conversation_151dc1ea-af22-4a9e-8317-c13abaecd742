import { z } from "zod";

export type InsertSlugOutput = {
  prompt_id: string;
  found_existing: boolean;
};

export async function insertSlug({
  orgId,
  projectId,
  promptId,
  slug,
}: {
  orgId: string;
  projectId: string;
  promptId: string;
  slug: string;
}): Promise<InsertSlugOutput> {
  const resp = await fetch(`/api/prompt/register`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      org_id: orgId,
      project_id: projectId,
      slug,
      prompt_id: promptId,
    }),
  });
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
  const respBody = await resp.json();
  return {
    prompt_id: z.string().parse(respBody["prompt"]["id"]),
    found_existing: z.boolean().parse(respBody["found_existing"]),
  };
}

export async function updateSlug({
  promptId,
  slug,
}: {
  promptId: string;
  slug: string;
}): Promise<void> {
  const resp = await fetch(`/api/prompt/patch_id`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      id: promptId,
      slug,
    }),
  });
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
}

export async function deletePrompt({ id }: { id: string }): Promise<void> {
  const resp = await fetch(`/api/prompt/delete_id`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      id,
    }),
  });
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
}
