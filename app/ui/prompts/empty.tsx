import { getOrgLink } from "#/app/app/[org]/getOrgLink";
import { Plus, Sparkle } from "lucide-react";
import { BlueLink } from "#/ui/link";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { Anthropic, Gemini, Mistral, OpenAI } from "#/ui/icons/providers";
import { cn } from "#/utils/classnames";
import Link from "next/link";

export function NoAISecrets({ orgName }: { orgName: string }) {
  return (
    <>
      No AI provider API secrets have been configured yet
      <BlueLink
        className="block font-medium"
        href={`${getOrgLink({ orgName })}/settings/secrets`}
      >
        Configure AI providers
      </BlueLink>
    </>
  );
}

export function AIProviderLogoStack({
  iconClassName,
  iconSize = 24,
  providerCount = 4,
}: {
  iconClassName?: string;
  iconSize?: number;
  providerCount?: number;
}) {
  const providers = [OpenAI, Anthropic, Gemini, Mistral].slice(
    0,
    providerCount,
  );

  const className = cn(
    "-mr-2 flex size-8 items-center justify-center rounded-full border bg-background",
    iconClassName,
  );
  return (
    <div className="flex">
      {providers.map((Provider, i) => (
        <div className={className} key={i}>
          <Provider size={iconSize} />
        </div>
      ))}
      <div className={cn(className, "mr-0 bg-primary-50 text-primary-500")}>
        <Plus size={iconSize * 0.75} />
      </div>
    </div>
  );
}

export function NoAISecretsEmptyState({ orgName }: { orgName: string }) {
  return (
    <TableEmptyState Icon={Sparkle} label={<NoAISecrets orgName={orgName} />}>
      <AIProviderLogoStack />
    </TableEmptyState>
  );
}

export function NoAISecretsLink({
  orgName,
  className,
  suffix,
}: {
  orgName: string;
  className?: string;
  suffix?: string;
}) {
  return (
    <Link
      href={`${getOrgLink({ orgName })}/settings/secrets`}
      className={className}
    >
      <AIProviderLogoStack
        iconSize={14}
        iconClassName="size-5 -mr-1.5"
        providerCount={4}
      />
      <span className="text-xs">Add AI providers {suffix ? suffix : ""}</span>
    </Link>
  );
}
