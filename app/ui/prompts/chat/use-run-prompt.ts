import { invoke } from "#/app/app/[org]/prompt/[prompt]/scorers/invoke";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { BraintrustStream } from "braintrust";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useState,
} from "react";
import { toast } from "sonner";
import { type PromptData } from "#/ui/prompts/schema";
import {
  type InvokeFunctionRequest,
  type chatCompletionMessageParamSchema,
} from "@braintrust/typespecs";
import z from "zod";
import { parallelToolCallSchema } from "@braintrust/local/functions";

export type ChatCompletionMessage = z.infer<
  typeof chatCompletionMessageParamSchema
>;

export type StreamChunkData = {
  previewText: string;
  textChunks: string[];
  pendingTools: Set<string>;
  toolCalls?: Array<{ id: string; name: string; args: unknown }>;
  toolResults: Map<string, unknown>;
};

export function useRunPrompt({
  promptData,
  runData,
  onStreamChunk,
  onStreamDone,
  onError,
  setToolDefinitions,
  extraMessages,
  parent,
}: {
  promptData?: PromptData;
  runData?: {
    input?: unknown;
    expected?: unknown;
    metadata?: Record<string, unknown>;
  } | null;
  onStreamChunk: (data: StreamChunkData) => void;
  onStreamDone?: (messages: ChatCompletionMessage[]) => void;
  onError?: (error: string) => void;
  setToolDefinitions?: Dispatch<SetStateAction<Map<string, string>>>;
  extraMessages?: ChatCompletionMessage[];
  parent?: InvokeFunctionRequest["parent"];
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const [isGenerating, setIsGenerating] = useState(false);

  const runPrompt = useCallback(
    async (runTimeMessages?: ChatCompletionMessage[]) => {
      if (!promptData) {
        toast.error("Prompt not loaded");
        return;
      }

      setIsGenerating(true);
      let result: Response | null = null;

      const promptDataMessages =
        promptData.prompt?.type === "chat"
          ? promptData.prompt.messages
          : [
              {
                content: promptData.prompt?.content ?? "",
                role: "assistant" as const,
              },
            ];
      // Include extra messages at the end of the prompt but before the run time messages, rather than passing them as `messages`. This matches the UI in chat mode.
      const additionalMessages = (extraMessages ?? []).concat(
        runTimeMessages ?? [],
      );
      const inlinePrompt =
        additionalMessages.length > 0
          ? {
              ...promptData,
              prompt: {
                ...promptData.prompt,
                type: "chat" as const,
                messages: promptDataMessages.concat(additionalMessages),
              },
            }
          : promptData;

      try {
        result = await invoke({
          orgName: org.name,
          sessionToken: await getOrRefreshToken(),
          proxyUrl: org.proxy_url,
          functionId: {
            inline_prompt: inlinePrompt,
          },
          input: runData?.input,
          expected: runData?.expected,
          metadata: runData?.metadata,
          stream: true,
          parent,
        });
      } catch (e) {
        console.error(e);
        const errorMessage = e instanceof Error ? e.message : String(e);
        toast.error("Failed to run prompt", {
          description: errorMessage,
        });
        onError?.(errorMessage);
        setIsGenerating(false);
        return;
      }

      if (!result) {
        console.warn("No result");
        setIsGenerating(false);
        return;
      }

      const body = result.body;
      if (!body) {
        toast.error("No response body");
        setIsGenerating(false);
        return;
      }

      await processResultStream({
        stream: body,
        onStreamChunk,
        onStreamDone,
        setToolDefinitions,
      });
      setIsGenerating(false);
    },
    [
      promptData,
      onStreamChunk,
      onStreamDone,
      org.name,
      org.proxy_url,
      getOrRefreshToken,
      runData?.input,
      runData?.expected,
      runData?.metadata,
      setToolDefinitions,
      extraMessages,
      onError,
      parent,
    ],
  );

  return {
    runPrompt,
    isGenerating,
  };
}

async function processResultStream({
  stream: rawStream,
  onStreamChunk,
  onStreamDone,
  setToolDefinitions,
}: {
  stream: ReadableStream<Uint8Array>;
  onStreamChunk: (data: StreamChunkData) => void;
  onStreamDone?: (messages: ChatCompletionMessage[]) => void;
  setToolDefinitions?: Dispatch<SetStateAction<Map<string, string>>>;
}) {
  const textChunks: string[] = [];
  const jsonChunks: string[] = [""];
  const errors: string[] = [];
  const pendingTools = new Set<string>();
  /** tool_name -> tool_call_id. */
  const toolNameToCallId = new Map<string, string>();
  /** tool_call_id -> result. */
  const toolResults = new Map<string, unknown>();
  /** tool_call_id -> call data. */
  const completedToolCalls = new Map<string, { name: string; args: unknown }>();

  let previewText = "";

  const stream = new BraintrustStream(rawStream);
  const reader = stream.toReadableStream().getReader();
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    switch (value.type) {
      case "text_delta":
        if (
          value.data.length === 0 ||
          textChunks[textChunks.length - 1] == null
        ) {
          textChunks.push(value.data);
        } else {
          textChunks[textChunks.length - 1] += value.data;
        }

        previewText += value.data;
        break;
      case "json_delta":
        jsonChunks.push(value.data);
        previewText += value.data;
        break;
      case "progress":
        switch (value.data.event) {
          case "json_delta":
            jsonChunks[jsonChunks.length - 1] += value.data.data;
            const currentChunk = jsonChunks[jsonChunks.length - 1];

            // Handle prompt tool calls
            if (value.data.object_type === "prompt" && currentChunk.trim()) {
              try {
                const parsed = JSON.parse(currentChunk);
                const safeParsed = z
                  .array(parallelToolCallSchema)
                  .safeParse(parsed);
                if (safeParsed.success && safeParsed.data.length > 0) {
                  safeParsed.data.forEach((toolCall) => {
                    completedToolCalls.set(toolCall.tool_call_id, {
                      name: toolCall.function_name,
                      args: toolCall.arguments,
                    });
                    pendingTools.add(toolCall.function_name);
                    toolNameToCallId.set(
                      toolCall.function_name,
                      toolCall.tool_call_id,
                    );
                  });
                }
              } catch (e) {
                // Not valid JSON yet, continue
              }
            }

            // Handle tool results
            if (
              value.data.object_type === "tool" &&
              value.data.name &&
              value.data.data
            ) {
              try {
                const result = JSON.parse(value.data.data);
                const toolName = value.data.name.toLowerCase();
                const toolCallId = toolNameToCallId.get(toolName);

                if (toolCallId) {
                  toolResults.set(toolCallId, result);
                  pendingTools.delete(toolName);
                }
              } catch (e) {}
            }
            break;
          case "start":
            jsonChunks.push("");
            break;
          default:
          // Unknown progress event
        }
        break;
      case "error":
        errors.push("ERROR! " + value.data);
        break;
      case "done":
        break;
      default:
        console.warn("Unknown stream type", value);
    }

    onStreamChunk({
      previewText,
      textChunks,
      pendingTools,
      toolCalls: Array.from(completedToolCalls.entries()).map(([id, tool]) => ({
        id,
        name: tool.name,
        args: tool.args,
      })),
      toolResults,
    });
  }

  if (completedToolCalls.size > 0) {
    setToolDefinitions?.((prev) => {
      const newMap = new Map(prev);
      completedToolCalls.forEach((tool, toolCallId) => {
        newMap.set(toolCallId, tool.name);
      });
      return newMap;
    });
  }

  const messages: ChatCompletionMessage[] = [];

  // Add first assistant message, then any tool messages, then any additional assistant messages
  if (textChunks.length > 0) {
    const message: ChatCompletionMessage = {
      content: textChunks[0],
      role: "assistant" as const,
    };

    if (completedToolCalls.size > 0) {
      message.tool_calls = Array.from(completedToolCalls.entries()).map(
        ([id, tool]) => ({
          id,
          type: "function" as const,
          function: {
            name: tool.name,
            arguments: JSON.stringify(tool.args),
          },
        }),
      );
    }
    messages.push(message);
  } else {
    messages.push({
      content: jsonChunks.join(""),
      role: "assistant" as const,
    });
  }

  completedToolCalls.forEach((_tool, toolCallId) => {
    const result = toolResults.get(toolCallId);
    if (result !== undefined) {
      messages.push({
        role: "tool" as const,
        content: JSON.stringify(result),
        tool_call_id: toolCallId,
      });
    }
  });

  // Add additional assistant messages _after_ tool messages, which is required for continued prompt execution
  textChunks.slice(1).forEach((content) => {
    messages.push({
      content,
      role: "assistant" as const,
    });
  });

  onStreamDone?.(messages);
}
