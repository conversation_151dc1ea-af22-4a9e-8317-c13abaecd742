import { useContext, useMemo } from "react";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useParquetView } from "#/utils/duckdb";
import { useDBQuery } from "#/utils/duckdb";

export function useCustomScorerNames() {
  const { projectId } = useContext(ProjectContext);

  const { refreshed: promptsReady, scan: promptsScan } = useParquetView({
    objectType: "project_functions",
    search: projectId ?? undefined,
  });

  const { data: customScorerData, hasLoaded: promptDataHasLoaded } = useDBQuery(
    promptsScan
      ? `SELECT DISTINCT slug, name FROM (${promptsScan}) base WHERE function_type = 'scorer'`
      : null,
    [promptsReady],
  );

  const { customScorerNames } = useMemo(() => {
    const namesMap: Record<string, string> = {};
    for (const row of customScorerData ?? []) {
      namesMap[row.slug] = row.name;
    }
    return { customScorerNames: namesMap };
  }, [customScorerData]);

  return { customScorerNames, promptDataHasLoaded, promptsReady };
}
