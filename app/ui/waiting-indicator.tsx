import { cn } from "#/utils/classnames";
import { Spinner } from "./icons/spinner";

interface WaitingIndicatorProps {
  task: string;
  className?: string;
  spinnerClassName?: string;
}

export function WaitingIndicator({
  task,
  className,
  spinnerClassName = "size-5",
}: WaitingIndicatorProps) {
  return (
    <div
      className={cn(
        "-mb-1 flex items-center gap-2 rounded-full bg-accent-600 py-1 pr-3 pl-2 text-[15px] font-medium text-background",
        className,
      )}
    >
      <Spinner className={spinnerClassName} />
      <span className="animate-text-shimmer bg-linear-to-r from-accent-50 via-accent-300 to-accent-50 bg-clip-text text-transparent">
        Waiting for {task}
      </span>
    </div>
  );
}
