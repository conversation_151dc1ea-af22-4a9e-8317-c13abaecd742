import Link from "next/link";
import { cn } from "#/utils/classnames";

export default function TextLink({
  href,
  children,
  className,
  target,
}: {
  href: string;
  children: React.ReactNode;
  className?: string;
  target?: string;
}) {
  return (
    <Link
      href={href}
      className={cn("text-primary-800 hover:underline", className)}
      target={target}
    >
      {children}
    </Link>
  );
}
