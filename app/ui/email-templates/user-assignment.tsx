import { getAssignmentText } from "#/utils/email/email-utils";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  Html,
  Preview,
  Section,
  Text,
  Img,
  Link,
  Markdown,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface AssignmentProps {
  assignerName: string;
  projectName?: string;
  entityType?: string | null;
  entityName?: string | null;
  baseUrl?: string;
  link: string;
}

const DEFAULT_BASE_URL =
  process.env.NEXT_PUBLIC_SITE_URL ?? "https://braintrust.dev";

const AssignmentNotification = ({
  assignerName,
  projectName,
  baseUrl = DEFAULT_BASE_URL,
  entityType,
  entityName,
  link,
}: AssignmentProps) => {
  const assignedUrl = `${baseUrl}${link}`;

  return (
    <Html>
      <Head />
      <Preview>
        {getAssignmentText(
          false,
          entityName,
          projectName,
          assignerName,
          entityType,
        )}
      </Preview>
      <Tailwind>
        <Body className="m-auto p-4 font-sans">
          <Container className="max-w-[520px] bg-white pt-8">
            <Section>
              <Img
                src={`${baseUrl}/logo-letter.png`}
                width="100"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Markdown
              markdownContainerStyles={{
                paddingTop: "16px",
              }}
              markdownCustomStyles={{
                p: {
                  fontSize: "16px",
                  color: "#222222",
                },
              }}
            >
              {getAssignmentText(
                true,
                entityName,
                projectName,
                assignerName,
                entityType,
              )}
            </Markdown>
            <Section className="my-[24px]">
              <Button
                className="rounded-md bg-[#3b82f6] px-4 py-2.5 text-center text-[14px] font-medium text-white no-underline"
                href={assignedUrl}
              >
                View trace
              </Button>
            </Section>
            <div className="h-px bg-zinc-200/80" />
            <Text className="text-sm text-zinc-500">
              Or, copy and paste this URL into your browser:{" "}
              <Link
                href={assignedUrl}
                className="break-all text-blue-600 no-underline"
              >
                {assignedUrl}
              </Link>
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: AssignmentProps = {
  projectName: "Example Project",
  baseUrl: "https://braintrust.dev",
  assignerName: "Test User",
  entityType: "experiment",
  entityName: "bt experiment",
  link: "/org/example-org/projects/example-project/experiments/bt-experiment",
};

AssignmentNotification.PreviewProps = previewProps;

export default AssignmentNotification;
