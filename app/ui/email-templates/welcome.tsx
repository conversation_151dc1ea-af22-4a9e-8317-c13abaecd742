import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface WelcomeProps {
  baseUrl?: string;
}

const DEFAULT_BASE_URL =
  process.env.NEXT_PUBLIC_SITE_URL ?? "https://braintrustdata.com";

const Welcome = ({ baseUrl = DEFAULT_BASE_URL }: WelcomeProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        Welcome to Braintrust, the end-to-end platform for building AI
        applications.
      </Preview>
      <Tailwind>
        <Body className="mx-auto bg-white px-2 font-sans">
          <Container className="mx-auto max-w-[480px]">
            <Section className="mt-[32px]">
              <Img
                src={`${baseUrl}/icon192.png`}
                width="40"
                height="40"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Heading className="mx-0 my-5 p-0 text-3xl font-medium text-black">
              Welcome to Braintrust, the&nbsp;end-to-end platform for building
              AI&nbsp;applications.
            </Heading>
            <Text className="text-base">
              We&apos;re glad you&apos;re here :) We built Braintrust to help
              developers build and ship AI products with confidence, and
              I&apos;m excited to see what you create!
            </Text>
            <Text className="text-base">
              Here are a few helpful resources to help you get started
            </Text>
            <ul>
              <li className="text-base">
                <Link href={`${baseUrl}/docs/start`} className="text-blue-600">
                  Getting started docs
                </Link>
              </li>
              <li className="text-base">
                <Link href={`${baseUrl}/docs/guides`} className="text-blue-600">
                  Guides
                </Link>{" "}
                on evals, logging, prompt engineering and more
              </li>
              <li className="text-base">
                <Link
                  href={`${baseUrl}/docs/cookbook`}
                  className="text-blue-600"
                >
                  Cookbooks
                </Link>{" "}
                with sample code and use cases
              </li>
            </ul>
            <Text className="text-base">
              If you have any questions or feedback, our team is here to help!
              Join our{" "}
              <Link
                href="https://discord.gg/6G8s47F44X"
                className="text-blue-600"
              >
                Discord
              </Link>
              , or just reply to this email.
            </Text>
            <Text className="text-base">
              Ankur Goyal
              <br />
              Founder and CEO, Braintrust
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: WelcomeProps = {
  baseUrl: "https://braintrustdata.com",
};

Welcome.PreviewProps = previewProps;

export default Welcome;
