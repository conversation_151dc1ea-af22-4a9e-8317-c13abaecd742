import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  <PERSON>ing,
  Html,
  Preview,
  Section,
  Text,
  Img,
  Link,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface InviteUserProps {
  email: string;
  invitedByEmail?: string | null;
  organizationName?: string;
  baseUrl?: string;
}

const DEFAULT_BASE_URL =
  process.env.NEXT_PUBLIC_SITE_URL ?? "https://braintrust.dev";

const InviteUser = ({
  email,
  invitedByEmail,
  organizationName,
  baseUrl = DEFAULT_BASE_URL,
}: InviteUserProps) => {
  const params = new URLSearchParams({
    email_address: email,
  });

  if (organizationName) {
    params.set("redirect_url", `${baseUrl}/app/${organizationName}`);
  }

  const signUpUrl = `${baseUrl}/signup?${params.toString()}`;

  return (
    <Html>
      <Head />
      <Preview>{`Join ${organizationName} on Braintrust`}</Preview>
      <Tailwind>
        <Body className="m-auto p-4 font-sans">
          <Container className="max-w-[520px] bg-white pt-8">
            <Section>
              <Img
                src={`${baseUrl}/logo-letter.png`}
                width="100"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Heading className="mt-[32px] text-base font-normal text-black">
              You&apos;ve been invited to join{" "}
              <span className="font-medium">{organizationName}</span> on
              Braintrust by{" "}
              <Link
                href={`mailto:${invitedByEmail}`}
                className="text-blue-600 no-underline"
              >
                {invitedByEmail}
              </Link>
            </Heading>
            <Section className="my-[24px]">
              <Button
                className="rounded-md bg-[#3b82f6] px-4 py-2.5 text-center text-[14px] font-medium text-white no-underline"
                href={signUpUrl}
              >
                Join organization
              </Button>
            </Section>
            <div className="h-px bg-zinc-200/80" />
            <Text className="text-sm text-zinc-500">
              Or, copy and paste this URL into your browser:{" "}
              <Link
                href={signUpUrl}
                className="break-all text-blue-600 no-underline"
              >
                {signUpUrl}
              </Link>
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: InviteUserProps = {
  email: "<EMAIL>",
  invitedByEmail: "<EMAIL>",
  organizationName: "Example Org",
  baseUrl: "https://braintrust.dev",
};

InviteUser.PreviewProps = previewProps;

export default InviteUser;
