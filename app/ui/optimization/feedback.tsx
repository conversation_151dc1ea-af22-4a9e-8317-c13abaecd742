import { But<PERSON> } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { type ChatContext } from "@braintrust/local/optimization";
import { type Span } from "braintrust";
import { ThumbsUp, ThumbsDown, X, Clipboard, Ellipsis } from "lucide-react";
import { useState, useRef } from "react";
import TextArea from "#/ui/text-area";
import { useUser } from "#/utils/user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";

export const Feedback = ({
  sendFeedback,
  isActive,
}: {
  sendFeedback: ChatContext["recordFeedback"];
  isActive: boolean;
}) => {
  const [feedback, setFeedback] = useState<"good" | "bad" | null>(null);
  const [isSubmitted, setIsSubmitted] = useState<"good" | "bad" | null>(null);
  const [submissionSpan, setSubmissionSpan] = useState<Span | null>(null);
  const feedbackRef = useRef<HTMLTextAreaElement>(null);
  const { user } = useUser();

  const handleSubmit = async (
    type: "good" | "bad" | null,
    comment?: string,
  ) => {
    const span = await sendFeedback({
      score: type === "good" ? 1 : type === "bad" ? 0 : null,
      comment: comment,
      userId: user?.email ?? undefined,
      opts: submissionSpan ? { span: submissionSpan } : undefined,
    });
    setIsSubmitted(type);
    setSubmissionSpan(span);
  };

  if (!isActive) {
    return isSubmitted ? (
      <div className="group flex w-full items-center">
        <div className="flex items-center justify-center p-2">
          {feedback === "good" ? (
            <ThumbsUp className="size-3 text-good-600" />
          ) : (
            <ThumbsDown className="size-3 text-bad-600" />
          )}
        </div>
        <MiscButton submissionSpan={submissionSpan} />
      </div>
    ) : null;
  }

  return (
    <div className="flex w-full flex-col gap-2">
      <div className="flex items-center gap-0.5">
        <Button
          size="xs"
          variant="ghost"
          Icon={ThumbsUp}
          onClick={async () => {
            if (feedback === "good") {
              setFeedback(null);
              handleSubmit(null);
            } else {
              setFeedback("good");
              handleSubmit("good");
            }
          }}
          className={cn(
            "text-primary-500 hover:bg-primary-200",
            isSubmitted === "good" &&
              "bg-good-50 text-good-600 hover:bg-good-100 hover:text-good-600",
          )}
        />
        <Button
          size="xs"
          variant="ghost"
          Icon={ThumbsDown}
          onClick={async () => {
            if (feedback === "bad") {
              setFeedback(null);
              handleSubmit(null);
            } else {
              setFeedback("bad");
              handleSubmit("bad");
            }
          }}
          className={cn(
            "text-primary-500 hover:bg-primary-200",
            isSubmitted === "bad" &&
              "bg-bad-50/80 text-bad-600 hover:bg-bad-100 hover:text-bad-600",
          )}
        />
        {isSubmitted && <MiscButton submissionSpan={submissionSpan} />}
      </div>
      {feedback && (
        <div className="relative flex flex-col gap-2 rounded-md border bg-background p-2 text-xs text-primary-700">
          <span className="mb-1 font-medium">
            {feedback === "good"
              ? "What did you like about this response?"
              : "What could be improved?"}
          </span>
          <Button
            size="xs"
            variant="ghost"
            Icon={X}
            onClick={() => setFeedback(null)}
            className="absolute top-0.5 right-0.5 text-primary-500"
          />
          <TextArea
            placeholder={
              feedback === "good"
                ? "Enter feedback to help us improve (optional)"
                : "Enter what went wrong to help us improve (optional)"
            }
            className="min-h-15 text-xs"
            ref={feedbackRef}
            onMetaEnter={() => {
              setFeedback(null);
              handleSubmit(
                feedback,
                feedbackRef.current?.value &&
                  feedbackRef.current.value.length > 0
                  ? feedbackRef.current.value
                  : undefined,
              );
            }}
            autoFocus
          />
          <Button
            size="xs"
            variant="primary"
            className="w-fit"
            onClick={() => {
              setFeedback(null);
              handleSubmit(
                feedback,
                feedbackRef.current?.value &&
                  feedbackRef.current.value.length > 0
                  ? feedbackRef.current.value
                  : undefined,
              );
            }}
          >
            Submit
          </Button>
        </div>
      )}
    </div>
  );
};

const MiscButton = ({ submissionSpan }: { submissionSpan: Span | null }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="xs"
          variant="ghost"
          Icon={Ellipsis}
          className="text-primary-500"
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuItem
          onClick={() => {
            navigator.clipboard.writeText(submissionSpan?.id ?? "");
          }}
        >
          <Clipboard className="size-3" />
          Copy feedback ID
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
