import { type <PERSON>actN<PERSON>, type <PERSON><PERSON><PERSON> } from "react";
import { <PERSON><PERSON> } from "#/ui/button";
import { defaultUrlTransform } from "react-markdown";
import type { ExtraProps } from "react-markdown";
import type { AnchorHTMLAttributes } from "react";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import { ArrowUpRightIcon } from "lucide-react";

export const evalRowUrlTransform = (url: string): string => {
  if (url?.startsWith("eval-row:")) {
    return url;
  }
  return defaultUrlTransform(url);
};

interface ObjectRowLinkProps
  extends Omit<AnchorHTMLAttributes<HTMLAnchorElement>, "href">,
    ExtraProps {
  /**
   * href that react-markdown passes for links. For our custom `eval-row:` links
   * this will be `eval-row:<rowId>`.
   */
  href?: string;
  /**
   * The rendered children (link label).
   */
  children?: ReactNode;
  // `node` is already provided by ExtraProps from react-markdown
}

export function ObjectRowLink({
  href = "",
  children,
  className,
  node,
  ...rest
}: ObjectRowLinkProps) {
  const [, setActiveRowAndSpan] = useActiveRowAndSpan();

  const resolvedHref = href || (node?.properties?.href ?? "");

  // Check if this is an `eval-row:` link
  const protocolMatch = resolvedHref.toString().match(/^eval-row:(.+)$/);

  if (!protocolMatch) {
    // Not an eval-row link - render as normal link
    return (
      <a {...rest} href={href}>
        {children}
      </a>
    );
  }

  const rowId = protocolMatch[1];

  const handleClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const openInNewTab = e.metaKey || e.ctrlKey || e.button === 1;

    if (openInNewTab) {
      const pathname = window.location.pathname;
      const params = new URLSearchParams(window.location.search);
      params.set("r", rowId);
      window.open(`${pathname}?${params.toString()}`, "_blank");
      return;
    }

    // Same-tab: update the query param so existing side-panel logic opens the sheet
    setActiveRowAndSpan({ r: rowId, s: null });
  };
  if (!children) {
    return null;
  }

  return (
    <Button
      onClick={handleClick}
      variant="default"
      size="xs"
      className="h-fit bg-primary-200/80 px-1.5 py-[2px] text-xs text-primary-700 hover:bg-primary-300/80"
      title={`View evaluation row ${rowId}`}
      Icon={ArrowUpRightIcon}
      iconClassName="text-primary-500"
    >
      {children}
    </Button>
  );
}
