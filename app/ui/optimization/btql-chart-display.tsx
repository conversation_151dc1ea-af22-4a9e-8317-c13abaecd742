"use client";

import { type ParsedQuery } from "@braintrust/btql/parser";
import { TimeseriesChartFromAst } from "./btql-timeseries-chart";
import { CategoricalChartFromAst } from "./btql-categorical-chart";
import { BTQLSingleValueDisplay } from "./btql-single-value-display";
import { BTQLMonitorAdapter } from "./query-monitor-adapter";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { Button } from "#/ui/button";
import { Asterisk, Code2, Ellipsis, ListFilter } from "lucide-react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "#/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { memo, useMemo } from "react";
import { useOrg } from "#/utils/user";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "#/ui/tabs";
import { cn } from "#/utils/classnames";
import { format } from "date-fns";
import { parseDateString } from "#/ui/time-range-select/parse-date-string";
import { BasicTooltip } from "#/ui/tooltip";

interface BTQLChartDisplayProps {
  ast: ParsedQuery;
  chartKind: ChartKind;
  query: string;
  projectId?: string;
  explanation?: string;
  data?: Record<string, unknown>[];
  visualizationConfig?: {
    vizType?: "lines" | "bars";
    unitType?: "count" | "duration" | "percent" | "cost";
  };
  timeRangeFilter?: string | { from: string; to: string };
  onBTQLFilter?: (filterText: string) => void;
  onRunInSandbox?: (args: {
    query: string;
    title?: string;
    openNewTab?: boolean;
  }) => void;
  title?: string;
}

interface ChartRendererProps {
  ast: ParsedQuery;
  data: Record<string, unknown>[];
  projectId?: string;
  explanation?: string;
  visualizationConfig?: {
    vizType?: "lines" | "bars";
    unitType?: "count" | "duration" | "percent" | "cost";
  };
  timeRangeFilter?: string | { from: string; to: string };
  chartKind: ChartKind;
  maxCategories?: number;
}

type ChartKind = "timeseries" | "categorical" | "single-value" | "table";

function ReadOnlyTimeRange({
  value,
}: {
  value?: string | { from: string; to: string };
}) {
  if (!value) return null;

  if (typeof value === "string") {
    const v = value;
    const label = v === "all" ? "All time" : ` past ${v}`;
    return (
      <div className="w-fit rounded-[3px] bg-primary-200 px-1.5 py-px text-[11px] text-primary-600">
        {label}
      </div>
    );
  }

  const fromDate = parseDateString(value.from);
  const toDate = parseDateString(value.to);
  const durationMs = toDate.getTime() - fromDate.getTime();
  const showTime = durationMs <= 7 * 24 * 60 * 60 * 1000;
  const fmt = (d: Date) =>
    format(d, showTime ? "MMM d, yyyy HH:mm" : "MMM d, yyyy");
  const formattedFrom = fmt(fromDate);
  const formattedTo = fmt(toDate);

  return (
    <div className="flex items-center gap-1 text-xs text-primary-600">
      <span className="rounded-[3px] bg-primary-200 px-1 py-px text-[11px] text-primary-600">
        Range
      </span>
      <BasicTooltip tooltipContent={`${formattedFrom} - ${formattedTo}`}>
        <span className="max-w-[100px] truncate">
          {formattedFrom} - {formattedTo}
        </span>
      </BasicTooltip>
    </div>
  );
}

/**
 * Classify a BTQL query to determine the appropriate visualization type
 */
export function classifyQuery(ast: ParsedQuery): ChartKind {
  const dimensions = ast.dimensions || [];
  const measures = ast.measures || [];

  // Check for time-based dimensions
  const hasTimeDimension = dimensions.some((dim) => {
    if (dim.expr && "op" in dim.expr && dim.expr.op === "function") {
      const funcNameObj = dim.expr.name;
      if (funcNameObj && funcNameObj.name && Array.isArray(funcNameObj.name)) {
        const funcNameStr = funcNameObj.name[0]?.toString() || "";
        return ["minute", "hour", "day", "week", "month", "year"].includes(
          funcNameStr,
        );
      }
    }
    return false;
  });

  // Count non-time dimensions
  const nonTimeDimensions = dimensions.filter((dim) => {
    if (dim.expr && "op" in dim.expr && dim.expr.op === "function") {
      const funcNameObj = dim.expr.name;
      if (funcNameObj && funcNameObj.name && Array.isArray(funcNameObj.name)) {
        const funcNameStr = funcNameObj.name[0]?.toString() || "";
        return !["minute", "hour", "day", "week", "month", "year"].includes(
          funcNameStr,
        );
      }
    }
    return true;
  });

  // Heuristic to determine the chart type
  if (hasTimeDimension) {
    return "timeseries";
  } else if (nonTimeDimensions.length >= 1) {
    return "categorical";
  } else if (dimensions.length === 0 && measures.length >= 1) {
    return "single-value";
  } else {
    return "table";
  }
}

export function DataTable({ data }: { data: Record<string, unknown>[] }) {
  if (!data || data.length === 0) {
    return (
      <div className="-mt-1 rounded-md border bg-background px-2 py-1 text-xs text-primary-700">
        No data to display
      </div>
    );
  }

  const columns = Object.keys(data[0]);

  return (
    <div className="-mt-1 overflow-hidden rounded-md border bg-background">
      <Table containerClassName="max-h-[240px] no-scrollbar">
        <TableHeader className="sticky top-0 bg-primary-50">
          <TableRow>
            {columns.map((column) => (
              <TableHead
                key={column}
                className="h-fit flex-1 truncate px-2 py-1 text-left font-mono text-[11px] font-medium text-primary-600"
              >
                {column}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((row, rowIndex) => (
            <TableRow key={rowIndex} className="border-b hover:bg-primary-50">
              {columns.map((column) => (
                <TableCell key={column} className="flex-1 p-2 text-xs">
                  <span className="truncate">
                    {row[column] !== null && row[column] !== undefined
                      ? typeof row[column] === "object"
                        ? JSON.stringify(row[column])
                        : String(row[column])
                      : "-"}
                  </span>
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

/**
 * Component that handles the logic of which chart type to render
 */
export function BTQLChartRenderer({
  ast,
  data,
  projectId,
  explanation,
  visualizationConfig,
  timeRangeFilter,
  query,
  chartKind,
  maxCategories,
}: ChartRendererProps & { query: string }) {
  // Memoized to avoid unnecessary identity changes; include fields the adapter uses
  const monitorArgs = useMemo(
    () => ({
      query,
      explanation,
      visualizationConfig,
      data,
      timeRangeFilter,
      rowCount: data.length,
    }),
    [query, explanation, visualizationConfig, timeRangeFilter, data],
  );

  // visualizationConfig comes from LLM tool call -- this means LLM decided that monitor card is the right visualization. So we skip the results of the heuristics
  if (visualizationConfig) {
    return (
      <div className="flex min-h-[180px] flex-col pt-2">
        <BTQLMonitorAdapter
          monitorArgs={monitorArgs}
          projectId={projectId ?? ""}
        />
      </div>
    );
  }

  switch (chartKind) {
    case "timeseries":
      return (
        <div className="flex min-h-[180px] flex-col pt-2">
          <TimeseriesChartFromAst ast={ast} data={data} />
        </div>
      );

    case "categorical":
      return (
        <div className="flex min-h-[180px] flex-col pt-2">
          <CategoricalChartFromAst
            ast={ast}
            data={data}
            maxCategories={maxCategories}
          />
        </div>
      );

    case "single-value":
      return (
        <div className="flex flex-col pt-2">
          <BTQLSingleValueDisplay
            ast={ast}
            data={data}
            explanation={explanation}
          />
        </div>
      );

    case "table":
      return (
        <div className="flex flex-col pt-2">
          <DataTable data={data} />
        </div>
      );
    default:
      return null;
  }
}

export function BTQLChartDisplay({
  ast,
  chartKind,
  query,
  projectId,
  explanation,
  data,
  visualizationConfig,
  onBTQLFilter,
  onRunInSandbox,
  title,
  timeRangeFilter,
}: BTQLChartDisplayProps) {
  const router = useRouter();
  const { name: orgName } = useOrg();

  if (!ast || !data || data.length === 0) {
    return (
      <div className="flex h-fit items-center py-1 text-xs text-primary-700">
        {!ast ? "Unable to parse query" : "No relevant data found"}
      </div>
    );
  }

  const fullQuery = projectId
    ? `from: project_logs('${projectId}') | ${query}`
    : query;

  return (
    <Tabs
      defaultValue={chartKind === "table" ? "table" : "chart"}
      className="flex flex-col"
    >
      <div className="flex items-center justify-between gap-2">
        <TabsList className="size-fit p-0">
          <TabsTrigger
            value="chart"
            className={cn(
              "gap-1 rounded-md px-2 py-1.5 text-xs",
              chartKind === "table",
            )}
            disabled={chartKind === "table"}
          >
            Chart
          </TabsTrigger>
          <TabsTrigger
            value="table"
            className="gap-1 rounded-md px-2 py-1.5 text-xs"
          >
            Table
          </TabsTrigger>
        </TabsList>
        <div className="flex items-center gap-2">
          <ReadOnlyTimeRange value={timeRangeFilter} />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="xs"
                Icon={Ellipsis}
                className="text-primary-500 hover:bg-primary-200"
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Code2 className="size-3" /> View query
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent className="max-w-[320px] p-4">
                  <div className="mb-1 flex items-center justify-between text-xs text-primary-500">
                    Full BTQL query
                    <CopyToClipboardButton
                      textToCopy={query}
                      className="text-primary-400"
                      variant="ghost"
                      size="xs"
                    />
                  </div>
                  <div className="font-mono text-xs leading-normal break-words whitespace-pre-wrap text-emerald-800 dark:text-emerald-200">
                    {query}
                  </div>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
              {onBTQLFilter && (
                <DropdownMenuItem
                  onClick={() => {
                    if (ast.filter && onBTQLFilter) {
                      const filterMatch = query.match(
                        /filter:\s*([^|]+?)(?:\s*\||$)/,
                      );
                      if (filterMatch) {
                        onBTQLFilter(filterMatch[1].trim());
                      }
                    }
                  }}
                  disabled={!ast.filter || !onBTQLFilter}
                >
                  <ListFilter className="size-3" />
                  Apply as filter
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={(e) => {
                  if (onRunInSandbox) {
                    // let onMouseDown handle it
                    return;
                  }

                  const searchParams = new URLSearchParams();
                  searchParams.set("q", fullQuery);

                  const url = `/app/${orgName}/btql?${searchParams.toString()}`;

                  // Check if user wants to open in new tab (Cmd/Ctrl+Click)
                  if (e.metaKey || e.ctrlKey) {
                    // Open in new tab
                    window.open(url, "_blank");
                  } else {
                    // Navigate in same tab
                    router.push(url);
                  }
                }}
                onMouseDown={(e) => {
                  if (onRunInSandbox) {
                    onRunInSandbox({
                      query: fullQuery,
                      title,
                      openNewTab: true,
                    });
                    return;
                  }

                  // Handle middle-click to open in new tab
                  if (e.button === 1) {
                    e.preventDefault();
                    const searchParams = new URLSearchParams();
                    searchParams.set("q", fullQuery);
                    const url = `/app/${orgName}/btql?${searchParams.toString()}`;
                    window.open(url, "_blank");
                  }
                }}
              >
                <Asterisk className="size-3" />
                Run in sandbox
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <TabsContent value="chart" className="flex flex-col">
        <BTQLChartRenderer
          ast={ast}
          data={data}
          projectId={projectId}
          explanation={explanation}
          visualizationConfig={visualizationConfig}
          query={query}
          chartKind={chartKind}
          timeRangeFilter={timeRangeFilter}
        />
      </TabsContent>
      <TabsContent value="table">
        <DataTable data={data} />
      </TabsContent>
    </Tabs>
  );
}

export const BTQLChartDisplayMemo = memo(BTQLChartDisplay);
