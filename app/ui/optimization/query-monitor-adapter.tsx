"use client";

import { memo, useMemo } from "react";
import { MonitorCard } from "#/app/app/[org]/monitor/card/monitor-card";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";
import { type UnitType } from "#/app/app/[org]/monitor/card/monitor-card-config.types";
import type { TIME_BUCKET } from "#/ui/charts/time-bucket";

// Time range constants matching monitor-card expectations
const TIME_RANGE_TO_MILLISECONDS = {
  "1h": 60 * 60 * 1000,
  "3h": 3 * 60 * 60 * 1000,
  "12h": 12 * 60 * 60 * 1000,
  "1d": 24 * 60 * 60 * 1000,
  "3d": 3 * 24 * 60 * 60 * 1000,
  "7d": 7 * 24 * 60 * 60 * 1000,
  "30d": 30 * 24 * 60 * 60 * 1000,
  "90d": 90 * 24 * 60 * 60 * 1000,
} as const;

// Helper function to parse date strings
const parseDateString = (dateStr: string): Date => {
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) {
    // Fallback to current date if parsing fails
    return new Date();
  }
  return date;
};

// Adapter component to render BTQL results as MonitorCard
export interface BTQLMonitorAdapterProps {
  monitorArgs: {
    query: string;
    explanation?: string;
    visualizationConfig?: {
      vizType?: TimeseriesVizType;
      unitType?: UnitType;
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any[];
    timeRangeFilter?: string | { from: string; to: string };
    rowCount?: number;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    schema?: any;
  };
  projectId: string;
}

const BTQLMonitorAdapterImpl = ({
  monitorArgs,
  projectId,
}: BTQLMonitorAdapterProps) => {
  const timeRangeFilter = monitorArgs.timeRangeFilter;

  // Calculate chart time frame from the timeRangeFilter
  const chartTimeFrame = useMemo(() => {
    const now = Date.now();

    if (!timeRangeFilter) {
      // Default to 7 days if no filter provided
      return {
        start: now - 7 * 24 * 60 * 60 * 1000,
        end: now,
      };
    }

    if (typeof timeRangeFilter === "string") {
      // Handle predefined time ranges like "3d", "7d", etc.
      const duration =
        TIME_RANGE_TO_MILLISECONDS[
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          timeRangeFilter as keyof typeof TIME_RANGE_TO_MILLISECONDS
        ];
      if (duration) {
        return {
          start: now - duration,
          end: now,
        };
      }
      // Special case for "all"
      if (timeRangeFilter === "all") {
        return {
          start: now - 365 * 24 * 60 * 60 * 1000, // 1 year for "all"
          end: now,
        };
      }
    } else {
      // Handle custom date range
      const fromDate = parseDateString(timeRangeFilter.from);
      const toDate = parseDateString(timeRangeFilter.to);
      return {
        start: fromDate.getTime(),
        end: toDate.getTime() + 24 * 60 * 60 * 1000 - 1, // End of day
      };
    }

    // Fallback to 7 days
    return {
      start: now - 7 * 24 * 60 * 60 * 1000,
      end: now,
    };
  }, [timeRangeFilter]);

  // Determine time bucket based on the time range
  const timeBucket = useMemo((): TIME_BUCKET => {
    const duration = chartTimeFrame.end - chartTimeFrame.start;
    if (duration <= 2 * 60 * 60 * 1000) {
      return "minute" as const;
    } else if (duration <= 7 * 24 * 60 * 60 * 1000) {
      return "hour" as const;
    } else {
      return "day" as const;
    }
  }, [chartTimeFrame]);

  // Build monitor card config
  const cardConfig = useMemo(() => {
    // Estimate bucket count to choose default viz type when not explicitly provided
    const durationMs = chartTimeFrame.end - chartTimeFrame.start;
    const bucketMs =
      timeBucket === "minute"
        ? 60 * 1000
        : timeBucket === "hour"
          ? 60 * 60 * 1000
          : 24 * 60 * 60 * 1000;
    const approxBuckets = Math.max(1, Math.ceil(durationMs / bucketMs));
    const AUTO_BAR_BUCKET_THRESHOLD = 60;

    const vizType: TimeseriesVizType = monitorArgs.visualizationConfig?.vizType
      ? monitorArgs.visualizationConfig.vizType
      : approxBuckets <= AUTO_BAR_BUCKET_THRESHOLD
        ? "bars"
        : "lines";
    const unitType: UnitType =
      monitorArgs.visualizationConfig?.unitType || "count";

    // Parse measures from the query string
    // Example: "measures: count(1) as activity_count, sum(duration) as total_duration"
    const measuresMatch = monitorArgs.query.match(/measures:\s*([^|]+)/);
    let measures: Array<{ alias: string; btql: string; displayName: string }> =
      [];

    if (measuresMatch) {
      const measuresStr = measuresMatch[1].trim();
      // Split by comma but respect parentheses
      const measureParts = measuresStr.split(/,(?![^()]*\))/);

      measures = measureParts.map((part) => {
        const trimmed = part.trim();
        // Check for "expression as alias" pattern
        const asMatch = trimmed.match(/(.+?)\s+as\s+(\w+)/);

        if (asMatch) {
          const btql = asMatch[1].trim();
          const alias = asMatch[2].trim();
          return {
            alias,
            btql,
            displayName: alias
              .replace(/_/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()),
          };
        } else {
          // No alias, use the expression as both btql and alias
          return {
            alias: trimmed,
            btql: trimmed,
            displayName: trimmed
              .replace(/_/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()),
          };
        }
      });
    }

    return {
      name: monitorArgs.explanation || "Query Results",
      idName: "id",
      vizType: vizType,
      unitType: unitType,
      measures,
      hasBottomLegend: false,
    };
  }, [
    monitorArgs.query,
    monitorArgs.explanation,
    monitorArgs.visualizationConfig?.vizType,
    monitorArgs.visualizationConfig?.unitType,
    chartTimeFrame.end,
    chartTimeFrame.start,
    timeBucket,
  ]);

  // Stable, memoized values to avoid prop identity churn when parent re-renders
  const stableProjectIds = useMemo(() => [projectId], [projectId]);
  const stableExperimentIds: [] = [];

  return (
    <MonitorCard
      cardConfig={cardConfig}
      setCardState={() => {}}
      chartTimeFrame={chartTimeFrame}
      from="project_logs"
      projectIds={stableProjectIds}
      experimentIds={stableExperimentIds}
      standaloneLayoutMode
      disableClick={true}
      tzUTC={false}
      className="bg-transparent p-0"
      onBrush={() => {}}
    />
  );
};

export const BTQLMonitorAdapter = memo(BTQLMonitorAdapterImpl, (prev, next) => {
  // Re-render only if inputs that affect MonitorCard truly change
  const prevArgs = prev.monitorArgs;
  const nextArgs = next.monitorArgs;
  return (
    prev.projectId === next.projectId &&
    prevArgs.query === nextArgs.query &&
    prevArgs.explanation === nextArgs.explanation &&
    prevArgs.visualizationConfig?.vizType ===
      nextArgs.visualizationConfig?.vizType &&
    prevArgs.visualizationConfig?.unitType ===
      nextArgs.visualizationConfig?.unitType &&
    prevArgs.timeRangeFilter === nextArgs.timeRangeFilter &&
    true
  );
});
