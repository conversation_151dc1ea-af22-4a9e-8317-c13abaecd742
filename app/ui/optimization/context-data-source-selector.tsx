import { NestedDropdown } from "#/ui/nested-dropdown";
import { But<PERSON> } from "#/ui/button";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { BasicTooltip } from "#/ui/tooltip";
import { Plus } from "lucide-react";
import { cn } from "#/utils/classnames";
import { forwardRef, useMemo, useState } from "react";
import type { SearchableItemInfo } from "#/utils/codemirror/btql-lang";
import type {
  BTQLContextObject,
  ContextObject,
} from "#/ui/optimization/use-global-chat-context";

function makeDataSourceMetadata(
  items: SearchableItemInfo[],
  contextObjectIds: Set<string>,
): ContextObject[] {
  return items
    .filter((item) => !contextObjectIds.has(item.id))
    .map((item) => ({
      id: item.id,
      resource: "dataSourceMetadata" as const,
      name: item.name,
      metadata: item,
    }));
}

function filterItems(search: string, items: ContextObject[]): ContextObject[] {
  return items.filter((item) =>
    item.name.toLowerCase().includes(search.toLowerCase()),
  );
}

interface DataSourceSelectorProps {
  contextSelectorData: {
    dataSources: {
      projects: SearchableItemInfo[];
      datasets: SearchableItemInfo[];
      experiments: SearchableItemInfo[];
      promptSessions: SearchableItemInfo[];
      orgs: SearchableItemInfo[];
    };
    btqlTabs: BTQLContextObject[];
  };
  onAddContextObject: (contextObject: ContextObject) => void;
  hasDisplayedObjects: boolean;
  contextObjects: Record<string, ContextObject>;
}

export const ContextDataSourceSelector = ({
  contextSelectorData: { dataSources, btqlTabs },
  onAddContextObject,
  hasDisplayedObjects,
  contextObjects,
}: DataSourceSelectorProps) => {
  const [showDataSourceSelector, setShowDataSourceSelector] = useState(false);

  const contextObjectIds = useMemo(
    () => new Set(Object.keys(contextObjects)),
    [contextObjects],
  );

  const btqlTabsItems = useMemo(
    () => btqlTabs.filter((tab) => !contextObjectIds.has(`btql-${tab.id}`)),
    [btqlTabs, contextObjectIds],
  );

  const groupedDataSourceMetadata = useMemo(() => {
    const projects = makeDataSourceMetadata(
      dataSources.projects,
      contextObjectIds,
    );
    const datasets = makeDataSourceMetadata(
      dataSources.datasets,
      contextObjectIds,
    );
    const experiments = makeDataSourceMetadata(
      dataSources.experiments,
      contextObjectIds,
    );
    const promptSessions = makeDataSourceMetadata(
      dataSources.promptSessions,
      contextObjectIds,
    );
    const orgs = makeDataSourceMetadata(dataSources.orgs, contextObjectIds);

    const groupedDataSourceMetadata = [];
    if (projects.length > 0) {
      groupedDataSourceMetadata.push({
        groupLabel: "Projects",
        items: projects,
      });
    }
    if (datasets.length > 0) {
      groupedDataSourceMetadata.push({
        groupLabel: "Datasets",
        items: datasets,
      });
    }
    if (experiments.length > 0) {
      groupedDataSourceMetadata.push({
        groupLabel: "Experiments",
        items: experiments,
      });
    }
    if (promptSessions.length > 0) {
      groupedDataSourceMetadata.push({
        groupLabel: "Playgrounds",
        items: promptSessions,
      });
    }
    if (orgs.length > 0) {
      groupedDataSourceMetadata.push({
        groupLabel: "Organizations",
        items: orgs,
      });
    }
    return groupedDataSourceMetadata;
  }, [
    contextObjectIds,
    dataSources.datasets,
    dataSources.experiments,
    dataSources.orgs,
    dataSources.projects,
    dataSources.promptSessions,
  ]);

  const DataSourceMenuItem = useMemo(() => {
    const Component = forwardRef<
      HTMLDivElement,
      { item: ContextObject } & Omit<
        React.HTMLAttributes<HTMLDivElement>,
        "onSelect"
      >
    >(({ item, ...rest }, ref) => (
      <DropdownMenuItem
        ref={ref}
        {...rest}
        className="flex gap-2"
        onSelect={() => onAddContextObject(item)}
      >
        <span className="flex-1 truncate">{item.name}</span>
        {item.resource === "dataSourceMetadata" &&
          "projectName" in item.metadata &&
          item.metadata.projectName && (
            <span className="max-w-24 flex-none truncate text-primary-500">
              {item.metadata.projectName}
            </span>
          )}
      </DropdownMenuItem>
    ));
    Component.displayName = "DataSourceMenuItem";
    return Component;
  }, [onAddContextObject]);

  const subGroups = useMemo(
    () => [
      {
        groupLabel: "Data source metadata",
        subGroups: groupedDataSourceMetadata,
      },
      ...(btqlTabsItems.length > 0
        ? [
            {
              groupLabel: "BTQL Editor Tabs",
              items: btqlTabsItems,
            },
          ]
        : []),
    ],
    [groupedDataSourceMetadata, btqlTabsItems],
  );

  const addContextButton = (
    <Button
      variant="ghost"
      size="xs"
      className={cn(
        "h-5 w-7 truncate text-xs text-primary-500",
        !hasDisplayedObjects && "w-auto",
      )}
      iconClassName={cn("size-3")}
      Icon={Plus}
      onClick={() => setShowDataSourceSelector(true)}
    >
      {!hasDisplayedObjects && "Add context"}
    </Button>
  );

  return subGroups.length > 0 ? (
    <NestedDropdown<ContextObject>
      objectType="context item"
      subGroups={subGroups}
      filterItems={filterItems}
      open={showDataSourceSelector}
      setOpen={setShowDataSourceSelector}
      DropdownItemComponent={DataSourceMenuItem}
    >
      <div className="flex h-5">
        {hasDisplayedObjects ? (
          <BasicTooltip tooltipContent="Add context">
            {addContextButton}
          </BasicTooltip>
        ) : (
          addContextButton
        )}
      </div>
    </NestedDropdown>
  ) : null;
};
