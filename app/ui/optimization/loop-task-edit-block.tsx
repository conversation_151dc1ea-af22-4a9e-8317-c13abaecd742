import { Blend } from "lucide-react";
import { Button } from "#/ui/button";
import { PromptDiff } from "#/app/app/[org]/p/[project]/prompts/prompt-diff";
import { type TaskEditConfirmationData } from "#/utils/optimization/provider";

const LoopTaskEditBlock = ({
  loopEditConfirmationData,
  setEditTaskConfirmationData,
  promptId,
}: {
  loopEditConfirmationData: TaskEditConfirmationData;
  setEditTaskConfirmationData: (data: TaskEditConfirmationData | null) => void;
  promptId: string;
}) => {
  return (
    <div className="flex h-full flex-col overflow-hidden">
      <div className="flex flex-none items-center gap-2 border-b px-3 py-2">
        <h3 className="flex flex-1 items-center gap-2 text-xs font-medium">
          <Blend className="size-3" />
          Suggested change
        </h3>
        <Button
          variant="ghost"
          size="xs"
          className="text-primary-600"
          onClick={() => {
            loopEditConfirmationData?.onCancel(undefined, {
              continueChat: false,
            });
            setEditTaskConfirmationData(null);
          }}
        >
          Skip
        </Button>
        <Button
          variant="primary"
          size="xs"
          onClick={() => {
            loopEditConfirmationData?.onConfirm({
              continueChat: false,
            });
            setEditTaskConfirmationData(null);
          }}
        >
          Accept
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto px-4 py-2">
        <PromptDiff
          promptData={loopEditConfirmationData.newDefinition}
          diffPromptData={loopEditConfirmationData.originalDefinition}
          rowId={promptId}
          hideDiffDisclaimer
          hideLineNumbers
        />
      </div>
    </div>
  );
};

export default LoopTaskEditBlock;
