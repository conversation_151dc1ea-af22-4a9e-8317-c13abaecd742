import { Button, type ButtonProps } from "#/ui/button";
import {
  type ContextObject,
  useGlobalChat,
} from "#/ui/optimization/use-global-chat-context";
import { StopCircle } from "lucide-react";
import React from "react";

export const OptimizationButton = ({
  message,
  contextObjects = {},
  clearContextObjects = false,
  clearUserMessage = false,
  className,
  children,
  onSubmit,
  ...buttonProps
}: {
  children: React.ReactNode;
  message: string;
  clearContextObjects?: boolean;
  clearUserMessage?: boolean;
  contextObjects?: Record<string, ContextObject>;
  className?: string;
  onSubmit?: () => void;
} & ButtonProps) => {
  const {
    handleSendMessage,
    currentSessionIsActive,
    handleAbort,
    setIsChatOpen,
  } = useGlobalChat();

  if (currentSessionIsActive) {
    return (
      <div className={"flex w-fit items-center rounded-md border"}>
        <div className="animate-text-shimmer truncate bg-linear-to-r from-primary-300 via-primary-600 to-primary-300 bg-clip-text px-2 text-xs text-transparent">
          Generating...
        </div>
        <Button
          {...buttonProps}
          className="rounded-l-none border-l"
          variant={"ghost"}
          Icon={StopCircle}
          onClick={handleAbort}
        />
      </div>
    );
  }

  return (
    <Button
      className={className}
      onClick={() => {
        if (onSubmit) {
          onSubmit();
        }
        handleSendMessage(
          {
            id: crypto.randomUUID(),
            type: "user_message",
            message: message,
            contextObjects: contextObjects,
          },
          {
            clearContextObjects: clearContextObjects,
            clearUserMessage: clearUserMessage,
          },
        );

        setTimeout(() => {
          setIsChatOpen(true);
        }, 250);
      }}
      {...buttonProps}
    >
      {children}
    </Button>
  );
};
