import { type OrgPromptSession } from "#/app/app/[org]/p/[project]/library/library-actions";
import { isEmpty } from "#/utils/object";
import {
  forwardRef,
  type HTMLAttributes,
  type PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { ArrowUpRight, Blend, Plus, X } from "lucide-react";
import { cn } from "#/utils/classnames";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { NestedDropdown } from "./nested-dropdown";
import { DropdownMenuCheckboxItem } from "./dropdown-menu";
import { useRouter } from "next/navigation";
import { getPlaygroundLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useOrg } from "#/utils/user";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";

const PlaygroundOptionLabel = ({
  playgroundName,
  projectName,
  className,
}: {
  playgroundName: string;
  projectName?: string;
  className?: string;
}) => {
  return (
    <span
      className={cn("flex w-full flex-1 items-center gap-2", className)}
      title={`${playgroundName} from ${projectName ?? "this project"}`}
    >
      <span className="flex-1 truncate">{playgroundName}</span>
      {projectName && (
        <span className="max-w-24 flex-none truncate text-primary-500">
          {projectName}
        </span>
      )}
    </span>
  );
};

export function PlaygroundDropdown({
  playgrounds,
  children,
  selectedPlaygroundId,
  onSelectPlayground,
  onCreateNewPlayground,
  isLoopEnabled = false,
  align,
  open,
  setOpen,
  onClear,
  showOpenPlaygroundOption,
  refetchPlaygrounds,
}: PropsWithChildren<{
  playgrounds: OrgPromptSession[] | undefined;
  selectedPlaygroundId?: string;
  onSelectPlayground: (playground: OrgPromptSession) => void;
  onCreateNewPlayground?: (name: string) => void;
  isLoopEnabled?: boolean;
  align?: DropdownMenuContentProps["align"];
  open?: boolean;
  setOpen?: (open: boolean) => void;
  onClear?: VoidFunction;
  showOpenPlaygroundOption?: boolean;
  refetchPlaygrounds?: () => void;
}>) {
  const org = useOrg();
  const router = useRouter();
  const { projectId, projectName } = useContext(ProjectContext);

  const { setIsChatOpen, handleSendMessage } = useGlobalChat();

  useEffect(() => {
    if (open) {
      refetchPlaygrounds?.();
    }
  }, [open, refetchPlaygrounds]);

  const groupedPlaygrounds = useMemo(() => {
    const playgroundsByProject = (playgrounds ?? []).reduce<{
      [projectId: string]: {
        projectId: string;
        projectName: string;
        playgrounds: OrgPromptSession[];
      };
    }>((acc, playground) => {
      const { project_id, project_name } = playground;
      if (!acc[project_id]) {
        acc[project_id] = {
          projectId: project_id,
          projectName: project_name,
          playgrounds: [],
        };
      }

      acc[project_id].playgrounds.push(playground);
      return acc;
    }, {});

    return Object.values(playgroundsByProject).sort((a, b) =>
      a.projectName === projectName
        ? -1
        : b.projectName === projectName
          ? 1
          : a.projectName.localeCompare(b.projectName),
    );
  }, [playgrounds, projectName]);

  const dropdownData = useMemo(() => {
    if (isEmpty(groupedPlaygrounds)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      groupedPlaygrounds[0]?.projectName === projectName
        ? groupedPlaygrounds[0].playgrounds.sort((a, b) =>
            (a.name ?? "").localeCompare(b.name ?? ""),
          )
        : undefined;

    const otherProjects = groupedPlaygrounds
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ playgrounds }) =>
        playgrounds.sort((a, b) => (a.name ?? "").localeCompare(b.name ?? "")),
      );

    const subGroups =
      otherProjects.length > 0
        ? [{ groupLabel: "Other projects", items: otherProjects }]
        : undefined;

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [groupedPlaygrounds, projectName]);

  const PlaygroundMenuItem = forwardRef<
    HTMLDivElement,
    { item: OrgPromptSession } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item: playground, ...rest }, ref) => {
        const onSelect = () => {
          onSelectPlayground(playground);
        };
        const isProjectPlayground = playground.project_id === projectId;
        return (
          <DropdownMenuCheckboxItem {...rest} onSelect={onSelect} ref={ref}>
            <PlaygroundOptionLabel
              playgroundName={playground.name}
              projectName={
                isProjectPlayground ? undefined : playground.project_name
              }
              className={"pr-0"}
            />
            {selectedPlaygroundId === playground.id && (
              <X className="ml-auto size-3 text-primary-500" />
            )}
          </DropdownMenuCheckboxItem>
        );
      },
      [onSelectPlayground, selectedPlaygroundId, projectId],
    ),
  );

  return (
    <NestedDropdown
      align={align}
      objectType="prompt_session"
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      DropdownItemComponent={PlaygroundMenuItem}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      open={open}
      setOpen={setOpen}
      additionalActions={[
        ...(isLoopEnabled && !selectedPlaygroundId
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Blend className="size-3" />
                    Generate playground from dataset
                  </div>
                ),
                onSelect: () => {
                  handleSendMessage({
                    id: crypto.randomUUID(),
                    type: "user_message",
                    message:
                      "Generate a new playground based on the current dataset.",
                  });
                  setTimeout(() => {
                    setIsChatOpen(true);
                  }, 250);
                },
              },
            ]
          : []),
        ...(onCreateNewPlayground
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Plus className="size-3" />
                    Create new playground
                  </div>
                ),
                onSelect: () => onCreateNewPlayground("New playground"),
              },
            ]
          : []),
        ...(showOpenPlaygroundOption && selectedPlaygroundId
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <ArrowUpRight className="size-3" />
                    Open playground
                  </div>
                ),
                onSelect: () => {
                  const selectedPlayground = playgrounds?.find(
                    (p) => p.id === selectedPlaygroundId,
                  );
                  if (selectedPlayground) {
                    router.push(
                      getPlaygroundLink({
                        orgName: org.name,
                        projectName: selectedPlayground.project_name,
                        playgroundName: selectedPlayground.name,
                      }),
                    );
                  }
                },
              },
            ]
          : []),
        ...(onClear && selectedPlaygroundId
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <X className="size-3" />
                    Clear selection
                  </div>
                ),
                onSelect: () => onClear(),
              },
            ]
          : []),
      ]}
      emptyMessage={
        <div className="p-3 text-center text-xs text-primary-500">
          No playgrounds found
        </div>
      }
    >
      {children}
    </NestedDropdown>
  );
}

PlaygroundDropdown.displayName = "PlaygroundDropdown";
