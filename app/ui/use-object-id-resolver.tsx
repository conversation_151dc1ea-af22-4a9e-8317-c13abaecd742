"use client";

import { useEffect } from "react";
import { useActiveRowAndSpan, useNavToObjectId } from "./query-parameters";
import { useDBQuery } from "#/utils/duckdb";
import { singleQuote } from "#/utils/sql-utils";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import { fetchBtql, rowWithIdsSchema } from "#/utils/btql/btql";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useSessionToken } from "#/utils/auth/session-token";
import * as Query from "#/utils/btql/query-builder";
import { useBtqlFlags } from "#/lib/feature-flags";
import { useOrg } from "#/utils/user";

/**
 * Arrow table and paginated object viewer only support deep linking to the root
 * span ID. The oid= query parameter may refer to a non-root span. In this case,
 * use the resolution logic below to find the root span and overwrite `oid=` ->
 * `r=&s=`, ensuring the table selects the correct row.
 *
 * @param rowScanRaw The return value of `useParquetView` / `useParquetViews`
 * query for the relevant objects.
 * @param rowRefreshed The return value of `useParquetView` / `useParquetViews`
 * query for the relevant objects.
 */
export function useObjectIdResolver(
  rowScanRaw: string | null,
  rowRefreshed: number[],
) {
  const [_, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [navToObjectId, setNavToObjectId] = useNavToObjectId();

  const { data: findSelectedRowData } = useDBQuery(
    rowScanRaw &&
      navToObjectId &&
      `
      SELECT child.span_id AS child_span_id, root.id AS root_id
      FROM (${rowScanRaw}) AS child
      JOIN (${rowScanRaw}) AS root
      ON child.root_span_id = root.span_id
      WHERE child.id = ${singleQuote(navToObjectId)}`,
    rowRefreshed,
  );

  useEffect(() => {
    if (!findSelectedRowData || !navToObjectId) {
      return;
    }
    if (findSelectedRowData.numRows !== 1) {
      toast.error(`Couldn’t find object with ID ${navToObjectId}`);
      setNavToObjectId(null);
      return;
    }
    const result = findSelectedRowData.get(0)!;
    // Avoid pushing history items, which makes the back button redirect to
    // the same page.
    const options = { history: "replace" as const };
    setActiveRowAndSpan(
      { r: result.root_id, s: result.child_span_id },
      options,
    );
    setNavToObjectId(null, options);
  }, [
    findSelectedRowData,
    setActiveRowAndSpan,
    navToObjectId,
    setNavToObjectId,
  ]);
}

export function useBtqlObjectIdResolver({
  objectType,
  objectId,
}: {
  objectType: DataObjectType;
  objectId: string | null;
}) {
  const [_, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [navToObjectId, setNavToObjectId] = useNavToObjectId();

  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const btqlFlags = useBtqlFlags();

  const { data: findSelectedRowData } = useQuery({
    queryKey: ["objectIdResolver", navToObjectId, objectId, objectType],
    queryFn: async ({ signal }) =>
      await fetchBtql({
        args: {
          query: {
            filter: { btql: `id = '${navToObjectId}'` },
            from: Query.from(objectType, [objectId!], "traces"),
            select: [{ op: "star" }],
          },
          brainstoreRealtime: true,
        },
        btqlFlags,
        apiUrl: org.api_url,
        getOrRefreshToken,
        schema: rowWithIdsSchema,
        signal,
      }),
    throwOnError: false,
    enabled: !!objectId && !!navToObjectId,
  });

  useEffect(() => {
    if (!findSelectedRowData || !navToObjectId) {
      return;
    }

    // Avoid pushing history items, which makes the back button redirect to
    // the same page.
    const options = { history: "replace" as const };
    const child = findSelectedRowData.data.find(
      (row) => row.id === navToObjectId,
    );
    if (!child) {
      toast.error(`Couldn’t find object with ID ${navToObjectId}`);
      setNavToObjectId(null);
      return;
    }
    const { root_span_id, span_id } = child;
    const root = findSelectedRowData.data.find(
      (row) => row.span_id === root_span_id,
    );
    if (!root) {
      toast.error(`Couldn’t find object with ID ${navToObjectId}`);
      setNavToObjectId(null);
      return;
    }
    setActiveRowAndSpan({ r: root.id, s: span_id }, options);
    setNavToObjectId(null, options);
  }, [
    findSelectedRowData,
    setActiveRowAndSpan,
    navToObjectId,
    setNavToObjectId,
  ]);
}
