import { footerLinks } from "#/app/(landing)/constants";
import { getOrgLink } from "#/app/app/[org]/getOrgLink";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import { Logo } from "./logo";

interface Props {
  className?: string;
  maxWidth?: string;
  inApp?: boolean;
  inDocs?: boolean;
  orgName?: string;
}

export const Footer = ({
  className,
  maxWidth,
  inApp,
  inDocs,
  orgName,
}: Props) => {
  const braintrustLinkHref = inApp && orgName ? getOrgLink({ orgName }) : "/";

  return (
    <footer
      className={cn(
        "@container relative z-10 flex-none border-primary-200 pt-12 pb-12 text-sm",
        {
          "lg:flex lg:items-start lg:gap-10 lg:pb-30": !inDocs,
          "text-xs text-primary-500 sm:pb-8 lg:pb-12": inApp,
          "justify-center": !inApp && !inDocs,
          "flex items-start text-xs": inDocs,
        },
        className,
      )}
      style={{ maxWidth }}
    >
      <nav
        className={cn(
          // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
          "footer flex max-w-none items-center justify-center md:justify-start",
          {
            "items-start justify-start": inApp || inDocs,
            "md:justify-center": !inApp,
          },
        )}
      >
        <ul
          className={cn(
            "flex flex-col flex-wrap items-start gap-2 font-medium @lg:flex-row @lg:items-center @lg:gap-x-5 @lg:gap-y-2",
            {
              "font-normal": inDocs,
              "items-center": !inApp && !inDocs,
              "flex-row justify-start @lg:flex-row": inApp,
            },
          )}
        >
          <li
            className={cn("mb-3 @md:mb-0", {
              "w-full pt-2 md:mb-3": inDocs,
              "w-full @md:mb-1 @lg:mb-0 @lg:w-auto": inApp,
            })}
          >
            <Link href={braintrustLinkHref} className="hover:text-primary-700">
              <Logo width={inApp ? 110 : 120} className="overflow-visible" />
            </Link>
          </li>
          {(inApp || inDocs
            ? [{ title: "Home", href: "/home" }, ...footerLinks]
            : footerLinks
          ).map((link) => (
            <li key={link.title} style={{ marginLeft: 0 }}>
              <Link
                href={link.href}
                className="font-normal hover:text-primary-700"
                target={inApp ? "_blank" : undefined}
              >
                {link.title}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </footer>
  );
};

export default Footer;
