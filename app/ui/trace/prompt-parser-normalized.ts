import { parsePromptFromSpan, type Span } from "@braintrust/local";
import { normalizeToKnownFormat } from "#/ui/trace/format-normalizer";
import { openAIResponseNormalizer } from "#/ui/trace/converters/openai-response-converter";
import { aiSDKResponseNormalizer } from "#/ui/trace/converters/ai-sdk-response-converter";

/**
 * Normalized version of parsePromptFromSpan that handles multiple input formats.
 *
 * This wrapper:
 * 1. Normalizes the span data using available format normalizers
 * 2. Creates a normalized span with the processed data
 * 3. Calls the original parsePromptFromSpan with normalized data
 */
export function parsePromptFromSpanNormalized(span: Span) {
  try {
    if (span.data.span_attributes?.type !== "llm") return false;

    // Parse the raw input/output/metadata
    const rawInput =
      typeof span.data.input === "string"
        ? JSON.parse(span.data.input)
        : span.data.input;

    const metadataString =
      typeof span.data.metadata === "string" ? span.data.metadata : undefined;

    const spanAttributes = span.data.span_attributes;
    // Normalize using available normalizers (Try Prompt specific)
    // Pass undefined for output to get input-only behavior
    const normalized = normalizeToKnownFormat(
      rawInput,
      undefined, // Don't process output for Try Prompt context
      metadataString,
      spanAttributes,
      [aiSDKResponseNormalizer, openAIResponseNormalizer],
      { cacheKey: span.id + "-try-prompt" },
    );

    // Keep all messages for Try Prompt - tool messages are needed for conversation continuity
    const processedInput = normalized.input;

    // Create a new span with normalized and filtered data
    const normalizedSpan: Span = {
      ...span,
      data: {
        ...span.data,
        input:
          typeof span.data.input === "string"
            ? JSON.stringify(processedInput)
            : processedInput,
        metadata: normalized.metadata,
        // Don't modify output since parsePromptFromSpan doesn't use it
      },
    };

    // Call the original parser with normalized data
    const result = parsePromptFromSpan(normalizedSpan);

    return result;
  } catch (error) {
    return false;
  }
}
