"use client";
import { useState, useEffect, useCallback } from "react";
import {
  useExpandedFrameState,
  useFullscreenTraceState,
} from "#/ui/query-parameters";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";

export const useTraceFullscreen = () => {
  // set dummy value to avoid window error on server
  const [windowWidth, setWindowWidth] = useState(1000);
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const [fullscreenState, _setFullscreenState] = useFullscreenTraceState();

  const isNarrowViewport = windowWidth < 760;
  const isTraceFullscreen = isNarrowViewport || !!fullscreenState;

  const { enableScope, disableScope } = useHotkeysContext();

  const [_, setExpandedFrameId] = useExpandedFrameState();

  const setFullscreenState = useCallback(
    (state: string | null) => {
      if (state === null) {
        disableScope(HotkeyScope.ExpandedTrace);
        setExpandedFrameId(null);
      } else {
        enableScope(HotkeyScope.ExpandedTrace);
      }
      _setFullscreenState(state);
    },
    [enableScope, disableScope, _setFullscreenState, setExpandedFrameId],
  );

  useHotkeys(
    ["Escape"],
    () => {
      setFullscreenState(null);
    },
    [setFullscreenState],
    { scopes: [HotkeyScope.ExpandedTrace], description: "Exit fullscreen" },
  );

  return { isTraceFullscreen, setFullscreenState, isNarrowViewport };
};
