import { useMemo } from "react";
import { type PreviewSpan, type Span, type Trace } from "./graph";
import { useHotkeys } from "react-hotkeys-hook";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";

function buildNodeList(roots: (Span | PreviewSpan)[], seen: Set<string>) {
  const ret: (Span | PreviewSpan)[] = [];
  for (const node of roots) {
    if (seen.has(node.id) || seen.has(node.span_id)) {
      continue;
    }
    seen.add(node.id);
    seen.add(node.span_id);
    ret.push(node);
    try {
      ret.push(...buildNodeList(node.children, seen));
    } catch (e) {
      if (
        e instanceof RangeError &&
        e.message.includes("Maximum call stack size exceeded")
      ) {
        console.error("Caught buildNodeList recursion limit error:", e.message);
        return ret;
      }
      throw e;
    }
  }

  return ret;
}

export function useTraceShortcuts({
  traces,
  selectedSpan,
  setActiveSpan,
}: {
  traces: Trace[];
  selectedSpan: Span | null;
  setActiveSpan(spanId: string, rootSpanId: string | undefined): void;
}) {
  const nodeList = useMemo(
    () => traces.flatMap((t) => buildNodeList(t ? [t.root] : [], new Set())),
    [traces],
  );

  const rowIdx = selectedSpan
    ? nodeList.findIndex((n) => n.id === selectedSpan.id)
    : -1;

  const prevRow = useDebouncedCallback(() => {
    if (rowIdx > 0) {
      const nextSpan = nodeList[rowIdx - 1];
      if (nextSpan?.span_id) {
        setActiveSpan(nextSpan.span_id, nextSpan.root_span_id);
      }
    }
  }, 10);

  const nextRow = useDebouncedCallback(() => {
    if (rowIdx < nodeList.length - 1) {
      const nextSpan = nodeList[rowIdx + 1];
      if (nextSpan?.span_id) {
        setActiveSpan(nextSpan.span_id, nextSpan.root_span_id);
      }
    }
  }, 10);

  useHotkeys(
    "p",
    prevRow,
    {
      scopes: ["sidepanel"],
      description: "Move to the previous span",
      preventDefault: true,
    },
    [prevRow],
  );
  useHotkeys(
    "n",
    nextRow,
    {
      scopes: ["sidepanel"],
      description: "Move to the next span",
      preventDefault: true,
    },
    [nextRow],
  );
}
