import { isEmpty } from "braintrust/util";
import { type Trace } from "./graph";
import { getDiffRight } from "#/utils/diffs/diff-objects";

export const getTraceDurations = ({
  traces,
  calculateTotalDuration,
}: {
  traces: Trace[];
  calculateTotalDuration?: boolean;
}) => {
  const { minStart, maxEnd } = traces.reduce(
    (
      acc: { minStart: number | undefined; maxEnd: number | undefined },
      trace,
    ) => {
      const start = getDiffRight(trace.root.data.metrics?.start);
      const end = getDiffRight(trace.root.data.metrics?.end);
      return {
        minStart:
          start && (!acc.minStart || start < acc.minStart)
            ? start
            : acc.minStart,
        maxEnd: end && (!acc.maxEnd || end > acc.maxEnd) ? end : acc.maxEnd,
      };
    },
    { minStart: undefined, maxEnd: undefined },
  );

  const totalDuration =
    calculateTotalDuration && !isEmpty(maxEnd) && !isEmpty(minStart)
      ? maxEnd - minStart
      : undefined;

  return { traceStart: minStart, traceEnd: maxEnd, totalDuration };
};
