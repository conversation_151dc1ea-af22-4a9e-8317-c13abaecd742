import { deserializePlainStringAsJSON } from "braintrust";
import type { LLMMessageType } from "#/ui/LLMView";
import { type spanAttributesSchema, type Span } from "@braintrust/local";
import type {
  FormatNormalizer,
  NormalizedData,
} from "#/ui/trace/format-normalizer";
import { z } from "zod";

function generateCallId(): string {
  return typeof crypto.randomUUID === "function"
    ? crypto.randomUUID()
    : `call_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

// Zod schemas based on OpenAI Agents SDK types to avoid breaking changes
// Based on @openai/agents-core/dist/tracing/spans.d.ts

const OpenAIAgentsMessageItemSchema = z.object({
  type: z.literal("message").optional(),
  role: z
    .enum([
      "system",
      "user",
      "assistant",
      "tool",
      "function",
      "developer",
      "model",
    ])
    .optional(),
  content: z
    .union([
      z.string(),
      z.array(
        z.object({
          type: z.string().optional(),
          text: z.string().optional(),
        }),
      ),
    ])
    .optional(),
});

const OpenAIAgentsFunctionCallItemSchema = z.object({
  type: z.literal("function_call"),
  callId: z.string().optional(),
  id: z.string().optional(),
  call_id: z.string().optional(),
  name: z.string().optional(),
  arguments: z.string().optional(),
});

const OpenAIAgentsFunctionCallResultItemSchema = z.object({
  type: z.literal("function_call_result"),
  callId: z.string().optional(),
  call_id: z.string().optional(),
  output: z
    .union([z.object({ text: z.string().optional() }), z.string(), z.unknown()])
    .optional(),
});

const OpenAIAgentsWebSearchCallItemSchema = z.object({
  type: z.literal("web_search_call"),
  id: z.string().optional(),
  action: z
    .object({
      type: z.string(),
      query: z.string(),
    })
    .optional(),
  status: z.string().optional(),
});

const OpenAIAgentsReasoningItemSchema = z.object({
  type: z.literal("reasoning"),
  id: z.string().optional(),
  summary: z.array(z.object({ text: z.string(), type: z.string() })).optional(),
});

const OpenAIAgentsImageGenerationCallItemSchema = z.object({
  type: z.literal("image_generation_call"),
  id: z.string().optional(),
  background: z.string().optional(),
  output_format: z.string().optional(),
  quality: z.string().optional(),
  size: z.string().optional(),
  status: z.string().optional(),
  revised_prompt: z.string().optional(),
  result: z
    .object({
      content_type: z.string().optional(),
      filename: z.string().optional(),
      key: z.string().optional(),
      type: z.string().optional(),
    })
    .optional(),
});

const OpenAIAgentsResponseItemSchema = z.discriminatedUnion("type", [
  OpenAIAgentsMessageItemSchema,
  OpenAIAgentsFunctionCallItemSchema,
  OpenAIAgentsFunctionCallResultItemSchema,
  OpenAIAgentsWebSearchCallItemSchema,
  OpenAIAgentsReasoningItemSchema,
  OpenAIAgentsImageGenerationCallItemSchema,
]);

const OpenAIAgentsResponseArraySchema = z.array(OpenAIAgentsResponseItemSchema);

const OpenAIAgentsToolDefinitionSchema = z.object({
  name: z.string(),
  description: z.string(),
  parameters: z.unknown().optional(),
  strict: z.boolean().optional(),
});

const OpenAIAgentsToolDefinitionArraySchema = z.array(
  OpenAIAgentsToolDefinitionSchema,
);

// Infer types from schemas
type OpenAIAgentsMessageItem = z.infer<typeof OpenAIAgentsMessageItemSchema>;
type OpenAIAgentsFunctionCallItem = z.infer<
  typeof OpenAIAgentsFunctionCallItemSchema
>;
type OpenAIAgentsFunctionCallResultItem = z.infer<
  typeof OpenAIAgentsFunctionCallResultItemSchema
>;
type OpenAIAgentsWebSearchCallItem = z.infer<
  typeof OpenAIAgentsWebSearchCallItemSchema
>;
type OpenAIAgentsReasoningItem = z.infer<
  typeof OpenAIAgentsReasoningItemSchema
>;
type OpenAIAgentsToolDefinition = z.infer<
  typeof OpenAIAgentsToolDefinitionSchema
>;
export interface OpenAIResponseSpan {
  span: Span;
  spanType: "llm" | "score";
  isFirst: boolean;
  toolResponses: Record<string, unknown>;
}

function hasInputOutputTextType(part: unknown): boolean {
  if (typeof part !== "object" || part === null || !("type" in part)) {
    return false;
  }
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const record = part as Record<string, unknown>;
  return (
    typeof record.type === "string" &&
    (record.type === "input_text" || record.type === "output_text")
  );
}

export function isOpenAIResponse(
  input: unknown,
  output: unknown,
  metadata?: string,
  spanAttributes?: z.infer<typeof spanAttributesSchema>,
): boolean {
  // Check if input is in expected format (string or OpenAI message array)
  let inputIsValid = false;

  // Input can be a string (for simple prompts)
  if (typeof input === "string") {
    inputIsValid = true;
  } else if (Array.isArray(input)) {
    // Reject standard OpenAI Chat Completions format to avoid false positives
    // Chat Completions have messages with role fields and tool_calls arrays in assistant messages
    const isStandardChatCompletions = input.some((item) => {
      if (typeof item !== "object" || item === null || !("role" in item))
        return false;

      // Check for assistant message with tool_calls (standard Chat Completions)
      if (
        item.role === "assistant" &&
        "tool_calls" in item &&
        Array.isArray(item.tool_calls)
      ) {
        return true;
      }

      // Check for tool message with tool_call_id (standard Chat Completions)
      if (
        item.role === "tool" &&
        "tool_call_id" in item &&
        typeof item.tool_call_id === "string"
      ) {
        return true;
      }

      return false;
    });

    if (isStandardChatCompletions) {
      return false; // This is standard Chat Completions, not Agents format
    }

    // Or input can be OpenAI Agents message array
    const inputResult = OpenAIAgentsResponseArraySchema.safeParse(input);
    if (inputResult.success) {
      inputIsValid = inputResult.data.some(
        (item) =>
          [
            "function_call",
            "function_call_result",
            "message",
            "web_search_call",
            "reasoning",
            "image_generation_call",
          ].includes(item.type || "") ||
          ("role" in item && !item.type), // Handle message-like items without explicit type
      );
    }
  }

  if (!inputIsValid) return false;

  // If output exists, validate it's OpenAI format
  let outputIsValid = true; // Default to true for undefined/null output (try prompt case)
  if (output !== undefined && output !== null) {
    const outputResult = OpenAIAgentsResponseArraySchema.safeParse(output);
    if (!outputResult.success) {
      outputIsValid = false;
    } else {
      outputIsValid = outputResult.data.some((item) => {
        if (
          item.type === "function_call" ||
          item.type === "function_call_result" ||
          item.type === "web_search_call" ||
          item.type === "reasoning" ||
          item.type === "image_generation_call"
        ) {
          return true;
        }
        // Check for message with content array
        return item.type === "message" && Array.isArray(item.content);
      });
    }
  }

  if (!outputIsValid) return false;

  // Check metadata for OpenAI indicators to avoid being overly broad
  if (metadata) {
    try {
      const parsedMetadata = JSON.parse(metadata);
      const hasOpenAIIndicators =
        parsedMetadata.provider === "openai" ||
        parsedMetadata.object === "response" ||
        (parsedMetadata.model &&
          typeof parsedMetadata.model === "string" &&
          (parsedMetadata.model.includes("gpt") ||
            parsedMetadata.model.includes("o1"))) ||
        parsedMetadata.instructions !== undefined; // OpenAI responses often have instructions

      return hasOpenAIIndicators;
    } catch {
      // If metadata parsing fails, continue with other checks
    }
  }

  // Without metadata, be more conservative - only accept if we have strong format indicators
  // For array input, check for OpenAI-specific patterns
  if (Array.isArray(input)) {
    const inputResult = OpenAIAgentsResponseArraySchema.safeParse(input);
    if (inputResult.success) {
      const hasOpenAIPatterns = inputResult.data.some((item) => {
        // Look for content arrays with input_text/output_text types
        // Only check content for message items
        if (
          (item.type === "message" || (!item.type && "role" in item)) &&
          "content" in item &&
          Array.isArray(item.content)
        ) {
          return item.content.some(hasInputOutputTextType);
        }
        return false;
      });
      return hasOpenAIPatterns;
    }
  }

  // For string input without metadata, only accept if output has clear OpenAI patterns
  if (typeof input === "string" && output) {
    const outputResult = OpenAIAgentsResponseArraySchema.safeParse(output);
    if (outputResult.success) {
      return outputResult.data.some(
        (item) =>
          item.type === "message" &&
          Array.isArray(item.content) &&
          item.content.some(
            (part) =>
              typeof part === "object" &&
              part !== null &&
              "type" in part &&
              part.type === "output_text",
          ),
      );
    }
  }

  return false;
}

export interface ConvertedSpanData {
  messages: LLMMessageType[];
  toolDefinitions: Map<string, string>;
}

export function convertOpenAIResponseToMessages(
  input: unknown,
  output: unknown | undefined,
  metadata?: string,
  spanAttributes?: z.infer<typeof spanAttributesSchema>,
): ConvertedSpanData & {
  inputMessages: LLMMessageType[];
  outputMessages: LLMMessageType[];
} {
  const inputMessages: LLMMessageType[] = [];
  const outputMessages: LLMMessageType[] = [];
  const toolDefinitions = new Map<string, string>();

  // Process input messages - following proxy.ts patterns
  // Handle string input by converting to user message
  if (typeof input === "string") {
    inputMessages.push({
      role: "user",
      content: input,
    });
  } else {
    const inputParseResult = OpenAIAgentsResponseArraySchema.safeParse(input);
    if (inputParseResult.success) {
      const inputItems = inputParseResult.data;

      // Filter by type like the proxy does, including items without explicit type but with role
      const inputMessageItems = inputItems.filter(
        (item): item is OpenAIAgentsMessageItem =>
          item.type === "message" || (!item.type && "role" in item),
      );
      const inputFunctionCalls = inputItems.filter(
        (item): item is OpenAIAgentsFunctionCallItem =>
          item.type === "function_call",
      );
      const inputFunctionResults = inputItems.filter(
        (item): item is OpenAIAgentsFunctionCallResultItem =>
          item.type === "function_call_result",
      );
      const inputWebSearchCalls = inputItems.filter(
        (item): item is OpenAIAgentsWebSearchCallItem =>
          item.type === "web_search_call",
      );
      // Note: reasoning items are handled as metadata, not converted to messages

      // Process regular messages (system, user, assistant without tool_calls)
      for (const item of inputMessageItems) {
        if (
          item.content &&
          item.role &&
          (item.role === "system" ||
            item.role === "user" ||
            item.role === "assistant")
        ) {
          // Handle content array with input_text/output_text types
          if (Array.isArray(item.content)) {
            // Extract text content like we do for output
            const textParts = item.content
              .filter(
                (part) =>
                  typeof part === "object" &&
                  part !== null &&
                  "type" in part &&
                  (part.type === "input_text" || part.type === "output_text"),
              )
              .map((part) =>
                typeof part === "object" && part !== null && "text" in part
                  ? part.text
                  : "",
              )
              .filter(Boolean);

            if (textParts.length > 0) {
              inputMessages.push({
                role: item.role,
                content: textParts.join(""),
              });
            }
          } else {
            inputMessages.push({
              role: item.role,
              content:
                typeof item.content === "string"
                  ? item.content
                  : JSON.stringify(item.content),
            });
          }
        }
      }

      // Process function calls - convert to assistant messages with tool_calls
      if (inputFunctionCalls.length > 0) {
        inputMessages.push({
          role: "assistant",
          content: null,
          tool_calls: inputFunctionCalls.map((item) => ({
            id: item.callId || item.id || generateCallId(),
            type: "function" as const,
            function: {
              name: item.name || "",
              arguments: item.arguments || "",
            },
          })),
        });

        // Track tool definitions
        for (const item of inputFunctionCalls) {
          if (item.name) {
            toolDefinitions.set(item.name, item.name);
          }
        }
      }

      // Process web search calls - convert to assistant messages with tool_calls
      if (inputWebSearchCalls.length > 0) {
        inputMessages.push({
          role: "assistant",
          content: null,
          tool_calls: inputWebSearchCalls.map((item) => ({
            id: item.id || generateCallId(),
            type: "function" as const,
            function: {
              name: "web_search",
              arguments: JSON.stringify({
                query: item.action?.query || "",
                type: item.action?.type || "search",
              }),
            },
          })),
        });

        // Track web_search tool definition
        toolDefinitions.set("web_search", "Search the web for information");
      }

      // Process function call results
      for (const item of inputFunctionResults) {
        if (item.callId && item.output) {
          inputMessages.push({
            role: "tool",
            content:
              typeof item.output === "object" &&
              item.output !== null &&
              "text" in item.output &&
              typeof item.output.text === "string"
                ? item.output.text
                : JSON.stringify(item.output),
            tool_call_id: item.callId,
          });
        }
      }
    }
  }

  // Process output messages - following proxy.ts patterns
  // Skip output processing if undefined (for Try Prompt context)
  const outputParseResult = OpenAIAgentsResponseArraySchema.safeParse(output);
  if (outputParseResult.success) {
    const outputItems = outputParseResult.data;

    // Filter messages and function calls separately like the proxy does
    const outputMessageItems = outputItems.filter(
      (item): item is OpenAIAgentsMessageItem => item.type === "message",
    );
    const outputFunctionCalls = outputItems.filter(
      (item): item is OpenAIAgentsFunctionCallItem =>
        item.type === "function_call",
    );
    const outputWebSearchCalls = outputItems.filter(
      (item): item is OpenAIAgentsWebSearchCallItem =>
        item.type === "web_search_call",
    );
    // Handle reasoning blocks from output items
    const outputReasoningCalls = outputItems.filter(
      (item): item is OpenAIAgentsReasoningItem => item.type === "reasoning",
    );
    const reasoningBlocks =
      outputReasoningCalls?.flatMap(
        (reasoning) =>
          reasoning.summary?.map((summary) => ({
            content: summary.text,
          })) ?? [],
      ) ?? [];

    // Process regular messages
    for (const item of outputMessageItems) {
      if (
        item.content &&
        item.role &&
        (item.role === "system" ||
          item.role === "user" ||
          item.role === "assistant")
      ) {
        // Handle assistant messages with content array - following proxy pattern
        if (Array.isArray(item.content)) {
          // Extract text content like chatCompletionMessageFromResponseOutput does
          const textParts = item.content
            .filter(
              (part) =>
                typeof part === "object" &&
                part !== null &&
                "type" in part &&
                part.type === "output_text",
            )
            .map((part) =>
              typeof part === "object" && part !== null && "text" in part
                ? part.text
                : "",
            )
            .filter(Boolean);

          if (
            textParts.length > 0 &&
            item.role === "assistant" &&
            reasoningBlocks.length > 0
          ) {
            outputMessages.push({
              role: "assistant",
              content: textParts.join(""),
              reasoning: reasoningBlocks,
            });
          } else if (textParts.length > 0) {
            outputMessages.push({
              role: item.role,
              content: textParts.join(""),
            });
          }
        } else {
          if (item.role === "assistant" && reasoningBlocks.length > 0) {
            outputMessages.push({
              role: "assistant",
              content: item.content,
              reasoning: reasoningBlocks,
            });
          } else {
            outputMessages.push({
              role: item.role,
              content: item.content,
            });
          }
        }
      }
    }

    // If we have reasoning blocks but no message items, create an assistant message with reasoning
    if (reasoningBlocks.length > 0 && outputMessageItems.length === 0) {
      outputMessages.push({
        role: "assistant",
        content: "",
        reasoning: reasoningBlocks,
      });
    }

    // Process function calls separately - convert to assistant messages with tool_calls
    if (outputFunctionCalls.length > 0) {
      // Following proxy pattern: create one assistant message with all tool_calls
      outputMessages.push({
        role: "assistant",
        content: null,
        tool_calls: outputFunctionCalls.map((item) => ({
          id: item.call_id || item.id || generateCallId(),
          type: "function" as const,
          function: {
            name: item.name || "",
            arguments: item.arguments || "",
          },
        })),
      });

      // Track tool definitions
      for (const item of outputFunctionCalls) {
        if (item.name) {
          toolDefinitions.set(item.name, item.name);
        }
      }
    }

    // Process web search calls separately - convert to assistant messages with tool_calls
    if (outputWebSearchCalls.length > 0) {
      outputMessages.push({
        role: "assistant",
        content: null,
        tool_calls: outputWebSearchCalls.map((item) => ({
          id: item.id || generateCallId(),
          type: "function" as const,
          function: {
            name: "web_search",
            arguments: JSON.stringify({
              query: item.action?.query || "",
              type: item.action?.type || "search",
            }),
          },
        })),
      });

      // Track web_search tool definition
      toolDefinitions.set("web_search", "Search the web for information");
    }

    // Process function call results
    const outputFunctionResults = outputItems.filter(
      (item): item is OpenAIAgentsFunctionCallResultItem =>
        item.type === "function_call_result",
    );

    for (const item of outputFunctionResults) {
      if ((item.call_id || item.callId) && item.output) {
        outputMessages.push({
          role: "tool",
          content:
            typeof item.output === "object" &&
            item.output !== null &&
            "text" in item.output &&
            typeof item.output.text === "string"
              ? item.output.text
              : JSON.stringify(item.output),
          tool_call_id: item.call_id || item.callId!,
        });
      }
    }
  }

  // Parse metadata for system prompt and tool definitions
  if (metadata) {
    const { value: parsedMetadata } = deserializePlainStringAsJSON(metadata);
    if (parsedMetadata) {
      // Extract system prompt from instructions - only if there isn't already a system message
      if (parsedMetadata.instructions) {
        const hasSystemMessage = inputMessages.some(
          (msg) => msg.role === "system",
        );
        if (!hasSystemMessage) {
          inputMessages.unshift({
            role: "system",
            content: parsedMetadata.instructions,
          });
        }
      }

      // Extract tool definitions
      const toolsParseResult = OpenAIAgentsToolDefinitionArraySchema.safeParse(
        parsedMetadata.tools,
      );
      if (toolsParseResult.success) {
        for (const tool of toolsParseResult.data) {
          toolDefinitions.set(tool.name, tool.description);
        }
      }
    }
  }

  return {
    messages: [...inputMessages, ...outputMessages], // Combined for backward compatibility
    inputMessages,
    outputMessages,
    toolDefinitions,
  };
}

/**
 * Transforms metadata from OpenAI Responses format to Chat Completions format
 */
export function transformMetadataForChatCompletions(
  metadataString?: string,
): string | undefined {
  if (!metadataString) return metadataString;

  try {
    const metadata = JSON.parse(metadataString);
    const updatedMetadata = { ...metadata };

    // Transform tools from OpenAI Responses format to Chat Completions format
    const toolsParseResult = OpenAIAgentsToolDefinitionArraySchema.safeParse(
      metadata.tools,
    );
    if (toolsParseResult.success) {
      const transformedTools = toolsParseResult.data.map(
        (tool: OpenAIAgentsToolDefinition) => ({
          type: "function" as const,
          function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters,
            ...(tool.strict && { strict: tool.strict }),
          },
        }),
      );

      updatedMetadata.tools = transformedTools;
    }

    // Auto-detect reasoning from metadata
    if (
      updatedMetadata.reasoning?.effort !== undefined &&
      updatedMetadata.reasoning?.effort !== null
    ) {
      updatedMetadata.reasoning_effort = updatedMetadata.reasoning.effort;
    }
    if (
      updatedMetadata.text?.verbosity !== undefined &&
      updatedMetadata.text?.verbosity !== null
    ) {
      updatedMetadata.verbosity = updatedMetadata.text.verbosity;
    }

    // Fix text format - convert text.format to proper response_format
    if (metadata.text?.format) {
      const formatType = metadata.text.format.type;
      if (formatType === "text") {
        updatedMetadata.response_format = { type: "text" };
      } else if (
        formatType === "json_schema" &&
        metadata.text.format.json_schema
      ) {
        // Handle json_schema format properly
        updatedMetadata.response_format = {
          type: "json_schema",
          json_schema: metadata.text.format.json_schema,
        };
      } else if (formatType === "json_object") {
        updatedMetadata.response_format = { type: "json_object" };
      }
      // Remove the nested text structure after converting
      delete updatedMetadata.text;
    }

    return JSON.stringify(updatedMetadata);
  } catch (error) {
    // If metadata parsing fails, use original
    console.warn("Failed to parse metadata for tools transformation:", error);
    return metadataString;
  }
}

/**
 * Normalizer for OpenAI response format that converts to format parseLLMSpanPart can handle
 * Includes both input and output for thread view
 */
export const openAIResponseNormalizer: FormatNormalizer = {
  name: "OpenAIResponseNormalizer",
  detect: isOpenAIResponse,

  normalize(
    input: unknown,
    output: unknown,
    metadata?: string,
  ): NormalizedData {
    const transformedMetadata = transformMetadataForChatCompletions(metadata);
    const converted = convertOpenAIResponseToMessages(
      input,
      output,
      transformedMetadata,
    );

    return {
      input: converted.inputMessages,
      output: converted.outputMessages,
      toolDefinitions: converted.toolDefinitions,
      metadata: transformedMetadata,
    };
  },
};
