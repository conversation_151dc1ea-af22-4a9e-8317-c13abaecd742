/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-assertions */
import { describe, expect, test } from "vitest";
import {
  isOpenAIResponse,
  openAIResponseNormalizer,
  transformMetadataForChatCompletions,
} from "./openai-response-converter";

// Test fixtures based on OpenAI Responses format
const simpleOpenAIInput = [
  {
    type: "message",
    role: "user",
    content: "What's the weather like?",
  },
];

const simpleOpenAIOutput = [
  {
    type: "message",
    role: "assistant",
    content: [
      {
        type: "output_text",
        text: "I'd be happy to help you check the weather!",
      },
    ],
  },
];

const functionCallInput = [
  {
    type: "message",
    role: "user",
    content: "What's 2+2?",
  },
  {
    type: "function_call",
    name: "calculate",
    arguments: '{"operation": "add", "a": 2, "b": 2}',
    callId: "call_123",
  },
];

const functionCallOutput = [
  {
    type: "message",
    role: "assistant",
    content: [
      {
        type: "output_text",
        text: "I'll calculate that for you.",
      },
    ],
  },
  {
    type: "function_call",
    name: "calculate",
    arguments: '{"operation": "add", "a": 2, "b": 2}',
    call_id: "call_456",
  },
  {
    type: "function_call_result",
    call_id: "call_456",
    output: {
      text: "The result is 4",
    },
  },
];

const realWorldOpenAIInput = [
  {
    content:
      "I have invested considerable money into indoor shrimp farming. Will my venture pay off?",
    role: "user",
    type: "message",
  },
  {
    arguments: '{"question":"Will my indoor shrimp farming venture pay off?"}',
    callId: "call_INr0WHzVazlsMLwxCRNiQMJJ",
    id: "fc_68a39a124fe0819d972d60df79b05e0e054102c164a4fee5",
    name: "magic_8_ball",
    providerData: {
      id: "fc_68a39a124fe0819d972d60df79b05e0e054102c164a4fee5",
      type: "function_call",
    },
    status: "completed",
    type: "function_call",
  },
  {
    callId: "call_INr0WHzVazlsMLwxCRNiQMJJ",
    name: "magic_8_ball",
    output: {
      text: 'The magic 8-ball says: "Signs point to yes"',
      type: "text",
    },
    status: "completed",
    type: "function_call_result",
  },
];

const realWorldOpenAIOutput = [
  {
    content: [
      {
        annotations: [],
        logprobs: [],
        text: "I consulted the mystical 8-ball regarding your indoor shrimp farming venture, and its response was quite favorable! \n\nAs the stars align in your favor, it seems that your investment may indeed yield bountiful rewards. This is a time to nurture your shrimp, pay attention to the nuances of their care, and perhaps even explore innovative marketing strategies. The tides of fortune are likely to surge in your direction, so keep your hopes high and your ambitions even higher!",
        type: "output_text",
      },
    ],
    id: "msg_68a39a133928819da8f6dbe40da51590054102c164a4fee5",
    role: "assistant",
    status: "completed",
    type: "message",
  },
];

const realWorldMetadata = JSON.stringify({
  background: false,
  created_at: 1755552274,
  error: null,
  id: "resp_68a39a12a3ec819dbc40bd567c2e96d2054102c164a4fee5",
  incomplete_details: null,
  instructions:
    "You are a mystical fortune teller with access to a magical 8-ball. For EVERY question the user asks, you MUST:\n\n1. ALWAYS use the magic_8_ball tool first to consult the mystical oracle\n2. Based on the 8-ball's response, provide a fortune teller-style interpretation\n\nYou cannot answer questions without consulting the magic 8-ball first. Every response must start by using the magic_8_ball tool.\n\nYou must mention that you have consulted the magic 8 ball in your response, but you CAN'T tell the user what it said. It's words are for you alone.",
  max_output_tokens: null,
  max_tool_calls: null,
  model: "gpt-4o-mini-2024-07-18",
  object: "response",
  parallel_tool_calls: true,
  previous_response_id: null,
  prompt_cache_key: null,
  reasoning: {
    effort: null,
    summary: null,
  },
  safety_identifier: null,
  service_tier: "default",
  status: "completed",
  store: true,
  temperature: 1,
  text: {
    format: {
      type: "text",
    },
    verbosity: "medium",
  },
  tool_choice: "auto",
  tools: [
    {
      description: "Consult the magic 8-ball for mystical guidance",
      name: "magic_8_ball",
      parameters: {
        additionalProperties: false,
        properties: {
          question: {
            description: "The question to ask the magic 8-ball",
            type: "string",
          },
        },
        required: ["question"],
        type: "object",
      },
      strict: true,
      type: "function",
    },
  ],
  top_logprobs: 0,
  top_p: 1,
  truncation: "disabled",
  user: null,
});

const invalidInput = [
  {
    type: "invalid_type",
    content: "This is not a valid OpenAI response format",
  },
];

describe("openai-response-converter", () => {
  describe("isOpenAIResponse", () => {
    test("detects valid OpenAI response format with metadata", () => {
      expect(
        isOpenAIResponse(
          simpleOpenAIInput,
          simpleOpenAIOutput,
          realWorldMetadata,
        ),
      ).toBe(true);
      expect(
        isOpenAIResponse(
          functionCallInput,
          functionCallOutput,
          realWorldMetadata,
        ),
      ).toBe(true);
      expect(
        isOpenAIResponse(
          realWorldOpenAIInput,
          realWorldOpenAIOutput,
          realWorldMetadata,
        ),
      ).toBe(true);
    });

    test("handles undefined output with metadata (like Try Prompt)", () => {
      expect(
        isOpenAIResponse(simpleOpenAIInput, undefined, realWorldMetadata),
      ).toBe(true);
      expect(isOpenAIResponse(simpleOpenAIInput, null, realWorldMetadata)).toBe(
        true,
      );
    });

    test("rejects invalid formats even with metadata", () => {
      expect(
        isOpenAIResponse(invalidInput, simpleOpenAIOutput, realWorldMetadata),
      ).toBe(false);
      expect(
        isOpenAIResponse(simpleOpenAIInput, "not an array", realWorldMetadata),
      ).toBe(false);
    });

    test("accepts string input with valid OpenAI output and metadata", () => {
      expect(
        isOpenAIResponse(
          "What is the capital of Japan?",
          simpleOpenAIOutput,
          realWorldMetadata,
        ),
      ).toBe(true);
    });
  });

  describe("openAIResponseNormalizer", () => {
    test("normalizes real-world OpenAI response data", () => {
      const result = openAIResponseNormalizer.normalize(
        realWorldOpenAIInput,
        realWorldOpenAIOutput,
        realWorldMetadata,
      );

      // Should have system message from instructions, user message, function call, and function result
      expect(result.input).toHaveLength(4);
      expect((result.input as any)[0].role).toBe("system");
      expect((result.input as any)[1].role).toBe("user");
      expect((result.input as any)[2].role).toBe("assistant");
      expect((result.input as any)[3].role).toBe("tool");

      // Check output processing
      expect(result.output).toHaveLength(1);
      expect((result.output as any)[0]).toEqual({
        role: "assistant",
        content: expect.stringContaining("I consulted the mystical 8-ball"),
      });

      // Check tool definitions and metadata transformation
      expect(result.toolDefinitions?.has("magic_8_ball")).toBe(true);
      const transformedMetadata = JSON.parse(result.metadata!);
      expect(transformedMetadata.tools[0].type).toBe("function");
      expect(transformedMetadata.response_format).toEqual({ type: "text" });
    });

    test("detect function works correctly", () => {
      expect(
        openAIResponseNormalizer.detect(
          realWorldOpenAIInput,
          realWorldOpenAIOutput,
          realWorldMetadata,
        ),
      ).toBe(true);
      expect(
        openAIResponseNormalizer.detect(
          invalidInput,
          simpleOpenAIOutput,
          realWorldMetadata,
        ),
      ).toBe(false);
    });
  });

  describe("non-agent examples from the responses wrapper", () => {
    // Example 1: String input with OpenAI message output
    const example1Input = "What is the capital of Japan?";
    const example1Output = [
      {
        content: [
          {
            annotations: [],
            logprobs: [],
            text: "Tokyo.",
            type: "output_text",
          },
        ],
        id: "msg_68b0ec928508819e84aee527dca50eff0126b3dfcd3871b5",
        role: "assistant",
        status: "completed",
        type: "message",
      },
    ];
    const example1Metadata = {
      background: false,
      created_at: **********,
      error: null,
      id: "resp_68b0ec91eefc819eb0fcf7bbf9957d490126b3dfcd3871b5",
      incomplete_details: null,
      instructions: "Answer in one word.",
      max_output_tokens: null,
      max_tool_calls: null,
      metadata: {},
      model: "gpt-4o-mini-2024-07-18",
      object: "response",
      output_text: "Tokyo.",
      parallel_tool_calls: true,
      previous_response_id: null,
      prompt_cache_key: null,
      provider: "openai",
      reasoning: { effort: null, summary: null },
      safety_identifier: null,
      service_tier: "default",
      status: "completed",
      store: true,
      temperature: 1,
      text: { format: { type: "text" }, verbosity: "medium" },
      tool_choice: "auto",
      tools: [],
      top_logprobs: 0,
      top_p: 1,
      truncation: "disabled",
      user: null,
    };

    // Example 2: OpenAI message input and output with input_text/output_text types
    const example2Input = [
      {
        content: [{ text: "Is this a question?", type: "input_text" }],
        role: "user",
      },
    ];
    const example2Output = [
      {
        content: [
          {
            annotations: [],
            logprobs: [],
            text: "Question.",
            type: "output_text",
          },
        ],
        id: "msg_68b0ec909120819eb5ae5431edbc7bbd0ea94e5b1614ab95",
        role: "assistant",
        status: "completed",
        type: "message",
      },
    ];
    const example2Metadata = {
      background: false,
      created_at: **********,
      error: null,
      id: "resp_68b0ec903d18819e806dcfd4e479ad0e0ea94e5b1614ab95",
      incomplete_details: null,
      instructions: "Classify the following text as a question or a statement.",
      max_output_tokens: null,
      max_tool_calls: null,
      metadata: {},
      model: "gpt-4o-2024-08-06",
      object: "response",
      output_text: "Question.",
      parallel_tool_calls: true,
      previous_response_id: null,
      prompt_cache_key: null,
      provider: "openai",
      reasoning: { effort: null, summary: null },
      safety_identifier: null,
      service_tier: "default",
      status: "completed",
      store: true,
      temperature: 1,
      text: { format: { type: "text" }, verbosity: "medium" },
      tool_choice: "auto",
      tools: [],
      top_logprobs: 0,
      top_p: 1,
      truncation: "disabled",
      user: null,
    };

    test("example 1: string input with OpenAI output should be detected with metadata", () => {
      const detected = isOpenAIResponse(
        example1Input,
        example1Output,
        JSON.stringify(example1Metadata),
      );

      // Should detect because metadata has OpenAI indicators
      expect(detected).toBe(true);
    });

    test("example 1: try prompt scenario (string input, no output)", () => {
      const detected = isOpenAIResponse(
        example1Input,
        undefined,
        JSON.stringify(example1Metadata),
      );

      // Should detect because metadata has OpenAI indicators
      expect(detected).toBe(true);
    });

    test("example 2: should detect OpenAI format with input_text/output_text", () => {
      const detected = isOpenAIResponse(
        example2Input,
        example2Output,
        JSON.stringify(example2Metadata),
      );

      // Should detect because metadata has OpenAI indicators
      expect(detected).toBe(true);
    });

    test("example 2: try prompt scenario (input array, no output)", () => {
      const detected = isOpenAIResponse(
        example2Input,
        undefined,
        JSON.stringify(example2Metadata),
      );

      // Should detect because metadata has OpenAI indicators
      expect(detected).toBe(true);
    });

    test("example 2: should detect based on input_text content pattern without metadata", () => {
      const detected = isOpenAIResponse(example2Input, example2Output);

      // Should detect because input has input_text content type pattern
      expect(detected).toBe(true);
    });

    test("example 1: normalization with string input and metadata should work correctly", () => {
      // Test the original Example 1: string input with OpenAI output and metadata
      const result = openAIResponseNormalizer.normalize(
        example1Input,
        example1Output,
        JSON.stringify(example1Metadata),
      );

      // Should have system message from instructions + user message from string input
      expect(result.input).toHaveLength(2);
      expect((result.input as any)[0].role).toBe("system");
      expect((result.input as any)[0].content).toBe("Answer in one word.");
      expect((result.input as any)[1].role).toBe("user");
      expect((result.input as any)[1].content).toBe(
        "What is the capital of Japan?",
      );

      // Should have assistant message
      expect(result.output).toHaveLength(1);
      expect((result.output as any)[0].role).toBe("assistant");
      expect((result.output as any)[0].content).toBe("Tokyo.");

      // Should have transformed metadata
      const transformedMetadata = JSON.parse(result.metadata!);
      expect(transformedMetadata.response_format).toEqual({ type: "text" });
      expect(transformedMetadata.text).toBeUndefined();
    });

    test("example 2: normalization should work correctly", () => {
      const result = openAIResponseNormalizer.normalize(
        example2Input,
        example2Output,
        JSON.stringify(example2Metadata),
      );

      // Should have system message from instructions + user message
      expect(result.input).toHaveLength(2);
      expect((result.input as any)[0].role).toBe("system");
      expect((result.input as any)[0].content).toContain(
        "Classify the following",
      );
      expect((result.input as any)[1].role).toBe("user");
      expect((result.input as any)[1].content).toBe("Is this a question?");

      // Should have assistant message
      expect(result.output).toHaveLength(1);
      expect((result.output as any)[0].role).toBe("assistant");
      expect((result.output as any)[0].content).toBe("Question.");
    });
  });
});

describe("Web search", () => {
  const exampleInput =
    "What are the latest developments in artificial intelligence from this week? Please search the web for current AI news.";
  const exampleOutput = [
    {
      action: {
        query: "latest developments in artificial intelligence October 2023",
        type: "search",
      },
      id: "ws_68b20f25a22c819ebb9c5451493b80ea0cd61c76e8a46553",
      status: "completed",
      type: "web_search_call",
    },
    {
      content: [
        {
          annotations: [
            {
              end_index: 620,
              start_index: 468,
              title:
                "China's Alibaba develops new AI chip to help fill Nvidia void, WSJ reports",
              type: "url_citation",
              url: "https://www.reuters.com/world/china/chinas-alibaba-develops-new-ai-chip-help-fill-nvidia-void-wsj-reports-2025-08-29/?utm_source=openai",
            },
            {
              end_index: 1308,
              start_index: 1145,
              title:
                "Meta to add new AI safeguards after Reuters report raises teen safety concerns",
              type: "url_citation",
              url: "https://www.reuters.com/legal/litigation/meta-add-new-ai-safeguards-after-reuters-report-raises-teen-safety-concerns-2025-08-29/?utm_source=openai",
            },
            {
              end_index: 2097,
              start_index: 1640,
              title:
                "Reliance AGM 2025: Chairman Mukesh Ambani announces Reliance Intelligence, deepens partnership with Google, Meta",
              type: "url_citation",
              url: "https://timesofindia.indiatimes.com/technology/tech-news/reliance-agm-2025-chairman-mukesh-ambani-announces-reliance-intelligence-deepens-partnership-with-google-meta/articleshow/123582421.cms?utm_source=openai",
            },
            {
              end_index: 2097,
              start_index: 1640,
              title:
                "Reliance AGM 2025: Mukesh Ambani's key announcements on AI push in India",
              type: "url_citation",
              url: "https://economictimes.indiatimes.com/tech/artificial-intelligence/reliance-agm-2025-mukesh-ambanis-key-announcements-on-ai-push-in-india/articleshow/123582676.cms?utm_source=openai",
            },
            {
              end_index: 2647,
              start_index: 2403,
              title:
                "Cloudflare expands 402 payment protocol for AI crawler communication",
              type: "url_citation",
              url: "https://ppc.land/cloudflare-expands-402-payment-protocol-for-ai-crawler-communication/?utm_source=openai",
            },
            {
              end_index: 2647,
              start_index: 2403,
              title:
                "AI by AI Weekly Top 5 (August 18 – 24, 2025) – Champaign Magazine",
              type: "url_citation",
              url: "https://champaignmagazine.com/2025/08/24/ai-by-ai-weekly-top-5-august-18-24-2025/?utm_source=openai",
            },
            {
              end_index: 3082,
              start_index: 2998,
              title:
                "Introducing gpt-realtime and Realtime API updates for production voice agents",
              type: "url_citation",
              url: "https://openai.com/index/introducing-gpt-realtime/?utm_source=openai",
            },
            {
              end_index: 3521,
              start_index: 3390,
              title:
                "Artificial Intelligence News for the Week of August 22, 2025",
              type: "url_citation",
              url: "https://solutionsreview.com/artificial-intelligence-news-for-the-week-of-august-22-2025/?utm_source=openai",
            },
            {
              end_index: 3977,
              start_index: 3846,
              title:
                "AI’s Two-Day Tech Storm: Global Breakthroughs, Big Bets & Bold Moves (Aug 28–29, 2025)",
              type: "url_citation",
              url: "https://ts2.tech/en/ais-two-day-tech-storm-global-breakthroughs-big-bets-bold-moves-aug-28-29-2025/?utm_source=openai",
            },
            {
              end_index: 4543,
              start_index: 4412,
              title:
                "AI’s Two-Day Tech Storm: Global Breakthroughs, Big Bets & Bold Moves (Aug 28–29, 2025)",
              type: "url_citation",
              url: "https://ts2.tech/en/ais-two-day-tech-storm-global-breakthroughs-big-bets-bold-moves-aug-28-29-2025/?utm_source=openai",
            },
            {
              end_index: 4974,
              start_index: 4836,
              title:
                "AI Breakthroughs, Billion-Dollar Bets & Regulatory Showdowns – Aug 26–27, 2025 News Roundup",
              type: "url_citation",
              url: "https://ts2.tech/en/ai-breakthroughs-billion-dollar-bets-regulatory-showdowns-aug-26-27-2025-news-roundup/?utm_source=openai",
            },
            {
              end_index: 5455,
              start_index: 5317,
              title:
                "AI Breakthroughs, Billion-Dollar Bets & Regulatory Showdowns – Aug 26–27, 2025 News Roundup",
              type: "url_citation",
              url: "https://ts2.tech/en/ai-breakthroughs-billion-dollar-bets-regulatory-showdowns-aug-26-27-2025-news-roundup/?utm_source=openai",
            },
            {
              end_index: 5931,
              start_index: 5761,
              title:
                "Malaysia targets top 20 AI economy status by 2030 with new national roadmap",
              type: "url_citation",
              url: "https://www.malaymail.com/news/malaysia/2025/08/29/malaysia-targets-top-20-ai-economy-status-by-2030-with-new-national-roadmap/189251?utm_source=openai",
            },
            {
              end_index: 6345,
              start_index: 6201,
              title:
                "China doubles down on AI – but calls for rational, not ‘blind’, growth",
              type: "url_citation",
              url: "https://www.scmp.com/economy/china-economy/article/3323644/china-doubles-down-ai-calls-rational-not-blind-growth?utm_source=openai",
            },
          ],
          logprobs: [],
          text: "Here’s a comprehensive overview (around 700+ words) of the most significant developments in artificial intelligence from the week...",
          type: "output_text",
        },
      ],
      id: "msg_68b20f291d9c819e90c290cf95776ea10cd61c76e8a46553",
      role: "assistant",
      status: "completed",
      type: "message",
    },
  ];
  const exampleMetadata = JSON.stringify({
    background: false,
    created_at: 1756499748,
    error: null,
    id: "resp_68b20f24d044819e914175b78f68ba290cd61c76e8a46553",
    incomplete_details: null,
    instructions: null,
    max_output_tokens: null,
    max_tool_calls: null,
    metadata: {},
    model: "gpt-4o-2024-08-06",
    object: "response",
    output_text:
      "Here’s a comprehensive overview (around 700+ words) of the most recent developments in artificial intelligence from this week...",
    parallel_tool_calls: true,
    previous_response_id: null,
    prompt_cache_key: null,
    provider: "openai",
    reasoning: { effort: null, summary: null },
    safety_identifier: null,
    service_tier: "default",
    status: "completed",
    store: true,
    temperature: 1,
    text: { format: { type: "text" }, verbosity: "medium" },
    tool_choice: "auto",
    tools: [
      {
        filters: null,
        search_context_size: "medium",
        type: "web_search",
        user_location: {
          city: null,
          country: "US",
          region: null,
          timezone: null,
          type: "approximate",
        },
      },
    ],
    top_logprobs: 0,
    top_p: 1,
    truncation: "disabled",
    user: null,
  });

  test("Web search should be detected correctly", () => {
    const detected = isOpenAIResponse(
      exampleInput,
      exampleOutput,
      exampleMetadata,
    );

    // Should detect because metadata has OpenAI indicators
    expect(detected).toBe(true);
  });

  test("normalization with web search should work correctly", () => {
    const result = openAIResponseNormalizer.normalize(
      exampleInput,
      exampleOutput,
      exampleMetadata,
    );

    // Should have user message from string input
    expect(result.input).toHaveLength(1);
    expect((result.input as any)[0].role).toBe("user");
    expect((result.input as any)[0].content).toBe(exampleInput);

    // Should have assistant message with content and web search tool call
    expect(result.output).toHaveLength(2);

    // First should be assistant response with content
    expect((result.output as any)[0].role).toBe("assistant");
    expect((result.output as any)[0].content).toContain(
      "comprehensive overview",
    );

    // Second should be web search tool call
    expect((result.output as any)[1].role).toBe("assistant");
    expect((result.output as any)[1].content).toBe(null);
    expect((result.output as any)[1].tool_calls).toHaveLength(1);
    expect((result.output as any)[1].tool_calls[0].function.name).toBe(
      "web_search",
    );

    // Should have web_search tool definition
    expect(result.toolDefinitions?.has("web_search")).toBe(true);
  });
});

describe("Reasoning", () => {
  const exampleInput =
    "Solve this step by step: If a train travels 120 miles in 2 hours, and then increases its speed by 25% for the next 3 hours, how many total miles did it travel? Think through this carefully and show your reasoning step by step.";
  const exampleOutput = [
    {
      id: "rs_68b20f1f636c819fa42fbd287dbb0532055933e8366db5be",
      summary: [],
      type: "reasoning",
    },
    {
      content: [
        {
          annotations: [],
          logprobs: [],
          text: "Step 1: Find the train's initial speed...",
          type: "output_text",
        },
      ],
      id: "msg_68b20f222374819fbbef4fc1b43d503e055933e8366db5be",
      role: "assistant",
      status: "completed",
      type: "message",
    },
  ];
  const exampleMetadata = JSON.stringify({
    background: false,
    created_at: **********,
    error: null,
    id: "resp_68b20f1ca348819faf3586d1f8bc84cd055933e8366db5be",
    incomplete_details: null,
    instructions: null,
    max_output_tokens: null,
    max_tool_calls: null,
    metadata: {},
    model: "o4-mini-2025-04-16",
    object: "response",
    output_text: "Step 1: Find the train's initial speed...",
    parallel_tool_calls: true,
    previous_response_id: null,
    prompt_cache_key: null,
    provider: "openai",
    reasoning: { effort: "medium", summary: null },
    safety_identifier: null,
    service_tier: "default",
    status: "completed",
    store: true,
    temperature: 1,
    text: { format: { type: "text" }, verbosity: "medium" },
    tool_choice: "auto",
    tools: [],
    top_logprobs: 0,
    top_p: 1,
    truncation: "disabled",
    user: null,
  });

  test("Reasoning should be detected correctly", () => {
    const detected = isOpenAIResponse(
      exampleInput,
      exampleOutput,
      exampleMetadata,
    );

    expect(detected).toBe(true);
  });
});

describe("Reasoning blocks", () => {
  test("Reasoning blocks work with output processing", () => {
    const input = [
      {
        type: "message",
        role: "user",
        content: "List South American countries by Pacific capital population.",
      },
    ];
    const output = [
      {
        id: "rs_68bf36c2a150819fb04ab5ab7cea8dd30c31c6bbbc4ffbf9",
        status: null,
        type: "reasoning",
        summary: [
          {
            text: "**Estimating South American capitals**\n\nI'm considering the countries in South America...",
            type: "summary_text",
          },
          {
            text: "**Estimating capital populations**\n\nWait, so for Quito...",
            type: "summary_text",
          },
        ],
      },
      {
        content: [
          {
            annotations: [],
            logprobs: [],
            text: "Step 1: List all countries in South America...",
            type: "output_text",
          },
        ],
        id: "msg_68bf36d31e4c819f97322187fa06a1270c31c6bbbc4ffbf9",
        status: "completed",
        role: "assistant",
        type: "message",
      },
    ];
    const metadata = JSON.stringify({
      provider: "openai",
      model: "o4-mini-2025-04-16",
    });

    const result = openAIResponseNormalizer.normalize(
      input as any,
      output as any,
      metadata,
    );
    const expectedOutput = [
      {
        role: "assistant",
        content: "Step 1: List all countries in South America...",
        reasoning: [
          {
            content:
              "**Estimating South American capitals**\n\nI'm considering the countries in South America...",
          },
          {
            content:
              "**Estimating capital populations**\n\nWait, so for Quito...",
          },
        ],
      },
    ];

    expect(result.output).toEqual(expectedOutput);
  });

  test("No reasoning blocks added when no reasoning items", () => {
    const input = "Simple query";
    const output = [
      {
        type: "message",
        role: "assistant",
        content: [{ type: "output_text", text: "Simple answer" }],
      },
    ];
    const metadata = JSON.stringify({
      provider: "openai",
      model: "gpt-4",
    });

    const result = openAIResponseNormalizer.normalize(
      input as any,
      output as any,
      metadata,
    );
    expect((result.output as any).length).toBe(1);
    const first = (result.output as any)[0];
    expect(first.role).toBe("assistant");
    expect(first.content).toBe("Simple answer");
    expect(first.reasoning).toBeUndefined();
  });
});

describe("Standard Chat Completions format rejection", () => {
  // Test case for the false positive bug - standard Chat Completions format should be rejected
  const standardChatCompletionsInput = [
    {
      content: "You are",
      role: "system",
    },
    {
      content: "test",
      role: "user",
    },
    {
      content: "test",
      role: "user",
    },
    {
      role: "assistant",
      tool_calls: [
        {
          function: {
            arguments:
              '{"context":"The user has shared an image of their forearm with a visible bruise and tattoo, and is asking for an opinion about their hand.","question":"Could you clarify if you are seeking medical advice, or if you are asking for an opinion about the appearance of your hand in the image?"}',
            name: "ask-clarifying-question",
          },
          id: "call_6NVVB4ZIIASQgEY6Af484OvG",
          type: "function",
        },
      ],
    },
    {
      content: "test",
      role: "tool",
      tool_call_id: "call_6NVVB4ZIIASQgEY6Af484OvG",
    },
  ];

  const standardChatCompletionsMetadata = JSON.stringify({
    model: "gpt-4o-2024-11-20",
    prompt: {
      id: "f023946d-f3e5-4875-89e8-10220e2093db",
      project_id: "4542abd4-537e-4ca1-b5a4-14964fe42b78",
      prompt_session_id: "2ff67362-26a1-43e8-a0f6-d9e2f5ee7c64",
      variables: {
        transcript: "Oh, 3 interviews.",
      },
      version: "1000195729087936151",
    },
    stream: true,
    stream_options: {
      include_usage: true,
    },
    temperature: 0,
    tool_choice: "auto",
    tools: [
      {
        function: {
          description: "test",
          name: "ask-clarifying-question",
          parameters: {
            properties: {
              context: {
                default: "",
                title: "Context",
                type: "string",
              },
              question: {
                title: "Question",
                type: "string",
              },
            },
            required: ["question"],
            title: "AskClarifyingQuestionInput",
            type: "object",
          },
        },
        type: "function",
      },
    ],
  });

  test("should reject standard Chat Completions format to avoid false positives", () => {
    const detected = isOpenAIResponse(
      standardChatCompletionsInput,
      undefined,
      standardChatCompletionsMetadata,
    );

    // Should NOT detect this as OpenAI Agents format - this is standard Chat Completions
    expect(detected).toBe(false);
  });
});

describe("transformMetadataForChatCompletions", () => {
  test("should set reasoning_effort and verbosity when defined", () => {
    const metadata = JSON.stringify({
      reasoning: { effort: "medium" },
      text: { verbosity: "high" },
    });

    const result = transformMetadataForChatCompletions(metadata);
    const parsed = JSON.parse(result!);

    expect(parsed.reasoning_effort).toBe("medium");
    expect(parsed.verbosity).toBe("high");
  });

  test("should not set reasoning_effort or verbosity when undefined", () => {
    const metadata = JSON.stringify({});

    const result = transformMetadataForChatCompletions(metadata);
    const parsed = JSON.parse(result!);

    expect(parsed.reasoning_effort).toBeUndefined();
    expect(parsed.verbosity).toBeUndefined();
  });

  test("should not set reasoning_effort or verbosity when null", () => {
    const metadata = JSON.stringify({
      reasoning: { effort: null },
      text: { verbosity: null },
    });

    const result = transformMetadataForChatCompletions(metadata);
    const parsed = JSON.parse(result!);

    expect(parsed.reasoning_effort).toBeUndefined();
    expect(parsed.verbosity).toBeUndefined();
  });
});

describe("Image generation", () => {
  test("should detect and handle image generation responses", () => {
    const input =
      "Generate an image of a serene mountain landscape at sunset. Use the image generation tool to create this image.";
    const output = [
      {
        background: "opaque",
        id: "ig_68c079cc4710819face512f31f8f46300dc27ee90f24d0a3",
        output_format: "png",
        quality: "medium",
        result: {
          content_type: "image/png",
          filename: "serene_mountain_landscape_at_sunset.png",
          key: "d7658502-a4b1-4017-b955-dd62d95ac281",
          type: "braintrust_attachment",
        },
        revised_prompt: "serene mountain landscape at sunset",
        size: "1024x1024",
        status: "completed",
        type: "image_generation_call",
      },
      {
        content: [
          {
            annotations: [],
            logprobs: [],
            text: "Here is the serene mountain landscape at sunset.",
            type: "output_text",
          },
        ],
        id: "msg_68c079e4e8e8819faa0644af8f7ff9f60dc27ee90f24d0a3",
        role: "assistant",
        status: "completed",
        type: "message",
      },
    ];
    const metadata = JSON.stringify({
      background: false,
      created_at: **********,
      error: null,
      id: "resp_68c079cb952c819fac2d836bacbc73f00dc27ee90f24d0a3",
      incomplete_details: null,
      instructions: null,
      max_output_tokens: null,
      max_tool_calls: null,
      metadata: {},
      model: "gpt-4o-2024-08-06",
      object: "response",
      output_text: "Here is the serene mountain landscape at sunset.",
      parallel_tool_calls: true,
      previous_response_id: null,
      prompt_cache_key: null,
      provider: "openai",
      reasoning: {
        effort: null,
        summary: null,
      },
      safety_identifier: null,
      service_tier: "default",
      status: "completed",
      store: true,
      temperature: 1,
      text: {
        format: {
          type: "text",
        },
        verbosity: "medium",
      },
      tool_choice: "auto",
      tools: [
        {
          background: "auto",
          moderation: "auto",
          n: 1,
          output_compression: 100,
          output_format: "png",
          quality: "auto",
          size: "auto",
          type: "image_generation",
        },
      ],
      top_logprobs: 0,
      top_p: 1,
      truncation: "disabled",
      user: null,
    });

    // Test that isOpenAIResponse detects this format
    const detected = isOpenAIResponse(input, output, metadata);
    expect(detected).toBe(true);

    // Test normalization handles image generation output correctly
    const result = openAIResponseNormalizer.normalize(input, output, metadata);

    // Should have user message from string input
    expect(result.input).toHaveLength(1);
    expect((result.input as any)[0].role).toBe("user");
    expect((result.input as any)[0].content).toBe(input);

    // Should have assistant message with the text content
    expect(result.output).toHaveLength(1);
    expect((result.output as any)[0].role).toBe("assistant");
    expect((result.output as any)[0].content).toBe(
      "Here is the serene mountain landscape at sunset.",
    );

    // Metadata should be properly transformed
    const transformedMetadata = JSON.parse(result.metadata!);
    expect(transformedMetadata.response_format).toEqual({ type: "text" });
  });
});

describe("New failure case", () => {
  test("should detect OpenAI response with reasoning-only output and handle conversion properly", () => {
    const input = [
      {
        content: "Content to be evaluated",
        role: "system",
      },
      {
        content:
          "EVALUATE THIS CONTENT:\n<User>Optimize the prompts in this playground</User>\n<Assistant>I'll help you optimize the prompts in this playground. Let me start by getting an overview of the current setup.</Assistant>\n<Assistant>Now let me examine some results to understand the current performance and identify optimization opportunities.</Assistant>\n<Assistant>I can see several issues with the current prompt that are causing performance problems:\n\n1. **Changelog Formatting Scorer is consistently 0** - The prompt doesn't specify the expected format clearly\n2. **Some results show the LLM asking for clarification** instead of generating changelogs\n3. **The prompt lacks specific structure guidance** for the changelog format\n4. **No handling for edge cases** like empty commits or missing data\n\nLet me look at the expected output format to understand what structure is needed:</Assistant>\n<Assistant>Now let me run the task to see how the improved prompt performs:</Assistant>\n<Assistant>Excellent! The improvements are significant:\n\n- **Changelog Accuracy Scorer**: Improved from 0.875 to 0.938 (****%)\n- **Changelog Completeness Scorer**: Perfect 1.0 (maintained)\n- **Changelog Formatting Scorer**: Dramatically improved from 0 to 0.875 (+87.5%)\n\nLet me check a few results to see the quality improvements:</Assistant>\n<Assistant>Perfect! The optimized prompt is working very well. Let me check one edge case - the empty commits scenario:</Assistant>\n<Assistant>Let me check the edge case with empty commits:</Assistant>",
        role: "user",
      },
    ];
    const output = [
      {
        id: "rs_07674fa1f043ac9f0068c481908edc81909ec9303ddb5940ea",
        summary: [
          {
            text: "aaaaa",
            type: "summary_text",
          },
          {
            text: "aaaa",
            type: "summary_text",
          },
          {
            text: "aaaaa",
            type: "summary_text",
          },
        ],
        type: "reasoning",
      },
    ];
    const metadata = {
      max_output_tokens: 2000,
      model: "gpt-5-nano",
      reasoning: {
        effort: "high",
        summary: "auto",
      },
      seed: 123,
      stream: false,
      text: {
        format: {
          type: "json_schema",
          json_schema: {
            name: "evaluation_result",
            schema: {
              type: "object",
              properties: {
                score: { type: "number" },
                feedback: { type: "string" },
              },
              required: ["score", "feedback"],
            },
          },
        },
        verbosity: "low",
      },
    };

    // Test that isOpenAIResponse detects this format
    const detected = isOpenAIResponse(input, output, JSON.stringify(metadata));

    expect(detected).toBe(true);

    // Test normalization handles reasoning-only output correctly
    const result = openAIResponseNormalizer.normalize(
      input,
      output,
      JSON.stringify(metadata),
    );

    // Should have input messages
    expect(result.input).toHaveLength(2);
    expect((result.input as any)[0].role).toBe("system");
    expect((result.input as any)[1].role).toBe("user");

    // Should have output with reasoning blocks even though there's no message content
    expect(result.output).toHaveLength(1);
    expect((result.output as any)[0].role).toBe("assistant");
    expect((result.output as any)[0].content).toBe("");
    expect((result.output as any)[0].reasoning).toHaveLength(3);
    expect((result.output as any)[0].reasoning[0].content).toBe("aaaaa");

    // Test metadata transformation handles json_schema correctly
    const transformedMetadata = JSON.parse(result.metadata!);
    expect(transformedMetadata.response_format).toEqual({
      type: "json_schema",
      json_schema: {
        name: "evaluation_result",
        schema: {
          type: "object",
          properties: {
            score: { type: "number" },
            feedback: { type: "string" },
          },
          required: ["score", "feedback"],
        },
      },
    });
    expect(transformedMetadata.text).toBeUndefined();
    expect(transformedMetadata.reasoning_effort).toBe("high");
    expect(transformedMetadata.verbosity).toBe("low");
  });
});
