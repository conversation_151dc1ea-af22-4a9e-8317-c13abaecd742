export const scrollIntoViewWithMargin = (
  el: HTMLElement,
  container: HTMLElement | null,
  scrollTopMarginPx: number = 12,
) => {
  if (!container) {
    el.scrollIntoView({ behavior: "smooth", block: "start" });
    return;
  }
  const containerRect = container.getBoundingClientRect();
  const elRect = el.getBoundingClientRect();
  const deltaTop = elRect.top - containerRect.top;
  container.scrollTo({
    top: container.scrollTop + deltaTop - scrollTopMarginPx,
    behavior: "smooth",
  });
};
