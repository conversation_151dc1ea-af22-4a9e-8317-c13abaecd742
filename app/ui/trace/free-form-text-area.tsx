import { type TransactionId } from "braintrust/util";
import { type ConfiguredScore } from "@braintrust/local";
import TextArea, { type TextAreaProps } from "#/ui/text-area";
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  memo,
  useEffect,
} from "react";
import { useOptimisticState } from "#/utils/optimistic-update";
import { type DiscriminatedProjectScore } from "#/utils/score-config";
import { parseStringOnlyIdentPath } from "#/utils/btql/path-helpers";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
export interface FreeFormTextAreaHandle {
  getValue(): string;
  save(value: string): Promise<void>;
  focus: VoidFunction;
}

interface FreeFormTextAreaProps {
  score: ConfiguredScore;
  rowKey?: string;
  xactId: TransactionId | null;
  onAutoSave?: (value: string) => Promise<TransactionId | null>;
}

export const FreeFormTextArea = memo(
  forwardRef<FreeFormTextAreaHandle, FreeFormTextAreaProps & TextAreaProps>(
    ({ score, rowKey, xactId, onAutoSave, ...props }, ref) => {
      const [value, setValue] = useState<string | undefined>(score.freeForm);

      const autoSaveOrNoop = useMemo(
        () => (onAutoSave ? onAutoSave : () => Promise.resolve(null)),
        [onAutoSave],
      );

      const optimisticSave = useCallback(
        async (newValue: string) => {
          setValue(newValue);
          return autoSaveOrNoop(newValue);
        },
        [autoSaveOrNoop],
      );

      const { save, value: initValue } = useOptimisticState({
        rowKey,
        xactId,
        value: score.freeForm,
        save: optimisticSave,
      });

      useEffect(() => {
        setValue((v) => {
          if (
            initValue !== v &&
            // This logic roughly resembles what we do in TextEditor, which is to
            // ignore 3rd party updates while we have focus on the editor block
            !inputRef.current?.contains(document.activeElement)
          ) {
            return initValue;
          }
          return v;
        });
      }, [initValue]);

      const debouncedAutoSave = useDebouncedCallback(autoSaveOrNoop, 500);
      const debouncedSave = useCallback(
        (newValue: string) => {
          setValue(newValue);
          debouncedAutoSave(newValue);
        },
        [debouncedAutoSave, setValue],
      );

      const inputRef = useRef<HTMLTextAreaElement>(null);
      useImperativeHandle(ref, () => ({
        focus: () => {
          inputRef.current?.focus();
        },
        save,
        getValue: () => value ?? "",
      }));

      return (
        <TextArea
          ref={inputRef}
          placeholder="Enter value"
          value={value ?? ""}
          onBlur={(e) => {
            if (e.target.value === score.freeForm) return;
            save(e.target.value);
          }}
          onChange={(e) => {
            debouncedSave(e.target.value);
          }}
          {...props}
        />
      );
    },
  ),
);
FreeFormTextArea.displayName = "FreeFormTextArea";

export function freeFormDataPath({
  scoreType,
  destination,
  name,
}: {
  scoreType: DiscriminatedProjectScore["score_type"];
  destination?: string | null;
  name: string;
}): string[] {
  if (scoreType !== "free-form") {
    throw new Error("Score is not free-form");
  }

  if (destination) {
    return parseStringOnlyIdentPath(destination);
  } else {
    return ["metadata", name];
  }
}
