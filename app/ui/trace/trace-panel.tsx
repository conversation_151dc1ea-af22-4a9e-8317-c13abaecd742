"use client";

import { memo, useCallback, useContext, useEffect } from "react";
import { type TraceViewParams, TraceViewer } from "#/ui/trace/trace";
import { makeRowIdPrimary } from "#/utils/diffs/diff-objects";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { type RowId } from "#/utils/diffs/diff-objects";
import {
  useActiveRowAndSpan,
  useActiveCommentId,
  useHumanReviewState,
} from "#/ui/query-parameters";
import { HotkeyScope } from "#/ui/hotkeys";
import { useTraceFullscreen } from "./use-trace-fullscreen";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { TraceSearchProvider } from "./trace-search-context";
import { useRowNavigation } from "./use-row-navigation";
import { useQueryClient } from "@tanstack/react-query";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { makeFullSpanQueryFn } from "./use-load-full-spans";
import {
  makeFullSpanQueryKey,
  makeTraceQueryFn,
  makeTraceQueryKey,
} from "./loading/query-utils";
import { useFetchBtqlOptions } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { useIsFeatureEnabled } from "#/lib/feature-flags";

/**
 * In order to allow Sidepanel have access to duck DB and run queries
 * It should have OrgProvider (to get user context)
 * and SessionRoot (to get duckdb context)
 */
export const TracePanel = memo(
  ({
    rowIds = [],
    traceViewParams,
    onApplySearch,
    isReadOnly,
  }: {
    rowIds: RowId[];
    traceViewParams: TraceViewParams;
    onApplySearch: ApplySearch;
    isReadOnly?: boolean;
  }) => {
    const { enableScope, disableScope, enabledScopes } = useHotkeysContext();
    useEffect(() => {
      enableScope("sidepanel");
      return () => {
        disableScope("sidepanel");
      };
    }, [enableScope, disableScope]);

    const objectType = traceViewParams.objectType;
    const objectId = traceViewParams.expandedRowParams?.primaryDynamicObjectId;
    const {
      activeRowId,
      rowIdx,
      hasNextRow,
      hasPrevRow,
      onNextRow,
      onPrevRow,
      jumpToRow,
      isDelayedSpanChangeTransitioning,
      startSpanChangeTransition,
    } = useRowNavigation({ rowIds, objectType, objectId });

    useHotkeys(
      ["j", "ArrowDown"],
      () => onNextRow({ withTransition: true }),
      {
        scopes: ["sidepanel"],
        description: "Move to the next row in the table",
        preventDefault: true,
      },
      [onNextRow],
    );
    useHotkeys(
      ["k", "ArrowUp"],
      () => onPrevRow({ withTransition: true }),
      {
        scopes: ["sidepanel"],
        description: "Move to the previous row in the table",
        preventDefault: true,
      },
      [onPrevRow],
    );

    const fullyCloseSidePanel = useFullyCloseSidePanel();

    useHotkeys(
      ["Escape"],
      fullyCloseSidePanel,
      {
        scopes: ["sidepanel"],
        description: "Close the side panel.",
        enabled: ![
          HotkeyScope.GlobalKeyboardShortcuts,
          HotkeyScope.PromptModal,
          HotkeyScope.ConfirmationModal,
          HotkeyScope.ExpandedTrace,
          HotkeyScope.ExpandedFrame,
          HotkeyScope.HumanReview,
        ].some((scope) => enabledScopes.includes(scope)),
      },
      [fullyCloseSidePanel],
    );

    const [humanReviewState] = useHumanReviewState();
    const isHumanReviewExpanded = humanReviewState === "1";
    const queryClient = useQueryClient();

    const { projectId } = useContext(ProjectContext);
    const customColumns =
      traceViewParams.expandedRowParams?.customColumnsParams?.customColumns;
    const builder = useBtqlQueryBuilder({});
    const btqlOptions = useFetchBtqlOptions();
    const fastExperimentSummary = useIsFeatureEnabled("fastExperimentSummary");
    const loadCustomColumnsFromScope = useIsFeatureEnabled(
      "loadCustomColumnsFromScope",
    );
    // prefetch the adjacent rows for human review
    useEffect(() => {
      if (!isHumanReviewExpanded || !objectType || !objectId) {
        return;
      }

      const rowIdsToPrefetch = rowIds.slice(
        Math.max(0, rowIdx - 2),
        Math.min(rowIds.length, rowIdx + 5),
      );
      rowIdsToPrefetch.forEach((rowId) => {
        const primaryRowId = makeRowIdPrimary(rowId);
        if (!primaryRowId) {
          return;
        }
        queryClient.prefetchQuery({
          queryKey: makeFullSpanQueryKey(
            objectType,
            objectId,
            projectId,
            primaryRowId,
            true,
            customColumns,
          ),
          queryFn: makeFullSpanQueryFn(
            builder,
            btqlOptions,
            fastExperimentSummary,
            loadCustomColumnsFromScope,
            customColumns,
          ),
          staleTime: 5 * 60 * 1000,
        });
        const traceQueryParams = {
          objectType,
          objectId,
          traceRowId: primaryRowId,
        };
        queryClient.prefetchQuery({
          queryKey: makeTraceQueryKey(traceQueryParams),
          queryFn: makeTraceQueryFn(traceQueryParams, builder, btqlOptions),
          staleTime: 5 * 60 * 1000,
        });
      });
    }, [
      isHumanReviewExpanded,
      objectType,
      objectId,
      projectId,
      rowIds,
      rowIdx,
      customColumns,
      queryClient,
      builder,
      btqlOptions,
      fastExperimentSummary,
      loadCustomColumnsFromScope,
    ]);

    if (!activeRowId) {
      return null;
    }

    return (
      <TraceSearchProvider>
        <TraceViewer
          rowId={activeRowId}
          traceViewParams={traceViewParams}
          onClose={fullyCloseSidePanel}
          totalRows={rowIds.length}
          rowIndex={rowIdx + 1}
          firstRowId={rowIds[0]}
          onPrevRow={hasPrevRow ? onPrevRow : undefined}
          onNextRow={hasNextRow ? onNextRow : undefined}
          onJumpToRow={jumpToRow}
          onApplySearch={onApplySearch}
          isReadOnly={isReadOnly}
          isDelayedSpanChangeTransitioning={isDelayedSpanChangeTransitioning}
          startSpanChangeTransition={startSpanChangeTransition}
        />
      </TraceSearchProvider>
    );
  },
);

TracePanel.displayName = "TracePanel";

export function useFullyCloseSidePanel() {
  const [_activeRowAndSpan, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_activeCommentId, setFocusedCommentId] = useActiveCommentId();
  const { setFullscreenState } = useTraceFullscreen();
  return useCallback(() => {
    setActiveRowAndSpan({ r: null, s: null });
    setFocusedCommentId(null);
    setFullscreenState(null);
  }, [setActiveRowAndSpan, setFullscreenState, setFocusedCommentId]);
}
