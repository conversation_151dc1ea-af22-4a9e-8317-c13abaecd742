import {
  type SetStateAction,
  type TransitionStartFunction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  useActiveCommentId,
  useActiveRowAndSpan,
  useScrollTo,
} from "#/ui/query-parameters";
import { type LoadedTrace, type Span, type Trace } from "./graph";
import { type MultiTraceContext } from "#/ui/copilot/trace";

type Args = {
  copilotContext: MultiTraceContext;
  startSpanChangeTransition: TransitionStartFunction;
  trace: Trace | null;
  loadedTrace: LoadedTrace | null;
  relatedRowIds?: { id: string; root_span_id: string }[];
  comparisonTrace?: Trace | null;
  skipUpdateUrl?: boolean;
};

export default function useSpanSelection({
  copilotContext,
  startSpanChangeTransition,
  trace,
  loadedTrace,
  relatedRowIds,
  comparisonTrace,
  skipUpdateUrl,
}: Args) {
  const [{ s: activeSpanId, r: activeRowId }, setActiveRowAndSpan] =
    useActiveRowAndSpan();
  const [scrollToSpanId, setScrollToSpanId] = useState<string | null>(null);
  const [scrollTo, _setScrollTo] = useScrollTo();
  const setScrollTo = useCallback(
    (args: { spanId: string; v: SetStateAction<string | null> } | null) => {
      if (args == null) {
        setScrollToSpanId(null);
        _setScrollTo(null);
        return;
      }
      const { spanId, v } = args;
      setScrollToSpanId(spanId);
      _setScrollTo(v);
    },
    [setScrollToSpanId, _setScrollTo],
  );
  const [_, setFocusedComment] = useActiveCommentId();

  const lastTrace = useRef<LoadedTrace | null>(null);
  const lastSpanPath = useRef<number[]>([]);

  const setSelectedSpan = useCallback(
    (spanOrSpanId: Span | string, rootSpanId?: string) => {
      startSpanChangeTransition(() => {
        lastTrace.current = loadedTrace;
        const spanId =
          typeof spanOrSpanId === "string"
            ? spanOrSpanId
            : spanOrSpanId.span_id;
        const rootSpanIdResolved =
          typeof spanOrSpanId === "string"
            ? rootSpanId
            : spanOrSpanId.root_span_id;
        if (spanId !== activeSpanId) {
          const rootSpanRowId = relatedRowIds?.find(
            ({ root_span_id }) =>
              root_span_id === rootSpanIdResolved ||
              root_span_id === rootSpanId,
          )?.id;
          setActiveRowAndSpan({
            s: spanId,
            ...(rootSpanRowId && activeRowId !== rootSpanRowId
              ? { r: rootSpanRowId }
              : {}),
          });
          setFocusedComment(null);
          setScrollTo(null);
        }

        let spanPath = [];
        const span =
          typeof spanOrSpanId === "string"
            ? loadedTrace?.spans[spanOrSpanId]
            : spanOrSpanId;
        let curr = span;
        while (
          loadedTrace &&
          curr?.parent_span_id &&
          loadedTrace.spans[curr?.parent_span_id]
        ) {
          const idx = loadedTrace.spans[curr.parent_span_id].children.findIndex(
            (s) => s.span_id === curr?.span_id,
          );
          if (idx >= 0) {
            spanPath.push(idx);
          } else {
            // If the element isn't found, just reset the path and don't apply this heuristic
            spanPath = [];
            break;
          }
          curr = loadedTrace.spans[curr.parent_span_id];
        }
        spanPath.reverse();
        lastSpanPath.current = spanPath;
        if (span) {
          copilotContext.visitSpan(span);
        }
      });
    },
    [
      activeRowId,
      activeSpanId,
      setActiveRowAndSpan,
      setScrollTo,
      loadedTrace,
      copilotContext,
      startSpanChangeTransition,
      setFocusedComment,
      relatedRowIds,
    ],
  );

  const hasValidActiveSpan =
    activeSpanId &&
    (trace?.spans[activeSpanId] || comparisonTrace?.spans[activeSpanId]);
  useEffect(() => {
    if (!trace || hasValidActiveSpan || skipUpdateUrl) {
      // If the trace is not loaded, or the active span is already in the trace, do nothing
      return;
    }

    if (lastSpanPath.current?.length > 0) {
      let curr = trace.root;
      for (const idx of lastSpanPath.current) {
        if (!curr.children[idx]) {
          break;
        }
        curr = curr.children[idx];
      }
      setActiveRowAndSpan({ s: curr.span_id });
    } else {
      setActiveRowAndSpan({ s: trace.root.span_id });
    }
  }, [
    skipUpdateUrl,
    activeSpanId,
    setActiveRowAndSpan,
    trace,
    hasValidActiveSpan,
  ]);

  return {
    activeSpanId,
    setSelectedSpan,
    scrollTo,
    scrollToSpanId,
    setScrollTo,
  };
}
