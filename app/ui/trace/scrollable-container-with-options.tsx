import { type RefObject, useCallback, useEffect, useState } from "react";
import { But<PERSON> } from "#/ui/button";
import { ArrowDown, ArrowUp, Menu } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { SpanField } from "./use-span-field-order.tsx";

const defaultTocIds = [
  { id: `${SpanField.SCORES}-section`, label: "Scores" },
  { id: `${SpanField.HUMAN_REVIEW}-section`, label: "Human review" },
  { id: `${SpanField.INPUT}-section`, label: "Input" },
  { id: `${SpanField.OUTPUT}-section`, label: "Output" },
  { id: `${SpanField.EXPECTED}-section`, label: "Expected" },
  {
    id: `${SpanField.OUTPUT_VS_EXPECTED}-section`,
    label: "Output vs. expected",
  },
  { id: `${SpanField.METADATA}-section`, label: "Metadata" },
];

export const ScrollableContainerWithOptions = ({
  className,
  children,
  threshold = 1500,
  tocIds = defaultTocIds,
  containerRef,
  hideOptions,
  enableScrollToBottom,
  enableScrollToTop = true,
}: {
  className?: string;
  children: React.ReactNode;
  threshold?: number;
  tocIds?: { id: string; label: string }[];
  containerRef: RefObject<HTMLDivElement | null>;
  hideOptions?: boolean;
  enableScrollToBottom?: boolean;
  enableScrollToTop?: boolean;
}) => {
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [tocItems, setTocItems] = useState<
    { id: string; label: string; available: boolean }[]
  >([]);

  useEffect(() => {
    const checkToc = () => {
      setTocItems(
        tocIds.map((t) => ({
          ...t,
          available: !!document.querySelector(selector(t.id)),
        })),
      );
    };

    const observer = new MutationObserver(checkToc);
    observer.observe(document.body, { childList: true, subtree: true });
    checkToc();
    return () => observer.disconnect();
  }, [tocIds]);

  const onScroll = useCallback(() => {
    if (!containerRef.current) return;
    if (!enableScrollToTop && !enableScrollToBottom) return;
    const currentScroll = containerRef.current.scrollTop;
    const containerHeight = containerRef.current.clientHeight;
    const totalHeight = containerRef.current.scrollHeight;

    if (enableScrollToTop) {
      if (showScrollToTop && currentScroll < threshold) {
        setShowScrollToTop(false);
      } else if (!showScrollToTop && currentScroll > threshold) {
        setShowScrollToTop(true);
      }
    }

    if (enableScrollToBottom) {
      const distanceFromBottom =
        totalHeight - (currentScroll + containerHeight);
      const shouldShowScrollToBottom = distanceFromBottom > 100;

      if (showScrollToBottom !== shouldShowScrollToBottom) {
        setShowScrollToBottom(shouldShowScrollToBottom);
      }
    }
  }, [
    containerRef,
    enableScrollToBottom,
    enableScrollToTop,
    showScrollToTop,
    threshold,
    showScrollToBottom,
  ]);

  useEffect(onScroll, [onScroll]);

  const scrollToTop = () => {
    containerRef.current?.scrollTo({ top: 0, behavior: "smooth" });
  };

  const scrollToBottom = () => {
    containerRef.current?.scrollTo({
      top: containerRef.current?.scrollHeight,
      behavior: "smooth",
    });
  };

  const showToc =
    !hideOptions && tocItems.filter((t) => t.available).length > 0;

  return (
    <>
      <div ref={containerRef} onScroll={onScroll} className={className}>
        {children}
      </div>
      <div className="absolute right-4 bottom-5 flex flex-col gap-2">
        {showScrollToTop && (
          <Button onClick={scrollToTop} className="size-7 bg-background p-0">
            <ArrowUp className="size-3" />
          </Button>
        )}
        {showToc && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="size-7 bg-background p-0">
                <Menu className="size-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="left" align="end">
              <DropdownMenuLabel>Jump to</DropdownMenuLabel>
              {tocItems
                .filter((item) => item.available)
                .map((item, idx) => (
                  <DropdownMenuItem
                    hidden={!item.available}
                    key={idx}
                    onSelect={() => scrollIdIntoView(item.id)}
                  >
                    {item.label}
                  </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {showScrollToBottom && enableScrollToBottom && (
          <Button
            onClick={scrollToBottom}
            className="size-7 bg-background p-0"
            Icon={ArrowDown}
          />
        )}
      </div>
    </>
  );
};

const selector = (id: string) => `[data-scroll-id="${id}"]`;
export const selectorForField = (field: string) =>
  `[data-scroll-id="${field}-section"]`;

const scrollIdIntoView = (id: string) => {
  document.querySelectorAll(selector(id))?.forEach((el) => {
    el.scrollIntoView({ behavior: "smooth" });
  });
};
