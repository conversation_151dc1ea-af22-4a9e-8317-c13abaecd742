import { type Span } from "@braintrust/local";
import { Info } from "lucide-react";

export const ParentSpanNotFound = ({
  selectedSpan,
}: {
  selectedSpan: Span;
}) => {
  if (
    !Boolean(
      selectedSpan &&
        selectedSpan.span_parents?.length &&
        !selectedSpan.parent_span_id,
    )
  ) {
    return null;
  }

  return (
    <div className="rounded-md border bg-primary-50 p-2 text-xs">
      <div className="mb-0.5 flex items-center gap-1 font-medium">
        <Info className="size-3 flex-none text-primary-500" />
        Parent span not found. This span will appear under the root span.
      </div>
      <div className="text-primary-700">
        This can occur if the parent span is logged in a different process and
        has not yet hit the backend. Please check back again later and report to{" "}
        <a
          href="mailto:<EMAIL>"
          className="font-medium text-accent-700"
        >
          <EMAIL>
        </a>{" "}
        if the issue persists.
      </div>
    </div>
  );
};
