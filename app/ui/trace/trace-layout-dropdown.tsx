import { MessagesSquare } from "lucide-react";
import { type ReactNode, useContext, useEffect } from "react";

import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
} from "#/ui/dropdown-menu";
import { useDiffModeState, useTraceViewTypeState } from "#/ui/query-parameters";
import { ListTree, SquareChartGantt } from "lucide-react";
import { useTraceSearch } from "./trace-search-context";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

export const TraceLayoutDropdown = ({
  label = "Trace",
  appendedLabel,
  className,
  variant = "dropdown",
}: {
  label?: ReactNode;
  appendedLabel?: string;
  className?: string;
  variant?: "dropdown" | "tabs";
}) => {
  const { projectId } = useContext(ProjectContext);
  const [viewType, setViewType] = useTraceViewTypeState(projectId);
  const { setSearchOpen } = useTraceSearch();
  const [diffModeState] = useDiffModeState();

  const isDiffMode = diffModeState.enabled;

  // If we somehow end up in diff mode while viewing the thread, switch to trace
  useEffect(() => {
    if (isDiffMode && viewType === "thread") {
      setViewType("trace");
    }
  }, [isDiffMode, viewType, setViewType]);

  if (variant === "tabs") {
    return (
      <div className="flex flex-1 gap-1">
        <Button
          size="xs"
          variant={viewType === "trace" ? "default" : "ghost"}
          Icon={ListTree}
          onClick={() => setViewType("trace")}
        >
          Trace
        </Button>
        <Button
          size="xs"
          variant={viewType === "timeline" ? "default" : "ghost"}
          Icon={SquareChartGantt}
          onClick={() => {
            setViewType("timeline");
            setSearchOpen(false);
          }}
        >
          Timeline
        </Button>
        <Button
          size="xs"
          variant={viewType === "thread" ? "default" : "ghost"}
          Icon={MessagesSquare}
          onClick={() => {
            setViewType("thread");
            setSearchOpen(false);
          }}
        >
          Thread
        </Button>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown className={className}>
          {viewType === "timeline"
            ? "Timeline"
            : viewType === "thread"
              ? "Thread"
              : label}
          {appendedLabel}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuCheckboxItem
          checked={viewType === "trace"}
          className="gap-2"
          onSelect={() => {
            setViewType("trace");
          }}
        >
          <ListTree className="size-3" />
          {label}
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={viewType === "timeline"}
          className="gap-2"
          onSelect={() => {
            setViewType("timeline");
            setSearchOpen(false);
          }}
        >
          <SquareChartGantt className="size-3" />
          Timeline
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          disabled={isDiffMode}
          checked={viewType === "thread"}
          className="gap-2"
          onSelect={() => {
            setViewType("thread");
            setSearchOpen(false);
          }}
        >
          <MessagesSquare className="size-3" />
          Thread
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
