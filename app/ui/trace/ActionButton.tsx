"use client";
import React, { forwardRef } from "react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipPortal,
} from "#/ui/tooltip";
import { renderHotkey } from "#/utils/hotkeys";
import { type Keys, useHotkeys } from "react-hotkeys-hook";
import { Button, type ButtonProps } from "#/ui/button";
import { cn } from "#/utils/classnames";

type ActionButtonProps = {
  icon?: React.ReactNode;
  label?: React.ReactNode;
  hotkey: Keys;
  tooltipText: string;
  actionHandler: () => void;
  hotkeyScopes?: string[];
  buttonVariant?: ButtonProps["variant"];
  className?: string;
  disabled?: boolean;
  labelClassName?: string;
};

export const ActionButton = forwardRef<HTMLButtonElement, ActionButtonProps>(
  (
    {
      icon,
      label,
      actionHandler,
      tooltipText,
      hotkey,
      hotkeyScopes = ["sidepanel"],
      buttonVariant,
      className,
      disabled,
      labelClassName,
    },
    ref,
  ) => {
    useHotkeys(
      hotkey,
      (e) => {
        e.preventDefault();
        actionHandler();
      },
      {
        description: tooltipText,
        splitKey: ";",
        scopes: hotkeyScopes,
      },
    );

    return (
      <Tooltip disableHoverableContent>
        <TooltipTrigger asChild>
          <Button
            ref={ref}
            variant={buttonVariant}
            className={cn("flex gap-1", className)}
            size="xs"
            onClick={actionHandler}
            disabled={disabled}
          >
            <span className="flex-none">{icon}</span>
            {label && (
              <span className={cn("flex-1 truncate", labelClassName)}>
                {label}
              </span>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="bottom" className="text-xs">
            {tooltipText}
            {hotkey ? (
              <span className="ml-2.5 inline-block opacity-50">
                {renderHotkey(hotkey)}
              </span>
            ) : null}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    );
  },
);
ActionButton.displayName = "ActionButton";
