import {
  type DiffObjectType,
  isDiffObject,
  type RowId,
} from "#/utils/diffs/diff-objects";
import { Combobox } from "#/ui/combobox/combobox";
import { cn } from "#/utils/classnames";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { But<PERSON> } from "#/ui/button";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { useCallback, useEffect } from "react";

export const RowComparisonSelection = ({
  rowId,
  selectableExperimentsList,
  selectedExperimentId,
  onExperimentSelected,
  comparisonClassName,
  comparableRowIds,
  comparisonRowId,
  onCompareRowIdSelected,
}: {
  rowId: string | DiffObjectType<string>;
  selectableExperimentsList?: { id: string; name: string }[];
  selectedExperimentId?: string;
  onExperimentSelected: (id: string | null) => void;
  comparisonClassName: string;
  comparableRowIds: string[];
  comparisonRowId: string;
  onCompareRowIdSelected: (id: string) => void;
}) => {
  const { enableScope, disableScope } = useHotkeysContext();
  useEffect(() => {
    enableScope("trace-trials");
    return () => {
      disableScope("trace-trials");
    };
  }, [enableScope, disableScope]);

  const index = Math.max(
    comparableRowIds.findIndex((id) => comparisonRowId === id),
    0,
  );

  const prevRow = useCallback(() => {
    if (index > 0) {
      onCompareRowIdSelected(comparableRowIds[index - 1]);
    }
  }, [index, comparableRowIds, onCompareRowIdSelected]);

  const nextRow = useCallback(() => {
    if (index < comparableRowIds.length - 1) {
      onCompareRowIdSelected(comparableRowIds[index + 1]);
    }
  }, [index, comparableRowIds, onCompareRowIdSelected]);

  useHotkeys(
    ["ArrowLeft"],
    prevRow,
    {
      scopes: ["trace-trials"],
      description: "Compare with the previous row in the comparison experiment",
      preventDefault: true,
    },
    [prevRow],
  );

  useHotkeys(
    ["ArrowRight"],
    nextRow,
    {
      scopes: ["trace-trials"],
      description: "Compare with the next row in the comparison experiment",
      preventDefault: true,
    },
    [nextRow],
  );

  if (!isDiffModeRowId(rowId)) {
    return null;
  }

  const showRowSelector = comparableRowIds.length > 0;
  const showExperimentSelector =
    (selectableExperimentsList && selectableExperimentsList.length > 1) ||
    showRowSelector;
  if (!showExperimentSelector && !showRowSelector) {
    return null;
  }

  return (
    <div className="flex flex-wrap items-center justify-between gap-y-1.5 border-b px-2 py-1.5 text-xs">
      {showExperimentSelector && (
        <Combobox
          options={(selectableExperimentsList ?? []).map((e) => ({
            value: e.id,
            label: e.name,
          }))}
          searchPlaceholder="Compare with experiment"
          noResultsLabel="No experiments found"
          variant="button"
          buttonSize="xs"
          buttonVariant="ghost"
          align="start"
          iconClassName="text-primary-500"
          placeholderClassName="font-medium text-xs"
          selectedValue={
            selectedExperimentId ?? selectableExperimentsList?.[0]?.id
          }
          onChange={(value) => {
            onExperimentSelected(value ?? null);
          }}
          placeholderLabel="select an experiment"
          renderComboboxDisplayLabel={({ label, value }) => (
            <span className="inline-flex items-center gap-2">
              <span className="font-normal">Comparing trace with </span>
              <span
                className={cn(
                  "block size-1.5 flex-none rounded-full",
                  comparisonClassName,
                )}
              />
              {label}
            </span>
          )}
        />
      )}
      {showRowSelector && (
        <div className="flex h-7 gap-1 px-2">
          <span className="self-center pr-1">
            <span className="font-normal text-primary-600">trial row</span>{" "}
            {index + 1} of {comparableRowIds.length}
          </span>
          <Button
            size="xs"
            Icon={ArrowLeft}
            variant="ghost"
            disabled={index === 0}
            onClick={prevRow}
          />
          <Button
            size="xs"
            Icon={ArrowRight}
            variant="ghost"
            disabled={index === comparableRowIds.length - 1}
            onClick={nextRow}
          />
        </div>
      )}
    </div>
  );
};

// in grid mode (for playx) we always want access to every comparison rowId even when diff mode is off,
// so we added an extra field to the rowId to differentiate these cases
export function isDiffModeRowId(rowId: RowId): rowId is DiffObjectType<string> {
  return isDiffObject(rowId) && rowId.diffModeEnabled !== "false";
}
