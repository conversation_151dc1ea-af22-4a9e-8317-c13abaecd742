import {
  type Trace,
  type LoadedTrace,
  type PreviewSpan,
  type PreviewTrace,
} from "#/ui/trace/graph";
import {
  DiffRightField,
  isDiffObject,
  type RowId,
} from "#/utils/diffs/diff-objects";
import { type Span } from "@braintrust/local";

export type LoadingState = "loading" | "not_found" | "loaded";

export function calculateLoadingState({
  isTraceQueryLoading,
  hasNoLoadedTraceData,
  loadedSpan,
  isConfigLoading,
}: {
  isTraceQueryLoading?: boolean;
  hasNoLoadedTraceData?: boolean;
  loadedSpan?: Span | null;
  isConfigLoading?: boolean;
}): LoadingState {
  if (isTraceQueryLoading || isConfigLoading) {
    return "loading";
  }

  if (hasNoLoadedTraceData) {
    return "not_found";
  }

  if (loadedSpan) {
    return "loaded";
  }
  return "loading";
}

/**
 *
 * @param id row id or span id or root span id
 * @param trace
 * @param relatedRowIds related trace ids for grouping on the logs table
 * @returns
 */
export function isTraceLoadedForId({
  id,
  trace,
  relatedRowIds,
}: {
  id?: string | null;
  trace: Trace | null | undefined;
  relatedRowIds?: { id: string; root_span_id: string }[];
}) {
  const rootSpan = trace?.root;
  if (!id || !rootSpan) {
    return false;
  }

  // span_id case
  if (trace.spans[id]) {
    return true;
  }

  // root_span_id case
  if (
    (rootSpan.span_parents ?? []).length === 0 &&
    id === rootSpan.root_span_id
  ) {
    return true;
  }

  // row id case
  if (id === rootSpan.id) {
    return true;
  }

  if (relatedRowIds?.some((r) => r.id === id || r.root_span_id === id)) {
    return true;
  }
  return false;
}

/**
 * Try to make sure changes to diff span selection update at the same time:
 *  - traces must both be loaded
 *  - spans must both be loaded
 *  - expected matching span must match the loaded span
 * Also handle non-existing comparison cases
 * @returns
 */
export function resolveLoadedDiffSpans({
  isDiffMode,
  loadedPrimarySpan: primarySpan,
  loadedComparisonSpan: comparisonSpan,
  activeSpanId,
  loadedPrimaryTrace: primaryTrace,
  loadedComparisonTrace: comparisonTrace,
  expectedComparisonExperimentRowId,
  expectedMatchingSpan,
}: {
  isDiffMode: boolean;
  loadedPrimarySpan: Span | null;
  loadedComparisonSpan: Span | null;
  activeSpanId: string | null;
  loadedPrimaryTrace: PreviewTrace | null;
  loadedComparisonTrace: PreviewTrace | null;
  expectedComparisonExperimentRowId: string | null;
  expectedMatchingSpan?: PreviewSpan;
}) {
  if (!isDiffMode) {
    return { primarySpan };
  }

  // For non-overlapping traces, the s param refers to the span_id on the comparison trace
  const urlComparisonSpanRowId = comparisonTrace?.spans[activeSpanId ?? ""]?.id;
  if (urlComparisonSpanRowId) {
    return comparisonSpan?.id === urlComparisonSpanRowId
      ? { comparisonSpan }
      : {};
  }

  if (!primarySpan || !primaryTrace) {
    return {};
  }

  if (!expectedComparisonExperimentRowId) {
    // no matching comparison row at all -- return null for comparison span so a comparison can still be made
    return { primarySpan, comparisonSpan: null };
  }

  if (!comparisonTrace) {
    return {};
  }

  if (!expectedMatchingSpan) {
    // no matching span in comparison trace -- return null for comparison span so a comparison can still be made
    return { primarySpan, comparisonSpan: null };
  }
  if (expectedMatchingSpan.id === comparisonSpan?.id) {
    return { primarySpan, comparisonSpan };
  }

  return {};
}

/**
 * The r param accepts these values:
 * - row id
 * - root span id
 * - subspan row id
 * We want the r param to resolve to the id in all cases
 * TODO: resolve to root_span_id in all cases
 * @param existingRowId the existing row id from the url
 * @param loadedTrace
 * @returns a resolved row id, null otherwise
 */
export function resolvePrimaryRowIdForUpdate({
  existingRowId,
  loadedTrace,
}: {
  existingRowId: RowId | null;
  loadedTrace: LoadedTrace | null;
}) {
  if (!loadedTrace) {
    return null;
  }

  if (isDiffObject(existingRowId)) {
    return {
      ...existingRowId,
      [DiffRightField]: loadedTrace.root.id,
    };
  }

  if (existingRowId === loadedTrace.root.id) {
    return null;
  }
  return loadedTrace.root.id;
}
