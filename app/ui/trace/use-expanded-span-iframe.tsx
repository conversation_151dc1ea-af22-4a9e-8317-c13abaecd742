import {
  useExpandedFrameState,
  useHumanReviewState,
} from "#/ui/query-parameters";
import { useCallback, useMemo } from "react";
import { useTraceFullscreen } from "./use-trace-fullscreen";
import { parseObjectJSON } from "#/utils/schema";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { type SpanIFrame } from "@braintrust/typespecs";
import { type Span } from "@braintrust/local";

export const useExpandedSpanIframe = ({
  objectType,
  spanIframes,
  selectedSpan,
}: {
  objectType?: DataObjectType;
  spanIframes?: SpanIFrame[];
  selectedSpan?: Span | null;
}) => {
  const [humanReviewMode] = useHumanReviewState();
  const { isTraceFullscreen, setFullscreenState } = useTraceFullscreen();
  const [expandedFrameId, setExpandedFrameId] = useExpandedFrameState();

  const { expandedFrame, parsedSpanData } = useMemo(() => {
    if (
      (!isTraceFullscreen && !humanReviewMode) ||
      !expandedFrameId ||
      !selectedSpan
    ) {
      return {};
    }
    const expandedFrame = spanIframes?.find(({ id }) => expandedFrameId === id);
    const parsedSpanData =
      expandedFrame && objectType
        ? parseObjectJSON(objectType, selectedSpan.data)
        : undefined;
    return { expandedFrame, parsedSpanData };
  }, [
    isTraceFullscreen,
    expandedFrameId,
    spanIframes,
    objectType,
    selectedSpan,
    humanReviewMode,
  ]);

  const setExpandedFrame = useCallback(
    (frameId: string | null) => {
      if (frameId) {
        setFullscreenState(frameId);
      }
      setExpandedFrameId(frameId);
    },
    [setFullscreenState, setExpandedFrameId],
  );

  return {
    expandedFrame,
    parsedSpanData,
    setExpandedFrame,
  };
};
