import { isObject } from "braintrust/util";
import { type LLMMessageType } from "#/ui/LLMView";
import { useCallback, useRef } from "react";

export const useThreadViewHashMessage = () => {
  const hashCache = useRef(new Map<string, string>());

  const hashMessage = useCallback(
    (message: LLMMessageType) => getMessageHash(message, hashCache.current),
    [hashCache],
  );

  return { hashMessage, hashCache };
};

const getMessageHash = (
  message: LLMMessageType,
  hashCache: Map<string, string>,
): string => {
  const base: Record<string, unknown> = {
    role: message.role,
    content: message.content,
  };

  // OpenAI-style function call
  if ("function_call" in message && message.function_call) {
    const func = message.function_call;
    if (isObject(func)) {
      const name = typeof func["name"] === "string" ? func["name"] : undefined;
      const args =
        typeof func["arguments"] === "string" ? func["arguments"] : undefined;
      base.function_call = {
        name,
        arguments: args,
      };
    }
  }

  // OpenAI-style tool calls
  if ("tool_calls" in message && Array.isArray(message.tool_calls)) {
    const toolCalls = message.tool_calls.map((tc) => {
      let type: string | undefined;
      let name: string | undefined;
      let argsNorm: string | undefined;
      if (isObject(tc)) {
        const t = tc["type"];
        type = typeof t === "string" ? t : undefined;
        const fn = tc["function"];
        if (isObject(fn)) {
          const n = fn["name"];
          name = typeof n === "string" ? n : undefined;
          const a = fn["arguments"];
          argsNorm = typeof a === "string" ? a : undefined;
        }
      }
      return { type, name, arguments: argsNorm };
    });
    // Sort deterministically by name+arguments
    toolCalls.sort((a, b) => {
      const aKey = `${a.name ?? ""}:${a.arguments ?? ""}`;
      const bKey = `${b.name ?? ""}:${b.arguments ?? ""}`;
      return aKey.localeCompare(bKey);
    });
    base.tool_calls = toolCalls;
  }

  // Tool role messages should include the tool_call_id in the hash
  if ("tool_call_id" in message && message.tool_call_id) {
    base.tool_call_id = message.tool_call_id;
  }

  const messageString = JSON.stringify(base);

  if (hashCache.has(messageString)) {
    return hashCache.get(messageString)!;
  }

  let hash = 0;
  for (let i = 0; i < messageString.length; i++) {
    const char = messageString.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  const hashString = hash.toString(36); // Convert to base36 for shorter string

  // Cache the result
  hashCache.set(messageString, hashString);
  return hashString;
};
