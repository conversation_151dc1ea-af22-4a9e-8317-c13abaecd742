import { SpanTypeAttribute } from "braintrust/util";
import {
  <PERSON>ert<PERSON>ir<PERSON>,
  Bolt,
  Box,
  CircleDot,
  type LucideIcon,
  MessageCircle,
  Parentheses,
  Percent,
  Square,
  SquareFunction,
} from "lucide-react";

export const getSpanDisplayConfig = ({
  type,
  cached,
  remote,
  hasError,
}: {
  type: SpanTypeAttribute | string | undefined | null;
  cached?: boolean;
  remote?: boolean;
  hasError?: boolean;
}) => {
  let label = "";
  let iconClassName = "";
  let lightClassName = "";
  let borderClassName = "";
  let Icon: LucideIcon;
  let tooltip: string | undefined;
  switch (type) {
    case SpanTypeAttribute.EVAL: {
      label = "Eval";
      iconClassName = "bg-primary-600 text-primary-100";
      lightClassName = "bg-primary-200 text-primary-700";
      borderClassName = "border-primary-400";
      Icon = CircleDot;
      break;
    }
    case SpanTypeAttribute.TASK: {
      label = "Task";
      iconClassName = "bg-accent-600 text-accent-100";
      lightClassName = "bg-accent-100 text-accent-700";
      borderClassName = "border-accent-400";
      Icon = Box;
      break;
    }
    case SpanTypeAttribute.SCORE: {
      label = "Score";
      iconClassName =
        "bg-green-600 text-green-100 dark:bg-green-400 dark:text-green-900";
      lightClassName =
        "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300";
      borderClassName = "border-green-400 dark:border-green-600";
      Icon = Percent;
      break;
    }
    case SpanTypeAttribute.LLM: {
      label = "LLM";
      iconClassName = cached
        ? "text-violet-700 border border-violet-300 dark:text-violet-400 dark:border-violet-700 border-dashed bg-background"
        : "bg-violet-600 text-violet-100 dark:bg-violet-400 dark:text-violet-900";
      lightClassName =
        "bg-violet-100 text-violet-700 dark:bg-violet-900 dark:text-violet-300";
      borderClassName = "border-violet-400 dark:border-violet-600";
      Icon = MessageCircle;
      tooltip = cached ? "This LLM call was cached" : undefined;
      break;
    }
    case SpanTypeAttribute.FUNCTION: {
      label = "Fn";
      iconClassName =
        "bg-cyan-600 text-cyan-100 dark:bg-cyan-400 dark:text-cyan-900";
      lightClassName =
        "bg-cyan-100 text-cyan-700 dark:bg-cyan-900 dark:text-cyan-300";
      borderClassName = "border-cyan-400 dark:border-cyan-600";
      Icon = remote ? SquareFunction : Parentheses;
      tooltip = remote ? "This function was called remotely" : undefined;
      break;
    }
    case SpanTypeAttribute.TOOL: {
      label = "Tool";
      iconClassName =
        "bg-orange-600 text-orange-100 dark:bg-orange-400 dark:text-orange-900";
      lightClassName =
        "bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300";
      borderClassName = "border-orange-400 dark:border-orange-600";
      Icon = Bolt;
      break;
    }
    default: {
      iconClassName = "bg-primary-500 text-primary-100";
      lightClassName = "bg-primary-100 text-primary-700";
      borderClassName = "border-primary-400 dark:border-primary-600";
      Icon = Square;
    }
  }

  if (hasError) {
    iconClassName = "bg-bad-600 text-bad-200";
    lightClassName = "bg-bad-100 text-bad-700";
    borderClassName = "border-bad-400";
    Icon = AlertCircle;
  }

  return {
    label,
    iconClassName,
    lightClassName,
    borderClassName,
    Icon,
    tooltip,
    type,
  };
};

export const backfillSpanType = (name?: string | null) => {
  switch (name) {
    case "eval":
      return SpanTypeAttribute.EVAL;
    case "OpenAI Chat Completion":
    case "OpenAI Completion":
      return SpanTypeAttribute.LLM;
    default:
      return name;
  }
};
