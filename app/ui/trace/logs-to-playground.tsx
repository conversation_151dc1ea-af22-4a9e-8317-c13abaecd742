import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogContent,
  DialogDescription,
} from "#/ui/dialog";
import { fetchBtql, useFetchBtqlOptions } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type Span } from "./graph";
import { useQuery } from "@tanstack/react-query";
import { Spinner } from "#/ui/icons/spinner";
import { ErrorBanner } from "#/ui/error-banner";
import { Button, buttonVariants } from "#/ui/button";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { DataTextEditor } from "#/ui/data-text-editor";
import { CollapsibleSection } from "#/ui/collapsible-section";
import {
  <PERSON>Lef<PERSON>,
  ArrowUpRight,
  CircleCheck,
  <PERSON>Dashed,
  <PERSON>,
  Shapes,
  <PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import { createDataset } from "#/app/app/[org]/p/[project]/datasets/[dataset]/createDataset";
import { createPromptSession } from "#/app/app/[org]/prompt/[prompt]/createPromptSession";
import { performUpsert } from "#/utils/duckdb";
import { newId } from "#/utils/btapi/btapi";
import { getPlaygroundLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { useUser } from "#/utils/user";
import { DatasetIdField } from "#/utils/duckdb";
import { IS_MERGE_FIELD } from "braintrust/util";
import { useContext, useState } from "react";
import pluralize from "pluralize";
import { Skeleton } from "#/ui/skeleton";
import { toast } from "sonner";
import Link from "next/link";
import { Label } from "#/ui/label";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { Switch } from "#/ui/switch";
import { PlaygroundDropdown } from "#/ui/playground-dropdown";
import { useQueryFunc } from "#/utils/react-query";
import { type getOrgPromptSessions } from "#/app/app/[org]/p/[project]/library/library-actions";
import { type Message } from "@braintrust/typespecs";
import { getModelIcon } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { useAvailableModels } from "#/ui/prompts/models";
import { InfoBanner } from "#/ui/info-banner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormField, FormItem, FormMessage } from "#/ui/form";
import { cn } from "#/utils/classnames";

// Type definition for playground data
interface PlaygroundTraceData {
  traces: {
    trace: Span;
  }[];
  uniqueSystemPrompts: {
    model?: string;
    prompt?: string;
  }[];
  uniqueDatasetInputs: unknown[];
}

// Type definition for LLM span data
interface LLMSpanData {
  id: string;
  span_id: string;
  root_span_id: string;
  input?: unknown;
  output?: unknown;
  created: string;
  span_parents?: string;
  model?: string;
  span_attributes?: {
    type?: string;
    name?: string;
  };
  [key: string]: unknown;
}

export const BulkTraceToPlayground = ({
  getRows,
  projectId,
  projectName,
}: {
  getRows: () => Promise<Span[]>;
  projectId: string;
  projectName: string;
}) => {
  const [createPlaygroundDialogOpen, setCreatePlaygroundDialogOpen] =
    useState(false);
  const [selectedRowsHash, setSelectedRowHash] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <Button
        variant="border"
        size="xs"
        Icon={Shapes}
        onClick={async () => {
          const traces = await getRows();
          const ids = traces.map((t) => t.id);
          const selectionHash = JSON.stringify(ids.sort());
          setSelectedRowHash(selectionHash);
          setCreatePlaygroundDialogOpen(true);
        }}
      >
        Iterate in playground
      </Button>
      <TraceToPlaygroundDialog
        selectedRowsHash={selectedRowsHash || ""}
        projectId={projectId}
        projectName={projectName}
        getRows={getRows}
        createPlaygroundDialogOpen={createPlaygroundDialogOpen}
        setCreatePlaygroundDialogOpen={setCreatePlaygroundDialogOpen}
        dialogTitle="Iterate in playground"
        dialogDescription="Iterate in playground with the extracted prompts and dataset"
      />
    </>
  );
};

const TraceToPlaygroundDialog = ({
  selectedRowsHash,
  projectId,
  projectName,
  getRows,
  createPlaygroundDialogOpen,
  setCreatePlaygroundDialogOpen,
  dialogTitle = "Iterate in playground",
  dialogDescription = "Iterate in playground with the extracted prompts and dataset",
}: {
  selectedRowsHash: string;
  projectId: string;
  projectName: string;
  getRows: () => Promise<Span[]>;
  createPlaygroundDialogOpen: boolean;
  setCreatePlaygroundDialogOpen: (open: boolean) => void;
  dialogTitle?: string;
  dialogDescription?: string;
}) => {
  const builder = useBtqlQueryBuilder({});
  const { btqlFlags, apiUrl } = useFetchBtqlOptions();
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const {
    data: playgroundData,
    isLoading,
    isRefetching,
    error,
  } = useQuery<PlaygroundTraceData>({
    queryKey: ["trace-to-playground-data", projectId, selectedRowsHash],
    queryFn: async () => {
      const traces = await getRows();
      const rootSpanIds = traces.map((trace) => trace.root_span_id);

      const response = await fetchBtql<LLMSpanData>({
        args: {
          query: {
            filter: builder.and(
              {
                op: "in",
                left: { btql: "root_span_id" },
                right: {
                  op: "literal",
                  value: rootSpanIds,
                },
              },
              {
                op: "eq",
                left: { btql: "span_attributes.type" },
                right: { op: "literal", value: "llm" },
              },
              {
                op: "eq",
                left: { btql: "is_root" },
                right: { op: "literal", value: false },
              },
            ),
            from: builder.from("project_logs", [projectId], "spans"),
            select: [
              {
                expr: { btql: "id" },
                alias: "id",
              },
              {
                expr: { btql: "span_id" },
                alias: "span_id",
              },
              {
                expr: { btql: "root_span_id" },
                alias: "root_span_id",
              },
              {
                expr: { btql: "input" },
                alias: "input",
              },
              {
                expr: { btql: "metadata.model" },
                alias: "model",
              },
              {
                expr: { btql: "created" },
                alias: "created",
              },
            ],
            sort: [
              { expr: { btql: "root_span_id" }, dir: "asc" },
              { expr: { btql: "created" }, dir: "asc" },
            ],
          },
          brainstoreRealtime: false,
          useColumnstore: false,
        },
        btqlFlags,
        apiUrl: apiUrl || org.api_url,
        getOrRefreshToken,
      });

      //Get the first LLM span because that usually contains the system prompt
      const firstLLMSpanPerTrace: Record<string, LLMSpanData> = {};

      if (response.data && response.data.length > 0) {
        for (const span of response.data) {
          const rootSpanId = span.root_span_id;
          if (!firstLLMSpanPerTrace[rootSpanId]) {
            firstLLMSpanPerTrace[rootSpanId] = span;
          }
        }
      }

      const systemPromptsByTrace: Record<string, string | null> = {};
      const uniqueSystemPrompts = new Map<
        string,
        { prompt: string; model: string }
      >();
      const uniqueDatasetInputs = new Map<string, { input: unknown }>();

      for (const [rootSpanId, llmSpan] of Object.entries(
        firstLLMSpanPerTrace,
      )) {
        let systemPrompt: string | null = null;

        //Grab the system prompt from the LLM span input
        if (llmSpan.input && Array.isArray(llmSpan.input)) {
          const systemMessage = llmSpan.input.find(
            (m: Message) => m.role === "system",
          );
          if (systemMessage?.content) {
            if (Array.isArray(systemMessage.content)) {
              systemPrompt = systemMessage.content[0]?.text || null;
            } else if (typeof systemMessage.content === "string") {
              systemPrompt = systemMessage.content;
            }
          }
        }

        systemPromptsByTrace[rootSpanId] = systemPrompt;

        // deduping system prompts
        if (systemPrompt) {
          const existing = uniqueSystemPrompts.get(systemPrompt);
          if (!existing) {
            uniqueSystemPrompts.set(systemPrompt, {
              prompt: systemPrompt,
              model: llmSpan.model || "",
            });
          }
        }
      }

      // deduping dataset inputs
      const processedTraces = traces.map((trace) => {
        if (trace.data.input) {
          const existing = uniqueDatasetInputs.get(
            JSON.stringify(trace.data.input),
          );
          if (!existing) {
            uniqueDatasetInputs.set(JSON.stringify(trace.data.input), {
              input: trace.data.input,
            });
          }
        }
        const systemPrompt = systemPromptsByTrace[trace.root_span_id] || null;

        return {
          trace,
          systemPrompt,
        };
      });
      return {
        traces: processedTraces,
        uniqueDatasetInputs: Array.from(uniqueDatasetInputs.values()).map(
          (i) => i.input,
        ),
        uniqueSystemPrompts: Array.from(uniqueSystemPrompts.values()),
      };
    },
    enabled: createPlaygroundDialogOpen,
  });

  const isEffectivelyLoading = isLoading || isRefetching;

  return (
    <Dialog
      open={createPlaygroundDialogOpen}
      onOpenChange={setCreatePlaygroundDialogOpen}
    >
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogDescription>{dialogDescription}</DialogDescription>
        </DialogHeader>
        {error && (
          <ErrorBanner skipErrorReporting> {error.message}</ErrorBanner>
        )}
        <TraceToPlaygroundDialogBody
          isLoading={isEffectivelyLoading}
          systemPrompts={playgroundData?.uniqueSystemPrompts || []}
          datasetInputs={playgroundData?.uniqueDatasetInputs || []}
          projectId={projectId}
          projectName={projectName}
          orgName={org.name}
          setCreatePlaygroundDialogOpen={setCreatePlaygroundDialogOpen}
        />
      </DialogContent>
    </Dialog>
  );
};

export const SingleTraceToPlaygroundDialog = ({
  trace,
  projectId,
  projectName,
  createPlaygroundDialogOpen,
  setCreatePlaygroundDialogOpen,
}: {
  trace: Span;
  projectId: string;
  projectName: string;
  createPlaygroundDialogOpen: boolean;
  setCreatePlaygroundDialogOpen: (open: boolean) => void;
}) => {
  // This is so we can use the same dialog for both bulk and single trace
  const getRows = async () => [trace];
  const selectedRowsHash = JSON.stringify([trace.id]);

  return (
    <TraceToPlaygroundDialog
      selectedRowsHash={selectedRowsHash}
      projectId={projectId}
      projectName={projectName}
      getRows={getRows}
      createPlaygroundDialogOpen={createPlaygroundDialogOpen}
      setCreatePlaygroundDialogOpen={setCreatePlaygroundDialogOpen}
      dialogTitle="Iterate in playground"
      dialogDescription="Iterate in playground with the extracted prompts and dataset"
    />
  );
};

type ObjectProgressStatus = {
  status: "not_started" | "pending" | "success" | "error";
  error?: string;
};

const SystemPromptsList = ({
  systemPrompts,
  isLoading,
  orgName,
  allAvailableModels,
  defaultPromptData,
}: {
  systemPrompts: { prompt?: string; model: string; originalModel?: string }[];
  isLoading: boolean;
  orgName: string;
  allAvailableModels: ReturnType<
    typeof useAvailableModels
  >["allAvailableModels"];
  defaultPromptData: ReturnType<typeof useDefaultPromptData>;
}) => {
  const [showAllPrompts, setShowAllPrompts] = useState(false);
  const systemPromptsToShow = showAllPrompts
    ? systemPrompts
    : systemPrompts.slice(0, 3);
  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <span className="text-sm">
          {pluralize("Prompt", systemPrompts.length)}
        </span>
        {!isLoading && (
          <span className="text-xs text-primary-500">
            {systemPrompts.length} found
          </span>
        )}
      </div>
      {isLoading ? (
        <div className="flex items-center justify-center">
          <Skeleton className="h-24 w-full" />
        </div>
      ) : systemPrompts.length > 0 ? (
        systemPromptsToShow.map((systemPrompt, idx) => {
          const modelIsConfigured =
            systemPrompt.originalModel === systemPrompt.model;

          // Use the system prompt model if configured, otherwise use the default model

          const ModelIcon = getModelIcon(systemPrompt.model);
          const modelDisplayName =
            allAvailableModels[systemPrompt.model]?.displayName ||
            systemPrompt.model;

          const originalModelDisplayName =
            allAvailableModels[
              systemPrompt.originalModel || defaultPromptData.options.model
            ]?.displayName || systemPrompt.originalModel;

          return (
            <div
              key={idx}
              className="flex flex-col gap-0 rounded-md border border-primary-200/80 bg-primary-100/50"
            >
              <CollapsibleSection
                title={
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-1 text-xs">
                      {!modelIsConfigured && systemPrompt.model ? (
                        <div className="flex items-center gap-1">
                          <ModelIcon size={12} />
                          <span className="text-[11px]">
                            {modelDisplayName}
                          </span>
                          <ArrowLeft size={10} />
                          <span className="text-[11px] line-through">
                            {originalModelDisplayName}
                          </span>
                        </div>
                      ) : (
                        <>
                          <ModelIcon size={12} />
                          <span className="text-[11px]">
                            {modelDisplayName}
                          </span>
                        </>
                      )}
                    </div>
                    <span className="max-w-sm truncate">
                      {systemPrompt.prompt
                        ? systemPrompt.prompt.slice(0, 100)
                        : "prompt missing"}
                    </span>
                  </div>
                }
                className="h-11 px-2"
                defaultCollapsed
              >
                <div className="no-scrollbar max-h-40 overflow-auto rounded-md bg-primary-50 px-2 pb-1">
                  {!modelIsConfigured && systemPrompt.model && (
                    <InfoBanner
                      className="flex items-center gap-1"
                      iconClassName="hidden"
                    >
                      Using {modelDisplayName} because{" "}
                      {systemPrompt.originalModel} is not configured in this
                      organization. To use the original model, please configure
                      the provider in the{" "}
                      <Link
                        href={`/app/${orgName}/settings/secrets`}
                        className="font-medium underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        settings page
                      </Link>
                      .
                    </InfoBanner>
                  )}
                  <DataTextEditor
                    value={{
                      model: systemPrompt.model,
                      prompt: systemPrompt.prompt,
                    }}
                    readOnly
                    hideLineNumbers
                    className="text-xs"
                  />
                </div>
              </CollapsibleSection>
            </div>
          );
        })
      ) : (
        <div className="text-sm text-primary-500">No system prompt found</div>
      )}
      {!showAllPrompts && systemPrompts.length > 5 && !isLoading && (
        <Button
          variant="ghost"
          onClick={() => setShowAllPrompts(true)}
          size="xs"
        >
          Show all prompts
        </Button>
      )}
    </div>
  );
};

const DatasetInputsList = ({
  datasetInputs,
  isLoading,
}: {
  datasetInputs: unknown[];
  isLoading: boolean;
}) => {
  const [showAllData, setShowAllData] = useState(false);
  const datasetInputsToShow = showAllData
    ? datasetInputs
    : datasetInputs.slice(0, 5);
  console.log(datasetInputs);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <span className="text-sm">Dataset inputs</span>
        {!isLoading && (
          <span className="text-xs text-primary-500">
            {datasetInputs.length} found
          </span>
        )}
      </div>
      {isLoading ? (
        <div className="flex items-center justify-center">
          <Skeleton className="h-40 w-full" />
        </div>
      ) : datasetInputsToShow.length > 0 ? (
        datasetInputsToShow.map((datasetInput, idx) => (
          <div
            key={idx}
            className="flex flex-col gap-0 rounded-md border border-primary-200/80 bg-primary-100/50"
          >
            <CollapsibleSection
              title={
                <div className="flex items-center gap-2">
                  <ArrowUpRight className="size-3" />
                  <span className="max-w-sm truncate">
                    {typeof datasetInput === "string"
                      ? datasetInput
                      : JSON.stringify(datasetInput)}
                  </span>
                </div>
              }
              defaultCollapsed
              className="px-2"
            >
              <div className="no-scrollbar max-h-32 overflow-auto px-2 pb-1">
                <DataTextEditor
                  value={datasetInput}
                  readOnly
                  hideLineNumbers
                  className="text-xs"
                />
              </div>
            </CollapsibleSection>
          </div>
        ))
      ) : (
        <div className="text-sm text-primary-500">No dataset inputs found</div>
      )}
      {!showAllData && datasetInputs.length > 5 && !isLoading && (
        <Button variant="ghost" onClick={() => setShowAllData(true)} size="xs">
          Show all inputs
        </Button>
      )}
    </div>
  );
};

const ProgressItem = ({
  status,
  labels,
}: {
  status: ObjectProgressStatus;
  labels: {
    notStarted: string;
    pending: string;
    success: string;
    error: string;
  };
}) => {
  const statusConfig = {
    not_started: {
      icon: CircleDashed,
      text: labels.notStarted,
    },
    pending: {
      icon: Spinner,
      text: labels.pending,
    },
    success: {
      icon: CircleCheck,
      text: labels.success,
    },
    error: {
      icon: TriangleAlert,
      text: labels.error,
    },
  };

  const config = statusConfig[status.status];
  if (!config) return null;

  const Icon = config.icon;
  return (
    <div className="flex items-center gap-1">
      <Icon className="size-3" />
      <span>{config.text}</span>
    </div>
  );
};

const ProgressToastContent = ({
  progressStatus,
  orgName,
  projectName,
  playgroundName,
  addToExistingPlayground,
  addToExistingDataset,
}: {
  progressStatus: {
    dataset: ObjectProgressStatus;
    playground: ObjectProgressStatus;
  };
  orgName: string;
  projectName: string;
  playgroundName: string;
  addToExistingPlayground: boolean;
  addToExistingDataset: boolean;
}) => {
  const progressItems = [
    {
      status: progressStatus.dataset,
      labels: {
        notStarted: addToExistingDataset
          ? "Add to existing dataset"
          : "Create dataset",
        pending: addToExistingDataset
          ? "Adding to existing dataset..."
          : "Creating dataset...",
        success: addToExistingDataset ? "Added to dataset" : "Created dataset",
        error: addToExistingDataset
          ? "Failed to add to dataset"
          : "Failed to create dataset",
      },
    },
    {
      status: progressStatus.playground,
      labels: {
        notStarted: addToExistingPlayground
          ? "Add to existing playground"
          : "Create playground",
        pending: addToExistingPlayground
          ? "Adding to existing playground..."
          : "Creating playground...",
        success: addToExistingPlayground
          ? "Added to playground"
          : "Created playground",
        error: addToExistingPlayground
          ? "Failed to add to playground"
          : "Failed to create playground",
      },
    },
  ];

  return (
    <div className="flex w-full items-center justify-between">
      <div className="flex flex-col gap-1">
        {progressItems.map((item, index) => (
          <ProgressItem key={index} status={item.status} labels={item.labels} />
        ))}
      </div>
      <Link
        className={cn(
          buttonVariants({ variant: "border", size: "xs" }),
          progressStatus.playground.status !== "success" &&
            "pointer-events-none opacity-60",
        )}
        href={getPlaygroundLink({
          orgName,
          projectName: projectName,
          playgroundName: playgroundName,
        })}
      >
        Go to playground
      </Link>
    </div>
  );
};

// Form validation schema
const playgroundFormSchema = z
  .object({
    addAsNewDataset: z.boolean().default(false),
    selectedDatasetId: z.string().optional(),
    addAsNewPlayground: z.boolean().default(false),
    selectedPlaygroundId: z.string().optional(),
  })
  .refine(
    (data) => {
      // If addAsNewDataset is true, selectedDatasetId is required
      if (data.addAsNewDataset && !data.selectedDatasetId) {
        return false;
      }
      return true;
    },
    {
      message: "Please select a dataset",
      path: ["selectedDatasetId"],
    },
  )
  .refine(
    (data) => {
      // If addAsNewPlayground is true, selectedPlaygroundId is required
      if (data.addAsNewPlayground && !data.selectedPlaygroundId) {
        return false;
      }
      return true;
    },
    {
      message: "Please select a playground",
      path: ["selectedPlaygroundId"],
    },
  );

type PlaygroundFormValues = z.infer<typeof playgroundFormSchema>;

export const TraceToPlaygroundDialogBody = ({
  isLoading,
  systemPrompts,
  datasetInputs,
  projectName,
  orgName,
  setCreatePlaygroundDialogOpen,
}: {
  isLoading: boolean;
  systemPrompts: { prompt?: string; model?: string }[];
  datasetInputs: unknown[];
  projectId: string;
  projectName: string;
  orgName: string;
  setCreatePlaygroundDialogOpen: (open: boolean) => void;
}) => {
  const { user } = useUser();
  const org = useOrg();
  const { apiUrl } = useFetchBtqlOptions();
  const { getOrRefreshToken } = useSessionToken();
  const { orgDatasets } = useContext(ProjectContext);

  const { data: orgPlaygrounds, refetch: refetchOrgPlaygrounds } = useQueryFunc<
    typeof getOrgPromptSessions
  >({
    fName: "getOrgPromptSessions",
    args: { org_name: org.name },
  });

  const form = useForm<PlaygroundFormValues>({
    resolver: zodResolver(playgroundFormSchema),
    defaultValues: {
      addAsNewDataset: false,
      selectedDatasetId: undefined,
      addAsNewPlayground: false,
      selectedPlaygroundId: undefined,
    },
  });

  const addAsNewDataset = form.watch("addAsNewDataset");
  const addAsNewPlayground = form.watch("addAsNewPlayground");
  const selectedDatasetId = form.watch("selectedDatasetId");
  const selectedPlaygroundId = form.watch("selectedPlaygroundId");

  const defaultPromptData = useDefaultPromptData({ orgName });
  const { configuredModelsByProvider, allAvailableModels } = useAvailableModels(
    { orgName },
  );

  const flatConfiguredModels = Object.entries(
    configuredModelsByProvider,
  ).flatMap(([_, models]) => models);

  const checkedSystemPrompts = systemPrompts.map(
    ({ prompt: systemPrompt, model }) => {
      const modelIsConfigured = model
        ? flatConfiguredModels.some(
            (configuredModel) => configuredModel.modelName === model,
          )
        : false;
      const actualModelToBeUsed =
        modelIsConfigured && model ? model : defaultPromptData.options.model;

      return {
        prompt: systemPrompt,
        model: actualModelToBeUsed,
        originalModel: model,
      };
    },
  );

  const handleCreatePlayground = form.handleSubmit(async (values) => {
    const userId = user?.id;
    const date = new Date().toISOString();
    const playgroundName = `Playground from trace - ${date}`;

    if (!userId || !org.id) {
      console.error("Missing user ID or org ID");
      return;
    }

    try {
      const progressToastId = `playground-creation-progress-${date}`;

      const progressStatus: {
        dataset: ObjectProgressStatus;
        playground: ObjectProgressStatus;
      } = {
        dataset: { status: "not_started" },
        playground: { status: "not_started" },
      };

      const updateProgressToast = () => {
        const message = (
          <ProgressToastContent
            progressStatus={progressStatus}
            orgName={orgName}
            projectName={projectName}
            playgroundName={playgroundName}
            addToExistingPlayground={selectedPlaygroundId ? true : false}
            addToExistingDataset={selectedDatasetId ? true : false}
          />
        );

        toast(message, {
          id: progressToastId,
          duration: 10000,
        });
      };

      updateProgressToast();

      setCreatePlaygroundDialogOpen(false);

      const createOrUseDataset = async (selectedId: string | undefined) => {
        try {
          progressStatus.dataset = { status: "pending" };
          updateProgressToast();

          let datasetId: string;
          let result: {
            data: { dataset?: { id?: string } } | null;
            error: string | null;
          };

          // If user selected 'add to existing", create a dataset, otherwise use the selected dataset
          if (!selectedId) {
            result = await createDataset({
              orgId: org.id!,
              projectName,
              datasetName: `Dataset for ${playgroundName} - ${date}`,
            });

            if (result.error || !result.data?.dataset?.id) {
              progressStatus.dataset = { status: "error" };
              updateProgressToast();
              return result;
            }

            datasetId = result.data.dataset.id;
          } else {
            datasetId = selectedId;
            result = {
              data: { dataset: { id: selectedId } },
              error: null,
            };
          }

          if (datasetInputs) {
            const sessionToken = await getOrRefreshToken();
            const datasetRows = datasetInputs.map((input) => ({
              id: newId(),
              [DatasetIdField]: datasetId,
              input: input,
              expected: null,
              metadata: null,
              [IS_MERGE_FIELD]: false,
            }));

            await performUpsert(
              null,
              apiUrl || org.api_url,
              sessionToken,
              userId,
              datasetRows,
            );
          }

          progressStatus.dataset = { status: "success" };
          updateProgressToast();
          return result;
        } catch (error) {
          progressStatus.dataset = { status: "error" };
          updateProgressToast();
          throw error;
        }
      };

      const createOrUsePlayground = async (selectedId: string | undefined) => {
        try {
          progressStatus.playground = { status: "pending" };
          updateProgressToast();

          let playgroundId: string;
          let result: { data: { id?: string } | null; error: string | null };

          // If user selected 'add to existing', create a playground, otherwise use the selected playground
          if (!selectedId) {
            const tasks = checkedSystemPrompts.map(
              ({ prompt: systemPrompt, model }) => {
                return {
                  prompt_data: systemPrompt
                    ? {
                        prompt: {
                          type: "chat" as const,
                          messages: [
                            {
                              role: "system" as const,
                              content: systemPrompt,
                            },
                            {
                              role: "user" as const,
                              content: "{{input}}",
                            },
                          ],
                        },
                        options: {
                          ...defaultPromptData.options,
                          model: model,
                        },
                      }
                    : {
                        ...defaultPromptData,
                        options: {
                          ...defaultPromptData.options,
                          model: model,
                        },
                      },
                };
              },
            );

            result = await createPromptSession({
              orgName,
              projectName,
              sessionName: playgroundName,
              initialRecords: tasks,
              initialPromptArgs: {
                apiUrl: apiUrl || org.api_url,
                getOrRefreshToken,
                userId,
              },
            });

            if (result?.error || !result?.data?.id) {
              progressStatus.playground = { status: "error" };
              updateProgressToast();
              return result;
            }

            playgroundId = result.data.id;
          } else {
            playgroundId = selectedId;

            const sessionToken = await getOrRefreshToken();
            const tasks = checkedSystemPrompts.map(
              ({ prompt: systemPrompt, model }) => {
                return {
                  id: newId(),
                  prompt_session_id: playgroundId,
                  org_id: org.id!,
                  prompt_data: systemPrompt
                    ? {
                        prompt: {
                          type: "chat" as const,
                          messages: [
                            {
                              role: "system" as const,
                              content: systemPrompt,
                            },
                            {
                              role: "user" as const,
                              content: "{{input}}",
                            },
                          ],
                        },
                        options: {
                          ...defaultPromptData.options,
                          model: model,
                        },
                      }
                    : {
                        ...defaultPromptData,
                        options: {
                          ...defaultPromptData.options,
                          model: model,
                        },
                      },
                };
              },
            );

            await performUpsert(
              null,
              apiUrl || org.api_url,
              sessionToken,
              userId,
              tasks,
            );

            result = {
              data: { id: selectedId },
              error: null,
            };
          }

          return result;
        } catch (error) {
          progressStatus.playground = { status: "error" };
          updateProgressToast();
          throw error;
        }
      };

      // Create dataset and prompt session in parallel
      const [datasetResult, promptSessionResult] = await Promise.all([
        createOrUseDataset(
          values.addAsNewDataset ? values.selectedDatasetId : undefined,
        ),
        createOrUsePlayground(
          values.addAsNewPlayground ? values.selectedPlaygroundId : undefined,
        ),
      ]);

      // Check for errors
      if (datasetResult.error || !datasetResult.data?.dataset?.id) {
        console.error("Failed to create dataset:", datasetResult.error);
        return;
      }
      if (promptSessionResult.error || !promptSessionResult.data?.id) {
        console.error(
          "Failed to create prompt session:",
          promptSessionResult.error,
        );
        return;
      }

      try {
        const sessionToken = await getOrRefreshToken();
        await performUpsert(null, apiUrl || org.api_url, sessionToken, userId, [
          {
            id: newId(),
            prompt_session_id: promptSessionResult.data.id,
            org_id: org.id,
            prompt_session_data: {
              dataset_id: datasetResult.data.dataset.id,
            },
          },
        ]);

        progressStatus.playground = { status: "success" };
        updateProgressToast();
      } catch (error) {
        progressStatus.playground = { status: "error" };
        updateProgressToast();
      }
    } catch (error) {
      console.error("Failed to create playground:", error);
    }
  });

  return (
    <Form {...form}>
      <form onSubmit={handleCreatePlayground} className="flex flex-col gap-4">
        <SystemPromptsList
          systemPrompts={checkedSystemPrompts}
          isLoading={isLoading}
          orgName={orgName}
          defaultPromptData={defaultPromptData}
          allAvailableModels={allAvailableModels}
        />
        <DatasetInputsList
          datasetInputs={datasetInputs}
          isLoading={isLoading}
        />
        <div className="-mt-1 flex flex-col gap-1">
          {!isLoading && datasetInputs.length > 0 && (
            <>
              <div className="flex h-7 items-center justify-between gap-2">
                <FormField
                  control={form.control}
                  name="addAsNewDataset"
                  render={({ field }) => (
                    <Label className="flex items-center gap-2 text-xs">
                      <Switch
                        className="scale-75"
                        checked={field.value}
                        onCheckedChange={(value) => {
                          field.onChange(value);
                          if (!value) {
                            form.setValue("selectedDatasetId", undefined);
                            form.clearErrors("selectedDatasetId");
                          }
                        }}
                      />
                      Add to existing dataset
                    </Label>
                  )}
                />
                {addAsNewDataset && (
                  <FormField
                    control={form.control}
                    name="selectedDatasetId"
                    render={({ field }) => (
                      <FormItem>
                        <DatasetDropdown
                          datasets={orgDatasets}
                          selectedDatasetId={field.value}
                          onSelectDataset={({ id }) => field.onChange(id)}
                          onClear={() => field.onChange(undefined)}
                        >
                          <Button
                            size="xs"
                            className="w-40 justify-between"
                            isDropdown
                          >
                            {field.value ? (
                              <div className="flex items-center gap-1">
                                <Database className="size-3 text-fuchsia-600 dark:text-fuchsia-400" />
                                <span className="max-w-28 truncate">
                                  {
                                    orgDatasets?.find(
                                      (d) => d.id === field.value,
                                    )?.name
                                  }
                                </span>
                              </div>
                            ) : (
                              "Select a dataset"
                            )}
                          </Button>
                        </DatasetDropdown>
                      </FormItem>
                    )}
                  />
                )}
              </div>
              {
                //Given the structure of the components, to get it to render neatly below the components, separating into its own formfield.
                addAsNewDataset && (
                  <FormField
                    control={form.control}
                    name="selectedDatasetId"
                    render={() => (
                      <FormMessage className="mb-1 ml-auto w-40 text-xs" />
                    )}
                  />
                )
              }
            </>
          )}
          {!isLoading && (
            <>
              <div className="flex h-7 items-center justify-between gap-2">
                <FormField
                  control={form.control}
                  name="addAsNewPlayground"
                  render={({ field }) => (
                    <Label className="flex items-center gap-2 text-xs">
                      <Switch
                        className="scale-75"
                        checked={field.value}
                        onCheckedChange={(value) => {
                          field.onChange(value);
                          if (!value) {
                            form.setValue("selectedPlaygroundId", undefined);
                            form.clearErrors("selectedPlaygroundId");
                          }
                        }}
                      />
                      Add to existing playground
                    </Label>
                  )}
                />
                {
                  //Given the structure of the components, to get it to render neatly below the components, separating into its own formfield.
                  addAsNewPlayground && (
                    <FormField
                      control={form.control}
                      name="selectedPlaygroundId"
                      render={({ field }) => (
                        <FormItem>
                          <PlaygroundDropdown
                            playgrounds={orgPlaygrounds}
                            selectedPlaygroundId={field.value}
                            onSelectPlayground={({ id }) => field.onChange(id)}
                            onClear={() => field.onChange(undefined)}
                            refetchPlaygrounds={refetchOrgPlaygrounds}
                          >
                            <Button
                              size="xs"
                              className="w-40 justify-between"
                              isDropdown
                            >
                              {field.value ? (
                                <div className="flex items-center gap-1">
                                  <Shapes className="size-3 text-accent-600 dark:text-accent-400" />
                                  <span className="max-w-28 truncate">
                                    {
                                      orgPlaygrounds?.find(
                                        (p) => p.id === field.value,
                                      )?.name
                                    }
                                  </span>
                                </div>
                              ) : (
                                "Select a playground"
                              )}
                            </Button>
                          </PlaygroundDropdown>
                        </FormItem>
                      )}
                    />
                  )
                }
              </div>
              {addAsNewPlayground && (
                <FormField
                  control={form.control}
                  name="selectedPlaygroundId"
                  render={() => (
                    <FormMessage className="mb-1 ml-auto w-40 text-xs" />
                  )}
                />
              )}
            </>
          )}
          <div className="mt-2 flex justify-end gap-2">
            <Button
              variant="border"
              onClick={() => setCreatePlaygroundDialogOpen(false)}
              size="sm"
            >
              Cancel
            </Button>
            <Button size="sm" variant="primary" type="submit">
              {addAsNewPlayground ? "Add to playground" : "Create playground"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};
