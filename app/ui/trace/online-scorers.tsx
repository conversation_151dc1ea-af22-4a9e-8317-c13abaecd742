import { type PropsWith<PERSON>hildren, useContext, useState, useMemo } from "react";
import { Combobox } from "#/ui/combobox/combobox";
import {
  Plus,
  Settings2,
  Radio,
  Percent,
  Pencil,
  ListVideo,
} from "lucide-react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { decodeURIComponentPatched } from "#/utils/url";
import {
  ProjectContext,
  type ProjectContextT,
} from "#/app/app/[org]/p/[project]/projectContext";
import { isOnlineScore } from "@braintrust/local/query";
import { Button } from "#/ui/button";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { type DiscriminatedProjectScore } from "@braintrust/local/query";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";

type OnlineScorerOption = {
  value: string;
  label: string;
  ruleId: string;
  description?: string;
  rule?: DiscriminatedProjectScore;
  isCreate?: boolean;
};

const OnlineScorerTooltip = ({
  rule,
}: {
  rule?: DiscriminatedProjectScore;
}) => {
  const { functions, status } = useScorerFunctions({});

  if (!rule) return null;

  const onlineConfig = rule.config?.online;

  const spans = [
    ...(onlineConfig?.apply_to_root_span ? ["Root"] : []),
    ...(onlineConfig?.apply_to_span_names ?? []),
  ];

  return (
    <div className="flex w-64 flex-col gap-2 py-2">
      <div className="flex justify-between">
        <Radio className="size-4 text-primary-400" />
        <span className="text-xs text-primary-400">Online scorer</span>
      </div>

      <div>
        <span className="block font-medium break-words">{rule.name}</span>
        {rule.description && (
          <span className="block text-xs break-words text-primary-500">
            {rule.description}
          </span>
        )}
      </div>

      {onlineConfig && (
        <>
          {onlineConfig.scorers && onlineConfig.scorers.length > 0 && (
            <div className="flex min-h-[20px] flex-wrap items-start gap-1 text-xs text-primary-600">
              {onlineConfig.scorers.map((scorer, index) => (
                <span
                  key={index}
                  className="inline-flex items-center rounded-sm border border-primary-200 bg-primary-100 px-1 py-0.5 text-xs text-primary-700"
                >
                  <div className="flex items-center gap-1">
                    <Percent className="size-3" />
                    {scorer.type === "global"
                      ? scorer.name
                      : (status === "loaded" && functions[scorer.id]?.name) ||
                        scorer.id}
                  </div>
                </span>
              ))}
            </div>
          )}

          {onlineConfig.sampling_rate !== undefined &&
            onlineConfig.sampling_rate < 1 && (
              <div className="flex items-start gap-2 text-xs text-primary-600">
                {Math.round(onlineConfig.sampling_rate * 100)}% sampling rate
              </div>
            )}

          {onlineConfig.btql_filter && (
            <SyntaxHighlight
              content={onlineConfig.btql_filter}
              language="sql"
              className="break-all"
            />
          )}

          {(onlineConfig.apply_to_root_span ||
            onlineConfig.apply_to_span_names?.length) && (
            <div className="flex items-start gap-2 text-xs text-primary-600">
              {spans.join(", ")}
              {spans.length === 1 ? " span only" : " spans"}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export interface OnlineScorersProjectInfo {
  projectName: ProjectContextT["projectName"];
  config: ProjectContextT["config"];
  mutateConfig: ProjectContextT["mutateConfig"];
}

export const OnlineScorersCombobox = ({
  canAddRule,
  onCreateRule,
  onEditRule,
  onAsyncRule,
}: PropsWithChildren<{
  canAddRule?: boolean;
  onCreateRule?: () => void;
  onEditRule?: (rule: DiscriminatedProjectScore | null) => void;
  onAsyncRule?: () => void;
}>) => {
  const router = useRouter();
  const [searchValue, setSearchValue] = useState("");
  const params = useParams<{ org: string; project: string }>();
  const orgName = decodeURIComponentPatched(params?.org ?? "");
  const projCtx = useContext(ProjectContext);

  // use the provided project if it exists, else use context
  const { projectName, config: projectConfig } = projCtx;

  const onlineRules = useMemo(() => {
    const scores = projectConfig?.scores ?? [];
    return scores.filter((row) => isOnlineScore(row.score_type));
  }, [projectConfig?.scores]);

  const options: OnlineScorerOption[] = onlineRules.map((rule) => ({
    value: rule.name,
    label: rule.name,
    ruleId: rule.id,
    description: rule.description ?? undefined,
    rule: rule,
  }));

  const showCreateRuleOption =
    canAddRule &&
    searchValue !== "" &&
    !options.some((o) => o.value === searchValue);

  return (
    <>
      <Combobox<OnlineScorerOption>
        searchPlaceholder="Find online scoring rule"
        options={[
          ...options,
          ...(showCreateRuleOption
            ? [
                {
                  value: searchValue,
                  label: `Create "${searchValue}" rule`,
                  ruleId: "",
                  isCreate: true,
                },
              ]
            : []),
        ]}
        stayOpenOnChange
        variant="button"
        buttonVariant="border"
        onChange={(_, option) => {
          if (!option.isCreate) return;
          onCreateRule?.();
        }}
        placeholderLabel="Online scoring rules"
        onSearchChange={setSearchValue}
        noResultsLabel={
          <span className="flex h-4 items-center">
            No online scoring rules found
          </span>
        }
        bottomActions={[
          {
            label: (
              <>
                <Plus className="mr-2 size-3 flex-none" />
                Create online scoring rule
              </>
            ),
            onSelect: () => onCreateRule?.(),
            hidden: showCreateRuleOption,
          },
          {
            label: (
              <>
                <Settings2 className="mr-2 size-3 flex-none" />
                Manage online scoring rules
              </>
            ),
            onSelect: () => {
              router.push(
                `/app/${orgName}/p/${projectName}/configuration/online-scoring`,
              );
            },
          },
          [
            {
              label: (
                <>
                  <ListVideo className="mr-2 size-3 flex-none" />
                  Score past logs
                </>
              ),
              onSelect: () => {
                onAsyncRule?.();
              },
            },
          ],
        ]}
        renderOptionTooltip={(rule) => <OnlineScorerTooltip rule={rule.rule} />}
        renderOptionLabel={(rule) => {
          if (rule.isCreate) {
            return (
              <span className={"flex h-4 items-center gap-1.5"}>
                Create online scoring rule
                <span className="rounded bg-accent-100 p-0.5 text-xs font-medium text-accent-800">
                  {searchValue}
                </span>
              </span>
            );
          }

          return (
            <div className="group flex w-full min-w-0 items-center gap-2">
              <span className="flex-1 truncate">{rule.label}</span>
              <Pencil
                className="size-3 flex-none cursor-pointer text-primary-400 opacity-0 transition-all group-hover:opacity-100 hover:text-primary-600"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (onEditRule && rule.rule) {
                    onEditRule(rule.rule);
                  }
                }}
              />
            </div>
          );
        }}
      >
        <Button size="xs" Icon={Radio}>
          Online scoring
        </Button>
      </Combobox>
    </>
  );
};
