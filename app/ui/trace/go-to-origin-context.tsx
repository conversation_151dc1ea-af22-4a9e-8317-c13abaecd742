import { getObjectLink } from "#/app/app/[org]/object/getObjectLink";
import {
  getObjectLookupTypeDescription,
  objectLookupSupportedType,
  type SpanData,
} from "@braintrust/local";
import { createContext, useMemo } from "react";
import { toast } from "sonner";
import { type getObjectRedirectLink } from "#/app/app/[org]/object/object-actions";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { HTTPError } from "#/utils/http_error";
import { useOrg } from "#/utils/user";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";

export interface GoToOriginParams {
  /** Callback that navigates to the destination object. */
  callback: VoidFunction;
  /** Human readable name of the destination object type. */
  objectTypeName: string;
  /** Link that can be used interchangeably with callback. */
  link: string;
}

export const GoToOriginContext = createContext<GoToOriginParams | undefined>(
  undefined,
);

export const GoToOriginProvider = ({
  children,
  origin,
}: {
  children: React.ReactNode;
  origin?: SpanData["origin"];
}) => {
  const { name: orgName } = useOrg();
  const { getToken } = useAuth();
  const router = useRouter();

  const goToOriginParams: GoToOriginParams | undefined = useMemo(() => {
    const objectType = objectLookupSupportedType.safeParse(origin?.object_type);
    if (!origin || !objectType.success) {
      return;
    }
    return {
      callback: async () => {
        const toastId = toast.loading("Finding original object", {
          duration: Infinity,
        });
        try {
          const newPath = await invokeServerAction<
            typeof getObjectRedirectLink
          >({
            fName: "getObjectRedirectLink",
            args: { orgName, ...origin },
            getToken,
          });
          router.push(newPath);
        } catch (error) {
          if (error instanceof HTTPError) {
            toast.error(`Couldn’t find original object`, {
              description: `${error.message}`,
            });
          } else {
            throw error;
          }
        } finally {
          toast.dismiss(toastId);
        }
      },
      objectTypeName: getObjectLookupTypeDescription(objectType.data),
      link: getObjectLink({ orgName, ...origin }),
    };
  }, [orgName, origin, router, getToken]);

  return (
    <GoToOriginContext.Provider value={goToOriginParams}>
      {children}
    </GoToOriginContext.Provider>
  );
};
