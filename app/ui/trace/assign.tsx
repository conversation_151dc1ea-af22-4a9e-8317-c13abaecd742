import { MERGE_PATHS_FIELD, type TransactionId } from "braintrust/util";
import { useOptimisticState } from "#/utils/optimistic-update";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from "react";
import { UserPlus, UserRound } from "lucide-react";
import { Combobox } from "#/ui/combobox/combobox";
import { Button, type ButtonProps } from "#/ui/button";
import { type PropsWithChildren } from "react";
import { BasicTooltip } from "#/ui/tooltip";
import { useHotkeys } from "react-hotkeys-hook";
import { cn } from "#/utils/classnames";
import { OrgUsersContext } from "#/utils/org-users-context";
import { Avatar, AvatarGroup } from "#/ui/avatar";
import { type FormatterProps } from "#/ui/arrow-table";
import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { z } from "zod";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { type FetchOrgUsersOutput } from "#/utils/org-users";
import {
  addClause,
  type AnyClauseSpec,
  type ClauseChecker,
  makeBubble,
  type Search,
} from "#/utils/search/search";
import { singleQuote } from "#/utils/sql-utils";
import untruncateJson from "untruncate-json";
import { type DML } from "#/utils/mutable-object";
import { type TableSelection } from "#/ui/table/useTableSelection";
import { safeDeserializeUnknown, strMax, updatePathMut } from "#/utils/object";
import { MetadataField, TransactionIdField } from "@braintrust/local/query";
import { BT_ASSIGNMENTS, BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";
import { ObjectIdFields } from "@braintrust/local/api-schema";
import { useInviteNewMemberModal } from "#/ui/members/useInviteNewMemberModal";
import { useSendAssignmentNotification } from "#/utils/email/use-send-assignment-notification";

const assigneesSchema = z.array(z.string());

type UserOption = {
  value: string;
  label: string;
  description?: string | null;
  avatarUrl?: string | null;
  aliases?: string[];
};
export const AssignCombobox = ({
  selectedUsers,
  onChange,
  disabled,
  children,
}: PropsWithChildren<{
  selectedUsers: string[] | null;
  onChange: (label: string) => void;
  disabled?: boolean;
}>) => {
  const { orgUsers } = useContext(OrgUsersContext);
  const [searchText, setSearchText] = useState("");
  const { inviteNewMemberModal, showInviteNewMemberModal } =
    useInviteNewMemberModal();

  const options: UserOption[] = Object.values(orgUsers).reduce<UserOption[]>(
    (acc, user) => {
      if (user.user_type === "service_account") {
        return acc;
      }
      const userName =
        user.given_name && user.family_name
          ? `${user.given_name} ${user.family_name}`
          : undefined;
      acc.push({
        value: user.id,
        label: userName || user.email || user.id,
        description: userName ? user.email : undefined,
        avatarUrl: user.avatar_url,
        aliases: [user.email || "", userName || "", user.id],
      });
      return acc;
    },
    [],
  );

  return (
    <>
      <Combobox<UserOption>
        searchPlaceholder="Find member to assign"
        options={options}
        align="start"
        stayOpenOnChange
        selectedValues={selectedUsers ?? undefined}
        variant="button"
        buttonVariant="border"
        onChange={(_, option) => {
          if (disabled) {
            return;
          }
          onChange(option.value);
        }}
        onOpenChange={() => {
          setSearchText("");
        }}
        onSearchChange={setSearchText}
        placeholderLabel="Members"
        noResultsLabel={
          <span className="flex h-4 items-center">No members found</span>
        }
        bottomActions={[
          {
            label: (
              <div className="flex max-w-full items-center gap-x-2 text-xs text-primary-500">
                <UserPlus size={16} />
                Invite a member
              </div>
            ),
            onSelect: () => {
              showInviteNewMemberModal(searchText);
            },
          },
        ]}
        disabled={disabled}
        renderOptionLabel={(user) => {
          const userContent = (
            <div className="flex max-w-full items-center gap-1 overflow-hidden py-px">
              <Avatar imgUrl={user.avatarUrl} size="xs" />
              <span>{user.label}</span>
            </div>
          );
          return (
            <div
              className={cn(
                "w-full overflow-hidden",
                disabled && "cursor-not-allowed opacity-50",
              )}
            >
              {user.description ? (
                <BasicTooltip tooltipContent={user.description}>
                  {userContent}
                </BasicTooltip>
              ) : (
                userContent
              )}
            </div>
          );
        }}
      >
        {children}
      </Combobox>
      {inviteNewMemberModal}
    </>
  );
};

export function updateAssignmentsList(
  assignments: string[],
  userId: string,
  added: boolean,
) {
  const oldAssignments = assignments.filter((a) => a !== userId);
  return added ? oldAssignments.concat(userId) : oldAssignments;
}

export function Assign({
  assignedUsers,
  rowId,
  xactId,
  updateAssignments,
  buttonVariant,
  buttonClassName,
  disabled,
}: {
  assignedUsers: string[];
  rowId: string;
  xactId: TransactionId;
  updateAssignments:
    | ((users: string[]) => Promise<TransactionId | null>)
    | undefined;
  buttonVariant?: ButtonProps["variant"];
  buttonClassName?: string;
  disabled?: boolean;
}) {
  const [selectedUsers, setSelectedUsers] = useState(assignedUsers);

  const { save } = useOptimisticState({
    xactId,
    value: assignedUsers,
    save: updateAssignments || (() => Promise.resolve(null)),
    rowKey: rowId,
    onUpdatedValue: setSelectedUsers,
  });

  const buttonRef = useRef<HTMLButtonElement>(null);
  useHotkeys(
    "a",
    (e) => {
      if (buttonRef.current) {
        e.preventDefault();
        buttonRef.current.click();
      }
    },
    {
      description: "Assign user",
    },
  );

  const { sendAssignNotification } =
    useSendAssignmentNotification(assignedUsers);

  const handleChange = useCallback(
    async (label: string) => {
      const isSelected = selectedUsers?.includes(label);
      const newUsers = updateAssignmentsList(selectedUsers, label, !isSelected);
      setSelectedUsers(newUsers);
      try {
        await save(newUsers);
      } catch (error) {
        console.error("Error saving assignments:", error);
        return;
      }
      sendAssignNotification(newUsers);
    },
    [selectedUsers, save, sendAssignNotification],
  );

  return (
    <AssignCombobox
      onChange={handleChange}
      selectedUsers={selectedUsers}
      disabled={disabled}
    >
      <div>
        <BasicTooltip
          tooltipContent={
            <span>
              Assign <span className="ml-2.5 inline-block opacity-50">A</span>
            </span>
          }
          side="bottom"
        >
          <Button
            role="combobox"
            aria-controls="combobox-options"
            className={cn("text-primary-500", buttonClassName)}
            size="xs"
            ref={buttonRef}
            variant={buttonVariant}
            Icon={UserRound}
          >
            <span className="hidden @lg:block">Assign</span>
          </Button>
        </BasicTooltip>
      </div>
    </AssignCombobox>
  );
}

export function useAssignmentsFormatter({
  orgUsers,
  onAssignmentClick,
}: {
  orgUsers: FetchOrgUsersOutput;
  onAssignmentClick?: (userId: string) => void;
}) {
  return useMemo(() => {
    return AssignmentsFormatterFactory({ orgUsers, onAssignmentClick });
  }, [orgUsers, onAssignmentClick]);
}

export function AssignmentsFormatterFactory({
  orgUsers,
  onAssignmentClick,
}: {
  orgUsers: FetchOrgUsersOutput;
  onAssignmentClick?: (userId: string) => void;
}) {
  return function AssignmentsFormatter<
    TsTable extends { [BT_IS_GROUP]?: boolean },
    TsValue,
  >({
    value: valueProp,
    cell,
    renderForTooltip,
  }: FormatterProps<TsTable, TsValue>) {
    const valueString = isDiffObject(valueProp)
      ? valueProp[DiffRightField]
      : valueProp;

    let value: string[] | null = null;

    try {
      const untruncated =
        typeof valueString === "string"
          ? untruncateJson(valueString)
          : valueString;
      // We can't really trust the data coming through here, so try to parse it as the right type
      // and fall back to null if it doesn't work.
      const parsed = assigneesSchema.safeParse(
        typeof untruncated === "string"
          ? JSON.parse(untruncated)
          : untruncated &&
              typeof untruncated === "object" &&
              untruncated.toArray
            ? untruncated.toArray()
            : untruncated,
      );

      if (parsed.success && parsed.data.length > 0) {
        value = parsed.data;
      }
    } catch {}

    const isEmpty = value === null || valueString == null || value.length === 0;

    if (isEmpty) {
      return cell.row.original[BT_IS_GROUP] ? null : <NullFormatter />;
    }

    const users = (value ?? []).reduce<
      {
        userId: string;
        name: string;
        imgUrl: string | null | undefined;
        email?: string | null;
        className?: string;
      }[]
    >((acc, userId) => {
      const orgUser = orgUsers[userId];
      if (orgUser) {
        const userName =
          orgUser.given_name && orgUser.family_name
            ? `${orgUser.given_name} ${orgUser.family_name}`
            : undefined;
        acc.push({
          userId,
          name: userName || orgUser?.email || orgUser?.id,
          email: userName ? orgUser?.email : undefined,
          imgUrl: orgUser?.avatar_url,
          className: "size-5",
        });
      }
      return acc;
    }, []);

    if (renderForTooltip) {
      return (
        <div className="flex flex-col">
          <div className="mb-1.5 w-full text-xs text-primary-500">
            Filter by assigned user
          </div>
          <div className="flex flex-col flex-wrap gap-1">
            {users.map((user, idx) => (
              <Button
                key={idx}
                variant="ghost"
                size="sm"
                className="flex items-center justify-start gap-1 pl-1 text-left hover:bg-primary-200"
                onClick={(e) => {
                  if (!onAssignmentClick) {
                    return;
                  }
                  e.stopPropagation();
                  onAssignmentClick(user.userId);
                }}
              >
                <Avatar imgUrl={user.imgUrl} />
                <div className="flex flex-col">
                  <span className="text-xs">{user.name}</span>
                  {user.email && (
                    <span className="text-xs text-primary-500">
                      {user.email}
                    </span>
                  )}
                </div>
              </Button>
            ))}
          </div>
        </div>
      );
    }

    const avatarGroup = <AvatarGroup avatars={users} />;
    if (users.length === 1) {
      return (
        <div className="flex w-full items-center gap-1">
          {avatarGroup}
          <span className="flex-1 truncate">{users[0].name}</span>
        </div>
      );
    }
    return avatarGroup;
  };
}

export function setAssignmentsSearchFn(
  clauseChecker: ClauseChecker | null,
  setSearch: Dispatch<SetStateAction<Search>>,
) {
  return async (userId: string) => {
    if (!clauseChecker || !setSearch) {
      return;
    }
    const clause = {
      type: "filter" as const,
      text: `${BT_ASSIGNMENTS} includes ${singleQuote(userId)}`,
      label: `Assigned to includes ${singleQuote(userId)}`,
    };
    const checkResult = await clauseChecker(clause);
    if (checkResult.type !== "checked") {
      return;
    }
    setSearch((s) =>
      addClause(s, {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        ...(clause as AnyClauseSpec),
        ...checkResult.extraFields,
        bubble: makeBubble({
          clause,
          setSearch,
        }),
      }),
    );
  };
}

export function AssignBulkEditor({
  dml,
  selectionProps: { selectedRows, getSelectedRowsWithData },
  buttonClassName,
  isPlayground,
}: {
  dml: DML;
  selectionProps: Pick<
    TableSelection,
    "selectedRows" | "getSelectedRowsWithData"
  >;
  buttonClassName?: string;
  isPlayground?: boolean;
}) {
  const [isPending, setIsPending] = useState(false);
  // Hopefully this isn't too expensive. We need to recompute the maximum transaction id each time
  // this component is rendered, because getSelectedRowsWithData itself doesn't change. This computation,
  // however, allows us to figure out if other things should change.
  const lastMaxXactId = useRef<string>("0");
  const maxXactId = strMax(
    getSelectedRowsWithData().reduce(
      (max, row) => strMax(max, row[TransactionIdField] ?? "0"),
      "0",
    ),
    // eslint-disable-next-line react-compiler/react-compiler
    lastMaxXactId.current,
  );
  // eslint-disable-next-line react-compiler/react-compiler
  lastMaxXactId.current = maxXactId;

  const recomputeUnionAssignments = useCallback(() => {
    const userIds = new Set<string>();
    getSelectedRowsWithData().forEach((row) => {
      const rowAssignments = z
        .array(z.string())
        .safeParse(
          safeDeserializeUnknown(row[MetadataField] ?? "{}")?.[
            BT_ASSIGNMENTS_META_FIELD
          ],
        );
      if (rowAssignments.success) {
        rowAssignments.data.forEach((userId) => userIds.add(userId));
      }
    });
    return Array.from(userIds);
  }, [getSelectedRowsWithData]);

  const { unionAssignments } = useMemo(() => {
    return { unionAssignments: recomputeUnionAssignments(), maxXactId };
  }, [recomputeUnionAssignments, maxXactId]);

  const rowId = useMemo(
    () => Object.keys(selectedRows).join(","),
    [selectedRows],
  );

  const updateAssignmentWithPendingTracking = useCallback(
    async (newAssignments: string[]) => {
      setIsPending(true);
      try {
        // Compute the delta based on unionAssignments, so that even if a change hasn't saved yet,
        // we'll save the correct one relative to the optimistic state.
        const unionAssignments = recomputeUnionAssignments();
        const addedAssignments = newAssignments.filter(
          (assignment) => !unionAssignments.includes(assignment),
        );
        const removedAssignments = unionAssignments.filter(
          (assignment) => !newAssignments.includes(assignment),
        );
        const updatedRows = await Promise.all(
          getSelectedRowsWithData().map(async (row) => {
            const rowAssignments = assigneesSchema.safeParse(
              safeDeserializeUnknown(
                row[MetadataField]?.[BT_ASSIGNMENTS_META_FIELD] ?? "[]",
              ),
            );
            let newAssignments = rowAssignments.success
              ? rowAssignments.data
              : [];
            for (const assignment of addedAssignments) {
              newAssignments = updateAssignmentsList(
                newAssignments,
                assignment,
                true,
              );
            }
            for (const assignment of removedAssignments) {
              newAssignments = updateAssignmentsList(
                newAssignments,
                assignment,
                false,
              );
            }

            const updates = [
              {
                path: [MetadataField, BT_ASSIGNMENTS_META_FIELD],
                newValue: newAssignments,
              },
            ];

            // This logic roughly mirrors the implementation of dml.update()
            const updatedRow = (await dml.prepareUpdates([row], updates))[0];

            const newRow = {
              id: updatedRow.id,
              ...Object.fromEntries(
                ObjectIdFields.filter((k) => updatedRow[k]).map((k) => [
                  k,
                  updatedRow[k],
                ]),
              ),
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              [MERGE_PATHS_FIELD]: [] as string[][],
            };
            for (const { path, newValue } of updates) {
              updatePathMut(newRow, path, newValue);
              newRow[MERGE_PATHS_FIELD].push(path);
            }

            return { updatedRow, newRow };
          }),
        );

        const rowsWithUpdates = updatedRows.map((row) => row.updatedRow);
        const pathUpdates = updatedRows.map((row) => row.newRow);
        return await dml.upsert(rowsWithUpdates, { pathUpdates });
      } finally {
        setIsPending(false);
      }
    },
    [dml, getSelectedRowsWithData, recomputeUnionAssignments],
  );

  return (
    <Assign
      buttonClassName={buttonClassName}
      disabled={isPending}
      updateAssignments={updateAssignmentWithPendingTracking}
      assignedUsers={unionAssignments}
      xactId={maxXactId}
      rowId={rowId}
    />
  );
}
