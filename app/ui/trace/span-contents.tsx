"use client";

import { TRANSACTION_ID_FIELD, type TransactionId } from "braintrust/util";
import React, {
  Suspense,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  DiffRightField,
  getDiffRight,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { ManualReview } from "#/ui/trace/manual-review";
import {
  AppWindow,
  ArrowDownRight,
  ArrowUpRight,
  BarChart,
  CircleAlert,
  Columns2,
  CurlyBraces,
  DiffIcon,
  Equal,
  GalleryVerticalEnd,
  GripVertical,
  ListChecks,
  Maximize2,
  MoveHorizontal,
  Percent,
} from "lucide-react";
import {
  CollapsibleSection,
  type CollapsibleSectionProps,
} from "#/ui/collapsible-section";
import { MetricSection } from "#/ui/trace/trace-metrics";
import { Loading } from "#/ui/loading";
import {
  DataDisplay,
  dataDisplayedFieldSchema,
  DiffDataDisplay,
} from "#/ui/trace/data-display";
import { toast } from "sonner";
import { DiffScoreObject } from "./diff-score-object";
import { ReviewButton } from "#/app/app/[org]/p/[project]/review-button";
import { ErrorDisplay } from "./trace-error";
import {
  type ConfiguredScore,
  type Span,
  type SpanScore,
  type LoadedTrace,
} from "./graph";
import { type ManualScoreUpdate, type TraceViewParams } from "./trace";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { useSystemMetrics } from "./use-system-metrics";
import { type ProjectConfig } from "#/utils/score-config";
import { IS_AUDIT_LOG_AVAILABLE, newId } from "#/utils/btapi/btapi";
import { useUser } from "#/utils/user";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { Activity } from "./activity";
import {
  type UpdateRowFn,
  type CommentFn,
  type DeleteCommentFn,
} from "#/utils/mutable-object";
import {
  type AuditLogRow,
  type CommentData,
  ObjectIdFields,
} from "@braintrust/local/api-schema";
import { TransactionIdField } from "#/utils/duckdb";
import { useDiffModeState } from "#/ui/query-parameters";
import { type SearchResultField } from "./trace-search-context";
import { type MultiTraceContext } from "#/ui/copilot/trace";
import { IFrameViewer, RefreshIframeButton } from "#/ui/iframe-viewer";
import { parseObjectJSON } from "#/utils/schema";
import { Button } from "#/ui/button";
import { getSearchResultFieldClassName } from "./search-utils";
import { FullscreenTraceAction } from "./fullscreen-trace-action";
import { cn } from "#/utils/classnames";
import { useExpandedSpanIframe } from "./use-expanded-span-iframe";
import { SpanField, useSpanFieldOrder } from "./use-span-field-order.tsx";
import StyledDndContext from "#/ui/styled-dnd-context";
import { SortableContext, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { BasicTooltip } from "#/ui/tooltip";
import {
  type CustomColumnDefinition,
  type CustomColumn,
} from "#/utils/custom-columns/use-custom-columns";
import { deserializePlainStringAsJSON } from "#/utils/object";
import { RESERVED_TAGS } from "#/app/app/[org]/p/[project]/configuration/tags/tag-dialog";
import { isOnlineScore } from "@braintrust/local/query";
import { ErrorBanner } from "#/ui/error-banner";
import { type SpanIFrame } from "@braintrust/typespecs";
import { type ModelCosts } from "#/ui/prompts/models";
import { selectorForField } from "./scrollable-container-with-options";
import { z } from "zod";
import {
  validateNoReservedMetadataFields,
  removeReservedMetadataFields,
  reservedMetadataSchema,
} from "#/utils/metadata";
import { BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";
import { usePathname } from "next/navigation";

export const SpanContents = ({
  isDatasetRow,
  isRoot,
  trace,
  span,
  onApplySearch,
  autoScores,
  rootManualScores,
  shouldRenderManualScores,
  shouldRenderSpanDetails,
  projectConfig,
  comparisonClassName,
  commentFn,
  deleteCommentFn,
  resetForcedSpanCollapse,
  collapseSpanState,
  resultIndex,
  searchResultFields,
  searchQuery,
  manualScores,
  objectType,
  experimentNames,
  editableFields,
  hiddenFields,
  updateRow,
  copilotContext,
  onUpdateManualReviewScore,
  hasError,
  customColumns,
  auditLogData,
  defaultFieldOrder,
  defaultCollapsedFields,
  localStorageKeyPrefix: localStorageKeyPrefixProp,
  diffMessage,
  allAvailableModelCosts,
  scrollTo,
}: {
  isDatasetRow?: boolean;
  isRoot: boolean;
  trace: LoadedTrace;
  span: Span;
  rootManualScores?: Record<string, SpanScore>;
  projectConfig: ProjectConfig;
  onApplySearch: ApplySearch;
  autoScores?: Record<string, SpanScore>;
  shouldRenderManualScores?: boolean;
  shouldRenderSpanDetails?: boolean;
  comparisonClassName: string;
  commentFn?: CommentFn;
  deleteCommentFn?: DeleteCommentFn;
  resetForcedSpanCollapse?: () => void;
  collapseSpanState?: "collapsed" | "expanded" | "mixed";
  resultIndex: number | null;
  searchResultFields: SearchResultField[];
  searchQuery: string;
  manualScores?: Record<string, ConfiguredScore>;
  objectType: TraceViewParams["objectType"];
  experimentNames?: TraceViewParams["experimentNames"];
  editableFields: TraceViewParams["editableFields"];
  hiddenFields?: TraceViewParams["hiddenFields"];
  defaultFieldOrder?: SpanField[];
  defaultCollapsedFields?: SpanField[];
  updateRow?: UpdateRowFn;
  copilotContext: MultiTraceContext;
  hasError?: boolean;
  onUpdateManualReviewScore?: (
    scoreUpdates: ManualScoreUpdate[],
  ) => Promise<(TransactionId | null)[]>;
  customColumns?: CustomColumn[] | CustomColumnDefinition[];
  auditLogData?: AuditLogRow[];
  localStorageKeyPrefix?: string;
  diffMessage?: string;
  allAvailableModelCosts?: Record<string, ModelCosts>;
  scrollTo?: string | null;
}) => {
  useEffect(() => {
    if (scrollTo) {
      const fieldElement = document.querySelector(selectorForField(scrollTo));
      if (!fieldElement) return;
      fieldElement.scrollIntoView(true);
    }
  }, [scrollTo]);

  const { user } = useUser();
  const {
    projectId,
    config: { span_iframes },
  } = useContext(ProjectContext);
  const pathname = usePathname();

  const entityName = useMemo(() => {
    if (!pathname) {
      return;
    }

    return pathname.split("/").pop();
  }, [pathname]);

  const _systemMetrics = useSystemMetrics({
    selectedSpan: span,
    rootStartTime: getDiffRight(trace.root.data.metrics?.start),
  });
  const systemMetrics = trace?.root ? _systemMetrics : undefined;

  const [diffModeState] = useDiffModeState();
  const { setExpandedFrame } = useExpandedSpanIframe({});

  const tagsObject = trace?.root.data.tags?.filter(
    (t) => !RESERVED_TAGS.includes(t),
  );
  const tagsInMetrics = tagsObject?.map((t) => ({
    name: t,
    ...(projectConfig?.tags.find((tag) => tag.name === t) || {}),
  }));
  const assignedUsers = useMemo(() => {
    try {
      const metadata = JSON.parse(trace?.root.data.metadata);
      const parsed = z
        .array(z.string())
        .nullish()
        .safeParse(metadata[BT_ASSIGNMENTS_META_FIELD]);
      return parsed.success ? (parsed.data ?? []) : [];
    } catch {
      return [];
    }
  }, [trace?.root.data.metadata]);

  const localStorageKeyPrefix =
    localStorageKeyPrefixProp ?? `${projectId}-${objectType}`;

  // This is useful for debugging whether/when the audit log gets updated
  /*
  const { data: auditLogCount } = useDBQuery(
    expandedRowParams.auditLogScan
      ? `
      SELECT COUNT(*) c FROM (${expandedRowParams.auditLogScan}) "t"
    `
      : null,
    expandedRowParams.auditLogScanReady
  );
  console.log("AUDIT LOG COUNT", auditLogCount?.toArray()[0]?.toJSON().c);
  */

  const addComment = useMemo(() => {
    return (
      commentFn &&
      (async (comment: CommentData) => {
        if (!span) {
          // This should never happen
          toast.error("Cannot post a comment without selecting a span");
          return { transactionId: null };
        }

        const row = {
          id: span.data.id,
          [TransactionIdField]: span.data[TRANSACTION_ID_FIELD],
          ...Object.fromEntries(
            ObjectIdFields.map((k) => {
              const s = span.data[k];
              return [k, s && isDiffObject<string>(s) ? s[DiffRightField] : s];
            }),
          ),
        };

        const commentId = newId();
        const transactionId = await commentFn!([
          {
            id: commentId,
            row,
            comment,
          },
        ]);
        return { transactionId, commentId };
      })
    );
  }, [span, commentFn]);

  // Disable the comment section (audit log) in two cases:
  // - IS_AUDIT_LOG_AVAILABLE is false when the API cannot serve it (this should
  //   be removed once everyone updates their API: DEPRECATION_NOTICE)
  // - Anonymous users
  const activitySection = IS_AUDIT_LOG_AVAILABLE && user && auditLogData && (
    <div>
      <SpanCollapsibleSection
        title="Activity"
        Icon={GalleryVerticalEnd}
        id={`${SpanField.ACTIVITY}-section`}
        onChange={resetForcedSpanCollapse}
        forcedState={collapseSpanState}
        localStorageKey={`${localStorageKeyPrefix}-traceActivity`}
        defaultCollapsed={defaultCollapsedFields?.includes(SpanField.ACTIVITY)}
      >
        <Activity
          auditLog={auditLogData}
          addComment={addComment}
          deleteComment={deleteCommentFn}
          objectName={isDatasetRow ? "dataset row" : "span"}
          rootObjectName={
            isRoot ? (isDatasetRow ? "dataset row" : "row") : undefined
          }
          entityName={entityName}
        />
      </SpanCollapsibleSection>
    </div>
  );

  const hasManualScores = manualScores && Object.keys(manualScores).length > 0;

  const parsedSpanData = useMemo(
    () =>
      span_iframes.length === 0
        ? undefined
        : parseObjectJSON(objectType, span.data),
    [span.data, objectType, span_iframes],
  );

  const fullscreenAction = useMemo(
    () => <FullscreenTraceAction diffModeState={diffModeState} />,
    [diffModeState],
  );

  const expectedExtraActions = useMemo(
    () => (
      <>
        {editableFields.includes("expected") && !isDatasetRow && (
          <UseOutputValueButton span={span} updateRow={updateRow} />
        )}
        {fullscreenAction}
      </>
    ),
    [editableFields, fullscreenAction, isDatasetRow, span, updateRow],
  );

  const { order, onDragEnd, onToggleLayout } = useSpanFieldOrder({
    defaultFields: defaultFieldOrder,
    objectType,
    customColumns,
    spanIframes: span_iframes,
  });

  const parseMetadataValue = useCallback((value: string) => {
    try {
      const parsed = JSON.parse(value);
      if (
        typeof parsed !== "object" ||
        parsed === null ||
        Array.isArray(parsed)
      ) {
        return value;
      }

      const filtered = removeReservedMetadataFields(parsed);
      if (Object.keys(filtered).length === 0) {
        return null;
      }
      return JSON.stringify(filtered);
    } catch {
      return value;
    }
  }, []);

  const validateMetadataValue = useCallback((value: unknown) => {
    if (!value) {
      return;
    }

    if (typeof value !== "object" || Array.isArray(value)) {
      return;
    }

    validateNoReservedMetadataFields(value);
  }, []);

  const updateMetadataRow = useCallback(
    async (row: unknown, path: string[], newValue: unknown) => {
      if (!updateRow) {
        return null;
      }

      try {
        const rowObject = z.record(z.string(), z.unknown()).parse(row);
        const currentMetadata =
          rowObject.metadata && typeof rowObject.metadata === "string"
            ? JSON.parse(rowObject.metadata)
            : {};
        const parsedMetadata = z
          .record(z.string(), z.unknown())
          .nullish()
          .safeParse(currentMetadata);
        if (!parsedMetadata.success) {
          return await updateRow(row, path, newValue);
        }
        const newMetadata =
          typeof newValue === "string" ? JSON.parse(newValue) : newValue;
        const reservedMetadata = reservedMetadataSchema.parse(
          parsedMetadata.data,
        );

        return await updateRow(row, path, {
          ...newMetadata,
          ...reservedMetadata,
        });
      } catch (error) {
        return await updateRow(row, path, newValue);
      }
    },
    [updateRow],
  );

  const shouldShowOutputVsExpected = useMemo(() => {
    return (
      diffModeState?.enabled &&
      diffModeState.enabledValue === "expected_output" &&
      // This is a bit of a dirty hack. If you are in experiment diff mode, until the row
      // is refreshed, the output may still be a diff
      !isDiffObject(span.data.output)
    );
  }, [diffModeState, span.data.output]);

  const reorderableSpanFields = order
    .map((spanFieldOrder) => {
      const parsedField = dataDisplayedFieldSchema.safeParse(
        spanFieldOrder.column_id,
      );
      if (parsedField.success && hiddenFields?.includes(parsedField.data)) {
        return null;
      }
      const fieldId = spanFieldOrder.column_id;
      const layout = spanFieldOrder.layout;

      if (fieldId === SpanField.METRICS) {
        return {
          id: fieldId,
          layout,
          component: (
            <div
              className={getSearchResultFieldClassName({
                resultIndex,
                searchResultFields,
                spanId: span.span_id,
                field: "metrics",
              })}
            >
              <SpanCollapsibleSection
                title="Metrics"
                Icon={BarChart}
                id={`${SpanField.METRICS}-section`}
                localStorageKey={`${localStorageKeyPrefix}-traceMetrics`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.METRICS,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <MetricSection
                  systemMetrics={systemMetrics}
                  spanMetrics={span.data.metrics}
                  tags={tagsInMetrics}
                  assignments={assignedUsers}
                  shouldOnlyRenderTagsAndAssignments={!shouldRenderSpanDetails}
                  spanId={span.span_id}
                  comparisonClassName={comparisonClassName}
                />
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Scores
      if (
        fieldId === SpanField.SCORES &&
        autoScores &&
        Object.keys(autoScores).length > 0
      ) {
        return {
          id: fieldId,
          layout,
          component: (
            <div
              className={getSearchResultFieldClassName({
                resultIndex,
                searchResultFields,
                spanId: span.span_id,
                field: "scores",
              })}
            >
              <SpanCollapsibleSection
                title="Scores"
                Icon={Percent}
                id={`${SpanField.SCORES}-section`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                localStorageKey={`${localStorageKeyPrefix}-traceScores`}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.SCORES,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <div className="group @container mb-2 flex flex-wrap gap-x-2 gap-y-3 font-inter text-xs">
                  <DiffScoreObject
                    onApplySearch={onApplySearch}
                    scores={autoScores}
                    comparisonClassName={comparisonClassName}
                  />
                </div>
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Human review
      if (
        fieldId === SpanField.HUMAN_REVIEW &&
        hasManualScores &&
        shouldRenderManualScores &&
        onUpdateManualReviewScore
      ) {
        return {
          id: fieldId,
          layout,
          component: (
            <div>
              <SpanCollapsibleSection
                Icon={ListChecks}
                localStorageKey={`${localStorageKeyPrefix}-traceManualScores`}
                title="Human review"
                id={`${SpanField.HUMAN_REVIEW}-section`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.HUMAN_REVIEW,
                )}
                actionRight={
                  <ReviewButton variant="default">
                    <Maximize2 className="size-3" />
                  </ReviewButton>
                }
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <div className="@container mb-2 flex flex-wrap gap-x-2 gap-y-3 font-inter text-xs">
                  <ManualReview
                    rootScores={rootManualScores}
                    scores={manualScores}
                    rowKey={span.data.id}
                    xactId={
                      span.data[
                        TRANSACTION_ID_FIELD
                      ] /* XXX Should create one for the score */
                    }
                    updateScores={onUpdateManualReviewScore}
                  />
                </div>
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Error
      if (fieldId === SpanField.ERROR && hasError) {
        return {
          id: fieldId,
          layout,
          component: (
            <div>
              <SpanCollapsibleSection
                title={
                  <span className="inline-flex items-center gap-1">
                    <CircleAlert className="size-3 text-bad-500" /> Error
                  </span>
                }
                localStorageKey={`${localStorageKeyPrefix}-traceError`}
                id={`${SpanField.ERROR}-section`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.ERROR,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <ErrorDisplay error={span.data.error} />
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Input
      if (fieldId === SpanField.INPUT) {
        return {
          id: fieldId,
          layout,
          component: (
            <div
              className={getSearchResultFieldClassName({
                resultIndex,
                searchResultFields,
                spanId: span.span_id,
                field: "input",
              })}
            >
              <SpanCollapsibleSection
                title="Input"
                Icon={ArrowUpRight}
                id={`${SpanField.INPUT}-section`}
                localStorageKey={`${localStorageKeyPrefix}-traceInput`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.INPUT,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <DataDisplay
                  comparisonClassName={comparisonClassName}
                  searchQuery={searchQuery}
                  span={span}
                  fieldName="input"
                  diffMessage={diffMessage}
                  foldStateId={`${localStorageKeyPrefix}-input`}
                  onApplySearch={(query, replace, opts) => {
                    const q = (query ?? "").replace(/^\$\./, "input.");
                    toast.promise(
                      onApplySearch(q, replace, {
                        ...opts,
                        label: q,
                      }),
                      {
                        loading: "Applying filter",
                        success: "Filter applied",
                        error: "Failed to apply filter",
                      },
                    );
                    return Promise.resolve();
                  }}
                  updateRow={
                    editableFields.includes("input") ? updateRow : undefined
                  }
                  experimentNames={experimentNames}
                  localStorageKey={`${localStorageKeyPrefix}-traceInput`}
                  copilotContext={copilotContext}
                  extraActions={fullscreenAction}
                />
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Output vs expected
      if (
        fieldId === SpanField.OUTPUT_VS_EXPECTED &&
        shouldShowOutputVsExpected
      ) {
        return {
          id: fieldId,
          layout,
          component: (
            <div>
              <SpanCollapsibleSection
                Icon={DiffIcon}
                title="Output vs. expected"
                id={`${SpanField.OUTPUT_VS_EXPECTED}-section`}
                localStorageKey={`${localStorageKeyPrefix}-traceOutputVsExpected`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.OUTPUT_VS_EXPECTED,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <div className="rounded-md border p-2 text-xs">
                  <DiffDataDisplay
                    rowId={span.data.id}
                    xactId={span.data[TRANSACTION_ID_FIELD] ?? null}
                    leftData={span.data.expected}
                    rightData={span.data.output}
                    attachmentsLocalStorageKey={`${localStorageKeyPrefix}-traceOutputVsExpected-attachments`}
                    extraActions={fullscreenAction}
                    experimentNames={experimentNames}
                    diffMessage="Showing diff from output -> expected"
                  />
                </div>
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Output
      if (
        fieldId === SpanField.OUTPUT &&
        !shouldShowOutputVsExpected &&
        // The difference between undefined and null here is actually important.
        // undefined means that the field does not exist in the data (i.e. `output` does not
        // exist for datasets), while `null` means that the field exists but is empty.
        (span.data.output !== undefined || editableFields.includes("output"))
      ) {
        return {
          id: fieldId,
          layout,
          component: (
            <div
              className={getSearchResultFieldClassName({
                resultIndex,
                searchResultFields,
                spanId: span.span_id,
                field: "output",
              })}
            >
              <SpanCollapsibleSection
                title="Output"
                Icon={ArrowDownRight}
                id={`${SpanField.OUTPUT}-section`}
                localStorageKey={`${localStorageKeyPrefix}-traceOutput`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.OUTPUT,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <DataDisplay
                  comparisonClassName={comparisonClassName}
                  searchQuery={searchQuery}
                  span={span}
                  fieldName="output"
                  foldStateId={`${localStorageKeyPrefix}-output`}
                  diffMessage={diffMessage}
                  updateRow={
                    editableFields.includes("output") ? updateRow : undefined
                  }
                  experimentNames={experimentNames}
                  onApplySearch={(query, replace, opts) => {
                    const q = (query ?? "").replace(/^\$\./, "output.");
                    return onApplySearch(q, replace, {
                      ...opts,
                      label: q,
                    });
                  }}
                  localStorageKey={`${localStorageKeyPrefix}-traceOutput`}
                  copilotContext={copilotContext}
                  extraActions={fullscreenAction}
                />
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      // Expected
      if (
        fieldId === SpanField.EXPECTED &&
        !shouldShowOutputVsExpected &&
        (span.data.expected !== undefined ||
          editableFields.includes("expected"))
      ) {
        return {
          id: fieldId,
          layout,
          component: (
            <div
              className={getSearchResultFieldClassName({
                resultIndex,
                searchResultFields,
                spanId: span.span_id,
                field: "expected",
              })}
            >
              <SpanCollapsibleSection
                Icon={Equal}
                title="Expected"
                id={`${SpanField.EXPECTED}-section`}
                localStorageKey={`${localStorageKeyPrefix}-traceExpected`}
                onChange={resetForcedSpanCollapse}
                forcedState={collapseSpanState}
                defaultCollapsed={defaultCollapsedFields?.includes(
                  SpanField.EXPECTED,
                )}
                draggableId={fieldId}
                layout={layout}
                onToggleLayout={onToggleLayout}
              >
                <DataDisplay
                  comparisonClassName={comparisonClassName}
                  searchQuery={searchQuery}
                  span={span}
                  fieldName="expected"
                  foldStateId={`${localStorageKeyPrefix}-expected`}
                  diffMessage={diffMessage}
                  updateRow={
                    editableFields.includes("expected") ? updateRow : undefined
                  }
                  experimentNames={experimentNames}
                  onApplySearch={(query, replace, opts) => {
                    const q = (query ?? "").replace(/^\$\./, "expected.");
                    return onApplySearch(q, replace, {
                      ...opts,
                      label: q,
                    });
                  }}
                  localStorageKey={`${localStorageKeyPrefix}-traceExpected`}
                  copilotContext={copilotContext}
                  extraActions={expectedExtraActions}
                />
              </SpanCollapsibleSection>
            </div>
          ),
        };
      }

      if (fieldId === SpanField.METADATA) {
        return {
          id: fieldId,
          layout,
          component: (
            <>
              <div
                className={getSearchResultFieldClassName({
                  resultIndex,
                  searchResultFields,
                  spanId: span.span_id,
                  field: "metadata",
                })}
              >
                <SpanCollapsibleSection
                  Icon={CurlyBraces}
                  title="Metadata"
                  id={`${SpanField.METADATA}-section`}
                  localStorageKey={`${localStorageKeyPrefix}-traceMetadata`}
                  onChange={resetForcedSpanCollapse}
                  forcedState={collapseSpanState}
                  defaultCollapsed={defaultCollapsedFields?.includes(
                    SpanField.METADATA,
                  )}
                  draggableId={fieldId}
                  layout={layout}
                  onToggleLayout={onToggleLayout}
                >
                  <DataDisplay
                    comparisonClassName={comparisonClassName}
                    parseData={parseMetadataValue}
                    searchQuery={searchQuery}
                    span={span}
                    fieldName="metadata"
                    foldStateId={`${localStorageKeyPrefix}-metadata`}
                    diffMessage={diffMessage}
                    updateRow={
                      editableFields.includes("metadata") && updateRow
                        ? updateMetadataRow
                        : undefined
                    }
                    validateFn={validateMetadataValue}
                    isObjectOnly
                    experimentNames={experimentNames}
                    onApplySearch={(query, replace, opts) => {
                      const q = (query ?? "").replace(/^\$\./, "metadata.");
                      return onApplySearch(q, replace, {
                        ...opts,
                        label: q,
                      });
                    }}
                    localStorageKey={`${localStorageKeyPrefix}-traceMetadata`}
                    copilotContext={copilotContext}
                    extraActions={fullscreenAction}
                  />
                </SpanCollapsibleSection>
              </div>
            </>
          ),
        };
      }

      // Custom columns
      if (
        typeof fieldId === "string" &&
        fieldId.startsWith("customColumn-") &&
        !span.span_parents?.length
      ) {
        const customColumn = customColumns?.find(
          (c) => c.id === fieldId.replace("customColumn-", ""),
        );
        if (customColumn && !customColumn.builtIn) {
          return {
            id: fieldId,
            layout,
            component: (
              <div className="flex flex-col font-inter text-xs">
                <SpanCollapsibleSection
                  Icon={CurlyBraces}
                  title={customColumn.name}
                  id={`custom-field-${customColumn.name}`}
                  localStorageKey={`${localStorageKeyPrefix}-traceCustom-${customColumn.name}`}
                  onChange={resetForcedSpanCollapse}
                  forcedState={collapseSpanState}
                  className="border-t border-transparent"
                  expandedClassName="border-t border-primary-100 rounded-t-none"
                  draggableId={fieldId}
                  layout={layout}
                  onToggleLayout={onToggleLayout}
                >
                  <DataDisplay
                    comparisonClassName={comparisonClassName}
                    searchQuery={searchQuery}
                    span={span}
                    fieldName={customColumn.name}
                    foldStateId={`${projectId}-${objectType}-${customColumn.name}`}
                    diffMessage={diffMessage}
                    onApplySearch={async (query, replace, opts) => {
                      const q = (query ?? "").replace(/^\$/, customColumn.name);
                      toast.promise(
                        onApplySearch(q, replace, {
                          ...opts,
                          label: q,
                        }),
                        {
                          loading: "Applying filter",
                          success: "Filter applied",
                          error: "Failed to apply filter",
                        },
                      );
                    }}
                    updateRow={undefined}
                    experimentNames={experimentNames}
                    localStorageKey={`${localStorageKeyPrefix}-traceCustom-${customColumn.name}`}
                    copilotContext={copilotContext}
                  />
                </SpanCollapsibleSection>
              </div>
            ),
          };
        }
      }

      // Span iframes
      if (
        typeof fieldId === "string" &&
        fieldId.startsWith("spanIframe-") &&
        parsedSpanData !== undefined
      ) {
        const spanIframe = span_iframes.find(
          (iframe) => iframe.id === fieldId.replace("spanIframe-", ""),
        );
        if (spanIframe) {
          return {
            id: fieldId,
            layout,
            component: (
              <SpanIframeSection
                span={span}
                spanIframe={spanIframe}
                localStorageKeyPrefix={localStorageKeyPrefix}
                resetForcedSpanCollapse={resetForcedSpanCollapse}
                collapseSpanState={collapseSpanState}
                parsedSpanData={parsedSpanData}
                updateRow={updateRow}
                onToggleLayout={onToggleLayout}
                fieldId={fieldId}
                layout={layout}
                setExpandedFrame={setExpandedFrame}
              />
            ),
          };
        }
      }

      return null;
    })
    .filter((x) => x !== null);

  // The assumption here is that if they haven't set _both_ start and end, then
  // they are probably not setting either in their code.
  const missingStartEndError = useMemo(() => {
    return objectType === "project_logs" &&
      projectConfig.scores.some((s) => isOnlineScore(s.score_type)) &&
      !span.data.metrics?.start &&
      !span.data.metrics?.end
      ? "This span is missing start and end metrics, which are required to trigger online scoring. Please add them to your logging code to ensure that online scoring works."
      : null;
  }, [objectType, projectConfig.scores, span.data.metrics]);

  return (
    <Suspense fallback={<Loading />}>
      <div className="group/draggable-span-fields flex flex-col gap-3 @3xl:grid @3xl:grid-cols-2 @3xl:gap-5">
        {missingStartEndError && (
          <ErrorBanner className="mt-0">{missingStartEndError}</ErrorBanner>
        )}
        <StyledDndContext onDragEnd={onDragEnd}>
          <SortableContext items={reorderableSpanFields.map(({ id }) => id)}>
            {reorderableSpanFields.map(({ id, component, layout }) => (
              <DraggableSpanField key={id} id={id} layout={layout}>
                {component}
              </DraggableSpanField>
            ))}
          </SortableContext>
        </StyledDndContext>
      </div>
      {activitySection}
    </Suspense>
  );
};

const DraggableSpanField = ({
  id,
  children,
  layout,
}: React.PropsWithChildren<{
  id: string;
  layout?: "full" | "two_column" | null;
}>) => {
  const { attributes, setNodeRef, transform, transition, isDragging } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString({
      x: transform?.x ?? 0,
      y: transform?.y ?? 0,
      scaleX: 1,
      scaleY: 1,
    }),
    transition,
  };
  return (
    <div
      ref={setNodeRef}
      {...attributes}
      style={style}
      className={cn(
        "cursor-auto",
        {
          "z-50 rounded-md border-8 border-background bg-background pt-2 ring-2 ring-accent-500":
            isDragging,
        },
        { "col-span-2": layout !== "two_column" },
      )}
    >
      {children}
    </div>
  );
};

export const SpanCollapsibleSection = ({
  children,
  actionRight,
  draggableId,
  onToggleLayout,
  layout,
  ...rest
}: CollapsibleSectionProps) => {
  const { listeners } = useSortable({
    id: draggableId ?? "",
  });
  return (
    <CollapsibleSection
      {...rest}
      className="mb-2 border-t-2 border-transparent"
      expandedClassName="border-t-2 border-primary-100 rounded-t-none"
      actionRight={
        <>
          {actionRight}
          {draggableId && (
            <>
              {onToggleLayout && (
                <BasicTooltip
                  tooltipContent={
                    layout === "full"
                      ? "Switch to two column layout"
                      : "Switch to full column layout"
                  }
                >
                  <Button
                    variant="default"
                    size="xs"
                    className="hidden text-primary-400 @3xl:group-hover/draggable-span-fields:flex"
                    Icon={
                      !layout || layout === "two_column"
                        ? MoveHorizontal
                        : Columns2
                    }
                    onClick={() => onToggleLayout(draggableId)}
                  />
                </BasicTooltip>
              )}
              <Button
                variant="default"
                size="xs"
                className="hidden cursor-grab text-primary-400 group-hover/draggable-span-fields:flex"
                {...listeners}
              >
                <GripVertical className="size-3" />
              </Button>
            </>
          )}
        </>
      }
    >
      {children}
    </CollapsibleSection>
  );
};

const UseOutputValueButton = ({
  span,
  updateRow,
}: {
  span: Span;
  updateRow?: UpdateRowFn;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  return (
    <Button
      variant="ghost"
      className="text-primary-500"
      size="xs"
      isLoading={isLoading}
      onClick={async () => {
        setIsLoading(true);
        const outputValue = getDiffRight(span.data.output);
        await updateRow?.(
          span.data,
          ["expected"],
          typeof outputValue === "string"
            ? deserializePlainStringAsJSON(outputValue).value
            : null,
        );
        setIsLoading(false);
      }}
    >
      Set to output value
    </Button>
  );
};

const SpanIframeSection = ({
  span,
  spanIframe,
  localStorageKeyPrefix,
  resetForcedSpanCollapse,
  collapseSpanState,
  parsedSpanData,
  updateRow,
  onToggleLayout,
  fieldId,
  layout,
  setExpandedFrame,
}: {
  span: Span;
  spanIframe: SpanIFrame;
  localStorageKeyPrefix: string;
  resetForcedSpanCollapse?: VoidFunction;
  collapseSpanState?: "collapsed" | "expanded" | "mixed";
  parsedSpanData: Record<string, unknown>;
  updateRow?: UpdateRowFn;
  onToggleLayout: (id: string) => void;
  fieldId: string;
  layout?: "full" | "two_column" | null;
  setExpandedFrame: (id: string) => void;
}) => {
  const ref = useRef<HTMLIFrameElement>(null);
  return (
    <div>
      <SpanCollapsibleSection
        title={spanIframe.name}
        id={spanIframe.id}
        Icon={AppWindow}
        localStorageKey={`${localStorageKeyPrefix}-${spanIframe.id}`}
        onChange={resetForcedSpanCollapse}
        forcedState={collapseSpanState}
        actionRight={
          <>
            <RefreshIframeButton
              spanIframe={spanIframe}
              expandedFrameRef={ref}
              parsedSpanData={parsedSpanData}
              variant="border"
            />
            <Button
              size="xs"
              variant="default"
              className="text-primary-500"
              Icon={Maximize2}
              onClick={() => setExpandedFrame(spanIframe.id)}
            />
          </>
        }
        draggableId={fieldId}
        layout={layout}
        onToggleLayout={onToggleLayout}
      >
        <IFrameViewer
          key={span.id}
          iframeRef={ref}
          value={parsedSpanData}
          urlTemplate={spanIframe.url}
          postMessage={spanIframe.post_message ?? false}
          onUpdate={(field, data) => {
            updateRow?.(span.data, field.split("."), data);
          }}
        />
      </SpanCollapsibleSection>
    </div>
  );
};
