"use client";

import { useMemo } from "react";
import {
  type Span,
  spanMetricNames,
  type SpanMetrics,
  type SystemMetrics,
} from "./graph";
import {
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  isDiffObject,
} from "#/utils/diffs/diff-objects";

function buildSystemMetrics(
  metrics: SpanMetrics | undefined,
  startTime?: number,
): SystemMetrics {
  return {
    offset: metrics?.start && startTime ? metrics.start - startTime : undefined,
    duration:
      metrics?.start && metrics?.end ? metrics.end - metrics.start : undefined,
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    ...(Object.fromEntries(
      spanMetricNames.map((name) => [name, metrics?.[name]]),
    ) as SpanMetrics),
  };
}

export const useSystemMetrics = ({
  selectedSpan,
  rootStartTime,
}: {
  selectedSpan: Span | null;
  rootStartTime?: number;
}) => {
  const systemMetrics:
    | SystemMetrics
    | DiffObjectType<SystemMetrics>
    | undefined = useMemo(
    () =>
      selectedSpan?.data.metrics &&
      (isDiffObject<SpanMetrics>(selectedSpan.data.metrics)
        ? ({
            [DiffLeftField]: selectedSpan.data.metrics[DiffLeftField]
              ? buildSystemMetrics(
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  selectedSpan.data.metrics[DiffLeftField] as SpanMetrics,
                  rootStartTime,
                )
              : undefined,
            [DiffRightField]: buildSystemMetrics(
              // this can be undefined if the comparison experiment doesn't have a matching span
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              selectedSpan.data.metrics[DiffRightField] as SpanMetrics,
              rootStartTime,
            ),
          } as const)
        : buildSystemMetrics(selectedSpan.data.metrics, rootStartTime)),
    [selectedSpan?.data.metrics, rootStartTime],
  );

  return systemMetrics;
};
