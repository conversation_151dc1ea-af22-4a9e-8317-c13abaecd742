import { type spanAttributesSchema } from "@braintrust/local";
import { type z } from "zod";

export interface NormalizedData {
  input: unknown;
  output: unknown;
  toolDefinitions: Map<string, string>;
  metadata?: string;
}

export interface FormatNormalizer {
  name: string;
  detect(
    input: unknown,
    output: unknown,
    metadata?: string,
    spanAttributes?: z.infer<typeof spanAttributesSchema>,
  ): boolean;
  normalize(
    input: unknown,
    output: unknown,
    metadata?: string,
    spanAttributes?: z.infer<typeof spanAttributesSchema>,
  ): NormalizedData;
}

export interface NormalizeOptions {
  cacheKey?: string;
}

const MAX_CACHE_SIZE = 1000;
const normalizationCache = new Map<string, NormalizedData>();

/**
 * Normalizes various input/output formats to formats that parseLLMSpanPart can handle.
 * Tries each registered normalizer until one succeeds, falling back to pass-through.
 */
export function normalizeToKnownFormat(
  input: unknown,
  output: unknown,
  metadata?: string,
  spanAttributes?: z.infer<typeof spanAttributesSchema>,
  normalizers: FormatNormalizer[] = [],
  options?: NormalizeOptions,
): NormalizedData {
  if (options?.cacheKey) {
    const cached = normalizationCache.get(options.cacheKey);
    if (cached) {
      return cached;
    }
  }

  let result: NormalizedData | undefined;

  for (const normalizer of normalizers) {
    try {
      if (normalizer.detect(input, output, metadata, spanAttributes)) {
        result = normalizer.normalize(input, output, metadata, spanAttributes);
        break;
      }
    } catch (error) {
      console.warn(
        `Normalizer ${normalizer.name} thought it could process a message but failed: ${error}`,
      );
      continue;
    }
  }

  if (!result) {
    result = {
      input,
      output,
      toolDefinitions: new Map<string, string>(),
      metadata,
    };
  }

  if (options?.cacheKey) {
    // Evict oldest if at capacity
    if (normalizationCache.size >= MAX_CACHE_SIZE) {
      // Evict oldest 10% of cache entries
      const numToEvict = Math.ceil(MAX_CACHE_SIZE * 0.1);
      const keysToEvict = Array.from(normalizationCache.keys()).slice(
        0,
        numToEvict,
      );
      for (const key of keysToEvict) {
        normalizationCache.delete(key);
      }
    }
    normalizationCache.set(options.cacheKey, result);
  }

  return result;
}
