import {
  type DiffModeState,
  useFullscreenTraceState,
} from "#/ui/query-parameters";
import { BasicTooltip } from "#/ui/tooltip";
import { Button } from "#/ui/button";
import { Columns } from "lucide-react";

export const FullscreenTraceAction = ({
  diffModeState,
}: {
  diffModeState: DiffModeState;
}) => {
  const [isFullscreen, setFullscreen] = useFullscreenTraceState();

  if (
    !diffModeState?.enabled ||
    diffModeState.enabledValue !== "between_experiments" ||
    isFullscreen
  ) {
    return null;
  }

  return (
    <BasicTooltip tooltipContent="Fullscreen trace to compare side-by-side">
      <Button
        size="xs"
        variant="ghost"
        className="text-primary-400"
        Icon={Columns}
        onClick={() => setFullscreen("1")}
      />
    </BasicTooltip>
  );
};
