import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { cn } from "#/utils/classnames";
import { type TransactionId, TransactionIdField } from "#/utils/duckdb";
import { type DeleteCommentFn } from "#/utils/mutable-object";
import { isEmpty, normalizeArrowForJSON } from "#/utils/object";
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { CommentErrorBoundary } from "#/ui/comment-error-boundary";
import { Avatar, AvatarGroupWithTooltip, type AvatarSize } from "#/ui/avatar";
import { Button } from "#/ui/button";
import { prettifyXact } from "braintrust/util";
import { ArrowRight, EllipsisVertical, Server } from "lucide-react";
import {
  type AuditData,
  type AuditLogRow,
  type CommentData,
} from "@braintrust/local/api-schema";
import { DateWithTooltip } from "#/ui/date";
import {
  type OrgUsersContextT,
  OrgUsersContext,
} from "#/utils/org-users-context";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { BlueLink } from "#/ui/link";
import { GoToOriginContext } from "./go-to-origin-context";
import TextEditor, { type TextEditorHandle } from "#/ui/text-editor";
import { useActiveCommentId } from "#/ui/query-parameters";
import { toast } from "sonner";
import { STARRED_TAG } from "#/ui/table/star-cell";
import { imageUrlSplitPattern } from "#/ui/image-utils";
import { MarkdownViewer } from "#/ui/markdown";
import { useOrg, useUser } from "#/utils/user";
import { mentionsSupport } from "#/utils/codemirror/mentions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  isDatasetPage,
  isExperimentPage,
  isPlaygroundPage,
  useActivePage,
} from "#/app/app/[org]/pathname-checker";
import { usePathname } from "next/navigation";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { type sendCommentMention } from "#/utils/mentions";
import {
  mentionRegexCaptureGlobal,
  parseMentions,
} from "#/utils/email/mentions-utils";
import { useEnvironmentObjectAssociations } from "#/utils/environments/use-environment-associations";
import { useEnvironments } from "#/utils/environments/use-environments";
import { useAtomValue } from "jotai/react";
import {
  environmentsAtom,
  useEnvironmentSlugsForVersionAtom,
} from "#/utils/environments/atoms";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { EnvironmentMenu } from "#/ui/environment-menu";
import { ErrorBoundary } from "#/utils/error-boundary";
import { ErrorBanner } from "#/ui/error-banner";
import {
  BT_ASSIGNMENTS_META_FIELD,
  BT_ASSIGNMENTS_META_FIELD_DISPLAY_NAME,
} from "#/utils/assign";
import { useInviteNewMemberModal } from "#/ui/members/useInviteNewMemberModal";

type AddCommentFn = (
  comment: CommentData,
) => Promise<{ transactionId: TransactionId | null; commentId?: string }>;
export type SelectVersionFn = (version: TransactionId) => void;
type OrgUsers = OrgUsersContextT["orgUsers"];

export function Activity({
  auditLog,
  objectName,
  rootObjectName,
  addComment,
  deleteComment,
  onSelectVersion,
  selectedVersion,
  className,
  entityName,
  functionType,
  objectId,
}: {
  auditLog: AuditLogRow[];
  objectName: string;
  rootObjectName?: string;
  addComment?: AddCommentFn;
  deleteComment?: DeleteCommentFn;
  onSelectVersion?: SelectVersionFn;
  selectedVersion?: TransactionId | null;
  className?: string;
  entityName?: string | null;
  functionType?: string;
  objectId?: string;
}) {
  const { orgUsers } = useContext(OrgUsersContext);
  const auditEntries = useMemo(() => parseAuditTree(auditLog), [auditLog]);
  const lastUpsert = useMemo(() => {
    for (let i = auditEntries.length - 1; i >= 0; i--) {
      const entry = auditEntries[i];
      if (entry.row.audit_data?.action === "upsert") {
        return entry;
      }
    }
    return undefined;
  }, [auditEntries]);

  const isPrompt = functionType === "prompt";
  const hasValidObjectId = !!(objectId && isPrompt);
  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");
  const showEnvironmentMenu = isEnvironmentsEnabled && hasValidObjectId;

  // Only fetch environment data for prompts with valid objectId when environments are enabled
  useEnvironmentObjectAssociations({
    objectType: showEnvironmentMenu ? "prompt" : "",
    objectId: showEnvironmentMenu ? objectId : "",
  });

  useEnvironments();

  return (
    <div className={cn("flex flex-col pb-5 text-xs", className)}>
      {addComment && (
        <CommentBox addComment={addComment} entityName={entityName} />
      )}
      <div className="relative flex w-full flex-col">
        {auditEntries.map((entry, i) => (
          <ActivityRenderer
            isLast={i == auditEntries.length - 1}
            activity={entry}
            addComment={addComment}
            deleteComment={deleteComment}
            key={entry.id}
            users={orgUsers}
            objectName={objectName}
            rootObjectName={rootObjectName}
            isCreate={lastUpsert?.id === entry.id}
            onSelectVersion={onSelectVersion}
            isSelected={entry.row[TransactionIdField] === selectedVersion}
            entityName={entityName}
            showEnvironmentMenu={showEnvironmentMenu}
            objectId={objectId}
          />
        ))}
      </div>
    </div>
  );
}

const ActivityRenderer = ({
  activity,
  isLast,
  users,
  deleteComment,
  addComment,
  objectName,
  rootObjectName,
  isCreate,
  onSelectVersion,
  isSelected,
  entityName,
  showEnvironmentMenu,
  objectId,
}: {
  activity: AuditTree;
  isLast: boolean;
  addComment: AddCommentFn | undefined;
  deleteComment: DeleteCommentFn | undefined;
  users: OrgUsers;
  objectName: string;
  rootObjectName?: string;
  onSelectVersion?: SelectVersionFn;
  isSelected?: boolean;
  entityName?: string | null;
  // for audit data
  isCreate: boolean;
  showEnvironmentMenu?: boolean;
  objectId?: string;
}) => {
  if (activity.row.comment) {
    return (
      <ActivityWrapper isLast={isLast} isComment>
        <RootComment
          entry={activity}
          users={users}
          deleteComment={deleteComment}
          addComment={addComment}
          entityName={entityName}
        />
      </ActivityWrapper>
    );
  } else if (activity.row.audit_data) {
    return (
      <ActivityWrapper isLast={isLast} isSelected={isSelected}>
        <AuditDataEntry
          entry={activity}
          users={users}
          isCreate={isCreate}
          objectName={objectName}
          rootObjectName={rootObjectName}
          onSelectVersion={onSelectVersion}
          showEnvironmentMenu={showEnvironmentMenu}
          objectId={objectId}
        />
      </ActivityWrapper>
    );
  }
};

const ActivityWrapper = ({
  children,
  isLast,
  isComment,
  isSelected,
}: {
  children: React.ReactNode;
  isLast: boolean;
  isComment?: boolean;
  isSelected?: boolean;
}) => {
  const marking = isSelected ? (
    <div className="relative size-[12px] rounded-full border border-accent-600 bg-background">
      <div className="absolute top-1/2 left-1/2 size-[4px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-accent-600"></div>
    </div>
  ) : (
    <div className="size-[7px] rounded-full bg-primary-200"></div>
  );

  return (
    <div className="relative flex self-stretch">
      <div
        className={cn(
          "absolute top-0 left-0 ml-3 border-l",
          isLast ? "h-4" : "h-full",
        )}
      ></div>
      {isComment ? (
        <div className="relative mt-4 flex w-full flex-1 items-start">
          {children}
        </div>
      ) : (
        <div className="relative mt-4 flex w-full flex-1 items-start">
          <div className="flex min-h-[16px] min-w-[26px] items-center justify-center">
            {marking}
          </div>
          <div className="relative flex flex-1 overflow-hidden hyphens-auto whitespace-pre-wrap">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

interface AuditTree {
  id: string;
  created: Date;
  user_id: string | undefined;
  row: AuditLogRow;
  children: AuditTree[];
}

function parseAuditTree(auditLog: AuditLogRow[]): AuditTree[] {
  const entry = (r: AuditLogRow) =>
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    ({
      id: r.id,
      created: new Date(r.created),
      user_id: r.metadata?.user_id,
      row: r,
      children: [],
    }) as AuditTree;

  // Sort by created DESC, TransactionIdField DESC, action=upsert ASC
  const sortKey = (a: AuditTree, b: AuditTree) =>
    Number(b.created) - Number(a.created) ||
    Number(
      BigInt(b.row[TransactionIdField]) - BigInt(a.row[TransactionIdField]),
    ) ||
    Number(b.row.audit_data?.action === "upsert" ? 0 : 1) -
      Number(a.row.audit_data?.action === "upsert" ? 0 : 1);
  const parentEntries = Object.fromEntries(
    auditLog.filter((r) => !r.comment?.parent_id).map((r) => [r.id, entry(r)]),
  );

  for (const r of auditLog) {
    if (r.comment?.parent_id) {
      const parent = parentEntries[r.comment.parent_id];
      if (parent) {
        parent.children.push(entry(r));
      } else {
        console.warn(
          `Comment ${r.id} has parent ${r.comment.parent_id} which does not exist`,
        );
      }
    }
  }

  for (const entry of Object.values(parentEntries)) {
    entry.children.sort(sortKey);
  }

  const tree = Object.values(parentEntries);
  tree.sort(sortKey);

  for (const entry of tree) {
    for (const child of entry.children) {
      if (child.children.length > 0) {
        console.error(
          `Comment ${child.id} has children but is not a root comment. These comments will be ignored.`,
        );
      }
    }
  }

  return tree;
}

function fieldName(path: string[]): string {
  return path[path.length - 1];
}

function inlineJSONSerialize(v: unknown) {
  return JSON.stringify(v, normalizeArrowForJSON);
}

const bolder = (s: string) => <span className="font-medium">{s}</span>;

function AuditDataEntry({
  entry,
  users,
  isCreate,
  objectName,
  rootObjectName,
  onSelectVersion,
  showEnvironmentMenu,
  objectId,
}: {
  entry: AuditTree;
  users: OrgUsers;
  isCreate: boolean;
  objectName: string;
  rootObjectName?: string;
  onSelectVersion?: SelectVersionFn;
  showEnvironmentMenu?: boolean;
  objectId?: string;
}) {
  const versionId = entry.row[TransactionIdField];

  // Get environment data from atoms
  const environments = useAtomValue(environmentsAtom);
  const environmentSlugsAtom = useEnvironmentSlugsForVersionAtom(
    "prompt",
    objectId || "",
    versionId,
  );
  const environmentSlugs = useAtomValue(environmentSlugsAtom);

  if (!entry.row.audit_data) {
    console.assert(false, "AuditEntry called with non-audit entry");
    return null;
  }

  // Find all environments for this version (only for version-creating actions like upsert)
  const isVersionCreatingAction = entry.row.audit_data?.action === "upsert";
  const versionEnvironments = isVersionCreatingAction
    ? environmentSlugs
        .map((slug) => environments.find((env) => env.slug === slug))
        .filter((env): env is NonNullable<typeof env> => !!env)
    : [];

  return (
    <div className="relative flex w-full flex-1 flex-wrap items-center overflow-hidden">
      {onSelectVersion && (
        <div className="-mt-1 flex h-6 w-full gap-2">
          <button
            className="block text-left font-mono text-accent-700"
            onClick={() => onSelectVersion(entry.row[TransactionIdField])}
          >
            {prettifyXact(entry.row[TransactionIdField])}
          </button>
          {showEnvironmentMenu && environments && !!objectId && (
            <EnvironmentMenu
              versionId={versionId}
              objectId={objectId}
              objectType="prompt"
              versionEnvironments={versionEnvironments}
            />
          )}
        </div>
      )}
      <AuditName entry={entry} users={users} />
      <div className="w-full">
        <span className="hyphens-auto whitespace-pre-wrap text-primary-500">
          <AuditDataEntryLabel
            auditData={entry.row.audit_data}
            isCreate={isCreate}
            objectName={objectName}
            rootObjectName={rootObjectName}
          />{" "}
          {bolder("·")}{" "}
          <span className="inline-block text-nowrap">
            <DateWithTooltip dateMs={entry.created.getTime()} />
          </span>
        </span>
      </div>
    </div>
  );
}

const mapIdsToAvatarGroup = (ids: unknown, orgUsers: OrgUsers) => {
  return Array.isArray(ids)
    ? ids.reduce<
        {
          userId: string;
          name: string;
          imgUrl: string | null | undefined;
          email?: string | null;
          size: AvatarSize;
        }[]
      >((acc, userId) => {
        const orgUser = orgUsers[userId];
        if (orgUser) {
          const userName =
            orgUser.given_name && orgUser.family_name
              ? `${orgUser.given_name} ${orgUser.family_name}`
              : undefined;
          acc.push({
            userId,
            name: userName || orgUser?.email || orgUser?.id,
            email: userName ? orgUser?.email : undefined,
            imgUrl: orgUser?.avatar_url,
            size: "xs",
          });
        }
        return acc;
      }, [])
    : [];
};

function AuditDataEntryLabel({
  auditData,
  isCreate,
  objectName,
  rootObjectName,
}: {
  auditData: AuditData;
  isCreate: boolean;
  objectName: string;
  rootObjectName?: string;
}) {
  const goToOriginParams = useContext(GoToOriginContext);
  const { orgUsers } = useContext(OrgUsersContext);

  if (isCreate) {
    return goToOriginParams ? (
      <span>
        copied the {rootObjectName ?? objectName} from{" "}
        <BlueLink
          href={goToOriginParams.link}
          onClick={(e) => {
            e.preventDefault();
            goToOriginParams.callback();
          }}
        >
          {goToOriginParams.objectTypeName ?? "object"}
        </BlueLink>
      </span>
    ) : (
      `created the ${objectName}`
    );
  } else if (auditData.action === "merge") {
    const { path, from, to } = auditData;
    if (isEmpty(path) || path.length === 0) {
      return `updated the ${objectName}`;
    }

    const pathField = fieldName(path);
    if (pathField === "tags") {
      if (
        (isEmpty(from) || !from.includes(STARRED_TAG)) &&
        !isEmpty(to) &&
        to.includes(STARRED_TAG)
      ) {
        return `starred the ${rootObjectName ?? objectName}`;
      } else if (
        !isEmpty(from) &&
        from.includes(STARRED_TAG) &&
        (isEmpty(to) || !to.includes(STARRED_TAG))
      ) {
        return `unstarred the ${rootObjectName ?? objectName}`;
      }
    }
    const isAssignment = pathField === BT_ASSIGNMENTS_META_FIELD;
    const isMetadata = pathField == "metadata";

    const pathDisplay = (v: unknown) => {
      // If this is the assignment field, treat the value as an array of user ids
      if (isAssignment) {
        const users = mapIdsToAvatarGroup(v, orgUsers);
        return users.length ? <AvatarGroupWithTooltip users={users} /> : null;
      }

      // If this is metadata, check for the special BT_ASSIGNMENTS_META_FIELD key
      if (isMetadata) {
        if (v && typeof v === "object") {
          let maybeAssignments: unknown = undefined;
          if (
            typeof v === "object" &&
            v !== null &&
            BT_ASSIGNMENTS_META_FIELD in v
          ) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            maybeAssignments = (v as Record<string, unknown>)[
              BT_ASSIGNMENTS_META_FIELD
            ];
          }
          if (Array.isArray(maybeAssignments) && maybeAssignments.length > 0) {
            const users = mapIdsToAvatarGroup(maybeAssignments, orgUsers);
            return users.length ? (
              <AvatarGroupWithTooltip users={users} />
            ) : null;
          }
        }
      }

      return isEmpty(v) ? null : bolder(inlineJSONSerialize(v));
    };

    const pathElem = bolder(
      isAssignment ? BT_ASSIGNMENTS_META_FIELD_DISPLAY_NAME : pathField,
    );
    const fromElem = pathDisplay(from);
    const toElem = pathDisplay(to);
    if (fromElem && toElem) {
      return (
        <span className="inline-flex items-center">
          updated {pathElem} from {fromElem} to {toElem}
        </span>
      );
    } else if (toElem) {
      return (
        <span className="inline-flex items-center">
          set {pathElem} to {toElem}
        </span>
      );
    } else {
      return <span>updated {pathElem}</span>;
    }
  } else if (auditData.action === "delete") {
    return `deleted the ${objectName}`;
  } else {
    return `updated the ${objectName}`;
  }
}

function RootComment({
  entry,
  addComment,
  deleteComment,
  users,
  entityName,
}: {
  entry: AuditTree;
  addComment: AddCommentFn | undefined;
  deleteComment: DeleteCommentFn | undefined;
  users: OrgUsers;
  entityName?: string | null;
}) {
  const [showReply, setShowReply] = useState<boolean>(false);
  const [activeCommentId] = useActiveCommentId();

  useEffect(() => {
    if (activeCommentId) {
      const target = document.getElementById(activeCommentId);
      const targetArrow = document.getElementById(`arrow-${activeCommentId}`);
      const highlightClasses = ["bg-accent-50!", "shadow-lg"];
      if (target) {
        target.classList.add(...highlightClasses);
        targetArrow?.classList.add(...highlightClasses);
        setTimeout(() => {
          target.classList.remove(...highlightClasses);
          targetArrow?.classList.remove(...highlightClasses);
        }, 4000);
        target.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [activeCommentId]);

  if (!entry.row.comment) {
    console.assert(false, "RootComment called with non-comment entry");
    return null;
  }

  return (
    <div
      className={cn(
        "flex min-h-[4lh] flex-1 rounded-md border bg-primary-50 transition",
        {
          "cursor-pointer hover:border-primary-300": !!addComment,
        },
      )}
      onClick={(e) => {
        // Don't toggle if clicking inside the reply box or if text editor is focused
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        const target = e.target as HTMLElement;
        const isInsideReplyBox = target.closest(".reply-box");
        const isTextEditorFocused =
          document.activeElement?.closest(".cm-editor") ||
          document.activeElement?.tagName === "INPUT" ||
          document.activeElement?.tagName === "TEXTAREA";

        if (!isInsideReplyBox && !isTextEditorFocused) {
          setShowReply((s) => !s);
        }
      }}
    >
      <AuditAvatar
        entry={entry}
        users={users}
        size="md"
        className="size-[18px] flex-none translate-x-1 translate-y-2"
      />
      <div className="flex flex-1 flex-col" id={entry.id}>
        <div
          className={cn(
            "group/comment flex flex-1 flex-col transition ease-in-out first:rounded-t-md only:rounded-md",
          )}
        >
          <div className="relative px-2.5 pt-2.5 text-xs">
            <span className="font-medium">
              {getActorNameForActivity(entry.user_id, entry.row.source, users)}
            </span>
            <span className="text-xs text-primary-500">
              {" "}
              commented {bolder("·")}{" "}
              <DateWithTooltip dateMs={entry.created.getTime()} />
            </span>
          </div>
          <div className="px-2 pt-2.5 pb-3.5">
            {entry.row.comment.text
              .split(imageUrlSplitPattern)
              .map((part, i) => {
                if (i % 2 === 1) {
                  // URL parts
                  return (
                    <img
                      key={i}
                      src={part}
                      alt="Comment attachment"
                      className="max-w-full rounded-sm"
                    />
                  );
                }
                // Text parts
                return (
                  <CommentErrorBoundary key={i}>
                    <MarkdownViewer
                      className="prose-sm py-0 break-all whitespace-normal empty:hidden"
                      value={part}
                    />
                  </CommentErrorBoundary>
                );
              })}
          </div>
          {deleteComment && (
            <CommentMenu
              deleteComment={() => deleteComment(entry.row)}
              commentId={entry.id}
            />
          )}
        </div>
        {entry.children.map(
          (entry) =>
            entry.row.comment && (
              <div
                className="group/comment relative mt-2 flex flex-row border-t border-primary-100 p-2 pl-0 ease-in-out"
                id={entry.id}
                key={entry.id}
              >
                <div className="flex">
                  <AuditAvatar
                    entry={entry}
                    className="translate-y-0.5"
                    users={users}
                    size="xs"
                  />
                </div>
                <div className="flex flex-1 flex-col pl-1.5">
                  <div className="mt-0.5">
                    <span className="font-medium">
                      {getActorNameForActivity(
                        entry.user_id,
                        entry.row.source,
                        users,
                      )}
                    </span>
                    <span className="text-xs text-primary-500">
                      {" "}
                      commented{" "}
                      <DateWithTooltip dateMs={entry.created.getTime()} />
                    </span>
                  </div>
                  <div className="flex flex-1 flex-col">
                    <CommentErrorBoundary>
                      <MarkdownViewer
                        className="prose-sm break-all whitespace-normal"
                        value={entry.row.comment.text}
                      />
                    </CommentErrorBoundary>
                  </div>
                  {deleteComment && (
                    <div>
                      <CommentMenu
                        deleteComment={() => deleteComment(entry.row)}
                        commentId={entry.id}
                      />
                    </div>
                  )}
                </div>
              </div>
            ),
        )}
        {addComment && showReply && (
          // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
          <div className="reply-box p-1 pr-3">
            <CommentBox
              addComment={addComment}
              parentId={entry.id}
              setShowReply={setShowReply}
              entityName={entityName}
            />
          </div>
        )}
      </div>
    </div>
  );
}

function AuditAvatar({
  entry,
  users,
  size,
  className,
}: {
  entry: AuditTree;
  users: OrgUsers;
  size?: "xs" | "sm" | "md" | "lg";
  className?: string;
}) {
  const user = entry.user_id && users[entry.user_id];
  if (user && user.user_type === "service_account") {
    return <Server className={cn("size-4", className)} />;
  }
  return (
    <Avatar
      imgUrl={
        entry.row.source === "app" && entry.user_id
          ? users[entry.user_id]?.avatar_url
          : undefined
      }
      size={size}
      className={className}
    />
  );
}

export const getActorNameForActivity = (
  userId: string | undefined,
  source: string,
  users: OrgUsers,
) => {
  const name =
    source === "app"
      ? ((userId && users[userId]
          ? ((users[userId].given_name &&
              `${users[userId].given_name} ${users[userId].family_name || ""}`) ??
            users[userId].email)
          : undefined) ?? `Braintrust user${userId ? ` (${userId})` : ""}`)
      : source === "api"
        ? "API user"
        : source === "external"
          ? `External user${userId ? ` (${userId})` : ""}`
          : `${source} user${userId ? ` (${userId})` : ""}`;
  return name;
};

function AuditName({ entry, users }: { entry: AuditTree; users: OrgUsers }) {
  return (
    <span className="mr-1 font-medium">
      {getActorNameForActivity(entry.user_id, entry.row.source, users)}
    </span>
  );
}

function CommentBox({
  addComment,
  parentId,
  className,
  setShowReply,
  entityName,
}: {
  addComment: AddCommentFn;
  parentId?: string;
  className?: string;
  setShowReply?: (show: boolean) => void;
  entityName?: string | null;
}) {
  const org = useOrg();
  const { projectName } = useContext(ProjectContext);
  const pathname = usePathname();
  const activePage = useActivePage();
  const { getToken } = useAuth();
  const pageType = useMemo(() => {
    if (!pathname) {
      return;
    }

    if (isExperimentPage(pathname)) {
      return "experiment";
    }
    if (isDatasetPage(pathname)) {
      return "dataset";
    }
    if (isPlaygroundPage(pathname)) {
      return "playground";
    }

    return activePage;
  }, [pathname, activePage]);

  const commentRef = useRef<TextEditorHandle<string>>(null);
  const [value, setValue] = useState("");
  const { orgUsers } = useContext(OrgUsersContext);
  const { user } = useUser();

  const isEmpty = value === "";
  const isChildComment = !!parentId;

  const submitComment = useCallback(async () => {
    if (isEmpty) {
      return;
    }

    const { commentId } = await addComment({
      text: value,
      parent_id: parentId,
      reactions: {},
    });
    setValue("");
    commentRef.current?.setValue("");

    if (user && commentId) {
      const mentionerName =
        user.given_name && user.family_name
          ? `${user.given_name} ${user.family_name}`
          : (user.email ?? "Someone");
      const emails = [...value.matchAll(mentionRegexCaptureGlobal)].reduce<
        string[]
      >((acc, m) => {
        const email = orgUsers[m[1]]?.email;
        if (email) {
          acc.push(email);
        }
        return acc;
      }, []);
      const commentText = parseMentions({
        text: value,
        formatMention: (mention) => `**${mention}**`,
        orgUsers,
      });
      const { pathname, search } = window.location;
      const searchParams = new URLSearchParams(search);
      searchParams.set("cm", commentId);
      const commentLink = `${pathname}?${searchParams.toString()}`;

      try {
        await invokeServerAction<typeof sendCommentMention>({
          fName: "comment",
          args: {
            orgId: org.id,
            orgName: org.name,
            commentText,
            mentionerName,
            emails,
            projectName,
            commentId,
            commentLink,
            entityType: pageType,
            entityName: entityName && decodeURIComponent(entityName),
          },
          getToken,
        });
      } catch (error) {
        console.error("Failed to send mention notifications:", error);
        toast.error(`Failed to send mention notifications: ${error}`);
      }
    }
  }, [
    addComment,
    entityName,
    getToken,
    isEmpty,
    org.name,
    orgUsers,
    pageType,
    parentId,
    projectName,
    user,
    value,
    org.id,
  ]);

  const [isFocused, setFocused] = useState(false);
  const { inviteNewMemberModal, showInviteNewMemberModal } =
    useInviteNewMemberModal();

  const onEscapePressedCallback = useCallback(() => {
    if (value.length === 0) {
      setShowReply?.(false);
      return true;
    }
    return false;
  }, [setShowReply, value]);

  const extensions = useMemo(() => {
    return [
      mentionsSupport(orgUsers, onEscapePressedCallback, (text: string) => {
        showInviteNewMemberModal(text);
      }),
    ];
  }, [orgUsers, onEscapePressedCallback, showInviteNewMemberModal]);

  return (
    <label className="relative w-full">
      <div
        className={cn(
          "cursor-text rounded-md border bg-primary-50 dark:bg-primary-100",
          {
            "mb-2": isChildComment,
          },
          className,
        )}
      >
        <ErrorBoundary
          fallback={
            <ErrorBanner skipErrorReporting>
              There was a problem rendering the text area. Please refresh the
              page to try again and contact support if the problem persists.
            </ErrorBanner>
          }
        >
          <TextEditor
            autoFocus={isChildComment}
            ref={commentRef}
            value={value}
            placeholder={isChildComment ? "Leave a reply…" : "Leave a comment…"}
            onChange={(newValue) => setValue(newValue)}
            onMetaEnter={() => submitComment()}
            styled
            extensions={extensions}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            className={cn("flex grow text-sm", {
              "h-14": !isChildComment,
            })}
            wrap
            isMonospace={false}
          />
        </ErrorBoundary>
      </div>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className="absolute right-0 bottom-0 mr-2 mb-2 size-6 opacity-0 transition-opacity"
            style={{ opacity: isFocused && isEmpty ? 0.6 : !isEmpty ? 100 : 0 }}
            variant="primary"
            Icon={ArrowRight}
            onClick={(e) => {
              e.preventDefault();
              submitComment();
            }}
            disabled={isEmpty}
          />
        </TooltipTrigger>
        <TooltipContent className="text-xs">Ctrl+Enter/⌘+Enter</TooltipContent>
      </Tooltip>
      {inviteNewMemberModal}
    </label>
  );
}

function CommentMenu({
  deleteComment,
  commentId,
}: {
  deleteComment: () => Promise<TransactionId | null>;
  commentId: string;
}) {
  const [open, setOpen] = useState<boolean>(false);
  return (
    <div
      className={cn(
        `absolute top-1 right-1 opacity-0 group-hover/comment:opacity-100`,
        open && "opacity-100",
      )}
    >
      <DropdownMenu onOpenChange={setOpen}>
        <DropdownMenuTrigger onClick={(e) => e.stopPropagation()} asChild>
          <Button
            Icon={EllipsisVertical}
            className="text-primary-500"
            size="xs"
            variant="ghost"
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              const { origin, pathname, search } = window.location;
              const searchParams = new URLSearchParams(search);
              searchParams.set("cm", commentId);
              navigator.clipboard.writeText(
                `${origin}${pathname}?${searchParams.toString()}`,
              );
              toast("Link copied to clipboard");
            }}
          >
            Copy link to comment
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              deleteComment();
            }}
          >
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
