import { isObject } from "braintrust/util";
import { type Span, type Trace } from "./graph";
import { type LLMMessageType, parseLLMSpanPart } from "#/ui/LLMView";
import { openAIResponseNormalizer } from "#/ui/trace/converters/openai-response-converter";
import { aiSDKResponseNormalizer } from "#/ui/trace/converters/ai-sdk-response-converter";
import { normalizeToKnownFormat } from "#/ui/trace/format-normalizer";
import { ErrorBoundary } from "#/utils/error-boundary";
import { ErrorBanner } from "#/ui/error-banner";
import { ThreadSpanMetrics } from "./thread-span-metrics";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useVirtualizer, type Virtualizer } from "@tanstack/react-virtual";
import { scrollIntoViewWithMargin } from "./scroll-into-view-with-margin";
import {
  useMemo,
  useCallback,
  useRef,
  useEffect,
  useState,
  useContext,
} from "react";
import React from "react";
import { ErrorDisplay } from "./trace-error";
import { Spinner } from "#/ui/icons/spinner";
import { type ModelCosts } from "#/ui/prompts/models";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { deserializePlainStringAsJSON } from "braintrust";
import { useInfiniteFetch } from "#/utils/virtualizer/infinite-fetch";
import { cn } from "#/utils/classnames";

import {
  ArrowRightToLine,
  BoltIcon,
  type LucideIcon,
  PanelLeft,
  Percent,
} from "lucide-react";
import { Button } from "#/ui/button";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ThreadSpanItem } from "./thread-span-item";
import { useThreadViewHashMessage } from "./thread-view-hash-message";

function noopFn() {}

export type TraceThreadViewComponentProps = {
  traces: Trace[];
  setSelectedSpan: (span: Span) => void;
  setViewType: (viewType: "trace" | "timeline" | "thread") => void;
  containerRef: React.RefObject<HTMLDivElement | null>;
  allAvailableModelCosts?: Record<string, ModelCosts>;
};

type TocItem = {
  title: string;
  spanIndex: number;
  messageIndex?: number;
  toolCallId?: string;
  icon?: LucideIcon;
  iconClassName?: string;
  description?: string;
  scoreValue?: number;
};

export const TraceThreadViewContents = ({
  traces,
  queryProps,
  threadSpans,
  setSelectedSpan,
  setViewType,
  containerRef,
  allAvailableModelCosts,
}: {
  queryProps?: {
    error: Error | null;
    isFetching: boolean;
    isPlaceholderData: boolean;
    hasNextPage: boolean;
    fetchNextPage: () => void;
  };
  threadSpans: Span[];
} & TraceThreadViewComponentProps) => {
  const { hashMessage, hashCache } = useThreadViewHashMessage();

  // Cache for processed span data - only compute when item enters viewport
  const processedSpansRef = useRef(
    new Map<
      string,
      {
        messages: LLMMessageType[];
        toolDefinitions: Map<string, string>;
        toolResponses: Map<string, unknown>;
      }
    >(),
  );

  // Build deterministic maps for deduplication across spans
  const { spanIndexMap, firstSeenToolIdIndex, firstSeenToolId } =
    useMemo(() => {
      const spanIndexMap = new Map<string, number>();
      threadSpans.forEach((s, idx) => spanIndexMap.set(s.id, idx));

      const firstSeenToolIdIndex = new Map<string, number>();
      const firstSeenToolId = new Map<string, number>();
      threadSpans.forEach((s, idx) => {
        if (s.data.span_attributes?.type !== "llm") return;
        const inputString =
          typeof s.data.input === "string" ? s.data.input : "{}";
        const { value: input } = deserializePlainStringAsJSON(inputString);
        const outputString =
          typeof s.data.output === "string" ? s.data.output : "{}";
        const { value: output } = deserializePlainStringAsJSON(outputString);

        // Use the same normalization logic as in processSpanData
        const metadataString =
          typeof s.data.metadata === "string" ? s.data.metadata : undefined;

        const spanAttributes = s.data.span_attributes;

        const normalized = normalizeToKnownFormat(
          input,
          output,
          metadataString,
          spanAttributes,
          [aiSDKResponseNormalizer, openAIResponseNormalizer],
          { cacheKey: s.id },
        );

        const allMessages: LLMMessageType[] = [];
        // Always use normalized data for consistency
        const parsedMessages =
          !!normalized.input && parseLLMSpanPart(normalized.input);
        if (parsedMessages) {
          allMessages.push(...parsedMessages);
        }

        const parsedOutput =
          !!normalized.output && parseLLMSpanPart(normalized.output);
        if (parsedOutput) {
          allMessages.push(...parsedOutput);
        }
        for (const m of allMessages) {
          if (!m || m.role === "tool") continue;
          const h = hashMessage(m);
          if (!firstSeenToolIdIndex.has(h)) {
            firstSeenToolIdIndex.set(h, idx);
          }
          if (
            m.role === "assistant" &&
            "tool_calls" in m &&
            Array.isArray(m.tool_calls)
          ) {
            for (const tc of m.tool_calls) {
              if (tc.id && !firstSeenToolId.has(tc.id)) {
                firstSeenToolId.set(tc.id, idx);
              }
            }
          }
        }
      });

      return { spanIndexMap, firstSeenToolIdIndex, firstSeenToolId };
    }, [threadSpans, hashMessage]);

  const onClickSpan = useCallback(
    (span: Span) => {
      setSelectedSpan(span);
      setViewType("trace");
    },
    [setSelectedSpan, setViewType],
  );

  const { projectId } = useContext(ProjectContext);
  const [collapsed, setCollapsed] = useEntityStorage({
    entityType: "traceTree",
    entityIdentifier: projectId ?? "",
    key: "isCollapsed",
  });

  const { isFetching, hasNextPage, error, fetchNextPage } = queryProps ?? {
    isFetching: false,
    hasNextPage: false,
    error: null,
    fetchNextPage: noopFn,
  };

  // Reset caches when trace changes
  useEffect(() => {
    processedSpansRef.current.clear();
    // Clear hash cache periodically to prevent memory leaks
    if (hashCache.current.size > 10000) {
      hashCache.current.clear();
    }
  }, [traces, hashCache]);

  // Also reset per-span processed cache when span list changes (e.g., pagination)
  useEffect(() => {
    processedSpansRef.current.clear();
  }, [threadSpans]);

  const processSpanData = useCallback(
    (span: Span) => {
      if (processedSpansRef.current.has(span.id)) {
        return processedSpansRef.current.get(span.id)!;
      }

      if (span.data.span_attributes.type !== "llm") {
        const emptyResult = {
          messages: [],
          toolDefinitions: new Map<string, string>(),
          toolResponses: new Map<string, unknown>(),
        };
        processedSpansRef.current.set(span.id, emptyResult);
        return emptyResult;
      }

      const inputString =
        typeof span.data.input === "string" ? span.data.input : "{}";
      const { value: input } = deserializePlainStringAsJSON(inputString);
      const outputString =
        typeof span.data.output === "string" ? span.data.output : "{}";
      const { value: output } = deserializePlainStringAsJSON(outputString);

      const allMessages = [];
      let toolDefinitions = new Map<string, string>();

      // Normalize data first, then parse with parseLLMSpanPart
      const metadataString =
        typeof span.data.metadata === "string" ? span.data.metadata : undefined;

      const spanAttributes = span.data.span_attributes;

      const normalized = normalizeToKnownFormat(
        input,
        output,
        metadataString,
        spanAttributes,
        [aiSDKResponseNormalizer, openAIResponseNormalizer], // List of available normalizers
        { cacheKey: span.id },
      );

      // Use parseLLMSpanPart on normalized data
      const parsedMessages =
        !!normalized.input && parseLLMSpanPart(normalized.input);
      if (parsedMessages) {
        allMessages.push(...parsedMessages);
      }

      const parsedOutput =
        !!normalized.output && parseLLMSpanPart(normalized.output);
      if (parsedOutput) {
        allMessages.push(...parsedOutput);
      }

      toolDefinitions = normalized.toolDefinitions;

      // Track tool responses by tool_call_id from 'tool' role messages
      // Use a local map for this span's tool responses
      const localToolResponses = new Map<string, unknown>();
      for (const message of allMessages) {
        if (message && message.role === "tool" && message.tool_call_id) {
          // Extract content from tool message - handle both string and array formats
          let toolContent = message.content;
          if (Array.isArray(toolContent)) {
            // For array content (like Claude format), extract text from content blocks
            toolContent = toolContent
              .map((block) => {
                if (typeof block === "string") return block;
                if (block && typeof block === "object" && "text" in block) {
                  return block.text;
                }
                return JSON.stringify(block);
              })
              .join("");
          }

          localToolResponses.set(message.tool_call_id, toolContent);
        }
      }

      // Deduplicate messages by comparing against the first span index where they appear
      const uniqueMessages: LLMMessageType[] = [];
      const currentSpanIndex = spanIndexMap.get(span.id) ?? -1;
      for (let index = 0; index < allMessages.length; index++) {
        const message = allMessages[index];
        if (message) {
          if (message.role === "tool") {
            // Skip tool messages from being rendered as separate bubbles
            // They are already tracked in toolResponses for display in tool calls
            continue;
          }
          const messageHash = hashMessage(message);
          const firstIdx = firstSeenToolIdIndex.get(messageHash);
          const keepByMessage =
            firstIdx === undefined || firstIdx === currentSpanIndex;

          // Additionally, for assistant tool_calls, keep only the first occurrence of each tool_call id across spans
          let keepByTools = true;
          if (
            message.role === "assistant" &&
            "tool_calls" in message &&
            Array.isArray(message.tool_calls)
          ) {
            keepByTools = message.tool_calls.every((tc) => {
              const id = tc.id;
              if (!id) return true;
              const firstToolIdx = firstSeenToolId.get(id);
              return (
                firstToolIdx === undefined || firstToolIdx === currentSpanIndex
              );
            });
          }

          if (keepByMessage && keepByTools) {
            // Keep only messages whose first appearance is in this span
            // Within the same span, preserve the order they appear
            if (!uniqueMessages.some((m) => hashMessage(m) === messageHash)) {
              uniqueMessages.push(message);
            }
          }
        }
      }

      const result = {
        messages: uniqueMessages,
        toolDefinitions,
        toolResponses: localToolResponses,
      };

      processedSpansRef.current.set(span.id, result);
      return result;
    },
    [firstSeenToolIdIndex, spanIndexMap, firstSeenToolId, hashMessage],
  );

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: hasNextPage ? threadSpans.length + 1 : threadSpans.length,
    getScrollElement: () => scrollContainerRef.current,
    estimateSize: () => 80,
    overscan: 4,
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 80;
    },
  });

  useInfiniteFetch({
    virtualizer,
    hasNextPage,
    fetchNextPage,
    totalRowCount: threadSpans.length,
    isFetching,
  });

  // Derived toc items
  const shouldRenderSpan = useCallback(
    (span: Span): boolean => {
      const attrs = span.data.span_attributes;
      const isRecord = (v: unknown): v is Record<string, unknown> =>
        typeof v === "object" && v !== null;
      const typeValue: unknown = isRecord(attrs) ? attrs.type : undefined;
      if (typeValue === "score") return true;
      if (typeValue === "llm") {
        const { messages } = processSpanData(span);
        return messages.length > 0;
      }
      return false;
    },
    [processSpanData],
  );

  const { tocItems, toolResponses } = useMemo(() => {
    const items: TocItem[] = [];
    const toolResponses: Map<string, unknown> = new Map();
    // eslint-disable-next-line react-compiler/react-compiler
    threadSpans.forEach((span, index) => {
      const attrs = span.data.span_attributes;
      if (!isObject(attrs)) return;
      const typeValue = attrs["type"];
      const nameValue = attrs["name"];

      if (shouldRenderSpan(span)) {
        if (typeValue === "llm") {
          const spanData = processSpanData(span);

          for (const [toolCallId, result] of spanData.toolResponses) {
            if (result) {
              toolResponses.set(toolCallId, result);
            }
          }

          spanData.messages.forEach((message, messageIndex) => {
            const description =
              typeof message.content === "string"
                ? message.content.slice(0, 50)
                : message.content
                    ?.map((c) => (c.type === "text" ? c.text : ""))
                    .join("")
                    .slice(0, 50);

            items.push({
              spanIndex: index,
              messageIndex: messageIndex,
              title:
                message.role.charAt(0).toUpperCase() + message.role.slice(1),
              description,
            });

            // Add individual tool calls as separate TOC items
            if (
              message.role === "assistant" &&
              "tool_calls" in message &&
              message.tool_calls
            ) {
              const seenToolSignatures = new Set<string>();
              message.tool_calls.forEach((toolCall) => {
                const normalizedArgs = toolCall.function?.arguments;
                const signature = `${toolCall.function?.name ?? ""}|${normalizedArgs ?? ""}|${toolCall.type ?? ""}`;
                if (seenToolSignatures.has(signature)) return;
                seenToolSignatures.add(signature);
                items.push({
                  spanIndex: index,
                  messageIndex: messageIndex,
                  toolCallId: toolCall.id,
                  icon: BoltIcon,
                  iconClassName: "text-amber-600 dark:text-amber-400",
                  title: toolCall.function.name,
                  description: toolCall.function?.arguments,
                });
              });
            }
          });
        } else if (typeValue === "score") {
          // For score spans, include score information
          const scores = span.data.scores || {};
          const scoreNames = Object.keys(scores);
          const scoreValues: Record<string, number> = {};

          Object.entries(scores).forEach(([name, score]) => {
            if (typeof score === "number") {
              scoreValues[name] = score;
            } else if (score && typeof score === "object") {
              // Handle various score object formats
              // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
              const scoreObj = score as any;
              if (scoreObj.score != null) {
                scoreValues[name] = Number(scoreObj.score) || 0;
              } else if (scoreObj.value != null) {
                scoreValues[name] = Number(scoreObj.value) || 0;
              }
            }
          });

          const name = typeof nameValue === "string" ? nameValue : "Score";
          items.push({
            title: name,
            spanIndex: index,
            icon: Percent,
            scoreValue: scoreValues[scoreNames[0]],
          });
        }
      }
    });
    return { tocItems: items, toolResponses };
  }, [threadSpans, shouldRenderSpan, processSpanData]);

  // Track active TOC item based on scroll position
  const [activeTocIndex, setActiveTocIndex] = useState<number>(0);
  // When the user clicks a TOC item, pin the active index to that item
  // until the user manually scrolls, to avoid misselection when multiple
  // items are visible at once (e.g., near the end of the list).
  const [pinnedTocIndex, setPinnedTocIndex] = useState<number | null>(null);
  const isProgrammaticScrollRef = useRef(false);
  const programmaticScrollTimeoutRef = useRef<number | null>(null);
  const userInteractingRef = useRef(false);
  const tocContainerRef = useRef<HTMLDivElement>(null);

  const getTocTargetId = useCallback(
    (toc: TocItem): string | null => {
      const spanId = threadSpans[toc.spanIndex]?.id;
      if (!spanId) return null;
      if (toc.toolCallId != null && toc.messageIndex != null) {
        return `tool-${spanId}-${toc.messageIndex}-${toc.toolCallId}`;
      }
      if (toc.messageIndex != null) {
        return `message-${spanId}-${toc.messageIndex}`;
      }
      return `span-${spanId}`;
    },
    [threadSpans],
  );

  // Scroll handler to update active TOC index (disabled while pinned)
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    let rafId: number | null = null;
    const computeActive = () => {
      // If a TOC item is pinned (from a click), do not update the
      // active index based on scroll position.
      if (pinnedTocIndex != null) {
        return;
      }
      rafId = null;
      if (!container) return;
      const containerRect = container.getBoundingClientRect();

      let bestIdx: number | null = null;
      let bestPositiveTop = Number.POSITIVE_INFINITY;
      let bestNegativeTop = -Number.POSITIVE_INFINITY;
      let bestNegativeIdx: number | null = null;

      for (let i = 0; i < tocItems.length; i++) {
        const id = getTocTargetId(tocItems[i]);
        if (!id) continue;
        const el = document.getElementById(id);
        if (!el) continue;
        const rect = el.getBoundingClientRect();
        const topDelta = rect.top - containerRect.top;
        if (topDelta >= 0) {
          if (topDelta < bestPositiveTop) {
            bestPositiveTop = topDelta;
            bestIdx = i;
          }
        } else {
          if (topDelta > bestNegativeTop) {
            bestNegativeTop = topDelta;
            bestNegativeIdx = i;
          }
        }
      }

      // If we found an element below the top, use it
      // Otherwise, use the last element that was above the top (closest to top)
      if (bestIdx != null) {
        setActiveTocIndex(bestIdx);
      } else if (bestNegativeIdx != null) {
        setActiveTocIndex(bestNegativeIdx);
      }
    };

    const clearPinIfUserScroll = () => {
      if (pinnedTocIndex != null && !isProgrammaticScrollRef.current) {
        setPinnedTocIndex(null);
      }
    };

    const onWheel = () => clearPinIfUserScroll();
    const onPointerDown = () => {
      userInteractingRef.current = true;
    };
    const onPointerUp = () => {
      userInteractingRef.current = false;
    };
    const onScroll = () => {
      if (userInteractingRef.current) {
        clearPinIfUserScroll();
      }

      // Update active TOC inde
      if (rafId != null) return;
      rafId = requestAnimationFrame(computeActive);
    };

    container.addEventListener("scroll", onScroll, { passive: true });
    container.addEventListener("wheel", onWheel, { passive: true });
    container.addEventListener("pointerdown", onPointerDown, {
      passive: true,
    });
    container.addEventListener("pointerup", onPointerUp, {
      passive: true,
    });
    computeActive();

    return () => {
      container.removeEventListener("scroll", onScroll);
      container.removeEventListener("wheel", onWheel);
      container.removeEventListener("pointerdown", onPointerDown);
      container.removeEventListener("pointerup", onPointerUp);
      if (rafId != null) cancelAnimationFrame(rafId);
    };
  }, [scrollContainerRef, tocItems, getTocTargetId, pinnedTocIndex]);

  // Cleanup pending programmatic scroll timeout on unmount
  useEffect(() => {
    return () => {
      if (programmaticScrollTimeoutRef.current != null) {
        window.clearTimeout(programmaticScrollTimeoutRef.current);
        programmaticScrollTimeoutRef.current = null;
      }
    };
  }, []);

  // Keep active TOC item visible within the TOC container
  useEffect(() => {
    const tocContainer = tocContainerRef.current;
    if (!tocContainer) return;
    if (activeTocIndex === 0) {
      tocContainerRef.current?.scrollTo({
        top: 0,
        behavior: "smooth",
      });
      return;
    }
    const activeBtn = tocContainer.querySelector<HTMLElement>(
      `[data-toc-index="${activeTocIndex}"]`,
    );
    if (activeBtn) {
      activeBtn.scrollIntoView({
        block: activeTocIndex === 0 ? "start" : "nearest",
        inline: "nearest",
        behavior: "smooth",
      });
    }
  }, [activeTocIndex]);

  const handleTocClick = useCallback(
    ({ index, tocItem }: { index: number; tocItem: TocItem }) => {
      // Pin the selected TOC index so it stays highlighted even if
      // the clicked target cannot scroll to the very top.
      setPinnedTocIndex(index);
      setActiveTocIndex(index);

      // Mark the following scroll as programmatic to avoid clearing the pin
      // due to the resulting scroll events.
      isProgrammaticScrollRef.current = true;
      if (programmaticScrollTimeoutRef.current != null) {
        window.clearTimeout(programmaticScrollTimeoutRef.current);
      }
      programmaticScrollTimeoutRef.current = window.setTimeout(() => {
        isProgrammaticScrollRef.current = false;
      }, 700);

      if (tocItem.toolCallId && tocItem.messageIndex !== undefined) {
        // Scroll to specific tool within the message
        const toolId = `tool-${threadSpans[tocItem.spanIndex]?.id}-${tocItem.messageIndex}-${tocItem.toolCallId ?? ""}`;
        const toolElement = document.getElementById(toolId);
        if (toolElement) {
          scrollIntoViewWithMargin(toolElement, scrollContainerRef.current);
          return;
        }
      }

      if (tocItem.messageIndex !== undefined) {
        // Scroll to specific message within the span
        const messageId = `message-${threadSpans[tocItem.spanIndex]?.id}-${tocItem.messageIndex}`;
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
          scrollIntoViewWithMargin(messageElement, scrollContainerRef.current);
          return;
        }
      }

      // Fallback to span-level scrolling
      virtualizer.scrollToIndex(tocItem.spanIndex, {
        align: "start",
        behavior: "smooth",
      });
    },
    [virtualizer, threadSpans],
  );

  return (
    <>
      {error && (
        <div className="flex-1 p-3">
          <ErrorDisplay error={error} />
        </div>
      )}
      {(threadSpans.length ?? 0) === 0 &&
      (!queryProps || !queryProps.isPlaceholderData) ? (
        <div className="flex-1 p-3">
          <TableEmptyState
            className="gap-3"
            label="No conversation thread to display"
            labelClassName="text-sm font-medium"
          >
            <div className="text-xs text-primary-500">
              This trace doesn&apos;t contain any LLM, score, or tool spans
            </div>
          </TableEmptyState>
        </div>
      ) : (
        <div className="@container flex flex-1 overflow-hidden">
          <div
            className={cn(
              "hidden w-40 flex-none space-y-1 overflow-auto border-r border-primary-100 px-2 py-3",
              {
                "@lg:block @2xl:w-48 @3xl:w-56": !collapsed,
              },
            )}
            ref={tocContainerRef}
          >
            <Button
              variant="ghost"
              size="xs"
              className="mb-2 ml-1 text-primary-500"
              Icon={PanelLeft}
              onClick={() => setCollapsed(true)}
            >
              Contents
            </Button>
            {tocItems.map((toc, idx) => (
              <Button
                title={toc.title}
                variant="ghost"
                key={`toc-${toc.spanIndex}-${idx}`}
                size="xs"
                data-toc-index={idx}
                id={`toc-btn-${toc.spanIndex}-${idx}`}
                className={cn(
                  "flex h-auto min-h-6 w-full flex-col items-start gap-1 overflow-hidden py-1 text-left font-normal",
                  {
                    "bg-primary-100 hover:bg-primary-200":
                      idx === activeTocIndex,
                  },
                )}
                onClick={() =>
                  handleTocClick({
                    index: idx,
                    tocItem: toc,
                  })
                }
              >
                <span
                  className={cn(
                    "flex w-full items-center gap-1 text-primary-600",
                    {
                      "pl-3": toc.icon === BoltIcon,
                    },
                  )}
                >
                  {toc.icon && (
                    <toc.icon
                      className={cn("size-3 flex-none", toc.iconClassName)}
                    />
                  )}
                  {toc.title && (
                    <span
                      className={cn(
                        "flex-1 truncate font-medium",
                        idx === activeTocIndex && "text-primary-900",
                      )}
                    >
                      {toc.title}
                    </span>
                  )}
                  {toc.scoreValue !== undefined && (
                    <span className="flex-none text-xs text-primary-500">
                      {(toc.scoreValue * 100).toLocaleString(undefined, {
                        maximumFractionDigits: 1,
                      })}
                      %
                    </span>
                  )}
                </span>
                {toc.description && (
                  <span
                    className={cn(
                      "block w-full truncate font-normal text-primary-500",
                      {
                        "pl-3": toc.icon === BoltIcon,
                      },
                    )}
                  >
                    {toc.description}
                  </span>
                )}
              </Button>
            ))}
          </div>
          <div className="grow overflow-auto p-3" ref={scrollContainerRef}>
            <div
              className="relative"
              style={{ height: `${virtualizer.getTotalSize()}px` }}
            >
              {virtualizer.getVirtualItems().map((virtualItem) => {
                const isLoaderRow = virtualItem.index > threadSpans.length - 1;
                const item = threadSpans.at(virtualItem.index);
                if (!isLoaderRow && !item) return null;

                const isRoot = item && item.parent_span_id == null;

                return (
                  <div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={(node) => virtualizer.measureElement(node)}
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      width: "100%",
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                    id={!isLoaderRow && item ? `span-${item.id}` : undefined}
                    className=""
                  >
                    {isLoaderRow ? (
                      <div className="flex items-center justify-center">
                        <Spinner />
                      </div>
                    ) : !item ? null : (
                      <>
                        <div className="flex items-center gap-4 pb-4">
                          {virtualItem.index === 0 && collapsed && (
                            <Button
                              size="xs"
                              className="hidden @lg:flex"
                              Icon={ArrowRightToLine}
                              onClick={() => setCollapsed(false)}
                            >
                              Contents
                            </Button>
                          )}
                          {isRoot && <RootSpanHeader span={item} />}
                        </div>
                        <ErrorBoundary
                          fallback={
                            <ErrorBanner skipErrorReporting>
                              There was a problem rendering this item
                            </ErrorBanner>
                          }
                        >
                          <LazyThreadItem
                            isFirst={virtualItem.index === 0}
                            span={item}
                            onClickSpan={onClickSpan}
                            processSpanData={processSpanData}
                            toolResponses={toolResponses}
                            allAvailableModelCosts={allAvailableModelCosts}
                            virtualizer={virtualizer}
                          />
                        </ErrorBoundary>
                      </>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const LazyThreadItem = React.memo(
  ({
    span,
    onClickSpan,
    processSpanData,
    toolResponses,
    allAvailableModelCosts,
    isFirst,
    virtualizer,
  }: {
    span: Span;
    onClickSpan: (span: Span) => void;
    processSpanData: (span: Span) => {
      messages: LLMMessageType[];
      toolDefinitions: Map<string, string>;
    };
    toolResponses: Map<string, unknown>;
    allAvailableModelCosts?: Record<string, ModelCosts>;
    isFirst: boolean;
    virtualizer: Virtualizer<HTMLDivElement, Element>;
  }) => {
    if (
      span.data.span_attributes.type !== "score" &&
      span.data.span_attributes.type !== "llm"
    ) {
      return null;
    }

    return (
      <ThreadSpanItem
        span={span}
        isFirst={isFirst}
        spanType={span.data.span_attributes.type}
        onClickSpan={onClickSpan}
        processSpanData={processSpanData}
        allAvailableModelCosts={allAvailableModelCosts}
        toolResponses={toolResponses}
        virtualizer={virtualizer}
      />
    );
  },
);

LazyThreadItem.displayName = "LazyThreadItem";

const RootSpanHeader = ({ span }: { span: Span }) => {
  return span.data.error ? (
    <ErrorDisplay error={span.data.error} />
  ) : (
    <ThreadSpanMetrics span={span} isRoot />
  );
};
