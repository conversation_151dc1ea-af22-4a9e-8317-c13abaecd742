import { Calendar, Timer, Blocks } from "lucide-react";
import {
  deserializePlainStringAsJSON,
  isEmpty,
  isObject,
} from "#/utils/object";
import { flattenDiffObjects } from "#/utils/diffs/diff-objects";
import { DateWithTooltip } from "#/ui/date";
import { formatPriceValue } from "#/ui/type-formatters/metrics";
import { type Span } from "#/ui/trace/graph";
import { ModelOptionLabel } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useContext } from "react";
import { RESERVED_TAGS } from "#/app/app/[org]/p/[project]/configuration/tags/tag-dialog";
import { Tag } from "#/ui/tag";

const ThreadSpanMetric = ({
  value,
  Icon,
  title,
}: {
  value: unknown;
  Icon?: React.ComponentType<{ className?: string }>;
  title: string;
}) => {
  if (isEmpty(value)) {
    return null;
  }
  return (
    <div
      className="flex items-center gap-1 text-xs text-primary-600"
      title={title}
    >
      {Icon && <Icon className="size-3" />}
      <span>
        {title === "Created" && typeof value === "number" ? (
          <DateWithTooltip
            dateMs={value}
            withSeconds
            relativeTimeOptions={{ round: true }}
          />
        ) : title === "Duration" && typeof value === "number" ? (
          `${value.toLocaleString(undefined, {
            maximumFractionDigits: 2,
          })}s`
        ) : title === "Completion tokens" && typeof value === "number" ? (
          value.toLocaleString()
        ) : title === "Estimated LLM cost" && typeof value === "number" ? (
          formatPriceValue(value)
        ) : (
          String(value)
        )}
      </span>
    </div>
  );
};

export const ThreadSpanMetrics = ({
  span,
  isRoot,
}: {
  span: Span | null;
  isRoot?: boolean;
}) => {
  const { config: projectConfig } = useContext(ProjectContext);

  if (!span) return null;

  const flattenedRootData = flattenDiffObjects(span.data);
  const metadataString =
    typeof span.data.metadata === "string" ? span.data.metadata : "{}";
  const { value: metadata } = deserializePlainStringAsJSON(metadataString);

  const scorerLlmSpan = span.children.find(
    (c) =>
      c.data.span_attributes &&
      c.data.span_attributes.type === "llm" &&
      "purpose" in c.data.span_attributes &&
      c.data.span_attributes.purpose === "scorer",
  );

  const scorerLlmSpanMetadataString =
    typeof scorerLlmSpan?.data.metadata === "string"
      ? scorerLlmSpan?.data.metadata
      : "{}";
  const { value: llmSpanChildMetadata } = deserializePlainStringAsJSON(
    scorerLlmSpanMetadataString,
  );
  const model = metadata?.model ?? llmSpanChildMetadata?.model;
  const metrics =
    flattenedRootData?.metrics || scorerLlmSpan?.data.metrics
      ? {
          ...scorerLlmSpan?.data.metrics,
          ...flattenedRootData?.metrics,
        }
      : undefined;

  if (!isObject(metrics)) return null;

  const metricsStart =
    typeof metrics.start === "number" ? metrics.start : undefined;
  const metricsEnd = typeof metrics.end === "number" ? metrics.end : undefined;
  const metricsDuration =
    metricsStart && metricsEnd ? metricsEnd - metricsStart : undefined;
  const cost =
    "estimated_cost" in metrics && typeof metrics.estimated_cost === "number"
      ? formatPriceValue(metrics.estimated_cost)
      : undefined;

  const tagsObject = isRoot
    ? span.data.tags?.filter((t) => !RESERVED_TAGS.includes(t))
    : undefined;
  const tagsInMetrics = tagsObject?.map((t) => ({
    name: t,
    ...(projectConfig?.tags.find((tag) => tag.name === t) || {}),
  }));

  return (
    <div className="flex min-h-7 flex-wrap items-center gap-4 text-xs">
      {tagsInMetrics && tagsInMetrics.length > 0 && (
        <div className="flex flex-wrap items-center gap-2 text-xs">
          {tagsInMetrics.map((tag) => (
            <Tag key={tag.name} label={tag.name} color={tag.color} />
          ))}
        </div>
      )}
      {isRoot && (
        <ThreadSpanMetric
          value={metricsStart ? metricsStart * 1000 : undefined}
          Icon={Calendar}
          title="Created"
        />
      )}
      {!isRoot && model && (
        <div className="flex items-center gap-2 text-xs text-primary-600">
          <ModelOptionLabel
            className="w-full gap-1 overflow-hidden"
            labelClassName="truncate text-primary-600"
            model={model}
          />
        </div>
      )}
      {metricsDuration != null && (
        <ThreadSpanMetric
          value={metricsDuration}
          Icon={Timer}
          title="Duration"
        />
      )}
      <ThreadSpanMetric
        value={metrics?.completion_tokens}
        Icon={Blocks}
        title="Completion tokens"
      />
      <ThreadSpanMetric value={cost} title="Estimated LLM cost" />
    </div>
  );
};
