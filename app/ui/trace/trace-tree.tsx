"use client";

import { cn } from "#/utils/classnames";
import React, {
  type RefObject,
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
} from "react";
import { DiffNumbers } from "#/ui/diff";
import {
  type Trace,
  type PreviewSpan,
  type Span,
  type SpanMetrics,
} from "./graph";
import {
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  flattenDiffObjects,
  getDiffRight,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { backfillSpanType, getSpanDisplayConfig } from "./span-display";
import { ChevronRight } from "lucide-react";
import { isEmpty } from "#/utils/object";
import { BasicTooltip } from "#/ui/tooltip";
import { Spinner } from "#/ui/icons/spinner";
import * as d3 from "d3";
import { formatPriceValue } from "#/ui/type-formatters/metrics";
import { SpanTypeAttribute } from "braintrust/util";
import { useVirtualizer, type Virtualizer } from "@tanstack/react-virtual";
import { type traceCollapseState } from "./trace";
import { useTraceViewTypeState } from "#/ui/query-parameters";
import { useModelConfig } from "./use-model-config";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type TracePaginationProps } from "./use-related-traces";
import { useInfiniteFetch } from "#/utils/virtualizer/infinite-fetch";
import { noopFn } from "#/utils/react";
import { Skeleton } from "#/ui/skeleton";

const TimelineViewHeader = ({ totalDuration }: { totalDuration?: number }) => {
  const ref = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  const redrawChart = useCallback(() => {
    if (!totalDuration || !ref.current || !svgRef.current) return;

    d3.select(svgRef.current).selectAll("*").remove();

    const width = ref.current?.clientWidth ?? 100;

    const xScale = d3
      .scaleLinear()
      .domain([0, totalDuration])
      .range([0, width]);

    const xAxis = d3
      .axisTop(xScale)
      .ticks(width / 80)
      .tickSize(2000)
      .tickFormat((d) => `${xScale.tickFormat(10)(d)}s`);

    d3.select(svgRef.current)
      .attr("width", width)
      .attr("height", 30)
      .append("g")
      .attr("transform", "translate(0,2016)")
      .call(xAxis);
  }, [totalDuration]);

  useEffect(() => {
    redrawChart();

    if (!ref.current) return;
    const resizeObserver = new ResizeObserver(() => {
      redrawChart();
    });

    resizeObserver.observe(ref.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [redrawChart]);

  if (!totalDuration) {
    return null;
  }

  return (
    <div className="flex h-7 px-3 py-2">
      <div className="w-64 flex-none" />
      <div ref={ref} className="relative flex-1">
        <svg
          ref={svgRef}
          className="absolute top-0 left-0 w-full overflow-visible text-primary-500 [&_.domain]:hidden [&_.tick>line]:text-primary-100"
        />
      </div>
    </div>
  );
};

interface FlattenedSpan {
  span: Span | PreviewSpan;
  level: number;
  index: number;
  totalSiblings: number;
  parentCollapsed: boolean;
  finishedLevels: Set<number>; // Levels where isLastChild was true
  ancestors: (Span | PreviewSpan)[]; // Ancestors from root to parent, indexed by level
}

function flattenSpanTree(
  roots: (PreviewSpan | Span)[],
  collapsedIds: Set<string>,
  level = 0,
  parentCollapsed = false,
  parentFinishedLevels: Set<number> = new Set(),
  ancestors: (Span | PreviewSpan)[] = [],
): FlattenedSpan[] {
  const result: FlattenedSpan[] = [];

  for (let i = 0; i < roots.length; i++) {
    const span = roots[i];
    const isCollapsed = collapsedIds.has(span.id);
    const shouldShow = !parentCollapsed;
    const isLastChild = i === roots.length - 1;

    const finishedLevels = new Set(parentFinishedLevels);
    if (isLastChild) {
      finishedLevels.add(level);
    }

    if (shouldShow) {
      result.push({
        span,
        level,
        index: i,
        totalSiblings: roots.length,
        parentCollapsed,
        finishedLevels,
        ancestors: [...ancestors],
      });
    }

    if (span.children.length > 0) {
      result.push(
        ...flattenSpanTree(
          span.children,
          collapsedIds,
          level + 1,
          parentCollapsed || isCollapsed,
          finishedLevels,
          [...ancestors, span],
        ),
      );
    }
  }

  return result;
}

export function TraceTree(props: {
  traces: Trace[];
  level?: number;
  seen: Set<string>;
  comparisonClassName: string;
  setSelectedSpan: (span: Span) => void;
  firstRootTitle?: string;
  showMetrics: boolean;
  collapseState: traceCollapseState;
  onCollapseStateChange: (state: traceCollapseState) => void;
  totalDuration?: number;
  traceStart?: number;
  containerRef: RefObject<HTMLDivElement | null>;
  hideCollapseButton?: boolean;
  selectedSpan: Span | null;
  tracePaginationProps?: TracePaginationProps;
  isPaginatedTracesLoading?: boolean;
}) {
  const {
    traces,
    level,
    seen,
    showMetrics,
    collapseState,
    onCollapseStateChange,
    totalDuration,
    containerRef,
    hideCollapseButton,
    selectedSpan,
    tracePaginationProps,
    isPaginatedTracesLoading,
  } = props;
  const { projectId } = useContext(ProjectContext);
  const [viewType] = useTraceViewTypeState(projectId);
  const isTimelineView = viewType === "timeline";

  const collapsedIds = useMemo(
    () =>
      collapseState.state === "expanded"
        ? new Set<string>()
        : collapseState.ids,
    [collapseState],
  );

  const toggleCollapsed = useCallback(
    (spanId: string) => {
      const currentCollapsedIds =
        collapseState.state === "expanded"
          ? new Set<string>()
          : collapseState.ids;
      const newCollapsedIds = new Set(currentCollapsedIds);

      if (newCollapsedIds.has(spanId)) {
        newCollapsedIds.delete(spanId);
      } else {
        newCollapsedIds.add(spanId);
      }

      onCollapseStateChange({ state: "mixed", ids: newCollapsedIds });
    },
    [collapseState, onCollapseStateChange],
  );

  // Flatten the tree for virtualization
  const flattenedSpans = useMemo(() => {
    const filtered = traces.flatMap((t) =>
      !seen.has(t.root.id) ? [t.root] : [],
    );
    return flattenSpanTree(filtered, collapsedIds);
  }, [traces, seen, collapsedIds]);

  const lowestVisibleSpanIndex = useMemo(() => {
    if (!selectedSpan) {
      return -1;
    }

    return flattenedSpans.findLastIndex(
      (span) => selectedSpan.span_id === span.span.span_id,
    );
  }, [selectedSpan, flattenedSpans]);

  // https://github.com/TanStack/virtual/discussions/195#discussioncomment-13906325
  const bottomDistanceRef = useRef(0);
  const scrollMargin = 52;
  const virtualizer = useVirtualizer({
    count: tracePaginationProps?.hasNextPage
      ? flattenedSpans.length + 1
      : flattenedSpans.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => {
      return showMetrics && !isTimelineView ? 40 : 32;
    },
    overscan: 10,
    onChange: useCallback(
      <S extends Element | Window, I extends Element>(
        instance: Virtualizer<S, I>,
        sync: boolean,
      ) => {
        bottomDistanceRef.current =
          instance.getTotalSize() - (instance.scrollOffset ?? 0);
      },
      [],
    ),
    // unique key for top-pagination
    getItemKey: useCallback(
      (index: number) => {
        return flattenedSpans[index]?.span.id;
      },
      [flattenedSpans],
    ),
    scrollMargin,
    paddingEnd: 40,
  });

  const hasVirtualItems = virtualizer.getVirtualIndexes().length > 0;
  const hasVirtualizerInitialized = useRef(false);
  useLayoutEffect(() => {
    if (
      hasVirtualizerInitialized.current ||
      !lowestVisibleSpanIndex ||
      !hasVirtualItems
    ) {
      return;
    }
    const initialOffset =
      virtualizer.getOffsetForIndex(lowestVisibleSpanIndex, "start")?.[0] ?? 0;
    virtualizer.scrollToOffset(initialOffset, {
      align: "start",
    });
    bottomDistanceRef.current = virtualizer.getTotalSize() - initialOffset;
    hasVirtualizerInitialized.current = true;
  }, [flattenedSpans, virtualizer, lowestVisibleSpanIndex, hasVirtualItems]);

  const prevLowestLoadedIndexRef = useRef(
    tracePaginationProps?.lowestLoadedIndex,
  );
  // reset lower bound ref when switching traces or grouping
  useLayoutEffect(() => {
    if (
      tracePaginationProps?.lowestLoadedIndex != null &&
      (prevLowestLoadedIndexRef.current == null ||
        tracePaginationProps.lowestLoadedIndex >
          prevLowestLoadedIndexRef.current)
    ) {
      prevLowestLoadedIndexRef.current =
        tracePaginationProps.lowestLoadedIndex + 1;
    }
  }, [tracePaginationProps?.lowestLoadedIndex]);
  useLayoutEffect(() => {
    if (tracePaginationProps?.lowestLoadedIndex == null || !hasVirtualItems) {
      return;
    }
    if (
      prevLowestLoadedIndexRef.current == null ||
      tracePaginationProps?.lowestLoadedIndex < prevLowestLoadedIndexRef.current
    ) {
      // maintain the previous scroll offset when paginating upward so we don't infinitely page
      const totalSize = virtualizer.getTotalSize();
      virtualizer.scrollToOffset(totalSize - bottomDistanceRef.current, {
        align: "start",
      });
    }
    prevLowestLoadedIndexRef.current = tracePaginationProps?.lowestLoadedIndex;
  }, [tracePaginationProps?.lowestLoadedIndex, virtualizer, hasVirtualItems]);

  useEffect(() => {
    virtualizer.measure();
  }, [showMetrics, isTimelineView, virtualizer]);

  useInfiniteFetch({
    virtualizer: tracePaginationProps != null ? virtualizer : undefined,
    hasNextPage: !!tracePaginationProps?.hasNextPage,
    fetchNextPage: tracePaginationProps?.fetchNextPage ?? noopFn,
    hasPreviousPage: !!tracePaginationProps?.hasPreviousPage,
    fetchPreviousPage: tracePaginationProps?.fetchPreviousPage,
    totalRowCount: flattenedSpans.length,
    isFetching: tracePaginationProps?.isFetching || false,
  });

  return (
    <div className="@container">
      {isTimelineView && !level && (
        <TimelineViewHeader totalDuration={totalDuration} />
      )}

      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: "100%",
          position: "relative",
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const flattenedSpan = flattenedSpans[virtualItem.index];
          if (!flattenedSpan) return null;

          return (
            <div
              key={virtualItem.key}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start - scrollMargin}px)`,
              }}
            >
              <TreeChild
                {...props}
                selectedSpanId={selectedSpan?.id ?? null}
                currentSpan={flattenedSpan.span}
                level={flattenedSpan.level}
                childIndex={flattenedSpan.index}
                totalSiblings={flattenedSpan.totalSiblings}
                toggleCollapsed={toggleCollapsed}
                isCollapsed={collapsedIds.has(flattenedSpan.span.id)}
                disableChildrenRendering={true}
                hideCollapseButton={hideCollapseButton}
                finishedLevels={flattenedSpan.finishedLevels}
              />
            </div>
          );
        })}
      </div>
      {isPaginatedTracesLoading && (
        <div
          // match paddingEnd of the virtualizer + the extra loader item if it exists
          className={cn("-mt-[40px]", {
            "-mt-[80px]": tracePaginationProps?.hasNextPage,
          })}
        >
          {Array.from({ length: 10 }).map((_, i) => (
            <Skeleton key={i} className={cn("mx-0.5 mt-0.5 h-10")} />
          ))}
        </div>
      )}
    </div>
  );
}

const TreeChild = (props: {
  currentSpan: Span | PreviewSpan;
  level?: number;
  childIndex?: number;
  totalSiblings?: number;
  seen: Set<string>;
  selectedSpanId: string | null;
  setSelectedSpan: (span: Span | PreviewSpan) => void;
  firstRootTitle?: string;
  showMetrics: boolean;
  collapseState: traceCollapseState;
  totalDuration?: number;
  traceStart?: number;
  comparisonClassName: string;
  toggleCollapsed: (spanId: string) => void;
  isCollapsed: boolean;
  disableChildrenRendering?: boolean;
  hideCollapseButton?: boolean;
  finishedLevels?: Set<number>;
}) => {
  const {
    currentSpan,
    selectedSpanId,
    setSelectedSpan,
    firstRootTitle,
    showMetrics,
    level = 0,
    childIndex = 0,
    totalSiblings = 1,
    totalDuration,
    traceStart,
    comparisonClassName,
    toggleCollapsed,
    isCollapsed,
    hideCollapseButton,
    finishedLevels = new Set(),
  } = props;

  const { projectId } = useContext(ProjectContext);
  const [viewType, setViewType] = useTraceViewTypeState(projectId);
  const isTimelineView = viewType === "timeline";
  const hasChildren = currentSpan.children.length > 0;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const currentSpanMetrics = flattenDiffObjects(currentSpan.data).metrics as
    | {
        cached?: boolean;
        start?: number;
        end?: number;
      }
    | undefined;

  const handleToggleCollapsed = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      toggleCollapsed(currentSpan.id);
    },
    [toggleCollapsed, currentSpan.id],
  );

  const spanType =
    currentSpan.data.span_attributes?.type ??
    backfillSpanType(currentSpan.data.span_attributes?.name);

  const modelConfig = useModelConfig(
    spanType === "llm" ? currentSpan.data.model : undefined,
  );

  const spanName = currentSpan.data.span_attributes?.name || firstRootTitle;
  const name =
    spanName === "Chat Completion" && modelConfig
      ? modelConfig.modelDisplayName
      : spanName;

  const hasError =
    !isEmpty(currentSpan.data.error) &&
    (!isDiffObject(currentSpan.data.error) ||
      !isEmpty(currentSpan.data.error[DiffRightField]));

  const {
    Icon,
    iconClassName,
    lightClassName,
    borderClassName,
    tooltip,
    type,
  } = useMemo(
    () =>
      getSpanDisplayConfig({
        type: spanType,
        cached: !!currentSpanMetrics?.cached,
        remote: !!currentSpan.data.span_attributes?.remote,
        hasError,
      }),
    [
      currentSpanMetrics,
      currentSpan.data.span_attributes?.remote,
      hasError,
      spanType,
    ],
  );

  const isSelected = selectedSpanId === currentSpan.id;
  const isLastChild = childIndex === totalSiblings - 1;

  const start = currentSpanMetrics?.start;
  const end = currentSpanMetrics?.end;
  const duration = !isEmpty(start) && !isEmpty(end) ? end - start : undefined;

  const offset = !isEmpty(start) && !isEmpty(traceStart) && start - traceStart;

  const offsetPercent =
    offset !== false &&
    !isEmpty(totalDuration) &&
    (offset / totalDuration) * 100 + "%";

  const durationPercent =
    !isEmpty(duration) &&
    !isEmpty(totalDuration) &&
    (duration / totalDuration) * 100 + "%";

  const iconComponent = (
    <div
      className={cn(
        "relative flex size-5 flex-none items-center justify-center rounded-sm",
        iconClassName,
        {
          "border border-bad-200 bg-bad-100 text-bad-500 dark:border-bad-100 dark:bg-bad-50 dark:text-bad-500":
            hasError,
        },
      )}
    >
      {isEmpty(end) && !isEmpty(start) ? (
        <Spinner className="size-[10px]" />
      ) : (
        <Icon strokeWidth={2.5} className="size-3" />
      )}
    </div>
  );

  const score =
    spanType === SpanTypeAttribute.SCORE
      ? (() => {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          return Object.values(
            getDiffRight(currentSpan.data.scores) ?? {},
          )[0] as number;
        })()
      : undefined;

  const wasScorerSkipped =
    type === SpanTypeAttribute.SCORE &&
    !isEmpty(end) &&
    Object.keys(currentSpan.data.scores).length > 0 &&
    Object.values(currentSpan.data.scores).some((v) => v === null);
  const spanTooltip = (
    <span className="flex flex-col gap-1">
      <div className="flex items-baseline gap-2 text-sm font-medium">
        {iconComponent}
        <span className="grow">{name}</span>
        <span className="text-[9px] text-primary-500 uppercase">{type}</span>
      </div>
      {tooltip}
      {wasScorerSkipped && <div>This scorer returned a null value.</div>}
      {currentSpan.data.metrics && (
        <SpanMetricsSummary
          metrics={currentSpan.data.metrics}
          comparisonClassName={comparisonClassName}
          expanded
        />
      )}
    </span>
  );

  return (
    <BasicTooltip side="left" align="start" tooltipContent={spanTooltip}>
      <div
        className={cn(
          "group relative flex h-8 cursor-pointer items-center rounded-md border border-background bg-primary-50 pr-2.5",
          {
            "h-10": showMetrics && !isTimelineView,
            "bg-primary-200/80": isSelected,
            "hover:bg-primary-100": !isSelected,
            "bg-transparent": isTimelineView && !isSelected,
          },
        )}
        style={{ margin: "0 2px" }}
        onClick={() => {
          if (isTimelineView) {
            setSelectedSpan(currentSpan);
            setViewType("trace");
            return;
          }
          setSelectedSpan(currentSpan);
        }}
      >
        {Boolean(durationPercent) && isTimelineView && (
          <div className="absolute inset-x-0 top-0.5 bottom-0 left-64 px-3 pb-1.5">
            <div
              className={cn(
                "relative border-primary-300",
                lightClassName,
                borderClassName,
                "h-6 cursor-pointer rounded-[4px] border transition-opacity hover:opacity-80",
              )}
              style={{
                marginLeft: offsetPercent || 0,
                width: durationPercent || 0,
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedSpan(currentSpan);
                setViewType("trace");
              }}
            />
          </div>
        )}
        {level > 0 && (
          <>
            <div
              className={cn(
                "absolute -top-0.5 left-3 rounded-bl border border-t-0 border-r-0 border-primary-300",
              )}
              style={{
                height: isTimelineView ? 16 : showMetrics ? 22 : 18,
                width: hasChildren ? 14 : 18,
                transform: `translateX(${(level - 1) * 12 + 3.5}px)`,
              }}
            />
          </>
        )}
        {Array.from({ length: level + (hasChildren ? 1 : 0) }).map((_, i) => {
          const isLast = i === level + (hasChildren ? 1 : 0) - 1;
          const shouldHideForFinishedLevel = finishedLevels.has(i + 1);

          return (
            <div
              key={i}
              className={cn(
                "absolute -top-0.5 left-2 border-l border-primary-300",
                {
                  hidden:
                    shouldHideForFinishedLevel ||
                    (isLast &&
                      ((hasChildren && isCollapsed) || isLastChild) &&
                      !(isLastChild && hasChildren && !isCollapsed)),
                  "top-3": isLast && hasChildren,
                },
              )}
              style={{
                height: isTimelineView ? 32 : 48,
                transform: `translateX(${(i + 1) * 12 - 4.5}px)`,
              }}
            />
          );
        })}

        <div
          className={cn("z-10 flex items-center truncate", {
            "w-64": isTimelineView,
            "flex-1": !isTimelineView,
          })}
          style={{ paddingLeft: `calc(${level * 12}px + 12px)` }}
        >
          {iconComponent}
          <span className="flex flex-1 flex-col truncate pl-1.5 @xs:flex-row @xs:gap-2">
            <span className="flex items-center gap-1 truncate">
              {modelConfig?.ModelIcon && (
                <span className="flex-none text-primary-600">
                  <modelConfig.ModelIcon size={16} />
                </span>
              )}
              <span
                className={cn(
                  "flex-1 truncate text-xs font-medium text-primary-700",
                )}
              >
                {name}
                {!isEmpty(score) && (
                  <span className="pl-1 text-xs text-primary-500">
                    {`${(score * 100).toLocaleString(undefined, {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 2,
                    })}%`}
                  </span>
                )}
              </span>
            </span>
            {showMetrics && currentSpan.data.metrics && (
              <span
                className={cn(
                  "z-10 flex-none truncate text-[11px] text-primary-500",
                  {
                    "pointer-events-none absolute right-4 pr-1.5 text-primary-800":
                      isTimelineView,
                  },
                )}
              >
                <SpanMetricsSummary
                  metrics={currentSpan.data.metrics}
                  comparisonClassName={comparisonClassName}
                />
              </span>
            )}
          </span>
          {!hideCollapseButton && hasChildren && (
            <button
              className={cn(
                "flex size-4 shrink-0 items-center justify-center rounded-sm bg-transparent hover:bg-primary-200",
                {
                  "translate-y-px": showMetrics && !isTimelineView,
                  "hover:bg-primary-300": isSelected,
                },
              )}
              onClick={handleToggleCollapsed}
            >
              <ChevronRight
                className={cn("size-3 transition-transform", {
                  "rotate-90": !isCollapsed,
                })}
              />
            </button>
          )}
        </div>
      </div>
    </BasicTooltip>
  );
};

function roundDecimals(num: number, decimals: number) {
  return Math.round(num * 10 ** decimals) / 10 ** decimals;
}

export function SpanMetricsSummary({
  metrics,
  comparisonClassName,
  expanded,
}: {
  metrics: SpanMetrics | DiffObjectType<SpanMetrics>;
  comparisonClassName: string;
  expanded?: boolean;
}) {
  if (isDiffObject(metrics)) {
    const leftMetrics = metrics[DiffLeftField];
    const rightMetrics = metrics[DiffRightField];

    const rightTime =
      !isEmpty(rightMetrics?.start) && !isEmpty(rightMetrics?.end)
        ? rightMetrics.end - rightMetrics.start
        : 0;
    const leftTime =
      !isEmpty(leftMetrics?.start) && !isEmpty(leftMetrics?.end)
        ? leftMetrics.end - leftMetrics.start
        : rightTime;

    return (
      <div>
        <DiffNumbers
          oldNumber={roundDecimals(leftTime, 2)}
          newNumber={roundDecimals(rightTime, 2)}
          oldScoreClassName={comparisonClassName}
          formatNumber={(n) =>
            `${n.toLocaleString(undefined, {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })}s`
          }
          percentDiff={(start, end) => (end - start) / start}
          upIsGood={false}
          hideChanged
        />
      </div>
    );
  } else {
    const components: { label: string; value: string }[] = [];
    if (!isEmpty(metrics.start)) {
      if (!isEmpty(metrics.end)) {
        components.push({
          label: "Duration",
          value: `${(metrics.end - metrics.start).toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          })}s`,
        });
      } else {
        components.push({ label: "Duration", value: "in progress" });
      }
    }
    if (metrics.tokens) {
      components.push({
        label: "Total tokens",
        value: `${metrics.tokens} tok`,
      });
    }
    if (metrics.time_to_first_token && expanded) {
      components.push({
        label: "Time to first token",
        value: `${metrics.time_to_first_token.toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        })}s`,
      });
    }
    if (metrics.estimated_cost && expanded) {
      components.push({
        label: "Estimated LLM cost",
        value: formatPriceValue(metrics.estimated_cost),
      });
    }
    if (metrics.retries && expanded) {
      components.push({
        label: "Retries",
        value: `${metrics.retries}`,
      });
    }

    if (expanded) {
      return (
        <>
          {components.map((c) => (
            <span key={c.label} className="flex flex-col gap-0.5">
              <span className="text-primary-500">{c.label}</span>
              <span>{c.value}</span>
            </span>
          ))}
        </>
      );
    }
    return <span>{components.map((c) => c.value).join(", ")}</span>;
  }
}
