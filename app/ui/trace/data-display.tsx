"use client";

import {
  deserializePlainStringAsJSON,
  isEmpty,
  safeDeserializeUnknown,
} from "#/utils/object";
import { isArray, isObject, TRANSACTION_ID_FIELD } from "braintrust/util";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
  useState,
} from "react";
import { type UpdateValueFn } from "#/ui/arrow-table";
import { type Span } from "./graph";
import {
  Diff<PERSON>eftField,
  DiffRightField,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import {
  type DataEditorCopilotContextFn,
  type RenderOptionProps,
  UpdateableDataTextEditor,
} from "#/ui/data-text-editor";
import { Download, Eye, Paperclip } from "lucide-react";
import { useEntityStorage } from "#/lib/clientDataStorage";
import prettyBytes from "pretty-bytes";
import { downloadBlob } from "#/utils/download";
import { But<PERSON> } from "#/ui/button";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { type CopilotContextFormat } from "@braintrust/local/copilot";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { useTraceFullscreen } from "./use-trace-fullscreen";
import { type RenderOption } from "#/utils/parse";
import { z } from "zod";
import { type UpdateRowFn } from "#/utils/mutable-object";
import { type TraceViewParams } from "./trace";
import { stringify } from "#/utils/string";
import { FileInputButton } from "#/ui/file-input-button";
import { isAttachmentReference } from "#/ui/attachment/use-extracted-attachment";
import { BasicTooltip } from "#/ui/tooltip";
import { uploadAttachment } from "#/utils/btapi/attachment";
import { toast } from "sonner";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { type AttachmentReference } from "@braintrust/typespecs";
import { useFeatureFlags } from "#/lib/feature-flags";
import { normalizeToKnownFormat } from "#/ui/trace/format-normalizer";
import { openAIResponseNormalizer } from "#/ui/trace/converters/openai-response-converter";
import { aiSDKResponseNormalizer } from "#/ui/trace/converters/ai-sdk-response-converter";
import { parseLLMSpanPart } from "#/ui/LLMView";

const toJSONSchema = z.object({
  toJSON: z.function(),
});

const parseField = (field: unknown) => {
  const parsed = toJSONSchema.safeParse(field);
  const val = parsed.success ? JSON.stringify(parsed.data.toJSON()) : field;
  return typeof val === "string" || typeof val === "number" ? `${val}` : null;
};

export const dataDisplayedFieldSchema = z.union([
  z.literal("metrics"),
  z.literal("input"),
  z.literal("output"),
  z.literal("expected"),
  z.literal("error"),
  z.literal("metadata"),
]);

export type DataDisplayedField = z.infer<typeof dataDisplayedFieldSchema>;

export interface DataDisplayProps {
  span: Span;
  fieldName: DataDisplayedField | string;
  updateRow: UpdateRowFn | undefined;
  experimentNames?: TraceViewParams["experimentNames"];
  onApplySearch?: ApplySearch;
  searchQuery?: string;
  copilotContext: CopilotContextBuilder;
  foldStateId?: string;
  comparisonClassName?: string;
  extraActions?: React.ReactNode;
  isObjectOnly?: boolean; // Value must be an object
  diffMessage?: string;
  parseData?: (value: string) => string | null;
  validateFn?: (value: unknown) => void;
}

export function DataDisplay({
  localStorageKey,
  ...props
}: DataDisplayProps & { localStorageKey?: string }) {
  if (localStorageKey) {
    return <WithLocalStorage {...props} localStorageKey={localStorageKey} />;
  } else {
    return <WithoutLocalStorage {...props} />;
  }
}

const WithLocalStorage = (
  props: DataDisplayProps & { localStorageKey: string },
) => {
  const [renderMode, setRenderMode] = useEntityStorage({
    entityType: "editorMode",
    entityIdentifier: props.localStorageKey,
    key: "mode",
  });
  return (
    <DataDisplayContent
      {...props}
      renderOption={renderMode ?? undefined}
      setRenderOption={setRenderMode}
    />
  );
};

const WithoutLocalStorage = (props: DataDisplayProps) => {
  const [renderOption, setRenderOption] = useState<RenderOption | undefined>(
    undefined,
  );

  return (
    <DataDisplayContent
      {...props}
      renderOption={renderOption}
      setRenderOption={setRenderOption}
    />
  );
};

const ONE_MB = 1024 * 1024;

function DataDisplayContent({
  span,
  fieldName,
  updateRow,
  experimentNames,
  onApplySearch,
  renderOption,
  setRenderOption,
  searchQuery,
  copilotContext,
  foldStateId,
  comparisonClassName,
  localStorageKey,
  extraActions,
  isObjectOnly,
  diffMessage,
  parseData,
  validateFn,
}: DataDisplayProps & {
  localStorageKey?: string;
  renderOption: RenderOption | undefined;
  setRenderOption: Dispatch<SetStateAction<RenderOption | undefined>>;
}) {
  const { isTraceFullscreen } = useTraceFullscreen();
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const { flags } = useFeatureFlags();

  const [showLargeData, setShowLargeData] = useState(false);
  const wrappedUpdateRow: UpdateValueFn = useCallback(
    (value) =>
      updateRow?.(span.data, [fieldName], value) ?? Promise.resolve(null),
    [fieldName, span.data, updateRow],
  );

  const makeFieldAwareCopilotContext = useMemo(() => {
    if (!copilotContext) {
      return undefined;
    }
    return (fmt: CopilotContextFormat) =>
      copilotContext.makeCopilotContext({
        type: "row",
        field: fieldName,
        fmt,
        // These are filled in by the implementation, which rely on visitSpan
        // to know the latest (saved) value of the field.
        objectName: null,
        objectType: null,
        row: null,
      });
  }, [fieldName, copilotContext]);

  const normalizedData = useMemo(() => {
    // Only compute normalized data for input, output, and metadata fields
    if (!["input", "output", "metadata"].includes(fieldName)) {
      return null;
    }

    const inputString =
      typeof span.data.input === "string" ? span.data.input : "{}";
    const outputString =
      typeof span.data.output === "string" ? span.data.output : "{}";
    const metadataString =
      typeof span.data.metadata === "string" ? span.data.metadata : undefined;
    const spanAttributes = span.data.span_attributes;

    const inputValue = deserializePlainStringAsJSON(inputString).value;
    const outputValue = deserializePlainStringAsJSON(outputString).value;

    const normalized = normalizeToKnownFormat(
      inputValue,
      outputValue,
      metadataString,
      spanAttributes,
      [aiSDKResponseNormalizer, openAIResponseNormalizer],
      { cacheKey: `${span.data.id}-${fieldName}` },
    );

    // Get the normalized value for the specific field
    let normalizedFieldValue = null;
    if (fieldName === "input") normalizedFieldValue = normalized.input;
    if (fieldName === "output") normalizedFieldValue = normalized.output;
    if (fieldName === "metadata") normalizedFieldValue = normalized.metadata;

    // Only return the normalized value if it can be parsed as LLM messages
    if (normalizedFieldValue && parseLLMSpanPart(normalizedFieldValue)) {
      return normalizedFieldValue;
    }

    return null;
  }, [span.data, fieldName]);

  if (
    span.data[fieldName] &&
    span.data[fieldName].length > ONE_MB &&
    !showLargeData
  ) {
    return (
      <TooLarge
        data={span.data[fieldName]}
        fieldName={fieldName}
        setShowLargeData={setShowLargeData}
      >
        Data is too large to display ({prettyBytes(span.data[fieldName].length)}
        )
      </TooLarge>
    );
  }

  if (isDiffObject(span.data[fieldName])) {
    const leftField = parseField(span.data[fieldName][DiffLeftField]);
    const rightField = parseField(span.data[fieldName][DiffRightField]);
    if (isTraceFullscreen) {
      // Side by side renderers in fullscreen
      return (
        <div className="flex gap-4">
          <div className="flex-1">
            <MemoizedUpdateableDataTextEditor
              value={leftField}
              searchQuery={searchQuery}
              rowId={span.data.id}
              xactId={span.data[TRANSACTION_ID_FIELD] ?? null}
              selectedRenderOption={renderOption}
              setSelectedRenderOption={setRenderOption}
              selectedLineId={`${span.data.id}-${fieldName}`}
              foldStateId={foldStateId}
              isReadOnly
              colorSwatchClassName={comparisonClassName}
              attachmentsLocalStorageKey={localStorageKey + "-attachments"}
              experimentNames={experimentNames}
              diffMessage={diffMessage}
            />
          </div>
          <div className="flex-1">
            <MemoizedUpdateableDataTextEditor
              value={rightField}
              searchQuery={searchQuery}
              updateValue={updateRow ? wrappedUpdateRow : undefined}
              rowId={span.data.id}
              xactId={span.data[TRANSACTION_ID_FIELD] ?? null}
              selectedRenderOption={renderOption}
              setSelectedRenderOption={setRenderOption}
              onApplySearch={onApplySearch}
              makeCopilotContext={makeFieldAwareCopilotContext}
              selectedLineId={`${span.data.id}-${fieldName}`}
              foldStateId={foldStateId}
              colorSwatchClassName="bg-primary-500"
              isReadOnly
              attachmentsLocalStorageKey={localStorageKey + "-attachments"}
              experimentNames={experimentNames}
              diffMessage={diffMessage}
            />
          </div>
        </div>
      );
    }

    return (
      <DiffDataDisplay
        searchQuery={searchQuery}
        rowId={span.data.id}
        xactId={span.data[TRANSACTION_ID_FIELD] ?? null}
        leftData={leftField}
        rightData={rightField}
        selectedRenderOption={renderOption}
        setSelectedRenderOption={setRenderOption}
        attachmentsLocalStorageKey={localStorageKey + "-attachments"}
        extraActions={extraActions}
        experimentNames={experimentNames}
        diffMessage={diffMessage}
      />
    );
  }

  return (
    <MemoizedUpdateableDataTextEditor
      value={
        !isEmpty(span.data[fieldName])
          ? (stringify(span.data[fieldName]) ?? null)
          : null
      }
      normalizedLLMValue={normalizedData}
      searchQuery={searchQuery}
      updateValue={updateRow ? wrappedUpdateRow : undefined}
      rowId={span.data.id}
      xactId={span.data[TRANSACTION_ID_FIELD] ?? null}
      selectedRenderOption={renderOption}
      setSelectedRenderOption={setRenderOption}
      onApplySearch={onApplySearch}
      makeCopilotContext={makeFieldAwareCopilotContext}
      selectedLineId={`${span.data.id}-${fieldName}`}
      foldStateId={foldStateId}
      attachmentsLocalStorageKey={localStorageKey + "-attachments"}
      extraActions={
        updateRow &&
        flags.attachmentsInMessages && (
          <>
            <BasicTooltip tooltipContent="Attach a file">
              <FileInputButton
                variant="ghost"
                size="xs"
                Icon={Paperclip}
                className="text-primary-400"
                onChange={async (file) => {
                  if (!file) {
                    toast.warning("No file selected");
                    return;
                  }
                  toast.promise(
                    async () => {
                      const token = await getOrRefreshToken();
                      const reference = await uploadAttachment({
                        file,
                        sessionToken: token,
                        org: org,
                      });
                      wrappedUpdateRow(
                        appendAttachment(span.data[fieldName], reference),
                      );
                    },
                    {
                      loading: "Uploading file",
                      success: "File uploaded",
                      error: "Failed to upload file",
                    },
                  );
                }}
              />
            </BasicTooltip>
            {extraActions}
          </>
        )
      }
      allowedRenderOptions={isObjectOnly ? ["json", "tree", "yaml"] : undefined}
      experimentNames={experimentNames}
      parseData={parseData}
      validateFn={validateFn}
    />
  );
}

export function TooLarge({
  children,
  data,
  fieldName,
  setShowLargeData,
}: {
  children: React.ReactNode;
  data?: string;
  fieldName?: string;
  setShowLargeData?: Dispatch<SetStateAction<boolean>>;
}) {
  return (
    <div className="flex flex-col items-center justify-center gap-1.5 rounded-md border border-primary-100 bg-primary-50 px-4 py-6 text-center text-sm text-primary-500">
      <div>{children}</div>
      <div className="flex flex-row gap-2">
        {data && fieldName && (
          <Button
            size="xs"
            onClick={() => downloadBlob(new Blob([data]), `${fieldName}.json`)}
          >
            <Download className="size-3" />
            Download as JSON
          </Button>
        )}
        {setShowLargeData && (
          <Button
            size="xs"
            variant="ghost"
            onClick={() => setShowLargeData(true)}
            Icon={Eye}
          >
            Show it anyway
          </Button>
        )}
      </div>
    </div>
  );
}

export function DiffDataDisplay({
  rowId,
  xactId,
  leftData,
  rightData,
  selectedRenderOption,
  setSelectedRenderOption,
  searchQuery,
  makeCopilotContext,
  attachmentsLocalStorageKey,
  extraActions,
  experimentNames,
  diffMessage,
}: {
  rowId: string;
  xactId: string | null;
  leftData: string | null;
  rightData: string | null;
  searchQuery?: string;
  makeCopilotContext?: DataEditorCopilotContextFn;
  attachmentsLocalStorageKey?: string;
  extraActions?: React.ReactNode;
  experimentNames?: TraceViewParams["experimentNames"];
  diffMessage?: string;
} & RenderOptionProps) {
  return (
    <MemoizedUpdateableDataTextEditor
      value={rightData}
      diffValue={leftData}
      searchQuery={searchQuery}
      rowId={rowId}
      xactId={xactId}
      makeCopilotContext={makeCopilotContext}
      selectedRenderOption={selectedRenderOption}
      setSelectedRenderOption={setSelectedRenderOption}
      attachmentsLocalStorageKey={attachmentsLocalStorageKey}
      allowedRenderOptions={leftData ? ["text", "yaml", "json"] : undefined}
      extraActions={extraActions}
      experimentNames={experimentNames}
      diffMessage={diffMessage}
    />
  );
}

function MemoizedUpdateableDataTextEditor({
  value,
  normalizedLLMValue,
  diffValue,
  updateValue,
  rowId,
  xactId,
  selectedRenderOption,
  setSelectedRenderOption,
  onApplySearch,
  searchQuery,
  makeCopilotContext,
  selectedLineId,
  foldStateId,
  isReadOnly,
  colorSwatchClassName,
  attachmentsLocalStorageKey,
  allowedRenderOptions,
  extraActions,
  experimentNames,
  diffMessage,
  parseData,
  validateFn,
}: {
  value: string | null;
  normalizedLLMValue?: unknown;
  diffValue?: string | null;
  updateValue?: UpdateValueFn;
  rowId: string;
  searchQuery?: string;
  xactId: string | null;
  makeCopilotContext?: DataEditorCopilotContextFn;
  onApplySearch?: ApplySearch;
  selectedLineId?: string;
  foldStateId?: string;
  isReadOnly?: boolean;
  colorSwatchClassName?: string;
  attachmentsLocalStorageKey?: string;
  allowedRenderOptions?: RenderOption[];
  extraActions?: React.ReactNode;
  experimentNames?: TraceViewParams["experimentNames"];
  diffMessage?: string;
  parseData?: (value: string) => string | null;
  validateFn?: (value: unknown) => void;
} & RenderOptionProps) {
  const { valueJSON, diffValueJSON } = useMemo(() => {
    const parsedValue = parseData && value ? parseData(value) : value;
    return {
      valueJSON: parsedValue
        ? deserializePlainStringAsJSON(parsedValue).value
        : parsedValue,
      diffValueJSON: diffValue
        ? deserializePlainStringAsJSON(diffValue).value
        : diffValue,
    };
  }, [value, diffValue, parseData]);
  const baseExperimentName = experimentNames?.current ?? "base experiment";
  const comparisonExperimentName =
    experimentNames?.comparison ?? "comparison experiment";
  return (
    <UpdateableDataTextEditor
      value={valueJSON}
      normalizedLLMValue={normalizedLLMValue}
      diffValue={diffValueJSON}
      updateValue={updateValue}
      searchQuery={searchQuery}
      rowId={rowId}
      allowedRenderOptions={allowedRenderOptions}
      xactId={xactId}
      selectedRenderOption={selectedRenderOption}
      setSelectedRenderOption={setSelectedRenderOption}
      onApplySearch={onApplySearch}
      makeCopilotContext={makeCopilotContext}
      selectedLineId={selectedLineId}
      foldStateId={foldStateId}
      isReadOnly={isReadOnly}
      colorSwatchClassName={colorSwatchClassName}
      attachmentsLocalStorageKey={attachmentsLocalStorageKey}
      extraActions={extraActions}
      experimentNames={experimentNames}
      diffMessage={
        diffMessage ??
        `Showing diff from ${comparisonExperimentName} -> ${baseExperimentName}`
      }
      validateFn={validateFn}
    />
  );
}

function appendAttachment(value: unknown, attachment: AttachmentReference) {
  value = safeDeserializeUnknown(value);
  if (isEmpty(value)) {
    return attachment;
  } else if (isArray(value)) {
    return [...value, attachment];
  } else if (isAttachmentReference(value)) {
    return [value, attachment];
  } else if (isObject(value)) {
    for (let idx = 0; idx < 100000; idx++) {
      const key = idx === 0 ? "attachment" : `attachment_${idx}`;
      if (!value[key]) {
        value[key] = attachment;
        return value;
      }
    }
    throw new Error("Failed to find a key to append attachment");
  } else {
    return {
      data: value,
      attachment,
    };
  }
}
