import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { deserializeJSONStringAsString } from "#/utils/object";
import { useMemo } from "react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";

export function ErrorDisplay({ error }: { error: unknown }) {
  const errorString = useMemo(() => {
    let e = error;
    if (isDiffObject(error)) {
      e = error[DiffRightField];
    }
    if (typeof e !== "string") {
      // NOTE: This should never happen, but it's probably worth being a bit defensive.
      return JSON.stringify(e, null, 2);
    }
    return deserializeJSONStringAsString(e);
  }, [error]);
  return (
    <div className="group relative mb-4 rounded-md border border-bad-100 bg-bad-50 p-2 font-mono text-xs break-words whitespace-pre-wrap text-primary-800 dark:border-bad-100/40 dark:bg-bad-50/40">
      <CopyToClipboardButton
        textToCopy={errorString}
        size="xs"
        variant="ghost"
        className="absolute top-1 right-1 border border-primary-200 text-primary-400 opacity-0 transition-opacity group-hover:bg-primary-100 group-hover:opacity-100"
      />
      {errorString}
    </div>
  );
}
