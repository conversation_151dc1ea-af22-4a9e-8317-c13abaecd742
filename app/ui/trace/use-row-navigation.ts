import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { isEmpty } from "braintrust/util";
import { useActiveCommentId, useActiveRowAndSpan } from "#/ui/query-parameters";
import { makeRowIdPrimary, type RowId } from "#/utils/diffs/diff-objects";
import { useTransitionWithDelay } from "#/utils/use-transition-with-delay";
import { makeRowIdPrimaryOrigin } from "#/ui/table/use-active-row-effects";
import { useQueryClient } from "@tanstack/react-query";
import {
  makeTraceQueryKey,
  type TraceQueryResult,
} from "./loading/query-utils";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useCallback } from "react";

type RowNavigationFnOptions = {
  withTransition?: boolean;
  preserveSpanName?: string | null;
};

export type RowNavigationFn = (
  opts?: RowNavigationFnOptions,
) => Promise<unknown>;

export function useRowNavigation({
  rowIds,
  objectType,
  objectId,
}: {
  rowIds: RowId[];
  objectType: DataObjectType;
  objectId: string | null;
}) {
  const [{ r: activeRowId }, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_, setFocusedCommentId] = useActiveCommentId();

  const rowIdx = isEmpty(activeRowId)
    ? -1
    : rowIds.findIndex(
        (r) =>
          makeRowIdPrimaryOrigin(r) === makeRowIdPrimaryOrigin(activeRowId),
      );

  const {
    isDelayedPending: isDelayedSpanChangeTransitioning,
    startTransition: startSpanChangeTransition,
  } = useTransitionWithDelay(100);

  const hasPrevRow = rowIdx > 0;
  const hasNextRow = rowIdx < rowIds.length - 1;

  const queryClient = useQueryClient();
  const goToRow = useCallback(
    async (rowId: RowId, opts?: RowNavigationFnOptions) => {
      if (!rowId) {
        return;
      }
      const traceData = queryClient.getQueryData<TraceQueryResult | undefined>(
        makeTraceQueryKey({
          objectType,
          objectId,
          traceRowId: makeRowIdPrimary(rowId),
        }),
      );
      // somewhat of a hack to preserve the span name while navigating in human review
      // once we ship span table view, we won't need this anymore
      const matchingSpan = traceData?.queryData.data?.find((span) => {
        return opts?.preserveSpanName === span.span_attributes?.name;
      });
      if (opts?.withTransition) {
        startSpanChangeTransition(() => {
          setActiveRowAndSpan({ r: rowId, s: matchingSpan?.span_id ?? null });
          setFocusedCommentId(null);
        });
      } else {
        await setActiveRowAndSpan({
          r: rowId,
          s: matchingSpan?.span_id ?? null,
        });
        await setFocusedCommentId(null);
      }
    },
    [
      queryClient,
      objectType,
      objectId,
      startSpanChangeTransition,
      setActiveRowAndSpan,
      setFocusedCommentId,
    ],
  );

  const onPrevRow = useDebouncedCallback(
    async (opts?: RowNavigationFnOptions) => {
      if (document.getElementById("nested-dropdown-dataset")) {
        return;
      }
      const rowId = rowIds[rowIdx - 1] ?? null;
      goToRow(rowId, opts);
    },
    10,
  );

  const onNextRow = useDebouncedCallback(
    async (opts?: RowNavigationFnOptions) => {
      if (document.getElementById("nested-dropdown-dataset")) {
        return;
      }
      const rowId = rowIds[rowIdx + 1] ?? null;
      goToRow(rowId, opts);
    },
    10,
  );

  const jumpToRow = useDebouncedCallback((rowIndexToJumpTo: number) => {
    if (rowIndexToJumpTo >= 1 && rowIndexToJumpTo <= rowIds.length) {
      setActiveRowAndSpan({ r: rowIds[rowIndexToJumpTo - 1] ?? null, s: null });
    }
    setFocusedCommentId(null);
  }, 10);

  return {
    hasPrevRow,
    hasNextRow,
    onPrevRow,
    onNextRow,
    jumpToRow,
    isDelayedSpanChangeTransitioning,
    startSpanChangeTransition,
    rowIdx,
    activeRowId,
  };
}
