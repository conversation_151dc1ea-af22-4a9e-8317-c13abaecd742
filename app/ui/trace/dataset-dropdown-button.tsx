import { useRef } from "react";
import { type Span } from "@braintrust/local";
import { useSpanPromptMeta } from "./use-span-prompt-meta";
import { parsePromptFromSpan } from "@braintrust/local";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { useBtqlFlags } from "#/lib/feature-flags";
import { type ProjectContextDataset } from "#/app/app/[org]/p/[project]/project-actions";
import { DatasetActionButton } from "./dataset-action-button";
import {
  createScorerFormatOption,
  createPlaygroundFormatOption,
} from "./span-formatters";

type TraceDatasetDropdownButtonProps = {
  span: Span;
  projectId: string;
  orgDatasets: ProjectContextDataset[];
  performAddRowsToDataset: (params: {
    datasetName: string;
    datasetId: string;
    spans: Span[];
    selectedProjectId: string;
    selectedProjectName: string;
  }) => void;
  openCreateDatasetDialog: (
    name: string,
    getRows?: () => Promise<Span[]>,
  ) => void;
  spanTitleRef: React.RefObject<HTMLElement | null>;
};

export const TraceDatasetDropdownButton = ({
  span,
  projectId,
  orgDatasets,
  performAddRowsToDataset,
  openCreateDatasetDialog,
  spanTitleRef,
}: TraceDatasetDropdownButtonProps) => {
  const parsedPrompt = parsePromptFromSpan(span) ?? undefined;
  const isLLMSpan = !!parsedPrompt && parsedPrompt.success;

  const spanPromptMeta = useSpanPromptMeta({
    span,
    projectId,
    isLLMSpan,
  });

  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const abortController = useRef<AbortController>(new AbortController());
  const { getOrRefreshToken } = useSessionToken();

  const formatOptions = [
    createScorerFormatOption(),
    // eslint-disable-next-line react-compiler/react-compiler -- react-compiler is confused: we eventually access the ref in a callback which is called outside of render
    createPlaygroundFormatOption({
      spanPromptMeta,
      btqlFlags,
      apiUrl: org.api_url,
      getOrRefreshToken,
      abortController,
      shouldShow:
        isLLMSpan && !!spanPromptMeta?.id && spanPromptMeta.id !== null,
    }),
  ];

  return (
    <DatasetActionButton
      orgDatasets={orgDatasets}
      getSpans={() => [span]}
      performAddRowsToDataset={performAddRowsToDataset}
      openCreateDatasetDialog={openCreateDatasetDialog}
      buttonText="Add span to dataset"
      formatOptions={formatOptions}
      scrollTargetRef={spanTitleRef}
      enableHotkeys={true}
    />
  );
};
