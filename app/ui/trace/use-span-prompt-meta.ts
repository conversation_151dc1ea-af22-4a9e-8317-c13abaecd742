import { type Span } from "@braintrust/local";
import { isObject } from "#/utils/object";
import { z } from "zod";
import { useMemo } from "react";

const promptMetaSchema = z.object({
  project_id: z.string(),
  id: z.string(),
  prompt_session_id: z.string().optional(),
  version: z.string().optional(),
  variables: z.unknown().optional(),
});

export type PromptMeta = z.infer<typeof promptMetaSchema>;

export function useSpanPromptMeta({
  isLLMSpan,
  span,
  projectId,
}: {
  isLLMSpan: boolean;
  span: Span;
  projectId: string | null;
}) {
  return useMemo(() => {
    if (!isLLMSpan) {
      return null;
    }

    try {
      const metadata = span.data.metadata
        ? typeof span.data.metadata === "string"
          ? JSON.parse(span.data.metadata)
          : span.data.metadata
        : {};
      const { prompt: promptArg } = metadata;
      const prompt = promptMetaSchema.safeParse(promptArg);
      // Saved prompt
      return prompt.success && !prompt.data.id.includes("inline")
        ? {
            id: prompt.data.id,
            prompt_session_id: prompt.data.prompt_session_id,
            project_id: prompt.data.project_id,
            version: prompt.data.version,
            variables: isObject(prompt.data.variables)
              ? prompt.data.variables
              : undefined,
          }
        : // Inline prompt
          {
            id: null,
            prompt_session_id: null,
            project_id: projectId,
            version: null,
            variables:
              isObject(promptArg) &&
              "variables" in promptArg &&
              isObject(promptArg.variables)
                ? promptArg.variables
                : undefined,
          };
    } catch (e) {
      console.warn("Failed to parse prompt", e);
      return null;
    }
  }, [projectId, span.data.metadata, isLLMSpan]);
}
