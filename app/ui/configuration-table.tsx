import * as React from "react";

import { cn } from "#/utils/classnames";
import {
  Table,
  TableBody,
  TableCaption,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";

// Re-export all the basic table components
const ConfigurationTable = Table;
const ConfigurationTableHeader = TableHeader;
const ConfigurationTableBody = TableBody;
const ConfigurationTableFooter = TableFooter;
const ConfigurationTableRow = TableRow;
const ConfigurationTableHead = TableHead;
const ConfigurationTableCaption = TableCaption;

const ConfigurationTableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, children, ...props }, ref) => (
  <td
    ref={ref}
    className={cn(
      "flex w-28 flex-none items-center pr-4 [&:has([role=checkbox])]:pr-0",
      className,
    )}
    {...props}
  >
    {/* Different from basic table: automatically truncate string/number content */}
    {typeof children === "string" || typeof children === "number" ? (
      <span className={cn("min-w-0 flex-1 truncate")}>{children}</span>
    ) : (
      children
    )}
  </td>
));
ConfigurationTableCell.displayName = "ConfigurationTableCell";

export {
  ConfigurationTable,
  ConfigurationTableHeader,
  ConfigurationTableBody,
  ConfigurationTableFooter,
  ConfigurationTableHead,
  ConfigurationTableRow,
  ConfigurationTableCell,
  ConfigurationTableCaption,
};
