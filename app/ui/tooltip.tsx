"use client";

import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import * as React from "react";

import { cn } from "#/utils/classnames";

const TooltipProvider = TooltipPrimitive.Provider;

const Tooltip = TooltipPrimitive.Root;

const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-auto rounded-md bg-primary-50 ring-1 ring-primary-300/40",
      "px-3 py-1.5 text-sm shadow-md animate-in fade-in-0 zoom-in-95",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0",
      "data-[side=bottom]:slide-in-from-top-2 data-[state=closed]:zoom-out-95",
      "data-[side=left]:slide-in-from-right-2",
      "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className,
    )}
    collisionPadding={8}
    style={{ maxHeight: "var(--radix-tooltip-content-available-height	)" }}
    {...props}
  />
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

const TooltipPortal = TooltipPrimitive.Portal;

// an intentionally opinionated tooltip component. In cases where you
// need something more bespoke, use the tooltip primitives instead of
// adding too many configuration options here.
const BasicTooltip = React.forwardRef<
  React.ElementRef<typeof TooltipContent>,
  React.ComponentPropsWithoutRef<typeof TooltipContent> & {
    tooltipContent?: React.ReactNode;
    disableHoverableContent?: boolean;
    delayDuration?: number;
    container?: Element | DocumentFragment | null;
  }
>(
  (
    {
      className,
      tooltipContent,
      children,
      disableHoverableContent = true,
      delayDuration,
      container,
      ...contentProps
    },
    ref,
  ) => (
    <Tooltip
      disableHoverableContent={disableHoverableContent}
      delayDuration={delayDuration}
    >
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipPortal container={container}>
        {tooltipContent && (
          <TooltipContent
            ref={ref}
            className={cn(
              "max-w-sm text-xs",
              {
                "pointer-events-auto": !disableHoverableContent,
              },
              className,
            )}
            {...contentProps}
          >
            {tooltipContent}
          </TooltipContent>
        )}
      </TooltipPortal>
    </Tooltip>
  ),
);
BasicTooltip.displayName = "BasicTooltip";

export {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipTrigger,
  BasicTooltip,
};
