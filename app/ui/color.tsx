// stroke for lines, fill for circles, bg/border/text for any traditional html elements

import { cn } from "#/utils/classnames";

enum ColorName {
  BLUE = "blue",
  ORANGE = "orange",
  LIME = "lime",
  PINK = "pink",
  YELLOW = "yellow",
  SKY = "sky",
  FUCHSIA = "fuchsia",
  CYAN = "cyan",
  AMBER = "amber",
  PURPLE = "purple",
  TEAL = "teal",
  VIOLET = "violet",
  RED = "red",
  INDIGO = "indigo",
  EMERALD = "emerald",
  GREEN = "green",
}

const colorConfig: Record<
  ColorName,
  {
    stroke: string;
    fill: string;
    bg: string;
    border: string;
    text: string;
  }
> = {
  [ColorName.BLUE]: {
    stroke: "stroke-blue-500",
    fill: "fill-blue-500",
    bg: "bg-blue-500",
    border: "border-blue-500",
    text: "text-blue-700 dark:text-blue-200",
  },
  [ColorName.ORANGE]: {
    stroke: "stroke-orange-500",
    fill: "fill-orange-500",
    bg: "bg-orange-500",
    border: "border-orange-500",
    text: "text-orange-700 dark:text-orange-200",
  },
  [ColorName.LIME]: {
    stroke: "stroke-lime-500",
    fill: "fill-lime-500",
    bg: "bg-lime-500",
    border: "border-lime-500",
    text: "text-lime-700 dark:text-lime-200",
  },
  [ColorName.PINK]: {
    stroke: "stroke-pink-500",
    fill: "fill-pink-500",
    bg: "bg-pink-500",
    border: "border-pink-500",
    text: "text-pink-700 dark:text-pink-200",
  },
  [ColorName.YELLOW]: {
    stroke: "stroke-yellow-500",
    fill: "fill-yellow-500",
    bg: "bg-yellow-500",
    border: "border-yellow-500",
    text: "text-yellow-700 dark:text-yellow-200",
  },
  [ColorName.SKY]: {
    stroke: "stroke-sky-500",
    fill: "fill-sky-500",
    bg: "bg-sky-500",
    border: "border-sky-500",
    text: "text-sky-700 dark:text-sky-200",
  },
  [ColorName.FUCHSIA]: {
    stroke: "stroke-fuchsia-500",
    fill: "fill-fuchsia-500",
    bg: "bg-fuchsia-500",
    border: "border-fuchsia-500",
    text: "text-fuchsia-700 dark:text-fuchsia-200",
  },
  [ColorName.CYAN]: {
    stroke: "stroke-cyan-500",
    fill: "fill-cyan-500",
    bg: "bg-cyan-500",
    border: "border-cyan-500",
    text: "text-cyan-700 dark:text-cyan-200",
  },
  [ColorName.AMBER]: {
    stroke: "stroke-amber-500",
    fill: "fill-amber-500",
    bg: "bg-amber-500",
    border: "border-amber-500",
    text: "text-amber-700 dark:text-amber-200",
  },
  [ColorName.PURPLE]: {
    stroke: "stroke-purple-500",
    fill: "fill-purple-500",
    bg: "bg-purple-500",
    border: "border-purple-500",
    text: "text-purple-700 dark:text-purple-200",
  },
  [ColorName.TEAL]: {
    stroke: "stroke-teal-500",
    fill: "fill-teal-500",
    bg: "bg-teal-500",
    border: "border-teal-500",
    text: "text-teal-700 dark:text-teal-200",
  },
  [ColorName.VIOLET]: {
    stroke: "stroke-violet-500",
    fill: "fill-violet-500",
    bg: "bg-violet-500",
    border: "border-violet-500",
    text: "text-violet-700 dark:text-violet-200",
  },
  [ColorName.RED]: {
    stroke: "stroke-red-500",
    fill: "fill-red-500",
    bg: "bg-red-500",
    border: "border-red-500",
    text: "text-red-700 dark:text-red-200",
  },
  [ColorName.INDIGO]: {
    stroke: "stroke-indigo-500",
    fill: "fill-indigo-500",
    bg: "bg-indigo-500",
    border: "border-indigo-500",
    text: "text-indigo-700 dark:text-indigo-200",
  },
  [ColorName.EMERALD]: {
    stroke: "stroke-emerald-500",
    fill: "fill-emerald-500",
    bg: "bg-emerald-500",
    border: "border-emerald-500",
    text: "text-emerald-700 dark:text-emerald-200",
  },
  [ColorName.GREEN]: {
    stroke: "stroke-green-500",
    fill: "fill-green-500",
    bg: "bg-green-500",
    border: "border-green-500",
    text: "text-green-700 dark:text-green-200",
  },
};

const defaultOrder = [
  ColorName.BLUE,
  ColorName.ORANGE,
  ColorName.LIME,
  ColorName.PINK,
  ColorName.YELLOW,
  ColorName.SKY,
  ColorName.FUCHSIA,
  ColorName.CYAN,
  ColorName.AMBER,
  ColorName.PURPLE,
  ColorName.TEAL,
  ColorName.VIOLET,
  ColorName.RED,
  ColorName.INDIGO,
  ColorName.EMERALD,
  ColorName.GREEN,
];

const chartColorOrder = [
  ColorName.BLUE,
  ColorName.ORANGE,
  ColorName.VIOLET,
  ColorName.PINK,
  ColorName.TEAL,
  ColorName.YELLOW,
  ColorName.CYAN,
  ColorName.GREEN,
];

// arbitrarily pick a starting color, then nextColor = (index - 6) % 17
export const COLOR_CLASSNAMES = defaultOrder.map((c) => {
  const config = colorConfig[c];
  return cn(config.stroke, config.fill, config.bg, config.border, config.text);
});

export const CHART_COLOR_CLASSNAMES = chartColorOrder.map((c) => {
  const config = colorConfig[c];
  return cn(config.stroke, config.fill, config.bg, config.border, config.text);
});

export const TIMESERIES_COLOR_CLASSNAMES = [
  "fill-blue-700 bg-blue-700 dark:fill-blue-400 dark:bg-blue-400",
  "fill-blue-300 bg-blue-300 dark:fill-blue-700 dark:bg-blue-700",
  "fill-orange-300 bg-orange-300 dark:fill-orange-900 dark:bg-orange-900",
  "fill-orange-700 bg-orange-700 dark:fill-orange-500 dark:bg-orange-500",
  "fill-teal-700 bg-teal-700 dark:fill-teal-500 dark:bg-teal-500",
  "fill-teal-300 bg-teal-300 dark:fill-teal-900 dark:bg-teal-900",
  "fill-pink-300 bg-pink-300 dark:fill-pink-900 dark:bg-pink-900",
  "fill-pink-700 bg-pink-700 dark:fill-pink-500 dark:bg-pink-500",
];

export const getColorForIndex = (
  i: number,
  values: string[] = COLOR_CLASSNAMES,
) => {
  return values[i % values.length];
};

export const EXPERIMENT_PRIMARY_COLOR_CLASSNAME =
  "stroke-primary-600 fill-primary-500 bg-primary-500 border-primary-600 text-primary-700";
// to preserve backwards compatibility with existing color ordering
export const EXPERIMENT_COMPARISON_COLOR_CLASSNAMES = [
  "stroke-comparison-500 fill-comparison-500 bg-comparison-600 border-comparison-600 text-comparison-700",
  ...COLOR_CLASSNAMES,
];

export const ALL_EXPERIMENT_COLOR_CLASSNAMES = [
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
  ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
];

export const DEFAULT_COLOR_CLASSNAME =
  "stroke-primary-500 fill-primary-500 bg-primary-500 border-primary-500 text-primary-700";

export function nextColor(
  usedColorIndexes: (number | null)[],
  values: string[] = COLOR_CLASSNAMES,
) {
  const histogram = usedColorIndexes.reduce(
    (acc, index) => {
      if (index == null) return acc;
      acc[index]++;
      return acc;
    },
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    new Array(values.length).fill(0) as number[],
  );

  const minValue = Math.min(...histogram);
  for (let i = 0; i < histogram.length; i++) {
    if (histogram[i] === minValue) return [i, values[i]] as const;
  }

  return [0, values[0]] as const;
}

export function ExperimentColorSwatch({
  index,
  children,
  className,
  swatchClassName,
}: {
  index?: number;
  className?: string;
  swatchClassName?: string;
  children?: React.ReactNode;
}) {
  if (index == null) {
    return children;
  }
  return (
    <span className={cn("flex flex-none items-center gap-1.5", className)}>
      <span
        className={cn(
          "size-1.5 flex-none rounded-full",
          swatchClassName,
          [
            EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
            ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
          ][index],
        )}
      />
      {children}
    </span>
  );
}
