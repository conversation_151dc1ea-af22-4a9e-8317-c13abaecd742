import { type PropsWithChildren, type ReactNode } from "react";
import { Button, type ButtonProps } from "./button";
import { type PopoverContentProps } from "@radix-ui/react-popover";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "./dropdown-menu";

export interface DropdownColumn {
  id: string;
  header: string;
  isVisible: boolean;
  toggleVisibility: (e: Event) => void;
}

export interface FieldDropdownProps
  extends Pick<PopoverContentProps, "align" | "side"> {
  columns: DropdownColumn[];
  /**
   * @default "Toggle All"
   */
  allPlaceholder?: string;
  /**
   * Renders icon button with tooltip.
   */
  asIcon?: ReactNode;
  buttonClassName?: string;
  buttonSize?: ButtonProps["size"];
  renderItem?: (column: DropdownColumn) => ReactNode;
  emptyState?: string | ReactNode;
}

export default function FieldDropdown({
  columns,
  align = "start",
  side,
  buttonSize,
  buttonClassName,
  renderItem,
  children,
  emptyState,
}: PropsWithChildren<FieldDropdownProps>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="border" size={buttonSize} className={buttonClassName}>
          {children}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} side={side}>
        {columns.length > 0 ? (
          <DropdownMenuGroup className="min-w-42">
            {columns.map((column) => {
              return (
                <DropdownMenuCheckboxItem
                  onSelect={(e) => {
                    e.preventDefault();
                    column.toggleVisibility(e);
                  }}
                  key={column.id}
                  checked={!!column.isVisible}
                >
                  {renderItem?.(column) ?? column.header}
                </DropdownMenuCheckboxItem>
              );
            })}
          </DropdownMenuGroup>
        ) : (
          emptyState
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
