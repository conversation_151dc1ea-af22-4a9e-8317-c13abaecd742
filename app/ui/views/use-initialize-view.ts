import { useEffect, useRef } from "react";
import { parseAsString, useQueryState } from "nuqs";
import {
  type View,
  type ViewParams,
  useViewQuery,
} from "#/utils/view/use-view";
import { useEntityStorage } from "#/lib/clientDataStorage";

export function useInitializeView({
  pageIdentifier,
  viewParams,
  onViewInitialized,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  onViewInitialized: (view: View | null) => void;
}) {
  const { data, isPending } = useViewQuery({
    viewParams,
    pageIdentifier,
  });

  const [viewNameFromQuery, setViewName] = useQueryState("v", parseAsString);
  const [defaultViewName] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "defaultView",
  });

  const hasInitialized = useRef(false);
  useEffect(() => {
    if (isPending || hasInitialized.current) {
      return;
    }
    hasInitialized.current = true;
    const name =
      viewNameFromQuery === null && defaultViewName
        ? defaultViewName
        : viewNameFromQuery;
    const selectedView = data?.find((v) => v.name === name);
    if (!selectedView?.name) {
      setViewName(null);
      onViewInitialized(null);
      return;
    }
    setViewName(selectedView.name);
    onViewInitialized(selectedView);
  }, [
    isPending,
    data,
    viewNameFromQuery,
    setViewName,
    onViewInitialized,
    defaultViewName,
  ]);

  return [viewNameFromQuery, setViewName] as const;
}
