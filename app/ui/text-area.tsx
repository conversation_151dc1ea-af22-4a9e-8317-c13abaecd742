import TextareaAutosize, {
  type TextareaAutosizeProps,
} from "react-textarea-autosize";
import { inputClassName } from "./input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { cn } from "#/utils/classnames";

export type TextAreaProps = {
  onDebouncedChange?: (value: string) => void;
  onMetaEnter?: (type: "shift" | "cmd") => void;
} & TextareaAutosizeProps &
  React.RefAttributes<HTMLTextAreaElement>;

const TextArea = ({
  onDebouncedChange,
  onMetaEnter,
  className,
  ...props
}: TextAreaProps) => {
  const onChangeDebounced = useDebouncedCallback((value: string) => {
    onDebouncedChange?.(value);
  }, 1000);

  return (
    <TextareaAutosize
      {...props}
      className={cn(inputClassName, className)}
      onKeyDown={(e) => {
        props.onKeyDown?.(e);
        if (e.key === "Enter" && onMetaEnter) {
          if (e.metaKey) {
            e.preventDefault();
            onMetaEnter("cmd");
          } else if (e.shiftKey) {
            e.preventDefault();
            onMetaEnter("shift");
          }
        }
      }}
      onChange={(e) => {
        props.onChange?.(e);
        onChangeDebounced(e.target.value);
      }}
    />
  );
};

export default TextArea;
