import { animated, useSpring } from "@react-spring/web";

type RectangleProps = React.SVGProps<SVGRectElement> & {
  width: number;
  height: number;
  x: number;
  y: number;
};

export const Rectangle = (props: RectangleProps) => {
  const { x, y, width, height, ref: _ref, ...restProps } = props;

  const springProps = useSpring({
    to: { x, y, width, height },
    config: {
      friction: 10,
      duration: 120,
    },
  });

  if (y === undefined) {
    return null;
  }

  return (
    <animated.rect
      fill="currentColor"
      {...restProps}
      style={{
        x: springProps.x,
        y: springProps.y,
        width: springProps.width,
        height: springProps.height,
      }}
    />
  );
};
