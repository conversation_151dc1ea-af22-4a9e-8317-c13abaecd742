"use client";

import { cn } from "#/utils/classnames";
import * as React from "react";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  calcPadding,
  calcPaddingStandalone,
  calcVizSize,
  X_AXIS_HEIGHT,
} from "#/ui/charts/padding/calc-padding";
import { nanoid } from "ai";
import { useResizeObserver } from "#/ui/charts/padding/use-resize-observer";
import {
  getResponsiveFeatures,
  type ResponsiveFeatures,
} from "#/ui/charts/padding/get-responsive-features";
import { VizLines } from "#/ui/charts/viz/viz-lines";
import {
  type NearestPoint,
  type TimeseriesData,
} from "#/ui/charts/timeseries-data/chart-data.types";

import { type ChartTimeFrame } from "#/app/app/[org]/monitor/time-controls/time-range";
import { getXScale, getYScale } from "./get-timeseries-scales";
import { getTimeSeriesValuesExtents } from "#/ui/charts/timeseries-data/get-timeseries-values-extents";
import { TimeCursor } from "#/ui/charts/cursor/TimeCursor";
import { LeftAxis } from "#/ui/charts/axis/LeftAxis";
import { BottomAxis } from "#/ui/charts/axis/BottomAxis";
import { isLineSelected, type HighlightStateType } from "#/ui/charts/highlight";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { MonitorChartLegend } from "#/ui/charts/timeseries-legend/timeseries-legend";
import { ChartTooltip } from "#/ui/charts/tooltip/chart-tooltip";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Tooltip, TooltipContent } from "#/ui/tooltip";

import { useAtom } from "jotai";
import {
  globalDisableInteractivityAtom,
  setGlobalTimeCursorAtom,
} from "#/ui/charts/store/atoms";
import { useBrush } from "#/ui/charts/brush";
import { SearchSlash, TriangleAlert } from "lucide-react";
import { Spinner } from "#/ui/icons/spinner";
import { VizBars } from "#/ui/charts/viz/viz-bars";
import { getTimeseriesStackedTimeBuckets } from "#/ui/charts/timeseries-data/get-timeseries-stacked-time";
import { getNearestPoint } from "./get-nearest-point";

const AXIS_GRADIENT_EDGE_PX = 6;

export type TimeseriesChartProps<DataPointMetadata, SeriesMetadata> = {
  timeseriesData: TimeseriesData<DataPointMetadata, SeriesMetadata>;
  chartTimeFrame: ChartTimeFrame;

  // todo - figure out cleaner interface for these
  highlightState: HighlightStateType;
  aggregateFormatter: (value: number) => string;
  tickFormatter?: (value: number, index: number) => string | null;
  onClick?: (
    event: React.MouseEvent,
    timestamp: number,
    seriesGroupBys?: { key: string; value: string }[],
  ) => Promise<void>;
  onDoubleClick?: () => void;
  onBrush?: (v: [number, number] | null) => void;
  clickMessage?: string;
  isFetchingData?: boolean;
  vizType: "lines" | "bars";
  tzUTC?: boolean;
  hasError?: boolean;
  overrides?: Partial<ResponsiveFeatures>;
  setVizWidth?: Dispatch<SetStateAction<number | null>>;
  standaloneLayoutMode?: boolean;
};

export const TimeseriesChart = <
  DataPointMetadata extends { count: bigint },
  SeriesMetadata extends {
    name: string;
    selectionType: SelectionType;
    groupBys?: { key: string; value: string }[];
  },
>(
  props: TimeseriesChartProps<DataPointMetadata, SeriesMetadata>,
) => {
  const {
    timeseriesData,
    chartTimeFrame,
    onClick,
    onDoubleClick,
    clickMessage,
    isFetchingData,
    highlightState,
    vizType,
    tzUTC,
    hasError,
    overrides,
    setVizWidth,
    standaloneLayoutMode,
  } = props;

  const aggregateType: "sum" | "average" =
    vizType === "bars" ? "sum" : "average";

  const {
    seriesMetadata,
    seriesValues,
    timestamps,
    aggregates: seriesAggregates,
    timeBucketDuration,
    groupBys,
  } = timeseriesData;

  const visibleAggregates = useMemo(() => {
    return seriesAggregates.filter((agg, i) => {
      const meta = seriesMetadata[i];
      return isLineSelected(meta.selectionType, highlightState.state);
    });
  }, [highlightState, seriesAggregates, seriesMetadata]);

  // consider to have no data if no visible series have any point counts
  const noData = useMemo(() => {
    if (timestamps.length === 0) {
      return true;
    }
    return visibleAggregates.reduce((acc, v) => v.count + acc, 0) === 0;
  }, [timestamps, visibleAggregates]);

  const [nearestPoint, setNearestPoint] = useState<NearestPoint | null>(null);
  const [mousePosition, setMousePosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  const [disableInteractivity] = useAtom(globalDisableInteractivityAtom);
  const [_, setTimestamp] = useAtom(setGlobalTimeCursorAtom);

  const monitorLegendProps = {
    groupBys: groupBys.map((g) => g.alias ?? g.btql),
    highlightState: highlightState.state,
    addSelected: highlightState.addSelected,
    removeSelected: highlightState.removeSelected,
    clearSelected: highlightState.clearSelected,
    setSelected: highlightState.setSelected,
    clearHighlight: highlightState.clearHighlight,
    highlight: highlightState.highlight,
    seriesAggregates,
    seriesMetadata,
    aggregateType,
    aggregateFormatter: props.aggregateFormatter,
  };

  // Remember that svg clip paths ids are global, so use unique id per chart
  const vizAreaClipPathId = useMemo(() => {
    return `viz-area-${nanoid()}`;
  }, []);

  const chartId = useMemo(() => {
    return nanoid();
  }, []);

  // Responsive logic
  const chartRef = useRef<HTMLDivElement>(null);
  const chartSize = useResizeObserver(chartRef);

  const chartFeatures = useMemo(() => {
    return getResponsiveFeatures(chartSize, overrides);
  }, [chartSize, overrides]);

  const numSeries = seriesValues.length;
  const [leftAxisWidth, onLeftAxisWidth] = useState<number | null>(null);

  const { padding: chartPadding, bottomLegendHeight } = useMemo(() => {
    if (standaloneLayoutMode) {
      return calcPaddingStandalone(
        chartSize,
        chartFeatures,
        numSeries + (numSeries > 1 && vizType === "bars" ? 1 : 0),
        leftAxisWidth,
      );
    }
    return calcPadding(chartSize, chartFeatures);
  }, [
    chartSize,
    chartFeatures,
    numSeries,
    standaloneLayoutMode,
    leftAxisWidth,
    vizType,
  ]);

  const { width: vizWidth, height: vizHeight } = useMemo(
    () => calcVizSize(chartSize, chartPadding),
    [chartSize, chartPadding],
  );

  // call up to let parent know viz width
  useEffect(() => {
    if (setVizWidth && vizWidth > 0) {
      setVizWidth(vizWidth);
    }
  }, [setVizWidth, vizWidth]);

  const selectedSeriesIndices = useMemo(() => {
    const selected = new Set<number>();
    seriesMetadata.forEach((s, seriesIndex) => {
      if (isLineSelected(s.selectionType, highlightState.state)) {
        selected.add(seriesIndex);
      }
    });
    return selected;
  }, [seriesMetadata, highlightState]);

  const stackedData = useMemo(() => {
    if (vizType !== "bars") {
      return null;
    }
    return getTimeseriesStackedTimeBuckets(
      timeseriesData,
      selectedSeriesIndices,
    );
  }, [timeseriesData, vizType, selectedSeriesIndices]);

  // generate x and a y scales
  const xScale = useMemo(
    () => getXScale(chartTimeFrame, vizWidth),
    [chartTimeFrame, vizWidth],
  );

  // base y extents on all visible series min and max values
  const yExtents = useMemo(() => {
    return getTimeSeriesValuesExtents(
      seriesAggregates.filter((a, i) => {
        const s = seriesMetadata[i];
        return isLineSelected(s.selectionType, highlightState.state);
      }),
    );
  }, [seriesAggregates, seriesMetadata, highlightState]);

  const yScale = useMemo(() => {
    // todo - cleanup
    if (stackedData) {
      const bucketMaxes = stackedData.seriesStackedSum
        .map((b) => b.at(-1) ?? 0)
        .filter((v) => Number.isFinite(v));
      const maxStack = Math.max(...bucketMaxes);
      return getYScale({ min: 0, max: maxStack }, vizHeight);
    }
    return getYScale(yExtents, vizHeight);
  }, [yExtents, vizHeight, stackedData]);

  // brush logic
  const [isBrushing, setIsBrushing] = useState<boolean>(false);
  const handleOnBrushStart = useCallback(() => {
    setIsBrushing(true);
  }, [setIsBrushing]);

  const handleOnBrushEnd = useCallback(() => {
    setIsBrushing(false);
  }, [setIsBrushing]);

  const { brushRef } = useBrush({
    xScale,
    yScale,
    onBrushStart: handleOnBrushStart,
    onBrushEnd: handleOnBrushEnd,
    onBrush: props.onBrush,
    ignoreBrushClick: true,
  });

  const getVizMousePoint = useCallback(
    (e: React.MouseEvent) => {
      const { offsetX, offsetY } = e.nativeEvent;
      return {
        x: xScale.invert(offsetX),
        y: yScale.invert(offsetY),
      };
    },
    [xScale, yScale],
  );

  // filter out not selected values and empty values
  // this is for mouse over later - we want to only consider visible
  const visibleData = useMemo(() => {
    // if selection not set everything is visible, skip work
    if (!highlightState || !highlightState.state.selected) {
      return {
        visibleToOriginal: null,
        originalToVisible: null,
      };
    }

    // 'compressed' time index back to 'original' index
    const visibleToOriginal = new Map<number, number>();
    const originalToVisible = new Map<number, number>();

    let numSoFar = 0;
    timestamps.forEach((t, timeIndex) => {
      const retain = seriesMetadata.some((s, seriesIndex) => {
        const v = seriesValues[seriesIndex][timeIndex];
        if (!Number.isFinite(v)) {
          return false;
        }
        if (vizType === "bars" && v <= 0) {
          return false;
        }
        return isLineSelected(s.selectionType, highlightState.state);
      });

      if (retain) {
        originalToVisible?.set(timeIndex, numSoFar);
        visibleToOriginal?.set(numSoFar, timeIndex);
        numSoFar++;
      }
    });

    return {
      visibleToOriginal,
      originalToVisible,
    };
  }, [highlightState, seriesMetadata, seriesValues, timestamps, vizType]);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY,
      });

      const point = getVizMousePoint(e);
      setTimestamp(point.x);

      if (
        isFetchingData ||
        timestamps.length === 0 ||
        seriesValues.length === 0
      ) {
        setNearestPoint(null);
        return;
      }

      const { visibleToOriginal, originalToVisible } = visibleData;

      const nearest = getNearestPoint({
        point,
        timestamps,
        seriesValues,
        stackedData,
        timeBucketDuration,
        selectedSeriesIndices,
        originalToVisible,
        visibleToOriginal,
      });

      setNearestPoint(nearest);
    },
    [
      getVizMousePoint,
      setTimestamp,
      seriesValues,
      timestamps,
      setNearestPoint,
      setMousePosition,
      isFetchingData,
      stackedData,
      timeBucketDuration,
      selectedSeriesIndices,
      visibleData,
    ],
  );

  const tooltipPosition = useMemo(() => {
    if (mousePosition === null) {
      return null;
    }

    const { x, y } = mousePosition;
    let xWithOffset = x + 8;
    const yWithOffset = y + 8;

    // flip x on right edge
    const TOOLTIP_WIDTH = 224;
    if (xWithOffset + TOOLTIP_WIDTH > window.innerWidth) {
      xWithOffset = x - TOOLTIP_WIDTH - 8;
    }

    return {
      x: xWithOffset,
      y: yWithOffset,
    };
  }, [mousePosition]);

  const handleMouseLeave = useCallback(() => {
    setNearestPoint(null);
    setMousePosition(null);
    setTimestamp(0);
  }, [setNearestPoint, setTimestamp, setMousePosition]);

  // we have to use timeouts because we have conflicting single and double click events
  const [clickTimeout, setClickTimeout] = useState<NodeJS.Timeout>();

  const handleMouseDoubleClick = useCallback(() => {
    clearTimeout(clickTimeout);
    setClickTimeout(undefined);
    onDoubleClick?.();
    setTimestamp(0); // clear global cursor on click because tf and data can change
  }, [clickTimeout, onDoubleClick, setTimestamp]);

  const handleMouseClick = useCallback(
    (e: React.MouseEvent) => {
      if (clickTimeout) {
        return;
      }
      const id = setTimeout(() => {
        setClickTimeout(undefined);
        if (!onClick || !nearestPoint) {
          return;
        }
        const timestamp = timestamps[nearestPoint.timeIndex];
        const pointSeriesMeta = seriesMetadata[nearestPoint.seriesIndex];
        const { groupBys } = pointSeriesMeta ?? {};
        onClick(e, timestamp, groupBys);
        setTimestamp(0); // clear global cursor on click because tf and data can change
      }, 250);
      setClickTimeout(id);
    },
    [
      onClick,
      nearestPoint,
      timestamps,
      setTimestamp,
      clickTimeout,
      seriesMetadata,
    ],
  );

  const svgVizStyle = useMemo(() => {
    return {
      left: chartPadding.left,
      top: chartPadding.top,
      width: vizWidth,
      height: vizHeight,
    };
  }, [chartPadding.left, chartPadding.top, vizWidth, vizHeight]);

  // will-change-transform to tell compositor to treat as a separate layer, important for perf
  const svgVizClassName =
    "absolute z-20 overflow-visible will-change-transform";

  const axisGradientEdgePct = useMemo(() => {
    if (vizWidth <= 0) return 0;
    const pct = AXIS_GRADIENT_EDGE_PX / vizWidth;
    return Math.max(0, Math.min(0.5, pct));
  }, [vizWidth]);

  // placeholder empty container until observer gets size
  if (chartSize.width === 0 || chartSize.height === 0) {
    return <div style={{ flexGrow: 1, position: "relative" }} ref={chartRef} />;
  }

  return (
    <div style={{ flexGrow: 1, position: "relative" }} ref={chartRef}>
      {/* Viz section */}
      <svg style={svgVizStyle} className={svgVizClassName}>
        <defs>
          <clipPath id={vizAreaClipPathId}>
            <rect x={0} y={0} width={vizWidth} height={vizHeight + 1} />
          </clipPath>
        </defs>
        {!noData && !hasError && (
          <g clipPath={`url(#${vizAreaClipPathId})`}>
            <TimeCursor
              xScale={xScale}
              yScale={yScale}
              vizType={vizType}
              nearestPoint={nearestPoint}
              timeBucketDuration={timeBucketDuration}
              timestamps={timestamps}
            />
          </g>
        )}
      </svg>
      {/* x axis */}
      {chartFeatures.hasXAxis && (
        <svg
          style={{
            left: chartPadding.left,
            top: chartPadding.top + vizHeight,
            width: vizWidth,
            height: X_AXIS_HEIGHT,
          }}
          className={cn("pointer-events-none absolute z-20 overflow-hidden")}
        >
          <defs>
            <linearGradient
              id={`x-axis-gradient-${chartId}`}
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" stopColor="white" stopOpacity="0" />
              <stop
                offset={`${axisGradientEdgePct * 100}%`}
                stopColor="white"
                stopOpacity="1"
              />
              <stop
                offset={`${(1 - axisGradientEdgePct) * 100}%`}
                stopColor="white"
                stopOpacity="1"
              />
              <stop offset="100%" stopColor="white" stopOpacity="0" />
            </linearGradient>
            <mask id={`x-axis-mask-${chartId}`}>
              <rect
                width="100%"
                height="100%"
                fill={`url(#x-axis-gradient-${chartId})`}
              />
            </mask>
          </defs>
          <g mask={`url(#x-axis-mask-${chartId})`}>
            <BottomAxis
              xScale={xScale}
              yScale={yScale}
              tzUTC={tzUTC}
              isTimeFrameAxis
            />
          </g>
        </svg>
      )}
      {!noData &&
        !hasError &&
        (vizType === "lines" ? (
          <VizLines
            style={svgVizStyle}
            className={svgVizClassName}
            timeseriesData={timeseriesData}
            xScale={xScale}
            yScale={yScale}
            highlightState={props.highlightState.state}
            nearestPoint={nearestPoint}
            clipPathId={vizAreaClipPathId}
          />
        ) : (
          stackedData && (
            <VizBars
              style={svgVizStyle}
              className={svgVizClassName}
              timeseriesData={timeseriesData}
              xScale={xScale}
              yScale={yScale}
              highlightState={props.highlightState.state}
              nearestPoint={nearestPoint}
              clipPathId={vizAreaClipPathId}
              stackedData={stackedData}
            />
          )
        ))}
      {/* interaction layer */}
      <svg
        style={svgVizStyle}
        className={cn(svgVizClassName, {
          "pointer-events-none": disableInteractivity,
        })}
      >
        <g
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          onClick={handleMouseClick}
          onDoubleClick={handleMouseDoubleClick}
          className="cursor-crosshair"
        >
          <rect width={vizWidth} height={vizHeight} fill="transparent"></rect>
          {brushRef && (
            <g
              ref={brushRef}
              className={
                disableInteractivity ? "[&>.overlay]:pointer-events-none" : ""
              }
            ></g>
          )}
        </g>
      </svg>
      {(isFetchingData || noData || hasError) && (
        <div
          className="absolute flex items-center justify-center gap-1.5 text-sm text-primary-400"
          style={{
            left: chartPadding.left,
            top: chartPadding.top,
            width: vizWidth,
            height: vizHeight,
          }}
        >
          {hasError ? (
            <>
              <TriangleAlert className="size-3" />
              Query error
            </>
          ) : noData ? (
            <>
              <SearchSlash className="size-3" />
              No data
            </>
          ) : (
            <Spinner className="size-4" />
          )}
        </div>
      )}
      {/* y axis */}
      {chartFeatures.hasYAxis && (
        <svg
          style={{
            left: -6, // todo left padding properly
            top: chartPadding.top,
            width: chartPadding.left,
            height: vizHeight,
          }}
          className={cn("pointer-events-none absolute z-20 overflow-visible")}
        >
          <LeftAxis
            xScale={xScale}
            yScale={yScale}
            isResponsiveTimeseries
            onLeftAxisWidth={onLeftAxisWidth}
            tickFormatter={
              props.tickFormatter
                ? props.tickFormatter
                : (v: number, i: number) => v.toLocaleString()
            }
          />
        </svg>
      )}
      {/* bottom legend element */}
      {chartFeatures.hasBottomLegend && (
        <div
          className={cn(
            "absolute flex flex-wrap justify-end gap-2 overflow-hidden",
          )}
          style={{
            left: 0,
            top:
              chartPadding.top +
              vizHeight +
              (chartFeatures.hasXAxis ? 1 : 0) * X_AXIS_HEIGHT,
            height: bottomLegendHeight,
            width: chartSize.width,
          }}
        >
          <MonitorChartLegend
            {...monitorLegendProps}
            height={bottomLegendHeight}
            width={chartSize.width}
            vizType={vizType}
            nearestIndex={nearestPoint?.seriesIndex}
            isStackedTotal={nearestPoint?.isStackedTotal}
          />
        </div>
      )}
      {/* Tooltip */}
      {nearestPoint && !noData && !isFetchingData && !isBrushing && (
        <Tooltip open delayDuration={0} disableHoverableContent>
          <TooltipPortal>
            <TooltipContent
              hideWhenDetached
              className="pointer-events-none absolute w-56 will-change-transform"
              style={{
                transform: `translate(${tooltipPosition?.x || 0}px, ${tooltipPosition?.y || 0}px)`,
              }}
            >
              <ChartTooltip
                values={seriesValues.map((s) => ({
                  value: s[nearestPoint.timeIndex],
                }))}
                runDate={new Date(
                  timestamps[nearestPoint.timeIndex],
                ).toString()}
                numberFormatter={props.aggregateFormatter}
                count={
                  timeseriesData.timeMetadata[nearestPoint.timeIndex]?.count ??
                  0
                }
                stackedTotal={
                  vizType === "bars" ? nearestPoint.stackedTotal : 0
                }
                isStackedTotal={nearestPoint.isStackedTotal}
                nearestIndex={nearestPoint.seriesIndex}
                labels={seriesMetadata.map((m) => m.name)}
                extraBottomText={onClick ? clickMessage : undefined}
                vizType={vizType}
                tzUTC={tzUTC}
              />
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      )}
    </div>
  );
};
