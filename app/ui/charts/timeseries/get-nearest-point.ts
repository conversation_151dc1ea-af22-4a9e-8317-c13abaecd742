import { type NearestPoint } from "#/ui/charts/timeseries-data/chart-data.types";
import type { StackedTimeBuckets } from "#/ui/charts/timeseries-data/get-timeseries-stacked-time";

interface GetNearestPointProps {
  point: { x: number; y: number };
  timestamps: number[];
  seriesValues: Float64Array[];
  stackedData: StackedTimeBuckets | null;
  timeBucketDuration: number;
  selectedSeriesIndices: Set<number>;
  originalToVisible: Map<number, number> | null;
  visibleToOriginal: Map<number, number> | null;
}

/**
 * Get the closest timestamp index to a goal timestamp
 * Assumes timestamps are in order, not overlapping,
 * and that every timestamp is the start of an interval [start, start + timeBucketDuration)
 */
export const getNearestTimeIndex = (
  timestamps: number[],
  timeBucketDuration: number,
  goalTimestamp: number,
): number => {
  let leftmostBinIndex = 0;
  let leftmostBinDistance = Infinity;
  for (let i = 0; i < timestamps.length; i++) {
    const binStart = timestamps[i];
    const binEnd = binStart + timeBucketDuration;

    // always use a bucket if goal timestamp intersects it
    if (goalTimestamp >= binStart && goalTimestamp < binEnd) {
      return i;
    }

    // if bin is before goal, keep track of bin and continue search
    if (binEnd <= goalTimestamp) {
      leftmostBinIndex = i;
      leftmostBinDistance = goalTimestamp - binEnd;
      continue;
    }

    // here we must be in the case where bin is to the right of the goalTimestamp
    // (rightmost because timestamps are in order)
    const rightmostBinDistance = binStart - goalTimestamp;

    // use this rightmost bin if it's closer, else use the leftmost
    if (rightmostBinDistance < leftmostBinDistance) {
      return i;
    }
    return leftmostBinIndex;
  }

  // all bins are to the left of goal time
  return leftmostBinIndex;
};

export const getNearestPoint = (props: GetNearestPointProps): NearestPoint => {
  const {
    point,
    timestamps,
    stackedData,
    timeBucketDuration,
    seriesValues,
    selectedSeriesIndices,
    originalToVisible,
    visibleToOriginal,
  } = props;

  const visibleTimestamps = timestamps.filter((t, i) =>
    originalToVisible === null ? true : originalToVisible.has(i),
  );

  const nearestTimeIndex = getNearestTimeIndex(
    visibleTimestamps,
    timeBucketDuration,
    point.x,
  );

  const originalTimeIndex =
    visibleToOriginal === null
      ? nearestTimeIndex
      : (visibleToOriginal.get(nearestTimeIndex) ?? 0);

  let nearestSeriesIndex = -1;
  let smallestDelta = Infinity;
  let isStackedTotal = false;
  let stackedTotal = 0;

  // if stackedData exists, assume it's vizType bars
  if (stackedData) {
    const { seriesIndices, seriesStackedSum } = stackedData;
    const indices = seriesIndices[originalTimeIndex];
    const stackedValues = seriesStackedSum[originalTimeIndex];
    const stackMax = stackedValues.at(-1) ?? 0;
    stackedTotal = stackMax;

    for (let i = 0; i < stackedValues.length; i++) {
      const prevValue = stackedValues[i - 1] ?? 0;
      const currentValue = stackedValues[i];

      if (point.y >= prevValue && point.y <= currentValue) {
        nearestSeriesIndex = indices[i];
        break;
      }
    }
    if (point.y > stackMax) {
      isStackedTotal = true;
    }
  } else {
    seriesValues.forEach((s, i) => {
      if (!selectedSeriesIndices.has(i)) {
        return;
      }
      const v = s[originalTimeIndex];
      if (!Number.isFinite(v)) {
        return;
      }
      const delta = Math.abs(v - point.y);
      if (delta < smallestDelta) {
        smallestDelta = delta;
        nearestSeriesIndex = i;
      }
    });
  }
  return {
    timeIndex: originalTimeIndex,
    timeStart: visibleTimestamps[nearestTimeIndex],
    timeEnd: visibleTimestamps[nearestTimeIndex] + timeBucketDuration,
    seriesIndex: nearestSeriesIndex,
    isStackedTotal,
    stackedTotal,
  };
};
