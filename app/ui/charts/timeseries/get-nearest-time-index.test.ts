import { describe, expect, test } from "vitest";
import { getNearestTimeIndex } from "./get-nearest-point";

describe("getNearestTimeIndex", () => {
  // Timestamps represent mostly adjacent segments, with a gap between the bucket ending at 250 and the one starting at 350.
  // Buckets are: [100, 150), [150, 200), [200, 250), [350, 400), [400, 450)
  const timestamps = [100, 150, 200, 350, 400];
  const timeBucketDuration = 50;

  test("should return the correct index when goalTimestamp is inside a bucket", () => {
    // Goal is inside the bucket starting at 150
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 175)).toBe(1);
    // Goal is inside the bucket starting at 350
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 399)).toBe(3);
  });

  test("should return the correct index when goalTimestamp is at the exact start of a bucket", () => {
    // Goal is exactly at the start of the bucket at index 2
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 200)).toBe(2);
  });

  test("should return the correct index when goalTimestamp is just before the end of a bucket", () => {
    // Goal is just before the end of the bucket starting at 100
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 149)).toBe(0);
  });

  test("should return the index of the closer bin when in a sparse gap (closer to left)", () => {
    // Goal is 280. It's in the gap between bucket 2 (ends at 250) and bucket 3 (starts at 350).
    // Distance from end of bucket 2: 280 - 250 = 30.
    // Distance from start of bucket 3: 350 - 280 = 70.
    // Should choose index 2.
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 280)).toBe(2);
  });

  test("should return the index of the closer bin when in a sparse gap (closer to right)", () => {
    // Goal is 320. It's in the gap between bucket 2 (ends at 250) and bucket 3 (starts at 350).
    // Distance from end of bucket 2: 320 - 250 = 70.
    // Distance from start of bucket 3: 350 - 320 = 30.
    // Should choose index 3.
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 320)).toBe(3);
  });

  test("should return the left index when goalTimestamp is equidistant in a sparse gap", () => {
    // Goal is 300. It's in the gap between bucket 2 (ends at 250) and bucket 3 (starts at 350).
    // Distance from end of bucket 2: 300 - 250 = 50.
    // Distance from start of bucket 3: 350 - 300 = 50.
    // The function should prefer the leftmost bin in a tie.
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 300)).toBe(2);
  });

  test("should return the first index when goalTimestamp is before all buckets", () => {
    // Goal is 50, which is before the first timestamp (100).
    // The closest bin is the first one.
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 50)).toBe(0);
  });

  test("should return the first index when goalTimestamp is far before all buckets", () => {
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, -1000)).toBe(0);
  });

  test("should return the last index when goalTimestamp is after all buckets", () => {
    // Goal is 500. The last bucket ends at 450.
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 500)).toBe(4);
  });

  test("should return the last index when goalTimestamp is far after all buckets", () => {
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 10000)).toBe(4);
  });

  test("should handle an empty timestamps array", () => {
    expect(getNearestTimeIndex([], timeBucketDuration, 150)).toBe(0);
  });

  test("should handle a single timestamp in the array", () => {
    const singleTimestamp = [300];
    // Inside the bucket
    expect(getNearestTimeIndex(singleTimestamp, timeBucketDuration, 325)).toBe(
      0,
    );
    // Before the bucket
    expect(getNearestTimeIndex(singleTimestamp, timeBucketDuration, 100)).toBe(
      0,
    );
    // After the bucket
    expect(getNearestTimeIndex(singleTimestamp, timeBucketDuration, 400)).toBe(
      0,
    );
  });

  test("should handle goalTimestamp being exactly the end of a bucket", () => {
    // Goal is 250, which is the end of the bucket at index 2.
    // The distance to the end of bucket 2 is 0.
    // The distance to the start of bucket 3 (at 350) is 100.
    // It should choose the closer one, which is index 2.
    expect(getNearestTimeIndex(timestamps, timeBucketDuration, 250)).toBe(2);
  });
});
