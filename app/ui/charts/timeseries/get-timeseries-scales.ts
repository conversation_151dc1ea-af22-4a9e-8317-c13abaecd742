import { type ChartTimeFrame } from "#/app/app/[org]/monitor/time-controls/time-range";
import * as d3 from "d3";

export const getXScale = (chartTimeFrame: ChartTimeFrame, width: number) => {
  const domain = [chartTimeFrame.start, chartTimeFrame.end];
  return d3.scaleLinear().domain(domain).range([0, width]);
};

export const getYScale = (
  extents: { min: number; max: number },
  height: number,
) => {
  const { min, max } = extents;

  // include zero in domain
  const domain = [Math.min(0, min), Math.max(0, max)];

  // add 5% padding
  const dy = 0.05 * (domain[1] - domain[0]);
  if (domain[1] > 0) {
    domain[1] += dy;
  } else {
    domain[0] -= dy;
  }

  // in [0, 0] case, default to [0, 1]
  if (domain[0] === 0 && domain[1] === 0) {
    domain[1] = 1;
  }

  return d3.scaleLinear().domain(domain).range([height, 0]).nice();
};
