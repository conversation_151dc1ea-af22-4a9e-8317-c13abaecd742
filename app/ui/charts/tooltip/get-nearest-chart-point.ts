import { bisector as d3Bisector } from "d3";
import type { ScaleLinear } from "d3";

import { type Coords } from "#/ui/charts/Chart";
import { type YScale } from "#/ui/charts/chart.types";

export const getNearestChartPoint = (
  coords: Coords,
  xScale: ScaleLinear<number, number>,
  yScale: YScale,
  xValuesWithYValue: { value: number; index: number }[],
  chartData: { y: ({ value: number } | null)[] }[],
  /**
   * If ordinal X, values are not time bucketed linearly - so we want nearest.
   * Else we assume linear time scale with bucketing and that points represent
   * the start of a time bucket - thus we want the point to the left of coord x
   * (assuming dense x data for now)
   */
  isOrdinalX?: boolean,
) => {
  if (coords.x == null || coords.y == null) {
    return null;
  }

  const chartBoundsWidth = xScale.range()[1];
  const domainValue = xScale.invert(coords.x * chartBoundsWidth);
  const bisectorGen = d3Bisector(
    ({ value }: { value: number }, x: number) => value - x,
  );

  // nearest for ordinal, else left
  const bisector = isOrdinalX ? bisectorGen.center : bisectorGen.left;
  const offset = isOrdinalX ? 0 : -1;

  const xValueIndex = Math.max(
    0,
    bisector(xValuesWithYValue, domainValue) + offset,
  );
  const values = xValuesWithYValue[xValueIndex];
  const valueIndex = values?.index ?? null;

  if (valueIndex === null) {
    return null;
  }

  const chartBoundsHeight = yScale.range()[1];
  const rangeValue = yScale.invert((1 - coords.y) * chartBoundsHeight);

  const yValues = chartData[valueIndex].y;
  let closestSeriesIndex = 0;
  let closetSeriesDy = Infinity;
  for (let i = 0; i < yValues.length; i++) {
    const y = yValues[i];
    if (!y) {
      continue;
    }
    const dy = Math.abs(y.value - rangeValue);
    // take last equal so z-index on top
    if (dy <= closetSeriesDy) {
      closestSeriesIndex = i;
      closetSeriesDy = dy;
    }
  }

  return { valueIndex, seriesIndex: closestSeriesIndex };
};
