import {
  CHART_COLOR_CLASSNAMES,
  DEFAULT_COLOR_CLASSNAME,
  getColorForIndex,
  TIMESERIES_COLOR_CLASSNAMES,
} from "#/ui/color";
import { NULL_DASH } from "#/ui/table/formatters/null-formatter";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

interface ConversionProps {
  values: ({ value: number } | null)[];
  labels: string[];
  seriesColorMap?: { [key: string]: number | null };
  valueFormatOptions?: Intl.NumberFormatOptions;
  nearestIndex?: number;
  isStackedTotal?: boolean;
  numberFormatter?: (value: number) => string;
  vizType?: TimeseriesVizType;
}

export const valuesToTooltipRows = (props: ConversionProps) => {
  const {
    values,
    labels,
    seriesColorMap,
    valueFormatOptions,
    nearestIndex,
    isStackedTotal,
    numberFormatter,
    vizType,
  } = props;
  return values.map((v, i) => {
    const labelName = labels[i];
    const seriesColor = (seriesColorMap ?? {})[labelName];
    const colorClassName =
      seriesColorMap == null
        ? (getColorForIndex(
            i,
            vizType === "bars"
              ? TIMESERIES_COLOR_CLASSNAMES
              : CHART_COLOR_CLASSNAMES,
          ) ?? DEFAULT_COLOR_CLASSNAME)
        : seriesColor != null
          ? getColorForIndex(
              seriesColor,
              vizType === "bars"
                ? TIMESERIES_COLOR_CLASSNAMES
                : CHART_COLOR_CLASSNAMES,
            )
          : DEFAULT_COLOR_CLASSNAME;

    const showNull =
      v == null || Number.isNaN(v?.value) || v.value === undefined;
    return {
      className: colorClassName,
      label: labels[i],
      isNearest: nearestIndex === i || Boolean(isStackedTotal),
      // for sorting
      value: v || 0,
      valueString: showNull
        ? NULL_DASH
        : numberFormatter
          ? numberFormatter(v.value)
          : v.value.toLocaleString(undefined, {
              maximumFractionDigits: 2,
              ...valueFormatOptions,
            }),
    };
  });
};
