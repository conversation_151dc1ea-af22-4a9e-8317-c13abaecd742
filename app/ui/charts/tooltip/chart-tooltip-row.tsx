import { cn } from "#/utils/classnames";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

interface ChartTooltipRowData {
  className: string;
  label: string;
  value:
    | number
    | {
        value: number;
      };
  valueString: string;
  isNearest: boolean;
  vizType: TimeseriesVizType;
}

export const ChartTooltipRow = (props: ChartTooltipRowData) => {
  const { valueString, label, className, isNearest, vizType } = props;
  return (
    <div
      className={cn("flex items-center gap-2 font-normal text-primary-700", {
        "text-primary-400": !isNearest,
      })}
    >
      <div
        className={cn("box-content size-2 flex-none", className, {
          "rounded-full": vizType === "lines",
        })}
      />
      <div className="flex-auto truncate">{label}</div>
      <div className="flex-none font-medium tabular-nums">{valueString}</div>
    </div>
  );
};
