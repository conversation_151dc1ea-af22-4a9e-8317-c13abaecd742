import { formatFullDateTime } from "#/utils/date";
import { isEmpty } from "braintrust/util";
import { useMemo } from "react";
import { valuesToTooltipRows } from "./values-to-tooltip-row";
import { ChartTooltipRow } from "./chart-tooltip-row";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

const MAX_ROWS = 15;

export const ChartTooltip = ({
  values,
  title,
  runDate,
  count,
  labels,
  seriesColorMap,
  valueFormatOptions,
  extraBottomText,
  nearestIndex,
  isStackedTotal,
  stackedTotal,
  numberFormatter,
  vizType = "lines",
  tzUTC = false,
}: {
  values: ({ value: number } | null)[];
  title?: string;
  runDate: string;
  count: bigint;
  labels: string[];
  seriesColorMap?: { [key: string]: number | null };
  valueFormatOptions?: Intl.NumberFormatOptions;
  extraBottomText?: string;
  nearestIndex?: number;
  isStackedTotal?: boolean;
  stackedTotal?: number;
  numberFormatter?: (value: number) => string;
  vizType?: TimeseriesVizType;
  tzUTC?: boolean;
}) => {
  const numRowsClipped = Math.max(0, values.length - MAX_ROWS);

  const rowData = useMemo(() => {
    return valuesToTooltipRows({
      values: values.slice(0, MAX_ROWS),
      labels: labels.slice(0, MAX_ROWS),
      seriesColorMap,
      valueFormatOptions,
      numberFormatter,
      nearestIndex,
      isStackedTotal,
      vizType,
    });
  }, [
    values,
    labels,
    seriesColorMap,
    valueFormatOptions,
    nearestIndex,
    isStackedTotal,
    numberFormatter,
    vizType,
  ]);

  return (
    <>
      <div className="mb-1 text-sm font-medium">{title}</div>
      <div className="text-xs text-primary-500">
        Ran {formatFullDateTime(new Date(runDate), tzUTC)}
      </div>
      <div className="mt-2 text-xs">
        <div className="flex flex-col gap-2">
          {rowData.map((rowData, i) => {
            return <ChartTooltipRow key={i} {...rowData} vizType={vizType} />;
          })}
        </div>
        {numRowsClipped > 0 && (
          <div className="mt-2 flex items-center gap-2">
            <div className="flex-auto truncate text-left text-xs text-primary-500">
              {numRowsClipped.toLocaleString()} more
            </div>
          </div>
        )}
        {vizType === "bars" && stackedTotal !== undefined && (
          <div className="mt-2 flex items-center gap-2 border-t pt-1.5 text-primary-700">
            <div className="flex-auto truncate">Total sum</div>
            <div className="flex-none font-semibold tabular-nums">
              {numberFormatter
                ? numberFormatter(stackedTotal)
                : stackedTotal.toLocaleString()}
            </div>
          </div>
        )}
        {!isEmpty(count) && (
          <div className="mt-2 flex items-center gap-2 border-t pt-1.5 text-primary-700">
            <div className="flex-auto truncate">Total events</div>
            <div className="flex-none font-semibold tabular-nums">
              {count.toLocaleString()}
            </div>
          </div>
        )}
      </div>
      {extraBottomText && (
        <div className="mt-2 text-xs text-primary-500">{extraBottomText}</div>
      )}
    </>
  );
};
