import { type TimeseriesAggregates } from "./chart-data.types";

export const getTimeSeriesValuesExtents = (
  aggregates: TimeseriesAggregates[],
) => {
  const hasCountAggrs = aggregates.filter((a) => a.count > 0);
  if (hasCountAggrs.length === 0) {
    return { min: 0, max: 0 };
  }

  const minY = Math.min(...hasCountAggrs.map((a) => a.min));
  const maxY = Math.max(...hasCountAggrs.map((a) => a.max));

  return { min: minY, max: maxY };
};
