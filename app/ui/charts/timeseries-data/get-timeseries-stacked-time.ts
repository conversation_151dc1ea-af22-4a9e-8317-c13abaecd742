import { type TimeseriesData } from "./chart-data.types";

export interface StackedTimeBuckets {
  // time bucket to data
  timestamps: number[]; // start of bucket
  bucketDuration: number; // width of every bucket (only support equal widths for now)

  seriesIndices: number[][]; // series index in original timeseries data
  seriesStackedSum: number[][]; // corresponding stacked sum
  seriesIndexToStackedIndex: number[][];
}

export const getTimeseriesStackedTimeBuckets = <S, T>(
  timeseriesData: TimeseriesData<S, T>,
  selectedSeriesIndices: Set<number>,
): StackedTimeBuckets => {
  const { timestamps, seriesValues, timeBucketDuration } = timeseriesData;

  const seriesIndices: number[][] = [];
  const seriesStackedSum: number[][] = [];
  const seriesIndexToStackedIndex: number[][] = [];

  seriesValues.forEach((s, seriesIndex) => {
    // ignore not selected
    if (!selectedSeriesIndices.has(seriesIndex)) {
      return;
    }
    s.forEach((v, timeIndex) => {
      if (!seriesIndices[timeIndex]) {
        seriesStackedSum[timeIndex] = [0]; // init at zero
        seriesIndices[timeIndex] = [0]; // shouldn't be accessed
        seriesIndexToStackedIndex[timeIndex] = [];
      }
      if (!Number.isFinite(v) || v <= 0) {
        // ignore nans, ignore zeros, ignore negatives
        return;
      }
      const currentLength = seriesIndices[timeIndex].length;
      const prevValue = seriesStackedSum[timeIndex][currentLength - 1];
      seriesStackedSum[timeIndex][currentLength] = prevValue + v;
      seriesIndices[timeIndex][currentLength] = seriesIndex;
      seriesIndexToStackedIndex[timeIndex][seriesIndex] = currentLength;
    });
  });

  return {
    timestamps,
    bucketDuration: timeBucketDuration,
    seriesIndices,
    seriesStackedSum,
    seriesIndexToStackedIndex,
  };
};
