import { type TimeseriesAggregates } from "./chart-data.types";

// gets (and caches) series aggregates
export const getSeriesAggregates = (
  seriesValues: Float64Array[],
): TimeseriesAggregates[] => {
  return seriesValues.map((values) => {
    let count = 0;
    let min = Infinity;
    let max = -Infinity;
    let sum = 0;
    for (let i = 0; i < values.length; i++) {
      const v = values[i];
      // only consider finite points
      if (!Number.isFinite(v)) {
        continue;
      }

      count++;
      sum += v;
      min = Math.min(min, v);
      max = Math.max(max, v);
    }

    return {
      count,
      min,
      max,
      sum,
    };
  });
};
