export interface TimeseriesData<DataPointMetadata, TimeseriesMetadata> {
  timestamps: number[];
  timeBucketDuration: number; // for now assume all buckets have same duration
  timeMetadata: DataPointMetadata[]; // // to timestamps
  seriesValues: Float64Array[];
  seriesMetadata: TimeseriesMetadata[]; // // to series
  aggregates: TimeseriesAggregates[]; // // to series
  groupBys: { btql: string; alias?: string }[];
}

export interface MonitorTimeseriesDataPoint<DataPointMetadata> {
  x: number;
  y: ({ value: number } | null)[];
  metadata: DataPointMetadata;
}

export interface TimeseriesAggregates {
  count: number; // number of aggregated points, not number of original points
  sum: number;
  min: number;
  max: number;
}

export interface NearestPoint {
  timeIndex: number;
  seriesIndex: number;
  isStackedTotal?: boolean;
  stackedTotal?: number;
  timeStart: number;
  timeEnd: number;
}
