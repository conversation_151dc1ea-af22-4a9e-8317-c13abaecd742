import { describe, expect, test } from "vitest";
import { getSeriesAggregates } from "./get-series-aggregates";

describe("series aggregates", () => {
  test("simple cases", () => {
    const zeros = new Float64Array(100);
    const nans = new Float64Array(100).fill(NaN);
    const sparseTens = new Float64Array(
      new Array(100).fill(0).map((_, i) => (i % 2 === 0 ? 10 : NaN)),
    );
    const linear = new Float64Array(new Array(100).fill(0).map((_, i) => i));

    expect(
      getSeriesAggregates([
        zeros,
        nans,
        sparseTens,
        linear,
        linear.toReversed(),
      ]),
    ).toEqual([
      {
        min: 0,
        max: 0,
        sum: 0,
        count: 100,
      },
      {
        min: Infinity,
        max: -Infinity,
        sum: 0,
        count: 0,
      },
      {
        min: 10,
        max: 10,
        sum: 500,
        count: 50,
      },
      {
        min: 0,
        max: 99,
        sum: (99 * 100) / 2,
        count: 100,
      },
      {
        min: 0,
        max: 99,
        sum: (99 * 100) / 2,
        count: 100,
      },
    ]);
  });

  test("edge cases", () => {
    const noLength = new Float64Array(0);
    const inf = new Float64Array([Infinity]);
    const mixed = new Float64Array([
      Infinity,
      -Infinity,
      0,
      NaN,
      42,
      Infinity,
      NaN,
      1,
      NaN,
      -7,
    ]);
    const zero = new Float64Array([0]);
    const long = new Float64Array(1e5);

    expect(getSeriesAggregates([noLength, inf, mixed, zero, long])).toEqual([
      {
        min: Infinity,
        max: -Infinity,
        sum: 0,
        count: 0,
      },
      {
        min: Infinity,
        max: -Infinity,
        sum: 0,
        count: 0,
      },
      {
        min: -7,
        max: 42,
        sum: 36,
        count: 4,
      },
      {
        min: 0,
        max: 0,
        sum: 0,
        count: 1,
      },
      {
        min: 0,
        max: 0,
        sum: 0,
        count: 1e5,
      },
    ]);
  });
});
