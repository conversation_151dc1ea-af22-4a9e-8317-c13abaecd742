import {
  type MonitorTimeseriesDataPoint,
  type TimeseriesData,
} from "./chart-data.types";
import { getSeriesAggregates } from "./get-series-aggregates";

interface DataPointMetadata {
  time: string;
  count: bigint;
}

interface TimeseriesMetadata {
  name: string;
}

// todo - unit test this
export const metricsDataToTimeseriesData = <S>(
  monitorData: MonitorTimeseriesDataPoint<DataPointMetadata>[],
  seriesMetadata: (TimeseriesMetadata & S)[], // // to y array
  timeBucketDuration: number,
  groupBys: { btql: string; alias?: string }[],
): TimeseriesData<DataPointMetadata, TimeseriesMetadata & S> => {
  const timestampsSet = new Set<number>();
  monitorData.forEach((p) => {
    timestampsSet.add(p.x);
  });

  const numSeries = seriesMetadata.length;

  // create timestamps array
  const timestamps = [...timestampsSet].toSorted((a, b) => (a > b ? 1 : -1));

  // for lookup later
  const timestampToIndex = new Map<number, number>();
  timestamps.forEach((t, i) => {
    timestampToIndex.set(t, i);
  });

  // initially fill all values with NaNs
  const initialSeriesValues = new Array(timestamps.length).fill(NaN);
  const seriesValues = new Array(numSeries)
    .fill(0)
    .map((_) => new Float64Array(initialSeriesValues));

  // populate values
  monitorData.forEach((p) => {
    p.y.forEach((y, i) => {
      const v = y?.value;
      const timestampIndex = timestampToIndex.get(p.x);
      const series = seriesValues[i];
      if (v !== undefined && timestampIndex !== undefined && series) {
        series[timestampIndex] = v;
      }
    });
  });

  // todo - need a way to reduce any dupe time meta buckets together
  const timeMetadata = monitorData
    .toSorted((a, b) => {
      const aIndex = timestampToIndex.get(a.x);
      const bIndex = timestampToIndex.get(b.x);
      if (aIndex === undefined) {
        return -1;
      }
      if (bIndex === undefined) {
        return 1;
      }
      return bIndex < aIndex ? 1 : -1;
    })
    .map((p) => p.metadata);

  const aggregates = getSeriesAggregates(seriesValues);
  return {
    timestamps,
    timeBucketDuration,
    timeMetadata,
    seriesValues,
    seriesMetadata,
    aggregates,
    groupBys,
  };
};
