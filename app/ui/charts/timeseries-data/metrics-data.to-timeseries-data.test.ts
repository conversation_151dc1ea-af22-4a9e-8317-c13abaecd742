import { describe, expect, test } from "vitest";
import { metricsDataToTimeseriesData } from "./metrics-data-to-timeseries-data";

const BRAIN_EPOCH_STRING = "2020-01-01T00:00:00.000Z";
const BRAIN_EPOCH_NUMBER = 1577836800000;

describe("series aggregates", () => {
  test("no data case", () => {
    expect(metricsDataToTimeseriesData([], [], 42)).toEqual({
      aggregates: [],
      seriesMetadata: [],
      seriesValues: [],
      timeMetadata: [],
      timestamps: [],
      timeBucketDuration: 42,
    });
  });

  test("single point case", () => {
    const monitorData = [
      {
        x: BRAIN_EPOCH_NUMBER,
        y: [{ value: 42 }],
        metadata: { time: BRAIN_EPOCH_STRING, count: BigInt(1) },
      },
    ];
    const monitorMeta = [{ name: "series1" }];

    expect(metricsDataToTimeseriesData(monitorData, monitorMeta, 7)).toEqual({
      aggregates: [
        {
          count: 1,
          max: 42,
          min: 42,
          sum: 42,
        },
      ],
      seriesMetadata: [
        {
          name: "series1",
        },
      ],
      seriesValues: [new Float64Array([42])],
      timeMetadata: [
        {
          count: BigInt(1),
          time: "2020-01-01T00:00:00.000Z",
        },
      ],
      timestamps: [1577836800000],
      timeBucketDuration: 7,
    });
  });

  test("one time, multi series case", () => {
    const monitorData = [
      {
        x: BRAIN_EPOCH_NUMBER,
        y: [{ value: 42 }, { value: -8 }, null, { value: 0 }],
        metadata: { time: BRAIN_EPOCH_STRING, count: BigInt(77) },
      },
    ];
    const monitorMeta = [
      { name: "series1" },
      { name: "series2" },
      { name: "series3" },
      { name: "series4" },
    ];

    expect(metricsDataToTimeseriesData(monitorData, monitorMeta, 88)).toEqual({
      aggregates: [
        {
          count: 1,
          max: 42,
          min: 42,
          sum: 42,
        },
        {
          count: 1,
          max: -8,
          min: -8,
          sum: -8,
        },
        {
          count: 0,
          max: -Infinity,
          min: Infinity,
          sum: 0,
        },
        {
          count: 1,
          max: 0,
          min: 0,
          sum: 0,
        },
      ],
      seriesMetadata: [
        {
          name: "series1",
        },
        {
          name: "series2",
        },
        {
          name: "series3",
        },
        {
          name: "series4",
        },
      ],
      seriesValues: [
        new Float64Array([42]),
        new Float64Array([-8]),
        new Float64Array([NaN]),
        new Float64Array([0]),
      ],
      timeMetadata: [
        {
          count: BigInt(77),
          time: "2020-01-01T00:00:00.000Z",
        },
      ],
      timestamps: [1577836800000],
      timeBucketDuration: 88,
    });
  });

  test("one series multi time buckets case", () => {
    const monitorData = [
      {
        x: BRAIN_EPOCH_NUMBER,
        y: [{ value: 42 }],
        metadata: { time: BRAIN_EPOCH_STRING, count: BigInt(7) },
      },
      {
        x: BRAIN_EPOCH_NUMBER + 10_000,
        y: [{ value: 3 }],
        metadata: {
          time: new Date(BRAIN_EPOCH_NUMBER + 10_000).toISOString(),
          count: BigInt(1),
        },
      },
      {
        x: BRAIN_EPOCH_NUMBER + 20_000,
        y: [null],
        metadata: {
          time: new Date(BRAIN_EPOCH_NUMBER + 20_000).toISOString(),
          count: BigInt(0),
        },
      },
      {
        x: BRAIN_EPOCH_NUMBER + 30_000,
        y: [{ value: -3 }],
        metadata: {
          time: new Date(BRAIN_EPOCH_NUMBER + 30_000).toISOString(),
          count: BigInt(1),
        },
      },
    ];
    const monitorMeta = [{ name: "series1" }];

    expect(metricsDataToTimeseriesData(monitorData, monitorMeta, 11)).toEqual({
      aggregates: [
        {
          count: 3,
          max: 42,
          min: -3,
          sum: 42,
        },
      ],
      seriesMetadata: [
        {
          name: "series1",
        },
      ],
      seriesValues: [new Float64Array([42, 3, NaN, -3])],
      timeMetadata: [
        {
          count: BigInt(7),
          time: "2020-01-01T00:00:00.000Z",
        },
        {
          count: BigInt(1),
          time: "2020-01-01T00:00:10.000Z",
        },
        {
          count: BigInt(0),
          time: "2020-01-01T00:00:20.000Z",
        },
        {
          count: BigInt(1),
          time: "2020-01-01T00:00:30.000Z",
        },
      ],
      timestamps: [
        1577836800000,
        1577836800000 + 10_000,
        1577836800000 + 20_000,
        1577836800000 + 30_000,
      ],
      timeBucketDuration: 11,
    });
  });
});
