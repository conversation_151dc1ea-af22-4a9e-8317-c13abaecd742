import { cn } from "#/utils/classnames";
import { DEFAULT_COLOR_CLASSNAME } from "#/ui/color";

export function calculateSeriesClassName({
  colorClassName,
  isDimmed,
  isHidden,
}: {
  colorClassName: string;
  isDimmed: boolean;
  isHidden: boolean;
}) {
  return cn(
    "transition-opacity",
    DEFAULT_COLOR_CLASSNAME,
    {
      "opacity-30": isDimmed,
      "opacity-0": isHidden,
    },
    colorClassName,
  );
}
