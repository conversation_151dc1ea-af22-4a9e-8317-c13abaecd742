import { z } from "zod";
import { transformMetricName } from "#/ui/type-formatters/metrics";

export const selectionTypesEnum = z.enum([
  "none",
  "score",
  "metric",
  "metadata",

  // we should probably deprecate score/metric and use these
  // "scores",
  // "metrics"
]);
export const selectionTypeSchema = z.object({
  value: z.string(),
  type: selectionTypesEnum,
});

export type SelectionTypesEnum = z.infer<typeof selectionTypesEnum>;
export type SelectionType = z.infer<typeof selectionTypeSchema>;

export function isEqualSelectionType(a: SelectionType, b: SelectionType) {
  return a.type === b.type && a.value === b.value;
}

const SELECTION_TYPE_ORDER = {
  none: 0,
  score: 1,
  metric: 2,
  metadata: 3,
};
export function compareSelectionTypes(
  a: SelectionTypesEnum,
  b: SelectionTypesEnum,
) {
  return SELECTION_TYPE_ORDER[a] - SELECTION_TYPE_ORDER[b];
}

export function formatSelectionTypeName(s: SelectionType): string {
  switch (s.type) {
    case "metric": {
      switch (s.value) {
        case "duration":
        default:
          return `${transformMetricName(s.value)} (avg)`;
      }
    }
    case "metadata": {
      return metadataToString(s.value);
    }
    default:
      return s.value;
  }
}

export function metadataToString(metadata: string): string {
  try {
    const parsed = JSON.parse(metadata);
    return `${parsed.at(-1)}`;
  } catch {
    return metadata;
  }
}

export function formatValueForSelectionType(
  value: number | string,
  s: SelectionType,
) {
  if (typeof value === "string") {
    return value;
  }

  switch (s.type) {
    case "metadata": {
      return value.toLocaleString(undefined, {
        maximumFractionDigits: 1,
      });
    }
    case "score": {
      return value.toLocaleString(undefined, {
        style: "percent",
        maximumFractionDigits: 0,
      });
    }
    case "metric": {
      switch (s.value) {
        case "llm_duration":
        case "duration":
          return (
            value.toLocaleString(undefined, {
              maximumFractionDigits: 1,
            }) + "s"
          );
        case "cost":
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
          }).format(value);
        default:
        // fall through
      }
    }
    default: {
      return value.toLocaleString(undefined, {
        maximumFractionDigits: 1,
      });
    }
  }
}

export const GROUP_BY_NONE_VALUE = "__bt_none";
export const GROUP_BY_NONE: SelectionType = {
  value: GROUP_BY_NONE_VALUE,
  type: "none",
};

export const GROUP_BY_SCORE: SelectionType = {
  value: "__bt_score",
  type: "none",
};

export const X_AXIS_EXPERIMENT: SelectionType = {
  value: "__bt_experiment",
  type: "none",
};
export const X_AXIS_TIME: SelectionType = {
  value: "__bt_time",
  type: "none",
};

export const X_AXIS_COMPARISON: SelectionType = {
  value: "__bt_score_comparison",
  type: "none",
};

export const BASE_X_AXIS_OPTIONS = [
  {
    label: "Experiment",
    value: selectionTypeKey(X_AXIS_EXPERIMENT),
    selectionType: X_AXIS_EXPERIMENT,
  },
  {
    label: "Score comparison",
    value: selectionTypeKey(X_AXIS_COMPARISON),
    selectionType: X_AXIS_COMPARISON,
  },
  {
    label: "Time",
    value: selectionTypeKey(X_AXIS_TIME),
    selectionType: X_AXIS_TIME,
  },
];

// needed for combobox keys since they are string only
export function selectionTypeKey(s: SelectionType) {
  return `__bt_${s.type}_${s.value}`;
}

export function isGroupByYMetric(
  colorGrouping: SelectionType,
  xMetric: SelectionType,
) {
  return (
    isEqualSelectionType(colorGrouping, GROUP_BY_SCORE) &&
    (isBaseXMetric(xMetric) || isCategoricalXMetric(xMetric))
  );
}

export function isBaseXMetric(xMetric: SelectionType) {
  return BASE_X_AXIS_OPTIONS.some(({ selectionType }) =>
    isEqualSelectionType(xMetric, selectionType),
  );
}

export function isTimeBasedXMetric(xMetric: SelectionType) {
  return [X_AXIS_EXPERIMENT, X_AXIS_TIME].some((selectionType) =>
    isEqualSelectionType(xMetric, selectionType),
  );
}

export function isCategoricalXMetric(xMetric: SelectionType) {
  return xMetric.type === "metadata";
}
