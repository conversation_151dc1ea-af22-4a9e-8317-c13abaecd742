import type React from "react";
import { useState, useEffect, useCallback } from "react";

/**
 * Watch width and height of given div ref
 * if borderBox is true, use derive dimensions from borderBoxSize (which includes padding and border) instead of contentRect
 */
export function useResizeObserver(
  ref: React.RefObject<HTMLElement | null> | undefined,
  borderBox: boolean = false,
) {
  const [dimensions, setDimensions] = useState({
    width: 0,
    height: 0,
  });

  const handleResize = useCallback(
    (entries: ResizeObserverEntry[]) => {
      if (!entries || entries.length === 0) {
        return;
      }
      const entry = entries[0];
      if (borderBox) {
        const { inlineSize, blockSize } = entry.borderBoxSize[0];
        setDimensions({ width: inlineSize, height: blockSize });
      } else {
        const { width, height } = entry.contentRect;
        setDimensions({ width, height });
      }
    },
    [borderBox],
  );

  useEffect(() => {
    if (!ref || !ref.current) {
      return;
    }

    const observer = new ResizeObserver(handleResize);
    observer.observe(ref.current);
    return () => {
      observer.disconnect();
    };
  }, [ref, handleResize]);

  return dimensions;
}
