import { type ChartSize, type ChartPadding } from "#/ui/charts/chart.types";
import { LEGEND_ENTRY_HEIGHT } from "#/ui/charts/timeseries-legend/timeseries-legend.constants";
import { type ResponsiveFeatures } from "./get-responsive-features";

const Y_AXIS_WIDTH = 40;
export const X_AXIS_HEIGHT = 25;

const BOTTOM_LEGEND_MAX_HEIGHT = 800;
const BOTTOM_LEGEND_MIN_HEIGHT = 15;

export const calcPadding = (
  chartSize: ChartSize,
  features: ResponsiveFeatures,
): { padding: ChartPadding; bottomLegendHeight: number } => {
  const { height } = chartSize;
  const { hasYAxis, hasXAxis, hasBottomLegend } = features;

  const top = 0;
  const right = 0;
  const left = hasYAxis ? Y_AXIS_WIDTH : 0;
  const bottomAxis = hasXAxis ? X_AXIS_HEIGHT : 0;
  const bottomLegendHeight = hasBottomLegend
    ? Math.min(
        BOTTOM_LEGEND_MAX_HEIGHT,
        Math.max(BOTTOM_LEGEND_MIN_HEIGHT, (height - bottomAxis) * 0.4),
      )
    : 0;
  const bottom = bottomAxis + bottomLegendHeight;

  const padding = {
    top,
    right,
    bottom,
    left,
  };

  return { padding, bottomLegendHeight };
};

export const calcPaddingStandalone = (
  chartSize: ChartSize,
  features: ResponsiveFeatures,
  numLegendSeries: number,
  maxYTickWidth: number | null,
): { padding: ChartPadding; bottomLegendHeight: number } => {
  const { height } = chartSize;
  const { hasYAxis, hasXAxis, hasBottomLegend } = features;

  const top = 0;
  const right = 0;
  const left = maxYTickWidth ?? (hasYAxis ? Y_AXIS_WIDTH : 0);
  const bottomAxis = hasXAxis ? X_AXIS_HEIGHT : 0;

  const targetLegendHeight = numLegendSeries * LEGEND_ENTRY_HEIGHT;

  const bottomLegendHeight = hasBottomLegend
    ? Math.min(
        BOTTOM_LEGEND_MAX_HEIGHT,
        LEGEND_ENTRY_HEIGHT *
          Math.ceil(((height - bottomAxis) * 0.4) / LEGEND_ENTRY_HEIGHT),
        targetLegendHeight,
      )
    : 0;
  const bottom = bottomAxis + bottomLegendHeight;

  const padding = {
    top,
    right,
    bottom,
    left,
  };

  return { padding, bottomLegendHeight };
};

export const calcVizSize = (
  chartSize: ChartSize,
  chartPadding: ChartPadding,
): ChartSize => {
  return {
    width: chartSize.width - chartPadding.left - chartPadding.right,
    height: chartSize.height - chartPadding.top - chartPadding.bottom,
  };
};

export { ChartPadding };
