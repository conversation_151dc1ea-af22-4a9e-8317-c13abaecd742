import { cn } from "#/utils/classnames";
import * as d3 from "d3";

export const D3_SYMBOLS = d3.symbolsFill.map(
  (symbolType) => () => d3.symbol(symbolType)(),
);

export const ChartSymbol = ({
  className,
  index,
  size: _size,
  isFocused,
  x,
  y,
}: {
  className?: string;
  index: number;
  size: number;
  isFocused?: boolean;
  x?: number;
  y?: number;
}) => {
  const size = (isFocused ? Math.max(_size + 5, 8) : _size) + 8;
  const path = D3_SYMBOLS[index % D3_SYMBOLS.length]();

  if (!path) return null;

  const translate =
    x == null && y == null
      ? index === 5
        ? // triangle is drawn a little higher than the rest of the icons for some reason
          "translate(10px, 12px)"
        : // symbols are drawn at 0,0, so translate them to the center
          "translate(10px, 10px)"
      : undefined;

  return (
    <svg
      className="overflow-visible"
      viewBox="0 0 20 20"
      height={size}
      width={size}
      x={x}
      y={y}
    >
      <path
        className={cn(className, "stroke-background stroke-3", {
          "stroke-4": isFocused,
        })}
        style={{
          paintOrder: "stroke",
          // a little bit of a hack:
          // our charts are flipped so that data manipulation in d3 is easier
          // when x and y are provided, that means this icon is probably in the chart
          // so we need to flip the icon to be rightside-up
          transform: translate ?? "scaleY(-1)",
        }}
        d={path}
      />
    </svg>
  );
};
