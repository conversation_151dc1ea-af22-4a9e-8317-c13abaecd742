import { useState, useRef, useLayoutEffect } from "react";
import { z } from "zod";

export const annotationDataSchema = z.object({
  id: z.string(),
  text: z.string(),
});

export const annotationsSchema = z.array(annotationDataSchema);

export type AnnotationData = z.infer<typeof annotationDataSchema>;
export type Annotations = z.infer<typeof annotationsSchema>;

export function Annotation({
  x,
  y,
  onMouseMove,
  children,
}: {
  x?: number;
  y: number;
  onMouseMove: React.MouseEventHandler;
  children: React.ReactNode;
}) {
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
  }>();
  const ref = useRef<HTMLDivElement>(null);
  useLayoutEffect(() => {
    if (!ref.current) {
      return;
    }

    function handleResize(entries: ResizeObserverEntry[]) {
      for (const entry of entries) {
        setDimensions({
          width: entry.target.clientWidth,
          height: entry.target.clientHeight,
        });
      }
    }
    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(ref.current);

    setDimensions({
      width: ref.current.offsetWidth,
      height: ref.current.offsetHeight,
    });

    return () => {
      resizeObserver.disconnect();
    };
  }, [setDimensions]);
  if (x == null) {
    return null;
  }

  const height = dimensions?.height ?? 1;
  const width = dimensions?.width ?? 1;
  return (
    <>
      <foreignObject
        className="overflow-visible"
        height={height}
        width={width}
        x={x - width / 2}
        y={y}
      >
        <div ref={ref} className="w-min" onMouseMove={onMouseMove}>
          {children}
        </div>
      </foreignObject>
    </>
  );
}
