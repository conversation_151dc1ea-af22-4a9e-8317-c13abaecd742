import type { XScale } from "#/ui/charts/chart.types";

export const isTimestampWithinXScale = (timestamp: number, xScale: XScale) => {
  // consider only valid for scale linear
  if (!("ticks" in xScale)) {
    return false;
  }
  // hack discard low timestamps
  if (timestamp < 1e6) {
    return false;
  }
  const domain = xScale.domain();
  return timestamp >= domain[0] && timestamp < domain[1];
};
