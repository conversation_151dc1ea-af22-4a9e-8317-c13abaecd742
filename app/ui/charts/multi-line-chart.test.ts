import { fillEmptyDataPoints } from "#/ui/charts/multi-line-chart";
import { scaleTime } from "#/ui/charts/scale-time";

import { describe, expect, test } from "vitest";

describe("fillEmptyDataPoints", () => {
  test("fills in missing data points", () => {
    const result = fillEmptyDataPoints({
      data: [
        {
          x: 1727827200000,
          y: [{ value: 1 }, null],
          metadata: { empty: true },
        },
        {
          x: 1727924400000,
          y: [{ value: 200 }, { value: 300 }],
          metadata: { empty: true },
        },
        // This x is not in the time range, but it will be snapped to the nearest time slot
        // which is 1727946000000
        {
          x: 1727946000001,
          y: [{ value: 400 }, { value: 500 }],
          metadata: { empty: true },
        },
        {
          x: 1728086400000,
          y: [null, { value: 600 }],
          metadata: { empty: true },
        },
      ],
      xTimeRange: {
        allXValues: scaleTime({
          startTime: "2024-10-02T00:55:22.999Z",
          timeBucket: "hour",
          totalDurationMS: 3 * 24 * 60 * 60 * 1000, // 3 days
        }),
        getEmptyMetadata: () => ({ empty: true }),
      },
      numberOfSeries: 2,
    });
    expect(result).toEqual([
      {
        x: 1727827200000,
        y: [{ value: 1 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727830800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727834400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727838000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727841600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727845200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727848800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727852400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727856000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727859600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727863200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727866800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727870400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727874000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727877600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727881200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727884800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727888400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727892000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727895600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727899200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727902800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727906400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727910000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727913600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727917200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727920800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727924400000,
        y: [{ value: 200 }, { value: 300 }],
        metadata: { empty: true },
      },
      {
        x: 1727928000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727931600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727935200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727938800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727942400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727946000001,
        y: [{ value: 400 }, { value: 500 }],
        metadata: { empty: true },
      },
      {
        x: 1727949600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727953200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727956800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727960400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727964000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727967600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727971200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727974800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727978400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727982000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727985600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727989200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727992800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1727996400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728000000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728003600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728007200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728010800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728014400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728018000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728021600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728025200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728028800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728032400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728036000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728039600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728043200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728046800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728050400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728054000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728057600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728061200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728064800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728068400000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728072000000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728075600000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728079200000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728082800000,
        y: [{ value: 0 }, { value: 0 }],
        metadata: { empty: true },
      },
      {
        x: 1728086400000,
        y: [{ value: 0 }, { value: 600 }],
        metadata: { empty: true },
      },
    ]);
  });
});
