import type { ScaleLinear, ScalePoint, ScaleBand } from "d3";
import type { SelectionType } from "./selectionTypes";

export type XScale =
  | ScaleLinear<number, number>
  | ScalePoint<string>
  | ScaleBand<SelectionType>;

export type YScale = ScaleLinear<number, number>;

export interface ChartSize {
  width: number;
  height: number;
}

export interface ChartPadding {
  top: number;
  right: number;
  bottom: number;
  left: number;
}
