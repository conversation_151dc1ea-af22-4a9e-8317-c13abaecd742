import { isGroupHighlighted, type HighlightState } from "#/ui/charts/highlight";
import { cn } from "#/utils/classnames";
import * as d3 from "d3";
import { type ScaleBand } from "d3";
import React, { useCallback, useMemo } from "react";
import { type Coords } from "./Chart";
import { DEFAULT_COLOR_CLASSNAME } from "#/ui/color";
import { useChartResponsiveWidth } from "./responsiveHooks";
import { TopRoundedRectangle } from "./rounded-rectangle";
import {
  isEqualSelectionType,
  type SelectionType,
  type SelectionTypesEnum,
} from "./selectionTypes";

type Data<Metadata> = {
  value: number | null;
  colorClassName: string;
  metadata: Metadata;
};

type GroupedData<T> = Record<
  string,
  Record<
    SelectionTypesEnum,
    Record<string, Record<SelectionTypesEnum, Data<T>>>
  >
>;

export type NearestBarplotItems = {
  group: SelectionType;
  subGroup: SelectionType;
};

export type GroupedBarplotProps<Metadata> = {
  height: number;
  data: GroupedData<Metadata>;
  yAxisBounds?: [number, number];
  categories: SelectionType[];
  subGroups: SelectionType[];
  // true when there is only one subgroup, where the subgroup is identical to the category
  noSubGroups?: boolean;
  highlightState?: HighlightState;
  valueRenderer: (
    value: number,
    group: SelectionType,
    subGroup: SelectionType,
  ) => string;
  renderTooltip?: (
    args: NearestBarplotItems & {
      d: Data<Metadata> | undefined;
    },
  ) => React.ReactNode;
};

export function useGroupedBarplot<T>({
  height,
  data,
  yAxisBounds,
  categories,
  subGroups,
  noSubGroups,
  highlightState,
  valueRenderer,
  renderTooltip,
}: GroupedBarplotProps<T>) {
  const { ref, boundsWidth, leftAxisWidth, onLeftAxisWidth } =
    useChartResponsiveWidth();

  const [xScale, xScaleSubGroup] = useMemo(() => {
    const scale = d3
      .scaleBand<SelectionType>()
      .domain(categories)
      .range([0, boundsWidth])
      .padding(0.1);
    const scaleSubGroup = d3
      .scaleBand<SelectionType>()
      .domain(subGroups)
      .paddingInner(0.1)
      .range([0, scale.bandwidth()]);
    return [scale, scaleSubGroup];
  }, [categories, boundsWidth, subGroups]);

  const yScale = useMemo(() => {
    const yValues: number[] = [];
    for (const groupTypes of Object.values(data)) {
      for (const groupValues of Object.values(groupTypes)) {
        for (const subGroupTypes of Object.values(groupValues)) {
          for (const subGroupValue of Object.values(subGroupTypes)) {
            if (subGroupValue.value != null) {
              yValues.push(subGroupValue.value);
            }
          }
        }
      }
    }
    const domain = yAxisBounds ?? [Math.min(...yValues), Math.max(...yValues)];
    return d3.scaleLinear().domain(domain).range([0, height]);
  }, [height, data, yAxisBounds]);

  const renderTooltipForData = useCallback(
    (nearestGroups: NearestBarplotItems | null) => {
      if (!renderTooltip || !nearestGroups || !nearestGroups?.group) return;

      const nearestSubGroup = noSubGroups
        ? nearestGroups.group
        : nearestGroups.subGroup;
      const d =
        data[nearestGroups.group.value]?.[nearestGroups.group.type]?.[
          nearestSubGroup.value
        ]?.[nearestSubGroup.type];
      return renderTooltip({
        group: nearestGroups.group,
        subGroup: nearestSubGroup,
        d,
      });
    },
    [renderTooltip, data, noSubGroups],
  );

  const getNearestGroups = useCallback(
    (coords: Coords) => {
      // mouse on the left axis doesn't trigger any actions
      if (coords.x == null || coords.x < 0) {
        return null;
      }
      const x = coords.x * xScale.range()[1];
      const group = scaleBandInvert(xScale, x);
      const subGroup = scaleBandInvert(xScaleSubGroup, x - xScale(group)!);
      return { group, subGroup };
    },
    [xScale, xScaleSubGroup],
  );

  const renderChartContent = useCallback(
    (nearestGroups: NearestBarplotItems | null) => {
      if (Object.keys(data).length === 0) return null;

      const chart = categories.map((group, i) =>
        (noSubGroups ? [group] : subGroups).map((subGroup, j) => {
          const barData =
            data[group.value]?.[group.type]?.[subGroup.value]?.[subGroup.type];
          const height = barData?.value != null ? yScale(barData.value) : null;

          const xGroup = xScale(group);
          const xSubGroup = noSubGroups ? 0 : xScaleSubGroup(subGroup);
          const width = noSubGroups
            ? xScale.bandwidth()
            : xScaleSubGroup.bandwidth();
          const isHighlighting = nearestGroups != null;
          const isFocused =
            nearestGroups != null &&
            isEqualSelectionType(group, nearestGroups.group) &&
            (noSubGroups ||
              isEqualSelectionType(subGroup, nearestGroups.subGroup));

          const isColored = calculateIsColored({
            isHighlighted: isGroupHighlighted(
              group,
              subGroup,
              noSubGroups ?? false,
              highlightState,
            ),
            isFocused,
            isHighlighting,
          });

          return (
            <g key={`${i}-${j}`} transform={`translate(${xGroup})`}>
              {height == null ? (
                <rect
                  className={cn(DEFAULT_COLOR_CLASSNAME, "stroke-0")}
                  x={xSubGroup}
                  width={width}
                  y={0}
                  height={2}
                />
              ) : (
                <>
                  {barData?.value != null && width > 32 && (
                    <BarLabel
                      x={xSubGroup!}
                      y={height}
                      maxWidth={width}
                      value={
                        valueRenderer
                          ? valueRenderer(barData.value, group, subGroup)
                          : barData.value
                      }
                    />
                  )}
                  <TopRoundedRectangle
                    className={cn("transition-opacity", {
                      [barData?.colorClassName ?? ""]: true,
                      ["opacity-50"]: !isColored,
                      //[barData.colorClassName]: isColored,
                      //[cn(DEFAULT_COLOR_CLASSNAME, "opacity-30")]: !isColored,
                      "stroke-transparent": true,
                    })}
                    x={xSubGroup!}
                    y={0}
                    width={width}
                    height={height}
                  />
                </>
              )}
            </g>
          );
        }),
      );

      return chart;
    },
    [
      data,
      xScale,
      xScaleSubGroup,
      yScale,
      categories,
      subGroups,
      noSubGroups,
      highlightState,
      valueRenderer,
    ],
  );

  return {
    chartProps: {
      chartRef: ref,
      height,
      leftAxisWidth,
      renderChartContent,
      renderTooltip: renderTooltipForData,
      getNearestItems: getNearestGroups,
      xScale,
      yScale,
      //onClick: onPointsClick,
      //onMouseMove: onChartHover,
    },
    leftAxisProps: {
      onLeftAxisWidth,
      xScale,
      yScale,
    },
    bottomAxisProps: {
      xScale,
      yScale,
    },
  };
}

function scaleBandInvert(xScale: ScaleBand<SelectionType>, x: number) {
  // left padding is step * paddingOuter
  // https://observablehq.com/@d3/d3-scaleband#cell-402
  const leftPadding = xScale.paddingOuter() * xScale.step();
  const index = Math.trunc((x - leftPadding) / xScale.step());
  const domain = xScale.domain();
  return domain[Math.max(0, Math.min(index, domain.length - 1))];
}

function calculateIsColored({
  isHighlighted,
  isHighlighting,
  isFocused,
}: {
  isHighlighted: boolean;
  isHighlighting: boolean;
  isFocused: boolean;
}) {
  if (isHighlighted) {
    return true;
  }

  if (!isHighlighting) {
    return true;
  }
  return isFocused;
}

const BAR_LABEL_HEIGHT = 20;

function BarLabel({
  x,
  y,
  maxWidth,
  value,
}: {
  x: number;
  y: number;
  maxWidth: number;
  value: number | string;
}) {
  return (
    <g
      style={{
        transform: `scaleY(-1)`,
      }}
    >
      <foreignObject
        x={x}
        y={-y - BAR_LABEL_HEIGHT}
        width={maxWidth}
        height={BAR_LABEL_HEIGHT}
      >
        <div
          className="truncate text-center text-[10px] text-primary-500"
          style={{
            maxWidth,
          }}
        >
          {value}
        </div>
      </foreignObject>
    </g>
  );
}
