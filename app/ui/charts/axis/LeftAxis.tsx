import { useMemo, useState } from "react";
import type { XScale, YScale } from "#/ui/charts/chart.types";

/**
 * LeftAxis.tsx
 */

export type LeftAxisProps = {
  onLeftAxisWidth?: (leftAxisWidth: number) => void;
  xScale: XScale;
  yScale: YScale;
  tickCount?: number;
  showGridLines?: boolean;
  tickFormatter?: (v: number, i: number) => string | null;
  label?: string;
  isResponsiveTimeseries?: boolean;
};

const LEFT_AXIS_SPACING = 10;

export const LeftAxis = ({
  onLeftAxisWidth,
  xScale,
  yScale,
  tickCount = 5,
  showGridLines,
  tickFormatter,
  label,
  isResponsiveTimeseries,
}: LeftAxisProps) => {
  const rangeX = xScale.range();
  const rangeY = yScale.range();
  const width = rangeX[1] - rangeX[0];
  const height = rangeY[1] - rangeY[0];

  const ticks = useMemo(() => {
    return yScale.ticks(tickCount).map((tick, i) => ({
      value: tickFormatter ? tickFormatter(tick, i) : tick,
      yOffset: isResponsiveTimeseries ? yScale(tick) : height - yScale(tick),
    }));
  }, [height, yScale, tickCount, tickFormatter, isResponsiveTimeseries]);

  // get the width of just the non-label portion so we can position the label properly
  const [valuesWidth, setValuesWidth] = useState<number | null>(null);
  return (
    <>
      {/* separate out the left axis labels and the gridlines so we can get the width on the left axis only */}
      <g
        ref={(node) => {
          const leftAxisWidth = node ? node.getBBox().width : 0;
          onLeftAxisWidth && onLeftAxisWidth(leftAxisWidth);
        }}
        className="text-primary-500"
        transform={`translate(-${isResponsiveTimeseries ? 0 : LEFT_AXIS_SPACING})`}
      >
        {label && valuesWidth != null && (
          <text
            className="fill-primary-500 text-xs"
            style={{
              writingMode: "vertical-rl",
              textAnchor: "middle",
              transform: `translate(-${valuesWidth + 5}px, ${Math.round(
                height / 2,
              )}px) rotate(180deg)`,
            }}
          >
            {label}
          </text>
        )}
        <g
          ref={(node) => {
            if (node) {
              setValuesWidth(node.getBBox().width);
            }
          }}
        >
          {ticks.map(({ value, yOffset }, i) => (
            <g
              key={i}
              transform={`translate(0, ${yOffset})`}
              shapeRendering={"crispEdges"}
            >
              <text
                className="tabular-nums"
                x={isResponsiveTimeseries ? "100%" : 0}
                y={3}
                style={{
                  fontSize: "10px",
                  textAnchor: "end",
                  fill: "currentColor",
                }}
              >
                {value}
              </text>
            </g>
          ))}
          {/* spacer between axis values and start of chart so that
        the width of this component calculates more accurately */}
          <rect
            width={isResponsiveTimeseries ? 0 : LEFT_AXIS_SPACING}
            height={1}
            fillOpacity="0"
          />
        </g>
      </g>
      {showGridLines &&
        ticks.map(({ yOffset }, i) => (
          <g key={i} transform={`translate(0, ${yOffset})`}>
            <line
              x1={0}
              x2={width}
              stroke="currentColor"
              strokeWidth={0.5}
              opacity={0.2}
            />
          </g>
        ))}
    </>
  );
};
