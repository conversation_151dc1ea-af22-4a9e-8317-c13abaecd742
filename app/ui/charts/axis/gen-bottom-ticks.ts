import { scaleTime, scaleUtc } from "d3";

import { type XScale } from "#/ui/charts/chart.types";

interface GenBottomTicksProps {
  xScale: XScale;
  isTimeFrame: boolean;
  tickCount: number;
  shiftFirstTickLabel?: boolean;
  tickFormatter?: (v: Date | number, i: number) => string | null;
  tzUTC?: boolean;
}

export const genBottomTicks = (props: GenBottomTicksProps) => {
  const {
    xScale,
    isTimeFrame,
    tickCount,
    shiftFirstTickLabel,
    tickFormatter,
    tzUTC,
  } = props;
  const domain = xScale.domain();
  if (
    isTimeFrame &&
    (typeof domain[0] == "string" || typeof domain[0] === "number") &&
    (typeof domain[1] == "string" || typeof domain[1] === "number")
  ) {
    // normalize extents to epoch numbers
    const extents = [
      typeof domain[0] === "string" ? new Date(domain[0]).getTime() : domain[0],
      typeof domain[1] === "string" ? new Date(domain[1]).getTime() : domain[1],
    ];

    const range = xScale.range();
    const xScaleTime = tzUTC
      ? scaleUtc(extents, range)
      : scaleTime(extents, range);
    const ticksFormatter = xScaleTime.tickFormat();
    return xScaleTime.ticks(tickCount).map((tick, i, arr) => ({
      value: ticksFormatter(tick),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      xOffset: xScale(tick.getTime() as any) ?? 0,
      textAnchor: calculateTextAnchor(
        i,
        arr.length,
        tick.getTime(),
        xScaleTime.domain().map((d) => d.getTime()),
        shiftFirstTickLabel,
      ),
    }));
  }

  // other numerical scales
  if ("ticks" in xScale) {
    return xScale.ticks(tickCount).map((tick, i, arr) => ({
      value: tickFormatter ? tickFormatter(tick, i) : null,
      textAnchor: calculateTextAnchor(
        i,
        arr.length,
        tick,
        xScale.domain(),
        shiftFirstTickLabel,
      ),
      xOffset: xScale(tick),
    }));
  }

  // scaleBand used for categorical charts
  return xScale.domain().map((v) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const offset = xScale(v as any);
    const value = typeof v === "string" ? v : v.value;
    return {
      value,
      xOffset: (offset != null ? offset : 0) + xScale.bandwidth() / 2,
      maxWidth: xScale.bandwidth() === 0 ? xScale.step() : xScale.bandwidth(),
      textAnchor: "middle",
    };
  });
};

function calculateTextAnchor(
  i: number,
  tickCount: number,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  value: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  domainX: any[],
  shiftFirstTickLabel?: boolean,
) {
  if (tickCount <= 1) {
    return "middle";
  }
  return i === 0 && shiftFirstTickLabel && domainX[0] === value
    ? "start"
    : i === tickCount - 1 && domainX[1] === value
      ? "end"
      : "middle";
}
