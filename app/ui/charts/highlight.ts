import { type Point } from "#/ui/charts/multi-line-chart";
import { type DataPoint } from "#/ui/charts/scatterplot";
import {
  isEqualSelectionType,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import { useCallback, useState } from "react";

export type HighlightedGroupTypeEnum = "points" | "symbols";

export type HighlightedGroup = {
  groupVal: SelectionType;
  type: HighlightedGroupTypeEnum;
};

export type HighlightState = {
  selected: HighlightedGroup[];
  highlighted?: HighlightedGroup;
};

export const createHighlightState = (): HighlightState => ({
  selected: [],
});

export const isHighlightStateEmpty = (
  highlightState: HighlightState | undefined,
) => !Boolean(highlightState?.selected.length || highlightState?.highlighted);

export const isPointHighlighted = (
  point: Point,
  line: SelectionType,
  highlightState: HighlightState | undefined,
) =>
  Boolean(
    highlightState &&
      [...highlightState.selected, highlightState.highlighted].find(
        (highlighted) =>
          highlighted &&
          ((highlighted.type === "points" &&
            isEqualSelectionType(highlighted.groupVal, line)) ||
            (highlighted.type === "symbols" &&
              point.symbolGroup &&
              isEqualSelectionType(highlighted.groupVal, point.symbolGroup))),
      ),
  );

export const isDatapointHighlighted = <T>(
  d: DataPoint<T>,
  highlightState: HighlightState | undefined,
) =>
  Boolean(
    highlightState &&
      [...highlightState.selected, highlightState.highlighted].find(
        (highlight) =>
          highlight &&
          ((highlight.type === "symbols" &&
            isEqualSelectionType(d.symbolGroup, highlight.groupVal)) ||
            (highlight.type === "points" &&
              isEqualSelectionType(d.colorGroup, highlight.groupVal))),
      ),
  );

export const isLineHighlighted = (
  line: SelectionType,
  highlightState: HighlightState | undefined,
) =>
  Boolean(
    !highlightState ||
      isHighlightStateEmpty(highlightState) ||
      (highlightState.highlighted &&
        isEqualSelectionType(highlightState.highlighted.groupVal, line)),
  );

export const isLineSelected = (
  line: SelectionType,
  highlightState: HighlightState | undefined,
) =>
  Boolean(
    !highlightState ||
      highlightState.selected.length === 0 ||
      highlightState.selected.find(
        (highlighted) =>
          highlighted && isEqualSelectionType(highlighted.groupVal, line),
      ),
  );

export const isGroupHighlighted = (
  group: SelectionType,
  subGroup: SelectionType,
  noSubGroups: boolean,
  highlightState: HighlightState | undefined,
) =>
  Boolean(
    highlightState &&
      [highlightState.highlighted, ...highlightState.selected].find(
        (highlight) =>
          highlight &&
          (isEqualSelectionType(highlight.groupVal, subGroup) ||
            (noSubGroups && isEqualSelectionType(highlight.groupVal, group))),
      ),
  );

// TODO: use jotai

export type HighlightStateType = {
  state: HighlightState;
  setState: (v: HighlightState) => void;
  addSelected: (v: HighlightedGroup) => void;
  removeSelected: (v: HighlightedGroup) => void;
  setSelected: (v: HighlightedGroup) => void;
  clearSelected: VoidFunction;
  highlight: (v: HighlightedGroup) => void;
  clearHighlight: VoidFunction;
};

export const useHighlightState = (): HighlightStateType => {
  const [state, setState] = useState(createHighlightState());

  const highlight = useCallback(
    (v: HighlightedGroup) => {
      setState({ ...state, highlighted: v });
    },
    [state, setState],
  );

  const clearHighlight = useCallback(() => {
    setState({ ...state, highlighted: undefined });
  }, [state, setState]);

  const addSelected = useCallback(
    (v: HighlightedGroup) => {
      setState({ ...state, selected: [...state.selected, v] });
    },
    [state, setState],
  );

  const removeSelected = useCallback(
    (v: HighlightedGroup) => {
      setState({
        highlighted: undefined,
        selected: state.selected.filter(
          (s) => !isEqualSelectionType(v.groupVal, s.groupVal),
        ),
      });
    },
    [state, setState],
  );

  const setSelected = useCallback(
    (v: HighlightedGroup) => {
      // allow the user to toggle to clear selections
      if (
        state.selected.length === 1 &&
        isEqualSelectionType(state.selected[0].groupVal, v.groupVal)
      ) {
        setState({ ...state, selected: [] });
      } else {
        setState({ ...state, selected: [v] });
      }
    },
    [state, setState],
  );

  const clearSelected = useCallback(() => {
    setState({ ...state, selected: [] });
  }, [state, setState]);

  return {
    state,
    setState,
    highlight,
    clearHighlight,
    clearSelected,
    addSelected,
    removeSelected,
    setSelected,
  } as const;
};
