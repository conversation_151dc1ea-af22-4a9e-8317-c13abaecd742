import { atom } from "jotai";

const _globalTimeCursorAtom = atom(0);

export const globalTimeCursorAtom = atom((get) => get(_globalTimeCursorAtom));
export const setGlobalTimeCursorAtom = atom(
  null,
  (get, set, timeStamp: number) => {
    set(_globalTimeCursorAtom, timeStamp);
  },
);

const _globalDisableInteractivityAtom = atom(false);

export const globalDisableInteractivityAtom = atom((get) =>
  get(_globalDisableInteractivityAtom),
);
export const setGlobalDisableInteractivityAtom = atom(
  null,
  (get, set, disable: boolean) => {
    set(_globalDisableInteractivityAtom, disable);
  },
);
