import { cn } from "#/utils/classnames";
import { ChartSymbol } from "#/ui/charts/symbols";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";
import {
  CHART_COLOR_CLASSNAMES,
  DEFAULT_COLOR_CLASSNAME,
  getColorForIndex,
  TIMESERIES_COLOR_CLASSNAMES,
} from "#/ui/color";

import { type TimeseriesLegendLabelData } from "./timeseries-legend.types";

export const TimeseriesLegendLabel = (
  props: TimeseriesLegendLabelData & {
    className?: string;
    contentClassName?: string;
    onClick?: React.MouseEventHandler;
    onMouseEnter?: React.MouseEventHandler;
    onMouseLeave?: React.MouseEventHandler;
    vizType: TimeseriesVizType;
  },
) => {
  const {
    className,
    contentClassName,
    label,
    onClick,
    onMouseEnter,
    onMouseLeave,
    formattedValue,
    vizType,
  } = props;
  const symbol = (
    <ChartSymbol
      className={cn(
        getColorForIndex(
          props.colorIndex,
          vizType === "bars"
            ? TIMESERIES_COLOR_CLASSNAMES
            : CHART_COLOR_CLASSNAMES,
        ) || DEFAULT_COLOR_CLASSNAME,
        className,
        contentClassName,
      )}
      // use square for bars and circles for lines
      index={vizType === "bars" ? 3 : 0}
      size={8}
    />
  );

  return (
    <div
      className={cn("flex items-center gap-1", className)}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {symbol}
      <div
        className={cn("flex-1 truncate text-xs", contentClassName)}
        title={label}
      >
        {label}
      </div>
      <div
        className={cn(
          "flex-none text-xs font-semibold text-primary-900 tabular-nums",
          contentClassName,
        )}
      >
        {formattedValue}
      </div>
    </div>
  );
};
