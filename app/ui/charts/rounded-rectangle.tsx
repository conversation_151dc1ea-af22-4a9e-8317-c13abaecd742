// https://stackoverflow.com/questions/12115691/svg-d3-js-rounded-corners-on-one-side-of-a-rectangle
export function TopRoundedRectangle({
  className,
  x,
  y,
  width,
  height: _height,
  radius = 2,
}: {
  className?: string;
  x: number;
  y: number;
  width: number;
  height: number;
  radius?: number;
}) {
  // since the arc radius is N px this has to be at least N pixels high
  const height = Math.max(_height, radius);
  if (width <= 2) {
    return (
      <rect className={className} x={x} y={y} width={width} height={height} />
    );
  }

  return (
    <path
      className={className}
      d={`M ${x} ${y}
        v ${height - radius}
        a ${radius} ${radius} 0 0 0 ${radius} ${radius}
        h ${width - 2 * radius}
        a ${radius} ${radius} 0 0 0 ${radius} -${radius}
        v -${height - radius}
        z`}
    ></path>
  );
}
