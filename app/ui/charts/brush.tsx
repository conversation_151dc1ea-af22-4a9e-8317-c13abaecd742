import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import * as d3 from "d3";

export const useBrush = ({
  xScale,
  yScale,
  onBrush,
  // snap brush to tick for histogram data
  snapTicks,
  // default behavior of a single click is to clear the brush
  ignoreBrushClick,
  onBrushStart,
  onBrushEnd,
}: {
  xScale: d3.ScaleLinear<number, number>;
  yScale: d3.ScaleLinear<number, number>;
  onBrush?: (v: [number, number] | null) => void;
  onBrushStart?: () => void;
  onBrushEnd?: () => void; // end or cancel
  // snap brush to tick for histogram data
  snapTicks?: number[];
  ignoreBrushClick?: boolean;
}) => {
  const brushRef = useRef<SVGGElement>(null);

  // keep this in state in case we have to responsively re-render
  const [brushVal, _setBrushVal] = useState<[number, number] | null>(null);
  const setBrushVal = useCallback(
    (v: typeof brushVal) => {
      _setBrushVal(v);
      if (onBrush) {
        onBrush(v);
      }
    },
    [onBrush],
  );

  const { d3Brush, removeBrush } = useMemo(() => {
    const xRange = xScale.range();
    const yRange = yScale.range();

    const yMin = Math.min(yRange[0], yRange[1]);
    const yMax = Math.max(yRange[0], yRange[1]);
    const brush = d3.brushX().extent([
      [xRange[0], yMin],
      [xRange[1], yMax],
    ]);

    return {
      d3Brush: brush,
      removeBrush: () => {
        _setBrushVal(null);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        d3.select(brushRef.current).call(d3Brush.clear as any);
        onBrushEnd?.();
      },
    };
  }, [xScale, yScale, _setBrushVal, onBrushEnd]);

  useEffect(() => {
    if (!brushRef.current || !brushVal) {
      return;
    }

    d3.select(brushRef.current).call(
      d3Brush.move,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      brushVal.map(xScale) as [number, number],
    );
  }, [brushVal, d3Brush, xScale]);

  useEffect(() => {
    if (!brushRef.current) {
      return;
    }
    // disable dragging via box
    brushRef.current.style.pointerEvents = "none";
    d3.select(brushRef.current)
      .selectChild(".selection")
      .style("shape-rendering", "geometricPrecision")
      .style("rx", 3)
      .attr("class", "selection fill-primary-200 stroke-primary-400");

    // https://observablehq.com/@d3/brush-snapping
    function handleBrush(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      this: any,
      e: {
        type: string;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        sourceEvent: any;
        selection: [number, number];
        mode: string;
      },
    ) {
      // no source event means programmatic brush move (e.g. snapping to discrete points)
      if (!e.sourceEvent) return;
      if (!e.selection) {
        if (ignoreBrushClick) {
          removeBrush();
        } else {
          setBrushVal(null);
        }
        return;
      }

      if (!snapTicks) {
        if (e.selection && e.type === "end") {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          setBrushVal(e.selection.map(xScale.invert) as [number, number]);
          removeBrush();
        }
        return;
      }
      const domainSelection = e.selection.map(xScale.invert);
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      const snappedX = domainSelection.map((x) => {
        const index = d3.bisectCenter(snapTicks, x);
        return snapTicks[index];
      }) as [number, number];

      if (e.type === "end") {
        setBrushVal(snappedX);
        return;
      }
      d3.select(this).call(
        d3Brush.move,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        snappedX.map(xScale) as [number, number],
      );
    }

    d3Brush.on("brush end", (e) => {
      if (e.type === "end") {
        onBrushEnd?.();
      }
      handleBrush(e);
    });
    d3Brush.on("start", () => onBrushStart?.());
    d3.select(brushRef.current).call(d3Brush);
  }, [
    d3Brush,
    snapTicks,
    xScale,
    brushVal,
    setBrushVal,
    removeBrush,
    ignoreBrushClick,
    onBrushStart,
    onBrushEnd,
  ]);

  return { brushRef, removeBrush };
};
