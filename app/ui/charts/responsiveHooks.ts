import { useRef, useState, useEffect, useCallback } from "react";

export const useChartResponsiveWidth = () => {
  const [leftAxisWidth, setLeftAxisWidth] = useState<number>(0);
  const [boundsWidth, setBoundsWidth] = useState<number>(0);

  const ref = useRef<HTMLDivElement | null>(null);
  // use ref callbacks instead of useEffect in case the component is conditionally rendered
  const chartRefCallback = useCallback(
    (node: HTMLDivElement) => {
      ref.current = node;
      setBoundsWidth((node?.clientWidth ?? 0) - leftAxisWidth);

      function handleResize(entries: ResizeObserverEntry[]) {
        for (const entry of entries) {
          setBoundsWidth((entry.target.clientWidth ?? 0) - leftAxisWidth);
        }
      }
      const resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(node);

      return () => {
        ref.current = null;
        resizeObserver.disconnect();
      };
    },
    [setBoundsWidth, leftAxisWidth],
  );

  useEffect(() => {
    setBoundsWidth((ref.current?.clientWidth ?? 0) - leftAxisWidth);
  }, [leftAxisWidth]);

  return {
    ref: chartRefCallback,
    boundsWidth,
    leftAxisWidth,
    onLeftAxisWidth: setLeftAxisWidth,
  };
};
