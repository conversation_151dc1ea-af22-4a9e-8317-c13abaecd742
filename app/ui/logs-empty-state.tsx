import { TableEmptyState } from "./table/TableEmptyState";
import { TraceSetupInstructions } from "#/app/app/setup/[org]/trace/trace-setup-instructions";
import { WaitingIndicator } from "./waiting-indicator";
import { ExternalLink } from "./link";
import { useTraceSetupState } from "#/app/app/setup/[org]/trace/use-trace-setup";

export const LogsEmptyState = ({
  orgName,
  projectName,
}: {
  orgName: string;
  projectName: string;
}) => {
  const {
    selectedLanguage,
    selectedProvider,
    setSelectedLanguage,
    setSelectedProvider,
  } = useTraceSetupState();

  return (
    <TableEmptyState
      labelClassName="max-w-152 text-left"
      label={
        <div className="mb-8">
          <div className="mb-6 flex gap-2 text-xs text-primary-900">
            <WaitingIndicator task="logs" />
          </div>
          There are no logs in this project yet. Get started in just a few
          minutes with the instructions below or{" "}
          <ExternalLink href={"/docs/guides/logging"}>
            learn&nbsp;more
          </ExternalLink>
          .
        </div>
      }
    >
      <TraceSetupInstructions
        orgName={orgName}
        projectName={projectName}
        selectedLanguage={selectedLanguage}
        onLanguageChange={setSelectedLanguage}
        selectedProvider={selectedProvider}
        onProviderChange={setSelectedProvider}
      />
    </TableEmptyState>
  );
};
