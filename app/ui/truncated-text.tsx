"use client";

import {
  forwardRef,
  useRef,
  useState,
  useLayoutEffect,
  useImperativeHandle,
} from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";

const TruncatedText = forwardRef<
  HTMLDivElement,
  React.PropsWithoutRef<React.HTMLAttributes<HTMLDivElement>> & {
    className?: string;
    tooltipClassName?: string;
    showTooltip?: boolean;
  }
>(({ className, tooltipClassName, showTooltip, children, ...props }, ref) => {
  const divRef = useRef<HTMLDivElement>(null);
  useImperativeHandle(ref, () => divRef.current!, []);

  const [isTextTruncated, setIsTextTruncated] = useState(false);
  useLayoutEffect(() => {
    if (
      divRef.current &&
      divRef.current.offsetWidth < divRef.current.scrollWidth
    ) {
      setIsTextTruncated(true);
    }
  }, [setIsTextTruncated]);

  const triggerElem = (
    <div className={cn("truncate", className)} {...props} ref={divRef}>
      {children}
    </div>
  );

  if (!showTooltip) {
    return triggerElem;
  }

  return isTextTruncated ? (
    <Tooltip>
      <TooltipTrigger asChild>{triggerElem}</TooltipTrigger>
      <TooltipContent className={cn("max-w-sm py-2", tooltipClassName)}>
        {children}
      </TooltipContent>
    </Tooltip>
  ) : (
    triggerElem
  );
});

TruncatedText.displayName = "TruncatedText";
export { TruncatedText };
