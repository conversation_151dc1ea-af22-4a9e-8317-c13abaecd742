import { useEffect, useRef, useState } from "react";
import { Button } from "#/ui/button";
import { Loading } from "#/ui/loading";

export type InfiniteScrollFn = () => Promise<void>;

export default function InfiniteScroll({
  infiniteScroll,
  scrollContainerRef,
}: {
  infiniteScroll: InfiniteScrollFn;
  scrollContainerRef?: React.RefObject<HTMLElement | null>;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const currentlyLoading = useRef(false);

  const loadMoreButtonRef = useRef<HTMLButtonElement>(null);
  const observer = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    const buttonRef = loadMoreButtonRef.current;

    const checkIntersection = ([entry]: IntersectionObserverEntry[]) => {
      if (entry.isIntersecting && !isLoading && !currentlyLoading.current) {
        buttonRef?.click();
      }
    };

    observer.current = new IntersectionObserver(checkIntersection, {
      root: scrollContainerRef?.current ?? null,
      rootMargin: "500px",
      threshold: 0,
    });

    if (buttonRef && !isLoading) {
      observer.current.observe(buttonRef);
    }

    return () => {
      if (buttonRef) {
        observer.current?.unobserve(buttonRef);
      }
    };
  }, [scrollContainerRef, isLoading]);

  // Re-observe the button when loading state changes
  useEffect(() => {
    const buttonRef = loadMoreButtonRef.current;
    if (buttonRef) {
      if (isLoading) {
        observer.current?.unobserve(buttonRef);
      } else {
        observer.current?.observe(buttonRef);
      }
    }
  }, [isLoading]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      const buttonRef = loadMoreButtonRef.current;
      if (
        !buttonRef ||
        isLoading ||
        currentlyLoading.current ||
        buttonRef.getBoundingClientRect().top >= window.innerHeight
      ) {
        return;
      }
      buttonRef.click();
    }, 1000); // Poll every second to check if the button is visible

    return () => {
      clearInterval(intervalId);
    };
  }, [isLoading]);

  return (
    <div className="sticky left-0 mt-4 flex w-full grow justify-center">
      <Button
        size="sm"
        variant="ghost"
        ref={loadMoreButtonRef}
        disabled={isLoading}
        onClick={async (e) => {
          e.preventDefault();
          if (currentlyLoading.current) {
            return;
          }
          try {
            currentlyLoading.current = true;
            setIsLoading(true);
            await infiniteScroll();
          } finally {
            currentlyLoading.current = false;
            setIsLoading(false);
          }
        }}
      >
        <Loading className="size-4 flex-none" instant />
      </Button>
    </div>
  );
}
