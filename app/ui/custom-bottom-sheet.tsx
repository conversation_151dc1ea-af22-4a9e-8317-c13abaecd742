import { cn } from "#/utils/classnames";
import { type PropsWithChildren } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";

export const CustomBottomSheet = ({
  isOpen,
  className,
  children,
  onClose,
}: PropsWithChildren<{
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}>) => {
  const { enabledScopes } = useHotkeysContext();

  useHotkeys("Escape", onClose, {
    enabled: isOpen && !enabledScopes.includes("global-keyboard-shortcuts"),
    preventDefault: true,
  });

  return (
    <div className={cn("pointer-events-none fixed inset-0 z-50")}>
      <div
        className={cn(
          "pointer-events-none absolute inset-0 z-10 bg-black opacity-0 transition-opacity",
          {
            "pointer-events-auto opacity-40": isOpen,
          },
        )}
        onClick={onClose}
      />
      <div
        className={cn(
          "pointer-events-none absolute inset-0 z-50 flex translate-y-full flex-col justify-end transition-transform duration-100 ease-in-out",
          className,
          {
            "translate-y-0": isOpen,
          },
        )}
      >
        {children}
      </div>
    </div>
  );
};
