import {
  type DiffObjectType,
  type RowId,
  makeRowIdPrimary,
  createDiffObjectSchema,
  DiffRightField,
  DiffLeftField,
  getDiffRight,
  getDiffFieldIndex,
} from "#/utils/diffs/diff-objects";
import { cn } from "#/utils/classnames";
import { type PathTree } from "#/utils/display-paths";
import { type TransactionId } from "#/utils/duckdb";
import { makeViewsQueryKey, type ViewProps } from "#/utils/view/use-view";
import { isEmpty } from "#/utils/object";
import { type DuckDBJSONStruct } from "#/utils/schema";
import {
  type ClauseSpec,
  type Clause,
  type Search,
  makeBubble,
  type SortComparison,
  noopChecker,
} from "#/utils/search/search";
import "@tanstack/react-table";
import {
  type Cell,
  type CellContext,
  type Column,
  type ColumnDef,
  type ColumnSizingState,
  type ColumnSort,
  type ExpandedState,
  type Header,
  type OnChangeFn,
  type Row,
  type SortingState,
  type Table as TanstackTable,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  type Field,
  type Table as ArrowTableType,
  type TypeMap,
  Vector,
} from "apache-arrow";
import React, {
  type Dispatch,
  type MouseEventHandler,
  type RefObject,
  type ReactNode,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  memo,
} from "react";
import InfiniteScroll, { type InfiniteScrollFn } from "#/ui/infinite-scroll";
import { compileSort } from "#/utils/sql-utils";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

import {
  type MultilineRowProps,
  VirtualTableBody,
} from "#/ui/virtual-table-body";
import {
  useActiveRowAndSpan,
  useColumnOrderOverrideState,
  useDiffModeState,
} from "#/ui/query-parameters";
import { type MergedValue, type FormatterMap } from "./field-to-column";
import isEqual from "lodash.isequal";
import {
  FilterEditor,
  type MinimalColumnData,
  resolveDefaultColumnId,
} from "./table/filter-editor";
import { objectReferenceSchema } from "@braintrust/local";
import { produce } from "immer";
import { useIsMutating } from "@tanstack/react-query";
import { useOrg } from "#/utils/user";
import { TableSkeleton } from "./table/table-skeleton";
import {
  resolveColumnOrder,
  resolveOrderByRegressionColumnOrder,
} from "./column-utils";
import { type NestedField } from "@braintrust/local/query";
import {
  ColumnsMenu,
  type CustomColumnDialogVariant,
} from "./table/columns-menu";
import { type CustomColumnDef } from "./table/custom-column-form";
import { type RegressionFilter } from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import {
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
  KeyboardSensor,
  DragOverlay,
  closestCorners,
  type CollisionDetection,
  type DragStartEvent,
} from "@dnd-kit/core";
import { arrayMove, sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { type SummaryBreakdownData } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { calculateFieldHeights } from "./table/formatters/grid-layout-columns";
import { TableHeaders } from "./table/table-headers";
import {
  BT_GROUP_KEY,
  BT_IS_GROUP,
  BT_SUBROWS,
  groupRowsForTable,
} from "./table/grouping/queries";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useTraceFullscreen } from "./trace/use-trace-fullscreen";
import { type RowComparisonFormatterProps } from "./table/formatters/row-comparison-formatter";
import { SummaryTableLayout } from "./summary-table-layout";
import StyledDndContext from "#/ui/styled-dnd-context";
import { Button } from "./button";
import { ConfirmationDialog } from "./dialogs/confirmation";
import { type ColumnMeta } from "@tanstack/react-table";
import { sortGroupRowsWithSummary } from "./table/grouping/sorting";
import { type StreamingContentProps } from "./table/cells/streaming";
import { z } from "zod";
import useEvent from "react-use-event-hook";
import {
  RowComparisonColumnId,
  RowSelectionColumnId,
} from "./table/display-columns";
import { type UpdateRowFn } from "#/utils/mutable-object";
import useColumnDefs, {
  type FieldInfo,
  type SizeConstraintsMap,
} from "./table/use-column-defs";
import { makeColumnOptions } from "./table/columns/make-column-options";
import useActiveRowEffects from "./table/use-active-row-effects";
import useOpenSidePanel from "./table/use-open-side-panel";
import TableHotkeyManager from "./table/table-hotkey-manager";
import { ErrorBanner } from "./error-banner";
import { TRANSACTION_ID_FIELD } from "braintrust/util";
import useGridColumnDefs from "./table/use-grid-column-defs";
import { SINGLETON_DATASET_ID } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/stream";
import { type Virtualizer } from "@tanstack/react-virtual";
import { useColumnVisibility } from "./table/columns/use-column-visibility";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";
import { useGroupedRowIds } from "#/utils/grouping";
import { type MetricDefinition } from "@braintrust/local/api-schema";

// https://github.com/TanStack/table/issues/4240
const emptyArray: unknown[] = [];
const emptyRowIds: RowId[] = [];
const emptyFunction = () => {};

export interface FormatterProps<TsData, TsValue>
  extends CellContext<TsData, TsValue> {
  className?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  value: any;
  valueDiffObject?: DiffObjectType<unknown>;
  diffIndex?: number;
  mergedValues?: Record<string, MergedValue<TsData, TsValue>>;
  colorClassName?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  setValue?: (value: any) => void;
  updateRow?: UpdateRowFn;
  inTable?: boolean;
  hideNulls?: boolean;
  renderForTooltip?: (value: string | React.ReactNode) => React.ReactNode;
  meta: ColumnMeta<TsData, TsValue>;
}

export type RowGroupingData<TsData> = {
  // if this is undefined, then we highlight grouped rows instead of grouping the table data
  groupRows?: TsData[];
  groupBy: string;
  initialCollapseGroups?: boolean;
};

export type BatchUpdateRowFn = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  row: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  updates: { path: string[]; newValue: any }[],
) => Promise<TransactionId | null>;

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export type UpdateValueFn = (newValue: any) => Promise<TransactionId | null>;

export type CustomColumnProps = {
  options: Record<string, NestedField[]>;
  columns?: CustomColumnDefinition[];
  isEnabled: boolean;
  createColumn: (col: CustomColumnDef) => Promise<void>;
  deleteColumn: (columnName: string) => Promise<void>;
  updateColumn?: ({
    columnId,
    columnData,
  }: {
    columnId: string;
    columnData: CustomColumnDef;
  }) => Promise<void>;
  loading?: boolean;
  error?: Error | null;
};

export type ArrowTableUIProps<TsData, TsValue> = {
  rowIds?: RowId[];
  queriedRowIds?: RowId[];
  setRowIds?: Dispatch<SetStateAction<RowId[]>>;
  /**
   * Reorders initial (i.e. non-user determined) column order. This is useful
   * for scores, metrics, etc., which have sub columns, so the order can't be changed where data is queried.
   */
  columnReorderer?: (
    columns: ColumnDef<TsData, TsValue>[],
  ) => ColumnDef<TsData, TsValue>[];
  formatters?: FormatterMap<TsData, TsValue>;
  /**
   * 'detailed' table type has tooltip for cell to see full value
   */
  tableType?: "list" | "detailed";
  isLoading?: boolean;
  extraLeftControls?: ReactNode;
  extraRightControls?: ReactNode;
  beforeToolbarSlot?: ReactNode;
  toolbarSlot?: ReactNode;
  afterToolbarSlot?: ReactNode;
  initiallyVisibleColumns?: Record<string, boolean>;
  // hide column in table and column selection
  neverVisibleColumns?: Set<string>;
  // hide column in table without affecting column selection
  hiddenColumns?: Record<string, boolean>;
  // since its unlikely that grouping will be used here, remove it to make the typing work easier
  viewProps: Omit<ViewProps, "grouping" | "setGrouping">;
  error?: React.ReactNode;
  displayPaths?: PathTree;
  typeHints?: DuckDBJSONStruct;
  sizeConstraintsMap?: SizeConstraintsMap;
  rowComparisonProps?: RowComparisonFormatterProps;
  streamingContentProps?: StreamingContentProps;
  rowSelection?: Record<string, boolean> | null;
  setRowSelection?: OnChangeFn<Record<string, boolean>> | null;
  expandedState?: ExpandedState | null;
  setExpandedState?: OnChangeFn<ExpandedState> | null;
  pageSize?: number;
  tableRef?: RefObject<TanstackTable<TsData> | null>;
  updateRow?: UpdateRowFn;
  rowEvents?: Record<string, (row: Row<TsData>) => MouseEventHandler>;
  initiallyExpandedRow?: string;
  infiniteScroll?: InfiniteScrollFn;
  activeRowOnPageLoad?: RowId | null;
  hasNoRowsComponent?: ReactNode;
  scrollContainerRef?: RefObject<HTMLDivElement | null>;
  className?: string;
  isHumanReviewModeEnabled?: boolean;
  loadingColumns?: string[];
  experimentName?: string;
  experimentId?: string;
  multilineRow?: MultilineRowProps;
  rowGroupingData?: RowGroupingData<TsData> | null;
  comparisonExperiments?: {
    name: string;
    id: string;
  }[];
  addRegressionFilter?: (filter: RegressionFilter) => void;
  summaryBreakdownData?: SummaryBreakdownData;
  showRowNumber?: boolean;
  customColumns?: CustomColumnProps;
  objectId?: string | null;
  objectType?: string | null;
  enableStarColumn?: boolean;
  setAggregationExperimentId?: Dispatch<SetStateAction<string>>;
  skipEmptyTableState?: boolean;
  isPlayground?: boolean;
  isSortable?: boolean;
  isLoopEnabled?: boolean;
  disableFilters?: boolean;
  isReadOnly?: boolean;
  removeLimiter?: boolean;
  customColumnDialogVariant?: CustomColumnDialogVariant;
  setCustomColumnDialogVariant?: (variant?: CustomColumnDialogVariant) => void;
  disableNegativeTagFilters?: boolean;
  disabledFilterColumns?: string[];
  stickyBarClassName?: string;
  scoreNames?: string[];
  skipErrorReporting?: boolean | ((value: string) => boolean);
  refetchTooltipContentData?: (
    fieldName: string,
    rowId: string,
    previewLength?: number,
  ) => Promise<unknown>;
  summarySlots?: Record<string, React.ReactNode>;
  noRowsFoundLabel?: ReactNode;
  filterSuggestions?: { label: string; btql: string }[];
  enableScrollToColumn?: boolean;
  openRowId?: string | null;
  metricDefinitions?: MetricDefinition[];
  /** Content above the sticky bar which scrolls with the page. This is still shown in loading/empty states. */
  notStickySlot?: ReactNode;
  scrollMarginRef?: RefObject<HTMLDivElement | null>;
};

interface ArrowTableDataProps<TData extends TypeMap> {
  table: ArrowTableType<TData> | null;
  dataReplacementFn?: (table: ArrowTableType<TData>) => TData[];
}

type ArrowTableProps<
  TData extends TypeMap,
  TsData,
  TsValue,
> = ArrowTableDataProps<TData> & ArrowTableUIProps<TsData, TsValue>;

const searchSortToSortingState = (sort: Search["sort"]): SortingState => {
  return (
    sort
      ?.map((s) => {
        const col = s.spec?.col;
        if (!col) return undefined;
        return {
          id: col.id,
          desc: col.desc,
        };
      })
      .filter((c): c is ColumnSort => !!c) ?? []
  );
};

const flattenColumns = <TsData, TsValue>(
  columns: ColumnDef<TsData, TsValue>[] | undefined,
  acc: Array<{
    id: string;
    pinnedColumnIndex?: number;
    internalType?: string;
  }> = [],
) => {
  columns?.forEach((column) => {
    acc.push({
      id: `${column.id}`,
      pinnedColumnIndex: column.meta?.pinnedColumnIndex,
      internalType: column.meta?.internalType,
    });
    if ("columns" in column) {
      flattenColumns(column.columns, acc);
    }
  });
  return acc;
};

export type RowData = Record<string, unknown> & {
  id: string | DiffObjectType<string>;
  [BT_IS_GROUP]?: boolean;
  [BT_GROUP_KEY]?: string;
};

export function ArrowTable<
  TData extends TypeMap,
  TsData extends RowData,
  TsValue,
>(props: ArrowTableProps<TData, TsData, TsValue>) {
  const { table, dataReplacementFn } = props;
  // This memoization is important because it prevents the table from re-rendering
  // (in an infinite loop), because useReactTable() causes a re-render each time its
  // arguments change.
  const data = useMemo(() => {
    if (!table) return null;

    const result = dataReplacementFn
      ? dataReplacementFn(table)
      : table.toArray();
    return result;
  }, [table, dataReplacementFn]);

  return (
    <TableComponent
      {...props}
      data={data}
      fields={props.table?.schema.fields}
    />
  );
}

function TableComponent<
  TData extends TypeMap,
  TsData extends RowData,
  TsValue,
>({
  rowIds = emptyRowIds,
  queriedRowIds,
  setRowIds = emptyFunction,
  formatters,
  objectType,
  objectId,
  typeHints,
  tableType = "detailed",
  extraLeftControls,
  extraRightControls,
  beforeToolbarSlot,
  toolbarSlot,
  afterToolbarSlot,
  initiallyVisibleColumns,
  neverVisibleColumns,
  hiddenColumns,
  error: errorUI,
  isLoading,
  displayPaths,
  sizeConstraintsMap,
  rowComparisonProps,
  streamingContentProps,
  rowSelection,
  setRowSelection,
  expandedState,
  setExpandedState,
  tableRef,
  rowEvents,
  infiniteScroll,
  activeRowOnPageLoad,
  hasNoRowsComponent,
  viewProps,
  scrollContainerRef,
  className,
  isHumanReviewModeEnabled,
  multilineRow,
  rowGroupingData,
  comparisonExperiments,
  experimentName,
  experimentId,
  addRegressionFilter,
  loadingColumns,
  summaryBreakdownData,
  showRowNumber,
  customColumns,
  enableStarColumn,
  updateRow,
  setAggregationExperimentId,
  columnReorderer,
  isPlayground,
  isLoopEnabled,
  isSortable = true,
  data,
  fields,
  disableFilters,
  isReadOnly,
  removeLimiter,
  customColumnDialogVariant: customColumnDialogVariantProp,
  setCustomColumnDialogVariant: setCustomColumnDialogVariantProp,
  disableNegativeTagFilters,
  disabledFilterColumns,
  stickyBarClassName,
  scoreNames,
  skipErrorReporting = false,
  refetchTooltipContentData,
  noRowsFoundLabel = "No rows found",
  summarySlots,
  filterSuggestions,
  enableScrollToColumn,
  openRowId,
  metricDefinitions,
  notStickySlot,
  scrollMarginRef,
}: Omit<ArrowTableProps<TData, TsData, TsValue>, "table"> & {
  data: TsData[] | null;
  fields: Field<TData[keyof TData]>[] | undefined;
}) {
  const {
    isPending,
    pageIdentifier,
    viewParams,
    currentView,
    search,
    columnVisibility,
    columnOrder,
    columnSizing,
    setSearch,
    setColumnVisibility,
    setColumnOrder,
    setColumnSizing,
    clauseChecker,
  } = viewProps;

  const gridLayout = viewProps.layout === "grid";
  const summaryLayout = viewProps.layout === "summary";

  useActiveRowEffects({ rowIds, setRowIds });

  const { columns, fieldMap } = useColumnDefs<TData, TsData, TsValue>({
    fields,
    columnReorderer,
    customColumnsColumns: customColumns?.columns,
    displayPaths,
    enableStarColumn,
    formatters,
    multilineRow,
    neverVisibleColumns,
    rowComparisonProps,
    rowSelection,
    showRowNumber,
    sizeConstraintsMap,
    summaryBreakdownData,
    typeHints,
    updateRow,
    refetchTooltipContentData,
  });

  const tableHeaderRefs = useRef<(HTMLDivElement | null)[]>([]);

  const measureColumnsRef = useRef<() => void>(null);
  const measureRowsRef = useRef<() => void>(null);
  const scrollToIndexRef =
    useRef<Virtualizer<HTMLDivElement, Element>["scrollToIndex"]>(null);
  const onColumnSizingChange: OnChangeFn<ColumnSizingState> = (value) => {
    setColumnSizing(value);
    measureRowsRef.current?.();
    measureColumnsRef.current?.();
  };

  useEffect(() => {
    if (Object.keys(columnSizing).length === 0) {
      measureColumnsRef.current?.();
    }
  }, [columnSizing]);

  const sortingState = useMemo(
    () => searchSortToSortingState(search.sort),
    [search.sort],
  );

  const setColumnSort = useEvent(
    (
      col: Column<TsData, unknown>,
      desc: boolean,
      comparison?: SortComparison,
    ) => {
      col.toggleSorting(desc);
      if (comparison) {
        // replace the sort that is set by the table since it strips the comparison information
        setSearch((prev: Search) => {
          const nextSortClause = [
            newSortClause({
              fieldInfo: fieldMap[col.id],
              columnSort: {
                id: col.id,
                desc,
              },
              setSearch,
              comparison,
            }),
          ];
          return {
            ...prev,
            sort: [
              ...(prev.sort?.filter((s) => !s.spec?.col) ?? []),
              ...nextSortClause,
            ],
          };
        });
      }
      setAggregationExperimentId?.(comparison?.experimentId ?? "");
    },
  );

  const setSorting: OnChangeFn<SortingState> = useCallback(
    (updater) => {
      setSearch((prev: Search) => {
        const newSorting =
          typeof updater === "function"
            ? updater(searchSortToSortingState(prev.sort))
            : updater;
        const newSortClauses: Clause<"sort">[] = newSorting.map((col) => {
          return newSortClause({
            fieldInfo: fieldMap[col.id],
            columnSort: col,
            setSearch,
          });
        });
        return {
          ...prev,
          sort: [
            ...(prev.sort?.filter((s) => !s.spec?.col) ?? []),
            ...newSortClauses,
          ],
        };
      });
    },
    [setSearch, fieldMap],
  );

  const { api_url: apiUrl } = useOrg();
  const queryKey = makeViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      viewParams,
    },
  });
  const isSavingView = useIsMutating({ mutationKey: queryKey }) > 0;

  // DEPRECATION_NOTICE (Aug 22): We can remove this and rest of changes from PR #2268
  // after clients have had time to update their saved views from index to column path keys.
  useEffect(() => {
    function getColumnPath(key: string) {
      const field = fieldMap[key];
      if (!field) {
        const currField = Object.entries(fieldMap).find(
          ([_, f]) => f.indexColumnId === key,
        );
        const newKey = currField ? currField[0] : key;
        return newKey;
      }
      return key;
    }

    function parseNewEntries<T>(current: Record<string, T>) {
      return Object.fromEntries(
        Object.entries(current).map(([key, value]) => [
          getColumnPath(key),
          value,
        ]),
      );
    }

    if (!Object.keys(fieldMap).length || isPending || isSavingView) {
      return;
    }

    const newColumnVisibility = parseNewEntries(columnVisibility);
    if (
      newColumnVisibility &&
      !isEqual(columnVisibility, newColumnVisibility)
    ) {
      setColumnVisibility(newColumnVisibility);
    }

    const newColumnSizing = parseNewEntries(columnSizing);
    if (newColumnSizing && !isEqual(columnSizing, newColumnSizing)) {
      setColumnSizing(newColumnSizing);
    }

    if (columnOrder.length > 0) {
      const newOrder = columnOrder.map((col) => getColumnPath(col));
      if (!isEqual(newOrder, columnOrder)) {
        setColumnOrder(newOrder);
      }
    }

    if (search.sort && search.sort.length > 0) {
      const newSearch = produce(search, (draft) => {
        if (draft.sort) {
          draft.sort.forEach((s) => {
            if (typeof s !== "string" && !!s.spec) {
              const existingId = s.spec.col.id;
              const newId = getColumnPath(existingId);
              s.spec.col.id = newId;
            }
          });
        }
      });
      if (!isEqual(search, newSearch)) {
        setSearch(newSearch);
      }
    }
  }, [
    isPending,
    currentView,
    isSavingView,
    columnVisibility,
    columnOrder,
    columnSizing,
    setColumnVisibility,
    setColumnOrder,
    setColumnSizing,
    search,
    fieldMap,
    setSearch,
  ]);

  const [columnOrderOverride, setColumnOrderOverride] =
    useColumnOrderOverrideState();
  const baseSummary = summaryBreakdownData?.summary.experiments.find(
    (s) => s.experiment.type === "base",
  );

  // only populate this variable when columnOverride is on so that changes to the summary data
  // don't re-render the tableColumnOrder in the other cases
  const columnOrderSummary =
    columnOrderOverride === "regression" ? baseSummary : undefined;
  const tableColumnOrder = useMemo(() => {
    const flatColumns = flattenColumns<TsData, TsValue>(columns);

    if (columnOrderSummary) {
      return resolveOrderByRegressionColumnOrder(
        flatColumns,
        columnOrderSummary,
      );
    }

    return resolveColumnOrder(flatColumns, columnOrder);
  }, [columns, columnOrder, columnOrderSummary]);

  // Once we support table grouping on logs page, we can just use rowGroupingData.groupBy directly
  const groupBy = rowGroupingData?.groupRows
    ? rowGroupingData?.groupBy
    : undefined;
  const { groupRows, groupRowIds } = useMemo(() => {
    if (!data || !rowGroupingData?.groupRows) {
      return {};
    }

    if (data[0]?.[BT_SUBROWS]) {
      // already calculated in the main query
      return {};
    }

    const rowsForGroup = groupRowsForTable<TsData>(data);
    // re-calculate the order of rowIds for row navigation since the groups are ordered separately
    const groupRowIds: RowId[] = [];
    // ordered by group key
    // Regenerate group rows in the case where the same group rows exist but the subrow data is changing.
    // Since getSubRows is only called once per data, this will make the subrows data stale unless we regenerate the main rows
    // e.g. switching diff modes while grouped
    // since we generate summary info in a separate query, we will sort groups manually
    const groupRows = sortGroupRowsWithSummary({
      groupRows: rowGroupingData.groupRows,
      searchSort: search.sort,
      summaryBreakdownData,
    }).map((r) => {
      const subRows = rowsForGroup[r[BT_GROUP_KEY] ?? "null"] ?? [];
      groupRowIds.push(
        ...subRows.flatMap((r) => {
          const rowId = extractRowIdDiffObject(r);
          return rowId ? [rowId] : [];
        }),
      );
      return {
        ...r,
        [BT_SUBROWS]: subRows,
      };
    });

    return { groupRows, groupRowIds };
  }, [data, rowGroupingData?.groupRows, summaryBreakdownData, search.sort]);

  const [{ r: activeRowId }] = useActiveRowAndSpan();

  const groupedRowIds = useGroupedRowIds({
    rowId: activeRowId,
    objectType,
    objectId,
    groupingStr: rowGroupingData?.groupBy,
  });

  const {
    config: { span_iframes },
  } = useContext(ProjectContext);

  const [diffMode] = useDiffModeState();
  const tableRowHeight = viewProps.rowHeight;
  const { gridLayoutColumns, spanIframeColumns } = useGridColumnDefs({
    isPlayground,
    isLoopEnabled,
    gridLayout,
    experimentName,
    columns,
    comparisonExperiments,
    customColumns: customColumns?.columns,
    columnVisibility,
    neverVisibleColumns,
    span_iframes,
    streamingContentProps,
    rowHeight: tableRowHeight,
    rowComparisonProps,
    setColumnSort,
    removeLimiter,
    isExpectedOutput:
      diffMode?.enabled && diffMode?.enabledValue === "expected_output",
  });

  const tableColumnVisibility = useColumnVisibility({
    columnDefs: columns,
    hiddenGridColumnDefs: spanIframeColumns,
    columnVisibility,
    hiddenColumns,
    initiallyVisibleColumns,
    neverVisibleColumns,
  });

  const table = useReactTable<TsData>({
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    data: (groupRows ?? data ?? emptyArray) as TsData[],
    columns: columns.concat(gridLayoutColumns ?? []) || emptyArray,
    getCoreRowModel: getCoreRowModel(),
    manualFiltering: true,
    state: {
      sorting: sortingState,
      columnSizing,
      columnOrder: tableColumnOrder,
      columnVisibility: tableColumnVisibility,
      rowSelection: rowSelection || undefined,
      ...(expandedState ? { expanded: expandedState } : {}),
    },
    initialState: {
      expanded: rowGroupingData?.initialCollapseGroups ? {} : true,
    },
    enableRowSelection: (row) => {
      return !!rowSelection && !row.getCanExpand();
    },
    onRowSelectionChange: setRowSelection || undefined,
    ...(setExpandedState ? { onExpandedChange: setExpandedState } : {}),
    getSubRows: (groupRow: TsData) => {
      const subRows = groupRow[BT_SUBROWS];
      return subRows instanceof Vector ? subRows.toArray() : subRows;
    },
    getExpandedRowModel: rowGroupingData?.groupRows
      ? getExpandedRowModel()
      : undefined,
    onSortingChange: setSorting,
    columnResizeMode: "onChange",
    onColumnSizingChange,
    onColumnVisibilityChange: setColumnVisibility,
    getRowId: (row: TsData) => {
      const primaryRowId = makeRowIdPrimary(row.id) ?? "";

      if (row[BT_IS_GROUP]) {
        const groupValue = row[BT_GROUP_KEY];
        return groupValue ? `${groupValue}` : primaryRowId;
      }
      return primaryRowId;
    },
    defaultColumn: {
      minSize: 70,
      size: 70,
    },
  });

  const rows = table.getRowModel().rows;
  const leafHeaders = table.getLeafHeaders();
  const tableState = table.getState();
  const totalSize = table.getTotalSize();

  const leafColumns = table.getAllLeafColumns();
  const { visibilityColumns, filterableColumns } = useMemo(
    () =>
      makeColumnOptions({
        columns: leafColumns,
        isGridLayout: gridLayout,
        isPlayground,
        neverVisibleColumns,
        disabledFilterColumns,
        inferEnabled: Boolean(objectType) && Boolean(objectId),
      }),
    [
      gridLayout,
      leafColumns,
      isPlayground,
      neverVisibleColumns,
      disabledFilterColumns,
      objectType,
      objectId,
    ],
  );

  const filterableColIds = useMemo(() => {
    return new Set<string>(filterableColumns.map((c) => c.id));
  }, [filterableColumns]);

  const isOutputVsExpected =
    diffMode?.enabled && diffMode?.enabledValue === "expected_output";
  const multilineRowConfig = useMemo(() => {
    return gridLayout && !removeLimiter
      ? {
          numRows: 1,
          fixedHeight: calculateFieldHeights({
            columns: leafColumns,
            columnVisibility,
            customColumns: customColumns?.columns,
            isPlayground,
            tableRowHeight: tableRowHeight ?? "compact",
            isOutputVsExpected,
          }),
        }
      : multilineRow;
  }, [
    gridLayout,
    leafColumns,
    columnVisibility,
    customColumns?.columns,
    isPlayground,
    tableRowHeight,
    isOutputVsExpected,
    multilineRow,
    removeLimiter,
  ]);

  const { isTraceFullscreen } = useTraceFullscreen();
  const visibleColumns = table.getVisibleLeafColumns();
  useEffect(() => {
    measureColumnsRef.current?.();
  }, [visibleColumns.length, isTraceFullscreen]);

  useEffect(() => {
    measureRowsRef.current?.();
  }, [
    columnVisibility,
    multilineRow,
    gridLayout,
    tableRowHeight,
    removeLimiter,
  ]);

  const resetExpanded = table.resetExpanded;
  useEffect(() => {
    if (rowGroupingData?.initialCollapseGroups) {
      resetExpanded(true);
    } else if (groupBy) {
      // not sure why autoResetExpanded is not working, so implement something ourselves
      resetExpanded();
    }
    measureColumnsRef.current?.();
  }, [groupBy, rowGroupingData?.initialCollapseGroups, resetExpanded]);

  const showAddCustomColumn = useMemo(
    () => !!customColumns?.options && !gridLayout && !isReadOnly,
    [customColumns?.options, gridLayout, isReadOnly],
  );

  const tableStyle = useMemo(() => {
    return {
      width: totalSize + (showAddCustomColumn ? 45 : 0) + 16,
    };
  }, [showAddCustomColumn, totalSize]);

  useImperativeHandle(tableRef, () => table, [table]);

  useEffect(() => {
    const newRowIds = groupRowIds ?? queriedRowIds;
    if (isEqual(newRowIds, rowIds)) return;
    setRowIds(newRowIds ?? emptyRowIds);
    measureColumnsRef.current?.();
  }, [groupRowIds, queriedRowIds, rowIds, setRowIds]);

  const didScrollToActiveRowOnPageLoad = useRef(false);
  useEffect(() => {
    if (
      !table ||
      !activeRowOnPageLoad ||
      didScrollToActiveRowOnPageLoad.current ||
      !rowIds ||
      !rowIds.includes(activeRowOnPageLoad)
    ) {
      return;
    }

    didScrollToActiveRowOnPageLoad.current = true;

    const rowId = makeRowIdPrimary(activeRowOnPageLoad);
    if (!rowId) return;
    const rowIndex = rowIds.indexOf(rowId);
    if (rowIndex === -1) return;

    scrollToIndexRef.current?.(rowIndex, { align: "center" });
  }, [activeRowOnPageLoad, rowIds, table]);

  const [filterState, setFilterState] = useState<{
    isOpen: boolean;
    selectedColumn: MinimalColumnData<TsData> | undefined;
  }>({
    isOpen: false,
    selectedColumn: resolveDefaultColumnId(filterableColumns),
  });
  const onFilterEditorSetOpen = useCallback(
    (isOpen: boolean) => {
      setFilterState((prev) => ({ ...prev, isOpen }));
    },
    [setFilterState],
  );
  const setSelectedFilterColumn = useCallback(
    (col: MinimalColumnData<TsData> | undefined) => {
      setFilterState((prev) => ({ ...prev, selectedColumn: col }));
    },
    [setFilterState],
  );
  const forceFilterState = useCallback(
    (colId: string | undefined) => {
      setFilterState({
        isOpen: true,
        selectedColumn: resolveDefaultColumnId(filterableColumns, colId),
      });
    },
    [setFilterState, filterableColumns],
  );

  const showNoRowsComponent = useMemo(
    () =>
      Boolean(
        hasNoRowsComponent &&
          data !== null &&
          data.length === 0 &&
          !Object.values(search).some((s) => s?.length),
      ),
    [hasNoRowsComponent, data, search],
  );
  const [_customColumnDialogVariant, _setCustomColumnDialogVariant] =
    useState<CustomColumnDialogVariant>();
  const [customColumnDialogVariant, setCustomColumnDialogVariant] =
    useMemo(() => {
      if (setCustomColumnDialogVariantProp) {
        return [
          customColumnDialogVariantProp,
          setCustomColumnDialogVariantProp,
        ];
      }
      return [_customColumnDialogVariant, _setCustomColumnDialogVariant];
    }, [
      customColumnDialogVariantProp,
      setCustomColumnDialogVariantProp,
      _customColumnDialogVariant,
    ]);

  const [openedCustomColumn, setOpenedCustomColumn] = useState<string>();

  const openedColValues = useMemo(() => {
    const col = customColumns?.columns?.find(
      (c) => c.id === openedCustomColumn,
    );
    return openedCustomColumn && col
      ? { name: col.name, expr: col.expr, id: openedCustomColumn }
      : undefined;
  }, [openedCustomColumn, customColumns?.columns]);

  /** Used to set virtualizer scrollMargin dynamically, if notStickySlot is provided. */
  const notStickySlotRef = useRef<HTMLDivElement>(null);
  /** Used to offset table headers by the height of the sticky bar, and to set virtualizer scrollMargin dynamically, if notStickySlot is not provided. */
  const stickyBarRef = useRef<HTMLDivElement>(null);

  const intersectionObserverRef = useRef<IntersectionObserver>(null);
  const visibleMeasureRef = useCallback((node: HTMLDivElement | null) => {
    intersectionObserverRef.current?.disconnect();
    if (!node) {
      return;
    }
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          measureColumnsRef.current?.();
        }
      },
      { root: null },
    );

    observer.observe(node);
    intersectionObserverRef.current = observer;
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const [draggingHeader, setDraggingHeader] = useState<Header<
    TsData,
    unknown
  > | null>(null);
  const handleColumnDragEnd = useEvent((event: DragEndEvent) => {
    setDraggingHeader(null);
    const { active, over } = event;
    if (over?.id && active.id !== over.id) {
      const items = table.getAllLeafColumns().map((c) => c.id);
      const oldIndex = items.indexOf(active.id.toString());
      const newIndex = items.indexOf(over.id.toString());
      if (newIndex === 0) {
        return;
      }
      setColumnOrder(arrayMove(items, oldIndex, newIndex));
      setColumnOrderOverride(null);
      measureColumnsRef.current?.();
      measureRowsRef.current?.();
    }
  });
  const handleColumnDragStart = useEvent((event: DragStartEvent) => {
    setDraggingHeader(
      table.getLeafHeaders().find((h) => h.id === event.active.id) ?? null,
    );
  });

  const [customColumnToDelete, setCustomColumnToDelete] = useState<{
    id: string;
    name: string;
  }>();

  const collisionAlgorithm: CollisionDetection = useEvent((args) => {
    // remove pinned columns from droppable rects
    const pinnedColumnIds = new Set<string | number>(
      visibleColumns.flatMap((c) =>
        c.columnDef?.meta?.pinnedColumnIndex != null ? [c.id] : [],
      ),
    );
    args.droppableRects.delete(RowSelectionColumnId);
    args.droppableRects.delete(RowComparisonColumnId);
    args.droppableRects.forEach((_, id) => {
      if (pinnedColumnIds.has(id)) {
        args.droppableRects.delete(id);
      }
    });
    return closestCorners(args);
  });

  const createCustomColumn = useMemo(
    () =>
      customColumns?.createColumn
        ? async (col: CustomColumnDef) => {
            await customColumns.createColumn(col);
            measureColumnsRef.current?.();
          }
        : undefined,
    [customColumns],
  );
  const onCloseCustomDialog = useCallback(() => {
    setOpenedCustomColumn(undefined);
  }, [setOpenedCustomColumn]);

  const onSelectOrderByRegression = useEvent(() => {
    setColumnOrderOverride((prev) => (prev == null ? "regression" : null));
    measureColumnsRef.current?.();
  });

  const openSidePanel = useOpenSidePanel({
    rowIds,
  });

  const onCellClick = useEvent(
    (e: React.MouseEvent, cell: Cell<TsData, unknown>) => {
      // Check for text selection. If the user has highlighted text, don't open the trace view.
      if (window.getSelection()?.toString()) {
        return;
      }

      // Open trace view
      openSidePanel(cell.row.id, {
        // check for cmd+click or ctrl+click or middle button click
        openInNewTab: e.metaKey || e.ctrlKey || e.button === 1,
      });
    },
  );

  const onDeadCellClick = useEvent((e: React.MouseEvent, row: Row<TsData>) => {
    if (rowEvents?.onClick) {
      return rowEvents.onClick(row)(e);
    }
    openSidePanel(row.id, {
      openInNewTab: e.metaKey || e.ctrlKey || e.button === 1,
    });
  });

  const onAddColumn = useCallback(() => {
    setCustomColumnDialogVariant("create");
  }, [setCustomColumnDialogVariant]);

  const resolvedColumnVisibility = useMemo(() => {
    return {
      ...initiallyVisibleColumns,
      ...columnVisibility,
    };
  }, [initiallyVisibleColumns, columnVisibility]);

  const useTableVirtualizerProps = useMemo(
    () => ({
      multilineRowConfig,
      scrollContainerRef,
      table,
      isGridLayout: gridLayout,
    }),
    [multilineRowConfig, scrollContainerRef, table, gridLayout],
  );

  const notStickyContent = notStickySlot ? (
    <div className="@container sticky left-0" ref={notStickySlotRef}>
      {notStickySlot}
    </div>
  ) : null;

  if (showNoRowsComponent) {
    return (
      <>
        {notStickyContent}
        {hasNoRowsComponent}
      </>
    );
  }

  if (data === null && !errorUI) {
    return (
      <>
        {notStickyContent}
        <div className={cn("relative", className)}>
          <TableSkeleton />
        </div>
      </>
    );
  }

  const allColumnsHidden = leafColumns
    .filter(
      (c) =>
        !c.columnDef.meta?.internalType &&
        (c.columnDef.meta?.pinnedColumnIndex == null ||
          c.columnDef.meta?.isGridLayout),
    )
    .every((c) => !c.getIsVisible());

  // Use the same logic as useFilterSortBarSearch to determine if filters should be disabled
  const shouldDisableFilters =
    clauseChecker === null || clauseChecker === noopChecker;
  const filtersEnabled = clauseChecker !== undefined && !shouldDisableFilters;

  const customColsError = customColumns?.error?.message ? (
    <ErrorBanner>
      There was a problem fetching custom columns.{" "}
      <Button
        transparent
        size="inline"
        className="text-xs text-bad-700 underline"
        onClick={() => setCustomColumnDialogVariant("edit")}
      >
        Edit the column
      </Button>{" "}
      or{" "}
      <a href="mailto:<EMAIL>" className="underline">
        contact us
      </a>{" "}
      for support.
    </ErrorBanner>
  ) : null;

  return (
    <StyledDndContext
      autoScroll={{
        acceleration: 35,
      }}
      collisionDetection={collisionAlgorithm}
      sensors={sensors}
      onDragEnd={handleColumnDragEnd}
      onDragStart={handleColumnDragStart}
    >
      <ColumnDragOverlay draggingHeader={draggingHeader} />
      {notStickyContent}
      <div
        className={cn(
          "@container sticky top-0 -left-3 z-20 -mx-3 bg-background px-3",
          stickyBarClassName,
        )}
        ref={stickyBarRef}
      >
        {beforeToolbarSlot}
        <div>
          <div
            className={cn(
              "@container/controls -mx-3 flex flex-wrap items-center gap-1.5 overflow-hidden bg-background px-3 py-2",
              className,
            )}
          >
            {toolbarSlot ?? (
              <>
                {extraLeftControls}
                {filtersEnabled && (
                  <FilterEditor
                    enableStarColumn={enableStarColumn}
                    selectableColumns={filterableColumns}
                    formatters={formatters}
                    objectType={objectType}
                    objectId={objectId}
                    clauseChecker={clauseChecker}
                    search={search}
                    setSearch={setSearch}
                    baseExperiment={{
                      name: experimentName ?? "Base experiment",
                      id: experimentId ?? "",
                    }}
                    comparisonExperiments={comparisonExperiments}
                    addRegressionFilter={addRegressionFilter}
                    isOpen={filterState.isOpen}
                    setOpen={onFilterEditorSetOpen}
                    disabled={disableFilters}
                    disableNegativeTagFilters={disableNegativeTagFilters}
                    isPlayground={isPlayground}
                    scoreNames={scoreNames}
                    suggestions={filterSuggestions}
                    column={filterState.selectedColumn}
                    setColumn={setSelectedFilterColumn}
                  />
                )}
                <ColumnsMenu<TsData, TsValue>
                  customColumnDialogVariant={customColumnDialogVariant}
                  setCustomColumnDialogVariant={setCustomColumnDialogVariant}
                  columns={visibilityColumns}
                  formatters={formatters}
                  allColumnsHidden={allColumnsHidden}
                  baseSummary={baseSummary}
                  onSelectOrderByRegression={onSelectOrderByRegression}
                  columnOrderOverride={columnOrderOverride}
                  customColumns={customColumns?.columns}
                  customColumnOptions={customColumns?.options}
                  clauseChecker={clauseChecker ?? null}
                  initialFormValues={openedColValues}
                  createCustomColumn={createCustomColumn}
                  updateCustomColumn={customColumns?.updateColumn}
                  deleteCustomColumn={setCustomColumnToDelete}
                  onCloseCustomDialog={onCloseCustomDialog}
                  gridLayout={gridLayout}
                  columnVisibility={resolvedColumnVisibility}
                  handleDragEnd={handleColumnDragEnd}
                  customColumnsEnabled={customColumns?.isEnabled}
                  enableScrollToColumn={
                    viewProps.layout === "list" && enableScrollToColumn
                  }
                />
                {!isPlayground && extraRightControls}
              </>
            )}
            {isPlayground && extraRightControls}
          </div>
          {errorUI && (
            <ErrorBanner skipErrorReporting={skipErrorReporting}>
              {errorUI}
            </ErrorBanner>
          )}
          {customColsError}
        </div>
        {afterToolbarSlot}
      </div>
      {summaryLayout && (
        <SummaryTableLayout<TData, TsData>
          summaryData={summaryBreakdownData?.summary.experiments}
          headers={leafHeaders}
          addRegressionFilter={addRegressionFilter}
          metricDefinitions={metricDefinitions}
        />
      )}
      <div ref={visibleMeasureRef}>
        {data !== null && !summaryLayout && (
          <>
            {allColumnsHidden ? (
              <TableEmptyState
                className="sticky left-0 my-4 w-full py-24"
                labelClassName="text-sm"
                label="No columns selected"
              />
            ) : rows.length === 0 ? (
              <TableEmptyState
                className="sticky left-0 my-4 w-full py-24"
                labelClassName="text-sm"
                label={noRowsFoundLabel}
              />
            ) : (
              <>
                <div className="-mx-3">
                  <div className="max-w-full pl-3">
                    <div
                      className="min-w-full border-collapse border-spacing-0 pr-3 font-inter tabular-nums will-change-auto"
                      style={tableStyle}
                    >
                      <TableHeaders<TsData>
                        headers={leafHeaders}
                        stickyBarRef={stickyBarRef}
                        tableHeaderRefs={tableHeaderRefs}
                        allColumnsHidden={allColumnsHidden}
                        filterableColIds={filterableColIds}
                        onSetFilterState={forceFilterState}
                        filtersEnabled={filtersEnabled}
                        summaryData={summaryBreakdownData?.summary.experiments}
                        addRegressionFilter={addRegressionFilter}
                        onEdit={setOpenedCustomColumn}
                        onDelete={setCustomColumnToDelete}
                        onAddColumn={onAddColumn}
                        customColumnsLoading={
                          !!customColumns?.isEnabled && !!customColumns?.loading
                        }
                        showAddCustomColumn={showAddCustomColumn}
                        isGridLayout={gridLayout}
                        setColumnSort={setColumnSort}
                        groupBy={groupBy}
                        customColumnsEnabled={!!customColumns?.isEnabled}
                        isSortable={isSortable}
                        isReadOnly={isReadOnly}
                        summarySlots={summarySlots}
                        metricDefinitions={metricDefinitions}
                      />
                      <VirtualTableBody<TsData>
                        scrollMarginRef={
                          notStickySlot ? notStickySlotRef : scrollMarginRef
                        }
                        tableType={tableType}
                        useTableVirtualizerProps={useTableVirtualizerProps}
                        measureColumnsRef={measureColumnsRef}
                        measureRowsRef={measureRowsRef}
                        scrollToIndexRef={scrollToIndexRef}
                        loadingColumns={loadingColumns}
                        isLoading={isLoading}
                        rows={rows}
                        rowEvents={rowEvents}
                        rowSelectionState={tableState.rowSelection}
                        onCellClick={onCellClick}
                        onClickRowDeadCell={onDeadCellClick}
                        groupedRowIds={groupedRowIds}
                        rowComparisonProps={rowComparisonProps}
                        streamingContentProps={streamingContentProps}
                        multilineRow={multilineRowConfig}
                        isGridLayout={gridLayout}
                        disableErrorRowHighlight={isPlayground}
                        removeLimiter={removeLimiter}
                        openRowId={openRowId}
                      />
                    </div>
                  </div>
                </div>
                {
                  /*
                NOTE: Unlike filtering, I was lazy and just implemented pagination directly using tanstack's
                table pagination API. This means things like canGetPreviousPage(), canGetNextPage(),
                getPrePaginationRowModel(), and getFacetedUniqueValues() work and give us some niceties. Ideally,
                we can derive the relevant answers to these functions from the query itself, and implement pagination
                via the query. This will allow us to implement server-side pagination when the time comes.
              */
                  infiniteScroll && (
                    <InfiniteScroll
                      scrollContainerRef={scrollContainerRef}
                      infiniteScroll={infiniteScroll}
                    />
                  )
                }
              </>
            )}
          </>
        )}
      </div>
      <ConfirmationDialog
        title="Confirm delete custom column"
        description={`Are you sure you want to delete ${customColumnToDelete?.name}?`}
        open={Boolean(customColumnToDelete)}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setCustomColumnToDelete(undefined);
          }
        }}
        onConfirm={() => {
          const deleteFn = async (colId: string) => {
            await customColumns?.deleteColumn?.(colId);
            measureColumnsRef.current?.();
          };
          if (customColumnToDelete) {
            deleteFn(customColumnToDelete.id);
          }
        }}
        confirmText="Confirm"
      />
      <TableHotkeyManager
        isHumanReviewModeEnabled={isHumanReviewModeEnabled}
        openSidePanel={openSidePanel}
        rowIds={rowIds}
      />
    </StyledDndContext>
  );
}

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const Table = memo(TableComponent) as typeof TableComponent;
// @ts-expect-error
Table.displayName = "Table";

const ColumnDragOverlay = <TsData extends {}>({
  draggingHeader,
}: {
  draggingHeader: Header<TsData, unknown> | null;
}) => {
  return (
    <DragOverlay>
      {draggingHeader ? (
        <div className="truncate rounded-md border-2 border-accent-500 bg-background p-2 text-xs font-medium text-primary-800 shadow-md">
          {flexRender(
            draggingHeader.column.columnDef.header,
            draggingHeader.getContext(),
          )}
        </div>
      ) : null}
    </DragOverlay>
  );
};

function newSortClause({
  fieldInfo,
  columnSort,
  setSearch,
  comparison,
}: {
  fieldInfo: FieldInfo;
  columnSort: ColumnSort;
  setSearch: Dispatch<SetStateAction<Search>>;
  comparison?: SortComparison;
}) {
  const sortPath = {
    desc: columnSort.desc,
    path: fieldInfo.path,
  };
  const text = compileSort(sortPath);
  const clause: ClauseSpec<"sort"> = {
    type: "sort",
    text,
    spec: {
      col: columnSort,
      path: sortPath,
    },
    comparison,
  };
  return {
    ...clause,
    bubble: makeBubble({ clause, setSearch }),
  };
}

const layoutTypeEnum = z.enum(["list", "grid"]);
type LayoutType = z.infer<typeof layoutTypeEnum>;
const newBaseQueryRowSchema = z.object({
  id: z.union([z.string(), createDiffObjectSchema(z.string())]),
  __bt_internal: z
    .object({
      _meta: z
        .object({
          diffModeEnabled: z.boolean(),
          layoutType: layoutTypeEnum,
        })
        .nullish(),
    })
    .passthrough()
    .nullish(),
});

export function extractRowIdDiffObject(r: unknown): RowId | null {
  if (isEmpty(r)) {
    return null;
  }
  const parsed = newBaseQueryRowSchema.safeParse(r);
  if (!parsed.success) {
    return null;
  }

  const data = parsed.data;
  return data.__bt_internal?._meta?.layoutType
    ? playgroundRowId(r, data.__bt_internal._meta.layoutType)
    : data.id;
}

function playgroundRowId(data: unknown, layoutType: LayoutType) {
  const parsed = parseRowId(data, layoutType);
  if (!parsed) {
    return null;
  }
  const { rowId, originDataset, hasPlaygroundData, datasetId } = parsed;
  const result = {
    ...rowId,
    ...(datasetId === SINGLETON_DATASET_ID && hasPlaygroundData
      ? {
          originId: SINGLETON_DATASET_ID,
          originObjectId: SINGLETON_DATASET_ID,
          originXactId: "0",
        }
      : originDataset
        ? {
            originId: originDataset.id,
            originObjectId: originDataset.object_id,
            originXactId: originDataset[TRANSACTION_ID_FIELD],
          }
        : datasetId
          ? {
              objectType: "dataset",
              objectId: datasetId,
            }
          : undefined),
  };
  return result;
}

const listLayoutRowBaseSchema = z.object({
  id: createDiffObjectSchema(z.string()),
  dataset_id: createDiffObjectSchema(z.string()),
  playground_row_id: createDiffObjectSchema(z.string()).nullish(),
  origin: createDiffObjectSchema(z.string()).nullish(),
});
const listLayoutRowSchema = newBaseQueryRowSchema.extend(
  listLayoutRowBaseSchema.shape,
);

function parseRowId(data: unknown, layoutType: LayoutType) {
  if (layoutType === "list") {
    return parseListLayoutRowId(data);
  }
  return parseGridLayoutRowId(data);
}

function parseListLayoutRowId(_data: unknown) {
  const parsedRow = listLayoutRowSchema.safeParse(_data);
  if (!parsedRow.success) {
    return null;
  }
  const data = parsedRow.data;

  // track playground_row_id instead of origin because the non-dataset case won't have an origin
  const hasPlaygroundData =
    data.playground_row_id &&
    Object.values(data.playground_row_id).some((id) => id);
  const originDataset = data.origin ? parseDiffObjectOrigin(data.origin) : null;
  const datasetId = getDiffRight(data.dataset_id);

  if (!hasPlaygroundData) {
    return {
      rowId: data.id,
      originDataset: null,
      hasPlaygroundData,
      datasetId,
    };
  }

  const rowId: Record<string, unknown> = {
    // Right now this is always true.
    // If we change it so that we can view comparisons in list mode without diff view,
    // then we need to change this we can check this value
    diffModeEnabled: `${!!data.__bt_internal?._meta?.diffModeEnabled}`,
  };
  for (const k of Object.keys(data.id)) {
    if (getDiffFieldIndex(k) == null) {
      continue;
    }
    if (data.playground_row_id?.[k] != null) {
      rowId[k] = data.playground_row_id[k];
    }
  }

  return {
    rowId,
    originDataset,
    hasPlaygroundData,
    datasetId,
  };
}

function parseDiffObjectOrigin(origin: DiffObjectType<string>) {
  for (const [k, v] of Object.entries(origin)) {
    if (getDiffFieldIndex(k) == null) {
      continue;
    }
    const parsed = parseOrigin(v);
    if (parsed?.object_type === "dataset") {
      return parsed;
    }
  }
  return null;
}

const gridLayoutRowBaseSchema = z.object({
  id: z.string(),
  dataset_id: z.string().nullish(),
  playground_row_id: z.string().nullish(),
  origin: z.string().nullish(),
});
const gridLayoutRowSchema = newBaseQueryRowSchema.extend(
  gridLayoutRowBaseSchema.shape,
);
const gridLayoutComparisonRowSchema = z.object({
  data: gridLayoutRowBaseSchema,
});

function parseGridLayoutRowId(_data: unknown) {
  const parsedRow = gridLayoutRowSchema.safeParse(_data);
  if (!parsedRow.success) {
    return null;
  }
  const data = parsedRow.data;
  const rowDataEntries: [string, unknown][] = [
    ["diffModeEnabled", `${!!data.__bt_internal?._meta?.diffModeEnabled}`],
    [
      DiffRightField,
      {
        id: data.playground_row_id ?? data.id,
        isPlaygroundRow: !!data.playground_row_id,
      },
    ],
  ];
  // track playground_row_id instead of origin because the non-dataset case won't have an origin
  let hasPlaygroundData = false;
  const primaryOrigin = parseOrigin(data.origin);
  let originDataset = null;
  if (primaryOrigin?.object_type === "dataset") {
    originDataset = primaryOrigin;
  }
  hasPlaygroundData = !!data.playground_row_id;
  for (const [k, d] of Object.entries(data.__bt_internal ?? {})) {
    const parsed = gridLayoutComparisonRowSchema.safeParse(d);
    if (!parsed.success || !parsed.data.data.id) {
      continue;
    }

    hasPlaygroundData ||= !!parsed.data.data.playground_row_id;

    const origin = parseOrigin(parsed.data.data?.origin);
    originDataset ??= origin?.object_type === "dataset" ? origin : null;

    rowDataEntries.push([
      k === "e2" ? DiffLeftField : k,
      {
        id: parsed.data.data.playground_row_id ?? parsed.data.data.id,
        isPlaygroundRow: !!parsed.data.data.playground_row_id,
      },
    ]);
  }

  const rowId = Object.fromEntries(
    rowDataEntries.flatMap(([k, v]) => {
      if (typeof v === "string") {
        return [[k, v]];
      }
      if (
        hasPlaygroundData &&
        v instanceof Object &&
        (!("isPlaygroundRow" in v) || !v.isPlaygroundRow)
      ) {
        return [];
      }
      if (v instanceof Object && "id" in v) {
        return [[k, v.id]];
      }
      return [[k, v]];
    }),
  );
  return {
    rowId,
    originDataset,
    hasPlaygroundData,
    datasetId: data.dataset_id,
  };
}

function parseOrigin(origin?: string | null) {
  if (!origin) {
    return null;
  }
  try {
    return objectReferenceSchema.parse(JSON.parse(origin));
  } catch {
    return null;
  }
}
