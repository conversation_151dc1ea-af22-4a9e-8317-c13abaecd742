import { cn } from "#/utils/classnames";
import { forwardRef } from "react";

export const GhostButton = forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(function GhostButton(props, ref) {
  const { className, children, ...otherProps } = props;

  return (
    <button
      className={cn(
        `border-2 border-transparent px-3 py-2 text-sm`,
        `rounded-sm font-medium text-accent-500`,
        className,
      )}
      ref={ref}
      {...otherProps}
    >
      {children}
    </button>
  );
});
