import { useMemo } from "react";
import { type ColumnDef } from "@tanstack/react-table";

export function useColumnVisibility<TsData, TsValue>({
  columnDefs,
  hiddenGridColumnDefs,
  columnVisibility,
  hiddenColumns,
  initiallyVisibleColumns,
  neverVisibleColumns,
}: {
  columnDefs: ColumnDef<TsData, TsValue>[];
  // if this is non-undefined, then we are in grid mode
  hiddenGridColumnDefs?: ColumnDef<TsData, TsValue>[];
  columnVisibility?: Record<string, boolean>;
  hiddenColumns?: Record<string, boolean>;
  initiallyVisibleColumns?: Record<string, boolean>;
  neverVisibleColumns?: Set<string>;
}) {
  return useMemo(() => {
    const hiddenColumnVisibility: Record<string, boolean> = {};
    const pinnedColumnVisibility: Record<string, boolean> = {};
    columnDefs.forEach((column) => {
      if (!column.id) {
        return;
      }

      if (column.meta?.pinnedColumnIndex != null) {
        pinnedColumnVisibility[column.id] = true;
        return;
      }
      if (hiddenColumns && hiddenColumns[column.id]) {
        hiddenColumnVisibility[column.id] = false;
      }
    });

    const gridLayoutVisibility = !!hiddenGridColumnDefs
      ? [
          ...hiddenGridColumnDefs,
          // in grid mode hide all non-grid columns
          ...columnDefs,
        ].reduce((acc: Record<string, boolean>, column) => {
          if (column.id && !column.meta?.internalType) {
            acc[column.id] = false;
          }
          return acc;
        }, {})
      : undefined;

    return {
      ...initiallyVisibleColumns,
      ...columnVisibility,
      ...hiddenColumnVisibility,
      ...pinnedColumnVisibility,
      ...gridLayoutVisibility,
      ...Object.fromEntries(
        Array.from(neverVisibleColumns ?? []).map((k) => [k, false]),
      ),
    };
  }, [
    columnDefs,
    columnVisibility,
    hiddenColumns,
    initiallyVisibleColumns,
    hiddenGridColumnDefs,
    neverVisibleColumns,
  ]);
}
