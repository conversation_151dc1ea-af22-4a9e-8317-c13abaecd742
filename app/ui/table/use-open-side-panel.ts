import { type RowId, makeRowIdPrimary } from "#/utils/diffs/diff-objects";
import { useCallback } from "react";
import {
  useActiveRowAndSpan,
  useHumanReviewState,
} from "#/ui/query-parameters";
import { matchesRowId } from "./use-active-row-effects";

type Args = {
  rowIds: RowId[];
};

export type OpenSidePanelFn = (
  rowId: RowId,
  opts?: {
    humanReviewMode?: boolean;
    openInNewTab?: boolean;
    preserveSpan?: boolean;
  },
) => void;

export default function useOpenSidePanel({ rowIds }: Args) {
  const [_activeRowAndSpan, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_humanReviewState, setHumanReviewState] = useHumanReviewState();

  return useCallback(
    (
      rowId: RowId,
      opts?: {
        humanReviewMode?: boolean;
        openInNewTab?: boolean;
        preserveSpan?: boolean;
      },
    ) => {
      const primaryRowId = makeRowIdPrimary(rowId);
      const matchingRowId = rowIds.find((r) => {
        return matchesRowId(r, primaryRowId);
      });
      if (!matchingRowId) return;

      if (opts?.openInNewTab) {
        const pathname = window.location.pathname;
        const params = new URLSearchParams(window.location.search);
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        params.set("r", matchingRowId as string);
        if (opts?.humanReviewMode) {
          params.set("review", "1");
        }
        window.open(`${pathname}?${params.toString()}`, "_blank");
        return;
      }

      setActiveRowAndSpan({
        r: matchingRowId,
        ...(opts?.preserveSpan ? {} : { s: null }),
      });

      if (opts?.humanReviewMode) {
        setHumanReviewState("1");
      }
    },
    [rowIds, setActiveRowAndSpan, setHumanReviewState],
  );
}
