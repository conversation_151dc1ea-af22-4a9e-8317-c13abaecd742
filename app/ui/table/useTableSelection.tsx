"use client";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  type ExpandedState,
  type Row,
  type Table as TanStackTable,
} from "@tanstack/react-table";
import { isEmpty } from "#/utils/object";
import { flattenDiffObjects } from "#/utils/diffs/diff-objects";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { fetchBtql, rowWithIdsSchema } from "#/utils/btql/btql";
import { useBtqlFlags } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";

export interface TableSelection {
  selectedRows: Record<string, boolean>;
  setSelectedRows: Dispatch<SetStateAction<Record<string, boolean>>>;
  selectAllTableRows: VoidFunction;
  deselectAllTableRows: VoidFunction;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  getSelectedRowsWithData: (extraFields?: Record<string, unknown>) => any[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  refetchSelectedRowsWithData?: () => Promise<any[]>;
  selectedRowsNumber: number;
  expandedState: ExpandedState;
  setExpandedState: Dispatch<SetStateAction<ExpandedState>>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  getRow: (key: string) => null | Row<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  tableRef: React.RefObject<TanStackTable<any> | null>;
}

export const useTableSelection = (
  refetchDataQueryFn?: (rowIds: string[]) => ParsedQuery | undefined,
): TableSelection => {
  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({});
  const [expandedState, setExpandedState] = useState<ExpandedState>({});
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const tableRef = useRef<TanStackTable<any>>(null);
  const btqlFlags = useBtqlFlags();
  const { getOrRefreshToken } = useSessionToken();
  const { api_url } = useOrg();

  const getRow = useCallback((key: string) => {
    if (!tableRef.current) return null;
    try {
      let row = tableRef.current?.getRow(key);
      row = flattenDiffObjects(row?.original);
      return row;
    } catch {
      return null;
    }
  }, []);

  const getSelectedRowsWithData = useCallback(
    (extraFields?: Record<string, unknown>) => {
      if (!tableRef.current) {
        return [];
      }
      return Object.keys(selectedRows)
        .map((key) => {
          if (!selectedRows[key]) {
            return null;
          }
          return { ...getRow(key), ...extraFields };
        })
        .filter((val) => !isEmpty(val));
    },
    [selectedRows, getRow],
  );

  const refetchSelectedRowsWithData = useCallback(async () => {
    if (!refetchDataQueryFn) {
      return [];
    }
    if (!tableRef.current) {
      return [];
    }
    const rowIds = Object.keys(selectedRows);
    const query = refetchDataQueryFn(rowIds);
    if (!query) {
      return [];
    }
    return (
      await fetchBtql({
        args: {
          query,
          brainstoreRealtime: true,
          disableLimit: true,
        },
        btqlFlags,
        apiUrl: api_url,
        getOrRefreshToken,
        schema: rowWithIdsSchema,
      })
    ).data;
  }, [selectedRows, refetchDataQueryFn, btqlFlags, api_url, getOrRefreshToken]);

  const deselectAllTableRows = useCallback(() => {
    setSelectedRows({});
  }, []);
  const selectAllTableRows = useCallback(() => {
    if (!tableRef.current) {
      return;
    }
    tableRef.current.toggleAllRowsSelected(true);
  }, []);
  const selectedRowsNumber = useMemo(() => {
    return Object.keys(selectedRows).filter((key) => selectedRows[key]).length;
  }, [selectedRows]);

  return useMemo(
    () => ({
      selectedRows,
      setSelectedRows,
      selectAllTableRows,
      deselectAllTableRows,
      getSelectedRowsWithData,
      refetchSelectedRowsWithData: refetchDataQueryFn
        ? refetchSelectedRowsWithData
        : undefined,
      selectedRowsNumber,
      expandedState,
      setExpandedState,
      getRow,
      tableRef,
    }),
    [
      deselectAllTableRows,
      expandedState,
      getRow,
      getSelectedRowsWithData,
      refetchDataQueryFn,
      refetchSelectedRowsWithData,
      selectAllTableRows,
      selectedRows,
      selectedRowsNumber,
    ],
  );
};
