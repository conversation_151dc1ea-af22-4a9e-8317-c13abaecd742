import { type Cell, flexRender } from "@tanstack/react-table";

interface CheckboxCellProps<TsData> {
  cell: Cell<TsData, unknown>;
}

export function CheckboxCell<TsData>({ cell }: CheckboxCellProps<TsData>) {
  return (
    <div
      style={{ width: cell.column.getSize() }}
      className="relative flex-none"
    >
      {flexRender(cell.column.columnDef.cell, {
        inTable: true,
        meta: cell.column.columnDef.meta,
        value: cell.getValue(),
        setValue: (_: unknown) => {},
        ...cell.getContext(),
      })}
    </div>
  );
}
