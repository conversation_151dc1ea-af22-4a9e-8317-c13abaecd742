import {
  createDiffObjectSchema,
  getDiffValue,
} from "#/utils/diffs/diff-objects";
import { objectReferenceSchema } from "@braintrust/local";
import { z } from "zod";

function safeJsonParse(value: unknown) {
  if (typeof value !== "string") {
    return null;
  }
  try {
    return JSON.parse(value);
  } catch (e) {
    return null;
  }
}

const spanTypeInfoSchema = z.object({
  generation: z.string(),
});

const originSchema = z.object({
  id: z.string(),
  object_id: z.string(),
  object_type: z.string(),
});

const rowSchemaRaw = z.object({
  id: z.string(),
  output: z.string().nullish(),
  span_type_info: z.string(),
  origin: z.string().nullish(),
  metrics: z
    .object({
      end: z.number().nullish(),
    })
    .nullish(),
  dataset_id: z.string().nullish(),
  playground_xact_id: z.string().nullish(),
});

const btRowSchema = rowSchemaRaw.extend({
  __bt_internal: z.record(
    z.string(),
    z.object({
      data: rowSchemaRaw.nullish(),
    }),
  ),
});

export const parseGridLayoutRow = <TsData>({
  rowOriginal,
  index,
}: {
  rowOriginal: TsData;
  index: number;
}) => {
  const parsedRow = btRowSchema.safeParse(rowOriginal);
  if (!parsedRow.success) {
    return { data: null, spanTypeInfo: null, origin: null };
  }

  const data =
    index === 0
      ? parsedRow.data
      : parsedRow.data.__bt_internal[`e${index + 1}`]?.data;

  if (!data) {
    return { data: null, spanTypeInfo: null, origin: null };
  }

  const parsedSpanTypeInfo = spanTypeInfoSchema.safeParse(
    safeJsonParse(data.span_type_info),
  );
  const parsedOrigin = objectReferenceSchema.safeParse(
    safeJsonParse(data.origin),
  );

  return {
    spanTypeInfo: parsedSpanTypeInfo.success ? parsedSpanTypeInfo.data : null,
    origin: parsedOrigin.success ? parsedOrigin.data : null,
    data,
  };
};

const baseRowOriginalSchema = z.object({
  id: createDiffObjectSchema(z.string()),
  span_type_info: createDiffObjectSchema(z.string()),
  origin: createDiffObjectSchema(z.string()),
});

export const parseListLayoutRow = <TsData>({
  rowOriginal,
  index,
}: {
  rowOriginal: TsData;
  index: number;
}) => {
  const parsed = baseRowOriginalSchema.safeParse(rowOriginal);
  if (!parsed.success) {
    return { data: null, spanTypeInfo: null, origin: null };
  }

  const data = parsed.data;
  const spanTypeInfoString = getDiffValue({
    value: data.span_type_info,
    index,
  });

  const parsedSpanTypeInfo = spanTypeInfoSchema.safeParse(
    safeJsonParse(spanTypeInfoString),
  );
  const originString = getDiffValue({
    value: data.origin,
    index,
  });
  const parsedOrigin = originSchema.safeParse(safeJsonParse(originString));

  return {
    spanTypeInfo: parsedSpanTypeInfo.success ? parsedSpanTypeInfo.data : null,
    origin: parsedOrigin.success ? parsedOrigin.data : null,
    id: getDiffValue({ value: data.id, index }),
  };
};
