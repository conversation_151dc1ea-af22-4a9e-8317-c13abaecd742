import { type Table } from "@tanstack/react-table";
import { IndeterminateCheckbox } from "#/ui/checkbox";
import { useCallback, type RefObject } from "react";

interface TableHeaderCheckboxProps<TData> {
  table: Table<TData>;
  lastSelectedRowId: RefObject<string | undefined>;
}

export function TableHeaderCheckbox<TData>({
  table,
  lastSelectedRowId,
}: TableHeaderCheckboxProps<TData>) {
  const handler = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // eslint-disable-next-line react-compiler/react-compiler
      lastSelectedRowId.current = undefined;
      table.getToggleAllRowsSelectedHandler()(e);
    },
    [table, lastSelectedRowId],
  );
  return (
    <IndeterminateCheckbox
      state={
        table.getIsAllRowsSelected()
          ? "checked"
          : table.getIsSomeRowsSelected()
            ? "indeterminate"
            : "unchecked"
      }
      name="header-row-selection"
      onChange={handler}
    />
  );
}
