import {
  useHasStreaming<PERSON>ompletion<PERSON>tom,
  useStreamingCompletionError<PERSON>tom,
  useStreamingCompletionsListener,
  useStreamingCompletionConsoleMessagesAtom,
  useStreamingCompletionTaskDoneAtom,
  useStreamingCompletionsAtom,
  useStreamingStatusAtom,
  useStreamingCompletionReasoningAtom,
} from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/atoms";
import { SINGLETON_DATASET_ID } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/stream";
import { type PlayXRunPromptsArgs } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/playx";
import { type AllCompletionsStreamingCell } from "#/ui/prompts/schema";
import { type AtomListenerCallback } from "#/utils/atomWithListener";
import { useAtomValue } from "jotai";
import React from "react";
import { useCallback, useRef } from "react";
import {
  parseGridLayoutRow as parseGridLayoutRowRaw,
  parseListLayoutRow as parseListLayoutRowRaw,
} from "./parse-row";

export type StreamingContentProps = {
  generationIdToPromptId: Record<string, string>;
  havePlaygroundLogsLoaded: boolean;
  runPrompts: (args: PlayXRunPromptsArgs) => void;
};

const parseGridLayoutRow = <TsData>({
  isStreamingEnabled,
  rowOriginal,
  index,
}: {
  isStreamingEnabled: boolean;
  rowOriginal: TsData;
  index: number;
}) => {
  if (!isStreamingEnabled) {
    return null;
  }

  const { data, spanTypeInfo, origin } = parseGridLayoutRowRaw({
    rowOriginal,
    index,
  });

  if (!data || !spanTypeInfo) {
    return null;
  }

  const datasetRowId =
    origin?.object_type === "dataset"
      ? origin.id
      : data.dataset_id && data.dataset_id !== SINGLETON_DATASET_ID
        ? data.id
        : SINGLETON_DATASET_ID;
  const generationId = spanTypeInfo.generation;

  return { datasetRowId, generationId };
};

const parseListLayoutRow = <TsData>({
  isStreamingEnabled,
  rowOriginal,
  index,
}: {
  isStreamingEnabled: boolean;
  rowOriginal: TsData;
  index: number;
}) => {
  if (!isStreamingEnabled) {
    return null;
  }

  const { id, spanTypeInfo, origin } = parseListLayoutRowRaw({
    rowOriginal,
    index,
  });

  if (!spanTypeInfo) {
    return null;
  }

  const datasetRowId = origin?.object_type === "dataset" ? origin.id : id;
  const generationId = spanTypeInfo.generation;

  return { datasetRowId, generationId };
};

export function useStreamingData<TsData>({
  colName,
  rowOriginal,
  index,
  streamingContentProps,
  isGridLayout,
}: {
  colName: string;
  rowOriginal: TsData;
  index: number;
  streamingContentProps?: StreamingContentProps;
  isGridLayout?: boolean;
}) {
  const isStreamingEnabled = colName === "output" && !!streamingContentProps;
  const parsed = isGridLayout
    ? parseGridLayoutRow({
        isStreamingEnabled,
        rowOriginal,
        index,
      })
    : parseListLayoutRow({
        isStreamingEnabled,
        rowOriginal,
        index,
      });
  const datasetRowId = parsed?.datasetRowId ?? "";
  const generationId = parsed?.generationId ?? "";
  const atom = useStreamingCompletionsAtom(datasetRowId, generationId);
  const streamingValue = useAtomValue(atom);
  return streamingValue;
}

export function useStreamingNode<TsData>({
  className,
  colName,
  rowOriginal,
  index,
  streamingContentProps,
  isGridLayout,
  fullText,
}: {
  className?: string;
  colName: string;
  rowOriginal: TsData;
  index: number;
  streamingContentProps?: StreamingContentProps;
  isGridLayout?: boolean;
  fullText?: boolean;
}): React.ReactNode | null {
  const nodeRef = useRef<HTMLParagraphElement | null>(null);
  const isStreamingEnabled = colName === "output" && !!streamingContentProps;

  const parsed = isGridLayout
    ? parseGridLayoutRow({
        isStreamingEnabled,
        rowOriginal,
        index,
      })
    : parseListLayoutRow({
        isStreamingEnabled,
        rowOriginal,
        index,
      });

  const datasetRowId = parsed?.datasetRowId ?? "";
  const generationId = parsed?.generationId ?? "";

  const { hasStreamingData, error } = useHasStreamingDataOrError({
    colName,
    rowOriginal,
    index,
    streamingContentProps,
    isGridLayout,
  });

  const callback: AtomListenerCallback<
    Record<string, Record<string, AllCompletionsStreamingCell>>
  > = useCallback(
    (_get, _set, newVal) => {
      if (isStreamingEnabled && generationId) {
        const streamingValue = newVal[datasetRowId]?.[generationId];
        const hasError = !!streamingValue?.error;

        const reasoning = streamingValue && streamingValue?.reasoning;

        const truncated = fullText
          ? streamingValue?.completion
          : streamingValue?.completion
            ? streamingValue?.completion?.slice(0, 1024)
            : reasoning; // try to display reasoning whenever available and the completion is not available

        if (truncated && !hasError) {
          if (nodeRef?.current && !nodeRef.current.textContent) {
            nodeRef.current.textContent = truncated;
            return;
          }
          // Coordinate the DOM update with the next paint
          requestAnimationFrame(() => {
            if (nodeRef.current && nodeRef.current.textContent !== truncated) {
              nodeRef.current.textContent = truncated;
            }
          });
        }
      }
    },
    [datasetRowId, fullText, generationId, isStreamingEnabled],
  );

  useStreamingCompletionsListener(isStreamingEnabled ? callback : undefined);

  // Allow the default formatter to render if there is an error or no streaming data
  if (!isStreamingEnabled || !parsed || !hasStreamingData || error) {
    return null;
  }

  // eslint-disable-next-line react-compiler/react-compiler -- this is the equivalent of JSX and is a valid use for nodeRef
  return React.createElement("p", {
    ref: nodeRef,
    className,
  });
}

export function useHasStreamingDataOrError<TsData>({
  colName,
  rowOriginal,
  index,
  streamingContentProps,
  isGridLayout,
}: {
  colName: string;
  rowOriginal: TsData;
  index: number;
  streamingContentProps?: StreamingContentProps;
  isGridLayout?: boolean;
}) {
  const isStreamingEnabled = colName === "output" && !!streamingContentProps;
  const parsed = isGridLayout
    ? parseGridLayoutRow({
        isStreamingEnabled,
        rowOriginal,
        index,
      })
    : parseListLayoutRow({
        isStreamingEnabled,
        rowOriginal,
        index,
      });

  const datasetRowId = parsed?.datasetRowId ?? "";
  const generationId = parsed?.generationId ?? "";
  const hasCompletionsAtom = useHasStreamingCompletionAtom(
    datasetRowId,
    generationId,
  );
  const errorAtom = useStreamingCompletionErrorAtom(datasetRowId, generationId);
  const reasoningAtom = useStreamingCompletionReasoningAtom(
    datasetRowId,
    generationId,
  );
  const consoleMessagesAtom = useStreamingCompletionConsoleMessagesAtom(
    datasetRowId,
    generationId,
  );
  const isTaskDoneAtom = useStreamingCompletionTaskDoneAtom(
    datasetRowId,
    generationId,
  );

  const streamingStatusAtom = useStreamingStatusAtom(
    datasetRowId,
    generationId,
  );

  return {
    datasetRowId,
    generationId,
    hasStreamingData: useAtomValue(hasCompletionsAtom),
    error: useAtomValue(errorAtom),
    consoleMessages: useAtomValue(consoleMessagesAtom),
    reasoning: useAtomValue(reasoningAtom),
    isTaskDone: useAtomValue(isTaskDoneAtom),
    streamingStatus: useAtomValue(streamingStatusAtom),
  };
}
