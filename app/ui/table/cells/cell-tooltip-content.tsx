"use client";
import React, { isValidElement, useMemo } from "react";
import { TooltipContent } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { DateTooltipContent, isValidTimestamp } from "#/ui/date";
import { flexRender } from "@tanstack/react-table";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { MarkdownViewer } from "#/ui/markdown";
import { type CellContentProps } from "./cell-content";
import { useStreamingData } from "./streaming";
import { isObjectOrArray, serializeJSONWithPlainString } from "#/utils/object";
import untruncateJson from "untruncate-json";
import { truncateJson } from "#/utils/parse";

// performance improvement for super long json objects
const MAX_LINES = 50;

const CellTooltipContent = <TsTable, TsValue>({
  cell,
  value,
  valueDiffObject,
  mergedValues,
  colorClassName,
  meta,
  index,
  streamingContentProps,
  isLoading,
}: Omit<CellContentProps<TsTable, TsValue>, "size" | "tableType">) => {
  const streamingContent = useStreamingData({
    colName: cell.column.columnDef.id ?? "",
    rowOriginal: cell.row.original,
    index: index ?? 0,
    streamingContentProps,
    isGridLayout: false,
  });

  const cellContext = cell.getContext();
  // not sure why this is rendering multiple times,
  // but the content of this tooltip shouldn't change
  // so we can improve performance by memoizing
  const defaultContent = useMemo(() => {
    return flexRender(cell.column.columnDef.cell, {
      inTable: true,
      renderForTooltip: (value: string | React.ReactNode) => {
        if (
          isValidElement(value) ||
          (typeof value !== "string" && !isObjectOrArray(value))
        ) {
          return value;
        }

        let jsonString: string = "";
        try {
          if (typeof value !== "string") {
            jsonString = JSON.stringify(truncateJson(value, 1500), null, 2);
          } else if (value.endsWith("...") || value.endsWith('..."')) {
            jsonString = untruncateJson(value);
          } else {
            jsonString = JSON.stringify(
              truncateJson(JSON.parse(value), 1500),
              null,
              2,
            );
          }
        } catch {
          return (
            <MarkdownViewer
              className="p-0 whitespace-normal"
              value={serializeJSONWithPlainString(value).replace(/\\n/g, "\n")}
            />
          );
        }
        return (
          <SyntaxHighlight
            language="json"
            className="text-xs"
            content={jsonString.split("\n").slice(0, MAX_LINES).join("\n")}
          />
        );
      },
      meta,
      value,
      valueDiffObject,
      mergedValues,
      colorClassName,
      diffIndex: index,
      ...cellContext,
    });
  }, [
    cell,
    value,
    valueDiffObject,
    cellContext,
    meta,
    mergedValues,
    colorClassName,
    index,
  ]);

  let content = defaultContent;
  if (
    (meta?.name === "start" || meta?.name === "end") &&
    typeof value === "number"
  ) {
    // Start/end are stored as seconds since epoch
    content = <DateTooltipContent date={new Date(value * 1000)} withSeconds />;
  } else if (
    (meta?.isTimestamp && value != null) ||
    (typeof value === "string" && isValidTimestamp(value))
  ) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    content = <DateTooltipContent date={new Date(value as string)} />;
  }
  if (isLoading) {
    content = null;
  }

  return (
    <TooltipContent
      className={cn(
        "max-w-[600px] overflow-hidden p-2 text-xs font-normal whitespace-pre-wrap text-primary-800",
        streamingContent ? "" : colorClassName,
        "border-primary-200 bg-primary-50",
      )}
      align="start"
      hideWhenDetached
    >
      {streamingContent || content}
    </TooltipContent>
  );
};

const genericMemo: <T>(component: T) => T = React.memo;
export default genericMemo(CellTooltipContent);
export type CellTooltipContentType = typeof CellTooltipContent;
