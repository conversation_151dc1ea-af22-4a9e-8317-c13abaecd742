import { type ComparisonExperimentSpanSummary } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/useExperiment";
import { rootSpanScanFn, whereClauseFn } from "#/ui/trace/query";
import { useDuckDB, dbQuery } from "#/utils/duckdb";
import { skipToken, useQuery } from "@tanstack/react-query";

export function useComparisonRowQuery({
  rowId,
  experiment,
}: {
  rowId: string | null;
  experiment?: ComparisonExperimentSpanSummary | null;
}) {
  const scan = experiment?.scan;
  const tableScan = experiment?.tableScan;
  const duck = useDuckDB();
  const isQueryEnabled =
    !!duck &&
    !!rowId &&
    !!scan &&
    !!tableScan &&
    (experiment?.refreshed ?? 0) > 0;
  const { data, isPending } = useQuery({
    queryKey: [
      [experiment?.tempTableName],
      "rowQuery",
      rowId,
      experiment?.scan,
      experiment?.tableScan,
    ],
    queryFn: isQueryEnabled
      ? async ({ signal }) => {
          const conn = await duck!.connect();
          const rootSpanScan = rootSpanScanFn(rowId, scan);
          const rootSpans = await dbQuery(conn, signal, rootSpanScan);
          const rootSpansArray = rootSpans
            ?.toArray()
            .map((row) => row.root_span_id);
          const whereClause = whereClauseFn(rootSpansArray);

          const query = whereClause
            ? `SELECT * from (${tableScan.replaceAll(
                "/*RE_PUSHDOWN_FILTER_SUB*/",
                whereClause,
              )})`
            : null;

          const data = await dbQuery(conn, signal, query);

          return data?.at(0) ?? null;
        }
      : skipToken,
    placeholderData: undefined,
    staleTime: Infinity,
    enabled: isQueryEnabled,
  });

  return {
    data,
    isPending,
    isQueryEnabled,
  };
}
