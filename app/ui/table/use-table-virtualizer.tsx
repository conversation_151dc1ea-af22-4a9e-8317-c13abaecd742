import { type MultilineRowProps } from "#/ui/virtual-table-body";
import { type Table } from "@tanstack/react-table";
import { useCallback } from "react";
import {
  useVirtualizer,
  useWindowVirtualizer,
  type Virtualizer,
} from "@tanstack/react-virtual";
import { type RefObject } from "react";

export type UseTableVirtualizerArgs<TsData> = {
  multilineRowConfig?: MultilineRowProps;
  scrollContainerRef?: RefObject<HTMLElement | null>;
  table: Table<TsData>;
  isGridLayout?: boolean;
  topOffset?: number;
  scrollMargin?: number;
};

export type RowVirtualizerParams =
  | {
      type: "scrollContainer";
      virtualizer: Virtualizer<HTMLElement, Element>;
    }
  | {
      type: "window";
      virtualizer: Virtualizer<Window, Element>;
    };

type TableVirtualizer = {
  measureColumns: () => void;
  measureRows: () => void;
  scrollToIndex: Virtualizer<HTMLElement, Element>["scrollToIndex"];
  rowVirtualizerParams: RowVirtualizerParams;
  columnVirtualizer: Virtualizer<HTMLElement, Element>;
};

export default function useTableVirtualizer<TsData>({
  multilineRowConfig,
  scrollContainerRef,
  table,
  isGridLayout,
  topOffset = 0,
  scrollMargin,
}: UseTableVirtualizerArgs<TsData>): TableVirtualizer {
  const visibleColumns = table.getVisibleLeafColumns();
  const columnVirtualizer = useVirtualizer({
    count: visibleColumns.length + 1,
    horizontal: true,
    getScrollElement: () => scrollContainerRef?.current ?? null,
    overscan: 3,
    estimateSize: (columnIndex) => visibleColumns[columnIndex]?.getSize() ?? 0,
  });

  const rows = table.getRowModel().rows;
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    estimateSize: (i) =>
      getRowHeight({
        ...multilineRowConfig,
        isGroupRow: rows[i]?.getCanExpand(),
      }),
    getScrollElement: () => scrollContainerRef?.current ?? null,
    getItemKey: useCallback((index: number) => rows[index]?.id, [rows]),
    overscan: isGridLayout ? 5 : 10,
    initialOffset: topOffset,
    scrollMargin,
  });

  const windowRowVirtualizer = useWindowVirtualizer({
    count: rows.length,
    estimateSize: (i) =>
      getRowHeight({
        ...multilineRowConfig,
        isGroupRow: rows[i]?.getCanExpand(),
      }),
    getItemKey: useCallback((index: number) => rows[index]?.id, [rows]),
    overscan: 10,
    initialOffset: topOffset,
  });

  const measureColumns = columnVirtualizer.measure;
  const measureRows = scrollContainerRef
    ? rowVirtualizer.measure
    : windowRowVirtualizer.measure;
  const scrollToIndex = scrollContainerRef
    ? rowVirtualizer.scrollToIndex
    : windowRowVirtualizer.scrollToIndex;

  return {
    measureColumns,
    measureRows,
    scrollToIndex,
    columnVirtualizer,
    // This is intentionally unmemoized so that VirtualTableBody updates
    // every time the virtualizer triggers a re-render
    rowVirtualizerParams: scrollContainerRef
      ? {
          type: "scrollContainer",
          virtualizer: rowVirtualizer,
        }
      : {
          type: "window",
          virtualizer: windowRowVirtualizer,
        },
  };
}

type GetRowHeightProps = MultilineRowProps & { isGroupRow?: boolean };

export const getRowHeight = ({
  numRows = 1,
  gapSize = 0,
  isGroupRow = false,
  numGroupRows = 1,
  fixedHeight,
}: GetRowHeightProps) => {
  if (isGroupRow) {
    return numGroupRows * 26 + 24;
  }
  if (fixedHeight) {
    return fixedHeight;
  }

  return numRows <= 1 ? 38 : 16 + numRows * 21 + (numRows - 1) * gapSize;
};
