import {
  type RowId,
  isDiffObject,
  makeRowIdPrimary,
} from "#/utils/diffs/diff-objects";
import { type Dispatch, useEffect } from "react";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import isEqual from "lodash.isequal";
import { SINGLETON_DATASET_ID } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/stream";

type Args = {
  rowIds: RowId[];
  setRowIds: Dispatch<RowId[]>;
};

export default function useActiveRowEffects({ rowIds, setRowIds }: Args) {
  const [{ r: activeRowId }, setActiveRowAndSpan] = useActiveRowAndSpan();

  useEffect(() => {
    const primaryRowId = makeRowIdPrimaryOrigin(activeRowId);
    const matchingRowId = rowIds.find((r) => {
      return matchesRowId(r, primaryRowId);
    });

    // hack for playx mode single row
    if (
      isDiffObject(activeRowId) &&
      activeRowId.originId === SINGLETON_DATASET_ID &&
      rowIds.length > 0
    ) {
      setActiveRowAndSpan({ r: rowIds[0] }, { history: "replace" });
      return;
    }

    if (!matchingRowId) return;
    if (
      isDiffObject(activeRowId) &&
      isDiffObject(matchingRowId) &&
      !isEqual(activeRowId, matchingRowId)
    ) {
      // in multi-experiment mode, the diff object may increase/decrease in size when experiments are selected
      // in playX mode, the diff values may change as data comes in
      // in playX mode, the diffModeEnabled key may change
      setActiveRowAndSpan({ r: matchingRowId }, { history: "replace" });
      return;
    }

    if (
      (isDiffObject(matchingRowId) && !isDiffObject(activeRowId)) ||
      (!isDiffObject(matchingRowId) && isDiffObject(activeRowId))
    ) {
      setActiveRowAndSpan({ r: matchingRowId }, { history: "replace" });
    }
  }, [activeRowId, setActiveRowAndSpan, rowIds]);

  useEffect(() => {
    if (!activeRowId) return;

    // If the activeRowId is set, update trace state on mount
    setRowIds(rowIds);
    // we only want this hook to run on mount
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}

export function matchesRowId(row: RowId, rowIdToMatch: string | null) {
  if (rowIdToMatch === null) {
    return false;
  }

  const primaryRowId = makeRowIdPrimary(row);
  if (primaryRowId === rowIdToMatch) {
    return true;
  }

  if (isDiffObject(row) && row.originId) {
    return row.originId === rowIdToMatch;
  }

  return false;
}

// with the playground, the first prompt may not have been run but other prompts have
// in these cases, the primaryRowId will be null so we have to rely on the origin
export function makeRowIdPrimaryOrigin(row: RowId | null) {
  if (row === null) {
    return null;
  }

  const primaryRowId = makeRowIdPrimary(row);
  if (primaryRowId) {
    return primaryRowId;
  }

  if (isDiffObject(row) && row.originId) {
    return row.originId;
  }

  return null;
}
