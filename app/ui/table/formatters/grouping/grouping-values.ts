import { isDiffObject, DiffRightField } from "#/utils/diffs/diff-objects";
import { z } from "zod";

function groupKeySchema(groupKey: string) {
  return z.object({
    [groupKey]: z.unknown(),
  });
}

export function getGroupValue<TsData>(rowOriginal: TsData, groupKey: string) {
  const parsed = groupKeySchema(groupKey).safeParse(rowOriginal);
  const groupValueRaw = parsed.success ? parsed.data[groupKey] : "null";
  return (
    (isDiffObject<string>(groupValueRaw)
      ? groupValueRaw[DiffRightField]
      : `${groupValueRaw}`) ?? "null"
  );
}
