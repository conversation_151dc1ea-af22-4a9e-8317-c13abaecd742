import { type FormatterProps } from "#/ui/arrow-table";
import {
  DiffObject,
  DiffScore,
  DiffText,
  roundedScoreInPercents,
} from "#/ui/diff";
import { DataType } from "apache-arrow";
import { isDuckDBJSONType } from "#/utils/schema";
import { DiffRightField } from "#/utils/diffs/diff-objects";
import { cn } from "#/utils/classnames";
import { NullFormatter } from "./null-formatter";

const CELL_MAX_CHARS = 128;

export const DiffFormatterFactory = ({
  addedFieldName,
  removedFieldName,
}: {
  addedFieldName: string;
  removedFieldName: string;
}) => {
  return function DiffFormatter<TsTable, TsValue>({
    value: valueProp,
    inTable,
    meta,
  }: FormatterProps<TsTable, TsValue>) {
    const value = valueProp?.toArray?.();

    if (!value) {
      return null;
    }

    if (!DataType.isStruct(meta.type)) {
      return null;
    }

    const [current, comparison] = value;

    if (
      DataType.isList(meta.type.children[0]) &&
      DataType.isList(meta.type.children[1])
    ) {
      const l1Struct = DataType.isStruct(
        meta.type.children[1].type.children[0],
      );
      const l2Struct = DataType.isStruct(
        meta.type.children[0].type.children[0],
      );
      const l1 = comparison
        ?.toArray?.()
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        .map((v: any) => (l1Struct && v?.toJSON ? v.toJSON() : v));
      const l2 = current
        ?.toArray?.()
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        .map((v: any) => (l2Struct && v?.toJSON ? v?.toJSON() : v));
      return (
        <>
          <DiffObject
            oldObject={l1}
            newObject={l2}
            prefix={inTable ? CELL_MAX_CHARS : undefined}
            addedFieldName={inTable ? undefined : addedFieldName}
            removedFieldName={inTable ? undefined : removedFieldName}
          />
        </>
      );
    } else if (
      DataType.isStruct(meta.type.children[0]) &&
      DataType.isStruct(meta.type.children[1])
    ) {
      const l1 = comparison?.toJSON?.();
      const l2 = current?.toJSON?.();
      return (
        <>
          <DiffObject
            oldObject={l1}
            newObject={l2}
            prefix={inTable ? CELL_MAX_CHARS : undefined}
            addedFieldName={inTable ? undefined : addedFieldName}
            removedFieldName={inTable ? undefined : removedFieldName}
          />
        </>
      );
    } else if (isDuckDBJSONType(meta.type.children[0]?.type, meta.typeHint)) {
      const l1 = comparison ? JSON.parse(comparison) : null;
      const l2 = current ? JSON.parse(current) : null;
      return (
        <>
          <DiffObject
            oldObject={l1}
            newObject={l2}
            prefix={inTable ? CELL_MAX_CHARS : undefined}
            addedFieldName={inTable ? undefined : addedFieldName}
            removedFieldName={inTable ? undefined : removedFieldName}
          />
        </>
      );
    } else {
      return (
        <DiffText
          oldText={`${comparison}`}
          newText={`${current}`}
          prefix={inTable ? CELL_MAX_CHARS : undefined}
          addedFieldName={inTable ? undefined : addedFieldName}
          removedFieldName={inTable ? undefined : removedFieldName}
        />
      );
    }
  };
};

export const DiffScoreFormatter = <TsTable, TsValue>({
  value,
  valueDiffObject,
  colorClassName,
  renderForTooltip,
}: FormatterProps<TsTable, TsValue>) => {
  const originalValue =
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    (valueDiffObject?.[DiffRightField] as number | null) ?? null;
  const content =
    value == null ? (
      <NullFormatter />
    ) : (
      <span>{roundedScoreInPercents(value)}</span>
    );
  if (renderForTooltip) {
    if (!valueDiffObject) {
      return content;
    }
    return renderForTooltip(
      <DiffScore
        newScore={originalValue}
        oldScore={value}
        oldScoreSwatchClassName={colorClassName}
        oldScoreClassName={cn(colorClassName, "bg-transparent")}
      />,
    );
  }
  return (
    <span className="block truncate text-left">
      {content}
      {value != null && valueDiffObject && originalValue !== value && (
        <DiffScore
          newScore={originalValue ?? null}
          oldScore={value}
          changedOnly
        />
      )}
    </span>
  );
};
