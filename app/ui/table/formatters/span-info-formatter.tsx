import { type FormatterProps } from "#/ui/arrow-table";
import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { z } from "zod";
import { getSpanDisplayConfig } from "#/ui/trace/span-display";
import { cn } from "#/utils/classnames";
import { Spinner } from "#/ui/icons/spinner";
import { safeDeserializeUnknown } from "#/utils/object";

export const spanTypeInfoSchema = z.object({
  name: z.string(),
  type: z.string().nullish(),
  cached: z
    .number()
    .nullish()
    .transform((n) => !!n),
  remote: z.number().transform((n) => !!n),
  has_error: z.boolean(),
  in_progress: z.boolean(),
  is_playground: z.boolean().nullish(),
});

export function SpanTypeInfoFormatter<TsTable, TsValue>({
  value: valueProp,
  ...rest
}: FormatterProps<TsTable, TsValue>) {
  if (valueProp === null) {
    return null;
  }

  if (isDiffObject(valueProp)) {
    return (
      <SpanTypeInfoFormatter value={valueProp[DiffRightField]} {...rest} />
    );
  }

  let value;
  try {
    value = spanTypeInfoSchema.parse(safeDeserializeUnknown(valueProp));
  } catch (e) {
    return null;
  }

  const isStalePlaygroundRow =
    value.is_playground && getIsStalePlaygroundRow(rest.row.original);
  const { Icon, lightClassName } = getSpanDisplayConfig({
    type: value.type ?? undefined,
    cached: value.cached,
    remote: value.remote,
    hasError: value.has_error,
  });

  return (
    <div className="flex items-center gap-2 truncate">
      {value.in_progress ? (
        <div className="relative flex size-5 flex-none items-center justify-center rounded-[3px] bg-primary-100">
          <Spinner className="size-[12px]" />
        </div>
      ) : (
        <div
          className={cn(
            "relative flex size-5 flex-none items-center justify-center rounded-[3px]",
            lightClassName,
          )}
        >
          <Icon strokeWidth={2.5} className="size-3" />
        </div>
      )}
      <span
        className={cn("flex-1 truncate", {
          "opacity-50": value.in_progress,
        })}
      >
        {value.name}
      </span>
      {isStalePlaygroundRow && (
        <span className="text-xs text-gray-500">(stale)</span>
      )}
    </div>
  );
}

const playgroundRowSchema = z.object({
  _xact_id: z.string(),
  origin: z.string().nullish(),
});

function getIsStalePlaygroundRow(rowOriginal: unknown) {
  const row = playgroundRowSchema.safeParse(rowOriginal);
  if (!row.success || !row.data.origin) {
    return false;
  }
  return false;
  // TODO: query with pushdown for the latest dataset xact_id?
  // braintrust/app/ui/table/cells/comparison-row-query.tsx
}
