import { type FormatterProps } from "#/ui/arrow-table";
import { Avatar, AvatarGroup, type AvatarSize } from "#/ui/avatar";
import { cn } from "#/utils/classnames";
import { Vector } from "apache-arrow";
import React from "react";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { Server } from "lucide-react";
import { isServiceAccountEmail } from "#/utils/user";

type CreatorProps = {
  avatar_url: string;
  given_name: string;
  family_name: string;
  // older creator fields
  avatar?: string;
  name?: string;
  email?: string;
};

function Creator({
  className,
  avatar_url,
  given_name,
  family_name,
  size = "xs",
  avatar,
  name,
  email,
}: {
  className?: string;
  size?: AvatarSize;
} & CreatorProps) {
  return (
    <div
      className={cn("flex items-center gap-x-2 truncate font-inter", className)}
    >
      {isServiceAccountEmail(email) ? (
        <div className="flex size-4 items-center justify-center">
          <Server className="size-3 text-primary-600" />
        </div>
      ) : (
        <Avatar
          imgUrl={avatar_url || avatar}
          size={size}
          className="flex-none"
        />
      )}
      {given_name || family_name ? (
        <span className="flex-1 truncate">
          {given_name} {family_name}
        </span>
      ) : name ? (
        <span className="flex-1 truncate">{name}</span>
      ) : email ? (
        <span className="flex-1 truncate">{email}</span>
      ) : (
        <span className="flex-1 truncate">Unnamed</span>
      )}
    </div>
  );
}

export function CreatorFormatter<
  TsTable extends {
    [BT_IS_GROUP]?: boolean;
  },
  TsValue,
>(props: FormatterProps<TsTable, TsValue>) {
  const { cell, value: valueProp, renderForTooltip } = props;

  if (cell.row.original[BT_IS_GROUP]) {
    const valueArray =
      valueProp instanceof Vector ? valueProp.toArray() : valueProp;
    if (!Array.isArray(valueArray)) {
      return null;
    }
    const values: CreatorProps[] = valueArray.flatMap((v: string) => {
      let parsed = null;
      try {
        parsed = JSON.parse(v);
      } catch {
        // Swallow
      }
      return parsed ? [parsed] : [];
    });
    if (renderForTooltip) {
      return (
        <div className="flex flex-col gap-1">
          {values.map((v, i) => (
            <Creator key={i} {...v} />
          ))}
        </div>
      );
    }

    return values.length > 1 ? (
      <div className="flex items-center gap-x-2 px-3">
        <div className="flex-none">
          <AvatarGroup
            avatars={values.map((v: CreatorProps) => ({
              imgUrl: v.avatar_url,
              size: "xs",
            }))}
          />
        </div>
        <span className="truncate">{values.length} creators</span>
      </div>
    ) : (
      <div className="px-3">
        <Creator className="font-medium" {...values[0]} />
      </div>
    );
  }

  try {
    return <Creator {...JSON.parse(valueProp)} />;
  } catch {
    return null;
  }
}
