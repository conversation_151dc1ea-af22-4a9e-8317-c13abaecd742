import { type FormatterProps } from "#/ui/arrow-table";
import { Percent } from "#/ui/trace/diff-score-object";
import { DataType } from "apache-arrow";
import { DefaultFormatter } from "./default-formatter";
import {
  GroupAggregationFormatterFactory,
  type GroupAggregationProps,
} from "./group-aggregation-formatter";
import { DiffScoreFormatter } from "./diff-formatter";
import { type BT_IS_GROUP } from "#/ui/table/grouping/queries";

export function PercentFormatter<
  TsTable extends { [BT_IS_GROUP]?: boolean },
  TsValue,
>({ value, meta, ...props }: FormatterProps<TsTable, TsValue>) {
  if (props.valueDiffObject) {
    return <DiffScoreFormatter value={value} meta={meta} {...props} />;
  } else if (DataType.isStruct(meta.type)) {
    return <Percent value={value} type={meta.type} />;
  } else if (meta.isNumeric || typeof value === "number") {
    return <Percent value={value} type={meta.type} />;
  } else {
    return <DefaultFormatter value={value} meta={meta} {...props} />;
  }
}

export const PercentWithAggregationFormatter = ({
  isGrouping,
  ...groupAggregationProps
}: GroupAggregationProps) => ({
  cell: GroupAggregationFormatterFactory({
    render: PercentFormatter,
    renderForGroup: (props) => <Percent {...props} />,
    ...groupAggregationProps,
    supportedAggregationTypes: ["avg", "max", "min"],
  }),
  colSize:
    groupAggregationProps.summaryEnabled || isGrouping
      ? {
          minSize: 200,
          size: 200,
        }
      : undefined,
});
