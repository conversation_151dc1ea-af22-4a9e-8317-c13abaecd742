import { type FormatterProps } from "#/ui/arrow-table";
import { conciseTimeFormat } from "#/ui/date";
import { isEmpty } from "#/utils/object";
import { NullFormatter } from "./null-formatter";

export function StartEndFormatter<TsData, TsValue>({
  value,
  meta,
}: FormatterProps<TsData, TsValue>) {
  if (value && Number.isFinite(value)) {
    // Start/end are stored as seconds since epoch
    return <>{conciseTimeFormat(value * 1000)}</>;
  } else if (isEmpty(value)) {
    return <NullFormatter />;
  }
}
