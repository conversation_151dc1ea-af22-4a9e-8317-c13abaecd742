import { type FormatterProps } from "#/ui/arrow-table";
import { deserializeJSONStringAsString } from "#/utils/object";
import { AlertCircle } from "lucide-react";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";

function ErrorFormatter<TsTable, TsValue>(
  props: FormatterProps<TsTable, TsValue>,
) {
  const { value } = props;
  return value && typeof value === "string" ? (
    <span className="font-mono text-xs">
      <AlertCircle className="-mt-0.5 mr-1.5 inline size-3 align-middle text-bad-500" />
      <span>{deserializeJSONStringAsString(value).slice?.(0, 1000)}</span>
    </span>
  ) : (
    <NullFormatter />
  );
}

export const ErrorCellWithFormatter = {
  cell: ErrorFormatter,
  mergeInto: "output",
};
