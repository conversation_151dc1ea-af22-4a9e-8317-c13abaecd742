import type { FormatterProps } from "#/ui/arrow-table";
import type { FormatComponent } from "#/ui/field-to-column";
import { cn } from "#/utils/classnames";
import { Utf8, type Vector } from "apache-arrow";
import {
  type GroupAggregationProps,
  type SummaryProps,
} from "./group-aggregation-formatter";
import { HeaderSummary } from "#/ui/table/header-summary";
import { GROUP_BY_INPUT_VALUE } from "#/ui/table/grouping/controls";
import { getGroupValue } from "./grouping/grouping-values";
import { DefaultValueDisplay } from "./default-formatter";
import { JSONType } from "#/utils/schema";
import { COLOR_CLASSNAMES } from "#/ui/color";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import {
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "#/ui/dropdown-menu";
import {
  CompareMenuItems,
  type RowComparisonFormatterProps,
} from "./row-comparison-formatter";
import { Tooltip, TooltipTrigger, TooltipPortal } from "#/ui/tooltip";
import CellTooltipContent from "#/ui/table/cells/cell-tooltip-content";
import { type Cell } from "@tanstack/react-table";
import { getDiffRight } from "#/utils/diffs/diff-objects";
import { deserializePlainStringAsJSON } from "#/utils/object";

export type GroupKeyFormatterType = {
  [BT_IS_GROUP]?: boolean;
} & Record<string, unknown>;

type GroupKeyFormatterProps<TsData, TsValue> = {
  Formatter: FormatComponent<TsData, TsValue>;
  groupKey: string;
  groupAggregationProps?: GroupAggregationProps;
  colorMap?: Record<string, number>;
  rowComparisonProps?: RowComparisonFormatterProps;
};

export function GroupKeyFormatter<
  TsData extends GroupKeyFormatterType,
  TsValue,
>({
  Formatter,
  groupKey,
  groupAggregationProps,
  colorMap,
  rowComparisonProps,
}: GroupKeyFormatterProps<TsData, TsValue>) {
  return function formatter(props: FormatterProps<TsData, TsValue>) {
    const { cell, renderForTooltip } = props;

    if (!cell.row.original[BT_IS_GROUP]) {
      return <Formatter {...props} />;
    }

    const groupValue = getGroupValue(cell.row.original, groupKey);
    const groupBy = groupAggregationProps?.summaryData?.groupBy;
    const summaryGroupValue =
      groupBy === GROUP_BY_INPUT_VALUE
        ? getGroupValue(cell.row.original, "comparison_key")
        : groupValue;
    if (renderForTooltip) {
      return renderForTooltip(groupValue);
    }

    const subrowCount = cell.row.subRows.length;
    return (
      <span className="flex items-baseline overflow-hidden px-3 text-base font-medium">
        {colorMap?.[groupValue] != null && (
          <span
            className={cn(
              "mr-2 inline-block size-3 flex-none rounded-full",
              COLOR_CLASSNAMES[colorMap[groupValue]],
            )}
          />
        )}
        <Tooltip disableHoverableContent>
          <TooltipTrigger asChild>
            <span className="flex-1 truncate">
              {groupBy === GROUP_BY_INPUT_VALUE ? (
                // a little bit of a hack for now to display input values without quotes
                <DefaultValueDisplay
                  metaType={new Utf8()}
                  typeHint={JSONType}
                  inTable={true}
                  value={groupValue}
                  row={cell.row.original}
                />
              ) : (
                groupValue
              )}
            </span>
          </TooltipTrigger>
          <TooltipPortal>
            <CellTooltipContent {...props} />
          </TooltipPortal>
        </Tooltip>{" "}
        {subrowCount != null && (
          <span className="flex flex-none items-center pl-2 text-xs font-normal text-primary-500 tabular-nums">
            {groupAggregationProps && groupAggregationProps.summaryData ? (
              <SummaryDropdown
                summaryData={groupAggregationProps.summaryData}
                groupBy={groupBy}
                groupValue={summaryGroupValue}
                totalCount={subrowCount}
                cell={cell}
                rowComparisonProps={rowComparisonProps}
                groupAggregationProps={groupAggregationProps}
              />
            ) : (
              <span>{subrowCount}</span>
            )}
          </span>
        )}
      </span>
    );
  };
}

export function GroupKeyFormatterWithCell<
  TsData extends GroupKeyFormatterType,
  TsValue,
>(props: GroupKeyFormatterProps<TsData, TsValue>) {
  return {
    cell: GroupKeyFormatter(props),
    colSpan: true,
    groupColumnName: props.groupKey,
  };
}

function SummaryDropdown<
  TsData extends {
    comparison_key?: string;
    __bt_internal?: Record<string, { compareIds?: Vector }>;
  } & GroupKeyFormatterType,
  TsValue,
>({
  summaryData,
  groupBy,
  groupValue,
  totalCount,
  cell,
  rowComparisonProps,
  groupAggregationProps,
}: {
  summaryData: SummaryProps;
  groupBy?: string;
  groupValue: string;
  totalCount: number;
  cell: Cell<TsData, TsValue>;
  rowComparisonProps?: RowComparisonFormatterProps;
  groupAggregationProps?: GroupAggregationProps;
}) {
  const {
    aggregationExperimentId,
    experimentSummaryData,
    addRegressionFilter,
  } = summaryData;
  const selectedExperiment =
    experimentSummaryData.experiments.find(
      ({ experiment }) => experiment.id === aggregationExperimentId,
    ) ?? experimentSummaryData.experiments[0];

  const comparisonKey = `${getDiffRight(cell?.row.original.comparison_key) ?? ""}`;

  const customColumn = groupAggregationProps?.customColumns?.find(
    ({ name }) => name === groupBy,
  );
  const parsedGroupBy = deserializePlainStringAsJSON(groupBy ?? "").value;
  return (
    <HeaderSummary
      title={null}
      addRegressionFilter={addRegressionFilter}
      generateRegressionFilter={(comparisonType, id) => ({
        comparisonType,
        field: {
          type: "none",
          value: "",
        },
        anyField: true,
        group:
          groupBy === GROUP_BY_INPUT_VALUE
            ? {
                type: "input",
                value: groupValue,
              }
            : customColumn
              ? {
                  type: "custom",
                  expr: customColumn.expr,
                  name: customColumn.name,
                  value: groupValue,
                }
              : Array.isArray(parsedGroupBy)
                ? {
                    type: "metadata",
                    path: parsedGroupBy,
                    value: groupValue,
                  }
                : undefined,
        experimentId: id ?? "any",
      })}
      fieldName=""
      aggregations={{
        type: "groupTotals",
        totalCount,
        additionalMenuItems:
          groupBy === GROUP_BY_INPUT_VALUE && rowComparisonProps ? (
            <>
              <DropdownMenuGroup>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>Compare with</DropdownMenuSubTrigger>
                  <DropdownMenuSubContent>
                    <CompareMenuItems
                      rowOriginal={cell?.row.subRows.at(0)?.original}
                      comparisonKey={comparisonKey}
                      rowComparisonProps={rowComparisonProps}
                    />
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              </DropdownMenuGroup>
            </>
          ) : undefined,
      }}
      summary={{
        ...selectedExperiment.groupTotals?.[groupValue],
        experiment: selectedExperiment.experiment,
      }}
      cachedCount={null}
      summaryData={experimentSummaryData.experiments}
      columnType="scores"
      metricDefinition={groupAggregationProps?.metricDefinition}
    />
  );
}
