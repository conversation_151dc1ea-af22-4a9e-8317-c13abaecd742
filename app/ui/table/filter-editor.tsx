import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "#/ui/tabs";
import { BTQLFilterForm } from "./btql-filter-form";
import { <PERSON><PERSON> } from "#/ui/button";
import { Calendar } from "#/ui/calendar";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ListFilter, PercentIcon, XIcon } from "lucide-react";
import { type Column, type ColumnMeta } from "@tanstack/react-table";
import type { FormatterMap } from "#/ui/field-to-column";
import { Input } from "#/ui/input";
import {
  type Dispatch,
  memo,
  type PropsWithChildren,
  type SetStateAction,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { cn } from "#/utils/classnames";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import type {
  ClauseSpec,
  ClauseChecker,
  ClauseType,
  Search,
} from "#/utils/search/search";
import { getBubbles } from "#/utils/search/search-bubbles";
import { TagsCombobox, type TagsProjectInfo } from "#/ui/trace/tags";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { Tag } from "#/ui/tag";
import { useHotkeys } from "react-hotkeys-hook";
import { isAlphaNumeric } from "#/utils/string";
import { useRecentFilters } from "./use-recent-filters";
import {
  ALL_EXPERIMENT_COLOR_CLASSNAMES,
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
} from "#/ui/color";
import {
  comparisonTypeEnum,
  type RegressionFilter,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { type DateRange } from "react-day-picker";
import { subDays, format, endOfDay, startOfDay } from "date-fns";
import {
  FilterEditorColumnsSelect,
  isScoreColumn,
} from "./filters/filter-editor-columns-select";
import {
  defaultOperators,
  FilterEditorOperatorSelect,
  scoreComparisonOperators,
  type FilterEditorOperator,
} from "./filters/filter-editor-operator-select";
import { STARRED_TAG } from "./star-cell";
import { useFeatureFlags } from "#/lib/feature-flags";
import { isObject } from "braintrust/util";
import { DataType } from "apache-arrow";
import { BasicTooltip } from "#/ui/tooltip";
import { RowStarColumnId } from "./display-columns";
import { FilterEditorNestedFields } from "./filters/filter-editor-nested-fields";
import {
  getInferField,
  inferDataToInferFields,
  CUSTOM_FIELD_TYPE,
  type NestedPath,
  type InferField,
} from "./filters/filter-editor-infer-data";
import { jsonTypeToColumnType } from "./filters/json-type-to-column-type";
import { FilterEditorTopValuesSelector } from "./filters/filter-editor-top-values-selector";
import { AssignCombobox } from "#/ui/trace/assign";
import { OrgUsersContext } from "#/utils/org-users-context";
import { BT_ASSIGNMENTS } from "#/utils/assign";
import { Skeleton } from "#/ui/skeleton";
import { useInferQuery } from "#/utils/btql/infer";
// BTQL-TODO: Re-enable this tab once we have a good tool for BTQLs
// import { useIsLoopEnabled } from "#/ui/optimization/optimization-chat";

const INFER_SUPPORTED_FIELDS = new Set([
  "metadata",
  "input",
  "output",
  "expected",
  "id",
  "metrics",
  "scores",
]);

const BTQL_FILTER_EXAMPLES: Record<string, string[]> = {
  project_logs: [
    'span_attributes.type = "llm"',
    "created > now() - interval 1 day",
    'metadata.user_id = "123"',
    "scores.Factuality > 0.8",
  ],
  //TODO add more examples. objectType is undefined elsewhere right now
};

export type FilterEditorColumnType =
  | "id"
  | "span_name"
  | "text"
  | "full-text"
  | "number"
  | "score"
  | "date"
  | "boolean"
  | "tags"
  | "assignments"
  | "comments"
  | "starred";

// A reduced version of Column to ease reuse of component
export interface MinimalColumnData<TsData> {
  id: Column<TsData>["id"];
  meta: Column<TsData>["columnDef"]["meta"];
  columnDefId: Column<TsData>["columnDef"]["id"];
}

interface FilterEditorProps<TsData, TsValue> {
  enableStarColumn?: boolean;
  clauseToReplace?: ClauseSpec<ClauseType>;
  selectableColumns: MinimalColumnData<TsData>[];
  formatters?: FormatterMap<TsData, TsValue>;
  objectId?: string | null;
  objectType?: string | null;
  clauseChecker: ClauseChecker | null;
  search?: Search;
  setSearch: Dispatch<SetStateAction<Search>>;
  baseExperiment?: {
    name: string;
    id: string;
  };
  comparisonExperiments?: {
    name: string;
    id: string;
  }[];
  initialExperimentId?: string;
  addRegressionFilter?: (filter: RegressionFilter) => void;
  isOpen: boolean;
  setOpen: (open: boolean) => void;
  forceTab?: "basic" | "btql";
  sideOffset?: number;
  alignOffset?: number;
  disabled?: boolean;
  disableNegativeTagFilters?: boolean;
  disableTooltip?: boolean;
  tooltipContent?: string;
  isPlayground?: boolean;
  scoreNames?: string[];
  tagsProvidedProject?: TagsProjectInfo;
  suggestions?: { label: string; btql: string }[];
  column?: MinimalColumnData<TsData> | undefined;
  setColumn?: (column: MinimalColumnData<TsData> | undefined) => void;
}

function FilterEditorComponent<TsData, TsValue>({
  enableStarColumn,
  selectableColumns,
  objectType,
  objectId,
  clauseToReplace,
  formatters,
  clauseChecker,
  search,
  setSearch,
  baseExperiment,
  comparisonExperiments,
  initialExperimentId,
  addRegressionFilter,
  isOpen,
  setOpen,
  forceTab,
  sideOffset,
  alignOffset,
  children,
  disabled,
  disableNegativeTagFilters,
  disableTooltip,
  tooltipContent,
  isPlayground,
  scoreNames,
  tagsProvidedProject,
  suggestions,
  column: columnProp,
  setColumn: setColumnProp,
}: PropsWithChildren<FilterEditorProps<TsData, TsValue>>) {
  const [_column, _setColumn] = useState<
    MinimalColumnData<TsData> | undefined
  >();
  const column = columnProp ?? _column;
  const setColumn = setColumnProp ?? _setColumn;
  const [value, setValue] = useState("");
  const [btqlValue, setBtqlValue] = useState(clauseToReplace?.text ?? "");

  // BTQL-TODO: Re-enable this tab once we have a good tool for BTQLs
  // const [loopValue, setLoopValue] = useState("");
  const [tagsValue, setTagsValue] = useState<string[]>([]);
  const [dateRangeValue, setDateRangeValue] = useState<DateRange | undefined>({
    from: subDays(startOfDay(new Date()), 10),
    to: endOfDay(new Date()),
  });
  const [isCalendarPopoverOpen, setIsCalendarPopoverOpen] = useState(false);

  // BTQL-TODO: Re-enable this tab once we have a good tool for BTQLs
  // const isLoopEnabled = useIsLoopEnabled();
  const nestedFieldName = useMemo(() => {
    // use the column id as the field name
    // this will need to be remapped for wider nested filtering support
    return column?.id ?? "";
  }, [column?.id]);

  const { query: inferQuery, enabled: inferQueryEnabled } = useInferQuery({
    objectType,
    objectId,
    fieldName: nestedFieldName,
    supportedFields: INFER_SUPPORTED_FIELDS,
  });
  const {
    data: inferSchemaData,
    isLoading: inferQueryIsLoading,
    isPlaceholderData: inferQueryIsPlaceholderData,
  } = inferQuery;

  const inferFields = useMemo(() => {
    return inferDataToInferFields(inferSchemaData);
  }, [inferSchemaData]);

  const [nestedPath, setNestedPath] = useState<NestedPath | null>(null);
  const [hasSelectedCustomValue, setHasSelectedCustomValue] = useState(false);
  const inferredSelections = useInferredSelections({
    inferFields,
    nestedPath,
    hasSelectedCustomValue,
    defaultFieldName: nestedFieldName,
    value,
  });

  const appliedFilters = useMemo(() => {
    if (!search) return [];
    return getBubbles({ search, setSearch })
      .filter((s) => s.originType === "form" && !!s.text)
      .map((s) => ({
        label: s.label,
        btql: s.text,
        comparisonId: s.comparisonId,
      }));
  }, [search, setSearch]);

  const { recentFilters, addRecentFilter, clearRecentFilters } =
    useRecentFilters();
  const filteredRecentFilters = useMemo(() => {
    const comparisonExperimentIds = new Set(
      comparisonExperiments?.map((e) => e.id) ?? [],
    );
    return recentFilters.filter(
      (f) =>
        !appliedFilters.some(
          (a) =>
            a.btql === f.btql && a.comparisonId === f.comparison?.experimentId,
        ) &&
        (!f.comparison ||
          comparisonExperimentIds.has(f.comparison.experimentId)),
    );
  }, [recentFilters, appliedFilters, comparisonExperiments]);

  // use provided tags first over context
  const {
    config: { tags: tagsContext },
  } = useContext(ProjectContext);
  const tags = tagsProvidedProject?.config?.tags ?? tagsContext;

  const { orgUsers } = useContext(OrgUsersContext);

  const {
    flags: { brainstore },
  } = useFeatureFlags();

  const type: FilterEditorColumnType = useMemo(() => {
    const meta = column?.meta;
    const isScore =
      isScoreColumn(meta, column?.columnDefId, scoreNames) ||
      meta?.path?.[0] === "scores";
    const isStartOrEnd =
      meta?.path?.[0] === "metrics" &&
      (meta?.path?.[1] === "start" || meta?.path?.[1] === "end");
    if (isScore || meta?.name === "error_rate") return "score";
    if (column?.id === RowStarColumnId) return "starred";
    if (isStartOrEnd) return "date";
    if (meta?.isNumeric) return "number";
    if (meta?.isTimestamp) return "date";
    if (meta?.name === "tags") return "tags";
    if (meta?.name === BT_ASSIGNMENTS) return "assignments";
    if (meta?.name === "id") return "id";
    if (meta?.name === "span_type_info") return "span_name";
    if (meta?.name === "comments") return "comments";

    // use the nested column type if it exists
    const nestedType = inferredSelections.inferField?.type?.type;
    if (nestedType) {
      const nestedColType = jsonTypeToColumnType(nestedType);
      if (nestedColType) {
        return nestedColType;
      }
    }

    if (
      brainstore &&
      ["input", "output", "expected", "metadata"].includes(meta?.name ?? "")
    ) {
      return "full-text";
    }
    if (DataType.isBool(meta?.type)) {
      return "boolean";
    }
    return "text";
  }, [column, brainstore, inferredSelections.inferField, scoreNames]);

  const hasComparisonExperiments =
    comparisonExperiments && comparisonExperiments.length > 0;
  const operators = useMemo(() => {
    const ops = defaultOperators.filter(
      (op) => !disableNegativeTagFilters || op.id !== "tags-not-includes",
    );
    if (addRegressionFilter && hasComparisonExperiments) {
      ops.push(...scoreComparisonOperators);
    }
    if (!type) return ops;
    return ops.filter((op) => op.type.includes(type));
  }, [
    addRegressionFilter,
    disableNegativeTagFilters,
    type,
    hasComparisonExperiments,
  ]);

  const [operator, setOperator] = useState<string>(operators[0]?.id ?? "");
  const [experimentId, setExperimentId] = useState<string | undefined>(
    initialExperimentId,
  );

  const selectedOperator = useMemo(() => {
    return operators.find((op) => op.id === operator);
  }, [operators, operator]);

  useEffect(() => {
    // When the type changes, the available operators change.
    // If the operator is not available, set it to the first available operator.
    if (operators.find((op) => op.id === operator)) return;
    setOperator(operators[0]?.id ?? "");
  }, [operators, operator]);

  const parsedScoreValue = useMemo(() => {
    if (type !== "score") return null;
    return parseFloat(value);
  }, [type, value]);

  // Clamp score values between 0 and 100
  useEffect(() => {
    if (parsedScoreValue === null || Number.isNaN(parsedScoreValue)) return;
    if (parsedScoreValue > 100) {
      setValue("100");
    }
    if (parsedScoreValue < 0) {
      setValue("0");
    }
  }, [parsedScoreValue]);

  const { applySearch, isValidBTQL, disableFilters } = useFilterSortBarSearch({
    clauseChecker,
    setSearch,
  });

  const basicBTQLQuery = useMemo(() => {
    return buildBTQLClauses<TsData, TsValue>({
      columnMeta: column?.meta,
      operator: selectedOperator,
      type,
      value: inferredSelections.value,
      dateRangeValue,
      tagsValue,
      formatters,
      overridePath: inferredSelections.hasNestedFields
        ? inferredSelections.nestedPath?.namePath
        : undefined,
    });
  }, [
    column?.meta,
    selectedOperator,
    type,
    inferredSelections.value,
    dateRangeValue,
    tagsValue,
    formatters,
    inferredSelections.hasNestedFields,
    inferredSelections.nestedPath?.namePath,
  ]);

  const selectedExperiment = useMemo(() => {
    const index =
      comparisonExperiments?.findIndex((e) => e.id === experimentId) ?? -1;
    if (index > -1 && comparisonExperiments) {
      return { ...comparisonExperiments[index], index: index + 1 };
    }

    if (selectedOperator?.isRegressionFilter) {
      // in the future we can implement "any experiment" filters on non-regression filters
      // for now it's a little tricky
      if (experimentId === "any") {
        return { id: "any", name: "any experiment", index: -1 };
      }
      return comparisonExperiments?.[0]
        ? { ...comparisonExperiments[0], index: 1 }
        : undefined;
    }

    return baseExperiment ? { ...baseExperiment, index: 0 } : undefined;
  }, [
    selectedOperator?.isRegressionFilter,
    comparisonExperiments,
    experimentId,
    baseExperiment,
  ]);

  const [_tabValue, setTabValue] = useState<string>();
  const tabValue = forceTab ?? _tabValue;

  const allowsComparison =
    tabValue === "btql" ||
    (column &&
      column.id !== "input" &&
      (!isPlayground ||
        (column.id !== "metadata" && column.id !== "expected")) &&
      column.id !== RowStarColumnId);

  const showExperimentSelector =
    (forceTab && initialExperimentId) ||
    (allowsComparison &&
      (selectedOperator?.isRegressionFilter ||
        (comparisonExperiments && comparisonExperiments.length > 0)));

  const [isValid, setValid] = useState(false);

  useEffect(() => {
    if (type === "starred") {
      setValid(true);
      return;
    }
    if (selectedOperator?.isRegressionFilter && selectedExperiment?.id) {
      setValid(true);
      return;
    }
    if (!basicBTQLQuery) {
      setValid(false);
      return;
    }
    const check = async () => {
      const result = await isValidBTQL(basicBTQLQuery.clause, "filter");
      setValid(result.valid);
    };
    check();
  }, [
    experimentId,
    selectedOperator?.isRegressionFilter,
    basicBTQLQuery,
    isValidBTQL,
    type,
    selectedExperiment?.id,
  ]);

  const addFilterButtonRef = useRef<HTMLButtonElement>(null);

  useHotkeys("f", (e) => {
    e.preventDefault();
    setOpen(!isOpen);
  });

  const reset = () => {
    // Use the same default selection logic as initialization
    const nextDefault = resolveDefaultColumnId(selectableColumns);
    setColumn(nextDefault);
    setNestedPath(null);
    setValue("");
    setTagsValue([]);
    setBtqlValue(clauseToReplace?.text ?? "");
    setExperimentId(initialExperimentId);
  };

  const trigger = children ?? (
    <Button
      size="xs"
      variant={appliedFilters.length > 0 ? "default" : "ghost"}
      disabled={disabled}
      Icon={ListFilter}
    >
      <span className="hidden @xl/controls:block">Filter</span>
    </Button>
  );
  const isLoadingInferQuery =
    inferQueryEnabled && (inferQueryIsLoading || inferQueryIsPlaceholderData);
  return (
    <Popover
      open={isOpen}
      onOpenChange={(open) => {
        reset();
        setOpen(open);
      }}
    >
      <PopoverTrigger asChild>
        <div
          className={cn("flex-none truncate", {
            "pointer-events-none": disabled,
          })}
        >
          {disableTooltip ? (
            trigger
          ) : (
            <BasicTooltip tooltipContent={tooltipContent ?? "Filter"}>
              {trigger}
            </BasicTooltip>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        sideOffset={sideOffset}
        alignOffset={alignOffset}
        collisionPadding={16}
        className="flex w-[500px] flex-col overflow-hidden p-0"
        onFocusOutside={(e) => {
          // https://github.com/braintrustdata/braintrust/pull/4074
          e.preventDefault();
        }}
        onInteractOutside={(e) => {
          // dismiss only if there isn't another popover to dismiss first
          if (isCalendarPopoverOpen) {
            e.preventDefault();
          }
        }}
      >
        <Tabs defaultValue="basic" value={tabValue} onValueChange={setTabValue}>
          {!forceTab && (
            <TabsList className="mb-0 flex h-9 w-full items-end justify-start gap-4 rounded-none rounded-t-md border-b border-primary-100 bg-primary-50 px-4 py-0">
              <TabsTrigger
                value="basic"
                className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
              >
                Basic
              </TabsTrigger>
              <TabsTrigger
                value="btql"
                className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
              >
                BTQL
              </TabsTrigger>
              {/*
           //BTQL-TODO: Re-enable this tab once we have a good tool for BTQLs
           {isLoopEnabled && (
                <TabsTrigger
                  value="loop"
                  className="-mb-px rounded-none border-b p-0 pb-2 text-xs bg-transparent border-transparent data-[state=active]:bg-transparent data-[state=active]:border-primary-600"
                >
                  Filter with Loop
                </TabsTrigger>
              )} */}
            </TabsList>
          )}
          {showExperimentSelector && (
            <div className="px-4 pt-4">
              <ExperimentSelector
                selectedExperiment={selectedExperiment}
                baseExperiment={baseExperiment}
                comparisonExperiments={comparisonExperiments}
                selectedOperator={selectedOperator}
                setExperimentId={setExperimentId}
                isPlayground={isPlayground}
                isReadOnly={forceTab === "btql"}
              />
            </div>
          )}
          <TabsContent
            value="basic"
            forceMount
            className="m-0 hidden flex-col data-[state=active]:flex"
          >
            <div className="flex flex-1 flex-wrap gap-2 p-4">
              <FilterEditorColumnsSelect<TsData, TsValue>
                selectableColumns={selectableColumns}
                column={column}
                formatters={formatters}
                setColumn={setColumn}
                setNestedPath={setNestedPath}
                enableStarColumn={enableStarColumn}
              />
              {type !== "starred" && (
                <>
                  <FilterEditorNestedFields
                    inferFields={inferFields}
                    fieldName={inferredSelections.fieldName}
                    nestedPath={inferredSelections.nestedPath}
                    setNestedPath={setNestedPath}
                    isLoading={isLoadingInferQuery}
                    hasNestedFields={inferredSelections.hasNestedFields}
                  />
                  <FilterEditorOperatorSelect
                    selectedOperator={selectedOperator}
                    setOperator={setOperator}
                    operators={operators}
                    type={type}
                  />
                  <div
                    className={cn("relative flex shrink grow", {
                      "min-w-14": !selectedOperator?.isRegressionFilter,
                      "overflow-hidden": type === "tags",
                    })}
                  >
                    {selectedOperator?.isRegressionFilter ? null : selectedOperator?.hideInput ? null : type ===
                      "tags" ? (
                      <TagsCombobox
                        selectedTags={tagsValue}
                        onChange={(tag) =>
                          setTagsValue((prev) =>
                            prev.includes(tag)
                              ? prev.filter((t) => t !== tag)
                              : [...prev, tag],
                          )
                        }
                        providedProject={tagsProvidedProject}
                      >
                        <Button
                          isDropdown
                          className="flex-1 justify-start overflow-hidden text-left"
                          size="xs"
                        >
                          {tagsValue.length === 0 ? (
                            <span className="flex-1 truncate text-primary-500">
                              Select tags
                            </span>
                          ) : (
                            <span className="flex-1 truncate">
                              {tagsValue.map((t) => {
                                const tagConfig = tags?.find(
                                  (tag) => tag.name === t,
                                );
                                return (
                                  <Tag
                                    key={t}
                                    className="mr-1 align-middle"
                                    label={t}
                                    color={tagConfig?.color}
                                  />
                                );
                              })}
                            </span>
                          )}
                        </Button>
                      </TagsCombobox>
                    ) : type === "assignments" ? (
                      <AssignCombobox
                        selectedUsers={tagsValue}
                        onChange={(tag) =>
                          setTagsValue((prev) =>
                            prev.includes(tag)
                              ? prev.filter((t) => t !== tag)
                              : [...prev, tag],
                          )
                        }
                      >
                        <Button
                          isDropdown
                          className="flex-1 justify-start overflow-hidden text-left"
                          size="xs"
                        >
                          {tagsValue.length === 0 ? (
                            <span className="flex-1 truncate text-primary-500">
                              Select users
                            </span>
                          ) : (
                            <span className="flex-1 truncate">
                              {tagsValue.map((t) => {
                                const orgUser = Object.values(orgUsers).find(
                                  (user) => user.id === t,
                                );
                                return (
                                  <Tag
                                    key={t}
                                    className="mr-1 align-middle"
                                    label={
                                      orgUser?.given_name &&
                                      orgUser?.family_name
                                        ? `${orgUser.given_name} ${orgUser.family_name}`
                                        : (orgUser?.email ?? orgUser?.id ?? "")
                                    }
                                  />
                                );
                              })}
                            </span>
                          )}
                        </Button>
                      </AssignCombobox>
                    ) : type === "date" ? (
                      <Popover
                        open={isCalendarPopoverOpen}
                        onOpenChange={setIsCalendarPopoverOpen}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            size="xs"
                            id="date"
                            className="w-full justify-between"
                            isDropdown
                          >
                            {dateRangeValue?.from ? (
                              dateRangeValue.to ? (
                                <>{`${format(dateRangeValue.from, "LLL dd, y")} - ${format(dateRangeValue.to, "LLL dd, y")}`}</>
                              ) : (
                                format(dateRangeValue.from, "LLL dd, y")
                              )
                            ) : (
                              <span>Pick a date range</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="range"
                            defaultMonth={dateRangeValue?.from}
                            numberOfMonths={2}
                            selected={dateRangeValue}
                            onSelect={(dateRange) => {
                              setDateRangeValue({
                                from:
                                  dateRange?.from &&
                                  startOfDay(dateRange?.from),
                                to: dateRange?.to
                                  ? endOfDay(dateRange?.to)
                                  : dateRange?.from
                                    ? // if there is only a "from" date selected, use the end of the day time for the "to" date
                                      endOfDay(dateRange?.from)
                                    : undefined,
                              });
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    ) : isLoadingInferQuery ? (
                      <Skeleton className="h-7 min-w-20" />
                    ) : inferredSelections.hasNestedFields &&
                      selectedOperator?.showInferredValues ? (
                      <FilterEditorTopValuesSelector
                        inferField={inferredSelections.inferField}
                        value={inferredSelections.value}
                        setValue={setValue}
                        onEnter={() => {
                          addFilterButtonRef.current?.click();
                        }}
                        showCounts={appliedFilters.length === 0}
                        isScore={type === "score"}
                        hasCustomField={inferredSelections.hasCustomField}
                        hasCustomValue={inferredSelections.hasCustomValue}
                        setHasSelectedCustomValue={setHasSelectedCustomValue}
                      />
                    ) : (
                      <>
                        <Input
                          autoFocus={!inferredSelections.hasNestedFields}
                          type={type === "score" ? "number" : "text"}
                          placeholder="Enter value"
                          name="value"
                          value={value}
                          onChange={(e) => {
                            setValue(e.target.value);
                          }}
                          className={cn("h-7 flex-1 px-2 text-xs", {
                            "text-right": type === "number",
                            "pr-6 text-right": type === "score",
                          })}
                          min={type === "score" ? 0 : undefined}
                          max={type === "score" ? 100 : undefined}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              addFilterButtonRef.current?.click();
                            }
                          }}
                        />
                        {type === "score" && (
                          <div className="pointer-events-none absolute top-0 right-0 flex size-7 items-center justify-center">
                            <PercentIcon className="size-3 text-primary-500" />
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </>
              )}
              <Button
                size="xs"
                ref={addFilterButtonRef}
                variant="success"
                disabled={!isValid}
                onClick={async () => {
                  if (
                    selectedOperator?.isRegressionFilter &&
                    selectedExperiment?.id &&
                    column?.meta?.path
                  ) {
                    const comparisonType = comparisonTypeEnum.safeParse(
                      selectedOperator.value,
                    );
                    if (!comparisonType.success) {
                      return;
                    }
                    const path = column.meta.path;
                    const isMetric = path?.[0] === "metrics";
                    addRegressionFilter?.({
                      field: {
                        value: (column?.meta?.path ?? []).slice(-1)[0] ?? "",
                        type: isMetric ? "metric" : "score",
                      },
                      comparisonType: comparisonType.data,
                      experimentId: selectedExperiment.id,
                    });
                    setOpen(false);
                    return;
                  }

                  if (!basicBTQLQuery) return;
                  const comparison =
                    allowsComparison && experimentId
                      ? {
                          comparison: {
                            experimentId,
                          },
                        }
                      : {};
                  await applySearch(basicBTQLQuery.clause, undefined, {
                    originType: "form",
                    label: basicBTQLQuery.label,
                    ...comparison,
                  });
                  setOpen(false);
                  addRecentFilter({
                    btql: basicBTQLQuery.clause,
                    label: basicBTQLQuery.label,
                    type,
                    ...comparison,
                  });
                  reset();
                }}
              >
                Add filter
              </Button>
            </div>
            {filteredRecentFilters.length > 0 && (
              <div className="border-t border-primary-100 p-4">
                <div className="mb-2 flex flex-none items-center justify-between gap-2 text-xs text-primary-500">
                  Recent filters
                  <Button
                    onClick={clearRecentFilters}
                    size="xs"
                    transparent
                    className="h-auto p-0 text-primary-400 hover:text-primary-800"
                    IconRight={XIcon}
                  >
                    Clear
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {filteredRecentFilters.map((f) => (
                    <Button
                      key={`${f.btql}-${f.comparison?.experimentId ?? ""}`}
                      size="xs"
                      className="max-w-full flex-none text-primary-500"
                      onClick={async () => {
                        await applySearch(f.btql, undefined, {
                          originType: "form",
                          label: f.label,
                          ...(f.comparison
                            ? {
                                comparison: {
                                  experimentId: f.comparison.experimentId,
                                },
                              }
                            : {}),
                        });
                        setOpen(false);
                        reset();
                      }}
                    >
                      <span className="max-w-full truncate">{f.label}</span>
                      {f.comparison?.experimentId && (
                        <>
                          {" "}
                          <ComparisonTag
                            comparisonExperiments={comparisonExperiments}
                            comparisonId={f.comparison.experimentId}
                          />
                        </>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            )}
            {filteredRecentFilters.length === 0 &&
              (suggestions?.length ?? 0) > 0 && (
                <div className="border-t border-primary-100 p-4">
                  <div className="mb-2 flex flex-none items-center justify-between gap-2 text-xs text-primary-500">
                    Suggestions
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {suggestions?.map((s) => (
                      <Button
                        key={s.btql}
                        size="xs"
                        className="max-w-full flex-none text-primary-500"
                        onClick={async () => {
                          await applySearch(s.btql, undefined, {
                            originType: "form",
                            label: s.label,
                          });
                          setOpen(false);
                          reset();
                        }}
                      >
                        <span className="max-w-full truncate">{s.label}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              )}
          </TabsContent>
          <TabsContent
            value="btql"
            forceMount
            className="m-0 hidden flex-col data-[state=active]:flex"
          >
            <BTQLFilterForm
              value={btqlValue}
              setValue={setBtqlValue}
              clauseChecker={clauseChecker}
              setSearch={setSearch}
              setOpen={setOpen}
              clauseToReplace={clauseToReplace}
              experimentId={experimentId}
              disableFilters={disableFilters}
              examples={
                objectType ? BTQL_FILTER_EXAMPLES[objectType] : undefined
              }
            />
          </TabsContent>
          {/* BTQL-TODO: Re-enable this tab once we have a good tool for BTQLs
           {isLoopEnabled && (
            <TabsContent
              value="loop"
              forceMount
              className="m-0 hidden flex-col data-[state=active]:flex"
            >
              <div className="p-4">
                <Textarea
                  placeholder="How would you like to filter this experiment?"
                  className="w-full"
                  value={loopValue}
                  onChange={(e) => setLoopValue(e.target.value)}
                />
              </div>
              <div className="border-t px-4 py-2">
                <OptimizationButton
                  message={`Generate a filter for the following: ${loopValue}`}
                  size="xs"
                  variant={"primary"}
                  onSubmit={() => {
                    setLoopValue("");
                  }}
                >
                  Submit
                </OptimizationButton>
              </div>
            </TabsContent>
          )} */}
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const FilterEditor = memo(
  FilterEditorComponent,
) as typeof FilterEditorComponent;

const buildBTQLClauses = <TData, TValue>({
  columnMeta,
  operator,
  value,
  dateRangeValue,
  type,
  tagsValue,
  formatters,
  overridePath,
}: {
  columnMeta?: ColumnMeta<TData, TValue>;
  operator?: FilterEditorOperator;
  type: FilterEditorColumnType;
  prefixValue?: string;
  value: string;
  dateRangeValue: DateRange | undefined;
  tagsValue?: string[];
  formatters?: FormatterMap<TData, TValue>;
  overridePath?: string[];
}) => {
  if (type === "starred") {
    return { clause: `tags includes ["${STARRED_TAG}"]`, label: "Starred" };
  }

  const path = (() => {
    switch (type) {
      case "span_name":
        return "span_attributes.name";
      case "comments":
        return "comment.text";
      default:
        const path = overridePath ?? columnMeta?.path ?? [];
        return path.map((p) => (isAlphaNumeric(p) ? p : `\`${p}\``)).join(".");
    }
  })();
  if (!path || !operator) return null;

  const clauses = (
    isObject(operator.value) ? operator.value.values : [operator.value]
  )
    .map((v) =>
      buildBTQLClause({
        operator,
        operatorValue: v,
        path,
        value,
        dateRangeValue,
        type,
        tagsValue,
      }),
    )
    .filter(<T,>(c: T | null): c is T => !!c);

  const overridePathName =
    (overridePath && overridePath?.length > 1 && overridePath.join(".")) ||
    undefined;
  const columnName =
    overridePathName ??
    formatters?.[columnMeta?.name ?? ""]?.headerLabel ??
    columnMeta?.name ??
    path;
  const label = `${columnName} ${operator.label} ${clauses[0]?.label}`.trim();

  const joinExpr =
    isObject(operator.value) && operator.value?.joinExpr
      ? ` ${operator.value.joinExpr} `
      : " OR ";
  return {
    clause: clauses.map(({ clause }) => clause).join(joinExpr),
    label,
  };
};

function buildBTQLClause({
  operator,
  operatorValue,
  path,
  value,
  dateRangeValue,
  type,
  tagsValue,
}: {
  operator: Omit<FilterEditorOperator, "value">;
  operatorValue: string;
  path: string;
  type: FilterEditorColumnType;
  value: string;
  dateRangeValue: DateRange | undefined;
  tagsValue?: string[];
}) {
  const clause = operator.prefixValue
    ? [operator.prefixValue, path, operatorValue]
    : [path, operatorValue];

  let valueLabel = "";

  if (!operator.hideInput) {
    if (value === "" && !dateRangeValue) {
      return null;
    }

    if (type === "date" && dateRangeValue?.from && dateRangeValue?.to) {
      clause.push(
        `"${dateRangeValue.from?.toISOString()}" AND ${path} <= "${dateRangeValue.to?.toISOString()}"`,
      );
      valueLabel =
        dateRangeValue.from && dateRangeValue.to
          ? `${format(dateRangeValue.from, "LLL dd, y")} - ${format(dateRangeValue.to, "LLL dd, y")}`
          : "";
    }

    if (type === "tags" || type === "assignments") {
      if (!tagsValue || tagsValue?.length === 0) return null;
      if (clause[1] !== "is null") {
        clause.push(JSON.stringify(tagsValue));
      }
      valueLabel = tagsValue.join(", ");
    }

    if (
      type === "text" ||
      type === "full-text" ||
      type === "id" ||
      type === "span_name" ||
      type === "comments"
    ) {
      clause.push(operator.wrapLikeValue ? `"%${value}%"` : `"${value}"`);
      valueLabel = value;
    }
    if (type === "score") {
      clause.push(`${value}%`);
      valueLabel = `${value}%`;
    }
    if (type === "number") {
      clause.push(value);
      valueLabel = value;
    }
  }

  return { clause: clause.join(" "), label: valueLabel };
}

function ExperimentSelector({
  className,
  selectedExperiment,
  baseExperiment,
  comparisonExperiments,
  selectedOperator,
  setExperimentId,
  isPlayground,
  isReadOnly,
}: {
  className?: string;
  selectedExperiment:
    | {
        name: string;
        id: string;
        index: number;
      }
    | undefined;
  baseExperiment?: {
    name: string;
    id: string;
  };
  comparisonExperiments?: {
    name: string;
    id: string;
  }[];
  selectedOperator?: FilterEditorOperator;
  setExperimentId: (experimentId: string | undefined) => void;
  isPlayground?: boolean;
  isReadOnly?: boolean;
}) {
  const content = (
    <div className="flex items-center gap-1.5 truncate">
      From{" "}
      {selectedExperiment?.id !== "any" && (
        <span
          className={cn(
            "block size-2 flex-none rounded-full bg-primary-700",
            ALL_EXPERIMENT_COLOR_CLASSNAMES[selectedExperiment?.index ?? 0],
          )}
        />
      )}
      <span className="truncate">{selectedExperiment?.name}</span>
    </div>
  );

  if (isReadOnly) {
    return (
      <div className={cn("max-w-full text-xs font-medium", className)}>
        {content}
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown className="max-w-full">
          {content}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuGroup>
          {!selectedOperator?.isRegressionFilter && baseExperiment && (
            <>
              <DropdownMenuLabel>
                Base {isPlayground ? "task" : "experiment"}
              </DropdownMenuLabel>
              <ExperimentSelectorItem
                key="base"
                selectedExperimentId={selectedExperiment?.id}
                setExperimentId={() => setExperimentId(undefined)}
                id={baseExperiment.id}
                name={baseExperiment.name ?? "Base experiment"}
                index={0}
              />
            </>
          )}
          {comparisonExperiments && comparisonExperiments.length > 0 && (
            <DropdownMenuLabel>Comparison experiments</DropdownMenuLabel>
          )}
          {(comparisonExperiments ?? []).map((e, i) => (
            <ExperimentSelectorItem
              key={e.id || i}
              selectedExperimentId={selectedExperiment?.id}
              setExperimentId={setExperimentId}
              id={e.id}
              name={e.name}
              index={i + 1}
            />
          ))}
          {selectedOperator?.isRegressionFilter && (
            <DropdownMenuCheckboxItem
              onSelect={() => {
                setExperimentId("any");
              }}
              key={"any"}
            >
              any experiment
            </DropdownMenuCheckboxItem>
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function ExperimentSelectorItem({
  selectedExperimentId,
  setExperimentId,
  id,
  name,
  index,
}: {
  selectedExperimentId: string | undefined;
  setExperimentId: (experimentId: string | undefined) => void;
  id: string;
  name: string;
  index: number;
}) {
  return (
    <DropdownMenuCheckboxItem
      className="flex items-center gap-2"
      checked={id === selectedExperimentId}
      onSelect={() => {
        setExperimentId(id);
      }}
    >
      <div className="flex items-center gap-1.5">
        <span
          className={cn(
            "block size-2 flex-none rounded-full bg-primary-700",
            ALL_EXPERIMENT_COLOR_CLASSNAMES[index],
          )}
        />
        <span className="truncate">{name}</span>
      </div>
    </DropdownMenuCheckboxItem>
  );
}

export function ComparisonTag({
  className,
  comparisonExperiments,
  comparisonId,
}: {
  className?: string;
  comparisonExperiments?: { id: string; name: string }[];
  comparisonId?: string;
}) {
  const comparisonExperimentIndex =
    comparisonExperiments?.findIndex(({ id }) => id === comparisonId) ?? -1;
  const comparisonExperiment =
    comparisonExperiments?.[comparisonExperimentIndex];
  if (!comparisonExperiment) {
    return null;
  }

  return (
    <div className={className}>
      <span
        className={cn(
          "font-bold",
          EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[
            Math.max(comparisonExperimentIndex, 0)
          ],
          "bg-transparent",
          "truncate",
        )}
      >
        {comparisonExperiment?.name}
      </span>
    </div>
  );
}

function useInferredSelections({
  inferFields,
  nestedPath,
  hasSelectedCustomValue,
  defaultFieldName,
  value,
}: {
  inferFields: InferField[];
  nestedPath: NestedPath | null;
  hasSelectedCustomValue: boolean;
  defaultFieldName: string;
  value: string;
}) {
  const inferField = useMemo(() => {
    return getInferField(inferFields, nestedPath) ?? inferFields[0];
  }, [inferFields, nestedPath]);

  const fieldName = nestedPath?.namePath?.[0] ?? defaultFieldName;
  const hasNestedFields = inferFields.some((f) => f.namePath[0] === fieldName);
  const hasCustomValue =
    nestedPath?.type === CUSTOM_FIELD_TYPE || hasSelectedCustomValue;
  return {
    fieldName,
    inferField,
    nestedPath: nestedPath ?? inferField,
    value: hasCustomValue
      ? value
      : value || String(inferField?.topValues[0]?.value ?? ""),
    hasNestedFields,
    hasCustomField: nestedPath?.type === CUSTOM_FIELD_TYPE,
    hasCustomValue,
  };
}

export function resolveDefaultColumnId<TsData>(
  selectableColumns: MinimalColumnData<TsData>[],
  forcedColumnId?: string,
) {
  return (
    selectableColumns.find(({ id }) => id === forcedColumnId) ??
    selectableColumns.find(({ id }) => id === "input") ??
    selectableColumns[0]
  );
}
