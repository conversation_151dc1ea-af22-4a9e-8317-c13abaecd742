import { type RowId } from "#/utils/diffs/diff-objects";
import { useContext, useEffect } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import { type OpenSidePanelFn } from "./use-open-side-panel";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

type Props = {
  isHumanReviewModeEnabled?: boolean;
  openSidePanel: OpenSidePanelFn;
  rowIds: RowId[];
};

/**
 * Handle "r" hotkey when not in sidebar or human review mode.
 * This is mounted as a component returning null to limit the hotkey-context re-renders
 */
export default function TableHotkeyManager({
  isHumanReviewModeEnabled,
  openSidePanel,
  rowIds,
}: Props) {
  const [{ r: activeRowId }] = useActiveRowAndSpan();
  const firstRowId = rowIds[0];
  const { disableScope, enableScope, enabledScopes } = useHotkeysContext();

  const { projectId } = useContext(ProjectContext);
  const [isTraceTreeCollapsed] = useEntityStorage({
    entityType: "humanReview",
    entityIdentifier: projectId ?? "",
    key: "isTraceTreeCollapsed",
  });

  useEffect(() => {
    enableScope("table");
    return () => {
      disableScope("table");
    };
  }, [enableScope, disableScope]);

  useHotkeys(
    "r",
    () => {
      if (!firstRowId) return;
      openSidePanel(activeRowId ?? firstRowId, {
        humanReviewMode: true,
        preserveSpan: !isTraceTreeCollapsed,
      });
    },
    {
      enabled:
        isHumanReviewModeEnabled &&
        !enabledScopes.includes("human-review") &&
        !!firstRowId,
      description: "Open human review mode",
      preventDefault: true,
    },
  );

  return null;
}
