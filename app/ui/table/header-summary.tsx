import { cn } from "#/utils/classnames";
import React, { Fragment, useContext } from "react";
import {
  ArrowDownRight,
  ArrowUpRight,
  CircleAlert,
  ExternalLink,
  ListFilter,
} from "lucide-react";
import {
  ComputedTokenMetricFields,
  ComputedCostMetricFields,
  type ScoreSummary,
  ComputedDurationMetricFields,
} from "@braintrust/local/query";
import { NULL_DASH } from "./formatters/null-formatter";
import {
  type AggregationFieldType,
  type AggregationType,
} from "#/utils/queries/aggregations";
import { isEmpty } from "#/utils/object";
import { formatPriceValue } from "#/ui/type-formatters/metrics";
import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { pluralizeWithCount } from "#/utils/plurals";
import {
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  ExperimentColorSwatch,
} from "#/ui/color";
import Link from "next/link";
import { getExperimentLink } from "#/app/app/[org]/p/[project]/experiments/[experiment]/getExperimentLink";
import { regressionFilterSerialize } from "#/ui/query-parameters";
import { useOrg } from "#/utils/user";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type ScoreSummaryExperiment } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import {
  type ComparisonType,
  type RegressionFilter,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { type ColumnMeta } from "@tanstack/react-table";
import { DataType } from "apache-arrow";
import { DiffLeftField, DiffRightField } from "#/utils/diffs/diff-objects";
import {
  makeDefaultCustomMetric,
  type MetricDefinition,
} from "@braintrust/local/api-schema";

export const CACHEABLE_SUMMARY_METRICS = [
  ...ComputedDurationMetricFields,
  ...ComputedTokenMetricFields,
  ...ComputedCostMetricFields,
];

type AggregationsProps =
  | {
      type: "experiments";
      aggregationType: AggregationType;
      setAggregationType: (v: AggregationType, experimentId?: string) => void;
    }
  | {
      type: "selectedExperiment";
      aggregationType: AggregationType;
      setAggregationType: (v: AggregationType, experimentId?: string) => void;
    }
  | {
      type: "groupTotals";
      totalCount: number;
      additionalMenuItems: React.ReactNode;
    };

export const HeaderSummary = function <TsData, TsValue>({
  title,
  fieldName,
  summary,
  errorsSummary,
  summaryData,
  cachedCount,
  aggregations,
  generateRegressionFilter,
  addRegressionFilter,
  columnType,
  showAggregationType,
  isGrouping,
  isBig,
  columnMeta,
  metricDefinition: metricDefinitionProp,
}: {
  title: React.ReactNode;
  fieldName: string;
  summary: ScoreSummary[keyof ScoreSummary] & {
    experiment: ScoreSummaryExperiment;
  };
  errorsSummary?: ScoreSummary[keyof ScoreSummary];
  summaryData: { scores?: ScoreSummary; experiment: ScoreSummaryExperiment }[];
  cachedCount: number | null | undefined;
  aggregations: AggregationsProps;
  generateRegressionFilter: (
    comparisonType: ComparisonType,
    id?: string,
  ) => RegressionFilter;
  addRegressionFilter?: (f: RegressionFilter) => void;
  columnType: AggregationFieldType;
  showAggregationType?: boolean;
  isGrouping?: boolean;
  isBig?: boolean;
  columnMeta?: ColumnMeta<TsData, TsValue>;
  metricDefinition: MetricDefinition | undefined;
}) {
  // If we got passed a "diff" style field definition, only use the field type from the diff child.
  if (
    columnMeta?.type.children &&
    columnMeta.type.children.length > 0 &&
    (columnMeta.type.children[0].name === DiffLeftField ||
      columnMeta.type.children[0].name === DiffRightField)
  ) {
    columnMeta = columnMeta.type.children[0];
  }

  const metricDefinition =
    metricDefinitionProp ??
    (columnType === "metrics" ? makeDefaultCustomMetric(fieldName) : undefined);

  const org = useOrg();
  const { projectName } = useContext(ProjectContext);

  const count =
    summary.experiment.type === "comparison"
      ? (summary.compare_keys?.length ?? 0)
      : (summary.all_keys ?? []).length;

  const errorsCount =
    summary.experiment.type === "comparison"
      ? (errorsSummary?.compare_all_keys?.length ?? 0)
      : (errorsSummary?.all_keys ?? []).length;

  const compareCount = summary.compare_keys?.length ?? 0;

  const totalsView = aggregations.type === "groupTotals";
  const {
    scoreValue,
    compareValue,
    diffValue: diffValueRaw,
    compareAllValue,
  } = getScoreValues(
    summary,
    totalsView ? "avg" : aggregations.aggregationType,
  );
  const scoreV =
    summary.experiment.type === "comparison" ? compareValue : scoreValue;
  const scoreValueStr = isEmpty(summary)
    ? ""
    : formatValue({
        v: scoreV,
        scoreName: fieldName,
        metricDefinition,
        columnType,
        suffix: "%",
        decimalPoints: 2,
        columnMeta,
      });

  const compareAllScoreValueStr = isEmpty(summary)
    ? ""
    : formatValue({
        v: compareAllValue,
        scoreName: fieldName,
        metricDefinition,
        columnType,
        suffix: "%",
        decimalPoints: 2,
        columnMeta,
      });

  const isLowerBetter = metricDefinition?.optimize_dir === "minimize";
  const diffValue = isEmpty(diffValueRaw)
    ? null
    : isLowerBetter
      ? -diffValueRaw
      : diffValueRaw;
  const ppDiffStr = isEmpty(summary)
    ? ""
    : formatValue({
        v: diffValue,
        scoreName: fieldName,
        metricDefinition,
        columnType,
        suffix: "%",
        columnMeta,
      });

  const alwaysPPDiffStr =
    isEmpty(summary) || diffValue == null || scoreValue == null
      ? ""
      : formatValue({
          v: diffValue / scoreValue,
          scoreName: "", // force the percentage format
          metricDefinition,
          columnType,
          suffix: "%",
          columnMeta,
        });

  const hasCachedData =
    columnType === "metrics" &&
    CACHEABLE_SUMMARY_METRICS.some((f) => f === fieldName) &&
    cachedCount != null;

  const prefix = diffValue && diffValue > 0 ? "+" : "";
  const diffStringWithPrefix =
    ppDiffStr &&
    diffValue &&
    `${prefix}${ppDiffStr}${alwaysPPDiffStr && columnType === "metrics" && isBig ? ` (${prefix}${alwaysPPDiffStr})` : ""}`;

  const improvementsCount = summary?.improvements?.length ?? 0;
  const regressionsCount = summary?.regressions?.length ?? 0;

  const isComparison = summary.experiment.type === "comparison";
  const comparisonClassName =
    EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[summary.experiment.index];

  const baseExperiment = summaryData.find(
    ({ experiment }) => experiment.type === "base",
  );

  const supportedAggTypes = [
    ...(["avg", "median", "min", "max"] as const),
    ...(columnType === "metrics" ? (["sum"] as const) : []),
  ];

  const filters = [
    improvementsCount > 0 ? (
      <DropdownMenuItem
        className="gap-1.5 font-medium"
        onClick={(e) => {
          e.stopPropagation();
        }}
        onSelect={() => {
          addRegressionFilter?.(
            generateRegressionFilter(
              "improvements",
              isComparison ? summary.experiment.id : undefined,
            ),
          );
        }}
      >
        <div
          className={cn(
            "-ml-0.5 flex items-center gap-1 rounded-xs p-0.5 text-background",
            metricDefinition && !metricDefinition.optimize_dir
              ? "bg-primary-600"
              : "bg-good-600",
          )}
        >
          <ArrowUpRight className="size-3" />
        </div>
        {pluralizeWithCount(improvementsCount, "improvement")}
      </DropdownMenuItem>
    ) : null,
    regressionsCount > 0 ? (
      <DropdownMenuItem
        className="gap-1.5 font-medium"
        onClick={(e) => {
          e.stopPropagation();
        }}
        onSelect={(e) => {
          addRegressionFilter?.(
            generateRegressionFilter(
              "regressions",
              isComparison ? summary.experiment.id : undefined,
            ),
          );
        }}
      >
        <div
          className={cn(
            "-ml-0.5 flex items-center gap-1 rounded-xs p-0.5 text-background",
            metricDefinition && !metricDefinition.optimize_dir
              ? "bg-primary-600"
              : "bg-bad-600",
          )}
        >
          <ArrowDownRight className="size-3" />
        </div>
        {pluralizeWithCount(regressionsCount, "regression")}
      </DropdownMenuItem>
    ) : null,
    count > 0 ? (
      <DropdownMenuItem
        onClick={(e) => {
          e.stopPropagation();
        }}
        onSelect={(e) => {
          addRegressionFilter?.(
            generateRegressionFilter("exists", summary.experiment.id),
          );
        }}
      >
        <ListFilter className="size-3" />
        {count.toLocaleString()} {isComparison ? "matching" : "in"} this{" "}
        {totalsView || isGrouping ? "group" : "experiment"}
      </DropdownMenuItem>
    ) : null,
    summary.experiment.type === "base" &&
    summaryData.length > 2 &&
    !totalsView ? (
      <DropdownMenuItem
        onClick={(e) => {
          e.stopPropagation();
        }}
        onSelect={(e) => {
          addRegressionFilter?.(generateRegressionFilter("exists"));
        }}
      >
        <ListFilter className="size-3" />
        {compareCount.toLocaleString()} total matching any experiment
      </DropdownMenuItem>
    ) : null,
  ];

  const hideFilters = filters.every((f) => f == null);

  return (
    <div
      className={cn("flex", {
        "-mx-2": !totalsView,
      })}
    >
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "flex h-auto min-h-[26px] flex-auto items-center justify-end gap-2 truncate px-2 py-0.5 align-middle text-primary-900",
              {
                "border border-transparent hover:border-primary-200":
                  isGrouping || totalsView,
              },
            )}
            onClick={(e) => e.stopPropagation()}
          >
            {!totalsView && (
              <div className="flex-1 truncate text-left">
                <span
                  className={cn("tabular-nums", {
                    "text-base font-semibold":
                      !isGrouping &&
                      !isComparison &&
                      aggregations.type === "experiments",
                    [`${comparisonClassName} bg-transparent!`]: isComparison,
                    "font-semibold": isGrouping,
                    "text-xl": isBig,
                  })}
                >
                  {scoreValueStr}
                </span>
                {showAggregationType && !isEmpty(scoreV) && (
                  <span
                    className={cn(
                      "pl-1 text-[10px] font-normal text-primary-500 uppercase",
                      {
                        "text-xs": isBig,
                      },
                    )}
                  >
                    {aggregations.aggregationType}
                  </span>
                )}
                <span
                  className={cn("pl-1 text-xs font-normal text-primary-500", {
                    "text-sm": isBig,
                  })}
                >
                  {diffStringWithPrefix || ""}
                </span>
              </div>
            )}
            <div className="flex items-baseline gap-1">
              {totalsView && (
                <span className="flex items-baseline text-xs font-normal text-primary-500">
                  {aggregations.totalCount.toLocaleString()} in this group
                </span>
              )}
              {errorsCount > 0 && (
                <span
                  className={cn(
                    "flex items-baseline text-xs text-primary-500",
                    {
                      "text-sm": isBig,
                    },
                  )}
                >
                  <CircleAlert
                    className={cn("mr-0.5 size-2.5 translate-y-px", {
                      "size-3": isBig,
                    })}
                  />
                </span>
              )}
              {improvementsCount > 0 && (
                <span
                  className={cn("flex items-baseline text-xs", {
                    "text-sm": isBig,
                    "text-good-700":
                      !metricDefinition || metricDefinition.optimize_dir,
                  })}
                >
                  <ArrowUpRight
                    className={cn("size-3 translate-y-px", {
                      "size-4": isBig,
                    })}
                  />
                  {improvementsCount}
                </span>
              )}
              {regressionsCount > 0 && (
                <span
                  className={cn("flex items-baseline text-xs", {
                    "text-sm": isBig,
                    "text-bad-700":
                      !metricDefinition || metricDefinition.optimize_dir,
                  })}
                >
                  <ArrowDownRight
                    className={cn("size-3 translate-y-px", {
                      "size-4": isBig,
                    })}
                  />
                  {regressionsCount}
                </span>
              )}
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" onClick={(e) => e.stopPropagation()}>
          {!totalsView && (
            <>
              <DropdownMenuGroup>
                <DropdownMenuLabel
                  className={cn("rounded-md px-2 py-1 text-primary-500", {
                    [`${comparisonClassName} bg-transparent`]: isComparison,
                  })}
                >
                  {isComparison ? "Comparison experiment" : "Base experiment"}
                </DropdownMenuLabel>
                {isComparison && scoreValueStr !== compareAllScoreValueStr && (
                  <DropdownMenuLabel className="max-w-60 leading-normal text-primary-700">
                    For rows that match{" "}
                    <span className="font-medium">
                      <span
                        className={cn(
                          "mx-0.5 inline-block size-2 rounded-full bg-primary-500",
                        )}
                      />{" "}
                      {baseExperiment?.experiment.name}
                    </span>
                    , the{" "}
                    <span className="font-medium">
                      {aggregations.aggregationType}
                    </span>{" "}
                    value for <span className="font-medium">{title}</span> is{" "}
                    <span className="font-medium">{scoreValueStr}</span>
                  </DropdownMenuLabel>
                )}
                <DropdownMenuLabel className="max-w-72 leading-normal text-primary-700">
                  For{" "}
                  <span className="font-medium">
                    <span
                      className={cn(
                        "mx-0.5 inline-block size-2 rounded-full bg-primary-500",
                        {
                          [comparisonClassName]: isComparison,
                        },
                      )}
                    />{" "}
                    {summary.experiment.name}
                  </span>
                  , the{" "}
                  <span className="font-medium">
                    {aggregations.aggregationType}
                  </span>{" "}
                  {isComparison ? (
                    <>
                      value for all <span className="font-medium">{title}</span>{" "}
                      rows is{" "}
                      <span className="font-medium">
                        {compareAllScoreValueStr === NULL_DASH
                          ? "null"
                          : compareAllScoreValueStr}
                      </span>
                    </>
                  ) : (
                    <>
                      value for <span className="font-medium">{title}</span> is{" "}
                      <span className="font-medium">
                        {scoreValueStr === NULL_DASH ? "null" : scoreValueStr}
                      </span>
                    </>
                  )}
                  {columnType === "scores" && scoreValueStr === NULL_DASH ? (
                    <div className="pt-2">
                      Scores are skipped if they return null as a score value
                    </div>
                  ) : null}
                </DropdownMenuLabel>
                {isComparison && (
                  <DropdownMenuLabel className="max-w-72 text-xs text-primary-700">
                    The delta with the base experiment is{" "}
                    <span className="font-medium">
                      {diffStringWithPrefix || NULL_DASH}
                    </span>
                  </DropdownMenuLabel>
                )}
                {hasCachedData && cachedCount ? (
                  <DropdownMenuLabel className="max-w-72 text-xs">
                    {cachedCount} of {count} rows contain LLM calls that were
                    cached by the Braintrust proxy.
                  </DropdownMenuLabel>
                ) : null}
                {errorsCount > 0 && (
                  <DropdownMenuLabel className="max-w-72 text-xs text-primary-500">
                    <CircleAlert className="inline-block size-2.5 -translate-y-px" />{" "}
                    This aggregate score may not account for{" "}
                    {pluralizeWithCount(errorsCount, "row")} with errors
                  </DropdownMenuLabel>
                )}
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
            </>
          )}
          {!hideFilters && (
            <>
              <DropdownMenuGroup>
                <DropdownMenuLabel>Filter by unique</DropdownMenuLabel>
                {filters.map((f, i) => (
                  <Fragment key={i}>{f}</Fragment>
                ))}
              </DropdownMenuGroup>
            </>
          )}
          {totalsView && aggregations.additionalMenuItems
            ? aggregations.additionalMenuItems
            : null}
          {!totalsView && (
            <>
              {!hideFilters && <DropdownMenuSeparator />}
              <DropdownMenuGroup>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>Aggregate</DropdownMenuSubTrigger>
                  <DropdownMenuSubContent>
                    {aggregations.type === "selectedExperiment"
                      ? summaryData.map(({ experiment }, idx) => (
                          <DropdownMenuGroup key={idx} className="pb-1">
                            <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-600">
                              <ExperimentColorSwatch index={idx}>
                                {experiment.name}
                              </ExperimentColorSwatch>
                            </DropdownMenuLabel>
                            {supportedAggTypes.map((t, i) => (
                              <DropdownMenuCheckboxItem
                                key={`${t}-${i}`}
                                onClick={(e) => e.stopPropagation()}
                                onSelect={() => {
                                  aggregations.setAggregationType(
                                    t,
                                    experiment.id,
                                  );
                                }}
                                checked={
                                  aggregations.aggregationType === t &&
                                  experiment.id === summary.experiment.id
                                }
                              >
                                {formatAggType(t)}
                              </DropdownMenuCheckboxItem>
                            ))}
                          </DropdownMenuGroup>
                        ))
                      : supportedAggTypes.map((t, i) => (
                          <DropdownMenuCheckboxItem
                            key={`${t}-${i}`}
                            onClick={(e) => e.stopPropagation()}
                            onSelect={() => {
                              aggregations.setAggregationType(t);
                            }}
                            checked={aggregations.aggregationType === t}
                          >
                            {formatAggType(t)}
                          </DropdownMenuCheckboxItem>
                        ))}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              </DropdownMenuGroup>
            </>
          )}
          {isComparison && !totalsView && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <ExternalLink className="size-3" />
                  Open experiment {isGrouping ? "in this group" : ""}
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Link
                        className="flex grow items-center gap-2"
                        href={
                          getExperimentLink({
                            orgName: org.name,
                            projectName,
                            experimentName: summary.experiment.name,
                          }) +
                          regressionFilterSerialize(
                            new URLSearchParams({
                              c: baseExperiment?.experiment.name ?? "",
                              diff: "between_experiments",
                            }),
                            {
                              rf: [
                                generateRegressionFilter(
                                  "exists",
                                  baseExperiment?.experiment.id,
                                ),
                              ],
                            },
                          )
                        }
                        target="_blank"
                      >
                        View {summary.compare_keys?.length ?? 0} matching rows
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Link
                        className="flex grow items-center gap-2"
                        href={
                          getExperimentLink({
                            orgName: org.name,
                            projectName,
                            experimentName: summary.experiment.name,
                          }) +
                          regressionFilterSerialize(
                            new URLSearchParams({
                              c: baseExperiment?.experiment.name ?? "",
                              diff: "between_experiments",
                            }),
                            {
                              rf: [
                                generateRegressionFilter(
                                  "exists",
                                  summary.experiment.id,
                                ),
                              ],
                            },
                          )
                        }
                        target="_blank"
                      >
                        View all {summary.compare_all_keys?.length ?? 0} rows
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export function getScoreValues(
  scoreData: ScoreSummary[keyof ScoreSummary],
  aggregationType: AggregationType,
) {
  switch (aggregationType) {
    case "min":
      return {
        scoreValue: scoreData.min,
        compareValue: scoreData.compareMin,
        diffValue: differenceOrNull(scoreData.min, scoreData.compareMin),
        compareAllValue: scoreData.compare_all_min,
      };

    case "max":
      return {
        scoreValue: scoreData.max,
        compareValue: scoreData.compareMax,
        diffValue: differenceOrNull(scoreData.max, scoreData.compareMax),
        compareAllValue: scoreData.compare_all_max,
      };
    case "sum":
      return {
        scoreValue: scoreData.sum,
        compareValue: scoreData.compareSum,
        diffValue: differenceOrNull(scoreData.sum, scoreData.compareSum),
        compareAllValue: scoreData.compare_all_sum,
      };
    case "median":
      return {
        scoreValue: scoreData.median,
        compareValue: scoreData.compareMedian,
        diffValue: differenceOrNull(scoreData.median, scoreData.compareMedian),
        compareAllValue: scoreData.compare_all_median,
      };
    case "avg":
    default:
      return {
        scoreValue: scoreData.avg,
        compareValue: scoreData.compareAvg,
        diffValue: differenceOrNull(scoreData.avg, scoreData.compareAvg),
        compareAllValue: scoreData.compare_all_avg,
      };
  }
}

function differenceOrNull(
  a: number | null | undefined,
  b: number | null | undefined,
) {
  if (a == null || b == null) {
    return null;
  }
  return a - b;
}

type MetricType = "cost" | "duration" | "tokens" | "other";
function metricType(
  metricName: string,
  metricDefinition: MetricDefinition | undefined,
): MetricType {
  if (metricName.includes("cost") || metricDefinition?.unit === "dollars") {
    return "cost";
  } else if (
    metricName.includes("duration") ||
    metricDefinition?.unit === "seconds"
  ) {
    return "duration";
  } else {
    return "other";
  }
}

function formatValue<TsData, TsValue>({
  v,
  scoreName,
  columnType,
  metricDefinition,
  suffix,
  decimalPoints,
  columnMeta,
}: {
  v: number | null | undefined;
  scoreName: string;
  columnType: AggregationFieldType;
  metricDefinition?: MetricDefinition;
  suffix: string;
  decimalPoints?: number;
  columnMeta?: ColumnMeta<TsData, TsValue>;
}) {
  let value = v;

  if (isEmpty(value)) {
    return NULL_DASH;
  }

  if (DataType.isDecimal(columnMeta?.type)) {
    try {
      if (columnMeta.type.scale) {
        // Decimal values are stored as integers and scale is the number of digits after the decimal point
        value = value / Math.pow(10, columnMeta.type.scale);
      }
    } catch {}
  }

  switch (metricType(scoreName, metricDefinition)) {
    case "cost":
      return formatPriceValue(value);
    case "duration":
      return `${value >= 1 ? value.toLocaleString(undefined, { maximumFractionDigits: 2 }) : value.toLocaleString(undefined, { maximumFractionDigits: 3 })}s`;
    default:
      if (columnType === "metrics") {
        return Number.isInteger(value)
          ? `${value.toLocaleString()}`
          : `${value.toLocaleString(undefined, { maximumFractionDigits: 2 })}`;
      } else {
        return `${(value * 100).toLocaleString(undefined, {
          maximumFractionDigits: decimalPoints ?? 0,
        })}${suffix}`;
      }
  }
}

function formatAggType(aggType: AggregationType) {
  switch (aggType) {
    case "avg":
      return "Average";
    case "min":
      return "Minimum";
    case "max":
      return "Maximum";
    case "sum":
      return "Sum";
    case "median":
      return "Median";
    default:
      const _: never = aggType;
      throw new Error(`Unsupported aggregation type ${_}`);
  }
}
