import { But<PERSON> } from "#/ui/button";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import {
  type ClauseType,
  type ClauseChecker,
  type Search,
  type ClauseSpec,
} from "#/utils/search/search";
import { inputClassName } from "#/ui/input";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";
import { useBtqlAutocompleteProjectDataSource } from "#/app/app/[org]/btql/use-btql-autocomplete-data-sources";

export function BTQLFilterForm({
  clauseChecker,
  setSearch,
  clauseToReplace,
  setOpen,
  value,
  setValue,
  experimentId,
  disableFilters,
  examples,
}: {
  clauseChecker: ClauseChecker | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  clauseToReplace?: ClauseSpec<ClauseType>;
  setOpen: (open: boolean) => void;
  value: string;
  setValue: (value: string) => void;
  experimentId?: string;
  disableFilters?: boolean;
  examples?: string[];
}) {
  const { applySearch, isValidBTQL } = useFilterSortBarSearch({
    clauseChecker,
    setSearch,
  });

  const [isBTQLValid, setBTQLValid] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (!value) {
      setBTQLValid(false);
      setErrorMessage(null);
      return;
    }
    const check = async () => {
      const result = await isValidBTQL(value, "filter");
      setBTQLValid(result.valid);
      setErrorMessage(result.error || null);
    };
    check();
  }, [value, isValidBTQL]);

  const disabled = useMemo(
    () => !value || disableFilters || !isBTQLValid,
    [value, isBTQLValid, disableFilters],
  );

  const onSubmitBTQL = useCallback(async () => {
    if (disabled) return;
    await applySearch(value, clauseToReplace, {
      originType: "btql",
      label: value,
      ...(experimentId
        ? {
            comparison: {
              experimentId,
            },
          }
        : {}),
    });
    setOpen(false);
  }, [applySearch, value, setOpen, clauseToReplace, experimentId, disabled]);

  const dataSources = useBtqlAutocompleteProjectDataSource();

  return (
    <>
      <div className="p-4">
        <BtqlEditor
          dataSources={dataSources}
          mode="expr"
          onValueChange={setValue}
          value={value}
          autoFocus
          placeholder="Enter BTQL filter clause"
          onMetaEnter={onSubmitBTQL}
          disableLinter={true}
          className={cn(inputClassName, "h-auto transition-colors", {
            "border-bad-500 focus:border-bad-500": value && !isBTQLValid,
          })}
        />
        {errorMessage && (
          <div className="mt-1 text-xs text-bad-600 duration-200 animate-in fade-in slide-in-from-top-1">
            {errorMessage}
          </div>
        )}
      </div>
      {examples && examples.length > 0 && (
        <div className="border-t border-primary-100 px-4 py-2">
          <div className="mb-2 text-xs text-primary-500">
            Example filter clauses
          </div>
          <div className="flex flex-wrap gap-2">
            {examples.map((example) => (
              <Button
                key={example}
                size="xs"
                className="max-w-full flex-none font-mono text-[11px] font-semibold text-primary-500"
                onClick={() => setValue(example)}
              >
                {example}
              </Button>
            ))}
          </div>
        </div>
      )}
      <div className="flex flex-none items-center gap-3 border-t border-primary-100 px-4 py-2">
        <Button
          size="xs"
          variant="primary"
          disabled={disabled}
          onClick={onSubmitBTQL}
        >
          {clauseToReplace ? "Update" : "Apply"}
        </Button>
        <div className="w-full text-right text-xs text-balance text-primary-500">
          BTQL filter clause{" "}
          <Link
            className="font-medium text-accent-600"
            href="/docs/reference/btql#filter-clause"
            target="_blank"
          >
            documentation
          </Link>
        </div>
      </div>
    </>
  );
}
