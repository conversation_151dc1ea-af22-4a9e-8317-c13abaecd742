import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { doubleQuote, jsonPath } from "#/utils/sql-utils";
import { getGroupValue } from "#/ui/table/formatters/grouping/grouping-values";
import { GROUP_BY_INPUT_VALUE } from "./controls";

export const BT_GROUP_KEY = "__bt_group_key" as const;
export const BT_IS_GROUP = "__bt_is_group" as const;
export const BT_SUBROWS = "__bt_subrows" as const;
export const BT_GROUP_BY_METADATA = "__bt_group_by_metadata" as const;

export const btGroupColumnNames: Set<string> = new Set([
  BT_GROUP_KEY,
  BT_IS_GROUP,
  BT_SUBROWS,
  BT_GROUP_BY_METADATA,
]);

export const rowGroupingQuery = ({
  tableGrouping,
  baseQuery,
  groupColumnName,
  columnsFn,
  sortExprsFn,
}: {
  tableGrouping: string | null;
  baseQuery: string | string[] | null;
  groupColumnName: string;
  columnsFn: (columnName: string) => string | null;
  sortExprsFn: (defaultSort: string) => string;
}) => {
  const groupExpr = groupExprFn(tableGrouping, "d", "");
  if (!groupExpr) {
    return null;
  }
  const columnsExpr = columnsFn(groupColumnName);
  const query =
    baseQuery && columnsExpr
      ? `
      WITH base AS (
        SELECT
          ${groupExpr}
          *,
          d as rows,
        FROM (${baseQuery}) d
      ),
      grouped AS (
        SELECT
          ${BT_GROUP_KEY},
          ${columnsExpr},
          array_agg(base.rows) AS __bt_subrows
        FROM base
        GROUP BY ${BT_GROUP_KEY}
      )
      SELECT TRUE AS ${BT_IS_GROUP}, * FROM grouped
      ORDER BY ${sortExprsFn(`${groupColumnName} ASC NULLS LAST`)}
  `
      : null;

  return query;
};

// Create groups in application code because using array_agg can run into an out of memory error
// when there are too many subrows, so just group things in js for now.
// Eventually we will implement paging across groups and lazy-loading subrows
// https://duckdb.org/docs/guides/performance/how_to_tune_workloads.html#limitations
export function groupRowsForTable<TsData>(rows: TsData[]) {
  const rowsForGroup: Record<string, TsData[]> = {};
  for (const row of rows) {
    const groupKey = getGroupValue(row, BT_GROUP_KEY);
    rowsForGroup[groupKey] ??= [];
    rowsForGroup[groupKey].push(row);
  }
  return rowsForGroup;
}

export function nullValueExpr(
  columnName: string | string[],
  structField?: boolean,
) {
  const columnNameStr = Array.isArray(columnName)
    ? columnName.map(doubleQuote).join(".")
    : doubleQuote(columnName);
  const fieldName = doubleQuote(
    Array.isArray(columnName) ? columnName[columnName.length - 1] : columnName,
  );
  // use this instead of `NULL AS ${f}` so we can preserve column type information
  return `${structField ? `${fieldName} := ` : ""}(SELECT ${columnNameStr} FROM base LIMIT 0) ${structField ? "" : `AS ${fieldName}`}`;
}

export function groupExprFn(
  grouping: string | null,
  relation: string,
  diffObjKey: string,
) {
  if (!grouping || grouping === GROUP_BY_NONE_VALUE) {
    return "";
  }
  // for accessing raw values in diff mode
  const aliasSuffix = diffObjKey ? `.${diffObjKey}` : "";
  if (grouping === GROUP_BY_INPUT_VALUE) {
    return `${relation}.comparison_key${aliasSuffix} as ${BT_GROUP_KEY},`;
  }

  try {
    const groupPath = JSON.parse(grouping);
    const path = jsonPath(groupPath);
    return `JSON_EXTRACT_STRING(${relation}."metadata"${aliasSuffix}, ${path}) as ${BT_GROUP_KEY},`;
  } catch {
    return `${relation}.${doubleQuote(grouping)}${aliasSuffix}::VARCHAR as ${BT_GROUP_KEY},`;
  }
}
