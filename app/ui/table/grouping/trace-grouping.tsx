import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { Combobox } from "#/ui/combobox/combobox";
import { StretchHorizontal } from "lucide-react";
import { useEffect, useMemo } from "react";

/**
 * Control for determining which associated traces to load in the trace panel
 * We can replace this with GroupBySelect once we support group by on logging page.
 */
export const TraceGroupBySelector = ({
  metadataOptions,
  tagOptions,
  grouping,
  onSetGrouping,
  disabled,
}: {
  metadataOptions: string[][];
  tagOptions?: string[];
  grouping: string;
  onSetGrouping: (value: string) => void;
  //customColumnOptions?: { label: string; value: string }[];
  disabled?: boolean;
}) => {
  const comboboxOptions = useMemo(() => {
    // customColumnOptions
    return {
      tags: (tagOptions ?? []).map((option) => ({
        label: option,
        value: JSON.stringify(["tags", option]),
      })),
      metadata: metadataOptions.flatMap((option) =>
        // only allow grouping on 1 depth metadata fields for now
        // since it's faster
        option.length > 1
          ? []
          : [
              {
                label: option.join("."),
                value: JSON.stringify(["metadata", ...option]),
              },
            ],
      ),
    };
  }, [metadataOptions, tagOptions]);

  useEffect(() => {
    try {
      const parsed = JSON.parse(grouping);
      const path = parsed.slice(1);
      if (path.length > 1) {
        onSetGrouping(GROUP_BY_NONE_VALUE);
      }
    } catch {}
  }, [grouping, onSetGrouping]);

  const hasOptions =
    comboboxOptions.metadata.length > 0 || comboboxOptions.tags.length > 0;
  return (
    <Combobox
      disabled={disabled}
      tooltipContent="Group traces by"
      options={
        hasOptions
          ? [
              {
                label: "Group by",
                options: [{ label: "None", value: GROUP_BY_NONE_VALUE }],
              },
              ...(comboboxOptions.metadata.length > 0
                ? [
                    {
                      label: "Metadata",
                      options: comboboxOptions.metadata,
                    },
                  ]
                : []),
              ...(comboboxOptions.tags.length > 0
                ? [
                    {
                      label: "Tags",
                      options: comboboxOptions.tags,
                    },
                  ]
                : []),
            ]
          : [
              {
                label:
                  "To group rows, add row metadata, tags, or a custom column",
                disabled: true,
                value: "",
              },
            ]
      }
      align="start"
      onChange={(v) => {
        onSetGrouping(v ?? GROUP_BY_NONE_VALUE);
      }}
      renderComboboxDisplayLabel={() => {
        return <ButtonLabel />;
      }}
      // in the case where there is a metadata selection but no matching option
      placeholderLabel={<ButtonLabel />}
      iconClassName="hidden"
      variant="button"
      buttonSize="xs"
      buttonVariant={grouping === GROUP_BY_NONE_VALUE ? "ghost" : "default"}
      selectedValue={grouping || GROUP_BY_NONE_VALUE}
      searchPlaceholder="Find grouping"
      noResultsLabel="No metadata"
    />
  );
};

const ButtonLabel = () => {
  return (
    <span className="flex items-center gap-1">
      <StretchHorizontal className="size-3" />
      <span className="hidden @xl/controls:block">Group</span>
    </span>
  );
};
