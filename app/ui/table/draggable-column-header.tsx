import { cn } from "#/utils/classnames";
import { type Column, type Header, flexRender } from "@tanstack/react-table";
import React, { useRef } from "react";
import { ColumnMenu } from "./column-menu";
import { ArrowDown, ArrowUp } from "lucide-react";
import { BasicTooltip } from "#/ui/tooltip";
import {
  SUMMARY_CACHED_METRIC_NAME,
  type ScoreSummary,
} from "@braintrust/local/query";
import { isEmpty } from "#/utils/object";
import { HeaderSummary } from "./header-summary";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  getColumnType,
  type ScoreSummaryExperiment,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type RegressionFilter } from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { useOrg } from "#/utils/user";
import { type SortComparison } from "#/utils/search/search";
import { type MetricDefinition } from "@braintrust/local/api-schema";

type DraggableColumnHeaderProps<TsData, TsValue> = {
  header: Header<TsData, unknown>;
  onFilter?: () => void;
  isPinned?: boolean;
  headerRefFn: (ref: HTMLDivElement | null) => void;
  onEdit: (columnId?: string) => void;
  onDelete?: (col: { id: string; name: string }) => void;
  summaryData?:
    | { scores?: ScoreSummary; experiment: ScoreSummaryExperiment }[]
    | null;
  addRegressionFilter?: (filter: RegressionFilter) => void;
  setColumnSort: (
    col: Column<TsData, TsValue>,
    desc: boolean,
    comparison?: SortComparison,
  ) => void;
  groupBy?: string;
  layout: string;
  isSortable: boolean;
  isReadOnly?: boolean;
  summarySlot?: React.ReactNode;
  metricDefinitions: MetricDefinition[] | undefined;
};

const SUMMARY_DISABLED_METRICS = ["start", "end"];

export const DraggableColumnHeader = function DraggableColumnHeader<TsData>({
  header,
  onFilter,
  isPinned,
  headerRefFn,
  onEdit,
  onDelete,
  summaryData,
  addRegressionFilter,
  setColumnSort,
  groupBy,
  layout,
  isSortable,
  isReadOnly,
  summarySlot,
  metricDefinitions,
}: DraggableColumnHeaderProps<TsData, unknown>) {
  const meta = header.column.columnDef.meta;
  const internalColumnType = meta?.internalType;
  const isCheckbox =
    internalColumnType === "checkbox" || internalColumnType === "star";
  const element = useRef<HTMLDivElement | null>(null);

  const gridLayout = layout === "grid";

  const isMetric = meta?.path?.[0] === "metrics";
  const isScore = meta?.path?.[0] === "scores";
  const summaryEnabled =
    !isEmpty(summaryData) &&
    (isMetric || isScore) &&
    !SUMMARY_DISABLED_METRICS.includes(meta?.name);

  const org = useOrg();
  const [aggregationTypes, setAggregationType] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: org.id ?? "",
    key: "aggregationTypes",
  });
  const aggregationTypeKey = isMetric ? "metrics" : "scores";
  const aggregationType = aggregationTypes[aggregationTypeKey];

  const isDraggable = !isPinned && !internalColumnType;

  const {
    attributes,
    listeners,
    setNodeRef,
    isDragging: isSortableDragging,
    isOver,
    transform,
    transition,
  } = useSortable({
    id: header.id,
  });

  const dragStyle = {
    transform: CSS.Transform.toString({
      y: 0,
      x: transform?.x ?? 0,
      scaleX: 1,
      scaleY: 1,
    }),
    transition,
  };

  const width = header.getSize();

  const resizer = (
    <div
      className={cn(
        "pointer-events-auto absolute top-2 right-0 bottom-2 z-20 w-1 translate-x-[2.5px] cursor-col-resize rounded-full hover:bg-accent-500",
        header.column.getIsResizing() && "bg-accent-500",
      )}
      onMouseDown={(e) => {
        e.preventDefault();
        e.stopPropagation();
        header.getResizeHandler()(e);
      }}
      onTouchStart={header.getResizeHandler()}
    />
  );

  const headerWrapper = (content: React.ReactNode) => (
    <div className="relative" style={{ width }}>
      <div
        data-column-name={header.id}
        ref={(e) => {
          setNodeRef(e);
          element.current = e;
          headerRefFn(e);
        }}
        key={header.id}
        className={cn(
          "group h-full min-w-min rounded-sm py-2 font-inter font-medium",
          {
            "bg-accent-50": isSortableDragging,
          },
        )}
        style={{
          ...(isDraggable ? dragStyle : {}),
          width,
        }}
        {...(isDraggable ? attributes : {})}
      >
        <div
          className={cn(
            "relative h-full border-l border-primary-200/80 py-1 pr-3",
            {
              "flex items-start justify-end border-l-0 px-2": isCheckbox,
            },
          )}
          style={{ width }}
        >
          {content}
        </div>
      </div>
      {!internalColumnType && resizer}
    </div>
  );

  if (header.isPlaceholder) {
    return <>{headerWrapper(null)}</>;
  }

  if (isCheckbox) {
    return headerWrapper(
      <>{flexRender(header.column.columnDef.header, header.getContext())}</>,
    );
  }

  const content = flexRender(
    header.column.columnDef.header,
    header.getContext(),
  );

  return (
    <>
      {headerWrapper(
        <div
          className={cn(
            "flex flex-col text-xs font-medium text-primary-600 select-none",
            {
              "cursor-grab": !isSortableDragging && isDraggable,
            },
          )}
        >
          {header.column.getCanSort() &&
            !header.isPlaceholder &&
            !isOver &&
            !isSortableDragging &&
            !gridLayout && (
              <ColumnMenu
                isReadOnly={isReadOnly}
                isNumeric={
                  isMetric ||
                  isScore ||
                  header.column?.columnDef?.meta?.isNumeric ||
                  header.column?.columnDef?.meta?.isTimestamp
                }
                summaryData={summaryData}
                onClickAsc={() => {
                  header.column.toggleSorting(false);
                  setColumnSort(header.column, false);
                }}
                onClickDesc={() => {
                  header.column.toggleSorting(true);
                  setColumnSort(header.column, true);
                }}
                onComparisonSort={(comparison, desc) =>
                  setColumnSort(header.column, desc, comparison)
                }
                groupBy={groupBy}
                onFilter={onFilter}
                onHide={
                  isPinned
                    ? undefined
                    : () => {
                        header.column.toggleVisibility();
                      }
                }
                onDelete={
                  header.column.columnDef.meta?.customColumnId && onDelete
                    ? () => {
                        onDelete({
                          id:
                            header.column.columnDef.meta?.customColumnId || "",
                          name: header.column.columnDef.id || "",
                        });
                      }
                    : undefined
                }
                onEdit={
                  header.column.columnDef.meta?.customColumnId
                    ? () => {
                        onEdit(header.column.columnDef.meta?.customColumnId);
                      }
                    : undefined
                }
                isSortable={isSortable}
              />
            )}
          <div
            className={cn(
              // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
              "draggable-column-header z-10 flex pl-3 text-xs font-medium text-primary-600 select-none",
            )}
          >
            <span className="w-full truncate">{content}</span>
            {
              {
                "": null,
                asc: <ArrowUp className="mx-1 mt-0.5 size-3 shrink-0" />,
                desc: <ArrowDown className="mx-1 mt-0.5 size-3 shrink-0" />,
              }[header.column.getIsSorted() || ""]
            }
            <BasicTooltip
              tooltipContent={!isSortableDragging && !isOver && content}
              align="start"
              className="text-xs font-medium text-primary-700"
            >
              <div
                className="absolute inset-0 z-10"
                {...(isDraggable ? { ...listeners } : {})}
              />
            </BasicTooltip>
          </div>
          {summaryEnabled && (
            <div className="pointer-events-auto z-20 flex flex-col gap-0.5 pt-1 pl-3">
              {summaryData.map((s, idx) => {
                const columnType = getColumnType(meta.path);
                const columnName = meta.name;
                const cachedKey =
                  s.experiment.type === "base" ? "sum" : "compareSum";
                return (
                  <HeaderSummary
                    key={`${s.experiment.id || idx}-${s.experiment.type}`}
                    showAggregationType={idx === 0}
                    title={content}
                    generateRegressionFilter={(comparisonType, id) => ({
                      comparisonType,
                      field: {
                        type: columnType,
                        value: columnName,
                      },
                      experimentId: id ?? "any",
                    })}
                    addRegressionFilter={addRegressionFilter}
                    fieldName={columnName}
                    metricDefinition={metricDefinitions?.find(
                      (m) => m.field_name === columnName,
                    )}
                    aggregations={{
                      type: "experiments",
                      aggregationType,
                      setAggregationType: (v) => {
                        setAggregationType({
                          ...aggregationTypes,
                          [aggregationTypeKey]: v,
                        });
                      },
                    }}
                    summary={{
                      ...s.scores?.[columnName],
                      experiment: s.experiment,
                    }}
                    errorsSummary={s.scores?.error}
                    summaryData={summaryData}
                    cachedCount={
                      s.scores?.[SUMMARY_CACHED_METRIC_NAME]?.[cachedKey]
                    }
                    columnType={aggregationTypeKey}
                    columnMeta={meta}
                  />
                );
              })}
            </div>
          )}
          {summarySlot}
        </div>,
      )}
    </>
  );
};
