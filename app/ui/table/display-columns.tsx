import {
  type ColumnDef,
  type Table as TanstackTable,
} from "@tanstack/react-table";
import { type RefObject } from "react";
import { TableCheckbox } from "./cells/table-checkbox";
import { TableHeaderCheckbox } from "./cells/table-header-checkbox";
import { StarCell } from "./star-cell";
import { type UpdateRowFn } from "#/utils/mutable-object";
import { COLUMN_ICON_CLASS } from "#/ui/field-to-column";
import { ListPlus } from "lucide-react";
import {
  RowComparisonFormatterFactory,
  type RowComparisonFormatterProps,
} from "./formatters/row-comparison-formatter";

export const RowSelectionColumnId = "__row_selection";
export const RowComparisonColumnId = "__row_comparison";
export const RowStarColumnId = "__row_star";

export function RowSelectionColumnDefFactory<TsData, TsValue>({
  showRowNumber,
  lastSelectedRowId,
}: {
  showRowNumber: boolean | undefined;
  lastSelectedRowId: RefObject<string | undefined>;
}): ColumnDef<TsData, TsValue> {
  const checkboxColumnSize = showRowNumber ? 40 : 36;
  const HeaderComponent = ({ table }: { table: TanstackTable<TsData> }) => (
    <TableHeaderCheckbox table={table} lastSelectedRowId={lastSelectedRowId} />
  );
  return {
    id: RowSelectionColumnId,
    enableHiding: false,
    header: HeaderComponent,
    cell: ({ table, row }) => (
      <TableCheckbox
        lastSelectedRowId={lastSelectedRowId}
        table={table}
        row={row}
        showRowNumber={showRowNumber}
      />
    ),
    meta: {
      header: HeaderComponent,
      name: RowSelectionColumnId,
      path: [],
      type: null,
      isNumeric: false,
      isTimestamp: false,
      internalType: "checkbox",
    },
    size: checkboxColumnSize,
    minSize: checkboxColumnSize,
    maxSize: checkboxColumnSize,
  };
}

export function RowStarColumnDefFactory<TsData, TsValue>({
  updateRow,
}: {
  updateRow: UpdateRowFn;
}): ColumnDef<TsData, TsValue> {
  const HeaderComponent = () => null;
  return {
    id: RowStarColumnId,
    enableHiding: false,
    header: HeaderComponent,
    cell: ({ row }) => <StarCell row={row} updateRow={updateRow} />,
    meta: {
      header: HeaderComponent,
      name: RowStarColumnId,
      path: [],
      type: null,
      isNumeric: false,
      isTimestamp: false,
      internalType: "star",
    },
    size: 36,
    minSize: 36,
    maxSize: 36,
  };
}

export function RowComparisonColumnDefFactory<TsData, TsValue>({
  rowComparisonProps,
}: {
  rowComparisonProps: RowComparisonFormatterProps;
}): ColumnDef<TsData, TsValue> {
  const HeaderComponent = () => (
    <>
      <ListPlus className={COLUMN_ICON_CLASS} />
      Trials
    </>
  );
  const CellComponent = RowComparisonFormatterFactory(rowComparisonProps);

  return {
    id: RowComparisonColumnId,
    enableHiding: false,
    header: HeaderComponent,
    // @ts-ignore
    cell: (props) => <CellComponent {...props} />,
    meta: {
      header: HeaderComponent,
      name: RowComparisonColumnId,
      path: [],
      type: null,
      isNumeric: false,
      isTimestamp: false,
      internalType: "row_comparison" as const,
    },
    size: 76,
    minSize: 76,
    maxSize: 76,
  };
}
