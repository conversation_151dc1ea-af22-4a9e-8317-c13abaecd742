import { type FilterEditorColumnType } from "#/ui/table/filter-editor";
import { type JSONSchemaObject } from "@braintrust/btql/schema";

export const jsonTypeToColumnType = (
  jsonType: JSONSchemaObject["type"],
): FilterEditorColumnType | undefined => {
  if (!jsonType) {
    return;
  }
  if (jsonType === "string") {
    return "text";
  }

  if (jsonType === "boolean") {
    return "boolean";
  }

  if (jsonType === "integer" || jsonType === "number") {
    return "number";
  }

  if (jsonType === "object") {
    return "full-text";
  }

  // todo - support for null and array types
  return "text";
};
