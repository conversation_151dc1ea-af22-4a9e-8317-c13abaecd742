import { useMemo } from "react";
import type { InferField } from "./filter-editor-infer-data";
import { Combobox } from "#/ui/combobox/combobox";
import { Input } from "#/ui/input";
import { type NestedPath, CUSTOM_FIELD_TYPE } from "./filter-editor-infer-data";
import { Skeleton } from "#/ui/skeleton";

export const CUSTOM_PATH_VALUE: NestedPath = {
  namePath: [],
  type: CUSTOM_FIELD_TYPE,
};
const ROOT_NAME = "Field root";

type FilterEditorNestedFieldsProps = {
  inferFields: InferField[];
  fieldName: string;
  nestedPath: NestedPath | undefined;
  setNestedPath: (path: NestedPath) => void;
};

const FilterEditorNestedFieldsComponent = (
  props: FilterEditorNestedFieldsProps,
) => {
  const { inferFields, fieldName, nestedPath, setNestedPath } = props;

  const options = useMemo(() => {
    const nestedValueStr = nestedPath?.namePath.join("|");
    const options: {
      label: string;
      value: string;
      nestedPath: NestedPath;
    }[] = inferFields.map((f) => {
      const flatName = f.namePath.slice(1).join(".") || ROOT_NAME;
      const valueStr = f.namePath.join("|");
      return {
        label: flatName,
        value: valueStr,
        nestedPath: {
          namePath: f.namePath,
          type: f.type,
        },
      };
    });

    if (
      nestedPath != null &&
      nestedPath.type !== CUSTOM_FIELD_TYPE &&
      !options.some((o) => o.value === nestedValueStr)
    ) {
      options.push({
        label: nestedPath.namePath.slice(1).join(".") || ROOT_NAME,
        value: nestedPath.namePath.join("|"),
        nestedPath: {
          ...nestedPath,
        },
      });
    }
    return options;
  }, [inferFields, nestedPath]);

  const value = nestedPath?.namePath.join("|");
  return (
    <>
      <Combobox
        options={options}
        noSearch={options.length < 5}
        variant="button"
        buttonSize="xs"
        noResultsLabel="No path found"
        itemLabelClassName="line-clamp-3"
        searchPlaceholder={`Search paths in ${fieldName}`}
        selectedValue={value ?? ""}
        placeholderLabel="Custom path"
        align="start"
        onChange={(_, opt) => {
          setNestedPath(opt.nestedPath);
        }}
        bottomActions={[
          {
            label: "Use a custom path",
            onSelect: () => {
              setNestedPath(CUSTOM_PATH_VALUE);
            },
            selected: nestedPath?.type === CUSTOM_FIELD_TYPE,
          },
        ]}
      />
      {nestedPath && nestedPath.type === CUSTOM_FIELD_TYPE && (
        <div className="flex">
          <Input
            type="text"
            placeholder="Enter path"
            name="path"
            value={nestedPath.namePath.slice(1).join(".") ?? ""}
            onChange={(e) => {
              const { value } = e.target;
              setNestedPath({
                namePath: [fieldName, ...value.split(".")],
                type: CUSTOM_FIELD_TYPE,
              });
            }}
            className="h-7 px-2 text-xs"
          />
        </div>
      )}
    </>
  );
};

export const FilterEditorNestedFields = (
  props: FilterEditorNestedFieldsProps & {
    isLoading?: boolean;
    hasNestedFields?: boolean;
  },
) => {
  if (props.isLoading) {
    return <Skeleton className="h-7 min-w-20" />;
  }

  if (!props.hasNestedFields) {
    return null;
  }

  return <FilterEditorNestedFieldsComponent {...props} />;
};
