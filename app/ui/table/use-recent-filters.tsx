import { useEntityStorage } from "#/lib/clientDataStorage";
import { usePathname, useParams } from "next/navigation";
import { useCallback, useMemo } from "react";

export const useRecentFilters = () => {
  const pathname = usePathname();
  const params = useParams();

  const tableType = useMemo(() => {
    if (!pathname) return null;

    if (pathname.includes("/experiments")) {
      if (params?.experiment) return "experiment";
      return "experiments";
    }

    if (pathname.includes("/datasets")) {
      if (params?.dataset) return "dataset";
      return "datasets";
    }

    if (pathname.includes("/logs")) {
      return "logs";
    }

    if (pathname.includes("/prompts")) {
      return "prompts";
    }

    if (pathname.includes("/playgrounds")) {
      return "playgrounds";
    }

    return null;
  }, [pathname, params]);

  const [recentFiltersState, setRecentFilters] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: tableType ?? "",
    key: "recentFilters",
  });

  const addRecentFilter = useCallback(
    (filter: {
      label: string;
      btql: string;
      type: string;
      comparison?: { experimentId: string };
    }) => {
      setRecentFilters((filters) => {
        if (
          filters.find(
            (f) =>
              f.btql === filter.btql &&
              f.comparison?.experimentId === filter.comparison?.experimentId,
          )
        )
          return filters;
        const newFilters = [...filters, filter];
        return newFilters.slice(-5);
      });
    },
    [setRecentFilters],
  );

  const clearRecentFilters = useCallback(() => {
    setRecentFilters([]);
  }, [setRecentFilters]);

  return {
    recentFilters: tableType === null ? [] : recentFiltersState,
    addRecentFilter,
    clearRecentFilters,
  };
};
