import { type BubbleType } from "./bubbles";
import { type ClauseType } from "#/utils/search/search";

let bubbleId = 0;
export class Bubble implements BubbleType {
  id;
  type;
  label;
  text;
  comparisonId;
  isReadonly;
  clear;
  originType;
  hidden;

  constructor({
    type,
    label,
    text,
    comparisonId,
    isReadonly,
    clear,
    originType,
    hidden,
  }: {
    type: ClauseType;
    label: React.ReactNode;
    text?: string;
    comparisonId?: string;
    isReadonly?: boolean;
    clear: () => void;
    originType?: "btql" | "form";
    hidden?: boolean;
  }) {
    this.id = bubbleId++;
    this.type = type;
    this.label = label;
    this.text = text;
    this.comparisonId = comparisonId;
    this.isReadonly = isReadonly;
    this.clear = clear;
    this.originType = originType;
    this.hidden = hidden;
  }
}
