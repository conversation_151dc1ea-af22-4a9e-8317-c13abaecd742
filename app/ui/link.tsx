"use client";
import { cn } from "#/utils/classnames";

import NextLink from "next/link";

export function BlueLink({
  className,
  children,
  href = "#",
  ...props
}: { children: React.ReactNode; href?: string } & Omit<
  React.ComponentProps<typeof NextLink>,
  "href"
>) {
  return (
    <NextLink
      href={href}
      className={cn("text-accent-600 hover:underline", className)}
      {...props}
    >
      {children}
    </NextLink>
  );
}

export function ExternalLink({
  children,
  className,
  ...props
}: { children: React.ReactNode } & React.ComponentProps<typeof NextLink>) {
  return (
    <NextLink
      className={cn("text-accent-600 hover:underline", className)}
      target="_blank"
      {...props}
    >
      {children}
    </NextLink>
  );
}
