import { cn } from "#/utils/classnames";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { UserRound } from "lucide-react";
import Link from "next/link";

export type AvatarSize = "xs" | "sm" | "md" | "lg";

type AvatarProps = {
  imgUrl?: string | null;
  size?: AvatarSize;
  link?: string | null;
  className?: string;
};

export const Avatar = ({
  className,
  imgUrl,
  size = "sm",
  link,
}: {
  className?: string;
} & AvatarProps) => {
  const measuredSizeClass =
    size == "xs"
      ? "size-4"
      : size === "sm"
        ? "size-6"
        : size === "md"
          ? "size-8"
          : "size-12";
  const avatar = imgUrl ? (
    <img
      src={imgUrl}
      className={cn(
        "flex-none rounded-full border bg-primary-200 text-[0px]",
        measuredSizeClass,
        className,
      )}
      alt="Avatar"
    />
  ) : (
    <div
      className={cn(
        "flex flex-none items-center justify-center rounded-full border border-primary-400 bg-primary-300 text-primary-600",
        measuredSizeClass,
        className,
      )}
    >
      <UserRound
        size={size === "xs" ? 10 : size === "sm" ? 12 : size === "md" ? 16 : 18}
      />
    </div>
  );
  if (!link) {
    return avatar;
  }
  return (
    <Link
      href={link}
      target="_blank"
      rel="noreferrer"
      className="flex items-center space-x-1"
    >
      {avatar}
    </Link>
  );
};

export const AvatarGroup = ({
  className,
  avatars,
}: {
  className?: string;
  avatars: AvatarProps[];
}) => {
  return (
    <div className={cn("flex self-center", className)}>
      {avatars.map((avatarProps, idx) => (
        <div
          className={cn(
            "flex-none",
            idx > 0 && marginForSize(avatarProps.size),
            avatarProps.className,
          )}
          key={idx}
          style={{ zIndex: avatars.length - idx }}
        >
          <Avatar {...avatarProps} />
        </div>
      ))}
    </div>
  );
};

export const AvatarGroupWithTooltip = ({
  users,
}: {
  users: Array<{
    userId: string;
    name: string;
    imgUrl: string | null | undefined;
    email?: string | null;
    className?: string;
  }>;
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="flex max-w-full items-center gap-1 overflow-hidden py-px">
          <AvatarGroup avatars={users} />
          {users.length === 1 && (
            <span className="flex-1 truncate">{users[0].name}</span>
          )}
        </div>
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent side="right">
          <div className="flex flex-col gap-2">
            {users.map((user, idx) => (
              <div key={idx} className="flex flex-row items-center gap-2">
                <Avatar imgUrl={user.imgUrl} />
                <div className="flex flex-col">
                  <span className="text-xs">{user.name}</span>
                  {user.email && (
                    <span className="text-xs text-primary-500">
                      {user.email}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
};

function marginForSize(size: AvatarSize = "sm"): string {
  switch (size) {
    case "xs":
      return "-ml-2";
    case "lg":
      return "-ml-5";
    case "md":
    case "sm":
    default:
      return "-ml-3";
  }
}
