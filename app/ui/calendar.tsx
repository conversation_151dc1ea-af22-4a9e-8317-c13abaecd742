"use client";

import * as React from "react";
import { DayPicker } from "react-day-picker";

import { cn } from "#/utils/classnames";
import { buttonVariants } from "#/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

export const Calendar = ({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) => {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      components={{
        IconLeft: () => <ArrowLeft className="size-3" />,
        IconRight: () => (
          <div className="flex justify-end">
            <ArrowRight className="size-3" />
          </div>
        ),
      }}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-xs font-medium",
        nav: "space-x-1 flex items-center",
        nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell: "text-primary-400 rounded-md w-7 font-normal text-[0.8rem]",
        row: "flex w-full mt-1",
        cell: cn(
          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-primary-100 [&:has([aria-selected].day-outside)]:bg-primary-100 [&:has([aria-selected].day-range-end)]:rounded-r-md",
          props.mode === "range"
            ? "[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md"
            : "[&:has([aria-selected])]:rounded-md",
        ),
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-7 w-7 p-0 text-xs font-medium tabular-nums aria-selected:opacity-100",
        ),
        day_range_start: "day-range-start",
        day_range_end: "day-range-end",
        day_selected:
          "bg-primary-900 [&]:text-primary-50 hover:bg-primary-900 [&]:hover:text-primary-50 focus:bg-primary-900 [&]:focus:text-primary-50",
        day_today: "bg-accent-100 text-primary-900",
        day_outside:
          "day-outside text-primary-100 aria-selected:bg-primary-100 [&]:aria-selected:text-primary-900 [&.day-range-start]:bg-primary-900 [&.day-range-end]:bg-primary-900 [&.day-range-start]:aria-selected:text-primary-50 [&.day-range-end]:aria-selected:text-primary-50 opacity-50",
        day_disabled: "text-primary-100 opacity-50",
        day_range_middle:
          "aria-selected:bg-primary-100 [&]:aria-selected:text-primary-900",
        day_hidden: "invisible",
        ...classNames,
      }}
      {...props}
    />
  );
};

Calendar.displayName = "Calendar";
