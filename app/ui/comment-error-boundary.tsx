import React, { Component, type ReactNode, type ErrorInfo } from "react";
import { AlertCircle } from "lucide-react";
import { cn } from "#/utils/classnames";

interface Props {
  children: ReactNode;
  fallbackText?: string;
  className?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class CommentErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Comment rendering error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          className={cn(
            "flex items-center gap-2 rounded-md bg-red-50 p-2 text-xs text-red-600",
            this.props.className,
          )}
        >
          <AlertCircle className="size-3.5 flex-none" />
          <span>{this.props.fallbackText || "Error rendering comment"}</span>
        </div>
      );
    }

    return this.props.children;
  }
}
