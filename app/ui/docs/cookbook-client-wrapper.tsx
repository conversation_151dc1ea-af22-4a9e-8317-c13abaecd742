"use client";

import dynamic from "next/dynamic";

// Client-side wrapper that can handle the dynamic import
export const CookbookWithFilter = dynamic(
  () =>
    import("#/ui/docs/cookbook-with-filter").then((mod) => ({
      default: mod.CookbookWithFilter,
    })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center py-8">
        <div className="text-sm text-muted-foreground">
          Loading cookbook filter...
        </div>
      </div>
    ),
  },
);
