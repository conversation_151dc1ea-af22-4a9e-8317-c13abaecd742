import {
  getAvailableModels,
  getModelEndpointTypes,
} from "@braintrust/proxy/schema";

export default function SupportedModels() {
  const availableModels = getAvailableModels();
  return (
    <ul className="mt-4 list-disc">
      {Object.entries(availableModels)
        .filter(([_, { format }]) => format !== "js")
        .map(([name]) => {
          const provider = getModelEndpointTypes(name);
          return (
            <li key={name} className="my-2">
              {name} ({provider.join(", ")}){" "}
            </li>
          );
        })}
    </ul>
  );
}
