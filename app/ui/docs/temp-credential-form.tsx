"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "#/ui/button";
import { Input } from "#/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
} from "#/ui/form";
import { type CredentialsRequest } from "@braintrust/proxy/schema";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import CodeToCopy from "#/ui/code-to-copy";
import { BlueLink } from "#/ui/link";
import { cn } from "#/utils/classnames";
import { CodeTabs, CurlTab, PYTab, TSTab } from "./code-tabs";
import { toast } from "sonner";

const CREDENTIALS_URL = "https://api.braintrust.dev/v1/proxy/credentials";
const DEFAULT_MODEL_NAME = "gpt-4o-realtime-preview-2024-10-01";
const MIN_TTL_SECONDS = 60;

const credentialFormParamSchema = z.object({
  model: z
    .string()
    .optional()
    .transform((v) => v && v.trim()),
  ttl_seconds: z.number().min(MIN_TTL_SECONDS),
  project_name: z
    .string()
    .optional()
    .transform((v) => v && v.trim()),
  authToken: z
    .string()
    .min(1)
    .transform((v) => v.trim()),
});

type TempCredentialFormParams = z.infer<typeof credentialFormParamSchema>;

/** UI for generating a temporary credential. */
export function TemporaryCredentialForm({
  className,
  id,
  codeSampleDisplayMode = "auto",
}: {
  className?: string;
  id?: string;
  codeSampleDisplayMode: "always" | "auto" | "never";
}) {
  const [tempCredential, setTempCredential] = useState("");
  const [loading, setLoading] = useState(false);
  const displayCodeSamples =
    codeSampleDisplayMode === "always" ||
    (codeSampleDisplayMode === "auto" && !!tempCredential);

  const form = useForm<TempCredentialFormParams>({
    resolver: zodResolver(credentialFormParamSchema),
    defaultValues: {
      model: DEFAULT_MODEL_NAME,
      ttl_seconds: 600,
      authToken: "",
    },
  });

  const requestTemporaryCredential = async ({
    authToken,
    model,
    project_name,
    ttl_seconds,
  }: TempCredentialFormParams) => {
    let response: Response | undefined;

    try {
      const body: CredentialsRequest = {
        model: model || undefined,
        logging: project_name
          ? { project_name, compress_audio: true }
          : undefined,
        ttl_seconds,
      };

      response = await fetch(CREDENTIALS_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify(body),
        cache: "no-store",
      });
    } catch (error) {
      toast.error(`Couldn’t request temporary credential: ${error}`);
      return;
    }

    if (!response.ok) {
      const error = await response.text();
      toast.error(`Couldn’t request temporary credential: ${error}`);
      return;
    }

    const parsedResponse = z
      .object({ key: z.string().min(1) })
      .safeParse(await response.json());
    if (!parsedResponse.success) {
      toast.error(
        `Couldn’t request temporary credential: ${parsedResponse.error}`,
      );
      return;
    }

    setTempCredential(parsedResponse.data.key);
  };

  const onSubmit = form.handleSubmit(async (data) => {
    setLoading(true);
    await requestTemporaryCredential(data);
    setLoading(false);
  });

  return (
    // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
    <div className={cn("not-prose flex flex-col gap-4", className)} id={id}>
      <Form {...form}>
        <form
          className="flex max-w-lg flex-col gap-2 rounded-md border-2 border-primary-800 p-4"
          autoCapitalize="off"
          autoComplete="off"
          autoCorrect="off"
          onSubmit={onSubmit}
        >
          <span className="text-lg font-bold">Temporary credential demo</span>
          <FormField
            control={form.control}
            name="authToken"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>API key</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    spellCheck={false}
                    {...field}
                    value={field.value ?? ""}
                    placeholder="Braintrust API key or model provider API key"
                    required
                  />
                </FormControl>
                <FormDescription>
                  This secret will be encrypted at rest using{" "}
                  <BlueLink
                    href="https://en.wikipedia.org/wiki/Transparent_data_encryption"
                    target="_blank"
                  >
                    Transparent Data Encryption
                  </BlueLink>{" "}
                  with a{" "}
                  <BlueLink
                    href="https://libsodium.gitbook.io/doc/secret-key_cryptography/aead"
                    target="_blank"
                  >
                    unique 256-bit key and nonce
                  </BlueLink>
                  . The encrypted secret will be deleted at the expiration time.
                  The plaintext secret is never saved.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="model"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>Model name (optional)</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    spellCheck={false}
                    {...field}
                    value={field.value ?? ""}
                    placeholder={DEFAULT_MODEL_NAME}
                  />
                </FormControl>
                <FormDescription>
                  Enter a model name or leave blank to allow access to any
                  model.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="project_name"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>Braintrust project name (optional)</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    spellCheck={false}
                    {...field}
                    value={field.value ?? ""}
                    placeholder="My project"
                  />
                </FormControl>
                <FormDescription>
                  Enter a project name for logging or leave blank to disable
                  logging.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ttl_seconds"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>Expiration time</FormLabel>
                <div className="flex flex-row items-center gap-2">
                  <FormControl>
                    <Input
                      type="number"
                      spellCheck={false}
                      {...field}
                      value={field.value}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        form.setValue("ttl_seconds", isNaN(value) ? 0 : value);
                      }}
                      placeholder="600"
                      required
                    />
                  </FormControl>
                  <span className="text-xs text-primary-500">seconds</span>
                </div>
                <FormDescription />
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            variant="primary"
            className="mt-2"
            isLoading={loading}
          >
            Generate
          </Button>
        </form>
      </Form>
      {tempCredential && (
        <CodeToCopy
          className="break-all"
          data={tempCredential}
          language="html"
        />
      )}
      {displayCodeSamples && (
        <CodeTabs items={["TypeScript", "Python", "cURL"]}>
          <TSTab>
            <CodeToCopy
              className="break-all"
              language="typescript"
              data={makeSampleCode("typescript", tempCredential)}
            />
          </TSTab>
          <PYTab>
            <CodeToCopy
              className="break-all"
              language="python"
              data={makeSampleCode("python", tempCredential)}
            />
          </PYTab>
          <CurlTab>
            <CodeToCopy
              className="break-all"
              language="bash"
              data={makeSampleCode("curl", tempCredential)}
            />
          </CurlTab>
        </CodeTabs>
      )}
    </div>
  );
}

function makeSampleCode(
  language: "typescript" | "python" | "curl",
  tempCredential: string,
) {
  tempCredential = tempCredential || "YOUR_TEMPORARY_CREDENTIAL";

  switch (language) {
    case "typescript":
      return `import { OpenAI } from "openai";
const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: "${tempCredential}",
  // It is safe to store temporary credentials in the browser because they have
  // limited lifetime and access.
  dangerouslyAllowBrowser: true,
});

async function main() {
  const response = await client.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [{ role: "user", content: "What is a proxy?" }],
  });
  console.log(response.choices[0].message.content);
}

main();
`;

    case "python":
      return `from openai import OpenAI

client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key="${tempCredential}",
)

response = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[{"role": "user", "content": "What is a proxy?"}],
)
print(response.choices[0].message.content)
`;

    case "curl":
      return `curl -i https://api.braintrust.dev/v1/proxy/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{
    "model": "gpt-4o-mini",
    "messages": [{ "role": "user", "content": "What is a proxy?" }]
  }' \\
  -H "Authorization: Bearer ${tempCredential}" \\
  --compressed
`;
  }
}
