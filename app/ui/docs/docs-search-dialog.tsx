"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "#/ui/command";
import { useQuery } from "@tanstack/react-query";
import { BookO<PERSON>, Hash, StickyNote } from "lucide-react";
import { Spinner } from "#/ui/icons/spinner";

const MIN_QUERY_LENGTH = 3;

type SearchHit = {
  objectID?: string;
  url: string;
  title: string;
  description?: string;
  content?: string;
  structuredData?: {
    headers: string[];
  };
  recordType: string;
  contentType?: string;
  _highlightResult: {
    title: {
      value: string;
    };
    description: {
      value: string;
    };
    content: {
      value: string;
    };
  };
};

type SharedPropsLocal = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export default function DocsSearchDialog({
  open,
  onOpenChange,
}: SharedPropsLocal) {
  const [query, setQuery] = useState("");

  const enabled = !!query.trim() && query.length >= MIN_QUERY_LENGTH;

  const {
    data: results,
    isLoading,
    isFetching,
  } = useQuery<SearchHit[]>({
    queryKey: ["docs-search", query],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const params = new URLSearchParams({
        query,
      });
      return fetch(`/api/search?${params.toString()}`, { signal }).then((res) =>
        res.json(),
      );
    },
    enabled,
  });

  return (
    <CommandDialog
      open={open}
      onOpenChange={onOpenChange}
      className="relative h-[520px] sm:max-w-2xl"
      commandProps={{
        shouldFilter: false,
        filter: () => 1,
        loop: true,
      }}
    >
      {(isLoading || isFetching) && enabled && (
        <div className="absolute top-4 right-12">
          <Spinner className="size-4 text-primary-400" />
        </div>
      )}
      <CommandInput
        placeholder="Search docs and blog"
        value={query}
        className="px-1 text-base placeholder:text-primary-400"
        hideIcon
        onValueChange={setQuery}
      />
      {!enabled || (isLoading && !results) ? (
        <CommandList>
          <CommandGroup heading="Suggestions">
            <Item url="/docs/start/eval-ui" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                Eval via UI
              </span>
            </Item>
            <Item url="/docs/start/eval-sdk" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                Eval via SDK
              </span>
            </Item>
            <Item url="/docs/providers" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                Model providers
              </span>
            </Item>
            <Item url="/docs/guides" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                Guides
              </span>
            </Item>
            <Item url="/docs/cookbook" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                Cookbook
              </span>
            </Item>
            <Item url="/docs/reference/api" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                API reference
              </span>
            </Item>
            <Item url="/docs/reference/btql" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                BTQL query syntax
              </span>
            </Item>
            <Item url="/docs/changelog" onOpenChange={onOpenChange}>
              <span className="flex items-center gap-2">
                <BookOpen className="!size-4 text-primary-400" />
                Changelog
              </span>
            </Item>
          </CommandGroup>
        </CommandList>
      ) : (
        <CommandList>
          <CommandEmpty className="p-4 text-center text-sm text-primary-400">
            No results
          </CommandEmpty>
          {results && results.length > 0 && (
            <CommandGroup heading="Results">
              {results?.map((hit) => (
                <Item
                  key={hit.objectID ?? hit.url}
                  url={hit.url}
                  onOpenChange={onOpenChange}
                >
                  <span className="flex w-full items-center gap-2 overflow-hidden">
                    {hit.recordType === "page" ? (
                      hit.contentType === "blog" ? (
                        <StickyNote className="!size-4 flex-none text-comparison-600" />
                      ) : (
                        <BookOpen className="!size-4 flex-none text-accent-600" />
                      )
                    ) : (
                      <Hash className="!size-4 flex-none text-primary-400" />
                    )}
                    <span
                      // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
                      className="docs-search-item-title flex-1 truncate text-base leading-tight font-medium"
                      dangerouslySetInnerHTML={{
                        __html: hit._highlightResult.title.value,
                      }}
                    />
                  </span>
                  {hit._highlightResult.content?.value ? (
                    <span
                      className="line-clamp-2 pt-1 text-xs break-words text-primary-500"
                      dangerouslySetInnerHTML={{
                        __html: hit._highlightResult.content.value,
                      }}
                    />
                  ) : hit._highlightResult.description?.value ? (
                    <span
                      className="truncate pt-1 text-xs text-primary-500"
                      dangerouslySetInnerHTML={{
                        __html: hit._highlightResult.description.value,
                      }}
                    />
                  ) : null}
                  {hit.url && (
                    <span className="text-[11px] text-primary-400">
                      {hit.url}
                    </span>
                  )}
                </Item>
              ))}
            </CommandGroup>
          )}
        </CommandList>
      )}
    </CommandDialog>
  );
}

const Item = ({
  children,
  url,
  onOpenChange,
}: {
  children: React.ReactNode;
  url: string;
  onOpenChange: (open: boolean) => void;
}) => {
  const router = useRouter();
  return (
    <CommandItem
      onSelect={() => {
        onOpenChange(false);
        router.push(url);
      }}
      // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
      className="docs-search-item flex flex-col items-start !py-2 text-sm"
    >
      {children}
    </CommandItem>
  );
};
