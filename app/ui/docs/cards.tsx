import { cn } from "#/utils/classnames";
import Link from "next/link";
import type { ComponentProps, CSSProperties, ReactNode } from "react";

export function Card({
  children,
  title,
  description,
  href,
  ...props
}: {
  children: ReactNode;
  title: string;
  description?: string;
  href: string;
}) {
  return (
    <Link
      href={href}
      className="mb-4 flex flex-col rounded-lg border border-primary-200 bg-primary-50 p-4 transition-colors hover:bg-primary-100"
      {...props}
    >
      <h3 className="text-xl leading-snug font-medium">{title}</h3>
      {description && (
        <p className="mt-2 text-sm text-primary-600">{description}</p>
      )}
    </Link>
  );
}

function _Cards({
  children,
  className,
  style,
  ...props
}: ComponentProps<"div">) {
  return (
    <div
      className={cn(className, "mt-4 mb-4 columns-2")}
      {...props}
      style={
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        {
          ...style,
        } as CSSProperties
      }
    >
      {children}
    </div>
  );
}

export const Cards = Object.assign(_Cards, { displayName: "Cards", Card });
