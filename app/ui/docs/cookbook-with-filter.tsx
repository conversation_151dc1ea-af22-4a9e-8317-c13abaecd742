"use client";

import { useState, useMemo } from "react";
import { CookbookCards, CookbookCard } from "#/ui/docs/cookbook-cards";
import { CookbookTagFilter } from "#/ui/docs/cookbook-tag-filter";
import { type Author } from "#/ui/docs/cookbook";
import { Button } from "#/ui/button";

interface Recipe {
  title: string;
  path: string;
  tags: string[];
  authors: Author[];
  date: string;
  urlPath: string;
  language: string;
  logo?: string;
  banner?: string;
}

interface CookbookWithFilterProps {
  recipes: Recipe[];
}

export function CookbookWithFilter({ recipes }: CookbookWithFilterProps) {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(null);

  // Extract all unique tags from recipes
  const availableTags = useMemo(() => {
    const allTags = recipes.flatMap((recipe) => recipe.tags);
    return Array.from(new Set(allTags)).sort();
  }, [recipes]);

  // Extract all unique languages from recipes
  const availableLanguages = useMemo(() => {
    const allLanguages = recipes.map((recipe) => recipe.language);
    return Array.from(new Set(allLanguages)).sort();
  }, [recipes]);

  // Filter recipes based on selected tags and language
  const filteredRecipes = useMemo(() => {
    let filtered = recipes;

    const languageTags = selectedTags
      .filter((tag) => tag.startsWith("language:"))
      .map((tag) => tag.replace("language:", ""));
    const actualTags = selectedTags.filter(
      (tag) => !tag.startsWith("language:"),
    );

    if (selectedLanguage) {
      filtered = filtered.filter(
        (recipe) => recipe.language === selectedLanguage,
      );
    }

    if (languageTags.length > 0) {
      filtered = filtered.filter((recipe) =>
        languageTags.includes(recipe.language),
      );
    }

    if (actualTags.length > 0) {
      filtered = filtered.filter((recipe) =>
        actualTags.every((selectedTag) => recipe.tags.includes(selectedTag)),
      );
    }

    return filtered;
  }, [recipes, selectedTags, selectedLanguage]);

  // Sort filtered recipes by date (newest first)
  const sortedRecipes = useMemo(() => {
    return filteredRecipes.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    );
  }, [filteredRecipes]);

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap items-start gap-2">
        <CookbookTagFilter
          availableTags={availableTags}
          availableLanguages={availableLanguages}
          selectedTags={selectedTags}
          onSelectedTagsChange={setSelectedTags}
        />
      </div>

      <div className="space-y-4">
        <CookbookCards>
          {sortedRecipes.map((recipe, idx) => {
            const slug = encodeURIComponent(recipe.urlPath);
            return (
              <CookbookCard
                key={idx}
                route={`/docs/cookbook/recipes/${slug}`}
                title={recipe.title}
                authors={recipe.authors}
                date={recipe.date}
                language={recipe.language}
                tags={recipe.tags}
                logoIconUrl={recipe.logo}
              />
            );
          })}
        </CookbookCards>

        {sortedRecipes.length === 0 &&
          (selectedTags.length > 0 || selectedLanguage) && (
            <div className="py-12 text-center">
              <div className="mb-2 text-lg font-medium text-primary-700">
                No cookbooks found
              </div>
              <Button
                size="xs"
                onClick={() => {
                  setSelectedTags([]);
                  setSelectedLanguage(null);
                }}
              >
                Clear all filters
              </Button>
            </div>
          )}
      </div>
    </div>
  );
}
