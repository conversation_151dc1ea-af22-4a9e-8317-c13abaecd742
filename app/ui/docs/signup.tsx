"use client";

import { signUpPath } from "#/utils/auth/redirects";
import { useUser } from "#/utils/user";
import Link from "next/link";

export default function SignUpLink({
  children,
}: {
  children: React.ReactNode;
}) {
  return <Link href={signUpPath()}>{children}</Link>;
}

export function RenderIfAnonymous({ children }: { children: React.ReactNode }) {
  const { status } = useUser();
  if (status === "unauthenticated") {
    return <>{children}</>;
  } else {
    return null;
  }
}
