import { Evaluators } from "autoevals";
import Markdown from "react-markdown";

export default function EvaluatorsSection() {
  return (
    <div>
      {Evaluators.map((section) => {
        return (
          <div key={section.label}>
            <h3>{section.label}</h3>
            <ul>
              {section.methods.map((method) => {
                return (
                  <li key={method.method.name} className="inline">
                    <b>{method.method.name}</b>:{" "}
                    <Markdown
                      components={{
                        p: (props) => {
                          return <p className="inline" {...props} />;
                        },
                      }}
                    >
                      {method.description}
                    </Markdown>
                  </li>
                );
              })}
            </ul>
          </div>
        );
      })}
    </div>
  );
}
