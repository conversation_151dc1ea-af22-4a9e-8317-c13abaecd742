import {
  Hash,
  Type,
  ToggleLeft,
  Brackets,
  <PERSON><PERSON><PERSON>,
  CircleOff,
} from "lucide-react";
import { z } from "zod";

// TODO: clean the types up
export type FieldValue =
  | string
  | number
  | boolean
  | null
  | FieldObject
  | FieldArray;
export type FieldObject = { [key: string]: FieldValue };
export type FieldArray = FieldValue[];

export enum NodeStructureType {
  OBJECT = "object",
  ARRAY = "array",
}

export enum NodeValueType {
  STRING = "string",
  NUMBER = "number",
  BOOLEAN = "boolean",
  NULL = "null",
}

export interface SchemaNode {
  id: string;
  type: NodeStructureType | NodeValueType;
  children?: SchemaNode[];
  key?: string;
  required?: boolean;
  description?: string;
  title?: string;
  additionalProperties?: boolean;
  isExpanded?: boolean;
  value?: FieldValue;
  enum?: string[] | number[];
}

export const TYPE_OPTIONS = [
  {
    nodeType: NodeValueType.STRING,
    label: "String",
    icon: Type,
    defaultValue: "",
  },
  {
    nodeType: NodeValueType.NUMBER,
    label: "Number",
    icon: Hash,
    defaultValue: 0,
  },
  {
    nodeType: NodeValueType.BOOLEAN,
    label: "Boolean",
    icon: ToggleLeft,
    defaultValue: false,
  },
  {
    nodeType: NodeValueType.NULL,
    label: "Null",
    icon: CircleOff,
    defaultValue: null,
  },
  {
    nodeType: NodeStructureType.OBJECT,
    label: "Object",
    icon: Braces,
    defaultValue: {},
  },
  {
    nodeType: NodeStructureType.ARRAY,
    label: "Array",
    icon: Brackets,
    defaultValue: [],
  },
];

// Utility functions
export const generateId = () =>
  `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const getDefaultValue = (
  type: NodeStructureType | NodeValueType,
): FieldValue => {
  const option = TYPE_OPTIONS.find((opt) => opt.nodeType === type);
  return option?.defaultValue ?? "";
};

export const generateUniquePropertyKey = (node: SchemaNode): string => {
  const existingKeys = new Set(
    (node.children || []).map((c) => c.key).filter(Boolean),
  );
  let counter = 0;
  while (existingKeys.has(`property${counter}`)) counter++;
  return `property${counter}`;
};

const isValueType = (type: string): type is NodeValueType => {
  return (
    type === NodeValueType.STRING ||
    type === NodeValueType.NUMBER ||
    type === NodeValueType.BOOLEAN ||
    type === NodeValueType.NULL
  );
};

const extractRequiredFields = (value: FieldValue): string[] => {
  if (
    typeof value === "object" &&
    value !== null &&
    "required" in value &&
    Array.isArray(value.required)
  ) {
    return value.required.filter(
      (item): item is string => typeof item === "string",
    );
  }
  return [];
};

const isRequired = (
  key: string | undefined,
  requiredFields: string[] | undefined,
): boolean => {
  return requiredFields?.includes(key || "") || false;
};

const createBaseNode = (
  nodeId: string,
  parentKey: string | undefined,
  nodeType: NodeStructureType | NodeValueType,
  value: FieldValue,
  parentRequiredFields?: string[],
  title?: string,
  description?: string,
  isExpanded = true,
): SchemaNode => ({
  id: nodeId,
  key: parentKey,
  type: nodeType,
  value,
  required: isRequired(parentKey, parentRequiredFields),
  title,
  description,
  isExpanded,
});

const createValueNode = (
  nodeId: string,
  parentKey: string | undefined,
  valueType: NodeValueType,
  value: FieldValue,
  parentRequiredFields?: string[],
  title?: string,
  description?: string,
  enumValues?: string[] | number[],
): SchemaNode => ({
  ...createBaseNode(
    nodeId,
    parentKey,
    valueType,
    value,
    parentRequiredFields,
    title,
    description,
  ),
  enum: enumValues,
});

const createObjectNode = (
  nodeId: string,
  parentKey: string | undefined,
  value: FieldValue,
  children: SchemaNode[],
  additionalProperties?: boolean,
  title?: string,
  description?: string,
  isExpanded = true,
  parentRequiredFields?: string[],
): SchemaNode => ({
  ...createBaseNode(
    nodeId,
    parentKey,
    NodeStructureType.OBJECT,
    value,
    parentRequiredFields,
    title,
    description,
    isExpanded,
  ),
  additionalProperties,
  children,
});

const createArrayNode = (
  nodeId: string,
  parentKey: string | undefined,
  value: FieldValue,
  children: SchemaNode[],
  parentRequiredFields?: string[],
  title?: string,
  description?: string,
  isExpanded = true,
): SchemaNode => ({
  ...createBaseNode(
    nodeId,
    parentKey,
    NodeStructureType.ARRAY,
    value,
    parentRequiredFields,
    title,
    description,
    isExpanded,
  ),
  children,
});

const getValueType = (value: FieldValue): NodeValueType | undefined => {
  if (value === null) return NodeValueType.NULL;
  if (typeof value === "string") return NodeValueType.STRING;
  if (typeof value === "number") return NodeValueType.NUMBER;
  if (typeof value === "boolean") return NodeValueType.BOOLEAN;
  return undefined;
};

const extractEnumValues = (
  value: FieldObject,
  valueType: NodeValueType,
): string[] | number[] | undefined => {
  if (!("enum" in value) || !Array.isArray(value.enum)) return undefined;

  if (valueType === NodeValueType.STRING) {
    return value.enum.filter(
      (item: unknown): item is string => typeof item === "string",
    );
  }
  if (valueType === NodeValueType.NUMBER) {
    return value.enum.filter(
      (item: unknown): item is number => typeof item === "number",
    );
  }
  return undefined;
};

const isFieldObject = (value: FieldValue): value is FieldObject => {
  return typeof value === "object" && value !== null && !Array.isArray(value);
};

const processObjectProperties = (
  value: FieldObject,
  existingChildren: SchemaNode[],
  currentRequiredFields: string[],
): SchemaNode[] => {
  const existingChildrenMap = new Map(
    existingChildren.map((child) => [child.key, child]),
  );

  const filteredEntries = Object.entries(value).filter(
    ([key]) =>
      key !== "required" &&
      key !== "additionalProperties" &&
      key !== "type" &&
      key !== "title" &&
      key !== "description" &&
      key !== "enum",
  );

  return filteredEntries.map(([key, val]) => {
    const existingChild = existingChildrenMap.get(key);
    return valueToNodes(val, key, existingChild, currentRequiredFields);
  });
};

export const valueToNodes = (
  value: FieldValue,
  parentKey?: string,
  existingNode?: SchemaNode,
  parentRequiredFields?: string[],
): SchemaNode => {
  const nodeId = existingNode?.id || generateId();
  const valueType = getValueType(value);

  if (valueType) {
    const enumValues = isFieldObject(value)
      ? extractEnumValues(value, valueType)
      : undefined;

    return createValueNode(
      nodeId,
      parentKey,
      valueType,
      value,
      parentRequiredFields,
      undefined,
      undefined,
      enumValues,
    );
  }

  if (Array.isArray(value)) {
    const existingChildren = existingNode?.children || [];
    const children = value
      .map((item, index) => {
        const existingChild = existingChildren[index];
        return valueToNodes(
          item,
          index.toString(),
          existingChild,
          parentRequiredFields,
        );
      })
      .concat(existingChildren.slice(value.length));

    return createArrayNode(
      nodeId,
      parentKey,
      value,
      children,
      parentRequiredFields,
      undefined,
      undefined,
      existingNode?.isExpanded ?? true,
    );
  }

  if (typeof value === "object" && value !== null && !Array.isArray(value)) {
    const existingChildren = existingNode?.children || [];
    const rootRequiredFields =
      parentKey === undefined
        ? extractRequiredFields(value)
        : parentRequiredFields;

    if (value && "type" in value && typeof value.type === "string") {
      const schemaType = value.type;

      if (isValueType(schemaType)) {
        const enumValues = isFieldObject(value)
          ? extractEnumValues(value, schemaType)
          : undefined;
        return createValueNode(
          nodeId,
          parentKey,
          schemaType,
          getDefaultValue(NodeStructureType.OBJECT),
          rootRequiredFields,
          typeof value.title === "string" ? value.title : undefined,
          typeof value.description === "string" ? value.description : undefined,
          enumValues,
        );
      }

      if (schemaType === "object") {
        const currentRequiredFields = extractRequiredFields(value);
        const additionalProperties =
          typeof value.additionalProperties === "boolean"
            ? value.additionalProperties
            : false;

        let children: SchemaNode[] = [];
        if (
          "properties" in value &&
          typeof value.properties === "object" &&
          value.properties !== null
        ) {
          children = Object.entries(value.properties).map(([key, val]) => {
            const existingChild = existingChildren.find(
              (child) => child.key === key,
            );
            return valueToNodes(val, key, existingChild, currentRequiredFields);
          });
        } else {
          children = processObjectProperties(
            value,
            existingChildren,
            currentRequiredFields,
          );
        }

        const newChildren = existingChildren.filter(
          (child) => !children.some((c) => c.key === child.key),
        );

        return createObjectNode(
          nodeId,
          parentKey,
          value,
          [...children, ...newChildren],
          additionalProperties,
          typeof value.title === "string" ? value.title : undefined,
          typeof value.description === "string" ? value.description : undefined,
          existingNode?.isExpanded ?? true,
          rootRequiredFields,
        );
      }

      if (schemaType === "array") {
        const items = value.items;
        let children: SchemaNode[] = [];
        if (items) {
          const itemsRequiredFields = extractRequiredFields(items);
          children = [
            valueToNodes(
              items,
              undefined,
              existingChildren[0],
              itemsRequiredFields,
            ),
          ];
        }

        return createArrayNode(
          nodeId,
          parentKey,
          value,
          children,
          rootRequiredFields,
          typeof value.title === "string" ? value.title : undefined,
          typeof value.description === "string" ? value.description : undefined,
          existingNode?.isExpanded ?? true,
        );
      }
    }

    const currentRequiredFields = extractRequiredFields(value);
    const additionalProperties =
      typeof value?.additionalProperties === "boolean"
        ? value?.additionalProperties
        : false;

    const children = processObjectProperties(
      value,
      existingChildren,
      currentRequiredFields,
    );
    const newChildren = existingChildren.filter(
      (child) => !children.some((c) => c.key === child.key),
    );

    return createObjectNode(
      nodeId,
      parentKey,
      value,
      [...children, ...newChildren],
      additionalProperties,
      undefined,
      undefined,
      existingNode?.isExpanded ?? true,
      rootRequiredFields,
    );
  }

  return createValueNode(
    nodeId,
    parentKey,
    NodeValueType.STRING,
    "",
    parentRequiredFields,
  );
};

// Convert nodes back to JSON schema
export const nodesToValue = (node: SchemaNode): FieldValue => {
  if (isValueType(node.type)) {
    const valueSchema: FieldObject = { type: node.type };
    if (node.title) valueSchema.title = node.title;
    if (node.description) valueSchema.description = node.description;
    if (node.enum) valueSchema.enum = node.enum;
    return valueSchema;
  }

  switch (node.type) {
    case NodeStructureType.OBJECT: {
      if (!node.children) return {};

      const obj: FieldObject = { type: NodeStructureType.OBJECT };
      const required: string[] = [];
      const properties: FieldObject = {};

      node.children.forEach((child) => {
        if (child.key !== undefined && child.key !== "required") {
          properties[child.key] = nodesToValue(child);
          if (child.required) {
            required.push(child.key);
          }
        }
      });

      if (Object.keys(properties).length > 0) obj.properties = properties;
      if (required.length > 0) obj.required = required;
      if (node.additionalProperties !== undefined)
        obj.additionalProperties = node.additionalProperties;
      if (node.title) obj.title = node.title;
      if (node.description) obj.description = node.description;

      return obj;
    }

    case NodeStructureType.ARRAY: {
      if (!node.children) return [];

      const arraySchema: FieldObject = { type: NodeStructureType.ARRAY };
      if (node.children.length > 0)
        arraySchema.items = nodesToValue(node.children[0]);
      if (node.title) arraySchema.title = node.title;
      if (node.description) arraySchema.description = node.description;

      return arraySchema;
    }

    default:
      return node.value ?? "";
  }
};

export const validateNodeKey = (
  key: string,
  parentNode?: SchemaNode,
  currentNodeId?: string,
): { success: boolean; error?: string } => {
  const keySchema = z.string().min(1, "Key cannot be empty");
  const result = keySchema.safeParse(key);
  if (!result.success) {
    return { success: false, error: result.error.errors[0].message };
  }

  if (parentNode && parentNode.children) {
    const duplicateKey = parentNode.children.find(
      (sibling) => sibling.id !== currentNodeId && sibling.key === key,
    );

    if (duplicateKey) {
      return { success: false, error: "Key already exists" };
    }
  }

  return { success: true };
};
