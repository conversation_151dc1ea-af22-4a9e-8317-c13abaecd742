"use client";

import { cn } from "#/utils/classnames";
import { ErrorBoundary } from "#/utils/error-boundary";
import * as ResizablePrimitive from "react-resizable-panels";

const ResizablePanelGroup = ({
  className,
  debugProps,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup> & {
  debugProps?: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    windowInnerWidth: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    minSize: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    maxSize: any;
  };
}) => (
  <ErrorBoundary autoSaveId={props.autoSaveId} {...debugProps}>
    <ResizablePrimitive.PanelGroup
      className={cn(
        "flex h-full w-full data-[panel-group-direction=vertical]:flex-col",
        className,
      )}
      {...props}
    />
  </ErrorBoundary>
);

const ResizablePanel = ResizablePrimitive.Panel;

const ResizableHandle = ({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle>) => (
  <ResizablePrimitive.PanelResizeHandle
    className={cn(
      "relative flex w-px items-center justify-center bg-primary-200 after:absolute after:inset-y-0 after:left-1/2 after:z-50 after:w-1 after:-translate-x-1/2 after:transition-colors focus-within:bg-accent-200 hover:after:bg-accent-200 focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 focus-visible:outline-hidden active:after:bg-accent-200 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:translate-x-0 data-[panel-group-direction=vertical]:after:-translate-y-1/2 [&[data-panel-group-direction=vertical]>div]:rotate-90",
      className,
    )}
    {...props}
  ></ResizablePrimitive.PanelResizeHandle>
);

export { ResizablePanelGroup, ResizablePanel, ResizableHandle };
