#!/usr/bin/env node
import path from "node:path";
import { fileURLToPath } from "node:url";
import fs from "node:fs/promises";
import process from "node:process";
import fg from "fast-glob";
import matter from "gray-matter";
import { algoliasearch } from "algoliasearch";
import { unified } from "unified";
import remarkParse from "remark-parse";
import remarkMdx from "remark-mdx";
import slugify from "slugify";
import dotenv from "dotenv";

const envCandidates = [
  path.resolve(process.cwd(), ".env"),
  path.resolve(process.cwd(), ".env.development"),
  path.resolve(process.cwd(), ".env.local"),
];
for (const p of envCandidates) {
  try {
    dotenv.config({ path: p });
  } catch {}
}

// Configuration
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Resolve directories relative to this script so it works in postinstall
const DOCS_ROOT = path.resolve(__dirname, "..", "content/docs");
const BLOG_ROOT = path.resolve(__dirname, "..", "content/blog");
const DOCS_BASE_URL_PREFIX = "/docs";
const BLOG_BASE_URL_PREFIX = "/blog";

function toUrlFromFile(relativeFile, baseUrlPrefix) {
  const noExt = relativeFile.replace(/\.mdx?$/i, "");
  // Treat `foo/index.mdx` as `/docs/foo`, otherwise `/docs/foo/bar`
  const normalized = noExt.endsWith("/index")
    ? noExt.slice(0, -"/index".length)
    : noExt;
  const urlPath = `${baseUrlPrefix}/${normalized}`
    .replace(/\\+/g, "/")
    .replace(/\/+/, "/");
  return urlPath;
}

function nodeToText(node) {
  if (!node) return "";
  if (typeof node.value === "string") return node.value;
  if (Array.isArray(node.children))
    return node.children.map(nodeToText).join("");
  return "";
}

function slug(text) {
  return slugify(String(text || ""), { lower: true, strict: true });
}

async function extractSections(markdown) {
  const ast = unified().use(remarkParse).use(remarkMdx).parse(markdown);
  const children = Array.isArray(ast.children) ? ast.children : [];

  // Collect heading indices
  const headings = [];
  for (let i = 0; i < children.length; i++) {
    const n = children[i];
    if (
      n &&
      n.type === "heading" &&
      typeof n.depth === "number" &&
      n.depth >= 1 &&
      n.depth <= 6
    ) {
      const title = nodeToText(n).trim();
      headings.push({ index: i, depth: n.depth, title });
    }
  }

  // Build sections between headings
  const sections = [];
  for (let h = 0; h < headings.length; h++) {
    const { index, depth, title } = headings[h];
    const nextIndex =
      h + 1 < headings.length ? headings[h + 1].index : children.length;
    const bodyNodes = children
      .slice(index + 1, nextIndex)
      .filter((n) => n.type !== "heading");
    const text = nodeToText({ children: bodyNodes })
      .replace(/\s+/g, " ")
      .trim();
    sections.push({ depth, title, text });
  }

  // If no headings, create a single section with document text
  if (sections.length === 0) {
    const text = nodeToText({ children }).replace(/\s+/g, " ").trim();
    return [{ depth: 1, title: "", text }];
  }

  return sections;
}

function safeTrim(str, max = 2000) {
  if (!str) return "";
  if (str.length <= max) return str;
  return `${str.slice(0, max - 1)}…`;
}

async function buildDocsRecords() {
  const files = await fg(["**/*.mdx"], { cwd: DOCS_ROOT, absolute: true });
  const records = [];

  for (const absFile of files) {
    const relFile = path.relative(DOCS_ROOT, absFile);
    const urlBase = toUrlFromFile(relFile, DOCS_BASE_URL_PREFIX);
    const raw = await fs.readFile(absFile, "utf8");
    const { content, data } = matter(raw);

    const pageTitle = (data && (data.title || data.pageTitle)) || "";
    const pageDescription = (data && (data.description || data.summary)) || "";

    // Page-level record
    if (content.trim()) {
      const sections = await extractSections(content);

      // Include a top-level page record derived from frontmatter and first section
      const firstText = sections[0]?.text || "";
      records.push({
        objectID: urlBase,
        url: urlBase,
        title: pageTitle || sections[0]?.title || urlBase,
        description: safeTrim(pageDescription || firstText, 300),
        content: safeTrim(firstText, 3000),
        // Prioritize page-level records over section-level ones
        recordType: "page",
        contentType: "docs",
        rankScore: 1000,
        structuredData: {
          headers: sections.map((s) => s.title).filter(Boolean),
          filePath: absFile,
        },
      });

      // Section-level records for better recall
      for (const s of sections) {
        if (!s.title || !s.text) continue;
        if (s.depth === 1) continue; // avoid duplicating the page H1 as a section
        const anchor = slug(s.title);
        const url = anchor ? `${urlBase}#${anchor}` : urlBase;
        records.push({
          objectID: url,
          url,
          title: s.title,
          description: safeTrim(s.text, 300),
          content: safeTrim(s.text, 3000),
          recordType: "section",
          contentType: "docs",
          // Slightly prioritize shallower headings
          rankScore: Math.max(1, 500 - (s.depth ?? 3) * 10),
          structuredData: {
            headers: [pageTitle, s.title].filter(Boolean),
            filePath: absFile,
          },
        });
      }
    }
  }

  return records;
}

async function buildBlogRecords() {
  const files = await fg(["**/*.mdx"], { cwd: BLOG_ROOT, absolute: true });
  const records = [];

  for (const absFile of files) {
    const relFile = path.relative(BLOG_ROOT, absFile);

    // Skip draft posts (those starting with underscore)
    if (relFile.startsWith("_")) continue;

    const urlBase = toUrlFromFile(relFile, BLOG_BASE_URL_PREFIX);
    const raw = await fs.readFile(absFile, "utf8");
    const { content, data } = matter(raw);

    const pageTitle = data?.title || "";
    const pageDescription = data?.description || "";
    const author = data?.author || "";
    const date = data?.date || "";

    // Only create page-level records for blog posts (no sections)
    if (content.trim() && pageTitle) {
      const sections = await extractSections(content);
      const firstText = sections[0]?.text || "";

      records.push({
        objectID: urlBase,
        url: urlBase,
        title: pageTitle,
        description: safeTrim(pageDescription || firstText, 300),
        content: safeTrim(firstText, 3000),
        recordType: "page",
        contentType: "blog",
        // Slightly lower rank than docs for general searches
        rankScore: 800,
        structuredData: {
          headers: [pageTitle],
          filePath: absFile,
          author,
          date,
        },
      });
    }
  }

  return records;
}

async function buildRecords() {
  const docsRecords = await buildDocsRecords();
  const blogRecords = await buildBlogRecords();

  console.log(
    `Built ${docsRecords.length} docs records and ${blogRecords.length} blog records`,
  );

  return [...docsRecords, ...blogRecords];
}

async function main() {
  const dryRun = process.argv.includes("--dry-run");

  console.log("Building Algolia records from", DOCS_ROOT, "and", BLOG_ROOT);
  const records = await buildRecords();
  console.log(`Prepared ${records.length} records`);

  if (dryRun) {
    console.log("Dry run. Sample record:", JSON.stringify(records[0], null, 2));
    return;
  }

  const appId = process.env.ALGOLIA_APP_ID;
  const adminKey = process.env.ALGOLIA_ADMIN_API_KEY;
  const indexName = process.env.ALGOLIA_INDEX_NAME;

  if (!appId || !adminKey || !indexName) {
    console.error(
      "Missing required env vars: ALGOLIA_APP_ID, ALGOLIA_ADMIN_API_KEY, ALGOLIA_INDEX_NAME",
    );
    process.exit(1);
  }

  const client = algoliasearch(appId, adminKey);

  // Ensure index prioritizes titles and page-level records (Algolia v5 API)
  await client.setSettings({
    indexName,
    indexSettings: {
      searchableAttributes: [
        "unordered(title)",
        "unordered(description)",
        "unordered(content)",
      ],
      customRanking: ["desc(rankScore)"],
      attributesToRetrieve: [
        "objectID",
        "url",
        "title",
        "description",
        "content",
        "structuredData",
        "recordType",
        "contentType",
        "rankScore",
      ],
    },
  });

  console.log(`Replacing all objects in index '${indexName}'...`);
  await client.replaceAllObjects({
    indexName,
    objects: records,
    autoGenerateObjectIDIfNotExist: false,
  });
  console.log("Done.");
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
