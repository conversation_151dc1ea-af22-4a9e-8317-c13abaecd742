import $RefParser from "@apidevtools/json-schema-ref-parser";
import * as path from "path";
import * as fs from "fs";
async function main() {
  const mySchema = JSON.parse(
    new TextDecoder().decode(
      fs.readFileSync(
        path.resolve(__dirname, "../../openapi/openapi/spec.json"),
      ),
    ),
  );
  try {
    await $RefParser.dereference(mySchema);
    console.log(JSON.stringify(mySchema, null, 2));
  } catch (err) {
    console.error(err);
  }
}

main();
