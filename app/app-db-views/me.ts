// Mirrors the 'me' view defined in app/supabase/schema.sql.

import { SqlQueryParams } from "#/utils/sql-query-params";
export { meSchema } from "@braintrust/local/app-schema";

// RBAC_DISCLAIMER: Any org member can view basic information about the orgs
// they are in.
export function meQuery({
  auth_id,
  startingParams,
}: {
  auth_id: string;
  startingParams?: SqlQueryParams;
}) {
  const queryParams = startingParams ?? new SqlQueryParams();
  const query = `
with results as (
select
    users.id,
    users.email,
    (
        select
            json_agg(jsonb_build_object('id', members.org_id,
                                        'name', organizations.name)) json_agg
        from
            members
            join organizations on members.org_id = organizations.id
        where members.user_id = users.id
    ) organizations
from users
where users.auth_id = ${queryParams.add(auth_id)}
group by users.id
)
select
    id,
    email,
    coalesce(organizations, '[]'::json) organizations
from
    results
  `;
  return { query, queryParams };
}
