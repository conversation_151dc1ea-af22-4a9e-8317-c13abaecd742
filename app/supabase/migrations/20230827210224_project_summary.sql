CREATE OR REPLACE VIEW project_summary
WITH (security_invoker = on)
AS
SELECT
  organizations.name AS org_name, projects.id AS project_id, MAX(projects.name) AS project_name,
  COUNT(DISTINCT experiments.id) AS num_experiments, COUNT(DISTINCT datasets.id) AS num_datasets
FROM
  organizations JOIN projects ON organizations.id = projects.org_id
  LEFT JOIN active_experiments experiments ON projects.id = experiments.project_id
  LEFT JOIN active_datasets datasets ON projects.id = datasets.project_id
  GROUP BY 1, 2
;
