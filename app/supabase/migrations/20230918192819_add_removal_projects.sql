-- Add deleted_at column to projects table
ALTER TABLE projects
ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;

-- Add the deleted_at column to the unique index.
DROP INDEX projects_org_id_name_idx;
CREATE UNIQUE INDEX ON projects (org_id, name, deleted_at) NULLS NOT DISTINCT;

-- Add view for only active projects
CREATE OR REPLACE VIEW active_projects WITH (security_invoker = on) AS
SELECT *
FROM projects
WHERE deleted_at IS NULL;

-- Redefine views/functions where projects are referenced to use the
-- active_projects view.

-- Should be changed, otherwise user could not create projects with the same name as deleted
create or replace function get_or_create_project(
    project_name text,
    org_id uuid)
returns uuid
language plpgsql
security invoker
as $$
declare
    _project_id uuid;
begin
    select
        id into _project_id
    from
        active_projects projects
    where
        projects.name = project_name
        and projects.org_id = get_or_create_project.org_id
    ;
    if not found then
        insert into
            projects(org_id, name, user_id)
        values
            (get_or_create_project.org_id, project_name, get_user_id())
        returning
            id into _project_id
        ;
    end if;
    return _project_id;
end;
$$;

CREATE OR REPLACE VIEW active_experiments WITH (security_invoker = on) AS
SELECT experiments.*
FROM experiments JOIN active_projects ON active_projects.id = experiments.project_id
WHERE experiments.deleted_at IS NULL;

CREATE OR REPLACE VIEW active_datasets WITH (security_invoker = on) AS
SELECT datasets.*
FROM datasets JOIN active_projects ON active_projects.id = datasets.project_id
WHERE datasets.deleted_at IS NULL;

CREATE OR REPLACE VIEW visible_experiments WITH (security_invoker = on) AS
SELECT
  projects.name AS project_name,
  organizations.id AS org_id,
  organizations.name AS org_name,
  experiments.*
FROM active_experiments experiments
JOIN active_projects projects ON projects.id = experiments.project_id
JOIN organizations ON organizations.id = projects.org_id
WHERE
  -- Auth check
  (experiments.public = true OR (
    EXISTS (
      SELECT 1
      FROM members
      JOIN users ON users.id = members.user_id
      WHERE
        users.auth_id = auth.user_id()
        AND members.org_id = organizations.id
    )
  ));

CREATE OR REPLACE VIEW project_summary
WITH (security_invoker = on)
AS
SELECT
  organizations.name AS org_name,
  projects.id AS project_id,
  MAX(projects.name) AS project_name,
  COUNT(DISTINCT experiments.id) AS num_experiments,
  COUNT(DISTINCT datasets.id) AS num_datasets
FROM
  organizations
  JOIN active_projects projects ON organizations.id = projects.org_id
  LEFT JOIN active_experiments experiments ON projects.id = experiments.project_id
  LEFT JOIN active_datasets datasets ON projects.id = datasets.project_id
  GROUP BY 1, 2;
