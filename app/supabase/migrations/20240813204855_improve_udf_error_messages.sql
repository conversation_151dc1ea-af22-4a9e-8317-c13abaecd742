CREATE OR REPLACE FUNCTION public.create_api_key_full(auth_id uuid, org_id uuid, name text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  _org_check int;
  api_key text;
  _created_api_key_id uuid;
BEGIN
  SELECT 1
  FROM users JOIN members ON users.id = members.user_id
  WHERE users.auth_id = create_api_key_full.auth_id
    AND (members.org_id = create_api_key_full.org_id OR create_api_key_full.org_id IS NULL)
  INTO _org_check;

  if not found then
    raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('User with auth_id %s does not belong to organization with id %s', auth_id, org_id));
  end if;

  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  INSERT INTO api_keys (key_hash, name, preview_name, user_id, org_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT('sk-', RIGHT(api_key, 4)), id, org_id
    FROM users WHERE users.auth_id = create_api_key_full.auth_id
  RETURNING id INTO _created_api_key_id;

  return (
    select jsonb_build_object(
        'api_key', to_jsonb(api_keys) || jsonb_build_object('key', concat('sk-', api_key))
    )
    from api_keys
    where id = _created_api_key_id
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.determine_resource_tier(user_id uuid)
 RETURNS resource_tier
 LANGUAGE plpgsql
 STABLE
AS $function$
declare
    _user_email text;
begin
    select email
    from users
    where id = user_id
    into _user_email;

    if not found then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'user', 'objectId', user_id);
    end if;

    -- If the user's email ends in '.edu', use the 'edu' tier. Otherwise use the
    -- 'free' tier.
    if trim(trailing from _user_email) like '%.edu' then
        return 'edu';
    else
        return 'free';
    end if;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.get_or_create_project(auth_id uuid, project_name text, org_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_id uuid;
begin
    -- Try to find the project. If found, check if we have the appropriate
    -- permissions and then return it.
    select id
    from projects
    where
        projects.name = project_name
        and projects.org_id = get_or_create_project.org_id
        and projects.deleted_at isnull
    into _project_id
    ;

    if _project_id is not null then
      if not has_project_acl('project', _project_id, 'read', _user_id) then
          raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', project_name);
      end if;
      return _project_id;
    end if;

    -- Project does not exist. Check if we have permissions to create it, and
    -- then do that.

    if not has_under_organization_acl('org_project', org_id, org_id, 'create', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create', 'objectType', 'org_project', 'objectId', organization_id);
    end if;

    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, _user_id)
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
            and projects.deleted_at isnull
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project', _object_id => _project_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;
    return _project_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_dataset(user_id uuid, project_id uuid, name text, description text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _forbid_insert_datasets boolean;
    _dataset_id uuid;
begin
    -- Permission check for this org. Default is to permit insertion.
    select resources.forbid_insert_datasets
    from projects join resources using (org_id)
    where projects.id = project_id
    into _forbid_insert_datasets
    ;

    if _forbid_insert_datasets or not has_project_acl('dataset', project_id, 'create', user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create dataset', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into datasets(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _dataset_id
    ;

    return _dataset_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_experiment(user_id uuid, project_id uuid, name text, description text, repo_info jsonb, base_exp_id uuid, dataset_id uuid, dataset_version text, metadata jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _experiment_id uuid;
begin
    if not has_project_acl('experiment', project_id, 'create', user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create experiment', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, user_id, metadata)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, user_id, metadata)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_prompt_session(user_id uuid, project_id uuid, name text, description text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _prompt_session_id uuid;
begin
    if not has_project_acl('prompt_session', project_id, 'create', user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create prompt_session', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into prompt_sessions(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _prompt_session_id
    ;

    return _prompt_session_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_nonpublic(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'delete_acls', 'objectType', 'experiment', 'objectId', experiment_id);
    end if;
    delete from acls
    where id = find_acl_id(
        _object_type => 'experiment',
        _object_id => experiment_id,
        _user_id => anon_user_id(),
        _permission => 'read',
        _restrict_object_type => 'experiment')
    ;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_public(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'create_acls', performing_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create_acls', 'objectType', 'experiment', 'objectId', experiment_id);
    end if;
    perform register_acl_unchecked(
        _object_type => 'experiment', _object_id => experiment_id,
        _user_id => anon_user_id(), _permission => 'read',
        _restrict_object_type => 'experiment');
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_dataset(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, dataset_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_dataset.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_dataset.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    select insert_dataset(
        user_id => _user_id,
        project_id => _project_id,
        name => _dataset_name,
        description => description)
    into _dataset_id;

    if _dataset_id is null then
        -- Duplicate key.
        select datasets.id
        from
            datasets
            join projects on datasets.project_id = projects.id
        where
            datasets.project_id = _project_id
            and datasets.name = _dataset_name
            and datasets.deleted_at isnull
            and projects.deleted_at isnull
        into _dataset_id
        ;
        if not found then
            raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                _dataset_name, _project_id;
        end if;

        if _update then
            -- Check for update permissions on the dataset.
            if not has_under_project_acl('dataset', _dataset_id, _project_id, 'update', _user_id) then
                raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'dataset', 'objectId', _dataset_id);
            end if;

            update datasets
            set description = register_dataset.description
            where id = _dataset_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'dataset', _object_id => _dataset_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version text DEFAULT NULL::text, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_experiment.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_experiment.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.id = register_experiment.base_exp_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', register_experiment.base_exp_id);
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            experiments.id into _base_exp_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('No base experiment found with name %s', base_experiment));
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                experiments.id,
                experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join experiments
                    on experiments.commit = c.commit
                    and experiments.project_id = _project_id
                join projects on experiments.project_id = projects.id
            where
                has_under_project_acl('experiment', experiments.id,
                                      _project_id, 'read', _user_id)
                and experiments.deleted_at isnull
                and projects.deleted_at isnull
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', _base_exp_id);
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just return the
    -- existing experiment.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'read', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', _conflicting_experiment_id);
        end if;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    metadata)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                _object_type => 'experiment', _object_id => _inserted_experiment_id,
                _user_id => _user_id, _role_id => get_owner_role_id());

            if _public then
                perform mark_experiment_as_public(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            else
                perform mark_experiment_as_nonpublic(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            end if;
        end;
    end if;

    -- The additional projections are copied from
    -- app/pages/api/experiment/_constants.ts.
    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from (
            select
                *,
                has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id()) as "public"
            from
                experiments
            where
                experiments.id = _inserted_experiment_id
        ) experiments join projects on projects.id = experiments.project_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_project_score(auth_id uuid, project_id uuid, project_score_name text, description text, score_type score_type, categories jsonb, config jsonb, update boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_score_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_scores(project_id, user_id, name, description, score_type, categories, config)
    values (project_id, _user_id, project_score_name, description, score_type, categories, config)
    on conflict do nothing
    returning id into _project_score_id
    ;

    if _project_score_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_scores
      where
          project_scores.project_id = register_project_score.project_id
          and name = register_project_score.project_score_name
      into _project_score_id
      ;
      if not found then
          raise exception 'Project score % likely deleted concurrently with creation', project_score_name;
      end if;

      if update then
        update project_scores
        set
            user_id = _user_id,
            name = register_project_score.project_score_name,
            description = register_project_score.description,
            score_type = register_project_score.score_type,
            categories = register_project_score.categories,
            config = register_project_score.config
        where id = _project_score_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_score', project_scores, 'found_existing', _found_existing)
        from project_scores
        where project_scores.id = _project_score_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_project_tag(auth_id uuid, project_id uuid, project_tag_name text, description text, color text, update boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_tag_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_tags(project_id, user_id, name, description, color)
    values (project_id, _user_id, project_tag_name, description, color)
    on conflict do nothing
    returning id into _project_tag_id
    ;

    if _project_tag_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_tags
      where
          project_tags.project_id = register_project_tag.project_id
          and name = register_project_tag.project_tag_name
      into _project_tag_id
      ;
      if not found then
          raise exception 'Project tag % likely deleted concurrently with creation', project_tag_name;
      end if;

      if update then
        update project_tags
        set
            user_id = _user_id,
            name = register_project_tag.project_tag_name,
            description = register_project_tag.description,
            color = register_project_tag.color
        where id = _project_tag_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_tag', project_tags, 'found_existing', _found_existing)
        from project_tags
        where project_tags.id = _project_tag_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, slug text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, prompt_id uuid DEFAULT NULL::uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _prompt_id uuid := coalesce(prompt_id, uuid_generate_v4());

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _inserted_prompt_id uuid;
    _found_existing boolean := false;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_prompt.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_prompt.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if not has_project_acl('prompt', _project_id, 'create', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create prompt', 'objectType', 'project', 'objectId', _project_id);
    end if;

    insert into prompts (id, project_id, user_id, slug)
    values (_prompt_id, _project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _inserted_prompt_id
    ;

    if _inserted_prompt_id is null then
        -- Duplicate key.
        _found_existing := true;
        select prompts.id
        from
            prompts
            join projects on prompts.project_id = projects.id
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
            and prompts.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    else
        perform register_acl_unchecked(
            _object_type => 'prompt', _object_id => _prompt_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts, 'found_existing', _found_existing)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt_session(auth_id uuid, org_name text, project_name text, session_name text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
  _user_id uuid = get_user_id_by_auth_id(auth_id);
  _org_id uuid;
  _project_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('No organization with name %s', org_name));
    end if;


    _project_id = get_or_create_project(auth_id, project_name, _org_id);

    select insert_prompt_session(
            user_id => _user_id,
            project_id => _project_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select prompt_sessions.id
        from prompt_sessions join projects on prompt_sessions.project_id = projects.id
        where
            project_id = _project_id
            and prompt_sessions.name = session_name
            and prompt_sessions.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'prompt_session', _object_id => _prompt_session_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.remove_member_from_org(user_to_remove_id uuid, organization_id uuid, actor_auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    actor_user_id uuid := get_user_id_by_auth_id(actor_auth_id);
begin
    if not has_under_organization_acl('org_member', organization_id, organization_id, 'delete', actor_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'delete org_member', 'objectType', 'organization', 'objectId', organization_id);
    end if;

    -- We must first delete any instances of rows which are foreign-key linked
    -- to the (user_id, org_id) tuple in the members table.
    delete from acls where user_id = user_to_remove_id and _object_org_id = organization_id;
    delete from group_users where user_id = user_to_remove_id and _group_org_id = organization_id;
    delete from members where user_id = user_to_remove_id and org_id = organization_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_windowed_resource_counts(resource_name_col text, num_shards integer, org_id_and_counts org_id_and_count[], auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _now_date date := current_date;
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', 'User does not have permission to update resources for all requested orgs');
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        coalesce(_mock_current_date, _now_date) date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
        left join _braintrust_testing_mock_current_date using (org_id)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_days" days of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' resource_total_counts as ('
        ' select'
        '     org_id,'
        '     SUM(resource_counts.count) total_value,'
        '     (resources.%I).max_value'
        ' from'
        '     resource_counts'
        '     join resources using (org_id)'
        '     join unnest($1) using (org_id)'
        ' where'
        '     resource_name = $2'
        '     and (resources.%I) is not null'
        '     and date_bucket > ($3 - (resources.%I).window_size_days)'
        '     and date_bucket <= $4'
        ' group by'
        '     org_id, max_value'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col, _now_date, _now_date
    ;

    if (array_length(_resource_violations, 1) > 0) then
        -- The text of this error message is used to identify the error in the
        -- data backend. If you change it, make sure that the filter in
        -- api-ts/src/resource_check.ts is still valid.
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('Violations of resource constraint %s: %s', resource_name_col, _resource_violations));
    end if;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_group(auth_id uuid, org_id uuid, group_name text, description text DEFAULT NULL::text, member_users uuid[] DEFAULT NULL::uuid[], member_groups uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = group_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_groups uuid[] = coalesce(member_groups, '{}');
    _update boolean = coalesce(update, false);

    _group_id uuid;
begin
    -- Search for a matching existing group.
    select id into _group_id
    from groups where groups.org_id = _org_id and name = _name and deleted_at is null;

    if _group_id is not null then
        if _update then
          -- Replace the contents of the existing group, including users and
          -- inheritors.
          if not has_under_organization_acl('group', _group_id, _org_id, 'update', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'group', 'objectId', _group_id);
          end if;

          -- Update groups, users, and inheritors with our given contents.

          update groups
          set name = _name, description = _description
          where id = _group_id;

          delete from group_users where group_id = _group_id;
          insert into group_users(group_id, user_id)
          select _group_id, user_id from unnest(_member_users) user_id;

          delete from group_members where group_id = _group_id;
          insert into group_members(group_id, member_group_id)
          select _group_id, member_group_id from unnest(_member_groups) member_group_id;
        else
          -- They must have read permission on the group.
          if not has_under_organization_acl('group', _group_id, _org_id, 'read', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'group', 'objectId', _group_id);
          end if;
        end if;
    else
        if not has_organization_acl('group', _org_id, 'create', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create group', 'objectType', 'organization', 'objectId', _org_id);
        end if;

        -- Add groups, users, and inheritors with our given contents.

        insert into groups(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _group_id;

        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        insert into group_members(group_id, member_group_id)
        select _group_id, member_group_id from unnest(_member_groups) member_group_id;

        perform register_acl_unchecked(
            _object_type => 'group', _object_id => _group_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/group/_constants.ts.
    return (
        select jsonb_build_object('group', t)
        from (
            select
                *,
                (select coalesce(array_agg(user_id)::uuid[], '{}') member_users from group_users where group_users.group_id = groups.id),
                (select coalesce(array_agg(member_group_id)::uuid[], '{}') member_groups from group_members join groups "_joined_groups" on group_members.member_group_id = "_joined_groups".id where "_joined_groups".deleted_at isnull and group_members.group_id = groups.id)
            from
                groups
            where
                groups.id = _group_id
        ) t
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_permissions permission_type[] DEFAULT NULL::permission_type[], member_restrict_object_types acl_object_type[] DEFAULT NULL::acl_object_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_restrict_object_types acl_object_type[] = coalesce(member_restrict_object_types, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Since 'member_permissions' and 'member_restrict_object_types' are
    -- coupled, the arrays must have the same length.
    if array_length(_member_permissions, 1) <> array_length(_member_restrict_object_types, 1) then
        raise exception 'member_permissions array must have the same length as member_restrict_object_types';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        if _update then
          -- Replace the contents of the existing role, including permissions and
          -- inheritors.
          if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'role', 'objectId', _role_id);
          end if;

          -- Update roles, permissions, and members with our given contents.

          update roles
          set name = _name, description = _description
          where id = _role_id;

          delete from role_permissions where role_id = _role_id;
          insert into role_permissions(role_id, permission, restrict_object_type)
          select _role_id, permission, restrict_object_type
          from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

          delete from role_members where role_id = _role_id;
          insert into role_members(role_id, member_role_id)
          select _role_id, member_role_id from unnest(_member_roles) member_role_id;
        else
          -- They must have read permission on the role.
          if not has_under_organization_acl('role', _role_id, _org_id, 'read', _user_id) then
              raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'role', 'objectId', _role_id);
          end if;
        end if;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create role', 'objectType', 'organization', 'objectId', _org_id);
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission, restrict_object_type)
        select _role_id, permission, restrict_object_type
        from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

        insert into role_members(role_id, member_role_id)
        select _role_id, member_role_id from unnest(_member_roles) member_role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/role/_constants.ts.
    return (
        select jsonb_build_object('role', t)
        from (
            select
                *,
                (select coalesce(array_agg(jsonb_build_object('permission', permission::text, 'restrict_object_type', restrict_object_type::text))::jsonb[], '{}') member_permissions from role_permissions where role_permissions.role_id = roles.id),
                (select coalesce(array_agg(role_members.member_role_id)::uuid[], '{}') member_roles from role_members join roles "_joined_roles" on role_members.member_role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_members.role_id = roles.id)
            from
                roles
            where
                roles.id = _role_id
        ) t
    );
end;
$function$
;
