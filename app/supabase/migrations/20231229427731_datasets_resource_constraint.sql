alter table "public"."resources" add column "num_dataset_row_actions" max_over_window;


CREATE OR REPLACE FUNCTION public.insert_resource_definition(org_id uuid, tier resource_tier)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 1000);
            _insert_row.num_production_log_row_actions := row(7, 1000);
            _insert_row.num_dataset_row_actions := row(7, 1000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'edu' then
            _insert_row.forbid_insert_datasets := true;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := false;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_resource_counts_for_insert(input jsonb, num_shards integer, auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _num_private_experiment_row_actions_org_id_and_counts org_id_and_count[];
    _num_production_log_row_actions_org_id_and_counts org_id_and_count[];
    _num_dataset_row_actions_org_id_and_counts org_id_and_count[];
begin
    -- Process resource 'num_private_experiment_row_actions'.

    -- 1. Grab the set of private experiments out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_private_experiment_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_experiments as (
    select
       key::uuid experiment_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'experiments', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_experiments.num_row_actions))
            ::org_id_and_count r
    from
        input_experiments
        join active_experiments on input_experiments.experiment_id = active_experiments.id
        join active_projects on active_experiments.project_id = active_projects.id
        join organizations on active_projects.org_id = organizations.id
    where
        not active_experiments.public
    group by
      organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_private_experiment_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_private_experiment_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
        auth_id => auth_id);

    -- Process resource 'num_production_log_row_actions'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the logs recorded per org. Store these into
    -- _num_production_log_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_logs as (
    select
        key::uuid org_id,
        (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_actions_per_org as (
    select
        row(org_id, SUM(num_row_actions))::org_id_and_count r
    from
        input_logs
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_production_log_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_production_log_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_production_log_row_actions_org_id_and_counts,
        auth_id => auth_id);

    -- Process resource 'num_dataset_row_actions'.

    -- 1. Grab the set of datasets out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_dataset_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_datasets as (
    select
       key::uuid dataset_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'datasets', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_datasets.num_row_actions))
            ::org_id_and_count r
    from
        input_datasets
        join active_datasets on input_datasets.dataset_id = active_datasets.id
        join active_projects on active_datasets.project_id = active_projects.id
        join organizations on active_projects.org_id = organizations.id
    group by
        organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_dataset_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_dataset_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_dataset_row_actions_org_id_and_counts,
        auth_id => auth_id);
end;
$function$
;
