-- add new column
alter table "public"."resources" add column "num_log_bytes" max_over_window;

-- update udf to insert by tier

CREATE OR REPLACE FUNCTION public.insert_resource_definition(org_id uuid, tier resource_tier)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 250000);
            _insert_row.num_production_log_row_actions := row(7, 250000);
            _insert_row.num_dataset_row_actions := row(7, 250000);
            _insert_row.num_log_bytes := row(7, 250000000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$function$;


-- update the udf to update resource counts

CREATE OR REPLACE FUNCTION public.update_resource_counts_for_insert(input jsonb, num_shards integer, auth_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _num_private_experiment_row_actions_org_id_and_counts org_id_and_count[];
    _num_production_log_row_actions_org_id_and_counts org_id_and_count[];
    _num_dataset_row_actions_org_id_and_counts org_id_and_count[];
    _num_log_bytes_org_id_and_counts org_id_and_count[];
begin
    -- Process resource 'num_private_experiment_row_actions'.

    -- 1. Grab the set of private experiments out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_private_experiment_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_experiments as (
    select
       key::uuid experiment_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'experiments', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_experiments.num_row_actions))
            ::org_id_and_count r
    from
        input_experiments
        join experiments on input_experiments.experiment_id = experiments.id
        join projects on experiments.project_id = projects.id
        join organizations on projects.org_id = organizations.id
    where
        experiments.deleted_at is null
        and projects.deleted_at is null
        and not has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id())
    group by
      organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_private_experiment_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_private_experiment_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
        auth_id => auth_id);

    -- Process resource 'num_production_log_row_actions'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the logs recorded per org. Store these into
    -- _num_production_log_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_logs as (
    select
        key::uuid org_id,
        (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_actions_per_org as (
    select
        row(org_id, SUM(num_row_actions))::org_id_and_count r
    from
        input_logs
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_production_log_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_production_log_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_production_log_row_actions_org_id_and_counts,
        auth_id => auth_id);

    -- Process resource 'num_log_bytes'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the total bytes recorded per org. Store these into
    -- _num_log_bytes_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_log_bytes as (
    select
        key::uuid org_id,
        coalesce((value->'num_row_bytes')::bigint, 0) num_row_bytes
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_bytes_per_org as (
    select
        row(org_id, SUM(num_row_bytes))::org_id_and_count r
    from
        input_log_bytes
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_bytes_per_org
    into _num_log_bytes_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_log_bytes',
        num_shards => num_shards,
        org_id_and_counts => _num_log_bytes_org_id_and_counts,
        auth_id => auth_id);

    -- Process resource 'num_dataset_row_actions'.

    -- 1. Grab the set of datasets out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_dataset_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_datasets as (
    select
       key::uuid dataset_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'datasets', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_datasets.num_row_actions))
            ::org_id_and_count r
    from
        input_datasets
        join datasets on input_datasets.dataset_id = datasets.id
        join projects on datasets.project_id = projects.id
        join organizations on projects.org_id = organizations.id
    where
        datasets.deleted_at is null
        and projects.deleted_at is null
    group by
        organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_dataset_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_dataset_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_dataset_row_actions_org_id_and_counts,
        auth_id => auth_id);

    -- Finally, return whether or not the orgs considered have unlimited
    -- resource counts, which can allow skipping future resource checks.
    return (
        with
        all_org_ids as (
            select org_id from unnest(_num_private_experiment_row_actions_org_id_and_counts)
            union all
            select org_id from unnest(_num_production_log_row_actions_org_id_and_counts)
            union all
            select org_id from unnest(_num_dataset_row_actions_org_id_and_counts)
            union all
            select value::uuid as org_id from jsonb_array_elements_text(coalesce(input->'additional_org_ids', '[]'::jsonb))
        ),
        org_id_is_unlimited as (
            select
                organizations.id org_id,
                (
                    -- No resource entry for an org implies unlimited.
                    resources.org_id isnull
                    or (
                        resources.num_private_experiment_row_actions isnull
                        and resources.num_production_log_row_actions isnull
                        and resources.num_dataset_row_actions isnull
                        and resources.num_log_bytes isnull
                    )
                ) is_unlimited
            from
                organizations left join resources on organizations.id = resources.org_id
                join members on members.org_id = organizations.id
            where
                organizations.id in (select org_id from all_org_ids)
                and members.user_id = _user_id
        )
        select jsonb_build_object(
            'is_unlimited', coalesce(jsonb_object_agg(org_id::text, is_unlimited), '{}'::jsonb))
        from org_id_is_unlimited
    );
end;
$function$
;



CREATE OR REPLACE FUNCTION public.determine_resource_tier(user_id uuid)
 RETURNS resource_tier
 LANGUAGE plpgsql
 STABLE
AS $function$
declare
    _user_email text;
begin
    select email
    from users
    where id = user_id
    into _user_email;

    if not found then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'user', 'objectId', user_id);
    end if;

    return 'free';
end;
$function$
;


-- bulk update orgs to new limits

-- First, update rows where any of the action counts are less than 250000
UPDATE resources
SET
    num_private_experiment_row_actions =
        CASE
            WHEN (num_private_experiment_row_actions IS NOT NULL AND (num_private_experiment_row_actions).max_value < 250000)
            THEN ROW((num_private_experiment_row_actions).window_size_days, 250000)::max_over_window
            ELSE num_private_experiment_row_actions
        END,
    num_production_log_row_actions =
        CASE
            WHEN (num_production_log_row_actions IS NOT NULL AND (num_production_log_row_actions).max_value < 250000)
            THEN ROW((num_production_log_row_actions).window_size_days, 250000)::max_over_window
            ELSE num_production_log_row_actions
        END,
    num_dataset_row_actions =
        CASE
            WHEN (num_dataset_row_actions IS NOT NULL AND (num_dataset_row_actions).max_value < 250000)
            THEN ROW((num_dataset_row_actions).window_size_days, 250000)::max_over_window
            ELSE num_dataset_row_actions
        END,
    num_log_bytes =
        CASE
            WHEN (num_private_experiment_row_actions IS NOT NULL AND (num_private_experiment_row_actions).max_value < 250000) OR
                 (num_production_log_row_actions IS NOT NULL AND (num_production_log_row_actions).max_value < 250000) OR
                 (num_dataset_row_actions IS NOT NULL AND (num_dataset_row_actions).max_value < 250000)
            THEN ROW(7, 250000000)::max_over_window
            ELSE num_log_bytes
        END
WHERE
    (num_private_experiment_row_actions IS NOT NULL AND (num_private_experiment_row_actions).max_value < 250000) OR
    (num_production_log_row_actions IS NOT NULL AND (num_production_log_row_actions).max_value < 250000) OR
    (num_dataset_row_actions IS NOT NULL AND (num_dataset_row_actions).max_value < 250000);

-- Then, update rows where any of the action counts are greater than 250000
UPDATE resources
SET
    num_private_experiment_row_actions =
        CASE
            WHEN (num_private_experiment_row_actions IS NOT NULL AND (num_private_experiment_row_actions).max_value > 250000 AND (num_private_experiment_row_actions).max_value < 1000000)
            THEN ROW((num_private_experiment_row_actions).window_size_days, 1000000)::max_over_window
            ELSE num_private_experiment_row_actions
        END,
    num_production_log_row_actions =
        CASE
            WHEN (num_production_log_row_actions IS NOT NULL AND (num_production_log_row_actions).max_value > 250000 AND (num_production_log_row_actions).max_value < 1000000)
            THEN ROW((num_production_log_row_actions).window_size_days, 1000000)::max_over_window
            ELSE num_production_log_row_actions
        END,
    num_dataset_row_actions =
        CASE
            WHEN (num_dataset_row_actions IS NOT NULL AND (num_dataset_row_actions).max_value > 250000 AND (num_dataset_row_actions).max_value < 1000000)
            THEN ROW((num_dataset_row_actions).window_size_days, 1000000)::max_over_window
            ELSE num_dataset_row_actions
        END,
    num_log_bytes =
        CASE
            WHEN (num_private_experiment_row_actions IS NOT NULL AND (num_private_experiment_row_actions).max_value > 250000) OR
                 (num_production_log_row_actions IS NOT NULL AND (num_production_log_row_actions).max_value > 250000) OR
                 (num_dataset_row_actions IS NOT NULL AND (num_dataset_row_actions).max_value > 250000)
            THEN NULL
            ELSE num_log_bytes
        END
WHERE
    (num_private_experiment_row_actions IS NOT NULL AND (num_private_experiment_row_actions).max_value > 250000) OR
    (num_production_log_row_actions IS NOT NULL AND (num_production_log_row_actions).max_value > 250000) OR
    (num_dataset_row_actions IS NOT NULL AND (num_dataset_row_actions).max_value > 250000);
