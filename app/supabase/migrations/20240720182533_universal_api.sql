ALTER TABLE organizations ADD COLUMN is_universal_api BOOLEAN DEFAULT NULL;

drop view if exists organizations_auth_id;
create view organizations_auth_id with (security_invoker=on) as
select
    -- TODO(manu): just return organizations.*?
    organizations.id,
    organizations.name,
    organizations.api_url,
    organizations.is_universal_api,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
from
    organizations
    join members on organizations.id = members.org_id
    join users on members.user_id = users.id;
