CREATE OR REPLACE VIEW base_experiments WITH (security_invoker) AS
SELECT sub.*, base_experiment.name AS base_exp_name
FROM (
  SELECT
  id, project_id, name,
  COALESCE(exp.base_exp_id,
  (SELECT base_exp.id FROM active_experiments base_exp WHERE
    base_exp.project_id = exp.project_id
      AND base_exp.id != exp.id
      AND base_exp.created <= exp.created ORDER BY base_exp.created DESC LIMIT 1)) base_exp_id
  FROM active_experiments exp
) sub JOIN active_experiments base_experiment ON base_experiment.id = sub.base_exp_id;
