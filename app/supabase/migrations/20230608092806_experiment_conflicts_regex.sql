create or replace view "public"."experiment_conflicts" as SELECT e1.name,
    e1.project_id,
    ( SELECT experiments.name
           FROM experiments
          WHERE ((experiments.name ~~ concat(e1.name, '%')) AND (experiments.name ~ concat(e1.name, '-\d+')) AND (e1.project_id = experiments.project_id))
          ORDER BY experiments.name DESC
         LIMIT 1) AS highest_conflict
   FROM experiments e1;
