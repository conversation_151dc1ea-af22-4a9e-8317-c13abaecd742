CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version text DEFAULT NULL::text, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_experiment.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from active_experiments experiments
        where experiments.id = register_experiment.base_exp_id;
        if not found then
            raise exception 'Base experiment does not exist or user does not have access to it';
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            where
                has_under_project_acl('experiment', active_experiments.id,
                                      _project_id, 'read', _user_id)
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception 'User does not have permissions to read base experiment';
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            active_experiments experiments
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just return the
    -- existing experiment.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'read', _user_id) then
            raise exception 'User does not have permissions to read experiment';
        end if;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    metadata)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                _object_type => 'experiment', _object_id => _inserted_experiment_id,
                _user_id => _user_id, _role_id => get_owner_role_id());

            if _public then
                perform mark_experiment_as_public(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            else
                perform mark_experiment_as_nonpublic(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            end if;
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join active_and_inactive_experiments experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$function$
;
