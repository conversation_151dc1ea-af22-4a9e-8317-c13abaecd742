create table prompts (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid references projects,
    slug text not null,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone,
    user_id uuid references users
);
alter table prompts enable row level security;

create unique index on prompts (project_id, slug, deleted_at) nulls not distinct;

create view active_prompts with (security_invoker=on) as
select *
from prompts
where deleted_at is null;

create or replace function register_prompt(
    auth_id uuid,
    org_id uuid,
    project_id uuid default null,
    project_name text default null,
    slug text default null,
    update boolean default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _prompt_id uuid;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the user has access to the project.
        select active_projects_auth_id.id into _project_id
        from active_projects_auth_id
        where
            active_projects_auth_id.auth_id = register_prompt.auth_id
            and active_projects_auth_id.id = register_prompt.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    insert into prompts (project_id, user_id, slug)
    values (_project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _prompt_id
    ;

    if _prompt_id is null then
        -- Duplicate key.
        select id
        from active_prompts prompts
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$$;

REVOKE EXECUTE ON FUNCTION register_prompt FROM PUBLIC;
