-- BEGIN SECTION RBAC APPLICATION LOGIC

-- Convenience views for different application-level queries.

create view active_roles with (security_invoker=on) as
with
all_member_privileges as (
    -- We cast the privileges enum to strings, because node-post<PERSON><PERSON> has trouble
    -- returning enum arrays as string arrays.
    select role_id id, array_agg(privilege)::text[] member_privileges
    from role_privileges
    group by role_id
),
all_member_roles as (
    select inheritor_role_id id, array_agg(role_id)::uuid[] member_roles
    from role_inheritors join roles on role_inheritors.role_id = roles.id
    where roles.deleted_at is null
    group by inheritor_role_id
)
select
    roles.*,
    coalesce(all_member_privileges.member_privileges, '{}') member_privileges,
    coalesce(all_member_roles.member_roles, '{}') member_roles
from
    roles
    left join all_member_privileges using (id)
    left join all_member_roles using (id)
where
    roles.deleted_at is null
;

create view active_teams with (security_invoker=on) as
with
all_member_users as (
    select team_id id, array_agg(user_id) member_users
    from team_users
    group by team_id
),
all_member_teams as (
    select inheritor_team_id id, array_agg(team_id) member_teams
    from team_inheritors join teams on team_inheritors.team_id = teams.id
    where teams.deleted_at is null
    group by inheritor_team_id
)
select
    teams.*,
    coalesce(all_member_users.member_users, '{}') member_users,
    coalesce(all_member_teams.member_teams, '{}') member_teams
from
    teams
    left join all_member_users using (id)
    left join all_member_teams using (id)
where
    teams.deleted_at is null
;

create view project_acl_ids with (security_invoker=on) as
select
    projects.id project_id,
    projects.name project_name,
    organizations.id org_id,
    organizations.name org_name
from
    projects join organizations on projects.org_id = organizations.id
;

create view organization_acl_ids with (security_invoker=on) as
select
    organizations.id org_id,
    organizations.name org_name
from
    organizations
;

-- Functions for checking ACLs on individual objects. It is generally preferable
-- to put ACL checks into the query directly as joins against _expanded_acls,
-- but for certain use-cases (e.g. udfs for registering a project/experiment),
-- it is more convenient to run a single ACL check as its own statement.

create function has_organization_acl(
    _object_type acl_object_type,
    _org_id uuid,
    _privilege privilege_type,
    _user_id uuid)
returns boolean
language sql
security invoker
as $$
select exists(
    select 1 from _expanded_acls
    where (
        object_type = 'organization'
        and object_id = _org_id
        and user_id = _user_id
        and privilege = _privilege
        and (restrict_object_type is null or restrict_object_type = _object_type)
    )
);
$$;

revoke execute on function has_organization_acl from public, anon;

create function has_under_organization_acl(
    _object_type acl_object_type,
    _object_id uuid,
    _org_id uuid,
    _privilege privilege_type,
    _user_id uuid)
returns boolean
language sql
security invoker
as $$
select exists(
    select 1 from _expanded_acls
    where (
        (
            object_type = _object_type
            and object_id = _object_id
            and user_id = _user_id
            and privilege = _privilege
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
        or (
            object_type = 'organization'
            and object_id = _org_id
            and user_id = _user_id
            and privilege = _privilege
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
    )
);
$$;

revoke execute on function has_under_organization_acl from public, anon;

create function has_project_acl(
    _object_type acl_object_type,
    _project_id uuid,
    _privilege privilege_type,
    _user_id uuid)
returns boolean
language sql
security invoker
as $$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_id = _user_id
                    and privilege = _privilege
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_id = _user_id
                    and privilege = _privilege
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$$;

revoke execute on function has_project_acl from public, anon;

create function has_under_project_acl(
    _object_type acl_object_type,
    _object_id uuid,
    _project_id uuid,
    _privilege privilege_type,
    _user_id uuid)
returns boolean
language sql
security invoker
as $$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = _object_type
                    and object_id = _object_id
                    and user_id = _user_id
                    and privilege = _privilege
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_id = _user_id
                    and privilege = _privilege
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_id = _user_id
                    and privilege = _privilege
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$$;

revoke execute on function has_under_project_acl from public, anon;

create function register_role(
    auth_id uuid,
    org_id uuid,
    role_name text,
    description text default null,
    member_privileges privilege_type[] default null,
    member_roles uuid[] default null,
    update boolean default null)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_privileges privilege_type[] = coalesce(member_privileges, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        -- Replace the contents of the existing role, including privileges and
        -- inheritors.
        if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update role';
        end if;

        -- Update roles, privileges, and inheritors with our given contents.

        update roles
        set name = _name, description = _description
        where id = _role_id;

        delete from role_privileges where role_id = _role_id;
        insert into role_privileges(role_id, privilege)
        select _role_id, privilege from unnest(_member_privileges) privilege;

        delete from role_inheritors where inheritor_role_id = _role_id;
        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, privileges, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_privileges(role_id, privilege)
        select _role_id, privilege from unnest(_member_privileges) privilege;

        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;

        perform register_acl_unchecked(
            object_type => 'role', object_id => _role_id,
            user_id => _user_id, role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('role', active_roles)
        from active_roles
        where id = _role_id
    );
end;
$$;

revoke execute on function register_role from public, anon;

create function register_team(
    auth_id uuid,
    org_id uuid,
    team_name text,
    description text default null,
    member_users uuid[] default null,
    member_teams uuid[] default null,
    update boolean default null)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = team_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_teams uuid[] = coalesce(member_teams, '{}');
    _update boolean = coalesce(update, false);

    _team_id uuid;
begin
    -- Search for a matching existing team.
    select id into _team_id
    from teams where teams.org_id = _org_id and name = _name and deleted_at is null;

    if _team_id is not null then
        -- Replace the contents of the existing team, including users and
        -- inheritors.
        if not has_under_organization_acl('team', _team_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update team';
        end if;

        -- Update teams, users, and inheritors with our given contents.

        update teams
        set name = _name, description = _description
        where id = _team_id;

        delete from team_users where team_id = _team_id;
        insert into team_users(team_id, user_id)
        select _team_id, user_id from unnest(_member_users) user_id;

        delete from team_inheritors where inheritor_team_id = _team_id;
        insert into team_inheritors(team_id, inheritor_team_id)
        select team_id, _team_id from unnest(_member_teams) team_id;
    else
        if not has_organization_acl('team', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create team';
        end if;

        -- Add teams, users, and inheritors with our given contents.

        insert into teams(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _team_id;

        insert into team_users(team_id, user_id)
        select _team_id, user_id from unnest(_member_users) user_id;

        insert into team_inheritors(team_id, inheritor_team_id)
        select team_id, _team_id from unnest(_member_teams) team_id;

        perform register_acl_unchecked(
            object_type => 'team', object_id => _team_id,
            user_id => _user_id, role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('team', active_teams)
        from active_teams
        where id = _team_id
    );
end;
$$;

revoke execute on function register_team from public, anon;

-- NOTE: the permissions requirements for registering an ACL differ based on
-- context, so we leave it up to the user.
--
-- If the ACL already exists, returns the existing one.
create function register_acl_unchecked(
    object_type acl_object_type,
    object_id uuid,
    user_id uuid default null,
    team_id uuid default null,
    privilege privilege_type default null,
    role_id uuid default null,
    restrict_object_type acl_object_type default null)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _object_type acl_object_type = object_type;
    _object_id uuid = object_id;
    _user_id uuid = user_id;
    _team_id uuid = team_id;
    _privilege privilege_type = privilege;
    _role_id uuid = role_id;
    _restrict_object_type acl_object_type = restrict_object_type;

    _acl_id uuid;
    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;

    _ret_obj jsonb;
begin
    if (_user_id is null) = (_team_id is null) then
        raise exception 'Exactly one of user_id and team_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'team';
    else
        _user_object_type = 'user';
    end if;

    if (_privilege is null) = (_role_id is null) then
        raise exception 'Exactly one of privilege and role_id must be non-null';
    elsif _privilege is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'privilege';
    end if;

    insert into acls(
        object_type, object_id, user_object_type, user_id, team_id,
        grant_object_type, privilege, role_id, restrict_object_type)
    values (
        _object_type, _object_id, _user_object_type, _user_id, _team_id,
        _grant_object_type, _privilege, _role_id, _restrict_object_type)
    on conflict do nothing
    returning id into _acl_id;

    if _acl_id is null then
        -- Duplicate key. Just find the ID.
        select id into _acl_id
        from acls
        where
            acls.object_type = _object_type
            and acls.object_id = _object_id
            and acls.user_object_type = _user_object_type
            and acls.user_id is not distinct from _user_id
            and acls.team_id is not distinct from _team_id
            and acls.grant_object_type = _grant_object_type
            and acls.privilege is not distinct from _privilege
            and acls.role_id is not distinct from _role_id
            and acls.restrict_object_type is not distinct from _restrict_object_type
        ;
        if not found then
            raise exception 'Acl likely deleted concurrently with creation';
        end if;
    end if;

    return (
        select jsonb_build_object('acl', acls)
        from acls where id = _acl_id
    );
end;
$$;

revoke execute on function register_acl_unchecked from public, anon;

-- END SECTION RBAC APPLICATION LOGIC
