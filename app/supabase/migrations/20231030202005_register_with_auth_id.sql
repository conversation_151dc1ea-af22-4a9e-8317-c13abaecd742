drop function if exists get_user_id;

create or replace function get_user_id_by_auth_id(auth_id uuid)
returns uuid
language sql
security definer
as $$
SELECT id FROM users WHERE users.auth_id = get_user_id_by_auth_id.auth_id
$$;
REVOKE EXECUTE ON FUNCTION get_user_id_by_auth_id FROM PUBLIC;


drop function if exists get_or_create_project(
    project_name text,
    org_id uuid);

create or replace function get_or_create_project(
    auth_id uuid,
    project_name text,
    org_id uuid)
returns uuid
language plpgsql
security definer
as $$
declare
    _project_id uuid;
begin
    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, get_user_id_by_auth_id(auth_id))
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from active_projects projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    end if;
    return _project_id;
end;
$$;
REVOKE EXECUTE ON FUNCTION get_or_create_project FROM PUBLIC;


drop function if exists register_project(
    project_name text,
    org_id uuid);

create or replace function register_project(
    auth_id uuid,
    project_name text,
    org_id uuid)
returns jsonb
language plpgsql
security definer
as $$
declare
    _project_id uuid := get_or_create_project(auth_id, project_name, org_id);
begin
    return (
        select jsonb_build_object('project', projects)
        from projects
        where projects.id = _project_id
    );
end;
$$;

REVOKE EXECUTE ON FUNCTION register_project FROM PUBLIC;

drop function if exists register_experiment(
    project_name text,
    org_id uuid,
    experiment_name text,
    description text,
    update boolean,
    repo_info jsonb,
    base_experiment text,
    ancestor_commits text[],
    dataset_id uuid,
    dataset_version bigint,
    public boolean
);

create or replace function register_experiment(
    auth_id uuid,
    project_name text,
    org_id uuid,
    experiment_name text default null,
    description text default null,
    update boolean default false,
    repo_info jsonb default '{}',
    base_experiment text default null,
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version bigint default null,
    public boolean default false)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid := get_or_create_project(auth_id, project_name, org_id);
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Get the base experiment id from a named experiment.
    if base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            id into _conflicting_experiment_id
        from
            active_experiments
        where
            project_id = _project_id
            and name = experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = register_experiment.repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                repo_info, _base_exp_id, dataset_id, dataset_version,
                public)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    repo_info, _base_exp_id, dataset_id, dataset_version,
                    public)
                into _inserted_experiment_id;
            end if;
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$$;
REVOKE EXECUTE ON FUNCTION register_experiment FROM PUBLIC;

drop function if exists insert_experiment(
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version bigint,
    public boolean);

create or replace function insert_experiment(
    user_id uuid,
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version bigint,
    public boolean)
returns uuid
language plpgsql
security definer
as $$
declare
    _experiment_id uuid;
begin
    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, public, user_id)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, public, user_id)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$$;
REVOKE EXECUTE ON FUNCTION insert_experiment FROM PUBLIC;

drop function if exists register_dataset(
    org_id uuid,
    project_name text,
    dataset_name text
);

create or replace function register_dataset(
    auth_id uuid,
    org_id uuid,
    project_name text,
    dataset_name text default 'logs')
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid := get_or_create_project(auth_id, project_name, org_id);
    _dataset_id uuid;
begin
    select insert_dataset(
        user_id => _user_id,
        project_id => _project_id,
        name => dataset_name,
        description => null)
    into _dataset_id;

    if _dataset_id is null then
        -- Duplicate key.
        select id
        from active_datasets datasets
        where
            datasets.project_id = _project_id
            and datasets.name = register_dataset.dataset_name
        into _dataset_id
        ;
        if not found then
            raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                dataset_name, _project_id;
        end if;
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$$;
REVOKE EXECUTE ON FUNCTION register_dataset FROM PUBLIC;

drop function if exists insert_dataset(
    project_id uuid,
    name text,
    description text);

create or replace function insert_dataset(
    user_id uuid,
    project_id uuid,
    name text,
    description text)
returns uuid
language plpgsql
security definer
as $$
declare
    _forbid_insert_datasets boolean;
    _dataset_id uuid;
begin
    -- Permission check for this org. Default is to permit insertion.
    select resources.forbid_insert_datasets
    from projects join resources using (org_id)
    where projects.id = project_id
    into _forbid_insert_datasets
    ;

    if _forbid_insert_datasets then
        raise exception 'User does not have permission to insert datasets';
    end if;

    insert into datasets(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _dataset_id
    ;

    return _dataset_id;
end;
$$;
REVOKE EXECUTE ON FUNCTION insert_dataset FROM PUBLIC;


drop function if exists insert_prompt_session(
    org_id uuid,
    name text,
    description text);

create or replace function insert_prompt_session(
    user_id uuid,
    org_id uuid,
    name text,
    description text)
returns uuid
language plpgsql
security definer
as $$
declare
    _forbid_insert_prompt_sessions boolean;
    _prompt_session_id uuid;
begin
    -- Permission check for this org. Default is to permit insertion.
    select resources.forbid_insert_prompt_sessions
    from resources
    where resources.org_id = insert_prompt_session.org_id
    into _forbid_insert_prompt_sessions
    ;

    if _forbid_insert_prompt_sessions then
        raise exception 'User does not have permission to insert prompt_sessions';
    end if;

    insert into prompt_sessions(org_id, name, description, user_id)
    values (org_id, name, description, user_id)
    on conflict do nothing
    returning id into _prompt_session_id
    ;

    return _prompt_session_id;
end;
$$;

REVOKE EXECUTE ON FUNCTION insert_prompt_session FROM PUBLIC;


drop function if exists register_prompt_session(
    org_name text,
    session_name text);

create or replace function register_prompt_session(
    auth_id uuid,
    org_name text,
    session_name text)
returns jsonb
language plpgsql
security definer
as $$
declare
  _org_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;

    select insert_prompt_session(
            user_id => get_user_id_by_auth_id(auth_id),
            org_id => _org_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select id
        from active_prompt_sessions prompt_sessions
        where
            org_id = _org_id
            and name = session_name
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$$;
REVOKE EXECUTE ON FUNCTION register_prompt_session FROM PUBLIC;

drop function if exists register_org(org_name text);
create or replace function register_org(auth_id uuid, org_name text)
returns uuid
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(register_org.auth_id);
    _org_id uuid;
begin
    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    insert into members(user_id, org_id)
    values (_user_id, _org_id);

    perform insert_resource_definition(_org_id, determine_resource_tier(_user_id));

    return _org_id;
end;
$$;

REVOKE EXECUTE ON FUNCTION register_org FROM PUBLIC;
