drop function if exists insert_experiment(
    user_id uuid,
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version bigint,
    public boolean
);

drop function if exists register_experiment(
    auth_id uuid,
    project_name text,
    org_id uuid,
    experiment_name text,
    description text,
    update boolean,
    repo_info jsonb,
    base_experiment text,
    ancestor_commits text[],
    dataset_id uuid,
    dataset_version bigint,
    public boolean
);

drop function if exists load_experiment(
    org_name text,
    project_name text,
    experiment_name text
);

drop view if exists load_experiment_view;

alter table experiments add column "metadata" jsonb;

set check_function_bodies = off;

create or replace function insert_experiment(
    user_id uuid,
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version bigint,
    public boolean,
    metadata jsonb
)
returns uuid
language plpgsql
security definer
as $$
declare
    _experiment_id uuid;
begin
    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, public, user_id, metadata)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, public, user_id, metadata)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$$;
REVOKE EXECUTE ON FUNCTION insert_experiment FROM PUBLIC;

create or replace function register_experiment(
    auth_id uuid,
    project_name text,
    org_id uuid,
    experiment_name text default null,
    description text default null,
    update boolean default false,
    repo_info jsonb default '{}',
    base_experiment text default null,
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version bigint default null,
    public boolean default false,
    metadata jsonb default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid := get_or_create_project(auth_id, project_name, org_id);
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Get the base experiment id from a named experiment.
    if base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            id into _conflicting_experiment_id
        from
            active_experiments
        where
            project_id = _project_id
            and name = experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = register_experiment.repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id,
            metadata = register_experiment.metadata
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                repo_info, _base_exp_id, dataset_id, dataset_version,
                public, metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    repo_info, _base_exp_id, dataset_id, dataset_version,
                    public, metadata)
                into _inserted_experiment_id;
            end if;
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$$;
REVOKE EXECUTE ON FUNCTION register_experiment FROM PUBLIC;


create or replace view active_experiments with (security_invoker=on) as
select
    experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.public,
    experiments.user_id,
    experiments.metadata
   FROM (experiments
     JOIN active_projects ON ((active_projects.id = experiments.project_id)))
  WHERE (experiments.deleted_at IS NULL);


create or replace view visible_experiments with (security_invoker=on) as
select
    projects.name as project_name,
    organizations.id AS org_id,
    organizations.name AS org_name,
    experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.public,
    experiments.user_id,
    experiments.metadata
   FROM ((active_experiments experiments
     JOIN active_projects projects ON ((projects.id = experiments.project_id)))
     JOIN organizations ON ((organizations.id = projects.org_id)))
  WHERE ((experiments.public = true) OR (EXISTS ( SELECT 1
           FROM (members
             JOIN users ON ((users.id = members.user_id)))
          WHERE ((users.auth_id = auth.user_id()) AND (members.org_id = organizations.id)))));


create or replace view load_experiment_view with (security_invoker=on) as
select
    v1.project_name,
    v1.org_id,
    v1.org_name,
    v1.id,
    v1.project_id,
    v1.name,
    v1.description,
    v1.created,
    v1.repo_info,
    v1.commit,
    v1.base_exp_id,
    v1.deleted_at,
    v1.dataset_id,
    v1.dataset_version,
    v1.public,
    v1.user_id,
    v1.metadata,
    ( SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name, 'created', v2.created)) AS jsonb_agg
           FROM visible_experiments v2
          WHERE ((v1.project_id = v2.project_id) AND (v1.id <> v2.id))) AS comparables
   FROM visible_experiments v1;
