create OR REPLACE view
  public.experiment_conflicts
WITH (security_invoker = on)
AS
select
  e1.name,
  e1.project_id,
  (
    select
      experiments.name
    from
      experiments
    where
      experiments.name ~~ concat(regexp_replace(e1.name, '-\d+$', ''), '%')
      and experiments.name ~ '-\d+$'
      and e1.project_id = experiments.project_id
    order by
      lpad(substring(experiments.name from '-(\d+)$'), 10)
       desc
    limit
      1
  ) as highest_conflict
from
  experiments e1;
