CREATE OR R<PERSON>LACE FUNCTION check_org_name_conflict(
    org_name text
)
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
AS $$
    select
    EXISTS (
        SELECT 1
        FROM organizations
        WHERE
            organizations.name = org_name
    )
END;
$$;

GRANT EXECUTE ON FUNCTION public.check_org_name_conflict(text) TO anon;

CREATE OR REPLACE FUNCTION register_org(
    org_name text
)
RETURNS UUID
LANGUAGE PLPGSQL
SECURITY DEFINER
AS $$
DECLARE
    _org_id uuid;
BEGIN
    IF (auth.user_id() IS NULL) THEN
        RAISE EXCEPTION 'Must be logged in to register an organization';
    END IF;

    INSERT INTO
        organizations(name)
    VALUES
        (org_name)
    RETURNING
        id
    INTO
        _org_id
    ;

    -- Free plan
    INSERT INTO
        resources(org_id, forbid_toggle_experiment_public_to_private, num_private_experiment_row_actions)
    VALUES
        (_org_id, true, row(7, 1000));

    INSERT INTO
        members(user_id, org_id)
    SELECT id, _org_id
    FROM users
    WHERE auth_id = auth.user_id();

    return _org_id;
END;
$$;

GRANT EXECUTE ON FUNCTION register_org(text) TO anon;
