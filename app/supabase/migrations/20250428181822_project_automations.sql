create table project_automations (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    created timestamp with time zone default current_timestamp,

    name text not null,
    description text,
    config jsonb not null
);

create unique index project_automations_project_id_name_idx on project_automations(project_id, name);

alter table project_automations enable row level security;

create or replace function register_project_automation(
    auth_id uuid,
    project_id uuid,
    project_automation_name text,
    description text,
    config jsonb,
    update boolean)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_automation_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_automations(
        project_id,
        user_id,
        name,
        description,
        config
    )
    values (
        project_id,
        _user_id,
        project_automation_name,
        description,
        config
    )
    on conflict do nothing
    returning id into _project_automation_id
    ;

    if _project_automation_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_automations
      where
          project_automations.project_id = register_project_automation.project_id
          and name = register_project_automation.project_automation_name
      into _project_automation_id
      ;
      if not found then
          raise exception 'Project automation % likely deleted concurrently with creation', project_automation_name;
      end if;

      if update then
        update project_automations
        set
            user_id = _user_id,
            name = register_project_automation.project_automation_name,
            description = register_project_automation.description,
            config = register_project_automation.config
        where id = _project_automation_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_automation', project_automations, 'found_existing', _found_existing)
        from project_automations
        where project_automations.id = _project_automation_id
    );
end;
$$;

revoke execute on function register_project_automation from public, anon;
