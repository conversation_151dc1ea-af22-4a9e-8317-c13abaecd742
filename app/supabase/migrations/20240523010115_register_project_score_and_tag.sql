CREATE OR REPLACE FUNCTION public.register_project_score(auth_id uuid, project_id uuid, project_score_name text, description text, score_type score_type, categories jsonb, update boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_score_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project_score', project_id, 'create', _user_id) then
        raise exception 'User does not have permissions to create a project score within this project';
    end if;

    insert into project_scores(project_id, user_id, name, description, score_type, categories)
    values (project_id, _user_id, project_score_name, description, score_type, categories)
    on conflict do nothing
    returning id into _project_score_id
    ;

    if _project_score_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_scores
      where
          project_scores.project_id = register_project_score.project_id
          and name = register_project_score.project_score_name
      into _project_score_id
      ;
      if not found then
          raise exception 'Project score % likely deleted concurrently with creation', project_score_name;
      end if;

      if update then
        -- Check for update permissions on the project_score.
        if not has_under_project_acl('project_score', _project_score_id, project_id, 'update', _user_id) then
            raise exception 'User does not have permission to update project_score';
        end if;

        update project_scores
        set
            user_id = _user_id,
            name = register_project_score.project_score_name,
            description = register_project_score.description,
            score_type = register_project_score.score_type,
            categories = register_project_score.categories
        where id = _project_score_id;
      end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project_score', _object_id => _project_score_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project_score', project_scores, 'found_existing', _found_existing)
        from project_scores
        where project_scores.id = _project_score_id
    );
end;
$function$
;

revoke execute on function register_project_score from public, anon;

CREATE OR REPLACE FUNCTION public.register_project_tag(auth_id uuid, project_id uuid, project_tag_name text, description text, color text, update boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_tag_id uuid;
    _found_existing boolean = false;
begin
    if not has_project_acl('project_tag', project_id, 'create', _user_id) then
        raise exception 'User does not have permissions to create a project tag within this project';
    end if;

    insert into project_tags(project_id, user_id, name, description, color)
    values (project_id, _user_id, project_tag_name, description, color)
    on conflict do nothing
    returning id into _project_tag_id
    ;

    if _project_tag_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_tags
      where
          project_tags.project_id = register_project_tag.project_id
          and name = register_project_tag.project_tag_name
      into _project_tag_id
      ;
      if not found then
          raise exception 'Project tag % likely deleted concurrently with creation', project_tag_name;
      end if;

      if update then
        -- Check for update permissions on the project_tag.
        if not has_under_project_acl('project_tag', _project_tag_id, project_id, 'update', _user_id) then
            raise exception 'User does not have permission to update project_tag';
        end if;

        update project_tags
        set
            user_id = _user_id,
            name = register_project_tag.project_tag_name,
            description = register_project_tag.description,
            color = register_project_tag.color
        where id = _project_tag_id;
      end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project_tag', _object_id => _project_tag_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project_tag', project_tags, 'found_existing', _found_existing)
        from project_tags
        where project_tags.id = _project_tag_id
    );
end;
$function$
;

revoke execute on function register_project_tag from public, anon;
