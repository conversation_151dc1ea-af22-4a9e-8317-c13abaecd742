-- Revise the DB functions.

drop trigger if exists "add_new_member_to_org_owners_trigger" on "public"."members";

drop trigger if exists "remove_old_member_from_org_owners_trigger" on "public"."members";

drop trigger if exists "create_org_owners_new_org_trigger" on "public"."organizations";

drop function if exists "public"."add_member_to_org_owners"(_org_id uuid, _user_id uuid);

drop function if exists "public"."add_member_to_org_owners_trigger_f"();

drop function if exists "public"."create_org_owners"(_org_id uuid);

drop function if exists "public"."create_org_owners_trigger_f"();

drop function if exists "public"."get_org_owners_group_id"(_org_id uuid);

drop function if exists "public"."remove_member_from_org_owners"(_org_id uuid, _user_id uuid);

drop function if exists "public"."remove_member_from_org_owners_trigger_f"();

CREATE OR REPLACE FUNCTION public.add_member_to_org_unchecked(user_to_add_id uuid, organization_id uuid, initial_group_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _added_user_id uuid;
begin
    insert into members(org_id, user_id)
    values (organization_id, user_to_add_id)
    on conflict do nothing
    returning user_id into _added_user_id;

    if initial_group_id is not null then
        insert into group_users(group_id, user_id)
        values (initial_group_id, user_to_add_id)
        on conflict do nothing;
    end if;

    return _added_user_id;
end;
$function$
;

revoke execute on function add_member_to_org_unchecked from anon;

CREATE OR REPLACE FUNCTION public.create_system_groups(_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Owners
    insert into groups(org_id, name)
    values (_org_id, 'Owners')
    on conflict do nothing;
    perform register_acl_unchecked(
        _object_type => 'organization', _object_id => _org_id,
        _group_id => get_group_id(_org_id, 'Owners'), _role_id => get_system_role_id('Owner'));

    -- Engineers
    insert into groups(org_id, name)
    values (_org_id, 'Engineers')
    on conflict do nothing;
    perform register_acl_unchecked(
        _object_type => 'org_project', _object_id => _org_id,
        _group_id => get_group_id(_org_id, 'Engineers'), _role_id => get_system_role_id('Engineer'));

    -- Viewers
    insert into groups(org_id, name)
    values (_org_id, 'Viewers')
    on conflict do nothing;
    perform register_acl_unchecked(
        _object_type => 'org_project', _object_id => _org_id,
        _group_id => get_group_id(_org_id, 'Viewers'), _role_id => get_system_role_id('Viewer'));
end;
$function$
;

revoke execute on function create_system_groups from public, anon;

CREATE OR REPLACE FUNCTION public.create_system_groups_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
begin
    perform create_system_groups(new.id);
    return new;
end;
$function$
;

revoke execute on function create_system_groups_trigger_f from public, anon;

CREATE OR REPLACE FUNCTION public.get_group_id(_org_id uuid, _group_name text)
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
    select id from groups where org_id = _org_id and name = _group_name and deleted_at is null;
$function$
;

revoke execute on function get_group_id from public, anon;

CREATE OR REPLACE FUNCTION public.get_system_role_id(_role_name text)
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
    select id from roles where org_id is null and name = _role_name and deleted_at is null;
$function$
;

revoke execute on function get_system_role_id from public, anon;

CREATE OR REPLACE FUNCTION public.get_owner_role_id()
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
    select get_system_role_id('Owner')
$function$
;

CREATE OR REPLACE FUNCTION public.register_org(auth_id uuid, org_name text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid := get_user_id_by_auth_id(register_org.auth_id);
    _org_id uuid;
begin
    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    perform add_member_to_org_unchecked(_user_id, _org_id, get_group_id(_org_id, 'Owners'));
    perform insert_resource_definition(_org_id, determine_resource_tier(_user_id));
    return _org_id;
end;
$function$
;

CREATE TRIGGER create_system_groups_new_org_trigger AFTER INSERT ON public.organizations FOR EACH ROW EXECUTE FUNCTION create_system_groups_trigger_f();

-- Revise the system roles and groups. To minimize redundant ACL
-- re-computations, disable the triggers and do it all once at the end.

alter table groups disable trigger user;
alter table group_users disable trigger user;
alter table group_inheritors disable trigger user;
alter table roles disable trigger user;
alter table role_permissions disable trigger user;
alter table role_inheritors disable trigger user;
alter table acls disable trigger user;
-- But we still need the 'upsert_acls_trigger' for inserting new ACLs.
alter table acls enable trigger "upsert_acls_trigger";

-- Role modifications.

-- Remove the 'editor' and 'admin' roles.
delete from role_inheritors
where inheritor_role_id in (
    '44bac3ec-5c62-478b-bffd-4cb6db6370c4',
    'cd994cdd-4697-4c18-82c6-022133135199',
    '2f98db42-ae10-426b-8a58-8060732d5cbd',
    '7c1a37d9-00ac-4e61-b19a-499f4f4b30df'
);
delete from role_permissions
where role_id in ('cd994cdd-4697-4c18-82c6-022133135199', '2f98db42-ae10-426b-8a58-8060732d5cbd');
delete from roles
where id in ('cd994cdd-4697-4c18-82c6-022133135199', '2f98db42-ae10-426b-8a58-8060732d5cbd');

-- Rename 'viewer' and 'owner' to 'Viewer' and 'Owner'.
update roles set name='Viewer' where name='viewer';
update roles set name='Owner' where name='owner';

-- Add all the permissions to 'Owner'.
insert into role_permissions(role_id, permission) values
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'create'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'read'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'update'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'delete'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'create_acls'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'read_acls'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'update_acls'),
   ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'delete_acls')
;

-- Create an 'Engineer' role.
insert into roles(id, name) values ('7b1a111f-ae54-4c44-920f-fdf37eda8fb8', 'Engineer');
insert into role_permissions(role_id, permission) values
   ('7b1a111f-ae54-4c44-920f-fdf37eda8fb8', 'create'),
   ('7b1a111f-ae54-4c44-920f-fdf37eda8fb8', 'read'),
   ('7b1a111f-ae54-4c44-920f-fdf37eda8fb8', 'update'),
   ('7b1a111f-ae54-4c44-920f-fdf37eda8fb8', 'delete')
;

-- Set a description for each system role.
update roles
    set description = 'Unrestricted access to the organization, its data, and its settings. Can add, modify, and delete projects and all other resources. Can invite and remove members and can manage group membership.'
    where id = get_system_role_id('Owner')
;
update roles
    set description = 'Can access, create, update, and delete projects and all resources within projects. Cannot invite or remove members or manage access to resources.'
    where id = get_system_role_id('Engineer')
;
update roles
    set description = 'Can access projects and all resources within projects. Cannot create, update, or delete any resources. Cannot invite or remove members or manage access to resources.'
    where id = get_system_role_id('Viewer')
;

-- Group modifications.

-- Rename all the groups named 'org-owners' to 'Owners'.
update groups set name='Owners' where name='org-owners';

-- Re-create all system groups for all orgs.
select create_system_groups(id) from organizations;

alter table groups enable trigger user;
alter table group_users enable trigger user;
alter table group_inheritors enable trigger user;
alter table roles enable trigger user;
alter table role_permissions enable trigger user;
alter table role_inheritors enable trigger user;
alter table acls enable trigger user;

select refresh_expanded_acls_by_org(null);
