create table "public"."brainstore_licenses" (
    "id" uuid not null default uuid_generate_v4(),
    "org_id" uuid not null,
    "created" timestamp with time zone default CURRENT_TIMESTAMP,
    "license" text not null
);


alter table "public"."brainstore_licenses" enable row level security;

CREATE UNIQUE INDEX brainstore_licenses_license_idx ON public.brainstore_licenses USING btree (license);

CREATE UNIQUE INDEX brainstore_licenses_pkey ON public.brainstore_licenses USING btree (id);

alter table "public"."brainstore_licenses" add constraint "brainstore_licenses_pkey" PRIMARY KEY using index "brainstore_licenses_pkey";

alter table "public"."brainstore_licenses" add constraint "brainstore_licenses_org_id_fkey" FOREIGN KEY (org_id) REFERENCES organizations(id) not valid;

alter table "public"."brainstore_licenses" validate constraint "brainstore_licenses_org_id_fkey";
