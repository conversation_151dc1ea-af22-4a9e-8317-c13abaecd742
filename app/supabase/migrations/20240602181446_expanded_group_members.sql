drop function if exists "public"."refresh_expanded_acls_by_object"(_object_type acl_object_type, _object_id uuid);

drop function if exists "public"."refresh_expanded_acls_by_org"(_arg_org_id uuid);

drop function if exists "public"."refresh_expanded_group_users_by_org"(_arg_org_id uuid);

alter table "public"."_expanded_group_users" drop constraint "_expanded_group_users_pkey";

drop index if exists "public"."_expanded_acls__object_org_id_idx";

drop index if exists "public"."_expanded_acls_acl_id_object_type_object_id_user_id_permiss_idx";

drop index if exists "public"."_expanded_acls_object_type_object_id_user_id_permission_res_idx";

drop index if exists "public"."_expanded_group_users__group_org_id_idx";

drop index if exists "public"."_expanded_group_users_pkey";

drop table "public"."_expanded_acls";

drop table "public"."_expanded_group_users";

create table "public"."_expanded_acls" (
    "acl_id" uuid not null,
    "object_type" acl_object_type not null,
    "object_id" uuid not null,
    "user_object_type" acl_user_object_type not null,
    "user_group_id" uuid not null,
    "permission" permission_type not null,
    "restrict_object_type" acl_object_type,
    "_object_org_id" uuid not null
);


alter table "public"."_expanded_acls" enable row level security;

create table "public"."_expanded_group_members" (
    "group_id" uuid not null,
    "user_object_type" acl_user_object_type not null,
    "user_group_id" uuid not null,
    "_group_org_id" uuid not null
);


alter table "public"."_expanded_group_members" enable row level security;

CREATE INDEX _expanded_acls__object_org_id_idx ON public._expanded_acls USING btree (_object_org_id);

CREATE UNIQUE INDEX _expanded_acls_acl_id_object_type_object_id_user_object_ty_idx ON public._expanded_acls USING btree (acl_id, object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type) NULLS NOT DISTINCT;

CREATE INDEX _expanded_acls_object_type_object_id_user_object_type_user_idx ON public._expanded_acls USING btree (object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type);

CREATE INDEX _expanded_group_members__group_org_id_idx ON public._expanded_group_members USING btree (_group_org_id);

CREATE UNIQUE INDEX _expanded_group_members_pkey ON public._expanded_group_members USING btree (group_id, user_object_type, user_group_id);

alter table "public"."_expanded_group_members" add constraint "_expanded_group_members_pkey" PRIMARY KEY using index "_expanded_group_members_pkey";

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_object(_object_type acl_object_type, _object_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    delete from _expanded_acls
    where object_type = _object_type and object_id = _object_id;

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "object_type = _object_type and object_id = _object_id"
    with
    candidate_acls as (
        select *
        from acls
        where object_type = _object_type and object_id = _object_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_members.user_object_type expanded_user_object_type,
            _expanded_group_members.user_group_id expanded_user_group_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_members using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(expanded_user_object_type, user_object_type) as user_object_type,
        coalesce(expanded_user_group_id, user_id) as user_group_id,
        coalesce(expanded_permission, permission) as permission,
        coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_object_type is not null
            and user_group_id is not null
            and permission is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_acls
    where _arg_org_id is null or _arg_org_id = _object_org_id;

    -- Rebuild the set of acls, either across the entire table, or scoped to the
    -- acls in _arg_org_id.
    --
    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "_arg_org_id is null or _arg_org_id = _object_org_id"
    with
    candidate_acls as (
        select *
        from acls
        where _arg_org_id is null or _arg_org_id = _object_org_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_members.user_object_type expanded_user_object_type,
            _expanded_group_members.user_group_id expanded_user_group_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_members using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(expanded_user_object_type, user_object_type) as user_object_type,
        coalesce(expanded_user_group_id, user_id) as user_group_id,
        coalesce(expanded_permission, permission) as permission,
        coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_object_type is not null
            and user_group_id is not null
            and permission is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_group_members_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to the org_id. If org_id is
    -- null, delete everything.
    delete from _expanded_group_members
    where _arg_org_id is null or _group_org_id = _arg_org_id;

    -- Rebuild the set of group members, either across the entire table, or
    -- scoped to the groups in the given organization.
    with recursive t(group_id, user_object_type, user_group_id, _group_org_id) as (
        (
            select group_users.group_id, 'user'::acl_user_object_type, group_users.user_id, group_users._group_org_id
            from group_users join groups on group_users.group_id = groups.id
            where
                (_arg_org_id is null or _group_org_id = _arg_org_id)
                and groups.deleted_at is null
            union all
            select groups.id, 'group'::acl_user_object_type, groups.id, groups.org_id
            from groups
            where
                (_arg_org_id is null or org_id = _arg_org_id)
                and groups.deleted_at is null
        )
        union
        select group_inheritors.inheritor_group_id, t.user_object_type, t.user_group_id, t._group_org_id
        from
            t
            join group_inheritors using (group_id)
            join groups on group_inheritors.inheritor_group_id = groups.id
        where groups.deleted_at is null
    )
    -- Note that for the 'user'-type expansions, we want to emit the set of
    -- groups which contain the users in the base-case set. But for the
    -- 'group'-type expansions, we want to emit the set of groups inheriting
    -- from each initial group in the base-case set. So we need to flip the
    -- ordering of the fields for the 'group'-type expansions.
    insert into _expanded_group_members (
        select * from t where user_object_type = 'user'
        union all
        select user_group_id, user_object_type, group_id, _group_org_id
        from t where user_object_type = 'group'
    );

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$function$
;

CREATE OR REPLACE FUNCTION public.has_organization_acl(_object_type acl_object_type, _org_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from _expanded_acls
    where (
        object_type = 'organization'
        and object_id = _org_id
        and user_object_type = 'user'
        and user_group_id = _user_id
        and permission = _permission
        and (restrict_object_type is null or restrict_object_type = _object_type)
    )
);
$function$
;

CREATE OR REPLACE FUNCTION public.has_project_acl(_object_type acl_object_type, _project_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'org_project'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$function$
;

CREATE OR REPLACE FUNCTION public.has_under_organization_acl(_object_type acl_object_type, _object_id uuid, _org_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from _expanded_acls
    where (
        (
            object_type = _object_type
            and object_id = _object_id
            and user_object_type = 'user'
            and user_group_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
        or (
            object_type = 'organization'
            and object_id = _org_id
            and user_object_type = 'user'
            and user_group_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
    )
);
$function$
;

CREATE OR REPLACE FUNCTION public.has_under_project_acl(_object_type acl_object_type, _object_id uuid, _project_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = _object_type
                    and object_id = _object_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'org_project'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_object_type = 'user'
                    and user_group_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_role_permissions_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_role_permissions
    where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles in the given organization.
    --
    -- When building the set of updated role grants, we always include the
    -- system roles to start with, since they can be included in any role.
    --
    -- When updating the grants, we include everything if _arg_org_id is null,
    -- because system grants can change everything (and we would have wiped the
    -- whole table in the delete statement above). Otherwise, we only include
    -- the organization-scoped grants, since organization-scoped roles cannot
    -- affect different organizations or system roles.
    with recursive t(role_id, permission, _role_org_id, restrict_object_type) as (
        select role_permissions.role_id, role_permissions.permission,
               roles.org_id _role_org_id, role_permissions.restrict_object_type
        from role_permissions join roles on role_permissions.role_id = roles.id
        where
            (_arg_org_id is null or org_id is null or org_id = _arg_org_id)
            and roles.deleted_at is null
        union
        -- When system roles propagate into organization-scoped roles, we need
        -- to pick the organization-scoped role's org_id for the final entry.
        select
            role_inheritors.inheritor_role_id role_id,
            t.permission,
            coalesce(t._role_org_id, roles.org_id) _role_org_id,
            t.restrict_object_type
        from
            t
            join role_inheritors on t.role_id = role_inheritors.role_id
            join roles on role_inheritors.inheritor_role_id = roles.id
        where roles.deleted_at is null
    )
    insert into _expanded_role_permissions
    select * from t where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_acl_unchecked(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _group_id uuid DEFAULT NULL::uuid, _permission permission_type DEFAULT NULL::permission_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _acl_id uuid;
    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;

    _ret_obj jsonb;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    insert into acls(
        object_type, object_id, user_object_type, user_id, group_id,
        grant_object_type, permission, role_id, restrict_object_type)
    values (
        _object_type, _object_id, _user_object_type, _user_id, _group_id,
        _grant_object_type, _permission, _role_id, _restrict_object_type)
    on conflict do nothing
    returning id into _acl_id;

    if _acl_id is null then
        -- Duplicate key. Just find the ID.
        select find_acl_id(
            _object_type, _object_id, _user_id, _group_id, _permission, _role_id,
            _restrict_object_type) into _acl_id
        ;
        if not found then
            raise exception 'Acl likely deleted concurrently with creation';
        end if;
    else
        -- It was a new ACL. Insert the expanded acls directly in this
        -- operation, rather than taking a lock and recomputing them from
        -- scratch.
        --
        -- Query generated from the following command:
        --
        -- ./scripts/make_expanded_acls_query.py "id = _acl_id"
        with
        candidate_acls as (
            select *
            from acls
            where id = _acl_id
        ),
        joined_acls as (
            select
                candidate_acls.*,
                _expanded_group_members.user_object_type expanded_user_object_type,
                _expanded_group_members.user_group_id expanded_user_group_id,
                _expanded_role_permissions.permission expanded_permission,
                _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
            from
                candidate_acls
                    left join _expanded_group_members using (group_id)
                    left join _expanded_role_permissions using (role_id)
        ),
        coalesced_acls as (
        select
            id acl_id,
            object_type,
            object_id,
            coalesce(expanded_user_object_type, user_object_type) as user_object_type,
            coalesce(expanded_user_group_id, user_id) as user_group_id,
            coalesce(expanded_permission, permission) as permission,
            coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
            _object_org_id
        from
            joined_acls
        ),
        filtered_acls as (
            select * from coalesced_acls
            where
                -- It is possible that the user specifies an empty group or role, in
                -- which case we don't need to include these entries in the expanded
                -- ACLs.
                user_object_type is not null
                and user_group_id is not null
                and permission is not null
        )
        insert into _expanded_acls select * from filtered_acls on conflict do nothing;
    end if;

    return (
        select jsonb_build_object('acl', acls)
        from acls where id = _acl_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_acls_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct object_type, object_id
        from transition_tbl
    loop
        perform refresh_expanded_acls_by_object(
            object_rec.object_type, object_rec.object_id);
    end loop;
    return null;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_group_members_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct groups.org_id
        from transition_tbl join groups on transition_tbl.group_id = groups.id
    loop
        perform refresh_expanded_group_members_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_groups_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.org_id
        from transition_tbl
    loop
        perform refresh_expanded_group_members_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$function$
;

revoke execute on function "public"."refresh_expanded_acls_by_object" from public, anon;
revoke execute on function "public"."refresh_expanded_acls_by_org" from public, anon;
revoke execute on function "public"."refresh_expanded_group_members_by_org" from public, anon;

select refresh_expanded_group_members_by_org(null);

drop index if exists "public"."_expanded_acls_acl_id_object_type_object_id_user_object_ty_idx";

drop index if exists "public"."_expanded_acls_object_type_object_id_user_object_type_user_idx";

CREATE UNIQUE INDEX _expanded_acls_acl_id_object_type_object_id_user_object_typ_idx ON public._expanded_acls USING btree (acl_id, object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type) NULLS NOT DISTINCT;

CREATE INDEX _expanded_acls_object_type_object_id_user_object_type_user__idx ON public._expanded_acls USING btree (object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type);

CREATE TRIGGER insert_groups_trigger AFTER INSERT ON public.groups REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_groups_trigger_f();
