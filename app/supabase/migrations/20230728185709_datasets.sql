create table datasets (
    id uuid not null primary key DEFAULT uuid_generate_v4(),
    project_id uuid references projects,
    name text not null,
    description text,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone
);
alter table datasets enable row level security;
CREATE INDEX ON datasets (project_id);
CREATE UNIQUE INDEX on datasets (project_id, name);

CREATE OR REPLACE VIEW active_datasets WITH (security_invoker = on) AS
SELECT *
FROM datasets
WHERE deleted_at IS NULL;

create policy "Org members can see all org datasets."
  on datasets
  for all using (
    auth.user_id() in (
      select auth_id from
      projects
      JOIN organizations on projects.org_id = organizations.id
      JOIN members ON organizations.id = members.org_id
      JOIN users ON members.user_id = users.id
      WHERE projects.id = datasets.project_id
    )
  );

-- I noticed these were nullable. They should not be!
ALTER TABLE experiments ALTER COLUMN name SET not null;
ALTER TABLE projects ALTER COLUMN name SET not null;


CREATE OR REPLACE FUNCTION register_dataset(org_id uuid, project_name text, dataset_name text)
R<PERSON>URNS JSONB
SECURITY INVOKER
AS $$
DECLARE
    _project_id uuid;
    _dataset_id uuid;
BEGIN
  -- https://stackoverflow.com/questions/35265453/use-insert-on-conflict-do-nothing-returning-failed-rows
  with inserted (id) as (
    INSERT INTO projects (org_id, name)
    VALUES (register_dataset.org_id, register_dataset.project_name)
    ON CONFLICT DO NOTHING
    returning id
  )
  SELECT id INTO _project_id FROM (
    select projects.id AS id
    from projects WHERE projects.org_id = register_dataset.org_id and projects.name = register_dataset.project_name
    union all
    select id
    from inserted
  ) sub;

  with inserted (id) as (
    INSERT INTO datasets (project_id, name)
    VALUES (_project_id, register_dataset.dataset_name)
    ON CONFLICT DO NOTHING
    returning id
  )
  SELECT id INTO _dataset_id FROM (
    select datasets.id AS id
    from datasets WHERE datasets.project_id = _project_id and datasets.name = register_dataset.dataset_name
    union all
    select id
    from inserted
  ) sub;


  RETURN (
    SELECT jsonb_build_object('project', projects, 'dataset', datasets)  FROM
    projects JOIN datasets ON projects.id = datasets.project_id
    WHERE datasets.project_id = _project_id AND datasets.id = _dataset_id
  );

END;
$$ LANGUAGE plpgsql;
