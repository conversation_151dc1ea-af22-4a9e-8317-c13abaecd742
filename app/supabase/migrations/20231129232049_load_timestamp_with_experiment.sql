create or replace view "public"."load_experiment_view" with (security_invoker='on') as  SELECT v1.project_name,
    v1.org_id,
    v1.org_name,
    v1.id,
    v1.project_id,
    v1.name,
    v1.description,
    v1.created,
    v1.repo_info,
    v1.commit,
    v1.base_exp_id,
    v1.deleted_at,
    v1.dataset_id,
    v1.dataset_version,
    v1.public,
    v1.user_id,
    ( SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name, 'created', v2.created)) AS jsonb_agg
           FROM visible_experiments v2
          WHERE ((v1.project_id = v2.project_id) AND (v1.id <> v2.id))) AS comparables
   FROM visible_experiments v1;
