drop trigger if exists "acquire_acl_lock_group_inheritors_trigger" on "public"."group_inheritors";

drop trigger if exists "delete_group_inheritors_trigger" on "public"."group_inheritors";

drop trigger if exists "insert_group_inheritors_trigger" on "public"."group_inheritors";

drop trigger if exists "update_group_inheritors_new_trigger" on "public"."group_inheritors";

drop trigger if exists "update_group_inheritors_old_trigger" on "public"."group_inheritors";

drop trigger if exists "upsert_group_inheritors_trigger" on "public"."group_inheritors";

drop function if exists "public"."check_new_group_inheritor"();

drop function if exists "public"."refresh_expanded_group_members_by_org"(_arg_org_id uuid);

create table "public"."group_members" (
    "group_id" uuid not null,
    "member_group_id" uuid not null
);


alter table "public"."group_members" enable row level security;

CREATE INDEX acls_group_id_idx ON public.acls USING btree (group_id);

CREATE INDEX acls_role_id_idx ON public.acls USING btree (role_id);

CREATE INDEX group_members_member_group_id_group_id_idx ON public.group_members USING btree (member_group_id, group_id);

CREATE UNIQUE INDEX group_members_pkey ON public.group_members USING btree (group_id, member_group_id);

alter table "public"."group_members" add constraint "group_members_pkey" PRIMARY KEY using index "group_members_pkey";

alter table "public"."group_members" add constraint "group_members_group_id_fkey" FOREIGN KEY (group_id) REFERENCES groups(id) not valid;

alter table "public"."group_members" validate constraint "group_members_group_id_fkey";

alter table "public"."group_members" add constraint "group_members_member_group_id_fkey" FOREIGN KEY (member_group_id) REFERENCES groups(id) not valid;

alter table "public"."group_members" validate constraint "group_members_member_group_id_fkey";

CREATE INDEX _expanded_group_members_user_object_type_user_group_id_grou_idx ON public._expanded_group_members USING btree (user_object_type, user_group_id, group_id);

-- Backfill group_members.
insert into group_members(group_id, member_group_id)
select inheritor_group_id, group_id from group_inheritors;

CREATE OR REPLACE FUNCTION public.check_new_group_member()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    _group_org_id uuid;
    _member_group_org_id uuid;
begin
    select groups.org_id into _group_org_id
    from groups where groups.id = new.group_id;
    select groups.org_id into _member_group_org_id
    from groups where groups.id = new.member_group_id;

    if _group_org_id <> _member_group_org_id then
        raise exception 'Both groups must belong to the same organization';
    end if;

    return new;
end;
$function$
;

revoke execute on function check_new_group_member from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_group_members_by_group(_arg_group_id uuid)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
declare
    _out0 integer;
    _out1 integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of group members, either across the entire table, or
    -- scoped to the groups which '_arg_group_id' is a member of.
    with recursive
    groups_to_update as (
        select group_id from _expanded_group_members
        where (_arg_group_id is null or user_group_id = _arg_group_id) and user_object_type = 'group'
        union all
        select _arg_group_id group_id where _arg_group_id is not null
    ),
    t(group_id, user_object_type, user_group_id, _group_org_id) as (
        (
            select groups.id, 'group'::acl_user_object_type, groups.id, groups.org_id
            from groups join groups_to_update on groups_to_update.group_id = groups.id
            where groups.deleted_at is null
        )
        union
        select * from (
            -- Postgres is weird and doesn't allow the recursive term to appear
            -- more than once, but it is just a parser thing.
            with t_inner as (select * from t)
            select t_inner.group_id, 'user'::acl_user_object_type, group_users.user_id, t_inner._group_org_id
            from t_inner join group_users on t_inner.user_group_id = group_users.group_id
            where t_inner.user_object_type = 'group'
            union all
            select t_inner.group_id, 'group'::acl_user_object_type, group_members.member_group_id, t_inner._group_org_id
            from
                t_inner
                join group_members on t_inner.user_group_id = group_members.group_id
                join groups on group_members.member_group_id = groups.id
            where t_inner.user_object_type = 'group' and groups.deleted_at is null
        ) "x"
    ),
    final_result_set as (
        select * from t
    ),
    deleted_members as (
        delete from _expanded_group_members where
        _expanded_group_members.group_id in (select group_id from groups_to_update)
        and not exists (
            select 1 from final_result_set
            where
                _expanded_group_members.group_id = final_result_set.group_id
                and _expanded_group_members.user_object_type = final_result_set.user_object_type
                and _expanded_group_members.user_group_id = final_result_set.user_group_id
        )
        returning 1
    ),
    inserted_members as (
        insert into _expanded_group_members select * from final_result_set on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out0
    from
        (select count(*) cnt from deleted_members) "num_deleted"
        join (select count(*) cnt from inserted_members) "num_inserted"
        on true;

    -- Rebuild the set of expanded ACLs for all objects which have an ACL on
    -- this group or any group which inherits from it.
    with
    acl_groups_to_update as (
        select user_group_id group_id from _expanded_group_members
        where (_arg_group_id is null or group_id = _arg_group_id) and user_object_type = 'group'
        -- Note that we still need to explicitly include the input group in case
        -- it was deleted, so it's no longer present in _expanded_group_members,
        -- but we still need to update the ACLs which reference it.
        union all
        select _arg_group_id group_id where _arg_group_id is not null
    ),
    acl_objects_to_update as (
        select distinct acls.object_type, acls.object_id
        from acls join acl_groups_to_update on acls.group_id = acl_groups_to_update.group_id
    ),
    updated_acls as (
        select refresh_expanded_acls_by_object(object_type, object_id) ret
        from acl_objects_to_update
    )
    select sum(ret) into _out1 from updated_acls;

    return _out0 + _out1;
end;
$function$
;

revoke execute on function refresh_expanded_group_members_by_group from public, anon;

CREATE OR REPLACE FUNCTION public.register_group(auth_id uuid, org_id uuid, group_name text, description text DEFAULT NULL::text, member_users uuid[] DEFAULT NULL::uuid[], member_groups uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = group_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_groups uuid[] = coalesce(member_groups, '{}');
    _update boolean = coalesce(update, false);

    _group_id uuid;
begin
    -- Search for a matching existing group.
    select id into _group_id
    from groups where groups.org_id = _org_id and name = _name and deleted_at is null;

    if _group_id is not null then
        if _update then
          -- Replace the contents of the existing group, including users and
          -- inheritors.
          if not has_under_organization_acl('group', _group_id, _org_id, 'update', _user_id) then
              raise exception 'User does not have permissions to update group';
          end if;

          -- Update groups, users, and inheritors with our given contents.

          update groups
          set name = _name, description = _description
          where id = _group_id;

          delete from group_users where group_id = _group_id;
          insert into group_users(group_id, user_id)
          select _group_id, user_id from unnest(_member_users) user_id;

          delete from group_members where group_id = _group_id;
          insert into group_members(group_id, member_group_id)
          select _group_id, member_group_id from unnest(_member_groups) member_group_id;
        else
          -- They must have read permission on the group.
          if not has_under_organization_acl('group', _group_id, _org_id, 'read', _user_id) then
              raise exception 'User does not have permissions to read group';
          end if;
        end if;
    else
        if not has_organization_acl('group', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create group';
        end if;

        -- Add groups, users, and inheritors with our given contents.

        insert into groups(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _group_id;

        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        insert into group_members(group_id, member_group_id)
        select _group_id, member_group_id from unnest(_member_groups) member_group_id;

        perform register_acl_unchecked(
            _object_type => 'group', _object_id => _group_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/group/_constants.ts.
    return (
        select jsonb_build_object('group', t)
        from (
            select
                *,
                (select coalesce(array_agg(user_id)::uuid[], '{}') member_users from group_users where group_users.group_id = groups.id),
                (select coalesce(array_agg(member_group_id)::uuid[], '{}') member_groups from group_members join groups "_joined_groups" on group_members.member_group_id = "_joined_groups".id where "_joined_groups".deleted_at isnull and group_members.group_id = groups.id)
            from
                groups
            where
                groups.id = _group_id
        ) t
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_group_members_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.group_id
        from transition_tbl
    loop
        perform refresh_expanded_group_members_by_group(object_rec.group_id);
    end loop;
    return null;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_groups_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.id
        from transition_tbl
    loop
        perform refresh_expanded_group_members_by_group(object_rec.id);
    end loop;
    return null;
end;
$function$
;

CREATE TRIGGER acquire_acl_lock_group_members_trigger BEFORE INSERT OR DELETE OR UPDATE ON public.group_members FOR EACH STATEMENT EXECUTE FUNCTION acquire_acl_lock_trigger_f();

CREATE TRIGGER delete_group_members_trigger AFTER DELETE ON public.group_members REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER insert_group_members_trigger AFTER INSERT ON public.group_members REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER update_group_members_new_trigger AFTER UPDATE ON public.group_members REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER update_group_members_old_trigger AFTER UPDATE ON public.group_members REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER upsert_group_members_trigger BEFORE INSERT OR UPDATE ON public.group_members FOR EACH ROW EXECUTE FUNCTION check_new_group_member();
