-- 1. Create table
drop table if exists members cascade;
drop table if exists users cascade;
drop table if exists organizations cascade;
drop table if exists projects cascade;
drop table if exists experiments cascade;

create or replace function auth.user_id() returns uuid as $$
  select nullif(current_setting('request.jwt.claims', true)::json->>'userId', '')::uuid;
$$ language sql stable;


create table users (
  id uuid not null primary key DEFAULT uuid_generate_v4(),
  auth_id uuid UNIQUE,
  given_name text,
  family_name text,
  email text UNIQUE,
  avatar_url text,
  created timestamp with time zone default current_timestamp
);

alter table users enable row level security;

create policy "Users can view their profiles."
  on users for select using (
    auth.user_id() = auth_id
);

create table organizations (
  id uuid not null primary key DEFAULT uuid_generate_v4(),
  name text,
  api_url text,
  created timestamp with time zone default current_timestamp
);
alter table organizations enable row level security;

create table members (
  org_id uuid references organizations,
  user_id uuid references users,
  PRIMARY KEY (org_id, user_id)
);
CREATE INDEX ON members (user_id);

alter table members enable row level security;

create policy "Org members can see their own membership details."
  on members
  for select using (
    auth.user_id() in (
      select auth_id from users WHERE users.id = user_id
    )
  );

create policy "Org members can update org details if they belong to the team."
  on organizations
  for all using (
    auth.user_id() in (
      select auth_id from members JOIN users ON members.user_id = users.id WHERE members.org_id = organizations.id
    )
  );


create table projects (
    id uuid not null primary key DEFAULT uuid_generate_v4(),
    org_id uuid references organizations,
    name text,
    created timestamp with time zone default current_timestamp
);
alter table projects enable row level security;
CREATE INDEX ON projects (org_id);
CREATE UNIQUE INDEX ON projects (org_id, name);

create table experiments (
    id uuid not null primary key DEFAULT uuid_generate_v4(),
    project_id uuid references projects,
    name text,
    description text,
    created timestamp with time zone default current_timestamp
);
alter table experiments enable row level security;
CREATE INDEX ON experiments (project_id);
CREATE UNIQUE INDEX on experiments (project_id, name);

create policy "Org members can see all org projects."
  on projects
  for all using (
    auth.user_id() in (
      select auth_id from
      organizations
      JOIN members ON organizations.id = members.org_id
      JOIN users ON members.user_id = users.id
      WHERE organizations.id = projects.org_id
    )
  );

create policy "Org members can see all org experiments."
  on experiments
  for all using (
    auth.user_id() in (
      select auth_id from
      projects
      JOIN organizations on projects.org_id = organizations.id
      JOIN members ON organizations.id = members.org_id
      JOIN users ON members.user_id = users.id
      WHERE projects.id = experiments.project_id
    )
  );

CREATE OR REPLACE VIEW me AS SELECT id, auth_id, json_agg((SELECT org_id FROM members WHERE user_id=id)) AS organizations FROM users WHERE auth_id=auth.user_id() or true GROUP BY 1;

create table if not exists tokens (
  id uuid not null primary key DEFAULT uuid_generate_v4(),
  created timestamp with time zone default current_timestamp,
  jwt text not null
);

CREATE INDEX ON tokens (created);
CREATE UNIQUE INDEX ON tokens (jwt);

-- 2. Enable RLS
alter table tokens enable row level security;
