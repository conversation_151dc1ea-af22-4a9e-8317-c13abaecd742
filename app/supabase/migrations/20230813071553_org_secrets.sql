-- Following
-- https://supabase.com/docs/guides/database/vault
-- https://supabase.com/blog/transparent-column-encryption-with-postgres#one-key-id-per-row-with-associated-data

CREATE TABLE org_secrets(
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  created TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  key_id uuid not null DEFAULT (pgsodium.create_key()).id,
  nonce bytea default pgsodium.crypto_aead_det_noncegen(),
  org_id uuid NOT NULL references organizations,
  name text NOT NULL,
  secret text
);

CREATE UNIQUE INDEX ON org_secrets (org_id, name);

-- ASSOCIATED means that if org_id or name are changed, then the key is unrecoverable
security label for pgsodium
 on column org_secrets.secret
 is 'ENCRYPT WITH KEY COLUMN key_id ASSOCIATED (org_id, name) NONCE nonce';

alter table org_secrets enable row level security;

CREATE VIEW preview_org_secrets AS
    SELECT org_secrets.id, org_secrets.created, org_secrets.org_id, org_secrets.name, '********' as preview_secret
    FROM org_secrets
    JOIN organizations ON org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id();

CREATE OR REPLACE FUNCTION lookup_org_secret(name text)
 RETURNS text
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  SELECT decrypted_secret FROM decrypted_org_secrets
    JOIN organizations ON decrypted_org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE
      decrypted_org_secrets.name = lookup_org_secret.name
      AND users.auth_id = auth.user_id()
$function$
;

CREATE OR REPLACE FUNCTION set_org_secret(name text, value text)
 RETURNS uuid
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  INSERT INTO org_secrets (org_id, name, secret)
  SELECT organizations.id, set_org_secret.name, set_org_secret.value
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
  ON CONFLICT (org_id, name)
  DO UPDATE SET secret = set_org_secret.value
  RETURNING id
$function$
;

CREATE OR REPLACE FUNCTION delete_org_secret(name text)
 RETURNS uuid
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  DELETE FROM org_secrets WHERE org_id IN (
    SELECT organizations.id
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
  ) AND name = delete_org_secret.name
  RETURNING id
$function$
;

create policy "Org members can delete org secrets."
  on org_secrets
  for delete using (
    auth.user_id() in (
      select auth_id from
      organizations
      JOIN members ON organizations.id = members.org_id
      JOIN users ON members.user_id = users.id
      WHERE organizations.id = org_secrets.org_id
    )
  );
