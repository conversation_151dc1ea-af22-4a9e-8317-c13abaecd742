-- AUTOGENERATED BY MIGRATION SCRIPT: just contains the DDL changes

create type "public"."max_over_calendar_months_underlying" as ("window_size_months" integer, "max_value" bigint);

create domain max_over_calendar_months as max_over_calendar_months_underlying check (
	(value) is null or (
        (value).window_size_months is not null
        and (value).window_size_months > 0
        and (value).max_value is not null
        and (value).max_value >= 0
    )
);

create table "public"."app_config" (
    "key" text not null,
    "value_text" text,
    "value_boolean" boolean
);


alter table "public"."app_config" enable row level security;

alter table "public"."resources" add column "num_dataset_row_actions_calendar_months" max_over_calendar_months;

alter table "public"."resources" add column "num_log_bytes_calendar_months" max_over_calendar_months;

alter table "public"."resources" add column "num_private_experiment_row_actions_calendar_months" max_over_calendar_months;

alter table "public"."resources" add column "num_production_log_row_actions_calendar_months" max_over_calendar_months;

CREATE UNIQUE INDEX app_config_pkey ON public.app_config USING btree (key);

alter table "public"."app_config" add constraint "app_config_pkey" PRIMARY KEY using index "app_config_pkey";

CREATE OR REPLACE FUNCTION public.get_app_config_boolean(key text)
 RETURNS boolean
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
select value_boolean from app_config where key = $1;
$function$
;

revoke execute on function get_app_config_boolean from public, anon;

CREATE OR REPLACE FUNCTION public.update_windowed_resource_counts_calendar_months(resource_name_col text, num_shards integer, org_id_and_counts org_id_and_count[], auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _now_date date := current_date;
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts_calendar_months.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', 'User does not have permission to update resources for all requested orgs');
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        date_trunc('month', coalesce(_mock_current_date, _now_date)) date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
        left join _braintrust_testing_mock_current_date using (org_id)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_months" months of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' current_dates as ('
        '   select'
        '     org_id,'
        '     date_trunc(''month'', coalesce(_mock_current_date, $3)) as current_month_date'
        '   from'
        '     unnest($1) as u(org_id)'
        '     left join _braintrust_testing_mock_current_date using (org_id)'
        ' ),'
        ' resource_total_counts as ('
        ' select'
        '     rc.org_id,'
        '     SUM(rc.count) total_value,'
        '     (r.%I).max_value,'
        '     cd.current_month_date'
        ' from'
        '     resource_counts rc'
        '     join resources r on rc.org_id = r.org_id'
        '     join current_dates cd on rc.org_id = cd.org_id'
        ' where'
        '     rc.resource_name = $2'
        '     and (r.%I) is not null'
        '     and rc.date_bucket > (cd.current_month_date - make_interval(months => (r.%I).window_size_months))'
        '     and rc.date_bucket <= cd.current_month_date'
        ' group by'
        '     rc.org_id, max_value, cd.current_month_date'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col, _now_date
    ;

    if (array_length(_resource_violations, 1) > 0) then
        -- The text of this error message is used to identify the error in the
        -- data backend. If you change it, make sure that the filter in
        -- api-ts/src/resource_check.ts is still valid.
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('Violations of resource constraint %s: %s', resource_name_col, _resource_violations));
    end if;
end;
$function$
;

revoke execute on function update_windowed_resource_counts_calendar_months from anon;

CREATE OR REPLACE FUNCTION public.insert_resource_definition(org_id uuid, tier resource_tier)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 250000);
            _insert_row.num_production_log_row_actions := row(7, 250000);
            _insert_row.num_dataset_row_actions := row(7, 250000);
            _insert_row.num_log_bytes := row(7, 250000000);
            _insert_row.num_private_experiment_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_production_log_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_dataset_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_log_bytes_calendar_months := row(1, 1000000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_windowed_resource_counts(resource_name_col text, num_shards integer, org_id_and_counts org_id_and_count[], auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _now_date date := current_date;
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', 'User does not have permission to update resources for all requested orgs');
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        coalesce(_mock_current_date, _now_date) date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
        left join _braintrust_testing_mock_current_date using (org_id)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_days" days of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' current_dates as ('
        '   select'
        '     org_id,'
        '     coalesce(_mock_current_date, current_date) as current_date_value'
        '   from'
        '     unnest($1) as u(org_id)'
        '     left join _braintrust_testing_mock_current_date using (org_id)'
        ' ),'
        ' resource_total_counts as ('
        ' select'
        '     rc.org_id,'
        '     SUM(rc.count) total_value,'
        '     (r.%I).max_value,'
        '     cd.current_date_value'
        ' from'
        '     resource_counts rc'
        '     join resources r on rc.org_id = r.org_id'
        '     join current_dates cd on rc.org_id = cd.org_id'
        ' where'
        '     rc.resource_name = $2'
        '     and (r.%I) is not null'
        '     and rc.date_bucket > (cd.current_date_value - (r.%I).window_size_days)'
        '     and rc.date_bucket <= cd.current_date_value'
        ' group by'
        '     rc.org_id, max_value, cd.current_date_value'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col
    ;

    if (array_length(_resource_violations, 1) > 0) then
        -- The text of this error message is used to identify the error in the
        -- data backend. If you change it, make sure that the filter in
        -- api-ts/src/resource_check.ts is still valid.
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('Violations of resource constraint %s: %s', resource_name_col, _resource_violations));
    end if;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_resource_counts_for_insert(input jsonb, num_shards integer, auth_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _num_private_experiment_row_actions_org_id_and_counts org_id_and_count[];
    _num_production_log_row_actions_org_id_and_counts org_id_and_count[];
    _num_dataset_row_actions_org_id_and_counts org_id_and_count[];
    _num_log_bytes_org_id_and_counts org_id_and_count[];
    _use_calendar_months boolean := coalesce(get_app_config_boolean('use_calendar_months'), false);
begin
    -- Process resource 'num_private_experiment_row_actions'.

    -- 1. Grab the set of private experiments out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_private_experiment_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_experiments as (
    select
       key::uuid experiment_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'experiments', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_experiments.num_row_actions))
            ::org_id_and_count r
    from
        input_experiments
        join experiments on input_experiments.experiment_id = experiments.id
        join projects on experiments.project_id = projects.id
        join organizations on projects.org_id = organizations.id
    where
        experiments.deleted_at is null
        and projects.deleted_at is null
        and not has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id())
    group by
      organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_private_experiment_row_actions_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_private_experiment_row_actions_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_private_experiment_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
            auth_id => auth_id);
    end if;
    -- Process resource 'num_production_log_row_actions'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the logs recorded per org. Store these into
    -- _num_production_log_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_logs as (
    select
        key::uuid org_id,
        (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_actions_per_org as (
    select
        row(org_id, SUM(num_row_actions))::org_id_and_count r
    from
        input_logs
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_production_log_row_actions_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_production_log_row_actions_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_production_log_row_actions_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_production_log_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_production_log_row_actions_org_id_and_counts,
            auth_id => auth_id);
    end if;

    -- Process resource 'num_log_bytes'.

    -- 1. Grab the set of logs out of the input.
    --
    -- 2. Collect the total bytes recorded per org. Store these into
    -- _num_log_bytes_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_log_bytes as (
    select
        key::uuid org_id,
        coalesce((value->'num_row_bytes')::bigint, 0) num_row_bytes
    from
        jsonb_each(coalesce(input->'logs', '{}'::jsonb))
    ),
    num_row_bytes_per_org as (
    select
        row(org_id, SUM(num_row_bytes))::org_id_and_count r
    from
        input_log_bytes
    group by
        org_id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_bytes_per_org
    into _num_log_bytes_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_log_bytes_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_log_bytes_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_log_bytes',
            num_shards => num_shards,
            org_id_and_counts => _num_log_bytes_org_id_and_counts,
            auth_id => auth_id);
    end if;

    -- Process resource 'num_dataset_row_actions'.

    -- 1. Grab the set of datasets out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_dataset_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_datasets as (
    select
       key::uuid dataset_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'datasets', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_datasets.num_row_actions))
            ::org_id_and_count r
    from
        input_datasets
        join datasets on input_datasets.dataset_id = datasets.id
        join projects on datasets.project_id = projects.id
        join organizations on projects.org_id = organizations.id
    where
        datasets.deleted_at is null
        and projects.deleted_at is null
    group by
        organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_dataset_row_actions_org_id_and_counts
    ;

    if _use_calendar_months then
        perform update_windowed_resource_counts_calendar_months(
            resource_name_col => 'num_dataset_row_actions_calendar_months',
            num_shards => num_shards,
            org_id_and_counts => _num_dataset_row_actions_org_id_and_counts,
            auth_id => auth_id);
    else
        perform update_windowed_resource_counts(
            resource_name_col => 'num_dataset_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_dataset_row_actions_org_id_and_counts,
            auth_id => auth_id);
    end if;

    -- Finally, return whether or not the orgs considered have unlimited
    -- resource counts, which can allow skipping future resource checks.
    return (
        with
        all_org_ids as (
            select org_id from unnest(_num_private_experiment_row_actions_org_id_and_counts)
            union all
            select org_id from unnest(_num_production_log_row_actions_org_id_and_counts)
            union all
            select org_id from unnest(_num_dataset_row_actions_org_id_and_counts)
            union all
            select value::uuid as org_id from jsonb_array_elements_text(coalesce(input->'additional_org_ids', '[]'::jsonb))
        ),
        org_id_is_unlimited as (
            select
                organizations.id org_id,
                (
                    -- No resource entry for an org implies unlimited.
                    resources.org_id isnull
                    or (
                        not _use_calendar_months
                        and resources.num_private_experiment_row_actions isnull
                        and resources.num_production_log_row_actions isnull
                        and resources.num_dataset_row_actions isnull
                        and resources.num_log_bytes isnull
                    ) or (
                        _use_calendar_months
                        and resources.num_private_experiment_row_actions_calendar_months isnull
                        and resources.num_production_log_row_actions_calendar_months isnull
                        and resources.num_dataset_row_actions_calendar_months isnull
                        and resources.num_log_bytes_calendar_months isnull
                    )
                ) is_unlimited
            from
                organizations left join resources on organizations.id = resources.org_id
                join members on members.org_id = organizations.id
            where
                organizations.id in (select org_id from all_org_ids)
                and members.user_id = _user_id
        )
        select jsonb_build_object(
            'is_unlimited', coalesce(jsonb_object_agg(org_id::text, is_unlimited), '{}'::jsonb))
        from org_id_is_unlimited
    );
end;
$function$
;

-- MANUAL MIGRATION STEPS:

-- 1. Set the app config key 'use_calendar_months' to true

insert into app_config (key, value_boolean) values ('use_calendar_months', true) on conflict do nothing;

-- 2. Update all existing resource limits to fill in a calendar month window.
-- Only fill the new resource columns where the old columns are non-null and
-- have window_size = 7.
UPDATE resources
SET
    num_private_experiment_row_actions_calendar_months =
        CASE
            WHEN num_private_experiment_row_actions IS NOT NULL
            AND ((num_private_experiment_row_actions).window_size_days = 7)
            THEN ROW(1, (num_private_experiment_row_actions).max_value * 4)::max_over_calendar_months_underlying
            ELSE NULL
        END,
    num_production_log_row_actions_calendar_months =
        CASE
            WHEN num_production_log_row_actions IS NOT NULL
            AND ((num_production_log_row_actions).window_size_days = 7)
            THEN ROW(1, (num_production_log_row_actions).max_value * 4)::max_over_calendar_months_underlying
            ELSE NULL
        END,
    num_dataset_row_actions_calendar_months =
        CASE
            WHEN num_dataset_row_actions IS NOT NULL
            AND ((num_dataset_row_actions).window_size_days = 7)
            THEN ROW(1, (num_dataset_row_actions).max_value * 4)::max_over_calendar_months_underlying
            ELSE NULL
        END,
    num_log_bytes_calendar_months =
        CASE
            WHEN num_log_bytes IS NOT NULL
            AND ((num_log_bytes).window_size_days = 7)
            THEN ROW(1, (num_log_bytes).max_value * 4)::max_over_calendar_months_underlying
            ELSE NULL
        END
;
