CREATE OR REPLACE FUNCTION create_api_key(auth_id uuid, name text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  api_key text;
BEGIN
  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  INSERT INTO api_keys (key_hash, name, preview_name, user_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT('sk-', RIGHT(api_key, 4)), id
    FROM users WHERE users.auth_id = create_api_key.auth_id;
  RETURN CONCAT('sk-', api_key);
END;
$function$
;

CREATE OR REPLACE FUNCTION lookup_api_key(key text)
 RETURNS uuid
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  SELECT auth_id FROM api_keys JOIN users ON api_keys.user_id = users.id
  WHERE key_hash = pgsodium.crypto_generichash(decode(regexp_replace(key, '^sk-', ''), 'base64'))
$function$
;
