drop function if exists "public"."braintrust_current_date"();

alter table "public"."_braintrust_testing_mock_current_date" drop column "value";

alter table "public"."_braintrust_testing_mock_current_date" add column "_mock_current_date" date;

alter table "public"."_braintrust_testing_mock_current_date" add column "id" uuid not null default uuid_generate_v4();

alter table "public"."_braintrust_testing_mock_current_date" add column "org_id" uuid not null;

CREATE UNIQUE INDEX _braintrust_testing_mock_current_date_pkey ON public._braintrust_testing_mock_current_date USING btree (id);

alter table "public"."_braintrust_testing_mock_current_date" add constraint "_braintrust_testing_mock_current_date_pkey" PRIMARY KEY using index "_braintrust_testing_mock_current_date_pkey";

alter table "public"."_braintrust_testing_mock_current_date" add constraint "_braintrust_testing_mock_current_date_org_id_fkey" FOREIGN KEY (org_id) REFERENCES organizations(id) not valid;

alter table "public"."_braintrust_testing_mock_current_date" validate constraint "_braintrust_testing_mock_current_date_org_id_fkey";

CREATE OR REPLACE FUNCTION public.update_windowed_resource_counts(resource_name_col text, num_shards integer, org_id_and_counts org_id_and_count[], auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _now_date date := current_date;
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception 'User does not have permission to update resources for all requested orgs';
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        coalesce(_mock_current_date, _now_date) date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
        left join _braintrust_testing_mock_current_date using (org_id)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_days" days of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' resource_total_counts as ('
        ' select'
        '     org_id,'
        '     SUM(resource_counts.count) total_value,'
        '     (resources.%I).max_value'
        ' from'
        '     resource_counts'
        '     join resources using (org_id)'
        '     join unnest($1) using (org_id)'
        ' where'
        '     resource_name = $2'
        '     and (resources.%I) is not null'
        '     and date_bucket > ($3 - (resources.%I).window_size_days)'
        '     and date_bucket <= $4'
        ' group by'
        '     org_id, max_value'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col, _now_date, _now_date
    ;

    if (array_length(_resource_violations, 1) > 0) then
        -- The text of this error message is used to identify the error in
        -- chalicelib. If you change it, make sure that the chalicelib filter is
        -- still valid.
        raise exception 'Violations of resource constraint %: %',
            resource_name_col, _resource_violations;
    end if;
end;
$function$
;
