-- We don't support decrementing resource counts for now.
drop function decrement_resource_counts(jsonb);

-- Just switch to nulls by default for all resources. It's easier when searching
-- for orgs with no resource definition.
alter table resources alter column forbid_toggle_experiment_public_to_private drop not null;
alter table resources alter column forbid_toggle_experiment_public_to_private drop default;

-- Switch the org_id foreign key on resource_counts to reference organizations,
-- so that we can increment resource counts for entities which don't have a
-- resource entry.
alter table resource_counts drop constraint resource_counts_org_id_fkey;
alter table resource_counts add constraint resource_counts_org_id_fkey
    foreign key (org_id) references organizations;

-- Redefine update_windowed_resource_counts to accept an 'auth_id' parameter and
-- make sure that the provided auth_id is part af all organizations being
-- updated.
drop function update_resource_counts_for_insert(jsonb, int);
drop function update_windowed_resource_counts(text, int, org_id_and_count[]);
drop type inserted_resource_count_row;
drop type org_id_and_max_over_window;

-- Function to check/update a resource of type max_over_window. Will update
-- resource_counts even if there is no limit. Will raise an exception if any of
-- the resource counts are exceeded.
create or replace function update_windowed_resource_counts(
    -- Should match the column name in 'resources' that we are working with.
    resource_name_col text,
    num_shards int,
    org_id_and_counts org_id_and_count[],
    auth_id uuid
)
returns void
language plpgsql
security definer
as $$
declare
    _now_date date := braintrust_current_date();
    _auth_violation_orgs uuid[];
    _resource_violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Check that the user named by `auth_id` belongs to all orgs being updated.
    with
    valid_org_ids as (
    select
        org_id
    from
        unnest(org_id_and_counts)
        join members using (org_id)
        join users on members.user_id = users.id
    where
        users.auth_id = update_windowed_resource_counts.auth_id
    )
    select array_agg(org_id)
    from unnest(org_id_and_counts)
    where org_id not in (select org_id from valid_org_ids)
    into _auth_violation_orgs
    ;

    if (array_length(_auth_violation_orgs, 1) > 0) then
        raise exception 'User does not have permission to update resources for all requested orgs';
    end if;

    -- Upsert each row in org_id_and_counts.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        _now_date date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
    )
    insert into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select *
    from rows_to_insert
    order by org_id, resource_name, date_bucket, shard_key
    on conflict (org_id, resource_name, date_bucket, shard_key)
    do update set count = resource_counts.count + excluded.count
    ;

    -- For error check, compute the total value of each resource count, windowed
    -- over the last "window_size_days" days of the corresponding resource (ignore
    -- orgs with null resource definition). Filter for rows where total_value >
    -- max_value. Aggregate these into an array and raise an exception if the array
    -- is nonempty.
    --
    -- We must generate this query dynamically because the column name of the
    -- resource is defined as `resource_name_col`.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    execute format(
        'with'
        ' resource_total_counts as ('
        ' select'
        '     org_id,'
        '     SUM(resource_counts.count) total_value,'
        '     (resources.%I).max_value'
        ' from'
        '     resource_counts'
        '     join resources using (org_id)'
        '     join unnest($1) using (org_id)'
        ' where'
        '     resource_name = $2'
        '     and (resources.%I) is not null'
        '     and date_bucket > ($3 - (resources.%I).window_size_days)'
        '     and date_bucket <= $4'
        ' group by'
        '     org_id, max_value'
        ' )'
        ' select array_agg(row(org_id, total_value, max_value)'
        '                     ::max_value_resource_violation)'
        ' from resource_total_counts'
        ' where total_value > max_value'
        , resource_name_col, resource_name_col, resource_name_col)
    into _resource_violations
    using org_id_and_counts, resource_name_col, _now_date, _now_date
    ;

    if (array_length(_resource_violations, 1) > 0) then
        raise exception 'Violations of resource constraint %: %',
            resource_name_col, _resource_violations;
    end if;
end;
$$;

-- Function to check/update resource counts upon insertion. Throws an exception
-- if any of the resource counts are exceeded.
--
-- Input schema:
-- {
--     experiments: {
--         [experiment_id]: {
--             num_row_actions: number;
--         }
--     }
-- }
create or replace function update_resource_counts_for_insert(
    input jsonb,
    num_shards int,
    auth_id uuid)
returns void
language plpgsql
security definer
as $$
declare
    _num_private_experiment_row_actions_org_id_and_counts org_id_and_count[];
begin
    -- Process resource 'num_private_experiment_row_actions'.

    -- 1. Grab the set of private experiments out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_private_experiment_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts`.
    with
    input_experiments as (
    select
       key::uuid experiment_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'experiments', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_experiments.num_row_actions))
            ::org_id_and_count r
    from
        input_experiments
        join active_experiments on input_experiments.experiment_id = active_experiments.id
        join active_projects on active_experiments.project_id = active_projects.id
        join organizations on active_projects.org_id = organizations.id
    where
        not active_experiments.public
    group by
      organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_private_experiment_row_actions_org_id_and_counts
    ;

    perform update_windowed_resource_counts(
        resource_name_col => 'num_private_experiment_row_actions',
        num_shards => num_shards,
        org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts,
        auth_id => auth_id);
end;
$$;
