create OR REPLACE view
  public.experiment_conflicts as
select
  e1.name,
  e1.project_id,
  (
    select
      experiments.name
    from
      experiments
    where
      experiments.name ~~ concat(e1.name, '%')
      and experiments.name ~ '-\d+$'
      and e1.project_id = experiments.project_id
    order by
      experiments.name desc
    limit
      1
  ) as highest_conflict
from
  experiments e1;
