ALTER TABLE org_secrets ADD COLUMN type text;
UPDATE org_secrets SET
    type = CASE WHEN name = 'OPENAI_API_KEY' THEN 'openai'
                WHEN name = 'ANTHROPIC_API_KEY' THEN 'anthropic'
                WHEN name = 'PERPLEXITY_API_KEY' THEN 'perplexity'
            END;

create index on org_secrets(org_id, type);
ALTER TABLE org_secrets ADD COLUMN metadata json;


create or replace view preview_org_secrets as
select
    org_secrets.id,
    org_secrets.created,
    org_secrets.org_id,
    org_secrets.name,
    CASE WHEN org_secrets.secret IS NULL THEN NULL ELSE '********'::text E<PERSON> as preview_secret,
    organizations.name as org_name,
    org_secrets.type,
    org_secrets.metadata as metadata
from
    org_secrets
    join organizations on org_secrets.org_id = organizations.id
    join members on organizations.id = members.org_id
    join users on members.user_id = users.id
where users.auth_id = auth.user_id();


drop function set_org_secret(
    name text,
    value text);


drop function set_org_secret(
    name text,
    value text,
    org_name text);


create or replace function set_org_secret(
    type text,
    name text,
    metadata json,
    org_name text)
returns uuid
language sql
security definer
as $$
  INSERT INTO org_secrets (org_id, type, name, metadata)
  SELECT organizations.id, set_org_secret.type, set_org_secret.name, set_org_secret.metadata
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = set_org_secret.org_name
  ON CONFLICT (org_id, name)
  DO UPDATE SET metadata = set_org_secret.metadata, type = set_org_secret.type
  RETURNING id
$$;

grant execute on function set_org_secret(text, text, json, text) to anon;

create or replace function set_org_secret(
    type text,
    name text,
    value text,
    metadata json,
    org_name text)
returns uuid
language sql
security definer
as $$
  INSERT INTO org_secrets (org_id, type, name, secret, metadata)
  SELECT organizations.id, set_org_secret.type, set_org_secret.name, set_org_secret.value, set_org_secret.metadata
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = set_org_secret.org_name
  ON CONFLICT (org_id, name)
  DO UPDATE SET secret = set_org_secret.value, metadata = set_org_secret.metadata, type = set_org_secret.type
  RETURNING id
$$;

grant execute on function set_org_secret(text, text, text, json, text) to anon;
