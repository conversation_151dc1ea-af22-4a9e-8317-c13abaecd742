CREATE OR REPLACE FUNCTION register_dataset(org_id uuid, project_name text, dataset_name text)
RETURNS JSONB
SECURITY INVOKER
AS $$
DECLARE
    _project_id uuid;
    _dataset_id uuid;
BEGIN
  -- https://stackoverflow.com/questions/35265453/use-insert-on-conflict-do-nothing-returning-failed-rows
  with inserted (id) as (
    INSERT INTO projects (org_id, name)
    VALUES (register_dataset.org_id, register_dataset.project_name)
    ON CONFLICT DO NOTHING
    returning id
  )
  SELECT id INTO _project_id FROM (
    select projects.id AS id
    from projects WHERE projects.org_id = register_dataset.org_id and projects.name = register_dataset.project_name
    union all
    select id
    from inserted
  ) sub;

  with inserted (id) as (
    INSERT INTO datasets (project_id, name)
    VALUES (_project_id, register_dataset.dataset_name)
    ON CONFLICT DO NOTHING
    returning id
  )
  SELECT id INTO _dataset_id FROM (
    select datasets.id AS id
    from active_datasets datasets WHERE datasets.project_id = _project_id and datasets.name = register_dataset.dataset_name
    union all
    select id
    from inserted
  ) sub;


  RETURN (
    SELECT jsonb_build_object('project', projects, 'dataset', datasets)  FROM
    projects JOIN datasets ON projects.id = datasets.project_id
    WHERE datasets.project_id = _project_id AND datasets.id = _dataset_id
  );

END;
$$ LANGUAGE plpgsql;
