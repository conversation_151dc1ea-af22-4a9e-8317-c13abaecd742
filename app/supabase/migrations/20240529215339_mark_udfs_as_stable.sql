CREATE OR <PERSON><PERSON>LACE FUNCTION public.check_org_name_conflict(org_name text)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
AS $function$
    select
    EXISTS (
        SELECT 1
        FROM organizations
        WHERE
            organizations.name = org_name
    )
END;
$function$
;

CREATE OR REPLACE FUNCTION public.determine_resource_tier(user_id uuid)
 RETURNS resource_tier
 LANGUAGE plpgsql
 STABLE
AS $function$
declare
    _user_email text;
begin
    select email
    from users
    where id = user_id
    into _user_email;

    if not found then
        raise exception 'User id % does not exist or you do not have access', user_id;
    end if;

    -- If the user's email ends in '.edu', use the 'edu' tier. Otherwise use the
    -- 'free' tier.
    if trim(trailing from _user_email) like '%.edu' then
        return 'edu';
    else
        return 'free';
    end if;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.find_acl_id(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _group_id uuid DEFAULT NULL::uuid, _permission permission_type DEFAULT NULL::permission_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS uuid
 LANGUAGE plpgsql
 STABLE
AS $function$
declare
    _user_id_comparator text :=
        case when _user_id isnull
        then '(user_id isnull and $1 isnull)'
        else '(user_id = $1)'
        end;
    _group_id_comparator text :=
        case when _group_id isnull
        then '(group_id isnull and $2 isnull)'
        else '(group_id = $2)'
        end;
    _permission_comparator text :=
        case when _permission isnull
        then '(permission isnull and $3 isnull)'
        else '(permission = $3)'
        end;
    _role_id_comparator text :=
        case when _role_id isnull
        then '(role_id isnull and $4 isnull)'
        else '(role_id = $4)'
        end;
    _restrict_object_type_comparator text :=
        case when _restrict_object_type isnull
        then '(restrict_object_type isnull and $5 isnull)'
        else '(restrict_object_type = $5)'
        end;

    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;
    _ret uuid;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    execute (
        'select id from acls where' ||
        concat_ws(' and ', _user_id_comparator, _group_id_comparator,
                  _permission_comparator, _role_id_comparator,
                  _restrict_object_type_comparator) ||
        ' and object_type = $6 and object_id = $7 and user_object_type = $8' ||
        ' and grant_object_type = $9'
    )
    into _ret
    using _user_id, _group_id, _permission, _role_id, _restrict_object_type,
          _object_type, _object_id, _user_object_type, _grant_object_type;
    return _ret;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.get_new_acl_object_org_id(_object_type acl_object_type, _object_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 STABLE
AS $function$
declare
    _object_org_id uuid;
begin
    if (_object_type = 'organization' or _object_type = 'org_member') then
        _object_org_id = _object_id;
    elsif (_object_type = 'project' or _object_type = 'project_log') then
        select projects.org_id into _object_org_id
        from projects where projects.id = _object_id;
    elsif _object_type = 'experiment' then
        select projects.org_id into _object_org_id
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _object_id;
    elsif _object_type = 'dataset' then
        select projects.org_id into _object_org_id
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _object_id;
    elsif _object_type = 'prompt' then
        select projects.org_id into _object_org_id
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _object_id;
    elsif _object_type = 'prompt_session' then
        select projects.org_id into _object_org_id
        from projects join prompt_sessions on projects.id = prompt_sessions.project_id
        where prompt_sessions.id = _object_id;
    elsif _object_type = 'project_score' then
        select projects.org_id into _object_org_id
        from projects join project_scores on projects.id = project_scores.project_id
        where project_scores.id = _object_id;
    elsif _object_type = 'project_tag' then
        select projects.org_id into _object_org_id
        from projects join project_tags on projects.id = project_tags.project_id
        where project_tags.id = _object_id;
    elsif _object_type = 'group' then
        select groups.org_id into _object_org_id
        from groups where groups.id = _object_id;
    elsif _object_type = 'role' then
        select roles.org_id into _object_org_id
        from roles where roles.id = _object_id;
        if _object_org_id is null then
            raise exception 'Cannot create ACL on system role object';
        end if;
    end if;
    if _object_org_id is null then
        raise exception 'ACL object id is invalid for % object', _object_type;
    end if;
    return _object_org_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.get_org_owners_group_id(_org_id uuid)
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
    select id from groups where org_id = _org_id and name = 'org-owners' and deleted_at is null;
$function$
;

CREATE OR REPLACE FUNCTION public.get_owner_role_id()
 RETURNS uuid
 LANGUAGE sql
 STABLE
AS $function$
    select id from roles where org_id is null and name = 'owner' and deleted_at is null;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_email(user_id uuid)
 RETURNS text
 LANGUAGE sql
 STABLE
AS $function$
    select email
    from users
    where id = user_id;
$function$
;

CREATE OR REPLACE FUNCTION public.has_organization_acl(_object_type acl_object_type, _org_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from _expanded_acls
    where (
        object_type = 'organization'
        and object_id = _org_id
        and user_id = _user_id
        and permission = _permission
        and (restrict_object_type is null or restrict_object_type = _object_type)
    )
);
$function$
;

CREATE OR REPLACE FUNCTION public.has_project_acl(_object_type acl_object_type, _project_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$function$
;

CREATE OR REPLACE FUNCTION public.has_under_organization_acl(_object_type acl_object_type, _object_id uuid, _org_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from _expanded_acls
    where (
        (
            object_type = _object_type
            and object_id = _object_id
            and user_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
        or (
            object_type = 'organization'
            and object_id = _org_id
            and user_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
    )
);
$function$
;

CREATE OR REPLACE FUNCTION public.has_under_project_acl(_object_type acl_object_type, _object_id uuid, _project_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE
AS $function$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = _object_type
                    and object_id = _object_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$function$
;

CREATE OR REPLACE FUNCTION public.load_experiment(org_name text, project_name text, experiment_name text)
 RETURNS SETOF load_experiment_view
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM load_experiment_view experiments
  WHERE
    -- Name check
    experiments.name = load_experiment.experiment_name
    AND project_name = load_experiment.project_name
    AND org_name = load_experiment.org_name
$function$
;

CREATE OR REPLACE FUNCTION public.load_project(org_name text, project_name text)
 RETURNS SETOF visible_experiments
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM visible_experiments experiments
  WHERE
    -- Name check
    project_name = load_project.project_name
    AND org_name = load_project.org_name
$function$
;

CREATE OR REPLACE FUNCTION public.lookup_api_key(key text)
 RETURNS api_key_lookup
 LANGUAGE sql
 STABLE SECURITY DEFINER
AS $function$
  SELECT auth_id, api_keys.org_id AS org_id FROM api_keys JOIN users ON api_keys.user_id = users.id
  WHERE key_hash = pgsodium.crypto_generichash(decode(regexp_replace(key, '^sk-', ''), 'base64'))
$function$
;

CREATE OR REPLACE FUNCTION public.lookup_experiment(experiment_id uuid)
 RETURNS TABLE(id uuid, name text, project_id uuid)
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT id, name, project_id FROM visible_experiments experiments
  WHERE
    -- id check
    experiments.id = lookup_experiment.experiment_id
$function$
;

CREATE OR REPLACE FUNCTION public.lookup_experiments_by_commits(project_id uuid, commits text[])
 RETURNS TABLE(commit text, id uuid, repo_info jsonb, created timestamp with time zone)
 LANGUAGE sql
 STABLE
AS $function$
SELECT
  c AS commit, experiments.id, experiments.repo_info, experiments.created
FROM
  UNNEST(commits) c
  JOIN experiments ON experiments.project_id = lookup_experiments_by_commits.project_id
        AND experiments.commit = c
$function$
;

CREATE OR REPLACE FUNCTION public.lookup_org_secret(name text)
 RETURNS text
 LANGUAGE sql
 STABLE SECURITY DEFINER
AS $function$
  SELECT decrypted_secret FROM secrets.decrypted_org_secrets
    JOIN organizations ON decrypted_org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE
      decrypted_org_secrets.name = lookup_org_secret.name
      AND users.auth_id = auth.user_id()
$function$
;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_nonpublic(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as nonpublic';
    end if;
    delete from acls
    where id = find_acl_id(
        _object_type => 'experiment',
        _object_id => experiment_id,
        _user_id => anon_user_id(),
        _permission => 'read')
    ;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.sanitize_repo_info(repo_info jsonb, org_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
AS $function$
declare
    _repo_info jsonb;
    _git_metadata_settings jsonb;
    _fields text[];
begin
    select git_metadata
    from org_settings
    where org_settings.org_id = sanitize_repo_info.org_id
    into _git_metadata_settings;

    if (_git_metadata_settings is null or _git_metadata_settings->>'collect' = 'all') then
        _repo_info := repo_info;
    end if;

    if (_git_metadata_settings->>'collect' = 'some') then
        select array_agg(fields)
        from jsonb_array_elements_text(COALESCE(_git_metadata_settings->'fields', '[]')) fields
        into _fields;

        select jsonb_object_agg(key, value)
        from jsonb_each(repo_info)
        where key = any(_fields)
        into _repo_info;
    end if;

    return _repo_info;
end;
$function$
;
