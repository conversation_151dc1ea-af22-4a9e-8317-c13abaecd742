CREATE TABLE IF NOT EXISTS api_keys (
  id uuid not null primary key DEFAULT uuid_generate_v4(),
  created timestamp with time zone default current_timestamp,
  key_hash bytea not null,
  name text not null,
  preview_name text not null,
  user_id uuid references users
);

alter table api_keys enable row level security;

create policy "Users can view their api_keys."
  on api_keys for select using (
    auth.user_id() in (SELECT auth_id FROM users WHERE users.id = user_id)
);

create policy "Users can delete their api_keys."
  on api_keys for delete using (
    auth.user_id() in (SELECT auth_id FROM users WHERE users.id = user_id)
);

GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA pgsodium TO service_role;

CREATE OR REPLACE FUNCTION create_api_key(auth_id uuid, name text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
  api_key text;
BEGIN
  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  INSERT INTO api_keys (key_hash, name, preview_name, user_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT('sk-', RIGHT(api_key, 4)), id
    FROM users WHERE users.auth_id = create_api_key.auth_id;
  RETURN CONCAT('sk-', api_key);
END;
$function$
;

CREATE OR REPLACE FUNCTION lookup_api_key(key text)
 RETURNS uuid
 LANGUAGE sql
AS $function$
  SELECT auth_id FROM api_keys JOIN users ON api_keys.user_id = users.id
  WHERE key_hash = pgsodium.crypto_generichash(decode(regexp_replace(key, '^sk-', ''), 'base64'))
$function$
;

ALTER TABLE tokens DROP COLUMN refresh_token;
