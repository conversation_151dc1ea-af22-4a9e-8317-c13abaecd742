-- Part 1: drop any dependent views before altering the table.

drop view if exists "public"."active_experiments" cascade;
drop view if exists "public"."base_experiments" cascade;
drop view if exists "public"."experiment_conflicts" cascade;
drop view if exists "public"."visible_experiments" cascade;
drop view if exists "public"."active_base_experiments" cascade;

-- Part 2: alter the table.

alter table "public"."experiments" alter column "dataset_version" set data type text using "dataset_version"::text;

-- Part 3: recreate any dependent views / functions.

create or replace view "public"."active_experiments" as  SELECT experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.public,
    experiments.user_id,
    experiments.metadata
   FROM (experiments
     JOIN active_projects ON ((active_projects.id = experiments.project_id)))
  WHERE (experiments.deleted_at IS NULL);

create or replace view "public"."base_experiments" as  SELECT sub.id,
    sub.project_id,
    sub.name,
    sub.base_exp_id,
    base_experiment.name AS base_exp_name
   FROM (( SELECT exp.id,
            exp.project_id,
            exp.name,
            COALESCE(exp.base_exp_id, ( SELECT base_exp.id
                   FROM active_experiments base_exp
                  WHERE ((base_exp.project_id = exp.project_id) AND (base_exp.id <> exp.id) AND (base_exp.created <= exp.created))
                  ORDER BY base_exp.created DESC
                 LIMIT 1)) AS base_exp_id
           FROM active_experiments exp) sub
     JOIN active_experiments base_experiment ON ((base_experiment.id = sub.base_exp_id)));

create or replace view "public"."visible_experiments" as  SELECT projects.name AS project_name,
    organizations.id AS org_id,
    organizations.name AS org_name,
    experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.public,
    experiments.user_id,
    experiments.metadata
   FROM ((active_experiments experiments
     JOIN active_projects projects ON ((projects.id = experiments.project_id)))
     JOIN organizations ON ((organizations.id = projects.org_id)))
  WHERE ((experiments.public = true) OR (EXISTS ( SELECT 1
           FROM (members
             JOIN users ON ((users.id = members.user_id)))
          WHERE ((users.auth_id = auth.user_id()) AND (members.org_id = organizations.id)))));

create or replace view "public"."active_base_experiments" as  SELECT base_experiments.id,
    base_experiments.project_id,
    base_experiments.name,
    base_experiments.base_exp_id,
    base_experiments.base_exp_name
   FROM base_experiments;

create or replace view "public"."load_experiment_view" as  SELECT v1.project_name,
    v1.org_id,
    v1.org_name,
    v1.id,
    v1.project_id,
    v1.name,
    v1.description,
    v1.created,
    v1.repo_info,
    v1.commit,
    v1.base_exp_id,
    v1.deleted_at,
    v1.dataset_id,
    v1.dataset_version,
    v1.public,
    v1.user_id,
    v1.metadata,
    ( SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name, 'created', v2.created) ORDER BY v2.created DESC) AS jsonb_agg
           FROM visible_experiments v2
          WHERE ((v1.project_id = v2.project_id) AND (v1.id <> v2.id))) AS comparables
   FROM visible_experiments v1;

create or replace view "public"."experiment_conflicts" as  SELECT e1.name,
    e1.project_id,
    ( SELECT experiments.name
           FROM experiments
          WHERE ((experiments.name ~~ concat(regexp_replace(e1.name, '-\d+$'::text, ''::text), '%')) AND (experiments.name ~ '-\d+$'::text) AND (e1.project_id = experiments.project_id))
          ORDER BY (lpad("substring"(experiments.name, '-(\d+)$'::text), 10)) DESC
         LIMIT 1) AS highest_conflict
   FROM experiments e1;

drop function if exists "public"."insert_experiment"(user_id uuid, project_id uuid, name text, description text, repo_info jsonb, base_exp_id uuid, dataset_id uuid, dataset_version bigint, public boolean, metadata jsonb);

drop function if exists "public"."register_experiment"(auth_id uuid, org_id uuid, project_id uuid, project_name text, experiment_name text, description text, update boolean, repo_info jsonb, base_exp_id uuid, base_experiment text, ancestor_commits text[], dataset_id uuid, dataset_version bigint, public boolean, metadata jsonb);

CREATE OR REPLACE FUNCTION public.insert_experiment(user_id uuid, project_id uuid, name text, description text, repo_info jsonb, base_exp_id uuid, dataset_id uuid, dataset_version text, public boolean, metadata jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _experiment_id uuid;
begin
    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, public, user_id, metadata)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, public, user_id, metadata)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.load_experiment(org_name text, project_name text, experiment_name text)
 RETURNS SETOF load_experiment_view
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM load_experiment_view experiments
  WHERE
    -- Name check
    experiments.name = load_experiment.experiment_name
    AND project_name = load_experiment.project_name
    AND org_name = load_experiment.org_name
$function$
;

CREATE OR REPLACE FUNCTION public.load_project(org_name text, project_name text)
 RETURNS SETOF visible_experiments
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM visible_experiments experiments
  WHERE
    -- Name check
    project_name = load_project.project_name
    AND org_name = load_project.org_name
$function$
;

CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version text DEFAULT NULL::text, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the user has access to the project.
        select active_projects_auth_id.id into _project_id
        from active_projects_auth_id
        where
            active_projects_auth_id.auth_id = register_experiment.auth_id
            and active_projects_auth_id.id = register_experiment.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Make sure the user has access to the base experiment.
        select experiments.id into _base_exp_id
        from
            active_experiments experiments
            join projects_auth_id on experiments.project_id = projects_auth_id.id
        where
            projects_auth_id.auth_id = register_experiment.auth_id
            and experiments.id = register_experiment.base_exp_id;
        if not found then
            raise exception 'Base experiment does not exist or user does not have access to it';
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            active_experiments experiments
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and _update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = _repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id,
            metadata = register_experiment.metadata
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                _public, metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    _public, metadata)
                into _inserted_experiment_id;
            end if;
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$function$
;
