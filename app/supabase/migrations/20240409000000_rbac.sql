-- BEGIN SECTION RBAC SCHEMA

-- Type and table definitions

-- Teams are collections of users, which may be assigned their own access
-- controls. Teams can consist of users as well as other teams.

create table teams(
    id uuid not null primary key default uuid_generate_v4(),
    -- It is forbidden to change the org after creating a team.
    org_id uuid not null references organizations,
    user_id uuid references users,
    created timestamp with time zone default current_timestamp,
    name text not null,
    description text,
    deleted_at timestamp with time zone
);
alter table teams enable row level security;
create unique index on teams(org_id, name, deleted_at) nulls not distinct;
-- For foreign keys.
create unique index on teams(id, org_id);

create table team_users(
    team_id uuid not null,
    user_id uuid not null,
    -- This is derived and set by an insert/update trigger.
    _team_org_id uuid not null,
    primary key (team_id, user_id),
    foreign key (team_id, _team_org_id) references teams(id, org_id),
    foreign key (_team_org_id, user_id) references members(org_id, user_id)
);
alter table team_users enable row level security;

create table team_inheritors(
    team_id uuid not null references teams,
    inheritor_team_id uuid not null references teams,
    primary key (team_id, inheritor_team_id)
);
alter table team_inheritors enable row level security;
-- For querying the reverse relationship.
create index on team_inheritors(inheritor_team_id, team_id);

-- A privilege is a verb permitting a certain type of operation on a specific
-- object in the system. They can be assigned on an individual basis, or grouped
-- into roles.
create type privilege_type as enum (
    -- CRUD operations on the object itself.
    'create',
    'read',
    'update',
    'delete',
    -- CRUD operations on the object ACLs.
    'create_acls',
    'read_acls',
    'update_acls',
    'delete_acls'
);

create table roles(
    id uuid not null primary key default uuid_generate_v4(),
    -- A null org_id indicates a system role, which may be assigned to anybody
    -- and inherited by any other role, but only edited by the DB superuser.
    -- System roles also may not inherit from non-system roles. A role with an
    -- org_id is called a custom role.
    --
    -- It is forbidden to change the org after creating a role.
    org_id uuid references organizations,
    -- Similarly, system roles do not have user_ids.
    user_id uuid references users,
    created timestamp with time zone default current_timestamp,
    name text not null,
    description text,
    deleted_at timestamp with time zone
);
alter table roles enable row level security;
create unique index on roles(org_id, name, deleted_at) nulls not distinct;
-- For foreign keys.
create unique index on roles(id, org_id);

create table role_privileges(
    role_id uuid not null references roles,
    privilege privilege_type not null,
    primary key (role_id, privilege)

);
alter table role_privileges enable row level security;

create table role_inheritors(
    role_id uuid not null references roles,
    inheritor_role_id uuid not null references roles,
    primary key (role_id, inheritor_role_id)
);
alter table role_inheritors enable row level security;
-- For querying the reverse relationship.
create index on role_inheritors(inheritor_role_id, role_id);

-- Each ACL applies to a specific object, named by (object_type, object_id), a
-- user/team, named by (user_object_type, user_id, team_id), and a grant/role,
-- named by (grant_object_type, privilege, role_id).
--
-- The ACL will grant every member of the team the set of permissions in the
-- role for the specified object AND any object beneath it in the object
-- hierarchy. E.g. granting 'read' permission on an organization will imply read
-- permission on all experiments, datasets, etc. within the org.
--
-- The ACL may optionally specify 'restrict_object_type' to restrict the
-- permission to just sub-objects of a certain type. For example, granting
-- 'read' permission on an organization with 'restrict_object_type = dataset'
-- will imply read permission on just datasets within the org. A null value for
-- 'restrict_object_type' means the ACL applies to all object types.

create type acl_object_type as enum (
    'organization',
    'project',
    'experiment',
    'dataset',
    'prompt',
    'prompt_session',
    'project_score',
    'project_tag',
    'team',
    'role'
);

create type acl_user_object_type as enum ('user', 'team');

create type acl_grant_object_type as enum ('privilege', 'role');

create table acls(
    id uuid not null primary key default uuid_generate_v4(),
    object_type acl_object_type not null,
    object_id uuid not null,
    user_object_type acl_user_object_type not null,
    -- not null if user_object_type = 'user'
    user_id uuid,
    -- not null if user_object_type = 'team'
    team_id uuid,
    grant_object_type acl_grant_object_type not null,
    -- not null if grant_object_type = 'privilege'
    privilege privilege_type,
    -- not null if grant_object_type = 'role'
    role_id uuid,
    -- Optionally restrict the ACL to a specific type of sub-object. Null means
    -- no restriction.
    restrict_object_type acl_object_type,
    -- This is derived and set by an insert/update trigger. If provided
    -- explicitly, it must agree with the org ID derived from (object_type,
    -- object_id).
    _object_org_id uuid not null,
    created timestamp with time zone default current_timestamp,
    foreign key (_object_org_id, user_id) references members(org_id, user_id),
    foreign key (team_id, _object_org_id) references teams(id, org_id),
    -- While it would be nice to have a foreign key on (role_id, _object_org_id)
    -- to roles(id, org_id), this would prevent us from assigning a system role
    -- to some organization object, because system roles have no org_id. Thus we
    -- must enforce this with more complicated logic in the trigger. We should
    -- be protected against changing the org of a role by the `update_roles`
    -- trigger.
    foreign key (role_id) references roles(id)
);
alter table acls enable row level security;

create unique index on acls(
    object_type, object_id, user_object_type, user_id, team_id,
    grant_object_type, privilege, role_id, restrict_object_type) nulls not distinct;
-- For quickly identifying which org an ACL belongs to.
create index on acls(_object_org_id);

-- We strictly serialize any statements which modify RBAC tables, because
-- updating the materialized views requires reading and writing several tables
-- in one transaction. In general it's not easy to reason granularly about which
-- row locks any modification will require, so we just acquire a single advisory
-- lock before any statement which modifies any RBAC table.

create function acquire_acl_lock()
returns void
language sql
security invoker
as $$
    -- This must be kept in sync with app/utils/advisory-locks.ts.
    select pg_advisory_xact_lock(0);
$$;

revoke execute on function acquire_acl_lock from public, anon;

create function acquire_acl_lock_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
begin
    perform acquire_acl_lock();
    return null;
end;
$$;

revoke execute on function acquire_acl_lock_trigger_f from public, anon;

create trigger acquire_acl_lock_teams_trigger
    before insert or update or delete on teams
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_team_users_trigger
    before insert or update or delete on team_users
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_team_inheritors_trigger
    before insert or update or delete on team_inheritors
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_roles_trigger
    before insert or update or delete on roles
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_role_privileges_trigger
    before insert or update or delete on role_privileges
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_role_inheritors_trigger
    before insert or update or delete on role_inheritors
    for each statement execute procedure acquire_acl_lock_trigger_f();
create trigger acquire_acl_lock_acls_trigger
    before insert or update or delete on acls
    for each statement execute procedure acquire_acl_lock_trigger_f();

-- Triggers for enforcing constraints and filling in derived information on the
-- RBAC tables. These are applied before each row.

create function check_update_team()
returns trigger
language plpgsql
security invoker
as $$
begin
    if old.org_id is distinct from new.org_id then
        raise exception 'Cannot modify org_id of team';
    end if;
    return new;
end;
$$;

revoke execute on function check_update_team from public, anon;

create trigger update_teams_trigger
    before update on teams
    for each row execute procedure check_update_team();

create function process_new_team_user()
returns trigger
language plpgsql
security invoker
as $$
begin
    select teams.org_id into new._team_org_id
    from teams where teams.id = new.team_id;
    return new;
end;
$$;

revoke execute on function process_new_team_user from public, anon;

create trigger upsert_team_users_trigger
    before insert or update on team_users
    for each row execute procedure process_new_team_user();

create function check_new_team_inheritor()
returns trigger
language plpgsql
security invoker
as $$
declare
    _team_org_id uuid;
    _inheritor_team_org_id uuid;
begin
    select teams.org_id into _team_org_id
    from teams where teams.id = new.team_id;
    select teams.org_id into _inheritor_team_org_id
    from teams where teams.id = new.inheritor_team_id;

    if _team_org_id <> _inheritor_team_org_id then
        raise exception 'Both teams must belong to the same organization';
    end if;

    return new;
end;
$$;

revoke execute on function check_new_team_inheritor from public, anon;

create trigger upsert_team_inheritors_trigger
    before insert or update on team_inheritors
    for each row execute procedure check_new_team_inheritor();

create function check_update_role()
returns trigger
language plpgsql
security invoker
as $$
begin
    if old.org_id is distinct from new.org_id then
        raise exception 'Cannot modify org_id of role';
    end if;
    return new;
end;
$$;

revoke execute on function check_update_role from public, anon;

create trigger update_roles_trigger
    before update on roles
    for each row execute procedure check_update_role();

create function check_new_role_inheritor()
returns trigger
language plpgsql
security invoker
as $$
declare
    _role_org_id uuid;
    _inheritor_role_org_id uuid;
begin
    select roles.org_id into _role_org_id
    from roles where roles.id = new.role_id;
    select roles.org_id into _inheritor_role_org_id
    from roles where roles.id = new.inheritor_role_id;

    if (_role_org_id is not null and _inheritor_role_org_id is not null and _role_org_id <> _inheritor_role_org_id) then
        raise exception 'Both roles must belong to the same organization';
    end if;

    if (_inheritor_role_org_id is null and _role_org_id is not null) then
        raise exception 'System roles cannot inherit from non-system roles';
    end if;

    return new;
end;
$$;

revoke execute on function check_new_role_inheritor from public, anon;

create trigger upsert_role_inheritors_trigger
    before insert or update on role_inheritors
    for each row execute procedure check_new_role_inheritor();

-- Return the org id that the given _object_id falls under. If an org id is not
-- found, throws an exception if the _object_type specifies an object where this
-- is derivable (most objects), otherwise returns null.
create function get_new_acl_object_org_id(
    _object_type acl_object_type, _object_id uuid)
returns uuid
language plpgsql
security invoker
as $$
declare
    _object_org_id uuid;
begin
    if _object_type = 'organization' then
        _object_org_id = _object_id;
    elsif _object_type = 'project' then
        select projects.org_id into _object_org_id
        from projects where projects.id = _object_id;
    elsif _object_type = 'experiment' then
        select projects.org_id into _object_org_id
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _object_id;
    elsif _object_type = 'dataset' then
        select projects.org_id into _object_org_id
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _object_id;
    elsif _object_type = 'prompt' then
        select projects.org_id into _object_org_id
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _object_id;
    elsif _object_type = 'prompt_session' then
        select projects.org_id into _object_org_id
        from projects join prompt_sessions on projects.id = prompt_sessions.project_id
        where prompt_sessions.id = _object_id;
    elsif _object_type = 'project_score' then
        select projects.org_id into _object_org_id
        from projects join project_scores on projects.id = project_scores.project_id
        where project_scores.id = _object_id;
    elsif _object_type = 'project_tag' then
        select projects.org_id into _object_org_id
        from projects join project_tags on projects.id = project_tags.project_id
        where project_tags.id = _object_id;
    elsif _object_type = 'team' then
        select teams.org_id into _object_org_id
        from teams where teams.id = _object_id;
    elsif _object_type = 'role' then
        select roles.org_id into _object_org_id
        from roles where roles.id = _object_id;
        if _object_org_id is null then
            raise exception 'Cannot create ACL on system role object';
        end if;
    end if;
    if _object_org_id is null then
        raise exception 'ACL object id is invalid for % object', _object_type;
    end if;
    return _object_org_id;
end;
$$;

revoke execute on function get_new_acl_object_org_id from public, anon;

create function process_new_acl()
returns trigger
language plpgsql
security invoker
as $$
declare
    _object_org_id uuid;
begin
    -- If _object_org_id is null, the user must have supplied one in the new
    -- row. If it is non-null and the user has supplied one anyways, they must
    -- agree.
    _object_org_id = get_new_acl_object_org_id(new.object_type, new.object_id);
    if _object_org_id is null then
        if new._object_org_id is null then
            raise exception 'Must supply _object_org_id explicitly for non-derivable ACL object %', new.object_type;
        else
            _object_org_id = new._object_org_id;
        end if;
    else
        if new._object_org_id is null then
            new._object_org_id = _object_org_id;
        elsif _object_org_id <> new._object_org_id then
            raise exception 'Derived org id does not match provided org id for % object', new.object_type;
        end if;
    end if;
    if _object_org_id is null or new._object_org_id is null then
        raise exception 'Impossible';
    end if;

    -- Check that the field type enum corresponds to the correct field, and that
    -- exactly one field in each category is set.

    if ((new.user_object_type = 'user' and new.user_id is null) or
        (new.user_object_type = 'team' and new.team_id is null)) then
        raise exception 'user_object_type must correspond to set column';
    end if;
    if ((new.user_id is not null)::int + (new.team_id is not null)::int <> 1) then
        raise exception 'At most one user object column must be set';
    end if;

    if ((new.grant_object_type = 'privilege' and new.privilege is null) or
        (new.grant_object_type = 'role' and new.role_id is null)) then
        raise exception 'grant_object_type must correspond to set column';
    end if;
    if ((new.privilege is not null)::int + (new.role_id is not null)::int <> 1) then
        raise exception 'At most one grant object column must be set';
    end if;

    -- If the role corresponds to a non-system role, its org must match
    -- _object_org_id.
    if new.grant_object_type = 'role' then
        declare
            _role_org_id uuid;
        begin
            select roles.org_id into _role_org_id
            from roles where roles.id = new.role_id;
            if _role_org_id is not null and _role_org_id <> _object_org_id then
                raise exception 'Custom role must belong to same org as object';
            end if;
        end;
    end if;

    return new;
end;
$$;

revoke execute on function process_new_acl from public, anon;

create trigger upsert_acls_trigger
    before insert or update on acls
    for each row execute procedure process_new_acl();

-- In addition to the ground-truth tables, we define "expanded" tables, which
-- reduce the nested information in the ground-truth tables to a more basic form
-- that is efficient to query.

-- An expanded materialization of the team_users table which walks through the
-- team inheritance hierarchy and allows quickly checking whether a user is part
-- of a team.
create table _expanded_team_users(
    team_id uuid not null references teams,
    user_id uuid not null,
    _team_org_id uuid not null,
    primary key (team_id, user_id)
);
alter table _expanded_team_users enable row level security;
-- For quickly identifying which org a team user entry belongs to.
create index on _expanded_team_users(_team_org_id);

-- An expanded materialization of the role_privileges table which walks through the
-- role inheritance hierarchy and allows quickly checking whether a privilege is
-- part of a role.
create table _expanded_role_privileges(
    role_id uuid not null references roles,
    privilege privilege_type not null,
    _role_org_id uuid,
    primary key (role_id, privilege)
);
alter table _expanded_role_privileges enable row level security;
-- For quickly identifying which org a role grant belongs to.
create index on _expanded_role_privileges(_role_org_id);

-- An expanded materialization of the acls table, which joins in both
-- expanded_team_users and expanded_role_privileges to allow quickly querying
-- whether a particular (object, user, grant) tuple exists.
create table _expanded_acls(
    acl_id uuid not null references acls,
    object_type acl_object_type not null,
    object_id uuid not null,
    user_id uuid not null,
    privilege privilege_type not null,
    restrict_object_type acl_object_type,
    _object_org_id uuid not null
);
alter table _expanded_acls enable row level security;
create unique index on _expanded_acls(object_type, object_id, user_id, privilege, restrict_object_type) nulls not distinct;
-- For quickly identifying which org an ACL belongs to.
create index on _expanded_acls(_object_org_id);

-- Functions for maintaining incrementally updated materialized views. These are
-- defined in reverse order of dependency, because updates to the more basic
-- views will need to update the more derived ones.

-- Much of this function's implementation is mirrored in
-- refresh_expanded_acls_by_object, so make sure to keep them in sync.
create function refresh_expanded_acls_by_org(_arg_org_id uuid)
returns void
language plpgsql
security invoker
as $$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_acls
    where _arg_org_id is null or _arg_org_id = _object_org_id;

    -- Rebuild the set of acls, either across the entire table, or scoped to the
    -- acls in _arg_org_id.
    with
    candidate_acls as (
        select *
        from acls
        -- This filter is the same as the statements above.
        where _arg_org_id is null or _arg_org_id = _object_org_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_team_users.user_id expanded_user_id,
            _expanded_role_privileges.privilege expanded_privilege
        from
            candidate_acls
                left join _expanded_team_users using (team_id)
                left join _expanded_role_privileges using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(privilege, expanded_privilege) as privilege,
        restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty team or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and privilege is not null
    )
    -- It's possible that different ACLs boil down to the same basic
    -- representation, so we must remove duplicates.
    insert into _expanded_acls select distinct * from filtered_acls;
end;
$$;

revoke execute on function refresh_expanded_acls_by_org from public, anon;

-- This function is mostly a copy of refresh_expanded_acls_by_org.
create function refresh_expanded_acls_by_object(
    _object_type acl_object_type, _object_id uuid)
returns void
language plpgsql
security invoker
as $$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    delete from _expanded_acls
    where object_type = _object_type and object_id = _object_id;

    with
    candidate_acls as (
        select * from acls
        -- This filter is the same as the statement above.
        where object_type = _object_type and object_id = _object_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_team_users.user_id expanded_user_id,
            _expanded_role_privileges.privilege expanded_privilege
        from
            candidate_acls
                left join _expanded_team_users using (team_id)
                left join _expanded_role_privileges using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(privilege, expanded_privilege) as privilege,
        restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty team or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and privilege is not null
    )
    -- It's possible that different ACLs boil down to the same basic
    -- representation, so we must remove duplicates.
    insert into _expanded_acls select distinct * from filtered_acls;
end;
$$;

revoke execute on function refresh_expanded_acls_by_object from public, anon;

create function refresh_expanded_team_users_by_org(_arg_org_id uuid)
returns void
language plpgsql
security invoker
as $$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to the org_id. If org_id is
    -- null, delete everything.
    delete from _expanded_team_users
    where _arg_org_id is null or _team_org_id = _arg_org_id;

    -- Rebuild the set of team users, either across the entire table, or scoped
    -- to the teams in the given organization.
    with recursive t(team_id, user_id, _team_org_id) as (
        select team_users.team_id, team_users.user_id, team_users._team_org_id
        from team_users join teams on team_users.team_id = teams.id
        where
            (_arg_org_id is null or _team_org_id = _arg_org_id)
            and teams.deleted_at is null
        union
        select team_inheritors.inheritor_team_id team_id, t.user_id, t._team_org_id
        from
            t
            join team_inheritors using (team_id)
            join teams on team_inheritors.inheritor_team_id = teams.id
        where teams.deleted_at is null
    )
    insert into _expanded_team_users select * from t;

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$$;

revoke execute on function refresh_expanded_team_users_by_org from public, anon;

create function refresh_expanded_role_privileges_by_org(_arg_org_id uuid)
returns void
language plpgsql
security invoker
as $$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_role_privileges
    where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles in the given organization.
    --
    -- When building the set of updated role grants, we always include the
    -- system roles to start with, since they can be included in any role.
    --
    -- When updating the grants, we include everything if _arg_org_id is null,
    -- because system grants can change everything (and we would have wiped the
    -- whole table in the delete statement above). Otherwise, we only include
    -- the organization-scoped grants, since organization-scoped roles cannot
    -- affect different organizations or system roles.
    with recursive t(role_id, privilege, _role_org_id) as (
        select role_privileges.role_id, role_privileges.privilege, roles.org_id _role_org_id
        from role_privileges join roles on role_privileges.role_id = roles.id
        where
            (_arg_org_id is null or org_id is null or org_id = _arg_org_id)
            and roles.deleted_at is null
        union
        -- When system roles propagate into organization-scoped roles, we need
        -- to pick the organization-scoped role's org_id for the final entry.
        select
            role_inheritors.inheritor_role_id role_id,
            t.privilege,
            coalesce(t._role_org_id, roles.org_id) _role_org_id
        from
            t
            join role_inheritors on t.role_id = role_inheritors.role_id
            join roles on role_inheritors.inheritor_role_id = roles.id
        where roles.deleted_at is null
    )
    insert into _expanded_role_privileges
    select * from t where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$$;

revoke execute on function refresh_expanded_role_privileges_by_org from public, anon;

-- Triggers for maintaining incrementally updated materialized expansions. These
-- are run after each statement, so they should run after the locking and
-- row-processing triggers installed above.

create function update_teams_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.org_id
        from transition_tbl
    loop
        perform refresh_expanded_team_users_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_teams_trigger_f from public, anon;

create function update_roles_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.org_id
        from transition_tbl
    loop
        perform refresh_expanded_role_privileges_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_roles_trigger_f from public, anon;

create function update_team_members_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct teams.org_id
        from transition_tbl join teams on transition_tbl.team_id = teams.id
    loop
        perform refresh_expanded_team_users_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_team_members_trigger_f from public, anon;

create function update_role_members_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct roles.org_id
        from transition_tbl join roles on transition_tbl.role_id = roles.id
    loop
        perform refresh_expanded_role_privileges_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_role_members_trigger_f from public, anon;

create function update_acls_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct object_type, object_id
        from transition_tbl
    loop
        perform refresh_expanded_acls_by_object(
            object_rec.object_type, object_rec.object_id);
    end loop;
    return null;
end;
$$;

revoke execute on function update_acls_trigger_f from public, anon;

create trigger update_teams_old_trigger
    after update on teams
    referencing old table as transition_tbl
    for each statement execute procedure update_teams_trigger_f();
create trigger update_teams_new_trigger
    after update on teams
    referencing new table as transition_tbl
    for each statement execute procedure update_teams_trigger_f();

create trigger insert_team_users_trigger
    after insert on team_users
    referencing new table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();
create trigger delete_team_users_trigger
    after delete on team_users
    referencing old table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();
create trigger update_team_users_old_trigger
    after update on team_users
    referencing old table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();
create trigger update_team_users_new_trigger
    after update on team_users
    referencing new table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();

create trigger insert_team_inheritors_trigger
    after insert on team_inheritors
    referencing new table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();
create trigger delete_team_inheritors_trigger
    after delete on team_inheritors
    referencing old table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();
create trigger update_team_inheritors_old_trigger
    after update on team_inheritors
    referencing old table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();
create trigger update_team_inheritors_new_trigger
    after update on team_inheritors
    referencing new table as transition_tbl
    for each statement execute procedure update_team_members_trigger_f();

create trigger update_roles_old_trigger
    after update on roles
    referencing old table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();
create trigger update_roles_new_trigger
    after update on roles
    referencing new table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();

create trigger insert_role_privileges_trigger
    after insert on role_privileges
    referencing new table as transition_tbl for each statement
    execute procedure update_role_members_trigger_f();
create trigger delete_role_privileges_trigger
    after delete on role_privileges
    referencing old table as transition_tbl for each statement
    execute procedure update_role_members_trigger_f();
create trigger update_role_privileges_old_trigger
    after update on role_privileges
    referencing old table as transition_tbl for each statement
    execute procedure update_role_members_trigger_f();
create trigger update_role_privileges_new_trigger
    after update on role_privileges
    referencing new table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();

create trigger insert_role_inheritors_trigger
    after insert on role_inheritors
    referencing new table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();
create trigger delete_role_inheritors_trigger
    after delete on role_inheritors
    referencing old table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();
create trigger update_role_inheritors_old_trigger
    after update on role_inheritors
    referencing old table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();
create trigger update_role_inheritors_new_trigger
    after update on role_inheritors
    referencing new table as transition_tbl
    for each statement execute procedure update_role_members_trigger_f();

create trigger insert_acls_trigger
    after insert on acls
    referencing new table as transition_tbl for each statement
    execute procedure update_acls_trigger_f();
create trigger delete_acls_trigger
    after delete on acls
    referencing old table as transition_tbl for each statement
    execute procedure update_acls_trigger_f();
create trigger update_acls_old_trigger
    after update on acls
    referencing old table as transition_tbl for each statement
    execute procedure update_acls_trigger_f();
create trigger update_acls_new_trigger
    after update on acls
    referencing new table as transition_tbl
    for each statement execute procedure update_acls_trigger_f();

-- END SECTION RBAC SCHEMA
