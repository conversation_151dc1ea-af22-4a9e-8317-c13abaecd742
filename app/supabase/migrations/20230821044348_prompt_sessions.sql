create table prompt_sessions (
    id uuid not null primary key DEFAULT uuid_generate_v4(),
    org_id uuid references organizations,
    name text not null,
    description text,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone
);

alter table prompt_sessions enable row level security;
CREATE INDEX ON prompt_sessions (org_id);
CREATE UNIQUE INDEX on prompt_sessions (org_id, name, deleted_at) NULLS NOT DISTINCT;

CREATE OR REPLACE VIEW active_prompt_sessions WITH (security_invoker = on) AS
SELECT *
FROM prompt_sessions
WHERE deleted_at IS NULL;

create policy "Org members can see all prompt sessions."
  on prompt_sessions
  for all using (
    auth.user_id() in (
      select auth_id from
      members
      JOIN users ON members.user_id = users.id
      WHERE members.org_id = prompt_sessions.org_id
    )
  );

CREATE OR REPLACE FUNCTION register_prompt_session(org_name text, session_name text)
RETURNS JSONB
SECURITY INVOKER
AS $$
    INSERT INTO prompt_sessions (org_id, name)
    SELECT organizations.id, register_prompt_session.session_name FROM organizations WHERE organizations.name = register_prompt_session.org_name
    RETURNING jsonb_build_object('id', id, 'name', name, 'org_id', org_id)
$$ LANGUAGE SQL;
