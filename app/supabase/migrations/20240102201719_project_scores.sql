create type score_type as enum ('slider', 'categorical');
create table project_scores (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    created timestamp with time zone default current_timestamp,

    -- The score itself
    name text not null,
    description text,
    score_type score_type NOT NULL,
    categories jsonb
);

create unique index project_scores_project_id_name_idx on project_scores(project_id, name);

-- Let's see if we can implement everything needed for project scores without granular RLS.
-- So just create a generic lockdown rule
alter table project_scores enable row level security;
