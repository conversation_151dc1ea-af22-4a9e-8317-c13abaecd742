-- Add generated columns for event_type and object_type
ALTER TABLE project_automations
ADD COLUMN event_type text GENERATED ALWAYS AS (config->>'event_type') STORED,
ADD COLUMN object_type text GENERATED ALWAYS AS (config->>'object_type') STORED;

-- Create unique index on project_id/object_type for retention
CREATE UNIQUE INDEX project_automations_retention_unique_idx ON project_automations (project_id, object_type) WHERE event_type = 'retention';

-- Update register_project_automation to handle conflicts on the unique index
CREATE OR REPLACE FUNCTION public.register_project_automation(auth_id uuid, project_id uuid, project_automation_name text, description text, config jsonb, update boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_automation_id uuid;
    _found_existing boolean = false;
    _existing_retention_automation_name text;
begin
    if not has_project_acl('project', project_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into project_automations(
        project_id,
        user_id,
        name,
        description,
        config
    )
    values (
        project_id,
        _user_id,
        project_automation_name,
        description,
        config
    )
    on conflict do nothing
    returning id into _project_automation_id
    ;

    if _project_automation_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id
      from project_automations
      where
          project_automations.project_id = register_project_automation.project_id
          and name = register_project_automation.project_automation_name
      into _project_automation_id
      ;

      -- secondary lookup on object_type unique constraint for better error message
      if not found and config->>'event_type' = 'retention' then
        select name
        from project_automations
        where
            project_automations.project_id = register_project_automation.project_id
            and project_automations.event_type = 'retention'
            and project_automations.object_type = register_project_automation.config->>'object_type'
        into _existing_retention_automation_name
        ;

        if found then
          raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format(
            'Project automation %s already exists with this retention configuration', _existing_retention_automation_name
          ));
        end if;
      elsif not found then
        raise exception 'Project automation % likely deleted concurrently with creation', project_automation_name;
      end if;

      if update then
        update project_automations
        set
            user_id = _user_id,
            name = register_project_automation.project_automation_name,
            description = register_project_automation.description,
            config = register_project_automation.config
        where id = _project_automation_id;
      end if;
    end if;

    return (
        select jsonb_build_object('project_automation', project_automations, 'found_existing', _found_existing)
        from project_automations
        where project_automations.id = _project_automation_id
    );
end;
$function$
;
