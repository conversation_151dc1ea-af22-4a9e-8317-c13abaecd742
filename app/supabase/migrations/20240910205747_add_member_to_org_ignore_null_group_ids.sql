CREATE OR REPLACE FUNCTION public.add_member_to_org_unchecked(user_to_add_id uuid, organization_id uuid, initial_group_ids uuid[])
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _added_user_id uuid;
    _initial_group_ids uuid[] := coalesce(initial_group_ids, '{}');
begin
    insert into members(org_id, user_id)
    values (organization_id, user_to_add_id)
    on conflict do nothing
    returning user_id into _added_user_id;

    insert into group_users(group_id, user_id)
    select group_id, user_to_add_id
    from unnest(_initial_group_ids) group_id
    where group_id is not null
    on conflict do nothing;

    return _added_user_id;
end;
$function$
;
