CREATE OR REPLACE FUNCTION public.register_org(auth_id uuid, org_name text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid := get_user_id_by_auth_id(register_org.auth_id);
    _org_id uuid;
begin
    -- Anonymous users cannot create orgs.
    if _user_id is null or _user_id = anon_user_id() then
        raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 401, 'message', 'Unauthorized users cannot create orgs');
    end if;

    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    perform add_member_to_org_unchecked(_user_id, _org_id, array [get_group_id(_org_id, 'Owners')]);
    perform insert_resource_definition(_org_id, determine_resource_tier(_user_id));
    return _org_id;
end;
$function$
;
