CREATE OR REPLACE FUNCTION public.insert_resource_definition(org_id uuid, tier resource_tier)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 1000);
            _insert_row.num_production_log_row_actions := row(7, 1000);
            _insert_row.num_dataset_row_actions := row(7, 1000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'edu' then
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := false;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$function$
;

-- Patch up any existing edu resources.
update resources set forbid_insert_datasets = false where forbid_insert_datasets;
