update acls set restrict_object_type='experiment'
where
    object_type='experiment'
    and object_id in (select id from experiments)
    and user_id=anon_user_id();

CREATE OR REPLACE FUNCTION public.mark_experiment_as_nonpublic(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as nonpublic';
    end if;
    delete from acls
    where id = find_acl_id(
        _object_type => 'experiment',
        _object_id => experiment_id,
        _user_id => anon_user_id(),
        _permission => 'read',
        _restrict_object_type => 'experiment')
    ;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_public(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'create_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as public';
    end if;
    perform register_acl_unchecked(
        _object_type => 'experiment', _object_id => experiment_id,
        _user_id => anon_user_id(), _permission => 'read',
        _restrict_object_type => 'experiment');
end;
$function$
;
