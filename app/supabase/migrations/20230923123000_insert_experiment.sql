-- A UDF to insert an experiment directly with the provided arguments. Returns
-- the newly-inserted row as JSON.
create or replace function insert_experiment(
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version bigint,
    public boolean
)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _experiment_id uuid;
begin
    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, public, user_id)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, public, get_user_id())
    returning
        id into _experiment_id
    ;

    return (
        select to_jsonb(experiments)
        from experiments
        where id = _experiment_id
    );
end;
$$;

-- Redefine experiment registration to use the new insertion routine. Also
-- accept a 'public' argument.
drop function register_experiment(
    text, uuid, text, text, boolean, jsonb, text, text[], uuid, int8);
create or replace function register_experiment(
    project_name text,
    org_id uuid,
    experiment_name text default null,
    description text default null,
    update boolean default false,
    repo_info jsonb default '{}',
    base_experiment text default null,
    -- List of parent commits to determine a base experiment. Commits are
    -- expected to be in order from most to least recent.
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version int8 default null,
    public boolean default false
)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _user_id uuid := get_user_id();
    _project_id uuid := get_or_create_project(project_name, org_id);
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _inserted_experiment jsonb;
begin
    -- Get the base experiment id from a named experiment.
    if base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            id into _conflicting_experiment_id
        from
            active_experiments
        where
            project_id = _project_id
            and name = experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = register_experiment.repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id
        where
            id = _conflicting_experiment_id
        ;
        select to_jsonb(experiments)
        from experiments
        where id = _conflicting_experiment_id
        into _inserted_experiment
        ;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
                select
                    id into _conflicting_experiment_id
                from
                    active_experiments
                where
                    project_id = _project_id
                    and name = _insert_experiment_name
                ;
            else
                _insert_experiment_name := experiment_name;
                -- _conflicting_experiment_id was already assigned in the
                -- previous block.
            end if;

            if _conflicting_experiment_id is not null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
            end if;

            select
                insert_experiment(
                    _project_id, _insert_experiment_name, description,
                    repo_info, _base_exp_id, dataset_id, dataset_version,
                    public)
            into
                _inserted_experiment
            ;
        end;
    end if;

    return (
        select
            jsonb_build_object('experiment', _inserted_experiment, 'project', p)
        from
            projects p
        where
            p.id = _project_id
    );
end;
$$;
