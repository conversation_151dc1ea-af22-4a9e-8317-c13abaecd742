create table org_settings (
    org_id uuid primary key references organizations,
    git_metadata jsonb
);

alter table org_settings enable row level security;

create function sanitize_repo_info(
    repo_info jsonb,
    org_id uuid
)
returns jsonb
language plpgsql
security definer
as $$
declare
    _repo_info jsonb;
    _git_metadata_settings jsonb;
    _fields text[];
begin
    select git_metadata
    from org_settings
    where org_settings.org_id = sanitize_repo_info.org_id
    into _git_metadata_settings;

    if (_git_metadata_settings is null or _git_metadata_settings->>'collect' = 'all') then
        _repo_info := repo_info;
    end if;

    if (_git_metadata_settings->>'collect' = 'some') then
        select array_agg(fields)
        from jsonb_array_elements_text(COALESCE(_git_metadata_settings->'fields', '[]')) fields
        into _fields;

        select jsonb_object_agg(key, value)
        from jsonb_each(repo_info)
        where key = any(_fields)
        into _repo_info;
    end if;

    return _repo_info;
end;
$$;


create or replace function register_experiment(
    auth_id uuid,
    project_name text,
    org_id uuid,
    experiment_name text default null,
    description text default null,
    update boolean default false,
    repo_info jsonb default '{}',
    base_experiment text default null,
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version bigint default null,
    public boolean default false,
    metadata jsonb default null
)
returns jsonb
language plpgsql
security definer
as $$
declare
    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid := get_or_create_project(auth_id, project_name, org_id);
    _repo_info jsonb := sanitize_repo_info(repo_info, org_id);
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Get the base experiment id from a named experiment.
    if base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            id into _conflicting_experiment_id
        from
            active_experiments
        where
            project_id = _project_id
            and name = experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = _repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id,
            metadata = register_experiment.metadata
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                public, metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    public, metadata)
                into _inserted_experiment_id;
            end if;
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$$;
