alter table "public"."datasets" alter column "project_id" set not null;

alter table "public"."prompt_sessions" alter column "project_id" set not null;

alter table "public"."prompts" alter column "project_id" set not null;

CREATE OR REPLACE FUNCTION public.get_user_id_by_auth_id(auth_id uuid)
 RETURNS uuid
 LANGUAGE sql
 STABLE SECURITY DEFINER
AS $function$
SELECT id FROM users WHERE users.auth_id = get_user_id_by_auth_id.auth_id
$function$
;
