-- Drop the existing policy
DROP POLICY IF EXISTS "Users can view their profiles." ON users;
DROP POLICY IF EXISTS "Org members can see their own membership details." ON members;
DROP POLICY IF EXISTS "Org members can update org details if they belong to the team." ON organizations;
DROP POLICY IF EXISTS "Org members can see all prompt sessions." ON prompt_sessions;
DROP POLICY IF EXISTS "Org members can see their own resource counts." ON resource_counts;
DROP POLICY IF EXISTS "Users can view their profiles." ON users;
DROP POLICY IF EXISTS "Org members can see their own membership details." ON members;
DROP POLICY IF EXISTS "Org members can see all org projects." ON projects;
DROP POLICY IF EXISTS "Org members can see all org experiments." ON experiments;
DROP POLICY IF EXISTS "Users can view their api_keys." ON api_keys;
DROP POLICY IF EXISTS "Users can delete their api_keys." ON api_keys;
DROP POLICY IF EXISTS "Org members can see all org datasets." ON datasets;
DROP POLICY IF EXISTS "Org members can see their own resource definitions." ON resources;
DROP POLICY IF EXISTS "Org members can see their own resource counts." ON resource_counts;
DROP POLICY IF EXISTS "Users can submit feedback." ON user_feedback;
DROP POLICY IF EXISTS "Users can only see their tokens." ON tokens;

drop view if exists org_members CASCADE;
drop view if exists me CASCADE;
drop view if exists preview_org_secrets CASCADE;
drop view if exists projects_auth_id CASCADE;
drop view if exists visible_experiments CASCADE;

drop table if exists tokens;

drop function if exists lookup_org_secret(name text);
drop function if exists lookup_org_secret(name text, org_name text);
drop function if exists delete_org_secret(name text);
drop function if exists delete_org_secret(name text, org_name text);
drop function if exists lookup_experiment(experiment_id uuid);
drop function if exists lookup_experiments_by_commits(project_id uuid, commits text[]);
drop function if exists set_org_secret(type text, name text, metadata json, org_name text);
drop function if exists set_org_secret(type text, name text, value text, metadata json, org_name text);
