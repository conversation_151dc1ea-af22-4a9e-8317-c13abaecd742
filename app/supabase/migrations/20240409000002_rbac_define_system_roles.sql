-- Create some system roles.
insert into roles(id, name) values
    ('44bac3ec-5c62-478b-bffd-4cb6db6370c4', 'viewer'),
    ('cd994cdd-4697-4c18-82c6-022133135199', 'editor'),
    ('2f98db42-ae10-426b-8a58-8060732d5cbd', 'admin'),
    ('7c1a37d9-00ac-4e61-b19a-499f4f4b30df', 'owner')
;

-- Assign privileges to the roles.
insert into role_privileges(role_id, privilege) values
    ('44bac3ec-5c62-478b-bffd-4cb6db6370c4', 'read'),
    ('cd994cdd-4697-4c18-82c6-022133135199', 'create'),
    ('cd994cdd-4697-4c18-82c6-022133135199', 'update'),
    ('cd994cdd-4697-4c18-82c6-022133135199', 'delete'),
    ('2f98db42-ae10-426b-8a58-8060732d5cbd', 'create_acls'),
    ('2f98db42-ae10-426b-8a58-8060732d5cbd', 'read_acls'),
    ('2f98db42-ae10-426b-8a58-8060732d5cbd', 'update_acls'),
    ('2f98db42-ae10-426b-8a58-8060732d5cbd', 'delete_acls')
;

-- An editor is a viewer, and an owner is both editor and admin.
insert into role_inheritors(role_id, inheritor_role_id) values
    ('44bac3ec-5c62-478b-bffd-4cb6db6370c4', 'cd994cdd-4697-4c18-82c6-022133135199'),
    ('cd994cdd-4697-4c18-82c6-022133135199', '7c1a37d9-00ac-4e61-b19a-499f4f4b30df'),
    ('2f98db42-ae10-426b-8a58-8060732d5cbd', '7c1a37d9-00ac-4e61-b19a-499f4f4b30df')
;
