-- Custom type definitions for resources.

-- max_over_window is a type that imposes a maximum limit on some resource over
-- a particular time window in days. The time window specifies the number of
-- days into the past from the current day (inclusive).
drop domain if exists max_over_window;
create type max_over_window_underlying as (
    window_size_days int,
    max_value bigint
);
create domain max_over_window as max_over_window_underlying check (
    (value) is null or (
        (value).window_size_days is not null
        and (value).window_size_days > 0
        and (value).max_value is not null
        and (value).max_value >= 0
    )
);

create table if not exists resources (
    org_id uuid not null primary key references organizations,
    -- Controls whether an org has the ability to toggle its experiments from
    -- public to private. If true, they must copy the public experiment to a new
    -- private one.
    forbid_toggle_experiment_public_to_private boolean not null default false,
    -- Controls the number of row actions (inserts, updates, deletes) that can
    -- occur across all private experiments in the org. Row action counts are
    -- tracked in the `resource_counts` table. No limit if null.
    num_private_experiment_row_actions max_over_window
);

alter table resources enable row level security;

create policy "Org members can see their own resource definitions."
  on resources
  for select using (
    auth.user_id() in (
      select users.auth_id
      from members join users on members.user_id = users.id
      where members.org_id = resources.org_id
    )
  );

create table if not exists resource_counts (
    id uuid not null primary key default uuid_generate_v4(),
    org_id uuid not null references resources (org_id),
    -- This is expected to be the textual name of the column in `resources` that
    -- we are counting.
    resource_name text not null,
    date_bucket date,
    shard_key int,
    -- Note: it is important for operations on this resource to be atomic,
    -- commutative, and reversible. It is also important for values to be exact
    -- (no rounding errors). The bigint type satisfies these requirements and
    -- should work for most kinds of counts.
    count bigint
);

create unique index on resource_counts (org_id, resource_name, date_bucket, shard_key) nulls not distinct;

alter table resource_counts enable row level security;

create policy "Org members can see their own resource counts."
  on resource_counts
  for select using (
    auth.user_id() in (
      select users.auth_id
      from members join users on members.user_id = users.id
      where members.org_id = resource_counts.org_id
    )
  );

-- Helper function to return the current date. If there is a row present in
-- '_braintrust_testing_mock_current_date', it will prefer this value, otherwise
-- return the current date.

drop table if exists _braintrust_testing_mock_current_date;
create table _braintrust_testing_mock_current_date (value date);

create or replace function braintrust_current_date()
returns date
language sql
as $$
    with
    actual_current_date as (select current_date as value)
    select
        coalesce(mock_date.value, actual_current_date.value)
    from
        actual_current_date
        left join _braintrust_testing_mock_current_date mock_date
            on true
    limit 1
$$;

-- Function to check/update a resource of type max_over_window. Will update
-- resource_counts even if there is no limit. Will raise an exception if any of
-- the resource counts are exceeded.
--
-- Return schema:
-- {
--     -- The increment of each updated row in the table. This could be used to
--     -- reverse the update in a future (non-transactional) query.
--     [resource_count_id]: number;
-- }

-- Helper types
drop type if exists org_id_and_count_underlying;
create type org_id_and_count_underlying as (
    org_id uuid,
    count bigint
);
create domain org_id_and_count as org_id_and_count_underlying check (
    (value) is not null
    and (value).org_id is not null
    and (value).count is not null
    and (value).count >= 0
);

drop type if exists org_id_and_max_over_window;
create type org_id_and_max_over_window as (
  org_id uuid,
  constraint_value max_over_window
);

drop type if exists inserted_resource_count_row;
create type inserted_resource_count_row as (
  org_id uuid,
  resource_count_id uuid
);

drop type if exists max_value_resource_violation;
create type max_value_resource_violation as (
  org_id uuid,
  total_value bigint,
  max_value bigint
);

create or replace function update_windowed_resource_counts(
    -- Should match the column name in 'resources' that we are working with.
    resource_name_col text,
    num_shards int,
    org_id_and_counts org_id_and_count[]
)
returns jsonb
language plpgsql
security definer
as $$
declare
    _now_date date := braintrust_current_date();
    _resources org_id_and_max_over_window[];
    _inserted_rows inserted_resource_count_row[];
    _violations max_value_resource_violation[];
begin
    -- Check for not null inputs.
    if (resource_name_col is null) then
      raise exception 'Must provide resource_name_col';
    end if;

    if (num_shards is null or num_shards <= 0) then
      raise exception 'Must provide positive num_shards';
    end if;

    if (org_id_and_counts is null) then
      raise exception 'Must provide org_id_and_counts';
    end if;

    -- Grab the resource definition for each org. Upsert defaults for orgs with
    -- no resource definition. Collect these into _resources.

    insert into resources(org_id)
    select org_id
    from unnest(org_id_and_counts)
    order by org_id
    on conflict do nothing
    ;

    -- This query must be dynamically generated because it references
    -- the column in `resources` named by `resource_name_col`.
    execute format(
        'with rows as ('
        ' select row(org_id, %I)::org_id_and_max_over_window r'
        ' from resources '
        ' join unnest($1) using (org_id) '
        ' )'
        ' select array_agg(r) from rows'
        , resource_name_col
    )
    into _resources
    using org_id_and_counts
    ;

    -- Upsert each row in org_id_and_counts. Collect results into
    -- _inserted_rows.
    with
    rows_to_insert as (
    select
        org_id,
        resource_name_col resource_name,
        _now_date date_bucket,
        floor(random() * num_shards) as shard_key,
        count
    from
        unnest(org_id_and_counts)
    ),
    inserted_rows as (
    insert
        into resource_counts(org_id, resource_name, date_bucket, shard_key, count)
    select
        *
    from
        rows_to_insert
    order by
        org_id, resource_name, date_bucket, shard_key
    on conflict
        (org_id, resource_name, date_bucket, shard_key)
    do update set
        count = resource_counts.count + excluded.count
    returning org_id, id
    ),
    inserted_rows_composite as (
    select row(org_id, id)::inserted_resource_count_row r
    from inserted_rows
    )
    select array_agg(r)
    from inserted_rows_composite
    into _inserted_rows
    ;

    -- For error check, compute the final value of each resource, windowed over
    -- the last "window_size_days" days of each resource (ignore resources with
    -- no window size). Join this with the max value in _resources and filter
    -- for rows where total_value > max_value. Aggregate these into an array and
    -- raise an exception if the array is nonempty.
    --
    -- Note: This sum may race with other concurrent resource updates. These
    -- concurrent updates will not see the results of the concurrent uncommitted
    -- transactions, meaning we may let some inserts through which violate the
    -- resource limitation. This should be okay, since it favors the user and
    -- we should trigger the resource violation the next time they insert.
    with
    resource_total_counts as (
    select
        org_id,
        SUM(count) total_value,
        (constraint_value).max_value
    from
        resource_counts
        join unnest(_resources) using (org_id)
    where
        (constraint_value) is not null
        and date_bucket > (_now_date - (constraint_value).window_size_days)
        and date_bucket <= _now_date
    group by
        org_id, max_value
    ),
    violations as (
    select
        row(org_id, total_value, max_value)::max_value_resource_violation r
    from
        resource_total_counts
    where
        total_value > max_value
    )
    select array_agg(r) from violations into _violations
    ;

    if (array_length(_violations, 1) > 0) then
        raise exception 'Violations of resource constraint %: %',
            resource_name_col, _violations;
    end if;

    return (
      select
        coalesce(jsonb_object_agg(resource_count_id, count), '{}')
      from
          unnest(_inserted_rows) t0
          join unnest(org_id_and_counts) t1 using (org_id)
    );
end;
$$;

-- Function to check/update resource counts upon insertion. Throws an exception
-- if any of the resource counts are exceeded.
--
-- Input schema:
-- {
--     experiments: {
--         [experiment_id]: {
--             num_row_actions: number;
--         }
--     }
-- }
--
-- Return schema:
-- {
--     [resource_count_id]: number;
-- }
create or replace function update_resource_counts_for_insert(
    input jsonb,
    num_shards int)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- For 'num_private_experiment_row_actions'
    _num_private_experiment_row_actions_org_id_and_counts org_id_and_count[];
    _num_private_experiment_row_actions_return jsonb;
begin
    -- Process resource 'num_private_experiment_row_actions'.

    -- 1. Grab the set of private experiments out of the input.
    --
    -- 2. Join them up to get their containing org. Collect the number of row
    -- actions per org. Store these into
    -- _num_private_experiment_row_actions_org_id_and_counts.
    --
    -- 3. Invoke `update_windowed_resource_counts` and store the return value
    -- into _num_private_experiment_row_actions_return.
    with
    input_experiments as (
    select
       key::uuid experiment_id,
       (value->'num_row_actions')::bigint num_row_actions
    from
        jsonb_each(coalesce(input->'experiments', '{}'::jsonb))
     ),
     num_row_actions_per_org as (
     select
        row(organizations.id,
            SUM(input_experiments.num_row_actions))
            ::org_id_and_count r
    from
        input_experiments
        join active_experiments on input_experiments.experiment_id = active_experiments.id
        join active_projects on active_experiments.project_id = active_projects.id
        join organizations on active_projects.org_id = organizations.id
    where
        not active_experiments.public
    group by
      organizations.id
    )
    select coalesce(array_agg(r), array [] :: org_id_and_count[])
    from num_row_actions_per_org
    into _num_private_experiment_row_actions_org_id_and_counts
    ;

    select
        update_windowed_resource_counts(
            resource_name_col => 'num_private_experiment_row_actions',
            num_shards => num_shards,
            org_id_and_counts => _num_private_experiment_row_actions_org_id_and_counts)
    into _num_private_experiment_row_actions_return;

    return _num_private_experiment_row_actions_return;
end;
$$;

-- Function to decrement a set of resource counts. Could be used to undo the
-- resource increment after a failed insertion.
--
-- Input schema:
-- {
--     [resource_count_id]: number;
-- }
create or replace function decrement_resource_counts(input jsonb)
returns void
language sql
security definer
as $$
with
input_counts as (
select key::uuid as id, value::bigint as count
from jsonb_each(input)
),
locked_counts as (
select id, resource_counts.count - input_counts.count as new_count
from resource_counts join input_counts using (id)
order by id
for update
)
update resource_counts
set count = locked_counts.new_count
from locked_counts
where resource_counts.id = locked_counts.id
$$;

revoke execute on function update_windowed_resource_counts from public, anon;
revoke execute on function update_resource_counts_for_insert from public, anon;
revoke execute on function decrement_resource_counts from public, anon;
