alter table "public"."organizations" add column "proxy_url" text;

alter table "public"."organizations" add column "realtime_url" text;


create or replace view "public"."organizations_auth_id" as  SELECT organizations.id,
    organizations.name,
    organizations.api_url,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
   FROM ((organizations
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));


create or replace view "public"."projects_auth_id" as  SELECT projects.id,
    projects.name,
    projects.deleted_at,
    organizations.id AS org_id,
    organizations.name AS org_name,
    organizations.api_url,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
   FROM (((projects
     JOIN organizations ON ((projects.org_id = organizations.id)))
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));

create or replace view "public"."active_projects_auth_id" as  SELECT projects_auth_id.id,
    projects_auth_id.name,
    projects_auth_id.deleted_at,
    projects_auth_id.org_id,
    projects_auth_id.org_name,
    projects_auth_id.api_url,
    projects_auth_id.auth_id,
    projects_auth_id.proxy_url,
    projects_auth_id.realtime_url
   FROM projects_auth_id
  WHERE (projects_auth_id.deleted_at IS NULL);
