CREATE OR REPLACE FUNCTION public.get_or_create_project(auth_id uuid, project_name text, org_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_id uuid;
begin
    if not has_organization_acl('project', org_id, 'create', _user_id) then
        raise exception 'User does not have permissions to create a project within this org';
    end if;

    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, _user_id)
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from active_projects projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    else
        perform register_acl_unchecked(
            object_type => 'project', object_id => _project_id,
            user_id => _user_id, role_id => get_owner_role_id());
    end if;
    return _project_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_dataset(user_id uuid, project_id uuid, name text, description text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _forbid_insert_datasets boolean;
    _dataset_id uuid;
begin
    -- Permission check for this org. Default is to permit insertion.
    select resources.forbid_insert_datasets
    from projects join resources using (org_id)
    where projects.id = project_id
    into _forbid_insert_datasets
    ;

    if _forbid_insert_datasets or not has_project_acl('dataset', project_id, 'create', user_id) then
        raise exception 'User does not have permission to create a dataset within this project';
    end if;

    insert into datasets(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _dataset_id
    ;

    return _dataset_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_experiment(user_id uuid, project_id uuid, name text, description text, repo_info jsonb, base_exp_id uuid, dataset_id uuid, dataset_version text, public boolean, metadata jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _experiment_id uuid;
begin
    if not has_project_acl('experiment', project_id, 'create', user_id) then
        raise exception 'User does not have permissions to create an experiment within this project';
    end if;

    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, public, user_id, metadata)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, public, user_id, metadata)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_prompt_session(user_id uuid, project_id uuid, name text, description text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _prompt_session_id uuid;
begin
    if not has_project_acl('prompt_session', project_id, 'create', user_id) then
        raise exception 'User does not have permissions to create a prompt session within this project';
    end if;

    insert into prompt_sessions(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _prompt_session_id
    ;

    return _prompt_session_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_dataset(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, dataset_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_dataset.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    select insert_dataset(
        user_id => _user_id,
        project_id => _project_id,
        name => _dataset_name,
        description => description)
    into _dataset_id;

    if _dataset_id is null then
        -- Duplicate key.
        select id
        from active_datasets datasets
        where
            datasets.project_id = _project_id
            and datasets.name = _dataset_name
        into _dataset_id
        ;
        if not found then
            raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                _dataset_name, _project_id;
        end if;

        if _update then
            -- Check for update permissions on the dataset.
            if not has_under_project_acl('dataset', _dataset_id, _project_id, 'update', _user_id) then
                raise exception 'User does not have permission to update dataset';
            end if;

            update datasets
            set description = register_dataset.description
            where id = _dataset_id;
        end if;
    else
        perform register_acl_unchecked(
            object_type => 'dataset', object_id => _dataset_id,
            user_id => _user_id, role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version text DEFAULT NULL::text, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_experiment.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from active_experiments experiments
        where experiments.id = register_experiment.base_exp_id;
        if not found then
            raise exception 'Base experiment does not exist or user does not have access to it';
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            where
                has_under_project_acl('experiment', active_experiments.id,
                                      _project_id, 'read', _user_id)
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception 'User does not have permissions to read base experiment';
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            active_experiments experiments
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update experiment';
        end if;

        update
            experiments
        set
            description = register_experiment.description,
            repo_info = _repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id,
            metadata = register_experiment.metadata
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                _public, metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    _public, metadata)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                object_type => 'experiment', object_id => _inserted_experiment_id,
                user_id => _user_id, role_id => get_owner_role_id());
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, slug text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _prompt_id uuid;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_prompt.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if not has_project_acl('prompt', _project_id, 'create', _user_id) then
        raise exception 'User does not have permission to create a prompt within this project';
    end if;

    insert into prompts (project_id, user_id, slug)
    values (_project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _prompt_id
    ;

    if _prompt_id is null then
        -- Duplicate key.
        select id
        from active_prompts prompts
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    else
        perform register_acl_unchecked(
            object_type => 'prompt', object_id => _prompt_id,
            user_id => _user_id, role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt_session(auth_id uuid, org_name text, project_name text, session_name text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
  _user_id uuid = get_user_id_by_auth_id(auth_id);
  _org_id uuid;
  _project_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;


    _project_id = get_or_create_project(auth_id, project_name, _org_id);

    select insert_prompt_session(
            user_id => _user_id,
            project_id => _project_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select id
        from active_prompt_sessions prompt_sessions
        where
            project_id = _project_id
            and name = session_name
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    else
        perform register_acl_unchecked(
            object_type => 'prompt_session', object_id => _prompt_session_id,
            user_id => _user_id, role_id => get_owner_role_id());
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$function$
;
