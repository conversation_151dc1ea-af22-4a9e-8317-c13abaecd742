drop function if exists "public"."register_dataset"(auth_id uuid, org_id uuid, project_name text, dataset_name text);

drop function if exists "public"."register_experiment"(auth_id uuid, project_name text, org_id uuid, experiment_name text, description text, update boolean, repo_info jsonb, base_experiment text, ancestor_commits text[], dataset_id uuid, dataset_version bigint, public boolean, metadata jsonb);

create or replace view "public"."projects_auth_id" as  SELECT projects.id,
    projects.name,
    projects.deleted_at,
    organizations.id AS org_id,
    organizations.name AS org_name,
    organizations.api_url,
    users.auth_id
   FROM (((projects
     JOIN organizations ON ((projects.org_id = organizations.id)))
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));

create or replace view "public"."active_projects_auth_id" as  SELECT projects_auth_id.id,
    projects_auth_id.name,
    projects_auth_id.deleted_at,
    projects_auth_id.org_id,
    projects_auth_id.org_name,
    projects_auth_id.api_url,
    projects_auth_id.auth_id
   FROM projects_auth_id
  WHERE (projects_auth_id.deleted_at IS NULL);


CREATE OR REPLACE FUNCTION public.register_dataset(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, dataset_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the user has access to the project.
        select active_projects_auth_id.id into _project_id
        from active_projects_auth_id
        where
            active_projects_auth_id.auth_id = register_dataset.auth_id
            and active_projects_auth_id.id = register_dataset.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    select insert_dataset(
        user_id => _user_id,
        project_id => _project_id,
        name => _dataset_name,
        description => description)
    into _dataset_id;

    if _dataset_id is null then
        -- Duplicate key.
        select id
        from active_datasets datasets
        where
            datasets.project_id = _project_id
            and datasets.name = _dataset_name
        into _dataset_id
        ;
        if not found then
            raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                _dataset_name, _project_id;
        end if;

        if _update then
            update datasets
            set description = register_dataset.description
            where id = _dataset_id;
        end if;
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version bigint DEFAULT NULL::bigint, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the user has access to the project.
        select active_projects_auth_id.id into _project_id
        from active_projects_auth_id
        where
            active_projects_auth_id.auth_id = register_experiment.auth_id
            and active_projects_auth_id.id = register_experiment.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Make sure the user has access to the base experiment.
        select experiments.id into _base_exp_id
        from
            active_experiments experiments
            join projects_auth_id on experiments.project_id = projects_auth_id.id
        where
            projects_auth_id.auth_id = register_experiment.auth_id
            and experiments.id = register_experiment.base_exp_id;
        if not found then
            raise exception 'Base experiment does not exist or user does not have access to it';
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            active_experiments experiments
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and _update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = _repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id,
            metadata = register_experiment.metadata
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                _public, metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    _public, metadata)
                into _inserted_experiment_id;
            end if;
        end;
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$function$
;
