alter type "public"."acl_object_type" add value 'org_member';
alter type "public"."acl_object_type" add value 'project_log';

CREATE OR REPLACE FUNCTION public.get_new_acl_object_org_id(_object_type acl_object_type, _object_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _object_org_id uuid;
begin
    if (_object_type = 'organization' or _object_type = 'org_member') then
        _object_org_id = _object_id;
    elsif (_object_type = 'project' or _object_type = 'project_log') then
        select projects.org_id into _object_org_id
        from projects where projects.id = _object_id;
    elsif _object_type = 'experiment' then
        select projects.org_id into _object_org_id
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _object_id;
    elsif _object_type = 'dataset' then
        select projects.org_id into _object_org_id
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _object_id;
    elsif _object_type = 'prompt' then
        select projects.org_id into _object_org_id
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _object_id;
    elsif _object_type = 'prompt_session' then
        select projects.org_id into _object_org_id
        from projects join prompt_sessions on projects.id = prompt_sessions.project_id
        where prompt_sessions.id = _object_id;
    elsif _object_type = 'project_score' then
        select projects.org_id into _object_org_id
        from projects join project_scores on projects.id = project_scores.project_id
        where project_scores.id = _object_id;
    elsif _object_type = 'project_tag' then
        select projects.org_id into _object_org_id
        from projects join project_tags on projects.id = project_tags.project_id
        where project_tags.id = _object_id;
    elsif _object_type = 'group' then
        select groups.org_id into _object_org_id
        from groups where groups.id = _object_id;
    elsif _object_type = 'role' then
        select roles.org_id into _object_org_id
        from roles where roles.id = _object_id;
        if _object_org_id is null then
            raise exception 'Cannot create ACL on system role object';
        end if;
    end if;
    if _object_org_id is null then
        raise exception 'ACL object id is invalid for % object', _object_type;
    end if;
    return _object_org_id;
end;
$function$
;
