-- Delete all ACLs on project_score and project_tag. This is permissible because
-- we have checked that all current ACLs on those objects are against users who
-- are also part of org-owners, so they will retain permission to those objects.
--
-- It would be good to double-check that this is the case before merging. Make
-- sure both of these queries return true:
--
-- select (select count(*) from acls where object_type='project_score') = (select count(*) from acls where object_type='project_score' and exists(select 1 from group_users where group_id = get_org_owners_group_id(acls._object_org_id) and user_id = acls.user_id));
--
-- select (select count(*) from acls where object_type='project_tag') = (select count(*) from acls where object_type='project_tag' and exists(select 1 from group_users where group_id = get_org_owners_group_id(acls._object_org_id) and user_id = acls.user_id));

delete from acls where object_type='project_score';
delete from acls where object_type='project_tag';
