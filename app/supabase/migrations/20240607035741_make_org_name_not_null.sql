drop function if exists "public"."load_experiment";

drop function if exists "public"."load_project";

drop view if exists "public"."load_experiment_view";

drop view if exists "public"."org_members";

drop view if exists "public"."organizations_auth_id";

drop view if exists "public"."preview_org_secrets";

drop view if exists "public"."projects_auth_id";

drop view if exists "public"."visible_experiments";

drop view if exists "public"."me";

alter table "public"."organizations" alter column "name" set not null;

create or replace view "public"."me" as  SELECT users.id,
    users.auth_id,
    ( SELECT json_agg(jsonb_build_object('id', members.org_id, 'name', organizations.name)) AS json_agg
           FROM (members
             JOIN organizations ON ((members.org_id = organizations.id)))
          WHERE (members.user_id = users.id)) AS organizations
   FROM users
  WHERE (users.auth_id = auth.user_id())
  GROUP BY users.id;


create or replace view "public"."org_members" as  SELECT users.id,
    users.email,
    users.given_name,
    users.family_name,
    other_members.org_id,
    users.avatar_url,
    users.created
   FROM (((me
     JOIN members my_memberships ON ((me.id = my_memberships.user_id)))
     JOIN members other_members ON ((my_memberships.org_id = other_members.org_id)))
     JOIN users ON ((other_members.user_id = users.id)));


create or replace view "public"."organizations_auth_id" with (security_invoker=on) as  SELECT organizations.id,
    organizations.name,
    organizations.api_url,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
   FROM ((organizations
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));


create or replace view "public"."preview_org_secrets" as  SELECT org_secrets.id,
    org_secrets.created,
    org_secrets.org_id,
    org_secrets.name,
        CASE
            WHEN (org_secrets.secret IS NULL) THEN NULL::text
            ELSE '********'::text
        END AS preview_secret,
    organizations.name AS org_name,
    org_secrets.type,
    org_secrets.metadata
   FROM (((secrets.org_secrets org_secrets
     JOIN organizations ON ((org_secrets.org_id = organizations.id)))
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)))
  WHERE (users.auth_id = auth.user_id());


create or replace view "public"."projects_auth_id" with (security_invoker=on) as  SELECT projects.id,
    projects.name,
    projects.deleted_at,
    organizations.id AS org_id,
    organizations.name AS org_name,
    organizations.api_url,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
   FROM (((projects
     JOIN organizations ON ((projects.org_id = organizations.id)))
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));


create or replace view "public"."visible_experiments" as  WITH base AS (
         SELECT projects.name AS project_name,
            organizations.id AS org_id,
            organizations.name AS org_name,
            experiments.id,
            experiments.project_id,
            experiments.name,
            experiments.description,
            experiments.created,
            experiments.repo_info,
            experiments.commit,
            experiments.base_exp_id,
            experiments.deleted_at,
            experiments.dataset_id,
            experiments.dataset_version,
            experiments.user_id,
            experiments.metadata,
            has_under_project_acl('experiment'::acl_object_type, experiments.id, experiments.project_id, 'read'::permission_type, anon_user_id()) AS public
           FROM ((experiments
             JOIN projects ON ((projects.id = experiments.project_id)))
             JOIN organizations ON ((organizations.id = projects.org_id)))
          WHERE ((experiments.deleted_at IS NULL) AND (projects.deleted_at IS NULL))
        )
 SELECT base.project_name,
    base.org_id,
    base.org_name,
    base.id,
    base.project_id,
    base.name,
    base.description,
    base.created,
    base.repo_info,
    base.commit,
    base.base_exp_id,
    base.deleted_at,
    base.dataset_id,
    base.dataset_version,
    base.user_id,
    base.metadata,
    base.public
   FROM base
  WHERE (base.public OR (EXISTS ( SELECT 1
           FROM (members
             JOIN users ON ((users.id = members.user_id)))
          WHERE ((users.auth_id = auth.user_id()) AND (members.org_id = base.org_id)))));


create or replace view "public"."load_experiment_view" as  SELECT v1.project_name,
    v1.org_id,
    v1.org_name,
    v1.id,
    v1.project_id,
    v1.name,
    v1.description,
    v1.created,
    v1.repo_info,
    v1.commit,
    v1.base_exp_id,
    v1.deleted_at,
    v1.dataset_id,
    v1.dataset_version,
    v1.user_id,
    v1.metadata,
    v1.public,
    ( SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name, 'created', v2.created) ORDER BY v2.created DESC) AS jsonb_agg
           FROM visible_experiments v2
          WHERE ((v1.project_id = v2.project_id) AND (v1.id <> v2.id))) AS comparables
   FROM visible_experiments v1;

CREATE OR REPLACE FUNCTION public.load_experiment(org_name text, project_name text, experiment_name text)
 RETURNS SETOF load_experiment_view
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM load_experiment_view experiments
  WHERE
    -- Name check
    experiments.name = load_experiment.experiment_name
    AND project_name = load_experiment.project_name
    AND org_name = load_experiment.org_name
$function$
;

grant execute on function load_experiment to anon;

CREATE OR REPLACE FUNCTION public.load_project(org_name text, project_name text)
 RETURNS SETOF visible_experiments
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM visible_experiments experiments
  WHERE
    -- Name check
    project_name = load_project.project_name
    AND org_name = load_project.org_name
$function$
;

grant execute on function load_project to anon;
