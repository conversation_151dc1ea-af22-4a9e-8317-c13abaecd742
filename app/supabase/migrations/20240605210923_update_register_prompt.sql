drop function if exists "public"."register_prompt"(auth_id uuid, org_id uuid, project_id uuid, project_name text, slug text, update boolean);

CREATE OR REPLACE FUNCTION public.register_prompt(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, slug text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, prompt_id uuid DEFAULT NULL::uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _prompt_id uuid := coalesce(prompt_id, uuid_generate_v4());

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _inserted_prompt_id uuid;
    _found_existing boolean := false;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_prompt.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if not has_project_acl('prompt', _project_id, 'create', _user_id) then
        raise exception 'User does not have permission to create a prompt within this project';
    end if;

    insert into prompts (id, project_id, user_id, slug)
    values (_prompt_id, _project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _inserted_prompt_id
    ;

    if _inserted_prompt_id is null then
        -- Duplicate key.
        _found_existing := true;
        select prompts.id
        from
            prompts
            join projects on prompts.project_id = projects.id
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
            and prompts.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    else
        perform register_acl_unchecked(
            _object_type => 'prompt', _object_id => _prompt_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts, 'found_existing', _found_existing)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$function$
;

revoke execute on function register_prompt from public, anon;
