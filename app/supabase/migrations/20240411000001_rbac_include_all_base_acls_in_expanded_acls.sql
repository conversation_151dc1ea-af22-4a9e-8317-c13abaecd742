drop index if exists "public"."_expanded_acls_object_type_object_id_user_id_privilege_rest_idx";

CREATE UNIQUE INDEX _expanded_acls_acl_id_object_type_object_id_user_id_privile_idx ON public._expanded_acls USING btree (acl_id, object_type, object_id, user_id, privilege, restrict_object_type) NULLS NOT DISTINCT;

CREATE INDEX _expanded_acls_object_type_object_id_user_id_privilege_rest_idx ON public._expanded_acls USING btree (object_type, object_id, user_id, privilege, restrict_object_type);

-- Refresh the _expanded_acls table manually.
select refresh_expanded_acls_by_org(null);
