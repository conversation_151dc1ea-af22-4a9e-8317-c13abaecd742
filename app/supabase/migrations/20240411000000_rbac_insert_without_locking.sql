drop trigger if exists "insert_acls_trigger" on "public"."acls";

drop function if exists "public"."register_acl_unchecked"(object_type acl_object_type, object_id uuid, user_id uuid, team_id uuid, privilege privilege_type, role_id uuid, restrict_object_type acl_object_type);

CREATE OR REPLACE FUNCTION public.find_acl_id(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _team_id uuid DEFAULT NULL::uuid, _privilege privilege_type DEFAULT NULL::privilege_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _user_id_comparator text :=
        case when _user_id isnull
        then '(user_id isnull and $1 isnull)'
        else '(user_id = $1)'
        end;
    _team_id_comparator text :=
        case when _team_id isnull
        then '(team_id isnull and $2 isnull)'
        else '(team_id = $2)'
        end;
    _privilege_comparator text :=
        case when _privilege isnull
        then '(privilege isnull and $3 isnull)'
        else '(privilege = $3)'
        end;
    _role_id_comparator text :=
        case when _role_id isnull
        then '(role_id isnull and $4 isnull)'
        else '(role_id = $4)'
        end;
    _restrict_object_type_comparator text :=
        case when _restrict_object_type isnull
        then '(restrict_object_type isnull and $5 isnull)'
        else '(restrict_object_type = $5)'
        end;

    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;
    _ret uuid;
begin
    if (_user_id is null) = (_team_id is null) then
        raise exception 'Exactly one of user_id and team_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'team';
    else
        _user_object_type = 'user';
    end if;

    if (_privilege is null) = (_role_id is null) then
        raise exception 'Exactly one of privilege and role_id must be non-null';
    elsif _privilege is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'privilege';
    end if;

    execute (
        'select id from acls where' ||
        concat_ws(' and ', _user_id_comparator, _team_id_comparator,
                  _privilege_comparator, _role_id_comparator,
                  _restrict_object_type_comparator) ||
        ' and object_type = $6 and object_id = $7 and user_object_type = $8' ||
        ' and grant_object_type = $9'
    )
    into _ret
    using _user_id, _team_id, _privilege, _role_id, _restrict_object_type,
          _object_type, _object_id, _user_object_type, _grant_object_type;
    return _ret;
end;
$function$
;

revoke execute on function find_acl_id from public, anon;

CREATE OR REPLACE FUNCTION public.register_acl_unchecked(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _team_id uuid DEFAULT NULL::uuid, _privilege privilege_type DEFAULT NULL::privilege_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _acl_id uuid;
    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;

    _ret_obj jsonb;
begin
    if (_user_id is null) = (_team_id is null) then
        raise exception 'Exactly one of user_id and team_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'team';
    else
        _user_object_type = 'user';
    end if;

    if (_privilege is null) = (_role_id is null) then
        raise exception 'Exactly one of privilege and role_id must be non-null';
    elsif _privilege is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'privilege';
    end if;

    insert into acls(
        object_type, object_id, user_object_type, user_id, team_id,
        grant_object_type, privilege, role_id, restrict_object_type)
    values (
        _object_type, _object_id, _user_object_type, _user_id, _team_id,
        _grant_object_type, _privilege, _role_id, _restrict_object_type)
    on conflict do nothing
    returning id into _acl_id;

    if _acl_id is null then
        -- Duplicate key. Just find the ID.
        select find_acl_id(
            _object_type, _object_id, _user_id, _team_id, _privilege, _role_id,
            _restrict_object_type) into _acl_id
        ;
        if not found then
            raise exception 'Acl likely deleted concurrently with creation';
        end if;
    else
        -- It was a new ACL. Insert the expanded acls directly in this
        -- operation, rather than taking a lock and recomputing them from
        -- scratch.
        --
        -- Query generated from the following command:
        --
        -- ./scripts/make_expanded_acls_query.py "id = _acl_id"
        with
        candidate_acls as (
            select *
            from acls
            where id = _acl_id
        ),
        joined_acls as (
            select
                candidate_acls.*,
                _expanded_team_users.user_id expanded_user_id,
                _expanded_role_privileges.privilege expanded_privilege
            from
                candidate_acls
                    left join _expanded_team_users using (team_id)
                    left join _expanded_role_privileges using (role_id)
        ),
        coalesced_acls as (
        select
            id acl_id,
            object_type,
            object_id,
            coalesce(user_id, expanded_user_id) as user_id,
            coalesce(privilege, expanded_privilege) as privilege,
            restrict_object_type,
            _object_org_id
        from
            joined_acls
        ),
        filtered_acls as (
            select * from coalesced_acls
            where
                -- It is possible that the user specifies an empty team or role, in
                -- which case we don't need to include these entries in the expanded
                -- ACLs.
                user_id is not null
                and privilege is not null
        )
        insert into _expanded_acls select * from filtered_acls on conflict do nothing;
    end if;

    return (
        select jsonb_build_object('acl', acls)
        from acls where id = _acl_id
    );
end;
$function$
;

revoke execute on function register_acl_unchecked from public, anon;

CREATE OR REPLACE FUNCTION public.create_org_owners(_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    _org_owners_team_id uuid;
    _owner_role_id uuid;
begin
    -- Create the team.
    insert into teams(org_id, name)
    values (_org_id, 'org-owners')
    on conflict do nothing;

    select get_org_owners_team_id(_org_id) into _org_owners_team_id;
    select get_owner_role_id() into _owner_role_id;

    -- Add an owner ACL for the team on its org.
    perform register_acl_unchecked(
        _object_type => 'organization', _object_id => _org_id,
        _team_id => _org_owners_team_id, _role_id => _owner_role_id);
end;
$function$
;

CREATE OR REPLACE FUNCTION public.get_or_create_project(auth_id uuid, project_name text, org_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_id uuid;
begin
    if not has_organization_acl('project', org_id, 'create', _user_id) then
        raise exception 'User does not have permissions to create a project within this org';
    end if;

    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, _user_id)
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from active_projects projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project', _object_id => _project_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;
    return _project_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_nonpublic(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as nonpublic';
    end if;
    delete from acls
    where id = find_acl_id(
        _object_type => 'experiment',
        _object_id => experiment_id,
        _user_id => anon_user_id(),
        _privilege => 'read')
    ;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_public(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'create_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as public';
    end if;
    perform register_acl_unchecked(
        _object_type => 'experiment', _object_id => experiment_id,
        _user_id => anon_user_id(), _privilege => 'read');
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_object(_object_type acl_object_type, _object_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    delete from _expanded_acls
    where object_type = _object_type and object_id = _object_id;

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "object_type = _object_type and object_id = _object_id"
    with
    candidate_acls as (
        select *
        from acls
        where object_type = _object_type and object_id = _object_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_team_users.user_id expanded_user_id,
            _expanded_role_privileges.privilege expanded_privilege
        from
            candidate_acls
                left join _expanded_team_users using (team_id)
                left join _expanded_role_privileges using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(privilege, expanded_privilege) as privilege,
        restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty team or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and privilege is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_acls
    where _arg_org_id is null or _arg_org_id = _object_org_id;

    -- Rebuild the set of acls, either across the entire table, or scoped to the
    -- acls in _arg_org_id.
    --
    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "_arg_org_id is null or _arg_org_id = _object_org_id"
    with
    candidate_acls as (
        select *
        from acls
        where _arg_org_id is null or _arg_org_id = _object_org_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_team_users.user_id expanded_user_id,
            _expanded_role_privileges.privilege expanded_privilege
        from
            candidate_acls
                left join _expanded_team_users using (team_id)
                left join _expanded_role_privileges using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(privilege, expanded_privilege) as privilege,
        restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty team or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and privilege is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_dataset(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, dataset_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_dataset.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    select insert_dataset(
        user_id => _user_id,
        project_id => _project_id,
        name => _dataset_name,
        description => description)
    into _dataset_id;

    if _dataset_id is null then
        -- Duplicate key.
        select id
        from active_datasets datasets
        where
            datasets.project_id = _project_id
            and datasets.name = _dataset_name
        into _dataset_id
        ;
        if not found then
            raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                _dataset_name, _project_id;
        end if;

        if _update then
            -- Check for update permissions on the dataset.
            if not has_under_project_acl('dataset', _dataset_id, _project_id, 'update', _user_id) then
                raise exception 'User does not have permission to update dataset';
            end if;

            update datasets
            set description = register_dataset.description
            where id = _dataset_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'dataset', _object_id => _dataset_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version text DEFAULT NULL::text, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_experiment.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from active_experiments experiments
        where experiments.id = register_experiment.base_exp_id;
        if not found then
            raise exception 'Base experiment does not exist or user does not have access to it';
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
                    and active_experiments.project_id = _project_id
            where
                has_under_project_acl('experiment', active_experiments.id,
                                      _project_id, 'read', _user_id)
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception 'User does not have permissions to read base experiment';
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            active_experiments experiments
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update experiment';
        end if;

        update
            experiments
        set
            description = register_experiment.description,
            repo_info = _repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id,
            metadata = register_experiment.metadata
        where
            id = _conflicting_experiment_id
        ;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    metadata)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                _object_type => 'experiment', _object_id => _inserted_experiment_id,
                _user_id => _user_id, _role_id => get_owner_role_id());
        end;
    end if;

    if _public then
        perform mark_experiment_as_public(
            experiment_id => _inserted_experiment_id,
            project_id => _project_id,
            performing_user_id => _user_id);
    else
        perform mark_experiment_as_nonpublic(
            experiment_id => _inserted_experiment_id,
            project_id => _project_id,
            performing_user_id => _user_id);
    end if;

    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from projects join active_and_inactive_experiments experiments on projects.id = experiments.project_id
        where experiments.id = _inserted_experiment_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, slug text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _prompt_id uuid;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the project exists.
        select active_projects.id into _project_id from active_projects
        where active_projects.id = register_prompt.project_id;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if not has_project_acl('prompt', _project_id, 'create', _user_id) then
        raise exception 'User does not have permission to create a prompt within this project';
    end if;

    insert into prompts (project_id, user_id, slug)
    values (_project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _prompt_id
    ;

    if _prompt_id is null then
        -- Duplicate key.
        select id
        from active_prompts prompts
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    else
        perform register_acl_unchecked(
            _object_type => 'prompt', _object_id => _prompt_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt_session(auth_id uuid, org_name text, project_name text, session_name text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
  _user_id uuid = get_user_id_by_auth_id(auth_id);
  _org_id uuid;
  _project_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;


    _project_id = get_or_create_project(auth_id, project_name, _org_id);

    select insert_prompt_session(
            user_id => _user_id,
            project_id => _project_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select id
        from active_prompt_sessions prompt_sessions
        where
            project_id = _project_id
            and name = session_name
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'prompt_session', _object_id => _prompt_session_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_privileges privilege_type[] DEFAULT NULL::privilege_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_privileges privilege_type[] = coalesce(member_privileges, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        -- Replace the contents of the existing role, including privileges and
        -- inheritors.
        if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update role';
        end if;

        -- Update roles, privileges, and inheritors with our given contents.

        update roles
        set name = _name, description = _description
        where id = _role_id;

        delete from role_privileges where role_id = _role_id;
        insert into role_privileges(role_id, privilege)
        select _role_id, privilege from unnest(_member_privileges) privilege;

        delete from role_inheritors where inheritor_role_id = _role_id;
        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, privileges, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_privileges(role_id, privilege)
        select _role_id, privilege from unnest(_member_privileges) privilege;

        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('role', active_roles)
        from active_roles
        where id = _role_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_team(auth_id uuid, org_id uuid, team_name text, description text DEFAULT NULL::text, member_users uuid[] DEFAULT NULL::uuid[], member_teams uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = team_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_teams uuid[] = coalesce(member_teams, '{}');
    _update boolean = coalesce(update, false);

    _team_id uuid;
begin
    -- Search for a matching existing team.
    select id into _team_id
    from teams where teams.org_id = _org_id and name = _name and deleted_at is null;

    if _team_id is not null then
        -- Replace the contents of the existing team, including users and
        -- inheritors.
        if not has_under_organization_acl('team', _team_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update team';
        end if;

        -- Update teams, users, and inheritors with our given contents.

        update teams
        set name = _name, description = _description
        where id = _team_id;

        delete from team_users where team_id = _team_id;
        insert into team_users(team_id, user_id)
        select _team_id, user_id from unnest(_member_users) user_id;

        delete from team_inheritors where inheritor_team_id = _team_id;
        insert into team_inheritors(team_id, inheritor_team_id)
        select team_id, _team_id from unnest(_member_teams) team_id;
    else
        if not has_organization_acl('team', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create team';
        end if;

        -- Add teams, users, and inheritors with our given contents.

        insert into teams(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _team_id;

        insert into team_users(team_id, user_id)
        select _team_id, user_id from unnest(_member_users) user_id;

        insert into team_inheritors(team_id, inheritor_team_id)
        select team_id, _team_id from unnest(_member_teams) team_id;

        perform register_acl_unchecked(
            _object_type => 'team', _object_id => _team_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('team', active_teams)
        from active_teams
        where id = _team_id
    );
end;
$function$
;
