create table project_tags (
    id uuid not null primary key default uuid_generate_v4(),
    project_id uuid not null references projects(id),
    user_id uuid not null references users(id),
    created timestamp with time zone default current_timestamp,

    -- The score itself
    name text not null,
    description text,
    color text
);

create unique index project_tags_project_id_name_idx on project_tags(project_id, name);
alter table project_tags enable row level security;
