-- BEGIN SECTION RBAC ORG-OWNERS MIGRATION

-- In order to migrate existing users into the RBAC system, we maintain a team
-- in every org called 'org-owners'. This team is granted 'owner' permissions
-- on their respective org. Every time a user is added/removed from the members
-- table, we add/remove them from the corresponding org-owners team, if it
-- exists. Every time an org is created, we create a corresponding
-- 'org-owners' team.
--
-- An org can migrate off the org-owners system by simply deleting that team.

create function get_owner_role_id()
returns uuid
language sql
security invoker
as $$
    select id from roles where org_id is null and name = 'owner' and deleted_at is null;
$$;

revoke execute on function get_owner_role_id from public, anon;

create function get_org_owners_team_id(_org_id uuid)
returns uuid
language sql
security invoker
as $$
    select id from teams where org_id = _org_id and name = 'org-owners' and deleted_at is null;
$$;

revoke execute on function get_org_owners_team_id from public, anon;

create function create_org_owners(_org_id uuid)
returns void
language plpgsql
security invoker
as $$
declare
    _org_owners_team_id uuid;
    _owner_role_id uuid;
begin
    -- Create the team.
    insert into teams(org_id, name)
    values (_org_id, 'org-owners')
    on conflict do nothing;

    select get_org_owners_team_id(_org_id) into _org_owners_team_id;
    select get_owner_role_id() into _owner_role_id;

    -- Add an owner ACL for the team on its org.
    insert into acls(object_type, object_id, user_object_type, team_id, grant_object_type, role_id)
    values ('organization', _org_id, 'team', _org_owners_team_id, 'role', _owner_role_id)
    on conflict do nothing;
end;
$$;

revoke execute on function create_org_owners from public, anon;

create function create_org_owners_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
begin
    perform create_org_owners(new.id);
    return new;
end;
$$;

revoke execute on function create_org_owners_trigger_f from public, anon;

create trigger create_org_owners_new_org_trigger
    after insert on organizations
    for each row execute procedure create_org_owners_trigger_f();

create function add_member_to_org_owners(_org_id uuid, _user_id uuid)
returns void
language plpgsql
security invoker
as $$
declare
    _org_owners_team_id uuid;
begin
    select get_org_owners_team_id(_org_id) into _org_owners_team_id;
    if _org_owners_team_id is not null then
        insert into team_users(team_id, user_id)
        values (_org_owners_team_id, _user_id)
        on conflict do nothing;
    end if;
end;
$$;

revoke execute on function add_member_to_org_owners from public, anon;

create function add_member_to_org_owners_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
begin
    perform add_member_to_org_owners(new.org_id, new.user_id);
    return new;
end;
$$;

revoke execute on function add_member_to_org_owners_trigger_f from public, anon;

create trigger add_new_member_to_org_owners_trigger
    after insert on members
    for each row execute procedure add_member_to_org_owners_trigger_f();

create function remove_member_from_org_owners(_org_id uuid, _user_id uuid)
returns void
language plpgsql
security invoker
as $$
declare
    _org_owners_team_id uuid;
begin
    select get_org_owners_team_id(_org_id) into _org_owners_team_id;
    if _org_owners_team_id is not null then
        delete from team_users
        where
            team_id = _org_owners_team_id
            and user_id = _user_id;
    end if;
end;
$$;

revoke execute on function remove_member_from_org_owners from public, anon;

create function remove_member_from_org_owners_trigger_f()
returns trigger
language plpgsql
security invoker
as $$
begin
    perform remove_member_from_org_owners(old.org_id, old.user_id);
    return old;
end;
$$;

revoke execute on function remove_member_from_org_owners_trigger_f from public, anon;

create trigger remove_old_member_from_org_owners_trigger
    after delete on members
    for each row execute procedure remove_member_from_org_owners_trigger_f();

-- END SECTION RBAC ORG-OWNERS MIGRATION
