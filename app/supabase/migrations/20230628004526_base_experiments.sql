ALTER TABLE experiments ADD COLUMN "repo_info" jsonb;
ALTER TABLE experiments ADD COLUMN "commit" text GENERATED ALWAYS AS ("repo_info"->>'commit') STORED;
CREATE INDEX commit_idx ON experiments (project_id, commit, created);

ALTER TABLE experiments ADD COLUMN "base_exp_id" uuid;

CREATE OR REPLACE VIEW base_experiments WITH (security_invoker) AS
SELECT sub.*, base_experiment.name AS base_exp_name
FROM (
  SELECT
  id, project_id, name,
  COALESCE(exp.base_exp_id,
  (SELECT base_exp.id FROM experiments base_exp WHERE
    base_exp.project_id = exp.project_id
      AND base_exp.id != exp.id
      AND base_exp.created <= exp.created ORDER BY base_exp.created DESC LIMIT 1)) base_exp_id
  FROM experiments exp
) sub JOIN experiments base_experiment ON base_experiment.id = sub.base_exp_id;

CREATE OR REPLACE FUNCTION lookup_experiments_by_commits(project_id uuid, commits text[])
 RETURNS TABLE (commit text, id uuid, repo_info jsonb, created timestamptz)
 LANGUAGE sql
AS $function$
SELECT
  c AS commit, experiments.id, experiments.repo_info, experiments.created
FROM
  UNNEST(commits) c
  JOIN experiments ON experiments.project_id = lookup_experiments_by_commits.project_id
        AND experiments.commit = c
$function$
;
