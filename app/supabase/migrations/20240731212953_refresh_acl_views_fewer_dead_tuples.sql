drop function refresh_expanded_acls_by_org;
drop function refresh_expanded_acls_by_object;
drop function refresh_expanded_group_members_by_org;
drop function refresh_expanded_role_permissions_by_org;

create function refresh_expanded_acls_by_org(_arg_org_id uuid)
-- The return value is an arbitrary int to ensure the optimizer runs all parts
-- of the query we specify.
returns integer
language plpgsql
security invoker
as $$
declare
    _out integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of acls, either across the entire table, or scoped to the
    -- acls in _arg_org_id.
    --
    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "_arg_org_id is null or _arg_org_id = _object_org_id"
    with
    candidate_acls as (
        select *
        from acls
        where (_arg_org_id is null or _arg_org_id = _object_org_id)
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_members.user_object_type expanded_user_object_type,
            _expanded_group_members.user_group_id expanded_user_group_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_members using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(expanded_user_object_type, user_object_type) as user_object_type,
        coalesce(expanded_user_group_id, user_id) as user_group_id,
        coalesce(expanded_permission, permission) as permission,
        coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    final_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_object_type is not null
            and user_group_id is not null
            and permission is not null
    ),
    deleted_acls as (
        delete from _expanded_acls where
        (_arg_org_id is null or _arg_org_id = _object_org_id)
        and not exists(
            select 1 from final_acls
            where
                _expanded_acls.acl_id = final_acls.acl_id
                and _expanded_acls.object_type = final_acls.object_type
                and _expanded_acls.object_id = final_acls.object_id
                and _expanded_acls.user_object_type = final_acls.user_object_type
                and _expanded_acls.user_group_id = final_acls.user_group_id
                and _expanded_acls.permission = final_acls.permission
                and ((_expanded_acls.restrict_object_type isnull and final_acls.restrict_object_type isnull)
                     or (_expanded_acls.restrict_object_type = final_acls.restrict_object_type))
        )
        returning 1
    ),
    inserted_acls as (
        insert into _expanded_acls select * from final_acls on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out
    from
        (select count(*) cnt from deleted_acls) "num_deleted"
        join (select count(*) cnt from inserted_acls) "num_inserted"
        on true;
    return _out;
end;
$$;

revoke execute on function refresh_expanded_acls_by_org from public, anon;

-- This function is mostly a copy of refresh_expanded_acls_by_org.
create function refresh_expanded_acls_by_object(
    _object_type acl_object_type, _object_id uuid)
returns integer
language plpgsql
security invoker
as $$
declare
    _out integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "object_type = _object_type and object_id = _object_id"
    with
    candidate_acls as (
        select *
        from acls
        where (object_type = _object_type and object_id = _object_id)
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_members.user_object_type expanded_user_object_type,
            _expanded_group_members.user_group_id expanded_user_group_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_members using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(expanded_user_object_type, user_object_type) as user_object_type,
        coalesce(expanded_user_group_id, user_id) as user_group_id,
        coalesce(expanded_permission, permission) as permission,
        coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    final_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_object_type is not null
            and user_group_id is not null
            and permission is not null
    ),
    deleted_acls as (
        delete from _expanded_acls where
        (object_type = _object_type and object_id = _object_id)
        and not exists(
            select 1 from final_acls
            where
                _expanded_acls.acl_id = final_acls.acl_id
                and _expanded_acls.object_type = final_acls.object_type
                and _expanded_acls.object_id = final_acls.object_id
                and _expanded_acls.user_object_type = final_acls.user_object_type
                and _expanded_acls.user_group_id = final_acls.user_group_id
                and _expanded_acls.permission = final_acls.permission
                and ((_expanded_acls.restrict_object_type isnull and final_acls.restrict_object_type isnull)
                     or (_expanded_acls.restrict_object_type = final_acls.restrict_object_type))
        )
        returning 1
    ),
    inserted_acls as (
        insert into _expanded_acls select * from final_acls on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out
    from
        (select count(*) cnt from deleted_acls) "num_deleted"
        join (select count(*) cnt from inserted_acls) "num_inserted"
        on true;
    return _out;
end;
$$;

revoke execute on function refresh_expanded_acls_by_object from public, anon;

create function refresh_expanded_group_members_by_org(_arg_org_id uuid)
-- The return value is an arbitrary int to ensure the optimizer runs all parts
-- of the query we specify.
returns integer
language plpgsql
security invoker
as $$
declare
    _out0 integer;
    _out1 integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of group members, either across the entire table, or
    -- scoped to the groups in the given organization.
    with
    recursive t(group_id, user_object_type, user_group_id, _group_org_id) as (
        (
            select group_users.group_id, 'user'::acl_user_object_type, group_users.user_id, group_users._group_org_id
            from group_users join groups on group_users.group_id = groups.id
            where
                (_arg_org_id is null or _group_org_id = _arg_org_id)
                and groups.deleted_at is null
            union all
            select groups.id, 'group'::acl_user_object_type, groups.id, groups.org_id
            from groups
            where
                (_arg_org_id is null or org_id = _arg_org_id)
                and groups.deleted_at is null
        )
        union
        select group_inheritors.inheritor_group_id, t.user_object_type, t.user_group_id, t._group_org_id
        from
            t
            join group_inheritors using (group_id)
            join groups on group_inheritors.inheritor_group_id = groups.id
        where groups.deleted_at is null
    ),
    final_result_set as (
        -- Note that for the 'user'-type expansions, we want to emit the set of
        -- groups which contain the users in the base-case set. But for the
        -- 'group'-type expansions, we want to emit the set of groups inheriting
        -- from each initial group in the base-case set. So we need to flip the
        -- ordering of the fields for the 'group'-type expansions.
        select * from t where user_object_type = 'user'
        union all
        select user_group_id, user_object_type, group_id, _group_org_id
        from t where user_object_type = 'group'
    ),
    deleted_members as (
        delete from _expanded_group_members where
        (_arg_org_id is null or _group_org_id = _arg_org_id)
        and not exists (
            select 1 from final_result_set
            where
                _expanded_group_members.group_id = final_result_set.group_id
                and _expanded_group_members.user_object_type = final_result_set.user_object_type
                and _expanded_group_members.user_group_id = final_result_set.user_group_id
        )
        returning 1
    ),
    inserted_members as (
        insert into _expanded_group_members select * from final_result_set on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out0
    from
        (select count(*) cnt from deleted_members) "num_deleted"
        join (select count(*) cnt from inserted_members) "num_inserted"
        on true;

    -- Rebuild the set of expanded ACLs for this organization.
    select refresh_expanded_acls_by_org(_arg_org_id) into _out1;

    return _out0 + _out1;
end;
$$;

revoke execute on function refresh_expanded_group_members_by_org from public, anon;

create function refresh_expanded_role_permissions_by_org(_arg_org_id uuid)
returns integer
language plpgsql
security invoker
as $$
declare
    _out0 integer;
    _out1 integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles in the given organization.
    --
    -- When building the set of updated role grants, we always include the
    -- system roles to start with, since they can be included in any role.
    --
    -- When updating the grants, we include everything if _arg_org_id is null,
    -- because system grants can change everything (and we would have wiped the
    -- whole table in the delete statement above). Otherwise, we only include
    -- the organization-scoped grants, since organization-scoped roles cannot
    -- affect different organizations or system roles.
    with recursive t(role_id, permission, _role_org_id, restrict_object_type) as (
        select role_permissions.role_id, role_permissions.permission,
               roles.org_id _role_org_id, role_permissions.restrict_object_type
        from role_permissions join roles on role_permissions.role_id = roles.id
        where
            (_arg_org_id is null or org_id is null or org_id = _arg_org_id)
            and roles.deleted_at is null
        union
        -- When system roles propagate into organization-scoped roles, we need
        -- to pick the organization-scoped role's org_id for the final entry.
        select
            role_inheritors.inheritor_role_id role_id,
            t.permission,
            coalesce(t._role_org_id, roles.org_id) _role_org_id,
            t.restrict_object_type
        from
            t
            join role_inheritors on t.role_id = role_inheritors.role_id
            join roles on role_inheritors.inheritor_role_id = roles.id
        where roles.deleted_at is null
    ),
    final_result_set as (
        select * from t
        where (_arg_org_id is null or _role_org_id = _arg_org_id)
    ),
    deleted_permissions as (
        delete from _expanded_role_permissions where
        (_arg_org_id is null or _role_org_id = _arg_org_id)
        and not exists (
            select 1 from final_result_set
            where
                _expanded_role_permissions.role_id = final_result_set.role_id
                and _expanded_role_permissions.permission = final_result_set.permission
                and (
                    (_expanded_role_permissions.restrict_object_type isnull and final_result_set.restrict_object_type isnull)
                    or (_expanded_role_permissions.restrict_object_type = final_result_set.restrict_object_type)
                )
        )
        returning 1
    ),
    inserted_permissions as (
        insert into _expanded_role_permissions select * from final_result_set
        on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out0
    from
        (select count(*) cnt from deleted_permissions) "num_deleted"
        join (select count(*) cnt from inserted_permissions) "num_inserted"
        on true;

    -- Rebuild the set of expanded ACLs for this organization.
    select refresh_expanded_acls_by_org(_arg_org_id) into _out1;

    return _out0 + _out1;
end;
$$;

revoke execute on function refresh_expanded_role_permissions_by_org from public, anon;
