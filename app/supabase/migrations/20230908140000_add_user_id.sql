ALTER TABLE projects ADD COLUMN user_id uuid REFERENCES users(id);
ALTER TABLE experiments ADD COLUMN user_id uuid REFERENCES users(id);
ALTER TABLE datasets ADD COLUMN user_id uuid REFERENCES users(id);
ALTER TABLE prompt_sessions ADD COLUMN user_id uuid REFERENCES users(id);

-- Redefine any dependent views and UDFs to include the new field.

-- projects

-- experiments

CREATE OR REPLACE VIEW active_experiments WITH (security_invoker = on) AS
SELECT *
FROM experiments
WHERE deleted_at IS NULL;

-- The 'visible_experiments' view created in
-- app/supabase/migrations/20230904174409_public_sharing.sql is difficult to
-- modify because it selects all columns of 'experiments' first, and postgres
-- only allows view modification to add columns to the end of the projection.
--
-- Therefore we delete all objects which depend on visible_experiments and
-- recreate them here.
DROP VIEW visible_experiments CASCADE;

CREATE OR REPLACE VIEW visible_experiments WITH (security_invoker = on) AS
SELECT
  projects.name AS project_name,
  organizations.id AS org_id,
  organizations.name AS org_name,
  experiments.*
FROM active_experiments experiments
JOIN projects ON projects.id = experiments.project_id
JOIN organizations ON organizations.id = projects.org_id
WHERE
  -- Auth check
  (experiments.public = true OR (
    EXISTS (
      SELECT 1
      FROM members
      JOIN users ON users.id = members.user_id
      WHERE
        users.auth_id = auth.user_id()
        AND members.org_id = organizations.id
    )
  ));

CREATE OR REPLACE VIEW load_experiment_view WITH (security_invoker = on) AS
  SELECT v1.*, (SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name)) FROM visible_experiments v2 WHERE v1.project_id = v2.project_id AND v1.id != v2.id) AS comparables FROM visible_experiments v1;
;

CREATE OR REPLACE FUNCTION load_experiment(org_name text, project_name text, experiment_name text)
RETURNS SETOF load_experiment_view
LANGUAGE sql
SECURITY DEFINER SET search_path=public
AS $function$
  SELECT * FROM load_experiment_view experiments
  WHERE
    -- Name check
    experiments.name = load_experiment.experiment_name
    AND project_name = load_experiment.project_name
    AND org_name = load_experiment.org_name
$function$
;

CREATE OR REPLACE FUNCTION lookup_experiment(experiment_id uuid)
RETURNS TABLE (id uuid, name text, project_id uuid)
LANGUAGE sql
SECURITY DEFINER SET search_path=public
AS $function$
  SELECT id, name, project_id FROM visible_experiments experiments
  WHERE
    -- id check
    experiments.id = lookup_experiment.experiment_id
$function$
;

-- datasets

CREATE OR REPLACE VIEW active_datasets WITH (security_invoker = on) AS
SELECT *
FROM datasets
WHERE deleted_at IS NULL;

CREATE OR REPLACE FUNCTION register_dataset(
    org_id uuid, project_name text, dataset_name text DEFAULT 'logs')
RETURNS JSONB
SECURITY INVOKER
AS $$
DECLARE
    _project_id uuid;
    _dataset_id uuid;
    _user_id uuid;
BEGIN
  -- Get the user_id corresponding to our current auth_id.
  SELECT id INTO _user_id
  FROM users
  WHERE auth_id = auth.user_id();

  -- https://stackoverflow.com/questions/35265453/use-insert-on-conflict-do-nothing-returning-failed-rows
  with inserted (id) as (
    INSERT INTO projects (org_id, name, user_id)
    VALUES (register_dataset.org_id, register_dataset.project_name, _user_id)
    ON CONFLICT DO NOTHING
    returning id
  )
  SELECT id INTO _project_id FROM (
    select projects.id AS id
    from projects WHERE projects.org_id = register_dataset.org_id and projects.name = register_dataset.project_name
    union all
    select id
    from inserted
  ) sub;

  with inserted (id) as (
    INSERT INTO datasets (project_id, name, user_id)
    VALUES (_project_id, register_dataset.dataset_name, _user_id)
    ON CONFLICT DO NOTHING
    returning id
  )
  SELECT id INTO _dataset_id FROM (
    select datasets.id AS id
    from active_datasets datasets WHERE datasets.project_id = _project_id and datasets.name = register_dataset.dataset_name
    union all
    select id
    from inserted
  ) sub;

  RETURN (
    SELECT jsonb_build_object('project', projects, 'dataset', datasets)  FROM
    projects JOIN datasets ON projects.id = datasets.project_id
    WHERE datasets.project_id = _project_id AND datasets.id = _dataset_id
  );
END;
$$ LANGUAGE plpgsql;

-- prompt_sessions

CREATE OR REPLACE VIEW active_prompt_sessions WITH (security_invoker = on) AS
SELECT *
FROM prompt_sessions
WHERE deleted_at IS NULL;

CREATE OR REPLACE FUNCTION register_prompt_session(org_name text, session_name text)
RETURNS JSONB
SECURITY INVOKER
AS $$
DECLARE
    _user_id uuid;
    _result jsonb;
BEGIN
    -- Get the user_id corresponding to our current auth_id.
    SELECT id INTO _user_id
    FROM users
    WHERE auth_id = auth.user_id();

    INSERT INTO prompt_sessions (org_id, name, user_id)
    SELECT organizations.id, register_prompt_session.session_name, _user_id FROM organizations WHERE organizations.name = register_prompt_session.org_name
    RETURNING jsonb_build_object('id', id, 'name', name, 'org_id', org_id)
    INTO _result;

    RETURN _result;
END;
$$ LANGUAGE plpgsql;
