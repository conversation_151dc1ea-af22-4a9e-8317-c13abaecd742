alter table "public"."datasets" add column "metadata" jsonb;

create or replace view "public"."active_datasets" with (security_invoker=on) as  SELECT datasets.id,
    datasets.project_id,
    datasets.name,
    datasets.description,
    datasets.created,
    datasets.deleted_at,
    datasets.user_id,
    datasets.metadata
   FROM (datasets
     JOIN projects ON ((datasets.project_id = projects.id)))
  WHERE ((datasets.deleted_at IS NULL) AND (projects.deleted_at IS NULL));
