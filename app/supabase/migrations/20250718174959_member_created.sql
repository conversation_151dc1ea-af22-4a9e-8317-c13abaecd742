alter table members add column created timestamp with time zone;

-- For existing members, set created to the max of organization's created and user's created
-- as a reasonable approximation since we don't have the actual date joined
UPDATE members
SET created = GREATEST(organizations.created, users.created)
FROM organizations, users
WHERE members.org_id = organizations.id
AND members.user_id = users.id
AND members.created IS NULL;

-- Set default value for future inserts
ALTER TABLE members ALTER COLUMN created SET DEFAULT current_timestamp;
