CREATE OR REPLACE FUNCTION public.remove_member_from_org(user_to_remove_id uuid, organization_id uuid, actor_auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    actor_user_id uuid := get_user_id_by_auth_id(actor_auth_id);
begin
    if user_to_remove_id != actor_user_id and not has_under_organization_acl('org_member', organization_id, organization_id, 'delete', actor_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'delete org_member', 'objectType', 'organization', 'objectId', organization_id);
    end if;
    perform remove_member_from_org_unchecked(user_to_remove_id, organization_id);
end;
$function$
;
