create or replace function register_org(
    org_name text
)
returns uuid
language plpgsql
security definer
as $$
declare
    _org_id uuid;
begin
    if (auth.user_id() IS NULL) then
        raise exception 'Must be logged in to register an organization';
    end if;

    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    insert into members(user_id, org_id)
    select id, _org_id
    from users
    where auth_id = auth.user_id();

    perform insert_resource_definition(_org_id, 'free');

    return _org_id;
end;
$$;
