-- Add new fields input_cost_per_mil_tokens and output_cost_per_mil_tokens if input_cost_per_token and output_cost_per_token are defined
UPDATE secrets.org_secrets
SET metadata = jsonb_set(
    metadata::jsonb,
    '{customModels}',
    (
        SELECT jsonb_object_agg(
            key,
            CASE
                WHEN value ? 'input_cost_per_token' AND value->>'input_cost_per_token' IS NOT NULL
                    AND value ? 'output_cost_per_token' AND value->>'output_cost_per_token' IS NOT NULL
                THEN value || jsonb_build_object(
                    'input_cost_per_mil_tokens', (to_char((value->>'input_cost_per_token')::numeric * 1000000, 'FM999999999.999999'))::numeric,
                    'output_cost_per_mil_tokens', (to_char((value->>'output_cost_per_token')::numeric * 1000000, 'FM999999999.999999'))::numeric)
                WHEN value ? 'input_cost_per_token' AND value->>'input_cost_per_token' IS NOT NULL
                THEN value || jsonb_build_object(
                    'input_cost_per_mil_tokens', (to_char((value->>'input_cost_per_token')::numeric * 1000000, 'FM999999999.999999'))::numeric)
                WHEN value ? 'output_cost_per_token' AND value->>'output_cost_per_token' IS NOT NULL
                THEN value || jsonb_build_object(
                    'output_cost_per_mil_tokens', (to_char((value->>'output_cost_per_token')::numeric * 1000000, 'FM999999999.999999'))::numeric)
                ELSE value
            END
        )
        FROM jsonb_each(metadata::jsonb->'customModels')
    )
)::json
WHERE metadata::jsonb ? 'customModels' AND metadata::jsonb->>'customModels' IS NOT NULL;
