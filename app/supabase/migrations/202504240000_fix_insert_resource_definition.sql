CREATE OR REPLACE FUNCTION public.insert_resource_definition(org_id uuid, tier resource_tier)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 250000);
            _insert_row.num_production_log_row_actions := row(7, 250000);
            _insert_row.num_dataset_row_actions := row(7, 250000);
            _insert_row.num_log_bytes := row(7, 250000000);
            _insert_row.num_private_experiment_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_production_log_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_dataset_row_actions_calendar_months := row(1, 1000000);
            _insert_row.num_log_bytes_calendar_months := row(1, 1000000000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$function$
;
