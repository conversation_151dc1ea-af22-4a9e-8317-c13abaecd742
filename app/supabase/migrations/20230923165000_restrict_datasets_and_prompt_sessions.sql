-- Include resource definitions for creating datasets and prompt sessions. Also
-- add one for the SQL explorer (a UI only feature).
alter table resources add column forbid_insert_datasets boolean;
alter table resources add column forbid_insert_prompt_sessions boolean;
alter table resources add column forbid_access_sql_explorer boolean;

-- Define a function for inserting a resource definition for different tiers.
-- Fails if there already exists a resource definition for this org.
create type resource_tier as enum ('free', 'unlimited');
create or replace function insert_resource_definition(
    org_id uuid,
    tier resource_tier
)
returns void
language plpgsql
security definer
as $$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 1000);
            _insert_row.forbid_insert_datasets := true;
            _insert_row.forbid_insert_prompt_sessions := true;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$$;

revoke execute on function insert_resource_definition from public, anon;

CREATE OR REPLACE FUNCTION register_org(
    org_name text
)
RETURNS UUID
LANGUAGE PLPGSQL
SECURITY DEFINER
AS $$
DECLARE
    _org_id uuid;
BEGIN
    IF (auth.user_id() IS NULL) THEN
        RAISE EXCEPTION 'Must be logged in to register an organization';
    END IF;

    INSERT INTO
        organizations(name)
    VALUES
        (org_name)
    RETURNING
        id
    INTO
        _org_id
    ;

    INSERT INTO members(user_id, org_id)
    SELECT id, _org_id
    FROM users
    WHERE auth_id = auth.user_id();

    select insert_resource_definition(_org_id, 'free');

    return _org_id;
END;
$$;

-- Define functions for inserting a new dataset and new prompt session, and
-- redefine the registration functions to use these raw insertion functions.

create or replace function insert_dataset(
    project_id uuid,
    name text,
    description text
)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _forbid_insert_datasets boolean;
    _dataset_id uuid;
begin
    -- Permission check for this org. Default is to permit insertion.
    select resources.forbid_insert_datasets
    from projects join resources using (org_id)
    where projects.id = project_id
    into _forbid_insert_datasets;
    if _forbid_insert_datasets then
        raise exception 'User does not have permission to insert datasets';
    end if;

    insert into datasets(project_id, name, description, user_id)
    values (project_id, name, description, get_user_id())
    returning id into _dataset_id;

    return (
        select to_jsonb(datasets)
        from datasets
        where id = _dataset_id
    );
end;
$$;

create or replace function insert_prompt_session(
    org_id uuid,
    name text,
    description text
)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _forbid_insert_prompt_sessions boolean;
    _prompt_session_id uuid;
begin
    -- Permission check for this org. Default is to permit insertion.
    select resources.forbid_insert_prompt_sessions
    from resources
    where resources.org_id = insert_prompt_session.org_id
    into _forbid_insert_prompt_sessions;
    if _forbid_insert_prompt_sessions then
        raise exception 'User does not have permission to insert prompt_sessions';
    end if;

    insert into prompt_sessions(org_id, name, description, user_id)
    values (org_id, name, description, get_user_id())
    returning id into _prompt_session_id;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where id = _prompt_session_id
    );
end;
$$;

create or replace function register_dataset(
    org_id uuid,
    project_name text,
    dataset_name text default 'logs'
)
returns jsonb
language plpgsql
as $$
declare
    _user_id uuid := get_user_id();
    _project_id uuid := get_or_create_project(project_name, org_id);
    _dataset_result jsonb;
begin
    select to_jsonb(datasets)
    from
        active_datasets datasets
    where
        datasets.project_id = _project_id
        and datasets.name = register_dataset.dataset_name
    into
        _dataset_result
    ;
    if not found then
        select insert_dataset(
            project_id => _project_id,
            name => dataset_name,
            description => null)
        into _dataset_result;
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', _dataset_result)
        from projects
        where projects.id = _project_id
    );
end;
$$;

create or replace function register_prompt_session(
    org_name text,
    session_name text
)
returns jsonb
language plpgsql
as $$
declare
  _org_id uuid;
  _prompt_session_result jsonb;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;

    select
        to_jsonb(prompt_sessions)
    from
        active_prompt_sessions prompt_sessions
    where
        org_id = _org_id
        and name = session_name
    into
        _prompt_session_result
    ;
    if not found then
        select insert_prompt_session(
                org_id => _org_id,
                name => session_name,
                description => null)
        into _prompt_session_result;
    end if;

    return _prompt_session_result;
end;
$$;
