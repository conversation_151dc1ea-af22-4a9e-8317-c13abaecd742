alter table "public"."tokens" add column "auth_id" uuid;

alter table "public"."tokens" add constraint "auth_id_ref" FOREIGN KEY (auth_id) REFERENCES users(auth_id) not valid;

alter table "public"."tokens" validate constraint "auth_id_ref";

create policy "Users can only see their tokens."
on "public"."tokens"
as permissive
for all
to public
using ((auth.user_id() = auth_id));

CREATE VIEW organizations_auth_id AS SELECT
  organizations.id, organizations.name, organizations.api_url, auth_id
  FROM organizations JOIN members on organizations.id = members.org_id JOIN users ON members.user_id = users.id;
