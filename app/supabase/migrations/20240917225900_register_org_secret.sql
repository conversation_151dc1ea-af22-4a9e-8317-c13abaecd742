drop view secrets.decrypted_org_secrets;
alter table secrets.org_secrets alter column metadata set data type jsonb using metadata::jsonb;

CREATE OR REPLACE FUNCTION public.register_org_secret(auth_id uuid, org_id uuid, org_secret_name text, type text DEFAULT NULL::text, metadata jsonb DEFAULT NULL::jsonb, secret text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = org_secret_name;
    _type text = type;
    _metadata jsonb = metadata;
    _secret text = secret;
    _update boolean = coalesce(update, false);

    _org_secret_id uuid;
    _found_existing boolean = false;
begin
    if not has_organization_acl('organization', org_id, 'update', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'organization', 'objectId', org_id);
    end if;

    insert into secrets.org_secrets(org_id, name, type, secret, metadata)
    values (_org_id, _name, _type, _secret, _metadata)
    on conflict do nothing
    returning id into _org_secret_id;

    if _org_secret_id is null then
      -- Duplicate key.
      _found_existing := true;

      select id from secrets.org_secrets
      where
          org_secrets.org_id = _org_id
          and org_secrets.name = _name
      into _org_secret_id;
      if not found then
        raise exception 'Org secret % likely deleted concurrently with creation', _name;
      end if;

      if _update then
        -- Replace the contents of the existing org secrets. We have to be
        -- careful to omit the 'secret' column entirely if it is null, so we
        -- write two separate SQL statements.
        if _secret is null then
          update secrets.org_secrets
          set type = _type, metadata = _metadata
          where id = _org_secret_id;
        else
          update secrets.org_secrets
          set type = _type, metadata = _metadata, secret = _secret
          where id = _org_secret_id;
        end if;
      end if;
    end if;

    return (
        select jsonb_build_object('org_secret', org_secrets, 'found_existing', _found_existing)
        from secrets.org_secrets
        where org_secrets.id = _org_secret_id
    );
end;
$function$
;
revoke execute on function register_org_secret from public, anon;
