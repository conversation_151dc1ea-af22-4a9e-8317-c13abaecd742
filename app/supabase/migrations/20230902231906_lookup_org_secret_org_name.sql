CREATE OR REPLACE FUNCTION lookup_org_secret(name text, org_name text)
 RETURNS text
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  SELECT decrypted_secret FROM decrypted_org_secrets
    JOIN organizations ON decrypted_org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE
      decrypted_org_secrets.name = lookup_org_secret.name
      AND organizations.name = lookup_org_secret.org_name or lookup_org_secret.org_name is null
      AND users.auth_id = auth.user_id()
$function$
;
