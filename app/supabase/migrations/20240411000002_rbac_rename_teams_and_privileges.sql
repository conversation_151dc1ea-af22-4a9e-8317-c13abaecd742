-- Dropping all RBAC-related objects except tables and types.

drop trigger if exists "acquire_acl_lock_role_privileges_trigger" on "public"."role_privileges";
drop trigger if exists "delete_role_privileges_trigger" on "public"."role_privileges";
drop trigger if exists "insert_role_privileges_trigger" on "public"."role_privileges";
drop trigger if exists "update_role_privileges_new_trigger" on "public"."role_privileges";
drop trigger if exists "update_role_privileges_old_trigger" on "public"."role_privileges";
drop trigger if exists "acquire_acl_lock_team_inheritors_trigger" on "public"."team_inheritors";
drop trigger if exists "delete_team_inheritors_trigger" on "public"."team_inheritors";
drop trigger if exists "insert_team_inheritors_trigger" on "public"."team_inheritors";
drop trigger if exists "update_team_inheritors_new_trigger" on "public"."team_inheritors";
drop trigger if exists "update_team_inheritors_old_trigger" on "public"."team_inheritors";
drop trigger if exists "upsert_team_inheritors_trigger" on "public"."team_inheritors";
drop trigger if exists "acquire_acl_lock_team_users_trigger" on "public"."team_users";
drop trigger if exists "delete_team_users_trigger" on "public"."team_users";
drop trigger if exists "insert_team_users_trigger" on "public"."team_users";
drop trigger if exists "update_team_users_new_trigger" on "public"."team_users";
drop trigger if exists "update_team_users_old_trigger" on "public"."team_users";
drop trigger if exists "upsert_team_users_trigger" on "public"."team_users";
drop trigger if exists "acquire_acl_lock_teams_trigger" on "public"."teams";
drop trigger if exists "delete_teams_trigger" on "public"."teams";
drop trigger if exists "update_teams_new_trigger" on "public"."teams";
drop trigger if exists "update_teams_old_trigger" on "public"."teams";
drop trigger if exists "update_teams_trigger" on "public"."teams";
drop view if exists "public"."active_teams";
drop function if exists "public"."check_new_team_inheritor"();
drop function if exists "public"."check_update_team"();
drop function if exists "public"."find_acl_id"(_object_type acl_object_type, _object_id uuid, _user_id uuid, _team_id uuid, _privilege privilege_type, _role_id uuid, _restrict_object_type acl_object_type);
drop function if exists "public"."get_org_owners_team_id"(_org_id uuid);
drop function if exists "public"."has_organization_acl"(_object_type acl_object_type, _org_id uuid, _privilege privilege_type, _user_id uuid);
drop function if exists "public"."has_project_acl"(_object_type acl_object_type, _project_id uuid, _privilege privilege_type, _user_id uuid);
drop function if exists "public"."has_under_organization_acl"(_object_type acl_object_type, _object_id uuid, _org_id uuid, _privilege privilege_type, _user_id uuid);
drop function if exists "public"."process_new_team_user"();
drop function if exists "public"."refresh_expanded_role_privileges_by_org"(_arg_org_id uuid);
drop function if exists "public"."refresh_expanded_team_users_by_org"(_arg_org_id uuid);
drop function if exists "public"."register_acl_unchecked"(_object_type acl_object_type, _object_id uuid, _user_id uuid, _team_id uuid, _privilege privilege_type, _role_id uuid, _restrict_object_type acl_object_type);
drop function if exists "public"."register_role"(auth_id uuid, org_id uuid, role_name text, description text, member_privileges privilege_type[], member_roles uuid[], update boolean);
drop function if exists "public"."register_team"(auth_id uuid, org_id uuid, team_name text, description text, member_users uuid[], member_teams uuid[], update boolean);
drop function if exists "public"."update_team_members_trigger_f"();
drop function if exists "public"."update_teams_trigger_f"();
drop function if exists "public"."load_experiment"(org_name text, project_name text, experiment_name text);
drop function if exists "public"."load_project"(org_name text, project_name text);
drop view if exists "public"."active_base_experiments";
drop view if exists "public"."active_roles";
drop view if exists "public"."base_experiments";
drop view if exists "public"."load_experiment_view";
drop view if exists "public"."visible_experiments";
drop view if exists "public"."active_experiments";
drop view if exists "public"."active_and_inactive_experiments";
drop function if exists "public"."has_under_project_acl"(_object_type acl_object_type, _object_id uuid, _project_id uuid, _privilege privilege_type, _user_id uuid);
alter table "public"."acls" drop constraint "acls_team_id__object_org_id_fkey";
alter table "public"."role_privileges" drop constraint "role_privileges_role_id_fkey";
alter table "public"."team_inheritors" drop constraint "team_inheritors_inheritor_team_id_fkey";
alter table "public"."team_inheritors" drop constraint "team_inheritors_team_id_fkey";
alter table "public"."team_users" drop constraint "team_users__team_org_id_user_id_fkey";
alter table "public"."team_users" drop constraint "team_users_team_id__team_org_id_fkey";
alter table "public"."teams" drop constraint "teams_org_id_fkey";
alter table "public"."teams" drop constraint "teams_user_id_fkey";
alter table "public"."_expanded_role_privileges" drop constraint "_expanded_role_privileges_pkey";
alter table "public"."_expanded_team_users" drop constraint "_expanded_team_users_pkey";
alter table "public"."role_privileges" drop constraint "role_privileges_pkey";
alter table "public"."team_inheritors" drop constraint "team_inheritors_pkey";
alter table "public"."team_users" drop constraint "team_users_pkey";
alter table "public"."teams" drop constraint "teams_pkey";
drop index if exists "public"."_expanded_acls_acl_id_object_type_object_id_user_id_privile_idx";
drop index if exists "public"."_expanded_acls_object_type_object_id_user_id_privilege_rest_idx";
drop index if exists "public"."_expanded_role_privileges__role_org_id_idx";
drop index if exists "public"."_expanded_role_privileges_pkey";
drop index if exists "public"."_expanded_team_users__team_org_id_idx";
drop index if exists "public"."_expanded_team_users_pkey";
drop index if exists "public"."acls_object_type_object_id_user_object_type_user_id_team_id_idx";
drop index if exists "public"."role_privileges_pkey";
drop index if exists "public"."team_inheritors_inheritor_team_id_team_id_idx";
drop index if exists "public"."team_inheritors_pkey";
drop index if exists "public"."team_users_pkey";
drop index if exists "public"."teams_id_org_id_idx";
drop index if exists "public"."teams_org_id_name_deleted_at_idx";
drop index if exists "public"."teams_pkey";

-- Rename types and constituent enum members.
alter type privilege_type rename to permission_type;
alter type acl_object_type rename value 'team' to 'group';
alter type acl_user_object_type rename value 'team' to 'group';
alter type acl_grant_object_type rename value 'privilege' to 'permission';

-- Rename tables and constituent columns.
alter table teams rename to groups;
alter table team_users rename to group_users;
alter table group_users rename column team_id to group_id;
alter table group_users rename column _team_org_id to _group_org_id;
alter table team_inheritors rename to group_inheritors;
alter table group_inheritors rename column team_id to group_id;
alter table group_inheritors rename column inheritor_team_id to inheritor_group_id;
alter table role_privileges rename to role_permissions;
alter table role_permissions rename column privilege to permission;
alter table acls rename column team_id to group_id;
alter table acls rename column privilege to permission;
alter table _expanded_team_users rename to _expanded_group_users;
alter table _expanded_group_users rename column team_id to group_id;
alter table _expanded_group_users rename column _team_org_id to _group_org_id;
alter table _expanded_role_privileges rename to _expanded_role_permissions;
alter table _expanded_role_permissions rename column privilege to permission;
alter table _expanded_acls rename column privilege to permission;

-- Re-add dropped objects.

CREATE UNIQUE INDEX _expanded_acls_acl_id_object_type_object_id_user_id_permiss_idx ON public._expanded_acls USING btree (acl_id, object_type, object_id, user_id, permission, restrict_object_type) NULLS NOT DISTINCT;

CREATE INDEX _expanded_acls_object_type_object_id_user_id_permission_res_idx ON public._expanded_acls USING btree (object_type, object_id, user_id, permission, restrict_object_type);

CREATE INDEX _expanded_group_users__group_org_id_idx ON public._expanded_group_users USING btree (_group_org_id);

CREATE UNIQUE INDEX _expanded_group_users_pkey ON public._expanded_group_users USING btree (group_id, user_id);

CREATE INDEX _expanded_role_permissions__role_org_id_idx ON public._expanded_role_permissions USING btree (_role_org_id);

CREATE UNIQUE INDEX _expanded_role_permissions_pkey ON public._expanded_role_permissions USING btree (role_id, permission);

CREATE UNIQUE INDEX acls_object_type_object_id_user_object_type_user_id_group_i_idx ON public.acls USING btree (object_type, object_id, user_object_type, user_id, group_id, grant_object_type, permission, role_id, restrict_object_type) NULLS NOT DISTINCT;

CREATE INDEX group_inheritors_inheritor_group_id_group_id_idx ON public.group_inheritors USING btree (inheritor_group_id, group_id);

CREATE UNIQUE INDEX group_inheritors_pkey ON public.group_inheritors USING btree (group_id, inheritor_group_id);

CREATE UNIQUE INDEX group_users_pkey ON public.group_users USING btree (group_id, user_id);

CREATE UNIQUE INDEX groups_id_org_id_idx ON public.groups USING btree (id, org_id);

CREATE UNIQUE INDEX groups_org_id_name_deleted_at_idx ON public.groups USING btree (org_id, name, deleted_at) NULLS NOT DISTINCT;

CREATE UNIQUE INDEX groups_pkey ON public.groups USING btree (id);

CREATE UNIQUE INDEX role_permissions_pkey ON public.role_permissions USING btree (role_id, permission);

alter table "public"."_expanded_group_users" add constraint "_expanded_group_users_pkey" PRIMARY KEY using index "_expanded_group_users_pkey";

alter table "public"."_expanded_role_permissions" add constraint "_expanded_role_permissions_pkey" PRIMARY KEY using index "_expanded_role_permissions_pkey";

alter table "public"."group_inheritors" add constraint "group_inheritors_pkey" PRIMARY KEY using index "group_inheritors_pkey";

alter table "public"."group_users" add constraint "group_users_pkey" PRIMARY KEY using index "group_users_pkey";

alter table "public"."groups" add constraint "groups_pkey" PRIMARY KEY using index "groups_pkey";

alter table "public"."role_permissions" add constraint "role_permissions_pkey" PRIMARY KEY using index "role_permissions_pkey";

alter table "public"."acls" add constraint "acls_group_id__object_org_id_fkey" FOREIGN KEY (group_id, _object_org_id) REFERENCES groups(id, org_id) not valid;

alter table "public"."acls" validate constraint "acls_group_id__object_org_id_fkey";

alter table "public"."group_inheritors" add constraint "group_inheritors_group_id_fkey" FOREIGN KEY (group_id) REFERENCES groups(id) not valid;

alter table "public"."group_inheritors" validate constraint "group_inheritors_group_id_fkey";

alter table "public"."group_inheritors" add constraint "group_inheritors_inheritor_group_id_fkey" FOREIGN KEY (inheritor_group_id) REFERENCES groups(id) not valid;

alter table "public"."group_inheritors" validate constraint "group_inheritors_inheritor_group_id_fkey";

alter table "public"."group_users" add constraint "group_users__group_org_id_user_id_fkey" FOREIGN KEY (_group_org_id, user_id) REFERENCES members(org_id, user_id) not valid;

alter table "public"."group_users" validate constraint "group_users__group_org_id_user_id_fkey";

alter table "public"."group_users" add constraint "group_users_group_id__group_org_id_fkey" FOREIGN KEY (group_id, _group_org_id) REFERENCES groups(id, org_id) not valid;

alter table "public"."group_users" validate constraint "group_users_group_id__group_org_id_fkey";

alter table "public"."groups" add constraint "groups_org_id_fkey" FOREIGN KEY (org_id) REFERENCES organizations(id) not valid;

alter table "public"."groups" validate constraint "groups_org_id_fkey";

alter table "public"."groups" add constraint "groups_user_id_fkey" FOREIGN KEY (user_id) REFERENCES users(id) not valid;

alter table "public"."groups" validate constraint "groups_user_id_fkey";

alter table "public"."role_permissions" add constraint "role_permissions_role_id_fkey" FOREIGN KEY (role_id) REFERENCES roles(id) not valid;

alter table "public"."role_permissions" validate constraint "role_permissions_role_id_fkey";

create or replace view "public"."active_groups" with (security_invoker=on) as  WITH all_member_users AS (
         SELECT group_users.group_id AS id,
            array_agg(group_users.user_id) AS member_users
           FROM group_users
          GROUP BY group_users.group_id
        ), all_member_groups AS (
         SELECT group_inheritors.inheritor_group_id AS id,
            array_agg(group_inheritors.group_id) AS member_groups
           FROM (group_inheritors
             JOIN groups groups_1 ON ((group_inheritors.group_id = groups_1.id)))
          WHERE (groups_1.deleted_at IS NULL)
          GROUP BY group_inheritors.inheritor_group_id
        )
 SELECT groups.id,
    groups.org_id,
    groups.user_id,
    groups.created,
    groups.name,
    groups.description,
    groups.deleted_at,
    COALESCE(all_member_users.member_users, '{}'::uuid[]) AS member_users,
    COALESCE(all_member_groups.member_groups, '{}'::uuid[]) AS member_groups
   FROM ((groups
     LEFT JOIN all_member_users USING (id))
     LEFT JOIN all_member_groups USING (id))
  WHERE (groups.deleted_at IS NULL);


create or replace view "public"."active_roles" with (security_invoker=on) as  WITH all_member_permissions AS (
         SELECT role_permissions.role_id AS id,
            (array_agg(role_permissions.permission))::text[] AS member_permissions
           FROM role_permissions
          GROUP BY role_permissions.role_id
        ), all_member_roles AS (
         SELECT role_inheritors.inheritor_role_id AS id,
            array_agg(role_inheritors.role_id) AS member_roles
           FROM (role_inheritors
             JOIN roles roles_1 ON ((role_inheritors.role_id = roles_1.id)))
          WHERE (roles_1.deleted_at IS NULL)
          GROUP BY role_inheritors.inheritor_role_id
        )
 SELECT roles.id,
    roles.org_id,
    roles.user_id,
    roles.created,
    roles.name,
    roles.description,
    roles.deleted_at,
    COALESCE(all_member_permissions.member_permissions, '{}'::text[]) AS member_permissions,
    COALESCE(all_member_roles.member_roles, '{}'::uuid[]) AS member_roles
   FROM ((roles
     LEFT JOIN all_member_permissions USING (id))
     LEFT JOIN all_member_roles USING (id))
  WHERE (roles.deleted_at IS NULL);


CREATE OR REPLACE FUNCTION public.check_new_group_inheritor()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    _group_org_id uuid;
    _inheritor_group_org_id uuid;
begin
    select groups.org_id into _group_org_id
    from groups where groups.id = new.group_id;
    select groups.org_id into _inheritor_group_org_id
    from groups where groups.id = new.inheritor_group_id;

    if _group_org_id <> _inheritor_group_org_id then
        raise exception 'Both groups must belong to the same organization';
    end if;

    return new;
end;
$function$
;

revoke execute on function check_new_group_inheritor from public, anon;

CREATE OR REPLACE FUNCTION public.check_update_group()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
begin
    if old.org_id is distinct from new.org_id then
        raise exception 'Cannot modify org_id of group';
    end if;
    return new;
end;
$function$
;

revoke execute on function check_update_group from public, anon;

CREATE OR REPLACE FUNCTION public.find_acl_id(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _group_id uuid DEFAULT NULL::uuid, _permission permission_type DEFAULT NULL::permission_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _user_id_comparator text :=
        case when _user_id isnull
        then '(user_id isnull and $1 isnull)'
        else '(user_id = $1)'
        end;
    _group_id_comparator text :=
        case when _group_id isnull
        then '(group_id isnull and $2 isnull)'
        else '(group_id = $2)'
        end;
    _permission_comparator text :=
        case when _permission isnull
        then '(permission isnull and $3 isnull)'
        else '(permission = $3)'
        end;
    _role_id_comparator text :=
        case when _role_id isnull
        then '(role_id isnull and $4 isnull)'
        else '(role_id = $4)'
        end;
    _restrict_object_type_comparator text :=
        case when _restrict_object_type isnull
        then '(restrict_object_type isnull and $5 isnull)'
        else '(restrict_object_type = $5)'
        end;

    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;
    _ret uuid;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    execute (
        'select id from acls where' ||
        concat_ws(' and ', _user_id_comparator, _group_id_comparator,
                  _permission_comparator, _role_id_comparator,
                  _restrict_object_type_comparator) ||
        ' and object_type = $6 and object_id = $7 and user_object_type = $8' ||
        ' and grant_object_type = $9'
    )
    into _ret
    using _user_id, _group_id, _permission, _role_id, _restrict_object_type,
          _object_type, _object_id, _user_object_type, _grant_object_type;
    return _ret;
end;
$function$
;

revoke execute on function find_acl_id from public, anon;

CREATE OR REPLACE FUNCTION public.get_org_owners_group_id(_org_id uuid)
 RETURNS uuid
 LANGUAGE sql
AS $function$
    select id from groups where org_id = _org_id and name = 'org-owners' and deleted_at is null;
$function$
;

revoke execute on function get_org_owners_group_id from public, anon;

CREATE OR REPLACE FUNCTION public.has_organization_acl(_object_type acl_object_type, _org_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
AS $function$
select exists(
    select 1 from _expanded_acls
    where (
        object_type = 'organization'
        and object_id = _org_id
        and user_id = _user_id
        and permission = _permission
        and (restrict_object_type is null or restrict_object_type = _object_type)
    )
);
$function$
;

revoke execute on function has_organization_acl from public, anon;

CREATE OR REPLACE FUNCTION public.has_project_acl(_object_type acl_object_type, _project_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
AS $function$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$function$
;

revoke execute on function has_project_acl from public, anon;

CREATE OR REPLACE FUNCTION public.has_under_organization_acl(_object_type acl_object_type, _object_id uuid, _org_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
AS $function$
select exists(
    select 1 from _expanded_acls
    where (
        (
            object_type = _object_type
            and object_id = _object_id
            and user_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
        or (
            object_type = 'organization'
            and object_id = _org_id
            and user_id = _user_id
            and permission = _permission
            and (restrict_object_type is null or restrict_object_type = _object_type)
        )
    )
);
$function$
;

revoke execute on function has_under_organization_acl from public, anon;

CREATE OR REPLACE FUNCTION public.has_under_project_acl(_object_type acl_object_type, _object_id uuid, _project_id uuid, _permission permission_type, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
AS $function$
select exists(
    select 1 from projects
    where
        projects.id = _project_id
        and exists (
            select 1 from _expanded_acls
            where (
                (
                    object_type = _object_type
                    and object_id = _object_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'project'
                    and object_id = _project_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
                or (
                    object_type = 'organization'
                    and object_id = projects.org_id
                    and user_id = _user_id
                    and permission = _permission
                    and (restrict_object_type is null or restrict_object_type = _object_type)
                )
            )
        )
);
$function$
;

revoke execute on function has_under_project_acl from public, anon;

create or replace view "public"."active_and_inactive_experiments" with (security_invoker=on) as  SELECT experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.user_id,
    experiments.metadata,
    has_under_project_acl('experiment'::acl_object_type, experiments.id, experiments.project_id, 'read'::permission_type, anon_user_id()) AS public
   FROM experiments;


create or replace view "public"."active_experiments" with (security_invoker=on) as  SELECT experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.user_id,
    experiments.metadata,
    experiments.public
   FROM (active_and_inactive_experiments experiments
     JOIN active_projects ON ((active_projects.id = experiments.project_id)))
  WHERE (experiments.deleted_at IS NULL);


create or replace view "public"."base_experiments" with (security_invoker=on) as  SELECT sub.id,
    sub.project_id,
    sub.name,
    sub.base_exp_id,
    base_experiment.name AS base_exp_name
   FROM (( SELECT exp.id,
            exp.project_id,
            exp.name,
            COALESCE(exp.base_exp_id, ( SELECT base_exp.id
                   FROM active_experiments base_exp
                  WHERE ((base_exp.project_id = exp.project_id) AND (base_exp.id <> exp.id) AND (base_exp.created <= exp.created))
                  ORDER BY base_exp.created DESC
                 LIMIT 1)) AS base_exp_id
           FROM active_experiments exp) sub
     JOIN active_experiments base_experiment ON ((base_experiment.id = sub.base_exp_id)));


create or replace view "public"."visible_experiments" as  SELECT projects.name AS project_name,
    organizations.id AS org_id,
    organizations.name AS org_name,
    experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.user_id,
    experiments.metadata,
    experiments.public
   FROM ((active_experiments experiments
     JOIN active_projects projects ON ((projects.id = experiments.project_id)))
     JOIN organizations ON ((organizations.id = projects.org_id)))
  WHERE ((experiments.public = true) OR (EXISTS ( SELECT 1
           FROM (members
             JOIN users ON ((users.id = members.user_id)))
          WHERE ((users.auth_id = auth.user_id()) AND (members.org_id = organizations.id)))));


create or replace view "public"."active_base_experiments" with (security_invoker=on) as  SELECT base_experiments.id,
    base_experiments.project_id,
    base_experiments.name,
    base_experiments.base_exp_id,
    base_experiments.base_exp_name
   FROM base_experiments;


create or replace view "public"."load_experiment_view" as  SELECT v1.project_name,
    v1.org_id,
    v1.org_name,
    v1.id,
    v1.project_id,
    v1.name,
    v1.description,
    v1.created,
    v1.repo_info,
    v1.commit,
    v1.base_exp_id,
    v1.deleted_at,
    v1.dataset_id,
    v1.dataset_version,
    v1.user_id,
    v1.metadata,
    v1.public,
    ( SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name, 'created', v2.created) ORDER BY v2.created DESC) AS jsonb_agg
           FROM visible_experiments v2
          WHERE ((v1.project_id = v2.project_id) AND (v1.id <> v2.id))) AS comparables
   FROM visible_experiments v1;


CREATE OR REPLACE FUNCTION public.load_experiment(org_name text, project_name text, experiment_name text)
 RETURNS SETOF load_experiment_view
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM load_experiment_view experiments
  WHERE
    -- Name check
    experiments.name = load_experiment.experiment_name
    AND project_name = load_experiment.project_name
    AND org_name = load_experiment.org_name
$function$
;

grant execute on function load_experiment to anon;

CREATE OR REPLACE FUNCTION public.load_project(org_name text, project_name text)
 RETURNS SETOF visible_experiments
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT * FROM visible_experiments experiments
  WHERE
    -- Name check
    project_name = load_project.project_name
    AND org_name = load_project.org_name
$function$
;

grant execute on function load_project to anon;

CREATE OR REPLACE FUNCTION public.process_new_group_user()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
begin
    select groups.org_id into new._group_org_id
    from groups where groups.id = new.group_id;
    return new;
end;
$function$
;

revoke execute on function process_new_group_user from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_group_users_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to the org_id. If org_id is
    -- null, delete everything.
    delete from _expanded_group_users
    where _arg_org_id is null or _group_org_id = _arg_org_id;

    -- Rebuild the set of group users, either across the entire table, or scoped
    -- to the groups in the given organization.
    with recursive t(group_id, user_id, _group_org_id) as (
        select group_users.group_id, group_users.user_id, group_users._group_org_id
        from group_users join groups on group_users.group_id = groups.id
        where
            (_arg_org_id is null or _group_org_id = _arg_org_id)
            and groups.deleted_at is null
        union
        select group_inheritors.inheritor_group_id group_id, t.user_id, t._group_org_id
        from
            t
            join group_inheritors using (group_id)
            join groups on group_inheritors.inheritor_group_id = groups.id
        where groups.deleted_at is null
    )
    insert into _expanded_group_users select * from t;

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$function$
;

revoke execute on function refresh_expanded_group_users_by_org from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_role_permissions_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_role_permissions
    where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles in the given organization.
    --
    -- When building the set of updated role grants, we always include the
    -- system roles to start with, since they can be included in any role.
    --
    -- When updating the grants, we include everything if _arg_org_id is null,
    -- because system grants can change everything (and we would have wiped the
    -- whole table in the delete statement above). Otherwise, we only include
    -- the organization-scoped grants, since organization-scoped roles cannot
    -- affect different organizations or system roles.
    with recursive t(role_id, permission, _role_org_id) as (
        select role_permissions.role_id, role_permissions.permission, roles.org_id _role_org_id
        from role_permissions join roles on role_permissions.role_id = roles.id
        where
            (_arg_org_id is null or org_id is null or org_id = _arg_org_id)
            and roles.deleted_at is null
        union
        -- When system roles propagate into organization-scoped roles, we need
        -- to pick the organization-scoped role's org_id for the final entry.
        select
            role_inheritors.inheritor_role_id role_id,
            t.permission,
            coalesce(t._role_org_id, roles.org_id) _role_org_id
        from
            t
            join role_inheritors on t.role_id = role_inheritors.role_id
            join roles on role_inheritors.inheritor_role_id = roles.id
        where roles.deleted_at is null
    )
    insert into _expanded_role_permissions
    select * from t where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$function$
;

revoke execute on function refresh_expanded_role_permissions_by_org from public, anon;

CREATE OR REPLACE FUNCTION public.register_acl_unchecked(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _group_id uuid DEFAULT NULL::uuid, _permission permission_type DEFAULT NULL::permission_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _acl_id uuid;
    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;

    _ret_obj jsonb;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    insert into acls(
        object_type, object_id, user_object_type, user_id, group_id,
        grant_object_type, permission, role_id, restrict_object_type)
    values (
        _object_type, _object_id, _user_object_type, _user_id, _group_id,
        _grant_object_type, _permission, _role_id, _restrict_object_type)
    on conflict do nothing
    returning id into _acl_id;

    if _acl_id is null then
        -- Duplicate key. Just find the ID.
        select find_acl_id(
            _object_type, _object_id, _user_id, _group_id, _permission, _role_id,
            _restrict_object_type) into _acl_id
        ;
        if not found then
            raise exception 'Acl likely deleted concurrently with creation';
        end if;
    else
        -- It was a new ACL. Insert the expanded acls directly in this
        -- operation, rather than taking a lock and recomputing them from
        -- scratch.
        --
        -- Query generated from the following command:
        --
        -- ./scripts/make_expanded_acls_query.py "id = _acl_id"
        with
        candidate_acls as (
            select *
            from acls
            where id = _acl_id
        ),
        joined_acls as (
            select
                candidate_acls.*,
                _expanded_group_users.user_id expanded_user_id,
                _expanded_role_permissions.permission expanded_permission
            from
                candidate_acls
                    left join _expanded_group_users using (group_id)
                    left join _expanded_role_permissions using (role_id)
        ),
        coalesced_acls as (
        select
            id acl_id,
            object_type,
            object_id,
            coalesce(user_id, expanded_user_id) as user_id,
            coalesce(permission, expanded_permission) as permission,
            restrict_object_type,
            _object_org_id
        from
            joined_acls
        ),
        filtered_acls as (
            select * from coalesced_acls
            where
                -- It is possible that the user specifies an empty group or role, in
                -- which case we don't need to include these entries in the expanded
                -- ACLs.
                user_id is not null
                and permission is not null
        )
        insert into _expanded_acls select * from filtered_acls on conflict do nothing;
    end if;

    return (
        select jsonb_build_object('acl', acls)
        from acls where id = _acl_id
    );
end;
$function$
;

revoke execute on function register_acl_unchecked from public, anon;

CREATE OR REPLACE FUNCTION public.register_group(auth_id uuid, org_id uuid, group_name text, description text DEFAULT NULL::text, member_users uuid[] DEFAULT NULL::uuid[], member_groups uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = group_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_groups uuid[] = coalesce(member_groups, '{}');
    _update boolean = coalesce(update, false);

    _group_id uuid;
begin
    -- Search for a matching existing group.
    select id into _group_id
    from groups where groups.org_id = _org_id and name = _name and deleted_at is null;

    if _group_id is not null then
        -- Replace the contents of the existing group, including users and
        -- inheritors.
        if not has_under_organization_acl('group', _group_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update group';
        end if;

        -- Update groups, users, and inheritors with our given contents.

        update groups
        set name = _name, description = _description
        where id = _group_id;

        delete from group_users where group_id = _group_id;
        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        delete from group_inheritors where inheritor_group_id = _group_id;
        insert into group_inheritors(group_id, inheritor_group_id)
        select group_id, _group_id from unnest(_member_groups) group_id;
    else
        if not has_organization_acl('group', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create group';
        end if;

        -- Add groups, users, and inheritors with our given contents.

        insert into groups(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _group_id;

        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        insert into group_inheritors(group_id, inheritor_group_id)
        select group_id, _group_id from unnest(_member_groups) group_id;

        perform register_acl_unchecked(
            _object_type => 'group', _object_id => _group_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('group', active_groups)
        from active_groups
        where id = _group_id
    );
end;
$function$
;

revoke execute on function register_group from public, anon;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_permissions permission_type[] DEFAULT NULL::permission_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        -- Replace the contents of the existing role, including permissions and
        -- inheritors.
        if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update role';
        end if;

        -- Update roles, permissions, and inheritors with our given contents.

        update roles
        set name = _name, description = _description
        where id = _role_id;

        delete from role_permissions where role_id = _role_id;
        insert into role_permissions(role_id, permission)
        select _role_id, permission from unnest(_member_permissions) permission;

        delete from role_inheritors where inheritor_role_id = _role_id;
        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission)
        select _role_id, permission from unnest(_member_permissions) permission;

        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('role', active_roles)
        from active_roles
        where id = _role_id
    );
end;
$function$
;

revoke execute on function register_role from public, anon;

CREATE OR REPLACE FUNCTION public.update_group_members_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct groups.org_id
        from transition_tbl join groups on transition_tbl.group_id = groups.id
    loop
        perform refresh_expanded_group_users_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$function$
;

revoke execute on function update_group_members_trigger_f from public, anon;

CREATE OR REPLACE FUNCTION public.update_groups_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.org_id
        from transition_tbl
    loop
        perform refresh_expanded_group_users_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$function$
;

revoke execute on function update_groups_trigger_f from public, anon;

CREATE OR REPLACE FUNCTION public.add_member_to_org_owners(_org_id uuid, _user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    _org_owners_group_id uuid;
begin
    select get_org_owners_group_id(_org_id) into _org_owners_group_id;
    if _org_owners_group_id is not null then
        insert into group_users(group_id, user_id)
        values (_org_owners_group_id, _user_id)
        on conflict do nothing;
    end if;
end;
$function$
;

revoke execute on function add_member_to_org_owners from public, anon;

CREATE OR REPLACE FUNCTION public.create_org_owners(_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    _org_owners_group_id uuid;
    _owner_role_id uuid;
begin
    -- Create the group.
    insert into groups(org_id, name)
    values (_org_id, 'org-owners')
    on conflict do nothing;

    select get_org_owners_group_id(_org_id) into _org_owners_group_id;
    select get_owner_role_id() into _owner_role_id;

    -- Add an owner ACL for the group on its org.
    perform register_acl_unchecked(
        _object_type => 'organization', _object_id => _org_id,
        _group_id => _org_owners_group_id, _role_id => _owner_role_id);
end;
$function$
;

revoke execute on function create_org_owners from public, anon;

CREATE OR REPLACE FUNCTION public.get_new_acl_object_org_id(_object_type acl_object_type, _object_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _object_org_id uuid;
begin
    if _object_type = 'organization' then
        _object_org_id = _object_id;
    elsif _object_type = 'project' then
        select projects.org_id into _object_org_id
        from projects where projects.id = _object_id;
    elsif _object_type = 'experiment' then
        select projects.org_id into _object_org_id
        from projects join experiments on projects.id = experiments.project_id
        where experiments.id = _object_id;
    elsif _object_type = 'dataset' then
        select projects.org_id into _object_org_id
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _object_id;
    elsif _object_type = 'prompt' then
        select projects.org_id into _object_org_id
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _object_id;
    elsif _object_type = 'prompt_session' then
        select projects.org_id into _object_org_id
        from projects join prompt_sessions on projects.id = prompt_sessions.project_id
        where prompt_sessions.id = _object_id;
    elsif _object_type = 'project_score' then
        select projects.org_id into _object_org_id
        from projects join project_scores on projects.id = project_scores.project_id
        where project_scores.id = _object_id;
    elsif _object_type = 'project_tag' then
        select projects.org_id into _object_org_id
        from projects join project_tags on projects.id = project_tags.project_id
        where project_tags.id = _object_id;
    elsif _object_type = 'group' then
        select groups.org_id into _object_org_id
        from groups where groups.id = _object_id;
    elsif _object_type = 'role' then
        select roles.org_id into _object_org_id
        from roles where roles.id = _object_id;
        if _object_org_id is null then
            raise exception 'Cannot create ACL on system role object';
        end if;
    end if;
    if _object_org_id is null then
        raise exception 'ACL object id is invalid for % object', _object_type;
    end if;
    return _object_org_id;
end;
$function$
;

revoke execute on function get_new_acl_object_org_id from public, anon;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_nonpublic(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as nonpublic';
    end if;
    delete from acls
    where id = find_acl_id(
        _object_type => 'experiment',
        _object_id => experiment_id,
        _user_id => anon_user_id(),
        _permission => 'read')
    ;
end;
$function$
;

revoke execute on function mark_experiment_as_nonpublic from public, anon;

CREATE OR REPLACE FUNCTION public.mark_experiment_as_public(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'create_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as public';
    end if;
    perform register_acl_unchecked(
        _object_type => 'experiment', _object_id => experiment_id,
        _user_id => anon_user_id(), _permission => 'read');
end;
$function$
;

revoke execute on function mark_experiment_as_public from public, anon;

CREATE OR REPLACE FUNCTION public.process_new_acl()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    _object_org_id uuid;
begin
    -- If _object_org_id is null, the user must have supplied one in the new
    -- row. If it is non-null and the user has supplied one anyways, they must
    -- agree.
    _object_org_id = get_new_acl_object_org_id(new.object_type, new.object_id);
    if _object_org_id is null then
        if new._object_org_id is null then
            raise exception 'Must supply _object_org_id explicitly for non-derivable ACL object %', new.object_type;
        else
            _object_org_id = new._object_org_id;
        end if;
    else
        if new._object_org_id is null then
            new._object_org_id = _object_org_id;
        elsif _object_org_id <> new._object_org_id then
            raise exception 'Derived org id does not match provided org id for % object', new.object_type;
        end if;
    end if;
    if _object_org_id is null or new._object_org_id is null then
        raise exception 'Impossible';
    end if;

    -- Check that the field type enum corresponds to the correct field, and that
    -- exactly one field in each category is set.

    if ((new.user_object_type = 'user' and new.user_id is null) or
        (new.user_object_type = 'group' and new.group_id is null)) then
        raise exception 'user_object_type must correspond to set column';
    end if;
    if ((new.user_id is not null)::int + (new.group_id is not null)::int <> 1) then
        raise exception 'At most one user object column must be set';
    end if;

    if ((new.grant_object_type = 'permission' and new.permission is null) or
        (new.grant_object_type = 'role' and new.role_id is null)) then
        raise exception 'grant_object_type must correspond to set column';
    end if;
    if ((new.permission is not null)::int + (new.role_id is not null)::int <> 1) then
        raise exception 'At most one grant object column must be set';
    end if;

    -- If the role corresponds to a non-system role, its org must match
    -- _object_org_id.
    if new.grant_object_type = 'role' then
        declare
            _role_org_id uuid;
        begin
            select roles.org_id into _role_org_id
            from roles where roles.id = new.role_id;
            if _role_org_id is not null and _role_org_id <> _object_org_id then
                raise exception 'Custom role must belong to same org as object';
            end if;
        end;
    end if;

    return new;
end;
$function$
;

revoke execute on function process_new_acl from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_object(_object_type acl_object_type, _object_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    delete from _expanded_acls
    where object_type = _object_type and object_id = _object_id;

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "object_type = _object_type and object_id = _object_id"
    with
    candidate_acls as (
        select *
        from acls
        where object_type = _object_type and object_id = _object_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_users.user_id expanded_user_id,
            _expanded_role_permissions.permission expanded_permission
        from
            candidate_acls
                left join _expanded_group_users using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(permission, expanded_permission) as permission,
        restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and permission is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

revoke execute on function refresh_expanded_acls_by_object from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_acls
    where _arg_org_id is null or _arg_org_id = _object_org_id;

    -- Rebuild the set of acls, either across the entire table, or scoped to the
    -- acls in _arg_org_id.
    --
    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "_arg_org_id is null or _arg_org_id = _object_org_id"
    with
    candidate_acls as (
        select *
        from acls
        where _arg_org_id is null or _arg_org_id = _object_org_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_users.user_id expanded_user_id,
            _expanded_role_permissions.permission expanded_permission
        from
            candidate_acls
                left join _expanded_group_users using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(permission, expanded_permission) as permission,
        restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and permission is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

revoke execute on function refresh_expanded_acls_by_org from public, anon;

CREATE OR REPLACE FUNCTION public.remove_member_from_org_owners(_org_id uuid, _user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    _org_owners_group_id uuid;
begin
    select get_org_owners_group_id(_org_id) into _org_owners_group_id;
    if _org_owners_group_id is not null then
        delete from group_users
        where
            group_id = _org_owners_group_id
            and user_id = _user_id;
    end if;
end;
$function$
;

revoke execute on function remove_member_from_org_owners from public, anon;

CREATE OR REPLACE FUNCTION public.update_role_members_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct roles.org_id
        from transition_tbl join roles on transition_tbl.role_id = roles.id
    loop
        perform refresh_expanded_role_permissions_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$function$
;

revoke execute on function update_role_members_trigger_f from public, anon;

CREATE OR REPLACE FUNCTION public.update_roles_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.org_id
        from transition_tbl
    loop
        perform refresh_expanded_role_permissions_by_org(object_rec.org_id);
    end loop;
    return null;
end;
$function$
;

revoke execute on function update_roles_trigger_f from public, anon;

CREATE TRIGGER acquire_acl_lock_group_inheritors_trigger BEFORE INSERT OR DELETE OR UPDATE ON public.group_inheritors FOR EACH STATEMENT EXECUTE FUNCTION acquire_acl_lock_trigger_f();

CREATE TRIGGER delete_group_inheritors_trigger AFTER DELETE ON public.group_inheritors REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER insert_group_inheritors_trigger AFTER INSERT ON public.group_inheritors REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER update_group_inheritors_new_trigger AFTER UPDATE ON public.group_inheritors REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER update_group_inheritors_old_trigger AFTER UPDATE ON public.group_inheritors REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER upsert_group_inheritors_trigger BEFORE INSERT OR UPDATE ON public.group_inheritors FOR EACH ROW EXECUTE FUNCTION check_new_group_inheritor();

CREATE TRIGGER acquire_acl_lock_group_users_trigger BEFORE INSERT OR DELETE OR UPDATE ON public.group_users FOR EACH STATEMENT EXECUTE FUNCTION acquire_acl_lock_trigger_f();

CREATE TRIGGER delete_group_users_trigger AFTER DELETE ON public.group_users REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER insert_group_users_trigger AFTER INSERT ON public.group_users REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER update_group_users_new_trigger AFTER UPDATE ON public.group_users REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER update_group_users_old_trigger AFTER UPDATE ON public.group_users REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_group_members_trigger_f();

CREATE TRIGGER upsert_group_users_trigger BEFORE INSERT OR UPDATE ON public.group_users FOR EACH ROW EXECUTE FUNCTION process_new_group_user();

CREATE TRIGGER acquire_acl_lock_groups_trigger BEFORE INSERT OR DELETE OR UPDATE ON public.groups FOR EACH STATEMENT EXECUTE FUNCTION acquire_acl_lock_trigger_f();

CREATE TRIGGER delete_groups_trigger AFTER DELETE ON public.groups REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_groups_trigger_f();

CREATE TRIGGER update_groups_new_trigger AFTER UPDATE ON public.groups REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_groups_trigger_f();

CREATE TRIGGER update_groups_old_trigger AFTER UPDATE ON public.groups REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_groups_trigger_f();

CREATE TRIGGER update_groups_trigger BEFORE UPDATE ON public.groups FOR EACH ROW EXECUTE FUNCTION check_update_group();

CREATE TRIGGER acquire_acl_lock_role_permissions_trigger BEFORE INSERT OR DELETE OR UPDATE ON public.role_permissions FOR EACH STATEMENT EXECUTE FUNCTION acquire_acl_lock_trigger_f();

CREATE TRIGGER delete_role_permissions_trigger AFTER DELETE ON public.role_permissions REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER insert_role_permissions_trigger AFTER INSERT ON public.role_permissions REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER update_role_permissions_new_trigger AFTER UPDATE ON public.role_permissions REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER update_role_permissions_old_trigger AFTER UPDATE ON public.role_permissions REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();
