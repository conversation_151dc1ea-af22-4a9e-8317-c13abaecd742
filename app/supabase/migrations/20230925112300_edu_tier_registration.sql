alter type resource_tier add value 'edu';
create or replace function insert_resource_definition(
    org_id uuid,
    tier resource_tier
)
returns void
language plpgsql
security definer
as $$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 1000);
            _insert_row.forbid_insert_datasets := true;
            _insert_row.forbid_insert_prompt_sessions := true;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'edu' then
            _insert_row.forbid_insert_datasets := true;
            _insert_row.forbid_insert_prompt_sessions := true;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$$;

create or replace function determine_resource_tier(
    user_id uuid)
returns resource_tier
language plpgsql
security invoker
as $$
declare
    _user_email text;
begin
    select email
    from users
    where id = user_id
    into _user_email;

    if not found then
        raise exception 'User id % does not exist or you do not have access', user_id;
    end if;

    -- If the user's email ends in '.edu', use the 'edu' tier. Otherwise use the
    -- 'free' tier.
    if trim(trailing from _user_email) like '%.edu' then
        return 'edu';
    else
        return 'free';
    end if;
end;
$$;

create or replace function register_org(
    org_name text
)
returns uuid
language plpgsql
security definer
as $$
declare
    _org_id uuid;
begin
    if (auth.user_id() IS NULL) then
        raise exception 'Must be logged in to register an organization';
    end if;

    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    insert into members(user_id, org_id)
    select id, _org_id
    from users
    where auth_id = auth.user_id();

    perform insert_resource_definition(_org_id, determine_resource_tier(get_user_id()));

    return _org_id;
end;
$$;
