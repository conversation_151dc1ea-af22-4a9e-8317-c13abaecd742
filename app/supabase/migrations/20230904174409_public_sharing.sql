
-- NOTE: I expect us to make this more sophisticated in the future, but for now opt for simplicity/clarity
ALTER TABLE experiments ADD COLUMN public boolean NOT NULL DEFAULT false;

CREATE OR REPLACE VIEW active_experiments WITH (security_invoker = on) AS
SELECT *
FROM experiments
WHERE deleted_at IS NULL;


-- This is defined as if anyone could query these experiments, but since it's restricted by
-- security_invoker=on, anonymous users still can't scan the table
CREATE OR REPLACE VIEW visible_experiments WITH (security_invoker = on) AS
SELECT
  experiments.*,
  projects.name AS project_name,
  organizations.id AS org_id,
  organizations.name AS org_name
FROM active_experiments experiments
JOIN projects ON projects.id = experiments.project_id
JOIN organizations ON organizations.id = projects.org_id
WHERE
  -- Auth check
  (experiments.public = true OR (
    EXISTS (
      SELECT 1
      FROM members
      JOIN users ON users.id = members.user_id
      WHERE
        users.auth_id = auth.user_id()
        AND members.org_id = organizations.id
    )
  ));

CREATE OR REPLACE VIEW load_experiment_view WITH (security_invoker = on) AS
  SELECT v1.*, (SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name)) FROM visible_experiments v2 WHERE v1.project_id = v2.project_id AND v1.id != v2.id) AS comparables FROM visible_experiments v1;
;

CREATE OR REPLACE FUNCTION load_experiment(org_name text, project_name text, experiment_name text)
RETURNS SETOF load_experiment_view
LANGUAGE sql
SECURITY DEFINER SET search_path=public
AS $function$
  SELECT * FROM load_experiment_view experiments
  WHERE
    -- Name check
    experiments.name = load_experiment.experiment_name
    AND project_name = load_experiment.project_name
    AND org_name = load_experiment.org_name
$function$
;

CREATE OR REPLACE FUNCTION lookup_experiment(experiment_id uuid)
RETURNS TABLE (id uuid, name text, project_id uuid)
LANGUAGE sql
SECURITY DEFINER SET search_path=public
AS $function$
  SELECT id, name, project_id FROM visible_experiments experiments
  WHERE
    -- id check
    experiments.id = lookup_experiment.experiment_id
$function$
;

-- TODO Implement for datasets too
