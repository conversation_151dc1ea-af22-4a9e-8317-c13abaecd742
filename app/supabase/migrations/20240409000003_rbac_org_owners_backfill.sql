-- Backfill existing orgs/users into the org-owners system.

insert into teams(org_id, name)
select id org_id, 'org-owners'
from organizations
on conflict do nothing;

insert into acls(object_type, object_id, user_object_type, team_id, grant_object_type, role_id)
select
    'organization' object_type,
    teams.org_id object_id,
    'team' user_object_type,
    teams.id team_id,
    'role',
    get_owner_role_id() role_id
from
    teams
where
    name = 'org-owners'
on conflict do nothing
;

insert into team_users(team_id, user_id)
select teams.id team_id, members.user_id
from members join teams on members.org_id = teams.org_id
where teams.name = 'org-owners'
on conflict do nothing;
