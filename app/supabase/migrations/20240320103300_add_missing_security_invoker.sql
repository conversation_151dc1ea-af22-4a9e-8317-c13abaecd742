create or replace view decrypted_org_secrets with (security_invoker=on) as
 SELECT org_secrets.id,
    org_secrets.created,
    org_secrets.key_id,
    org_secrets.nonce,
    org_secrets.org_id,
    org_secrets.name,
    org_secrets.secret,
        CASE
            WHEN (org_secrets.secret IS NULL) THEN NULL::text
            ELSE
            CASE
                WHEN (org_secrets.key_id IS NULL) THEN NULL::text
                ELSE convert_from(pgsodium.crypto_aead_det_decrypt(decode(org_secrets.secret, 'base64'::text), convert_to(((org_secrets.org_id)::text || org_secrets.name), 'utf8'::name), org_secrets.key_id, org_secrets.nonce), 'utf8'::name)
            END
        END AS decrypted_secret,
    org_secrets.type,
    org_secrets.metadata
   FROM org_secrets;

create or replace view organizations_auth_id with (security_invoker=on) as
 SELECT organizations.id,
    organizations.name,
    organizations.api_url,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
   FROM ((organizations
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));

create or replace view active_experiments with (security_invoker=on) as
 SELECT experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.public,
    experiments.user_id,
    experiments.metadata
   FROM (experiments
     JOIN active_projects ON ((active_projects.id = experiments.project_id)))
  WHERE (experiments.deleted_at IS NULL);

create or replace view projects_auth_id with (security_invoker=on) as
 SELECT projects.id,
    projects.name,
    projects.deleted_at,
    organizations.id AS org_id,
    organizations.name AS org_name,
    organizations.api_url,
    users.auth_id,
    organizations.proxy_url,
    organizations.realtime_url
   FROM (((projects
     JOIN organizations ON ((projects.org_id = organizations.id)))
     JOIN members ON ((organizations.id = members.org_id)))
     JOIN users ON ((members.user_id = users.id)));

create or replace view base_experiments with (security_invoker=on) as
 SELECT sub.id,
    sub.project_id,
    sub.name,
    sub.base_exp_id,
    base_experiment.name AS base_exp_name
   FROM (( SELECT exp.id,
            exp.project_id,
            exp.name,
            COALESCE(exp.base_exp_id, ( SELECT base_exp.id
                   FROM active_experiments base_exp
                  WHERE ((base_exp.project_id = exp.project_id) AND (base_exp.id <> exp.id) AND (base_exp.created <= exp.created))
                  ORDER BY base_exp.created DESC
                 LIMIT 1)) AS base_exp_id
           FROM active_experiments exp) sub
     JOIN active_experiments base_experiment ON ((base_experiment.id = sub.base_exp_id)));

create or replace view active_base_experiments with (security_invoker=on) as
 SELECT base_experiments.id,
    base_experiments.project_id,
    base_experiments.name,
    base_experiments.base_exp_id,
    base_experiments.base_exp_name
   FROM base_experiments;

create or replace view active_projects_auth_id with (security_invoker=on) as
 SELECT projects_auth_id.id,
    projects_auth_id.name,
    projects_auth_id.deleted_at,
    projects_auth_id.org_id,
    projects_auth_id.org_name,
    projects_auth_id.api_url,
    projects_auth_id.auth_id,
    projects_auth_id.proxy_url,
    projects_auth_id.realtime_url
   FROM projects_auth_id
  WHERE (projects_auth_id.deleted_at IS NULL);

create or replace view experiment_conflicts with (security_invoker=on) as
 SELECT e1.name,
    e1.project_id,
    ( SELECT experiments.name
           FROM experiments
          WHERE ((experiments.name ~~ concat(regexp_replace(e1.name, '-\d+$'::text, ''::text), '%')) AND (experiments.name ~ '-\d+$'::text) AND (e1.project_id = experiments.project_id))
          ORDER BY (lpad("substring"(experiments.name, '-(\d+)$'::text), 10)) DESC
         LIMIT 1) AS highest_conflict
   FROM experiments e1;
