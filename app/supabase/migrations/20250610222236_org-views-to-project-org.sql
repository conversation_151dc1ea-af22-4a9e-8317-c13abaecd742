CREATE OR REPLACE FUNCTION public.valid_object_type_view_type_check(object_type acl_object_type, view_type view_type)
 RETURNS boolean
 LANGUAGE plpgsql
 IMMUTABLE PARALLEL SAFE
AS $function$
begin
    return (select (
        case
          when object_type = 'org_project' then view_type in ('projects', 'monitor')
          when object_type = 'project' then view_type in (
              'logs',
              'experiments',
              'datasets',
              'prompts',
              'playgrounds',
              'experiment',
              'dataset',
              'playground',
              'tools',
              'scorers'
          )
          when object_type = 'experiment' then view_type in ('experiment')
          when object_type = 'dataset' then view_type in ('dataset')
          else false
        end
    ));
end;
$function$
;

UPDATE views
SET object_type = 'org_project'
WHERE object_type = 'organization';
