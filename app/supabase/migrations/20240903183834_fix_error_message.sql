CREATE OR REPLACE FUNCTION public.get_or_create_project(auth_id uuid, project_name text, org_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_id uuid;
begin
    -- Try to find the project. If found, check if we have the appropriate
    -- permissions and then return it.
    select id
    from projects
    where
        projects.name = project_name
        and projects.org_id = get_or_create_project.org_id
        and projects.deleted_at isnull
    into _project_id
    ;

    if _project_id is not null then
      if not has_project_acl('project', _project_id, 'read', _user_id) then
          raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', project_name);
      end if;
      return _project_id;
    end if;

    -- Project does not exist. Check if we have permissions to create it, and
    -- then do that.

    if not has_under_organization_acl('org_project', org_id, org_id, 'create', _user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create', 'objectType', 'org_project', 'objectId', org_id);
    end if;

    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, _user_id)
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
            and projects.deleted_at isnull
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project', _object_id => _project_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;
    return _project_id;
end;
$function$
;
