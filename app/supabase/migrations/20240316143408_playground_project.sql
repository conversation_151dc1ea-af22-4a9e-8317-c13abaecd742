ALTER TABLE prompt_sessions ADD COLUMN project_id uuid references projects;

-- Create a new project for existing playground sessions
INSERT INTO projects (org_id, name)
SELECT id, 'Playground Sessions'
FROM organizations
ON CONFLICT DO NOTHING;

-- Move them over
UPDATE prompt_sessions
SET project_id = (
    SELECT id FROM projects
    WHERE
        projects.name = 'Playground Sessions'
        AND projects.org_id = prompt_sessions.org_id
)
WHERE project_id IS NULL;

DROP INDEX prompt_sessions_org_id_idx;
DROP INDEX prompt_sessions_org_id_name_deleted_at_idx;

DROP VIEW active_prompt_sessions;

DROP POLICY "Org members can see all prompt sessions." on public.prompt_sessions;

ALTER TABLE prompt_sessions DROP COLUMN org_id;

CREATE INDEX ON prompt_sessions (project_id);
create unique index on prompt_sessions (project_id, name, deleted_at) nulls not distinct;

create policy "Org members can see all prompt sessions."
on prompt_sessions
using (
    auth.user_id() in (
        select users.auth_id
        from
            projects join members on projects.org_id = members.org_id
                    join users on members.user_id = users.id
        where projects.id = prompt_sessions.project_id
    )
);

create or replace view active_prompt_sessions with (security_invoker=on) as
select
    *,
    -- We have to project the org_id because there are legacy API endpoints that
    -- expect it to be present.
    (SELECT organizations.id
      FROM organizations JOIN projects ON organizations.id = projects.org_id
      WHERE projects.id = prompt_sessions.project_id) as org_id
from prompt_sessions
where deleted_at is null;

drop function insert_prompt_session;

create function insert_prompt_session(
    user_id uuid,
    project_id uuid,
    name text,
    description text)
returns uuid
language plpgsql
security definer
as $$
declare
    _forbid_insert_prompt_sessions boolean;
    _prompt_session_id uuid;
begin
    insert into prompt_sessions(project_id, name, description, user_id)
    values (project_id, name, description, user_id)
    on conflict do nothing
    returning id into _prompt_session_id
    ;

    return _prompt_session_id;
end;
$$;

REVOKE EXECUTE ON FUNCTION insert_prompt_session FROM PUBLIC;

DROP FUNCTION register_prompt_session;

create or replace function register_prompt_session(
    auth_id uuid,
    org_name text,
    project_name text,
    session_name text)
returns jsonb
language plpgsql
security definer
as $$
declare
  _org_id uuid;
  _project_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;


    _project_id = get_or_create_project(auth_id, project_name, _org_id);

    select insert_prompt_session(
            user_id => get_user_id_by_auth_id(auth_id),
            project_id => _project_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select id
        from active_prompt_sessions prompt_sessions
        where
            project_id = _project_id
            and name = session_name
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$$;

REVOKE EXECUTE ON FUNCTION register_prompt_session FROM PUBLIC;
