-- Define a few utilities shared between object registration functions.

create or replace function get_user_id()
returns uuid
language sql
security invoker
as $$
SELECT id FROM users WHERE auth_id = auth.user_id();
$$;

create or replace function get_or_create_project(
    project_name text,
    org_id uuid)
returns uuid
language plpgsql
security invoker
as $$
declare
    _project_id uuid;
begin
    select
        id into _project_id
    from
        projects
    where
        projects.name = project_name
        and projects.org_id = get_or_create_project.org_id
    ;
    if not found then
        insert into
            projects(org_id, name, user_id)
        values
            (get_or_create_project.org_id, project_name, get_user_id())
        returning
            id into _project_id
        ;
    end if;
    return _project_id;
end;
$$;

create or replace function get_user_email(user_id uuid)
returns text
language sql
security invoker
as $$
    select email
    from users
    where id = user_id;
$$;

-- Redefine existing registration functions using shared utils.

create or replace function register_dataset(
    org_id uuid,
    project_name text,
    dataset_name text DEFAULT 'logs')
returns jsonb
language plpgsql
security invoker
as $$
declare
    _user_id uuid := get_user_id();
    _project_id uuid := get_or_create_project(project_name, org_id);
    _dataset_id uuid;
begin
    select
        id into _dataset_id
    from
        active_datasets datasets
    where
        datasets.project_id = _project_id
        and datasets.name = register_dataset.dataset_name
    ;
    if not found then
        insert into
            datasets(project_id, name, user_id)
        values
            (_project_id, register_dataset.dataset_name, _user_id)
        returning
            id into _dataset_id
        ;
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$$;

create or replace function register_prompt_session(
    org_name text,
    session_name text)
returns jsonb
language plpgsql
security invoker
as $$
declare
  _org_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;

    select
        id into _prompt_session_id
    from
        active_prompt_sessions prompt_sessions
    where
        org_id = _org_id
        and name = session_name
    ;
    if not found then
        insert into
            prompt_sessions(org_id, name, user_id)
        values
            (_org_id, session_name, get_user_id())
        returning
            id into _prompt_session_id
        ;
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where id = _prompt_session_id
    );
end;
$$;

create or replace function register_experiment(
    project_name text,
    org_id uuid,
    experiment_name text default null,
    description text default null,
    update boolean default false,
    repo_info jsonb default '{}',
    base_experiment text default null,
    -- List of parent commits to determine a base experiment. Commits are
    -- expected to be in order from most to least recent.
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version int8 default null
)
returns jsonb
language plpgsql
security invoker
as $$
declare
    _user_id uuid := get_user_id();
    _project_id uuid := get_or_create_project(project_name, org_id);
    _base_exp_id uuid;
    _conflicting_experiment_id uuid;
    _final_experiment_id uuid;
begin
    -- Get the base experiment id from a named experiment.
    if base_experiment is not null then
        select
            id into _base_exp_id
        from
            active_experiments experiments
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base.
    if _base_exp_id is null and ancestor_commits is not null then
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                active_experiments.id,
                active_experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join active_experiments
                    on active_experiments.commit = c.commit
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            id into _conflicting_experiment_id
        from
            active_experiments
        where
            project_id = _project_id
            and name = experiment_name
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just update the
    -- table.
    if _conflicting_experiment_id is not null and update then
        update
            experiments
        set
            description = register_experiment.description,
            repo_info = register_experiment.repo_info,
            base_exp_id = _base_exp_id,
            dataset_id = register_experiment.dataset_id,
            dataset_version = register_experiment.dataset_version,
            user_id = _user_id
        where
            id = _conflicting_experiment_id
        ;
        _final_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
                select
                    id into _conflicting_experiment_id
                from
                    active_experiments
                where
                    project_id = _project_id
                    and name = _insert_experiment_name
                ;
            else
                _insert_experiment_name := experiment_name;
                -- _conflicting_experiment_id was already assigned in the
                -- previous block.
            end if;

            if _conflicting_experiment_id is not null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
            end if;
            insert into
                experiments(project_id, name, description, repo_info,
                            base_exp_id, dataset_id, dataset_version, user_id)
            values
                (_project_id, _insert_experiment_name,
                 register_experiment.description, register_experiment.repo_info,
                 _base_exp_id,
                 register_experiment.dataset_id,
                 register_experiment.dataset_version, _user_id)
            returning
                id into _final_experiment_id
            ;
        end;
    end if;

    return (
        select
            jsonb_build_object('experiment', e, 'project', p)
        from
            experiments e join projects p on e.project_id = p.id
        where
            e.id = _final_experiment_id
    );
end;
$$;
