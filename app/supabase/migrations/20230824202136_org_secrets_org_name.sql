CREATE OR REPLACE VIEW preview_org_secrets AS
    SELECT org_secrets.id, org_secrets.created, org_secrets.org_id, org_secrets.name, '********' as preview_secret, organizations.name as org_name
    FROM org_secrets
    JOIN organizations ON org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id();

CREATE OR REPLACE FUNCTION set_org_secret(name text, value text, org_name text)
 RETURNS uuid
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  INSERT INTO org_secrets (org_id, name, secret)
  SELECT organizations.id, set_org_secret.name, set_org_secret.value
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = set_org_secret.org_name
  ON CONFLICT (org_id, name)
  DO UPDATE SET secret = set_org_secret.value
  RETURNING id
$function$
;

CREATE OR REPLACE FUNCTION delete_org_secret(name text, org_name text)
 RETURNS uuid
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
  DELETE FROM org_secrets WHERE org_id IN (
    SELECT organizations.id
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = delete_org_secret.org_name
  ) AND name = delete_org_secret.name
  RETURNING id
$function$
;
