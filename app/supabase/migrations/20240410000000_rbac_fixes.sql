alter table "public"."_expanded_acls" drop constraint "_expanded_acls_acl_id_fkey";

alter table "public"."_expanded_role_privileges" drop constraint "_expanded_role_privileges_role_id_fkey";

alter table "public"."_expanded_team_users" drop constraint "_expanded_team_users_team_id_fkey";

create trigger delete_teams_trigger
    after delete on teams
    referencing old table as transition_tbl
    for each statement execute procedure update_teams_trigger_f();

create trigger delete_roles_trigger
    after delete on roles
    referencing old table as transition_tbl
    for each statement execute procedure update_roles_trigger_f();
