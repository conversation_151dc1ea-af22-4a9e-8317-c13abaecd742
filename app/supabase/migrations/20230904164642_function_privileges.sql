ALTER DEFAULT PRIVILEGES REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC, anon;

<PERSON><PERSON><PERSON><PERSON> EXECUTE ON FUNCTION create_api_key FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION create_user_and_member FROM PUBLIC, anon;
<PERSON><PERSON><PERSON><PERSON> EXECUTE ON FUNCTION delete_org_secret(name text) FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION delete_org_secret(name text, org_name text) FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION lookup_api_key FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION lookup_experiments_by_commits FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION lookup_org_secret(name text) FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION lookup_org_secret(name text, org_name text) FROM PUBLIC, anon;
<PERSON><PERSON><PERSON><PERSON> EXECUTE ON FUNCTION register_dataset FROM PUBLIC, anon;
<PERSON><PERSON><PERSON><PERSON> EXECUTE ON FUNCTION register_prompt_session FROM PUBLIC, anon;
<PERSON><PERSON><PERSON><PERSON> EXECUTE ON FUNCTION remove_member_from_org FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION set_org_secret(name text, value text) FROM PUBLIC, anon;
REVOKE EXECUTE ON FUNCTION set_org_secret(name text, value text, org_name text) FROM PUBLIC, anon;

-- Fix permissions by hand
GRANT EXECUTE ON FUNCTION delete_org_secret(name text) TO anon;
GRANT EXECUTE ON FUNCTION delete_org_secret(name text, org_name text) TO anon;
GRANT EXECUTE ON FUNCTION lookup_experiments_by_commits TO anon;
GRANT EXECUTE ON FUNCTION lookup_org_secret(name text) TO anon;
GRANT EXECUTE ON FUNCTION lookup_org_secret(name text, org_name text) TO anon;
GRANT EXECUTE ON FUNCTION register_dataset TO anon;
GRANT EXECUTE ON FUNCTION register_prompt_session TO anon;
GRANT EXECUTE ON FUNCTION set_org_secret(name text, value text) TO anon;
GRANT EXECUTE ON FUNCTION set_org_secret(name text, value text, org_name text) TO anon;
