create or replace view "public"."active_datasets" with (security_invoker=on) as  SELECT datasets.id,
    datasets.project_id,
    datasets.name,
    datasets.description,
    datasets.created,
    datasets.deleted_at,
    datasets.user_id
   FROM (datasets
     JOIN projects ON ((datasets.project_id = projects.id)))
  WHERE ((datasets.deleted_at IS NULL) AND (projects.deleted_at IS NULL));


create or replace view "public"."active_experiments" with (security_invoker=on) as  SELECT experiments.id,
    experiments.project_id,
    experiments.name,
    experiments.description,
    experiments.created,
    experiments.repo_info,
    experiments.commit,
    experiments.base_exp_id,
    experiments.deleted_at,
    experiments.dataset_id,
    experiments.dataset_version,
    experiments.user_id,
    experiments.metadata,
    has_under_project_acl('experiment'::acl_object_type, experiments.id, experiments.project_id, 'read'::permission_type, anon_user_id()) AS public
   FROM (experiments
     JOIN projects ON ((experiments.project_id = projects.id)))
  WHERE ((experiments.deleted_at IS NULL) AND (projects.deleted_at IS NULL));


CREATE OR REPLACE FUNCTION public.get_or_create_project(auth_id uuid, project_name text, org_id uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _project_id uuid;
begin
    if not has_organization_acl('project', org_id, 'create', _user_id) then
        raise exception 'User does not have permissions to create a project within this org';
    end if;

    insert into projects(org_id, name, user_id)
    values (get_or_create_project.org_id, project_name, _user_id)
    on conflict do nothing
    returning id into _project_id
    ;

    if _project_id is null then
        -- Duplicate key.
        select id
        from projects
        where
            projects.name = project_name
            and projects.org_id = get_or_create_project.org_id
            and projects.deleted_at isnull
        into _project_id
        ;
        if not found then
            raise exception 'Project % likely deleted concurrently with creation', project_name;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'project', _object_id => _project_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;
    return _project_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_dataset(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, dataset_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_dataset.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    select insert_dataset(
        user_id => _user_id,
        project_id => _project_id,
        name => _dataset_name,
        description => description)
    into _dataset_id;

    if _dataset_id is null then
        -- Duplicate key.
        select datasets.id
        from
            datasets
            join projects on datasets.project_id = projects.id
        where
            datasets.project_id = _project_id
            and datasets.name = _dataset_name
            and datasets.deleted_at isnull
            and projects.deleted_at isnull
        into _dataset_id
        ;
        if not found then
            raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                _dataset_name, _project_id;
        end if;

        if _update then
            -- Check for update permissions on the dataset.
            if not has_under_project_acl('dataset', _dataset_id, _project_id, 'update', _user_id) then
                raise exception 'User does not have permission to update dataset';
            end if;

            update datasets
            set description = register_dataset.description
            where id = _dataset_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'dataset', _object_id => _dataset_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_experiment(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, experiment_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, repo_info jsonb DEFAULT NULL::jsonb, base_exp_id uuid DEFAULT NULL::uuid, base_experiment text DEFAULT NULL::text, ancestor_commits text[] DEFAULT NULL::text[], dataset_id uuid DEFAULT NULL::uuid, dataset_version text DEFAULT NULL::text, public boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_experiment.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.id = register_experiment.base_exp_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception 'Base experiment does not exist or user does not have access to it';
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            experiments.id into _base_exp_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception 'No base experiment found with name %', base_experiment;
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                experiments.id,
                experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join experiments
                    on experiments.commit = c.commit
                    and experiments.project_id = _project_id
                join projects on experiments.project_id = projects.id
            where
                has_under_project_acl('experiment', experiments.id,
                                      _project_id, 'read', _user_id)
                and experiments.deleted_at isnull
                and projects.deleted_at isnull
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception 'User does not have permissions to read base experiment';
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just return the
    -- existing experiment.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'read', _user_id) then
            raise exception 'User does not have permissions to read experiment';
        end if;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                metadata)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    metadata)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                _object_type => 'experiment', _object_id => _inserted_experiment_id,
                _user_id => _user_id, _role_id => get_owner_role_id());

            if _public then
                perform mark_experiment_as_public(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            else
                perform mark_experiment_as_nonpublic(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            end if;
        end;
    end if;

    -- The additional projections are copied from
    -- app/pages/api/experiment/_constants.ts.
    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from (
            select
                *,
                has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id()) as "public"
            from
                experiments
            where
                experiments.id = _inserted_experiment_id
        ) experiments join projects on projects.id = experiments.project_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_group(auth_id uuid, org_id uuid, group_name text, description text DEFAULT NULL::text, member_users uuid[] DEFAULT NULL::uuid[], member_groups uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = group_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_groups uuid[] = coalesce(member_groups, '{}');
    _update boolean = coalesce(update, false);

    _group_id uuid;
begin
    -- Search for a matching existing group.
    select id into _group_id
    from groups where groups.org_id = _org_id and name = _name and deleted_at is null;

    if _group_id is not null then
        -- Replace the contents of the existing group, including users and
        -- inheritors.
        if not has_under_organization_acl('group', _group_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update group';
        end if;

        -- Update groups, users, and inheritors with our given contents.

        update groups
        set name = _name, description = _description
        where id = _group_id;

        delete from group_users where group_id = _group_id;
        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        delete from group_inheritors where inheritor_group_id = _group_id;
        insert into group_inheritors(group_id, inheritor_group_id)
        select group_id, _group_id from unnest(_member_groups) group_id;
    else
        if not has_organization_acl('group', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create group';
        end if;

        -- Add groups, users, and inheritors with our given contents.

        insert into groups(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _group_id;

        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        insert into group_inheritors(group_id, inheritor_group_id)
        select group_id, _group_id from unnest(_member_groups) group_id;

        perform register_acl_unchecked(
            _object_type => 'group', _object_id => _group_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/group/_constants.ts.
    return (
        select jsonb_build_object('group', t)
        from (
            select
                *,
                (select coalesce(array_agg(user_id)::uuid[], '{}') member_users from group_users where group_users.group_id = groups.id),
                (select coalesce(array_agg(group_id)::uuid[], '{}') member_groups from group_inheritors join groups "_joined_groups" on group_inheritors.group_id = "_joined_groups".id where "_joined_groups".deleted_at isnull and group_inheritors.inheritor_group_id = groups.id)
            from
                groups
            where
                groups.id = _group_id
        ) t
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, slug text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _prompt_id uuid;
begin
    -- Assign the project_id.
    if register_prompt.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_prompt.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception 'Project does not exist or user does not have access to it';
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if not has_project_acl('prompt', _project_id, 'create', _user_id) then
        raise exception 'User does not have permission to create a prompt within this project';
    end if;

    insert into prompts (project_id, user_id, slug)
    values (_project_id, _user_id, register_prompt.slug)
    on conflict do nothing
    returning id into _prompt_id
    ;

    if _prompt_id is null then
        -- Duplicate key.
        select prompts.id
        from
            prompts
            join projects on prompts.project_id = projects.id
        where
            prompts.project_id = _project_id
            and prompts.slug = register_prompt.slug
            and prompts.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_id
        ;
        if not found then
            raise exception 'Prompt % (under project %) likely deleted concurrently with creation',
                register_prompt.slug, _project_id;
        end if;

        -- Update is just ignored for now
    else
        perform register_acl_unchecked(
            _object_type => 'prompt', _object_id => _prompt_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select jsonb_build_object('project', projects, 'prompt', prompts)
        from projects join prompts on projects.id = prompts.project_id
        where prompts.id = _prompt_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_prompt_session(auth_id uuid, org_name text, project_name text, session_name text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
  _user_id uuid = get_user_id_by_auth_id(auth_id);
  _org_id uuid;
  _project_id uuid;
  _prompt_session_id uuid;
begin
    select
        id into _org_id
    from
        organizations
    where
        organizations.name = org_name
    ;
    if not found then
        raise exception 'No organization with name %', org_name;
    end if;


    _project_id = get_or_create_project(auth_id, project_name, _org_id);

    select insert_prompt_session(
            user_id => _user_id,
            project_id => _project_id,
            name => session_name,
            description => null)
    into _prompt_session_id;

    if _prompt_session_id is null then
        -- Duplicate key.
        select prompt_sessions.id
        from prompt_sessions join projects on prompt_sessions.project_id = projects.id
        where
            project_id = _project_id
            and prompt_sessions.name = session_name
            and prompt_sessions.deleted_at isnull
            and projects.deleted_at isnull
        into _prompt_session_id
        ;

        if not found then
            raise exception 'Prompt session % (under org %) likely deleted concurrently with creation',
                session_name, _org_id;
        end if;
    else
        perform register_acl_unchecked(
            _object_type => 'prompt_session', _object_id => _prompt_session_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    return (
        select to_jsonb(prompt_sessions)
        from prompt_sessions
        where prompt_sessions.id = _prompt_session_id
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_permissions permission_type[] DEFAULT NULL::permission_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        -- Replace the contents of the existing role, including permissions and
        -- inheritors.
        if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
            raise exception 'User does not have permissions to update role';
        end if;

        -- Update roles, permissions, and inheritors with our given contents.

        update roles
        set name = _name, description = _description
        where id = _role_id;

        delete from role_permissions where role_id = _role_id;
        insert into role_permissions(role_id, permission)
        select _role_id, permission from unnest(_member_permissions) permission;

        delete from role_inheritors where inheritor_role_id = _role_id;
        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission)
        select _role_id, permission from unnest(_member_permissions) permission;

        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/role/_constants.ts.
    return (
        select jsonb_build_object('role', t)
        from (
            select
                *,
                (select coalesce(array_agg(permission)::text[], '{}') member_permissions from role_permissions where role_permissions.role_id = roles.id),
                (select coalesce(array_agg(role_inheritors.role_id)::uuid[], '{}') member_roles from role_inheritors join roles "_joined_roles" on role_inheritors.role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_inheritors.inheritor_role_id = roles.id)
            from
                roles
            where
                roles.id = _role_id
        ) t
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.remove_member_from_org(user_to_remove_id uuid, organization_id uuid, actor_auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$

DECLARE
    user_auth_id uuid;
BEGIN
    -- Check if the user with given actor_auth_id belongs to an organization
     IF NOT EXISTS (
        SELECT 1
        FROM users u
        JOIN members m ON u.id = m.user_id
        JOIN organizations o ON m.org_id = o.id
        WHERE u.auth_id = actor_auth_id AND o.id = organization_id
    )
    THEN
        RAISE EXCEPTION 'User with auth_id % does not belong to organization with id %', auth_id, organization_id;
    END IF;

    DELETE FROM acls WHERE user_id = user_to_remove_id AND _object_org_id = organization_id;
    DELETE FROM group_users WHERE user_id = user_to_remove_id AND _group_org_id = organization_id;
    DELETE FROM members WHERE user_id = user_to_remove_id AND org_id = organization_id;

END;
$function$
;

create or replace view "public"."visible_experiments" as  WITH base AS (
         SELECT projects.name AS project_name,
            organizations.id AS org_id,
            organizations.name AS org_name,
            experiments.id,
            experiments.project_id,
            experiments.name,
            experiments.description,
            experiments.created,
            experiments.repo_info,
            experiments.commit,
            experiments.base_exp_id,
            experiments.deleted_at,
            experiments.dataset_id,
            experiments.dataset_version,
            experiments.user_id,
            experiments.metadata,
            has_under_project_acl('experiment'::acl_object_type, experiments.id, experiments.project_id, 'read'::permission_type, anon_user_id()) AS public
           FROM ((experiments
             JOIN projects ON ((projects.id = experiments.project_id)))
             JOIN organizations ON ((organizations.id = projects.org_id)))
          WHERE ((experiments.deleted_at IS NULL) AND (projects.deleted_at IS NULL))
        )
 SELECT base.project_name,
    base.org_id,
    base.org_name,
    base.id,
    base.project_id,
    base.name,
    base.description,
    base.created,
    base.repo_info,
    base.commit,
    base.base_exp_id,
    base.deleted_at,
    base.dataset_id,
    base.dataset_version,
    base.user_id,
    base.metadata,
    base.public
   FROM base
  WHERE (base.public OR (EXISTS ( SELECT 1
           FROM (members
             JOIN users ON ((users.id = members.user_id)))
          WHERE ((users.auth_id = auth.user_id()) AND (members.org_id = base.org_id)))));

create or replace view "public"."load_experiment_view" as  SELECT v1.project_name,
    v1.org_id,
    v1.org_name,
    v1.id,
    v1.project_id,
    v1.name,
    v1.description,
    v1.created,
    v1.repo_info,
    v1.commit,
    v1.base_exp_id,
    v1.deleted_at,
    v1.dataset_id,
    v1.dataset_version,
    v1.user_id,
    v1.metadata,
    v1.public,
    ( SELECT jsonb_agg(jsonb_build_object('id', v2.id, 'name', v2.name, 'created', v2.created) ORDER BY v2.created DESC) AS jsonb_agg
           FROM visible_experiments v2
          WHERE ((v1.project_id = v2.project_id) AND (v1.id <> v2.id))) AS comparables
   FROM visible_experiments v1;

drop view if exists "public"."active_base_experiments";

drop view if exists "public"."active_groups";

drop view if exists "public"."active_organizations";

drop view if exists "public"."active_projects_auth_id";

drop view if exists "public"."active_roles";

drop view if exists "public"."base_experiments";

drop view if exists "public"."experiment_conflicts";

drop view if exists "public"."organization_acl_ids";

drop view if exists "public"."project_acl_ids";

drop view if exists "public"."active_and_inactive_experiments";
