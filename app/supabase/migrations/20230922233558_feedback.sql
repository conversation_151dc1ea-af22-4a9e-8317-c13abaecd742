CREATE TABLE user_feedback(
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users,
    created timestamp with time zone DEFAULT current_timestamp,
    content text,
    emoji text,
    page text
);

alter table user_feedback enable row level security;

create policy "Users can submit feedback."
  on user_feedback
  for INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM users WHERE auth.user_id() = auth_id
    )
  );
