ALTER TABLE api_keys ADD COLUMN org_id uuid references organizations;

-- Fast lookups
create index api_keys_user_id_key_hash on api_keys (user_id, key_hash);

create or replace function create_api_key(
    auth_id uuid,
    org_id uuid,
    name text)
returns text
language plpgsql
security definer
as $$
DECLARE
  _org_check int;
  api_key text;
BEGIN
  SELECT 1
  FROM users JOIN members ON users.id = members.user_id
  WHERE users.auth_id = create_api_key.auth_id
    AND (members.org_id = create_api_key.org_id OR create_api_key.org_id IS NULL)
  INTO _org_check;

  if not found then
    raise exception 'User with auth_id % does not belong to organization with id %', auth_id, org_id;
  end if;

  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  INSERT INTO api_keys (key_hash, name, preview_name, user_id, org_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT('sk-', RIGHT(api_key, 4)), id, org_id
    FROM users WHERE users.auth_id = create_api_key.auth_id;
  RETURN CONCAT('sk-', api_key);
END;
$$;


create type api_key_lookup as (
    auth_id uuid,
    org_id uuid
);

drop function lookup_api_key;
create function lookup_api_key(
    key text)
returns api_key_lookup
language sql
security definer
as $$
  SELECT auth_id, api_keys.org_id AS org_id FROM api_keys JOIN users ON api_keys.user_id = users.id
  WHERE key_hash = pgsodium.crypto_generichash(decode(regexp_replace(key, '^sk-', ''), 'base64'))
$$;
