create or replace function create_api_key_full(
    auth_id uuid,
    org_id uuid,
    name text)
returns jsonb
language plpgsql
security definer
as $$
DECLARE
  _org_check int;
  api_key text;
  _created_api_key_id uuid;
BEGIN
  SELECT 1
  FROM users JOIN members ON users.id = members.user_id
  WHERE users.auth_id = create_api_key_full.auth_id
    AND (members.org_id = create_api_key_full.org_id OR create_api_key_full.org_id IS NULL)
  INTO _org_check;

  if not found then
    raise exception 'User with auth_id % does not belong to organization with id %', auth_id, org_id;
  end if;

  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  INSERT INTO api_keys (key_hash, name, preview_name, user_id, org_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT('sk-', RIGHT(api_key, 4)), id, org_id
    FROM users WHERE users.auth_id = create_api_key_full.auth_id
  RETURNING id INTO _created_api_key_id;

  return (
    select jsonb_build_object(
        'api_key', to_jsonb(api_keys) || jsonb_build_object('key', concat('sk-', api_key))
    )
    from api_keys
    where id = _created_api_key_id
  );
END;
$$;

revoke execute on function create_api_key_full from anon;

create or replace function create_api_key(
    auth_id uuid,
    org_id uuid,
    name text)
returns text
language sql
security definer
as $$ select create_api_key_full(auth_id, org_id, name)->'api_key'->>'key' $$;

revoke execute on function create_api_key from anon;
