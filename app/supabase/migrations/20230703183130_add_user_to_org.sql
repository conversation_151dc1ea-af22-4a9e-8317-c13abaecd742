CREATE OR REPLACE FUNCTION create_user_and_member(user_email text, organization_id uuid, actor_auth_id uuid)
RETURNS void AS $$

DECLARE
    new_user_id uuid;
BEGIN
    -- Check if the user with given actor_auth_id belongs to an organization
     IF NOT EXISTS (
        SELECT 1
        FROM users u
        JOIN members m ON u.id = m.user_id
        JOIN organizations o ON m.org_id = o.id
        WHERE u.auth_id = actor_auth_id AND o.id = organization_id
    )
    THEN
        RAISE EXCEPTION 'User with auth_id % does not belong to organization with id %', auth_id, organization_id;
    END IF;

    -- If the user doesn't exist, create a new one
    INSERT INTO users (email)
    VALUES (user_email)
    ON CONFLICT (email) DO UPDATE SET email = EXCLUDED.email
    RETURNING id INTO new_user_id;

    -- Check if the user is already linked to the organization
    INSERT INTO members (user_id, org_id)
    VALUES (new_user_id, organization_id)
    ON CONFLICT (user_id, org_id) DO NOTHING;

END;
$$ LANGUAGE plpgsql;




CREATE OR REPLACE FUNCTION remove_member_from_org(user_to_remove_id uuid, organization_id uuid, actor_auth_id uuid)
RETURNS void AS $$

DECLARE
    user_auth_id uuid;
BEGIN
    -- Check if the user with given actor_auth_id belongs to an organization
     IF NOT EXISTS (
        SELECT 1
        FROM users u
        JOIN members m ON u.id = m.user_id
        JOIN organizations o ON m.org_id = o.id
        WHERE u.auth_id = actor_auth_id AND o.id = organization_id
    )
    THEN
        RAISE EXCEPTION 'User with auth_id % does not belong to organization with id %', auth_id, organization_id;
    END IF;

    DELETE FROM members WHERE user_id = user_to_remove_id AND org_id = organization_id;

END;
$$ LANGUAGE plpgsql;

CREATE or REPLACE VIEW org_members AS
SELECT users.id, users.email, users.given_name, users.family_name, other_members.org_id AS org_id, users.avatar_url, users.created
FROM me
JOIN members my_memberships on me.id = my_memberships.user_id
JOIN members other_members ON my_memberships.org_id = other_members.org_id
JOIN users on other_members.user_id = users.id;
