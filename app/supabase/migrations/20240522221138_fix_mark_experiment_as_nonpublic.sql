CREATE OR REPLACE FUNCTION public.mark_experiment_as_nonpublic(experiment_id uuid, project_id uuid, performing_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _anon_acl_id uuid;
begin
    if not has_under_project_acl('experiment', experiment_id, project_id, 'delete_acls', performing_user_id) then
        raise exception 'User does not have permission to mark experiment as nonpublic';
    end if;
    select find_acl_id(_object_type => 'experiment', _object_id => experiment_id,
                       _user_id => anon_user_id(), _permission => 'read') into _anon_acl_id;
    if _anon_acl_id is not null then
        delete from acls
        where id = _anon_acl_id;
    end if;
end;
$function$
;
