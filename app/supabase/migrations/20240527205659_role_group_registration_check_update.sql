CREATE OR REPLACE FUNCTION public.register_group(auth_id uuid, org_id uuid, group_name text, description text DEFAULT NULL::text, member_users uuid[] DEFAULT NULL::uuid[], member_groups uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = group_name;
    _description text = description;
    _member_users uuid[] = coalesce(member_users, '{}');
    _member_groups uuid[] = coalesce(member_groups, '{}');
    _update boolean = coalesce(update, false);

    _group_id uuid;
begin
    -- Search for a matching existing group.
    select id into _group_id
    from groups where groups.org_id = _org_id and name = _name and deleted_at is null;

    if _group_id is not null then
        if _update then
          -- Replace the contents of the existing group, including users and
          -- inheritors.
          if not has_under_organization_acl('group', _group_id, _org_id, 'update', _user_id) then
              raise exception 'User does not have permissions to update group';
          end if;

          -- Update groups, users, and inheritors with our given contents.

          update groups
          set name = _name, description = _description
          where id = _group_id;

          delete from group_users where group_id = _group_id;
          insert into group_users(group_id, user_id)
          select _group_id, user_id from unnest(_member_users) user_id;

          delete from group_inheritors where inheritor_group_id = _group_id;
          insert into group_inheritors(group_id, inheritor_group_id)
          select group_id, _group_id from unnest(_member_groups) group_id;
        else
          -- They must have read permission on the group.
          if not has_under_organization_acl('group', _group_id, _org_id, 'read', _user_id) then
              raise exception 'User does not have permissions to read group';
          end if;
        end if;
    else
        if not has_organization_acl('group', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create group';
        end if;

        -- Add groups, users, and inheritors with our given contents.

        insert into groups(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _group_id;

        insert into group_users(group_id, user_id)
        select _group_id, user_id from unnest(_member_users) user_id;

        insert into group_inheritors(group_id, inheritor_group_id)
        select group_id, _group_id from unnest(_member_groups) group_id;

        perform register_acl_unchecked(
            _object_type => 'group', _object_id => _group_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/group/_constants.ts.
    return (
        select jsonb_build_object('group', t)
        from (
            select
                *,
                (select coalesce(array_agg(user_id)::uuid[], '{}') member_users from group_users where group_users.group_id = groups.id),
                (select coalesce(array_agg(group_id)::uuid[], '{}') member_groups from group_inheritors join groups "_joined_groups" on group_inheritors.group_id = "_joined_groups".id where "_joined_groups".deleted_at isnull and group_inheritors.inheritor_group_id = groups.id)
            from
                groups
            where
                groups.id = _group_id
        ) t
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_permissions permission_type[] DEFAULT NULL::permission_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        if _update then
          -- Replace the contents of the existing role, including permissions and
          -- inheritors.
          if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
              raise exception 'User does not have permissions to update role';
          end if;

          -- Update roles, permissions, and inheritors with our given contents.

          update roles
          set name = _name, description = _description
          where id = _role_id;

          delete from role_permissions where role_id = _role_id;
          insert into role_permissions(role_id, permission)
          select _role_id, permission from unnest(_member_permissions) permission;

          delete from role_inheritors where inheritor_role_id = _role_id;
          insert into role_inheritors(role_id, inheritor_role_id)
          select role_id, _role_id from unnest(_member_roles) role_id;
        else
          -- They must have read permission on the role.
          if not has_under_organization_acl('role', _role_id, _org_id, 'read', _user_id) then
              raise exception 'User does not have permissions to read role';
          end if;
        end if;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission)
        select _role_id, permission from unnest(_member_permissions) permission;

        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/role/_constants.ts.
    return (
        select jsonb_build_object('role', t)
        from (
            select
                *,
                (select coalesce(array_agg(permission)::text[], '{}') member_permissions from role_permissions where role_permissions.role_id = roles.id),
                (select coalesce(array_agg(role_inheritors.role_id)::uuid[], '{}') member_roles from role_inheritors join roles "_joined_roles" on role_inheritors.role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_inheritors.inheritor_role_id = roles.id)
            from
                roles
            where
                roles.id = _role_id
        ) t
    );
end;
$function$
;
