ALTER TABLE experiments
ADD COLUMN tags TEXT[];

-- Add GIN index for efficient searching within the array
CREATE INDEX experiments_tags_gin_idx ON experiments USING GIN (tags);

-- Drop the old insert_experiment function with the old signature
DROP FUNCTION IF EXISTS insert_experiment(
    user_id uuid,
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version text,
    metadata jsonb
);

-- Create the new insert_experiment function to accept tags parameter
CREATE OR REPLACE FUNCTION insert_experiment(
    user_id uuid,
    project_id uuid,
    name text,
    description text,
    repo_info jsonb,
    base_exp_id uuid,
    dataset_id uuid,
    dataset_version text,
    metadata jsonb,
    tags text[]
)
returns uuid
language plpgsql
security definer
as $$
declare
    _experiment_id uuid;
begin
    if not has_project_acl('experiment', project_id, 'create', user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create experiment', 'objectType', 'project', 'objectId', project_id);
    end if;

    insert into
        experiments(project_id, name, description, repo_info, base_exp_id,
                    dataset_id, dataset_version, user_id, metadata, tags)
    values
        (project_id, name, description, repo_info, base_exp_id, dataset_id,
         dataset_version, user_id, metadata, tags)
    on conflict
        do nothing
    returning
        id into _experiment_id
    ;

    return _experiment_id;
end;
$$;

revoke execute on function insert_experiment from public, anon;

-- Drop the old register_experiment function with the old signature
DROP FUNCTION IF EXISTS register_experiment(
    auth_id uuid,
    org_id uuid,
    project_id uuid,
    project_name text,
    experiment_name text,
    description text,
    update boolean,
    repo_info jsonb,
    base_exp_id uuid,
    base_experiment text,
    ancestor_commits text[],
    dataset_id uuid,
    dataset_version text,
    public boolean,
    metadata jsonb
);

-- Create the new register_experiment function to accept tags parameter
CREATE OR REPLACE FUNCTION register_experiment(
    auth_id uuid,
    org_id uuid,
    project_id uuid default null,
    project_name text default null,
    experiment_name text default null,
    description text default null,
    update boolean default null,
    repo_info jsonb default null,
    base_exp_id uuid default null,
    base_experiment text default null,
    ancestor_commits text[] default null,
    dataset_id uuid default null,
    dataset_version text default null,
    public boolean default null,
    metadata jsonb default null,
    tags text[] default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);
    _public boolean := coalesce(public, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _repo_info jsonb := sanitize_repo_info(coalesce(repo_info, '{}'), org_id);
    _project_id uuid;
    _base_exp_id uuid;
    _base_exp_project_id uuid;
    _found_base_exp_id_from_ancestor_commits boolean = false;
    _conflicting_experiment_id uuid;
    _inserted_experiment_id uuid;
begin
    -- Assign the project_id.
    if register_experiment.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_experiment.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_experiment.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    if base_exp_id is not null then
        -- Check that the base experiment exists.
        select experiments.id, experiments.project_id into _base_exp_id, _base_exp_project_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.id = register_experiment.base_exp_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', register_experiment.base_exp_id);
        end if;
    end if;

    -- Get the base experiment id from a named experiment.
    if _base_exp_id is null and base_experiment is not null then
        _base_exp_project_id = _project_id;
        select
            experiments.id into _base_exp_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.name = base_experiment
            and experiments.project_id = _project_id
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('No base experiment found with name %s', base_experiment));
        end if;
    end if;

    -- If still no base experiment, pick the latest experiment of the first
    -- ancestor which has any experiments as the base and the user has read
    -- permissions for.
    if _base_exp_id is null and ancestor_commits is not null then
        _base_exp_project_id = _project_id;
        _found_base_exp_id_from_ancestor_commits = true;
        select
            id into _base_exp_id
        from (
            select
                c.nr as commit_nr,
                experiments.id,
                experiments.created
            from
                unnest(ancestor_commits) with ordinality c(commit, nr)
                join experiments
                    on experiments.commit = c.commit
                    and experiments.project_id = _project_id
                join projects on experiments.project_id = projects.id
            where
                has_under_project_acl('experiment', experiments.id,
                                      _project_id, 'read', _user_id)
                and experiments.deleted_at isnull
                and projects.deleted_at isnull
            order by
                commit_nr asc, created desc
            limit 1
        ) sub
        ;
    end if;

    -- Permissions check for base experiment (we can skip when deriving from an
    -- ancestor commit, because we already did the check there).
    if _base_exp_id is not null and not _found_base_exp_id_from_ancestor_commits then
        if not has_under_project_acl('experiment', _base_exp_id,
                                     _base_exp_project_id, 'read',
                                     _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', _base_exp_id);
        end if;
    end if;

    -- Search for an existing experiment matching the provided name.
    if experiment_name is not null then
        select
            experiments.id into _conflicting_experiment_id
        from
            experiments join projects on experiments.project_id = projects.id
        where
            experiments.project_id = _project_id
            and experiments.name = register_experiment.experiment_name
            and experiments.deleted_at isnull
            and projects.deleted_at isnull
        ;
    end if;

    -- If we have a conflicting experiment and are updating, just return the
    -- existing experiment.
    if _conflicting_experiment_id is not null and _update then
        if not has_under_project_acl('experiment', _conflicting_experiment_id,
                                     _project_id, 'read', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'experiment', 'objectId', _conflicting_experiment_id);
        end if;
        _inserted_experiment_id := _conflicting_experiment_id;
    else
        -- If no experiment_name was provided, we generate a default name of the
        -- form (<branch> | <user_email>)-<unix timestamp>. Otherwise, we start
        -- with the provided experiment_name.
        --
        -- If there is already an experiment with the potential name, we append
        -- a portion of a UUID to guarantee uniqueness.
        declare
            _insert_experiment_name text;
        begin
            if experiment_name is null then
                _insert_experiment_name := concat_ws(
                    '-',
                    coalesce(_repo_info->>'branch', get_user_email(_user_id)),
                    extract('epoch' from now())::bigint);
            else
                _insert_experiment_name := experiment_name;
            end if;

            select insert_experiment(
                _user_id, _project_id, _insert_experiment_name, description,
                _repo_info, _base_exp_id, dataset_id, dataset_version,
                metadata, tags)
            into _inserted_experiment_id;

            if _inserted_experiment_id is null then
                _insert_experiment_name := concat_ws(
                    '-', _insert_experiment_name,
                    substring(gen_random_uuid()::text for 8));
                select insert_experiment(
                    _user_id, _project_id, _insert_experiment_name, description,
                    _repo_info, _base_exp_id, dataset_id, dataset_version,
                    metadata, tags)
                into _inserted_experiment_id;
            end if;

            perform register_acl_unchecked(
                _object_type => 'experiment', _object_id => _inserted_experiment_id,
                _user_id => _user_id, _role_id => get_owner_role_id());

            if _public then
                perform mark_experiment_as_public(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            else
                perform mark_experiment_as_nonpublic(
                    experiment_id => _inserted_experiment_id,
                    project_id => _project_id,
                    performing_user_id => _user_id);
            end if;
        end;
    end if;

    -- The additional projections are copied from
    -- app/pages/api/experiment/_constants.ts.
    return (
        select jsonb_build_object('project', projects, 'experiment', experiments)
        from (
            select
                *,
                has_under_project_acl('experiment', experiments.id, experiments.project_id, 'read', anon_user_id()) as "public"
            from
                experiments
            where
                experiments.id = _inserted_experiment_id
        ) experiments join projects on projects.id = experiments.project_id
    );
end;
$$;

revoke execute on function register_experiment from public, anon;
