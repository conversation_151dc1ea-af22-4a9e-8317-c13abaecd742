drop trigger if exists "acquire_acl_lock_role_inheritors_trigger" on "public"."role_inheritors";

drop trigger if exists "delete_role_inheritors_trigger" on "public"."role_inheritors";

drop trigger if exists "insert_role_inheritors_trigger" on "public"."role_inheritors";

drop trigger if exists "update_role_inheritors_new_trigger" on "public"."role_inheritors";

drop trigger if exists "update_role_inheritors_old_trigger" on "public"."role_inheritors";

drop trigger if exists "upsert_role_inheritors_trigger" on "public"."role_inheritors";

drop function if exists "public"."check_new_role_inheritor"();

drop function if exists "public"."refresh_expanded_acls_by_org"(_arg_org_id uuid);

drop function if exists "public"."refresh_expanded_role_permissions_by_org"(_arg_org_id uuid);

drop index if exists "public"."_expanded_role_permissions_role_id_permission_restrict_obje_idx";

create table "public"."role_members" (
    "role_id" uuid not null,
    "member_role_id" uuid not null
);


alter table "public"."role_members" enable row level security;

alter table "public"."_expanded_role_permissions" add column "grant_object_type" acl_grant_object_type;

alter table "public"."_expanded_role_permissions" add column "member_role_id" uuid;

alter table "public"."_expanded_role_permissions" alter column "permission" drop not null;

CREATE UNIQUE INDEX _expanded_role_permissions_role_id_grant_object_type_permis_idx ON public._expanded_role_permissions USING btree (role_id, grant_object_type, permission, restrict_object_type, member_role_id) NULLS NOT DISTINCT;

CREATE INDEX _expanded_role_permissions_grant_object_type_permission_res_idx ON public._expanded_role_permissions USING btree (grant_object_type, permission, restrict_object_type, member_role_id, role_id);

CREATE INDEX role_members_member_role_id_role_id_idx ON public.role_members USING btree (member_role_id, role_id);

CREATE UNIQUE INDEX role_members_pkey ON public.role_members USING btree (role_id, member_role_id);

alter table "public"."role_members" add constraint "role_members_pkey" PRIMARY KEY using index "role_members_pkey";

alter table "public"."role_members" add constraint "role_members_member_role_id_fkey" FOREIGN KEY (member_role_id) REFERENCES roles(id) not valid;

alter table "public"."role_members" validate constraint "role_members_member_role_id_fkey";

alter table "public"."role_members" add constraint "role_members_role_id_fkey" FOREIGN KEY (role_id) REFERENCES roles(id) not valid;

alter table "public"."role_members" validate constraint "role_members_role_id_fkey";

-- Backfill role_members.
insert into role_members(role_id, member_role_id)
select inheritor_role_id, role_id from role_inheritors;

-- Backfill the grant_object_type column for existing rows in
-- _expanded_role_permissions. Then set it to not null.
update _expanded_role_permissions set grant_object_type = 'permission' where grant_object_type is null;
alter table "public"."_expanded_role_permissions" alter column "grant_object_type" set not null;

-- Backfill the grant_object_type='role' rows in _expanded_role_permissions. We
-- only add the tautological rows for now. After updating the
-- refresh_expanded_role_permissions_by_role function, we will refresh any roles
-- which have members.
insert into _expanded_role_permissions(role_id, _role_org_id, grant_object_type, member_role_id)
select roles.id, roles.org_id, 'role', roles.id from roles;

CREATE OR REPLACE FUNCTION public.check_new_role_member()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    _role_org_id uuid;
    _member_role_org_id uuid;
begin
    select roles.org_id into _role_org_id
    from roles where roles.id = new.role_id;
    select roles.org_id into _member_role_org_id
    from roles where roles.id = new.member_role_id;

    if (_role_org_id is not null and _member_role_org_id is not null and _role_org_id <> _member_role_org_id) then
        raise exception 'Both roles must belong to the same organization';
    end if;

    if (_role_org_id is null and _member_role_org_id is not null) then
        raise exception 'System roles cannot contain non-system roles';
    end if;

    return new;
end;
$function$
;

revoke execute on function check_new_role_member from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_role_permissions_by_role(_arg_role_id uuid)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
declare
    _out0 integer;
    _out1 integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles which '_arg_role_id' is a member of.
    with recursive
    roles_to_update as (
        select role_id from _expanded_role_permissions
        where (_arg_role_id is null or (permission is null and restrict_object_type is null and member_role_id = _arg_role_id)) and grant_object_type = 'role'
        union all
        select _arg_role_id role_id where _arg_role_id is not null
    ),
    t(role_id, permission, _role_org_id, restrict_object_type, grant_object_type, member_role_id) as (
        (
            select roles.id, null::permission_type, roles.org_id, null::acl_object_type, 'role'::acl_grant_object_type, roles.id
            from roles join roles_to_update on roles_to_update.role_id = roles.id
            where roles.deleted_at is null
        )
        union
        select * from (
            -- Postgres is weird and doesn't allow the recursive term to appear
            -- more than once, but it is just a parser thing.
            with t_inner as (select * from t)
            select t_inner.role_id, role_permissions.permission, t_inner._role_org_id, role_permissions.restrict_object_type, 'permission'::acl_grant_object_type, null
            from t_inner join role_permissions on t_inner.member_role_id = role_permissions.role_id
            where t_inner.grant_object_type = 'role'
            union all
            select t_inner.role_id, null, t_inner._role_org_id, null, 'role'::acl_grant_object_type, role_members.member_role_id
            from
                t_inner
                join role_members on t_inner.member_role_id = role_members.role_id
                join roles on role_members.member_role_id = roles.id
            where t_inner.grant_object_type = 'role' and roles.deleted_at is null
        ) "x"
    ),
    final_result_set as (
        select * from t
    ),
    deleted_permissions as (
        delete from _expanded_role_permissions where
        _expanded_role_permissions.role_id in (select role_id from roles_to_update)
        and not exists (
            select 1 from final_result_set
            where
                _expanded_role_permissions.role_id = final_result_set.role_id
                and _expanded_role_permissions.grant_object_type = final_result_set.grant_object_type
                and (
                    (_expanded_role_permissions.permission isnull and final_result_set.permission isnull)
                    or (_expanded_role_permissions.permission = final_result_set.permission)
                )
                and (
                    (_expanded_role_permissions.restrict_object_type isnull and final_result_set.restrict_object_type isnull)
                    or (_expanded_role_permissions.restrict_object_type = final_result_set.restrict_object_type)
                )
                and (
                    (_expanded_role_permissions.member_role_id isnull and final_result_set.member_role_id isnull)
                    or (_expanded_role_permissions.member_role_id = final_result_set.member_role_id)
                )
        )
        returning 1
    ),
    inserted_permissions as (
        insert into _expanded_role_permissions select * from final_result_set
        on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out0
    from
        (select count(*) cnt from deleted_permissions) "num_deleted"
        join (select count(*) cnt from inserted_permissions) "num_inserted"
        on true;

    -- Rebuild the set of expanded ACLs for all objects which have an ACL on
    -- this role or any role which inherits from it.
    with
    acl_roles_to_update as (
        select member_role_id role_id from _expanded_role_permissions
        where (_arg_role_id is null or role_id = _arg_role_id) and grant_object_type = 'role'
        -- Note that we still need to explicitly include the input role in case
        -- it was deleted, so it's no longer present in
        -- _expanded_role_permissions, but we still need to update the ACLs
        -- which reference it.
        union all
        select _arg_role_id role_id where _arg_role_id is not null
    ),
    acl_objects_to_update as (
        select distinct acls.object_type, acls.object_id
        from acls join acl_roles_to_update on acls.role_id = acl_roles_to_update.role_id
    ),
    updated_acls as (
        select refresh_expanded_acls_by_object(object_type, object_id) ret
        from acl_objects_to_update
    )
    select sum(ret) into _out1 from updated_acls;

    return _out0 + _out1;
end;
$function$
;

revoke execute on function refresh_expanded_role_permissions_by_role from public, anon;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_object(_object_type acl_object_type, _object_id uuid)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
declare
    _out integer;
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "(_object_type isnull and _object_id isnull) or (object_type = _object_type and object_id = _object_id)"
    with
    candidate_acls as (
        select *
        from acls
        where ((_object_type isnull and _object_id isnull) or (object_type = _object_type and object_id = _object_id))
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_members.user_object_type expanded_user_object_type,
            _expanded_group_members.user_group_id expanded_user_group_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_members using (group_id)
                left join _expanded_role_permissions using (role_id)
        where
            _expanded_role_permissions.role_id is null or _expanded_role_permissions.grant_object_type = 'permission'
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(expanded_user_object_type, user_object_type) as user_object_type,
        coalesce(expanded_user_group_id, user_id) as user_group_id,
        coalesce(expanded_permission, permission) as permission,
        coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    final_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_object_type is not null
            and user_group_id is not null
            and permission is not null
    ),
    deleted_acls as (
        delete from _expanded_acls where
        ((_object_type isnull and _object_id isnull) or (object_type = _object_type and object_id = _object_id))
        and not exists(
            select 1 from final_acls
            where
                _expanded_acls.acl_id = final_acls.acl_id
                and _expanded_acls.object_type = final_acls.object_type
                and _expanded_acls.object_id = final_acls.object_id
                and _expanded_acls.user_object_type = final_acls.user_object_type
                and _expanded_acls.user_group_id = final_acls.user_group_id
                and _expanded_acls.permission = final_acls.permission
                and ((_expanded_acls.restrict_object_type isnull and final_acls.restrict_object_type isnull)
                     or (_expanded_acls.restrict_object_type = final_acls.restrict_object_type))
        )
        returning 1
    ),
    inserted_acls as (
        insert into _expanded_acls select * from final_acls on conflict do nothing
        returning 1
    )
    select num_deleted.cnt + num_inserted.cnt into _out
    from
        (select count(*) cnt from deleted_acls) "num_deleted"
        join (select count(*) cnt from inserted_acls) "num_inserted"
        on true;
    return _out;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_permissions permission_type[] DEFAULT NULL::permission_type[], member_restrict_object_types acl_object_type[] DEFAULT NULL::acl_object_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_restrict_object_types acl_object_type[] = coalesce(member_restrict_object_types, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Since 'member_permissions' and 'member_restrict_object_types' are
    -- coupled, the arrays must have the same length.
    if array_length(_member_permissions, 1) <> array_length(_member_restrict_object_types, 1) then
        raise exception 'member_permissions array must have the same length as member_restrict_object_types';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        if _update then
          -- Replace the contents of the existing role, including permissions and
          -- inheritors.
          if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
              raise exception 'User does not have permissions to update role';
          end if;

          -- Update roles, permissions, and members with our given contents.

          update roles
          set name = _name, description = _description
          where id = _role_id;

          delete from role_permissions where role_id = _role_id;
          insert into role_permissions(role_id, permission, restrict_object_type)
          select _role_id, permission, restrict_object_type
          from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

          delete from role_members where role_id = _role_id;
          insert into role_members(role_id, member_role_id)
          select _role_id, member_role_id from unnest(_member_roles) member_role_id;
        else
          -- They must have read permission on the role.
          if not has_under_organization_acl('role', _role_id, _org_id, 'read', _user_id) then
              raise exception 'User does not have permissions to read role';
          end if;
        end if;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission, restrict_object_type)
        select _role_id, permission, restrict_object_type
        from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

        insert into role_members(role_id, member_role_id)
        select _role_id, member_role_id from unnest(_member_roles) member_role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/role/_constants.ts.
    return (
        select jsonb_build_object('role', t)
        from (
            select
                *,
                (select coalesce(array_agg(jsonb_build_object('permission', permission::text, 'restrict_object_type', restrict_object_type::text))::jsonb[], '{}') member_permissions from role_permissions where role_permissions.role_id = roles.id),
                (select coalesce(array_agg(role_members.member_role_id)::uuid[], '{}') member_roles from role_members join roles "_joined_roles" on role_members.member_role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_members.role_id = roles.id)
            from
                roles
            where
                roles.id = _role_id
        ) t
    );
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_role_members_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.role_id
        from transition_tbl
    loop
        perform refresh_expanded_role_permissions_by_role(object_rec.role_id);
    end loop;
    return null;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_roles_trigger_f()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    object_rec RECORD;
begin
    for object_rec in
        select distinct transition_tbl.id
        from transition_tbl
    loop
        perform refresh_expanded_role_permissions_by_role(object_rec.id);
    end loop;
    return null;
end;
$function$
;

CREATE TRIGGER acquire_acl_lock_role_members_trigger BEFORE INSERT OR DELETE OR UPDATE ON public.role_members FOR EACH STATEMENT EXECUTE FUNCTION acquire_acl_lock_trigger_f();

CREATE TRIGGER delete_role_members_trigger AFTER DELETE ON public.role_members REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER insert_roles_trigger AFTER INSERT ON public.roles REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_roles_trigger_f();

CREATE TRIGGER insert_role_members_trigger AFTER INSERT ON public.role_members REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER update_role_members_new_trigger AFTER UPDATE ON public.role_members REFERENCING NEW TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER update_role_members_old_trigger AFTER UPDATE ON public.role_members REFERENCING OLD TABLE AS transition_tbl FOR EACH STATEMENT EXECUTE FUNCTION update_role_members_trigger_f();

CREATE TRIGGER upsert_role_members_trigger BEFORE INSERT OR UPDATE ON public.role_members FOR EACH ROW EXECUTE FUNCTION check_new_role_member();

-- Refresh the materializations for any roles with inheritance.
with
roles_to_refresh as (select distinct role_id from role_members)
select refresh_expanded_role_permissions_by_role(role_id) from roles_to_refresh;
