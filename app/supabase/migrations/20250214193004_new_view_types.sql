-- Replace the view constraint with a UDF that can be modified in the same
-- transaction as adding members to an enum.

alter table "public"."views" drop constraint "valid_object_type_view_type";

CREATE OR REPLACE FUNCTION public.valid_object_type_view_type_check(object_type acl_object_type, view_type view_type)
 RETURNS boolean
 LANGUAGE plpgsql
 IMMUTABLE PARALLEL SAFE
AS $function$
begin
    return (select (
        case
          when object_type = 'organization' then view_type in ('projects')
          when object_type = 'project' then view_type in (
              'logs',
              'experiments',
              'datasets',
              'prompts',
              'playgrounds',
              'experiment',
              'dataset'
          )
          when object_type = 'experiment' then view_type in ('experiment')
          when object_type = 'dataset' then view_type in ('dataset')
          else false
        end
    ));
end;
$function$
;

revoke execute on function valid_object_type_view_type_check from public, anon;

alter table "public"."views" add constraint "valid_object_type_view_type" CHECK (valid_object_type_view_type_check(object_type, view_type)) not valid;
alter table "public"."views" validate constraint "valid_object_type_view_type";

-- Now add the new enum values.

alter type view_type add value 'playground';
alter type view_type add value 'tools';
alter type view_type add value 'scorers';

-- And modify the function to include the new values.

CREATE OR REPLACE FUNCTION public.valid_object_type_view_type_check(object_type acl_object_type, view_type view_type)
 RETURNS boolean
 LANGUAGE plpgsql
 IMMUTABLE PARALLEL SAFE
AS $function$
begin
    return (select (
        case
          when object_type = 'organization' then view_type in ('projects')
          when object_type = 'project' then view_type in (
              'logs',
              'experiments',
              'datasets',
              'prompts',
              'playgrounds',
              'experiment',
              'dataset',
              'playground',
              'tools',
              'scorers'
          )
          when object_type = 'experiment' then view_type in ('experiment')
          when object_type = 'dataset' then view_type in ('dataset')
          else false
        end
    ));
end;
$function$
;
