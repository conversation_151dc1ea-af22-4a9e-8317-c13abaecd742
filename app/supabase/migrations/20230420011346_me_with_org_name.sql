create or replace view "public"."me" as  SELECT users.id,
    users.auth_id,
    json_agg(( SELECT jsonb_build_object('id', members.org_id, 'name', organizations.name) AS jsonb_build_object
           FROM (members
             JOIN organizations ON ((members.org_id = organizations.id)))
          WHERE (members.user_id = users.id))) AS organizations
   FROM users
  WHERE ((users.auth_id = auth.user_id()) OR true)
  GROUP BY users.id;
