drop function if exists "public"."register_role"(auth_id uuid, org_id uuid, role_name text, description text, member_permissions permission_type[], member_roles uuid[], update boolean);

alter table "public"."_expanded_role_permissions" drop constraint "_expanded_role_permissions_pkey";

alter table "public"."role_permissions" drop constraint "role_permissions_pkey";

drop index if exists "public"."_expanded_role_permissions_pkey";

drop index if exists "public"."role_permissions_pkey";

drop index if exists "public"."something_pkey";

alter table "public"."_expanded_role_permissions" add column "restrict_object_type" acl_object_type;

alter table "public"."role_permissions" add column "restrict_object_type" acl_object_type;

CREATE UNIQUE INDEX _expanded_role_permissions_role_id_permission_restrict_obje_idx ON public._expanded_role_permissions USING btree (role_id, permission, restrict_object_type) NULLS NOT DISTINCT;

CREATE UNIQUE INDEX role_permissions_role_id_permission_restrict_object_type_idx ON public.role_permissions USING btree (role_id, permission, restrict_object_type) NULLS NOT DISTINCT;

CREATE OR REPLACE FUNCTION public.register_role(auth_id uuid, org_id uuid, role_name text, description text DEFAULT NULL::text, member_permissions permission_type[] DEFAULT NULL::permission_type[], member_restrict_object_types acl_object_type[] DEFAULT NULL::acl_object_type[], member_roles uuid[] DEFAULT NULL::uuid[], update boolean DEFAULT NULL::boolean)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _org_id uuid = org_id;
    _user_id uuid = get_user_id_by_auth_id(auth_id);
    _name text = role_name;
    _description text = description;
    _member_permissions permission_type[] = coalesce(member_permissions, '{}');
    _member_restrict_object_types acl_object_type[] = coalesce(member_restrict_object_types, '{}');
    _member_roles uuid[] = coalesce(member_roles, '{}');
    _update boolean = coalesce(update, false);

    _role_id uuid;
begin
    -- This function should not be used for registering system roles. So org_id
    -- must not be null.
    if _org_id is null then
        raise exception 'Must specify a non-null org_id when registering a role';
    end if;

    -- Since 'member_permissions' and 'member_restrict_object_types' are
    -- coupled, the arrays must have the same length.
    if array_length(_member_permissions, 1) <> array_length(_member_restrict_object_types, 1) then
        raise exception 'member_permissions array must have the same length as member_restrict_object_types';
    end if;

    -- Search for a matching existing role.
    select id into _role_id
    from roles where roles.org_id = _org_id and name = _name and deleted_at is null;

    if _role_id is not null then
        if _update then
          -- Replace the contents of the existing role, including permissions and
          -- inheritors.
          if not has_under_organization_acl('role', _role_id, _org_id, 'update', _user_id) then
              raise exception 'User does not have permissions to update role';
          end if;

          -- Update roles, permissions, and inheritors with our given contents.

          update roles
          set name = _name, description = _description
          where id = _role_id;

          delete from role_permissions where role_id = _role_id;
          insert into role_permissions(role_id, permission, restrict_object_type)
          select _role_id, permission, restrict_object_type
          from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

          delete from role_inheritors where inheritor_role_id = _role_id;
          insert into role_inheritors(role_id, inheritor_role_id)
          select role_id, _role_id from unnest(_member_roles) role_id;
        else
          -- They must have read permission on the role.
          if not has_under_organization_acl('role', _role_id, _org_id, 'read', _user_id) then
              raise exception 'User does not have permissions to read role';
          end if;
        end if;
    else
        if not has_organization_acl('role', _org_id, 'create', _user_id) then
            raise exception 'User does not have permissions to create role';
        end if;

        -- Add roles, permissions, and inheritors with our given contents.

        insert into roles(org_id, user_id, name, description)
        values (_org_id, _user_id, _name, _description)
        returning id into _role_id;

        insert into role_permissions(role_id, permission, restrict_object_type)
        select _role_id, permission, restrict_object_type
        from unnest(_member_permissions, _member_restrict_object_types) t(permission, restrict_object_type);

        insert into role_inheritors(role_id, inheritor_role_id)
        select role_id, _role_id from unnest(_member_roles) role_id;

        perform register_acl_unchecked(
            _object_type => 'role', _object_id => _role_id,
            _user_id => _user_id, _role_id => get_owner_role_id());
    end if;

    -- The additional projections are copied from
    -- app/pages/api/role/_constants.ts.
    return (
        select jsonb_build_object('role', t)
        from (
            select
                *,
                (select coalesce(array_agg(jsonb_build_object('permission', permission::text, 'restrict_object_type', restrict_object_type::text))::jsonb[], '{}') member_permissions from role_permissions where role_permissions.role_id = roles.id),
                (select coalesce(array_agg(role_inheritors.role_id)::uuid[], '{}') member_roles from role_inheritors join roles "_joined_roles" on role_inheritors.role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_inheritors.inheritor_role_id = roles.id)
            from
                roles
            where
                roles.id = _role_id
        ) t
    );
end;
$function$
;

revoke execute on function register_role from public, anon;

CREATE OR REPLACE FUNCTION public.process_new_acl()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
declare
    _object_org_id uuid;
begin
    -- If _object_org_id is null, the user must have supplied one in the new
    -- row. If it is non-null and the user has supplied one anyways, they must
    -- agree.
    _object_org_id = get_new_acl_object_org_id(new.object_type, new.object_id);
    if _object_org_id is null then
        if new._object_org_id is null then
            raise exception 'Must supply _object_org_id explicitly for non-derivable ACL object %', new.object_type;
        else
            _object_org_id = new._object_org_id;
        end if;
    else
        if new._object_org_id is null then
            new._object_org_id = _object_org_id;
        elsif _object_org_id <> new._object_org_id then
            raise exception 'Derived org id does not match provided org id for % object', new.object_type;
        end if;
    end if;
    if _object_org_id is null or new._object_org_id is null then
        raise exception 'Impossible';
    end if;

    -- Check that the field type enum corresponds to the correct field, and that
    -- exactly one field in each category is set.

    if ((new.user_object_type = 'user' and new.user_id is null) or
        (new.user_object_type = 'group' and new.group_id is null)) then
        raise exception 'user_object_type must correspond to set column';
    end if;
    if ((new.user_id is not null)::int + (new.group_id is not null)::int <> 1) then
        raise exception 'At most one user object column must be set';
    end if;

    if ((new.grant_object_type = 'permission' and new.permission is null) or
        (new.grant_object_type = 'role' and new.role_id is null)) then
        raise exception 'grant_object_type must correspond to set column';
    end if;
    if ((new.permission is not null)::int + (new.role_id is not null)::int <> 1) then
        raise exception 'At most one grant object column must be set';
    end if;
    if (new.restrict_object_type is not null and new.role_id is not null) then
        raise exception 'Cannot set both role_id and restrict_object_type';
    end if;

    -- If the role corresponds to a non-system role, its org must match
    -- _object_org_id.
    if new.grant_object_type = 'role' then
        declare
            _role_org_id uuid;
        begin
            select roles.org_id into _role_org_id
            from roles where roles.id = new.role_id;
            if _role_org_id is not null and _role_org_id <> _object_org_id then
                raise exception 'Custom role must belong to same org as object';
            end if;
        end;
    end if;

    return new;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_object(_object_type acl_object_type, _object_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    delete from _expanded_acls
    where object_type = _object_type and object_id = _object_id;

    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "object_type = _object_type and object_id = _object_id"
    with
    candidate_acls as (
        select *
        from acls
        where object_type = _object_type and object_id = _object_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_users.user_id expanded_user_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_users using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(permission, expanded_permission) as permission,
        coalesce(restrict_object_type, expanded_restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and permission is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_acls_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_acls
    where _arg_org_id is null or _arg_org_id = _object_org_id;

    -- Rebuild the set of acls, either across the entire table, or scoped to the
    -- acls in _arg_org_id.
    --
    -- Query generated from the following command:
    --
    -- ./scripts/make_expanded_acls_query.py "_arg_org_id is null or _arg_org_id = _object_org_id"
    with
    candidate_acls as (
        select *
        from acls
        where _arg_org_id is null or _arg_org_id = _object_org_id
    ),
    joined_acls as (
        select
            candidate_acls.*,
            _expanded_group_users.user_id expanded_user_id,
            _expanded_role_permissions.permission expanded_permission,
            _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
        from
            candidate_acls
                left join _expanded_group_users using (group_id)
                left join _expanded_role_permissions using (role_id)
    ),
    coalesced_acls as (
    select
        id acl_id,
        object_type,
        object_id,
        coalesce(user_id, expanded_user_id) as user_id,
        coalesce(permission, expanded_permission) as permission,
        coalesce(restrict_object_type, expanded_restrict_object_type) as restrict_object_type,
        _object_org_id
    from
        joined_acls
    ),
    filtered_acls as (
        select * from coalesced_acls
        where
            -- It is possible that the user specifies an empty group or role, in
            -- which case we don't need to include these entries in the expanded
            -- ACLs.
            user_id is not null
            and permission is not null
    )
    insert into _expanded_acls select * from filtered_acls on conflict do nothing;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_expanded_role_permissions_by_org(_arg_org_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- Acquire locks against other RBAC operations.
    perform acquire_acl_lock();

    -- Delete all existing entries corresponding to _arg_org_id. If _arg_org_id
    -- is null, delete everything.
    delete from _expanded_role_permissions
    where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of role grants, either across the entire table, or scoped
    -- to the roles in the given organization.
    --
    -- When building the set of updated role grants, we always include the
    -- system roles to start with, since they can be included in any role.
    --
    -- When updating the grants, we include everything if _arg_org_id is null,
    -- because system grants can change everything (and we would have wiped the
    -- whole table in the delete statement above). Otherwise, we only include
    -- the organization-scoped grants, since organization-scoped roles cannot
    -- affect different organizations or system roles.
    with recursive t(role_id, permission, _role_org_id, restrict_object_type) as (
        select role_permissions.role_id, role_permissions.permission,
               roles.org_id _role_org_id, role_permissions.restrict_object_type
        from role_permissions join roles on role_permissions.role_id = roles.id
        where
            (_arg_org_id is null or org_id is null or org_id = _arg_org_id)
            and roles.deleted_at is null
        union
        -- When system roles propagate into organization-scoped roles, we need
        -- to pick the organization-scoped role's org_id for the final entry.
        select
            role_inheritors.inheritor_role_id role_id,
            t.permission,
            coalesce(t._role_org_id, roles.org_id) _role_org_id,
            t.restrict_object_type
        from
            t
            join role_inheritors on t.role_id = role_inheritors.role_id
            join roles on role_inheritors.inheritor_role_id = roles.id
        where roles.deleted_at is null
    )
    insert into _expanded_role_permissions
    select * from t where _arg_org_id is null or _role_org_id = _arg_org_id;

    -- Rebuild the set of expanded ACLs for this organization.
    perform refresh_expanded_acls_by_org(_arg_org_id);
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_acl_unchecked(_object_type acl_object_type, _object_id uuid, _user_id uuid DEFAULT NULL::uuid, _group_id uuid DEFAULT NULL::uuid, _permission permission_type DEFAULT NULL::permission_type, _role_id uuid DEFAULT NULL::uuid, _restrict_object_type acl_object_type DEFAULT NULL::acl_object_type)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
declare
    _acl_id uuid;
    _user_object_type acl_user_object_type;
    _grant_object_type acl_grant_object_type;

    _ret_obj jsonb;
begin
    if (_user_id is null) = (_group_id is null) then
        raise exception 'Exactly one of user_id and group_id must be non-null';
    elsif _user_id is null then
        _user_object_type = 'group';
    else
        _user_object_type = 'user';
    end if;

    if (_permission is null) = (_role_id is null) then
        raise exception 'Exactly one of permission and role_id must be non-null';
    elsif _permission is null then
        _grant_object_type = 'role';
    else
        _grant_object_type = 'permission';
    end if;

    insert into acls(
        object_type, object_id, user_object_type, user_id, group_id,
        grant_object_type, permission, role_id, restrict_object_type)
    values (
        _object_type, _object_id, _user_object_type, _user_id, _group_id,
        _grant_object_type, _permission, _role_id, _restrict_object_type)
    on conflict do nothing
    returning id into _acl_id;

    if _acl_id is null then
        -- Duplicate key. Just find the ID.
        select find_acl_id(
            _object_type, _object_id, _user_id, _group_id, _permission, _role_id,
            _restrict_object_type) into _acl_id
        ;
        if not found then
            raise exception 'Acl likely deleted concurrently with creation';
        end if;
    else
        -- It was a new ACL. Insert the expanded acls directly in this
        -- operation, rather than taking a lock and recomputing them from
        -- scratch.
        --
        -- Query generated from the following command:
        --
        -- ./scripts/make_expanded_acls_query.py "id = _acl_id"
        with
        candidate_acls as (
            select *
            from acls
            where id = _acl_id
        ),
        joined_acls as (
            select
                candidate_acls.*,
                _expanded_group_users.user_id expanded_user_id,
                _expanded_role_permissions.permission expanded_permission,
                _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
            from
                candidate_acls
                    left join _expanded_group_users using (group_id)
                    left join _expanded_role_permissions using (role_id)
        ),
        coalesced_acls as (
        select
            id acl_id,
            object_type,
            object_id,
            coalesce(user_id, expanded_user_id) as user_id,
            coalesce(permission, expanded_permission) as permission,
            coalesce(restrict_object_type, expanded_restrict_object_type) as restrict_object_type,
            _object_org_id
        from
            joined_acls
        ),
        filtered_acls as (
            select * from coalesced_acls
            where
                -- It is possible that the user specifies an empty group or role, in
                -- which case we don't need to include these entries in the expanded
                -- ACLs.
                user_id is not null
                and permission is not null
        )
        insert into _expanded_acls select * from filtered_acls on conflict do nothing;
    end if;

    return (
        select jsonb_build_object('acl', acls)
        from acls where id = _acl_id
    );
end;
$function$
;
