create or replace view "public"."me" as  SELECT users.id,
    users.auth_id,
    ( SELECT json_agg(jsonb_build_object('id', members.org_id, 'name', organizations.name))
           FROM (members
             JOIN organizations ON ((members.org_id = organizations.id)))
          WHERE (members.user_id = users.id)) AS organizations
   FROM users
  WHERE (users.auth_id = auth.user_id())
  GROUP BY users.id;
