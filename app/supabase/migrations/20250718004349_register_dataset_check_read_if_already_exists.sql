drop function if exists "public"."insert_dataset"(user_id uuid, project_id uuid, name text, description text, metadata jsonb);

CREATE OR REPLACE FUNCTION public.register_dataset(auth_id uuid, org_id uuid, project_id uuid DEFAULT NULL::uuid, project_name text DEFAULT NULL::text, dataset_name text DEFAULT NULL::text, description text DEFAULT NULL::text, update boolean DEFAULT NULL::boolean, metadata jsonb DEFAULT NULL::jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    -- Initialize meaningful defaults.
    _dataset_name text := coalesce(dataset_name, 'logs');
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _project_id uuid;
    _dataset_id uuid;
    _forbid_insert_datasets boolean;
begin
    -- Assign the project_id.
    if register_dataset.project_id is not null then
        -- Check that the project exists.
        select projects.id into _project_id
        from projects
        where
            projects.id = register_dataset.project_id
            and projects.deleted_at isnull
        ;
        if not found then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'project', 'objectId', register_dataset.project_id);
        end if;
    else
        _project_id = get_or_create_project(auth_id, project_name, org_id);
    end if;

    -- First check if the dataset already exists.
    select datasets.id
    from
        datasets
        join projects on datasets.project_id = projects.id
    where
        datasets.project_id = _project_id
        and datasets.name = _dataset_name
        and datasets.deleted_at isnull
        and projects.deleted_at isnull
    into _dataset_id
    ;

    if _dataset_id is not null then
        -- Duplicate key. Check for read permissions.
        if not has_under_project_acl('dataset', _dataset_id, _project_id, 'read', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'read', 'objectType', 'dataset', 'objectId', _dataset_id);
        end if;

        if _update then
            -- Check for update permissions on the dataset.
            if not has_under_project_acl('dataset', _dataset_id, _project_id, 'update', _user_id) then
                raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'update', 'objectType', 'dataset', 'objectId', _dataset_id);
            end if;

            update datasets
            set
                description = register_dataset.description,
                metadata = register_dataset.metadata
            where id = _dataset_id;
        end if;
    else
        -- Check for create permissions and create the dataset.
        select resources.forbid_insert_datasets
        from projects join resources using (org_id)
        where projects.id = _project_id
        into _forbid_insert_datasets
        ;

        if _forbid_insert_datasets or not has_project_acl('dataset', _project_id, 'create', _user_id) then
            raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'create dataset', 'objectType', 'project', 'objectId', _project_id);
        end if;

        insert into datasets(project_id, name, description, user_id, metadata)
        values (_project_id, _dataset_name, register_dataset.description, _user_id, register_dataset.metadata)
        on conflict do nothing
        returning id into _dataset_id
        ;

        if _dataset_id is null then
            -- Dataset was likely created concurrently. Just fetch it.
            select datasets.id from datasets
            where datasets.project_id = _project_id and datasets.name = _dataset_name
            into _dataset_id
            ;

            if not found then
              raise exception 'Dataset % (under project %) likely deleted concurrently with creation',
                  _dataset_name, _project_id;
            end if;
        else
            perform register_acl_unchecked(
                _object_type => 'dataset', _object_id => _dataset_id,
                _user_id => _user_id, _role_id => get_owner_role_id());
        end if;
    end if;

    return (
        select jsonb_build_object('project', projects, 'dataset', datasets)
        from projects join datasets on projects.id = datasets.project_id
        where datasets.id = _dataset_id
    );
end;
$function$
;
