CREATE OR REPLACE FUNCTION public.remove_member_from_org(user_to_remove_id uuid, organization_id uuid, actor_auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$

DECLARE
    user_auth_id uuid;
BEGIN
    -- Check if the user with given actor_auth_id belongs to an organization
     IF NOT EXISTS (
        SELECT 1
        FROM users u
        JOIN members m ON u.id = m.user_id
        JOIN organizations o ON m.org_id = o.id
        WHERE u.auth_id = actor_auth_id AND o.id = organization_id
    )
    THEN
        RAISE EXCEPTION 'User with auth_id % does not belong to organization with id %', auth_id, organization_id;
    END IF;

    DELETE FROM acls WHERE user_id = user_to_remove_id AND _object_org_id = organization_id;
    DELETE FROM group_users WHERE user_id = user_to_remove_id AND _group_org_id = organization_id;
    DELETE FROM members WHERE user_id = user_to_remove_id AND org_id = organization_id;

END;
$function$
;
