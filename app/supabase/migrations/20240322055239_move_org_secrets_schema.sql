CREATE SCHEMA IF NOT EXISTS secrets;
ALTER TABLE org_secrets SET SCHEMA secrets;

DROP  POLICY "Org members can delete org secrets." ON secrets.org_secrets;

drop view if exists "public"."decrypted_org_secrets";
drop function if exists "public"."org_secrets_encrypt_secret_secret"();

create or replace view preview_org_secrets as
select
    org_secrets.id,
    org_secrets.created,
    org_secrets.org_id,
    org_secrets.name,
    CASE WHEN org_secrets.secret IS NULL THEN NULL ELSE '********'::text END as preview_secret,
    organizations.name as org_name,
    org_secrets.type,
    org_secrets.metadata as metadata
from
    secrets.org_secrets org_secrets
    join organizations on org_secrets.org_id = organizations.id
    join members on organizations.id = members.org_id
    join users on members.user_id = users.id
where users.auth_id = auth.user_id();

create or replace function lookup_org_secret(name text)
returns text
language sql
security definer
as $$
  SELECT decrypted_secret FROM secrets.decrypted_org_secrets
    JOIN organizations ON decrypted_org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE
      decrypted_org_secrets.name = lookup_org_secret.name
      AND users.auth_id = auth.user_id()
$$;

grant execute on function lookup_org_secret(text) to anon;

create or replace function lookup_org_secret(
    name text,
    org_name text)
returns text
language sql
security definer
as $$
  SELECT decrypted_secret FROM secrets.decrypted_org_secrets
    JOIN organizations ON decrypted_org_secrets.org_id = organizations.id
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE
      decrypted_org_secrets.name = lookup_org_secret.name
      AND organizations.name = lookup_org_secret.org_name or lookup_org_secret.org_name is null
      AND users.auth_id = auth.user_id()
$$;

grant execute on function lookup_org_secret(text, text) to anon;

create or replace function set_org_secret(
    type text,
    name text,
    metadata json,
    org_name text)
returns uuid
language sql
security definer
as $$
  INSERT INTO secrets.org_secrets (org_id, type, name, metadata)
  SELECT organizations.id, set_org_secret.type, set_org_secret.name, set_org_secret.metadata
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = set_org_secret.org_name
  ON CONFLICT (org_id, name)
  DO UPDATE SET metadata = set_org_secret.metadata, type = set_org_secret.type
  RETURNING id
$$;

grant execute on function set_org_secret(text, text, json, text) to anon;

create or replace function set_org_secret(
    type text,
    name text,
    value text,
    metadata json,
    org_name text)
returns uuid
language sql
security definer
as $$
  INSERT INTO secrets.org_secrets (org_id, type, name, secret, metadata)
  SELECT organizations.id, set_org_secret.type, set_org_secret.name, set_org_secret.value, set_org_secret.metadata
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = set_org_secret.org_name
  ON CONFLICT (org_id, name)
  DO UPDATE SET secret = set_org_secret.value, metadata = set_org_secret.metadata, type = set_org_secret.type
  RETURNING id
$$;

grant execute on function set_org_secret(text, text, text, json, text) to anon;

create or replace function delete_org_secret(name text)
returns uuid
language sql
security definer
as $$
  DELETE FROM secrets.org_secrets WHERE org_id IN (
    SELECT organizations.id
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
  ) AND name = delete_org_secret.name
  RETURNING id
$$;

grant execute on function delete_org_secret(text) to anon;

create or replace function delete_org_secret(
    name text,
    org_name text)
returns uuid
language sql
security definer
as $$
  DELETE FROM secrets.org_secrets WHERE org_id IN (
    SELECT organizations.id
    FROM organizations
    JOIN members ON organizations.id = members.org_id
    JOIN users ON members.user_id = users.id
    WHERE users.auth_id = auth.user_id()
    AND organizations.name = delete_org_secret.org_name
  ) AND name = delete_org_secret.name
  RETURNING id
$$;

grant execute on function delete_org_secret(text, text) to anon;
