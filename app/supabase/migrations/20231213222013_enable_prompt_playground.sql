UPDATE resources SET forbid_insert_prompt_sessions=false, forbid_insert_datasets=false;

create or replace function insert_resource_definition(
    org_id uuid,
    tier resource_tier)
returns void
language plpgsql
security definer
as $$
declare
    _insert_row resources%rowtype;
begin
    _insert_row.org_id := org_id;
    case
        when tier = 'free' then
            _insert_row.forbid_toggle_experiment_public_to_private := true;
            _insert_row.num_private_experiment_row_actions := row(7, 1000);
            _insert_row.num_production_log_row_actions := row(7, 1000);
            _insert_row.forbid_insert_datasets := false;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := true;
        when tier = 'edu' then
            _insert_row.forbid_insert_datasets := true;
            _insert_row.forbid_insert_prompt_sessions := false;
            _insert_row.forbid_access_sql_explorer := false;
        when tier = 'unlimited' then
    end case;

    insert into resources
    select _insert_row.*;
end;
$$;
