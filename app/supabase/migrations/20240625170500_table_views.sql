create type view_type as enum (
    'projects',
    'logs',
    'experiments',
    'datasets',
    'prompts',
    'playgrounds',
    'experiment',
    'dataset'
);

create table views (
    id uuid not null primary key default uuid_generate_v4(),
    object_type acl_object_type not null,
    object_id uuid not null,
    view_type view_type not null,
    name text not null,
    view_data_id uuid not null,
    options jsonb,
    created timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone,
    user_id uuid references users,
    constraint valid_object_type_view_type check (
        case when object_type = 'organization'
             then view_type in ('projects')
             when object_type = 'project'
             then view_type in ('logs', 'experiments', 'datasets', 'prompts', 'playgrounds', 'experiment', 'dataset')
             when object_type = 'experiment'
             then view_type in ('experiment')
             when object_type = 'dataset'
             then view_type in ('dataset')
             else false
        end
    )
);
alter table views enable row level security;

create unique index on views (object_type, object_id, view_type, name, deleted_at) nulls not distinct;

create function register_view_unchecked(
    auth_id uuid,
    object_type acl_object_type default null,
    object_id uuid default null,
    view_type view_type default null,
    name text default null,
    view_data_id uuid default null,
    options jsonb default null,
    update boolean default null)
returns jsonb
language plpgsql
security definer
as $$
declare
    -- Initialize meaningful defaults.
    _update boolean := coalesce(update, false);

    _user_id uuid := get_user_id_by_auth_id(auth_id);
    _view_id uuid;
    _found_existing boolean := false;
begin
    insert into views (object_type, object_id, view_type, name, view_data_id, options, user_id)
    values (
        register_view_unchecked.object_type,
        register_view_unchecked.object_id,
        register_view_unchecked.view_type,
        register_view_unchecked.name,
        register_view_unchecked.view_data_id,
        register_view_unchecked.options,
        _user_id
    )
    on conflict do nothing
    returning id into _view_id
    ;

    if _view_id is null then
        -- Duplicate key.
        _found_existing := true;
        select views.id
        from
            views
        where
            views.object_type = register_view_unchecked.object_type
            and views.object_id = register_view_unchecked.object_id
            and views.view_type = register_view_unchecked.view_type
            and views.name = register_view_unchecked.name
            and views.deleted_at isnull
        into _view_id
        ;
        if not found then
            raise exception 'View % (object_type %, object_id %, view_type %, name %) likely deleted concurrently with creation',
                register_view_unchecked.id, register_view_unchecked.object_type, register_view_unchecked.object_id, register_view_unchecked.view_type, register_view_unchecked.name;
        end if;

        if _update then
            update views
            set
                name = register_view_unchecked.name,
                view_data_id = register_view_unchecked.view_data_id,
                options = register_view_unchecked.options
            where id = _view_id;
        end if;
    end if;

    return (
        select jsonb_build_object('view', views, 'found_existing', _found_existing)
        from views
        where views.id = _view_id
    );
end;
$$;

revoke execute on function register_view_unchecked from public, anon;
