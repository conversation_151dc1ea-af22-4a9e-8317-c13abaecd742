drop function if exists "public"."add_member_to_org_unchecked"(user_to_add_id uuid, organization_id uuid, initial_group_id uuid);

CREATE OR REPLACE FUNCTION public.add_member_to_org_unchecked(user_to_add_id uuid, organization_id uuid, initial_group_ids uuid[])
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
declare
    _added_user_id uuid;
    _initial_group_ids uuid[] := coalesce(initial_group_ids, '{}');
begin
    insert into members(org_id, user_id)
    values (organization_id, user_to_add_id)
    on conflict do nothing
    returning user_id into _added_user_id;

    insert into group_users(group_id, user_id)
    select group_id, user_to_add_id
    from unnest(_initial_group_ids) group_id
    on conflict do nothing;

    return _added_user_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.register_org(auth_id uuid, org_name text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
    _user_id uuid := get_user_id_by_auth_id(register_org.auth_id);
    _org_id uuid;
begin
    insert into
        organizations(name)
    values
        (org_name)
    returning
        id
    into
        _org_id
    ;

    perform add_member_to_org_unchecked(_user_id, _org_id, array [get_group_id(_org_id, 'Owners')]);
    perform insert_resource_definition(_org_id, determine_resource_tier(_user_id));
    return _org_id;
end;
$function$
;

revoke execute on function add_member_to_org_unchecked from anon;
