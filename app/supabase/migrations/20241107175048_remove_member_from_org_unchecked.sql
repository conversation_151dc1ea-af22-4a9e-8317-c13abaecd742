CREATE OR REPLACE FUNCTION public.remove_member_from_org_unchecked(user_to_remove_id uuid, organization_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
begin
    -- We must first delete any instances of rows which are foreign-key linked
    -- to the (user_id, org_id) tuple in the members table.
    delete from acls where user_id = user_to_remove_id and _object_org_id = organization_id;
    delete from group_users where user_id = user_to_remove_id and _group_org_id = organization_id;
    delete from members where user_id = user_to_remove_id and org_id = organization_id;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.remove_member_from_org(user_to_remove_id uuid, organization_id uuid, actor_auth_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    actor_user_id uuid := get_user_id_by_auth_id(actor_auth_id);
begin
    if not has_under_organization_acl('org_member', organization_id, organization_id, 'delete', actor_user_id) then
        raise exception '%', jsonb_build_object('kind', 'not-found', 'permission', 'delete org_member', 'objectType', 'organization', 'objectId', organization_id);
    end if;
    perform remove_member_from_org_unchecked(user_to_remove_id, organization_id);
end;
$function$
;

revoke execute on function remove_member_from_org_unchecked from anon;
revoke execute on function remove_member_from_org from anon;
