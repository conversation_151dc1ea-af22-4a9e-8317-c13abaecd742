alter table "public"."group_inheritors" drop constraint "group_inheritors_group_id_fkey";

alter table "public"."group_inheritors" drop constraint "group_inheritors_inheritor_group_id_fkey";

alter table "public"."role_inheritors" drop constraint "role_inheritors_inheritor_role_id_fkey";

alter table "public"."role_inheritors" drop constraint "role_inheritors_role_id_fkey";

drop view if exists "public"."active_datasets";

drop view if exists "public"."active_experiments";

drop view if exists "public"."active_projects";

drop view if exists "public"."active_prompt_sessions";

drop view if exists "public"."active_prompts";

drop view if exists "public"."organizations_auth_id";

alter table "public"."group_inheritors" drop constraint "group_inheritors_pkey";

alter table "public"."role_inheritors" drop constraint "role_inheritors_pkey";

drop index if exists "public"."group_inheritors_inheritor_group_id_group_id_idx";

drop index if exists "public"."group_inheritors_pkey";

drop index if exists "public"."role_inheritors_inheritor_role_id_role_id_idx";

drop index if exists "public"."role_inheritors_pkey";

drop table "public"."group_inheritors";

drop table "public"."role_inheritors";

alter table "public"."experiments" add constraint "experiments_base_exp_id_fkey" FOREIGN KEY (base_exp_id) REFERENCES experiments(id) not valid;

alter table "public"."experiments" validate constraint "experiments_base_exp_id_fkey";
