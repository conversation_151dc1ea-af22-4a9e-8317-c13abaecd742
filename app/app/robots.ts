import type { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: "*",
        allow: [
          "/",
          "/docs/*",
          "/blog/*",
          "/articles/*",
          "/legal/*",
          "/careers",
          "/pricing",
          "/contact",
          "/sitemap.xml",
        ],
        disallow: [
          "/app/*",
          "/api/*",
          "/_next/*",
          "/_vercel/*",
          "/admin/*",
          "/private/*",
          "/temp/*",
          "/tmp/*",
          "/playground/*",
          "*.json$",
          "*.csv$",
          "*.sql$",
          "*.log$",
        ],
      },
      {
        userAgent: "*",
        allow: "/sitemap.xml",
        disallow: "*.xml$",
      },
    ],
    sitemap: "https://www.braintrust.dev/sitemap.xml",
  };
}
