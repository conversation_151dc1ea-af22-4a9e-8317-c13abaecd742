import "#/styles/globals.css";
import Analytics<PERSON>rovider from "#/ui/analytics-provder";
import DatadogRumProvider from "#/ui/datadog-rum-provider";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { buildMetadata } from "./metadata";
import localFont from "next/font/local";
import { Inter } from "next/font/google";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { getNonce } from "#/security/csp";

const clerkLocalization = {
  signUp: {
    start: {
      title: "Create your Braintrust account",
      actionText: "Already have a Braintrust account?",
    },
  },
  formFieldInputPlaceholder__emailAddress: "Enter email address",
  formFieldInputPlaceholder__password: "Enter password",
};

const btSans = localFont({
  src: [
    {
      path: "./bt-sans-regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "./bt-sans-semibold.woff2",
      weight: "600",
      style: "normal",
    },
    {
      path: "./bt-sans-bold.woff2",
      weight: "700",
      style: "normal",
    },
  ],
  display: "swap",
  variable: "--font-display",
});

const suisseMono = localFont({
  src: [
    { path: "./SuisseIntlMono-Regular.otf", weight: "400", style: "normal" },
    {
      path: "./SuisseIntlMono-Bold.otf",
      weight: "700",
      style: "bold",
    },
  ],
  display: "swap",
  variable: "--font-suisse",
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      suppressHydrationWarning
      className={`${btSans.variable} ${inter.variable} ${suisseMono.variable} antialiased`}
      lang="en"
    >
      {/* Uncomment to enable react-scan */}
      {/* <head>
        <script src="https://unpkg.com/react-scan/dist/auto.global.js" async />
      </head> */}
      <body suppressHydrationWarning>
        <ClerkProvider
          nonce={await getNonce()}
          dynamic
          localization={clerkLocalization}
          appearance={{
            cssLayerName: "clerk",
            layout: {
              socialButtonsPlacement: "bottom",
            },
            elements: {
              logoBox: "hidden",
              headerSubtitle: "hidden",
              card: "bg-inherit px-0 shadow-none",
              cardBox:
                "w-full shadow-none max-w-full overflow-visible border-0",
              rootBox: "mx-auto w-full",
              headerTitle:
                "text-balance text-4xl font-medium leading-tighter tracking-[-0.01em] sm:text-5xl text-left text-primary-900 font-display",
              formFieldLabel: "text-primary-800 font-normal",
              input:
                "rounded-md h-11 px-3 !shadow-none !border max-h-none border-primary-200 hover:border-primary-300 text-primary-800 bg-primary-50",
              formFieldErrorText: "!text-bad-700 text-xs",
              otpCodeFieldErrorText: "!text-bad-700 text-xs",
              formFieldSuccessText: "text-xs",
              formFieldInfoText: "text-xs",
              formFieldInputShowPasswordButton:
                "w-7 h-7 !p-0 flex items-center justify-center mr-2 text-primary-800 hover:text-primary-800",
              formFieldAction: "text-accent-700 text-sm hover:text-accent-600",
              formButtonPrimary:
                "!shadow-none flex-none inline-flex items-center justify-center rounded-md transition-colors font-medium disabled:pointer-events-none disabled:opacity-60 disabled:bg-primary-50 gap-2 !bg-accent-700 dark:!bg-accent-400 text-white !hover:bg-accent-600 disabled:text-inherit h-11 px-8 w-full text-base",
              footer: "bg-none p-0 py-4 justify-start items-start",
              footerAction: "border-0 m-0 !px-0",
              footerActionText: "text-sm",
              footerActionLink: "text-accent-700 text-sm hover:text-accent-600",
              footerPagesLink: "text-accent-700 text-sm",
              buttonArrowIcon: "hidden",
              dividerText: "uppercase text-xs px-2 text-primary-400",
              button:
                "h-11 !shadow-none !border border-primary-200 bg-primary-100 hover:bg-primary-200 transition-colors after:hidden text-primary-800",
              alternativeMethodsBlockButton:
                "h-11 !shadow-none !border border-primary-200 bg-primary-100 hover:bg-primary-200 transition-colors after:hidden text-primary-800",
              socialButtonsBlockButton:
                "h-11 !shadow-none !border border-primary-200 bg-primary-100 hover:bg-primary-200 transition-colors",
              socialButtonsBlockButtonText:
                "text-primary-800 font-medium text-sm",
              dividerLine: "bg-primary-200",
              identityPreview: "pt-6 justify-start",
              identityPreviewEditButton: "!border-0 !bg-transparent h-auto",
              backLink: "text-primary-600",
              formResendCodeLink: "!border-0 !bg-transparent self-start",
              otpCodeField: "w-full",
              otpCodeFieldInputs: "w-full flex",
              otpCodeFieldInput:
                "flex-1 rounded-md max-h-none h-16 text-xl font-medium",
              alert:
                "bg-bad-50 p-2 text-bad-600 items-center gap-1 border-bad-200 hover:border-bad-200",
              alertIcon: "text-bad-600",
              alertText: "text-sm text-bad-600",
            },
            variables: {
              fontSize: "1rem",
            },
          }}
        >
          <NuqsAdapter>
            <DatadogRumProvider>
              <AnalyticsProvider>{children}</AnalyticsProvider>
            </DatadogRumProvider>
          </NuqsAdapter>
        </ClerkProvider>
      </body>
    </html>
  );
}

export const metadata = buildMetadata({
  skipTitleFormatting: true,
  title:
    "Braintrust - The evals and observability platform for building reliable AI agents",
});

export const viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ddd" },
    { media: "(prefers-color-scheme: dark)", color: "#222" },
  ],
};
