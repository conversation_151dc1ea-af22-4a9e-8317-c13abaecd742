"use client";

import { useMemo, useRef } from "react";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useGetRequest } from "#/utils/btapi/get";
import { lastIndexOperationWithObjectId } from "@braintrust/local/app-schema";
import { z } from "zod";
import { zodErrorToString } from "#/utils/validation";
import { Button } from "#/ui/button";
import { RefreshCw } from "lucide-react";
import { ProcessTable } from "./process-table";

export function ClientPage({
  apiUrl,
  orgId,
}: {
  apiUrl: string;
  orgId: string | undefined;
}) {
  const isClient = useIsClient();

  const {
    data: activeBackfillOperations,
    error: requestError,
    refresh: refreshStatus,
  } = useGetRequest(
    `/brainstore/backfill/status/active`,
    useMemo(() => ({ ...(orgId ? { org_id: orgId } : {}) }), [orgId]),
    {
      apiUrl,
    },
  );

  const [parsedData, parseError] = useMemo(() => {
    if (!activeBackfillOperations) {
      return [null, null];
    }

    const status = z
      .array(lastIndexOperationWithObjectId)
      .safeParse(activeBackfillOperations);
    if (status.success) {
      return [status.data, null];
    } else {
      return [null, zodErrorToString(status.error, 2, true)];
    }
  }, [activeBackfillOperations]);

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const error = requestError ?? parseError;

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <div
      className="flex flex-1 flex-col overflow-auto"
      ref={scrollContainerRef}
    >
      <div className="mb-2 flex-none px-5">
        <Button
          size="xs"
          Icon={RefreshCw}
          className="flex-none"
          onClick={() => refreshStatus()}
        >
          Refresh
        </Button>
      </div>
      {error && (
        <div className="mb-2 flex flex-none items-center gap-1.5 rounded-md border border-rose-500/10 bg-rose-500/5 p-2 font-mono text-xs font-semibold text-bad-700">
          {`${error}`}
        </div>
      )}
      <div className="@container flex flex-1 flex-col px-5 pb-8">
        <ProcessTable
          indexOperations={parsedData}
          scrollContainerRef={scrollContainerRef}
        />
      </div>
    </div>
  );
}
