import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import SessionRoot from "#/ui/root";
import { cn } from "#/utils/classnames";
import { MultiTenantApiURL } from "#/utils/user-types";
import { ClientPage } from "./clientpage";

export default async function ProcessesPage() {
  return (
    <SessionRoot loginRequired>
      <DefaultHead />
      <Header isAdmin />
      <MainContentWrapper
        hideFooter
        className={cn(
          "flex flex-col overflow-hidden p-0",
          HEIGHT_WITH_TOP_OFFSET,
        )}
      >
        <h1 className="flex-none px-5 py-3 text-xl font-semibold">
          Brainstore processes
        </h1>
        <ClientPage apiUrl={MultiTenantApiURL} orgId={undefined} />
      </MainContentWrapper>
    </SessionRoot>
  );
}
