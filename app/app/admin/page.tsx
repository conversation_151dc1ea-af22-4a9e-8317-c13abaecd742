import { adminFetchAllOrgs } from "#/app/admin/actions";
import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import SessionRoot from "#/ui/root";
import { cn } from "#/utils/classnames";
import { HEIGHT_WITH_TOP_OFFSET } from "../app/body-wrapper";
import { ClientPage } from "./clientpage";

export default async function AdminPage() {
  const orgRows = await adminFetchAllOrgs({});

  return (
    <SessionRoot loginRequired>
      <div className="flex flex-1 flex-col">
        <DefaultHead />
        <Header isAdmin />
        <MainContentWrapper
          hideFooter
          className={cn(
            "flex flex-col overflow-hidden p-0",
            HEIGHT_WITH_TOP_OFFSET,
          )}
        >
          <div className="px-5 py-3">
            <h1 className="text-2xl font-semibold">Organizations</h1>
          </div>
          <ClientPage orgRows={orgRows} />
        </MainContentWrapper>
      </div>
    </SessionRoot>
  );
}
