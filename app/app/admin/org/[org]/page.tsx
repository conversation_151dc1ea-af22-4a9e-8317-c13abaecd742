import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import SessionRoot from "#/ui/root";
import { decodeURIComponentPatched } from "#/utils/url";
import { ClientPage } from "./clientpage";
import { adminFetchAllOrgs, adminFetchAllProjects } from "../../actions";
import { cn } from "#/utils/classnames";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";

interface Params {
  org: string;
}

export default async function AdminPage(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);

  const orgInfo = await adminFetchAllOrgs({ name: orgName });
  if (orgInfo.length === 0) {
    return <div>Org not found</div>;
  }

  const projectRows = await adminFetchAllProjects({
    orgName,
  });

  return (
    <SessionRoot loginRequired>
      <div className="flex flex-1 flex-col">
        <DefaultHead />
        <Header isAdmin />
        <MainContentWrapper
          hideFooter
          className={cn(
            "flex flex-col overflow-hidden p-0",
            HEIGHT_WITH_TOP_OFFSET,
          )}
        >
          <div className="px-5 py-3">
            <h1 className="mb-2 text-2xl font-semibold">{orgName}</h1>
          </div>
          <ClientPage orgInfo={orgInfo} projectRows={projectRows} />
        </MainContentWrapper>
      </div>
    </SessionRoot>
  );
}
