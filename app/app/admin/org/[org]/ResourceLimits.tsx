import { useState, useEffect, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { ChevronDown } from "lucide-react";
import { Input } from "#/ui/input";
import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { type adminUpdateResourceLimit } from "../../actions";
import { FREE_TIER_LOG_BYTES_LIMIT } from "./resources";

// NOTE: keep this in sync with control plane's special value
const UNLIMITED = -1 as const;

export const SPAN_LIMIT_OPTIONS = [
  { label: "1M spans/month", value: 1_000_000 },
  { label: "2M spans/week", value: 2_000_000 },
  { label: "Unlimited (clear)", value: UNLIMITED },
];

export const LOG_BYTES_LIMIT_OPTIONS = [
  { label: "Free plan limit (~1 GiB)", value: FREE_TIER_LOG_BYTES_LIMIT },
  { label: "Unlimited (clear)", value: UNLIMITED },
];

// Helper function to get the display label for an option
function getOptionLabel(option: { label: string; value: number | null }) {
  return option.value === null ? "Unlimited" : option.label;
}

/**
 * Unified ResourceLimits component that handles both span limits and log bytes limits
 */
export function ResourceLimits({
  spanLimitValue,
  logBytesLimitValue,
  orgId,
  refreshOrgRows,
}: {
  spanLimitValue: number | null;
  logBytesLimitValue: number | null;
  orgId: string;
  refreshOrgRows: () => Promise<void>;
}) {
  const [spanInputValue, setSpanInputValue] = useState<string>(
    spanLimitValue === null ? "" : spanLimitValue.toString(),
  );
  const [spanDropdownOpen, setSpanDropdownOpen] = useState(false);

  const [logBytesInputValue, setLogBytesInputValue] = useState<string>(
    logBytesLimitValue === null ? "" : logBytesLimitValue.toString(),
  );
  const [logBytesDropdownOpen, setLogBytesDropdownOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  const { getToken } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    setSpanInputValue(spanLimitValue === null ? "" : spanLimitValue.toString());
  }, [spanLimitValue]);

  useEffect(() => {
    setLogBytesInputValue(
      logBytesLimitValue === null ? "" : logBytesLimitValue.toString(),
    );
  }, [logBytesLimitValue]);

  const parseSpanInputValue = useCallback(
    (value: string): number | null => {
      if (!value || value.trim() === "") return null;
      const match = value.match(/^\d+$/);
      if (!match) return spanLimitValue; // Keep current value if parsing fails
      return parseInt(value, 10);
    },
    [spanLimitValue],
  );

  const parseLogBytesInputValue = useCallback(
    (value: string): number | null => {
      if (!value || value.trim() === "") return null;
      const match = value.match(/^\d+$/);
      if (!match) return logBytesLimitValue; // Keep current value if parsing fails
      return parseInt(value, 10);
    },
    [logBytesLimitValue],
  );

  const updateLimits = useCallback(
    async (newSpanLimit: number | null, newLogBytesLimit: number | null) => {
      setIsLoading(true);
      try {
        // Create the args object with both limit properties
        const args = {
          orgId,
          spanLimit: newSpanLimit ?? UNLIMITED,
          logBytesLimit: newLogBytesLimit ?? UNLIMITED,
        };

        await invokeServerAction<typeof adminUpdateResourceLimit>({
          fName: "adminUpdateResourceLimit",
          args,
          getToken,
        });

        // Invalidate the specific org query
        await refreshOrgRows();

        // Also invalidate the query that fetches all organizations
        queryClient.invalidateQueries({
          queryKey: ["adminFetchAllOrgs"],
        });
      } catch (error) {
        toast.error("Failed to update resource limits");
      } finally {
        setIsLoading(false);
      }
    },
    [getToken, orgId, refreshOrgRows, queryClient],
  );

  const handleSpanLimitChange = useCallback(
    (newSpanLimit: number | null) => {
      if (newSpanLimit !== spanLimitValue) {
        updateLimits(newSpanLimit, logBytesLimitValue);
      }
    },
    [spanLimitValue, logBytesLimitValue, updateLimits],
  );

  // Handle log bytes limit change
  const handleLogBytesLimitChange = useCallback(
    (newLogBytesLimit: number | null) => {
      if (newLogBytesLimit !== logBytesLimitValue) {
        updateLimits(spanLimitValue, newLogBytesLimit);
      }
    },
    [spanLimitValue, logBytesLimitValue, updateLimits],
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="text-xs text-primary-500">Span/Experiment/Log Limits</div>
      <div className="flex w-full items-center gap-1">
        <div className="relative flex-1">
          <Input
            disabled={isLoading}
            value={spanInputValue}
            onChange={(e) => setSpanInputValue(e.target.value)}
            onBlur={() => {
              const newLimit = parseSpanInputValue(spanInputValue);
              handleSpanLimitChange(newLimit);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.currentTarget.blur();
              }
            }}
            className="h-8 text-sm"
            placeholder="Enter number"
            type="number"
          />
        </div>
        <DropdownMenu
          open={spanDropdownOpen}
          onOpenChange={setSpanDropdownOpen}
        >
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <Button size="sm" variant="border" className="h-8">
              <ChevronDown className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="min-w-40">
            <DropdownMenuLabel>Select preset</DropdownMenuLabel>
            {SPAN_LIMIT_OPTIONS.map((option) => (
              <DropdownMenuItem
                disabled={isLoading}
                key={option.value?.toString() ?? "null"}
                onClick={(e) => {
                  e.stopPropagation();
                  setSpanInputValue(
                    option.value === null ? "" : option.value.toString(),
                  );
                  handleSpanLimitChange(option.value);
                }}
              >
                {getOptionLabel(option)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Log Bytes Limit Control */}
      <div className="text-xs font-medium text-primary-500">
        Ingested Log Limits (in Bytes)
      </div>
      <div className="flex w-full items-center gap-1">
        <div className="relative flex-1">
          <Input
            disabled={isLoading}
            value={logBytesInputValue}
            onChange={(e) => setLogBytesInputValue(e.target.value)}
            onBlur={() => {
              const newLimit = parseLogBytesInputValue(logBytesInputValue);
              handleLogBytesLimitChange(newLimit);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.currentTarget.blur();
              }
            }}
            className="h-8 text-sm"
            placeholder="Enter number"
            type="number"
          />
        </div>
        <DropdownMenu
          open={logBytesDropdownOpen}
          onOpenChange={setLogBytesDropdownOpen}
        >
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <Button size="sm" variant="border" className="h-8">
              <ChevronDown className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="min-w-40">
            <DropdownMenuLabel>Select preset</DropdownMenuLabel>
            {LOG_BYTES_LIMIT_OPTIONS.map((option) => (
              <DropdownMenuItem
                disabled={isLoading}
                key={option.value?.toString() ?? "null"}
                onClick={(e) => {
                  e.stopPropagation();
                  setLogBytesInputValue(
                    option.value === null ? "" : option.value.toString(),
                  );
                  handleLogBytesLimitChange(option.value);
                }}
              >
                {getOptionLabel(option)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
