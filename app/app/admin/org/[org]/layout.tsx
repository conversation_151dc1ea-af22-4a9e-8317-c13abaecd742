import ClientLayout from "./clientlayout";
import { decodeURIComponentPatched } from "#/utils/url";
import { adminFetchOrgContextInfo } from "../../actions";

export default async function RootLayout(props: {
  children: React.ReactNode;
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;

  const { org } = params;

  const { children } = props;

  const orgName = decodeURIComponentPatched(org);
  const orgContext = await adminFetchOrgContextInfo({ name: orgName });
  if (!orgContext) {
    return <div>Org not found</div>;
  }

  return <ClientLayout orgContext={orgContext}>{children}</ClientLayout>;
}
