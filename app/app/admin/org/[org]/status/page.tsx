import { adminFetchAllOrgs } from "#/app/admin/actions";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { cn } from "#/utils/classnames";
import SessionRoot from "#/ui/root";
import { decodeURIComponentPatched } from "#/utils/url";
import { MultiTenantApiURL } from "#/utils/user-types";
import { ClientPage } from "./clientpage";

interface Params {
  org: string;
}

export default async function ProcessesPage(props: {
  params: Promise<Params>;
}) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);

  const orgInfo = await adminFetchAllOrgs({ name: orgName });
  if (orgInfo.length === 0) {
    return <div>Org not found</div>;
  }

  return (
    <SessionRoot loginRequired>
      <div className="flex flex-1 flex-col">
        <DefaultHead />
        <Header isAdmin />
        <MainContentWrapper
          hideFooter
          className={cn(
            "flex flex-col overflow-hidden p-0",
            HEIGHT_WITH_TOP_OFFSET,
          )}
        >
          <div className="px-5 py-3">
            <h1 className="mb-2 text-2xl font-semibold">Brainstore status</h1>
          </div>
          <ClientPage apiUrl={orgInfo[0].api_url ?? MultiTenantApiURL} />
        </MainContentWrapper>
      </div>
    </SessionRoot>
  );
}
