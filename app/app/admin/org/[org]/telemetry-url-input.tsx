import { useState, useCallback, useEffect } from "react";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { ChevronDown } from "lucide-react";
import { Input } from "#/ui/input";
import { Button } from "#/ui/button";
import { Spinner } from "#/ui/icons/spinner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { type adminUpdateTelemetryUrl } from "../../actions";
import { useQueryClient } from "@tanstack/react-query";

const TELEMETRY_URL_OPTIONS = [
  {
    label: "Live",
    value: "https://www.braintrust.dev/api/billing/telemetry/v1/events",
  },
  {
    label: "Test",
    value: "https://878fvqgzmi.execute-api.us-east-2.amazonaws.com/v1/events",
  },
  {
    label: "Local",
    value: "http://127.0.0.1:8001/events",
  },
  {
    label: "Clear",
    value: "",
  },
];

export function TelemetryUrlInput({
  orgId,
  telemetryUrl,
  refreshOrgRows,
}: {
  orgId: string;
  telemetryUrl: string | null;
  refreshOrgRows: () => Promise<void>;
}) {
  const { getToken } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    setInputValue(telemetryUrl ?? "");
  }, [telemetryUrl]);

  const updateTelemetryUrl = useCallback(
    async (newValue: string) => {
      setIsUpdating(true);
      try {
        await invokeServerAction<typeof adminUpdateTelemetryUrl>({
          fName: "adminUpdateTelemetryUrl",
          args: {
            orgId,
            telemetryUrl: newValue || undefined,
          },
          getToken,
        });

        // Invalidate the specific org query
        await refreshOrgRows();

        // Also invalidate the query that fetches all organizations
        queryClient.invalidateQueries({
          queryKey: ["adminFetchAllOrgs"],
        });

        toast.success(
          newValue ? "Telemetry URL updated" : "Telemetry URL cleared",
        );
      } catch (error) {
        toast.error("Failed to update telemetry URL");
      } finally {
        setIsUpdating(false);
      }
    },
    [getToken, orgId, refreshOrgRows, queryClient],
  );

  const handleTelemetryUrlChange = useCallback(
    (newValue: string) => {
      if (newValue !== telemetryUrl) {
        updateTelemetryUrl(newValue);
      }
    },
    [telemetryUrl, updateTelemetryUrl],
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="text-xs font-medium text-primary-500">Telemetry URL</div>
      <div className="flex w-full items-center gap-1">
        <div className="relative flex-1">
          <Input
            disabled={isUpdating}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onBlur={() => {
              handleTelemetryUrlChange(inputValue);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.currentTarget.blur();
              }
            }}
            className="h-8 text-sm"
            placeholder="Enter telemetry URL"
            type="url"
          />
          {isUpdating && (
            <div className="absolute top-1/2 right-3 -translate-y-1/2">
              <Spinner className="size-3" />
            </div>
          )}
        </div>
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <Button size="sm" variant="border" className="h-8">
              <ChevronDown className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="min-w-40">
            <DropdownMenuLabel>Select preset</DropdownMenuLabel>
            {TELEMETRY_URL_OPTIONS.map((option) => (
              <DropdownMenuItem
                disabled={isUpdating}
                key={option.label}
                onClick={(e) => {
                  e.stopPropagation();
                  setInputValue(option.value);
                  handleTelemetryUrlChange(option.value);
                }}
              >
                {option.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
