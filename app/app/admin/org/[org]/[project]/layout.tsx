import ClientLayout from "./clientlayout";
import { decodeURIComponentPatched } from "#/utils/url";
import { adminFetchProjectContextInfo } from "#/app/admin/actions";

export default async function RootLayout(props: {
  children: React.ReactNode;
  params: Promise<{ org: string; project: string }>;
}) {
  const params = await props.params;

  const { org, project } = params;

  const { children } = props;

  const orgName = decodeURIComponentPatched(org);
  const projectName = decodeURIComponentPatched(project);
  const projectContext = await adminFetchProjectContextInfo({
    orgName,
    projectName,
  });
  if (!projectContext) {
    return <div>Project not found</div>;
  }

  return (
    <ClientLayout
      orgName={orgName}
      projectName={projectName}
      adminProjectContext={projectContext}
    >
      {children}
    </ClientLayout>
  );
}
