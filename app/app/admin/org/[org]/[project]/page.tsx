import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import SessionRoot from "#/ui/root";
import { ClientPage } from "./clientpage";
import {
  adminFetchAllOrgs,
  adminFetchAllProjects,
  adminFetchObjects,
} from "../../../actions";
import Link from "next/link";
import { cn } from "#/utils/classnames";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";

interface Params {
  org: string;
  project: string;
}

export default async function AdminPage(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponent(params.org);
  const projectName = decodeURIComponent(params.project);

  const [orgRows, projectRows, objectRows] = await Promise.all([
    adminFetchAllOrgs({
      name: orgName,
    }),
    adminFetchAllProjects({
      orgName,
      projectName,
    }),
    adminFetchObjects({
      orgName,
      projectName,
    }),
  ]);

  if (!projectRows.length) {
    return <div>Project not found</div>;
  }

  return (
    <SessionRoot loginRequired>
      <div className="flex-1">
        <DefaultHead />
        <Header isAdmin />
        <MainContentWrapper
          hideFooter
          className={cn(
            "flex flex-1 flex-col overflow-hidden p-0",
            HEIGHT_WITH_TOP_OFFSET,
          )}
        >
          <h1 className="px-5 py-3 text-2xl font-semibold">
            <Link
              className="text-accent-600"
              href={`/admin/org/${encodeURIComponent(orgName)}`}
            >
              {orgName}
            </Link>{" "}
            / {projectName}
          </h1>
          <ClientPage
            orgName={orgName}
            projectName={projectName}
            orgRows={orgRows}
            projectRows={projectRows}
            objectRows={objectRows}
          />
        </MainContentWrapper>
      </div>
    </SessionRoot>
  );
}
