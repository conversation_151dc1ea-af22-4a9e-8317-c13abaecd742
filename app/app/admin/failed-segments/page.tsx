import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import SessionRoot from "#/ui/root";
import { cn } from "#/utils/classnames";
import { MultiTenantApiURL } from "#/utils/user-types";
import { ClientPage } from "./clientpage";

export default async function FailedSegmentsPage() {
  return (
    <SessionRoot loginRequired>
      <div className="flex flex-1 flex-col">
        <DefaultHead />
        <Header isAdmin />
        <MainContentWrapper
          hideFooter
          className={cn(
            "flex flex-col overflow-hidden p-0",
            HEIGHT_WITH_TOP_OFFSET,
          )}
        >
          <div className="px-5 py-3">
            <h1 className="mb-2 text-2xl font-semibold">
              Failed Brainstore segments
            </h1>
          </div>
          <ClientPage apiUrl={MultiTenantApiURL} orgId={undefined} />
        </MainContentWrapper>
      </div>
    </SessionRoot>
  );
}
