import { getNonce } from "#/security/csp";
import { Logo } from "#/ui/landing/logo";
import { ThemeProvider } from "#/ui/theme-provider";
import Link from "next/link";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      nonce={await getNonce()}
    >
      <main className="z-10 flex justify-center py-10 sm:items-center">
        <div className="mx-auto w-full max-w-xl md:py-16">
          <div className="rounded-xl border-primary-100 px-8 py-10 md:border">
            <Link href="/">
              <Logo width={150} className="mb-12" />
            </Link>
            {children}
          </div>
        </div>
      </main>
    </ThemeProvider>
  );
}
