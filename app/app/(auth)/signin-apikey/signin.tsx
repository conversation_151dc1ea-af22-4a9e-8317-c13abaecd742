"use client";
import { But<PERSON> } from "#/ui/button";
import { Input } from "#/ui/input";
import { signIn as nextAuthSignIn } from "next-auth/react";

export function NextAuthSignIn() {
  const credentialsAction = (formData: FormData) => {
    nextAuthSignIn("credentials", {
      api_key: formData.get("api_key"),
    });
  };

  return (
    <div className="flex flex-col gap-2">
      <h1 className="pt-8 pb-4 text-left font-display text-4xl leading-tight font-medium tracking-[-0.01em] text-balance text-primary-900 sm:text-5xl">
        Sign in with API key
      </h1>
      <form action={credentialsAction}>
        <label htmlFor="credentials-api_key" className="mb-2 block">
          <span className="mb-2 block text-base">API key</span>
          <Input
            placeholder="Enter API key"
            type="password"
            className="text-base"
            id="credentials-api_key"
            name="api_key"
          />
        </label>
        <Button
          variant="primary"
          type="submit"
          className="mt-4 w-full text-base"
        >
          Sign In
        </Button>
      </form>
    </div>
  );
}
