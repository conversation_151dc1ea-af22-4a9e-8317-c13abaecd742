"use client";

import { cn } from "#/utils/classnames";
import { type PropsWithChildren } from "react";
import { useIsSidenavDocked } from "./[org]/sidenav-state";

export const HEIGHT_WITH_TOP_OFFSET = "h-[calc(100vh-44px)]";
export const HEIGHT_WITH_DOUBLE_TOP_OFFSET = "h-[calc(100vh-84px)]";

export const BodyWrapper = ({
  children,
  outerClassName,
  isSidenavDocked: isSidenavDockedProp,
  innerClassName,
}: PropsWithChildren<{
  height?: number;
  outerClassName?: string;
  isSidenavDocked?: boolean;
  innerClassName?: string;
}>) => {
  const isSidenavDockedHookValue = useIsSidenavDocked();
  const isSidenavDocked = isSidenavDockedProp ?? isSidenavDockedHookValue;
  return (
    <div
      className={cn(
        "flex bg-primary-50",
        HEIGHT_WITH_TOP_OFFSET,
        outerClassName,
      )}
    >
      <div
        className={cn(
          "flex-1 overflow-hidden rounded-tl-md border-t border-l border-primary-200/80 bg-background",
          {
            "rounded-none border-l-0": !isSidenavDocked,
          },
          innerClassName,
        )}
      >
        {children}
      </div>
    </div>
  );
};
