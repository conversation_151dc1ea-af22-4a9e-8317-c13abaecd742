"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { TileList, TileListItem } from "#/ui/tile-list";
import { Suspense, useMemo } from "react";
import { getOrgLink } from "../[org]/getOrgLink";
import { useUser } from "#/utils/user";
import { Spinner } from "#/ui/icons/spinner";

interface Props {
  subroute?: string;
}

export default function ClientPage({ subroute }: Props) {
  const { orgs } = useUser();
  const organizations = useMemo(() => Object.values(orgs), [orgs]);

  useAnalytics({ page: { category: "multiorg-settings" } });

  return (
    <MainContentWrapper contentTitle="Select an organization to edit its settings">
      <Suspense fallback={<Spinner />}>
        <TileList>
          {organizations.map(({ name, id }) => {
            return (
              <TileListItem
                key={id}
                href={`${getOrgLink({
                  orgName: name,
                })}/settings/${subroute || ""}`}
                title={name}
                subtitle={""}
              />
            );
          })}
        </TileList>
      </Suspense>
    </MainContentWrapper>
  );
}
