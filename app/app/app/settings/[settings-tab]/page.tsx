import { getFirstOrg } from "../actions";
import { notFound, redirect } from "next/navigation";
import { getOrgLink } from "../../[org]/getOrgLink";

export default async function Page(props: {
  params: Promise<{ settingsTab: string }>;
}) {
  const params = await props.params;
  const orgInfo = await getFirstOrg();

  if (orgInfo?.orgName) {
    redirect(
      `${getOrgLink({
        orgName: orgInfo.orgName,
      })}/settings/${params.settingsTab || ""}`,
    );
  }
  notFound();
}
