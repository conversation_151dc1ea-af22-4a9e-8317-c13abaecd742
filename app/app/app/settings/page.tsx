import { getFirstOrg } from "./actions";
import { redirect } from "next/navigation";
import { getOrgLink } from "../[org]/getOrgLink";
import SessionRoot from "#/ui/root";
import ClientPage from "./clientpage";

export default async function Page(props: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  searchParams: Promise<Record<string, any>>;
}) {
  const searchParams = await props.searchParams;
  const orgInfo = await getFirstOrg();

  if (orgInfo?.total === 1 && orgInfo?.orgName) {
    redirect(
      `${getOrgLink({
        orgName: orgInfo.orgName,
      })}/settings/${searchParams.subroute || ""}`,
    );
  }

  return (
    <SessionRoot loginRequired>
      <ClientPage subroute={searchParams.subroute} />
    </SessionRoot>
  );
}
