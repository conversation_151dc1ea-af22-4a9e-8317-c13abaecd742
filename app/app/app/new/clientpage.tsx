"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { TileList, TileListItem } from "#/ui/tile-list";
import { useRouter } from "next/navigation";
import { Suspense, useMemo } from "react";
import { getOrgLink } from "../[org]/getOrgLink";
import { useUser } from "#/utils/user";
import { Spinner } from "#/ui/icons/spinner";

interface Props {}

export default function ClientPage({}: Props) {
  const router = useRouter();
  const { orgs } = useUser();
  const organizations = useMemo(() => Object.values(orgs), [orgs]);

  useAnalytics({ page: { category: "multiorg-new" } });

  if (organizations.length === 1) {
    // redirect to the only project
    router.replace(
      `${getOrgLink({
        orgName: organizations[0].name,
      })}?new=1`,
    );
  }

  return (
    <MainContentWrapper
      contentTitle="Select an organization to create a new eval"
      className="min-h-screen"
    >
      <Suspense fallback={<Spinner />}>
        <TileList>
          {organizations.map(({ name, id }) => {
            return (
              <TileListItem
                key={id}
                href={`${getOrgLink({
                  orgName: name,
                })}?new=1`}
                title={name}
                subtitle={""}
              />
            );
          })}
        </TileList>
      </Suspense>
    </MainContentWrapper>
  );
}
