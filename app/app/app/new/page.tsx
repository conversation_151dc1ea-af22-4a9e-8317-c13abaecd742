import { redirect } from "next/navigation";
import { getOrgLink } from "../[org]/getOrgLink";
import SessionRoot from "#/ui/root";
import ClientPage from "./clientpage";
import { getFirstOrg } from "../settings/actions";

export default async function Page(props: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  searchParams: Promise<Record<string, any>>;
}) {
  const orgInfo = await getFirstOrg();

  if (orgInfo?.total === 1 && orgInfo?.orgName) {
    redirect(
      `${getOrgLink({
        orgName: orgInfo.orgName,
      })}?new=1`,
    );
  }

  return (
    <SessionRoot loginRequired>
      <ClientPage />
    </SessionRoot>
  );
}
