"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { PlainInput } from "#/ui/plain-input";
import { parseAsString, useQueryState } from "nuqs";
import { useState } from "react";
import { useSelectedProjectState } from "#/ui/query-parameters";
import {
  type ExtendedExperiment,
  getExperiment,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/experiment-actions";
import { toast } from "sonner";

export default function ClientPage() {
  const [orgName, setOrgName] = useQueryState("o", parseAsString);
  const [projectName, setProjectName] = useSelectedProjectState();
  const [experimentName, setExperimentName] = useQueryState("e", parseAsString);

  const [experiment, setExperiment] = useState<
    ExtendedExperiment | null | undefined
  >(undefined);
  const refresh = async () => {
    try {
      if (orgName && projectName && experimentName) {
        setExperiment(
          await getExperiment({
            org_name: orgName,
            project_name: projectName,
            experiment_name: experimentName,
          }),
        );
      }
    } catch (e) {
      toast.error(`Failed to lookup experiment`, {
        description: `${e}`,
      });
    }
  };

  const registerOrg = async () => {
    const result = await fetch("/api/org/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        org_name: orgName,
      }),
    });
    if (!result.ok) {
      toast.error(`Failed to create org`, {
        description: await result.text(),
      });
    } else {
      toast(`Created org ${orgName}`);
    }
  };

  return (
    <MainContentWrapper>
      <div>
        <h1>Lookup</h1>
        <div className="mt-2">
          <PlainInput
            type="text"
            placeholder="org name"
            className="w-[400px]"
            value={orgName || ""}
            onChange={(e) => setOrgName(e.target.value)}
          />
        </div>
        <div className="mt-2">
          <PlainInput
            type="text"
            placeholder="project name"
            className="w-[400px]"
            value={projectName || ""}
            onChange={(e) => setProjectName(e.target.value)}
          />
        </div>
        <div className="mt-2">
          <PlainInput
            type="text"
            placeholder="experiment name"
            className="w-[400px]"
            value={experimentName || ""}
            onChange={(e) => setExperimentName(e.target.value)}
          />
        </div>
        <div className="mt-4">
          <Button className="text-sm" onClick={refresh}>
            Lookup
          </Button>
        </div>
        <div className="mt-4">
          <Button className="text-sm" onClick={registerOrg}>
            Register org
          </Button>
        </div>
      </div>

      {experiment !== undefined && (
        <div className="mt-8">
          <h1>Experiment</h1>
          <div className="mt-2">
            {experiment ? (
              <div>
                {`${experiment.name} (${experiment.id})`}
                <h2 className="mt-2 font-bold">Can compare to</h2>
                <ul>
                  {experiment.comparables.map((c) => {
                    return <li key={c.id}>{`${c.name} (${c.id})`}</li>;
                  })}
                </ul>
              </div>
            ) : (
              "No experiment found"
            )}
          </div>
        </div>
      )}
    </MainContentWrapper>
  );
}
