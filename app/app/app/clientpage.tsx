"use client";

import { useAnalytics } from "#/ui/use-analytics";
import DefaultHead from "#/ui/layout/default-head";
import Header from "#/ui/layout/header";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { TileList, TileListItem } from "#/ui/tile-list";
import { Suspense } from "react";
import { getOrgLink } from "./[org]/getOrgLink";
import { Spinner } from "#/ui/icons/spinner";
import {
  type ToplevelOrganizationInfo,
  type getToplevelOrganizationInfo,
} from "./actions";
import { pluralizeWithCount } from "#/utils/plurals";
import { useQueryFunc } from "#/utils/react-query";

export const ClientPage = ({
  orgInfos: orgInfosServer,
}: {
  orgInfos: ToplevelOrganizationInfo[] | null;
}) => {
  useAnalytics({ page: { category: "multiorg" } });

  const { data: orgInfos } = useQueryFunc<typeof getToplevelOrganizationInfo>({
    fName: "getToplevelOrganizationInfo",
    args: {},
    serverData: orgInfosServer,
  });

  return (
    <div className="flex-1">
      <DefaultHead />
      <Header />
      <MainContentWrapper contentTitle="Select organization">
        <Suspense fallback={<Spinner />}>
          <TileList>
            {(orgInfos ?? []).map(({ name, id, num_projects }) => {
              return (
                <TileListItem
                  key={id}
                  href={getOrgLink({ orgName: name })}
                  title={name}
                  subtitle={pluralizeWithCount(
                    num_projects,
                    "project",
                    "projects",
                  )}
                />
              );
            })}
          </TileList>
        </Suspense>
      </MainContentWrapper>
    </div>
  );
};
