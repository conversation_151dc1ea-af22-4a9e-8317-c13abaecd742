import { ClientPage } from "#/app/admin/processes/clientpage";
import { getOrganization } from "#/app/app/actions";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import SessionRoot from "#/ui/root";
import { cn } from "#/utils/classnames";
import { MultiTenantApiURL } from "#/utils/user-types";

export interface Params {
  org: string;
}

export default async function FailedSegmentsPage({
  params,
}: {
  params: Promise<Params>;
}) {
  const { org } = await params;
  const org_name = decodeURIComponent(org);
  const orgInfo = await getOrganization({ org_name });

  if (!orgInfo) {
    return <div>Org not found</div>;
  }

  return (
    <SessionRoot loginRequired>
      <div className="flex flex-1 flex-col">
        <MainContentWrapper
          hideFooter
          className={cn(
            "flex flex-col overflow-hidden p-0",
            HEIGHT_WITH_TOP_OFFSET,
          )}
        >
          <div className="px-5 py-3">
            <h1 className="mb-2 text-2xl font-semibold">
              Brainstore processes
            </h1>
          </div>
          <ClientPage
            apiUrl={orgInfo.api_url ?? MultiTenantApiURL}
            orgId={orgInfo.id}
          />
        </MainContentWrapper>
      </div>
    </SessionRoot>
  );
}
