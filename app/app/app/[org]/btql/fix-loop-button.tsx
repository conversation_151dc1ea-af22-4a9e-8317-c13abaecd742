import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";
import { Button } from "#/ui/button";
import { cn } from "#/utils/classnames";

export const FixLoopButton = ({
  query,
  error,
  className,
}: {
  query?: string;
  error?: string;
  className?: string;
}) => {
  const { handleSendMessage, setIsChatOpen } = useGlobalChat();

  const queryMessage = query
    ? `For the following BTQL query: \n\n${query}\n\n`
    : "";
  const errorMessage = error ? `Here is the error: \n\n${error}\n\n` : "";

  return (
    <Button
      variant="border"
      size="xs"
      className={cn("w-fit bg-background text-xs group-hover:block", className)}
      onClick={() => {
        handleSendMessage(
          {
            id: crypto.randomUUID(),
            type: "user_message",
            message: `${queryMessage}${errorMessage}Please look at the query and the error and fix the issue. Be concise and to the point.`,
          },
          {
            clearContextObjects: false,
            clearUserMessage: false,
          },
        );
        setTimeout(() => {
          setIsChatOpen(true);
        }, 250);
      }}
    >
      Fix with Loop
    </Button>
  );
};
