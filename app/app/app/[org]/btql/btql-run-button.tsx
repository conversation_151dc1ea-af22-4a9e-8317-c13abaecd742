import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useRef,
  useState,
} from "react";
import { Button } from "#/ui/button";
import { Play } from "lucide-react";
import { toast } from "sonner";
import { useSessionToken } from "#/utils/auth/session-token";
import { type BTQLResponse, fetchBtql } from "#/utils/btql/btql";
import { useOrg } from "#/utils/user";
import { useBtqlFlags } from "#/lib/feature-flags";
import { Spinner } from "#/ui/icons/spinner";

export function useBtqlQuery({
  getCurrentQuery,
  onQueryComplete,
  setSelectedRow,
}: {
  getCurrentQuery: () => string;
  onQueryComplete: (
    result: BTQLResponse<Record<string, unknown>> | null,
    error: string | null,
    lastQueryMs: number | null,
  ) => void;
  setSelectedRow: Dispatch<
    SetStateAction<{
      row: Record<string, unknown> | null;
      index?: number;
    } | null>
  >;
}) {
  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const { getOrRefreshToken } = useSessionToken();

  const [running, setRunning] = useState(false);
  const signal = useRef<AbortController>(new AbortController());

  const runQuery = useCallback(
    async (queryArg?: string) => {
      setSelectedRow(null);
      const query = queryArg ?? getCurrentQuery();
      if (query.trim() === "") {
        toast.error("Please enter a query");
        throw new Error("Please enter a query");
      }

      setRunning(true);
      const start = Date.now();
      signal.current = new AbortController();

      try {
        const result = await fetchBtql({
          args: {
            query,
            brainstoreRealtime: true, // TODO: This should be a switch
            disableLimit: false,
          },
          btqlFlags,
          apiUrl: org.api_url,
          getOrRefreshToken,
          signal: signal.current.signal,
        });
        onQueryComplete(result, null, Date.now() - start);
        return result;
      } catch (e) {
        if (signal.current.signal.aborted) {
          onQueryComplete(null, "Query aborted", Date.now() - start);
          throw new Error("Query aborted");
        }
        const errorMessage = e instanceof Error ? e.message : `${e}`;
        onQueryComplete(null, errorMessage, Date.now() - start);
        throw new Error(errorMessage);
      } finally {
        setRunning(false);
      }
    },
    [
      btqlFlags,
      getCurrentQuery,
      getOrRefreshToken,
      org.api_url,
      onQueryComplete,
      setSelectedRow,
    ],
  );

  const abortQuery = useCallback(() => {
    signal.current.abort();
  }, []);

  return {
    runQuery,
    abortQuery,
    running,
  };
}

export function BtqlRunButton({
  runQuery,
  abortQuery,
  running,
  value,
}: {
  runQuery: () => void;
  abortQuery: () => void;
  running: boolean;
  value: string;
}) {
  return (
    <Button
      variant="primary"
      size="xs"
      onClick={running ? abortQuery : () => runQuery()}
      disabled={value.trim() === ""}
    >
      {running ? (
        <>
          <Spinner className="size-3" />
          Stop
        </>
      ) : (
        <>
          <Play className="size-3" />
          Run
        </>
      )}
    </Button>
  );
}
