export const formatBTQLQuery = (query: string): string => {
  // Split by pipe and format each statement on a new line
  const statements = query.split("|").map((s) => s.trim());

  if (statements.length <= 1) {
    return query;
  }

  // First statement doesn't get a pipe prefix
  const formatted =
    statements[0] +
    "\n" +
    statements
      .slice(1)
      .map((stmt) => `| ${stmt}`)
      .join("\n");

  return formatted;
};
