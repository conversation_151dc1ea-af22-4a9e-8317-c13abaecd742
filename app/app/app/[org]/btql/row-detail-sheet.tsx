"use client";

import { useMemo, useState } from "react";
import { type Field, DataType } from "apache-arrow";
import { isEmpty, serializeJSONWithPlainString } from "#/utils/object";
import { z } from "zod";
import { DataTextEditor } from "#/ui/data-text-editor";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { Button } from "#/ui/button";
import { X } from "lucide-react";
import { stringify } from "#/utils/string";
import { DateWithTooltip } from "#/ui/date";
import { TooLarge } from "#/ui/trace/data-display";
import prettyBytes from "pretty-bytes";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";

interface RowDetailSheetProps {
  rowData: Record<string, unknown> | null;
  fields: Field[] | undefined;
  onClose: VoidFunction;
  rowIdx?: number;
}

const ONE_MB = 1024 * 1024;

export function RowDetailSheet({
  rowData,
  fields,
  onClose,
  rowIdx,
}: RowDetailSheetProps) {
  const entries = useMemo(() => {
    if (!rowData || !fields) return [];

    return fields?.map((field) => {
      return {
        key: field.name,
        value: rowData[field.name],
        field,
      };
    });
  }, [rowData, fields]);

  if (!rowData) return null;

  return (
    <div className="flex flex-col gap-3 overflow-y-auto p-3">
      <div className="flex items-center">
        <span className="flex-1 gap-2 text-sm font-medium">
          Row {rowIdx !== undefined ? rowIdx + 1 : ""}
        </span>
        <CopyToClipboardButton
          variant="ghost"
          size="xs"
          textToCopy={JSON.stringify(rowData, null, 2)}
          copyMessage="Copy row data to clipboard"
        />
        <Button size="xs" Icon={X} variant="ghost" onClick={onClose} />
      </div>
      {entries.map(({ key, value, field }) => {
        return (
          <div key={key}>
            <CollapsibleSection
              title={<div className="font-mono text-xs">{key}</div>}
              id={`field-${key}`}
              defaultCollapsed={false}
              className="mt-1 border-t border-transparent py-3"
              expandedClassName="border-t-2 border-primary-100 rounded-t-none"
            >
              <RowItem key={key} value={value} field={field} />
            </CollapsibleSection>
          </div>
        );
      })}
    </div>
  );
}

const RowItem = ({ value, field }: { value: unknown; field?: Field }) => {
  const [showLargeData, setShowLargeData] = useState(false);

  const size = useMemo(() => {
    if (value === null || value === undefined) {
      return undefined;
    }
    const stringified = stringify(value);
    const large = stringified !== undefined && stringified.length > ONE_MB;
    return large ? stringified.length : undefined;
  }, [value]);

  if (value === null || value === undefined) {
    return <span className="text-sm text-primary-400">null</span>;
  }

  if (field?.type && DataType.isTimestamp(field.type)) {
    try {
      const timestamp = z.string().safeParse(value);
      if (timestamp.success) {
        const currentDate = new Date(timestamp.data).getTime();
        return (
          <DateWithTooltip
            className="text-sm"
            dateMs={currentDate}
            withSeconds
            relativeTimeOptions={{ round: true, includeSeconds: true }}
          />
        );
      }
    } catch {}
  }

  return !isEmpty(value) ? (
    size && !showLargeData ? (
      <TooLarge
        data={serializeJSONWithPlainString(value)}
        fieldName={field?.name}
        setShowLargeData={setShowLargeData}
      >
        Data is too large to display ({prettyBytes(size)})
      </TooLarge>
    ) : (
      <DataTextEditor value={value} readOnly className="text-sm" />
    )
  ) : (
    <span className="text-sm text-primary-400 italic">null</span>
  );
};
