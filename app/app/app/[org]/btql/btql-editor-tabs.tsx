"use client";

import { type Dispatch, type SetStateAction, useState } from "react";
import { type BTQLResponse } from "#/utils/btql/btql";
import { cn } from "#/utils/classnames";
import { TabsList, TabsTrigger } from "@radix-ui/react-tabs";
import { Button, buttonVariants } from "#/ui/button";
import { Plus, X } from "lucide-react";
import { newId } from "#/utils/btapi/btapi";
import { Input } from "#/ui/input";
import { BasicTooltip } from "#/ui/tooltip";
import { produce } from "immer";

export interface BtqlTab {
  id: string;
  name: string;
  query: string;
}

export interface BtqlTabResult {
  id: string;
  result?: BTQLResponse<Record<string, unknown>> | null;
  error?: string | null;
  lastQueryMs?: number | null;
}

export function BTQLEditorTabs({
  activeTabId,
  onChangeActiveTab,
  tabs,
  setTabs,
  setTabResults,
}: {
  activeTabId: string;
  onChangeActiveTab: (tabId: string) => void;
  tabs: BtqlTab[];
  setTabs: Dispatch<SetStateAction<BtqlTab[]>>;
  setTabResults: Dispatch<SetStateAction<BtqlTabResult[]>>;
}) {
  const [renamingTabId, setRenamingTabId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState("");

  const createNewTab = () => {
    const newTabId = newId();
    const newTab: BtqlTab = {
      id: newTabId,
      name: `Query ${tabs.length + 1}`,
      query: "",
    };
    setTabs((prevTabs) =>
      produce(prevTabs, (draft) => {
        draft.push(newTab);
      }),
    );
    setTabResults((prevTabResults) =>
      produce(prevTabResults, (draft) => {
        draft.push({ id: newTabId });
      }),
    );
    onChangeActiveTab(newTabId);
    return newTabId;
  };

  const closeTab = (tabId: string) => {
    if (tabs.length <= 1) return;

    setTabs((prevTabs) =>
      produce(prevTabs, (draft) => {
        const currentIndex = draft.findIndex((tab) => tab.id === tabId);

        // If we're closing the active tab, switch to another tab
        if (tabId === activeTabId) {
          const filteredTabs = draft.filter((tab) => tab.id !== tabId);
          const newActiveTab = filteredTabs[Math.max(0, currentIndex - 1)];
          onChangeActiveTab(newActiveTab.id);
        }

        // Remove the tab
        const tabIndex = draft.findIndex((tab) => tab.id === tabId);
        if (tabIndex !== -1) {
          draft.splice(tabIndex, 1);
        }
      }),
    );

    setTabResults((prevTabResults) =>
      produce(prevTabResults, (draft) => {
        const tabIndex = draft.findIndex((tab) => tab.id === tabId);
        if (tabIndex !== -1) {
          draft.splice(tabIndex, 1);
        }
      }),
    );
  };

  const renameTab = (tabId: string, newName: string) => {
    setTabs((prevTabs) =>
      produce(prevTabs, (draft) => {
        const tabIndex = draft.findIndex((tab) => tab.id === tabId);
        if (tabIndex !== -1) {
          draft[tabIndex].name = newName;
        }
      }),
    );
  };

  const handleRenameClick = (tabId: string, currentName: string) => {
    setRenamingTabId(tabId);
    setRenameValue(currentName);
  };

  const handleRenameSubmit = () => {
    const trimmedValue = renameValue.trim();
    setRenamingTabId(null);
    setRenameValue("");
    if (!trimmedValue) {
      return;
    }
    if (renamingTabId) {
      renameTab(renamingTabId, trimmedValue);
    }
  };

  const handleRenameCancel = () => {
    setRenamingTabId(null);
    setRenameValue("");
  };

  return (
    <div className="relative flex max-w-full min-w-0 flex-1 items-end justify-start gap-1 bg-primary-50">
      <TabsList className="flex h-8 min-w-0 gap-1 rounded-tl-md border-primary-200/80 bg-transparent p-0">
        {tabs.map((tab, index) => (
          <div
            key={tab.id}
            className={cn(
              "relative flex w-40 min-w-0 flex-1 basis-0 pb-1",
              tab.id === activeTabId && "pb-0",
            )}
          >
            <BasicTooltip tooltipContent={tab.name}>
              <TabsTrigger value={tab.id} asChild>
                <Button
                  size="xs"
                  variant={tab.id === activeTabId ? "default" : "ghost"}
                  className={cn(
                    "group relative h-full w-full min-w-0 justify-start px-3 hover:bg-primary-100",
                    tab.id === activeTabId &&
                      "rounded-b-none border-x border-t border-b-0 bg-background pb-0.5 hover:bg-background",
                    index === 0 && "rounded-tl-md",
                  )}
                  onClick={() => {
                    onChangeActiveTab(tab.id);
                  }}
                >
                  {renamingTabId === tab.id ? (
                    <Input
                      type="text"
                      value={renameValue}
                      onChange={(e) => setRenameValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          handleRenameSubmit();
                        } else if (e.key === "Escape") {
                          e.preventDefault();
                          handleRenameCancel();
                        }
                      }}
                      onBlur={handleRenameSubmit}
                      className={cn(
                        "h-auto min-w-0 border-0 bg-transparent p-0 text-left text-xs focus:border-0 focus:ring-0 focus:outline-hidden",
                        tab.id === activeTabId &&
                          "-translate-x-px -translate-y-[2px]",
                      )}
                      autoFocus
                      onFocus={(e) => e.target.select()}
                    />
                  ) : (
                    <span
                      className={cn(
                        "min-w-0 truncate text-left",
                        tab.id === activeTabId &&
                          "-translate-x-px -translate-y-[2px]",
                      )}
                      onDoubleClick={(e) => {
                        e.stopPropagation();
                        handleRenameClick(tab.id, tab.name);
                      }}
                    >
                      {tab.name}
                    </span>
                  )}
                  {tabs.length > 1 && (
                    <div
                      className={cn(
                        buttonVariants({
                          size: "xs",
                          variant: "ghost",
                        }),
                        "absolute top-1/2 right-1 size-5 min-w-5 shrink-0 -translate-y-1/2 rounded-full px-0 opacity-0 group-hover:opacity-100 hover:bg-primary-200",
                        activeTabId === tab.id &&
                          "translate-x-px -translate-y-[calc(50%+3px)] bg-background",
                        activeTabId !== tab.id && "bg-primary-100",
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        closeTab(tab.id);
                      }}
                    >
                      <X className="size-3 text-primary-500" />
                    </div>
                  )}
                </Button>
              </TabsTrigger>
            </BasicTooltip>
            <div
              className={cn(
                "my-auto ml-[1.5px] h-4 w-px shrink-0 bg-primary-200/80",
                (tab.id === activeTabId ||
                  tabs[index + 1]?.id === activeTabId) &&
                  "hidden",
              )}
            />
            <div
              className={cn(
                tab.id === activeTabId &&
                  "absolute inset-x-px -bottom-px z-10 h-px bg-background",
              )}
            />
          </div>
        ))}
      </TabsList>
      <BasicTooltip tooltipContent="Add new tab">
        <Button
          size="xs"
          variant="ghost"
          onClick={createNewTab}
          className={cn(
            "mb-1 border-primary-200/80 text-primary-500 hover:bg-primary-200/80",
          )}
          aria-label="Add new tab"
          Icon={Plus}
        />
      </BasicTooltip>
    </div>
  );
}
