import { useQueryFunc } from "#/utils/react-query";
import { useOrg, useUser } from "#/utils/user";
import { useContext, useMemo } from "react";
import { type ProjectSummary, type getProjectSummary } from "../org-actions";
import {
  type getOrgDatasets,
  type getOrgExperiments,
  type getOrgPromptSessions,
} from "../p/[project]/library/library-actions";
import { type SearchableItemInfo } from "#/utils/codemirror/btql-lang";
import { ProjectContext } from "../p/[project]/projectContext";

export function useBtqlAutocompleteDataSources({
  projectSummaryServer,
}: {
  projectSummaryServer?: ProjectSummary[];
}): {
  dataSources: {
    projects: SearchableItemInfo[];
    datasets: SearchableItemInfo[];
    experiments: SearchableItemInfo[];
    promptSessions: SearchableItemInfo[];
    orgs: SearchableItemInfo[];
  };
  projectsPending: boolean;
} {
  const { name: orgName } = useOrg();
  const { data: projects, isPending: projectsPending } = useQueryFunc<
    typeof getProjectSummary
  >({
    fName: "getProjectSummary",
    args: { org_name: orgName },
    serverData: projectSummaryServer ?? [],
  });
  const { data: datasets } = useQueryFunc<typeof getOrgDatasets>({
    fName: "getOrgDatasets",
    args: { org_name: orgName },
  });
  const { data: experiments } = useQueryFunc<typeof getOrgExperiments>({
    fName: "getOrgExperiments",
    args: { org_name: orgName },
  });
  const { data: promptSessions } = useQueryFunc<typeof getOrgPromptSessions>({
    fName: "getOrgPromptSessions",
    args: { org_name: orgName },
  });
  const { orgs } = useUser();

  const dataSources = useMemo(
    () => ({
      projects:
        projects?.map((p) => ({
          id: p.project_id,
          name: p.project_name,
          type: "project" as const,
        })) ?? [],
      datasets:
        datasets?.map((d) => ({
          id: d.id,
          name: d.name,
          type: "dataset" as const,
          projectId: d.project_id,
          projectName: d.project_name,
        })) ?? [],
      experiments:
        experiments?.map((e) => ({
          id: e.id,
          name: e.name,
          type: "experiment" as const,
          projectId: e.project_id,
          projectName: e.project_name,
        })) ?? [],
      promptSessions:
        promptSessions?.map((ps) => ({
          id: ps.id,
          name: ps.name,
          type: "playground_session" as const,
          projectId: ps.project_id,
          projectName: ps.project_name,
        })) ?? [],
      orgs: Object.entries(orgs ?? {}).map(([orgId, org]) => ({
        id: orgId,
        name: org.name,
        type: "org" as const,
      })),
    }),
    [projects, datasets, experiments, promptSessions, orgs],
  );

  return {
    dataSources,
    projectsPending,
  };
}

export function useBtqlAutocompleteProjectDataSource() {
  const { name: orgName } = useOrg();
  const project = useContext(ProjectContext);
  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  return useMemo(() => {
    const projectSummary = projects?.find(
      (p) => p.project_id === project?.projectId,
    );
    return {
      projects: projectSummary
        ? [
            {
              id: projectSummary.project_id,
              name: projectSummary.project_name,
              type: "project" as const,
            },
          ]
        : [],
      datasets: [],
      experiments: [],
      promptSessions: [],
      orgs: [],
    };
  }, [project?.projectId, projects]);
}
