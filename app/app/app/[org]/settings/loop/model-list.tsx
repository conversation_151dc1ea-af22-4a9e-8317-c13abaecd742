import { CollapsibleSection } from "#/ui/collapsible-section";
import {
  PRIORITIZED_DEFAULT_MODELS,
  isModelMatch,
} from "#/ui/optimization/model-utils";
import { Checkbox } from "#/ui/checkbox";
import { Label } from "#/ui/label";
import { Button } from "#/ui/button";
import {
  getProviderConfig,
  providerReadableName,
} from "#/app/app/[org]/settings/secrets/utils";
import { type LoopAllowedModels } from "#/app/app/[org]/settings/loop/actions";
import { useMemo, useState, memo, useCallback, useEffect } from "react";
import { type ModelDetails } from "#/ui/prompts/models";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { type ModelItem } from "./loop-model-whitelist";
import { Input } from "#/ui/input";
import { getModelIcon } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Ellipsis } from "lucide-react";
import { makeKey, parseKey } from "./whitelist-edit-dialog";
import Link from "next/link";

const ModelList = ({
  modelOptionsByProvider,
  loopAllowedModels,
  onSave,
  disabled,
  setResetOpen,
  setAddModelOpen,
  orgName,
}: {
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  loopAllowedModels: ModelItem[];
  onSave: (newWhitelist: LoopAllowedModels) => void | Promise<void>;
  disabled: boolean;
  setResetOpen: (open: boolean) => void;
  setAddModelOpen: (open: boolean) => void;
  orgName: string;
}) => {
  const [draftSelected, setDraftSelected] = useState<Set<string>>(new Set());
  const [search, setSearch] = useState("");
  const [saving, setSaving] = useState(false);

  const initialSelected = useMemo(() => {
    return new Set(
      loopAllowedModels.map((m) => makeKey(m.provider, m.modelName)),
    );
  }, [loopAllowedModels]);

  const otherModels = useMemo(() => {
    return loopAllowedModels.filter((model) => {
      return !PRIORITIZED_DEFAULT_MODELS.some((recommendedModel) =>
        isModelMatch(model.modelName, recommendedModel, model.provider),
      );
    });
  }, [loopAllowedModels]);

  useEffect(() => {
    setDraftSelected(initialSelected);
  }, [initialSelected]);

  const toggleModel = useCallback((provider: string, modelName: string) => {
    setDraftSelected((prev) => {
      const next = new Set(prev);
      const key = makeKey(provider, modelName);
      if (next.has(key)) {
        next.delete(key);
      } else {
        next.add(key);
      }
      return next;
    });
  }, []);

  const modelProviderMappings = useMemo(() => {
    return PRIORITIZED_DEFAULT_MODELS.map((model) => {
      const providersWithModel = Object.entries(modelOptionsByProvider)
        .flatMap(([provider, models]) => {
          const matchingModels = models.filter((m) =>
            isModelMatch(m.modelName, model, provider),
          );
          return matchingModels.map((modelDetails) => ({
            provider,
            modelDetails,
          }));
        })
        .filter((item) => item.modelDetails);

      return {
        model,
        providersWithModel,
      };
    });
  }, [modelOptionsByProvider]);

  const handleSave = async () => {
    setSaving(true);
    try {
      const draftEqualsInitial =
        draftSelected.size === initialSelected.size &&
        [...draftSelected].every((n) => initialSelected.has(n));

      if (!draftEqualsInitial) {
        const newValue = Array.from(draftSelected).map((key) => {
          return parseKey(key);
        });
        await onSave(newValue);
      }
    } finally {
      setSaving(false);
    }
  };

  const addedModels = [...draftSelected].filter(
    (name) => !initialSelected.has(name),
  );
  const removedModels = [...initialSelected].filter(
    (name) => !draftSelected.has(name),
  );

  const changesByProvider = useMemo(() => {
    type ChangeItem = { details: ModelDetails; change: "added" | "removed" };
    const grouped: Record<string, ChangeItem[]> = {};
    for (const key of addedModels) {
      const { provider, modelName } = parseKey(key);
      const details = modelOptionsByProvider[provider]?.find(
        (m) => m.modelName === modelName,
      );
      if (details) {
        (grouped[provider] ??= []).push({ details, change: "added" });
      }
    }
    for (const key of removedModels) {
      const { provider, modelName } = parseKey(key);
      const details = modelOptionsByProvider[provider]?.find(
        (m) => m.modelName === modelName,
      );
      if (details) {
        (grouped[provider] ??= []).push({ details, change: "removed" });
      }
    }
    return grouped;
  }, [addedModels, removedModels, modelOptionsByProvider]);

  return (
    <div>
      {addedModels.length > 0 || removedModels.length > 0 ? (
        <div className="flex items-center gap-2 py-0.5">
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <Button
                variant="primary"
                size="xs"
                onClick={() => setDraftSelected(initialSelected)}
              >
                <div className="mr-1 flex items-center gap-1">
                  {addedModels.length > 0 && (
                    <span className="text-xs text-primary-50 dark:text-primary-900">
                      +{addedModels.length}
                    </span>
                  )}
                  {removedModels.length > 0 && (
                    <span className="text-xs text-primary-50 dark:text-primary-900">
                      -{removedModels.length}
                    </span>
                  )}
                </div>
                Clear
              </Button>
            </TooltipTrigger>
            <TooltipContent className="flex max-w-[360px] min-w-[240px] flex-col gap-1 p-2">
              <span className="text-xs font-medium text-primary-700">
                Change overview
              </span>
              {(addedModels.length > 0 || removedModels.length > 0) && (
                <div className="mt-1 flex flex-col gap-2 text-xs">
                  {Object.entries(changesByProvider).map(
                    ([provider, items]) => {
                      const { Icon: ProviderIcon } =
                        getProviderConfig(provider);
                      return (
                        <div
                          key={"changes-" + provider}
                          className="flex flex-col gap-2"
                        >
                          <div className="flex items-center gap-1">
                            <ProviderIcon size={12} />
                            <span className="font-medium">
                              {providerReadableName(provider)}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {items.map(({ details, change }) => (
                              <span
                                key={change + "-" + details.modelName}
                                className={
                                  change === "added"
                                    ? "ml-1 w-fit rounded border border-good-200 bg-good-50 px-1 py-px text-good-700"
                                    : "ml-1 w-fit rounded border border-bad-200 bg-bad-50 px-1 py-px text-bad-700 line-through"
                                }
                              >
                                {details.displayName ?? details.modelName}
                              </span>
                            ))}
                          </div>
                        </div>
                      );
                    },
                  )}
                </div>
              )}
            </TooltipContent>
          </Tooltip>
          <Button
            variant="border"
            size="xs"
            onClick={handleSave}
            disabled={
              saving || (addedModels.length === 0 && removedModels.length === 0)
            }
          >
            Save changes
          </Button>
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search"
            className="h-8"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="border" size="sm" Icon={Ellipsis} />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setResetOpen(true)}>
                Reset to default recommendations
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setAddModelOpen(true)}>
                Customize allowlist
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
      <div className="mt-4 flex flex-col gap-4">
        <div className="flex flex-col">
          <div className="text-sm font-medium">Recommended models</div>
          <span className="text-xs text-gray-500">
            Models that have been evaluated and found to be compatible with Loop
          </span>
        </div>
        {modelProviderMappings.map(({ model, providersWithModel }) => (
          <ModelCheckboxItem
            key={model}
            model={model}
            providersWithModel={providersWithModel}
            draftSelected={draftSelected}
            toggleModel={toggleModel}
            search={search}
            disabled={disabled}
            orgName={orgName}
          />
        ))}
      </div>
      {otherModels.length > 0 && (
        <div className="mt-4 flex flex-col gap-1">
          <CollapsibleSection
            title="Other models"
            defaultCollapsed={true}
            className="h-8 text-sm"
          >
            {otherModels.map((model) => {
              const ModelIcon = getModelIcon(model.modelName);
              return (
                <Label
                  key={model.modelName}
                  className="flex cursor-pointer items-center gap-2 rounded"
                >
                  <Checkbox
                    checked={draftSelected.has(
                      makeKey(model.provider, model.modelName),
                    )}
                    disabled={disabled}
                    onChange={() =>
                      toggleModel(model.provider, model.modelName)
                    }
                  />
                  <div className="flex items-center gap-2">
                    <ModelIcon size={16} />
                    <span className="text-sm">
                      {model.displayName || model.modelName}
                    </span>
                  </div>
                </Label>
              );
            })}
          </CollapsibleSection>
        </div>
      )}
    </div>
  );
};

export default ModelList;

const ModelCheckboxItem = memo(
  ({
    model,
    providersWithModel,
    draftSelected,
    toggleModel,
    search,
    disabled,
    orgName,
  }: {
    model: string;
    providersWithModel: Array<{ provider: string; modelDetails: ModelDetails }>;
    draftSelected: Set<string>;
    toggleModel: (provider: string, modelName: string) => void;
    search: string;
    disabled: boolean;
    orgName: string;
  }) => {
    const searchLower = search.toLowerCase();
    const modelMatches = model.toLowerCase().includes(searchLower);

    const filteredProviders = useMemo(
      () =>
        providersWithModel.filter(({ provider }) =>
          providerReadableName(provider).toLowerCase().includes(searchLower),
        ),
      [providersWithModel, searchLower],
    );

    // Early return if no matches
    if (!modelMatches && filteredProviders.length === 0 && search) {
      return null;
    }

    const providersToShow =
      search && modelMatches
        ? providersWithModel
        : search
          ? filteredProviders
          : providersWithModel;

    const modelDisplayName =
      providersWithModel[0]?.modelDetails?.displayName || model;
    const ModelIcon = getModelIcon(model);

    return (
      <div>
        <div className="mb-1 flex items-center gap-1 text-sm font-medium">
          <ModelIcon size={16} />
          {modelDisplayName}
        </div>
        {providersWithModel.length > 0 ? (
          <div className="flex flex-col gap-1">
            {providersToShow.map(({ provider, modelDetails }) => {
              const key = makeKey(provider, modelDetails.modelName);
              const isChecked = draftSelected.has(key);
              const { Icon: ProviderIcon } = getProviderConfig(provider);

              return (
                <Label
                  key={key}
                  className="flex cursor-pointer items-center gap-2 rounded"
                >
                  <Checkbox
                    checked={isChecked}
                    disabled={disabled}
                    onChange={() =>
                      toggleModel(provider, modelDetails.modelName)
                    }
                  />
                  <ProviderIcon size={14} />
                  <span className="text-sm">
                    {providerReadableName(provider)}
                  </span>
                  {modelDetails?.displayName &&
                    modelDetails.displayName !== modelDetails.modelName && (
                      <span className="ml-auto text-xs text-gray-500">
                        {modelDetails.displayName || modelDetails.modelName}
                      </span>
                    )}
                </Label>
              );
            })}
          </div>
        ) : (
          <div className="text-sm text-gray-500">
            <Link
              href={`/app/${encodeURIComponent(orgName)}/settings/secrets`}
              className="text-accent-600"
            >
              Add provider
            </Link>{" "}
            to enable this model
          </div>
        )}
      </div>
    );
  },
);

ModelCheckboxItem.displayName = "ModelCheckboxItem";
