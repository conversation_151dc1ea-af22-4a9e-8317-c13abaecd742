"use server";

import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import { z } from "zod";

// Zod schemas and types for loop_allowed_models
const ModelProviderPairSchema = z.object({
  modelName: z.string(),
  provider: z.string(),
});

export type ModelProviderPair = z.infer<typeof ModelProviderPairSchema>;

const LoopAllowedModelsSchema = z.preprocess(
  (value) => {
    if (value === undefined || value === null) return null;
    if (
      typeof value === "object" &&
      value !== null &&
      !Array.isArray(value) &&
      Object.keys(value).length === 0
    )
      return null;
    if (Array.isArray(value)) {
      if (value.length === 0) return [];
      const normalizedPairs = value
        .filter(
          (v) =>
            v &&
            typeof v === "object" &&
            typeof v.modelName === "string" &&
            typeof v.provider === "string",
        )
        .map((v) => ({
          modelName: v.modelName,
          provider: v.provider,
        }));
      if (normalizedPairs.length === value.length) return normalizedPairs;
    }
    return null;
  },
  z.union([z.null(), z.array(ModelProviderPairSchema)]),
);

export type LoopAllowedModels = z.infer<typeof LoopAllowedModelsSchema>;

// RBAC_DISCLAIMER: Any org member can read the loop model whitelist.
export async function fetchLoopModelWhitelist({
  orgId,
}: {
  orgId?: string;
}): Promise<LoopAllowedModels> {
  if (!orgId) {
    return null;
  }

  const authLookup = await getServerSessionAuthLookup();

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
    select loop_allowed_models
    from org_settings
    where
        org_id = $1
        and exists (
            select 1
            from
                organizations
                join members on organizations.id = members.org_id
                join users on members.user_id = users.id
            where
                organizations.id = $1 and users.auth_id = $2
        )
    `,
    [orgId, authLookup.auth_id],
  );

  const result = rows.length ? rows[0].loop_allowed_models : null;
  return LoopAllowedModelsSchema.parse(result);
}

export async function upsertLoopModelWhitelist({
  orgId,
  loopAllowedModels,
}: {
  orgId?: string;
  loopAllowedModels: LoopAllowedModels;
}): Promise<LoopAllowedModels> {
  if (!orgId) {
    throw new Error("Missing input");
  }
  // Use the input directly since we'll validate the output from the database
  const validatedModels = loopAllowedModels;

  const authLookup = await getServerSessionAuthLookup();

  const {
    query: orgQuery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "update",
    },
    filters: {
      org_id: orgId,
    },
  });

  // Ensure proper JSON serialization for PostgreSQL
  const jsonValue =
    validatedModels === null || validatedModels === undefined
      ? null
      : JSON.stringify(validatedModels);

  const query = `
    with rows_to_insert as (
        select organizations.id org_id, ${queryParams.add(
          jsonValue,
        )}::jsonb loop_allowed_models
        from (${orgQuery}) organizations
        limit 1
    )
    insert into org_settings(org_id, loop_allowed_models)
    select * from rows_to_insert
    on conflict (org_id) do update set loop_allowed_models = EXCLUDED.loop_allowed_models
    returning loop_allowed_models
  `;

  const supabase = getServiceRoleSupabase();
  const row = extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage,
  });

  const result = row.loop_allowed_models;
  return LoopAllowedModelsSchema.parse(result);
}
