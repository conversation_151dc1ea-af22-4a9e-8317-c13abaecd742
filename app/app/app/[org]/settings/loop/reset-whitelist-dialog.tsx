import { But<PERSON> } from "#/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, <PERSON><PERSON><PERSON><PERSON>er, DialogContent } from "#/ui/dialog";
import { getProviderConfig } from "#/app/app/[org]/settings/secrets/utils";
import { type ModelItem } from "./loop-model-whitelist";
import { useState } from "react";

export const ResetWhitelistDialog = ({
  resetOpen,
  setResetOpen,
  onReset,
  defaultWhitelistedModels,
}: {
  resetOpen: boolean;
  setResetOpen: (open: boolean) => void;
  onReset: () => void;
  defaultWhitelistedModels: ModelItem[];
}) => {
  const [resetting, setResetting] = useState(false);
  const handleReset = async () => {
    try {
      setResetting(true);
      await onReset();
    } finally {
      setResetting(false);
      setResetOpen(false);
    }
  };

  return (
    <Dialog open={resetOpen} onOpenChange={setResetOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reset allowlist to default recommendations</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-2">
          <p className="text-sm text-primary-700">
            Resetting the model allowlist will set the allowlist to a list of
            default recommended models:
          </p>
          <ul className="flex max-h-[500px] flex-col gap-1 overflow-y-auto">
            {defaultWhitelistedModels.map((model) => {
              const { Icon: ProviderIcon } = getProviderConfig(model.provider);
              return (
                <li
                  key={model.modelName + model.provider}
                  className="flex items-center gap-2 text-sm"
                >
                  <ProviderIcon size={16} />{" "}
                  {model.displayName || model.modelName}
                </li>
              );
            })}
          </ul>
        </div>
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="border"
            size="sm"
            onClick={() => setResetOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={handleReset}
            disabled={resetting}
            isLoading={resetting}
          >
            Confirm
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
