"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { type ModelDetails } from "#/ui/prompts/models";
import { useState, useMemo, useCallback } from "react";
import {
  type upsertLoopModelWhitelist,
  type LoopAllowedModels,
} from "#/app/app/[org]/settings/loop/actions";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { getDefaultWhitelistedModels } from "#/ui/optimization/model-utils";
import WhitelistEditDialog from "#/app/app/[org]/settings/loop/whitelist-edit-dialog";
import { Ellipsis } from "lucide-react";
import {
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenu,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ResetWhitelistDialog } from "./reset-whitelist-dialog";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { InfoBanner } from "#/ui/info-banner";
import ModelList from "./model-list";

export type ModelItem = ModelDetails & { provider: string };

interface LoopModelWhitelistProps {
  orgName: string;
  orgId: string;
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  disabled?: boolean;
  fetchedLoopAllowedModels: LoopAllowedModels;
  invalidateWhitelist: () => void;
}

export default function LoopModelWhitelist({
  fetchedLoopAllowedModels,
  orgId,
  modelOptionsByProvider,
  orgName,
  disabled,
  invalidateWhitelist,
}: LoopModelWhitelistProps) {
  const { getToken } = useAuth();

  const [addModelOpen, setAddModelOpen] = useState(false);
  const [resetOpen, setResetOpen] = useState(false);
  const availableModels = useMemo(
    () =>
      Object.entries(modelOptionsByProvider).flatMap(([provider, models]) =>
        models.map((model) => ({ ...model, provider })),
      ),
    [modelOptionsByProvider],
  );

  const defaultWhitelistedModels = useMemo(
    () => getDefaultWhitelistedModels(availableModels),
    [availableModels],
  );

  const enrichedLoopAllowedModels = useMemo<ModelItem[] | null>(() => {
    if (fetchedLoopAllowedModels === null) return null;
    if (Array.isArray(fetchedLoopAllowedModels)) {
      if (fetchedLoopAllowedModels.length === 0) return [];
      const enrichedModels: ModelItem[] = [];
      for (const model of fetchedLoopAllowedModels) {
        const providerModels = modelOptionsByProvider[model.provider];
        if (providerModels) {
          const fullModelDetails = providerModels.find(
            (m) => m.modelName === model.modelName,
          );
          if (fullModelDetails) {
            enrichedModels.push({
              ...fullModelDetails,
              provider: model.provider,
            });
          }
        }
      }
      return enrichedModels;
    }
    return null;
  }, [fetchedLoopAllowedModels, modelOptionsByProvider]);

  const updateWhitelist = useCallback(
    async (newWhitelist: LoopAllowedModels) => {
      try {
        await invokeServerAction<typeof upsertLoopModelWhitelist>({
          fName: "upsertLoopModelWhitelist",
          args: {
            orgId,
            loopAllowedModels: newWhitelist,
          },
          getToken,
        });

        invalidateWhitelist();
      } catch (error) {
        toast.error("Failed to update model whitelist", {
          description: `${error}`,
        });
      }
    },
    [orgId, getToken, invalidateWhitelist],
  );

  const effectiveWhitelistedModels =
    enrichedLoopAllowedModels === null
      ? defaultWhitelistedModels
      : enrichedLoopAllowedModels && enrichedLoopAllowedModels.length > 0
        ? enrichedLoopAllowedModels
        : [];

  if (effectiveWhitelistedModels.length === 0) {
    return (
      <>
        {disabled && (
          <InfoBanner className="mb-4">
            You do not have the permissions to edit the model allowlist for
            Loop. To edit the allowlist, please contact your administrator.
          </InfoBanner>
        )}
        <TableEmptyState
          className="flex flex-col rounded-md border p-4"
          label="No models are allowlisted for Loop in this organization. The default recommended models will be used."
        >
          <div className="flex shrink-0 items-center rounded-md border">
            <Button
              variant="ghost"
              size="sm"
              disabled={disabled}
              className="rounded-r-none"
              onClick={() => setResetOpen(true)}
            >
              Add recommended models
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={disabled}
                  className="shrink-0 rounded-l-none border-l"
                  Icon={Ellipsis}
                />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setAddModelOpen(true)}>
                  Customize allowlist
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableEmptyState>
        <WhitelistEditDialog
          modelOptionsByProvider={modelOptionsByProvider}
          loopAllowedModels={effectiveWhitelistedModels}
          addModelOpen={addModelOpen}
          setAddModelOpen={setAddModelOpen}
          onSave={updateWhitelist}
        />
        <ResetWhitelistDialog
          resetOpen={resetOpen}
          setResetOpen={setResetOpen}
          onReset={() =>
            updateWhitelist(
              defaultWhitelistedModels.map((m) => ({
                modelName: m.modelName,
                provider: m.provider,
              })),
            )
          }
          defaultWhitelistedModels={defaultWhitelistedModels}
        />
      </>
    );
  }

  return (
    <div className="px-1">
      {disabled && (
        <InfoBanner className="mb-4">
          You do not have the permissions to edit the model allowlist for Loop.
          To edit the allowlist, please contact your administrator.
        </InfoBanner>
      )}
      <div className="flex flex-col gap-4">
        <ModelList
          modelOptionsByProvider={modelOptionsByProvider}
          loopAllowedModels={effectiveWhitelistedModels}
          onSave={updateWhitelist}
          setResetOpen={setResetOpen}
          setAddModelOpen={setAddModelOpen}
          disabled={disabled ?? false}
          orgName={orgName}
        />

        <WhitelistEditDialog
          modelOptionsByProvider={modelOptionsByProvider}
          loopAllowedModels={effectiveWhitelistedModels}
          addModelOpen={addModelOpen}
          setAddModelOpen={setAddModelOpen}
          onSave={updateWhitelist}
        />
        <ResetWhitelistDialog
          resetOpen={resetOpen}
          setResetOpen={setResetOpen}
          onReset={() =>
            updateWhitelist(
              defaultWhitelistedModels.map((m) => ({
                modelName: m.modelName,
                provider: m.provider,
              })),
            )
          }
          defaultWhitelistedModels={defaultWhitelistedModels}
        />
      </div>
    </div>
  );
}
