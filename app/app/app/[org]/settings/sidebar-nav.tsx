"use client";

import Link from "next/link";
import { cn } from "#/utils/classnames";
import { usePathname } from "next/navigation";
import { buttonVariants } from "#/ui/button";
import {
  ChevronsLeftRight,
  CreditCard,
  KeySquare,
  Layers,
  Server,
  LockIcon,
  type LucideIcon,
  ScrollText,
  ShieldCheck,
  Sparkle,
  SquareUserRound,
  ToggleRight,
  UsersRound,
  Blend,
} from "lucide-react";
import { useOrg } from "#/utils/user";
import { useIsFeatureEnabled } from "#/lib/feature-flags";

import { getOrgSettingsLink } from "#/app/app/[org]/getOrgLink";

export const personalSettingsItems = [
  {
    title: "Profile",
    href: "user",
    Icon: SquareUserRound,
  },
  // Hide 'Copilot' for now. It causes out of memory errors in large projects and we have not prioritized fixing it yet.
  // {
  //   title: "Copilot",
  //   href: "copilot",
  //   Icon: TextCursor,
  // },
  {
    title: "Feature flags",
    href: "feature-flags",
    Icon: ToggleRight,
  },
  {
    title: "Security",
    href: "security",
    Icon: LockIcon,
  },
];
export const orgSettingsItems = [
  {
    title: "Members",
    href: "team",
    Icon: UsersRound,
  },
  {
    title: "Permission groups",
    href: "groups",
    Icon: ShieldCheck,
  },
  {
    title: "API keys",
    href: "api-keys",
    Icon: KeySquare,
  },
  {
    title: "Service tokens",
    href: "service-tokens",
    Icon: Server,
  },
  {
    title: "AI providers",
    href: "secrets",
    Icon: Sparkle,
  },
  {
    title: "Loop",
    href: "loop",
    Icon: Blend,
  },
  {
    title: "Env variables",
    href: "env-vars",
    Icon: ChevronsLeftRight,
  },
  {
    title: "Environments",
    href: "environments",
    Icon: Layers,
  },
  {
    title: "Data plane",
    href: "api-url",
    Icon: LockIcon,
  },
  {
    title: "Logging",
    href: "logging",
    Icon: ScrollText,
  },
  {
    title: "Billing",
    href: "billing",
    Icon: CreditCard,
  },
];

export const SidebarNav = ({ className }: { className?: string }) => {
  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");
  const isLoopEnabled = useIsFeatureEnabled("loop");
  const filteredOrgItems = orgSettingsItems.filter((item) => {
    if (item.href === "environments") {
      return isEnvironmentsEnabled;
    }
    if (item.href === "loop") {
      return isLoopEnabled;
    }
    return true;
  });

  return (
    <nav
      className={cn(
        "-mx-4 no-scrollbar flex gap-x-2 overflow-x-auto border-b px-3 pb-2 lg:mx-0 lg:flex-col lg:gap-0 lg:gap-y-1 lg:overflow-x-visible lg:border-0 lg:p-0",
        className,
      )}
    >
      <div className="mb-2 hidden text-xs text-primary-600 lg:block">
        Organization
      </div>
      {filteredOrgItems.map((item) => (
        <SidebarNavLink key={item.href} {...item} />
      ))}
      <div className="mb-2 hidden pt-4 text-xs text-primary-600 lg:block">
        Personal
      </div>
      {personalSettingsItems.map((item) => (
        <SidebarNavLink key={item.href} {...item} />
      ))}
    </nav>
  );
};

const SidebarNavLink = ({
  href,
  title,
  Icon,
}: {
  href: string;
  title: string;
  Icon: LucideIcon;
}) => {
  const org = useOrg();
  const settingsLink = getOrgSettingsLink({ orgName: org.name });
  const pathname = usePathname();
  const pathPart = pathname?.split("/").pop();

  return (
    <Link
      key={href}
      href={`${settingsLink}/${href}`}
      className={cn(
        buttonVariants({
          size: "xs",
          variant: pathPart === href ? "default" : "ghost",
        }),
        "justify-start gap-2.5 pl-2 text-[13px]",
      )}
    >
      <Icon className="size-3.5 flex-none" />
      {title}
    </Link>
  );
};
