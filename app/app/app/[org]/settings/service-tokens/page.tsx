import { getOrganization, isOrganizationOwner } from "#/app/app/actions";
import { decodeURIComponentPatched } from "#/utils/url";
import ClientPage from "./clientpage";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { Alert } from "#/ui/alert";
import { isAllowedSysadmin } from "#/utils/derive-error-context";

export default async function Page(props: {
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const authLookup = await getServerSessionAuthLookup();
  const isSysadmin = await isAllowedSysadmin(authLookup);
  const org = await getOrganization({ org_name }, authLookup);

  if (!org) {
    return (
      <Alert variant="destructive">
        Service tokens not found. Please reload the page to try again.
      </Alert>
    );
  }

  const isOrgOwner = await isOrganizationOwner({ org_id: org.id }, authLookup);

  return (
    <ClientPage org={org} isOrgOwner={isOrgOwner} isSysadmin={isSysadmin} />
  );
}
