import { type addMembers } from "#/pages/api/organization/member_actions";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { type User, type Organization } from "@braintrust/typespecs";
import { Plus } from "lucide-react";
import { useState, useCallback } from "react";
import { Button, type ButtonProps } from "#/ui/button";
import { toast } from "sonner";
import { AddToGroupCombobox } from "../team/add-to-group-combobox";
import { NO_GROUP_ID } from "../team/constants";
import { Label } from "#/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from "#/ui/select";

export function CreateNewServiceAccountModal({
  org,
  refreshAccounts,
  refreshTokens,
  setLastCreatedToken,
  accounts,
  variant = "ghost",
  createToken,
}: {
  org: Organization;
  refreshAccounts: () => void;
  refreshTokens: () => void;
  setLastCreatedToken: (token: string) => void;
  accounts: Array<User> | undefined;
  variant?: ButtonProps["variant"];
  createToken: (
    name: string,
    orgName: string,
    accountId: string,
  ) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string>("");
  const [opened, setOpened] = useState(false);

  const [selectedGroups, setSelectedGroups] = useState<
    { groupId: string; groupLabel: string }[]
  >([]);

  const [selectedAccount, setSelectedAccount] = useState<string | undefined>(
    undefined,
  );

  const [serviceAccountName, setServiceAccountName] = useState<string>(
    accounts && accounts.length > 0
      ? `Service account ${accounts.length}`
      : "Service account",
  );
  const [serviceTokenName, setServiceTokenName] = useState<string>(
    "Default service token",
  );

  const { getToken } = useAuth();
  const createServiceAccount = useCallback(
    async (name: string, token_name: string) => {
      try {
        const group_ids = selectedGroups
          .map((g) => g.groupId)
          .filter((g) => g !== NO_GROUP_ID);
        const res = await invokeServerAction<typeof addMembers>({
          fName: "addMembers",
          args: {
            orgId: org.id,
            users: {
              service_accounts: [{ name, token_name }],
              send_invite_emails: false,
              group_ids: group_ids,
            },
          },
          getToken,
        });
        if (res.send_email_error) {
          throw new Error(
            "Unexpected email invite error while creating service account.",
          );
        }
        if (res.added_users.length !== 1) {
          throw new Error(
            `Unexpected number of service accounts (${res.added_users.length}) created.`,
          );
        }
        if (!res.added_users[0].api_key) {
          throw new Error(
            "Missing service token while creating service account.",
          );
        }
        setLastCreatedToken(res.added_users[0].api_key);

        refreshAccounts();
        refreshTokens();
      } catch (error) {
        toast.error(`Could not add service account and token`, {
          description: `${error}`,
        });
      }
    },
    [
      getToken,
      org.id,
      refreshAccounts,
      refreshTokens,
      selectedGroups,
      setLastCreatedToken,
    ],
  );

  // Simple helper to get the effective selected account
  const currentSelection =
    selectedAccount ||
    (!accounts || accounts.length === 0
      ? "internal_value_only_new"
      : undefined);

  return (
    <Dialog open={opened} onOpenChange={setOpened}>
      <DialogTrigger asChild>
        <Button Icon={Plus} size="sm" variant={variant}>
          Service token
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-xl">
        <form
          onSubmit={async (e) => {
            e.preventDefault();

            setCreating(true);
            setError("");

            try {
              if (
                currentSelection &&
                currentSelection !== "internal_value_only_new"
              ) {
                await createToken(serviceTokenName, org.name, currentSelection);
              } else {
                await createServiceAccount(
                  serviceAccountName ||
                    `Service account ${accounts?.length ?? 0}`,
                  serviceTokenName,
                );
              }

              setCreating(false);
              setOpened(false);
            } catch (e) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              setError(
                (e instanceof Error ? e.message : `${e}`) ||
                  "Failed to create service account",
              );
              console.error("Failed to create service account", e);
              setCreating(false);
            } finally {
              setCreating(false);
              setServiceAccountName("");
              setSelectedAccount(undefined);
              setSelectedGroups([]);
              setServiceTokenName("Default service token");
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Create new service token</DialogTitle>
            <DialogDescription>
              Create a service token for an existing or new service account.
              Service tokens are used by service accounts to authenticate with
              Braintrust.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4">
            {accounts && accounts.length > 0 && (
              <div className="flex flex-col gap-2">
                <Label htmlFor="serviceAccountName">Service account</Label>
                <Select
                  value={currentSelection}
                  onValueChange={(value) => {
                    setSelectedAccount(value);
                  }}
                >
                  <SelectTrigger autoFocus>
                    <SelectValue placeholder="Select existing or create new service account" />
                  </SelectTrigger>
                  <SelectContent>
                    {accounts?.map((a) => (
                      <SelectItem key={a.id} value={a.id} className="text-sm">
                        {a.given_name}
                      </SelectItem>
                    ))}
                    <SelectSeparator />
                    <SelectItem
                      value="internal_value_only_new"
                      className="text-sm"
                    >
                      Create new service account
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            {currentSelection === "internal_value_only_new" && (
              <div className="flex flex-col gap-2">
                <Label htmlFor="serviceAccountName">Service account name</Label>
                <PlainInput
                  autoFocus={!accounts || accounts.length === 0}
                  id="serviceAccountName"
                  name="serviceAccountName"
                  placeholder="Enter service account name"
                  value={serviceAccountName}
                  onChange={(e) => setServiceAccountName(e.target.value)}
                />
              </div>
            )}
            {currentSelection === "internal_value_only_new" && (
              <AddToGroupCombobox
                orgName={org.name}
                selectedGroups={selectedGroups.map((g) => g.groupId)}
                onChange={(groupId, groupLabel) => {
                  const isNoGroupSelected = selectedGroups.some(
                    (g) => g.groupId === NO_GROUP_ID,
                  );
                  const isCurrentGroupNoGroup = groupId === NO_GROUP_ID;

                  if (isNoGroupSelected !== isCurrentGroupNoGroup) {
                    setSelectedGroups([{ groupId, groupLabel }]);
                    return;
                  }

                  const isAlreadySelected = selectedGroups.some(
                    (g) => g.groupId === groupId,
                  );

                  if (isAlreadySelected) {
                    setSelectedGroups(
                      selectedGroups.filter((g) => g.groupId !== groupId),
                    );
                  } else {
                    setSelectedGroups([
                      ...selectedGroups,
                      { groupId, groupLabel },
                    ]);
                  }
                }}
              >
                <Button
                  isDropdown
                  className="flex-1 justify-start overflow-hidden pr-3 pl-2 text-left"
                >
                  {selectedGroups.length === 0 ? (
                    <span className="flex-1 truncate text-primary-500">
                      Select permission groups
                    </span>
                  ) : (
                    <span className="flex-1 truncate font-normal">
                      {selectedGroups.map((g) => g.groupLabel).join(", ")}
                    </span>
                  )}
                </Button>
              </AddToGroupCombobox>
            )}
            <div className="flex flex-col gap-2">
              <Label htmlFor="serviceTokenName">Service token name</Label>
              <PlainInput
                id="serviceTokenName"
                name="serviceTokenName"
                placeholder="Enter service token name"
                value={serviceTokenName}
                onChange={(e) => setServiceTokenName(e.target.value)}
              />
            </div>
          </div>

          {error && <div className="mt-4 text-red-500">Error: {error}</div>}
          <DialogFooter>
            <Button
              isLoading={creating}
              variant="primary"
              disabled={
                !currentSelection ||
                !serviceTokenName.trim() ||
                (currentSelection === "internal_value_only_new" &&
                  (!serviceAccountName.trim() || selectedGroups.length === 0))
              }
            >
              Create
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
