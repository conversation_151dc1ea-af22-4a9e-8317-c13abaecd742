import * as Al<PERSON> from "#/ui/alert-dialog";
import Token from "#/ui/token";

export const CreatedServiceTokenDialog = ({
  lastCreatedToken,
  setLastCreatedToken,
}: {
  lastCreatedToken: string | null;
  setLastCreatedToken: (token: string | null) => void;
}) => {
  return (
    lastCreatedToken && (
      <>
        <Alert.AlertDialog open={!!lastCreatedToken}>
          <Alert.AlertDialogContent className="max-w-3xl">
            <Alert.AlertDialogHeader>
              <Alert.AlertDialogTitle>
                Service token created
              </Alert.AlertDialogTitle>
              <Alert.AlertDialogDescription>
                Please save this service token somewhere safe and accessible.
                For security reasons, you will not be able to view it again. If
                you lose this service token, you must generate a new one.
              </Alert.AlertDialogDescription>
            </Alert.AlertDialogHeader>
            <Token token={lastCreatedToken} />
            <Alert.AlertDialogFooter>
              <Alert.AlertDialogAction
                onClick={() => {
                  setLastCreatedToken(null);
                }}
              >
                Close
              </Alert.AlertDialogAction>
            </Alert.AlertDialogFooter>
          </Alert.AlertDialogContent>
        </Alert.AlertDialog>
      </>
    )
  );
};
