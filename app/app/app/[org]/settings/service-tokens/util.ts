import {
  type DataPlaneManager,
  type DataPlaneServiceToken,
} from "#/pages/api/service_token/provision_data_plane_manager";
import {
  type LoadedBtSessionToken,
  sessionFetchProps,
  useSessionToken,
} from "#/utils/auth/session-token";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

export async function createServiceToken({
  name,
  orgName,
  accountId,
}: {
  name: string;
  orgName: string;
  accountId: string;
}) {
  const resp = await fetch("/api/service_token/register", {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      service_token_name: name,
      org_name: orgName,
      service_account_id: accountId,
    }),
  });

  if (!resp.ok) {
    throw new Error(await resp.text());
  }

  const { service_token } = await resp.json();
  return service_token.key;
}

export async function provisionDataPlaneManager({
  orgId,
  forceRecreateToken,
}: {
  orgId: string;
  forceRecreateToken?: boolean;
}): Promise<{
  data_plane_manager: DataPlaneManager;
  data_plane_service_token: DataPlaneServiceToken;
}> {
  const resp = await fetch("/api/service_token/provision_data_plane_manager", {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      org_id: orgId,
      force_recreate_token: forceRecreateToken,
    }),
  });

  if (!resp.ok) {
    throw new Error(await resp.text());
  }

  return await resp.json();
}

export function useCreateServiceToken() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      orgId,
      accountId,
    }: {
      name: string;
      orgId: string;
      accountId: string;
    }) => {
      const resp = await fetch("/api/service_token/register", {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, org_id: orgId, account_id: accountId }),
      });

      if (!resp.ok) {
        throw new Error(await resp.text());
      }

      const { service_token } = await resp.json();
      return service_token.key;
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ["serviceTokens"] });
    },
    onError: (error) => {
      toast.error("Error while creating service token:", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    },
  });
}

export function useDataPlaneManager(orgId: string) {
  return useQuery({
    queryKey: ["dataPlaneManager", orgId],
    queryFn: () => provisionDataPlaneManager({ orgId }),
    enabled: !!orgId,
  });
}

export function useReprovisionDataPlaneManager() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ orgId }: { orgId: string }) => {
      return provisionDataPlaneManager({ orgId, forceRecreateToken: true });
    },
    onSuccess: (data, variables) => {
      void queryClient.invalidateQueries({
        queryKey: ["dataPlaneManager", variables.orgId],
      });
      queryClient.setQueryData(["dataPlaneManager", variables.orgId], data);
    },
    onError: (error) => {
      toast.error("Error while reprovisioning data plane manager:", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    },
  });
}

export async function checkDataPlaneSyncStatus({
  apiUrl,
  dataPlaneServiceToken,
  token,
}: {
  apiUrl: string;
  dataPlaneServiceToken: DataPlaneServiceToken;
  token: LoadedBtSessionToken;
}): Promise<boolean> {
  const { sessionHeaders, sessionExtraFetchProps } = sessionFetchProps(token);
  const resp = await fetch(
    `${apiUrl}/service-token/${dataPlaneServiceToken.name}`,
    {
      method: "HEAD",
      headers: {
        "Content-Type": "application/json",
        ...sessionHeaders,
      },
      ...sessionExtraFetchProps,
    },
  );
  return resp.ok;
}

export function useCheckDataPlaneSyncStatus({
  apiUrl,
  dataPlaneServiceToken,
}: {
  apiUrl: string;
  dataPlaneServiceToken?: DataPlaneServiceToken;
}) {
  const { getOrRefreshToken } = useSessionToken();
  return useQuery({
    queryKey: ["dataPlaneSync", apiUrl, dataPlaneServiceToken?.name],
    queryFn: async () => {
      const token = await getOrRefreshToken();
      if (!token) throw new Error("No auth token available");
      return checkDataPlaneSyncStatus({
        apiUrl,
        dataPlaneServiceToken: dataPlaneServiceToken!,
        token,
      });
    },
    enabled: !!apiUrl && !!dataPlaneServiceToken?.name,
  });
}

export function useUpsertDataPlaneServiceToken() {
  const queryClient = useQueryClient();
  const { getOrRefreshToken } = useSessionToken();
  return useMutation({
    mutationFn: async ({
      apiUrl,
      dataPlaneServiceToken,
    }: {
      apiUrl: string;
      dataPlaneServiceToken: DataPlaneServiceToken;
    }) => {
      const token = await getOrRefreshToken();
      const { sessionHeaders, sessionExtraFetchProps } =
        sessionFetchProps(token);
      const resp = await fetch(`${apiUrl}/service-token/upsert`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...sessionHeaders,
        },
        body: JSON.stringify({
          name: dataPlaneServiceToken.name,
          service_token: dataPlaneServiceToken.key,
        }),
        ...sessionExtraFetchProps,
      });

      if (!resp.ok) {
        throw new Error(await resp.text());
      }

      return await resp.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries when the upsert succeeds
      void queryClient.invalidateQueries({ queryKey: ["dataPlaneSync"] });
    },
    onError: (error) => {
      void queryClient.invalidateQueries({ queryKey: ["dataPlaneSync"] });
      toast.error("Error while updating data plane with new service token:", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    },
  });
}
