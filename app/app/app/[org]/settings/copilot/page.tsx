"use client";

import { useOrg } from "#/utils/user";
import { useEffect, useMemo, useState } from "react";

import "react-image-crop/dist/ReactCrop.css";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useFeatureFlags } from "#/lib/feature-flags";
import { Skeleton } from "#/ui/skeleton";
import { useAvailableModels } from "#/ui/prompts/models";
import { ModelDropdown } from "../../prompt/[prompt]/ModelDropdown";
import { deriveCopilotModel } from "#/ui/copilot/complete";
import Link from "next/link";
import { getOrgSettingsLink } from "../../getOrgLink";
import { FeatureFlagToggle } from "../feature-flags/feature-flag-toggle";

// See https://github.com/pacocoursey/next-themes/issues/169
export default function Page() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex w-full max-w-md flex-col gap-2">
        <Skeleton className="h-20" />
        <Skeleton className="h-20" />
        <Skeleton className="h-20" />
      </div>
    );
  }

  return <PageImpl />;
}

function PageImpl() {
  const org = useOrg();

  const {
    allAvailableModels,
    configuredModelsByProvider,
    noConfiguredSecrets,
  } = useAvailableModels({ orgName: org.name });

  const [copilotModel, setCopilotModel] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.name,
    key: "copilotModel",
  });
  const derivedCopilotModel = deriveCopilotModel(
    copilotModel,
    allAvailableModels,
  );
  const availableCopilotModel = useMemo(
    () => !!(derivedCopilotModel && allAvailableModels[derivedCopilotModel]),
    [derivedCopilotModel, allAvailableModels],
  );

  const { flags, setFlag } = useFeatureFlags();

  return (
    <div className="flex flex-col gap-8">
      <div>
        <h2 className="mb-1 text-lg font-semibold">Copilot autocomplete</h2>
        <p className="text-sm">
          AI autocomplete in text fields based on project context.{" "}
          <Link href="/blog/copilot" className="font-medium text-accent-600">
            Read more
          </Link>{" "}
          in the blog.
        </p>
      </div>

      <div>
        <FeatureFlagToggle
          className="mb-2"
          checked={flags.copilot}
          flag="copilot"
          setFlag={setFlag}
          title="Enable Copilot autocomplete"
        />
        <div className="max-w-sm text-xs text-primary-600">
          Unless self-hosting, completions are logged by Braintrust for quality
          control
        </div>
      </div>

      {flags.copilot && (
        <>
          <div className="flex w-full flex-col gap-2">
            <div className="text-sm font-medium text-primary-800">
              Product areas
            </div>
            <FeatureFlagToggle
              checked={flags.copilotPrompts}
              flag="copilotPrompts"
              setFlag={setFlag}
              title="Prompts"
            />
            <FeatureFlagToggle
              checked={flags.copilotData}
              flag="copilotData"
              setFlag={setFlag}
              title="Experiments, logs, and datasets"
            />
          </div>
          <div className="flex w-full flex-col gap-2">
            <div className="text-sm font-medium text-primary-800">Model</div>
            {noConfiguredSecrets ? (
              <div className="max-w-sm text-sm text-primary-600">
                Configure an{" "}
                <Link
                  className="font-medium text-accent-700"
                  href={`${getOrgSettingsLink({ orgName: org.name })}/secrets`}
                >
                  AI provider
                </Link>{" "}
                to enable autocomplete for prompt and data inputs
              </div>
            ) : (
              <div>
                <ModelDropdown
                  isReadOnly={false}
                  orgName={org.name}
                  currentModel={derivedCopilotModel ?? undefined}
                  onChange={(value) => {
                    setCopilotModel(value);
                  }}
                  modelOptionsByProvider={configuredModelsByProvider}
                  currentModelNotFound={
                    copilotModel !== undefined && !availableCopilotModel
                  }
                  className="w-full max-w-sm rounded-r-md"
                />
                <div className="max-w-sm pt-2 text-xs text-primary-600">
                  The model is run using your organization&apos;s AI provider
                  keys
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
