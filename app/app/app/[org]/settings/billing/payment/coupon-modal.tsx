"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "#/ui/dialog";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { Input } from "#/ui/input";
import { Button } from "#/ui/button";

interface CouponModalProps {
  open: boolean;
  onClose: () => void;
  onCouponApplied: (couponCode: string) => void;
}

type ValidateCouponFn = (args: { couponCode: string }) => Promise<{
  isValid: boolean;
}>;

export function CouponModal({
  open,
  onClose,
  onCouponApplied,
}: CouponModalProps) {
  const [couponCode, setCouponCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();

  const validateCoupon = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsValidating(true);
    setError(null);

    try {
      const result = await invokeServerAction<ValidateCouponFn>({
        fName: "validateCoupon",
        args: { couponCode },
        getToken,
      });

      if (result?.isValid) {
        onCouponApplied(couponCode);
        onClose();
      } else {
        setError("Invalid coupon code");
      }
    } catch (err) {
      setError("Failed to validate coupon");
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="pb-4">Apply Coupon</DialogTitle>
        </DialogHeader>
        <form onSubmit={validateCoupon} className="space-y-4">
          <div>
            <label htmlFor="couponCode" className="block text-xs font-medium">
              Coupon code
            </label>
            <Input
              id="couponCode"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value)}
              placeholder="Enter coupon code"
              className="mt-2"
            />
            {error && <p className="mt-2 text-sm text-bad-600">{error}</p>}
          </div>
          <div className="flex justify-end gap-3">
            <Button type="button" variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={isValidating}
              disabled={!couponCode}
            >
              Apply
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
