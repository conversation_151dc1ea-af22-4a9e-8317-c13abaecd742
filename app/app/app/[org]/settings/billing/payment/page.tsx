import { type Permission } from "@braintrust/typespecs";
import { ClientPage, type Params } from "./clientpage";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { getOrganization } from "#/app/app/actions";
import { decodeURIComponentPatched } from "#/utils/url";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const org = await getOrganization({ org_name });

  let orgPermissions: Permission[] = [];

  try {
    const orgPerms = await getObjectAclPermissions({
      objectType: "organization",
      objectId: org?.id,
    });
    orgPermissions = orgPerms ?? [];
  } catch (e) {
    console.error("Failed to get permissions on payment page", e);
  }

  return <ClientPage orgPermissions={orgPermissions} />;
}
