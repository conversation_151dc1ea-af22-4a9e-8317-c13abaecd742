import * as Sentry from "@sentry/nextjs";
import { useState } from "react";
import { But<PERSON> } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "#/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "#/ui/select";
import TextArea from "#/ui/text-area";
import { Label } from "#/ui/label";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { type downgradeProSubscription } from "#/utils/billing/utils";
import { useAuth } from "@clerk/nextjs";
import { useOrg } from "#/utils/user";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";

function formatErrorMessage(error: unknown, action: string): string {
  const baseMessage = `Failed to ${action}.`;
  const supportMessage =
    "Please try again. Contact <NAME_EMAIL> if the problem persists.";

  if (error instanceof Error) {
    return `${baseMessage} ${error.message}. ${supportMessage}`;
  }

  return `${baseMessage} ${supportMessage}`;
}

const DowngradeModal = ({
  isOpen,
  onOpenChange,
  onSuccess,
}: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSuccess: () => void;
}) => {
  const { track } = useAppAnalytics();
  const org = useOrg();
  const { getToken } = useAuth();

  const [isDowngradeInProgress, setIsDowngradeInProgress] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [downgradeReason, setDowngradeReason] = useState<string>("");
  const [otherReason, setOtherReason] = useState<string>("");

  const handleDowngrade = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (
      !downgradeReason ||
      (downgradeReason === "other" && !otherReason.trim())
    ) {
      return;
    }

    setIsDowngradeInProgress(true);
    setError(null);

    if (org.id) {
      try {
        await invokeServerAction<typeof downgradeProSubscription>({
          fName: "downgradeProSubscription",
          args: {
            orgId: org.id,
          },
          getToken,
        });
        onOpenChange(false);
        onSuccess();
        track("downgradeProSubscription", {
          reason: downgradeReason,
          otherReason,
        });
      } catch (error) {
        setError(formatErrorMessage(error, "downgrade subscription"));
        Sentry.captureException(error, {
          tags: {
            page: "billing-page",
          },
        });
      } finally {
        setIsDowngradeInProgress(false);
      }
    }
  };

  const handleClose = () => {
    setError(null);
    setDowngradeReason("");
    setOtherReason("");
    onOpenChange(false);
  };

  const isFormValid =
    downgradeReason && (downgradeReason !== "other" || otherReason.trim());

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Downgrade to free plan</DialogTitle>
          <DialogDescription>
            <span className="block pb-2">
              Are you sure you want to downgrade to the Free plan?
            </span>
            <span className="block">
              You&apos;ll be charged for any Pro plan usage this month, and your
              account will immediately switch to Free plan limits.
            </span>
          </DialogDescription>
        </DialogHeader>
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-700">
            {error}
          </div>
        )}
        <form onSubmit={handleDowngrade} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="downgrade-reason">Why are you downgrading?</Label>
            <Select
              value={downgradeReason || undefined}
              onValueChange={setDowngradeReason}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="too-expensive">Too expensive</SelectItem>
                <SelectItem value="not-using-features">
                  Not using Pro features
                </SelectItem>
                <SelectItem value="switching-platform">
                  Switching to another platform
                </SelectItem>
                <SelectItem value="temporary">Temporary downgrade</SelectItem>
                <SelectItem value="budget-cuts">Budget cuts</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {downgradeReason === "other" && (
            <div className="space-y-2">
              <Label htmlFor="other-reason">Please specify</Label>
              <TextArea
                id="other-reason"
                placeholder="Tell us more about why you're downgrading..."
                value={otherReason}
                onChange={(e) => setOtherReason(e.target.value)}
                minRows={3}
                maxRows={6}
              />
            </div>
          )}

          <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:gap-0">
            <Button type="button" variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isDowngradeInProgress}
              variant="primary"
              disabled={!isFormValid}
            >
              Downgrade
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DowngradeModal;
