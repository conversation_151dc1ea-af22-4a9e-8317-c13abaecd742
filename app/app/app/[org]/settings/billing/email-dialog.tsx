import React, { useState } from "react";
import * as Sentry from "@sentry/nextjs";
import { But<PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "#/ui/dialog";
import { Input } from "#/ui/input";
import { type addOrbCustomerBillingEmail } from "#/utils/billing/utils";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useOrg } from "#/utils/user";
import { useAuth } from "@clerk/nextjs";
import { EMAIL_REGEX } from "#/utils/email-validation";

const BillingEmailDialog = ({
  isOpen,
  onOpenChange,
  onSuccess,
}: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSuccess: () => void;
}) => {
  const { id: orgId } = useOrg();
  const { getToken } = useAuth();

  const [isEmailUpdateInProgress, setIsEmailUpdateInProgress] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [billingEmail, setBillingEmail] = useState("");
  const [validEmail, setValidEmail] = useState(false);

  const handleEmailUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setIsEmailUpdateInProgress(true);
    setError(null);

    if (!orgId) {
      setError("Organization ID is not available");
      setIsEmailUpdateInProgress(false);
      return;
    }
    try {
      await invokeServerAction<typeof addOrbCustomerBillingEmail>({
        fName: "addOrbCustomerBillingEmail",
        args: {
          orgId,
          email: billingEmail,
        },
        getToken,
      });
      onOpenChange(false);
      onSuccess();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred",
      );

      // All serverside errors will ping the #billing-errors channel in Slack so that we can monitor them closely
      Sentry.captureException(err, {
        tags: {
          page: "billing-page",
        },
      });

      console.error(err);
    } finally {
      setIsEmailUpdateInProgress(false);
    }
  };

  const handleClose = () => {
    setError(null);
    setBillingEmail("");
    onOpenChange(false);
  };

  const setEmail = (email: string) => {
    setBillingEmail(email);
    setValidEmail(EMAIL_REGEX.test(email));
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Billing Email Address</DialogTitle>
        </DialogHeader>
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-700">
            {error}
          </div>
        )}
        <form onSubmit={handleEmailUpdate}>
          <div className="space-y-2">
            <div className="mb-8 flex flex-col gap-2">
              <Input
                type="email"
                placeholder="Enter email address"
                className="h-11 text-base"
                required
                value={billingEmail}
                onChange={(e) => setEmail(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.currentTarget.blur();
                    e.currentTarget.form?.requestSubmit();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:gap-0">
            <Button type="button" variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isEmailUpdateInProgress}
              variant="primary"
              disabled={!validEmail}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default BillingEmailDialog;
