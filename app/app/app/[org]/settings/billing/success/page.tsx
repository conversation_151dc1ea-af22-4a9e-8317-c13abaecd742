import { redirect } from "next/navigation";
import {
  applyPendingSubscriptionChange,
  createAndConfirmPaymentFromPendingSubscription,
  getSetupIntent,
  maybeCreateOrbSubscription,
  setDefaultPaymentMethod,
  syncPaymentMethodInOrb,
  updateOrbCustomerAddressFromStripeSetupIntent,
} from "#/utils/billing/utils";
import { captureSentryServerException } from "#/utils/sentry/sentry";

// The billing success page:
// 1. Sets the default payment method in Stripe
// 2. Syncs the payment method to Orb
// 3. Updates the Orb customer address with the address collected from the Stripe form
// 4. Creates a pending Orb subscription if a purchasePlanId is provided
// 5. Applies the pending subscription change if billing the first invoice is successful
export default async function SuccessPage({
  searchParams: searchParamsPromise,
}: {
  searchParams: Promise<{
    setup_intent?: string;
    orbCustomerId?: string;
    purchasePlanId?: string;
    couponCode?: string;
    purchasePlanSlug?: string;
  }>;
}) {
  const searchParams = await searchParamsPromise;

  const setupIntentId = searchParams.setup_intent;
  const orbCustomerId = searchParams.orbCustomerId;
  const purchasePlanId = searchParams.purchasePlanId;
  const couponCode = searchParams.couponCode;
  const purchasePlanSlug = searchParams.purchasePlanSlug;

  if (!setupIntentId || !orbCustomerId) {
    redirect("../billing");
  }

  let paymentErrorMessage = "";

  try {
    const setupIntent = await getSetupIntent(setupIntentId);
    await setDefaultPaymentMethod(setupIntent);
    await syncPaymentMethodInOrb({ orgId: orbCustomerId });

    // Update Orb customer address with the address collected from the Stripe form
    await updateOrbCustomerAddressFromStripeSetupIntent({
      setupIntent,
      orbCustomerId,
    });

    // If a purchasePlanId is provided, create an Orb subscription
    // Otherwise, we assume we are just updating the payment method
    // and don't need to create an Orb subscription
    if (purchasePlanId) {
      const pendingSubscription = await maybeCreateOrbSubscription({
        orbCustomerId,
        purchasePlanId,
        couponCode,
        isPending: true,
      });

      if (!pendingSubscription) {
        throw new Error("Failed to create pending subscription");
      }

      const { pendingSubscriptionChangeId, previouslyCollectedAmount } =
        await createAndConfirmPaymentFromPendingSubscription({
          pendingSubscription,
          setupIntent,
        });

      // Finalize the pending subscription, making the subscription active
      await applyPendingSubscriptionChange({
        changeId: pendingSubscriptionChangeId,
        previouslyCollectedAmount,
      });
    }
  } catch (error) {
    console.error(error);

    paymentErrorMessage =
      error instanceof Error ? error.message : String(error);

    // Anything that goes wrong on the success page should be sent to #billing-errors
    captureSentryServerException({
      error: error instanceof Error ? error : String(error),
      tags: {
        page: "billing-page",
        orbCustomerId,
      },
    });
  }

  if (paymentErrorMessage) {
    const searchParams = new URLSearchParams({
      errorMessage: paymentErrorMessage,
      ...(purchasePlanSlug && { purchasePlanSlug }),
    });
    // If we have an error, redirect back to the payment page to show the error in a banner
    redirect(`../billing/payment?${searchParams}`);
  } else {
    redirect("../billing");
  }
}
