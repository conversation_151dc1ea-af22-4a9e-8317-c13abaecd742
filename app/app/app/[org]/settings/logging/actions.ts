"use server";

import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";

// RBAC_DISCLAIMER: Any org member can read the git metadata.
export async function fetchGitMetadata({
  orgId,
}: {
  orgId?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
}): Promise<any> {
  if (!orgId) {
    return null;
  }

  const authLookup = await getServerSessionAuthLookup();

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
    select git_metadata
    from org_settings
    where
        org_id = $1
        and exists (
            select 1
            from
                organizations
                join members on organizations.id = members.org_id
                join users on members.user_id = users.id
            where
                organizations.id = $1 and users.auth_id = $2
        )
    `,
    [orgId, authLookup.auth_id],
  );
  return rows.length ? rows[0].git_metadata : null;
}

export async function upsertGitMetadata({
  orgId,
  gitMetadata,
}: {
  orgId?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  gitMetadata: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
}): Promise<any> {
  if (!orgId) {
    throw new Error("Missing input");
  }

  const authLookup = await getServerSessionAuthLookup();

  const {
    query: orgQuery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "update",
    },
    filters: {
      org_id: orgId,
    },
  });

  const query = `
    with rows_to_insert as (
        select organizations.id org_id, ${queryParams.add(
          gitMetadata,
        )}::jsonb git_metadata
        from (${orgQuery}) organizations
        limit 1
    )
    insert into org_settings(org_id, git_metadata)
    select * from rows_to_insert
    on conflict (org_id) do update set git_metadata = EXCLUDED.git_metadata
    returning git_metadata
  `;
  const supabase = getServiceRoleSupabase();
  const row = extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage,
  });
  return row.git_metadata;
}
