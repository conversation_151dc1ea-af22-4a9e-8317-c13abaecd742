import { isClerkAPIResponseError } from "@clerk/nextjs/errors";
import { type ClerkAPIError } from "@clerk/types";

export function handleClerkError(e: unknown): ClerkAPIError[] {
  if (isClerkAPIResponseError(e)) {
    return e.errors;
  } else {
    const message = e instanceof Error ? e.message : String(e);
    return [
      {
        code: "unknown_error",
        longMessage: message,
        message: message,
      },
    ];
  }
}

export function ClerkErrors({ errors }: { errors: ClerkAPIError[] }) {
  return (
    <ul className="pt-2">
      {errors.map((el, index) => (
        <li className="text-red-500" key={index}>
          {el.longMessage}
        </li>
      ))}
    </ul>
  );
}
