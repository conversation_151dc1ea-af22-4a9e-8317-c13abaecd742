"use client";

import { useState } from "react";
import { useUser as useClerkUser } from "@clerk/nextjs";
import { <PERSON><PERSON> } from "#/ui/button";
import { Input } from "#/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "#/ui/dialog";
import { type ClerkAPIError } from "@clerk/types";
import { ClerkErrors, handleClerkError } from "./errors";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const createPasswordFormSchema = (mode: "set" | "update") =>
  z.object({
    currentPassword:
      mode === "update"
        ? z.string().min(1, "Current password is required")
        : z.string().optional(),
    password: z.string().min(1, "Password is required"),
    confirmPassword: z.string().min(1, "Confirm password is required"),
  });

type PasswordFormData = z.infer<ReturnType<typeof createPasswordFormSchema>>;

interface PasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: "set" | "update";
}

export function PasswordModal({ isOpen, onClose, mode }: PasswordModalProps) {
  const { user } = useClerkUser();
  const [errors, setErrors] = useState<ClerkAPIError[]>();

  const form = useForm<PasswordFormData>({
    resolver: zodResolver(createPasswordFormSchema(mode)),
    mode: "onSubmit",
    defaultValues: {
      currentPassword: "",
      password: "",
      confirmPassword: "",
    },
  });

  const handleSubmit = form.handleSubmit(async (data) => {
    setErrors(undefined);

    if (data.password !== data.confirmPassword) {
      form.setError("confirmPassword", {
        type: "manual",
        message: "Passwords don't match",
      });
      return;
    }

    try {
      if (mode === "set") {
        await user?.updatePassword({
          newPassword: data.password,
        });
      } else {
        await user?.updatePassword({
          currentPassword: data.currentPassword || "",
          newPassword: data.password,
        });
      }
      onClose();
      form.reset();
    } catch (error) {
      setErrors(handleClerkError(error));
    }
  });

  const handleClose = () => {
    form.reset();
    setErrors(undefined);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {mode === "set" ? "Set password" : "Update password"}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-4">
            {mode === "update" && (
              <FormField
                control={form.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter current password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {mode === "set" ? "Password" : "New password"}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {mode === "set"
                      ? "Confirm password"
                      : "Confirm new password"}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Confirm password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {errors && <ClerkErrors errors={errors} />}

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="border" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" isLoading={form.formState.isSubmitting}>
                {mode === "set" ? "Set password" : "Update password"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
