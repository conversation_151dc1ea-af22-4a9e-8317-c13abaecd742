"use client";

import { useState } from "react";

import { type BackupCodeResource, type ClerkAPIError } from "@clerk/types";
import { useUser as useClerkUser } from "@clerk/nextjs";

import { But<PERSON> } from "#/ui/button";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { ClerkErrors, handleClerkError } from "./errors";
import TextArea from "#/ui/text-area";

function BackupCodes() {
  const [backupCodes, setBackupCodes] = useState<
    BackupCodeResource | undefined
  >(undefined);
  const { user } = useClerkUser();
  const [errors, setErrors] = useState<ClerkAPIError[]>();
  async function generateBackupCodes() {
    try {
      const backupCodes = await user?.createBackupCode();
      setBackupCodes(backupCodes);
    } catch (error) {
      setErrors(handleClerkError(error));
    }
  }
  return (
    <>
      <div className="max-w-lg pt-6 pb-3 text-sm">
        Generate backup codes to access your account if you lose your
        authenticator app. Make sure you store these codes securely.
      </div>

      {backupCodes ? (
        <div className="py-2">
          <TextArea
            className="max-w-lg resize-none font-mono text-sm"
            value={backupCodes.codes.join(" ")}
          />
          <CopyToClipboardButton
            className="mt-2"
            size="xs"
            prependIcon
            textToCopy={backupCodes.codes.join(" ")}
          >
            Copy codes
          </CopyToClipboardButton>
        </div>
      ) : (
        <Button size="sm" onClick={generateBackupCodes}>
          Generate
        </Button>
      )}
      {errors && <ClerkErrors errors={errors} />}
    </>
  );
}

export { BackupCodes };
