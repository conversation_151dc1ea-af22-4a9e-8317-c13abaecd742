import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "#/ui/dialog";
import { Button } from "#/ui/button";
import { PlainInput } from "#/ui/plain-input";
import { Spinner } from "#/ui/icons/spinner";
import { createGroup } from "./api-groups";
import TextareaAutosize from "react-textarea-autosize";
import { inputClassName } from "#/ui/input";
import { useSessionToken } from "#/utils/auth/session-token";
function NewGroupButton({
  orgName,
  onCreate,
  apiUrl,
  allGroupNames,
}: {
  orgName: string;
  onCreate: () => void;
  apiUrl: string;
  allGroupNames: string[];
}) {
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string>("");
  const [opened, setOpened] = useState(false);
  const { getOrRefreshToken } = useSessionToken();

  return (
    <Dialog open={opened} onOpenChange={setOpened}>
      <DialogTrigger asChild>
        <Button>Create permission group</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-xl">
        <form
          onSubmit={async (e) => {
            e.preventDefault();

            setCreating(true);
            setError("");

            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const formData = new FormData(e.target as HTMLFormElement);
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const newGroupName = formData.get("newGroupName") as string;
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const newGroupDescription = formData.get(
              "newGroupDescription",
            ) as string;

            if (!newGroupName) {
              setError("Name is required");
              setCreating(false);
              return;
            }

            if (!newGroupDescription) {
              setError("Description is required");
              setCreating(false);
              return;
            }

            if (allGroupNames.includes(newGroupName.toLowerCase())) {
              setError("Group with this name already exists");
              setCreating(false);
              return;
            }

            if (!apiUrl) {
              setError(
                "Auth session still loading, try again in a few seconds",
              );
              setCreating(false);
              return;
            }

            try {
              await createGroup({
                name: newGroupName,
                description: newGroupDescription,
                orgName,
                apiUrl,
                sessionToken: await getOrRefreshToken(),
              });

              setOpened(false);
              onCreate();
            } catch (e) {
              setError(
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                (e as Error).message || "Failed to create permission group",
              );
              console.error("Failed to create group", e);
            } finally {
              setCreating(false);
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Create permission group</DialogTitle>
          </DialogHeader>
          <div className="pt-8 pb-4">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col items-start gap-2">
                <label htmlFor="newGroupName" className="text-xs font-medium">
                  Name
                </label>
                <PlainInput
                  id="newGroupName"
                  name="newGroupName"
                  className="w-full"
                  required
                  placeholder="Permission group name"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.target.setCustomValidity("");
                    setError("");
                  }}
                  onInvalid={(e: React.ChangeEvent<HTMLInputElement>) =>
                    e.target.setCustomValidity("Enter a group name")
                  }
                />
              </div>
              <div className="flex flex-col gap-2">
                <label
                  htmlFor="newGroupDescription"
                  className="text-xs font-medium"
                >
                  Description
                </label>
                <TextareaAutosize
                  id="newGroupDescription"
                  name="newGroupDescription"
                  className={inputClassName}
                  placeholder="Permission group description"
                  required
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    e.target.setCustomValidity("")
                  }
                  onInvalid={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    e.target.setCustomValidity("Enter a group description")
                  }
                />
              </div>
            </div>
          </div>
          {error && <div className="text-red-500">{error}</div>}
          <DialogFooter>
            <Button disabled={creating}>
              {creating ? <Spinner className="mr-1" /> : null}
              Create
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default NewGroupButton;
