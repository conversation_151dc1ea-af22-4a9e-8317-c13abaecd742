import {
  type LoadedBtSessionToken,
  sessionFetchProps,
} from "#/utils/auth/session-token";

export async function createGroup({
  name,
  description,
  orgName,
  apiUrl,
  sessionToken,
}: {
  name: string;
  description: string;
  orgName: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}): Promise<void> {
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const resp = await fetch(`${apiUrl}/v1/group`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    body: JSON.stringify({ name, description, org_name: orgName }),
    ...sessionExtraFetchProps,
  });

  if (!resp.ok) {
    throw new Error(await resp.text());
  }
}

export async function deleteGroup({
  groupId,
  apiUrl,
  sessionToken,
}: {
  groupId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}): Promise<void> {
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const resp = await fetch(`${apiUrl}/v1/group/${groupId}`, {
    method: "DELETE",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    ...sessionExtraFetchProps,
  });
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
}

export async function addMemberToGroup({
  userId,
  groupId,
  apiUrl,
  sessionToken,
}: {
  userId: string;
  groupId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}): Promise<void> {
  const groupIdUrl = `${apiUrl}/v1/group/${groupId}`;
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const headers = {
    Accept: "application/json",
    "Content-Type": "application/json",
    ...sessionHeaders,
  };
  const patchGroupResp = await fetch(groupIdUrl, {
    method: "PATCH",
    body: JSON.stringify({
      add_member_users: [userId],
    }),
    headers,
    ...sessionExtraFetchProps,
  });
  if (!patchGroupResp.ok) {
    throw new Error(await patchGroupResp.text());
  }
}

export async function removeMemberFromGroup({
  userId,
  groupId,
  apiUrl,
  sessionToken,
}: {
  userId: string;
  groupId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}): Promise<void> {
  const groupIdUrl = `${apiUrl}/v1/group/${groupId}`;
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const headers = {
    Accept: "application/json",
    "Content-Type": "application/json",
    ...sessionHeaders,
  };

  const patchGroupResp = await fetch(groupIdUrl, {
    method: "PATCH",
    headers,
    body: JSON.stringify({
      remove_member_users: [userId],
    }),
    ...sessionExtraFetchProps,
  });
  if (!patchGroupResp.ok) {
    throw new Error(await patchGroupResp.text());
  }
}
