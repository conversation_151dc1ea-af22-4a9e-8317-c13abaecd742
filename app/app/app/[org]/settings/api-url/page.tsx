import { getOrganization } from "#/app/app/actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { type Permission } from "@braintrust/typespecs";
import ClientPage from "./clientpage";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { isCspEnabled } from "#/security/csp";

export default async function Page(props: {
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const org = await getOrganization({ org_name });

  let orgPermissions: Permission[] = [];

  try {
    const orgPerms = await getObjectAclPermissions({
      objectType: "organization",
      objectId: org?.id,
    });

    orgPermissions = orgPerms ?? [];
  } catch (e) {
    console.error("Failed to get permissions on API URL settings page", e);
  }

  const isCspEnabledForOrg = isCspEnabled(org_name);

  return (
    <ClientPage
      orgPermissions={orgPermissions}
      orgName={org_name}
      isCspEnabled={isCspEnabledForOrg}
    />
  );
}
