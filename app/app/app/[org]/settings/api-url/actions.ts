"use server";

import { patchObjects } from "#/pages/api/_object_crud_util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { z } from "zod";

export async function patchOrganizationUrls({
  org_name,
  api_url,
  proxy_url,
  realtime_url,
}: {
  org_name: string;
  api_url?: string | null;
  proxy_url?: string | null;
  realtime_url?: string | null;
}) {
  // Nullify zero-length strings.
  api_url = api_url || null;
  proxy_url = proxy_url || null;
  realtime_url = realtime_url || null;

  const authLookup = await getServerSessionAuthLookup();

  return await patchObjects({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "update",
    },
    filters: {
      org_name,
    },
    fullResultsSize: 1,
    noCoalescePatchValueParams: {
      api_url,
      proxy_url,
      realtime_url,
    },
  });
}

export async function patchIsUniversalApi({
  org_name,
  is_universal_api,
}: {
  org_name: string;
  is_universal_api: boolean | null;
}) {
  const authLookup = await getServerSessionAuthLookup();

  return await patchObjects({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "update",
    },
    filters: {
      org_name,
    },
    fullResultsSize: 1,
    noCoalescePatchValueParams: {
      is_universal_api,
    },
  });
}

export async function getBrainstoreLicense({ org_name }: { org_name: string }) {
  const authLookup = await getServerSessionAuthLookup();

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
    select CONCAT('brainstore-', license) AS license
    from
        brainstore_licenses
        join organizations on (brainstore_licenses.org_id = organizations.id)
        join members on (organizations.id = members.org_id)
    where
      members.user_id = get_user_id_by_auth_id($1)
      and organizations.name = $2
    ORDER BY brainstore_licenses.created DESC
    LIMIT 1
  `,
    [authLookup.auth_id, org_name],
  );
  if (rows.length === 1) {
    return z.string().parse(rows[0].license);
  } else {
    return null;
  }
}
