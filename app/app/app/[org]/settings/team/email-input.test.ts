import { describe, test, expect } from "vitest";
import { extractGmailStyleEmail } from "./invite-members-form";

describe("extractGmailStyleEmail", () => {
  test("extracts email from Gmail-style format", () => {
    expect(extractGmailStyleEmail("<PERSON> <<EMAIL>>")).toBe(
      "<EMAIL>",
    );
    expect(extractGmailStyleEmail("First Last <<EMAIL>>")).toBe(
      "<EMAIL>",
    );

    expect(
      extractGmailStyleEmail("Name <<EMAIL>>"),
    ).toBe("<EMAIL>");

    expect(extractGmailStyleEmail("Test <user@[127.0.0.1]>")).toBe(
      "user@[127.0.0.1]",
    );

    expect(
      extractGmailStyleEmail(
        "Complex <<EMAIL>>",
      ),
    ).toBe("<EMAIL>");

    expect(extractGmailStyleEmail('Special <"quoted.email"@domain.com>')).toBe(
      '"quoted.email"@domain.com',
    );
  });

  test("returns plain email as-is", () => {
    expect(extractGmailStyleEmail("<EMAIL>")).toBe("<EMAIL>");
    expect(extractGmailStyleEmail("<EMAIL>")).toBe(
      "<EMAIL>",
    );
    expect(extractGmailStyleEmail("<EMAIL>")).toBe(
      "<EMAIL>",
    );
    expect(extractGmailStyleEmail("<EMAIL>")).toBe(
      "<EMAIL>",
    );
    expect(
      extractGmailStyleEmail("very.unusual.'@'.<EMAIL>"),
    ).toBe("very.unusual.'@'.<EMAIL>");
    expect(extractGmailStyleEmail("!#$%&'*+-/=?^_`{|}~@example.com")).toBe(
      "!#$%&'*+-/=?^_`{|}~@example.com",
    );
    expect(extractGmailStyleEmail("admin@localhost")).toBe("admin@localhost");
    expect(extractGmailStyleEmail("user@[IPv6:2001:db8::1]")).toBe(
      "user@[IPv6:2001:db8::1]",
    );
  });

  test("handles complex cases", () => {
    expect(extractGmailStyleEmail("Dan <<EMAIL>> <<EMAIL>>")).toBe(
      "<EMAIL>",
    );

    expect(extractGmailStyleEmail("Dan <>")).toBe("Dan <>");

    expect(extractGmailStyleEmail("Dan <<EMAIL>")).toBe(
      "<EMAIL>",
    );
    expect(extractGmailStyleEmail("Dan <EMAIL>>")).toBe(
      "<EMAIL>",
    );
  });

  test("handles edge cases", () => {
    expect(extractGmailStyleEmail("")).toBe("");

    expect(extractGmailStyleEmail("Dan Lee")).toBe("Dan Lee");

    expect(extractGmailStyleEmail("Dan & Bob <<EMAIL>>")).toBe(
      "<EMAIL>",
    );
  });
});
