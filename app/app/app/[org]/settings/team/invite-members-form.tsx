import React from "react";
import { EmailTagsInput } from "./email-tags-input";
import { GroupSelector } from "./group-selector";
import { NO_GROUP_ID } from "./constants";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { pluralize } from "#/utils/plurals";
import { invokeServerAction } from "#/utils/invoke-server-action";
import type { addMembers } from "#/pages/api/organization/member_actions";

interface InviteMembersFormProps
  extends InviteMembersFormState,
    InviteMembersFormActions {
  className?: string;
  size?: "default" | "lg";
  orgName: string;
}

export function InviteMembersForm({
  inputText,
  newMembers,
  conflictingUser,
  invalidUserEmail,
  selectedGroups,
  setInputText,
  addMember,
  deleteMember,
  setSelectedGroups,
  validateEmail,
  clearValidationErrors,
  className = "",
  size,
  orgName,
}: InviteMembersFormProps) {
  const handleGroupChange = (groupId: string, groupLabel: string) => {
    const isNoGroupSelected = selectedGroups.some(
      (g) => g.groupId === NO_GROUP_ID,
    );
    const isCurrentGroupNoGroup = groupId === NO_GROUP_ID;

    if (isNoGroupSelected !== isCurrentGroupNoGroup) {
      setSelectedGroups([{ groupId, groupLabel }]);
      return;
    }

    const isAlreadySelected = selectedGroups.some((g) => g.groupId === groupId);

    if (isAlreadySelected) {
      setSelectedGroups(selectedGroups.filter((g) => g.groupId !== groupId));
    } else {
      setSelectedGroups([...selectedGroups, { groupId, groupLabel }]);
    }
  };

  return (
    <div className={`flex flex-col space-y-4 ${className}`}>
      <EmailTagsInput
        emails={newMembers}
        inputText={inputText}
        onInputChange={setInputText}
        onAddEmail={addMember}
        onDeleteEmail={deleteMember}
        onValidateEmail={validateEmail}
        onClearValidation={clearValidationErrors}
        placeholder="Enter email addresses"
        size={size}
      />

      {conflictingUser && (
        <div className="text-sm text-bad-700">
          {conflictingUser} already exists
        </div>
      )}

      {invalidUserEmail && (
        <div className="text-sm text-bad-700">
          {invalidUserEmail} is not a valid email address
        </div>
      )}

      <GroupSelector
        selectedGroups={selectedGroups}
        onGroupChange={handleGroupChange}
        placeholder="Select permission groups"
        size={size}
        orgName={orgName}
      />
    </div>
  );
}

const isDefinitelyNotEmail = (email: string) => {
  // Just check for an @ symbol and a dot followed by some characters
  return !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
};

const findInvalidEmail = (text: string) => {
  const emailsToFind = text
    .split(",")
    .map((t) => t.trim())
    .map(extractGmailStyleEmail)
    .filter(Boolean);
  return emailsToFind.find((e) => isDefinitelyNotEmail(e));
};

export interface InviteMembersFormState {
  inputText: string;
  newMembers: string[];
  conflictingUser: string;
  invalidUserEmail: string;
  selectedGroups: { groupId: string; groupLabel: string }[];
  isInviteInProgress: boolean;
}

export interface InviteMembersFormActions {
  setInputText: (text: string) => void;
  addMember: (text: string) => void;
  deleteMember: (email: string) => void;
  setSelectedGroups: (
    groups: { groupId: string; groupLabel: string }[],
  ) => void;
  setIsInviteInProgress: (loading: boolean) => void;
  validateEmail: (text: string) => void;
  clearValidationErrors: () => void;
  reset: () => void;
  getAllEmails: () => string[];
  isFormValid: () => boolean;
}

export function useInviteMembersForm(opts: {
  findExistingEmail?: (user: string) => string | undefined;
  initialInputText?: string;
}): InviteMembersFormState & InviteMembersFormActions {
  const { findExistingEmail, initialInputText } = opts;
  const [inputText, setInputText] = useState(initialInputText ?? "");
  const [newMembers, setNewMembers] = useState<string[]>([]);
  const [conflictingUser, setConflictingUser] = useState<string>("");
  const [invalidUserEmail, setInvalidUserEmail] = useState<string>("");
  const [selectedGroups, setSelectedGroups] = useState<
    { groupId: string; groupLabel: string }[]
  >([]);
  const [isInviteInProgress, setIsInviteInProgress] = useState(false);

  const addMember = useCallback((text: string) => {
    const newMembersFromInputText = text
      .split(",")
      .map((t) => t.trim())
      .map(extractGmailStyleEmail)
      .filter(Boolean);
    setNewMembers((prev) => [...prev, ...newMembersFromInputText]);
  }, []);

  const deleteMember = useCallback((emailToDelete: string) => {
    setNewMembers((prev) => {
      const idx = prev.findIndex((email) => email === emailToDelete);
      return [...prev.slice(0, idx), ...prev.slice(idx + 1)];
    });
  }, []);

  const validateEmail = useCallback(
    (text: string) => {
      const existingEmail = findExistingEmail?.(text);
      if (existingEmail) {
        setConflictingUser(existingEmail);
        return;
      }
      const invalidEmail = findInvalidEmail(text);
      if (invalidEmail) {
        setInvalidUserEmail(invalidEmail);
        return;
      }
    },
    [findExistingEmail],
  );

  const clearValidationErrors = useCallback(() => {
    setConflictingUser("");
    setInvalidUserEmail("");
  }, []);

  const reset = useCallback(() => {
    setInputText("");
    setNewMembers([]);
    setSelectedGroups([]);
    setConflictingUser("");
    setInvalidUserEmail("");
    setIsInviteInProgress(false);
  }, []);

  const getAllEmails = useCallback(() => {
    let newMembersFromInputText: string[] = [];
    if (inputText !== "") {
      newMembersFromInputText = inputText
        .split(",")
        .map((t) => t.trim())
        .map(extractGmailStyleEmail)
        .filter(Boolean);
    }

    return Array.from(new Set([...newMembers, ...newMembersFromInputText]));
  }, [inputText, newMembers]);

  const isFormValid = useCallback(() => {
    const isNewMemberEmpty = newMembers.length === 0 && !inputText.trim();
    const isConflictingUser = conflictingUser !== "";
    const isInvalidUserEmail = invalidUserEmail !== "";

    return !(
      isNewMemberEmpty ||
      selectedGroups.length === 0 ||
      isConflictingUser ||
      isInvalidUserEmail
    );
  }, [
    newMembers,
    inputText,
    selectedGroups,
    conflictingUser,
    invalidUserEmail,
  ]);

  return {
    // State
    inputText,
    newMembers,
    conflictingUser,
    invalidUserEmail,
    selectedGroups,
    isInviteInProgress,
    // Actions
    setInputText,
    addMember,
    deleteMember,
    setSelectedGroups,
    setIsInviteInProgress,
    validateEmail,
    clearValidationErrors,
    reset,
    getAllEmails,
    isFormValid,
  };
}

export const extractGmailStyleEmail = (input: string): string => {
  // Look for Gmail-style email addresses (e.g. "Dan Lee <<EMAIL>>")
  const emailRegex = /[^<]*<([^>]+@[^>]+)>|([^<>\s]+@[^<>\s]+)/;
  const match = input.match(emailRegex);

  return match?.[1] || match?.[2] || input;
};

export async function addUsersToOrg({
  orgId,
  orgName,
  emails,
  refresh,
  groupIds,
  getToken,
}: {
  orgId: string | undefined;
  groupIds?: string[];
  orgName: string;
  emails: string[];
  refresh: () => void;
  getToken: () => Promise<string | null>;
}) {
  if (!(orgId && orgName && emails.length)) return;
  try {
    const res = await invokeServerAction<typeof addMembers>({
      fName: "addMembers",
      args: {
        orgId,
        users: {
          emails,
          send_invite_emails: true,
          group_ids: groupIds
            ? groupIds.some((g) => g === NO_GROUP_ID)
              ? undefined
              : groupIds
            : undefined,
          group_names: groupIds ? undefined : ["Owners"],
        },
      },
      getToken,
    });
    refresh();
    if (res.send_email_error) {
      toast.warning(
        `Users successfully added, but failed to send invitation emails`,
        {
          description: res.send_email_error,
        },
      );
    } else {
      toast.success(
        res.added_users.length === 1
          ? `Invitation sent to ${res.added_users[0].email}`
          : `Invited ${res.added_users.length} members`,
      );
    }
  } catch (error) {
    const userNoun = pluralize(emails.length, "user", "users");
    toast.error(`Could not add ${userNoun}`, { description: `${error}` });
  }
}
