import React from "react";
import { But<PERSON> } from "#/ui/button";
import { AddToGroupCombobox } from "./add-to-group-combobox";
import { cn } from "#/utils/classnames";

interface GroupSelectorProps {
  selectedGroups: { groupId: string; groupLabel: string }[];
  onGroupChange: (groupId: string, groupLabel: string) => void;
  placeholder?: string;
  children?: React.ReactNode;
  size?: "default" | "lg";
  orgName: string;
}

export function GroupSelector({
  selectedGroups,
  onGroupChange,
  placeholder = "Select permission groups",
  size = "default",
  orgName,
}: GroupSelectorProps) {
  return (
    <AddToGroupCombobox
      selectedGroups={selectedGroups.map((g) => g.groupId)}
      onChange={onGroupChange}
      orgName={orgName}
    >
      <Button
        isDropdown
        className={cn(
          "flex-1 justify-start overflow-hidden pr-3 pl-2 text-left",
          { "h-9 pl-3 text-base": size === "lg" },
        )}
      >
        {selectedGroups.length === 0 ? (
          <span className="flex-1 truncate text-primary-500">
            {placeholder}
          </span>
        ) : (
          <span className="flex-1 truncate font-normal">
            {selectedGroups.map((g) => g.groupLabel).join(", ")}
          </span>
        )}
      </Button>
    </AddToGroupCombobox>
  );
}
