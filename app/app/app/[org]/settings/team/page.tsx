import { getOrganization } from "#/app/app/actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { type Permission } from "@braintrust/typespecs";
import ClientPage from "./clientpage";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";

export default async function Page(props: {
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const org = await getOrganization({ org_name });

  let orgPermissions: Permission[] = [];
  let orgMemberPermissions: Permission[] = [];

  try {
    const [orgPerms, orgMemberPerms] = await Promise.all([
      getObjectAclPermissions({
        objectType: "organization",
        objectId: org?.id,
      }),
      getObjectAclPermissions({
        objectType: "org_member",
        objectId: org?.id,
      }),
    ]);

    orgPermissions = orgPerms ?? [];
    orgMemberPermissions = orgMemberPerms ?? [];
  } catch (e) {
    console.error("Failed to get permissions on team page", e);
  }

  return (
    <ClientPage
      orgPermissions={orgPermissions}
      orgMemberPermissions={orgMemberPermissions}
    />
  );
}
