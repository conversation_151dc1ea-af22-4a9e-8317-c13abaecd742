"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { type fetchOrgGroups } from "#/utils/org-groups";
import { useQueryFunc } from "#/utils/react-query";
import { Combobox } from "#/ui/combobox/combobox";
import { type getGroupRoleDescriptions } from "../groups/group-actions";
import { NO_GROUP_ID } from "./constants";

interface GroupItem {
  value: string;
  label: string;
  description?: string;
  userId?: string | null;
}

function isSystemGroup(group: GroupItem): boolean {
  return group.userId === null;
}

export function sortGroups(a: GroupItem, b: GroupItem) {
  const isASystemGroup = isSystemGroup(a);
  const isBSystemGroup = isSystemGroup(b);

  // System groups go at the top, alphabetically by label, with 'No permission group' last
  if (isASystemGroup && isBSystemGroup) {
    if (a.value === NO_GROUP_ID && b.value !== NO_GROUP_ID) {
      return 1;
    } else if (a.value !== NO_GROUP_ID && b.value === NO_GROUP_ID) {
      return -1;
    } else {
      return a.label.localeCompare(b.label);
    }
  } else if (isASystemGroup && !isBSystemGroup) {
    return -1;
  } else if (!isASystemGroup && isBSystemGroup) {
    return 1;
  }

  // For all user created groups, sort alphabetically by label
  return a.label.localeCompare(b.label);
}

export function AddToGroupCombobox({
  selectedGroups,
  onChange,
  children,
  orgName,
}: {
  selectedGroups: string[] | null;
  onChange: (groupId: string, groupLabel: string) => void;
  children: React.ReactNode;
  orgName: string;
}) {
  const { data: groupsData, isLoading: isLoadingGroups } = useQueryFunc<
    typeof fetchOrgGroups
  >({
    fName: "fetchOrgGroups",
    args: { orgName },
  });

  const {
    data: groupRoleDescriptions,
    isLoading: isLoadingGroupRoleDescriptions,
  } = useQueryFunc<typeof getGroupRoleDescriptions>({
    fName: "getGroupRoleDescriptions",
    args: { orgName },
  });

  if (isLoadingGroups || !groupsData || isLoadingGroupRoleDescriptions) {
    return (
      <Button isLoading isDropdown className="justify-between py-2">
        Select permission groups
      </Button>
    );
  }

  const groupItems = Object.values(groupsData).map((group) => ({
    value: group.id,
    label: group.name,
    description: group.description || groupRoleDescriptions?.[group.id],
    userId: group.user_id,
  }));

  groupItems.push({
    value: NO_GROUP_ID,
    label: "No permission group",
    description: "Invite someone now, but set up their permissions later.",
    userId: null,
  });

  return (
    <Combobox<{
      label: string;
      value: string;
      description?: string | null;
    }>
      stayOpenOnChange
      modal
      contentWidth={462}
      variant="button"
      searchPlaceholder="Find permission group"
      buttonClassName="px-3"
      placeholderLabel="Select permission groups"
      placeholderClassName="w-full text-left font-normal"
      selectedValues={selectedGroups ?? undefined}
      onChange={(groupId, option) => onChange(groupId ?? "", option.label)}
      options={groupItems.sort(sortGroups)}
      renderOptionLabel={(option) => {
        return (
          <span>
            <span className="block text-sm font-medium">{option.label}</span>
            {option.description && (
              <span className="pt-1.5 text-xs text-primary-600">
                {option.description}
              </span>
            )}
          </span>
        );
      }}
    >
      {children}
    </Combobox>
  );
}
