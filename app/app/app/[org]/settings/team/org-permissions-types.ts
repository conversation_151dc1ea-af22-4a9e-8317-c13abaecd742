import { z } from "zod";

import {
  type PermissionsRequirement,
  crudPermissions,
  manageAccessPermissions,
  permissionsCheckboxSchema,
  permissionsRequirementKeyStr,
} from "#/ui/permissions/permissions-types";

export const orgPermissionsCheckboxesSchema = z.strictObject({
  project: z.strictObject({
    create: permissionsCheckboxSchema,
    read: permissionsCheckboxSchema,
    update: permissionsCheckboxSchema,
    delete: permissionsCheckboxSchema,
    manageAccess: permissionsCheckboxSchema,
  }),
  organization: z.strictObject({
    manageSettings: permissionsCheckboxSchema,
    inviteMembers: permissionsCheckboxSchema,
    removeMembers: permissionsCheckboxSchema,
    manageAccess: permissionsCheckboxSchema,
  }),
});

export type OrgPermissionsCheckboxes = z.infer<
  typeof orgPermissionsCheckboxesSchema
>;

export const orgPermissionsRequirements: Record<
  string,
  PermissionsRequirement
> = {
  ...Object.fromEntries(
    crudPermissions.map((p) => [
      permissionsRequirementKeyStr("project", p),
      {
        objectType: "org_project",
        permissions: [{ permission: p }],
      },
    ]),
  ),
  [permissionsRequirementKeyStr("project", "manageAccess")]: {
    objectType: "org_project",
    permissions: manageAccessPermissions.map((p) => ({ permission: p })),
  },
  [permissionsRequirementKeyStr("organization", "manageSettings")]: {
    objectType: "organization",
    permissions: [{ permission: "update", restrictObjectType: "organization" }],
  },
  [permissionsRequirementKeyStr("organization", "inviteMembers")]: {
    objectType: "organization",
    permissions: [{ permission: "create", restrictObjectType: "org_member" }],
  },
  [permissionsRequirementKeyStr("organization", "removeMembers")]: {
    objectType: "organization",
    permissions: [{ permission: "delete", restrictObjectType: "org_member" }],
  },
  [permissionsRequirementKeyStr("organization", "manageAccess")]: {
    objectType: "organization",
    permissions: [
      ...manageAccessPermissions.map(
        (p) =>
          ({
            permission: p,
          }) as const,
      ),
      ...crudPermissions.map(
        (p) =>
          ({
            permission: p,
            restrictObjectType: "group",
          }) as const,
      ),
    ],
  },
};
