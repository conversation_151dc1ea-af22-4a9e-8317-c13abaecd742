import * as Alert from "#/ui/alert-dialog";
import Token from "#/ui/token";

export const CreatedApiKeyDialog = ({
  lastCreatedKey,
  setLastCreatedKey,
}: {
  lastCreatedKey: string | null;
  setLastCreatedKey: (key: string | null) => void;
}) => {
  return (
    lastCreatedKey && (
      <>
        <Alert.AlertDialog open={!!lastCreatedKey}>
          <Alert.AlertDialogContent className="max-w-3xl">
            <Alert.AlertDialogHeader>
              <Alert.AlertDialogTitle>API key created</Alert.AlertDialogTitle>
              <Alert.AlertDialogDescription>
                Please save this API key somewhere safe and accessible. For
                security reasons, you will not be able to view it again. If you
                lose this API key, you must generate a new one.
              </Alert.AlertDialogDescription>
            </Alert.AlertDialogHeader>
            <Token token={lastCreatedKey} />
            <Alert.AlertDialogFooter>
              <Alert.AlertDialogAction
                onClick={() => {
                  setLastCreatedKey(null);
                }}
              >
                Close
              </Alert.AlertDialogAction>
            </Alert.AlertDialogFooter>
          </Alert.AlertDialogContent>
        </Alert.AlertDialog>
      </>
    )
  );
};
