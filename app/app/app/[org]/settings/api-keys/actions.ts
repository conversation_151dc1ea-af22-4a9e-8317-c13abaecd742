"use server";

import { type AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { type ApiKey, apiKeySchema } from "@braintrust/typespecs";
import { deleteObjects } from "#/pages/api/_object_crud_util";
import {
  makeApiKeyFullResultSetQuery,
  makeOwnerApiKeysFullResultSetQuery,
  sanitizeApiKey,
} from "#/pages/api/apikey/_util";

export async function fetchApiKeys(
  {},
  authLookupRaw?: AuthLookup,
): Promise<ApiKey[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query, queryParams } = makeApiKeyFullResultSetQuery({ authLookup });
  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return apiKeySchema.array().parse(rows.map(sanitizeApiKey));
}

export type DeleteApiKeyInput = { api_key_id: string };

export async function deleteApiKey(
  { api_key_id }: DeleteApiKeyInput,
  authLookupRaw?: AuthLookup,
): Promise<null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query, queryParams, notFoundErrorMessage } =
    makeApiKeyFullResultSetQuery({
      authLookup,
      id: [api_key_id],
    });
  await deleteObjects({
    fullResultsQueryOverride: query,
    baseTableOverride: "api_key",
    startingParams: queryParams,
    fullResultsSize: 1,
    notFoundErrorMessage,
  });
  return null;
}

export async function deleteApiKeyAsOrgOwner(
  { api_key_id, org_id }: { api_key_id: string; org_id: string },
  authLookupRaw?: AuthLookup,
): Promise<null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query, queryParams, notFoundErrorMessage } =
    makeOwnerApiKeysFullResultSetQuery({
      authLookup,
      org_id,
      id: api_key_id,
      user_type: "user",
    });
  await deleteObjects({
    fullResultsQueryOverride: query,
    baseTableOverride: "api_key",
    startingParams: queryParams,
    fullResultsSize: 1,
    notFoundErrorMessage,
  });
  return null;
}

export async function fetchApiKeysAsOrgOwner(
  { org_id }: { org_id: string },
  authLookupRaw?: AuthLookup,
): Promise<ApiKey[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const supabase = getServiceRoleSupabase();

  const { query, queryParams } = makeOwnerApiKeysFullResultSetQuery({
    authLookup,
    org_id,
    user_type: "user",
  });

  const { rows } = await supabase.query(query, queryParams.params);
  return apiKeySchema.array().parse(rows.map(sanitizeApiKey));
}
