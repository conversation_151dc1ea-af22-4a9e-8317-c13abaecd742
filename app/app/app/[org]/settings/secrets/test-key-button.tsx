import { But<PERSON> } from "#/ui/button";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { testKey } from "./utils";

export const TestKeyButton = ({
  provider,
  apiBase,
  secret,
  model,
}: {
  provider: string;
  apiBase?: string;
  secret: string;
  model?: string;
}) => {
  const [loading, setLoading] = useState(false);

  const testConnection = useCallback(async () => {
    setLoading(true);
    try {
      const resp = await testKey({
        provider,
        api_key: secret,
        model,
        ...(apiBase ? { api_base: apiBase } : {}),
      });
      if (!resp?.ok) {
        const responseBody = await resp?.json();
        toast.error("Error validating API key", {
          description:
            responseBody.error?.message ??
            responseBody.error ??
            responseBody.message ??
            responseBody.detail ??
            `${resp?.status}: ${resp?.statusText}`,
        });
      } else {
        toast.success("API key validated");
      }
    } catch (err) {
      toast.error("Error validating API key", {
        description: err instanceof Error ? err.message : "Unknown error",
      });
    }

    setLoading(false);
  }, [provider, secret, apiBase, model]);

  return (
    <Button
      disabled={!secret || secret === "*".repeat(64)}
      size="sm"
      type="button"
      isLoading={loading}
      onClick={testConnection}
    >
      Test key
    </Button>
  );
};
