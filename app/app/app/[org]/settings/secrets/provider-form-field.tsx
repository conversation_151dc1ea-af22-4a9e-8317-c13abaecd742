import { cn } from "#/utils/classnames";
import type { PropsWithChildren, ReactNode } from "react";

export const ProviderFormField = ({
  htmlFor,
  error,
  label,
  labelClassName,
  children,
  helpText,
  containerClassName,
}: PropsWithChildren<{
  htmlFor?: string;
  error?: ReactNode;
  helpText?: ReactNode;
  label: ReactNode;
  labelClassName?: string;
  containerClassName?: string;
}>) => {
  return (
    <div className={cn("flex items-start gap-3", containerClassName)}>
      <label
        htmlFor={htmlFor}
        className={cn(
          "flex min-h-8 w-1/3 items-center justify-end text-right text-sm font-medium",
          labelClassName,
        )}
      >
        {label}
      </label>
      <div className="flex-1">
        {children}
        {helpText && (
          <div className="py-1 text-xs text-primary-600">{helpText}</div>
        )}
        {error && <div className="py-1 text-xs text-bad-600">{error}</div>}
      </div>
    </div>
  );
};
