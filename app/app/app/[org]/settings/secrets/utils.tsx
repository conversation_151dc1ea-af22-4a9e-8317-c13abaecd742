import { formatCurrency } from "#/ui/cost-input";
import {
  Anthropic,
  Azure,
  Baseten,
  Bedrock,
  Cerebras,
  Databricks,
  Fireworks,
  Gemini,
  GoogleCloud,
  Groq,
  Lepton,
  Mistral,
  Ollama,
  OpenAI,
  Perplexity,
  Replicate,
  Together,
  XAI,
} from "#/ui/icons/providers";
import { isEmpty } from "#/utils/object";
import {
  EndpointProviderToBaseURL,
  ModelEndpointType,
  modelProviderHasReasoning,
  ModelSchema,
  type ModelSpec,
} from "@braintrust/proxy/schema";
import { Box, Chrome } from "lucide-react";
import { z } from "zod";

export const checkValidateAndParse = (
  provider: string,
): ModelEndpointType | undefined => {
  switch (provider) {
    case "openai":
    case "anthropic":
    case "google":
    case "groq":
    case "xAI":
    case "mistral":
    case "together":
    case "fireworks":
    case "perplexity":
    case "cerebras":
      return modelEndpointTypeSchema.parse(provider);
    case "replicate":
    case "ollama":
    case "lepton":
    case "bedrock":
    case "databricks":
    case "azure":
    case "vertex":
    case "js":
    default:
      return undefined;
  }
};

const modelEndpointTypeSchema = z.enum(ModelEndpointType);

export const CustomModelFormSchema = ModelSchema.pick({
  format: true,
  flavor: true,
  multimodal: true,
  displayName: true,
  locations: true,
  o1_like: true,
  reasoning: true,
  reasoning_budget: true,
  description: true,
}).merge(
  z.object({
    modelName: z.string().min(1, "Name cannot be empty"),
    input_cost_per_mil_tokens: z.string().nullish(),
    output_cost_per_mil_tokens: z.string().nullish(),
  }),
);

export const CustomModelFormArraySchema = z.array(CustomModelFormSchema);
type CustomModelFormArray = z.infer<typeof CustomModelFormArraySchema>;

export const providerReadableName = (name: string): string => {
  switch (name) {
    case "openai":
      return "OpenAI";
    case "together":
      return "Together.ai";
    case "google":
      return "Gemini";
    case "vertex":
      return "Vertex AI";
    case "azure_entra":
      return "Azure (Entra)";
    case "xAI":
      return "xAI";
    default:
      return `${name.charAt(0).toUpperCase()}${
        name.length > 1 ? name.substring(1) : ""
      }`;
  }
};

export const getProviderConfig = (name: string) => {
  if (name.toLowerCase().startsWith("openai")) {
    return {
      Icon: OpenAI,
      docsUrl: "/docs/providers/openai",
    };
  }
  if (name.toLowerCase().startsWith("azure")) {
    return {
      Icon: Azure,
      docsUrl: "/docs/providers/azure",
    };
  }
  if (name.toLowerCase().startsWith("google")) {
    return {
      Icon: Gemini,
      docsUrl: "/docs/providers/google",
    };
  }
  if (name.toLowerCase().startsWith("perplexity")) {
    return {
      Icon: Perplexity,
      docsUrl: "/docs/providers/perplexity",
    };
  }
  if (
    name.toLowerCase().startsWith("magistral") ||
    name.toLowerCase().startsWith("mistral") ||
    name.toLowerCase().startsWith("pixtral") ||
    name.toLowerCase().startsWith("devstral")
  ) {
    return {
      Icon: Mistral,
      docsUrl: "/docs/providers/mistral",
    };
  }
  if (name.toLowerCase().startsWith("anthropic")) {
    return {
      Icon: Anthropic,
      docsUrl: "/docs/providers/anthropic",
    };
  }
  if (name.toLowerCase().startsWith("replicate")) {
    return {
      Icon: Replicate,
      docsUrl: "/docs/providers/replicate",
    };
  }
  if (name.toLowerCase().startsWith("ollama")) {
    return {
      Icon: Ollama,
    };
  }
  if (name.toLowerCase().startsWith("groq")) {
    return {
      Icon: Groq,
      docsUrl: "/docs/providers/groq",
    };
  }
  if (name.toLowerCase().startsWith("together")) {
    return {
      Icon: Together,
      docsUrl: "/docs/providers/together",
    };
  }
  if (name.toLowerCase().startsWith("window")) {
    return {
      Icon: Chrome,
    };
  }
  if (
    name.toLowerCase().startsWith("bedrock") ||
    name.toLowerCase().includes("nova")
  ) {
    return {
      Icon: Bedrock,
      docsUrl: "/docs/providers/bedrock",
    };
  }
  if (name.toLowerCase().startsWith("vertex")) {
    return {
      Icon: GoogleCloud,
      docsUrl: "/docs/providers/vertex",
    };
  }
  if (name.toLowerCase().startsWith("lepton")) {
    return {
      Icon: Lepton,
      docsUrl: "/docs/providers/lepton",
    };
  }
  if (name.toLowerCase().startsWith("cerebras")) {
    return {
      Icon: Cerebras,
      docsUrl: "/docs/providers/cerebras",
    };
  }
  if (name.toLowerCase().startsWith("fireworks")) {
    return {
      Icon: Fireworks,
      docsUrl: "/docs/providers/fireworks",
    };
  }
  if (
    name.toLowerCase().startsWith("grok") ||
    name.toLowerCase().startsWith("xai")
  ) {
    return { Icon: XAI, docsUrl: "/docs/providers/xai" };
  }
  if (name.toLowerCase().startsWith("databricks")) {
    return {
      Icon: Databricks,
      docsUrl: "/docs/providers/databricks",
    };
  }
  if (name.toLowerCase().startsWith("baseten")) {
    return {
      Icon: Baseten,
      docsUrl: "/docs/providers/baseten",
    };
  }

  return { Icon: Box };
};

export function getDisplayName(fieldPath: string, provider: string) {
  const pathParts = fieldPath.split(".");
  const leafName = pathParts[pathParts.length - 1];
  switch (leafName) {
    case "api_base":
      if (provider === "bedrock") {
        return "Endpoint URL";
      } else {
        return "API base URL";
      }
    case "api_version":
      return "API version";
    case "organization_id":
      return "Organization ID";
    default:
      return leafName
        .split("_")
        .map((part) => part.substring(0, 1).toUpperCase() + part.substring(1))
        .join(" ");
  }
}

export function getPlaceholderName(fieldPath: string) {
  const pathParts = fieldPath.split(".");
  const leafName = pathParts[pathParts.length - 1];
  switch (leafName) {
    case "api_base":
      return "API base URL";
    case "api_version":
      return "API version";
    case "organization_id":
      return "organization ID";
    default:
      return leafName.split("_").join(" ");
  }
}

export function customModelArrayToObject(
  modelsArray: CustomModelFormArray,
): Record<string, ModelSpec> {
  return modelsArray.reduce(
    (
      acc,
      {
        modelName,
        input_cost_per_mil_tokens,
        output_cost_per_mil_tokens,
        ...model
      },
    ) => ({
      ...acc,
      [modelName]: {
        ...model,
        reasoning:
          model.reasoning ||
          model.o1_like ||
          modelProviderHasReasoning?.[model.format]?.test(modelName),
        input_cost_per_mil_tokens: input_cost_per_mil_tokens
          ? parseFloat(input_cost_per_mil_tokens.replaceAll("$", ""))
          : undefined,
        output_cost_per_mil_tokens: output_cost_per_mil_tokens
          ? parseFloat(output_cost_per_mil_tokens.replaceAll("$", ""))
          : undefined,
      },
    }),
    {},
  );
}

export function customModelObjectToArray(
  modelsObject: Record<string, ModelSpec>,
): CustomModelFormArray {
  return Object.entries(modelsObject).map(
    ([
      modelName,
      { input_cost_per_mil_tokens, output_cost_per_mil_tokens, ...spec },
    ]) => ({
      ...spec,
      modelName,
      reasoning:
        spec.reasoning ||
        spec.o1_like ||
        modelProviderHasReasoning?.[spec.format]?.test(modelName),
      input_cost_per_mil_tokens: !isEmpty(input_cost_per_mil_tokens)
        ? formatCurrency(input_cost_per_mil_tokens)
        : undefined,
      output_cost_per_mil_tokens: !isEmpty(output_cost_per_mil_tokens)
        ? formatCurrency(output_cost_per_mil_tokens)
        : null,
    }),
  );
}

export async function testKey({
  provider,
  api_key,
  model,
  api_base,
}: {
  provider: string;
  api_key: string;
  model?: string;
  api_base?: string;
}) {
  const parsedProvider = checkValidateAndParse(provider);
  // If no parsed provider, we want to skip validating the key
  if (!parsedProvider) {
    return new Response(null, { status: 200 });
  }

  const baseUrl = api_base ?? EndpointProviderToBaseURL[parsedProvider];
  if (!baseUrl) {
    throw new Error(`Invalid URL for provider: ${provider}`);
  }

  switch (parsedProvider) {
    case "perplexity":
    case "openai": {
      return fetch(`${baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${api_key}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "user",
              content: "Hello, how are you?",
            },
          ],
        }),
      });
    }
    case "together":
    case "groq":
    case "fireworks":
    case "xAI":
    case "cerebras":
    case "mistral": {
      return fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: { Authorization: `Bearer ${api_key}` },
      });
    }

    case "anthropic": {
      return fetch(`${baseUrl}/models`, {
        method: "GET",
        headers: {
          "x-api-key": api_key,
          "anthropic-version": "2023-06-01",
          "anthropic-dangerous-direct-browser-access": "true",
        },
      });
    }

    case "google": {
      return fetch(`${baseUrl}/models?key=${api_key}`, {
        method: "GET",
      });
    }
    default:
      return new Response(null, { status: 200 });
  }
}
