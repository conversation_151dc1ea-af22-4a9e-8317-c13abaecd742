"use client";

import { buttonVariants } from "#/ui/button";

import "react-image-crop/dist/ReactCrop.css";
import { type FeatureFlags, MinVersion } from "#/lib/feature-flags";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Switch } from "#/ui/switch";
import { cn } from "#/utils/classnames";

export const FeatureFlagToggle = ({
  flag,
  checked,
  disabled,
  isForced,
  setFlag,
  className,
  title,
  description,
}: {
  flag: keyof FeatureFlags;
  checked: boolean;
  disabled?: boolean;
  isForced?: boolean;
  setFlag: (flag: keyof FeatureFlags, checked: boolean) => void;
  className?: string;
  title: string;
  description?: string;
}) => {
  const { version: apiVersion } = useAPIVersion();

  return (
    <div
      className={cn(
        "flex items-baseline gap-3 text-sm",
        {
          "pointer-events-none opacity-50": isForced,
        },
        className,
      )}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            {/* Using <a> tag instead of <Button> to avoid hydration issues when nesting buttons */}
            <a
              className={cn(buttonVariants({ transparent: true }), "p-0")}
              onClick={() => {
                setFlag(flag, !checked);
              }}
            >
              <Switch
                id={flag}
                checked={checked}
                disabled={!checked && disabled}
              />
            </a>
          </div>
        </TooltipTrigger>
        {disabled && (
          <TooltipPortal>
            <TooltipContent>
              Your API ({apiVersion}) is behind the minimum required version (
              {MinVersion[flag]}) for this feature.
            </TooltipContent>
          </TooltipPortal>
        )}
      </Tooltip>
      <div>
        <label htmlFor={flag} className="block font-medium">
          {title}
        </label>
        {description && (
          <div className="pt-1 text-sm text-primary-600">{description}</div>
        )}
      </div>
    </div>
  );
};
