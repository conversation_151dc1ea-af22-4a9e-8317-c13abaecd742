"use server";

import { getServiceRoleSupabase } from "#/utils/supabase";
import { getServerAuthSession } from "#/utils/auth/server-session";

// RBAC_DISCLAIMER: Users can only update their own name information.
export async function updateUserName(
  firstName: string | null,
  lastName: string | null,
) {
  const session = await getServerAuthSession();
  if (!session || !session.loggedIn) {
    throw new Error("Unauthorized");
  }

  const supabase = getServiceRoleSupabase();

  await supabase.query(
    `
    UPDATE users
    SET given_name = $1, family_name = $2
    WHERE auth_id = $3
    `,
    [firstName, lastName, session.authId],
  );
}
