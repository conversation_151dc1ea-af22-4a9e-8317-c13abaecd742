"use client";

import { Ava<PERSON> } from "#/ui/avatar";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { useUser } from "#/utils/user";
import { useTheme } from "next-themes";
import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import ReactCrop, {
  type PixelCrop,
  centerCrop,
  makeAspectCrop,
  type Crop,
} from "react-image-crop";
import { updateUserName } from "./actions";
import { OrgUsersContext } from "#/utils/org-users-context";

import "react-image-crop/dist/ReactCrop.css";
import {
  type RenderOption,
  isAllDataRenderOption,
  renderOptions,
} from "#/utils/parse";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ToggleGroup, ToggleGroupItem } from "#/ui/toggle-group";
import { Skeleton } from "#/ui/skeleton";
import { Combobox } from "#/ui/combobox/combobox";
import { getAppearanceIcon } from "#/ui/layout/profile-menu";
import { toast } from "sonner";

const TO_RADIANS = Math.PI / 180;

// This is to demonstate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: "%",
        width: 50,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

// See https://github.com/pacocoursey/next-themes/issues/169
export default function Page() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex w-full max-w-md flex-col gap-2">
        <Skeleton className="h-20" />
        <Skeleton className="h-20" />
        <Skeleton className="h-20" />
      </div>
    );
  }

  return <PageImpl />;
}

function PageImpl() {
  const { user, invalidate: refreshUser } = useUser();

  const { theme, themes, setTheme } = useTheme();

  const [initialized, setInitialized] = useState(false);
  const [nameSaveState, setNameSaveState] = useState<
    "unsaved" | "saving" | "saved"
  >("saved");
  const [firstName, setFirstName] = useState<string | null>(null);
  const [lastName, setLastName] = useState<string | null>(null);

  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const inputFileRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (user) {
      setFirstName(user.given_name ?? null);
      setLastName(user.family_name ?? null);
      setAvatarUrl(user.avatar_url ?? null);
      setInitialized(true);
    }
  }, [user]);

  const { refreshOrgUsers } = useContext(OrgUsersContext);

  const saveName = async () => {
    setNameSaveState("saving");
    await updateUserName(firstName, lastName);
    refreshUser();
    refreshOrgUsers();
    setNameSaveState("saved");
  };

  const [file, setFile] = useState<File | null>(null);
  const [cropDialogOpen, setCropDialogOpen] = useState(false);
  const imgRef = useRef<HTMLImageElement | null>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();

  const [parseMode, onParseModeChange] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "data-view",
    key: "parseMode",
  });
  const allDataRenderOptions = Object.entries(renderOptions).filter(
    ([_, option]) => isAllDataRenderOption(option),
  );
  const [_, currentParseOption] =
    allDataRenderOptions.find(([key]) => key === parseMode) || [];
  const [__, defaultParseOption] = allDataRenderOptions[0];
  const currentParseOptionLabel =
    currentParseOption?.name || defaultParseOption?.name;

  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const { width, height } = e.currentTarget;
    setCrop(centerAspectCrop(width, height, 1));
  }

  const [imgSavingState, setImgSavingState] = useState<"saving" | "saved">(
    "saved",
  );

  const imgUrl = useMemo(() => {
    if (!file) {
      return null;
    }
    return URL.createObjectURL(file);
  }, [file]);

  // https://dev.to/deepakjaiswal/convert-canvas-to-image-4l1j
  async function onDownloadCropClick() {
    const image = imgRef.current;
    if (!image || !completedCrop) {
      throw new Error("Image does not exist");
    }

    setImgSavingState("saving");

    try {
      const crop = completedCrop;

      const canvas = document.createElement("canvas");
      const scale = 1;
      const rotate = 0;
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        throw new Error("No 2d context");
      }

      // This will size relative to the uploaded image
      // size. If you want to size according to what they
      // are looking at on screen, remove scaleX + scaleY
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      const pixelRatio = window.devicePixelRatio;
      canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
      canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

      ctx.scale(pixelRatio, pixelRatio);
      ctx.imageSmoothingQuality = "high";
      const cropX = crop.x * scaleX;
      const cropY = crop.y * scaleY;

      const rotateRads = rotate * TO_RADIANS;
      const centerX = image.naturalWidth / 2;
      const centerY = image.naturalHeight / 2;
      ctx.save();
      ctx.translate(-cropX, -cropY);
      ctx.translate(centerX, centerY);
      ctx.rotate(rotateRads);
      ctx.scale(scale, scale);
      ctx.translate(-centerX, -centerY);
      ctx.drawImage(
        image,
        0,
        0,
        image.naturalWidth,
        image.naturalHeight,
        0,
        0,
        image.naturalWidth,
        image.naturalHeight,
      );

      const blob: Blob | null = await new Promise((resolve) => {
        canvas.toBlob(
          (blob) => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            (blob as any).name = "avatar.jpeg";
            resolve(blob);
          },
          "image/jpeg",
          0.9,
        );
      });

      if (!blob) {
        throw new Error("No blob");
      }

      const response = await fetch(`/api/avatar/upload?filename=avatar.png`, {
        method: "POST",
        body: blob,
      });
      if (!response.ok) {
        let msg = `Failed to upload avatar: ${response.statusText}`;
        if (response.status === 413) {
          msg = `Failed to upload: max file size is 4.5MB. Please try again.`;
        }
        toast.error(msg);
        return;
      }

      refreshUser();
      setImgSavingState("saved");
      setCropDialogOpen(false);
      if (inputFileRef.current) {
        inputFileRef.current.value = "";
      }
    } catch (e) {
      toast.error(`Failed to upload avatar`, {
        description: `${e}`,
      });
    }
  }

  return (
    <div className="flex flex-col gap-8">
      {initialized && (
        <div>
          <h2 className="mb-4 text-lg font-semibold">Profile</h2>
          <div className="flex flex-col gap-2">
            <div className="text-sm text-primary-800">Name</div>
            <div className="mb-2 flex gap-2">
              <PlainInput
                value={firstName ?? undefined}
                placeholder="First name"
                onChange={(e) => {
                  setFirstName(e.target.value);
                  setNameSaveState("unsaved");
                }}
              />
              <PlainInput
                value={lastName ?? undefined}
                placeholder="Last name"
                onChange={(e) => {
                  setLastName(e.target.value);
                  setNameSaveState("unsaved");
                }}
              />
              <Button
                onClick={saveName}
                disabled={
                  nameSaveState === "saving" ||
                  nameSaveState === "saved" ||
                  (user?.given_name == firstName &&
                    user?.family_name == lastName)
                }
              >
                Save
              </Button>
            </div>
            <div className="text-sm text-primary-800">Avatar</div>

            <form>
              <label htmlFor="file">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    setFile(null);
                    inputFileRef.current?.click();
                  }}
                >
                  <Avatar imgUrl={avatarUrl} size="md" />
                </button>
              </label>
              <input
                id="avatarFile"
                name="file"
                ref={inputFileRef}
                type="file"
                className="hidden"
                onChange={async (e) => {
                  e.preventDefault();
                  const file = e.target.files?.[0];
                  if (!file) {
                    toast.error("No file selected");
                    return;
                  }
                  setFile(file);
                  setCropDialogOpen(true);
                }}
              />
            </form>
          </div>
        </div>
      )}

      <Dialog open={cropDialogOpen} onOpenChange={setCropDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Crop avatar</DialogTitle>
            <DialogDescription>Crop your avatar to a square.</DialogDescription>
          </DialogHeader>
          {imgUrl && (
            <ReactCrop
              crop={crop}
              aspect={1}
              onChange={(_c, pc) => {
                setCrop(pc);
              }}
              minHeight={100}
              onComplete={(c) => {
                setCompletedCrop(c);
              }}
            >
              <img
                autoFocus
                src={imgUrl}
                alt="avatar"
                onLoad={onImageLoad}
                ref={imgRef}
              />
            </ReactCrop>
          )}

          <DialogFooter>
            <div className="flex grow flex-row justify-between">
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  setCropDialogOpen(false);
                  if (inputFileRef.current) {
                    inputFileRef.current.value = "";
                  }
                }}
              >
                Cancel
              </Button>
              <Button
                color="primary"
                onClick={(e) => {
                  e.preventDefault();
                  onDownloadCropClick();
                }}
              >
                {imgSavingState === "saved" ? "Save" : "Saving..."}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <div className="text-sm text-primary-800">Appearance</div>
          <ToggleGroup
            type="single"
            onValueChange={setTheme}
            value={theme}
            className="flex w-full max-w-sm"
          >
            {themes.map((option) => (
              <ToggleGroupItem
                className="flex-1 gap-2 border text-sm capitalize"
                key={option}
                value={option}
              >
                {getAppearanceIcon(option)}
                {option}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>

        <div className="flex w-full max-w-sm flex-col gap-2">
          <div className="text-sm text-primary-800">Default data view</div>
          <Combobox
            noSearch
            placeholderLabel={
              <span className="text-sm font-medium">
                {currentParseOptionLabel}
              </span>
            }
            renderOptionLabel={(option) => (
              <span className="text-sm">{option.label}</span>
            )}
            contentClassName="w-[384px] font-inter"
            buttonClassName="px-3"
            placeholderClassName="text-left flex-1"
            variant="button"
            selectedValue={currentParseOptionLabel}
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            onChange={(v) => onParseModeChange(v as RenderOption)}
            options={allDataRenderOptions.map(([key, option]) => ({
              value: key,
              label: option.name,
            }))}
          />
        </div>
      </div>
    </div>
  );
}
