import { But<PERSON> } from "#/ui/button";
import { BasicTooltip } from "#/ui/tooltip";
import { renderHotkey } from "#/utils/hotkeys";
import { Search } from "lucide-react";
import { useSet<PERSON>tom } from "jotai";
import { commandBar<PERSON><PERSON><PERSON>tom } from "./sidenav-state";

export const ToggleCommandBar = () => {
  const setCommandBarOpen = useSetAtom(commandBarOpenAtom);

  return (
    <BasicTooltip
      tooltipContent={
        <>
          Command
          <span className="ml-2.5 inline-block opacity-50">
            {renderHotkey("Mod+k")}
          </span>
        </>
      }
    >
      <Button
        size="icon"
        variant="ghost"
        className="size-8 flex-none p-0 text-primary-500 hover:text-primary-950"
        onClick={() => {
          setCommandBarOpen(true);
        }}
      >
        <Search className="size-3.5" />
      </Button>
    </BasicTooltip>
  );
};
