import { useEntityStorage } from "#/lib/clientDataStorage";
import { useOrg } from "#/utils/user";
import { atom, useAtom, useAtomValue } from "jotai";

export const sidenavStateAtom = atom<
  "collapsed" | "docked" | "floating-closed" | "floating-open"
>("collapsed");

export const commandBarOpenAtom = atom(false);

const isSidenavDockedAtom = atom((get) => get(sidenavStateAtom) === "docked");
export const useIsSidenavDocked = () => {
  const org = useOrg();
  return useAtomValue(isSidenavDockedAtom) && !!org.id;
};

export const useToggleSidenavState = () => {
  const [sidenavState, setSidenavState] = useAtom(sidenavStateAtom);

  const [_, setDidUserCollapseSidenav] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "app",
    key: "isSidenavCollapsed",
  });

  return () => {
    if (sidenavState === "floating-closed") {
      setSidenavState("floating-open");
    } else if (sidenavState === "floating-open") {
      setSidenavState("floating-closed");
    } else if (sidenavState === "docked") {
      setSidenavState("collapsed");
      setDidUserCollapseSidenav(true);
    } else {
      setSidenavState("docked");
      setDidUserCollapseSidenav(false);
    }
  };
};
