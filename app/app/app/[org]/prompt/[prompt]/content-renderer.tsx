import React from "react";
import { HelpTip } from "#/ui/help-tip";
import { ExternalLink } from "#/ui/link";
import { safeParseImage } from "#/ui/image";
import { type Content, type ContentPart } from "@braintrust/typespecs";

const docsLink = "https://platform.openai.com/docs/guides/vision/quick-start";

export const MultimodalHelpTip = (
  <HelpTip
    side="bottom"
    help={
      <div className="flex flex-col gap-2.5">
        <p>
          Add text and image blocks to create a multimodal prompt. Images can be
          supplied as URLs or base64-encoded strings. Consult the{" "}
          <ExternalLink href={docsLink}>OpenAI documentation</ExternalLink> for
          information on supported image formats.
        </p>
      </div>
    }
  />
);

function renderImagePart(part: ContentPart) {
  return safeParseImage({ data: part, alt: JSON.stringify(part, null, 2) });
}

function makeContentElements(content: Exclude<Content, null | undefined>) {
  return typeof content === "string"
    ? content
    : content.map((part, i) =>
        part.type === "text" ? (
          <div key={i}>{part.text}</div>
        ) : (
          <div key={i} className="inline-flex">
            {renderImagePart(part)}
          </div>
        ),
      );
}

export default function ContentRenderer({ content }: { content: Content }) {
  return (
    content && (
      <div className="flex-auto space-x-2">{makeContentElements(content)}</div>
    )
  );
}
