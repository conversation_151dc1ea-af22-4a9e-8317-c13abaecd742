import { <PERSON><PERSON> } from "#/ui/button";
import { type CreateExperimentTask } from "./create-experiment-dialog";
import { type SavedScorer } from "#/utils/scorers";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>List } from "#/ui/tabs";
import { <PERSON><PERSON><PERSON>, Typescript<PERSON>ogo } from "../../onboarding-logos";
import CodeToCopy from "#/ui/code-to-copy";
import ModuleInstaller from "#/ui/docs/module-installer";

import { useMemo } from "react";
import { useQueryFunc } from "#/utils/react-query";
import { type getProjectSummary } from "../../org-actions";
import { useOrg } from "#/utils/user";
import { Spinner } from "#/ui/icons/spinner";
import { useApiKeyGeneration } from "#/utils/use-api-key-generation";
import { useProjectPromptsDropdown } from "../../p/[project]/playgrounds/prompts-dropdown";
import { useAgentsDropdown } from "../../p/[project]/playgrounds/agents-dropdown";
import { useScorersDropdown } from "../../p/[project]/playgrounds/scorers-dropdown";
import { type UIFunction } from "#/ui/prompts/schema";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { cn } from "#/utils/classnames";

interface CreateExperimentSdkSnippetProps {
  tasks: CreateExperimentTask[];
  dataset?: { datasetName?: string; projectName?: string } | null;
  scorers: SavedScorer[];
  scorerFunctions?: Record<string, UIFunction>;
  projectName: string;
  metadata?: Record<string, unknown>;
  maxConcurrency?: number | null;
  trialCount?: number | null;
  strict?: boolean | null;
}

export const CreateExperimentSdkSnippet = ({
  tasks,
  dataset,
  scorers,
  scorerFunctions,
  projectName,
  metadata,
  maxConcurrency,
  trialCount,
  strict,
}: CreateExperimentSdkSnippetProps) => {
  const org = useOrg();

  const { generatedApiKey, creating, createKey } = useApiKeyGeneration({
    keyNamePrefix: "experiment-setup-generated",
  });

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: org.name,
    },
  });

  const { idToPrompt } = useProjectPromptsDropdown();
  const { idToPrompt: idToAgent } = useAgentsDropdown();
  const { idToPrompt: idToScorer } = useScorersDropdown();

  const projectsById = useMemo(() => {
    if (!projects) return undefined;
    return Object.fromEntries(projects.map((p) => [p.project_id, p]));
  }, [projects]);

  const resolveFunctionRef = (
    task: CreateExperimentTask,
  ): { projectName: string; slug: string; version?: string } | null => {
    if (!task.versionData?.objectType) return null;

    const objectType = task.versionData.objectType;
    const projectId = task.versionData.projectId;
    const version = task.versionData.version;
    const taskProjectName = projectId
      ? projectsById?.[projectId]?.project_name
      : undefined;

    const getFunctionSlug = (): string | null => {
      if ("function_id" in task && task.function_id) {
        const functionId = task.function_id;

        if (objectType === "project_prompts") {
          return (
            idToPrompt[functionId]?.slug ||
            scorerFunctions?.[functionId]?.slug ||
            null
          );
        } else if (objectType === "project_functions") {
          return (
            scorerFunctions?.[functionId]?.slug ||
            idToPrompt[functionId]?.slug ||
            idToAgent[functionId]?.slug ||
            idToScorer[functionId]?.slug ||
            null
          );
        } else if (
          objectType === "org_prompts" ||
          objectType === "org_functions"
        ) {
          return (
            scorerFunctions?.[functionId]?.slug ||
            idToPrompt[functionId]?.slug ||
            idToAgent[functionId]?.slug ||
            idToScorer[functionId]?.slug ||
            null
          );
        }
      }

      if ("project_name" in task && "slug" in task && task.slug) {
        return task.slug;
      }

      return null;
    };

    const slug = getFunctionSlug();
    if (!slug || !taskProjectName) return null;

    return {
      projectName: taskProjectName,
      slug,
      version: version || undefined,
    };
  };

  const scorerDescriptor = (
    scorer: SavedScorer,
  ):
    | { type: "global"; name: string }
    | { type: "function"; projectName: string; slug: string }
    | { type: "todo"; id: string } => {
    if (scorer.type === "global") {
      return { type: "global", name: scorer.name };
    } else {
      const scorerData =
        scorerFunctions?.[scorer.id] || idToScorer?.[scorer.id];
      if (scorerData && scorerData.slug && scorerData.project_id) {
        const projectName = projectsById?.[scorerData.project_id]?.project_name;
        if (projectName) {
          return { type: "function", projectName, slug: scorerData.slug };
        }
      }
      return { type: "todo", id: scorer.id };
    }
  };

  const buildOptionalParams = () => {
    const taskName = tasks[0]?.name;
    const params: Record<string, string | number | boolean> = {};

    if (taskName) params.experimentName = taskName;
    if (trialCount) params.trialCount = trialCount;
    if (maxConcurrency) params.maxConcurrency = maxConcurrency;
    if (strict) params.strict = strict;

    return params;
  };

  interface LanguageSpec {
    evalIdent: string;
    initDatasetIdent: string;
    initFunctionIdent: string;

    keys: {
      experimentName: string;
      trialCount: string;
      maxConcurrency: string;
      strict: string;
      metadata: string;
    };

    renderImports(args: {
      includeDataset: boolean;
      includeInitFn: boolean;
      scorerImports: string[];
    }): string;
    renderScorerGlobal(name: string): string;
    renderScorerFunction(projectName: string, slug: string): string;
    renderScorerTodo(id: string): string;
    renderDatasetInit(projectName: string, datasetName: string): string;
    renderInlineData(): string;
    renderTaskFallback(): string;
    renderInitFunction(
      projectName: string,
      slug: string,
      version?: string,
    ): string;
    renderOptionalParams(
      params: Record<string, string | number | boolean>,
      metadata?: Record<string, unknown>,
    ): string;
    renderEval(
      projectName: string,
      data: string,
      task: string,
      scores: string[],
      optionalParams: string,
    ): string;
  }

  const tsSpec: LanguageSpec = {
    evalIdent: "Eval",
    initDatasetIdent: "initDataset",
    initFunctionIdent: "initFunction",

    keys: {
      experimentName: "experimentName",
      trialCount: "trialCount",
      maxConcurrency: "maxConcurrency",
      strict: "strict",
      metadata: "metadata",
    },

    renderImports({ includeDataset, includeInitFn, scorerImports }) {
      const imports = [
        `import { Eval${includeDataset ? ", initDataset" : ""}${includeInitFn ? ", initFunction" : ""} } from "braintrust";`,
      ];
      if (scorerImports.length > 0) {
        imports.push(
          `import { ${scorerImports.join(", ")} } from "autoevals";`,
        );
      }
      return imports.join("\n");
    },

    renderScorerGlobal(name) {
      return `      ${name},\n`;
    },

    renderScorerFunction(projectName, slug) {
      return `      initFunction({
        projectName: "${projectName}",
        slug: "${slug}",
      }),\n`;
    },

    renderScorerTodo(id) {
      return `      // TODO: Replace with your scorer function for ID: ${id}\n`;
    },

    renderDatasetInit(projectName, datasetName) {
      return `initDataset("${projectName}", { dataset: "${datasetName}" })`;
    },

    renderInlineData() {
      return `() => {
      // Replace with your dataset
      return [
        {
          input: "Hello",
          expected: "Hi there!",
        },
      ];
    }`;
    },

    renderTaskFallback() {
      return `async (input) => {
      // Replace with your LLM call
      // This is where you would call your model/function
      return "Your model response here";
    }`;
    },

    renderInitFunction(projectName, slug, version) {
      return `initFunction({
      projectName: "${projectName}",
      slug: "${slug}",
      ${version ? `version: "${version}",` : ""}
    })`;
    },

    renderOptionalParams(params, metadata) {
      const mappedParams = Object.entries(params).map(([key, value]) => {
        const keyMap: Record<string, string> = this.keys;
        const tsKey = keyMap[key] || key;
        return `    ${tsKey}: ${typeof value === "string" ? `"${value}"` : value},`;
      });

      if (metadata && Object.keys(metadata).length > 0) {
        mappedParams.push(
          `    metadata: ${JSON.stringify(metadata, null, 6)},`,
        );
      }

      return mappedParams.length > 0 ? `\n${mappedParams.join("\n")}` : "";
    },

    renderEval(projectName, data, task, scores, optionalParams) {
      const scoresStr =
        scores.length > 0
          ? `\n    scores: [\n${scores.join("")}    ],`
          : "\n    scores: [],";
      return `${this.evalIdent}(
  "${projectName}",
  {
    data: ${data},
    task: ${task},${scoresStr}${optionalParams}
  },
);`;
    },
  };

  const pySpec: LanguageSpec = {
    evalIdent: "Eval",
    initDatasetIdent: "init_dataset",
    initFunctionIdent: "init_function",

    keys: {
      experimentName: "experiment_name",
      trialCount: "trial_count",
      maxConcurrency: "max_concurrency",
      strict: "strict",
      metadata: "metadata",
    },

    renderImports({ includeDataset, includeInitFn, scorerImports }) {
      const imports = [
        `from braintrust import Eval${includeDataset ? ", init_dataset" : ""}${includeInitFn ? ", init_function" : ""}`,
      ];
      if (scorerImports.length > 0) {
        imports.push(`from autoevals import ${scorerImports.join(", ")}`);
      }
      return imports.join("\n");
    },

    renderScorerGlobal(name) {
      return name;
    },

    renderScorerFunction(projectName, slug) {
      return `init_function(
        project="${projectName}",
        slug="${slug}",
    )`;
    },

    renderScorerTodo(id) {
      return `# TODO: Replace with your scorer function for ID: ${id}`;
    },

    renderDatasetInit(projectName, datasetName) {
      return `init_dataset(project="${projectName}", name="${datasetName}")`;
    },

    renderInlineData() {
      return `lambda: [
        # Replace with your dataset
        {
            "input": "Hello",
            "expected": "Hi there!",
        },
    ]`;
    },

    renderTaskFallback() {
      return `lambda input: "Your model response here"  # Replace with your LLM call`;
    },

    renderInitFunction(projectName, slug, version) {
      return `init_function(
        project="${projectName}",
        slug="${slug}",${
          version
            ? `
        version="${version}",`
            : ""
        }
    )`;
    },

    renderOptionalParams(params, metadata) {
      const mappedParams = Object.entries(params).map(([key, value]) => {
        const keyMap: Record<string, string> = this.keys;
        const pyKey = keyMap[key] || key;
        return `    ${pyKey}=${typeof value === "string" ? `"${value}"` : value},`;
      });

      if (metadata && Object.keys(metadata).length > 0) {
        mappedParams.push(`    metadata=${JSON.stringify(metadata, null, 4)},`);
      }

      return mappedParams.length > 0 ? `\n${mappedParams.join("\n")}` : "";
    },

    renderEval(projectName, data, task, scores, optionalParams) {
      const scoresStr =
        scores.length > 0
          ? `\n    scores=[${scores.join(", ")}],`
          : "\n    scores=[],";
      return `${this.evalIdent}(
    "${projectName}",
    data=${data},
    task=${task},${scoresStr}${optionalParams}
)`;
    },
  };

  const generateCode = (spec: LanguageSpec): string => {
    const firstTask = tasks[0];
    const taskRef = firstTask ? resolveFunctionRef(firstTask) : null;

    const task = taskRef
      ? spec.renderInitFunction(
          taskRef.projectName,
          taskRef.slug,
          taskRef.version,
        )
      : spec.renderTaskFallback();

    const data = dataset
      ? spec.renderDatasetInit(dataset.projectName!, dataset.datasetName!)
      : spec.renderInlineData();

    const scorerDescriptors = scorers.map(scorerDescriptor);
    const globalScorers = scorerDescriptors
      .filter((d): d is { type: "global"; name: string } => d.type === "global")
      .map((d) => d.name);
    const scores = scorerDescriptors.map((desc) => {
      switch (desc.type) {
        case "global":
          return spec.renderScorerGlobal(desc.name);
        case "function":
          return spec.renderScorerFunction(desc.projectName, desc.slug);
        case "todo":
          return spec.renderScorerTodo(desc.id);
      }
    });

    const params = buildOptionalParams();
    const optionalParams = spec.renderOptionalParams(params, metadata);

    const includeDataset = !!dataset;
    const includeInitFn =
      taskRef !== null || scorerDescriptors.some((d) => d.type === "function");

    const imports = spec.renderImports({
      includeDataset,
      includeInitFn,
      scorerImports: globalScorers,
    });

    const evalCall = spec.renderEval(
      projectName,
      data,
      task,
      scores,
      optionalParams,
    );

    return `${imports}\n\n${evalCall}`;
  };

  const tsCode = generateCode(tsSpec);
  const pyCode = generateCode(pySpec);

  const environmentVariables = (
    <>
      <div className="mb-2 flex items-center justify-between text-sm">
        Configure environment variables
        <Button
          size="xs"
          className={cn("bg-background", {
            invisible: generatedApiKey,
          })}
          onClick={() => createKey(org.id)}
          disabled={creating}
        >
          {creating ? (
            <div className="flex items-center">
              Generating API key <Spinner className="ml-1" />
            </div>
          ) : (
            "Generate API key"
          )}
        </Button>
      </div>
      <CodeToCopy
        className="w-full"
        highlighterClassName="bg-primary-50 text-[13px]"
        language="bash"
        data={`${org.api_url ? `BRAINTRUST_API_URL=${org.api_url}\n` : ""}BRAINTRUST_API_KEY=${generatedApiKey || "<YOUR_BRAINTRUST_API_KEY>"}`}
      />
    </>
  );

  return (
    <div className="space-y-6">
      <Tabs
        defaultValue="ts"
        className="flex w-full max-w-(--breakpoint-sm) flex-col"
      >
        <TabsList className="inline-flex h-auto self-start p-1">
          <TabsTrigger asChild value="ts">
            <Button
              size="xs"
              className="rounded border-0 text-xs text-primary-500"
            >
              <TypescriptLogo className="size-3" /> TypeScript
            </Button>
          </TabsTrigger>
          <TabsTrigger asChild value="py">
            <Button
              size="xs"
              className="rounded border-0 text-xs text-primary-500"
            >
              <PythonLogo className="size-3" /> Python
            </Button>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="ts" className="w-full">
          <CollapsibleSection
            className="mt-4"
            defaultCollapsed
            localStorageKey="experiment-empty-sdk"
            title="Install and configure the Braintrust SDK"
          >
            <div className="mt-4 mb-2 text-sm">Install the Braintrust SDK</div>
            <ModuleInstaller
              className="bg-primary-50"
              languages={["typescript"]}
              packageNames="braintrust"
            />
            <div className="mt-4 mb-2 text-sm">{environmentVariables}</div>
          </CollapsibleSection>
          <div className="mt-6 mb-2 text-sm">
            Run the following code with{" "}
            <code className="rounded bg-comparison-50 px-1 text-xs font-medium text-comparison-700">
              npx braintrust eval my-first-eval.ts
            </code>
          </div>
          <CodeToCopy
            className="w-full"
            highlighterClassName="bg-primary-50 text-[13px]"
            language="typescript"
            data={tsCode}
          />
        </TabsContent>
        <TabsContent value="py" className="w-full">
          <CollapsibleSection
            className="mt-4"
            defaultCollapsed
            localStorageKey="experiment-empty-sdk"
            title="Install and configure the Braintrust SDK"
          >
            <div className="mt-4 mb-2 text-sm">Install the Braintrust SDK</div>
            <ModuleInstaller
              className="bg-primary-50"
              languages={["python"]}
              packageNames="braintrust"
            />
            <div className="mt-4 mb-2 text-sm">{environmentVariables}</div>
          </CollapsibleSection>
          <div className="mt-6 mb-2 text-sm">
            Run the following code with{" "}
            <code className="rounded bg-comparison-50 px-1 text-xs font-medium text-comparison-700">
              braintrust eval my_first_eval.py
            </code>
          </div>
          <CodeToCopy
            className="w-full"
            highlighterClassName="bg-primary-50 text-[13px]"
            language="python"
            data={pyCode}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
