import { type FunctionObjectType } from "@braintrust/typespecs";
import { getProjectLink } from "../../p/[project]/getProjectLink";

export const getPlaygroundsLink = ({
  orgName,
  projectName,
}: {
  orgName: string;
  projectName: string;
}) => `${getProjectLink({ orgName, projectName })}/playgrounds`;

export const getPlaygroundLink = (args: {
  orgName: string;
  projectName: string;
  playgroundName: string;
}) => {
  return `${getPlaygroundsLink({
    orgName: args.orgName,
    projectName: args.projectName,
  })}/${encodeURIComponent(args.playgroundName)}`;
};

export const getSavedPromptLink = ({
  orgName,
  projectSlug,
  promptId,
  promptVersion,
  type = "prompt",
  editFromPromptVersion,
}: {
  orgName: string;
  promptId: string;
  projectSlug: string;
  promptVersion?: string;
  type?: FunctionObjectType;
  editFromPromptVersion?: boolean;
}) => {
  if (type === "scorer" || type === "prompt") {
    return `${getProjectLink({ projectName: projectSlug, orgName })}/${type}s/${promptId}${promptVersion ? `?vn=${promptVersion}${editFromPromptVersion ? "&ev=true" : "&pt=activity"}` : ""}`;
  }
  return `${getProjectLink({
    orgName,
    projectName: projectSlug,
  })}/${type}s?pr=${promptId}${promptVersion ? `&vn=${promptVersion}` : ""}`;
};
