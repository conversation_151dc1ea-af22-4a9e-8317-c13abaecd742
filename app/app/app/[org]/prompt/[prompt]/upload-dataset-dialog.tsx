import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "#/ui/dialog";
import { Loading } from "#/ui/loading";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useContext,
  useState,
} from "react";
import { ImportWizard } from "../../p/[project]/datasets/[dataset]/importWizard";
import { type ImportProgressType, UploadContext } from "#/ui/upload-provider";
import { useAutoCreateDataset } from "#/utils/auto-create-dataset";
import { ProjectContext } from "../../p/[project]/projectContext";
import { useParquetView } from "#/utils/duckdb";
import { useMutableObject } from "#/utils/mutable-object";
import { useInsertRows } from "../../p/[project]/datasets/[dataset]/useInsertRows";

type Dataset = { project_id: string; id: string };

interface UploadDatasetDialogProps {
  onSuccessfullyUploaded: (createdDataset: {
    project_id: string;
    id: string;
  }) => Promise<void>;
  promptName: string;
  projectName: string;
  orgId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dataset?: Dataset;
  setDataset: Dispatch<SetStateAction<Dataset | undefined>>;
}

export function UploadDatasetDialog({
  onSuccessfullyUploaded,
  open,
  onOpenChange,
  promptName,
  projectName,
  orgId,
  dataset,
  setDataset,
}: UploadDatasetDialogProps) {
  const { importProgress, setImportProgress, executeImport } =
    useContext(UploadContext);

  const { mutateDatasets } = useContext(ProjectContext);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { mutateAsync: autoCreateDataset, isPending: isCreatingDataset } =
    useAutoCreateDataset(
      {
        orgId: orgId || "",
        projectName,
      },
      {
        onError: (error) => {
          setErrorMessage(error.message);
        },
      },
    );

  // Set up parquet view and DML for the dataset (existing or created)
  const { scan: datasetScan, channel: datasetChannel } = useParquetView({
    objectType: "dataset",
    search: dataset?.id,
  });

  const dml = useMutableObject({
    scan: datasetScan,
    objectType: "dataset",
    channel: datasetChannel,
  });

  const insertRows = useInsertRows({
    dml,
    projectId: dataset?.project_id,
    datasetId: dataset?.id,
  });

  const setImportProgressPartial = useCallback(
    (params: Partial<ImportProgressType>) => {
      setImportProgress?.((oldState) => ({ ...oldState, ...params }));
    },
    [setImportProgress],
  );

  const handleDataUpload = useCallback(
    async (
      data: Record<string, unknown>[],
      dataset: { project_id: string; id: string },
    ) => {
      setErrorMessage(null);
      try {
        await executeImport({
          data,
          insertRows: (d) =>
            insertRows(d, {
              projectId: dataset.project_id,
              datasetId: dataset.id,
            }),
          setImportProgress: setImportProgressPartial,
        });
        mutateDatasets();
        await onSuccessfullyUploaded(dataset);
        onOpenChange(false);
      } catch (e) {
        console.error(e);
        setErrorMessage(
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          (e as Error)?.message || `Import failed`,
        );
      }
    },
    [
      executeImport,
      insertRows,
      onSuccessfullyUploaded,
      onOpenChange,
      setImportProgressPartial,
      mutateDatasets,
    ],
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-full overflow-auto sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>
            {dataset ? "Upload rows to dataset" : "Upload dataset"}
          </DialogTitle>
        </DialogHeader>
        {isCreatingDataset ? (
          <Loading />
        ) : (
          <ImportWizard
            setDataToImport={async ({ data, file }) => {
              if (dataset) {
                // If using existing dataset, skip creation and go straight to uploading data
                await handleDataUpload(data, dataset);
              } else {
                // Create new dataset and wait for it to complete
                if (!orgId) throw new Error(`orgId is not defined`);
                const created = await autoCreateDataset(
                  promptName + " " + file.name + " " + new Date().valueOf(),
                );
                if (!created?.id || !created?.data.project.id) {
                  throw new Error(
                    `There was a problem creating the dataset ${created?.name} ${created?.id}`,
                  );
                }
                const projectId = created.data.project.id;
                setDataset({
                  project_id: projectId,
                  id: created.id,
                });
                await handleDataUpload(data, {
                  project_id: projectId,
                  id: created.id,
                });
              }
            }}
            importProgress={importProgress}
            setImportProgress={setImportProgressPartial}
          />
        )}
        {errorMessage && (
          <div className="px-3 py-4 text-center text-bad-700">
            {errorMessage}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

export const useUploadDatasetDialog = (
  params: Omit<
    UploadDatasetDialogProps,
    "open" | "onOpenChange" | "dataset" | "setDataset"
  > & { existingDataset?: Dataset },
) => {
  const [isModalOpened, setIsModalOpened] = useState(false);
  const [dataset, setDataset] = useState<
    { project_id: string; id: string } | undefined
  >(params.existingDataset);

  return {
    open: useCallback((dataset?: Dataset) => {
      setDataset(dataset);
      setIsModalOpened(true);
    }, []),
    modal: isModalOpened ? (
      <UploadDatasetDialog
        open={isModalOpened}
        onOpenChange={(open) => {
          setIsModalOpened(open);
          if (!open) {
            setDataset(undefined);
          }
        }}
        {...params}
        dataset={dataset}
        setDataset={setDataset}
      />
    ) : null,
  };
};
