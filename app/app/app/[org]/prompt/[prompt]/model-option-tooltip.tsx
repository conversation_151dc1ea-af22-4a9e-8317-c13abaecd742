import { type ModelDetails } from "#/ui/prompts/models";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { providerReadableName } from "../../settings/secrets/utils";
import { getModelIcon } from "./model-icon";

export const ModelOptionTooltip = ({
  model,
}: {
  model: ModelDetails & { provider: string };
}) => {
  const Icon = getModelIcon(model.modelName);
  return (
    <div className="flex min-w-52 flex-col gap-2 py-2">
      <div className="flex justify-between">
        <Icon size={18} className="text-primary-400" />
        <div className="text-primary-400">
          <span className="capitalize">{model.flavor}</span> model
        </div>
      </div>
      <div className="text-sm">
        <span className="text-primary-500">
          {providerReadableName(model.provider)}
        </span>
        <span className="px-2 text-primary-300">/</span>
        <span className="font-medium">
          {model.displayName || model.modelName}
        </span>
      </div>
      {model.displayName && (
        <div className="font-mono text-xs text-primary-500">
          {model.modelName}
        </div>
      )}
      {model.description && (
        <div className="text-xs text-primary-500">{model.description}</div>
      )}
      {model.input_cost_per_mil_tokens !== undefined &&
        model.input_cost_per_mil_tokens !== null && (
          <div className="text-xs text-primary-600">
            $
            {model.input_cost_per_mil_tokens.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}{" "}
            per million input tokens
          </div>
        )}
      {model.output_cost_per_mil_tokens !== undefined &&
        model.output_cost_per_mil_tokens !== null && (
          <div className="text-xs text-primary-600">
            $
            {model.output_cost_per_mil_tokens.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}{" "}
            per million output tokens
          </div>
        )}
      {model.multimodal && (
        <div className="flex items-center gap-2 text-primary-600">
          <Images className="size-3" /> Multimodal
        </div>
      )}
      {model.reasoning && (
        <div className="flex items-center gap-2 text-primary-600">
          <Brain className="size-3" /> Reasoning
        </div>
      )}
      {model.experimental && (
        <div className="text-xs leading-4 text-pretty text-primary-500">
          This model is experimental and may not be available in the future
        </div>
      )}
      {model.deprecated && (
        <div className="text-xs leading-4 text-pretty text-primary-500">
          <AlertTriangle className="mr-1 inline-block size-3 text-bad-600" />
          This model is deprecated and will not be available after it is
          deselected
        </div>
      )}
    </div>
  );
};
