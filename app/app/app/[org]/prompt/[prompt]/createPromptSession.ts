import { newId } from "#/utils/btapi/btapi";
import { performUpsert } from "#/utils/duckdb";
import { IS_MERGE_FIELD } from "braintrust/util";
import { type LoadedBtSessionToken } from "#/utils/auth/session-token";
import { type PromptData } from "@braintrust/typespecs";
import { type UIFunction } from "#/ui/prompts/schema";

export function makePromptSessionDefaultName(
  currentNumberOfPlaygrounds: number,
) {
  return `Playground ${currentNumberOfPlaygrounds + 1}`;
}

export interface InitialPromptArgs {
  apiUrl: string;
  getOrRefreshToken: () => Promise<LoadedBtSessionToken>;
  userId: string;
}

export async function createPromptSession({
  orgName,
  projectName,
  sessionName,
  initialRecords: initialRecordsProp,
  initialPromptArgs,
  shouldAwait,
}: {
  orgName: string;
  projectName: string;
  sessionName: string;
  initialRecords: {
    prompt_data: PromptData;
    function_data?: UIFunction["function_data"];
  }[];
  initialPromptArgs?: InitialPromptArgs;
  shouldAwait?: boolean;
}) {
  const resp = await fetch("/api/prompt_session/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      org_name: orgName,
      project_name: projectName,
      session_name: sessionName,
    }),
  });

  if (!resp.ok) {
    return { data: null, error: await resp.text() };
  }

  const data = await resp.json();

  (async () => {
    if (!initialPromptArgs) {
      return;
    }
    const { apiUrl, getOrRefreshToken, userId } = initialPromptArgs;
    const sessionToken = await getOrRefreshToken();
    const initialRecords = initialRecordsProp.map((record) => ({
      ...record,
      id: newId(),
      prompt_session_id: data.id,
      project_id: data.project_id,
      [IS_MERGE_FIELD]: false,
    }));

    if (shouldAwait) {
      const resp = await performUpsert(
        null,
        apiUrl,
        sessionToken,
        userId,
        initialRecords,
      );
      return resp;
    }

    // Do not wait for this to complete
    return performUpsert(null, apiUrl, sessionToken, userId, initialRecords);
  })();

  return { data, error: null };
}
