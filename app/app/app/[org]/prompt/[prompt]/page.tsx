import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { notFound, redirect } from "next/navigation";
import { getPlaygroundLink } from "./getPromptLink";
import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import { z } from "zod";
import { decodeURIComponentPatched } from "#/utils/url";

async function getProjectName(
  {
    org,
    prompt,
  }: {
    org: string;
    prompt: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<string | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "prompt_session",
      aclPermission: "read",
    },
    filters: {
      name: prompt,
      org_name: org,
    },
  });
  const query = `
    with full_results as (${fullResultSetQuery})
    select projects.name
    from full_results join projects on full_results.project_id = projects.id
    order by full_results.created desc
    limit 1
  `;
  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return rows.length === 1 ? z.string().parse(rows[0]["name"]) : null;
}

export default async function Page(props: {
  params: Promise<{ org: string; prompt: string }>;
}) {
  const params = await props.params;
  const { org, prompt } = {
    org: decodeURIComponentPatched(params.org),
    prompt: decodeURIComponentPatched(params.prompt),
  };
  try {
    const projectName = await getProjectName({ org, prompt });
    if (projectName) {
      return redirect(
        getPlaygroundLink({
          orgName: org,
          projectName,
          playgroundName: prompt,
        }),
      );
    }
  } catch (e) {
    console.error("Error finding prompt session", e);
  }
  return notFound();
}
