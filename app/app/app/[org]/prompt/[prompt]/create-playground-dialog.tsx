import { useCallback, useState } from "react";
import { OneLineTextPrompt } from "#/ui/dialogs/one-line-text-prompt";
import {
  createPromptSession,
  type InitialPromptArgs,
} from "./createPromptSession";
import { toast } from "sonner";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { newId } from "braintrust";

export interface MakeNewPromptSessionDialogArgs {
  orgName: string;
  projectName: string;
  projectId: string;
  refreshPromptSessions: () => void;
  initialPromptArgs?: InitialPromptArgs;
  onSuccess?: (promptSession: { id: string; name: string }) => void;
  entryPoint?: "playgroundsPageEmptyState" | "playgroundsPageCreateButton" | "promptDialogCreateButton" | "promptDialogDuplicateButton" | "functionEditorCreateButton" | "datasetPageCreateButton" | "onboardingFlow" | "projectOverviewCreateButton";
}

export function useCreatePlaygroundDialog() {
  // If set to |undefined|, we don't show the new prompt dialog.
  const [newPromptSessionDefaultValue, setNewPromptSessionDefaultValue] =
    useState<string | undefined>(undefined);

  const defaultPromptData = useDefaultPromptData();
  const { track } = useAppAnalytics();

  const makeNewPromptSessionDialog = useCallback(
    ({
      orgName,
      projectName,
      projectId,
      refreshPromptSessions,
      initialPromptArgs,
      onSuccess,
      entryPoint = "promptDialogCreateButton",
    }: MakeNewPromptSessionDialogArgs) => {
      if (!newPromptSessionDefaultValue) {
        return undefined;
      }
      return (
        <OneLineTextPrompt
          title="Create playground"
          fieldName="Name"
          onSubmit={async (name: string) => {
            // Generate a consistent flow ID for this creation flow
            const flowId = newId();
            
            // Track the playground create attempt
            track("playgroundCreateAttempt", {
              playgroundName: name,
              entryPoint: entryPoint,
              flowId: flowId,
              projectId: projectId,
              projectName: projectName,
            });
            
            const resp = await createPromptSession({
              orgName,
              projectName,
              sessionName: name,
              initialRecords: [
                {
                  prompt_data: defaultPromptData,
                },
              ],
              initialPromptArgs,
            });
            setNewPromptSessionDefaultValue(undefined);
            const promptSession = {
              id: resp?.data?.id,
              name: resp?.data?.name,
            };
            if (!promptSession.id || !promptSession.name || resp?.error) {
              toast.error(
                `Failed to create playground ${JSON.stringify(name)}`,
                {
                  description: JSON.stringify(resp?.error, null, 2),
                },
              );
            } else {
              // Track successful playground creation
              track("playgroundCreate", {
                playgroundName: name,
                playgroundId: promptSession.id,
                entryPoint: entryPoint,
                flowId: flowId,
                projectId: resp.data.project_id,
                projectName: projectName,
                initialDataType: "prompt",
                initialObjectId: undefined,
              });
              refreshPromptSessions?.();
              onSuccess?.(promptSession);
            }
          }}
          onOpenChange={() => setNewPromptSessionDefaultValue(undefined)}
          open={newPromptSessionDefaultValue !== undefined}
          defaultValue={newPromptSessionDefaultValue}
          submitLabel="Create"
        />
      );
    },
    [newPromptSessionDefaultValue, defaultPromptData, track],
  );
  return { setNewPromptSessionDefaultValue, makeNewPromptSessionDialog };
}
