"use client";

import { type RefObject, useEffect, useState } from "react";

export const useScrollPosition = (options?: {
  ref?: RefObject<HTMLElement | null>; // if no ref is provided, window will be used
  onScroll?: (scrollYPos: number) => void;
}) => {
  const { ref, onScroll } = options ?? {};
  const [scrollY, setScrollY] = useState(
    typeof window === "undefined" ? 0 : window.scrollY,
  );

  useEffect(() => {
    const handleScroll = () => {
      if (onScroll) {
        onScroll(window.scrollY);
      } else {
        setScrollY(window.scrollY);
      }
    };

    const container = ref?.current || window;
    container.addEventListener("scroll", handleScroll);

    // Clean up the event listener
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [onScroll, ref]);

  return scrollY;
};
