import { isObject, serializeJSONWithPlainString } from "#/utils/object";
import {
  type Completion,
  type CompletionSource,
  autocompletion,
} from "@codemirror/autocomplete";
import { type Diagnostic, linter } from "@codemirror/lint";
import { type Extension } from "@codemirror/state";
import { type EditorView } from "@codemirror/view";
import Mustache from "mustache";
import { v4 as uuidv4 } from "uuid";
import { type PromptBlockData } from "#/ui/prompts/schema";
import { renderMessage } from "braintrust";
import { getObjValueByPath } from "braintrust/util";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function escapeData(value: any): string {
  return serializeJSONWithPlainString(value);
}

export function isEmptyPromptData(data?: PromptBlockData | null): boolean {
  if (!data) {
    return true;
  }

  if (data.type === "completion") {
    return data.content.trim().length === 0;
  } else {
    return (
      data.messages.length === 0 ||
      data.messages.every(
        (d) =>
          !("content" in d) ||
          (typeof d.content === "string" && d.content.trim().length === 0),
      )
    );
  }
}

export function renderPromptData(
  data: PromptBlockData,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  args: any,
): PromptBlockData {
  const render = (template: string) =>
    Mustache.render(template, args, undefined, {
      escape: escapeData,
    });
  if (data.type === "completion") {
    return {
      type: data.type,
      content: render(data.content),
    };
  } else {
    return {
      type: data.type,
      messages: data.messages.map((d) => renderMessage(render, d)),
      tools: data.tools && render(data.tools),
    };
  }
}

export function getMustacheVars(prompt: string) {
  try {
    return Mustache.parse(prompt).filter(
      (span) => span[0] === "name" || span[0] === "&",
    );
  } catch {
    return [];
  }
}

export function getPromptLinter({
  argStructure,
  onCreateDataset,
  onFixWithLoop,
  onLint,
  getMissingVariableMessage,
}: {
  argStructure: Record<string, unknown>;
  onCreateDataset?: VoidFunction;
  onFixWithLoop?: (message: string) => void;
  onLint?: (editorId: string, diagnostics: Diagnostic[]) => void;
  getMissingVariableMessage?: (variableName: string) => string;
}): Extension {
  return linter(
    (view) => {
      const diagnostics: Diagnostic[] = [];
      const variables = getMustacheVars(view.state.doc.toString());

      for (const variable of variables) {
        // argStructure is the schema structure of the input rows in the playground, so in this validation we
        // are just checking that the mustache variable is accessing a field that is defined in the schema. we
        // replace all variables accessing indices in an array with 0 because a field that is an array type
        // will only ever be one item long, where the item is the structure of all items in the array.
        const arrPathsReplaced = variable[1].replaceAll(/\.\d+/g, ".0");
        const fieldExists =
          getObjValueByPath(argStructure, arrPathsReplaced.split(".")) !=
          undefined;

        if (!fieldExists) {
          const actions: Diagnostic["actions"] = [
            {
              name: "Remove variable",
              apply: (view, from, to) =>
                view.dispatch({ changes: { from, to } }),
            },
            ...(onCreateDataset
              ? [
                  {
                    name: "Create dataset",
                    apply: () => onCreateDataset(),
                  },
                ]
              : []),
            ...(onFixWithLoop
              ? [
                  {
                    name: "Fix with Loop",
                    apply: () => {
                      onFixWithLoop(
                        onCreateDataset
                          ? "There is no dataset selected for this playground. Create a new one with 5 rows for the following prompt: ${JSON.stringify(promptData)}"
                          : (getMissingVariableMessage?.(variable[1]) ??
                              `Variable '${variable[1]}' is not defined in your dataset. Please look at my dataset and fix my prompt's variable usage.`),
                      );
                    },
                  },
                ]
              : []),
          ];

          diagnostics.push({
            from: variable[2],
            to: variable[3],
            severity: "error",
            message: onCreateDataset
              ? "There is no dataset selected for this playground. To use variables, select an existing dataset or create a new one."
              : (getMissingVariableMessage?.(variable[1]) ??
                `Variable '${variable[1]}' is not defined in your dataset. You may encounter unexpected results.`),
            actions,
          });
        }
      }

      if (onLint) {
        const editorId =
          "values" in view.state && Array.isArray(view.state.values)
            ? view.state.values.find(
                (v) => typeof v === "string" && v.startsWith("prompt-"),
              )
            : null;
        if (editorId) {
          onLint(editorId, diagnostics);
        }
      }

      return diagnostics;
    },
    { delay: 100 },
  );
}

const insertCurlyBraces = (
  view: EditorView,
  completion: Completion,
  from: number,
  to: number,
) => {
  const currentStr = view.state.sliceDoc(from, to);
  const regex = new RegExp(`({{)?${currentStr}(}})?`);
  const [, openingBraces, closingBraces] =
    view.state.sliceDoc(from - 2, to + 2).match(regex) || [];

  if (openingBraces && closingBraces) {
    view.dispatch({ changes: [{ insert: completion.label, from, to }] });
  } else if (!openingBraces && !closingBraces) {
    const insert = `{{${completion.label}}}`;
    view.dispatch({
      changes: [{ insert, from, to }],
      selection: { anchor: insert.length - 2 + from },
    });
  } else if (!openingBraces) {
    view.dispatch({ changes: [{ insert: `{{${completion.label}`, from, to }] });
  } else {
    const insert = `${completion.label}}}`;
    view.dispatch({
      changes: [{ insert, from, to }],
      selection: { anchor: insert.length - 2 + from },
    });
  }
};

/**
 * This hook takes a prompt argument object and generates custom
 * autocompletion config for Codemirror as we don't really use an existing language
 * that can be parsed into a syntax tree via Codemirror plugin.
 * @example
 * Having object { bar: "test", arnold: "schwarzenegger", foo: { hello: "world" } }:
 * ar // Suggests "bar" and "arnold"
 * llo // Suggests "hello"
 * hello.wo // Suggests "world"
 */
export function getPromptAutocompletion(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  args: Record<string, any> = {},
): Extension {
  const TOP_LEVEL_KEY = uuidv4();
  const optionsObj = Object.entries(args)
    .map((item) => [...item, ""])
    .reduce(
      function reducer(
        result,
        [key, value, path],
      ): Record<string, Completion[]> {
        result[path || TOP_LEVEL_KEY] = [
          ...(result?.[path || TOP_LEVEL_KEY] || []),
          {
            label: key,
            type: path ? "property" : "variable",
            apply: !path ? insertCurlyBraces : undefined,
          },
        ];

        if (isObject(value)) {
          return Object.entries(value)
            .map((item) => [...item, path ? `${path}\\.${key}` : key])
            .reduce(reducer, result);
        }

        return result;
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      { [TOP_LEVEL_KEY]: [] } as Record<string, Completion[]>,
    );

  const override: CompletionSource[] = [];

  const prefix = "{{";
  for (const [key, options] of Object.entries(optionsObj)) {
    const isTopLevel = key === TOP_LEVEL_KEY;
    try {
      // Sometimes this regular expression is invalid, because `key` may be
      // a very long string, e.g. {{bla bla. some sentecne! with special characters...
      // if the user does not terminate a variable with a closing curly brace.
      // In that case, just skip this key.
      const matchRegex = new RegExp(
        isTopLevel ? `${prefix}\\w+` : `${prefix}${key}\\.\\w*`,
      );

      override.push((context) => {
        const textBefore = context.matchBefore(matchRegex);
        const optionWord = context.matchBefore(/\w+/);

        if (!textBefore) return null;

        return {
          from: isTopLevel
            ? textBefore.from + prefix.length
            : optionWord?.from || context.pos,
          options,
        };
      });
    } catch (e) {
      console.warn("Invalid completion key, skipping autocomplete", key, e);
    }
  }

  return autocompletion({ override });
}
