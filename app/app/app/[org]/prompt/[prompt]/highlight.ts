import { useDarkMode } from "#/utils/useDarkMode";
import { HighlightStyle, syntaxHighlighting } from "@codemirror/language";
import { tags as t } from "@lezer/highlight";
import { githubDarkInit, githubLightInit } from "@uiw/codemirror-theme-github";
import { useMemo } from "react";

export const monoVarStyle = HighlightStyle.define([
  {
    tag: [t.definition(t.propertyName), t.lineComment],
    class: "font-mono font-semibold",
  },
]);
const settings = {
  settings: { background: "transparent" },
};

export function usePromptTheme() {
  const darkMode = useDarkMode();

  const theme = useMemo(
    () => [
      darkMode ? githubDarkInit(settings) : githubLightInit(settings),
      syntaxHighlighting(monoVarStyle),
    ],
    [darkMode],
  );

  return theme;
}
