import { isEmpty } from "#/utils/object";
import {
  forwardRef,
  type HTMLAttributes,
  type PropsWithChildren,
  useCallback,
  useContext,
  useMemo,
} from "react";
import { cn } from "#/utils/classnames";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type UIFunction } from "#/ui/prompts/schema";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { useOrg } from "#/utils/user";
import { Button } from "#/ui/button";
import { Bolt } from "lucide-react";
import { DropdownMenuCheckboxItem, DropdownMenuItem } from "#/ui/dropdown-menu";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { usePromptsByProjectSorted } from "../../../p/[project]/prompts/use-prompts-by-project";

const ToolOptionLabel = ({
  toolName,
  projectName,
  className,
}: {
  toolName: string;
  projectName?: string;
  className?: string;
}) => {
  return (
    <span
      className={cn("flex w-full flex-1 items-center gap-2", className)}
      title={`${toolName} from ${projectName ?? "this project"}`}
    >
      <span className="flex-1">{toolName}</span>
      {projectName && (
        <span className="max-w-24 flex-none truncate text-primary-500">
          {projectName}
        </span>
      )}
    </span>
  );
};

type ToolItem = UIFunction & {
  projectName: string;
};

export function ToolsDropdown({
  selectedTools,
  onSelectTool,
  align,
  className,
  isReadOnly,
}: PropsWithChildren<{
  selectedTools?: {
    type: "function";
    id: string;
  }[];
  onSelectTool: (tool: ToolItem) => void;
  align?: DropdownMenuContentProps["align"];
  open?: boolean;
  setOpen?: (open: boolean) => void;
  className?: string;
  isReadOnly?: boolean;
}>) {
  const { projectId, projectName } = useContext(ProjectContext);

  const { name: orgName } = useOrg();

  const { promptsByProjectSorted: toolsByProject, isLoading } =
    usePromptsByProjectSorted({
      functionObjectType: "tool",
      orgName,
      projectName,
      query: "",
      btqlFilter:
        "function_schema.parameters IS NOT NULL AND function_type = 'tool'",
    });

  const allTools = useMemo(
    () =>
      toolsByProject.flatMap((p) =>
        p.prompts.map((prompt) => ({ ...prompt, projectName: p.projectName })),
      ),
    [toolsByProject],
  );

  const selectedToolItems = useMemo(
    () =>
      (selectedTools || []).reduce<typeof allTools>((acc, t) => {
        const tool = allTools.find((tool) => tool.id === t.id);
        if (tool) acc.push(tool);
        return acc;
      }, []),
    [selectedTools, allTools],
  );

  const dropdownData = useMemo(() => {
    if (isEmpty(toolsByProject)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      toolsByProject[0]?.projectName === projectName
        ? toolsByProject[0].prompts
            .map((p) => ({ ...p, projectName: projectName }))
            .sort((a, b) => (a.name ?? "").localeCompare(b.name ?? ""))
        : undefined;

    const otherProjects = toolsByProject
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ prompts, projectName }) =>
        prompts
          .map((p) => ({ ...p, projectName }))
          .sort((a, b) => (a.name ?? "").localeCompare(b.name ?? "")),
      );

    const subGroups =
      otherProjects.length > 0
        ? [{ groupLabel: "Other projects", items: otherProjects }]
        : undefined;

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [toolsByProject, projectName]);

  const ToolMenuItem = forwardRef<
    HTMLDivElement,
    { item: ToolItem } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item: tool, ...rest }, ref) => {
        return (
          <DropdownMenuCheckboxItem
            {...rest}
            ref={ref}
            onSelect={() => onSelectTool(tool)}
            checked={
              !!selectedTools?.find(
                (v) => v.type === "function" && v.id === tool.id,
              )
            }
          >
            <ToolOptionLabel
              toolName={tool.name ?? ""}
              projectName={
                tool.project_id !== projectId ? tool.projectName : undefined
              }
            />
          </DropdownMenuCheckboxItem>
        );
      },
      [onSelectTool, selectedTools, projectId],
    ),
  );

  return (
    <NestedDropdown<ToolItem>
      align={align}
      modal
      objectType="tool"
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      DropdownItemComponent={ToolMenuItem}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name?.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      selectedItems={selectedToolItems}
      renderItems={(
        items: ToolItem[],
        DropdownItemComponent: typeof ToolMenuItem,
        isSearching?: boolean,
      ) => {
        const isSelected = (item: ToolItem) =>
          selectedToolItems.some((t) => {
            return t.id === item.id;
          });
        const selectedItems = items.filter(isSelected);
        // If searching, show all items, otherwise show only items that are not selected
        const prunedItems = isSearching
          ? items
          : items.filter((item) => !selectedItems.includes(item));

        if (prunedItems.length === 0) {
          return (
            <DropdownMenuItem key="all-items-selected" disabled>
              All tools are selected
            </DropdownMenuItem>
          );
        }

        return prunedItems.map((item, key) => (
          <DropdownItemComponent item={item} key={key} />
        ));
      }}
    >
      <Button
        isDropdown
        isLoading={isLoading}
        disabled={isReadOnly}
        className={cn("w-full px-3 disabled:opacity-100", className)}
      >
        <span
          className={cn(
            "flex flex-1 flex-col gap-1.5 text-left",
            selectedTools?.length === 0 && "font-normal text-primary-500",
          )}
        >
          {selectedToolItems?.length === 0
            ? "Select tools"
            : selectedToolItems?.map((t, idx) => {
                const name = allTools.find((p) => p.id === t.id)?.name;
                return (
                  <span className="flex items-center gap-1.5" key={idx}>
                    <Bolt className="size-3 text-amber-700 dark:text-amber-300" />
                    {name}
                  </span>
                );
              })}
        </span>
      </Button>
    </NestedDropdown>
  );
}
