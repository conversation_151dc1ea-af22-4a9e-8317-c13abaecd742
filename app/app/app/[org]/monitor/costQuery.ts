import { useAvailableModels } from "#/ui/prompts/models";
import * as Query from "#/utils/btql/query-builder";
import { useOrg } from "#/utils/user";
import { useBtql } from "#/utils/btql/btql";
import { crunchCosts } from "./costs";
import { crunchCostsByTime } from "./costs";
import { type Expr, type ParsedQuery } from "@braintrust/btql/parser";
import { useMemo } from "react";
import { CreatedField } from "#/utils/duckdb";
import { getMonitorBtqlArgs } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

export function useCostQuery(
  projectIds: string[],
  {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  }: {
    chartTimeFrame: ChartTimeFrame;
    timeBucket: "minute" | "hour" | "day";
    groupBy?: string;
    experimentIds: string[];
    from: "project_logs" | "experiment";
  },
) {
  const { name: orgName } = useOrg();
  const { allAvailableModels } = useAvailableModels({ orgName });

  const query = useMemo<ParsedQuery | null>(() => {
    if (projectIds.length === 0) {
      return null;
    }

    const startTime = new Date(chartTimeFrame.start).toISOString();
    const endTime = new Date(chartTimeFrame.end).toISOString();

    const filters: Expr[] = [
      {
        op: "gt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: startTime,
        },
      },
      {
        op: "lt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: endTime,
        },
      },
    ];

    return {
      from:
        from === "project_logs"
          ? Query.from("project_logs", projectIds)
          : Query.from("experiment", experimentIds),
      dimensions: [
        {
          alias: "time",
          expr: {
            op: "function",
            name: Query.ident(timeBucket),
            args: [Query.ident(CreatedField)],
          },
        },
        {
          alias: "model",
          expr: { btql: "metadata.model" },
        },
        ...(groupBy
          ? [{ alias: "group", expr: { btql: `metadata.${groupBy}` } }]
          : []),
      ],
      measures: [
        {
          alias: "prompt_uncached_tokens",
          expr: {
            btql: "sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0)",
          },
        },
        {
          alias: "prompt_cached_tokens",
          expr: {
            btql: "sum(metrics.prompt_cached_tokens)",
          },
        },
        {
          alias: "prompt_cache_creation_tokens",
          expr: { btql: "sum(metrics.prompt_cache_creation_tokens)" },
        },
        {
          alias: "completion_tokens",
          expr: {
            btql: "sum(metrics.completion_tokens)",
          },
        },
        {
          alias: "count",
          expr: {
            btql: "count(1)",
          },
        },
      ],
      filter: Query.and(...filters),
      sort: [{ expr: { btql: "time" }, dir: "asc" }],
    };
  }, [projectIds, chartTimeFrame, timeBucket, groupBy, from, experimentIds]);

  const { data, loading } = useBtql({
    name: "Cost query",
    query,
    ...getMonitorBtqlArgs({
      startTime: new Date(chartTimeFrame.start).toISOString(),
      projectIds,
    }),
    disableLimit: undefined,
  });

  const costData = data?.toArray().map((r) => r.toJSON());

  // When groupBy is provided, we don't bucket by 'time', as that will happen in the Chart component
  const crunchedCosts = groupBy
    ? crunchCosts({ costData, allAvailableModels })
    : crunchCostsByTime({ costData, allAvailableModels });

  return { costData: crunchedCosts, loading };
}
