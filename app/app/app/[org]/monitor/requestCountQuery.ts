import { useMemo } from "react";
import { useMonitorQuery } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

export type RequestCountMetricsData = {
  count: bigint;
  spans: bigint;
  llm_count: bigint;
  tool_count: bigint;
  time: number;
  group?: string | null;
};

export type RequestCountMetrics = Extract<
  keyof RequestCountMetricsData,
  "count" | "spans" | "llm_count" | "tool_count"
>;

export function useRequestCountQuery(
  projectIds: string[],
  {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  }: {
    chartTimeFrame: ChartTimeFrame;
    timeBucket: "minute" | "hour" | "day";
    groupBy?: string;
    experimentIds: string[];
    from: "project_logs" | "experiment";
  },
): { data: RequestCountMetricsData[] | undefined; loading: boolean } {
  const measures = useMemo(
    () => [
      {
        alias: "count",
        expr: { btql: "sum(is_root ? 1 : 0)" },
      },
      {
        alias: "spans",
        expr: { btql: "count(1)" },
      },
      {
        alias: "llm_count",
        expr: { btql: "sum(span_attributes.type = 'llm' ? 1 : 0)" },
      },
      {
        alias: "tool_count",
        expr: { btql: "sum(span_attributes.type = 'tool' ? 1 : 0)" },
      },
    ],
    [],
  );

  const dimensions = useMemo(
    () =>
      groupBy
        ? [
            {
              alias: "group",
              expr: { btql: `metadata.${groupBy}` },
            },
          ]
        : undefined,
    [groupBy],
  );

  return useMonitorQuery<RequestCountMetricsData>(
    "Request count query",
    projectIds,
    {
      chartTimeFrame,
      timeBucket,
      measures,
      dimensions,
      experimentIds,
      from,
    },
  );
}
