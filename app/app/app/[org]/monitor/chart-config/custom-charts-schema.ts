import { z } from "zod";

// All schema changes within same version must be backwards compatible
export const CUSTOM_CHARTS_VERSION = "0.0.0"; // dev version

const measuresConfigSchema = z.array(
  z.object({
    btql: z.string(),
    displayName: z.string().optional(),
  }),
);
export type MeasuresConfig = z.infer<typeof measuresConfigSchema>;

const filtersConfigSchema = z.array(z.object({ btql: z.string() }));
export type FiltersConfig = z.infer<typeof filtersConfigSchema>;

const groupBysConfigSchema = z.array(
  z.object({ btql: z.string(), displayName: z.string().optional() }),
);

// Right now we only support timeseries, but plan to extend this
// Each one should map to a specific visualization
// some visualizations could be reused in multiple chart types
// const chartVizTypeSchema = z.enum(["timeseries"]);
// type CHART_VIZ_TYPE = z.infer<typeof chartVizTypeSchema>;

// timeseries has a sub viz type for bars/lines
const timeseriesVizTypeSchema = z.enum(["bars", "lines"]);
export const unitTypeSchema = z.enum(["duration", "percent", "count", "cost"]);

const timeseriesVizSchema = z.object({
  type: z.literal("timeseries"),
  timeseriesVizType: timeseriesVizTypeSchema,
  unitType: unitTypeSchema.optional(),
});

/** Hypothetical future viz schemas to add */
// const singleValueVizSchema = z.object({
//   type: z.literal("singleValue"),
//   unitTypeSchema: unitTypeSchema.optional(),
//   customUnitString: z.string().optional(),
// });
// const toplistVizSchema = z.object({
//   type: z.literal("toplist"),
//   normToTotal: z.boolean().optional(),
// });
// const treemapVizSchema = z.object({
//   type: z.literal("treemap"),
//   maxGroupsShown: z.number().optional(),
// });
// const pdfVizSchema = z.object({
//   type: z.literal("pdf"),
// });
// const cdfVizSchema = z.object({
//   type: z.literal("cdf"),
// });

const monitorTimeseriesDefinitionSchema = z.object({
  type: z.literal("monitorTimeseries"),

  // our measures to query for. Must exist.
  measures: measuresConfigSchema,

  // optional filters on all data (an AND of all filters)
  filters: filtersConfigSchema.optional(),

  // optional group bys (splits data series into unique group by combinations)
  groupBys: groupBysConfigSchema.optional(),

  // our viz options
  viz: z.discriminatedUnion("type", [timeseriesVizSchema]),
});

// for now we we only have monitor timeseries
const definitionSchema = z.discriminatedUnion("type", [
  monitorTimeseriesDefinitionSchema,
]);

// /** Rank data - hypothetical extension */
// const ranksDefinitionSchema = z
//   .object({
//     measures: measuresConfigSchema,
//     filters: filtersConfigSchema.optional(),
//     groupBys: groupBysConfigSchema.optional(),
//     sortByOptions: z
//       .object({
//         aggregator: z
//           .enum(["min", "max", "avg", "sum", "count", "percentile"])
//           .optional(),
//         aggregatorPercentile: z.number().optional(),
//         direction: z.enum(["asc", "desc"]).optional(),
//         limit: z.number().optional(),
//       })
//       .optional(),
//     viz: z.union([singleValueVizSchema, toplistVizSchema, treemapVizSchema]),
//   })
//   .optional(),

// /** Distribution data - hypothetical extension */
// const distributionsDefinitionSchema: z
//   .object({
//     metrics: z.array(z.string()),
//     filters: filtersConfigSchema.optional(),
//     groupBys: groupBysConfigSchema.optional(),
//     viz: z.union([pdfVizSchema, cdfVizSchema]),
//   })
//   .optional(),

// /** In time order by not bucketed data - hypothetical extension */
// const ordinalTimeseriesSchema = z
//   .object({
//     measures: measuresConfigSchema,
//     filters: filtersConfigSchema.optional(),
//     groupBys: groupBysConfigSchema.optional(),
//     viz: timeseriesVizSchema,
//   })
//   .optional(),

/**
 * Our core chart config schema, this defines the contents of a chart
 * This is what's serialized for persisting or sending charts anywhere
 *
 * Info must be in the config in order to be "portable" across the app
 * If you need app runtime level info in the chart, consider placing logic
 * within charts and creating additional options to activate that logic.
 * For example if you want to add a special tooltip component,
 * do not pass in an extra prop to the chart component, but instead add a schema option
 * like 'mySpecialTooltip?: boolean', and conditionally render your component
 * from within the chart. This way the functionality can be preserved across the app
 *
 */
export const chartConfigSchema = z.object({
  /** A chart always needs a title */
  title: z.string(),

  /**
   * One chart type should exist within the chart config
   * If multiple exist, only one will be chosen
   * If none exist the chart will be empty
   */

  definition: definitionSchema,

  /** for when we add support for sending individual charts around */
  version: z.string().optional(),
});
export type ChartConfig = z.infer<typeof chartConfigSchema>;

/**
 * A collection of charts is a record of their id to their config
 */
export const chartsCollection = z.record(z.string(), chartConfigSchema);

/**
 * For now we only support a linear ordering layout,
 * an array of chart ids
 *
 * In the future, a more flexible layout may be represented
 * as an object of chart ids to 2d rectangle
 * */
const linearLayoutSchema = z.object({
  type: z.literal("linear"),
  order: z.array(z.string()),
});
const layout = z.discriminatedUnion("type", [linearLayoutSchema]).nullish();

/**
 * CustomCharts is a collection of charts, their layout on a page, and version
 *
 * Layout and chart definitions are separated
 * to isolate concerns and make any future changes to layout easier
 *
 * Version is always required, while we are flexible otherwise
 */
export const customChartsSchema = z.object({
  layout: layout.optional(),
  charts: chartsCollection.optional(),
  version: z.string(),
});

export type CustomCharts = z.infer<typeof customChartsSchema>;
