import { MONITOR_PRESET_CARDS } from "../card-config/monitor-cards.constants";

// get the base preset id (if it exists, else undefined)
export const getPresetMonitorCard = (id: string) => {
  // preset cards ids are in the form of 'preset_id-id' or just 'preset_id'
  const [basePresetId] = id.split("-");
  const config = MONITOR_PRESET_CARDS.get(basePresetId);
  if (config) {
    return {
      id: basePresetId,
      config,
    };
  }
};
