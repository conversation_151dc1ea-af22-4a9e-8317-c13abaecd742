import { nanoid } from "ai";
import type { MonitorCardConfig } from "../card/monitor-card-config.types";
import type { ChartConfig } from "./custom-charts-schema";
import { getPresetMonitorCard } from "./get-preset-monitor-card";

/**
 * Transforms a ChartConfig into a MonitorCardConfig
 *
 * The reverse isn't always possible since MonitorCardConfig has extra options
 * that aren't all represented in ChartConfig
 *
 * For existing default monitor cards, we use special 'preset' monitor cards to retain functionality
 *
 * In the future we'll map to various types of charts besides timeseries
 */
export const chartConfigToMonitorCardConfig = (
  chart?: ChartConfig,
  chartId?: string,
): MonitorCardConfig => {
  // if chart is missing, try to find it in presets from chartId
  if (!chart && chartId) {
    const preset = getPresetMonitorCard(chartId);
    if (preset) {
      return preset.config;
    }
  }

  const monitorTimeseries = chart?.definition;

  // empty chart placeholder case
  if (!chart || !monitorTimeseries) {
    return {
      header: {
        title: "Unknown chart",
        iconName: "custom-chart",
      },
      name: "empty chart",
      idName: "empty chart",
      measures: [],
      vizType: "lines",
    };
  }

  const id = chartId ?? nanoid();

  return {
    header: {
      title: chart.title,
      iconName: "custom-chart",
    },
    measures: monitorTimeseries.measures.map((m, i) => ({
      btql: m.btql,
      alias: `measure-${i}`,
      displayName: m.displayName,
    })),
    chartFilters: monitorTimeseries.filters?.map((f) => ({ btql: f.btql })),
    chartGroupBys: monitorTimeseries.groupBys?.map((f) => ({
      btql: f.btql,
      alias: f.displayName,
    })),
    name: id,
    idName: id,
    vizType: monitorTimeseries.viz.timeseriesVizType,
    unitType: monitorTimeseries.viz.unitType,
  };
};
