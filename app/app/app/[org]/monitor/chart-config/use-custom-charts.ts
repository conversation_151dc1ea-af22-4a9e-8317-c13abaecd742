import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  CUSTOM_CHARTS_VERSION,
  customChartsSchema,
  type ChartConfig,
  type CustomCharts,
} from "./custom-charts-schema";
import type z from "zod";
import { MONITOR_PRESET_CARDS } from "../card-config/monitor-cards.constants";
import type { viewDataSchema } from "@braintrust/typespecs";
import isEqual from "lodash.isequal";
import { nanoid } from "ai";
import { getPresetMonitorCard } from "./get-preset-monitor-card";
import { produce } from "immer";

export type ChartSaveMode = "existing" | "new-first" | "new-last" | "new-next";

// by default use preset monitor cards
// we store the keys of these cards, but not their config
const DEFAULT_CUSTOM_CHARTS: CustomCharts = {
  layout: {
    type: "linear",
    order: [...MONITOR_PRESET_CARDS.keys()],
  },
  version: CUSTOM_CHARTS_VERSION,
};

type RawCustomCharts = z.infer<typeof viewDataSchema>["custom_charts"];

export const useCustomCharts = () => {
  // the raw format that's persisted in the view
  // this isn't fully typed so we need separate step to parse
  const [savedRawCustomCharts, setSavedRawCustomCharts] =
    useState<RawCustomCharts | null>(null);

  // the current charts version, which we modify for local state changes
  const [customCharts, setCustomCharts] = useState<CustomCharts>(
    DEFAULT_CUSTOM_CHARTS,
  );

  // charts in order of layout
  const orderedCharts = useMemo(() => {
    const { layout, charts } = customCharts ?? {};

    return (layout?.order ?? []).map((chartId) => {
      return charts?.[chartId];
    });
  }, [customCharts]);

  const setCardOrdering = useCallback((order: string[]) => {
    setCustomCharts(
      produce((draft) => {
        draft.layout = {
          type: "linear",
          order,
        };
      }),
    );
  }, []);

  /** Set the chart config for a specific chart id */
  const setChartConfig = useCallback((chart: ChartConfig, chartId: string) => {
    setCustomCharts(
      produce((draft) => {
        if (!draft.charts) {
          draft.charts = {};
        }
        draft.charts[chartId] = chart;
      }),
    );
  }, []);

  const deleteChart = useCallback((chartId: string) => {
    setCustomCharts(
      produce((draft) => {
        if (!draft.layout) {
          return;
        }
        // remove chartId from layout
        const newOrder = draft.layout.order.filter((id) => id !== chartId);

        // remove any orphaned charts from the new layout
        const newCharts: Record<string, ChartConfig> = {};
        newOrder.forEach((id) => {
          const existing = draft?.charts?.[id];
          if (existing) {
            newCharts[id] = existing;
          }
        });

        draft.layout = {
          type: "linear",
          order: newOrder,
        };
        draft.charts = newCharts;
      }),
    );
  }, []);

  // load from raw view custom charts data
  const loadCustomCharts = useCallback(
    (rawCustomCharts: RawCustomCharts | null) => {
      setSavedRawCustomCharts(rawCustomCharts);
      try {
        const charts = customChartsSchema.parse(rawCustomCharts);
        setCustomCharts(charts);
      } catch (error) {
        // else we load the default chart
        setCustomCharts(DEFAULT_CUSTOM_CHARTS);
      }
    },
    [],
  );

  // conversion back into raw persisted version
  const rawCustomCharts: RawCustomCharts | null = useMemo(() => {
    // convert default chart state to null
    if (
      JSON.stringify(customCharts) === JSON.stringify(DEFAULT_CUSTOM_CHARTS)
    ) {
      return null;
    }

    return customCharts;
  }, [customCharts]);

  const isDirty = useMemo(() => {
    // use lodash isEqual to ignore key ordering differences
    return !isEqual(savedRawCustomCharts, rawCustomCharts);
  }, [rawCustomCharts, savedRawCustomCharts]);

  const cardOrdering = useMemo(() => {
    return customCharts?.layout?.order ?? [];
  }, [customCharts]);

  const getChartConfig = useCallback(
    (chartId: string) => {
      if (!customCharts?.charts) {
        return null;
      }

      return customCharts.charts[chartId] ?? null;
    },
    [customCharts.charts],
  );

  const saveChart = useCallback(
    ({
      chartConfig,
      chartId,
      mode,
      presetId,
    }: {
      chartConfig: ChartConfig;
      chartId: string;
      mode: ChartSaveMode;
      presetId?: string;
    }) => {
      if (mode === "existing") {
        // if saving a preset we only update the id in the layout
        if (presetId) {
          const newOrdering = cardOrdering.map((id) =>
            id === chartId ? presetId : id,
          );
          setCardOrdering(newOrdering);
          return;
        }

        // if existing id is that of a preset, replace with a new chart instead
        const existingPreset = getPresetMonitorCard(chartId);
        if (existingPreset) {
          const newId = nanoid();
          const orderIndex = cardOrdering.indexOf(chartId);
          if (orderIndex >= 0) {
            const newOrdering = [...cardOrdering];
            newOrdering[orderIndex] = newId;
            setCardOrdering(newOrdering);
            setChartConfig(chartConfig, newId);
            return;
          }
        }

        // else we only need to update the chart config
        setChartConfig(chartConfig, chartId);
        return;
      }

      // new chart we're going to create
      const newChartId = presetId ?? nanoid();

      // save new chart config when not a preset
      if (!presetId) {
        setChartConfig(chartConfig, newChartId);
      }

      // update layout
      if (mode === "new-first") {
        setCardOrdering([newChartId, ...cardOrdering]);
      } else if (mode === "new-last") {
        setCardOrdering([...cardOrdering, newChartId]);
      } else if (mode === "new-next") {
        // insert right after current place
        const idx = cardOrdering.indexOf(chartId);
        const newOrder = [...cardOrdering];
        newOrder.splice(idx + 1, 0, newChartId);
        setCardOrdering(newOrder);
      }
    },
    [cardOrdering, setCardOrdering, setChartConfig],
  );

  return {
    orderedCharts,
    cardOrdering,
    getChartConfig,
    setCardOrdering,
    setChartConfig,
    saveChart,
    deleteChart,

    // call when any view is saved so that dirty flag state is correct
    loadCustomCharts,
    isDirty,

    // the raw charts format to be saved in view
    rawCustomCharts,
  };
};
