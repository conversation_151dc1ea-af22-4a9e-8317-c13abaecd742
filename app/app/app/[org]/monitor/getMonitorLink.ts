import { type TIME_BUCKET } from "#/ui/charts/time-bucket";
import { parseAsJsonEncoded } from "#/ui/query-parameters";
import { type UrlSearch, urlSearchSchema } from "#/utils/search/search";
import { getOrgLink } from "../getOrgLink";
import {
  addMinutes,
  addHours,
  addDays,
  subMilliseconds,
  addSeconds,
} from "date-fns";
import {
  getExperimentsPageRedirectLink,
  getObjectRedirectLink,
} from "#/app/app/[org]/object/object-actions";

export const getMonitorLink = ({
  orgName,
  projectId,
}: {
  orgName: string;
  projectId?: string;
}) =>
  `${getOrgLink({ orgName })}/monitor${projectId ? `?projectId=${projectId}` : ""}`;

function getEndTime({
  timeBucket,
  time,
}: {
  timeBucket: TIME_BUCKET;
  time: string;
}) {
  let endTime = new Date(time);

  if (timeBucket === "second") {
    endTime = addSeconds(endTime, 1);
  } else if (timeBucket === "minute") {
    endTime = addMinutes(endTime, 1);
  } else if (timeBucket === "hour") {
    endTime = addHours(endTime, 1);
  } else if (timeBucket === "day") {
    endTime = subMilliseconds(addDays(endTime, 1), 1);
  }

  return endTime.toISOString();
}

export const getTracesLink = async ({
  orgName,
  projectId,
  from,
  timeBucket,
  time,
  existingSearch,
  extraFilters,
}: {
  orgName: string;
  projectId: string;
  from: "experiment" | "project_logs";
  timeBucket: TIME_BUCKET;
  time: string;
  existingSearch?: UrlSearch;
  extraFilters?: string[];
}) => {
  const endTime = getEndTime({ time, timeBucket });
  const rangeQuery = encodeURIComponent(
    JSON.stringify({
      from: time,
      to: endTime,
    }),
  );

  const appendedSearch = existingSearch ?? {};
  if (extraFilters && extraFilters.length > 0) {
    appendedSearch.filter = [
      ...(appendedSearch.filter ?? []),
      ...extraFilters.map((f) => ({ text: f })),
    ];
  }

  const parser = parseAsJsonEncoded(urlSearchSchema.parse);
  const filterQueryString = encodeURIComponent(
    parser.serialize(appendedSearch),
  );

  let logsOrExperimentsLink;

  if (from === "project_logs") {
    logsOrExperimentsLink = await getObjectRedirectLink({
      orgName,
      object_type: from,
      object_id: projectId,
      _xact_id: "",
      id: "",
    });
  } else if (from === "experiment") {
    logsOrExperimentsLink = await getExperimentsPageRedirectLink({
      orgName,
      projectId,
    });
  }
  return `${logsOrExperimentsLink}?search=${filterQueryString}&range=${rangeQuery}`;
};
