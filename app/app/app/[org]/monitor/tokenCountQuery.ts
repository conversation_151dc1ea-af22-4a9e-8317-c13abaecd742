import { useIsRootBtqlSnippet } from "#/utils/btql/btql";
import { useMemo } from "react";
import { useMonitorQuery } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

export type TokenCountMetricsData = {
  time: number;
  count: bigint;
  sum_tokens: number | null;
  sum_prompt_uncached_tokens: bigint | null;
  sum_prompt_cached_tokens: bigint | null;
  sum_prompt_cache_creation_tokens: bigint | null;
  sum_completion_tokens: bigint | null;
  group?: string | null;
};

export type TokenMetrics = Extract<
  keyof TokenCountMetricsData,
  | "sum_tokens"
  | "sum_prompt_uncached_tokens"
  | "sum_prompt_cached_tokens"
  | "sum_prompt_cache_creation_tokens"
  | "sum_completion_tokens"
>;

export function useTokenCountQuery(
  projectIds: string[],
  {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  }: {
    chartTimeFrame: ChartTimeFrame;
    timeBucket: "minute" | "hour" | "day";
    groupBy?: string;
    experimentIds: string[];
    from: "project_logs" | "experiment";
  },
): { data: TokenCountMetricsData[] | undefined; loading: boolean } {
  const isRootBtqlSnippet = useIsRootBtqlSnippet();
  const measures = useMemo(
    () => [
      {
        alias: "count",
        expr: { btql: `sum(${isRootBtqlSnippet})` },
      },
      {
        alias: "sum_tokens",
        expr: {
          btql: `sum(metrics.tokens)`,
        },
      },
      {
        alias: "sum_prompt_uncached_tokens",
        expr: {
          btql: `sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0)`,
        },
      },
      {
        alias: "sum_prompt_cached_tokens",
        expr: {
          btql: `sum(metrics.prompt_cached_tokens)`,
        },
      },
      {
        alias: "sum_prompt_cache_creation_tokens",
        expr: {
          btql: `sum(metrics.prompt_cache_creation_tokens)`,
        },
      },
      {
        alias: "sum_completion_tokens",
        expr: {
          btql: `sum(metrics.completion_tokens)`,
        },
      },
    ],
    [isRootBtqlSnippet],
  );

  const dimensions = useMemo(
    () =>
      groupBy
        ? [
            {
              alias: "group",
              expr: { btql: `metadata.${groupBy}` },
            },
          ]
        : undefined,
    [groupBy],
  );

  return useMonitorQuery<TokenCountMetricsData>(
    "Token count query",
    projectIds,
    {
      chartTimeFrame,
      timeBucket,
      measures,
      dimensions,
      experimentIds,
      from,
    },
  );
}
