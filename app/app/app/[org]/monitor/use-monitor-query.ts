import { type Expr, type ParsedQuery } from "@braintrust/btql/parser";
import * as Query from "#/utils/btql/query-builder";
import { CreatedField } from "#/utils/duckdb";
import { useMemo } from "react";
import { useBtql } from "#/utils/btql/btql";
import type { ChartTimeFrame } from "./time-controls/time-range";

export function getMonitorBtqlArgs({
  startTime,
  projectIds,
}: {
  startTime: string;
  projectIds: string[];
}) {
  const startTimeDate = new Date(startTime);
  const brainstoreRealtime =
    Date.now() - startTimeDate.getTime() < 6 * 60 * 60 * 1000; /* 6 hours */
  return {
    disableLimit: true,
    brainstoreRealtime,
    brainstoreIncludeUnbackfilled: projectIds.length > 1,
    expensive: true,
  };
}

export function useMonitorQuery<DataType>(
  name: string,
  projectIds: string[],
  {
    chartTimeFrame,
    timeBucket,
    dimensions,
    measures,
    experimentIds,
    from,
  }: {
    chartTimeFrame: ChartTimeFrame;
    timeBucket: "minute" | "hour" | "day";
    dimensions?: ParsedQuery["dimensions"];
    measures: ParsedQuery["measures"];
    experimentIds: string[];
    from: "project_logs" | "experiment";
  },
): {
  data: DataType[] | undefined;
  loading: boolean;
  error: Error | undefined;
} {
  const query = useMemo<ParsedQuery | null>(() => {
    if (projectIds.length === 0) {
      return null;
    }

    const filters: Expr[] = [
      {
        op: "gt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: new Date(chartTimeFrame.start).toISOString(),
        },
      },
      {
        op: "lt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: new Date(chartTimeFrame.end).toISOString(),
        },
      },
    ];

    return {
      from:
        from === "project_logs"
          ? Query.from("project_logs", projectIds)
          : Query.from("experiment", experimentIds),
      dimensions: [
        {
          alias: "time",
          expr: {
            op: "function",
            name: Query.ident(timeBucket),
            args: [Query.ident(CreatedField)],
          },
        },
        ...(dimensions || []),
      ],
      measures,
      filter: Query.and(...filters),
      sort: [{ expr: { btql: "time" }, dir: "asc" }],
    };
  }, [
    projectIds,
    chartTimeFrame,
    timeBucket,
    measures,
    dimensions,
    from,
    experimentIds,
  ]);
  const { data, loading, error } = useBtql({
    name,
    query,
    ...getMonitorBtqlArgs({
      startTime: new Date(chartTimeFrame.start).toISOString(),
      projectIds,
    }),
    disableLimit: undefined,
  });

  const dataAsJson = useMemo(
    () => data?.toArray().map((r) => r.toJSON()),
    [data],
  );

  return { data: dataAsJson, loading, error };
}
