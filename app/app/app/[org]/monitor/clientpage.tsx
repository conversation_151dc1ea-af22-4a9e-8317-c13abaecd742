"use client";

import { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState } from "react";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { useAnalytics } from "#/ui/use-analytics";

import { MonitorCards } from "./monitor-cards";
import { GroupBySelect, NONE_GROUP } from "./group-by-select";
import { RowTypeSelect } from "./row-type-select";
import { type getProjectSummary, type ProjectSummary } from "../org-actions";
import { useOrg } from "#/utils/user";
import { BodyWrapper } from "../../body-wrapper";
import { isEmpty } from "braintrust/util";
import { AccessFailed } from "#/ui/access-failed";
import { ViewDropdown } from "#/ui/views/view-dropdown";
import { CreateViewDialog } from "#/ui/views/create-view-dialog";
import { RenameViewDialog } from "#/ui/views/rename-view-dialog";
import { DeleteViewDialog } from "#/ui/views/delete-view-dialog";
import { useQueryFunc } from "#/utils/react-query";
import type { getRecentExperimentIds } from "#/app/api/actions/getRecentExperimentIds/action";
import { useTimeControls } from "./time-controls/use-time-controls";
import { cn } from "#/utils/classnames";
import {
  clausesToUrlValue,
  makeBubble,
  normalizeUrlSearch,
  urlValueToClauses,
  type Search,
} from "#/utils/search/search";
import AppliedFilters from "#/ui/applied-filters";
import { useClauseChecker } from "#/utils/search-btql";
import { Bubble } from "#/ui/table/bubble";
import { PlusIcon, SearchIcon } from "lucide-react";
import { Input } from "#/ui/input";
import { useMonitorView } from "./use-monitor-view";
import { MonitorFilterEditor } from "./monitor-filter-editor";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import type { TimeRangeFilter as TimeRangeFilterType } from "#/utils/view/use-view";
import { TimeRangeSelect } from "#/ui/time-range-select/time-range-select";
import { parseDateString } from "#/ui/time-range-select/parse-date-string";
import { getRoundedTimeFrame } from "./time-controls/get-rounded-time-frame";
import { CardEditorModal } from "./chart-editor/chart-editor-modal";
import { Button } from "#/ui/button";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { usePreventNavigation } from "#/utils/use-prevent-navigation";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { getPresetMonitorCard } from "./chart-config/get-preset-monitor-card";

export interface Params {
  org: string;
  project: string;
}

export default function ClientPage({
  projectSummary,
}: {
  params: Params;
  projectSummary: ProjectSummary[];
}) {
  const { projectId, orgName } = useContext(ProjectContext);

  useAnalytics({
    page: projectId
      ? {
          category: "monitor",
          props: { project_id: projectId },
        }
      : null,
  });

  if (isEmpty(orgName)) {
    return <AccessFailed objectType="Organization" objectName={orgName} />;
  }

  return <MonitorPageContent serverProjectSummary={projectSummary} />;
}

function MonitorPageContent({
  serverProjectSummary,
}: {
  serverProjectSummary: ProjectSummary[];
}) {
  const { name: orgName } = useOrg();

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: orgName },
    serverData: serverProjectSummary,
  });

  const customDashboard = useIsFeatureEnabled("customDashboard");

  // "input" view states that need to update immediately to input
  const [inputSearchFilter, setInputSearchFilter] = useState<string | null>(
    null,
  );

  // we debounce because we don't want every text input creating new queries
  const debouncedSetSearchFilter = useDebouncedCallback(
    (text: string | null) => {
      setInputSearch((prev) => {
        if (!prev) {
          return prev;
        }
        const copy = { ...prev };
        if (text === null || text === "") {
          copy.match = undefined;
        } else {
          const bubble = new Bubble({
            type: "match",
            label: text,
            text: text,
            clear: () => setInputSearchFilter(null),
          });
          copy.match = [{ text, type: "match", bubble }];
        }
        return copy;
      });
    },
    500,
  );

  useEffect(() => {
    debouncedSetSearchFilter(inputSearchFilter);
  }, [inputSearchFilter, debouncedSetSearchFilter]);

  const [viewLoads, setViewLoads] = useState<number>(0);

  // "input" Search object that we pass directly into children components to modify
  const [inputSearch, setInputSearch] = useState<Search>({});
  const [initSearch, setInitSearch] = useState<boolean>(false);

  const {
    allViews,
    selectedView,
    isPendingViews,
    isUpdatingViews,
    selectedViewName,
    pageIdentifier,
    loadView,
    createView,
    renameView,
    deleteView,
    saveView,
    isViewDirty,
    preventNav,
    loadedViewOptions, // just for time controls

    // our monitor view options state
    projectId,
    projectType,
    setProjectType,
    setSpanType,
    setRangeValue,
    setFrameStart,
    setFrameEnd,
    tzUTC,
    setTzUTC,
    groupBy,
    setGroupBy,
    setSearch,

    orderedCharts,
    cardOrdering,
    setCardOrdering,
    getChartConfig,
    saveChart,
    deleteChart,
  } = useMonitorView(projects);

  // soft prevent navigation away when monitor view says to
  const {
    showNavigationConfirmation,
    handleConfirmNavigation,
    handleCancelNavigation,
  } = usePreventNavigation(preventNav);

  // time controls
  const {
    setExplicitTimeFrame,
    timeSpan,
    setTimeSpan,
    chartTimeFrame,
    loadMonitorViewOptions,
    isLive,
    pause,
  } = useTimeControls();

  // on view load, set the time controls
  useEffect(() => {
    if (!loadedViewOptions) {
      return;
    }

    loadMonitorViewOptions(loadedViewOptions);
  }, [loadMonitorViewOptions, loadedViewOptions]);

  // on view load set local Search object
  useEffect(() => {
    if (!loadedViewOptions) {
      return;
    }

    setInitSearch(true);
    const { search } = loadedViewOptions;
    if (!search) {
      setInputSearchFilter(null);
      setInputSearch({});
      return;
    }

    // free text search input
    const firstMatch = search.match?.[0];
    if (firstMatch) {
      const text =
        typeof firstMatch === "string" ? firstMatch : firstMatch.text;
      setInputSearchFilter(text);
    } else {
      setInputSearchFilter(null);
    }

    // convert to search object
    const filter = urlValueToClauses("filter", search.filter);
    const match = urlValueToClauses("match", search.match);

    const newSearch = {
      filter: filter.map((f) => ({
        ...f,
        bubble: makeBubble({
          clause: f,
          setSearch: setInputSearch,
        }),
      })),
      match: match.map((m) => ({
        ...m,
        bubble: makeBubble({
          clause: m,
          setSearch: setInputSearch,
        }),
      })),
    };

    setInputSearch(newSearch);
  }, [loadedViewOptions]);

  useEffect(() => {
    if (!loadedViewOptions) {
      return;
    }
    setViewLoads((p) => p + 1);
  }, [loadedViewOptions]);

  // sync timeframe controls to local state on timeSpan changes
  useEffect(() => {
    if (typeof timeSpan === "string") {
      setSpanType("range");
      setRangeValue(timeSpan);
      setFrameStart(null);
      setFrameEnd(null);
      return;
    }
    setSpanType("frame");
    setRangeValue(null);
    setFrameStart(String(timeSpan.start));
    setFrameEnd(String(timeSpan.end ?? new Date().getTime()));
  }, [timeSpan, setSpanType, setRangeValue, setFrameStart, setFrameEnd]);

  const objectType: "project_logs" | "experiment" =
    projectType === "project" ? "project_logs" : "experiment";
  const { clauseChecker } = useClauseChecker(objectType, false);

  const onBrush = useCallback(
    (v: [number, number] | null) => {
      if (!v) {
        return;
      }
      const rounded = getRoundedTimeFrame({ start: v[0], end: v[1] }, tzUTC);
      setExplicitTimeFrame(rounded.start, rounded.end);
    },
    [setExplicitTimeFrame, tzUTC],
  );

  const projectIds = useMemo(() => (projectId ? [projectId] : []), [projectId]);

  const { data: recentExperimentIds } = useQueryFunc<
    typeof getRecentExperimentIds
  >({
    fName: "getRecentExperimentIds",
    args: { project_id: projectId ?? "", timeFrame: chartTimeFrame },
    queryOptions: {
      enabled: !!projectId && projectType === "experiment",
    },
  });

  const experimentIds = useMemo(
    () => recentExperimentIds ?? [],
    [recentExperimentIds],
  );

  const timeRangeFilterValue = useMemo<TimeRangeFilterType>(() => {
    if (typeof timeSpan === "string") {
      return timeSpan;
    }
    const { start, end } = chartTimeFrame;
    return {
      from: new Date(start).toISOString(),
      to: new Date(end).toISOString(),
    };
  }, [chartTimeFrame, timeSpan]);

  const onTimeRangeFilterChange = useCallback(
    (v: TimeRangeFilterType) => {
      if (typeof v === "string") {
        // live span
        setTimeSpan(v);
        return;
      }
      const startMs = parseDateString(v.from).getTime();
      const endMs = parseDateString(v.to).getTime();
      setExplicitTimeFrame(startMs, endMs);
    },
    [setTimeSpan, setExplicitTimeFrame],
  );

  // View picker state
  const [isCreateViewDialogOpen, setIsCreateViewDialogOpen] = useState(false);
  const [isRenameViewDialogOpen, setIsRenameViewDialogOpen] = useState(false);
  const [isDeleteViewDialogOpen, setIsDeleteViewDialogOpen] = useState(false);

  // when 'input' search changes locally, update the view search
  useEffect(() => {
    if (!initSearch) {
      // do not set url params before getting initialized from view
      return;
    }
    const newUrlSearch =
      normalizeUrlSearch({
        filter: clausesToUrlValue(inputSearch.filter),
        tag: clausesToUrlValue(inputSearch.tag),
        match: clausesToUrlValue(inputSearch.match),
        sort: clausesToUrlValue(inputSearch.sort),
      }) ?? {};

    setSearch(newUrlSearch);

    // clear input search if empty
    if (!inputSearch.match?.[0]) {
      setInputSearchFilter(null);
    }
  }, [inputSearch, setSearch, initSearch]);

  const [isFilterEditorOpen, setIsFilterEditorOpen] = useState<boolean>(false);

  const showAppliedFilters = useMemo(() => {
    return (
      (inputSearch.filter && inputSearch.filter.length > 0) ||
      (inputSearch.match && inputSearch.match.length > 0)
    );
  }, [inputSearch]);

  const fromClause = useMemo(() => {
    if (projectType === "project") {
      return `project_logs("${projectId}")`;
    }
    return `experiment("${experimentIds.join('", "')}")`;
  }, [projectType, projectId, experimentIds]);

  const objectIds = useMemo(() => {
    if (objectType === "experiment") {
      return experimentIds;
    }
    return [projectId ?? ""];
  }, [experimentIds, objectType, projectId]);

  const cardProps = useMemo(
    () => ({
      orderedCharts,
      cardOrdering: cardOrdering,
      setCardOrdering: setCardOrdering,
      projectIds: projectIds,
      chartTimeFrame: chartTimeFrame,
      search: inputSearch,
      groupBy: groupBy ? `metadata.${groupBy}` : undefined,
      from: objectType,
      experimentIds: experimentIds,
      isFirstLoad: viewLoads === 1,
      tzUTC: tzUTC ?? false,
      onBrush: onBrush,
    }),
    [
      cardOrdering,
      chartTimeFrame,
      experimentIds,
      groupBy,
      inputSearch,
      orderedCharts,
      objectType,
      onBrush,
      projectIds,
      setCardOrdering,
      tzUTC,
      viewLoads,
    ],
  );

  const [editorOpenId, setEditorOpenId] = useState<string | null>(null);
  const [saveNewMode, setSaveNewMode] = useState<
    "new-first" | "new-next" | "new-last"
  >("new-first");

  const onEdit = useCallback((chartId: string) => {
    setEditorOpenId(chartId);
  }, []);

  const cardEditorModal = useMemo(() => {
    // force dismount on close
    // todo - improve state management in editor to work always being mounted
    if (!editorOpenId) {
      return null;
    }

    const editorChartConfig =
      (editorOpenId ? getChartConfig(editorOpenId) : undefined) ?? undefined;
    const existingEditorOpen = editorOpenId !== "new";
    const project = projects.find((p) => p.project_id === projectId);

    return (
      <CardEditorModal
        title={existingEditorOpen ? "Edit chart" : "Create chart"}
        project={project}
        objectType={objectType}
        objectIds={objectIds}
        showEdit={existingEditorOpen}
        initialChart={editorChartConfig}
        presetId={getPresetMonitorCard(editorOpenId ?? "")?.id}
        saveNewMode={existingEditorOpen ? "new-next" : saveNewMode}
        {...cardProps}
        setCardState={() => {}}
        onClose={() => setEditorOpenId(null)}
        onSave={(props) => {
          setEditorOpenId(null);
          saveChart({ ...props, chartId: editorOpenId });
        }}
        onDelete={() => {
          deleteChart(editorOpenId ?? "");
          setEditorOpenId(null);
        }}
      />
    );
  }, [
    cardProps,
    deleteChart,
    editorOpenId,
    getChartConfig,
    objectIds,
    objectType,
    projectId,
    projects,
    saveChart,
    saveNewMode,
  ]);

  return (
    <BodyWrapper innerClassName="overflow-auto">
      <MainContentWrapper
        hideFooterSpacer
        className="h-auto min-h-[calc(100vh-45px)] pt-0"
      >
        <div className="@container/controls sticky top-0 z-50 flex items-center gap-2 bg-background py-3">
          <div className="flex flex-1 items-center gap-2">
            <RowTypeSelect value={projectType} onChange={setProjectType} />
            <ViewDropdown
              views={allViews}
              selectedView={selectedView}
              viewNameOverride={
                isUpdatingViews ? (selectedViewName ?? undefined) : undefined
              }
              loadView={loadView}
              saveView={saveView}
              isLoadingViews={isPendingViews}
              setCreateViewDialogOpen={setIsCreateViewDialogOpen}
              setRenameViewDialogOpen={setIsRenameViewDialogOpen}
              setDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
              pageIdentifier={pageIdentifier}
              descriptionText="Save page configuration as&nbsp;a&nbsp;view"
              isDirty={isViewDirty ?? false}
              resetView={loadView}
            />
            {projectId && (
              <>
                <MonitorFilterEditor
                  clauseChecker={clauseChecker}
                  isOpen={isFilterEditorOpen}
                  setIsOpen={(open) => {
                    setIsFilterEditorOpen(open);
                  }}
                  search={inputSearch}
                  setSearch={setInputSearch}
                  objectType={objectType}
                  // for experiments use the first experiment - we should change to all experiments in future
                  objectId={
                    objectType === "project_logs"
                      ? projectId
                      : (experimentIds[0] ?? "")
                  }
                  projects={projects}
                  projectId={projectId}
                />
                <GroupBySelect
                  projectId={projectId}
                  experimentIds={experimentIds}
                  startTime={new Date(chartTimeFrame.start).toISOString()}
                  value={groupBy ?? NONE_GROUP}
                  onChange={(newValue: string | null) => {
                    setGroupBy(newValue);
                  }}
                  from={objectType}
                />
                <div className="flex-1 md:hidden" />
                <SearchControl
                  objectType={objectType}
                  inputSearchFilter={inputSearchFilter}
                  setInputSearchFilter={setInputSearchFilter}
                  className="hidden md:flex"
                />
              </>
            )}
            <TimeRangeSelect
              context="monitor"
              value={timeRangeFilterValue}
              setValue={onTimeRangeFilterChange}
              isUTC={tzUTC}
              setIsUTC={setTzUTC}
              isLiveOverride={isLive}
              onDisableLiveOverride={pause}
            />
            {customDashboard && (
              <Button
                size="xs"
                Icon={PlusIcon}
                onClick={() => {
                  setEditorOpenId("new");
                  setSaveNewMode("new-first");
                }}
              >
                Chart
              </Button>
            )}
          </div>
        </div>
        <div className="flex pb-3 md:hidden">
          <SearchControl
            objectType={objectType}
            inputSearchFilter={inputSearchFilter}
            setInputSearchFilter={setInputSearchFilter}
          />
        </div>
        {showAppliedFilters && (
          <div>
            <AppliedFilters
              matchesLast
              search={inputSearch}
              setSearch={setInputSearch}
              clauseChecker={clauseChecker}
              fromClause={fromClause}
            />
          </div>
        )}
        <MonitorCards {...cardProps} onEdit={onEdit} />
        {cardEditorModal}
      </MainContentWrapper>
      <CreateViewDialog
        isCreateViewDialogOpen={isCreateViewDialogOpen}
        setIsCreateDatasetDialogOpen={setIsCreateViewDialogOpen}
        createView={createView}
      />
      <RenameViewDialog
        isRenameViewDialogOpen={isRenameViewDialogOpen}
        setIsRenameViewDialogOpen={setIsRenameViewDialogOpen}
        viewId={selectedView.id}
        viewName={selectedView.name}
        renameView={renameView}
      />
      <DeleteViewDialog
        isDeleteViewDialogOpen={isDeleteViewDialogOpen}
        setIsDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
        viewName={selectedView.name}
        deleteView={useCallback(
          () => deleteView(selectedView.id!),
          [deleteView, selectedView.id],
        )}
      />
      <ConfirmationDialog
        open={showNavigationConfirmation}
        onOpenChange={() => handleCancelNavigation()}
        title="Unsaved changes"
        description="You have unsaved changes. Are you sure you want to leave?"
        confirmText="Leave"
        onConfirm={handleConfirmNavigation}
      />
    </BodyWrapper>
  );
}

const SearchControl = ({
  objectType,
  inputSearchFilter,
  setInputSearchFilter,
  className,
}: {
  objectType: string;
  inputSearchFilter: string | null;
  setInputSearchFilter: (value: string | null) => void;
  className?: string;
}) => {
  return (
    <div className={cn("relative flex flex-1 gap-2", className)}>
      <SearchIcon className="pointer-events-none absolute top-1/2 left-2 size-3 -translate-y-1/2 text-primary-500" />
      <Input
        className={cn(
          "h-7 flex-1 border-none bg-primary-50 pl-7 text-xs transition-colors hover:bg-primary-100 focus-visible:bg-primary-100 focus-visible:ring-0",
        )}
        placeholder={`Search ${objectType === "project_logs" ? "logs" : objectType}`}
        value={inputSearchFilter ?? ""}
        onChange={(e) => {
          setInputSearchFilter(e.target.value);
        }}
      />
    </div>
  );
};
