import { Combobox } from "#/ui/combobox/combobox";
import { But<PERSON> } from "#/ui/button";
import { getMetricsLabel } from "./utils";
import { Settings2 } from "lucide-react";

function MeasuresSelect({
  selectedMeasures,
  availableMeasures,
  onChange,
  buttonClassName,
  contentClassName,
  measureToDisplayName,
}: {
  selectedMeasures: string[];
  availableMeasures: string[];
  onChange: (seriesId: string) => void;
  contentClassName?: string;
  buttonClassName?: string;
  measureToDisplayName?: Map<string, string>;
}) {
  return (
    <Combobox<{
      label: string;
      value: string;
      description?: string | null;
    }>
      contentClassName={contentClassName}
      noSearch
      stayOpenOnChange
      variant="button"
      align="start"
      selectedValues={selectedMeasures ?? undefined}
      onChange={(_seriesName, option) => {
        if (option.value) {
          onChange(option.value);
        }
      }}
      options={availableMeasures.map((series) => ({
        label:
          measureToDisplayName?.get(series) ?? getMetricsLabel(series) ?? "",
        value: series,
      }))}
    >
      <Button Icon={Settings2} size="xs" className={buttonClassName} />
    </Combobox>
  );
}

export { MeasuresSelect };
