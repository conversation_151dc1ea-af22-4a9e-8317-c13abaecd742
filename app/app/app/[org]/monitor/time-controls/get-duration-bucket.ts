import {
  timeBucketToDuration,
  type TIME_BUCKET,
} from "#/ui/charts/time-bucket";
import type { TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

// min bin widths in px before readability gets unreasonably hard
const MIN_LINE_PX = 2.5;
const MIN_BAR_PX = 4;

/**
 * Get the time bucket to use for a given duration and viz type
 */
export const getDurationBucket = (
  duration: number,
  vizType: TimeseriesVizType = "lines",
  vizWidth = 800,
): TIME_BUCKET => {
  const msPerPx = duration / vizWidth;

  const minBucketPx = vizType === "bars" ? MIN_BAR_PX : MIN_LINE_PX;
  const minBucketDuration = minBucketPx * msPerPx;

  // pick the smallest bin that's bigger than the min
  if (timeBucketToDuration("second") > minBucketDuration) {
    return "second";
  }
  if (timeBucketToDuration("minute") > minBucketDuration) {
    return "minute";
  }
  if (timeBucketToDuration("hour") > minBucketDuration) {
    return "hour";
  }

  // else use our largest bucket 'day'
  return "day";
};
