import { useQueryState, parseAsString, parseAsStringLiteral } from "nuqs";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  type TimeSpan,
  timeFrameFromStrings,
  timeSpanToTimeFrame,
  DEFAULT_TIME_RANGE,
  type ChartTimeFrame,
  timeFrameToChartTimeFrame,
  TIME_RANGE_TO_MILLISECONDS,
  getNextLETimeRange,
  timeRangeEnumSchema,
} from "./time-range";
import { type MonitorViewOptions } from "@braintrust/typespecs";

import { getLiveRefreshInterval } from "./get-live-refresh-interval";
import { useQuery } from "@tanstack/react-query";
import { z } from "zod";

// hack to avoid requeries when new chart timeframe is close
const updateIfNotClose = (n: ChartTimeFrame) => {
  return (prev: ChartTimeFrame) => {
    const dS = Math.abs(n.start - prev.start);
    const dE = Math.abs(n.end - prev.end);

    // new reference
    if (dS > 200 || dE > 200) {
      return n;
    }

    // else do not update
    return prev;
  };
};

export const timingOptionsSchema = z.discriminatedUnion("spanType", [
  z.object({
    spanType: z.literal("frame"),
    rangeValue: z.literal(""),
    frameStart: z.string(),
    frameEnd: z.string(),
  }),
  z.object({
    spanType: z.literal("range"),
    rangeValue: timeRangeEnumSchema,
    frameStart: z.literal(""),
    frameEnd: z.literal(""),
  }),
]);
export type TimingOptions = z.infer<typeof timingOptionsSchema>;

export const defaultTimingOptions: TimingOptions = {
  spanType: "range",
  rangeValue: DEFAULT_TIME_RANGE.value,
  frameStart: "",
  frameEnd: "",
};

export const useTimeControls = () => {
  const [isLive, setIsLive] = useState<boolean>(true);

  const [spanType, setSpanType] = useQueryState(
    "spanType",
    parseAsStringLiteral(["range", "frame"]).withDefault(
      defaultTimingOptions.spanType,
    ),
  );
  const [rangeValue, setRangeValue] = useQueryState(
    "rangeValue",
    parseAsStringLiteral(timeRangeEnumSchema.options).withDefault(
      defaultTimingOptions.rangeValue,
    ),
  );
  const [frameStart, setFrameStart] = useQueryState(
    "frameStart",
    parseAsString.withDefault(defaultTimingOptions.frameStart),
  );
  const [frameStop, setFrameStop] = useQueryState(
    "frameEnd",
    parseAsString.withDefault(defaultTimingOptions.frameEnd),
  );

  const timeSpan: TimeSpan = useMemo(() => {
    if (spanType === "frame" && frameStart && frameStop) {
      return timeFrameFromStrings(frameStart, frameStop);
    }

    if (
      spanType === "range" &&
      typeof rangeValue === "string" &&
      timeRangeEnumSchema.options.includes(rangeValue)
    ) {
      return rangeValue;
    }

    // url is not valid, use default
    return DEFAULT_TIME_RANGE.value;
  }, [spanType, frameStart, frameStop, rangeValue]);

  const [chartTimeFrame, setChartTimeFrame] = useState<ChartTimeFrame>(
    timeFrameToChartTimeFrame(timeSpanToTimeFrame(timeSpan)),
  );

  const getChartTimeFrame = useCallback(() => {
    if (typeof timeSpan !== "string") {
      // explicit timeframe case
      return {
        start: timeSpan.start,
        end: timeSpan.end ?? new Date().getTime(),
      };
    }

    // live timeframe case
    let sanitizedRange = rangeValue;
    if (!timeRangeEnumSchema.options.includes(rangeValue)) {
      sanitizedRange = DEFAULT_TIME_RANGE.value;
    }

    const timeframe = timeSpanToTimeFrame(sanitizedRange);
    return timeFrameToChartTimeFrame(timeframe);
  }, [rangeValue, timeSpan]);

  const liveRefreshInterval = useMemo(() => {
    const duration =
      TIME_RANGE_TO_MILLISECONDS[rangeValue || DEFAULT_TIME_RANGE.value];
    const interval = getLiveRefreshInterval(duration);
    return interval;
  }, [rangeValue]);

  // use a dummy useQuery for polling updates and auto disable updates when page in background
  const { data: queryChartTimeFrame } = useQuery({
    queryKey: ["monitor-page", timeSpan, rangeValue],
    queryFn: getChartTimeFrame,
    // stale at refresh interval, else one hour if not live
    staleTime: isLive ? liveRefreshInterval : 3600_000,
    // poll refresh if live, else do not
    refetchInterval: isLive ? liveRefreshInterval : Infinity,
  });

  // update local chartTimeFrame when it changes from useQuery
  useEffect(() => {
    if (queryChartTimeFrame) {
      setChartTimeFrame(queryChartTimeFrame);
    }
  }, [queryChartTimeFrame]);

  const play = useCallback(
    (timeRange: string) => {
      setSpanType("range");
      const parsedTimeRange = timeRangeEnumSchema.safeParse(timeRange);
      setRangeValue(
        parsedTimeRange.success
          ? parsedTimeRange.data
          : DEFAULT_TIME_RANGE.value,
      );
      setFrameStart(null);
      setFrameStop(null);
      setIsLive(true);
    },
    [setSpanType, setRangeValue, setFrameStart, setFrameStop, setIsLive],
  );

  const pause = useCallback(
    (tf: ChartTimeFrame) => {
      setSpanType("frame");
      setRangeValue(null);
      setFrameStart(String(Math.round(tf.start)));
      setFrameStop(String(Math.round(tf.end)));
      setIsLive(false);
      setChartTimeFrame(updateIfNotClose(tf));
    },
    [
      setSpanType,
      setRangeValue,
      setFrameStart,
      setFrameStop,
      setIsLive,
      setChartTimeFrame,
    ],
  );

  const setExplicitTimeFrame = useCallback(
    (start: number, end: number) => {
      pause({ start, end });
    },
    [pause],
  );

  const setTimeSpan = useCallback(
    (timeSpan: TimeSpan) => {
      if (typeof timeSpan === "string") {
        play(timeSpan);
      } else if (timeSpan.start && timeSpan.end) {
        pause({
          start: timeSpan.start,
          end: timeSpan.end ?? new Date().getTime(),
        });
      }
    },
    [pause, play],
  );

  const loadMonitorViewOptions = useCallback(
    (viewOptions: MonitorViewOptions) => {
      const spanType = viewOptions.spanType ?? "range";
      if (spanType === "frame") {
        const tf = timeFrameFromStrings(
          viewOptions.frameStart ?? "",
          viewOptions.frameEnd ?? "",
        );
        pause({
          start: tf.start,
          end: tf.end ?? new Date().getTime(),
        });
      } else if (spanType === "range") {
        play(viewOptions.rangeValue ?? DEFAULT_TIME_RANGE.value);
      }
    },
    [pause, play],
  );

  // use the current chart timeframe on an external pause
  const externalPause = useCallback(() => {
    pause(chartTimeFrame);
  }, [pause, chartTimeFrame]);

  // use current range or default on an external play
  const externalPlay = useCallback(() => {
    const duration = chartTimeFrame.end - chartTimeFrame.start;
    const nextLarger = getNextLETimeRange(duration);
    play(nextLarger);
  }, [play, chartTimeFrame]);

  return {
    timeSpan,
    chartTimeFrame,
    setExplicitTimeFrame,
    loadMonitorViewOptions,
    setTimeSpan,
    isLive: isLive,
    pause: externalPause,
    play: externalPlay,
  };
};
