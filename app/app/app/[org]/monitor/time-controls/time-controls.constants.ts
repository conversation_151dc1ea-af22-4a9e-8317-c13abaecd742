// the duration at which we use different levels of precision for rounding
export const DAY_RESOLUTION_DURATION = 14 * 86_400_000; // two weeks
export const HOUR_RESOLUTION_DURATION = 2 * 86_400_000; // two days
export const MINUTE_RESOLUTION_DURATION = 3_600_000; // one hour
export const SECOND_RESOLUTION_DURATION = 0;

// limits when to show what in display
export const SHOW_YEAR_LIMIT = 30 * 24 * 60 * 60 * 1000; // 30 days
export const SHOW_TIME_LIMIT = 7 * 24 * 60 * 60 * 1000; // one week

// min duration we allow a timeframe to be selected to
export const MIN_TIMEFRAME_DURATION = 300_000; // 5 min
