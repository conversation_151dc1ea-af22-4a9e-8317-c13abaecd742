import {
  addHours,
  roundToNearestHours,
  roundToNearestMinutes,
  startOfDay,
  addMilliseconds,
  startOfSecond,
} from "date-fns";
import { UTCDate } from "@date-fns/utc";

import type { ChartTimeFrame } from "./time-range";
import {
  DAY_RESOLUTION_DURATION,
  HOUR_RESOLUTION_DURATION,
  MIN_TIMEFRAME_DURATION,
  MINUTE_RESOLUTION_DURATION,
} from "./time-controls.constants";

// Add 12 hours and get the start of the resulting day
function roundToNearestDay(date: Date) {
  return startOfDay(addHours(date, 12));
}

// Add 500ms, then use startOfSecond to "floor" it.
function roundToNearestSecond(date: Date) {
  return startOfSecond(addMilliseconds(date, 500));
}

/**
 * get a calendar rounded version of the timeframe
 * round more for longer durations than short ones
 * also enforces a 5 minute minimum timeframe
 */
export const getRoundedTimeFrame = (
  tf: ChartTimeFrame,
  isUTC: boolean = false,
): ChartTimeFrame => {
  let { start, end } = tf;

  // clamp any reversed timeframes to min time
  start = Math.min(start, end);

  const duration = end - start;

  // enforce min duration to span
  const durationToAdd = MIN_TIMEFRAME_DURATION - duration;
  if (durationToAdd > 0) {
    // expand equally on both sides, but end clamped to 'now'
    const now = new Date().getTime();
    const expandedEnd = Math.min(now, end + durationToAdd / 2);
    const startToExtend = durationToAdd - (expandedEnd - end);
    const expandedStart = start - startToExtend;

    end = expandedEnd;
    start = expandedStart;
  }

  const startDate = isUTC ? new UTCDate(start) : new Date(start);
  const endDate = isUTC ? new UTCDate(end) : new Date(end);

  // days for more than two weeks
  if (duration > DAY_RESOLUTION_DURATION) {
    return {
      start: roundToNearestDay(startDate).getTime(),
      end: roundToNearestDay(endDate).getTime(),
    };
  }

  // hours for more than two days
  if (duration > HOUR_RESOLUTION_DURATION) {
    return {
      start: roundToNearestHours(startDate).getTime(),
      end: roundToNearestHours(endDate).getTime(),
    };
  }

  // minutes for more than one hour
  if (duration > MINUTE_RESOLUTION_DURATION) {
    return {
      start: roundToNearestMinutes(startDate).getTime(),
      end: roundToNearestMinutes(endDate).getTime(),
    };
  }

  // else seconds
  return {
    start: roundToNearestSecond(startDate).getTime(),
    end: roundToNearestSecond(endDate).getTime(),
  };
};
