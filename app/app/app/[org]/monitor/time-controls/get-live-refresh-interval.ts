const MIN_INTERVAL = 10_000;
const MAX_INTERVAL = 86400_000;

// what ratio of span to refresh data
const REFRESH_FRACTION = 1 / 180;

/**
 * How frequently to refresh 'live' timespans
 * @param durationMS timespan duration (start - end) in milliseconds
 * @returns refresh interval in milliseconds
 */
export const getLiveRefreshInterval = (durationMS?: number) => {
  if (!durationMS) {
    return MAX_INTERVAL;
  }
  const targetInterval = Math.round(durationMS * REFRESH_FRACTION);
  return Math.max(MIN_INTERVAL, Math.min(MAX_INTERVAL, targetInterval));
};
