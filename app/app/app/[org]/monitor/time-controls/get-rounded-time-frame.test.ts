import { describe, it, expect } from "vitest";
import { getRoundedTimeFrame } from "./get-rounded-time-frame";
import {
  DAY_RESOLUTION_DURATION,
  HOUR_RESOLUTION_DURATION,
  MINUTE_RESOLUTION_DURATION,
} from "./time-controls.constants";
import { type ChartTimeFrame } from "./time-range";

describe("getRoundedTimeFrame", () => {
  // Test case 1: Duration > 2 weeks (rounds to nearest day)
  it("should round to the nearest day for durations longer than two weeks", () => {
    const tf: ChartTimeFrame = {
      start: new Date("2023-01-01T13:30:00.000Z").getTime(), // Rounds up
      end: new Date("2023-01-20T10:30:00.000Z").getTime(), // Rounds down
    };

    const result = getRoundedTimeFrame(tf, true);

    expect(result.start).toBe(new Date("2023-01-02T00:00:00.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-20T00:00:00.000Z").getTime());
  });

  // Test case 2: Duration > 2 days (rounds to nearest hour)
  it("should round to the nearest hour for durations between two days and two weeks", () => {
    const tf: ChartTimeFrame = {
      start: new Date("2023-01-01T12:45:00.000Z").getTime(), // Rounds up
      end: new Date("2023-01-04T12:15:00.000Z").getTime(), // Rounds down
    };

    const result = getRoundedTimeFrame(tf, true);

    expect(result.start).toBe(new Date("2023-01-01T13:00:00.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-04T12:00:00.000Z").getTime());
  });

  // Test case 3: Duration > 1 hour (rounds to nearest minute)
  it("should round to the nearest minute for durations between one hour and two days", () => {
    const tf: ChartTimeFrame = {
      start: new Date("2023-01-01T12:00:45.000Z").getTime(), // Rounds up
      end: new Date("2023-01-01T14:00:15.000Z").getTime(), // Rounds down
    };

    const result = getRoundedTimeFrame(tf, true);

    expect(result.start).toBe(new Date("2023-01-01T12:01:00.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-01T14:00:00.000Z").getTime());
  });

  // Test case 4: Duration <= 1 hour (rounds to nearest second)
  it("should round to the nearest second for durations one hour or less", () => {
    const tf: ChartTimeFrame = {
      start: new Date("2023-01-01T12:00:00.700Z").getTime(), // Rounds up
      end: new Date("2023-01-01T12:30:00.300Z").getTime(), // Rounds down
    };

    const result = getRoundedTimeFrame(tf, true);

    expect(result.start).toBe(new Date("2023-01-01T12:00:01.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-01T12:30:00.000Z").getTime());
  });

  // Test case 6: Edge case - exact duration boundaries
  it("should correctly handle exact duration boundaries", () => {
    // Exactly 2 weeks -> should fall into HOUR rounding
    let start = new Date("2023-01-01T12:35:00.000Z").getTime();
    let end = start + DAY_RESOLUTION_DURATION;
    let tf: ChartTimeFrame = { start, end };
    let result = getRoundedTimeFrame(tf, true);
    expect(result.start).toBe(new Date("2023-01-01T13:00:00.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-15T13:00:00.000Z").getTime());

    // Exactly 2 days -> should fall into MINUTE rounding
    start = new Date("2023-01-01T12:00:35.000Z").getTime();
    end = start + HOUR_RESOLUTION_DURATION;
    tf = { start, end };
    result = getRoundedTimeFrame(tf, true);
    expect(result.start).toBe(new Date("2023-01-01T12:01:00.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-03T12:01:00.000Z").getTime());

    // Exactly 1 hour -> should fall into SECOND rounding
    start = new Date("2023-01-01T12:00:00.600Z").getTime();
    end = start + MINUTE_RESOLUTION_DURATION;
    tf = { start, end };
    result = getRoundedTimeFrame(tf, true);
    expect(result.start).toBe(new Date("2023-01-01T12:00:01.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-01T13:00:01.000Z").getTime());
  });

  // Test case 7: reversed timespans
  it("reversed timeframes get clamped to min duration at earlier time", () => {
    const tf: ChartTimeFrame = {
      start: new Date("2023-01-01T12:30:00.000Z").getTime(),
      end: new Date("2023-01-01T11:30:00.000Z").getTime(),
    };

    const result = getRoundedTimeFrame(tf, true);

    expect(result.start).toBe(new Date("2023-01-01T11:27:30.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-01T11:32:30.000Z").getTime());
  });

  // Test case 8: small timespans
  it("small timespans have enforced min duration", () => {
    const tf: ChartTimeFrame = {
      start: new Date("2023-01-01T11:30:00.000Z").getTime(),
      end: new Date("2023-01-01T11:31:00.000Z").getTime(),
    };

    const result = getRoundedTimeFrame(tf, true);

    expect(result.start).toBe(new Date("2023-01-01T11:28:00.000Z").getTime());
    expect(result.end).toBe(new Date("2023-01-01T11:33:00.000Z").getTime());
  });
});
