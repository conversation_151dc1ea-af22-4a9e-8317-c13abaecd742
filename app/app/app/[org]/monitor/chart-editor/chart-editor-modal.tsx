import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
} from "#/ui/dialog";
import { useCallback, useMemo, useState } from "react";
import { cn } from "#/utils/classnames";
import {
  chartConfigSchema,
  type ChartConfig,
} from "../chart-config/custom-charts-schema";
import { MonitorCard, type MonitorCardProps } from "../card/monitor-card";
import { ChartEditorText } from "./chart-editor-text";
import { ChartVisualEditor } from "./chart-visual-editor";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "#/ui/tabs";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import type { ProjectSummary } from "../../org-actions";
import { chartConfigToMonitorCardConfig } from "../chart-config/chart-config-to-monitor-card-config";
import { nanoid } from "ai";
import { getPresetMonitorCard } from "../chart-config/get-preset-monitor-card";
import type { ChartSaveMode } from "../chart-config/use-custom-charts";
import type { DataObjectType } from "#/utils/btapi/btapi";
import { DEFAULT_EDITOR_CHART_CONFIG } from "./chart-editor.constants";
import isEqual from "lodash.isequal";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { searchToExpressions } from "../search-to-expressions";
import type { Search } from "#/utils/search/search";

export function CardEditorModal(
  props: {
    title: string;
    objectType: DataObjectType;
    objectIds: string[];
    project?: ProjectSummary;
    showEdit: boolean;
    initialChart?: ChartConfig;
    presetId?: string;
    saveNewMode: "new-first" | "new-next" | "new-last";
    onSave: (props: {
      chartConfig: ChartConfig;
      mode: ChartSaveMode;
      presetId?: string;
    }) => void;
    onClose?: () => void;
    onDelete?: () => void;
    search?: Search;
  } & Omit<MonitorCardProps, "cardConfig">,
) {
  const {
    onDelete,
    showEdit,
    title,
    project,
    saveNewMode,
    onSave,
    onClose,
    initialChart = DEFAULT_EDITOR_CHART_CONFIG,
    presetId,
    objectType,
    objectIds,
    search,
    ...rest
  } = props;

  const additionalFilters = useMemo(() => {
    return searchToExpressions(search, objectType);
  }, [search, objectType]);

  const enableTextEditor = useIsFeatureEnabled("chartTextEditor");

  const [editorContent, setEditorContent] = useState<string>(
    JSON.stringify(initialChart),
  );

  const [editorValidity, setEditorValidity] = useState<"valid" | "invalid">(
    "valid",
  );

  const [selectedPresetId, setSelectedPresetId] = useState<string | undefined>(
    presetId,
  );

  /** The monitor card config to live preview chart */
  const displayCard = useMemo(() => {
    if (selectedPresetId) {
      const preset = getPresetMonitorCard(selectedPresetId);
      if (preset) {
        return preset.config;
      }
    }

    try {
      const parsed = JSON.parse(editorContent);
      const config = chartConfigSchema.parse(parsed);
      return chartConfigToMonitorCardConfig(config, selectedPresetId);
    } catch {
      // todo - create some kind of invalid card config type
      // for now fallback to initial chart provided
      return chartConfigToMonitorCardConfig(initialChart, selectedPresetId);
    }
  }, [editorContent, initialChart, selectedPresetId]);

  /** our current chart config */
  const displayChart = useMemo(() => {
    try {
      const parsed = JSON.parse(editorContent);
      return chartConfigSchema.parse(parsed);
    } catch {
      // todo - create some kind of invalid card config type
      // for now fallback to initial chart provided
      return initialChart;
    }
  }, [editorContent, initialChart]);

  // if editor saved state will be different than initial
  const isDirty = useMemo(() => {
    if (presetId !== selectedPresetId) {
      return true;
    }
    return !isEqual(displayChart, initialChart);
  }, [displayChart, initialChart, presetId, selectedPresetId]);

  // soft prevent modal close when there are unsaved changes
  const [showCloseConfirm, setShowCloseConfirm] = useState<boolean>(false);

  // disable save buttons if editor state is invalid
  const disableSave = useMemo(() => {
    return editorValidity === "invalid";
  }, [editorValidity]);

  const handleOnSave = useCallback(
    (mode: ChartSaveMode) => {
      // append preset id with id to avoid id collisions
      const presetId = Boolean(selectedPresetId)
        ? `${selectedPresetId}-${nanoid()}`
        : undefined;

      onSave({
        chartConfig: displayChart,
        mode,
        presetId,
      });
    },
    [displayChart, onSave, selectedPresetId],
  );

  const onVisualEditorChange = useCallback((c: ChartConfig) => {
    setEditorContent(JSON.stringify(c));
  }, []);

  return (
    <>
      <Dialog
        open
        onOpenChange={() => {
          if (isDirty) {
            setShowCloseConfirm(true);
            return;
          }
          onClose?.();
        }}
      >
        <DialogContent
          className="flex flex-col gap-0 overflow-hidden p-0"
          style={{ width: "80vw", maxWidth: "80vw", height: "80vh" }}
        >
          <DialogHeader className="flex-none border-b p-5">
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <div className="flex flex-1 flex-col overflow-hidden lg:flex-row">
            {displayCard && (
              <MonitorCard
                {...rest}
                filters={additionalFilters}
                disableClick
                className="m-0 max-h-64 flex-1 rounded-none border-r p-6 lg:max-h-none"
                cardConfig={{ ...displayCard, header: undefined }}
                standaloneLayoutMode
                setCardState={() => {}}
              />
            )}
            <div className="relative flex-1 overflow-auto px-5 pb-5">
              <Tabs defaultValue="visual">
                {enableTextEditor && (
                  <TabsList className="sticky top-0 z-10 flex w-full items-end justify-start gap-6 rounded-none border-b border-primary-200 bg-background pb-0">
                    <TabsTrigger
                      value="visual"
                      className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
                    >
                      Configuration
                    </TabsTrigger>
                    <TabsTrigger
                      value="text"
                      className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
                    >
                      Raw config
                    </TabsTrigger>
                  </TabsList>
                )}
                <TabsContent
                  value="visual"
                  className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
                >
                  <ChartVisualEditor
                    config={displayChart}
                    onChange={onVisualEditorChange}
                    selectedPresetId={selectedPresetId}
                    setSelectedPresetId={setSelectedPresetId}
                    project={project}
                    objectType={objectType}
                    objectIds={objectIds}
                    chartTimeFrame={rest.chartTimeFrame}
                  />
                </TabsContent>
                <TabsContent
                  value="text"
                  className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
                >
                  <div
                    className={cn({
                      "border border-primary-200 bg-blue-50": enableTextEditor,
                      "bg-bad-50 dark:bg-bad-50/40":
                        editorValidity === "invalid",
                    })}
                  >
                    <ChartEditorText
                      config={displayChart}
                      onValidity={(v) => setEditorValidity(v)}
                      onChange={setEditorContent}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
          <DialogFooter className="flex-none border-t p-5">
            {showEdit && onDelete && (
              <Button
                type="submit"
                size="xs"
                variant="ghost"
                onClick={() => onDelete()}
              >
                Delete
              </Button>
            )}
            <span className="flex-1" />
            <Button
              type="submit"
              size="xs"
              disabled={disableSave}
              onClick={() => {
                handleOnSave(saveNewMode);
              }}
            >
              Save as new
            </Button>
            {showEdit && (
              <Button
                type="submit"
                size="xs"
                variant="primary"
                disabled={disableSave}
                onClick={() => {
                  handleOnSave("existing");
                }}
              >
                Save
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <ConfirmationDialog
        open={showCloseConfirm}
        onOpenChange={() => {
          setShowCloseConfirm(false);
        }}
        title="Unsaved chart changes"
        description="You have unsaved changes. Are you sure you want to close the editor?"
        confirmText="Close"
        onConfirm={() => {
          setShowCloseConfirm(false);
          onClose?.();
        }}
      />
    </>
  );
}
