import type {
  ChartConfig,
  MeasuresConfig,
} from "../chart-config/custom-charts-schema";

export const DEFAULT_EDITOR_CHART_MEASURE: MeasuresConfig[number] = {
  btql: "percentile(metrics.end-metrics.start, 0.95)",
  displayName: "p95",
};

export const DEFAULT_EDITOR_CHART_CONFIG: ChartConfig = {
  title: "New chart",
  definition: {
    type: "monitorTimeseries",
    measures: [DEFAULT_EDITOR_CHART_MEASURE],
    viz: {
      type: "timeseries",
      timeseriesVizType: "lines",
    },
  },
};
