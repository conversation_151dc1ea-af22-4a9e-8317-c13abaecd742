import { FilterEditor, type MinimalColumnData } from "#/ui/table/filter-editor";
import { HeaderIcons } from "#/ui/table/formatters/header-formatters";
import { useQueryFunc } from "#/utils/react-query";
import { makeBubble, type Search } from "#/utils/search/search";
import { BlocksIcon, Plus } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useOrg } from "#/utils/user";
import type { fetchProjectConfig } from "../../p/[project]/configuration/configuration-actions";
import { useClause<PERSON>hecker } from "#/utils/search-btql";
import type { ProjectSummary } from "../../org-actions";
import { Button } from "#/ui/button";
import AppliedFilters from "#/ui/applied-filters";
import type { FiltersConfig } from "../chart-config/custom-charts-schema";
import type { DataObjectType } from "#/utils/btapi/btapi";
import isEqual from "lodash.isequal";

const hardcodedCols = [
  "metadata",
  "scores",
  "metrics",
  "input",
  "output",
  "expected",
  "tags",
];

const MONITOR_FILTER_COLUMNS: MinimalColumnData<null>[] = hardcodedCols.map(
  (colName) => ({
    id: colName,
    columnDefId: colName,
    meta: {
      name: colName,
      path: [colName],
      header: () => null,
      type: "object",
    },
  }),
);

const MONITOR_FILTER_FORMATTERS = Object.fromEntries(
  hardcodedCols.map((name) => [
    name,
    { headerIcon: HeaderIcons[name] ?? BlocksIcon },
  ]),
);

interface ChartEditorFilterProps {
  filters?: FiltersConfig;
  objectType: DataObjectType;
  objectIds: string[];
  setFilters: (filters?: FiltersConfig) => void;
  project?: ProjectSummary;
}

export const ChartEditorFilter = ({
  filters,
  setFilters,
  objectType,
  objectIds,
  project,
}: ChartEditorFilterProps) => {
  const initialized = useRef<boolean>(false);
  const [search, setSearch] = useState<Search>({});

  // init filters
  useEffect(() => {
    if (initialized.current) {
      return;
    }
    initialized.current = true;

    if (!filters) {
      setSearch({});
      return;
    }

    setSearch({
      filter: filters
        .map((f) => ({
          text: f.btql,
          label: f.btql,
          type: "filter" as const,
          originType: "form" as const,
        }))
        .map((filterClause) => ({
          ...filterClause,
          bubble: makeBubble({
            clause: filterClause,
            setSearch,
          }),
        })),
    });
  }, [filters]);

  const searchFilters = useMemo(() => {
    if (!search.filter) {
      return undefined;
    }
    return search.filter.map((f) => ({ btql: f.text }));
  }, [search]);

  // sync parent filters state to local search state
  useEffect(() => {
    // without deep equality check there is an inf update loop
    if (!isEqual(filters, searchFilters)) {
      setFilters(searchFilters);
    }
  }, [filters, setFilters, searchFilters]);

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { clauseChecker } = useClauseChecker(objectType, false);

  const { name: orgName } = useOrg();

  const projectId = project?.project_id ?? "";
  const projectName = project?.project_name ?? "";

  const args = useMemo(
    () => ({ org_name: orgName, project_name: projectName }),
    [orgName, projectName],
  );

  const { data: config, invalidate: mutateConfig } = useQueryFunc<
    typeof fetchProjectConfig
  >({
    fName: "fetchProjectConfig",
    args,
  });

  const tagsProvidedProject = useMemo(() => {
    if (!projectName || !config) {
      return undefined;
    }
    return {
      projectId,
      projectName,
      config: config,
      mutateConfig,
    };
  }, [config, mutateConfig, projectId, projectName]);

  const selectableColumns = useMemo(() => {
    if (tagsProvidedProject) {
      return MONITOR_FILTER_COLUMNS;
    }
    return MONITOR_FILTER_COLUMNS.filter((col) => col.id !== "tags");
  }, [tagsProvidedProject]);

  return (
    <>
      <AppliedFilters
        matchesLast
        search={search}
        setSearch={setSearch}
        clauseChecker={clauseChecker}
        fromClause={objectType}
      />
      <FilterEditor
        selectableColumns={selectableColumns}
        formatters={MONITOR_FILTER_FORMATTERS}
        clauseChecker={clauseChecker}
        isOpen={isOpen}
        setOpen={setIsOpen}
        search={search}
        setSearch={setSearch}
        objectType={objectType}
        objectId={objectIds[0]}
        tagsProvidedProject={tagsProvidedProject}
      >
        <Button size="xs" variant="ghost" className="w-full" Icon={Plus}>
          Filter
        </Button>
      </FilterEditor>
    </>
  );
};
