import TextEditor from "#/ui/text-editor";
import { useCallback, useState } from "react";

import { json, jsonParseLinter } from "@codemirror/lang-json";

import { zodAutocomplete, zodLinter } from "./chart-editor-text-extensions";
import { autocompletion } from "@codemirror/autocomplete";
import { linter } from "@codemirror/lint";
import z from "zod";
import {
  chartConfigSchema,
  type ChartConfig,
} from "../chart-config/custom-charts-schema";
import { lineNumbers } from "@codemirror/view";
import { useDarkMode } from "#/utils/useDarkMode";
import { githubDarkInit, githubLightInit } from "@uiw/codemirror-theme-github";

const editorThemeSettings = {
  settings: { background: "transparent" },
};

interface ChartConfigEditorProps {
  config?: ChartConfig;
  onChange?: (newConfig: string) => void;
  onValidity?: (status: "valid" | "invalid") => void;
}

const safeJsonStringSchema = z.preprocess((arg) => {
  if (typeof arg !== "string") {
    return arg;
  }
  try {
    return JSON.parse(arg);
  } catch (error) {
    return arg;
  }
}, chartConfigSchema);

export const ChartEditorText = (props: ChartConfigEditorProps) => {
  const { config, onChange, onValidity } = props;

  const darkMode = useDarkMode();

  const [editorValue, setEditorValue] = useState<string | undefined>(
    JSON.stringify(config, undefined, 2),
  );

  const onSave = useCallback(
    (newValue: string | undefined) => {
      const result = safeJsonStringSchema.safeParse(newValue);
      if (result.success) {
        onChange?.(newValue ?? "");
      }
    },
    [onChange],
  );

  return (
    <TextEditor
      className="m-0 size-full p-0 text-sm"
      onSave={onSave}
      onChange={(newValue: string | undefined) => {
        const result = safeJsonStringSchema.safeParse(newValue);
        onValidity?.(result.success ? "valid" : "invalid");
        setEditorValue(newValue ?? "");
      }}
      autoFocus
      value={editorValue}
      extensions={[
        darkMode
          ? githubDarkInit(editorThemeSettings)
          : githubLightInit(editorThemeSettings),
        json(),
        linter(jsonParseLinter()),
        lineNumbers(),
        zodLinter(chartConfigSchema),
        autocompletion({
          override: [zodAutocomplete(chartConfigSchema)],
        }),
      ]}
      wrap
      styled
    />
  );
};
