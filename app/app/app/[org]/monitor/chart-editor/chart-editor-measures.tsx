import type { ChartConfig } from "../chart-config/custom-charts-schema";
import type { UseFormReturn } from "react-hook-form";
import { useCallback, useMemo } from "react";
import { But<PERSON> } from "#/ui/button";
import { ChartEditorMeasure } from "./chart-editor-measure";
import type { ProjectSummary } from "../../org-actions";
import { Plus } from "lucide-react";
import type { ChartTimeFrame } from "../time-controls/time-range";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { DEFAULT_EDITOR_CHART_MEASURE } from "./chart-editor.constants";
import { nanoid } from "ai";

// nicer to read default measure name in the form of 'name-n'
const getNextDisplayName = (lastName?: string) => {
  if (!lastName) {
    return "measure-1";
  }

  // try to see if ends in '-<<n>>'
  const parts = lastName.split("-");
  const last = parts.at(-1);
  if (parts.length > 1 && last) {
    if (last && Number.isFinite(parseInt(last))) {
      const n = parseInt(last);
      return `${parts.slice(0, -1).join("-")}-${n + 1}`;
    }
  }

  return `${lastName}-1`;
};

export const ChartEditorMeasures = ({
  form,
  objectType,
  objectIds,
  chartTimeFrame,
  projectSummary,
}: {
  form: UseFormReturn<ChartConfig>;
  objectType: DataObjectType;
  objectIds: string[];
  chartTimeFrame: ChartTimeFrame;
  projectSummary?: ProjectSummary;
}) => {
  const currentData = form.watch();
  const measures = currentData.definition?.measures;

  const addMeasure = useCallback(() => {
    if (!measures) {
      return;
    }

    const lastMeasure = measures.at(-1) ?? DEFAULT_EDITOR_CHART_MEASURE;

    // pick a naturally sequential display name
    const newDisplayName = getNextDisplayName(lastMeasure.displayName);

    const newMeasures = [
      ...measures,
      { ...lastMeasure, displayName: newDisplayName },
    ];
    form.setValue("definition.measures", newMeasures);
  }, [measures, form]);

  const deleteMeasure = useCallback(
    (measureIndex: number) => {
      if (!measures || measures.length <= 0) {
        return;
      }

      const newMeasures = [...measures];
      newMeasures.splice(measureIndex, 1);
      form.setValue("definition.measures", newMeasures);
    },
    [measures, form],
  );

  const onMeasureChange = useCallback(
    (
      measureIndex: number,
      newMeasure: { btql: string; displayName?: string },
    ) => {
      if (!measures) {
        return;
      }
      const newMeasures = [...measures];
      newMeasures[measureIndex] = { ...newMeasure };
      form.setValue("definition.measures", newMeasures);
    },
    [measures, form],
  );

  // any time the length of measures changes, regen stable react ids
  const measureIds = useMemo(() => {
    return new Array(measures.length).fill(undefined).map(() => nanoid());
  }, [measures.length]);

  return (
    <div className="flex flex-col gap-4">
      {measures.map((m, i, arr) => {
        return (
          <ChartEditorMeasure
            key={measureIds[i]}
            measure={m}
            objectType={objectType}
            objectIds={objectIds}
            projectSummary={projectSummary}
            chartTimeFrame={chartTimeFrame}
            deleteMeasure={() => deleteMeasure(i)}
            onChange={(m: { btql: string; displayName?: string }) =>
              onMeasureChange(i, m)
            }
          />
        );
      })}
      <Button size="xs" variant="ghost" Icon={Plus} onClick={addMeasure}>
        Measure
      </Button>
    </div>
  );
};
