import { useQuery } from "@tanstack/react-query";
import { StretchHorizontal } from "lucide-react";
import { useOrg } from "#/utils/user";
import * as Query from "#/utils/btql/query-builder";
import { fetchInferBtql } from "#/utils/btql/btql";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { Combobox } from "#/ui/combobox/combobox";
import { useMemo, useState } from "react";
import { useIsClient } from "#/utils/use-is-client";
import { escapeIdentPath } from "#/utils/btql/path-helpers";
import { Button } from "#/ui/button";
import { getTimeframeFilter } from "../card/get-timeframe-filter";
import type { ChartTimeFrame } from "../time-controls/time-range";
import type { DataObjectType } from "#/utils/btapi/btapi";

function ChartEditorGroupBySelect({
  objectType,
  objectIds,
  chartTimeFrame,
  value,
  onChange,
}: {
  objectType: DataObjectType;
  objectIds: string[];
  chartTimeFrame: ChartTimeFrame;
  value: string;
  onChange: (newValue: string | null) => void;
}) {
  const [search, setSearch] = useState<string | null>(null);

  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const schemaInference = useIsFeatureEnabled("schemaInference");

  const { getOrRefreshToken } = useSessionToken();

  const timeFilter = useMemo(
    () => getTimeframeFilter(chartTimeFrame),
    [chartTimeFrame],
  );

  const { data: inferSchemaData, isLoading: inferSchemaLoading } = useQuery({
    queryKey: [
      "inferChartEditorGroupBys",
      org.id,
      objectType,
      objectIds,
      timeFilter,
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) =>
      await fetchInferBtql({
        args: {
          query: {
            from: Query.from(objectType, objectIds),
            infer: [
              { op: "ident", name: ["metadata"] },
              { op: "ident", name: ["span_attributes"] },
              { op: "ident", name: ["input"] },
              { op: "ident", name: ["output"] },
              { op: "ident", name: ["expected"] },
              { op: "ident", name: ["error"] },
            ],
            filter: timeFilter,
            limit: 1000,
          },
          brainstoreRealtime: true,
        },
        btqlFlags,
        apiUrl: org.api_url,
        getOrRefreshToken,
        signal,
      }),
    gcTime: 1000 * 30,
    enabled: schemaInference,
  });

  const options = useMemo(() => {
    // If schema inference is enabled, only use the inferred schema data
    const inferOptions =
      inferSchemaData?.data
        ?.map((item) => ({
          label: item.name.join("."),
          value: escapeIdentPath(item.name),
          count: item.top_values.length ?? 0,
        }))
        .toSorted((a, b) => a.value.localeCompare(b.value)) ?? [];

    // ability to select any custom input
    if (search) {
      inferOptions.push({
        label: `Custom value '${search}'`,
        value: search,
        count: 0,
      });
    }

    return inferOptions;
  }, [inferSchemaData, search]);

  const isClient = useIsClient();

  return (
    <Combobox<{
      label: string;
      value: string;
    }>
      isLoading={isClient && inferSchemaLoading}
      modal
      side="bottom"
      align="start"
      className="max-h-[300px]"
      variant="button"
      disabled={!isClient || options.length === 0}
      searchPlaceholder="Select grouping"
      selectedValue={value}
      onChange={(newValue) => onChange(newValue ?? "")}
      onSearchChange={setSearch}
      options={options}
      renderComboboxDisplayLabel={({ label, value }) => {
        return <ButtonLabel label={value === "" ? undefined : label} />;
      }}
      placeholderLabel={<ButtonLabel />}
    >
      <Button size="xs" isDropdown className="flex-1 overflow-hidden">
        <span className="flex-1 truncate text-left">{value}</span>
      </Button>
    </Combobox>
  );
}

const ButtonLabel = ({ label }: { label?: string }) => {
  return (
    <span className="flex items-center gap-1 text-xs font-normal">
      <StretchHorizontal className="size-3" />
      {label && label !== "None" ? `Group by ${label}` : "Set group by"}
    </span>
  );
};

export { ChartEditorGroupBySelect };
