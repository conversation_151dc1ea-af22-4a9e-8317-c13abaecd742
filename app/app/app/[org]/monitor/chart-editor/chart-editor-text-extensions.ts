import {
  type CompletionContext,
  type CompletionResult,
} from "@codemirror/autocomplete";
import { jsonParseLinter } from "@codemirror/lang-json";
import { syntaxTree } from "@codemirror/language";
import { type Diagnostic, linter } from "@codemirror/lint";
import { type Extension } from "@codemirror/state";
import { type EditorView } from "@codemirror/view";
import z from "zod";

/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-assertions */

// --- Autocompletion Logic ---
export function zodAutocomplete(
  schema: z.ZodObject<any>,
): (context: CompletionContext) => CompletionResult | null {
  const allKeys = Object.keys(schema.shape);

  return (context: CompletionContext): CompletionResult | null => {
    const node = syntaxTree(context.state).resolveInner(context.pos, -1);

    // Suggest top-level keys
    if (node.name === "Object") {
      const existingKeys: string[] = [];
      node.getChildren("Property").forEach((prop) => {
        const keyNode = prop.getChild("PropertyName");
        if (keyNode) {
          const key = context.state
            .sliceDoc(keyNode.from, keyNode.to)
            .replace(/"/g, "");
          existingKeys.push(key);
        }
      });

      const availableKeys = allKeys.filter(
        (key) => !existingKeys.includes(key),
      );

      return {
        from: context.pos,
        options: availableKeys.map((key) => ({
          label: key,
          type: "property",
          apply: `"${key}": `,
          info:
            schema.shape[key].description ||
            `Type: ${schema.shape[key]._def.typeName}`,
        })),
      };
    }

    // Suggest enum values
    const propertyNode = node.parent;
    if (node.name === "String" && propertyNode?.name === "Property") {
      const propertyNameNode = propertyNode.getChild("PropertyName");
      if (!propertyNameNode) return null;

      const propertyName = context.state
        .sliceDoc(propertyNameNode.from, propertyNameNode.to)
        .replace(/"/g, "");
      const subSchema = schema.shape[propertyName];

      if (
        subSchema instanceof z.ZodEnum ||
        (subSchema instanceof z.ZodOptional &&
          subSchema.unwrap() instanceof z.ZodEnum)
      ) {
        const enumSchema =
          subSchema instanceof z.ZodOptional ? subSchema.unwrap() : subSchema;
        const enumOptions = (enumSchema as z.ZodEnum<[string, ...string[]]>)
          .options;

        return {
          from: node.from + 1,
          to: node.to - 1,
          options: enumOptions.map((val) => ({
            label: val,
            type: "enum",
            apply: val,
          })),
        };
      }
    }

    return null;
  };
}

export const zodLinter = (schema: z.ZodObject<any>): Extension => {
  const lintSource = (view: EditorView): readonly Diagnostic[] => {
    const { state } = view;
    const diagnostics: Diagnostic[] = [];

    const jsonDiagnostics = jsonParseLinter()(view);
    if (jsonDiagnostics.length > 0) {
      return jsonDiagnostics;
    }

    try {
      const jsonValue = JSON.parse(state.doc.toString());
      const result = schema.safeParse(jsonValue);

      if (!result.success) {
        result.error.issues.forEach((issue) => {
          diagnostics.push({
            from: 0, // Simplified position
            to: state.doc.length,
            severity: "error",
            message: `[${issue.path.join(".")}] ${issue.message}`,
          });
        });
      }
    } catch (e) {
      // This case should ideally be handled by the jsonParseLinter
    }

    return diagnostics;
  };
  return linter(lintSource);
};
