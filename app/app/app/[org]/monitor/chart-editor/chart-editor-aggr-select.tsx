import { Combobox } from "#/ui/combobox/combobox";
import { useMemo } from "react";

export const AGGR_FUNCTIONS = [
  "avg",
  "sum",
  "percentile",
  "count",
  "min",
  "max",
];

export function ChartEditorAggrSelect({
  value,
  onChange,
  buttonClassName,
}: {
  value: string;
  onChange: (newValue?: string) => void;
  buttonClassName?: string;
}) {
  const options = useMemo(() => {
    // If schema inference is enabled, only use the inferred schema data
    return AGGR_FUNCTIONS.map((aggr) => ({
      label: aggr,
      value: aggr,
    }));
  }, []);

  return (
    <Combobox<{
      label: string;
      value: string;
    }>
      modal
      side="bottom"
      align="start"
      className="max-h-[250px]"
      variant="button"
      buttonClassName={buttonClassName}
      buttonSize="xs"
      noSearch
      searchPlaceholder="Select aggregator function"
      selectedValue={value}
      onChange={(v) => onChange(v)}
      options={options}
      renderComboboxDisplayLabel={({ label, value }) => {
        return <ButtonLabel label={value === "" ? undefined : label} />;
      }}
      placeholderLabel={<ButtonLabel />}
    />
  );
}

const ButtonLabel = ({ label }: { label?: string }) => {
  return (
    <span className="flex items-center gap-1 text-xs font-normal">
      {label && label !== "None" ? label : "Select metric"}
    </span>
  );
};
