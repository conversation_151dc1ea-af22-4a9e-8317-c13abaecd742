import React, { use<PERSON><PERSON>back, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import {
  chartConfigSchema,
  unitTypeSchema,
  type ChartConfig,
} from "../chart-config/custom-charts-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { Input } from "#/ui/input";
import { ToggleGroup, ToggleGroupItem } from "#/ui/toggle-group";
import { Button } from "#/ui/button";
import {
  BarChart3,
  ChartLineIcon,
  ChartNoAxesColumnIncreasing,
  LineChart,
  ListFilter,
  Plus,
  Settings2,
  StretchHorizontal,
  Trash2,
} from "lucide-react";
import { ChartEditorFilter } from "./chart-editor-filter";
import type { ProjectSummary } from "../../org-actions";
import { ChartEditorGroupBySelect } from "./chart-editor-group-by-select";
import { ChartEditorMeasures } from "./chart-editor-measures";
import { CollapsibleSection } from "#/ui/collapsible-section";
import type { ChartTimeFrame } from "../time-controls/time-range";
import type { DataObjectType } from "#/utils/btapi/btapi";
import { ChartEditorPresets } from "./chart-editor-presets";
import { MONITOR_PRESET_CARDS } from "../card-config/monitor-cards.constants";
import { cn } from "#/utils/classnames";

const sectionClassName = "border-t-2 border-transparent";
const sectionExpandedClassName = "border-t-2 border-primary-100 rounded-t-none";

export const ChartVisualEditor = ({
  config,
  chartTimeFrame,
  objectType,
  objectIds,
  onChange,
  project,
  selectedPresetId,
  setSelectedPresetId,
}: {
  config: ChartConfig;
  chartTimeFrame: ChartTimeFrame;
  objectType: DataObjectType;
  objectIds: string[];
  onChange: (newConfig: ChartConfig) => void;
  project?: ProjectSummary;
  selectedPresetId?: string;
  setSelectedPresetId: (preset?: string) => void;
}) => {
  const form = useForm<ChartConfig>({
    resolver: zodResolver(chartConfigSchema),
    defaultValues: config,
  });

  const currentData = form.watch();

  useEffect(() => {
    onChange(currentData);
  }, [currentData, onChange]);

  const addGroupBy = useCallback(() => {
    const groupBys = currentData.definition?.groupBys ?? [];
    // todo - add display name option for group bys
    const lastGroupByBtql = groupBys.at(-1)?.btql ?? "metadata.model";
    const newGroupBys = [...groupBys, { btql: lastGroupByBtql }];
    form.setValue("definition.groupBys", newGroupBys);
  }, [currentData.definition?.groupBys, form]);

  const deleteGroupBy = useCallback(
    (groupByIndex: number) => {
      const groupBys = currentData.definition?.groupBys;
      if (!groupBys) {
        return;
      }
      const newGroupBys = [...groupBys];
      newGroupBys.splice(groupByIndex, 1);
      form.setValue("definition.groupBys", newGroupBys);
    },
    [currentData.definition?.groupBys, form],
  );

  const presetTitle = useMemo(() => {
    const card = MONITOR_PRESET_CARDS.get(selectedPresetId ?? "");
    return card?.header?.title ?? "Custom";
  }, [selectedPresetId]);

  return (
    <div className="mt-2 flex flex-col gap-5">
      <Form {...form}>
        <div>
          <CollapsibleSection
            title={
              <span className="flex flex-1 justify-between">
                Presets
                <span className="text-[10px] font-normal text-primary-500">
                  {presetTitle}
                </span>
              </span>
            }
            Icon={Settings2}
            className={sectionClassName}
            expandedClassName={sectionExpandedClassName}
            defaultCollapsed={!Boolean(selectedPresetId)}
          >
            <ChartEditorPresets
              selectedPresetId={selectedPresetId}
              onChange={setSelectedPresetId}
            />
          </CollapsibleSection>
        </div>
        <div className={cn("contents", { hidden: Boolean(selectedPresetId) })}>
          <div>
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Chart title</FormLabel>
                  <FormControl>
                    <Input type="text" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div>
            <CollapsibleSection
              title={
                <span className="flex flex-1 justify-between">
                  Measures
                  <span className="text-[10px] font-normal text-primary-500">
                    {form.getValues().definition?.measures.length ?? "None"}
                  </span>
                </span>
              }
              Icon={ChartNoAxesColumnIncreasing}
              className={sectionClassName}
              expandedClassName={sectionExpandedClassName}
            >
              <ChartEditorMeasures
                form={form}
                objectType={objectType}
                objectIds={objectIds}
                projectSummary={project}
                chartTimeFrame={chartTimeFrame}
              />
            </CollapsibleSection>
          </div>
          <div>
            <CollapsibleSection
              title={
                <span className="flex flex-1 justify-between">
                  Filters
                  <span className="text-[10px] font-normal text-primary-500">
                    {form.getValues().definition?.filters?.length || "None"}
                  </span>
                </span>
              }
              Icon={ListFilter}
              className={sectionClassName}
              expandedClassName={sectionExpandedClassName}
              defaultCollapsed={
                !Boolean(form.getValues().definition?.filters?.length)
              }
            >
              <ChartEditorFilter
                project={project}
                objectType={objectType}
                objectIds={objectIds}
                filters={form.getValues().definition?.filters}
                setFilters={(f) => form.setValue("definition.filters", f)}
              />
            </CollapsibleSection>
          </div>
          <div>
            <CollapsibleSection
              title={
                <span className="flex flex-1 justify-between">
                  Group by
                  <span className="text-[10px] font-normal text-primary-500">
                    {form.getValues().definition?.groupBys?.length ?? "None"}
                  </span>
                </span>
              }
              Icon={StretchHorizontal}
              className={sectionClassName}
              expandedClassName={sectionExpandedClassName}
              defaultCollapsed={
                !Boolean(form.getValues().definition?.groupBys?.length)
              }
            >
              <div className="flex flex-col gap-2">
                {(form.getValues().definition?.groupBys ?? []).map((_, i) => {
                  return (
                    <div className="flex w-full gap-2" key={i}>
                      <FormField
                        control={form.control}
                        name={`definition.groupBys.${i}.btql`}
                        render={({ field }) => (
                          <ChartEditorGroupBySelect
                            key={i}
                            objectType={objectType}
                            objectIds={objectIds}
                            value={field.value}
                            onChange={field.onChange}
                            chartTimeFrame={chartTimeFrame}
                          />
                        )}
                      />
                      <Button
                        Icon={Trash2}
                        size="xs"
                        variant="ghost"
                        className="flex-none text-primary-500"
                        onClick={() => deleteGroupBy(i)}
                      />
                    </div>
                  );
                })}
                <div>
                  <Button
                    size="xs"
                    variant="ghost"
                    className="w-full"
                    Icon={Plus}
                    onClick={addGroupBy}
                  >
                    Group by
                  </Button>
                </div>
              </div>
            </CollapsibleSection>
          </div>
          <div>
            <CollapsibleSection
              title="Viz options"
              Icon={ChartLineIcon}
              className={sectionClassName}
              expandedClassName={sectionExpandedClassName}
            >
              <div className="flex flex-wrap gap-6">
                <FormField
                  control={form.control}
                  name="definition.viz.timeseriesVizType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Visualization</FormLabel>
                      <FormControl>
                        <ToggleGroup
                          type="single"
                          className="flex w-full justify-start"
                          value={field.value}
                          onValueChange={(v) => v && field.onChange(v)}
                        >
                          <ToggleGroupItem
                            size="sm"
                            value="lines"
                            className="gap-2 border px-3 text-xs"
                          >
                            <LineChart className="size-3" />
                            Lines
                          </ToggleGroupItem>
                          <ToggleGroupItem
                            size="sm"
                            value="bars"
                            className="gap-2 border px-3 text-xs"
                          >
                            <BarChart3 className="size-3" />
                            Bars
                          </ToggleGroupItem>
                        </ToggleGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="definition.viz.unitType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Unit</FormLabel>
                      <FormControl>
                        <ToggleGroup
                          type="single"
                          className="h-8"
                          value={field.value || "not-set"}
                          onValueChange={(v) => field.onChange(v || undefined)}
                        >
                          {unitTypeSchema.options.map((unit, i) => {
                            return (
                              <ToggleGroupItem
                                size="sm"
                                value={unit}
                                key={i}
                                className="gap-2 border px-3 text-xs capitalize"
                              >
                                {unit}
                              </ToggleGroupItem>
                            );
                          })}
                        </ToggleGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CollapsibleSection>
          </div>
        </div>
      </Form>
    </div>
  );
};
