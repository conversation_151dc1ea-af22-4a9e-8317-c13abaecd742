import { getTextSearchInputFields } from "#/utils/get-text-search-input-fields";
import type { Search } from "#/utils/search/search";
import { type Expr } from "@braintrust/btql/parser";
import type { DataObjectType } from "#/utils/btapi/btapi";

export const searchToExpressions = (
  search: Search | null | undefined,
  from: DataObjectType,
) => {
  if (!search) {
    return [];
  }

  const filters: Expr[] = [];

  // conversion of search filters to Expr
  if (search.filter) {
    const filtersBtql = search.filter.map((f) => ({ btql: f.text }));
    filters.push(...filtersBtql);
  }

  // search text
  const searchFilter = search.match?.[0]?.text;
  if (searchFilter) {
    // search filter is true is when any field matches the searchFilter
    const fieldsToSearch = getTextSearchInputFields(from);
    const searchFilterExpressions: Expr[] = fieldsToSearch.map((f) => ({
      left: {
        name: [f],
        op: "ident",
      },
      op: "match",
      right: {
        op: "literal",
        value: searchFilter,
      },
    }));

    // use OR for any field match
    const searchFilterOr: Expr = {
      children: searchFilterExpressions,
      op: "or",
    };

    // then appended to filters (which is all AND)
    filters.push(searchFilterOr);
  }

  return filters;
};
