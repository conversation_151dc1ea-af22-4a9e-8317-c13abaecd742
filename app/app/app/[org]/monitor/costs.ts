import { isEmpty } from "#/utils/object";
import { type ModelSpec } from "@braintrust/proxy/schema";

type AvailableModelsType = { [name: string]: ModelSpec };

export type CostSummary = CostDetails & { time: number };
export type CostDetails = {
  promptUncachedTokensCost: number;
  promptCachedTokensCost: number;
  promptCacheCreationTokensCost: number;
  completionTokensCost: number;
  totalCost: number;
  count: bigint;
  group?: string;
};

export type CostData = {
  count: bigint | number;
  prompt_uncached_tokens?: number | bigint | null;
  prompt_cached_tokens?: number | bigint | null;
  prompt_cache_creation_tokens?: number | bigint | null;
  completion_tokens?: number | bigint | null;
  model?: string | null;
  time: number;
  group?: string;
}[];

type CostDetailsByTime = {
  [time: string]: CostDetails;
};

function calculateTokenCosts(
  tokens: number | bigint,
  costPerMilTokens: number | null | undefined,
): number {
  return Number(tokens) * ((costPerMilTokens ?? 0) / 1_000_000);
}

const BIG_ZERO = BigInt(0);

export function crunchOneCost({
  allAvailableModels,
  data: {
    model,
    prompt_uncached_tokens,
    prompt_cached_tokens,
    prompt_cache_creation_tokens,
    completion_tokens,
    count,
  },
}: {
  allAvailableModels: AvailableModelsType;
  data: Omit<CostData[number], "time" | "group">;
}): CostDetails | null {
  if (!model || !allAvailableModels[model]) {
    return null;
  }
  const modelSchema = allAvailableModels[model];

  const promptUncachedTokensCost = calculateTokenCosts(
    prompt_uncached_tokens ?? BIG_ZERO,
    modelSchema.input_cost_per_mil_tokens,
  );
  const promptCacheCreationTokensCost = calculateTokenCosts(
    prompt_cache_creation_tokens ?? BIG_ZERO,
    modelSchema.input_cache_write_cost_per_mil_tokens,
  );
  const promptCachedTokensCost = calculateTokenCosts(
    prompt_cached_tokens ?? BIG_ZERO,
    modelSchema.input_cache_read_cost_per_mil_tokens,
  );
  const completionTokensCost = calculateTokenCosts(
    completion_tokens ?? BIG_ZERO,
    modelSchema.output_cost_per_mil_tokens,
  );
  const totalCost =
    promptUncachedTokensCost +
    promptCacheCreationTokensCost +
    promptCachedTokensCost +
    completionTokensCost;

  return {
    promptUncachedTokensCost,
    promptCacheCreationTokensCost,
    promptCachedTokensCost,
    completionTokensCost,
    totalCost,
    count: BigInt(count),
  };
}

export function crunchCosts({
  costData,
  allAvailableModels,
}: {
  costData: CostData | undefined;
  allAvailableModels: AvailableModelsType;
}): CostSummary[] | null {
  if (!costData) {
    return null;
  }

  const crunchedCosts: CostSummary[] = [];
  costData.forEach((item) => {
    const { time, group } = item;

    const costData = crunchOneCost({
      allAvailableModels,
      data: item,
    });

    if (costData) {
      crunchedCosts.push({
        ...costData,
        time,
        group,
      });
    }
  });

  return crunchedCosts;
}

export function crunchCostsByTime({
  costData,
  allAvailableModels,
}: {
  costData: CostData | undefined;
  allAvailableModels: AvailableModelsType;
}): CostSummary[] | null {
  if (!costData) {
    return null;
  }

  const costDetailsByTime: CostDetailsByTime = {};

  for (const item of costData) {
    const {
      time,
      model,
      prompt_uncached_tokens,
      prompt_cached_tokens,
      prompt_cache_creation_tokens,
      completion_tokens,
      count,
    } = item;

    if (isEmpty(time)) {
      continue;
    }

    if (!costDetailsByTime[time]) {
      costDetailsByTime[time] = {
        promptUncachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        promptCachedTokensCost: 0,
        completionTokensCost: 0,
        totalCost: 0,
        count: BigInt(0),
      };
    }

    if (isEmpty(model) || !allAvailableModels[model]) continue;

    const modelSchema = allAvailableModels[model];
    const promptUncachedTokensCost = calculateTokenCosts(
      prompt_uncached_tokens ?? BIG_ZERO,
      modelSchema.input_cost_per_mil_tokens,
    );
    const promptCachedTokensCost = calculateTokenCosts(
      prompt_cached_tokens ?? BIG_ZERO,
      modelSchema.input_cache_read_cost_per_mil_tokens,
    );
    const promptCacheCreationTokensCost = calculateTokenCosts(
      prompt_cache_creation_tokens ?? BIG_ZERO,
      modelSchema.input_cache_write_cost_per_mil_tokens,
    );
    const completionTokensCost = calculateTokenCosts(
      completion_tokens ?? BIG_ZERO,
      modelSchema.output_cost_per_mil_tokens,
    );
    const totalCost =
      promptUncachedTokensCost +
      promptCacheCreationTokensCost +
      promptCachedTokensCost +
      completionTokensCost;

    costDetailsByTime[time].promptUncachedTokensCost +=
      promptUncachedTokensCost;
    costDetailsByTime[time].promptCachedTokensCost += promptCachedTokensCost;
    costDetailsByTime[time].promptCacheCreationTokensCost +=
      promptCacheCreationTokensCost;
    costDetailsByTime[time].completionTokensCost += completionTokensCost;

    costDetailsByTime[time].totalCost += totalCost;
    costDetailsByTime[time].count += BigInt(count);
  }

  return Object.entries(costDetailsByTime).map(
    ([
      time,
      {
        promptUncachedTokensCost,
        promptCacheCreationTokensCost,
        promptCachedTokensCost,
        completionTokensCost,
        totalCost,
        count,
        group,
      },
    ]) => ({
      time: Number(time),
      promptUncachedTokensCost,
      promptCacheCreationTokensCost,
      promptCachedTokensCost,
      completionTokensCost,
      totalCost,
      count,
      group,
    }),
  );
}

export function crunchCostsByTimeGroup({
  costData,
  allAvailableModels,
}: {
  costData: CostData | undefined;
  allAvailableModels: AvailableModelsType;
}): CostSummary[] | null {
  if (!costData) {
    return null;
  }

  const costDetailsByTimeGroup: Map<string, CostDetails & { time: number }> =
    new Map();
  for (const item of costData) {
    const {
      time,
      model,
      prompt_uncached_tokens,
      prompt_cached_tokens,
      prompt_cache_creation_tokens,
      completion_tokens,
      count,
      group,
    } = item;

    const bucketKey = `${time}-${group ?? ""}`;
    const bucket: CostDetails & { time: number } = costDetailsByTimeGroup.get(
      bucketKey,
    ) ?? {
      promptUncachedTokensCost: 0,
      promptCacheCreationTokensCost: 0,
      promptCachedTokensCost: 0,
      completionTokensCost: 0,
      totalCost: 0,
      count: BigInt(0),
      time,
      group,
    };

    if (isEmpty(model) || !allAvailableModels[model]) continue;

    const modelSchema = allAvailableModels[model];
    const promptUncachedTokensCost = calculateTokenCosts(
      prompt_uncached_tokens ?? BIG_ZERO,
      modelSchema.input_cost_per_mil_tokens,
    );
    const promptCachedTokensCost = calculateTokenCosts(
      prompt_cached_tokens ?? BIG_ZERO,
      modelSchema.input_cache_read_cost_per_mil_tokens,
    );
    const promptCacheCreationTokensCost = calculateTokenCosts(
      prompt_cache_creation_tokens ?? BIG_ZERO,
      modelSchema.input_cache_write_cost_per_mil_tokens,
    );
    const completionTokensCost = calculateTokenCosts(
      completion_tokens ?? BIG_ZERO,
      modelSchema.output_cost_per_mil_tokens,
    );

    bucket.promptUncachedTokensCost += promptUncachedTokensCost;
    bucket.promptCachedTokensCost += promptCachedTokensCost;
    bucket.promptCacheCreationTokensCost += promptCacheCreationTokensCost;
    bucket.completionTokensCost += completionTokensCost;
    bucket.count += BigInt(count);
    costDetailsByTimeGroup.set(bucketKey, bucket);
  }

  return [...costDetailsByTimeGroup.values()].map(
    ({
      promptUncachedTokensCost,
      promptCacheCreationTokensCost,
      promptCachedTokensCost,
      completionTokensCost,
      totalCost,
      count,
      group,
      time,
    }) => ({
      promptUncachedTokensCost,
      promptCacheCreationTokensCost,
      promptCachedTokensCost,
      completionTokensCost,
      totalCost,
      count,
      group,
      time,
    }),
  );
}
