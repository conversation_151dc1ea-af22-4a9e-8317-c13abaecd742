import { describe, expect, test } from "vitest";
import { getQueryTimeframe } from "./get-query-timeframe";

describe("getQueryTimeframe", () => {
  test("typical case", () => {
    const tf = {
      start: new Date("2025-07-03T17:03:16.346Z").getTime(),
      end: new Date("2025-07-10T17:09:06.346Z").getTime(),
    };
    expect(getQueryTimeframe(tf, "minute")).toEqual({
      start: new Date("2025-07-03T17:03:00.000Z").getTime(),
      end: new Date("2025-07-10T17:09:20.000Z").getTime(),
    });
    expect(getQueryTimeframe(tf, "hour")).toEqual({
      start: new Date("2025-07-03T17:00:00.000Z").getTime(),
      end: new Date("2025-07-10T17:10:00.000Z").getTime(),
    });
    expect(getQueryTimeframe(tf, "day")).toEqual({
      start: new Date("2025-07-03T17:00:00.000Z").getTime(),
      end: new Date("2025-07-10T18:00:00.000Z").getTime(),
    });
  });

  test('should return the same timeframe if already aligned with the "minute" step', () => {
    const timeframe = {
      start: 1678886400000, // Multiple of 20,000
      end: 1678886420000, // Multiple of 20,000
    };

    const result = getQueryTimeframe(timeframe, "minute");
    expect(result.start).toBe(timeframe.start);
    expect(result.end).toBe(timeframe.end);
  });

  test('should expand to cover the full step range for "hour" bucket', () => {
    const timeframe = {
      start: 1678886999999, // Just before a 10-min mark
      end: 1678887000001, // Just after a 10-min mark
    };
    const result = getQueryTimeframe(timeframe, "hour");
    expect(result.start).toBe(1678886400000);
    expect(result.end).toBe(1678887600000);
  });

  test("should handle identical start and end times", () => {
    const timestamp = 1678886410000;
    const timeframe = { start: timestamp, end: timestamp };
    const result = getQueryTimeframe(timeframe, "minute");
    expect(result).toEqual({ start: 1678886400000, end: 1678886420000 });
  });

  test("should handle a zero timeframe", () => {
    const timeframe = { start: 0, end: 0 };
    const result = getQueryTimeframe(timeframe, "hour");
    expect(result).toEqual({ start: 0, end: 0 });
  });

  test("should handle start time being after end time, even though this should not happen", () => {
    const timeframe = { start: 1678887000000, end: 1678886410000 };
    const result = getQueryTimeframe(timeframe, "day");

    // The logic still applies rounding down start and rounding up end, even if swapped.
    expect(result).toEqual({ start: 1678885200000, end: 1678888800000 });
  });
});
