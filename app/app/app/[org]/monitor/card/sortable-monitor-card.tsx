import { cn } from "#/utils/classnames";
import { useSortable } from "@dnd-kit/sortable";
import { MonitorCard, type MonitorCardProps } from "./monitor-card";
import { CSS } from "@dnd-kit/utilities";
import type { CardState } from "../monitor-cards";
import { useCallback, useEffect, useMemo } from "react";
import { type ChartConfig } from "../chart-config/custom-charts-schema";
import { chartConfigToMonitorCardConfig } from "../chart-config/chart-config-to-monitor-card-config";
import { setGlobalDisableInteractivityAtom } from "#/ui/charts/store/atoms";
import { useAtom } from "jotai";

const fullscreenClassName = "flex h-full p-2";
const gridElementClassName = "w-full grow flex h-72 md:h-[340px]";

export const SortableMonitorCard = ({
  isFullscreen,
  commonProps,
  chartId,
  chartConfig,
  pageInFullscreen,
  dragEnabled,
  setCardState,
  onEdit,
}: {
  isFullscreen: boolean;
  commonProps: Omit<MonitorCardProps, "setCardState" | "cardConfig">;
  chartId: string;
  chartConfig?: ChartConfig;
  pageInFullscreen: boolean;
  dragEnabled: boolean;
  setCardState: (state: CardState, chartId: string) => void;
  onEdit?: (chartId: string) => void;
}) => {
  const setCardStateLocal = useCallback(
    (cardState: CardState) => {
      setCardState(cardState, chartId);
    },
    [chartId, setCardState],
  );

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: chartId, disabled: !dragEnabled });

  // disable all card interactivity during drag
  const [_, setDisableInteractivity] = useAtom(
    setGlobalDisableInteractivityAtom,
  );
  useEffect(() => {
    if (isDragging) {
      setDisableInteractivity(true);
    } else {
      setDisableInteractivity(false);
    }
    return () => setDisableInteractivity(false);
  }, [setDisableInteractivity, isDragging]);

  // Style for the item, applying transform and transition for smooth animation
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  /** in future we will fork to different card types here */
  const cardConfig = useMemo(() => {
    return chartConfigToMonitorCardConfig(chartConfig, chartId);
  }, [chartId, chartConfig]);

  return (
    <div
      ref={setNodeRef}
      key={chartId}
      style={style}
      {...attributes}
      // note that dnd role "button" collides with our css a bit
      className={cn("flex cursor-default flex-col", {
        "w-full": isFullscreen,
        "z-10": isDragging,
      })}
    >
      <div
        className={cn({
          [gridElementClassName]: !isFullscreen,
          [fullscreenClassName]: isFullscreen,
          hidden: pageInFullscreen && !isFullscreen,
        })}
      >
        <MonitorCard
          {...commonProps}
          cardConfig={cardConfig}
          chartId={chartId}
          setCardState={setCardStateLocal}
          standaloneLayoutMode={isFullscreen}
          dragListeners={dragEnabled ? listeners : undefined}
          isDragging={isDragging}
          onEdit={onEdit ? () => onEdit(chartId) : undefined}
        />
      </div>
    </div>
  );
};
