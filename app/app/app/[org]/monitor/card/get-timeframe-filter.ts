import type { Expr } from "@braintrust/btql/parser";
import type { ChartTimeFrame } from "../time-controls/time-range";
import { CreatedField } from "#/utils/duckdb";
import * as Query from "#/utils/btql/query-builder";
import { getRoundedTimeFrame } from "../time-controls/get-rounded-time-frame";

// get the btql expr for filtering by timeframe
export const getTimeframeFilter = (timeframe: ChartTimeFrame): Expr => {
  // round to make query cache a bit more stable
  const { start, end } = getRoundedTimeFrame(timeframe);
  const startFilter = {
    op: "ge" as const,
    left: Query.ident(CreatedField),
    right: {
      op: "literal" as const,
      value: new Date(start).toISOString(),
    },
  };

  const endFilter = {
    op: "lt" as const,
    left: Query.ident(CreatedField),
    right: {
      op: "literal" as const,
      value: new Date(end).toISOString(),
    },
  };

  return Query.and(startFilter, endFilter);
};
