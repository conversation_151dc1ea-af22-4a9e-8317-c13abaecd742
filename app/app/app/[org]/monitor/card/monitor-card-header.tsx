import { But<PERSON> } from "#/ui/button";
import { useFullscreenMonitorCardState } from "#/ui/query-parameters";
import { Expand, GripVertical, PencilIcon, Shrink } from "lucide-react";
import { type CardIconName } from "./monitor-card-config.types";
import { MonitorCardIcon } from "./monitor-card-icon";
import { MeasuresSelect } from "../measures-select";
import { cn } from "#/utils/classnames";
import type { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";

interface MonitorCardHeaderProps {
  title: string;
  iconName: CardIconName;
  chartId: string;
  showMeasuresSelect: boolean;
  selectedMeasures: string[];
  availableMeasures: string[];
  measureToDisplayName?: Map<string, string>;
  showReset: boolean;
  onReset: () => void;
  onSelectionChange?: (seriesName: string) => void;
  dragListeners?: SyntheticListenerMap;
  size?: "sm" | "md" | "lg";
  onEdit?: () => void;
}

export const MonitorCardHeader = (props: MonitorCardHeaderProps) => {
  const {
    title,
    iconName,
    chartId,
    showReset,
    onReset,
    showMeasuresSelect,
    selectedMeasures,
    availableMeasures,
    onSelectionChange,
    measureToDisplayName,
    dragListeners,
    size,
    onEdit,
  } = props;

  const [fullscreenCard, setFullscreenCard] = useFullscreenMonitorCardState();
  const isFullscreen = fullscreenCard === chartId;

  const Icon = MonitorCardIcon(iconName);

  const cardIsDraggable = Boolean(dragListeners) && !isFullscreen;

  // transition icon opacity
  const iconAnimationClass =
    "text-primary-400 opacity-0 transition-opacity duration-100 group-hover:opacity-100";

  return (
    <div className="mb-5 flex w-full items-center gap-1 overflow-hidden">
      <div
        className={cn(
          "box-border flex h-7 min-w-0 flex-1 items-center gap-2 text-sm",
          {
            "text-xs": size === "sm",
            "text-base": size === "lg",
            "cursor-grab touch-none select-none active:cursor-grabbing":
              cardIsDraggable,
          },
        )}
        {...dragListeners}
      >
        {iconName && (
          <div className="flex w-4 items-center justify-center">
            <Icon
              className={cn("size-4", {
                "size-3": size === "sm",
                "group-hover:hidden": cardIsDraggable && !isFullscreen,
              })}
            />
            {cardIsDraggable && !isFullscreen && (
              <Button
                variant="ghost"
                transparent
                size="xs"
                title="Drag chart"
                className="hidden cursor-grab text-primary-400 group-hover:flex"
                Icon={GripVertical}
                {...dragListeners}
              />
            )}
          </div>
        )}
        <div className="truncate text-primary-800">{title}</div>
      </div>
      <div
        className={cn(
          "box-border flex items-center gap-1",
          {
            "max-w-0 transition-[max-width] duration-0 group-hover:max-w-full":
              !isFullscreen,
          },
          "[&:has([data-state=open])]:max-w-full",
        )}
      >
        {showReset && (
          <Button
            size="xs"
            variant="ghost"
            className={isFullscreen ? undefined : iconAnimationClass}
            onClick={onReset}
          >
            Reset
          </Button>
        )}
        <Button
          variant={isFullscreen ? "inverted" : "ghost"}
          size="xs"
          title="Enter fullscreen mode"
          className={isFullscreen ? undefined : iconAnimationClass}
          onClick={() => {
            setFullscreenCard(isFullscreen ? null : chartId);
          }}
          Icon={isFullscreen ? Shrink : Expand}
        >
          {isFullscreen ? "Exit fullscreen" : null}
        </Button>
        {onEdit && !isFullscreen && (
          <Button
            size="xs"
            title="Edit chart"
            variant="ghost"
            className={iconAnimationClass}
            onClick={onEdit}
            Icon={PencilIcon}
          />
        )}
        {showMeasuresSelect && (
          <MeasuresSelect
            contentClassName="w-32"
            buttonClassName={isFullscreen ? undefined : iconAnimationClass}
            selectedMeasures={selectedMeasures}
            availableMeasures={availableMeasures}
            onChange={onSelectionChange ?? (() => {})}
            measureToDisplayName={measureToDisplayName}
          />
        )}
      </div>
    </div>
  );
};
