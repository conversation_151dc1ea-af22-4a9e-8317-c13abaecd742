import { COST_CARD_CONFIG } from "./cost-card.constants";
import { LATENCY_CARD_CONFIG } from "./latency-card.constants";
import { REQUEST_COUNT_CARD_CONFIG } from "./request-count-card.constants";
import { SCORES_CARD_CONFIG } from "./scores-card.constants";
import { TOKEN_COUNT_CARD_CONFIG } from "./token-count-card.constants";
import { TOOL_DURATION_CARD_CONFIG } from "./tool-duration-card.constants";
import { TOOL_ERROR_RATE_CARD_CONFIG } from "./tool-error-rate.constants";
import { TOOL_REQUESTS_CARD_CONFIG } from "./tool-requests-card.constants";
import { TTFT_CARD_CONFIG } from "./ttft-card.constants";

// Preset cards are the default cards we show in the monitor page
// Some of these aren't representable as chart config schemas,
// so we only store the ids, and the monitor config lives in code.
// These ids will get persisted in views so we can't change them
export const MONITOR_PRESET_CARDS = new Map([
  ["__preset_card_request_count", REQUEST_COUNT_CARD_CONFIG],
  ["__preset_card_latency", LATENCY_CARD_CONFIG],
  ["__preset_card_cost", COST_CARD_CONFIG],
  ["__preset_card_token_count", TOKEN_COUNT_CARD_CONFIG],
  ["__preset_card_ttf", TTFT_CARD_CONFIG],
  ["__preset_card_scores", SCORES_CARD_CONFIG],
  ["__preset_card_tool_requests", TOOL_REQUESTS_CARD_CONFIG],
  ["__preset_card_tool_error_rate", TOOL_ERROR_RATE_CARD_CONFIG],
  ["__preset_card_tool_duration", TOOL_DURATION_CARD_CONFIG],
]);

// prefix for group by key
export const CARD_GROUP_BY_PREFIX = "__group_by_";
