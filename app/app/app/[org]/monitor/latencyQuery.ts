import { useMemo } from "react";
import { useIsRootBtqlSnippet } from "#/utils/btql/btql";
import { useMonitorQuery } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

export type LatencyMetricsData = {
  time: number;
  count: bigint;
  p50_duration: number | null;
  p95_duration: number | null;
  group?: string | null;
};

export type DurationMetrics = Extract<
  keyof LatencyMetricsData,
  "p50_duration" | "p95_duration"
>;

export function useLatencyQuery(
  projectIds: string[],
  {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  }: {
    chartTimeFrame: ChartTimeFrame;
    timeBucket: "minute" | "hour" | "day";
    groupBy?: string;
    experimentIds: string[];
    from: "project_logs" | "experiment";
  },
): {
  data: LatencyMetricsData[] | undefined;
  loading: boolean;
  error: Error | undefined;
} {
  const isRootBtqlSnippet = useIsRootBtqlSnippet();
  const durationExpr = groupBy
    ? "metrics.end-metrics.start"
    : `${isRootBtqlSnippet} ? metrics.end-metrics.start : null`;

  const measures = useMemo(
    () => [
      {
        alias: "count",
        expr: { btql: `sum(${isRootBtqlSnippet})` },
      },
      {
        alias: "p50_duration",
        expr: {
          btql: `percentile(${durationExpr}, 0.5)`,
        },
      },
      {
        alias: "p95_duration",
        expr: {
          btql: `percentile(${durationExpr}, 0.95)`,
        },
      },
    ],
    [isRootBtqlSnippet, durationExpr],
  );

  const dimensions = useMemo(
    () =>
      groupBy
        ? [
            {
              alias: "group",
              expr: { btql: `metadata.${groupBy}` },
            },
          ]
        : undefined,
    [groupBy],
  );

  return useMonitorQuery<LatencyMetricsData>("Latency query", projectIds, {
    chartTimeFrame,
    timeBucket,
    measures,
    dimensions,
    from,
    experimentIds,
  });
}
