import { useIsRootBtqlSnippet } from "#/utils/btql/btql";
import { useMemo } from "react";
import { useMonitorQuery } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

export type TTFTMetricsData = {
  time: number;
  count: bigint;
  p50_time_to_first_token: number | null;
  p95_time_to_first_token: number | null;
};

export type TTFTMetrics = Extract<
  keyof TTFTMetricsData,
  "p50_time_to_first_token" | "p95_time_to_first_token"
>;

export function useTTFTQuery(
  projectIds: string[],
  {
    chartTimeFrame,
    timeBucket,
    groupBy,
    from,
    experimentIds,
  }: {
    chartTimeFrame: ChartTimeFrame;
    timeBucket: "minute" | "hour" | "day";
    groupBy?: string;
    from: "project_logs" | "experiment";
    experimentIds: string[];
  },
): { data: TTFTMetricsData[] | undefined; loading: boolean } {
  const isRootBtqlSnippet = useIsRootBtqlSnippet();
  const measures = useMemo(
    () => [
      {
        alias: "count",
        expr: { btql: `sum(${isRootBtqlSnippet})` },
      },
      {
        alias: "p50_time_to_first_token",
        expr: {
          btql: `percentile(metrics.time_to_first_token, 0.5)`,
        },
      },
      {
        alias: "p95_time_to_first_token",
        expr: {
          btql: `percentile(metrics.time_to_first_token, 0.95)`,
        },
      },
    ],
    [isRootBtqlSnippet],
  );

  const dimensions = useMemo(
    () =>
      groupBy
        ? [
            {
              alias: "group",
              expr: { btql: `metadata.${groupBy}` },
            },
          ]
        : undefined,
    [groupBy],
  );

  return useMonitorQuery<TTFTMetricsData>("TTFT query", projectIds, {
    chartTimeFrame,
    timeBucket,
    measures,
    dimensions,
    experimentIds,
    from,
  });
}
