import { describe, expect, test } from "vitest";

import { crunchCosts, crunchCostsByTime } from "./costs";

type ModelSpec = {
  format: "openai" | "google" | "anthropic" | "js" | "window";
  flavor: "chat" | "completion";
  multimodal?: boolean | null | undefined;
  input_cost_per_token?: number | null | undefined;
  output_cost_per_token?: number | null | undefined;
  input_cost_per_mil_tokens?: number | null | undefined;
  output_cost_per_mil_tokens?: number | null | undefined;
  input_cache_read_cost_per_mil_tokens?: number | null | undefined;
  input_cache_write_cost_per_mil_tokens?: number | null | undefined;
  displayName?: string | null | undefined;
};

const costData = [
  {
    model: "gpt-3.5-turbo",
    time: 1731600000000,
    group: "gpt-3.5-turbo",
    prompt_uncached_tokens: BigInt(696),
    prompt_cached_tokens: BigInt(0),
    prompt_cache_creation_tokens: BigInt(0),
    completion_tokens: BigInt(294),
    count: BigInt(1),
  },
  {
    model: "gpt-4o",
    time: 1731600000000,
    group: "gpt-4o",
    prompt_uncached_tokens: BigInt(1059738),
    prompt_cached_tokens: BigInt(0),
    prompt_cache_creation_tokens: BigInt(0),
    completion_tokens: BigInt(301085),
    count: BigInt(1206),
  },
  {
    model: "gpt-4",
    time: 1731600000000,
    group: "gpt-4",
    prompt_uncached_tokens: BigInt(63724),
    prompt_cached_tokens: BigInt(0),
    prompt_cache_creation_tokens: BigInt(0),
    completion_tokens: BigInt(27403),
    count: BigInt(91),
  },
  {
    model: "gpt-3.5-turbo",
    time: 1751600000000,
    group: "gpt-3.5-turbo",
    prompt_uncached_tokens: BigInt(696),
    prompt_cached_tokens: BigInt(0),
    prompt_cache_creation_tokens: BigInt(0),
    completion_tokens: BigInt(294),
    count: BigInt(1),
  },
  {
    model: "gpt-4o",
    time: 1751600000000,
    group: "gpt-4o",
    prompt_uncached_tokens: BigInt(1059738),
    prompt_cached_tokens: BigInt(0),
    prompt_cache_creation_tokens: BigInt(0),
    completion_tokens: BigInt(301085),
    count: BigInt(1205),
  },
  {
    model: "gpt-4",
    time: 1751600000000,
    group: "gpt-4",
    prompt_uncached_tokens: BigInt(63724),
    prompt_cached_tokens: BigInt(0),
    prompt_cache_creation_tokens: BigInt(0),
    completion_tokens: BigInt(27403),
    count: BigInt(91),
  },
];

const allAvailableModels: { [name: string]: ModelSpec } = {
  "gpt-4": {
    format: "openai",
    flavor: "chat",
    input_cost_per_mil_tokens: 30,
    output_cost_per_mil_tokens: 60,
    input_cache_read_cost_per_mil_tokens: 15,
    input_cache_write_cost_per_mil_tokens: 37.5,
    displayName: "GPT 4",
  },
  "gpt-4o": {
    format: "openai",
    flavor: "chat",
    multimodal: true,
    input_cost_per_mil_tokens: 2.5,
    output_cost_per_mil_tokens: 10,
    input_cache_read_cost_per_mil_tokens: 1.25,
    input_cache_write_cost_per_mil_tokens: 3.125,
    displayName: "GPT 4o",
  },
  "gpt-3.5-turbo": {
    format: "openai",
    flavor: "chat",
    input_cost_per_mil_tokens: 0.5,
    output_cost_per_mil_tokens: 1.5,
    input_cache_read_cost_per_mil_tokens: 0.25,
    input_cache_write_cost_per_mil_tokens: 0.625,
    displayName: "GPT 3.5T",
  },
};

describe("costs", () => {
  test("crunchCosts", () => {
    const data = crunchCosts({ costData, allAvailableModels });
    expect(data).toEqual([
      {
        promptUncachedTokensCost: 0.000348,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 0.000441,
        totalCost: 0.000789,
        time: 1731600000000,
        count: BigInt(1),
        group: "gpt-3.5-turbo",
      },
      {
        promptUncachedTokensCost: 2.6493450000000003,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 3.01085,
        totalCost: 5.660195,
        time: 1731600000000,
        count: BigInt(1206),
        group: "gpt-4o",
      },
      {
        promptUncachedTokensCost: 1.91172,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 1.64418,
        totalCost: 3.5559000000000003,
        time: 1731600000000,
        count: BigInt(91),
        group: "gpt-4",
      },
      {
        promptUncachedTokensCost: 0.000348,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 0.000441,
        totalCost: 0.000789,
        time: 1751600000000,
        count: BigInt(1),
        group: "gpt-3.5-turbo",
      },
      {
        promptUncachedTokensCost: 2.6493450000000003,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 3.01085,
        totalCost: 5.660195,
        time: 1751600000000,
        count: BigInt(1205),
        group: "gpt-4o",
      },
      {
        promptUncachedTokensCost: 1.91172,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 1.64418,
        totalCost: 3.5559000000000003,
        time: 1751600000000,
        count: BigInt(91),
        group: "gpt-4",
      },
    ]);
  });

  test("crunchCostsByTime", () => {
    const data = crunchCostsByTime({ costData, allAvailableModels });
    expect(data).toEqual([
      {
        time: 1731600000000,
        promptUncachedTokensCost: 4.561413,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 4.655471,
        totalCost: 9.216884,
        count: BigInt(1298),
      },
      {
        time: 1751600000000,
        promptUncachedTokensCost: 4.561413,
        promptCachedTokensCost: 0,
        promptCacheCreationTokensCost: 0,
        completionTokensCost: 4.655471,
        totalCost: 9.216884,
        count: BigInt(1297),
      },
    ]);
  });
});
