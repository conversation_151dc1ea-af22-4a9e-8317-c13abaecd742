import { type ObjectRedirectLinkParams } from "#/utils/object-link";
import { getOrgLink } from "#/app/app/[org]/getOrgLink";

export function getObjectLink({
  orgName,
  object_type,
  object_id,
  id,
}: ObjectRedirectLinkParams): string {
  const queryParams = new URLSearchParams({
    object_type,
    object_id,
    ...(id ? { id } : {}),
  }).toString();
  return orgName
    ? `${getOrgLink({ orgName })}/object?${queryParams}`
    : `/app/object?${queryParams}`;
}
