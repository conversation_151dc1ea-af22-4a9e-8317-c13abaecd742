import { decodeURIComponentPatched } from "#/utils/url";
import { getObjectRedirectLink } from "./object-actions";
import { redirect } from "next/navigation";
import { HTTPError } from "#/utils/http_error";
import { objectLinkParamsSchema } from "#/utils/object-link";

export default async function Page(props: {
  params: Promise<{ org: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const [searchParams, params] = await Promise.all([
    props.searchParams,
    props.params,
  ]);
  const orgName = decodeURIComponentPatched(params.org);

  const searchParamsParsed = objectLinkParamsSchema.safeParse(searchParams);
  if (!searchParamsParsed.success) {
    return (
      <div>
        <p>Invalid query parameter format.</p>
        <pre>{JSON.stringify(searchParamsParsed.error.flatten(), null, 2)}</pre>
      </div>
    );
  }

  try {
    const destination = await getObjectRedirectLink({
      orgName,
      ...searchParamsParsed.data,
    });
    return redirect(destination);
  } catch (error) {
    // Unlike `notFound()`, this will not return a 404 status. However, it is
    // preferable to expose the error message to the user than to have the
    // correct HTTP status code.
    if (error instanceof HTTPError) {
      return error.message;
    } else {
      throw error;
    }
  }
}
