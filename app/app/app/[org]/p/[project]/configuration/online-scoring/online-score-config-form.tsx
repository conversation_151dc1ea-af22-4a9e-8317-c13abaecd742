"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
} from "#/ui/dialog";
import { useContext, useState } from "react";
import { Input, inputClassName } from "#/ui/input";
import {
  type UpsertProjectScore,
  type UpsertResponse,
  performUpsert,
} from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Slider } from "#/ui/slider";
import { ScorersDropdownWithCreateDialog } from "#/app/app/[org]/prompt/[prompt]/scorers/scorers-dropdown";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import ReactTextareaAutosize from "react-textarea-autosize";
import { onlineScoreConfigSchema } from "@braintrust/typespecs";
import { type DiscriminatedProjectScore } from "@braintrust/local/query";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { pluralizeWithCount } from "#/utils/plurals";
import { Switch } from "#/ui/switch";

import { ErrorBanner } from "#/ui/error-banner";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";
import { parseQuery } from "@braintrust/btql/parser";
import { OnlineScoringTestResults } from "./online-scoring-test-results";
import { InfoBanner } from "#/ui/info-banner";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import {
  performAutomationTest,
  type AutomationTestResponse,
} from "#/utils/btapi/scoring";
import { useBtqlAutocompleteProjectDataSource } from "#/app/app/[org]/btql/use-btql-autocomplete-data-sources";
import Link from "next/link";

const onlineScoreConfigFormSchema = z
  .object({
    name: z.string(),
    description: z.string().optional(),
  })
  .and(onlineScoreConfigSchema);

const formatSpanNames = (names?: string[] | null) =>
  names?.flatMap((n) => n.split(",").map((n) => n.trim())).filter(Boolean);

//TODO (anna): make adding a scorer mandatory to create online scoring rule
export function OnlineScoreConfigForm({
  orgName,
  row,
  onClose,
  leftFooterSlot,
  preSelectedScorerId,
}: {
  orgName: string;
  row: DiscriminatedProjectScore | null;
  onClose: VoidFunction;
  leftFooterSlot?: React.ReactNode;
  preSelectedScorerId?: string;
}) {
  const { projectId, projectName, mutateConfig } = useContext(ProjectContext);
  const { flags } = useFeatureFlags();
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const dataSources = useBtqlAutocompleteProjectDataSource();

  const [isCreatingScorer, setIsCreatingScorer] = useState(false);
  const action = row ? "Update" : "Create";

  const getReset = (row: DiscriminatedProjectScore | null) => {
    if (!row) return undefined;
    return {
      name: row.name,
      description: row.description ?? undefined,
      sampling_rate: row.config?.online?.sampling_rate,
      scorers: row.config?.online?.scorers,
      btql_filter: row.config?.online?.btql_filter,
      apply_to_root_span: row.config?.online?.apply_to_root_span,
      apply_to_span_names: formatSpanNames(
        row.config?.online?.apply_to_span_names,
      ),
      skip_logging: row.config?.online?.skip_logging,
    };
  };

  const form = useForm<
    z.infer<typeof onlineScoreConfigFormSchema> & { "": string }
  >({
    resolver: async (values, context, options) => {
      const result = await zodResolver(onlineScoreConfigFormSchema)(
        values,
        context,
        options,
      );
      if (values.btql_filter) {
        try {
          // TODO: should we swap with `useClauseChecker("project_logs", true)`?
          const query = parseQuery(
            `select: * | from: project_logs('fake_project') | filter: ${values.btql_filter}`,
          );
          if (!query.filter) {
            result.errors = {
              ...result.errors,
              btql_filter: { message: "Invalid BTQL filter" },
            };
          }
        } catch {
          result.errors = {
            ...result.errors,
            btql_filter: { message: "Invalid BTQL filter" },
          };
        }
      }
      return result;
    },
    defaultValues: row
      ? getReset(row)
      : {
          sampling_rate: 0.1,
          scorers: preSelectedScorerId
            ? [{ type: "function" as const, id: preSelectedScorerId }]
            : [],
          btql_filter: undefined,
          apply_to_root_span: true,
          apply_to_span_names: [],
          skip_logging: false,
        },
  });

  const scorersCount = form.watch("scorers").length;
  const hasScorers = scorersCount > 0;

  const applyToRootSpan = form.watch("apply_to_root_span");
  const applyToSpanNames = form.watch("apply_to_span_names");
  const btqlFilter = form.watch("btql_filter");

  const validBtqlFilter = !!btqlFilter;
  const validSpans =
    applyToRootSpan || (applyToSpanNames && applyToSpanNames.length > 0);
  const validFilter = validSpans || validBtqlFilter;

  const [isRunningTest, setIsRunningTest] = useState(false);
  const [testResult, setTestResult] = useState<AutomationTestResponse | null>(
    null,
  );

  const test = async (
    row: UpsertProjectScore,
  ): Promise<AutomationTestResponse> => {
    if (!org.api_url) {
      return {
        kind: "error",
        message: "Not logged in (yet)",
      };
    }
    const sessionToken = await getOrRefreshToken();
    return await performAutomationTest({
      apiUrl: org.api_url!,
      sessionToken,
      projectId: projectId!,
      row,
      errorContext: "online scoring",
    });
  };

  const save = async (row: UpsertProjectScore): Promise<UpsertResponse> => {
    if (!org.api_url) {
      return {
        kind: "error",
        message: "Not logged in (yet)",
      };
    }
    const sessionToken = await getOrRefreshToken();
    return await performUpsert({
      apiUrl: org.api_url!,
      sessionToken,
      objectType: "project_score",
      row,
      projectId: projectId!,
      mutate: mutateConfig,
    });
  };

  return (
    <Form {...form}>
      <div className="flex flex-1 flex-col overflow-hidden">
        <form
          onSubmit={form.handleSubmit(async (data) => {
            const { name, description, ...config } = data;
            const result = await save({
              id: row?.id,
              name,
              description,
              score_type: "online",
              config: {
                online: {
                  ...config,
                  apply_to_span_names: formatSpanNames(
                    config.apply_to_span_names,
                  ),
                },
              },
            });
            if (result.kind === "duplicate") {
              form.setError(
                "name",
                {
                  type: "value",
                  message:
                    "Name already exists. Please choose a unique rule name.",
                },
                {
                  shouldFocus: true,
                },
              );
              return;
            } else if (result.kind === "error") {
              toast.error(`Failed to create or update rule`, {
                description: result.message,
              });
              return;
            }

            const actionText = row ? "updated" : "created";
            toast.success(`Online scoring rule ${actionText}`, {
              description: `"${data.name}" has been ${actionText} successfully`,
            });
            onClose();
          })}
          className="flex flex-1 flex-col overflow-hidden"
        >
          <div className="flex flex-1 flex-col gap-6 overflow-y-auto p-4">
            <DialogHeader className="flex-none">
              <DialogTitle>Configure online scoring rule</DialogTitle>
              <DialogDescription>
                Continuous evaluation from real-time logs
              </DialogDescription>
            </DialogHeader>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rule name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter online scoring rule name"
                      {...field}
                      value={field.value ?? ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <div>
                  <CollapsibleSection
                    title="Description (optional)"
                    defaultCollapsed={!row?.description}
                  >
                    <FormItem>
                      <FormControl>
                        <ReactTextareaAutosize
                          {...field}
                          className={inputClassName}
                          rows={2}
                          placeholder="Enter description"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </CollapsibleSection>
                </div>
              )}
            />
            <FormField
              control={form.control}
              name="scorers"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex justify-between">
                    Scorers
                    {hasScorers && (
                      <Button
                        size="inline"
                        transparent
                        className="text-xs text-primary-500"
                        onClick={(e) => {
                          e.preventDefault();
                          form.setValue("scorers", [], { shouldDirty: true });
                        }}
                      >
                        Clear
                      </Button>
                    )}
                  </FormLabel>
                  <FormControl>
                    {projectId && (
                      <ScorersDropdownWithCreateDialog
                        projectId={projectId}
                        projectName={projectName}
                        savedScorers={field.value}
                        updateScorers={async (scorers) => {
                          field.onChange(scorers);
                          return Promise.resolve(null);
                        }}
                        disableScorersThatRequireConfiguration
                        isCreatingScorer={isCreatingScorer}
                        setIsCreatingScorer={setIsCreatingScorer}
                        hideSelected={false}
                      />
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="sampling_rate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sampling rate</FormLabel>
                  <FormControl>
                    <div className="flex max-w-xs items-center gap-2">
                      <div className="relative flex w-18 flex-none items-center">
                        <Input
                          ref={field.ref}
                          name={field.name}
                          onBlur={field.onBlur}
                          disabled={field.disabled}
                          value={Number((field.value * 100).toFixed(2))}
                          onChange={(e) => {
                            const value = Math.max(
                              0,
                              Math.min(100, Number(e.target.value)),
                            );
                            field.onChange(Number((value / 100).toFixed(4)));
                          }}
                          type="number"
                          placeholder="100"
                          className="h-7 w-full text-sm"
                          min={0}
                          max={100}
                          step="any"
                        />
                        <span className="pointer-events-none absolute right-2 text-xs text-primary-400">
                          %
                        </span>
                      </div>
                      <Slider
                        value={[Math.round(field.value * 100)]}
                        onValueChange={(v) => {
                          field.onChange(
                            Number((Math.round(v[0]) / 100).toFixed(2)),
                          );
                        }}
                        min={0}
                        max={100}
                        step={1}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Percentage of filtered logs to score
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            {flags.onlineScoringFilterAndTest && (
              <FormField
                control={form.control}
                name="btql_filter"
                render={({ field }) => (
                  <FormItem>
                    {/* TODO (anna): give example of BTQL filter */}
                    <FormLabel>BTQL filter</FormLabel>
                    <FormControl>
                      <BtqlEditor
                        mode="expr"
                        value={field.value ?? ""}
                        onValueChange={(v) =>
                          field.onChange(v, {
                            shouldDirty: true,
                            shouldTouch: true,
                          })
                        }
                        onMetaEnter={() => {
                          form.setValue("btql_filter", field.value, {
                            shouldDirty: true,
                            shouldTouch: true,
                          });
                        }}
                        dataSources={dataSources}
                        className={`${inputClassName} h-auto font-mono placeholder:font-inter`}
                        placeholder="Enter BTQL filter clause"
                      />
                    </FormControl>
                    <FormDescription>
                      Filter logs using BTQL&apos;s{" "}
                      <Link
                        className="font-medium text-accent-600"
                        href="/docs/reference/btql#filter-clause"
                        target="_blank"
                      >
                        filter clause
                      </Link>
                      . If no filter is provided, the rule will apply to all
                      logs.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <div className="flex flex-col gap-2">
              <div className="gap-2 text-sm">Apply to spans</div>
              <FormField
                control={form.control}
                name="apply_to_root_span"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex h-8 items-center gap-2 text-sm">
                        <Switch
                          className="flex-none data-[state=checked]:bg-primary-700"
                          checked={!!field.value}
                          onCheckedChange={(value) =>
                            field.onChange(value, {
                              shouldDirty: true,
                            })
                          }
                        />
                        Root spans
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="apply_to_span_names"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Enter span names (comma separated)"
                        {...field}
                        value={field.value?.join(", ") ?? ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === "") {
                            field.onChange(null, {
                              shouldDirty: true,
                              shouldValidate: true,
                            });
                          } else {
                            field.onChange(
                              value.split(",").map((n) => n.trim()),
                              {
                                shouldDirty: true,
                                shouldValidate: true,
                              },
                            );
                          }
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the span names this rule should apply to, separated
                      by comma. Span names are case-sensitive.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {hasScorers && validFilter && (
              <InfoBanner>
                {pluralizeWithCount(scorersCount, "scorer", "scorers")} will be
                added to {(form.watch("sampling_rate") * 100).toFixed(2)}% of
                new logs,{" "}
                {validBtqlFilter
                  ? `filtered to spans matching the BTQL filter${validSpans ? ", " : ""}`
                  : ""}
                {validSpans ? "and applied to " : ""}
                {applyToRootSpan &&
                applyToSpanNames &&
                applyToSpanNames.length > 0
                  ? "root spans and selected spans"
                  : applyToRootSpan
                    ? "root spans"
                    : applyToSpanNames && applyToSpanNames.length > 0
                      ? "selected spans"
                      : ""}
              </InfoBanner>
            )}
            <div>
              <CollapsibleSection
                title="Advanced"
                className="mb-2"
                defaultCollapsed
              >
                <FormField
                  control={form.control}
                  name="skip_logging"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex h-8 items-center gap-2 text-sm">
                          <Switch
                            checked={!!field.value}
                            onCheckedChange={(value) => field.onChange(value)}
                          />
                          Disable logging
                        </div>
                      </FormControl>
                      <FormDescription>
                        Disable logging for this rule. When disabled, logging
                        will be disabled on the target spans regardless of
                        whether there are other online scoring rules for those
                        spans.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CollapsibleSection>
            </div>

            {testResult?.kind === "error" && (
              <ErrorBanner skipErrorReporting>
                <div className="mb-1 font-medium">Test failed</div>
                {testResult.message}
              </ErrorBanner>
            )}
            {testResult?.kind === "success" && (
              <OnlineScoringTestResults payload={testResult.payload} />
            )}

            {form.formState.errors[""] && (
              <p className="text-xs font-medium text-bad-700">
                {form.formState.errors[""].message}
              </p>
            )}
          </div>
          <DialogFooter className="flex-none border-t px-4 py-3">
            <div className="flex w-full items-center justify-between">
              <div className="flex-none">{leftFooterSlot}</div>
              <div className="flex gap-2">
                {flags.onlineScoringFilterAndTest && (
                  <Button
                    type="button" // don't trigger on enter
                    variant="ghost"
                    disabled={!form.formState.isValid}
                    isLoading={isRunningTest}
                    onClick={async (e) => {
                      e.preventDefault();
                      if (!form.formState.isValid) {
                        return;
                      }
                      const data = form.getValues();

                      setTestResult(null);
                      setIsRunningTest(true);
                      const result = await test({
                        id: row?.id,
                        name: data.name,
                        description: data.description,
                        score_type: "online",
                        config: {
                          online: {
                            scorers: data.scorers,
                            sampling_rate: data.sampling_rate,
                            btql_filter: data.btql_filter || undefined,
                            apply_to_root_span: data.apply_to_root_span,
                            apply_to_span_names: data.apply_to_span_names,
                            skip_logging: data.skip_logging,
                          },
                        },
                      });

                      setTestResult(result);
                      setIsRunningTest(false);
                    }}
                  >
                    Test rule
                  </Button>
                )}
                <Button
                  variant="primary"
                  type="submit"
                  disabled={!form.formState.isDirty || !validFilter}
                  isLoading={
                    form.formState.isLoading || form.formState.isSubmitting
                  }
                >
                  {action} rule
                </Button>
              </div>
            </div>
          </DialogFooter>
        </form>
      </div>
    </Form>
  );
}
