"use client";

import {
  ConfigurationTable as Table,
  ConfigurationTableBody as TableBody,
  ConfigurationTableCell as TableCell,
  ConfigurationTableHead as TableHead,
  ConfigurationTableHeader as TableHeader,
  ConfigurationTableRow as TableRow,
} from "#/ui/configuration-table";
import * as Query from "#/utils/btql/query-builder";
import { Button } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { Spinner } from "#/ui/icons/spinner";
import {
  type InputHTMLAttributes,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { DiamondPercent, PencilLine, Plus, Trash2 } from "lucide-react";
import { Input } from "#/ui/input";
import {
  type UpsertProjectScore,
  type UpsertResponse,
  performUpsert,
  performDelete,
} from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { TextareaWithMdPreview } from "#/ui/textarea-with-md-preview";
import { Switch } from "#/ui/switch";
import { useBtql } from "#/utils/btql/btql";
import { useRecentExperiments } from "../../experiments/clientpage";
import { isEmpty } from "#/utils/object";
import { cn } from "#/utils/classnames";
import {
  type AggregateScoreCategory,
  type DiscriminatedProjectScore,
  isAggregateScore,
  isHumanReviewScore,
  sortScoreFields,
} from "@braintrust/local/query";
import { toast } from "sonner";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { useFeatureFlags } from "#/lib/feature-flags";
import { BasicTooltip } from "#/ui/tooltip";
import { AlertTriangle } from "lucide-react";
import { TableSkeleton } from "#/ui/table/table-skeleton";

const AggregateScoreConfig = () => {
  const [openedModal, setOpenedModal] = useState(false);
  const [openedRow, setOpenedRow] = useState<DiscriminatedProjectScore | null>(
    null,
  );
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
    projectName,
    isConfigLoading,
  } = useContext(ProjectContext);
  const { scores } = config;

  if (!projectId) {
    throw new Error("Cannot instantiate AggregateScoreConfig outside project");
  }

  const filteredScores = useMemo(() => {
    return scores.filter((row) => isAggregateScore(row.score_type));
  }, [scores]);

  return (
    <ConfigurationSectionLayout
      title="Aggregate scores"
      description="Summarize other scores by applying a weighted average."
      action={
        filteredScores.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setOpenedModal(true);
              setOpenedRow(null);
            }}
            Icon={Plus}
          >
            Aggregate score
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : filteredScores.length > 0 ? (
        <Table className="table-auto text-left">
          <TableHeader>
            <TableRow className="hover:bg-background">
              <TableHead className="w-48">Score name</TableHead>
              <TableHead className="flex-1">Description</TableHead>
              <TableHead className="flex-1">Type</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredScores.map((row) => (
              <TableRow key={row.id} className="py-2">
                <TableCell title={row.name} className="w-48">
                  <p className="truncate">{row.name}</p>
                </TableCell>
                <TableCell className="flex-1">
                  <p className="truncate">{row.description}</p>
                </TableCell>
                <TableCell className="flex-1">
                  <p className="truncate">{row.score_type}</p>
                </TableCell>
                <TableCell className="w-28 justify-end gap-2 pr-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setOpenedModal(true);
                      setOpenedRow(row);
                    }}
                  >
                    <PencilLine className="size-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={async (_e) => {
                      const sessionToken = await getOrRefreshToken();
                      return toast.promise(
                        performDelete({
                          apiUrl,
                          sessionToken,
                          objectType: "project_score",
                          rowId: row.id,
                          mutate,
                        }),
                      );
                    }}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <DiamondPercent className="text-primary-500" />
              <p className="text-base text-primary-500">
                No aggregate scores defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setOpenedModal(true);
                  setOpenedRow(null);
                }}
                Icon={Plus}
              >
                Create aggregate score
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <ConfigureScoreModal
        projectName={projectName}
        row={openedRow}
        opened={openedModal}
        setOpened={(opened) => {
          !opened && setOpenedRow(null);
          setOpenedModal(opened);
        }}
        create={async (row) => {
          const sessionToken = await getOrRefreshToken();
          return await performUpsert({
            apiUrl,
            sessionToken,
            objectType: "project_score",
            row,
            projectId,
            mutate,
          });
        }}
        configScores={config.scores}
      />
    </ConfigurationSectionLayout>
  );
};

function ConfigureScoreModal({
  opened,
  setOpened,
  row,
  create,
  configScores,
}: {
  projectName: string;
  opened: boolean;
  setOpened: (opened: boolean) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  row: any | null;
  create: (row: UpsertProjectScore) => Promise<UpsertResponse>;
  configScores: DiscriminatedProjectScore[];
}) {
  const { projectId } = useContext(ProjectContext);
  const { experimentIds } = useRecentExperiments();

  const {
    flags: { enableExpensiveSummaries },
  } = useFeatureFlags();

  const btqlExperimentScores = useBtql({
    name: "Score summary query (experiment)",
    query: useMemo(
      () =>
        projectId && experimentIds
          ? {
              from: Query.from("experiment", experimentIds),
              unpivot: [
                { expr: { btql: "scores" }, alias: ["score", "value"] },
              ],
              dimensions: [{ alias: "score", expr: { btql: "score" } }],
            }
          : null,
      [experimentIds, projectId],
    ),
    disableLimit: true,
    brainstoreRealtime: false,
    expensive: !enableExpensiveSummaries,
  });

  const btqlLogsScores = useBtql({
    name: "Score summary query (logs)",
    query: useMemo(
      () =>
        projectId
          ? {
              from: Query.from("project_logs", [projectId]),
              filter: {
                btql: "created >= NOW() - INTERVAL 3 DAY",
              },
              unpivot: [
                { expr: { btql: "scores" }, alias: ["score", "value"] },
              ],
              dimensions: [{ alias: "score", expr: { btql: "score" } }],
            }
          : null,
      [projectId],
    ),
    disableLimit: true,
    brainstoreRealtime: false,
    expensive: !enableExpensiveSummaries,
  });

  const dataScoreNames: string[] = useMemo(() => {
    const rows =
      (btqlExperimentScores?.data?.toArray() ?? [])
        .concat(btqlLogsScores?.data?.toArray() ?? [])
        .map((row) => row.toJSON().score) ?? [];
    rows.sort((a, b) => a.localeCompare(b));
    return rows;
  }, [btqlExperimentScores?.data, btqlLogsScores?.data]);

  const scoreNames = useMemo(() => {
    const manualScoreNames = configScores
      .filter(
        (s) =>
          isHumanReviewScore(s.score_type) &&
          s.config?.destination !== "expected",
      )
      .map((s) => s.name);
    return Array.from(
      new Set(
        sortScoreFields([...manualScoreNames, ...dataScoreNames], configScores),
      ),
    );
  }, [configScores, dataScoreNames]);

  // Defunct scores are scores that have been previously saved
  // in an aggregate score but are neither present in Human Review scores
  // or returned in the last 1000 experiments.
  // We will show a warning to the user to indicate these scores may be defunct
  const defunctScores = useMemo(() => {
    if (row?.score_type !== "weighted" || !row.categories) {
      return new Set<string>();
    }

    const currentScores = new Set(scoreNames);
    return new Set(
      Object.keys(row.categories).filter((score) => !currentScores.has(score)),
    );
  }, [row, scoreNames]);

  const allScoreNames = useMemo(() => {
    return Array.from(
      new Set(
        sortScoreFields(
          [...scoreNames, ...Array.from(defunctScores)],
          configScores,
        ),
      ),
    );
  }, [scoreNames, defunctScores, configScores]);

  const action = row?.name ? "Update" : "Create";

  const [name, setName] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [scoreType, setScoreType] =
    useState<AggregateScoreCategory>("weighted");
  const [weightedScoreConfig, setWeightedScoreConfig] = useState<Record<
    string,
    number
  > | null>(null);
  const [disabledBuffer, setDisabledBuffer] = useState<Record<string, number>>(
    {},
  );

  useEffect(() => {
    if (row) {
      setName(row.name);
      setDescription(row.description ?? "");
      setScoreType(row.score_type);

      if (row.score_type === "weighted") {
        setWeightedScoreConfig(row.categories ?? null);
      } else if (row.score_type === "minimum") {
        setWeightedScoreConfig(
          Object.fromEntries(row.categories.map((c: string) => [c, 1])),
        );
      } else {
        setWeightedScoreConfig(null);
      }
    }
  }, [row]);

  const clearInputs = () => {
    setName("");
    setDescription("");
    setScoreType("weighted");
    setWeightedScoreConfig(null);
    setDisabledBuffer({});
  };

  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  const defaultedScores = useMemo(
    () =>
      weightedScoreConfig ?? Object.fromEntries(scoreNames.map((s) => [s, 1])),
    [scoreNames, weightedScoreConfig],
  );

  const denominator = Object.values(defaultedScores).reduce((a, b) => a + b, 0);

  return (
    <Dialog
      open={opened}
      onOpenChange={(open) => {
        !open && clearInputs();
        setOpened(open);
      }}
    >
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>{action} aggregate score</DialogTitle>
          <DialogDescription>
            Summarize other scores by applying a weighted average. This score
            will update as other scores change (for example, through human
            review).
          </DialogDescription>
        </DialogHeader>
        <form
          className="w-full overflow-hidden"
          onSubmit={async (e) => {
            e.preventDefault();
            setError(null);

            if (!name || name.length === 0) {
              setError("Name is required");
              return;
            }

            setUpdating(true);
            try {
              const result = await create({
                id: row?.id,
                name,
                description: description.length > 0 ? description : null,
                score_type: scoreType,
                categories:
                  scoreType === "weighted"
                    ? defaultedScores
                    : Object.keys(weightedScoreConfig ?? defaultedScores),
              });
              if (result.kind === "duplicate") {
                setError(`Score with name ${name} already exists`);
                return;
              } else if (result.kind === "error") {
                setError(`Failed to create or update score: ${result.message}`);
                return;
              }
              setOpened(false);
              clearInputs();
            } catch (e) {
              setError(`${e}`);
            } finally {
              setUpdating(false);
            }
          }}
        >
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            type="text"
            placeholder="Score name"
            className="mb-2"
          />
          <TextareaWithMdPreview
            className="mb-6"
            value={description}
            setValue={setDescription}
          />
          <div className="mb-6 flex gap-4">
            <ScoreConfigOption
              label="Weighted average"
              description="Weight by an integer value"
              name={"weighted"}
              checked={scoreType === "weighted"}
              onChange={() => setScoreType("weighted")}
            />
            <ScoreConfigOption
              label="Minimum"
              description="Min of selected scores"
              name={"minimum"}
              checked={scoreType === "minimum"}
              onChange={() => setScoreType("minimum")}
            />
            <ScoreConfigOption
              label="Maximum"
              description="Max of selected scores"
              name={"maximum"}
              checked={scoreType === "maximum"}
              onChange={() => setScoreType("maximum")}
            />
            {/*<ScoreConfigOption
              label="Function"
              description="Write custom scoring code"
              disabled={true}
              name={AggregateScoreCategory.Function}
              checked={scoreType === AggregateScoreCategory.Function}
              onChange={() => setScoreType(AggregateScoreCategory.Function)}
            />*/}
          </div>
          <div className="flex w-full">
            {(scoreType === "minimum" ||
              scoreType === "maximum" ||
              scoreType === "weighted") && (
              <>
                {allScoreNames.length === 0 ? (
                  <div className="text-sm text-primary-500">
                    There are no scores defined
                  </div>
                ) : (
                  <div className="flex w-full flex-col gap-2">
                    {allScoreNames.map((score, idx) => (
                      <div
                        key={idx}
                        className="flex w-full items-center gap-3 text-sm"
                      >
                        <Switch
                          checked={
                            weightedScoreConfig === null ||
                            !isEmpty(weightedScoreConfig[score])
                          }
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setWeightedScoreConfig({
                                ...defaultedScores,
                                [score]: disabledBuffer[score] ?? 1,
                              });
                            } else {
                              const { [score]: val, ...rest } = defaultedScores;
                              setWeightedScoreConfig(rest);
                              setDisabledBuffer({
                                ...disabledBuffer,
                                [score]: val,
                              });
                            }
                          }}
                        />
                        {scoreType === "weighted" && (
                          <Input
                            type="number"
                            className="h-8 w-20"
                            min={1}
                            placeholder={
                              disabledBuffer[score]?.toString() ?? "1"
                            }
                            value={defaultedScores[score] ?? ""}
                            onChange={(e) => {
                              if (e.target.value === "") {
                                const { [score]: _, ...rest } = defaultedScores;
                                setWeightedScoreConfig(rest);
                                setDisabledBuffer((prev) => {
                                  const { [score]: _, ...rest } = prev;
                                  return rest;
                                });
                                return;
                              }
                              const value = parseInt(e.target.value);
                              if (
                                isNaN(value) ||
                                value.toString() !== e.target.value
                              ) {
                                return;
                              }
                              setWeightedScoreConfig({
                                ...defaultedScores,
                                [score]: value,
                              });
                            }}
                          />
                        )}
                        <span
                          className={cn(
                            "flex-1 truncate",
                            isEmpty(defaultedScores[score]) && "opacity-50",
                          )}
                        >
                          {score}
                          {defunctScores.has(score) && (
                            <BasicTooltip tooltipContent="This score could not be found in human review scores or the last 1000 experiments, and may be defunct">
                              <AlertTriangle className="ml-2 inline-block size-4 text-yellow-500" />
                            </BasicTooltip>
                          )}
                        </span>
                        {scoreType === "weighted" && (
                          <span className="flex-none text-xs text-primary-500">
                            {isEmpty(defaultedScores[score]) ||
                            denominator === 0
                              ? null
                              : (
                                  (100 * defaultedScores[score]) /
                                  denominator
                                ).toFixed(1) + "% of total"}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
          {error && <div className="mt-4 text-red-500">Error: {error}</div>}
          <DialogFooter className="mt-4">
            <Button type="submit">
              {updating ? (
                <div className="flex items-center">
                  Saving <Spinner className="ml-1" />
                </div>
              ) : (
                action
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

const ScoreConfigOption = ({
  label,
  description,
  ...rest
}: {
  label: string;
  description: string;
} & InputHTMLAttributes<HTMLInputElement>) => (
  <label className="flex flex-1 flex-col items-center gap-2 text-center text-sm">
    <input type="radio" {...rest} />
    <div>
      <div className="font-medium">{label}</div>
      <div className="text-xs text-balance text-primary-600">{description}</div>
    </div>
  </label>
);

export default AggregateScoreConfig;
