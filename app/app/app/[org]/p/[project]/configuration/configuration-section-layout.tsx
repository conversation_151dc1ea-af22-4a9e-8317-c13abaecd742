import * as React from "react";

export const ConfigurationSectionLayout = ({
  children,
  title,
  description,
  action,
}: React.PropsWithChildren<{
  title: React.ReactNode;
  description?: React.ReactNode;
  action?: React.ReactNode;
}>) => {
  return (
    <div className="flex flex-col gap-5">
      <div className="flex flex-col gap-5 sm:flex-row sm:items-end">
        <div className="flex-1">
          <h3 className="text-base font-semibold">{title}</h3>
          {description && (
            <p className="pt-2 text-sm text-primary-700">{description}</p>
          )}
        </div>
        {action && <div className="flex-none">{action}</div>}
      </div>
      <div className="max-w-(--breakpoint-xl)">{children}</div>
    </div>
  );
};
