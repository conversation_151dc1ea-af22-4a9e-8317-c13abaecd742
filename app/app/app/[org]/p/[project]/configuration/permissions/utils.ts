import { type UserGroupWithObjectPermissions } from "#/ui/permissions/permissions-types";
import { type Group, type User } from "@braintrust/typespecs";

import { getDisplayName, isServiceAccount } from "#/utils/user";
import { OBJECT_TYPE_LABELS } from "#/ui/permissions/items";
import { type AccessSummary } from "../../../object-permissions-types";

export function getInheritanceLabel(inheritanceList?: string[]) {
  let inheritanceLabel;
  if (inheritanceList && inheritanceList.length > 0) {
    const moreCount =
      inheritanceList.length > 3 ? inheritanceList.length - 3 : 0;
    inheritanceLabel =
      inheritanceList.slice(0, 3).join(", ") +
      (moreCount > 0 ? `, and ${moreCount} more...` : "");
  }
  return inheritanceLabel;
}

function isExclusivelyDirectAccess(itemAccess: UserGroupWithObjectPermissions) {
  return (
    itemAccess.has_direct_permission &&
    !itemAccess.roles.length &&
    !itemAccess.inherited_groups.length &&
    !itemAccess.inherited_parent_objects.length
  );
}

export function bucketUsersAndGroupsByAccess(
  usersAndGroupsWithAccess: UserGroupWithObjectPermissions[],
  allGroups: Group[],
  allUsers: User[],
) {
  const groupsWithAccess: AccessSummary[] = [];
  const groupsWithInheritedAccess: AccessSummary[] = [];
  const groupsWithoutAccess: AccessSummary[] = [];
  const usersWithAccess: AccessSummary[] = [];
  const usersWithInheritedAccess: AccessSummary[] = [];
  const usersWithoutAccess: AccessSummary[] = [];
  const serviceAccountsWithAccess: AccessSummary[] = [];
  const serviceAccountsWithInheritedAccess: AccessSummary[] = [];
  const serviceAccountsWithoutAccess: AccessSummary[] = [];

  const categorizeAccess = ({
    item,
    isGroup,
  }: {
    item: Group | User;
    isGroup: boolean;
  }) => {
    const itemAccess = usersAndGroupsWithAccess.find(
      (userOrGroup) =>
        userOrGroup.user_object_type === (isGroup ? "group" : "user") &&
        userOrGroup.user_group_id === item.id,
    );

    const accessObject = {
      userOrGroupId: item.id,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      label: isGroup ? (item as Group).name : getDisplayName(item),
      accessLabel: itemAccess
        ? isExclusivelyDirectAccess(itemAccess)
          ? "Direct access"
          : "Inherited access"
        : "No access",
    };

    if (itemAccess) {
      if (isExclusivelyDirectAccess(itemAccess)) {
        if (isGroup) {
          groupsWithAccess.push(accessObject);
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        } else if (isServiceAccount(item as User)) {
          serviceAccountsWithAccess.push(accessObject);
        } else {
          usersWithAccess.push(accessObject);
        }
      } else {
        const inheritanceLabels = [];

        if (
          itemAccess.inherited_groups &&
          itemAccess.inherited_groups.length > 0
        ) {
          const label = getInheritanceLabel(itemAccess.inherited_groups);
          if (label) {
            inheritanceLabels.push(label);
          }
        }

        if (
          itemAccess.inherited_parent_objects &&
          itemAccess.inherited_parent_objects.length > 0
        ) {
          const label = getInheritanceLabel(
            itemAccess.inherited_parent_objects.map(
              (parent) => OBJECT_TYPE_LABELS[parent],
            ),
          );
          if (label) {
            inheritanceLabels.push(label);
          }
        }

        if (isGroup) {
          groupsWithInheritedAccess.push({
            ...accessObject,
            inheritanceLabels,
          });
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        } else if (isServiceAccount(item as User)) {
          serviceAccountsWithInheritedAccess.push({
            ...accessObject,
            inheritanceLabels,
          });
        } else {
          usersWithInheritedAccess.push({
            ...accessObject,
            inheritanceLabels,
          });
        }
      }
    } else {
      if (isGroup) {
        groupsWithoutAccess.push(accessObject);
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      } else if (isServiceAccount(item as User)) {
        serviceAccountsWithoutAccess.push(accessObject);
      } else {
        usersWithoutAccess.push(accessObject);
      }
    }
  };

  allGroups.forEach((group) =>
    categorizeAccess({ item: group, isGroup: true }),
  );
  allUsers.forEach((user) => categorizeAccess({ item: user, isGroup: false }));

  return {
    groupsWithAccess,
    groupsWithInheritedAccess,
    usersWithAccess,
    usersWithInheritedAccess,
    serviceAccountsWithAccess,
    serviceAccountsWithInheritedAccess,
    groupsWithoutAccess,
    usersWithoutAccess,
    serviceAccountsWithoutAccess,
  };
}
