"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { ExternalLink } from "#/ui/link";
import { useContext, useMemo, useState } from "react";
import { ListChecks, Plus } from "lucide-react";
import { performUpsert, performDelete } from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  type DiscriminatedProjectScore,
  getPositionDescription,
  getPositionDescriptionsForAllScores,
} from "#/utils/score-config";
import {
  ConfigureScoreModal,
  formatScoreLabel,
} from "../configure-score-modal";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { toast } from "sonner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import {
  type PositionDescriptions,
  type setAllHumanReviewScoresSortOrder,
  type setHumanReviewScoreSortOrder,
} from "../configuration-actions";
import { useOrg } from "#/utils/user";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useSessionToken } from "#/utils/auth/session-token";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { DraggableTable, type ColumnDefinition } from "../draggable-table";

// Column definitions for the human review scores table
const getHumanReviewScoreColumns =
  (): ColumnDefinition<DiscriminatedProjectScore>[] => [
    {
      key: "name",
      header: "Name",
      width: "w-48",
      className: "flex w-48 items-center",
      render: (row) => (
        <p className="truncate" title={row.name}>
          {row.name}
        </p>
      ),
    },
    {
      key: "definition",
      header: "Definition",
      className: "flex flex-1 truncate",
      render: (row) => (
        <>
          {row.score_type === "slider"
            ? "Slider"
            : row.score_type === "free-form"
              ? "Free-form"
              : row.score_type === "categorical"
                ? row.categories.map((c, idx) => (
                    <span className="inline-flex items-center" key={idx}>
                      {formatScoreLabel(c.name)}
                      {idx < row.categories.length - 1 ? <>,&nbsp;</> : ""}
                    </span>
                  ))
                : "Invalid"}
        </>
      ),
    },
    {
      key: "description",
      header: "Description",
      className: "flex-1",
      render: (row) => <p className="truncate">{row.description}</p>,
    },
  ];

const updateSortOrderOnServer = async (
  orgName: string,
  projectName: string,
  positionDescriptions: PositionDescriptions,
  hasAllPositionsDefined: boolean,
  getToken: () => Promise<string | null>,
) => {
  try {
    const args = {
      org_name: orgName,
      project_name: projectName,
      position_descriptions: positionDescriptions,
    };

    if (hasAllPositionsDefined) {
      await invokeServerAction<typeof setHumanReviewScoreSortOrder>({
        fName: "setHumanReviewScoreSortOrder",
        args,
        getToken,
      });
    } else {
      await invokeServerAction<typeof setAllHumanReviewScoresSortOrder>({
        fName: "setAllHumanReviewScoresSortOrder",
        args,
        getToken,
      });
    }
  } catch (e) {
    console.error("Failed to update project_scores_sort_order", e);
    toast.error(
      "Failed to update project scores sort order. Please reload the page and try again.",
    );
  }
};

const HumanReviewScoreConfig = () => {
  const { getToken } = useAuth();
  const org = useOrg();
  const orgName = org.name;
  const [openedModal, setOpenedModal] = useState(false);
  const [openedRow, setOpenedRow] = useState<DiscriminatedProjectScore | null>(
    null,
  );
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
    projectName,
    isConfigLoading,
  } = useContext(ProjectContext);

  const { scores: allScores } = config;

  const humanReviewScores = useMemo(
    () =>
      allScores.filter((s) =>
        ["slider", "categorical", "free-form"].includes(s.score_type),
      ),
    [allScores],
  );

  const hasAllPositionsDefined = humanReviewScores.every(
    (s) => typeof s.position === "string",
  );

  if (!projectId) {
    throw new Error(
      "Cannot instantiate ScoreConfigurationSection outside project",
    );
  }

  const handleReorder = async (
    newOrder: DiscriminatedProjectScore[],
    draggedItem: DiscriminatedProjectScore,
    originalIndex: number,
    newIndex: number,
  ) => {
    let positionDescriptions: PositionDescriptions | null = null;

    // If all of the scores have Lexorank positions defined, we only need to update the moved score
    if (hasAllPositionsDefined) {
      const prevScore = newOrder[newIndex - 1];
      const nextScore = newOrder[newIndex + 1];
      positionDescriptions = getPositionDescription({
        score: draggedItem,
        prevScore,
        nextScore,
      });
    }

    // If one or more scores do not have Lexorank positions defined, we need to update all of the scores
    // This typically happens on the very first DnD operation for a set of scores
    if (!hasAllPositionsDefined) {
      positionDescriptions = getPositionDescriptionsForAllScores(newOrder);
    }

    if (positionDescriptions) {
      await updateSortOrderOnServer(
        orgName,
        projectName,
        positionDescriptions,
        hasAllPositionsDefined,
        getToken,
      );
      await mutate();
    }
  };

  return (
    <ConfigurationSectionLayout
      title="Human review"
      description={
        <>
          Define scores and labels for manual human review, either as feedback
          from your users (
          <ExternalLink href="/docs/guides/logging">
            through the API
          </ExternalLink>
          ) or directly through the UI.
        </>
      }
      action={
        humanReviewScores.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setOpenedModal(true);
              setOpenedRow(null);
            }}
            Icon={Plus}
          >
            Human review score
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : humanReviewScores.length > 0 ? (
        <DraggableTable
          items={humanReviewScores}
          columns={getHumanReviewScoreColumns()}
          onReorder={handleReorder}
          onEdit={(row) => {
            setOpenedModal(true);
            setOpenedRow(row);
          }}
          onDelete={async (row) => {
            const sessionToken = await getOrRefreshToken();
            return toast.promise(
              performDelete({
                apiUrl: org.api_url,
                sessionToken,
                objectType: "project_score",
                rowId: row.id,
                mutate,
              }),
            );
          }}
          getDragPreview={(row) => row.name}
        />
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <ListChecks className="text-primary-500" />
              <p className="text-base text-primary-500">
                No human review scores defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setOpenedModal(true);
                  setOpenedRow(null);
                }}
                Icon={Plus}
              >
                Create human review score
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <ConfigureScoreModal
        projectName={projectName}
        row={openedRow}
        opened={openedModal}
        setOpened={(opened) => {
          !opened && setOpenedRow(null);
          setOpenedModal(opened);
        }}
        isHumanReviewScore
        create={async (row) => {
          const sessionToken = await getOrRefreshToken();
          return await performUpsert({
            apiUrl: org.api_url,
            sessionToken,
            objectType: "project_score",
            row,
            projectId,
            mutate,
          });
        }}
      />
    </ConfigurationSectionLayout>
  );
};

export default HumanReviewScoreConfig;
