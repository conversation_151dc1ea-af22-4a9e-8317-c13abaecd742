"use client";
import {
  ConfigurationTable as Table,
  ConfigurationTableBody as TableBody,
  ConfigurationTableCell as TableCell,
  ConfigurationTableHead as TableHead,
  ConfigurationTableHeader as TableHeader,
  ConfigurationTableRow as TableRow,
} from "#/ui/configuration-table";
import { Button } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useContext, useState } from "react";
import { AppWindowMac, PencilLine, Plus, Trash2 } from "lucide-react";
import { Input, inputClassName } from "#/ui/input";
import {
  type UpsertResponse,
  performUpsert,
  performDelete,
  type UpsertSpanIFrame,
} from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import ReactTextareaAutosize from "react-textarea-autosize";
import { type SpanIFrame } from "@braintrust/typespecs";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { Switch } from "#/ui/switch";
import { TableSkeleton } from "#/ui/table/table-skeleton";
const SpanIframeConfig = () => {
  // null means we're creating a new viewer
  const [selectedViewer, setSelectedViewer] = useState<
    SpanIFrame | null | undefined
  >(undefined);

  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
    isConfigLoading,
  } = useContext(ProjectContext);
  const { span_iframes } = config;

  if (!projectId) {
    throw new Error("Cannot instantiate CustomViewerConfig outside project");
  }

  return (
    <ConfigurationSectionLayout
      title="Span iframes"
      description="Render custom iframe URLs as fields in trace spans."
      action={
        span_iframes.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setSelectedViewer(null);
            }}
            Icon={Plus}
          >
            Span iframe
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : span_iframes.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow className="hover:bg-background">
              <TableHead className="w-48">Field name</TableHead>
              <TableHead className="w-96">URL pattern</TableHead>
              <TableHead className="flex-1">Description</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {span_iframes.map((spanIframe) => (
              <TableRow key={spanIframe.id} className="py-2">
                <TableCell className="w-48 truncate">
                  {spanIframe.name}
                </TableCell>
                <TableCell className="w-96 truncate">
                  {spanIframe.url}
                </TableCell>
                <TableCell className="flex-1 truncate">
                  {spanIframe.description ?? ""}
                </TableCell>
                <TableCell className="w-28 justify-end gap-2 pr-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setSelectedViewer(spanIframe);
                    }}
                  >
                    <PencilLine className="size-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={async (_e) => {
                      return toast.promise(
                        performDelete({
                          apiUrl,
                          sessionToken: await getOrRefreshToken(),
                          objectType: "span_iframe",
                          rowId: spanIframe.id,
                          mutate,
                        }),
                      );
                    }}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <AppWindowMac className="text-primary-500" />
              <p className="text-base text-primary-500">
                No span iframes defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setSelectedViewer(null);
                }}
                Icon={Plus}
              >
                Create span iframe
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <Dialog
        open={selectedViewer !== undefined}
        onOpenChange={(o) => {
          if (!o) {
            setSelectedViewer(undefined);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Configure span iframe</DialogTitle>
            <DialogDescription>
              Configure a span iframe for your project. The URL can contain
              mustache <span className="font-mono">{`{{parameters}}`}</span>{" "}
              corresponding to span data paths to dynamically render the iframe.
            </DialogDescription>
          </DialogHeader>
          <SpanIframeConfigForm
            row={selectedViewer ?? null}
            onClose={() => setSelectedViewer(undefined)}
            save={async (row) => {
              const sessionToken = await getOrRefreshToken();
              return await performUpsert({
                apiUrl,
                sessionToken,
                objectType: "span_iframe",
                row,
                projectId,
                mutate,
              });
            }}
          />
        </DialogContent>
      </Dialog>
    </ConfigurationSectionLayout>
  );
};

const spanIframeConfigFormSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  url: z.string().url(),
  post_message: z.boolean().default(false),
});

const getReset = (row: SpanIFrame | null) => {
  if (!row) return undefined;
  return {
    name: row.name,
    description: row.description ?? undefined,
    url: row.url,
    post_message: row.post_message ?? false,
  };
};

function SpanIframeConfigForm({
  row,
  save,
  onClose,
}: {
  row: SpanIFrame | null;
  save: (row: UpsertSpanIFrame) => Promise<UpsertResponse>;
  onClose: VoidFunction;
}) {
  const action = row ? "Update" : "Create";

  const form = useForm<
    z.infer<typeof spanIframeConfigFormSchema> & { "": string }
  >({
    resolver: zodResolver(spanIframeConfigFormSchema),
    defaultValues: row
      ? getReset(row)
      : {
          name: "",
          url: "",
          post_message: false,
        },
  });

  const watchedUrl = form.formState.isValid && form.watch("url");

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(async (data) => {
          const { name, description, url, post_message } = data;
          const result = await save({
            id: row?.id,
            name,
            description,
            url,
            post_message,
          });
          if (result.kind === "duplicate") {
            form.setError(
              "name",
              {
                type: "value",
                message:
                  "Name already exists. Please choose a unique field name.",
              },
              {
                shouldFocus: true,
              },
            );
            return;
          } else if (result.kind === "error") {
            toast.error(`Failed to create or update span iframe`, {
              description: result.message,
            });
            return;
          }
          onClose();
        })}
        className="flex flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Field name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter iframe field name"
                  {...field}
                  value={field.value ?? ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <div>
              <CollapsibleSection
                title="Description"
                defaultCollapsed={!row?.description}
              >
                <FormItem>
                  <FormControl>
                    <ReactTextareaAutosize
                      {...field}
                      className={inputClassName}
                      rows={2}
                      placeholder="Enter description (optional)"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </CollapsibleSection>
            </div>
          )}
        />
        <FormField
          control={form.control}
          name="url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>URL</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter iframe URL with optional {{parameters}}"
                  type="url"
                />
              </FormControl>
              <FormDescription>
                Eg.{" "}
                <span className="font-mono">{`https://example.com/{{input.id}}?q={{metadata.foo.bar}}`}</span>
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <CollapsibleSection
            title="Preview (without parameters)"
            defaultCollapsed={!row?.url}
          >
            {watchedUrl && (
              <iframe
                className="h-80 w-full overflow-hidden rounded-md border border-primary-100 bg-primary-50"
                allowFullScreen
                // @ts-ignore -- allowtransparency is not a valid attribute
                allowtransparency="true"
                src={watchedUrl}
              />
            )}
          </CollapsibleSection>
        </div>

        <FormField
          control={form.control}
          name="post_message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Post span data</FormLabel>
              <FormControl>
                <label className="flex items-center gap-2 text-sm">
                  <Switch
                    className="data-[state=checked]:bg-primary-700"
                    checked={!!field.value}
                    onCheckedChange={field.onChange}
                  />
                  Post message with span data to iframe on load
                </label>
              </FormControl>
            </FormItem>
          )}
        />
        {form.formState.errors[""] && (
          <p className="text-xs font-medium text-bad-700">
            {form.formState.errors[""].message}
          </p>
        )}
        <DialogFooter className="mt-4">
          <Button
            variant="primary"
            type="submit"
            disabled={!form.formState.isDirty || !form.formState.isValid}
            isLoading={form.formState.isLoading || form.formState.isSubmitting}
          >
            {action} span iframe
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}

export default SpanIframeConfig;
