import {
  type ProjectScore,
  type ProjectTag,
  type SpanIFrame,
  type ProjectAutomation,
} from "@braintrust/typespecs";
import { toast } from "sonner";
import {
  type LoadedBtSessionToken,
  sessionFetchProps,
} from "#/utils/auth/session-token";

export type UpsertProjectScore = Omit<
  ProjectScore,
  "id" | "project_id" | "user_id"
> & { id: string | undefined };

export type UpsertProjectTag = Omit<
  ProjectTag,
  "id" | "project_id" | "user_id"
> & {
  id: string | undefined;
};

export type UpsertSpanIFrame = Omit<
  SpanIFrame,
  "id" | "project_id" | "user_id"
> & {
  id: string | undefined;
};

export type UpsertProjectAutomation = Omit<
  ProjectAutomation,
  "id" | "project_id" | "user_id"
> & {
  id: string | undefined;
};

export type UpsertResponse =
  | { kind: "success"; id: string }
  | { kind: "duplicate" }
  | { kind: "error"; message: string };

export async function performUpsert({
  apiUrl,
  sessionToken,
  objectType,
  row,
  projectId,
  mutate,
}: (
  | {
      objectType: "project_score";
      row: UpsertProjectScore;
    }
  | {
      objectType: "project_tag";
      row: UpsertProjectTag;
    }
  | {
      objectType: "span_iframe";
      row: UpsertSpanIFrame;
    }
  | {
      objectType: "project_automation";
      row: UpsertProjectAutomation;
    }
) & {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  projectId: string;
  mutate: () => void;
}): Promise<UpsertResponse> {
  const resp = await (() => {
    const { sessionHeaders, sessionExtraFetchProps } =
      sessionFetchProps(sessionToken);
    if (row.id) {
      return fetch(`${apiUrl}/api/${objectType}/patch_id`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...sessionHeaders,
        },
        body: JSON.stringify(row),
        ...sessionExtraFetchProps,
      });
    } else {
      const { name, ...rest } = row;
      return fetch(`${apiUrl}/api/${objectType}/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...sessionHeaders,
        },
        body: JSON.stringify({
          project_id: projectId,
          [`${objectType}_name`]: name,
          ...rest,
        }),
        ...sessionExtraFetchProps,
      });
    }
  })();
  mutate();
  if (!resp.ok) {
    return { kind: "error", message: await resp.text() };
  } else {
    const body = await resp.json();
    if (!row.id && body.found_existing) {
      return { kind: "duplicate" };
    } else {
      return { kind: "success", id: row.id ?? body[objectType].id };
    }
  }
}

export async function performDelete({
  apiUrl,
  sessionToken,
  objectType,
  rowId,
  mutate,
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  objectType:
    | "project_score"
    | "project_tag"
    | "span_iframe"
    | "project_automation";
  rowId: string;
  mutate: () => void;
}): Promise<void> {
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  const resp = await fetch(`${apiUrl}/api/${objectType}/delete_id`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    body: JSON.stringify({ id: rowId }),
    ...sessionExtraFetchProps,
  });
  mutate();
  if (!resp.ok) {
    let objectName = "unknown";
    switch (objectType) {
      case "project_score":
        objectName = "score";
        break;
      case "project_tag":
        objectName = "tag";
        break;
      case "span_iframe":
        objectName = "span iframe";
        break;
      case "project_automation":
        objectName = "automation";
        break;
      default:
        const _exhaustiveCheck: never = objectType;
        throw new Error(`Unknown object type: ${_exhaustiveCheck}`);
    }

    toast.error(`Failed to delete ${objectName}`, {
      description: `${await resp.text()}`,
    });
  }
}
