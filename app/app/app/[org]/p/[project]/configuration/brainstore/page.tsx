"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useContext } from "react";
import { BrainstoreProjectConfiguration } from "./brainstore-project-configuration";
import { useOrg } from "#/utils/user";

export default function Page() {
  const { projectId } = useContext(ProjectContext);
  const org = useOrg();

  if (!projectId || !org.id) {
    return null;
  }

  return (
    <BrainstoreProjectConfiguration projectId={projectId} orgId={org.id} />
  );
}
