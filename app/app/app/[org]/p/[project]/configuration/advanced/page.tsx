"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { useContext, useEffect, useState } from "react";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { isEmpty } from "#/utils/object";
import { PlainInput } from "#/ui/plain-input";
import { makeComparisonKeySQL } from "@braintrust/local/query";
import { toast } from "sonner";
import { ConfigurationSectionLayout } from "../configuration-section-layout";

const AdvancedSettings = () => {
  const {
    projectId,
    projectSettings,
    mutateProject: mutate,
  } = useContext(ProjectContext);

  const [comparisonKey, setComparisonKey] = useState<string | null>(
    projectSettings?.comparison_key ?? null,
  );
  const [saveState, setSaveState] = useState<"unsaved" | "saving" | "saved">(
    "saved",
  );

  const performSave = async () => {
    try {
      makeComparisonKeySQL({ comparisonKey });
    } catch (e) {
      toast.error(
        `${e && typeof e === "object" && "message" in e ? e.message : e}`,
      );
      return;
    }

    setSaveState("saving");
    const resp = await fetch(`/api/project/patch_id`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: projectId,
        settings: {
          ...projectSettings,
          comparison_key: comparisonKey,
        },
      }),
    });

    if (!resp.ok) {
      setSaveState("unsaved");
      toast.error(`Failed to save comparison key`, {
        description: `${await resp.text()}`,
      });
    } else {
      mutate();
      setSaveState("saved");
    }
  };

  useEffect(() => {
    setComparisonKey((prev) => {
      if (!isEmpty(prev)) {
        return prev;
      }
      setSaveState("saved");
      return projectSettings?.comparison_key ?? null;
    });
  }, [projectSettings]);

  if (!projectId) {
    throw new Error(
      "Cannot instantiate advanced project configuration outside project",
    );
  }

  return (
    <ConfigurationSectionLayout
      title="Comparison key (advanced)"
      description={`The field used to determine which rows are the same across different experiments. Defaults to \"input\".`}
    >
      <div className="flex gap-2">
        <PlainInput
          value={isEmpty(comparisonKey) ? "input" : comparisonKey}
          onChange={(e) => {
            setComparisonKey(e.target.value);
            setSaveState("unsaved");
          }}
          placeholder="input"
          className="block w-full max-w-sm"
        />
        <Button
          onClick={performSave}
          disabled={saveState === "saved"}
          isLoading={saveState === "saving"}
        >
          Save
        </Button>
      </div>
    </ConfigurationSectionLayout>
  );
};

export default AdvancedSettings;
