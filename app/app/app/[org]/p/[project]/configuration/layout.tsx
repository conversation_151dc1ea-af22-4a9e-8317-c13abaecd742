"use client";
import { PageTracker } from "#/ui/use-analytics";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { LoginRequired } from "#/ui/root";
import React, { useContext, useState } from "react";
import { SidebarNav } from "./sidebar-nav";
import { RenderIfUser } from "../../../clientlayout";
import { ClientLayout } from "../../../settings/clientlayout";
import { useOrg } from "#/utils/user";
import { type getProjectSummary } from "../../../org-actions";
import { useQueryFunc } from "#/utils/react-query";
import { useEntityContextActions } from "../../useEntityContextActions";
import { getProjectConfigurationLink } from "../getProjectLink";
import { ProjectContext } from "../projectContext";
import { getOrgLink } from "../../../getOrgLink";
import { AccessFailed } from "#/ui/access-failed";
import { isEmpty } from "braintrust/util";
import { Button } from "#/ui/button";
import { Clipboard, PencilLine, ShieldCheck, Trash2 } from "lucide-react";
import { ObjectPermissionsDialog } from "../permissions/object-permissions-dialog";
import { BodyWrapper } from "#/app/app/body-wrapper";

export default function Layout({ children }: { children: React.ReactNode }) {
  const { name: orgName } = useOrg();
  const { projectId, projectName, projectPermissions } =
    useContext(ProjectContext);
  const { invalidate } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  const { actions: projectActions, modals: projectActionModals } =
    useEntityContextActions({
      entityType: "project",
      onUpdate: invalidate,
      reloadPageOnUpdateArgs: {
        getEditedEntityLink: (projectName) =>
          getProjectConfigurationLink({ orgName, projectName }),
        getDeletedEntityLink: () => getOrgLink({ orgName }),
      },
    });

  const [isPermissionsOpen, setIsPermissionsOpen] = useState(false);

  const shouldShowPermissions = projectPermissions.includes("read_acls");

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return (
    <LoginRequired loginRequired>
      <RenderIfUser>
        <ClientLayout>
          <PageTracker category="configuration">
            <BodyWrapper innerClassName="overflow-auto">
              <MainContentWrapper className="h-auto min-h-[calc(100vh-45px)] w-full">
                {projectActionModals}
                <div className="flex flex-col gap-6 pb-6 lg:flex-row lg:gap-10">
                  <aside className="w-full flex-none lg:max-w-[180px]">
                    <SidebarNav />
                    <div className="-mx-1 mt-6 hidden flex-col gap-1 border-t border-primary-100 pt-6 lg:flex">
                      <Button
                        size="xs"
                        variant="ghost"
                        className="justify-start font-normal text-primary-600"
                        onClick={() => {
                          projectActions.editEntityName({
                            entityId: projectId,
                            entityName: projectName,
                          });
                        }}
                        Icon={PencilLine}
                      >
                        Rename project
                      </Button>
                      <Button
                        size="xs"
                        variant="ghost"
                        className="justify-start font-normal text-primary-600"
                        onClick={() => {
                          projectActions.copyEntityId({ entityId: projectId });
                        }}
                        Icon={Clipboard}
                      >
                        Copy project ID
                      </Button>
                      {shouldShowPermissions && (
                        <Button
                          size="xs"
                          variant="ghost"
                          className="justify-start font-normal text-primary-600"
                          onClick={() => {
                            setIsPermissionsOpen(true);
                          }}
                          Icon={ShieldCheck}
                        >
                          Project permissions
                        </Button>
                      )}
                      {isPermissionsOpen && (
                        <ObjectPermissionsDialog
                          open
                          onOpenChange={setIsPermissionsOpen}
                          orgName={orgName}
                          projectName={projectName}
                          objectId={projectId}
                          objectName={projectName}
                          objectType="project"
                        />
                      )}
                      <Button
                        size="xs"
                        variant="ghost"
                        className="justify-start font-normal text-bad-600"
                        onClick={() => {
                          projectActions.deleteEntity({
                            entityId: projectId,
                            entityName: projectName,
                            trackAnalytics: {
                              source: "project_page_overflow_control",
                            },
                          });
                        }}
                        Icon={Trash2}
                      >
                        Delete project
                      </Button>
                    </div>
                  </aside>
                  <div className="max-w-(--breakpoint-xl) flex-1">
                    {children}
                  </div>
                </div>
              </MainContentWrapper>
            </BodyWrapper>
          </PageTracker>
        </ClientLayout>
      </RenderIfUser>
    </LoginRequired>
  );
}
