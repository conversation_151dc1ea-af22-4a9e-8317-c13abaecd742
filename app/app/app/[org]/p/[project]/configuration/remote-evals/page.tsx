"use client";
import {
  ConfigurationTable as Table,
  ConfigurationTableBody as TableBody,
  ConfigurationTableCell as TableCell,
  ConfigurationTableHead as TableHead,
  ConfigurationTableHeader as TableHeader,
  ConfigurationTableRow as TableRow,
} from "#/ui/configuration-table";
import { Button } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useContext, useMemo, useState } from "react";
import { PencilLine, Plus, Trash2, Unplug } from "lucide-react";
import { Input } from "#/ui/input";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import {
  remoteEvalSourceSchema,
  type RemoteEvalSource,
} from "@braintrust/typespecs";
import { isEmpty } from "#/utils/object";
import { CollapsibleSection } from "#/ui/collapsible-section";
import TextArea from "#/ui/text-area";
import { useQuery } from "@tanstack/react-query";
import { useSessionToken } from "#/utils/auth/session-token";
import { remoteEvalListQuery } from "../../playgrounds/[playground]/playx/remote-evals-provider";
import { useOrg } from "#/utils/user";
import { pluralizeWithCount } from "#/utils/plurals";
import { useDebounce } from "#/utils/useDebouncedCallback";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import Link from "next/link";
const RemoteEvalsConfig = () => {
  // null means we're creating a new viewer
  const [selectedIndex, setSelectedIndex] = useState<number | null | undefined>(
    undefined,
  );

  const {
    mutateConfig: mutate,
    projectId,
    projectSettings,
    isConfigLoading,
  } = useContext(ProjectContext);

  const [remoteEvalSources, setRemoteEvalSources] = useState<
    RemoteEvalSource[]
  >(projectSettings?.remote_eval_sources ?? []);

  if (!projectId) {
    throw new Error("Cannot instantiate CustomViewerConfig outside project");
  }

  const onSave = async (data: RemoteEvalSource[]) => {
    const resp = await fetch(`/api/project/patch_id`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: projectId,
        settings: {
          ...projectSettings,
          remote_eval_sources: data,
        },
      }),
    });

    if (!resp.ok) {
      toast.error(`Failed to save remote eval sources`, {
        description: `${await resp.text()}`,
      });
    } else {
      mutate();
      setRemoteEvalSources(data);
      setSelectedIndex(undefined);
    }
  };

  return (
    <ConfigurationSectionLayout
      title="Remote evals"
      description={
        <>
          Remote evals let you run evaluations directly in the playground,
          iterate quickly across datasets, run scorers, and compare results with
          other tasks.{" "}
          <Link
            href="/docs/guides/remote-evals"
            className="font-medium text-accent-600"
            target="_blank"
          >
            Learn more
          </Link>
        </>
      }
      action={
        remoteEvalSources.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setSelectedIndex(null);
            }}
            Icon={Plus}
          >
            Remote eval source
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : remoteEvalSources.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow className="hover:bg-background">
              <TableHead className="w-32 flex-none">Name</TableHead>
              <TableHead className="w-64 flex-none">URL</TableHead>
              <TableHead className="flex-1">Description</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {remoteEvalSources.map((remoteEvalSource, index) => (
              <TableRow key={index} className="py-2">
                <TableCell className="w-32 flex-none truncate">
                  {remoteEvalSource.name}
                </TableCell>
                <TableCell className="w-64 flex-none truncate">
                  {remoteEvalSource.url}
                </TableCell>
                <TableCell className="flex-1 truncate">
                  {remoteEvalSource.description}
                </TableCell>
                <TableCell className="w-28 justify-end gap-2 pr-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setSelectedIndex(index);
                    }}
                  >
                    <PencilLine className="size-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={async () => {
                      await onSave(
                        remoteEvalSources.filter((_, i) => i !== index),
                      );
                    }}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <Unplug className="text-primary-500" />
              <p className="text-base text-primary-500">
                No remote eval sources defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setSelectedIndex(null);
                }}
                Icon={Plus}
              >
                Create remote eval source
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <Dialog
        open={selectedIndex !== undefined}
        onOpenChange={(o) => {
          if (!o) {
            setSelectedIndex(undefined);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Configure remote eval source</DialogTitle>
            <DialogDescription>
              Configure a remote eval source for your project. This source will
              be scanned for remote evals and made available to be run in
              playgrounds.
            </DialogDescription>
          </DialogHeader>
          <RemoteEvalSourceConfigForm
            row={
              !isEmpty(selectedIndex) ? remoteEvalSources[selectedIndex] : null
            }
            onClose={() => setSelectedIndex(undefined)}
            onSave={async (data) => {
              const newRemoteEvalSources = [...remoteEvalSources];
              if (isEmpty(selectedIndex)) {
                newRemoteEvalSources.push(data);
              } else {
                newRemoteEvalSources[selectedIndex] = data;
              }
              await onSave(newRemoteEvalSources);
            }}
          />
        </DialogContent>
      </Dialog>
    </ConfigurationSectionLayout>
  );
};

const getReset = (row: RemoteEvalSource | null) => {
  if (!row) return undefined;
  return {
    url: row.url,
    name: row.name,
    description: row.description,
  };
};

function RemoteEvalSourceConfigForm({
  row,
  onSave,
  onClose,
}: {
  row: RemoteEvalSource | null;
  onSave: (data: RemoteEvalSource) => Promise<void>;
  onClose: VoidFunction;
}) {
  const action = row ? "Update" : "Create";
  const form = useForm<RemoteEvalSource>({
    resolver: zodResolver(remoteEvalSourceSchema),
    defaultValues: row
      ? getReset(row)
      : {
          url: "",
          name: "",
          description: null,
        },
  });

  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const watchedUrl = useDebounce(form.watch("url"), 500);
  const endpointTest = useQuery({
    queryKey: ["remote-evals-test", watchedUrl],
    queryFn: async () =>
      await remoteEvalListQuery({
        endpointUrl: watchedUrl,
        getOrRefreshToken,
        orgName: org.name,
      }),
    meta: {
      disableGlobalErrorToast: true,
    },
    enabled: !!watchedUrl && !form.formState.errors.url,
  });

  const endpointTestStatus = useMemo(() => {
    if (!watchedUrl) {
      return null;
    }
    if (endpointTest.isLoading || endpointTest.isFetching) {
      return {
        label: "Connecting...",
        type: "info",
      };
    }
    if (endpointTest.error) {
      return {
        label:
          "There was an error connecting or listing evals at the provided URL. Please make sure a dev server is running at the provided URL.",
        type: "error",
      };
    }

    return {
      label: `Connected - ${pluralizeWithCount(Object.keys(endpointTest.data ?? {}).length, "eval")} found`,
      type: "info",
    };
  }, [endpointTest, watchedUrl]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(async (data) => {
          await onSave(data);
          onClose();
        })}
        className="flex flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  value={field.value ?? ""}
                  placeholder="Enter remote eval source name"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>URL</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter remote eval source URL"
                  type="url"
                />
              </FormControl>
              {endpointTestStatus && typeof endpointTestStatus === "object" && (
                <FormDescription
                  className={
                    endpointTestStatus.type === "error"
                      ? "text-bad-700"
                      : "text-primary-500"
                  }
                >
                  {endpointTestStatus.label}
                </FormDescription>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
        <div>
          <CollapsibleSection title="Description (optional)" defaultCollapsed>
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <TextArea
                      {...field}
                      value={field.value ?? ""}
                      placeholder="Enter remote eval source description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CollapsibleSection>
        </div>
        <DialogFooter className="mt-4">
          <Button
            variant="primary"
            type="submit"
            disabled={!form.formState.isDirty || !form.formState.isValid}
            isLoading={form.formState.isLoading || form.formState.isSubmitting}
          >
            {action} remote eval source
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}

export default RemoteEvalsConfig;
