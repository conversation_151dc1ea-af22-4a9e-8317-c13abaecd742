"use client";

import { useEffect } from "react";
import { useAutomationIdState } from "#/ui/query-parameters";
import { useRouter } from "next/navigation";

const Automations = () => {
  const [selectedAutomationId, _] = useAutomationIdState();
  const router = useRouter();

  useEffect(() => {
    router.replace(
      `../configuration/alerts${selectedAutomationId ? `?aid=${selectedAutomationId}` : ""}`,
    );
  }, [router, selectedAutomationId]);

  return;
};

export default Automations;
