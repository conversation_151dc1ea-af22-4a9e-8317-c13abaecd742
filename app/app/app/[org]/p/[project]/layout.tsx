import {
  ProjectClientLayout,
  type ProjectContextElements,
} from "./clientlayout";
import { decodeURIComponentPatched } from "#/utils/url";
import { getProjectContextProjects } from "./project-actions";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";

export default async function ProjectLayout(props: {
  children: React.ReactNode;
  params: Promise<{ org: string; project: string }>;
}) {
  const params = await props.params;

  const { children } = props;

  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);

  const projectRowsPromise = getProjectContextProjects({
    org_name: orgName,
    project_name: projectName,
  });

  const projectContextElements: ProjectContextElements = {
    projects: await projectRowsPromise,
  };

  const projectId = projectContextElements.projects?.[0]?.id;

  const projectPermissions =
    (await getObjectAclPermissions({
      objectType: "project",
      objectId: projectId ?? undefined,
    })) ?? [];

  return (
    <ProjectClientLayout
      orgName={orgName}
      projectName={projectName}
      projectContextElements={projectContextElements}
      projectPermissions={projectPermissions}
    >
      {children}
    </ProjectClientLayout>
  );
}
