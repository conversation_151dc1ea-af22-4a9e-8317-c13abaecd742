import { cn } from "#/utils/classnames";
import { type LinkItem, useLibraryLinks } from "./use-library-links";
import { Loading } from "#/ui/loading";
import Link from "next/link";
import { buttonVariants } from "#/ui/button";
import { CollapsibleSection } from "#/ui/collapsible-section";

export function LibraryItemLinks({
  projectName,
  objectType,
  objectId,
  objectName,
  objectSlug,
  className,
}: {
  projectName: string;
  objectType: "dataset" | "prompt" | "scorer" | "tool" | "task" | "agent";
  objectId: string;
  objectName?: string;
  objectSlug?: string;
  className?: string;
}) {
  const { linksByProject, loading, promptSessionsError, experimentsError } =
    useLibraryLinks({
      objectType,
      objectId,
      objectName,
      objectSlug,
      projectName,
    });

  return (
    <CollapsibleSection
      title="Recently used in"
      defaultCollapsed
      className={className}
    >
      <LinksContent
        objectType={objectType}
        loading={loading}
        promptSessionsError={promptSessionsError}
        experimentsError={experimentsError}
        linksByProject={linksByProject}
      />
    </CollapsibleSection>
  );
}

const LinksContent = ({
  objectType,
  loading,
  promptSessionsError,
  experimentsError,
  linksByProject,
}: {
  objectType: "dataset" | "prompt" | "scorer" | "tool" | "task" | "agent";
  loading: boolean;
  promptSessionsError?: Error;
  experimentsError?: Error;
  linksByProject?: {
    projectId: string;
    projectName: string;
    links: LinkItem[];
  }[];
}) => {
  if (loading) {
    return <Loading />;
  }

  if (promptSessionsError && experimentsError) {
    return <span className="text-xs text-bad-600">Error loading links</span>;
  }

  const timeRangeTip = (
    <p className="pt-1 text-xs text-primary-500">
      Only experiments and playgrounds created in the past 30 days will be
      listed here
    </p>
  );

  if (!linksByProject || linksByProject.length === 0) {
    return (
      <>
        <span className="text-xs text-primary-500">
          This {objectType} is not used yet
        </span>
        {timeRangeTip}
      </>
    );
  }

  return (
    <>
      {linksByProject.flatMap(({ links }, i) =>
        links.map((link, j) =>
          link.href ? (
            <Link
              key={`${i}-${j}`}
              className={cn(
                buttonVariants({ variant: "ghost", size: "xs" }),
                "w-full justify-start",
              )}
              href={link.href}
            >
              {link.icon}
              <span className="flex-1 truncate">{link.objectName}</span>
              <span className="max-w-28 flex-none truncate text-xs font-normal text-primary-500">
                {link.projectName}
              </span>
            </Link>
          ) : null,
        ),
      )}
      {promptSessionsError || experimentsError ? (
        <p className="text-xs text-bad-600">
          {`Error loading ${promptSessionsError ? "playground" : "experiment"} links`}
        </p>
      ) : null}
      {timeRangeTip}
    </>
  );
};
