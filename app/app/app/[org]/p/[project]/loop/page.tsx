import { buildMetadata } from "#/app/metadata";
import { decodeURIComponentPatched } from "#/utils/url";
import { getProjectId } from "../project-actions";
import ClientPage, { type Params } from "./clientpage";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const projectId = await getProjectId({
    org_name: decodeURIComponentPatched(params.org),
    project_name: decodeURIComponentPatched(params.project),
  });
  return <ClientPage params={params} projectId={projectId ?? ""} />;
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);
  return buildMetadata({
    title: "Loop",
    sections: [orgName, projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/loop`,
  });
}
