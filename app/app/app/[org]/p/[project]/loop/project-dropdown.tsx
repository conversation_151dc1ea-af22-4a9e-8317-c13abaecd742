"use client";

import { Check, ChevronDown, Search } from "lucide-react";
import { useRef, useState } from "react";
import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Input } from "#/ui/input";
import type { ProjectSummary } from "#/app/app/[org]/org-actions";
import { cn } from "#/utils/classnames";

export interface SimpleProjectDropdownProps {
  projects: ProjectSummary[];
  selectedProject?: ProjectSummary;
  onSelectProject: (project: ProjectSummary) => void;
  isLoading?: boolean;
  className?: string;
}

export const ProjectDropdown = ({
  projects,
  selectedProject,
  onSelectProject,
  isLoading,
  className,
}: SimpleProjectDropdownProps) => {
  const [projectQuery, setProjectQuery] = useState("");
  const [isDropdownOpen, setDropdownOpen] = useState(false);

  const firstItemRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredProjects =
    projects?.filter((p) =>
      p.project_name.toLowerCase().includes(projectQuery.toLowerCase()),
    ) ?? [];

  return (
    <DropdownMenu
      open={isDropdownOpen}
      onOpenChange={(open) => {
        setDropdownOpen(open);
        if (!open) {
          setProjectQuery("");
        }
      }}
    >
      <DropdownMenuTrigger asChild>
        <Button className={cn("gap-2", className)} variant="border" size="xs">
          <span className="truncate">
            {selectedProject?.project_name ? (
              <>Searching in {selectedProject?.project_name}</>
            ) : (
              "Select a project"
            )}
          </span>
          <ChevronDown className="size-4 shrink-0 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[250px]">
        <DropdownMenuGroup className="px-2 py-1">
          <div className="relative" onKeyDown={(e) => e.stopPropagation()}>
            <Search className="pointer-events-none absolute top-[10px] left-2 size-3 text-primary-500" />
            <Input
              placeholder="Search projects..."
              autoFocus
              ref={inputRef}
              value={projectQuery}
              className="h-8 border-0 bg-transparent! pr-1.5 pl-7 text-xs ring-0 outline-hidden focus-visible:border-0 focus-visible:ring-0"
              style={{ boxShadow: "none" }}
              onChange={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setProjectQuery(e.target.value);
              }}
              onKeyDown={(event) => {
                if (event.key === "ArrowDown") {
                  event.preventDefault();
                  firstItemRef.current?.focus();
                }
              }}
            />
          </div>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="mx-2 my-0" />

        <DropdownMenuGroup className="max-h-[300px] overflow-auto p-2">
          <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
            Projects
          </DropdownMenuLabel>

          {isLoading && (
            <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-400">
              Loading projects...
            </DropdownMenuLabel>
          )}

          {!isLoading && filteredProjects.length === 0 && (
            <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-400">
              No projects found
            </DropdownMenuLabel>
          )}

          {!isLoading &&
            filteredProjects.map((project, i) => (
              <DropdownMenuItem
                key={project.project_id}
                ref={i === 0 ? firstItemRef : undefined}
                onSelect={() => {
                  onSelectProject(project);
                  setDropdownOpen(false);
                }}
                onKeyDown={(event) => {
                  if (event.key === "ArrowUp" && i === 0) {
                    event.preventDefault();
                    inputRef.current?.focus();
                  }
                }}
              >
                {selectedProject?.project_id === project.project_id ? (
                  <Check className="mr-2 size-3" />
                ) : (
                  <span className="mr-2 size-3" />
                )}
                <span className="truncate">{project.project_name}</span>
              </DropdownMenuItem>
            ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
