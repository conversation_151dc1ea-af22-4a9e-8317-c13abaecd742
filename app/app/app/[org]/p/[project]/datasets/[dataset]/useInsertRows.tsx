import { useCallback } from "react";
import { DatasetIdField, ProjectIdField } from "#/utils/duckdb";
import { type DML } from "#/utils/mutable-object";

export function useInsertRows({
  dml,
  projectId,
  datasetId,
}: {
  dml: DML;
  projectId?: string;
  datasetId?: string;
}) {
  return useCallback(
    async (
      rows: Record<string, unknown>[],
      overrides?: {
        datasetId: string;
        projectId: string;
      },
    ) => {
      const preparedRows = await dml.prepareUpserts(
        rows.map((row) => ({
          [DatasetIdField]: overrides?.datasetId || datasetId,
          [ProjectIdField]: overrides?.projectId || projectId,
          ...row,
        })),
      );
      await dml.upsert(preparedRows);
    },
    [datasetId, projectId, dml],
  );
}
