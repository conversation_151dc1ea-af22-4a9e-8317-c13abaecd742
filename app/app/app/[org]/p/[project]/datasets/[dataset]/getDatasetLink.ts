import { getProjectLink } from "../../getProjectLink";

export const getDatasetLink = (args: {
  orgName: string;
  projectName: string;
  datasetName: string;
  rowId?: string | null;
}) => {
  return `${getDatasetsLink({
    orgName: args.orgName,
    projectName: args.projectName,
  })}/${encodeURIComponent(args.datasetName)}${
    args.rowId ? `?r=${args.rowId}` : ""
  }`;
};

export const getDatasetsLink = ({
  orgName,
  projectName,
}: {
  orgName: string;
  projectName: string;
}) =>
  `${getProjectLink({
    orgName,
    projectName,
  })}/datasets`;
