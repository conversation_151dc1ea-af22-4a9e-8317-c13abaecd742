import ClientPage, { type DatasetPanelLayout, type Params } from "./clientpage";
import { type Metadata, type ResolvingMetadata } from "next";
import { fetchDatasetInfo } from "./actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { cookies } from "next/headers";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { type Permission } from "@braintrust/typespecs";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const project_name = decodeURIComponentPatched(params.project);
  const dataset_name = decodeURIComponentPatched(params.dataset);
  const datasetInfo = await fetchDatasetInfo({
    org_name,
    project_name,
    dataset_name,
  });

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#server-component
  const layout = (await cookies()).get("react-resizable-panels:dataset-layout");

  const defaultPanelLayout: DatasetPanelLayout = {
    main: 80,
    trace: 20,
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  let datasetPermissions: Permission[] = [];
  try {
    const permissions = await getObjectAclPermissions({
      objectType: "dataset",
      objectId: datasetInfo?.id,
    });
    datasetPermissions = permissions ?? [];
  } catch (e) {
    console.error("Failed to get dataset permissions", e);
  }

  return (
    <ClientPage
      params={params}
      datasetInfo={datasetInfo}
      defaultPanelLayout={defaultPanelLayout}
      permissions={datasetPermissions}
    />
  );
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const datasetName = decodeURIComponentPatched(params.dataset);
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: datasetName,
    sections: ["Datasets", projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/datasets/${params.dataset}`,
    ogTemplate: "product",
  });
}
