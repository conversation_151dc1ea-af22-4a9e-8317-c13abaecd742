export async function createDataset({
  orgId,
  projectName,
  datasetName,
}: {
  orgId: string;
  projectName: string;
  datasetName: string;
}) {
  const resp = await fetch("/api/dataset/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      org_id: orgId,
      project_name: projectName,
      dataset_name: datasetName,
    }),
  });

  if (!resp.ok) {
    return { data: null, error: await resp.text() };
  }

  return { data: await resp.json(), error: null };
}
