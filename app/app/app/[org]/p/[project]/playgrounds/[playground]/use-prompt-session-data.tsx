import { TransactionIdField, useDBQuery } from "#/utils/duckdb";
import { savedScoreSchema } from "#/utils/scorers";
import { useMemo, useCallback, useState, useEffect, useRef } from "react";
import { playgroundSettingsSchema } from "../settings";
import { z } from "zod";
import { type DML } from "#/utils/mutable-object";
import { type PromptSessionMeta } from "./playground-actions";
import { newId } from "#/utils/btapi/btapi";
import { useOptimisticState } from "#/utils/optimistic-update";
import useEvent from "react-use-event-hook";

const PromptSessionDataSchema = z.object({
  dataset_id: z.string().nullish(),
  scorers: z.array(savedScoreSchema).default([]),
  settings: playgroundSettingsSchema.nullish(),
  generations: z.record(z.string()).nullish(),
});

const PromptSessionSchema = z.object({
  id: z.string(),
  _xact_id: z.string().nullish(),
  prompt_session_id: z.string(),
  prompt_session_data: PromptSessionDataSchema,
});

export type PromptSession = z.infer<typeof PromptSessionSchema>;
export type PromptSessionData = z.infer<typeof PromptSessionDataSchema>;

export const usePromptSessionData = ({
  promptSessionReady,
  promptSessionScan,
  promptSessionDML,
  promptSessionMeta,
  onInitialized,
}: {
  promptSessionReady: number;
  promptSessionScan: string | null;
  promptSessionDML: DML;
  promptSessionMeta: PromptSessionMeta | null;
  onInitialized?: () => void;
}) => {
  const { data: promptSessionMetadataRows, hasLoaded } = useDBQuery(
    promptSessionScan
      ? `SELECT
      id,
      ${TransactionIdField},
      prompt_session_id,
      prompt_session_data,
    FROM (${promptSessionScan})
    WHERE json(prompt_session_data) IS NOT NULL LIMIT 1`
      : null,
    [promptSessionReady],
  );
  const promptSession: PromptSession | undefined = useMemo(() => {
    if (!promptSessionMetadataRows) {
      return undefined;
    }
    if (promptSessionMetadataRows.toArray().length > 1) {
      console.warn(
        "Multiple prompt session metadata rows found. Will use the first.",
      );
    }

    const parsedMetadata = promptSessionMetadataRows.toArray().map((r) => {
      const obj = r.toJSON();
      return PromptSessionSchema.safeParse({
        ...obj,
        prompt_session_data: JSON.parse(obj.prompt_session_data),
      });
    })[0];
    if (!parsedMetadata?.success) {
      return undefined;
    }
    return parsedMetadata?.data;
  }, [promptSessionMetadataRows]);

  const defaultPromptSession = useCallback((): PromptSession => {
    if (!promptSessionMeta) {
      throw new Error("Missing required input: promptSessionMeta");
    }
    return {
      id: newId(),
      prompt_session_id: promptSessionMeta.id,
      prompt_session_data: {
        scorers: [],
      },
    };
  }, [promptSessionMeta]);

  const updatePromptSessionData = useCallback(
    async (updates: Partial<PromptSessionData>) => {
      const existingPromptSession = promptSession ?? defaultPromptSession();

      return await promptSessionDML.update(
        [existingPromptSession],
        Object.entries(updates).flatMap(([key, value]) => ({
          path: ["prompt_session_data", key],
          newValue: value,
        })),
      );
    },
    [promptSessionDML, promptSession, defaultPromptSession],
  );

  const [value, setValue] = useState<PromptSessionData | undefined>(
    promptSession?.prompt_session_data,
  );

  const { save } = useOptimisticState({
    xactId: promptSession?.[TransactionIdField] ?? null,
    value: promptSession?.prompt_session_data,
    save: updatePromptSessionData,
    rowKey: promptSession?.id,
    onUpdatedValue: setValue,
  });

  const setPromptSessionData = useEvent(
    async (data: Partial<PromptSessionData>) => {
      setValue((prev) => ({
        ...(prev ?? {}),
        ...data,
        scorers: data.scorers ?? prev?.scorers ?? [],
      }));
      save(data);
    },
  );

  const hasInitialized = useRef(false);
  useEffect(() => {
    if (!hasLoaded || hasInitialized.current) {
      return;
    }
    hasInitialized.current = true;
    onInitialized?.();
  }, [
    hasLoaded,
    promptSession?.prompt_session_data,
    onInitialized,
    setPromptSessionData,
  ]);

  return {
    promptSession,
    promptSessionData: value,
    setPromptSessionData,
    hasLoaded,
  };
};
