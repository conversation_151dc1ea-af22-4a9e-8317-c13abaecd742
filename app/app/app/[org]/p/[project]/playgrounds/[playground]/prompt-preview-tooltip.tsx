import { getModelIcon } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { type PromptData } from "@braintrust/typespecs";
import { getAvailableModels } from "@braintrust/proxy/schema";
import { type SavedPromptMeta } from "./use-saved-prompt-meta";
import { useMemo } from "react";
import { MarkdownViewer } from "#/ui/markdown";
import { isArray, prettifyXact } from "braintrust/util";
import { NULL_DASH } from "#/ui/table/formatters/null-formatter";
import { parseTransactionBigInt } from "#/utils/transactions";
import { smartTimeFormat } from "#/ui/date";
import { jsonToYaml } from "#/utils/parse";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import {
  parseResponseFormat,
  responseFormatLabel,
} from "../../prompts/output-format";

export const AgentPromptPreviewTooltip = ({
  prompt,
}: {
  prompt: PromptData;
}) => {
  const outputFormat = parseResponseFormat(prompt);
  const { label: outputFormatLabel, Icon: OutputFormatIcon } =
    responseFormatLabel[outputFormat.type];

  return (
    <div className="flex flex-col py-1">
      <PromptPreviewMessages prompt={prompt} />
      <div className="mt-3 flex flex-col gap-1 border-t border-primary-200/60 pt-3 empty:hidden">
        <div className="flex flex-none items-center gap-1 text-xs text-primary-600">
          <OutputFormatIcon className="size-3 flex-none" />
          {outputFormatLabel}
        </div>
        {outputFormat.type === "json_schema" && (
          <SyntaxHighlight
            className="font-mono text-xs leading-5 whitespace-pre-wrap"
            language="yaml"
            content={jsonToYaml(outputFormat.schema)}
          />
        )}
      </div>
    </div>
  );
};

export const PromptPreviewTooltip = ({
  prompt,
  promptMeta,
  version,
  promptVersionText,
}: {
  prompt?: PromptData | null;
  promptMeta: SavedPromptMeta | null;
  version?: string;
  promptVersionText?: string;
}) => {
  const model = prompt?.options?.model ?? "";
  const { displayName, modelName } = useMemo(() => {
    const modelSpec = getAvailableModels()[model];
    const parentModelSpec = modelSpec?.parent
      ? getAvailableModels()[modelSpec.parent]
      : undefined;
    const modelName =
      modelSpec?.displayName ?? parentModelSpec?.displayName ?? model;
    return { displayName: promptMeta?.name ?? modelName, modelName };
  }, [model, promptMeta?.name]);
  const Icon = getModelIcon(model) ?? undefined;

  const promptVersion = version || prompt?.origin?.prompt_version;
  const parsedVersion = promptVersion
    ? parseTransactionBigInt(promptVersion)
    : undefined;

  return (
    <>
      <div className="text-base font-medium">{displayName}</div>
      {promptMeta?.description && (
        <MarkdownViewer
          className="py-0 text-xs text-primary-500"
          value={promptMeta.description}
        />
      )}
      <PromptPreviewMessages prompt={prompt} />
      <div className="mt-1 flex flex-col gap-2 border-t border-primary-200/60 pt-4 empty:hidden">
        {Icon && modelName && (
          <div className="flex flex-none items-center gap-1 text-xs">
            <Icon size={16} className="flex-none" />
            <span className="text-primary-600">{modelName}</span>
          </div>
        )}
        {prompt?.options?.params && (
          <SyntaxHighlight
            className="font-mono text-[12px] leading-5 whitespace-pre-wrap"
            language="yaml"
            content={jsonToYaml(prompt.options.params)}
          />
        )}
        {promptVersionText ? (
          <div className="text-[10px] text-primary-400 tabular-nums">
            {promptVersionText}
          </div>
        ) : parsedVersion ? (
          <div className="flex justify-between gap-4 text-xs text-primary-400 tabular-nums">
            <div>Version {prettifyXact(promptVersion!)}</div>
            <time dateTime={parsedVersion?.date?.toISOString()}>
              {smartTimeFormat(parsedVersion?.date.getTime())}
            </time>
          </div>
        ) : null}
      </div>
    </>
  );
};

const PromptPreviewMessages = ({ prompt }: { prompt?: PromptData | null }) => {
  return (
    prompt?.prompt?.type === "chat" && (
      <div className="flex flex-col gap-3">
        {prompt.prompt.messages.map((message, idx) => (
          <div className="text-xs text-primary-500" key={idx}>
            <div className="text-primary-600 capitalize">{message.role}</div>
            {message.content ? (
              typeof message.content === "string" ? (
                <MarkdownViewer
                  className="py-0"
                  value={message.content || NULL_DASH}
                />
              ) : isArray(message.content) ? (
                message.content.map((m, idx) => (
                  <div key={idx}>
                    {"type" in m && m.type === "text" && "text" in m && (
                      <MarkdownViewer
                        className="py-0"
                        value={m.text || NULL_DASH}
                      />
                    )}
                  </div>
                ))
              ) : (
                NULL_DASH
              )
            ) : (
              NULL_DASH
            )}
          </div>
        ))}
      </div>
    )
  );
};
