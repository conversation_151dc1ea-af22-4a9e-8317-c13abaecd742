import { useDurableObjectByIds } from "#/utils/mutable-object";
import { useMemo } from "react";
import { type PromptSessionState } from "#/ui/prompts/schema";
import { useOrg } from "#/utils/user";
import { useJSONMemo } from "#/utils/memo";
import { functionIdSchema } from "@braintrust/typespecs";

export type SavedPromptMeta = {
  name?: string | null;
  description?: string | null;
  metadata?: Record<string, unknown> | null;
};

export const useSavedPromptMeta = ({
  promptSessionDbState,
}: {
  promptSessionDbState: PromptSessionState | null;
}): {
  savedPromptMeta: Record<string, SavedPromptMeta>;
  isInitializing: boolean;
} => {
  const { id: orgId } = useOrg();

  const promptIds = useMemo(() => {
    const promptIds: string[] = [];

    Object.values(promptSessionDbState ?? {}).forEach((p) => {
      if (p.function_data.type === "graph") {
        // Add the saved agent id
        if (p.prompt_data.origin?.prompt_id) {
          promptIds.push(p.prompt_data.origin.prompt_id);
        }

        // Add any saved prompts in the agent
        Object.values(p.function_data.nodes).forEach((n) => {
          if (n.type !== "function") return;
          const parsedFunction = functionIdSchema.safeParse(n.function);
          if (
            parsedFunction.success &&
            "inline_prompt" in parsedFunction.data &&
            parsedFunction.data.inline_prompt?.origin?.prompt_id
          ) {
            promptIds.push(
              parsedFunction.data.inline_prompt?.origin?.prompt_id,
            );
          }
        });
      } else if (p.prompt_data.origin?.prompt_id) {
        // Add saved prompt
        promptIds.push(p.prompt_data.origin.prompt_id);
      }
    });

    return promptIds;
  }, [promptSessionDbState]);

  const { objects, status } = useDurableObjectByIds({
    ids: promptIds,
    version: undefined,
    objectType: "org_functions",
    objectId: orgId,
    disableCache: true,
  });

  const savedPromptMeta = useJSONMemo(
    useMemo(() => {
      return objects
        ? Object.fromEntries(
            objects.map((p) => [
              p.id,
              {
                name: p.name,
                description: p.description,
                metadata: p.metadata,
              },
            ]),
          )
        : {};
    }, [objects]),
  );

  return {
    savedPromptMeta,
    isInitializing: status === "loading",
  };
};
