import {
  usePlaygroundFullscreenTaskIndex,
  usePlaygroundPromptSheetIndexState,
} from "#/ui/query-parameters";
import { cn } from "#/utils/classnames";
import { type PropsWithChildren, forwardRef, useEffect } from "react";

export const PlaygroundPromptBlocks = forwardRef<
  HTMLDivElement,
  PropsWithChildren<{}>
>(({ children }: PropsWithChildren<{}>, ref) => {
  const { promptSheetIndex } = usePlaygroundPromptSheetIndexState();

  useEffect(() => {
    if (promptSheetIndex === null) return;
    document
      .getElementById(`playground-prompt-${promptSheetIndex}`)
      ?.scrollIntoView({
        behavior: "smooth",
        // These options ensure that the container isn't unnecessarily scrolled vertically
        // and the container is scrolled horizontally enough that the add task button is scrolled into view.
        block: "nearest",
        inline: "start",
      });
  }, [promptSheetIndex]);

  const [fullscreenTaskIndex] = usePlaygroundFullscreenTaskIndex();

  return (
    <div
      className={cn("flex-none", {
        "flex h-full flex-1": fullscreenTaskIndex !== null,
      })}
      ref={ref}
    >
      <div
        className={cn("relative flex h-[460px] w-full flex-col pt-3", {
          "h-auto flex-1": fullscreenTaskIndex !== null,
        })}
      >
        <div className="-mx-3 flex grow gap-2 overflow-hidden overflow-x-auto px-3 pb-3">
          {children}
        </div>
      </div>
    </div>
  );
});
PlaygroundPromptBlocks.displayName = "PlaygroundPromptBlocks";
