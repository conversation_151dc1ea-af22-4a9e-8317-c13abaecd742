import { type Metadata, type ResolvingMetadata } from "next";
import ClientPage, { type Params } from "./clientpage";
import { getPromptSessionMeta } from "./playground-actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { type Permission } from "@braintrust/typespecs";
import { cookies } from "next/headers";
import { type PanelLayout } from "../../experiments/[experiment]/clientpage";
import { type PlaygroundTracePanelLayout } from "./playx/playground-trace-sheet";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const [promptSessionMeta] = await Promise.all([
    getPromptSessionMeta({
      org_name: decodeURIComponentPatched(params.org),
      project_name: decodeURIComponentPatched(params.project),
      playground: decodeURIComponentPatched(params.playground),
    }),
  ]);

  let permissions: Permission[] = [];
  try {
    const promptSessionPermissions = await getObjectAclPermissions({
      objectType: "prompt_session",
      objectId: promptSessionMeta?.id,
    });

    permissions = promptSessionPermissions ?? [];
  } catch (e) {
    console.error("Failed to get prompt session permissions", e);
  }

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#server-component
  const layout = (await cookies()).get(
    "react-resizable-panels:playground-layout",
  );

  const defaultPanelLayout: PanelLayout = {
    main: 80,
    sidebar: 20,
    trace: 0,
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  const defaultTracePanelLayout: PlaygroundTracePanelLayout = {
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  return (
    <ClientPage
      params={params}
      promptSessionMeta={promptSessionMeta}
      permissions={permissions}
      defaultPanelLayout={defaultPanelLayout}
      defaultTracePanelLayout={defaultTracePanelLayout}
    />
  );
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const playgroundName = decodeURIComponentPatched(params.playground);
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: playgroundName,
    sections: ["Playgrounds", projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/playgrounds/${params.playground}`,
    ogTemplate: "product",
  });
}
