import { z } from "zod";

export const DEFAULT_MAX_CONCURRENCY = 10;

// If we really wanted to go crazy we could define some sort of "deepNullish"
// function that transforms the final schema, and avoid repeating here,
// but I think that's a zod-antipattern (see discussion around deepPartial).
const minimalSchema = z
  .object({
    displayedFields: z
      .object({
        input: z.boolean().nullish(),
        expected: z.boolean().nullish(),
        metadata: z.boolean().nullish(),
      })
      .nullish(),
    maxConcurrency: z.number().nullish(),
    strict: z.boolean().nullish(),
    extraMessages: z.string().nullish(),
  })
  .nullish();

export const playgroundSettingsSchema = z.preprocess(
  (val) => {
    // Worth checking what kind of error gets propagated if this parse fails.
    const parsedVal = minimalSchema.parse(val);
    return {
      displayedFields: {
        input: parsedVal?.displayedFields?.input ?? true,
        expected: parsedVal?.displayedFields?.expected ?? false,
        metadata: parsedVal?.displayedFields?.metadata ?? false,
      },
      maxConcurrency: parsedVal?.maxConcurrency,
      strict: parsedVal?.strict,
      extraMessages: parsedVal?.extraMessages,
    };
  },
  z.object({
    displayedFields: z.object({
      input: z.boolean(),
      expected: z.boolean(),
      metadata: z.boolean(),
    }),
    maxConcurrency: z.number().nullish(),
    strict: z.boolean().nullish(),
    extraMessages: z.string().nullish(),
  }),
);

export const DEFAULT_PLAYGROUND_SETTINGS =
  playgroundSettingsSchema.parse(undefined);

export type PlaygroundSettings = z.infer<typeof playgroundSettingsSchema>;
