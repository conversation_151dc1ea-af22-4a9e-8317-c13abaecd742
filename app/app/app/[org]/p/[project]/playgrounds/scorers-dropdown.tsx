import { Button, type ButtonProps } from "#/ui/button";
import { InfoI<PERSON>, Percent, Plus } from "lucide-react";
import {
  forwardRef,
  type HTMLAttributes,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { type UIFunctionData, type UIFunction } from "#/ui/prompts/schema";
import { ProjectContext } from "../projectContext";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { isEmpty } from "#/utils/object";
import { type FunctionType, type PromptData } from "@braintrust/typespecs";
import { makePromptDropdownContext } from "./prompts-dropdown";
import {
  PLACEHOLDER_PY,
  PLACEHOLDER_TS,
} from "#/ui/prompts/function-editor/code-editor-placeholders";
import { useFunctionEditorPrompt } from "#/ui/prompts/function-editor/use-function-editor-prompt";
import { newId } from "braintrust";
import { PythonLogo, TypescriptLogo } from "../../../onboarding-logos";

const [ScorersDropdownProvider, useScorersDropdown] = makePromptDropdownContext(
  {
    name: "Scorers",
    args: {
      functionObjectType: "scorer",
      query: "", // not used
      btqlFilter: "function_type = 'scorer'",
    },
  },
);

export { ScorersDropdownProvider, useScorersDropdown };

type ScorerItem = UIFunction & {
  projectName?: string;
  disabled?: boolean;
  Icon?: React.ReactNode;
};

const TS_SCORER_FUNCTION_DATA = {
  type: "code",
  data: {
    type: "inline",
    runtime_context: {
      runtime: "node",
      version: "22",
    },
    code: PLACEHOLDER_TS,
  },
} as const;

const PY_SCORER_FUNCTION_DATA = {
  type: "code",
  data: {
    type: "inline",
    runtime_context: {
      runtime: "python",
      version: "3.12",
    },
    code: PLACEHOLDER_PY,
  },
} as const;

export const ScorersDropdown = ({
  onAddScorer,
  projectName,
  selectedScorerIds = [],
  disabledOriginIds = [],
  buttonProps,
  dropdownMenuContentProps,
  children,
  open,
  isInSubMenu,
  hideBlankOption,
}: React.PropsWithChildren<{
  onAddScorer: (scorer: {
    promptData?: PromptData;
    functionData?: UIFunctionData;
    origin?: PromptData["origin"];
    functionType: FunctionType;
  }) => void;
  projectName: string;
  selectedScorerIds?: string[];
  disabledOriginIds?: string[];
  buttonProps?: ButtonProps;
  dropdownMenuContentProps?: DropdownMenuContentProps;
  open?: boolean;
  isInSubMenu?: boolean;
  hideBlankOption?: boolean;
}>) => {
  const { projectId } = useContext(ProjectContext);
  const {
    promptsByProjectSorted: scorersByProjectSorted,
    isLoading,
    invalidate,
  } = useScorersDropdown();

  useEffect(() => {
    invalidate();
    //invalidate once on mount
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { coercedFunction: promptScorer } = useFunctionEditorPrompt({
    type: "scorer",
    projectId: projectId ?? "",
    modeType: "create",
  });

  const dropdownData = useMemo(() => {
    if (isEmpty(scorersByProjectSorted)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      scorersByProjectSorted[0]?.projectName === projectName
        ? scorersByProjectSorted[0].prompts.map((p) => ({
            ...p,
            disabled: disabledOriginIds.includes(p.id),
          }))
        : undefined;

    const otherProjects = scorersByProjectSorted
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ projectName, prompts }) =>
        prompts.map((p) => ({
          ...p,
          projectName,
          disabled: disabledOriginIds.includes(p.id),
        })),
      );

    const subGroups: {
      groupLabel: string;
      items: ScorerItem[];
    }[] = [];

    if (!hideBlankOption) {
      subGroups.push({
        groupLabel: "Blank scorer",
        items: [
          {
            ...promptScorer,
            Icon: <Percent className="size-3" />,
            name: "LLM judge scorer",
          },
          {
            name: "TypeScript scorer",
            function_data: TS_SCORER_FUNCTION_DATA,
            function_type: "scorer",
            id: newId(),
            Icon: <TypescriptLogo className="size-3" />,
            _xact_id: "0",
            project_id: projectId ?? "",
          },
          {
            name: "Python scorer",
            function_data: PY_SCORER_FUNCTION_DATA,
            function_type: "scorer",
            id: newId(),
            Icon: <PythonLogo className="size-3" />,
            _xact_id: "0",
            project_id: projectId ?? "",
          },
        ],
      });
    }

    if (otherProjects.length > 0) {
      subGroups.unshift({ groupLabel: "Other projects", items: otherProjects });
    }

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [
    scorersByProjectSorted,
    projectName,
    hideBlankOption,
    disabledOriginIds,
    promptScorer,
    projectId,
  ]);

  const selectedPrompts = useMemo(
    () =>
      scorersByProjectSorted
        .flatMap((project) =>
          project.prompts.map((p) => ({
            ...p,
            projectName: project.projectName,
            disabled: disabledOriginIds.includes(p.id),
          })),
        )
        .filter((p) => selectedScorerIds.includes(p.id)),
    [disabledOriginIds, scorersByProjectSorted, selectedScorerIds],
  );

  const PromptMenuItem = forwardRef<
    HTMLDivElement,
    { item: ScorerItem } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item, ...rest }, ref) => {
        return (
          <DropdownMenuItem
            ref={ref}
            {...rest}
            className="flex gap-2"
            disabled={item.disabled}
            onSelect={() => {
              onAddScorer({
                promptData: item.prompt_data ?? undefined,
                functionData: item.function_data,
                origin: {
                  prompt_id: item.id,
                  project_id: item.project_id,
                  prompt_version: item._xact_id,
                },
                functionType: "scorer",
              });
            }}
            title={`${item.name} from ${item.projectName ?? "this project"}`}
          >
            {item.Icon && <div className="flex-none">{item.Icon}</div>}
            <span className="flex-1">{item.name}</span>
            {item.project_id === projectId ? null : (
              <span className="max-w-24 flex-none truncate text-primary-500">
                {item.projectName}
              </span>
            )}
          </DropdownMenuItem>
        );
      },
      [onAddScorer, projectId],
    ),
  );

  return (
    <NestedDropdown<ScorerItem>
      open={open}
      isInSubMenu={isInSubMenu}
      objectType="scorer"
      align={dropdownMenuContentProps?.align}
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name?.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      DropdownItemComponent={PromptMenuItem}
      selectedItems={selectedPrompts}
      additionalActions={
        hideBlankOption
          ? undefined
          : [
              {
                className: "pointer-events-none",
                label: (
                  <span className="block flex-none text-xs text-primary-500">
                    <InfoIcon className="inline-block size-3 flex-none" /> To
                    use a scorer to evaluate outputs instead of adding a scorer
                    task, use the + Scorer button.
                  </span>
                ),
              },
            ]
      }
    >
      {isInSubMenu
        ? (children ?? (
            <Button
              size="xs"
              variant="ghost"
              className="gap-1"
              isLoading={isLoading}
              Icon={Plus}
              {...buttonProps}
            >
              Scorer
            </Button>
          ))
        : null}
    </NestedDropdown>
  );
};
