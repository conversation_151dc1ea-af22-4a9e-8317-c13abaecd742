import { expect, test } from "vitest";
import { playgroundSettingsSchema } from "./settings";

test("playground settings", () => {
  const defaultSettings = playgroundSettingsSchema.parse(undefined);
  for (const emptySettings of [
    null,
    undefined,
    {},
    { displayedFields: null },
    { displayedFields: undefined },
    { displayedFields: {} },
  ]) {
    const settings = playgroundSettingsSchema.parse(emptySettings);
    expect(settings).toEqual(defaultSettings);
  }

  expect(() =>
    playgroundSettingsSchema.parse({
      displayedFields: {
        input: "foo",
        expected: false,
        metadata: false,
      },
    }),
  ).toThrow();
});
