"use client";

import { useContext } from "react";
import { ProjectContext } from "./projectContext";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { Button, buttonVariants } from "#/ui/button";
import {
  Activity,
  Beaker,
  ChartSpline,
  Plus,
  Settings2,
  <PERSON><PERSON><PERSON>,
  <PERSON>rk<PERSON>,
} from "lucide-react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import Link from "next/link";
import { cn } from "#/utils/classnames";
import { getMonitorLink } from "../../monitor/getMonitorLink";
import { type PromptSessionSummary } from "./playgrounds/playground-actions";
import { smartDateFormat } from "#/ui/date";
import { getProjectConfigurationLink } from "./getProjectLink";
import { useQueryFunc } from "#/utils/react-query";
import { useEntityContextActions } from "../useEntityContextActions";
import { getOrgLink, getOrgSettingsLink } from "../../getOrgLink";
import { EntityContextMenu } from "#/ui/entity-context-menu";
import { type getProjectSummary } from "../../org-actions";
import {
  getPlaygroundLink,
  getPlaygroundsLink,
} from "../../prompt/[prompt]/getPromptLink";
import {
  getExperimentLink,
  getExperimentsLink,
} from "./experiments/[experiment]/getExperimentLink";
import { Avatar } from "#/ui/avatar";
import { useOrg } from "#/utils/user";
import { LogsSparklines } from "./logs-sparklines";
import { useCreateNewPlaygroundDialog } from "./playgrounds/clientpage";
import { useAvailableModels } from "#/ui/prompts/models";
import { LibraryTiles } from "./LibraryTiles";
import { AccessFailed } from "#/ui/access-failed";
import { Skeleton } from "#/ui/skeleton";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { CreateSingleExperimentNullStateDialog } from "../../prompt/[prompt]/create-experiment-dialog";
import { ProjectPromptsDropdownProvider } from "./playgrounds/prompts-dropdown";
import { AgentsDropdownProvider } from "./playgrounds/agents-dropdown";
import { getProjectLogsLink } from "./logs/getProjectLogsLink";
import { ScorersDropdownProvider } from "./playgrounds/scorers-dropdown";
import { useIsFeatureEnabled } from "#/lib/feature-flags";

export default function ClientPage({
  playgrounds,
}: {
  playgrounds: PromptSessionSummary[];
}) {
  const org = useOrg();
  const project = useContext(ProjectContext);
  const orgName = org.name;
  const projectName = project.projectName;

  const { invalidate, isLoading } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  const { noConfiguredSecrets } = useAvailableModels({ orgName });
  const projectSummaryMetrics = useIsFeatureEnabled("projectSummaryMetrics");

  const { actions: projectActions, modals: projectActionModals } =
    useEntityContextActions({
      entityType: "project",
      onUpdate: invalidate,
      reloadPageOnUpdateArgs: {
        getEditedEntityLink: (projectName) =>
          getProjectConfigurationLink({ orgName, projectName }),
        getDeletedEntityLink: () => getOrgLink({ orgName }),
      },
    });

  const experimentsSorted = project.experiments
    .sort(
      (a, b) =>
        new Date(b.created ?? "").getTime() -
        new Date(a.created ?? "").getTime(),
    )
    .slice(0, 8);

  const playgroundsSorted = playgrounds
    .sort(
      (a, b) =>
        new Date(b.created ?? "").getTime() -
        new Date(a.created ?? "").getTime(),
    )
    .slice(0, 8);

  const {
    newPromptDialog: createPlaygroundDialog,
    onCreate: openCreatePlaygroundDialog,
  } = useCreateNewPlaygroundDialog({
    orgName,
    projectName,
    projectId: project.projectId ?? "",
    numPlaygrounds: 0,
    refreshPromptSessions: invalidate,
    defaultEntryPoint: "projectOverviewCreateButton",
  });

  if (!isLoading && !project.projectId) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return (
    <ProjectPromptsDropdownProvider>
      <AgentsDropdownProvider>
        <ScorersDropdownProvider>
          <div className="flex items-center bg-primary-50 px-3 pt-1 pb-2">
            <h1 className="pr-1 text-sm font-semibold">Overview</h1>
            {project.projectId && (
              <EntityContextMenu
                objectType="project"
                objectId={project.projectId}
                objectName={project.projectName}
                orgName={project.orgName}
                projectName={project.projectName}
                buttonClassName="w-7 h-7 flex-none"
                handleEdit={() =>
                  projectActions.editEntityName({
                    entityId: project.projectId ?? "",
                    entityName: project.projectName,
                    trackAnalytics: {
                      source: "project_page_overflow_control",
                    },
                  })
                }
                handleDelete={() =>
                  projectActions.deleteEntity({
                    entityId: project.projectId ?? "",
                    entityName: project.projectName,
                    trackAnalytics: {
                      source: "project_page_overflow_control",
                    },
                  })
                }
                handleCopyId={() =>
                  projectActions.copyEntityId({
                    entityId: project.projectId ?? "",
                  })
                }
              />
            )}
            {projectActionModals}
            <div className="grow" />
            <CopyToClipboardButton
              size="xs"
              variant="ghost"
              copyMessage="Copy project ID to clipboard"
              textToCopy={project.projectId ?? ""}
              className="flex-none font-mono text-xs font-medium text-primary-500"
            >
              <span className="font-inter text-[10px] tracking-wider uppercase opacity-60">
                Project ID
              </span>
              {project.projectId}
            </CopyToClipboardButton>
          </div>
          <BodyWrapper
            innerClassName="flex"
            outerClassName={cn(HEIGHT_WITH_DOUBLE_TOP_OFFSET)}
          >
            <MainContentWrapper className="h-auto flex-1 overflow-auto pt-0">
              <div className="mt-4 mb-10 flex flex-wrap items-center gap-2">
                <Link
                  className={cn(buttonVariants({ size: "xs" }))}
                  href={getMonitorLink({
                    orgName,
                    projectId: project.projectId ?? undefined,
                  })}
                >
                  <ChartSpline className="size-3" />
                  Go to monitor
                </Link>
                <Link
                  className={cn(buttonVariants({ size: "xs" }))}
                  href={getProjectLogsLink({
                    orgName,
                    projectName,
                  })}
                >
                  <Activity className="size-3" />
                  View logs
                </Link>
                {/* <Link
              className={cn(buttonVariants({ size: "xs" }))}
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/btql`}
            >
              <Asterisk className="size-3" />
              BTQL sandbox
            </Link> */}
                <Link
                  className={cn(buttonVariants({ size: "xs" }))}
                  href={getProjectConfigurationLink({
                    orgName,
                    projectName,
                  })}
                >
                  <Settings2 className="size-3" />
                  Configure project
                </Link>
                {noConfiguredSecrets && (
                  <Link
                    className={cn(buttonVariants({ size: "xs" }))}
                    href={`${getOrgSettingsLink({
                      orgName,
                    })}/secrets`}
                  >
                    <Sparkle className="size-3" />
                    Setup AI providers
                  </Link>
                )}
              </div>
              {projectSummaryMetrics && (
                <div className="mb-12">
                  <h2 className="mb-4 flex items-center gap-2 text-xs text-primary-600">
                    <Activity className="size-3 text-primary-400" />
                    Logs in the past 7 days
                  </h2>
                  <LogsSparklines context="overview-page" />
                </div>
              )}
              <div className="flex flex-col gap-8 lg:flex-row">
                <div className="flex max-w-(--breakpoint-lg) flex-1 flex-col gap-8">
                  <div className="group flex flex-col gap-2">
                    <div className="mb-3 flex items-center gap-2">
                      <h2 className="flex flex-1 items-center gap-2 text-xs text-primary-600">
                        <span className="block flex-none rounded-md bg-accent-50 p-1.5 text-accent-500 dark:bg-accent-50/50">
                          <Shapes className="size-4" />
                        </span>
                        Recent playgrounds
                      </h2>
                      <Button
                        size="xs"
                        variant="ghost"
                        className="text-primary-400"
                        onClick={openCreatePlaygroundDialog}
                        Icon={Plus}
                      >
                        Create
                      </Button>
                      <Link
                        href={getPlaygroundsLink({ orgName, projectName })}
                        className={cn(
                          buttonVariants({ size: "xs", variant: "ghost" }),
                          "text-primary-400",
                        )}
                      >
                        View all
                      </Link>
                      {createPlaygroundDialog}
                    </div>
                    <div className="flex flex-col gap-2">
                      {playgroundsSorted.length === 0 ? (
                        <>
                          <div className="flex h-32 flex-col items-center justify-center gap-3 rounded-md border border-primary-200/50 bg-primary-50 text-xs text-primary-400">
                            No playgrounds yet
                          </div>
                          {createPlaygroundDialog}
                        </>
                      ) : (
                        playgroundsSorted.map((playground) => {
                          return (
                            <Link
                              key={playground.id}
                              className="flex items-center rounded-md bg-accent-50 p-3 text-sm transition-colors hover:bg-accent-100 dark:bg-accent-50/50 dark:hover:bg-accent-100"
                              prefetch
                              href={getPlaygroundLink({
                                orgName,
                                projectName,
                                playgroundName: playground.name,
                              })}
                            >
                              <span className="flex-1 truncate font-medium">
                                {playground.name}
                              </span>
                              {playground.created && (
                                <span className="flex-none text-xs text-primary-500">
                                  Created{" "}
                                  {smartDateFormat(
                                    new Date(playground.created).getTime(),
                                  )}
                                </span>
                              )}
                              {playground.avatar_url && (
                                <Avatar
                                  size="xs"
                                  imgUrl={playground.avatar_url}
                                />
                              )}
                            </Link>
                          );
                        })
                      )}
                    </div>
                  </div>
                  <div className="group flex flex-col gap-2">
                    <div className="mb-3 flex items-center gap-2">
                      <h2 className="flex flex-1 items-center gap-2 text-xs text-primary-600">
                        <span className="block flex-none rounded-md bg-violet-50 p-1.5 text-violet-500 dark:bg-violet-950/50">
                          <Beaker className="size-4" />
                        </span>
                        Recent experiments
                      </h2>
                      <CreateSingleExperimentNullStateDialog>
                        <Button
                          size="xs"
                          variant="ghost"
                          className="text-primary-400"
                          Icon={Plus}
                        >
                          Create
                        </Button>
                      </CreateSingleExperimentNullStateDialog>
                      <Link
                        href={getExperimentsLink({ orgName, projectName })}
                        className={cn(
                          buttonVariants({ size: "xs", variant: "ghost" }),
                          "text-primary-400",
                        )}
                      >
                        View all
                      </Link>
                    </div>
                    <div className="flex flex-col gap-2">
                      {isLoading || project.isExperimentsLoading ? (
                        <>
                          <Skeleton className="h-11 rounded-md" />
                          <Skeleton className="h-11 rounded-md opacity-80" />
                          <Skeleton className="h-11 rounded-md opacity-60" />
                          <Skeleton className="h-11 rounded-md opacity-40" />
                        </>
                      ) : experimentsSorted.length === 0 ? (
                        <div className="flex h-32 flex-col items-center justify-center gap-3 rounded-md border border-primary-200/50 bg-primary-50 text-xs text-primary-400">
                          No experiments yet
                        </div>
                      ) : (
                        experimentsSorted.map((exp) => {
                          return (
                            <Link
                              key={exp.id}
                              prefetch
                              className="flex items-center gap-2 rounded-md bg-violet-50 p-3 text-sm transition-colors hover:bg-violet-100 dark:bg-violet-950/50 dark:hover:bg-violet-900"
                              href={getExperimentLink({
                                orgName,
                                projectName,
                                experimentName: exp.name,
                              })}
                            >
                              <span className="flex-1 truncate font-medium">
                                {exp.name}
                              </span>
                              {exp.created && (
                                <span className="flex-none text-xs text-primary-500">
                                  Created{" "}
                                  {smartDateFormat(
                                    new Date(exp.created).getTime(),
                                  )}
                                </span>
                              )}
                              {exp.user?.avatar_url && (
                                <Avatar
                                  size="xs"
                                  imgUrl={exp.user.avatar_url}
                                />
                              )}
                            </Link>
                          );
                        })
                      )}
                    </div>
                  </div>
                </div>
                <LibraryTiles isProjectLoading={isLoading} />
              </div>
              {createPlaygroundDialog}
            </MainContentWrapper>
          </BodyWrapper>
        </ScorersDropdownProvider>
      </AgentsDropdownProvider>
    </ProjectPromptsDropdownProvider>
  );
}
