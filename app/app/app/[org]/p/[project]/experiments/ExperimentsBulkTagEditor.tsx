import { use<PERSON>allback, useContext, useMemo } from "react";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { TagsCombobox } from "#/ui/trace/tags";
import { SelectionBarButton } from "#/ui/table/selection-bar";
import { TagIcon } from "lucide-react";
import { toast } from "sonner";

interface ExperimentsBulkTagEditorProps {
  selectedExperiments: { id: string; tags?: string[] | null | string }[];
  onTagsUpdated: () => void;
}

export function ExperimentsBulkTagEditor({
  selectedExperiments,
  onTagsUpdated,
}: ExperimentsBulkTagEditorProps) {
  const { mutateExperiments } = useContext(ProjectContext);

  // Calculate union of tags (all unique tags across selected experiments)
  const unionTags = useMemo(() => {
    const allTags = new Set<string>();
    selectedExperiments.forEach((exp) => {
      let expTags: string[] = [];
      if (exp.tags) {
        if (typeof exp.tags === "string") {
          try {
            const parsed = JSON.parse(exp.tags);
            expTags = Array.isArray(parsed) ? parsed : [];
          } catch {
            // If parsing fails, treat as empty array
            expTags = [];
          }
        } else if (Array.isArray(exp.tags)) {
          expTags = exp.tags;
        }
      }
      expTags.forEach((tag) => allTags.add(tag));
    });
    return Array.from(allTags);
  }, [selectedExperiments]);

  // Update tags for all selected experiments using new API fields
  const updateExperimentTags = useCallback(
    async (action: "add" | "remove", tagToModify: string) => {
      try {
        const body: {
          id: string[];
          add_tags?: string[];
          remove_tags?: string[];
        } = {
          id: selectedExperiments.map((exp) => exp.id),
        };

        if (action === "add") {
          body.add_tags = [tagToModify];
        } else {
          body.remove_tags = [tagToModify];
        }

        const response = await fetch("/api/experiment/patch_id", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body),
        });

        if (!response.ok) {
          const respText = await response.text();
          toast.error("Failed to update experiment tags", {
            description: respText,
          });
          return;
        }

        mutateExperiments();
        onTagsUpdated();
      } catch (error) {
        console.error("Failed to update experiment tags", error);
        toast.error("Failed to update experiment tags");
      }
    },
    [selectedExperiments, mutateExperiments, onTagsUpdated],
  );

  const handleTagChange = useCallback(
    (label: string) => {
      const isSelectedOnSome = unionTags.includes(label);

      if (isSelectedOnSome) {
        updateExperimentTags("remove", label);
      } else {
        updateExperimentTags("add", label);
      }
    },
    [unionTags, updateExperimentTags],
  );

  return (
    <TagsCombobox selectedTags={unionTags} onChange={handleTagChange} canAddTag>
      <SelectionBarButton Icon={TagIcon} />
    </TagsCombobox>
  );
}
