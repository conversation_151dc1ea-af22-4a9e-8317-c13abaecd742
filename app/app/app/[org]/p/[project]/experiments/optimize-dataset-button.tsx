import { Button } from "#/ui/button";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Blend } from "lucide-react";
import {
  type ContextObject,
  useGlobalChat,
} from "#/ui/optimization/use-global-chat-context";
import { type TableSelection } from "#/ui/table/useTableSelection";
import { useHotkeys } from "react-hotkeys-hook";
import { useState } from "react";
import { BasicTooltip } from "#/ui/tooltip";

const OptimizeDatasetButton = ({
  selectionProps,
  onTuneScorer,
}: {
  selectionProps: TableSelection;
  onTuneScorer: () => void;
}) => {
  const { setCurrentSessionContextObjects, setIsChatOpen, handleSendMessage } =
    useGlobalChat();
  const [open, setOpen] = useState(false);

  useHotkeys(
    "l",
    (e) => {
      e.preventDefault();
      setOpen((prev) => !prev);
    },
    {
      description: "Loop shortcuts",
    },
  );
  const { getSelectedRowsWithData } = selectionProps;

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <BasicTooltip
        tooltipContent={
          <>
            Loop shortcuts
            <span className="ml-2.5 inline-block opacity-50">L</span>
          </>
        }
        side="bottom"
      >
        <DropdownMenuTrigger asChild>
          <Button size="xs" variant="border" Icon={Blend} />
        </DropdownMenuTrigger>
      </BasicTooltip>
      <DropdownMenuContent align="start">
        <DropdownMenuItem
          onSelect={() => {
            const rows = getSelectedRowsWithData();
            const newContextObjects: Record<string, ContextObject> =
              Object.fromEntries(
                rows.map((row) => [
                  String(row.id),
                  {
                    id: String(row.id),
                    name: String(row.id),
                    resource: "dataset",
                    datasetId: row.dataset_id,
                    numberOfRecords: row.count,
                    expected: row.expected,
                    input: row.input,
                    metadata: row.metadata,
                    origin: row.origin,
                    output: row.output,
                    scores: row.scores,
                    tags: row.tags,
                    created: row.created,
                    error: row.error,
                    metrics: row.metrics,
                  },
                ]),
              );

            setCurrentSessionContextObjects((prev) => ({
              ...prev,
              ...newContextObjects,
            }));
            setIsChatOpen(true);
          }}
        >
          Add to Loop chat
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            const rows = getSelectedRowsWithData();

            const rowData = rows.map((row) => ({
              input: row.input,
              output: row.output,
              expected: row.expected,
              score: row.scores,
            }));

            handleSendMessage(
              {
                id: crypto.randomUUID(),
                type: "user_message",
                message: `Optimize the prompt to improve the output of the following rows: ${JSON.stringify(rowData, null, 2)}`,
              },
              {
                clearContextObjects: false,
                clearUserMessage: false,
              },
            );
            setTimeout(() => {
              setIsChatOpen(true);
            }, 250);
          }}
        >
          Quick optimize
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onTuneScorer}>Tune scorer</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default OptimizeDatasetButton;
