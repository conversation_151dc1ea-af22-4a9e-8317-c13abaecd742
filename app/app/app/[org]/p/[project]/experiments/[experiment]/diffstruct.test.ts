import { describe, expect, test } from "vitest";
import { Field, Utf8, Struct, Float, Precision } from "apache-arrow";
import { buildDiffStruct } from "./diffstruct";

const DISPLAY_PATHS = {
  type: "node" as const,
  children: {
    id: { type: "leaf" as const },
    _xact_id: { type: "leaf" as const },
    input: { type: "leaf" as const },
    output: { type: "leaf" as const },
    expected: { type: "leaf" as const },
    tags: { type: "leaf" as const },
    scores: {
      type: "node" as const,
      children: {
        score1: { type: "leaf" as const },
        score2: { type: "leaf" as const },
      },
    },
    metadata: { type: "leaf" as const },
  },
};

describe("buildDiffStruct", () => {
  test("no-ops empty list", () => {
    expect(
      buildDiffStruct({
        experimentsFields: [],
        names: [],
        parentPath: [],
        displayPaths: { type: "leaf" },
        allKeysOrdered: [],
      }),
    ).toEqual([]);
  });

  test("throws error when extra field name", () => {
    expect(() =>
      buildDiffStruct({
        experimentsFields: [[new Field("extra", new Utf8())]],
        names: [],
        parentPath: [],
        displayPaths: { type: "leaf" },
        allKeysOrdered: [],
      }),
    ).toThrow();
  });

  test("builds struct no comparison experiment", () => {
    const fieldNames = Object.keys(DISPLAY_PATHS.children).filter(
      (f) => f !== "scores",
    );
    const fields = [
      ...fieldNames.map((fName) => new Field(fName, new Utf8())),
      new Field(
        "scores",
        new Struct([
          new Field("score1", new Float(Precision.DOUBLE)),
          new Field("score2", new Float(Precision.DOUBLE)),
        ]),
      ),
    ];

    const output = buildDiffStruct({
      experimentsFields: [fields, []],
      names: ["e1", "e2"],
      parentPath: [],
      displayPaths: DISPLAY_PATHS,
      allKeysOrdered: Object.keys(DISPLAY_PATHS.children),
    });
    // trim whitespace from value so we can test it easier
    expect(
      output.map(({ name, value }) => ({
        name,
        value: value.replace(/\s/g, ""),
      })),
    ).toEqual([
      {
        name: "id",
        value:
          'struct_pack("_bt_internal_right":=e1."id","_bt_internal_left":=null)',
      },
      {
        name: "_xact_id",
        value:
          'struct_pack("_bt_internal_right":=e1."_xact_id","_bt_internal_left":=null)',
      },
      {
        name: "input",
        value:
          'struct_pack("_bt_internal_right":=e1."input","_bt_internal_left":=null)',
      },
      {
        name: "output",
        value:
          'struct_pack("_bt_internal_right":=e1."output","_bt_internal_left":=null)',
      },
      {
        name: "expected",
        value:
          'struct_pack("_bt_internal_right":=e1."expected","_bt_internal_left":=null)',
      },
      {
        name: "tags",
        value:
          'struct_pack("_bt_internal_right":=e1."tags","_bt_internal_left":=null)',
      },
      {
        name: "scores",
        value:
          'struct_pack("score1":=struct_pack("_bt_internal_right":=e1."scores"."score1","_bt_internal_left":=null),"score2":=struct_pack("_bt_internal_right":=e1."scores"."score2","_bt_internal_left":=null))',
      },
      {
        name: "metadata",
        value:
          'struct_pack("_bt_internal_right":=e1."metadata","_bt_internal_left":=null)',
      },
    ]);
  });

  test("builds structs with standard paths and matching schemas", () => {
    const fieldNames = Object.keys(DISPLAY_PATHS.children).filter(
      (f) => f !== "scores",
    );
    const fields = [
      ...fieldNames.map((fName) => new Field(fName, new Utf8())),
      new Field(
        "scores",
        new Struct([
          new Field("score1", new Float(Precision.DOUBLE)),
          new Field("score2", new Float(Precision.DOUBLE)),
        ]),
      ),
    ];

    const output = buildDiffStruct({
      experimentsFields: [fields, fields],
      names: ["e1", "e2"],
      parentPath: [],
      displayPaths: DISPLAY_PATHS,
      allKeysOrdered: Object.keys(DISPLAY_PATHS.children),
    });
    // trim whitespace from value so we can test it easier
    expect(
      output.map(({ name, value }) => ({
        name,
        value: value.replace(/\s/g, ""),
      })),
    ).toEqual([
      {
        name: "id",
        value:
          'struct_pack("_bt_internal_right":=e1."id","_bt_internal_left":=e2."id")',
      },
      {
        name: "_xact_id",
        value:
          'struct_pack("_bt_internal_right":=e1."_xact_id","_bt_internal_left":=e2."_xact_id")',
      },
      {
        name: "input",
        value:
          'struct_pack("_bt_internal_right":=e1."input","_bt_internal_left":=e2."input")',
      },
      {
        name: "output",
        value:
          'struct_pack("_bt_internal_right":=e1."output","_bt_internal_left":=e2."output")',
      },
      {
        name: "expected",
        value:
          'struct_pack("_bt_internal_right":=e1."expected","_bt_internal_left":=e2."expected")',
      },
      {
        name: "tags",
        value:
          'struct_pack("_bt_internal_right":=e1."tags","_bt_internal_left":=e2."tags")',
      },
      {
        name: "scores",
        value:
          'struct_pack("score1":=struct_pack("_bt_internal_right":=e1."scores"."score1","_bt_internal_left":=e2."scores"."score1"),"score2":=struct_pack("_bt_internal_right":=e1."scores"."score2","_bt_internal_left":=e2."scores"."score2"))',
      },
      {
        name: "metadata",
        value:
          'struct_pack("_bt_internal_right":=e1."metadata","_bt_internal_left":=e2."metadata")',
      },
    ]);
  });

  test("builds structs with non-matching schemas", () => {
    const output = buildDiffStruct({
      experimentsFields: [
        [
          new Field("fieldA", new Utf8()),
          new Field(
            "scores",
            new Struct([
              new Field("commonScore", new Float(Precision.DOUBLE)),
              new Field("scoreA", new Float(Precision.DOUBLE)),
            ]),
          ),
        ],
        [
          new Field(
            "scores",
            new Struct([
              new Field("commonScore", new Float(Precision.DOUBLE)),
              new Field("scoreB", new Float(Precision.DOUBLE)),
            ]),
          ),
        ],
      ],
      names: ["e1", "e2"],
      parentPath: [],
      displayPaths: {
        type: "node" as const,
        children: {
          fieldA: { type: "leaf" as const },
          scores: {
            type: "node" as const,
            children: {
              commonScore: { type: "leaf" as const },
              scoreA: { type: "leaf" as const },
            },
          },
          metadata: { type: "leaf" as const },
        },
      },

      allKeysOrdered: ["fieldA", "scores"],
    });

    // trim whitespace from value so we can test it easier
    expect(
      output.map(({ name, value }) => ({
        name,
        value: value.replace(/\s/g, ""),
      })),
    ).toEqual(
      [
        {
          name: "fieldA",
          value:
            'struct_pack("_bt_internal_right":=e1."fieldA","_bt_internal_left":=null)',
        },
        {
          name: "scores",
          value: `struct_pack(
          "commonScore" := struct_pack(
            "_bt_internal_right":=e1."scores"."commonScore",
            "_bt_internal_left":=e2."scores"."commonScore"
          ),
          "scoreA" := struct_pack(
            "_bt_internal_right":=e1."scores"."scoreA",
            "_bt_internal_left":=null
          ),
          "scoreB" := struct_pack(
            "_bt_internal_right":=null,
            "_bt_internal_left":=e2."scores"."scoreB"
          )
        )`,
        },
      ].map(({ name, value }) => ({ name, value: value.replace(/\s/g, "") })),
    );
  });

  test("builds structs for more than 2 experiments", () => {
    const fieldNames = Object.keys(DISPLAY_PATHS.children).filter(
      (f) => f !== "scores",
    );
    const fields = [
      ...fieldNames.map((fName) => new Field(fName, new Utf8())),
      new Field(
        "scores",
        new Struct([
          new Field("score1", new Float(Precision.DOUBLE)),
          new Field("score2", new Float(Precision.DOUBLE)),
        ]),
      ),
    ];

    const output = buildDiffStruct({
      experimentsFields: [fields, fields, fields, fields],
      names: ["e1", "e2", "e3", "e4"],
      parentPath: [],
      displayPaths: DISPLAY_PATHS,
      allKeysOrdered: Object.keys(DISPLAY_PATHS.children),
    });
    // trim whitespace from value so we can test it easier
    expect(
      output.map(({ name, value }) => ({
        name,
        value: value.replace(/\s/g, ""),
      })),
    ).toEqual(
      [
        {
          name: "id",
          value: `
            struct_pack(
              "_bt_internal_right":=e1."id",
              "_bt_internal_left":=e2."id",
              "e3":=e3."id",
              "e4":=e4."id"
            )
          `,
        },
        {
          name: "_xact_id",
          value: `
            struct_pack(
              "_bt_internal_right":=e1."_xact_id",
              "_bt_internal_left":=e2."_xact_id",
              "e3":=e3."_xact_id",
              "e4":=e4."_xact_id"
            )
            `,
        },
        {
          name: "input",
          value: `
            struct_pack(
              "_bt_internal_right":=e1."input",
              "_bt_internal_left":=e2."input",
              "e3":=e3."input",
              "e4":=e4."input"
              )`,
        },
        {
          name: "output",
          value: `struct_pack(
              "_bt_internal_right":=e1."output",
              "_bt_internal_left":=e2."output",
              "e3":=e3."output",
              "e4":=e4."output"
            )`,
        },
        {
          name: "expected",
          value: `
            struct_pack(
              "_bt_internal_right":=e1."expected",
              "_bt_internal_left":=e2."expected",
              "e3":=e3."expected",
              "e4":=e4."expected"
            )`,
        },
        {
          name: "tags",
          value: `
            struct_pack(
              "_bt_internal_right":=e1."tags",
              "_bt_internal_left":=e2."tags",
              "e3":=e3."tags",
              "e4":=e4."tags"
            )`,
        },
        {
          name: "scores",
          value: `
          struct_pack(
            "score1":=struct_pack(
              "_bt_internal_right":=e1."scores"."score1",
              "_bt_internal_left":=e2."scores"."score1",
              "e3":=e3."scores"."score1",
              "e4":=e4."scores"."score1"
            ),
            "score2":=struct_pack(
              "_bt_internal_right":=e1."scores"."score2",
              "_bt_internal_left":=e2."scores"."score2",
              "e3":=e3."scores"."score2",
              "e4":=e4."scores"."score2"
            )
          )`,
        },
        {
          name: "metadata",
          value: `
            struct_pack(
              "_bt_internal_right":=e1."metadata",
              "_bt_internal_left":=e2."metadata",
              "e3":=e3."metadata",
              "e4":=e4."metadata"
            )
            `,
        },
      ].map(({ name, value }) => ({ name, value: value.replace(/\s/g, "") })),
    );
  });

  test("picks comparison struct if it exists within the same path", () => {
    const displayPaths = {
      type: "node" as const,
      children: {
        replace: {
          type: "node" as const,
          children: {
            leaf: { type: "leaf" as const },
          },
        },
      },
    };

    const output = buildDiffStruct({
      experimentsFields: [
        [new Field("replace", new Utf8())],
        [new Field("replace", new Struct([new Field("leaf", new Utf8())]))],
      ],
      names: ["e1", "e2"],
      parentPath: [],
      displayPaths,
      allKeysOrdered: ["replace"],
    });
    // trim whitespace from value so we can test it easier
    expect(
      output.map(({ name, value }) => ({
        name,
        value: value.replace(/\s/g, ""),
      })),
    ).toEqual([
      {
        name: "replace",
        value:
          'struct_pack("leaf":=struct_pack("_bt_internal_right":=null,"_bt_internal_left":=e2."replace"."leaf"))',
      },
    ]);
  });
});
