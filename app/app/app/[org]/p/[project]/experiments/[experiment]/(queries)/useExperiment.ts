import {
  type LoadedExperiment,
  loadExperimentScans,
  type LoadExperimentScansParams,
  useLoadComparisonExperiments,
} from "./loaders";
import { dbQuery, useDuckDB } from "#/utils/duckdb";
import {
  useQueries,
  useQuery,
  useQueryClient,
  type UseQueryResult,
} from "@tanstack/react-query";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  combineResults,
  invalidateId,
  keepPreviousQueries,
} from "#/utils/react-query";
import {
  useAbortCleanup,
  useCleanupTempTables,
} from "#/utils/queries/useTempDuckTable";
import { type ExperimentItem } from "../ExperimentHeader";
import {
  type DiffModeState,
  useRegressionFilterState,
} from "#/ui/query-parameters";
import { type RegressionFilter } from "../regressions-query";
import { type Schema, type TypeMap } from "apache-arrow";
import { makeSimpleMatchFilters, type Search } from "#/utils/search/search";
import {
  type DataObjectSearch,
  type DataObjectType,
} from "#/utils/btapi/btapi";
import { makeFilterClauses } from "#/utils/search-btql";
import useEvent from "react-use-event-hook";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { SINGLETON_DATASET_ID } from "../../../playgrounds/[playground]/playx/stream";
import { type SpanSummaryQueryParams } from "./experimentQueries";
import { projectedPathsFn } from "../experiment-table";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";
import { BT_ASSIGNMENTS } from "#/utils/assign";

export const PLAYGROUND_EXPERIMENT_FIELDS = [
  "dataset_id",
  "playground_row_id",
  "playground_xact_id",
];

type LoadExperimentScansResult = Awaited<
  ReturnType<typeof loadExperimentScans>
>;
export type LoadedExperimentSpanSummary = LoadExperimentScansResult & {
  queryKeys: string[];
  id: string;
  name: string;
  isEmpty: boolean;
  spanSummarySchema: Schema<TypeMap> | null;
};

export type PrimaryExperimentSpanSummary = LoadedExperimentSpanSummary & {
  hasTrials: boolean;
  plainFilters: string[];
};

type DatasetParam = {
  loadedDataset: LoadedExperiment & { count: number };
  scoreNames: string[];
  metricNames: string[];
  metadataFields: string[][];
  generation?: { id: string; name: string };
};

export function useExperimentScan({
  experiment,
  loadExperimentScanParams,
  search,
  datasetParam,
}: {
  experiment: LoadedExperiment;
  loadExperimentScanParams: LoadExperimentScansParams;
  search: Search;
  datasetParam?: DatasetParam;
}) {
  const queryKeys = [
    experiment.id,
    experiment.searchKeyName,
    experiment.auditLog?.searchKeyName,
    experiment.summary?.searchKeyName,
  ];
  const queryClient = useQueryClient();

  const placeholderDatasetFn = useMemo(
    () =>
      (
        prev:
          | {
              projectedPaths: string[];
              customColumns: string[] | undefined;
              experiment: PrimaryExperimentSpanSummary;
            }
          | undefined,
      ) => {
        if (datasetParam && !experiment.id && !experiment.scan) {
          const { loadedExperiment, id, name } =
            loadDatasetExperiment(datasetParam);

          const { paths } = projectedPathsFn({
            schema: datasetParam.loadedDataset.schema ?? null,
            hasComments: false,
            hasOrigin: true,
          });
          return {
            projectedPaths: [...paths, ...PLAYGROUND_EXPERIMENT_FIELDS],
            customColumns: undefined,
            experiment: {
              queryKeys: [id],
              id,
              name,
              ...loadedExperiment,
              isEmpty: datasetParam.loadedDataset.count === 0,
              spanSummarySchema: datasetParam.loadedDataset.schema,
              hasTrials: false,
              plainFilters: [],
            },
          };
        }

        return prev;
      },
    [datasetParam, experiment.id, experiment.scan],
  );
  const duck = useDuckDB();
  const { data, isPending, isLoading, error } = useQuery({
    queryKey: [
      queryKeys,
      "experimentScan",
      {
        experiment,
        loadExperimentScanParams,
        filter: search.filter,
        match: search.match,
        tagFilters: search.tag,
        datasetParam,
      },
    ],
    queryFn: async ({ signal }) => {
      const conn = await duck!.connect();

      const { paths, customColumns } = projectedPathsFn({
        schema: experiment.schema ?? null,
        hasOrigin:
          experiment.schema?.fields.some((f) => f.name === "origin") ||
          !!datasetParam,
        hasAssignments:
          experiment.summary === undefined
            ? !!experiment.schema?.fields.some((f) => f.name === BT_ASSIGNMENTS)
            : !!experiment.summary.schema?.fields.find(
                (f) => f.name === BT_ASSIGNMENTS,
              ),
        hasComments:
          experiment.summary === undefined
            ? !!experiment.auditLog?.scan
            : !!experiment.summary.schema?.fields.find(
                (f) => f.name === "comments",
              ),
        summarySchema: experiment.summary?.schema,
      });
      const projectedPaths = [
        ...paths,
        ...(datasetParam ? PLAYGROUND_EXPERIMENT_FIELDS : []),
      ];

      const nonComparisonSearch = Object.fromEntries(
        Object.entries(search).map(([k, v]) => [
          k,
          v?.filter((c) => c.comparison?.experimentId == null),
        ]),
      );

      const filters = searchToFilter({
        hasBackendSummary: experiment.summary !== undefined,
        search: nonComparisonSearch,
        customColumns: loadExperimentScanParams.customColumns,
        projectedPaths,
      });

      const {
        loadedExperiment: result,
        id,
        name,
      } = await loadExperimentWithDataset({
        conn,
        signal,
        loadedData: {
          datasetParam,
          experiment,
        },
        loadExperimentScanParams: {
          ...loadExperimentScanParams,
          plainFilters: filters.plain,
          postAggregationFilters: filters.postAggregation,
          commentsFilters: filters.comments,
          tags: filters.tagFilters,
          customColumnFilters: filters.customColumns,
          tableNameSuffix: "primary",
        },
        hasSummaryTable: !!experiment.summary,
      });

      const [emptyExperimentData, trialsData, isEmpty] = await Promise.all([
        dbQuery(
          conn,
          signal,
          result.tableScan
            ? `SELECT * FROM (${result.tableScan}) LIMIT 0`
            : null,
        ),
        dbQuery(
          conn,
          signal,
          result.tableScan
            ? `SELECT EXISTS (
            SELECT 1
            FROM (${result.tableScan}) t
            GROUP BY comparison_key
            HAVING COUNT(*) > 1
          ) AS exists`
            : null,
        ),
        isEmptyQuery(
          conn,
          signal,
          experiment.summary === undefined
            ? (experiment.scan ?? datasetParam?.loadedDataset.scan)
            : experiment.summary.scan,
        ),
      ]);

      return {
        projectedPaths: projectedPaths.concat(
          result.customColumns?.map(({ name }) => name) ?? [],
        ),
        customColumns,
        experiment: {
          id,
          name,
          queryKeys: [
            id,
            result.tempTableName ?? "",
            experiment.auditLog?.searchKeyName ?? "",
            experiment.summary?.searchKeyName ?? "",
          ],
          spanSummarySchema: emptyExperimentData?.schema ?? null,
          hasTrials: !isEmpty && !!trialsData?.get(0)?.exists,
          isEmpty,
          plainFilters: filters.plain,
          ...result,
        },
      };
    },
    throwOnError: false,
    staleTime: Infinity,
    // never cache this query, since the data is tied to the same table name
    gcTime: 0,
    placeholderData: (d) => placeholderDatasetFn(d),
    enabled:
      !!duck && !!datasetParam
        ? !!datasetParam.loadedDataset?.scan &&
          datasetParam.loadedDataset.refreshed > 0 &&
          (datasetParam.loadedDataset.summary === undefined ||
            !!(
              datasetParam.loadedDataset.summary.scan &&
              datasetParam.loadedDataset.summary.refreshed > 0
            ))
        : !!experiment.id &&
          !!experiment.scan &&
          experiment.refreshed > 0 &&
          (experiment.summary === undefined ||
            !!(
              experiment.summary.scan &&
              experiment.summary.schema &&
              experiment.summary.refreshed > 0
            )),
  });
  useCleanupTempTables("primaryExperiment", data?.experiment?.tempTableName);

  const experimentData = data?.experiment;
  const projectedPaths = useMemo(() => {
    const filteredCustomColumns = data?.customColumns?.filter(
      (col) =>
        !!loadExperimentScanParams.customColumns?.find(
          ({ name }) => name.toLowerCase() === col.toLowerCase(),
        ),
    );
    return (data?.projectedPaths ?? []).concat(filteredCustomColumns || []);
  }, [
    data?.projectedPaths,
    data?.customColumns,
    loadExperimentScanParams.customColumns,
  ]);

  const [ready, setReady] = useState(0);
  useEffect(() => {
    setReady((v) => v + 1);
    invalidateId(queryClient, data?.experiment?.tempTableName);
    // Since the tempTableName may not change depending on the useQuery params,
    // we need this dependency to depend on the any query result change
  }, [data, queryClient]);

  return {
    data: experimentData,
    projectedPaths,
    isExperimentEmpty: !!experimentData?.isEmpty,
    isPending,
    isLoading,
    ready,
    error,
  };
}

const comparisonTempTablesKey = "comparisonExperiments";

export type ComparisonExperimentSpanSummary = LoadedExperimentSpanSummary &
  LoadedExperiment & {
    index: number;
  };

export function useComparisonExperimentScans({
  experiment,
  loadExperimentScanParams,
  objectType = "experiment",
  selectedComparisonExperiments,
  onSetSelectedComparisonExperiments,
  tableScansPaused,
  diffMode,
  setDiffMode,
  search,
  setSearch,
  enableSummary,
}: {
  experiment: LoadedExperiment;
  loadExperimentScanParams: LoadExperimentScansParams;
  objectType?: DataObjectType;
  selectedComparisonExperiments: (ExperimentItem & {
    search?: DataObjectSearch;
    datasetParam?: DatasetParam;
    skipLoading?: boolean;
  })[];
  onSetSelectedComparisonExperiments?: Dispatch<
    SetStateAction<ExperimentItem[]>
  >;
  tableScansPaused?: boolean;
  diffMode: DiffModeState;
  setDiffMode: Dispatch<SetStateAction<DiffModeState>>;
  search: Search;
  setSearch: Dispatch<SetStateAction<Search>>;
  enableSummary: boolean;
}) {
  const [comparisonsInitialized, setComparisonsInitialized] = useState(
    !!onSetSelectedComparisonExperiments,
  );

  const [regressionFilters, setRegressionFilters] = useRegressionFilterState();

  const addRegressionFilter = useEvent((filter: RegressionFilter) => {
    setRegressionFilters((prev) => (prev ? [...prev, filter] : [filter]));
    setDiffMode({
      enabled: true,
      enabledValue: "between_experiments",
    });
  });
  const clearRegressionFilters = useEvent(() => {
    setRegressionFilters(null);
  });

  const setSelectedComparisonExperiments = useCallback(
    (newValues: ExperimentItem[], initializing: boolean = false) => {
      onSetSelectedComparisonExperiments?.(newValues);
      setRegressionFilters((prev) =>
        (prev ?? []).filter(
          ({ experimentId }) =>
            experimentId === "all" ||
            experimentId === "any" ||
            experimentId === experiment?.id ||
            newValues.find((e) => e.id === experimentId),
        ),
      );
      setSearch((prev) => ({
        ...prev,
        sort: prev.sort?.filter(
          (s) =>
            !s.comparison ||
            newValues.find((e) => e.id === s.comparison?.experimentId),
        ),
      }));

      if (initializing) {
        setComparisonsInitialized(true);
        return;
      }

      if (
        newValues.length === 0 &&
        diffMode?.enabled &&
        diffMode.enabledValue === "between_experiments"
      ) {
        setDiffMode({
          enabled: false,
          enabledValue: "between_experiments",
        });
      }
      if (newValues.length > selectedComparisonExperiments.length) {
        setDiffMode({ enabled: true, enabledValue: "between_experiments" });
      }
    },
    [
      diffMode,
      setDiffMode,
      selectedComparisonExperiments,
      onSetSelectedComparisonExperiments,
      setRegressionFilters,
      setSearch,
      experiment?.id,
    ],
  );

  const loadableExperiments = useMemo(() => {
    return selectedComparisonExperiments.filter((e) => !e.skipLoading);
  }, [selectedComparisonExperiments]);
  const loadedComparisonExperiments = useLoadComparisonExperiments(
    loadableExperiments,
    !!tableScansPaused,
    objectType,
    enableSummary,
  );
  const loadedExperiments = useMemo(() => {
    return selectedComparisonExperiments
      .map(({ id, datasetParam }) => {
        const loadedExperiment = loadedComparisonExperiments.find(
          (e) => e.id === id,
        );
        if (!loadedExperiment && !datasetParam) {
          return null;
        }

        if (datasetParam) {
          return {
            datasetParam,
            loadedExperiment,
          };
        }

        if (loadedExperiment) {
          return {
            loadedExperiment,
            datasetParam,
          };
        }

        return null;
      })
      .filter((v) => !!v);
  }, [selectedComparisonExperiments, loadedComparisonExperiments]);

  const queryClient = useQueryClient();
  const duck = useDuckDB();
  const combineResultsWithIndex = useCallback(
    (
      results: UseQueryResult<LoadedExperimentSpanSummary & LoadedExperiment>[],
    ) => {
      const result = combineResults(results);
      return {
        ...result,
        data: result.data.map((d, i) => ({ ...d, index: i })),
      };
    },
    [],
  );

  const abortCleanup = useAbortCleanup();
  const queries = useMemo(
    () =>
      loadedExperiments.map((d) => {
        const hasDataset = "datasetParam" in d && !!d.datasetParam;
        const hasExperiment = "loadedExperiment" in d && !!d.loadedExperiment;
        const loadedDatasetParam = hasDataset ? d.datasetParam : undefined;
        const loadedExperiment = hasExperiment ? d.loadedExperiment : undefined;
        const base = hasDataset
          ? d.datasetParam.loadedDataset
          : d.loadedExperiment;

        const comparisonSearch = Object.fromEntries(
          Object.entries(search).map(([k, v]) => [
            k,
            !loadedExperiment?.id
              ? v
              : v?.filter(
                  (c) => c.comparison?.experimentId === loadedExperiment.id,
                ),
          ]),
        );

        return {
          queryKey: [
            [
              loadedExperiment?.id,
              loadedExperiment?.searchKeyName,
              loadedExperiment?.auditLog?.searchKeyName,
              loadedExperiment?.summary?.searchKeyName,
            ],
            "comparisonExperimentScan",
            [
              loadedDatasetParam?.loadedDataset.id,
              loadedDatasetParam?.generation?.id,
            ],
            {
              scan: base.scan,
              schema: base.schema,
              auditLogScan: base.auditLog?.scan ?? null,
              loadExperimentScanParams,
              datasetParam: loadedDatasetParam,
              filter: comparisonSearch.filter,
              match: comparisonSearch.match,
              tagFilters: comparisonSearch.tag,
              summary:
                loadedExperiment?.summary === undefined
                  ? null
                  : (loadedExperiment?.summary.scoreNames || []).concat(
                      loadedExperiment?.summary.metricNames || [],
                    ),
            },
          ],
          queryFn: async ({ signal }: { signal: AbortSignal }) => {
            const conn = await duck!.connect();

            const filters = searchToFilter({
              hasBackendSummary: experiment.summary !== undefined,
              search: comparisonSearch,
              customColumns: loadExperimentScanParams.customColumns,
              projectedPaths: [],
            });

            const {
              loadedExperiment: loaded,
              id,
              name,
            } = await loadExperimentWithDataset({
              conn,
              signal,
              loadedData: hasDataset
                ? {
                    datasetParam: d.datasetParam,
                    experiment: loadedExperiment,
                  }
                : {
                    datasetParam: loadedDatasetParam,
                    experiment: d.loadedExperiment,
                  },
              loadExperimentScanParams: {
                ...loadExperimentScanParams,
                plainFilters: filters.plain,
                postAggregationFilters: filters.postAggregation,
                commentsFilters: filters.comments,
                tags: filters.tagFilters,
                customColumnFilters: filters.customColumns,
              },
              beforeCreateFn: (t) => abortCleanup(comparisonTempTablesKey, t),
              hasSummaryTable: enableSummary,
            });

            const [emptyScan, isEmpty] = await Promise.all([
              dbQuery(
                conn,
                signal,
                loaded.tableScan
                  ? `SELECT * FROM (${loaded.tableScan}) LIMIT 0`
                  : null,
              ),
              isEmptyQuery(
                conn,
                signal,
                base.summary === undefined
                  ? (base?.scan ?? null)
                  : base.summary.scan,
              ),
              //countQuery(conn, signal, loadedExperiment?.scan ?? null, true),
              //countQuery(conn, signal, loaded.tableScan ?? "", true),
            ]);

            return {
              // for invalidation
              queryKeys: [
                id,
                loaded.tempTableName ?? "",
                base?.auditLog?.searchKeyName ?? "",
                base?.summary?.searchKeyName ?? "",
              ],
              ...base,
              id,
              name,
              ...loaded,
              isEmpty,
              spanSummarySchema: emptyScan?.schema ?? null,
            };
          },
          placeholderData: () => {
            if (hasDataset && !hasExperiment) {
              const { loadedExperiment, id, name } = loadDatasetExperiment(
                d.datasetParam,
              );
              return {
                queryKeys: [id],
                ...d.datasetParam.loadedDataset,
                id,
                name,
                ...loadedExperiment,
                isEmpty: d.datasetParam.loadedDataset.count === 0,
                spanSummarySchema: d.datasetParam.loadedDataset.schema,
              };
            }
            return keepPreviousQueries<
              LoadedExperimentSpanSummary & LoadedExperiment
            >(queryClient, {
              predicate: (query) => {
                const queryKey = query.queryKey;
                if (queryKey[1] !== "comparisonExperimentScan") {
                  return false;
                }
                return (
                  Array.isArray(queryKey[0]) &&
                  Array.isArray(queryKey[2]) &&
                  (loadedDatasetParam?.loadedDataset
                    ? queryKey[2][0] === loadedDatasetParam.loadedDataset.id &&
                      queryKey[2][1] === loadedDatasetParam.generation?.id
                    : queryKey[0].some(
                        (k) => !!k && loadedExperiment?.id === k,
                      ))
                );
              },
            })();
          },
          staleTime: Infinity,
          throwOnError: false,
          enabled:
            !!duck && !!loadedDatasetParam
              ? !!loadedDatasetParam.loadedDataset?.scan &&
                loadedDatasetParam.loadedDataset.refreshed > 0 &&
                (loadedDatasetParam.loadedDataset.summary === undefined ||
                  !!(
                    loadedDatasetParam.loadedDataset.summary.scan &&
                    loadedDatasetParam.loadedDataset.summary.refreshed > 0
                  ))
              : !!loadedExperiment &&
                loadedExperiment.refreshed > 0 &&
                !!loadedExperiment.scan &&
                (loadedExperiment.summary === undefined ||
                  !!(
                    loadedExperiment.summary.scan &&
                    loadedExperiment.summary.schema &&
                    loadedExperiment.summary.refreshed > 0
                  )),
          // never cache this query, since the data is tied to the same table name
          gcTime: 0,
        };
      }),
    [
      loadedExperiments,
      search,
      loadExperimentScanParams,
      duck,
      experiment.summary,
      enableSummary,
      abortCleanup,
      queryClient,
    ],
  );

  const {
    data: comparisonExperimentData,
    isPending,
    errors,
    isPlaceholderData,
  } = useQueries({
    queries,
    combine: combineResultsWithIndex,
  });

  const tempTableNames = useMemo(() => {
    return comparisonExperimentData.map((e) => e.tempTableName);
  }, [comparisonExperimentData]);
  useCleanupTempTables(comparisonTempTablesKey, tempTableNames);

  useEffect(() => {
    isPlaceholderData.forEach((isPlaceholder, i) => {
      if (isPlaceholder) {
        return;
      }
      invalidateId(queryClient, comparisonExperimentData[i]?.tempTableName);
    });
  }, [comparisonExperimentData, isPlaceholderData, queryClient]);

  return {
    selectedComparisonExperiments,
    setSelectedComparisonExperiments,
    comparisonsInitialized,
    regressionFilters,
    setRegressionFilters,
    addRegressionFilter,
    clearRegressionFilters,
    comparisonExperimentData,
    isPending: isPending && selectedComparisonExperiments.length > 0,
    errors,
  };
}

export const datasetSpanTypeInfoExpr = ({
  generationId,
  isDataset,
  inProgress,
  runningRowId,
}: {
  generationId?: string;
  isDataset?: boolean;
  inProgress?: boolean;
  runningRowId?: string | null;
}) =>
  `json_object(
  'name', ${isDataset ? "'dataset'" : "'eval'"},
  'type', ${isDataset ? "'dataset'" : "'eval'"},
  'cached', 0,
  'remote', 0,
  'has_error', false,
  'in_progress', ${
    inProgress
      ? runningRowId
        ? `CASE WHEN id = '${runningRowId}' THEN true ELSE false END`
        : "true"
      : "false"
  }
  ${generationId ? `, 'generation', '${generationId}'` : ""}
)`;

async function isEmptyQuery(
  conn: AsyncDuckDBConnection,
  signal: AbortSignal,
  scan: string | null,
) {
  if (!scan) {
    return true;
  }
  const exists = await dbQuery(
    conn,
    signal,
    `SELECT EXISTS (SELECT 1 FROM (${scan})) as exists`,
  );
  return !exists?.get(0)?.exists;
}

export async function countQuery(
  conn: AsyncDuckDBConnection,
  signal: AbortSignal,
  scan: string | null,
  isRootAvailable: boolean,
) {
  const count = await dbQuery(
    conn,
    signal,
    scan
      ? `SELECT COUNT(1) as count FROM (${scan})
     WHERE ${isRootAvailable ? "is_root" : "root_span_id = span_id"}`
      : null,
  );
  return Number(count?.get(0)?.count ?? 0);
}

async function loadExperimentWithDataset({
  conn,
  signal,
  loadedData,
  loadExperimentScanParams,
  beforeCreateFn,
  hasSummaryTable,
}: {
  conn: AsyncDuckDBConnection;
  signal: AbortSignal;
  loadedData:
    | {
        datasetParam?: undefined;
        experiment: LoadedExperiment;
      }
    | {
        datasetParam: DatasetParam;
        experiment?: LoadedExperiment;
      };
  loadExperimentScanParams: SpanSummaryQueryParams;
  beforeCreateFn?: (t: string) => void;
  hasSummaryTable: boolean;
}): Promise<{
  loadedExperiment: LoadExperimentScansResult;
  id: string;
  name: string;
}> {
  if (!loadedData.datasetParam) {
    return {
      loadedExperiment: await loadExperimentScans(conn, signal, {
        experiment: loadedData.experiment,
        spanSummaryQueryParams: loadExperimentScanParams,
        originDatasetRowId: false,
        beforeCreateFn,
        hasSummaryTable,
      }),
      id: loadedData.experiment.id,
      name: loadedData.experiment.name,
    };
  }

  const { loadedDataset, generation, scoreNames, metadataFields } =
    loadedData.datasetParam;
  const datasetScan = datasetScanWithSpanTypeInfo(
    loadedDataset.scan,
    generation?.id ?? "",
  );

  if (loadedData.experiment) {
    const { experiment } = loadedData;
    const loadedExperiment =
      experiment.scan && experiment.refreshed > 0
        ? await loadExperimentScans(conn, signal, {
            experiment,
            spanSummaryQueryParams: loadExperimentScanParams,
            originDatasetRowId: true,
            beforeCreateFn,
            hasSummaryTable,
          })
        : null;
    const hasFinishedScores = !!(
      await dbQuery(
        conn,
        signal,
        loadedExperiment?.tableScan
          ? `SELECT EXISTS(SELECT 1 FROM (${loadedExperiment.tableScan}) WHERE metrics.end IS NOT NULL) as has_end_time`
          : null,
      )
    )?.get(0)?.has_end_time;

    const hasFilters =
      (loadExperimentScanParams.plainFilters?.length ?? 0) > 0 ||
      (loadExperimentScanParams.postAggregationFilters?.length ?? 0) > 0 ||
      (loadExperimentScanParams.commentsFilters?.length ?? 0) > 0 ||
      (loadExperimentScanParams.customColumnFilters?.length ?? 0) > 0 ||
      (loadExperimentScanParams.tags?.length ?? 0) > 0;

    if (loadedExperiment?.tableScan) {
      const tableScan = `(SELECT
          dataset.* EXCLUDE (span_type_info, origin, scores, output, is_root, comparison_key, metrics, playground_row_id, playground_xact_id, error),
          exp.* EXCLUDE (id, span_id, root_span_id, project_id, scores, input, expected, metadata, created, comparison_key, span_type_info, _xact_id, tags),
          ${!!hasFinishedScores ? "exp.scores" : "dataset.scores"} AS scores,
          dataset.comparison_key AS comparison_key,
          COALESCE(exp.span_type_info, dataset.span_type_info) AS span_type_info,
          -- special fields for rowId generation and re-running metadata
          exp.id AS playground_row_id,
          exp._xact_id AS playground_xact_id,
        FROM
          (${datasetScan}) dataset
          ${!hasFilters ? "LEFT" : ""} JOIN (${loadedExperiment.tableScan}) exp ON ${!loadedDataset.id || loadedDataset.id === SINGLETON_DATASET_ID ? "true" : "dataset.id = exp.dataset_row_id"}
        )`;

      return {
        loadedExperiment: {
          ...loadedExperiment,
          scoreFields: hasFinishedScores
            ? loadedExperiment.scoreFields
            : scoreNames,
          // Override these from the dataset metadata so that grouping can stay stable.
          // If the user writes custom metadata then it might not work, but this should be good enough.
          metadataRootFields: metadataFields,
          metadataFields,
          tableScan,
        },
        id: experiment.id,
        name: experiment.name,
      };
    }
  }

  return loadDatasetExperiment(loadedData.datasetParam);
}

function loadDatasetExperiment(datasetParam: DatasetParam) {
  const { loadedDataset, scoreNames, metricNames, metadataFields, generation } =
    datasetParam;

  const tableScan = datasetScanWithSpanTypeInfo(
    loadedDataset.scan,
    generation?.id ?? "",
  );

  return {
    loadedExperiment: {
      scoreFields: scoreNames ?? [],
      metricFields: metricNames,
      metadataRootFields: metadataFields ?? [],
      customColumns: undefined,
      customColumnsSchema: undefined,
      metadataFields: metadataFields ?? [],
      inputFields: [],
      outputFields: [],
      expectedFields: [],
      tempTableName: undefined,
      tableScan,
      metricDefinitions: [],
    },
    id: generation?.id ?? "",
    name: generation?.name ?? "",
  };
}

function datasetScanWithSpanTypeInfo(
  scan: string | null,
  generationId: string,
) {
  // span_type_info is created in the mounted object with an empty generation
  // so we can have a stabilized schema in list view
  return scan
    ? `(SELECT * EXCLUDE (span_type_info),
        ${datasetSpanTypeInfoExpr({ generationId })} as span_type_info,
     FROM (${scan}))`
    : null;
}

function searchToFilter({
  hasBackendSummary,
  search,
  customColumns,
  projectedPaths,
}: {
  hasBackendSummary: boolean;
  search: Search;
  customColumns?: CustomColumnDefinition[];
  projectedPaths: string[];
}) {
  const filters = hasBackendSummary
    ? {
        plain: [],
        postAggregation: [],
        comments: [],
        customColumns: [],
        tagFilters: [],
      }
    : makeFilterClauses(search.filter ?? [], customColumns);
  filters.plain.push(...makeSimpleMatchFilters(search, projectedPaths));
  filters.tagFilters.push(...(search.tag?.map((t) => t.text) ?? []));

  return filters;
}
