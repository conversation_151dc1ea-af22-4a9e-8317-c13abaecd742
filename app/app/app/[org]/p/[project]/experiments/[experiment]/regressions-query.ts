import {
  type SelectionType,
  selectionTypeSchema,
  type SelectionTypesEnum,
} from "#/ui/charts/selectionTypes";
import { type CustomColumn } from "#/utils/custom-columns/use-custom-columns";
import { type Search } from "#/utils/search/search";
import { doubleQuote, jsonPath, singleQuote } from "#/utils/sql-utils";
import { COMPARISON_DIGITS } from "@braintrust/local/query";
import { useCallback } from "react";
import { z } from "zod";

export const comparisonTypeEnum = z.enum([
  "improvements",
  "regressions",
  "equal",
  "exists",
]);
export type ComparisonType = z.infer<typeof comparisonTypeEnum>;

const regressionExperimentId = z.union([
  z.string(),
  z.literal("any"),
  z.literal("all"),
]);

const regressionGroupSchema = z.union([
  z.object({
    type: z.literal("metadata").optional().default("metadata"),
    path: z.array(z.string()),
    value: z.string(),
  }),
  z.object({
    // custom columns
    type: z.literal("custom"),
    //expr: z.string(),
    name: z.string(),
    value: z.string(),
  }),
  z.object({ type: z.literal("input"), value: z.string() }),
]);
type RegressionGroup = z.infer<typeof regressionGroupSchema>;

const regressionFilterSchema = z.object({
  comparisonType: comparisonTypeEnum,
  field: selectionTypeSchema,
  anyField: z.boolean().optional(),
  group: regressionGroupSchema.optional(),
  experimentId: regressionExperimentId,
});
export type RegressionFilter = z.infer<typeof regressionFilterSchema>;

export const regressionFiltersSchema = z.array(regressionFilterSchema);

type SelectedSummaryMeasures = {
  scores: SelectionType[];
  metrics: SelectionType[];
};

function buildRegressionFilterExpr({
  filters,
  experimentIds,
  alias,
  selectedSummaryMeasures,
  fastExperimentSummary,
}: {
  filters: RegressionFilter[] | null;
  experimentIds: string[];
  alias: string;
  selectedSummaryMeasures: SelectedSummaryMeasures;
  fastExperimentSummary?: boolean;
}) {
  // todo: maybe group by group expression
  const expr = (filters ?? [])
    .flatMap((f) => {
      if (!experimentIds.includes(f.experimentId)) {
        return [];
      }

      const groupExpr =
        groupComparisonExpr(f.group, fastExperimentSummary) || "true";
      const fieldExprs = f.anyField
        ? `(${
            Object.values(selectedSummaryMeasures)
              .flatMap((measures) =>
                measures.map((m) =>
                  fieldComparisonExpr({ ...f, field: m }, alias),
                ),
              )
              .join(" OR\n") || "true"
          })`
        : fieldComparisonExpr(f, alias);
      return [`(${groupExpr} AND ${fieldExprs})`];
    })
    .join(" AND\n ");

  return expr;
}

function groupComparisonExpr(
  group: RegressionGroup | undefined,
  fastExperimentSummary?: boolean,
) {
  if (!group) {
    return "";
  }
  const rhs =
    group.value === "null" ? "IS NULL" : `= ${singleQuote(group.value)}`;
  switch (group.type) {
    case "input":
      return `e1.comparison_key ${rhs}`;
    case "custom":
      return fastExperimentSummary ? "true" : `e1.${group.name} ${rhs}`;
    case "metadata":
      if (group.path.length === 0) {
        return "";
      }
      const path = jsonPath(group.path);
      return fastExperimentSummary
        ? "true"
        : `JSON_EXTRACT_STRING(e1.metadata, ${path}) ${rhs}`;
  }
}

export function groupToString(group: RegressionGroup | undefined) {
  if (!group) {
    return "";
  }

  const rhs = group.value === "null" ? " is NULL" : `="${group.value}"`;
  switch (group.type) {
    case "metadata":
      return `${group.path.slice(-1)[0]}${rhs}`;
    case "custom":
      return `${group.name}${rhs}`;
    case "input":
      return `input${rhs}`;
    default:
      return "";
  }
}

function fieldComparisonExpr(f: RegressionFilter, comparisonRelation: string) {
  const comparisonIdent = `${comparisonRelation}.${f.field.type}s.${doubleQuote(f.field.value)}`;
  if (f.comparisonType === "exists") {
    return `${comparisonIdent} IS NOT NULL`;
  }
  return `e1.${f.field.type}s.${doubleQuote(f.field.value)} ${comparisonTypeOp(f.field.type, f.comparisonType)} ${comparisonIdent}`;
}

function comparisonTypeOp(
  fieldType: SelectionTypesEnum,
  comparisonType: Exclude<ComparisonType, "exists">,
) {
  switch (comparisonType) {
    case "regressions":
      return fieldType === "metric" ? ">" : "<";
    case "improvements":
      return fieldType === "metric" ? "<" : ">";
    case "equal":
      return "=";
  }
}

function buildComparisonFilterQuery({
  primaryExperimentScan,
  experimentId,
  comparisonExperimentData,
  regressionFilters,
  search,
  selectedSummaryMeasures,
  customColumns,
  fastExperimentSummary,
}: {
  primaryExperimentScan: string | null;
  experimentId: string;
  comparisonExperimentData: {
    id: string;
    tableScan: string | null;
    scoreFields: string[];
  }[];
  regressionFilters: RegressionFilter[] | null;
  search: Search;
  selectedSummaryMeasures: SelectedSummaryMeasures;
  customColumns?: CustomColumn[];
  fastExperimentSummary?: boolean;
}) {
  const comparisonFilterIds = Object.values(search).reduce((acc, clauses) => {
    clauses?.forEach((c) => {
      if (c.comparison) {
        acc.add(c.comparison.experimentId);
      }
    });
    return acc;
  }, new Set<string>());
  if (
    !primaryExperimentScan ||
    ((!regressionFilters || regressionFilters.length === 0) &&
      comparisonFilterIds.size === 0)
  ) {
    return null;
  }

  const aliasMap = Object.fromEntries(
    comparisonExperimentData.map((e, i) => [e.id, `e${i + 2}`]),
  );

  // avg across scores and metrics grouped by comparison_key for trials
  const query = `WITH
    ${[
      {
        scan: primaryExperimentScan,
        scoreFields: undefined,
      },
      ...comparisonExperimentData.map((e) => ({
        scan: `SELECT * FROM (${e.tableScan})`,
        scoreFields: e.scoreFields,
      })),
    ]
      .map((e, i) => {
        const alias = `e${i + 1}`;
        return `${alias} AS (SELECT
          ${[
            "comparison_key",
            "ANY_VALUE(metadata) AS metadata",
            customColumns?.length
              ? `${customColumns.map(({ name }) => `ANY_VALUE(${name}) AS ${name}`).join(",\n")}`
              : null,
            ...Object.entries(selectedSummaryMeasures).map(
              ([field, measures]) =>
                measures.length === 0
                  ? `NULL AS ${field}`
                  : `struct_pack(${measures
                      .map((m) => `${structField(m, e.scoreFields)}`)
                      .join(",\n")}) AS ${field}
              `,
            ),
          ]
            .filter(Boolean)
            .join(",\n")}
          FROM (${e.scan})
          GROUP BY 1)`;
      })
      .join(",\n")}
    ${
      comparisonExperimentData.length > 0
        ? `
    , comparisons AS (
      -- generate a CTE with all comparison data to help us process "any experiment" filters
      ${comparisonExperimentData
        .map((_, i) => {
          const alias = `e${i + 2}`;
          return `SELECT * FROM ${alias}`;
        })
        .join(" UNION ALL\n")}
    )`
        : ""
    }
    SELECT comparison_key FROM e1
    WHERE
    ${
      comparisonFilterIds.size === 0
        ? "true"
        : Array.from(comparisonFilterIds)
            .map((id) => {
              const alias = aliasMap[id];
              return alias
                ? `EXISTS (SELECT 1 FROM ${alias} WHERE e1.comparison_key = ${alias}.comparison_key)`
                : "true";
            })
            .join(" AND\n")
    } AND
    ${
      buildRegressionFilterExpr({
        filters: regressionFilters,
        experimentIds: [experimentId, "all"],
        alias: "e1",
        selectedSummaryMeasures,
        fastExperimentSummary,
      }) || "true"
    } AND
    ${
      comparisonExperimentData
        .flatMap((e, i) => {
          const alias = `e${i + 2}`;
          const expr = buildRegressionFilterExpr({
            filters: regressionFilters,
            experimentIds: [e.id, "all"],
            alias,
            selectedSummaryMeasures,
            fastExperimentSummary,
          });
          return expr
            ? [
                `EXISTS (SELECT 1 FROM ${alias} WHERE e1.comparison_key = ${alias}.comparison_key AND (
            ${expr}
          ))`,
              ]
            : [];
        })
        .join(" AND \n") || "true"
    } AND (
        ${
          comparisonExperimentData.length > 0 && regressionFilters
            ? regressionFilters
                .flatMap((f) => {
                  if (f.experimentId !== "any") {
                    return [];
                  }
                  const expr = buildRegressionFilterExpr({
                    filters: [f],
                    experimentIds: ["any"],
                    alias: "comparisons",
                    selectedSummaryMeasures,
                    fastExperimentSummary,
                  });
                  return [
                    `EXISTS (SELECT 1 FROM comparisons WHERE e1.comparison_key = comparisons.comparison_key AND (${
                      expr
                    }
                ))`,
                  ];
                })
                .join(" AND \n") || "true"
            : "true"
        }
    )`;

  return query;
}

function structField(st: SelectionType, scoreFields?: string[]) {
  const key = doubleQuote(st.value);
  if (
    st.type === "score" &&
    scoreFields != null &&
    !scoreFields.includes(st.value)
  ) {
    return `${key} := NULL::DOUBLE`;
  }
  return `${key} := ROUND(AVG(${st.type}s.${key}), ${COMPARISON_DIGITS})`;
}

export function useComparisonKeyFilterClause({
  experimentId,
  primaryExperimentScan,
  comparisonExperimentData,
  regressionFilters,
  search,
  selectedSummaryMeasures,
  comparisonKey,
  customColumns,
  fastExperimentSummary,
}: {
  experimentId: string;
  primaryExperimentScan: string | null;
  comparisonExperimentData: {
    id: string;
    tableScan: string | null;
    scoreFields: string[];
  }[];
  regressionFilters: RegressionFilter[];
  search: Search;
  selectedSummaryMeasures: SelectedSummaryMeasures;
  comparisonKey: (path: string) => string;
  customColumns?: CustomColumn[];
  fastExperimentSummary?: boolean;
}) {
  return useCallback(
    (relation: string) => {
      if (!experimentId) {
        return "";
      }
      const query = buildComparisonFilterQuery({
        primaryExperimentScan,
        experimentId,
        comparisonExperimentData,
        regressionFilters,
        search,
        selectedSummaryMeasures,
        customColumns,
        fastExperimentSummary,
      });
      if (!query) {
        return "";
      }
      return `SEMI JOIN (${query}) AS comparison_keys ON ${comparisonKey(
        relation,
      )} IS NOT DISTINCT FROM ${comparisonKey("comparison_keys")}`;
    },
    [
      experimentId,
      primaryExperimentScan,
      comparisonExperimentData,
      regressionFilters,
      selectedSummaryMeasures,
      comparisonKey,
      customColumns,
      search,
      fastExperimentSummary,
    ],
  );
}
