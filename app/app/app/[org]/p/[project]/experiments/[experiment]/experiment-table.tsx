import {
  usePlaygroundFullscreenTaskIndex,
  type DiffModeState,
} from "#/ui/query-parameters";
import { type TableSelection } from "#/ui/table/useTableSelection";
import { VizQuery, type VizQueryProps } from "#/ui/viz-query";
import {
  useRef,
  useState,
  useMemo,
  type Dispatch,
  type SetStateAction,
  memo,
  useCallback,
  useEffect,
  useContext,
} from "react";
import { type <PERSON><PERSON><PERSON><PERSON><PERSON>, type Search } from "#/utils/search/search";
import { type ChartBrushFilter } from "./DistributionChartInsight";
import { DuckDBTypeHints } from "#/utils/schema";
import {
  type ComparisonExperimentSpanSummary,
  type PrimaryExperimentSpanSummary,
} from "./(queries)/useExperiment";
import { LayoutTypeControl } from "#/ui/table/layout-type-control";
import { useOrg } from "#/utils/user";
import { useTableQueries } from "./(queries)/table-queries";
import { useTableFormatters } from "./formatters";
import { Bubble } from "#/ui/table/bubble";
import { FilterOpLabel } from "./filter-op-label";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/color";
import { groupToString, type RegressionFilter } from "./regressions-query";
import { GroupBySelect } from "#/ui/table/grouping/controls";
import { TableRowHeightToggle } from "#/ui/table-row-height-toggle";
import { TagsField, TransactionIdField } from "#/utils/duckdb";
import Footer from "#/ui/landing/footer";
import { Views } from "#/ui/views";
import { ExperimentSelectionSection } from "./ExperimentSelectionSection";
import { type UpdateRowFn, type DML } from "#/utils/mutable-object";
import { type Schema, type Table, type TypeMap } from "apache-arrow";
import { type SummaryBreakdownData } from "./(charts)/(SummaryBreakdown)/use-summary-breakdown";
import {
  type CustomColumnDefinition,
  type CustomColumn,
  type useCustomColumns,
  BUILT_IN_CUSTOM_COLUMNS,
} from "#/utils/custom-columns/use-custom-columns";
import { type ViewParams, type ViewProps } from "#/utils/view/use-view";
import AppliedFilters from "#/ui/applied-filters";
import { type RowData } from "#/ui/arrow-table";
import columnReorderer from "./table-column-reorderer";
import { type RowId } from "#/utils/diffs/diff-objects";
import { singleQuote } from "@braintrust/local/query";
import { Button } from "#/ui/button";
import { type CustomColumnDialogVariant } from "#/ui/table/columns-menu";
import { type Expr, type ParsedQuery } from "@braintrust/btql/parser";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { useInferCustomColumnPaths } from "#/utils/custom-columns/use-infer-custom-column-paths";
import { cn } from "#/utils/classnames";
import { BT_GROUP_BY_METADATA } from "#/ui/table/grouping/queries";
import { BT_ASSIGNMENTS } from "#/utils/assign";
import { type SavedScorer } from "#/utils/scorers";
import { type MetricDefinition } from "@braintrust/local/api-schema";
import { ProjectContext } from "../../projectContext";

export type ExperimentTableProps<TsData, TsValue> = ReturnType<
  typeof useExperimentTableProps
> & {
  regressionFilters: RegressionFilter[];
  setRegressionFilters: Dispatch<SetStateAction<RegressionFilter[]>>;
  tableGroupingOptions: {
    label: string;
    value: string;
  }[];
  // todo
  dml?: DML;
  isLoading: boolean;
  rowIds: RowId[];
  setRowIds: Dispatch<SetStateAction<RowId[]>>;
  customColumnState?: ReturnType<typeof useCustomColumns>;
  extraBubbles?: Bubble[];
  isReadOnly: boolean;
  // todo
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  viewProps: ViewProps;
  setSearch: Dispatch<SetStateAction<Search>>;
  queryError?: string | null;
  updateRow: UpdateRowFn;
  setAggregationExperimentId?: Dispatch<SetStateAction<string>>;
  vizQueryProps?: Partial<VizQueryProps<TsData, TsValue>>;
  initiallyVisibleColumns?: Record<string, boolean>;
  selectedComparisonExperiments?: {
    id: string;
    name: string;
  }[];
  isPlayground?: boolean;
  isLoopEnabled?: boolean;
  playgroundId?: string;
  hasDataset?: boolean;
  disableFilters?: boolean;
  tableSelectionProps: TableSelection;
  neverVisibleColumns?: Set<string>;
  exportName: string;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
  notStickySlot?: React.ReactNode;
  afterToolbarSlot?: React.ReactNode;
  savedScorers?: SavedScorer[];
  metricDefinitions?: MetricDefinition[];
};

export const INITIALLY_VISIBLE_COLUMNS = { id: false };
export const NEVER_VISIBLE_COLUMNS = new Set([
  TransactionIdField,
  "cached",
  "comparison_key",
  "__bt_internal",
  "origin",
  "dataset_id",
  "span_id",
]);

export function ExperimentTableComponent<TsData extends RowData, TsValue>({
  experiment,
  search,
  clauseChecker,
  comparisonExperimentData,
  selectedComparisonExperiments,
  diffMode,
  rowComparisonProps,
  groupQueryData,
  tableQuery,
  multilineRow,
  regressionFilters,
  setRegressionFilters,
  tableGroupingOptions,
  displayPaths,
  dml,
  isLoading,
  summaryBreakdownData,
  customColumnState,
  tableGrouping,
  setTableGrouping,
  addRegressionFilter,
  clearRegressionFilters,
  clearChartBrushFilters,
  extraBubbles: extraBubblesProps,
  isReadOnly,
  pageIdentifier,
  viewParams,
  viewProps,
  setSearch,
  queryError,
  updateRow,
  setAggregationExperimentId,
  enableStarColumn,
  vizQueryProps,
  initiallyVisibleColumns = INITIALLY_VISIBLE_COLUMNS,
  rowIds,
  setRowIds,
  isPlayground,
  isLoopEnabled,
  playgroundId,
  hasDataset,
  tableSelectionProps,
  disableFilters,
  neverVisibleColumns = NEVER_VISIBLE_COLUMNS,
  exportName,
  getRowsForExport,
  refetchDataQueryFn,
  notStickySlot,
  afterToolbarSlot: afterToolbarSlotProp,
  isTableHidden,
  savedScorers,
  metricDefinitions,
}: ExperimentTableProps<TsData, TsValue>) {
  const {
    extraLeftControls: extraLeftControlsProp,
    extraRightControls: extraRightControlsProp,
    disableLimit,
    ...vizQueryPassthroughProps
  } = vizQueryProps ?? {};
  const {
    selectedRows,
    setSelectedRows,
    selectedRowsNumber,
    expandedState,
    setExpandedState,
    deselectAllTableRows,
    getSelectedRowsWithData,
    refetchSelectedRowsWithData,
    selectAllTableRows,
    tableRef,
    getRow,
  } = tableSelectionProps;
  const org = useOrg();

  const comparisonExperiments =
    selectedComparisonExperiments ?? comparisonExperimentData;
  const rowGroupingData = useMemo(() => {
    if (!groupQueryData?.groupRows) {
      return null;
    }
    return {
      groupRows: groupQueryData.groupRows,
      groupBy: groupQueryData.tableGrouping,
      //initialCollapseGroups: !isGridLayout && groupQueryData.tableGrouping === GROUP_BY_INPUT_VALUE,
    };
  }, [groupQueryData]);

  const extraBubbles = useMemo(() => {
    const bubbles: Bubble[] = [];
    if (regressionFilters.length > 0) {
      const experimentNames = Object.fromEntries(
        comparisonExperiments?.map((e, i) => [
          e.id,
          { name: e.name, index: i },
        ]) ?? [],
      );
      regressionFilters.forEach((f) => {
        if (
          f.experimentId !== "any" &&
          f.experimentId !== "all" &&
          !experimentNames[f.experimentId] &&
          experiment?.id !== f.experimentId
        ) {
          return;
        }
        bubbles.push(
          new Bubble({
            type: "filter",
            label: (
              <FilterOpLabel
                comparisonType={f.comparisonType}
                field={f.anyField ? "any score" : f.field.value}
                group={groupToString(f.group)}
                experimentName={
                  f.experimentId === "any" || f.experimentId === "all"
                    ? f.experimentId
                    : (experimentNames[f.experimentId]?.name ?? "")
                }
                experimentClassName={
                  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[
                    experimentNames[f.experimentId]?.index
                  ]
                }
              />
            ),
            isReadonly: true,
            clear: () => {
              setRegressionFilters((prev) =>
                (prev ?? []).filter((prevF) => prevF !== f),
              );
            },
          }),
        );
      });
    }
    return bubbles.concat(extraBubblesProps ?? []);
  }, [
    experiment,
    comparisonExperiments,
    regressionFilters,
    extraBubblesProps,
    setRegressionFilters,
  ]);

  const { isOrgDatasetsLoading } = useContext(ProjectContext);
  const isPlaygroundWithoutDataset =
    isPlayground && !hasDataset && !isOrgDatasetsLoading;
  const defaultLayout = isPlayground ? "grid" : "list";
  const tableLayout = viewProps.layout ?? defaultLayout;
  const isGridLayout = tableLayout === "grid";

  const extraRightControls = useMemo(
    () => (
      <>
        {selectedRowsNumber < 1 && (
          <>
            {!isReadOnly && (
              <LayoutTypeControl
                viewProps={viewProps}
                defaultLayout={defaultLayout}
              />
            )}
            {tableLayout !== "summary" && (
              <GroupBySelect
                allowGroupByInput={!!experiment?.hasTrials}
                options={tableGroupingOptions}
                tableGrouping={tableGrouping}
                onTableGrouping={setTableGrouping}
                groupRowData={
                  comparisonExperiments?.length
                    ? {
                        showGroupRows: viewProps.tallGroupRows,
                        onShowGroupRows: viewProps.setTallGroupRows,
                      }
                    : undefined
                }
                expandData={
                  tableRef && tableRef.current != null
                    ? {
                        expandAllGroups: () => {
                          tableRef.current?.resetExpanded();
                        },
                        collapseAllGroups: () => {
                          tableRef.current?.resetExpanded(true);
                        },
                        areAllRowsExpanded:
                          expandedState === true ||
                          !tableRef.current
                            ?.getPreExpandedRowModel()
                            .rows.some((r) => !expandedState[r.id]),
                        areAllRowsCollapsed:
                          expandedState !== true &&
                          !tableRef.current
                            ?.getPreExpandedRowModel()
                            .rows.some((r) => expandedState[r.id]),
                      }
                    : undefined
                }
                customColumnOptions={
                  customColumnState?.customColumnDefinitions?.map(
                    ({ name }) => ({
                      label: name,
                      value: name,
                    }),
                  ) ?? []
                }
              />
            )}
            {!(
              tableLayout === "list" &&
              ((diffMode?.enabled &&
                diffMode.enabledValue === "between_experiments") ||
                isPlayground)
            ) &&
              tableLayout !== "summary" &&
              !isPlaygroundWithoutDataset && (
                <TableRowHeightToggle
                  tableRowHeight={viewProps.rowHeight ?? "compact"}
                  onSetRowHeight={viewProps.setRowHeight}
                />
              )}
          </>
        )}
        {extraRightControlsProp}
      </>
    ),
    [
      comparisonExperiments?.length,
      customColumnState?.customColumnDefinitions,
      diffMode?.enabled,
      diffMode?.enabledValue,
      expandedState,
      experiment?.hasTrials,
      extraRightControlsProp,
      setTableGrouping,
      tableGrouping,
      tableGroupingOptions,
      tableRef,
      viewProps,
      defaultLayout,
      isPlaygroundWithoutDataset,
      isReadOnly,
      isPlayground,
      tableLayout,
      selectedRowsNumber,
    ],
  );

  const displayPathsProp = useMemo(() => {
    if (!displayPaths) {
      return undefined;
    }
    return {
      ...displayPaths,
      children: {
        ...displayPaths.children,
        // This is a hack to inform displayPaths of output_vs_expected.
        output_vs_expected: displayPaths.children["expected"],
      },
    };
  }, [displayPaths]);

  const mainPaneScrollContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handler: EventListener = (ev: Event) => {
      if (tableLayout !== "list") return;
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const e = ev as CustomEvent<{ columnId: string }>;
      const columnId = e?.detail?.columnId;
      const container = mainPaneScrollContainer.current;
      if (!columnId || !container) return;

      const tryScroll = () => {
        const headerEl = container.querySelector<HTMLElement>(
          `[data-column-name="${columnId}"]`,
        );
        if (!headerEl) return false;
        headerEl.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
        // Flash the target column header background briefly
        try {
          headerEl.classList.remove("animate-column-flash");
          headerEl.classList.add("animate-column-flash");
          const remove = () =>
            headerEl.classList.remove("animate-column-flash");
          headerEl.addEventListener("animationend", remove, { once: true });
        } catch {}
        return true;
      };

      if (tryScroll()) return;

      // Ensure the column is visible in the current view state
      viewProps.setColumnVisibility?.((prev) => ({
        ...prev,
        [columnId]: true,
      }));

      // If hidden via chart brushes (which hide other score columns), clear them
      clearChartBrushFilters?.();

      // Try again on the next frame after re-render
      requestAnimationFrame(() => {
        tryScroll();
      });
    };

    window.addEventListener("bt:scroll-to-column", handler);
    return () => {
      window.removeEventListener("bt:scroll-to-column", handler);
    };
  }, [tableLayout, viewProps, clearChartBrushFilters]);

  const vizQueryRef = useRef<{
    data: Table<TypeMap> | null;
  }>(null);

  const suggestedCustomColumnFields = useInferCustomColumnPaths({
    objectType: isPlayground ? "playground_logs" : "experiment",
    objectId: isPlayground ? null : (experiment?.id ?? null),
    rowId: rowIds[0] ?? null,
  });

  const customColumns = useMemo(
    () =>
      customColumnState
        ? {
            options: suggestedCustomColumnFields
              ? {
                  input: suggestedCustomColumnFields?.input ?? [],
                  output: suggestedCustomColumnFields?.output ?? [],
                  expected: suggestedCustomColumnFields?.expected ?? [],
                  metadata: suggestedCustomColumnFields?.metadata ?? [],
                  scores: experiment?.scoreFields.map((f) => [f]) ?? [],
                }
              : {
                  input: experiment?.inputFields ?? [],
                  output: experiment?.outputFields ?? [],
                  expected: experiment?.expectedFields ?? [],
                  metadata: experiment?.metadataFields ?? [],
                  scores: experiment?.scoreFields.map((f) => [f]) ?? [],
                },
            isEnabled: customColumnState.customColumnsEnabled,
            columns: customColumnState.customColumnDefinitions,
            createColumn: customColumnState.createCustomColumn,
            deleteColumn: customColumnState.deleteCustomColumn,
            updateColumn: customColumnState.updateCustomColumn,
            loading: customColumnState.customColumnDefinitions === undefined,
          }
        : undefined,
    [
      customColumnState,
      experiment?.expectedFields,
      experiment?.inputFields,
      experiment?.metadataFields,
      experiment?.outputFields,
      suggestedCustomColumnFields,
      experiment?.scoreFields,
    ],
  );

  const extraLeftControls = useMemo(
    () => (
      <>
        {extraLeftControlsProp}
        {isReadOnly ? null : (
          <Views
            pageIdentifier={pageIdentifier}
            viewParams={viewParams}
            viewProps={viewProps}
          />
        )}
      </>
    ),
    [extraLeftControlsProp, isReadOnly, pageIdentifier, viewParams, viewProps],
  );
  const [customColumnDialogVariant, setCustomColumnDialogVariant] =
    useState<CustomColumnDialogVariant>();

  const errorMessage = useMemo(() => {
    const baseMessage =
      !!diffMode?.enabled && diffMode.enabledValue === "between_experiments"
        ? "There was an error querying experiments."
        : "An internal error occurred while trying to load this experiment or search.";
    return queryError ? (
      (customColumns?.columns?.length || 0) > 0 ? (
        <>
          {baseMessage}{" "}
          <Button
            transparent
            size="inline"
            className="text-xs text-bad-700 underline"
            onClick={() => setCustomColumnDialogVariant("edit")}
          >
            Edit your custom columns
          </Button>{" "}
          or contact <NAME_EMAIL> for support.
        </>
      ) : (
        `${baseMessage} Please contact <NAME_EMAIL> for help.`
      )
    ) : null;
  }, [diffMode, queryError, customColumns?.columns?.length]);

  const [fullscreenTaskIndex] = usePlaygroundFullscreenTaskIndex();
  const hideTable = fullscreenTaskIndex !== null || isTableHidden;

  const toolbar = useMemo(
    () => (
      <ExperimentSelectionSection
        selectionProps={{
          selectedRows,
          selectedRowsNumber,
          setSelectedRows,
          deselectAllTableRows,
          getSelectedRowsWithData,
          refetchSelectedRowsWithData,
          selectAllTableRows,
          expandedState,
          setExpandedState,
          tableRef,
          getRow,
        }}
        orgId={org.id}
        orgName={org.name}
        objectType="experiment"
        dml={dml}
        experimentId={experiment?.id ?? ""}
        isPlayground={isPlayground}
        getRowsForExport={getRowsForExport}
        exportName={exportName}
        deleteButtonLabel={isPlayground ? "Delete from dataset" : undefined}
        additionalDeleteConfirmationDescription={
          isPlayground
            ? "Deleting dataset rows from a playground will delete them from the original dataset. This action cannot be undone."
            : undefined
        }
        refetchDataQueryFn={refetchDataQueryFn}
        savedScorers={savedScorers}
      />
    ),
    [
      deselectAllTableRows,
      dml,
      expandedState,
      experiment?.id,
      exportName,
      getRow,
      getRowsForExport,
      getSelectedRowsWithData,
      isPlayground,
      org.id,
      org.name,
      refetchDataQueryFn,
      refetchSelectedRowsWithData,
      selectAllTableRows,
      selectedRows,
      selectedRowsNumber,
      setExpandedState,
      setSelectedRows,
      tableRef,
      savedScorers,
    ],
  );

  const afterToolbarSlot = useMemo(
    () => (
      <div className="sticky left-0 flex w-full flex-wrap gap-1 py-1 empty:hidden">
        <AppliedFilters
          className="mb-0 flex-1"
          search={search}
          extraBubbles={extraBubbles}
          clauseChecker={clauseChecker}
          setSearch={setSearch}
          baseExperiment={experiment}
          comparisonExperiments={comparisonExperiments}
          fromClause={`experiment(${singleQuote(experiment?.id ?? "EXPERIMENT_ID")})`}
          disabled={disableFilters}
          disableFullBtqlQueryHint={isPlayground}
          isPlayground={isPlayground}
          onClearAll={() => {
            clearRegressionFilters();
            clearChartBrushFilters();
          }}
        />
        {afterToolbarSlotProp}
      </div>
    ),
    [
      afterToolbarSlotProp,
      clauseChecker,
      comparisonExperiments,
      disableFilters,
      experiment,
      extraBubbles,
      isPlayground,
      search,
      setSearch,
      clearRegressionFilters,
      clearChartBrushFilters,
    ],
  );

  return (
    <>
      <div
        ref={mainPaneScrollContainer}
        className={cn("@container flex flex-auto flex-col overflow-auto px-3", {
          "overflow-hidden": hideTable,
        })}
      >
        {hideTable && notStickySlot}
        {!hideTable && (
          <>
            <VizQuery
              {...tableQuery}
              enableLoadingForDBQuery={!!isGridLayout && !isLoading}
              summaryBreakdownData={summaryBreakdownData}
              showRowNumber={!isPlaygroundWithoutDataset}
              enableStarColumn={enableStarColumn}
              updateRow={updateRow}
              notStickySlot={notStickySlot}
              customColumns={customColumns}
              className="border-0 pt-2"
              disableFilters={disableFilters}
              isReadOnly={isReadOnly}
              vizQueryRef={vizQueryRef}
              typeHints={DuckDBTypeHints.experiment}
              experimentName={experiment?.name ?? ""}
              experimentId={experiment?.id}
              addRegressionFilter={addRegressionFilter}
              comparisonExperiments={comparisonExperiments}
              scrollContainerRef={mainPaneScrollContainer}
              extraLeftControls={extraLeftControls}
              extraRightControls={extraRightControls}
              initiallyVisibleColumns={initiallyVisibleColumns}
              neverVisibleColumns={neverVisibleColumns}
              columnReorderer={columnReorderer}
              rowComparisonProps={rowComparisonProps}
              rowSelection={isPlaygroundWithoutDataset ? null : selectedRows}
              setRowSelection={
                isPlaygroundWithoutDataset ? null : setSelectedRows
              }
              expandedState={expandedState}
              setExpandedState={setExpandedState}
              tableRef={tableRef}
              viewProps={viewProps}
              rowIds={rowIds}
              setRowIds={setRowIds}
              rowGroupingData={rowGroupingData}
              setAggregationExperimentId={setAggregationExperimentId}
              disableLimit={disableLimit || !!rowGroupingData}
              error={errorMessage}
              displayPaths={displayPathsProp}
              isHumanReviewModeEnabled
              multilineRow={multilineRow}
              isLoading={isLoading}
              customColumnDialogVariant={customColumnDialogVariant}
              setCustomColumnDialogVariant={setCustomColumnDialogVariant}
              toolbarSlot={
                isReadOnly || selectedRowsNumber === 0 ? undefined : toolbar
              }
              afterToolbarSlot={afterToolbarSlot}
              objectId={isPlayground ? playgroundId : experiment?.id}
              isLoopEnabled={isLoopEnabled}
              removeLimiter={isPlaygroundWithoutDataset}
              {...vizQueryPassthroughProps}
              objectType={isPlayground ? "playground_logs" : "experiment"}
              metricDefinitions={metricDefinitions}
            />
            <div className="grow" />
            <Footer
              className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
              inApp
              orgName={org.name}
            />
          </>
        )}
      </div>
    </>
  );
}

export const ExperimentTable = memo(ExperimentTableComponent);
ExperimentTable.displayName = "ExperimentTable";

export function useExperimentTableProps(props: {
  experiment: PrimaryExperimentSpanSummary | undefined;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  search: Search;
  projectedPaths: string[];
  diffMode: DiffModeState | null;
  tableGrouping: string;
  setTableGrouping: (value: string) => void;
  comparisonKey: (relation: string) => string;
  comparisonKeyFilterClause: (relation: string) => string;
  addRegressionFilter: (f: RegressionFilter) => void;
  clearRegressionFilters: () => void;
  summaryBreakdownData: SummaryBreakdownData;
  clauseChecker: ClauseChecker | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  experimentScanRaw: string;
  primaryExperimentReady: number[];
  enableStarColumn?: boolean;
  defaultSortExprs?: string[];
  viewProps: ViewProps;
  isPlayground?: boolean;
  isLoopEnabled?: boolean;
  refetchDataParams?: {
    objectType: DataObjectType;
    objectId: string;
  };
  isTableHidden?: boolean;
  customColumns?: CustomColumn[] | CustomColumnDefinition[];
  metricDefinitions: MetricDefinition[] | undefined;
}) {
  const {
    experiment,
    comparisonExperimentData,
    search,
    projectedPaths,
    diffMode,
    tableGrouping,
    comparisonKey,
    comparisonKeyFilterClause,
    addRegressionFilter,
    summaryBreakdownData,
    clauseChecker,
    setSearch,
    experimentScanRaw,
    primaryExperimentReady,
    enableStarColumn,
    defaultSortExprs,
    viewProps,
    isPlayground,
    refetchDataParams,
    customColumns,
    metricDefinitions,
  } = props;

  const [chartBrushFilters, setChartBrushFilters] = useState<
    ChartBrushFilter[]
  >([]);

  const builder = useBtqlQueryBuilder({});

  const setChartBrushFilter = useCallback((filter: ChartBrushFilter) => {
    setChartBrushFilters((prev) => {
      const filtered = prev.filter((f) => f.scoreName !== filter.scoreName);
      if (!filter.scoreBounds) return filtered;
      return [...filtered, filter];
    });
  }, []);

  const removeChartBrushFilter = useCallback((scoreName: string) => {
    setChartBrushFilters((prev) =>
      prev.filter((f) => f.scoreName !== scoreName),
    );
  }, []);

  const clearChartBrushFilters = useCallback(() => {
    setChartBrushFilters([]);
  }, []);

  const refetchDataQueryFn: (
    rowIds?: string[],
    withFilter?: boolean,
  ) => ParsedQuery | undefined = useCallback(
    (rowIds?: string[], withFilter?: boolean) => {
      return refetchDataParams
        ? {
            filter: builder.and(
              builder.or(
                ...(rowIds ?? []).map(
                  (id): Expr => ({
                    op: "eq" as const,
                    left: { btql: "id" },
                    right: { op: "literal", value: id },
                  }),
                ),
              ),
              withFilter
                ? builder.and(
                    ...(search.filter ?? []).map((f) => f.btql?.parsed),
                  )
                : undefined,
            ),
            from: builder.from(
              refetchDataParams.objectType,
              [refetchDataParams.objectId],
              "summary",
            ),
            custom_columns: BUILT_IN_CUSTOM_COLUMNS["experiment"]?.map(
              ({ name, expr }) => ({
                alias: name,
                expr: { btql: expr },
              }),
            ),
            select: [{ op: "star" }],
            sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
            preview_length: -1,
          }
        : undefined;
    },
    [refetchDataParams, builder, search.filter],
  );

  const defaultLayout = isPlayground ? "grid" : "list";

  const {
    displayPaths,
    baseQuery,
    diffQuery,
    baseComparisonQuery,
    groupQueryData,
    getRowsForExport,
    getRawData,
    isLoading,
  } = useTableQueries({
    experimentScanRaw,
    experiment,
    comparisonExperimentData,
    search,
    projectedPaths,
    diffMode,
    chartBrushFilters,
    tableGrouping,
    comparisonKey,
    comparisonKeyFilterClause,
    defaultSortExprs,
    isPlayground,
    layout: viewProps.layout ?? defaultLayout,
  });

  const {
    tableQuery,
    rowComparisonProps,
    multilineRow,
    setAggregationExperimentId,
  } = useTableFormatters({
    experiment,
    diffMode,
    comparisonExperimentData,
    addRegressionFilter,
    summaryBreakdownData,
    clauseChecker,
    setSearch,
    tableGrouping,
    primaryExperimentReady,
    baseQuery,
    diffQuery,
    enableStarColumn,
    rowHeight: viewProps.rowHeight ?? "compact",
    layout: viewProps.layout ?? defaultLayout,
    isPlayground,
    customColumns,
    tallGroupRows: viewProps.tallGroupRows,
    metricDefinitions: metricDefinitions ?? [],
  });

  return useMemo(
    () => ({
      ...props,
      tableQuery,
      baseQuery,
      rowComparisonProps,
      groupQueryData,
      displayPaths,
      multilineRow,
      rowHeight: viewProps.rowHeight,
      getRawData,
      getRowsForExport,
      baseComparisonQuery,
      chartBrushFilters,
      setChartBrushFilter,
      removeChartBrushFilter,
      clearChartBrushFilters,
      setAggregationExperimentId,
      isLoading,
      refetchDataQueryFn,
      metricDefinitions,
    }),
    [
      props,
      tableQuery,
      baseQuery,
      rowComparisonProps,
      groupQueryData,
      displayPaths,
      multilineRow,
      viewProps.rowHeight,
      getRawData,
      getRowsForExport,
      baseComparisonQuery,
      chartBrushFilters,
      setChartBrushFilter,
      removeChartBrushFilter,
      clearChartBrushFilters,
      setAggregationExperimentId,
      isLoading,
      refetchDataQueryFn,
      metricDefinitions,
    ],
  );
}

// When we introduce new fields, like `error`, we need to make sure that the backend returns them,
// otherwise we shouldn't project them. The other fields are either always returned or generated
// as a metric.
// DEPRECATION_NOTICE:
// error was added in 0.0.51
const NEW_DATA_FIELDS = ["error"];

export function projectedPathsFn({
  schema,
  hasComments,
  hasOrigin,
  hasAssignments,
  summarySchema,
}: {
  schema: Schema | null;
  hasComments?: boolean;
  hasOrigin?: boolean;
  hasAssignments?: boolean;
  summarySchema?: Schema | null;
}) {
  const baseFields = new Set([
    // used for grouping
    "comparison_key",
    TransactionIdField,
    "span_type_info",
    "input",
    "output",
    "error",
    "expected",
    TagsField,
    "scores",
    "metrics",
    "metadata",
    "comments",
    "id",
    "created",
    "span_id",
    ...(hasAssignments ? [BT_ASSIGNMENTS] : []),
    // used for playx
    ...(hasOrigin ? ["origin"] : []),
    // used for fast summary
    ...(summarySchema !== undefined ? [BT_GROUP_BY_METADATA] : []),
  ]);
  const customColumns = new Set<string>();
  if (summarySchema) {
    const summaryFieldNames = new Set(summarySchema.fields.map((f) => f.name));
    summaryFieldNames.forEach((fieldName) => {
      if (!baseFields.has(fieldName)) {
        customColumns.add(fieldName);
      }
    });
  }
  return {
    paths: Array.from(baseFields).filter((p) => {
      // There's a subtle race condition where the audit log scan may not be loaded yet, so the
      // `comments` field will be missing from the generated experiment scan. This happens when
      // experimentAuditLogScan is null, so we can use that field to know if it'll be available.
      if (p === "comments") {
        return hasComments;
      }
      return (
        !NEW_DATA_FIELDS.includes(p) || schema?.fields.find((f) => f.name === p)
      );
    }),
    customColumns:
      summarySchema !== undefined ? Array.from(customColumns) : undefined,
  };
}
