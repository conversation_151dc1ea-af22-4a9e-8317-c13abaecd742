import { usePrompt } from "#/ui/prompts/use-prompt";
import { useDurableObjectByIds } from "#/utils/mutable-object";
import { useMemo } from "react";
import { z } from "zod";

const metadataPromptSchema = z.object({
  metadata: z
    .object({
      prompt: z
        .object({
          id: z.string().nullish(),
          project_id: z.string().nullish(),
          prompt_session_id: z.string().nullish(),
          version: z.string().nullish(),
        })
        .nullish(),
    })
    .nullish(),
});

export function useExperimentPrompt(experimentId?: string) {
  const params: Parameters<typeof useDurableObjectByIds>[0] = useMemo(
    () => ({
      ids: undefined,
      filters: [
        {
          // The `metadata.tool_choice.function.name != 'select_choice'` filter is not needed for new experiments,
          // but because we were not including `span_attributes.purpose` in the LLM spans before https://github.com/braintrustdata/braintrust/pull/5065
          // we need to include this for existing experiments.
          btql: `span_attributes.type = 'llm'
          AND metadata.prompt.id IS NOT NULL
          AND COALESCE(span_attributes.purpose, '') != 'scorer'
          AND COALESCE(metadata.tool_choice.function.name, '') != 'select_choice'`,
        },
      ],
      version: undefined,
      objectType: "experiment",
      objectId: experimentId,
    }),
    [experimentId],
  );

  const experimentPrompts = useDurableObjectByIds(params);

  const promptSpan = useMemo(() => {
    if (!experimentPrompts.objects || experimentPrompts.objects.length < 1) {
      return;
    }
    const prompt = metadataPromptSchema.safeParse(experimentPrompts.objects[0]);
    if (prompt.success) {
      return prompt.data.metadata?.prompt;
    }
  }, [experimentPrompts.objects]);

  const { prompt, status, createPrompt } = usePrompt({
    promptInfoFromSpan: promptSpan,
  });

  return {
    prompt,
    status:
      experimentPrompts.status === "loading" || status === "loading"
        ? "loading"
        : status,
    createPrompt,
  };
}
