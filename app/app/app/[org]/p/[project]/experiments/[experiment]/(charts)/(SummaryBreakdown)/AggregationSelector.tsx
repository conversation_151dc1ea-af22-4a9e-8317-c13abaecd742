import type { AggregationType } from "#/utils/queries/aggregations";
import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { cn } from "#/utils/classnames";

export function AggregationSelector({
  aggregationType,
  setAggregationType,
  className,
  allowedAggregationTypes = ["avg", "min", "max", "sum"],
}: {
  aggregationType: AggregationType;
  setAggregationType: (v: AggregationType) => void;
  className?: string;
  allowedAggregationTypes?: AggregationType[];
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className={cn(
            "mr-1 -ml-1 inline-flex items-center gap-0.5 px-1 py-0.5 text-xs text-primary-500 capitalize",
            className,
          )}
          variant="ghost"
          size="inline"
          onClick={(e) => e.stopPropagation()}
          isDropdown
        >
          {aggregationType}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {allowedAggregationTypes.map((type) => (
          <DropdownMenuItem
            key={type}
            onClick={(e) => {
              e.stopPropagation();
              setAggregationType(type);
            }}
          >
            {type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
