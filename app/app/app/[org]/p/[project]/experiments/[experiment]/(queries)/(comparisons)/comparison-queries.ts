import {
  computeSummaryScoreBatches,
  type DiscriminatedProjectScore,
  doubleQuote,
  EXCLUDE_METRICS,
  jsonPath,
  MetadataField,
  singleQuote,
  SUMMARY_DEFAULT_GROUP_KEY,
  type SummaryDataParams,
} from "@braintrust/local/query";
import {
  type RegressionFilter,
  useComparisonKeyFilterClause,
} from "../../regressions-query";
import {
  GROUP_BY_NONE_VALUE,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import { useMemo } from "react";
import { useSummaryBreakdown } from "../../(charts)/(SummaryBreakdown)/use-summary-breakdown";
import {
  type ComparisonExperimentSpanSummary,
  type PrimaryExperimentSpanSummary,
} from "../useExperiment";
import { useSummaryQueries } from "./summary-queries";
import { type CustomColumn } from "#/utils/custom-columns/use-custom-columns";
import { G<PERSON><PERSON>_BY_INPUT_VALUE } from "#/ui/table/grouping/controls";
import { type Search } from "#/utils/search/search";
import { BT_GROUP_BY_METADATA } from "#/ui/table/grouping/queries";

export function useComparisonQueries({
  experiment,
  comparisonExperimentData,
  regressionFilters,
  search,
  columnVisibility,
  tableGrouping,
  comparisonKey,
  scoreConfig,
  hasErrorField,
  customColumns,
  fastExperimentSummary,
}: {
  experiment: PrimaryExperimentSpanSummary | undefined;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  regressionFilters: RegressionFilter[];
  search: Search;
  columnVisibility: Record<string, boolean>;
  tableGrouping: string;
  comparisonKey: (relation: string) => string;
  scoreConfig?: DiscriminatedProjectScore[];
  hasErrorField?: boolean;
  customColumns?: CustomColumn[];
  fastExperimentSummary?: boolean;
}) {
  const isGroupByInput = tableGrouping === GROUP_BY_INPUT_VALUE;
  const isGroupByNone = tableGrouping === GROUP_BY_NONE_VALUE;

  const summaryDataParams: SummaryDataParams = useMemo(
    () => ({
      primaryExperimentScan: experiment?.tableScan ?? null,
      // don't need to run comparisons in multi mode, since
      // all the comparison querying will be queried separately
      comparisonExperimentScan: null,
      groupKey: (relation: string) => {
        if (isGroupByNone) {
          return singleQuote(SUMMARY_DEFAULT_GROUP_KEY);
        }
        if (isGroupByInput) {
          return `${relation}.comparison_key`;
        }

        try {
          const metadataPath = JSON.parse(tableGrouping);
          return fastExperimentSummary
            ? `${relation}.${doubleQuote(BT_GROUP_BY_METADATA)}::VARCHAR`
            : `JSON_EXTRACT_STRING(${relation}.${doubleQuote(MetadataField)},
              ${jsonPath(metadataPath)}
            )`;
        } catch {
          return `${relation}.${doubleQuote(tableGrouping)}::VARCHAR`;
        }
      },
      comparisonKey,
      scoreFields: experiment?.scoreFields ?? [],
      comparisonScoreFields: [],
      metricFields: experiment?.metricFields ?? [],
      comparisonMetricFields: [],
      scoreConfig: scoreConfig ?? [],
      hasErrorField,
      metricDefinitions: experiment?.metricDefinitions ?? [],
    }),
    [
      experiment?.tableScan,
      experiment?.scoreFields,
      experiment?.metricFields,
      experiment?.metricDefinitions,
      comparisonKey,
      scoreConfig,
      hasErrorField,
      isGroupByNone,
      isGroupByInput,
      tableGrouping,
      fastExperimentSummary,
    ],
  );

  const comparisonMeasures = useMemo(() => {
    const { summaryMetricNames } = computeSummaryScoreBatches({
      scoreFields: experiment?.scoreFields ?? [],
      metricFields: experiment?.metricFields ?? [],
      metricDefinitions: experiment?.metricDefinitions ?? [],
      comparisonMetricFields: [],
      comparisonScoreFields: [],
      scoreConfig: summaryDataParams.scoreConfig,
    });

    const existingFilterMeasures = regressionFilters.reduce(
      (acc: Record<string, Record<string, boolean>>, f) => {
        acc[f.field.type] ??= {};
        acc[f.field.type][f.field.value] ??= true;
        return acc;
      },
      {},
    );

    const ignoredMeasures: SelectionType[] = [];
    function filterFn(m: SelectionType) {
      const visibleValue = columnVisibility[`${m.type}s.${m.value}`];
      const isVisible =
        // null value means show
        visibleValue == null ||
        visibleValue ||
        existingFilterMeasures[m.type]?.[m.value];

      if (!isVisible) {
        ignoredMeasures.push(m);
      }
      if (EXCLUDE_METRICS.includes(m.value)) {
        return false;
      }
      return isVisible;
    }

    const scores = (experiment?.scoreFields ?? [])
      .map((s) => ({
        type: "score" as const,
        value: s,
      }))
      .filter(filterFn);
    const metrics = summaryMetricNames
      .map((m) => ({
        type: "metric" as const,
        value: m,
      }))
      .filter(filterFn);

    return {
      measures: {
        scores,
        metrics,
      },
      ignoredMeasures,
    };
  }, [
    columnVisibility,
    experiment?.scoreFields,
    experiment?.metricFields,
    experiment?.metricDefinitions,
    summaryDataParams.scoreConfig,
    regressionFilters,
  ]);

  const comparisonKeyFilterClause = useComparisonKeyFilterClause({
    experimentId: experiment?.id ?? "",
    primaryExperimentScan: experiment?.tableScan ?? null,
    comparisonExperimentData,
    regressionFilters,
    search,
    selectedSummaryMeasures: comparisonMeasures.measures,
    comparisonKey: summaryDataParams.comparisonKey,
    customColumns,
    fastExperimentSummary,
  });

  const { summaryData, comparisonSummaryData } = useSummaryQueries({
    experiment,
    comparisonExperimentData,
    summaryDataParams: {
      ...summaryDataParams,
      comparisonKeyFilterClause,
    },
    tableGrouping,
  });

  const summaryExperiments = useMemo(
    () => [
      {
        id: experiment?.id ?? "",
        name: experiment?.name ?? "",
        type: "base" as const,
        index: 0,
      },
      ...comparisonExperimentData.map(({ id, name, index }) => ({
        id,
        name,
        index,
        type: "comparison" as const,
      })),
    ],
    [experiment, comparisonExperimentData],
  );

  const summaryBreakdownData = useSummaryBreakdown({
    experiments: summaryExperiments,
    summaryData,
    comparisonSummaryData,
    ignoredMeasures: comparisonMeasures.ignoredMeasures,
  });

  return { summaryBreakdownData, comparisonKeyFilterClause };
}
