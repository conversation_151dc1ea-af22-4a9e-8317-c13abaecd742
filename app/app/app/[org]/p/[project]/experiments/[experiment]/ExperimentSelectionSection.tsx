import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { type SavingState } from "#/ui/saving";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { type TableSelection } from "#/ui/table/useTableSelection";
import type { DataObjectType } from "#/utils/btapi/btapi";
import {
  DatasetIdField,
  OriginField,
  ProjectIdField,
  TagsField,
} from "#/utils/duckdb";
import {
  type OtherObjectInserterInfo,
  useOtherObjectInserter,
} from "#/utils/other-object-inserter";
import { pluralizeWithCount } from "#/utils/plurals";
import { parseObjectJSON } from "#/utils/schema";
import { useOrg } from "#/utils/user";
import { useContext, useState } from "react";
import { getDatasetLink } from "../../datasets/[dataset]/getDatasetLink";
import { TagBulkEditor } from "#/ui/trace/tags";
import { type DML } from "#/utils/mutable-object";
import { toast } from "sonner";
import { buttonVariants } from "#/ui/button";
import Link from "next/link";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { FileJson, FileSpreadsheet, Percent, Trash } from "lucide-react";
import { objectLookupSupportedType, type Span } from "@braintrust/local";
import { type GetRowsForExportFn } from "./(queries)/table-queries";
import { downloadAsCSV, downloadAsJSON } from "#/utils/download";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { BasicTooltip } from "#/ui/tooltip";
import OptimizeDatasetButton from "../optimize-dataset-button";
import { useIsLoopEnabled } from "#/ui/optimization/optimization-chat";
import { TuneScorerDialog } from "#/app/app/[org]/p/[project]/tune-scorer-dialog";
import { AssignBulkEditor } from "#/ui/trace/assign";
import { type SavedScorer } from "#/utils/scorers";
import { type UIFunction } from "#/ui/prompts/schema";
import { DatasetActionButton } from "#/ui/trace/dataset-action-button";
import { createScorerFormatOption } from "#/ui/trace/span-formatters";
import LoopLogsButton from "../loop-logs-button";
import { BulkTraceToPlayground } from "#/ui/trace/logs-to-playground";
import { Button } from "#/ui/button";
import { AsyncScoringDialog } from "#/app/app/[org]/p/[project]/logs/async-scoring-dialog";

export const ExperimentSelectionSection = (props: {
  selectionProps: TableSelection;
  orgName: string;
  orgId?: string;
  objectType: DataObjectType;
  dml?: DML;
  experimentId?: string;
  isPlayground?: boolean;
  isLogsPage?: boolean;
  getRowsForExport?: GetRowsForExportFn;
  exportName: string;
  additionalDeleteConfirmationDescription?: string;
  deleteButtonLabel?: string;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
  savedScorers?: SavedScorer[];
  scorerFunctions?: Record<string, UIFunction>;
}) => {
  const {
    selectedRowsNumber,
    deselectAllTableRows,
    getSelectedRowsWithData,
    refetchSelectedRowsWithData,
  } = props.selectionProps;
  const {
    orgName,
    orgId,
    objectType,
    dml,
    experimentId,
    getRowsForExport,
    exportName,
    additionalDeleteConfirmationDescription,
    deleteButtonLabel,
    refetchDataQueryFn,
    isPlayground,
  } = props;

  const org = useOrg();
  const maxSelectedLogsToScore = 300;

  //The usecases like generating dataset rows are only supported in playground.
  const isLoopEnabled = useIsLoopEnabled();

  const [_savingState, setSavingState] = useState<SavingState>("none");
  const addRowsToDataset = useOtherObjectInserter({
    objectType: "dataset",
    setSavingState,
  });
  const { projectId, projectName, orgDatasets, mutateDatasets } =
    useContext(ProjectContext);

  const { modal: createDatasetModal, open: openCreateDatasetDialog } =
    useCreateDatasetDialog({
      onSuccessfulCreate: async ({
        datasetId,
        datasetName,
        projectName,
        getRows,
      }) => {
        mutateDatasets();
        const spans = getRows ? await getRows() : await getSelectedSpans();
        performAddRowsToDataset({
          datasetName: datasetName,
          datasetId: datasetId,
          spans,
          selectedProjectId: projectId!,
          selectedProjectName: projectName,
        });
      },
      orgId,
      projectName,
    });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const [rowsToDelete, setRowsToDelete] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const [rowsToTuneScorer, setRowsToTuneScorer] = useState<any[]>([]);
  const [isScoringDialogOpen, setIsScoringDialogOpen] = useState(false);

  if (!projectId) {
    return null;
  }

  const performAddRowsToDataset = async (params: {
    datasetName: string;
    datasetId: string;
    spans: Span[];
    selectedProjectId: string;
    selectedProjectName: string;
  }) => {
    const rows = prepareRowsForCopy({
      orgName,
      projectName: params.selectedProjectName,
      projectId,
      objectType,
      objectId: experimentId,
      selectedRowsWithData: params.spans.map((span) => ({
        id: span.id,
        input: span.data.input,
        output: span.data.output,
        expected: span.data.expected,
        metadata: span.data.metadata,
        /* eslint-disable @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any */
        tags: (span as any).tags,
        _xact_id: (span as any)._xact_id,
        dataset_id: (span as any).dataset_id, // Preserve for playground origin logic
        /* eslint-enable @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any */
      })),
      targetDataset: {
        id: params.datasetId,
        name: params.datasetName,
      },
      expectedFieldSource: "auto",
      cleanup: () => deselectAllTableRows(),
    });
    addRowsToDataset(rows);
  };

  const getSelectedSpans = async (): Promise<Span[]> => {
    const selectedRowsWithData = refetchSelectedRowsWithData
      ? await refetchSelectedRowsWithData()
      : getSelectedRowsWithData();

    return selectedRowsWithData.map((row) => {
      const rowJson = parseObjectJSON(objectType, row);
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      return {
        id: rowJson.id,
        data: {
          input: rowJson.input,
          output: rowJson.output,
          expected: rowJson.expected,
          metadata: rowJson.metadata,
        },
        span_id: rowJson.span_id ?? null,
        root_span_id: rowJson.root_span_id ?? null,
        span_type_info: rowJson.span_type_info ?? null,
        tags: rowJson.tags,
        _xact_id: rowJson._xact_id,
        dataset_id: rowJson.dataset_id, // Preserve for playground origin logic
      } as unknown as Span;
    });
  };

  const formatOptions = [createScorerFormatOption(true)];

  return (
    <>
      <CancelSelectionButton
        onCancelSelection={deselectAllTableRows}
        selectedRowsNumber={selectedRowsNumber}
      />
      {!org.resources?.forbid_insert_datasets && (
        <DatasetActionButton
          orgDatasets={orgDatasets}
          getSpans={getSelectedSpans}
          performAddRowsToDataset={performAddRowsToDataset}
          openCreateDatasetDialog={openCreateDatasetDialog}
          buttonText="Add to dataset"
          formatOptions={formatOptions}
          enableHotkeys={false}
        />
      )}
      {getRowsForExport && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SelectionBarButton isDropdown>Download</SelectionBarButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              onSelect={() => {
                toast.promise(
                  async () => {
                    try {
                      const rowIds = getSelectedRowsWithData().map((r) => r.id);
                      const refetchDataQuery = refetchDataQueryFn?.(rowIds);
                      const data = await getRowsForExport({
                        withComparisons: props.isPlayground,
                        rowIds,
                        refetchDataQuery,
                      });
                      if (!data) {
                        throw new Error("Unable to download csv");
                      }
                      downloadAsCSV(exportName, data);
                    } catch (error) {
                      console.error(error);
                      throw error;
                    }
                  },
                  {
                    loading: `Downloading ${exportName}`,
                    success: "Download complete",
                    error: `Failed to download ${objectType}`,
                  },
                );
              }}
            >
              <FileSpreadsheet className="size-3 flex-none" />
              Download as CSV
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={() => {
                toast.promise(
                  async () => {
                    try {
                      const rowIds = getSelectedRowsWithData().map((r) => r.id);
                      const refetchDataQuery = refetchDataQueryFn?.(rowIds);
                      const data = await getRowsForExport({
                        withComparisons: props.isPlayground,
                        rowIds,
                        refetchDataQuery,
                      });
                      if (!data) {
                        throw new Error("Unable to download json");
                      }
                      downloadAsJSON(exportName + ".json", data);
                    } catch (error) {
                      console.error(error);
                      throw error;
                    }
                  },
                  {
                    loading: `Downloading ${exportName}`,
                    success: "Download complete",
                    error: `Failed to download ${objectType}`,
                  },
                );
              }}
            >
              <FileJson className="size-3 flex-none" />
              Download as JSON
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {dml && (
        <TagBulkEditor
          dml={dml}
          buttonClassName="text-primary-700"
          selectionProps={props.selectionProps}
          isPlayground={isPlayground}
        />
      )}
      {!isPlayground && dml && (
        <AssignBulkEditor
          dml={dml}
          selectionProps={props.selectionProps}
          isPlayground={isPlayground}
          buttonClassName="text-primary-700"
        />
      )}
      {props.isLogsPage && (
        <BasicTooltip
          tooltipContent={
            selectedRowsNumber > maxSelectedLogsToScore
              ? `Score up to ${maxSelectedLogsToScore} logs at a time`
              : undefined
          }
        >
          <span>
            <Button
              size="xs"
              variant="border"
              Icon={Percent}
              onClick={() => setIsScoringDialogOpen(true)}
              disabled={
                selectedRowsNumber === 0 ||
                selectedRowsNumber > maxSelectedLogsToScore
              }
            >
              Score
            </Button>
          </span>
        </BasicTooltip>
      )}
      {isLoopEnabled && props.isPlayground && (
        <OptimizeDatasetButton
          selectionProps={props.selectionProps}
          onTuneScorer={() => {
            setRowsToTuneScorer(getSelectedRowsWithData());
          }}
        />
      )}
      {isLoopEnabled && props.isLogsPage && (
        <LoopLogsButton selectionProps={props.selectionProps} />
      )}
      {props.isLogsPage && (
        <BulkTraceToPlayground
          getRows={getSelectedSpans}
          projectId={projectId}
          projectName={projectName}
        />
      )}
      {dml && (
        <BasicTooltip
          tooltipContent={`Delete selected rows from current ${objectType.replace("_", " ")}`}
        >
          <SelectionBarButton
            onClick={() => {
              const rows = getSelectedRowsWithData(
                objectType === "experiment"
                  ? {
                      experiment_id: experimentId,
                      project_id: projectId,
                    }
                  : undefined,
              );
              setRowsToDelete(rows);
            }}
            Icon={Trash}
          />
        </BasicTooltip>
      )}
      <TuneScorerDialog
        open={rowsToTuneScorer.length > 0}
        onOpenChange={(open) => {
          if (!open) setRowsToTuneScorer([]);
        }}
        projectName={projectName}
        projectId={projectId}
        rowsToTuneScorer={rowsToTuneScorer}
        savedScorers={props.savedScorers}
        scorerFunctions={props.scorerFunctions}
      />

      {createDatasetModal}
      {props.isLogsPage && (
        <AsyncScoringDialog
          mode="selected"
          open={isScoringDialogOpen}
          onOpenChange={setIsScoringDialogOpen}
          onClose={() => {
            setIsScoringDialogOpen(false);
            deselectAllTableRows();
          }}
          selectedLogIds={getSelectedRowsWithData().map(
            (row: { id: string }) => row.id,
          )}
          selectedLogsCount={selectedRowsNumber}
          title="Apply scorers to selected logs"
          description={`Choose scorers to apply to ${selectedRowsNumber} selected log${
            selectedRowsNumber !== 1 ? "s" : ""
          }`}
        />
      )}
      {dml && rowsToDelete.length > 0 && (
        <ConfirmationDialog
          open={rowsToDelete.length > 0}
          onOpenChange={() => setRowsToDelete([])}
          title="Delete rows"
          description={`Are you sure you want to delete ${pluralizeWithCount(
            rowsToDelete.length,
            "row",
            "rows",
          )}?${
            additionalDeleteConfirmationDescription
              ? ` ${additionalDeleteConfirmationDescription}`
              : ""
          }`}
          confirmText={deleteButtonLabel ?? "Delete"}
          onConfirm={async () => {
            deselectAllTableRows();
            const preparedDeletes = await dml.prepareDeletes(rowsToDelete);
            await dml.upsert(preparedDeletes, {
              onOptimisticUpdate: () => {
                toast.success(
                  `Deleted ${pluralizeWithCount(rowsToDelete.length, "row", "rows")}`,
                );
              },
            });
          }}
        />
      )}
    </>
  );
};

export function prepareRowsForCopy({
  orgName,
  projectName,
  projectId,
  objectType,
  objectId,
  selectedRowsWithData,
  targetDataset,
  expectedFieldSource,
  cleanup,
}: {
  orgName: string;
  projectName: string;
  projectId: string;
  objectType: DataObjectType;
  objectId?: string;
  selectedRowsWithData: unknown[];
  targetDataset: {
    id: string;
    name: string;
  };
  expectedFieldSource: "output" | "auto";
  cleanup: () => void;
}): OtherObjectInserterInfo {
  return {
    objectInfo: targetDataset,
    getRows: async () =>
      selectedRowsWithData.map((row) => {
        const rowJson = parseObjectJSON(objectType, row);
        if (!objectLookupSupportedType.safeParse(objectType).success) {
          console.error(rowJson);
          throw new Error(`Unsupported origin object type ${objectType}`);
        }
        // for playgrounds, the underlying row could be a dataset row, so just use that
        const originObjectType = rowJson.dataset_id ? "dataset" : objectType;
        const originObjectId = rowJson.dataset_id ?? objectId;
        if (!originObjectId) {
          console.error(rowJson);
          throw new Error(`Source of type ${objectType} does not have ID`);
        }
        return {
          id: rowJson.id,
          input: rowJson.input,
          metadata: rowJson.metadata,
          expected:
            expectedFieldSource == "output"
              ? rowJson.output
              : (rowJson.expected ?? rowJson.output),
          [TagsField]: rowJson.tags,
          [ProjectIdField]: projectId,
          [DatasetIdField]: targetDataset.id,
          [OriginField]: {
            object_type: originObjectType,
            object_id: originObjectId,
            id: rowJson.id,
            _xact_id: rowJson._xact_id,
          },
        };
      }),
    onSuccess: (numRows: number) => {
      const datasetName = targetDataset.name;
      toast(`Copied ${pluralizeWithCount(numRows, "row", "rows")} to dataset`, {
        action: (
          <Link
            className={buttonVariants({ size: "xs" })}
            href={getDatasetLink({ orgName, projectName, datasetName })}
            target="_blank"
          >
            Go to dataset
          </Link>
        ),
        duration: 10000,
        dismissible: true,
      });
    },
    cleanup,
  };
}
