import { DataType, type Field } from "apache-arrow";
import { type PathTree } from "#/utils/display-paths";
import { doubleQuote } from "@braintrust/local/query";
import { diffKeynameForIndex } from "#/utils/diffs/diff-objects";

export function buildDiffStruct({
  experimentsFields,
  names,
  parentPath,
  displayPaths,
  allKeysOrdered,
}: {
  experimentsFields: Field[][];
  names: string[];
  parentPath: string[];
  displayPaths: PathTree;
  allKeysOrdered?: string[];
}): { name: string; value: string }[] {
  const paths = [];

  // If we have provided allKeysOrdered, check that the fields are
  // all in the allKeysOrdered.
  if (allKeysOrdered !== undefined) {
    const allKeysOrderedSet = new Set(allKeysOrdered);
    for (const field of experimentsFields.flat()) {
      if (!allKeysOrderedSet.has(field.name)) {
        throw new Error(
          `Unexpected field ${field.name} with expected keys ${JSON.stringify(
            allKeysOrdered,
          )}`,
        );
      }
    }
  }

  const experimentFieldsByName: Record<string, Field>[] = experimentsFields.map(
    (fields) => fields.reduce((acc, f) => ({ ...acc, [f.name]: f }), {}),
  );

  const allKeysSet = new Set([
    ...experimentFieldsByName.flatMap((fieldsByName) =>
      Object.keys(fieldsByName),
    ),
  ]);
  const allKeys = allKeysOrdered
    ? allKeysOrdered.filter((k) => allKeysSet.has(k))
    : Array.from(allKeysSet);

  for (const fieldName of allKeys) {
    const path = [...parentPath, fieldName];

    const fields =
      experimentFieldsByName.flatMap((fieldsByName) =>
        fieldsByName[fieldName] ? [fieldsByName[fieldName]] : [],
      ) ?? {};
    if (fields.length === 0) {
      continue;
    }

    // Slight edge case where the primary field is null but the comparison field is a struct
    // (e.g. primary has no scores, but comparison has scores).
    // This may populate the struct fields improperly so use the struct field schema from the comparison
    // In theory we could calculate the display paths completely with all comparison schemas
    // and then rely on the display paths directly, but this is decent a workaround for now
    const field = fields.reduce(
      (result: Field | null, f) =>
        result == null ||
        (!DataType.isStruct(result.type) && DataType.isStruct(f))
          ? f
          : result,
      null,
    );
    if (!field) {
      continue;
    }

    if (
      DataType.isStruct(field.type) &&
      displayPaths.type == "node" &&
      displayPaths.children[fieldName]?.type == "node"
    ) {
      const subPaths = buildDiffStruct({
        experimentsFields: experimentFieldsByName.map(
          (fieldsByName) => fieldsByName[fieldName]?.type.children ?? [],
        ),
        parentPath: path,
        displayPaths: displayPaths.children[fieldName],
        names,
      });
      paths.push({ name: field.name, value: wrapStruct(subPaths) });
    } else {
      const fieldPath = path.map((x) => `${doubleQuote(x)}`).join(".");

      const structFields = experimentFieldsByName
        .map((fieldsByName, i) => {
          const name = diffKeynameForIndex(i, true);
          const value = fieldsByName[fieldName]
            ? `${names[i]}.${fieldPath}`
            : "null";
          return `${doubleQuote(name)} := ${value}`;
        })
        .join(",");

      paths.push({
        name: field.name,
        value: `struct_pack(${structFields})
        `,
      });
    }
  }

  return paths;
}

export function wrapStruct(paths: { name: string; value: string }[]) {
  const value = paths
    .map(({ name, value }) => `${doubleQuote(name)} := ${value}`)
    .join(", ");
  return `struct_pack(${value})`;
}
