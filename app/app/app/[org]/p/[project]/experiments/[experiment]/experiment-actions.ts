"use server";

import {
  getObjects,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import { additionalProjections } from "#/pages/api/experiment/_constants";
import { experimentSchema } from "@braintrust/typespecs";
import { z } from "zod";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";

// In addition to the base experiment, we add a set of "comparables". Analogous
// to the comparables computed in `load_experiment_view`.
const experimentComparablesSchema = z.array(
  z.object({
    id: experimentSchema.shape.id,
    name: experimentSchema.shape.name,
    created: experimentSchema.shape.created,
    base_exp_id: experimentSchema.shape.base_exp_id,
  }),
);

const extendedExperimentSchema = experimentSchema.merge(
  z.object({
    comparables: experimentComparablesSchema,
  }),
);

export type ExtendedExperiment = z.infer<typeof extendedExperimentSchema>;

export type GetExperimentInput = {
  org_name: string;
  project_name: string;
  experiment_name: string;
};

export async function getExperiment({
  org_name,
  project_name,
  experiment_name,
}: GetExperimentInput): Promise<ExtendedExperiment | null> {
  const authLookup = await getServerSessionAuthLookup();
  const { query: comparablesQueryBase, queryParams: queryParams } =
    makeFullResultSetQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "experiment",
        aclPermission: "read",
      },
      filters: {
        org_name,
        project_name,
      },
    });

  const comparablesAdditionalProjection = (baseTableName: string) => `(
    with
    comparables_base as (${comparablesQueryBase})
    select coalesce(jsonb_agg(
        jsonb_build_object(
            'id', comparables_base.id,
            'name', comparables_base.name,
            'created', comparables_base.created,
            'base_exp_id', comparables_base.base_exp_id)
        order by comparables_base.created desc
    ), '[]'::jsonb) jsonb_agg
    from comparables_base
    where comparables_base.id <> ${baseTableName}.id
  ) "comparables"`;

  const extendedAdditionalProjections = (baseTableName: string) => [
    ...additionalProjections(baseTableName),
    comparablesAdditionalProjection(baseTableName),
  ];

  const rows = await getObjects({
    authLookup,
    startingParams: queryParams,
    permissionInfo: {
      aclObjectType: "experiment",
    },
    filters: {
      org_name,
      project_name,
      name: experiment_name,
    },
    finalResultSetAdditionalProjections: extendedAdditionalProjections,
    fullResultsSize: undefined,
  });
  if (rows.length !== 1) return null;
  return extendedExperimentSchema.parse(rows[0]);
}
