import { getProjectLink } from "../../getProjectLink";

export const getExperimentLink = ({
  orgName,
  projectName,
  experimentName,
}: {
  orgName: string;
  projectName: string;
  experimentName: string;
}) =>
  `${getExperimentsLink({ orgName, projectName })}/${encodeURIComponent(
    experimentName,
  )}`;

export const getExperimentsLink = ({
  orgName,
  projectName,
}: {
  orgName: string;
  projectName: string;
}) => `${getProjectLink({ orgName, projectName })}/experiments`;
