import { type ColumnDef } from "@tanstack/react-table";

/**
 * The column order is determined by the order they are queried for
 * i.e. for experiment-table, this is determined by `projectedPaths`.
 * Since start/end are sub columns of metric, we need to remove and append them
 * rather than changing the order in `projectedPaths`.
 */
export default function columnReorderer<TsData, TsValue>(
  columns: ColumnDef<TsData, TsValue>[],
) {
  const [filteredColumns, suffixColumns] = columns.reduce<
    [ColumnDef<TsData, TsValue>[], ColumnDef<TsData, TsValue>[]]
  >(
    ([filtered, suffix], column) => {
      if (column.meta?.name === "start" || column.meta?.name === "end") {
        return [filtered, [...suffix, column]];
      }
      return [[...filtered, column], suffix];
    },
    [[], []],
  );

  return [...filteredColumns, ...suffixColumns];
}
