import type * as duckdb from "@duckdb/duckdb-wasm";
import { dbQuery } from "#/utils/duckdb";
import { doubleQuote } from "#/utils/sql-utils";
import {
  EXCLUDE_METRICS,
  SUMMARY_DEFAULT_GROUP_KEY,
  type ScoreSummary,
  type SummaryDataParams,
  compareStructFieldsFn,
  computeSummaryScoreBatches,
  constructFullSummaryScan,
  constructFullSummaryScanAbbrev,
  experimentScanSpanSummary,
  makeChangesQueries,
  makeFullSummary,
  makeSummaryQueries,
  singleQuote,
} from "@braintrust/local/query";
import { createTempTable } from "#/utils/queries/useTempDuckTable";
import { normalizeArrowForJSON } from "#/utils/object";
import {
  type CustomColumnDefinition,
  parseCustomColumnsSchema,
} from "#/utils/custom-columns/use-custom-columns";
import {
  type LoadedExperimentSummary,
  type LoadExperimentScansParams,
} from "./loaders";
import { DataType } from "apache-arrow";

export type SpanSummaryQueryParams = LoadExperimentScansParams & {
  plainFilters?: string[];
  postAggregationFilters?: string[];
  commentsFilters?: string[];
  customColumnFilters?: string[];
  tags?: string[];
  hasErrorField: boolean;
  isRootAvailable: boolean;
  customColumns?: CustomColumnDefinition[];
  originDatasetRowId?: boolean;
  tableNameSuffix?: string;
  loadedSummary?: LoadedExperimentSummary;
};

export async function experimentScanSpanSummaryQuery(
  conn: duckdb.AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  {
    experimentId,
    experimentScanRaw,
    modelSpecScan,
    auditLogScan,
    scoreFields,
    metricFields,
    scoreConfig,
    comparisonKey,
    plainFilters,
    postAggregationFilters,
    commentsFilters,
    customColumnFilters,
    tags,
    hasErrorField,
    isRootAvailable,
    customColumns,
    originDatasetRowId,
    tableNameSuffix,
    loadedSummary,
    beforeCreateFn,
  }: SpanSummaryQueryParams & {
    experimentId: string | null;
    experimentScanRaw: string | null;
    auditLogScan: string | null;
    scoreFields: string[];
    metricFields: string[];
    beforeCreateFn?: (t: string) => void;
  },
) {
  if (!experimentId) {
    return null;
  }
  const tableNameHashKey = `exp_summary_${experimentId}_${tableNameSuffix ?? ""}`;
  if (loadedSummary === undefined) {
    const fullScan = experimentScanSpanSummary({
      experimentScanRaw,
      modelSpecScan,
      auditLogScan,
      scoreFields,
      scoreConfig,
      comparisonKey,
      plainFilters,
      postAggregationFilters,
      commentsFilters,
      tags,
      hasErrorField,
      isRootAvailable,
      originDatasetRowId,
    });
    if (!fullScan) {
      return null;
    }

    const tableName = await createTempTable(conn, abortSignal, {
      scan: fullScan,
      tableNameHashKey,
      beforeCreateFn,
    });
    if (!tableName) {
      return null;
    }

    const summaryScan = `FROM ${doubleQuote(tableName)}`;
    const initialScan = constructFullSummaryScan({
      experimentScanRaw,
      summaryScan,
      auditLogScan,
      includeScoresMap: true,
    });

    const { customColumns: customColumnsTyped, customColumnsSchema } =
      await parseCustomColumnsSchema({
        conn,
        abort: abortSignal,
        customColumns,
        variant: "experiment",
        rowScan: initialScan,
      });

    const scan = constructFullSummaryScan({
      experimentScanRaw,
      summaryScan,
      auditLogScan,
      customColumns: customColumnsTyped?.map(({ sql }) => sql),
      customColumnFilters,
      originDatasetRowId,
    });

    if (!scan) {
      return null;
    }

    return {
      tempTableName: tableName,
      tableNameHashKey,
      scan,
      customColumns: customColumnsTyped,
      customColumnsSchema,
    };
  } else {
    if (!loadedSummary.scan) {
      return null;
    }
    // This is a total hack because we pass this temp table name around a bunch of places
    const tableName = await createTempTable(conn, abortSignal, {
      scan: loadedSummary.scan,
      tableNameHashKey,
      beforeCreateFn,
    });
    if (!tableName) {
      return null;
    }

    const intFields =
      loadedSummary.schema?.fields
        .find((f) => f.name === "metrics" && DataType.isStruct(f.type))
        ?.type.children.filter((c) => DataType.isInt(c.type))
        .map((c) => c.name) ?? [];

    // For back-compat, include these fields so that users don't have to update their data plane
    // to have integer values.
    intFields.push(
      "prompt_tokens",
      "prompt_cached_tokens",
      "prompt_cache_creation_tokens",
      "completion_tokens",
      "total_tokens",
    );

    const scan = constructFullSummaryScanAbbrev({
      summaryScan: `FROM ${doubleQuote(tableName)}`,
      filters: plainFilters,
      scoreNames: loadedSummary.scoreNames,
      metricNames: loadedSummary.metricNames,
      integerMetrics: new Set(intFields),
    });

    if (!scan) {
      return null;
    }

    return {
      tempTableName: tableName,
      tableNameHashKey,
      scan,
    };
  }
}

export const DEFAULT_GROUP_KEY_FN = () =>
  singleQuote(SUMMARY_DEFAULT_GROUP_KEY);

export async function summaryDataQuery(
  conn: duckdb.AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  {
    primaryExperimentScan,
    comparisonExperimentScan,
    groupKey,
    comparisonKey,
    comparisonKeyFilterClause,
    scoreFields,
    comparisonScoreFields,
    metricFields,
    comparisonMetricFields,
    metricDefinitions,
    scoreConfig,
    hasErrorField,
  }: SummaryDataParams,
): Promise<{
  summary: {
    [key: string]: ScoreSummary;
  } | null;
  summaryMetrics: string[];
}> {
  // DuckDB struggles trying to compute aggregates over lots of fields at once, so we compute
  // these summaries in batches.
  const { allSummaryDefBatches, comparableDefBatches, summaryMetricNames } =
    computeSummaryScoreBatches({
      scoreFields,
      comparisonScoreFields,
      metricFields,
      metricDefinitions,
      comparisonMetricFields,
      scoreConfig,
      hasErrorField,
    });

  const summaryQueries = makeSummaryQueries({
    experimentScan: primaryExperimentScan,
    allSummaryDefBatches,
    groupKey,
    comparisonKey,
    comparisonKeyFilterClause,
    includeNullGroups: true,
  });

  const comparisonSummaryQueries = makeSummaryQueries({
    experimentScan: comparisonExperimentScan,
    allSummaryDefBatches: comparableDefBatches,
    groupKey,
    comparisonKey,
    structFieldsFn: compareStructFieldsFn,
    includeNullGroups: true,
  });

  if (summaryQueries == null || summaryQueries.length === 0) {
    return {
      summary: null,
      summaryMetrics: summaryMetricNames,
    };
  }

  const changesQueries = makeChangesQueries({
    primaryExperimentScan,
    comparisonExperimentScan,
    comparableDefBatches,
    groupKey,
    comparisonKey,
    comparisonKeyFilterClause,
    includeNullGroups: true,
  });

  // In both cases, we want to convert to ordinary javascript types. The
  // most straightforward way to do that appears to be via JSON roundtripping.
  const [summaryData, diffData, comparisonSummaryData] = await Promise.all([
    Promise.all(summaryQueries.map((q) => dbQuery(conn, abortSignal, q))),
    Promise.all(
      (changesQueries ?? []).map((q) => dbQuery(conn, abortSignal, q)),
    ),
    Promise.all(
      (comparisonSummaryQueries ?? []).map((q) =>
        dbQuery(conn, abortSignal, q),
      ),
    ),
  ]);

  const summary = makeFullSummary({
    summaryData: summaryData
      .flatMap((batch) => batch?.toArray() ?? [])
      .map((r) =>
        JSON.parse(JSON.stringify(r.toJSON(), normalizeArrowForJSON)),
      ),
    compareSummaryData:
      comparisonSummaryData
        .flatMap((batch) => batch?.toArray() ?? [])
        .map((r) =>
          JSON.parse(JSON.stringify(r.toJSON(), normalizeArrowForJSON)),
        ) ?? [],
    diffData: diffData
      ? diffData
          .flatMap((batch) => batch?.toArray() ?? [])
          .map((r) =>
            JSON.parse(JSON.stringify(r.toJSON(), normalizeArrowForJSON)),
          )
      : null,
  });

  return {
    summary,
    summaryMetrics: summaryMetricNames.filter(
      (m) => !EXCLUDE_METRICS.includes(m),
    ),
  };
}
