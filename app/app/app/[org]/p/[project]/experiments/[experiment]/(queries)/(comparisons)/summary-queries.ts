import { useDuckDB } from "#/utils/duckdb";
import { combineResults } from "#/utils/react-query";
import { type SummaryDataParams } from "@braintrust/local/query";
import { useQueries, useQuery } from "@tanstack/react-query";
import { useEffect, useMemo, useState } from "react";
import { DEFAULT_GROUP_KEY_FN, summaryDataQuery } from "../experimentQueries";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import {
  type PrimaryExperimentSpanSummary,
  type ComparisonExperimentSpanSummary,
} from "../useExperiment";

export function useSummaryQueries({
  experiment,
  summaryDataParams,
  comparisonExperimentData,
  tableGrouping,
}: {
  experiment: PrimaryExperimentSpanSummary | undefined;
  summaryDataParams: SummaryDataParams;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  tableGrouping: string;
}) {
  const duck = useDuckDB();
  const enabled =
    !!duck &&
    !!experiment?.id &&
    !!experiment?.queryKeys &&
    !!summaryDataParams.primaryExperimentScan;
  const summaryQuery = useQuery({
    queryKey: [
      experiment?.queryKeys,
      "summaryData",
      tableGrouping,
      summaryDataParams,
      summaryDataParams.groupKey?.(""),
      summaryDataParams.comparisonKey?.(""),
      summaryDataParams.comparisonKeyFilterClause?.(""),
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      const queries = [
        summaryDataQuery(conn, signal, {
          ...summaryDataParams,
          groupKey: DEFAULT_GROUP_KEY_FN,
        }),
      ];
      if (tableGrouping !== GROUP_BY_NONE_VALUE) {
        queries.push(
          summaryDataQuery(conn, signal, {
            ...summaryDataParams,
          }),
        );
      }

      const [result, groupedResult] = await Promise.all(queries);

      return result.summary
        ? {
            summary: result.summary,
            groupedSummary: groupedResult?.summary,
            summaryMetrics: result.summaryMetrics,
            tableGrouping,
          }
        : null;
    },
    staleTime: Infinity,
    enabled,
  });
  const summaryData = useMemo(() => {
    // can't use placeholderData because isSuccess defaults to false when it's provided
    if (!summaryQuery.data || !enabled) {
      return {
        summary: null,
        summaryMetrics: [],
        groupedSummary: null,
        tableGrouping: GROUP_BY_NONE_VALUE,
      };
    }
    return summaryQuery.data;
  }, [summaryQuery.data, enabled]);

  const _comparisonSummaryResult = useQueries({
    queries: comparisonExperimentData.map((e) => ({
      queryKey: [
        [...(experiment?.queryKeys ?? []), e.queryKeys],
        "breakdownSummary",
        tableGrouping,
        [
          summaryDataParams.primaryExperimentScan,
          summaryDataParams.scoreFields,
          summaryDataParams.metricFields,
          summaryDataParams.groupKey?.(""),
          summaryDataParams.comparisonKey?.(""),
          summaryDataParams.comparisonKeyFilterClause?.(""),
          summaryDataParams.hasErrorField,
          e.tableScan,
          e.scoreFields,
          e.metricFields,
        ],
      ],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
        const conn = await duck!.connect();
        const overlappingScoreFields = summaryDataParams.scoreFields.filter(
          (f) => e.scoreFields.includes(f),
        );
        const overlappingMetricFields = summaryDataParams.metricFields.filter(
          (f) => e.metricFields.includes(f),
        );

        const params = {
          ...summaryDataParams,
          comparisonExperimentScan: e.tableScan,
          comparisonScoreFields: overlappingScoreFields,
          comparisonMetricFields: overlappingMetricFields,
        };

        const queries = [
          summaryDataQuery(conn, signal, {
            ...params,
            groupKey: DEFAULT_GROUP_KEY_FN,
          }),
        ];
        if (tableGrouping !== GROUP_BY_NONE_VALUE) {
          queries.push(summaryDataQuery(conn, signal, { ...params }));
        }

        const [result, groupedResult] = await Promise.all(queries);

        return result.summary
          ? {
              summary: result.summary,
              groupedSummary: groupedResult?.summary,
            }
          : null;
      },
      staleTime: Infinity,
      throwOnError: (e: Error) => {
        console.warn("error querying", e);
        return false;
      },
      enabled:
        !!duck &&
        !!experiment?.id &&
        !!e.id &&
        !!e.tableScan &&
        !!summaryDataParams.primaryExperimentScan,
    })),
    combine: combineResults,
  });

  // cache the results so we don't flicker when changing group-by
  const [_comparisonSummaryData, setComparisonSummaryData] = useState(
    _comparisonSummaryResult.data,
  );
  const areComparisonsEmpty =
    comparisonExperimentData.every((e) => !e.id) ||
    comparisonExperimentData.length === 0;
  useEffect(() => {
    if (areComparisonsEmpty) {
      setComparisonSummaryData([]);
      return;
    }
    if (!_comparisonSummaryResult.isFetching) {
      setComparisonSummaryData(_comparisonSummaryResult.data);
    }
  }, [
    setComparisonSummaryData,
    _comparisonSummaryResult.data,
    _comparisonSummaryResult.isFetching,
    areComparisonsEmpty,
  ]);

  const comparisonSummaryData = useMemo(() => {
    return _comparisonSummaryData.filter((v) => !!v);
  }, [_comparisonSummaryData]);

  return { summaryData, comparisonSummaryData };
}
