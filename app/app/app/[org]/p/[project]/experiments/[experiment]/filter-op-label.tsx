import { cn } from "#/utils/classnames";
import { ArrowDownRight, ArrowUpR<PERSON>, Equal, ListCheck } from "lucide-react";
import { type ComparisonType } from "./regressions-query";

interface FilterOpLabelProps {
  comparisonType: ComparisonType;
  experimentName?: string;
  experimentClassName?: string;
  field: string;
  group?: string;
}

export function FilterOpLabel({
  comparisonType,
  experimentName,
  experimentClassName,
  field,
  group,
}: FilterOpLabelProps) {
  let prefix = null;
  switch (comparisonType) {
    case "improvements":
      prefix = (
        <span className="inline-block">
          <ArrowUpRight className="inline size-3 shrink-0 text-good-700" />{" "}
          <span className="font-medium text-good-700">Higher</span>
        </span>
      );
      break;
    case "equal":
      prefix = (
        <span className="inline-block">
          <Equal className="inline size-3 shrink-0 text-primary-700" />{" "}
          <span className="font-medium text-primary-700">Equal</span>
        </span>
      );
      break;
    case "regressions":
      prefix = (
        <span className="inline-block">
          <ArrowDownRight className="inline size-3 shrink-0 text-bad-700" />{" "}
          <span className="font-medium text-bad-700">Lower</span>
        </span>
      );
      break;
    case "exists":
      prefix = (
        <span className="inline-block">
          <ListCheck className="inline size-3 shrink-0 text-primary-500" />{" "}
          <span className="font-medium text-primary-500">Exists</span>
        </span>
      );
      break;
  }

  return (
    <>
      {prefix} {field}
      <span>
        {experimentName && (
          <>
            {experimentName === "any" ? (
              " in any experiment"
            ) : (
              <>
                {comparisonType === "exists" ? " in " : " than "}
                <span
                  className={cn(
                    "font-semibold",
                    experimentClassName,
                    "bg-transparent",
                  )}
                >
                  {experimentName}
                </span>
              </>
            )}
          </>
        )}
        {group && (
          <>
            {" with "}
            {`${group}`}
          </>
        )}
      </span>
    </>
  );
}
