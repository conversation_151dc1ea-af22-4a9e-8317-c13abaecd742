import { DiffLeftField, DiffRightField } from "#/utils/diffs/diff-objects";
import { doubleQuote } from "#/utils/sql-utils";

export function outputVsExpectedExpr(
  expectedField: string,
  outputField: string,
  relation?: string,
) {
  const relationStr = relation ? `${relation}.` : "";

  return `struct_pack(${doubleQuote(DiffLeftField)} := (${relationStr}${doubleQuote(
    expectedField,
  )}), ${doubleQuote(DiffRightField)} := (${relationStr}${doubleQuote(outputField)}))`;
}

export function fieldNamesForExperiment(
  fields: string[],
  alias: string,
  asStructField?: boolean,
) {
  return fields
    .map((f) => {
      const isOutputVsExpected = f.includes("output_vs_expected");
      if (isOutputVsExpected) {
        const value = outputVsExpectedExpr("expected", "output", alias);
        return asStructField
          ? `output_vs_expected := ${value}`
          : `${value} AS output_vs_expected`;
      }

      const value = `${alias}.${doubleQuote(f)}`;
      return asStructField ? `${doubleQuote(f)} := ${value}` : value;
    })
    .join(", ");
}
