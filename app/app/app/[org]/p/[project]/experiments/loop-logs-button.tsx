import { Button } from "#/ui/button";
import { Blend } from "lucide-react";
import {
  type ContextObject,
  useGlobalChat,
} from "#/ui/optimization/use-global-chat-context";
import { type TableSelection } from "#/ui/table/useTableSelection";

const LoopLogsButton = ({
  selectionProps,
}: {
  selectionProps: TableSelection;
}) => {
  const { setCurrentSessionContextObjects, setIsChatOpen, handleSendMessage } =
    useGlobalChat();

  const { getSelectedRowsWithData } = selectionProps;

  return (
    <>
      <Button
        size="xs"
        variant="border"
        onClick={() => {
          const rows = getSelectedRowsWithData();
          const traceContextObjects: Record<string, ContextObject> =
            Object.fromEntries(
              rows.map((row) => [
                String(row.id),
                {
                  id: String(row.id),
                  name:
                    typeof row.input === "object"
                      ? JSON.stringify(row.input)
                      : String(row.input),
                  resource: "trace",
                  expected: row.expected,
                  input: row.input,
                  metadata: row.metadata,
                  origin: row.origin,
                  output: row.output,
                  scores: row.scores,
                  tags: row.tags,
                  created: row.created,
                  error: row.error,
                  metrics: row.metrics,
                },
              ]),
            );

          setCurrentSessionContextObjects((prev) => ({
            ...prev,
            ...traceContextObjects,
          }));
          setTimeout(() => {
            setIsChatOpen(true);
          }, 250);
        }}
        Icon={Blend}
      >
        Add to Loop chat
      </Button>
      <Button
        size="xs"
        variant="border"
        onClick={() => {
          const rows = getSelectedRowsWithData();
          const traceContextObjects: Record<string, ContextObject> =
            Object.fromEntries(
              rows.map((row) => [
                String(row.id),
                {
                  id: String(row.id),
                  name:
                    typeof row.input === "object"
                      ? JSON.stringify(row.input)
                      : String(row.input),
                  resource: "trace",
                  expected: row.expected,
                  input: row.input,
                  metadata: row.metadata,
                  origin: row.origin,
                  output: row.output,
                  scores: row.scores,
                  tags: row.tags,
                  created: row.created,
                  error: row.error,
                  metrics: row.metrics,
                },
              ]),
            );

          handleSendMessage(
            {
              id: crypto.randomUUID(),
              type: "user_message",
              message: `Find other traces that are similar to the tagged traces.`,
              contextObjects: traceContextObjects,
            },
            {
              clearContextObjects: false,
              clearUserMessage: false,
            },
          );
          setTimeout(() => {
            setIsChatOpen(true);
          }, 250);
        }}
        Icon={Blend}
      >
        Find similar traces
      </Button>
    </>
  );
};

export default LoopLogsButton;
