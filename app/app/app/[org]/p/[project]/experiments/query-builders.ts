import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import {
  type BTQLTableDefinition,
  compileClauseToSQL,
} from "#/utils/search-btql";
import { type ViewProps } from "#/utils/view/use-view";
import { isAggregateScore, jsonPath } from "@braintrust/local/query";
import {
  type CustomColumn,
  type CustomColumnDefinition,
  useParseCustomColumnsSchema,
} from "#/utils/custom-columns/use-custom-columns";
import { useMemo } from "react";
import { BT_GROUP_KEY } from "#/ui/table/grouping/queries";
import { z } from "zod";

export interface QueryGenerationParams {
  experimentsTable: string | null;
  viewProps: ViewProps<true>;
  sortedScoreFields: string[];
  scoreAliases: string[];
  summaryTable: string | null;
  summaryReady: boolean;
  aggregateScoreSQL: Record<string, string>;
  btqlSummaryUnsupported: boolean;
  btqlCountSummaryUnsupported: boolean;
  countTable: string | null;
  countReady: boolean;
  projectId: string;
  scoreToAlias: Record<string, string>;
  scoreConfig: Array<{
    name: string;
    score_type?:
      | "minimum"
      | "maximum"
      | "slider"
      | "categorical"
      | "weighted"
      | "online"
      | "free-form";
  }>;
  customColumns?: CustomColumn[];
}

// ========================================
// COLUMN GENERATION UTILITIES
// ========================================

/**
 * Generates the field list for the main SELECT statement
 */

export function generateFields(
  scoreFields: string[],
  customColumns?: string[],
  mapField?: (f: string) => string,
): string {
  const baseFields = ["name"];
  const metricFields = [
    "num_examples",
    "error_rate",
    "num_errors",
    "duration",
    "llm_duration",
    "prompt_tokens",
    "completion_tokens",
    "total_tokens",
    "creator",
    "last_updated",
    "source",
    "metadata",
    "description",
    "id",
    "dataset",
    "tags",
  ];

  const allFields = [
    ...baseFields.map((f) => (mapField ? mapField(f) : f)),
    ...scoreFields,
    ...metricFields.map((f) => (mapField ? mapField(f) : f)),
    ...(customColumns || []).map((f) => (mapField ? mapField(f) : f)),
  ];

  return allFields.join(",");
}

/**
 * Generates score column selections for non-aggregate scores
 */
function generateScoreColumns(
  nonAggregateScores: string[],
  hasSummaryData: boolean,
  btqlSummaryUnsupported: boolean,
  scoreToAlias: Record<string, string>,
): string {
  if (nonAggregateScores.length === 0) return "";

  const scoreColumns = nonAggregateScores.map((scoreField) => {
    const aliasName = scoreToAlias[scoreField] ?? scoreField;

    if (!hasSummaryData) {
      return `NULL AS ${doubleQuote(aliasName)}`;
    }

    if (btqlSummaryUnsupported) {
      return `COALESCE(experiments_summary.${doubleQuote(scoreField)}, NULL) AS ${doubleQuote(aliasName)}`;
    } else {
      return `CASE
        WHEN experiments_summary.scores[${singleQuote(scoreField)}] IS NULL THEN NULL
        ELSE experiments_summary.scores[${singleQuote(scoreField)}].avg
      END AS ${doubleQuote(aliasName)}`;
    }
  });

  return scoreColumns.join(",") + ",";
}

/**
 * Generates aggregate score column selections
 */
function generateAggregateScoreColumns(
  aggregateScoreSQL: Record<string, string>,
  hasSummaryData: boolean,
  scoreToAlias: Record<string, string>,
): string {
  if (Object.keys(aggregateScoreSQL).length === 0) return "";

  const aggregateColumns = Object.entries(aggregateScoreSQL).map(
    ([name, expr]) => {
      const aliasName = scoreToAlias[name] ?? name;

      if (hasSummaryData) {
        return `COALESCE(${expr}, NULL) AS ${doubleQuote(aliasName)}`;
      } else {
        return `NULL AS ${doubleQuote(aliasName)}`;
      }
    },
  );

  return aggregateColumns.join(",") + ",";
}

/**
 * Generates metric column selections (duration, tokens, etc.)
 */
function generateMetricColumns(
  hasCountData: boolean,
  btqlCountSummaryUnsupported: boolean,
): string {
  const metricFields = [
    "duration",
    "llm_duration",
    "prompt_tokens",
    "completion_tokens",
    "total_tokens",
    "num_errors",
    "error_rate",
  ] as const;

  return metricFields
    .map((field) => {
      if (btqlCountSummaryUnsupported) {
        return `NULL AS ${doubleQuote(field)}`;
      } else if (hasCountData) {
        return `COALESCE(experiments_count.${doubleQuote(field)}, NULL) AS ${doubleQuote(field)}`;
      } else {
        return `NULL AS ${doubleQuote(field)}`;
      }
    })
    .join(",");
}

/**
 * Generates custom column selections
 */
function generateCustomColumns(customColumns?: CustomColumn[]): string {
  if (!customColumns || customColumns.length === 0) return "";

  return customColumns.map(({ sql }) => sql).join(",") + ",";
}

// ========================================
// JOIN AND TABLE UTILITIES
// ========================================

/**
 * Generates the JOIN clauses for summary and count tables
 */
function generateJoinClause(
  summaryTable: string | null,
  hasSummaryData: boolean,
  countTable: string | null,
  hasCountData: boolean,
  btqlCountSummaryUnsupported: boolean,
): string {
  const joins: string[] = [];

  if (summaryTable && hasSummaryData) {
    joins.push(
      `LEFT JOIN "${summaryTable}" experiments_summary ON experiments.id = experiments_summary.experiment_id`,
    );
  }

  if (!btqlCountSummaryUnsupported && countTable && hasCountData) {
    joins.push(
      `LEFT JOIN "${countTable}" experiments_count ON experiments.id = experiments_count.experiment_id`,
    );
  }

  return joins.join("\n        ");
}

/**
 * Generates the last_updated column selection with fallback logic
 */
function generateLastUpdatedColumn(
  hasSummaryData: boolean,
  btqlCountSummaryUnsupported: boolean,
): string {
  if (hasSummaryData) {
    const timestampCast = btqlCountSummaryUnsupported ? "::timestamp" : "";
    return `COALESCE(experiments_summary.last_updated${timestampCast}, experiments.created::timestamp)`;
  } else {
    return `experiments.created::timestamp`;
  }
}

/**
 * Generates the num_examples column with fallback logic
 */
function generateNumExamplesColumn(
  hasCountData: boolean,
  btqlCountSummaryUnsupported: boolean,
  hasSummaryData: boolean,
  btqlSummaryUnsupported: boolean,
): string {
  if (!btqlCountSummaryUnsupported && hasCountData) {
    return `COALESCE(experiments_count.num_examples, NULL)`;
  } else if (btqlSummaryUnsupported && hasSummaryData) {
    return `COALESCE(experiments_summary.num_examples, NULL)`;
  } else {
    return `NULL`;
  }
}

// ========================================
// QUERY BUILDING UTILITIES
// ========================================

/**
 * Creates the base experiments table subquery with metadata handling
 */
function buildExperimentsTableSubquery(
  experimentsTable: string | null,
): string | null {
  return experimentsTable
    ? `(
        SELECT * REPLACE(json(metadata) AS metadata)
        FROM ${doubleQuote(experimentsTable)}
      )`
    : null;
}

/**
 * Builds match expressions for full-text search
 */
function buildMatchExpressions(
  matchTerms: Array<{ text: string }> | undefined,
  experimentsTable: string | null,
) {
  if (!matchTerms || !experimentsTable) return undefined;

  return matchTerms.map((term, index) => {
    const scoreField = `__fts_score${index}`;
    return {
      column: `fts_main_${experimentsTable}.match_bm25(
                id,
                ${singleQuote(term.text)}
              ) AS ${scoreField}`,
      filterText: `experiments.${scoreField} IS NOT NULL OR experiments.search_text ILIKE ${singleQuote(
        "%" + term.text + "%",
      )}`,
      sortText: `${scoreField} DESC NULLS LAST`,
    };
  });
}

/**
 * Builds the experiments subquery with optional match expressions
 */
function buildExperimentsSubquery(
  experimentsTableSubquery: string | null,
  matchExprs: ReturnType<typeof buildMatchExpressions>,
): string | null {
  if (!experimentsTableSubquery) return null;

  if (matchExprs?.length) {
    return `(
           SELECT *, ${matchExprs.map((e) => e.column).join(", ")}
           FROM ${experimentsTableSubquery}
         )`;
  }

  return experimentsTableSubquery;
}

// ========================================
// WHERE AND ORDER BY UTILITIES
// ========================================

/**
 * Builds WHERE clause components
 */
function buildWhereClause(
  filters: string[],
  matchExprs: ReturnType<typeof buildMatchExpressions>,
): { where: string; matchFilter: string } {
  const matchFilter = matchExprs?.length
    ? matchExprs.map((e) => e.filterText).join(" AND ")
    : "TRUE";

  const where = filters.length
    ? filters.map((f) => `(${f})`).join(" AND ")
    : "true";

  return { where, matchFilter };
}

/**
 * Builds the ORDER BY clause with proper precedence
 */
function buildOrderByClause(
  sortClauses: Array<{ text: string }> | undefined,
  matchExprs: ReturnType<typeof buildMatchExpressions>,
): string {
  const orderByClauses: string[] = [];

  // User-defined sorts take precedence
  if (sortClauses?.length) {
    orderByClauses.push(...sortClauses.map((s) => s.text));
  }

  // Full-text search scores come next
  if (matchExprs?.length) {
    orderByClauses.push(...matchExprs.map((e) => e.sortText));
  }

  // Default sort by last updated
  orderByClauses.push("last_updated DESC NULLS LAST");

  return orderByClauses.join(", ");
}

export function useExperimentsQuery({
  params,
  customColumns,
  experimentsSignals,
  experimentsSchema,
}: {
  params: QueryGenerationParams;
  customColumns?: CustomColumnDefinition[];
  experimentsSignals: number[];
  experimentsSchema: BTQLTableDefinition | null;
}) {
  const baseExperimentsQuery = useMemo(
    () => buildExperimentsQuery(params),
    [params],
  );

  const {
    data: customColumnsTyped,
    error: parseCustomColumnsError,
    isPending: parseCustomColumnsPending,
  } = useParseCustomColumnsSchema(
    useMemo(
      () => ({
        rowScan: baseExperimentsQuery,
        customColumns,
        tableDef: experimentsSchema,
        tableAlias: "experiments",
        variant: "experiment",
      }),
      [baseExperimentsQuery, customColumns, experimentsSchema],
    ),
    experimentsSignals,
  );

  const experimentsQuery = useMemo(
    () =>
      buildExperimentsQuery({
        ...params,
        customColumns: customColumnsTyped?.customColumns ?? [],
      }),
    [params, customColumnsTyped?.customColumns],
  );
  return {
    experimentsQuery,
    customColumnsTyped,
    parseCustomColumnsError,
    parseCustomColumnsPending,
  };
}

// ========================================
// MAIN QUERY BUILDER
// ========================================

/**
 * Builds the complete experiments query
 */
export function buildExperimentsQuery(
  params: QueryGenerationParams,
): string | null {
  const {
    experimentsTable,
    viewProps,
    sortedScoreFields,
    scoreAliases,
    summaryTable,
    summaryReady,
    aggregateScoreSQL,
    btqlSummaryUnsupported,
    btqlCountSummaryUnsupported,
    countTable,
    countReady,
    projectId,
    scoreToAlias,
    scoreConfig,
    customColumns,
  } = params;

  const searchParams = viewProps.search;

  // Build subqueries and expressions
  const experimentsTableSubquery =
    buildExperimentsTableSubquery(experimentsTable);
  const matchExprs = buildMatchExpressions(
    searchParams.match,
    experimentsTable,
  );
  const experimentsSubquery = buildExperimentsSubquery(
    experimentsTableSubquery,
    matchExprs,
  );

  if (!experimentsSubquery) return null;

  // Build filter and order clauses
  const filters = searchParams.filter?.map((f) => compileClauseToSQL(f)) ?? [];
  const { where, matchFilter } = buildWhereClause(filters, matchExprs);
  const orderBy = buildOrderByClause(searchParams.sort, matchExprs);

  // Determine which scores are non-aggregate
  const nonAggregateScores = sortedScoreFields.filter(
    (scoreField) =>
      !scoreConfig.find(
        (s) =>
          s.name === scoreField &&
          s.score_type &&
          isAggregateScore(s.score_type),
      ),
  );

  // Calculate data availability
  const hasSummaryData = summaryTable !== null && summaryReady;
  const hasCountData = countTable !== null && countReady;

  // Build the complete query
  return `
    SELECT
      ${generateFields(
        scoreAliases.map(doubleQuote),
        customColumns?.map(({ name }) => doubleQuote(name)),
      )}
    FROM (
      SELECT
        experiments.id AS id,
        experiments.name,
        ${generateScoreColumns(
          nonAggregateScores,
          hasSummaryData,
          btqlSummaryUnsupported,
          scoreToAlias,
        )}
        ${generateAggregateScoreColumns(
          aggregateScoreSQL,
          hasSummaryData,
          scoreToAlias,
        )}
        ${generateMetricColumns(hasCountData, btqlCountSummaryUnsupported)},
        ${generateCustomColumns(customColumns)}
        experiments.user AS creator,
        ${generateLastUpdatedColumn(
          hasSummaryData,
          btqlCountSummaryUnsupported,
        )} AS last_updated,
        ${generateNumExamplesColumn(
          hasCountData,
          btqlCountSummaryUnsupported,
          hasSummaryData,
          btqlSummaryUnsupported,
        )} AS num_examples,
        experiments.repo_info AS source,
        experiments.metadata AS metadata,
        experiments.description AS description,
        experiments.dataset AS dataset,
        experiments.tags AS tags
      FROM ${experimentsSubquery} experiments
      ${generateJoinClause(
        summaryTable,
        hasSummaryData,
        countTable,
        hasCountData,
        btqlCountSummaryUnsupported,
      )}
      WHERE experiments.project_id = '${projectId}'
        AND ${matchFilter}
      ORDER BY ${orderBy}
    ) base
    WHERE ${customColumns === undefined ? "TRUE" : where}
  `;
}

export const getGroupBy = (
  groupByVal: string,
  customColumns?: CustomColumn[],
) => {
  const metadataPath = jsonPath(JSON.parse(groupByVal));
  const metadataGroupBy = `
    JSON_TYPE("metadata", ${metadataPath}) as metadata_type,
    CASE WHEN metadata_type NOT IN ('ARRAY', 'OBJECT') THEN
      JSON_EXTRACT_STRING("metadata", ${metadataPath})
    ELSE null END as ${BT_GROUP_KEY}
  `;
  try {
    const colNames = new Set((customColumns || []).map(({ name }) => name));
    const columnNameCandidate = z.string().parse(JSON.parse(groupByVal)[0]);
    const isCustomColumn = colNames.has(columnNameCandidate);
    if (isCustomColumn) {
      return `${doubleQuote(columnNameCandidate)}::VARCHAR as ${BT_GROUP_KEY}`;
    }
    return metadataGroupBy;
  } catch {
    return metadataGroupBy;
  }
};
