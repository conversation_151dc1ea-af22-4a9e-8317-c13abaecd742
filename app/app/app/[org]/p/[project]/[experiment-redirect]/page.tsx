import { redirect } from "next/navigation";

export default async function Page(props: {
  params: Promise<{
    org: string;
    project: string;
    "experiment-redirect": string;
  }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const searchParams = await props.searchParams;
  const params = await props.params;
  const queryString = new URLSearchParams(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    Object.entries(searchParams)
      .flatMap(([key, value]) =>
        Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]],
      )
      .filter(([, value]) => value !== undefined) as [string, string][],
  ).toString();
  return redirect(
    `/app/${params.org}/p/${params.project}/experiments/${params["experiment-redirect"]}${queryString ? `?${queryString}` : ""}`,
  );
}
