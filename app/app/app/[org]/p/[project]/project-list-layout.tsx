import * as React from "react";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import Link from "next/link";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import Footer from "#/ui/landing/footer";
import { BodyWrapper, HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import { useIsSidenavDocked } from "../../sidenav-state";

export const ProjectNavItem = ({
  href,
  active,
  className,
  activeClassName,
  children,
  onClick,
}: {
  href: string;
  active?: boolean;
  className?: string;
  activeClassName?: string;
  children: React.ReactNode;
  onClick?: () => void;
}) => {
  return (
    <Link
      prefetch
      href={href}
      className={cn(
        buttonVariants({
          variant: "ghost",
          size: "xs",
        }),
        "justify-start gap-1.5 px-2 text-primary-600",
        className,
        {
          [`pointer-events-none bg-primary-200 text-primary-950 ${activeClassName}`]:
            active,
        },
      )}
      onClick={onClick}
    >
      {children}
    </Link>
  );
};

export const ProjectListLayout = ({
  children,
  orgName,
  scrollContainerRef,
}: React.PropsWithChildren<{
  orgName: string;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
}>) => {
  const isSidenavDocked = useIsSidenavDocked();
  return (
    <MainContentWrapper
      className={cn(
        "flex flex-col overflow-hidden p-0",
        HEIGHT_WITH_TOP_OFFSET,
      )}
      hideFooter
    >
      <BodyWrapper innerClassName="flex" isSidenavDocked={isSidenavDocked}>
        <div
          className="relative flex w-full flex-1 flex-col overflow-auto px-3"
          ref={scrollContainerRef}
        >
          {children}
          <div className="grow" />
          <Footer
            className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
            inApp
            orgName={orgName}
          />
        </div>
      </BodyWrapper>
    </MainContentWrapper>
  );
};
