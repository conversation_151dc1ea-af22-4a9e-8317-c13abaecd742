// Common utilities shared between different project views.
import { getDatasetLink } from "#/app/app/[org]/p/[project]/datasets/[dataset]/getDatasetLink";
import type { FormatterProps } from "#/ui/arrow-table";
import type { FormatterMap } from "#/ui/field-to-column";
import { type Row } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { type MouseEventHandler, useCallback, useMemo } from "react";
import { getPlaygroundLink } from "../../prompt/[prompt]/getPromptLink";
import { getExperimentLink } from "./experiments/[experiment]/getExperimentLink";
import { PercentIcon } from "lucide-react";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { cn } from "#/utils/classnames";
import type { AggregationType } from "#/utils/queries/aggregations";
import { HeaderAliases } from "@braintrust/local/api-schema";
import {
  DefaultFormatter,
  DefaultWithAggregationFormatter,
} from "#/ui/table/formatters/default-formatter";
import { DurationWithAggregationFormatter } from "#/ui/table/formatters/duration-formatter";
import { PercentWithAggregationFormatter } from "#/ui/table/formatters/percent-formatter";
import { CreatorFormatter } from "#/ui/table/formatters/creator-formatter";
import { SourceFormatter } from "#/ui/table/formatters/source-formatter";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { GroupKeyFormatterWithCell } from "#/ui/table/formatters/group-key-formatter";
import {
  ComputedDurationMetricFields,
  ComputedTokenMetricFields,
} from "@braintrust/local/query";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { getProjectLink } from "./getProjectLink";
import { BasicTooltip } from "#/ui/tooltip";
import { TagsFormatterFactory } from "#/ui/trace/tags";

export function makeExperimentsFormatters({
  scoreFields,
  groupAggregationTypes,
  setGroupAggregationType,
  isGrouping,
  colorMap,
  latestExperimentId,
  baselineExperimentId,
  config,
  onTagClick,
}: {
  scoreFields: string[];
  groupAggregationTypes: Record<string, AggregationType>;
  setGroupAggregationType: (scoreAlias: string, value: AggregationType) => void;
  isGrouping: boolean;
  colorMap?: Record<string, number>;
  latestExperimentId?: string;
  baselineExperimentId?: string;
  config?: {
    tags?: Array<{
      name: string;
      id: string;
      project_id: string;
      user_id: string;
      [key: string]: unknown;
    }>;
  };
  onTagClick?: (tag: string) => void;
}): FormatterMap {
  const aggregationProps = {
    groupAggregationTypes,
    setGroupAggregationType,
    isGrouping,
    scoreNames: scoreFields,
  };

  return makeFormatterMap({
    name: {
      ...GroupKeyFormatterWithCell({
        Formatter: (props) => {
          const isLatestExperiment =
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- row.original is not typed
            (props.cell.row.original as { id: string }).id ===
            latestExperimentId;
          const isBaselineExperiment =
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- row.original is not typed
            (props.cell.row.original as { id: string }).id ===
            baselineExperimentId;
          return (
            <div className="flex items-center">
              <DefaultFormatter {...props} className="flex-1 truncate" />
              {isLatestExperiment && <LatestObjectBadge />}
              {isBaselineExperiment && <BaselineObjectBadge isDefault />}
            </div>
          );
        },
        groupKey: "name",
        colorMap,
      }),
      pinnedColumnIndex: 1,
    },
    creator: {
      cell: CreatorFormatter,
    },
    source: {
      cell: SourceFormatter,
    },
    last_updated: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      cell: (props: FormatterProps<{ [BT_IS_GROUP]?: boolean }, any>) => (
        <DefaultFormatter
          {...props}
          className={cn({
            "px-3 font-medium": props.cell.row.original[BT_IS_GROUP],
          })}
        />
      ),
    },
    metadata: {
      cell: ({ value, hideNulls, renderForTooltip }) =>
        renderForTooltip ? (
          renderForTooltip(value ?? "")
        ) : (
          <span className="font-mono text-xs">
            {value?.slice?.(0, 1000) ??
              (hideNulls ? (
                ""
              ) : (
                <span className="font-inter text-sm">
                  <NullFormatter />
                </span>
              ))}
          </span>
        ),
    },
    error_rate: PercentWithAggregationFormatter(aggregationProps),
    num_errors: DefaultWithAggregationFormatter(aggregationProps),
    num_examples: DefaultWithAggregationFormatter(aggregationProps),
    ...Object.fromEntries(
      ComputedDurationMetricFields.map((f) => [
        f,
        {
          ...DurationWithAggregationFormatter(aggregationProps),
          headerLabel: HeaderAliases[f] + " (avg)",
        },
      ]),
    ),
    ...Object.fromEntries(
      ComputedTokenMetricFields.map((f) => [
        f,
        {
          ...DefaultWithAggregationFormatter(aggregationProps),
          headerLabel: HeaderAliases[f] + " (avg)",
        },
      ]),
    ),
    ...Object.fromEntries(
      scoreFields.map((f) => [
        f,
        {
          ...PercentWithAggregationFormatter(aggregationProps),
          headerIcon: PercentIcon,
        },
      ]),
    ),
    tags: {
      cell: TagsFormatterFactory({
        tagConfig: config?.tags ?? [],
        onTagClick,
      }),
    },
  });
}

export const useProjectRowEvents = (
  params:
    | {
        entity: "experiment" | "dataset";
        entityNameProp: string;
        orgName: string;
        projectName: string;
      }
    | {
        entity: "project";
        entityNameProp: string;
        orgName: string;
        // We include this so that the hooks linter and typechecker are satisfied that
        // `params.projectName` always exists.
        projectName?: undefined;
      }
    | {
        entity: "playground";
        entityNameProp: string;
        orgName: string;
        projectName: string;
      },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
): Record<string, (row: Row<any>) => MouseEventHandler> => {
  const router = useRouter();

  const getEntityLink = useCallback(
    (name: string) => {
      if (params.entity === "experiment") {
        return getExperimentLink({
          orgName: params.orgName,
          projectName: params.projectName,
          experimentName: name,
        });
      }
      if (params.entity === "dataset") {
        return getDatasetLink({
          orgName: params.orgName,
          projectName: params.projectName,
          datasetName: name,
        });
      }
      if (params.entity === "project") {
        return getProjectLink({
          orgName: params.orgName,
          projectName: name,
        });
      }
      if (params.entity === "playground") {
        return getPlaygroundLink({
          orgName: params.orgName,
          projectName: params.projectName,
          playgroundName: name,
        });
      }
      return "";
    },
    [params.entity, params.orgName, params.projectName],
  );

  return useMemo(
    () => ({
      onClick: (row) => (e) => {
        // Check for text selection. If the user has highlighted text, don't open the clicked row.
        if (window.getSelection()?.toString()) {
          return;
        }

        // Open row
        const entityName = row.original[params.entityNameProp];
        const link = getEntityLink(entityName);
        if (e.ctrlKey || e.metaKey || e.button === 1) {
          window.open(link, "_blank");
        } else {
          router.push(link);
        }
      },
      onAuxClick: (row) => () => {
        const entityName = row.original[params.entityNameProp];
        const link = getEntityLink(entityName);
        window.open(link, "_blank");
      },
    }),
    [getEntityLink, params.entityNameProp, router],
  );
};

export const LatestObjectBadge = ({ className }: { className?: string }) => (
  <span
    className={cn(
      "ml-2 inline-block flex-none rounded-xs bg-accent-500/10 px-1 py-0.5 text-[10px] leading-normal font-semibold text-accent-500 uppercase",
      className,
    )}
  >
    Latest
  </span>
);

export const BaselineObjectBadge = ({
  className,
  isDefault,
}: {
  className?: string;
  isDefault?: boolean;
}) => (
  <BasicTooltip
    content={
      isDefault
        ? "This experiment was set as the default comparison experiment"
        : "This experiment was set as the comparison experiment via the SDK"
    }
  >
    <span
      className={cn(
        "ml-2 inline-block flex-none rounded-xs bg-violet-500/10 px-1 py-0.5 text-[10px] leading-normal font-semibold text-violet-500 uppercase",
        className,
      )}
    >
      {isDefault ? "Default Baseline" : "Baseline"}
    </span>
  </BasicTooltip>
);
