import ClientPage, { type Params } from "./clientpage";
import { type Metadata, type ResolvingMetadata } from "next";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { cookies } from "next/headers";
import { type LogsPanelLayout } from "#/ui/logs-viewer";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { type Permission } from "@braintrust/typespecs";
import { getProjectId } from "../project-actions";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const projectId = await getProjectId({
    org_name: decodeURIComponentPatched(params.org),
    project_name: decodeURIComponentPatched(params.project),
  });

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#server-component
  const layout = (await cookies()).get("react-resizable-panels:logs-layout");

  const defaultPanelLayout: LogsPanelLayout = {
    main: 80,
    trace: 20,
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  let projectLogsPermissions: Permission[] = [];
  try {
    const permissions = await getObjectAclPermissions({
      objectType: "project_log",
      objectId: projectId ?? undefined,
    });
    projectLogsPermissions = permissions ?? [];
  } catch (e) {
    console.error("Failed to get projectLogs permissions", e);
  }

  return (
    <ClientPage
      params={params}
      defaultPanelLayout={defaultPanelLayout}
      permissions={projectLogsPermissions}
    />
  );
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Logs",
    sections: [projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/logs`,
    ogTemplate: "product",
  });
}
