import { EntityContextMenu } from "#/ui/entity-context-menu";
import { type GetRowsForExportFn } from "../experiments/[experiment]/(queries)/table-queries";
import { ReviewButton } from "../review-button";
import { toast } from "sonner";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";
import { useState } from "react";
import { ConfigureOnlineScoringRuleDialog } from "./configure-online-scoring-rule-dialog";
import { OnlineScorersCombobox } from "#/ui/trace/online-scorers";
import { type DiscriminatedProjectScore } from "@braintrust/local/query";
import { AsyncScoringDialog } from "./async-scoring-dialog";
import { getCreatedTimeRangeLabelFromBtql } from "#/utils/btql/btql-parsing-helpers";
import { MAX_LOGS_SCORING_LIMIT } from "#/utils/btapi/scoring";
import { type DataObjectSearch } from "#/utils/btapi/btapi";

export const LogsHeader = ({
  projectId,
  projectName,
  orgName,
  getRowsForExport,
  filters,
  hideActions,
  columnVisibility,
  onBTQLFilter,
  isReadOnly,
}: {
  projectId: string;
  projectName: string;
  orgName: string;
  getRowsForExport: GetRowsForExportFn;
  filters: DataObjectSearch["filters"];
  hideActions?: boolean;
  columnVisibility?: Record<string, boolean>;
  onBTQLFilter?: (filterText: string) => void;
  isReadOnly?: boolean;
}) => {
  const isLoopEnabled = useIsLoopEnabled();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isAsyncDialogOpen, setIsAsyncDialogOpen] = useState(false);
  const [editingRule, setEditingRule] =
    useState<DiscriminatedProjectScore | null>(null);

  return (
    <div className="flex items-center gap-2 bg-primary-50 px-3 pt-1 pb-2">
      <h1 className="text-base font-semibold">Logs</h1>
      <EntityContextMenu
        objectType="project_log"
        objectId={projectId ?? ""}
        objectName={projectName}
        orgName={orgName}
        projectName={projectName}
        buttonClassName="h-7 w-7"
        getRowsForExport={getRowsForExport}
        exportName={[projectName, "logs"].join(" ")}
        onlyExportingLoadedRows={true}
        columnVisibility={columnVisibility}
        handleCopyId={() => {
          navigator.clipboard.writeText(projectId ?? "");
          toast("Project ID copied to clipboard");
        }}
        isReadOnly={isReadOnly}
      />
      <div className="grow" />
      {!hideActions && (
        <>
          <ReviewButton />
          <OnlineScorersCombobox
            canAddRule
            onCreateRule={() => setIsCreateDialogOpen(true)}
            onAsyncRule={() => setIsAsyncDialogOpen(true)}
            onEditRule={(rule) => {
              setEditingRule(rule);
              setIsCreateDialogOpen(true);
            }}
          />
          {isLoopEnabled && (
            <OptimizationChat
              hasMultipleSelectedExperiments={false}
              onBTQLFilter={onBTQLFilter}
            />
          )}
        </>
      )}
      <ConfigureOnlineScoringRuleDialog
        open={isCreateDialogOpen}
        onOpenChange={(open) => {
          setIsCreateDialogOpen(open);
          if (!open) {
            setEditingRule(null);
          }
        }}
        selectedRule={editingRule}
        showDelete={true}
      />
      <AsyncScoringDialog
        mode="filtered"
        open={isAsyncDialogOpen}
        onClose={() => {
          setIsAsyncDialogOpen(false);
        }}
        onOpenChange={(open) => {
          setIsAsyncDialogOpen(open);
          if (!open) {
            setEditingRule(null);
          }
        }}
        preSelectedScorerId={editingRule?.id}
        filters={filters}
        title="Apply scorers to filtered logs"
        description={`Choose scorers to apply to the ${MAX_LOGS_SCORING_LIMIT}  most recent logs in the filtered view`}
        infoBannerContent={`Scores will apply to the ${MAX_LOGS_SCORING_LIMIT} most recent filtered logs${
          getCreatedTimeRangeLabelFromBtql(filters?.btql)
            ? ` from ${getCreatedTimeRangeLabelFromBtql(filters?.btql)}`
            : ""
        }. To score fewer logs, add filters in the logs table or manually select logs and use the "Scorers" button.`}
      />
    </div>
  );
};
