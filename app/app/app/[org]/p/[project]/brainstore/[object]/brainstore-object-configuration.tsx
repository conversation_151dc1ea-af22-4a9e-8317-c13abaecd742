"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useCallback, useContext, useMemo, useRef, useState } from "react";
import { apiPost } from "#/utils/btapi/fetch";
import { zodErrorToString } from "#/utils/validation";
import {
  backfillableObjectTypeSchema,
  brainstoreOptimizationResultSchema,
  type BrainstoreOptimizationStep,
  type LastIndexOperation,
  objectInfoWithBackfillStatusSchema,
} from "@braintrust/local/app-schema";
import { parseTransactionBigInt } from "#/utils/transactions";
import { relativeTimeMs, smartTimeFormat } from "#/ui/date";
import {
  ConfigurationTable as Table,
  ConfigurationTableBody as TableBody,
  ConfigurationTableCell as TableCell,
  ConfigurationTableHead as TableHead,
  ConfigurationTableHeader as TableHeader,
  ConfigurationTableRow as TableRow,
} from "#/ui/configuration-table";
import { useOrg } from "#/utils/user";
import { Button } from "#/ui/button";
import { toast } from "sonner";
import { useSessionToken } from "#/utils/auth/session-token";
import { runEnableBackfilling } from "../ops";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { useFeatureFlags } from "#/lib/feature-flags";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "#/ui/dialog";
import { DialogDescription } from "@radix-ui/react-dialog";
import {
  BarChart2,
  CheckCircle2,
  Cog,
  ExternalLinkIcon,
  InfoIcon,
  RefreshCw,
  Search,
  ShieldAlert,
  TriangleAlert,
} from "lucide-react";
import { BasicTooltip } from "#/ui/tooltip";
import { isEmpty } from "#/utils/object";
import { numberWithCommas } from "#/ui/table/formatters/default-formatter";
import { useGetRequest } from "#/utils/btapi/get";
import { z } from "zod";
import { cn } from "#/utils/classnames";
import pluralize from "pluralize";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { ExternalLink } from "#/ui/link";
import { useIsAdmin } from "#/app/app/[org]/pathname-checker";

type SegmentRow = {
  segmentId: string;
  meta: z.infer<typeof objectInfoWithBackfillStatusSchema>["segments"][string];
};

const columnHelper = createColumnHelper<SegmentRow>();

export const BrainstoreObjectConfiguration = ({
  objectId,
  onOpenChange,
  apiUrl,
}: {
  objectId: string;
  onOpenChange: (open: boolean) => void;
  apiUrl?: string;
}) => {
  const { projectId } = useContext(ProjectContext);
  const org = useOrg();
  const {
    flags: { brainstore },
  } = useFeatureFlags();

  const [search, setSearch] = useState("");
  const setSearchDebounced = useDebouncedCallback(setSearch, 200);

  const {
    data,
    error: requestError,
    errorCode: requestErrorCode,
    refresh: refreshStatus,
  } = useGetRequest(
    `/brainstore/backfill/status/object/${encodeURIComponent(objectId)}`,
    useMemo(() => ({}), []),
    {
      apiUrl,
    },
  );

  const [parsedData, parseError] = useMemo(() => {
    if (!data || !brainstore) {
      return [null, null];
    }

    const status = objectInfoWithBackfillStatusSchema.safeParse(data);
    if (status.success) {
      return [status.data, null];
    } else {
      return [null, zodErrorToString(status.error, 2, true)];
    }
  }, [data, brainstore]);

  const sortedSegments = useMemo(() => {
    if (!parsedData?.segments) {
      return [];
    }

    let entries = Object.entries(parsedData.segments);

    if (search) {
      const searchLower = search.toLowerCase();
      entries = entries.filter(([segmentId, meta]) => {
        const searchData = {
          segmentId,
          last_index_error: meta.last_index_operation?.error,
        };
        const segmentText = JSON.stringify(searchData).toLowerCase();
        return segmentText.includes(searchLower);
      });
    }

    const segmentsWithId = entries.map(([segmentId, meta]) => ({
      segmentId,
      meta,
    }));

    segmentsWithId.sort(
      (a, b) =>
        // We want the highest pagination key to be first.
        a.meta.minimum_pagination_key.localeCompare(
          b.meta.minimum_pagination_key,
        ) * -1,
    );

    return segmentsWithId;
  }, [parsedData?.segments, search]);

  const totalChunks = useMemo(() => {
    return sortedSegments.reduce(
      (acc, { meta }) =>
        acc +
        (meta.last_compacted_index_meta?.storage_meta.segments?.length ?? 0),
      0,
    );
  }, [sortedSegments]);

  const totalRows = useMemo(() => {
    return sortedSegments.reduce((acc, { meta }) => acc + meta.num_rows, 0);
  }, [sortedSegments]);

  const { getOrRefreshToken } = useSessionToken();

  const enableBackfilling = useCallback(async () => {
    const sessionToken = await getOrRefreshToken();
    if (!org.api_url) {
      toast.error(`Not logged in (yet)`);
      return;
    }
    if (!projectId) {
      toast.error(`No project id`);
      return;
    }

    const objectType = backfillableObjectTypeSchema.parse(
      objectId.split(":")[0],
    );

    try {
      await runEnableBackfilling({
        apiUrl: org.api_url,
        objectTypes: [objectType],
        projectId,
        sessionToken,
      });

      refreshStatus();
    } catch (e) {
      toast.error("Failed to enable Brainstore", {
        description: `${e}`,
      });
      return;
    }
  }, [org.api_url, objectId, projectId, refreshStatus, getOrRefreshToken]);

  const optimizeObject = useCallback(
    async (segmentIds: string[] | null, recompact: boolean) => {
      const sessionToken = await getOrRefreshToken();
      if (!org.api_url) {
        toast.error(`Not logged in (yet)`);
        return;
      }

      try {
        const result = await apiPost({
          url: `${org.api_url}/brainstore/backfill/optimize`,
          sessionToken,
          payload: {
            object_id: objectId,
            segment_ids: segmentIds ?? undefined,
            recompact,
          },
          alreadySerialized: false,
        });

        if (!result.ok) {
          const errorText = await result.text();
          throw new Error(errorText);
        }

        const steps = brainstoreOptimizationResultSchema.safeParse(
          await result.json(),
        );

        if (steps.success) {
          toast.success("Optimization started", {
            description: (
              <>
                <div>
                  This process may take up to an hour (
                  {steps.data.planned.length}{" "}
                  {pluralize("step", steps.data.planned.length)} planned).
                </div>
                <div className="mt-2">
                  <OptimizationSteps steps={steps.data.planned} />
                </div>
              </>
            ),
          });
        } else {
          toast.success("Optimization started", {
            description: "This process may take up to an hour",
          });
        }
      } catch (e) {
        toast.error("Optimization failed", { description: `${e}` });
        return;
      }

      refreshStatus();
    },
    [getOrRefreshToken, org.api_url, refreshStatus, objectId],
  );

  const error = requestError ?? parseError;

  const missingStats =
    parsedData &&
    !Object.values(parsedData.segments).every(
      (segment) =>
        segment.stats?.["_pagination_key"]?.min &&
        segment.stats?.["_pagination_key"]?.max,
    );

  const columns = useMemo(
    () => [
      columnHelper.accessor("segmentId", {
        header: "Segment ID",
        cell: (info) => <div className="font-mono">{info.getValue()}</div>,
        size: 384, // w-96
      }),
      columnHelper.accessor("meta", {
        id: "range",
        header: "Range",
        cell: (info) => {
          const meta = info.getValue();
          return (
            <PaginationKeyRange
              minimumPaginationKey={
                meta.stats?.["_pagination_key"]?.min ??
                meta.minimum_pagination_key
              }
              maximumPaginationKey={meta.stats?.["_pagination_key"]?.max}
            />
          );
        },
        size: 384, // w-96
      }),
      columnHelper.accessor((row) => row.meta.num_rows, {
        header: "Rows",
        cell: (info) => info.getValue(),
        size: 64, // w-16
      }),
      columnHelper.accessor("meta", {
        id: "compactionLag",
        header: "Compaction lag",
        cell: (info) => {
          const meta = info.getValue();
          return (
            <CompactionLag
              lastCompactXactId={
                meta.last_compacted_index_meta?.xact_id ?? null
              }
              lastProcessedXactId={meta.last_processed_xact_id ?? null}
            />
          );
        },
        size: 160, // w-40
      }),
      columnHelper.accessor(
        (row) =>
          row.meta.last_compacted_index_meta?.storage_meta.segments.length ?? 0,
        {
          header: "Chunks",
          cell: (info) => info.getValue(),
          size: 128, // w-32
        },
      ),
      columnHelper.accessor("meta", {
        id: "status",
        header: "Status",
        cell: (info) => {
          const meta = info.getValue();
          return meta.last_index_operation ? (
            <StatusCell status={meta.last_index_operation} />
          ) : (
            <div className="text-xs text-primary-600">Indexing not started</div>
          );
        },
        size: 192, // w-48
      }),
      columnHelper.display({
        id: "actions",
        header: "",
        cell: (info) => {
          const segmentId = info.row.original.segmentId;
          return (
            <div className="flex gap-2">
              <Button
                size="xs"
                onClick={() => {
                  optimizeObject([segmentId], false);
                }}
                isLoading={false}
              >
                Optimize
              </Button>
              <Button
                size="xs"
                onClick={() => {
                  optimizeObject([segmentId], true);
                }}
                isLoading={false}
              >
                Recompact
              </Button>
            </div>
          );
        },
        size: 192, // w-48 flex-1
      }),
    ],
    [optimizeObject],
  );

  const table = useReactTable({
    data: sortedSegments,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const tableContainerRef = useRef<HTMLDivElement>(null);

  const { rows } = table.getRowModel();
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 48, // Estimate row height
    overscan: 5,
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  const paddingTop =
    virtualRows.length > 0 ? (virtualRows?.[0]?.start ?? 0) : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? totalSize - (virtualRows?.[virtualRows.length - 1]?.end ?? 0)
      : 0;

  const isAdmin = useIsAdmin();

  return (
    <Dialog open onOpenChange={onOpenChange}>
      <DialogContent
        className="flex flex-col pb-0 sm:min-h-[600px] sm:max-w-[calc(100vw-2rem)]"
        hideCloseButton
      >
        {requestErrorCode === 403 ? (
          <TableEmptyState
            Icon={ShieldAlert}
            label={`You do not have permissions to manage Brainstore for this project. Please contact your organization administrator for assistance.`}
          />
        ) : (
          <>
            <DialogHeader className="flex flex-none flex-row items-center gap-2">
              <DialogTitle className="flex-1">
                Brainstore backfill status
              </DialogTitle>
              <DialogDescription />
              <Button
                size="xs"
                className="flex-none"
                onClick={() => refreshStatus()}
              >
                <RefreshCw className="size-3" />
                Refresh
              </Button>
              <Button
                size="xs"
                className="flex-none"
                onClick={() => optimizeObject(null, false)}
              >
                <Cog className="size-3" />
                Optimize all segments
              </Button>
              {parsedData && !parsedData.enabled && (
                <Button
                  size="xs"
                  variant="primary"
                  className="flex-none"
                  onClick={enableBackfilling}
                >
                  Enable Brainstore
                </Button>
              )}
            </DialogHeader>
            {error && (
              <div className="mb-2 flex flex-none items-center gap-1.5 rounded-md border border-rose-500/10 bg-rose-500/5 p-2 font-mono text-xs font-semibold text-bad-700">
                {`${error}`}
              </div>
            )}
            {parsedData?.enabled && (
              <div className="mb-2 flex-none">
                <div className="text-lg font-medium tabular-nums">
                  {Math.round((parsedData.estimated_progress ?? 0) * 10000) /
                    100}
                  % complete
                </div>
                <div className="text-xs text-primary-600">
                  Last backfilled{" "}
                  {parsedData?.last_backfilled_ts ? (
                    smartTimeFormat(
                      new Date(parsedData.last_backfilled_ts).getTime(),
                    )
                  ) : (
                    <NullFormatter />
                  )}
                  {parsedData?.completed_initial_backfill_ts && (
                    <BasicTooltip
                      tooltipContent={`Initial backfill completed ${smartTimeFormat(
                        new Date(
                          parsedData.completed_initial_backfill_ts,
                        ).getTime(),
                      )}.`}
                      side="right"
                    >
                      <span className="ml-1 inline-flex translate-y-[2px] items-center gap-1 rounded-full px-1.5 text-xs font-medium text-good-600">
                        <CheckCircle2 className="size-3" />
                        Live
                      </span>
                    </BasicTooltip>
                  )}
                  {missingStats && (
                    <BasicTooltip
                      tooltipContent={`Some segments are missing information about their minimum and maximum pagination key range. These will be computed the next time they are compacted, or you can trigger them with the Optimize button.`}
                      side="right"
                    >
                      <span className="inline-flex translate-y-[2px] items-center gap-1 rounded-full px-1.5 text-xs font-medium text-amber-600 dark:text-amber-400">
                        <BarChart2 className="size-3" />
                        Missing stats
                      </span>
                    </BasicTooltip>
                  )}
                </div>
                <div className="mt-2 text-sm text-primary-600">
                  <BasicTooltip
                    tooltipContent={
                      "The number of chunks is the number of physical index files across segments. Over time, each segment will be compacted to have 1-3 chunks."
                    }
                  >
                    <span>
                      {sortedSegments.length} segments /{" "}
                      <span>
                        {numberWithCommas(totalChunks)} total{" "}
                        {pluralize("chunk", totalChunks)}
                      </span>{" "}
                      / {numberWithCommas(totalRows)}{" "}
                      {pluralize("row", totalRows)}
                    </span>
                  </BasicTooltip>
                  {isAdmin && (
                    <span className="ml-2">
                      <ExternalLink
                        href={`https://us5.datadoghq.com/apm/traces?query=env%3Aproduction%20%40btql.object_id%3A${encodeURIComponent(objectId.split(":")[1])}&agg_m=count&agg_m_source=base&agg_t=count&cols=core_service%2Ccore_resource_name%2Clog_duration%2Clog_http.method%2Clog_http.status_code&fromUser=false&historicalData=true&messageDisplay=inline&sort=desc&spanType=all&storage=hot&view=spans`}
                      >
                        <ExternalLinkIcon className="inline-block size-3" />{" "}
                        Recent queries in Datadog
                      </ExternalLink>
                    </span>
                  )}
                </div>
              </div>
            )}

            <div className="relative flex flex-none">
              <Search className="pointer-events-none absolute top-[8px] left-2 size-3 text-primary-500" />
              <PlainInput
                placeholder="Find segment"
                onChange={(e) => setSearchDebounced(e.target.value)}
                className="h-7 flex-1 border-0 bg-transparent pl-7 outline-hidden transition-all hover:bg-primary-100 focus:bg-primary-100"
              />
            </div>

            <div
              ref={tableContainerRef}
              className="-mx-6 w-auto flex-1 overflow-auto px-6 pb-6"
            >
              <Table>
                <TableHeader className="sticky top-0 z-10 bg-background">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          style={{ width: header.getSize() }}
                          className="text-xs"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody
                  style={{ height: `${totalSize}px`, position: "relative" }}
                >
                  {paddingTop > 0 && (
                    <TableRow style={{ height: `${paddingTop}px` }} />
                  )}
                  {virtualRows.map((virtualRow) => {
                    const row = rows[virtualRow.index];
                    return (
                      <TableRow
                        key={row.id}
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: `${virtualRow.size}px`,
                          transform: `translateY(${virtualRow.start}px)`,
                        }}
                        className="py-2"
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            key={cell.id}
                            style={{ width: cell.column.getSize() }}
                            className={cn(
                              cell.column.id === "segmentId" && "font-mono",
                            )}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    );
                  })}
                  {paddingBottom > 0 && (
                    <TableRow style={{ height: `${paddingBottom}px` }} />
                  )}
                </TableBody>
              </Table>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export function StatusCell({ status }: { status: LastIndexOperation }) {
  const lastOp: "compact" | "merge" | null = !status.details
    ? null
    : "compact" in status.details
      ? "compact"
      : "merge";

  const lastOpUpdated = status.last_updated
    ? relativeTimeMs(
        new Date().getTime() - new Date(status.last_updated).getTime(),
      )
    : null;

  const lastOpDuration =
    status.start && status.last_updated
      ? new Date(status.last_updated).getTime() -
        new Date(status.start).getTime()
      : null;

  const isStale =
    !status.finished &&
    status.last_updated &&
    Date.now() - new Date(status.last_updated).getTime() >=
      3600 * 1000; /* 1 hour */

  const timingInfo = (
    <span>
      {lastOpUpdated ? (
        <span>
          Operation ran {lastOpUpdated} ago. <br />
        </span>
      ) : null}
      {lastOpDuration ? (
        <span>
          Took {relativeTimeMs(lastOpDuration)} to complete. <br />
        </span>
      ) : null}
    </span>
  );

  if (!lastOp) {
    return "-";
  } else if (status.finished && !status.error) {
    return (
      <div className="text-xs">
        <BasicTooltip
          tooltipContent={timingInfo}
          disableHoverableContent={false}
        >
          <div className="flex items-center gap-1">
            <div>{lastOp === "compact" ? "Compacted" : "Merged"}</div>
            <InfoIcon className="size-3" />
          </div>
        </BasicTooltip>
      </div>
    );
  } else if (status.error) {
    return (
      <BasicTooltip
        disableHoverableContent={false}
        tooltipContent={
          <span>
            <span className="text-bad-600">{status.error}</span>
            <br />
            <br />
            {timingInfo}
          </span>
        }
      >
        <div className="flex items-center gap-1">
          <div className="text-xs text-bad-600">
            {lastOp === "compact" ? "Compaction" : "Merge"} failed
          </div>
          <InfoIcon className="size-3 text-bad-600" />
        </div>
      </BasicTooltip>
    );
  } else if (isStale) {
    return (
      <BasicTooltip
        disableHoverableContent={false}
        tooltipContent={
          <span>
            Status: {status.stage}
            <br />
            {status.start ? (
              <span>
                Started{" "}
                {relativeTimeMs(
                  new Date().getTime() - new Date(status.start).getTime(),
                )}{" "}
                ago. <br />
              </span>
            ) : null}
            {lastOpUpdated ? (
              <span>
                Last updated {lastOpUpdated} ago. <br />
              </span>
            ) : null}
          </span>
        }
      >
        <div className="flex items-center gap-1">
          <div className="text-xs text-amber-600 dark:text-amber-400">
            {lastOp === "compact" ? "Compaction" : "Merge"} stalled
            {!isEmpty(status.estimated_progress)
              ? ` (${Math.round(status.estimated_progress * 10000) / 100}%)`
              : ""}
          </div>
          <TriangleAlert className="size-3 text-amber-600 dark:text-amber-400" />
        </div>
      </BasicTooltip>
    );
  } else if (!status.finished) {
    return (
      <BasicTooltip
        disableHoverableContent={false}
        tooltipContent={
          <span>
            Status: {status.stage}
            <br />
            {status.start ? (
              <span>
                Started{" "}
                {relativeTimeMs(
                  new Date().getTime() - new Date(status.start).getTime(),
                )}{" "}
                ago. <br />
              </span>
            ) : null}
            {lastOpUpdated ? (
              <span>
                Last updated {lastOpUpdated} ago. <br />
              </span>
            ) : null}
          </span>
        }
      >
        <div className="flex items-center gap-1">
          <div className="text-xs text-good-600">
            {lastOp === "compact" ? "Compaction" : "Merge"} running
            {!isEmpty(status.estimated_progress)
              ? ` (${Math.round(status.estimated_progress * 10000) / 100}%)`
              : ""}
          </div>
          <InfoIcon className="size-3 text-good-600" />
        </div>
      </BasicTooltip>
    );
  } else {
    return (
      <BasicTooltip
        disableHoverableContent={false}
        tooltipContent={<span>{JSON.stringify(status, null, 2)}</span>}
      >
        <div className="text-xs text-primary-600">Unknown status</div>
      </BasicTooltip>
    );
  }
}

export function PaginationKeyRange({
  minimumPaginationKey,
  maximumPaginationKey,
}: {
  minimumPaginationKey: string;
  maximumPaginationKey: string | undefined;
}) {
  const minimum = parsePaginationKey(minimumPaginationKey);
  const maximum = maximumPaginationKey
    ? parsePaginationKey(maximumPaginationKey)
    : undefined;

  return (
    <BasicTooltip
      disableHoverableContent={false}
      tooltipContent={
        !maximum
          ? "This segment is missing information about its minimum and maximum pagination key range. These will be computed the next time it is compacted, or you can trigger them with the Optimize button."
          : `This segment contains rows that were originally created from ${minimum} to ${maximum}.`
      }
      side="right"
    >
      <div className="flex items-center gap-1">
        <div>
          {minimum && maximum
            ? `${minimum} - ${maximum}`
            : minimum
              ? minimum
              : "-"}
        </div>
        {!maximum && (
          <BarChart2
            className={cn("size-3", "text-amber-600 dark:text-amber-400")}
          />
        )}
      </div>
    </BasicTooltip>
  );
}

function CompactionLag({
  lastCompactXactId,
  lastProcessedXactId,
}: {
  lastCompactXactId: string | null;
  lastProcessedXactId: string | null;
}) {
  const isBad = useMemo(() => {
    if (!lastCompactXactId || !lastProcessedXactId) {
      return null;
    }
    const { date: lastCompactDate } = parseTransactionBigInt(lastCompactXactId);
    const { date: lastProcessedDate } =
      parseTransactionBigInt(lastProcessedXactId);
    const diffSeconds =
      (lastProcessedDate.getTime() - lastCompactDate.getTime()) / 1000;

    return diffSeconds < 60 ? "good" : diffSeconds < 600 ? "warning" : "bad";
  }, [lastCompactXactId, lastProcessedXactId]);

  return (
    <BasicTooltip
      tooltipContent={
        lastCompactXactId
          ? `The last compacted row is from
${xactToSmartTime(lastCompactXactId)}.`
          : "No compacted rows yet."
      }
    >
      <div
        className={cn(
          "flex items-center gap-1",
          isBad === "warning" && "text-amber-600 dark:text-amber-400",
          isBad === "bad" && "text-bad-600",
        )}
      >
        {xactRelativeGap(lastCompactXactId, lastProcessedXactId)}
        <InfoIcon className="size-3" />
      </div>
    </BasicTooltip>
  );
}

export function xactToSmartTime(xactId: string | null) {
  if (!xactId) {
    return "-";
  }
  const { date } = parseTransactionBigInt(xactId);
  return smartTimeFormat(date.getTime(), { includeTimeZone: true });
}

function xactRelativeGap(a: string | null, b: string | null) {
  if (!a || !b) {
    return "-";
  }
  const aDate = parseTransactionBigInt(a).date;
  const bDate = parseTransactionBigInt(b).date;
  return relativeTimeMs(aDate.getTime() - bDate.getTime());
}

function parsePaginationKey(paginationKey: string) {
  if (!paginationKey.startsWith("p")) {
    return null;
  }
  // This inverts the logic in api-ts/src/brainstore/wal.ts
  const paginationKeyNumber = z.coerce.bigint().parse(paginationKey.slice(1));
  const approxXactId =
    ((paginationKeyNumber >> BigInt(32)) & BigInt("0xffffffff")) << BigInt(16);
  const { date } = parseTransactionBigInt(approxXactId.toString());
  return smartTimeFormat(date.getTime(), { includeTimeZone: true });
}

function OptimizationSteps({ steps }: { steps: BrainstoreOptimizationStep[] }) {
  return (
    <div>
      {steps.map((step) => (
        <div key={step.segment_id}>{formatStep(step)}</div>
      ))}
    </div>
  );
}

function formatStep(step: BrainstoreOptimizationStep) {
  const actions = [];
  if (step.compact) {
    actions.push("compact");
  }
  if (step.merge) {
    actions.push("merge");
  }

  return (
    <div className="mt-2 flex flex-col">
      <div className="font-mono font-semibold">{step.segment_id}</div>
      <div className="text-xs text-primary-600">{actions.join(" and ")}</div>
    </div>
  );
}
