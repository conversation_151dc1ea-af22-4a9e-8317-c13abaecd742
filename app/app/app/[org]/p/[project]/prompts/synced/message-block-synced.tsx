import { But<PERSON> } from "#/ui/button";
import TextEditor, { type TextEditorProps } from "#/ui/text-editor";
import { type ContentPart, type Content } from "@braintrust/typespecs";
import { memo, useCallback, useMemo, useRef, useEffect } from "react";
import { File, MessageSquare, Minus, Paperclip } from "lucide-react";
import { ImagePartThumb } from "../ImagePartThumb";
import { BasicTooltip } from "#/ui/tooltip";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { useFeatureFlags } from "#/lib/feature-flags";
import { type TransactionId } from "braintrust/util";
import { useSyncedPrompts } from "./use-synced-prompts";
import { uploadAttachment } from "#/utils/btapi/attachment";
import { FileInputButton } from "#/ui/file-input-button";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { toast } from "sonner";
import { useFocusManager } from "@react-aria/focus";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useContext } from "react";
import { ProjectContext } from "../../projectContext";

export interface MessageBlockProps {
  data?: Content;
  isMultimodal: boolean;
  textPlaceholder?: string;
  triggerSave: () => Promise<TransactionId | null>;
  runPrompts: () => void;
  extensions: TextEditorProps["extensions"];
  isReadOnly?: boolean;
  copilotContext?: CopilotContextBuilder;
  promptId: string;
  messageIdx: number;
  playgroundId?: string; // The actual playground ID (promptSessionId)
  messageRole?: string; // system | user | assistant | tool
}

export type PartType = ContentPart["type"];

const MessageBlockSynced = memo(
  ({
    data,
    isMultimodal,
    textPlaceholder,
    triggerSave,
    runPrompts,
    extensions,
    isReadOnly,
    copilotContext,
    promptId,
    messageIdx,
    playgroundId,
    messageRole,
  }: MessageBlockProps) => {
    const { removeMessagePart, updateMessagePart_NO_SAVE, focusedEditor } =
      useSyncedPrompts();

    const parts: ContentPart[] = useMemo(
      () => (Array.isArray(data) ? data : [{ type: "text", text: data ?? "" }]),
      [data],
    );

    const focusManager = useFocusManager();

    // Analytics tracking
    const { track } = useAppAnalytics();
    const projectContext = useContext(ProjectContext);
    const projectId = projectContext.projectId;

    // Track original values for analytics
    const originalValuesRef = useRef<Map<string, string>>(new Map());
    const lastChangeTimeRef = useRef<Map<string, number>>(new Map());
    const debounceTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

    // Track prompt changes when user finishes editing (onBlur or save)
    const trackPromptChange = useCallback(
      (partIndex: number) => {
        const key = `${promptId}-${messageIdx}-${partIndex}`;
        const originalValue = originalValuesRef.current.get(key) || "";
        const currentValue =
          parts[partIndex]?.type === "text"
            ? parts[partIndex].text
            : parts[partIndex]?.type === "image_url"
              ? parts[partIndex].image_url.url
              : "";

        // Track analytics if content changed
        if (originalValue !== currentValue && playgroundId && projectId) {
          track("playgroundEdit", {
            playgroundId,
            projectId,
            taskId: promptId,
            triggerEntity: "human",
            entryPoint: "promptEditor",
            editType: "promptChanged",
            previousValue: originalValue.length.toString(),
            newValue: currentValue.length.toString(),
            details: {
              messageIndex: messageIdx,
              partIndex,
              contentLength: currentValue.length,
              messageRole,
            },
            source: "web",
          });
        }

        // Clear the original value after tracking
        originalValuesRef.current.delete(key);
        lastChangeTimeRef.current.delete(key);

        // Clear any pending debounced analytics for this key
        const timeout = debounceTimeoutsRef.current.get(key);
        if (timeout) {
          clearTimeout(timeout);
          debounceTimeoutsRef.current.delete(key);
        }
      },
      [
        track,
        playgroundId,
        projectId,
        promptId,
        messageIdx,
        parts,
        messageRole,
      ],
    );

    // Debounced analytics tracking for interrupted sessions
    const debouncedTrackPromptChange = useCallback(
      (partIndex: number) => {
        const key = `${promptId}-${messageIdx}-${partIndex}`;

        // Clear any existing timeout for this key
        const existingTimeout = debounceTimeoutsRef.current.get(key);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
        }

        // Set a new timeout to track analytics after 2 seconds of inactivity
        const timeout = setTimeout(() => {
          trackPromptChange(partIndex);
        }, 2000);

        debounceTimeoutsRef.current.set(key, timeout);
      },
      [trackPromptChange, promptId, messageIdx],
    );

    // Save handler that tracks prompt changes
    const handleSave = useCallback(
      async (partIndex: number) => {
        // Track the prompt change immediately on save
        trackPromptChange(partIndex);

        // Call the original save function
        return triggerSave();
      },
      [trackPromptChange, triggerSave],
    );

    const jump = useCallback(
      (up: boolean) => {
        if (up) {
          focusManager?.focusPrevious({ accept: textEditorSelector });
        } else {
          focusManager?.focusNext({ accept: textEditorSelector });
        }
      },
      [focusManager],
    );

    const { flags, isLoading: isLoadingFlags } = useFeatureFlags();
    const getAutoCompleteContext = useCallback(() => {
      return copilotContext?.makeCopilotContext({
        type: "function",
        functionType: "prompt",
      });
    }, [copilotContext]);

    const org = useOrg();
    const { getOrRefreshToken } = useSessionToken();

    // Cleanup function to prevent memory leaks
    useEffect(() => {
      const originalValues = originalValuesRef.current;
      const lastChangeTimes = lastChangeTimeRef.current;
      const debounceTimeouts = debounceTimeoutsRef.current;
      return () => {
        // Clear all refs to prevent memory leaks
        originalValues.clear();
        lastChangeTimes.clear();

        // Clear all pending timeouts
        debounceTimeouts.forEach((timeout) => {
          clearTimeout(timeout);
        });
        debounceTimeouts.clear();
      };
    }, []);

    return (
      <div className="flex flex-col gap-2">
        {parts.map((part, i) => (
          <div className="flex w-full flex-row gap-1.5" key={i}>
            {isMultimodal && (
              <span className="flex-none pt-[3px]">
                {part.type === "text" ? (
                  <MessageSquare className="size-3 text-primary-600" />
                ) : (
                  <File className="size-3 text-primary-600" />
                )}
              </span>
            )}
            <div className="flex flex-1 flex-col gap-1.5 overflow-hidden">
              <TextEditor
                className="flex text-sm"
                isMonospace={false}
                spellcheck={part.type !== "image_url"}
                wrap
                readOnly={isReadOnly}
                onMetaEnter={(type) => {
                  if (type === "cmd") {
                    triggerSave();
                    runPrompts();
                  }
                }}
                autoFocus={
                  focusedEditor.current?.promptId === promptId &&
                  focusedEditor.current?.messageIndex === messageIdx &&
                  focusedEditor.current?.partIndex === i
                }
                jump={jump}
                placeholder={
                  part.type === "text"
                    ? textPlaceholder || "Enter message"
                    : "Multimodal URL, base64 contents, or variable"
                }
                value={part.type === "text" ? part.text : part.image_url.url}
                onSave={() => handleSave(i)}
                onChange={(value) => {
                  const key = `${promptId}-${messageIdx}-${i}`;

                  // Store original value on first keystroke
                  if (!originalValuesRef.current.has(key)) {
                    const currentValue =
                      part.type === "text" ? part.text : part.image_url.url;
                    originalValuesRef.current.set(key, currentValue);
                  }

                  // Track the time of this change
                  lastChangeTimeRef.current.set(key, Date.now());

                  // Set up debounced analytics tracking for interrupted sessions
                  debouncedTrackPromptChange(i);

                  updateMessagePart_NO_SAVE({
                    id: promptId,
                    index: messageIdx,
                    partIndex: i,
                    value,
                  });
                }}
                extensions={extensions}
                getAutoCompleteContext={
                  !flags.copilotPrompts ? undefined : getAutoCompleteContext
                }
                editorId={`prompt-${promptId}-message-${messageIdx}-part-${i}`}
              />
              {part.type === "image_url" && (
                <ImagePartThumb value={part.image_url.url} />
              )}
            </div>
            {part.type === "image_url" &&
              flags.attachmentsInMessages &&
              !isLoadingFlags && (
                <BasicTooltip content="Upload file">
                  <FileInputButton
                    variant="ghost"
                    size="icon"
                    className="size-5 flex-none rounded-[4px] bg-primary-200 text-primary-600 opacity-0 transition-opacity group-hover/promptblock:opacity-100 hover:bg-primary-200/50"
                    onChange={async (file) => {
                      if (!file) {
                        toast.warning("No file selected");
                        return;
                      }

                      toast.promise(
                        async () => {
                          const attachment = await uploadAttachment({
                            file,
                            sessionToken: await getOrRefreshToken(),
                            org: org,
                          });

                          updateMessagePart_NO_SAVE({
                            id: promptId,
                            index: messageIdx,
                            partIndex: i,
                            // Not sure if this is right...
                            value: JSON.stringify(attachment),
                          });
                          triggerSave();
                        },
                        {
                          loading: "Uploading file",
                          success: "File uploaded",
                          error: "Failed to upload file",
                        },
                      );
                    }}
                  >
                    <Paperclip className="size-3 opacity-80" />
                  </FileInputButton>
                </BasicTooltip>
              )}
            {parts.length > 1 && !isReadOnly && (
              <BasicTooltip content="Remove message part">
                <Button
                  variant="ghost"
                  size="icon"
                  className="-mr-0.5 size-5 flex-none rounded-[4px] bg-primary-200 text-primary-600 opacity-0 transition-opacity group-hover/promptblock:opacity-100 hover:bg-primary-200/50"
                  onClick={(e) => {
                    e.preventDefault();
                    removeMessagePart({
                      id: promptId,
                      index: messageIdx,
                      partIndex: i,
                    });
                  }}
                >
                  <Minus className="size-3 opacity-80" />
                </Button>
              </BasicTooltip>
            )}
          </div>
        ))}
      </div>
    );
  },
);

MessageBlockSynced.displayName = "MessageBlockSynced";

export default MessageBlockSynced;

const textEditorSelector = (el: Element) =>
  el instanceof HTMLElement && !!el.classList.contains("cm-content");
