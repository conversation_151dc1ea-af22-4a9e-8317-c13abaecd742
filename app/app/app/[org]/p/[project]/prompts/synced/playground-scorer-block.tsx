import { type ReactNode, type RefObject } from "react";
import { type PromptData } from "@braintrust/typespecs";

import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import { SavedTaskBarSynced } from "./saved-task-bar-synced";
import { type SavedPromptMeta } from "../../playgrounds/[playground]/use-saved-prompt-meta";
import { FunctionEditor } from "#/ui/prompts/function-editor/function-editor";
import { getFunctionEditorTab } from "#/ui/prompts/function-editor/use-function-editor-tabs";
import { type UIFunction } from "#/ui/prompts/schema";
import {
  type PromptExtensionsParams,
  usePromptExtensions,
} from "#/ui/prompts/hooks";
import Link from "next/link";
import { PythonLogo } from "#/app/app/[org]/onboarding-logos";
import { TypescriptLogo } from "#/app/app/[org]/onboarding-logos";
import { Percent } from "lucide-react";
import { type TaskEditConfirmationData } from "#/utils/optimization/provider";
import LoopTaskEditBlock from "#/ui/optimization/loop-task-edit-block";

interface PlaygroundScorerBlockProps {
  isReadOnly?: boolean;
  orgName: string;
  projectId?: string;
  promptId: string;
  promptData?: PromptData;
  functionData?: UIFunction["function_data"];
  onRunPrompts: VoidFunction;
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
  copilotContext?: PlaygroundCopilotContext;
  containerRef: RefObject<HTMLDivElement | null>;
  datasetId?: string;
  promptExtensionsParams: PromptExtensionsParams;
  index: number;
  rowData?: Record<string, unknown>;
  loopEditConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (data: TaskEditConfirmationData | null) => void;
}

export const PlaygroundScorerBlock = ({
  isReadOnly,
  orgName,
  projectId,
  promptId,
  promptData,
  functionData,
  onRunPrompts,
  savedPromptMeta,
  copilotContext,
  containerRef,
  datasetId,
  promptExtensionsParams,
  index,
  rowData,
  loopEditConfirmationData,
  setEditTaskConfirmationData,
}: PlaygroundScorerBlockProps) => {
  const tab = getFunctionEditorTab(functionData);
  const { extensions } = usePromptExtensions({
    ...promptExtensionsParams,
    hoistReservedNames: true,
  });

  const inputShape = "{input, expected, metadata, output}";

  let scorerIcon: ReactNode = <Percent className="size-3" />;
  let typeLabel: ReactNode = "LLM judge scorer";
  if (functionData?.type === "code") {
    const runtime = functionData.data.runtime_context.runtime;
    if (runtime === "node") {
      scorerIcon = <TypescriptLogo className="size-3" />;
      typeLabel = "TypeScript scorer";
    } else if (runtime === "python") {
      scorerIcon = <PythonLogo className="size-3" />;
      typeLabel = "Python scorer";
    }
  }
  const hasEditConfirmationData =
    loopEditConfirmationData?.type === "edit_task" &&
    loopEditConfirmationData.index === index;

  return !hasEditConfirmationData ? (
    <>
      <FunctionEditor
        beforeEditor={
          <div className="px-2 pt-2 pb-3 text-xs">
            <div className="mb-1 flex items-center gap-1 text-xs font-medium text-primary-500">
              {scorerIcon} {typeLabel}
            </div>
            <div className="text-primary-500">
              The assumed input schema for task scorers is{" "}
              <code className="font-mono">{inputShape}</code>.{" "}
              <Link
                href="/docs/guides/playground#for-scorers-as-task"
                className="font-medium hover:underline"
                target="_blank"
              >
                Learn more
              </Link>
            </div>
          </div>
        }
        classNames={{
          codeEditorContainer:
            "p-1 bg-transparent transition-colors hover:not-focus-within:bg-primary-100 focus-within:bg-primary-200/70 border-primary-200/50 text-xs",
          codeEditor: "text-xs",
          tab:
            tab !== "llm" || functionData?.type === "global"
              ? "p-2 overflow-auto flex-auto"
              : undefined,
          promptEditorModelDropdown: "p-2 border-b",
          promptEditorBody: "overflow-auto p-2 min-h-0",
        }}
        orgName={orgName}
        type="scorer"
        extensions={extensions}
        copilotContext={copilotContext}
        modeType="update"
        onRun={onRunPrompts}
        datasetId={datasetId}
        index={index}
        rowData={rowData}
        hideLabel
      />
      {!isReadOnly && (
        <SavedTaskBarSynced
          containerRef={containerRef}
          isReadOnly={isReadOnly}
          orgName={orgName}
          promptData={promptData}
          functionData={functionData}
          origin={promptData?.origin}
          promptId={promptId}
          projectId={projectId}
          savedPromptMetaName={savedPromptMeta[promptId]?.name}
          type="scorer"
        />
      )}
    </>
  ) : (
    <LoopTaskEditBlock
      loopEditConfirmationData={loopEditConfirmationData}
      setEditTaskConfirmationData={setEditTaskConfirmationData}
      promptId={promptId}
    />
  );
};
