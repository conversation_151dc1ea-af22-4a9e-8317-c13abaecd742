import { type UIFunctionData } from "#/ui/prompts/schema";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import {
  type Dimensions,
  type Edge,
  type OnEdgesChange,
  applyEdgeChanges,
  type OnNodesChange,
  applyNodeChang<PERSON>,
  type OnConnect,
  addEdge,
  type OnReconnect,
  reconnectEdge,
} from "@xyflow/react";
import { useState, useMemo, useCallback, useEffect } from "react";
import useEvent from "react-use-event-hook";
import {
  defaultNodes,
  defaultEdges,
  deriveAgentFlowState,
  type EdgeData,
  type AgentFlowNode,
} from "./agent-canvas-utils";
import { useSyncedPrompts } from "./synced/use-synced-prompts";
import isEqual from "lodash.isequal";

export function useAgentCanvasState({
  functionData,
  id,
  isFullscreen,
}: {
  functionData: UIFunctionData | undefined;
  id: string;
  isFullscreen: boolean;
}) {
  const [localFlowState, setLocalFlowState] = useState<
    Record<string, { measured?: Dimensions; selected?: boolean }>
  >({});

  const { nodes, edges } = useMemo(() => {
    if (!functionData || functionData.type !== "graph") {
      return { nodes: defaultNodes, edges: defaultEdges };
    }

    const { nodes: persistedNodes, edges: persistedEdges } =
      deriveAgentFlowState(functionData);

    const nodesWithLocalState = persistedNodes.map((node) => {
      const { selected, measured } = localFlowState[node.id] ?? {};
      return { ...node, selected: selected ?? node.selected, measured };
    });
    const edgesWithLocalState = persistedEdges.map((edge) => {
      const { selected } = localFlowState[edge.id] ?? {};
      return { ...edge, selected: selected ?? edge.selected };
    });

    return { nodes: nodesWithLocalState, edges: edgesWithLocalState };
  }, [functionData, localFlowState]);

  const { saveSyncedPrompt, setAgentNodes_NO_SAVE, setAgentEdges_NO_SAVE } =
    useSyncedPrompts();
  const debouncedSaveSyncedPrompt = useDebouncedCallback(saveSyncedPrompt, 500);

  const setEdges = useEvent(
    (
      edgesOrUpdater:
        | Edge<EdgeData>[]
        | ((edges: Edge<EdgeData>[]) => Edge<EdgeData>[]),
    ) => {
      const newEdges =
        typeof edgesOrUpdater === "function"
          ? edgesOrUpdater(edges)
          : edgesOrUpdater;
      setAgentEdges_NO_SAVE({ id, edges: newEdges });
      debouncedSaveSyncedPrompt(id);
    },
  );

  const onEdgesChange: OnEdgesChange<Edge<EdgeData>> = useCallback(
    (changes) => {
      const localChanges = changes.filter((change) => change.type === "select");
      const remoteChanges = changes.filter(
        (change) => change.type !== "select",
      );
      const hasNewSelection = changes.some(
        (change) =>
          (change.type === "select" && change.selected) ||
          ((change.type === "replace" || change.type === "add") &&
            change.item.selected),
      );
      const hasRemoval = changes.some((change) => change.type === "remove");

      if (localChanges.length > 0 || hasNewSelection || hasRemoval) {
        setLocalFlowState((prev) => {
          const newLocalFlowState = { ...prev };

          if (hasNewSelection) {
            Object.keys(newLocalFlowState).forEach((key) => {
              if (newLocalFlowState[key].selected) {
                newLocalFlowState[key].selected = false;
              }
            });
          }

          changes.forEach((change) => {
            switch (change.type) {
              case "select":
                newLocalFlowState[change.id] ??= {};
                newLocalFlowState[change.id].selected = change.selected;
                break;
              case "remove":
                delete newLocalFlowState[change.id];
                break;
              case "add":
              case "replace":
                if (change.item.selected) {
                  newLocalFlowState[change.item.id] ??= {};
                  newLocalFlowState[change.item.id].selected =
                    change.item.selected;
                }
                break;
            }
          });

          return newLocalFlowState;
        });
      }
      if (remoteChanges.length === 0) return;

      setEdges((prevEdges) =>
        applyEdgeChanges<Edge<EdgeData>>(remoteChanges, prevEdges),
      );
    },
    [setEdges],
  );

  const onNodesChange: OnNodesChange<AgentFlowNode> = useEvent((changes) => {
    const localChanges = changes.filter(
      (change) => change.type === "dimensions" || change.type === "select",
    );
    const remoteChanges = changes.filter(
      (change) => change.type !== "dimensions" && change.type !== "select",
    );
    const hasNewSelection = changes.some(
      (change) =>
        (change.type === "select" && change.selected) ||
        ((change.type === "replace" || change.type === "add") &&
          change.item.selected),
    );
    const hasRemoval = changes.some((change) => change.type === "remove");

    if (localChanges.length > 0 || hasNewSelection || hasRemoval) {
      setLocalFlowState((prev) => {
        const newLocalFlowState = { ...prev };

        if (hasNewSelection) {
          Object.keys(newLocalFlowState).forEach((key) => {
            if (newLocalFlowState[key].selected) {
              newLocalFlowState[key].selected = false;
            }
          });
        }

        changes.forEach((change) => {
          switch (change.type) {
            case "dimensions":
              newLocalFlowState[change.id] ??= {};
              newLocalFlowState[change.id].measured = change.dimensions;
              break;
            case "select":
              newLocalFlowState[change.id] ??= {};
              newLocalFlowState[change.id].selected = change.selected;
              break;
            case "remove":
              delete newLocalFlowState[change.id];
              break;
            case "add":
            case "replace":
              if (change.item.selected) {
                newLocalFlowState[change.item.id] ??= {};
                newLocalFlowState[change.item.id].selected =
                  change.item.selected;
              }
              break;
          }
        });

        return newLocalFlowState;
      });
    }

    if (
      remoteChanges.length === 0 ||
      // Holding a click for some duration is interpreted as a drag and will report a position change, even if there is none
      (remoteChanges.length === 1 &&
        remoteChanges[0].type === "position" &&
        isEqual(
          remoteChanges[0].position,
          nodes.find(
            (node) =>
              // @ts-ignore -- typescript doesn't understand that remoteChange[0] is still a NodePositionChange
              node.id === remoteChanges[0].id,
          )?.position,
        ))
    ) {
      return;
    }

    const newNodes = applyNodeChanges<AgentFlowNode>(remoteChanges, nodes);
    setAgentNodes_NO_SAVE({ id, nodes: newNodes });

    const newEdges = edges.filter(
      (edge) =>
        newNodes.some((node) => node.id === edge.source) &&
        newNodes.some((node) => node.id === edge.target),
    );
    if (newEdges.length !== edges.length) {
      setAgentEdges_NO_SAVE({ id, edges: newEdges });
    }

    debouncedSaveSyncedPrompt(id);
  });

  const onConnect: OnConnect = useCallback(
    (params) => {
      setEdges((eds) => {
        const edgeExists = eds.some(
          (edge) =>
            (edge.source === params.source && edge.target === params.target) ||
            (edge.source === params.target && edge.target === params.source),
        );

        if (!edgeExists && params.source !== params.target) {
          return addEdge(params, eds);
        }
        return eds;
      });
    },
    [setEdges],
  );

  const onReconnect: OnReconnect<Edge<EdgeData>> = useCallback(
    (oldEdge, newConnection) =>
      setEdges((e) => reconnectEdge(oldEdge, newConnection, e)),
    [setEdges],
  );

  // deselect nodes when not fullscreen
  useEffect(() => {
    if (!isFullscreen) {
      setLocalFlowState((prev) => {
        const newLocalFlowState = { ...prev };
        Object.keys(newLocalFlowState).forEach((key) => {
          if (newLocalFlowState[key].selected) {
            newLocalFlowState[key].selected = false;
          }
        });
        return newLocalFlowState;
      });
    }
  }, [isFullscreen]);

  return {
    nodes,
    edges,
    onEdgesChange,
    onNodesChange,
    onConnect,
    onReconnect,
  };
}
