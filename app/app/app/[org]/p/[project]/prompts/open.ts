import { promptSchema, type UIFunction } from "#/ui/prompts/schema";
import { type SavingState } from "#/ui/saving";
import { type DML, useDurableObjectByIds } from "#/utils/mutable-object";
import { type SetStateAction, type Dispatch, useMemo } from "react";

export type OpenedPlaygroundPrompt = {
  prompt: UIFunction | null;
  dml: DML;
  status: "loading" | "error" | "not_found" | "loaded" | "empty";
};

export function useOpenedPlaygroundPrompt({
  promptId,
  projectId,
  promptVersion,
  setSavingState,
}: {
  promptId: string | undefined;
  projectId: string | undefined;
  promptVersion: string | undefined;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
}): OpenedPlaygroundPrompt {
  const ids = useMemo(() => (promptId ? [promptId] : []), [promptId]);
  const { objects, dml, status } = useDurableObjectByIds({
    ids,
    version: promptVersion,
    setSavingState,
    objectType: "project_functions",
    objectId: projectId,
  });

  const prompt = useMemo(
    () => objects && objects[0] && promptSchema.parse(objects[0]),
    [objects],
  );

  return {
    prompt,
    dml,
    status,
  };
}
