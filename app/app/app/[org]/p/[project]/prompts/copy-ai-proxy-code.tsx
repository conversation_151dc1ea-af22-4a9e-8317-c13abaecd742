"use client";

import { But<PERSON> } from "#/ui/button";
import CodeToCopy from "#/ui/code-to-copy";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "#/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "#/ui/tabs";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { isArray, isEmpty, isObject } from "#/utils/object";
import { useOrg } from "#/utils/user";
import { type PromptData } from "@braintrust/typespecs";
import { Code } from "lucide-react";
import Link from "next/link";
import { type PropsWithChildren, useState } from "react";

export function CopyAIProxyCode({
  promptData,
  children,
  className,
}: PropsWithChildren<{ promptData?: PromptData; className?: string }>) {
  const [open, setOpen] = useState(false);

  const { proxy_url: proxyUrl } = useOrg();

  if (!promptData?.prompt || promptData?.prompt?.type !== "chat") return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div>
          <BasicTooltip tooltipContent="Get code snippet">
            {children ?? (
              <Button
                variant="ghost"
                size="icon"
                className={cn("size-7 text-primary-500", className)}
                Icon={Code}
              />
            )}
          </BasicTooltip>
        </div>
      </DialogTrigger>
      <DialogContent
        onOpenAutoFocus={(e) => {
          e.preventDefault();
        }}
        className="flex w-[500px] flex-col gap-4 overflow-auto"
      >
        <DialogTitle>Call this prompt from your codebase</DialogTitle>
        <p className="text-sm">
          Call this prompt from your codebase using the{" "}
          <Link href="/docs/guides/proxy" className="underline" target="_blank">
            AI proxy
          </Link>
          .
        </p>

        <Tabs defaultValue="typescript" className="w-full">
          <TabsList className="h-auto">
            <TabsTrigger value="typescript" className="text-xs">
              TypeScript
            </TabsTrigger>
            <TabsTrigger value="python" className="text-xs">
              Python
            </TabsTrigger>
            <TabsTrigger value="curl" className="text-xs">
              cURL
            </TabsTrigger>
          </TabsList>
          <TabsContent value="typescript">
            <CodeToCopy
              className="break-all"
              highlighterClassName="text-xs"
              language="typescript"
              data={makeSampleCode({
                language: "typescript",
                promptData,
                proxyUrl,
              })}
            />
          </TabsContent>
          <TabsContent value="python">
            <CodeToCopy
              className="break-all"
              highlighterClassName="text-xs"
              language="python"
              data={makeSampleCode({
                language: "python",
                promptData,
                proxyUrl,
              })}
            />
          </TabsContent>
          <TabsContent value="curl">
            <CodeToCopy
              className="break-all"
              highlighterClassName="text-xs"
              language="bash"
              data={makeSampleCode({
                language: "curl",
                promptData,
                proxyUrl,
              })}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

const safeParse = (jsonString: string) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error(
      "dropping invalid json from prompt:",
      jsonString,
      "error :",
      error,
    );
    return null;
  }
};

function makeSampleCode({
  language,
  promptData,
  proxyUrl,
}: {
  language: "typescript" | "python" | "curl";
  promptData?: PromptData;
  proxyUrl?: string;
}) {
  proxyUrl = proxyUrl || "https://api.braintrust.dev/v1/proxy";

  if (promptData?.prompt?.type !== "chat") {
    throw new Error("Prompt type must be 'chat'");
  }

  const { use_cache: _, ...params } = promptData?.options?.params || {};

  const input = {
    model: promptData?.options?.model,
    messages: promptData?.prompt?.messages,
    ...params,
    ...(promptData?.prompt?.tools
      ? { tools: safeParse(promptData.prompt.tools) }
      : {}),
  };

  switch (language) {
    case "typescript":
      return `import { OpenAI } from "openai";

const client = new OpenAI({
  baseURL: "${proxyUrl}",
  // Can use Braintrust, OpenAI, Anthropic, etc. API keys here
  apiKey: process.env.BRAINTRUST_API_KEY,
});

async function main() {
  const response = await client.chat.completions.create(${JSON.stringify(
    input,
    null,
    2,
  )});

  console.log(response.choices[0].message.content);
}

main().catch(console.error);
`;

    case "python":
      const pythonArgs = Object.entries(input)
        .filter(([key, value]) => !isEmpty(value))
        .map(([key, value]) => `    ${key}=${escapePythonValue(value)},`);
      return `import os

from openai import OpenAI

client = OpenAI(
    base_url="${proxyUrl}",
    # Can use Braintrust, OpenAI, Anthropic, etc. API keys here
    api_key=os.environ.get("BRAINTRUST_API_KEY"),
)

response = client.chat.completions.create(
${pythonArgs.join("\n")}
)
print(response.choices[0].message.content)
`;

    case "curl":
      return `curl -i ${proxyUrl}/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -H "Authorization: Bearer $BRAINTRUST_API_KEY" \\
  --compressed \\
  -d @- <<'EOF'
${JSON.stringify(input, null, 2)}
EOF`;
  }
}

function escapePythonValue(value: unknown, indent: number = 0): string {
  if (value === null || value === undefined) return "None";
  if (value === true) return "True";
  if (value === false) return "False";
  if (typeof value === "string") {
    // Use repr() style escaping for strings
    return JSON.stringify(value);
  }
  if (typeof value === "number") {
    return value.toString();
  }
  if (isArray(value)) {
    if (value.length === 0) return "[]";
    const items = value.map(
      (item) =>
        `${"    ".repeat(indent + 1)}${escapePythonValue(item, indent + 1)}`,
    );
    return `[\n${items.join(",\n")}\n${"    ".repeat(indent)}]`;
  }
  if (isObject(value)) {
    const entries = Object.entries(value);
    if (entries.length === 0) return "{}";
    const formattedEntries = entries.map(
      ([key, val]) =>
        `${"    ".repeat(indent + 1)}${JSON.stringify(key)}: ${escapePythonValue(val, indent + 1)}`,
    );
    return `{\n${formattedEntries.join(",\n")}\n${"    ".repeat(indent)}}`;
  }
  // Fallback to JSON string if we can't handle the type
  return `json.loads('${JSON.stringify(value).replace(/'/g, "\\'")}')`;
}
