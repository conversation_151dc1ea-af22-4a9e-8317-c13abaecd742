import "@xyflow/react/dist/style.css";
import { excludeKeys } from "./utils";
import { DataTextEditor } from "#/ui/data-text-editor";
import { isObject } from "braintrust/util";

export function PromptDiff({
  promptData,
  diffPromptData,
  rowId,
  hideDiffDisclaimer = false,
  hideLineNumbers = false,
}: {
  promptData: object | undefined;
  diffPromptData: object | undefined;
  rowId: string;
  hideDiffDisclaimer?: boolean;
  hideLineNumbers?: boolean;
}) {
  return (
    <>
      <DataTextEditor
        allowedRenderOptions={["json", "yaml"]}
        hideLineNumbers={hideLineNumbers}
        diffValue={
          diffPromptData
            ? {
                ...diffPromptData,
                ...("options" in diffPromptData &&
                isObject(diffPromptData.options)
                  ? {
                      options: excludeKeys(diffPromptData.options, [
                        "position",
                      ]),
                    }
                  : undefined),
              }
            : undefined
        }
        rowId={rowId}
        readOnly
        value={
          promptData
            ? {
                ...promptData,
                ...("options" in promptData && isObject(promptData.options)
                  ? { options: excludeKeys(promptData.options, ["position"]) }
                  : undefined),
              }
            : undefined
        }
      />
      {!hideDiffDisclaimer && (
        <div className="py-2 text-xs text-pretty text-primary-400">
          This is a diff of this prompt compared to the base prompt. To edit
          this prompt, disable diff mode.
        </div>
      )}
    </>
  );
}
