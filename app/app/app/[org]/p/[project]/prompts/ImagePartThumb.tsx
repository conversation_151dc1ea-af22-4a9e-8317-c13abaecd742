import { useMemo, useState } from "react";
import { cn } from "#/utils/classnames";
import { Tooltip, TooltipTrigger } from "#/ui/tooltip";
import { TooltipContent } from "@radix-ui/react-tooltip";
import {
  braintrustAttachmentReferenceSchema,
  type AttachmentReference,
} from "@braintrust/typespecs";
import { safeDeserializePlainStringAsJSON } from "#/utils/object";
import { AttachmentItem } from "#/ui/attachment/attachment-list";

export const ImagePartThumb = ({ value }: { value?: string }) => {
  const [isInvalid, setInvalid] = useState(true);
  const attachmentReference: AttachmentReference | undefined = useMemo(() => {
    if (!value) {
      return undefined;
    }
    const deserialized = safeDeserializePlainStringAsJSON(value);
    const parsed = braintrustAttachmentReferenceSchema.safeParse(deserialized);
    if (parsed.success) {
      return parsed.data;
    }
    return undefined;
  }, [value]);

  if (!value) {
    return null;
  }

  if (attachmentReference) {
    return <AttachmentItem mode="preview" extracted={attachmentReference} />;
  }

  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger disabled={isInvalid} asChild>
        <div
          className={cn(
            "h-12 w-16 flex-none overflow-hidden rounded-xs border",
            {
              hidden: isInvalid,
            },
          )}
        >
          <img
            src={value}
            onLoad={() => setInvalid(false)}
            onError={() => setInvalid(true)}
            alt="thumbnail"
            className="object-cover"
          />
        </div>
      </TooltipTrigger>
      <TooltipContent className="z-50 overflow-hidden rounded-md border shadow-lg">
        <img src={value} alt="thumbnail" className="w-full max-w-md" />
      </TooltipContent>
    </Tooltip>
  );
};
