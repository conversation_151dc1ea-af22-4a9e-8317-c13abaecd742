import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { useMemo, useState } from "react";
import {
  type OnSaveStructuredOutputFn,
  StructuredOutput,
  structuredOutputSchema,
} from "./structured-output";
import { BracesIcon, FileJson2Icon, TypeIcon } from "lucide-react";
import { zodErrorToString } from "#/utils/validation";
import { isObject } from "#/utils/object";
import { type PromptData } from "@braintrust/typespecs";
import { type z } from "zod";
import { cn } from "#/utils/classnames";
import { type OnSaveResponseTypeFn } from "./synced/prompt-block-synced";

export const responseFormatLabel = {
  text: { label: "Text output", Icon: TypeIcon },
  json_object: { label: "JSON output", Icon: BracesIcon },
  json_schema: { label: "Structured output", Icon: FileJson2Icon },
};

type ResponseFormat = {
  type: "json_schema" | "json_object" | "text";
  schema?: z.infer<typeof structuredOutputSchema>;
};

export const parseResponseFormat = (
  prompt?: PromptData | null,
): ResponseFormat => {
  const format = prompt?.options?.params?.response_format;
  if (isObject(format) && format.type === "json_schema") {
    const schemaParsed = structuredOutputSchema.safeParse(format.json_schema);
    if (schemaParsed.success) {
      return { type: "json_schema", schema: schemaParsed.data };
    } else {
      console.warn(
        "Failed to parse response format JSON schema",
        zodErrorToString(schemaParsed.error, 2, true),
      );
      return { type: "text" };
    }
  }
  if (isObject(format) && format.type === "json_object") {
    return { type: "json_object" };
  }
  return { type: "text" };
};

export const OutputFormat = ({
  supportsStructuredOutput,
  currentInitData,
  onSaveStructuredOutput,
  onSaveResponseType,
  isReadOnly,
}: {
  supportsStructuredOutput: boolean;
  currentInitData?: PromptData;
  onSaveStructuredOutput: OnSaveStructuredOutputFn;
  onSaveResponseType: OnSaveResponseTypeFn;
  isReadOnly?: boolean;
}) => {
  const { type, schema } = useMemo<{
    type: keyof typeof responseFormatLabel;
    schema?: z.infer<typeof structuredOutputSchema>;
  }>(() => parseResponseFormat(currentInitData), [currentInitData]);

  const { label, Icon } = responseFormatLabel[type];

  const [isStructuredOutputOpen, setIsStructuredOutputOpen] = useState(false);

  return (
    <div className="flex">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            isDropdown={!isReadOnly}
            size="xs"
            className={cn("disabled:opacity-100", {
              "rounded-r-none": type === "json_schema",
            })}
            disabled={isReadOnly}
          >
            <Icon className="size-3 flex-none" />
            {label}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          {Object.entries(responseFormatLabel).map(([key, { label, Icon }]) => (
            <DropdownMenuItem
              key={key}
              disabled={key === "json_schema" && !supportsStructuredOutput}
              onClick={async () => {
                if (key === "json_schema") {
                  setIsStructuredOutputOpen(true);
                  return;
                }
                await onSaveResponseType(
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                  key as "text" | "json_object" | "json_schema",
                );
              }}
            >
              <Icon className="size-3" />
              {label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      <StructuredOutput
        schema={schema}
        onSaveStructuredOutput={onSaveStructuredOutput}
        isOpen={isStructuredOutputOpen}
        setIsOpen={setIsStructuredOutputOpen}
        isReadOnly={isReadOnly}
      >
        <Button
          size="xs"
          className={cn("rounded-l-none border-l-0", {
            hidden: type !== "json_schema" || !schema,
          })}
        >
          <span className="max-w-20 truncate">{schema?.name}</span>
        </Button>
      </StructuredOutput>
    </div>
  );
};
