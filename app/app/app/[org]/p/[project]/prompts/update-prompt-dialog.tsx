import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import { type RefObject, useMemo, useState } from "react";
import { DiffText } from "#/ui/diff";
import { RadioGroup, RadioGroupItem } from "#/ui/radio";
import { deepEqual, excludeKeys } from "./utils";
import { CollapsibleSection } from "#/ui/collapsible-section";
import React from "react";

export const UpdatePromptDialog = ({
  type,
  isDirty,
  isBehind,
  onSave,
  onResetToLatest,
  onResetToOpened,
  openedPrompt,
  latestPrompt,
  promptData,
  functionData,
  dirtyPromptMessage,
  name,
  description,
  metadata,
  buttonRef,
}: {
  type: "prompt" | "agent" | "scorer";
  isDirty: boolean;
  isBehind: boolean;
  onSave: () => Promise<void>;
  onResetToOpened: () => Promise<void>;
  onResetToLatest: () => Promise<void>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  openedPrompt?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  latestPrompt?: any;
  name: string;
  description?: string | null;
  metadata?: Record<string, unknown> | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  promptData: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  functionData: any;
  dirtyPromptMessage: string;
  buttonRef: RefObject<HTMLButtonElement | null>;
}) => {
  const [isOpen, setOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string>(
    isDirty ? "save" : "reset",
  );

  const diff = useMemo(() => {
    const unsaved = {
      name: name || "",
      description: description || "",
      metadata: metadata || {},
      prompt: promptData?.prompt || {},
      function_data: functionData || {},
      options: excludeKeys(promptData?.options, ["position"]) || {},
      tool_functions: promptData?.tool_functions || [],
    };
    const latest = {
      name: latestPrompt?.name || "",
      description: latestPrompt?.description || "",
      metadata: latestPrompt?.metadata || {},
      prompt: latestPrompt?.prompt_data?.prompt || {},
      function_data: latestPrompt?.function_data || {},
      options:
        excludeKeys(latestPrompt?.prompt_data?.options, ["position"]) || {},
      tool_functions: latestPrompt?.prompt_data?.tool_functions || [],
    };
    const pinned = {
      name: openedPrompt?.name || "",
      description: openedPrompt?.description || "",
      metadata: openedPrompt?.metadata || {},
      prompt: openedPrompt?.prompt_data?.prompt || {},
      function_data: openedPrompt?.function_data || {},
      options:
        excludeKeys(openedPrompt?.prompt_data?.options, ["position"]) || {},
      tool_functions: openedPrompt?.prompt_data?.tool_functions || [],
    };
    return {
      old:
        selectedOption === "reset" || selectedOption === "revert"
          ? unsaved
          : pinned,
      new:
        selectedOption === "save"
          ? unsaved
          : selectedOption === "reset"
            ? latest
            : pinned,
    };
  }, [
    latestPrompt,
    openedPrompt,
    promptData?.prompt,
    promptData?.options,
    functionData,
    name,
    description,
    metadata,
    selectedOption,
    promptData?.tool_functions,
  ]);

  return (
    <Dialog open={isOpen} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          size="xs"
          className="flex-none bg-background"
          ref={buttonRef}
          onClick={() => {
            setSelectedOption(isDirty ? "save" : "reset");
          }}
        >
          {isDirty && !isBehind ? "Save version..." : "Update..."}
        </Button>
      </DialogTrigger>
      <DialogContent className="flex flex-col gap-0 overflow-hidden p-0">
        <DialogHeader className="border-b p-6">
          <DialogTitle>Update saved {type}</DialogTitle>
          <DialogDescription>
            {dirtyPromptMessage}. Select an option below to preview changes and
            update the {type}.
          </DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-auto p-6">
          <RadioGroup
            className="mb-8 gap-4 font-medium"
            value={selectedOption}
            onValueChange={setSelectedOption}
          >
            {isDirty && (
              <>
                <Option
                  value="save"
                  label={`Save as new ${type} version`}
                  description={`Create a new version of this ${type} with your changes`}
                />
                <Option
                  value="revert"
                  label="Revert changes"
                  description={
                    isBehind
                      ? `Discard your unsaved changes and revert to the pinned version of this ${type}`
                      : `Discard your unsaved changes and revert to the latest version of this ${type}`
                  }
                />
              </>
            )}
            {isBehind && (
              <Option
                value="reset"
                label={`Update to latest ${type} version`}
                description={
                  isDirty
                    ? `Discard your unsaved changes and revert to the latest version of this ${type}`
                    : `Update to the latest version of this ${type}`
                }
              />
            )}
          </RadioGroup>
          <CollapsibleSection
            title="Preview changes"
            localStorageKey="promptUpdatePreviewChanges"
          >
            {diff.old.name !== diff.new.name && (
              <>
                <div className="mb-2 text-xs">Name</div>
                <div className="mb-6 text-sm text-primary-700">
                  <DiffText oldText={diff.old.name} newText={diff.new.name} />
                </div>
              </>
            )}
            {diff.old.description !== diff.new.description && (
              <>
                <div className="mb-2 text-xs">Description</div>
                <div className="mb-6 text-sm text-primary-700">
                  <DiffText
                    oldText={diff.old.description}
                    newText={diff.new.description}
                  />
                </div>
              </>
            )}

            {!deepEqual(diff.old.metadata, diff.new.metadata) && (
              <>
                <div className="mb-2 text-xs">Metadata</div>
                <pre className="mb-6">
                  <code className="block font-mono text-xs leading-normal whitespace-pre-wrap text-primary-700">
                    <DiffText
                      oldText={JSON.stringify(diff.old.metadata, null, 2)}
                      newText={JSON.stringify(diff.new.metadata, null, 2)}
                    />
                  </code>
                </pre>
              </>
            )}

            {!deepEqual(diff.old.prompt, diff.new.prompt) &&
              (type === "prompt" || type === "scorer") && (
                <>
                  <div className="mb-2 text-xs">Prompt</div>
                  <pre className="mb-6">
                    <code className="block font-mono text-xs leading-normal whitespace-pre-wrap text-primary-700">
                      <DiffText
                        oldText={JSON.stringify(diff.old.prompt, null, 2)}
                        newText={JSON.stringify(diff.new.prompt, null, 2)}
                      />
                    </code>
                  </pre>
                </>
              )}

            {!deepEqual(diff.old.tool_functions, diff.new.tool_functions) &&
              type === "prompt" && (
                <>
                  <div className="mb-2 text-xs">Tool functions</div>
                  <pre className="mb-6">
                    <code className="block font-mono text-xs leading-normal whitespace-pre-wrap text-primary-700">
                      <DiffText
                        oldText={JSON.stringify(
                          diff.old.tool_functions,
                          null,
                          2,
                        )}
                        newText={JSON.stringify(
                          diff.new.tool_functions,
                          null,
                          2,
                        )}
                      />
                    </code>
                  </pre>
                </>
              )}

            {!deepEqual(diff.old.function_data, diff.new.function_data) &&
              type === "agent" && (
                <>
                  <div className="mb-2 text-xs">Function data</div>
                  <pre className="mb-6">
                    <code className="block font-mono text-xs leading-normal whitespace-pre-wrap text-primary-700">
                      <DiffText
                        oldText={JSON.stringify(
                          diff.old.function_data,
                          null,
                          2,
                        )}
                        newText={JSON.stringify(
                          diff.new.function_data,
                          null,
                          2,
                        )}
                      />
                    </code>
                  </pre>
                </>
              )}

            {type === "scorer" &&
              diff.old.function_data?.type === "code" &&
              !deepEqual(
                diff.old.function_data.data.code,
                diff.new.function_data.data.code,
              ) && (
                <>
                  <div className="mb-2 text-xs">Code</div>
                  <pre className="mb-6">
                    <code className="block font-mono text-xs leading-normal whitespace-pre-wrap text-primary-700">
                      <DiffText
                        oldText={diff.old.function_data.data.code}
                        newText={diff.new.function_data.data.code}
                      />
                    </code>
                  </pre>
                </>
              )}

            {!deepEqual(diff.old.options, diff.new.options) &&
              type === "prompt" && (
                <>
                  <div className="mb-2 text-xs">Options</div>
                  <pre className="mb-6">
                    <code className="block font-mono text-xs leading-normal whitespace-pre-wrap text-primary-700">
                      <DiffText
                        oldText={JSON.stringify(diff.old.options, null, 2)}
                        newText={JSON.stringify(diff.new.options, null, 2)}
                      />
                    </code>
                  </pre>
                </>
              )}
          </CollapsibleSection>
        </div>
        <DialogFooter className="border-t px-6 py-3">
          <Button
            variant="primary"
            size="sm"
            onClick={() => {
              if (selectedOption === "save") {
                onSave();
              } else if (selectedOption === "reset") {
                onResetToLatest();
              } else if (selectedOption === "revert") {
                onResetToOpened();
              }
              setOpen(false);
            }}
          >
            Update
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const Option = ({
  value,
  label,
  description,
}: {
  value: string;
  label: string;
  description: string;
}) => {
  return (
    <label className="flex items-baseline gap-3">
      <RadioGroupItem value={value} />
      <div>
        {label}
        <div className="text-sm font-normal text-primary-600">
          {description}
        </div>
      </div>
    </label>
  );
};
