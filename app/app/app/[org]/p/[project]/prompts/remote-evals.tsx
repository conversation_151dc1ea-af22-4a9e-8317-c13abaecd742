import { memo, useCallback, useMemo, useState, type RefObject } from "react";
import { isEmpty } from "braintrust/util";
import { promptDataSchema, type PromptData } from "@braintrust/typespecs";
import { type PromptEditorProps } from "./agent-canvas";
import { useRemoteEvals } from "../playgrounds/[playground]/playx/remote-evals-provider";
import { type RemoteEvalData } from "@braintrust/typespecs";
import { type EvalParameterSerializedSchema } from "braintrust";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { AlertTriangle, RefreshCcw, Unplug } from "lucide-react";
import { Button } from "#/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import { cn } from "#/utils/classnames";
import { type JSONSchema, toSchemaObject } from "@braintrust/btql/schema";
import { Switch } from "#/ui/switch";
import { DataTextEditor } from "#/ui/data-text-editor";
import { Input } from "#/ui/input";
import Ajv from "ajv";
import { InfoBanner } from "#/ui/info-banner";
import Link from "next/link";
import {
  SyncedPromptsScoperProvider,
  useSyncedPrompts,
} from "./synced/use-synced-prompts";
import { PromptEditorSynced } from "../../../prompt/[prompt]/prompt-editor-synced";
import { type SyncedPlaygroundBlock } from "#/ui/prompts/schema";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { type RenderOption } from "#/utils/parse";

// TODO:
// - Support custom endpoint URLs (probably as configuration in the project).
// - Write docs: how to run directly and with `runDevServer`
// - What are the right semantics for sharing scores? Right now, we run both sets of scores, but
//   maybe we should surface the scores from your code into the UI and let you select them?
// - Same question for dataset. Maybe we let you explicitly use the dataset in the remote eval?
// - `--watch` flag

export const RemoteEvalForm = memo(
  ({
    containerRef,
    id,
    functionData,
    isFullscreen,
    setFullscreen,
    ...promptEditorProps
  }: {
    containerRef: RefObject<HTMLDivElement | null>;
    id: string;
    functionData: RemoteEvalData;
    isFullscreen: boolean;
    setFullscreen: (isFullscreen: boolean) => void;
  } & PromptEditorProps) => {
    const { remoteEvals } = useRemoteEvals({ enabled: true });
    const remoteEndpoint = useMemo(
      () => remoteEvals[functionData.endpoint],
      [remoteEvals, functionData.endpoint],
    );
    const remoteEval = useMemo(
      () => remoteEndpoint?.evals[functionData.eval_name],
      [remoteEndpoint, functionData.eval_name],
    );

    const queryClient = useQueryClient();
    const [isRetrying, setIsRetrying] = useState(false);
    const retry = useCallback(async () => {
      setIsRetrying(true);
      try {
        await queryClient.invalidateQueries({
          queryKey: ["remote-evals", functionData.endpoint],
        });
      } finally {
        setIsRetrying(false);
      }
    }, [queryClient, functionData.endpoint]);

    if (!remoteEval || remoteEndpoint.error) {
      return (
        <div className="flex flex-1 p-2">
          <TableEmptyState
            className="w-full justify-center"
            Icon={!remoteEval ? Unplug : AlertTriangle}
            label={
              !remoteEval
                ? `Remote eval "${functionData.eval_name}" not found`
                : `There was a problem loading the remote eval "${functionData.eval_name}"`
            }
          >
            <div className="text-center text-xs text-primary-500">
              {!remoteEval
                ? `Please check that the remote server is running at ${functionData.endpoint}`
                : `Please check the remote eval configuration at ${functionData.endpoint}`}
            </div>
            <Button
              onClick={retry}
              size="xs"
              isLoading={isRetrying}
              Icon={RefreshCcw}
              className="px-3"
            >
              Retry
            </Button>
          </TableEmptyState>
        </div>
      );
    }

    return (
      <>
        <div
          ref={containerRef}
          className="flex flex-1 flex-col overflow-hidden"
        >
          <div className="flex flex-none items-center gap-1 border-b p-3 text-xs">
            <Unplug className="size-3" />
            <span className="flex-1">{functionData.eval_name}</span>
            <span className="text-primary-500">{functionData.endpoint}</span>
          </div>
          <div className="flex flex-1 flex-col overflow-auto pb-4">
            {Object.entries(remoteEval.parameters ?? {}).map(
              ([name, parameterSchema], idx) => (
                <div key={idx} className="mb-2">
                  <div
                    className={cn("mx-2 px-1 pt-4 pb-2 text-xs", {
                      "border-t": idx > 0,
                    })}
                  >
                    <div className="text-sm font-medium">{name}</div>
                    {parameterSchema.description && (
                      <div className="mt-1 text-xs text-primary-500">
                        {parameterSchema.description}
                      </div>
                    )}
                  </div>
                  <RemoteEvalParameter
                    name={name}
                    parameter={functionData.parameters?.[name]}
                    schema={parameterSchema}
                    promptEditorProps={promptEditorProps}
                    remoteEvalId={id}
                  />
                </div>
              ),
            )}
          </div>
        </div>
        <InfoBanner className="my-0 flex-none rounded-t-none border-x-0 border-b-0 border-primary-200 px-3">
          <div className="flex justify-between">
            Remote evals are in beta
            <Link
              href="/docs/guides/remote-evals"
              className="font-medium hover:underline"
              target="_blank"
            >
              Learn more
            </Link>
          </div>
        </InfoBanner>
      </>
    );
  },
);
RemoteEvalForm.displayName = "RemoteEvalForm";

function RemoteEvalParameter({
  name,
  parameter,
  schema,
  promptEditorProps,
  remoteEvalId,
}: {
  name: string;
  parameter: unknown;
  schema: EvalParameterSerializedSchema[string];
  promptEditorProps: PromptEditorProps;
  remoteEvalId: string;
}) {
  if (schema.type === "prompt") {
    return (
      <PromptParameter
        remoteEvalId={remoteEvalId}
        name={name}
        data={parameter}
        promptEditorProps={promptEditorProps}
        defaultPrompt={schema.default}
      />
    );
  } else {
    return (
      <div className="px-2">
        <JSONParameterEditor
          remoteEvalId={remoteEvalId}
          name={name}
          data={parameter}
          schema={schema.schema}
        />
      </div>
    );
  }
}

function PromptParameter({
  remoteEvalId,
  name,
  data,
  defaultPrompt,
  promptEditorProps,
}: {
  remoteEvalId: string;
  name: string;
  data: unknown;
  defaultPrompt: PromptData | undefined;
  promptEditorProps: PromptEditorProps;
}) {
  // Scope useSyncedPrompts calls in PromptEditorSynced to the remote eval nested prompts
  const promptScoper = useCallback(
    (data: SyncedPlaygroundBlock) => {
      if (data.function_data.type !== "remote_eval") {
        return undefined;
      }
      // If this is the first time this remote prompt is being edited, initialize it to the default prompt.
      // This mutation syntax is necessary for immer to work.
      data.function_data.parameters[name] ??= structuredClone(defaultPrompt);
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      return data.function_data.parameters[name] as PromptData;
    },
    [name, defaultPrompt],
  );

  const parsedPrompt = useMemo(() => {
    if (!isEmpty(data)) {
      return promptDataSchema.safeParse(data);
    }
    return { success: true, data: defaultPrompt ?? {} };
  }, [data, defaultPrompt]);

  if (!parsedPrompt.success) {
    return <div>Invalid prompt</div>;
  }

  return (
    <SyncedPromptsScoperProvider
      persistenceId={remoteEvalId}
      scoper={promptScoper}
    >
      <PromptParameterEditor
        data={parsedPrompt.data}
        remoteEvalId={remoteEvalId}
        {...promptEditorProps}
      />
    </SyncedPromptsScoperProvider>
  );
}

function PromptParameterEditor({
  data,
  orgName,
  modelOptionsByProvider,
  allAvailableModels,
  copilotContext,
  extensions,
  remoteEvalId,
  showNoConfiguredSecretsMessage,
}: {
  data: PromptData;
  remoteEvalId: string;
} & PromptEditorProps) {
  return (
    <div className="p-2">
      <PromptEditorSynced
        orgName={orgName}
        modelOptionsByProvider={modelOptionsByProvider}
        allAvailableModels={allAvailableModels}
        extensions={extensions}
        promptData={data}
        onRun={() => {
          console.warn("running not supported");
        }}
        copilotContext={copilotContext}
        promptId={remoteEvalId}
        showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
      />
    </div>
  );
}

const TEXT_RENDER_OPTIONS: RenderOption[] = ["text"];
const JSON_RENDER_OPTIONS: RenderOption[] = ["json", "yaml"];

export function JSONParameterEditor({
  name,
  remoteEvalId,
  data: dataProp,
  schema,
}: {
  name: string;
  remoteEvalId: string;
  data: unknown;
  schema: JSONSchema;
}) {
  const { updateRemoteEvalParameter_NO_SAVE__ROOT, saveSyncedPrompt } =
    useSyncedPrompts();
  const onSave = useCallback(() => {
    saveSyncedPrompt(remoteEvalId);
  }, [remoteEvalId, saveSyncedPrompt]);
  const saveDebounced = useDebouncedCallback(onSave, 500);
  const onChange = useCallback(
    (value: unknown) => {
      updateRemoteEvalParameter_NO_SAVE__ROOT({
        id: remoteEvalId,
        name,
        value,
      });
    },
    [remoteEvalId, name, updateRemoteEvalParameter_NO_SAVE__ROOT],
  );

  const schemaObject = useMemo(() => toSchemaObject(schema), [schema]);
  const validateFn = useMemo(() => {
    const ajv = new Ajv();
    const validate = ajv.compile(schema);
    return (data: unknown) => {
      const valid = validate(data);
      if (!valid) {
        throw new Error(validate.errors?.map((e) => e.message).join("\n"));
      }
    };
  }, [schema]);
  const data = dataProp ?? schemaObject.default;

  if (schemaObject.type === "boolean") {
    return (
      <div className="px-1">
        <Switch
          checked={!!data}
          onCheckedChange={(checked) => {
            onChange(checked);
            onSave();
          }}
        />
      </div>
    );
  }

  if (schemaObject.type === "number") {
    const numberValue = typeof data === "number" ? data : undefined;
    return (
      <Input
        type="number"
        placeholder="Enter number"
        value={numberValue}
        onChange={(e) => {
          const value =
            e.target.value === "" ? undefined : Number(e.target.value);
          onChange(value);
          saveDebounced();
        }}
      />
    );
  }

  if (schemaObject.type === "string") {
    return (
      <DataTextEditor
        value={data}
        allowedRenderOptions={TEXT_RENDER_OPTIONS}
        onSave={onSave}
        onChange={onChange}
      />
    );
  }

  return (
    <DataTextEditor
      value={data}
      allowedRenderOptions={JSON_RENDER_OPTIONS}
      onSave={onSave}
      onChange={onChange}
      validateFn={validateFn}
    />
  );
}
