import { isEmpty } from "#/utils/object";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function deepEqual(object1: any, object2: any): boolean {
  const keys1 = Object.keys(object1);
  const keys2 = Object.keys(object2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    const val1 = object1[key];
    const val2 = object2[key];
    const areObjects = isObject(val1) && isObject(val2);
    if (
      (areObjects && !deepEqual(val1, val2)) ||
      (!areObjects && val1 !== val2)
    ) {
      return false;
    }
  }

  return true;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function isObject(object: any) {
  return object != null && typeof object === "object";
}

export function excludeKeys(
  object: Record<string, unknown> | null | undefined,
  keys: string[],
) {
  if (isEmpty(object)) {
    return object;
  }
  return Object.fromEntries(
    Object.entries(object).filter(([key]) => !keys.includes(key)),
  );
}
