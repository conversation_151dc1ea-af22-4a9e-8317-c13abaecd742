import { getOrgLink } from "../../getOrgLink";

export const getProjectLink = (args: {
  orgName: string;
  projectName: string;
}) => {
  return `${getOrgLink({
    orgName: args.orgName,
  })}/p/${encodeURIComponent(args.projectName)}`;
};

export const getProjectConfigurationLink = (args: {
  orgName: string;
  projectName: string;
}) => {
  return `${getProjectLink({
    orgName: args.orgName,
    projectName: args.projectName,
  })}/configuration`;
};
