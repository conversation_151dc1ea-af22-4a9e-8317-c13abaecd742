import type { AggregationType } from "#/utils/queries/aggregations";
import { CHART_COLOR_CLASSNAMES } from "#/ui/color";
import { type Datapoint } from "#/ui/charts/multi-line-chart";
import { D3_SYMBOLS } from "#/ui/charts/symbols";
import { type ExperimentData } from "./useLoadExperimentChartData";
import { getObjectPathValue } from "./utils";
import {
  GROUP_BY_NONE,
  GROUP_BY_SCORE,
  type SelectionType,
  type SelectionTypesEnum,
  X_AXIS_COMPARISON,
  X_AXIS_EXPERIMENT,
  X_AXIS_TIME,
  formatSelectionTypeName,
  isEqualSelectionType,
  isGroupByYMetric,
} from "#/ui/charts/selectionTypes";

export type PointMetadata = Omit<ExperimentData, "scores" | "metrics">;

export function groupExperimentData(
  experimentData: ExperimentData[],
  colorGrouping: SelectionType,
  symbolGrouping: SelectionType,
  xMetric: SelectionType,
  yMetric: SelectionType,
  visibleMeasures: SelectionType[],
  selectedExperiments: Record<string, boolean>,
) {
  // use array because if the keys are number values then order is not preserved properly
  const lineNames: SelectionType[] = [];
  const colorGroups: Record<string, Record<SelectionTypesEnum, number>> = {};
  const symbolGroups: Record<string, Record<SelectionTypesEnum, number>> = {};
  const groupByYMetric = isGroupByYMetric(colorGrouping, xMetric);

  const pointData = experimentData.flatMap((e, i) => {
    const measures = groupByYMetric ? visibleMeasures : [yMetric];
    return measures.flatMap((measure) => {
      const xMetricVal = getMetricVal({
        metric: xMetric,
        scores: e.scores,
        metadata: e.metadata,
        metrics: e.metrics,
      });
      const yMetricVal = getMetricVal({
        metric: measure,
        scores: e.scores,
        metadata: e.metadata,
        metrics: e.metrics,
      });

      // use i to evenly space the points out
      const xVal = isEqualSelectionType(xMetric, X_AXIS_EXPERIMENT)
        ? i
        : isEqualSelectionType(xMetric, X_AXIS_TIME)
          ? e.last_updated
          : isEqualSelectionType(xMetric, X_AXIS_COMPARISON)
            ? formatSelectionTypeName(measure)
            : xMetricVal;
      if (xVal == null) return [];

      // we preserve nulls for the line chart
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      const point = [xVal, yMetricVal] as [string | number, number | null];

      const colorGroupVal =
        isEqualSelectionType(xMetric, X_AXIS_COMPARISON) && groupByYMetric
          ? { value: e.name, type: "none" as const }
          : isEqualSelectionType(colorGrouping, GROUP_BY_SCORE)
            ? measure
            : getGroupVal(colorGrouping, e);
      const symbolGroupVal = getGroupVal(symbolGrouping, e);

      if (!lineNames.find((n) => isEqualSelectionType(n, colorGroupVal))) {
        lineNames.push(colorGroupVal);
      }
      const lineIndex = lineNames.findIndex((n) =>
        isEqualSelectionType(n, colorGroupVal),
      );
      colorGroups[colorGroupVal.value] = colorGroups[colorGroupVal.value] ?? {};
      colorGroups[colorGroupVal.value][colorGroupVal.type] =
        colorGroups[colorGroupVal.value][colorGroupVal.type] ??
        Object.keys(colorGroups).length % CHART_COLOR_CLASSNAMES.length;
      symbolGroups[symbolGroupVal.value] =
        symbolGroups[symbolGroupVal.value] ?? {};
      symbolGroups[symbolGroupVal.value][symbolGroupVal.type] =
        symbolGroups[symbolGroupVal.value][symbolGroupVal.type] ??
        Object.keys(symbolGroups).length % D3_SYMBOLS.length;

      return {
        point: point,
        className:
          CHART_COLOR_CLASSNAMES[
            colorGroups[colorGroupVal.value][colorGroupVal.type]
          ],
        symbolIndex: isEqualSelectionType(symbolGrouping, GROUP_BY_NONE)
          ? 0
          : (symbolGroups[symbolGroupVal.value][symbolGroupVal.type] ?? 0),
        lineIndex: Math.max(lineIndex, 0),
        colorGroup: colorGroupVal,
        symbolGroup: symbolGroupVal,
        isSelected: !!selectedExperiments[e.id],
        metadata: {
          id: e.id,
          name: e.name,
          last_updated: e.last_updated,
          metadata: e.metadata,
          count: 1,
        },
      };
    });
  });

  // a little bit of a hack so that the table can sync up colors easier with the table
  // ideally we should get the color map from the query
  let colorIndex = 0;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const sortedColorGroups = Object.fromEntries(
    Object.entries(colorGroups)
      .toSorted((a, b) => colorSortFn(a[0], b[0]))
      .map(([groupName, groupTypes]) => [
        groupName,
        Object.fromEntries(
          Object.entries(groupTypes).map(([type]) => {
            return [type, colorIndex++ % CHART_COLOR_CLASSNAMES.length];
          }),
        ),
      ]),
  ) as Record<string, Record<SelectionTypesEnum, number>>;
  const colorSortedPointData = pointData.map((d) => ({
    ...d,
    className:
      CHART_COLOR_CLASSNAMES[
        sortedColorGroups[d.colorGroup.value][d.colorGroup.type]
      ],
  }));
  return {
    pointData: colorSortedPointData,
    colorMap: sortedColorGroups,
    symbolMap: symbolGroups,
    lineNames,
  };
}

const getMetricVal = ({
  metric,
  scores,
  metrics,
  metadata,
}: {
  metric: SelectionType;
  scores: { [key: string]: number };
  metrics: { [key: string]: number };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  metadata: Record<any, any>;
}): number | string | null => {
  if (!metric) {
    return null;
  }

  switch (metric.type) {
    case "metadata": {
      const v = getObjectPathValue(metadata, metric.value);
      if (v == null) {
        return null;
      }
      const parsed = parseFloat(v);
      if (!Number.isNaN(parsed)) {
        return parsed;
      }
      return v;
    }
    case "metric": {
      return metrics[metric.value];
    }
    default: {
      return scores[metric.value];
    }
  }
};

const getGroupVal = (
  grouping: SelectionType,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  e: { metadata?: any },
): SelectionType => {
  switch (grouping.type) {
    case "metadata": {
      const value = getObjectPathValue(e.metadata, grouping.value) ?? "null";
      return {
        value,
        type: "none",
      };
    }
    default:
      return grouping;
  }
};

export function groupForBarChart(
  groupedData: ReturnType<typeof groupExperimentData>,
  xMetric: SelectionType,
  aggregationType?: AggregationType,
) {
  const groups: Record<string, Record<SelectionTypesEnum, boolean>> = {};
  const subGroups: Record<string, Record<SelectionTypesEnum, boolean>> = {};
  const grouped: Record<
    string,
    Record<
      SelectionTypesEnum,
      Record<
        string,
        Record<
          SelectionTypesEnum,
          { metadata: PointMetadata; className: string; values: number[] }
        >
      >
    >
  > = {};
  Object.values(groupedData.pointData).forEach(
    ({ point, className, colorGroup, metadata }) => {
      if (point[1] == null) {
        return;
      }

      grouped[point[0]] = grouped[point[0]] ?? {};
      grouped[point[0]][xMetric.type] = grouped[point[0]][xMetric.type] ?? {};
      grouped[point[0]][xMetric.type][colorGroup.value] =
        grouped[point[0]][xMetric.type][colorGroup.value] ?? {};
      grouped[point[0]][xMetric.type][colorGroup.value][colorGroup.type] =
        grouped[point[0]][xMetric.type][colorGroup.value][colorGroup.type] ?? {
          metadata,
          className,
          values: [],
        };
      grouped[point[0]][xMetric.type][colorGroup.value][
        colorGroup.type
      ].values.push(point[1]);

      groups[point[0]] = groups[point[0]] ?? {};
      groups[point[0]][xMetric.type] = true;
      subGroups[colorGroup.value] = subGroups[colorGroup.value] ?? {};
      subGroups[colorGroup.value][colorGroup.type] = true;
    },
  );

  const categories = Object.entries(groups)
    .flatMap(
      ([name, v]) =>
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        Object.entries(v).map(([type]) => ({
          value: name,
          type,
        })) as SelectionType[],
    )
    .toSorted((a, b) => colorSortFn(a.value, b.value));
  const subCategories = Object.entries(subGroups)
    .flatMap(
      ([name, v]) =>
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        Object.entries(v).map(([type]) => ({
          value: name,
          type,
        })) as SelectionType[],
    )
    .toSorted((a, b) => colorSortFn(a.value, b.value));

  const aggregated: Record<
    string,
    Record<
      SelectionTypesEnum,
      Record<
        string,
        Record<
          SelectionTypesEnum,
          { metadata: PointMetadata; colorClassName: string; value: number }
        >
      >
    >
  > = {};

  for (const [groupName, groupTypes] of Object.entries(grouped)) {
    for (const [_groupType, groupValues] of Object.entries(groupTypes)) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      const groupType = _groupType as SelectionTypesEnum;
      for (const [subGroupName, subGroupTypes] of Object.entries(groupValues)) {
        for (const [_subGroupType, subGroupValues] of Object.entries(
          subGroupTypes,
        )) {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          const subGroupType = _subGroupType as SelectionTypesEnum;
          aggregated[groupName] = aggregated[groupName] ?? {};
          aggregated[groupName][groupType] =
            aggregated[groupName][groupType] ?? {};
          aggregated[groupName][groupType][subGroupName] =
            aggregated[groupName][groupType][subGroupName] ?? {};

          let value = 0;
          switch (aggregationType) {
            case "min":
              value = Math.min(...subGroupValues.values);
              break;
            case "max":
              value = Math.max(...subGroupValues.values);
              break;
            case "median":
              const { values } = subGroupValues;
              const n = values.length;
              value =
                n === 0
                  ? 0
                  : n % 2 === 1
                    ? values[Math.floor((n - 1) / 2)]
                    : (values[Math.floor(n / 2 - 1)] +
                        values[Math.floor(n / 2)]) /
                      2;
              break;
            case "sum":
              value = subGroupValues.values.reduce((avg, v) => avg + v, 0);
              break;
            case "avg":
            default:
              value = subGroupValues.values.reduce(
                (avg, v, _, list) => avg + v / list.length,
                0,
              );
          }
          aggregated[groupName][groupType][subGroupName][subGroupType] = {
            metadata: subGroupValues.metadata,
            colorClassName: subGroupValues.className,
            value,
          };
        }
      }
    }
  }

  return {
    chartData: aggregated,
    categories,
    subGroups: subCategories,
  };
}

export function groupForLineChart(
  groupedData: ReturnType<typeof groupExperimentData>,
  symbolGrouping: SelectionType,
) {
  const groupedByXValue = groupedData.pointData.reduce(
    (acc, d) => {
      const [x, y] = d.point;
      acc[x] = acc[x] ?? {
        x,
        y: new Array(groupedData.lineNames.length).fill(null),
        isSelected: d.isSelected,
        metadata: {
          ...d.metadata,
          colorGroup: d.colorGroup,
          symbolGroup: d.symbolGroup,
        },
      };
      acc[x].y[d.lineIndex] =
        y == null
          ? null
          : {
              value: y,
              symbolIndex: isEqualSelectionType(symbolGrouping, GROUP_BY_NONE)
                ? undefined
                : d.symbolIndex,
              symbolGroup: d.symbolGroup,
            };

      return acc;
    },
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    {} as Record<
      string,
      Datapoint<
        PointMetadata & {
          colorGroup: SelectionType;
          symbolGroup: SelectionType;
        }
      >
    >,
  );

  return {
    chartData: Object.values(groupedByXValue),
    lineNames: groupedData.lineNames,
  };
}

function colorSortFn(a: string, b: string) {
  if (a === b) return 0;
  if (b === "null") return -1;
  else if (a === "null") return 1;
  else if (a < b) return -1;
  else if (a > b) return 1;
  else return 0;
}
