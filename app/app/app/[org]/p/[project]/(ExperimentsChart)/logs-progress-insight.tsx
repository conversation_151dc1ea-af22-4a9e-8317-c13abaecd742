"use client";

import { useEntityStorage } from "#/lib/clientDataStorage";
import { Chart } from "#/ui/charts/Chart";
import { CHART_COLOR_CLASSNAMES, nextColor } from "#/ui/color";
import { useMultiLine<PERSON>hart } from "#/ui/charts/multi-line-chart";
import { type SelectionTypesEnum } from "#/ui/charts/selectionTypes";
import { Combobox } from "#/ui/combobox/combobox";
import { cn } from "#/utils/classnames";
import { MetadataField, useDBQuery } from "#/utils/duckdb";
import { normalizeArrowForJSON } from "#/utils/object";
import { unfoldNestedFields } from "@braintrust/local/query";
import {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { FilterPopover } from "./FilterPopover";
import { useIsClient } from "#/utils/use-is-client";
import { LOG_SUMMARY_BUCKETS } from "#/ui/use-log-summary";
import pluralize from "pluralize";
import { Skeleton } from "#/ui/skeleton";
import { ChartTooltip } from "#/ui/charts/tooltip/chart-tooltip";

const MAX_NUMBER_OF_SCORES = 5;
const MAX_EXPERIMENTS = 500;

export const LOG_TIME_BUCKETS = ["hour", "day", "week", "month"] as const;
export type LogTimeBucket = (typeof LOG_TIME_BUCKETS)[number];

export const LogsProgressInsight = forwardRef<
  {},
  {
    experimentsQuery: string | null;
    experimentsSignals: number[];
    scoreNames: string[];
    scoresLoading?: boolean;
    onExperimentClick: ({
      id,
      experimentName,
    }: {
      id: string;
      experimentName: string;
    }) => void;
    title?: string;
    className?: string;
    insightIdentifier: string;
    bucket?: LogTimeBucket;
    setBucket?: (bucket: LogTimeBucket) => void;
    hidePoints?: boolean;
    hasMetadata?: boolean;
  }
>(function LogsProgressInsight(
  {
    experimentsQuery,
    experimentsSignals,
    scoreNames,
    scoresLoading,
    onExperimentClick,
    title = "Experiment score progress",
    className,
    insightIdentifier,
    bucket,
    setBucket,
    hidePoints,
    hasMetadata,
  },
  ref,
) {
  const { data, hasLoaded: hasExperimentsLoaded } = useDBQuery(
    experimentsQuery &&
      `WITH base AS (
        SELECT * FROM (${experimentsQuery})
        WHERE last_updated IS NOT NULL
        ORDER BY last_updated DESC NULLS LAST
        LIMIT ${MAX_EXPERIMENTS}
      )
      SELECT * FROM base
      ORDER BY last_updated ASC NULLS LAST
    `,
    experimentsSignals,
  );

  const { metadataFields, loading: metadataFieldsLoading } = useMetadataFields(
    experimentsQuery,
    experimentsSignals,
    hasMetadata,
  );

  const [excludedScores, setExcludedScores] = useEntityStorage({
    entityType: "charts",
    entityIdentifier: insightIdentifier,
    key: "excludedScores",
  });

  const [selectedScores, setSelectedScores] = useState<Record<string, number>>(
    {},
  );

  const hasInitialized = useRef(false);
  useEffect(() => {
    if (scoresLoading || hasInitialized.current) {
      return;
    }
    const initialScores = initializeSelectedScores(scoreNames, excludedScores);
    setSelectedScores(initialScores);
    setExcludedScores(
      (existing) =>
        existing ?? scoreNames.filter((s) => initialScores[s] == null),
    );

    hasInitialized.current = true;
  }, [scoresLoading, excludedScores, scoreNames, setExcludedScores]);

  const toggleSelectedScores = useCallback(
    (scoreNames: string[]) => {
      setSelectedScores((existing) => {
        return {
          ...existing,
          ...scoreNames.reduce((acc, s) => {
            return {
              ...acc,
              [s]:
                existing[s] == null
                  ? nextColor(
                      Object.values({ ...existing, ...acc }),
                      CHART_COLOR_CLASSNAMES,
                    )[0]
                  : null,
            };
          }, {}),
        };
      });

      // This allows us to remember the exact set of scores we want to exclude.
      setExcludedScores((existing) =>
        toggleExcludedScores(existing ?? [], scoreNames),
      );
    },
    [setExcludedScores],
  );

  const visibleScores = useMemo(() => {
    return Object.entries(selectedScores)
      .filter(([_, v]) => v != null)
      .map(([k]) => k);
  }, [selectedScores]);

  const [groupBy, setGroupBy] = useEntityStorage({
    entityType: "charts",
    entityIdentifier: insightIdentifier,
    key: "groupBy",
  });

  const [selectedMetadataField, setSelectedMetadataField] = useEntityStorage({
    entityType: "charts",
    entityIdentifier: insightIdentifier,
    key: "groupByMetadataField",
  });

  const [groupByMetric, setGroupByMetric] = useEntityStorage({
    entityType: "charts",
    entityIdentifier: insightIdentifier,
    key: "groupByMetric",
  });

  const isClient = useIsClient();

  const loading = !hasExperimentsLoaded || metadataFieldsLoading || !isClient;
  // for backwards compatibility
  useEffect(() => {
    if (loading) return;
    if (groupBy !== "" && !selectedMetadataField) {
      setGroupBy("");
      setGroupByMetric("");
      return;
    } else if (
      groupBy !== "" &&
      selectedMetadataField &&
      !groupByMetric &&
      visibleScores.length > 0
    ) {
      setGroupByMetric(visibleScores[0] ?? "");
    }
  }, [
    loading,
    groupBy,
    setGroupBy,
    setGroupByMetric,
    selectedMetadataField,
    groupByMetric,
    visibleScores,
    metadataFields,
  ]);

  const { experimentsWithSelectedScores, seriesNames } = useMemo(() => {
    if (loading || !data || (scoreNames.length === 0 && !groupByMetric)) {
      return { seriesNames: [] };
    }

    const experimentsWithSelectedScores = (data?.toArray() ?? []).map((r) => {
      const row = JSON.parse(JSON.stringify(r, normalizeArrowForJSON));
      const experimentData = {
        scores: Object.fromEntries(
          (groupByMetric ? [groupByMetric] : visibleScores).map((scoreName) => [
            scoreName,
            row[scoreName],
          ]),
        ),
        name: row.name,
        id: row.id,
        runDate: row.last_updated,
        count: row.num_examples,
        //tag: row.tag,
        metadata: JSON.parse(row?.metadata ?? "{}"),
      };
      return experimentData;
    });

    if (
      !hasMetadata ||
      !groupBy ||
      (groupBy === "metadata" && !selectedMetadataField)
    ) {
      return {
        experimentsWithSelectedScores,
        seriesNames: visibleScores,
      };
    }

    const groupValues = new Set<string>();
    const groupedExperimentsByGroupValue: {
      [key: string]: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        __experimentData: any;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        [groupValue: string]: any;
      };
    } = experimentsWithSelectedScores.reduce(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      (acc: any, d: any) => {
        acc[d.id] = acc[d.id] ?? {
          __experimentData: d,
        };

        const groupValue =
          groupBy === "metadata"
            ? getObjectPathValue(d.metadata, selectedMetadataField)
            : null;
        if (groupValue == null) {
          return acc;
        }
        groupValues.add(groupValue);
        acc[d.id][groupValue] = d;
        return acc;
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      {} as Record<string, any>,
    );

    const sortedGroupValues = Array.from(groupValues).toSorted();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    const unpivotedData: any[] = [];
    for (const experimentsByGroupValue of Object.values(
      groupedExperimentsByGroupValue,
    )) {
      const newData = {
        ...experimentsByGroupValue.__experimentData,
        scores: {},
      };

      for (const groupValue of sortedGroupValues) {
        const experimentData = experimentsByGroupValue[groupValue] ?? null;
        newData.scores[groupValue] = !experimentData
          ? null
          : experimentData.scores[groupByMetric];
      }

      unpivotedData.push(newData);
    }

    return {
      experimentsWithSelectedScores: unpivotedData,
      seriesNames: sortedGroupValues,
    };
  }, [
    loading,
    data,
    hasMetadata,
    groupBy,
    groupByMetric,
    scoreNames,
    visibleScores,
    selectedMetadataField,
  ]);

  const seriesColorMap = useMemo(() => {
    return Object.fromEntries(
      Object.entries(selectedScores).map(([k, v]) => [
        k,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        { score: v } as Record<SelectionTypesEnum, number>,
      ]),
    );
  }, [selectedScores]);

  const bucketPlaceholder =
    `No ${visibleScores.length ? "selected" : ""} scores logged` +
    (bucket ? ` in the past ${LOG_SUMMARY_BUCKETS} ${pluralize(bucket)}` : "");

  const { chartProps, leftAxisProps } = useMultiLineChart({
    height: 140,
    data:
      experimentsWithSelectedScores?.map((exp, i) => ({
        x: i,
        y: Object.values(exp.scores).map((v) =>
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          v == null ? null : { value: v as number },
        ),
        metadata: {
          id: exp.id,
          experimentName: exp.name,
          runDate: exp.runDate,
          count: exp.count,
        },
      })) ?? [],
    seriesMetadata: seriesNames.map((name) => ({
      value: name,
      type: "score",
    })),
    seriesColorMap: groupByMetric === "" ? seriesColorMap : undefined,
    pointSize: hidePoints ? 0 : 4,
    onClick: ({ id, experimentName }) =>
      onExperimentClick({
        id,
        experimentName,
      }),
    renderTooltip: (
      { y: values, metadata: { experimentName, runDate, count } },
      labels,
      nearestSeriesIndex,
    ) => (
      <ChartTooltip
        values={values}
        title={experimentName}
        runDate={runDate}
        count={count}
        labels={labels.map((l) => l.value)}
        seriesColorMap={groupByMetric === "" ? selectedScores : undefined}
        valueFormatOptions={{
          maximumFractionDigits: 1,
          style: "percent",
        }}
        nearestIndex={nearestSeriesIndex}
      />
    ),
    isOrdinalX: true,
  });

  return (
    <article className={cn("w-full", className)}>
      <div className="mb-4 flex items-center gap-2">
        <h2 className="flex-auto shrink-0 text-sm text-primary-600">{title}</h2>
        {bucket && setBucket && !loading && (
          <Combobox
            options={LOG_TIME_BUCKETS.map((bucket) => ({
              value: bucket,
              label: `Group by ${bucket}`,
            }))}
            selectedValue={bucket}
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            onChange={(bucket) => bucket && setBucket(bucket as LogTimeBucket)}
            variant="button"
            buttonSize="xs"
            className="text-xs"
            noSearch
          />
        )}
        <FilterPopover
          loading={loading}
          scoreNames={scoreNames}
          selectedScores={selectedScores}
          metadataFields={metadataFields ?? []}
          selectedMetadataField={selectedMetadataField}
          onToggleSelectedScores={toggleSelectedScores}
          hasMetadata={hasMetadata}
          groupBy={groupBy}
          groupByMetric={groupByMetric}
          setGroupBy={(groupBy, metadataField, score) => {
            setGroupBy(groupBy);
            setSelectedMetadataField(metadataField);
            setGroupByMetric(score);
          }}
          setGroupByMetric={setGroupByMetric}
        />
      </div>
      {!isClient ? (
        <Skeleton className="h-[140px]" />
      ) : groupBy === "" && visibleScores.length === 0 ? (
        <div className="flex h-[140px] flex-col items-center justify-center">
          <div className="text-sm text-primary-400">
            {loading
              ? null
              : scoreNames.length > 0
                ? "No scores selected"
                : bucket
                  ? bucketPlaceholder
                  : "Set up scoring functions in your experiment to view score data"}
          </div>
        </div>
      ) : (
        <Chart
          {...chartProps}
          placeholder={bucketPlaceholder}
          loading={loading}
          leftAxisProps={{
            ...leftAxisProps,
            showGridLines: true,
            tickFormatter: (tick, i) =>
              tick.toLocaleString(undefined, {
                style: "percent",
                maximumFractionDigits: 0,
              }),
          }}
        />
      )}
    </article>
  );
});

function useMetadataFields(
  experimentsQuery: string | null,
  experimentsSignals: number[],
  hasMetadata?: boolean,
) {
  const {
    data: metadataStructure,
    loading,
    hasLoaded,
  } = useDBQuery(
    experimentsQuery && hasMetadata
      ? `
      SELECT
        1,
        json_group_structure(${MetadataField}) as metadata,
      FROM (
        SELECT * FROM ((${experimentsQuery}))
        WHERE last_updated IS NOT NULL
        LIMIT ${MAX_EXPERIMENTS}
      ) "t"`
      : null,
    experimentsSignals,
  );

  const metadataFields = useMemo(() => {
    if (!metadataStructure) return null;
    const row = metadataStructure?.toArray()?.[0] ?? {};
    if (!row?.metadata) return null;

    const parsedMetadata = JSON.parse(row.metadata);
    if (!(parsedMetadata instanceof Object)) return [];
    return parsedMetadata ? unfoldNestedFields([], parsedMetadata) : [];
  }, [metadataStructure]);

  return { metadataFields, loading, hasLoaded: !hasMetadata || hasLoaded };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function getObjectPathValue(obj: Record<any, any>, pathStr: string) {
  const path: string[] = JSON.parse(pathStr);
  if (!(path instanceof Object) || !Array.isArray(path)) return null;

  const result = path.reduce((o, v) => (o && o[v] ? o[v] : null), obj);
  if (result instanceof Object) return null;
  return result;
}

function initializeSelectedScores(
  scoreNames: string[],
  excludedScores: string[] | null,
): Record<string, number> {
  const usedScores: (number | null)[] = [];
  const selectedScores: Record<string, number> = {};
  for (const score of scoreNames) {
    if (excludedScores?.includes(score)) {
      continue;
    }
    selectedScores[score] = nextColor(usedScores, CHART_COLOR_CLASSNAMES)[0];
    usedScores.push(selectedScores[score]);

    if (!excludedScores && usedScores.length >= MAX_NUMBER_OF_SCORES) {
      break;
    }
  }
  return selectedScores;
}

function toggleExcludedScores(excluded: string[], newScoreNames: string[]) {
  const newExcludedScores = new Set(excluded);
  for (const s of newScoreNames) {
    if (newExcludedScores.has(s)) {
      newExcludedScores.delete(s);
    } else {
      newExcludedScores.add(s);
    }
  }
  return Array.from(newExcludedScores);
}
