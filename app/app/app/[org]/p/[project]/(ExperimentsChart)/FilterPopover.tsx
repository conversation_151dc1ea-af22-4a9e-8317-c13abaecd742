"use client";

import { Skeleton } from "#/ui/skeleton";
import { Combobox } from "#/ui/combobox/combobox";

export const GROUP_BY_OPTIONS = ["", "metadata"];

type FilterPopoverProps = {
  loading: boolean;
  scoreNames: string[];
  selectedScores: Record<string, number | null>;
  onToggleSelectedScores: (s: string[]) => void;
  hasMetadata?: boolean;
  metadataFields: string[][];
  selectedMetadataField: string;
  groupBy: string;
  groupByMetric: string;
  setGroupBy: (
    dimension: string,
    dimensionName: string,
    metric: string,
  ) => void;
  setGroupByMetric: (m: string) => void;
};

export const FilterPopover = ({
  loading,
  scoreNames,
  selectedScores,
  onToggleSelectedScores,
  hasMetadata,
  metadataFields,
  selectedMetadataField,
  groupBy,
  groupByMetric,
  setGroupBy,
  setGroupByMetric,
}: FilterPopoverProps) => {
  const scorePicker = loading ? (
    <>
      <Skeleton className="h-4" />
      <Skeleton className="h-4" />
      <Skeleton className="h-4" />
      <Skeleton className="h-4" />
    </>
  ) : (
    <Combobox
      options={scoreNames.map((n) => ({
        label: n,
        value: n,
      }))}
      onChange={(v) => {
        if (!v) return;
        if (groupBy === "") {
          onToggleSelectedScores([v]);
          return;
        }

        setGroupByMetric(v);
      }}
      placeholderLabel="Scores"
      renderComboboxDisplayLabel={() => "Scores"}
      variant="button"
      buttonSize="xs"
      bottomActions={
        groupBy === ""
          ? [
              {
                label: "Select all",
                onSelect: () => {
                  onToggleSelectedScores(
                    scoreNames.filter((s) => selectedScores[s] == null),
                  );
                },
                disabled: scoreNames.every((v) => selectedScores[v] != null),
              },
              {
                label: "Deselect all",
                onSelect: () => {
                  onToggleSelectedScores(
                    scoreNames.filter((s) => selectedScores[s] != null),
                  );
                },
                disabled: scoreNames.every((v) => selectedScores[v] == null),
              },
            ]
          : undefined
      }
      selectedValue={groupBy === "" ? undefined : groupByMetric}
      selectedValues={
        groupBy === ""
          ? Object.keys(selectedScores).filter((k) => selectedScores[k] != null)
          : undefined
      }
      searchPlaceholder="Select scores"
      noResultsLabel="No scores"
      stayOpenOnChange={groupBy === ""}
    />
  );
  if (!hasMetadata) {
    return scorePicker;
  }

  return (
    <div className="flex gap-2">
      <Combobox
        options={[
          { label: "None", value: "" },
          ...metadataFields.map((fieldPath) => ({
            value: JSON.stringify(fieldPath),
            label: fieldPath.join("."),
          })),
        ]}
        onChange={(v) => {
          if (v === "") {
            setGroupBy("", "", "");
            return;
          }

          setGroupBy(
            "metadata",
            v ?? "",
            (groupByMetric ||
              scoreNames.find((k) => selectedScores[k] != null)) ??
              scoreNames[0] ??
              "",
          );
        }}
        variant="button"
        buttonSize="xs"
        placeholderLabel="Group by"
        renderComboboxDisplayLabel={() => "Group by"}
        selectedValue={selectedMetadataField}
        searchPlaceholder="Select metadata grouping"
        noResultsLabel="No metadata fields"
        onKeyDown={(e) => {
          if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
            e.stopPropagation();
          }
        }}
      />
      {scorePicker}
    </div>
  );
};
