export function getObjectPathValue(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  obj: Record<any, any>,
  pathStr: string,
): string | null {
  if (!pathStr) return null;
  let path: string[];
  try {
    path = JSON.parse(pathStr);
  } catch {
    console.warn("invalid pathStr", pathStr);
    return null;
  }
  if (!(path instanceof Object) || !Array.isArray(path)) return null;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const result: unknown | Record<any, any> | null = path.reduce(
    (o, v) => (o && o[v] != null ? o[v] : null),
    obj,
  );
  if (result instanceof Object) return null;
  return `${result}`;
}
