import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import {
  type ProjectSummary,
  type getProjectSummary,
} from "#/app/app/[org]/org-actions";
import { pluralizeWithCount } from "#/utils/plurals";
import { useCallback, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { Button } from "#/ui/button";
import { useOrg } from "#/utils/user";
import { Combobox } from "#/ui/combobox/combobox";
import { deleteEntities, type moveEntities } from "./batch-entity-actions";
import { type EntityType } from "./batch-entity-types";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";

function entityTypeLabel(x: EntityType): string {
  switch (x) {
    case "prompt_session":
      return "playground";
    default:
      return x;
  }
}

export type EntityBatchActions = {
  deleteEntities: (args: { entityIds: string[] }) => void;
  moveEntities: (args: { entityIds: string[] }) => void;
};

export const useEntityBatchActions = ({
  onUpdate,
  entityType,
  entityName,
  additionalDeleteConfirmationDescription,
}: {
  onUpdate?: () => void;
  entityType: EntityType;
  entityName: string;
  additionalDeleteConfirmationDescription?: string;
}) => {
  const org = useOrg();
  const { getToken } = useAuth();

  const [activeAction, setActiveAction] = useState<{
    type: "batch-delete" | "move";
    data: { entityIds: string[] };
  } | null>(null);
  const { entityIds } = activeAction?.data || {};

  const toastError = useCallback(
    (action: "batch-delete" | "move", msg?: string) => {
      const actionLabel = action === "batch-delete" ? "delete" : action;
      toast.error(
        `Could not ${actionLabel} ${pluralizeWithCount(
          entityIds?.length || 0,
          "item",
          "items",
        )}`,
        {
          description: msg ? (
            <> {msg} </>
          ) : (
            <>
              Internal error. Please contact{" "}
              <a href="mailto:<EMAIL>" className="underline">
                <EMAIL>
              </a>{" "}
              for help.
            </>
          ),
        },
      );
    },
    [entityIds?.length],
  );

  const deleteEntity = async () => {
    const ids = entityIds;

    if (!ids || ids.length == 0) {
      toastError("batch-delete");
      return;
    }

    try {
      await deleteEntities({
        entityType,
        entityIds: [ids[0], ...ids.slice(1)],
      });
    } catch (e) {
      toastError("batch-delete", `${e}`);
      return;
    }

    toast(`Deleted ${ids.length || 0} ${ids.length === 1 ? "item" : "items"}`);
    onUpdate?.();
  };

  const moveEntity = async ({ entityName }: { entityName: string }) => {
    const ids = entityIds;

    if (!ids || ids.length == 0 || !org.name) {
      toastError("move");
      return;
    }

    const resp = await invokeServerAction<typeof moveEntities>({
      fName: "moveEntities",
      args: {
        entityType,
        entityIds: [ids[0], ...ids.slice(1)],
        orgName: org.name,
        entityName,
      },
      getToken,
    });

    if (resp.status === "success") {
      toast(`Moved ${ids.length || 0} ${ids.length === 1 ? "item" : "items"}`);
      onUpdate?.();
    } else {
      console.error(resp);
      toastError("move", resp.message);
    }
  };

  const modals = (
    <>
      {activeAction?.type == "batch-delete" ? (
        <ConfirmationDialog
          onConfirm={deleteEntity}
          open={true}
          onOpenChange={(open) => {
            if (!open) setActiveAction(null);
          }}
          title={`Delete ${entityTypeLabel(entityType)}`}
          description={`Are you sure you want to delete ${pluralizeWithCount(
            entityIds?.length || 0,
            entityTypeLabel(entityType),
            entityTypeLabel(entityType) + "s",
          )}?${additionalDeleteConfirmationDescription ? ` ${additionalDeleteConfirmationDescription}` : ""}`}
          confirmText={"Delete selected"}
        />
      ) : activeAction?.type == "move" ? (
        <MoveProjectDialog
          orgName={org.name!}
          projectName={entityName}
          open={true}
          onOpenChange={(open) => {
            if (!open) setActiveAction(null);
          }}
          entityLabel={entityTypeLabel(entityType)}
          itemCount={entityIds?.length || 0}
          onConfirm={(entityName) => {
            moveEntity({ entityName });
          }}
        />
      ) : null}
    </>
  );

  return {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    actions: {
      deleteEntities: ({ entityIds }: { entityIds: string[] }) => {
        if (!entityIds) {
          toastError("batch-delete");
          return;
        }
        setActiveAction({
          type: "batch-delete",
          data: {
            entityIds,
          },
        });
      },
      moveEntities: ({ entityIds }: { entityIds: string[] }) => {
        if (!entityIds) {
          toastError("move");
          return;
        }
        setActiveAction({
          type: "move",
          data: {
            entityIds,
          },
        });
      },
    } as EntityBatchActions,
    modals: modals,
  };
};

export function MoveProjectDialog({
  orgName,
  open,
  onOpenChange,
  entityLabel,
  itemCount,
  onConfirm,
}: {
  orgName: string;
  projectName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entityLabel: string;
  itemCount: number;
  onConfirm: (projectName: string) => void;
}) {
  const { data } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  const [selectedProject, setSelectedProject] = useState<
    ProjectSummary | undefined
  >(undefined);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{`Move ${entityLabel}`}</DialogTitle>
          <DialogDescription>
            {`Are you sure you want to move ${pluralizeWithCount(
              itemCount,
              entityLabel,
              entityLabel + "s",
            )}? `}
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-start">
          <Combobox
            options={(data || []).map((option) => ({
              value: option.project_id,
              label: option.project_name,
              created: option.project_created_at,
            }))}
            onChange={(value) => {
              const project = data?.find((p) => p.project_id === value);
              setSelectedProject(project);
            }}
            selectedValue={selectedProject?.project_name}
            placeholderLabel={selectedProject?.project_name || "Select project"}
            searchPlaceholder="Find a project"
            variant="inline"
            contentWidth={500}
            align="start"
            buttonClassName="max-w-full break-words text-left"
            iconClassName="text-primary-500 ml-1"
          />
        </div>
        <DialogFooter>
          <Button
            type="button"
            onClick={() => {
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            disabled={!selectedProject}
            onClick={() => {
              if (!selectedProject) return;
              onConfirm(selectedProject.project_name);
              onOpenChange(false);
            }}
          >
            Move {pluralizeWithCount(itemCount, entityLabel, entityLabel + "s")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
