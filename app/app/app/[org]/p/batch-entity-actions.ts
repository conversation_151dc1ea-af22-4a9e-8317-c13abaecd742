"use server";

import type { AuthLookup } from "#/utils/server-util";
import { HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  setDeletedAtObjects,
  extractSingularRow,
  resolveNonVirtualTableInfo,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import { doubleQuote } from "#/utils/sql-utils";
import { type EntityType } from "./batch-entity-types";

export type MoveEntitiesInput = {
  entityType: EntityType;
  entityIds: [string, ...string[]];
  orgName: string;
  entityName: string;
};

export type MoveEntitiesOutput =
  | { status: "success" }
  | { status: "error"; message: string }
  | { status: "internal_error"; message?: undefined };
export async function moveEntities(
  { entityType, entityIds, orgName, entityName }: MoveEntitiesInput,
  authLookupRaw?: AuthLookup,
): Promise<MoveEntitiesOutput> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(entityType && entityIds.length && orgName && entityName)) {
    return { status: "error", message: "Missing required input" };
  }

  const {
    query: projectQuery,
    queryParams,
    notFoundErrorMessage: projectNotFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "project",
      aclPermission: "create",
      overrideRestrictObjectType: entityType,
    },
    filters: {
      name: entityName,
      org_name: orgName,
    },
  });

  const {
    query: entityQuery,
    notFoundErrorMessage: entitiesNotFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    startingParams: queryParams,
    permissionInfo: {
      aclObjectType: entityType,
      aclPermission: "update",
    },
    filters: {
      id: entityIds,
    },
  });

  const objectTableName = doubleQuote(
    resolveNonVirtualTableInfo(entityType).tableName,
  );

  const fullQuery = `
    with
    project_id as (
        select id from (${projectQuery}) "t"
        limit 1
    ),
    entity_ids as (
        select id from (${entityQuery}) "t"
    ),
    num_entity_ids as (
        select count(*) cnt from entity_ids
    ),
    has_permission as (
        select (
            project_id.id is not null and
            num_entity_ids.cnt = ${queryParams.add(entityIds.length)}
        ) value
        from num_entity_ids left join project_id on true
    ),
    entities_to_update as (
        select e.id, e.name,
          case
            -- If no conflict, keep original name
            when not exists (
              select 1
              from ${objectTableName} existing
              where existing.project_id = (select id from project_id)
                and existing.deleted_at is null
                and existing.name = e.name
                and existing.id != e.id
            ) then e.name
            -- If first conflict, add "moved from {project}"
            when not exists (
              select 1
              from ${objectTableName} existing
              where existing.project_id = (select id from project_id)
                and existing.deleted_at is null
                and existing.name = e.name || ' (moved from ' || p.name || ')'
                and existing.id != e.id
            ) then e.name || ' (moved from ' || p.name || ')'
            -- If still conflict, add UUID
            else e.name || ' ' || substr(gen_random_uuid()::text, 1, 8) || ' (moved from ' || p.name || ')'
          end as new_name
        from ${objectTableName} e
        join projects p on e.project_id = p.id
        where e.id in (select id from entity_ids)
    ),
    update_results as (
        update ${objectTableName}
        set
          project_id = (select id from project_id),
          name = entities_to_update.new_name
        from entities_to_update
        where
            (select value from has_permission)
            and ${objectTableName}.id = entities_to_update.id
        returning ${objectTableName}.id
    )
    select update_results.id from update_results limit 1
  `;

  const supabase = getServiceRoleSupabase();
  try {
    extractSingularRow({
      rows: (await supabase.query(fullQuery, queryParams.params)).rows,
      notFoundErrorMessage: [
        projectNotFoundErrorMessage,
        entitiesNotFoundErrorMessage,
      ].join(" or "),
    });
    return { status: "success" };
  } catch (e) {
    if (e instanceof HTTPError) {
      return { status: "error", message: e.message };
    } else {
      console.error("Error moving entities", e);
      return { status: "internal_error" };
    }
  }
}

export async function deleteEntities(
  {
    entityType,
    entityIds,
  }: {
    entityType: EntityType;
    entityIds: [string, ...string[]];
  },
  authLookupRaw?: AuthLookup,
): Promise<void> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  await setDeletedAtObjects({
    authLookup,
    permissionInfo: {
      aclObjectType: entityType,
    },
    filters: {
      id: entityIds,
    },
    fullResultsSize: entityIds.length,
  });
}
