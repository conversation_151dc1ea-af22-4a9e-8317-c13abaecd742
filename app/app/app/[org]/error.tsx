"use client";

import { useEffect, useState } from "react";
import * as Sen<PERSON> from "@sentry/nextjs";
import { useOrg } from "#/utils/user";
import { MultiTenantApiURL } from "#/utils/user-types";
import { ErrorPageContent } from "#/app/global-error";
import { Button } from "#/ui/button";
import { inputClassName } from "#/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import {
  BROWSER_EXTENSION_ERROR_MESSAGE,
  GENERAL_ERROR_MESSAGE,
  isBrowserExtensionError,
} from "#/utils/errors/error-helpers";

function ShareErrorForm({ error }: { error: Error & { digest?: string } }) {
  const [didShare, setDidShare] = useState(false);
  const onShare = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (Sentry) {
      setDidShare(true);

      const formData = new FormData(e.currentTarget);
      const description = formData.get("description");

      Sentry.captureMessage("On-prem user shared error trace", {
        extra: {
          isOnPremShare: true,
          message: error.message,
          stack: error.stack,
          description,
        },
        tags: {
          page: "org-page-error",
        },
      });
    }
  };
  return (
    <form onSubmit={onShare} id="share">
      <div className="space-y-4">
        <h4 className="font-medium">Crash description</h4>
        <pre className="font-mono text-sm font-normal break-words whitespace-pre-wrap">
          {error.message}
        </pre>
        {!didShare && (
          <>
            <p className="text-sm text-primary-600">
              What were you doing when the crash happened?
            </p>

            <input
              type="text"
              id="description"
              name="description"
              placeholder="Description (optional)"
              className={inputClassName}
              disabled={didShare}
            />
          </>
        )}
        {didShare && (
          <h5 className="text-sm text-primary-600">Thank you for sharing!</h5>
        )}
        <div className="flex justify-end">
          {!didShare && (
            <Button type="submit" size="sm" disabled={didShare}>
              Share error
            </Button>
          )}
          {didShare && (
            <Button
              type="button"
              size="sm"
              onClick={() => {
                window.location.reload();
              }}
            >
              Reload page
            </Button>
          )}
        </div>
      </div>
    </form>
  );
}

export default function Error({
  error,
}: {
  error: Error & { digest?: string };
}) {
  const org = useOrg();
  const apiUrl = org.api_url;
  const isOnPrem = apiUrl !== MultiTenantApiURL;

  const isExtensionError = isBrowserExtensionError(error);

  useEffect(() => {
    if (isOnPrem) return;

    if (Sentry && !isExtensionError) {
      // Set the context here in case the exception is thrown before the
      // we were able to set it up in SentryInit.
      Sentry.setContext("org", { isOnPrem });

      Sentry.captureException(error, {
        tags: {
          page: "org-page-error",
        },
      });
    }
  }, [error, isOnPrem, isExtensionError]);

  const multiTenantErrorMessage = isExtensionError
    ? BROWSER_EXTENSION_ERROR_MESSAGE
    : GENERAL_ERROR_MESSAGE;

  return (
    <ErrorPageContent digest={error.digest}>
      {!isOnPrem ? (
        <>{multiTenantErrorMessage}</>
      ) : (
        <>
          <div>
            Something went wrong. You may optionally share the error trace with
            Braintrust for support.
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button className="mt-6" size="sm">
                Share error trace
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-96 overflow-y-auto">
              <ShareErrorForm error={error} />
            </PopoverContent>
          </Popover>
        </>
      )}
    </ErrorPageContent>
  );
}
