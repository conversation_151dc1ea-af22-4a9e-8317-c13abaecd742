import { BlueLink, ExternalLink } from "#/ui/link";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import {
  getExperimentLink,
  getExperimentsLink,
} from "./p/[project]/experiments/[experiment]/getExperimentLink";
import { Button } from "#/ui/button";
import { createApiKey } from "./settings/api-keys/api-key";
import CodeToCopy from "#/ui/code-to-copy";
import { PythonLogo, TypescriptLogo } from "./onboarding-logos";
import { useAnalytics } from "#/ui/use-analytics";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useCreateProjectDialog } from "#/ui/dialogs/create-project";
import { useOrg, useUser } from "#/utils/user";
import { getOnboardingFlowId } from "#/lib/onboardingFlow";
import { Spinner } from "#/ui/icons/spinner";
import {
  type getOnboardingExperimentInfo,
  type getProjectSummary,
} from "./org-actions";
import { RealtimeChannel } from "#/utils/realtime-data";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { z } from "zod";
import { toast } from "sonner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useSessionToken } from "#/utils/auth/session-token";
import { useQueryFunc } from "#/utils/react-query";
import { useAuth } from "@clerk/nextjs";
import { newId } from "braintrust";

const orgProjectMetadataPayloadSchema = z.record(z.record(z.any()));

export function ProjectOnboardingPage({ orgName }: { orgName: string }) {
  const { getToken } = useAuth();
  const { analytics } = useAnalytics();
  const { track } = useAppAnalytics();

  const org = useOrg();
  const { user, orgs } = useUser();
  const { getOrRefreshToken } = useSessionToken();

  const [creatingApiKey, setCreatingApiKey] = useState(false);
  const [apiKey, setApiKey] = useState<string | null>(null);
  const createKey = async () => {
    if (!org.id) {
      toast.error("Cannot find org id");
      return;
    }
    setCreatingApiKey(true);
    setApiKey(await createApiKey({ name: "tutorial", orgId: org.id }));
    setCreatingApiKey(false);
    analytics?.track("onboarding_api_key_created", {
      orgName,
    });
  };
  const apiKeyEnv = `BRAINTRUST_API_KEY=${apiKey || "YOUR_API_KEY"}`;

  useEffect(() => {
    analytics?.track("onboardingPageViewed", {
      flowId: getOnboardingFlowId(),
      orgName,
      orgId: org?.id,
      pageName: "onboarding_main",
      pageIndex: 0,
      isNewUser:
        Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0,
      source: "web",
    });
  }, [analytics, orgName, org?.id, orgs]);

  const { invalidate: refreshProjects } = useQueryFunc<
    typeof getProjectSummary
  >({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  const router = useRouter();
  const finishedOnboarding = useRef<boolean>(false);
  const processNewExperiment = useCallback(
    async (experimentId: string) => {
      if (finishedOnboarding.current) return;
      try {
        const experimentInfo = await invokeServerAction<
          typeof getOnboardingExperimentInfo
        >({
          fName: "getOnboardingExperimentInfo",
          args: { experimentId },
          getToken,
        });
        if (!experimentInfo) {
          console.warn("No experiments");
          return; // Could not find any experiments
        }
        // Don't redirect if other experiments were created by the time we got
        // around to processing this one.
        if (experimentInfo.exist_other_experiments_in_org) {
          console.warn(
            "Skipping onboarding redirect because there are other experiments in the org",
          );
          finishedOnboarding.current = true;
        }
        if (finishedOnboarding.current) return;
        router.push(
          getExperimentLink({
            orgName,
            projectName: experimentInfo.project_name,
            experimentName: experimentInfo.experiment_name,
          }),
        );
        finishedOnboarding.current = true;
      } catch (e) {
        toast.error("Failed to query tutorial experiment");
      }
    },
    [orgName, router, getToken],
  );
  const processOrgProjectMetadataEvent = useCallback(
    async (eventRaw: unknown) => {
      if (finishedOnboarding.current) return;
      const event = orgProjectMetadataPayloadSchema.parse(eventRaw);
      const experimentData = event["experiment"];
      if (!experimentData) return;
      for (const experimentId of Object.keys(experimentData)) {
        await processNewExperiment(experimentId);
      }
    },
    [processNewExperiment],
  );

  // Subscribe to updates to this org. Trigger processOrgProjectMetadataEvent on
  // each event.
  const channelStatus = useRef<"empty" | "creating" | "created">("empty");
  const fetchChannelUrl = useMemo(
    () =>
      org.api_url && org.id
        ? `${org.api_url}/broadcast-key?` +
          new URLSearchParams({
            object_type: "org_project_metadata",
            id: org.id,
            audit_log: "0",
          })
        : undefined,
    [org.api_url, org.id],
  );
  useEffect(() => {
    (async () => {
      if (
        !(
          fetchChannelUrl &&
          org.api_url &&
          user &&
          channelStatus.current === "empty"
        )
      ) {
        return;
      }
      channelStatus.current = "creating";
      try {
        const channelName = await (
          await apiFetchGet(fetchChannelUrl, await getOrRefreshToken())
        ).json();
        if (!(channelName.channel && channelName.token)) {
          throw new Error(
            `Invalid channel info: ${JSON.stringify(channelName)}`,
          );
        }
        new RealtimeChannel(
          channelName.channel,
          channelName.token,
          {
            user_id: user.id,
            email: user.email,
            avatar_url: user.avatar_url,
          },
          org,
          () => true,
          (payload) => {
            processOrgProjectMetadataEvent(payload);
          },
        );
        channelStatus.current = "created";
      } catch (e) {
        console.error("Failed to create channel\n", e);
        channelStatus.current = "empty";
      }
    })();
  }, [
    fetchChannelUrl,
    user,
    org,
    processOrgProjectMetadataEvent,
    getOrRefreshToken,
  ]);

  const { modal: createProjectModal, open: setCreateProjectOpen } =
    useCreateProjectDialog({
      onSuccessfulCreate: ({ projectName }: { projectName: string }) => {
        refreshProjects();

        router.push(
          getExperimentsLink({
            orgName: orgName,
            projectName,
          }),
        );
      },
      orgId: org.id,
    });

  const [lang, setLang] = useState<"ts" | "py">("ts");
  const [jsPackageManager, setJsPackageManager] = useState<
    "pnpm" | "npm" | "yarn"
  >("pnpm");
  const [pythonInstaller, setPythonInstaller] = useState<"pip" | "uv">("pip");

  return (
    <div className="relative overflow-hidden">
      <MainContentWrapper
        hideFooter
        className="z-20 mx-auto max-w-(--breakpoint-sm) pt-12 pb-48 text-lg"
      >
        <h1 className="mb-2 text-3xl font-semibold">Get started</h1>
        <p className="mb-10">
          The best way to get started with Braintrust is to run your first eval
          with starter code. You can also{" "}
          <Button
            transparent
            size="inline"
            className="text-lg font-medium text-accent-600 hover:text-accent-700"
            onClick={(e) => {
              e.preventDefault();
              track("projectCreateAttempt", {
                projectName: "My first project",
                entryPoint: "onboardingFlow",
                flowId: newId(),
              });
              setCreateProjectOpen({
                name: "My first project",
                entryPoint: "onboardingFlow",
              });
            }}
          >
            create a project
          </Button>{" "}
          from scratch via the UI. Choose your weapon, and let&apos;s dive in.
        </p>
        <div className="mb-6 flex gap-2 rounded-xl border p-2">
          <Button
            className="flex-1 gap-3 text-xl"
            variant={lang === "ts" ? "primary" : "ghost"}
            onClick={() => setLang("ts")}
            size="xl"
          >
            <TypescriptLogo />
            TypeScript
          </Button>
          <Button
            className="flex-1 gap-3 text-xl"
            variant={lang === "py" ? "primary" : "ghost"}
            onClick={() => setLang("py")}
            size="xl"
          >
            <PythonLogo />
            Python
          </Button>
        </div>

        <div className="mb-2 flex items-end gap-3 text-base">
          <p className="flex-1">Install the Braintrust SDK</p>
          {lang === "ts" && (
            <div className="flex gap-1">
              <Button
                className={
                  jsPackageManager === "pnpm"
                    ? "border"
                    : "border border-transparent"
                }
                variant="ghost"
                onClick={() => setJsPackageManager("pnpm")}
                size="xs"
              >
                pnpm
              </Button>
              <Button
                className={
                  jsPackageManager === "npm"
                    ? "border"
                    : "border border-transparent"
                }
                variant="ghost"
                onClick={() => setJsPackageManager("npm")}
                size="xs"
              >
                npm
              </Button>
              <Button
                className={
                  jsPackageManager === "yarn"
                    ? "border"
                    : "border border-transparent"
                }
                variant="ghost"
                onClick={() => setJsPackageManager("yarn")}
                size="xs"
              >
                yarn
              </Button>
            </div>
          )}
          {lang === "py" && (
            <div className="flex gap-1">
              <Button
                className={
                  pythonInstaller === "pip"
                    ? "border"
                    : "border border-transparent"
                }
                variant="ghost"
                onClick={() => setPythonInstaller("pip")}
                size="xs"
              >
                pip
              </Button>
              <Button
                className={
                  pythonInstaller === "uv"
                    ? "border"
                    : "border border-transparent"
                }
                variant="ghost"
                onClick={() => setPythonInstaller("uv")}
                size="xs"
              >
                uv
              </Button>
            </div>
          )}
        </div>
        <CodeToCopy
          data={
            lang === "py"
              ? `${pythonInstaller === "uv" ? "uv pip install" : "pip install"} braintrust autoevals`
              : `${
                  jsPackageManager === "npm"
                    ? "npm install"
                    : jsPackageManager === "pnpm"
                      ? "pnpm add"
                      : "yarn add"
                } braintrust autoevals`
          }
          language="bash"
        />

        <p className="mt-10 mb-2 text-base">
          Then, create a file named{" "}
          <code className="font-mono font-medium text-accent-600">
            {lang === "py" ? "eval_tutorial.py" : "tutorial.eval.ts"}
          </code>{" "}
          with the following contents
        </p>
        <CodeToCopy
          language={lang === "py" ? "python" : "javascript"}
          data={
            lang === "py"
              ? `from braintrust import Eval
from autoevals import LevenshteinScorer

Eval(
  "Say Hi Bot",
  data=lambda: [
      {
          "input": "Foo",
          "expected": "Hi Foo",
      },
      {
          "input": "Bar",
          "expected": "Hello Bar",
      },
  ],  # Replace with your eval dataset
  task=lambda input: "Hi " + input,  # Replace with your LLM call
  scores=[LevenshteinScorer],
)`
              : `import { Eval } from "braintrust";
import { LevenshteinScorer } from "autoevals";

Eval("Say Hi Bot", {
  data: () => {
    return [
      {
        input: "Foo",
        expected: "Hi Foo",
      },
      {
        input: "Bar",
        expected: "Hello Bar",
      },
    ]; // Replace with your eval dataset
  },
  task: async (input) => {
    return "Hi " + input; // Replace with your LLM call
  },
  scores: [LevenshteinScorer],
});`
          }
        />

        <p className="mt-10 mb-2 text-base">
          Create an API key and run your eval
        </p>
        <div>
          <APIKeyOrCommand
            apiKey={apiKey}
            creatingApiKey={creatingApiKey}
            createKey={createKey}
          >
            <CodeToCopy
              data={
                lang === "py"
                  ? `${apiKeyEnv} \\
  braintrust eval eval_tutorial.py`
                  : `${apiKeyEnv} \\
  npx braintrust eval tutorial.eval.ts`
              }
              language="bash"
            />
          </APIKeyOrCommand>
        </div>

        <LearnMoreSection />
        {createProjectModal}
      </MainContentWrapper>
      <div
        className="absolute inset-x-0 bottom-0 z-10 h-60 rounded-[50%] bg-indigo-500/30 blur-3xl"
        style={{
          transform: "translateY(100px)",
        }}
      />
    </div>
  );
}

const LearnMoreColumn = ({
  title,
  href,
  description,
}: {
  title: string;
  href: string;
  description: React.ReactNode;
}) => {
  return (
    <div className="flex flex-1 flex-col gap-y-1.5">
      <BlueLink
        href={href}
        className="flex items-center gap-x-2 text-left font-inter text-base font-semibold"
      >
        {title} {"->"}
      </BlueLink>
      <p className="text-sm text-balance">{description}</p>
    </div>
  );
};

const LearnMoreSection = () => {
  return (
    <div className="mt-16 w-full">
      <h2 className="mb-4 text-xl font-semibold">Learn more</h2>
      <div className="flex flex-col justify-between gap-x-4 gap-y-6 lg:flex-row">
        <LearnMoreColumn
          title="Evaluations"
          href="/docs/guides/evals"
          description={
            <>
              Answer questions like &quot;which examples regressed on my
              change?&quot; and &quot;what if I try this new model?&quot;
            </>
          }
        />
        <LearnMoreColumn
          title="Example apps"
          href="/docs/examples"
          description={
            <>
              Example use cases to go one level deeper into summarization,
              chatbots, RAG, and more.
            </>
          }
        />
        <LearnMoreColumn
          title="Self-hosting"
          href="/docs/guides/self-hosting"
          description={
            <>
              Our enterprise plan lets you run Braintrust in a private VPC.
              Learn how that works and{" "}
              <ExternalLink href="/contact">get in touch</ExternalLink> to try
              it out.
            </>
          }
        />
      </div>
    </div>
  );
};

function APIKeyOrCommand({
  apiKey,
  creatingApiKey,
  createKey,
  children,
}: {
  apiKey: string | null;
  creatingApiKey: boolean;
  createKey: () => Promise<void>;
  children: React.ReactNode;
}) {
  return !apiKey ? (
    <Button
      onClick={(e) => {
        e.preventDefault();
        createKey();
      }}
      variant="primary"
      className="gap-2 bg-primary-950 px-4 text-base text-primary-100 hover:bg-primary-800"
    >
      Create API key
      {creatingApiKey && <Spinner className="size-4" />}
    </Button>
  ) : (
    <>{children}</>
  );
}
