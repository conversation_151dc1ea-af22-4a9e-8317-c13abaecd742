import SessionRoot from "#/ui/root";
import ClientLayout from "./clientlayout";
import { fetchOrgUsers } from "#/utils/org-users";
import { decodeURIComponentPatched } from "#/utils/url";

export default async function RootLayout(props: {
  children: React.ReactNode;
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;

  const { org } = params;

  const { children } = props;

  const org_name = decodeURIComponentPatched(org);
  const orgUsers = await fetchOrgUsers({ orgName: org_name });

  return (
    <SessionRoot loginRequired={false}>
      <ClientLayout org={org_name} orgUsers={orgUsers}>
        {children}
      </ClientLayout>
    </SessionRoot>
  );
}
