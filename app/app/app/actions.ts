"use server";

import { z } from "zod";

import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { type Organization, organizationSchema } from "@braintrust/typespecs";
import { ANON_AUTH_ID } from "#/utils/constants";
import {
  type OrganizationContentSecurityPolicies,
  organizationContentSecurityPoliciesSchema,
} from "#/security/csp/types";
import { checkIsOrgOwnerQuery } from "#/pages/api/_special_queries";

const toplevelOrganizationInfoSchema = z.strictObject({
  id: organizationSchema.shape.id,
  name: organizationSchema.shape.name,
  num_projects: z.number(),
});

export type ToplevelOrganizationInfo = z.infer<
  typeof toplevelOrganizationInfoSchema
>;

// RBAC_DISCLAIMER: A user is allowed to see basic metadata about organizations
// they are a member of.
export async function getToplevelOrganizationInfo(
  {}: {},
  authLookupRaw?: AuthLookup,
): Promise<ToplevelOrganizationInfo[] | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (authLookup.auth_id === ANON_AUTH_ID) {
    return null;
  }

  const { query: projectsQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "project",
      aclPermission: "read",
    },
  });

  const query = `
    with
    member_orgs as (
    select organizations.id, organizations.name
    from organizations join members on members.org_id = organizations.id
    where members.user_id = ${queryParams.add(authLookup.user_id)}
    ),
    projects_by_org as (
    select projects.org_id, count(*) num_projects
    from (${projectsQuery}) projects
    group by org_id
    )
    select
        member_orgs.id,
        member_orgs.name,
        coalesce(projects_by_org.num_projects, 0)::integer num_projects
    from
        member_orgs
        left join projects_by_org on member_orgs.id = projects_by_org.org_id
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return toplevelOrganizationInfoSchema.array().parse(rows);
}

type GetOrgParams = { org_name: string } | { org_id: string };
// RBAC_DISCLAIMER: A user is allowed to see basic metadata about organizations
// they are a member of.
export async function getOrganization(
  params: GetOrgParams,
  authLookupRaw?: AuthLookup,
): Promise<Organization | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
    select organizations.*
    from
        organizations
        join members on (organizations.id = members.org_id)
    where
      members.user_id = $1
      and ${"org_id" in params ? "organizations.id = $2" : "organizations.name = $2"}
  `,
    [authLookup.user_id, "org_id" in params ? params.org_id : params.org_name],
  );
  if (rows.length === 1) {
    return organizationSchema.parse(rows[0]);
  } else {
    return null;
  }
}

// RBAC_DISCLAIMER: A user is allowed to see the CSP for their own organization.
export async function getOrganizationContentSecurityPolicies(
  {
    org_name,
  }: {
    org_name: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<OrganizationContentSecurityPolicies | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
    select organization_content_security_policies.*
    from
        organizations
        join organization_content_security_policies on (organizations.id = organization_content_security_policies.org_id)
        join members on (organizations.id = members.org_id)
    where
      members.user_id = $1
      and organizations.name = $2
  `,
    [authLookup.user_id, org_name],
  );

  if (rows.length === 1) {
    // TODO: add report_to column
    return organizationContentSecurityPoliciesSchema.parse(rows[0]);
  } else {
    return null;
  }
}

export async function setOrganizationContentSecurityPolicies(
  {
    org_id,
    policy,
    policy_report_only,
  }: {
    org_id: string;
    policy: string;
    policy_report_only: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<OrganizationContentSecurityPolicies | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const {
    query: orgQuery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "update",
    },
    filters: {
      org_id: org_id,
    },
  });

  const query = `
    with rows_to_insert as (
        select organizations.id org_id, ${queryParams.add(policy)} policy, ${queryParams.add(policy_report_only)} policy_report_only
        from (${orgQuery}) organizations
        limit 1
    )
    insert into organization_content_security_policies (org_id, policy, policy_report_only)
    select * from rows_to_insert
    on conflict (org_id) do update
        set policy = EXCLUDED.policy,
        policy_report_only = EXCLUDED.policy_report_only
    returning *
  `;

  const supabase = getServiceRoleSupabase();
  const row = extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage,
  });

  return organizationContentSecurityPoliciesSchema.parse(row);
}

// NOTE: this server action is invoked from the data plane so ensure changes don't
// break backwards compat
export async function isOrganizationOwner(
  {
    org_id,
  }: {
    org_id: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<boolean> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query, queryParams } = checkIsOrgOwnerQuery({
    userId: authLookup.user_id,
    orgId: org_id,
  });

  const supabase = getServiceRoleSupabase();
  const row = extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage: undefined,
  });
  const rowParsed = z.object({ exists: z.boolean() }).parse(row);
  return rowParsed.exists;
}
