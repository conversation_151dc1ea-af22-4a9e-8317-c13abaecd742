"use client";

import SessionRoot from "#/ui/root";
import { useChannel } from "#/utils/realtime-data";
import { useCallback } from "react";

export default function Page() {
  const onData = useCallback((data: unknown) => {
    console.log("data", data);
  }, []);

  // This is just an arbitrary, non-prod channel, which should be harmless for use.
  const { roster } = useChannel({
    channelName: "R7NHdcd05kaMNqaWLK1UcIUpSX-J3l3bETQwzzNG7ds",
    token: "aF4PFQ9y12mBeOA3ka/UkREvCRut9TByhEkuQyTFA7Y=",
    onData,
  });

  return (
    <SessionRoot loginRequired={true}>
      <div>
        <h1>Realtime Debugger</h1>
        {roster.map((user) => (
          <div key={user.user_id}>
            {user.email} ({user.sessions.length})
          </div>
        ))}
      </div>
    </SessionRoot>
  );
}
