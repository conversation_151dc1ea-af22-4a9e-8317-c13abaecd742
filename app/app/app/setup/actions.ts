"use server";

import { z } from "zod";
import { getServiceRoleSupabase } from "#/utils/supabase";

// RBAC_DISCLAIMER: Anybody is allowed to check for org name conflicts.
export async function checkOrgNameConflict({
  org_name,
}: {
  org_name: string;
}): Promise<boolean> {
  const supabase = getServiceRoleSupabase();
  try {
    const { rows } = await supabase.query(
      `select check_org_name_conflict($1) conflict`,
      [org_name],
    );
    return z.boolean().parse(rows[0]["conflict"]);
  } catch (e) {
    console.error("Failed to check org name conflict", e);
    throw new Error("Failed to check org name conflict");
  }
}
