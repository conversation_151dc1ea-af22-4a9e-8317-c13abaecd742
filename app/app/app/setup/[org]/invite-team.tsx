"use client";

import { But<PERSON> } from "#/ui/button";
import { AnimatePresence, motion } from "motion/react";
import {
  addUsersToOrg,
  InviteMembersForm,
  useInviteMembersForm,
} from "../../[org]/settings/team/invite-members-form";
import { useAuth } from "@clerk/nextjs";

export const InviteTeam = ({
  orgName,
  orgId,
  onSkip,
}: {
  orgName: string;
  orgId: string;
  onSkip: () => void;
}) => {
  const formState = useInviteMembersForm({});

  const { getToken } = useAuth();

  const handleSubmit = async () => {
    const emails = formState.getAllEmails();

    try {
      formState.setIsInviteInProgress(true);
      await addUsersToOrg({
        orgId,
        orgName,
        emails,
        groupIds: formState.selectedGroups.map((g) => g.groupId),
        refresh: () => {},
        getToken,
      });
    } finally {
      onSkip();
      formState.setIsInviteInProgress(false);
    }
  };

  return (
    <div className="flex w-full flex-col">
      <AnimatePresence mode="popLayout">
        <motion.h1
          layoutId="title"
          initial={{ opacity: 0, filter: "blur(10px)" }}
          animate={{ opacity: 1, filter: "blur(0px)" }}
          exit={{ opacity: 0, filter: "blur(10px)" }}
          className="mb-2 font-display text-3xl font-medium"
        >
          Invite your team
        </motion.h1>
      </AnimatePresence>
      <p className="mb-12 text-base text-primary-600">
        Invite team members to your organization.
      </p>
      <motion.div
        layoutId="main-container-ui"
        layout="position"
        className="relative"
      >
        <div className="flex flex-col gap-8">
          <InviteMembersForm {...formState} size="lg" orgName={orgName} />
        </div>
        <div className="flex justify-end gap-3 py-8">
          <Button
            variant="ghost"
            className="text-primary-500 transition-all"
            onClick={onSkip}
          >
            I&apos;ll do this later
          </Button>
          <Button
            variant="primary"
            disabled={formState.isInviteInProgress || !formState.isFormValid()}
            onClick={handleSubmit}
            isLoading={formState.isInviteInProgress}
          >
            Invite
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
