"use client";

import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ListTree, MousePointerClick } from "lucide-react";
import { getOnboardingFlowId } from "#/lib/onboardingFlow";
import Link from "next/link";
import { motion } from "motion/react";
import { useEffect } from "react";
import { useAnalytics } from "#/ui/use-analytics";
import { useUser } from "#/utils/user";

export const GetStarted = ({ orgName }: { orgName: string }) => {
  const { analytics } = useAnalytics();
  const { orgs } = useUser();

  // Track page view when component mounts
  useEffect(() => {
    analytics?.track("onboardingPageViewed", {
      flowId: getOnboardingFlowId(),
      orgName,
      orgId: orgs?.[orgName]?.id,
      pageName: "get_started",
      pageIndex: 2,
      isNewUser:
        Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0,
      source: "web",
    });
  }, [analytics, orgName, orgs]);

  const handleStepComplete = (stepName: string, nextPath: string) => {
    const org = orgs?.[orgName];
    analytics?.track("onboardingStepComplete", {
      flowId: getOnboardingFlowId(),
      flowName:
        Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0
          ? "new_user_signup"
          : "existing_user_org_creation",
      stepDetails: {
        stepName,
        stepIndex: 2,
        skippable: false,
      },
      isNewUser:
        Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0,
      path: nextPath === "trace" ? "trace" : "ui",
      org_id: org?.id,
      org_name: orgName,
      source: "web",
    });
  };

  return (
    <div className="flex w-full flex-col items-center">
      <div className="mb-6 flex w-full flex-col justify-center">
        <h1 className="mb-2 font-display text-3xl font-semibold">
          Let&apos;s get started
        </h1>
        <p className="text-base text-primary-600">
          Braintrust is the end-to-end platform for building world-class AI
          apps. Choose an option below to continue.
        </p>
      </div>
      <div className="flex flex-col gap-3 md:flex-row">
        <motion.div
          className="w-full flex-1"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ y: 5 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          <Link
            prefetch
            className={cn(
              buttonVariants({ variant: "border" }),
              "group flex h-fit w-full flex-1 flex-col justify-start gap-0 overflow-hidden rounded-xl border bg-primary-100 bg-center px-5 py-4 pr-0 text-primary-900 hover:border-primary-300 hover:bg-primary-200",
            )}
            href={`/app/setup/${orgName}/trace`}
            onClick={() => handleStepComplete("get_started", "trace")}
          >
            <div className="z-10 flex w-full flex-col gap-4 text-pretty">
              <div className="flex flex-col gap-2 text-start">
                <ListTree className="mb-4 size-7 text-primary-500" />
                <div className="font-display text-2xl leading-tight font-semibold">
                  Trace an existing app
                </div>
                <span className="text-sm font-normal text-primary-600">
                  I have an existing AI app in production and I want to add LLM
                  observability
                </span>
              </div>
            </div>
          </Link>
        </motion.div>
        <motion.div
          className="w-full flex-1"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ y: 5 }}
          transition={{ duration: 0.2, ease: "easeOut", delay: 0.1 }}
        >
          <Link
            prefetch
            className={cn(
              buttonVariants({ variant: "border" }),
              "group flex h-fit w-full flex-1 flex-col justify-start gap-0 overflow-hidden rounded-xl border bg-primary-100 bg-center px-5 py-4 pr-0 text-primary-900 hover:border-primary-300 hover:bg-primary-200",
            )}
            href={`/app/setup/${orgName}/providers`}
            onClick={() => handleStepComplete("get_started", "providers")}
          >
            <div className="z-10 flex w-full flex-col items-start gap-4 text-pretty">
              <div className="flex flex-col gap-2 text-start">
                <MousePointerClick className="mb-4 size-7 text-primary-500" />
                <div className="font-display text-2xl leading-tight font-semibold">
                  Explore new AI ideas
                </div>
                <span className="text-sm font-normal text-primary-600">
                  I&apos;m exploring ideas, prompts, and evals with Braintrust
                </span>
              </div>
            </div>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};
