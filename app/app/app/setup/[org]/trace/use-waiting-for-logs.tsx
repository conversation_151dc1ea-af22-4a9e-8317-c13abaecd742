"use client";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import { useSessionToken } from "#/utils/auth/session-token";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useUser } from "#/utils/user";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { starterProjectName } from "./trace-config";
import { getProjectLogsLink } from "#/app/app/[org]/p/[project]/logs/getProjectLogsLink";
import { toast } from "sonner";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { RealtimeChannel } from "#/utils/realtime-data";
import { z } from "zod";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { getOnboardingFlowId } from "#/lib/onboardingFlow";

const orgProjectMetadataPayloadSchema = z.record(z.record(z.any()));

export const useWaitingForLogs = ({
  orgName,
  selectedLanguage,
  selectedProvider,
}: {
  orgName: string;
  selectedLanguage?: string;
  selectedProvider?: string;
}) => {
  const { orgs, user } = useUser();
  const org = orgs[orgName];
  const { getToken } = useAuth();
  const { getOrRefreshToken } = useSessionToken();
  const { track } = useAppAnalytics();

  const router = useRouter();
  const finishedOnboarding = useRef<boolean>(false);
  const onboardingCompleteTracked = useRef<boolean>(false);

  const processNewLog = useCallback(
    async (projectId: string) => {
      if (finishedOnboarding.current) return;
      try {
        const freshProjects = await invokeServerAction<
          typeof getProjectSummary
        >({
          fName: "getProjectSummary",
          args: { org_name: org.name },
          getToken,
        });

        const project = freshProjects?.find((p) => p.project_id === projectId);
        if (project) {
          // Delete start project if unused for tutorial
          if (project.project_name !== starterProjectName) {
            const starterProject = freshProjects?.find(
              (p) => p.project_name === starterProjectName,
            );
            if (starterProject) {
              try {
                await fetch(`/api/project/delete_id`, {
                  method: "POST",
                  headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                    id: starterProject.project_id,
                  }),
                });
              } catch (error) {
                console.warn("Error deleting starter project:", error);
              }
            }
          }

          // Track onboarding completion for successful trace path (only once per flow)
          if (!onboardingCompleteTracked.current) {
            const isNewUser =
              Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0;
            track("onboardingComplete", {
              flowId: getOnboardingFlowId(),
              flowName: isNewUser
                ? "new_user_signup"
                : "existing_user_org_creation",
              completionDetails: {
                path: "trace",
                stepName: "trace_setup",
                stepIndex: 3,
                providerKeyProvided: false,
                providerChoice: selectedProvider, // Track the selected provider
                skipped: false, // This is successful completion, not skipped
                sdkLanguage: selectedLanguage, // Track the selected SDK language
              },
              isNewUser,
              org_id: orgs?.[orgName]?.id,
              org_name: orgName,
              source: "web",
            });
            onboardingCompleteTracked.current = true;
          }

          router.push(
            getProjectLogsLink({
              orgName: org?.name,
              projectName: project.project_name,
            }),
          );
          finishedOnboarding.current = true;
        } else {
          console.warn("Project not found");
        }
      } catch (e) {
        toast.error("Failed to query tutorial logs");
      }
    },
    [
      org,
      router,
      getToken,
      track,
      orgs,
      orgName,
      selectedLanguage,
      selectedProvider,
      onboardingCompleteTracked,
    ],
  );

  const processOrgProjectMetadataEvent = useCallback(
    async (eventRaw: unknown) => {
      if (finishedOnboarding.current) return;
      const event = orgProjectMetadataPayloadSchema.parse(eventRaw);
      const projectLogData = event["project_log"];
      if (projectLogData) {
        const projectIds = Object.keys(projectLogData);
        if (projectIds.length > 0) {
          for (const projectId of projectIds) {
            await processNewLog(projectId);
            return;
          }
        }
      }
    },
    [processNewLog],
  );

  // Subscribe to updates to this org. Trigger processOrgProjectMetadataEvent on
  // each event.
  const channelStatus = useRef<"empty" | "creating" | "created">("empty");
  const fetchChannelUrl = useMemo(
    () =>
      org?.api_url && org?.id
        ? `${org.api_url}/broadcast-key?` +
          new URLSearchParams({
            object_type: "org_project_metadata",
            id: org.id,
            audit_log: "0",
          })
        : undefined,
    [org?.api_url, org?.id],
  );

  const channelRef = useRef<RealtimeChannel | null>(null);
  useEffect(() => {
    const fetchChannel = async () => {
      if (
        !(
          fetchChannelUrl &&
          org?.api_url &&
          user &&
          channelStatus.current === "empty"
        )
      ) {
        return;
      }
      channelStatus.current = "creating";
      try {
        const channelName = await (
          await apiFetchGet(fetchChannelUrl, await getOrRefreshToken())
        ).json();
        if (!(channelName.channel && channelName.token)) {
          throw new Error(
            `Invalid channel info: ${JSON.stringify(channelName)}`,
          );
        }
        channelRef.current = new RealtimeChannel(
          channelName.channel,
          channelName.token,
          {
            user_id: user.id,
            email: user.email,
            avatar_url: user.avatar_url,
          },
          org,
          () => true,
          (payload) => {
            processOrgProjectMetadataEvent(payload);
          },
        );
        channelStatus.current = "created";
      } catch (e) {
        console.error("Failed to create channel\n", e);
        channelStatus.current = "empty";
      }
    };
    fetchChannel();

    return () => {
      if (channelRef.current) {
        channelRef.current.disconnect();
        channelRef.current = null;
        channelStatus.current = "empty";
      }
    };
  }, [
    fetchChannelUrl,
    user,
    org,
    processOrgProjectMetadataEvent,
    getOrRefreshToken,
  ]);
};
