import { buildMetadata } from "#/app/metadata";
import { decodeURIComponentPatched } from "#/utils/url";
import SessionRoot from "#/ui/root";
import { SetupTracing } from "./setup-tracing";
import { SetupLayout } from "../setup-layout";

export interface Params {
  org: string;
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  return (
    <SessionRoot loginRequired>
      <SetupLayout orgName={org_name}>
        <SetupTracing orgName={org_name} />
      </SetupLayout>
    </SessionRoot>
  );
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Setup observability",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/setup/${params.org}/trace`,
    ogTemplate: "product",
  });
}
