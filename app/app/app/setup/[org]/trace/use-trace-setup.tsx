"use client";

import { useState, useCallback } from "react";
import { Language, SdkIntegration } from "./trace-config";

export interface TraceSetupState {
  selectedLanguage: Language;
  selectedProvider: SdkIntegration;
  setSelectedLanguage: (language: Language) => void;
  setSelectedProvider: (provider: SdkIntegration) => void;
}

export const useTraceSetupState = (): TraceSetupState => {
  const [selectedLanguage, setSelectedLanguage] = useState<Language>(
    Object.values(Language)[0],
  );
  const [selectedProvider, setSelectedProvider] = useState<SdkIntegration>(
    Object.values(SdkIntegration)[0],
  );

  const handleLanguageChange = useCallback((language: Language) => {
    setSelectedLanguage(language);
  }, []);

  const handleProviderChange = useCallback((provider: SdkIntegration) => {
    setSelectedProvider(provider);
  }, []);

  return {
    selectedLanguage,
    selectedProvider,
    setSelectedLanguage: handleLanguageChange,
    setSelectedProvider: handleProviderChange,
  };
};
