"use client";

import { Bubble, Data, Score } from "#/app/(landing)/shapes";
import { createProject } from "#/app/app/[org]/p/[project]/createProject";
import { Button, buttonVariants } from "#/ui/button";
import { isEmpty, isObject } from "braintrust/util";
import { AnimatePresence, motion } from "motion/react";
import { useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { getProjectLink } from "../../[org]/p/[project]/getProjectLink";
import { type LoggedOutPlaygroundData } from "#/app/playground/logged-out-playground-table";
import { useIndexedDBObject } from "#/utils/use-indexeddb-object";
import { useCreateNewPlayground } from "#/ui/prompts/function-editor/create-playground";
import { starterProjectName } from "./trace/trace-config";
import { newId } from "braintrust";

export const MiniTutorial = ({
  orgId,
  orgName,
  onFinish,
  shouldReduceMotion,
}: {
  orgId: string | undefined;
  orgName: string;
  onFinish: VoidFunction;
  shouldReduceMotion: boolean;
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loggedOutPlaygroundData, _, clearLoggedOutPlaygroundData] =
    useIndexedDBObject<LoggedOutPlaygroundData>({
      store: "loggedOutData",
      key: "playground",
    });

  // TODO: move starter project ID made on org creation into some context, right now we're recreating it
  const { createNewPlayground } = useCreateNewPlayground({
    orgName,
    projectName: starterProjectName,
    onFinish: () => {
      clearLoggedOutPlaygroundData();
      onFinish();
    },
    routerMethod: "replace",
    orgId,
  });

  //TODO: make this create project on our starter project
  const handleProjectCreate = async () => {
    if (!orgId) {
      toast.error("Failed to create project. Please try again.");
      return;
    }
    try {
      setIsSubmitting(true);
      const { project, error } = await createProject({
        orgId: orgId,
        projectName: starterProjectName,
      });
      if (isEmpty(project)) {
        toast.error(`Failed to create project`, {
          description: `${error}`,
        });
      } else {
        try {
          await createNewPlayground({
            projectId: project.id,
            flowId: newId(),
            sessionName: "Playground 1",
            datasetName: "Dataset 1",
            initialRecords: loggedOutPlaygroundData?.playgroundBlocks.map(
              ({ prompt_data, function_data }) => ({
                prompt_data,
                function_data,
              }),
            ),
            datasetRows: loggedOutPlaygroundData?.datasetRows.map(
              ({ input, expected, metadata }) => ({
                input,
                expected,
                metadata: isObject(metadata) ? metadata : undefined,
              }),
            ),
            scorerFunctions: loggedOutPlaygroundData?.scorerFunctions,
            promptSessionData: {
              scorers: loggedOutPlaygroundData?.savedScorers ?? [],
              settings: loggedOutPlaygroundData?.playgroundSettings,
            },
          });
        } catch (error) {
          console.error(error);
          toast.error("Failed to create playground.", {
            action: (
              <Link
                href={getProjectLink({
                  orgName,
                  projectName: starterProjectName,
                })}
                className={buttonVariants({ size: "xs" })}
              >
                Go to project
              </Link>
            ),
          });
        }
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to create project. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex w-full flex-col">
      <AnimatePresence mode="popLayout">
        <motion.h1
          layoutId="title"
          initial={{ opacity: 0, filter: "blur(10px)" }}
          animate={{ opacity: 1, filter: "blur(0px)" }}
          exit={{ opacity: 0, filter: "blur(10px)" }}
          className="mb-3 font-display text-3xl font-medium"
        >
          You&apos;re all set
        </motion.h1>
      </AnimatePresence>
      <p className="mb-6 text-base text-primary-600">
        Go ahead and run your first eval with prompts, dataset, and scorers.
      </p>
      <motion.div
        layoutId="main-container-ui"
        layout="position"
        className="flex flex-col"
        transition={{
          duration: shouldReduceMotion ? 0 : 0.3,
        }}
      >
        <div className="flex items-center gap-4 py-3 text-base text-pretty text-primary-900">
          <div className="flex size-14 flex-none items-center justify-center">
            <Bubble className="size-10 text-primary-800 dark:text-primary-800" />
          </div>
          A prompt is an instruction to shape the model&apos;s response. Run
          prompts from any AI provider and track their performance over time.
        </div>
        <div className="flex items-center gap-4 py-3 text-base text-pretty text-primary-900">
          <div className="flex size-14 flex-none items-center justify-center">
            <Data className="ml-1.5 size-10 text-primary-800 dark:text-primary-800" />
          </div>
          A dataset is a set of inputs and expected values that are used to
          evaluate the model&apos;s performance.
        </div>
        <div className="flex items-center gap-4 py-3 text-base text-pretty text-primary-900">
          <div className="flex size-14 flex-none items-center justify-center">
            <Score className="size-10 text-primary-800 dark:text-primary-800" />
          </div>
          Scorers are used to score the quality of the response. Use industry
          standard autoevals or write your own using code or natural language.
        </div>

        <motion.div
          layoutId="action-container"
          className="z-10 flex w-full py-6"
          transition={{
            duration: shouldReduceMotion ? 0 : 0.3,
          }}
        >
          <Button
            disabled={isSubmitting}
            isLoading={isSubmitting}
            variant="primary"
            onClick={() => {
              handleProjectCreate();
            }}
          >
            Go to Braintrust
          </Button>
        </motion.div>
      </motion.div>
    </div>
  );
};
