"use client";

import { <PERSON><PERSON>, buttonVariants } from "#/ui/button";
import { AISecretTypes } from "@braintrust/proxy/schema";
import { cn } from "#/utils/classnames";
import { toast } from "sonner";
import { motion } from "motion/react";
import {
  getProviderConfig,
  providerReadableName,
  testKey,
} from "#/app/app/[org]/settings/secrets/utils";
import { Label } from "#/ui/label";
import { Input } from "#/ui/input";
import { useCallback, useState, useEffect } from "react";
import { modelsByProvider } from "#/ui/prompts/models";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Plus } from "lucide-react";
import { getProjectLink } from "#/app/app/[org]/p/[project]/getProjectLink";
import { starterProjectName } from "../trace/trace-config";
import { useAnalytics } from "#/ui/use-analytics";
import { useUser } from "#/utils/user";
import {
  getOnboardingFlowId,
  clearOnboardingFlowId,
} from "#/lib/onboardingFlow";

export const ConfigureProviders = ({ orgName }: { orgName: string }) => {
  const router = useRouter();
  const { analytics } = useAnalytics();
  const { orgs } = useUser();

  // Track page view when component mounts
  useEffect(() => {
    analytics?.track("onboardingPageViewed", {
      flowId: getOnboardingFlowId(),
      orgName,
      orgId: orgs?.[orgName]?.id,
      pageName: "provider_setup",
      pageIndex: 3,
      isNewUser:
        Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0,
      source: "web",
    });
  }, [analytics, orgName, orgs]);

  // Check if user is new (has only 1 org)
  const isNewUser =
    Object.keys(orgs || {}).filter((n) => n !== orgName).length === 0;
  /*
  const defaultSecrets = DefaultSecretNames.map((name, i) => {
    return apiSecretsByName[name] || { name, id: i, type: AISecretTypes[name] };
  }); */
  const DefaultSecretNames = Object.keys(AISecretTypes);

  const [provider, setProvider] = useState<string>(DefaultSecretNames[0]);
  const [apiKey, setApiKey] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isValidating, setIsValidating] = useState<boolean>(false);
  /*   const filteredModelEndpointType = ModelEndpointType.filter(
      (type) => type !== "js",
    ); */

  const createKey = useCallback(
    async ({
      type,
      name,
      value,
      metadata,
    }: {
      type: string;
      name: string;
      value: string;
      metadata?: Record<string, unknown>;
    }) => {
      try {
        try {
          setIsValidating(true);
          const resp = await testKey({
            provider: type,
            api_key: value,
            model: modelsByProvider[type][0].modelName,
          });
          if (!resp?.ok) {
            const responseBody = await resp?.json();
            const errorMessage =
              responseBody.error?.message ??
              responseBody.error ??
              responseBody.message ??
              responseBody.detail ??
              `${resp?.status}: ${resp?.statusText}`;
            toast.error("Error validating API key", {
              description: errorMessage,
            });
            return;
          }
        } catch (err) {
          toast.error("Error validating API key", {
            description: err instanceof Error ? err.message : "Unknown error",
          });
          return;
        } finally {
          setIsValidating(false);
        }
        setIsSubmitting(true);
        const resp = await fetch(`/api/ai_secret/register`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ai_secret_name: name,
            type,
            metadata,
            secret: value,
            update: true,
            org_name: orgName,
          }),
        });
        if (!resp.ok) {
          throw new Error(await resp.text());
        }

        // Track onboarding completion when provider is successfully added
        analytics?.track("onboardingComplete", {
          flowId: getOnboardingFlowId(),
          flowName: isNewUser
            ? "new_user_signup"
            : "existing_user_org_creation",
          completionDetails: {
            path: "ui",
            stepName: "providers",
            stepIndex: 3,
            providerKeyProvided: true,
            providerChoice: provider,
            skipped: false,
          },
          isNewUser,
          org_id: orgs?.[orgName]?.id,
          org_name: orgName,
          source: "web",
        });

        // Clear onboarding flow once onboarding completes
        clearOnboardingFlowId();

        toast.success("Secret saved successfully");
        router.push(
          getProjectLink({
            orgName,
            projectName: starterProjectName,
          }),
        );
      } catch (error) {
        toast.error(`Failed to set secret`, {
          description: `${error}`,
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [orgName, router, analytics, isNewUser, orgs, provider],
  );

  return (
    <div className="flex w-full flex-col">
      <h1 className="mb-2 font-display text-3xl font-semibold">
        Add a model provider
      </h1>
      <p className="mb-12 text-base text-primary-600">
        Select an AI model provider and enter an API key. You&apos;ll be able to
        configure custom providers and cloud providers later.
      </p>
      <motion.div
        layoutId="main-container-ui"
        layout="position"
        className="relative"
      >
        <div className="flex flex-col gap-8">
          <div className="flex w-full items-center gap-2">
            <Label className="flex w-full flex-col gap-2" htmlFor="provider">
              <span className="text-sm font-medium">Provider</span>
              <div className="grid grid-cols-5 gap-2 md:grid-cols-7">
                {DefaultSecretNames.map((name, index) => {
                  const { Icon } = getProviderConfig(AISecretTypes[name]);
                  return (
                    <motion.div
                      key={name}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      whileHover={{ y: 2 }}
                      transition={{
                        opacity: {
                          duration: 0.2,
                          ease: "easeOut",
                          delay: index * 0.02,
                        },
                        y: {
                          duration: 0.2,
                          ease: "easeOut",
                          delay: 0,
                        },
                      }}
                      className="flex w-full flex-none"
                    >
                      <Button
                        className={cn(
                          "flex flex-1 flex-col items-center px-0 text-xs",
                          {
                            "border-primary-500": provider === name,
                          },
                        )}
                        onClick={() => setProvider(name)}
                      >
                        <Icon size={38} />
                        {providerReadableName(AISecretTypes[name])}
                      </Button>
                    </motion.div>
                  );
                })}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  whileHover={{ y: 2 }}
                  transition={{
                    opacity: {
                      duration: 0.2,
                      ease: "easeOut",
                      delay: DefaultSecretNames.length * 0.02,
                    },
                    y: {
                      duration: 0.2,
                      ease: "easeOut",
                      delay: 0,
                    },
                  }}
                  className="flex w-full flex-none"
                >
                  <Link
                    href={`/app/${orgName}/settings/secrets`}
                    className={cn(
                      buttonVariants(),
                      "flex flex-1 flex-col items-center text-xs text-primary-500",
                    )}
                  >
                    <Plus className="size-8" />
                    Custom
                  </Link>
                </motion.div>
              </div>
            </Label>
          </div>
          <fieldset className="flex w-full flex-col items-start gap-2">
            <Label htmlFor="api-key">API Key</Label>
            <div className="flex w-full flex-col gap-1">
              <Input
                id={`api-key`}
                placeholder={`Enter ${providerReadableName(AISecretTypes[provider])} API key`}
                type="password"
                className="h-10 px-3 text-base"
                value={apiKey}
                onChange={(e) => {
                  setApiKey(e.target.value);
                }}
              />
              <div className="text-xs text-primary-500">
                This secret will be encrypted at rest using{" "}
                <a
                  href="https://en.wikipedia.org/wiki/Transparent_data_encryption"
                  target="_blank"
                  className="text-accent-600"
                >
                  Transparent Data Encryption
                </a>{" "}
                with a{" "}
                <a
                  href="https://libsodium.gitbook.io/doc/secret-key_cryptography/aead"
                  target="_blank"
                  className="text-accent-600"
                >
                  unique 256-bit key and nonce
                </a>
                .
              </div>
            </div>
          </fieldset>
        </div>
        <div className="flex gap-3 py-8">
          <Link
            prefetch
            href={`/app/setup/${orgName}`}
            className={cn(
              buttonVariants({ variant: "ghost", size: "sm" }),
              "text-primary-500 transition-all",
            )}
          >
            Back
          </Link>
          <span className="grow" />
          <Link
            href={getProjectLink({
              orgName,
              projectName: starterProjectName,
            })}
            className={cn(
              buttonVariants({ variant: "ghost", size: "sm" }),
              "text-primary-500 transition-all",
            )}
            onClick={() => {
              // Track onboarding completion when user skips provider setup
              analytics?.track("onboardingComplete", {
                flowId: getOnboardingFlowId(),
                flowName: isNewUser
                  ? "new_user_signup"
                  : "existing_user_org_creation",
                completionDetails: {
                  path: "ui",
                  stepName: "providers",
                  stepIndex: 3,
                  providerKeyProvided: false,
                  providerChoice: undefined,
                  skipped: true,
                },
                isNewUser,
                org_id: orgs?.[orgName]?.id,
                org_name: orgName,
                source: "web",
              });

              // Clear onboarding flow once onboarding completes (skip path)
              clearOnboardingFlowId();
            }}
          >
            Skip for now
          </Link>
          <Button
            variant="primary"
            size="sm"
            disabled={!apiKey}
            onClick={() => {
              createKey({
                type: AISecretTypes[provider],
                name: provider,
                value: apiKey,
              });
            }}
            isLoading={isSubmitting || isValidating}
          >
            Add provider
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
