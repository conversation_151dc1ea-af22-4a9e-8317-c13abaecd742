"use client";

import { createProject } from "#/app/app/[org]/p/[project]/createProject";
import { isEmpty, isObject } from "braintrust/util";
import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useIndexedDBObject } from "#/utils/use-indexeddb-object";
import { useCreateNewPlayground } from "#/ui/prompts/function-editor/create-playground";
import { type LoggedOutPlaygroundData } from "#/app/playground/logged-out-playground-table";
import { Spinner } from "#/ui/icons/spinner";
import { newId } from "braintrust";
import { toast } from "sonner";
import { HeaderMenu } from "#/ui/layout/header-menu";
import { type ToplevelOrganizationInfo } from "../actions";
import { Button } from "#/ui/button";
import { ArrowRight } from "lucide-react";
import { useHotkeys } from "react-hotkeys-hook";

const PROJECT_NAME = `project-${newId()}`;

export default function ClientPage({
  orgInfo,
}: {
  orgInfo: ToplevelOrganizationInfo[];
}) {
  const router = useRouter();
  const [selectedOrg, setSelectedOrg] = useState(orgInfo[0]);
  const [isSaving, setIsSaving] = useState(orgInfo.length === 1);

  const [loggedOutPlaygroundData, _, clearLoggedOutPlaygroundData] =
    useIndexedDBObject<LoggedOutPlaygroundData>({
      store: "loggedOutData",
      key: "playground",
    });

  const { createNewPlayground } = useCreateNewPlayground({
    orgName: selectedOrg.name,
    projectName: PROJECT_NAME,
    routerMethod: "replace",
    onFinish: () => {
      clearLoggedOutPlaygroundData();
    },
    orgId: selectedOrg.id,
  });

  const handleProjectCreate = useCallback(async () => {
    if (!selectedOrg?.id) {
      toast.error("No organization found.");
      router.replace("/app");
      return;
    }

    if (!loggedOutPlaygroundData) {
      // Return early when playground data is transiently undefined (e.g. on initial load)
      return;
    }

    const { project, error: projectError } = await createProject({
      orgId: selectedOrg.id,
      projectName: PROJECT_NAME,
    });

    if (isEmpty(project)) {
      toast.error(`Failed to create project`, {
        description:
          projectError instanceof Error
            ? projectError.message
            : `${projectError}`,
      });
      router.replace("/app");
      return;
    }

    try {
      setIsSaving(true);
      await createNewPlayground({
        projectId: project.id,
        flowId: newId(),
        sessionName: "Playground 1",
        datasetName: "Dataset 1",
        initialRecords: loggedOutPlaygroundData?.playgroundBlocks.map(
          ({ prompt_data, function_data }) => ({
            prompt_data,
            function_data,
          }),
        ),
        datasetRows: loggedOutPlaygroundData?.datasetRows.map(
          ({ input, expected, metadata }) => ({
            input,
            expected,
            metadata: isObject(metadata) ? metadata : undefined,
          }),
        ),
        scorerFunctions: loggedOutPlaygroundData?.scorerFunctions,
        promptSessionData: {
          scorers: loggedOutPlaygroundData?.savedScorers ?? [],
          settings: loggedOutPlaygroundData?.playgroundSettings,
        },
      });
    } catch (playgroundError) {
      toast.error("Failed to create playground.", {
        description:
          playgroundError instanceof Error
            ? playgroundError.message
            : `${playgroundError}`,
      });
      router.replace("/app");
    }
  }, [selectedOrg, loggedOutPlaygroundData, createNewPlayground, router]);

  useEffect(() => {
    if (orgInfo.length === 1) {
      handleProjectCreate();
    }
  }, [handleProjectCreate, orgInfo]);

  useHotkeys("enter", (e) => {
    // Don't trigger when pressing enter on an organization or when it's auto-saving
    if (e.target instanceof HTMLAnchorElement || orgInfo.length === 1) {
      return;
    }

    handleProjectCreate();
  });

  return (
    <div className="flex h-screen w-screen flex-col items-center justify-center bg-primary-50">
      <div className="flex flex-col items-center gap-4 text-center">
        {orgInfo.length > 1 && (
          <>
            <div className="flex items-center gap-2">
              <p>Save logged out playground to</p>
              <div className="w-40 truncate">
                {selectedOrg.name && selectedOrg.id && (
                  <HeaderMenu
                    type="orgs"
                    orgName={selectedOrg.name}
                    orgId={selectedOrg.id}
                    hideTooltip
                    fullWidth
                    disabled={isSaving}
                    triggerVariant="border"
                    onSelectOrg={(orgId) => {
                      const org = orgInfo.find((org) => org.id === orgId);
                      if (org) {
                        setSelectedOrg(org);
                      }
                    }}
                  />
                )}
              </div>
              <Button
                Icon={ArrowRight}
                size="xs"
                variant="inverted"
                onClick={handleProjectCreate}
                isLoading={isSaving}
              />
            </div>
          </>
        )}
        {isSaving && orgInfo.length === 1 && (
          <>
            <Spinner className="size-8 text-primary-600" />
            <div className="text-lg font-medium text-primary-900">
              Creating playground...
            </div>
          </>
        )}
      </div>
    </div>
  );
}
