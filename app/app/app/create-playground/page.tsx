import SessionRoot from "#/ui/root";
import { redirect } from "next/navigation";
import { getToplevelOrganizationInfo } from "../actions";
import ClientPage from "./clientpage";

export default async function CreatePlaygroundPage() {
  const orgInfo = await getToplevelOrganizationInfo({});
  if (!orgInfo || orgInfo.length === 0) {
    redirect("/app/setup?referrer=playground");
  }

  return (
    <SessionRoot loginRequired>
      <ClientPage orgInfo={orgInfo} />
    </SessionRoot>
  );
}
