"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { Input, inputClassName } from "#/ui/input";
import Header from "#/ui/landing/header";
import { type BtSession } from "#/utils/auth/server-session";
import { cn } from "#/utils/classnames";
import { DISCORD, INFO } from "#/utils/links";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { LandingFooterFlux } from "../landing-footer";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "#/ui/select";

enum ContactSubject {
  PRODUCT_SUPPORT = "Product support or report a bug",
  SALES_PRICING = "Sales and pricing",
  SECURITY_COMPLIANCE = "Security and compliance",
  COLLABORATION_OPPORTUNITIES = "Partnership inquires",
  OTHER = "Other",
}

const contactFormSchema = z.object({
  subject: z.nativeEnum(ContactSubject),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Please enter a valid email"),
  comment: z.string().min(1, "Comment is required"),
});

export const ContactClientPage = ({ session }: { session: BtSession }) => {
  const form = useForm<z.infer<typeof contactFormSchema> & { "": string }>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      subject: undefined,
      name: "",
      email: "",
      comment: "",
    },
  });

  return (
    <>
      <div className="px-4 sm:px-8">
        <div className="mx-auto max-w-landing font-display text-black">
          <Header session={session} />
          <section className="-mx-4 mb-48 border-t border-black px-4 py-16 sm:-mx-8 sm:px-8">
            <div className="mb-3 font-suisse text-xs tracking-wider uppercase">
              Contact
            </div>
            <h1 className="mb-8 text-6xl">Chat with us</h1>
            <div className="flex flex-col gap-12 lg:w-1/2">
              <div className="flex flex-1 flex-col justify-between gap-4">
                <p className="text-xl text-balance">
                  We would love to hear from you. Fill out the form or email us
                  at{" "}
                  <Link href={INFO} className="font-semibold text-brand-blue">
                    <EMAIL>
                  </Link>
                  . Have a technical question? Get help fast in our{" "}
                  <Link
                    href={DISCORD}
                    className="font-semibold text-brand-blue"
                  >
                    Discord
                  </Link>{" "}
                  channel.
                </p>
              </div>
              <div className="flex-1">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(async (data) => {
                      try {
                        const res = await fetch("/contact/submit", {
                          method: "POST",
                          body: JSON.stringify(data),
                          headers: {
                            "Content-Type": "application/json",
                          },
                        });
                        const json = await res.json();

                        if (!json.ok) {
                          toast.error(
                            "Something went wrong. Please email <NAME_EMAIL>.",
                          );
                          return;
                        }

                        form.reset({
                          subject: undefined,
                          name: "",
                          email: "",
                          comment: "",
                        });
                        toast.success("Thank you for registering");
                      } catch (error) {
                        toast.error(
                          "Something went wrong. Please email <NAME_EMAIL>.",
                        );
                      }
                    })}
                    className="flex flex-col gap-6"
                  >
                    <FormField
                      control={form.control}
                      name="subject"
                      render={({ field }) => {
                        return (
                          <FormItem className="flex-1">
                            <FormLabel className="text-base">
                              What do you need help with?
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger
                                  autoFocus
                                  className={cn(
                                    "bg-primary-50 text-base",
                                    !field.value && "text-muted-foreground",
                                  )}
                                >
                                  <SelectValue placeholder="Select a subject" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.values(ContactSubject).map(
                                    (value) => (
                                      <SelectItem
                                        className="text-sm"
                                        key={value}
                                        value={value}
                                        hideIndicator
                                      >
                                        {value}
                                      </SelectItem>
                                    ),
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        );
                      }}
                    />
                    <div className="flex flex-col gap-6 md:flex-row">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel className="text-base">
                              Full name
                            </FormLabel>
                            <FormControl>
                              <Input
                                className="text-base"
                                placeholder="Enter your full name"
                                {...field}
                                value={field.value ?? ""}
                                required
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormLabel className="text-base">
                              Work email
                            </FormLabel>
                            <FormControl>
                              <Input
                                className="text-base"
                                placeholder="Enter your work email"
                                {...field}
                                type="email"
                                value={field.value ?? ""}
                                required
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div>
                      <FormField
                        control={form.control}
                        name="comment"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base">
                              How can we help?
                            </FormLabel>
                            <FormControl>
                              <textarea
                                className={cn(
                                  inputClassName,
                                  "min-h-30 text-base",
                                )}
                                placeholder="Your company needs"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div>
                      <Button
                        type="submit"
                        className="h-10 bg-black! text-base text-white hover:cursor-pointer hover:bg-brand-blue hover:text-white disabled:opacity-50"
                        isLoading={form.formState.isSubmitting}
                        disabled={
                          !form.formState.isValid ||
                          form.getValues().subject === undefined
                        }
                      >
                        Submit
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>
            </div>
          </section>
        </div>
      </div>
      <LandingFooterFlux />
    </>
  );
};
