"use client";

import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "#/ui/accordion";
import Link from "next/link";

const faqItems = [
  {
    question: "Which plan is right for me?",
    answer: (
      <ul className="list-disc space-y-2 pl-6">
        <li>
          <span className="font-medium">Free</span>: Ideal for individuals or
          small teams getting started with Braintrust. It includes enough data
          ingestion, scoring, and data retention to explore and build small
          projects.
        </li>
        <li>
          <span className="font-medium">Pro</span>: Best suited for small teams
          of up to 5 people who are regularly running experiments or evaluations
          that require increased usage limits and longer data retention.
          Additional usage beyond included limits is billed flexibly, making it
          great for teams with growing or varying workloads.
        </li>
        <li>
          <span className="font-medium">Enterprise</span>: Recommended for
          larger organizations or teams with custom needs such as high volumes
          of data, special security requirements, on-premises deployment, or
          dedicated support.
        </li>
      </ul>
    ),
  },
  {
    question: "What does processed data mean?",
    answer:
      "Processed data refers to the data ingested by Braintrust when you create logs or experiments. This includes inputs, outputs, prompts, metadata, datasets, traces, and any related information. The cumulative size of this data (measured on disk) counts toward your monthly total, calculated from the first day to the last day of each calendar month.",
  },
  {
    question: "What are scores?",
    answer:
      "Scores are used to measure the results of offline or online evaluations run in Braintrust. Each time you record a score, including custom metrics, the total number of scores counted towards your monthly usage increases by one. Your monthly total is calculated cumulatively from the first to the last day of each calendar month.",
  },
  {
    question: "What are trace spans?",
    answer:
      "Spans are the fundamental units of observability in your traces. Each span represents a discrete operation in your application - like an LLM API call, prompt rendering, or evaluation step. Spans are automatically created when you use Braintrust's instrumentation and contribute to your monthly usage quota, which is calculated per calendar month.",
  },
  {
    question: "How do I track my usage?",
    answer:
      "You can track your usage by selecting View usage details in Settings > Billing. This will open your detailed usage report in the Orb usage portal, where you can view your current usage and monitor costs throughout the billing period.",
  },
  {
    question: "How does billing work?",
    answer:
      "The Free plan does not require a credit card to get started. You can upgrade to the Pro plan at any time via the Upgrade button in the top-right of your workspace. When you sign up for the Pro plan, you'll immediately be charged a prorated amount of the monthly $249 platform fee. For example, if you sign up on the 15th of the month, you'll pay about half of the monthly fee. On the 1st of the following month, you'll be charged the full $249 fee plus any additional usage-based charges incurred during the previous month. Charges will be processed automatically using the credit card provided at sign-up.",
  },
];

function FAQ() {
  return (
    <div className="mb-44 flex flex-col gap-4 lg:flex-row">
      <div className="w-full flex-none lg:w-1/3 lg:pr-12">
        <h3 className="mb-3 text-4xl">Frequently asked questions</h3>
        <p className="text-lg leading-tight text-balance">
          If you have any additional questions, please{" "}
          <Link href="/contact" className="font-semibold text-brand-blue">
            contact us
          </Link>
        </p>
      </div>
      <div className="flex-auto">
        <Accordion type="multiple">
          {faqItems.map((item, index) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="border-b border-black last:border-b-0"
            >
              <AccordionTrigger className="font-display text-xl hover:no-underline">
                {item.question}
              </AccordionTrigger>
              <AccordionContent className="text-base leading-snug">
                {item.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}

export { FAQ };
