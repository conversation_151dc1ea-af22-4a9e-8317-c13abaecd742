"use client";

import { Slider } from "#/ui/slider";
import { useState, useMemo } from "react";

import { Data, Score } from "../shapes";
import { cn } from "#/utils/classnames";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { CircleHelp } from "lucide-react";

const FREE_STORAGE_GB = 1;
const FREE_SCORES = 10000;
const PRO_BASE_COST = 249;
const PRO_STORAGE_GB = 5;
const PRO_SCORES = 50000;
const STORAGE_COST_PER_GB = 3;
const SCORING_COST_PER_1000 = 1.5;

const TOKENS_PER_REQUEST = 8000;
const LOGGING_OVERHEAD = 2;
const DEFAULT_NUMBER_OF_SCORERS = 3;

export const PricingCalculator = () => {
  const [tokensPerMonth, setTokensPerMonth] = useState(1000000);
  const [scoringPercentage, setScoringPercentage] = useState(0.1);

  // Define nice round numbers for different magnitudes
  const niceNumbers = useMemo(
    () => [
      10000, 15000, 25000, 35000, 50000, 65000, 75000, 85000, 100000, 150000,
      250000, 350000, 500000, 650000, 750000, 850000, 1000000, 1500000, 2500000,
      3500000, 5000000, 6500000, 7500000, 8500000, 10000000, 15000000, 25000000,
      35000000, 50000000, 65000000, 75000000, 85000000, 100000000, 150000000,
      250000000, 350000000, 500000000, 650000000, 750000000, 850000000,
      1000000000, 1500000000, 2000000000, 3000000000, 4000000000, 5000000000,
    ],
    [],
  );

  // Calculate the current slider index based on tokens
  const currentSliderIndex = useMemo(() => {
    // Find the index of the closest nice number
    const findClosestIndex = (value: number) => {
      let closestIndex = 0;
      let minDiff = Math.abs(value - niceNumbers[0]);

      for (let i = 0; i < niceNumbers.length; i++) {
        const diff = Math.abs(value - niceNumbers[i]);
        if (diff < minDiff) {
          minDiff = diff;
          closestIndex = i;
        }
      }

      return closestIndex;
    };

    return findClosestIndex(tokensPerMonth);
  }, [tokensPerMonth, niceNumbers]);

  // Calculate scores using the formula: (tok/10,000)*[#scorers]*[scoring%]
  const calculatedScoreCount =
    (tokensPerMonth / TOKENS_PER_REQUEST) *
    DEFAULT_NUMBER_OF_SCORERS *
    scoringPercentage;

  // Calculate GB usage: tokens * 4 bytes * overhead / 1GB
  const calculatedGbUsage =
    (tokensPerMonth * 4 * LOGGING_OVERHEAD) / (1024 * 1024 * 1024);

  const exceedsFreeLimit =
    calculatedGbUsage > FREE_STORAGE_GB || calculatedScoreCount > FREE_SCORES;

  const proStorageOverage = Math.max(0, calculatedGbUsage - PRO_STORAGE_GB);
  const proScoreOverage = Math.max(0, calculatedScoreCount - PRO_SCORES);

  const proStorageCost = proStorageOverage * STORAGE_COST_PER_GB;
  const proScoringCost = proScoreOverage * (SCORING_COST_PER_1000 / 1000);

  const proCost = PRO_BASE_COST + proStorageCost + proScoringCost;

  return (
    // eslint-disable-next-line better-tailwindcss/no-unregistered-classes
    <div className="pricing-calculator mb-24 flex flex-col gap-4 pt-32 lg:flex-row">
      <div className="w-full flex-none lg:w-1/3 lg:pr-12">
        <h3 className="mb-3 text-4xl">Pricing calculator</h3>
        <p className="text-lg leading-tight text-balance">
          Estimate your monthly costs based on your usage
        </p>
      </div>
      <div className="flex flex-auto flex-col gap-3 rounded-xl border border-primary-200/80 bg-primary-200/40 p-1">
        <div className="flex flex-none flex-col gap-4 rounded-lg border-0 bg-white p-4 pb-5 font-normal shadow-[0px_1px_1px_-0.5px_rgba(0,0,0,0.06),0px_3px_3px_-1.5px_rgba(0,0,0,0.06),0px_6px_6px_-3px_rgba(0,0,0,0.06),0px_12px_12px_-6px_rgba(0,0,0,0.06),0px_24px_24px_0px_rgba(0,0,0,0.06)] outline-1 outline-[rgba(0,0,0,0.06)] outline-solid dark:bg-primary-200 dark:outline-[rgb(var(--primary-700)/.16)]">
          <div className="flex items-center justify-between text-sm">
            Usage
            <div
              className={cn(
                "w-[50px] gap-2 rounded-full bg-primary-200 px-2 py-0.5 text-center text-xs transition-colors dark:bg-primary-700/30",
                {
                  "bg-brand-green text-brand-forest": exceedsFreeLimit,
                },
              )}
            >
              {exceedsFreeLimit ? "PRO" : "FREE"}
            </div>
          </div>
          <div className="flex flex-col gap-6 md:flex-row">
            <div className="flex flex-1 flex-col gap-3">
              <div className="flex gap-2 text-base text-primary-500">
                <span className="text-primary-900 tabular-nums">
                  {tokensPerMonth.toLocaleString()}
                </span>
                Tokens per month
              </div>
              <Slider
                value={[currentSliderIndex]}
                onValueChange={(v) => setTokensPerMonth(niceNumbers[v[0]])}
                rangeClassName="bg-primary-900"
                min={0}
                max={niceNumbers.length - 1}
                step={1}
                trackClassName="dark:bg-primary-700/30"
              />
            </div>
            <div className="flex flex-1 flex-col gap-3">
              <div className="flex gap-2 text-base text-primary-500">
                <span className="text-primary-900 tabular-nums">
                  {scoringPercentage.toLocaleString(undefined, {
                    style: "percent",
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2,
                  })}
                </span>
                Scoring percentage
              </div>
              <Slider
                value={[scoringPercentage]}
                onValueChange={(v) => setScoringPercentage(v[0])}
                rangeClassName="bg-primary-900"
                min={0.01}
                max={1}
                step={0.01}
                trackClassName="dark:bg-primary-700/30"
              />
            </div>
          </div>
        </div>
        <div className="mt-2 flex justify-between gap-4 px-4 text-base">
          <div className="flex items-center gap-2 text-primary-500">
            <div className="flex size-5 items-center justify-center">
              <Data className="size-4 text-gray-400" />
            </div>
            Storage
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-primary-500 tabular-nums">
              {calculatedGbUsage.toLocaleString(undefined, {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              })}{" "}
              GB of {exceedsFreeLimit ? 5 : 1} GB
            </span>
            <span className="tabular-nums">
              {proStorageCost.toLocaleString(undefined, {
                style: "currency",
                currency: "USD",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
        </div>
        <div className="flex justify-between gap-4 px-4 text-base">
          <div className="flex items-center gap-2 text-primary-500">
            <div className="flex size-5 items-center justify-center">
              <Score className="size-4 text-gray-400" />
            </div>
            Scores
          </div>
          <div>
            <span className="flex items-center gap-2 tabular-nums">
              <span className="text-sm text-primary-500 tabular-nums">
                {calculatedScoreCount.toLocaleString(undefined, {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                })}{" "}
                of {(exceedsFreeLimit ? 50000 : 10000).toLocaleString()}
                <span className="hidden sm:inline"> scores</span>
              </span>
              {proScoringCost.toLocaleString(undefined, {
                style: "currency",
                currency: "USD",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
        </div>
        <div className="mx-4 border-t border-dashed border-primary-300" />
        <div className="flex justify-between gap-4 px-4 text-base">
          <div className="flex items-center gap-2 text-primary-500">
            Estimated monthly total
          </div>
          <div className="flex items-center gap-2 font-medium tabular-nums">
            {(exceedsFreeLimit ? proCost : 0).toLocaleString(undefined, {
              style: "currency",
              currency: "USD",
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </div>
        </div>
        <div className="px-4 pb-3">
          <CollapsibleSection
            defaultCollapsed
            className="inline-flex flex-none hover:bg-background"
            collapsedClassName="px-0 hover:px-1.5 bg-transparent text-primary-400"
            expandedClassName="text-primary-500"
            title={
              <div className="flex items-center gap-1">
                <CircleHelp className="size-3" />
                How is this calculated?
              </div>
            }
          >
            <p className="text-sm text-primary-500">
              This calculator assumes 8,000 tokens per request and 3 scorers on
              average. Storage is based on 4 bytes per token and an overhead of
              2x.
            </p>
          </CollapsibleSection>
        </div>
      </div>
    </div>
  );
};
