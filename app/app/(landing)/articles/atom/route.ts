import { getPages } from "../source";

export async function GET() {
  const posts = getPages()
    .filter((page) => !page.data.draft)
    .sort(
      (a, b) =>
        new Date(b.data.date).getTime() - new Date(a.data.date).getTime(),
    )
    .slice(0, 20); // Latest 20 posts

  const feed = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:content="http://purl.org/rss/1.0/modules/content/">
  <channel>
    <title>Braintrust articles</title>
    <link>https://www.braintrust.dev/articles</link>
    <description>In-depth articles and insights about AI evaluation, development best practices, and technical deep dives from the Braintrust team.</description>
    <language>en-us</language>
    <atom:link href="https://www.braintrust.dev/articles/atom" rel="self" type="application/rss+xml"/>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <managingEditor><EMAIL> (Braintrust Team)</managingEditor>
    <webMaster><EMAIL> (Braintrust Team)</webMaster>
    <ttl>1440</ttl>
    <image>
      <url>https://www.braintrust.dev/icon512.png</url>
      <title>Braintrust articles</title>
      <link>https://www.braintrust.dev/articles</link>
      <width>144</width>
      <height>144</height>
    </image>
    <category>Technology</category>
    <category>Artificial Intelligence</category>
    <category>Machine Learning</category>
    <category>Software Development</category>
${posts
  .map(
    (post) => `    <item>
      <title><![CDATA[${post.data.title}]]></title>
      <link>https://www.braintrust.dev${post.url}</link>
      <guid isPermaLink="true">https://www.braintrust.dev${post.url}</guid>
      <pubDate>${new Date(post.data.date).toUTCString()}</pubDate>
      <description><![CDATA[${post.data.description || ""}]]></description>
      <author><EMAIL> (${post.data.authors?.join(", ") || "Braintrust Team"})</author>
      ${post.data.tags ? post.data.tags.map((tag) => `<category><![CDATA[${tag}]]></category>`).join("\n      ") : ""}
      ${post.data.image ? `<enclosure url="https://www.braintrust.dev${post.data.image}" type="image/png" length="0"/>` : ""}
    </item>`,
  )
  .join("\n")}
  </channel>
</rss>`;

  return new Response(feed, {
    headers: {
      "Content-Type": "application/rss+xml; charset=utf-8",
      "Cache-Control": "public, max-age=3600, stale-while-revalidate=1800",
    },
  });
}
