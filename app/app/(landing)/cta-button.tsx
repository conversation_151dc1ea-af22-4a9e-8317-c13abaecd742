"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { Button, type ButtonProps, buttonVariants } from "#/ui/button";

import { useRouter } from "next/navigation";
import { signInPath, signUpPath } from "#/utils/auth/redirects";
import { cn } from "#/utils/classnames";
import Link from "next/link";

interface Props {
  className?: string;
  cta?: React.ReactNode;
  loggedIn?: boolean;
  variant?: ButtonProps["variant"];
  size?: ButtonProps["size"];
}

export const CtaLink = ({
  className,
  cta = "Sign up for free",
  loggedIn = false,
  variant = "success",
  size = "lg",
}: Props) => {
  return (
    <Link
      href={loggedIn ? signInPath() : signUpPath()}
      className={cn(buttonVariants({ variant, size }), className)}
      onClick={() => {
        if (loggedIn) {
          analytics?.track("signin", {
            source: "cta_button",
          });
        } else {
          analytics?.track("signup", {
            source: "cta_button",
          });
        }
      }}
    >
      {cta}
    </Link>
  );
};

const CtaButton = ({
  cta = "Sign up for free",
  className,
  variant = "success",
  size = "lg",
  loggedIn = false,
}: Props) => {
  const { analytics } = useAnalytics();
  const router = useRouter();
  return (
    <Button
      className={className}
      variant={variant}
      size={size}
      onClick={(e) => {
        e.preventDefault();
        if (loggedIn) {
          analytics?.track("signin", {
            source: "cta_button",
          });
          router.push(signInPath());
        } else {
          analytics?.track("signup", {
            source: "cta_button",
          });
          router.push(signUpPath());
        }
      }}
    >
      {cta}
    </Button>
  );
};
export default CtaButton;
