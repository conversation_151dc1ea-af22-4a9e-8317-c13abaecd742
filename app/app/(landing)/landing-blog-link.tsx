import { ArrowUpRight } from "lucide-react";
import Link from "next/link";

export const LandingBlogLink = ({
  href,
  title,
}: {
  href: string;
  title: string;
}) => (
  <Link
    href={href}
    className="flex px-0 py-8 text-3xl leading-tight tracking-tight text-balance transition-all hover:bg-gray-100 hover:px-4 hover:text-black md:hover:px-8"
  >
    <span className="flex-1">{title}</span>
    <ArrowUpRight className="size-5 flex-none" />
  </Link>
);
