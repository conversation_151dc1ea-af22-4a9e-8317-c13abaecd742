export const navigationLinks = [
  { title: "<PERSON><PERSON>", href: "/docs" },
  { title: "Pricing", href: "/pricing" },
  { title: "Blog", href: "/blog" },
  { title: "Careers", href: "/careers" },
  { title: "Chat with us", href: "/contact" },
];

export const footerLinks = [
  ...navigationLinks,
  { title: "Discord", href: "https://discord.gg/6G8s47F44X" },
  { title: "Changelog", href: "/docs/reference/changelog" },
  { title: "Status", href: "https://status.braintrust.dev/" },
  { title: "Trust center", href: "https://trust.braintrust.dev/" },
  { title: "Privacy", href: "/legal/privacy-policy" },
  { title: "Terms", href: "/legal/terms-of-service" },
];

export const tagline = "Stop building AI in the dark";

export const summary =
  "Building with AI can feel like flying blind. Braintrust turns on the lights with enterprise-grade evaluations, prompt playground, data management, and more.";

export const features = [
  {
    title: "Evaluations",
    description:
      "We make it extremely easy to score, log, and visualize outputs. Interrogate failures; track performance over time; instantly answer questions like “which examples regressed when I made a change?”, and “what happens if I try this new model?”",
    image: "/landing/features/evaluations.png",
    darkImage: "/landing/features/evaluations-dark.png",
  },
  {
    title: "Logging",
    description:
      "Capture production and staging data with the same code and UI as your evals. Run online evals, capture user feedback, debug issues, and most importantly, find interesting cases to run your evals on.",
    image: "/landing/features/logging.png",
    darkImage: "/landing/features/logging-dark.png",
  },
  {
    title: "Prompt Playground",
    description:
      "Compare multiple prompts, benchmarks, respective input/output pairs between runs. Tinker ephemerally, or turn your draft into an experiment to evaluate over a large dataset.",
    image: "/landing/features/prompt-playground.png",
    darkImage: "/landing/features/prompt-playground-dark.png",
  },
  {
    title: "Continuous Integration",
    description:
      "Leverage Braintrust in your continuous integration workflow so you can track progress on your main branch, and automatically compare new experiments to what’s live before you ship.",
    image: "/landing/features/continuous-integration.png",
    darkImage: "/landing/features/continuous-integration-dark.png",
  },
  {
    title: "Datasets",
    description:
      "Easily capture rated examples from staging & production, evaluate them, and incorporate them into “golden” datasets. Datasets reside in your cloud and are automatically versioned, so you can evolve them without risk of breaking evaluations that depend on them.",
    image: "/landing/features/datasets.png",
    darkImage: "/landing/features/datasets-dark.png",
  },
  {
    title: "Proxy",
    description:
      "Access the world's best AI models with a single API, including all of OpenAI's models, Anthropic models, " +
      "LLaMa 2, Mistral, and others, with caching, API key management, load balancing, and more built in.",
    // https://gist.github.com/ankrgyl/e683c5e2f5c392313a074f02bba13e1c
    image: "/landing/features/proxy.png",
    darkImage: "/landing/features/proxy-dark.png",
  },
];
