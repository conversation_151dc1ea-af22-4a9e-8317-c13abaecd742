"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";

const TABS = [
  { name: "Iterate" as const, images: [1, 2, 3] },
  { name: "<PERSON><PERSON>" as const, images: [1, 2, 3] },
  { name: "Ship" as const, images: [1, 2, 3] },
];

export const LandingCarousel = () => {
  const [activeTab, setActiveTab] = useState<"Iterate" | "Eval" | "Ship">(
    "Iterate",
  );
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const activeTabRef = useRef(activeTab);

  // Keep ref in sync with state
  useEffect(() => {
    activeTabRef.current = activeTab;
  }, [activeTab]);

  // Start interval on mount and restart when tab changes
  useEffect(() => {
    setCurrentImageIndex(0);

    // Clear existing interval and start a new one
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      setCurrentImageIndex((prev) => {
        if (prev === 2) {
          // When we reach the last image, advance to next tab
          const currentIndex = TABS.findIndex(
            (tab) => tab.name === activeTabRef.current,
          );
          const nextIndex = (currentIndex + 1) % TABS.length;
          setActiveTab(TABS[nextIndex].name);
          return 0;
        }
        return prev + 1;
      });
    }, 3000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [activeTab]);

  return (
    <div className="mb-24">
      {/* Tab Navigation */}
      <div className="mb-6 flex gap-2">
        {TABS.map((tab) => (
          <Button
            key={tab.name}
            variant={tab.name === activeTab ? "primary" : "ghost"}
            onClick={() => {
              setActiveTab(tab.name);
            }}
            className={cn(
              "h-auto rounded-sm px-3 py-1.5 font-suisse text-xs text-brand-blue3 uppercase hover:bg-transparent hover:text-white hover:shadow-none",
              {
                "pointer-events-none bg-brand-blue2/40 text-white":
                  tab.name === activeTab,
              },
            )}
          >
            {tab.name}
          </Button>
        ))}
      </div>

      <div className="relative h-auto w-full overflow-hidden rounded-md bg-white">
        {TABS.map((tab) => (
          <div
            key={tab.name}
            className={cn(
              "transition-opacity duration-300",
              activeTab === tab.name
                ? "opacity-100"
                : "absolute inset-0 opacity-0",
            )}
          >
            {tab.images.map((imageNum, index) => (
              <Image
                key={`${tab.name}-${imageNum}`}
                src={`/img/UI_Preview_${tab.name}_${imageNum}.png`}
                alt={`${tab.name} Preview ${imageNum}`}
                className={cn(
                  "h-auto w-full rounded-md transition-opacity duration-500",
                  currentImageIndex === index
                    ? "opacity-100"
                    : "absolute inset-0 opacity-0",
                )}
                height={731}
                width={1312}
                priority={activeTab === tab.name && index === 0}
                quality={100}
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};
