"use client";

import { useState, useEffect, type RefObject } from "react";
import { LottieAnimation } from "./lottie-animation";

import iterateLottieData from "./landing-iterate.json";
import evalLottieData from "./landing-eval.json";
import shipLottieData from "./landing-ship.json";

interface StickyLottieAnimationProps {
  evalSectionRef: RefObject<HTMLDivElement | null>;
  shipSectionRef: RefObject<HTMLDivElement | null>;
}

export const StickyLottieAnimation = ({
  evalSectionRef,
  shipSectionRef,
}: StickyLottieAnimationProps) => {
  const [currentAnimationData, setCurrentAnimationData] = useState<
    typeof iterateLottieData | typeof evalLottieData | typeof shipLottieData
  >(iterateLottieData);

  useEffect(() => {
    if (!evalSectionRef.current || !shipSectionRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (entry.target === evalSectionRef.current) {
              setCurrentAnimationData(evalLottieData);
            } else if (entry.target === shipSectionRef.current) {
              setCurrentAnimationData(shipLottieData);
            }
          } else {
            // When a section leaves viewport, determine which animation to show
            if (
              entry.target === evalSectionRef.current ||
              entry.target === shipSectionRef.current
            ) {
              // Use a small delay to handle rapid scrolling
              setTimeout(() => {
                // Check current intersection state
                const evalElement = evalSectionRef.current;
                const shipElement = shipSectionRef.current;

                if (evalElement && shipElement) {
                  const evalRect = evalElement.getBoundingClientRect();
                  const shipRect = shipElement.getBoundingClientRect();
                  const viewportHeight = window.innerHeight;

                  const evalVisible =
                    evalRect.top < viewportHeight * 0.5 &&
                    evalRect.bottom > viewportHeight * 0.5;
                  const shipVisible =
                    shipRect.top < viewportHeight * 0.5 &&
                    shipRect.bottom > viewportHeight * 0.5;

                  if (!evalVisible && !shipVisible) {
                    // Check if we've scrolled past the ship section
                    const pastShip = shipRect.bottom < viewportHeight * 0.5;

                    if (pastShip) {
                      // If we're past ship, stay on ship animation
                      setCurrentAnimationData(shipLottieData);
                    } else {
                      // If we're above both sections, show iterate
                      setCurrentAnimationData(iterateLottieData);
                    }
                  }
                }
              }, 100);
            }
          }
        });
      },
      {
        threshold: 0.5, // Trigger when 50% of the element is visible
      },
    );

    observer.observe(evalSectionRef.current);
    observer.observe(shipSectionRef.current);

    return () => observer.disconnect();
  }, [evalSectionRef, shipSectionRef]);

  return (
    <div className="pointer-events-none absolute inset-x-12 top-12 bottom-20 hidden lg:block">
      <div className="sticky top-[calc(50vh-310px)] left-1/2 z-50 h-[620px] w-1/2 max-w-[720px] overflow-hidden bg-brand-forest">
        <LottieAnimation
          animationData={currentAnimationData}
          width={774}
          height={618}
        />
      </div>
    </div>
  );
};
