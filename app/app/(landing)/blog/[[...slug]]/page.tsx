import { getPages, getPage } from "../source";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { ArrowLeft, ArrowUpRight, Rss } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { InlineTOC } from "fumadocs-ui/components/inline-toc";
import "./blog-style.css";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { buildMetadata } from "#/app/metadata";
import { PageTracker } from "#/ui/use-analytics";
import Header from "#/ui/landing/header";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { StructuredData } from "#/ui/structured-data";
import {
  generateArticleSchema,
  generateBreadcrumbSchema,
} from "#/lib/structured-data";
import { LandingFooterFlux } from "../../landing-footer";

export default async function Page(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const session = await getServerAuthSession();
  if (!params.slug) {
    const posts = getPages()
      .filter((page) => !page.data.draft)
      .sort(
        (a, b) =>
          new Date(b.data.date).getTime() - new Date(a.data.date).getTime(),
      );

    const firstPost = posts[0];
    const restPosts = posts.slice(1);
    return (
      <PageTracker category="blog">
        <div className="px-4 sm:px-8">
          <div className="mx-auto max-w-landing">
            <Header session={session} />
            <div className="relative -mx-4 border-y border-black text-black min-[1440px]:border-x sm:-mx-8">
              <Link
                href="/blog/atom"
                className="absolute top-6 right-8 text-black"
              >
                <Rss className="size-4" />
              </Link>
              <Link
                href={firstPost.url}
                className="group block border-b border-black px-8 py-6 transition-colors hover:bg-gray-100"
              >
                <div className="mb-20 flex items-center justify-between font-suisse text-xs tracking-wider uppercase">
                  Latest Braintrust news
                </div>
                <div className="mb-2 text-7xl leading-16 tracking-tight text-balance lg:w-2/3">
                  {firstPost.data.title}
                </div>
                <div className="mb-20 text-lg">
                  {firstPost.data.description}
                </div>
                <div className="flex gap-24 font-suisse text-xs tracking-wider uppercase">
                  <span>{firstPost.data.date}</span>
                  <span>{firstPost.data.authors?.join(", ")}</span>
                  <span className="flex flex-1 justify-end opacity-0 transition-opacity group-hover:opacity-100">
                    <ReadArrow />
                  </span>
                </div>
              </Link>
              <section className="grid grid-cols-1 gap-px bg-black md:grid-cols-2 lg:grid-cols-3">
                {restPosts.map((post) => (
                  <Link
                    key={post.url}
                    href={post.url}
                    className="group relative flex h-80 cursor-pointer flex-col justify-end bg-white p-8 text-black transition-colors hover:bg-gray-100"
                  >
                    <span className="absolute top-6 right-8 opacity-0 transition-opacity group-hover:opacity-100">
                      <ReadArrow />
                    </span>
                    <div className="mb-2 text-4xl tracking-tight text-balance">
                      {post.data.title}
                    </div>
                    <div className="mb-6 truncate text-base">
                      {post.data.description}
                    </div>
                    <div className="flex gap-24 font-suisse text-xs tracking-wider uppercase">
                      {post.data.date}
                      {post.data.authors?.join(", ")}
                    </div>
                  </Link>
                ))}
              </section>
            </div>
          </div>
        </div>
        <LandingFooterFlux />
      </PageTracker>
    );
  }

  const page = getPage(params.slug);

  if (!page || page.data.draft) notFound();

  const breadcrumbs = [
    { name: "Home", url: "https://www.braintrust.dev" },
    { name: "Blog", url: "https://www.braintrust.dev/blog" },
    { name: page.data.title, url: `https://www.braintrust.dev${page.url}` },
  ];

  return (
    <PageTracker category="blog">
      <StructuredData
        data={[
          generateArticleSchema({
            title: page.data.title,
            description: page.data.description || "",
            url: `https://www.braintrust.dev${page.url}`,
            publishedTime: String(page.data.date),
            modifiedTime: page.data.lastModified
              ? String(page.data.lastModified)
              : String(page.data.date),
            authors: page.data.authors || ["Braintrust Team"],
            tags: page.data.tags || [],
          }),
          generateBreadcrumbSchema(breadcrumbs),
        ]}
      />
      <div className="px-4 sm:px-8">
        <div className="mx-auto max-w-landing">
          <Header session={session} />
          <div className="border-t border-black pt-12">
            <Link
              href="/blog"
              className="mb-6 flex items-center gap-1 font-suisse text-xs tracking-wider text-primary-600 uppercase underline-offset-4 hover:underline"
            >
              <ArrowLeft className="size-3" /> Latest news
            </Link>
            {/* eslint-disable-next-line better-tailwindcss/no-unregistered-classes */}
            <div className="blog-article flex">
              <div
                className={cn(
                  proseMDXBaseClassName,
                  "prose-xl mb-44 max-w-(--breakpoint-xl) flex-1 overflow-hidden font-display text-xl leading-[1.4] prose-headings:font-display prose-headings:font-medium prose-h1:text-4xl prose-h1:font-semibold prose-h1:text-balance sm:prose-h1:text-5xl sm:prose-h1:leading-[1.2] sm:prose-h1:font-semibold md:prose-h1:text-6xl md:prose-h1:leading-[1]",
                )}
              >
                <page.data.body components={defaultMdxComponents} />
              </div>
              {/* eslint-disable-next-line better-tailwindcss/no-unregistered-classes */}
              <div className="blog-toc sticky top-0 hidden max-h-screen flex-none overflow-auto py-9 pl-8 lg:block">
                <InlineTOC defaultOpen items={page.data.toc} />
              </div>
            </div>
          </div>
        </div>
      </div>
      <LandingFooterFlux />
    </PageTracker>
  );
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  if (!params.slug) {
    return {
      ...buildMetadata({
        title: "Latest Braintrust news",
        description:
          "Latest insights, tutorials, and updates from the Braintrust team. Learn about AI evaluation, LLM observability, and best practices for building reliable AI products.",
        relativeUrl: "/blog",
        ogTemplate: "blog",
        tags: [
          "AI blog",
          "LLM tutorials",
          "AI evaluation",
          "machine learning",
          "developer resources",
        ],
      }),
      head: [
        {
          tag: "link",
          props: {
            rel: "alternate",
            type: "application/rss+xml",
            title: "Braintrust blog RSS feed",
            href: "/blog/atom",
          },
        },
      ],
    };
  }
  const page = getPage(params.slug);

  if (page == null) notFound();

  const imageUrl = page.data.image;
  const twimageUrl = imageUrl ?? page.data.twimage;

  return buildMetadata({
    title: `${page.data.title}`,
    sections: ["Blog"],
    description: page.data.description,
    relativeUrl: `/blog/${params.slug.join("/")}`,
    ogImageUrl: imageUrl,
    ogTemplate: "blog",
    twitterImageUrl: twimageUrl,
    type: "article",
    publishedTime: String(page.data.date),
    modifiedTime: page.data.lastModified
      ? String(page.data.lastModified)
      : page.data.date,
    authors: page.data.authors || ["Braintrust Team"],
    tags: page.data.tags || ["AI", "machine learning", "evaluation"],
  });
}

const ReadArrow = () => (
  <div className="flex items-center gap-1 rounded-xs bg-brand-blue py-0.5 pr-1 pl-2 text-white">
    <span className="font-suisse text-xs tracking-wider uppercase">Read</span>
    <ArrowUpRight className="size-4" />
  </div>
);
