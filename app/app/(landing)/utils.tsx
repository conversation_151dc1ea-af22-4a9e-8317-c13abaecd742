"use client";
import { useCallback, useEffect, useState } from "react";

export const useBoundingRect = (ref: React.RefObject<HTMLElement | null>) => {
  const [boundingRect, setBoundingRect] = useState<DOMRect | null>(null);

  const calculateBoundingBox = useCallback(() => {
    if (ref.current) {
      setBoundingRect(ref.current.getBoundingClientRect());
    }
  }, [ref]);

  useEffect(() => {
    calculateBoundingBox();
    window.addEventListener("resize", calculateBoundingBox, { passive: true });
    window.addEventListener("scroll", calculateBoundingBox, { passive: true });

    return () => {
      window.removeEventListener("resize", calculateBoundingBox);
      window.removeEventListener("scroll", calculateBoundingBox);
    };
  }, [calculateBoundingBox]);

  return boundingRect;
};

export const useMousePosition = () => {
  const animationsDisabled = useAnimationsDisabled();
  const [mousePosition, setMousePosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  useEffect(() => {
    if (animationsDisabled) return;

    const handleMouseMove = (event: MouseEvent) => {
      setMousePosition({
        x: event.pageX,
        y: event.pageY,
      });
    };

    window.addEventListener("mousemove", handleMouseMove, { passive: true });

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [animationsDisabled]);

  return mousePosition;
};

export const useAnimationsDisabled = () => {
  const [animationsDisabled, setAnimationsDisabled] = useState<boolean>(false);

  useEffect(() => {
    const userAgent = window.navigator.userAgent;

    const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
    const isTouch = "ontouchstart" in window || navigator.maxTouchPoints > 0;
    const lowHardwareConcurrency = window.navigator.hardwareConcurrency < 5;

    setAnimationsDisabled(isSafari || isTouch || lowHardwareConcurrency);
  }, []);

  return animationsDisabled;
};
