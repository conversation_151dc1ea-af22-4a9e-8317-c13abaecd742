import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { createHash } from "crypto";
import { sha256 } from "#/utils/hash";
import { KV } from "#/utils/cache";
import { waitUntil } from "@vercel/functions";
import { decryptMessage } from "@braintrust/proxy/utils";
import { authCodeCachedDataSchema, makeMcpCodeCacheKey } from "./utils";

const exchangeAuthCodeSchema = z.object({
  code: z.string().min(1),
  client_id: z.string().min(1),
  // PKCE parameter
  code_verifier: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { code, client_id, code_verifier } =
      exchangeAuthCodeSchema.parse(body);

    const cacheKey = makeMcpCodeCacheKey({
      code,
      // No matter what, we hash the code verifier while constructing the cache key
      codeChallenge: code_verifier
        ? sha256(code_verifier, "base64url")
        : undefined,
    });

    // Find the authorization code
    const kv = new KV();
    const authCodeDataResponse = await kv.get(
      cacheKey,
      authCodeCachedDataSchema,
    );

    if (authCodeDataResponse.status === "not_found") {
      return NextResponse.json(
        { error: "invalid_grant", message: "Authorization code not found" },
        { status: 400 },
      );
    }
    const authCodeData = authCodeDataResponse.value;

    // Check if code has expired
    if (authCodeData.expires_at < Date.now()) {
      return NextResponse.json(
        { error: "invalid_grant", message: "Authorization code has expired" },
        { status: 400 },
      );
    }

    // Check if code has already been used
    if (authCodeData.used) {
      return NextResponse.json(
        { error: "invalid_grant", message: "Authorization code already used" },
        { status: 400 },
      );
    }

    // Verify client_id matches
    if (authCodeData.client_id !== client_id) {
      return NextResponse.json(
        { error: "invalid_client", message: "Client ID does not match" },
        { status: 400 },
      );
    }

    // PKCE validation if challenge was provided
    if (authCodeData.code_challenge) {
      if (!code_verifier) {
        return NextResponse.json(
          {
            error: "invalid_request",
            message: "Code verifier required for PKCE",
          },
          { status: 400 },
        );
      }

      // Validate PKCE challenge
      let expectedChallenge: string;
      if (authCodeData.code_challenge_method === "S256") {
        // SHA256 hash and base64url encode
        expectedChallenge = createHash("sha256")
          .update(code_verifier)
          .digest("base64url");
      } else {
        // Plain method - verifier equals challenge
        expectedChallenge = code_verifier;
      }

      if (expectedChallenge !== authCodeData.code_challenge) {
        return NextResponse.json(
          {
            error: "invalid_grant",
            message: "PKCE challenge verification failed",
          },
          { status: 400 },
        );
      }
    }

    // Mark code as used (single-use principle)
    authCodeData.used = true;
    waitUntil(kv.set(cacheKey, authCodeData));

    const authCodeEncryptionKey = sha256(code, "base64");
    const decryptedToken = await decryptMessage(
      authCodeEncryptionKey,
      authCodeData.iv,
      authCodeData.encrypted_token,
    );

    // Return the Braintrust token as access token
    return NextResponse.json({
      access_token: decryptedToken,
      token_type: "Bearer",
      expires_in: Math.floor(
        Math.max(
          (authCodeData.expires_at - Date.now() - 10000) /*10s buffer*/ / 1000,
          0,
        ),
      ), // OAuth spec wants this to be an int
      scope: "mcp",
    });
  } catch (error) {
    console.error("Auth code exchange error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "invalid_request", message: "Invalid request body" },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "server_error", message: "Internal server error" },
      { status: 500 },
    );
  }
}
