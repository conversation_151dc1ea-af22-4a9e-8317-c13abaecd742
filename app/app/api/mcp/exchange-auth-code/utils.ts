import { z } from "zod";
import { sha256 } from "#/utils/hash";

// See app/app/redirects/mcp/page.tsx for a full explanation of how this works.
export const authCodeCachedDataSchema = z.object({
  client_id: z.string(),
  code_challenge: z.string().optional(),
  code_challenge_method: z.enum(["S256", "plain"]),
  expires_at: z.number(),
  used: z.boolean(),

  // The token is encrypted in terms of the auth code, which is not stored
  // anywhere, which means if this data is compromised, the token is still safe.
  encrypted_token: z.string(),
  iv: z.string(),
});

export type AuthCodeCachedData = z.infer<typeof authCodeCachedDataSchema>;

export function makeMcpCodeCacheKey({
  code,
  codeChallenge,
}: {
  code: string;
  codeChallenge: string | undefined;
}): string {
  return sha256(`${code}|${codeChallenge || "no challenge"}`);
}
