import type Orb from "orb-billing";

export type OrbWebhookPayload = {
  id: string;
  created_at: string;
  properties: Record<string, unknown>;
  type: string;
};

export type SubscriptionStartedPayload = OrbWebhookPayload & {
  type: "subscription.started";
  subscription: Orb.Subscription;
};

export type SubscriptionPlanChangedPayload = OrbWebhookPayload & {
  type: "subscription.plan_changed";
  subscription: Orb.Subscription;
};

export type SubscriptionUsageExceededPayload = OrbWebhookPayload & {
  type: "subscription.usage_exceeded";
  subscription: Orb.Subscription;
  properties: {
    billable_metric_id: string;
    timeframe_start: string;
    timeframe_end: string;
    quantity_threshold: number;
    alert_configuration: Orb.Alert;
  };
};

export type InvoicePaymentFailedPayload = OrbWebhookPayload & {
  type: "invoice.payment_failed";
  invoice: Orb.Invoice;
  properties: {
    payment_provider: "stripe";
    payment_provider_id: string;
  };
};
