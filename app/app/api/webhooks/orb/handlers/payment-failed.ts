import * as React from "react";
import { type InvoicePaymentFailedPayload } from "../types";

import { z } from "zod";
import { resend } from "#/lib/resend";
import PaymentFailedNotification from "#/ui/email-templates/payment-failed-notification";
import { getOrbCustomer } from "#/utils/billing/utils";

const invoicePaymentFailedEventSchema = z.object({
  invoice: z.object({
    customer: z.object({
      id: z.string(),
      external_customer_id: z.string(),
    }),
  }),
});

export async function sendPaymentFailedEmail(
  event: InvoicePaymentFailedPayload,
) {
  if (!resend) {
    console.error(
      "Resend is not initialized, cannot send payment failed email",
    );
    return;
  }

  const parsedEvent = invoicePaymentFailedEventSchema.safeParse(event);
  if (!parsedEvent.success) {
    console.error(
      "Invalid event payload for payment failed email:",
      parsedEvent.error,
    );
    return;
  }

  const { invoice } = parsedEvent.data;
  const { customer } = invoice;

  // Fetch customer information from Orb using external_customer_id
  let customerInfo;
  try {
    customerInfo = await getOrbCustomer({
      orgId: customer.external_customer_id,
    });
  } catch (error) {
    console.error(
      `Failed to fetch customer info for external_customer_id: ${customer.external_customer_id}`,
      error,
    );
    return;
  }

  if (!customerInfo.email || !customerInfo.name) {
    console.error(
      `Customer info missing email or name for external_customer_id: ${customer.external_customer_id}`,
      { email: customerInfo.email, name: customerInfo.name },
    );
    return;
  }

  await resend.emails.send(
    {
      from: "Braintrust <<EMAIL>>",
      to: customerInfo.email,
      subject: "Action needed: Payment failed",
      react: React.createElement(
        PaymentFailedNotification,
        {
          organizationName: customerInfo.name,
        },
        null,
      ),
    },
    {
      // Prevents duplicate emails from being sent. Resend will not send the email if the idempotencyKey has been used within the last 24 hours.
      idempotencyKey: `payment-failed-for-${customer.external_customer_id}`,
    },
  );
}
