import { updatePlanId } from "#/app/app/[org]/settings/billing/actions";
import {
  type SubscriptionPlanChangedPayload,
  type SubscriptionStartedPayload,
} from "../types";
import { z } from "zod";

const subscriptionEventSchema = z.object({
  subscription: z.object({
    customer: z.object({
      external_customer_id: z.string(),
    }),
    plan: z.object({
      id: z.string(),
    }),
  }),
});

export async function setPlanId(
  event: SubscriptionStartedPayload | SubscriptionPlanChangedPayload,
) {
  const parsedEvent = subscriptionEventSchema.safeParse(event);

  if (!parsedEvent.success) {
    console.error("Invalid plan update event payload:", parsedEvent.error);
    return;
  }

  const { subscription } = parsedEvent.data;
  const orgId = subscription.customer.external_customer_id;

  if (!orgId) {
    return;
  }

  const planId = subscription.plan.id;

  await updatePlanId({
    orgId,
    planId,
  });
}
