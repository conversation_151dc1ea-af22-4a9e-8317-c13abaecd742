import {
  updateResourceLimits,
  updateResourceLimitsIfHigher,
} from "#/app/app/[org]/settings/billing/actions";
import {
  type SubscriptionPlanChangedPayload,
  type SubscriptionStartedPayload,
} from "../types";
import { z } from "zod";

const limitValueSchema = z
  .union([
    z.coerce.number(),
    z.literal("unlimited"),
    z.literal(""),
    z.undefined(),
    z.null(),
  ])
  .transform((x) => (x === "unlimited" || !x ? undefined : x));

const subscriptionEventSchema = z.object({
  subscription: z.object({
    customer: z.object({
      external_customer_id: z.string(),
    }),
    plan: z.object({
      metadata: z.object({
        "datasets.sanity": limitValueSchema,
        "evals.sanity": limitValueSchema,
        "logs.sanity": limitValueSchema,
        "logs.bytes": limitValueSchema,
      }),
    }),
  }),
});

export async function setResourceLimits({
  event,
  allowLowerLimits,
}: {
  event: SubscriptionStartedPayload | SubscriptionPlanChangedPayload;
  allowLowerLimits: boolean;
}) {
  const parsedEvent = subscriptionEventSchema.safeParse(event);

  if (!parsedEvent.success) {
    console.error("Invalid event payload:", parsedEvent.error);
    return;
  }

  const { subscription } = parsedEvent.data;
  const orgId = subscription.customer.external_customer_id;

  if (!orgId) {
    console.error("No orgId found in event payload");
    return;
  }

  const { metadata } = subscription.plan;

  // If the metadata is empty, we don't need to update the resource limits
  if (!Object.keys(metadata).length) {
    console.info("No metadata found in event payload");
    return;
  }

  const resourceLimits = {
    datasets: metadata["datasets.sanity"],
    experiments: metadata["evals.sanity"],
    logs: metadata["logs.sanity"],
    logsBytes: metadata["logs.bytes"],
  };

  if (allowLowerLimits) {
    await updateResourceLimits({
      orgId,
      resourceLimits,
    });
  } else {
    await updateResourceLimitsIfHigher({
      orgId,
      resourceLimits,
    });
  }
}
