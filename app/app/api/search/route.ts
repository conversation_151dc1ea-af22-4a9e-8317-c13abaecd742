import { algoliasearch } from "algoliasearch";
import { NextResponse } from "next/server";

// Simple Algolia-backed search endpoint compatible with old fumadocs "fetch" search
// Accepts `q` as the search query; returns an array of items containing
// { id, url, title, description, structuredData }.
export async function GET(request: Request): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const query = (searchParams.get("query") ?? "").trim();

    if (!query) {
      return NextResponse.json([]);
    }

    const appId = process.env.ALGOLIA_APP_ID;
    const apiKey = process.env.ALGOLIA_SEARCH_API_KEY;
    const indexName = process.env.ALGOLIA_INDEX_NAME;

    if (!appId || !apiKey || !indexName) {
      return NextResponse.json(
        {
          error:
            "Algolia not configured: missing ALGOLIA_APP_ID, ALGOLIA_SEARCH_API_KEY, or ALGOLIA_INDEX_NAME",
        },
        { status: 500 },
      );
    }

    const client = algoliasearch(appId, apiKey);

    const { results } = await client.search<{
      url: string;
      title: string;
      content: string;
      description: string;
      structuredData: Record<string, unknown>;
    }>({
      requests: [
        {
          indexName,
          query,
          hitsPerPage: 16,
        },
      ],
    });
    if ("hits" in results[0]) {
      return NextResponse.json(results[0].hits ?? []);
    }
    return NextResponse.json([]);
  } catch (error) {
    console.error("Algolia search route error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
