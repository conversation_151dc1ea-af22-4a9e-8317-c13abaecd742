import { NextResponse } from "next/server";
import { getAvailableModels } from "@braintrust/proxy/schema";

const allModels = getAvailableModels();
if (process.env.BRAINTRUST_ENABLE_TEST_MODELS === "true") {
  // This is some random model we added a while ago for testing purposes.
  allModels["ft:gpt-3.5-turbo-0125:braintrust-data::9Rac3rrg"] = {
    format: "openai",
    flavor: "chat",
    multimodal: false,
    input_cost_per_mil_tokens: 0.01,
    output_cost_per_mil_tokens: 0.02,
    displayName: "FakeGPT (Test)",
    max_input_tokens: 4096,
    max_output_tokens: 4096,
    description: "A test model that always returns a fixed response",
  };
}

export async function GET() {
  return NextResponse.json(allModels);
}
