import { NextResponse } from "next/server";
import { copilotLogger, feedbackArgsSchema } from "../util";

export const runtime = "edge";

export async function POST(request: Request): Promise<NextResponse> {
  const args = feedbackArgsSchema.parse(await request.json());
  if (copilotLogger) {
    copilotLogger.logFeedback({
      id: args.spanId,
      scores: {
        accepted: args.accepted ? 1 : 0,
      },
    });
  }

  return NextResponse.json({ ok: true });
}
