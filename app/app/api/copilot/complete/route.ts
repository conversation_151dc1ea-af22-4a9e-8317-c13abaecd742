import { MultiTenantApiURL } from "#/utils/user-types";
import {
  runAutocomplete,
  autoCompleteArgsSchema,
} from "@braintrust/local/copilot";
import { NextResponse } from "next/server";
import { copilotLogger } from "../util";

export const runtime = "edge";

// TODO We should swap in our own auth token here if the user hasn't provided one.
export async function POST(request: Request): Promise<NextResponse> {
  const args = autoCompleteArgsSchema.parse(await request.json());
  const result = await runAutocomplete(
    {
      ...args,
      credentials: {
        ...args.credentials,
        baseUrl: MultiTenantApiURL.includes("api.braintrust.dev")
          ? "https://braintrustproxy.com/v1"
          : args.credentials.baseUrl,
      },
    },
    { parentLogger: copilotLogger },
  );

  return NextResponse.json(result);
}
