import {
  ACCESS_TOKEN_REVALIDATION_WINDOW,
  type AccessTokenResponse,
} from "#/utils/auth/constants";
import { auth } from "@clerk/nextjs/server";
import { type NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  if (req.method !== "GET") {
    return new Response("Method not allowed", { status: 405 });
  }

  const provider = req.nextUrl.searchParams.get("provider");
  if (!provider) {
    return new Response(
      JSON.stringify({ error: "Provider query parameter is required" }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      },
    );
  }

  const { userId, sessionId } = await auth();
  if (!userId) {
    return new Response("Unauthorized", {
      status: 401,
    });
  }

  try {
    const response = await fetch(
      `https://api.clerk.com/v1/users/${encodeURIComponent(
        userId,
      )}/oauth_access_tokens/${encodeURIComponent(provider)}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
        },
        next: {
          // Allow the token to be cached for up to 5 minutes
          revalidate: ACCESS_TOKEN_REVALIDATION_WINDOW,
          tags: [`access-token-${userId}-${provider}-${sessionId}`],
        },
      },
    );
    if (!response.ok) {
      console.error(
        "Failed to retrieve access token",
        response.status,
        response.statusText,
        await response.text(),
      );
      return new Response("Failed to get access token", {
        status: 500,
      });
    }
    const data = await response.json();
    if (data.length === 0) {
      return new Response("No access token found", {
        status: 400,
      });
    } else if (data.length > 1) {
      return new Response(`Multiple (${data.length}) access tokens found`, {
        status: 400,
      });
    }
    const ret: AccessTokenResponse = {
      accessToken: data[0].token,
    };
    return new Response(JSON.stringify(ret), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error(error);
    return new Response("Failed to get access token", {
      status: 500,
    });
  }
}
