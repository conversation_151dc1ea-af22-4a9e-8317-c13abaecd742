import { DocumentSearcher } from "@braintrust/local/doc-index";
import { NextResponse } from "next/server";
import { z } from "zod";
import { HTTPError } from "#/utils/http_error";
import {
  getRequestIdNextRequest,
  httpErrorToNextResponse,
} from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { formatErrorMessageTags } from "@braintrust/local";
import { docsToolResultSchema } from "@braintrust/local/optimization/tools";

// Request schema for vector search
const vectorSearchRequestSchema = z.object({
  query: z.string().min(1, "Query is required"),
  topK: z.number().int().positive().default(10).optional(),
  filters: z
    .object({
      type: z.enum(["docs", "blog"]).optional(),
    })
    .optional(),
  namespace: z.string().default("braintrust-docs").optional(),
});

export async function POST(request: Request): Promise<NextResponse> {
  const extraTags = {
    request_id: getRequestIdNextRequest(request),
  };

  let authLookup:
    | Awaited<ReturnType<typeof getServerSessionAuthLookup>>
    | undefined;

  try {
    // Get auth session
    authLookup = await getServerSessionAuthLookup();
    if (!authLookup) {
      throw new HTTPError(401, "Unauthorized");
    }

    let rawBody;
    // Parse request body
    try {
      rawBody = await request.json();
    } catch (error) {
      return NextResponse.json({ error: "Invalid JSON body" }, { status: 400 });
    }
    const body = vectorSearchRequestSchema.parse(rawBody);

    // Initialize the searcher with environment variables
    const searcher = new DocumentSearcher({
      turbopufferApiKey: process.env.TURBOPUFFER_API_KEY,
      turbopufferRegion: process.env.TURBOPUFFER_REGION,
      openaiApiKey: process.env.OPENAI_API_KEY,
      namespace:
        body.namespace || process.env.DOC_INDEX_NAMESPACE || "braintrust-docs",
    });

    // Perform the search
    const searchResults = await searcher.search(body.query, {
      topK: body.topK,
      filters: body.filters,
    });

    // Transform results to match docsToolResultSchema
    const results = searchResults.map((result) => {
      const attributes = result.attributes;
      const filePath = String(attributes.filePath || "");

      // Convert file path to URL path
      let urlPath = filePath;

      // Remove the absolute path prefix and get relative path
      if (filePath.includes("/app/content/")) {
        urlPath = filePath.split("/app/content/")[1];
      }

      // Remove .mdx extension
      if (urlPath.endsWith(".mdx")) {
        urlPath = urlPath.replace(".mdx", "");
      }

      // Add fragment identifier from the last header (section name)
      const headers = Array.isArray(attributes.headers)
        ? attributes.headers.map(String)
        : [];

      if (headers.length > 0) {
        // Use the last header as the section anchor
        const sectionHeader = headers[headers.length - 1];
        // Convert to URL-friendly fragment (lowercase, replace spaces with hyphens, remove special chars)
        const urlFragment = sectionHeader
          .toLowerCase()
          .replace(/[^\w\s-]/g, "") // Remove special characters except spaces and hyphens
          .replace(/\s+/g, "-") // Replace spaces with hyphens
          .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
          .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens

        if (urlFragment) {
          urlPath = `/${urlPath}#${urlFragment}`;
        } else {
          urlPath = `/${urlPath}`;
        }
      } else {
        urlPath = `/${urlPath}`;
      }

      return {
        id: result.id,
        headers: Array.isArray(attributes.headers)
          ? attributes.headers.map(String)
          : [],
        content: String(attributes.content || ""),
        path: urlPath,
      };
    });

    // Validate that results match the schema
    const validatedResults = docsToolResultSchema.parse(results);

    return NextResponse.json(validatedResults);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request", details: error.errors },
        { status: 400 },
      );
    } else if (error instanceof HTTPError) {
      return httpErrorToNextResponse({ error, authLookup, extraTags });
    } else {
      const tagsStr = formatErrorMessageTags(extraTags);
      console.error(`Internal error${tagsStr}:\n`, error);
      return NextResponse.json(
        { error: `Internal error${tagsStr}` },
        { status: 500 },
      );
    }
  }
}
