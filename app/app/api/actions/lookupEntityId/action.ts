"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { validate as validateUuid } from "uuid";

const lookupEntityIdInputSchema = z.object({
  entityType: z.enum(["project", "experiment", "dataset"]),
  identifier: z.string(),
  projectId: z.string().optional(),
  orgName: z.string().optional(),
});

const lookupEntityIdOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export type LookupEntityIdInput = z.infer<typeof lookupEntityIdInputSchema>;
export type LookupEntityIdOutput = z.infer<typeof lookupEntityIdOutputSchema>;

export async function lookupEntityId(
  input: LookupEntityIdInput,
  authLookupRaw?: AuthLookup,
): Promise<LookupEntityIdOutput | null> {
  const { entityType, identifier, projectId, orgName } =
    lookupEntityIdInputSchema.parse(input);

  // If it's already a UUID, return it directly
  if (validateUuid(identifier)) {
    return { id: identifier, name: identifier };
  }

  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  if (entityType === "project") {
    // For projects, use org_name filter
    if (!orgName) {
      throw new Error("orgName is required for project lookup");
    }

    const { getProjectSummary } = await import("#/app/app/[org]/org-actions");
    const projects = await getProjectSummary({ org_name: orgName }, authLookup);
    const project = projects.find(
      (p) => p.project_name.toLowerCase() === identifier.toLowerCase(),
    );

    if (!project) {
      return null;
    }

    return { id: project.project_id, name: project.project_name };
  }

  // For experiments and datasets, build filters conditionally
  const filters: { name: string; project_id?: string } = {
    name: identifier,
  };
  if (projectId) {
    filters.project_id = projectId;
  }

  const { query, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: entityType,
      aclPermission: "read",
    },
    filters,
    fullResultSetSubsetProjection: new Set(["id", "name"]),
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);

  if (!rows || rows.length === 0) {
    return null;
  }

  return lookupEntityIdOutputSchema.parse(rows[0]);
}
