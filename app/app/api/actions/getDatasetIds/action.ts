"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";

const datasetIdsOutputSchema = z.object({
  id: z.string(),
});

export type DatasetIdsOutput = z.infer<typeof datasetIdsOutputSchema>;

export async function getDatasetIds(
  {
    project_id,
  }: {
    project_id: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<DatasetIdsOutput[] | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "dataset",
      aclPermission: "read",
    },
    filters: {
      project_id,
    },
    fullResultSetSubsetProjection: new Set(["id"]),
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return datasetIdsOutputSchema.array().parse(rows);
}
