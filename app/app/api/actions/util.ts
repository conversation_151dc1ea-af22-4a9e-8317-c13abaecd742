import {
  getRequestIdNextRequest,
  httpErrorToNextResponse,
} from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { NextResponse } from "next/server";
import { z } from "zod";
import { type AuthLookup } from "#/pages/api/_lookup_api_key";
import {
  getAuthorizationToken,
  loginToAuthId,
} from "#/pages/api/_login_to_auth_id";
import { HTTPError } from "#/utils/http_error";
import { type ServerActionRetType } from "#/utils/invoke-server-action";
import { formatErrorMessageTags } from "@braintrust/local";

const requestBodySchema = z.object({
  auth_info: z
    .object({
      org_name: z.string().nullish(),
    })
    .nullish(),
  function_args: z.any(),
});

type RequestBody = z.infer<typeof requestBodySchema>;

async function helper(
  fn: ServerActionFunction,
  body: RequestBody,
  authLookup: AuthLookup,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
): Promise<any> {
  return fn(body.function_args, authLookup);
}

export type ServerActionFunction = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  args: any,
  authLookup?: AuthLookup,
) => Promise<ServerActionRetType>;

export async function invokeServerActionHelper({
  request,
  fn,
}: {
  request: Request;
  fn: ServerActionFunction;
}): Promise<NextResponse> {
  let authLookup: AuthLookup | undefined = undefined;
  try {
    const rawBody = await request.json();
    const body = requestBodySchema.parse(rawBody);
    const authToken = getAuthorizationToken((x) => request.headers.get(x));
    if (authToken) {
      try {
        authLookup = await loginToAuthId({
          token: authToken,
          org_name: body.auth_info?.org_name ?? undefined,
        });
      } catch (e) {
        // Fallback: If Clerk JWT lacks bt_auth_id (race during onboarding),
        // derive auth from server session instead of failing the request.
        if (
          e instanceof HTTPError &&
          e.code === 401 &&
          `${e.message}`.includes("No bt_auth_id in public metadata")
        ) {
          authLookup = await getServerSessionAuthLookup();
        } else {
          throw e;
        }
      }
    } else {
      authLookup = await getServerSessionAuthLookup();
    }
    const res = await helper(fn, body, authLookup);
    return NextResponse.json(res);
  } catch (error) {
    const extraTags = {
      request_id: getRequestIdNextRequest(request),
    };
    if (error instanceof HTTPError) {
      return httpErrorToNextResponse({ error, authLookup, extraTags });
    } else {
      const tagsStr = formatErrorMessageTags(extraTags);
      console.error(`Internal error${tagsStr}:\n`, error);
      return NextResponse.json(
        { error: `Internal error${tagsStr}` },
        { status: 500 },
      );
    }
  }
}
