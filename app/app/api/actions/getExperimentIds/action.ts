"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";

const experimentIdsOutputSchema = z.object({
  id: z.string(),
});

export type ExperimentIdsOutput = z.infer<typeof experimentIdsOutputSchema>;

export async function getExperimentIds(
  {
    project_id,
  }: {
    project_id: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<ExperimentIdsOutput[] | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "experiment",
      aclPermission: "read",
    },
    filters: {
      project_id,
    },
    fullResultSetSubsetProjection: new Set(["id"]),
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return experimentIdsOutputSchema.array().parse(rows);
}
