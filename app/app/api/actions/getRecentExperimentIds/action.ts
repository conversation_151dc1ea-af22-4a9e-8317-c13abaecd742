"use server";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { z } from "zod";

const inputSchema = z.object({
  project_id: z.string(),
  timeFrame: z.object({
    start: z.number(),
    end: z.number().optional(),
  }),
});

export async function getRecentExperimentIds(
  {
    project_id,
    timeFrame,
  }: {
    project_id: string;
    timeFrame: { start: number; end?: number };
  },
  authLookupRaw?: AuthLookup,
): Promise<string[] | null> {
  try {
    inputSchema.parse({ project_id, timeFrame });
  } catch {
    return null;
  }

  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "experiment",
      aclPermission: "read",
    },
    filters: {
      project_id,
    },
    fullResultSetSubsetProjection: new Set(["id", "created"]),
  });

  const startTime = new Date(timeFrame.start).toISOString();
  let endTime: string | undefined = undefined;
  if (timeFrame.end) {
    endTime = new Date(timeFrame.end).toISOString();
  }

  let whereClause = `where created >= ${queryParams.add(startTime)}`;
  if (endTime) {
    whereClause += ` and created <= ${queryParams.add(endTime)}`;
  }

  // Get the 500 most recent experiments
  const query = `
    with full_results as (${fullResultSetQuery})
    select id, created
    from full_results
    ${whereClause}
    order by created desc
    limit 500
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);

  const ids = rows.map((row) => row.id);
  return z.array(z.string()).parse(ids);
}
