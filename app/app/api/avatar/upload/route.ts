import { getServerAuthSession } from "#/utils/auth/server-session";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { put } from "@vercel/blob";
import { NextResponse } from "next/server";

// RBAC_DISCLAIMER: A user can only update their own avatar.
export async function POST(request: Request): Promise<NextResponse> {
  const session = await getServerAuthSession();
  if (!session || !session.loggedIn) {
    throw new Error("Unauthorized");
  }

  const { searchParams } = new URL(request.url);
  const filename = searchParams.get("filename");

  if (!filename) {
    throw new Error("Missing filename");
  }
  if (!request.body) {
    throw new Error("Missing body");
  }

  const blob = await put(filename, request.body, {
    access: "public",
  });

  const supabase = getServiceRoleSupabase();
  await supabase.query(
    `
        UPDATE users
        SET avatar_url = $1
        WHERE auth_id = $2
        `,
    [blob.url, session.authId],
  );

  return NextResponse.json(blob);
}
