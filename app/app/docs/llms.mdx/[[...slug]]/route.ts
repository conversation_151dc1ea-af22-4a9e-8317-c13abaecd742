import * as fs from "node:fs/promises";
import fg from "fast-glob";
import matter from "gray-matter";
import { type NextRequest, NextResponse } from "next/server";
import { processContent } from "../../process-content";

export const revalidate = false;

export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ slug?: string[] }> },
) {

  const { slug } = await params;

  const filePath = `./content/docs/${slug?.join('/')}.mdx`

  const file = await fg(filePath);
  const fileContent = await fs.readFile(file[0]);
  const { content } = matter(fileContent.toString());
  const processed = await processContent(content);

  return new NextResponse(processed);
}
