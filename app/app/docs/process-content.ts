import remarkGfm from "remark-gfm";
import remarkMdx from "remark-mdx";
import remarkParse from "remark-parse";
import remarkStringify from "remark-stringify";
import { unified } from "unified";

export async function processContent(content: string): Promise<string> {
  // Only remove import statements at the top of the file (after frontmatter)
  // Split content by lines
  const lines = content.split(/\r?\n/);
  const processedLines = [];
  let i = 0;

  // Preserve frontmatter if present
  if (lines[0]?.trim() === "---") {
    // Add the opening frontmatter delimiter
    processedLines.push(lines[0]);
    i = 1;

    // Add all frontmatter content until closing delimiter
    while (i < lines.length && lines[i].trim() !== "---") {
      processedLines.push(lines[i]);
      i++;
    }

    // Add the closing frontmatter delimiter if found
    if (i < lines.length) {
      processedLines.push(lines[i]);
      i++;
    }
  }

  // Skip import statements that appear at the top (after frontmatter)
  while (i < lines.length) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // If we find a non-import, non-empty line, we're done with the top imports
    if (!trimmedLine.startsWith("import ") && trimmedLine !== "") {
      break;
    }

    // Skip import lines, but keep empty lines
    if (!trimmedLine.startsWith("import ")) {
      processedLines.push(line);
    }

    i++;
  }

  // Add all remaining content
  while (i < lines.length) {
    processedLines.push(lines[i]);
    i++;
  }

  const contentWithoutTopImports = processedLines.join("\n");

  const file = await unified()
    .use(remarkParse)
    .use(remarkMdx)
    // https://fumadocs.vercel.app/docs/mdx/include
    // .use(remarkInclude)
    // gfm styles
    .use(remarkGfm)
    // to string
    .use(remarkStringify)
    .process(contentWithoutTopImports);

  return String(file);
}
