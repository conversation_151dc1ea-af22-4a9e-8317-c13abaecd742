import * as fs from "node:fs/promises";
import fg from "fast-glob";
import matter from "gray-matter";
import { processContent } from "../process-content";

export const revalidate = false;

export async function GET() {
  // all scanned content
  const files = await fg(["./content/docs/**/*.mdx"]);

  const scan = files.map(async (file) => {
    const fileContent = await fs.readFile(file);
    const { content, data } = matter(fileContent.toString());

    const processed = await processContent(content);
    return `file: ${file}
meta: ${JSON.stringify(data, null, 2)}

${processed}

---`;
  });

  const scanned = await Promise.all(scan);

  return new Response(scanned.join("\n\n"));
}
