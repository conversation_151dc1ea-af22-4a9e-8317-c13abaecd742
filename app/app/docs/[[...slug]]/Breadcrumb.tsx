"use client";

import { useBreadcrumb } from "fumadocs-core/breadcrumb";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Fragment } from "react";
import { useTreeContext } from "fumadocs-ui/provider";

export function Breadcrumb({ full = false }): React.ReactNode {
  const { root } = useTreeContext();
  const pathname = usePathname();
  const items = useBreadcrumb(pathname ?? "", root, {
    includePage: full,
  });

  if (items.length === 0) return <div className="mt-2 h-4" />;

  return (
    <div className="-mb-1 flex flex-row items-center gap-1 pt-2 text-sm text-primary-600">
      {items.map((item, i) => (
        <Fragment key={i}>
          {i !== 0 && <span className="px-1 text-primary-400">/</span>}
          {item.url ? (
            <Link
              href={item.url}
              className="truncate transition-colors hover:text-primary-700"
            >
              {item.name}
            </Link>
          ) : (
            <span className="truncate">{item.name}</span>
          )}
        </Fragment>
      ))}
    </div>
  );
}
