import { createMDXSource } from "fumadocs-mdx";
import { loader } from "fumadocs-core/source";
import { docs, meta } from "#/.source";
import { createOpenAPI } from "fumadocs-openapi/server";
import { cn } from "#/utils/classnames";

const source = loader({
  baseUrl: "/docs",
  source: createMDXSource(docs, meta),
});

// TODO - replace with `disablePlayground` option once we upgrade fumadocs/openapi
const openapi = createOpenAPI({
  renderer: {
    APIPlayground: (props) => (
      <div className="rounded-xl border p-4 font-mono text-primary-800">
        <span
          style={{ fontWeight: 600 }}
          className={cn("text-good-700", {
            "text-yellow-700 dark:text-yellow-300": props.method === "PUT",
            "text-orange-700 dark:text-orange-300": props.method === "PATCH",
            "text-bad-700": props.method === "DELETE",
            "text-accent-700": props.method === "POST",
          })}
        >
          {props.method}
        </span>{" "}
        {props.route}
      </div>
    ),
  },
});

export default source;
export const { getPage, getPages, pageTree } = source;
export { openapi };
