import type { MetadataRoute } from "next";
import { getPages as getLegalPages } from "./(landing)/legal/source";
import { getPages as getDocsPages } from "./docs/source";
import { getPages as getBlogPages } from "./(landing)/blog/source";
import { getPages as getArticlesPages } from "./(landing)/articles/source";

const BASE_URL = "https://www.braintrust.dev";

const url = (path: string): string => new URL(path, BASE_URL).toString();

export default function sitemap(): MetadataRoute.Sitemap {
  const now = new Date();

  // Helper function to determine priority based on page type and path
  const getPriority = (
    path: string,
    type: "page" | "docs" | "blog" | "legal" | "articles",
  ): number => {
    if (path === "/") return 1.0;

    // High priority pages
    if (["pricing", "contact", "docs"].includes(path.split("/")[1])) return 0.9;

    // Documentation priorities
    if (type === "docs") {
      if (path.includes("/start")) return 0.9; // Getting started
      if (path.includes("/guides")) return 0.8; // Guides
      if (path.includes("/reference")) return 0.7; // Reference
      return 0.6;
    }

    // Blog priorities
    if (type === "blog") return 0.7;
    if (type === "articles") return 0.6;

    // Legal and other pages
    if (type === "legal") return 0.4;

    return 0.5;
  };

  const getChangeFrequency = (
    type: "page" | "docs" | "blog" | "legal" | "articles",
  ):
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never" => {
    switch (type) {
      case "docs":
        return "weekly";
      case "blog":
        return "monthly";
      case "legal":
        return "yearly";
      case "articles":
        return "monthly";
      default:
        return "monthly";
    }
  };

  return [
    // Main landing page
    {
      url: url("/"),
      changeFrequency: "weekly",
      priority: 1.0,
      lastModified: now,
    },

    // Key landing pages
    {
      url: url("/pricing"),
      changeFrequency: getChangeFrequency("page"),
      priority: getPriority("/pricing", "page"),
      lastModified: now,
    },
    {
      url: url("/contact"),
      changeFrequency: getChangeFrequency("page"),
      priority: getPriority("/contact", "page"),
      lastModified: now,
    },

    // Legal pages
    {
      url: url("/legal"),
      changeFrequency: getChangeFrequency("legal"),
      priority: getPriority("/legal", "legal"),
    },
    ...getLegalPages().map<MetadataRoute.Sitemap[number]>((p) => ({
      url: url(p.url),
      changeFrequency: getChangeFrequency("legal"),
      lastModified: p.data.lastModified
        ? new Date(p.data.lastModified)
        : undefined,
      priority: getPriority(p.url, "legal"),
    })),

    // Careers
    {
      url: url("/careers"),
      changeFrequency: "monthly",
      priority: 0.6,
    },

    // Documentation
    {
      url: url("/docs"),
      changeFrequency: getChangeFrequency("docs"),
      priority: getPriority("/docs", "docs"),
      lastModified: now,
    },
    ...getDocsPages().map<MetadataRoute.Sitemap[number]>((p) => ({
      url: url(p.url),
      changeFrequency: getChangeFrequency("docs"),
      lastModified: p.data.lastModified
        ? new Date(p.data.lastModified)
        : undefined,
      priority: getPriority(p.url, "docs"),
    })),

    // Blog
    {
      url: url("/blog"),
      changeFrequency: getChangeFrequency("blog"),
      priority: getPriority("/blog", "blog"),
      lastModified: now,
    },
    ...getBlogPages()
      .filter((page) => !page.data.draft)
      .map<MetadataRoute.Sitemap[number]>((p) => ({
        url: url(p.url),
        changeFrequency: getChangeFrequency("blog"),
        lastModified: p.data.lastModified
          ? new Date(p.data.lastModified)
          : p.data.date
            ? new Date(p.data.date)
            : undefined,
        priority: getPriority(p.url, "blog"),
      })),

    // Articles
    {
      url: url("/articles"),
      changeFrequency: getChangeFrequency("articles"),
      priority: getPriority("/articles", "articles"),
      lastModified: now,
    },
    ...getArticlesPages()
      .filter((page) => !page.data.draft)
      .map<MetadataRoute.Sitemap[number]>((p) => ({
        url: url(p.url),
        changeFrequency: getChangeFrequency("articles"),
        lastModified: p.data.lastModified
          ? new Date(p.data.lastModified)
          : p.data.date
            ? new Date(p.data.date)
            : undefined,
        priority: getPriority(p.url, "articles"),
      })),
  ];
}
