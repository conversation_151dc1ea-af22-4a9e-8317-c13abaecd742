"use client";

import { But<PERSON> } from "#/ui/button";
import { Play, Plus } from "lucide-react";
import { cn } from "#/utils/classnames";
import { DiffModeSwitch } from "#/ui/diff-mode-switch";
import { useDiffModeState } from "#/ui/query-parameters";
import { OptimizationChat } from "#/ui/optimization/optimization-chat";

export function LoggedOutPlaygroundHeader({
  openUpsellDialog,
}: {
  openUpsellDialog: VoidFunction;
}) {
  const [diffMode, setDiffMode] = useDiffModeState();
  return (
    <div
      className={cn(
        "-mx-3 flex flex-none items-center gap-2 border-b border-primary-200/80 bg-primary-50 px-3 pt-1 pb-2",
      )}
    >
      <h1 className="flex-none text-base font-semibold">Playground</h1>
      <div className="flex flex-none grow items-center justify-end gap-2">
        <DiffModeSwitch
          diffModeState={diffMode}
          diffModeOptions={["between_experiments", "expected_output"]}
          setDiffMode={setDiffMode}
        />
        <Button
          size="xs"
          variant="ghost"
          onClick={openUpsellDialog}
          Icon={Plus}
        >
          Experiments
        </Button>
        <OptimizationChat />
        <Button
          size="xs"
          variant="border"
          onClick={openUpsellDialog}
          Icon={Play}
        >
          Run
        </Button>
      </div>
    </div>
  );
}
