"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { ListVideo } from "lucide-react";
import { SpanContents } from "#/ui/trace/span-contents";
import { type DataDisplayedField } from "#/ui/trace/data-display";
import { PlaygroundRowSheet } from "../app/[org]/p/[project]/playgrounds/[playground]/playground-row-sheet";
import { useContext } from "react";
import { ProjectContext } from "../app/[org]/p/[project]/projectContext";
import { useTraceCopilotContext } from "#/ui/copilot/trace";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { type UpdateRowFn } from "#/utils/mutable-object";
import { type Span, type LoadedTrace } from "#/ui/trace/graph";

interface LoggedOutPlaygroundTraceSheetProps {
  isOpen: boolean;
  onClose: VoidFunction;
  datasetData: {
    id: string;
    input: string | null;
    expected: unknown;
    metadata: unknown;
  };
  onDatasetUpdate: (field: string, value: unknown) => void;
  onAddRow: VoidFunction;
  onPrevRow: VoidFunction;
  onNextRow: VoidFunction;
  hasPrevRow: boolean;
  hasNextRow: boolean;
  headerText: string;
  onRunRow: VoidFunction;
}

const DATASET_EDITABLE_FIELDS: DataDisplayedField[] = [
  "input",
  "expected",
  "metadata",
];

const DATASET_HIDDEN_FIELDS: "metrics"[] = ["metrics"];

const ON_APPLY_SEARCH: ApplySearch = async () => {};

export function LoggedOutPlaygroundTraceSheet({
  isOpen,
  onClose,
  datasetData,
  onDatasetUpdate,
  onAddRow,
  onPrevRow,
  onNextRow,
  hasPrevRow,
  hasNextRow,
  headerText,
  onRunRow,
}: LoggedOutPlaygroundTraceSheetProps) {
  const { config: projectConfig } = useContext(ProjectContext);
  const copilotContext = useTraceCopilotContext({
    objectType: "playground_logs",
    objectName: "Logged Out Playground",
  });

  const mockSpan = useMemo(
    (): Span => ({
      id: datasetData.id,
      span_id: datasetData.id,
      root_span_id: datasetData.id,
      data: {
        id: datasetData.id,
        span_id: datasetData.id,
        _xact_id: "0",
        input: datasetData.input,
        expected: datasetData.expected,
        metadata: datasetData.metadata,
        span_attributes: { name: "dataset" },
        scores: {},
      },
      scores: {},
      parent_span_id: null,
      children: [],
    }),
    [datasetData],
  );

  const mockTrace = useMemo(
    (): LoadedTrace => ({
      root: mockSpan,
      spans: { [mockSpan.id]: mockSpan },
    }),
    [mockSpan],
  );

  const updateRow: UpdateRowFn = useCallback(
    async (row, path, newValue) => {
      const field = path[0];
      if (typeof field === "string") {
        onDatasetUpdate(field, newValue);
      }
      return null;
    },
    [onDatasetUpdate],
  );

  const originDatasetContent = useMemo(() => {
    return (
      <>
        <div className="flex justify-between">
          <div className="flex h-7 items-center text-xs">Edit dataset row</div>
        </div>
        <SpanContents
          key={mockSpan.id}
          isDatasetRow
          isRoot
          span={mockSpan}
          trace={mockTrace}
          projectConfig={projectConfig}
          onApplySearch={ON_APPLY_SEARCH}
          copilotContext={copilotContext}
          comparisonClassName=""
          resultIndex={null}
          searchResultFields={[]}
          searchQuery=""
          objectType="dataset"
          editableFields={DATASET_EDITABLE_FIELDS}
          hiddenFields={DATASET_HIDDEN_FIELDS}
          updateRow={updateRow}
        />
      </>
    );
  }, [mockSpan, mockTrace, projectConfig, copilotContext, updateRow]);

  return (
    <PlaygroundRowSheet
      headerText={headerText}
      isOpen={isOpen}
      onClose={onClose}
      onRunRow={onRunRow}
      onAddRow={onAddRow}
      onPrevRow={onPrevRow}
      onNextRow={onNextRow}
      hasPrevRow={hasPrevRow}
      hasNextRow={hasNextRow}
      isReadOnly={false}
      isRunning={false}
      hasDataset={false}
    >
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        <ResizablePanel
          order={0}
          className="relative flex"
          id="dataset"
          minSize={30}
          defaultSize={30}
        >
          <div className="flex flex-1 flex-col gap-4 overflow-auto px-4 py-3">
            {originDatasetContent}
          </div>
        </ResizablePanel>
        <ResizableHandle className="bg-primary-200/70" />
        <ResizablePanel
          order={1}
          className="relative flex flex-col"
          id="tree"
          minSize={30}
          defaultSize={70}
        >
          <div className="flex flex-1 items-center justify-center">
            <TableEmptyState
              className="size-full justify-center"
              Icon={ListVideo}
              label="This row has not been run yet"
            />
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </PlaygroundRowSheet>
  );
}
