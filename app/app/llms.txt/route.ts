import * as fs from "node:fs/promises";
import { join } from "node:path";

export const revalidate = false;

export async function GET() {
  try {
    // Read the root llms.txt file (go up one directory from app)
    const llmsTxtPath = join(process.cwd(), "..", "llms.txt");
    const content = await fs.readFile(llmsTxtPath, "utf-8");

    return new Response(content, {
      headers: {
        "Content-Type": "text/plain",
      },
    });
  } catch (error) {
    return new Response("llms.txt not found", { status: 404 });
  }
}
