import "../docs/docs-style.css";
import { Root<PERSON>rovider as FumadocsProvider } from "fumadocs-ui/provider";
import { DocsBody } from "fumadocs-ui/page";
import { notFound } from "next/navigation";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { getPage } from "../docs/source";
import defaultMdxComponents from "fumadocs-ui/mdx";

// This route is intended to be rendered in an iframe for the changelog dialog
export default async function ChangelogContentPage() {
  const page = getPage(["changelog"]);

  if (page == null) {
    notFound();
  }

  return (
    <FumadocsProvider
      theme={{
        defaultTheme: "system",
        enableSystem: true,
        attribute: "class",
        disableTransitionOnChange: true,
      }}
    >
      {/* eslint-disable-next-line better-tailwindcss/no-unregistered-classes */}
      <div className="docs-content flex min-w-0 flex-1">
        <DocsBody className={cn(proseMDXBaseClassName, "p-5")}>
          <page.data.body components={defaultMdxComponents} />
        </DocsBody>
      </div>
    </FumadocsProvider>
  );
}
