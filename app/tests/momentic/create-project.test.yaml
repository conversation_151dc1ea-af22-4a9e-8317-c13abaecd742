fileType: momentic/test
id: d8240ca5-984f-45d5-8f47-e84f5facfc64
name: create-project
baseUrl: ""
description: null
schemaVersion: 1.0.19
advanced:
  viewport:
    width: 1280
    height: 800
  disableAICaching: false
  browserType: Chromium
retries: 1
envs:
  - name: ci
    default: true
disabled: true
labels: []
steps:
  - type: MODULE
    moduleId: 2cca190e-ca8a-4b37-bff8-47e5588091c9
    id: 0cab92df-a7b6-4d6a-b10d-8ef3f6462c0c
  - id: bc8c0228-946a-46bf-89c9-e3148dae4cb9
    type: PRESET_ACTION
    command:
      id: 14e6fdf7-c857-424b-8808-042d72db9003
      type: CLICK
      target:
        type: description
        elementDescriptor: organization dropdown
  - id: 7de669ce-6aee-4597-98fe-c23c0aa39cbc
    type: PRESET_ACTION
    command:
      id: 72f86b44-5ba3-45c5-a16d-f056c6fb34d2
      type: CLICK
      target:
        type: description
        elementDescriptor: create project
  - id: 6a0560f7-6a46-4411-9180-fc22091c4aa7
    envKey: PROJ_NAME
    type: PRESET_ACTION
    command:
      id: 906363a8-4c00-4fa1-bc89-449719be7f74
      code: return `test-${new Date().toISOString()}-${faker.string.alpha(10)}`
      type: JAVASCRIPT
  - id: ad1d38e2-5370-4335-826c-aa1a01ee5330
    type: PRESET_ACTION
    command:
      id: dc536d58-6a98-4fc7-856c-9b2fe239211d
      clearContent: true
      type: TYPE
      value: "{{env.PROJ_NAME}}"
  - id: 63d2d33a-4b05-4ae9-b841-ee05a2599c60
    type: PRESET_ACTION
    command:
      id: c47ef11f-ad2d-4b34-a490-c6cd2983a3a4
      type: CLICK
      target:
        type: description
        elementDescriptor: create button
  - id: 1d7ed7c9-5f14-4a69-adfb-f8993fd1995a
    type: PRESET_ACTION
    command:
      id: 217a6f8a-9870-48b7-975a-ac71f434c02c
      type: AI_ASSERTION
      assertion: Verify '{{env.PROJ_NAME}}' is visible on the page
