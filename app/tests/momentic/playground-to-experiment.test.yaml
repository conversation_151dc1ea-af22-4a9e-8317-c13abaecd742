fileType: momentic/test
id: b850c3eb-2a68-4063-a9b0-bc3698da1841
name: playground-to-experiment
description: ""
schemaVersion: 1.0.19
advanced:
  viewport:
    width: 1280
    height: 800
  disableAICaching: false
  browserType: Chromium
retries: 2
envs:
  - name: ci
    default: true
steps:
  - type: MODULE
    moduleId: 2cca190e-ca8a-4b37-bff8-47e5588091c9
    id: cf0b131d-9862-4a97-9275-97a23d7abafb
  - id: 106aa166-e33f-486d-b2e8-75c07f5163e1
    type: PRESET_ACTION
    command:
      id: 458d76d0-eae2-4a7c-ba02-f24a7e212e20
      type: NAVIGATE
      url: /app/braintrustdata.com/settings/secrets
  - id: 92262bec-505f-4333-be58-64583e1f3b6f
    type: PRESET_ACTION
    command:
      id: fe72ff60-62f3-4ca3-935d-98a0f3049090
      type: CLICK
      target:
        type: description
        elementDescriptor: OPENAI_API_KEY
  - id: f3999040-f9b4-4a79-afdf-daad4edd2d83
    type: PRESET_ACTION
    command:
      id: 1b1a0eb9-2262-4291-a78a-0c1cb959d54b
      clearContent: true
      type: TYPE
      target:
        type: description
        elementDescriptor: API key input
      value: "{{env.OPENAI_API_KEY}}"
  - id: f698b823-ef48-4ee2-983f-f8e46b6acda7
    type: PRESET_ACTION
    command:
      id: 96cb663f-7599-4046-a044-67a90be73c24
      type: CLICK
      target:
        type: description
        elementDescriptor: Save button
  - id: bb15aabb-7091-4d58-bcb9-50351cd2b334
    type: PRESET_ACTION
    command:
      id: 509112f2-726f-4489-bb45-c3c5017d0db1
      loadTimeout: 60
      type: NAVIGATE
      url: /app/braintrustdata.com
  - id: fc2711f0-f0b6-4800-8078-d4f1dfc9af8c
    type: AI_ACTION_DYNAMIC
    text: click the first project row name. if there are no projects, create one
      with a random name.
  - id: 37f8b783-cdc6-47bb-b25e-82b3a5ad9396
    type: PRESET_ACTION
    command:
      id: bcc69225-2537-4413-b9f0-e606575b5cef
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Evaluations" in the nav header'
  - id: c5a374d6-0955-47cd-a1a7-e205c36918ac
    type: PRESET_ACTION
    command:
      id: 3bf2b55d-fa83-41de-8d2b-aee7848d5264
      type: CLICK
      target:
        type: description
        elementDescriptor: Create playground button
  - id: 0f434f37-8bf9-4dcb-82fe-b53b786e9c7f
    type: PRESET_ACTION
    command:
      id: c382ae7a-888f-4b1f-bd80-a38538c4a36d
      clearContent: true
      pressEnter: true
      type: TYPE
      target:
        type: description
        elementDescriptor: name input
      value: Playground to experiment
  - id: ad1f4ecf-ca23-420b-9fff-6392ca59985b
    type: PRESET_ACTION
    command:
      id: 6178e58a-9e5c-4a0f-914d-e4c60e5df26b
      type: PAGE_CHECK
      assertion:
        type: CONTENT
        value: Tasks
      timeout: 300
  - id: 815da691-2a34-4749-8fb6-64dd8794eacb
    type: PRESET_ACTION
    command:
      id: 35a996cb-f27b-42a1-8423-8c48e7b70260
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Select a dataset"'
  - id: b10f3a48-2457-4d6c-890e-eb6fa0b9e85b
    type: PRESET_ACTION
    command:
      id: 6e08cafe-0104-4aac-8366-09d62a670057
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Upload dataset"'
  - id: 46ed4bcd-f9e6-41c9-b351-9ccc51ee941d
    type: PRESET_ACTION
    command:
      id: 60f75997-85ea-484f-bcd7-af8bc8732f72
      type: FILE_UPLOAD
      fileSource:
        type: URL
        url: https://gist.githubusercontent.com/edenh/4cf3d14f1fd312cad8f9d0f42b245622/raw/20a2fdfe6e96fbd0ee8dd7b77aa504b86d3feb7d/movies.csv
      filename: movies.csv
  - id: 4069099e-04aa-40ec-bc5a-d96cdd8b265a
    type: PRESET_ACTION
    command:
      id: 97baf4f0-d0d9-49d0-b591-9fcbc7463ff4
      type: PRESS
      value: Enter
  - id: 01f482da-a635-443a-9ba5-961010caaf63
    type: PRESET_ACTION
    command:
      id: 80bf174d-c2f2-46b8-92de-0671917cdbe1
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Import"'
  - id: 9c6cc0aa-ac21-4c5e-b994-fd0de9be92bc
    type: PRESET_ACTION
    command:
      id: 62ca17c2-34fd-4bbf-bd93-d2301a0b5740
      type: PAGE_CHECK
      assertion:
        type: CONTENT
        value: Successfully imported 150 rows
      timeout: 300
  - id: d4278cc1-5009-48a9-8fbc-7022bb1a75b1
    type: PRESET_ACTION
    command:
      id: dbb78bac-204e-46c5-99cf-a563270122c2
      type: CLICK
      target:
        type: description
        elementDescriptor: the first task in the sidebar
  - id: 0f268d57-3557-4541-ada2-829a541fb659
    type: PRESET_ACTION
    command:
      id: d205c085-9ff6-499c-93fc-b8516b7c72f7
      useSelector: false
      type: CLICK
      target:
        type: description
        elementDescriptor: In the tasks dialog, the model dropdown
  - id: 54acdcf4-8615-4f0d-8d84-a0bb24f27466
    type: PRESET_ACTION
    command:
      id: 6128d357-f26c-48a3-ac6d-c5d651d74ce8
      type: CLICK
      target:
        type: description
        elementDescriptor: OpenAI
  - id: ea57f80f-7d3d-4830-b243-65ff7330bc1e
    type: PRESET_ACTION
    command:
      id: a95174a5-9e93-4cf4-bb50-3ba9a9d84400
      type: CLICK
      target:
        type: description
        elementDescriptor: GPT-4o mini
  - id: 6042dd9d-296a-46ae-b1d6-17d2a0f518dd
    type: PRESET_ACTION
    command:
      id: 1b2472ad-1227-4ee2-8e24-b38b3d00eec7
      clearContent: true
      type: TYPE
      target:
        type: description
        elementDescriptor: System input
      value: summarize {{ "\{\{input\}\}" }}
  - id: 7731e7b5-7306-4c7f-8c77-f916bc92c619
    type: PRESET_ACTION
    command:
      id: e543a8ce-027f-48e0-95f6-c8c0fd3c4d17
      type: CLICK
      target:
        type: description
        elementDescriptor: Save prompt
  - id: 0ba7d244-c01e-43c8-956f-54d9911613ad
    type: PRESET_ACTION
    command:
      id: 91b73628-0b2a-4b4a-88c0-f1d773af05ef
      clearContent: true
      pressEnter: false
      type: TYPE
      target:
        type: description
        elementDescriptor: name input
      value: my prompt
  - id: 4b04964f-aa6f-4958-9f5b-d4d9fee47185
    type: PRESET_ACTION
    command:
      id: 454bad90-7a81-4bb5-9831-66a5d4410f22
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Save" button'
  - id: e8497c8b-f22d-4229-b78f-57b20860e25b
    type: PRESET_ACTION
    command:
      id: ca68435a-78d4-40f9-9879-ed8b2a23aa3c
      type: PRESS
      value: Escape
  - id: 80bde7ea-b51f-4144-900c-cc4d94929ebb
    type: PRESET_ACTION
    command:
      id: 9846b068-fcb8-41bc-97ac-a2af03d527e2
      type: CLICK
      target:
        type: description
        elementDescriptor: + Scorer
  - id: cf2b70ca-d7b4-40ed-96ca-6db8db0f6e16
    type: PRESET_ACTION
    command:
      id: b5073e13-4617-4ff6-b82a-aaf8adac45c3
      type: CLICK
      target:
        type: description
        elementDescriptor: AutoEvals
  - id: 76b7fb81-ba8c-4909-b119-13e5db788583
    type: PRESET_ACTION
    command:
      id: ef2dcf57-5c02-4810-bed5-73601b67c631
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Security" under LLM-as-a-Judge'
  - id: f8aff72c-353a-4670-984e-7e0dd70d9a57
    type: PRESET_ACTION
    command:
      id: 7f15919b-935c-4028-9c08-9c96da150819
      type: PRESS
      value: Escape
  - id: ac961888-ef48-4ec9-8586-d54daf83d6c3
    type: PRESET_ACTION
    command:
      id: ece003f6-517d-4dfb-90a3-f36bceaa12a6
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Run" button'
  - id: 3075d706-6f1f-41eb-b508-f02d277c0e9e
    type: PRESET_ACTION
    command:
      id: eac99293-7456-45ea-b5b9-29d1808de819
      type: PAGE_CHECK
      assertion:
        type: CONTENT
        value: 100.00%
      timeout: 300
  - id: 462ee84c-a9b2-42af-922e-91ba4907aa99
    type: PRESET_ACTION
    command:
      id: 1ace37e2-c9fe-4e60-8590-96553ebd7738
      type: CLICK
      target:
        type: description
        elementDescriptor: + Experiment
  - id: daf5044c-9093-4254-a206-ad0e52369429
    type: PRESET_ACTION
    command:
      id: 5d4ed210-b8d1-434f-ae7d-26d0f2614601
      type: ELEMENT_CHECK
      target:
        type: description
        elementDescriptor: The experiment name is "my prompt"
      assertion:
        type: ELEMENT_EXISTENCE
        condition: EXISTS
  - id: 1d11954a-0b38-4caa-a4da-98e2677031b6
    type: PRESET_ACTION
    command:
      id: d24bbd36-e138-459f-ac7d-1da13b33ff67
      type: CLICK
      target:
        type: description
        elementDescriptor: '"Create" button'
  - id: eeb7a92a-b3bd-4579-ab1c-71345c9f82aa
    type: AI_ACTION_DYNAMIC
    text: Experiment was successfully created
