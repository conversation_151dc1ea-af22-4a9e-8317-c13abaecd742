import { test as setup } from "@playwright/test";

const authFile = ".auth/user.json";

setup("authenticate", async ({ page }) => {
  // Perform authentication steps. Replace these actions with your own.
  await page.goto("http://localhost:3000/app");
  const apiKeyLogin = await page.getByText("Login with your API Key");
  if (apiKeyLogin) {
    await page.waitForURL("http://localhost:3000/auth/signin**");
    await page.getByLabel("API Key").fill(process.env.BRAINTRUST_API_KEY ?? "");
    await page.getByRole("button", { name: "Sign in" }).click();
  } else {
    await page.locator("input[name=username] >> visible=true").fill("ankur");
    await page
      .locator("input[name=password] >> visible=true")
      .fill("ASDFasdf1!");
    await page
      .locator("input[name=signInSubmitButton] >> visible=true")
      .click();
  }
  // Wait until the page receives the cookies.
  //
  // Sometimes login flow sets cookies in the process of several redirects.
  // Wait for the final URL to ensure that the cookies are actually set.
  await page.waitForURL("http://localhost:3000/app/braintrustdata.com");

  await page.context().storageState({ path: authFile });
});
