import { test, expect } from "@playwright/test";
import { <PERSON><PERSON> } from "braintrust";
import { LevenshteinScorer } from "autoevals";

const testName = `playwright-update_scores-${Date.now()}`;
const rowInput = "PLAYWRIGHT_TEST_INPUT";
const scoreName = "HUMAN_SCORE";

test("add human score", async ({ browser }) => {
  await Eval(testName, {
    data: async () => {
      return [
        {
          input: rowInput,
          expected: "a",
        },
      ];
    },
    task: () => {
      return "foo";
    },
    scores: [LevenshteinScorer],
    experimentName: testName,
    metadata: {
      foo: "baz",
    },
    update: true,
  });

  // Create two isolated browser contexts
  const experimentContext = await browser.newContext();
  const configContext = await browser.newContext();

  // Create pages and interact with contexts independently
  const configPage = await configContext.newPage();
  const experimentPage = await experimentContext.newPage();

  experimentPage.on("console", (msg) => {
    if (msg.type() === "error") {
      console.log(`Error text: "${msg.text()}"`);
      if (
        // when we set offline, some calls may return this, so ignore it as a fatal error
        msg.text() === "Failed to load resource: net::ERR_INTERNET_DISCONNECTED"
      ) {
        return;
      }
      throw new Error(msg.text());
    }
  });

  await experimentPage.goto(
    `http://localhost:3000/app/braintrustdata.com/p/${testName}/experiments/${testName}`,
  );

  await configPage.goto(
    `http://localhost:3000/app/braintrustdata.com/p/${testName}/configuration`,
  );
  await configPage.getByRole("button", { name: /add human review/i }).click();
  await configPage.getByLabel(/Continuous score/i).click();
  await configPage.getByPlaceholder("Score name").fill(scoreName);
  await configPage.getByRole("button", { name: /create/i }).click();

  await experimentPage.getByText(rowInput).click();
  await expect(
    experimentPage.locator("#trace").getByText("Levenshtein", { exact: true }),
  ).toBeVisible();

  // to trigger a revalidation, we disconnect/reconnect the network connection
  // we don't have the ability to blur/focus the window yet
  // https://github.com/microsoft/playwright/issues/3570
  await experimentContext.setOffline(true);
  // let the offline call trigger
  await new Promise((res) => setTimeout(res, 2000));
  await experimentContext.setOffline(false);

  await expect(
    experimentPage.locator("#trace").getByText(scoreName),
  ).toBeVisible({ timeout: 10000 });

  await experimentPage.reload();
  await expect(
    experimentPage.locator("#trace").getByText(scoreName),
  ).toBeVisible();

  //await experimentPage.waitForTimeout(60000);
});
