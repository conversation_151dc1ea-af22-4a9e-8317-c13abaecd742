import { test, expect } from "@playwright/test";
import { <PERSON><PERSON> } from "braintrust";
import { LevenshteinScorer } from "autoevals";

const testName = `playwright-update_scores-${Date.now()}`;
const scoreName = "NEW_EVAL_SCORE";

function customScorer() {
  return { name: scoreName, score: 0 };
}

test("eval new score", async ({ page }) => {
  await Eval(testName, {
    data: async () => {
      return [
        {
          input: "1",
          expected: "a",
        },
      ];
    },
    task: () => {
      return "foo";
    },
    scores: [LevenshteinScorer],
    experimentName: testName,
    metadata: {
      foo: "baz",
    },
    update: true,
  });

  page.on("console", (msg) => {
    if (msg.type() === "error") {
      console.log(`Error text: "${msg.text()}"`);
      throw new Error(msg.text());
    }
  });

  await page.goto(
    `http://localhost:3000/app/braintrustdata.com/p/${testName}/experiments/${testName}`,
  );

  await page.getByText("Levenshtein").first().waitFor();

  await Eval(testName, {
    data: async () => {
      return [
        {
          input: "2",
          expected: "a",
        },
      ];
    },
    task: () => {
      return "foo";
    },
    scores: [LevenshteinScorer, customScorer],
    experimentName: testName,
    metadata: {
      foo: "baz",
    },
    update: true,
  });

  await page.getByText("Levenshtein").first().waitFor();
  await expect(page.locator("#summary").getByText(scoreName)).toBeVisible();
  await expect(page.locator("#main").getByText(scoreName)).toBeVisible();

  await page.reload();
  await expect(page.locator("#summary").getByText(scoreName)).toBeVisible();
  await expect(page.locator("#main").getByText(scoreName)).toBeVisible();

  //await page.waitForTimeout(60000);
});
