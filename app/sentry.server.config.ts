import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,

  beforeSend: (event) => {
    // We want to be extremely careful about sending server-side errors to Sentry.
    // To avoid logging customer data accidentally, the `isSafeForServerSide` flag
    // must be set to true.
    const isSafe = event.extra?.isSafeForServerSide;

    if (isSafe === true) {
      return event;
    }

    return null;
  },
});
