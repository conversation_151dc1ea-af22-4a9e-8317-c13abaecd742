import { useContext, useMemo } from "react";
import { atom, useAtomValue } from "jotai";

import { featuresAtom } from "./feature-flag-atoms";
import { selectAtom } from "jotai/utils";
import {
  featureFlagConfig,
  type FeatureFlags,
  type BTQLFeatureFlags,
  FAST_OBJECT_TYPES,
  ORGS_WITHOUT_ADVANCED_METRICS,
  ORGS_WITHOUT_PROJECT_SUMMARY,
  ORGS_WITHOUT_ENVIRONMENTS,
} from "./feature-flag-config";
import { FeatureFlagsContext } from "./feature-flags-provider";
import { datadogEvalFlag } from "#/ui/datadog-rum-provider";

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const MinVersion = Object.fromEntries(
  Object.entries(featureFlagConfig).map(([key, def]) => [key, def.minVersion]),
) as Record<keyof FeatureFlags, string>;

export function useFeatureFlags(): {
  flags: FeatureFlags;
  forcedFlags: Record<string, boolean>;
  setFlag: (flag: keyof FeatureFlags, enabled: boolean) => void;
  isLoading: boolean;
} {
  const flagsCtx = useContext(FeatureFlagsContext);

  // throw annoying error if dev and hook outside of provider
  // if used in production outside, flag values will be featureFlagConfig defaults
  if (process.env.NODE_ENV === "development" && !flagsCtx.isInsideProvider) {
    throw new Error(
      "useFeatureFlags hook must be used inside FeatureFlagProvider",
    );
  }

  return flagsCtx;
}

/** Memoized featured flag check, will only rerender when value changes */
export const useIsFeatureEnabled = (flagKey: keyof FeatureFlags) => {
  // This derived atom is read-only because we only provide a 'read' function.
  const featureValueAtom = useMemo(
    () => atom((get) => get(featuresAtom)[flagKey] ?? false),
    [flagKey], // The atom is stable for a given key
  );

  // useAtomValue returns only the value and subscribes the component to it.
  const flagValue = useAtomValue(featureValueAtom);

  // instrument flag read
  datadogEvalFlag(flagKey, flagValue);

  return flagValue;
};

// instead of passing down the entire flags object for btql, use what's needed
export const useBtqlFlags = () => {
  const btqlFlagsValueAtom = useMemo(
    () =>
      selectAtom(
        featuresAtom,
        (allFeatures): BTQLFeatureFlags => ({
          brainstore: allFeatures["brainstore"] ?? false,
          brainstore_realtime: allFeatures["brainstore_realtime"] ?? false,
          brainstore_skip_backfill_check:
            allFeatures["brainstore_skip_backfill_check"] ?? false,
          queryDiagnostics: allFeatures["queryDiagnostics"] ?? false,
          customColumnsV2: allFeatures["customColumnsV2"] ?? false,
        }),
        // equality function for comparison
        (a, b) => JSON.stringify(a) === JSON.stringify(b),
      ),
    [],
  );

  return useAtomValue(btqlFlagsValueAtom);
};

export {
  featureFlagConfig,
  FeatureFlags,
  ORGS_WITHOUT_PROJECT_SUMMARY,
  ORGS_WITHOUT_ADVANCED_METRICS,
  ORGS_WITHOUT_ENVIRONMENTS,
  FAST_OBJECT_TYPES,
};
