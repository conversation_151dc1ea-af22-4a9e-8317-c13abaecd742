// These are the metrics that are baked into the Braintrust proxy and SDK.
// These metrics are not billed as custom metrics.
// Make sure to update the equivalent metrics in api-ts/src/telemetry/constants.ts
export const STANDARD_METRICS = new Set([
  "cached",
  "cache_creation_input_tokens",
  "cache_read_input_tokens",
  "caller_filename",
  "caller_functionname",
  "caller_lineno",
  "completion_accepted_prediction_tokens",
  "completion_audio_tokens",
  "completion_reasoning_tokens",
  "completion_rejected_prediction_tokens",
  "completion_tokens",
  "duration",
  "end",
  "estimated_cost",
  "input_tokens",
  "latency",
  "latency_ms",
  "llm_duration",
  "ms_to_finish",
  "ms_to_first_chunk",
  "offset",
  "output_tokens",
  "prompt_audio_tokens",
  "prompt_cached_tokens",
  "prompt_cache_creation_tokens",
  "prompt_tokens",
  "retries",
  "start",
  "time_to_first_token",
  "tokens",
  "tool_calls",
  "tool_count",
  "total_cost",
  "total_tokens",
]);
