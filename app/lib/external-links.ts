import { type Pluggable } from "unified";
import { visit } from "unist-util-visit";

export const externalLinks: Pluggable = [
  rehypeExternalLinks,
  {
    target: "_blank",
    rel: ["nofollow", "noopener", "noreferrer"],
  },
] as const;

const isBraintrustUrl = (url: string) => {
  try {
    return new URL(url, "https://www.braintrust.dev").hostname.includes(
      "braintrust.dev",
    );
  } catch (error) {
    return false;
  }
};

// credit: https://github.com/rehypejs/rehype-external-links/blob/b0ada86b178e135d55368ffa47b915db17219716/lib/index.js
// simpler implementation that considers braintrust.dev urls are internal
function rehypeExternalLinks({
  rel,
  target,
}: {
  target: string;
  rel: string[];
}) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function (tree: any) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    visit(tree, "element", function (node: any) {
      if (
        node.tagName === "a" &&
        typeof node.properties.href === "string" &&
        !isBraintrustUrl(node.properties.href)
      ) {
        if (rel.length > 0) {
          node.properties.rel = [...rel];
          node.properties.className = "break-all";
        }

        if (target) {
          node.properties.target = target;
        }
      }
    });
  };
}
