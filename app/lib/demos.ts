export type Item = {
  name: string | React.ReactNode;
  link: string;
  description?: string;
};

export const demos: { name: string; items: Item[] }[] = [
  {
    name: "Projects",
    items: [
      {
        name: "Nested Layouts",
        link: "layouts",
        description: "Create UI that is shared across routes",
      },
      {
        name: "Grouped Layouts",
        link: "route-groups",
        description: "Organize routes without affecting URL paths",
      },
    ],
  },
  {
    name: "File Conventions",
    items: [
      {
        name: "Loading",
        link: "loading",
        description:
          "Create meaningful Loading UI for specific parts of an app",
      },
      {
        name: "Error",
        link: "error-handling",
        description: "Create Error UI for specific parts of an app",
      },
      {
        name: "Not Found",
        link: "not-found",
        description: "Create Not Found UI for specific parts of an app",
      },
    ],
  },
  {
    name: "Data Fetching",
    items: [
      {
        name: "Streaming with Suspense",
        link: "streaming",
        description:
          "Streaming data fetching from the server with React Suspense",
      },
      {
        name: "Static-Site Generation",
        link: "ssg",
        description: "Generate static pages",
      },
      {
        name: "Server-Side Rendering",
        link: "ssr",
        description: "Server-render pages",
      },
      {
        name: "Incremental Static Regeneration",
        link: "isr",
        description: "Get the best of both worlds between static & dynamic",
      },
    ],
  },
  {
    name: "Components",
    items: [
      {
        name: "Client Context",
        link: "context",
        description:
          "Pass context between Client Components that cross Server/Client Component boundary",
      },
    ],
  },
  {
    name: "Misc",
    items: [
      {
        name: "Client Component Hooks",
        link: "hooks",
        description: "Preview the routing hooks available in Client Components",
      },
      {
        name: "CSS and CSS-in-JS",
        link: "styling",
        description: "Preview the supported styling solutions",
      },
      {
        name: "Code Snippets",
        link: "snippets",
        description: "A collection of useful App Router code snippets",
      },
    ],
  },
];
