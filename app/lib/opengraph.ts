export const generateOpenGraphUrl = (props?: {
  title?: string;
  description?: string;
  template?: string;
}) => {
  const params = new URLSearchParams();
  if (props?.title) {
    params.append("title", props.title);
  }
  if (props?.description) {
    params.append("description", props.description);
  }
  if (props?.template) {
    params.append("template", props.template);
  }
  const baseUrl =
    process.env.NODE_ENV === "production" || !process.env.VERCEL_URL
      ? "www.braintrust.dev"
      : process.env.VERCEL_URL;
  return `https://${baseUrl}/og?${params.toString()}`;
};
