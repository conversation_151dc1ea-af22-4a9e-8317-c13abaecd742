/**
 * Utility functions for managing consistent onboarding flow IDs
 */

const ONBOARDING_FLOW_ID_KEY = "onboarding_flow_id";

/**
 * Get or create a consistent onboarding flow ID
 * This ensures the same flow ID is used across all onboarding events
 */
export function getOnboardingFlowId(): string {
  if (typeof window === "undefined") return crypto.randomUUID();

  let flowId: string | null = null;
  try {
    flowId = localStorage.getItem(ONBOARDING_FLOW_ID_KEY);
  } catch {
    // Ignore storage errors and fall back to an ephemeral ID
  }

  if (!flowId) {
    flowId = crypto.randomUUID();
    try {
      localStorage.setItem(ONBOARDING_FLOW_ID_KEY, flowId);
    } catch {
      // Storage may be unavailable; continue with ephemeral ID
    }
  }

  return flowId;
}

/**
 * Clear the onboarding flow ID (useful when onboarding is complete)
 */
export function clearOnboardingFlowId(): void {
  if (typeof window === "undefined") return;
  try {
    localStorage.removeItem(ONBOARDING_FLOW_ID_KEY);
  } catch {
    // Ignore storage errors
  }
}

/**
 * Check if an onboarding flow is currently active
 */
export function hasActiveOnboardingFlow(): boolean {
  if (typeof window === "undefined") return false;
  try {
    return localStorage.getItem(ONBOARDING_FLOW_ID_KEY) !== null;
  } catch {
    return false;
  }
}
