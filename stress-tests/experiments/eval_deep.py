# This file creates really small root spans, but very deep traces, so network
# loading should be more of an issue.
import argparse
import random

import doc_utils
from braintrust import Eval, current_span, start_span, traced
from braintrust.framework import Eval

NUM_TRACES = 50


def span_tree():
    with start_span("node") as span:
        span.log(input=doc_utils.make_text(100000), output=doc_utils.make_text(100000))
        for _ in range(10):
            with start_span("child") as child:
                invoke_custom_llm(doc_utils.make_text(100000), {})
                invoke_custom_llm(doc_utils.make_text(100000), {})
                invoke_custom_llm(doc_utils.make_text(100000), {})


def task(input):
    span_tree()
    return doc_utils.make_text(100)


@traced(type="llm", name="Custom LLM", notrace_io=True)
def invoke_custom_llm(llm_input: str, params: dict):
    content = doc_utils.make_text(1000)
    current_span().log(
        input=[{"role": "user", "content": llm_input}],
        output=content,
        metrics=dict(
            prompt_tokens=1000,
            completion_tokens=1000,
            total_tokens=2000,
        ),
        metadata=params,
    )
    return content


def my_score_1(input, output, metadata):
    invoke_custom_llm(input, {})
    return random.random()


def my_score_2(input, output, metadata):
    invoke_custom_llm(input, {})
    return random.random()


Eval(
    "deep eval",
    data=[
        {
            "input": doc_utils.make_text(1024),
        }
        for _ in range(NUM_TRACES)
    ],
    task=task,
    scores=[my_score_1, my_score_2],
)
