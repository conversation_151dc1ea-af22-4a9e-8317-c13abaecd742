import argparse
import random

import braintrust
from doc_utils import jitter, make_text

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--num-traces", type=int, default=10000)
    parser.add_argument("--spans-per-trace", type=int, default=100)
    parser.add_argument("--max-trace-size", type=int, default=1000000)
    args = parser.parse_args()

    print(args)

    braintrust.login()

    logger = braintrust.init_logger("many_large_traces")

    for _ in range(args.num_traces):
        with logger.start_span("root") as root:
            for _ in range(args.spans_per_trace):
                with root.start_span("child") as child:
                    child.log(
                        input=make_text(
                            int(random.randint(1000, min(1001, args.max_trace_size / args.spans_per_trace)))
                        )
                    )
