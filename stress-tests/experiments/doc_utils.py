import random
import struct
from math import ceil, floor
from uuid import uuid4

from faker import Faker

fake = Faker()
Faker.seed(1234)
random.seed(1234)


def make_id():
    return str(uuid4())


def jitter(x, pct):
    return x + (random.random() * x * pct) * (-1 if random.random() < 0.5 else 1)


def random_string(length):
    # Each int32 includes 4 bytes of randomness, which amounts to 8 hex
    # characters.
    num_ints = ceil(length / 8)
    int_buffer = [random.randint(-(2**31), 2**31 - 1) for _ in range(num_ints)]

    # Convert int32 to bytes
    byte_buffer = struct.pack(f"{num_ints}i", *int_buffer)

    # Convert bytes to hex string
    hex_chars = byte_buffer.hex()

    if len(hex_chars) < length:
        raise ValueError(f"Impossible. Generated {len(hex_chars)} hex characters, expected at least {length}")

    return hex_chars[:length]


WORDS = (
    [fake.word() for _ in range(10000)]
    + [f"{fake.word()}_{fake.word()}" for _ in range(10000)]
    + [random_string(24) for _ in range(1000)]
)


def make_word():
    return random.choice(WORDS) if random.random() < 0.9 else make_id()


def make_text(length):
    return " ".join(make_word() for _ in range(floor(length / 10)))[:length]


def make_xml(length):
    return "<root>" + "".join(make_word() for _ in range(floor(length / 10)))[:length] + "</root>"


def make_weights():
    weights = [
        40,
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
    ]
    random.shuffle(weights)
    return weights


json_keys = [(fake.word(), make_weights()) for _ in range(100)]


def make_json():
    used_keys = random.sample(json_keys, random.randint(1, 10))
    return {key: make_scalar(weights) for (key, weights) in used_keys}


def make_scalar(weights):
    choice = random.choices(
        [
            "string",
            "high_cardinality_string",
            "number",
            "object",
            "array",
            "boolean",
            "null",
        ],
        weights=weights,
        k=1,
    )[0]

    if choice == "string":
        return make_word()
    elif choice == "high_cardinality_string":
        return random.choice(WORDS[:3])
    elif choice == "number":
        return random.random()
    elif choice == "object":
        return make_json()
    elif choice == "array":
        return [make_word() for _ in range(random.randint(1, 10))]
    elif choice == "boolean":
        return fake.boolean()
    elif choice == "null":
        return None
    else:
        raise Exception(f"Unknown scalar type {choice}")
