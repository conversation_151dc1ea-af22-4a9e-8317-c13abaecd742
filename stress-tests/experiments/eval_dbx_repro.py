# This reproes an experiment that the Dropbox team was kind enough to let us use
# to debug experiment performance. That said, please treat it with care.
#
# You can download more data using the examples/download_data_object.py script, and then
# uploading them to the S3 bucket and listing them here.

import gzip
import json
import os

import boto3
import braintrust
from braintrust.util import LazyValue

# S3 bucket and key information
BUCKET = "braintrust-data-benchmark"
KEYS = [
    "benchmarks/GPT4o_mini_MuSiQue_Json_2025-01-23_15_52.json.gz",
    "benchmarks/DeepSeekR1_Llama70B_GoogleNQ_Markdown_2025-01-24_11_09.json.gz",
    "benchmarks/experiment_33ee6993-61db-419c-8736-2d97cb4d2348.jsonl.gz",
]
LOCAL_DIR = "data"
LOCAL_FILES = [os.path.join(LOCAL_DIR, os.path.basename(key)) for key in KEYS]


def ensure_data_directory():
    """Create data directory if it doesn't exist"""
    os.makedirs(LOCAL_DIR, exist_ok=True)


def download_if_needed():
    """Download the file from S3 if it doesn't exist locally"""
    for key, local_file in zip(KEYS, LOCAL_FILES):
        if os.path.exists(local_file):
            print(f"File already exists at {local_file}")
            continue

        print(f"Downloading from S3 to {local_file}")
        s3 = boto3.client("s3")
        ensure_data_directory()
        s3.download_file(BUCKET, key, local_file)


def load_data(local_file: str) -> list:
    """Load and decompress the gzipped JSON/JSONL file"""
    with gzip.open(local_file, "rt") as f:
        if local_file.endswith(".jsonl.gz"):
            data = []
            for line in f:
                if line.strip():
                    data.append(json.loads(line))
            return data
        return json.load(f)


def run_log(logger, row):
    """Helper function to log rows"""
    logger.log(LazyValue(lambda: row, use_mutex=False))


def main():
    braintrust.login()

    # Download file if needed
    download_if_needed()

    # Load and process data
    for local_file in LOCAL_FILES:
        experiment = braintrust.init(project="repro", experiment=os.path.splitext(os.path.basename(local_file))[0])
        data = load_data(local_file)

        logger = braintrust.logger._state.global_bg_logger()
        for row in data:
            row["experiment_id"] = experiment.id
            row["span_parents"] = row.get("span_parents") or []

            # Remove unnecessary fields if they exist
            for field in ["project_id", "_xact_id", "created"]:
                row.pop(field, None)

            if len(row.get("span_parents")) > 0:
                row.pop("tags", None)

            run_log(logger, row)

        logger.flush()
        print(experiment.summarize())


if __name__ == "__main__":
    main()
