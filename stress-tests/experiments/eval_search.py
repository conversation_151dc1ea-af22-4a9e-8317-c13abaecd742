import argparse
import random

import doc_utils
from braintrust import Eval, current_span, traced
from braintrust.framework import Eval

NUM_TRACES = 2000


def task(input):
    return doc_utils.make_text(20000)


@traced(type="llm", name="Custom LLM", notrace_io=True)
def invoke_custom_llm(llm_input: str, params: dict):
    content = doc_utils.make_text(1000)
    current_span().log(
        input=[{"role": "user", "content": llm_input}],
        output=content,
        metrics=dict(
            prompt_tokens=1000,
            completion_tokens=1000,
            total_tokens=2000,
        ),
        metadata=params,
    )
    return content


def my_score_1(input, output, metadata):
    invoke_custom_llm(input, {})
    return random.random()


def my_score_2(input, output, metadata):
    invoke_custom_llm(input, {})
    return random.random()


Eval(
    "search style",
    data=[
        {
            "input": doc_utils.make_text(1024),
            "metadata": {
                "stats": doc_utils.make_json(),
                "results": [{"doc_id": doc_utils.make_id(), "result": doc_utils.make_text(5000)} for _ in range(10)],
            },
        }
        for _ in range(NUM_TRACES)
    ],
    task=task,
    scores=[my_score_1, my_score_2],
)
