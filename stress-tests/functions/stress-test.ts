import { invoke } from "braintrust";

const NUM_EXECUTIONS = 20;

async function executeTool() {
  const start = Date.now();
  const result = await invoke({
    projectName: "Functions stress test",
    slug: "restaurant-search",
    input: {},
    stream: true,
  });

  const finalValue = await result.finalValue();
  console.log(finalValue);

  const end = Date.now();
  console.log(end - start);
  return end - start;
}

async function main() {
  const times = await Promise.all(
    Array(NUM_EXECUTIONS)
      .fill(0)
      .map(() => executeTool()),
  );
  console.log(times);
}

main();
