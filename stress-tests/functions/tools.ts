import braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({
  name: "Functions stress test",
});

const search = project.tools.create({
  name: "Search",
  handler: async ({ query }: { query: string }) => {
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 100));
    return `pizza: $5 at dominos,
sushi: $10 at sushi king,
burgers: $15 at burger king,
pasta: $20 at pasta place,
tacos: $8 at taco truck,
salad: $12 at green eats,
steak: $30 at steakhouse,
ramen: $13 at noodle bar,
${query}: $100 at unknown place,
`;
  },
  parameters: z.object({
    query: z.string(),
  }),
  ifExists: "replace",
});

project.prompts.create({
  name: "Restaurant search",
  slug: "restaurant-search",
  messages: [
    {
      role: "system",
      content: `You can answer questions about restaurants in San Francisco. If you do not know the answer, state that explicitly without guessing. Provide just the answer to the question, without asking for follow ups.`,
    },
  ],
  model: "gpt-4o-mini",
  params: { temperature: 0.1, max_tokens: 1024 },
  tools: [search],
  ifExists: "replace",
});
