import argparse
import glob
import json
import multiprocessing
import os
from concurrent.futures import ThreadPoolExecutor

import psycopg2
from psycopg2.extras import execute_values
from tqdm import tqdm

PG_URL = os.getenv("PG_URL", "postgres://postgres:postgres@localhost:5532/postgres")
DEFAULT_THREADS = multiprocessing.cpu_count() * 4


def create_tracking_table(conn):
    """Create the table that tracks processed files if it doesn't exist."""
    cur = conn.cursor()
    cur.execute(
        """
        CREATE TABLE IF NOT EXISTS processed_files (
            filepath TEXT PRIMARY KEY,
            processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            record_count INTEGER
        )
    """
    )
    conn.commit()
    cur.close()


def is_file_processed(filepath, conn):
    """Check if a file has already been processed."""
    cur = conn.cursor()
    cur.execute("SELECT EXISTS(SELECT 1 FROM processed_files WHERE filepath = %s)", (filepath,))
    exists = cur.fetchone()[0]
    cur.close()
    return exists


def mark_file_processed(filepath, record_count, conn):
    """Mark a file as processed."""
    cur = conn.cursor()
    cur.execute("INSERT INTO processed_files (filepath, record_count) VALUES (%s, %s)", (filepath, record_count))
    conn.commit()
    cur.close()


def load_file(filepath, org_id, project_id, experiment_id=None, conn=None):
    """Load a single JSONL file into the logs table."""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Connect to database if needed
            if conn is None:
                conn = psycopg2.connect(PG_URL)

            # Check if file was already processed
            if is_file_processed(filepath, conn):
                tqdm.write(f"Skipping {os.path.basename(filepath)} - already processed")
                return conn

            # Read all lines from the file and modify the required fields
            records_with_sequence = []
            records_without_sequence = []

            with open(filepath, "r") as f:
                for line in f:
                    record = json.loads(line)
                    record["org_id"] = org_id
                    record["project_id"] = project_id
                    if experiment_id is not None:
                        record["experiment_id"] = experiment_id

                    if "sequence_id" in record:
                        sequence_id = int(record.pop("sequence_id"))
                        records_with_sequence.append((sequence_id, json.dumps(record)))
                    else:
                        records_without_sequence.append((json.dumps(record),))

            cur = conn.cursor()
            # Insert records with sequence_id
            if records_with_sequence:
                execute_values(
                    cur,
                    "INSERT INTO logs (sequence_id, data) VALUES %s",
                    records_with_sequence,
                    template="(%s, %s::jsonb)",
                    page_size=1000,
                )

            # Insert records without sequence_id
            if records_without_sequence:
                execute_values(
                    cur,
                    "INSERT INTO logs (data) VALUES %s",
                    records_without_sequence,
                    template="(%s::jsonb)",
                    page_size=1000,
                )

            num_values = len(records_with_sequence) + len(records_without_sequence)

            # Mark file as processed
            mark_file_processed(filepath, num_values, conn)

            conn.commit()
            cur.close()
            tqdm.write(f"Successfully loaded {num_values} records from {os.path.basename(filepath)}")
            return conn

        except (psycopg2.OperationalError, psycopg2.InterfaceError) as e:
            retry_count += 1
            tqdm.write(f"Database connection error on {filepath}, attempt {retry_count}/{max_retries}: {str(e)}")

            if conn is not None:
                try:
                    conn.close()
                except:
                    pass
                conn = None

            if retry_count < max_retries:
                tqdm.write("Retrying with new connection...")
            else:
                tqdm.write(f"Max retries reached for {filepath}")
                raise
        except Exception as e:
            tqdm.write(f"Error processing {filepath}: {str(e)}")
            raise


def load_and_sort_files(jsonl_files, org_id, project_id, experiment_id=None):
    """Load all files and sort by _xact_id before inserting."""
    all_records = []

    # Load all files
    for filepath in jsonl_files:
        try:
            with open(filepath, "r") as f:
                for line in f:
                    record = json.loads(line)
                    record["org_id"] = org_id
                    record["project_id"] = project_id
                    if experiment_id is not None:
                        record["experiment_id"] = experiment_id
                    all_records.append(record)
            print(f"Loaded {os.path.basename(filepath)}")
        except Exception as e:
            print(f"Error loading {filepath}: {str(e)}")

    print(f"Sorting {len(all_records)} records by _xact_id...")
    all_records.sort(key=lambda x: x.get("_xact_id", 0))

    # Insert sorted records in batches
    try:
        conn = psycopg2.connect(PG_URL)
        cur = conn.cursor()

        batch_size = 1000
        for i in range(0, len(all_records), batch_size):
            batch = all_records[i : i + batch_size]
            values = [(json.dumps(record),) for record in batch]

            execute_values(cur, "INSERT INTO logs (data) VALUES %s", values, template="(%s::jsonb)")
            conn.commit()
            print(f"Inserted batch {i//batch_size + 1}/{(len(all_records) + batch_size - 1)//batch_size}")

        cur.close()
        conn.close()
        print(f"Successfully loaded all {len(all_records)} records in sorted order")

    except Exception as e:
        print(f"Error during sorted insert: {str(e)}")


def process_files_for_thread(files, org_id, project_id, experiment_id=None, conn=None, pbar=None):
    """Process all files for a single thread using one connection."""
    try:
        for filepath in files:
            try:
                conn = load_file(filepath, org_id, project_id, experiment_id, conn)
                if pbar:
                    pbar.update(1)
            except Exception as e:
                tqdm.write(f"Error processing file {filepath}: {str(e)}")
                if pbar:
                    pbar.update(1)
                continue
    finally:
        if conn is not None:
            try:
                conn.close()
            except:
                pass


def main():
    parser = argparse.ArgumentParser(description="Load JSONL files into PostgreSQL database")
    parser.add_argument("path", help="Path to JSONL file or directory containing JSONL files")
    parser.add_argument(
        "--threads",
        type=int,
        default=DEFAULT_THREADS,
        help=f"Number of threads to use (default: {DEFAULT_THREADS} - CPU count * 2)",
    )
    parser.add_argument("--org-id", required=True, help="Organization ID to set in each record")
    parser.add_argument("--project-id", required=True, help="Project ID to set in each record")
    parser.add_argument("--experiment-id", help="Optional experiment ID to set in each record")
    parser.add_argument("--sort", action="store_true", help="Sort records by _xact_id before inserting")
    args = parser.parse_args()

    # Check if the path is a file or directory
    if os.path.isfile(args.path):
        if not args.path.endswith(".jsonl"):
            print(f"Error: {args.path} is not a JSONL file")
            return
        jsonl_files = [args.path]
    else:
        # Get all .jsonl files in the directory
        jsonl_files = glob.glob(os.path.join(args.path, "*.jsonl"))

    if not jsonl_files:
        print(f"No JSONL files found at {args.path}")
        return

    # Create tracking table
    conn = psycopg2.connect(PG_URL)
    create_tracking_table(conn)
    conn.close()

    if args.sort:
        print(f"Found {len(jsonl_files)} JSONL files. Loading all files for sorted insert...")
        load_and_sort_files(jsonl_files, args.org_id, args.project_id, args.experiment_id)
    else:
        print(f"Found {len(jsonl_files)} JSONL files. Starting import with {args.threads} threads...")
        # Split files into groups, one per thread
        files_per_thread = [jsonl_files[i :: args.threads] for i in range(args.threads)]

        # Create progress bar for total files
        with tqdm(total=len(jsonl_files), desc="Processing files") as pbar:
            # Process files in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=args.threads) as executor:
                # Each thread gets its own connection and group of files
                futures = [
                    executor.submit(
                        process_files_for_thread,
                        thread_files,
                        args.org_id,
                        args.project_id,
                        args.experiment_id,
                        None,  # conn
                        pbar,  # progress bar
                    )
                    for thread_files in files_per_thread
                ]
                # Wait for all threads to complete
                for future in futures:
                    future.result()


if __name__ == "__main__":
    main()
