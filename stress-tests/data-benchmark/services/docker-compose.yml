services:
  clickhouse:
    build: ./clickhouse
    ports:
      - 28123:8123
      - 29000:9000
      - 29005:9005
    environment:
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: default
      AWS_ACCESS_KEY_ID: minio_root_user
      AWS_SECRET_ACCESS_KEY: minio_root_password
    volumes:
      - ../data/clickhouse:/var/lib/clickhouse
      - ../data/clickhouse-logs:/var/log/clickhouse-server
      # S3 storage config.
      - ./clickhouse/storage_config.xml:/etc/clickhouse-server/config.d/storage_config.xml
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      minio:
        condition: service_healthy
  quickwit:
    build: ./quickwit
    ports:
      - 27280:7280
    environment:
      AWS_ACCESS_KEY_ID: minio_root_user
      AWS_SECRET_ACCESS_KEY: minio_root_password
      AWS_REGION: us-east-1
    volumes:
      - ../data/qwdata:/quickwit/qwdata
      - ../data/qwmount/:/qwmount
    command: ["run", "--config", "/etc/config.yaml"]
    depends_on:
      minio:
        condition: service_healthy
  minio:
    build: ./minio
    ports:
      # Main port to access minio REST API.
      - 20000:9000
      # Port for minio web UI.
      - 20001:9001
    volumes:
      - ../data/minio:/data
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      MINIO_ROOT_USER: minio_root_user
      MINIO_ROOT_PASSWORD: minio_root_password
      # This is the name of the bucket used by various data systems. If not
      # present, the buckets are created upon container startup.
      MINIO_CLICKHOUSE_BUCKET: clickhouse
      MINIO_QUICKWIT_BUCKET: quickwit
