import argparse
import json
from pprint import pprint
from typing import Any, Dict, List

import pyarrow as pa
from pyarrow import parquet as pq


def shred_variant(data):
    def convert_value(value):
        if isinstance(value, (int, float, str, bool)):
            return {"typed_value": value, "untyped_value": None}
        else:
            return {"typed_value": None, "untyped_value": json.dumps(value)}

    shredded = {"untyped_value": None, "object": {}, "array": None, "typed_value": None}

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, dict):
                shredded["object"][key] = shred_variant(value)
            elif isinstance(value, list):
                shredded["object"][key] = shred_variant(value)
            else:
                shredded["object"][key] = convert_value(value)
    elif isinstance(data, list):
        shredded["array"] = [shred_variant(item) for item in data]
    else:
        converted = convert_value(data)
        shredded["typed_value"] = converted["typed_value"]
        shredded["untyped_value"] = converted["untyped_value"]

    return shredded


def create_shredded_schema(data: Dict[str, Any]) -> pa.Schema:
    def create_field(key: str, value: Any) -> pa.Field:
        if isinstance(value, dict):
            if set(value.keys()) == {"typed_value", "untyped_value", "object", "array"}:
                # This is our shredded structure
                object_fields = [create_field(k, v) for k, v in value.items()]
                return pa.field(key, pa.struct(object_fields))
            else:
                # This is a regular nested object
                object_fields = [create_field(k, v) for k, v in value.items()]
                if not object_fields:
                    object_fields = [pa.field("dummy", pa.null())]
                return pa.field(key, pa.struct(object_fields))
        elif key == "untyped_value":
            return pa.field(key, pa.binary())
        elif key == "object":
            object_fields = [create_field(k, v) for k, v in value.items()]
            if not object_fields:
                object_fields = [pa.field("dummy", pa.null())]
            return pa.field(key, pa.struct(object_fields))
        elif key == "array":
            if value:
                array_type = create_field("item", value[0]).type
                return pa.field(key, pa.list_(array_type))
            else:
                return pa.field(key, pa.list_(pa.struct([pa.field("dummy", pa.null())])))
        elif key == "typed_value":
            if isinstance(value, bool):
                return pa.field(key, pa.bool_())
            elif isinstance(value, int):
                return pa.field(key, pa.int64())
            elif isinstance(value, float):
                return pa.field(key, pa.float64())
            elif isinstance(value, str):
                return pa.field(key, pa.string())
            else:
                return pa.field(key, pa.null())
        else:
            raise ValueError(f"Unknown key: {key}")

    fields = [create_field(key, value) for key, value in data.items()]
    return pa.schema(fields)


def conform_to_field(value, field: pa.DataType) -> Dict[str, Any]:
    result = {"typed_value": None, "untyped_value": None, "object": {}, "array": None}

    # This is already in our shredded format
    if isinstance(value.get("typed_value"), (int, float, str, bool)):
        typed_value = value["typed_value"]
        typed_value_type = field["typed_value"].type
        if (
            (pa.types.is_boolean(typed_value_type) and isinstance(typed_value, bool))
            or (pa.types.is_integer(typed_value_type) and isinstance(typed_value, int))
            or (pa.types.is_floating(typed_value_type) and isinstance(typed_value, float))
            or (pa.types.is_string(typed_value_type) and isinstance(typed_value, str))
        ):
            result["typed_value"] = value["typed_value"]
        else:
            result["untyped_value"] = json.dumps(value["typed_value"])
    elif value.get("untyped_value") is not None:
        result["untyped_value"] = json.dumps(value["untyped_value"])

    if isinstance(value.get("object"), dict):
        try:
            for k, v in value["object"].items():
                result["object"][k] = conform_to_field(v, field["object"].type[k].type)
        except KeyError as e:
            result["untyped_value"] = json.dumps(value["object"])

    if isinstance(value.get("array"), list) and field["array"].type.value_type:
        result["array"] = [conform_to_field(v, field["array"].type.value_type) for v in value["array"]]

    return result


def conform_to_schema(data: Dict[str, Any], schema: pa.Schema) -> Dict[str, Any]:
    return conform_to_field(data, pa.struct(list(schema)))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("input", type=str, help="Input file")
    parser.add_argument("--output", type=str, help="Output file")
    args = parser.parse_args()

    # Read JSONL file and convert to shredded format
    with open(args.input, "r") as f:
        data = [shred_variant(json.loads(line)) for line in f]

    # Create PyArrow Table with shredded schema
    schema = create_shredded_schema(data[0])
    # pprint(data)
    data = [conform_to_schema(row, schema) for row in data]
    # pprint(data)
    table = pa.Table.from_pylist(data, schema=schema)

    # Write to Parquet file
    output_file = args.output or args.input.rsplit(".", 1)[0] + ".parquet"
    pq.write_table(table, output_file, compression="zstd")

    print(f"Compressed and shredded data written to {output_file}")
