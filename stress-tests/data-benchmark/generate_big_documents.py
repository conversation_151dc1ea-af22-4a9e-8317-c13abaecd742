#!/usr/bin/env python3

# Generates documents that represent stressful characteristics of document-heavy workloads:
# - sparse fields
# - huge text blobs
# - high cardinality and low cardinality values
# - avg document size around 500kb
# - multiple spans per document
import argparse
import datetime
import json
import os
import random
import struct
import sys
import time
from math import ceil, floor
from uuid import uuid4

import braintrust
import requests
from braintrust.util import Lazy<PERSON><PERSON><PERSON>
from faker import Faker


def make_object_type(project_id, experiment_id):
    if experiment_id:
        return "experiment"
    else:
        return "global_log"


def make_object_id(project_id, experiment_id):
    if experiment_id:
        return experiment_id
    else:
        return project_id


fake = Faker()
Faker.seed(1234)
random.seed(1234)


def make_id():
    return str(uuid4())


def jitter(x, pct):
    return x + (random.random() * x * pct) * (-1 if random.random() < 0.5 else 1)


def random_string(length):
    # Each int32 includes 4 bytes of randomness, which amounts to 8 hex
    # characters.
    num_ints = ceil(length / 8)
    int_buffer = [random.randint(-(2**31), 2**31 - 1) for _ in range(num_ints)]

    # Convert int32 to bytes
    byte_buffer = struct.pack(f"{num_ints}i", *int_buffer)

    # Convert bytes to hex string
    hex_chars = byte_buffer.hex()

    if len(hex_chars) < length:
        raise ValueError(f"Impossible. Generated {len(hex_chars)} hex characters, expected at least {length}")

    return hex_chars[:length]


WORDS = (
    [fake.word() for _ in range(10000)]
    + [f"{fake.word()}_{fake.word()}" for _ in range(10000)]
    + [random_string(24) for _ in range(1000)]
)


def make_word():
    return random.choice(WORDS) if random.random() < 0.9 else make_id()


def make_text(length):
    return " ".join(make_word() for _ in range(floor(length / 10)))[:length]


def make_xml(length):
    return "<root>" + "".join(make_word() for _ in range(floor(length / 10)))[:length] + "</root>"


def make_weights():
    weights = [
        40,
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
        random.randint(1, 3),
    ]
    random.shuffle(weights)
    return weights


json_keys = [(fake.word(), make_weights()) for _ in range(100)]


def make_json():
    used_keys = random.sample(json_keys, random.randint(1, 10))
    return {key: make_scalar(weights) for (key, weights) in used_keys}


def make_scalar(weights):
    choice = random.choices(
        [
            "string",
            "high_cardinality_string",
            "number",
            "object",
            "array",
            "boolean",
            "null",
        ],
        weights=weights,
        k=1,
    )[0]

    if choice == "string":
        return make_word()
    elif choice == "high_cardinality_string":
        return random.choice(WORDS[:3])
    elif choice == "number":
        return random.random()
    elif choice == "object":
        return make_json()
    elif choice == "array":
        return [make_word() for _ in range(random.randint(1, 10))]
    elif choice == "boolean":
        return fake.boolean()
    elif choice == "null":
        return None
    else:
        raise Exception(f"Unknown scalar type {choice}")


exec_counter = 0

span_names = [fake.word() for _ in range(50)]
metadata_keys = [
    (
        fake.word(),
        make_weights(),
    )
    for _ in range(10000)
]
scores = [fake.word() for _ in range(100)]
models = ["gpt-4o", "gpt-3.5-turbo", "claude-3-5-sonnet-20240620"]
tags = [fake.word() for _ in range(50)]


def generate_trace_ids(num_traces, num_spans, unique_frac):
    num_traces = ceil(num_traces * unique_frac)
    assert num_traces > 0
    unique_traces = []
    for _ in range(num_traces):
        span_row_ids = [(make_id(), make_id()) for _ in range(max(floor(jitter(num_spans, 0.10)), 1))]
        unique_traces.append(span_row_ids)
    out = []
    while len(out) < num_traces:
        out.extend(random.sample(unique_traces, k=len(unique_traces)))
    out = out[:num_traces]
    return out


SEQUENCE_ID_COUNTER = 0


def generate_span(id, span_id, root_span_id, start, xact_id, doc_size, project_id, experiment_id):
    global exec_counter

    end = start + random.random() * 100
    exec_counter += 1
    is_root = span_id == root_span_id

    ret = {
        "id": id,
        "input": {},
        "output": {},
        "scores": {},
        "expected": {},
        "metadata": {},
        "metrics": {"end": end, "start": start},
        "context": {
            "caller_lineno": 103,
            "caller_filename": "/Users/<USER>/projects/braintrust/stress-tests/fake_experiments.py",
            "caller_functionname": "log_all_spans",
        },
        "created": datetime.datetime.fromtimestamp(end, datetime.timezone.utc).isoformat(),
        "span_id": span_id,
        "_xact_id": xact_id,
        "project_id": project_id,
        "root_span_id": root_span_id,
        "span_attributes": {"name": random.choice(span_names), "type": "task", "exec_counter": exec_counter},
        "span_parents": [] if is_root else [root_span_id],
        "object_type": make_object_type(project_id, experiment_id),
        "object_id": make_object_id(project_id, experiment_id),
    }

    if experiment_id is not None:
        ret["experiment_id"] = experiment_id
    else:
        ret["log_id"] = "g"

    if is_root and random.random() < 0.5:
        ret["tags"] = random.sample(tags, random.randint(1, 5))

    is_llm_span = random.random() < 0.25
    if is_llm_span:
        ret["span_attributes"]["type"] = "llm"
        ret["span_attributes"]["name"] = "Chat completion"

        ret["input"] = []
        num_messages = random.randint(1, 5)
        for _ in range(num_messages):
            ret["input"].append(
                {
                    "role": random.choice(["user", "assistant"]),
                    "content": make_text(floor(jitter(doc_size / num_messages, 0.25))),
                }
            )

        ret["output"] = [
            {
                "role": "assistant",
                "content": make_text(floor(jitter(doc_size / 4, 0.25))),
            }
        ]

        ret["metadata"] = {
            "model": random.choice(models),
            "temperature": random.random(),
            "max_tokens": random.randint(100, 1000),
        }

        ret["metrics"] = {
            "prompt_tokens": random.randint(100, 1000),
            "completion_tokens": random.randint(100, 1000),
            "total_tokens": random.randint(100, 1000),
        }
    else:
        ret["span_attributes"]["type"] = "function"

        input_keys_sample = random.sample(metadata_keys, random.randint(5, 20))
        ret["input"] = {
            "some_array": [make_word() for _ in range(random.randint(1, 10))],
            "some_xml": make_xml(1000),
            "some_json": {
                **{key: make_scalar(weights) for (key, weights) in input_keys_sample},
            },
            "some_text": make_text(floor(jitter(doc_size / 2, 0.25))),
        }
        output_keys_sample = random.sample(metadata_keys, random.randint(5, 20))
        ret["output"] = {
            "some_array": [make_word() for _ in range(random.randint(1, 10))],
            "some_xml": make_text(1000),
            "some_json": {
                **{key: make_scalar(weights) for (key, weights) in output_keys_sample},
            },
            "some_text": make_text(floor(jitter(doc_size / 2, 0.25))),
        }
        metadata_keys_sample = random.sample(metadata_keys, random.randint(5, 20))
        ret["metadata"] = {
            "high_cardinality_string": random.choice(WORDS[:3]),
            **{key: make_scalar(weights) for (key, weights) in metadata_keys_sample},
        }

        scores_sample = random.sample(scores, random.randint(20, 50))
        ret["scores"] = {**{key: random.random() for key in scores_sample}}

    global SEQUENCE_ID_COUNTER
    ret["sequence_id"] = SEQUENCE_ID_COUNTER
    SEQUENCE_ID_COUNTER += 1

    return ret


xact_id_counter = 0


def generate_trace(span_row_ids, doc_size, project_id, experiment_id):
    global xact_id_counter

    curr_time = int(time.time())
    start = curr_time - random.randint(0, 180 * 24 * 60 * 60)  # Random time within the past 6 months

    xact_id_counter += 1
    xact_id = str((0x0DE1 << 48) | ((curr_time & 0xFFFFFFFFFFFF) << 16) | (xact_id_counter & 0xFFFF))

    root_span_id = span_row_ids[0][1]
    spans = [
        generate_span(id, span_id, root_span_id, start, xact_id, doc_size, project_id, experiment_id)
        for id, span_id in span_row_ids
    ]
    return spans


def strip_for_logging(span):
    ret = {**span}
    del ret["project_id"]
    del ret["_xact_id"]
    del ret["created"]
    ret["span_parents"] = ret.get("span_parents") or []
    del ret["sequence_id"]
    if len(ret.get("span_parents")) > 0 and "tags" in ret:
        del ret["tags"]

    return ret


def run_log(logger, row):
    logger.log(LazyValue(lambda: row, use_mutex=False))


def log_spans(spans):
    logger = braintrust.logger._state.global_bg_logger()
    for span in spans:
        # This gets batched behind the scenes
        run_log(logger, strip_for_logging(span))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Generate large records suitable for stress testing analytical queries",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--num-traces", help="Number of traces to generate", type=int, default=100)
    parser.add_argument("--spans-per-trace", help="Number of spans per trace", type=int, default=4)
    parser.add_argument(
        "--unique-frac",
        help="Fraction of unique traces (between 0 and 1) in the output. Repeated traces will have the same id, span_id, root_span_id with different _xact_ids",
        type=float,
        default=1.0,
    )
    parser.add_argument("--doc-size-kb", help="Approximate size of each span in KB", type=int, default=500)
    parser.add_argument(
        "--project-id", help="Project ID value", type=str, default="53789c2c-3b61-423b-b253-4d008cd6f720"
    )
    parser.add_argument("--experiment-id", help="Experiment ID value", type=str, default=None)
    parser.add_argument("--create-experiment", action="store_true")
    parser.add_argument("--log-bt", help="Whether to log the generated spans to braintrust", action="store_true")

    args = parser.parse_args()

    experiment = None
    if args.create_experiment:
        experiment = braintrust.init("large experiment")
        args.experiment_id = experiment.id
        args.project_id = experiment.project.id
        args.log_bt = True

    if args.log_bt:
        braintrust.login()

    all_span_row_ids = generate_trace_ids(args.num_traces, args.spans_per_trace, args.unique_frac)
    start = time.time()
    for i, span_row_ids in enumerate(all_span_row_ids):
        print(f"Generating trace {i} ({1000*(time.time() - start)}ms)", file=sys.stderr)
        start = time.time()
        spans = generate_trace(span_row_ids, args.doc_size_kb * 1024, args.project_id, args.experiment_id)
        if args.log_bt:
            log_spans(spans)
        else:
            for span in spans:
                print(json.dumps(span))

    if experiment:
        print(experiment.summarize())
