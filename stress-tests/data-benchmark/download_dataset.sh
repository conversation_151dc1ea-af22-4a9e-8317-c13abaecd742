#!/bin/bash

# Parse command line arguments
SKIP_SYNC=false
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--skip-sync)
            SKIP_SYNC=true
            shift
            ;;
        *)
            if [ -z "$S3_PATH" ]; then
                S3_PATH=$1
            elif [ -z "$LOCAL_PATH" ]; then
                LOCAL_PATH=$1
            else
                echo "Usage: $0 [-s|--skip-sync] <s3_path> <local_path>"
                exit 1
            fi
            shift
            ;;
    esac
done

# Verify required arguments
if [ -z "$S3_PATH" ] || [ -z "$LOCAL_PATH" ]; then
    echo "Usage: $0 [-s|--skip-sync] <s3_path> <local_path>"
    exit 1
fi

# Create local directory if it doesn't exist
mkdir -p "$LOCAL_PATH"

# Download files from S3 unless skip flag is set
if [ "$SKIP_SYNC" = false ]; then
    echo "Downloading files from $S3_PATH..."
    aws s3 sync "$S3_PATH" "$LOCAL_PATH"
else
    echo "Skipping S3 sync..."
fi

# Find all .gz files
GZ_FILES=$(find "$LOCAL_PATH" -name "*.gz")
TOTAL_FILES=$(echo "$GZ_FILES" | wc -l)
CURRENT=0

# Extract files in parallel using all available cores
echo "Extracting files..."
echo "$GZ_FILES" | parallel --bar "gunzip -f {}"

echo -e "\nDone!"
