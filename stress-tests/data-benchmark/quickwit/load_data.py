#!/usr/bin/env python3
import argparse
import json
import os
import sys
import time

import requests


def upload_data(lines):
    resp = requests.post(
        f"http://localhost:27280/api/v1/{args.index_name}/ingest",
        headers={"Content-Type": "application/json"},
        data="\n".join(lines),
    )
    if not resp.ok:
        raise Exception(f"Failed to upload data: {resp.text}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Upload an data into quickwit",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--index-name", help=f"Name of index to upload to", default="braintrust")
    parser.add_argument("--filepath", required=True, help=f"Path to json file containing data")
    # The max payload size is 10MB.
    parser.add_argument(
        "--batch-size", help=f"Max number of bytes to upload at a time", default=(9 * 1000 * 1000), type=int
    )
    args = parser.parse_args()

    print("Loading data", file=sys.stderr)
    start_time = time.time()
    with open(args.filepath) as f:
        lines = []
        line_len = 0
        for i, line in enumerate(f):
            lines.append(line)
            line_len += len(line)
            if line_len >= args.batch_size:
                print("Uploading lines", i - len(lines) + 1, "to", i + 1, file=sys.stderr)
                lines_transformed = [{"text_data": line, "json_data": json.loads(line)} for line in lines]
                lines_serialized = [json.dumps(line) for line in lines_transformed]
                upload_data(lines_serialized)
                lines = []
                line_len = 0
        if lines:
            print("Uploading lines", i - len(lines) + 1, "to", i + 1, file=sys.stderr)
            upload_data(lines)
    print(f"Loading data finished in {time.time() - start_time} seconds", file=sys.stderr)
