#!/usr/bin/env python3
import argparse
import os

import requests

BASE_DIR = os.path.realpath(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Upload an index configuration into quickwit",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--filepath", help=f"Path to YAML file to load", default=os.path.join(BASE_DIR, "braintrust_index_config.yaml")
    )
    args = parser.parse_args()

    with open(args.filepath) as f:
        file_content = f.read()

    resp = requests.post(
        "http://localhost:27280/api/v1/indexes", headers={"Content-Type": "application/yaml"}, data=file_content
    )
    if not resp.ok:
        raise Exception(f"Failed to upload index configuration: {resp.text}")
