#!/usr/bin/env python3
import argparse

import requests

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Delete a quickwit index",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--index-name", help=f"Index to delete", default="braintrust")
    args = parser.parse_args()

    resp = requests.delete(f"http://localhost:27280/api/v1/indexes/{args.index_name}")
    if not resp.ok:
        raise Exception(resp.text)
