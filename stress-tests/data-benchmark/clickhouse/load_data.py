#!/usr/bin/env python3
import argparse
import os
import shutil
import sys
import time
import uuid

from util import USER_FILES_DIR, clickhouse_client


def load_logs(filepath):
    print("Mounting file to clickhouse", file=sys.stderr)
    dst_basename = str(uuid.uuid4())
    dst_filepath = os.path.join(USER_FILES_DIR, dst_basename)
    shutil.copy2(filepath, dst_filepath)
    try:
        print("Loading data", file=sys.stderr)
        start_time = time.time()
        client = clickhouse_client()
        query = f"""
    insert into logs(data, object_type, object_id, root_span_id, id)
    select
        data "data",
        JSONExtractString(data, 'object_type') "object_type",
        JSONExtractString(data, 'object_id') "object_id",
        JSONExtractString(data, 'root_span_id') "root_span_id",
        JSONExtractString(data, 'id') "id"
    from file('{dst_basename}', 'JSONAsString', 'data text')
    """
        client.query(query)
        print(f"Loading data finished in {time.time() - start_time} seconds", file=sys.stderr)
    finally:
        os.remove(dst_filepath)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Load data into the clickhouse instance",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--filepath", help=f"Path to json file to load", required=True)
    parser.add_argument("--tablename", help="Table to load into", choices=["logs"], default="logs")
    args = parser.parse_args()

    if args.tablename == "logs":
        load_logs(args.filepath)
    else:
        raise Exception(f"Unknown table {args.tablename}")
