#!/usr/bin/env python3
import argparse

from util import clickhouse_client

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Run a .sql script against the clickhouse instance. Assumes queries are separated by ';'",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("filepath", help=f"Path to .sql file to run")
    args = parser.parse_args()

    client = clickhouse_client()
    with open(args.filepath) as f:
        queries = f.read().split(";")
        for query in queries:
            query = "\n".join(l for l in query.split("\n") if not l.strip().startswith("--")).strip()
            if query:
                print(query)
                client.query(query)
