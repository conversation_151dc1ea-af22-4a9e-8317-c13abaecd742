import functools
import os

import clickhouse_connect

CLICKHOUSE_CONNECT_PARAMS = dict(
    host="localhost",
    port=28123,
    user="default",
    password="default",
    database="default",
)

DATA_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "data"))
USER_FILES_DIR = os.path.join(DATA_DIR, "clickhouse", "user_files")


@functools.cache
def clickhouse_client():
    return clickhouse_connect.get_client(**CLICKHOUSE_CONNECT_PARAMS)


def duplicate_clickhouse_table(tblname, suffix="_test"):
    client = clickhouse_client()
    stmt = client.query(f"show create table {tblname}").first_row[0]
    new_stmt = stmt.replace(f"default.{tblname}", f"default.{tblname}{suffix}")
    client.query(new_stmt)
