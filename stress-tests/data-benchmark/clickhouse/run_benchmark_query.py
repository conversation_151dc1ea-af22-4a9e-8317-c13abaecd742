#!/usr/bin/env python3
import argparse
import json
import time

from benchmark_queries import gen_object_scan
from util import clickhouse_client

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Run benchmark queries against the clickhouse instance",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--tblname", help="Table to run against", required=True)
    parser.add_argument("--object-type", help="Object type", required=True)
    parser.add_argument("--object-id", help="Object id", required=True)
    parser.add_argument("--pagination-root-span-id", help="The root span id to paginate below")
    parser.add_argument("--filter-expr", help="A SQL expression to filter for")
    parser.add_argument("--limit", type=int, help="Limit the number of traces")
    parser.add_argument("--include-full-data", action="store_true", help="Return the full data row in the query")
    args = parser.parse_args()

    query_params = dict(
        tblname=args.tblname,
        object_type=args.object_type,
        object_id=args.object_id,
        pagination_root_span_id=args.pagination_root_span_id,
        filter_expr=args.filter_expr,
        limit=args.limit,
        include_full_data=args.include_full_data,
    )
    query_str = gen_object_scan(**query_params)

    client = clickhouse_client()
    start_time = time.time()
    res = client.query(query_str)
    rows = list(res.named_results())
    end_time = time.time()
    pagination_root_span_id = min([r["root_span_id"] for r in rows]) if rows else None
    out = dict(
        rows=rows,
        pagination_root_span_id=pagination_root_span_id,
        query=query_str,
        running_time=(end_time - start_time),
    )
    print(json.dumps(out))
