import textwrap
from typing import Optional, TypedDict


class ObjectScanParams(TypedDict):
    tblname: str
    object_type: str
    object_id: str
    pagination_root_span_id: Optional[str]
    filter_expr: Optional[str]
    limit: Optional[int]
    include_full_data: Optional[bool]


def wrap_str(s):
    return textwrap.dedent(s).strip()


def gen_object_scan(**params: ObjectScanParams) -> str:
    tblname = params["tblname"]
    object_type = params["object_type"]
    object_id = params["object_id"]
    filter_expr = params.get("filter_expr")
    pagination_root_span_id = params.get("pagination_root_span_id")
    limit = params.get("limit")

    object_filter_expr = f"{tblname}.object_type = '{object_type}' and {tblname}.object_id = '{object_id}'"
    if pagination_root_span_id:
        object_filter_expr += f" and {tblname}.root_span_id < '{pagination_root_span_id}'"

    if limit is not None:
        limit_expr = wrap_str(
            f"""
            order by {tblname}.root_span_id desc
            limit {limit}
        """
        )
    else:
        limit_expr = ""

    if filter_expr or limit_expr:
        with_candidate_root_spans = wrap_str(
            f"""
            with candidate_root_spans as (
                select distinct root_span_id
                from {tblname}
                where {object_filter_expr}{f" and {filter_expr}" if filter_expr else ""}
                {limit_expr}
            )
        """
        )
        join_candidate_root_spans = "join candidate_root_spans using (root_span_id)"
    else:
        with_candidate_root_spans = ""
        join_candidate_root_spans = ""

    cols = ["object_type", "object_id", "root_span_id", "id"]
    if params.get("include_full_data"):
        cols.append("data")

    return wrap_str(
        f"""
        {with_candidate_root_spans}
        select {', '.join(cols)}
        from {tblname} {join_candidate_root_spans}
        where {object_filter_expr}
    """
    )
