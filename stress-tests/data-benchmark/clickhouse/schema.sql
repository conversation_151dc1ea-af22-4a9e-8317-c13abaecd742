SET allow_experimental_inverted_index = true;

create table logs (
  data text

  , object_type text
  , object_id text
  , root_span_id text
  , id text
)
ENGINE = MergeTree()
PRIMARY KEY (object_type, object_id, root_span_id, id)
SETTINGS storage_policy = 's3_main';

ALTER TABLE logs ADD COLUMN scores_map
  Map(LowCardinality(String), Nullable(Float64))
  MATERIALIZED JSONExtract(data, 'scores', 'Map(LowCardinality(String), Nullable(Float64))');
ALTER TABLE logs MATERIALIZE COLUMN scores_map;

-- -- In testing this seems to have no effect.
-- <PERSON>TE<PERSON> TABLE logs ADD INDEX data_inv_index0(data) TYPE inverted(0) GRANULARITY 1;
-- ALTER TABLE logs MATERIALIZE INDEX data_inv_index0;
