#!/bin/bash

set -ex

time curl -X POST http://localhost:4000/index/delete \
  -H "Content-Type: application/json" \
  --data '@-' <<'EOF'
{"object_ids":["project_logs:6536aee3-f265-4bfd-8a51-05e6fcaced36"]}
EOF

time curl -X POST http://localhost:4000/wal/process \
  --json '{"start_sequence_id":337189950,"end_sequence_id":337290579,"object_ids":["project_logs:6536aee3-f265-4bfd-8a51-05e6fcaced36"],"max_num_wal_transactions":100630}'

time curl -X POST http://localhost:4000/wal/compact \
  --json '{"object_id":"project_logs:6536aee3-f265-4bfd-8a51-05e6fcaced36", "all": true}'

time curl -X POST http://localhost:4000/index/merge \
  --json '{"object_id":"project_logs:6536aee3-f265-4bfd-8a51-05e6fcaced36", "all": true}'

# Define queries in an array
queries=(
  "measures: count(1) | from: project_logs('6536aee3-f265-4bfd-8a51-05e6fcaced36')"
  "select: id | from: project_logs('6536aee3-f265-4bfd-8a51-05e6fcaced36') | sort: _pagination_key desc | limit: 10"
  "select: * | from: project_logs('6536aee3-f265-4bfd-8a51-05e6fcaced36') | sort: _pagination_key desc | limit: 10"
  "select: * | from: project_logs('6536aee3-f265-4bfd-8a51-05e6fcaced36') | sort: _pagination_key desc | filter: input MATCH 'apple' | limit: 10"
  "dimensions: input.some_json.war | measures: count(1) |  from: project_logs('6536aee3-f265-4bfd-8a51-05e6fcaced36')"
  "dimensions: metadata.high_cardinality_string | measures: count(1) |  from: project_logs('6536aee3-f265-4bfd-8a51-05e6fcaced36')"
)

# Loop through each query
for query_index in "${!queries[@]}"; do
  query="${queries[$query_index]}"
  echo -e "\nRunning Query $((query_index + 1)): $query"

  total_time=0
  for i in {1..5}; do
    start_time=$(date +%s.%N)
    if ! curl -s -X POST http://localhost:4000/btql/query \
      --json "{\"query\":\"$query\"}" > /dev/null; then
      echo -e "\e[31mERROR: Query failed on run $i\e[0m" >&2
      exit 1
    fi
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    total_time=$(echo "$total_time + $duration" | bc)
    echo "Run $i took $duration seconds"
  done

  avg_time=$(echo "scale=3; $total_time / 5" | bc)
  echo "Average time for Query $((query_index + 1)): $avg_time seconds"
done
