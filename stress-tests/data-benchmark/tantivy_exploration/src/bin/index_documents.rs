use clap::Parser;
use std::{
    fs::File,
    io::{<PERSON><PERSON><PERSON><PERSON>, BufReader},
    path::PathBuf,
};
use tantivy::{schema::TantivyDocument, IndexWriter};
use tantivy_exploration::*;

#[derive(Parser, Debug)]
#[command(version, about)]
struct Args {
    /// Path to the file to index
    file_path: PathBuf,
    #[clap(flatten)]
    index_path: cli_args::IndexPathOptional,
    #[clap(flatten)]
    events_path: cli_args::EventsPath,
}

fn main() {
    let args = Args::parse();

    let default_index_dir: Option<PathBuf>;
    let index_path = match args.index_path.index_path.as_ref() {
        Some(index_path) => index_path,
        None => {
            let dir = util::get_unique_dir();
            std::fs::create_dir_all(dir.as_path()).unwrap();
            default_index_dir = Some(dir);
            default_index_dir.as_ref().unwrap()
        }
    };
    eprintln!(
        "Adding documents from file: {:?} to index at {:?}",
        args.file_path, index_path
    );

    let start_time = std::time::Instant::now();

    let directory = util::make_wrapped_directory(
        Box::new(tantivy::directory::MmapDirectory::open(index_path).unwrap()),
        &args.events_path.events_path,
    );
    let events = directory.events.clone();

    let index = tantivy::Index::open_or_create(directory, util::build_schema()).unwrap();
    let mut index_writer: IndexWriter = index.writer(500_000_000).unwrap();
    index_writer.set_merge_policy(Box::new(tantivy::merge_policy::LogMergePolicy::default()));
    let schema_data_field = index.schema().get_field("data").unwrap();

    let file = File::open(args.file_path).unwrap();
    let reader = BufReader::new(file);
    let mut line_count = 0;
    for line in reader.lines() {
        match line {
            Ok(content) => {
                let mut doc = TantivyDocument::default();
                doc.add_field_value(
                    schema_data_field,
                    util::parse_json_to_tantivy_owned(&content).unwrap(),
                );
                index_writer.add_document(doc).unwrap();

                line_count += 1;
                if line_count % 100 == 0 {
                    println!("Processed {} lines", line_count);
                }
            }
            Err(e) => eprintln!("Error reading line: {}", e),
        }
    }
    index_writer.commit().unwrap();
    index_writer.wait_merging_threads().unwrap();

    let duration = start_time.elapsed();
    println!("Success! Time elapsed: {:?}", duration);
    if let Some(events) = events {
        events.dump_json();
    }
}
