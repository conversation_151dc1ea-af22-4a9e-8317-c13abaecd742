use clap::Parser;
use tantivy::{collector::TopDocs, directory::MmapDirectory, query::QueryParser, Index};
use tantivy_exploration::*;

#[derive(Parser, Debug)]
#[command(version, about)]
struct Args {
    #[clap(flatten)]
    index_path: cli_args::IndexPathRequired,
    #[clap(flatten)]
    events_path: cli_args::EventsPath,
    #[clap(flatten)]
    query: cli_args::Query,
}

fn main() {
    let args = Args::parse();

    let directory = util::make_wrapped_directory(
        Box::new(MmapDirectory::open(args.index_path.index_path).unwrap()),
        &args.events_path.events_path,
    );
    let events = directory.events.clone();

    let start_time = std::time::Instant::now();

    let index = Index::open(directory).unwrap();
    let schema = index.schema();
    let data_field = schema.get_field("data").unwrap();

    let reader = index.reader().unwrap();
    let searcher = reader.searcher();

    let query_parser = QueryParser::for_index(&index, vec![data_field]);
    let query = query_parser.parse_query(&args.query.query).unwrap();

    let top_docs = searcher.search(&query, &TopDocs::with_limit(10)).unwrap();

    let duration = start_time.elapsed();
    println!("Found documents in {:?}:\n{:?}", duration, top_docs);
    if let Some(events) = events {
        events.dump_json();
    }
}
