use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    io,
    ops::Range,
    path::{Path, PathBuf},
    sync::{Arc, Mutex},
};
use tantivy::{
    directory::{
        error::{DeleteError, LockError, OpenReadError, OpenWriteError},
        Directory, FileHandle, OwnedBytes, WatchCallback, WatchHandle, WritePtr,
    },
    HasLen,
};

#[derive(<PERSON>lone, Debug)]
pub struct WrappedDirectory {
    directory: Box<dyn Directory>,
    pub events: Option<DirectoryEvents>,
    metadata: WrappedDirectoryMetadata,
}

impl WrappedDirectory {
    pub fn open(
        directory: Box<dyn Directory>,
        events: Option<DirectoryEvents>,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let mut metadata = WrappedDirectoryMetadata::default();
        let meta_file_path = Path::new(".wrapped_directory_meta.json");
        if directory.exists(meta_file_path)? {
            let contents = directory.atomic_read(meta_file_path)?;
            metadata = serde_json::from_slice(&contents)?;
        }
        Ok(Self {
            directory,
            events,
            metadata,
        })
    }

    fn add_event(&self, event: DirectoryEvent) {
        if let Some(events) = self.events.as_ref() {
            events.add_event(event);
        }
    }
}

impl Directory for WrappedDirectory {
    fn get_file_handle(&self, orig_path: &Path) -> Result<Arc<dyn FileHandle>, OpenReadError> {
        let path = self.metadata.remap_path(orig_path);
        self.add_event(DirectoryEvent::GetFileHandle {
            path: path.to_path_buf(),
        });
        Ok(Arc::new(WrappedFileHandle {
            handle: self.directory.get_file_handle(path)?,
            path: path.to_path_buf(),
            events: self.events.clone(),
        }))
    }

    fn delete(&self, orig_path: &Path) -> Result<(), DeleteError> {
        let path = self.metadata.remap_path(orig_path);
        self.add_event(DirectoryEvent::Delete {
            path: path.to_path_buf(),
        });
        self.directory.delete(path)
    }

    fn exists(&self, orig_path: &Path) -> Result<bool, OpenReadError> {
        let path = self.metadata.remap_path(orig_path);
        self.add_event(DirectoryEvent::Exists {
            path: path.to_path_buf(),
        });
        self.directory.exists(path)
    }

    fn open_write(&self, orig_path: &Path) -> Result<WritePtr, OpenWriteError> {
        let path = self.metadata.remap_path(orig_path);
        self.add_event(DirectoryEvent::OpenWrite {
            path: path.to_path_buf(),
        });
        self.directory.open_write(path)
    }

    fn atomic_read(&self, orig_path: &Path) -> Result<Vec<u8>, OpenReadError> {
        let path = self.metadata.remap_path(orig_path);
        let res = self.directory.atomic_read(path);
        if let Ok(content) = &res {
            self.add_event(DirectoryEvent::AtomicRead {
                path: path.to_path_buf(),
                file_len: content.len(),
            });
        };
        res
    }

    fn atomic_write(&self, orig_path: &Path, content: &[u8]) -> io::Result<()> {
        let path = self.metadata.remap_path(orig_path);
        self.add_event(DirectoryEvent::AtomicWrite {
            path: path.to_path_buf(),
        });
        self.directory.atomic_write(path, content)
    }

    fn acquire_lock(
        &self,
        lock: &tantivy::directory::Lock,
    ) -> Result<tantivy::directory::DirectoryLock, LockError> {
        let path = self.metadata.remap_path(lock.filepath.as_path());
        self.add_event(DirectoryEvent::AcquireLock {
            path: path.to_path_buf(),
        });
        self.directory.acquire_lock(lock)
    }

    fn watch(&self, watch_callback: WatchCallback) -> tantivy::Result<WatchHandle> {
        self.add_event(DirectoryEvent::Watch);
        self.directory.watch(watch_callback)
    }

    fn sync_directory(&self) -> Result<(), io::Error> {
        self.add_event(DirectoryEvent::SyncDirectory);
        self.directory.sync_directory()
    }
}

#[derive(Debug)]
pub struct WrappedFileHandle {
    handle: Arc<dyn FileHandle>,
    path: PathBuf,
    events: Option<DirectoryEvents>,
}

impl HasLen for WrappedFileHandle {
    fn len(&self) -> usize {
        self.handle.len()
    }
}

impl FileHandle for WrappedFileHandle {
    fn read_bytes(&self, range: Range<usize>) -> io::Result<OwnedBytes> {
        if let Some(events) = self.events.as_ref() {
            events.add_event(DirectoryEvent::ReadBytes {
                path: self.path.clone(),
                start: range.start,
                end: range.end,
            });
        }
        self.handle.read_bytes(range)
    }
}

#[derive(Clone, Serialize, Deserialize, Debug)]
pub enum DirectoryEvent {
    GetFileHandle {
        path: PathBuf,
    },
    ReadBytes {
        path: PathBuf,
        start: usize,
        end: usize,
    },
    Delete {
        path: PathBuf,
    },
    Exists {
        path: PathBuf,
    },
    OpenWrite {
        path: PathBuf,
    },
    AtomicRead {
        path: PathBuf,
        file_len: usize,
    },
    AtomicWrite {
        path: PathBuf,
    },
    AcquireLock {
        path: PathBuf,
    },
    Watch,
    SyncDirectory,
}

#[derive(Clone, Serialize, Deserialize, Debug)]
pub struct DirectoryEventEntry {
    pub timestamp: f64,
    pub event: DirectoryEvent,
}

#[derive(Clone, Debug, Default)]
pub struct DirectoryEvents {
    pub path: PathBuf,
    events: Arc<Mutex<Vec<DirectoryEventEntry>>>,
}

impl DirectoryEvents {
    pub fn new(path: PathBuf) -> Self {
        Self {
            path,
            events: Arc::new(Mutex::new(Vec::new())),
        }
    }

    pub fn add_event(&self, event: DirectoryEvent) {
        self.events.lock().unwrap().push(DirectoryEventEntry {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs_f64(),
            event,
        });
    }

    pub fn dump_json(&self) {
        let events = self.events.lock().unwrap();
        let json = serde_json::to_string(&*events).unwrap();
        std::fs::write(self.path.as_path(), json).unwrap();
    }
}

#[derive(Serialize, Deserialize, Default, Clone, Debug)]
struct WrappedDirectoryMetadata {
    #[serde(skip_serializing_if = "Option::is_none")]
    path_map: Option<HashMap<String, String>>,
}

impl WrappedDirectoryMetadata {
    fn remap_path<'a, 'b: 'a>(&'a self, path: &'b Path) -> &'a Path {
        if let Some(path_map) = &self.path_map {
            if let Some(new_path) = path_map.get(path.to_str().unwrap()) {
                return Path::new(new_path);
            }
        }
        path
    }
}
