use clap::Args;
use std::path::PathBuf;

#[derive(Args, Debug)]
pub struct IndexPathRequired {
    /// Path to the index directory
    #[arg(long)]
    pub index_path: PathBuf,
}

#[derive(Args, Debug)]
pub struct IndexPathOptional {
    /// Path to the index directory. If not specified, a new index will be generated.
    #[arg(long)]
    pub index_path: Option<PathBuf>,
}

#[derive(Args, Debug)]
pub struct EventsPath {
    /// Path to dump directory events to. If not specified, will not dump events
    #[arg(long)]
    pub events_path: Option<PathBuf>,
}

#[derive(Args, Debug)]
pub struct Query {
    /// Search query string
    #[arg(long)]
    pub query: String,
}
