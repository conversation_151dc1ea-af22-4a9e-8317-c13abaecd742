use crate::wrapped_directory::{DirectoryEvents, WrappedDirectory};
use std::path::{Path, PathBuf};
use tantivy::schema::{
    IndexRecordOption, JsonObjectOptions, OwnedValue, Schema, TextFieldIndexing,
};
use uuid::Uuid;

pub fn get_root_path() -> PathBuf {
    return std::fs::canonicalize(Path::new(file!()).join("../../../")).unwrap();
}

pub fn get_unique_dir() -> PathBuf {
    let root_path = get_root_path();
    return root_path.join(Path::new(&format!(
        "data/tantivy_exploration/{}",
        Uuid::new_v4().hyphenated()
    )));
}

pub fn parse_json_to_tantivy_owned(s: &str) -> serde_json::Result<OwnedValue> {
    serde_json::from_str(s)
}

pub fn build_schema() -> Schema {
    let mut schema_builder = Schema::builder();
    schema_builder.add_json_field(
        "data",
        JsonObjectOptions::default()
            .set_stored()
            .set_indexing_options(
                TextFieldIndexing::default()
                    .set_tokenizer("default")
                    .set_index_option(IndexRecordOption::WithFreqsAndPositions),
            ),
    );
    schema_builder.build()
}

pub fn make_wrapped_directory(
    directory: Box<dyn tantivy::directory::Directory>,
    events_path: &Option<PathBuf>,
) -> WrappedDirectory {
    let events = events_path
        .as_ref()
        .map(|p| DirectoryEvents::new(p.clone()));
    WrappedDirectory::open(directory, events.clone()).unwrap()
}
