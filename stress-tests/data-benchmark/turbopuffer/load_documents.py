#!/usr/bin/env python3
import argparse
import json
import os
import random
import sys
import time
from uuid import uuid4

import requests
import turbopuffer as tpuf


def upload_data(ns, lines):
    ns.upsert(
        ids=[uuid4().hex for _ in lines],
        vectors=[[random.random(), random.random()] for _ in lines],
        distance_metric="euclidean_squared",
        attributes={
            "text-data": [line["text_data"] for line in lines],
            # "json-data": [line["json_data"]],
        },
        schema={
            "text-data": {
                "type": "?string",
                "bm25": True,
            }
        },
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Upload an data into quickwit",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--index-name", help=f"Name of index to upload to", default="braintrust")
    parser.add_argument("--filepath", required=True, help=f"Path to json file containing data")
    # The max payload size is 10MB.
    parser.add_argument(
        "--batch-size", help=f"Max number of bytes to upload at a time", default=(9 * 1000 * 1000), type=int
    )
    args = parser.parse_args()
    ns = tpuf.Namespace(args.index_name)

    print("Loading data", file=sys.stderr)
    start_time = time.time()
    total_lines = 0
    with open(args.filepath) as f:
        lines = []
        line_len = 0
        for i, line in enumerate(f):
            lines.append(line)
            line_len += len(line)
            if line_len >= args.batch_size:
                print("Uploading lines", i - len(lines) + 1, "to", i + 1, file=sys.stderr)
                lines_transformed = [{"text_data": line, "json_data": json.loads(line)} for line in lines]
                batch_start = time.time()
                upload_data(ns, lines_transformed)
                total_lines += len(lines)

                print(
                    f"Uploaded {len(lines)} lines in {time.time() - batch_start} seconds ({len(lines) / (time.time() - batch_start)} lines/second) (overall {total_lines / (time.time() - start_time)} lines/second)",
                    file=sys.stderr,
                )

                lines = []
                line_len = 0
        if lines:
            print("Uploading lines", i - len(lines) + 1, "to", i + 1, file=sys.stderr)
            upload_data(ns, lines)
    print(f"Loading data finished in {time.time() - start_time} seconds", file=sys.stderr)
