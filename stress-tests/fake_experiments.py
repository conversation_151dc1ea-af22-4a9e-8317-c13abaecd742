import argparse
import math
import random
import sys
import time
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor
from uuid import uuid4

import braintrust
from faker import Faker

fake = Faker()
Faker.seed(1234)
random.seed(1234)


FIELD_TYPES = ["random_number", "text", "list"]


def fake_field_type():
    return random.choice(FIELD_TYPES)


def fake_value(field_type, args):
    if field_type == "random_number":
        return fake.random_number(args.max_number_digits)
    elif field_type == "text":
        return fake.text(args.max_text_chars)
    elif field_type == "list":
        return [fake_value("text", args) for _ in range(random.randint(1, args.max_list_len))]
    else:
        raise Exception(f"Unknown field type {field_type}")


def gen_random_schema(num_schema_keys):
    keynames = set()
    while len(keynames) < num_schema_keys:
        keynames.add(fake.word())

    def add_key(schema):
        choices = ["_NEW_FIELD"] + list(schema.keys())
        choice = random.choice(choices)
        if choice == "_NEW_FIELD":
            word = fake.word()
            while word in schema:
                word = fake.word()
            schema[word] = {}
            return 1
        else:
            delta_keys = 0 if schema[choice] else -1
            return add_key(schema[choice]) + delta_keys

    schema = {}
    num_keys = 0
    while num_keys < num_schema_keys:
        num_keys += add_key(schema)

    def set_field_types(schema):
        for key, val in schema.items():
            if val:
                set_field_types(val)
            else:
                schema[key] = fake_field_type()

    set_field_types(schema)

    return schema


def instantiate_schema(schema, args):
    def helper(schema, cur_obj):
        for key, schema_value in schema.items():
            if type(schema_value) == dict:
                cur_obj[key] = {}
                helper(schema_value, cur_obj[key])
            else:
                cur_obj[key] = fake_value(schema_value, args)

    cur_obj = {}
    helper(schema, cur_obj)
    return cur_obj


def log_all_spans(loggable, inputs, output_schema, metadata_schema, scores_keys, args):
    if args.column_major:
        # In order to exercise the span update logic, we log one field at a time,
        # incrementally, in batches across all rows.
        spans = [loggable.start_span(set_current=False) for _ in inputs]
        for span, input in zip(spans, inputs):
            span.log(input=input)
        for span in spans:
            span.log(output=instantiate_schema(output_schema, args))
        for span in spans:
            span.log(expected=instantiate_schema(output_schema, args))
        for span in spans:
            span.log(scores={k: random.random() for k in scores_keys})
        for span in spans:
            span.log(metadata=instantiate_schema(metadata_schema, args))
        for span in spans:
            span.close()
    else:
        for input in inputs:
            with loggable.start_span(set_current=False) as span:
                span.log(input=input)
                span.log(output=instantiate_schema(output_schema, args))
                span.log(expected=instantiate_schema(output_schema, args))
                span.log(scores={k: random.random() for k in scores_keys})
                span.log(metadata=instantiate_schema(metadata_schema, args))


def run_experiment_helper(inputs, output_schema, metadata_schema, scores_keys, random_seed, args):
    random.seed(random_seed)
    experiment = braintrust.init(project=args.project_name)
    log_all_spans(experiment, inputs, output_schema, metadata_schema, scores_keys, args)
    experiment.flush()


def run_logging_helper(inputs, output_schema, metadata_schema, scores_keys, random_seed, args):
    random.seed(random_seed)
    logger = braintrust.init_logger(project=args.project_name)
    log_all_spans(logger, inputs, output_schema, metadata_schema, scores_keys, args)
    logger.flush()


def run_experiment(kwargs):
    run_experiment_helper(**kwargs)


def run_logging(kwargs):
    run_logging_helper(**kwargs)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="FakeExperimentStressTest",
        description="Stress test for generating multiple experiments of customizable sizes",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    # Data size arguments.
    parser.add_argument(
        "--max_text_chars", type=int, default=100, help="Maximum size of randomly-generated text blobs"
    )
    parser.add_argument(
        "--max_number_digits", type=int, default=10, help="Maximum number of digits in randomly-generated numbers"
    )
    parser.add_argument("--max_list_len", type=int, default=5, help="Maximum size of randomly-generated lists")

    # Schema size arguments.
    parser.add_argument(
        "--input_schema_num_keys", type=int, default=25, help="Number of keys in the input object schema"
    )
    parser.add_argument(
        "--output_schema_num_keys", type=int, default=10, help="Number of keys in the output/expected object schemas"
    )
    parser.add_argument(
        "--metadata_schema_num_keys", type=int, default=15, help="Number of keys in the metadata object schema"
    )
    parser.add_argument("--num_scores", type=int, default=10, help="Number of scores in the scores schema")

    # Project arguments.
    parser.add_argument(
        "--project_name",
        type=str,
        default=f"fake_experiments_{str(uuid4())[:8]}",
        help="Name of project containing stress-test experiments",
    )
    parser.add_argument("--num_experiments", type=int, default=50, help="Number of experiments to log in the project")
    parser.add_argument(
        "--num_rows_per_experiment", type=int, default=100, help="Number of experiments to log in the project"
    )
    parser.add_argument(
        "--use_logging", action="store_true", help="Whether to use production logs instead of experiments"
    )

    # Execution args.
    parser.add_argument(
        "--num_procs",
        type=int,
        default=1,
        help="Number of concurrent processes to log over.",
    )
    parser.add_argument(
        "--column-major",
        action="store_true",
        help="If specified, log data in column-major order (all inputs, all outputs, etc), rather than everything per row",
    )

    args = parser.parse_args()

    # Generate schemas
    input_schema = gen_random_schema(args.input_schema_num_keys)
    output_schema = gen_random_schema(args.output_schema_num_keys)
    metadata_schema = gen_random_schema(args.metadata_schema_num_keys)
    scores_keys = set()
    while len(scores_keys) < args.num_scores:
        scores_keys.add(fake.word())
    scores_keys = list(scores_keys)

    # Generate input rows
    print("Generating inputs")
    start_time = time.time()
    inputs = [instantiate_schema(input_schema, args) for _ in range(args.num_rows_per_experiment)]
    print(f"Took {time.time() - start_time} seconds to generate inputs")

    # Map over the number of experiments.
    print(
        f"Running {args.num_experiments} experiments, each with {args.num_rows_per_experiment} rows, inside project {args.project_name}"
    )
    start_time = time.time()
    with ProcessPoolExecutor(args.num_procs) as executor:
        tasks = []
        for i in range(args.num_experiments):
            tasks.append(
                executor.submit(
                    run_logging if args.use_logging else run_experiment,
                    dict(
                        inputs=inputs,
                        output_schema=output_schema,
                        metadata_schema=metadata_schema,
                        scores_keys=scores_keys,
                        random_seed=int(start_time) + i,
                        args=args,
                    ),
                )
            )
        num_completed_tasks = 0
        while num_completed_tasks < len(tasks):
            new_num_completed_tasks = sum(t.done() for t in tasks)
            if new_num_completed_tasks > num_completed_tasks:
                print(f"Completed {new_num_completed_tasks} out of {len(tasks)} experiments", file=sys.stderr)
                num_completed_tasks = new_num_completed_tasks
        for task in tasks:
            task.result()
    print(f"Completed logging in {time.time() - start_time} seconds", file=sys.stderr)
