import { init } from "braintrust";

const NUM_EXPERIMENTS = 1;
const NUM_RECORDS_PER_EXPERIMENT = 5000;

(async () => {
  await Promise.all(
    Array.from({ length: NUM_EXPERIMENTS }).map(async (_, i) => {
      const experiment = init("parallel-repro");
      console.log(
        `Created experiment number ${i} name ${experiment.name} id ${experiment.id}`,
      );
      await Promise.all(
        Array.from({ length: NUM_RECORDS_PER_EXPERIMENT }).map(async () =>
          experiment.log({
            input: BIG_DOC,
            output: BIG_DOC,
            expected: BIG_DOC,
            metadata: BIG_DOC,
            scores: { foo: 1 },
          }),
        ),
      );

      console.log("DONE WRITING", i);

      // XXX summarizeScores: true returns a different timeout: supabase
      console.log(await experiment.summarize({ summarizeScores: false }));
    }),
  );
})();

const BIG_DOC = {
  input: {
    givesMap: null,
    needsMap: null,
    prompt: "Space fire also because.",
    userContext: {
      apps: null,
      company_size: null,
      priority_apps: [
        { app: "Visit.", priority: 0 },
        { app: "Week.", priority: 1 },
        { app: "Because.", priority: 0 },
        { app: "Will better.", priority: 0 },
        { app: "Huge.", priority: 0 },
        { app: "Decision around create.", priority: 1 },
        { app: "Open.", priority: 0 },
        { app: "Appear.", priority: 91296177 },
        { app: "First.", priority: 47380493 },
        { app: "Now.", priority: 12648282 },
        { app: "High.", priority: 89479562 },
        { app: "Year.", priority: 31232534 },
        { app: "Push.", priority: 15496686 },
        { app: "Large.", priority: 2640091 },
        { app: "Pull.", priority: 1938280 },
      ],
      role: null,
    },
  },
  output:
    "Generation have part thousand. Admit service medical light group minute.\nSeat know health natural room.\nCulture want price teach. Act child move consider should. Down bed senior agency American consider.\nMean whose pressure from offer.\nBad late radio argue plant. Sea act structure property president public.\nChange view near drug best during unit. Administration do mention.",
  expected: [
    {
      action: "Fact explain so.",
      app: "All nearly low.",
      id: "Order.",
      params: null,
      steps: null,
      type: "Whom.",
    },
    {
      action: "Cold.",
      app: "Reflect.",
      id: "Write.",
      params: null,
      steps: null,
      type: "Art.",
    },
    {
      action: "Less.",
      app: "Foot grow study.",
      id: "Less.",
      params: null,
      steps: null,
      type: "Even.",
    },
  ],
  scores: {
    StrictZDLOutlineGrader: 1,
    TolerantZDLMappingGrader: null,
    TolerantZDLOutlineGrader: 0.2282589906849033,
  },
  metadata: {
    category: "Whom focus fire.",
    original_gr_id: null,
    original_zap_id: null,
    original_zdl: null,
    priority: "Good.",
    prompt_specificity: null,
    rawExpected: {
      action: "Ahead write enjoy.",
      actionLabel: null,
      app: "Mind.",
      appLabel: null,
      id: "Left.",
      score: 0,
      steps: [
        {
          action: "Show good answer.",
          actionLabel: "Firm return.",
          alternatives: null,
          app: "Others expert image.",
          appLabel: "Investment.",
          id: "These.",
          meta: null,
          params: null,
          score: 0.7840455492294042,
          steps: null,
          title: "Example term structure drive environmental.",
          type: "Up.",
        },
        {
          action: "If.",
          actionLabel: "Task democratic.",
          alternatives: null,
          app: "Ground.",
          appLabel: "Concern.",
          id: "Court.",
          meta: null,
          params: null,
          score: 0.043338479398248086,
          steps: null,
          title: "Certainly sound think contain.",
          type: "Other.",
        },
        {
          action: "Matter.",
          actionLabel: "War resource from.",
          alternatives: null,
          app: "Growth affect.",
          appLabel: "Certainly.",
          id: "Like.",
          meta: null,
          params: null,
          score: 0.688598044575461,
          steps: null,
          title: "Simply already TV.",
          type: "Fear.",
        },
      ],
      title: null,
      type: "Pick.",
      zdl_version: "Him.",
    },
    rawInput: {
      givesMap: null,
      needsMap: null,
      prompt: "Present enter speech term tax ever professional.",
      userContext: {
        apps: null,
        company_size: null,
        priority_apps: [
          { app: "Thus.", priority: 0 },
          { app: "Take.", priority: 0 },
          { app: "Of up.", priority: 0 },
          { app: "Child argue.", priority: 1 },
          { app: "Build.", priority: 0 },
          { app: "Matter gas board yourself.", priority: 0 },
          { app: "More.", priority: 1 },
          { app: "Probably.", priority: 77358444 },
          { app: "See between.", priority: 41535498 },
          { app: "Author.", priority: 91728479 },
          { app: "Phone.", priority: 43022935 },
          { app: "Majority.", priority: 98719410 },
          { app: "Stay.", priority: 18716884 },
          { app: "News.", priority: 47836787 },
          { app: "Week.", priority: 73241716 },
        ],
        role: null,
      },
    },
    rawOutput: {
      action: "Arm series.",
      app: "View.",
      id: "Join.",
      message: null,
      score: 0,
      stack: null,
      steps: [
        {
          action: "Full audience.",
          actionLabel: "Community quite.",
          alternatives: [
            {
              action: "When political.",
              actionLabel: "More meeting born.",
              app: "Candidate together her.",
              appLabel: "Information.",
              score: 0.9714556881233845,
              type: "Red.",
            },
            {
              action: "Big first.",
              actionLabel: "Two traditional case.",
              app: "Almost friend store you.",
              appLabel: "Great cover.",
              score: 0.007878394384302267,
              type: "See.",
            },
            {
              action: "Computer race.",
              actionLabel: "Piece data easy west hot.",
              app: "Family generation box.",
              appLabel: "North.",
              score: 0.44685074043474515,
              type: "Loss.",
            },
            {
              action: "Yeah policy.",
              actionLabel: "Several.",
              app: "Theory act personal.",
              appLabel: "Stop.",
              score: 0.1070553951249581,
              type: "Ball.",
            },
            {
              action: "Hear.",
              actionLabel: "Set local.",
              app: "Republican.",
              appLabel: "Again line.",
              score: 0.02217291418498102,
              type: "Then.",
            },
            {
              action: "Movement avoid.",
              actionLabel: "Little.",
              app: "Situation way.",
              appLabel: "Total.",
              score: 0.49500877414207933,
              type: "So.",
            },
            {
              action: "Area.",
              actionLabel: "Upon.",
              app: "Today travel.",
              appLabel: "Ago hour.",
              score: 0.976855309147992,
              type: "Left.",
            },
            {
              action: "Think.",
              actionLabel: "Bit past.",
              app: "Next cut method.",
              appLabel: "North gas.",
              score: 0.9103184450089677,
              type: "Fact.",
            },
            {
              action: "Fall.",
              actionLabel: "Whether foot some since.",
              app: "Growth next.",
              appLabel: "Off he.",
              score: 0.05369350332306366,
              type: "Sing.",
            },
            {
              action: "Age.",
              actionLabel: "Read wife.",
              app: "Voice behavior.",
              appLabel: "World.",
              score: 0.08215007642880334,
              type: "Loss.",
            },
          ],
          app: "Southern world.",
          appLabel: "See.",
          id: "Stock.",
          params: {
            Company: null,
            Email: null,
            FirstName: null,
            Phone1: null,
            add_edit_link: null,
            as_bot: null,
            assignee_type: null,
            assistant_name: null,
            author: null,
            blockType: null,
            body: null,
            bodyFormat: null,
            body_type: null,
            card_pos: null,
            comment_format: null,
            content: null,
            content_format: null,
            date_string: null,
            desc: null,
            description: null,
            doc_completion_required: null,
            document_file: null,
            dow: null,
            due: null,
            due_date: null,
            email: null,
            file: null,
            filter_criteria: null,
            first_comment: null,
            first_comment_public: null,
            haltIfAssociationsErrors: null,
            hod: null,
            icon: null,
            importance: null,
            includeFile: null,
            include_completed_tasks: null,
            input: null,
            key: null,
            keyword: null,
            label: null,
            link_names: null,
            link_url: null,
            listen_for_bots: null,
            lookup_value: null,
            max_tokens: null,
            memo_description: null,
            memory_key: null,
            message: null,
            name: null,
            note: null,
            notes: null,
            plain_text: null,
            priority: null,
            query: null,
            raw: null,
            reply_broadcast: null,
            searchByValue: null,
            send_multi: null,
            skipValidation: null,
            source: null,
            split_message: null,
            status: null,
            subject: null,
            submittedAt: null,
            system_message: null,
            temperature: null,
            text: null,
            title: null,
            top_p: null,
            trigger_style: null,
            type: null,
            unfurl: null,
            unique: null,
            units: null,
            update_existing: null,
            url: null,
            user_message: null,
            user_name: null,
            username: null,
            zap_id: null,
            zap_step_id: null,
          },
          score: 0.36765973526761986,
          title: "Appear wife grow step participant.",
          type: "For.",
        },
        {
          action: "Those.",
          actionLabel: "Five.",
          alternatives: [
            {
              action: "Cup series during.",
              actionLabel: "Just.",
              app: "Step later day list east.",
              appLabel: "Professor gun.",
              score: 0.5234227583798817,
              type: "Just.",
            },
            {
              action: "List someone or.",
              actionLabel: "City.",
              app: "However meet image.",
              appLabel: "Western reveal.",
              score: 0.7256346194262101,
              type: "Key.",
            },
            {
              action: "Reach represent.",
              actionLabel: "Would agree accept very.",
              app: "Bag partner.",
              appLabel: "In pull.",
              score: 0.829646139023064,
              type: "Head.",
            },
            {
              action: "Pass per.",
              actionLabel: "Century main.",
              app: "Sing remember hope six.",
              appLabel: "Window up world he.",
              score: 0.7688674787286708,
              type: "Us.",
            },
            {
              action: "Base machine.",
              actionLabel: "Trouble green.",
              app: "Factor hold why theory.",
              appLabel: "Write operation.",
              score: 0.12828536889956255,
              type: "End.",
            },
            {
              action: "Wife majority.",
              actionLabel: "Whether give.",
              app: "Road.",
              appLabel: "During player.",
              score: 0.6450479894436549,
              type: "Four.",
            },
            {
              action: "Bit.",
              actionLabel: "Born.",
              app: "Happen.",
              appLabel: "Daughter weight.",
              score: 0.6691583957600734,
              type: "Will.",
            },
            {
              action: "Appear.",
              actionLabel: "Speak.",
              app: "Poor.",
              appLabel: "Office.",
              score: 0.9814626718675327,
              type: "Guy.",
            },
            {
              action: "Line.",
              actionLabel: "To.",
              app: "Ability.",
              appLabel: "Too respond add.",
              score: 0.17148860541093136,
              type: "Pay.",
            },
            {
              action: "Impact whatever company.",
              actionLabel: "Community.",
              app: "Million live spring mission.",
              appLabel: "Could card suddenly.",
              score: 0.2937238636777497,
              type: "Want.",
            },
          ],
          app: "Authority while smile.",
          appLabel: "Culture money.",
          id: "Test.",
          params: {
            Company: null,
            Email: null,
            FirstName: null,
            Phone1: null,
            add_edit_link: null,
            as_bot: null,
            assignee_type: null,
            assistant_name: null,
            author: null,
            blockType: null,
            body: null,
            bodyFormat: null,
            body_type: null,
            card_pos: null,
            comment_format: null,
            content: null,
            content_format: null,
            date_string: null,
            desc: null,
            description: null,
            doc_completion_required: null,
            document_file: null,
            dow: null,
            due: null,
            due_date: null,
            email: null,
            file: null,
            filter_criteria: null,
            first_comment: null,
            first_comment_public: null,
            haltIfAssociationsErrors: null,
            hod: null,
            icon: null,
            importance: null,
            includeFile: null,
            include_completed_tasks: null,
            input: null,
            key: null,
            keyword: null,
            label: null,
            link_names: null,
            link_url: null,
            listen_for_bots: null,
            lookup_value: null,
            max_tokens: null,
            memo_description: null,
            memory_key: null,
            message: null,
            name: null,
            note: null,
            notes: null,
            plain_text: null,
            priority: null,
            query: null,
            raw: null,
            reply_broadcast: null,
            searchByValue: null,
            send_multi: null,
            skipValidation: null,
            source: null,
            split_message: null,
            status: null,
            subject: null,
            submittedAt: null,
            system_message: null,
            temperature: null,
            text: null,
            title: null,
            top_p: null,
            trigger_style: null,
            type: null,
            unfurl: null,
            unique: null,
            units: null,
            update_existing: null,
            url: null,
            user_message: null,
            user_name: null,
            username: null,
            zap_id: null,
            zap_step_id: null,
          },
          score: 0.3723080799680506,
          title: "During follow statement hot.",
          type: "My.",
        },
        {
          action: "Same same.",
          actionLabel: "Figure.",
          alternatives: [
            {
              action: "Red degree main.",
              actionLabel: "Full threat vote.",
              app: "I large believe.",
              appLabel: "Write.",
              score: 0.4957023366656913,
              type: "Draw.",
            },
            {
              action: "Art consider.",
              actionLabel: "Or election share.",
              app: "Heart.",
              appLabel: "Bed.",
              score: 0.9122834709835387,
              type: "Seven.",
            },
            {
              action: "Learn.",
              actionLabel: "Yard upon.",
              app: "Prevent.",
              appLabel: "Accept.",
              score: 0.04160742516225702,
              type: "Right.",
            },
            {
              action: "Carry alone feel pay.",
              actionLabel: "Stock role month action.",
              app: "Read.",
              appLabel: "Home.",
              score: 0.22493622652045053,
              type: "Both.",
            },
            {
              action: "Join blood.",
              actionLabel: "Late.",
              app: "Include PM reveal.",
              appLabel: "Type.",
              score: 0.18529246278134526,
              type: "Woman.",
            },
            {
              action: "Family party best.",
              actionLabel: "Put billion ready.",
              app: "Require.",
              appLabel: "Letter.",
              score: 0.6344629534306262,
              type: "Animal.",
            },
            {
              action: "Two.",
              actionLabel: "Exist.",
              app: "Natural program staff.",
              appLabel: "Factor.",
              score: 0.017207003857283465,
              type: "Movie.",
            },
            {
              action: "Woman manage later.",
              actionLabel: "Property sea past.",
              app: "Strong contain.",
              appLabel: "Lay.",
              score: 0.5858105386003524,
              type: "Per.",
            },
            {
              action: "There.",
              actionLabel: "Recently.",
              app: "Now nature open.",
              appLabel: "Bag.",
              score: 0.38108302899132074,
              type: "Treat.",
            },
            {
              action: "Ball.",
              actionLabel: "Stock.",
              app: "Child example prove.",
              appLabel: "High policy.",
              score: 0.6275907655044924,
              type: "Hear.",
            },
          ],
          app: "Claim term realize.",
          appLabel: "Let.",
          id: "On.",
          params: {
            Company: null,
            Email: null,
            FirstName: null,
            Phone1: null,
            add_edit_link: null,
            as_bot: null,
            assignee_type: null,
            assistant_name: null,
            author: null,
            blockType: null,
            body: null,
            bodyFormat: null,
            body_type: null,
            card_pos: null,
            comment_format: null,
            content: null,
            content_format: null,
            date_string: null,
            desc: null,
            description: null,
            doc_completion_required: null,
            document_file: null,
            dow: null,
            due: null,
            due_date: null,
            email: null,
            file: null,
            filter_criteria: null,
            first_comment: null,
            first_comment_public: null,
            haltIfAssociationsErrors: null,
            hod: null,
            icon: null,
            importance: null,
            includeFile: null,
            include_completed_tasks: null,
            input: null,
            key: null,
            keyword: null,
            label: null,
            link_names: null,
            link_url: null,
            listen_for_bots: null,
            lookup_value: null,
            max_tokens: null,
            memo_description: null,
            memory_key: null,
            message: null,
            name: null,
            note: null,
            notes: null,
            plain_text: null,
            priority: null,
            query: null,
            raw: null,
            reply_broadcast: null,
            searchByValue: null,
            send_multi: null,
            skipValidation: null,
            source: null,
            split_message: null,
            status: null,
            subject: null,
            submittedAt: null,
            system_message: null,
            temperature: null,
            text: null,
            title: null,
            top_p: null,
            trigger_style: null,
            type: null,
            unfurl: null,
            unique: null,
            units: null,
            update_existing: null,
            url: null,
            user_message: null,
            user_name: null,
            username: null,
            zap_id: null,
            zap_step_id: null,
          },
          score: 0.7949942805446805,
          title: "Real up question material.",
          type: "Seem.",
        },
        {
          action: "Board.",
          actionLabel: "Our product require.",
          alternatives: [
            {
              action: "Owner job.",
              actionLabel: "With be through.",
              app: "Believe hotel.",
              appLabel: "Century.",
              score: 0.8650480830224906,
              type: "Hundred second.",
            },
            {
              action: "Huge.",
              actionLabel: "Relate modern may.",
              app: "Democrat beyond.",
              appLabel: "Tell writer.",
              score: 0.5452063522737745,
              type: "Than.",
            },
            {
              action: "Vote.",
              actionLabel: "Health medical arrive.",
              app: "Still admit.",
              appLabel: "Final budget.",
              score: 0.3853633723240094,
              type: "But.",
            },
            {
              action: "Miss the.",
              actionLabel: "Last positive radio.",
              app: "Front home.",
              appLabel: "Condition.",
              score: 0.11967382474655752,
              type: "Sort.",
            },
            {
              action: "Type late.",
              actionLabel: "Determine science exactly keep.",
              app: "Produce keep.",
              appLabel: "Yes.",
              score: 0.2571940700029224,
              type: "Five.",
            },
            {
              action: "Radio meet.",
              actionLabel: "Effort care show.",
              app: "Cause physical.",
              appLabel: "Order from.",
              score: 0.8222476727535145,
              type: "Wall.",
            },
            {
              action: "Pay.",
              actionLabel: "Stage.",
              app: "Appear prove laugh.",
              appLabel: "Still.",
              score: 0.1666779685050277,
              type: "Free.",
            },
            {
              action: "Put.",
              actionLabel: "Mouth.",
              app: "Offer.",
              appLabel: "Behavior admit.",
              score: 0.41102182017365185,
              type: "Case.",
            },
            {
              action: "Yeah.",
              actionLabel: "A control fine.",
              app: "Hold.",
              appLabel: "West economic.",
              score: 0.1616834606045472,
              type: "Exist same.",
            },
            {
              action: "Month.",
              actionLabel: "Off stage.",
              app: "Imagine.",
              appLabel: "Later.",
              score: 0.1302076064562383,
              type: "His.",
            },
          ],
          app: "Herself bed ago.",
          appLabel: "End.",
          id: "Force.",
          params: {
            Company: null,
            Email: null,
            FirstName: null,
            Phone1: null,
            add_edit_link: null,
            as_bot: null,
            assignee_type: null,
            assistant_name: null,
            author: null,
            blockType: null,
            body: null,
            bodyFormat: null,
            body_type: null,
            card_pos: null,
            comment_format: null,
            content: null,
            content_format: null,
            date_string: null,
            desc: null,
            description: null,
            doc_completion_required: null,
            document_file: null,
            dow: null,
            due: null,
            due_date: null,
            email: null,
            file: null,
            filter_criteria: null,
            first_comment: null,
            first_comment_public: null,
            haltIfAssociationsErrors: null,
            hod: null,
            icon: null,
            importance: null,
            includeFile: null,
            include_completed_tasks: null,
            input: null,
            key: null,
            keyword: null,
            label: null,
            link_names: null,
            link_url: null,
            listen_for_bots: null,
            lookup_value: null,
            max_tokens: null,
            memo_description: null,
            memory_key: null,
            message: null,
            name: null,
            note: null,
            notes: null,
            plain_text: null,
            priority: null,
            query: null,
            raw: null,
            reply_broadcast: null,
            searchByValue: null,
            send_multi: null,
            skipValidation: null,
            source: null,
            split_message: null,
            status: null,
            subject: null,
            submittedAt: null,
            system_message: null,
            temperature: null,
            text: null,
            title: null,
            top_p: null,
            trigger_style: null,
            type: null,
            unfurl: null,
            unique: null,
            units: null,
            update_existing: null,
            url: null,
            user_message: null,
            user_name: null,
            username: null,
            zap_id: null,
            zap_step_id: null,
          },
          score: 0.21985771359700967,
          title: "Trade task water purpose amount process least.",
          type: "Mission news.",
        },
      ],
      title: "Science my fast in. Now court reality born town interest.",
      type: "So.",
      zdl_version: "Kind.",
    },
    test: "Collection into.",
  },
};
