# MCP Server Implementation Summary

## Overview

This document summarizes the implementation of a Model Context Protocol (MCP) server with OAuth authentication integration for the Braintrust API service.

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   MCP Client    │────▶│  Express Server │────▶│ OAuth Provider  │
│ (MCP Inspector) │     │   (Port 8000)   │     │ (Internal)      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │  MCP Server     │
                        │ (StreamableHTTP)│
                        └─────────────────┘
```

## Implementation Details

### 1. Transport Layer

**Protocol**: MCP Streamable HTTP Transport (JSON-RPC 2.0)
**Endpoint**: `POST /mcp`
**Transport Class**: `StreamableHTTPServerTransport` from `@modelcontextprotocol/sdk`

Key features:

- Stateless transport with per-request server instances
- Proper Zod validation handling through MCP SDK
- CORS support for browser clients
- Session management (optional)

### 2. OAuth Integration

**Flow**: Authorization Code + PKCE
**Provider**: OAuth2Server library with custom model
**Storage**: In-memory (for development)

Endpoints:

- `GET /oauth/authorize` - Authorization endpoint
- `POST /oauth/token` - Token exchange
- `POST /oauth/register` - Dynamic client registration
- `/.well-known/openid-configuration` - Discovery endpoint

### 3. Available Tools

1. **get_user_info** - Get authenticated user information
2. **list_projects** - List user's projects
3. **create_experiment** - Create a new experiment

All tools require `userId` parameter for OAuth token lookup.

### 4. Security Features

- CORS protection with proper headers
- Origin validation to prevent DNS rebinding
- Public client support (no client secret required)
- Access token validation and expiration checking
- Fallback authentication for unregistered MCP clients

## File Structure

```
src/mcp/
├── mcp-server.ts      # Main MCP server with OAuth integration
└── index.ts           # Express integration utilities

app/app/redirects/mcp/
└── page.tsx           # OAuth callback handler (Next.js)
```

## Key Classes and Methods

### MCPServerWithOAuth

**Core Methods**:

- `setupExpressRoutes(app)` - Configures all OAuth and MCP endpoints
- `registerTool(tool)` - Registers new MCP tools
- `registerDefaultTools()` - Sets up built-in tools
- `handleToolCall(name, args)` - Executes tools with OAuth validation

**OAuth Model**:

- `getClient(clientId, secret)` - Client lookup with static/dynamic support
- `saveToken(token, client, user)` - Token storage
- `getAccessToken(token)` - Token validation

## Configuration

### Environment Variables

- Standard Braintrust API environment (inherited from existing setup)
- OAuth endpoints configured for localhost:8000
- Redirect URI: http://localhost:3001/redirects/mcp

### Dependencies Added

```json
{
  "@modelcontextprotocol/sdk": "^latest",
  "oauth2-server": "^latest",
  "@types/oauth2-server": "^latest",
  "zod": "^latest"
}
```

## Usage Examples

### 1. Testing with cURL

```bash
# Initialize MCP connection
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}}, "id": 1}'

# List available tools
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "tools/list", "id": 2}'
```

### 2. OAuth Flow

1. Client redirects to: `http://localhost:8000/oauth/authorize?response_type=code&client_id=braintrust-mcp&redirect_uri=http://localhost:3001/redirects/mcp`
2. User redirected to Next.js app at localhost:3001/redirects/mcp
3. Next.js app exchanges code for token via POST to localhost:8000/oauth/token
4. Token stored in localStorage for subsequent MCP tool calls

### 3. MCP Inspector Integration

The implementation is designed to work with the official MCP Inspector tool:

- Supports dynamic client registration
- Handles PKCE for secure public clients
- Provides proper discovery endpoints
- Uses stateless StreamableHTTP transport

## Current Status

✅ **Working Components**:

- OAuth 2.0 authorization code flow with PKCE
- Dynamic client registration (RFC 7591)
- MCP StreamableHTTP transport integration
- Tool registration and execution with authentication
- CORS support for browser clients

✅ **Tested With**:

- MCP Inspector (browser-based client)
- curl (command-line testing)
- Custom test clients (Node.js and HTML)

## Troubleshooting

### Common Issues

1. **Zod Validation Errors**: Fixed by using proper MCP SDK StreamableHTTPServerTransport
2. **CORS Issues**: Resolved with comprehensive CORS headers including mcp-protocol-version
3. **OAuth Client Recognition**: Supports both registered and unregistered MCP clients starting with "mcp\_"
4. **Session Management**: Uses stateless transport for scalability

### Debugging

Enable detailed logging by checking console output for:

- OAuth client lookups and registrations
- Token exchanges and validations
- MCP method calls and responses
- Transport connection status

## Future Enhancements

1. **Persistent Storage**: Replace in-memory storage with Redis/Database
2. **Advanced Tools**: Add more Braintrust API integrations
3. **Resource Support**: Implement MCP resources for file/data access
4. **Prompt Templates**: Add MCP prompt template support
5. **Production Security**: Enhanced client authentication and rate limiting
