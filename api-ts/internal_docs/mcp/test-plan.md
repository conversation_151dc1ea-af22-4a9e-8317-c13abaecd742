# MCP Server Test Plan

## Overview

This document outlines the comprehensive test plan for the MCP (Model Context Protocol) server implementation with OAuth authentication. These test cases should be implemented as unit tests, integration tests, and end-to-end tests.

## Test Categories

### 1. OAuth Authentication Tests

#### 1.1 OAuth Client Management

```typescript
describe("OAuth Client Management", () => {
  test("should recognize static client braintrust-mcp", async () => {
    const client = await oauth.getClient("braintrust-mcp", "secret");
    expect(client).toBeDefined();
    expect(client.id).toBe("braintrust-mcp");
    expect(client.grants).toContain("authorization_code");
  });

  test("should handle public clients without secrets", async () => {
    const client = await oauth.getClient("braintrust-mcp", undefined);
    expect(client).toBeDefined();
  });

  test("should reject invalid client credentials", async () => {
    const client = await oauth.getClient("invalid-client", "wrong-secret");
    expect(client).toBeNull();
  });

  test("should support dynamically registered clients", async () => {
    // Register a client first
    const registrationResponse = await request(app)
      .post("/oauth/register")
      .send({
        redirect_uris: ["http://localhost:3001/callback"],
        grant_types: ["authorization_code"],
        token_endpoint_auth_method: "none",
      });

    const clientId = registrationResponse.body.client_id;
    const client = await oauth.getClient(clientId);
    expect(client).toBeDefined();
  });
});
```

#### 1.2 Token Management

```typescript
describe("Token Management", () => {
  test("should save and retrieve access tokens", async () => {
    const mockToken = {
      accessToken: "test-token-123",
      accessTokenExpiresAt: new Date(Date.now() + 3600000),
      refreshToken: "refresh-123",
    };

    const savedToken = await oauth.saveToken(mockToken, mockClient, mockUser);
    expect(savedToken.accessToken).toBe(mockToken.accessToken);

    const retrievedToken = await oauth.getAccessToken("test-token-123");
    expect(retrievedToken).toBeDefined();
    expect(retrievedToken.user.id).toBe(mockUser.id);
  });

  test("should handle token expiration", async () => {
    const expiredToken = {
      accessToken: "expired-token",
      accessTokenExpiresAt: new Date(Date.now() - 1000), // expired
    };

    await oauth.saveToken(expiredToken, mockClient, mockUser);

    // Tool call should fail with expired token
    const result = await mcpServer.handleToolCall("get_user_info", {
      userId: "user-123",
    });
    expect(result).toThrow("Access token expired");
  });

  test("should generate unique access tokens", async () => {
    const token1 = generateAccessToken();
    const token2 = generateAccessToken();
    expect(token1).not.toBe(token2);
    expect(token1).toMatch(/^access_\d+_[a-z0-9]+$/);
  });
});
```

#### 1.3 OAuth Endpoints

```typescript
describe("OAuth Endpoints", () => {
  test("GET /.well-known/openid-configuration", async () => {
    const response = await request(app)
      .get("/.well-known/openid-configuration")
      .expect(200);

    expect(response.body).toEqual({
      issuer: "http://localhost:8000",
      authorization_endpoint: "http://localhost:8000/oauth/authorize",
      token_endpoint: "http://localhost:8000/oauth/token",
      response_types_supported: ["code"],
      grant_types_supported: ["authorization_code"],
      scopes_supported: ["mcp"],
      token_endpoint_auth_methods_supported: [
        "client_secret_post",
        "client_secret_basic",
        "none",
      ],
      code_challenge_methods_supported: ["S256", "plain"],
      registration_endpoint: "http://localhost:8000/oauth/register",
    });
  });

  test("POST /oauth/register should create new client", async () => {
    const response = await request(app)
      .post("/oauth/register")
      .send({
        redirect_uris: ["http://localhost:3001/callback"],
        grant_types: ["authorization_code"],
        response_types: ["code"],
        token_endpoint_auth_method: "none",
      })
      .expect(200);

    expect(response.body.client_id).toMatch(/^mcp_\d+_[a-z0-9]+$/);
    expect(response.body.redirect_uris).toEqual([
      "http://localhost:3001/callback",
    ]);
    expect(response.body.client_secret).toBeUndefined(); // public client
  });

  test("GET /oauth/authorize should redirect with auth code", async () => {
    const response = await request(app)
      .get("/oauth/authorize")
      .query({
        response_type: "code",
        client_id: "braintrust-mcp",
        redirect_uri: "http://localhost:3001/redirects/mcp",
        state: "test-state",
        code_challenge: "test-challenge",
        code_challenge_method: "S256",
      })
      .expect(302);

    const location = response.headers.location;
    expect(location).toContain("http://localhost:3001/redirects/mcp");
    expect(location).toContain("code=");
    expect(location).toContain("state=test-state");
  });

  test("POST /oauth/token should exchange code for token", async () => {
    const response = await request(app)
      .post("/oauth/token")
      .send({
        grant_type: "authorization_code",
        code: "test-code",
        client_id: "braintrust-mcp",
        redirect_uri: "http://localhost:3001/redirects/mcp",
        code_verifier: "test-verifier",
      })
      .expect(200);

    expect(response.body.access_token).toBeDefined();
    expect(response.body.token_type).toBe("Bearer");
    expect(response.body.expires_in).toBe(3600);
    expect(response.body.scope).toBe("mcp");
  });
});
```

### 2. MCP Protocol Tests

#### 2.1 Transport Layer

```typescript
describe("MCP Transport Layer", () => {
  test("should accept StreamableHTTP requests", async () => {
    const response = await request(app)
      .post("/mcp")
      .set("Accept", "application/json, text/event-stream")
      .send({
        jsonrpc: "2.0",
        method: "initialize",
        params: {
          protocolVersion: "2024-11-05",
          capabilities: {},
          clientInfo: { name: "test-client", version: "1.0.0" },
        },
        id: 1,
      })
      .expect(200);

    expect(response.text).toContain("event: message");
    expect(response.text).toContain("protocolVersion");
  });

  test("should reject requests without proper Accept headers", async () => {
    const response = await request(app)
      .post("/mcp")
      .send({ jsonrpc: "2.0", method: "initialize", id: 1 })
      .expect(406);

    expect(response.body.error.message).toContain("Client must accept");
  });

  test("should reject GET requests to /mcp", async () => {
    const response = await request(app).get("/mcp").expect(405);

    expect(response.body.error.code).toBe(-32000);
    expect(response.body.error.message).toBe("Method not allowed.");
  });

  test("should handle CORS preflight requests", async () => {
    const response = await request(app)
      .options("/mcp")
      .set("Origin", "http://localhost:3000")
      .expect(200);

    expect(response.headers["access-control-allow-origin"]).toBe("*");
    expect(response.headers["access-control-allow-methods"]).toContain("POST");
  });
});
```

#### 2.2 MCP Methods

```typescript
describe("MCP Methods", () => {
  test("initialize should return server info and capabilities", async () => {
    const response = await mcpRequest({
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {},
        clientInfo: { name: "test", version: "1.0.0" },
      },
    });

    expect(response.result.protocolVersion).toBe("2024-11-05");
    expect(response.result.serverInfo.name).toBe("braintrust-mcp-server");
    expect(response.result.serverInfo.version).toBe("1.0.0");
    expect(response.result.capabilities.tools).toBeDefined();
  });

  test("tools/list should return available tools", async () => {
    const response = await mcpRequest({ method: "tools/list" });

    expect(response.result.tools).toHaveLength(3);
    expect(response.result.tools[0].name).toBe("get_user_info");
    expect(response.result.tools[1].name).toBe("list_projects");
    expect(response.result.tools[2].name).toBe("create_experiment");

    // Verify tool schemas
    const getUserInfoTool = response.result.tools[0];
    expect(getUserInfoTool.inputSchema.properties.userId).toBeDefined();
    expect(getUserInfoTool.inputSchema.required).toContain("userId");
  });

  test("should reject invalid method names", async () => {
    const response = await mcpRequest({ method: "invalid/method" });

    expect(response.error.code).toBe(-32601);
    expect(response.error.message).toContain("Method not found");
  });

  test("should handle malformed JSON-RPC requests", async () => {
    const response = await request(app)
      .post("/mcp")
      .set("Accept", "application/json, text/event-stream")
      .send("{ invalid json }")
      .expect(400);

    expect(response.body.error.code).toBe(-32700);
    expect(response.body.error.message).toBe("Parse error");
  });
});
```

### 3. Tool Execution Tests

#### 3.1 Authentication Requirements

```typescript
describe("Tool Authentication", () => {
  test("should reject tool calls without userId", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: { name: "get_user_info", arguments: {} },
    });

    expect(response.result.isError).toBe(true);
    expect(response.result.content[0].text).toContain("User ID required");
  });

  test("should reject tool calls for unauthenticated users", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: { name: "get_user_info", arguments: { userId: "unknown-user" } },
    });

    expect(response.result.isError).toBe(true);
    expect(response.result.content[0].text).toContain("User not authenticated");
  });

  test("should reject tool calls with expired tokens", async () => {
    // Setup expired token
    await oauth.saveToken(
      {
        accessToken: "expired-token",
        accessTokenExpiresAt: new Date(Date.now() - 1000),
      },
      mockClient,
      { id: "user-123" },
    );

    const response = await mcpRequest({
      method: "tools/call",
      params: { name: "get_user_info", arguments: { userId: "user-123" } },
    });

    expect(response.result.isError).toBe(true);
    expect(response.result.content[0].text).toContain("Access token expired");
  });
});
```

#### 3.2 Tool Implementation

```typescript
describe("Tool Implementation", () => {
  beforeEach(async () => {
    // Setup valid authentication
    await oauth.saveToken(
      {
        accessToken: "valid-token",
        accessTokenExpiresAt: new Date(Date.now() + 3600000),
      },
      mockClient,
      { id: "user-123" },
    );
  });

  test("get_user_info should return user data", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: { name: "get_user_info", arguments: { userId: "user-123" } },
    });

    expect(response.result.isError).toBeUndefined();
    const userData = JSON.parse(response.result.content[0].text);
    expect(userData.id).toBe("user-123");
    expect(userData.name).toBeDefined();
    expect(userData.email).toBeDefined();
  });

  test("list_projects should return project list", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: { name: "list_projects", arguments: { userId: "user-123" } },
    });

    const projects = JSON.parse(response.result.content[0].text);
    expect(projects.projects).toBeInstanceOf(Array);
    expect(projects.projects.length).toBe(2);
    expect(projects.projects[0]).toHaveProperty("id");
    expect(projects.projects[0]).toHaveProperty("name");
  });

  test("create_experiment should create new experiment", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: {
        name: "create_experiment",
        arguments: {
          userId: "user-123",
          name: "Test Experiment",
          projectId: "proj-1",
        },
      },
    });

    const experiment = JSON.parse(response.result.content[0].text);
    expect(experiment.id).toMatch(/^exp-/);
    expect(experiment.name).toBe("Test Experiment");
    expect(experiment.projectId).toBe("proj-1");
    expect(experiment.created).toBeDefined();
  });

  test("should validate required tool parameters", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: {
        name: "create_experiment",
        arguments: { userId: "user-123" }, // missing name and projectId
      },
    });

    expect(response.result.isError).toBe(true);
    expect(response.result.content[0].text).toContain("Error");
  });
});
```

### 4. Integration Tests

#### 4.1 End-to-End OAuth Flow

```typescript
describe("End-to-End OAuth Flow", () => {
  test("complete OAuth flow with MCP tool execution", async () => {
    // 1. Dynamic client registration
    const registrationResponse = await request(app)
      .post("/oauth/register")
      .send({
        redirect_uris: ["http://localhost:3001/callback"],
        token_endpoint_auth_method: "none",
      });

    const clientId = registrationResponse.body.client_id;

    // 2. Authorization request
    const authResponse = await request(app)
      .get("/oauth/authorize")
      .query({
        response_type: "code",
        client_id: clientId,
        redirect_uri: "http://localhost:3001/callback",
        state: "test-state",
      })
      .expect(302);

    const authCode = extractCodeFromRedirect(authResponse.headers.location);

    // 3. Token exchange
    const tokenResponse = await request(app).post("/oauth/token").send({
      grant_type: "authorization_code",
      code: authCode,
      client_id: clientId,
      redirect_uri: "http://localhost:3001/callback",
    });

    expect(tokenResponse.body.access_token).toBeDefined();

    // 4. Use token for MCP tool call
    const toolResponse = await mcpRequest({
      method: "tools/call",
      params: {
        name: "get_user_info",
        arguments: { userId: "user-123" },
      },
    });

    expect(toolResponse.result.isError).toBeUndefined();
    const userData = JSON.parse(toolResponse.result.content[0].text);
    expect(userData.id).toBe("user-123");
  });
});
```

#### 4.2 CORS and Security Tests

```typescript
describe("CORS and Security", () => {
  test("should allow CORS from any origin for MCP endpoints", async () => {
    const response = await request(app)
      .post("/mcp")
      .set("Origin", "http://evil-site.com")
      .set("Accept", "application/json, text/event-stream")
      .send({ jsonrpc: "2.0", method: "tools/list", id: 1 });

    expect(response.headers["access-control-allow-origin"]).toBe("*");
  });

  test("should validate Origin headers where appropriate", () => {
    // Test DNS rebinding protection if implemented
    // This would be environment-specific
  });

  test("should not expose sensitive data in error messages", async () => {
    const response = await mcpRequest({
      method: "tools/call",
      params: { name: "get_user_info", arguments: { userId: "user-123" } },
    });

    // Even error messages should not expose internal tokens or secrets
    expect(response.result.content[0].text).not.toContain("secret");
    expect(response.result.content[0].text).not.toContain("token");
  });
});
```

### 5. Performance Tests

#### 5.1 Load Testing

```typescript
describe("Performance Tests", () => {
  test("should handle concurrent MCP requests", async () => {
    const requests = Array(10)
      .fill()
      .map((_, i) =>
        mcpRequest({
          method: "tools/list",
          id: i + 1,
        }),
      );

    const responses = await Promise.all(requests);

    responses.forEach((response, i) => {
      expect(response.id).toBe(i + 1);
      expect(response.result.tools).toHaveLength(3);
    });
  });

  test("should handle rapid token exchanges", async () => {
    const promises = Array(5)
      .fill()
      .map(() =>
        request(app).post("/oauth/token").send({
          grant_type: "authorization_code",
          code: "test-code",
          client_id: "braintrust-mcp",
        }),
      );

    const responses = await Promise.all(promises);

    responses.forEach((response) => {
      expect(response.status).toBe(200);
      expect(response.body.access_token).toBeDefined();
    });
  });
});
```

### 6. Error Handling Tests

#### 6.1 Network and Transport Errors

```typescript
describe("Error Handling", () => {
  test("should handle transport connection failures gracefully", async () => {
    // Mock transport failure
    jest
      .spyOn(StreamableHTTPServerTransport.prototype, "handleRequest")
      .mockRejectedValueOnce(new Error("Transport failure"));

    const response = await request(app)
      .post("/mcp")
      .set("Accept", "application/json, text/event-stream")
      .send({ jsonrpc: "2.0", method: "tools/list", id: 1 })
      .expect(500);

    expect(response.body.error.code).toBe(-32603);
    expect(response.body.error.message).toBe("Internal server error");
  });

  test("should handle OAuth server errors", async () => {
    // Mock OAuth error
    jest
      .spyOn(OAuth2Server.prototype, "token")
      .mockRejectedValueOnce(new Error("OAuth error"));

    const response = await request(app)
      .post("/oauth/token")
      .send({
        grant_type: "authorization_code",
        code: "invalid-code",
        client_id: "braintrust-mcp",
      })
      .expect(400);

    expect(response.body.error).toBeDefined();
  });
});
```

## Test Utilities

### Helper Functions

```typescript
// Test utilities to be implemented
const mcpRequest = async (payload) => {
  const response = await request(app)
    .post("/mcp")
    .set("Accept", "application/json, text/event-stream")
    .send({ jsonrpc: "2.0", id: 1, ...payload });

  return parseSSEResponse(response.text);
};

const parseSSEResponse = (sseText) => {
  const lines = sseText.split("\n");
  const dataLine = lines.find((line) => line.startsWith("data: "));
  return JSON.parse(dataLine.replace("data: ", ""));
};

const extractCodeFromRedirect = (location) => {
  const url = new URL(location);
  return url.searchParams.get("code");
};

const mockClient = {
  id: "braintrust-mcp",
  grants: ["authorization_code"],
};

const mockUser = {
  id: "user-123",
};
```

## Test Environment Setup

### Prerequisites

- Jest for unit testing framework
- Supertest for HTTP endpoint testing
- Test database/storage (in-memory for OAuth tokens)
- Mock external API dependencies

### Test Data

- Sample OAuth clients (public and confidential)
- Valid and expired access tokens
- Mock user data and project data
- Sample MCP requests and expected responses

### CI/CD Integration

- Run tests on PR creation and merge
- Generate coverage reports
- Integration with existing Braintrust test suite
- Performance benchmarks and regression testing

## Coverage Goals

- **Unit Tests**: 90%+ code coverage for all core functionality
- **Integration Tests**: Cover all OAuth flows and MCP protocol methods
- **E2E Tests**: Complete user journeys from authentication to tool execution
- **Security Tests**: CORS, authentication bypass, and input validation
- **Performance Tests**: Load testing under realistic usage patterns
