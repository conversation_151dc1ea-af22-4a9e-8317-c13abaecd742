# MCP Server Implementation Context Summary

## Project Context

This is a **Braintrust AI/ML evaluation platform** monorepo using pnpm workspaces. The main API service runs on **Express.js with TypeScript** on port 8000. We've integrated a **Model Context Protocol (MCP) server with OAuth 2.0 authentication** to allow external MCP clients (like MCP Inspector) to securely access Braintrust tools and data.

## Implementation Architecture

```
┌─────────────────────┐    OAuth Flow    ┌─────────────────────┐
│   MCP Inspector     │◄────────────────►│  localhost:3001     │
│  (Browser Client)   │                  │  (Next.js App)      │
└──────────┬──────────┘                  └─────────────────────┘
           │ MCP StreamableHTTP                        │
           │ JSON-RPC 2.0                             │ Token Exchange
           ▼                                          ▼
┌─────────────────────┐                  ┌─────────────────────┐
│   localhost:8000    │◄─────────────────│   OAuth Provider    │
│   (Express API)     │                  │   (Integrated)      │
│   - MCP Endpoint    │                  │   - Authorization   │
│   - OAuth Endpoints │                  │   - Token Exchange  │
│   - CORS Support    │                  │   - Client Reg      │
└─────────────────────┘                  └─────────────────────┘
```

## Key Files and Their Purpose

### Core Implementation

- **`/src/mcp/mcp-server.ts`** - Main implementation file containing:

  - `MCPServerWithOAuth` class - Central coordinator
  - OAuth 2.0 server with PKCE support
  - MCP tool definitions and execution logic
  - Express route handlers for all endpoints
  - StreamableHTTPServerTransport integration

- **`/src/mcp/index.ts`** - Integration utilities for Express app
- **`/src/api.ts`** - Modified to add `setupMCPRoutes(app)` call

### OAuth Callback Handler

- **`/app/app/redirects/mcp/page.tsx`** - Next.js page that:
  - Receives OAuth authorization code
  - Exchanges code for access token via POST to localhost:8000/oauth/token
  - Stores token in localStorage
  - Redirects back to original client

### Documentation

- **`/internal_docs/mcp/implementation-summary.md`** - Comprehensive technical overview
- **`/internal_docs/mcp/test-plan.md`** - Complete test plan for unit/integration tests
- **`/internal_docs/mcp/context-summary.md`** - This file (context for resuming work)

## Technical Stack

### Dependencies Added

```json
{
  "@modelcontextprotocol/sdk": "^latest", // Official MCP SDK
  "oauth2-server": "^latest", // OAuth 2.0 implementation
  "@types/oauth2-server": "^latest", // TypeScript definitions
  "zod": "^latest" // Schema validation (used by MCP SDK)
}
```

### Transport Protocol

- **Protocol**: MCP StreamableHTTP (Server-Sent Events over HTTP)
- **Format**: JSON-RPC 2.0 with SSE streaming
- **Authentication**: OAuth 2.0 Authorization Code + PKCE flow
- **CORS**: Enabled for all origins (development setting)

## Endpoints Implemented

### OAuth Endpoints

- `GET /.well-known/openid-configuration` - OpenID Connect discovery
- `GET /.well-known/oauth-authorization-server` - OAuth 2.0 discovery
- `POST /oauth/register` - Dynamic client registration (RFC 7591)
- `GET /oauth/authorize` - OAuth authorization endpoint
- `POST /oauth/token` - OAuth token exchange endpoint

### MCP Endpoints

- `POST /mcp` - Main MCP StreamableHTTP endpoint
- `GET /mcp` - Returns 405 Method Not Allowed (per spec)
- `OPTIONS /mcp` - CORS preflight handling
- `POST /mcp/tools/:toolName` - Legacy direct tool execution (backward compatibility)

## Available MCP Tools

1. **`get_user_info`**

   - **Purpose**: Get authenticated user information
   - **Parameters**: `userId: string`
   - **Returns**: User object with id, name, email

2. **`list_projects`**

   - **Purpose**: List user's projects
   - **Parameters**: `userId: string`
   - **Returns**: Array of project objects with id, name

3. **`create_experiment`**
   - **Purpose**: Create a new experiment
   - **Parameters**: `userId: string, name: string, projectId: string`
   - **Returns**: Experiment object with id, name, projectId, created timestamp

All tools require OAuth authentication and validate access tokens before execution.

## Authentication Flow

1. **Client Registration**: MCP client registers with `/oauth/register` (optional - static client exists)
2. **Authorization**: Client redirects user to `/oauth/authorize` with PKCE challenge
3. **User Consent**: User approves access (auto-approved for development)
4. **Authorization Code**: Server generates and stores authorization code with PKCE challenge
5. **Callback**: Redirect to `localhost:3001/redirects/mcp` with authorization code
6. **Token Exchange**: Next.js app exchanges code for token via `/oauth/token` (with proper validation)
7. **Token Storage**: Access token stored in browser localStorage
8. **MCP Access**: MCP client includes Bearer token in Authorization header for all operations

## Current Status

### ✅ Working Features

- **Secure OAuth 2.0 Flow**: Complete authorization code + PKCE with proper validation
- **Authorization Code Storage**: Server stores and validates authorization codes with PKCE
- **Selective Authentication**: `initialize` and `notifications/initialized` work without auth, all other operations require Bearer token
- **Public Client Support**: OAuth configured for public clients (no client secret required)
- **Dynamic Client Registration**: Supporting public clients with proper security
- **MCP StreamableHTTP Transport**: Proper SDK integration with authentication
- **Tool Auto-Context**: User ID automatically injected, simplified tool parameters
- **CORS Support**: Browser-based MCP clients fully supported
- **Next.js Callback Integration**: OAuth callback page working correctly
- **Compatible with MCP Inspector**: Follows MCP protocol specification

### 🧪 Testing Status

- **Manual Testing**: ✅ Confirmed working with curl and MCP Inspector (after fixing public client support)
- **Unit Tests**: ⏳ Test plan created, implementation pending
- **Integration Tests**: ✅ End-to-end OAuth flow tested manually with token exchange
- **Type Safety**: ✅ All TypeScript compilation passes

## Key Design Decisions

### 1. Stateless Transport

Uses `StreamableHTTPServerTransport` with `sessionIdGenerator: undefined` for stateless operation. Each request creates a new server instance for better scalability.

### 2. OAuth Storage

Currently using **in-memory storage** for development. Production should use Redis/database for:

- Access tokens and expiration times
- Registered OAuth clients
- Authorization codes (short-term)

### 3. Public Client Support

Supports both confidential clients (with secrets) and public clients (no secret required) to accommodate different MCP client architectures.

### 4. Error Handling Philosophy

- OAuth errors return standard OAuth 2.0 error responses
- MCP errors use JSON-RPC 2.0 error format with proper error codes
- Tool execution errors are wrapped in MCP content with `isError: true`
- All errors logged to console for debugging

## Common Debugging Patterns

### MCP Connection Issues

1. Check Accept headers: Must include `application/json, text/event-stream`
2. Verify CORS headers if browser-based client
3. Check console logs for StreamableHTTP transport errors
4. Validate JSON-RPC 2.0 request format

### OAuth Authentication Issues

1. Check client registration in console logs
2. Verify redirect URI matches exactly
3. Check token storage and expiration
4. Look for PKCE validation errors

### Tool Execution Issues

1. Verify userId parameter is provided
2. Check if user has valid, non-expired token
3. Look for tool parameter validation errors
4. Check handleToolCall error messages

## Future Enhancement Areas

### Short Term

1. **Persistent Storage**: Replace in-memory with Redis/PostgreSQL
2. **Unit Tests**: Implement comprehensive test suite from test plan
3. **Enhanced Logging**: Structured logging with levels and correlation IDs
4. **Rate Limiting**: Protect OAuth and MCP endpoints from abuse

### Medium Term

1. **More Tools**: Add more Braintrust API integrations
2. **Resources**: Implement MCP resources for file/data access
3. **Prompts**: Add MCP prompt templates for AI interactions
4. **Session Management**: Optional stateful sessions for complex workflows

### Long Term

1. **Multi-tenant**: Support multiple OAuth providers and user contexts
2. **Advanced Auth**: Implement scopes and permissions
3. **Production Security**: Enhanced client validation and DNS rebinding protection
4. **Monitoring**: Health checks, metrics, and alerting

## Troubleshooting Quick Reference

### "Zod validation errors"

**Fixed**: Use proper StreamableHTTPServerTransport instead of manual JSON-RPC handling.

### "Client must accept both application/json and text/event-stream"

**Solution**: Add proper Accept header to requests: `Accept: application/json, text/event-stream`

### "User not authenticated"

**Check**: Verify OAuth flow completed and token stored for the userId being used.

### "CORS policy errors"

**Solution**: CORS is enabled for all origins. Check for correct preflight handling.

### "Method not allowed"

**Check**: Only POST is supported for `/mcp` endpoint. GET returns 405 by design.

### "Invalid client: cannot retrieve client credentials"

**Fixed**: Set `requireClientAuthentication: { authorization_code: false }` in OAuth2Server config to support public clients (MCP clients without client secrets). This enables proper PKCE flow without requiring client secrets.

### "MCP request rejected: Invalid access token" on initialize

**Fixed**: Allow MCP `initialize` method without authentication while requiring Bearer tokens for all other operations. This follows proper MCP protocol where initialize handshake happens before authentication.

### "Invalid client: `redirect_uri` does not match client value"

**Fixed**: Added `http://localhost:6274/oauth/callback/debug` to allowed redirect URIs for MCP Inspector debug mode. MCP Inspector uses this specific path for OAuth callbacks.

### OAuth auto-approval security vulnerability

**CRITICAL FIX**: Removed auto-approval of OAuth requests. Now properly redirects to Next.js consent page (`localhost:3001/auth/oauth/authorize`) where users must manually approve access before authorization codes are generated. This closes the security bypass that allowed authentication without user consent.

## Development Commands

```bash
# Type checking
pnpm tsc --noEmit

# Test OAuth discovery
curl http://localhost:8000/.well-known/openid-configuration

# Test MCP initialize
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{"jsonrpc":"2.0","method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}},"id":1}'

# Test tool listing
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{"jsonrpc":"2.0","method":"tools/list","id":2}'
```

This implementation provides a solid foundation for MCP integration with Braintrust while maintaining security through OAuth 2.0 and following MCP protocol specifications.
