# This env file is just used for debugging docker containers. It is not kept in
# sync with any of the deployed docker configuration settings.

RESPONSE_BUCKET=responses
CODE_BUNDLE_BUCKET=code-bundles
ALLOW_CODE_FUNCTION_EXECUTION=true
STRICT_VALIDATION_MODE=1
TS_API_HEALTHSERVER_HOST=localhost
TS_API_HEALTHSERVER_PORT=8790
PG_URL=postgres://postgres:<EMAIL>:5532/postgres
REDIS_HOST=host.docker.internal
REDIS_PORT=6479
PROXY_ALLOW_PASSTHROUGH_CREDENTIALS=true
ALLOWED_ORIGIN=http://host.docker.internal:3000
CHALICE_LOCAL_USE_LOCAL_ENV=1

# used in test_api_rate_limits.py
RATELIMIT_API_LOGS_ORG=c5292878-410f-4bbb-bffc-c61f9e624805=5
RATELIMIT_API_LOGS_ORG_WINDOW_SECS=10
RATELIMIT_API_LOGS_ORG_ENFORCE=true

# For local testing
# QUARANTINE_REGION=us-east-1
# API_HANDLER_ROLE=arn:aws:iam::872608195481:role/Dev-Quarantine-APIHandlerRole-iRS8ejcTGAtY
# QUARANTINE_INVOKE_ROLE=arn:aws:iam::872608195481:role/Dev-Quarantine-QuarantineInvokeRole-X4oIEoB2E0K8
# QUARANTINE_FUNCTION_ROLE=arn:aws:iam::872608195481:role/Dev-Quarantine-QuarantineFunctionRole-EndiDi10DJJk
# QUARANTINE_PRIVATE_SUBNET_1_ID=subnet-0a6f2b75988f81315
# QUARANTINE_PRIVATE_SUBNET_2_ID=subnet-07c928a8d0d2e0bc5
# QUARANTINE_PRIVATE_SUBNET_3_ID=subnet-0e6e502f7f2d7c41c
# QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP=sg-011a1dd20481b3959
# QUARANTINE_PUB_PRIVATE_VPC_ID=vpc-0f0344bff7eb87100
