import * as btql_wasm from "@braintrust/btql-wasm";
import { bindExpr, BindContext, BoundExpr } from "@braintrust/btql/binder";

let _initialized = false;

function init() {
  if (!_initialized) {
    btql_wasm.init();
    _initialized = true;
  }
}

export function interpretBoundExpr(
  boundExpr: BoundExpr,
  row: Record<string, unknown>,
): unknown {
  init();
  return btql_wasm.interpret_bound_expr(boundExpr, row);
}

export function parseAndInterpretExpr({
  expr,
  bindCtx,
  row,
}: {
  expr: string;
  bindCtx?: BindContext;
  row: Record<string, unknown>;
}): unknown {
  init();
  const boundExpr = bindExpr(
    bindCtx ?? {
      schema: {},
      scope: {},
      queryText: undefined,
      bindingMeasures: false,
      skipFieldCasts: false,
    },
    { btql: expr },
  );
  return interpretBoundExpr(boundExpr, row);
}
