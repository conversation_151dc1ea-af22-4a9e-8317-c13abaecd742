import { customFetchRequest } from "./custom_fetch";
import { getRedis } from "./redis";
import { REDIS_ERROR_CONTEXT_CACHE_KEY, HTTPError } from "./util";
import { ErrorContext, errorContextSchema } from "@braintrust/local";
import { sha1 } from "./hash";

export class ErrorContextCache {
  constructor(private expiration_time = 3600) {}

  public async getErrorContext({
    appOrigin,
    authToken,
    wasCachedToken,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    wasCachedToken?: string;
  }): Promise<ErrorContext> {
    const cacheKey = ErrorContextCache.makeCacheKey(authToken || "anon");
    const redisClient = await getRedis();
    const value = await redisClient.get(cacheKey);
    if (value) {
      const ret = errorContextSchema.parse(JSON.parse(value));
      if (wasCachedToken) {
        await redisClient.set(wasCachedToken, "true", {
          EX: 3600,
        });
      }
      return ret;
    }

    const headers: Record<string, string> = {};
    if (authToken) {
      headers["Authorization"] = `Bearer ${authToken}`;
    }
    const resp = await customFetchRequest(
      `${appOrigin}/api/self/get_error_context`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...headers,
        },
        body: JSON.stringify({}),
      },
    );
    if (!resp.ok) {
      throw new HTTPError(
        resp.statusCode,
        `Failed to get error context:\n${await resp.body.text()}`,
      );
    }

    const errorContext = errorContextSchema.parse(await resp.body.json());
    await redisClient.set(cacheKey, JSON.stringify(errorContext), {
      EX: this.expiration_time,
    });
    return errorContext;
  }

  private static makeCacheKey(token: string): string {
    return [REDIS_ERROR_CONTEXT_CACHE_KEY, sha1(token)].join(";");
  }
}

export const ERROR_CONTEXT_CACHE = new ErrorContextCache();
