import { describe, test, expect, afterEach, beforeEach, vi } from "vitest";
import { mergeStreams } from "./stream_util";

type DripValue = (
  | {
      value: string;
    }
  | {
      done: true;
    }
) & {
  delayMs?: number;
};

function createStreamWithDrip(values: DripValue[]): ReadableStream {
  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();

  const drip = async () => {
    for (const drip of values) {
      if (drip.delayMs) {
        await new Promise<void>((r) => setTimeout(r, drip.delayMs));
      }
      if ("value" in drip) writer.write(drip.value);
      else if ("done" in drip) {
        writer.close();
        return;
      }
    }

    writer.close();
  };
  drip();

  return readable;
}

describe("mergeStreams", async () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    expect(vi.getTimerCount()).toBe(0);
    vi.clearAllTimers();
    vi.useRealTimers();
  });

  test("both empty", async () => {
    const stream1 = createStreamWithDrip([]);
    const stream2 = createStreamWithDrip([]);
    const mergedStream = mergeStreams(stream1, stream2);
    const reader = mergedStream.getReader();
    var { done, value } = await reader.read();
    expect(value).toBe(undefined);
    expect(done).toBe(true);

    await expect(reader.closed).resolves.toBe(undefined);
  });

  test("one empty", async () => {
    const stream1 = createStreamWithDrip([
      { value: "Hello" },
      { value: "World", delayMs: 100 },
      { value: "!", delayMs: 100 },
    ]);
    const stream2 = createStreamWithDrip([]);
    const mergedStream = mergeStreams(stream1, stream2);
    const reader = mergedStream.getReader();

    var { done, value } = await reader.read();
    expect(value).toBe("Hello");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toBe("World");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toBe("!");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toBe(undefined);
    expect(done).toBe(true);

    await expect(reader.closed).resolves.toBe(undefined);
  });

  test("one slow empty", async () => {
    const stream1 = createStreamWithDrip([
      { value: "Hello" },
      { value: "World" },
      { value: "!" },
    ]);
    const stream2 = createStreamWithDrip([{ done: true, delayMs: 100 }]);
    const mergedStream = mergeStreams(stream1, stream2);
    const reader = mergedStream.getReader();

    var { done, value } = await reader.read();
    expect(value).toBe("Hello");
    expect(done).toBe(false);

    var { done, value } = await reader.read();
    expect(value).toBe("World");
    expect(done).toBe(false);

    var { done, value } = await reader.read();
    expect(value).toBe("!");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toBe(undefined);
    expect(done).toBe(true);

    await expect(reader.closed).resolves.toBe(undefined);
  });

  test("both nonempty", async () => {
    const stream1 = createStreamWithDrip([
      { value: "Hello" },
      { value: "rld", delayMs: 200 },
    ]);

    const stream2 = createStreamWithDrip([
      { value: "Wo", delayMs: 100 },
      { value: "!", delayMs: 205 },
    ]);

    const mergedStream = mergeStreams(stream1, stream2);
    const reader = mergedStream.getReader();

    var { done, value } = await reader.read();
    expect(value).toEqual("Hello");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toEqual("Wo");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toEqual("rld");
    expect(done).toBe(false);

    vi.advanceTimersToNextTimer();

    var { done, value } = await reader.read();
    expect(value).toEqual("!");
    expect(done).toBe(false);

    var { done, value } = await reader.read();
    expect(value).toBe(undefined);
    expect(done).toBe(true);

    await expect(reader.closed).resolves.toBe(undefined);
  });
});
