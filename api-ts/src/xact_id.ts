import { getRedis } from "./redis";

// The transaction id is a 64-bit integer, with the top 48 bits being the
// timestamp in seconds and the bottom 16 bits being a monotonically increasing
// global id (mod 2^16) to prevent collisions. The 0x0de1 flag at the front
// "fills" up the top 16 bits so that the number uses 19 digits (the maximum
// number of digits that can be represented by a 64-bit #). This padding ensures
// that the numbers are lexicographically sortable as strings as well as
// integers.
//
// Decoder in implemented in `app/utils/transactions.ts`
export async function nextXactId(): Promise<string> {
  const redisClient = await getRedis();
  const [ts, xact_id] = await Promise.all([
    redisClient.time().then((x) => BigInt(Math.floor(x.getTime() / 1000))),
    redisClient.incr("global-xact-id").then((x) => BigInt(x)),
  ]);
  const ret =
    (0x0de1n << 48n) | ((ts & 0xffffffffffffn) << 16n) | (xact_id & 0xffffn);
  return String(ret);
}
