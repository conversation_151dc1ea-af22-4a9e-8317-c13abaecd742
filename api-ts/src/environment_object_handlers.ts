import { Request, Response } from "express";
import { z } from "zod";
import { getPG } from "./db/pg";
import { environmentObjectSchema } from "@braintrust/typespecs";
import {
  BadRequestError,
  wrapZodError,
  handleUniqueConstraintViolation,
  AccessDeniedError,
} from "./util";
import { getRequestContext } from "./request_context";
import { parseNoStrip } from "braintrust/util";
import { sql } from "@braintrust/btql/planner";
import { resolveOrganizationId } from "./organization_utils";
import { OBJECT_CACHE } from "./object_cache";
import { invalidatePromptEnvironmentCache } from "./prompt_cache_invalidation";

async function checkPromptPermission({
  objectId,
  appOrigin,
  authToken,
  permission,
}: {
  objectId: string;
  appOrigin: string;
  authToken: string | undefined;
  permission: "read" | "update";
}) {
  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken,
    aclObjectType: "prompt",
    overrideRestrictObjectType: undefined,
    objectId,
  });

  if (!permissions || !permissions.permissions.includes(permission)) {
    throw new AccessDeniedError({
      permission,
      objectType: "prompt",
      objectId,
    });
  }

  return permissions;
}

// PostgreSQL constraint names
const ENVIRONMENT_OBJECTS_UNIQUE_CONSTRAINT = "uq_env_objects_env_type_id";
// PostgreSQL bigint limit. Bigint in Postgres is 64-bit signed integer which does not match JavaScript's BigInt implementation.
const PG_BIGINT_MAX = 9223372036854775807n;

// URL params schemas
const objectParamsSchema = z.object({
  object_type: z.string(),
  object_id: z.string(),
});

const objectEnvironmentParamsSchema = z.object({
  object_type: z.string(),
  object_id: z.string(),
  environment_slug: z.string(),
});

// Body schemas
const bigIntStringSchema = z.string().refine(
  (val) => {
    if (!val || val.trim() === "") {
      return false;
    }

    try {
      const num = BigInt(val);

      // Make sure it fits within PostgreSQL bigint range
      return num >= 0n && num <= PG_BIGINT_MAX;
    } catch {
      return false;
    }
  },
  { message: "Must be a valid bigint string" },
);

const environmentObjectCreateSchema = z.object({
  object_version: bigIntStringSchema,
  environment_slug: z.string(),
  org_name: z.string().nullish(),
});

const environmentObjectUpsertSchema = z.object({
  object_version: bigIntStringSchema,
  org_name: z.string().nullish(),
});

// Query params schemas
const environmentObjectListQuerySchema = z.object({
  org_name: z.string().nullish(),
});

const environmentObjectGetQuerySchema = z.object({
  org_name: z.string().nullish(),
});

async function resolveEnvironmentId({
  orgId,
  environmentSlug,
}: {
  orgId: string;
  environmentSlug: string;
}): Promise<string> {
  const pg = getPG();
  const queryTemplate = sql`
    SELECT id FROM environments
    WHERE org_id = ${orgId} AND slug = ${environmentSlug} AND deleted_at IS NULL
  `;

  const { query, params: queryParams } = queryTemplate.toNumericParamQuery();
  const result = await pg.query(query, queryParams);

  if (result.rows.length === 0) {
    throw new BadRequestError(
      `Environment with slug '${environmentSlug}' not found`,
    );
  }
  return z.object({ id: z.string() }).parse(result.rows[0]).id;
}

export async function environmentObjectCreateHandler(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);
  const urlParams = wrapZodError(() =>
    parseNoStrip(objectParamsSchema, req.params),
  );
  const bodyParams = wrapZodError(() =>
    parseNoStrip(environmentObjectCreateSchema, ctx.data),
  );

  const orgId = await resolveOrganizationId({
    providedOrgName: bodyParams.org_name,
    ctx,
  });

  // Check permissions for prompts
  if (urlParams.object_type !== "prompt") {
    throw new BadRequestError(
      `Unsupported object type: ${urlParams.object_type}. Only 'prompt' is supported.`,
    );
  }

  await checkPromptPermission({
    objectId: urlParams.object_id,
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    permission: "update",
  });

  // Resolve environment slug to environment ID
  const environmentId = await resolveEnvironmentId({
    orgId,
    environmentSlug: bodyParams.environment_slug,
  });

  const pg = getPG();

  try {
    // Insert new environment object association
    const insertTemplate = sql`
      INSERT INTO environment_objects (object_type, object_id, object_version, environment_id)
      VALUES (${urlParams.object_type}, ${urlParams.object_id}, ${bodyParams.object_version}, ${environmentId})
      RETURNING id, object_type, object_id, object_version::text as object_version, created::text as created
    `;

    const { query, params: queryParams } = insertTemplate.toNumericParamQuery();
    const insertResult = await pg.query(query, queryParams);

    const environmentObject = environmentObjectSchema.parse({
      ...insertResult.rows[0],
      environment_slug: bodyParams.environment_slug,
    });
    res.json(environmentObject);
  } catch (error) {
    handleUniqueConstraintViolation(
      error,
      ENVIRONMENT_OBJECTS_UNIQUE_CONSTRAINT,
      "Object already has version associated with this environment (Use PUT to overwrite)",
    );
  }
}

export async function environmentObjectListHandler(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);
  const urlParams = wrapZodError(() =>
    parseNoStrip(objectParamsSchema, req.params),
  );
  const queryParams = wrapZodError(() =>
    parseNoStrip(environmentObjectListQuerySchema, req.query),
  );

  const orgId = await resolveOrganizationId({
    providedOrgName: queryParams.org_name,
    ctx,
  });

  // Check permissions for prompts
  if (urlParams.object_type !== "prompt") {
    throw new BadRequestError(
      `Unsupported object type: ${urlParams.object_type}. Only 'prompt' is supported.`,
    );
  }

  await checkPromptPermission({
    objectId: urlParams.object_id,
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    permission: "read",
  });

  const pg = getPG();

  const queryTemplate = sql`
    SELECT eo.id, eo.object_type, eo.object_id, eo.object_version::text as object_version, eo.environment_id,
           eo.created::text as created, e.slug as environment_slug
    FROM environment_objects eo
    JOIN environments e ON eo.environment_id = e.id
    WHERE eo.object_type = ${urlParams.object_type}
      AND eo.object_id = ${urlParams.object_id}
      AND e.org_id = ${orgId}
      AND e.deleted_at IS NULL
    ORDER BY eo.created DESC
  `;

  const { query, params: sqlParams } = queryTemplate.toNumericParamQuery();
  const result = await pg.query(query, sqlParams);

  const environmentObjects = environmentObjectSchema.array().parse(result.rows);

  res.json({ objects: environmentObjects });
}

export async function environmentObjectGetHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const urlParams = wrapZodError(() =>
    parseNoStrip(objectEnvironmentParamsSchema, req.params),
  );
  const queryParams = wrapZodError(() =>
    parseNoStrip(environmentObjectGetQuerySchema, req.query),
  );

  const orgId = await resolveOrganizationId({
    providedOrgName: queryParams.org_name,
    ctx,
  });

  // Check permissions for prompts
  if (urlParams.object_type !== "prompt") {
    throw new BadRequestError(
      `Unsupported object type: ${urlParams.object_type}. Only 'prompt' is supported.`,
    );
  }

  await checkPromptPermission({
    objectId: urlParams.object_id,
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    permission: "read",
  });

  const pg = getPG();

  const queryTemplate = sql`
    SELECT eo.id, eo.object_type, eo.object_id, eo.object_version::text as object_version, eo.environment_id,
           eo.created::text as created, e.slug as environment_slug
    FROM environment_objects eo
    JOIN environments e ON eo.environment_id = e.id
    WHERE eo.object_type = ${urlParams.object_type}
      AND eo.object_id = ${urlParams.object_id}
      AND e.slug = ${urlParams.environment_slug}
      AND e.org_id = ${orgId}
      AND e.deleted_at IS NULL
  `;

  const { query, params: sqlParams } = queryTemplate.toNumericParamQuery();
  const result = await pg.query(query, sqlParams);

  if (result.rows.length === 0) {
    throw new BadRequestError(
      `No association found for object ${urlParams.object_id} in environment ${urlParams.environment_slug}`,
    );
  }

  const environmentObject = environmentObjectSchema.parse(result.rows[0]);
  res.json(environmentObject);
}

export async function environmentObjectUpsertHandler(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);
  const urlParams = wrapZodError(() =>
    parseNoStrip(objectEnvironmentParamsSchema, req.params),
  );
  const bodyParams = wrapZodError(() =>
    parseNoStrip(environmentObjectUpsertSchema, ctx.data),
  );

  const orgId = await resolveOrganizationId({
    providedOrgName: bodyParams.org_name,
    ctx,
  });

  // Check permissions for prompts
  if (urlParams.object_type !== "prompt") {
    throw new BadRequestError(
      `Unsupported object type: ${urlParams.object_type}. Only 'prompt' is supported.`,
    );
  }

  const promptPermissions = await checkPromptPermission({
    objectId: urlParams.object_id,
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    permission: "update",
  });

  const projectInfo = promptPermissions.parent_cols.get("project");
  if (!projectInfo) {
    throw new BadRequestError("Unable to get project information");
  }

  // Resolve environment slug to environment ID
  const environmentId = await resolveEnvironmentId({
    orgId,
    environmentSlug: urlParams.environment_slug,
  });

  const pg = getPG();

  const upsertTemplate = sql`
    INSERT INTO environment_objects (object_type, object_id, object_version, environment_id)
    VALUES (${urlParams.object_type}, ${urlParams.object_id}, ${bodyParams.object_version}, ${environmentId})
    ON CONFLICT (environment_id, object_type, object_id)
    DO UPDATE SET
      object_version = ${bodyParams.object_version},
      created = NOW()
    RETURNING id, object_type, object_id, object_version::text as object_version, created::text as created
  `;

  const { query, params } = upsertTemplate.toNumericParamQuery();
  const result = await pg.query(query, params);

  const environmentObject = environmentObjectSchema.parse({
    ...result.rows[0],
    environment_slug: urlParams.environment_slug,
  });

  // Invalidate prompt-specific cache entries for this environment
  await invalidatePromptEnvironmentCache({
    environmentId,
    environmentSlug: urlParams.environment_slug,
    promptId: promptPermissions.object_id,
    promptSlug: promptPermissions.object_name,
    projectId: projectInfo.id,
    projectName: projectInfo.name,
  });

  res.json(environmentObject);
}

export async function environmentObjectDeleteHandler(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);
  const urlParams = wrapZodError(() =>
    parseNoStrip(objectEnvironmentParamsSchema, req.params),
  );
  const queryParams = wrapZodError(() =>
    parseNoStrip(environmentObjectGetQuerySchema, req.query),
  );

  const orgId = await resolveOrganizationId({
    providedOrgName: queryParams.org_name,
    ctx,
  });

  // Check permissions for prompts
  if (urlParams.object_type !== "prompt") {
    throw new BadRequestError(
      `Unsupported object type: ${urlParams.object_type}. Only 'prompt' is supported.`,
    );
  }

  const promptPermissions = await checkPromptPermission({
    objectId: urlParams.object_id,
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    permission: "update",
  });

  const projectInfo = promptPermissions.parent_cols.get("project");
  if (!projectInfo) {
    throw new BadRequestError("Unable to get project information");
  }

  // Resolve environment slug to environment ID
  const environmentId = await resolveEnvironmentId({
    orgId,
    environmentSlug: urlParams.environment_slug,
  });

  const pg = getPG();

  // Delete the association
  const deleteTemplate = sql`
    DELETE FROM environment_objects
    WHERE environment_id = ${environmentId}
      AND object_type = ${urlParams.object_type}
      AND object_id = ${urlParams.object_id}
    RETURNING id, object_type, object_id, object_version::text as object_version, created::text as created
  `;

  const { query, params: sqlParams } = deleteTemplate.toNumericParamQuery();
  const result = await pg.query(query, sqlParams);

  if (result.rows.length === 0) {
    throw new BadRequestError(
      `No association found for object ${urlParams.object_id} in environment ${urlParams.environment_slug}`,
    );
  }

  const deletedEnvironmentObject = environmentObjectSchema.parse({
    ...result.rows[0],
    environment_slug: urlParams.environment_slug,
  });

  // Invalidate prompt-specific cache entries for this environment
  await invalidatePromptEnvironmentCache({
    environmentId,
    environmentSlug: urlParams.environment_slug,
    promptId: promptPermissions.object_id,
    promptSlug: promptPermissions.object_name,
    projectId: projectInfo.id,
    projectName: projectInfo.name,
  });

  res.json(deletedEnvironmentObject);
}
