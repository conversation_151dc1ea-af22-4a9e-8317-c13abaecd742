import { Request, Response } from "express";
import { z } from "zod";
import { assertBrainstoreEnabled, runBrainstore } from "./brainstore";
import { authCheckObjectIds } from "./auth";
import { getRequestContext } from "../request_context";
import { wrapZodError } from "../util";
import { globalVacuumStatusSchema } from "@braintrust/local/app-schema";

const vacuumTypeSchema = z
  .enum(["vacuum_index", "vacuum_segment_wal"])
  .default("vacuum_index");

const brainstoreVacuumStatusRequest = z.object({
  object_ids: z.array(z.string()),
  vacuum_type: vacuumTypeSchema,
  vacuum_stale_seconds: z.number().int().nullish(),
});

export async function getVacuumStatusRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumStatusRequest.parse(ctx.data),
  );
  const objectIds = params.object_ids;

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/status",
      args: {
        object_ids: objectIds,
        vacuum_type: params.vacuum_type,
        vacuum_stale_seconds: params.vacuum_stale_seconds ?? undefined,
      },
      schema: globalVacuumStatusSchema,
      isWrite: true,
    }),
  );
}

const brainstoreVacuumResetStateRequest = z.object({
  object_ids: z.array(z.string()),
  vacuum_type: vacuumTypeSchema,
  wait_for_vacuum_grace_period: z.boolean().nullish(),
});

export async function resetVacuumStateRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumResetStateRequest.parse(ctx.data),
  );
  const objectIds = params.object_ids;

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  await runBrainstore({
    path: "/vacuum/reset_state",
    args: {
      object_ids: objectIds,
      vacuum_type: params.vacuum_type,
      wait_for_vacuum_grace_period:
        params.wait_for_vacuum_grace_period ?? undefined,
    },
    schema: z.object({ success: z.boolean() }),
    isWrite: true,
  });

  res.json({ success: true });
}

const brainstoreVacuumObjectRequest = z.object({
  segment_id_cursor: z.string().nullish(),
  dry_run: z.boolean().nullish(),
});

const brainstoreVacuumIndexRequest = z.object({
  object_ids: z.array(z.string()),
  dry_run: z.boolean().nullish(),
});

const brainstoreVacuumSegmentWalRequest = z.object({
  object_ids: z.array(z.string()),
  dry_run: z.boolean().nullish(),
});

const brainstoreVacuumIndexResponse = z.object({
  success: z.boolean(),
  segment_id_cursor: z.string().nullish(),
  num_processed_segments: z.number(),
  planned_num_deletes: z.number(),
  planned_total_bytes: z.number(),
  num_deleted_files: z.number(),
  error: z.string().nullish(),
});

const brainstoreVacuumSegmentWalResponse = z.object({
  success: z.boolean(),
  num_processed_segments: z.number(),
  planned_num_purged_entries: z.number(),
  num_purged_entries: z.number(),
  planned_num_deleted_files: z.number(),
  num_deleted_files: z.number(),
  error: z.string().nullish(),
});

export async function vacuumObjectRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumObjectRequest.parse(ctx.data),
  );
  const objectIds = [req.params.object_id];

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  const [vacuumIndexResult, vacuumSegmentWalResult] = await Promise.all([
    runBrainstore({
      path: "/vacuum/index",
      args: {
        object_ids: objectIds,
        segment_id_cursor: params.segment_id_cursor,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumIndexResponse,
      isWrite: true,
    }),
    runBrainstore({
      path: "/vacuum/segment_wal",
      args: {
        object_ids: objectIds,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumSegmentWalResponse,
      isWrite: true,
    }),
  ]);

  res.json({
    vacuum_index: vacuumIndexResult,
    vacuum_segment_wal: vacuumSegmentWalResult,
  });
}

export async function vacuumIndexObjectRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumObjectRequest.parse(ctx.data),
  );
  const objectIds = [req.params.object_id];

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/index",
      args: {
        object_ids: objectIds,
        segment_id_cursor: params.segment_id_cursor,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumIndexResponse,
      isWrite: true,
    }),
  );
}

export async function vacuumSegmentWalObjectRequest(
  req: Request,
  res: Response,
) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumObjectRequest.parse(ctx.data),
  );
  const objectIds = [req.params.object_id];

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/segment_wal",
      args: {
        object_ids: objectIds,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumSegmentWalResponse,
      isWrite: true,
    }),
  );
}

export async function vacuumIndexRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumIndexRequest.parse(ctx.data),
  );

  await authCheckObjectIds({
    objectIds: params.object_ids,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/index",
      args: {
        object_ids: params.object_ids,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumIndexResponse,
      isWrite: true,
    }),
  );
}

export async function vacuumSegmentWalRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumSegmentWalRequest.parse(ctx.data),
  );

  await authCheckObjectIds({
    objectIds: params.object_ids,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/segment_wal",
      args: {
        object_ids: params.object_ids,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumSegmentWalResponse,
      isWrite: true,
    }),
  );
}
