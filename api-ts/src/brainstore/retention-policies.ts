import { Request, Response } from "express";
import { z } from "zod";
import { assertBrainstoreEnabled, runBrainstore } from "./brainstore";
import { getRequestContext } from "../request_context";
import { wrapZodError } from "../util";
import { objectTypeSchema } from "../schema";

const retentionObjectSchema = z.object({
  object_type: objectTypeSchema,
  object_id: z.string().uuid(),
});

const retentionPolicyConfigSchema = z.object({
  automation_id: z.string().uuid(),
  retention_days: z.number().int().positive(),
});

const brainstoreRetentionPoliciesRequest = z.object({
  objects: z.array(retentionObjectSchema),
  fail_unknown_objects: z.boolean().optional(),
  // (For testing) If true, pass ctx.token to brainstore and have it use it to
  // in place of the data plane service token.
  passthrough_request_token_as_service_token: z.boolean().default(false),
});

const brainstoreRetentionPoliciesResponse = z.record(
  retentionPolicyConfigSchema,
);

export async function getRetentionPoliciesRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreRetentionPoliciesRequest.parse(ctx.data),
  );

  res.json(
    await runBrainstore({
      path: "/retention/policies",
      args: {
        objects: params.objects,
        fail_unknown_objects: params.fail_unknown_objects,
        service_token: params.passthrough_request_token_as_service_token
          ? ctx.token
          : undefined,
      },
      schema: brainstoreRetentionPoliciesResponse,
      isWrite: false,
    }),
  );
}
