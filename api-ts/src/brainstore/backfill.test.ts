import {
  OBJECT_TYPE_FIELD,
  objectIdsUnionSchema,
} from "@braintrust/local/api-schema";
import { expect, test } from "vitest";

test("backfill", () => {
  const row = {
    object_id: "prompt_session:306182a4-47c2-40d2-92be-62fdeb65ab0b:x",
    project_id: "14a64f77-cc7f-424a-b053-172bd39cb88e",
    experiment_id: null,
    dataset_id: null,
    prompt_session_id: "306182a4-47c2-40d2-92be-62fdeb65ab0b:x",
    log_id: null,
    max_sequence_id: 1398728,
  };
  const objectIdSet = objectIdsUnionSchema.parse(row);
  expect(objectIdSet[OBJECT_TYPE_FIELD]).toEqual("playground_logs");
});

// XXX NEXT STEPS:
// * Write a unit test that walks through the whole flow:
// - Writes project logs to the prompt session parent
// - Then, verifies that they show up in the logs and are queryable through BTQL
// - Make sure they get backfilled to Brainstore
// - Make sure after compaction runs, they are completely processed.
