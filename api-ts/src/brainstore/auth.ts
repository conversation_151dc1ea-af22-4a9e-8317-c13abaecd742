import {
  makeBrainstoreObjectIdFromParts,
  parseBrainstoreObjectId,
} from "./brainstore";
import { z } from "zod";
import { RequestContext } from "../request_context";
import {
  AccessDeniedError,
  InternalServerError,
  getSysadminRoles,
  objectTypeToAclObjectType,
  wrapZodError,
} from "../util";
import { ObjectType } from "../schema";
import { OBJECT_CACHE } from "../object_cache";
import { FullTrackedObject, TrackedObject } from "@braintrust/local/app-schema";

const TRACKING_PERMISSIONS = ["read", "update", "delete"] as const;

export async function authCheckObjectIds({
  ctx,
  objectIds,
  wasCachedToken,
  filterOnAccessDenied,
  ignoreMissingObjects,
}: {
  ctx: RequestContext;
  objectIds: string[];
  wasCachedToken?: string;
  filterOnAccessDenied?: boolean;
  ignoreMissingObjects?: boolean;
}): Promise<
  {
    object_type: ObjectType;
    object_id: string;
    project_id: string;
    org_id: string;
  }[]
> {
  const objectIdsByType: Map<ObjectType, string[]> = new Map();
  for (const fullObjectId of objectIds) {
    const { objectType, objectId } = parseBrainstoreObjectId(fullObjectId);
    const existing = objectIdsByType.get(objectType);
    if (!existing) {
      objectIdsByType.set(objectType, [objectId]);
    } else {
      existing.push(objectId);
    }
  }

  const resolvedObjectIds: {
    object_type: ObjectType;
    object_id: string;
    project_id: string;
    org_id: string;
  }[] = [];
  for (const [objectType, objectIds] of objectIdsByType.entries()) {
    const { aclObjectType, overrideRestrictObjectType } =
      objectTypeToAclObjectType(objectType);
    const objects = await OBJECT_CACHE.checkAndGetMulti({
      appOrigin: ctx.appOrigin,
      authToken:
        wrapZodError(() => z.string().nullish().parse(ctx.token)) ?? undefined,
      aclObjectType,
      overrideRestrictObjectType,
      objectIds,
      wasCachedToken,
      allowSysadminRoles: getSysadminRoles(),
      includeDeletedObjects: true,
      ignoreMissingObjects,
    });
    for (const id of objectIds) {
      const o = objects[id];

      let isAllowed = true;
      if (!o?.is_allowed_sysadmin) {
        const permissions = o?.permissions ?? [];
        for (const perm of TRACKING_PERMISSIONS) {
          if (!permissions.includes(perm)) {
            if (filterOnAccessDenied) {
              isAllowed = false;
              break;
            } else {
              throw new AccessDeniedError({
                permission: perm,
                aclObjectType,
                overrideRestrictObjectType,
                objectId: id,
              });
            }
          }
        }
      }

      if (!isAllowed) {
        continue;
      }

      const project = o.parent_cols.get("project");
      const organization = o.parent_cols.get("organization");
      if (!organization) {
        throw new InternalServerError(`Object ${id} has no organization`);
      }
      let projectId: string;
      if (objectType === "project_logs") {
        projectId = id;
      } else if (project) {
        projectId = project.id;
      } else {
        throw new InternalServerError(
          `Object ${id} of type ${objectType} has no project`,
        );
      }

      resolvedObjectIds.push({
        object_type: objectType,
        object_id: makeBrainstoreObjectIdFromParts(objectType, id),
        project_id: projectId,
        org_id: organization.id,
      });
    }
  }

  return resolvedObjectIds;
}

export async function authCheckTrackedObjects({
  ctx,
  trackedObjects,
  wasCachedToken,
}: {
  ctx: RequestContext;
  trackedObjects: TrackedObject[];
  wasCachedToken?: string;
}): Promise<FullTrackedObject[]> {
  const resolvedObjectIds: FullTrackedObject[] = [];
  for (const trackedObject of trackedObjects) {
    const projectInfo = await OBJECT_CACHE.checkAndGet({
      appOrigin: ctx.appOrigin,
      authToken:
        wrapZodError(() => z.string().nullish().parse(ctx.token)) ?? undefined,
      aclObjectType: "project",
      overrideRestrictObjectType:
        trackedObject.object_type === "project_logs"
          ? "project_log"
          : trackedObject.object_type === "playground_logs"
            ? "prompt_session"
            : trackedObject.object_type,
      objectId: trackedObject.project_id,
      wasCachedToken,
      allowSysadminRoles: getSysadminRoles(),
    });
    if (!projectInfo.is_allowed_sysadmin) {
      for (const perm of TRACKING_PERMISSIONS) {
        if (!projectInfo.permissions.includes(perm)) {
          throw new AccessDeniedError({
            permission: perm,
            aclObjectType: "project",
            overrideRestrictObjectType: undefined,
            objectId: trackedObject.project_id,
          });
        }
      }
    }

    const organization = projectInfo.parent_cols.get("organization");
    if (!organization) {
      throw new InternalServerError(
        `Project ${trackedObject.project_id} has no organization`,
      );
    }
    resolvedObjectIds.push({
      object_type: trackedObject.object_type,
      project_id: trackedObject.project_id,
      org_id: organization.id,
    });
  }

  return resolvedObjectIds;
}
