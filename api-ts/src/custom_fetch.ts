import {
  Dispatcher,
  EnvHttpProxyAgent,
  RetryAgent,
  errors,
  setGlobalDispatcher,
  fetch as undiciFetch,
  request as undiciRequest,
} from "undici";
import {
  ALLOWED_ORIGIN,
  OUTBOUND_RATE_LIMIT_MAX_REQUESTS,
  OUTBOUND_RATE_LIMIT_WINDOW_MINUTES,
} from "./env";
import { getRedis } from "./redis";
import {
  REDIS_OUTBOUND_RATE_LIMIT_COUNTER,
  TooManyRequestsError,
  getCurrentUnixTimestamp,
} from "./util";
import { getLogger } from "./instrumentation/logger";

// Get the badPorts list from the original undici module.
const badPorts = require("undici/lib/web/fetch/constants").badPorts;
const badPortsSet = require("undici/lib/web/fetch/constants").badPortsSet;

const CustomFetchError = errors.UndiciError;
export { CustomFetchError };

let RATE_LIMIT_ORIGINS: Set<string> | undefined = undefined;

export async function checkControlPlaneRateLimit(
  url: URL,
  options?: CustomFetchOptions,
): Promise<void> {
  const origin = url.host;

  if (!RATE_LIMIT_ORIGINS) {
    RATE_LIMIT_ORIGINS = new Set(
      [ALLOWED_ORIGIN].reduce(
        (acc, x) => (x ? acc.concat([new URL(x).host]) : acc),
        new Array<string>(),
      ),
    );
  }
  if (!RATE_LIMIT_ORIGINS.has(origin)) {
    return;
  }

  const authHeader = options?.headers?.["Authorization"] || "anon";
  const currentWindow = Math.floor(
    getCurrentUnixTimestamp() / 60 / OUTBOUND_RATE_LIMIT_WINDOW_MINUTES,
  );
  const key = `${REDIS_OUTBOUND_RATE_LIMIT_COUNTER}:${authHeader}:${origin}:${currentWindow}`;
  const overrideMaxRequestsKey = `${REDIS_OUTBOUND_RATE_LIMIT_COUNTER}_override_max_requests:${authHeader
    .split(" ")
    .at(-1)}`;
  let redisClient;
  try {
    redisClient = await getRedis();
  } catch (e) {
    getLogger().error(
      { error: e },
      "Error getting redis. Will skip rate limit check.",
    );
    return;
  }
  const values = await Promise.all(
    (() => {
      const promises = [];
      promises.push(redisClient.get(overrideMaxRequestsKey));
      promises.push(
        redisClient.set(key, 0, {
          NX: true,
          EX: OUTBOUND_RATE_LIMIT_WINDOW_MINUTES * 60 * 10,
        }),
      );
      promises.push(redisClient.incr(key));
      return promises;
    })(),
  );
  const overrideMaxRequests = values[0] ? Number(values[0]) : undefined;
  const maxRequests = overrideMaxRequests ?? OUTBOUND_RATE_LIMIT_MAX_REQUESTS;
  const cnt = Number(values[2]);
  if (maxRequests > 0 && cnt > maxRequests) {
    getLogger().error(
      {
        requestCount: cnt,
        origin: origin,
        maxRequests: maxRequests,
        windowMinutes: OUTBOUND_RATE_LIMIT_WINDOW_MINUTES,
      },
      "Rate limit exceeded for origin",
    );
    throw new TooManyRequestsError(OUTBOUND_RATE_LIMIT_WINDOW_MINUTES * 60);
  }
}

let hasSetGlobalDispatcher = false;

// Note: Any properties added to this struct will need to be manually added to
// the `undiciRequest` invocation in `customFetch`.
export type CustomFetchOptions = {
  method?: Dispatcher.HttpMethod;
  headers?: Record<string, string>;
  body?: string | Buffer | Uint8Array | null;
  timeout?: number;
};

export type CustomFetchResponse = Dispatcher.ResponseData & { ok: boolean };

function constructGlobalDispatcher(agent?: Dispatcher) {
  // Constructing an EnvHttpProxyAgent will emit a warning. We explicitly
  // suppress this.
  const originalEmitWarning = process.emitWarning;
  process.emitWarning = (warning, ...args) => {
    if (
      typeof warning === "string" &&
      warning.includes("EnvHttpProxyAgent is experimental")
    ) {
      return;
    }
    // @ts-ignore: Spread args are fine here since we're just forwarding them
    originalEmitWarning.call(process, warning, ...args);
  };

  // The EnvHttpProxyAgent makes our requests respect the NO_PROXY,
  // HTTP_PROXY, and HTTPS_PROXY environment variable settings.
  //
  // The default retry options for the RetryAgent seem reasonable.
  const retryDispatcher = new RetryAgent(agent ?? new EnvHttpProxyAgent());

  // Add in a dispatcher that sets a timeout for the headers and body.
  const timeoutDispatcher: Dispatcher.DispatcherComposeInterceptor = (
    dispatch,
  ) => {
    return (opts, handler) => {
      const modifiedOpts = {
        headersTimeout: 600 * 1000,
        bodyTimeout: 600 * 1000,
        ...opts,
      };
      return dispatch(modifiedOpts, handler);
    };
  };
  return retryDispatcher.compose(timeoutDispatcher);
}

export function initializeGlobalDispatcher({
  agent,
  force,
}: { agent?: Dispatcher; force?: boolean } = {}) {
  if (force || !hasSetGlobalDispatcher) {
    setGlobalDispatcher(constructGlobalDispatcher(agent));
    hasSetGlobalDispatcher = true;

    process.on("warning", (warning) => {
      if (
        warning.name === "ExperimentalWarning" &&
        warning.message.includes("EnvHttpProxyAgent")
      ) {
        // Suppress this warning
      } else {
        getLogger().warn(
          {
            warningName: warning.name,
            warningMessage: warning.message,
          },
          "Process warning",
        );
      }
    });

    // This hack specifically enables port 10080 which is important for certain customers (Stripe)
    // and blocked by the default `fetch` implementation.
    // https://fetch.spec.whatwg.org/#port-blocking
    const index = badPorts.indexOf("10080");
    if (index !== -1) {
      badPorts.splice(index, 1);
    }
    badPortsSet.delete("10080");
  }
}

// Same as request.ts with exception to logging changes. Keep both in sync.
export async function customFetchRequest(
  url: string | URL,
  options?: CustomFetchOptions,
): Promise<CustomFetchResponse> {
  initializeGlobalDispatcher();

  const urlObj = new URL(url);
  await checkControlPlaneRateLimit(urlObj, options);
  const resp = await (async () => {
    try {
      return await undiciRequest(
        urlObj,
        options
          ? {
              method: options.method,
              headers: options.headers,
              body: options.body,
              bodyTimeout: options.timeout,
            }
          : undefined,
      );
    } catch (e) {
      getLogger().error(
        {
          url: url.toString(),
          error: e,
        },
        "Error when requesting url",
      );
      throw e;
    }
  })();
  const ok = resp.statusCode >= 200 && resp.statusCode < 300;
  return { ...resp, ok };
}

export async function customFetch(
  input: RequestInfo | URL,
  init?: RequestInit,
): Promise<Response> {
  initializeGlobalDispatcher();
  // Typescript does not believe that undiciFetch has the same signature as
  // fetch, but it does (more-or-less), and it works.
  //
  // NOTE: do not modify the arguments without making sure the types are still
  // valid.
  // @ts-ignore
  return undiciFetch(input, init);
}
