import { customFetchRequest } from "./custom_fetch";
import { getRedis } from "./redis";
import {
  REDIS_OBJECT_CACHE_KEYS_SET_PREFIX,
  AccessDeniedError,
  ForbiddenError,
  HTTPError,
  InternalServerError,
  postDefaultHeaders,
  wrapZodError,
} from "./util";
import { AclObjectType } from "@braintrust/typespecs";
import { Me, endpointSchemas } from "@braintrust/local/app-schema";
import { z } from "zod";
import { sha1 } from "./hash";

const objectResponseSchema = endpointSchemas.self_get_object_info.output;

const objectCacheEntrySchema = objectResponseSchema.element.merge(
  z.object({
    // Note: the AclObjectTypes that we get back might correspond to nouns that
    // this backend is not yet aware of, so we decay the type to just a string.
    acl_object_type: z.string(),
    override_restrict_object_type: z.string().nullish(),
  }),
);

// Type for the raw parsed result from Zod
type ObjectCacheEntryRaw = z.infer<typeof objectCacheEntrySchema>;

// Type with Map instead of Record for parent_cols
export type ObjectCacheEntry = Omit<ObjectCacheEntryRaw, "parent_cols"> & {
  parent_cols: Map<string, { id: string; name: string }>;
};

// Convert raw parsed object to use Map for parent_cols
function convertToObjectCacheEntry(raw: ObjectCacheEntryRaw): ObjectCacheEntry {
  const parentColsMap = new Map<string, { id: string; name: string }>();
  for (const [key, value] of Object.entries(raw.parent_cols)) {
    parentColsMap.set(key, value);
  }

  return {
    ...raw,
    parent_cols: parentColsMap,
  };
}

// Convert ObjectCacheEntry to AuditObject format
export function objectCacheEntryToAuditObject(entry: ObjectCacheEntry): {
  acl_object_type: string;
  object_id: string;
  object_name: string;
  parent_cols: Record<string, { id: string; name: string }>;
} {
  const parentColsRecord: Record<string, { id: string; name: string }> = {};
  for (const [key, value] of entry.parent_cols.entries()) {
    parentColsRecord[key] = value;
  }

  return {
    acl_object_type: entry.acl_object_type,
    object_id: entry.object_id,
    object_name: entry.object_name,
    parent_cols: parentColsRecord,
  };
}

export function objectCacheEntryOrgId(
  entry: ObjectCacheEntry,
): string | undefined {
  if (entry.acl_object_type === "organization") {
    return entry.object_id;
  } else {
    return entry.parent_cols.get("organization")?.id;
  }
}

export class ObjectCache {
  constructor(private expiration_time = 3600) {}

  public async checkAndGet({
    appOrigin,
    authToken,
    aclObjectType,
    overrideRestrictObjectType,
    objectId,
    wasCachedToken,
    allowSysadminRoles,
    includeDeletedObjects,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    aclObjectType: AclObjectType;
    overrideRestrictObjectType: AclObjectType | undefined;
    objectId: string;
    wasCachedToken?: string;
    allowSysadminRoles?: string[];
    includeDeletedObjects?: boolean;
  }): Promise<ObjectCacheEntry> {
    const results = await this.checkAndGetMulti({
      appOrigin,
      authToken,
      aclObjectType,
      overrideRestrictObjectType,
      objectIds: [objectId],
      wasCachedToken,
      allowSysadminRoles,
      includeDeletedObjects,
    });
    return results[objectId];
  }

  public async checkAndGetUntyped({
    appOrigin,
    authToken,
    aclObjectType,
    overrideRestrictObjectType,
    objectId,
    wasCachedToken,
    allowSysadminRoles,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    aclObjectType: string;
    overrideRestrictObjectType: string | undefined;
    objectId: string;
    wasCachedToken?: string;
    allowSysadminRoles?: string[];
  }): Promise<ObjectCacheEntry> {
    const results = await this.checkAndGetMultiUntyped({
      appOrigin,
      authToken,
      aclObjectType,
      overrideRestrictObjectType,
      objectIds: [objectId],
      wasCachedToken,
      allowSysadminRoles,
    });
    return results[objectId];
  }

  public async checkAndGetMulti(params: {
    appOrigin: string;
    authToken: string | undefined;
    aclObjectType: AclObjectType;
    overrideRestrictObjectType: AclObjectType | undefined;
    objectIds: string[];
    wasCachedToken?: string;
    allowSysadminRoles?: string[];
    includeDeletedObjects?: boolean;
    ignoreMissingObjects?: boolean;
  }): Promise<Record<string, ObjectCacheEntry>> {
    return await this.checkAndGetMultiUntyped(params);
  }

  public async checkAndGetMultiUntyped({
    appOrigin,
    authToken,
    aclObjectType,
    overrideRestrictObjectType,
    objectIds,
    wasCachedToken,
    allowSysadminRoles,
    includeDeletedObjects,
    ignoreMissingObjects,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    aclObjectType: string;
    overrideRestrictObjectType: string | undefined;
    objectIds: string[];
    wasCachedToken?: string;
    allowSysadminRoles?: string[];
    includeDeletedObjects?: boolean;
    ignoreMissingObjects?: boolean;
  }): Promise<Record<string, ObjectCacheEntry>> {
    if (!appOrigin) {
      throw new InternalServerError("Request origin is required");
    }

    // get_object_info deduplicates object ids so do the same here to ensure
    // we don't incorrectly throw AccessDeniedError on an unexpected result
    // length
    objectIds = Array.from(new Set(objectIds));
    const keys = objectIds.map((objectId) =>
      ObjectCache.makeCacheKey(
        aclObjectType,
        overrideRestrictObjectType,
        objectId,
        authToken || "anon",
        allowSysadminRoles,
        includeDeletedObjects,
      ),
    );
    const idToKey = Object.fromEntries(
      objectIds.map((objectId, i) => [objectId, keys[i]]),
    );
    const idToValue: Record<string, ObjectCacheEntry> = {};
    const redisClient = await getRedis();

    // Apparently the redis client "auto pipelines" things that happen in the same tick.
    const values = await Promise.all(keys.map((key) => redisClient.get(key)));
    for (let i = 0; i < objectIds.length; i++) {
      const objectId = objectIds[i];
      const value = values[i];
      if (value) {
        idToValue[objectId] = convertToObjectCacheEntry(
          objectCacheEntrySchema.parse(JSON.parse(value)),
        );
      }
    }

    const notFoundIds = objectIds.filter((id) => !idToValue[id]);
    if (notFoundIds.length === 0) {
      if (wasCachedToken) {
        await redisClient.set(wasCachedToken, "true", {
          EX: 3600,
        });
      }
      return idToValue;
    }

    const resp = await customFetchRequest(
      `${appOrigin}/api/self/get_object_info`,
      {
        method: "POST",
        headers: postDefaultHeaders({ token: authToken }),
        body: JSON.stringify({
          object_type: aclObjectType,
          override_restrict_object_type: overrideRestrictObjectType,
          object_ids: notFoundIds,
          accept_arbitrary_acl_object_types: true,
          allow_sysadmin_roles: allowSysadminRoles,
          include_deleted_objects: includeDeletedObjects,
        }),
      },
    );

    if (!resp.ok) {
      throw new HTTPError(
        resp.statusCode,
        `Failed to get object info:\n${await resp.body.text()}`,
      );
    }

    const results = objectResponseSchema.parse(await resp.body.json());
    if (results.length !== notFoundIds.length && !ignoreMissingObjects) {
      throw new AccessDeniedError({
        permission: "read",
        aclObjectType,
        overrideRestrictObjectType,
        objectIds: notFoundIds,
      });
    }

    await Promise.all(
      results.flatMap(async (res) => {
        const objectId = res.object_id;
        const { rawEntry, entry } = ObjectCache.makeObjectCacheEntry({
          aclObjectType,
          overrideRestrictObjectType,
          res,
        });
        if (idToValue[objectId]) {
          throw new InternalServerError(
            `Object ${objectId} was already in cache`,
          );
        }
        idToValue[objectId] = entry;

        const cacheKey = idToKey[objectId];
        const promises: Promise<unknown>[] = [];
        promises.push(
          redisClient.set(cacheKey, JSON.stringify(rawEntry), {
            EX: this.expiration_time,
          }),
        );
        // Also store this key in a set of keys underneath each parent object so
        // that we can quickly retrieve the set of keys which belong to each
        // object.
        [[entry.acl_object_type, entry.object_id]]
          .concat(
            [...entry.parent_cols.entries()].map(
              ([parent_object_type, parent_entry]) => [
                parent_object_type,
                parent_entry.id,
              ],
            ),
          )
          .forEach(([setAclObjectType, setObjectId]) => {
            const setKey = ObjectCache.makeSetKey(
              objectCacheEntryOrgId(entry),
              setAclObjectType,
              setObjectId,
            );
            promises.push(redisClient.sAdd(setKey, cacheKey));
            promises.push(redisClient.expire(setKey, this.expiration_time));
          });

        return promises;
      }),
    );

    if (!ignoreMissingObjects) {
      for (const id of objectIds) {
        if (!idToValue[id]) {
          throw new InternalServerError(`Object ${id} was not retrieved`);
        }
      }
    }
    return idToValue;
  }

  public async flushEntries({
    orgId,
    aclObjectType,
    objectId,
  }: {
    orgId: string;
    aclObjectType: string;
    objectId: string;
  }) {
    const setKey = ObjectCache.makeSetKey(orgId, aclObjectType, objectId);
    const redisClient = await getRedis();
    const matchingKeys = await redisClient.sMembers(setKey);
    if (matchingKeys.length > 0) {
      await Promise.all([
        redisClient.del(matchingKeys),
        redisClient.sRem(setKey, matchingKeys),
      ]);
    }
  }

  private static makeCacheKey(
    aclObjectType: string,
    overrideRestrictObjectType: string | undefined,
    objectId: string,
    token: string,
    allowSysadminRoles: string[] | undefined,
    includeDeletedObjects: boolean | undefined,
  ): string {
    return [
      "object_cache:",
      aclObjectType,
      overrideRestrictObjectType ?? "",
      objectId,
      sha1(token),
    ]
      .concat((allowSysadminRoles ?? []).map((r) => `allow_sysadmin_${r}`))
      .concat(includeDeletedObjects ? ["include_deleted_objects"] : [])
      .join(";");
  }

  private static makeObjectCacheEntry({
    aclObjectType,
    overrideRestrictObjectType,
    res,
  }: {
    aclObjectType: string;
    overrideRestrictObjectType: string | undefined;
    res: z.infer<typeof objectResponseSchema.element>;
  }): { rawEntry: ObjectCacheEntryRaw; entry: ObjectCacheEntry } {
    const rawEntry: ObjectCacheEntryRaw = {
      acl_object_type: aclObjectType,
      override_restrict_object_type: overrideRestrictObjectType,
      ...res,
    };
    const entry = convertToObjectCacheEntry(rawEntry);
    return { rawEntry, entry };
  }

  private static makeSetKey(
    orgId: string | undefined,
    aclObjectType: string,
    objectId: string,
  ): string {
    return [
      REDIS_OBJECT_CACHE_KEYS_SET_PREFIX,
      orgId || "unknown",
      aclObjectType,
      objectId,
    ].join(";");
  }
}
export const OBJECT_CACHE = new ObjectCache();

const flushObjectCacheParamsSchema = z.object({
  org_id: z.string(),
  object_type: z.string(),
  object_id: z.string(),
});

export async function flushObjectCache(me: Me, ctxData: unknown) {
  const params = wrapZodError(() =>
    flushObjectCacheParamsSchema.parse(ctxData),
  );

  // Make sure the requester belongs to the org they are trying to flush.
  if (!me.organizations.find((x) => x.id === params.org_id)) {
    throw new ForbiddenError(
      "User does not belong to organization being flushed",
    );
  }

  await OBJECT_CACHE.flushEntries({
    orgId: params.org_id,
    aclObjectType: params.object_type,
    objectId: params.object_id,
  });
}

const flushOrgObjectCacheParamsSchema = z.object({
  org_name: z.string(),
});

export async function flushOrgObjectCache(me: Me, ctxData: unknown) {
  const params = wrapZodError(() =>
    flushOrgObjectCacheParamsSchema.parse(ctxData),
  );

  // Make sure the requester belongs to the org they are trying to flush.
  const orgEntry = me.organizations.find((x) => x.name === params.org_name);
  if (!orgEntry) {
    throw new ForbiddenError(
      "User does not belong to organization being flushed",
    );
  }

  await OBJECT_CACHE.flushEntries({
    orgId: orgEntry.id,
    aclObjectType: "organization",
    objectId: orgEntry.id,
  });
}
