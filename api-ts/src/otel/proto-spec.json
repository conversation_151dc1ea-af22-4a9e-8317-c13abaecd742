{"opentelemetry/proto/trace/v1/trace.proto": "// Copyright 2019, OpenTelemetry Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\nsyntax = \"proto3\";\n\npackage opentelemetry.proto.trace.v1;\n\nimport \"opentelemetry/proto/common/v1/common.proto\";\nimport \"opentelemetry/proto/resource/v1/resource.proto\";\n\noption csharp_namespace = \"OpenTelemetry.Proto.Trace.V1\";\noption java_multiple_files = true;\noption java_package = \"io.opentelemetry.proto.trace.v1\";\noption java_outer_classname = \"TraceProto\";\noption go_package = \"go.opentelemetry.io/proto/otlp/trace/v1\";\n\n// TracesData represents the traces data that can be stored in a persistent storage,\n// OR can be embedded by other protocols that transfer OTLP traces data but do\n// not implement the OTLP protocol.\n//\n// The main difference between this message and collector protocol is that\n// in this message there will not be any \"control\" or \"metadata\" specific to\n// OTLP protocol.\n//\n// When new fields are added into this message, the OTLP request MUST be updated\n// as well.\nmessage TracesData {\n  // An array of ResourceSpans.\n  // For data coming from a single resource this array will typically contain\n  // one element. Intermediary nodes that receive data from multiple origins\n  // typically batch the data before forwarding further and in that case this\n  // array will contain multiple elements.\n  repeated ResourceSpans resource_spans = 1;\n}\n\n// A collection of ScopeSpans from a Resource.\nmessage ResourceSpans {\n  reserved 1000;\n\n  // The resource for the spans in this message.\n  // If this field is not set then no resource info is known.\n  opentelemetry.proto.resource.v1.Resource resource = 1;\n\n  // A list of ScopeSpans that originate from a resource.\n  repeated ScopeSpans scope_spans = 2;\n\n  // The Schema URL, if known. This is the identifier of the Schema that the resource data\n  // is recorded in. To learn more about Schema URL see\n  // https://opentelemetry.io/docs/specs/otel/schemas/#schema-url\n  // This schema_url applies to the data in the \"resource\" field. It does not apply\n  // to the data in the \"scope_spans\" field which have their own schema_url field.\n  string schema_url = 3;\n}\n\n// A collection of Spans produced by an InstrumentationScope.\nmessage ScopeSpans {\n  // The instrumentation scope information for the spans in this message.\n  // Semantically when InstrumentationScope isn't set, it is equivalent with\n  // an empty instrumentation scope name (unknown).\n  opentelemetry.proto.common.v1.InstrumentationScope scope = 1;\n\n  // A list of Spans that originate from an instrumentation scope.\n  repeated Span spans = 2;\n\n  // The Schema URL, if known. This is the identifier of the Schema that the span data\n  // is recorded in. To learn more about Schema URL see\n  // https://opentelemetry.io/docs/specs/otel/schemas/#schema-url\n  // This schema_url applies to all spans and span events in the \"spans\" field.\n  string schema_url = 3;\n}\n\n// A Span represents a single operation performed by a single component of the system.\n//\n// The next available field id is 17.\nmessage Span {\n  // A unique identifier for a trace. All spans from the same trace share\n  // the same `trace_id`. The ID is a 16-byte array. An ID with all zeroes OR\n  // of length other than 16 bytes is considered invalid (empty string in OTLP/JSON\n  // is zero-length and thus is also invalid).\n  //\n  // This field is required.\n  bytes trace_id = 1;\n\n  // A unique identifier for a span within a trace, assigned when the span\n  // is created. The ID is an 8-byte array. An ID with all zeroes OR of length\n  // other than 8 bytes is considered invalid (empty string in OTLP/JSON\n  // is zero-length and thus is also invalid).\n  //\n  // This field is required.\n  bytes span_id = 2;\n\n  // trace_state conveys information about request position in multiple distributed tracing graphs.\n  // It is a trace_state in w3c-trace-context format: https://www.w3.org/TR/trace-context/#tracestate-header\n  // See also https://github.com/w3c/distributed-tracing for more details about this field.\n  string trace_state = 3;\n\n  // The `span_id` of this span's parent span. If this is a root span, then this\n  // field must be empty. The ID is an 8-byte array.\n  bytes parent_span_id = 4;\n\n  // Flags, a bit field.\n  //\n  // Bits 0-7 (8 least significant bits) are the trace flags as defined in W3C Trace\n  // Context specification. To read the 8-bit W3C trace flag, use\n  // `flags & SPAN_FLAGS_TRACE_FLAGS_MASK`.\n  //\n  // See https://www.w3.org/TR/trace-context-2/#trace-flags for the flag definitions.\n  //\n  // Bits 8 and 9 represent the 3 states of whether a span's parent\n  // is remote. The states are (unknown, is not remote, is remote).\n  // To read whether the value is known, use `(flags & SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK) != 0`.\n  // To read whether the span is remote, use `(flags & SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK) != 0`.\n  //\n  // When creating span messages, if the message is logically forwarded from another source\n  // with an equivalent flags fields (i.e., usually another OTLP span message), the field SHOULD\n  // be copied as-is. If creating from a source that does not have an equivalent flags field\n  // (such as a runtime representation of an OpenTelemetry span), the high 22 bits MUST\n  // be set to zero.\n  // Readers MUST NOT assume that bits 10-31 (22 most significant bits) will be zero.\n  //\n  // [Optional].\n  fixed32 flags = 16;\n\n  // A description of the span's operation.\n  //\n  // For example, the name can be a qualified method name or a file name\n  // and a line number where the operation is called. A best practice is to use\n  // the same display name at the same call point in an application.\n  // This makes it easier to correlate spans in different traces.\n  //\n  // This field is semantically required to be set to non-empty string.\n  // Empty value is equivalent to an unknown span name.\n  //\n  // This field is required.\n  string name = 5;\n\n  // SpanKind is the type of span. Can be used to specify additional relationships between spans\n  // in addition to a parent/child relationship.\n  enum SpanKind {\n    // Unspecified. Do NOT use as default.\n    // Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED.\n    SPAN_KIND_UNSPECIFIED = 0;\n\n    // Indicates that the span represents an internal operation within an application,\n    // as opposed to an operation happening at the boundaries. Default value.\n    SPAN_KIND_INTERNAL = 1;\n\n    // Indicates that the span covers server-side handling of an RPC or other\n    // remote network request.\n    SPAN_KIND_SERVER = 2;\n\n    // Indicates that the span describes a request to some remote service.\n    SPAN_KIND_CLIENT = 3;\n\n    // Indicates that the span describes a producer sending a message to a broker.\n    // Unlike CLIENT and SERVER, there is often no direct critical path latency relationship\n    // between producer and consumer spans. A PRODUCER span ends when the message was accepted\n    // by the broker while the logical processing of the message might span a much longer time.\n    SPAN_KIND_PRODUCER = 4;\n\n    // Indicates that the span describes consumer receiving a message from a broker.\n    // Like the PRODUCER kind, there is often no direct critical path latency relationship\n    // between producer and consumer spans.\n    SPAN_KIND_CONSUMER = 5;\n  }\n\n  // Distinguishes between spans generated in a particular context. For example,\n  // two spans with the same name may be distinguished using `CLIENT` (caller)\n  // and `SERVER` (callee) to identify queueing latency associated with the span.\n  SpanKind kind = 6;\n\n  // start_time_unix_nano is the start time of the span. On the client side, this is the time\n  // kept by the local machine where the span execution starts. On the server side, this\n  // is the time when the server's application handler starts running.\n  // Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.\n  //\n  // This field is semantically required and it is expected that end_time >= start_time.\n  fixed64 start_time_unix_nano = 7;\n\n  // end_time_unix_nano is the end time of the span. On the client side, this is the time\n  // kept by the local machine where the span execution ends. On the server side, this\n  // is the time when the server application handler stops running.\n  // Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.\n  //\n  // This field is semantically required and it is expected that end_time >= start_time.\n  fixed64 end_time_unix_nano = 8;\n\n  // attributes is a collection of key/value pairs. Note, global attributes\n  // like server name can be set using the resource API. Examples of attributes:\n  //\n  //     \"/http/user_agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36\"\n  //     \"/http/server_latency\": 300\n  //     \"example.com/myattribute\": true\n  //     \"example.com/score\": 10.239\n  //\n  // The OpenTelemetry API specification further restricts the allowed value types:\n  // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/common/README.md#attribute\n  // Attribute keys MUST be unique (it is not allowed to have more than one\n  // attribute with the same key).\n  repeated opentelemetry.proto.common.v1.KeyValue attributes = 9;\n\n  // dropped_attributes_count is the number of attributes that were discarded. Attributes\n  // can be discarded because their keys are too long or because there are too many\n  // attributes. If this value is 0, then no attributes were dropped.\n  uint32 dropped_attributes_count = 10;\n\n  // Event is a time-stamped annotation of the span, consisting of user-supplied\n  // text description and key-value pairs.\n  message Event {\n    // time_unix_nano is the time the event occurred.\n    fixed64 time_unix_nano = 1;\n\n    // name of the event.\n    // This field is semantically required to be set to non-empty string.\n    string name = 2;\n\n    // attributes is a collection of attribute key/value pairs on the event.\n    // Attribute keys MUST be unique (it is not allowed to have more than one\n    // attribute with the same key).\n    repeated opentelemetry.proto.common.v1.KeyValue attributes = 3;\n\n    // dropped_attributes_count is the number of dropped attributes. If the value is 0,\n    // then no attributes were dropped.\n    uint32 dropped_attributes_count = 4;\n  }\n\n  // events is a collection of Event items.\n  repeated Event events = 11;\n\n  // dropped_events_count is the number of dropped events. If the value is 0, then no\n  // events were dropped.\n  uint32 dropped_events_count = 12;\n\n  // A pointer from the current span to another span in the same trace or in a\n  // different trace. For example, this can be used in batching operations,\n  // where a single batch handler processes multiple requests from different\n  // traces or when the handler receives a request from a different project.\n  message Link {\n    // A unique identifier of a trace that this linked span is part of. The ID is a\n    // 16-byte array.\n    bytes trace_id = 1;\n\n    // A unique identifier for the linked span. The ID is an 8-byte array.\n    bytes span_id = 2;\n\n    // The trace_state associated with the link.\n    string trace_state = 3;\n\n    // attributes is a collection of attribute key/value pairs on the link.\n    // Attribute keys MUST be unique (it is not allowed to have more than one\n    // attribute with the same key).\n    repeated opentelemetry.proto.common.v1.KeyValue attributes = 4;\n\n    // dropped_attributes_count is the number of dropped attributes. If the value is 0,\n    // then no attributes were dropped.\n    uint32 dropped_attributes_count = 5;\n\n    // Flags, a bit field.\n    //\n    // Bits 0-7 (8 least significant bits) are the trace flags as defined in W3C Trace\n    // Context specification. To read the 8-bit W3C trace flag, use\n    // `flags & SPAN_FLAGS_TRACE_FLAGS_MASK`.\n    //\n    // See https://www.w3.org/TR/trace-context-2/#trace-flags for the flag definitions.\n    //\n    // Bits 8 and 9 represent the 3 states of whether the link is remote.\n    // The states are (unknown, is not remote, is remote).\n    // To read whether the value is known, use `(flags & SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK) != 0`.\n    // To read whether the link is remote, use `(flags & SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK) != 0`.\n    //\n    // Readers MUST NOT assume that bits 10-31 (22 most significant bits) will be zero.\n    // When creating new spans, bits 10-31 (most-significant 22-bits) MUST be zero.\n    //\n    // [Optional].\n    fixed32 flags = 6;\n  }\n\n  // links is a collection of Links, which are references from this span to a span\n  // in the same or different trace.\n  repeated Link links = 13;\n\n  // dropped_links_count is the number of dropped links after the maximum size was\n  // enforced. If this value is 0, then no links were dropped.\n  uint32 dropped_links_count = 14;\n\n  // An optional final status for this span. Semantically when Status isn't set, it means\n  // span's status code is unset, i.e. assume STATUS_CODE_UNSET (code = 0).\n  Status status = 15;\n}\n\n// The Status type defines a logical error model that is suitable for different\n// programming environments, including REST APIs and RPC APIs.\nmessage Status {\n  reserved 1;\n\n  // A developer-facing human readable error message.\n  string message = 2;\n\n  // For the semantics of status codes see\n  // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/api.md#set-status\n  enum StatusCode {\n    // The default status.\n    STATUS_CODE_UNSET               = 0;\n    // The Span has been validated by an Application developer or Operator to\n    // have completed successfully.\n    STATUS_CODE_OK                  = 1;\n    // The Span contains an error.\n    STATUS_CODE_ERROR               = 2;\n  };\n\n  // The status code.\n  StatusCode code = 3;\n}\n\n// SpanFlags represents constants used to interpret the\n// Span.flags field, which is protobuf 'fixed32' type and is to\n// be used as bit-fields. Each non-zero value defined in this enum is\n// a bit-mask.  To extract the bit-field, for example, use an\n// expression like:\n//\n//   (span.flags & SPAN_FLAGS_TRACE_FLAGS_MASK)\n//\n// See https://www.w3.org/TR/trace-context-2/#trace-flags for the flag definitions.\n//\n// Note that Span flags were introduced in version 1.1 of the\n// OpenTelemetry protocol.  Older Span producers do not set this\n// field, consequently consumers should not rely on the absence of a\n// particular flag bit to indicate the presence of a particular feature.\nenum SpanFlags {\n  // The zero value for the enum. Should not be used for comparisons.\n  // Instead use bitwise \"and\" with the appropriate mask as shown above.\n  SPAN_FLAGS_DO_NOT_USE = 0;\n\n  // Bits 0-7 are used for trace flags.\n  SPAN_FLAGS_TRACE_FLAGS_MASK = 0x000000FF;\n\n  // Bits 8 and 9 are used to indicate that the parent span or link span is remote.\n  // Bit 8 (`HAS_IS_REMOTE`) indicates whether the value is known.\n  // Bit 9 (`IS_REMOTE`) indicates whether the span or link is remote.\n  SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK = 0x00000100;\n  SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK = 0x00000200;\n\n  // Bits 10-31 are reserved for future use.\n}\n", "opentelemetry/proto/collector/trace/v1/trace_service.proto": "// Copyright 2019, OpenTelemetry Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\nsyntax = \"proto3\";\n\npackage opentelemetry.proto.collector.trace.v1;\n\nimport \"opentelemetry/proto/trace/v1/trace.proto\";\n\noption csharp_namespace = \"OpenTelemetry.Proto.Collector.Trace.V1\";\noption java_multiple_files = true;\noption java_package = \"io.opentelemetry.proto.collector.trace.v1\";\noption java_outer_classname = \"TraceServiceProto\";\noption go_package = \"go.opentelemetry.io/proto/otlp/collector/trace/v1\";\n\n// Service that can be used to push spans between one Application instrumented with\n// OpenTelemetry and a collector, or between a collector and a central collector (in this\n// case spans are sent/received to/from multiple Applications).\nservice TraceService {\n  // For performance reasons, it is recommended to keep this RPC\n  // alive for the entire life of the application.\n  rpc Export(ExportTraceServiceRequest) returns (ExportTraceServiceResponse) {}\n}\n\nmessage ExportTraceServiceRequest {\n  // An array of ResourceSpans.\n  // For data coming from a single resource this array will typically contain one\n  // element. Intermediary nodes (such as OpenTelemetry Collector) that receive\n  // data from multiple origins typically batch the data before forwarding further and\n  // in that case this array will contain multiple elements.\n  repeated opentelemetry.proto.trace.v1.ResourceSpans resource_spans = 1;\n}\n\nmessage ExportTraceServiceResponse {\n  // The details of a partially successful export request.\n  //\n  // If the request is only partially accepted\n  // (i.e. when the server accepts only parts of the data and rejects the rest)\n  // the server MUST initialize the `partial_success` field and MUST\n  // set the `rejected_<signal>` with the number of items it rejected.\n  //\n  // Servers MAY also make use of the `partial_success` field to convey\n  // warnings/suggestions to senders even when the request was fully accepted.\n  // In such cases, the `rejected_<signal>` MUST have a value of `0` and\n  // the `error_message` MUST be non-empty.\n  //\n  // A `partial_success` message with an empty value (rejected_<signal> = 0 and\n  // `error_message` = \"\") is equivalent to it not being set/present. Senders\n  // SHOULD interpret it the same way as in the full success case.\n  ExportTracePartialSuccess partial_success = 1;\n}\n\nmessage ExportTracePartialSuccess {\n  // The number of rejected spans.\n  //\n  // A `rejected_<signal>` field holding a `0` value indicates that the\n  // request was fully accepted.\n  int64 rejected_spans = 1;\n\n  // A developer-facing human-readable message in English. It should be used\n  // either to explain why the server rejected parts of the data during a partial\n  // success or to convey warnings/suggestions during a full success. The message\n  // should offer guidance on how users can address such issues.\n  //\n  // error_message is an optional field. An error_message with an empty value\n  // is equivalent to it not being set.\n  string error_message = 2;\n}\n", "opentelemetry/proto/common/v1/common.proto": "// Copyright 2019, OpenTelemetry Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\nsyntax = \"proto3\";\n\npackage opentelemetry.proto.common.v1;\n\noption csharp_namespace = \"OpenTelemetry.Proto.Common.V1\";\noption java_multiple_files = true;\noption java_package = \"io.opentelemetry.proto.common.v1\";\noption java_outer_classname = \"CommonProto\";\noption go_package = \"go.opentelemetry.io/proto/otlp/common/v1\";\n\n// AnyValue is used to represent any type of attribute value. AnyValue may contain a\n// primitive value such as a string or integer or it may contain an arbitrary nested\n// object containing arrays, key-value lists and primitives.\nmessage AnyValue {\n  // The value is one of the listed fields. It is valid for all values to be unspecified\n  // in which case this AnyValue is considered to be \"empty\".\n  oneof value {\n    string string_value = 1;\n    bool bool_value = 2;\n    int64 int_value = 3;\n    double double_value = 4;\n    ArrayValue array_value = 5;\n    KeyValueList kvlist_value = 6;\n    bytes bytes_value = 7;\n  }\n}\n\n// ArrayValue is a list of AnyValue messages. We need ArrayValue as a message\n// since oneof in AnyValue does not allow repeated fields.\nmessage ArrayValue {\n  // Array of values. The array may be empty (contain 0 elements).\n  repeated AnyValue values = 1;\n}\n\n// KeyValueList is a list of KeyValue messages. We need KeyValueList as a message\n// since `oneof` in AnyValue does not allow repeated fields. Everywhere else where we need\n// a list of KeyValue messages (e.g. in Span) we use `repeated KeyValue` directly to\n// avoid unnecessary extra wrapping (which slows down the protocol). The 2 approaches\n// are semantically equivalent.\nmessage KeyValueList {\n  // A collection of key/value pairs of key-value pairs. The list may be empty (may\n  // contain 0 elements).\n  // The keys MUST be unique (it is not allowed to have more than one\n  // value with the same key).\n  repeated KeyValue values = 1;\n}\n\n// KeyValue is a key-value pair that is used to store Span attributes, Link\n// attributes, etc.\nmessage KeyValue {\n  string key = 1;\n  AnyValue value = 2;\n}\n\n// InstrumentationScope is a message representing the instrumentation scope information\n// such as the fully qualified name and version.\nmessage InstrumentationScope {\n  // An empty instrumentation scope name means the name is unknown.\n  string name = 1;\n  string version = 2;\n\n  // Additional attributes that describe the scope. [Optional].\n  // Attribute keys MUST be unique (it is not allowed to have more than one\n  // attribute with the same key).\n  repeated KeyValue attributes = 3;\n  uint32 dropped_attributes_count = 4;\n}\n", "opentelemetry/proto/resource/v1/resource.proto": "// Copyright 2019, OpenTelemetry Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\nsyntax = \"proto3\";\n\npackage opentelemetry.proto.resource.v1;\n\nimport \"opentelemetry/proto/common/v1/common.proto\";\n\noption csharp_namespace = \"OpenTelemetry.Proto.Resource.V1\";\noption java_multiple_files = true;\noption java_package = \"io.opentelemetry.proto.resource.v1\";\noption java_outer_classname = \"ResourceProto\";\noption go_package = \"go.opentelemetry.io/proto/otlp/resource/v1\";\n\n// Resource information.\nmessage Resource {\n  // Set of attributes that describe the resource.\n  // Attribute keys MUST be unique (it is not allowed to have more than one\n  // attribute with the same key).\n  repeated opentelemetry.proto.common.v1.KeyValue attributes = 1;\n\n  // dropped_attributes_count is the number of dropped attributes. If the value is 0, then\n  // no attributes were dropped.\n  uint32 dropped_attributes_count = 2;\n}\n"}