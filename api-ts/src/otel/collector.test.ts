import { describe, expect, test } from "vitest";
import { otelTraceToRows } from "./collector";

describe("otel collector metrics", () => {
  test("should process spans and emit metrics without errors", () => {
    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                {
                  traceId: new Uint8Array([
                    0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22,
                    0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
                  ]),
                  spanId: new Uint8Array([
                    0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11,
                  ]),
                  name: "test_span",
                  startTimeUnixNano: "1640995200000000000",
                  endTimeUnixNano: "1640995201500000000",
                  attributes: [
                    {
                      key: "braintrust.input_json",
                      value: { stringValue: JSON.stringify("test input") },
                    },
                    {
                      key: "braintrust.output_json",
                      value: { stringValue: JSON.stringify("test output") },
                    },
                  ],
                  status: { code: 1 },
                },
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project");

    // Verify the basic processing worked
    expect(result.rowsByParent.size).toBe(1);
    expect(result.rejectedSpans).toHaveLength(0);

    // Verify we have processed a row with braintrust data
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(1);
    expect(rows[0]).toMatchObject({
      input: [{ role: "user", content: "test input" }],
      output: [{ role: "assistant", content: "test output" }],
    });
  });

  test("should process spans without default parent and reject spans appropriately", () => {
    const traceWithoutParent = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                {
                  traceId: new Uint8Array([
                    0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22,
                    0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
                  ]),
                  spanId: new Uint8Array([
                    0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11,
                  ]),
                  name: "test_span",
                  startTimeUnixNano: "1640995200000000000",
                  endTimeUnixNano: "1640995201500000000",
                  attributes: [
                    {
                      key: "braintrust.input_json",
                      value: { stringValue: JSON.stringify("test input") },
                    },
                  ],
                  status: { code: 1 },
                },
              ],
            },
          ],
        },
      ],
    };

    // Without a default parent, spans should be rejected
    const result = otelTraceToRows(traceWithoutParent); // no default parent

    expect(result.rowsByParent.size).toBe(0);
    expect(result.rejectedSpans).toHaveLength(1);
    expect(result.rejectedSpans[0].error).toContain("No parent specified");
  });
});
