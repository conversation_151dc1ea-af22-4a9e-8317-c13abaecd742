import { get, isEmpty } from "lodash";
import { SpanSpec } from "./attributes";
import type { Message } from "@braintrust/typespecs";

export const gcpAgentSpec: SpanSpec = {
  input(attributes: unknown) {
    const llmRequest = tryNonEmptyJSON(
      get(attributes, "gcp.vertex.agent.llm_request"),
    );
    if (!isEmpty(llmRequest)) {
      return {
        handled: true,
        value: {
          isLLM: true,
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          data: llmRequest as Message[],
        },
        deleteKeys: new Set(),
      };
    }

    const toolCall = tryNonEmptyJSON(
      get(attributes, "gcp.vertex.agent.tool_call_args"),
    );
    if (!isEmpty(toolCall)) {
      return {
        handled: true,
        value: { isLLM: false, data: toolCall },
        deleteKeys: new Set(),
      };
    }

    return { handled: false };
  },

  output(attributes: unknown) {
    const llmResponse = tryNonEmptyJSON(
      get(attributes, "gcp.vertex.agent.llm_response"),
    );
    if (!isEmpty(llmResponse)) {
      return {
        handled: true,
        value: {
          isLLM: true,
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          data: llmResponse as Message[],
        },
        deleteKeys: new Set(),
      };
    }

    const toolCall = tryNonEmptyJSON(
      get(attributes, "gcp.vertex.agent.tool_response"),
    );
    if (!isEmpty(toolCall)) {
      return {
        handled: true,
        value: { isLLM: false, data: toolCall },
        deleteKeys: new Set(),
      };
    }

    return { handled: false };
  },
};

const tryNonEmptyJSON = (str: string | undefined): unknown => {
  try {
    const data = typeof str === "string" ? JSON.parse(str || "") : str;
    if (!isEmpty(data)) {
      return data;
    }
  } catch {
    // Ignore parsing errors
  }
};
