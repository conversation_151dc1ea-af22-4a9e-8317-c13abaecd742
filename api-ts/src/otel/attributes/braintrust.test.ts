import { convertAttributesToSpan } from ".";
import { otelSpanToRow } from "../collector";
import { describe, expect, test } from "vitest";

describe("braintrust", () => {
  describe("convertAttributesToSpan", () => {
    test("handle eval span with expected", () => {
      const input = {
        "braintrust.expected": JSON.stringify({ a: [1, 2, 3] }),
        "braintrust.span_attributes": JSON.stringify({ type: "eval" }),
        "braintrust.input_json": JSON.stringify("test input"),
        "braintrust.output_json": JSON.stringify("test output"),
      };

      const expectedSpan = {
        input: [{ content: "test input", role: "user" }],
        output: [{ content: "test output", role: "assistant" }],
        metadata: {},
        metrics: {},
        expected: { a: [1, 2, 3] },
        span_attributes: { type: "eval" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(
        new Set([
          "braintrust.expected",
          "braintrust.input",
          "braintrust.input_json",
          "braintrust.output",
          "braintrust.output_json",
          "braintrust.span_attributes",
        ]),
      );
    });

    test("handles scores", () => {
      const input = {
        "braintrust.scores": JSON.stringify({
          "gut-check": 0.95,
          hunch: 0.9,
        }),
        "braintrust.span_attributes": JSON.stringify({ type: "score" }),
      };

      const expectedSpan = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        scores: {
          "gut-check": 0.95,
          hunch: 0.9,
        },
        span_attributes: { type: "score" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(
        new Set(["braintrust.span_attributes", "braintrust.scores"]),
      );
    });

    test("handles basic input/output with metadata, metrics, tags and type", () => {
      const input = {
        "braintrust.input_json": JSON.stringify("test input"),
        "braintrust.output_json": JSON.stringify("test output"),
        "braintrust.metadata": JSON.stringify({ source: "test" }),
        "braintrust.metrics": JSON.stringify({ score: 0.95 }),
        "braintrust.span_attributes": JSON.stringify({ type: "eval" }),
      };

      const expectedSpan = {
        input: [
          {
            content: "test input",
            role: "user",
          },
        ],
        output: [
          {
            content: "test output",
            role: "assistant",
          },
        ],
        metadata: { source: "test" },
        metrics: { score: 0.95 },
        span_attributes: { type: "eval" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(
        new Set([
          "braintrust.input",
          "braintrust.input_json",
          "braintrust.output",
          "braintrust.output_json",
          "braintrust.metadata",
          "braintrust.metrics",
          "braintrust.span_attributes",
        ]),
      );
    });

    test("handles message arrays with role/content structure", () => {
      const input = {
        "braintrust.input": [
          { role: "user", content: "Hello" },
          { role: "system", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        "braintrust.output": [
          { role: "assistant", content: "How can I help?" },
        ],
      };

      const expectedSpan = {
        input: [
          { role: "user", content: "Hello" },
          { role: "system", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        output: [{ role: "assistant", content: "How can I help?" }],
        metadata: {},
        metrics: {},
        span_attributes: { type: "llm" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(
        new Set([
          "braintrust.input",
          "braintrust.input_json",
          "braintrust.output",
          "braintrust.output_json",
        ]),
      );
    });

    test("handles JSON serialized input/output", () => {
      const input = {
        "braintrust.input_json": JSON.stringify("test input json"),
        "braintrust.output_json": JSON.stringify("test output json"),
        "braintrust.metadata": JSON.stringify({ source: "json test" }),
        "braintrust.metrics": JSON.stringify({ score: 0.85 }),
      };

      const expectedSpan = {
        input: [{ content: "test input json", role: "user" }],
        output: [{ content: "test output json", role: "assistant" }],
        metadata: { source: "json test" },
        metrics: { score: 0.85 },
        span_attributes: { type: "llm" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(
        new Set([
          "braintrust.input",
          "braintrust.input_json",
          "braintrust.output",
          "braintrust.output_json",
          "braintrust.metadata",
          "braintrust.metrics",
        ]),
      );
    });

    test("handles invalid JSON gracefully", () => {
      const input = {
        "braintrust.input_json": "invalid json{",
        "braintrust.output_json": "[not valid json",
        "braintrust.metadata": "not an object",
        "braintrust.metrics": JSON.stringify(["not", "metrics"]),
      };

      const expectedSpan = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        span_attributes: { type: "task" }, // FIXME[matt] this is weird
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(new Set());
    });

    test("handles empty values", () => {
      const input = {};

      const expectedSpan = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        span_attributes: { type: "task" }, // FIXME[matt] this is weird
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(new Set());
    });

    test("handles nested metadata", () => {
      const input = {
        "braintrust.metadata": JSON.stringify({
          model: {
            name: "gpt-4",
            version: "v2",
          },
          tags: ["test", "demo"],
        }),
      };

      const expectedSpan = {
        input: undefined,
        output: undefined,
        metadata: {
          model: {
            name: "gpt-4",
            version: "v2",
          },
          tags: ["test", "demo"],
        },
        metrics: {},
        span_attributes: { type: "task" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(new Set(["braintrust.metadata"]));
    });

    test("handles complex metrics", () => {
      const input = {
        "braintrust.metrics": JSON.stringify({
          accuracy: 0.95,
          latency_ms: 150,
          tokens: 512,
        }),
      };

      const expectedSpan = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {
          accuracy: 0.95,
          latency_ms: 150,
          tokens: 512,
        },
        span_attributes: { type: "task" },
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(new Set(["braintrust.metrics"]));
    });

    test("handles tags", () => {
      const input = {
        "braintrust.tags": ["test", "demo"],
      };

      const expectedSpan = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        span_attributes: { type: "task" },
        tags: ["test", "demo"],
      };

      const result = convertAttributesToSpan(input);
      expect(result.span).toEqual(expectedSpan);
      expect(result.keysToDelete).toEqual(new Set(["braintrust.tags"]));
    });
  });

  // Helper function to create OTEL span with Braintrust attributes
  function createBraintrustOtelSpan(attributes: Record<string, unknown>) {
    return {
      traceId: new Uint8Array([
        0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22, 0x33, 0x44,
        0x55, 0x66, 0x77, 0x88,
      ]),
      spanId: new Uint8Array([0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11]),
      name: "braintrust_span",
      startTimeUnixNano: "1640995200000000000",
      endTimeUnixNano: "1640995201500000000",
      attributes: Object.entries(attributes).map(([key, value]) => ({
        key,
        value: Array.isArray(value)
          ? {
              arrayValue: {
                values: value.map((v) => ({ stringValue: String(v) })),
              },
            }
          : typeof value === "number"
            ? Number.isInteger(value)
              ? { intValue: value }
              : { doubleValue: value }
            : { stringValue: String(value) },
      })),
      status: { code: 1 },
    };
  }

  describe("otelSpanToRow integration", () => {
    test("comprehensive evaluation span - remaps and removes braintrust.* attributes", () => {
      const otelSpan = createBraintrustOtelSpan({
        "braintrust.input_json": JSON.stringify([
          { role: "user", content: "What is 2+2?" },
          { role: "assistant", content: "4" },
        ]),
        "braintrust.output_json": JSON.stringify([
          { role: "assistant", content: "The answer is 4" },
        ]),
        "braintrust.expected": JSON.stringify({ correct_answer: 4 }),
        "braintrust.metadata": JSON.stringify({
          model: "gpt-4",
          temperature: 0.5,
          evaluation_run: "math_test_v1",
        }),
        "braintrust.metrics": JSON.stringify({
          latency_ms: 150,
          input_tokens: 12,
          output_tokens: 8,
        }),
        "braintrust.span_attributes": JSON.stringify({
          type: "eval",
          category: "math",
        }),
        "braintrust.scores": JSON.stringify({
          accuracy: 1.0,
          helpfulness: 0.95,
        }),
        "braintrust.tags": ["math", "basic_arithmetic"],
        // Additional non-braintrust attributes that should be preserved
        "custom.attribute": "should_remain",
        "system.version": "1.0.0",
      });

      const { row, fieldStats } = otelSpanToRow(otelSpan);

      expect(row).toMatchObject({
        input: [
          { role: "user", content: "What is 2+2?" },
          { role: "assistant", content: "4" },
        ],
        output: [{ role: "assistant", content: "The answer is 4" }],
        expected: { correct_answer: 4 },
        scores: {
          accuracy: 1.0,
          helpfulness: 0.95,
        },
        tags: ["math", "basic_arithmetic"],
        metadata: {
          // Braintrust metadata should be extracted here
          model: "gpt-4",
          temperature: 0.5,
          evaluation_run: "math_test_v1",
          // Non-braintrust attributes should remain
          "custom.attribute": "should_remain",
          "system.version": "1.0.0",
          // Braintrust.* attributes should NOT be here (deleted after processing)
        },
        metrics: {
          // Braintrust metrics should be extracted here
          latency_ms: 150,
          input_tokens: 12,
          output_tokens: 8,
        },
        span_attributes: {
          name: "braintrust_span",
          // Braintrust span attributes should be extracted here
          type: "eval",
          category: "math",
        },
        error: null,
      });

      expect(fieldStats.toObject()).toEqual({
        braintrust: { ok: 8, errs: 0 },
      });

      // CRITICAL: Verify braintrust.* attributes are NOT in metadata (they were deleted)
      expect(row!.metadata).not.toHaveProperty("braintrust.input_json");
      expect(row!.metadata).not.toHaveProperty("braintrust.output_json");
      expect(row!.metadata).not.toHaveProperty("braintrust.expected");
      expect(row!.metadata).not.toHaveProperty("braintrust.metadata");
      expect(row!.metadata).not.toHaveProperty("braintrust.metrics");
      expect(row!.metadata).not.toHaveProperty("braintrust.span_attributes");
      expect(row!.metadata).not.toHaveProperty("braintrust.scores");
      expect(row!.metadata).not.toHaveProperty("braintrust.tags");

      // Verify non-braintrust attributes are preserved
      expect(row!.metadata).toHaveProperty("custom.attribute", "should_remain");
      expect(row!.metadata).toHaveProperty("system.version", "1.0.0");
    });

    test("mixed serialization formats - handles both arrays and JSON strings", () => {
      const otelSpan = createBraintrustOtelSpan({
        // Direct JSON format for complex objects
        "braintrust.input_json": JSON.stringify([
          { role: "system", content: "You are a helpful assistant" },
          { role: "user", content: "Hello" },
        ]),
        // JSON string format
        "braintrust.output_json": JSON.stringify([
          { role: "assistant", content: "Hi there! How can I help you?" },
        ]),
        "braintrust.metadata": JSON.stringify({
          session_id: "sess_123",
          user_id: "user_456",
        }),
        "braintrust.span_attributes": JSON.stringify({
          type: "chat",
          provider: "openai",
        }),
        // Other attributes that should remain
        "openai.model": "gpt-4",
        "openai.temperature": 0.7,
      });

      const { row, fieldStats } = otelSpanToRow(otelSpan);

      expect(row).toMatchObject({
        input: [
          { role: "system", content: "You are a helpful assistant" },
          { role: "user", content: "Hello" },
        ],
        output: [
          { role: "assistant", content: "Hi there! How can I help you?" },
        ],
        metadata: {
          // Extracted braintrust metadata
          session_id: "sess_123",
          user_id: "user_456",
          // Non-braintrust attributes preserved
          "openai.model": "gpt-4",
          "openai.temperature": 0.7,
        },
        span_attributes: {
          name: "braintrust_span",
          type: "chat",
          provider: "openai",
        },
      });

      expect(fieldStats.toObject()).toEqual({
        braintrust: { ok: 4, errs: 0 },
      });

      // Verify braintrust attributes were removed
      expect(row!.metadata).not.toHaveProperty("braintrust.input");
      expect(row!.metadata).not.toHaveProperty("braintrust.output_json");
      expect(row!.metadata).not.toHaveProperty("braintrust.metadata");
      expect(row!.metadata).not.toHaveProperty("braintrust.span_attributes");
    });

    test("handles string inputs - converts to message format", () => {
      const otelSpan = createBraintrustOtelSpan({
        "braintrust.input_json": JSON.stringify("Simple text input"),
        "braintrust.output_json": JSON.stringify("Simple text output"),
        "braintrust.metadata": JSON.stringify({
          task_type: "text_generation",
        }),
        "other.field": "preserved_value",
      });

      const { row, fieldStats } = otelSpanToRow(otelSpan);

      expect(row).toMatchObject({
        input: [{ role: "user", content: "Simple text input" }],
        output: [{ role: "assistant", content: "Simple text output" }],
        metadata: {
          task_type: "text_generation",
          "other.field": "preserved_value",
          // braintrust.* fields should be gone
        },
      });

      expect(fieldStats.toObject()).toEqual({
        braintrust: { ok: 3, errs: 0 },
      });

      // Verify cleanup
      expect(row!.metadata).not.toHaveProperty("braintrust.input_json");
      expect(row!.metadata).not.toHaveProperty("braintrust.output_json");
      expect(row!.metadata).not.toHaveProperty("braintrust.metadata");
    });
  });
});
