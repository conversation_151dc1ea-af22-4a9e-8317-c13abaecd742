import { z } from "zod";
import { get } from "lodash";
import { isEmpty, isObject } from "braintrust/util";
import {
  Message,
  OpenAIModelParams,
  ToolCall,
  ChatCompletionTool,
  type ResponseFormatJsonSchema,
} from "@braintrust/typespecs";
import {
  SpanSpec,
  translateModelParams,
  notHandled,
  handled,
} from "./attributes";

export const vercelSpanSpec: SpanSpec = {
  input: (attributes) => {
    const prompt: unknown = get(attributes, "ai.prompt");
    if (!isEmpty(prompt)) {
      const obj = typeof prompt === "string" ? JSON.parse(prompt) : prompt;
      const parsed = vercelPromptSchema.parse(obj);
      return handled({ isLLM: true, data: translateVercelPrompt(parsed) });
    }

    const toolCall: unknown = get(attributes, "ai.toolCall.args");
    if (!isEmpty(toolCall) && typeof toolCall === "string") {
      return handled({ isLLM: false, data: JSON.parse(toolCall) });
    }

    return notHandled;
  },
  output: (attributes) => {
    const content = extractOutputContent(attributes);
    const tool_calls = extractOutputToolCalls(attributes);
    if (content || tool_calls) {
      return handled({
        isLLM: true,
        data: [
          {
            role: "assistant",
            content,
            ...(tool_calls ? { tool_calls } : {}),
          },
        ],
      });
    }

    const toolCall = get(attributes, "ai.toolCall.result");
    if (!isEmpty(toolCall) && typeof toolCall === "string") {
      return handled({ isLLM: false, data: JSON.parse(toolCall) });
    }

    return notHandled;
  },
  metadata: (attributes) => {
    const userMetadata = get(attributes, "ai.telemetry.metadata");

    const model = get(attributes, "ai.model.id");
    const modelParams = translateModelParams(get(attributes, "ai.settings"));

    const schema = get(attributes, "ai.schema");
    if (schema) {
      const parsed_schema = JSON.parse(schema);
      const json_schema: ResponseFormatJsonSchema = {
        name: get(attributes, "ai.schema_name") ?? "response",
        schema: parsed_schema,
      };
      modelParams.response_format = {
        type: "json_schema",
        json_schema,
        name: "response",
      };
    }

    const tools = get(attributes, "ai.prompt.tools");
    if (tools) {
      modelParams.tools = translateVercelTools(tools);
    }

    const toolChoice = get(attributes, "ai.prompt.toolChoice");
    if (toolChoice) {
      modelParams.tool_choice = translateVercelToolChoice(toolChoice);
    }

    const result = {
      ...(isObject(userMetadata) ? userMetadata : {}),
      model,
      ...modelParams,
      tool_call_id: get(attributes, "ai.toolCall.id"),
      function_name: get(attributes, "ai.toolCall.name"),
    };

    // Filter out empty/undefined values
    const filteredResult = Object.fromEntries(
      Object.entries(result).filter(
        ([_, value]) => value !== undefined && value !== null,
      ),
    );

    if (Object.keys(filteredResult).length === 0) {
      return notHandled;
    }

    return handled(filteredResult);
  },
  metrics: (attributes) => {
    const result = {
      prompt_tokens:
        get(attributes, "ai.usage.inputTokens") ??
        get(attributes, "ai.usage.promptTokens"),
      completion_tokens:
        get(attributes, "ai.usage.outputTokens") ??
        get(attributes, "ai.usage.completionTokens"),
      ms_to_first_chunk:
        get(attributes, "ai.stream.msToFirstChunk") ??
        get(attributes, "ai.response.msToFirstChunk"),
      ms_to_finish:
        get(attributes, "ai.stream.msToFinish") ??
        get(attributes, "ai.response.msToFinish"),
    };

    // Filter out undefined/null values
    const filteredResult = Object.fromEntries(
      Object.entries(result).filter(
        ([_, value]) => value !== undefined && value !== null,
      ),
    );

    if (Object.keys(filteredResult).length === 0) {
      return notHandled;
    }

    return handled(filteredResult);
  },
  span_attributes: (attributes) => {
    const toolName = get(attributes, "ai.toolCall.name");
    if (toolName) {
      return handled({
        name: get(attributes, "ai.toolCall.name") ?? undefined,
        type: "tool",
      });
    }
    return notHandled;
  },
};

// Partial zod implementation of `LanguageModelV1Prompt` from "@ai-sdk/provider":
// https://github.com/vercel/ai/blob/65e108f94f40b80890b00ccc12eeb04c792a4b92/packages/provider/src/language-model/v1/language-model-v1-prompt.ts
const vercelImagePartSchema = z.object({
  type: z.literal("image"),
  image: z.union([z.string(), z.instanceof(Uint8Array), z.record(z.number())]),
  mimeType: z.string().optional(),
});

const vercelTextPartSchema = z.object({
  type: z.literal("text"),
  text: z.string(),
});

const vercelToolSchema = z.object({
  type: z.literal("function"),
  name: z.string(),
  description: z.string().optional(),
  parameters: z.record(z.unknown()).optional(),
});

function translateVercelTools(tools: unknown): ChatCompletionTool[] {
  const toolStrs = z.array(z.string()).parse(tools);
  const vercelTools = toolStrs.map((toolStr) =>
    vercelToolSchema.parse(JSON.parse(toolStr)),
  );
  return vercelTools.map((vt) => ({
    type: "function",
    function: {
      name: vt.name,
      description: vt.description,
      parameters: vt.parameters,
    },
  }));
}

const vercelToolChoiceSchema = z.union([
  z.object({
    type: z.literal("auto"),
  }),
  z.object({
    type: z.literal("none"),
  }),
  z.object({
    type: z.literal("required"),
  }),
  z.object({
    type: z.literal("tool"),
    toolName: z.string(),
  }),
]);

function translateVercelToolChoice(
  toolCallStr: string,
): OpenAIModelParams["tool_choice"] {
  const toolChoice = vercelToolChoiceSchema.parse(JSON.parse(toolCallStr));
  switch (toolChoice.type) {
    case "auto":
    case "none":
    case "required":
      return toolChoice.type;
    case "tool":
      return {
        type: "function",
        function: {
          name: toolChoice.toolName,
        },
      };
    default:
      const _exhaustiveCheck: never = toolChoice;
      throw new Error(`Unsupported tool choice type: ${_exhaustiveCheck}`);
  }
}

const vercelToolResultPartSchema = z.object({
  type: z.literal("tool-result"),
  toolCallId: z.string(),
  toolName: z.string(),
  result: z.unknown(),
});

const vercelToolCallSchema = z.object({
  toolCallId: z.string(),
  toolName: z.string(),
  args: z.union([
    z.record(z.unknown()).transform((value) => JSON.stringify(value)),
    z.string(),
  ]),
});
type VercelToolCall = z.infer<typeof vercelToolCallSchema>;

const vercelToolCallPartSchema = z
  .object({
    type: z.literal("tool-call"),
  })
  .and(vercelToolCallSchema);

const vercelMessageSchema = z.union([
  z.object({
    role: z.literal("system"),
    content: z.string(),
  }),
  z.object({
    role: z.literal("user"),
    content: z.union([
      z.array(z.union([vercelTextPartSchema, vercelImagePartSchema])),
      z.string(),
    ]),
  }),
  z.object({
    role: z.literal("assistant"),
    content: z.union([
      z.array(z.union([vercelTextPartSchema, vercelToolCallPartSchema])),
      z.string(),
    ]),
  }),
  z.object({
    role: z.literal("tool"),
    content: z.array(vercelToolResultPartSchema),
  }),
]);
type VercelMessage = z.infer<typeof vercelMessageSchema>;

function translateVercelToolCall(tc: VercelToolCall): ToolCall {
  return {
    type: "function",
    id: tc.toolCallId,
    function: {
      arguments: tc.args,
      name: tc.toolName,
    },
  };
}

function extractOutputContent(attributes: unknown): string | undefined {
  for (const path of [
    "ai.response.text",
    "ai.result.text",
    "ai.response.object",
    "ai.result.object",
  ]) {
    const value = get(attributes, path);
    if (!isEmpty(value)) {
      return value;
    }
  }
}

function extractOutputToolCalls(attributes: unknown): ToolCall[] | undefined {
  for (const path of ["ai.response.toolCalls", "ai.result.toolCalls"]) {
    const value = get(attributes, path);
    if (!isEmpty(value)) {
      if (typeof value !== "string") {
        throw new Error(`Expected tool calls to be a string, got: ${value}`);
      }
      const vercelToolCalls = z
        .array(vercelToolCallSchema)
        .parse(JSON.parse(value));
      return vercelToolCalls.map(translateVercelToolCall);
    }
  }
}

function objectToUint8Array(obj: Record<string, number>): Uint8Array {
  const array = new Uint8Array(Object.keys(obj).length);
  for (const key in obj) {
    const idx = parseInt(key);
    if (isNaN(idx)) {
      throw new Error(`Invalid key: ${key}`);
    }
    if (idx < 0 || idx >= array.length) {
      throw new Error(`Array index out of bounds: ${idx}`);
    }
    array[idx] = obj[key];
  }
  return array;
}

function convertImageUrlToBase64({
  mimeType,
  url,
}: {
  mimeType: string;
  url: string | Uint8Array | Record<string, number>;
}): string {
  let urlString;
  if (typeof url === "string") {
    try {
      atob(url); // Throws if not a valid base64 string
      urlString = url;
    } catch {
      return url;
    }
  } else {
    const image = url instanceof Uint8Array ? url : objectToUint8Array(url);
    urlString = Buffer.from(image).toString("base64");
  }
  return `data:${mimeType};base64,${urlString}`;
}

const vercelPromptSchema = z.union([
  z.object({
    messages: z.union([z.string(), z.array(vercelMessageSchema)]),
    system: z.string().nullish(),
  }),
  z.object({
    prompt: z.string(),
    system: z.string().nullish(),
  }),
]);
type VercelPrompt = z.infer<typeof vercelPromptSchema>;

function translateVercelPrompt(prompt: VercelPrompt): Message[] {
  const ret: Message[] = [];

  if (prompt.system) {
    ret.push({ role: "system", content: prompt.system });
  }

  if ("messages" in prompt) {
    const messages = prompt.messages;
    const obj = typeof messages === "string" ? JSON.parse(messages) : messages;
    const parsed = z.array(vercelMessageSchema).parse(obj);
    ret.push(...translateVercelMessages(parsed));
  } else {
    ret.push({
      role: "user",
      content: [{ type: "text", text: prompt.prompt }],
    });
  }

  return ret;
}

// Partially lifted from the Vercel AI SDK:
// https://github.com/vercel/ai/blob/d3aa5486529e3d1a38b30e3972b4f4c63ea4ae9a/packages/openai/src/convert-to-openai-chat-messages.ts
// The types are a bit different in OTEL-land so there are some tweaks.
function translateVercelMessages(messages: VercelMessage[]): Message[] {
  const ret: Message[] = [];
  for (const { role, content } of messages) {
    switch (role) {
      case "system": {
        ret.push({ role, content });
        break;
      }

      case "user": {
        if (typeof content === "string") {
          ret.push({ role, content });
          break;
        }

        if (content.length === 1 && content[0].type === "text") {
          ret.push({ role: "user", content: content[0].text });
          break;
        }

        ret.push({
          role: "user",
          content: content.map((part) => {
            switch (part.type) {
              case "text": {
                return { type: "text", text: part.text };
              }
              case "image": {
                try {
                  return {
                    type: "image_url",
                    image_url: {
                      url: convertImageUrlToBase64({
                        mimeType: part.mimeType ?? "image/jpeg",
                        url: part.image,
                      }),
                    },
                  };
                } catch (e) {
                  throw new Error(
                    `Error converting image to base64 with type ${typeof part.image}, ${part.image}: ${e}`,
                  );
                }
              }
            }
          }),
        });

        break;
      }

      case "assistant": {
        if (typeof content === "string") {
          ret.push({ role, content });
          break;
        }

        let text = "";
        const toolCalls: Array<{
          id: string;
          type: "function";
          function: { name: string; arguments: string };
        }> = [];

        for (const part of content) {
          switch (part.type) {
            case "text": {
              text += part.text;
              break;
            }
            case "tool-call": {
              toolCalls.push(translateVercelToolCall(part));
              break;
            }
            default: {
              const _exhaustiveCheck: never = part;
              throw new Error(`Unsupported part: ${_exhaustiveCheck}`);
            }
          }
        }

        ret.push({
          role: "assistant",
          content: text,
          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
        });

        break;
      }

      case "tool": {
        for (const toolResponse of content) {
          ret.push({
            role: "tool",
            tool_call_id: toolResponse.toolCallId,
            content: JSON.stringify(toolResponse.result),
          });
        }
        break;
      }

      default: {
        const _exhaustiveCheck: never = role;
        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
      }
    }
  }

  return ret;
}
