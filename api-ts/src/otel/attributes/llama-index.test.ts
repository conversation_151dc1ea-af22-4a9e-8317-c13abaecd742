import { describe, expect, test } from "vitest";
import { convertAttributesToSpan } from "./index";
import { otelSpanToRow } from "../collector";
import { llamaIndexSpanSpec } from "./llama-index";
import { notHandled } from "./attributes";

const testCases = {
  basic: {
    metadata: '{"test_name": "llama"}',
    "llm.model_name": "gpt-3.5-turbo",
    "llm.invocation_parameters":
      '{"context_window":16384,"num_output":-1,"is_chat_model":true,"is_function_calling_model":true,"model_name":"gpt-3.5-turbo","system_role":"system"}',
    "input.value":
      '{"messages": ["ChatMessage(role=<MessageRole.USER: \'user\'>, content=\'What does a llama look like?\', additional_kwargs={})"], "kwargs": {"model": "gpt-4o-mini", "temperature": 0.7}}',
    "input.mime_type": "application/json",
    "llm.input_messages.0.message.role": "user",
    "llm.input_messages.0.message.content": "What does a llama look like?",
    "output.value":
      "assistant: Llamas are large, domesticated South American animals that belong to the camelid family. They have elongated bodies, long necks, and a distinctive, wedge-shaped head.",
    "llm.token_count.prompt": 14,
    "llm.token_count.completion": 138,
    "llm.token_count.total": 152,
    "llm.output_messages.0.message.role": "assistant",
    "llm.output_messages.0.message.content":
      "Llamas are large, domesticated South American animals that belong to the camelid family. They have elongated bodies, long necks, and a distinctive, wedge-shaped head.",
    "openinference.span.kind": "LLM",
  },
  messages: {
    metadata: '{"test_name": "messages"}',
    "llm.model_name": "gpt-3.5-turbo",
    "llm.invocation_parameters":
      '{"context_window":16384,"num_output":-1,"is_chat_model":true,"is_function_calling_model":true,"model_name":"gpt-3.5-turbo","system_role":"system"}',
    "input.value":
      "{\"messages\": [\"ChatMessage(role=<MessageRole.SYSTEM: 'system'>, content='You are a helpful assistant.', additional_kwargs={})\", \"ChatMessage(role=<MessageRole.USER: 'user'>, content=[{'type': 'text', 'text': 'Why are they orange?'}], additional_kwargs={})\", \"ChatMessage(role=<MessageRole.ASSISTANT: 'assistant'>, content='What do you mean?', additional_kwargs={})\", \"ChatMessage(role=<MessageRole.USER: 'user'>, content=[{'type': 'text', 'text': 'Why are llamas orange?'}], additional_kwargs={})\"], \"kwargs\": {\"model\": \"gpt-4o-mini\", \"temperature\": 0.7}}",
    "input.mime_type": "application/json",
    "llm.input_messages.0.message.role": "system",
    "llm.input_messages.0.message.content": "You are a helpful assistant.",
    "llm.input_messages.1.message.role": "user",
    "llm.input_messages.1.message.contents.0.message_content.type": "text",
    "llm.input_messages.1.message.contents.0.message_content.text":
      "Why are they orange?",
    "llm.input_messages.2.message.role": "assistant",
    "llm.input_messages.2.message.content": "What do you mean?",
    "llm.input_messages.3.message.role": "user",
    "llm.input_messages.3.message.contents.0.message_content.type": "text",
    "llm.input_messages.3.message.contents.0.message_content.text":
      "Why are llamas orange?",
    "output.value":
      "assistant: Llamas can have a variety of coat colors, including shades of brown, white, black, gray, and sometimes orange or reddish hues. The specific color of a llama's coat is determined by its genetics.",
    "llm.token_count.prompt": 41,
    "llm.token_count.completion": 119,
    "llm.token_count.total": 160,
    "llm.output_messages.0.message.role": "assistant",
    "llm.output_messages.0.message.content":
      "Llamas can have a variety of coat colors, including shades of brown, white, black, gray, and sometimes orange or reddish hues. The specific color of a llama's coat is determined by its genetics.",
    "openinference.span.kind": "LLM",
  },
  multimodal: {
    metadata: '{"test_name": "multimodal"}',
    "llm.model_name": "gpt-3.5-turbo",
    "llm.invocation_parameters":
      '{"context_window":16384,"num_output":-1,"is_chat_model":true,"is_function_calling_model":true,"model_name":"gpt-3.5-turbo","system_role":"system"}',
    "input.value":
      "{\"messages\": [\"ChatMessage(role=<MessageRole.SYSTEM: 'system'>, content='Speak like a pirate. Arrrr.', additional_kwargs={})\", \"ChatMessage(role=<MessageRole.USER: 'user'>, content=[{'type': 'text', 'text': 'Tell a brief story about the provided image(s).'}, {'type': 'image_url', 'image_url': {'url': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII='}}, {'type': 'image_url', 'image_url': {'url': 'https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png'}}], additional_kwargs={})\"], \"kwargs\": {\"model\": \"gpt-4o-mini\", \"temperature\": 0.7}}",
    "input.mime_type": "application/json",
    "llm.input_messages.0.message.role": "system",
    "llm.input_messages.0.message.content": "Speak like a pirate. Arrrr.",
    "llm.input_messages.1.message.role": "user",
    "llm.input_messages.1.message.contents.0.message_content.type": "text",
    "llm.input_messages.1.message.contents.0.message_content.text":
      "Tell a brief story about the provided image(s).",
    "llm.input_messages.1.message.contents.1.message_content.type": "image",
    "llm.input_messages.1.message.contents.1.message_content.image.image.url":
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=",
    "llm.input_messages.1.message.contents.2.message_content.type": "image",
    "llm.input_messages.1.message.contents.2.message_content.image.image.url":
      "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
    "output.value":
      "assistant: Arrr, gather 'round, mateys, for a tale of a jolly banana named Benny!",
    "llm.token_count.prompt": 17029,
    "llm.token_count.completion": 225,
    "llm.token_count.total": 17254,
    "llm.output_messages.0.message.role": "assistant",
    "llm.output_messages.0.message.content":
      "Arrr, gather 'round, mateys, for a tale of a jolly banana named Benny!",
    "openinference.span.kind": "LLM",
  },
};

function runTestCase(input: Record<string, unknown>) {
  const result = convertAttributesToSpan(input);
  expect(result).toMatchSnapshot();
}

describe("llama-index convertAttributesToSpan", () => {
  for (const [name, attributes] of Object.entries(testCases)) {
    test(name, () => runTestCase(attributes));
  }
});

// Helper function to convert test case attributes to OTEL span format
function createOtelSpanFromTestCase(testCaseName: string) {
  const attributes = testCases[testCaseName as keyof typeof testCases];
  return {
    traceId: new Uint8Array([
      0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22, 0x33, 0x44,
      0x55, 0x66, 0x77, 0x88,
    ]),
    spanId: new Uint8Array([0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11]),
    name: "llama_index_call",
    startTimeUnixNano: "1640995200000000000",
    endTimeUnixNano: "1640995201500000000",
    attributes: Object.entries(attributes).map(([key, value]) => ({
      key,
      value:
        typeof value === "number"
          ? { intValue: value }
          : { stringValue: String(value) },
    })),
    status: { code: 1 },
  };
}

describe("llama-index SpecResult format", () => {
  test("should return SpecResult with handled: true for valid input", () => {
    const attributes = {
      "llm.input_messages": [
        {
          message: {
            role: "user",
            content: "What does a llama look like?",
          },
        },
      ],
    };
    const result = llamaIndexSpanSpec.input!(attributes);
    expect(result.handled).toBe(true);
    if (result.handled) {
      expect(result.value.isLLM).toBe(true);
      expect(result.value.data).toHaveLength(1);
      expect(result.deleteKeys).toBeInstanceOf(Set);
    }
  });

  test("should return notHandled for missing input", () => {
    const attributes = {};
    const result = llamaIndexSpanSpec.input!(attributes);
    expect(result).toBe(notHandled);
  });

  test("should return SpecResult with handled: true for valid metrics", () => {
    const attributes = {
      "llm.token_count": {
        total: 152,
        prompt: 14,
        content: 138,
      },
    };
    const result = llamaIndexSpanSpec.metrics!(attributes);
    expect(result.handled).toBe(true);
    if (result.handled) {
      expect(result.value.tokens).toBe(152);
      expect(result.value.prompt_tokens).toBe(14);
      expect(result.deleteKeys).toBeInstanceOf(Set);
    }
  });

  test("should return notHandled for missing metrics", () => {
    const attributes = {};
    const result = llamaIndexSpanSpec.metrics!(attributes);
    expect(result).toBe(notHandled);
  });
});

describe("llama-index otelSpanToRow integration", () => {
  test("basic case - preserves all attributes including llm.invocation_parameters", () => {
    const otelSpan = createOtelSpanFromTestCase("basic");
    const { row, fieldStats } = otelSpanToRow(otelSpan);

    expect(row).toMatchObject({
      input: [
        {
          content: "What does a llama look like?",
          role: "user",
        },
      ],
      output: [
        {
          content:
            "Llamas are large, domesticated South American animals that belong to the camelid family. They have elongated bodies, long necks, and a distinctive, wedge-shaped head.",
          role: "assistant",
        },
      ],
      metadata: {
        // ALL original attributes preserved from testCases.basic
        metadata: '{"test_name": "llama"}',
        "llm.model_name": "gpt-3.5-turbo",
        "llm.invocation_parameters":
          '{"context_window":16384,"num_output":-1,"is_chat_model":true,"is_function_calling_model":true,"model_name":"gpt-3.5-turbo","system_role":"system"}',
        "input.value":
          '{"messages": ["ChatMessage(role=<MessageRole.USER: \'user\'>, content=\'What does a llama look like?\', additional_kwargs={})"], "kwargs": {"model": "gpt-4o-mini", "temperature": 0.7}}',
        "input.mime_type": "application/json",
        "llm.input_messages.0.message.role": "user",
        "llm.input_messages.0.message.content": "What does a llama look like?",
        "output.value":
          "assistant: Llamas are large, domesticated South American animals that belong to the camelid family. They have elongated bodies, long necks, and a distinctive, wedge-shaped head.",
        "llm.token_count.prompt": 14,
        "llm.token_count.completion": 138,
        "llm.token_count.total": 152,
        "llm.output_messages.0.message.role": "assistant",
        "llm.output_messages.0.message.content":
          "Llamas are large, domesticated South American animals that belong to the camelid family. They have elongated bodies, long necks, and a distinctive, wedge-shaped head.",
        "openinference.span.kind": "LLM",
        // PLUS processed metadata from LlamaIndex processor
        test_name: "llama",
        model: "gpt-4o-mini",
        temperature: 0.7,
      },
      metrics: {
        prompt_tokens: 14,
        tokens: 152,
      },
      span_attributes: {
        name: "llama_index_call",
        type: "llm",
      },
      error: null,
    });

    expect(fieldStats.toObject()).toEqual({
      llamaIndex: { ok: 4, errs: 0 },
    });
  });

  test("multimodal case - preserves complex invocation parameters", () => {
    const otelSpan = createOtelSpanFromTestCase("multimodal");
    const { row, fieldStats } = otelSpanToRow(otelSpan);

    expect(row).toMatchObject({
      metadata: {
        // Verify that llm.invocation_parameters is preserved
        "llm.invocation_parameters":
          '{"context_window":16384,"num_output":-1,"is_chat_model":true,"is_function_calling_model":true,"model_name":"gpt-3.5-turbo","system_role":"system"}',
        "llm.model_name": "gpt-3.5-turbo",
        "openinference.span.kind": "LLM",
        test_name: "multimodal",
        model: "gpt-4o-mini",
        temperature: 0.7,
      },
      metrics: {
        prompt_tokens: 17029,
        tokens: 17254,
      },
      span_attributes: {
        type: "llm",
      },
      error: null,
    });

    expect(fieldStats.toObject()).toEqual({
      llamaIndex: { ok: 4, errs: 0 },
    });

    // Verify that the invocation parameters contain the expected model capabilities
    const invocationParams = JSON.parse(
      row!.metadata!["llm.invocation_parameters"] as string,
    );
    expect(invocationParams).toEqual({
      context_window: 16384,
      num_output: -1,
      is_chat_model: true,
      is_function_calling_model: true,
      model_name: "gpt-3.5-turbo",
      system_role: "system",
    });
  });
});
