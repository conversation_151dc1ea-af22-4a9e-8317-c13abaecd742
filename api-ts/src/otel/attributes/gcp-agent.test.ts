import { otelSpanToRow } from "../collector";
import { describe, expect, test } from "vitest";

/**
 * Test data based on GCP Vertex AI Agent OTEL telemetry output
 * Tests conversion of GCP Agent spans with various attribute patterns to Braintrust rows
 *
 * GCP Vertex AI sends agent data as attributes:
 * - gcp.vertex.agent.llm_request: LLM input messages
 * - gcp.vertex.agent.llm_response: LLM output messages
 * - gcp.vertex.agent.tool_call_args: Tool call input arguments
 * - gcp.vertex.agent.tool_response: Tool call output response
 */
describe("gcp-agent", () => {
  test("handles GCP Vertex AI LLM request and response", () => {
    const gcpLLMSpan = {
      traceId: "a1b2c3d4e5f6789012345678901234ef",
      spanId: "1234567890abcdef",
      parentSpanId: null,
      name: "vertex-ai-chat",
      startTimeUnixNano: "1722479523945855000",
      endTimeUnixNano: "1722479523947252000",
      attributes: [
        {
          key: "gcp.vertex.agent.llm_request",
          value: {
            stringValue: JSON.stringify([
              {
                role: "user",
                content: "What is the weather in San Francisco?",
              },
              {
                role: "system",
                content: "You are a helpful weather assistant.",
              },
            ]),
          },
        },
        {
          key: "gcp.vertex.agent.llm_response",
          value: {
            stringValue: JSON.stringify([
              {
                role: "assistant",
                content:
                  "I don't have real-time weather data access. To get current weather information for San Francisco, I recommend checking weather.com or using a weather app.",
              },
            ]),
          },
        },
        {
          key: "gcp.project_id",
          value: { stringValue: "my-gcp-project" },
        },
        {
          key: "gcp.location",
          value: { stringValue: "us-central1" },
        },
        {
          key: "gcp.model",
          value: { stringValue: "gemini-1.5-pro" },
        },
      ],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(gcpLLMSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "user",
          content: "What is the weather in San Francisco?",
        },
        {
          role: "system",
          content: "You are a helpful weather assistant.",
        },
      ],
      output: [
        {
          role: "assistant",
          content: expect.stringContaining("real-time weather data"),
        },
      ],
      metadata: expect.objectContaining({
        "gcp.project_id": "my-gcp-project",
        "gcp.location": "us-central1",
        "gcp.model": "gemini-1.5-pro",
      }),
      span_attributes: expect.objectContaining({
        name: "vertex-ai-chat",
      }),
    });

    // Validate field processing stats
    expect(fieldStats.toObject()).toMatchObject({
      gcpAgent: { ok: 2, errs: 0 },
    });
  });

  test("handles GCP Vertex AI tool call and response", () => {
    const gcpToolSpan = {
      traceId: "b2c3d4e5f6789012345678901234567f",
      spanId: "234567890abcdef1",
      parentSpanId: "1234567890abcdef",
      name: "search_tool_execution",
      startTimeUnixNano: "1722479524945855000",
      endTimeUnixNano: "1722479524947252000",
      attributes: [
        {
          key: "gcp.vertex.agent.tool_call_args",
          value: {
            stringValue: JSON.stringify({
              tool_name: "web_search",
              query: "San Francisco weather today",
              max_results: 5,
              include_snippets: true,
            }),
          },
        },
        {
          key: "gcp.vertex.agent.tool_response",
          value: {
            stringValue: JSON.stringify({
              status: "success",
              results: [
                {
                  title: "San Francisco Weather - Weather.com",
                  snippet: "Current conditions: 68°F, partly cloudy",
                  url: "https://weather.com/sf",
                },
                {
                  title: "SF Bay Area Weather",
                  snippet: "Today's high: 72°F, low: 58°F",
                  url: "https://weather.gov/sf",
                },
              ],
              execution_time_ms: 245,
            }),
          },
        },
        {
          key: "gcp.tool.name",
          value: { stringValue: "web_search" },
        },
        {
          key: "gcp.tool.status",
          value: { stringValue: "success" },
        },
      ],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(gcpToolSpan);

    expect(row).toMatchObject({
      input: {
        tool_name: "web_search",
        query: "San Francisco weather today",
        max_results: 5,
        include_snippets: true,
      },
      output: {
        status: "success",
        results: expect.arrayContaining([
          expect.objectContaining({
            title: "San Francisco Weather - Weather.com",
            snippet: "Current conditions: 68°F, partly cloudy",
          }),
        ]),
        execution_time_ms: 245,
      },
      metadata: expect.objectContaining({
        "gcp.tool.name": "web_search",
        "gcp.tool.status": "success",
      }),
      span_attributes: expect.objectContaining({
        name: "search_tool_execution",
      }),
    });

    // Validate field processing stats
    expect(fieldStats.toObject()).toMatchObject({
      gcpAgent: { ok: 2, errs: 0 },
    });
  });

  test("handles complex nested GCP Vertex AI messages", () => {
    const complexGCPSpan = {
      traceId: "c3d4e5f678901234567890123456789a",
      spanId: "34567890abcdef12",
      parentSpanId: null,
      name: "vertex-ai-function-calling",
      startTimeUnixNano: "1722479525945855000",
      endTimeUnixNano: "1722479525947252000",
      attributes: [
        {
          key: "gcp.vertex.agent.llm_request",
          value: {
            stringValue: JSON.stringify([
              {
                role: "user",
                content: "Book a flight from NYC to SF for tomorrow",
              },
              {
                role: "assistant",
                content: "I'll help you search for flights from NYC to SF.",
                function_call: {
                  name: "search_flights",
                  arguments: JSON.stringify({
                    origin: "NYC",
                    destination: "SF",
                    date: "2024-08-02",
                  }),
                },
              },
              {
                role: "function",
                name: "search_flights",
                content: JSON.stringify({
                  flights: [
                    {
                      airline: "United",
                      flight_number: "UA123",
                      departure: "08:00",
                      arrival: "11:30",
                      price: 450,
                    },
                    {
                      airline: "Delta",
                      flight_number: "DL456",
                      departure: "14:00",
                      arrival: "17:30",
                      price: 380,
                    },
                  ],
                }),
              },
            ]),
          },
        },
        {
          key: "gcp.vertex.agent.llm_response",
          value: {
            stringValue: JSON.stringify([
              {
                role: "assistant",
                content:
                  "I found 2 flights from NYC to SF tomorrow:\n\n1. United UA123 - Departs 8:00 AM, arrives 11:30 AM - $450\n2. Delta DL456 - Departs 2:00 PM, arrives 5:30 PM - $380\n\nWhich flight would you prefer?",
              },
            ]),
          },
        },
        {
          key: "gcp.model",
          value: { stringValue: "gemini-1.5-flash" },
        },
        {
          key: "gcp.vertex.request.max_tokens",
          value: { intValue: 2048 },
        },
        {
          key: "gcp.vertex.request.temperature",
          value: { doubleValue: 0.7 },
        },
      ],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(complexGCPSpan);

    expect(row).toMatchObject({
      input: expect.arrayContaining([
        {
          role: "user",
          content: "Book a flight from NYC to SF for tomorrow",
        },
        expect.objectContaining({
          role: "assistant",
          content: expect.stringContaining("search for flights"),
        }),
        expect.objectContaining({
          role: "function",
          name: "search_flights",
        }),
      ]),
      output: [
        {
          role: "assistant",
          content: expect.stringContaining("United UA123"),
        },
      ],
      metadata: expect.objectContaining({
        "gcp.model": "gemini-1.5-flash",
        "gcp.vertex.request.max_tokens": 2048,
        "gcp.vertex.request.temperature": 0.7,
      }),
      span_attributes: expect.objectContaining({
        name: "vertex-ai-function-calling",
      }),
    });

    // Validate field processing stats
    expect(fieldStats.toObject()).toMatchObject({
      gcpAgent: { ok: 2, errs: 0 },
    });
  });

  test("handles invalid JSON in GCP attributes gracefully", () => {
    const invalidJSONSpan = {
      traceId: "d4e5f6789012345678901234567890ab",
      spanId: "4567890abcdef123",
      parentSpanId: null,
      name: "vertex-ai-error-case",
      startTimeUnixNano: "1722479526945855000",
      endTimeUnixNano: "1722479526947252000",
      attributes: [
        {
          key: "gcp.vertex.agent.llm_request",
          value: {
            stringValue: "invalid json {not valid]",
          },
        },
        {
          key: "gcp.vertex.agent.llm_response",
          value: {
            stringValue: '{"valid": "json", "message": "This is valid"}',
          },
        },
        {
          key: "gcp.error",
          value: { stringValue: "JSON parse error in request" },
        },
      ],
      status: { code: 2 }, // Error status
    };

    const { row, fieldStats } = otelSpanToRow(invalidJSONSpan);

    // Should only process the valid response, not the invalid request
    expect(row).toMatchObject({
      output: {
        valid: "json",
        message: "This is valid",
      },
      metadata: expect.objectContaining({
        "gcp.error": "JSON parse error in request",
      }),
      span_attributes: expect.objectContaining({
        name: "vertex-ai-error-case",
      }),
    });

    // Input should not be set due to invalid JSON
    expect(row?.input).toBeUndefined();

    // Validate field processing stats - only successful output processing
    expect(fieldStats.toObject()).toMatchObject({
      gcpAgent: { ok: 1, errs: 0 }, // Only output was processed successfully
    });
  });
});
