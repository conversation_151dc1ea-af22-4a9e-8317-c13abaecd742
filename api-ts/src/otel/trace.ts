// Zod-ification of the `IExportTraceServiceRequest` interface from
// `@opentelemetry/otlp-transformer`:
// https://github.com/open-telemetry/opentelemetry-js/blob/6be903a90225431def0f554d1fbac315e329de9f/experimental/packages/otlp-transformer/src/trace/types.ts
//
// These are partial schemas; some fields that aren't used to construct the
// Braintrust trace are omitted.
import { z } from "zod";
import Long from "long";
import { isEmpty } from "braintrust/util";

type SafeNumber = string | number | null | undefined;

function scalarToSafeNumber(value: unknown): SafeNumber {
  if (isEmpty(value)) {
    return value;
  }
  switch (typeof value) {
    case "number":
      // Convert NaN to "NaN" so that it gets JSON serialized as such.
      return isNaN(value) ? value.toString() : value;
    case "string":
      return value;
    default:
      throw new Error(
        `Expected value of type number or string, got ${typeof value}`,
      );
  }
}

function protoInt64ToSafeNumber(value: unknown): SafeNumber {
  if (Long.isLong(value)) {
    // Convert the Long to a number if it's in range, otherwise stringify it.
    if (
      value.gte(Number.MIN_SAFE_INTEGER) &&
      value.lte(Number.MAX_SAFE_INTEGER)
    ) {
      return value.toNumber();
    } else {
      return value.toString();
    }
  }
  return scalarToSafeNumber(value);
}

function protoDoubleToSafeNumber(value: unknown): SafeNumber {
  if (Long.isLong(value)) {
    return value.toNumber();
  }
  return scalarToSafeNumber(value);
}

function hexToUint8Array(hex: string): Uint8Array {
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < bytes.length; i++) {
    bytes[i] = parseInt(hex.slice(i * 2, i * 2 + 2), 16);
  }
  return bytes;
}

export const stringValueSchema = z.coerce.string().nullish();
export const boolValueSchema = z.coerce.boolean().nullish();
export const intValueSchema = z.preprocess(
  protoInt64ToSafeNumber,
  z.union([z.coerce.number().int(), z.string()]).nullish(),
);
export const doubleValueSchema = z.preprocess(
  protoDoubleToSafeNumber,
  z.union([z.coerce.number(), z.string()]).nullish(),
);

const byteSchema = z.number().int().min(0).max(255);
const hexStringSchema = z.string().refine((input) => {
  return /^[0-9a-fA-F]+$/.test(input) && input.length % 2 === 0;
});
const base64StringSchema = z.string().regex(/^[A-Za-z0-9+/]+={0,2}$/);

export const byteArraySchema = z.union([
  z.instanceof(Uint8Array),
  z.array(byteSchema).transform((arr) => new Uint8Array(arr)),
  hexStringSchema.transform(hexToUint8Array),
  base64StringSchema.transform((input) => {
    const binaryString = atob(input);
    const binaryArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      binaryArray[i] = binaryString.charCodeAt(i);
    }
    return binaryArray;
  }),
]);
export const bytesValueSchema = byteArraySchema.nullish();

function hasNonZeroByte(arr: Uint8Array): boolean {
  return arr.find((byte) => byte !== 0) !== undefined;
}

const idSchema = byteArraySchema.refine(
  hasNonZeroByte,
  "id field must have at least one non-zero byte",
);

const traceIdSchema = idSchema.refine(
  (arr) => arr.length === 16,
  "traceId must be 16 bytes long",
);

const spanIdSchema = idSchema.refine(
  (arr) => arr.length === 8,
  "spanId must be 8 bytes long",
);

const parentSpanIdSchema = byteArraySchema
  .transform((arr) => (!hasNonZeroByte(arr) ? null : arr))
  .refine((arr) => !arr || arr.length === 8, "spanId must be 8 bytes long")
  .nullish();
export type ParentSpanId = z.infer<typeof parentSpanIdSchema>;

// This is a Zod implementation of the OTEL `AnyValue` proto message:
// https://github.com/open-telemetry/opentelemetry-proto/blob/cc4ed55c082cb75e084d40b4ddf3805eda099f97/opentelemetry/proto/common/v1/common.proto#L27
//
const anyValueSchema = z
  .strictObject({
    stringValue: stringValueSchema,
    boolValue: boolValueSchema,
    intValue: intValueSchema,
    doubleValue: doubleValueSchema,
    bytesValue: bytesValueSchema,
    // Leave the composite fields unparsed because `z.preprocess`
    // doesn't play nicely with recursive schemas. We parse them
    // recursively in `parseAttributeValue` later.
    arrayValue: z.unknown(),
    kvlistValue: z.unknown(),
  })
  .refine(
    (obj) => Object.keys(obj).length <= 1,
    "AnyValue can only have one field set",
  );
export type AnyValue = z.infer<typeof anyValueSchema>;

export const arrayValueSchema = z.object({
  values: z.array(z.lazy(() => anyValueSchema)).nullish(),
});

export const kvlistValueSchema = z.object({
  values: z.array(z.lazy(() => keyValueSchema)).nullish(),
});

export const keyValueSchema = z.object({
  key: z.string(),
  value: anyValueSchema,
});
export type KeyValue = z.infer<typeof keyValueSchema>;

export const attributeSchema = keyValueSchema;
export type Attribute = z.infer<typeof attributeSchema>;

export const attributesSchema = z.array(attributeSchema);
export type Attributes = z.infer<typeof attributesSchema>;

const eventSchema = z.object({
  timeUnixNano: intValueSchema,
  name: z.string().nullish(),
  attributes: z.array(attributeSchema).nullish(),
});

const statusSchema = z.object({
  code: z.number().int().nullish(),
  message: z.string().nullish(),
});

export const otelSpanSchema = z.object({
  traceId: traceIdSchema,
  spanId: spanIdSchema,
  traceState: z.string().nullish(),
  parentSpanId: parentSpanIdSchema,
  name: z.string().nullish(),
  kind: z.unknown(),
  startTimeUnixNano: intValueSchema,
  endTimeUnixNano: intValueSchema,
  attributes: z.array(keyValueSchema).nullish(),
  status: statusSchema.nullish(),
  events: z.array(eventSchema).nullish(),
});
export type OtelSpan = z.infer<typeof otelSpanSchema>;

// Be as lenient as possible with the schema so that invalid spans
// don't tank the entire trace. We return the number of invalid spans
// in the API response.
const scopeSpansSchema = z.object({
  spans: z.array(
    z
      .object({
        traceId: z.unknown(),
        spanId: z.unknown(),
      })
      .passthrough(),
  ),
});

const resourceSpansSchema = z.object({
  scopeSpans: z.array(scopeSpansSchema),
});

export const exportTraceServiceRequestSchema = z.object({
  resourceSpans: z.array(resourceSpansSchema),
});

export type ExportTraceServiceRequest = z.infer<
  typeof exportTraceServiceRequestSchema
>;
