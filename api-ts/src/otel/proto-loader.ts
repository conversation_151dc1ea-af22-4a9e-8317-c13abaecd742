import * as fs from "node:fs/promises";
import path from "path";
import os from "os";
import type { ExportTraceServiceRequest } from "./trace";
import Long from "long";
import protobuf from "protobufjs";
import protoSpec from "./proto-spec.json";
import { LazyValue } from "braintrust";

// Simple interface for OTEL trace service response
interface IExportTraceServiceResponse {
  partialSuccess?: {
    rejectedSpans?: number;
    errorMessage?: string;
  };
}

// `proto-spec.json` contains the unmodified contents of .proto files from the
// OTEL specification keyed by relative path.
//
// https://github.com/open-telemetry/opentelemetry-proto/blob/a76fe9dea26871e8a6c494024bc9927fe73b8142
//
// The following files are included:
// - opentelemetry/proto/trace/v1/trace.proto
// - opentelemetry/proto/collector/trace/v1/trace_service.proto
// - opentelemetry/proto/collector/trace/v1/trace_service.proto
// - opentelemetry/proto/common/v1/common.proto
// - opentelemetry/proto/resource/v1/resource.proto
//
// We write the files to a temporary directory and load them using protobuf.js
// the first time they are needed.

interface ProtoSpec {
  [key: string]: string;
}

async function writeProtoFiles({
  baseDir,
  protoSpec,
}: {
  baseDir: string;
  protoSpec: ProtoSpec;
}): Promise<void> {
  for (const [relPath, content] of Object.entries(protoSpec)) {
    const fullPath = path.join(baseDir, relPath);
    await fs.mkdir(path.dirname(fullPath), { recursive: true });
    await fs.writeFile(fullPath, content);
  }
}

async function loadRoot(): Promise<protobuf.Root> {
  const tmpDir = await fs.mkdtemp(path.join(os.tmpdir(), "otel_protos"));
  await writeProtoFiles({
    baseDir: tmpDir,
    protoSpec,
  });

  // NOTE(austin): protobuf.js isn't guaranteed to use Long for int64 values
  // unless you explicitly set it.
  // https://github.com/protobufjs/protobuf.js#browserify-integration
  protobuf.util.Long = Long;
  protobuf.configure();

  const root = new protobuf.Root();
  root.resolvePath = (_, relPath) => {
    return path.join(tmpDir, relPath);
  };
  return root;
}

export class OtelProtoLoader {
  private root: LazyValue<protobuf.Root>;
  private exportTraceServiceRequestType: LazyValue<protobuf.Type>;
  private exportTraceServiceResponseType: LazyValue<protobuf.Type>;

  constructor() {
    this.root = new LazyValue(loadRoot);
    this.exportTraceServiceRequestType = new LazyValue(async () => {
      const root = await this.root.get();
      await root.load("opentelemetry/proto/trace/v1/trace.proto");
      return root.lookupType("opentelemetry.proto.trace.v1.TracesData");
    });
    this.exportTraceServiceResponseType = new LazyValue(async () => {
      const root = await this.root.get();
      await root.load(
        "opentelemetry/proto/collector/trace/v1/trace_service.proto",
      );
      return root.lookupType(
        "opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse",
      );
    });
  }

  async encodeExportTraceServiceRequest(
    request: ExportTraceServiceRequest,
  ): Promise<Uint8Array> {
    const exportTraceServiceRequestType =
      await this.exportTraceServiceRequestType.get();
    return exportTraceServiceRequestType.encode(request).finish();
  }

  async decodeExportTraceServiceRequest(buffer: Buffer): Promise<unknown> {
    const exportTraceServiceRequestType =
      await this.exportTraceServiceRequestType.get();
    const message = exportTraceServiceRequestType.decode(buffer);
    return exportTraceServiceRequestType.toObject(message);
  }

  async encodeExportTraceServiceResponse(
    response: IExportTraceServiceResponse,
  ): Promise<Uint8Array> {
    const exportTraceServiceResponseType =
      await this.exportTraceServiceResponseType.get();
    return exportTraceServiceResponseType.encode(response).finish();
  }

  async decodeExportTraceServiceResponse(buffer: Buffer): Promise<unknown> {
    const exportTraceServiceResponseType =
      await this.exportTraceServiceResponseType.get();
    const message = exportTraceServiceResponseType.decode(buffer);
    return exportTraceServiceResponseType.toObject(message);
  }
}
