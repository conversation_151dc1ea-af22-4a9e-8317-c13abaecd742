import * as crypto from "crypto";
import { z } from "zod";
import {
  AccessDeniedError,
  base64ToUrlSafe,
  objectTypeToAclObjectType,
  parseUrlBool,
  wrapZodError,
} from "./util";
import { objectTypeSchema } from "./schema";
import { OBJECT_CACHE } from "./object_cache";
import { getRedis } from "./redis";

const BROADCAST_KEY_EXPIRY_SECONDS = 3600;

const broadcastSecretSchema = z.object({
  channel: z.string(),
  token: z.string(),
});

export type BroadcastSecret = z.infer<typeof broadcastSecretSchema>;

const secretBroadcastKeyRequestSchema = z.object({
  object_type: objectTypeSchema,
  id: z.string(),
  audit_log: z
    .string()
    .nullish()
    .transform((x) => parseUrlBool(x || "false")),
});

export type SecretBroadcastKeyRequest = z.infer<
  typeof secretBroadcastKeyRequestSchema
>;

export async function secretBroadcastKeyRequest(
  appOrigin: string,
  token: string | undefined,
  ctxData: unknown,
): Promise<BroadcastSecret> {
  const params = wrapZodError(() =>
    secretBroadcastKeyRequestSchema.parse(ctxData),
  );
  return secretBroadcastKey({ ...params, appOrigin, token });
}

type SecretBroadcastKeyInput = SecretBroadcastKeyRequest & {
  appOrigin: string;
  token: string | undefined;
  skipAclCheck?: boolean;
};

export async function secretBroadcastKey({
  object_type,
  id,
  audit_log,
  appOrigin,
  token,
  skipAclCheck,
}: SecretBroadcastKeyInput): Promise<BroadcastSecret> {
  if (!skipAclCheck) {
    const { aclObjectType, overrideRestrictObjectType } =
      objectTypeToAclObjectType(object_type);
    const entry = await OBJECT_CACHE.checkAndGet({
      appOrigin,
      authToken: token,
      aclObjectType,
      overrideRestrictObjectType,
      objectId: id,
    });
    if (!entry.permissions.includes("read")) {
      throw new AccessDeniedError({
        permission: "read",
        aclObjectType,
        overrideRestrictObjectType,
        objectId: id,
      });
    }
  }

  const key = `broadcast-secrets:${object_type}:${id}:${
    audit_log ? "audit_log" : ""
  }`;
  const redisClient = await getRedis();
  const [secretStr, _] = await Promise.all([
    redisClient.get(key),
    redisClient.expire(key, BROADCAST_KEY_EXPIRY_SECONDS),
  ]);

  if (secretStr !== null) {
    return broadcastSecretSchema.parse(JSON.parse(secretStr));
  }

  const secret: BroadcastSecret = {
    channel: base64ToUrlSafe(crypto.randomBytes(32).toString("base64")),
    token: crypto.randomBytes(32).toString("base64"),
  };
  await Promise.all([
    redisClient.set(key, JSON.stringify(secret)),
    redisClient.expire(key, BROADCAST_KEY_EXPIRY_SECONDS),
  ]);

  return secret;
}
