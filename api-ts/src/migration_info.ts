import { ToSQL, ident, join, sql } from "@braintrust/btql/planner";
import { getPG, isPgError } from "./db/pg";
import { MIGRATION_VERSION_TABLE } from "./env";
import { z } from "zod";
import { PostgresError } from "pg-error-enum";
import { wrapZodError } from "./util";
import { PoolingBtPg } from "@braintrust/local/bt-pg";

export type MigrationVersion = number | number[] | null;

export type MigrationVersionInfo = {
  pg: MigrationVersion;
};

async function getMigrationVersion(
  conn: PoolingBtPg,
): Promise<MigrationVersion> {
  try {
    const { query, params } = sql`select last_migration_id from ${ident(
      MIGRATION_VERSION_TABLE,
    )}`.toNumericParamQuery();
    const { rows } = await conn.query(query, params);
    const ids = z.coerce
      .number()
      .array()
      .parse(rows.map((r) => r["last_migration_id"]));
    return ids.length === 1 ? ids[0] : ids;
  } catch (e) {
    if (
      isPgError(e) &&
      // For standard postgres.
      e.code === PostgresError.UNDEFINED_TABLE
    ) {
      return null;
    } else {
      throw e;
    }
  }
}

export async function migrationVersion(): Promise<MigrationVersionInfo> {
  return {
    pg: await getMigrationVersion(getPG()),
  };
}

export interface MigrationVersionIssue {
  issue: string;
  fixed: boolean;
}

export async function repairMigrationTable(): Promise<{
  issues: MigrationVersionIssue[];
}> {
  const conn = getPG();
  const issues: MigrationVersionIssue[] = [];
  let rows: { last_migration_id: number }[];
  try {
    const { rows: rows_ } = await conn.query(
      `select last_migration_id from ${MIGRATION_VERSION_TABLE}`,
    );
    rows = z.object({ last_migration_id: z.number() }).array().parse(rows_);
  } catch (e) {
    if (isPgError(e) && e.code === PostgresError.UNDEFINED_TABLE) {
      return {
        issues: [
          {
            issue: "Migration version table does not exist",
            fixed: false,
          },
        ],
      };
    } else {
      return {
        issues: [
          {
            issue: `Error checking migration version table: ${e instanceof Error ? e.message : String(e)}`,
            fixed: false,
          },
        ],
      };
    }
  }
  if (rows.length === 0) {
    issues.push({
      issue: "No migration version found",
      fixed: false,
    });
  } else if (rows.length > 1) {
    const distinctVersions = new Set(rows.map((r) => r["last_migration_id"]));
    if (distinctVersions.size > 1) {
      issues.push({
        issue: `Multiple migration versions found: ${Array.from(distinctVersions).join(", ")}`,
        fixed: false,
      });
    } else {
      await conn.query(`delete from ${MIGRATION_VERSION_TABLE}`);
      await conn.query(
        `insert into ${MIGRATION_VERSION_TABLE} (last_migration_id) values (${rows[0]["last_migration_id"]})`,
      );
      issues.push({
        issue: "Duplicate rows in migration version table",
        fixed: true,
      });
    }
  }

  return {
    issues,
  };
}

const migrationStatusData = z
  .object({
    job_name_pattern: z.string().nullish(),
  })
  .nullish();

const migrationStatusEntrySchema = z.strictObject({
  job_name: z.string().nullish(),
  job_id: z.coerce.number().nullish(),
  cron_status: z.string().nullish(),
  start_time: z.string().datetime().nullish(),
  end_time: z.string().datetime().nullish(),
  command: z.string().nullish(),
  message: z.string().nullish(),
});

export type MigrationStatusEntry = z.infer<typeof migrationStatusEntrySchema>;

export async function migrationStatus(
  ctxData: unknown,
): Promise<MigrationStatusEntry[]> {
  const ctxParams = wrapZodError(() => migrationStatusData.parse(ctxData));
  const filterExpr = ((): ToSQL => {
    const filters: ToSQL[] = [sql`true`];
    if (ctxParams?.job_name_pattern) {
      filters.push(sql`job_name like ${ctxParams.job_name_pattern}`);
    }
    return join(filters, " and ");
  })();
  const { query, params: queryParams } = sql`
    select
        run_migration_results.job_name,
        run_migration_results.job_id,
        cron_job_details.status cron_status,
        cron_job_details.start_time,
        cron_job_details.end_time,
        cron_job_details.command,
        run_migration_results.message
    from
        run_migration_results
        left join cron.job_run_details cron_job_details
            on run_migration_results.job_id = cron_job_details.jobid
    where ${filterExpr}
    order by cron_job_details.start_time desc nulls last
    limit 1000
    `.toNumericParamQuery();

  const conn = getPG();
  const { rows } = await conn.query(query, queryParams);
  const entries = migrationStatusEntrySchema.array().parse(rows);
  return entries;
}

const liveCronJobsInputSchema = z
  .object({
    job_name_pattern: z.string().nullish(),
  })
  .nullish();

const liveCronJobsEntrySchema = z.strictObject({
  job_name: z.string().nullish(),
  job_id: z.coerce.number(),
  schedule: z.string(),
  command: z.string(),
  active: z.boolean(),
});

export type LiveCronJobsEntry = z.infer<typeof liveCronJobsEntrySchema>;

export async function liveCronJobs(
  ctxData: unknown,
): Promise<LiveCronJobsEntry[]> {
  const ctxParams = wrapZodError(() => liveCronJobsInputSchema.parse(ctxData));
  const filterExpr = ((): ToSQL => {
    const filters: ToSQL[] = [sql`true`];
    if (ctxParams?.job_name_pattern) {
      filters.push(sql`jobname like ${ctxParams.job_name_pattern}`);
    }
    return join(filters, " and ");
  })();
  const { query, params: queryParams } = sql`
    select
        jobname job_name,
        jobid job_id,
        schedule,
        command,
        active
    from
        cron.job
    where ${filterExpr}
    order by jobid desc
    limit 1000
    `.toNumericParamQuery();

  const conn = getPG();
  const { rows } = await conn.query(query, queryParams);
  const entries = liveCronJobsEntrySchema.array().parse(rows);
  return entries;
}
