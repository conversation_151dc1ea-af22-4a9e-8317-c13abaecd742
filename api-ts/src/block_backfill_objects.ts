// A list of objects to block backfilling for. This is intended for temporary
// "stop the bleeding" situations.
import { ObjectType } from "./schema";

export type BlockBackfillObject = {
  // The object ID constructed from make_object_id.
  traditionalObjectId: string;
  // The brainstore object ID.
  brainstoreObjectId: string;
  // ObjectType/object_id representation for other parts of the codebase.
  objectType: ObjectType;
  objectId: string;
};

// IMPORTANT: Once canContainRowRefs (in brainstore.ts) is enabled, it is no
// longer possible to add objects to this list, because any such objects may
// already have row refs in postgres and thus must be queried by brainstore.
//
// So if we need to modify this list, we should move the implementation to
// `postgres_wal.rs` in brainstore.
export const blockBackfillObjects: BlockBackfillObject[] = [
  {
    traditionalObjectId: "global_log:a41ff717-634f-460c-aa00-b143d8772571",
    brainstoreObjectId: "project_logs:a41ff717-634f-460c-aa00-b143d8772571",
    objectType: "project_logs",
    objectId: "a41ff717-634f-460c-aa00-b143d8772571",
  },
];

export function isBlockBackfillObject(
  objectType: ObjectType,
  objectId: string,
): boolean {
  return blockBackfillObjects.some(
    (blockedObj) =>
      blockedObj.objectType === objectType && blockedObj.objectId === objectId,
  );
}
