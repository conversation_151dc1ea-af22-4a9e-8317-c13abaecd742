import { describe, it, expect, beforeAll, beforeEach, afterAll } from "vitest";
import {
  checkLogsOrgRateLimit,
  clearRateLimitStateForTests,
} from "./rate-limit";
import { getRedis } from "./redis";
import { TooManyRequestsError } from "./util";
import { RedisClientType } from "redis";

// Test constants
const TEST_ORG_ID = "test-rate-limit-org-ts";
const TEST_APP_ORIGIN = "http://localhost:3000";
const TEST_AUTH_TOKEN = "test-token";

// Environment variable names
const ENV_ORG_LIMITS = "RATELIMIT_API_LOGS_ORG";
const ENV_WINDOW_SECS = "RATELIMIT_API_LOGS_ORG_WINDOW_SECS";
const ENV_ENFORCE = "RATELIMIT_API_LOGS_ORG_ENFORCE";

describe("checkLogsOrgRateLimit", () => {
  let redis: RedisClientType;
  let originalEnv: Record<string, string | undefined>;
  const envVars = [ENV_ORG_LIMITS, ENV_WINDOW_SECS, ENV_ENFORCE];

  beforeAll(async () => {
    redis = await getRedis();
    // Save original environment variables
    originalEnv = {} as Record<string, string | undefined>;
    for (const key of envVars) {
      if (process.env[key]) {
        originalEnv[key] = process.env[key];
      }
    }
  });

  afterAll(() => {
    // Restore original environment variables
    envVars.forEach((key) => {
      process.env[key] = originalEnv[key];
    });
  });

  beforeEach(async () => {
    // Clear all rate limit state
    await clearRateLimitStateForTests();

    // Reset environment variables
    delete process.env[ENV_ORG_LIMITS];
    delete process.env[ENV_WINDOW_SECS];
    delete process.env[ENV_ENFORCE];
  });

  it("should connect to redis and make a basic rate limit check", async () => {
    // Verify redis connection
    expect(redis).toBeDefined();
    await expect(redis.ping()).resolves.toBe("PONG");

    // Make a single request that should succeed
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();
  });

  it("should throw an error if the user is over the limit and rate limiting is enforced", async () => {
    // Set a low limit for the org
    process.env[ENV_ORG_LIMITS] = `${TEST_ORG_ID}=1`;
    process.env[ENV_WINDOW_SECS] = "60";
    process.env[ENV_ENFORCE] = "true";

    // First request should succeed
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();

    // Second request should fail due to rate limit
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).rejects.toThrow(TooManyRequestsError);
  });

  it("should not throw an error when over the limit but rate limiting is not enforced", async () => {
    // Set a low limit for the org but don't enforce it
    process.env[ENV_ORG_LIMITS] = `${TEST_ORG_ID}=1`;
    process.env[ENV_WINDOW_SECS] = "60";
    process.env[ENV_ENFORCE] = "false";

    // First request should succeed
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();

    // Second request should also succeed (even though over limit) because enforcement is disabled
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();
  });

  it("should gracefully handle broken configuration", async () => {
    // Test with invalid limit value
    process.env[ENV_ORG_LIMITS] = `${TEST_ORG_ID}=not-a-number`;
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();

    // Test with invalid timeframe
    process.env[ENV_ORG_LIMITS] = `${TEST_ORG_ID}=10`;
    process.env[ENV_WINDOW_SECS] = "not-a-number";
    await clearRateLimitStateForTests();
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();
  });

  it("should not limit orgs with no configured limits", async () => {
    // No environment variables set
    const makeRequest = () =>
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: "unlimited-org",
        numLogs: 1,
      });

    // Should be able to make multiple requests without limits
    await expect(makeRequest()).resolves.not.toThrow();
    await expect(makeRequest()).resolves.not.toThrow();
    await expect(makeRequest()).resolves.not.toThrow();
  });

  it("should reset limits after the timeframe expires", async () => {
    // Set a low limit with a short timeframe
    process.env[ENV_ORG_LIMITS] = `${TEST_ORG_ID}=1`;
    process.env[ENV_WINDOW_SECS] = "1"; // 1 second
    process.env[ENV_ENFORCE] = "true";

    const makeRequest = () =>
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      });

    // First request should succeed
    await expect(makeRequest()).resolves.not.toThrow();

    // Second request should fail immediately
    await expect(makeRequest()).rejects.toThrow(TooManyRequestsError);

    // Wait for the timeframe to expire
    await new Promise((resolve) => setTimeout(resolve, 1100)); // Wait 1.1 seconds

    // Should be able to make another request after timeframe expires
    await expect(makeRequest()).resolves.not.toThrow();
  });

  it("should handle multiple orgs in configuration", async () => {
    const secondOrgId = "second-test-org";

    // Set limits for multiple orgs
    process.env[ENV_ORG_LIMITS] = `${TEST_ORG_ID}=5, ${secondOrgId}=50`;
    process.env[ENV_WINDOW_SECS] = "60";
    process.env[ENV_ENFORCE] = "true";

    // First org should be limited to 5
    for (let i = 0; i < 5; i++) {
      await expect(
        checkLogsOrgRateLimit({
          appOrigin: TEST_APP_ORIGIN,
          authToken: TEST_AUTH_TOKEN,
          orgId: TEST_ORG_ID,
          numLogs: 1,
        }),
      ).resolves.not.toThrow();
    }

    // 6th request should fail for first org
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: TEST_ORG_ID,
        numLogs: 1,
      }),
    ).rejects.toThrow(TooManyRequestsError);

    // Second org should still work (different limit)
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: secondOrgId,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();
  });

  it("should handle mixed valid and invalid org limits in configuration", async () => {
    const validOrgId = "valid-org";
    const invalidOrgId = "invalid-org";

    // Set config with one valid and one invalid org limit
    process.env[ENV_ORG_LIMITS] =
      `${validOrgId}=5, ${invalidOrgId}=not-a-number`;
    process.env[ENV_WINDOW_SECS] = "60";
    process.env[ENV_ENFORCE] = "true";

    // Valid org should be rate limited
    for (let i = 0; i < 5; i++) {
      await expect(
        checkLogsOrgRateLimit({
          appOrigin: TEST_APP_ORIGIN,
          authToken: TEST_AUTH_TOKEN,
          orgId: validOrgId,
          numLogs: 1,
        }),
      ).resolves.not.toThrow();
    }

    // 6th request should fail for valid org
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: validOrgId,
        numLogs: 1,
      }),
    ).rejects.toThrow(TooManyRequestsError);

    // Invalid org should not be rate limited (no limit configured)
    await expect(
      checkLogsOrgRateLimit({
        appOrigin: TEST_APP_ORIGIN,
        authToken: TEST_AUTH_TOKEN,
        orgId: invalidOrgId,
        numLogs: 1,
      }),
    ).resolves.not.toThrow();
  });
});
