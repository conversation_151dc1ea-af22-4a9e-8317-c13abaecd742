import { LEAF_OBJECT_ID_FIELDS, getLeafObjectId } from "./schema";
import {
  PARENT_ID_FIELD,
  OBJECT_DELETE_FIELD,
  mapSetNotPresent,
} from "braintrust/util";
import { ObjectIdsUnion, omitSchema } from "@braintrust/local/api-schema";
import { z } from "zod";

export function makeObjectIdKey(row: ObjectIdsUnion): string {
  return JSON.stringify(
    LEAF_OBJECT_ID_FIELDS.map((f) => getLeafObjectId(row, f)),
  );
}

export type SpanParentDeleted = {
  kind: "deleted";
  id: string;
  objectIdKey: string;
};

export type SpanParentLive = {
  kind: "live";
  id: string;
  objectIdKey: string;
  span_id: string;
  root_span_id: string;
};

type ResolvableRowBase = {
  [PARENT_ID_FIELD]: string;
  span_id: string;
  root_span_id: string | null;
  span_parents: string[];
};

export function spanParentResolveRow<T extends ResolvableRowBase>({
  spanParent,
  objectIds,
  row,
  addInvalidParentId,
}: {
  spanParent: SpanParentDeleted | SpanParentLive;
  objectIds: ObjectIdsUnion;
  row: T;
  addInvalidParentId: (invalidParentId: string) => void;
}): Omit<Omit<T, typeof PARENT_ID_FIELD>, "root_span_id"> & {
  [PARENT_ID_FIELD]: null;
  root_span_id: string;
} {
  if (spanParent.kind === "deleted") {
    const { [PARENT_ID_FIELD]: rowParentId, ...rowRest } = row;
    // When a row-to-insert references a deleted/missing row as its parent_id,
    // we resolve the reference by promoting the row to a root span.
    //
    // This can happen in normal operation, eg if someone stores a request's id,
    // someone else deletes it in the UI, and then the first person stores a
    // subspan, so we don't want to fail the entire batch. Instead, we just
    // ignore the parent, and save the intended parent as metadata, in case it
    // needs to be debugged later (NOTE: once we introduce an errors field, it
    // would be great to store here).
    if (
      !(
        rowParentId === spanParent.id &&
        makeObjectIdKey(objectIds) === spanParent.objectIdKey
      )
    ) {
      throw new Error(
        `id mismatch between SpanParentDeleted and row being resolved`,
      );
    }
    addInvalidParentId(rowParentId);
    return {
      ...rowRest,
      // Set the row as a root span. Clear out the span parents.
      root_span_id: rowRest.span_id,
      span_parents: [],
      [PARENT_ID_FIELD]: null,
    };
  } else if (spanParent.kind === "live") {
    const { [PARENT_ID_FIELD]: rowParentId, ...rowRest } = row;
    if (
      !(
        rowParentId === spanParent.id &&
        makeObjectIdKey(objectIds) === spanParent.objectIdKey
      )
    ) {
      throw new Error(
        `id mismatch between SpanParentLive and row being resolved`,
      );
    }
    return {
      ...rowRest,
      root_span_id: spanParent.root_span_id,
      span_parents: [spanParent.span_id],
      [PARENT_ID_FIELD]: null,
    };
  } else {
    const x: never = spanParent;
    throw new Error(`Unknown span parent type: ${JSON.stringify(x)}`);
  }
}

// Can parse against this schema to check if a row is resolved.
export const resolvedRowSchema = z.union([
  z.object({
    [OBJECT_DELETE_FIELD]: z.literal(true),
  }),
  z.object({
    [OBJECT_DELETE_FIELD]: z.literal(false),
    [PARENT_ID_FIELD]: omitSchema,
    span_id: z.string(),
    root_span_id: z.string(),
  }),
]);

export type ResolvedRow = z.infer<typeof resolvedRowSchema>;

export type IdToSpanParentMap = Map<string, SpanParentDeleted | SpanParentLive>;

export function makeIdToSpanParentKey({
  id,
  objectIdKey,
}: {
  id: string;
  objectIdKey: string;
}): string {
  return JSON.stringify([id, objectIdKey]);
}

// Add the given resolved row to the given map.
export function addResolvedRow({
  row,
  rowId,
  objectIds,
  idToSpanParent,
}: {
  row: ResolvedRow;
  rowId: string;
  objectIds: ObjectIdsUnion;
  idToSpanParent: IdToSpanParentMap;
}) {
  const objectIdKey = makeObjectIdKey(objectIds);
  const idToSpanParentKey = makeIdToSpanParentKey({ id: rowId, objectIdKey });
  if (row[OBJECT_DELETE_FIELD]) {
    mapSetNotPresent(idToSpanParent, idToSpanParentKey, {
      kind: "deleted",
      id: rowId,
      objectIdKey,
    });
  } else {
    mapSetNotPresent(idToSpanParent, idToSpanParentKey, {
      kind: "live",
      id: rowId,
      objectIdKey,
      span_id: row.span_id,
      root_span_id: row.root_span_id,
    });
  }
}

export type MaybeResolvableRowBase = Omit<
  ResolvableRowBase,
  typeof PARENT_ID_FIELD
> & {
  id: string;
  [OBJECT_DELETE_FIELD]: boolean;
  [PARENT_ID_FIELD]: string | null;
};

// After populating an IdToSpanParentMap with parent span info, we must go
// through and resolve any PARENT_ID_FIELD references in the rows that we have
// information for. This is slightly tricky, because rows may form a nested
// chain of references, so we must resolve in multiple passes.
//
// This function resolves as many PARENT_ID_FIELD references as possible, adding
// successfully-resolved references to the input map for future passes. Any
// unresolved references are left as they are, with their PARENT_ID_FIELD
// reference intact.
export function resolveSpanParents({
  rows,
  idToSpanParent,
  addInvalidParentIdToRow,
}: {
  // Originally I tried making the controlFields of type T, where T extends
  // MaybeResolvableRowBase, but typescript was not letting me re-assign the row
  // to type T after running `spanParentResolveRow`. I figured "upcasting" it to
  // MaybeResolvableRowBase would be better than using a type coercsion on
  // assignment, because we are just re-assigning the row in place.
  rows: { controlFields: MaybeResolvableRowBase; objectIds: ObjectIdsUnion }[];
  idToSpanParent: IdToSpanParentMap;
  addInvalidParentIdToRow: (rowIdx: number, invalidParentId: string) => void;
}) {
  const unresolvedParentIdRowIndices = new Set<number>();
  rows.forEach(({ controlFields }, idx) => {
    if (controlFields[PARENT_ID_FIELD]) {
      unresolvedParentIdRowIndices.add(idx);
    }
  });
  while (true) {
    const resolvedIndices = new Set<number>();
    unresolvedParentIdRowIndices.forEach((idx) => {
      const { controlFields, objectIds } = rows[idx];
      if (!controlFields[PARENT_ID_FIELD]) {
        throw new Error("Impossible");
      }
      const objectIdKey = makeObjectIdKey(objectIds);
      const spanParentInfo = idToSpanParent.get(
        makeIdToSpanParentKey({
          id: controlFields[PARENT_ID_FIELD],
          objectIdKey,
        }),
      );
      if (spanParentInfo !== undefined) {
        resolvedIndices.add(idx);
        const resolvedRow = spanParentResolveRow({
          spanParent: spanParentInfo,
          objectIds,
          row: {
            ...controlFields,
            [PARENT_ID_FIELD]: controlFields[PARENT_ID_FIELD],
          },
          addInvalidParentId: (id: string) => addInvalidParentIdToRow(idx, id),
        });
        // The redundant branching is necessary to satisfy the boolean literals
        // in the type signature.
        if (controlFields[OBJECT_DELETE_FIELD]) {
          addResolvedRow({
            row: { ...resolvedRow, [OBJECT_DELETE_FIELD]: true },
            rowId: controlFields.id,
            objectIds,
            idToSpanParent,
          });
        } else {
          addResolvedRow({
            row: { ...resolvedRow, [OBJECT_DELETE_FIELD]: false },
            rowId: controlFields.id,
            objectIds,
            idToSpanParent,
          });
        }
        rows[idx].controlFields = resolvedRow;
      }
    });
    resolvedIndices.forEach((idx) => unresolvedParentIdRowIndices.delete(idx));
    // Quit once we can no longer resolve any references.
    if (resolvedIndices.size === 0) {
      return;
    }
  }
}
