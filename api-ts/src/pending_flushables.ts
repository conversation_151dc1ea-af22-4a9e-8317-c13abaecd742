import { IS_LOCAL } from "./env";
import { getLogger } from "./instrumentation/logger";

const FLUSHABLE_TIMEOUT_MS = 10000;
const MAX_FLUSHABLE_STATES = 10240; // There's no reason to make this number too low

export type PendingFlushable = {
  promise: Promise<void>;
  completed: boolean;
  debugId?: string;
};

// A container which manages a set of pending promises.
export class PendingFlushables {
  private flushables: PendingFlushable[] = [];

  public get size() {
    return this.flushables.length;
  }

  public add(promise: Promise<void>, debugId?: string) {
    const pino = getLogger().child({
      task: "PendingFlushables",
    });
    this.flushables.push({ promise, completed: false, debugId });
    if (this.flushables.length >= MAX_FLUSHABLE_STATES) {
      if (!IS_LOCAL) {
        pino.warn("Too many states in memory, flushing");
      }
      this.flush().catch((e) => {
        pino.error(
          { error: e },
          "Unhandled exception in pending flushables flush",
        );
      });
    }
  }

  // Returns the number of promises that were resolved in this flush.
  public async flush(): Promise<number> {
    const pino = getLogger().child({
      task: "PendingFlushables",
    });
    const flushables: Promise<void>[] = [];
    for (const flushable of this.flushables) {
      const { promise, completed, debugId } = flushable;
      if (completed) {
        continue;
      }
      flushables.push(
        (async () => {
          try {
            const timeoutReturnObj = {};
            const timeoutPromise = new Promise((resolve) =>
              setTimeout(resolve, FLUSHABLE_TIMEOUT_MS, timeoutReturnObj),
            );
            if (debugId) {
              pino.info({ debugId }, "Waiting on flushable");
            }
            const res = await Promise.race([promise, timeoutPromise]);
            if (debugId) {
              pino.info({ debugId }, "Finished flushing");
            }
            if (Object.is(res, timeoutReturnObj)) {
              pino.warn("Timed out while flushing. Ignoring");
            }
          } catch (e) {
            pino.error({ error: e }, "Error surfaced while flushing");
          }
          flushable.completed = true;
        })(),
      );
    }

    await Promise.all(flushables);
    this.flushables = this.flushables.filter((f) => !f.completed);

    return flushables.length;
  }
}

export const PENDING_FLUSHABLES = new PendingFlushables();
