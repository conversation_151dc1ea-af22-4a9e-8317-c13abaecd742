// This has to be done before the API is initialized
import "./instrumentation/setup_dd";

import {
  lambdaRequestTracker,
  pinoLambdaDestination,
  StructuredLogFormatter,
} from "pino-lambda";
import { initPinoLogger } from "./instrumentation/logger";

initPinoLogger(
  pinoLambdaDestination({
    formatter: new StructuredLogFormatter(),
  }),
);
const withPinoRequest = lambdaRequestTracker();

import type { APIGatewayProxyHandler, APIGatewayProxyResult } from "aws-lambda";
import serverlessHttp from "serverless-http";
import { app } from "./api";
import { initializeEnv } from "./env";
import { PENDING_FLUSHABLES } from "./pending_flushables";
import { otelTraced } from "./instrumentation/api";

// Wrap the Express app with serverless-http
const serverlessHandler = serverlessHttp(app, {
  // We are using https://github.com/dougmoscrop/serverless-http, which does not have
  // much info about this (but I assume just setting "true" maps to how we have API gateway
  // configured). There's a similar issue called serverless-express, which has a related
  // github issue: https://github.com/CodeGenieApp/serverless-express/issues/104.
  binary: true,
});

initializeEnv(false);

export const handler: APIGatewayProxyHandler = async (event, context) => {
  // We set this in every lambda so that the response is sent immediately (not after
  // the event loop is empty).
  context.callbackWaitsForEmptyEventLoop = false;

  withPinoRequest(event, context);

  // Return the response from the Express app
  try {
    return await otelTraced(`${event.httpMethod} ${event.path}`, async () => {
      try {
        const response = await serverlessHandler(event, context);
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        return response as APIGatewayProxyResult;
      } finally {
        await otelTraced("flush_pending_flushables", async () =>
          PENDING_FLUSHABLES.flush(),
        );
      }
    });
  } finally {
    // In practice, this is a no-op, because we never use the otel provider in lambda (currently). But I wanted to
    // implement this while I still had the context fresh in my mind.
    // PENDING_FLUSHABLES.add(shutdownTracer());
  }
};
