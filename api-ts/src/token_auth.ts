import { customFetchRequest } from "./custom_fetch";
import { sha1 } from "./hash";
import { getLogger } from "./instrumentation/logger";
import { getRedis } from "./redis";
import { ForbiddenError, HTTPError, postDefaultHeaders } from "./util";
import { Me, meSchema } from "@braintrust/local/app-schema";

export async function checkTokenAuthorized({
  ctxToken,
  appOrigin,
  checkSysadmin,
}: {
  ctxToken: string | undefined;
  appOrigin: string;
  checkSysadmin?: boolean;
}): Promise<{ me: Promise<Me> }> {
  if (!ctxToken) {
    throw new ForbiddenError("Endpoint requires authorization");
  }

  const redisClient = await getRedis();
  const cacheKey = `check_token_authorized:${sha1(ctxToken ?? "anon")}${
    checkSysadmin ? ":sysadmin" : ""
  }`;
  const cached = await redisClient.get(cacheKey);
  if (cached) {
    return { me: Promise.resolve(meSchema.parse(JSON.parse(cached))) };
  }

  const headers = postDefaultHeaders({ token: ctxToken });
  const resp = await customFetchRequest(`${appOrigin}/api/self/me`, {
    method: "POST",
    headers,
    body: JSON.stringify({ check_sysadmin: checkSysadmin }),
  });
  if (!resp.ok) {
    throw new HTTPError(resp.statusCode, await resp.body.text());
  }
  return {
    me: resp.body.json().then((x) => {
      redisClient
        .set(cacheKey, JSON.stringify(x), {
          EX: 60, // Only cache credentials for a minute
        })
        .catch((e) => {
          getLogger().error({ error: e }, "Error caching token auth");
        });
      return meSchema.parse(x);
    }),
  };
}
