import { expect, test } from "vitest";
import { findChangedPath, findObjectAtPath } from "./audit_log";

test("findChangedPath basic", () => {
  expect(findChangedPath({ 0: 1 }, { 0: 2 })).toEqual(["0"]);
  expect(findChangedPath({ a: 4, b: 5 }, { a: 4, b: 5 })).toEqual([]);
  expect(findChangedPath({ a: 4, b: 5 }, { a: 4, b: 6 })).toEqual(["b"]);
  expect(
    findChangedPath({ a: { b: { c: 10 } } }, { a: { b: { c: 11 } } }),
  ).toEqual(["a", "b", "c"]);
  expect(
    findChangedPath(
      { a: { b: { c: 10 } }, x: { y: 40 } },
      { a: { b: { c: 10 } }, x: { y: 41 } },
    ),
  ).toEqual(["x", "y"]);
  expect(
    findChangedPath({ a: { b: { c: 10 } }, x: { y: 40 } }, { x: { y: 41 } }),
  ).toEqual(["x", "y"]);
  expect(findChangedPath({ a: { b: { c: 10 } } }, { x: { y: 41 } })).toEqual([
    "x",
  ]);
  expect(
    findChangedPath(
      { a: { b: { c: 10, d: 20 } } },
      { a: { b: { c: 10, d: 30 } } },
    ),
  ).toEqual(["a", "b", "d"]);
  expect(
    findChangedPath({ a: { b: { c: 10 } } }, { a: { b: { c: 10, d: 15 } } }),
  ).toEqual(["a", "b", "d"]);
  expect(
    findChangedPath(
      { a: { b: { c: 10 } }, y: 2 },
      { a: { b: { c: 20 } }, y: 2 },
    ),
  ).toEqual(["a", "b", "c"]);
  expect(findChangedPath({}, { a: 1 })).toEqual(["a"]);
  expect(findChangedPath({ a: [1, 2, 3] }, { a: [1, 2, 3] })).toEqual([]);
  expect(findChangedPath({ a: [1, 2, 3] }, { a: [1, 2, 4] })).toEqual(["a"]);
  expect(
    findChangedPath({ a: [1, 2, { b: 3 }] }, { a: [1, 2, { b: 3 }] }),
  ).toEqual([]);
  expect(
    findChangedPath({ a: [1, 2, { b: 3 }] }, { a: [1, 2, { b: 4 }] }),
  ).toEqual(["a"]);
});

test("findObjectAtPath basic", () => {
  expect(findObjectAtPath("boo", [])).toEqual("boo");
  expect(findObjectAtPath("boo", ["stuff"])).toEqual(null);
  expect(findObjectAtPath([0, 1, 2, 3], ["2"])).toEqual(2);
  expect(findObjectAtPath([0, 1, 2, 3], ["10"])).toEqual(null);
  expect(findObjectAtPath({ a: { b: { c: 10 } } }, ["a", "b", "c"])).toEqual(
    10,
  );
  expect(findObjectAtPath({ a: { b: { c: 10 } } }, ["a", "b"])).toEqual({
    c: 10,
  });
  expect(findObjectAtPath({ a: { b: { c: 10 } } }, ["a", "b", "q"])).toEqual(
    null,
  );
});
