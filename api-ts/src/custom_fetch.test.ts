import {
  <PERSON><PERSON><PERSON><PERSON>,
  getG<PERSON><PERSON><PERSON><PERSON>patcher,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  setGlobalDispatcher,
} from "undici";
import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  test,
  vi,
} from "vitest";
import { customFetchRequest, initializeGlobalDispatcher } from "./custom_fetch";
import * as env from "./env";
import { createMockRedisClient } from "./redis.test";
import { TooManyRequestsError } from "./util";
import { getLogger } from "./instrumentation/logger";

const mockRedis = createMockRedisClient();

vi.mock("./redis", () => ({
  getRedis: () => Promise.resolve(mockRedis.client),
}));

vi.mock("./env", () => ({
  ALLOWED_ORIGIN: "http://localhost:3000",
  OUTBOUND_RATE_LIMIT_WINDOW_MINUTES: 1,
  OUTBOUND_RATE_LIMIT_MAX_REQUESTS: 5,
}));

// Mock the logger
vi.mock("./instrumentation/logger", () => ({
  getLogger: vi.fn().mockReturnValue({
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  }),
}));

let mockAgent: MockAgent;
let originalDispatcher: Dispatcher;
let mockPools: Record<string, MockPool>;

beforeAll(async () => {
  originalDispatcher = getGlobalDispatcher();
});

beforeEach(async () => {
  mockAgent = new MockAgent();
  mockAgent.disableNetConnect();

  // NOTE: we must use real timers. Unclear why!
  vi.useRealTimers();

  mockPools = {
    localhost: mockAgent.get("http://localhost:3000"),
    otherDomain: mockAgent.get("http://other-domain.com"),
  };

  // Until we move to v7 undici we need to pass the mockAgent into the RetryAgent.
  initializeGlobalDispatcher({
    agent: mockAgent,
    force: true,
  });

  mockRedis.store.clear();
  vi.clearAllMocks();

  // Reset env vars to default values before each test
  vi.mocked(env).ALLOWED_ORIGIN = "http://localhost:3000";
  vi.mocked(env).OUTBOUND_RATE_LIMIT_WINDOW_MINUTES = 1;
  vi.mocked(env).OUTBOUND_RATE_LIMIT_MAX_REQUESTS = 5;
});

afterEach(async () => {
  await mockAgent.close();
  vi.clearAllMocks();
});

afterAll(() => {
  setGlobalDispatcher(originalDispatcher);
});

describe("customFetchRequest", () => {
  test("successfully mocks HTTP requests", async () => {
    mockPools.localhost
      .intercept({
        path: "/test",
        method: "GET",
      })
      .reply(200, { data: "success" });

    const response = await customFetchRequest("http://localhost:3000/test");
    expect(response.statusCode).toBe(200);
    expect(response.ok).toBe(true);
    const body = await response.body.json();
    expect(body).toEqual({ data: "success" });
  });

  test("retries failed requests", async () => {
    let requestCount = 0;
    mockPools.localhost
      .intercept({
        path: "/test",
        method: "GET",
      })
      .reply(() => {
        requestCount++;
        if (requestCount <= 2) {
          return {
            statusCode: 500,
            data: JSON.stringify({ error: "Internal Server Error" }),
            responseOptions: {
              headers: {
                "Retry-After": "0.0001",
              },
            },
          };
        }
        return {
          statusCode: 200,
          data: JSON.stringify({ success: true }),
        };
      })
      .persist();

    const response = await customFetchRequest("http://localhost:3000/test");

    expect(response.statusCode).toBe(200);
    expect(requestCount).toBe(3);

    const body = await response.body.json();
    expect(body).toEqual({ success: true });
  });

  test("enforces rate limits for allowed origins", async () => {
    const startTime = Date.now();
    vi.setSystemTime(startTime);

    mockPools.localhost
      .intercept({
        path: "/test",
        method: "GET",
      })
      .reply(200, { data: "success" })
      .persist();

    // Make requests up to the limit
    for (let i = 0; i < 5; i++) {
      const response = await customFetchRequest("http://localhost:3000/test");
      expect(response.statusCode).toBe(200);
    }

    // Next request should throw rate limit error
    await expect(
      customFetchRequest("http://localhost:3000/test"),
    ).rejects.toThrow(TooManyRequestsError);

    // Simulate time passing - move forward by the rate limit window
    const newTime =
      startTime + env.OUTBOUND_RATE_LIMIT_WINDOW_MINUTES * 60 * 1000;
    vi.setSystemTime(newTime);

    // After window expires, should be able to make requests again
    const response = await customFetchRequest("http://localhost:3000/test");
    expect(response.statusCode).toBe(200);
  });

  test("skips rate limiting for non-allowed origins", async () => {
    const incrSpy = vi.spyOn(mockRedis.client, "incr");

    mockPools.otherDomain
      .intercept({
        path: "/test",
        method: "GET",
      })
      .reply(200, { data: "success" });

    await customFetchRequest("http://other-domain.com/test");

    expect(incrSpy).not.toHaveBeenCalled();
  });

  test("handles unexpected thrown errors", async () => {
    const loggerErrorSpy = vi.spyOn(getLogger(), "error");

    // this test is an exception since we can't return responseOptions
    vi.useFakeTimers();

    mockPools.localhost
      .intercept({
        path: "/test",
        method: "GET",
      })
      .replyWithError(new Error("Network error"))
      .persist();

    const errorPromise = customFetchRequest("http://localhost:3000/test");

    // NOTE: unsure why we can't await here without vitest reporting an uncaught error, but
    // test works without it
    vi.advanceTimersByTimeAsync(20000);

    try {
      await errorPromise;
      throw new Error("Unexpected passed promise");
    } catch (e) {
      expect(e.message).toBe("Network error");

      // capturing that we will log an error when the request fails
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        {
          url: "http://localhost:3000/test",
          error: expect.any(Error),
        },
        "Error when requesting url",
      );
    }
  });

  test("handles permanent errors from API response", async () => {
    const loggerErrorSpy = vi.spyOn(getLogger(), "error");

    let requestCount = 0;
    mockPools.localhost
      .intercept({
        path: "/test",
        method: "GET",
      })
      .reply(() => {
        requestCount++;
        return {
          statusCode: 500, // this could be one of [429, 500, 502, 503, 504]
          data: JSON.stringify({ error: "Permanent error" }),
          responseOptions: {
            headers: {
              // Makes sure our tests run quickly
              "Retry-After": "0.0001",
            },
          },
        };
      })
      .persist();

    const errorPromise = customFetchRequest("http://localhost:3000/test");

    try {
      await errorPromise;
      throw new Error("Unexpected passed promise");
    } catch (e) {
      // 6 retries, 1 initial request, 5 retries
      expect(requestCount).toBe(6);

      expect(e.message).toBe("Request failed");

      // capturing that we will log an error when the request fails
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        {
          url: "http://localhost:3000/test",
          error: expect.any(Error),
        },
        "Error when requesting url",
      );
    }
  });

  test("sends correct headers and body", async () => {
    const headers = {
      "Content-Type": "application/json",
      Authorization: "Bearer token",
    };
    const body = JSON.stringify({ test: true });

    mockPools.localhost
      .intercept({
        path: "/test",
        method: "POST",
        headers,
        body,
      })
      .reply(201, { success: true });

    const response = await customFetchRequest("http://localhost:3000/test", {
      method: "POST",
      headers,
      body,
    });

    expect(response.statusCode).toBe(201);
    const responseBody = await response.body.json();
    expect(responseBody).toEqual({ success: true });
  });

  test("does not retry POST requests", async () => {
    let requestCount = 0;

    mockPools.localhost
      .intercept({
        path: "/test",
        method: "POST",
        body: JSON.stringify({ data: "test" }),
      })
      .reply(() => {
        requestCount++;
        return {
          statusCode: 500,
          data: JSON.stringify({ error: "Internal Server Error" }),
        };
      })
      .persist();

    const errorPromise = customFetchRequest("http://localhost:3000/test", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ data: "test" }),
    });

    expect(errorPromise).rejects.toThrow("Request failed");
  });

  test("skips rate limiting when max requests is set to 0", async () => {
    // Override max requests for this test
    vi.mocked(env).OUTBOUND_RATE_LIMIT_MAX_REQUESTS = 0;

    mockPools.localhost
      .intercept({
        path: "/test",
        method: "GET",
      })
      .reply(200, { data: "success" })
      .persist();

    // Make multiple requests - should all succeed without rate limiting
    for (let i = 0; i < 6; i++) {
      const response = await customFetchRequest("http://localhost:3000/test");
      expect(response.statusCode).toBe(200);
    }
  });
});
