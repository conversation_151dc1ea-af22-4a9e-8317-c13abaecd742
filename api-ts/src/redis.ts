import { REDIS_HOST, REDIS_PORT, REDIS_URL } from "./env";
import { createClient as createRedisClient, RedisClientType } from "redis";
import { getLogger } from "./instrumentation/logger";
import { extractErrorText, InternalServerError } from "./util";

let redisClient: RedisClientType | null = null;
let lastRedisErrorTime = 0;
let lastRedisReconnectTime = 0;

export async function getRedis(): Promise<RedisClientType> {
  const networkArgs = {
    keepAliveInitialDelay: 30_000,
  };
  if (redisClient === null) {
    let newClient: RedisClientType;
    if (REDIS_URL) {
      newClient = createRedisClient({
        url: REDIS_URL,
        pingInterval: 25_000,
        // This is necessary for TLS connections which don't specify a
        // client-side certificate. Would be nice to auto-detect this if
        // necessary.
        // socket: {
        //     tls: true,
        //     rejectUnauthorized: false
        // },
        socket: {
          ...networkArgs,
        },
      });
    } else if (REDIS_HOST || REDIS_PORT) {
      newClient = createRedisClient({
        pingInterval: 25_000,
        socket: {
          host: REDIS_HOST,
          port: REDIS_PORT,
          ...networkArgs,
        },
      });
    } else {
      throw new Error("Must either set REDIS_URL or REDIS_HOST");
    }

    newClient.on("error", (err) => {
      const now = Date.now();
      if (now - lastRedisErrorTime >= 10_000) {
        // This error message is quite noisy, so only print it once in a while.
        getLogger().error(
          { err },
          "Redis socket error. This means the Braintrust API cannot connect to Redis, and therefore will not be functional.",
        );
        lastRedisErrorTime = now;
      }
    });
    newClient.on("reconnecting", (ctx) => {
      const now = Date.now();
      if (now - lastRedisReconnectTime >= 10_000) {
        getLogger().warn({ ctx }, "Reconnecting to Redis.");
        lastRedisReconnectTime = now;
      }
    });

    try {
      await newClient.connect();
    } catch (e) {
      getLogger().error(
        { error: extractErrorText(e) },
        "Failed to connect to Redis",
      );
      throw new InternalServerError(
        `Failed to connect to Redis (see logs for details)`,
      );
    }
    redisClient = newClient;
  }

  return redisClient;
}

/*
 * Creates a pub/sub listener on the given channel.
 *
 * The listener will resolve when "stop" is published to the channel.
 * The listener will also clean up the channel when it is resolved or rejected.
 *
 */
export function createChannelListener(channel: string) {
  let resolved = false;
  let redis: RedisClientType | undefined;

  const cleanup = async () => {
    if (redis) {
      await redis.unsubscribe(channel);
    }
  };

  const stopPromise = new Promise<void>(async (resolve, reject) => {
    try {
      const sharedRedis = await getRedis();
      // duplicate the redis client to safely enter subscribe mode
      redis = sharedRedis.duplicate();
      await redis.connect();
      await redis.subscribe(channel, (message) => {
        if (message === "stop") {
          resolve();
          resolved = true;
        }
      });
    } catch (error) {
      getLogger().error({ error }, "Failed to setup Redis pub/sub");
      reject(error);
    }
  });

  // Wait for the promise to resolve before cleaning up
  void stopPromise.finally(async () => {
    await cleanup();
  });

  return {
    channel,
    promise: stopPromise,
    stopped: () => resolved,
    cleanup,
  };
}

export async function deleteMatchingKeys(pattern: string): Promise<void> {
  const redis = await getRedis();
  let cursor = 0;
  do {
    const result = await redis.scan(cursor, {
      MATCH: pattern,
      COUNT: 1000,
    });
    cursor = result.cursor;
    if (result.keys.length > 0) {
      await redis.del(result.keys);
    }
  } while (cursor !== 0);
}
