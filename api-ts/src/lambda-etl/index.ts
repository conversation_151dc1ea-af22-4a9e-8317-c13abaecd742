// This has to be done before the API is initialized
import "../instrumentation/setup_dd";

import {
  lambdaRequestTracker,
  pinoLambdaDestination,
  StructuredLogFormatter,
} from "pino-lambda";
import { getLogger, initPinoLogger } from "../instrumentation/logger";

initPinoLogger(
  pinoLambdaDestination({
    formatter: new StructuredLogFormatter(),
  }),
);
const withPinoRequest = lambdaRequestTracker();

import type { EventBridgeHandler } from "aws-lambda";
import { brainstoreEnabled } from "../brainstore/brainstore";
import { runBrainstoreEtlLoopRequest } from "../brainstore/backfill";
import { BRAINSTORE_DISABLE_ETL_LOOP } from "../env";

// We want to run this lambda for as long as possible, since these timeouts are used to inform
// how long the loops try to backfill.
const TIMEOUT_S = 800;

async function runBrainstoreEtlLoop() {
  if (!brainstoreEnabled() || BRAINSTORE_DISABLE_ETL_LOOP) {
    return;
  }
  return await runBrainstoreEtlLoopRequest({
    timeout: TIMEOUT_S,
  });
}

export const handler: EventBridgeHandler<"ETL", "ETL", unknown> = async (
  event,
  context,
) => {
  // We set this in every lambda so that the response is sent immediately (not after
  // the event loop is empty).
  context.callbackWaitsForEmptyEventLoop = false;

  withPinoRequest(event, context);

  const brainstoreResult = await runBrainstoreEtlLoop();
  getLogger().info(
    {
      brainstore: brainstoreResult,
    },
    "Brainstore ETL result",
  );
  return {
    brainstore: brainstoreResult,
  };
};
