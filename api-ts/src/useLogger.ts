import { isObject } from "braintrust/util";
import { LOG_LEVEL, TELEMETRY_LOG_LEVEL } from "./env";
import { redactSensitiveData } from "./logging_util";
import { z } from "zod";
import pino from "pino";
import { createPinoLogger } from "./instrumentation/logger";
import { pinoLambdaDestination, StructuredLogFormatter } from "pino-lambda";

export const registry = new Set<string>();

const logLevelsSchema = z.enum(["error", "warn", "info", "debug"]);

/**
 * Valid logging levels that can be used with the logger.
 * Lower severity numbers indicate higher importance.
 */
type LogLevel = Exclude<pino.Level, "fatal" | "trace">;

/**
 * Available logging levels, ordered by severity (lower number = higher severity).
 * Remember: Self-hosted customers may pay for each logged item.
 * @enum {number}
 */
const LEVEL_SEVERITY: Record<LogLevel, number> = {
  /** SEVERITY 0: Fatal errors requiring immediate customer intervention */
  error: 0,
  /** SEVERITY 1: Issues customers should investigate, but not critical */
  warn: 1,
  /** SEVERITY 2: General info, development only, noisy in production */
  info: 2,
  /** SEVERITY 3: Detailed debug info, local development only */
  debug: 3,
};

type LogContextValue =
  | string
  | number
  | boolean
  | null
  | undefined
  | Error
  | Date
  | URL
  | URLSearchParams
  | { [key: string]: LogContextValue }
  | LogContextValue[];

type LogContext = Record<string, LogContextValue>;

interface Logger {
  /**
   * Log fatal errors that require immediate customer intervention.
   * Remember: Self-hosted customers may pay for each logged item.
   */
  error: (message: string, context?: LogContext) => void;

  /**
   * Log potentially harmful situations that customers may want to investigate.
   * Use when something is wrong but the application can still continue.
   */
  warn: (message: string, context?: LogContext) => void;

  /**
   * Log general information, typically only needed during development.
   * Can be very noisy in production, use sparingly.
   */
  info: (message: string, context?: LogContext) => void;

  /**
   * Log detailed debugging information. Most verbose level.
   */
  debug: (message: string, context?: LogContext) => void;
}

const LOG_LEVEL_BY_NAMESPACE = {
  telemetry: TELEMETRY_LOG_LEVEL,
} as const;

export const useLogger = (namespace: string): Logger => {
  const pino = createPinoLogger(
    pinoLambdaDestination({
      formatter: new StructuredLogFormatter(),
    }),
    "debug", // we'll control this at runtime
  );

  if (registry.has(namespace)) {
    throw new Error(`Logger already registered: ${namespace}`);
  }
  registry.add(namespace);

  // resolved at runtime to make testing easier
  const maxLoggingLevel = (() => {
    if (process.env.DEBUG) return LEVEL_SEVERITY.debug;
    if (process.env.NODE_ENV === "test") return -1;
    const level = logLevelsSchema.safeParse(
      LOG_LEVEL_BY_NAMESPACE[
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        namespace as keyof typeof LOG_LEVEL_BY_NAMESPACE
      ] ?? LOG_LEVEL,
    );
    return level.success ? LEVEL_SEVERITY[level.data] : LEVEL_SEVERITY.error;
  })();

  const createLogMethod = (level: LogLevel) => {
    return (message: string, context?: LogContext) => {
      if (LEVEL_SEVERITY[level] > maxLoggingLevel) {
        // Skip logging if the current log level is higher than the maximum allowed log level.
        // i.e. if the maxLoggingLevel is set to 'info' (2), 'info', 'warn' and 'error' messages will be logged but not debug (3).
        return;
      }

      pino[level](
        // TODO: we should add more rich detail like commit hash, request/correlation id, etc.
        { namespace, level, ...sanitize(context ?? {}) },
        message,
      );
    };
  };

  return {
    error: createLogMethod("error"),
    warn: createLogMethod("warn"),
    info: createLogMethod("info"),
    debug: createLogMethod("debug"),
  };
};

const sanitize = (context: LogContext): Record<string, unknown> => {
  const serialized = serialize(context);
  const flattened = flatten(serialized);
  return z.record(z.unknown()).parse(redactSensitiveData(flattened));
};

const flatten = (obj: unknown, prefix = ""): Record<string, unknown> => {
  if (!isObject(obj) && !Array.isArray(obj)) {
    return prefix ? { [prefix]: obj } : {};
  }

  return Object.entries(obj).reduce(
    (acc: Record<string, unknown>, [key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;

      if (value === undefined) {
        return acc;
      }

      if (isObject(value) || Array.isArray(value)) {
        return { ...acc, ...flatten(value, newKey) };
      }

      acc[newKey] = value;
      return acc;
    },
    {},
  );
};

const serialize = (value: unknown): unknown => {
  if (value === null || value === undefined) {
    return undefined;
  }

  if (Array.isArray(value)) {
    return value.map(serialize);
  }

  if (value instanceof Error) {
    return {
      name: value.name,
      message: value.message,
    };
  }

  if (value instanceof Date) {
    return value.toISOString();
  }

  if (value instanceof URL || value instanceof URLSearchParams) {
    return value.toString();
  }

  if (isObject(value)) {
    const result: Record<string, unknown> = {};
    for (const [k, v] of Object.entries(value)) {
      const serialized = serialize(v);
      if (serialized !== undefined) {
        result[k] = serialized;
      }
    }
    return result;
  }

  return value;
};
