import { BadRequestError } from "../util";

export function parseObjectStoreURL(url: string): {
  protocol: "s3" | "az";
  bucket: string;
  key: string;
} {
  const urlObj = new URL(url);
  const protocol = urlObj.protocol.slice(0, -1);
  if (protocol !== "s3" && protocol !== "az") {
    throw new BadRequestError(`Invalid protocol: ${protocol}`);
  }
  const bucket = urlObj.hostname;
  const key = urlObj.pathname.slice(1);
  return { protocol, bucket, key };
}
