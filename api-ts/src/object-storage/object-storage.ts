import {
  GetO<PERSON><PERSON>ommand,
  HeadBucket<PERSON>ommand,
  HeadObjectCommand,
  PutO<PERSON>Command,
  S3Client,
} from "@aws-sdk/client-s3";
import { Readable } from "node:stream";
import type * as streamWeb from "node:stream/web";
import { getBraintrustServerSideEncryption, getS3Client } from "./s3";
import {
  BlobGenerateSasUrlOptions,
  BlobSASPermissions,
  BlobServiceClient,
  UserDelegationKey,
  generateBlobSASQueryParameters,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";
import { DefaultAzureCredential } from "@azure/identity";

import {
  AZURE_STORAGE_ACCOUNT_NAME,
  AZURE_STORAGE_CONNECTION_STRING,
} from "../env";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { InternalServerError } from "../util";
import { Upload } from "@aws-sdk/lib-storage";

export interface HeadResult {
  size: number;
  contentType?: string;
  contentEncoding?: string;
}
export interface GetResult {
  stream: Readable;
  contentType?: string;
  contentEncoding?: string;
}

export interface PutArgs {
  bucket: string;
  key: string;
  contentType: string;
  contentEncoding?: string;
  metadata?: Record<string, string>;
  body: string | Uint8Array | Buffer | Readable;
  allowOverwrite?: boolean;
}
export interface ObjectStore {
  put(args: PutArgs): Promise<void>;

  headBucket(args: { bucket: string }): Promise<void>;
  head(args: { bucket: string; key: string }): Promise<HeadResult>;

  get(args: {
    bucket: string;
    key: string;
    range?: { offset: number; length: number };
  }): Promise<GetResult>;

  signedGetUrl(args: {
    bucket: string;
    key: string;
    expiresIn: number;
    contentType?: string;
    contentDisposition?: string;
  }): Promise<string>;

  signedPutUrl(args: {
    bucket: string;
    key: string;
    expiresIn: number;
    contentType: string;
    allowOverwrite?: boolean;
  }): Promise<string>;
}

export async function makeObjectStore(): Promise<ObjectStore> {
  if (AZURE_STORAGE_CONNECTION_STRING || AZURE_STORAGE_ACCOUNT_NAME) {
    return await AzureObjectStore.make();
  }
  const s3Client = await getS3Client();
  return new S3ObjectStore(s3Client);
}

export class S3ObjectStore implements ObjectStore {
  constructor(private readonly s3Client: S3Client) {}

  async put({
    bucket,
    key,
    contentType,
    contentEncoding,
    metadata,
    body,
    allowOverwrite,
  }: PutArgs): Promise<void> {
    if (body instanceof Readable) {
      const uploader = new Upload({
        client: this.s3Client,
        params: {
          Bucket: bucket,
          Key: key,
          Body: body,
          ContentType: contentType,
          ContentEncoding: contentEncoding,
          ServerSideEncryption: getBraintrustServerSideEncryption(),
          Metadata: metadata,
          IfNoneMatch: allowOverwrite ? undefined : "*",
        },
      });

      await uploader.done();
    } else {
      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: bucket,
          Key: key,
          ContentType: contentType,
          ServerSideEncryption: getBraintrustServerSideEncryption(),
          Body: body,
          Metadata: metadata,
          IfNoneMatch: allowOverwrite ? undefined : "*",
        }),
      );
    }
  }

  async headBucket({ bucket }: { bucket: string }): Promise<void> {
    await this.s3Client.send(new HeadBucketCommand({ Bucket: bucket }));
  }

  async head({
    bucket,
    key,
  }: {
    bucket: string;
    key: string;
  }): Promise<HeadResult> {
    const response = await this.s3Client.send(
      new HeadObjectCommand({ Bucket: bucket, Key: key }),
    );
    if (!response.ContentLength) {
      throw new InternalServerError("Failed to fetch script");
    }
    return {
      size: response.ContentLength,
      contentType: response.ContentType,
      contentEncoding: response.ContentEncoding,
    };
  }

  async get({
    bucket,
    key,
    range,
  }: {
    bucket: string;
    key: string;
    range?: { offset: number; length: number };
  }): Promise<GetResult> {
    const Range = range
      ? `bytes=${range.offset}-${range.offset + range.length - 1}`
      : undefined;
    const getRequest = new GetObjectCommand({
      Bucket: bucket,
      Key: key,
      Range,
    });

    const response = await this.s3Client.send(getRequest);
    if (!response.Body) {
      throw new InternalServerError("Failed to fetch script");
    }

    const requestStream = Readable.fromWeb(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      response.Body.transformToWebStream() as streamWeb.ReadableStream,
    );

    return {
      stream: requestStream,
      contentType: response.ContentType,
      contentEncoding: response.ContentEncoding,
    };
  }

  async signedGetUrl({
    bucket,
    key,
    expiresIn,
    contentType,
    contentDisposition,
  }: {
    bucket: string;
    key: string;
    expiresIn: number;
    contentType?: string;
    contentDisposition?: string;
  }): Promise<string> {
    return getSignedUrl(
      this.s3Client,
      new GetObjectCommand({
        Bucket: bucket,
        Key: key,
        ResponseContentType: contentType,
        ResponseContentDisposition: contentDisposition,
      }),
      { expiresIn },
    );
  }

  async signedPutUrl({
    bucket,
    key,
    expiresIn,
    contentType,
    allowOverwrite,
  }: {
    bucket: string;
    key: string;
    expiresIn: number;
    contentType: string;
    allowOverwrite?: boolean;
  }): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: key,
      // TODO: We don't seem to upload things with the necessary flags to make
      // it work (would likely require changing the SDK code).
      // ServerSideEncryption: getBraintrustServerSideEncryption(),
      IfNoneMatch: allowOverwrite ? undefined : "*",
      ContentType: contentType,
    });
    return await getSignedUrl(this.s3Client, command, { expiresIn });
  }
}

export class AzureObjectStore implements ObjectStore {
  private readonly isSharedKeyAuth: boolean;

  constructor(private readonly blobServiceClient: BlobServiceClient) {
    this.isSharedKeyAuth =
      this.blobServiceClient.credential instanceof StorageSharedKeyCredential;
  }

  static async make(): Promise<AzureObjectStore> {
    let blobServiceClient: BlobServiceClient;

    if (AZURE_STORAGE_ACCOUNT_NAME) {
      blobServiceClient = new BlobServiceClient(
        `https://${AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net`,
        new DefaultAzureCredential(),
      );
    } else if (AZURE_STORAGE_CONNECTION_STRING) {
      blobServiceClient = BlobServiceClient.fromConnectionString(
        AZURE_STORAGE_CONNECTION_STRING,
      );
    } else {
      throw new Error(
        "AZURE_STORAGE_ACCOUNT_NAME or AZURE_STORAGE_CONNECTION_STRING is not defined",
      );
    }
    return new AzureObjectStore(blobServiceClient);
  }

  async put({
    bucket,
    key,
    contentType,
    body,
    metadata,
  }: PutArgs): Promise<void> {
    const containerClient = this.blobServiceClient.getContainerClient(bucket);
    const blockBlobClient = containerClient.getBlockBlobClient(key);
    // Upload the data from a Buffer.
    const bodyBuffer =
      typeof body === "string"
        ? Buffer.from(body)
        : body instanceof Uint8Array
          ? Buffer.from(body)
          : body instanceof Readable
            ? Buffer.from(await body.toArray())
            : body;
    await blockBlobClient.uploadData(bodyBuffer, {
      blobHTTPHeaders: { blobContentType: contentType },
      metadata,
    });
  }

  async headBucket({ bucket }: { bucket: string }): Promise<void> {
    const containerClient = this.blobServiceClient.getContainerClient(bucket);
    await containerClient.getProperties();
  }

  async head({
    bucket,
    key,
  }: {
    bucket: string;
    key: string;
  }): Promise<HeadResult> {
    const containerClient = this.blobServiceClient.getContainerClient(bucket);
    const blockBlobClient = containerClient.getBlockBlobClient(key);
    const response = await blockBlobClient.getProperties();
    return {
      size: response.contentLength ?? 0,
      contentType: response.contentType,
      contentEncoding: response.contentEncoding,
    };
  }

  async get({
    bucket,
    key,
    range,
  }: {
    bucket: string;
    key: string;
    range?: { offset: number; length: number };
  }): Promise<GetResult> {
    const containerClient = this.blobServiceClient.getContainerClient(bucket);
    const blockBlobClient = containerClient.getBlockBlobClient(key);
    const { offset, length } = range ?? {};
    const response = await blockBlobClient.download(offset, length);
    if (!response.readableStreamBody) {
      throw new InternalServerError("Failed to fetch blob");
    }
    return {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      stream: response.readableStreamBody as Readable,
      contentType: response.contentType,
      contentEncoding: response.contentEncoding,
    };
  }

  async signedGetUrl({
    bucket,
    key,
    expiresIn,
    contentType,
    contentDisposition,
  }: {
    bucket: string;
    key: string;
    expiresIn: number;
    contentType?: string;
    contentDisposition?: string;
  }): Promise<string> {
    const containerClient = this.blobServiceClient.getContainerClient(bucket);
    const blockBlobClient = containerClient.getBlockBlobClient(key);

    // Start in the past to be tolerant of clock skew.
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
    const expiryTime = new Date(Date.now() + expiresIn * 1000);

    if (this.isSharedKeyAuth) {
      const permissions: BlobGenerateSasUrlOptions = {
        startsOn: oneMinuteAgo,
        expiresOn: expiryTime,
        permissions: BlobSASPermissions.parse("r"), // Read
        contentType,
        contentDisposition,
      };
      return await blockBlobClient.generateSasUrl(permissions);
    } else {
      const userDelegationKey = await this.getUserDelegationKey(
        oneMinuteAgo,
        expiryTime,
      );
      const sasOptions = {
        containerName: bucket,
        blobName: key,
        permissions: BlobSASPermissions.parse("r"),
        startsOn: oneMinuteAgo,
        expiresOn: expiryTime,
        contentType,
        contentDisposition,
      };
      const sasToken = generateBlobSASQueryParameters(
        sasOptions,
        userDelegationKey,
        this.blobServiceClient.accountName,
      ).toString();
      return `${blockBlobClient.url}?${sasToken}`;
    }
  }

  async signedPutUrl({
    bucket,
    key,
    expiresIn,
    contentType,
  }: {
    bucket: string;
    key: string;
    expiresIn: number;
    contentType?: string;
    allowOverwrite?: boolean; // TODO
  }): Promise<string> {
    const containerClient = this.blobServiceClient.getContainerClient(bucket);
    const blockBlobClient = containerClient.getBlockBlobClient(key);

    // Start in the past to be tolerant of clock skew.
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
    const expiryTime = new Date(Date.now() + expiresIn * 1000);

    if (this.isSharedKeyAuth) {
      const permissions: BlobGenerateSasUrlOptions = {
        startsOn: oneMinuteAgo,
        expiresOn: expiryTime,
        permissions: BlobSASPermissions.parse("racwd"),
        contentType,
        contentDisposition: "attachment",
      };
      return await blockBlobClient.generateSasUrl(permissions);
    } else {
      const userDelegationKey = await this.getUserDelegationKey(
        oneMinuteAgo,
        expiryTime,
      );
      const sasOptions = {
        containerName: bucket,
        blobName: key,
        permissions: BlobSASPermissions.parse("racwd"),
        startsOn: oneMinuteAgo,
        expiresOn: expiryTime,
        contentType,
        contentDisposition: "attachment",
      };
      const sasToken = generateBlobSASQueryParameters(
        sasOptions,
        userDelegationKey,
        this.blobServiceClient.accountName,
      ).toString();
      return `${blockBlobClient.url}?${sasToken}`;
    }
  }

  private async getUserDelegationKey(
    startTime: Date,
    expiryTime: Date,
  ): Promise<UserDelegationKey> {
    try {
      return await this.blobServiceClient.getUserDelegationKey(
        startTime,
        expiryTime,
      );
    } catch (error) {
      throw new Error(
        `Failed to get user delegation key: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}
