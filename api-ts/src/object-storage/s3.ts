import {
  Create<PERSON>uck<PERSON><PERSON>ommand,
  HeadBucketCommand,
  S3Client,
  ServerSideEncryption,
} from "@aws-sdk/client-s3";
import { parseS3Url, S3Object } from "amazon-s3-url";
import {
  ATTACHMENT_BUCKET_NAME,
  BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
  CODE_BUNDLE_BUCKET_NAME,
  IS_LOCAL,
  RESPONSE_BUCKET_ACCESS_KEY_ID,
  RESPONSE_BUCKET_NAME,
  RESPONSE_BUCKET_REGION,
  RESPONSE_BUCKET_S3_ENDPOINT,
  RESPONSE_BUCKET_SECRET_ACCESS_KEY,
  RESPONSE_BUCKET_SESSION_TOKEN,
} from "../env";
import { getLogger } from "../instrumentation/logger";

export function parseExternalS3Url(url: string): S3Object | null {
  try {
    const s3Object = parseS3Url(url);
    if (!s3Object.bucket || !s3Object.key) {
      return null;
    }

    return s3Object;
  } catch (e) {
    getLogger().error(
      `Error parsing S3 URL: ${e instanceof Error ? e.message : String(e)}`,
    );
    return null;
  }
}

// TODO: If needed, allow this to be overridden by an env var
export function getBraintrustServerSideEncryption() {
  return IS_LOCAL ? undefined : ServerSideEncryption.AES256;
}

let s3Client: S3Client | null = null;
export async function getS3Client(): Promise<S3Client> {
  if (s3Client) {
    return s3Client;
  }
  s3Client = new S3Client({
    endpoint: RESPONSE_BUCKET_S3_ENDPOINT,
    region: RESPONSE_BUCKET_REGION,
    forcePathStyle: IS_LOCAL ? true : undefined,
    ...(RESPONSE_BUCKET_ACCESS_KEY_ID && RESPONSE_BUCKET_SECRET_ACCESS_KEY
      ? {
          credentials: {
            accessKeyId: RESPONSE_BUCKET_ACCESS_KEY_ID,
            secretAccessKey: RESPONSE_BUCKET_SECRET_ACCESS_KEY,
            sessionToken: RESPONSE_BUCKET_SESSION_TOKEN,
          },
        }
      : {}),
  });

  if (IS_LOCAL) {
    const promises: Promise<void>[] = [];
    const bucketNames = new Set([
      RESPONSE_BUCKET_NAME,
      CODE_BUNDLE_BUCKET_NAME,
      ATTACHMENT_BUCKET_NAME,
      BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
    ]);
    for (const bucketName of Array.from(bucketNames)) {
      if (!bucketName) {
        continue;
      }
      promises.push(createBucketIfNotExists(s3Client, bucketName));
    }
    await Promise.all(promises);
  }

  return s3Client;
}

async function createBucketIfNotExists(
  s3: S3Client,
  bucketName: string,
): Promise<void> {
  const pino = getLogger();
  try {
    // Try to get the bucket info to check if it exists
    const headCmd = new HeadBucketCommand({ Bucket: bucketName });
    await s3.send(headCmd);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  } catch (error: any) {
    if (error.name === "NotFound" || error.name === "NoSuchBucket") {
      // Bucket does not exist, create it
      pino.info({ bucketName }, `Bucket does not exist. Creating...`);
      const createCmd = new CreateBucketCommand({ Bucket: bucketName });
      await s3.send(createCmd);
      pino.info({ bucketName }, `Bucket created successfully.`);
    } else if (error.name === "BucketAlreadyOwnedByYou") {
      pino.info({ bucketName }, `Bucket was already created.`);
    } else {
      // Other errors, rethrow or handle accordingly
      throw error;
    }
  }
}
