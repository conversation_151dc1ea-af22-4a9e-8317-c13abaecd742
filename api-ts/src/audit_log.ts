import { z } from "zod";
import {
  AUDIT_SOURCE_FIELD,
  AUDIT_METADATA_FIELD,
  TRANSACTION_ID_FIELD,
  isObject,
  mergeDicts,
} from "braintrust/util";
import {
  AuditData,
  AuditMerge,
  AuditLogRow,
} from "@braintrust/local/api-schema";
import {
  SingleObjectType,
  OBJECT_ID_FIELDS,
  convertPathStorageLogical,
  getStorageToLogicalMap,
  noControlFieldsSchema,
} from "./schema";
import isEqual from "lodash.isequal";

export type PopAuditFieldsInput = {
  [AUDIT_SOURCE_FIELD]: string;
  [AUDIT_METADATA_FIELD]: Record<string, unknown>;
  user_id: string | null;
};

export type AuditFields = {
  source: string;
  metadata: Record<string, unknown>;
};

export type PoppedAuditFieldsOutput<T extends PopAuditFieldsInput> = {
  row: Omit<T, typeof AUDIT_SOURCE_FIELD | typeof AUDIT_METADATA_FIELD>;
  auditFields: AuditFields;
};

export function popAuditFields<T extends PopAuditFieldsInput>(
  row: Readonly<T>,
): PoppedAuditFieldsOutput<T> {
  const {
    [AUDIT_SOURCE_FIELD]: auditSource,
    [AUDIT_METADATA_FIELD]: rowAuditMetadata,
    ...rowRest
  } = row;

  const mergedMetadata = mergeDicts(
    row.user_id ? { user_id: row.user_id } : {},
    rowAuditMetadata,
  );
  return {
    row: rowRest,
    auditFields: {
      source: auditSource,
      metadata: mergedMetadata,
    },
  };
}

const MAX_AUDIT_VALUE_SIZE = 128;

export function computeAuditMergeData({
  objectType,
  oldRow,
  updateToMerge,
}: {
  objectType: SingleObjectType;
  oldRow: Readonly<Record<string, unknown>>;
  updateToMerge: Readonly<Record<string, unknown>>;
}): AuditMerge {
  noControlFieldsSchema.parse(oldRow);
  noControlFieldsSchema.parse(updateToMerge);

  // Find the deepest path that has changed.
  const changedPath = findChangedPath(oldRow, updateToMerge);

  // Then, find the object at that path.
  const [oldObject, newObject] = ((): [unknown, unknown] => {
    if (changedPath.length === 0) {
      return [oldRow, updateToMerge];
    } else {
      return [
        findObjectAtPath(oldRow, changedPath),
        findObjectAtPath(updateToMerge, changedPath),
      ];
    }
  })();

  const ret: AuditMerge = {
    action: "merge",
    path: convertPathStorageLogical(
      getStorageToLogicalMap(objectType),
      changedPath,
    ),
  };

  if (
    JSON.stringify(oldObject).length <= MAX_AUDIT_VALUE_SIZE &&
    JSON.stringify(newObject).length <= MAX_AUDIT_VALUE_SIZE
  ) {
    ret.from = oldObject;
    ret.to = newObject;
  }
  return ret;
}

const _nullishStringValue = z.string().nullish();
type ZodNullishStringType = typeof _nullishStringValue;
type ObjectIdFieldsT = (typeof OBJECT_ID_FIELDS)[number];

export const auditLogObjectIdsSchema = z.object({
  id: z.string(),
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  ...(Object.fromEntries(
    OBJECT_ID_FIELDS.map((x) => [x, z.string().nullish()]),
  ) as { [X in ObjectIdFieldsT]: ZodNullishStringType }),
});

export type AuditLogObjectIds = z.infer<typeof auditLogObjectIdsSchema>;

export type MakeAuditLogEntryInput = AuditFields & {
  audit_data: AuditData | null;
};

export type MakeAuditLogEntryOutput = Omit<AuditLogRow, "source">;

// DEVNOTE: This should be kept in sync with the definition of AuditLogRow in
// app/utils/schema.tsx and the jsonb_build_object(..) in
// api-ts/src/object_scan.ts.
export function makeAuditLogEntry({
  objectIds,
  xactId,
  created,
  auditLogEntryInput,
}: {
  objectIds: AuditLogObjectIds;
  xactId: string;
  created: string;
  auditLogEntryInput: MakeAuditLogEntryInput;
}): MakeAuditLogEntryOutput {
  return {
    id: `${objectIds.id}_${xactId}`,
    origin: { id: objectIds.id, [TRANSACTION_ID_FIELD]: xactId },
    created,
    [TRANSACTION_ID_FIELD]: xactId,
    ...auditLogEntryInput,
    ...Object.fromEntries(
      OBJECT_ID_FIELDS.map((f) => {
        const ret: [
          (typeof OBJECT_ID_FIELDS)[number],
          (typeof objectIds)["org_id"],
        ] = [f, objectIds[f]];
        return ret;
      }),
    ),
  };
}

export function findChangedPath(
  oldRow: Readonly<Record<string, unknown>>,
  update: Readonly<Record<string, unknown>>,
): string[] {
  let deepestPath: string[] = [];

  function compareObjects(
    base: Record<string, unknown>,
    change: Record<string, unknown>,
    currentPath: string[] = [],
  ): void {
    const changesetKeys = Object.keys(change);
    for (const key of changesetKeys) {
      const val1 = base[key];
      const val2 = change[key];

      if (isObject(val1) && isObject(val2)) {
        compareObjects(val1, val2, [...currentPath, key]);
      } else if (!isEqual(val1, val2)) {
        const newPath = [...currentPath, key];
        if (newPath.length > deepestPath.length) {
          deepestPath = newPath;
        }
      }
    }
  }

  compareObjects(oldRow, update);
  return deepestPath;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function findObjectAtPath(x: any, path: string[]): unknown {
  try {
    for (const k of path) {
      x = x[k];
    }
    return x ?? null;
  } catch {
    return null;
  }
}
