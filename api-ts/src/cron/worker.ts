import { getPG } from "../db/pg";
import { getLogger } from "../instrumentation/logger";
import { z } from "zod";
import { decryptServiceToken, runCronJob } from "./cron";
import { AUTOMATION_CACHE } from "../automations";
import { ALLOWED_ORIGIN, BRAINTRUST_APP_URL } from "../env";
import { OBJECT_CACHE } from "../object_cache";
import { CronLockAcquisitionError, extractErrorText } from "../util";

// No need to run the cron worker any more frequenlty than once per minute
export const SLEEP_INTERVAL_S = 60;
export function startAutomationCronLoop(): void {
  const appOrigin = BRAINTRUST_APP_URL ?? ALLOWED_ORIGIN;
  if (!appOrigin) {
    throw new Error("Missing appOrigin");
  }
  (async () => {
    getLogger().info("Starting Automation Cron loop");
    while (true) {
      const startTime = new Date();
      try {
        await runAutomationCronLoop({
          appOrigin,
          timeoutS: SLEEP_INTERVAL_S,
        });
      } catch (err) {
        getLogger().error({ error: err }, "Error in Automation Cron loop");
      }
      const endTime = new Date();
      const elapsedS = (endTime.getTime() - startTime.getTime()) / 1000;
      const sleepS = SLEEP_INTERVAL_S - elapsedS;
      if (sleepS > 0) {
        await new Promise((resolve) => setTimeout(resolve, sleepS * 1000));
      }
    }
  })().catch((err) => {
    getLogger().error(
      { error: err },
      "Fatal error in Automation Cron loop. The loop will exit and no cron jobs will be run.",
    );
  });
}

let currentSleepResolver: (() => void) | null = null;
export function forceCronLoopRun() {
  if (currentSleepResolver) {
    currentSleepResolver();
    currentSleepResolver = null;
  }
}

const automationIdRowsSchema = z.array(
  z.object({
    automation_id: z.string(),
    service_token_encrypted: z.string(),
  }),
);

export async function runAutomationCronLoop({
  appOrigin,
  timeoutS,
}: {
  appOrigin: string;
  timeoutS: number;
}) {
  getLogger().debug("Running Automation Cron iteration");

  while (true) {
    const startTime = Date.now();
    const db = getPG();
    const { rows } = await db.query(`
        SELECT automation_id, service_token_encrypted FROM automation_cron_jobs WHERE next_execution <= NOW()
        ORDER BY next_execution ASC
        LIMIT 100
    `);
    const automationIdRows = automationIdRowsSchema.parse(rows);
    const executions = [];
    for (const row of automationIdRows) {
      executions.push(
        (async () => {
          try {
            await executeAutomationCronJob({
              appOrigin,
              automationId: row.automation_id,
              serviceTokenEncrypted: row.service_token_encrypted,
            });
          } catch (err) {
            if (err instanceof CronLockAcquisitionError) {
              getLogger().debug(
                { id: row.automation_id },
                `Failed to acquire cron lock (someone else must be working on it) [id=${row.automation_id}]`,
              );
              return;
            }
            getLogger().error(
              { error: extractErrorText(err), id: row.automation_id },
              `Error in Automation Cron loop [id=${row.automation_id}]: ${extractErrorText(err)}`,
            );
          }
        })(),
      );
    }
    await Promise.all(executions);

    const elapsedS = (Date.now() - startTime) / 1000;
    if (elapsedS > timeoutS) {
      getLogger().debug(
        {
          timeoutS,
          elapsedS,
        },
        "Automation Cron loop timed out.",
      );
    } else {
      await new Promise<void>((resolve) => {
        currentSleepResolver = resolve;
        setTimeout(
          () => {
            if (currentSleepResolver === resolve) {
              currentSleepResolver = null;
              resolve();
            }
          },
          (timeoutS - elapsedS) * 1000,
        );
      });
    }
  }
}

async function executeAutomationCronJob({
  appOrigin,
  automationId,
  serviceTokenEncrypted,
}: {
  appOrigin: string;
  automationId: string;
  serviceTokenEncrypted: string;
}) {
  const serviceToken = await decryptServiceToken(serviceTokenEncrypted);
  const automation = await AUTOMATION_CACHE.getAutomationById({
    appOrigin,
    authToken: serviceToken,
    automationId,
  });
  const pino = getLogger();
  if (!automation) {
    pino.warn(
      {
        automationId,
      },
      `Skipping automation ${automationId} because it was not found. This may be because it no longer exists or the service token is invalid`,
    );
    return;
  }

  const projectInfo = await OBJECT_CACHE.checkAndGet({
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: automation.project_id,
    appOrigin,
    authToken: serviceToken,
  });
  if (!projectInfo) {
    pino.warn(
      {
        automationId,
        projectId: automation.project_id,
      },
      `Skipping automation ${automationId} because its project metadata is not accessible`,
    );
    return;
  }

  const orgId = projectInfo.parent_cols.get("organization")?.id;
  if (!orgId) {
    pino.warn(
      {
        automationId,
        projectId: automation.project_id,
      },
      `Skipping automation ${automationId} because its project has no organization ID`,
    );
    return;
  }

  const result = await runCronJob({
    orgId,
    automation,
    appOrigin,
    tryAcquire: true,
  });

  pino.debug(
    {
      automationId,
      result,
    },
    `Automation ${automationId} completed`,
  );
}
