// This has to be done before the API is initialized
import "../../instrumentation/setup_dd";

import {
  lambdaRequestTracker,
  pinoLambdaDestination,
  StructuredLogFormatter,
} from "pino-lambda";
import { getLogger, initPinoLogger } from "../../instrumentation/logger";

initPinoLogger(
  pinoLambdaDestination({
    formatter: new StructuredLogFormatter(),
  }),
);
const withPinoRequest = lambdaRequestTracker();

import type { EventBridgeHandler } from "aws-lambda";
import { runAutomationCronLoop, SLEEP_INTERVAL_S } from "../worker";
import { initializeEnv } from "../../env";

initializeEnv(false);

import { ALLOWED_ORIGIN, BRAINTRUST_APP_URL } from "../../env";

// We want to run this lambda for as long as possible, since these timeouts are used to inform
// how long the loops try to backfill.
const TIMEOUT_S = SLEEP_INTERVAL_S - 5;

export const handler: <PERSON><PERSON>ridge<PERSON><PERSON><PERSON><
  "Automation Cron",
  "Automation Cron",
  unknown
> = async (event, context) => {
  // We set this in every lambda so that the response is sent immediately (not after
  // the event loop is empty).
  context.callbackWaitsForEmptyEventLoop = false;

  withPinoRequest(event, context);

  const appOrigin = BRAINTRUST_APP_URL ?? ALLOWED_ORIGIN;
  if (!appOrigin) {
    throw new Error("Missing appOrigin");
  }

  await runAutomationCronLoop({
    appOrigin,
    timeoutS: TIMEOUT_S,
  });
  getLogger().info("Automation Cron loop completed");
  return { success: true };
};
