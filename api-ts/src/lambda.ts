import { <PERSON><PERSON> } from "@aws-sdk/client-lambda";
import { AssumeRoleCommand, STS, STSClient } from "@aws-sdk/client-sts";
import {
  API_HANDLER_ROLE,
  QUARANTINE_REGION,
  QUARANTINE_USER_ACCESS_KEY,
  QUARANTINE_USER_SECRET_ACCESS_KEY,
} from "./env";

let lambdaClient: Lambda | null = null;
export async function getLambdaClient(): Promise<Lambda> {
  if (lambdaClient) {
    return lambdaClient;
  }

  if (QUARANTINE_USER_ACCESS_KEY && QUARANTINE_USER_SECRET_ACCESS_KEY) {
    const sts = new STSClient({
      credentials: {
        accessKeyId: QUARANTINE_USER_ACCESS_KEY,
        secretAccessKey: QUARANTINE_USER_SECRET_ACCESS_KEY,
      },
      region: QUARANTINE_REGION,
    });

    const assumeRoleCommand = new AssumeRoleCommand({
      RoleArn: API_HANDLER_ROLE,
      RoleSessionName: "APIHandlerSession",
    });

    const assumeRoleResponse = await sts.send(assumeRoleCommand);
    if (!assumeRoleResponse.Credentials) {
      throw new Error("Failed to assume role");
    }
    if (
      !assumeRoleResponse.Credentials.AccessKeyId ||
      !assumeRoleResponse.Credentials.SecretAccessKey ||
      !assumeRoleResponse.Credentials.SessionToken
    ) {
      throw new Error("Failed to assume role (no credentials)");
    }
    lambdaClient = new Lambda({
      credentials: {
        accessKeyId: assumeRoleResponse.Credentials.AccessKeyId,
        secretAccessKey: assumeRoleResponse.Credentials.SecretAccessKey,
        sessionToken: assumeRoleResponse.Credentials.SessionToken,
      },
      region: QUARANTINE_REGION,
    });
  } else {
    lambdaClient = new Lambda();
  }
  return lambdaClient;
}

let stsClient: STS | null = null;
export async function getStsClient(): Promise<STS> {
  if (stsClient) {
    return stsClient;
  }
  if (QUARANTINE_USER_ACCESS_KEY && QUARANTINE_USER_SECRET_ACCESS_KEY) {
    const sts = new STSClient({
      credentials: {
        accessKeyId: QUARANTINE_USER_ACCESS_KEY,
        secretAccessKey: QUARANTINE_USER_SECRET_ACCESS_KEY,
      },
      region: QUARANTINE_REGION,
    });

    const assumeRoleCommand = new AssumeRoleCommand({
      RoleArn: API_HANDLER_ROLE,
      RoleSessionName: "APIHandlerSession",
    });

    const assumeRoleResponse = await sts.send(assumeRoleCommand);
    if (!assumeRoleResponse.Credentials) {
      throw new Error("Failed to assume role");
    }
    if (
      !assumeRoleResponse.Credentials.AccessKeyId ||
      !assumeRoleResponse.Credentials.SecretAccessKey ||
      !assumeRoleResponse.Credentials.SessionToken
    ) {
      throw new Error("Failed to assume role (no credentials)");
    }
    stsClient = new STS({
      credentials: {
        accessKeyId: assumeRoleResponse.Credentials.AccessKeyId,
        secretAccessKey: assumeRoleResponse.Credentials.SecretAccessKey,
        sessionToken: assumeRoleResponse.Credentials.SessionToken,
      },
      region: QUARANTINE_REGION,
    });
  } else {
    stsClient = new STS();
  }
  return stsClient;
}
