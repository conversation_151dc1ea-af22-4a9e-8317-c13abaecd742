import { expect, test } from "vitest";
import { OBJECT_DELETE_FIELD, PARENT_ID_FIELD } from "braintrust/util";
import {
  MaybeResolvableRowBase,
  IdToSpanParentMap,
  SpanParentLive,
  SpanParentDeleted,
  makeIdToSpanParentKey,
  makeObjectIdKey,
  resolveSpanParents,
} from "./span_parent";
import {
  OBJECT_TYPE_FIELD,
  ObjectIdsUnion,
} from "@braintrust/local/api-schema";

type MaybeResolvableRowBaseExtended = MaybeResolvableRowBase & { tag: string };

test("resolveSpanParents basic", () => {
  // We construct the following scenario:
  //  1. A deleted row and a live row in idToSpanParent
  //  2. Rows that directly reference the idToSpanParent entries
  //  3. Rows that reference the rows from step 2
  //  4. Rows that reference the rows from step 3
  //  5. Rows that cannot be resolved
  //  6. Rows that are already resolved

  const rowObjectIdsBase: ObjectIdsUnion[] = [
    { experiment_id: "foo", [OBJECT_TYPE_FIELD]: "experiment" },
    { dataset_id: "foo", [OBJECT_TYPE_FIELD]: "dataset" },
  ];

  const rowObjectIdKeysBase = rowObjectIdsBase.map(makeObjectIdKey);

  function makeIdToSpanParentEntry(
    id: string,
    objectIdKey: string,
    rest:
      | Omit<SpanParentLive, "id" | "objectIdKey">
      | Omit<SpanParentDeleted, "id" | "objectIdKey">,
  ): [string, SpanParentLive | SpanParentDeleted] {
    const key = makeIdToSpanParentKey({ id, objectIdKey });
    return [key, { id, objectIdKey, ...rest }];
  }

  const idToSpanParent: IdToSpanParentMap = new Map([
    makeIdToSpanParentEntry("row0", rowObjectIdKeysBase[0], {
      kind: "deleted",
    }),
    makeIdToSpanParentEntry("row1", rowObjectIdKeysBase[0], {
      kind: "live",
      span_id: "span1",
      root_span_id: "root_span1",
    }),
  ]);

  const rows: {
    controlFields: MaybeResolvableRowBaseExtended;
    objectIds: ObjectIdsUnion;
  }[] = [
    // Item 6.
    {
      controlFields: {
        id: "row2",
        span_id: "span2",
        root_span_id: "root_span2",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: true,
        [PARENT_ID_FIELD]: null,
        tag: "tag2",
      },
      objectIds: rowObjectIdsBase[0],
    },
    {
      controlFields: {
        id: "row3",
        span_id: "span3",
        root_span_id: "root_span3",
        span_parents: ["span_parent3"],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: null,
        tag: "tag3",
      },
      objectIds: rowObjectIdsBase[0],
    },
    // Item 4.
    {
      controlFields: {
        id: "row8",
        span_id: "span8",
        root_span_id: "root_span8",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: "row6",
        tag: "tag8",
      },
      objectIds: rowObjectIdsBase[0],
    },
    {
      controlFields: {
        id: "row9",
        span_id: "span9",
        root_span_id: "root_span9",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: "row7",
        tag: "tag9",
      },
      objectIds: rowObjectIdsBase[0],
    },
    // Item 5.
    //
    // This one cannot be resolved because it belongs to the dataset rather
    // than the experiment.
    {
      controlFields: {
        id: "row10",
        span_id: "span10",
        root_span_id: "root_span10",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: true,
        [PARENT_ID_FIELD]: "row0",
        tag: "tag10",
      },
      objectIds: rowObjectIdsBase[1],
    },
    // This one points to a row id that is not present.
    {
      controlFields: {
        id: "row11",
        span_id: "span11",
        root_span_id: "root_span11",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: "row12",
        tag: "tag11",
      },
      objectIds: rowObjectIdsBase[0],
    },
    // Item 2.
    {
      controlFields: {
        id: "row4",
        span_id: "span4",
        root_span_id: "root_span4",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: true,
        [PARENT_ID_FIELD]: "row0",
        tag: "tag4",
      },
      objectIds: rowObjectIdsBase[0],
    },
    {
      controlFields: {
        id: "row5",
        span_id: "span5",
        root_span_id: "root_span5",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: "row1",
        tag: "tag5",
      },
      objectIds: rowObjectIdsBase[0],
    },
    // Item 3.
    {
      controlFields: {
        id: "row6",
        span_id: "span6",
        root_span_id: "root_span6",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: "row4",
        tag: "tag6",
      },
      objectIds: rowObjectIdsBase[0],
    },
    {
      controlFields: {
        id: "row7",
        span_id: "span7",
        root_span_id: "root_span7",
        span_parents: [],
        [OBJECT_DELETE_FIELD]: false,
        [PARENT_ID_FIELD]: "row5",
        tag: "tag7",
      },
      objectIds: rowObjectIdsBase[0],
    },
  ];

  const invalidParentIdRows = new Map<string, string>();
  function addInvalidParentIdToRow(rowId: number, parentId: string) {
    invalidParentIdRows.set(rows[rowId].controlFields.id, parentId);
  }

  resolveSpanParents({
    rows,
    idToSpanParent,
    addInvalidParentIdToRow,
  });

  const expectedRows: MaybeResolvableRowBaseExtended[] = [
    // Item 6.
    {
      id: "row2",
      span_id: "span2",
      root_span_id: "root_span2",
      span_parents: [],
      [OBJECT_DELETE_FIELD]: true,
      [PARENT_ID_FIELD]: null,
      tag: "tag2",
    },
    {
      id: "row3",
      span_id: "span3",
      root_span_id: "root_span3",
      span_parents: ["span_parent3"],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: null,
      tag: "tag3",
    },
    // Item 4.
    {
      id: "row8",
      span_id: "span8",
      root_span_id: "span6",
      span_parents: ["span6"],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: null,
      tag: "tag8",
    },
    {
      id: "row9",
      span_id: "span9",
      root_span_id: "root_span1",
      span_parents: ["span7"],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: null,
      tag: "tag9",
    },
    // Item 5.
    {
      id: "row10",
      span_id: "span10",
      root_span_id: "root_span10",
      span_parents: [],
      [OBJECT_DELETE_FIELD]: true,
      [PARENT_ID_FIELD]: "row0",
      tag: "tag10",
    },
    {
      id: "row11",
      span_id: "span11",
      root_span_id: "root_span11",
      span_parents: [],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: "row12",
      tag: "tag11",
    },
    // Item 2.
    {
      id: "row4",
      span_id: "span4",
      root_span_id: "span4",
      span_parents: [],
      [OBJECT_DELETE_FIELD]: true,
      [PARENT_ID_FIELD]: null,
      tag: "tag4",
    },
    {
      id: "row5",
      span_id: "span5",
      root_span_id: "root_span1",
      span_parents: ["span1"],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: null,
      tag: "tag5",
    },
    // Item 3.
    {
      id: "row6",
      span_id: "span6",
      root_span_id: "span6",
      span_parents: [],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: null,
      tag: "tag6",
    },
    {
      id: "row7",
      span_id: "span7",
      root_span_id: "root_span1",
      span_parents: ["span5"],
      [OBJECT_DELETE_FIELD]: false,
      [PARENT_ID_FIELD]: null,
      tag: "tag7",
    },
  ];
  expect(rows.map(({ controlFields }) => controlFields)).toEqual(expectedRows);

  const expectedInvalidParentIdRows = new Map<string, string>([
    ["row4", "row0"],
    ["row6", "row4"],
  ]);
  expect(Object.fromEntries([...invalidParentIdRows.entries()])).toEqual(
    Object.fromEntries([...expectedInvalidParentIdRows.entries()]),
  );

  expect(Object.fromEntries([...idToSpanParent.entries()])).toEqual(
    Object.fromEntries([
      makeIdToSpanParentEntry("row0", rowObjectIdKeysBase[0], {
        kind: "deleted",
      }),
      makeIdToSpanParentEntry("row1", rowObjectIdKeysBase[0], {
        kind: "live",
        span_id: "span1",
        root_span_id: "root_span1",
      }),
      makeIdToSpanParentEntry("row4", rowObjectIdKeysBase[0], {
        kind: "deleted",
      }),
      makeIdToSpanParentEntry("row5", rowObjectIdKeysBase[0], {
        kind: "live",
        span_id: "span5",
        root_span_id: "root_span1",
      }),
      makeIdToSpanParentEntry("row6", rowObjectIdKeysBase[0], {
        kind: "live",
        span_id: "span6",
        root_span_id: "span6",
      }),
      makeIdToSpanParentEntry("row7", rowObjectIdKeysBase[0], {
        kind: "live",
        span_id: "span7",
        root_span_id: "root_span1",
      }),
      makeIdToSpanParentEntry("row8", rowObjectIdKeysBase[0], {
        kind: "live",
        span_id: "span8",
        root_span_id: "span6",
      }),
      makeIdToSpanParentEntry("row9", rowObjectIdKeysBase[0], {
        kind: "live",
        span_id: "span9",
        root_span_id: "root_span1",
      }),
    ]),
  );
});
