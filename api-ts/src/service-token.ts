import { Request, Response } from "express";
import { IncomingHttpHeaders } from "http";
import { z } from "zod";
import {
  AccessDeniedError,
  ForbiddenError,
  InternalServerError,
  NotFoundError,
  wrapZodError,
  postDefaultHeaders,
} from "./util";
import { RequestContext, getRequestContext } from "./request_context";
import { checkTokenAuthorized } from "./token_auth";
import { getPG } from "./db/pg";
import { encryptMessage } from "@braintrust/proxy/utils";
import { getServiceTokenSecretKey, decryptServiceToken } from "./cron/cron";
import {
  ORG_NAME,
  PRIMARY_ORG_NAME,
  TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE,
  BRAINTRUST_HOSTED_DATA_PLANE,
  DISABLE_SYSADMIN_TELEMETRY,
} from "./env";
import { customFetchRequest } from "./custom_fetch";

const BT_TEST_AS_SELF_HOSTED_DATA_PLANE_HEADER =
  "x-bt-test_as_self_hosted_data_plane";
const BT_TEST_WITH_PRIMARY_ORG_NAME_HEADER = "x-bt-test_with_primary_org_name";

const upsertServiceTokenRequest = z.object({
  name: z.string(),
  service_token: z.string(),
});

const checkServiceTokenPermission = async ({
  ctx,
  headers,
}: {
  ctx: RequestContext;
  headers?: IncomingHttpHeaders;
}) => {
  const testAsSelfHostedDataPlane =
    TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE &&
    headers?.[BT_TEST_AS_SELF_HOSTED_DATA_PLANE_HEADER] === "true";
  const testPrimaryOrgName = !Array.isArray(
    headers?.[BT_TEST_WITH_PRIMARY_ORG_NAME_HEADER],
  )
    ? headers?.[BT_TEST_WITH_PRIMARY_ORG_NAME_HEADER]
    : undefined;

  const selfHosted = !BRAINTRUST_HOSTED_DATA_PLANE || testAsSelfHostedDataPlane;
  const { me } = await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
    checkSysadmin: !selfHosted,
  });

  const meResult = await me;

  if (!selfHosted) {
    if (!meResult.is_sysadmin || DISABLE_SYSADMIN_TELEMETRY) {
      throw new AccessDeniedError({
        permission: "sysadmin",
        objectType: "service-token",
        objectId: "status",
      });
    }

    return;
  }

  // For self-hosted data planes we need to check that the requester is an owner
  // of the "primary" org.
  //
  // If ORG_NAME=*, check the PRIMARY_ORG_NAME env variable which defines the org
  // whose owners have authoritative access to these endpoints.
  let primaryOrgName = ORG_NAME !== "*" ? ORG_NAME : PRIMARY_ORG_NAME;
  if (testAsSelfHostedDataPlane && testPrimaryOrgName) {
    primaryOrgName = testPrimaryOrgName;
  }
  if (!primaryOrgName) {
    throw new InternalServerError(
      "Could not resolve primary org name for this data plane",
    );
  }
  const org = meResult.organizations.find((o) => o.name === primaryOrgName);
  if (!org) {
    throw new ForbiddenError(
      `Requesting user is not a member of primary org ${primaryOrgName}`,
    );
  }

  const resp = await customFetchRequest(
    `${ctx.appOrigin}/api/actions/isOrganizationOwner`,
    {
      method: "POST",
      headers: postDefaultHeaders({ token: ctx.token }),
      body: JSON.stringify({
        function_args: {
          org_id: org.id,
        },
      }),
    },
  );
  if (!resp.ok) {
    throw new InternalServerError(await resp.body.text());
  }
  const isOwner = z.boolean().parse(await resp.body.json());
  if (!isOwner) {
    throw new ForbiddenError(`Requires ownership of org ${primaryOrgName}`);
  }
};

export async function upsertServiceToken(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const params = wrapZodError(() => upsertServiceTokenRequest.parse(ctx.data));
  await checkServiceTokenPermission({
    ctx,
    headers: req.headers,
  });

  await performUpsertServiceToken({
    name: params.name,
    serviceToken: params.service_token,
  });

  res.json({ success: true });
}

const SERVICE_TOKENS_TABLE_NAME = "service_tokens";

// NOTE: This function assumes you have already done ACL checks.
async function performUpsertServiceToken({
  name,
  serviceToken,
}: {
  name: string;
  serviceToken: string;
}) {
  const db = getPG();
  const serviceTokenEncrypted = await encryptMessage(
    await getServiceTokenSecretKey(),
    serviceToken,
  );
  await db.query(
    `
      insert into ${SERVICE_TOKENS_TABLE_NAME} (name, service_token_encrypted)
      values ($1::text, $2::text)
      on conflict (name)
      do update set service_token_encrypted = excluded.service_token_encrypted
    `,
    [name, serviceTokenEncrypted],
  );
}

async function getServiceToken(name: string): Promise<string | undefined> {
  const db = getPG();
  const { rows } = await db.query(
    `SELECT service_token_encrypted FROM ${SERVICE_TOKENS_TABLE_NAME} WHERE name = $1`,
    [name],
  );

  if (rows.length === 0) {
    return undefined;
  }

  if (rows.length > 1) {
    throw new InternalServerError(
      `Multiple service tokens found with name: ${name}`,
    );
  }

  const encryptedServiceToken = z
    .object({
      service_token_encrypted: z.string().nullish(),
    })
    .parse(rows[0]).service_token_encrypted;
  if (!encryptedServiceToken) {
    return undefined;
  }

  return await decryptServiceToken(encryptedServiceToken);
}

export async function headServiceTokenRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const { name } = req.params;
  await checkServiceTokenPermission({ ctx, headers: req.headers });

  const serviceToken = await getServiceToken(name);
  if (!serviceToken) {
    throw new NotFoundError(`Service token not found: ${name}`);
  }

  res.status(200).end();
}

export async function getServiceTokenRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const { name } = req.params;
  await checkServiceTokenPermission({ ctx, headers: req.headers });

  const serviceToken = await getServiceToken(name);
  if (!serviceToken) {
    throw new NotFoundError(`Service token not found: ${name}`);
  }

  res.json({ service_token: serviceToken });
}
