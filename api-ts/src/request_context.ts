import { Request } from "express";

type OtelContentType = "application/json" | "application/x-protobuf";

export interface RequestContext {
  appOrigin: string;
  token?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any;
  api_version?: number;
  otelContentType?: OtelContentType;
}

export function getRequestContext(req: Request): RequestContext {
  if (!req.ctx) {
    throw new Error("Request context not found");
  }
  return req.ctx;
}

declare module "express" {
  interface Request {
    ctx?: RequestContext;
  }
}
