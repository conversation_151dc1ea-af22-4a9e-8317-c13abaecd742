import { z } from "zod";
import { BaseEventSchema } from "./types";

export const FunctionInvokedEventSchema = BaseEventSchema(
  z.literal("FunctionInvokedEvent"),
  z.object({
    org_id: z.string(),
    count: z.number().nullish(),
  }),
);

export type FunctionInvokedEvent = z.infer<typeof FunctionInvokedEventSchema>;

export const LogInsertedEventSchema = BaseEventSchema(
  z.literal("LogInsertedEvent"),
  z.object({
    org_id: z.string(),
    count: z.number().nullish(),
    log_bytes: z.number(),
    metrics_count: z.number(),
    scores_count: z.number(),
    type: z
      .string()
      .nullish()
      .describe("helpful flag to mark which event this is"),
  }),
);

export type LogInsertedEvent = z.infer<typeof LogInsertedEventSchema>;
