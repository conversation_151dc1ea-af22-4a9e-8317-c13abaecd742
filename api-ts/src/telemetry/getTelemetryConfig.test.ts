import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  expect,
  test,
  vi,
} from "vitest";
import { useTelemetryCache } from "./cache";
import { getTelemetryConfigs } from "./getTelemetryConfig";
import {
  MockAgent,
  setGlobalDispatcher,
  get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tcher,
  <PERSON><PERSON><PERSON><PERSON>,
  Mock<PERSON><PERSON>,
} from "undici";
import { createMockRedisClient } from "../redis.test";
import { initializeGlobalDispatcher } from "../custom_fetch";
import { logger } from "./logger";

vi.mock("../env", async (importOriginal) => {
  const actual = await importOriginal<typeof import("../env")>();
  return {
    ...actual,
    ALLOWED_ORIGIN: "http://localhost:3000",
    OUTBOUND_RATE_LIMIT_WINDOW_MINUTES: 1,
    OUTBOUND_RATE_LIMIT_MAX_REQUESTS: 5,
    TELEMETRY_URL: undefined,
    TELEMETRY_TOKEN: undefined,
  };
});

let mockAgent: MockAgent;
let originalDispatcher: Dispatcher;
let mockPool: MockPool;

const mockRedis = createMockRedisClient();

vi.mock("../redis", () => ({
  getRedis: () => Promise.resolve(mockRedis.client),
}));

beforeAll(async () => {
  originalDispatcher = getGlobalDispatcher();
});

beforeEach(async () => {
  vi.useFakeTimers();
  mockAgent = new MockAgent();
  mockAgent.disableNetConnect();

  mockPool = mockAgent.get("http://localhost:3000");
  initializeGlobalDispatcher({ agent: mockAgent, force: true });

  mockRedis.store.clear();
  vi.mocked(await import("../env")).TELEMETRY_URL = undefined;
  vi.mocked(await import("../env")).TELEMETRY_TOKEN = undefined;
});

afterEach(async () => {
  await mockAgent.close();
  vi.clearAllMocks();
  vi.useRealTimers();
});

afterAll(() => {
  setGlobalDispatcher(originalDispatcher);
});

test("fetches telemetry configs from API", async () => {
  const orgIds = [
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
  ];
  const mockConfigs = {
    [orgIds[0]]: { url: "http://telemetry1.com", secret: "secret1" },
    [orgIds[1]]: { url: "http://telemetry2.com", secret: "secret2" },
  };

  mockPool
    .intercept({
      path: "/api/telemetry/configs",
      method: "POST",
      body: JSON.stringify({ org_ids: orgIds }),
      headers: {
        // note test would fail if the Authorization header is incorrect
        // in this case the ":" is very important!
        Authorization: "Bearer: test-token",
        "Content-Type": "application/json",
      },
    })
    .reply(200, { configs: mockConfigs });

  const result = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  expect(result).toEqual({
    [orgIds[0]]: { ...mockConfigs[orgIds[0]], source: "api" },
    [orgIds[1]]: { ...mockConfigs[orgIds[1]], source: "api" },
  });
});

test("uses cached configs when available", async () => {
  const orgIds = [
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
  ];
  const cachedConfigs = {
    [orgIds[0]]: { url: "http://telemetry1.com", secret: "secret1" },
  };

  // Pre-populate cache
  const cache = useTelemetryCache();
  await cache.setTelemetryConfigs(cachedConfigs);

  const mockApiConfigs = {
    [orgIds[1]]: { url: "http://telemetry2.com", secret: "secret2" },
  };

  mockPool
    .intercept({
      path: "/api/telemetry/configs",
      method: "POST",
      body: JSON.stringify({ org_ids: [orgIds[1]] }),
    })
    .reply(200, { configs: mockApiConfigs });

  const result = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  expect(result).toEqual({
    [orgIds[0]]: { ...cachedConfigs[orgIds[0]], source: "cache" },
    [orgIds[1]]: { ...mockApiConfigs[orgIds[1]], source: "api" },
  });
});

test("uses environment overrides when present", async () => {
  vi.mocked(await import("../env")).TELEMETRY_URL = "http://override.com";
  vi.mocked(await import("../env")).TELEMETRY_TOKEN = "override-token";

  const orgIds = [
    "123e4567-e89b-12d3-a456-426614174000",
    "123e4567-e89b-12d3-a456-426614174001",
  ];

  const result = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  // Should use environment overrides for all orgs
  expect(result).toEqual({
    [orgIds[0]]: {
      url: "http://override.com",
      secret: "override-token",
      source: "environment",
    },
    [orgIds[1]]: {
      url: "http://override.com",
      secret: "override-token",
      source: "environment",
    },
  });
});

test("uses environment overrides when present and defaults to user token", async () => {
  vi.mocked(await import("../env")).TELEMETRY_URL = "http://override.com";
  vi.mocked(await import("../env")).TELEMETRY_TOKEN = "";

  const orgIds = [
    "123e4567-e89b-12d3-a456-426614174000",
    "123e4567-e89b-12d3-a456-426614174001",
  ];

  const result = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  // Should use environment overrides for all orgs
  expect(result).toEqual({
    [orgIds[0]]: {
      url: "http://override.com",
      secret: "test-token",
      source: "environment",
    },
    [orgIds[1]]: {
      url: "http://override.com",
      secret: "test-token",
      source: "environment",
    },
  });
});

test("handles upstream API 500 error gracefully", async () => {
  const warnSpy = vi.spyOn(logger, "warn");

  const orgIds = [
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
  ];

  let requestCount = 0;

  mockPool
    .intercept({
      path: "/api/telemetry/configs",
      method: "POST",
      body: JSON.stringify({ org_ids: orgIds }),
    })
    .reply(() => {
      requestCount++;
      if (requestCount <= 2) {
        return {
          statusCode: 500,
          data: JSON.stringify({ error: "Internal Server Error" }),
          responseOptions: {
            headers: {
              // just in case!
              "Retry-After": "0.001",
            },
          },
        };
      }
      // technically we'll never get here, but let's be safe
      return {
        statusCode: 200,
        data: JSON.stringify({
          configs: {
            [orgIds[0]]: { url: "http://telemetry1.com", secret: "secret1" },
          },
        }),
      };
    })
    .persist();

  const resultPromise = getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  const result = await resultPromise;

  await vi.runAllTimersAsync();

  // POST requests are not retried (default config of RetryAgent)
  expect(requestCount).toBe(1);

  expect(result).toEqual({
    [orgIds[0]]: { url: "", secret: "", source: "missing" },
    [orgIds[1]]: { url: "", secret: "", source: "missing" },
  });

  // a warn is acceptable since we can assume most customers will have this log disabled
  expect(warnSpy).toHaveBeenCalledWith("Error when requesting url", {
    error: expect.any(Object),
    url: new URL("http://localhost:3000/api/telemetry/configs"),
  });
});

test("caches empty configs from failed API responses to avoid lookups", async () => {
  const orgId = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11";
  const cache = useTelemetryCache();

  const initialCache = await cache.getTelemetryConfigs([orgId]);
  expect(initialCache).toEqual({});

  let requestCount = 0;

  mockPool
    .intercept({
      path: "/api/telemetry/configs",
      method: "POST",
      body: JSON.stringify({ org_ids: [orgId] }),
    })
    .reply(() => {
      requestCount++;
      return {
        statusCode: 500,
        data: JSON.stringify({ error: "Internal Server Error" }),
      };
    });

  const result1 = await getTelemetryConfigs({
    token: "test-token",
    orgIds: [orgId],
  });

  expect(result1).toEqual({
    [orgId]: { url: "", secret: "", source: "missing" },
  });

  // Verify empty config was cached
  const cachedConfig = await cache.getTelemetryConfigs([orgId]);
  expect(cachedConfig).toEqual({
    [orgId]: { url: "", secret: "", source: "missing" },
  });

  // Second call - should use cache and not hit API
  const result2 = await getTelemetryConfigs({
    token: "test-token",
    orgIds: [orgId],
  });

  expect(result2).toEqual({
    [orgId]: { url: "", secret: "", source: "cache" },
  });

  // Third call - should still use cache
  const result3 = await getTelemetryConfigs({
    token: "test-token",
    orgIds: [orgId],
  });

  expect(result3).toEqual({
    [orgId]: { url: "", secret: "", source: "cache" },
  });

  expect(requestCount).toBe(1);
});

// not something we'd expect at runtime, but noticed this a few times while testing
test("handles invalid UUIDs gracefully", async () => {
  const invalidOrgIds = ["not-a-uuid", "also-not-a-uuid"];

  // note there's no need for mock calls since the UUID check is done locally

  const result = await getTelemetryConfigs({
    token: "test-token",
    orgIds: invalidOrgIds,
  });

  expect(result).toEqual({
    "not-a-uuid": { url: "", secret: "", source: "missing" },
    "also-not-a-uuid": { url: "", secret: "", source: "missing" },
  });

  mockAgent.assertNoPendingInterceptors();
});

test("verifies cached configs don't have expiry extended on access", async () => {
  const orgId = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11";

  mockPool
    .intercept({
      path: "/api/telemetry/configs",
      method: "POST",
      body: JSON.stringify({ org_ids: [orgId] }),
    })
    .reply(() => {
      return {
        statusCode: 200,
        data: JSON.stringify({
          configs: {
            [orgId]: { url: "test-url", secret: "test-secret" },
          },
        }),
      };
    });

  // Initial call to cache the config
  const result1 = await getTelemetryConfigs({
    token: "test-token",
    orgIds: [orgId],
  });

  expect(result1).toEqual({
    [orgId]: { url: "test-url", secret: "test-secret", source: "api" },
  });

  // Get initial expiration timestamp
  const initialExpiry = [...mockRedis.expirations.values()][0];
  expect(initialExpiry).toBeDefined();

  // Advance time by 1 minute
  vi.advanceTimersByTime(60 * 1000);

  // Access the cached value
  const result2 = await getTelemetryConfigs({
    token: "test-token",
    orgIds: [orgId],
  });

  expect(result2).toEqual({
    [orgId]: { url: "test-url", secret: "test-secret", source: "cache" },
  });

  // Check if expiration changed
  const newExpiry = [...mockRedis.expirations.values()][0];
  expect(newExpiry).toBe(initialExpiry); // Should be exactly the same timestamp
});

test("caches missing configs when API returns partial results", async () => {
  const orgIds = [
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
    "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13",
  ];

  let requestCount = 0;
  mockPool
    .intercept({
      path: "/api/telemetry/configs",
      method: "POST",
      body: JSON.stringify({ org_ids: orgIds }),
    })
    .reply(() => {
      requestCount++;
      // Only return config for first org
      return {
        statusCode: 200,
        data: JSON.stringify({
          configs: {
            [orgIds[0]]: { url: "http://telemetry1.com", secret: "secret1" },
          },
        }),
      };
    });

  // First call should hit the API and cache all results (both found and missing)
  const result1 = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  expect(result1).toEqual({
    [orgIds[0]]: {
      url: "http://telemetry1.com",
      secret: "secret1",
      source: "api",
    },
    [orgIds[1]]: { url: "", secret: "", source: "missing" },
    [orgIds[2]]: { url: "", secret: "", source: "missing" },
  });

  expect(requestCount).toBe(1);

  // Second call should use cache for all orgs (including missing ones)
  const result2 = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  expect(result2).toEqual({
    [orgIds[0]]: {
      url: "http://telemetry1.com",
      secret: "secret1",
      source: "cache",
    },
    [orgIds[1]]: { url: "", secret: "", source: "cache" },
    [orgIds[2]]: { url: "", secret: "", source: "cache" },
  });

  // Verify no additional API calls were made
  expect(requestCount).toBe(1);

  // Third call should still use cache
  const result3 = await getTelemetryConfigs({
    token: "test-token",
    orgIds,
  });

  expect(result3).toEqual({
    [orgIds[0]]: {
      url: "http://telemetry1.com",
      secret: "secret1",
      source: "cache",
    },
    [orgIds[1]]: { url: "", secret: "", source: "cache" },
    [orgIds[2]]: { url: "", secret: "", source: "cache" },
  });

  expect(requestCount).toBe(1);
});
