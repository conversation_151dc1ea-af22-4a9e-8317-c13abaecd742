import { beforeEach, describe, expect, Mock, test, vi } from "vitest";
import { getTelemetryConfigs } from "./getTelemetryConfig";
import { reportTelemetry } from "./reportTelemetry";
import { request } from "./request";
import { AnyEvent } from "./types";

// Mock dependencies
vi.mock("./request", () => ({
  request: vi.fn(),
}));

vi.mock("./getTelemetryConfig", () => ({
  getTelemetryConfigs: vi.fn().mockResolvedValue({
    "test-org": {
      url: "https://test-telemetry.com",
      secret: "test-secret",
    },
  }),
}));

vi.mock("../token_auth", () => ({
  checkTokenAuthorized: vi.fn().mockResolvedValue({
    me: { id: "test-user", organizations: [] },
  }),
}));

describe("reportTelemetry", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("successfully reports telemetry", async () => {
    const mockResponse = { ok: true, body: { json: vi.fn() } };
    (request as Mock).mockResolvedValue(mockResponse);

    const testEvents = [
      {
        event_name: "test_event",
        properties: { test: "value", nullValue: null },
        external_customer_id: "test-org",
      },
    ] as unknown as AnyEvent[];

    await reportTelemetry({ events: testEvents, token: "test-token" });

    expect(request).toHaveBeenCalledWith(
      "https://test-telemetry.com",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer test-secret",
        },
        body: expect.stringContaining('"nullValue":"__UNSET__"'),
      }),
    );
  });

  test("handles empty events array", async () => {
    await reportTelemetry({ events: [], token: undefined });
    expect(request).not.toHaveBeenCalled();
  });

  test("handles request errors", async () => {
    const mockResponse = {
      ok: false,
      statusCode: 500,
      body: { json: vi.fn().mockResolvedValue("Server Error") },
    };
    (request as Mock).mockResolvedValue(mockResponse);

    const testEvents = [
      {
        event_name: "test_event",
        properties: { test: "value" },
        external_customer_id: "test-org",
      },
    ] as unknown as AnyEvent[];

    await expect(
      reportTelemetry({ events: testEvents, token: "test-token" }),
    ).resolves.toBeUndefined();

    expect(request).toHaveBeenCalledWith(
      "https://test-telemetry.com",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer test-secret",
        },
      }),
    );
  });

  test("skips telemetry when config has no url", async () => {
    vi.mocked(getTelemetryConfigs).mockResolvedValueOnce({
      "test-org": {
        url: "",
        secret: "test-secret",
      },
    });

    const testEvents = [
      {
        event_name: "test_event",
        properties: { test: "value" },
        external_customer_id: "test-org",
      },
    ] as unknown as AnyEvent[];

    await reportTelemetry({ events: testEvents, token: "test-token" });
    expect(request).not.toHaveBeenCalled();
  });

  test("skips telemetry when config has no secret", async () => {
    vi.mocked(getTelemetryConfigs).mockResolvedValueOnce({
      "test-org": {
        url: "https://test-telemetry.com",
        secret: "",
      },
    });

    const testEvents = [
      {
        event_name: "test_event",
        properties: { test: "value" },
        external_customer_id: "test-org",
      },
    ] as unknown as AnyEvent[];

    await reportTelemetry({ events: testEvents, token: "test-token" });
    expect(request).not.toHaveBeenCalled();
  });

  test("groups events by config and reports each group separately", async () => {
    const mockResponse = { ok: true, body: { json: vi.fn() } };
    (request as Mock).mockResolvedValue(mockResponse);

    // Mock configs for two different orgs with different endpoints
    vi.mocked(getTelemetryConfigs).mockResolvedValueOnce({
      "org-1": {
        url: "https://telemetry-1.com",
        secret: "secret-1",
      },
      "org-2": {
        url: "https://telemetry-2.com",
        secret: "secret-2",
      },
      // Same config as org-1 to test grouping
      "org-3": {
        url: "https://telemetry-1.com",
        secret: "secret-1",
      },
      "org-4": {
        url: "",
        secret: "",
      },
    });

    const testEvents = [
      {
        event_name: "event_1",
        properties: { test: "value1", type: "individual" },
        external_customer_id: "org-1",
      },
      {
        event_name: "event_2",
        properties: { test: "value2", type: "individual" },
        external_customer_id: "org-2",
      },
      {
        event_name: "event_3",
        properties: { test: "value3", type: "individual" },
        external_customer_id: "org-3",
      },
      {
        event_name: "event_4",
        properties: { test: "value4", type: "individual" },
        external_customer_id: "org-4",
      },
    ] as unknown as AnyEvent[];

    await reportTelemetry({ events: testEvents, token: "test-token" });

    // Should have been called twice - once for each unique config and none for the one with no url or secret
    expect(request).toHaveBeenCalledTimes(2);

    // Check call for first config (org-1 and org-3 events)
    expect(request).toHaveBeenCalledWith(
      "https://telemetry-1.com",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer secret-1",
        },
        body: JSON.stringify({
          events: [testEvents[0], testEvents[2]],
        }),
      }),
    );

    // Check call for second config (org-2 events)
    // Verify the second request was made correctly
    expect(request).toHaveBeenCalledWith(
      "https://telemetry-2.com",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer secret-2",
        },
        body: JSON.stringify({
          events: [
            {
              ...testEvents[1],
              properties: { ...testEvents[1].properties, type: "individual" },
            },
          ],
        }),
      }),
    );
  });

  test("continues processing events when one request fails", async () => {
    // Mock a success response for the first config and a failure for the second
    const mockSuccessResponse = { ok: true, body: { json: vi.fn() } };
    const mockFailureResponse = {
      ok: false,
      statusCode: 500,
      body: { json: vi.fn().mockResolvedValue("Internal Server Error") },
    };

    (request as Mock).mockImplementation((url) => {
      if (url === "https://telemetry-1.com") {
        return Promise.resolve(mockSuccessResponse);
      } else {
        return Promise.resolve(mockFailureResponse);
      }
    });

    // Mock configs for two different orgs
    vi.mocked(getTelemetryConfigs).mockResolvedValueOnce({
      "org-1": {
        url: "https://telemetry-1.com",
        secret: "secret-1",
      },
      "org-2": {
        url: "https://telemetry-2.com",
        secret: "secret-2",
      },
    });

    const testEvents = [
      {
        event_name: "event_1",
        properties: { test: "value1" },
        external_customer_id: "org-1",
      },
      {
        event_name: "event_2",
        properties: { test: "value2" },
        external_customer_id: "org-2",
      },
    ] as unknown as AnyEvent[];

    await expect(
      reportTelemetry({ events: testEvents, token: "test-token" }),
    ).resolves.toBeUndefined();

    // Both requests should have been attempted
    expect(request).toHaveBeenCalledTimes(2);

    // Verify the successful request was made correctly
    expect(request).toHaveBeenCalledWith(
      "https://telemetry-1.com",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer secret-1",
        },
        body: JSON.stringify({
          events: [testEvents[0]].map((event) => ({
            ...event,
            properties: Object.fromEntries(
              Object.entries({ ...event.properties, type: "individual" }).map(
                ([key, value]) => [key, value ?? "__UNSET__"],
              ),
            ),
          })),
        }),
      }),
    );

    // Verify the failed request was attempted
    expect(request).toHaveBeenCalledWith(
      "https://telemetry-2.com",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer secret-2",
        },
        body: JSON.stringify({
          events: [testEvents[1]].map((event) => ({
            ...event,
            properties: Object.fromEntries(
              Object.entries({ ...event.properties, type: "individual" }).map(
                ([key, value]) => [key, value ?? "__UNSET__"],
              ),
            ),
          })),
        }),
      }),
    );
  });
});
