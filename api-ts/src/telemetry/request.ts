import {
  checkControlPlaneRateLimit,
  CustomFetchOptions,
  CustomFetchResponse,
  initializeGlobalDispatcher,
} from "../custom_fetch";

import { request as undiciRequest } from "undici";
import { logger } from "./logger";

// Same as custom_fetch.ts with exception to logging changes. Keep both in sync.
export const request = async (
  url: string | URL,
  options?: CustomFetchOptions,
): Promise<CustomFetchResponse> => {
  initializeGlobalDispatcher();

  const urlObj = new URL(url);
  await checkControlPlaneRateLimit(urlObj, options);
  const resp = await (async () => {
    try {
      return await undiciRequest(
        urlObj,
        options
          ? {
              method: options.method,
              headers: options.headers,
              body: options.body,
              bodyTimeout: options.timeout,
            }
          : undefined,
      );
    } catch (e) {
      logger.warn(`Error when requesting url`, {
        url: urlObj,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        error: e as Error,
      });
      throw e;
    }
  })();
  const ok = resp.statusCode >= 200 && resp.statusCode < 300;
  return { ...resp, ok };
};
