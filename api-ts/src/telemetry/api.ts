import { Request, Response } from "express";
import { makeError } from "@braintrust/local";
import { getRequestContext } from "../request_context";
import { getTelemetryConfigs, redactSecrets } from "./getTelemetryConfig";
import { checkTokenAuthorized } from "../token_auth";
import { useTelemetryCache } from "./cache";
import { TelemetryConfig } from "./types";
import { TELEMETRY_ENABLED } from "../env";
import { AccessDeniedError, ForbiddenError, wrapZodError } from "../util";
import { z } from "zod";
import { getAggregationStatus } from "./aggregation";
import { logger } from "./logger";

// These API handlers are internal for debugging purposes. Re-enable in api.ts and app.py, if needed.
const getBillingStatusRequestSchema = z.object({
  orgIds: z.array(z.string()).default([]),
});

export async function getBillingStatusRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);

  const authInfo = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
      checkSysadmin: true,
    })
  ).me;

  // TODO: permit org owners to get the status for the orgs they have access to
  if (!authInfo.is_sysadmin) {
    throw new AccessDeniedError({
      permission: "sysadmin",
      objectType: "billing",
      objectId: "status",
    });
  }

  const params = wrapZodError(() =>
    getBillingStatusRequestSchema.parse(ctx.data),
  );

  try {
    const [telemetryConfigs, aggregation] = await Promise.all([
      getTelemetryConfigs({
        token: ctx.token,
        orgIds: params.orgIds,
      }),
      getAggregationStatus({
        orgIds: params.orgIds,
      }),
    ]);

    res.json({
      enabled: TELEMETRY_ENABLED,
      configs: redactSecrets(telemetryConfigs, ctx.token),
      aggregation,
    });
  } catch (error) {
    logger.error("Failed to get billing status", {
      error: makeError(error),
    });

    res.status(500).json({
      error: "Failed to retrieve billing status",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

const invalidateTelemetryCacheRequestSchema = z.object({
  orgIds: z.array(z.string()).default([]),
});

export async function invalidateTelemetryCacheRequest(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);

  const telemetryCache = useTelemetryCache();

  const authInfo = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
      checkSysadmin: true,
    })
  ).me;

  // TODO: permit org owners to clear this
  if (!authInfo.is_sysadmin) {
    throw new ForbiddenError("Permission denied to invalidate telemetry cache");
  }

  const params = wrapZodError(() =>
    invalidateTelemetryCacheRequestSchema.parse(ctx.data),
  );

  // Set the cache to expire in 1 second, so that it will be invalidated
  await telemetryCache.setTelemetryConfigs(
    params.orgIds.reduce((acc: Record<string, TelemetryConfig>, orgId) => {
      acc[orgId] = { url: "", secret: "" };
      return acc;
    }, {}),
    1,
  );

  res.json({});
}
