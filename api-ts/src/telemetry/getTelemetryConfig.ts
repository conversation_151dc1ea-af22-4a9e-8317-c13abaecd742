import { parseNoStrip } from "braintrust/util";
import { endpointSchemas } from "@braintrust/local/app-schema";
import { ALLOWED_ORIGIN, TELEMETRY_TOKEN, TELEMETRY_URL } from "../env";
import { postDefaultHeaders } from "../util";
import { useTelemetryCache } from "./cache";
import { logger } from "./logger";
import { request } from "./request";
import { TelemetryConfig } from "./types";
import { makeError } from "@braintrust/local";

type TelemetryConfigWithSource = TelemetryConfig & {
  // source helps with debugging to better understand the source of the config which may very grately
  source: "environment" | "api" | "cache" | "missing";
};

export async function getTelemetryConfigs({
  token,
  orgIds,
}: {
  token: string | undefined;
  orgIds: string[];
}) {
  const configs: Record<string, TelemetryConfigWithSource> = {};
  for (const orgId of orgIds) {
    configs[orgId] = { url: "", secret: "", source: "missing" as const };
  }

  // if set in the environment, all orgs need to use the same config
  if (TELEMETRY_URL) {
    for (const orgId of orgIds) {
      configs[orgId] = {
        url: TELEMETRY_URL,
        secret: (TELEMETRY_TOKEN || "").trim() || token || "",
        source: "environment" as const,
      };
    }
    return configs;
  }

  // override any configs that are already cached
  const telemetryCache = useTelemetryCache();
  const cachedConfigs = await telemetryCache.getTelemetryConfigs(orgIds);
  for (const [orgId, config] of Object.entries(cachedConfigs)) {
    configs[orgId] = { ...config, source: "cache" };
  }

  // find which org IDs still need to be looked up (those still marked as missing)
  const lookupOrgIds = orgIds.filter((id) => configs[id].source === "missing");
  const apiConfigs = await lookupConfigs(lookupOrgIds, token);
  for (const [orgId, config] of Object.entries(apiConfigs)) {
    configs[orgId] = { ...config, source: "api" };
  }

  // only update cache for new or missing configs to avoid extending TTL of existing entries
  const configsToUpdate: Record<string, TelemetryConfigWithSource> = {};
  for (const [orgId, config] of Object.entries(configs)) {
    if (!cachedConfigs[orgId] || config.source === "api") {
      configsToUpdate[orgId] = config;
    }
  }

  if (Object.keys(configsToUpdate).length > 0) {
    await telemetryCache.setTelemetryConfigs(configsToUpdate);
  }

  // Set token fallback for any configs with URLs but no secrets
  for (const [orgId, config] of Object.entries(configs)) {
    if (config.url && !config.secret) {
      configs[orgId] = { ...config, secret: token || "" };
    }
  }

  return configs;
}

const lookupConfigs = async (orgIds: string[], token?: string) => {
  if (orgIds.length === 0) {
    return {};
  }

  try {
    const resp = await request(`${ALLOWED_ORIGIN}/api/telemetry/configs`, {
      method: "POST",
      headers: postDefaultHeaders({ token }),
      body: JSON.stringify(
        parseNoStrip(endpointSchemas.telemetry_configs_typespecs.input, {
          org_ids: orgIds,
        }),
      ),
    });

    if (resp?.ok) {
      const { configs } =
        endpointSchemas.telemetry_configs_typespecs.output.parse(
          await resp.body.json(),
        );
      return configs;
    }
  } catch (e) {
    logger.warn("Error fetching telemetry configs", {
      error: makeError(e),
    });
  }

  return {};
};

export const redactSecrets = (
  configs: Record<string, TelemetryConfigWithSource>,
  token?: string,
) => {
  const result: Record<string, TelemetryConfigWithSource> = {};
  for (const [orgId, config] of Object.entries(configs)) {
    result[orgId] = {
      ...config,
      secret:
        config.secret === token
          ? "[TOKEN]"
          : config.secret
            ? "[REDACTED]"
            : config.secret,
    };
  }
  return result;
};
