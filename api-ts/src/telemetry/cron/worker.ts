import { makeError } from "@braintrust/local";
import { flushStore } from "../aggregation";
import { logger } from "../logger";
import { pushTelemetryEvents } from "../reportTelemetry";

/**
 * Our main entrypoint for the billing work.
 */
export const worker = async () => {
  // XXX: determine if the flushStore `canContinue`
  const flushed = await flushStore();

  await Promise.all(
    Object.values(flushed).map(({ events, token }) =>
      pushTelemetryEvents({ events, token }).catch((e) => {
        logger.error("Failed to push telemetry events", {
          error: makeError(e),
        });
      }),
    ),
  ).then(() => {
    logger.debug("All telemetry events pushed successfully");
  });
};
