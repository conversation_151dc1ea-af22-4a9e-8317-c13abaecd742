import { logger } from "../logger";

export const SLEEP_INTERVAL_SECONDS = (() => {
  if (process.env.TELEMETRY_SLEEP_INTERVAL_SECONDS) {
    const interval = parseInt(process.env.TELEMETRY_SLEEP_INTERVAL_SECONDS);
    if (!isNaN(interval)) {
      if (interval > 60000) {
        return interval;
      } else {
        logger.warn("TELEMETRY_SLEEP_INTERVAL_SECONDS is less than 60 seconds");
      }
    } else {
      logger.warn("TELEMETRY_SLEEP_INTERVAL_SECONDS is not a number");
    }
  }
  return 5 * 60 * 1000; // 5 minutes
})();
