// This has to be done before the API is initialized
import "../../instrumentation/setup_dd";

import {
  lambdaRequestTracker,
  pinoLambdaDestination,
  StructuredLogFormatter,
} from "pino-lambda";
import { initPinoLogger } from "../../instrumentation/logger";

initPinoLogger(
  pinoLambdaDestination({
    formatter: new StructuredLogFormatter(),
  }),
);
const withPinoRequest = lambdaRequestTracker();

import type { EventBridgeHandler } from "aws-lambda";
import { initializeEnv } from "../../env";

initializeEnv(false);

import { ALLOWED_ORIGIN, BRAINTRUST_APP_URL } from "../../env";
import { worker } from "./worker";
import { logger } from "../logger";

export const handler: EventBridgeHandler<
  "Billing Cron",
  "Billing Cron",
  unknown
> = async (event, context) => {
  // We set this in every lambda so that the response is sent immediately (not after
  // the event loop is empty).
  context.callbackWaitsForEmptyEventLoop = false;

  withPinoRequest(event, context);

  const appOrigin = BRAINTRUST_APP_URL ?? ALLOWED_ORIGIN;
  if (!appOrigin) {
    throw new Error("Missing appOrigin");
  }

  await worker();
  logger.debug("Billing cron loop completed");
  return { success: true };
};
