import { makeError } from "@braintrust/local";
import { ALLOWED_ORIGIN, BRAINTRUST_APP_URL } from "../../env";
import { sleep } from "../../util";
import { logger } from "../logger";
import { SLEEP_INTERVAL_SECONDS } from "./constants";
import { worker } from "./worker";

/**
 * Used for local development. Will create an endless cron-like sleep/wake routine.
 */
export const start = () => {
  const appOrigin = BRAINTRUST_APP_URL ?? ALLOWED_ORIGIN;
  if (!appOrigin) {
    throw new Error("Missing appOrigin");
  }

  (async () => {
    logger.debug("Start synthetic billing cron loop");
    while (true) {
      const startTime = Date.now();
      try {
        logger.debug("billing cron run");
        await worker();
      } catch (err) {
        logger.error("Error in billing cron loop", { error: makeError(err) });
      } finally {
        logger.debug("billing cron run completed");
      }

      const endTime = Date.now();

      const elapsed = endTime - startTime;

      const remaining = SLEEP_INTERVAL_SECONDS - elapsed;
      if (remaining < 0) {
        logger.warn("Last tick took too long", {
          extra: -1 * elapsed,
          interval: SLEEP_INTERVAL_SECONDS,
        });
      }

      logger.debug(
        `Elapsed time: ${elapsed}ms, Sleeping for: ${remaining}ms. Waking up at: ${new Date(Date.now() + remaining).toLocaleString()}`,
      );

      if (remaining > 0) {
        // did we execute too fast? need to sleep until the next tick
        await sleep(remaining);
      }
    }
  })().catch((err) => {
    logger.error(
      "Fatal error in billing cron loop. The loop will exit and no cron jobs will be run.",
      { error: err },
    );
    process.exit(1);
  });
};
