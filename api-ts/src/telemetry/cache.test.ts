import { afterEach, beforeEach, expect, test, vi } from "vitest";
import { createMockRedisClient } from "../redis.test";
import { useTelemetryCache } from "./cache";

let mockRedis = createMockRedisClient();

vi.mock("../redis", () => ({
  getRedis: () => Promise.resolve(mockRedis.client),
}));

beforeEach(() => {
  vi.useFakeTimers();
  mockRedis.store.clear();
});

afterEach(() => {
  vi.useRealTimers();
});

test("telemetry cache operations", async () => {
  const cache = useTelemetryCache();

  const configs = {
    org1: { url: "http://test1.com", secret: "secret1" },
    org2: { url: "http://test2.com" },
  } as const;

  await cache.setTelemetryConfigs(configs);

  const storedConfigs = await cache.getTelemetryConfigs(["org1", "org2"]);
  expect(storedConfigs).toEqual(configs);

  for (const [key, value] of mockRedis.store) {
    expect(key).toMatch(/^telemetry:/);
    expect(JSON.parse(value)).toEqual(
      configs[key.split(":")[1] as keyof typeof configs],
    );
  }
});

test("telemetry cache handles missing values", async () => {
  const cache = useTelemetryCache();

  const configs = {
    org1: { url: "http://test1.com" },
  };

  await cache.setTelemetryConfigs(configs);
  const result = await cache.getTelemetryConfigs(["org1", "nonexistent"]);

  expect(Object.keys(result)).toHaveLength(1);
  expect(result.org1).toEqual({ url: "http://test1.com" });
  expect(result.nonexistent).toBeUndefined();
});

test("Redis mock handles key expiration", async () => {
  const startTime = Date.now();
  vi.setSystemTime(startTime);

  // Set a key with 60 second expiration
  await mockRedis.client.set("test-key", "value", { EX: 60 });
  expect(await mockRedis.client.get("test-key")).toBe("value");

  // Move forward 30 seconds - key should still exist
  vi.setSystemTime(startTime + 30_000);
  expect(await mockRedis.client.get("test-key")).toBe("value");

  // Move forward past expiration
  vi.setSystemTime(startTime + 61_000);
  expect(await mockRedis.client.get("test-key")).toBeNull();

  // Verify NX behavior with expired keys
  await mockRedis.client.set("test-key-2", "initial", { EX: 60 });
  vi.setSystemTime(startTime + 122_000); // Move past expiration

  // Should succeed since original key is expired
  const result = await mockRedis.client.set("test-key-2", "new", { NX: true });
  expect(result).toBe("OK");
  expect(await mockRedis.client.get("test-key-2")).toBe("new");
});
