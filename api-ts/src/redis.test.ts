import type { RedisClientType } from "redis";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

export function createMockRedisClient() {
  const store = new Map<string, string>();
  const expirations = new Map<string, number>();

  const client = {
    get: async (key: string) => {
      const expiry = expirations.get(key);
      if (expiry && Date.now() > expiry) {
        store.delete(key);
        expirations.delete(key);
        return null;
      }
      return store.get(key) ?? null;
    },
    set: async (
      key: string,
      value: string,
      options?: { NX?: boolean; EX?: number },
    ) => {
      // Check expiration before NX check
      const expiry = expirations.get(key);
      if (expiry && Date.now() > expiry) {
        store.delete(key);
        expirations.delete(key);
      }

      if (options?.NX && store.has(key)) {
        return null;
      }

      store.set(key, value);
      if (options?.EX) {
        expirations.set(key, Date.now() + options.EX * 1000);
      }
      return "OK";
    },
    mGet: async (keys: string[]) => {
      return Promise.all(keys.map((key) => client.get(key)));
    },
    multi: () => {
      const operations: Array<() => Promise<unknown>> = [];
      return {
        set: (key: string, value: string) => {
          operations.push(() => client.set(key, value));
          return operations.length - 1;
        },
        exec: async () => {
          return Promise.all(operations.map((op) => op()));
        },
      };
    },
    incr: async (key: string) => {
      const value = await client.get(key);
      if (value === null) {
        await client.set(key, "1");
        return 1;
      }
      const current = parseInt(value, 10);
      const newValue = (current + 1).toString();
      await client.set(key, newValue);
      return current + 1;
    },
  } as unknown as RedisClientType;

  return {
    client,
    store,
    expirations,
  };
}

describe("Redis", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("should handle basic get and set operations", async () => {
    const { client, store } = createMockRedisClient();
    await client.set("key1", "value1");
    expect(await client.get("key1")).toBe("value1");
    expect(store.get("key1")).toBe("value1");
    expect(await client.get("nonexistent")).toBe(null);
    expect(store.has("nonexistent")).toBe(false);
  });

  it("should handle NX option for set", async () => {
    const { client, store } = createMockRedisClient();
    await client.set("key1", "value1");
    expect(store.get("key1")).toBe("value1");

    await client.set("key1", "value2", { NX: true });
    expect(await client.get("key1")).toBe("value1");
    expect(store.get("key1")).toBe("value1");

    await client.set("key2", "value2", { NX: true });
    expect(await client.get("key2")).toBe("value2");
    expect(store.get("key2")).toBe("value2");
  });

  it("should handle expiration with EX option", async () => {
    const { client, store, expirations } = createMockRedisClient();
    const now = Date.now();

    await client.set("key1", "value1", { EX: 1 });
    expect(await client.get("key1")).toBe("value1");
    expect(store.get("key1")).toBe("value1");
    expect(expirations.get("key1")).toBe(now + 1000);

    // Advance time by 1.1 seconds
    vi.advanceTimersByTime(1100);
    expect(await client.get("key1")).toBe(null);
    expect(store.has("key1")).toBe(false);
    expect(expirations.has("key1")).toBe(false);
  });

  it("should handle expired keys in set with NX", async () => {
    const { client, store, expirations } = createMockRedisClient();
    const now = Date.now();

    await client.set("key1", "value1", { EX: 1 });
    expect(store.get("key1")).toBe("value1");
    expect(expirations.get("key1")).toBe(now + 1000);

    // Advance time by 1.1 seconds
    vi.advanceTimersByTime(1100);

    // Trigger expiration check
    await client.get("key1");
    expect(expirations.has("key1")).toBe(false);

    // Should be able to set with NX after expiration
    await client.set("key1", "value2", { NX: true });
    expect(await client.get("key1")).toBe("value2");
    expect(store.get("key1")).toBe("value2");
    expect(expirations.has("key1")).toBe(false); // No expiration set
  });

  it("should handle mGet operations", async () => {
    const { client, store } = createMockRedisClient();
    await client.set("key1", "value1");
    await client.set("key2", "value2");

    expect(store.get("key1")).toBe("value1");
    expect(store.get("key2")).toBe("value2");

    const results = await client.mGet(["key1", "key2", "nonexistent"]);
    expect(results).toEqual(["value1", "value2", null]);
  });

  it("should handle multi operations", async () => {
    const { client, store } = createMockRedisClient();
    const multi = client.multi();
    multi.set("key1", "value1");
    multi.set("key2", "value2");

    // Store should be empty before exec
    expect(store.has("key1")).toBe(false);
    expect(store.has("key2")).toBe(false);

    await multi.exec();
    expect(await client.get("key1")).toBe("value1");
    expect(await client.get("key2")).toBe("value2");
    expect(store.get("key1")).toBe("value1");
    expect(store.get("key2")).toBe("value2");
  });

  it("should handle incr operations", async () => {
    const { client, store } = createMockRedisClient();

    // First increment on non-existent key
    expect(await client.incr("counter")).toBe(1);
    expect(store.get("counter")).toBe("1");

    // Subsequent increments
    expect(await client.incr("counter")).toBe(2);
    expect(store.get("counter")).toBe("2");
    expect(await client.incr("counter")).toBe(3);
    expect(store.get("counter")).toBe("3");

    // Verify the final value
    expect(await client.get("counter")).toBe("3");
  });

  it("should handle expiration with mGet", async () => {
    const { client, store, expirations } = createMockRedisClient();
    const now = Date.now();

    await client.set("key1", "value1", { EX: 1 });
    await client.set("key2", "value2");
    expect(expirations.get("key1")).toBe(now + 1000);

    // Before expiration
    let results = await client.mGet(["key1", "key2"]);
    expect(results).toEqual(["value1", "value2"]);

    // Advance time by 1.1 seconds
    vi.advanceTimersByTime(1100);

    // After expiration
    results = await client.mGet(["key1", "key2"]);
    expect(results).toEqual([null, "value2"]);
    expect(store.has("key1")).toBe(false);
    expect(expirations.has("key1")).toBe(false);
  });
});
