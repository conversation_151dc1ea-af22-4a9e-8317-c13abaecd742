import type { TableData, Connection } from "duckdb";

export let getDuckDBConn: () => Connection = () => {
  throw new Error("DuckDB not initialized");
};

export function setDuckDBConnGetter(fn: () => Connection) {
  getDuckDBConn = fn;
}

export async function duckq(con: Connection, sql: string): Promise<TableData> {
  return new Promise((resolve, reject) => {
    con.all(sql, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}
