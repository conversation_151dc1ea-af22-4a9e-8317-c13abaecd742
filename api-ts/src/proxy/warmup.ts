import { checkTokenAuthorized } from "../token_auth";
import { GetResFn } from "./proxy";
import { extractAllowedOrigin, ORIGIN_HEADER } from "../cors";
import { parseBraintrustAuthHeader, parseHeader } from "../auth-header";
import { ORG_NAME_HEADER } from "@braintrust/proxy";
import { BadRequestError } from "../util";
import {
  canUseLambdaQuarantine,
  getOrgInlineCodeRunner,
  RUNTIME_TO_LAMBDA_RUNTIME,
} from "../lambda-quarantine/pool";
import { finish } from "./request";

export async function handleWarmupCodeExecution({
  headers,
  setStatusCode,
  getRes,
}: {
  headers: Record<string, string>;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (statusCode: number) => void;
  getRes: GetResFn;
}) {
  if (!canUseLambdaQuarantine()) {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 200,
      message: "Code execution warmup skipped",
    });
  }

  const appOrigin = extractAllowedOrigin(headers[ORIGIN_HEADER]);
  const ctxToken = parseBraintrustAuthHeader(headers) ?? undefined;
  const orgName = parseHeader(headers, ORG_NAME_HEADER);

  const me = await (
    await checkTokenAuthorized({
      ctxToken,
      appOrigin,
    })
  ).me;

  const orgId = me.organizations.find(
    (org) => !orgName || org.name === orgName,
  )?.id;
  if (!orgId) {
    throw new BadRequestError(`Organization ${orgName} not found`);
  }

  const promises: Promise<unknown>[] = [];
  for (const [runtimeName, runtimeVersions] of Object.entries(
    RUNTIME_TO_LAMBDA_RUNTIME,
  )) {
    for (const runtimeVersion of Object.keys(runtimeVersions)) {
      promises.push(
        getOrgInlineCodeRunner({
          orgId,
          runtimeSpec: {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            runtime: runtimeName as "node" | "python",
            version: runtimeVersion,
          },
        }),
      );
    }
  }
  await Promise.all(promises);

  return finish({
    getRes,
    setStatusCode,
    statusCode: 200,
    message: "Warmup code execution completed",
  });
}
