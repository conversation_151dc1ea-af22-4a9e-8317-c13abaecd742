import express, { Request } from "express";
import cors from "cors";
import { baseAllowedHeaders, checkOrigin } from "../cors";
import { BT_CURSOR_HEADER, BT_FOUND_EXISTING_HEADER } from "braintrust/util";
import { nodeProxyV1 } from "./proxy";
import {
  functionPrefix,
  handleFunction,
  handleFunctionTask,
} from "./functions";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import bodyParser from "body-parser";
import { handleServerError } from "./server-error-handler";

const MAX_BODY_SIZE = "100mb";

// These should *roughly* match the settings in api/app.py.
// Notably, we permit a few headers that are only relevant to
// the proxy workload.
export const corsMiddleware = cors({
  origin: checkOrigin,
  methods: ["GET", "PATCH", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: [
    ...baseAllowedHeaders,
    // These headers are proxy-sepecific.
    "x-bt-org-name",
    "x-bt-stream-fmt",
    "x-bt-use-cache",
    "x-stainless-os",
    "x-stainless-lang",
    "x-stainless-package-version",
    "x-stainless-runtime",
    "x-stainless-runtime-version",
    "x-stainless-arch",
  ],
  credentials: true,
  exposedHeaders: [
    BT_CURSOR_HEADER,
    BT_FOUND_EXISTING_HEADER,
    "x-bt-span-id",
    "x-bt-span-export",
    "x-bt-query-plan",
    "x-bt-internal-trace-id",
  ],
  maxAge: 86400,
});

export function installProxyEndpoints({
  app,
  proxyServerPort,
  proxyPrefix,
}: {
  app: express.Express;
  proxyServerPort: number;
  proxyPrefix: string;
}) {
  const textParser = express.text({ type: () => true, limit: MAX_BODY_SIZE });

  app.get("/", corsMiddleware, textParser, async (req, res) => {
    res.setHeader("Content-Type", "text/plain");
    res.send("Hello World!");
  });

  const jsonParser = bodyParser.json({
    type: () => true,
    limit: MAX_BODY_SIZE,
  });
  const proxyUrl = `http://localhost:${proxyServerPort}${proxyPrefix}`;

  app.options(`${functionPrefix}/*`, corsMiddleware);
  app.post(
    `${functionPrefix}/*`,
    corsMiddleware,
    jsonParser,
    async (req, res) => {
      try {
        await handleFunction({
          url: req.url,
          headers: getHeaders(req),
          body: req.body,
          setHeader: res.setHeader.bind(res),
          setStatusCode: res.status.bind(res),
          getRes: () => res,
          proxyUrl,
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      } catch (e: unknown) {
        handleServerError(e, req, res);
      }
    },
  );

  app.options(`/v1/function/:id/invoke`, corsMiddleware);
  app.post(
    `/v1/function/:id/invoke`,
    corsMiddleware,
    jsonParser,
    async (req, res) => {
      try {
        await handleFunctionTask({
          taskName: "invoke",
          headers: getHeaders(req),
          body: {
            function_id: req.params.id,
            ...req.body,
          },
          setHeader: res.setHeader.bind(res),
          setStatusCode: res.status.bind(res),
          getRes: () => res,
          proxyUrl,
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      } catch (e: unknown) {
        handleServerError(e, req, res);
      }
    },
  );

  app.options(`/v1/eval`, corsMiddleware);
  app.post(`/v1/eval`, corsMiddleware, jsonParser, async (req, res) => {
    try {
      await handleFunctionTask({
        taskName: "eval",
        headers: getHeaders(req),
        body: req.body,
        setHeader: res.setHeader.bind(res),
        setStatusCode: res.status.bind(res),
        getRes: () => res,
        proxyUrl: `http://localhost:${proxyServerPort}${proxyPrefix}`,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    } catch (e: unknown) {
      handleServerError(e, req, res);
    }
  });

  app.get(proxyPrefix, corsMiddleware, textParser, async (req, res) => {
    res.setHeader("Content-Type", "text/plain");
    res.send("Hello World!");
  });

  // Since this can be the most permissive pattern, match it at the end.
  app.all(`${proxyPrefix}/*`, corsMiddleware, textParser, async (req, res) => {
    const url = req.url.slice(proxyPrefix.length);
    if (req.method !== "GET" && req.method !== "POST") {
      res.status(405).send("Method Not Allowed");
      return;
    }
    try {
      await nodeProxyV1({
        method: req.method,
        url,
        headers: getHeaders(req),
        body: req.body,
        setHeader: res.setHeader.bind(res),
        setStatusCode: res.status.bind(res),
        getRes: () => res,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    } catch (e: unknown) {
      handleServerError(e, req, res);
    }
  });

  app.post(`/debug/flush`, async (_req, res) => {
    const num = await PENDING_FLUSHABLES.flush();
    res.status(200).json({ flushed: num });
  });
}

function getHeaders(req: Request): Record<string, string> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return Object.fromEntries(
    Object.entries(req.headers)
      .filter(([k, v]) => v !== null && v !== undefined)
      .map(([k, v]) => [k.toLowerCase(), Array.isArray(v) ? v.join(",") : v]),
  ) as Record<string, string>;
}
