const _eventsByOrgId = new Map<
  string,
  { events: Event[]; timeout: NodeJS.Timeout | null }
>();

const _eventsByKey = new Set<string>();

export const reset = (query?: { orgId?: string }) => {
  if (query?.orgId) {
    _eventsByOrgId.set(query?.orgId, { events: [], timeout: null });
  } else {
    _eventsByOrgId.clear();
  }

  _eventsByKey.clear();

  return {
    response: {},
    status: 200,
  };
};

type Event = {
  idempotency_key: string;
  customer_id?: string;
  external_customer_id?: string;
  timestamp: string;
};

export const retrieve = (query?: { orgId?: string }) => {
  return {
    response: query?.orgId
      ? (_eventsByOrgId.get(query?.orgId)?.events ?? [])
      : Array.from(_eventsByOrgId.values()).reduce(
          (all, { events }) => [...all, ...events],
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          [] as Event[],
        ),
    status: 200,
  };
};

export const ingest = (
  events: Event[],
  options?: { debug?: boolean; backfill_id?: string },
) => {
  if (options?.backfill_id) {
    throw new Error('Unsupported query param "backfill_id" for mock ingestion');
  }

  const response: {
    debug: {
      duplicate: Set<string>;
      ingested: string[];
    };
    validation_failed: {
      idempotency_key: string;
      validation_errors: string[];
    }[];
  } = {
    debug: {
      duplicate: new Set(),
      ingested: [],
    },
    validation_failed: [],
  };

  for (const event of events) {
    const errors = [];
    if (!event.idempotency_key) {
      errors.push("Missing idempotency key");
    }

    if (_eventsByKey.has(event.idempotency_key)) {
      errors.push(
        `Duplicate idempotency key. See previous event: ${JSON.stringify(_eventsByKey.has(event.idempotency_key))}`,
      );
      response.debug.duplicate.add(event.idempotency_key);
    }

    if (isNaN(new Date(event.timestamp).getSeconds())) {
      errors.push(`Invalid timestamp "${event.timestamp}"`);
    } else {
      const now = new Date();
      const attempt = new Date(event.timestamp);

      // Check that attempt is UTC by comparing with toISOString()
      if (attempt.toISOString() !== event.timestamp) {
        errors.push("Timestamp must be in UTC ISO format");
      }

      // Check timestamp is not more than 24 hours ahead
      const twentyFourHoursFromNow = new Date(
        now.getTime() + 24 * 60 * 60 * 1000,
      );
      if (attempt > twentyFourHoursFromNow) {
        errors.push("Timestamp cannot be more than 24 hours in the future");
      }

      // Check timestamp is not more than 1 hour in the future
      const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
      if (attempt > oneHourFromNow) {
        errors.push("Timestamp cannot be more than 1 hour in the future");
      }

      // Check timestamp is not too far in the past (1 hour grace period)
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      if (attempt < oneHourAgo) {
        errors.push("Timestamp cannot be more than 1 hour in the past");
      }
    }

    if (event.customer_id && event.external_customer_id) {
      errors.push("Cannot have both customer_id and external_customer_id");
    }

    if (!event.customer_id && !event.external_customer_id) {
      errors.push("Need at least one customer_id or external_customer_id");
    }

    if (errors.length) {
      response.validation_failed.push({
        idempotency_key: event.idempotency_key,
        validation_errors: errors,
      });
      continue;
    }

    // success
    _eventsByKey.add(event.idempotency_key);

    // At this point, at least one ID exists due to validation above
    const orgId = (event.external_customer_id || event.customer_id)!;

    if (!_eventsByOrgId.has(orgId)) {
      _eventsByOrgId.set(orgId, { events: [], timeout: null });
    }

    const store = _eventsByOrgId.get(orgId)!;

    store.events.push(event);
    response.debug.ingested.push(event.idempotency_key);

    if (store.timeout) {
      clearTimeout(store.timeout);
    }

    // prevent memory leak
    store.timeout = setTimeout(
      () => reset({ orgId }),
      1000 * 60 * 60, // 1 hour
    );
  }

  return {
    response: {
      ...response,
      debug: options?.debug
        ? { ...response.debug, duplicate: [...response.debug.duplicate.keys()] }
        : undefined,
    },
    status: !response.validation_failed.length ? 200 : 400,
  };
};
