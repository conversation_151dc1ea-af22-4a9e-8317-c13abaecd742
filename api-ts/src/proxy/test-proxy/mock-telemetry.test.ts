import { beforeEach, expect, test } from "vitest";
import { ingest, reset, retrieve } from "./mock-telemetry";

beforeEach(() => {
  reset();
});

test("basic ingestion and retrieval", () => {
  const event = {
    idempotency_key: "test-1",
    customer_id: "cust-1",
    timestamp: new Date().toISOString(),
  };

  const result = ingest([event]);
  expect(result.status).toBe(200);

  const retrieved = retrieve();
  expect(retrieved.status).toBe(200);
  expect(retrieved.response).toHaveLength(1);
  expect(retrieved.response[0]).toEqual(event);
});

test("validation failures", () => {
  const now = new Date();
  const futureDate = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 hours in future
  const pastDate = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours in past

  const cases = [
    {
      name: "missing idempotency key",
      event: {
        customer_id: "cust-1",
        timestamp: now.toISOString(),
      },
      expectedErrors: ["Missing idempotency key"],
    },
    {
      name: "future timestamp",
      event: {
        idempotency_key: "test-2",
        customer_id: "cust-1",
        timestamp: futureDate.toISOString(),
      },
      expectedErrors: ["Timestamp cannot be more than 1 hour in the future"],
    },
    {
      name: "past timestamp",
      event: {
        idempotency_key: "test-3",
        customer_id: "cust-1",
        timestamp: pastDate.toISOString(),
      },
      expectedErrors: ["Timestamp cannot be more than 1 hour in the past"],
    },
    {
      name: "invalid timestamp",
      event: {
        idempotency_key: "test-4",
        customer_id: "cust-1",
        timestamp: "not-a-date",
      },
      expectedErrors: ['Invalid timestamp "not-a-date"'],
    },
    {
      name: "missing customer identifiers",
      event: {
        idempotency_key: "test-5",
        timestamp: now.toISOString(),
      },
      expectedErrors: ["Need at least one customer_id or external_customer_id"],
    },
    {
      name: "both customer identifiers",
      event: {
        idempotency_key: "test-6",
        customer_id: "cust-1",
        external_customer_id: "ext-1",
        timestamp: now.toISOString(),
      },
      expectedErrors: ["Cannot have both customer_id and external_customer_id"],
    },
  ];

  for (const c of cases) {
    const result = ingest([c.event as any], { debug: false, backfill_id: "" });
    expect(result.status).toBe(400);
    expect(result.response.validation_failed).toHaveLength(1);
    expect(result.response.validation_failed[0].validation_errors).toEqual(
      c.expectedErrors,
    );
  }
});

test("duplicate events", () => {
  const event = {
    idempotency_key: "test-1",
    customer_id: "cust-1",
    timestamp: new Date().toISOString(),
  };

  // First ingestion should succeed
  const result1 = ingest([event]);
  expect(result1.status).toBe(200);

  // Second ingestion should fail with duplicate error
  const result2 = ingest([event]);
  expect(result2.status).toBe(400);
  expect(result2.response.validation_failed[0].validation_errors[0]).toContain(
    "Duplicate idempotency key",
  );
});

test("backfill_id rejection", () => {
  const event = {
    idempotency_key: "test-1",
    customer_id: "cust-1",
    timestamp: new Date().toISOString(),
  };

  expect(() =>
    ingest([event], { debug: false, backfill_id: "some-id" }),
  ).toThrow('Unsupported query param "backfill_id" for mock ingestion');
});

test("reset clears state", () => {
  const event = {
    idempotency_key: "test-1",
    customer_id: "cust-1",
    timestamp: new Date().toISOString(),
  };

  // First ingestion
  ingest([event], { debug: false, backfill_id: "" });
  expect(retrieve().response).toHaveLength(1);

  // Reset
  reset();
  expect(retrieve().response).toHaveLength(0);
});
