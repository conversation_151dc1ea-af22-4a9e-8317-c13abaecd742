# This script helps to generate some test data via the proxy

#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BASE_URL="http://127.0.0.1:8001"

mkdir -p $SCRIPT_DIR/snapshots

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is 1+1?"
    }
  ],
  "stream": true
}' --compressed > snapshots/pure_text.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "Write the words one, two, and three, separated by 3 newlines"
    }
  ],
  "stream": true
}' --compressed > snapshots/newlines.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is the weather in San Francisco, CA?"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true
}' --compressed > snapshots/tool_call.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is the weather in San Francisco, CA?"
    }
  ],
  "functions": [
    {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
  ],
  "stream": true
}' --compressed > snapshots/function_call.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is the weather in San Francisco, CA and Los Angeles, CA?"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true
}' --compressed > snapshots/tool_call_double.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is 1+1? Provide an explanation but also call the tool."
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true
}' --compressed > snapshots/mixed_tool_call.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is 1+1? And the weather in SF?"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
    },
    {
      "type": "function",
      "function": {
        "name": "calculator",
        "description": "Add two numbers",
        "parameters": {
          "type": "object",
          "properties": {
            "a": {
              "type": "number"
            },
            "b": {
              "type": "number"
            }
          },
          "required": ["a", "b"]
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true
}' --compressed > snapshots/multi_tool_call.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "llama3.1-8b",
  "messages": [
    {
      "role": "user",
      "content": "Write the origin story of Apple."
    }
  ],
  "stream": true
}' --compressed > snapshots/cerebras_long.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "Write the origin story of Apple."
    }
  ],
  "max_tokens": 10,
  "stream": true
}' --compressed > snapshots/finish_reason_length.txt

curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "What is the system status?"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_system_status",
        "description": "Get the current system status",
        "parameters": {
          "type": "object",
          "properties": { },
          "required": []
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true
}' --compressed > snapshots/tool_call_empty.txt


curl "$BASE_URL/v1/chat/completions" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $BRAINTRUST_API_KEY" \
-H "x-bt-use-cache: always" \
-d '{
  "model": "claude-3-5-sonnet-latest",
  "messages": [
    {
      "role": "user",
      "content": "What is the system status?"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_system_status",
        "description": "Get the current system status",
        "parameters": {
          "type": "object",
          "properties": { },
          "required": []
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true
}' --compressed > snapshots/tool_call_empty_claude.txt

curl -i "$BASE_URL/dump-cache"
echo ""
