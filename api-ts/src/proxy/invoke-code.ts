import { CodeB<PERSON>le, RuntimeContext } from "@braintrust/typespecs";
import {
  canUseLambdaQuarantine,
  getBundledCodeRunner,
  getOrgInlineCodeRunner,
} from "../lambda-quarantine/pool";
import { useInvocableLambdaRuntime } from "../lambda-quarantine/invoke";
import {
  InvokeMethod,
  InvokeResponse,
  serializeSSEEvent,
} from "@braintrust/local/functions";
import { ForbiddenError, InternalServerError } from "../util";
import { PassThrough } from "node:stream";
import {
  createParser,
  ParsedEvent,
  ReconnectInterval,
} from "eventsource-parser";
import { z } from "zod";
import {
  ChildProcessWithoutNullStreams,
  exec,
  spawn,
} from "node:child_process";
import path from "path";
import { Worker } from "worker_threads";
import { INLINE_INVOKE_TIMEOUT_MS } from "../wrapper/constants";
import { checkTokenAuthorized } from "../token_auth";
import { ALLOW_CODE_FUNCTION_EXECUTION } from "../env";
import { promisify } from "node:util";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import os from "node:os";
import fs from "node:fs/promises";
import { HookData } from "../wrapper/lambda-util";
import { getLogger } from "../instrumentation/logger";
import pino from "pino";

export async function useInlineFunctionInvokeMethod({
  runtime,
  ctxToken,
  appOrigin,
  orgName,
}: {
  runtime: RuntimeContext;
  ctxToken: string | undefined;
  appOrigin: string;
  orgName: string | undefined;
}): Promise<InvokeMethod> {
  const authInfo = await (
    await checkTokenAuthorized({
      ctxToken,
      appOrigin,
    })
  ).me;

  const orgId = authInfo.organizations.find(
    (org) => orgName === undefined || org.name === orgName,
  )?.id;
  if (!orgId) {
    throw new ForbiddenError(`Access denied to organization ${orgName}`);
  }

  if (canUseLambdaQuarantine()) {
    const lambda = await getOrgInlineCodeRunner({
      orgId,
      runtimeSpec: runtime,
    });
    return useInvocableLambdaRuntime({ runtime, lambda });
  } else if (ALLOW_CODE_FUNCTION_EXECUTION) {
    return { type: "vm" };
  } else {
    throw new InternalServerError(
      "Inline code snippets are not supported (use-function)",
    );
  }
}

export async function useBundledFunctionInvokeMethod({
  codeBundle,
}: {
  codeBundle: CodeBundle;
}): Promise<InvokeMethod> {
  if (canUseLambdaQuarantine()) {
    const lambda = await getBundledCodeRunner({
      codeBundle,
    });
    return useInvocableLambdaRuntime({
      runtime: codeBundle.runtime_context,
      lambda,
    });
  } else if (ALLOW_CODE_FUNCTION_EXECUTION) {
    return {
      type: "vm",
    };
  } else {
    throw new InternalServerError(
      "Code bundles are not supported (use-function)",
    );
  }
}

export async function finishCodeEventStream({
  chunkIterator,
  writable,
  setStatusCode,
  onFinal,
  pino,
}: {
  chunkIterator: AsyncIterable<string>;
  writable: WritableStream<Uint8Array>;
  setStatusCode: (code: number) => void;
  onFinal?: (result: InvokeResponse) => void;
  pino: pino.Logger;
}) {
  const encoder = new TextEncoder();
  const writer = writable.getWriter();

  let error: string | undefined = undefined;
  const jsonChunks: string[] = [];

  const eventSourceParser = createParser(
    (event: ParsedEvent | ReconnectInterval) => {
      if (event.type === "reconnect-interval") {
        return;
      }

      switch (event.event) {
        case "error": {
          // Error is expected to be a JSON-serialized string (like text_delta)
          error = z.string().parse(JSON.parse(event.data));
          writer.write(encoder.encode(serializeSSEEvent(event))).catch((e) => {
            pino.error({ error: e }, "Error writing error event");
          });
          break;
        }
        case "json_delta": {
          jsonChunks.push(event.data);
          writer.write(encoder.encode(serializeSSEEvent(event))).catch((e) => {
            pino.error({ error: e }, "Error writing json_delta event");
          });
          break;
        }
        case "console": {
          writer.write(encoder.encode(serializeSSEEvent(event))).catch((e) => {
            pino.error({ error: e }, "Error writing console event");
          });
          break;
        }
        default:
          throw new InternalServerError(
            `Unknown event type: ${event.event ?? "<empty>"}`,
          );
      }
    },
  );

  try {
    for await (const chunk of chunkIterator) {
      eventSourceParser.feed(chunk);
    }
  } catch (e) {
    setStatusCode(500);
    await writer.ready;
    await writer.write(encoder.encode(`${e}`));
  }

  await writer.ready;
  writer.close().catch((e) => {
    pino.error({ error: e }, "Error closing writer");
  });
  onFinal?.({
    data: jsonChunks.length > 0 ? JSON.parse(jsonChunks.join("")) : undefined,
    error,
  });
}

export async function invokeInlineCodeVM({
  runtime,
  code,
  location,
  input,
  hookData,
  parent,
  env,
  codeCacheKey,
  writable: writableArg,
  onFinal,
  setHeader,
  setStatusCode,
  timeoutMs,
}: {
  runtime: RuntimeContext;
  code: Uint8Array;
  location: CodeBundle["location"] | undefined;
  input: unknown;
  hookData: HookData | undefined;
  parent?: string;
  env: Record<string, string>;
  codeCacheKey: string;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  writable: WritableStream<Uint8Array>;
  onFinal?: (result: InvokeResponse) => void;
  timeoutMs?: number;
}) {
  const pino = getLogger().child({
    task: "invoke",
    type: "inline-code",
  });

  const writable = new PassThrough();
  let invokePromise: Promise<void> | undefined = undefined;

  timeoutMs = timeoutMs ?? INLINE_INVOKE_TIMEOUT_MS;

  if (runtime.runtime === "node") {
    const worker = new Worker(
      path.join(__dirname, "..", "local", "vm", "wrapper-inline.js"),
      {
        workerData: {
          code: new TextDecoder().decode(code),
          input,
          hook_data: hookData,
          parent,
          location,
          timeoutMs: timeoutMs ?? INLINE_INVOKE_TIMEOUT_MS,
        },
        env,
      },
    );

    const abortController = new AbortController();
    const timeoutId = setTimeout(() => {
      abortController.abort();
      worker.postMessage("abort");
    }, timeoutMs + 200 /* give it a little extra time to fail gracefully */);

    invokePromise = new Promise((resolve, reject) => {
      worker.on("message", (message) => {
        if (message.type === "data") {
          writable.write(message.chunk);
        } else if (message.type === "done") {
          clearTimeout(timeoutId);
          writable.end();
          resolve();
        } else if (message.type === "error") {
          clearTimeout(timeoutId);
          reject(new Error(message.error));
        }
      });

      worker.on("error", (err) => {
        clearTimeout(timeoutId);
        reject(err);
      });

      PENDING_FLUSHABLES.add(
        new Promise<void>((innerResolve, innerReject) => {
          worker.on("exit", (code) => {
            clearTimeout(timeoutId);
            innerResolve();
            if (code !== 0) {
              reject(new Error(`Worker stopped with exit code ${code}`));
            }
          });
        }),
      );

      abortController.signal.addEventListener("abort", () => {
        worker.terminate().catch((e) => {
          pino.error({ error: e }, "Error terminating worker");
        });
        writable.write(
          serializeSSEEvent({
            event: "error",
            data: JSON.stringify("Function execution timed out (non-graceful)"),
          }),
        );
        writable.end();
      });
    });
  } else {
    let child: ChildProcessWithoutNullStreams;
    if (location) {
      // First, check if the bundle is already cached locally
      const bundleTmpDir = path.join(os.tmpdir(), "braintrust-bundles");
      const bundlePath = path.join(bundleTmpDir, `${codeCacheKey}.zip`);
      const bundleDir = path.join(bundleTmpDir, codeCacheKey);
      try {
        await fs.access(path.join(bundleDir, "register.py"));
        // The bundle already exists
      } catch {
        try {
          pino.info(
            {
              bundle: codeCacheKey,
              tmpDir: bundleTmpDir,
            },
            "Bundle does not exist, unzipping to tmpdir",
          );
          await fs.mkdir(bundleTmpDir, { recursive: true });
          await fs.writeFile(bundlePath, code);
          // Unzip the bundle into bundleDir
          await new Promise<void>((resolve, reject) => {
            const unzip = spawn("unzip", ["-o", bundlePath, "-d", bundleDir]);

            unzip.stdout.on("data", (data) => {
              // Ignore
            });

            unzip.stderr.on("data", (data) => {
              pino.error({ data }, "[unzip]");
            });

            unzip.on("close", (code) => {
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`unzip failed with code ${code}`));
              }
            });

            unzip.on("error", reject);
          });
        } catch (e) {
          pino.error({ error: e }, "Error unzipping bundle");

          writable.write(
            serializeSSEEvent({
              event: "error",
              data: JSON.stringify("Failed to prepare python code bundle"),
            }),
          );
          writable.end();
        }
      }

      const arg = {
        input,
        hook_data: hookData,
        timeout_ms: timeoutMs,
        parent,
        location,
        lib_path: bundleDir,
      };
      child = spawn(
        await getPythonExecutable(),
        [path.join(__dirname, "..", "local", "vm", "wrapper-bundle.py")],
        {
          stdio: ["pipe", "pipe", "pipe"],
          env,
        },
      );
      child.stdin.write(JSON.stringify(arg));
      child.stdin.end();
    } else {
      const arg = {
        code: new TextDecoder().decode(code),
        input,
        hook_data: hookData,
        timeout_ms: timeoutMs,
        parent,
      };
      child = spawn(
        await getPythonExecutable(),
        [path.join(__dirname, "..", "local", "vm", "wrapper-inline.py")],
        {
          stdio: ["pipe", "pipe", "pipe"],
          env,
        },
      );
      child.stdin.write(JSON.stringify(arg));
      child.stdin.end();
    }

    let wroteResponse = false;
    const decoder = new TextDecoder();
    child.stdout.on("data", (dataArray: Uint8Array) => {
      const data = decoder.decode(dataArray);
      writable.write(data);
      wroteResponse = true;
    });

    child.stdout.on("end", () => {
      if (wroteResponse) {
        writable.end();
      }
    });

    child.stderr.on("data", (data) => {
      pino.error({ data }, "Python stderr");
    });

    let timeoutId: NodeJS.Timeout | undefined = undefined;

    invokePromise = Promise.race([
      (async () => {
        clearTimeout(timeoutId);
        const exitCode = await new Promise<number>((resolve) => {
          child.on("close", resolve);
        });
        if (!wroteResponse && exitCode !== 0) {
          // Only throw this exception if we didn't write a response, because otherwise we'll
          // want to let the response stream include the error naturally.
          writable.write(
            new TextEncoder().encode(
              serializeSSEEvent({
                event: "error",
                data: "Python process failed to execute",
              }),
            ),
          );
          writable.end();
          throw new Error(`Python process failed to execute`);
        }

        if (writable.writable) {
          pino.warn("Did not end writable stream. Response may be empty...");
          writable.end();
        }
      })(),
      new Promise<void>((resolve, reject) => {
        timeoutId = setTimeout(() => {
          child.kill();
          writable.write(
            serializeSSEEvent({
              event: "error",
              data: JSON.stringify(
                `Python process timed out (non-graceful) after ${timeoutMs}ms`,
              ),
            }),
          );
          writable.end();
          resolve();
        }, timeoutMs);
      }),
    ]);
  }

  const chunkIterator = (async function* () {
    for await (const event of writable) {
      yield event.toString();
    }
  })();

  const finishPromise = finishCodeEventStream({
    chunkIterator,
    writable: writableArg,
    setStatusCode,
    onFinal,
    pino,
  });

  await Promise.all([invokePromise, finishPromise]);
}

const execAsync = promisify(exec);
let pythonExecutable: string | undefined = undefined;
async function getPythonExecutable(): Promise<string> {
  if (pythonExecutable) {
    return pythonExecutable;
  }
  try {
    const { stdout: pythonPath } = await execAsync("which python");
    pythonExecutable = pythonPath.trim();
  } catch {
    try {
      const { stdout: python3Path } = await execAsync("which python3");
      pythonExecutable = python3Path.trim();
    } catch {
      throw new Error("Python interpreter not found. Please install Python.");
    }
  }
  return pythonExecutable!;
}
