import { isObject } from "braintrust/util";
import {
  AttachmentReference,
  CallEventSchema,
  callEventSchema,
  FunctionId,
  Message,
  StreamingMode,
} from "@braintrust/typespecs";
import {
  createChoiceScoreParserStream,
  createConditionalJsonProcessingStream,
  createFinalValuePassThroughStream,
  InvocableFunction,
  InvokeResponse,
  isJSONResponseParams,
  serializeSSEEvent,
} from "@braintrust/local/functions";
import { ORG_NAME_HEADER } from "@braintrust/proxy";
import { buildClassificationTools, Evaluators, Scorer } from "autoevals";
import {
  BraintrustStream,
  BraintrustStreamChunk,
  Prompt,
  Span,
  withCurrent,
} from "braintrust";
import { createParser, EventSourceParser } from "eventsource-parser";
import slugifyLib from "slugify";
import { Writable } from "stream";
import { z } from "zod";
import { decryptSecrets } from "../function-secrets";
import { sha256 } from "../hash";
import { invokeCodeLambda } from "../lambda-quarantine/invoke";
import { FunctionInvokedEvent } from "../telemetry/events";
import { withTelemetry } from "../telemetry/withTelemetry";
import { BadRequestError, NotFoundError, wrapZodError } from "../util";
import { HookData } from "../wrapper/lambda-util";
import { fetchBundle, loadBundledCode } from "./bundle";
import { invokeInlineCodeVM } from "./invoke-code";
import { makeProxySpanLogger, proxyWrapper } from "./proxy";
import { getRuntimeEnv } from "./runtime-env";
import {
  createCleanupToolFormatStream,
  createToolCallbackStream,
  FunctionManager,
} from "./tool-stream";
import { checkTokenAuthorized } from "../token_auth";
import { logger } from "../telemetry/logger";
import {
  AsyncEventEmitterGroup,
  invokeGraph,
  makeLiteralStream,
} from "./graph/invoke";
import { replaceAttachmentsInPayload } from "./attachment-wrapper";
import { mergeStreams } from "../stream_util";
import { getLogger } from "../instrumentation/logger";
import { logCounter } from "../instrumentation/api";
import { randomUUID } from "crypto";

export const DEFAULT_MAX_ROUNDTRIPS = 5;
export type SpanPurpose = "scorer";

async function invokeFunctionImpl({
  func,
  input,
  messages,
  hookData,
  timeoutMs,
  strict,
  span,
  mode,
  ctxToken,
  orgName,
  orgId,
  proxyUrl,
  appOrigin,
  setHeader,
  setStatusCode,
  writable,
  onFinal,
  functionManager: functionManagerArg,
  maxRoundtrips,
  purpose,
}: {
  func: InvocableFunction;
  input: unknown;
  messages: Message[] | undefined;
  hookData: HookData | undefined;
  timeoutMs: number | undefined;
  // When building prompts, if strict is true, then we will throw an error if one of the
  // variables in the prompt is not present in the input.
  strict: boolean | undefined;
  span: Span;
  mode: StreamingMode | undefined;
  ctxToken: string | undefined;
  orgName: string | undefined;
  orgId: string | undefined;
  proxyUrl: string;
  appOrigin: string;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  writable: WritableStream<Uint8Array>;
  onFinal: (result: InvokeResponse) => void;
  functionManager?: FunctionManager;
  maxRoundtrips?: number;
  // This value is currently only used to mark LLM spans that are used as scorers
  // to remove them from token accounting. There's an analogous implementation in
  // autoevals (llm.ts).
  purpose?: SpanPurpose;
}): Promise<void> {
  mode = mode ?? "auto";
  maxRoundtrips = maxRoundtrips ?? DEFAULT_MAX_ROUNDTRIPS;

  if (
    func.function_data.type !== "prompt" &&
    messages !== undefined &&
    messages.length > 0
  ) {
    throw new BadRequestError("Messages are only supported for prompts");
  }

  logCounter({
    name: "api.function.call",
    value: 1,
    attributes: {
      org_id: orgId,
      function_type: func.function_type ?? undefined,
    },
  });

  switch (func.function_data.type) {
    case "global":
      return await callGlobalFunction({
        name: func.name,
        input,
        ctxToken,
        orgName,
        proxyUrl,
        span,
        writable,
        onFinal,
      });
    case "prompt": {
      const pino = getLogger().child({
        task: "call",
        type: "prompt",
      });
      const attachments: Record<string, AttachmentReference> = {};
      func.prompt_data = await replaceAttachmentsInPayload(
        func.prompt_data,
        span.state(),
        attachments,
      );
      const promptData = func.prompt_data;
      if (!promptData) {
        throw new Error("No prompt data available.");
      }

      let statusCode = 200;
      const setStatusCodeWrapper = (code: number) => {
        statusCode = code;
        setStatusCode(code);
      };
      const isFail = () => statusCode !== 200;
      const getStatusCode = () => statusCode;

      const prompt = new Prompt(func, {}, false);

      const hasUseCache = z
        .object({ use_cache: z.boolean() })
        .safeParse(func.prompt_data?.options?.params);

      const inputArg = await replaceAttachmentsInPayload(
        makePromptInputArg({
          input,
          hookData,
          hoistReservedNames: func.function_type === "scorer",
        }),
        span.state(),
        attachments,
      );

      let built;
      try {
        built = prompt.build(inputArg, {
          flavor: prompt.prompt?.type,
          messages,
          strict,
        });
      } catch (e) {
        if (e instanceof Error) {
          // If prompt.build fails, then it's almost certainly user error (invalid prompt syntax),
          // so we don't want to leak the stack trace.
          throw new BadRequestError(e.message);
        }
        throw e;
      }

      const { span_info, ...llmArgs } = built;

      const functionManager: FunctionManager =
        functionManagerArg ??
        new FunctionManager({
          ctxToken,
          orgName,
          orgId,
          appOrigin,
          proxyUrl,
          timeoutMs,
        });

      const toolFunctionIds: FunctionId[] = (
        func.prompt_data?.tool_functions ?? []
      ).map((tool) =>
        tool.type === "function"
          ? { function_id: tool.id }
          : { global_function: tool.name },
      );

      const toolDefinitions = await Promise.all(
        toolFunctionIds.map((func) =>
          functionManager.useFunction({
            func,
          }),
        ),
      );

      const callableTools: Record<string, FunctionId> = {};

      const promptArgs: Record<string, unknown> = {
        stream_options: {
          include_usage: true,
        },
        ...llmArgs,
        stream: true,
      };

      const existingTools = wrapZodError(() =>
        z.array(z.unknown()).nullish().parse(promptArgs.tools),
      );
      if (func.prompt_data?.parser) {
        if (existingTools && existingTools.length > 0) {
          throw new BadRequestError(
            "Cannot use both tool definitions and a parser",
          );
        }

        promptArgs.tools = buildClassificationTools(
          func.prompt_data.parser.use_cot,
          Object.keys(func.prompt_data.parser.choice_scores ?? {}),
        );
        promptArgs.tool_choice = {
          type: "function",
          function: {
            name: "select_choice",
          },
        };
      } else if (toolDefinitions.length > 0) {
        const seenNames: Record<string, number> = {};

        promptArgs.tools = [
          ...(existingTools ?? []),
          ...toolDefinitions.map((tool, index) => {
            if (!tool.function_schema?.parameters) {
              throw new BadRequestError(
                `Tool ${tool.name} does not have a parameter schema.`,
              );
            }

            let name = slugifyLib(tool.name, { lower: true, strict: true });
            if (seenNames[name] === undefined) {
              seenNames[name] = 0;
            } else {
              seenNames[name] += 1;
              name = `${name}_${seenNames[name]}`;
            }
            callableTools[name] = toolFunctionIds[index];

            return {
              type: "function",
              function: {
                name: name,
                description: tool.description,
                parameters: tool.function_schema.parameters,
              },
            };
          }),
        ];
      }

      const hasManagedToolCalls =
        toolDefinitions.length > 0 && maxRoundtrips > 0;

      // This is parsed above (into existingTools), so no need to parse it again.
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      if (promptArgs.tools && (promptArgs.tools as unknown[]).length > 0) {
        // Don't even bother letting the model respond with multiple tool calls
        // unless the downstream processor is prepared to handle them.
        promptArgs.parallel_tool_calls =
          mode === "auto" ? false : promptArgs.parallel_tool_calls;
      }

      const { readable: readableOrigin, writable: writableOrigin } =
        new TransformStream();
      let readable = readableOrigin;

      const isJSONResponse = isJSONResponseParams(promptArgs);

      readable = readable.pipeThrough(
        createConditionalJsonProcessingStream({
          mode: hasManagedToolCalls ? "parallel" : mode,
          includeToolCallIds: hasManagedToolCalls,
          isFail,
          isJSONResponse,
        }),
      );

      if (hasManagedToolCalls) {
        readable = readable.pipeThrough(
          createToolCallbackStream({
            isFail,
            span,
            baseFunction: {
              ...func,
              prompt_data: {
                ...promptData,
                prompt:
                  "prompt" in llmArgs
                    ? {
                        type: "completion",
                        content: llmArgs.prompt,
                      }
                    : {
                        type: "chat",
                        messages: llmArgs.messages,
                        tools: existingTools
                          ? JSON.stringify(existingTools)
                          : undefined,
                      },
              },
            },
            callableTools,
            functionManager,
            input,
            messages,
            maxRoundtrips: maxRoundtrips - 1,
            strict,
            isJSONResponse,
          }),
          {
            // This allows us to control when the stream terminates, so we can perform asynchronous operations
            // without the stream being closed. Specifically, since `preventClose` is set, the stream will not auto
            // close once all of the input events have been processed.
            preventClose: true,
            // If the source or destination is closed, we don't want to cancel this stream, because we know we'll always call
            // .terminate() in its implementation, and we don't really have a way of checking if either the source or destination
            // has canceled. Without these flags set, if they have terminated, then controller.terminate() will fail with an
            // unhandled promise rejection that looks like:
            //  TypeError [ERR_INVALID_STATE]: Invalid state: TransformStream has been terminated
            preventAbort: true,
            preventCancel: true,
          },
        );

        readable = readable.pipeThrough(
          createCleanupToolFormatStream({
            mode,
            isFail,
          }),
        );
      }

      if (func.prompt_data?.parser) {
        readable = readable.pipeThrough(
          createChoiceScoreParserStream({
            name: func.name,
            choiceScores: func.prompt_data.parser.choice_scores,
            isFail,
          }),
        );
      }

      readable = readable.pipeThrough(
        createFinalValuePassThroughStream({ onFinal, getStatusCode }),
      );

      const { readable: progressReadable, writable: progressWritable } =
        new TransformStream();

      const mergedReadable = mergeStreams(readable, progressReadable);

      mergedReadable.pipeTo(writable).catch((err) => {
        pino.error({ error: err }, "Error piping stream");
      });

      // We manually set the content type in handleInvoke(), and block out any attempts
      // to set it otherwise.
      let wroteProgress = false;
      const setHeaderWrapper = (name: string, value: string) => {
        if (
          wroteProgress ||
          // We control our own compression/output
          name.toLowerCase() === "content-type" ||
          // In the case of an error, the content length
          // can be totally different.
          name.toLowerCase() === "content-length"
        ) {
          return;
        }
        setHeader(name, value);
      };

      const subSpan = span.startSpan({
        type: "llm",
        name: "LLM",
        event: {
          ...span_info,
          span_attributes: {
            purpose,
            ...span_info?.spanAttributes,
          },
        },
      });

      const progressWriter = progressWritable.getWriter();

      await proxyWrapper({
        method: "POST",
        url: "/auto",
        headers: {
          "Content-Type": "application/json",
          ...(ctxToken ? { Authorization: `Bearer ${ctxToken}` } : {}),
          ...(orgName ? { [ORG_NAME_HEADER]: orgName } : {}),
          ...(hasUseCache.success
            ? {
                // If use_cache is specified, then if true, we'll try to use the cache, and if false,
                // we definitely won't.
                "x-bt-use-cache": hasUseCache.data.use_cache ? "auto" : "never",
              }
            : {}),
        },
        body: JSON.stringify(promptArgs),
        setHeader: setHeaderWrapper,
        setStatusCode: setStatusCodeWrapper,
        res: writableOrigin,
        spanLogger: {
          ...makeProxySpanLogger(subSpan, attachments),
          reportProgress(progress) {
            wroteProgress = true;
            progressWriter
              .write(
                new TextEncoder().encode(
                  serializeSSEEvent({
                    event: "progress",
                    data: JSON.stringify({
                      id: subSpan.id,
                      object_type: "prompt",
                      format: "llm",
                      output_type: "completion",
                      name: "warning",
                      event: "error",
                      data: JSON.stringify(progress),
                    }),
                  }),
                ),
              )
              .catch((err) => {
                pino.error({ error: err }, "Error writing progress");
              });
          },
        },
      }).finally(() => {
        progressWriter.close().catch((err) => {
          pino.error({ error: err }, "Error closing progress writer");
        });
      });

      // We have to call `getRes` after we give proxyWrapper the opportunity to set the status code.
      return;
    }
    case "code": {
      const pino = getLogger().child({
        task: "call",
        type: "code",
      });

      if (!func.invoke_method) {
        throw new Error("No invoke method available. Must call /use first");
      }

      const runtimeEnv = {
        ...getRuntimeEnv({
          ctxToken: ctxToken,
          appOrigin: appOrigin,
          proxyUrl: proxyUrl,
          orgName: orgName,
        }),
        ...(await decryptSecrets(func.env_secrets ?? [])),

        // avoid emitting warnings that may be shown to the user in console messages
        NODE_NO_WARNINGS: "1",
      };

      switch (func.invoke_method.type) {
        case "lambda": {
          const payload: Record<string, unknown> = {
            input,
            hook_data: hookData,
            parent: await span.export(),
            env: runtimeEnv,
          };
          if (func.function_data.data.type === "inline") {
            payload.code = func.function_data.data.code;
          } else {
            payload.location = func.function_data.data.location;
          }

          return await invokeCodeLambda({
            func: func.invoke_method,
            payload,
            writable,
            onFinal,
            setHeader,
            setStatusCode,
            timeoutMs,
          });
        }
        case "vm": {
          let code;
          let runtime_context;
          let location;
          let codeCacheKey;

          if (func.function_data.data.type === "inline") {
            code = new TextEncoder().encode(func.function_data.data.code);
            runtime_context = func.function_data.data.runtime_context;
            location = undefined;
            codeCacheKey = sha256(func.function_data.data.code);
          } else {
            const { bundlePath } = await loadBundledCode({
              bundleId: func.function_data.data.bundle_id,
            });

            const requestStream = await fetchBundle({ bundlePath });

            code = await new Promise<Uint8Array>((resolve, reject) => {
              const chunks: Uint8Array[] = [];
              requestStream.on("data", (chunk) => chunks.push(chunk));
              requestStream.on("end", () => {
                // Efficiently concatenate all chunks at once
                const totalLength = chunks.reduce(
                  (acc, chunk) => acc + chunk.length,
                  0,
                );
                const result = new Uint8Array(totalLength);
                let offset = 0;
                for (const chunk of chunks) {
                  result.set(chunk, offset);
                  offset += chunk.length;
                }
                resolve(result);
              });
              requestStream.on("error", reject);
            });

            location = func.function_data.data.location;
            runtime_context = func.function_data.data.runtime_context;
            codeCacheKey = func.function_data.data.bundle_id;
          }

          invokeInlineCodeVM({
            runtime: runtime_context,
            code,
            location,
            timeoutMs,
            input,
            hookData,
            parent: await span.export(),
            env: runtimeEnv,
            codeCacheKey: codeCacheKey,
            writable,
            onFinal,
            setHeader,
            setStatusCode,
          }).catch((err) => {
            pino.error({ error: err }, "Error invoking inline code");
          });
          return;
        }
        default: {
          const _: never = func.invoke_method;
          throw new Error(`Unsupported invoke method ${_}`);
        }
      }
    }
    case "graph": {
      const pino = getLogger().child({
        task: "call",
        type: "graph",
      });

      const functionManager: FunctionManager =
        functionManagerArg ??
        new FunctionManager({
          ctxToken,
          orgName,
          orgId,
          appOrigin,
          proxyUrl,
          timeoutMs,
        });

      const resultStream = await invokeGraph({
        graph: func.function_data,
        input: makeLiteralStream(
          makePromptInputArg({ input, hookData }),
          new AsyncEventEmitterGroup([]),
          new AbortController(),
        ),
        strict,
        functionManager,
        span,
      });

      const encoder = new TextEncoder();
      const serializer = new TransformStream<BraintrustStreamChunk, Uint8Array>(
        {
          transform(chunk, controller) {
            try {
              controller.enqueue(
                encoder.encode(
                  serializeSSEEvent(BraintrustStream.serializeRawEvent(chunk)),
                ),
              );
            } catch (err) {
              onFinal({
                data: undefined,
                error: `${err}`,
              });
            }
          },
        },
      );

      const finalValueStream = createFinalValuePassThroughStream({
        onFinal,
        getStatusCode: () => 200,
      });

      resultStream
        .pipeThrough(serializer)
        .pipeThrough(finalValueStream)
        .pipeTo(writable)
        .catch((err) => {
          pino.error({ error: err }, "Error piping stream");
        });

      return;
    }
    case "remote_eval": {
      throw new Error("Remote evals should not be executed directly (in call)");
    }
    default:
      const _: never = func.function_data;
      throw new Error(`Unsupported function type ${_}`);
  }
}

const maybeGetOrgIdFromName = async (
  orgName: string | undefined,
  {
    ctxToken,
    appOrigin,
  }: {
    ctxToken: string | undefined;
    appOrigin: string;
  },
) => {
  if (!orgName) {
    // If no orgName is provided, we can't guarantee matching an org by name. Give up!
    return undefined;
  }

  const authInfo = await (
    await checkTokenAuthorized({
      ctxToken,
      appOrigin,
    })
  ).me;

  return authInfo.organizations.find((org) => org.name === orgName)?.id;
};

export const invokeFunction = withTelemetry(
  invokeFunctionImpl,
  async ({
    args: [{ func, ctxToken, appOrigin, orgName, span }],
  }): Promise<{
    token: string | undefined;
    appOrigin: string;
    events: (FunctionInvokedEvent | undefined)[];
  }> => {
    const orgId =
      func.org_id ??
      (await maybeGetOrgIdFromName(orgName, { ctxToken, appOrigin }));

    if (!orgId) {
      throw new Error("No orgId found. Cannot attribute to a specific org.");
    }

    if (!func.id) {
      logger.warn(
        "No function id found. Cannot attribute to a specific function.",
      );
    }

    return {
      token: ctxToken,
      appOrigin: appOrigin,
      events: [
        {
          event_name: "FunctionInvokedEvent",
          // XXX: in production we had too much overlap, thus need for randomUUID
          idempotency_key: `${span.id}-${func.id}-${span.spanId}-${randomUUID().toString()}`,
          external_customer_id: orgId,
          timestamp: new Date().toISOString(),
          properties: {
            org_id: orgId,
          },
        },
      ],
    };
  },
);

async function callGlobalFunction({
  name,
  input,
  ctxToken,
  orgName,
  proxyUrl,
  span,
  writable,
  onFinal,
}: {
  name: string;
  input: unknown;
  ctxToken: string | undefined;
  orgName: string | undefined;
  proxyUrl: string;
  span: Span;
  writable: WritableStream<Uint8Array>;
  onFinal: (result: InvokeResponse) => void;
}) {
  const pino = getLogger().child({
    task: "call",
    type: "global",
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  let autoEvalFunction: Scorer<any, any> | null = null;
  for (const group of Evaluators) {
    for (const method of group.methods) {
      if (method.method.name === name) {
        autoEvalFunction = method.method;
        break;
      }
    }
  }

  if (!autoEvalFunction) {
    throw new NotFoundError(`Function ${name} not found`);
  }

  const inputParsed = z
    .object({
      output: z.unknown(),
      expected: z.unknown().nullish(),
    })
    .and(z.record(z.unknown()).nullish())
    .safeParse(input);

  if (!inputParsed.success) {
    throw new BadRequestError("Invalid autoeval arg. Must be a dictionary");
  }

  const result = await withCurrent(span, async () => {
    return await autoEvalFunction({
      openAiApiKey: ctxToken,
      openAiBaseUrl: proxyUrl,
      openAiDefaultHeaders: orgName
        ? {
            [ORG_NAME_HEADER]: orgName,
          }
        : undefined,
      ...inputParsed.data,
    });
  });

  const writer = writable.getWriter();
  writer
    .write(
      new TextEncoder().encode(
        serializeSSEEvent({
          event: "json_delta",
          data: JSON.stringify(result),
        }),
      ),
    )
    .catch((err) => {
      pino.error({ error: err }, "Error writing result");
    });
  writer.close().catch((err) => {
    pino.error({ error: err }, "Error closing writer");
  });
  onFinal({ data: result, error: undefined });
}

export async function callFunctionWrapper({
  func,
  input,
  hookData,
  messages,
  strict,
  ctxToken,
  orgName,
  orgId,
  appOrigin,
  proxyUrl,
  span,
  sendProgress,
  purpose,
  timeoutMs,
}: {
  func: InvocableFunction;
  input: unknown;
  hookData: HookData | undefined;
  messages: Message[] | undefined;
  strict: boolean | undefined;
  ctxToken: string | undefined;
  orgName: string | undefined;
  orgId: string | undefined;
  appOrigin: string;
  proxyUrl: string;
  span: Span;
  sendProgress?: (event: CallEventSchema) => void;
  purpose?: SpanPurpose;
  timeoutMs?: number;
}): Promise<unknown> {
  const metadata: {
    statusCode: number;
    headers: Record<string, string>;
  } = {
    statusCode: 200,
    headers: {},
  };

  const setHeader = (name: string, value: string) => {
    metadata.headers[name] = value;
  };

  const setStatusCode = (code: number) => {
    metadata.statusCode = code;
  };

  return await new Promise(async (resolve, reject) => {
    await invokeFunction({
      func,
      input,
      messages,
      hookData,
      timeoutMs,
      strict,
      span,
      mode: "auto",
      ctxToken,
      appOrigin,
      proxyUrl,
      orgName,
      orgId,
      setHeader,
      setStatusCode,
      writable: sendProgress
        ? progressWritable(sendProgress)
        : devNullWritableWeb(),
      onFinal: (result) => {
        if (result.error) {
          if (sendProgress) {
            sendProgress({
              event: "error",
              data: JSON.stringify(result.error),
            });
          }
          reject(result.error);
        } else {
          resolve(result.data);
        }
      },
      purpose,
    }).catch((err) => {
      if (sendProgress) {
        // This approximates the formatting we use in `logError` in the SDK.
        sendProgress({
          event: "error",
          data: JSON.stringify(`${err.message}\n\n${err.stack}`),
        });
      }
      reject(err);
    });
  });
}

export function devNullWritableNode(): Writable {
  return new Writable({
    write(_chunk, _encoding, callback) {
      callback();
    },
    final(callback) {
      callback();
    },
  });
}

export function devNullWritableWeb(): WritableStream<Uint8Array> {
  return new WritableStream({
    write() {
      // Discard the chunk
    },
  });
}

export function progressWritable(
  sendProgress: (event: CallEventSchema) => void,
): WritableStream<Uint8Array> {
  const decoder = new TextDecoder();
  let eventSourceParser: EventSourceParser;
  return new WritableStream({
    start(controller) {
      eventSourceParser = createParser((event) => {
        if (event.type === "reconnect-interval") {
          return;
        }

        const parsed = callEventSchema.safeParse(event);
        if (!parsed.success) {
          return;
        }
        sendProgress(parsed.data);
      });
    },
    write(chunk, controller) {
      eventSourceParser.feed(decoder.decode(chunk));
    },
  });
}

function isDatasetReservedName(name: string) {
  return (
    name === "input" ||
    name === "expected" ||
    name === "metadata" ||
    name === "output"
  );
}

// Here is my best attempt at writing down the intended semantics:
// 1. The "obvious" case is when you do not provide hook data. Hook data is _usually_ provided while
//    running an eval, and is how you propagate expected and metadata values to the function.
//    In the "obvious" case, `input` is just propagated "as-is". So if it contains a nested field, like
//    `{foo: "a"}`, then `input` refers to the object `{foo: "a"}` and `{{foo}}` refers to the string "a".
//    It also means that if the input is `{input: "a"}`, then `{{input}}` refers to the string "a".
// 2. If you are running an eval, and you provide hook data, then this gets installed as `expected` and
//    `metadata`. This also up-levels `input`, so if input is `{foo: "a"}`, then `input` refers to the
//    object `{foo: "a"}` and {{foo}} refers to the string "a".
// 3. This gets confusing when you have overlap between the `input` and `hookData` objects. We explicitly
//    prioritize the hooked data, so if you have `{input: "a", expected: "b"}`, then {{input}} refers to the
//    object `{input: "a", expected: "b"}` and `{{input.input}}` refers to the string "a".
// 4. Therefore, users must take care to use hook data if and only if they want this "hoisting" behavior to
//    occur. Of note, that means when you run something like a scorer, you *cannot* provide hook data, because
//    a prompt's use of {{input}} will no longer refer to the nested value of `input`.
// 5. Reserved names can optionally be hoisted. This is desired for scorers-as-tasks in the playground. There,
//    the typical task input looks like {input: "foo", expected: "bar", metadata: "baz", output: "qux"}.
//    We want to hoist the reserved names so that the scorer can use {{expected}}, {{output}}, etc. to refer
//    to the dataset's input.expected, input.output, etc. This mirrors how scorers are used in practice and saved
//    to the library, and enables users to evaluate scorers in the playground without needing to alter their prompts.
//
// DEVNOTE: Make sure to keep this logic in sync with app/ui/prompts/hooks.tsx (see the definition
// of `isDatasetReservedName` and how it's used).
export function makePromptInputArg({
  input,
  hookData,
  hoistReservedNames = false,
}: {
  input: unknown;
  hookData: HookData | undefined;
  hoistReservedNames?: boolean;
}) {
  return hookData
    ? {
        input,
        ...hookData,
        ...(isObject(input)
          ? Object.fromEntries(
              Object.entries(input).filter(([key]) =>
                hoistReservedNames ? true : !isDatasetReservedName(key),
              ),
            )
          : {}),
      }
    : input;
}
