// This proxy lets us mock LLM providers and errors to help testing in the playground.
// You can run it with:
//   cd api-ts
//   npx tsx src/proxy/mock-llm/index.ts
// and then make it a custom endpoint. If you set an env var like FORCE_429=true,
// then all requests will return 429s. Feel free to modify the code further to repro
// whatever error you're trying to test.

import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import { createProxyMiddleware } from "http-proxy-middleware";
import { parseBooleanEnv } from "../../env";

const BASE_URL = "https://api.openai.com";
const FORCE_429 = parseBooleanEnv(process.env.FORCE_429);

const app = express();
app.use(cors());

// Middleware to force 429 responses if flag is set
const force429Middleware = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (FORCE_429) {
    console.log("RATE LIMIT");
    res.status(429).json({
      error: {
        message: "Rate limit exceeded",
        type: "rate_limit_error",
        code: "rate_limit",
      },
    });
    return;
  }
  next();
};

app.use(force429Middleware);

// Proxy all requests to BASE_URL
app.use(
  "/",
  createProxyMiddleware({
    target: BASE_URL,
    changeOrigin: true,
    pathRewrite: {
      "^/": "/", // removes base path
    },
  }),
);

// Error handler
app.use((err: unknown, req: Request, res: Response, _next: NextFunction) => {
  console.error(err);
  res.status(500).json({ error: "Internal server error" });
});

const port = process.env.PORT || 8002;
app.listen(port, () => {
  console.log(`Mock LLM proxy server listening on port ${port}`);
  console.log(`Proxying requests to ${BASE_URL}`);
  if (FORCE_429) {
    console.log("FORCE_429 enabled - all requests will return 429");
  }
});
