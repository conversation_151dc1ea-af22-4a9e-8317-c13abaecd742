// This has to be done before the API is initialized
import "../instrumentation/setup_otel";
import { getLogger, initPinoLogger } from "../instrumentation/logger";
initPinoLogger();

import express from "express";
import { installProxyEndpoints } from "./server";
import { initializeEnv } from "../env";

initializeEnv(true);

export const proxyServerHost = process.env.TS_API_PROXY_HOST || "0.0.0.0";
export const proxyServerPort = parseInt(
  process.env.TS_API_PROXY_PORT || "8787",
);

function main() {
  const proxyApp = express();
  installProxyEndpoints({
    app: proxyApp,
    proxyServerPort,
    proxyPrefix: "/v1",
  });

  proxyApp.listen(proxyServerPort, proxyServerHost, () => {
    getLogger().info(
      `[proxy-server]: Proxy server is running at http://${proxyServerHost}:${proxyServerPort}`,
    );
  });
}

main();
