// This has to be done before the API is initialized
import "../instrumentation/setup_dd";

import {
  lambdaRequestTracker,
  pinoLambdaDestination,
  StructuredLogFormatter,
} from "pino-lambda";
import { getLogger, initPinoLogger } from "../instrumentation/logger";

initPinoLogger(
  pinoLambdaDestination({
    formatter: new StructuredLogFormatter(),
  }),
);
const withPinoRequest = lambdaRequestTracker();

import { streamifyResponse } from "lambda-stream";
import { APIGatewayProxyEventV2, Context } from "aws-lambda";
import { nodeProxyV1 } from "./proxy";
import {
  functionPrefix,
  handleFunction,
  handleFunctionTask,
} from "./functions";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import { initializeEnv } from "../env";
import { Writable } from "stream-json/utils/Utf8Stream";

initializeEnv(false);

const proxyPrefix = "/v1/proxy";
const legacyProxyPrefix = "/v1";

export async function handler(
  event: APIGatewayProxyEventV2,
  responseStream: Writable,
  context: Context | undefined,
): Promise<void> {
  if (context === undefined) {
    throw new Error("Context is undefined");
  }
  withPinoRequest(event, context);

  // This flag allows the function to instantly return after the responseStream finishes, without waiting
  // for sockets (namely, Redis) to close.
  // See https://stackoverflow.com/questions/46793670/reuse-redis-connections-for-nodejs-lambda-function
  // and https://docs.aws.amazon.com/lambda/latest/dg/nodejs-context.html
  context.callbackWaitsForEmptyEventLoop = false;

  // https://docs.aws.amazon.com/lambda/latest/dg/response-streaming-tutorial.html
  const metadata: {
    statusCode: number;
    headers: Record<string, string>;
  } = {
    statusCode: 200,
    headers: {
      "content-type": "text/event-stream; charset=utf-8",
    },
  };

  const getRes = (): Writable => {
    // @ts-ignore
    const nodeStream = awslambda.HttpResponseStream.from(
      responseStream,
      metadata,
    );
    const buffer: Uint8Array[] = [];
    let canWrite = true;
    return new Writable({
      write(chunk, encoding, callback) {
        if (canWrite) {
          buffer.push(chunk);
          while (buffer.length > 0 && canWrite) {
            canWrite = nodeStream.write(buffer.shift()!, encoding);
          }
          if (canWrite) {
            callback();
          } else {
            nodeStream.once("drain", () => {
              canWrite = true;
              callback();
            });
          }
        } else {
          buffer.push(chunk);
          callback();
        }
      },
      final(callback) {
        nodeStream.end(callback);
      },
    });
  };

  if (event.requestContext.http.method === "OPTIONS") {
    responseStream = getRes();
    responseStream.end();
    return;
  }

  const setHeader = (name: string, value: string) => {
    metadata.headers[name] = value;
  };
  const setStatusCode = (code: number) => {
    metadata.statusCode = code;
  };
  const proxyUrl = `https://${event.requestContext.domainName}${proxyPrefix}`;

  try {
    if (
      event.rawPath === "/" ||
      event.rawPath === proxyPrefix ||
      (event.rawPath.endsWith("/") &&
        event.rawPath.slice(0, -1) === proxyPrefix)
    ) {
      responseStream.write("Hello, world!");
      responseStream.end();
      return;
    } else if (event.rawPath.startsWith(functionPrefix)) {
      await handleFunction({
        url: event.rawPath,
        headers: getHeaders(event),
        body: JSON.parse(event.body ?? "{}"),
        setHeader,
        setStatusCode,
        getRes,
        proxyUrl,
      });
    } else if (event.rawPath === "/v1/eval") {
      await handleFunctionTask({
        taskName: "eval",
        headers: getHeaders(event),
        body: JSON.parse(event.body ?? "{}"),
        setHeader,
        setStatusCode,
        getRes,
        proxyUrl,
      });
    } else if (/^\/v1\/function\/[^/]+\/invoke$/.test(event.rawPath)) {
      const functionId = event.rawPath.split("/")[3]; // Extract function ID from the path
      await handleFunctionTask({
        taskName: "invoke",
        headers: getHeaders(event),
        body: {
          function_id: functionId,
          ...JSON.parse(event.body ?? "{}"),
        },
        setHeader,
        setStatusCode,
        getRes,
        proxyUrl,
      });
    } else if (
      event.rawPath.startsWith(proxyPrefix) ||
      event.rawPath.startsWith(legacyProxyPrefix)
    ) {
      // It is important to handle this last, because the legacy proxy prefix is otherwise a catch-all
      // for /v1/chat/completions, etc.
      if (
        event.requestContext.http.method !== "GET" &&
        event.requestContext.http.method !== "POST"
      ) {
        metadata.statusCode = 405;
        responseStream = getRes();
        responseStream.write("Method not allowed");
        return;
      }
      try {
        await nodeProxyV1({
          method: event.requestContext.http.method,
          url: event.rawPath.startsWith(proxyPrefix)
            ? event.rawPath.slice(proxyPrefix.length)
            : event.rawPath.slice(legacyProxyPrefix.length),
          headers: getHeaders(event),
          body: event.body,
          setHeader,
          setStatusCode,
          getRes,
        });
      } catch (err) {
        getLogger().error({ error: err }, "Error in proxy");
        metadata.statusCode = 500;
        responseStream = getRes();
        responseStream.write(`Internal Server Error: ${err}`);
        responseStream.end();
      }
    } else {
      metadata.statusCode = 404;
      responseStream = getRes();
      responseStream.write("Not found");
      responseStream.end();
    }
  } finally {
    await PENDING_FLUSHABLES.flush();
  }
}

streamifyResponse(handler);

function getHeaders(event: APIGatewayProxyEventV2): Record<string, string> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return Object.fromEntries(
    Object.entries(event.headers)
      .filter(([k, v]) => v !== null && v !== undefined)
      .map(([k, v]) => [k.toLowerCase(), v]),
  ) as Record<string, string>;
}
