import { RuntimeContext } from "@braintrust/typespecs";
import { sha256Digest } from "./proxy";
import path from "path";
import fs from "node:fs/promises";

export function wrapperPath({
  runtimeSpec,
  inline,
  lambda,
}: {
  runtimeSpec: RuntimeContext;
  inline: boolean;
  lambda: boolean;
}): string {
  const subdir = lambda ? "vm" : "../local/vm";
  const wrapperType =
    inline || (runtimeSpec.runtime == "node" && !lambda) ? "inline" : "bundle";
  const ext = runtimeSpec.runtime == "node" ? "js" : "py";
  return path.join(__dirname, subdir, `wrapper-${wrapperType}.${ext}`);
}

const codeHashCache: Record<string, string> = {};
export async function wrapperCodeHash({
  runtimeSpec,
  inline,
  lambda,
}: {
  runtimeSpec: RuntimeContext;
  inline: boolean;
  lambda: boolean;
}) {
  const filePath = wrapperPath({ runtimeSpec, inline, lambda });

  if (codeHashCache[filePath]) {
    return codeHashCache[filePath];
  }

  const fileContents = await fs.readFile(filePath, "utf-8");
  const hash = sha256Digest(fileContents);
  codeHashCache[filePath] = hash;
  return hash;
}
