import { test, expect, describe } from "vitest";
import { makePromptInputArg } from "./call";
import { z } from "zod";

describe("makePromptInputArg semantics", () => {
  describe("Case 1: No hook data (obvious case)", () => {
    test("simple input object propagated as-is", () => {
      const input = makePromptInputArg({
        input: { foo: "a" },
        hookData: undefined,
      });
      expect(input).toEqual({ foo: "a" });
    });

    test("input with reserved name 'input' propagated as-is", () => {
      const input = makePromptInputArg({
        input: { input: "a" },
        hookData: undefined,
      });
      expect(input).toEqual({ input: "a" });
    });

    test("input with multiple reserved names propagated as-is", () => {
      const input = makePromptInputArg({
        input: {
          input: "test_input",
          expected: "test_expected",
          metadata: { key: "value" },
          output: "test_output",
          foo: "bar",
        },
        hookData: undefined,
      });
      expect(input).toEqual({
        input: "test_input",
        expected: "test_expected",
        metadata: { key: "value" },
        output: "test_output",
        foo: "bar",
      });
    });

    test("primitive input propagated as-is", () => {
      const stringInput = makePromptInputArg({
        input: "hello world",
        hookData: undefined,
      });
      expect(stringInput).toEqual("hello world");

      const numberInput = makePromptInputArg({
        input: 42,
        hookData: undefined,
      });
      expect(numberInput).toEqual(42);

      const arrayInput = makePromptInputArg({
        input: [1, 2, 3],
        hookData: undefined,
      });
      expect(arrayInput).toEqual([1, 2, 3]);
    });
  });

  describe("Case 2: With hook data (eval scenario)", () => {
    test("simple input with hook data - input is preserved and hook data added", () => {
      const input = makePromptInputArg({
        input: { foo: "a" },
        hookData: {
          expected: "Brownie and Spotty",
          metadata: { source: "test" },
        },
      });
      expect(input).toEqual({
        input: { foo: "a" },
        expected: "Brownie and Spotty",
        metadata: { source: "test" },
        foo: "a", // Non-reserved field from input is "hoisted"
      });
    });

    test("input fields are hoisted except reserved names", () => {
      const input = makePromptInputArg({
        input: {
          question: "Who were the dogs?",
          context: "Story about pets",
          input: "nested_input", // Reserved - should not be hoisted
          expected: "nested_expected", // Reserved - should not be hoisted
          metadata: { nested: true }, // Reserved - should not be hoisted
          output: "nested_output", // Reserved - should not be hoisted
        },
        hookData: {
          expected: "Brownie and Spotty",
          metadata: { source: "eval" },
        },
      });
      expect(input).toEqual({
        input: {
          question: "Who were the dogs?",
          context: "Story about pets",
          input: "nested_input",
          expected: "nested_expected",
          metadata: { nested: true },
          output: "nested_output",
        },
        expected: "Brownie and Spotty", // Hook data takes precedence
        metadata: { source: "eval" }, // Hook data takes precedence
        question: "Who were the dogs?", // Non-reserved field hoisted
        context: "Story about pets", // Non-reserved field hoisted
      });
    });

    test("primitive input with hook data", () => {
      const input = makePromptInputArg({
        input: "simple string",
        hookData: {
          expected: "expected_value",
          metadata: { type: "string" },
        },
      });
      expect(input).toEqual({
        input: "simple string",
        expected: "expected_value",
        metadata: { type: "string" },
      });
    });
  });

  describe("Case 3: Overlap scenarios - hook data prioritized", () => {
    test("hook data overwrites conflicting top-level fields", () => {
      const input = makePromptInputArg({
        input: {
          question: "original_question",
          expected: "input_expected", // Will be filtered out due to reserved name
        },
        hookData: {
          expected: "hook_expected", // Should take precedence
          metadata: { source: "hook" },
        },
      });
      expect(input).toEqual({
        input: {
          question: "original_question",
          expected: "input_expected",
        },
        expected: "hook_expected", // Hook data wins
        metadata: { source: "hook" },
        question: "original_question", // Non-reserved field hoisted
      });
    });

    test("complex overlap scenario from comment example", () => {
      // From comment: "if you have `{input: "a", expected: "b"}` then {{input}} refers to the
      // object `{input: "a", expected: "b"}` and `{{input.input}}` refers to the string "a""
      const result = makePromptInputArg({
        input: {
          input: "a",
          expected: "b",
          custom_field: "c",
        },
        hookData: {
          expected: "hook_expected",
          metadata: { test: true },
        },
      });

      expect(result).toEqual({
        input: {
          input: "a",
          expected: "b",
          custom_field: "c",
        },
        expected: "hook_expected", // Hook data overrides input.expected
        metadata: { test: true },
        custom_field: "c", // Non-reserved field hoisted
      });

      // Verify the semantic: {{input}} would refer to the whole input object
      const parsedResult = z
        .object({
          input: z.object({
            input: z.string(),
            expected: z.string(),
            custom_field: z.string(),
          }),
        })
        .parse(result);
      expect(parsedResult.input).toEqual({
        input: "a",
        expected: "b",
        custom_field: "c",
      });
      // And {{input.input}} would refer to "a"
      expect(parsedResult.input.input).toEqual("a");
    });
  });

  describe("Edge cases", () => {
    test("empty input object with hook data", () => {
      const input = makePromptInputArg({
        input: {},
        hookData: {
          expected: "test_expected",
          metadata: { empty: true },
        },
      });
      expect(input).toEqual({
        input: {},
        expected: "test_expected",
        metadata: { empty: true },
      });
    });

    test("null input with hook data", () => {
      const input = makePromptInputArg({
        input: null,
        hookData: {
          expected: "test_expected",
          metadata: { null_input: true },
        },
      });
      expect(input).toEqual({
        input: null,
        expected: "test_expected",
        metadata: { null_input: true },
      });
    });

    test("input with only reserved names and hook data", () => {
      const input = makePromptInputArg({
        input: {
          input: "nested_input",
          expected: "nested_expected",
          metadata: { nested: true },
          output: "nested_output",
        },
        hookData: {
          expected: "hook_expected",
          metadata: { hook: true },
        },
      });
      expect(input).toEqual({
        input: {
          input: "nested_input",
          expected: "nested_expected",
          metadata: { nested: true },
          output: "nested_output",
        },
        expected: "hook_expected", // Hook data overrides
        metadata: { hook: true }, // Hook data overrides
        // No fields hoisted because all were reserved
      });
    });

    test("scorer scenario - no hook data to preserve input semantics", () => {
      // From comment: "when you run something like a scorer, you *cannot* provide hook data, because
      // a prompt's use of {{input}} will no longer refer to the nested value of `input`"
      const scorerInput = makePromptInputArg({
        input: {
          input: "Who were the dogs?",
          expected: "Brownie and Spotty",
          output: "The dogs were Brownie and Spotty",
          metadata: { scorer: true },
        },
        hookData: undefined, // Important: no hook data for scorers
      });

      // The whole object is preserved as-is, so {{input}} would refer to "Who were the dogs?"
      expect(scorerInput).toEqual({
        input: "Who were the dogs?",
        expected: "Brownie and Spotty",
        output: "The dogs were Brownie and Spotty",
        metadata: { scorer: true },
      });
    });
  });

  // Keep original tests for backwards compatibility
  describe("Original test cases", () => {
    test("original test case 1", () => {
      const input = makePromptInputArg({
        input: {
          expected: "Brownie and Spotty",
          input: "Who were the two canines who lived next door to each other?",
          output: "Brownie and Spotty",
        },
        hookData: undefined,
      });
      expect(input).toEqual({
        expected: "Brownie and Spotty",
        input: "Who were the two canines who lived next door to each other?",
        output: "Brownie and Spotty",
      });
    });

    test("original test case 2", () => {
      const input = makePromptInputArg({
        input: {
          expected: "Brownie and Spotty",
          input: "Who were the two canines who lived next door to each other?",
          output: "Brownie and Spotty",
        },
        hookData: {
          expected: "Brownie and Spotty",
          metadata: {
            foo: "bar",
          },
        },
      });

      expect(input).toEqual({
        input: {
          expected: "Brownie and Spotty",
          input: "Who were the two canines who lived next door to each other?",
          output: "Brownie and Spotty",
        },
        expected: "Brownie and Spotty",
        metadata: {
          foo: "bar",
        },
      });
    });
  });
});
