import { GetResFn } from "./proxy";

export function finish({
  getRes,
  setStatusCode,
  statusCode,
  message,
}: {
  getRes: GetResFn;
  setStatusCode: (code: number) => void;
  statusCode: number;
  message: string;
}) {
  setStatusCode(statusCode);
  writeTo({ getRes, message });
}

export function writeTo({
  getRes,
  message,
}: {
  getRes: GetResFn;
  message: string;
}) {
  const res = getRes();
  res.write(message);
  res.end();
}
