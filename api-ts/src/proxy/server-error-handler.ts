import { Request, Response } from "express";
import { ProxyBadRequestError } from "@braintrust/proxy";
import { BadRequestError, logRequestFailure } from "../util";
import { getLogger } from "../instrumentation/logger";

export function handleServerError(
  err: unknown,
  req: Request,
  res: Response,
): void {
  const { statusCode, jsonData } = (() => {
    if (err instanceof ProxyBadRequestError || err instanceof BadRequestError) {
      return {
        statusCode: 400,
        jsonData: { Code: "BadRequestError", Message: err.message },
      };
    } else {
      // TODO: we should probably be less permissive about what details we send
      // back here.
      const errMessage = err instanceof Error ? err.stack : String(err);
      return {
        statusCode: 500,
        jsonData: { Code: "InternalServerError", Message: errMessage },
      };
    }
  })();

  logRequestFailure({ logger: getLogger(), req, statusCode, err });
  res.status(statusCode).json(jsonData);
}
