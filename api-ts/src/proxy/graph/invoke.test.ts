import { describe, it, expect, beforeAll } from "vitest";
import { consumeStreamValue, invokeGraph } from "./invoke";
import { GraphData } from "@braintrust/typespecs";
import { BraintrustStreamChunk } from "braintrust";

describe("invokeGraph", () => {
  // Helper function to create a readable stream from a value
  function createReadableStream(
    value: any,
  ): ReadableStream<BraintrustStreamChunk> {
    return new ReadableStream({
      start(controller) {
        controller.enqueue({
          type: "json_delta",
          data: JSON.stringify(value),
        });
        controller.enqueue({
          type: "done",
          data: "",
        });
        controller.close();
      },
    });
  }

  it("should pass input directly to output", async () => {
    // Create a simple graph with input -> output
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputValue = { message: "Hello, world!" };
    const inputStream = createReadableStream(inputValue);

    const outputStream = await invokeGraph({ graph, input: inputStream });
    const chunks = await consumeStreamNoAbort(outputStream);

    expect(chunks).toEqual(inputValue);
  });

  it("should output a literal value", async () => {
    const literalValue = { message: "I'm a literal value" };
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        literal1: { type: "literal", value: literalValue },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "literal1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ ignored: true });
    const outputStream = await invokeGraph({ graph, input: inputStream });
    const chunks = await consumeStreamNoAbort(outputStream);

    expect(chunks).toEqual(literalValue);
  });

  it("should handle multiple literals connected to output", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        literal1: { type: "literal", value: "First literal" },
        literal2: { type: "literal", value: "Second literal" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "literal2", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ ignored: true });
    const outputStream = await invokeGraph({ graph, input: inputStream });
    const chunks = await consumeStreamNoAbort(outputStream);

    expect(chunks).toEqual("Second literal");
  });

  it("should throw error for multiple input nodes", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        input2: { type: "input" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ test: true });

    await expect(
      async () => await invokeGraph({ graph, input: inputStream }),
    ).rejects.toThrow("Multiple input nodes are not supported");
  });

  it("should throw error for multiple output nodes", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
        output2: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ test: true });

    await expect(
      async () => await invokeGraph({ graph, input: inputStream }),
    ).rejects.toThrow("Multiple output nodes are not supported");
  });

  it("should throw error for missing input node", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        literal1: { type: "literal", value: "test" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "literal1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ test: true });

    await expect(
      async () => await invokeGraph({ graph, input: inputStream }),
    ).rejects.toThrow("Did not encounter an input node");
  });

  it("should default create an output node if missing", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        literal1: { type: "literal", value: "test" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "literal1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ test: true });

    const outputStream = await invokeGraph({ graph, input: inputStream });
    const chunks = await consumeStreamNoAbort(outputStream);

    expect(chunks).toEqual("test");
  });

  it("should throw error for duplicate variable on a node", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        literal1: { type: "literal", value: "test" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
        edge2: {
          source: { node: "literal1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ test: true });

    await expect(
      async () => await invokeGraph({ graph, input: inputStream }),
    ).rejects.toThrow("Variable input already exists on node output1");
  });

  it("should handle error propagation", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    // Create a stream that will error
    const errorStream = new ReadableStream<BraintrustStreamChunk>({
      start(controller) {
        console.log("gonna fail");
        setTimeout(() => controller.error(new Error("Test error")), 0);
      },
    });

    const outputStream = await invokeGraph({ graph, input: errorStream });

    await expect(async () => {
      const result = await consumeStreamNoAbort(outputStream);
      console.log("result", result);
    }).rejects.toThrow(/Test error/);
    console.log("done");
  }, 1000);

  it("should handle multiple chunks from input", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    // Create a stream with multiple chunks
    const multiChunkStream = new ReadableStream<BraintrustStreamChunk>({
      start(controller) {
        controller.enqueue({
          type: "text_delta",
          data: "chunk 1",
        });
        controller.enqueue({
          type: "text_delta",
          data: "chunk 2",
        });
        controller.enqueue({
          type: "text_delta",
          data: "chunk 3",
        });
        controller.close();
      },
    });

    const outputStream = await invokeGraph({ graph, input: multiChunkStream });
    const chunks = await consumeStreamNoAbort(outputStream);

    expect(chunks).toBe("chunk 1chunk 2chunk 3");
  });

  it("nodes throwing an exception should propagate", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
        throw1: {
          // @ts-ignore
          type: "_debug_code",
          fn: () => {
            throw new Error("Test error");
          },
        },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "throw1", variable: "input" },
          purpose: "data",
        },
        edge2: {
          source: { node: "throw1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ ignored: true });
    expect(
      async () => await invokeGraph({ graph, input: inputStream }),
    ).rejects.toThrow("Test error");
  });

  it("unused nodes should shut down", async () => {
    let resolveRead2Finished: () => void;
    const read2Finished = new Promise<void>((resolve) => {
      resolveRead2Finished = resolve;
    });
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
        read1: {
          // @ts-ignore
          type: "_debug_code",
          fn: () => {
            return { value: createReadableStream({ ignored: 1 }) };
          },
        },
        orphan: {
          // @ts-ignore
          type: "_debug_code",
          fn: () => {
            return {
              value: new ReadableStream({
                start(controller) {
                  // Don't finish
                  controller.enqueue({
                    type: "text_delta",
                    data: "chunk 1",
                  });
                },
              }),
            };
          },
        },
        read2: {
          // @ts-ignore
          type: "_debug_code",
          fn: (
            args: {
              value: ReadableStream<BraintrustStreamChunk>;
            },
            {
              cancelController,
              errorController,
            }: {
              cancelController: AbortController;
              errorController: AbortController;
            },
          ) => {
            consumeStreamValue(args.value, {
              signal: cancelController.signal,
            })
              .catch((error) => {
                errorController.abort(error);
              })
              .finally(() => {
                resolveRead2Finished?.();
              });
            return {};
          },
        },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "read1", variable: "input" },
          purpose: "data",
        },
        edge2: {
          source: { node: "read1", variable: "output" },
          target: { node: "output1", variable: "input" },
          purpose: "data",
        },
        edge3: {
          source: { node: "orphan", variable: "output" },
          target: { node: "read2", variable: "input" },
          purpose: "data",
        },
      },
    };

    const inputStream = createReadableStream({ ignored: true });

    try {
      const outputStream = await invokeGraph({ graph, input: inputStream });
      const chunks = await consumeStreamNoAbort(outputStream);

      expect(chunks).toEqual({ ignored: 1 });

      const result = await read2Finished;
    } catch (e) {
      console.log("caught error", e);
    }
  });

  it("should handle gate nodes", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
        gate1: { type: "gate" },
        two: { type: "literal", value: 2 },
        three: { type: "literal", value: 3 },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "gate1", variable: "input" },
          purpose: "data",
        },
        edge2: {
          source: { node: "gate1", variable: "a" },
          target: { node: "two", variable: "input" },
          purpose: "control",
        },
        edge3: {
          source: { node: "gate1", variable: "b" },
          target: { node: "three", variable: "input" },
          purpose: "control",
        },
        edge4: {
          source: { node: "two", variable: "output" },
          target: { node: "output1", variable: "output2" },
          purpose: "data",
        },
        edge5: {
          source: { node: "three", variable: "output" },
          target: { node: "output1", variable: "output3" },
          purpose: "data",
        },
      },
    };

    const aInputStream = createReadableStream("a");
    const aOutputStream = await invokeGraph({ graph, input: aInputStream });
    const aResult = await consumeStreamNoAbort(aOutputStream);
    expect(aResult).toEqual(2);
    console.log("got 2");

    const bInputStream = createReadableStream("b");
    const bOutputStream = await invokeGraph({ graph, input: bInputStream });
    const bResult = await consumeStreamNoAbort(bOutputStream);
    expect(bResult).toEqual(3);
    console.log("got 3");
  });

  it("should handle invalid gate input", async () => {
    const graph: GraphData = {
      type: "graph",
      nodes: {
        input1: { type: "input" },
        output1: { type: "output" },
        gate1: { type: "gate" },
        two: { type: "literal", value: 2 },
      },
      edges: {
        edge1: {
          source: { node: "input1", variable: "output" },
          target: { node: "gate1", variable: "input" },
          purpose: "data",
        },
        edge2: {
          source: { node: "gate1", variable: "a" },
          target: { node: "two", variable: "input" },
          purpose: "control",
        },
        edge4: {
          source: { node: "two", variable: "output" },
          target: { node: "output1", variable: "output2" },
          purpose: "data",
        },
      },
    };

    const bInputStream = createReadableStream("b");
    const bOutputStream = await invokeGraph({ graph, input: bInputStream });
    await expect(
      async () => await consumeStreamNoAbort(bOutputStream),
    ).rejects.toThrow();
  });

  // This test should likely be removed, because it's not the "correct" way to handle loops.
  // it("should handle loop nodes", async () => {
  //   const graph: GraphData = {
  //     type: "graph",
  //     nodes: {
  //       input: { type: "input" },
  //       output: { type: "output" },
  //       loop: {
  //         // @ts-ignore
  //         type: "_debug_code",
  //         fn: (
  //           args: {
  //             iters: ReadableStream<BraintrustStreamChunk>;
  //             loop_values: ReadableStream<BraintrustStreamChunk>;
  //           },
  //           {
  //             cancelController,
  //           }: {
  //             cancelController: AbortController;
  //           },
  //         ) => {
  //           return {
  //             loop_yield: new ReadableStream<BraintrustStreamChunk>({
  //               async start(controller) {
  //                 const iters = await consumeStreamValue(args.iters, {
  //                   signal: cancelController.signal,
  //                 });
  //                 if (typeof iters !== "number") {
  //                   throw new Error("Iters must be a number");
  //                 }
  //                 for (let i = 0; i < iters; i++) {
  //                   await pipeToController(
  //                     createReadableStream(i),
  //                     controller,
  //                     { end: false },
  //                   );
  //                 }
  //                 controller.close();
  //               },
  //             }),
  //             value: new ReadableStream<BraintrustStreamChunk>({
  //               async start(controller) {
  //                 const btStream = new BraintrustStream(
  //                   args.loop_values,
  //                 ).toReadableStream();

  //                 const allValues: number[] = [];
  //                 const loopValueStream = createLoopValueStream(
  //                   (value) => {
  //                     if (typeof value !== "number") {
  //                       controller.error(new Error("Value must be a number"));
  //                       return;
  //                     }
  //                     allValues.push(value);
  //                   },
  //                   (error) => {
  //                     controller.error(error);
  //                   },
  //                 );

  //                 await btStream
  //                   .pipeThrough(loopValueStream)
  //                   .pipeTo(devNullWritableStream(), {
  //                     signal: cancelController.signal,
  //                   })
  //                   .catch((error) => {
  //                     controller.error(error);
  //                   });

  //                 pipeToController(createReadableStream(allValues), controller);
  //               },
  //             }),
  //           };
  //         },
  //       },
  //     },
  //     edges: {
  //       edge1: {
  //         source: { node: "input", variable: "value" },
  //         target: { node: "loop", variable: "iters" },
  //         purpose: "data",
  //       },
  //       edge2: {
  //         source: { node: "loop", variable: "loop_yield" },
  //         target: { node: "loop", variable: "loop_values" },
  //         purpose: "data",
  //       },
  //       edge3: {
  //         source: { node: "loop", variable: "value" },
  //         target: { node: "output", variable: "value" },
  //         purpose: "data",
  //       },
  //     },
  //   };

  //   for (let len = 0; len < 10; len++) {
  //     const inputStream = createReadableStream(len);
  //     const outputStream = await invokeGraph({ graph, input: inputStream });
  //     const result = await consumeStreamNoAbort(outputStream);
  //     expect(result).toEqual(Array.from({ length: len }, (_, i) => i));
  //   }
  // });
});

function consumeStreamNoAbort(stream: ReadableStream<BraintrustStreamChunk>) {
  const controller = new AbortController();
  return consumeStreamValue(stream, { signal: controller.signal });
}
