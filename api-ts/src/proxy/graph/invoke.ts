import {
  functionIdSchema,
  GraphData,
  GraphEdge,
  GraphNode,
} from "@braintrust/typespecs";
import {
  BraintrustStream,
  BraintrustStreamChunk,
  createFinalValuePassThroughStream,
  newId,
  NOOP_SPAN,
  Prompt,
  Span,
} from "braintrust";
import { FunctionManager } from "../tool-stream";
import { DEFAULT_MAX_ROUNDTRIPS } from "../call";
import { Parser } from "@braintrust/btql/parser";
import { BindContext, bindExpr, Field } from "@braintrust/btql/binder";
import {
  BadRequestError,
  getCurrentUnixTimestamp,
  wrapZodError,
} from "../../util";
import { EventEmitter } from "events";
import { findLoops } from "./loop";
import { getLogger } from "../../instrumentation/logger";
import isEqual from "lodash.isequal";

// TODO:
// - There's now a "messages" edge 'purpose', but it's not actually supported properly in the
//   function node.
// - Support loops (see evaluator_optimizer.ts for an example)
// - If a node doesn't get called, end its spans when the overall execution finishes.

export async function invokeGraph({
  graph,
  input,
  strict,
  functionManager,
  span,
}: {
  graph: GraphData;
  input: ReadableStream<BraintrustStreamChunk>;
  strict?: boolean;
  functionManager?: FunctionManager;
  span?: Span;
}): Promise<ReadableStream<BraintrustStreamChunk>> {
  const compiled = await compileGraph({ graph, functionManager, strict });
  return invokeCompiledGraph({ compiled, input, span });
}

type NodeFn = (args: {
  dataArgs: Record<string, ReadableStream<BraintrustStreamChunk>>;
  loopArgs: Record<string, ReadableStream<BraintrustStreamChunk>>;
  controlArgs: AsyncEventEmitterGroup;
  span: Span;
}) => {
  data: Record<string, ReadableStream<BraintrustStreamChunk>>;
  control: Record<string, EventEmitter>;
};

interface CompiledGraph {
  nodes: Map<string, NodeFn>;
  spanIds: Map<string, string>;
  // [target node id] -> ([variable name] -> [source node info])
  edges: Map<
    string,
    Map<
      string,
      {
        source: GraphEdge["source"];
        purpose: GraphEdge["purpose"];
        isLoop: boolean;
      }
    >
  >;

  input: { id: string; stream: WritableStream<BraintrustStreamChunk> };
  output: { id: string; stream: ReadableStream<BraintrustStreamChunk> };
  errorController: AbortController;
  cancelController: AbortController;
}

async function compileGraph({
  graph,
  functionManager,
  strict,
}: {
  graph: GraphData;
  functionManager?: FunctionManager;
  strict: boolean | undefined;
}): Promise<CompiledGraph> {
  // Find all the loops in the graph
  const loops = findLoops(graph);

  // Find out where to derive the value of each variable for each node
  const targetToVariables = new Map<
    string,
    Map<
      string,
      {
        source: GraphEdge["source"];
        purpose: GraphEdge["purpose"];
        isLoop: boolean;
      }
    >
  >();
  const sourceToOutputs = new Map<string, Set<string>>();
  for (const [nodeId, _] of Object.entries(graph.nodes)) {
    targetToVariables.set(nodeId, new Map());
    sourceToOutputs.set(nodeId, new Set());
  }
  for (const [edgeId, edge] of Object.entries(graph.edges)) {
    const target = targetToVariables.get(edge.target.node);
    if (!target) {
      throw new Error(`Target node ${edge.target.node} not found`);
    }
    if (target.has(edge.target.variable)) {
      throw new Error(
        `Variable ${edge.target.variable} already exists on node ${edge.target.node}`,
      );
    }
    target.set(edge.target.variable, {
      source: edge.source,
      purpose: edge.purpose,
      isLoop: loops.has(edgeId),
    });
    const source = sourceToOutputs.get(edge.source.node);
    if (!source) {
      throw new Error(`Source node ${edge.source.node} not found`);
    }
    source.add(edge.source.variable);
  }

  const errorController = new AbortController();
  const cancelController = new AbortController();
  const nodeImplementations = new Map<string, NodeFn>();
  let input:
    | { id: string; stream: WritableStream<BraintrustStreamChunk> }
    | undefined;
  let output:
    | { id: string; stream: ReadableStream<BraintrustStreamChunk> }
    | undefined;

  const setInput = (arg_input: {
    id: string;
    stream: WritableStream<BraintrustStreamChunk>;
  }) => {
    if (input) {
      throw new Error("Multiple input nodes are not supported");
    }
    input = arg_input;
  };
  const setOutput = (arg_output: {
    id: string;
    stream: ReadableStream<BraintrustStreamChunk>;
  }) => {
    if (output) {
      throw new Error("Multiple output nodes are not supported");
    }
    output = arg_output;
  };

  const compiledNodes = await Promise.all(
    Object.entries(graph.nodes).map(
      async ([nodeId, node]): Promise<[string, NodeFn]> => [
        nodeId,
        await compileNode({
          nodeId,
          node,
          args: new Set(targetToVariables.get(nodeId)!.keys()),
          strict,
          outputs: sourceToOutputs.get(nodeId)!,
          functionManager,
          errorController,
          cancelController,
          setInput,
          setOutput,
        }),
      ],
    ),
  );

  const spanIds = new Map<string, string>();
  for (const [nodeId, nodeFn] of compiledNodes) {
    nodeImplementations.set(nodeId, nodeFn);
    spanIds.set(nodeId, newId());
  }

  if (!input) {
    throw new Error("Did not encounter an input node");
  }
  if (!output) {
    // Try to find a node with no outgoing edges. If there's only one such node, use that.
    const outputCandidates = new Set(Object.keys(graph.nodes));
    for (const [_, edge] of Object.entries(graph.edges)) {
      outputCandidates.delete(edge.source.node);
    }
    if (outputCandidates.size !== 1) {
      throw new Error(
        "No explicit output node and graph is too ambiguous to infer one",
      );
    }

    const outputCandidate: string = outputCandidates.values().next().value!;

    const outputNodeId = newId();
    const outputNode: GraphNode = {
      type: "output",
    };
    targetToVariables.set(
      outputNodeId,
      new Map([
        [
          "output",
          {
            source: { node: outputCandidate, variable: "output" },
            purpose: "data",
            isLoop: false,
          },
        ],
      ]),
    );

    const compiled = await compileNode({
      nodeId: outputNodeId,
      node: outputNode,
      args: new Set(["input"]),
      strict,
      outputs: new Set(["output"]),
      functionManager,
      errorController,
      cancelController,
      setInput,
      setOutput,
    });
    nodeImplementations.set(outputNodeId, compiled);
    if (!output) {
      throw new Error("Output should have been set (graph is invalid)");
    }
  }

  return {
    nodes: nodeImplementations,
    edges: targetToVariables,
    input,
    output,
    errorController,
    cancelController,
    spanIds,
  };
}

async function compileNode({
  nodeId,
  node,
  args,
  strict,
  outputs,
  functionManager,
  errorController,
  cancelController,
  setInput,
  setOutput,
}: {
  nodeId: string;
  node: GraphNode;
  args: Set<string>;
  strict: boolean | undefined;
  outputs: Set<string>;
  functionManager?: FunctionManager;
  errorController: AbortController;
  cancelController: AbortController;
  setInput: (input: {
    id: string;
    stream: WritableStream<BraintrustStreamChunk>;
  }) => void;
  setOutput: (output: {
    id: string;
    stream: ReadableStream<BraintrustStreamChunk>;
  }) => void;
}): Promise<NodeFn> {
  const pino = getLogger().child({
    task: "graph",
    type: "compile",
  });

  switch (node.type) {
    case "function": {
      if (!functionManager) {
        throw new Error(
          "Must provide a function manager to compile function nodes",
        );
      }
      assertSimpleOutput(nodeId, outputs);
      const funcId = wrapZodError(() => functionIdSchema.parse(node.function));
      const func = await functionManager.useFunction({
        func: funcId,
      });

      // TODO:
      // Now that we split out the loop args, we have to handle them here. The semantics could be something
      // like:
      // - For each iteration, make sure all data args are present
      // - If there are any data args, then we can run without the first loop arg giving us a result
      // - If there are no data args, then we need the first loop arg to give us a result
      // - Each time there's a new loop arg, we can run the function again.
      // - Message args accumulate.
      return ({ dataArgs, loopArgs, controlArgs, span }) => {
        span.setAttributes({
          name: func.name,
          type: "function",
        });
        return {
          data: {
            output: new ReadableStream<BraintrustStreamChunk>({
              async start(controller) {
                await controlArgs.ready(cancelController.signal);
                // TODO: If the function has a parameter schema, we can validate it here.
                const baseArgs = await consumeArgs(dataArgs, cancelController);
                const args = prepareInputArgs(baseArgs);

                restartSpan(span);
                span.log({ input: args });

                try {
                  const result = await functionManager.chainFunction({
                    func,
                    input: args,
                    messages: [],
                    span,
                    maxRoundtrips: DEFAULT_MAX_ROUNDTRIPS,
                    sendProgress: () => {},
                    mode: "auto", // XXX This should be derived or config param from the function
                    strict,
                  });
                  const btStream = new BraintrustStream(result);
                  pipeToController(
                    btStream.toReadableStream(),
                    controller,
                  ).catch((error) => {
                    errorController.abort(error);
                  });
                } catch (error) {
                  pino.error({ error }, "function error");
                  errorController.abort(error);
                }
              },
            }),
          },
          control: {},
        };
      };
    }
    case "prompt_template": {
      return ({ dataArgs, loopArgs, controlArgs, span }) => {
        return {
          data: {
            output: new ReadableStream<BraintrustStreamChunk>({
              async start(controller) {
                await controlArgs.ready(cancelController.signal);
                const baseArgs = await consumeArgs(dataArgs, cancelController);
                restartSpan(span);

                const args = prepareInputArgs(baseArgs);
                span.log({ input: args });
                const prompt = node.prompt;
                const renderedPrompt = Prompt.renderPrompt({
                  prompt,
                  buildArgs: args,
                  options: { strict },
                });

                span.log({ output: renderedPrompt });
                controller.enqueue({
                  type: "json_delta",
                  data: JSON.stringify(renderedPrompt),
                });
                span.end();
                controller.close();
              },
            }),
          },
          control: {},
        };
      };
    }
    case "literal": {
      assertSimpleOutput(nodeId, outputs);
      const value = node.value;
      return ({ controlArgs, span }) => {
        span.log({ output: value });
        span.end();
        return {
          data: {
            output: makeLiteralStream(value, controlArgs, cancelController),
          },
          control: {},
        };
      };
    }
    case "aggregator": {
      return ({ dataArgs, loopArgs, controlArgs, span }) => ({
        data: {
          output: new ReadableStream<BraintrustStreamChunk>({
            async start(controller) {
              await controlArgs.ready(cancelController.signal);
              const baseArgs = await consumeArgs(dataArgs, cancelController);
              const args = prepareInputArgs(baseArgs);
              restartSpan(span);
              span.log({ input: args, output: args });
              controller.enqueue({
                type: "json_delta",
                data: JSON.stringify(args),
              });
              span.end();
              controller.close();
            },
          }),
        },
        control: {},
      });
    }
    case "gate": {
      return ({ dataArgs, loopArgs, controlArgs, span }) => {
        const args = parseArgs<GateNodeArgs>(dataArgs, ["input"]);
        if (!controlArgs.isEmpty()) {
          throw new Error("Gate node received control messages");
        }

        const controlEvents = Object.fromEntries(
          Array.from(outputs).map((output) => [output, new EventEmitter()]),
        );

        const expr = node.condition
          ? bindBtqlExpression(node.condition, Object.keys(args))
          : undefined;

        consumeStreamValue(args.input, {
          signal: cancelController.signal,
        })
          .then((result) => {
            restartSpan(span);
            span.log({ input: result });

            if (expr) {
              result = evaluateBtqlExpression(expr, { input: result });
            }

            const event = controlEvents[`${result}`];
            if (!event) {
              throw new Error(`Output ${result} not found`);
            } else {
              span.log({ output: result });
              event.emit("ready");
            }
          })
          .catch((error) => {
            span.log({ error: error });
            errorController.abort(error);
          })
          .finally(() => {
            span.end();
          });

        return {
          data: {},
          control: controlEvents,
        };
      };
    }
    case "btql": {
      assertSimpleOutput(nodeId, outputs);

      const bound = bindBtqlExpression(node.expr, Object.keys(args));

      return ({ dataArgs, loopArgs, controlArgs, span }) => {
        span.setAttributes({
          name: `btql`,
          type: "function",
        });
        span.log({
          metadata: {
            btql: node.expr,
          },
        });
        return {
          data: {
            output: new ReadableStream<BraintrustStreamChunk>({
              async start(controller) {
                await controlArgs.ready(cancelController.signal);
                const args = await consumeArgs(dataArgs, cancelController);

                restartSpan(span);
                const value = evaluateBtqlExpression(bound, args);
                span.log({ output: value });
                pipeToController(
                  makeLiteralStream(value, controlArgs, cancelController),
                  controller,
                )
                  .catch((error) => {
                    errorController.abort(error);
                  })
                  .finally(() => {
                    span.end();
                  });
              },
            }),
          },
          control: {},
        };
      };
    }
    case "input": {
      assertSimpleOutput(nodeId, outputs);
      const { readable, writable } =
        new TransformStream<BraintrustStreamChunk>();
      setInput({ id: nodeId, stream: writable });
      return ({ controlArgs, span }) => {
        if (!controlArgs.isEmpty()) {
          throw new Error("Input node received control messages");
        }

        span.setAttributes({
          name: "input",
          type: "function",
        });
        const [readableCopy, readableCopy2] = readable.tee();
        span.log({
          output: readableCopy.pipeThrough(
            new TransformStream({
              flush() {
                span.end();
              },
            }),
          ),
        });
        return { data: { output: readableCopy2 }, control: {} };
      };
    }
    case "output": {
      const { readable, writable } =
        new TransformStream<BraintrustStreamChunk>();
      setOutput({ id: nodeId, stream: readable });
      return ({ dataArgs, loopArgs, controlArgs, span }) => {
        if (!controlArgs.isEmpty()) {
          throw new Error("Output node received control messages");
        }

        span.setAttributes({
          name: "output",
          type: "function",
        });

        let valueStream: ReadableStream<BraintrustStreamChunk>;
        if (Object.keys(dataArgs).length === 0) {
          throw new Error("Output node received no arguments");
        } else if (Object.keys(dataArgs).length === 1) {
          valueStream = Object.values(dataArgs)[0];
        } else {
          valueStream = makeAnyStream(
            Object.values(dataArgs),
            cancelController,
          );
        }

        let started = false;
        // NOTE: I initially tried to log the stream (a copy of it), but quickly realized
        // that this could deadlock if we never output anything, because `span.flush()` will fail
        // to resolve the lazy value.
        valueStream
          .pipeThrough(
            new TransformStream({
              transform(chunk, controller) {
                if (!started) {
                  started = true;
                  restartSpan(span);
                }
                controller.enqueue(chunk);
              },
            }),
          )
          .pipeThrough(
            createFinalValuePassThroughStream(
              (result) => {
                span.log({ output: result });
                span.end();
              },
              (error) => {
                span.log({ error: error });
                span.end();
              },
            ),
          )
          .pipeTo(writable, { signal: cancelController.signal })
          .catch((error) => {
            errorController.abort(error);
          });
        return { data: { output: readable }, control: {} };
      };
    }
    // This _debug_code type is used to allow testing with code.
    // @ts-ignore
    case "_debug_code": {
      const fn: (
        dataArgs: Record<string, ReadableStream<BraintrustStreamChunk>>,
        loopArgs: Record<string, ReadableStream<BraintrustStreamChunk>>,
        controlArgs: AsyncEventEmitterGroup,
        {
          cancelController,
          errorController,
        }: {
          cancelController: AbortController;
          errorController: AbortController;
        },
        // @ts-ignore
      ) => Record<string, ReadableStream<BraintrustStreamChunk>> = node.fn;
      return ({ dataArgs, loopArgs, controlArgs, span }) => ({
        data: fn(dataArgs, loopArgs, controlArgs, {
          cancelController,
          errorController,
        }),
        control: {},
      });
    }
    default: {
      const _: never = node;
      throw new Error(`Unsupported node type: ${_}`);
    }
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function multiplyStream(
  stream: ReadableStream<BraintrustStreamChunk>,
  multiplier: number,
): ReadableStream<BraintrustStreamChunk>[] {
  let currentStream = stream;
  const streams: ReadableStream<BraintrustStreamChunk>[] = [];
  for (let i = 0; i < multiplier - 1; i++) {
    const [streamCopy, streamCopy2] = currentStream.tee();
    streams.push(streamCopy2);
    currentStream = streamCopy;
  }
  streams.push(currentStream);
  return streams;
}

function assertSimpleOutput(nodeId: string, outputs: Set<string>) {
  if (outputs.size === 0) {
    return;
  }
  if (outputs.size > 1) {
    throw new Error(
      `Only one output is supported (${nodeId}: got ${Array.from(outputs).join(", ")})`,
    );
  }
  const output = outputs.values().next().value;
  if (output !== "output") {
    throw new Error(
      `Only the 'output' output is supported (${nodeId}: got '${output}')`,
    );
  }
}

export function makeLiteralStream(
  value: unknown,
  control: AsyncEventEmitterGroup,
  cancelController: AbortController,
): ReadableStream<BraintrustStreamChunk> {
  return new ReadableStream({
    async start(controller) {
      await control.ready(cancelController.signal);
      controller.enqueue({
        type: "json_delta",
        // It's important to ?? to null, because even if the value is undefined,
        // we want to ensure that the JSON representation is a string,
        // and JSON.stringify(undefined) = undefined, which is an invalid event.
        data: JSON.stringify(value ?? null),
      });
      controller.close();
    },
  });
}

// We should probably write this in a way that can be reused in the UI
export type OutputNodeArgs = {
  value: ReadableStream<BraintrustStreamChunk>;
};

function parseArgs<
  T extends Record<string, ReadableStream<BraintrustStreamChunk>>,
>(
  args: Record<string, ReadableStream<BraintrustStreamChunk>>,
  argNames: (keyof T)[],
): T {
  for (const name of argNames) {
    const value = args[String(name)];
    if (!value) {
      throw new BadRequestError(
        `Value for argument '${String(name)}' not found`,
      );
    }
  }
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return args as T;
}

interface InvokedNode {
  input: Record<string, WritableStream<BraintrustStreamChunk>>;
  control: Record<string, EventEmitter>;
  output: {
    data: Record<string, ReadableStream<BraintrustStreamChunk>>;
    control: Record<string, EventEmitter>;
  };
  span: Span;
}

interface VariableJoint {
  sourceVariable: string;
  targetVariable: string;
}

const DATASET_NAME = "dataset";

function invokeCompiledGraph({
  compiled,
  input,
  span: spanArg,
}: {
  compiled: CompiledGraph;
  input: ReadableStream<BraintrustStreamChunk>;
  span?: Span;
}): ReadableStream<BraintrustStreamChunk> {
  const rootSpan = spanArg ?? NOOP_SPAN;
  // Instantiate each node, and get the input/output streams
  const invokedNodes = new Map<string, InvokedNode>();
  for (const [nodeId, nodeFn] of compiled.nodes.entries()) {
    const input: Record<string, WritableStream<BraintrustStreamChunk>> = {};
    const dataArgs: Record<string, ReadableStream<BraintrustStreamChunk>> = {};
    const loopArgs: Record<string, ReadableStream<BraintrustStreamChunk>> = {};
    const controlEvents: Record<string, EventEmitter> = {};
    const inputVariables = compiled.edges.get(nodeId);
    if (!inputVariables) {
      throw new Error(`Node ${nodeId} is missing input variables`);
    }

    if (nodeId !== compiled.output.id) {
      const { readable: datasetReadable, writable: datasetWritable } =
        new TransformStream<BraintrustStreamChunk>();
      input[DATASET_NAME] = datasetWritable;
      dataArgs[DATASET_NAME] = datasetReadable;
    }

    const nodeParents = new Set<string>();
    for (const [
      variable,
      { source, purpose, isLoop },
    ] of inputVariables.entries()) {
      if (purpose === "control") {
        const controlEvent = new EventEmitter();
        controlEvents[variable] = controlEvent;
      } else if (purpose === "data" || purpose === "messages") {
        const { readable, writable } =
          new TransformStream<BraintrustStreamChunk>();
        input[variable] = writable;
        if (isLoop) {
          loopArgs[variable] = readable;
        } else {
          dataArgs[variable] = readable;
        }

        nodeParents.add(source.node);
      } else {
        const _: never = purpose;
        throw new Error(`Unknown purpose: ${_}`);
      }
    }

    const spanParents = Array.from(nodeParents).map(
      (parentId) => compiled.spanIds.get(parentId)!,
    );

    const span = rootSpan.startSpanWithParents(
      compiled.spanIds.get(nodeId)!,
      spanParents.length > 0 ? spanParents : [rootSpan.spanId],
      { name: nodeId },
    );
    const output = nodeFn({
      dataArgs,
      loopArgs,
      controlArgs: new AsyncEventEmitterGroup(Object.values(controlEvents)),
      span,
    });
    invokedNodes.set(nodeId, { input, control: controlEvents, output, span });
  }

  // Now, connect the input and output streams. First, construct a map of all the
  // targets for each source node.
  const sourceToTargets = new Map<
    string /* source node id */,
    Map<string /* target node id */, VariableJoint /* variable name */>
  >();
  const sourceToControl = new Map<
    string /* source node id */,
    Map<string /* target node id */, VariableJoint /* variable name */>
  >();
  for (const [targetNodeId, _] of invokedNodes.entries()) {
    const inputVariables = compiled.edges.get(targetNodeId);
    if (!inputVariables) {
      throw new Error(`Node ${targetNodeId} is missing input variables`);
    }
    for (const [variable, { source, purpose }] of inputVariables.entries()) {
      if (purpose === "control") {
        let targets = sourceToControl.get(source.node);
        if (!targets) {
          targets = new Map();
          sourceToControl.set(source.node, targets);
        }
        targets.set(targetNodeId, {
          sourceVariable: source.variable,
          targetVariable: variable,
        });
      } else if (purpose === "data" || purpose === "messages") {
        let targets = sourceToTargets.get(source.node);
        if (!targets) {
          targets = new Map();
          sourceToTargets.set(source.node, targets);
        }
        targets.set(targetNodeId, {
          sourceVariable: source.variable,
          targetVariable: variable,
        });
      } else {
        const _: never = purpose;
        throw new Error(`Unknown purpose: ${_}`);
      }
    }
  }

  // Then, duplicate (.tee()) the source node's output stream for each target.
  for (const [sourceNodeId, targets] of sourceToTargets.entries()) {
    const sourceNode = invokedNodes.get(sourceNodeId);
    if (!sourceNode) {
      throw new Error(`Source node ${sourceNodeId} not found`);
    }

    const targetEntries = Array.from(targets.entries());
    if (targetEntries.length === 0) {
      continue;
    }

    const sourceStreams = sourceNode.output.data;
    for (let i = 0; i < targetEntries.length - 1; i++) {
      const [targetNodeId, joint] = targetEntries[i];
      const targetNode = invokedNodes.get(targetNodeId);
      if (!targetNode) {
        throw new Error(`Target node ${targetNodeId} not found`);
      }

      const sourceStream = sourceStreams[joint.sourceVariable];
      if (!sourceStream) {
        throw new BadRequestError(
          `Source variable ${joint.sourceVariable} not found in source node ${sourceNodeId}`,
        );
      }

      const [sourceCopy1, sourceCopy2] = sourceStream.tee();
      sourceStreams[joint.sourceVariable] = sourceCopy1;
      sourceCopy2
        .pipeTo(targetNode.input[joint.targetVariable])
        .catch((error) => {
          compiled.errorController.abort(error);
        });
    }

    const [targetNodeId, variable] = targetEntries[targetEntries.length - 1];
    const sourceStream = sourceStreams[variable.sourceVariable];
    if (!sourceStream || typeof sourceStream !== "object") {
      throw new BadRequestError(
        `Source variable ${variable.sourceVariable} not found in source node ${sourceNodeId}`,
      );
    }
    sourceStream
      .pipeTo(invokedNodes.get(targetNodeId)!.input[variable.targetVariable])
      .catch((error) => {
        compiled.errorController.abort(error);
      });
  }

  // Duplicate the input stream for each node, so it can be piped as 'dataset' to each.
  const datasetStreams = teeMultiple(input, invokedNodes.size);
  datasetStreams[0].pipeTo(compiled.input.stream).catch((error) => {
    compiled.errorController.abort(error);
  });
  let nodeIndex = 1;
  for (const [nodeId, node] of invokedNodes.entries()) {
    if (nodeId === compiled.output.id) {
      continue;
    }

    const datasetStream = datasetStreams[nodeIndex];
    datasetStream.pipeTo(node.input[DATASET_NAME]).catch((error) => {
      compiled.errorController.abort(error);
    });
    nodeIndex++;
  }

  for (const [sourceNodeId, controlTargets] of sourceToControl.entries()) {
    const sourceNode = invokedNodes.get(sourceNodeId);
    if (!sourceNode) {
      throw new Error(`Source node ${sourceNodeId} not found`);
    }
    const sourceControl = sourceNode.output.control;

    const targetEntries = Array.from(controlTargets.entries());
    if (targetEntries.length === 0) {
      continue;
    }
    for (let i = 0; i < targetEntries.length; i++) {
      const [targetNodeId, joint] = targetEntries[i];
      const targetNode = invokedNodes.get(targetNodeId);
      if (!targetNode) {
        throw new Error(`Target node ${targetNodeId} not found`);
      }
      const sourceControlEvent = sourceControl[joint.sourceVariable];
      if (!sourceControlEvent) {
        throw new Error(
          `Source control-flow variable ${joint.sourceVariable} not found in source node ${sourceNodeId}`,
        );
      }

      const targetControlEvent = targetNode.control[joint.targetVariable];
      if (!targetControlEvent) {
        throw new Error(
          `Target control-flow variable ${joint.targetVariable} not found in target node ${targetNodeId}`,
        );
      }
      sourceControlEvent.on("ready", () => {
        targetControlEvent.emit("ready");
      });
      sourceControlEvent.on("abort", () => {
        targetControlEvent.emit("abort");
      });
    }
  }

  // There are two ways this can end:
  // 1. The output stream returns a value
  // 2. The abortController is aborted
  //
  // We want to return a stream that emits the output value if it exists, and
  // converts the abort controller error into a BraintrustStreamChunk error
  // if it errors.
  return new ReadableStream<BraintrustStreamChunk>({
    async start(controller) {
      await Promise.race([
        (async () => {
          const outputReader = compiled.output.stream.getReader();
          while (true) {
            const { done, value } = await outputReader.read();
            if (value) {
              controller.enqueue(value);
            }
            if (done) {
              controller.close();
              compiled.cancelController.abort();
              break;
            }
          }
        })(),
        new Promise((resolve, _reject) => {
          compiled.errorController.signal.addEventListener("abort", () => {
            if (compiled.cancelController.signal.aborted) {
              resolve(null);
              return;
            }
            const error = compiled.errorController.signal.reason;
            controller.enqueue({
              type: "error",
              data: JSON.stringify(`${error}`),
            });
            controller.close();
            resolve(null);
          });
        }),
      ]);
    },
  });
}

// Add this with the other type definitions
export type IfNodeArgs = {
  condition: ReadableStream<BraintrustStreamChunk>;
  true: ReadableStream<BraintrustStreamChunk>;
  false: ReadableStream<BraintrustStreamChunk>;
};

export type GateNodeArgs = {
  input: ReadableStream<BraintrustStreamChunk>;
};

export async function consumeStreamValue(
  stream: ReadableStream<BraintrustStreamChunk>,
  { signal }: { signal: AbortSignal },
): Promise<unknown> {
  const btStream = new BraintrustStream(stream, { signal });
  return await btStream.finalValue();
}

export async function pipeToController<T>(
  stream: ReadableStream<T>,
  controller: ReadableStreamDefaultController<T>,
  { end = true }: { end?: boolean } = {},
) {
  const reader = stream.getReader();
  while (true) {
    const { done, value } = await reader.read();
    if (done) {
      if (end) {
        controller.close();
      }
      break;
    }
    controller.enqueue(value);
  }
}

function teeMultiple<T>(
  stream: ReadableStream<T>,
  count: number,
): ReadableStream<T>[] {
  if (count <= 0) {
    throw new Error("Count must be greater than 0");
  }
  if (count === 1) {
    return [stream];
  }
  if (count === 2) {
    return stream.tee();
  }

  const [first, second] = stream.tee();
  return [first, ...teeMultiple(second, count - 1)];
}

function restartSpan(span: Span) {
  span.log({ metrics: { start: getCurrentUnixTimestamp() } });
}

function makeAnyStream(
  streams: ReadableStream<BraintrustStreamChunk>[],
  cancelController: AbortController,
): ReadableStream<BraintrustStreamChunk> {
  return new ReadableStream<BraintrustStreamChunk>({
    async start(controller) {
      const readers = streams.map((stream) => stream.getReader());
      const abortSignal = cancelController.signal;

      // Function to handle abort
      const onAbort = () => {
        controller.error(new Error("Stream operation was aborted"));
        readers.forEach((reader) => reader.cancel());
      };

      // Listen for abort event
      abortSignal.addEventListener("abort", onAbort);

      try {
        const readPromises = readers.map((reader, index) =>
          reader.read().then((result) => ({ index, result })),
        );

        const { index, result } = await Promise.race(readPromises);
        if (!result.done) {
          controller.enqueue(result.value);
        }

        const reader = readers[index];
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          controller.enqueue(value);
        }
      } catch (error) {
        if (abortSignal.aborted) {
          getLogger().info("Stream operation was aborted");
        } else {
          getLogger().error({ error }, "Error in stream");
        }
      } finally {
        abortSignal.removeEventListener("abort", onAbort);
        await Promise.all(readers.map((reader) => reader.releaseLock()));
        controller.close();
      }
    },
  });
}

export function createLoopValueStream<T extends BraintrustStreamChunk>(
  onValue: (result: unknown) => void,
  onError: (error: unknown) => void,
): TransformStream<T, BraintrustStreamChunk> {
  let textChunks: string[] = [];
  let jsonChunks: string[] = [];
  let reasoningChunks: string[] = [];

  const flushValue = () => {
    if (jsonChunks.length > 0) {
      // If we received both text and json deltas in the same stream, we
      // only return the json delta
      onValue(JSON.parse(jsonChunks.join("")));
    } else if (reasoningChunks.length > 0) {
      onValue(reasoningChunks.join(""));
    } else if (textChunks.length > 0) {
      onValue(textChunks.join(""));
    }

    textChunks = [];
    jsonChunks = [];
    reasoningChunks = [];
  };

  const transformStream = new TransformStream<T, BraintrustStreamChunk>({
    transform(chunk, controller) {
      const chunkType = chunk.type;
      switch (chunkType) {
        case "text_delta":
          textChunks.push(chunk.data);
          break;
        case "reasoning_delta":
          reasoningChunks.push(chunk.data);
          break;
        case "json_delta":
          jsonChunks.push(chunk.data);
          break;
        case "error":
          onError(chunk.data);
          break;
        case "progress":
        case "start":
        case "done":
        case "console":
          flushValue();
          break;
        default:
          const _type: never = chunkType;
          onError(`Unknown chunk type: ${_type}`);
      }
      controller.enqueue(chunk);
    },
    flush(controller) {
      flushValue();
      controller.terminate();
    },
  });

  return transformStream;
}

export class AsyncEventEmitterGroup {
  public constructor(private emitters: EventEmitter[]) {}

  public async ready(signal: AbortSignal): Promise<void> {
    if (this.emitters.length === 0) {
      return;
    }
    await Promise.race(
      this.emitters.map(
        (e) =>
          new Promise((resolve, reject) => {
            EventEmitter.once(e, "ready", { signal })
              .then(resolve)
              .catch(reject);
          }),
      ),
    );
  }

  public isEmpty(): boolean {
    return this.emitters.length === 0;
  }
}

function bindBtqlExpression(expression: string, argsNames: string[]): Field {
  const parser = new Parser(expression);
  const parsed = parser.parseExpr();
  const bindCtx: BindContext = {
    schema: {
      type: "object",
      properties: Object.fromEntries(argsNames.map((name) => [name, {}])),
    },
    scope: {},
    queryText: expression,
    bindingMeasures: false,
    skipFieldCasts: true,
  };
  const bound = bindExpr(bindCtx, parsed);
  if (bound.op !== "field") {
    throw new Error("Only field access is supported in BTQL nodes");
  }
  return bound;
}

function evaluateBtqlExpression(expr: Field, args: Record<string, unknown>) {
  const field = expr.name;

  const [root, ...rest] = field;
  let rootValue = args[root];
  if (rootValue === undefined) {
    throw new Error(`Variable ${root} not found`);
  }

  for (const field of rest) {
    if (typeof field === "string" && typeof rootValue === "object") {
      if (rootValue) {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        rootValue = (rootValue as Record<string, unknown>)[field];
      }
    } else if (typeof field === "number" && Array.isArray(rootValue)) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      rootValue = (rootValue as unknown[])[field];
    } else {
      throw new Error(`Unsupported field access: ${field}`);
    }
  }

  return rootValue;
}

async function consumeArgs(
  rawArgs: Record<string, ReadableStream<BraintrustStreamChunk>>,
  cancelController: AbortController,
): Promise<Record<string, unknown>> {
  return Object.fromEntries(
    await Promise.all(
      Object.entries(rawArgs).map(async ([key, value]) => [
        key,
        await consumeStreamValue(value, {
          signal: cancelController.signal,
        }),
      ]),
    ),
  );
}

function prepareInputArgs(args: Record<string, unknown>): unknown {
  if (Object.keys(args).length === 1 && args.input) {
    // By convention, to make prompt templating and stuff easier, extract the one-and-only
    // 'input' argument.
    return args.input;
  }

  if (
    Object.keys(args).length === 2 &&
    args.dataset &&
    args.input &&
    isEqual(args.dataset, args.input)
  ) {
    // If the dataset is the same as the input, nest the dataset inside the input.
    // This is effectively a special case for the first prompt node in a graph,
    // where we want to be able to access {{input.foo}}, {{metadata}}, {{expected}},
    // etc., as well as {{dataset.input}}, etc, which is the convention for global access.
    return { ...args.input, dataset: args.dataset };
  }

  return args;
}
