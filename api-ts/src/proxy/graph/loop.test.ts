import { findLoops } from "./loop";
import { expect, test, describe } from "vitest";

describe("findLoops", () => {
  test("should detect all edges in a cycle for the evaluator_optimizer graph", () => {
    // Simplified version of the graph structure from evaluator_optimizer.ts
    const graph = {
      type: "graph",
      nodes: {
        "node-in": { type: "input" },
        "node-out": { type: "output" },
        "node-generator": { type: "prompt" },
        "node-evaluator": { type: "prompt" },
        "node-done-gate": { type: "gate" },
        "node-message-maker": { type: "prompt-template" },
      },
      edges: {
        "edge-1": {
          source: { node: "node-in", variable: "output" },
          target: { node: "node-generator", variable: "problem" },
          purpose: "data",
        },
        "edge-2": {
          source: { node: "node-generator", variable: "output" },
          target: { node: "node-evaluator", variable: "solution" },
          purpose: "data",
        },
        "edge-3": {
          source: { node: "node-evaluator", variable: "output" },
          target: { node: "node-done-gate", variable: "input" },
          purpose: "data",
        },
        "edge-4": {
          source: { node: "node-done-gate", variable: "true" },
          target: { node: "node-out", variable: "input" },
          purpose: "data",
        },
        "edge-5": {
          source: { node: "node-evaluator", variable: "output" },
          target: { node: "node-message-maker", variable: "critique" },
          purpose: "data",
        },
        "edge-6": {
          source: { node: "node-done-gate", variable: "false" },
          target: { node: "node-message-maker", variable: "control" },
          purpose: "control",
        },
        // This edge creates the loop - from message maker back to generator
        "edge-7": {
          source: { node: "node-message-maker", variable: "output" },
          target: { node: "node-generator", variable: "messages" },
          purpose: "messages",
        },
      },
    };

    const loops = findLoops(graph);

    // The algorithm detected this cycle:
    // edge-2: generator -> evaluator
    // edge-3: evaluator -> done-gate
    // edge-6: done-gate -> message-maker
    // edge-7: message-maker -> generator
    const expectedEdges = ["edge-2", "edge-3", "edge-6", "edge-7"];

    // We expect to find all edges in the cycle
    expect(loops).toEqual(new Set(expectedEdges));
  });

  test("should return empty array for a graph with no loops", () => {
    // Same graph as above but without the loop-creating edge
    const graphWithoutLoop = {
      type: "graph",
      nodes: {
        "node-in": { type: "input" },
        "node-out": { type: "output" },
        "node-generator": { type: "prompt" },
        "node-evaluator": { type: "prompt" },
        "node-done-gate": { type: "gate" },
        "node-message-maker": { type: "prompt-template" },
      },
      edges: {
        "edge-1": {
          source: { node: "node-in", variable: "output" },
          target: { node: "node-generator", variable: "problem" },
          purpose: "data",
        },
        "edge-2": {
          source: { node: "node-generator", variable: "output" },
          target: { node: "node-evaluator", variable: "solution" },
          purpose: "data",
        },
        "edge-3": {
          source: { node: "node-evaluator", variable: "output" },
          target: { node: "node-done-gate", variable: "input" },
          purpose: "data",
        },
        "edge-4": {
          source: { node: "node-done-gate", variable: "true" },
          target: { node: "node-out", variable: "input" },
          purpose: "data",
        },
        "edge-5": {
          source: { node: "node-evaluator", variable: "output" },
          target: { node: "node-message-maker", variable: "critique" },
          purpose: "data",
        },
        "edge-6": {
          source: { node: "node-done-gate", variable: "false" },
          target: { node: "node-message-maker", variable: "control" },
          purpose: "control",
        },
        // No edge from message-maker back to generator
      },
    };

    const loops = findLoops(graphWithoutLoop);
    expect(loops).toEqual(new Set());
  });

  test("should detect all edges in multiple cycles", () => {
    const graphWithMultipleLoops = {
      type: "graph",
      nodes: {
        "node-a": { type: "prompt" },
        "node-b": { type: "prompt" },
        "node-c": { type: "prompt" },
        "node-d": { type: "prompt" },
      },
      edges: {
        "edge-a-b": {
          source: { node: "node-a", variable: "output" },
          target: { node: "node-b", variable: "input" },
          purpose: "data",
        },
        "edge-b-c": {
          source: { node: "node-b", variable: "output" },
          target: { node: "node-c", variable: "input" },
          purpose: "data",
        },
        "edge-c-a": {
          // First loop: A -> B -> C -> A
          source: { node: "node-c", variable: "output" },
          target: { node: "node-a", variable: "input" },
          purpose: "data",
        },
        "edge-b-d": {
          source: { node: "node-b", variable: "output" },
          target: { node: "node-d", variable: "input" },
          purpose: "data",
        },
        "edge-d-b": {
          // Second loop: B -> D -> B
          source: { node: "node-d", variable: "output" },
          target: { node: "node-b", variable: "input" },
          purpose: "data",
        },
      },
    };

    const loops = findLoops(graphWithMultipleLoops);

    // First cycle: A -> B -> C -> A (edges: edge-a-b, edge-b-c, edge-c-a)
    // Second cycle: B -> D -> B (edges: edge-b-d, edge-d-b)
    const expectedEdges = [
      "edge-a-b",
      "edge-b-c",
      "edge-c-a", // First cycle
      "edge-b-d",
      "edge-d-b", // Second cycle
    ];

    // We expect to find all edges in both cycles
    expect(loops).toEqual(new Set(expectedEdges));
  });

  test("should detect overlapping cycles", () => {
    const graphWithOverlappingCycles = {
      type: "graph",
      nodes: {
        "node-a": { type: "prompt" },
        "node-b": { type: "prompt" },
        "node-c": { type: "prompt" },
        "node-d": { type: "prompt" },
        "node-e": { type: "prompt" },
      },
      edges: {
        "edge-a-b": {
          source: { node: "node-a", variable: "output" },
          target: { node: "node-b", variable: "input" },
          purpose: "data",
        },
        "edge-b-c": {
          source: { node: "node-b", variable: "output" },
          target: { node: "node-c", variable: "input" },
          purpose: "data",
        },
        "edge-c-a": {
          // First cycle: A -> B -> C -> A
          source: { node: "node-c", variable: "output" },
          target: { node: "node-a", variable: "input" },
          purpose: "data",
        },
        "edge-b-d": {
          source: { node: "node-b", variable: "output" },
          target: { node: "node-d", variable: "input" },
          purpose: "data",
        },
        "edge-d-e": {
          source: { node: "node-d", variable: "output" },
          target: { node: "node-e", variable: "input" },
          purpose: "data",
        },
        "edge-e-b": {
          // Second cycle: B -> D -> E -> B
          source: { node: "node-e", variable: "output" },
          target: { node: "node-b", variable: "input" },
          purpose: "data",
        },
      },
    };

    const loops = findLoops(graphWithOverlappingCycles);

    // First cycle: A -> B -> C -> A (edges: edge-a-b, edge-b-c, edge-c-a)
    // Second cycle: B -> D -> E -> B (edges: edge-b-d, edge-d-e, edge-e-b)
    // Note that node B is shared between both cycles
    const expectedEdges = [
      "edge-a-b",
      "edge-b-c",
      "edge-c-a", // First cycle
      "edge-b-d",
      "edge-d-e",
      "edge-e-b", // Second cycle
    ];

    expect(loops).toEqual(new Set(expectedEdges));
  });

  test("should detect self-loops", () => {
    const graphWithSelfLoop = {
      type: "graph",
      nodes: {
        "node-a": { type: "prompt" },
        "node-b": { type: "prompt" },
        "node-c": { type: "prompt" },
      },
      edges: {
        "edge-a-b": {
          source: { node: "node-a", variable: "output" },
          target: { node: "node-b", variable: "input" },
          purpose: "data",
        },
        "edge-b-c": {
          source: { node: "node-b", variable: "output" },
          target: { node: "node-c", variable: "input" },
          purpose: "data",
        },
        "edge-self-loop": {
          // Self-loop on node A
          source: { node: "node-a", variable: "output" },
          target: { node: "node-a", variable: "input" },
          purpose: "data",
        },
      },
    };

    const loops = findLoops(graphWithSelfLoop);

    // Only the self-loop edge should be detected
    expect(loops).toEqual(new Set(["edge-self-loop"]));
  });

  test("should detect a complex graph with multiple interconnected cycles", () => {
    const complexGraph = {
      type: "graph",
      nodes: {
        "node-1": { type: "prompt" },
        "node-2": { type: "prompt" },
        "node-3": { type: "prompt" },
        "node-4": { type: "prompt" },
        "node-5": { type: "prompt" },
        "node-6": { type: "prompt" },
      },
      edges: {
        "edge-1-2": {
          source: { node: "node-1", variable: "output" },
          target: { node: "node-2", variable: "input" },
          purpose: "data",
        },
        "edge-2-3": {
          source: { node: "node-2", variable: "output" },
          target: { node: "node-3", variable: "input" },
          purpose: "data",
        },
        "edge-3-1": {
          // Cycle 1: 1 -> 2 -> 3 -> 1
          source: { node: "node-3", variable: "output" },
          target: { node: "node-1", variable: "input" },
          purpose: "data",
        },
        "edge-3-4": {
          source: { node: "node-3", variable: "output" },
          target: { node: "node-4", variable: "input" },
          purpose: "data",
        },
        "edge-4-5": {
          source: { node: "node-4", variable: "output" },
          target: { node: "node-5", variable: "input" },
          purpose: "data",
        },
        "edge-5-3": {
          // Cycle 2: 3 -> 4 -> 5 -> 3
          source: { node: "node-5", variable: "output" },
          target: { node: "node-3", variable: "input" },
          purpose: "data",
        },
        "edge-4-6": {
          source: { node: "node-4", variable: "output" },
          target: { node: "node-6", variable: "input" },
          purpose: "data",
        },
        "edge-6-4": {
          // Cycle 3: 4 -> 6 -> 4
          source: { node: "node-6", variable: "output" },
          target: { node: "node-4", variable: "input" },
          purpose: "data",
        },
        "edge-5-2": {
          // Additional edge creating another cycle: 2 -> 3 -> 4 -> 5 -> 2
          source: { node: "node-5", variable: "output" },
          target: { node: "node-2", variable: "input" },
          purpose: "data",
        },
      },
    };

    const loops = findLoops(complexGraph);

    // All cycle-forming edges
    const expectedEdges = [
      "edge-1-2",
      "edge-2-3",
      "edge-3-1", // Cycle 1
      "edge-3-4",
      "edge-4-5",
      "edge-5-3", // Cycle 2
      "edge-4-6",
      "edge-6-4", // Cycle 3
      "edge-5-2", // Part of additional cycle
    ];

    expect(loops).toEqual(new Set(expectedEdges));
  });
});
