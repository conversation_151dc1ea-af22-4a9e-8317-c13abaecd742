import { describe, it, expect } from "vitest";
import { getInvokeErrorCtx, setInvokeErrorCtx } from "./functions";

describe("invoke error context", () => {
  it("returns undefined when no context set", () => {
    const err = new Error("no ctx");
    expect(getInvokeErrorCtx(err)).toBeUndefined();
  });

  it("returns stored context for the same error instance", () => {
    const err = new Error("with ctx");
    setInvokeErrorCtx(err, "proj_123", "fn_456");
    const ctx = getInvokeErrorCtx(err);
    expect(ctx).toEqual({ projectId: "proj_123", functionId: "fn_456" });
  });

  it("does not leak between different errors", () => {
    const err1 = new Error("one");
    const err2 = new Error("two");
    setInvokeErrorCtx(err1, "proj_A", "fn_A");
    expect(getInvokeErrorCtx(err2)).toBeUndefined();
  });
});
