import {
  deriveFunctionFormat,
  deriveFunctionObjectType,
  deriveFunctionOutputType,
  InvocableFunction,
  InvokeResponse,
  ParallelToolCall,
  parallelToolCallSchema,
  serializeSSEEvent,
} from "@braintrust/local/functions";
import {
  createParser,
  EventSourceParser,
  ParsedEvent,
  ReconnectInterval,
} from "eventsource-parser";
import {
  callEventSchema,
  chatCompletionMessageToolCallSchema,
  FunctionId,
  StreamingMode,
  SSEProgressEventData,
  Message,
  CallEventSchema,
  sseProgressEventDataSchema,
} from "@braintrust/typespecs";
import { Span } from "braintrust";
import { z } from "zod";
import { cachedUseFunction, functionSpanType } from "./functions";
import { callFunctionWrapper, invokeFunction } from "./call";
import { getLogger } from "../instrumentation/logger";

export function createToolCallbackStream({
  isFail,
  span,
  baseFunction,
  callableTools,
  functionManager,
  input,
  messages,
  maxRoundtrips,
  strict,
  isJSONResponse,
}: {
  isFail: () => boolean;
  span: Span;
  baseFunction: InvocableFunction;
  callableTools: Record<string, FunctionId>;
  functionManager: FunctionManager;
  input: unknown;
  messages: Message[] | undefined;
  maxRoundtrips: number;
  strict: boolean | undefined;
  isJSONResponse: boolean;
}) {
  const pino = getLogger().child({
    spanId: span.id,
    functionName: baseFunction.name,
    task: "tool_stream",
  });
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  let eventSourceParser: EventSourceParser;

  const textChunks: string[] = [];
  const toolJSONChunks: string[] = [];

  const progressFunctionType = deriveFunctionProgressInfo({
    func: baseFunction,
    span,
  });

  let isDone = false;
  let isTerminated = false;
  const sendDone = (
    controller: TransformStreamDefaultController,
    includeDoneEvent: boolean = true,
  ) => {
    if (isDone) {
      return;
    }
    isDone = true;
    if (includeDoneEvent) {
      controller.enqueue(
        encoder.encode(
          serializeSSEEvent({
            event: "done",
            data: "",
          }),
        ),
      );
    }

    (async () => {
      // You cannot call controller.terminate() in the same callstack as
      // transform(). This await ensures that we're no longer in that
      // callstack.
      await new Promise((resolve) => setTimeout(resolve, 0));
      // In case there's a race, be extra extra careful about calling this.
      if (!isTerminated) {
        controller.terminate();
        isTerminated = true;
      }
    })().catch((e) => {
      pino.error({ error: e }, "Error terminating stream");
    });
  };

  const sendProgress = (
    controller: TransformStreamDefaultController,
    progress: SSEProgressEventData,
  ) =>
    controller.enqueue(
      encoder.encode(
        serializeSSEEvent({
          event: "progress",
          data: JSON.stringify(progress),
        }),
      ),
    );

  const onDone = (controller: TransformStreamDefaultController) => {
    if (isDone) {
      return;
    }
    let parsedTools: ParallelToolCall[] = [];
    try {
      parsedTools = z
        .array(parallelToolCallSchema)
        .parse(
          toolJSONChunks.length > 0 ? JSON.parse(toolJSONChunks.join("")) : [],
        );
    } catch (err) {
      // If this isn't parallel tool calls, it maybe a structured output response.
      if (isJSONResponse) {
        for (const chunk of toolJSONChunks) {
          controller.enqueue(
            encoder.encode(
              serializeSSEEvent({
                event: "json_delta",
                data: chunk,
              }),
            ),
          );
        }
        sendDone(controller);
        return;
      }
      pino.error({ error: err }, "Failed to parse tools");
      controller.enqueue(
        encoder.encode(
          serializeSSEEvent({
            event: "error",
            data: JSON.stringify(`${err}`),
          }),
        ),
      );
    }

    if (parsedTools.length === 0) {
      sendDone(controller);
      return;
    }

    const numCallableTools = parsedTools.filter(
      (tool) => callableTools[tool.function_name],
    ).length;
    const numNonCallableTools = parsedTools.length - numCallableTools;

    // If all of the tools are non-callable, we can just forward along the result.
    if (numCallableTools === 0 && numNonCallableTools > 0) {
      for (const chunk of toolJSONChunks) {
        controller.enqueue(
          encoder.encode(
            serializeSSEEvent({
              event: "json_delta",
              data: chunk,
            }),
          ),
        );
      }
      sendDone(controller);
      return;
    } else if (numCallableTools > 0 && numNonCallableTools > 0) {
      controller.enqueue(
        encoder.encode(
          serializeSSEEvent({
            event: "error",
            data: JSON.stringify(
              `Received both callable and non-callable tools. If you are trying to format ` +
                `your output, try using only callable tools and a response format instead.`,
            ),
          }),
        ),
      );
      sendDone(controller);
      return;
    }

    continueWithTools({
      assistantText: textChunks.length > 0 ? textChunks.join("") : undefined,
      baseFunction,
      tools: parsedTools,
      callableTools,
      functionManager,
      span,
      input,
      messages,
      maxRoundtrips,
      strict,
      sendProgress: (progress) => sendProgress(controller, progress),
    })
      .then(async (newStream) => {
        const reader = newStream.getReader();
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          controller.enqueue(value);
        }
      })
      .catch((err) => {
        pino.error({ error: err }, "Failed to call tools");
        controller.enqueue(
          encoder.encode(
            serializeSSEEvent({
              event: "error",
              data: JSON.stringify(`${err}`),
            }),
          ),
        );
      })
      .finally(() => {
        sendDone(controller);
      });
  };

  return new TransformStream<Uint8Array, Uint8Array>({
    start(controller) {
      eventSourceParser = createParser(
        (event: ParsedEvent | ReconnectInterval) => {
          if (event.type === "reconnect-interval") {
            return;
          }

          const parsed = callEventSchema.safeParse(event);
          if (!parsed.success) {
            pino.warn({ event, error: parsed.error }, "Failed to parse event");
            return;
          }

          const data = parsed.data.data;
          switch (parsed.data.event) {
            case "text_delta":
              // TODO This case is sort of interesting. If the model starts returning some text,
              // and it either _later_ sends a tool call, or has already sent a tool call, we don't
              // have a great thing to do, other than to just forward along the text (and concatenate
              // all of it at once). Ideally, though, if the message corresponds to a tool call, this
              // text never makes it to the client (and is used to power the tool calls themselves).
              textChunks.push(JSON.parse(data));
              controller.enqueue(encoder.encode(serializeSSEEvent(event)));
              break;
            case "json_delta": {
              sendProgress(controller, {
                ...progressFunctionType,
                event: "json_delta",
                data,
              });
              toolJSONChunks.push(data);
              break;
            }
            case "console":
              sendProgress(controller, {
                ...progressFunctionType,
                event: "console",
                data,
              });
              break;
            case "start":
            case "error":
            case "reasoning_delta":
            case "progress":
              controller.enqueue(encoder.encode(serializeSSEEvent(event)));
              break;
            case "done": {
              onDone(controller);
              break;
            }
            default:
              const _ignore: never = parsed.data;
              throw new Error(`Unknown event type: ${parsed.data}`);
          }
        },
      );
    },
    transform(chunk, controller) {
      if (isFail()) {
        if (!isDone) {
          controller.enqueue(chunk);
          sendDone(controller, false /* includeDoneEvent */);
        }
      } else {
        eventSourceParser.feed(decoder.decode(chunk));
      }
    },
    flush(controller) {
      onDone(controller);
    },
  });
}

export function createCleanupToolFormatStream({
  isFail,
  mode,
}: {
  isFail: () => boolean;
  mode: StreamingMode;
}) {
  const pino = getLogger().child({
    task: "cleanup_tool_format_stream",
  });
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  let eventSourceParser: EventSourceParser;

  const jsonChunks: string[] = [];
  let sentDone = false;

  return new TransformStream<Uint8Array, Uint8Array>({
    start(controller) {
      eventSourceParser = createParser(
        (event: ParsedEvent | ReconnectInterval) => {
          if (event.type === "reconnect-interval") {
            return;
          }

          const parsed = callEventSchema.safeParse(event);
          if (!parsed.success) {
            pino.warn({ event, error: parsed.error }, "Failed to parse event");
            return;
          }

          if (sentDone) {
            if (parsed.data.event === "done") {
              return;
            } else {
              // It's possible to receive multiple "done" events, because we recursively just
              // forward along the events from the inner tool stream. But once the dones start
              // flowing, nothing else should be coming in.
              throw new Error("Received event after done");
            }
          }

          switch (parsed.data.event) {
            case "text_delta":
            case "progress":
            case "error":
            case "start":
            case "console":
            case "reasoning_delta":
              controller.enqueue(encoder.encode(serializeSSEEvent(event)));
              break;
            case "json_delta":
              jsonChunks.push(parsed.data.data);
              break;
            case "done": {
              if (jsonChunks.length > 0) {
                const jsonData = JSON.parse(jsonChunks.join(""));
                if (Array.isArray(jsonData)) {
                  for (const chunk of jsonData) {
                    delete chunk["tool_call_id"];
                  }
                  controller.enqueue(
                    encoder.encode(
                      serializeSSEEvent({
                        event: "json_delta",
                        data: JSON.stringify(
                          mode === "parallel"
                            ? jsonData
                            : jsonData[0].arguments,
                        ),
                      }),
                    ),
                  );
                } else {
                  for (const chunk of jsonChunks) {
                    controller.enqueue(
                      encoder.encode(
                        serializeSSEEvent({
                          event: "json_delta",
                          data: chunk,
                        }),
                      ),
                    );
                  }
                }
              }
              controller.enqueue(
                encoder.encode(
                  serializeSSEEvent({
                    event: "done",
                    data: "",
                  }),
                ),
              );
              sentDone = true;
              break;
            }
            default:
              const _: never = parsed.data;
              throw new Error(`Unknown event type: ${parsed.data}`);
          }
        },
      );
    },
    transform(chunk, controller) {
      if (isFail()) {
        controller.enqueue(chunk);
      } else {
        eventSourceParser.feed(decoder.decode(chunk));
      }
    },
  });
}

async function continueWithTools({
  assistantText,
  tools,
  callableTools,
  functionManager,
  baseFunction,
  span,
  input,
  messages,
  maxRoundtrips,
  strict,
  sendProgress,
}: {
  assistantText: string | undefined;
  tools: ParallelToolCall[];
  callableTools: Record<string, FunctionId>;
  functionManager: FunctionManager;
  baseFunction: InvocableFunction;
  span: Span;
  input: unknown;
  messages: Message[] | undefined;
  maxRoundtrips: number;
  strict: boolean | undefined;
  sendProgress: (progress: SSEProgressEventData) => void;
}) {
  if (tools.length === 0) {
    return new ReadableStream({
      async start(controller) {
        // This is a dumb hack. It turns out that streams cannot call controller.terminate() in the
        // same callstack as `transform`, so we need to schedule it via a trivial promise.
        await new Promise((resolve) => setTimeout(resolve, 0));
        controller.close();
      },
    });
  }

  const toolResults = await callTools({
    tools,
    callableTools,
    functionManager,
    span,
    sendProgress,
  });

  if (
    baseFunction.function_data.type !== "prompt" ||
    baseFunction.prompt_data?.prompt?.type !== "chat"
  ) {
    throw new Error("Only chat prompts are supported");
  }

  const subSpan = span.startSpan({
    type: functionSpanType(baseFunction),
    name: baseFunction.name,
    spanAttributes: {
      remote: true,
    },
    event: {
      input,
    },
  });

  return await functionManager.chainFunction({
    func: {
      ...baseFunction,
      // Override the prompt_data to make tool_choice "auto" for subsequent calls.
      prompt_data: {
        ...baseFunction.prompt_data,
        options: {
          ...baseFunction.prompt_data?.options,
          params: {
            ...baseFunction.prompt_data?.options?.params,
            tool_choice: "auto",
          },
        },
      },
    },
    span: subSpan,
    input,
    messages: [
      ...(messages ?? []),
      {
        role: "assistant",
        content: assistantText,
        tool_calls: tools.map(
          (tool): z.infer<typeof chatCompletionMessageToolCallSchema> => ({
            id: tool.tool_call_id,
            type: "function",
            function: {
              name: tool.function_name,
              arguments: JSON.stringify(tool.arguments),
            },
          }),
        ),
      },
      ...toolResults.map((tool, idx) => ({
        role: "tool" as const,
        content: JSON.stringify(tool),
        tool_call_id: tools[idx].tool_call_id,
      })),
    ],
    maxRoundtrips,
    strict,
    sendProgress,
    mode: "parallel",
  });
}

async function callTools({
  tools,
  callableTools,
  functionManager,
  span,
  sendProgress,
}: {
  tools: ParallelToolCall[];
  callableTools: Record<string, FunctionId>;
  functionManager: FunctionManager;
  span: Span;
  sendProgress: (progress: SSEProgressEventData) => void;
}) {
  const toolCalls = tools.map(async (tool) => {
    const functionId = callableTools[tool.function_name];
    if (!functionId) {
      throw new Error(`Function ${tool.function_name} not found`);
    }
    const func =
      functionManager.usedFunctions[tool.function_name] ??
      (await functionManager.useFunction({ func: functionId }));
    if (!functionManager.usedFunctions[tool.function_name]) {
      functionManager.usedFunctions[tool.function_name] = func;
    }

    const ret = await functionManager.callFunction({
      func: {
        ...func,
        name: tool.function_name,
      },
      input: tool.arguments,
      messages: undefined,
      strict: undefined,
      span,
      sendProgress,
    });

    return ret;
  });

  return await Promise.all(toolCalls);
}

export class FunctionManager {
  usedFunctions: Record<string, InvocableFunction> = {};
  constructor(
    private args: {
      ctxToken: string | undefined;
      orgName: string | undefined;
      orgId: string | undefined;
      proxyUrl: string;
      appOrigin: string;
      timeoutMs: number | undefined;
    },
  ) {}

  public useFunction(args: { func: FunctionId }): Promise<InvocableFunction> {
    return cachedUseFunction({
      body: args.func,
      appOrigin: this.args.appOrigin,
      ctxToken: this.args.ctxToken,
      orgName: this.args.orgName,
      setHeader: () => {},
    });
  }

  public async callFunction({
    func,
    input,
    messages,
    strict,
    span,
    sendProgress,
  }: {
    func: InvocableFunction;
    input: unknown;
    messages: Message[] | undefined;
    strict: boolean | undefined;
    span: Span;
    sendProgress: (progress: SSEProgressEventData) => void;
  }): Promise<unknown> {
    return await span.traced(
      async (span) => {
        const progressEventBase = deriveFunctionProgressInfo({
          func,
          span,
        });

        // TODO: We should probably start sending this for all invoke calls (because of CDN timeouts),
        // but since we don't, just keep it explicitly here.
        sendProgress({
          ...progressEventBase,
          event: "start",
          data: "",
        });

        const progressWrapper = (event: CallEventSchema) => {
          const progressEvent =
            sseProgressEventDataSchema.shape.event.safeParse(event.event);
          if (progressEvent.success) {
            sendProgress({
              ...progressEventBase,
              event: progressEvent.data,
              data: event.data,
            });
          }
        };

        const output = await callFunctionWrapper({
          func,
          input,
          hookData: undefined,
          messages,
          strict,
          span,
          sendProgress: progressWrapper,
          ...this.args,
        });
        span.log({ output });

        return output;
      },
      {
        name: func.name,
        type: functionSpanType(func),
        event: {
          input,
        },
      },
    );
  }

  public async chainFunction({
    func,
    input,
    messages,
    strict,
    span,
    maxRoundtrips,
    sendProgress,
    mode,
  }: {
    func: InvocableFunction;
    input: unknown;
    messages: Message[];
    strict: boolean | undefined;
    span: Span;
    maxRoundtrips: number;
    sendProgress: (progress: SSEProgressEventData) => void;
    mode: StreamingMode;
  }): Promise<ReadableStream<Uint8Array>> {
    const encoder = new TextEncoder();
    const decoder = new TextDecoder();

    let statusCode = 200;
    const setStatusCode = (code: number) => {
      statusCode = code;
    };
    const isFail = () => statusCode !== 200;

    const { writable, readable } = new TransformStream({
      transform(chunk, controller) {
        if (isFail()) {
          controller.enqueue(
            encoder.encode(
              serializeSSEEvent({
                event: "error",
                data: JSON.stringify(decoder.decode(chunk)),
              }),
            ),
          );
        } else {
          controller.enqueue(chunk);
        }
      },
    });

    sendProgress({
      ...deriveFunctionProgressInfo({ func, span }),
      event: "start",
      data: "",
    });

    await invokeFunction({
      func,
      input,
      hookData: undefined,
      messages,
      span,
      mode,
      strict,
      maxRoundtrips,
      ...this.args,
      functionManager: this,
      setHeader: () => {},
      setStatusCode,
      writable,
      onFinal: (result: InvokeResponse) => {
        span.log({
          output: result.data,
          error: result.error,
        });
        span.end();
      },
    });

    return readable;
  }
}

function deriveFunctionProgressInfo({
  func,
  span,
}: {
  func: InvocableFunction;
  span: Span;
}): Pick<
  SSEProgressEventData,
  "object_type" | "format" | "output_type" | "name" | "id"
> {
  return {
    object_type: deriveFunctionObjectType(func),
    format: deriveFunctionFormat(func),
    output_type: deriveFunctionOutputType(func),
    name: func.name,
    id: span.id,
  };
}
