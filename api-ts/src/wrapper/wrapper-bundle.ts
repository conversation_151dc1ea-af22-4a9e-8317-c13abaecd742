import { WrapperArgs } from "./args";
import { type EvaluatorFile, type Span, type SpanContext } from "braintrust";

let loadedBundle = false;
export function loadBundleFunction(code: string) {
  if (loadedBundle) {
    return;
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  (globalThis as any)._evals = {
    evaluators: {},
    reporters: {},
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  (globalThis as any)._lazy_load = true;

  const __filename = "function.eval.ts";
  new Function("module", "require", "__filename", "__dirname", code)(
    module,
    require,
    __filename,
    __dirname,
  );
  loadedBundle = true;
}

let _PENDING_FLUSHES: Promise<void>[] = [];
export async function flush() {
  await Promise.all(_PENDING_FLUSHES);
  _PENDING_FLUSHES = [];
}
export async function runWrapper(args: WrapperArgs): Promise<unknown> {
  loadBundleFunction(args.code);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  let toRun: (...args: any[]) => any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const evalFile = (globalThis as any)._evals as EvaluatorFile;

  if (args.location.type === "experiment") {
    const evaluator = evalFile.evaluators[args.location.eval_name];

    if (!evaluator) {
      throw new Error(`Evaluator ${args.location.eval_name} not found`);
    }

    if (args.location.position.type === "task") {
      toRun = evaluator.evaluator.task;
    } else if (args.location.position.type === "scorer") {
      toRun = evaluator.evaluator.scores[args.location.position.index];
    } else {
      const _type = args.location.position;
      throw new Error(`Unsupported position type ${JSON.stringify(_type)}`);
    }
  } else {
    toRun = evalFile.functions[args.location.index].handler;
  }

  if (!toRun) {
    throw new Error(
      `Could not find function at location ${JSON.stringify(args.location)}`,
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const spanContext: SpanContext = (globalThis as any)._spanContext;
  if (!spanContext) {
    throw new Error("Span context not set");
  }

  const span: Span = args.parent
    ? spanContext.startSpan({
        parent: args.parent,
        name: "node runtime",
        type: "function",
        event: {
          input: args.arg,
        },
      })
    : spanContext.NOOP_SPAN;

  try {
    const output = await spanContext.withCurrent(
      span,
      async () =>
        await toRun(args.arg, {
          span,
          ...args.hookData,
        }),
    );
    span.log({ output });
    return output;
  } finally {
    span.end();
    _PENDING_FLUSHES.push(span.flush());
  }
}
