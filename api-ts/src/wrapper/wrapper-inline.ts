import { parentPort, workerData } from "worker_threads";
import { Writable } from "stream";
import { invokeInline } from "./inline-vm";
import { INLINE_INVOKE_TIMEOUT_MS } from "./constants";
import { flush } from "./wrapper-bundle";

if (parentPort) {
  const { code, location, input, parent, timeoutMs, hook_data } = workerData;

  const responseStream = new Writable({
    write(chunk, encoding, callback) {
      parentPort?.postMessage({ type: "data", chunk });
      callback();
    },
  });

  const abortController = new AbortController();
  parentPort.on("message", (message) => {
    if (message === "abort") {
      // This will trigger the abort logic in invokeInline
      abortController.abort();
    }
  });

  invokeInline({
    code,
    location,
    input,
    hookData: hook_data,
    parent,
    responseStream,
    abortSignal: abortController.signal,
    timeoutMs: timeoutMs ?? INLINE_INVOKE_TIMEOUT_MS,
  })
    .then(async () => {
      parentPort?.postMessage({ type: "done" });
    })
    .catch((error) => {
      parentPort?.postMessage({ type: "error", error: error.toString() });
    })
    .finally(async () => {
      await flush();
    });
}
