import vm from "vm";
import { writeSSEEvent } from "@braintrust/local/functions";
import { transform } from "sucrase";
import { Writable } from "node:stream";
import type { CodeBundle } from "@braintrust/typespecs";
import { runWrapper } from "./wrapper-bundle";
import util from "util";
import { HookData } from "./lambda-util";
import { currentSpan } from "braintrust";

export async function invokeInline({
  code,
  location,
  input,
  hookData,
  parent,
  responseStream,
  abortSignal,
  timeoutMs,
}: {
  code: string;
  location: CodeBundle["location"] | undefined;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  input: any;
  hookData: HookData | undefined;
  parent?: string;
  responseStream: Writable;
  abortSignal: AbortSignal;
  timeoutMs: number;
}) {
  const controller = new AbortController();
  const { signal } = controller;
  abortSignal.addEventListener("abort", () => controller.abort(), {
    once: true,
  });

  const timeoutId = setTimeout(() => {
    controller.abort();
    writeSSEEvent(
      responseStream,
      {
        event: "error",
        data: JSON.stringify(
          `Function execution timed out after ${timeoutMs}ms`,
        ),
      },
      "utf-8",
    );
  }, timeoutMs);

  const captureOutput =
    (stream: "stdout" | "stderr") =>
    (...args: unknown[]) => {
      const message = args
        .map((arg) =>
          typeof arg === "object"
            ? util.inspect(arg, { depth: null })
            : String(arg),
        )
        .join(" ");
      writeSSEEvent(
        responseStream,
        {
          event: "console",
          data: JSON.stringify({
            stream,
            message,
          }),
        },
        "utf-8",
      );
    };

  // Intercept stdout and stderr
  const originalStdoutWrite = process.stdout.write;
  const originalStderrWrite = process.stderr.write;

  process.stdout.write = (
    chunk: Uint8Array | string,
    _encoding?: BufferEncoding | ((err?: Error) => void),
    _cb?: (err?: Error) => void,
  ) => {
    captureOutput("stdout")(chunk.toString());
    return true;
  };

  process.stderr.write = (
    chunk: Uint8Array | string,
    _encoding?: BufferEncoding | ((err?: Error) => void),
    _cb?: (err?: Error) => void,
  ) => {
    captureOutput("stderr")(chunk.toString());
    return true;
  };

  const originConsoleWriters = [];
  for (const name of ["log", "info", "error", "warn", "debug"] as const) {
    const originalWriter = console[name];
    console[name] = (...args: unknown[]) => {
      captureOutput(name === "log" ? "stdout" : "stderr")(...args);
      originalWriter(...args);
    };
    originConsoleWriters.push(originalWriter);
  }

  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;
  console.log = (...args: unknown[]) => {
    captureOutput("stdout")(...args);
  };
  console.error = (...args: unknown[]) => {
    captureOutput("stderr")(...args);
  };

  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    let fn: (input: any) => Promise<any>;
    let transpiledCode: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    let vmContextArgs: Record<string, any> = {
      Buffer,
      process: {
        env: { ...process.env },
        stdout: process.stdout,
        stderr: process.stderr,
      },
      console,
      fetch,
      Headers,
      Request,
      Response,
    };
    if (location) {
      transpiledCode = `
      callRunWrapper;
      `;
      vmContextArgs = {
        ...vmContextArgs,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        callRunWrapper: (arg: any) =>
          runWrapper({
            location: location!,
            arg,
            code,
            parent,
            hookData,
          }),
      };
    } else {
      const vmCode = `${code}

  handler
  `;

      try {
        transpiledCode = transform(vmCode, {
          transforms: ["typescript", "imports"],
        }).code;
      } catch (e) {
        writeSSEEvent(
          responseStream,
          {
            event: "error",
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            data: JSON.stringify(vmErrorToString(e as VMError)),
          },
          "utf-8",
        );
        responseStream.end();
        return;
      }
    }

    const vmContext = vm.createContext(vmContextArgs);
    try {
      const baseFn = await vm.runInContext(transpiledCode, vmContext);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      fn = (arg: any) => baseFn(arg, { span: currentSpan(), ...hookData });
    } catch (e) {
      writeSSEEvent(
        responseStream,
        {
          event: "error",
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          data: JSON.stringify(vmErrorToString(e as VMError)),
        },
        "utf-8",
      );
      responseStream.end();
      return;
    }

    try {
      const result = await Promise.race([
        fn(input),
        new Promise((resolve, reject) => {
          signal.addEventListener("abort", () => reject(new Error("aborted")), {
            once: true,
          });
        }),
      ]);

      writeSSEEvent(
        responseStream,
        {
          event: "json_delta",
          data: JSON.stringify(result ?? null),
        },
        "utf-8",
      );
    } catch (error) {
      if (signal.aborted) {
        return;
      }
      writeSSEEvent(
        responseStream,
        {
          event: "error",
          data: JSON.stringify(errorToString(error)),
        },
        "utf-8",
      );
    }
  } catch (error) {
    writeSSEEvent(
      responseStream,
      {
        event: "error",
        data: JSON.stringify(errorToString(error)),
      },
      "utf-8",
    );
  } finally {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    process.stdout.write = originalStdoutWrite;
    process.stderr.write = originalStderrWrite;
    clearTimeout(timeoutId);
    responseStream.end();
  }
}

interface VMError {
  stack: string;
}
function vmErrorToString(error: VMError): string {
  const lines = error.stack.split("\n");
  // Find the first line that starts with "\s*at"
  const atLine = lines.findIndex((line) => /^\s*at/.test(line));
  return lines.slice(0, atLine).join("\n").trim();
}

function errorToString(error: unknown): string {
  if (error instanceof Error) {
    return error.stack || error.message;
  } else if (
    typeof error === "object" &&
    error !== null &&
    "toString" in error &&
    typeof error.toString === "function"
  ) {
    return error.toString();
  }
  return String(error);
}
