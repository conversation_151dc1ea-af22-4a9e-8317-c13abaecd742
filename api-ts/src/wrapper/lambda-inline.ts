import { streamifyResponse, ResponseStream } from "lambda-stream";
import { Context } from "aws-lambda";
import { writeSSEEvent } from "@braintrust/local/functions";
import { invokeInline } from "./inline-vm";
import { INLINE_INVOKE_TIMEOUT_MS } from "./constants";
import { initializeRuntimeEnv, inlineEventFormat } from "./lambda-util";
import { flush } from "./wrapper-bundle";

export async function handler(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  eventRaw: any,
  responseStream: ResponseStream,
  context: Context | undefined,
): Promise<void> {
  if (context === undefined) {
    throw new Error("Context is undefined");
  }

  // This flag allows the function to instantly return after the responseStream finishes, without waiting
  // for sockets (namely, Redis) to close.
  // See https://stackoverflow.com/questions/46793670/reuse-redis-connections-for-nodejs-lambda-function
  // and https://docs.aws.amazon.com/lambda/latest/dg/nodejs-context.html
  context.callbackWaitsForEmptyEventLoop = false;

  const event = inlineEventFormat.safeParse(eventRaw);

  if (!event.success) {
    writeSSEEvent(
      responseStream,
      {
        event: "error",
        data: JSON.stringify(event.error.message),
      },
      "utf-8",
    );
    responseStream.end();
    return;
  }

  initializeRuntimeEnv(event.data.env);

  await invokeInline({
    code: event.data.code,
    location: undefined,
    input: event.data.input,
    hookData: event.data.hook_data,
    parent: event.data.parent,
    responseStream,
    abortSignal: new AbortController().signal,
    timeoutMs: INLINE_INVOKE_TIMEOUT_MS,
  });

  await flush();
}

streamifyResponse(handler);
