import { Request, Response } from "express";
import { ATTACHMENT_BUCKET_NAME, ATTACHMENT_BUCKET_PREFIX } from "./env";
import { getRequestContext } from "./request_context";
import { z } from "zod";
import {
  braintrustAttachmentReferenceSchema,
  externalAttachmentReferenceSchema,
  AttachmentStatus,
  attachmentStatusSchema,
} from "@braintrust/typespecs";
import { checkTokenAuthorized } from "./token_auth";
import {
  ForbiddenError,
  BadRequestError,
  InternalServerError,
  NotFoundError,
  wrapZodError,
} from "./util";
import {
  GetResult,
  HeadResult,
  makeObjectStore,
  AzureObjectStore,
} from "./object-storage/object-storage";
import { parseExternalS3Url } from "./object-storage/s3";
import contentDisposition from "content-disposition";
import { readableToString } from "./stream_util";

const oneDayInSeconds = 60 * 60 * 24;

export const makeAttachmentKey = ({
  prefix,
  id,
  orgId,
}: {
  prefix: string;
  id: string;
  orgId: string;
}) => `${prefix}${orgId}/${id.slice(0, 2)}/${id}`;
export const makeAttachmentStatusKey = (key: string) => `${key}.status.json`;

const keySchema = z.object({
  // Keys are generated client side. We need to restrict the endpoints in
  // this file to access only the objects under the attachments prefix.
  key: z.string().min(3),
});

const orgIdSchema = z.object({
  org_id: z.string().min(1),
});

const uploadRequestSchema = braintrustAttachmentReferenceSchema
  .pick({
    filename: true,
    content_type: true,
  })
  .merge(orgIdSchema)
  .merge(keySchema);

// Get /attachment supports both braintrust and external attachments.
const getRequestSchema = z.union([
  braintrustAttachmentReferenceSchema
    .pick({
      filename: true,
      content_type: true,
    })
    .merge(orgIdSchema)
    .merge(keySchema),
  externalAttachmentReferenceSchema
    .pick({
      filename: true,
      content_type: true,
      url: true,
    })
    .merge(orgIdSchema),
]);

const putStatusRequestSchema = z
  .object({ status: attachmentStatusSchema })
  .merge(orgIdSchema)
  .merge(keySchema);

export const defaultAttachmentStatus: AttachmentStatus = {
  upload_status: "uploading",
};
export const doneAttachmentStatus: AttachmentStatus = {
  upload_status: "done",
};

export async function runUploadAttachmentRequest(
  req: Request,
  res: Response,
): Promise<void> {
  const ctx = getRequestContext(req);
  const me = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
    })
  ).me;

  const { attachmentBucketName, attachmentBucketPrefix } =
    checkAttachmentsEnvironment();

  const parsed = wrapZodError(() => uploadRequestSchema.parse(ctx.data));

  if (!me.organizations.some((org) => org.id === parsed.org_id)) {
    throw new ForbiddenError(`Access denied to org ${parsed.org_id}`);
  }

  const objectStore = await makeObjectStore();

  // Write the status object.
  const attachmentKey = makeAttachmentKey({
    prefix: attachmentBucketPrefix,
    id: parsed.key,
    orgId: parsed.org_id,
  });
  const statusKey = makeAttachmentStatusKey(attachmentKey);

  await objectStore.put({
    bucket: attachmentBucketName,
    key: statusKey,
    contentType: "application/json",
    body: JSON.stringify(defaultAttachmentStatus),
    allowOverwrite: false,
  });

  const signedUrl = await objectStore.signedPutUrl({
    bucket: attachmentBucketName,
    key: attachmentKey,
    contentType: parsed.content_type,
    allowOverwrite: false,
    expiresIn: oneDayInSeconds,
  });

  res.json({
    signedUrl,
    headers: {
      "Content-Type": parsed.content_type,
      "If-None-Match": "*",
    },
  });
}

export async function runPutAttachmentStatusRequest(
  req: Request,
  res: Response,
): Promise<void> {
  const ctx = getRequestContext(req);
  const me = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
    })
  ).me;

  const { attachmentBucketName, attachmentBucketPrefix } =
    checkAttachmentsEnvironment();

  const parsed = wrapZodError(() => putStatusRequestSchema.parse(ctx.data));

  if (!me.organizations.some((org) => org.id === parsed.org_id)) {
    throw new InternalServerError(`Access denied to org ${parsed.org_id}`);
  }

  const objectStore = await makeObjectStore();

  // Write the status object.
  const attachmentKey = makeAttachmentKey({
    prefix: attachmentBucketPrefix,
    id: parsed.key,
    orgId: parsed.org_id,
  });
  const statusKey = makeAttachmentStatusKey(attachmentKey);
  await objectStore.put({
    bucket: attachmentBucketName,
    key: statusKey,
    contentType: "application/json",
    body: JSON.stringify(parsed.status),
    allowOverwrite: true,
  });
  res.json({ status: "success" });
}

export async function runGetAttachmentRequest(
  req: Request,
  res: Response,
): Promise<void> {
  const ctx = getRequestContext(req);
  const me = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
    })
  ).me;

  const parsed = wrapZodError(() => getRequestSchema.parse(ctx.data));

  if (!me.organizations.some((org) => org.id === parsed.org_id)) {
    throw new ForbiddenError(`Access denied to org ${parsed.org_id}`);
  }

  const objectStore = await makeObjectStore();

  let bucket: string | undefined;
  let key: string | undefined;
  let statusPromise: Promise<GetResult | undefined> | undefined;

  if ("key" in parsed) {
    const { attachmentBucketName, attachmentBucketPrefix } =
      checkAttachmentsEnvironment();
    bucket = attachmentBucketName;
    key = makeAttachmentKey({
      prefix: attachmentBucketPrefix,
      id: parsed.key,
      orgId: parsed.org_id,
    });
    const statusKey = makeAttachmentStatusKey(key);

    statusPromise = wrapS3NotFound(async () =>
      objectStore.get({
        bucket: bucket!,
        key: statusKey,
      }),
    );
  } else if ("url" in parsed) {
    if (objectStore instanceof AzureObjectStore) {
      throw new BadRequestError(
        "Azure Blob Storage is not supported for external attachments",
      );
    }

    const parsedUrl = parseExternalS3Url(parsed.url);
    if (!parsedUrl) {
      throw new BadRequestError("Invalid S3 URL for external attachment");
    }

    bucket = parsedUrl.bucket;
    key = parsedUrl.key;
    statusPromise = Promise.resolve(undefined);
  } else {
    throw new InternalServerError("Unknown attachment type");
  }

  // Get the object size.
  let headOutput: HeadResult | undefined;
  let status: AttachmentStatus | undefined;

  const headPromise = wrapS3NotFound(
    async () =>
      await objectStore.head({
        bucket,
        key,
      }),
  );

  // We can ignore any exception thrown from `statusPromiseResult` if
  // `headPromiseResult` resolves to a truthy value.
  const [statusPromiseResult, headPromiseResult] = await Promise.allSettled([
    statusPromise,
    headPromise,
  ]);

  if (headPromiseResult.status === "fulfilled") {
    headOutput = headPromiseResult.value;
  } else {
    throw headPromiseResult.reason;
  }

  if (
    statusPromiseResult.status === "fulfilled" &&
    statusPromiseResult.value?.stream
  ) {
    const statusStr = await readableToString(statusPromiseResult.value.stream);
    status = attachmentStatusSchema.strip().parse(JSON.parse(statusStr));
  } else if (headOutput) {
    // Assume the upload succeeded if the file is present.
    status = { upload_status: "done" };
  } else {
    // If we are missing both the file and status, then it doesn't exist.
    if ("key" in parsed) {
      throw new NotFoundError(`Key ${parsed.key} does not exist`);
    } else {
      throw new NotFoundError(`URL ${parsed.url} does not exist`);
    }
  }

  // Get the signed URL.
  const contentType = headOutput?.contentType || parsed.content_type;

  const baseParam = {
    bucket,
    key,
    contentType: contentType,
  };

  const downloadUrlPromise = objectStore.signedGetUrl({
    ...baseParam,
    expiresIn: oneDayInSeconds,
    contentDisposition: contentDisposition(parsed.filename),
  });

  // For embedding PDFs as iframe, we need to remove the Content-Disposition
  // header otherwise the PDF will download instead.
  const embedUrlPromise =
    baseParam.contentType === "application/pdf"
      ? objectStore.signedGetUrl({
          ...baseParam,
          expiresIn: oneDayInSeconds,
        })
      : undefined;

  const [downloadUrl, embedUrl] = await Promise.all([
    downloadUrlPromise,
    embedUrlPromise,
  ]);

  res.json({
    downloadUrl,
    embedUrl: embedUrl ?? downloadUrl,
    // Need to return contentLength separately because the client cannot HEAD
    // the signedUrl that has been signed with GetObjectCommand.
    contentLength: headOutput?.size,
    status,
  });
}

export function checkAttachmentsEnvironment(): {
  attachmentBucketName: string;
  attachmentBucketPrefix: string;
} {
  if (!ATTACHMENT_BUCKET_NAME) {
    throw new InternalServerError("ATTACHMENT_BUCKET is not set");
  }
  if (!ATTACHMENT_BUCKET_PREFIX) {
    throw new InternalServerError("ATTACHMENT_BUCKET_PREFIX is not set");
  }
  if (!ATTACHMENT_BUCKET_PREFIX.endsWith("/")) {
    throw new InternalServerError(
      "ATTACHMENT_BUCKET_PREFIX must have trailing slash `/`",
    );
  }

  return {
    attachmentBucketName: ATTACHMENT_BUCKET_NAME,
    attachmentBucketPrefix: ATTACHMENT_BUCKET_PREFIX,
  };
}

/**
 * Convert the NotFound exception from AWS S3 SDK to an `undefined` return
 * value. Re-throw all other exception types.
 */
async function wrapS3NotFound<T>(f: () => Promise<T>): Promise<T | undefined> {
  try {
    return await f();
  } catch (error) {
    if (error instanceof Error && error.name === "NotFound") {
      return undefined;
    }
    throw error;
  }
}
