import express from "express";
import { MCPServerWithOAuth } from "./mcp-server";

let mcpServer: MCPServerWithOAuth | null = null;

function initializeMCPServer(): MCPServerWithOAuth {
  if (mcpServer) {
    return mcpServer;
  }

  mcpServer = new MCPServerWithOAuth();

  return mcpServer;
}

export function setupMCPRoutes(app: express.Application): void {
  if (!mcpServer) {
    mcpServer = initializeMCPServer();
  }

  mcpServer.setupExpressRoutes(app);
}

export function getMCPServer(): MCPServerWithOAuth | null {
  return mcpServer;
}
