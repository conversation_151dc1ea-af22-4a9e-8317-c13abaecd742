import { pipeline, Readable, Writable } from "stream";

export function mergeStreams<T>(
  stream0: ReadableStream<T>,
  stream1: ReadableStream<T>,
): ReadableStream<T> {
  const readers = [stream0.getReader(), stream1.getReader()];
  const activeStreams = new Set([0, 1]);
  const readPromises: Map<number, Promise<void>> = new Map();

  const nextFromStream = async (
    streamIdx: number,
    controller: ReadableStreamDefaultController<T>,
  ): Promise<void> => {
    const { done, value } = await readers[streamIdx].read();
    if (value) {
      controller.enqueue(value);
    }
    if (done) {
      readers[streamIdx].releaseLock();
      activeStreams.delete(streamIdx);

      if (activeStreams.size === 0) {
        controller.close();
      }
    }

    readPromises.delete(streamIdx);
  };

  return new ReadableStream({
    async pull(controller) {
      for (const streamIdx of activeStreams) {
        if (!readPromises.has(streamIdx)) {
          readPromises.set(streamIdx, nextFromStream(streamIdx, controller));
        }
      }

      // don't block on this
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      (async function waitForNext() {
        await Promise.race(readPromises.values());
      })();
    },
    async cancel(reason) {
      for (const reader of readers) {
        await reader.cancel(reason);
      }
    },
  });
}

export async function readableToString(readable: Readable): Promise<string> {
  let result = "";
  return new Promise((resolve, reject) => {
    pipeline(
      readable,
      new Writable({
        write(chunk, encoding, callback) {
          result += chunk.toString();
          callback();
        },
      }),
      (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      },
    );
  });
}
