import crypto from "crypto";

export function sha1(data: string): string {
  return crypto.createHash("sha1").update(data).digest("hex");
}

export function sha256(
  data: string,
  format: crypto.BinaryToTextEncoding = "hex",
): string {
  return crypto.createHash("sha256").update(data).digest(format);
}

export function md5(data: string): string {
  return crypto.createHash("md5").update(data).digest("hex");
}

// Copied from the usage example on
// https://github.com/jedisct1/siphash-js/tree/master?tab=readme-ov-file#usage.
export const SIPHASH_KEY = [0xdeadbeef, 0xcafebabe, 0x8badf00d, 0x1badb002];
