import Analytics, { TrackParams } from "@segment/analytics-node";
import { logger } from "./telemetry/logger";

let _serverAnalytics: Analytics | undefined;

export function getServerAnalytics(): Analytics | undefined {
  if (_serverAnalytics) {
    return _serverAnalytics;
  }

  if (process.env.API_SEGMENT_WRITE_KEY) {
    _serverAnalytics = new Analytics({
      writeKey: process.env.API_SEGMENT_WRITE_KEY,
      // This ensures that tracked events are sent immediately
      flushAt: 1,
    });
  }

  return _serverAnalytics;
}

function _isSegmentEnabled() {
  return getServerAnalytics() !== undefined;
}

export async function trackSegmentEvent({
  userId,
  event,
  properties,
}: {
  userId: string;
  event: string;
  properties: TrackParams["properties"];
}) {
  const analytics = getServerAnalytics();
  if (!analytics) {
    return;
  }

  return new Promise<void>((resolve) => {
    analytics.track(
      {
        userId,
        event,
        properties,
      },
      (err) => {
        if (err) {
          // Log the error for debugging but don't block the main functionality
          logger.warn("Failed to track Segment event", {
            event,
            userId,
            error: err instanceof Error ? err.message : String(err),
            stack: err instanceof Error ? err.stack : undefined,
          });
        }
        resolve();
      },
    );
  });
}
