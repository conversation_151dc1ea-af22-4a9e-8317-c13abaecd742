import { BRAINTRUST_AUTH_TOKEN_HEADER } from "@braintrust/local";
import { parseAuthHeader } from "@braintrust/proxy";
import { IncomingHttpHeaders } from "http";

export function parseBraintrustAuthHeader(
  headers: IncomingHttpHeaders | Record<string, string>,
): string | undefined {
  const tokenString = parseHeader(headers, BRAINTRUST_AUTH_TOKEN_HEADER);
  return tokenString ?? parseAuthHeader(headers) ?? undefined;
}

export function parseHeader(
  headers: IncomingHttpHeaders | Record<string, string>,
  headerName: string,
): string | undefined {
  const token = headers[headerName];
  let tokenString;
  if (typeof token === "string") {
    tokenString = token;
  } else if (Array.isArray(token) && token.length > 0) {
    tokenString = token[0];
  }

  return tokenString;
}
