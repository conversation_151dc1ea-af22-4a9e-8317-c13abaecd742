import {
  IS_MERGE_FIELD,
  MERGE_PATHS_FIELD,
  deterministicReplacer,
} from "braintrust/util";
import {
  OBJECT_TYPE_FIELD,
  ObjectIdsUnion,
} from "@braintrust/local/api-schema";
import { v4 as uuidv4 } from "uuid";
import { expect, test } from "vitest";
import { EventPublisherItem, LogToPublisherFn } from "./event_publisher";
import { broadcastOrgProjectMetadata, mergeRowPopPaths } from "./run_log_data";

function makeLogToPublisher(
  publishedData: Record<string, Set<string>>,
): LogToPublisherFn {
  function ret(item: EventPublisherItem) {
    const val = JSON.stringify(item, deterministicReplacer);
    if (item.object_id in publishedData) {
      publishedData[item.object_id].add(val);
    } else {
      publishedData[item.object_id] = new Set([val]);
    }
  }
  return ret;
}

test("broadcastOrgProjectMetadata basic", () => {
  const [
    experiment_id,
    dataset_id,
    prompt_session_id,
    project_id,
    org_id0,
    org_id1,
    org_id2,
  ] = [uuidv4(), uuidv4(), uuidv4(), uuidv4(), uuidv4(), uuidv4(), uuidv4()];

  const rows: Record<string, unknown>[] = [
    { id: "row0", org_id: org_id0, project_id, experiment_id },
    { id: "row1", org_id: org_id0, project_id, dataset_id },
    { id: "row2", org_id: org_id1, project_id, prompt_session_id },
    { id: "row3", org_id: org_id1, project_id, prompt_session_id },
    { id: "row4", org_id: org_id2, prompt_session_id },
    { id: "row5", org_id: org_id2, project_id, log_id: "g" },
    { id: "row6", org_id: org_id2, project_id, log_id: "p" },
    { id: "row7", org_id: org_id2, project_id, log_id: "p", function_data: {} },
  ];

  const publishedData: Record<string, Set<string>> = {};
  broadcastOrgProjectMetadata({
    rows,
    logToPublisher: makeLogToPublisher(publishedData),
  });

  function expectedItem(
    orgId: string,
    value: Record<string, unknown>,
  ): EventPublisherItem {
    return {
      object_type: "org_project_metadata",
      object_id: orgId,
      is_audit_log: false,
      type: "data",
      value,
    };
  }

  const expectedItems: EventPublisherItem[] = [
    expectedItem(org_id0, {
      experiment: { [experiment_id]: {} },
      dataset: { [dataset_id]: {} },
    }),
    expectedItem(org_id1, { prompt_session: { [prompt_session_id]: {} } }),
    expectedItem(org_id2, {
      project_log: { [project_id]: {} },
      prompt: { row6: {}, row7: {} },
    }),
  ];
  const expectedPublishedData: Record<string, Set<string>> = {};
  const expectedLogToPublisher = makeLogToPublisher(expectedPublishedData);
  expectedItems.forEach(expectedLogToPublisher);

  expect(publishedData).toEqual(expectedPublishedData);
});

test("mergeRowPopPaths basic", () => {
  const objectIds: ObjectIdsUnion = {
    dataset_id: "d",
    [OBJECT_TYPE_FIELD]: "dataset",
  };
  const controlFields = {
    [IS_MERGE_FIELD]: true as const,
    [MERGE_PATHS_FIELD]: [["expected"], ["input"], ["metadata"]],
  };

  const newRow: Record<string, unknown> = {
    a: "hello",
    output: { b: "world" },
    inputs: { c: "goodbye" },
    metadata: { d: "world" },
    other: { e: "chair" },
  };

  const mergeData: Record<string, unknown> = {
    output: { c: "earth" },
    inputs: { d: "yes" },
    metadata: { e: "dog" },
    other: { f: "bear" },
  };

  mergeRowPopPaths({ newRow, mergeData, objectIds, controlFields });
  expect(newRow).toEqual({
    a: "hello",
    output: { c: "earth" },
    inputs: { d: "yes" },
    metadata: { e: "dog" },
    other: { e: "chair", f: "bear" },
  });
});
