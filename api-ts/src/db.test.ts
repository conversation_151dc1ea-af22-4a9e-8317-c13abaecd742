import { expect, test } from "vitest";
import { duckq, getDuckDBConn } from "./db/duckdb";
import "./db/duckdb-conn";

test("temp tables", async () => {
  const conn1 = getDuckDBConn();
  duckq(conn1, "CREATE TEMPORARY TABLE test (a INTEGER)");
  duckq(conn1, "INSERT INTO test (a) VALUES (1), (2), (3)");
  const conn1Tables = await duckq(
    conn1,
    "SELECT * FROM information_schema.tables WHERE table_name='test'",
  );
  expect(conn1Tables.length).toBe(1);

  const conn2 = getDuckDBConn();
  const conn2Tables = await duckq(
    conn2,
    "SELECT * FROM information_schema.tables WHERE table_name='test'",
  );
  expect(conn2Tables.length).toBe(0);
});
