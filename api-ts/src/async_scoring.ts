import {
  ASYNC_SCORING_CONTROL_FIELD,
  deterministicReplacer,
  isEmpty,
  isObject,
  mapAt,
  recordFind,
} from "braintrust/util";
import {
  AsyncScoringState,
  AsyncScoringControl,
  FunctionId,
  OnlineScoreConfig,
  functionIdSchema,
  onlineScoreConfigSchema,
  ProjectScore,
} from "@braintrust/typespecs";
import {
  ObjectIdsUnion,
  OBJECT_TYPE_FIELD,
  insertControlFieldsSchema,
  objectIdsUnionSchema,
  RowRef,
} from "@braintrust/local/api-schema";
import {
  InvokeAsyncInputRef,
  INVOKE_API_VERSION,
  invokeAsyncBatchRequestSchema,
  InvokeRequestSchema,
  type OnlineScoringTestResults,
  scoreSchema,
} from "@braintrust/local/functions";
import { ASYNC_SCORING_STATE_FIELD, ROW_REF_FIELD } from "@braintrust/local";
import {
  AI_PROXY_FN_ARN,
  AI_PROXY_FN_URL,
  AI_PROXY_INVOKE_ROLE,
  IS_LOCAL,
  TS_API_PORT,
  ASYNC_SCORING_FETCH_PAYLOAD,
  ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD,
  DISABLE_ASYNC_SCORING,
  DISABLE_ASYNC_SCORING_OBJECT_IDS,
} from "./env";
import { z } from "zod";
import { ORIGIN_HEADER } from "./cors";
import { PENDING_FLUSHABLES } from "./pending_flushables";
import {
  AccessDeniedError,
  HTTPError,
  REDIS_ASYNC_SCORING_CACHE_KEY,
  REDIS_ASYNC_SCORING_CACHE_KEYS_SET_KEY,
  extractErrorText,
  getAclObjectId,
  postDefaultHeaders,
  wrapZodError,
} from "./util";
import { getRedis } from "./redis";
import { v4 as uuidv4 } from "uuid";
import { OBJECT_CACHE } from "./object_cache";
import {
  getLogicalToStorageMap,
  getStorageToLogicalMap,
  ObjectType,
} from "./schema";
import { Lambda } from "@aws-sdk/client-lambda";
import { retryLambdaOp } from "./lambda-quarantine/invoke";
import { getStsClient } from "./lambda";
import { sha1 } from "./hash";
import { customFetchRequest } from "./custom_fetch";
import { getLogger } from "./instrumentation/logger";
import { parseBrainstoreObjectId } from "./brainstore/brainstore";
import { otelWrapTraced } from "./instrumentation/api";
import { parseQuery } from "@braintrust/btql/parser";
import { runBtql } from "./btql";
import { RequestContext } from "./request_context";
import { parseAndInterpretExpr } from "./btql_wasm";

const PROXY_URL =
  process.env.TS_API_ASYNC_SCORING_PROXY_URL ||
  // Explicitly default to localhost rather than whatever host the server is
  // listening on, so that we can directly utilize the loopback interface.
  `http://localhost:${TS_API_PORT}`;

const ASYNC_SCORING_INVOKE_FIELDS = ["input", "output", "expected", "metadata"];

let _disableAsyncScoringObjectIds:
  | { objectType: ObjectType; objectId: string }[]
  | null = null;
function getDisableAsyncScoringObjectIds(): {
  objectType: ObjectType;
  objectId: string;
}[] {
  if (_disableAsyncScoringObjectIds === null) {
    if (DISABLE_ASYNC_SCORING_OBJECT_IDS) {
      try {
        const objectIds = z
          .string()
          .array()
          .parse(DISABLE_ASYNC_SCORING_OBJECT_IDS.split(","));
        _disableAsyncScoringObjectIds = objectIds.map((brainstoreObjectId) => {
          const { objectType, objectId } =
            parseBrainstoreObjectId(brainstoreObjectId);
          return { objectType, objectId };
        });
      } catch (e) {
        getLogger().error(
          { error: e },
          "Failed to parse DISABLE_ASYNC_SCORING_OBJECT_IDS. Must be a comma-separated list of brainstore object ids.",
        );
        _disableAsyncScoringObjectIds = [];
      }
    } else {
      _disableAsyncScoringObjectIds = [];
    }
  }
  return _disableAsyncScoringObjectIds;
}

function isAsyncScoringDisabledForObject(
  disableAsyncScoringObjectIds: { objectType: ObjectType; objectId: string }[],
  fullObjectId: ObjectIdsUnion,
): boolean {
  const objectType = fullObjectId[OBJECT_TYPE_FIELD];
  const objectId = getAclObjectId(fullObjectId);
  return disableAsyncScoringObjectIds.some((disableAsyncScoringObjectId) => {
    return (
      objectType === disableAsyncScoringObjectId.objectType &&
      objectId === disableAsyncScoringObjectId.objectId
    );
  });
}

export class AsyncScoringConfigCache {
  constructor(public expiration_time = 3600) {}

  public async getAsyncScoringConfigMulti({
    appOrigin,
    authToken,
    allObjectIds,
    wasCachedToken,
  }: {
    allObjectIds: ObjectIdsUnion[];
    appOrigin: string;
    authToken: string | undefined;
    wasCachedToken?: string;
  }): Promise<Map<string, OnlineScoreConfig[]>> {
    if (DISABLE_ASYNC_SCORING) {
      return new Map();
    }

    const disableAsyncScoringObjectIds = getDisableAsyncScoringObjectIds();
    allObjectIds = allObjectIds.filter((fullObjectId) => {
      return !isAsyncScoringDisabledForObject(
        disableAsyncScoringObjectIds,
        fullObjectId,
      );
    });

    // We only support project_logs for now, so just filter down to those and
    // track the project IDs.
    const projectIdToKeys = new Map<
      string,
      { objectIdKey: string; cacheKey: string }
    >();
    allObjectIds.forEach((objectIds) => {
      if (
        objectIds[OBJECT_TYPE_FIELD] === "project_logs" &&
        !projectIdToKeys.has(objectIds.project_id)
      ) {
        const objectIdKey = AsyncScoringConfigCache.makeObjectIdKey(objectIds);
        const cacheKey = AsyncScoringConfigCache.makeCacheKey({
          projectId: objectIds.project_id,
          token: authToken || "anon",
        });
        projectIdToKeys.set(objectIds.project_id, {
          objectIdKey,
          cacheKey,
        });
      }
    });

    const objectIdKeyToConfig = new Map<string, OnlineScoreConfig[]>();
    if (projectIdToKeys.size === 0) {
      return objectIdKeyToConfig;
    }

    const allProjectIds = [...projectIdToKeys.keys()];
    const redisClient = await getRedis();
    const allConfigValues = await Promise.all(
      allProjectIds.map((projectId) =>
        redisClient.get(mapAt(projectIdToKeys, projectId).cacheKey),
      ),
    );

    const notFoundProjectIds: string[] = [];
    allProjectIds.forEach((projectId, i) => {
      const { objectIdKey } = mapAt(projectIdToKeys, projectId);
      const configValue = allConfigValues[i];
      if (configValue) {
        const scoringConfigs = onlineScoreConfigSchema
          .array()
          .parse(JSON.parse(configValue));
        objectIdKeyToConfig.set(objectIdKey, scoringConfigs);
      } else {
        notFoundProjectIds.push(projectId);
      }
    });

    if (notFoundProjectIds.length === 0) {
      if (wasCachedToken) {
        await redisClient.set(wasCachedToken, "true", { EX: 3600 });
      }
      return objectIdKeyToConfig;
    }

    // We need to query the app DB for the missing scoring configs.
    const resp = await customFetchRequest(
      `${appOrigin}/api/project_score/get`,
      {
        method: "POST",
        headers: postDefaultHeaders({ token: authToken }),
        body: JSON.stringify({
          project_id: notFoundProjectIds,
          score_type: "online",
        }),
      },
    );
    if (!resp.ok) {
      throw new HTTPError(
        resp.statusCode,
        `Failed to get online scoring config:\n${await resp.body.text()}`,
      );
    }
    const responseConfigs = z
      .object({
        project_id: z.string(),
        config: z.object({ online: onlineScoreConfigSchema }).nullish(),
      })
      .array()
      .parse(await resp.body.json());

    // Make sure to initialize the missing project IDs with an empty array, so
    // that we cache results for projects with no online scores.
    const projectIdToFetchedConfigs = new Map<string, OnlineScoreConfig[]>(
      notFoundProjectIds.map((projectId): [string, OnlineScoreConfig[]] => [
        projectId,
        [],
      ]),
    );
    responseConfigs.forEach(({ project_id, config }) => {
      if (!config) {
        return;
      }
      mapAt(projectIdToFetchedConfigs, project_id).push(config.online);
    });

    // Save the configs in redis.
    await Promise.all(
      [...projectIdToFetchedConfigs.entries()].flatMap(
        ([projectId, configs]): Promise<unknown>[] => {
          const { cacheKey } = mapAt(projectIdToKeys, projectId);
          const setKey = AsyncScoringConfigCache.makeSetKey({
            projectId,
          });
          const value = JSON.stringify(configs);
          return [
            redisClient.set(cacheKey, value, { EX: this.expiration_time }),
            redisClient.sAdd(setKey, cacheKey),
            redisClient.expire(setKey, this.expiration_time),
          ];
        },
      ),
    );

    // Merge the fetched configs with the cached ones and return.
    projectIdToFetchedConfigs.forEach((configs, projectId) => {
      const { objectIdKey } = mapAt(projectIdToKeys, projectId);
      objectIdKeyToConfig.set(objectIdKey, configs);
    });
    return objectIdKeyToConfig;
  }

  public static makeObjectIdKey(objectIds: ObjectIdsUnion): string {
    return JSON.stringify([
      objectIds[OBJECT_TYPE_FIELD],
      getAclObjectId(objectIds),
    ]);
  }

  public async flushEntries({ projectId }: { projectId: string }) {
    const setKey = AsyncScoringConfigCache.makeSetKey({ projectId });
    const redisClient = await getRedis();
    const matchingKeys = await redisClient.sMembers(setKey);
    if (matchingKeys.length > 0) {
      await Promise.all([
        redisClient.del(matchingKeys),
        redisClient.sRem(setKey, matchingKeys),
      ]);
    }
  }

  private static makeCacheKey({
    projectId,
    token,
  }: {
    projectId: string;
    token: string;
  }): string {
    return JSON.stringify([
      REDIS_ASYNC_SCORING_CACHE_KEY,
      "project",
      projectId,
      sha1(token),
    ]);
  }

  private static makeSetKey({ projectId }: { projectId: string }): string {
    return JSON.stringify([
      REDIS_ASYNC_SCORING_CACHE_KEYS_SET_KEY,
      "project",
      projectId,
    ]);
  }
}

export const ASYNC_SCORING_CONFIG_CACHE = new AsyncScoringConfigCache();

export async function flushAsyncScoringCache({
  appOrigin,
  authToken,
  ctxData,
}: {
  appOrigin: string;
  authToken: string | undefined;
  ctxData: unknown;
}) {
  const params = wrapZodError(() =>
    z
      .object({
        project_id: z.string(),
      })
      .parse(ctxData),
  );

  // Make sure the requester has read access to the project.
  const objectCacheEntry = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken,
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: params.project_id,
  });
  if (!objectCacheEntry.permissions.includes("read")) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType: "project",
      objectId: params.project_id,
    });
  }

  await ASYNC_SCORING_CONFIG_CACHE.flushEntries({
    projectId: params.project_id,
  });
}

export function hasMetricsEnd(opaqueData: Record<string, unknown>): boolean {
  return !isEmpty(
    isObject(opaqueData["metrics"]) ? opaqueData["metrics"]["end"] : undefined,
  );
}

export function getSpanName(
  opaqueData: Record<string, unknown>,
): string | null | undefined {
  const ret = isObject(opaqueData["span_attributes"])
    ? opaqueData["span_attributes"]["name"]
    : undefined;
  const retParsed = z.string().nullish().safeParse(ret);
  return retParsed.success ? retParsed.data : null;
}

export function asyncScoringSkipMerge({
  oldRowAsyncScoringState,
  newRowAsyncScoringControl,
}: {
  oldRowAsyncScoringState: AsyncScoringState;
  newRowAsyncScoringControl: AsyncScoringControl | null;
}): boolean {
  return !!(
    oldRowAsyncScoringState &&
    oldRowAsyncScoringState.status === "enabled" &&
    newRowAsyncScoringControl &&
    newRowAsyncScoringControl.kind === "score_update" &&
    oldRowAsyncScoringState.token !== newRowAsyncScoringControl.token
  );
}

type AsyncScoringDetermineStateRowInfo = {
  objectIds: ObjectIdsUnion;
  span_id: string;
  root_span_id: string;
  span_parents: string[];
  span_name: string | null | undefined;
  reachedSelectionEpoch: boolean;
  opaqueData: Record<string, unknown>;
  changedFields?: Set<string>;
};

function selectAsyncScoringState({
  rowInfo,
  asyncScoringConfigs,
}: {
  rowInfo: AsyncScoringDetermineStateRowInfo;
  asyncScoringConfigs: Map<string, OnlineScoreConfig[]>;
}): AsyncScoringState {
  const objectKey = AsyncScoringConfigCache.makeObjectIdKey(rowInfo.objectIds);
  const configs = asyncScoringConfigs.get(objectKey);
  if (!(configs && rowInfo.reachedSelectionEpoch)) {
    return null;
  }
  // Collect all the functions we obtain from applying each config.
  const configFunctions = new Set<string>();
  let skipLogging = false;
  configs.forEach((config) => {
    if (config.btql_filter) {
      const storageToLogical = getStorageToLogicalMap(
        rowInfo.objectIds[OBJECT_TYPE_FIELD],
      );
      const normalizedRow = Object.fromEntries(
        Object.entries(rowInfo.opaqueData).map(([k, v]) => [
          recordFind(storageToLogical, k) ?? k,
          v,
        ]),
      );
      if (
        parseAndInterpretExpr({
          expr: config.btql_filter,
          bindCtx: undefined,
          row: normalizedRow,
        }) !== true
      ) {
        return;
      }
    }
    if (
      (config.apply_to_root_span ||
        (config.apply_to_span_names &&
          config.apply_to_span_names.length > 0)) &&
      !(
        (config.apply_to_root_span && !rowInfo.span_parents.length) ||
        (config.apply_to_span_names &&
          rowInfo.span_name &&
          config.apply_to_span_names.includes(rowInfo.span_name))
      )
    ) {
      return;
    }
    if (Math.random() >= config.sampling_rate) {
      return;
    }
    config.scorers.forEach((savedFunctionId) => {
      const functionId = ((): FunctionId => {
        switch (savedFunctionId.type) {
          case "function":
            return { function_id: savedFunctionId.id };
          case "global":
            return { global_function: savedFunctionId.name };
          default:
            const x: never = savedFunctionId;
            throw new Error(
              `Unexpected function id type: ${JSON.stringify(x)}`,
            );
        }
      })();
      configFunctions.add(JSON.stringify(functionId, deterministicReplacer));
    });
    skipLogging = skipLogging || !!config.skip_logging;
  });
  const functionIds = [...configFunctions.keys()].map((x) =>
    functionIdSchema.parse(JSON.parse(x)),
  );
  if (!functionIds.length) {
    return null;
  }
  return {
    status: "enabled",
    token: uuidv4(),
    function_ids: [functionIds[0], ...functionIds.slice(1)],
    skip_logging: skipLogging,
  };
}

export function nextAsyncScoringState({
  asyncScoringState,
  asyncScoringControl,
  skipAsyncScoring,
  rowInfo,
  asyncScoringConfigs,
}: {
  asyncScoringState: AsyncScoringState;
  asyncScoringControl: AsyncScoringControl | null;
  skipAsyncScoring: boolean;
  rowInfo: AsyncScoringDetermineStateRowInfo;
  asyncScoringConfigs: Map<string, OnlineScoreConfig[]>;
}): { state: AsyncScoringState; shouldSkip: boolean } {
  if (skipAsyncScoring) {
    // If we are skipping async scoring for this row, leave the state as-is.
    return { state: asyncScoringState, shouldSkip: true };
  } else if (
    asyncScoringControl &&
    asyncScoringControl.kind === "state_override"
  ) {
    return { state: asyncScoringControl.state, shouldSkip: false };
  } else if (
    asyncScoringControl &&
    asyncScoringControl.kind === "state_force_reselect"
  ) {
    // Force override the epoch param to true to force a reselection.
    return {
      state: selectAsyncScoringState({
        rowInfo: { ...rowInfo, reachedSelectionEpoch: true },
        asyncScoringConfigs,
      }),
      shouldSkip: false,
    };
  } else if (asyncScoringState) {
    switch (asyncScoringState.status) {
      case "enabled":
        // Check if we have a force rescore control
        if (
          asyncScoringControl &&
          asyncScoringControl.kind === "state_enabled_force_rescore"
        ) {
          // Force rescore regardless of changed fields
          getLogger().debug({
            rowId: rowInfo.opaqueData.id,
            objectType: rowInfo.objectIds[OBJECT_TYPE_FIELD],
            message:
              "Force rescoring due to state_enabled_force_rescore control",
          });
          return {
            state: {
              ...asyncScoringState,
              token: uuidv4(),
            },
            shouldSkip: false,
          };
        }

        // Check if this is a re-trigger and only non-triggering fields changed
        if (rowInfo.changedFields) {
          const hasAsyncScoringFieldChange = ASYNC_SCORING_INVOKE_FIELDS.some(
            (field) => rowInfo.changedFields!.has(field),
          );
          if (!hasAsyncScoringFieldChange) {
            // Only non-triggering fields changed, skip re-scoring
            getLogger().debug({
              rowId: rowInfo.opaqueData.id,
              objectType: rowInfo.objectIds[OBJECT_TYPE_FIELD],
              changedFields: Array.from(rowInfo.changedFields),
              message:
                "Skipping async scoring re-trigger - only non-triggering fields changed",
            });
            return { state: asyncScoringState, shouldSkip: true };
          }
        }
        // Async scoring is already on. Just refresh the token.
        return {
          state: {
            ...asyncScoringState,
            token: uuidv4(),
          },
          shouldSkip: false,
        };
      case "disabled":
        // Leave it disabled.
        return { state: asyncScoringState, shouldSkip: false };
      default:
        const x: never = asyncScoringState;
        throw new Error(`Unexpected async scoring state: ${x}`);
    }
  } else {
    return {
      state: selectAsyncScoringState({ rowInfo, asyncScoringConfigs }),
      shouldSkip: false,
    };
  }
}

const invokeAsyncScoringRowIdsSchema = z.object({
  id: z.string(),
  span_id: z.string(),
  root_span_id: z.string(),
});

interface AccumulatedScoringRow {
  fullRowData: Record<string, unknown>;
  rowRef: RowRef | undefined;
  fullRowDataByteSize: number;
  functionIds: [FunctionId, ...FunctionId[]];
  scoringToken: string;
  skipLogging: boolean;
}

export async function invokeAsyncScoring({
  allRows,
  appOrigin,
  token,
}: {
  allRows: {
    fullRowData: Record<string, unknown>;
    rowRef: RowRef | undefined;
    fullRowDataByteSize: number;
    skipAsyncScoring: boolean;
  }[];
  appOrigin: string;
  token: string | undefined;
}): Promise<void> {
  if (DISABLE_ASYNC_SCORING) {
    return;
  }

  const pino = getLogger().child({
    task: "invokeAsyncScoring",
  });
  // Filter out rows that we do not want to kick off scoring for.
  const scoringRows = allRows.reduce(
    (
      acc: AccumulatedScoringRow[],
      { fullRowData, rowRef, fullRowDataByteSize, skipAsyncScoring },
    ): AccumulatedScoringRow[] => {
      const { [ASYNC_SCORING_STATE_FIELD]: asyncScoringState } =
        insertControlFieldsSchema
          .pick({ [ASYNC_SCORING_STATE_FIELD]: true })
          .parse(fullRowData);
      if (
        asyncScoringState &&
        asyncScoringState.status === "enabled" &&
        !skipAsyncScoring
      ) {
        return [
          ...acc,
          {
            fullRowData,
            rowRef,
            fullRowDataByteSize,
            functionIds: functionIdSchema
              .array()
              .nonempty()
              .parse(asyncScoringState.function_ids),
            scoringToken: asyncScoringState.token,
            skipLogging: !!asyncScoringState.skip_logging,
          },
        ];
      } else {
        return acc;
      }
    },
    [],
  );

  if (!scoringRows.length) {
    return;
  }

  const orgIdToOrgName = new Map<string, string>();
  for (const { fullRowData } of scoringRows) {
    const orgId = z.string().nullish().parse(fullRowData["org_id"]);
    if (!orgId || orgIdToOrgName.has(orgId)) {
      continue;
    }

    const orgEntry = await OBJECT_CACHE.checkAndGet({
      appOrigin,
      authToken: token,
      aclObjectType: "organization",
      overrideRestrictObjectType: undefined,
      objectId: orgId,
    });
    orgIdToOrgName.set(orgId, orgEntry.object_name);
  }

  const disableAsyncScoringObjectIds = getDisableAsyncScoringObjectIds();
  const scoringRowByteSize = scoringRows.reduce(
    (acc, { fullRowDataByteSize }) => acc + fullRowDataByteSize,
    0,
  );
  const preferRowRef =
    ASYNC_SCORING_FETCH_PAYLOAD &&
    scoringRowByteSize > ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD / 2;
  const bodyComponents = scoringRows.reduce(
    (
      acc: z.infer<typeof invokeAsyncBatchRequestSchema>[],
      { fullRowData, rowRef, functionIds, scoringToken, skipLogging },
    ) => {
      const orgId = z.string().nullish().parse(fullRowData["org_id"]);
      const orgName = orgId ? orgIdToOrgName.get(orgId) : undefined;
      const objectIds = objectIdsUnionSchema.parse(fullRowData);
      const objectType = objectIds[OBJECT_TYPE_FIELD];
      const aclObjectId = getAclObjectId(objectIds);
      if (!(objectType === "project_logs" || objectType === "experiment")) {
        throw new Error(
          `Unexpected object type ${objectType} for async scoring`,
        );
      }

      if (
        isAsyncScoringDisabledForObject(disableAsyncScoringObjectIds, objectIds)
      ) {
        return acc;
      }

      const rowIds = invokeAsyncScoringRowIdsSchema.parse(fullRowData);
      const invokeInput: unknown = (() => {
        if (rowRef && preferRowRef) {
          const ret: InvokeAsyncInputRef = {
            [ROW_REF_FIELD]: rowRef,
            fields: ASYNC_SCORING_INVOKE_FIELDS,
          };
          return ret;
        } else {
          const logicalToStorageMap = getLogicalToStorageMap(
            objectIds[OBJECT_TYPE_FIELD],
          );
          const rowProp = (key: string) =>
            fullRowData[logicalToStorageMap[key] ?? key];
          const ret: Record<string, unknown> = {};
          for (const field of ASYNC_SCORING_INVOKE_FIELDS) {
            ret[field] = rowProp(field);
          }
          return ret;
        }
      })();

      // Make sure the trace created for the score doesn't
      // trigger any scoring itself, otherwise we could end up
      // in an infinite loop.
      const asyncScoringStateOverride: AsyncScoringControl = {
        kind: "state_override",
        state: {
          status: "disabled",
        },
      };
      const req: z.infer<typeof invokeAsyncBatchRequestSchema> = {
        function_ids: functionIds,
        non_id_args: {
          api_version: INVOKE_API_VERSION,
          input: invokeInput,
          parent: skipLogging
            ? undefined
            : {
                object_type: objectType,
                object_id: aclObjectId,
                row_ids: rowIds,
                propagated_event: {
                  [ASYNC_SCORING_CONTROL_FIELD]: asyncScoringStateOverride,
                },
              },
          update_score: {
            object_type: objectType,
            object_id: aclObjectId,
            row_id: rowIds.id,
            token: scoringToken,
          },
          org_name: orgName,
        },
      };

      return [...acc, req];
    },
    [],
  );

  // We need to check the byte length of the payload against lambda's limit.  If
  // the payload is too large, log a warning and skip async scoring.
  const encoder = new TextEncoder();
  const body = JSON.stringify(bodyComponents);
  const payloadSizeBytes = encoder.encode(body).length;
  if (payloadSizeBytes > ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD) {
    const isRowRefInput = (input: unknown) => {
      if (input instanceof Object && ROW_REF_FIELD in input) {
        return true;
      }
      return false;
    };
    const rowRefInputSizes = JSON.stringify(
      bodyComponents.filter((b) => isRowRefInput(b.non_id_args.input)),
    ).length;
    pino.warn(
      { payloadSizeBytes, rowRefInputSizes },
      "Skipping async scoring request. Payload too large.",
    );
    return;
  }

  // Launch a batch async task to invoke the specified function on each of the
  // collected rows.
  const task = (async () => {
    try {
      if (!(IS_LOCAL || AI_PROXY_FN_ARN)) {
        pino.warn(
          "Skipping async scoring since we're neither in local mode nor have an ARN to run",
        );
        return;
      }
      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        [ORIGIN_HEADER]: appOrigin,
      };
      if (IS_LOCAL) {
        const resp = await fetch(`${PROXY_URL}/function/invoke-async-batch`, {
          method: "POST",
          headers,
          body,
        });
        if (!resp.ok) {
          throw new Error(await resp.text());
        }
      } else {
        await invokeLambdaAsyncBatchScoring({ headers, body });
      }
    } catch (e) {
      const err = e instanceof Error && "cause" in e ? e.cause : `${e}`;
      pino.error({ error: err }, "Failed to request async score");
    }
  })();
  PENDING_FLUSHABLES.add(task);
}

async function invokeLambdaAsyncBatchScoring({
  headers,
  body,
}: {
  headers: Record<string, string>;
  body: string;
}) {
  const pino = getLogger().child({
    task: "invokeLambdaAsyncBatchScoring",
  });
  if (!AI_PROXY_INVOKE_ROLE) {
    throw new Error("No AI Proxy invoke role configured");
  }
  const functionArn = AI_PROXY_FN_ARN!;

  const credentials = await loadCachedProxyInvokeCredentials({ functionArn });

  const invokeLambda = new Lambda({
    credentials: {
      accessKeyId: credentials.accessKeyId,
      secretAccessKey: credentials.secretAccessKey,
      sessionToken: credentials.sessionToken,
    },
  });
  const proxyURL = new URL(AI_PROXY_FN_URL ?? "https://api.braintrust.dev");

  const start = Date.now();
  const response = await retryLambdaOp({
    functionArn,
    op: () =>
      invokeLambda.invoke({
        FunctionName: functionArn,
        InvocationType: "Event",
        Payload: JSON.stringify({
          version: "2.0",
          routeKey: "$default",
          rawPath: "/function/invoke-async-batch",
          headers,
          requestContext: {
            http: { method: "POST", path: "/function/invoke" },
            domainName: proxyURL.hostname,
          },
          body,
          isBase64Encoded: false,
        }),
      }),
    name: "invoke async batch scoring",
  });
  pino.debug(
    { elapsedMs: Date.now() - start },
    "AI Proxy function invoke completed",
  );
  if (!(response.StatusCode === 200 || response.StatusCode === 202)) {
    throw new Error(`Failed to invoke async scoring: ${response.StatusCode}`);
  }
}

async function invokeLambdaAsyncScoring({
  headers,
  body,
}: {
  headers: Record<string, string>;
  body: string;
}): Promise<z.infer<typeof scoreSchema>> {
  const pino = getLogger().child({
    task: "invokeLambdaAsyncScoring",
  });
  if (!AI_PROXY_INVOKE_ROLE) {
    throw new Error("No AI Proxy invoke role configured");
  }
  const functionArn = AI_PROXY_FN_ARN!;

  const credentials = await loadCachedProxyInvokeCredentials({ functionArn });

  const invokeLambda = new Lambda({
    credentials: {
      accessKeyId: credentials.accessKeyId,
      secretAccessKey: credentials.secretAccessKey,
      sessionToken: credentials.sessionToken,
    },
  });
  const proxyURL = new URL(AI_PROXY_FN_URL ?? "https://api.braintrust.dev");

  const start = Date.now();
  const response = await retryLambdaOp({
    functionArn,
    op: () =>
      invokeLambda.invoke({
        FunctionName: functionArn,
        Payload: JSON.stringify({
          version: "2.0",
          routeKey: "$default",
          rawPath: "/function/invoke",
          headers,
          requestContext: {
            http: { method: "POST", path: "/function/invoke" },
            domainName: proxyURL.hostname,
          },
          body,
          isBase64Encoded: false,
        }),
      }),
    name: "invoke async scoring",
  });
  pino.debug(
    { elapsedMs: Date.now() - start },
    "AI Proxy function invoke completed",
  );
  if (!(response.StatusCode === 200 || response.StatusCode === 202)) {
    throw new Error(`Failed to invoke async scoring: ${response.StatusCode}`);
  }
  if (!response.Payload) {
    throw new Error("No payload received from async scoring");
  }
  const fullResult = new TextDecoder().decode(response.Payload);

  // https://aws.amazon.com/blogs/compute/using-response-streaming-with-aws-lambda-web-adapter-to-optimize-performance/
  const [_headers, resultPayload] = fullResult.split(
    "\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000",
    2,
  );

  const parsedPayload = scoreSchema.parse(JSON.parse(resultPayload));
  return parsedPayload;
}

const CREDENTIALS_CACHE_TTL = 900; // 15 minutes
const credentialsSchema = z.object({
  accessKeyId: z.string(),
  secretAccessKey: z.string(),
  sessionToken: z.string(),
});
async function loadCachedProxyInvokeCredentials({
  functionArn,
}: {
  functionArn: string;
}) {
  const pino = getLogger().child({
    task: "loadCachedProxyInvokeCredentials",
  });
  const redis = await getRedis();

  const proxyCredentialsKey = `proxy-invoke-credentials-${AI_PROXY_INVOKE_ROLE}-${functionArn}`;
  const cachedCredentials = await redis.get(proxyCredentialsKey);
  if (cachedCredentials) {
    const parsedCredentials = credentialsSchema.safeParse(
      JSON.parse(cachedCredentials),
    );
    if (parsedCredentials.success) {
      return parsedCredentials.data;
    } else {
      pino.warn(
        { error: parsedCredentials.error },
        "Failed to parse cached proxy invoke credentials",
      );
    }
  }

  const sts = await getStsClient();
  const roleResponse = await retryLambdaOp({
    functionArn,
    op: () =>
      sts.assumeRole({
        RoleArn: AI_PROXY_INVOKE_ROLE,
        RoleSessionName: "AIProxyInvokeRoleSession",
        DurationSeconds: CREDENTIALS_CACHE_TTL + 60,
        Policy: JSON.stringify({
          Version: "2012-10-17",
          Statement: [
            {
              Effect: "Allow",
              Action: "lambda:InvokeFunction",
              Resource: functionArn,
            },
          ],
        }),
      }),
    name: "assume role",
  });
  if (!roleResponse.Credentials) {
    throw new Error("Failed to assume role");
  }
  if (
    !roleResponse.Credentials.AccessKeyId ||
    !roleResponse.Credentials.SecretAccessKey ||
    !roleResponse.Credentials.SessionToken
  ) {
    throw new Error("Failed to assume role (no credentials)");
  }
  const credentials: z.infer<typeof credentialsSchema> = {
    accessKeyId: roleResponse.Credentials.AccessKeyId,
    secretAccessKey: roleResponse.Credentials.SecretAccessKey,
    sessionToken: roleResponse.Credentials.SessionToken,
  };
  PENDING_FLUSHABLES.add(
    (async () => {
      try {
        await redis.set(proxyCredentialsKey, JSON.stringify(credentials), {
          EX: CREDENTIALS_CACHE_TTL,
        });
      } catch (e) {
        pino.error({ error: e }, "Failed to cache proxy invoke credentials");
      }
    })(),
  );
  return credentials;
}

type TestOnlineScoringResponse =
  | { kind: "success"; payload: OnlineScoringTestResults }
  | { kind: "error"; message: string };

type TestOnlineScoringRowResult =
  OnlineScoringTestResults[number]["results"][number];

export const testOnlineScoring = otelWrapTraced(
  "testOnlineScoring",
  async function testOnlineScoring(
    {
      orgName,
      projectId,
      automation,
    }: {
      orgName: string;
      projectId: string;
      automation: Omit<ProjectScore, "id" | "project_id" | "user_id"> & {
        id?: string | undefined;
      };
    },
    ctx: RequestContext,
  ): Promise<TestOnlineScoringResponse> {
    const pino = getLogger().child({
      task: "testOnlineScoring",
    });

    if (!(IS_LOCAL || AI_PROXY_FN_ARN)) {
      pino.warn(
        "Skipping async scoring since we're neither in local mode nor have an ARN to run",
      );
      return {
        kind: "error",
        message:
          "Skipping async scoring since we're neither in local mode nor have an ARN to run",
      };
    }

    const config = automation.config?.online;
    if (!config) {
      return {
        kind: "error",
        message: "No online scoring config found",
      };
    } else if (config.scorers.length === 0) {
      return {
        kind: "error",
        message: "No scorers found in online scoring config",
      };
    }

    const spanFilter =
      [
        ...(config.apply_to_root_span ? ["is_root"] : []),
        ...(config.apply_to_span_names && config.apply_to_span_names.length > 0
          ? config.apply_to_span_names.map(
              (name) => `span_attributes.name = "${name}"`,
            )
          : []),
      ].join(" OR ") || null;
    const filter =
      [
        ...(config.btql_filter ? [`(${config.btql_filter})`] : []),
        ...(spanFilter ? [`(${spanFilter})`] : []),
      ].join(" AND ") || "true";

    const queryStr = `
    select: ${ASYNC_SCORING_INVOKE_FIELDS.join(", ")} |
    from: project_logs("${projectId}") spans |
    filter: ${filter} |
    limit: 10
  `;
    const query = parseQuery(queryStr);
    const btqlResult = await runBtql({
      body: {
        query,
        use_brainstore: true,
        brainstore_realtime: true,
      },
      appOrigin: ctx.appOrigin,
      ctxToken: ctx.token,
    });
    if (!("rows" in btqlResult)) {
      throw new Error("BTQL response missing rows");
    } else if (btqlResult.rows.length === 0) {
      return {
        kind: "error",
        message:
          "No matching rows found for the provided filters. Adjust the filter to test again.",
      };
    }

    const configFunctions = new Set(
      config.scorers.map((scorer) => {
        switch (scorer.type) {
          case "function":
            return JSON.stringify(
              { function_id: scorer.id },
              deterministicReplacer,
            );
          case "global":
            return JSON.stringify(
              { global_function: scorer.name },
              deterministicReplacer,
            );
          default:
            const exhaustiveCheck: never = scorer;
            throw new Error(`Unknown scorer type: ${exhaustiveCheck}`);
        }
      }),
    );
    const functionIds = [...configFunctions.keys()].map((functionIdStr) => {
      const f = functionIdSchema.parse(JSON.parse(functionIdStr));
      if ("function_id" in f) {
        return f;
      } else if ("global_function" in f) {
        return f;
      } else {
        throw new Error(`Unsupported function ID: ${JSON.stringify(f)}`);
      }
    });

    // Individually invoke the specified functions so we can get the results back
    const results = await Promise.all(
      btqlResult.rows.map(async (row) => {
        const rowResults: TestOnlineScoringRowResult[] = await Promise.all(
          functionIds.map(async (functionId) => {
            const globalFunction = "global_function" in functionId;
            const functionIdArgs = globalFunction
              ? { global_function: functionId.global_function }
              : { function_id: functionId.function_id };
            try {
              const req: InvokeRequestSchema = {
                ...functionIdArgs,
                api_version: INVOKE_API_VERSION,
                input: row,
                parent: undefined, // always skip logging
                update_score: undefined, // alwaysskip updating score
                org_name: orgName,
                stream: false,
              };
              const body = JSON.stringify(req);
              const encoder = new TextEncoder();
              const payloadSizeBytes = encoder.encode(body).length;
              if (payloadSizeBytes > ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD) {
                throw new Error(
                  "Async scoring payload too large, cannot test scorer interactively",
                );
              }

              const headers: Record<string, string> = {
                Authorization: `Bearer ${ctx.token}`,
                "Content-Type": "application/json",
                [ORIGIN_HEADER]: ctx.appOrigin,
              };
              if (IS_LOCAL) {
                const resp = await fetch(`${PROXY_URL}/function/invoke`, {
                  method: "POST",
                  headers,
                  body,
                });
                if (!resp.ok) {
                  throw new Error(await resp.text());
                }
                const result = await resp.json();
                if (!scoreSchema.safeParse(result).success) {
                  throw new Error(
                    `Invalid result from scorer: ${JSON.stringify(result)}`,
                  );
                }
                return {
                  kind: "success",
                  ...functionIdArgs,
                  result,
                };
              } else {
                const result = await invokeLambdaAsyncScoring({
                  headers,
                  body,
                });
                return {
                  kind: "success",
                  ...functionIdArgs,
                  result,
                };
              }
            } catch (e) {
              const err =
                e instanceof Error && "cause" in e ? `${e.cause}` : `${e}`;
              pino.error(
                { error: extractErrorText(err) },
                "Failed to request async score",
              );
              return {
                kind: "error",
                ...functionIdArgs,
                error: err,
              };
            }
          }),
        );

        return {
          row,
          results: rowResults,
        };
      }),
    );

    return {
      kind: "success",
      payload: results,
    };
  },
);
