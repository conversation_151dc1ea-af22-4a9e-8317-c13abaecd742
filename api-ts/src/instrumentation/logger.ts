// pino logging
import pino, { DestinationStream, Logger } from "pino";
import { getCurrentSpan } from "./api";
import { DEPLOYMENT_MODE, GIT_COMMIT, LOG_LEVEL, PRETTY_LOGGING } from "../env";

export const createPinoLogger = (
  destination?: DestinationStream,
  level?: string,
) =>
  pino(
    {
      name: "braintrust-api",
      level: level ?? "info",
      hooks: {
        logMethod: function (args, method) {
          const span = getCurrentSpan();
          if (span) {
            const spanCtx = span.spanContext();
            const spanAttributes = {
              traceId: spanCtx.traceId,
              spanId: spanCtx.spanId,
              ...("name" in span ? { spanName: span.name } : undefined),
            };
            // Pino arguments are either {context}, msg, ...interpolatedArgs
            // or msg, ...interpolatedArgs. If there's an existing context,
            // we add the traceId and spanId to it. Otherwise, we create a new
            // context with the traceId and spanId.
            if (args.length >= 2 && typeof args[0] === "object") {
              const obj: Record<string, unknown> = args[0];
              Object.assign(obj, spanAttributes);
            } else {
              args = [
                // Typescript/Pino disagree about the shape of arguments
                // @ts-ignore
                spanAttributes,
                ...args,
              ];
            }
          }
          return method.apply(this, args);
        },
      },
      transport: PRETTY_LOGGING
        ? {
            target: "pino-pretty",
          }
        : undefined,
    },
    destination,
  );

let _logger: Logger | undefined;
export function initPinoLogger(destination?: DestinationStream) {
  _logger = createPinoLogger(destination, LOG_LEVEL || "info");

  // These logs are used during startup/initialization to show important environment configuration
  if (GIT_COMMIT) {
    _logger.info({ gitCommit: GIT_COMMIT }, "COMMIT");
  }
  if (DEPLOYMENT_MODE) {
    _logger.info({ deploymentMode: DEPLOYMENT_MODE }, "DEPLOYMENT_MODE");
  }
}

export function getLogger(): Logger {
  if (!_logger) {
    throw new Error("logger uninitialized");
  }
  return _logger;
}
