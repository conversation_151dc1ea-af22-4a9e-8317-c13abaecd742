// base OTEL boilerplate
import {
  trace,
  context,
  Span,
  Tracer,
  baggageEntryMetadataFromString,
  propagation,
  Attributes,
} from "@opentelemetry/api";

// Relative imports
import { DEPLOYMENT_MODE } from "../env";
import { z } from "zod";
import { getLogger } from "./logger";

const IS_DATADOG_LAMBDA_MODE =
  DEPLOYMENT_MODE === "lambda" && process.env.DD_API_KEY;

// Get the current active span
export function getTracer(): Tracer {
  return trace.getTracer("braintrust-api");
}

export function getCurrentSpan(): Span | undefined {
  return trace.getSpan(context.active());
}

export async function otelTraced<T>(
  name: string,
  fn: (span: Span) => Promise<T>,
): Promise<T> {
  return getTracer().startActiveSpan(name, async (span) => {
    try {
      return await fn(span);
    } finally {
      span.end();
    }
  });
}

export function otelWrapTraced<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  F extends (...args: any[]) => Promise<any>,
>(name: string, fn: F): F {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return ((...args: Parameters<F>) => otelTraced(name, () => fn(...args))) as F;
}

export const baggageSchema = z.object({
  traceparent: z.string().optional(),
  // From what I can tell, these do not actually get set (?)
  tracestate: z.string().optional(),
  baggage: z.string().optional(),
});
export type MadeBaggage = z.infer<typeof baggageSchema>;

export function makeBaggage({
  span,
  properties,
}: {
  span: Span;
  properties?: Record<string, string>;
}): MadeBaggage {
  const baggage = propagation.createBaggage(
    Object.fromEntries(
      Object.entries(properties ?? {}).map(([key, value]) => [
        key,
        { value, metadata: baggageEntryMetadataFromString("") },
      ]),
    ),
  );

  const ctxWithBaggage = trace.setSpan(context.active(), span);
  propagation.setBaggage(ctxWithBaggage, baggage);

  const carrier: Record<string, string> = {};
  propagation.inject(ctxWithBaggage, carrier);

  const ret = baggageSchema.parse(carrier);
  if (IS_DATADOG_LAMBDA_MODE && ret.traceparent) {
    // Infuriatingly, datadog's OTEL "mirage" NEVER marks spans as recording. So we need to
    // manually update the traceparent to include the sampling decision.
    const parts = ret.traceparent.split("-");
    if (parts.length === 4 && parts[3] === "00") {
      ret.traceparent = `${parts[0]}-${parts[1]}-${parts[2]}-01`;
    } else {
      getLogger().warn(
        { traceparent: ret.traceparent },
        "Datadog traceparent is malformed. Expect 4 parts and the last part to be 00 (unsampled)",
      );
    }
  }
  return ret;
}

export type LogCounterFn = (args: {
  name: string;
  value: number;
  attributes?: Attributes;
}) => void;

let _logCounter: LogCounterFn = (args) => {};
export function setLogCounterFn(fn: LogCounterFn) {
  _logCounter = fn;
}

export type LogHistogramFn = (args: {
  name: string;
  value: number;
  attributes?: Attributes;
}) => void;

let _logHistogram: LogHistogramFn = (args) => {};
export function setLogHistogramFn(fn: LogHistogramFn) {
  _logHistogram = fn;
}

export const logCounter: LogCounterFn = (args) => {
  _logCounter(args);
};
export const logHistogram: LogHistogramFn = (args) => {
  _logHistogram(args);
};
