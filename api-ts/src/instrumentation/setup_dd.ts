// Relative imports
import { GIT_COMMIT, DEPLOYMENT_MODE } from "../env";
import { LogHistogramFn, setLogCounterFn, setLogHistogramFn } from "./api";
import { LogCounterFn } from "./api";

if (DEPLOYMENT_MODE === "lambda" && process.env.DD_API_KEY) {
  const ddTrace = require("dd-trace");
  if (!ddTrace) {
    throw new Error("dd-trace is not installed");
  }
  const tracer = ddTrace.init({
    service: "braintrust-api",
    version: GIT_COMMIT,
  });
  const { TracerProvider } = tracer;
  const provider = new TracerProvider();
  provider.register();

  const { sendDistributionMetric } = require("datadog-lambda-js");
  const logCounter: LogCounterFn = ({ name, value, attributes }) => {
    // Datadog seems to suggest always using a distribution metric for counters.
    sendDistributionMetric(
      name,
      value,
      ...Object.entries(attributes ?? {}).map(
        ([key, value]) => `${key}:${value}`,
      ),
    );
  };

  const logHistogram: LogHistogramFn = ({ name, value, attributes }) => {
    sendDistributionMetric(
      name,
      value,
      ...Object.entries(attributes ?? {}).map(
        ([key, value]) => `${key}:${value}`,
      ),
    );
  };
  setLogCounterFn(logCounter);
  setLogHistogramFn(logHistogram);
}
