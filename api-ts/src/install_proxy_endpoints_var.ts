import type { Express, Request, Response } from "express";

export type InstallProxyEndpointsFn = (args: {
  app: Express;
  proxyServerPort: number;
  proxyPrefix: string;
}) => void;

export let dynamic_installProxyEndpoints: InstallProxyEndpointsFn = ({
  app,
}) => {
  const fail = (req: Request, res: Response) => {
    res
      .status(403)
      .send(
        "This is not a universal API. Upgrade your API URL to the universal API URL.",
      );
  };
  app.all("/api/v1/proxy/*", fail);
  app.all("/api/v1/function/*", fail);
  app.all("/api/function/*", fail);
};

export function setInstallProxyEndpoints(fn: InstallProxyEndpointsFn) {
  dynamic_installProxyEndpoints = fn;
}
