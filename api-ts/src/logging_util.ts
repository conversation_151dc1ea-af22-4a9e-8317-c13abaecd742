const SENSITIVE_KEY_REGEX = new RegExp(
  [
    "api-key",
    "api_key",
    "apikey",
    "auth",
    "authorization",
    "cookie",
    "credentials",
    "oidc_access_token",
    "oidc_claim_e2etoken",
    "sslclientcert",
    "x-access-token",
    "x-forwarded-authentication",
    "x-.*-proxy-auth",
    "e2etoken",
    "token",
  ]
    .map((x) => `(${x})`)
    .join("|"),
  "i",
);

const MAX_STRING_LENGTH = 100 * 1024;

export function redactSensitiveData(obj: unknown): unknown {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  } else if (Array.isArray(obj)) {
    return obj.map((x) => redactSensitiveData(x));
  }
  const out: { [key: string]: unknown } = {};
  for (const [key, val] of Object.entries(obj)) {
    if (typeof key === "string" && SENSITIVE_KEY_REGEX.test(key)) {
      out[key] = "{REDACTED}";
    } else if (typeof val === "string" && val.length > MAX_STRING_LENGTH) {
      out[key] = `<${val.length} bytes>`;
    } else {
      out[key] = redactSensitiveData(val);
    }
  }
  return out;
}
