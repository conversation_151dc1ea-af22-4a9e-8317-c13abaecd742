import { CodeB<PERSON>le, RuntimeContext } from "@braintrust/typespecs";
import {
  QUARANTINE_FUNCTION_ROLE,
  QUARANTINE_INVOKE_ROLE,
  QUARANTINE_PRIVATE_SUBNET_1_ID,
  QUARANTINE_PRIVATE_SUBNET_2_ID,
  QUARANTINE_PRIVATE_SUBNET_3_ID,
  QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP,
  QUARANTINE_PUB_PRIVATE_VPC_ID,
  QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME,
  CODE_BUNDLE_BUCKET_NAME,
} from "../env";
import { getPG } from "../db/pg";
import { createTempDir, InternalServerError } from "../util";
import * as fs from "fs";
import * as fsp from "fs/promises";
import archiver from "archiver";
import path from "path";
import {
  CreateFunctionCommand,
  CreateFunctionCommandInput,
} from "@aws-sdk/client-lambda";
import { v4 as uuidv4 } from "uuid";
import { getLambdaClient } from "../lambda";
import { z } from "zod";
import { Readable } from "stream";
import { fetchBundle } from "../proxy/bundle";
import StreamZip from "node-stream-zip";
import { wrapperCodeHash, wrapperPath } from "../proxy/code-wrappers";
import { getS3Client } from "../object-storage/s3";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getLogger } from "../instrumentation/logger";
import { PoolingBtPg, PoolingBtPgClient } from "@braintrust/local/bt-pg";

const _ALL_LAMBDA_RUNTIMES = [
  "nodejs18.x",
  "nodejs20.x",
  "nodejs22.x",

  "python3.8",
  "python3.9",
  "python3.10",
  "python3.11",
  "python3.12",
  "python3.13",
] as const;
type LambdaRuntime = (typeof _ALL_LAMBDA_RUNTIMES)[number];
export const RUNTIME_TO_LAMBDA_RUNTIME: Record<
  RuntimeContext["runtime"],
  Record<string, LambdaRuntime>
> = {
  node: {
    "18": "nodejs18.x",
    "20": "nodejs20.x",
    "21": "nodejs22.x",
    "22": "nodejs22.x",
  },
  python: {
    "3.8": "python3.8",
    "3.9": "python3.9",
    "3.10": "python3.10",
    "3.11": "python3.11",
    "3.12": "python3.12",
    "3.13": "python3.13",
  },
} as const;

const PUBLISH_RETRIES = 20;
const RATE_LIMIT_MAX_WAIT_MS = 45 * 1000; // Wait up to 45 seconds while retrying
const BACKOFF_EXPONENT = 2;
const LAMBDA_UPLOAD_LIMIT = 40 * 1024 * 1024; // 40MB

export function nextSleepTime(
  current: number,
  totalWaitedTime: number,
): number {
  return Math.max(
    // Make sure we sleep at least 10ms. Sometimes the random backoff logic can get wonky.
    Math.min(
      // If we have a rate limit reset time, use that. Otherwise, use a random backoff.
      // Sometimes, limitReset is 0 (errantly), so fall back to the random backoff in that case too.
      // And never sleep longer than 10 seconds or the remaining budget.
      current * (BACKOFF_EXPONENT - Math.random()),
      10 * 1000,
      RATE_LIMIT_MAX_WAIT_MS - totalWaitedTime,
    ),
    10,
  );
}

export function canUseLambdaQuarantine(): boolean {
  return !!(
    QUARANTINE_INVOKE_ROLE &&
    QUARANTINE_FUNCTION_ROLE &&
    QUARANTINE_PRIVATE_SUBNET_1_ID &&
    QUARANTINE_PRIVATE_SUBNET_2_ID &&
    QUARANTINE_PRIVATE_SUBNET_3_ID &&
    QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP &&
    QUARANTINE_PUB_PRIVATE_VPC_ID
  );
}

const runnableLambdaSchema = z.object({
  lambda_function_arn: z.string(),
  version: z.string(),
});
export type RunnableLambda = z.infer<typeof runnableLambdaSchema>;

export async function getOrgInlineCodeRunner({
  orgId,
  runtimeSpec,
}: {
  orgId: string;
  runtimeSpec: RuntimeContext;
}): Promise<RunnableLambda> {
  const codeHash = await wrapperCodeHash({
    runtimeSpec,
    inline: true,
    lambda: true,
  });
  const pino = getLogger().child({
    task: "getOrgInlineCodeRunner",
  });

  const db = getPG();
  const { rows } = await db.query(
    `SELECT
        lambda_function_arn, version
    FROM
        inline_code_runners
    WHERE
        org_id = $1 AND runtime = $2 AND runtime_version = $3 AND code_hash = $4
    `,
    [orgId, runtimeSpec.runtime, normalizeVersion(runtimeSpec), codeHash],
  );

  if (rows.length > 0) {
    const row = rows[0];
    return runnableLambdaSchema.parse(row);
  }

  pino.info("Initializing new inline code runner");
  const inlineCodeRunnerId = await getBaseLambdaRunner({
    conn: db,
    runtimeSpec,
  });
  pino.info(
    { lambdaFunctionId: inlineCodeRunnerId },
    "Waiting for lambda to be ready",
  );
  await waitForLambdaFunctionToBeReady(inlineCodeRunnerId);

  const start = Date.now();
  const lambda = await getLambdaClient();
  let publishedVersion: string | undefined;
  const { zipPath, cleanupCallback } = await prepareBaseBundleDir({
    wrapperPath: wrapperPath({ runtimeSpec, inline: true, lambda: true }),
    bundleStream: undefined,
    codeBundle: undefined,
  });
  try {
    for (let i = 0; i < PUBLISH_RETRIES; i++) {
      let delayMs = 100;
      const start = Date.now();
      try {
        const published = await lambda.updateFunctionCode({
          FunctionName: inlineCodeRunnerId,
          ZipFile: await fs.promises.readFile(zipPath),
          Publish: true,
        });
        pino.info({ published }, "Published version");
        publishedVersion = published.Version;
        break;
      } catch (error) {
        if (i === PUBLISH_RETRIES - 1) {
          throw error;
        }
        pino.error(
          { error, attempt: i + 1, totalAttempts: PUBLISH_RETRIES },
          `Intermittent error while publishing version. Will try again.`,
        );
        await new Promise((resolve) => setTimeout(resolve, delayMs));
        delayMs = nextSleepTime(delayMs, Date.now() - start);
      }
    }
  } finally {
    cleanupCallback();
  }

  if (!publishedVersion) {
    throw new InternalServerError("Failed to publish version");
  }

  await db.query(
    `INSERT INTO inline_code_runners (org_id, lambda_function_arn, version, runtime, runtime_version, code_hash)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (org_id, runtime, runtime_version)
        DO UPDATE SET lambda_function_arn = $2, version = $3, code_hash = $6
    `,
    [
      orgId,
      inlineCodeRunnerId,
      publishedVersion,
      runtimeSpec.runtime,
      normalizeVersion(runtimeSpec),
      codeHash,
    ],
  );
  pino.info({ elapsedMs: Date.now() - start }, "Published version");

  return { lambda_function_arn: inlineCodeRunnerId, version: publishedVersion };
}

export async function getBundledCodeRunner({
  codeBundle,
}: {
  codeBundle: CodeBundle;
}): Promise<RunnableLambda> {
  const pino = getLogger().child({
    task: "getBundledCodeRunner",
  });

  const codeHash = await wrapperCodeHash({
    runtimeSpec: codeBundle.runtime_context,
    inline: false,
    lambda: true,
  });

  const db = getPG();
  let committed = false;

  const conn = await db.connect();
  try {
    await conn.query("BEGIN");
    const { rows } = await conn.query(
      `SELECT
        path, lambda_function_arn, version, code_hash
    FROM
        code_bundles
    WHERE
      id = $1
    FOR UPDATE
    `,
      [codeBundle.bundle_id],
    );

    if (
      rows.length > 0 &&
      rows[0].code_hash === codeHash &&
      rows[0].lambda_function_arn &&
      rows[0].version
    ) {
      const row = rows[0];
      return runnableLambdaSchema.parse(row);
    } else if (rows.length === 0) {
      throw new InternalServerError("Bundle not found");
    }

    const row = rows[0];
    const bundlePath = z.string().parse(row.path);

    pino.info("Initializing new bundled code runner");

    let baseLambdaRunnerId: string | null = null;
    const runBaseRunnerLambda = (async () => {
      baseLambdaRunnerId = await getBaseLambdaRunner({
        conn,
        runtimeSpec: codeBundle.runtime_context,
      });
      pino.info(
        { lambdaFunctionId: baseLambdaRunnerId },
        "Waiting for lambda to be ready",
      );
      await waitForLambdaFunctionToBeReady(baseLambdaRunnerId);
    })();

    let zipFile: Buffer | null = null;
    let cleanupCallback: () => void = () => {};

    const runZipFile = (async () => {
      const prep = await prepareBaseBundleDir({
        wrapperPath: wrapperPath({
          runtimeSpec: codeBundle.runtime_context,
          inline: false,
          lambda: true,
        }),
        bundleStream: await fetchBundle({ bundlePath }),
        codeBundle,
      });
      zipFile = await fs.promises.readFile(prep.zipPath);
      cleanupCallback = prep.cleanupCallback;
    })();

    await Promise.all([runBaseRunnerLambda, runZipFile]);

    if (!baseLambdaRunnerId || !zipFile) {
      throw new InternalServerError("Failed to initialize bundled code runner");
    }

    const start = Date.now();
    const lambda = await getLambdaClient();
    let publishedVersion: string | undefined;

    let delayMs = 100;
    try {
      const getBundleInput = async (
        baseLambdaRunnerId: string,
        zipFile: Buffer,
      ) => {
        // Lambda has a limit of 50MB for the zip file.
        // If it's larger than LAMBDA_UPLOAD_LIMIT (currently 40MB), we upload it to S3.
        if (zipFile.byteLength >= LAMBDA_UPLOAD_LIMIT) {
          const s3 = await getS3Client();
          const s3Key = `lambda-code/${path.basename(baseLambdaRunnerId)}-${Date.now()}.zip`;

          pino.info(
            {
              sizeInMB: zipFile.byteLength / 1024 / 1024,
              bucket: CODE_BUNDLE_BUCKET_NAME,
              key: s3Key,
            },
            "Uploading large code bundle to S3",
          );
          await s3.send(
            new PutObjectCommand({
              Bucket: CODE_BUNDLE_BUCKET_NAME,
              Key: s3Key,
              Body: zipFile,
            }),
          );
          pino.info(
            {
              sizeInMB: zipFile.byteLength / 1024 / 1024,
              bucket: CODE_BUNDLE_BUCKET_NAME,
              key: s3Key,
            },
            "Uploaded large code bundle to S3",
          );

          return {
            S3Bucket: CODE_BUNDLE_BUCKET_NAME,
            S3Key: s3Key,
          };
        } else {
          return {
            ZipFile: zipFile,
          };
        }
      };

      for (let i = 0; i < PUBLISH_RETRIES; i++) {
        try {
          const published = await lambda.updateFunctionCode({
            FunctionName: baseLambdaRunnerId,
            ...(await getBundleInput(baseLambdaRunnerId, zipFile)),
            Publish: true,
          });
          pino.info({ published }, "Published version");
          publishedVersion = published.Version;
          break;
        } catch (error) {
          if (i === PUBLISH_RETRIES - 1) {
            throw error;
          }
          pino.error(
            { error, attempt: i + 1, totalAttempts: PUBLISH_RETRIES },
            `Intermittent error while publishing version. Will try again.`,
          );
          await new Promise((resolve) => setTimeout(resolve, delayMs));
          delayMs = nextSleepTime(delayMs, Date.now() - start);
        }
      }
    } finally {
      cleanupCallback();
    }

    if (!publishedVersion) {
      throw new InternalServerError("Failed to publish version");
    }

    await conn.query(
      `
    UPDATE code_bundles
    SET lambda_function_arn = $1, version = $2, code_hash = $3
    WHERE id = $4
    `,
      [baseLambdaRunnerId, publishedVersion, codeHash, codeBundle.bundle_id],
    );
    await conn.query("COMMIT");
    committed = true;
    pino.info({ elapsedMs: Date.now() - start }, "Published version");

    return {
      lambda_function_arn: baseLambdaRunnerId,
      version: publishedVersion,
    };
  } finally {
    try {
      if (!committed) {
        await conn.query("ROLLBACK");
      }
    } finally {
      conn.release();
    }
  }
}

export async function getBaseLambdaRunner({
  conn,
  runtimeSpec,
}: {
  conn: PoolingBtPg | PoolingBtPgClient;
  runtimeSpec: RuntimeContext;
}): Promise<string> {
  if (!canUseLambdaQuarantine()) {
    throw new InternalServerError("Lambda quarantine is not enabled");
  }

  const runtime = getLambdaRuntimeVersion(runtimeSpec);
  if (!runtime) {
    throw new Error(`Unsupported runtime: ${runtimeSpec.runtime}`);
  }

  const { rows } = await conn.query(
    `WITH updated AS (
      UPDATE lambda_functions
      SET used_at = now()
      WHERE id IN (
        SELECT id FROM lambda_functions
        WHERE runtime = $1 AND runtime_version = $2 AND vpc_id = $3
        ORDER BY used_at ASC
        LIMIT 1
      )
      RETURNING id
    )
    SELECT lambda_functions.arn FROM lambda_functions
    WHERE runtime = $1 AND runtime_version = $2 AND vpc_id = $3
    ORDER BY id IN (SELECT id FROM updated) DESC, used_at ASC
    LIMIT $4`,
    [
      runtimeSpec.runtime,
      normalizeVersion(runtimeSpec),
      QUARANTINE_PUB_PRIVATE_VPC_ID,
      QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME *
        2 /* this is just for good measure*/,
    ],
  );

  if (rows.length < QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME) {
    const lambdaFunctionArn = await createBaseLambdaFunction(
      runtimeSpec,
      runtime,
    );
    await conn.query(
      `INSERT INTO lambda_functions (arn, runtime, runtime_version, vpc_id) VALUES ($1, $2, $3, $4)`,
      [
        lambdaFunctionArn,
        runtimeSpec.runtime,
        normalizeVersion(runtimeSpec),
        QUARANTINE_PUB_PRIVATE_VPC_ID,
      ],
    );
    return lambdaFunctionArn;
  } else {
    return z.object({ arn: z.string() }).parse(rows[0]).arn;
  }
}

async function createBaseLambdaFunction(
  runtimeSpec: RuntimeContext,
  lambdaRuntime: LambdaRuntime,
): Promise<string> {
  const pino = getLogger().child({
    task: "createBaseLambdaFunction",
  });

  if (
    !QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP ||
    !QUARANTINE_PRIVATE_SUBNET_1_ID ||
    !QUARANTINE_PRIVATE_SUBNET_2_ID ||
    !QUARANTINE_PRIVATE_SUBNET_3_ID
  ) {
    throw new InternalServerError("Lambda quarantine is not enabled");
  }

  const { zipPath, cleanupCallback } = await prepareBaseBundleDir({
    wrapperPath: wrapperPath({ runtimeSpec, inline: true, lambda: true }),
    bundleStream: undefined,
    codeBundle: undefined,
  });
  try {
    const params: CreateFunctionCommandInput = {
      FunctionName: `Quarantine-${lambdaRuntime.replace(".", "-")}-${uuidv4().slice(0, 8)}`,
      Runtime: lambdaRuntime,
      Role: QUARANTINE_FUNCTION_ROLE,
      Handler: "index.handler",
      Code: {
        ZipFile: await fs.promises.readFile(zipPath),
      },
      MemorySize: 1024,
      Timeout: 900,
      VpcConfig: {
        SecurityGroupIds: [QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP],
        SubnetIds: [
          QUARANTINE_PRIVATE_SUBNET_1_ID,
          QUARANTINE_PRIVATE_SUBNET_2_ID,
          QUARANTINE_PRIVATE_SUBNET_3_ID,
        ],
      },
      TracingConfig: {
        Mode: "Active",
      },
      Tags: {
        BraintrustQuarantine: "true",
        BraintrustVersion: "1",
        IsQuarantine: "true",
      },
    };

    const lambda = await getLambdaClient();
    const start = Date.now();
    const command = new CreateFunctionCommand(params);
    const createResult = await lambda.send(command);
    if (!createResult.FunctionArn) {
      throw new InternalServerError(
        "Failed to create lambda function (empty arn)",
      );
    }
    pino.info(
      {
        functionArn: createResult.FunctionArn,
        elapsedMs: Date.now() - start,
      },
      "Lambda function created successfully",
    );

    return createResult.FunctionArn;
  } finally {
    cleanupCallback();
  }
}

const LAMBDA_FUNCTION_READY_TIMEOUT = 10 * 60 * 1000; // 10 minutes

export async function waitForLambdaFunctionToBeReady(
  lambdaFunctionArn: string,
) {
  // Wait for the function to be ready
  const startStatus = Date.now();
  const lambda = await getLambdaClient();
  let status = await lambda.getFunctionConfiguration({
    FunctionName: lambdaFunctionArn,
  });
  const start = Date.now();
  let delayMs = 1000;

  while (status.State !== "Active") {
    if (Date.now() - start > LAMBDA_FUNCTION_READY_TIMEOUT) {
      getLogger().warn(
        { elapsedMs: Date.now() - start },
        "Lambda function failed to become ready. Will try to use it anyway",
      );
      return;
    }

    await new Promise((resolve) => setTimeout(resolve, delayMs));
    delayMs = nextSleepTime(delayMs, Date.now() - start);

    status = await lambda.getFunctionConfiguration({
      FunctionName: lambdaFunctionArn,
    });
  }
  getLogger().info(
    { elapsedMs: Date.now() - startStatus },
    "Lambda function ready",
  );
}

function normalizeVersion(runtime: RuntimeContext) {
  if (runtime.runtime === "node") {
    return runtime.version.split(".")[0];
  } else if (runtime.runtime === "python") {
    return runtime.version;
  } else {
    throw new InternalServerError(`Unsupported runtime: ${runtime}`);
  }
}

function getLambdaRuntimeVersion(runtime: RuntimeContext): LambdaRuntime {
  const lambdaRuntime =
    RUNTIME_TO_LAMBDA_RUNTIME[runtime.runtime]?.[normalizeVersion(runtime)];

  if (!lambdaRuntime) {
    throw new InternalServerError(
      `Unsupported runtime: ${runtime.runtime} ${runtime.version}`,
    );
  }
  return lambdaRuntime;
}

async function prepareBaseBundleDir({
  wrapperPath,
  bundleStream,
  codeBundle,
}: {
  wrapperPath: string;
  bundleStream: Readable | undefined;
  codeBundle: CodeBundle | undefined;
}) {
  const { path: tmpDir, cleanupCallback } = await createTempDir();

  const extName = path.extname(wrapperPath);
  const indexFile = "index" + extName;
  let bundleFile: string | undefined = undefined;
  let codeBundleFile: string | undefined = undefined;
  const zipPath = path.join(tmpDir, "index.zip");
  try {
    await fsp.copyFile(wrapperPath, path.join(tmpDir, indexFile));

    if (bundleStream && extName === ".js") {
      bundleFile = "bundle" + extName;
      await fsp.writeFile(path.join(tmpDir, bundleFile), bundleStream);
    }

    if (codeBundle) {
      codeBundleFile = "code_bundle.json";
      await fsp.writeFile(
        path.join(tmpDir, codeBundleFile),
        JSON.stringify(codeBundle),
      );
    }

    const zipStream = fs.createWriteStream(zipPath);
    const archive = archiver("zip", {
      zlib: { level: 9 },
    });
    archive.pipe(zipStream);

    if (bundleStream && extName == ".py") {
      const bundleZip = path.join(tmpDir, "bundle.zip");
      const ws = fs.createWriteStream(bundleZip);
      bundleStream.pipe(ws);
      await new Promise((resolve, reject) => {
        bundleStream.on("end", resolve);
        bundleStream.on("error", reject);
      });

      const zip = new StreamZip.async({ file: bundleZip });
      const entries = await zip.entries();
      for (const name in entries) {
        const entry = entries[name];
        if (entry.isFile) {
          const data = await zip.entryData(entry);
          archive.append(data, { name: entry.name });
        }
      }
    }

    archive.file(path.join(tmpDir, indexFile), { name: indexFile });

    if (bundleFile) {
      archive.file(path.join(tmpDir, bundleFile), { name: bundleFile });
    }
    if (codeBundleFile) {
      archive.file(path.join(tmpDir, codeBundleFile), { name: codeBundleFile });
    }

    if (!bundleStream && extName == ".py") {
      const pkgDir = path.join(__dirname, "vm", "pkg");
      const entries = await fsp.readdir(pkgDir, { withFileTypes: true });
      for (const entry of entries) {
        if (entry.isDirectory()) {
          archive.directory(path.join(pkgDir, entry.name), entry.name);
        } else if (entry.isFile()) {
          archive.file(path.join(pkgDir, entry.name), { name: entry.name });
        }
      }
    }

    // Can remove this once we are sure we don't need restricted python
    // if (indexFile === "index.py") {
    //   archive.directory(
    //     path.join(__dirname, "vm", "RestrictedPython"),
    //     "RestrictedPython",
    //   );
    // }

    await archive.finalize();

    await new Promise((resolve, reject) => {
      zipStream.on("error", reject);
      zipStream.on("close", resolve);
    });
  } catch (error) {
    cleanupCallback();
    throw error;
  }

  return { zipPath, cleanupCallback };
}

export function lambdaVersionedArn({
  lambda_function_arn,
  version,
}: {
  lambda_function_arn: string;
  version: string;
}) {
  return `${lambda_function_arn}:${version}`;
}
