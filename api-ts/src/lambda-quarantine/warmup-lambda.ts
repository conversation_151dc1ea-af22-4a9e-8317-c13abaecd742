import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "aws-lambda";
import { getPG } from "../db/pg";

import {
  lambdaRequestTracker,
  pinoLambdaDestination,
  StructuredLogFormatter,
} from "pino-lambda";
import { getLogger, initPinoLogger } from "../instrumentation/logger";

initPinoLogger(
  pinoLambdaDestination({
    formatter: new StructuredLogFormatter(),
  }),
);
const withPinoRequest = lambdaRequestTracker();

import {
  getBaseLambdaRunner,
  RUNTIME_TO_LAMBDA_RUNTIME,
  waitForLambdaFunctionToBeReady,
} from "./pool";
import {
  QUARANTINE_PUB_PRIVATE_VPC_ID,
  QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME,
} from "../env";
import { z } from "zod";

export const handler: EventBridgeHandler<
  "Warmup Lambda",
  "Warmup Lambda",
  void
> = async (event, context) => {
  withPinoRequest(event, context);
  const pino = getLogger();

  const db = getPG();
  const promises: Promise<unknown>[] = [];

  for (const [runtimeName, runtimeVersions] of Object.entries(
    RUNTIME_TO_LAMBDA_RUNTIME,
  )) {
    for (const runtimeVersion of Object.keys(runtimeVersions)) {
      const { rows } = await db.query(
        `SELECT COUNT(*) c FROM lambda_functions
    WHERE runtime = $1 AND runtime_version = $2 AND vpc_id = $3`,
        [runtimeName, runtimeVersion, QUARANTINE_PUB_PRIVATE_VPC_ID],
      );
      const n = z.number().parse(rows[0].c);
      pino.info(
        { runtime: runtimeName, version: runtimeVersion, count: n },
        "WARMING UP",
      );
      for (let i = n; i < QUARANTINE_TARGET_FUNCTIONS_PER_RUNTIME; i++) {
        promises.push(
          (async () => {
            const arn = await getBaseLambdaRunner({
              conn: db,
              runtimeSpec: {
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                runtime: runtimeName as "node" | "python",
                version: runtimeVersion,
              },
            });
            pino.info({ lambdaArn: arn }, "Waiting for lambda to be ready");
            await waitForLambdaFunctionToBeReady(arn);
            pino.info({ lambdaArn: arn }, "Lambda ready");
          })(),
        );
      }
    }
  }

  const start = Date.now();
  pino.info("Awaiting warmups");
  await Promise.all(promises);
  pino.info({ elapsedMs: Date.now() - start }, "Warmups complete");
};
