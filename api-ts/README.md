# API Server

## File Structure

There is a separate `proxy/` subdirectory containing the endpoints that require
streaming responses.

- In the containerized deployment with Docker, we serve both the api-ts and
  proxy requests from the same container.
- In the Cloud Formation deployment, we put the proxy into a separate lambda
  function. The proxy needs to do streaming responses so it cannot be part of
  the api-ts lambda, which is behind AWS API Gateway.
