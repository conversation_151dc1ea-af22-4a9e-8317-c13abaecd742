import asyncio
import importlib
import inspect
import json
import os
import sys

import braintrust
from braintrust.framework import Dict<PERSON><PERSON>H<PERSON>s, _set_lazy_load


def _call_user_fn_args(fn, kwargs):
    try:
        signature = inspect.signature(fn)
    except:
        return [], kwargs

    accepts_kwargs = any(p.kind == inspect.Parameter.VAR_KEYWORD for p in signature.parameters.values())

    positional_args = []
    final_kwargs = {}

    for name, param in signature.parameters.items():
        # VAR_POSITIONAL is *args
        # VAR_KEYWORD is **kwargs
        # We don't want to use eithers' name
        if param.kind == inspect.Parameter.VAR_POSITIONAL or param.kind == inspect.Parameter.VAR_KEYWORD:
            continue

        if name in ["input", "output", "expected", "metadata"]:
            final_kwargs[name] = kwargs.pop(name, None)
        elif name in kwargs:
            final_kwargs[name] = kwargs.pop(name)
        else:
            remaining_kwargs = list(kwargs.keys())
            if len(remaining_kwargs) > 0:
                next_arg = remaining_kwargs[0]
                final_kwargs[name] = kwargs.pop(next_arg)

    if accepts_kwargs:
        final_kwargs.update(kwargs)

    return positional_args, final_kwargs


class ForwardingStdout:
    def __init__(self, old_stream, stream_name, events):
        self.old_stream = old_stream
        self.stream_name = stream_name
        self.events = events

    def write(self, text):
        if text:  # Only forward non-empty writes
            self.events.append(
                serialize_sse_event(
                    "console",
                    json.dumps(
                        {
                            "stream": self.stream_name,
                            "message": text.rstrip(""),
                        }
                    ),
                )
            )


async def async_main(arg, hook_data, loc, parent):
    with _set_lazy_load(True):
        importlib.import_module("register")

    type_ = loc["type"]
    if type_ != "function":
        raise ValueError(f"Unsupported position type: {type_}")
    handler = braintrust.global_.functions[loc["index"]].handler

    if arg is None:
        arg = {}
    elif not isinstance(arg, dict):
        arg = {"input": arg}

    hooks = DictEvalHooks(**{"expected": None, "metadata": None, **(hook_data or {})}) if hook_data else None

    if hooks is not None:
        arg["hooks"] = hooks

    call_args, call_kwargs = _call_user_fn_args(handler, arg)

    span = (
        braintrust.start_span(
            parent=parent, name="python runtime", type=braintrust.SpanTypeAttribute.FUNCTION, input=arg
        )
        if parent
        else braintrust.logger.NOOP_SPAN
    )
    if hooks is not None:
        hooks.set_span(span)

    # Replace stdout with our forwarding version
    events = []
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    sys.stdout = ForwardingStdout(old_stdout, "stdout", events)
    sys.stderr = ForwardingStdout(old_stderr, "stderr", events)

    try:
        with span:
            result = handler(*call_args, **call_kwargs)

            if asyncio.isfuture(result):
                result = await asyncio.wait_for(result, timeout=None)
            elif asyncio.iscoroutine(result):
                result = await result

        span.log(output=result)
        return result
    finally:
        sys.stdout = old_stdout
        sys.stderr = old_stderr

        for event in events:
            add_event(event)


def serialize_sse_event(event, data):
    return f"""\
event: {event}
data: {data}


"""


_EVENTS = None

INLINE_INVOKE_TIMEOUT_MS = 30000


def main():
    input_string = sys.stdin.read()
    event = json.loads(input_string)

    arg = event["input"]
    hook_data = event.get("hook_data", {})
    loc = event["location"]
    timeout_ms = event.get("timeout_ms", INLINE_INVOKE_TIMEOUT_MS)
    parent = event.get("parent")
    lib_path = event.get("lib_path")

    if lib_path:
        sys.path.insert(0, lib_path)

    return asyncio.run(asyncio.wait_for(async_main(arg, hook_data, loc, parent), timeout_ms / 1000))


# In lambda mode, we can't use print statements, so we use this function to add events to a list
# that we'll join at the end.
def add_event(event):
    global _EVENTS
    if _EVENTS is not None:
        _EVENTS.append(event)
    else:
        print(event)


def handler(event, context):
    global _EVENTS
    _EVENTS = []

    arg = event["input"]
    hook_data = event.get("hook_data", {})
    loc = event["location"]
    timeout_ms = event.get("timeout_ms", INLINE_INVOKE_TIMEOUT_MS)
    parent = event.get("parent")

    runtime_env = event.get("env", {})
    for key, value in runtime_env.items():
        os.environ[key] = value

    try:
        res = asyncio.run(asyncio.wait_for(async_main(arg, hook_data, loc, parent), timeout_ms / 1000))
        add_event(serialize_sse_event("json_delta", json.dumps(res)))
    except Exception as e:
        add_event(serialize_sse_event("error", json.dumps(str(e))))

    return "".join(_EVENTS)


if __name__ == "__main__":
    try:
        result = main()
        add_event(serialize_sse_event("json_delta", json.dumps(result)))
    except Exception as e:
        add_event(serialize_sse_event("error", json.dumps(str(e))))
        exit(1)
