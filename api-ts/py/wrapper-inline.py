import asyncio
import inspect
import io
import json
import os
import sys

from braintrust.framework import Dict<PERSON>valHooks

# NOTE: This is commented out, but we can remove these comments (and the stuff below), as well as the code
# that packages up RestrictedPython, once we are certain that this approach works.
#
# from RestrictedPython import compile_restricted, limited_builtins, safe_builtins, utility_builtins
# from RestrictedPython.Eval import default_guarded_getitem, default_guarded_getiter
# from RestrictedPython.Guards import guarded_iter_unpack_sequence

# The non-stdlib packages here *must* be in //api-ts/requirements.txt.
_SAFE_MODULES = frozenset(
    ("math", "json", "asyncio", "typing", "re", "autoevals", "braintrust", "requests", "openai", "anthropic")
)


def _safe_import(name, *args, **kwargs):
    if name == "os":

        class SafeOSModule:
            def __init__(self):
                self.environ = {**os.environ}

        return SafeOSModule()
    elif name == "sys":

        class SafeSysModule:
            def __init__(self):
                self.stdout = sys.stdout
                self.stderr = sys.stderr

        return SafeSysModule()
    elif name not in _SAFE_MODULES:
        raise ValueError(f"Cannot import module {name!r}")
    return __import__(name, *args, **kwargs)


def serialize_sse_event(event, data):
    return f"""\
event: {event}
data: {data}


"""


class StreamingPrintCollector:
    def __init__(self, _getattr_=None):
        self._getattr_ = _getattr_

    def _call_print(self, *objects, **kwargs):
        stream = "stdout" if kwargs.pop("file", sys.stdout) == sys.stdout else "stderr"
        string_io = io.StringIO()
        print(*objects, file=string_io, **kwargs)
        console_event = serialize_sse_event(
            "console",
            json.dumps(
                {
                    "stream": stream,
                    "message": string_io.getvalue(),
                }
            ),
        )
        add_event(console_event)


def _call_user_fn_args(fn, kwargs):
    try:
        signature = inspect.signature(fn)
    except:
        return [], kwargs

    accepts_kwargs = any(p.kind == inspect.Parameter.VAR_KEYWORD for p in signature.parameters.values())

    positional_args = []
    final_kwargs = {}

    for name, param in signature.parameters.items():
        if param.kind == inspect.Parameter.VAR_KEYWORD or param.kind == inspect.Parameter.VAR_POSITIONAL:
            continue

        if name in ["input", "output", "expected", "metadata"]:
            final_kwargs[name] = kwargs.pop(name, None)
        elif name in kwargs:
            final_kwargs[name] = kwargs.pop(name)
        else:
            remaining_kwargs = list(kwargs.keys())
            if len(remaining_kwargs) > 0:
                next_arg = remaining_kwargs[0]
                final_kwargs[name] = kwargs.pop(next_arg)

    if accepts_kwargs:
        final_kwargs.update(kwargs)

    return positional_args, final_kwargs


async def async_main(code, arg, hook_data):
    byte_code = compile(code, filename="<inline>", mode="exec")
    globals = {
        "__builtins__": {
            # In certain python environments, __builtins__ is not a dict.
            **(__builtins__.__dict__ if hasattr(__builtins__, "__dict__") else __builtins__),
            "__import__": _safe_import,
            "print": StreamingPrintCollector()._call_print,
        },
        # # https://restrictedpython.readthedocs.io/en/latest/usage/policy.html#print
        # "_print_": StreamingPrintCollector,
        # "_getattr_": getattr,
        # # https://restrictedpython.readthedocs.io/en/latest/usage/basic_usage.html#necessary-setup
        # "__metaclass__": type,
        "__name__": "handler",
        # "_getiter_": default_guarded_getiter,
        # "_iter_unpack_sequence_": guarded_iter_unpack_sequence,
        # # Trial and error
        # "str": str,
        # "float": float,
        # "int": int,
        # "bool": bool,
        # "list": list,
        # "dict": dict,
        # "set": set,
        # "len": len,
        # "any": any,
        # # Referenced in https://stackoverflow.com/questions/71745931/restrictedpython-call-other-functions-within-user-specified-code
        # # but similar to getattr and getiter above
        # "_getitem_": default_guarded_getitem,
    }
    exec(byte_code, globals)

    handler = globals["handler"]

    if arg is None:
        arg = {}
    elif not isinstance(arg, dict):
        arg = {"input": arg}

    hooks = DictEvalHooks(**{"expected": None, "metadata": None, **(hook_data or {})}) if hook_data else None
    if hooks is not None:
        arg["hooks"] = hooks

    call_args, call_kwargs = _call_user_fn_args(handler, arg)
    result = handler(*call_args, **call_kwargs)

    if asyncio.isfuture(result):
        result = asyncio.wait_for(result, timeout=None)
    elif asyncio.iscoroutine(result):
        result = await result

    return result


INLINE_INVOKE_TIMEOUT_MS = 240 * 1000


def main():
    input_string = sys.stdin.read()
    input_json = json.loads(input_string)

    code = input_json["code"]
    arg = input_json["input"]
    hook_data = input_json.get("hook_data", {})
    timeout_ms = input_json.get("timeout_ms", INLINE_INVOKE_TIMEOUT_MS)

    return asyncio.run(asyncio.wait_for(async_main(code, arg, hook_data), timeout_ms / 1000))


_EVENTS = None


# In lambda mode, we can't use print statements, so we use this function to add events to a list
# that we'll join at the end.
def add_event(event):
    global _EVENTS
    if _EVENTS is not None:
        _EVENTS.append(event)
    else:
        print(event)


def handler(event, context):
    global _EVENTS
    _EVENTS = []

    code = event["code"]
    arg = event["input"]
    hook_data = event.get("hook_data", {})
    timeout_ms = event.get("timeout_ms", INLINE_INVOKE_TIMEOUT_MS)

    runtime_env = event.get("env", {})
    for key, value in runtime_env.items():
        os.environ[key] = value

    try:
        result = asyncio.run(asyncio.wait_for(async_main(code, arg, hook_data), timeout_ms / 1000))
        add_event(serialize_sse_event("json_delta", json.dumps(result)))
    except Exception as e:
        add_event(serialize_sse_event("error", json.dumps(str(e))))

    return "".join(_EVENTS)


if __name__ == "__main__":
    try:
        result = main()
        add_event(serialize_sse_event("json_delta", json.dumps(result)))
    except Exception as e:
        add_event(serialize_sse_event("error", json.dumps(str(e))))
        exit(1)
