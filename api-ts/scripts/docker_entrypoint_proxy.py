import os
import signal
import subprocess
import sys

REMAP_ENV_VARS = dict(
    BRAINTRUST_APP_URL="ALLOWED_ORIGIN",
    BRAINTRUST_APP_PUBLIC_URL="PUBLIC_ALLOWED_ORIGIN",
)


def sigterm_handler(signum, frame):
    del signum, frame
    print(f"Captured sigterm. Exiting container.", file=sys.stderr)
    sys.exit(0)


if __name__ == "__main__":
    signal.signal(signal.SIGTERM, sigterm_handler)

    bt_env = os.environ.copy()
    for env_var, remap_name in REMAP_ENV_VARS.items():
        env_value = bt_env.get(env_var)
        if env_value is not None and bt_env.get(remap_name) is None:
            bt_env[remap_name] = env_value

    proc = subprocess.Popen(
        ["node", "local-proxy/local.js"],
        cwd="/braintrust/api-ts",
        env=bt_env,
        stdout=sys.stdout,
        stderr=sys.stderr,
    )
    proc.wait()
