import os
import signal
import subprocess
import sys

REMAP_ENV_VARS = dict(
    BRAINTRUST_APP_URL="ALLOWED_ORIGIN",
    BRAINTRUST_APP_PUBLIC_URL="PUBLIC_ALLOWED_ORIGIN",
)


def sigterm_handler(signum, frame):
    del signum, frame
    print(f"Captured sigterm. Exiting container.", file=sys.stderr)
    sys.exit(0)


if __name__ == "__main__":
    signal.signal(signal.SIGTERM, sigterm_handler)

    bt_env = os.environ.copy()
    for env_var, remap_name in REMAP_ENV_VARS.items():
        env_value = bt_env.get(env_var)
        if env_value is not None and bt_env.get(remap_name) is None:
            bt_env[remap_name] = env_value

    # Run any api DB migrations.
    subprocess.run(
        ["python", "api-schema/lambda_function.py", "--execute", "--run-all-in-foreground"],
        cwd="/braintrust",
        env=bt_env,
        check=True,
    )

    node_args = ["node"]
    # Allow user to set NODE_MEMORY_PERCENT to override the default memory limit
    # Node by default will use only half of the memory available to the container up to 4GB max
    memory_percent = os.environ.get("NODE_MEMORY_PERCENT")
    if memory_percent:
        try:
            percent = float(memory_percent) / 100.0
            memory_in_bytes = None
            # Try to get memory limit from cgroup v1 or v2
            cgroup_v1_path = "/sys/fs/cgroup/memory/memory.limit_in_bytes"
            cgroup_v2_path = "/sys/fs/cgroup/memory.max"

            if os.path.exists(cgroup_v1_path):
                with open(cgroup_v1_path, "r") as f:
                    memory_in_bytes = int(f.read().strip())
            elif os.path.exists(cgroup_v2_path):
                with open(cgroup_v2_path, "r") as f:
                    content = f.read().strip()
                    # cgroup v2 uses "max" to indicate no limit was set
                    if content != "max":
                        memory_in_bytes = int(content)
            else:
                # If no cgroups found, try to get total system memory from /proc/meminfo
                with open("/proc/meminfo", "r") as f:
                    for line in f:
                        # "MemTotal:       16417532 kB"
                        if line.startswith("MemTotal:"):
                            memory_in_bytes = int(line.split()[1]) * 1024

            if memory_in_bytes:
                memory_in_mb = memory_in_bytes // (1024 * 1024)
                print(f"Setting memory limit to {memory_in_mb}MB", file=sys.stderr)
                node_args.append(f"--max-old-space-size={int(memory_in_mb * percent)}")
        except (IOError, ValueError):
            pass

    node_args.append("local/local.js")

    # Launch api-ts server with memory limit
    subprocess.run(
        node_args,
        cwd="/braintrust/api-ts",
        env=bt_env,
        check=True,
    )
