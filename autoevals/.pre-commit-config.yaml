repos:
  - repo: "https://github.com/pre-commit/pre-commit-hooks"
    rev: v4.4.0
    hooks:
      - id: end-of-file-fixer
      - id: trailing-whitespace
  - repo: "https://github.com/psf/black"
    rev: 22.6.0
    hooks:
      - id: black
        files: ./
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.12.7
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.1
    hooks:
      - id: codespell
        exclude: >
          (?x)^(
              .*\.(json|prisma|yaml)
          )$
        args:
          - "-L"
          - "rouge,afterall"

  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.3.2
    hooks:
      - id: prettier
        exclude: ^(extension/|.*\.json$)
