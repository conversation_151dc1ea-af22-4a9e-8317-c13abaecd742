name: js

on:
  pull_request:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        # duckdb has an incredibly slow install with 24.x
        node-version: [20.x, 22.x]

    steps:
      - uses: actions/checkout@v3
      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            !node_modules/.cache/turbo
          key: ${{ matrix.runner }}-${{ matrix.node_version }}-node-${{ env.nodeModulesCacheHash }}
          restore-keys: |
            ${{ matrix.runner }}-${{ matrix.node_version }}-node-
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - uses: pnpm/action-setup@v4
      - run: pnpm install
      - run: pnpm run test
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENAI_BASE_URL: ${{ secrets.OPENAI_BASE_URL }}
          BRAINTRUST_API_KEY: ${{ secrets.BRAINTRUST_API_KEY }}
      - run: pnpm run build
