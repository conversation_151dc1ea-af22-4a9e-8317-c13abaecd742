{"name": "@braintrust/local", "version": "0.0.1", "description": "Internal utilities for braintrust", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup --clean --dts", "watch": "tsup --watch --dts", "clean": "rm -r dist/*", "test": "vitest"}, "bin": {"doc-index": "./doc-index/dist/cli.js"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}, "./app-schema": {"types": "./app-schema/dist/index.d.ts", "import": "./app-schema/dist/index.mjs", "module": "./app-schema/dist/index.mjs", "require": "./app-schema/dist/index.js"}, "./api-schema": {"types": "./api-schema/dist/index.d.ts", "import": "./api-schema/dist/index.mjs", "module": "./api-schema/dist/index.mjs", "require": "./api-schema/dist/index.js"}, "./copilot": {"types": "./copilot/dist/index.d.ts", "import": "./copilot/dist/index.mjs", "module": "./copilot/dist/index.mjs", "require": "./copilot/dist/index.js"}, "./query": {"types": "./query/dist/index.d.ts", "import": "./query/dist/index.mjs", "module": "./query/dist/index.mjs", "require": "./query/dist/index.js"}, "./functions": {"types": "./functions/dist/index.d.ts", "import": "./functions/dist/index.mjs", "module": "./functions/dist/index.mjs", "require": "./functions/dist/index.js"}, "./dev": {"types": "./dev/dist/index.d.ts", "import": "./dev/dist/index.mjs", "module": "./dev/dist/index.mjs", "require": "./dev/dist/index.js"}, "./yaml": {"types": "./yaml/index.d.ts", "import": "./yaml/index.js", "module": "./yaml/index.js"}, "./bt-pg": {"types": "./bt-pg/dist/index.d.ts", "import": "./bt-pg/dist/index.js", "module": "./bt-pg/dist/index.js", "require": "./bt-pg/dist/index.js"}, "./optimization": {"types": "./optimization/dist/index.d.ts", "import": "./optimization/dist/index.mjs", "module": "./optimization/dist/index.mjs", "require": "./optimization/dist/index.js"}, "./optimization/tools": {"types": "./optimization/dist/tools.d.ts", "import": "./optimization/dist/tools.mjs", "module": "./optimization/dist/tools.mjs", "require": "./optimization/dist/tools.js"}, "./doc-index": {"types": "./doc-index/dist/index.d.ts", "import": "./doc-index/dist/index.mjs", "module": "./doc-index/dist/index.mjs", "require": "./doc-index/dist/index.js"}, "./doc-index/dev": {"types": "./doc-index/dist/dev.d.ts", "import": "./doc-index/dist/dev.mjs", "module": "./doc-index/dist/dev.mjs", "require": "./doc-index/dist/dev.js"}, "./typespecs": {"types": "./typespecs/dist/index.d.ts", "import": "./typespecs/dist/index.mjs", "module": "./typespecs/dist/index.mjs", "require": "./typespecs/dist/index.js"}}, "files": ["dist/**/*", "app-schema/**/*", "api-schema/**/*", "query/**/*", "functions/**/*", "dev/**/*", "copilot/**/*", "bt-pg/**/*", "optimization/**/*", "doc-index/**/*", "typespecs/**/*"], "devDependencies": {"@types/diff": "^5.0.9", "@types/figlet": "^1.7.0", "@types/mustache": "^4.2.5", "@types/pluralize": "^0.0.30", "boxen": "^8.0.1", "chalk": "^4.1.2", "figlet": "^1.8.1", "inquirer": "^12.6.1", "inquirer-command-prompt": "^0.1.0", "minimatch": "^9.0.3", "ora": "^8.2.0", "tsup": "^8.4.0", "tsx": "^3.14.0", "typescript": "^5.3.3", "vitest": "^2.1.9"}, "dependencies": {"@braintrust/btql": "workspace:*", "@braintrust/typespecs": "workspace:^", "@braintrust/proxy": "workspace:^", "@turbopuffer/turbopuffer": "^0.10.17", "autoevals": "workspace:*", "braintrust": "workspace:*", "commander": "^14.0.0", "diff": "^5.1.0", "dotenv": "^17.2.1", "eventsource-parser": "^1.1.2", "glob": "^11.0.3", "gray-matter": "^4.0.3", "lexorank": "^1.0.5", "mustache": "^4.2.0", "openai": "4.55.9", "pg": "^8.11.3", "pluralize": "^8.0.0", "slugify": "^1.6.6", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.5"}}