import { ID_FIELD, TRANSACTION_ID_FIELD } from "braintrust/util";
import { z } from "zod";

// Perhaps the types in this file can be replaced with:
// - typespecs/src/api_types.ts
// - typespecs/src/common_types.ts
export const objectLookupSupportedType = z.enum([
  "experiment",
  "dataset",
  "project_logs",
  "prompt_session",
]);
export type ObjectLookupSupportedType = z.infer<
  typeof objectLookupSupportedType
>;
export const getObjectLookupTypeDescription = (t: ObjectLookupSupportedType) =>
  t === "project_logs" ? "log" : t;

export const objectReferenceSchema = z.object({
  object_type: objectLookupSupportedType,
  object_id: z.string().uuid(),
  [ID_FIELD]: z.string(),
  // Populated by the backend but not currently used in frontend.
  [TRANSACTION_ID_FIELD]: z.string().optional(),
});

export type ObjectReference = z.infer<typeof objectReferenceSchema>;

export const aclType = (t: ObjectLookupSupportedType) =>
  t === "project_logs" ? "project_log" : t;
