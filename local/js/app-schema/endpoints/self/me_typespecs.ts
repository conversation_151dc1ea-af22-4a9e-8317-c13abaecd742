import {
  userSchema,
  memberSchema,
  organizationSchema,
} from "@braintrust/typespecs";
import { z } from "zod";

export const meSchema = z.object({
  id: userSchema.shape.id,
  email: userSchema.shape.email,
  organizations: z
    .object({
      id: memberSchema.shape.org_id,
      name: organizationSchema.shape.name,
    })
    .array(),
  is_sysadmin: z.boolean().optional(),
});
export type Me = z.infer<typeof meSchema>;
