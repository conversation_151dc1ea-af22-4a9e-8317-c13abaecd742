import { organizationSchema } from "@braintrust/typespecs";
import { z } from "zod";

export const inputSchema = z.object({
  input: z.object({
    experiments: z
      .record(
        z.object({
          num_row_actions: z.number().int().nonnegative().default(0),
        }),
      )
      .nullish(),
    logs: z
      .record(
        z.object({
          num_row_actions: z.number().int().nonnegative().default(0),
          num_row_bytes: z.number().int().nonnegative().default(0),
        }),
      )
      .nullish(),
    datasets: z
      .record(
        z.object({
          num_row_actions: z.number().int().nonnegative().default(0),
        }),
      )
      .nullish(),
    additional_org_ids: organizationSchema.shape.id.array().nullish(),
    additional_project_ids: z.string().array().nullish(),
  }),
  num_shards: z.number().int().positive(),
});

export const outputSchema = z.object({
  is_unlimited: z.record(organizationSchema.shape.id, z.boolean()),
});
