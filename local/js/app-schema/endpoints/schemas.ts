import * as self_get_object_info_typespecs from "./self/get_object_info_typespecs";
import * as self_auth_token_ids_typespecs from "./self/auth_token_ids_typespecs";
import * as insert_logs_resource_check_typespecs from "./insert-logs/resource_check_typespecs";
import * as telemetry_configs_typespecs from "./telemetry/configs_typespecs";
import { Me, meSchema } from "./self/me_typespecs";
export { type Me, meSchema };

export const endpointSchemas = {
  self_get_object_info: {
    input: self_get_object_info_typespecs.inputSchema,
    output: self_get_object_info_typespecs.outputSchema,
  },
  self_auth_token_ids: {
    input: self_auth_token_ids_typespecs.inputSchema,
    output: self_auth_token_ids_typespecs.outputSchema,
  },
  insert_logs_resource_check_typespecs: {
    input: insert_logs_resource_check_typespecs.inputSchema,
    output: insert_logs_resource_check_typespecs.outputSchema,
  },
  telemetry_configs_typespecs: {
    input: telemetry_configs_typespecs.inputSchema,
    output: telemetry_configs_typespecs.outputSchema,
  },
} as const;
