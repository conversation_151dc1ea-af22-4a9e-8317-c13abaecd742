import { Writable } from "node:stream";

export interface RawSSEEvent {
  id?: string;
  event?: string;
  data: string;
}

export function serializeSSEEvent(event: RawSSEEvent): string {
  return (
    Object.entries(event)
      .filter(([_key, value]) => value !== undefined)
      .map(([key, value]) => `${key}: ${value}`)
      .join("\n") + "\n\n"
  );
}

export function writeSSEEvent(
  destinationStream: Writable,
  event: RawSSEEvent,
  encoding: BufferEncoding = "utf-8",
  callback?: () => void,
) {
  destinationStream.write(serializeSSEEvent(event), encoding, callback);
}
