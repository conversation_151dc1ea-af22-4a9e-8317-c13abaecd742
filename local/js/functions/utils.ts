import { base64To<PERSON>rrayBuffer } from "@braintrust/proxy/utils";

// Being as specific as possible about allowable characters and avoiding greedy matching
// helps avoid catastrophic backtracking: https://github.com/braintrustdata/braintrust/pull/4831
const base64ContentTypePattern =
  /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9+.-]+);base64,/;

export function isBase64File(
  s: string,
  checkMimeType: undefined | ((mimeType: string) => boolean),
): boolean {
  // Avoid unnecessary (slower) pattern matching
  if (!s.startsWith("data:")) {
    return false;
  }

  const parts = s.match(base64ContentTypePattern);
  if (!parts) {
    return false;
  }
  const mimeType = parts[1];
  return checkMimeType ? checkMimeType(mimeType) : true;
}

export function isBase64Image(s: string): boolean {
  return isBase64File(s, (mimeType) => mimeType.startsWith("image/"));
}

export function getBase64Parts(s: string): {
  mimeType: string;
  data: A<PERSON>yBuffer;
} {
  const parts = s.match(base64ContentTypePattern);
  if (!parts) {
    throw new Error("Invalid base64 image");
  }
  const mimeType = parts[1];
  const data = s.slice(`data:${mimeType};base64,`.length);
  return { mimeType, data: base64ToArrayBuffer(data) };
}
