import { defineConfig } from "tsup";

export default defineConfig([
  {
    entry: ["index.ts"],
    format: ["cjs", "esm"],
    outDir: "dist",
    external: ["@braintrust/local"],
  },
  {
    entry: ["app-schema/index.ts"],
    format: ["cjs", "esm"],
    outDir: "app-schema/dist",
  },
  {
    entry: ["api-schema/index.ts"],
    format: ["cjs", "esm"],
    outDir: "api-schema/dist",
    external: ["braintrust/util"],
  },
  {
    entry: ["copilot/index.ts"],
    format: ["cjs", "esm"],
    outDir: "copilot/dist",
    external: ["braintrust/util", "braintrust"],
  },
  {
    entry: ["query/index.ts"],
    format: ["cjs", "esm"],
    outDir: "query/dist",
    external: ["braintrust/util"],
  },
  {
    entry: ["functions/index.ts"],
    format: ["cjs", "esm"],
    outDir: "functions/dist",
    external: ["braintrust/util"],
  },
  {
    entry: ["dev/index.ts"],
    format: ["cjs", "esm"],
    outDir: "dev/dist",
    external: ["@braintrust/local/dev"],
  },
  {
    entry: ["bt-pg/index.ts"],
    format: ["cjs"],
    outDir: "bt-pg/dist",
    external: ["@braintrust/local/bt-pg"],
  },
  {
    entry: ["optimization/index.ts", "optimization/tools.ts"],
    format: ["cjs", "esm"],
    outDir: "optimization/dist",
    external: ["@braintrust/local/optimization"],
  },
  {
    entry: ["doc-index/index.ts", "doc-index/cli.ts", "doc-index/dev.ts"],
    format: ["cjs", "esm"],
    outDir: "doc-index/dist",
    external: ["@braintrust/local/doc-index"],
  },
]);
