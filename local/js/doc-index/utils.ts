import * as fs from "fs";
import * as path from "path";

export function findGitRoot(startPath?: string): string | null {
  let currentPath = startPath || process.cwd();

  while (currentPath !== path.dirname(currentPath)) {
    const gitPath = path.join(currentPath, ".git");

    try {
      const stat = fs.statSync(gitPath);
      if (stat.isDirectory() || stat.isFile()) {
        return currentPath;
      }
    } catch {
      // .git doesn't exist at this level, continue
    }

    currentPath = path.dirname(currentPath);
  }

  return null;
}
