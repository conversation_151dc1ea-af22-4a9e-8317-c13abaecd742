import { readFile } from "fs/promises";
import { glob } from "glob";
const matter = require("gray-matter");
import * as path from "path";
import * as crypto from "crypto";
import { type DocumentChunk } from "./schema";

export interface ChunkerOptions {
  maxChunkSize?: number;
  minChunkSize?: number;
  overlapSize?: number;
  indexId?: string; // Custom index ID for idempotent indexing
}

const DEFAULT_OPTIONS: Required<Omit<ChunkerOptions, "indexId">> = {
  maxChunkSize: 1500,
  minChunkSize: 100,
  overlapSize: 200,
};

export class DocumentChunker {
  private options: Required<Omit<ChunkerOptions, "indexId">> & {
    indexId?: string;
  };

  constructor(options: ChunkerOptions = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  async chunkFile(filePath: string): Promise<DocumentChunk[]> {
    const content = await readFile(filePath, "utf-8");
    const { data: frontmatter, content: markdownContent } = matter(content);

    const type: "docs" | "blog" = filePath.includes("/blog/") ? "blog" : "docs";

    // Extract clean file path without line numbers
    const relativePath = filePath.replace(/^.*\/(docs|blog)\//, "$1/");
    const cleanPath = relativePath.replace(/\.(md|mdx)$/, "");

    // Debug logging
    if (!type) {
      console.error(`Failed to determine type for file: ${filePath}`);
    }

    const baseAttributes = {
      title: frontmatter.title,
      date: frontmatter.date,
      tags: frontmatter.tags,
      type,
    };

    const lines = markdownContent.split("\n");
    const chunks: DocumentChunk[] = [];

    const sections = this.splitIntoSections(lines);

    for (const section of sections) {
      // Skip empty sections
      if (
        section.lines.length === 0 ||
        section.lines.every((line) => line.trim() === "")
      ) {
        continue;
      }

      const sectionChunks = this.chunkSection(
        section,
        cleanPath,
        baseAttributes,
        type,
      );
      chunks.push(...sectionChunks);
    }

    return chunks;
  }

  private splitIntoSections(lines: string[]): Section[] {
    const sections: Section[] = [];
    let currentSection: Section | null = null;
    let currentHeaders: string[] = [];
    let inCodeBlock = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      if (trimmedLine.startsWith("```")) {
        inCodeBlock = !inCodeBlock;
      }

      if (!inCodeBlock) {
        const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
        if (headerMatch) {
          const level = headerMatch[1].length;
          const headerText = headerMatch[2];

          if (level <= 2) {
            if (currentSection) {
              sections.push(currentSection);
            }

            currentHeaders = currentHeaders.slice(0, level - 1);
            currentHeaders.push(headerText);

            currentSection = {
              headers: [...currentHeaders],
              lines: [line],
              startLine: i,
              endLine: i,
            };
          } else if (currentSection) {
            currentHeaders = currentHeaders.slice(0, level - 1);
            currentHeaders.push(headerText);
            currentSection.lines.push(line);
            currentSection.endLine = i;
          }
        } else if (currentSection) {
          currentSection.lines.push(line);
          currentSection.endLine = i;
        } else {
          if (!currentSection) {
            currentSection = {
              headers: [],
              lines: [],
              startLine: 0,
              endLine: i,
            };
          }
          currentSection.lines.push(line);
          currentSection.endLine = i;
        }
      } else if (currentSection) {
        currentSection.lines.push(line);
        currentSection.endLine = i;
      } else {
        // Handle lines before any section is created (e.g., in code blocks at start of file)
        if (!currentSection) {
          currentSection = {
            headers: [],
            lines: [],
            startLine: 0,
            endLine: i,
          };
        }
        currentSection.lines.push(line);
        currentSection.endLine = i;
      }
    }

    if (currentSection) {
      sections.push(currentSection);
    }

    return sections;
  }

  private chunkSection(
    section: Section,
    cleanPath: string,
    baseAttributes: { title?: string; type: "docs" | "blog" },
    type: "docs" | "blog",
  ): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    const content = section.lines.join("\n");

    if (content.length <= this.options.maxChunkSize) {
      chunks.push({
        id: this.generateChunkId(cleanPath, section.startLine, section.endLine),
        filePath: cleanPath,
        content,
        type,
        title: baseAttributes.title,
        headers: section.headers,
        startLine: section.startLine,
        endLine: section.endLine,
      });
    } else {
      const subChunks = this.splitLargeSection(section);
      for (const subChunk of subChunks) {
        chunks.push({
          id: this.generateChunkId(
            cleanPath,
            subChunk.startLine,
            subChunk.endLine,
          ),
          filePath: cleanPath,
          content: subChunk.lines.join("\n"),
          type,
          title: baseAttributes.title,
          headers: section.headers,
          startLine: subChunk.startLine,
          endLine: subChunk.endLine,
        });
      }
    }

    return chunks;
  }

  private splitLargeSection(section: Section): Section[] {
    const subSections: Section[] = [];
    let currentSubSection: Section = {
      headers: section.headers,
      lines: [],
      startLine: section.startLine,
      endLine: section.startLine,
    };

    let currentSize = 0;
    let inCodeBlock = false;

    for (let i = 0; i < section.lines.length; i++) {
      const line = section.lines[i];
      const lineSize = line.length + 1;

      if (line.trim().startsWith("```")) {
        inCodeBlock = !inCodeBlock;
      }

      const wouldExceedSize =
        currentSize + lineSize > this.options.maxChunkSize;
      const hasMinimumContent = currentSize >= this.options.minChunkSize;

      if (wouldExceedSize && hasMinimumContent && !inCodeBlock) {
        subSections.push(currentSubSection);

        const overlapStart = Math.max(
          0,
          currentSubSection.lines.length -
            Math.floor(this.options.overlapSize / 50),
        );
        const overlapLines = currentSubSection.lines.slice(overlapStart);

        currentSubSection = {
          headers: section.headers,
          lines: [...overlapLines, line],
          startLine: section.startLine + i - overlapLines.length + 1,
          endLine: section.startLine + i,
        };
        currentSize = overlapLines.join("\n").length + lineSize;
      } else {
        currentSubSection.lines.push(line);
        currentSubSection.endLine = section.startLine + i;
        currentSize += lineSize;
      }
    }

    if (currentSubSection.lines.length > 0) {
      subSections.push(currentSubSection);
    }

    return subSections;
  }

  private generateChunkId(
    cleanPath: string,
    startLine: number,
    endLine?: number,
  ): string {
    // Include index ID if provided for idempotent indexing
    const indexPrefix = this.options.indexId ? `${this.options.indexId}:` : "";
    // Use both start and end line to ensure uniqueness
    const lineRange =
      endLine !== undefined && endLine !== startLine
        ? `#L${startLine}-${endLine}`
        : `#L${startLine}`;
    let id = `${indexPrefix}${cleanPath}${lineRange}`;

    // Ensure ID is less than 64 bytes
    if (id.length >= 64) {
      // Create a hash of the full path and use it as a shorter identifier
      const pathHash = crypto
        .createHash("md5")
        .update(cleanPath)
        .digest("hex")
        .substring(0, 8);
      id = `${indexPrefix}${pathHash}${lineRange}`;

      // If still too long, just use hash and line numbers
      if (id.length >= 64) {
        const contentHash = crypto
          .createHash("md5")
          .update(`${cleanPath}${lineRange}`)
          .digest("hex")
          .substring(0, 16);
        id = `${indexPrefix}${contentHash}`;
      }
    }

    return id;
  }

  async chunkDirectory(directory: string): Promise<DocumentChunk[]> {
    const pattern = path.join(directory, "**/*.{md,mdx}");
    const files = await glob(pattern);

    const allChunks: DocumentChunk[] = [];

    for (const file of files) {
      try {
        const chunks = await this.chunkFile(file);
        // Validate chunks before adding them
        for (const chunk of chunks) {
          if (!chunk.type || chunk.headers === undefined) {
            console.error(`Invalid chunk from file ${file}:`, {
              hasType: !!chunk.type,
              type: chunk.type,
              hasHeaders: chunk.headers !== undefined,
              headers: chunk.headers,
            });
            throw new Error(`Invalid chunk generated from ${file}`);
          }
        }
        allChunks.push(...chunks);
      } catch (error) {
        console.error(`Error processing ${file}:`, error);
      }
    }

    return allChunks;
  }
}

interface Section {
  headers: string[];
  lines: string[];
  startLine: number;
  endLine: number;
}
