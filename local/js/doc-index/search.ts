import { Turbopuffer } from "@turbopuffer/turbopuffer";
import OpenAI from "openai";

export interface SearchOptions {
  topK?: number;
  filters?: {
    type?: "docs" | "blog";
  };
}

export interface SearchResult {
  id: string;
  score: number;
  attributes: Record<string, unknown>;
}

export class DocumentSearcher {
  private turbopuffer: Turbopuffer;
  private openai: OpenAI;
  private namespace: ReturnType<Turbopuffer["namespace"]>;

  constructor(config?: {
    turbopufferApiKey?: string;
    turbopufferRegion?: string;
    openaiApiKey?: string;
    namespace?: string;
  }) {
    const turbopufferApiKey =
      config?.turbopufferApiKey || process.env.TURBOPUFFER_API_KEY;
    const turbopufferRegion =
      config?.turbopufferRegion ||
      process.env.TURBOPUFFER_REGION ||
      "gcp-us-central1";
    const openaiApiKey = config?.openaiApiKey || process.env.OPENAI_API_KEY;

    if (!turbopufferApiKey) {
      throw new Error("TURBOPUFFER_API_KEY is required");
    }

    if (!openaiApiKey) {
      throw new Error("OPENAI_API_KEY is required");
    }

    this.turbopuffer = new Turbopuffer({
      apiKey: turbopufferApiKey,
      region: turbopufferRegion,
    });

    this.openai = new OpenAI({
      apiKey: openaiApiKey,
    });

    this.namespace = this.turbopuffer.namespace(
      config?.namespace || "braintrust-docs",
    );
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    const response = await this.openai.embeddings.create({
      model: "text-embedding-3-small",
      input: text,
    });

    return response.data[0].embedding;
  }

  async search(
    query: string,
    options?: SearchOptions,
  ): Promise<SearchResult[]> {
    // Generate embedding for the query
    const queryEmbedding = await this.generateEmbedding(query);

    // Build filters
    let filters: unknown = undefined;
    if (options?.filters?.type) {
      filters = ["type", "Eq", options.filters.type];
    }

    // Perform vector search
    const queryOptions: Record<string, unknown> = {
      rank_by: ["vector", "ANN", queryEmbedding],
      top_k: options?.topK || 10,
      include_attributes: true,
    };

    if (filters !== undefined) {
      queryOptions.filters = filters;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
    const response = await this.namespace.query(queryOptions as any);

    // Map results
    const results: SearchResult[] = [];
    const rows = response?.rows;

    if (Array.isArray(rows)) {
      console.log(`Found ${rows.length} results`);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Turbopuffer row type is not exported
      rows.forEach((result: any) => {
        const id = String(result.id || "");
        const distValue = result["$dist"];
        const score = typeof distValue === "number" ? distValue : 0;

        // All other fields (except id, $dist, and vector) are attributes
        const attributes: Record<string, unknown> = {};
        for (const [key, value] of Object.entries(result)) {
          if (key !== "id" && key !== "$dist" && key !== "vector") {
            attributes[key] = value;
          }
        }

        results.push({
          id,
          score,
          attributes,
        });
      });
    } else {
      console.log("No results found");
    }

    return results;
  }
}
