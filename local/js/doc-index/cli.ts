#!/usr/bin/env node

import { Command } from "commander";
import { DocumentChunker } from "./chunker";
import { DocumentIndexer } from "./indexer";
import * as path from "path";
import { writeFile, mkdir, readFile, stat } from "fs/promises";
import { findGitRoot } from "./utils";
import * as dotenv from "dotenv";

// Load environment variables from doc-index/.env
const envPath = path.resolve(__dirname.replace("/dist", ""), ".env");
dotenv.config({ path: envPath });

const program = new Command();

program
  .name("doc-index")
  .description("Process and chunk documentation files for vector indexing")
  .version("0.1.0");

program
  .command("chunk")
  .description("Chunk markdown files from docs and blog directories")
  .option(
    "-d, --docs-dir <path>",
    "Path to docs directory (relative to git root)",
    "app/content/docs",
  )
  .option(
    "-b, --blog-dir <path>",
    "Path to blog directory (relative to git root)",
    "app/content/blog",
  )
  .option(
    "-o, --output <path>",
    "Output file or directory path",
    "data/doc-chunks.json",
  )
  .option(
    "--max-chunk-size <number>",
    "Maximum chunk size in characters",
    "1500",
  )
  .option(
    "--min-chunk-size <number>",
    "Minimum chunk size in characters",
    "100",
  )
  .option("--overlap-size <number>", "Overlap size between chunks", "200")
  .option("--pretty", "Pretty print JSON output", false)
  .option("--limit <number>", "Maximum number of chunks to generate")
  .option("--index-id <id>", "Custom index ID for idempotent indexing")
  .action(async (options) => {
    const chunker = new DocumentChunker({
      maxChunkSize: parseInt(options.maxChunkSize),
      minChunkSize: parseInt(options.minChunkSize),
      overlapSize: parseInt(options.overlapSize),
      indexId: options.indexId,
    });

    // Find git root
    const gitRoot = findGitRoot();
    if (!gitRoot) {
      console.error("Error: Not in a git repository");
      process.exit(1);
    }

    // Resolve paths relative to git root
    const docsDir = path.resolve(gitRoot, options.docsDir);
    const blogDir = path.resolve(gitRoot, options.blogDir);

    console.log("Processing documentation files...");
    console.log(`Git root: ${gitRoot}`);

    const chunks = [];
    const limit = options.limit ? parseInt(options.limit) : undefined;

    try {
      console.log(`Chunking docs from ${docsDir}...`);
      const docsChunks = await chunker.chunkDirectory(docsDir);

      if (limit && chunks.length + docsChunks.length > limit) {
        const remaining = limit - chunks.length;
        chunks.push(...docsChunks.slice(0, remaining));
        console.log(
          `  Found ${docsChunks.length} chunks in docs (limited to ${remaining})`,
        );
      } else {
        chunks.push(...docsChunks);
        console.log(`  Found ${docsChunks.length} chunks in docs`);
      }
    } catch (error) {
      console.error(`Error processing docs directory:`, error);
    }

    if (!limit || chunks.length < limit) {
      try {
        console.log(`Chunking blog posts from ${blogDir}...`);
        const blogChunks = await chunker.chunkDirectory(blogDir);

        if (limit && chunks.length + blogChunks.length > limit) {
          const remaining = limit - chunks.length;
          chunks.push(...blogChunks.slice(0, remaining));
          console.log(
            `  Found ${blogChunks.length} chunks in blog (limited to ${remaining})`,
          );
        } else {
          chunks.push(...blogChunks);
          console.log(`  Found ${blogChunks.length} chunks in blog`);
        }
      } catch (error) {
        console.error(`Error processing blog directory:`, error);
      }
    }

    console.log(
      `\nTotal chunks created: ${chunks.length}${limit ? ` (limited to ${limit})` : ""}`,
    );

    // Handle output path - check if it's a directory
    let outputPath = options.output;
    try {
      const stats = await stat(outputPath);
      if (stats.isDirectory()) {
        outputPath = path.join(outputPath, "chunks.json");
      }
    } catch {
      // Path doesn't exist, check if it ends with a directory separator
      if (outputPath.endsWith(path.sep)) {
        await mkdir(outputPath, { recursive: true });
        outputPath = path.join(outputPath, "chunks.json");
      } else {
        // Ensure parent directory exists
        const outputDir = path.dirname(outputPath);
        await mkdir(outputDir, { recursive: true });
      }
    }

    const jsonOutput = options.pretty
      ? JSON.stringify(chunks, null, 2)
      : JSON.stringify(chunks);

    await writeFile(outputPath, jsonOutput, "utf-8");
    console.log(`\nChunks saved to ${outputPath}`);

    const stats = {
      totalChunks: chunks.length,
      docsChunks: chunks.filter((c) => c.type === "docs").length,
      blogChunks: chunks.filter((c) => c.type === "blog").length,
      avgChunkSize: Math.round(
        chunks.reduce((sum, c) => sum + c.content.length, 0) / chunks.length,
      ),
    };

    console.log("\nStatistics:");
    console.log(`  Documentation chunks: ${stats.docsChunks}`);
    console.log(`  Blog chunks: ${stats.blogChunks}`);
    console.log(`  Average chunk size: ${stats.avgChunkSize} characters`);
  });

program
  .command("inspect")
  .description("Inspect a specific file to see how it would be chunked")
  .argument("<file>", "Path to the markdown file to inspect")
  .option(
    "--max-chunk-size <number>",
    "Maximum chunk size in characters",
    "1500",
  )
  .option(
    "--min-chunk-size <number>",
    "Minimum chunk size in characters",
    "100",
  )
  .option("--overlap-size <number>", "Overlap size between chunks", "200")
  .action(async (file, options) => {
    const chunker = new DocumentChunker({
      maxChunkSize: parseInt(options.maxChunkSize),
      minChunkSize: parseInt(options.minChunkSize),
      overlapSize: parseInt(options.overlapSize),
    });

    try {
      const chunks = await chunker.chunkFile(file);

      console.log(`File: ${file}`);
      console.log(`Total chunks: ${chunks.length}\n`);

      chunks.forEach((chunk, index) => {
        console.log(`--- Chunk ${index + 1} ---`);
        console.log(`ID: ${chunk.id}`);
        console.log(`Lines: ${chunk.startLine}-${chunk.endLine}`);
        console.log(`Headers: ${chunk.headers.join(" > ")}`);
        console.log(`Size: ${chunk.content.length} characters`);
        console.log(`Content:\n${chunk.content}`);
        console.log("");
      });
    } catch (error) {
      console.error(`Error processing file:`, error);
      process.exit(1);
    }
  });

program
  .command("index")
  .description("Index documentation chunks to Turbopuffer")
  .option("-i, --input <path>", "Input chunks file", "data/doc-chunks.json")
  .option(
    "-d, --docs-dir <path>",
    "Path to docs directory (relative to git root)",
  )
  .option(
    "-b, --blog-dir <path>",
    "Path to blog directory (relative to git root)",
  )
  .option("--namespace <name>", "Turbopuffer namespace", "braintrust-docs")
  .option("--batch-size <number>", "Batch size for indexing", "100")
  .option("--clear", "Clear the namespace before indexing", false)
  .action(async (options) => {
    const indexer = new DocumentIndexer({
      namespace: options.namespace,
      batchSize: parseInt(options.batchSize),
    });

    let chunks = [];

    // Load chunks from file or generate them
    if (!options.docsDir && !options.blogDir) {
      // Load from file
      try {
        const data = await readFile(options.input, "utf-8");
        chunks = JSON.parse(data);
        console.log(`Loaded ${chunks.length} chunks from ${options.input}`);
      } catch (error) {
        console.error(`Error loading chunks from ${options.input}:`, error);
        process.exit(1);
      }
    } else {
      // Generate chunks
      const chunker = new DocumentChunker();
      console.log("Generating chunks...");

      // Find git root
      const gitRoot = findGitRoot();
      if (!gitRoot) {
        console.error("Error: Not in a git repository");
        process.exit(1);
      }

      if (options.docsDir) {
        try {
          const docsDir = path.resolve(gitRoot, options.docsDir);
          const docsChunks = await chunker.chunkDirectory(docsDir);
          chunks.push(...docsChunks);
          console.log(`  Generated ${docsChunks.length} chunks from docs`);
        } catch (error) {
          console.error(`Error processing docs directory:`, error);
        }
      }

      if (options.blogDir) {
        try {
          const blogDir = path.resolve(gitRoot, options.blogDir);
          const blogChunks = await chunker.chunkDirectory(blogDir);
          chunks.push(...blogChunks);
          console.log(`  Generated ${blogChunks.length} chunks from blog`);
        } catch (error) {
          console.error(`Error processing blog directory:`, error);
        }
      }
    }

    if (chunks.length === 0) {
      console.error("No chunks to index");
      process.exit(1);
    }

    // Clear namespace if requested
    if (options.clear) {
      console.log(`\nClearing namespace ${options.namespace}...`);
      await indexer.deleteNamespace();
    }

    // Index chunks
    console.log(`\nIndexing ${chunks.length} chunks to Turbopuffer...`);
    console.log(`Namespace: ${options.namespace}`);
    console.log(`Batch size: ${options.batchSize}\n`);

    const startTime = Date.now();

    await indexer.indexChunks(chunks, (processed, total) => {
      const percent = Math.round((processed / total) * 100);
      process.stdout.write(`\rProgress: ${processed}/${total} (${percent}%)`);
    });

    const duration = (Date.now() - startTime) / 1000;
    console.log(`\n\nIndexing completed in ${duration.toFixed(1)}s`);

    // Get stats
    const stats = await indexer.getStats();
    console.log(`\nNamespace stats:`);
    console.log(`  Namespace: ${stats.namespace}`);
    console.log(`  Vector count: ${stats.vectorCount}`);
  });

program
  .command("search")
  .description("Search indexed documentation")
  .argument("<query>", "Search query")
  .option("--namespace <name>", "Turbopuffer namespace", "braintrust-docs")
  .option("-k, --top-k <number>", "Number of results", "10")
  .option("--type <type>", "Filter by type (docs/blog)")
  .action(async (query, options) => {
    const indexer = new DocumentIndexer({
      namespace: options.namespace,
    });

    console.log(`Searching for: "${query}"`);
    console.log(`Namespace: ${options.namespace}\n`);

    const filters = options.type ? { type: options.type } : undefined;

    try {
      const results = await indexer.search(query, {
        topK: parseInt(options.topK),
        filters,
      });

      if (results.length === 0) {
        console.log("No results found.");
        return;
      }

      console.log(`Found ${results.length} results:\n`);

      results.forEach((result, index) => {
        console.log(`--- Result ${index + 1} ---`);
        console.log(`Score: ${result.score.toFixed(4)}`);
        console.log(`ID: ${result.id}`);
        console.log(`File: ${result.attributes.filePath}`);
        console.log(`Type: ${result.attributes.type}`);
        const headers = result.attributes.headers;
        if (Array.isArray(headers)) {
          console.log(`Headers: ${headers.join(" > ")}`);
        }
        console.log(`Content preview:`);
        const content = String(result.attributes.content || "");
        console.log(content.substring(0, 200) + "...");
        console.log("");
      });
    } catch (error) {
      console.error("Search error:", error);
      process.exit(1);
    }
  });

program.parse();
