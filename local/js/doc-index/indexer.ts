import { Turbopuffer } from "@turbopuffer/turbopuffer";
import type { Row } from "@turbopuffer/turbopuffer/resources/namespaces";
import { type DocumentChunk, chunkAttributesSchema } from "./schema";
import { OpenAI } from "openai";
import * as crypto from "crypto";
import * as fs from "fs/promises";
import * as path from "path";

export interface IndexerOptions {
  turbopufferApiKey?: string;
  turbopufferRegion?: string;
  openaiApiKey?: string;
  namespace?: string;
  embeddingModel?: string;
  batchSize?: number;
  cacheDir?: string;
}

export class DocumentIndexer {
  private turbopuffer: Turbopuffer;
  private openai: OpenAI;
  private namespace: string;
  private embeddingModel: string;
  private batchSize: number;
  private cacheDir: string | null;
  private embeddingCache: Map<string, number[]> = new Map();

  constructor(options: IndexerOptions = {}) {
    this.turbopuffer = new Turbopuffer({
      apiKey: options.turbopufferApiKey || process.env.TURBOPUFFER_API_KEY,
      region:
        options.turbopufferRegion ||
        process.env.TURBOPUFFER_REGION ||
        "us-east-1",
    });

    this.openai = new OpenAI({
      apiKey: options.openaiApiKey || process.env.OPENAI_API_KEY,
    });

    this.namespace = options.namespace || "braintrust-docs";
    this.embeddingModel = options.embeddingModel || "text-embedding-3-small";
    this.batchSize = options.batchSize || 100;
    this.cacheDir = options.cacheDir || null;

    if (this.cacheDir) {
      this.loadCache().catch(console.error);
    }
  }

  private hashText(text: string): string {
    return crypto
      .createHash("sha256")
      .update(text)
      .update(this.embeddingModel)
      .digest("hex");
  }

  private getCachePath(): string | null {
    if (!this.cacheDir) return null;
    return path.join(
      this.cacheDir,
      `embeddings-${this.embeddingModel.replace(/[^a-z0-9]/gi, "-")}.json`,
    );
  }

  private async loadCache(): Promise<void> {
    const cachePath = this.getCachePath();
    if (!cachePath) return;

    try {
      const data = await fs.readFile(cachePath, "utf-8");
      const cache = JSON.parse(data);
      if (typeof cache === "object" && cache !== null) {
        this.embeddingCache = new Map(Object.entries(cache));
        console.log(`Loaded ${this.embeddingCache.size} cached embeddings`);
      }
    } catch {
      // Cache doesn't exist yet, that's okay
    }
  }

  private async saveCache(): Promise<void> {
    const cachePath = this.getCachePath();
    if (!cachePath) return;

    try {
      await fs.mkdir(this.cacheDir!, { recursive: true });
      const cache: Record<string, number[]> = {};
      for (const [key, value] of this.embeddingCache.entries()) {
        cache[key] = value;
      }
      await fs.writeFile(cachePath, JSON.stringify(cache));
    } catch (error) {
      console.error("Failed to save embedding cache:", error);
    }
  }

  async generateEmbedding(text: string): Promise<number[]> {
    const response = await this.openai.embeddings.create({
      model: this.embeddingModel,
      input: text,
    });

    return response.data[0].embedding;
  }

  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    // Filter out empty strings and validate input
    const validTexts = texts.map((text) => {
      // Ensure text is not empty and trim whitespace
      const trimmed = text.trim();
      if (trimmed.length === 0) {
        return "empty";
      }

      // Truncate very long texts to avoid token limit issues
      // Rough estimate: 1 token ≈ 4 characters for English text
      // With 8192 token limit, use 20k chars to be safe
      const maxChars = 20000;
      if (trimmed.length > maxChars) {
        console.warn(
          `Truncating chunk from ${trimmed.length} to ${maxChars} characters`,
        );
        return trimmed.substring(0, maxChars) + "... [truncated]";
      }

      return trimmed;
    });

    // Check cache and separate cached vs uncached texts
    const embeddings: (number[] | null)[] = new Array(validTexts.length);
    const uncachedIndices: number[] = [];
    const uncachedTexts: string[] = [];

    for (let i = 0; i < validTexts.length; i++) {
      const hash = this.hashText(validTexts[i]);
      const cached = this.embeddingCache.get(hash);
      if (cached) {
        embeddings[i] = cached;
      } else {
        embeddings[i] = null;
        uncachedIndices.push(i);
        uncachedTexts.push(validTexts[i]);
      }
    }

    // Generate embeddings for uncached texts
    if (uncachedTexts.length > 0) {
      console.log(
        `Generating ${uncachedTexts.length} new embeddings (${validTexts.length - uncachedTexts.length} cached)`,
      );

      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: uncachedTexts,
      });

      // Store in cache and fill in results
      for (let i = 0; i < uncachedIndices.length; i++) {
        const embedding = response.data[i].embedding;
        const originalIndex = uncachedIndices[i];
        const hash = this.hashText(validTexts[originalIndex]);

        embeddings[originalIndex] = embedding;
        this.embeddingCache.set(hash, embedding);
      }

      // Save cache periodically
      if (this.cacheDir && uncachedTexts.length > 0) {
        await this.saveCache();
      }
    } else {
      console.log(`Using ${validTexts.length} cached embeddings`);
    }

    // All embeddings should be filled at this point
    return embeddings.filter((e): e is number[] => e !== null);
  }

  async indexChunks(
    chunks: DocumentChunk[],
    onProgress?: (processed: number, total: number) => void,
  ): Promise<void> {
    // Validate chunks before processing
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      if (!chunk.type || !chunk.headers) {
        console.error(`Invalid chunk at index ${i}:`, {
          id: chunk.id,
          hasType: !!chunk.type,
          type: chunk.type,
          hasHeaders: !!chunk.headers,
          headers: chunk.headers,
          filePath: chunk.filePath,
        });
      }
    }

    const namespace = this.turbopuffer.namespace(this.namespace);

    // Helper function to process all chunks
    const processChunks = async (): Promise<void> => {
      // Process chunks in batches
      for (let i = 0; i < chunks.length; i += this.batchSize) {
        const batch = chunks.slice(i, i + this.batchSize);

        try {
          // Generate embeddings for the batch
          const texts = batch.map((chunk) => chunk.content);
          const embeddings = await this.generateEmbeddings(texts);

          // Prepare documents for Turbopuffer
          const vectors: Row[] = batch.map((chunk, idx) => {
            // Create row with id and vector
            const row: Row = {
              id: chunk.id,
              vector: embeddings[idx],
            };

            // Add attributes directly to the row as additional properties
            const rowWithAttributes: Row & Record<string, unknown> =
              Object.assign(row, {});

            // Validate and add chunk attributes
            const attributes = chunkAttributesSchema.parse({
              content: chunk.content,
              filePath: chunk.filePath,
              type: chunk.type,
              title: chunk.title,
              headers: chunk.headers,
              startLine: chunk.startLine,
              endLine: chunk.endLine,
            });

            // Add all attributes to the row
            Object.assign(rowWithAttributes, attributes);

            return rowWithAttributes;
          });

          // Upload to Turbopuffer with schema marking content as non-filterable
          await namespace.write({
            distance_metric: "cosine_distance",
            upsert_rows: vectors,
            schema: {
              content: {
                type: "string",
                filterable: false,
              },
              filePath: {
                type: "string",
                filterable: true,
              },
              type: {
                type: "string",
                filterable: true,
              },
              title: {
                type: "string",
                filterable: true,
              },
              headers: {
                type: "[]string",
                filterable: true,
              },
              startLine: {
                type: "uint",
                filterable: true,
              },
              endLine: {
                type: "uint",
                filterable: true,
              },
            },
          });

          if (onProgress) {
            onProgress(
              Math.min(i + batch.length, chunks.length),
              chunks.length,
            );
          }
        } catch (error) {
          console.error(
            `\nError processing batch starting at index ${i}:`,
            error,
          );

          // Log the first chunk in the batch for debugging
          if (batch.length > 0) {
            console.error("First chunk in failed batch:", {
              id: batch[0].id,
              type: batch[0].type,
              headers: batch[0].headers,
              filePath: batch[0].filePath,
              contentLength: batch[0].content.length,
              contentPreview: batch[0].content.substring(0, 100) + "...",
            });
            console.error(
              "Full chunk object:",
              JSON.stringify(batch[0], null, 2),
            );
          }

          throw error;
        }
      }
    };

    // Try to process chunks, handle schema conflicts
    try {
      await processChunks();
    } catch (error) {
      // Check if this is a schema conflict error
      if (
        error instanceof Error &&
        error.message.includes("invalid schema update")
      ) {
        console.warn(
          "\n⚠️  Schema conflict detected. Dropping and recreating namespace...",
        );

        // Delete the existing namespace
        await this.deleteNamespace();

        // Wait a moment for the deletion to propagate
        await new Promise((resolve) => setTimeout(resolve, 1000));

        console.log("🔄 Retrying indexing with fresh namespace...\n");

        // Retry processing
        await processChunks();
      } else {
        // Re-throw other errors
        throw error;
      }
    }
  }

  async search(
    query: string,
    options: {
      topK?: number;
      includeAttributes?: string[];
      filters?: Record<string, unknown>;
    } = {},
  ): Promise<
    Array<{
      id: string;
      score: number;
      attributes: Record<string, unknown>;
    }>
  > {
    const namespace = this.turbopuffer.namespace(this.namespace);

    // Generate embedding for the query
    const queryEmbedding = await this.generateEmbedding(query);

    // Build filters if provided
    let filters: unknown = undefined;
    if (options.filters) {
      const filterClauses: unknown[] = [];
      for (const [key, value] of Object.entries(options.filters)) {
        filterClauses.push([key, "Eq", value]);
      }

      if (filterClauses.length === 1) {
        filters = filterClauses[0];
      } else if (filterClauses.length > 1) {
        filters = ["And", filterClauses];
      }
    }

    // Search in Turbopuffer
    const response = await namespace.query({
      rank_by: ["vector", "ANN", queryEmbedding],
      top_k: options.topK || 10,
      include_attributes: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
      filters: filters as any,
    });

    // Map results
    const mappedResults: Array<{
      id: string;
      score: number;
      attributes: Record<string, unknown>;
    }> = [];

    // The response has a "rows" property that contains the results
    const rows = response?.rows;
    if (Array.isArray(rows)) {
      rows.forEach((result) => {
        // Extract id and distance
        const id = String(result.id || "");
        const distValue = result["$dist"];
        const score = typeof distValue === "number" ? distValue : 0;

        // All other fields (except id, $dist, and vector) are attributes
        const attributes: Record<string, unknown> = {};
        for (const [key, value] of Object.entries(result)) {
          if (key !== "id" && key !== "$dist" && key !== "vector") {
            attributes[key] = value;
          }
        }

        mappedResults.push({
          id,
          score,
          attributes,
        });
      });
    }

    return mappedResults;
  }

  async deleteNamespace(): Promise<void> {
    const namespace = this.turbopuffer.namespace(this.namespace);
    try {
      // Check if namespace exists first
      const exists = await namespace.exists();
      if (exists) {
        await namespace.deleteAll();
        console.log(`✓ Namespace ${this.namespace} deleted successfully`);
      } else {
        console.log(`✓ Namespace ${this.namespace} does not exist`);
      }
    } catch (error) {
      console.error(`Failed to delete namespace ${this.namespace}:`, error);
      throw error;
    }
  }

  async getStats(): Promise<{
    namespace: string;
    vectorCount: number;
  }> {
    // Get namespace list to check if our namespace exists
    try {
      // Check if namespace exists
      const namespace = this.turbopuffer.namespace(this.namespace);
      const exists = await namespace.exists();

      return {
        namespace: this.namespace,
        vectorCount: exists ? 1 : 0, // Can't get exact count from the SDK
      };
    } catch {
      return {
        namespace: this.namespace,
        vectorCount: 0,
      };
    }
  }
}
