import { z } from "zod";

// Schema for the attributes stored in the vector database
export const chunkAttributesSchema = z.object({
  content: z.string(),
  filePath: z.string(), // Clean file path without #L suffix
  type: z.enum(["docs", "blog"]),
  title: z.string().optional(),
  headers: z.array(z.string()),
  startLine: z.number(),
  endLine: z.number(),
});

export type ChunkAttributes = z.infer<typeof chunkAttributesSchema>;

// Schema for document chunks including ID
export const documentChunkSchema = z.object({
  id: z.string(),
  content: z.string(),
  filePath: z.string(),
  type: z.enum(["docs", "blog"]),
  title: z.string().optional(),
  headers: z.array(z.string()),
  startLine: z.number(),
  endLine: z.number(),
});

export type DocumentChunk = z.infer<typeof documentChunkSchema>;
