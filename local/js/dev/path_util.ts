import * as path from "path";
import * as fs from "fs";
import { execSync } from "child_process";

// NOTE(austin): The `require.resolve` below will cause a "MODULE NOT FOUND"
// error in certain environments, like in Vercel serverless functions. Avoid
// calling this function outside of test environments.
let _rootPath: string | null = null;
export function braintrustRootPath() {
  if (_rootPath === null) {
    // Should refer to the dist/index.js of this module.
    const module_path = require.resolve("@braintrust/local/dev");
    _rootPath = path.join(path.dirname(module_path), "..", "..", "..", "..");
  }
  return _rootPath;
}

/**
 * Find the git root directory by traversing up from the given path
 * @param startPath The path to start searching from (defaults to current working directory)
 * @returns The absolute path to the git root directory, or null if not found
 */
export function findGitRoot(startPath: string = process.cwd()): string | null {
  let currentPath = path.resolve(startPath);

  // Traverse up the directory tree
  while (currentPath !== path.dirname(currentPath)) {
    const gitPath = path.join(currentPath, ".git");

    try {
      const stat = fs.statSync(gitPath);
      // Check if .git exists and is a directory (normal repos) or file (git worktrees)
      if (stat.isDirectory() || stat.isFile()) {
        return currentPath;
      }
    } catch {
      // .git doesn't exist at this level, continue traversing
    }

    currentPath = path.dirname(currentPath);
  }

  return null;
}

/**
 * Find the git root directory using git command
 * @param startPath The path to start searching from (defaults to current working directory)
 * @returns The absolute path to the git root directory, or null if not found
 */
export function findGitRootUsingGitCommand(
  startPath: string = process.cwd(),
): string | null {
  try {
    const gitRoot = execSync("git rev-parse --show-toplevel", {
      cwd: startPath,
      encoding: "utf-8",
      stdio: ["pipe", "pipe", "ignore"], // Ignore stderr to handle errors gracefully
    }).trim();

    return gitRoot || null;
  } catch {
    // Not in a git repository or git command not available
    return null;
  }
}
