import * as fs from "fs";
import * as path from "path";
import { z } from "zod";

import { braintrustRootPath } from "./path_util";

const serverConfigSchema = z.object({
  host: z.string(),
  port: z.number().int().min(0),
  relCachePath: z.string(),
});

export interface TestProxyServerConfig {
  host: string;
  port: number;
  cachePath: string;
}

let _testProxyServerConfig: TestProxyServerConfig | null = null;
export async function getTestProxyServerConfig(
  rootPath?: string,
): Promise<TestProxyServerConfig> {
  if (_testProxyServerConfig !== null) {
    return _testProxyServerConfig;
  }
  const configPath = path.join(
    rootPath ?? braintrustRootPath(),
    "test-proxy",
    "server_config.json",
  );
  const contents = serverConfigSchema.parse(
    JSON.parse(await fs.promises.readFile(configPath, { encoding: "utf-8" })),
  );
  _testProxyServerConfig = {
    host: contents.host,
    port: contents.port,
    cachePath: path.join(path.dirname(configPath), contents.relCachePath),
  };
  return _testProxyServerConfig;
}
