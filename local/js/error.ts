import { z } from "zod";
import pluralize from "pluralize";

export function makeError(e: unknown): Error {
  if (e instanceof Error) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    return e as Error;
  } else {
    throw new Error(`Wrapped error: ${e}`);
  }
}

export const formatNotFoundErrorMessageInputSchema = z.object({
  permission: z.string().nullish(),
  objectType: z.string().nullish(),
  objectId: z.string().nullish(),
  objectIds: z.array(z.string()).nullish(),
  objectTypeAndIds: z.array(z.tuple([z.string(), z.string()])).nullish(),
});

export type FormatNotFoundErrorMessageInput = z.infer<
  typeof formatNotFoundErrorMessageInputSchema
>;

export function formatNotFoundErrorMessage({
  permission,
  objectType,
  objectId,
  objectIds,
  objectTypeAndIds,
}: FormatNotFoundErrorMessageInput): string {
  const allObjectTypeAndIds = (objectId ? [objectId] : [])
    .concat(objectIds ?? [])
    .map((x): [string, string] => [objectType ?? "object", x])
    .concat(objectTypeAndIds ?? []);
  const allObjectIds = allObjectTypeAndIds.map(([, id]) => id);
  const singularObjectType = ((): string | null => {
    const allObjectTypes = new Set<string>(
      allObjectTypeAndIds.flatMap(([objectType]) => [objectType]),
    );
    return allObjectTypes.size === 1
      ? pluralize([...allObjectTypes][0], allObjectIds.length)
      : null;
  })();
  return `Missing ${permission ? `${permission} ` : ""}access${
    allObjectIds.length === 0
      ? ""
      : singularObjectType
        ? ` to ${singularObjectType} id ${allObjectIds.join(", ")}, or the ${singularObjectType} ${allObjectIds.length > 1 ? "do" : "does"} not exist`
        : ` to object ids (${allObjectTypeAndIds.map(([x, y]) => `${x} ${y}`).join(", ")}), or the objects do not exist`
  }`;
}

export const errorContextSchema = z.object({
  userId: z.string().nullish(),
  userEmail: z.string().nullish(),
  orgName: z.string().nullish(),
});

export type ErrorContext = z.infer<typeof errorContextSchema>;

export const EMPTY_ERROR_CONTEXT: ErrorContext = {
  userEmail: undefined,
  orgName: undefined,
};

export function augmentWithErrorContext(
  message: string,
  errorContext: ErrorContext,
  extraTags?: Record<string, string | null | undefined>,
): string {
  const timestamp = Date.now() / 1000;
  const tags = {
    user_email: errorContext.userEmail,
    user_org: errorContext.orgName,
    timestamp: `${timestamp}`,
    ...extraTags,
  };
  const tagsStr = formatErrorMessageTags(tags);
  return `${message}${tagsStr}`;
}

export function formatErrorMessageTags(
  tags: Record<string, string | null | undefined>,
): string {
  return Object.entries(tags)
    .filter(([, v]) => !!v)
    .map(([k, v]) => ` [${k}=${v}]`)
    .join("");
}
