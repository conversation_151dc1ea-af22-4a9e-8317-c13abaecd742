import { z } from "zod";
import { copilotEditorContextSchema } from "./context";

export const autoCompleteEditorContextSchema = z.object({
  prefix: z.string(),
  suffix: z.string(),
});
export type AutoCompleteEditorContext = z.infer<
  typeof autoCompleteEditorContextSchema
>;

export const autoCompleteContextSchema = autoCompleteEditorContextSchema.merge(
  z.object({
    editorCtx: copilotEditorContextSchema,
  }),
);
export type AutoCompleteContext = z.infer<typeof autoCompleteContextSchema>;

export const fewShotSchema = z.object({
  context: autoCompleteContextSchema,
  completion: z.string(),
  note: z.string(),
});
export type FewShot = z.infer<typeof fewShotSchema>;

export const autoCompleteCredentialsSchema = z.object({
  userId: z.string().nullable(),
  email: z.string().nullable(),
  orgName: z.string(),
  baseUrl: z.string(),
  authHeaders: z.record(z.string(), z.string()),
  model: z.string().optional(),
});
export type AutoCompleteCredentials = z.infer<
  typeof autoCompleteCredentialsSchema
>;

export const autoCompleteArgsSchema = z.object({
  context: autoCompleteContextSchema,
  credentials: autoCompleteCredentialsSchema,
});
export type AutoCompleteArgs = z.infer<typeof autoCompleteArgsSchema>;

export const completionToolArgs = z.object({
  completion: z
    .string()
    .describe("The completion text between prefix and suffix"),
});

export const completionToolResponse = completionToolArgs.merge(
  z.object({
    spanId: z.string().optional(),
  }),
);
export type AutocompleteResponse = z.infer<typeof completionToolResponse>;
