import { FewShot } from "./types";

export const fewShots: FewShot[] = [
  {
    context: {
      prefix: `{"formula": "1+`,
      suffix: `, "answer": 6}`,
      snippets: [],
    },
    completion: `5"`,
    note: "the completion forms a valid JSON object.",
  },
  {
    context: {
      prefix: `You are a`,
      suffix: ``,
      snippets: [],
    },
    completion: ` helpful assistant`,
    note: "there is a space before 'helpful assistant' because the prefix does not end with a space.",
  },
  {
    context: {
      prefix: `scores.Factuality > 5`,
      suffix: ` and scores.Factuality < 75%`,
      snippets: [],
    },
    completion: `0%`,
    note: "the completion is a number in btql syntax.",
  },
  {
    context: {
      prefix: ``,
      suffix: `{"result": 10}`,
      snippets: [],
    },
    completion: ``,
    note: "The result appears to be complete, so no completion is needed.",
  },
];
