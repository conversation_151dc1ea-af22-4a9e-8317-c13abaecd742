import {
  _internalGetGlobalState,
  currentSpan,
  Eval,
  initDataset,
} from "braintrust";
import { AutoCompleteContext, AutocompleteResponse } from "./types";
import { runAutocomplete } from "./call";
import {
  Levenshtein,
  LLMClassifierFromTemplate,
} from "../../../autoevals/jsdist";

function completionDiff({
  output,
  expected,
}: {
  output: AutocompleteResponse;
  expected: AutocompleteResponse;
}) {
  return Levenshtein({
    output: output.completion,
    expected: expected.completion,
  });
}

// const MODEL = "gpt-4o";
// const MODEL = "gpt-4o-mini";
// const MODEL = "gpt-4o-2024-08-06";
// const MODEL = "claude-3-5-sonnet-20240620";
const MODEL = "claude-3-haiku-20240307";

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
const CompletionEvaluator = LLMClassifierFromTemplate<{
  input: AutoCompleteContext;
  output: AutocompleteResponse;
}>({
  model: "gpt-4o",
  promptTemplate: `You are assessing the quality of an autocomplete tool.
The prefix is: {{{input.prefix}}}
The suffix is: {{{input.suffix}}}

The completion is: {{{output.completion}}}

Combined together, the text looks like:

{{{input.prefix}}}{{{output.completion}}}{{{input.suffix}}}

Please pick one of the following:
a) The completion perfectly fits within the prefix and suffix
b) The completion is semantically correct but has some overlapping text that is repetitive or lacking spaces.
c) The completion is directionally relevant
d) The completion is not at all relevant.
  `,
  choiceScores: {
    a: 1,
    b: 0.5,
    c: 0.25,
    d: 0,
  },
  useCoT: true,
  name: "CompletionScore",
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
}) as any; /* XXX We should allow LLMClassifierFromTemplate to override the output type */

Eval<AutoCompleteContext, AutocompleteResponse, AutocompleteResponse>(
  "copilot-bench",
  {
    data: initDataset({
      project: "copilot",
      dataset: "Golden",
    }),
    task: async (input: AutoCompleteContext) => {
      const state = _internalGetGlobalState();
      if (!state.proxyUrl) {
        throw new Error("Proxy URL not found");
      }
      if (!state.loginToken) {
        throw new Error("Login token not found");
      }
      if (!state.orgName) {
        throw new Error("Org name not found");
      }

      const result = await runAutocomplete(
        {
          context: input,
          credentials: {
            baseUrl: `${state.proxyUrl}/v1/proxy`,
            authHeaders: {
              Authorization: `Bearer ${state.loginToken}`,
            },
            orgName: state.orgName,
            userId: null,
            email: null,
            model: MODEL,
          },
        },
        {
          parentLogger: currentSpan(),
          cache: "never", // This is useful for doing timing tests
        },
      );
      return { completion: result.completion };
    },
    scores: [completionDiff, CompletionEvaluator],
    metadata: {
      model: MODEL,
    },
    experimentName: MODEL,
    maxConcurrency: 10,
    trialCount: 3,
  },
);
