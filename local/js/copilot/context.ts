import { z } from "zod";
import { functionSchema as rawFunctionSchema } from "@braintrust/typespecs";
import * as JSY from "../yaml";
import { wrapTraced } from "braintrust";

export const dataRowContextSchema = z.record(z.unknown());
export type DataRowContext = z.infer<typeof dataRowContextSchema>;

export const slimmedFunctionSchema = rawFunctionSchema
  .pick({
    id: true,
    prompt_data: true,
    function_data: true,
  })
  .extend({
    name: rawFunctionSchema.shape.name.nullish(),
    metadata: rawFunctionSchema.shape.metadata.nullish(),
  })
  .transform((e) => ({
    ...e,
    function_data: e.function_data ?? { type: "prompt" },
  }));

export type SlimmedUIFunction = z.infer<typeof slimmedFunctionSchema>;

export const copilotContextFormatSchema = z.enum(["json", "yaml", "text"]);
export type CopilotContextFormat = z.infer<typeof copilotContextFormatSchema>;

export const copilotCurrentCellContextSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("row"),
    row: z.unknown(),
    objectType: z.string().nullable(),
    objectName: z.string().nullable(),
    field: z.string(),
    fmt: copilotContextFormatSchema,
  }),
  z.object({
    type: z.literal("function"),
    functionType: z.enum(["prompt", "function"]),
    function: slimmedFunctionSchema.optional(),
  }),
  z.object({
    type: z.literal("tools"),
    fmt: copilotContextFormatSchema,
  }),
]);
export type CopilotCurrentCellContext = z.infer<
  typeof copilotCurrentCellContextSchema
>;

export const copilotEditorContextSchema: z.AnyZodObject = z.object({
  // The values at the end of the array are the most recent.
  recentRows: z.array(dataRowContextSchema),
  recentFunctions: z.array(slimmedFunctionSchema),
  currentCell: copilotCurrentCellContextSchema,
});
export type CopilotEditorContext = z.infer<typeof copilotEditorContextSchema>;

export const snippetSchema = z.object({
  data: z.string(),
  priority: z.number().describe("Higher numbers mean it's a higher priority"),
});
export type CopilotSnippet = z.infer<typeof snippetSchema>;

export const makeCopilotEditorSnippets = wrapTraced(
  function makeCopilotEditorSnippets(
    ctx: CopilotEditorContext,
  ): CopilotSnippet[] {
    const format =
      ctx.currentCell.type === "row" ? ctx.currentCell.fmt : "yaml";

    const functionSnippets: CopilotSnippet[] = [];
    const dataRowSnippets: CopilotSnippet[] = [];
    let priority = 0;
    for (const fn of ctx.recentFunctions) {
      functionSnippets.push({
        data: serializeFunction(fn),
        priority: ++priority,
      });
    }
    priority = 0;
    for (const row of ctx.recentRows) {
      // XXX Add back in the logic where we show:
      // The current field in other rows
      // All fields in the current row
      dataRowSnippets.push({
        data: fmtData(row, format),
        priority: ++priority,
      });
    }

    let currentContext = "";
    if (ctx.currentCell.type === "row") {
      const field = ctx.currentCell.field;
      currentContext = `You are editing:
${ctx.currentCell.objectType ?? "Dataset"}${ctx.currentCell.objectName ? `: ${ctx.currentCell.objectName}` : ""}
field: ${ctx.currentCell.field}
format: ${ctx.currentCell.fmt}

The other fields in the row:
${fmtData(Object.fromEntries(Object.entries(typeof ctx.currentCell.row === "object" && ctx.currentCell.row ? ctx.currentCell.row : {}).filter(([key]) => key !== field)), ctx.currentCell.fmt ?? "yaml")}
        `;
    } else if (ctx.currentCell.type === "function") {
      if (ctx.currentCell.function) {
        functionSnippets.push({
          data: `The current ${ctx.currentCell.functionType} is. You are autocompleting within it:
${serializeFunction(ctx.currentCell.function)}
`,
          priority: priority + 1,
        });
      }
      currentContext = `You are editing a ${ctx.currentCell.functionType}.`;
    } else if (ctx.currentCell.type === "tools") {
      currentContext = `You are editing a tools definition (JSON schema) in ${ctx.currentCell.fmt} format.`;
      functionSnippets.push({
        data: `An example of a complete tools definition is. You can use this as a starting point, but take into account the user's prompt to come up with suggestions.
${fmtData(EXAMPLE_TOOLS, format)}`,
        priority:
          functionSnippets.length > 0
            ? functionSnippets[functionSnippets.length - 1].priority - 1
            : 0,
      });
    }

    const snippets = [
      ...functionSnippets,
      ...dataRowSnippets,
      {
        data: currentContext,
        priority: priority + 1,
      },
    ];

    return snippets;
  },
  {
    asyncFlush: true,
  },
);

function serializeFunction(fn: SlimmedUIFunction) {
  const title = fn.function_data.type === "prompt" ? "Prompt" : "Function";
  const definition: string[] = [];
  if (fn.function_data.type === "prompt") {
    if (fn.prompt_data?.options?.model) {
      definition.push("Model: " + fn.prompt_data?.options?.model);
    }
    if (fn.prompt_data?.prompt?.type === "chat") {
      for (const message of fn.prompt_data?.prompt?.messages.slice(0)) {
        definition.push(`${message.role}: ${message.content}`);
        definition.push(`---`);
      }
    } else if (fn.prompt_data?.prompt?.type === "completion") {
      definition.push(fn.prompt_data?.prompt?.content ?? "");
    }
  } else if (
    fn.function_data.type === "code" &&
    fn.function_data.data.type === "inline"
  ) {
    definition.push(
      "```" +
        (fn.function_data.data.runtime_context.runtime === "node"
          ? "js"
          : "py") +
        "```",
    );
    definition.push(fn.function_data.data.code);
    definition.push("```");
  }
  return `${title}${fn.name ? ": " + fn.name : ""}

${definition.join("\n")}
`;
}

const EXAMPLE_TOOLS = [
  {
    type: "function",
    function: {
      description: "Returns the sum of two numbers.",
      name: "add",
      parameters: {
        type: "object",
        properties: {
          a: {
            type: "number",
            description: "The first number",
          },
          b: {
            type: "number",
            description: "The second number",
          },
        },
        required: ["a", "b"],
      },
    },
  },
];

export function fmtData(data: unknown, format: CopilotContextFormat) {
  if (typeof data === "object" && data) {
    return Object.entries(data)
      .map(([key, value]) => `${key}: ${fmtLeafData(value, format)}`)
      .join("\n");
  } else {
    return fmtLeafData(data, format);
  }
}
function fmtLeafData(data: unknown, format: CopilotContextFormat) {
  if (format === "json") {
    return JSON.stringify(data);
  } else if (format === "yaml") {
    return jsonToYaml(data);
  } else if (format === "text") {
    return serializeJSONWithPlainString(data);
  } else {
    const _: never = format;
    return _;
  }
}

function jsonToYaml(json: unknown) {
  return JSY.dump(json, {
    lineWidth: -1,
    noRefs: true,
    noCompatMode: true,
  }).replace(/\n$/, ""); // Remove the last newline character
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function serializeJSONWithPlainString(v: any) {
  if (typeof v === "string") {
    return v;
  } else {
    return JSON.stringify(v, null, 2);
  }
}
