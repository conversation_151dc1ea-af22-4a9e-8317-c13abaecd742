import mustache from "mustache";
import {
  Chat<PERSON>ompletion,
  ChatCompletionMessageParam,
  ChatCompletionTool,
} from "openai/resources";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";

export interface AISearchResult {
  match: boolean;
  filter?: string;
  sort?: string;
  tags?: string[];
}

export interface AISearchOutput extends AISearchResult {
  explanation?: string;
}

export interface Example {
  input: string;
  expected: AISearchOutput;
}

export function renderExample(
  example: Example,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: Record<string, any>,
): Example {
  const {
    input,
    expected: { match, filter, sort, tags, explanation },
  } = example;
  return {
    input: mustache.render(input, data),
    expected: {
      match: match,
      filter: filter && mustache.render(filter, data),
      sort: sort && mustache.render(sort, data),
      tags: tags && tags.map((t) => mustache.render(t, data)),
      explanation: explanation && mustache.render(explanation, data),
    },
  };
}

export function extractAISearchResult(
  completion: ChatCompletion,
): AISearchOutput {
  const functionCall = completion.choices[0].message.tool_calls?.[0]?.function;
  if (!functionCall) {
    throw new Error(`Expected a function call`);
  }

  const args = functionCall.arguments;
  let functionCallArgs: z.infer<typeof queryFunctionParametersSchema>;
  try {
    functionCallArgs = queryFunctionParametersSchema.parse(JSON.parse(args));
  } catch (e) {
    console.error(e);
    throw new Error("Unable to JSON parse function call arguments");
  }

  return {
    match: functionCallArgs.match,
    filter: functionCallArgs.filter ?? undefined,
    sort: functionCallArgs.sort ?? undefined,
    tags: functionCallArgs.tags ?? undefined,
    explanation: functionCallArgs.explanation,
  };
}

const aiSearchDescriptions = {
  projectExperiments: `rows in a table called "experiments"`,
  projectDatasets: `rows in a table called "datasets"`,
  experiment: `rows in a table representing individual eval cases, called "examples", in an experiment`,
  playground_logs: `rows in a table representing individual eval cases, called "examples", in an experiment`,
  logs: `logs in the \`logs\` table`,
  dataset: `rows in a table representing individual eval cases, called "examples" or "records", in a dataset`,
  prompts: `rows in a table called "prompts" representing saved prompts`,
};

export type AISearchType = keyof typeof aiSearchDescriptions;

export function renderMessages({
  systemTemplate,
  userTemplate,
  query,
  searchType,
  schema,
  scoreFields,
  examples,
}: {
  systemTemplate: string;
  userTemplate: string;
  query: string;
  searchType: AISearchType;
  schema: string;
  scoreFields?: string[];
  examples: Example[];
}): ChatCompletionMessageParam[] {
  const searchDescription = aiSearchDescriptions[searchType];
  return [
    {
      content: mustache.render(systemTemplate.trim(), {
        searchDescription,
        schema,
        scoreFields,
        examples: examples.map(({ input, expected }) => ({
          input: input.trim(),
          expected: JSON.stringify(expected),
        })),
      }),
      role: "system",
    },
    {
      content: mustache.render(userTemplate.trim(), { query }),
      role: "user",
    },
  ];
}

export type Field = {
  type: string;
  description?: string;
  properties?: Record<string, Field>;
  required?: string[];
  nullable?: boolean;
};

export const queryFunctionParametersSchema = z.object({
  match: z
    .boolean()
    .describe("Set to true if the query is a simple substring match"),
  explanation: z.string({
    description:
      "Explanation of why I determined this query to be a match or SQL call",
  }),
  filter: z
    .string({
      description: "SQL filter clause (only if the query is not a match)",
    })
    .nullish(),
  sort: z
    .string({
      description: "SQL filter clause (only if the query is not a match)",
    })
    .nullish(),
  tags: z
    .array(z.string({}))
    .describe("List of tags (only if the query is not a match)")
    .nullish(),
});
export type QueryFunctionParameters = z.infer<
  typeof queryFunctionParametersSchema
>;

export function aiSearchTools({
  includeTags, // Remove?
}: {
  includeTags?: boolean;
}): ChatCompletionTool[] {
  return [
    {
      type: "function",
      function: {
        name: "QUERY",
        description: "Break down the query either into a MATCH or SQL call",
        parameters: zodToJsonSchema(queryFunctionParametersSchema),
      },
    },
  ];
}
