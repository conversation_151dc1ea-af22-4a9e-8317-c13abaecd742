import { ChatCompletionCreateParamsNonStreaming } from "openai/resources";

import {
  AISearchType,
  Example,
  Field,
  aiSearchTools,
  renderMessages,
} from "./ai-search";
import { projectDatasetsRowSchema } from "./project-datasets";
import { projectExperimentsRowSchema } from "./project-experiments";

export const btqlSystemTemplate = `
You are helping a user search for {{{ searchDescription }}}. The user will provide a search query that you must interpret. If the query has semantic meaning and refers to one or more columns
(or keys within JSON columns) in the table, you should translate the query into a valid SQL filter and/or sort clause, then call the "SQL" function with your SQL filter and/or sort clause
as arguments. Otherwise, if it is unclear how to translate the query into an equivalent SQL filter or sort, you should fall back to calling the default "MATCH" function, which runs a basic
free text search that is guaranteed to run without syntax errors.

Each example has the following schema (wrapped in XML tags):
<Schema>
{{{ schema }}}
</Schema>

Next, I will provide some directives that you should follow when interpreting the user's query. Refer to these by number when writing your explanation.
1. Do not reference columns that aren't in the provided schema when calling the SQL function! A query like \`env = 'braintrust'\` clearly has semantic meaning, but there is no "env" column in the schema or anything conceptually related to the weather, so this filter would cause a SQL syntax error. Similarly, a query like \`order by height\` should not be translated into a SQL sort clause because there is no "height" field in the schema! In cases like these, err on the side of caution by calling the MATCH function to fall back to a simple substring match.
1b. However, fields like "scores" or "metadata" are JSON columns, and you should reference keys within these columns when constructing filters and sorts. For example, to reference the "Austin" score in the "scores" column, use the syntax \`scores."Austin"\`.
2. Any query that is entirely wrapped in single or double quotes or backticks is a simple substring match, and should be passed directly to the MATCH function.
3. Any query with terms like "contains", "in", or "includes", when referencing a table column (top-level field in the provided schema), should use the \`ILIKE '%{FILTER}%'\` syntax to invoke a substring match.
4. To extract fields from JSON columns, use the \`.\` operator and wrap each key in double quotes. For example, to extract the 'Austin' score from the "scores" column, use \`scores."Austin"\`.
5. If the query contains typos and you are able to understand the intent of the query, you should do your best to translate the output to a valid SQL filter or sort.
6. Always err on the side of using parentheses to establish order of operations. Wrap separate filter clauses separated by AND/OR in parentheses.
7. If the query contains string literals with the wildcard operator \`*\`, you should replace the each wildcard with the SQL operator \`%\` and create a valid SQL filter using the \`LIKE\` or \`ILIKE\` operator. For example, \`metadata.name is 'ben*A*bob'\` should be translated to \`metadata.name LIKE 'ben%A%bob'\`.
8. Any word that is prefixed with a "+" or "-" should be treated as a tag. Ignore tags when constructing filters and sorts. If tags are present, call the "SQL" function and pass the tags as an array in the "tag" field, stripping any quotes/delimiters. For example, \`+foo, -bar, +princess\` are a set of 3 tags, which should be returned as the array \`["+foo", "-bar", "+princess"]\`.

To help calibrate your responses, here are some sample queries and the corresponding function calls, each wrapped in a pair of \`Case\` XML tags:

{{#examples}}
<Case>
Query: {{{input}}}
Result: {{{expected}}}
</Case>

{{/examples}}
That's it for the examples!

When the user provides a query, you should try to interpret it and call the appropriate function. Remember, if the query has semantic meaning and you can translate it into a valid SQL filter and/or sort clause (or it contains tags) call the SQL function and pass it your filter, sort, and/or tags as arguments; do not include \`WHERE\` or \`ORDER BY\`, since these keywords will be prepended automatically by the function. Otherwise, if there is any doubt, fall back to a simple substring match and call the MATCH function instead.
`;

const userTemplate = `
{{{ query }}}
`;

const examples: Example[] = [
  {
    input: "metrics->'accuracy' at least 0.9",
    expected: {
      match: false,
      explanation:
        "The query refers to a JSON field, so we use the `->` operator to extract the string value, then wrap the entire chain in parentheses. The query is a greater-than-or-equal-to filter, so we use the greater-than-or-equal-to operator `>=` to compare the extracted value to the number literal `0.9`.",
      filter: "metrics->>'accuracy' >= 0.9",
    },
  },
  {
    input: "metadata.foo.bar = 'some string'",
    expected: {
      match: false,
      explanation:
        "This is a simple equality filter on a JSON field, so we use the `->` operator to extract the string value and compare it to the string literal `'some string'.",
      filter: `metadata."foo"."bar" = 'some string'`,
    },
  },
  {
    input: "vxlkje",
    expected: {
      match: true,
      explanation:
        "The query does not have semantic meaning that can be related to the named columns and JSON fields in the provided schema, so I default to calling the MATCH function to be safe.",
    },
  },
  {
    input: "+basketry, expected contains kevin",
    expected: {
      match: false,
      explanation:
        "The query refers to the `expected` column, so we use the `ILIKE` operator to check if the extracted string value contains the string literal `kevin`. We also return the `+basketry` tag in the Tag field.",
      filter: `expected ILIKE '%kevin%'`,
      tags: ["+basketry"],
    },
  },
  {
    input: "exp` + `cted[zoo]['animals']['numLimbs'] less than 4",
    expected: {
      match: false,
      explanation:
        "Even though there are typos and invalid syntax, the query is clearly referring to a field deep within the `expected` JSON column, which appears in the provided schema. I correct the syntax by using `->` operators to drill down through the JSON object and extract the bottom-level scalar field.",
      filter: `expected."zoo"."animals"."numLimbs" < 4`,
    },
  },
  {
    input: "highest scores.{{ scoreFields.0 }}",
    expected: {
      match: false,
      explanation:
        "The query refers to a score field in the provided schema, so we use the `->` operator to extract the value from the JSON object. The user wants the highest scores first, so we apply a descending sort.",
      sort: `scores."{{{ scoreFields.0 }}}" DESC`,
    },
  },
  {
    input: "input.requests['Big Header'] contains' 'hello",
    expected: {
      match: false,
      explanation:
        "The query refers to a JSON field, so we use the `->` operator to extract the string value. The stray quote appears to be a typo, so we correct it according to directive 5, and translate 'contains' to an ILIKE filter as per directive 3.",
      filter: `input."requests"."Big Header" ILIKE '%hello%'`,
    },
  },
  {
    input: "a43kj in expected.source.exampleIds",
    expected: {
      match: false,
      explanation:
        "The query refers to a JSON field, so we use the `->` operator to extract the string value, then wrap the entire chain in parentheses. The query is a less-than filter, so we use the less-than operator `<` to compare the extracted value to the scalar value `0.3`.",
      filter: `expected."source"."exampleIds" ILIKE '%a43kj%'`,
    },
  },
  {
    input: "`metadata.cycles = foo*bar*baz",
    expected: {
      match: false,
      explanation:
        "This appears to be a misspelled reference to the `metadata` column, so we correct the typos and proceed as per directive 5. I convert the wildcard operators `*` to `%` and apply the `LIKE` operator, according to directive 7.",
      filter: `metadata."cycles" LIKE 'foo%bar%baz'`,
    },
  },
  {
    input: "-pine, -M, +t1, +P42BCjf09278",
    expected: {
      match: false,
      explanation:
        "The query contains 4 tags, which I pass directly to the Tag field as an array. The remainder of the query refers to a sort on the {{ scoreFields.0 }} field in the `scores` JSON column, so we use the `->` operator to extract the string value from `scores`. The query is asking for the lowest values, so we apply an ascending sort.",
      sort: `scores."{{{ scoreFields.0 }}}" ASC`,
      tags: ["-pine", "-M", "+t1", "+P42BCjf09278"],
    },
  },
  {
    input: "source.id = 'abc'",
    expected: {
      match: true,
      explanation:
        "The query appears to reference a column named `source`, but there is no `source` column in the provided schema, so we fall back to a simple substring match.",
    },
  },
  {
    input: "scores.brightness < 0.2",
    expected: {
      match: true,
      explanation:
        "The query appears to refer to a field in the `scores` JSON column, but there is no `brightness` field on the `scores` column in the provided schema, so we fall back to calling the MATCH function.",
    },
  },
  {
    input: "+zoo -goo -x, lowest {{ scoreFields.0 }}",
    expected: {
      match: false,
      explanation:
        "The query contains 3 tags, which are passed directly to the Tag field as an array. The remainder of the query refers to a sort on the {{ scoreFields.0 }} field in the `scores` JSON column, so we use the `->` operator to extract the string value from `scores`. The query is asking for the lowest values, so we apply an ascending sort.",
      sort: `scores."{{{ scoreFields.0 }}}" ASC`,
      tags: ["+zoo", "-goo", "-x"],
    },
  },
  {
    input: "+gastronomic",
    expected: {
      match: false,
      explanation:
        "The query refers to a single positive tag `+gastronomic`, so we call the SQL function and pass the tag as a single-element array as per directive 9.",
      tags: ["+gastronomic"],
    },
  },
  {
    input: "-32%ballons94!ab3d",
    expected: {
      match: false,
      explanation:
        "The query refers to a single negative tag `-32%ballons94!ab3d`, so we call the SQL function and pass the tag array according to directive 9.",
      tags: ["-32%ballons94!ab3d"],
    },
  },
  {
    input: "{{{ scoreFields.1 }}} >= 0.5, -mouse, +tiger",
    expected: {
      match: false,
      explanation:
        "The {{ scoreFields.1 }} field exists in the `scores` object in the provided schema, so I supply the correct syntax to extract the field from the `scores` column, according to directive 5. I also return the `-mouse` and `+tiger` tags according to directive 9.",
      filter: `scores."{{{ scoreFields.1 }}}" >= 0.5`,
      tags: ["-mouse", "+tiger"],
    },
  },
  {
    input: "tallest",
    expected: {
      match: true,
      explanation:
        "The query does not have semantic meaning that can be related to the named columns and JSON fields in the provided schema, so we fall back to a simple substring match to be safe.",
    },
  },
  {
    input: "output like asdf",
    expected: {
      match: false,
      explanation:
        "The query refers to the `output` column, so we use the `ILIKE` operator to check if the extracted string value contains the string literal `asdf`.",
      filter: `output ILIKE '%asdf%'`,
    },
  },
  {
    input: "input contains adam",
    expected: {
      match: false,
      explanation:
        "The query refers to the `input` column, so we use the `ILIKE` operator to check if the extracted string value contains the string literal `adam`.",
      filter: `input ILIKE '%adam%'`,
    },
  },
];
const fakeScoreFields = ["Braintrust", "brain trust"];

function rowSchema({
  aiSchemaColumns,
  scoreFields,
}: {
  aiSchemaColumns: string[];
  scoreFields: string[];
}): Field {
  const aiSchemaFields = aiSchemaColumns
    .filter((c) => c !== "scores")
    .reduce(
      (acc, field) => {
        acc[field] = {
          type: "object",
          description: field,
        };
        return acc;
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      {} as Record<string, Field>,
    );

  const scoresField = {
    type: "object",
    description: "Score fields available for filter and sort",
    properties: scoreFields.reduce(
      (acc, field) => {
        acc[field] = {
          type: "number",
          description: field,
        };
        return acc;
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      {} as Record<string, Field>,
    ),
  };

  return {
    type: "object",
    properties: {
      ...aiSchemaFields,
      scores: scoresField,
    },
  };
}

export function eventsSearchCompletionArgs({
  query,
  searchType,
  aiSchemaColumns,
  scoreFields: realScoreFields,
}: {
  query: string;
  searchType: AISearchType;
  aiSchemaColumns: string[];
  scoreFields: string[];
  btql: boolean;
}): ChatCompletionCreateParamsNonStreaming {
  const scoreFields = [...realScoreFields, ...fakeScoreFields];
  return {
    model: "gpt-4o",
    temperature: 0,
    messages: renderMessages({
      systemTemplate: btqlSystemTemplate,
      userTemplate,
      query,
      searchType,
      schema: JSON.stringify(
        searchType === "projectDatasets"
          ? projectDatasetsRowSchema(aiSchemaColumns)
          : searchType === "projectExperiments"
            ? projectExperimentsRowSchema(aiSchemaColumns)
            : rowSchema({ aiSchemaColumns, scoreFields }),
      ),
      scoreFields,
      examples,
    }),
    tools: aiSearchTools({ includeTags: true }),
    tool_choice: {
      type: "function",
      function: { name: "QUERY" },
    },
  };
}
