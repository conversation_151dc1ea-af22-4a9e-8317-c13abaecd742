import { Field } from "./ai-search";

export function projectDatasetsRowSchema(aiSchemaColumns: string[]): Field {
  return {
    type: "object",
    properties: {
      id: {
        type: "string",
        description: "Dataset ID, unique",
      },
      name: {
        type: "string",
        description: "Name of the dataset",
      },
      last_updated: {
        type: "number",
        description:
          "Timestamp marking when the dataset was last updated. If the query deals with some notion of relative time, like age or recency, refer to this timestamp and, if appropriate, compare it to the current time `get_current_time()` by adding or subtracting an interval.",
      },
      num_examples: {
        type: "number",
        description:
          "Number of examples in the dataset. Synonyms for `examples` include `rows`, `records`, and `events`, so any mention of these should be interpreted as a reference to this field.",
      },
    },
    required: [
      "id",
      "name",
      "last_updated",
      "num_examples",
      ...aiSchemaColumns,
    ],
  };
}
