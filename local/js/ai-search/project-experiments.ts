import { Field } from "./ai-search";

export function projectExperimentsRowSchema(aiSchemaColumns: string[]): Field {
  const aiSchemaFields = aiSchemaColumns.reduce(
    (acc, col) => {
      acc[col] = {
        // TODO: Get each field's JSON schema type from the table schema.
        type: "number",
        description: col,
      };
      return acc;
    },
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    {} as Record<string, Field>,
  );

  // This schema is intended to match the result of the query built by `buildExperimentsQuery`
  // on the project page.
  // https://github.com/braintrustdata/braintrust/blob/d8a3e0c/app/app/app/[org]/p/[project]/clientpage.tsx#L316
  // TODO: We should consolidate these schema definitions and field descriptions in one place.
  return {
    type: "object",
    properties: {
      ...aiSchemaFields,
      id: {
        type: "string",
        description: "Experiment ID, unique",
      },
      name: {
        type: "string",
        description: "Name of the experiment",
      },
      last_updated: {
        type: "number",
        description:
          "Timestamp marking when the experiment was last updated. If the query deals with some notion of relative time, like age or recency, refer to this timestamp and, if appropriate, compare it to the current time `get_current_time()` by adding or subtracting an interval.",
      },
      creator: {
        type: "object",
        description: "Information about the experiment creator",
        properties: {
          id: {
            type: "string",
            description: "Creator ID, unique",
          },
          given_name: {
            type: "string",
            description: "Creator given (first) name",
          },
          family_name: {
            type: "string",
            description: "Creator family (last) name",
          },
          email: {
            type: "string",
            description: "Creator email address",
          },
          avatar_url: {
            type: "string",
            description: "Creator avatar URL",
          },
        },
        required: ["id", "given_name", "family_name", "email", "avatar_url"],
      },
      source: {
        type: "string",
        description: "Git state that the experiment was run on",
        properties: {
          commit: {
            type: "string",
            description: `Git commit hash. Any prefix of this hash at least 7 characters long should be considered an exact match, so use a substring filter rather than string equality to check the commit, e.g. """source.commit ILIKE '{COMMIT}%'"""`,
          },
          branch: {
            type: "string",
            description: "Git branch name",
          },
          tag: {
            type: "string",
            description: "Git commit tag",
          },
          commit_time: {
            type: "number",
            description: "Git commit timestamp",
          },
          author_name: {
            type: "string",
            description: "Author of git commit",
          },
          author_email: {
            type: "string",
            description: "Email address of git commit author",
          },
          commit_message: {
            type: "string",
            description: "Git commit message",
          },
          dirty: {
            type: "boolean",
            description:
              "Whether the git state was dirty when the experiment was run. If false, the git state was clean",
          },
        },
        required: [
          "commit",
          "branch",
          "tag",
          "commit_time",
          "author_name",
          "author_email",
          "commit_message",
          "dirty",
        ],
      },
      num_examples: {
        type: "number",
        description: "Number of examples run in the experiment",
      },
      metadata: {
        type: "object",
        description:
          "Custom metadata provided by the user. Ignore this field unless the query mentions metadata or refers to a metadata key specifically",
      },
    },
    required: [
      "id",
      "name",
      "last_updated",
      "creator",
      "source",
      "metadata",
      ...aiSchemaColumns,
    ],
  };
}
