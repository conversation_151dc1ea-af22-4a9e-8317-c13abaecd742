// Returns a filtered version of the object.
export function filterObject<K extends string, V>(
  obj: Record<K, V>,
  pred: (k: K, v: V) => boolean,
): Record<K, V> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    Object.entries(obj).filter(([k, v]) => pred(k as K, v as V)),
  ) as Record<K, V>;
}

// Returns a mapped version of the object.
export function mapObject<K extends string, V, V2>(
  obj: Record<K, V>,
  f: (k: K, v: V) => V2,
): Record<K, V2> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    Object.entries(obj).map(([k, v]) => [k as K, f(k as K, v as V)]),
  ) as Record<K, V2>;
}
// Lexicographic three-way comparison of keys between two objects. Returns 0 if
// all keys are equal between the objects, -1 if the left object compares less
// than, and 1 if it compares greater than. If keys is unspecified, uses
// `Object.keys(lhs)` (which will be in increasing numerical order for Arrays).
//
// undefined is treated the same as not being in the object.
export function lexicographicalComparison(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  lhs: Record<string, any>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  rhs: Record<string, any>,
  keys?: string[],
): number {
  const lhsNoUndefineds = filterObject(lhs, (_, v) => v !== undefined);
  const rhsNoUndefineds = filterObject(rhs, (_, v) => v !== undefined);
  const compareKeys = keys ?? Object.keys(lhsNoUndefineds);
  for (const key of compareKeys) {
    const lhsVal = lhsNoUndefineds[key];
    const rhsVal = rhsNoUndefineds[key];
    // Note that 'keys' may still include keys not in lhs, so we must still
    // account for lhsVal being undefined.
    if (lhsVal === undefined && rhsVal !== undefined) {
      return -1;
    } else if (lhsVal !== undefined && rhsVal === undefined) {
      return 1;
    } else if (lhsVal < rhsVal) {
      return -1;
    } else if (lhsVal > rhsVal) {
      return 1;
    }
  }
  return 0;
}
