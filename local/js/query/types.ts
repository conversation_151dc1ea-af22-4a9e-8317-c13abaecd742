import { singleQuote } from "./sql-utils";

const tagTypes = ["+", "-"];
type TagType = (typeof tagTypes)[number];

export type TypedTag = { type: TagType; text: string };

export function makeTypedTags(tags?: string[]): TypedTag[] {
  return (
    tags
      ?.map((tag) => ({ type: tag[0], text: tag.slice(1) }))
      .filter((t): t is TypedTag => tagTypes.includes(t.type)) ?? []
  );
}

export function makeTagFilter({ type, text }: TypedTag): string {
  const contains = `json_contains(tags, ${singleQuote(JSON.stringify(text))})`;
  return type === "+" ? contains : `COALESCE(${contains}, false) = false`;
}

export function batch<T>(arr: T[], batchSize: number): T[][] {
  const batches: T[][] = [];
  for (let i = 0; i < arr.length; i += batchSize) {
    batches.push(arr.slice(i, i + batchSize));
  }
  return batches;
}
