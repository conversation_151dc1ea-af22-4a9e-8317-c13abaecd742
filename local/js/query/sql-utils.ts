export function singleQuote(s: string) {
  return `'${s.replace(/'/g, "''")}'`;
}

export function doubleQuote(s: string) {
  return `"${s.replace(/"/g, '""')}"`;
}

export function doubleQuoteBlackslash(s: string) {
  return `"${s.replace(/"/g, '\\"')}"`;
}

export function ident(s: string | string[]) {
  if (typeof s === "string") {
    return doubleQuote(s);
  } else {
    return s.map(doubleQuote).join(".");
  }
}

// Note: keep this aligned with btql/planner/util.ts
export function jsonPath(path: string[]) {
  // In DuckDB, double quotes for SQL are escaped by repeating the quote character:
  // https://duckdb.org/docs/sql/dialect/keywords_and_identifiers.html
  // Double quotes in json path strings for DuckDB need to be escaped with backslashes.
  return singleQuote(["$"].concat(path.map(doubleQuoteBlackslash)).join("."));
}

// Note: keep this aligned with btql/planner/util.ts
export function jsonGet(field: string, path: string[], typeName?: string) {
  return (
    `json_extract(${field}, ${jsonPath(path)})` +
    (typeName ? `::${typeName}` : "")
  );
}

export function compileSort({
  path,
  desc,
}: {
  path: string[];
  desc?: boolean;
}) {
  return `${ident(path)} ${desc ? "DESC" : "ASC"}`;
}

export type NestedField = string[];

export function unfoldNestedFields(
  parentPath: NestedField,
  field: unknown,
): NestedField[] {
  if (field && field instanceof Object && !Array.isArray(field)) {
    return Object.entries(field).flatMap(([k, v]) =>
      unfoldNestedFields(parentPath.concat(k), v),
    );
  } else {
    return [parentPath];
  }
}

export function flattenNestedFields(fields: NestedField[]): string[] {
  return fields.map((f) => {
    if (f.length > 1) {
      throw new Error(`Expected nested field to be empty: ${f}`);
    }
    return f[0];
  });
}

export const IdField = "id";
export const SpanIdField = "span_id";
export const RootSpanIdField = "root_span_id";
export const TransactionIdField = "_xact_id";
export const UpdateNonceField = "_update_nonce";
export const ObjectDeleteField = "_object_delete";
export const CreatedField = "created";
export const UserIdField = "user_id";
export const OrgIdField = "org_id";
export const LogIdField = "log_id";
export const ProjectIdField = "project_id";
export const ExperimentIdField = "experiment_id";
export const DatasetIdField = "dataset_id";
export const PromptSessionIdField = "prompt_session_id";
export const ScoresField = "scores";
export const MetricsField = "metrics";
export const MetadataField = "metadata";
export const SpanAttributesField = "span_attributes";
export const AuditSourceField = "_audit_source";
export const AuditMetadataField = "_audit_metadata";
export const OutputField = "output";
export const ExpectedField = "expected";
export const TagsField = "tags";
export const TagFilterField = "__tag_filter";
export const CostField = "estimated_cost";
export const OriginField = "origin";

export const ComputedDurationMetricFields = [
  "duration",
  "llm_duration",
] as const;
export const ComputedCostMetricFields = ["estimated_cost"] as const;
export const ComputedTokenMetricFields = [
  "prompt_tokens",
  "completion_tokens",
  "total_tokens",
  "prompt_cached_tokens",
  "prompt_cache_creation_tokens",
] as const;
export const ComputedMetricFields = [
  "cached",
  ...ComputedDurationMetricFields,
  ...ComputedTokenMetricFields,
  ...ComputedCostMetricFields,
] as const;
