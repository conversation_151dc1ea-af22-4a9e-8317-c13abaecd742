import { Evaluators } from "autoevals";
import { GetAvailableScorersResult } from "./get-scorers";
import { Tool } from "./types";
import { z } from "zod";

export const editScorersToolResultSchema = z.object({
  ok: z.boolean(),
});
export type EditScorersToolResult = z.infer<typeof editScorersToolResultSchema>;

export const editScorersParamsSchema = z.object({
  scorers: z
    .array(
      z.object({
        id: z.string().describe("The id of the scorer."),
        name: z.string().describe("The name of the scorer."),
        description: z.string().describe("The description of the scorer."),
        enabled: z
          .boolean()
          .describe("Whether the scorer is being added or removed."),
      }),
    )
    .describe("The scorers to add or remove."),
});
export type EditScorersParams = z.infer<typeof editScorersParamsSchema>;

export const EditScorersTool: Tool<EditScorersParams, EditScorersToolResult> = {
  description:
    "Add or remove the use of specific pre-existing scorers. You can use the get_available_scorers tool to get a list of available scorers.",
  parameters: editScorersParamsSchema,
};

export function getGlobalScorers(): GetAvailableScorersResult[] {
  return Evaluators.flatMap((category) =>
    category.methods.map((method) => ({
      id: method.method.name,
      name: method.method.name,
      description: method.description + " (" + category.label + ")",
    })),
  );
}
