import { z } from "zod";
import { type Tool } from "./types";
import { applyPatch } from "diff";
import { deserializePlainStringAsJSON, Span } from "braintrust";
import { SpanTypeAttribute } from "braintrust/util";

export const editTaskToolParams = z.object({
  index: z.number().describe("The index of the task to edit"),
  edits: z.array(
    z.object({
      path: z
        .array(z.union([z.string(), z.number()]))
        .describe(
          "The path to the value to edit (relative to definition). Use strings for keys and numbers (0-indexed) for array indices. The path must point to a value (e.g. a string), not an array or object.",
        ),
      diff: z
        .string()
        .optional()
        .describe(
          `A string representing the diff in standard unified format (e.g., from \`git diff\`). Only use this to edit string values.
IMPORTANT:
1. The entire diff string must be correctly formatted. Each line *in the diff text itself* (hunks, context, added/removed lines) must end with a newline character (\`\\n\`).
2. For multi-line changes, each actual line of added content must start with a \`+\` character and be a separate line in the diff input string.
3. Empty lines being added should be represented as a line containing just \`+\` followed by \`\\n\`.

Example: To change 'Old' to 'New\\nLine2', the diff string provided should be like: \`'@@ -1 +1,2 @@\\n-Old\\n+New\\n+Line2\\n'\`

Incorrect newlines or \`+\` prefixes in the diff string will result in malformed output.
Applied using \`applyPatch\` from the \`diff\` package in Javascript.`,
        ),
      replace: z
        .string()
        .optional()
        .describe(
          `A string to replace the value at the path with. You can either provide a string or a JSON object.

Example: To change 'Old' to 'New\\nLine2', the replace string provided should be like: \`'New\\nLine2'\`

This is also a good way to change objects. For example, to change the object

{"key": "value"} to {"key": "newValue"}

the replace string provided should be like: \`'{"key": "newValue"}'\``,
        ),
    }),
  ),
});
export type EditTaskToolParameters = z.infer<typeof editTaskToolParams>;

export const editTaskToolResultSchema = z.object({
  ok: z.boolean(),
});
export type EditTaskToolResult = z.infer<typeof editTaskToolResultSchema>;

export const EditTaskTool: Tool<EditTaskToolParameters, EditTaskToolResult> = {
  description: "Edit a task",
  parameters: editTaskToolParams,
};

export function applyEdits(
  promptData: unknown,
  edits: EditTaskToolParameters["edits"],
  span: Span,
) {
  let promptDataJson = JSON.parse(JSON.stringify(promptData));
  for (const edit of edits) {
    if (!edit.diff && !edit.replace) {
      throw new Error("Either diff or replace must be provided");
    } else if (edit.diff && edit.replace) {
      throw new Error("Only one of diff or replace can be provided");
    }

    // strip "definition" from the path
    let fullPath = [...edit.path];
    if (fullPath[0] === "definition") {
      fullPath = fullPath.slice(1);
    }
    let parent = promptDataJson;
    for (const part of fullPath.slice(0, -1)) {
      parent = parent[part];

      if (!parent) {
        throw new Error(
          `Path piece '${part}' from ${edit.path.join(".")} not found in prompt data`,
        );
      }
    }
    const lastPiece = edit.path[edit.path.length - 1];
    if (!lastPiece && edit.path.length > 0) {
      throw new Error(
        `Path piece '${lastPiece}' from ${edit.path.join(".")} not found in prompt data`,
      );
    }

    const lastValue = lastPiece ? parent[lastPiece] : parent;

    let afterPatch;
    if (typeof lastValue === "string") {
      if (edit.diff) {
        const diff = edit.diff;
        afterPatch = span.traced(
          (span) => {
            const output = applyPatch(lastValue, diff);
            span.log({ output });
            if (!output) {
              throw new Error("Failed to apply patch");
            }
            return output;
          },
          {
            event: {
              input: {
                base: lastValue,
                diff,
              },
            },
            name: "applyPatch",
            type: SpanTypeAttribute.FUNCTION,
          },
        );
      } else if (edit.replace) {
        afterPatch = edit.replace;
      }
    } else {
      if (edit.diff) {
        throw new Error(
          `Non-string value cannot be edited with diff: ${edit.diff}`,
        );
      } else if (edit.replace) {
        afterPatch = deserializePlainStringAsJSON(edit.replace).value;
      }
    }

    if (afterPatch === false) {
      throw new Error("Failed to apply patch");
    }

    if (lastPiece) {
      parent[lastPiece] = afterPatch;
    } else {
      promptDataJson = afterPatch;
    }
  }

  return promptDataJson;
}
