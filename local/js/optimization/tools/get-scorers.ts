import { z } from "zod";
import { type Tool } from "./types";
import { PromptData } from "@braintrust/typespecs";

export const getAvailableScorersParamsSchema = z.object({
  id: z
    .string()
    .optional()
    .describe(
      "Leave empty to get an overview of available scorers. Provide id if looking for a specific scorer that matches the provided id",
    ),
});
export type GetAvailableScorersParams = z.infer<
  typeof getAvailableScorersParamsSchema
>;

export const getAvailableScorersResultSchema = z.object({
  id: z.string().describe("The id of the scorer."),
  name: z.string().describe("The name of the scorer."),
  description: z.string().describe("The description of the scorer."),
  code: z
    .string()
    .optional()
    .describe("The code of the scorer (for code-based scorers)."),
  runtime: z
    .string()
    .optional()
    .describe(
      "The runtime context (python/typescript) for code-based scorers.",
    ),
  prompt: z
    .custom<PromptData>()
    .optional()
    .describe("The prompt data for LLM as a judge scorers."),
  slug: z.string().nullable().optional().describe("The slug of the scorer."),
  type: z.string().optional().describe("The type of the scorer."),
});
export type GetAvailableScorersResult = z.infer<
  typeof getAvailableScorersResultSchema
>;

export const GetAvailableScorersTool: Tool<
  GetAvailableScorersParams,
  GetAvailableScorersResult[]
> = {
  description:
    "Get a list of scorers that could be added or removed from the eval using the edit_scorers tool. This is helpful when you want to add or remove a scorer from the eval.",
  parameters: getAvailableScorersParamsSchema,
};
