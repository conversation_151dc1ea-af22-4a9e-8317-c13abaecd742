import { z } from "zod";
import { type Tool } from "./types";

export const continueExecutionToolParams = z.object({
  continue: z
    .boolean()
    .describe(
      "Request whether to continue execution from the user. If true, the user will be prompted to continue execution.",
    ),
});
export type ContinueExecutionToolParameters = z.infer<
  typeof continueExecutionToolParams
>;

export const continueExecutionToolResult = z.object({
  allowed: z.boolean(),
});
export type ContinueExecutionToolResult = z.infer<
  typeof continueExecutionToolResult
>;

export const ContinueExecutionTool: Tool<
  ContinueExecutionToolParameters,
  ContinueExecutionToolResult
> = {
  description: "Continue execution from the user",
  parameters: continueExecutionToolParams,
};
