import { z } from "zod";
import { type Tool } from "./types";
import { freshnessStateSchema, realtimeStateSchema } from "../../app-schema";
import { BTQLDescription, dataSourceSchema, entityType } from "./query";

export const runBtqlToolParams = z.object({
  title: z.string().describe(
    `A very concise title for this query. This is used to identify the query in the UI.

Examples:
- "Errors < 24 hrs"
- "Most active users"
- "User confusion"
- "Model performance"`,
  ),
  explanation: z.string().describe(
    `A brief explanation of what you're trying to find or analyze with this query. This helps provide context for the query results.

Examples:
- "Finding all errors in the last 24 hours to identify what's causing failures"
- "Analyzing which users are most active to understand usage patterns"
- "Looking for examples where users expressed confusion or frustration"
- "Comparing model performance across different scoring dimensions"`,
  ),

  dataSource: dataSourceSchema,

  query: z.string().describe(
    `A BTQL query WITHOUT the 'from' clause. The tool will automatically add the correct from clause based on the data source.

IMPORTANT: Use dot notation for nested fields! If InferSchemaTool shows ['metadata', 'user_id'], use metadata.user_id in queries. Fields are case sensitive.

Examples of valid queries:
- "select: *" - Get all fields from recent logs
- "select: * | filter: metadata.user_id = 'user123' | limit: 10" - Find logs for specific user (NOT user_id alone!)
- "select: input, output, metadata.model | filter: metadata.model = 'gpt-4'" - Filter by nested field
- "dimensions: metadata.model | measures: count(1) as calls, avg(scores.Factuality) as avg_score" - Group by model
- "dimensions: metadata.user_id | measures: count(1) as activity | sort: activity desc | limit: 10" - Most active users
- "select: * | filter: scores.Factuality > 0.8" - Filter by score value (use scores.Factuality, not just Factuality)
- "dimensions: hour(created) as hour | measures: count(error) as errors | filter: created > now() - interval 1 day" - Hourly errors
- "select: metadata.model, metrics" - Get model and ALL metrics fields (NOT metrics.*, just metrics)`,
  ),

  shape: z
    .enum(["spans", "traces", "summary"])
    .optional()
    .describe(
      `The shape of data to return:
- spans: Returns individual spans (default)
- traces: Returns all spans from traces that contain at least one matching span
- summary: Returns one row per trace with aggregated metrics`,
    ),
});

/**
 * Kept for backwards compatibility when displaying old chat sessions.
 * Remove ~november 2025
 */
export const runBtqlToolParamsLegacy = runBtqlToolParams.extend({
  dataSource: z
    .object({
      entity: entityType,
      id: z.string().optional(),
    })
    .optional(),
});

export type RunBtqlToolParameters = z.infer<typeof runBtqlToolParams>;

const btqlResponseSchema = z.object({
  data: z.array(z.record(z.unknown())),
  schema: z.object({
    type: z.literal("array"),
    items: z.object({
      type: z.literal("object"),
      properties: z.record(z.unknown()),
    }),
  }),
  cursor: z.string().optional(),
  realtime_state: realtimeStateSchema.optional(),
  freshness_state: freshnessStateSchema.optional(),
  /** Legacy, no longer included in tool output but kept for backwards compatibility when displaying old chat sessions. */
  dataSource: z
    .object({
      entity: z.string().describe("The entity type that was queried"),
      id: z.string().describe("The ID of the entity that was queried"),
    })
    .optional()
    .describe("The data source that was queried"),
});

export const runBtqlToolResultSchema = btqlResponseSchema;

export type RunBtqlToolResult = z.infer<typeof runBtqlToolResultSchema>;

export const RunBtqlTool: Tool<RunBtqlToolParameters, RunBtqlToolResult> = {
  description: `Transform natural language or existing BTQL (Braintrust Query Language) queries into executable BTQL queries that match the user's intent.
${BTQLDescription}`,
  parameters: runBtqlToolParams,
};
