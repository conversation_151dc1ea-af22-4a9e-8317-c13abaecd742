import { z } from "zod";
import { type Tool } from "./types";

export const docsToolParamsSchema = z.object({
  query: z
    .string()
    .describe(
      `A search string to find relevant documentation. The string will be embedded and then used to search for relevant snippets in a vector database.`,
    ),
  topK: z
    .number()
    .int()
    .positive()
    .optional()
    .describe(
      "The number of top relevant document snippets to return. Higher values return more results but may include less relevant snippets.",
    ),
});

export type DocsToolParams = z.infer<typeof docsToolParamsSchema>;

export const docsToolResultSchema = z.array(
  z.object({
    id: z
      .string()
      .describe(
        "The unique identifier of the document snippet (a path into the docs)",
      ),
    headers: z
      .array(z.string())
      .describe("The header path of the document snippet"),
    content: z
      .string()
      .describe(
        "The content of the document snippet, which is a markdown string",
      ),
    path: z
      .string()
      .describe("The relative path to the document for constructing a URL"),
  }),
);

export type DocsToolResult = z.infer<typeof docsToolResultSchema>;

export const DocsTool: Tool<DocsToolParams, DocsToolResult> = {
  description: `Search Braintrust documentation to find relevant information and guidance.

This tool searches through the official Braintrust documentation using semantic search to find the most relevant content for your query.

What it returns:
- Multiple documentation snippets ranked by relevance
- Each snippet includes the content, headers hierarchy, and path
- Results are markdown-formatted and ready to use

Use this tool to:
- Find how to use specific Braintrust features
- Look up API references and code examples
- Understand concepts like experiments, logging, scoring, and evaluation
- Get guidance on best practices and workflows
- Discover configuration options and parameters

Example queries:
- "How do I create an experiment?"
- "BTQL query syntax"
- "Setting up scorers"
- "Using metadata fields"
- "Prompt playground features"`,

  parameters: docsToolParamsSchema,
};
