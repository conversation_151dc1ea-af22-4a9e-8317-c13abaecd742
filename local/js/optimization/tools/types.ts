import { Span } from "braintrust";
import { type z } from "zod";

export interface Tool<Input, Result> {
  description: string;
  parameters: z.ZodSchema<Input>;
  __phantom?: Result;
}

export type ToolHandler<Definition extends Tool<unknown, unknown>> = (
  parameters: z.infer<Definition["parameters"]>,
  span: Span,
  abortController?: AbortController,
) => Definition extends Tool<unknown, infer Result> ? Promise<Result> : never;
