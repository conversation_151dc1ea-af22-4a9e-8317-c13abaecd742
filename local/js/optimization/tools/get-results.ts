import { z } from "zod";
import { type Tool } from "./types";
import { FunctionObject, PromptData } from "@braintrust/typespecs";

export const scoreSummarySchema = z.object({
  avg: z.number().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
});

export type ScoreSummary = z.infer<typeof scoreSummarySchema>;

export const inlineTaskDefinitionSchema = z.object({
  id: z.string(),
  inline_prompt: z.custom<PromptData>(),
  inline_function: z.custom<FunctionObject>(),
});
export type InlineTaskDefinition = z.infer<typeof inlineTaskDefinitionSchema>;

export const getSummaryToolResultSchema = z.object({
  taskName: z.string(),
  definition: z.union([
    z.custom<Omit<PromptData, "origin">>(),
    z.custom<FunctionObject>(),
  ]),
  index: z.number(),
  scores: z.record(scoreSummarySchema),
  metrics: z.record(scoreSummarySchema),
});
export type GetSummaryToolResult = z.infer<typeof getSummaryToolResultSchema>;

export const GetSummaryTool: Tool<object, GetSummaryToolResult[]> = {
  description:
    "Get the summary of a playground or experiment, including each task name and its aggregate scores and metrics",
  parameters: z.object({}),
};

export const getResultsToolParams = z.object({
  index: z
    .number()
    .describe(
      "The index of the task or experiment to get results for. You can discover the tasks or experiments and their indices by calling get_summary.",
    ),
  numSamples: z.number().min(1).max(100),
});
export type GetResultsToolParameters = z.infer<typeof getResultsToolParams>;

export const getResultsToolResultSchema = z.object({
  id: z.string(),
  input: z.unknown(),
  output: z.unknown().optional(),
  expected: z.unknown().optional(),
  metadata: z.unknown().optional(),
  scores: z.record(z.number()),
  metrics: z.record(z.number()),
});
export type GetResultsToolResult = z.infer<typeof getResultsToolResultSchema>;

export const GetResultsTool: Tool<
  z.infer<typeof getResultsToolParams>,
  GetResultsToolResult[]
> = {
  description:
    "Get the results of a task, an experiment or dataset, including input, expected output, metadata, and scores",
  parameters: getResultsToolParams,
};

export const rerunTaskToolParams = z.object({
  index: z.number().describe("The index of the task to rerun"),
});
export type RerunTaskToolParameters = z.infer<typeof rerunTaskToolParams>;

export const rerunTaskToolResultSchema = z.object({
  summary: getSummaryToolResultSchema.omit({ definition: true }),
});

export type RerunTaskToolResult = z.infer<typeof rerunTaskToolResultSchema>;

export const RerunTaskTool: Tool<
  z.infer<typeof rerunTaskToolParams>,
  RerunTaskToolResult
> = {
  description: "Rerun a task",
  parameters: rerunTaskToolParams,
};

export function isInternalMetric(key: string) {
  return [
    "__bt_internal_cached",
    "start",
    "end",
    "duration",
    "llm_duration",
  ].includes(key);
}

export function roundNumber(num: number | undefined | null) {
  if (num === undefined || num === null) {
    return undefined;
  }
  return Math.round(num * 1000) / 1000;
}
