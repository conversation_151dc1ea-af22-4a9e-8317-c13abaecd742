import { z } from "zod";
import { type Tool } from "./types";

export const editDataEditSchema = z.object({
  purpose: z
    .string()
    .optional()
    .describe(
      "A quick summary of what you're trying to do with this edit. Why this row is a useful addition, or why you want to edit or delete it.",
    ),
  id: z
    .string()
    .optional()
    .describe(
      "The id of the data to edit. Leave blank to insert a new row. If provided, the row will be updated.",
    ),
  delete: z
    .boolean()
    .optional()
    .describe(
      "If true, the row will be deleted. If false, the row will be updated. If true, the id field is required.",
    ),
  input: z
    .unknown()
    .describe(
      "The value of the `input` field. This is generally the input to the LLM/agent.",
    ),
  expected: z
    .unknown()
    .describe(
      "The value of the `expected` field. This is generally the expected output of the LLM/agent. Leave blank if not applicable.",
    ),
  metadata: z
    .record(z.unknown())
    .optional()
    .describe(
      "Metadata to include with the data. Keys are strings, values are arbitrary JSON values.",
    ),
});
export const editDataToolParams = z.object({
  edits: z.array(editDataEditSchema).min(0).max(10),
  name: z
    .string()
    .describe("If the dataset doesn't exist, create it with this name."),
});
export type EditDataToolParameters = z.infer<typeof editDataToolParams>;

export const editDataToolResultSchema = z.object({
  ok: z.boolean(),
});
export type EditDataToolResult = z.infer<typeof editDataToolResultSchema>;

export const EditDataTool: Tool<EditDataToolParameters, EditDataToolResult> = {
  description:
    "Add or edit dataset rows. If the id is provided, the row will be updated. If the id is not provided, a new row will be inserted. To delete a row, specify its id and set the `delete` field to true.",
  parameters: editDataToolParams,
};
