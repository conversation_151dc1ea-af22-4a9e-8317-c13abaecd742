# Braintrust Optimization Tools

## Getting started

### Syncing Datasets

The first step to get started with optimization is to sync datasets locally. This allows you to work with Braintrust datasets for your evaluation and optimization workflows.

Run the following command:

```bash
npx tsx optimization/evals/sync-datasets.ts --local-api-key <YOUR-LOCAL-API-KEY> --prod-api-key <YOUR-PROD-API-KEY>
```

Replace `<YOUR-LOCAL-API-KEY>` and `<YOUR-PROD-API-KEY>` with your actual Braintrust API keys.

If you happen to make changes to the dataset locally and want to sync them back to production, you can run the following command:

```bash
npx tsx optimization/evals/sync-datasets.ts --local-api-key <YOUR-LOCAL-API-KEY> --prod-api-key <YOUR-PROD-API-KEY> --direction to-prod
```

## Running the CLI

You can run the CLI to "chat" with an eval and use the optimization tools:

```bash
npx tsx optimization/cli.ts ai-search
```

It will create a copy of the dataset you're using and create experiments as it runs.

## Developing new optimization evals

The `evals` directory contains a number of evals that we can optimize and test (to eval the optimizer itself). These evals are written in
a manner that's similar to an `Eval`, but slightly higher level, so that we can mess with them a bit.

To define an eval, look at the definition of one of them, like `evals/tasks/ai-search.ts`, and copy/paste it to a new file. Feel free to modify it
as you see fit, and when you're ready, add it to the `EVALS` object in `evals/tasks/index.ts`.

While working on these evals, it can be useful to run them! You can do this by running the following command:

```bash
npx tsx optimization/evals/run-eval.ts <YOUR-EVAL-NAME>
```
