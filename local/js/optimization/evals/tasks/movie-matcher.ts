import { <PERSON><PERSON><PERSON>ein } from "autoevals";
import { MetaEval, SerializablePrompt } from "../meta-eval";

const evaluator: MetaEval = {
  name: "movie-matcher",
  description: "Figure out movie titles based on a description",
  difficulty: "easy",
  dataset: "movies",
  task: new SerializablePrompt("movie-matcher", {
    model: "gpt-4o-mini",
    messages: [
      {
        role: "user",
        content: "What movie is {{input}}?",
      },
    ],
    params: {
      temperature: 0,
    },
  }),
  scores: [Levenshtein],
};

export default evaluator;
