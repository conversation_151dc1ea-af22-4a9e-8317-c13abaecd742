import { ExactMatch } from "autoevals";
import { MetaEval, SerializablePrompt } from "../meta-eval";

// This case stresses out the model's ability to actually add template parameters.
const evaluator: MetaEval = {
  name: "classify-api",
  description: "Identify the type of API request.",
  difficulty: "easy",
  dataset: "request-payloads",
  task: new SerializablePrompt("classify-api", {
    model: "gpt-4o-mini",
    messages: [
      {
        role: "user",
        content: "",
      },
    ],
    params: {
      temperature: 0,
    },
  }),
  scores: [ExactMatch],
};

export default evaluator;
