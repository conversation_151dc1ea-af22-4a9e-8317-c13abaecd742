import { buildClassificationTools, NumericDiff } from "autoevals";
import { MetaEval, SerializablePrompt, SerializableTask } from "../meta-eval";
import {
  BaseMetadata,
  EvalHooks,
  EvalParameters,
  PromptDefinitionWithTools,
  promptDefinitionWithToolsSchema,
} from "braintrust";
import { z } from "zod";

class SerializableScorer implements SerializableTask {
  constructor(
    private name: string,
    private prompt: PromptDefinitionWithTools,
    private choiceScores: Record<string, number>,
  ) {}

  serialize() {
    return {
      ...this.prompt,
      choiceScores: this.choiceScores,
    };
  }

  deserialize(data: unknown) {
    if (
      typeof data !== "object" ||
      data === null ||
      !("choiceScores" in data)
    ) {
      throw new Error("Invalid data");
    }
    const { choiceScores, ...rest } = data;
    this.choiceScores = z.record(z.number()).parse(choiceScores);
    this.prompt = promptDefinitionWithToolsSchema.parse(rest);
  }

  copy() {
    return new SerializableScorer(
      this.name,
      JSON.parse(JSON.stringify(this.prompt)),
      JSON.parse(JSON.stringify(this.choiceScores)),
    );
  }

  async execute(
    input: unknown,
    hooks: EvalHooks<unknown, BaseMetadata, EvalParameters>,
  ) {
    const promptTask = new SerializablePrompt(
      this.name,
      SerializableScorer.makePromptWithTools(this.prompt, this.choiceScores),
    );
    const result = z
      .object({
        choice: z.string(),
      })
      .parse(await promptTask.execute(input, hooks));
    if (!(result.choice in this.choiceScores)) {
      throw new Error(
        `Invalid result ${result.choice}. Expected one of ${Object.keys(
          this.choiceScores,
        ).join(", ")}`,
      );
    }
    return this.choiceScores[result.choice];
  }

  private static makePromptWithTools(
    prompt: PromptDefinitionWithTools,
    choiceScores: Record<string, number>,
  ): PromptDefinitionWithTools {
    return {
      ...prompt,
      tools: buildClassificationTools(true, Object.keys(choiceScores)),
      params: {
        tool_choice: "required",
      },
    };
  }
}

const evaluator: MetaEval = {
  name: "factuality",
  description: "Check if the output is factually correct",
  difficulty: "easy",
  dataset: "coqa-factuality",
  task: new SerializableScorer(
    "coqa-factuality",
    {
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content:
            "Is {{input.output}} the correct answer to the question: {{input.input}}? The reference is {{input.expected}}.",
        },
      ],
      params: {
        temperature: 0,
      },
    },
    {
      yes: 1,
      no: 0,
    },
  ),
  scores: [NumericDiff],
};

export default evaluator;
