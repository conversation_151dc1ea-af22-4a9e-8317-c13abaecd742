import { <PERSON><PERSON><PERSON><PERSON>, LLMClassifierFromTemplate } from "autoevals";
import { MetaEval, SerializablePrompt } from "../meta-eval";
import { aiSearchTools } from "@lib/ai-search";

const scorerEval = LLMClassifierFromTemplate({
  name: "BTQL query checker",
  promptTemplate: `For the following input:
{{{input}}}

The correct answer is:
{{expected}}

The generated output is:
{{output}}

Compare each of them, and categorize them as the following:

(a) Both the results (use of match, query, etc) and explanations match.
(b) The results match, but the generated explanation is unrelated to the result.
(c) The results match, but the generated explanation is incorrect.
(d) The results almost match, but they are not the same semantically.
(e) The results do not match, and the generated explanation is incorrect.
    `,
  choiceScores: {
    a: 1,
    b: 0.75,
    c: 0.5,
    d: 0.25,
    e: 0,
  },
});

async function querySimilarity({
  output,
  expected,
}: {
  output: unknown;
  expected?: unknown;
}) {
  const args = {
    output: normalizeFunctionParams(output),
    expected: expected ? normalizeFunctionParams(expected) : "",
  };
  const stringSimilarity = await <PERSON><PERSON><PERSON><PERSON>(args);
  return {
    name: "QuerySimilarity",
    score: stringSimilarity.score,
    metadata: args,
  };
}

function normalizeFunctionParams(params: unknown) {
  if (typeof params !== "object" || !params) {
    return JSON.stringify(params);
  }
  const { explanation: _, ...rest } = Object.fromEntries(
    Object.entries(params).filter(
      ([key, value]) => value !== undefined && value !== null,
    ),
  );
  return JSON.stringify(rest, Object.keys(rest).sort());
}

const evaluator: MetaEval = {
  name: "ai-search",
  description: "Evaluate the performance of an AI search engine",
  difficulty: "medium",
  dataset: "ai-search-examples",
  task: new SerializablePrompt("ai-search", {
    model: "gpt-4o-mini",
    messages: [
      {
        role: "user",
        content: "Write a SQL-style query for {{input}}",
      },
    ],
    tools: aiSearchTools({ includeTags: true }),
    params: {
      temperature: 0,
    },
  }),
  scores: [querySimilarity, scorerEval],
};

export default evaluator;
