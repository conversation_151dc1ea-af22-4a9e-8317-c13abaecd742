import {
  <PERSON>l,
  NOOP_SPAN,
  Span,
  _internalGetGlobalState,
  currentSpan,
  initDataset,
  initExperiment,
} from "braintrust";
import { makeEval<PERSON>hat, SpanChatLogger, MODEL } from "./eval-vars";
import { PROJECT_NAME } from "./meta-eval";
import {
  RunBtqlToolParameters,
  RunBtqlToolResult,
  InferSchemaToolParameters,
} from "../tools";
import { parseQuery } from "@braintrust/btql/parser";
import { bindQuery } from "@braintrust/btql/binder";
import { createSyntheticProjectLogs } from "./utils/project-logs";
import { BTQL_SANDBOX_SCORERS, ScoredOutput } from "./utils/scorers";
import {
  createPromptWithContext,
  createBTQLContext,
  type ContextObject,
} from "./utils/context-objects";
import { BRAINTRUST_LOGICAL_SCHEMA } from "@lib/api-schema";
import { validate as validateUuid } from "uuid";

function containsValidUuid(input: string): boolean {
  // Split on spaces and test each word to see if it's a valid UUID
  const words = input.split(/\s+/);
  return words.some((word) => {
    // Strip common delimiters from the beginning and end of the word
    const stripped = word.replace(
      /^['"`,;:()[\]{}<>]+|['"`,;:()[\]{}<>]+$/g,
      "",
    );
    return validateUuid(stripped);
  });
}

function getContextQuery(objectType: string, objectId: string): ContextObject {
  let queryFragment: string;
  if (objectType === "project_logs") {
    queryFragment =
      "dimensions: metadata.user_id | measures: count(1) as request_count | sort: request_count desc";
  } else if (objectType === "dataset") {
    queryFragment =
      "filter: metadata.difficulty = 'hard' | select: input, expected | limit: 5";
  } else if (objectType === "experiment") {
    queryFragment =
      "select: metrics.duration, metrics.tokens | filter: metrics.duration IS NOT NULL | sort: metrics.duration desc | limit: 10";
  } else {
    queryFragment = "select: * | limit: 5";
  }

  const fullQuery = `from: ${objectType}('${objectId}') | ${queryFragment}`;
  return createBTQLContext(fullQuery, `${objectType} analysis`, {
    hasResults: true,
    resultCount: Math.floor(Math.random() * 15) + 2,
  });
}

function wrapRunBTQLTool(
  handler: (args: RunBtqlToolParameters) => Promise<RunBtqlToolResult>,
  objectType: string,
  objectId: string,
  input: string,
  hasContextualQuery: boolean,
  toolOutputs: unknown[],
) {
  return async (args: RunBtqlToolParameters): Promise<RunBtqlToolResult> => {
    const inputContainsThis = input.includes("this");
    const shouldUseSandboxQuerySource = hasContextualQuery && inputContainsThis;

    const priorToolHasDataSource = toolOutputs.some(
      (output) =>
        typeof output === "object" &&
        output !== null &&
        "dataSource" in output &&
        typeof output.dataSource === "object" &&
        output.dataSource !== null &&
        "entity" in output.dataSource &&
        "id" in output.dataSource,
    );

    const inputContainsUuid = containsValidUuid(input);

    const thisToolHasDataSource = args.dataSource !== undefined;
    const isCorrectUsage =
      shouldUseSandboxQuerySource || priorToolHasDataSource || inputContainsUuid
        ? thisToolHasDataSource
        : !thisToolHasDataSource;

    currentSpan().log({
      scores: {
        datasourceArgumentUsage: isCorrectUsage ? 1 : 0,
        runBtqlCallTotal: 1,
      },
      metadata: {
        shouldUseSandboxQuerySource,
        priorToolHasDataSource,
        inputContainsUuid,
        thisToolHasDataSource,
        hasContextualQuery,
        entityType: objectType,
        inputContainsThis,
      },
    });

    // Use the provided object for the data source (simulates user selecting a data source in UI)
    const dataSource = args.dataSource?.id
      ? {
          entity: args.dataSource.entity,
          id: args.dataSource.id!,
        }
      : {
          entity: objectType,
          id: objectId,
        };

    const fullQuery = `from: ${dataSource.entity}('${dataSource.id}') ${args.shape ?? ""} | ${args.query}`;

    let parsed;
    try {
      parsed = parseQuery(fullQuery);
      currentSpan().log({
        scores: { parses: 1 },
      });
    } catch (e) {
      currentSpan().log({
        scores: { parses: 0 },
      });
      throw e;
    }

    let bound;
    try {
      bound = bindQuery({ query: parsed, schema: BRAINTRUST_LOGICAL_SCHEMA });
      currentSpan().log({
        scores: { binds: 1 },
      });
    } catch (e) {
      currentSpan().log({
        scores: { binds: 0 },
      });
      throw e;
    }

    if ("select" in bound && bound.select.length > 0) {
      // A select+sort is bad because it can be very slow
      currentSpan().log({
        // @ts-ignore
        scores: { noSlowSort: (bound.sort?.length ?? 0) === 0 ? 1 : 0 },
      });
    }

    try {
      // @ts-ignore
      const result = await handler({ ...args, dataSource });
      currentSpan().log({
        scores: { btqlSucceeds: 1 },
      });

      toolOutputs.push(result);

      return result;
    } catch (e) {
      currentSpan().log({
        scores: { btqlSucceeds: 0 },
      });
      throw e;
    }
  };
}

function wrapInferSchemaTool(
  handler: (args: InferSchemaToolParameters) => Promise<unknown>,
  objectType: string,
  objectId: string,
  input: string,
  hasContextualQuery: boolean,
  toolOutputs: unknown[],
) {
  return async (args: InferSchemaToolParameters): Promise<unknown> => {
    const inputContainsThis = input.includes("this");
    const shouldUseSandboxQuerySource = hasContextualQuery && inputContainsThis;

    const priorToolHasDataSource = toolOutputs.some(
      (output) =>
        typeof output === "object" &&
        output !== null &&
        "dataSource" in output &&
        typeof output.dataSource === "object" &&
        output.dataSource !== null &&
        "entity" in output.dataSource &&
        "id" in output.dataSource,
    );

    const inputContainsUuid = containsValidUuid(input);

    const hasDataSource = args.dataSource !== undefined;
    const isCorrectUsage =
      shouldUseSandboxQuerySource || priorToolHasDataSource || inputContainsUuid
        ? hasDataSource
        : !hasDataSource;

    currentSpan().log({
      scores: {
        datasourceArgumentUsage: isCorrectUsage ? 1 : 0,
      },
      metadata: {
        shouldUseSandboxQuerySource,
        priorToolHasDataSource,
        inputContainsUuid,
        hasDataSource,
        hasContextualQuery,
        entityType: objectType,
        inputContainsThis,
      },
    });

    // Use the provided object for the data source (simulates user selecting a data source in UI)
    const dataSource = args.dataSource?.id
      ? {
          entity: args.dataSource.entity,
          id: args.dataSource.id!,
        }
      : {
          entity: objectType,
          id: objectId,
        };

    try {
      // @ts-ignore
      const result = await handler({ ...args, dataSource });
      currentSpan().log({
        scores: { inferSchemaSucceeds: 1 },
      });

      toolOutputs.push(result);

      return result;
    } catch (e) {
      currentSpan().log({
        scores: { inferSchemaSucceeds: 0 },
      });
      throw e;
    }
  };
}

Eval(PROJECT_NAME, {
  data: async () => {
    const testObjects = [];

    const projectName = `${PROJECT_NAME} eval logs`;
    const projectLogId = "845ba1e0-f164-4f80-a29a-f84b2f2fd822";

    await createSyntheticProjectLogs(projectLogId, {
      numEntries: 50,
      includeErrors: true,
      includeUserActivity: true,
      includeModelVariety: true,
    });

    testObjects.push({
      objectType: "project_logs",
      objectId: projectLogId,
      testCases: [
        {
          input: "Write a query to get the most recent logs from project_logs",
          expectedQuery: "select: * | limit: 10",
          metadata: {
            scenario: "project_logs_generic",
            difficulty: "easy",
          },
        },
        {
          input: `Write a query to get error logs from project '${projectLogId}'`,
          expectedQuery: "filter: error IS NOT NULL | select: * | limit: 10",
          metadata: {
            scenario: "project_logs_specific_id",
            difficulty: "medium",
          },
        },
        {
          input: `Write a query to count requests by user from '${projectName}'`,
          expectedQuery:
            "dimensions: metadata.user_id | measures: count(1) as request_count | sort: request_count desc",
          metadata: {
            scenario: "project_logs_named_project",
            difficulty: "hard",
          },
        },
        {
          input: "Write a query to get the slowest requests from this project",
          expectedQuery:
            "filter: metrics.duration IS NOT NULL | select: *, metrics.duration | sort: metrics.duration desc | limit: 10",
          metadata: {
            scenario: "project_logs_contextual",
            difficulty: "medium",
          },
        },
      ],
    });

    const dataset = initDataset({
      project: `${PROJECT_NAME} eval logs`,
      dataset: "test-dataset",
    });
    for (let i = 0; i < 20; i++) {
      dataset.insert({
        id: `dataset-row-${i}`,
        input: `Sample question ${i}`,
        expected: `Expected answer ${i}`,
        metadata: {
          category:
            i % 3 === 0 ? "math" : i % 3 === 1 ? "science" : "literature",
          difficulty: i % 4 === 0 ? "easy" : i % 4 === 1 ? "medium" : "hard",
        },
      });
    }
    await dataset.flush();
    const datasetId = await dataset.id;

    testObjects.push({
      objectType: "dataset",
      objectId: datasetId,
      testCases: [
        {
          input:
            "Write a query to get the most recently added rows from a dataset",
          expectedQuery: "select: * | limit: 10",
          metadata: {
            scenario: "dataset_generic",
            difficulty: "easy",
          },
        },
        {
          input: `Write a query to count items by category from dataset '${datasetId}'`,
          expectedQuery:
            "dimensions: metadata.category | measures: count(1) as count | sort: count desc",
          metadata: {
            scenario: "dataset_specific_id",
            difficulty: "medium",
          },
        },
        {
          input: `Write a query to get math questions from 'test-dataset'`,
          expectedQuery:
            "filter: metadata.category = 'math' | select: * | limit: 10",
          metadata: {
            scenario: "dataset_named",
            difficulty: "hard",
          },
        },
        {
          input: "How many items are in this dataset?",
          expectedQuery: "measures: count(1) as total_items",
          metadata: {
            scenario: "dataset_contextual",
            difficulty: "easy",
          },
        },
      ],
    });

    // 3. Create an experiment
    const experiment = initExperiment(projectName, {
      experiment: "test-experiment",
    });

    // Add some sample experiment results
    for (let i = 0; i < 15; i++) {
      const span = experiment.startSpan({
        name: "evaluation",
        event: {
          input: `Test input ${i}`,
          output: `Test output ${i}`,
          expected: `Expected ${i}`,
          scores: {
            accuracy: Math.random(),
            relevance: Math.random(),
          },
          metrics: {
            duration: Math.random() * 1000,
            tokens: Math.floor(Math.random() * 500) + 100,
          },
        },
      });
      span.end();
    }
    await experiment.flush();
    const experimentId = await experiment.id;

    testObjects.push({
      objectType: "experiment",
      objectId: experimentId,
      testCases: [
        {
          input:
            "Write a query to get the lowest relevance scoring rows from an experiment",
          expectedQuery:
            "select: *, scores.relevance | sort: scores.relevance | limit: 10",
          metadata: {
            scenario: "experiment_generic",
            difficulty: "medium",
          },
        },
        {
          input: `Write a query to get results with high accuracy from experiment ${experimentId}`,
          expectedQuery:
            "filter: scores.accuracy > 0.8 | select: * | limit: 10",
          metadata: {
            scenario: "experiment_specific_id",
            difficulty: "medium",
          },
        },
        {
          input:
            "Write a query to get average token usage from 'test-experiment'",
          expectedQuery: "measures: avg(metrics.tokens) as avg_tokens",
          metadata: {
            scenario: "experiment_named",
            difficulty: "hard",
          },
        },
        {
          input: "What's the average accuracy score in this experiment?",
          expectedQuery: "measures: avg(scores.accuracy) as avg_accuracy",
          metadata: {
            scenario: "experiment_contextual",
            difficulty: "medium",
          },
        },
      ],
    });

    const allTestCases = [];
    for (const testObj of testObjects) {
      for (const testCase of testObj.testCases) {
        try {
          const query = `from: ${testObj.objectType}('${testObj.objectId}') | ${testCase.expectedQuery}`;
          const result = await _internalGetGlobalState()
            .apiConn()
            .post("/btql", {
              query,
            });
          const data = await result.json();

          let btqlContext: ContextObject;

          if (testCase.metadata.scenario.includes("contextual")) {
            // For contextual scenarios: add relevant query with same dataSource but dissimilar intent
            btqlContext = getContextQuery(testObj.objectType, testObj.objectId);
          } else {
            // For non-contextual scenarios: add unrelated query context -- we're testing whether the model can ignore the context if it's not relevant
            const unrelatedObjects = testObjects.filter(
              (obj) => obj.objectType !== testObj.objectType,
            );
            const randomUnrelated =
              unrelatedObjects[
                Math.floor(Math.random() * unrelatedObjects.length)
              ];

            btqlContext = getContextQuery(
              randomUnrelated.objectType,
              randomUnrelated.objectId,
            );
          }

          allTestCases.push({
            input: testCase.input,
            expected: `This is the raw data that answers the query: ${JSON.stringify(data.data, null, 2)}`,
            metadata: {
              ...testCase.metadata,
              query: testCase.expectedQuery,
              objectType: testObj.objectType,
              objectId: testObj.objectId,
              btqlContext,
            },
          });
        } catch (error) {
          console.warn(`Failed to pre-run query for ${testCase.input}:`, error);
        }
      }
    }

    return allTestCases;
  },
  task: async (input, hooks): Promise<ScoredOutput> => {
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;

    try {
      const toolOutputs: unknown[] = [];

      const {
        chat,
        consoleLogger: cl,
        rawOutput: ro,
      } = await makeEvalChat(null, {
        // Use BTQL sandbox page context - only has infer_schema, run_btql, continue_execution
        applicationContext: "btql",
        queryObject: {
          objectType: hooks.metadata.objectType,
          objectId: hooks.metadata.objectId,
        },
        wrapTools: (tools) => {
          tools.updateImplementations({
            run_btql: wrapRunBTQLTool(
              // @ts-ignore
              tools.tools.run_btql,
              hooks.metadata.objectType,
              hooks.metadata.objectId,
              input,
              !!hooks.metadata.btqlContext,
              toolOutputs,
            ),
            // @ts-ignore
            infer_schema: wrapInferSchemaTool(
              // @ts-ignore
              tools.tools.infer_schema,
              hooks.metadata.objectType,
              hooks.metadata.objectId,
              input,
              !!hooks.metadata.btqlContext,
              toolOutputs,
            ),
          });
        },
      });
      consoleLogger = cl;
      rawOutput = ro;

      const contextObjects: Record<string, ContextObject> = {
        [hooks.metadata.btqlContext.id]: hooks.metadata.btqlContext,
      };
      const prompt = createPromptWithContext(input, contextObjects);

      const result = await chat.turn(prompt);

      return {
        result: result.text,
        metrics: chat.metrics,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: BTQL_SANDBOX_SCORERS,
  experimentName: `Loop BTQL Sandbox - ${MODEL}`,
  metadata: {
    model: MODEL,
    description: "Tests loop in BTQL sandbox page context",
    page_context: "btql",
  },
});
