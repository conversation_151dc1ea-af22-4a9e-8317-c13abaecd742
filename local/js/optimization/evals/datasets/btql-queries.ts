export interface BTQLTestCase {
  input: string;
  expectedQuery: string;
  shape?: string;
  metadata: {
    scenario: string;
    difficulty: "easy" | "medium" | "hard";
    description?: string;
  };
}

export const btqlQueriesDataset: BTQLTestCase[] = [
  /*
  {
    input: "What are some common question types in my logs?",
    expectedQuery:
      "dimensions: input | measures: count(1) as count | sort: count desc | limit: 10",
    metadata: {
      scenario: "common_question_types",
      difficulty: "medium",
      description: "Should group by input field and show most common questions",
    },
  },
  {
    input: "What is causing the most errors?",
    expectedQuery:
      "filter: error IS NOT NULL | dimensions: error | measures: count(1) as count | sort: count desc",
    metadata: {
      scenario: "causing_errors",
      difficulty: "medium",
      description: "Should filter for errors and group by error type",
    },
  },
  {
    input: "Find everything <NAME_EMAIL>",
    expectedQuery: "filter: metadata.user_id = '<EMAIL>' | select: *",
    metadata: {
      scenario: "find_user",
      difficulty: "hard",
      description: "Should filter by metadata.user_id field",
    },
  },
  {
    input: "Which user is the most active?",
    expectedQuery:
      "dimensions: metadata.user_id | measures: count(1) as activity | sort: activity desc | limit: 1",
    metadata: {
      scenario: "most_active_user",
      difficulty: "hard",
      description: "Should group by metadata.user_id and count activity",
    },
  },
  {
    input: "Which model gets used the most?",
    expectedQuery:
      "dimensions: metadata.model | measures: count(1) as usage | sort: usage desc | limit: 1",
    metadata: {
      scenario: "most_used_model",
      difficulty: "medium",
      description: "Should group by metadata.model and count usage",
    },
  },
  {
    input: "Find me examples of users struggling",
    expectedQuery:
      "filter: input MATCH 'struggling' | select: input, metadata.user_id | limit: 5",
    metadata: {
      scenario: "users_struggling",
      difficulty: "medium",
      description: "Should use MATCH for semantic search",
    },
  },
  // Additional questions inspired by common log analysis needs
  {
    input: "Show me the error rate over time",
    expectedQuery:
      "dimensions: hour(created) as hour | measures: count(error) as errors, count(1) as total | sort: hour",
    metadata: {
      scenario: "error_rate",
      difficulty: "medium",
      description: "Should calculate error rate grouped by time",
    },
  },
  {
    input: "What's the average response time for each model?",
    expectedQuery:
      "dimensions: metadata.model | measures: avg(metrics.duration) as avg_duration | sort: avg_duration",
    metadata: {
      scenario: "response_times",
      difficulty: "hard",
      description: "Should calculate average duration by model",
    },
  },
  {
    input: "Which questions take the longest to answer?",
    expectedQuery:
      "dimensions: input | measures: avg(metrics.duration) as avg_duration | sort: avg_duration desc | limit: 10",
    metadata: {
      scenario: "slow_queries",
      difficulty: "hard",
      description: "Should find questions with highest average duration",
    },
  },
  {
    input: "Show me all authentication-related queries",
    expectedQuery:
      "filter: input MATCH 'authentication' | select: input, metadata.user_id, created",
    metadata: {
      scenario: "topic_search",
      difficulty: "easy",
      description: "Should search for authentication-related content",
    },
  },
  {
    input: "List all unique session IDs",
    expectedQuery:
      "dimensions: metadata.session_id | measures: count(1) as count | sort: metadata.session_id",
    metadata: {
      scenario: "unique_sessions",
      difficulty: "easy",
      description: "Should list unique session IDs with counts",
    },
  },
  {
    input: "How many follow-up questions does each user ask?",
    expectedQuery:
      "filter: input MATCH 'follow-up' | dimensions: metadata.user_id | measures: count(1) as follow_ups | sort: follow_ups desc",
    metadata: {
      scenario: "follow_up_analysis",
      difficulty: "medium",
      description: "Should count follow-up questions by user",
    },
  },
  {
    input: "Find all queries about performance optimization",
    expectedQuery:
      "filter: input MATCH 'optimize performance' OR input MATCH 'slow' | select: input, metadata.user_id",
    metadata: {
      scenario: "performance_queries",
      difficulty: "easy",
      description: "Should find performance-related queries",
    },
  },
  {
    input: "What percentage of queries result in errors?",
    expectedQuery: "measures: count(error) as errors, count(1) as total",
    metadata: {
      scenario: "error_percentage",
      difficulty: "medium",
      description:
        "Should calculate error count and total count for percentage",
    },
  },
  {
    input: "Show me the distribution of queries by hour of day",
    expectedQuery:
      "dimensions: hour(created) as hour_of_day | measures: count(1) as query_count | sort: hour_of_day",
    metadata: {
      scenario: "time_distribution",
      difficulty: "hard",
      description: "Should group queries by hour of day",
    },
  },
  // Test cases for "most recent" queries - these should NOT include sort clauses
  // because results are automatically sorted by timestamp descending
  {
    input: "Find the most recent error in the logs",
    expectedQuery: "filter: error IS NOT NULL | select: * | limit: 1",
    metadata: {
      scenario: "most_recent_error",
      difficulty: "easy",
      description: "Should find most recent error without sort clause",
    },
  },
  {
    input: "Show me the latest 5 requests that failed",
    expectedQuery: "filter: error IS NOT NULL | select: * | limit: 5",
    metadata: {
      scenario: "latest_failed_requests",
      difficulty: "easy",
      description: "Should find latest failures without sort clause",
    },
  },
  {
    input: "Get the most recent LLM calls",
    expectedQuery:
      "filter: span_attributes.type = 'llm' | select: * | limit: 10",
    metadata: {
      scenario: "recent_llm_calls",
      difficulty: "easy",
      description: "Should find recent LLM calls without sort clause",
    },
  },
  {
    input: "Find the newest traces with high latency (over 5 seconds)",
    expectedQuery:
      "filter: metrics.end - metrics.start > 5 | select: * | limit: 10",
    metadata: {
      scenario: "recent_high_latency",
      difficulty: "medium",
      description: "Should find recent high latency traces without sort clause",
    },
  },
  {
    input: "Show the last 10 tool calls",
    expectedQuery:
      "filter: span_attributes.type = 'tool' | select: * | limit: 10",
    metadata: {
      scenario: "last_tool_calls",
      difficulty: "easy",
      description: "Should find last tool calls without sort clause",
    },
  },
  {
    input: "Find the most recent requests with model gpt-4",
    expectedQuery: "filter: metadata.model = 'gpt-4' | select: * | limit: 10",
    metadata: {
      scenario: "recent_gpt4_requests",
      difficulty: "easy",
      description: "Should find recent GPT-4 requests without sort clause",
    },
  },
  {
    input: "Get the latest traces with completion tokens over 1000",
    expectedQuery:
      "filter: metrics.completion_tokens > 1000 | select: * | limit: 10",
    metadata: {
      scenario: "recent_high_tokens",
      difficulty: "medium",
      description: "Should find recent high token usage without sort clause",
    },
  },
  {
    input: "Show me the newest 20 spans",
    expectedQuery: "select: * | limit: 20",
    metadata: {
      scenario: "newest_spans",
      difficulty: "easy",
      description: "Should get newest spans without sort clause",
    },
  },
  {
    input: "Find the most recent successful requests",
    expectedQuery: "filter: error IS NULL | select: * | limit: 10",
    metadata: {
      scenario: "recent_successful",
      difficulty: "easy",
      description: "Should find recent successful requests without sort clause",
    },
  },
  {
    input: "Get the latest user <NAME_EMAIL>",
    expectedQuery:
      "filter: metadata.user_id = '<EMAIL>' | select: * | limit: 10",
    metadata: {
      scenario: "recent_user_activity",
      difficulty: "medium",
      description:
        "Should find recent activity for specific user without sort clause",
    },
  },
  */
  //Test cases for limit. Limit should not exceed 100.
  {
    input: "Find the 105 most recent logs",
    expectedQuery: "select: * | limit: 100",
    metadata: {
      scenario: "limit_105",
      difficulty: "easy",
    },
  },
  {
    input: "Find the 200 most recent logs",
    expectedQuery: "select: * | limit: 100",
    metadata: {
      scenario: "limit_200",
      difficulty: "easy",
    },
  },
  {
    input: "Look at all 500 of my logs",
    expectedQuery: "select: * | limit: 100",
    metadata: {
      scenario: "limit_500",
      difficulty: "medium",
    },
  },
  /*  // Test cases for tags field (tags are arrays of strings)
  {
    input: "What is the most popular tag?",
    expectedQuery:
      "unpivot: tags as tag | dimensions: tag | measures: count(1) as usage_count | sort: usage_count desc | limit: 1",
    metadata: {
      scenario: "most_popular_tag",
      difficulty: "hard",
      description: "Should find most frequently used tag using unpivot",
    },
  },

  {
    input: "Show me all requests tagged as production",
    expectedQuery: "filter: tags CONTAINS 'production' | select: *",
    metadata: {
      scenario: "filter_by_tag",
      difficulty: "easy",
      description: "Should filter by specific tag",
    },
  },
  {
    input: "Find requests with tag environment:staging",
    expectedQuery: "filter: tags CONTAINS 'environment:staging' | select: *",
    metadata: {
      scenario: "filter_by_tag_value",
      difficulty: "medium",
      description: "Should filter by tag with specific value",
    },
  },
  {
    input: "Count requests by environment tag",
    expectedQuery:
      "unpivot: tags as tag | dimensions: tag | measures: count(1) as count | sort: count desc",
    metadata: {
      scenario: "count_by_tag",
      difficulty: "medium",
      description:
        "Should group and count by all tags (filtering happens in post-processing)",
    },
  },
  {
    input: "Show me the distribution of all tags",
    expectedQuery:
      "unpivot: tags as tag | dimensions: tag | measures: count(1) as tag_count | sort: tag_count desc | limit: 20",
    metadata: {
      scenario: "tag_distribution",
      difficulty: "hard",
      description: "Should show distribution of all tags using unpivot",
    },
  },
  {
    input: "Find requests tagged with both production and high_priority",
    expectedQuery:
      "filter: tags CONTAINS 'production' AND tags CONTAINS 'high_priority' | select: *",
    metadata: {
      scenario: "multiple_tags_filter",
      difficulty: "medium",
      description: "Should filter by multiple tags",
    },
  },
  {
    input: "Which user creates the most production-tagged requests?",
    expectedQuery:
      "filter: tags CONTAINS 'production' | dimensions: metadata.user_id | measures: count(1) as prod_requests | sort: prod_requests desc | limit: 1",
    metadata: {
      scenario: "user_tag_analysis",
      difficulty: "hard",
      description: "Should analyze user activity filtered by tag",
    },
  },
  {
    input: "Show tag values for the environment tag",
    expectedQuery:
      "unpivot: tags as tag | dimensions: tag | measures: count(1) as count | sort: count desc",
    metadata: {
      scenario: "tag_values_distribution",
      difficulty: "medium",
      description:
        "Should show distribution of all tag values (filtering for environment tags happens in post-processing)",
    },
  },
  {
    input: "Find the most recent requests with any tags",
    expectedQuery:
      "filter: tags IS NOT NULL AND len(tags) > 0 | select: * | limit: 10",
    metadata: {
      scenario: "recent_tagged_requests",
      difficulty: "easy",
      description: "Should find recent requests that have any tags",
    },
  },
  {
    input: "What percentage of requests are tagged as experimental?",
    expectedQuery:
      "measures: count(tags CONTAINS 'experimental' ? 1 : null) as experimental_count, count(1) as total_count",
    metadata: {
      scenario: "tag_percentage",
      difficulty: "medium",
      description:
        "Should calculate percentage of requests with specific tag using ternary operator",
    },
  }, */
];
