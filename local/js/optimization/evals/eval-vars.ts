import { minimatch } from "minimatch";
import {
  buildSystemPrompt,
  DEFAULT_MODEL,
  PageKey,
} from "../llm/system-prompt";
import { EVALS } from "./tasks";
import {
  _internalGetGlobalState,
  currentSpan,
  Experiment,
  login,
  Span,
} from "braintrust";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>og<PERSON> } from "../llm/chat";
import { makeTools } from "../cli-provider";
import { PROJECT_NAME } from "./meta-eval";
import { SpanComponentsV3 } from "braintrust/util";
import { ToolManager, ToolName } from "../tools";
import { getAvailableModels } from "@braintrust/proxy/schema";

export const MODEL = process.env.MODEL ?? DEFAULT_MODEL;
export const TASK_FILTER = process.env.TASK_FILTER ?? "*";
export const DEFAULT_TRIAL_COUNT = 5;
export const TRIAL_COUNT = process.env.TRIAL_COUNT
  ? parseInt(process.env.TRIAL_COUNT)
  : DEFAULT_TRIAL_COUNT;

export function makeDataset() {
  return Object.keys(EVALS)
    .filter((name) => minimatch(name, TASK_FILTER))
    .map((name) => ({
      input: name,
      metadata: {
        description: EVALS[name].description,
        difficulty: EVALS[name].difficulty,
      },
    }));
}

export async function makeEvalChat(
  input: string | null,
  opts?: {
    allowed_tools?: ToolName[];
    maxRoundtrips?: number;
    applicationContext?: PageKey;
    preLoadedExperiment?: Experiment;
    queryObject?: { objectType: string; objectId: string };
    wrapTools?: (tools: ToolManager) => void;
  },
) {
  opts = opts ?? {};
  await login();
  const rawOutput = currentSpan().startSpan({
    name: "raw-output",
  });

  const defaultSystemPrompt = buildSystemPrompt(
    opts.applicationContext ?? "playground",
  );

  const consoleLogger = new SpanChatLogger(rawOutput);
  const hasTask = input !== null && EVALS[input] !== undefined;
  const task = hasTask
    ? { ...EVALS[input], scores: [...EVALS[input].scores] }
    : undefined;
  const tools = await makeTools({
    def: task,
    projectName: PROJECT_NAME,
    logProjectName: PROJECT_NAME + " eval logs",
    spinner: undefined,
    printExperimentResult: (result) => {
      consoleLogger.write("\n" + result + "\n");
    },
    deterministic: true, // This lets us avoid randomizing the order of results
    model: MODEL,
    applicationContext: opts.applicationContext,
    preLoadedExperiment: opts.preLoadedExperiment,
    queryObjectType: opts.queryObject?.objectType,
    queryObjectId: opts.queryObject?.objectId,
  });
  opts.wrapTools?.(tools);

  // Create a new chat context for each turn with a custom logger
  const chat = new ChatContext({
    loggingState: _internalGetGlobalState(),
    orgName: undefined,
    tools,
    loggingRoot: SpanComponentsV3.fromStr(await currentSpan().export()),
    loggingMetadata: {
      user_id: undefined,
      email: undefined,
      project_id: undefined,
      org_id: undefined,
    },
    enableLogging: true,
    consoleLogger,
    model: MODEL,
    defaultSystemPrompt,
    cacheMode: "never",
    modelParams: {
      temperature: 0, // to enable caching
      max_tokens: getAvailableModels()[MODEL].max_output_tokens,
    },
    allowed_tools: opts.allowed_tools,
    maxRoundtrips: opts.maxRoundtrips,
  });
  return {
    chat,
    tools,
    task,
    rawOutput,
    consoleLogger,
  };
}

export class SpanChatLogger implements ChatLogger {
  private text = "";
  constructor(private readonly span: Span) {}

  write(message: string): void {
    this.text += message;
  }

  async flush(): Promise<void> {
    this.span.log({ output: this.text });
  }
}
