import { Score } from "autoevals";
import { ChatMetrics } from "../llm/chat";
import { ScoreSummary } from "../tools";
import { isEmpty } from "braintrust/util";

export interface OptimizationResult {
  baseline: Record<string, ScoreSummary>;
  updated: Record<string, ScoreSummary>;
  metrics: ChatMetrics;
}

export function scoreImprovement({
  output,
}: {
  output: OptimizationResult;
}): Score[] {
  // Compute the average improvement across scores
  const allKeys = new Set<string>();
  for (const key of Object.keys(output.baseline)) {
    allKeys.add(key);
  }
  for (const key of Object.keys(output.updated)) {
    allKeys.add(key);
  }

  const numBaselineScores = Object.keys(output.baseline).length;
  let totalImprovement = 0;
  for (const key of allKeys) {
    const baselineScore = output.baseline[key];
    const updatedScore = output.updated[key];
    if (isEmpty(baselineScore) || isEmpty(updatedScore)) {
      continue;
    }
    const improvement = (updatedScore.avg ?? 0) - (baselineScore.avg ?? 0);
    totalImprovement += improvement;
  }

  const avgScoreImprovement =
    numBaselineScores > 0 ? totalImprovement / numBaselineScores : 0;

  const avgImprovementPerEdit =
    (output.metrics.toolCallsByType.edit_task ?? 0) > 0
      ? totalImprovement / (output.metrics.toolCallsByType.edit_task ?? 0)
      : 0;

  return [
    {
      name: "score_improvement",
      score: clipScore(avgScoreImprovement),
      metadata: {
        totalImprovement,
      },
    },
    {
      name: "avg_improvement_per_edit",
      score: clipScore(avgImprovementPerEdit),
      metadata: {
        totalImprovement,
        edits: output.metrics.toolCallsByType.edit_task ?? 0,
      },
    },
  ];
}

export function scoreSimilarity({
  output,
}: {
  output: OptimizationResult;
}): Score[] {
  // Only consider keys that exist in baseline (don't penalize for extra scores in updated)
  const baselineKeys = Object.keys(output.baseline);
  if (baselineKeys.length === 0) {
    return [];
  }

  let totalSimilarity = 0;
  let validCount = 0;

  for (const key of baselineKeys) {
    const baselineScore = output.baseline[key];
    if (!isEmpty(baselineScore)) {
      validCount++;
    }

    const updatedScore = output.updated[key];
    if (!isEmpty(updatedScore)) {
      const similarity =
        1 - Math.abs((baselineScore.avg ?? 0) - (updatedScore.avg ?? 0));
      totalSimilarity += similarity;
    }
  }

  const avgSimilarity = validCount > 0 ? totalSimilarity / validCount : 0;

  const baselineAvg =
    Object.values(output.baseline).length > 0
      ? Object.values(output.baseline).reduce((acc, score) => {
          if (!isEmpty(score)) {
            return acc + (score.avg ?? 0);
          }
          return acc;
        }, 0) / Object.values(output.baseline).length
      : 0;

  const updatedAvg =
    Object.values(output.updated).length > 0
      ? Object.values(output.updated).reduce((acc, score) => {
          if (!isEmpty(score)) {
            return acc + (score.avg ?? 0);
          }
          return acc;
        }, 0) / Object.values(output.updated).length
      : 0;

  return [
    {
      name: "net_similarity",
      score: 1 - Math.abs(updatedAvg - baselineAvg),
      metadata: {
        updatedAvg,
        baselineAvg,
      },
    },
    {
      name: "overlapping_similarity",
      score: clipScore(avgSimilarity),
      metadata: {
        totalSimilarity,
        validCount,
      },
    },
  ];
}

function clipScore(score: number): number {
  return Math.min(Math.max(score, 0), 1);
}
