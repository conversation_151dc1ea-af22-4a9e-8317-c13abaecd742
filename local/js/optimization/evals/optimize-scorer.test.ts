import { expect, test } from "vitest";
import { scoreSimilarity } from "./optimize-scorer";

test("scoreSimilarity", async () => {
  const result = {
    input: "classify-api",
    metadata: {
      description: "Identify the type of API request.",
      difficulty: "easy",
    },
    output: {
      baseline: {
        ExactMatch: {
          avg: 0,
          max: 0,
          min: 0,
        },
      },
      metrics: {
        completed: 6,
        errors: 0,
        invalidToolCalls: 0,
        toolCalls: 6,
        toolCallsByType: {
          edit_scorers: 1,
          get_playground_summary: 1,
          get_results: 2,
          get_available_scorers: 1,
          run_task: 1,
        },
      },
      updated: {
        ExactMatch: {
          avg: 0,
          max: 0,
          min: 0,
        },
        Factuality: {
          avg: 0.091,
          max: 1,
          min: 0,
        },
      },
    },
  };
  const scores = await scoreSimilarity(result);
  const overlapping_similarity = scores.find(
    (s) => s.name === "overlapping_similarity",
  );
  expect(overlapping_similarity).toBeDefined();
  expect(overlapping_similarity?.score).toBe(1);

  const net_similarity = scores.find((s) => s.name === "net_similarity");
  expect(net_similarity).toBeDefined();
  expect(net_similarity?.score).toBe(0.9545);
});
