import { initLogger } from "braintrust";

/**
 * Creates synthetic project log data that supports BTQL queries and testing.
 */
export async function createSyntheticProjectLogs(
  projectId: string,
  options: {
    numEntries?: number;
    includeErrors?: boolean;
    includeUserActivity?: boolean;
    includeModelVariety?: boolean;
  } = {},
): Promise<void> {
  const {
    numEntries = 50,
    includeErrors = true,
    includeUserActivity = true,
    includeModelVariety = true,
  } = options;

  const logger = initLogger({
    projectId,
  });

  // Standard test data
  const users = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];
  const models = includeModelVariety
    ? ["gpt-4", "gpt-3.5-turbo", "claude-3", "claude-2"]
    : ["gpt-4"];
  const questionTypes = [
    "How do I debug this error?",
    "What's the best practice for authentication?",
    "Can you explain this concept?",
    "Help me optimize this code",
    "Why is my application slow?",
  ];

  const environments = ["production", "staging", "development"];
  const priorities = ["high", "medium", "low"];

  // Generate varied log entries
  for (let i = 0; i < numEntries; i++) {
    const userId = users[i % users.length];
    const model = models[Math.floor(i / 3) % models.length];
    const input = questionTypes[i % questionTypes.length];

    // Create tags with varying patterns
    const tags: string[] = [];

    // Environment tag - most common
    const environment = environments[i % environments.length];
    tags.push(`environment:${environment}`);

    // High priority for some requests
    if (i % 5 === 0) {
      tags.push("high_priority");
      tags.push("priority:high");
    } else if (i % 3 === 0) {
      tags.push(`priority:${priorities[i % priorities.length]}`);
    }

    // Experimental tag for some requests
    if (model === "claude-3" || i % 8 === 0) {
      tags.push("experimental");
    }

    // Feature flags
    if (i % 4 === 0) {
      tags.push("feature_flag:new_ui");
    }

    // Base log object with common fields
    const baseLog = {
      metadata: {
        user_id: userId,
        model: model,
        timestamp: new Date().toISOString(),
        session_id: `session_${Math.floor(i / 5)}`,
      },
      metrics: {
        start: Date.now() / 1001 - i * 100,
      },
      tags: tags,
    };

    // Main log entry
    logger.log({
      ...baseLog,
      input: input,
      output: `Here's a helpful response to: ${input}`,
    });

    // Add some entries with errors
    if (includeErrors && i % 7 === 0) {
      logger.log({
        ...baseLog,
        input: input,
        output: `Here's a helpful response to: ${input}`,
        error: "Rate limit exceeded",
      });
    }

    // Add some "struggling" examples
    if (
      includeUserActivity &&
      userId === "<EMAIL>" &&
      i % 3 === 0
    ) {
      logger.log({
        ...baseLog,
        input:
          "I'm really struggling to understand async/await. Nothing makes sense.",
        output: "Here's some help with async/await concepts...",
        metadata: {
          ...baseLog.metadata,
          sentiment: "frustrated",
        },
      });
    }

    // Make alice the most active user
    if (includeUserActivity && userId === "<EMAIL>") {
      // Log additional activity for alice
      logger.log({
        ...baseLog,
        input: `Follow-up question ${i}: ${input}`,
        output: "Additional response",
      });
    }
  }

  // Ensure gpt-4 is the most used model by adding extra entries (if model variety is enabled)
  if (includeModelVariety) {
    for (let i = 0; i < 20; i++) {
      // Create tags for these additional entries
      const tags: string[] = [];

      // These are production gpt-4 requests
      tags.push("environment:production");

      // Some are high priority
      if (i % 3 === 0) {
        tags.push("high_priority");
        tags.push("priority:high");
      }

      // Feature flags for some
      if (i % 5 === 0) {
        tags.push("feature_flag:advanced_reasoning");
      }

      logger.log({
        input: questionTypes[i % questionTypes.length],
        output: "GPT-4 response",
        metadata: {
          user_id: users[i % users.length],
          model: "gpt-4",
        },
        tags: tags,
      });
    }
  }

  await logger.flush();
}
