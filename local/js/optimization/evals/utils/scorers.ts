import { Score, Factuality } from "autoevals";
import { ChatMetrics } from "../../llm/chat";

export interface ScoredOutput {
  result?: string;
  metrics: ChatMetrics;
}

/**
 * Score the result using Factuality comparison
 */
export async function scoreResultAccuracy({
  input,
  output,
  expected,
}: {
  input: string;
  output: ScoredOutput;
  expected: string;
}): Promise<Score> {
  if (!output.result) {
    return {
      name: "result_accuracy",
      score: 0,
      metadata: { error: "No result generated" },
    };
  }

  return await Factuality({
    input: input,
    output: output.result,
    expected: expected,
  });
}

/**
 * Score tool success rate based on completion metrics
 */
export function toolSuccessRate({ output }: { output: ScoredOutput }): Score {
  const metrics = output.metrics;
  return {
    name: "tool_success_rate",
    score: metrics.toolCalls > 0 ? metrics.completed / metrics.toolCalls : null,
    metadata: {
      completed: metrics.completed,
      toolCalls: metrics.toolCalls,
      errors: metrics.errors,
    },
  };
}

/**
 * Score that there are no invalid tool calls
 */
export function noInvalidToolCalls({
  output,
}: {
  output: ScoredOutput;
}): Score {
  return {
    name: "no_invalid_tool_calls",
    score: output.metrics.invalidToolCalls === 0 ? 1 : 0,
    metadata: {
      invalidToolCalls: output.metrics.invalidToolCalls,
    },
  };
}

/**
 * Score that btql_query was used
 */
export function btqlQueryUsed({ output }: { output: ScoredOutput }): Score {
  const btqlUsed = output.metrics.toolCallsByType?.btql_query > 0;
  return {
    name: "btql_query_used",
    score: btqlUsed ? 1 : 0,
    metadata: {
      toolCallsByType: output.metrics.toolCallsByType,
    },
  };
}

/**
 * Score that run_btql was used (for BTQL sandbox context)
 */
export function runBtqlUsed({ output }: { output: ScoredOutput }): Score {
  const runBtqlUsed = output.metrics.toolCallsByType?.run_btql > 0;
  return {
    name: "run_btql_used",
    score: runBtqlUsed ? 1 : 0,
    metadata: {
      toolCallsByType: output.metrics.toolCallsByType,
    },
  };
}

/**
 * Score that infer_schema was used
 */
export function inferSchemaUsed({ output }: { output: ScoredOutput }): Score {
  const inferSchemaUsed = output.metrics.toolCallsByType?.infer_schema > 0;
  return {
    name: "infer_schema_used",
    score: inferSchemaUsed ? 1 : 0,
    metadata: {
      toolCallsByType: output.metrics.toolCallsByType,
    },
  };
}

/**
 * Score that the model completed within a reasonable number of tool calls
 */
export function reasonableToolCallCount({
  output,
}: {
  output: ScoredOutput;
}): Score {
  return {
    name: "reasonable_tool_call_count",
    score: output.metrics.toolCalls <= 5 ? 1 : 0,
    metadata: {
      toolCalls: output.metrics.toolCalls,
    },
  };
}

/**
 * Common scorer combinations for different evaluation types
 */
export const BTQL_QUERY_SCORERS = [
  scoreResultAccuracy,
  toolSuccessRate,
  noInvalidToolCalls,
  btqlQueryUsed,
];

export const BTQL_SANDBOX_SCORERS = [
  scoreResultAccuracy,
  toolSuccessRate,
  noInvalidToolCalls,
  runBtqlUsed,
  inferSchemaUsed,
  reasonableToolCallCount,
];

export const BASIC_SCORERS = [toolSuccessRate, noInvalidToolCalls];
