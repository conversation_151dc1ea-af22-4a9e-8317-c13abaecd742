/**
 * Context objects for providing additional information to the AI agent,
 * mirroring the client-side context object functionality.
 */

export interface BaseContextObject {
  id: string;
  name: string;
}

export interface BTQLContextObject extends BaseContextObject {
  resource: "btql";
  query: string;
  hasResults: boolean;
  resultCount?: number;
  error?: string | null;
  lastQueryMs?: number | null;
}

export interface DataSourceMetadataContextObject extends BaseContextObject {
  resource: "dataSourceMetadata";
  schema: Record<string, unknown>;
  entityType: string;
  entityId: string;
}

export type ContextObject = BTQLContextObject | DataSourceMetadataContextObject;

/**
 * Creates a prompt with context objects, mirroring the client-side behavior
 */
export function createPromptWithContext(
  message: string,
  contextObjects: Record<string, ContextObject> = {},
): string {
  if (Object.keys(contextObjects).length === 0) {
    return message;
  }

  return `Here are the tagged context:
  <context>
  ${JSON.stringify(contextObjects)}
  </context>
  \n\n${message}`;
}

/**
 * Helper to create a BTQL context object
 */
export function createBTQLContext(
  query: string,
  name?: string,
  options: {
    hasResults?: boolean;
    resultCount?: number;
    error?: string | null;
    lastQueryMs?: number | null;
  } = {},
): BTQLContextObject {
  return {
    id: `btql-${Date.now()}`,
    resource: "btql",
    name: name || "BTQL Query",
    query,
    hasResults: options.hasResults ?? false,
    resultCount: options.resultCount,
    error: options.error,
    lastQueryMs: options.lastQueryMs,
  };
}

/**
 * Helper to create a data source metadata context object
 */
export function createDataSourceMetadataContext(
  entityType: string,
  entityId: string,
  schema: Record<string, unknown>,
  name?: string,
): DataSourceMetadataContextObject {
  return {
    id: `${entityType}-${entityId}`,
    resource: "dataSourceMetadata",
    name: name || `${entityType} Schema`,
    schema,
    entityType,
    entityId,
  };
}
