#!/bin/bash

# Test script for make-docs-questions standalone script

echo "Testing make-docs-questions script..."

# Test 1: Run with default docs directory
echo "Test 1: Using default docs directory"
npx tsx optimization/evals/make-docs-questions.ts -n 5 -o test-qa-default.json

# Test 2: Run with explicit docs directory
echo -e "\nTest 2: Using explicit docs directory"
npx tsx optimization/evals/make-docs-questions.ts ../../app/content/docs -n 5 -o test-qa-explicit.json

# Test 3: Show help
echo -e "\nTest 3: Showing help"
npx tsx optimization/evals/make-docs-questions.ts --help

echo -e "\nTests completed!"
