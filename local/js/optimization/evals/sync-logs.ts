import { ArgumentParser } from "argparse";
import { BraintrustState, loginToState, initLogger } from "braintrust";

interface SyncArgs {
  project_name: string;
  from_api_key: string;
  from_app_url: string;
  to_api_key: string;
  to_app_url: string;
  limit?: number;
  to_project_name?: string;
  filter?: string;
}

async function main() {
  const parser = new ArgumentParser({
    description: "Sync logs/experiments",
  });

  parser.add_argument("--project-name", {
    required: true,
    help: "Name of the project to sync logs from",
  });

  parser.add_argument("--from-api-key", {
    required: true,
    help: "Source API key",
  });

  parser.add_argument("--from-app-url", {
    default: "https://www.braintrust.dev",
    help: "Source app URL (default: https://www.braintrust.dev)",
  });

  parser.add_argument("--to-api-key", {
    required: true,
    help: "Target API key",
  });

  parser.add_argument("--to-app-url", {
    default: "https://www.braintrust.dev",
    help: "Target app URL (default: https://www.braintrust.dev)",
  });

  parser.add_argument("--limit", {
    type: "int",
    default: 1000,
    help: "Maximum number of logs to sync (default: 1000)",
  });

  parser.add_argument("--to-project-name", {
    help: "Name for the target project (default: '<project-name> [eval snapshot]')",
  });

  parser.add_argument("--filter", {
    help: "BTQL filter expression (e.g., \"metadata.email MATCH 'braintrustdata.com'\")",
  });

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const args = parser.parse_args() as SyncArgs;

  const fromState = await loginToState({
    appUrl: args.from_app_url,
    apiKey: args.from_api_key,
  });

  const toState = await loginToState({
    appUrl: args.to_app_url,
    apiKey: args.to_api_key,
  });

  const toProjectName =
    args.to_project_name || `${args.project_name} [eval snapshot]`;

  await syncProjectLogs({
    projectName: args.project_name,
    toProjectName,
    from: fromState,
    to: toState,
    limit: args.limit,
    filter: args.filter,
  });
}

async function getProjectId(
  projectName: string,
  state: BraintrustState,
): Promise<string> {
  console.log("STATE API URL:", state.apiUrl);
  const response = await state
    .apiConn()
    .get_json("v1/project", { name: projectName });

  if (!response.objects || response.objects.length === 0) {
    throw new Error(`Project '${projectName}' not found`);
  }

  return response.objects[0].id;
}

interface LogRow {
  id?: string;
  project_id?: string;
  span_parents?: string[];
  tags?: string[];
  _xact_id?: string;
  created?: string;
  org_id?: string;
  [key: string]: unknown;
}

async function fetchLogsViaBTQL(
  projectId: string,
  state: BraintrustState,
  limit: number,
  filter?: string,
): Promise<LogRow[]> {
  let query = `
    select: *
    from: project_logs('${projectId}') traces`;

  if (filter) {
    query += `\n    filter: ${filter}`;
  }

  query += `\n    limit: ${limit}`;

  const requestArgs = { query: query.trim(), fmt: "json" };
  const response = await state.apiConn().post_json("/btql", requestArgs);
  return response.data || [];
}

async function syncProjectLogs({
  projectName,
  toProjectName,
  from,
  to,
  limit = 1000,
  filter,
}: {
  projectName: string;
  toProjectName: string;
  from: BraintrustState;
  to: BraintrustState;
  limit?: number;
  filter?: string;
}): Promise<void> {
  console.log(
    `Syncing logs from project '${projectName}' (${from.apiUrl}) to '${toProjectName}' (${to.apiUrl})...`,
  );

  try {
    // Get project ID from source
    console.log(`Finding project '${projectName}' in source...`);
    const fromProjectId = await getProjectId(projectName, from);
    console.log(`Found source project ID: ${fromProjectId}`);

    // Fetch logs from source using BTQL
    console.log(
      `Fetching up to ${limit} logs from source${filter ? " with filter" : ""}...`,
    );
    const logs = await fetchLogsViaBTQL(fromProjectId, from, limit, filter);
    console.log(`Fetched ${logs.length} logs from source`);

    if (logs.length === 0) {
      console.log("No logs found to sync");
      return;
    }

    // Initialize logger in target environment
    console.log(`\nConnecting to target instance...`);
    const logger = initLogger({
      projectName: toProjectName,
      asyncFlush: false,
      state: to,
    });

    // For now, just use the logger directly
    // The Python implementation uses a background logger, but the TS SDK may handle this differently
    const loggerToUse = logger;

    // Process and insert logs
    console.log(`Inserting logs into target project '${toProjectName}'...`);
    for (let i = 0; i < logs.length; i++) {
      const row: LogRow = { ...logs[i] };

      // Update project_id to target project
      const projectId = (await logger.project).id;
      if (projectId) {
        row.project_id = projectId;
      }

      // Remove fields that shouldn't be copied
      delete row._xact_id;
      delete row.org_id;

      // Ensure span_parents is an array
      row.span_parents = row.span_parents || [];

      // Remove tags if it has span_parents (following the original logic)
      if (row.span_parents.length > 0) {
        delete row.tags;
      }

      // Log the row
      loggerToUse.log(row);

      if ((i + 1) % 100 === 0) {
        console.log(`  Processed ${i + 1} logs...`);
      }
    }

    // Flush the logger
    await loggerToUse.flush();

    console.log(
      `\nSuccessfully synced ${logs.length} logs to target project '${toProjectName}'`,
    );
  } catch (error) {
    console.error(`Error syncing project logs:`, error);
    throw error;
  }
}

main().catch(console.error);
