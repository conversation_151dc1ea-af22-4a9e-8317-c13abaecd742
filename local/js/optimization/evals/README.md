# Documentation Q&A Generation and Upload

This directory contains scripts for generating question-answer pairs from documentation and uploading them to Braintrust datasets.

## Scripts

### 1. Generate Q&A Dataset

Generate questions and answers from the documentation:

```bash
npx tsx make-docs-questions.ts
```

Options:

- `docs_dir` - Path to docs directory (defaults to `app/content/docs`)
- `-o, --output` - Output file path (defaults to `data/docs-qa-dataset.json`)
- `-n, --num-questions` - Number of questions to generate (default: 100)
- `-m, --model` - Model to use (default from system prompt)
- `--max-questions-per-file` - Max questions per documentation file (default: 5)
- `--batch-size` - Number of questions to generate in parallel (default: 5)
- `--difficulty-distribution` - JSON string for difficulty distribution
- `--category-distribution` - JSON string for category distribution

Example:

```bash
npx tsx make-docs-questions.ts -n 50 --batch-size 10
```

### 2. Upload to Braintrust

Upload the generated Q&A dataset to Braintrust:

```bash
./run-upload-docs-questions.sh
```

Or directly:

```bash
npx tsx upload-docs-questions.ts
```

Options:

- `input` - Input JSON file (defaults to `data/docs-qa-dataset.json`)
- `--project` - Braintrust project name (default: "Optimization evals")
- `--dataset` - Dataset name (default: "docs-questions")
- `--batch-size` - Upload batch size (default: 100)
- `--dry-run` - Preview what would be uploaded without actually uploading

Example:

```bash
# Dry run to preview
npx tsx upload-docs-questions.ts --dry-run

# Upload to Braintrust
npx tsx upload-docs-questions.ts data/docs-qa-dataset.json
```

## Dataset Format

The dataset uploaded to Braintrust has the following structure:

- **input**: `{ question: string }` - The generated question
- **expected**: `string` - The comprehensive answer (directly as a string)
- **metadata**:
  - `difficulty`: "easy" | "medium" | "hard"
  - `category`: Question category type
  - `references`: Array with a single URL path to the documentation (e.g., `["docs/start/eval-sdk#evaluate-via-sdk"]`)
  - `source`: Original source information including file path, headers, and line numbers
  - `model`: Model used for generation
  - `generated_at`: Timestamp

## Workflow

1. Generate questions from documentation:

   ```bash
   npx tsx make-docs-questions.ts -n 200 --batch-size 10
   ```

2. Review the generated dataset:

   ```bash
   cat data/docs-qa-dataset.json | jq '.questions[0:3]'
   ```

3. Preview what will be uploaded:

   ```bash
   npx tsx upload-docs-questions.ts --dry-run
   ```

4. Upload to Braintrust:
   ```bash
   npx tsx upload-docs-questions.ts
   ```

## Requirements

- Set `BRAINTRUST_API_KEY` environment variable
- Ensure you have access to the "Optimization evals" project in Braintrust
