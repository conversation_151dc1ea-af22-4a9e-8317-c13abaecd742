#!/usr/bin/env node

import { ArgumentParser } from "argparse";
import { Document<PERSON>hunker } from "../../doc-index/chunker";
import { DocumentChunk } from "../../doc-index/schema";
import { _internalGetGlobalState, wrapOpenAI } from "braintrust";
import { login } from "braintrust";
import OpenAI from "openai";
import ora from "ora";
import chalk from "chalk";
import * as fs from "fs/promises";
import path from "path";
import { z } from "zod";
import { DEFAULT_MODEL } from "../llm/system-prompt";
import { findGitRoot } from "../../doc-index/utils";
import { _urljoin } from "braintrust/util";

const questionGenerationPrompt = `You are a helpful assistant that generates high-quality questions and answers based on documentation content.

Given a documentation chunk, generate a question that someone might ask about this content, along with a comprehensive answer based on the information provided.

IMPORTANT GUIDELINES:

The questions should:
- Use terminology that real users would use, not internal implementation details
- For example: ask about "continuous export" not "automation", ask about "real-time data streaming" not "websockets"
- Avoid mentioning internal tools/libraries (like fumadocs, MDX compilation, etc.)
- Be natural and realistic - something a developer or data scientist would actually ask
- Focus on user-facing features and capabilities, not implementation details
- Cover different difficulty levels based on the parameters
- Be specific enough to have a clear answer
- Vary in type (how-to, conceptual, troubleshooting, best practices, etc.)

The answers should:
- Be accurate and based solely on the provided content
- Be comprehensive but concise
- Include code examples when relevant
- Reference specific details from the documentation
- Explain concepts in terms of what users can achieve, not how it's implemented

Examples of GOOD questions:
- "How do I set up continuous export to send my data to S3?"
- "What's the best way to track experiments across multiple model versions?"
- "How can I integrate Braintrust with my existing CI/CD pipeline?"

Examples of BAD questions:
- "What is an automation in Braintrust?" (too vague, uses internal terminology)
- "How does the MDX compilation work?" (implementation detail)
- "What fumadocs components are used?" (internal tooling)

Return your response as a JSON object with the following structure:
{
  "question": "The question text",
  "answer": "The comprehensive answer",
  "difficulty": "easy|medium|hard",
  "category": "how-to|conceptual|troubleshooting|best-practices|integration|api-reference"
}`;

interface QuestionAnswer {
  question: string;
  answer: string;
  difficulty: "easy" | "medium" | "hard";
  category:
    | "how-to"
    | "conceptual"
    | "troubleshooting"
    | "best-practices"
    | "integration"
    | "api-reference";
  source: {
    filePath: string;
    headers: string[];
    startLine: number;
    endLine: number;
  };
}

const argsSchema = z.object({
  docs_dir: z.string(),
  output: z.string(),
  model: z.string(),
  num_questions: z.number(),
  max_questions_per_file: z.number(),
  batch_size: z.number(),
  difficulty_distribution: z.object({
    easy: z.number(),
    medium: z.number(),
    hard: z.number(),
  }),
  category_distribution: z.object({
    "how-to": z.number(),
    conceptual: z.number(),
    troubleshooting: z.number(),
    "best-practices": z.number(),
    integration: z.number(),
    "api-reference": z.number(),
  }),
});

async function generateQuestionsForChunk(
  chunk: DocumentChunk,
  client: OpenAI,
  model: string,
  targetDifficulty?: string,
  targetCategory?: string,
): Promise<QuestionAnswer | null> {
  try {
    const prompt = `${questionGenerationPrompt}

${targetDifficulty ? `Focus on generating a ${targetDifficulty} difficulty question.` : ""}
${targetCategory ? `Focus on generating a ${targetCategory} type question.` : ""}

Documentation chunk:
Title: ${chunk.title || "N/A"}
Headers: ${chunk.headers.join(" > ")}
Content:
${chunk.content}`;

    // Use json_object mode which is more widely supported
    const response = await client.chat.completions.create({
      model,
      messages: [
        {
          role: "system",
          content:
            "You are a helpful assistant that generates questions and answers from documentation. Always return valid JSON matching the specified format without any markdown formatting or code blocks.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
      max_tokens: 2048,
    });

    let content = response.choices[0].message.content || "{}";

    // Handle case where LLM wraps JSON in markdown code blocks
    const jsonMatch = content.match(/```(?:json)?\s*\n?([\s\S]*?)\n?```/);
    if (jsonMatch) {
      content = jsonMatch[1].trim();
    }

    try {
      const result = JSON.parse(content);

      // Validate the result has required fields
      if (
        !result.question ||
        !result.answer ||
        !result.difficulty ||
        !result.category
      ) {
        console.error("Missing required fields in response:", result);
        return null;
      }

      // Validate enums
      if (!["easy", "medium", "hard"].includes(result.difficulty)) {
        console.error("Invalid difficulty:", result.difficulty);
        return null;
      }

      if (
        ![
          "how-to",
          "conceptual",
          "troubleshooting",
          "best-practices",
          "integration",
          "api-reference",
        ].includes(result.category)
      ) {
        console.error("Invalid category:", result.category);
        return null;
      }

      return {
        question: result.question,
        answer: result.answer,
        difficulty: result.difficulty,
        category: result.category,
        source: {
          filePath: chunk.filePath,
          headers: chunk.headers,
          startLine: chunk.startLine,
          endLine: chunk.endLine,
        },
      };
    } catch (parseError) {
      console.error("Failed to parse JSON response:", content);
      console.error("Parse error:", parseError);
      return null;
    }
  } catch (error) {
    console.error("Error generating question for chunk:", error);
    if (error instanceof Error) {
      console.error("Error details:", {
        message: error.message,
        name: error.name,
      });
    }
    return null;
  }
}

function selectTargetDifficulty(
  currentCounts: Record<string, number>,
  targetDistribution: Record<string, number>,
): string {
  const total = Object.values(currentCounts).reduce((a, b) => a + b, 0);
  if (total === 0) return "medium";

  let maxRatio = -1;
  let selected = "medium";

  for (const [difficulty, targetPct] of Object.entries(targetDistribution)) {
    const currentPct = (currentCounts[difficulty] || 0) / total;
    const ratio = targetPct - currentPct;
    if (ratio > maxRatio) {
      maxRatio = ratio;
      selected = difficulty;
    }
  }

  return selected;
}

function selectTargetCategory(
  currentCounts: Record<string, number>,
  targetDistribution: Record<string, number>,
): string {
  const total = Object.values(currentCounts).reduce((a, b) => a + b, 0);
  if (total === 0) return "how-to";

  let maxRatio = -1;
  let selected = "how-to";

  for (const [category, targetPct] of Object.entries(targetDistribution)) {
    const currentPct = (currentCounts[category] || 0) / total;
    const ratio = targetPct - currentPct;
    if (ratio > maxRatio) {
      maxRatio = ratio;
      selected = category;
    }
  }

  return selected;
}

async function main() {
  const parser = new ArgumentParser({
    description: "Generate questions and answers from documentation",
  });

  parser.add_argument("docs_dir", {
    help: "Path to the documentation directory (optional, defaults to app/content/docs)",
    nargs: "?",
  });

  parser.add_argument("-o", "--output", {
    help: "Output file path for the generated Q&A dataset",
    default: "data/docs-qa-dataset.json",
  });

  parser.add_argument("-n", "--num-questions", {
    type: "int",
    help: "Total number of questions to generate",
    default: 100,
  });

  parser.add_argument("--max-questions-per-file", {
    type: "int",
    help: "Maximum questions to generate per documentation file",
    default: 5,
  });

  parser.add_argument("--model", {
    help: "Model to use for generation",
    default: DEFAULT_MODEL,
  });

  parser.add_argument("--difficulty-distribution", {
    help: 'Distribution of difficulty levels as JSON (e.g., \'{"easy": 0.3, "medium": 0.5, "hard": 0.2}\')',
    default: '{"easy": 0.3, "medium": 0.5, "hard": 0.2}',
    type: "str",
  });

  parser.add_argument("--category-distribution", {
    help: "Distribution of categories as JSON",
    default:
      '{"how-to": 0.3, "conceptual": 0.2, "troubleshooting": 0.15, "best-practices": 0.15, "integration": 0.1, "api-reference": 0.1}',
    type: "str",
  });

  parser.add_argument("--batch-size", {
    help: "Number of questions to generate in parallel",
    type: "int",
    default: 5,
  });

  const rawArgs = parser.parse_args();

  // Find git root and determine docs directory
  const gitRoot = findGitRoot();
  if (!gitRoot) {
    console.error(chalk.red("Error: Not in a git repository"));
    process.exit(1);
  }

  const docsDir = rawArgs.docs_dir
    ? path.resolve(rawArgs.docs_dir)
    : path.resolve(gitRoot, "app/content/docs");

  const args = argsSchema.parse({
    ...rawArgs,
    docs_dir: docsDir,
    difficulty_distribution: JSON.parse(rawArgs.difficulty_distribution),
    category_distribution: JSON.parse(rawArgs.category_distribution),
  });

  const spinner = ora("Initializing...").start();

  try {
    await login();

    const proxyURL = _urljoin(
      _internalGetGlobalState().proxyUrl ??
        _internalGetGlobalState().apiUrl ??
        "https://api.braintrust.dev",
      "v1/proxy",
    );

    const apiKey = _internalGetGlobalState().loginToken;
    if (!apiKey) {
      throw new Error(
        "Not logged in. Please make sure to set BRAINTRUST_API_KEY.",
      );
    }

    // Initialize OpenAI client
    const openai = wrapOpenAI(
      new OpenAI({
        baseURL: proxyURL,
        apiKey,
      }),
    );

    spinner.text = "Loading documentation files...";

    const chunker = new DocumentChunker({
      maxChunkSize: 2000,
      minChunkSize: 200,
      overlapSize: 100,
    });

    const chunks = await chunker.chunkDirectory(args.docs_dir);

    spinner.text = `Found ${chunks.length} documentation chunks`;

    // Group chunks by file
    const chunksByFile = chunks.reduce(
      (acc, chunk) => {
        if (!acc[chunk.filePath]) {
          acc[chunk.filePath] = [];
        }
        acc[chunk.filePath].push(chunk);
        return acc;
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      {} as Record<string, typeof chunks>,
    );

    const questions: QuestionAnswer[] = [];
    const difficultyCounts: Record<string, number> = {
      easy: 0,
      medium: 0,
      hard: 0,
    };
    const categoryCounts: Record<string, number> = {
      "how-to": 0,
      conceptual: 0,
      troubleshooting: 0,
      "best-practices": 0,
      integration: 0,
      "api-reference": 0,
    };

    // Prepare all chunks for generation
    const allChunksToProcess: Array<{
      chunk: DocumentChunk;
      targetDifficulty: string;
      targetCategory: string;
    }> = [];

    const files = Object.keys(chunksByFile);

    // Calculate how many questions we want from each file
    const questionsPerFile = Math.min(
      args.max_questions_per_file,
      Math.ceil(args.num_questions / files.length),
    );

    // Prepare chunks from each file
    for (const filePath of files) {
      const fileChunks = chunksByFile[filePath];

      // Shuffle chunks to get variety
      const shuffledChunks = [...fileChunks].sort(() => Math.random() - 0.5);

      // Take only the number of chunks we need from this file
      const chunksToTake = Math.min(questionsPerFile, shuffledChunks.length);

      for (
        let i = 0;
        i < chunksToTake && allChunksToProcess.length < args.num_questions;
        i++
      ) {
        const targetDifficulty = selectTargetDifficulty(
          difficultyCounts,
          args.difficulty_distribution,
        );
        const targetCategory = selectTargetCategory(
          categoryCounts,
          args.category_distribution,
        );

        // Pre-update counts to better distribute across parallel requests
        difficultyCounts[targetDifficulty] =
          (difficultyCounts[targetDifficulty] || 0) + 1;
        categoryCounts[targetCategory] =
          (categoryCounts[targetCategory] || 0) + 1;

        allChunksToProcess.push({
          chunk: shuffledChunks[i],
          targetDifficulty,
          targetCategory,
        });
      }
    }

    // Process chunks in parallel batches
    const BATCH_SIZE = args.batch_size;
    let processedCount = 0;

    for (let i = 0; i < allChunksToProcess.length; i += BATCH_SIZE) {
      const batch = allChunksToProcess.slice(
        i,
        Math.min(i + BATCH_SIZE, allChunksToProcess.length),
      );

      spinner.text = `Generating Q&A ${processedCount + 1}-${Math.min(processedCount + batch.length, args.num_questions)}/${args.num_questions}...`;

      // Generate questions in parallel for this batch
      const batchPromises = batch.map(
        ({ chunk, targetDifficulty, targetCategory }) =>
          generateQuestionsForChunk(
            chunk,
            openai,
            args.model,
            targetDifficulty,
            targetCategory,
          ),
      );

      const batchResults = await Promise.all(batchPromises);

      // Process results
      for (const qa of batchResults) {
        if (qa && questions.length < args.num_questions) {
          questions.push(qa);
          processedCount++;

          // Update spinner with current file being processed
          const currentFile = path.basename(qa.source.filePath);
          spinner.text = `Generated ${processedCount}/${args.num_questions} Q&A (current: ${currentFile})`;
        }
      }
    }

    // Recalculate actual distribution from generated questions
    difficultyCounts.easy = 0;
    difficultyCounts.medium = 0;
    difficultyCounts.hard = 0;
    Object.keys(categoryCounts).forEach((key) => {
      categoryCounts[key] = 0;
    });

    for (const qa of questions) {
      difficultyCounts[qa.difficulty] =
        (difficultyCounts[qa.difficulty] || 0) + 1;
      categoryCounts[qa.category] = (categoryCounts[qa.category] || 0) + 1;
    }

    spinner.succeed(`Generated ${questions.length} questions and answers`);

    // Save the dataset
    // Create output directory if it doesn't exist
    const outputDir = path.dirname(args.output);
    if (outputDir && outputDir !== ".") {
      await fs.mkdir(outputDir, { recursive: true });
    }

    await fs.writeFile(
      args.output,
      JSON.stringify(
        {
          generated_at: new Date().toISOString(),
          model: args.model,
          total_questions: questions.length,
          difficulty_distribution: difficultyCounts,
          category_distribution: categoryCounts,
          questions,
        },
        null,
        2,
      ),
    );

    console.log(chalk.green(`\n✓ Dataset saved to ${args.output}`));
    console.log(chalk.dim(`\nDocumentation directory: ${args.docs_dir}`));
    console.log(chalk.dim("\nDifficulty distribution:"));
    for (const [level, count] of Object.entries(difficultyCounts)) {
      const pct = ((count / questions.length) * 100).toFixed(1);
      console.log(chalk.dim(`  ${level}: ${count} (${pct}%)`));
    }
    console.log(chalk.dim("\nCategory distribution:"));
    for (const [cat, count] of Object.entries(categoryCounts)) {
      const pct = ((count / questions.length) * 100).toFixed(1);
      console.log(chalk.dim(`  ${cat}: ${count} (${pct}%)`));
    }
  } catch (error) {
    spinner.fail("Error generating questions");
    console.error(chalk.red("Error:"), error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error(chalk.red("Unhandled error:"), error);
  process.exit(1);
});
