#!/usr/bin/env node

import { ArgumentParser } from "argparse";
import { init, initDataset, login } from "braintrust";
import * as fs from "fs/promises";
import ora from "ora";
import chalk from "chalk";
import path from "path";

interface QuestionAnswer {
  question: string;
  answer: string;
  difficulty: "easy" | "medium" | "hard";
  category: string;
  source: {
    filePath: string;
    headers: string[];
    startLine: number;
    endLine: number;
  };
}

interface DatasetFile {
  generated_at: string;
  model: string;
  total_questions: number;
  difficulty_distribution: Record<string, number>;
  category_distribution: Record<string, number>;
  questions: QuestionAnswer[];
}

function createReferenceUrls(filePath: string, headers: string[]): string[] {
  // Remove the .mdx extension and any leading slashes
  const cleanPath = filePath.replace(/\.mdx?$/, "").replace(/^\//, "");

  // If headers exist, return path with anchor; otherwise just the path
  if (headers.length > 0) {
    // Convert header to URL-friendly anchor format
    const lastHeader = headers[headers.length - 1];
    const anchor = lastHeader
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .trim();

    return [`${cleanPath}#${anchor}`];
  }

  return [cleanPath];
}

async function main() {
  const parser = new ArgumentParser({
    description: "Upload generated Q&A dataset to Braintrust",
  });

  parser.add_argument("input", {
    help: "Input JSON file containing the Q&A dataset",
    default: "data/docs-qa-dataset.json",
    nargs: "?",
  });

  parser.add_argument("--project", {
    help: "Braintrust project name",
    default: "Optimization evals",
  });

  parser.add_argument("--dataset", {
    help: "Dataset name",
    default: "docs-questions",
  });

  parser.add_argument("--batch-size", {
    help: "Number of records to upload in each batch",
    type: "int",
    default: 100,
  });

  parser.add_argument("--dry-run", {
    help: "Print what would be uploaded without actually uploading",
    action: "store_true",
  });

  const args = parser.parse_args();
  const spinner = ora("Loading dataset...").start();

  try {
    // Read the dataset file
    const inputPath = args.input || "data/docs-qa-dataset.json";
    const datasetContent = await fs.readFile(inputPath, "utf-8");
    const data: DatasetFile = JSON.parse(datasetContent);

    spinner.text = `Loaded ${data.total_questions} questions from ${inputPath}`;

    if (args.dry_run) {
      spinner.info("Dry run mode - showing sample records");

      // Show first 3 records
      const sampleRecords = data.questions.slice(0, 3).map((qa) => ({
        input: qa.question,
        expected: qa.answer,
        metadata: {
          difficulty: qa.difficulty,
          category: qa.category,
          references: createReferenceUrls(
            qa.source.filePath,
            qa.source.headers,
          ),
          source: {
            filePath: qa.source.filePath,
            headers: qa.source.headers,
            startLine: qa.source.startLine,
            endLine: qa.source.endLine,
          },
        },
      }));

      console.log(chalk.blue("\nSample records:"));
      console.log(JSON.stringify(sampleRecords, null, 2));

      console.log(
        chalk.dim(`\n... and ${data.total_questions - 3} more records`),
      );
      return;
    }

    // Initialize Braintrust
    spinner.text = "Connecting to Braintrust...";
    await login();

    // Get or create the dataset
    spinner.text = `Accessing dataset "${args.dataset}" in project "${args.project}"...`;
    const dataset = initDataset({
      project: args.project,
      dataset: args.dataset,
    });

    // Prepare records for upload
    const records = data.questions.map((qa) => ({
      input: qa.question,
      expected: qa.answer,
      metadata: {
        difficulty: qa.difficulty,
        category: qa.category,
        references: createReferenceUrls(qa.source.filePath, qa.source.headers),
        source: {
          filePath: qa.source.filePath,
          headers: qa.source.headers,
          startLine: qa.source.startLine,
          endLine: qa.source.endLine,
        },
        model: data.model,
        generated_at: data.generated_at,
      },
    }));

    // Upload in batches
    spinner.text = "Uploading records...";
    let uploadedCount = 0;

    for (let i = 0; i < records.length; i += args.batch_size) {
      const batch = records.slice(
        i,
        Math.min(i + args.batch_size, records.length),
      );

      // Insert records
      for (const record of batch) {
        await dataset.insert(record);
      }

      uploadedCount += batch.length;
      spinner.text = `Uploaded ${uploadedCount}/${records.length} records`;
    }

    // Flush any remaining records
    await dataset.flush();

    spinner.succeed(
      `Successfully uploaded ${records.length} Q&A pairs to dataset "${args.dataset}" in project "${args.project}"`,
    );

    // Show distribution summary
    console.log(chalk.dim("\nDifficulty distribution:"));
    for (const [level, count] of Object.entries(data.difficulty_distribution)) {
      const pct = ((count / data.total_questions) * 100).toFixed(1);
      console.log(chalk.dim(`  ${level}: ${count} (${pct}%)`));
    }

    console.log(chalk.dim("\nCategory distribution:"));
    for (const [cat, count] of Object.entries(data.category_distribution)) {
      const pct = ((count / data.total_questions) * 100).toFixed(1);
      console.log(chalk.dim(`  ${cat}: ${count} (${pct}%)`));
    }

    // Show sample reference URLs
    console.log(chalk.dim("\nSample reference URLs:"));
    const sampleRefs = records.slice(0, 3).map((r) => r.metadata.references);
    sampleRefs.forEach((ref) => console.log(chalk.dim(`  ${ref}`)));
  } catch (error) {
    spinner.fail("Error uploading dataset");
    console.error(chalk.red("Error:"), error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error(chalk.red("Unhandled error:"), error);
  process.exit(1);
});
