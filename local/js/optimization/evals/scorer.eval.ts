import { current<PERSON><PERSON>, <PERSON><PERSON>, NOOP_SPAN, Span, traced } from "braintrust";
import { PROJECT_NAME } from "./meta-eval";
import {
  DEFAULT_TRIAL_COUNT,
  makeDataset,
  makeEvalChat,
  MODEL,
  SpanChatLogger,
  TASK_FILTER,
  TRIAL_COUNT,
} from "./eval-vars";
import { scoreSimilarity } from "./optimize-scorer";
import { type OptimizationResult } from "./optimize-scorer";

Eval(PROJECT_NAME, {
  data: makeDataset(),
  task: async (input): Promise<OptimizationResult> => {
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;
    try {
      // Create a fresh chat provider to get the baseline performance
      const baselineResults = await getBaseline(input);

      const {
        chat,
        consoleLogger: cl,
        tools,
        rawOutput: ro,
        task,
      } = await makeEvalChat(input, {
        allowed_tools: [
          "continue_execution",
          "get_summary",
          "get_results",
          "get_available_scorers",
          "edit_scorers",
          "run_task",
        ],
      });

      consoleLogger = cl;
      rawOutput = ro;

      // Now, remove all the scorers from the task
      task.scores = [];

      // Now, ask the model to improve the task
      await chat.turn(
        "improve the performance, re-run as needed. do not ask me for permission to proceed. just proceed with steps.",
      );

      const updatedResults = await traced(
        async (span) => {
          const ret = await tools.tools.run_task(
            {
              index: 0,
            },
            span,
          );
          span.log({ output: ret });
          return ret;
        },
        {
          name: "updated experiment",
        },
      );

      const { toolCallsByType: _, ...numericMetrics } = chat.metrics;
      currentSpan().log({ metrics: numericMetrics });
      return {
        baseline: baselineResults.summary.scores,
        updated: updatedResults.summary.scores,
        metrics: chat.metrics,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: [scoreSimilarity],
  experimentName: `scorer selection-${MODEL}${
    TASK_FILTER === "*" ? "" : ` [tasks: ${TASK_FILTER}]`
  }${TRIAL_COUNT === DEFAULT_TRIAL_COUNT ? "" : ` [trials: ${TRIAL_COUNT}]`}`,
  metadata: {
    model: MODEL,
    taskFilter: TASK_FILTER,
  },
  trialCount: TRIAL_COUNT,
});

async function getBaseline(input: string) {
  const { tools } = await makeEvalChat(input);

  // Get the baseline performance
  const baselineResults = await traced(
    async (span) => {
      const ret = await tools.tools.run_task(
        {
          index: 0,
        },
        span,
      );
      span.log({ output: ret });
      return ret;
    },
    {
      name: "baseline experiment",
    },
  );
  return baselineResults;
}
