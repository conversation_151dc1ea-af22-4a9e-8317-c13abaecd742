import { Argument<PERSON><PERSON><PERSON> } from "argparse";
import { Eval, initDataset } from "braintrust";
import { EVALS } from "./tasks";
import { PROJECT_NAME } from "./meta-eval";
import { z } from "zod";

export const syncArgsSchema = z.object({
  eval_name: z.string(),
  project_name: z.string(),
  dataset_project_name: z.string(),
});

async function main() {
  const parser = new ArgumentParser({
    description: "Run a base evaluation",
  });

  parser.add_argument("eval-name", {
    help: "Evaluation name",
    choices: Object.keys(EVALS),
  });

  parser.add_argument("--dataset-project-name", {
    default: PROJECT_NAME,
    help: `Project name from which to load the dataset (default: ${PROJECT_NAME})`,
  });

  parser.add_argument("--project-name", {
    default: PROJECT_NAME + " dev",
    help: `Project name to save the results to (default: ${PROJECT_NAME} dev)`,
  });

  const rawArgs = parser.parse_args();
  const args = syncArgsSchema.parse({
    eval_name: rawArgs["eval-name"],
    project_name: rawArgs.project_name,
    dataset_project_name: rawArgs.dataset_project_name,
  });

  const metaEval = EVALS[args.eval_name];

  const results = await Eval(args.project_name, {
    data: initDataset({
      project: args.dataset_project_name,
      dataset: metaEval.dataset,
    }),
    task: async (input, hook) => {
      return await metaEval.task.execute(input, hook);
    },
    scores: metaEval.scores,
  });

  console.log(results.summary);
}

main().catch(console.error);
