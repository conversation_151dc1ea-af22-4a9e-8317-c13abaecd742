import {
  _internalGetGlobalState,
  BaseMetadata,
  BraintrustStream,
  EvalHooks,
  EvalParameters,
  getSpanParentObject,
  PromptDefinitionWithTools,
  promptDefinitionToPromptData,
  type Evaluator,
  promptDefinitionWithToolsSchema,
} from "braintrust";

export const DATASETS = [
  "ai-search-examples",
  "movies",
  "coqa-factuality",
  "request-payloads",
];
export const PROJECT_NAME = "Optimization evals";

export interface SerializableTask {
  serialize: () => object;
  deserialize: (data: object) => void;
  copy: () => SerializableTask;
  execute: (
    input: unknown,
    hooks: EvalHooks<unknown, BaseMetadata, EvalParameters>,
  ) => Promise<unknown>;
}

export type MetaEval = {
  name: string;
  description: string;
  difficulty: "easy" | "medium" | "hard";
  dataset: (typeof DATASETS)[number];
  task: SerializableTask;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  scores: Evaluator<any, any, any, any, any>["scores"];
};

export class SerializablePrompt implements SerializableTask {
  constructor(
    public name: string,
    public prompt: PromptDefinitionWithTools,
  ) {}

  serialize() {
    return JSON.parse(JSON.stringify(this.prompt));
  }

  deserialize(data: unknown) {
    this.prompt = promptDefinitionWithToolsSchema.parse(data);
  }

  copy() {
    return new SerializablePrompt(
      this.name,
      promptDefinitionWithToolsSchema.parse(this.serialize()),
    );
  }

  async execute(
    input: unknown,
    hooks: EvalHooks<unknown, BaseMetadata, EvalParameters>,
  ) {
    const state = _internalGetGlobalState();
    if (!state.proxyUrl) {
      throw new Error("Proxy URL not found");
    }

    const result = await state.proxyConn().post(
      "function/invoke",
      {
        input,
        expected: hooks.expected,
        metadata: hooks.metadata,
        inline_prompt: promptDefinitionToPromptData(
          this.prompt,
          this.prompt.tools,
        ),
        name: this.name,
        parent: await getSpanParentObject().export(),
        stream: true,
      },
      {
        headers: {
          Accept: "text/event-stream",
        },
      },
    );
    if (!result.ok) {
      throw new Error(
        `Failed to invoke function: ${result.status} ${result.statusText}. ${await result.text()}`,
      );
    }
    if (!result.body) {
      throw new Error("No body returned from function");
    }
    const btStream = new BraintrustStream(result.body);
    return await btStream.finalValue();
  }
}
