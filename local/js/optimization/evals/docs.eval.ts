import { Eval, initDataset, wrapOpenAI } from "braintrust";
import { PROJECT_NAME } from "./meta-eval";
import { DocumentChunker } from "../../doc-index/chunker";
import { DocumentIndexer } from "../../doc-index/indexer";
import { DocumentSearcher } from "../../doc-index/search";
import path from "path";
import { Factuality } from "autoevals";
import { OpenAI } from "openai";
import z from "zod";

const FactualityGTP5 = Factuality.partial({
  model: "gpt-5-mini",
  maxTokens: 10000,
});

/*
Eval<string, string, string>(PROJECT_NAME + " (qa)", {
  data: initDataset(PROJECT_NAME, { dataset: "docs-questions" }),
  task: async (input, hooks) => {
    // Extract the question from input
    const question = input;

    // Construct prompt with context
    const prompt = `You are a helpful assistant answering questions about Braintrust documentation.

Question: ${question}

Please provide a clear and accurate answer based on the context above. If the context doesn't contain enough information to fully answer the question, acknowledge what's missing.`;

    // Generate response using OpenAI
    const openai = wrapOpenAI(new OpenAI());
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.1,
      max_tokens: 1000,
    });

    const answer = response.choices[0]?.message?.content || "";

    return answer;
  },
  scores: [FactualityGTP5],
  experimentName: "No context",
});
*/

interface IndexParams {
  chunkSize?: number;
  minChunkSize?: number;
  overlapSize?: number;
  embeddingModel?: string;
  batchSize?: number;
}

async function prepareIndex(params: IndexParams = {}): Promise<string> {
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const indexName = `braintrust-docs-${randomSuffix}`;

  const chunker = new DocumentChunker({
    maxChunkSize: params.chunkSize || 1500,
    minChunkSize: params.minChunkSize || 100,
    overlapSize: params.overlapSize || 200,
    indexId: indexName,
  });

  const indexer = new DocumentIndexer({
    namespace: indexName,
    embeddingModel: params.embeddingModel || "text-embedding-3-small",
    batchSize: params.batchSize || 100,
    cacheDir: path.join(__dirname, ".embedding-cache"),
  });

  // Resolve paths relative to the file location
  const projectRoot = path.resolve(__dirname, "../../../../");
  const docsPath = path.join(projectRoot, "app/content/docs");
  const blogPath = path.join(projectRoot, "app/content/blog");

  console.log(`Creating index: ${indexName}`);
  console.log(`Chunking documents from ${docsPath} and ${blogPath}...`);

  const docsChunks = await chunker.chunkDirectory(docsPath);
  const blogChunks = await chunker.chunkDirectory(blogPath);
  const allChunks = [...docsChunks, ...blogChunks];

  console.log(`Total chunks: ${allChunks.length}`);
  console.log("Indexing chunks to TurboPuffer...");

  await indexer.indexChunks(allChunks, (processed, total) => {
    process.stdout.write(`\rIndexing progress: ${processed}/${total}`);
  });

  console.log("\nIndexing complete!");

  return indexName;
}

const INDEX_PARAMS: IndexParams = {
  chunkSize: 1500,
  minChunkSize: 100,
  overlapSize: 200,
  embeddingModel: "text-embedding-3-small",
  batchSize: 100,
};

const indexNamePromise: Promise<string> = prepareIndex(INDEX_PARAMS);

Eval<string, string, string>(PROJECT_NAME + " (qa)", {
  data: initDataset(PROJECT_NAME, { dataset: "docs-questions" }),
  task: async (input, hooks) => {
    const indexName = await indexNamePromise;

    // Extract the question from input
    const question = input;

    // Search for relevant documents
    const searcher = new DocumentSearcher({
      namespace: indexName,
    });

    const searchResults = await searcher.search(question, {
      topK: 5,
    });

    // Extract context from search results
    const contexts = searchResults
      .map((result, idx) => {
        const parsed = z
          .object({
            attributes: z.object({
              content: z.string().optional(),
              filePath: z.string().optional(),
              headers: z.array(z.string()).optional(),
            }),
          })
          .parse(result);

        const content = parsed.attributes.content || "";
        const filePath = parsed.attributes.filePath || "Unknown file";
        const headers = parsed.attributes.headers || [];

        return `[${idx + 1}] File: ${filePath}${headers.length > 0 ? ` > ${headers.join(" > ")}` : ""}\n${content}`;
      })
      .join("\n\n---\n\n");

    // Construct prompt with context
    const prompt = `You are a helpful assistant answering questions about Braintrust documentation.

Here is relevant context from the documentation:

${contexts}

---

Question: ${question}

Please provide a clear and accurate answer based on the context above. If the context doesn't contain enough information to fully answer the question, acknowledge what's missing.`;

    // Generate response using OpenAI
    const openai = wrapOpenAI(new OpenAI());
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.1,
      max_tokens: 1000,
    });

    const answer = response.choices[0]?.message?.content || "";

    hooks.metadata = {
      ...hooks.metadata,
      searchResults: searchResults.length,
      indexName,
    };

    return answer;
  },
  scores: [FactualityGTP5],
  experimentName: "Raw QA",
  metadata: { ...INDEX_PARAMS },
});
