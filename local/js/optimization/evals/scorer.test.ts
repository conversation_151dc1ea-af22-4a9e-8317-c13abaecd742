import { expect, it } from "vitest";

import { scoreImprovement } from "./optimize-scorer";

const input = {
  input: "ai-search",
  metadata: {
    description: "Evaluate the performance of an AI search engine",
    difficulty: "medium",
  },
  output: {
    baseline: {
      "BTQL query checker": {
        avg: 0.083,
        max: 0.25,
        min: 0,
      },
      QuerySimilarity: {
        avg: 0.455,
        max: 0.759,
        min: 0,
      },
    },
    metrics: {
      completed: 0,
      errors: 0,
      toolCalls: 0,
      toolCallsByType: {},
    },
    updated: {
      "BTQL query checker": {
        avg: 0.042,
        max: 0.25,
        min: 0,
      },
      QuerySimilarity: {
        avg: 0.454,
        max: 0.759,
        min: 0,
      },
    },
  },
};

it("should score the input", () => {
  const scores = scoreImprovement(input);
  for (const score of scores) {
    expect(score.score).toBeGreaterThanOrEqual(0);
  }
});
