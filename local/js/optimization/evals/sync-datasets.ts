import { Argument<PERSON>ars<PERSON> } from "argparse";
import { BraintrustState, initDataset, loginToState } from "braintrust";
import { DATASETS, PROJECT_NAME } from "./meta-eval";

interface SyncArgs {
  datasets: string[];
  project_name: string;
  direction: "from-prod" | "to-prod";
  local_api_key: string;
  local_app_url: string;
  prod_api_key: string;
  prod_app_url: string;
}

async function main() {
  const parser = new ArgumentParser({
    description: "Sync datasets",
  });

  parser.add_argument("--datasets", {
    nargs: "+",
    default: DATASETS,
    help: "Datasets to sync (defaults to all)",
  });

  parser.add_argument("--project-name", {
    default: PROJECT_NAME,
    help: "Project name (default: Optimization evals)",
  });

  parser.add_argument("--local-api-key", {
    required: true,
    help: "Local API key",
  });

  parser.add_argument("--local-app-url", {
    default: "http://localhost:3000",
    help: "Local app URL (default: http://localhost:3000)",
  });

  parser.add_argument("--prod-api-key", {
    required: true,
    help: "Production API key",
  });

  parser.add_argument("--prod-app-url", {
    default: "https://www.braintrust.dev",
    help: "Production app URL (default: https://www.braintrust.dev)",
  });

  parser.add_argument("--direction", {
    choices: ["from-prod", "to-prod"],
    default: "from-prod",
    help: "Sync direction: from-prod (Braintrust to S3) or to-prod (S3 to Braintrust)",
  });

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const args = parser.parse_args() as SyncArgs;

  const localState = await loginToState({
    appUrl: args.local_app_url,
    apiKey: args.local_api_key,
  });

  const prodState = await loginToState({
    appUrl: args.prod_app_url,
    apiKey: args.prod_api_key,
  });

  // Process each dataset
  await Promise.all(
    args.datasets.map(async (name) => {
      console.log(`Processing dataset: ${name}`);
      await runSync({
        projectName: args.project_name,
        datasetName: name,
        from: args.direction === "from-prod" ? prodState : localState,
        to: args.direction === "from-prod" ? localState : prodState,
      });
    }),
  );
}

async function runSync({
  projectName,
  datasetName,
  from,
  to,
}: {
  projectName: string;
  datasetName: string;
  from: BraintrustState;
  to: BraintrustState;
}): Promise<void> {
  console.log(`Syncing ${datasetName} from ${from.appUrl} to ${to.appUrl}...`);

  const allData = [];
  for await (const record of initDataset({
    project: projectName,
    dataset: datasetName,
    state: from,
  })) {
    allData.push(record);
  }

  const targetDataset = initDataset({
    project: projectName,
    dataset: datasetName,
    state: to,
  });

  for (const record of allData) {
    targetDataset.insert({
      id: record.id,
      input: record.input,
      expected: record.expected,
      tags: record.tags,
      ...(record.metadata ? { metadata: record.metadata } : {}),
    });
  }

  console.log(await targetDataset.summarize());
}

main().catch(console.error);
