import { parseQuery, traverseQuery } from "@braintrust/btql/parser";
import { ParsedQuery } from "@braintrust/btql/parser/ast";
import { Expr } from "@braintrust/btql/parser/ast";
import { BRAINTRUST_LOGICAL_SCHEMA } from "./logical_schemas";

export function redactBtqlQuery(query: string | ParsedQuery | null) {
  if (query === null) {
    return null;
  }

  if (typeof query === "string") {
    try {
      const parsed = parseQuery(query);
      return redactBtqlQuery(parsed);
    } catch {
      return "<Failed to parse query>";
    }
  }

  return redactBtqlParsedQuery(query);
}

function redactBtqlParsedQuery(queryArg: ParsedQuery) {
  const query = JSON.parse(JSON.stringify(queryArg));
  const redact = (expr: Expr) => {
    if (
      expr.op === "function" &&
      typeof expr.name.name[0] === "string" &&
      BRAINTRUST_LOGICAL_SCHEMA.properties &&
      expr.name.name[0] in BRAINTRUST_LOGICAL_SCHEMA.properties
    ) {
      return false;
    }
    if (expr.op === "literal" && typeof expr.value === "string") {
      expr.value = expr.value.replace(/[a-zA-Z0-9]/g, "*");
    }
    return true;
  };
  traverseQuery(query, redact);
  return query;
}
