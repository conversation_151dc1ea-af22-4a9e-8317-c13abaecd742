import { z } from "zod";
import {
  ASYNC_SCORING_CONTROL_FIELD,
  AUDIT_SOURCE_FIELD,
  AUDIT_METADATA_FIELD,
  IS_MERGE_FIELD,
  MERGE_PATHS_FIELD,
  OBJECT_DELETE_FIELD,
  PARENT_ID_FIELD,
  SKIP_ASYNC_SCORING_FIELD,
  TRANSACTION_ID_FIELD,
  objectNullish,
} from "braintrust/util";
import {
  asyncScoringControlSchema,
  asyncScoringStateSchema,
  datetimeStringSchema,
  functionDataSchema,
  objectReferenceSchema,
  playgroundLogsLogIdLiteralSchema,
  projectLogsLogIdLiteralSchema,
  promptLogIdLiteralSchema,
  strictPromptDataSchema,
} from "@braintrust/typespecs";
import {
  auditLogMetadataSchema,
  auditLogOriginSchema,
  commentSchema,
} from "./logical_schemas";
import { ASYNC_SCORING_STATE_FIELD, ROW_REF_FIELD } from "../constants";

// Allows us to explicitly assert that a particular field is NOT provided.
export const omitSchema = z.null().optional();

// When parsing the object IDs, we explicitly include the ids of other object
// types in each union item ensure they are null/omitted. This prevents someone
// from specifying multiple object IDs for the same row.

export const OBJECT_TYPE_FIELD = "__insert_object_type__";

export const experimentObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z.literal("experiment").default("experiment"),
  experiment_id: z.string(),
  dataset_id: omitSchema,
  prompt_session_id: omitSchema,
  log_id: omitSchema,
});

export const datasetObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z.literal("dataset").default("dataset"),
  experiment_id: omitSchema,
  dataset_id: z.string(),
  prompt_session_id: omitSchema,
  log_id: omitSchema,
});

export const promptSessionObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z.literal("prompt_session").default("prompt_session"),
  experiment_id: omitSchema,
  dataset_id: omitSchema,
  prompt_session_id: z.string(),
  log_id: omitSchema,
});

export const projectLogsObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z.literal("project_logs").default("project_logs"),
  experiment_id: omitSchema,
  dataset_id: omitSchema,
  prompt_session_id: omitSchema,
  project_id: z.string(),
  log_id: projectLogsLogIdLiteralSchema,
});

export const promptObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z.literal("project_prompts").default("project_prompts"),
  experiment_id: omitSchema,
  dataset_id: omitSchema,
  prompt_session_id: omitSchema,
  project_id: z.string(),
  log_id: promptLogIdLiteralSchema,
  function_data: z.object({ type: z.literal("prompt") }).nullish(),
});

export const playgroundLogsObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z.literal("playground_logs").default("playground_logs"),
  experiment_id: omitSchema,
  dataset_id: omitSchema,
  prompt_session_id: z.string(),
  log_id: playgroundLogsLogIdLiteralSchema,
});

export const functionObjectIdsSchema = z.object({
  [OBJECT_TYPE_FIELD]: z
    .literal("project_functions")
    .default("project_functions"),
  experiment_id: omitSchema,
  dataset_id: omitSchema,
  prompt_session_id: omitSchema,
  project_id: z.string(),
  log_id: promptLogIdLiteralSchema,
  function_data: z.record(z.any()).refine((x) => x.type !== "prompt"),
});

export const objectIdsUnionSchema = z.union([
  // First handle the special playground case
  z
    .object({
      prompt_session_id: z.string().refine((id) => id.endsWith(":x")),
      experiment_id: omitSchema,
      dataset_id: omitSchema,
      log_id: omitSchema,
    })
    .transform((x) => ({
      __insert_object_type__: "playground_logs" as const,
      prompt_session_id: x.prompt_session_id.replace(/:x$/, ""),
      log_id: "x",
      experiment_id: null,
      dataset_id: null,
    })),
  experimentObjectIdsSchema,
  datasetObjectIdsSchema,
  promptSessionObjectIdsSchema,
  projectLogsObjectIdsSchema,
  playgroundLogsObjectIdsSchema,
  promptObjectIdsSchema,
  functionObjectIdsSchema,
]);

export type ObjectIdsUnion = z.infer<typeof objectIdsUnionSchema>;

export const INTERNAL_OVERRIDE_TRANSACTION_ID_FIELD =
  "_bt_internal_override_xact_id";
// These control fields are manipulated by the insert routine through the
// process of insertion. We manually separate them from the opaque input row
// object so that most of the business logic can deal solely with the control
// fields. Before inserting, we manually merge any remaining fields back into
// the final row-to-insert.
//
// IMPORTANT: Control fields are processed separately in "merge" updates, so be
// very careful about including fields that are part of the merge process.
//
// IMPORTANT: Select control fields (like `id`) are manually included into the
// final JSON object inserted into the db (for both `logs` and `comments`). If
// you add control fields that need to make it into the final object, make sure
// you add them in `api-ts/src/run_log_data.ts:logPGHelper`.
// Define the input type that matches your schema structure
export const insertControlFieldsSchema = z.object({
  [IS_MERGE_FIELD]: z.preprocess((x) => x ?? false, z.boolean()),
  [MERGE_PATHS_FIELD]: z.preprocess((x) => x ?? [], z.string().array().array()),
  [PARENT_ID_FIELD]: z.preprocess((x) => x ?? null, z.string().nullable()),
  [AUDIT_SOURCE_FIELD]: z.preprocess((x) => x ?? "api", z.string()),
  [AUDIT_METADATA_FIELD]: z.preprocess((x) => x ?? {}, auditLogMetadataSchema),
  [OBJECT_DELETE_FIELD]: z.preprocess((x) => x ?? false, z.boolean()),
  [TRANSACTION_ID_FIELD]: omitSchema,
  [INTERNAL_OVERRIDE_TRANSACTION_ID_FIELD]: z.preprocess(
    (x) => x ?? undefined,
    z.string().optional(),
  ),
  [ASYNC_SCORING_STATE_FIELD]: z.preprocess(
    (x) => x ?? null,
    asyncScoringStateSchema,
  ),
  [ASYNC_SCORING_CONTROL_FIELD]: z.preprocess(
    (x) => x ?? null,
    asyncScoringControlSchema.nullable(),
  ),
  [SKIP_ASYNC_SCORING_FIELD]: z.preprocess((x) => x ?? false, z.boolean()),
  id: z.preprocess((x) => x ?? null, z.string().nullable()),
  created: z.preprocess((x) => x ?? null, datetimeStringSchema.nullable()),
  span_id: z.preprocess((x) => x ?? null, z.string().nullable()),
  span_parents: z.preprocess((x) => x ?? [], z.string().array()),
  root_span_id: z.preprocess((x) => x ?? null, z.string().nullable()),
  org_id: z.preprocess((x) => x ?? null, z.string().nullable()),
  user_id: z.preprocess((x) => x ?? null, z.string().nullable()),
});

// Keep this in sync with the RowRef type in brainstore/storage/src/postgres_wal.rs.
export const rowRefSchema = z.object({
  key: z.string().describe("Key in the object store bucket for this row"),
  byte_range_start: z.number().describe("Start of the byte range for this row"),
  byte_range_end: z.number().describe("End of the byte range for this row"),
});

export type RowRef = z.infer<typeof rowRefSchema>;

// For validating the "opaque" row data, we go for a very lightweight level of
// validation that is fully-specified in the data backend, rather than being
// reliant on types in the global typespecs which may be more specific and
// change over time. We make sure all object schemas are wrapped by
// `objectNullish` so that the validation accommodates merge updates.
export const logEventSchema = objectNullish(
  z.object({
    input: z.unknown(),
    output: z.unknown(),
    expected: z.unknown(),
    tags: z.array(z.string()),
    scores: z.record(z.number().min(0).max(1).nullish()),
    metadata: z
      .object({
        model: z.string().nullish(),
        "~__bt_assignments": z.array(z.string()).nullish(),
      })
      .catchall(z.unknown()),
    // Note that the typespecs signature here is actually more permissive, but we
    // can now validate that all metrics we insert are numbers.
    metrics: z.record(z.number().nullish()),
    context: z.record(z.unknown()),
    span_attributes: z.record(z.unknown()),
    comment: omitSchema,
    prompt_session_data: z.unknown(),
    object_data: z.unknown(),
    completion: z.unknown(),
    prompt_data: strictPromptDataSchema,
    function_data: functionDataSchema,
    origin: objectNullish(objectReferenceSchema),
    [ROW_REF_FIELD]: rowRefSchema,
  }),
);

export const commentEventSchema = z.object({
  origin: auditLogOriginSchema,
  comment: objectNullish(commentSchema),
});
