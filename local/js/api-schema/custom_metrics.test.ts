import { describe, expect, test } from "vitest";
import { defaultMetricDisplayName } from "./custom_metrics";

describe("defaultMetricDisplayName", () => {
  test("replaces underscores with spaces", () => {
    expect(defaultMetricDisplayName("metric_name")).toBe("Metric name");
    expect(defaultMetricDisplayName("total_request_count")).toBe(
      "Total request count",
    );
  });

  test("applies sentence case", () => {
    expect(defaultMetricDisplayName("METRIC_NAME")).toBe("Metric name");
    expect(defaultMetricDisplayName("Total_Request_Count")).toBe(
      "Total request count",
    );
  });

  test("handles camelCase", () => {
    expect(defaultMetricDisplayName("someMetricName")).toBe("Some metric name");
    expect(defaultMetricDisplayName("totalRequestCount")).toBe(
      "Total request count",
    );
  });

  test("capitalizes LLM", () => {
    expect(defaultMetricDisplayName("llm_error_rate")).toBe("LLM error rate");
    expect(defaultMetricDisplayName("total_llm_tokens")).toBe(
      "Total LLM tokens",
    );
    expect(defaultMetricDisplayName("llmErrors")).toBe("LLM errors");
    expect(defaultMetricDisplayName("llm")).toBe("LLM");
  });

  test("handles mixed cases", () => {
    expect(defaultMetricDisplayName("llm_token_count")).toBe("LLM token count");
    expect(defaultMetricDisplayName("avgLLMLatency")).toBe("Avg LLM latency");
    expect(defaultMetricDisplayName("tool_and_llm_errors")).toBe(
      "Tool and LLM errors",
    );
  });

  test("handles edge cases", () => {
    expect(defaultMetricDisplayName("")).toBe("");
    expect(defaultMetricDisplayName("a")).toBe("A");
    expect(defaultMetricDisplayName("_")).toBe(" ");
  });
});
