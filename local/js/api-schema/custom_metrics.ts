import { z } from "zod";

export const metricUnitSchema = z.enum(["percent", "seconds", "dollars"]);

export const metricDefinitionSchema = z.object({
  field_name: z.string(),
  unit: metricUnitSchema.nullish(),
  optimize_dir: z
    .enum(["minimize", "maximize"])
    .nullish()
    .describe(
      "Whether to minimize or maximize this metric (used to compute improvements+regressions).",
    ),
  display_name: z
    .string()
    .nullish()
    .describe("The display name for this metric."),
  btql_definition: z
    .string()
    .nullish()
    .describe(
      "A custom aggregation expression in BTQL syntax to compute this metric.",
    ),
  ignore_if_cached: z
    .boolean()
    .nullish()
    .describe(
      "If true, this metric will not be computed if the data is cached.",
    ),
});

export type MetricDefinition = z.infer<typeof metricDefinitionSchema>;

export const builtInMetrics: MetricDefinition[] = [
  {
    field_name: "time_to_first_token",
    unit: "seconds",
    optimize_dir: "minimize",
  },
  {
    field_name: "llm_calls",
    btql_definition: "SUM(span_attributes.type = 'llm')",
  },
  {
    field_name: "tool_calls",
    btql_definition: "SUM(span_attributes.type = 'tool')",
  },
  {
    field_name: "errors",
    optimize_dir: "minimize",
    btql_definition: "COUNT(error)",
  },
  {
    field_name: "llm_errors",
    optimize_dir: "minimize",
    btql_definition: "COUNT(span_attributes.type = 'llm' ? error : null)",
  },
  {
    field_name: "tool_errors",
    optimize_dir: "minimize",
    btql_definition: "COUNT(span_attributes.type = 'tool' ? error : null)",
  },
  {
    field_name: "prompt_tokens",
    optimize_dir: "minimize",
  },
  {
    field_name: "prompt_cached_tokens",
    optimize_dir: "maximize",
  },
  {
    field_name: "prompt_cache_creation_tokens",
    optimize_dir: "minimize",
  },
  {
    field_name: "completion_tokens",
    optimize_dir: "minimize",
  },
  {
    field_name: "completion_reasoning_tokens",
    optimize_dir: "minimize",
  },
  {
    field_name: "completion_accepted_prediction_tokens",
    optimize_dir: "maximize",
  },
  {
    field_name: "completion_rejected_prediction_tokens",
    optimize_dir: "minimize",
  },
  {
    field_name: "completion_audio_tokens",
    optimize_dir: "minimize",
  },
  {
    field_name: "total_tokens",
    optimize_dir: "minimize",
    btql_definition: "SUM(metrics.tokens)",
  },
  {
    field_name: "estimated_cost",
    optimize_dir: "minimize",
    unit: "dollars",
    btql_definition: null,
    ignore_if_cached: true,
  },
];

export function makeDefaultCustomMetric(fieldName: string): MetricDefinition {
  return { field_name: fieldName };
}

export function defaultMetricDisplayName(fieldName: string) {
  return (
    fieldName
      .replace(/_/g, " ")
      // Handle transitions: lowercase to uppercase, and uppercase followed by lowercase
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/([A-Z]+)([A-Z][a-z])/g, "$1 $2")
      .split(" ")
      .map((word, index) => {
        // Special case for "llm"
        if (word.toLowerCase() === "llm") {
          return "LLM";
        }
        // Sentence case: capitalize first word only
        if (index === 0) {
          return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word.toLowerCase();
      })
      .join(" ")
  );
}

export function metricUnitSuffix(
  unit: z.infer<typeof metricUnitSchema>,
): string {
  switch (unit) {
    case "percent":
      return "%";
    case "seconds":
      return "s";
    case "dollars":
      return "$";
    default:
      const _exhaustiveCheck: never = unit;
      return _exhaustiveCheck;
  }
}
