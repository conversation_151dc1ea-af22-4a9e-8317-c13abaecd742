import { z } from "zod";

export const cloudIdentitySchema = z.object({
  aws_account_id: z.string().nullish().describe("The AWS account ID"),
});

export type CloudIdentity = z.infer<typeof cloudIdentitySchema>;

export const btqlExportJobSchema = z.object({
  type: z.literal("btql_export"),
  cursor: z.string().nullish(),
  last_execution: z
    .object({
      rows: z.number(),
      bytes: z.number(),
      files: z.array(z.string()).default([]),
      error: z.string().nullish(),
      duration_ms: z.number(),
    })
    .default({
      rows: 0,
      bytes: 0,
      files: [],
      error: undefined,
      duration_ms: 0,
    }),
  all_executions: z
    .object({
      rows: z.number(),
      bytes: z.number(),
      duration_ms: z.number(),
    })
    .default({
      rows: 0,
      bytes: 0,
      duration_ms: 0,
    }),
});
export type BtqlExportJobState = z.infer<typeof btqlExportJobSchema>;

export const cronJobSchema = btqlExportJobSchema; // This will eventually be a union
export type CronJobState = z.infer<typeof cronJobSchema>;

export const runCronJobResponseSchema = z.object({
  locked: z.boolean(),
  result: cronJobSchema.nullish(),
});
export type RunCronJobResponse = z.infer<typeof runCronJobResponseSchema>;

export const cronJobStatusResponseSchema = z.object({
  last_executed: z.string().nullish(),
  next_execution: z.string().nullish(),
  state: cronJobSchema,
});
export type CronJobStatusResponse = z.infer<typeof cronJobStatusResponseSchema>;

export function makeExternalId({
  orgId,
  projectId,
  automationExtId,
}: {
  orgId: string;
  projectId: string;
  automationExtId: string;
}) {
  return `bt:${orgId}:${projectId}:${automationExtId}`;
}
