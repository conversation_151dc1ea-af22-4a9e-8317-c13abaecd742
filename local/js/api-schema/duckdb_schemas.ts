import { DuckDBTableDefinition, PhysicalSchema } from "@braintrust/btql/schema";

const baseDuckDBColumns: DuckDBTableDefinition["columns"] = {
  id: { path: ["id"], type: { type: "varchar" } },
  span_id: { path: ["span_id"], type: { type: "varchar" } },
  root_span_id: { path: ["root_span_id"], type: { type: "varchar" } },
  is_root: { path: ["is_root"], type: { type: "boolean" } },
  _xact_id: { path: ["_xact_id"], type: { type: "varchar" } },
  _pagination_key: { path: ["_xact_id"], type: { type: "varchar" } },
  created: { path: ["created"], type: { type: "timestamp" } },
  tags: { path: ["tags"], type: { type: "json" } },
  origin: { path: ["origin"], type: { type: "json" } },
};

const commonLogDuckDBColumns: DuckDBTableDefinition["columns"] = {
  ...baseDuckDBColumns,
  project_id: { path: ["project_id"], type: { type: "varchar" } },
  input: { path: ["input"], type: { type: "json" } },
  output: { path: ["output"], type: { type: "json" } },
  expected: { path: ["expected"], type: { type: "json" } },
  error: { path: ["error"], type: { type: "json" } },
  metadata: { path: ["metadata"], type: { type: "json" } },
  scores: {
    path: ["scores_map"],
    type: { type: "map", key: { type: "varchar" }, value: { type: "double" } },
  },
  metrics: {
    path: ["metrics"],
    type: { type: "struct_map", value: { type: "double" } },
  },
  context: { path: ["context"], type: { type: "json" } },
  span_parents: { path: ["span_parents"], type: { type: "json" } },
  span_attributes: { path: ["span_attributes"], type: { type: "json" } },
  comments: { path: ["comments"], type: { type: "varchar" } },
};

const experimentDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...commonLogDuckDBColumns,
    experiment_id: { path: ["experiment_id"], type: { type: "varchar" } },
    dataset_record_id: {
      path: ["dataset_record_id"],
      type: { type: "varchar" },
    },
  },
};

const playgroundLogsDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...commonLogDuckDBColumns,
    prompt_session_id: {
      path: ["prompt_session_id"],
      type: { type: "varchar" },
    },
    dataset_record_id: {
      path: ["dataset_record_id"],
      type: { type: "varchar" },
    },
  },
};

const logsDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...commonLogDuckDBColumns,
    org_id: { path: ["org_id"], type: { type: "varchar" } },
    log_id: { path: ["log_id"], type: { type: "varchar" } },
  },
};

const projectDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...experimentDuckDBSchema.columns,
    ...logsDuckDBSchema.columns,
  },
};

const datasetDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...baseDuckDBColumns,
    project_id: { path: ["project_id"], type: { type: "varchar" } },
    dataset_id: { path: ["dataset_id"], type: { type: "varchar" } },
    input: { path: ["input"], type: { type: "json" } },
    expected: { path: ["expected"], type: { type: "json" } },
    metadata: { path: ["metadata"], type: { type: "json" } },
  },
};

const promptSessionDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...baseDuckDBColumns,
    prompt_session_id: {
      path: ["prompt_session_id"],
      type: { type: "varchar" },
    },
    prompt_session_data: {
      path: ["prompt_session_data"],
      type: { type: "json" },
    },
    prompt_data: { path: ["prompt_data"], type: { type: "json" } },
    function_data: { path: ["function_data"], type: { type: "json" } },
    object_data: { path: ["object_data"], type: { type: "json" } },
    completion: { path: ["completion"], type: { type: "json" } },
  },
};

const promptDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...baseDuckDBColumns,
    org_id: { path: ["org_id"], type: { type: "varchar" } },
    project_id: { path: ["project_id"], type: { type: "varchar" } },
    log_id: { path: ["log_id"], type: { type: "varchar" } },
    name: { path: ["name"], type: { type: "varchar" } },
    slug: { path: ["slug"], type: { type: "varchar" } },
    description: { path: ["description"], type: { type: "varchar" } },
    prompt_data: { path: ["prompt_data"], type: { type: "json" } },
    metadata: { path: ["metadata"], type: { type: "json" } },
    function_type: { path: ["function_type"], type: { type: "varchar" } },
  },
};

const functionDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    // The "proper" solution is to put the prompt_data inside of function_data, but prompt_data gets
    // stored "outside" of the function_data field in the database, so until we support computed fields,
    // it will be challenging to do that.
    //
    // We should revisit this after we ship that. It won't affect the storage format but will affect
    // all the APIs that use it. We should then also change the physical schemas in api-ts/src/schema.ts
    // and typespecs in app_types.ts
    ...promptDuckDBSchema.columns,
    function_data: { path: ["function_data"], type: { type: "json" } },
    function_schema: {
      path: ["function_schema"],
      type: { type: "json" },
    },
    origin: { path: ["origin"], type: { type: "json" } },
  },
};

export const auditLogDuckDBSchema: DuckDBTableDefinition = {
  columns: {
    ...baseDuckDBColumns,
    project_id: { path: ["project_id"], type: { type: "varchar" } },
    experiment_id: { path: ["experiment_id"], type: { type: "varchar" } },
    origin: { path: ["origin"], type: { type: "json" } },
    source: { path: ["source"], type: { type: "varchar" } },
    metadata: { path: ["metadata"], type: { type: "json" } },
    comment: { path: ["comment"], type: { type: "json" } },
    audit_data: { path: ["audit_data"], type: { type: "json" } },
  },
};

export const DUCKDB_PHYSICAL_SCHEMA: PhysicalSchema = {
  type: "duckdb",
  tables: {
    experiment: experimentDuckDBSchema,
    dataset: datasetDuckDBSchema,
    prompt_session: promptSessionDuckDBSchema,
    playground_logs: playgroundLogsDuckDBSchema,
    project_logs: logsDuckDBSchema,
    project: projectDuckDBSchema,
    project_prompts: promptDuckDBSchema,
    org_prompts: promptDuckDBSchema,
    project_functions: functionDuckDBSchema,
    org_functions: functionDuckDBSchema,
    audit_log: auditLogDuckDBSchema,
  },
};

export type DuckDBTableName = keyof (typeof DUCKDB_PHYSICAL_SCHEMA)["tables"];
