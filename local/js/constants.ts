export const IMPERSONATE_TOKEN_PREFIX = "__impersonate__";
export const BRAINTRUST_AUTH_TOKEN_HEADER = "x-bt-auth-token";
export const ASYNC_SCORING_STATE_FIELD = "_async_scoring_state";
export const WAS_UDF_CACHED_HEADER = "x-bt-was-udf-cached";
export const TESTING_ONLY_KV_INJECT_TIMEOUT_SLEEP_HEADER =
  "x-bt-testing-only-kv-inject-timeout-sleep";
// Same as the ROW_REF_FIELD in brainstore/storage/src/postgres_wal.rs.
export const ROW_REF_FIELD = "_row_ref";
