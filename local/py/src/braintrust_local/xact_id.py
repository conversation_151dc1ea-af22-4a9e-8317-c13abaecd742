import time


# Mirror of the `nextXactId` function defined in api-ts/src/xact_id.ts. Retained
# for scripting convenience.
def next_xact_id():
    r = get_redis_client()
    with r.pipeline() as pipe:
        pipe.time()
        pipe.incr("global-xact-id")
        ((ts, _), xact_id) = pipe.execute()
    ret = (0x0DE1 << 48) | ((ts & 0xFFFFFFFFFFFF) << 16) | (xact_id & 0xFFFF)
    return json.dumps(ret)


def xact_id_from_time_ago(ago_seconds, counter=0):
    """
    Create a TransactionId from a time in the past.

    Args:
        ago_seconds: Number of seconds ago from the current time
        counter: A 16-bit counter value (default 0)

    Returns:
        The transaction ID as an integer

    This mirrors the Rust implementation of TransactionId::from_time_ago.
    """

    # Get current time in seconds since Unix epoch
    now = int(time.time())

    # Calculate the timestamp for the past time
    timestamp = now - int(ago_seconds)

    return ts_to_xact_id(timestamp, counter)


def ts_to_xact_id(timestamp, counter):
    # Ensure counter is within 16-bit range
    counter = counter & 0xFFFF

    # Construct the transaction ID
    # (0x0de1 << 48) | ((timestamp & 0xffffffff) << 16) | counter
    xact_id = (0x0DE1 << 48) | ((timestamp & 0xFFFFFFFF) << 16) | counter

    return xact_id


def xact_id_to_ts(xact_id):
    return (xact_id >> 16) & 0xFFFFFFFF
