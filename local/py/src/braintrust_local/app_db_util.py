"""Utilities for working with the app metadata DB (the supabase one)"""

import os
from functools import cache

import dotenv
import psycopg2


@cache
def get_app_db_url():
    dotenv.load_dotenv(os.path.join(os.path.dirname(__file__), *("../../../../app/.env.development".split("/"))))
    return os.getenv("SUPABASE_PG_URL")


def print_app_db_url():
    print(get_app_db_url())


def _update_foreign_keys_toggle_cascade_delete(enable_cascade):
    """For local testing, it is convenient to set up foreign keys with cascading
    deletes, so that it's easier to clean up data created under temporary orgs. We
    avoid doing this on the prod schema because it can be risky.

    This function allows modify the foreign key constraints of the app DB to
    turn on/off cascading deletes.
    """

    # Get all foreign key constraints.
    GET_FKEY_CONSTRAINTS_QUERY = f"""
    with
    wrapped_constraints as (
    select
        tc.constraint_name,
        pg_constraint.conrelid,
        pg_constraint.conkey,
        pg_constraint.confrelid,
        pg_constraint.confkey
    from
        information_schema.table_constraints tc
        join pg_constraint on tc.constraint_name = pg_constraint.conname
    where
        tc.constraint_schema='public'
        and pg_constraint.contype = 'f'
    ),
    unwrapped_constraints as (
    select
        constraint_name,
        conrelid,
        conkey_colind,
        confrelid,
        confkey_colind,
        constraint_ind
    from
        wrapped_constraints,
        unnest(conkey) with ordinality a(conkey_colind, constraint_ind),
        unnest(confkey) with ordinality b(confkey_colind, fconstraint_ind)
    where
        constraint_ind = fconstraint_ind
    ),
    cleaned_constraints as (
    select
        constraint_name,
        tbl.relname tblname,
        ftbl.relname ftblname,
        tbl_attr.attname colname,
        ftbl_attr.attname fcolname,
        constraint_ind
    from
        unwrapped_constraints
        join pg_class tbl on conrelid = tbl.oid
        join pg_class ftbl on confrelid = ftbl.oid
        join pg_attribute tbl_attr on conrelid = tbl_attr.attrelid and conkey_colind = tbl_attr.attnum
        join pg_attribute ftbl_attr on confrelid = ftbl_attr.attrelid and confkey_colind = ftbl_attr.attnum
    ),
    grouped_constraints as (
    select
        constraint_name,
        tblname,
        array_agg(colname order by constraint_ind) cols,
        ftblname,
        array_agg(fcolname order by constraint_ind) fcols
    from
        cleaned_constraints
    group by
        constraint_name, tblname, ftblname
    )
    select * from grouped_constraints
    """

    if enable_cascade:
        cascade_str = "on delete cascade"
    else:
        cascade_str = ""

    with psycopg2.connect(get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute(GET_FKEY_CONSTRAINTS_QUERY)
            # For each constraint, drop it, and then re-add it with/without
            # cascading delete.
            for (
                constraint_name,
                tblname,
                cols,
                ftblname,
                fcols,
            ) in cursor.fetchall():
                cursor.execute(f"alter table {tblname} drop constraint {constraint_name}")
                cursor.execute(
                    f"""
                    alter table {tblname} add constraint {constraint_name}
                        foreign key ({','.join(cols)})
                        references {ftblname} ({','.join(fcols)})
                        {cascade_str}
                """
                )


def update_foreign_keys_enable_cascade_delete():
    return _update_foreign_keys_toggle_cascade_delete(enable_cascade=True)


def update_foreign_keys_disable_cascade_delete():
    return _update_foreign_keys_toggle_cascade_delete(enable_cascade=False)
