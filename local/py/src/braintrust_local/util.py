import sys
from typing import Any, Optional, TypeVar

T = TypeVar("T")


def ensure_not_none(value: Optional[T]) -> T:
    if value is None:
        raise ValueError("Expected value to be not None")
    return value


# Taken from
# https://stackoverflow.com/questions/5574702/how-do-i-print-to-stderr-in-python.
def eprint(*args, **kwargs) -> None:
    print(*args, file=sys.stderr, **kwargs)


def get_from_path(obj: dict, path: str, default: Any = None) -> Any:
    try:
        parts = path.split(".")
        for part in parts:
            obj = obj[part]
        return obj
    except (KeyError, TypeError, AttributeError):
        return default
