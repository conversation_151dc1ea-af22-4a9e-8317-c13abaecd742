#!/usr/bin/env python3
import os
import sys

import boto3
from botocore.exceptions import ClientError


def list_ecs_clusters():
    """List all ECS clusters and return them."""
    ecs = boto3.client("ecs")
    try:
        response = ecs.list_clusters()
        clusters = response["clusterArns"]

        if not clusters:
            print("No ECS clusters found.")
            sys.exit(1)

        # Extract cluster names from ARNs
        cluster_names = [arn.split("/")[-1] for arn in clusters]
        return cluster_names
    except ClientError as e:
        print(f"Error listing ECS clusters: {e}")
        sys.exit(1)


def list_ecs_services(cluster_name):
    """List all services in an ECS cluster and return them."""
    ecs = boto3.client("ecs")
    try:
        response = ecs.list_services(cluster=cluster_name)
        services = response["serviceArns"]

        if not services:
            print(f"No services found in cluster {cluster_name}.")
            sys.exit(1)

        # Extract service names from ARNs
        service_names = [arn.split("/")[-1] for arn in services]
        return service_names
    except ClientError as e:
        print(f"Error listing ECS services: {e}")
        sys.exit(1)


def get_running_task(cluster_name, service_name):
    """Get the ARN of a running task for the specified service."""
    ecs = boto3.client("ecs")
    try:
        response = ecs.list_tasks(cluster=cluster_name, serviceName=service_name, desiredStatus="RUNNING")

        tasks = response["taskArns"]

        if not tasks:
            print(f"No running tasks found for service {service_name} in cluster {cluster_name}.")
            sys.exit(1)

        return tasks[0]  # Return the first running task
    except ClientError as e:
        print(f"Error getting running task: {e}")
        sys.exit(1)


def get_task_containers(cluster_name, task_arn):
    """Get the containers for a specific task."""
    ecs = boto3.client("ecs")
    try:
        response = ecs.describe_tasks(cluster=cluster_name, tasks=[task_arn])

        if not response["tasks"]:
            print(f"Task {task_arn} not found.")
            sys.exit(1)

        task = response["tasks"][0]
        containers = task.get("containers", [])

        if not containers:
            print(f"No containers found in task {task_arn}.")
            sys.exit(1)

        # Return container names
        container_names = [container["name"] for container in containers]
        return container_names
    except ClientError as e:
        print(f"Error getting task containers: {e}")
        sys.exit(1)


def interactive_select_container(containers):
    """Interactively select a container from a list."""
    if len(containers) == 1:
        return containers[0]

    print(f"\nAvailable containers:")
    for i, container in enumerate(containers, 1):
        print(f"  {i}. {container}")

    while True:
        try:
            choice = input(f"\nSelect container (1-{len(containers)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(containers):
                return containers[index]
            else:
                print(f"Please enter a number between 1 and {len(containers)}")
        except ValueError:
            print("Please enter a valid number")


def get_container_name(cluster_name, service_name, container_name=None):
    """Get container name, either from argument or interactive selection."""
    # Get the task ARN
    task_arn = get_running_task(cluster_name, service_name)
    print(f"Found task: {task_arn}")

    # Get containers for the task
    containers = get_task_containers(cluster_name, task_arn)

    if container_name is None:
        if len(containers) == 1:
            container_name = containers[0]
        else:
            container_name = interactive_select_container(containers)
    else:
        if container_name not in containers:
            print(f"Container '{container_name}' not found.")
            container_name = interactive_select_container(containers)

    return container_name


def connect_to_container(cluster_name, service_name, container_name):
    """Connect to an ECS container using ECS Exec."""
    print(f"Connecting to ECS container...")

    # Get the task ARN
    task_arn = get_running_task(cluster_name, service_name)

    print(f"Connecting to container: {container_name}")

    # Execute interactive shell
    cmd = [
        "aws",
        "ecs",
        "execute-command",
        "--cluster",
        cluster_name,
        "--task",
        task_arn,
        "--container",
        container_name,
        "--interactive",
        "--command",
        "/bin/bash",
    ]

    try:
        os.execvp("aws", cmd)
    except FileNotFoundError:
        print("AWS CLI not found. Please install AWS CLI.")
        sys.exit(1)


def interactive_select_cluster():
    """Interactively select an ECS cluster."""
    clusters = list_ecs_clusters()

    print("Available ECS clusters:")
    for i, cluster in enumerate(clusters, 1):
        print(f"  {i}. {cluster}")

    while True:
        try:
            choice = input(f"\nSelect cluster (1-{len(clusters)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(clusters):
                return clusters[index]
            else:
                print(f"Please enter a number between 1 and {len(clusters)}")
        except ValueError:
            print("Please enter a valid number")


def interactive_select_service(cluster_name):
    """Interactively select an ECS service."""
    services = list_ecs_services(cluster_name)

    print(f"\nAvailable services in cluster '{cluster_name}':")
    for i, service in enumerate(services, 1):
        print(f"  {i}. {service}")

    while True:
        try:
            choice = input(f"\nSelect service (1-{len(services)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(services):
                return services[index]
            else:
                print(f"Please enter a number between 1 and {len(services)}")
        except ValueError:
            print("Please enter a valid number")


def main():
    interactive_run = False

    # Parse arguments
    cluster_name = None
    service_name = None
    container_name = None

    if len(sys.argv) >= 2:
        cluster_name = sys.argv[1]
    if len(sys.argv) >= 3:
        service_name = sys.argv[2]
    if len(sys.argv) >= 4:
        container_name = sys.argv[3]

    # Interactive selection for missing arguments
    if cluster_name is None:
        cluster_name = interactive_select_cluster()
        interactive_run = True

    if service_name is None:
        service_name = interactive_select_service(cluster_name)
        interactive_run = True

    # Get container name (may trigger interactive selection)
    container_name = get_container_name(cluster_name, service_name, container_name)
    if container_name != (sys.argv[3] if len(sys.argv) >= 4 else None):
        interactive_run = True

    # Show the command that can be used for direct connection only after interactive runs
    if interactive_run:
        print(f"\nTo connect directly in the future, run:")
        print(f"  ecs-connect {cluster_name} {service_name} {container_name}")
        print()

    connect_to_container(cluster_name, service_name, container_name)


if __name__ == "__main__":
    main()
