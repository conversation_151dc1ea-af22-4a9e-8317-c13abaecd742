import dataclasses
import json
import os

SCRIPT_DIR = os.path.dirname(__file__)


@dataclasses.dataclass
class TestProxyServerConfig:
    host: str
    port: int
    cache_path: str


_test_proxy_server_config = None


def get_test_proxy_server_config():
    global _test_proxy_server_config

    if _test_proxy_server_config is not None:
        return _test_proxy_server_config
    config_path = os.path.join(SCRIPT_DIR, *("../../../../test-proxy/server_config.json").split("/"))
    with open(config_path, "rb") as f:
        contents = json.load(f)
    _test_proxy_server_config = TestProxyServerConfig(
        host=contents["host"],
        port=contents["port"],
        cache_path=os.path.join(os.path.dirname(config_path), contents["relCachePath"]),
    )
    return _test_proxy_server_config
