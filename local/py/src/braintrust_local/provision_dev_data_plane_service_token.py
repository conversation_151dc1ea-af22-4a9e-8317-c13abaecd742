#!/usr/bin/env python3

# This script is used to provision and upsert a sysadmin service token in a dev environment.
# If BRAINTRUST_API_KEY is not set, we create a sysadmin API key.

# Using the key from above we check if the data plane service token already exists.
# If it doesn't, we force-provision a new data plane manager API key and
# upsert it into the `service_tokens` table.
#
# Usage:
#
# provision-dev-data-plane-service-token

import argparse
import logging
import os
import sys
from uuid import uuid4

import psycopg2
import requests
from braintrust_local import app_db_util
from braintrust_local.constants import BT_DATA_PLANE_SERVICE_TOKEN_NAME
from braintrust_local.util import ensure_not_none

logger = logging.getLogger("provision_dev_data_plane_service_token")

LOCAL_API_URL = os.environ.get("BRAINTRUST_API_URL", "http://localhost:8000")
LOCAL_APP_URL = os.environ.get("BRAINTRUST_APP_URL", "http://localhost:3000")

# from app/supabase/seed.sql
SYSADMIN_USER_EMAIL = "<EMAIL>"
SYSADMIN_USER_ID = "fac36c53-c882-458b-bf80-60d06c3e8a0d"
ORG_ID = "abb9f3e4-7fdd-4ccc-af40-f7e894fd4125"


def create_sysadmin_api_key():
    """Generate a temporary api key for the sysadmin user"""
    logger.info("Creating temporary API key for sysadmin user...")

    name = f"__unit_test_sysadmin_temp_api_key-{str(uuid4())}"
    with psycopg2.connect(app_db_util.get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            # Create API key
            cursor.execute(
                "SELECT create_api_key(users.auth_id, %s, %s) FROM users WHERE id=%s",
                (ORG_ID, name, SYSADMIN_USER_ID),
            )
            api_key = ensure_not_none(cursor.fetchone())[0]

            logger.info(f"Created sysadmin API key")
            return api_key, name


def check_is_sysadmin(api_key):
    """Check if the user associated with the API key is a sysadmin"""
    logger.debug("Checking if user is sysadmin...")

    try:
        response = requests.post(
            f"{LOCAL_API_URL}/api/self/me",
            headers={"Authorization": f"Bearer {api_key}"},
            json={"check_sysadmin": True},
        )
        if response.status_code == 200:
            data = response.json()
            return data.get("is_sysadmin", False)
        else:
            logger.debug(f"Failed to check sysadmin status: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"Error checking sysadmin status: {e}")
        return False


def check_service_token_exists(api_key):
    """Check if the service token already exists"""
    logger.debug("Checking if service token exists...")

    try:
        response = requests.head(
            f"{LOCAL_API_URL}/service-token/{BT_DATA_PLANE_SERVICE_TOKEN_NAME}",
            headers={"Authorization": f"Bearer {api_key}"},
        )
        exists = response.status_code == 200
        return exists
    except Exception as e:
        logger.error(f"Error checking service token: {e}")
        return False


def provision_data_plane_manager(api_key):
    """Provision the data plane manager and get the service token (force recreate to get actual key)"""
    logger.debug("Provisioning data plane manager with force_recreate_token=True")

    response = requests.post(
        f"{LOCAL_APP_URL}/api/service_token/provision_data_plane_manager",
        headers={
            "Authorization": f"Bearer {api_key}",
            "Content-type": "application/json",
        },
        json={
            "org_id": ORG_ID,
            "force_recreate_token": True,
        },
    )

    if not response.ok:
        raise Exception(f"Failed to provision data plane manager: {response.text}")

    data = response.json()
    service_token_key = data["data_plane_service_token"]["key"]

    if service_token_key is None:
        raise Exception("Expected request to contain non-null service token key when using force_recreate_token=True")

    logger.debug("Successfully provisioned data plane manager")
    return service_token_key


def upsert_service_token(api_key, service_token_key):
    """Upsert the service token into the service tokens table"""

    response = requests.post(
        f"{LOCAL_API_URL}/service-token/upsert",
        json={
            "name": BT_DATA_PLANE_SERVICE_TOKEN_NAME,
            "service_token": service_token_key,
        },
        headers={"Authorization": f"Bearer {api_key}"},
    )

    if not response.ok:
        raise Exception(f"Failed to upsert service token: {response.text}")

    logger.info(f"Successfully provisioned service token {BT_DATA_PLANE_SERVICE_TOKEN_NAME}: {api_key}")


def cleanup_sysadmin_api_key(name):
    """Clean up the temporary sysadmin API key"""
    logger.info(f"Cleaning up temporary api key: {name}")

    with psycopg2.connect(app_db_util.get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "DELETE FROM api_keys WHERE org_id = %s AND user_id = %s AND name = %s",
                (ORG_ID, SYSADMIN_USER_ID, name),
            )

    logger.info("Cleanup completed")


def provision_data_plane_service_token(force=False, log_level=logging.ERROR):
    """Main provisioning logic that can be called programmatically."""
    logging.basicConfig(level=log_level, format="%(levelname)s: %(message)s")

    cleanup_api_key = None
    api_key = None
    try:
        if "BRAINTRUST_API_KEY" in os.environ:
            provided_api_key = os.environ["BRAINTRUST_API_KEY"]
            if check_is_sysadmin(provided_api_key):
                logger.info("Using provided API key (user is sysadmin)")
                api_key = provided_api_key

        if not api_key:
            logger.info("Creating temporary sysadmin API key")
            api_key, api_key_name = create_sysadmin_api_key()
            cleanup_api_key = api_key_name
        if check_service_token_exists(api_key):
            if not force:
                logger.info(
                    "Service token already exists in data plane, skipping provisioning. Use --force to recreate."
                )
                return
            else:
                logger.info("Service token already exists in data plane, force recreating")
        else:
            logger.info("Service token does not exist in data plane")

        # Provision the data plane manager with force_recreate_token=True to force creation
        # of a new API key.
        service_token_key = provision_data_plane_manager(api_key)

        # Upsert the new key into the `service_tokens` table.
        upsert_service_token(api_key, service_token_key)
    except Exception as e:
        logger.error(f"{e}")
        sys.exit(1)
    finally:
        # Clean up temporary api key
        if cleanup_api_key:
            try:
                cleanup_sysadmin_api_key(cleanup_api_key)
            except Exception as e:
                logger.warning(f"Failed to cleanup api key {cleanup_api_key}: {e}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--force", action="store_true", help="Overwrite any existing service token stored in the data plane"
    )

    args = parser.parse_args()
    provision_data_plane_service_token(force=args.force, log_level=logging.INFO)


if __name__ == "__main__":
    main()
