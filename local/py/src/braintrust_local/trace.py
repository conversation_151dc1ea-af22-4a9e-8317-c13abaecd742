import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from braintrust.logger import <PERSON><PERSON><PERSON><PERSON>ttachment
from braintrust.prompt import PromptBlockData, PromptChatBlock, PromptData
from braintrust.serializable_data_class import SerializableDataClass


@dataclass
class SpanAttributes(SerializableDataClass):
    name: str
    type: Optional[str] = None


@dataclass
class Span(SerializableDataClass):
    id: str
    created: Any
    span_id: str
    root_span_id: str
    is_root: Optional[bool] = None
    span_parents: Optional[List[str]] = None
    span_attributes: SpanAttributes = None
    project_id: str = ""
    log_id: str = ""
    input: Any = None
    output: Any = None
    metadata: Dict[str, Any] = None
    metrics: Dict[str, float] = None


def parse_prompt_from_span(span: Span) -> Optional[PromptData]:
    try:
        if span.span_attributes and span.span_attributes.type == "llm":
            return parse_prompt_span_data(span)
    except Exception as e:
        print("Error parsing prompt from span", e)
    return None


# Keep this aligned with the keys of `modelParamToModelParam` in
# proxy/packages/proxy/schema/index.ts
KNOWN_PARAMS = {
    "frequency_penalty",
    "function_call",
    "logprobs",
    "max_tokens_to_sample",
    "max_tokens",
    "maxOutputTokens",
    "n",
    "parallel_tool_calls",
    "presence_penalty",
    "reasoning_effort",
    "stream_options",
    "temperature",
    "tool_choice",
    "top_k",
    "top_p",
    "topK",
    "topP",
    "use_cache",
    "user",
}


def parse_prompt_span_data(span: Span) -> PromptData:
    input_data = json.loads(span.input) if isinstance(span.input, str) else span.input
    metadata = json.loads(span.metadata) if isinstance(span.metadata, str) else span.metadata

    model = metadata.get("model")
    functions_arg = metadata.get("functions")
    function_call_arg = metadata.get("function_call")
    tools_arg = metadata.get("tools")
    tool_choice_arg = metadata.get("tool_choice")

    params = {k: v for k, v in metadata.items() if k in KNOWN_PARAMS}

    if functions_arg and tools_arg:
        raise Exception("Unsupported: both functions and tools set")
    if function_call_arg and tool_choice_arg:
        raise Exception("Unsupported: both function_call and tool_choice set")

    if functions_arg:
        tools = [{"type": "function", "function": f} for f in functions_arg]
    else:
        tools = tools_arg

    if function_call_arg:
        tool_choice = {"function": function_call_arg["name"], "type": "function"}
    else:
        tool_choice = tool_choice_arg

    prompt = PromptChatBlock(
        type="chat",
        messages=input_data,
        tools=json.dumps(tools, indent=2) if tools else None,
    )

    if tool_choice:
        params["tool_choice"] = tool_choice

    options = dict(model=model, params=params)

    return PromptData(prompt=prompt, options=options)


def span_to_prompt(span: Span) -> Dict:
    prompt_data = parse_prompt_from_span(span)
    if not prompt_data:
        return None

    prompt = prompt_data.prompt
    options = prompt_data.options
    if not prompt:
        return None

    model = options.get("model")
    bt_params = options.get("params") or {}
    bt_params.pop("use_cache", None)
    params = bt_params

    if prompt.type == "chat":
        resolve_attachments_in_messages(prompt.messages)

    prompt_value = prompt.content if prompt.type == "completion" else prompt.messages

    prompt_arg = {"prompt": prompt_value} if prompt.type == "completion" else {"messages": prompt_value}

    tools = None
    if getattr(prompt, "tools", None):
        try:
            tools = json.loads(prompt.tools)
        except Exception as e:
            raise Exception(f"Invalid tools JSON: {e}")

    result = {
        **prompt_arg,
        "model": model,
        **(params or {}),
    }
    if tools:
        result["tools"] = tools

    return result


def resolve_attachments_in_messages(messages: List[Dict]):
    for message in messages:
        if isinstance(message.get("content"), list):
            for part in message["content"]:
                if part.get("type") == "image_url" and isinstance(part["image_url"]["url"], dict):
                    attachment = ReadonlyAttachment(part["image_url"]["url"])
                    part["image_url"]["url"] = str(attachment)
