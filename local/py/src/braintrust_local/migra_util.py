import sys
from io import <PERSON><PERSON>
from urllib.parse import urlparse

import migra
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT


# https://stackoverflow.com/questions/16571150/how-to-capture-stdout-output-from-a-python-function-call
class Capturing(list):
    def __enter__(self):
        self._stdout = sys.stdout
        sys.stdout = self._stringio = StringIO()
        return self

    def __exit__(self, *args):
        self.extend(self._stringio.getvalue().splitlines())
        del self._stringio  # free up some memory
        sys.stdout = self._stdout


def to_staging_db(pg_url, migration_db_name):
    return urlparse(pg_url)._replace(path=f"/{migration_db_name}").geturl()


def create_migration_db(pg_url, migration_db_name):
    conn = psycopg2.connect(pg_url)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    conn.autocommit = True

    # Make sure the migration DB doesn't exist already.
    cursor.execute("select 1 from pg_catalog.pg_database where datname=%s", [migration_db_name])
    if cursor.fetchone():
        raise Exception(
            f"DB {migration_db_name} already exists. Please delete it or specify a different name to use for migration staging"
        )

    cursor.execute(f"CREATE DATABASE {migration_db_name}")
    cursor.close()
    conn.close()


def construct_migration_db(pg_url, migration_db_name, schema_file):
    staging_url = to_staging_db(pg_url, migration_db_name)
    with open(schema_file, "r") as f:
        schema_file_contents = f.read()
    with psycopg2.connect(staging_url) as staging_conn:
        with staging_conn.cursor() as cursor:
            cursor.execute(schema_file_contents)


def drop_migration_db(pg_url, migration_db_name):
    conn = psycopg2.connect(pg_url)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    conn.autocommit = True
    cursor.execute(f"DROP DATABASE IF EXISTS {migration_db_name} WITH (FORCE)")
    cursor.close()
    conn.close()


def _to_migra_url(s):
    return urlparse(s)._replace(scheme="postgresql").geturl()


def compute_schema_migration(
    pg_url,
    schema_file,
    migration_db_name,
    extra_migra_args=[],
    cleanup_migration_db=True,
):
    """
    Computes a migration from a live postgres DB at `pg_url` to the schema
    described by `schema_file`. The `schema_file` schema is instantiated into a
    temporary database named by `migration_db_name` into the live postgres DB
    and then dropped.
    """

    create_migration_db(pg_url, migration_db_name)
    try:
        construct_migration_db(pg_url, migration_db_name, schema_file)
        args = migra.command.parse_args(
            [
                _to_migra_url(pg_url),
                _to_migra_url(to_staging_db(pg_url, migration_db_name)),
            ]
            + extra_migra_args
        )
        with Capturing() as output:
            migra.command.run(args)
        return ("\n".join(output)).strip()
    finally:
        if cleanup_migration_db:
            drop_migration_db(pg_url, migration_db_name)
