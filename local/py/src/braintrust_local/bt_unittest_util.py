import json
import os
import sqlite3

import psutil

SCRIPT_DIR = os.path.dirname(__file__)
ARGS_DB_NAME = os.path.join(SCRIPT_DIR, ".bt_unittest_args.db")


def store_args_for_pid(pid, args):
    conn = sqlite3.connect(ARGS_DB_NAME)
    try:
        with conn:
            conn.execute("create table if not exists proc_args(pid integer primary key, args varchar)")
            conn.execute("insert into proc_args(pid, args) values (?, ?)", (pid, json.dumps(args)))
    finally:
        conn.close()


def remove_args_for_pid(pid):
    conn = sqlite3.connect(ARGS_DB_NAME)
    try:
        with conn:
            conn.execute("create table if not exists proc_args(pid integer primary key, args varchar)")
            conn.execute("delete from proc_args where pid = ?", (pid,))
    finally:
        conn.close()


def get_args_for_pid(pid):
    pids = [pid]
    while True:
        last_pid = pids[-1]
        ppid = psutil.Process(last_pid).ppid()
        if ppid == 0 or ppid == last_pid:
            break
        pids.append(ppid)

    conn = sqlite3.connect(ARGS_DB_NAME)
    try:
        with conn:
            cur = conn.cursor()
            cur.execute("create table if not exists proc_args(pid integer primary key, args varchar)")
            value_str = ", ".join(["?"] * len(pids))
            cur.execute(f"select pid, args from proc_args where pid in ({value_str})", pids)
            results = cur.fetchall()
        results = {pid: args for pid, args in results}
        for pid in pids:
            if pid in results:
                return json.loads(results[pid])
        return {}
    finally:
        conn.close()
