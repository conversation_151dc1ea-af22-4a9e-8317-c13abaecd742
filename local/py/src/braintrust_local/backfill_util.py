import time

import braintrust


# Note that we don't actually need to run the catch up in order to query the
# latest data for an object in brainstore, because we will immediately start
# inserting records for new objects into the object's realtime WAL.
#
# But the catchup can still be useful for testing the backfill logic
# specifically. This function ensures that all objects have been backfilled
# up to the point of the last-snapshotted max sequence id in the logs table.
def catchup_brainstore_backfill(backfill_args):
    logs_max_sequence_id = None
    logs2_max_sequence_id = None
    for _ in range(1000):
        d = braintrust.api_conn().get_json("brainstore/backfill/status")
        if logs_max_sequence_id is None:
            logs_max_sequence_id = d["logs_max_sequence_id"]
        if logs2_max_sequence_id is None:
            logs2_max_sequence_id = d["logs2_max_sequence_id"]
        if (
            d["all_backfilled"]
            and d["backfill_frontier_sequence_id"] >= logs_max_sequence_id
            and d["backfill_frontier_sequence_id_2"] >= logs2_max_sequence_id
            and d["historical_backfill_progress"]
            and d["historical_backfill_progress"] >= 1
        ):
            break
        resp = braintrust.api_conn().post_json("brainstore/backfill/run", args=backfill_args)
        if not resp["locked"]:
            time.sleep(0.1)
    else:
        raise Exception("Brainstore ETL did not catch up")
