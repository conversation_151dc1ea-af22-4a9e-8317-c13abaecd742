# Utilities for checking whether a service is up.

import time

import requests


def check_get(url, request_timeout=None) -> bool:
    try:
        requests.get(url, timeout=request_timeout)
        return True
    except (requests.ConnectionError, requests.ReadTimeout):
        return False


def check_get_retry_max_time(url, max_time, sleep_time, request_timeout=None) -> bool:
    start = time.time()
    while time.time() - start < max_time:
        if check_get(url, request_timeout=request_timeout):
            return True
        time.sleep(sleep_time)
    return False
