import argparse
import os
import os.path
import sys

from braintrust_local.api_db_util import get_api_db_url
from braintrust_local.app_db_util import get_app_db_url
from braintrust_local.migra_util import compute_schema_migration, drop_migration_db

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
MIGRATION_DB_NAME = "migration_staging"


def generate(pg_url, schema_file, skip_lines=None, drop_migration_db_if_exists=False, cleanup_migration_db=True):
    if drop_migration_db_if_exists:
        drop_migration_db(pg_url, MIGRATION_DB_NAME)

    print("Generating schema migration:", file=sys.stderr)
    extra_migra_args = ["--schema", "public", "--unsafe"]
    sql = compute_schema_migration(
        pg_url=pg_url,
        schema_file=schema_file,
        migration_db_name=MIGRATION_DB_NAME,
        extra_migra_args=extra_migra_args,
        cleanup_migration_db=cleanup_migration_db,
    )

    if skip_lines is not None:
        skip_lines_set = set(skip_lines)
        sql = "\n".join(l for l in sql.split("\n") if l.strip() not in skip_lines_set)
    sql = sql.strip()
    if sql:
        print(sql)


def main():
    parser = argparse.ArgumentParser(
        prog="Generate migration",
        description=f"Migration tool for various postgres schemas. The tool auto-generates a migration using the 'migra' utility, comparing an existing DB to the schema defined in a '.sql' file",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "db",
        choices=["api", "app"],
        help="Database to migrate. 'api' is the backend data DB. 'app' is the metadata DB.",
    )
    parser.add_argument("--override-pg-url", help="Use the specified postgres url")
    parser.add_argument("--override-schema-file", help="Use the specified schema file")
    parser.add_argument(
        "--drop-migration-db-if-exists",
        action="store_true",
        help=f"If provided, drop any existing DB named {MIGRATION_DB_NAME} before generating",
    )
    parser.add_argument(
        "--nocleanup-migration-db",
        default=False,
        action="store_true",
        help=f"If provided, leave the migration db intact after generating. Otherwise, it is dropped",
    )

    args = parser.parse_args()

    if args.db == "api":
        pg_url = get_api_db_url()
        schema_file = os.path.join(SCRIPT_DIR, *("../../../../api-schema/schema.sql".split("/")))
        skip_lines = ['drop extension if exists "pg_cron";']
    else:
        assert args.db == "app"
        pg_url = get_app_db_url()
        schema_file = os.path.join(SCRIPT_DIR, *("../../../../app/supabase/schema.sql".split("/")))
        skip_lines = None

    if args.override_pg_url:
        pg_url = args.override_pg_url
    if args.override_schema_file:
        schema_file = args.override_schema_file

    generate(
        pg_url=pg_url,
        schema_file=schema_file,
        skip_lines=skip_lines,
        drop_migration_db_if_exists=args.drop_migration_db_if_exists,
        cleanup_migration_db=not args.nocleanup_migration_db,
    )
