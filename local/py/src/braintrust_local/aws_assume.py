#!/usr/bin/env python3
"""
Usage: assume [--profile AWS_PROFILE] <ROLE_ARN> <REGION>

This script assumes the given AWS role and sets temporary credentials
in the environment. This makes it easier to assume roles that customers
have shared for debugging their AWS environment.

This will be made available as `assume` in your environment if you have your
venv activated.
"""

import argparse
import json
import os
import subprocess
import sys

import boto3


def assume_role(role_arn: str, aws_region: str, profile: str | None = None) -> None:
    # Create STS client
    session = boto3.Session(profile_name=profile) if profile else boto3.Session()
    sts_client = session.client("sts", region_name=aws_region)

    try:
        # Assume the role
        response = sts_client.assume_role(RoleArn=role_arn, RoleSessionName="BraintrustSupport")
    except Exception as e:
        print(f"Error assuming role: {str(e)}", file=sys.stderr)
        sys.exit(1)

    # Set environment variables
    credentials = response["Credentials"]
    os.environ["AWS_ACCESS_KEY_ID"] = credentials["AccessKeyId"]
    os.environ["AWS_SECRET_ACCESS_KEY"] = credentials["SecretAccessKey"]
    os.environ["AWS_SESSION_TOKEN"] = credentials["SessionToken"]
    os.environ["AWS_DEFAULT_REGION"] = aws_region
    os.environ["AWS_REGION"] = aws_region
    # Useful flag to check for in shell prompts
    os.environ["IN_ASSUMED_ROLE"] = "true"

    role_name = role_arn.split("/")[-1]
    print("Successfully assumed role!")
    print(f"AWS Role:   {role_name}")
    print(f"AWS Region: {aws_region}")
    print("Spawning a new shell with the credentials set in the environment...")
    print("(type exit to return to your original shell)")

    # Start a new shell with the credentials
    shell = os.environ.get("SHELL", "/bin/zsh")
    os.execvp(shell, [shell])


def get_instance_az(instance_id: str) -> str:
    """
    Run the 'list-instances.sh --json' command and parse the output to get the AvailabilityZone for the given instance_id.
    """
    try:
        result = subprocess.check_output(["./list-instances.sh", "--json"], text=True)
        data = json.loads(result)
    except subprocess.CalledProcessError as e:
        print("Failed to get JSON output from list-instances.sh:", e)
        sys.exit(1)
    except json.JSONDecodeError as e:
        print("Failed to decode JSON output:", e)
        sys.exit(1)

    # Search in the JSON structure for the instance and retrieve its AvailabilityZone.
    for group in data.get("AutoScalingGroups", []):
        for instance in group.get("Instances", []):
            if instance.get("InstanceId") == instance_id:
                return instance.get("AvailabilityZone")
    print(f"Instance with ID {instance_id} not found in the JSON output.")
    sys.exit(1)


def send_ssh_public_key(instance_id: str, az: str, os_user: str) -> None:
    """
    Sends the SSH public key to the instance using aws ec2-instance-connect send-ssh-public-key.
    """
    print(f"Sending SSH key to instance {os_user}@{instance_id}")
    try:
        subprocess.run(
            [
                "aws",
                "ec2-instance-connect",
                "send-ssh-public-key",
                "--instance-id",
                instance_id,
                "--instance-os-user",
                os_user,
                "--ssh-public-key",
                "file:///home/<USER>/.ssh/id_rsa.pub",
                "--availability-zone",
                az,
            ],
            check=True,
        )
    except subprocess.CalledProcessError as e:
        print("Error sending SSH public key:", e)
        sys.exit(1)


def connect_to_instance(instance_id: str, os_user: str) -> None:
    """
    Connects to the instance using aws ec2-instance-connect ssh.
    """
    print(f"Connecting to instance {os_user}@{instance_id}")
    key_file = f"file://{os.path.expanduser('~')}/.ssh/id_rsa"
    try:
        subprocess.run(
            [
                "aws",
                "ec2-instance-connect",
                "ssh",
                "--instance-id",
                instance_id,
                "--os-user",
                os_user,
                "--private-key-file",
                key_file,
            ],
            check=True,
        )
    except subprocess.CalledProcessError as e:
        print("Error connecting via SSH:", e)
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Assume an AWS role and start a shell with temporary credentials.",
        usage="assume [--profile PROFILE] ROLE_ARN REGION",
    )
    parser.add_argument("--profile", help="AWS profile to use for assuming the role")
    parser.add_argument("role_arn", help="The ARN of the role to assume")
    parser.add_argument("region", help="The AWS region to use")

    args = parser.parse_args()
    assume_role(args.role_arn, args.region, args.profile)

    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} INSTANCE_ID")
        sys.exit(1)

    instance_id = sys.argv[1]
    os_user = "ubuntu"

    # Retrieve the availability zone for this instance.
    az = get_instance_az(instance_id)

    # Send the SSH public key to the target instance.
    send_ssh_public_key(instance_id, az, os_user)

    # Connect to the instance via EC2 Instance Connect.
    connect_to_instance(instance_id, os_user)


if __name__ == "__main__":
    main()
