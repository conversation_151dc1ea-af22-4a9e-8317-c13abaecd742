"""
Usage: btapi btql 'query'

Logs into Braintrust (using your local environment) and runs API commands.
"""

import argparse
import json
import logging
import pprint
import sys

import braintrust
from braintrust.logger import api_conn
from tabulate import tabulate


def run_get(args):
    payload = json.loads(args.payload[0]) if len(args.payload) > 0 else None
    response = api_conn().get_json(args.path, payload)
    print(json.dumps(response, indent=2))


def run_post(args):
    payload = json.loads(args.payload[0]) if len(args.payload) > 0 else None
    response = api_conn().post_json(args.path, payload)
    print(json.dumps(response, indent=2))


def btql_query(args):
    request_args = {"query": args.query, "fmt": "json"}

    if args.audit_log:
        request_args["audit_log"] = True

    if args.disable_limit:
        request_args["disable_limit"] = True

    if args.explain:
        request_args["_debug_explain"] = True

    if args.columnstore:
        request_args["use_columnstore"] = True
    elif args.postgres:
        request_args["use_columnstore"] = False
    elif args.duckdb:
        request_args["use_duckdb"] = True
    elif args.brainstore:
        request_args["use_brainstore"] = True
        request_args["brainstore_skip_backfill_check"] = True
        if args.realtime:
            request_args["brainstore_realtime"] = True

    if args.export_path:
        request_args["export_path"] = args.export_path
        response = api_conn().post("/btql", json=request_args, allow_redirects=False)
        print(response)
        print(response.headers["Location"])
        return

    response = api_conn().post_json("/btql", request_args)

    if args.explain:
        explain = response["explain"]
        print(explain)
        return

    schema = response["schema"]
    data = response["data"]

    if args.extended:
        for i, item in enumerate(data):
            if i > 0:  # Add two newlines between items
                print("\n")
            for key, value in item.items():
                formatted_value = pprint.pformat(value, indent=4)
                # Handle multi-line values by indenting them
                if "\n" in formatted_value:
                    print(f"{key}:")
                    for line in formatted_value.splitlines():
                        print(f"    {line}")
                else:
                    print(f"{key}: {formatted_value}")
    elif args.jsonl:
        for item in data:
            print(json.dumps(item))
    elif args.json:
        print(json.dumps(response))
    else:
        # Extract headers from the first item
        headers = list(schema["items"]["properties"].keys())
        # Create rows from the data
        rows = [[row.get(header) for header in headers] for row in data]
        # Format nicely
        rows = [
            [json.dumps(c, indent=2) if isinstance(c, dict) or isinstance(c, list) else c for c in row] for row in rows
        ]
        print(tabulate(rows, headers=headers, tablefmt="grid"))

    if args.cursor:
        print(f"\n\nCursor: {response.get('cursor')}")


def main(args=None):
    if args is None:
        args = sys.argv[1:]

    parent_parser = argparse.ArgumentParser(add_help=False)
    parent_parser.add_argument(
        "--verbose",
        "-v",
        default=0,
        action="count",
        help="Include additional details",
    )

    parser = argparse.ArgumentParser()
    subparsers = parser.add_subparsers(required=True, dest="subcommand", help="subcommand help")

    get_parser = subparsers.add_parser("get", parents=[parent_parser])
    get_parser.add_argument("path", help="The path to request")
    get_parser.add_argument("payload", help="The payload to include", default=None, nargs="*")
    get_parser.set_defaults(func=run_get)

    post_parser = subparsers.add_parser("post", parents=[parent_parser])
    post_parser.add_argument("path", help="The path to request")
    post_parser.add_argument("payload", help="The payload to include", default=None, nargs="*")
    post_parser.set_defaults(func=run_post)

    query_parser = subparsers.add_parser("btql", parents=[parent_parser])

    output_format = query_parser.add_mutually_exclusive_group()
    output_format.add_argument("--table", action="store_true", default=False, help="Output results in table format")
    output_format.add_argument(
        "--extended", action="store_true", default=False, help="Output results in extended format"
    )
    output_format.add_argument("--json", action="store_true", help="Output results as one JSON object")
    output_format.add_argument("--jsonl", action="store_true", help="Output results in JSONL format")

    backend_type = query_parser.add_mutually_exclusive_group()
    backend_type.add_argument("--postgres", action="store_true", default=False, help="Use the Postgres database")
    backend_type.add_argument("--columnstore", action="store_true", default=False, help="Use the ClickHouse database")
    backend_type.add_argument("--duckdb", action="store_true", default=False, help="Use DuckDB")
    backend_type.add_argument("--brainstore", action="store_true", default=False, help="Use the Brainstore database")

    query_parser.add_argument(
        "--realtime", action="store_true", default=False, help="Use the Brainstore realtime feature"
    )

    query_parser.add_argument(
        "--audit-log", action="store_true", default=False, help="Include audit log in the response"
    )
    query_parser.add_argument(
        "--disable-limit", action="store_true", default=False, help="Disable the default limit of 1000 rows"
    )
    query_parser.add_argument("--cursor", help="Show the result cursor", action="store_true")
    query_parser.add_argument("--explain", action="store_true", help="Explain the query")
    query_parser.add_argument("--export-path", help="Export the results to the specified path")
    query_parser.add_argument("query", help="The BTQL query to execute")

    query_parser.set_defaults(func=btql_query)

    args = parser.parse_args(args=args)
    level = logging.DEBUG if args.verbose >= 2 else logging.INFO
    logging.basicConfig(format="%(asctime)s %(levelname)s [%(name)s]: %(message)s", level=level)

    braintrust.login()

    args.func(args)


if __name__ == "__main__":
    sys.exit(main())
