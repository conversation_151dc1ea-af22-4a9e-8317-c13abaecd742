"""
This module provides a CustomLoader and CustomDumper class that can be used to
load YAML files with unknown tags. It will preserve the tags so that they are
dumped back to the file when saving the data.
"""

import yaml


class Loader(yaml.SafeLoader):
    pass


class Tagged:
    def __init__(self, tag, value):
        self.tag = tag
        self.value = value

    def __repr__(self):
        return f"Tagged(tag={self.tag!r}, value={self.value!r})"


class CustomLoader(yaml.SafeLoader):
    pass


class CustomDumper(yaml.SafeDumper):
    pass


def construct_unknown_tag(loader, node):
    if isinstance(node, yaml.ScalarNode):
        value = loader.construct_scalar(node)
    elif isinstance(node, yaml.SequenceNode):
        value = loader.construct_sequence(node)
    elif isinstance(node, yaml.MappingNode):
        value = loader.construct_mapping(node)
    else:
        raise yaml.constructor.ConstructorError(None, None, f"Unsupported YAML node: {node}", node.start_mark)
    return Tagged(node.tag, value)


def represent_tagged(dumper, data):
    if isinstance(data.value, dict):
        return dumper.represent_mapping(data.tag, data.value)
    elif isinstance(data.value, list):
        return dumper.represent_sequence(data.tag, data.value)
    else:
        return dumper.represent_scalar(data.tag, data.value)


CustomLoader.add_constructor(None, construct_unknown_tag)
CustomDumper.add_representer(Tagged, represent_tagged)
