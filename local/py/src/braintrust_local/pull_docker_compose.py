import subprocess
import time


def pull_docker_compose(fpath, num_retries=5, retry_interval_init=1, retry_interval_mult=2, services=None):
    retry_interval = retry_interval_init
    for i in range(num_retries):
        try:
            subprocess.run(["docker", "compose", "-f", fpath, "pull"] + (services if services else []), check=True)
        except subprocess.CalledProcessError as e:
            if i == num_retries - 1:
                raise e
            print(f"Pull failed with error {e}, retrying in {retry_interval} seconds...")
            time.sleep(retry_interval_init)
            retry_interval_init *= retry_interval_mult
        else:
            break
