import braintrust
from typing import Iterator, List, Any

def process_dataset_in_batches(project_name: str, dataset_name: str, batch_size: int = 100) -> Iterator[List[Any]]:
    """
    Process dataset records in batches using the existing SDK approach.
    
    Note: This still loads all records into memory first, but processes them in batches.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        batch_size: Number of records per batch
    
    Yields:
        Batches of records as lists
    """
    dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
    
    batch = []
    for record in dataset:
        batch.append(record)
        
        if len(batch) >= batch_size:
            yield batch
            batch = []
    
    # Yield remaining records if any
    if batch:
        yield batch

def process_records_with_limit(project_name: str, dataset_name: str, max_records: int) -> Iterator[Any]:
    """
    Process only a limited number of records from the dataset.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        max_records: Maximum number of records to process
    
    Yields:
        Individual records up to max_records
    """
    dataset = braintrust.init_dataset(
        project=project_name, 
        name=dataset_name,
        _internal_btql={"limit": max_records}
    )
    
    for record in dataset:
        yield record

# Example usage
if __name__ == "__main__":
    project_name = "pedro-brainstore1"
    dataset_name = "Dataset 1"
    
    print("=== Processing in batches ===")
    batch_count = 0
    for batch in process_dataset_in_batches(project_name, dataset_name, batch_size=5):
        batch_count += 1
        print(f"Batch {batch_count}: {len(batch)} records")
        for i, record in enumerate(batch):
            print(f"  Record {i+1}: {record.get('id', 'no-id')}")
        
        # Stop after 3 batches for demo
        if batch_count >= 3:
            break
    
    print(f"\n=== Processing with limit ===")
    count = 0
    for record in process_records_with_limit(project_name, dataset_name, max_records=10):
        count += 1
        print(f"Record {count}: {record.get('id', 'no-id')}")
