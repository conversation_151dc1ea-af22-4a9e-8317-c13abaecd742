#!/usr/bin/env python

from uuid import uuid4

import psycopg2
from braintrust_local.app_db_util import get_app_db_url

USER_EMAIL = "<EMAIL>"

if __name__ == "__main__":
    with psycopg2.connect(get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT auth_id FROM users WHERE email = %s", (USER_EMAIL,))
            auth_id = cursor.fetchone()[0]
            cursor.execute("SELECT create_api_key(%s, null, %s)", (auth_id, str(uuid4())))
            api_key = cursor.fetchone()[0]
    print(api_key)
