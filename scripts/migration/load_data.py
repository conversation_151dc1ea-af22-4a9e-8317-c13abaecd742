#!/usr/bin/env python3
# USAGE: PG_URL='...' python3 load_data.py <path_to_json_file>
#
# Saved here: https://gist.github.com/ankrgyl/8ba94a302ea4f75c593c8309d97092d6

import json
import os
import sys

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

PG_URL = os.environ.get("PG_URL")
if not PG_URL:
    print("Please set PG_URL environment variable")
    sys.exit(1)


def batch(iterable, n=1):
    batch_list = []
    for item in iterable:
        batch_list.append(item)
        if len(batch_list) == n:
            yield batch_list
            batch_list = []
    if batch_list:
        yield batch_list


if __name__ == "__main__":
    json_file_path = sys.argv[1]

    with psycopg2.connect(PG_URL) as conn:
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        with conn.cursor() as cursor:
            conn.autocommit = True
            table_name = json_file_path.rsplit(".", 1)[0].split("-")[-1].strip()
            with open(json_file_path, "r") as json_file:
                count = 0
                for b in batch(json_file, n=100):
                    json_batch = [json.dumps(json.loads(line)) for line in b]
                    inserts = ",".join(["(%s)"] * len(json_batch))
                    cursor.execute(f"INSERT INTO {table_name} (data) VALUES {inserts}", json_batch)
                    count += len(json_batch)
                    print(f"Processed batch of {len(json_batch)} records, total: {count}")
