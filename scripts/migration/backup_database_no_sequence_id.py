# https://gist.github.com/ankrgyl/b94b66d6283745ccf521226bc14a8fb6
import argparse
import os

import psycopg2

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--comments", action="store_true")
    args = parser.parse_args()

    def single_quote(s):
        return f"'{s}'" if s is not None else "NULL"

    def make_object_id(d):
        project_id = single_quote(d.get("project_id"))
        experiment_id = single_quote(d.get("experiment_id"))
        dataset_id = single_quote(d.get("dataset_id"))
        prompt_session_id = single_quote(d.get("prompt_session_id"))
        log_id = single_quote(d.get("log_id"))
        return f"make_object_id({project_id}, {experiment_id}, {dataset_id}, {prompt_session_id}, {log_id})"

    log_conn = psycopg2.connect(os.environ.get("PG_URL"))
    cursor = log_conn.cursor()

    table = "comments" if args.comments else "logs"
    cursor.execute(f"""SELECT data::text AS data FROM {table}""")

    with open(f"backup-{table}.json", "w") as f:
        for row in cursor:
            print(row[0], file=f)

    cursor.close()
