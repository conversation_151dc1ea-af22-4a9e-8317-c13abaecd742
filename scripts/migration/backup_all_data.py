import argparse
import os

import psycopg2

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--comments", action="store_true")
    args = parser.parse_args()

    org_name = os.environ.get("ORG_NAME")
    supabase_conn = psycopg2.connect(os.environ.get("SUPABASE_PG_URL"))

    cursor = supabase_conn.cursor()
    cursor.execute(
        f"""
        SELECT experiments.id FROM
            experiments JOIN projects on experiments.project_id = projects.id
                        JOIN organizations ON projects.org_id = organizations.id
        WHERE
            organizations.name='{org_name}'
    """
    )

    experiment_ids = [row[0] for row in cursor]

    cursor.execute(
        f"""
        SELECT datasets.id FROM
            datasets JOIN projects on datasets.project_id = projects.id
                        JOIN organizations ON projects.org_id = organizations.id
        WHERE
            organizations.name='{org_name}'
    """
    )

    dataset_ids = [row[0] for row in cursor]

    cursor.execute(
        f"""
        SELECT prompt_sessions.id FROM
            prompt_sessions JOIN organizations ON prompt_sessions.org_id = organizations.id
        WHERE
            organizations.name='{org_name}'
    """
    )

    prompt_session_ids = [row[0] for row in cursor]

    cursor.execute(
        f"""
        SELECT projects.id FROM
            organizations JOIN projects
                ON organizations.id = projects.org_id
        WHERE
            organizations.name='{org_name}'
            and projects.deleted_at is NULL
    """
    )

    project_ids = [row[0] for row in cursor]

    print(experiment_ids, dataset_ids, prompt_session_ids, project_ids)

    object_ids = (
        [{"experiment_id": x} for x in experiment_ids]
        + [{"dataset_id": x} for x in dataset_ids]
        + [{"prompt_session_id": x} for x in prompt_session_ids]
        + [{"project_id": x, "log_id": "p"} for x in project_ids]
        + [{"project_id": x, "log_id": "g"} for x in project_ids]
    )

    def single_quote(s):
        return f"'{s}'" if s is not None else "NULL"

    def make_object_id(d):
        project_id = single_quote(d.get("project_id"))
        experiment_id = single_quote(d.get("experiment_id"))
        dataset_id = single_quote(d.get("dataset_id"))
        prompt_session_id = single_quote(d.get("prompt_session_id"))
        log_id = single_quote(d.get("log_id"))
        return f"make_object_id({project_id}, {experiment_id}, {dataset_id}, {prompt_session_id}, {log_id})"

    log_conn = psycopg2.connect(os.environ.get("PG_URL"))
    cursor = log_conn.cursor()

    table = "comments" if args.comments else "logs"
    cursor.execute(
        f"""
    SELECT data::text AS data FROM {table} WHERE
        make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)
        IN ({", ".join(make_object_id(d) for d in object_ids)})
    """
    )

    with open(f"backup-{table}.json", "w") as f:
        for row in cursor:
            print(row[0], file=f)

    cursor.close()
