import os
import time

import psycopg2
from dotenv import load_dotenv

load_dotenv()


def connect():
    return psycopg2.connect(os.environ.get("PG_URL"))


if __name__ == "__main__":
    conn = connect()
    cursor = conn.cursor()
    cursor.execute("""SET maintenance_work_mem = '4GB'""")

    while True:
        start = time.time()
        result = cursor.execute(
            """
WITH logs_batch AS (
    SELECT
        sequence_id,
        make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) AS object_id,
        convert_to(data::text, 'UTF8') AS data_text,
        length(convert_to(data::text, 'UTF8')) AS text_length
    FROM logs
    WHERE sequence_id > COALESCE((SELECT MAX(sequence_id) FROM logs_search_index_composite), -1)
    ORDER BY sequence_id
    LIMIT 100
),
snippets AS (
    SELECT
        sequence_id,
        object_id,
        data_text,
        text_length,
        generate_series(0, text_length - 1, 524287 - 256) AS start_pos
    FROM logs_batch
)
INSERT INTO logs_search_index_composite (sequence_id, object_id, snippet)
SELECT
    sequence_id,
    object_id,
    to_tsvector(
        'english',
        left(encode(substring(data_text, start_pos + 1, 524287), 'escape')::text, 1048575)
    )
FROM snippets"""
        )
        end = time.time()
        diff = end - start
        conn.commit()
        rows_sec = cursor.rowcount / diff
        print(f"Inserted {cursor.rowcount} rows in {end-start}s ({rows_sec} rows/sec)")

        if cursor.rowcount == 0:
            break
