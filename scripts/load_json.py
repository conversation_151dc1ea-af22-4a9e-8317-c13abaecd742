#!/usr/bin/env python3

import json
import os
import sys

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

PG_URL = os.environ.get("PG_URL", "postgres://postgres:postgres@localhost:5532/postgres")

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))


def batch(iterable, n=1):
    it_len = len(iterable)
    for ndx in range(0, it_len, n):
        yield iterable[ndx : min(ndx + n, it_len)]


if __name__ == "__main__":
    json_file = sys.argv[1]
    lines = [json.dumps(json.loads(line)) for line in open(json_file, "r")]

    conn = psycopg2.connect(PG_URL)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    conn.autocommit = True

    for b in batch(lines, n=100):
        inserts = ",".join(["(%s)"] * len(b))
        cursor.execute(f"INSERT INTO logs (data) VALUES {inserts}", b)
