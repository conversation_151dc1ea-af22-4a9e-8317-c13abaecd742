#!/usr/bin/env python
"""
This script is a wrapper around the chalice CLI that sets the default python version
to 3.11 and the glibc version to 2.26. This is necessary because <PERSON>lice does not support
these versions.
"""

import re
import sys

from chalice.cli import main
from chalice.config import Config
from chalice.deploy.packager import BaseLambdaDeploymentPackager, DependencyBuilder

if __name__ == "__main__":
    Config.lambda_python_version = "python3.11"
    BaseLambdaDeploymentPackager._RUNTIME_TO_ABI["python3.11"] = "cp311"
    DependencyBuilder._RUNTIME_GLIBC["cp311"] = (2, 26)

    # This is just copy/pasted from the chalice cli (venv/bin/chalice)
    sys.argv[0] = re.sub(r"(-script\.pyw|\.exe)?$", "", sys.argv[0])
    sys.exit(main())
