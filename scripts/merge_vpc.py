#!/usr/bin/env python3

import re
import sys

from cfn_tools import dump_yaml, load_yaml

# This script is primarily used to generate our Staging CloudFormation template.
# It hacks up the merge-base.yaml template so that our staging stack uses the production
# VPC and datastores. The exception here is Brainstore, which is deployed specifically for staging.
# The staging brainstore instances point to the production brainstore S3 bucket.

if __name__ == "__main__":
    src_file = sys.argv[1]
    dst_file = sys.argv[2]

    with open(src_file, "r") as f:
        contents = load_yaml(f.read())

    contents["Metadata"]["AWS::CloudFormation::Interface"]["ParameterGroups"].append(
        {"Label": {"default": "VPC Settings"}, "Parameters": ["SecurityGroupId", "SubnetIds"]}
    )
    contents["Parameters"]["PostgresUrl"] = {
        "Type": "String",
        "Description": "Enter the postgres URL",
    }
    contents["Parameters"]["ClickhousePGUrl"] = {
        "Type": "String",
        "Description": "Enter the clickhouse PG URL",
        "Default": "",
    }
    contents["Parameters"]["ClickhouseConnectUrl"] = {
        "Type": "String",
        "Description": "Enter the clickhouse connect URL",
        "Default": "",
    }
    contents["Parameters"]["ClickhouseCatchupEtlArn"] = {
        "Type": "String",
        "Description": "Enter the clickhouse catchup ETL ARN",
        "Default": "",
    }
    contents["Parameters"]["SecurityGroupId"] = {
        "Type": "String",
        "Description": "Enter the security group ID to use for the lambda function",
    }
    contents["Parameters"]["SubnetIds"] = {
        "Type": "CommaDelimitedList",
        "Description": "Enter the subnet IDs to use for the lambda function",
    }
    contents["Parameters"]["VpcId"] = {
        "Type": "String",
        "Description": "Enter the VPC ID to use for Brainstore",
    }
    contents["Parameters"]["BrainstoreS3Bucket"] = {
        "Type": "String",
        "Description": "S3 bucket for Brainstore data storage",
    }
    contents["Parameters"]["ServiceTokenSecret"] = {
        "Type": "String",
        "Description": "Enter the service token secret",
    }

    del contents["Conditions"]["UseManagedPostgres"]
    for policy in contents["Resources"]["DefaultRole"]["Properties"]["Policies"]:
        for i, statement in enumerate(policy["PolicyDocument"]["Statement"]):
            resource = statement["Resource"]
            if isinstance(resource, dict) and resource.get("Ref") == "DatabaseSecret":
                del policy["PolicyDocument"]["Statement"][i]
                break

    api_functions = [
        "APIHandler",
        "APIHandlerJS",
        "AIProxyFn",
        "QuarantineWarmupFunction",
        "ClickhouseCatchupEtl",
        "AutomationCron",
        "BillingCron",
    ]
    for fn in api_functions:
        for env_var, ref_var in [
            ("REDIS_URL", "RedisAlternativeUrl"),
            ("PG_URL", "PostgresUrl"),
            ("CLICKHOUSE_PG_URL", "ClickhousePGUrl"),
            ("CLICKHOUSE_CONNECT_URL", "ClickhouseConnectUrl"),
            ("CATCHUP_ETL_ARN", "ClickhouseCatchupEtlArn"),
            ("SERVICE_TOKEN_SECRET_KEY", "ServiceTokenSecret"),
        ]:
            if (
                env_var in contents["Resources"][fn]["Properties"]["Environment"]["Variables"]
                or env_var == "SERVICE_TOKEN_SECRET_KEY"
            ):
                contents["Resources"][fn]["Properties"]["Environment"]["Variables"][env_var] = {
                    "Ref": ref_var,
                }

    for fn in api_functions + ["InitializePrimerFunction"]:
        contents["Resources"][fn]["Properties"]["VpcConfig"] = {
            "SecurityGroupIds": [{"Ref": "SecurityGroupId"}],
            "SubnetIds": {"Ref": "SubnetIds"},
        }

    # Temporary hack to enable Telemetry for staging stack only.
    contents["Resources"]["APIHandlerJS"]["Properties"]["Environment"]["Variables"]["TELEMETRY_ENABLED"] = "true"
    contents["Resources"]["BillingCron"]["Properties"]["Environment"]["Variables"]["TELEMETRY_ENABLED"] = "true"

    contents["Resources"]["APIHandlerRole"]["Properties"]["Policies"][0]["PolicyDocument"]["Statement"] = [
        (
            {
                **x,
                "Resource": {
                    "Ref": "ClickhouseCatchupEtlArn",
                },
            }
            if x.get("Sid") == "ClickhouseCatchupEtlInvoke"
            else x
        )
        for x in contents["Resources"]["APIHandlerRole"]["Properties"]["Policies"][0]["PolicyDocument"]["Statement"]
    ]

    contents["Resources"]["InitializePrimerRole"]["Properties"]["Policies"][0]["PolicyDocument"]["Statement"][0][
        "Resource"
    ] = [
        x
        for x in contents["Resources"]["InitializePrimerRole"]["Properties"]["Policies"][0]["PolicyDocument"][
            "Statement"
        ][0]["Resource"]
        if x.get("Ref") != "MigrateDatabaseFunction.Version"
    ]

    for key in [
        "DBParameterGroup",
        "DBSubnetGroup",
        "DatabaseMonitoringRole",
        "MigrateDatabaseFunction",
        "CallMigrateDatabase",
        "CallMigrateDatabaseAlt",
        "ElastiCacheSubnetGroup",
        "ElastiCacheCluster",
        "pubPrivateVPC",
        "publicSubnet1",
        "privateSubnet1",
        "privateSubnet2",
        "privateSubnet3",
        "internetGateway",
        "gatewayToInternet",
        "publicRouteTable",
        "publicRoute",
        "publicSubnet1RouteTableAssociation",
        "natGateway",
        "natPublicIP",
        "privateRouteTable",
        "privateRoute",
        "privateSubnet1RouteTableAssociation",
        "privateSubnet2RouteTableAssociation",
        "privateSubnet3RouteTableAssociation",
        "s3Endpoint",
        "dynamoDBEndpoint",
        "BrainstoreS3Bucket",
        "BastionInstance",
        "BastionSSHSecurityGroup",
        "BastionInstanceLaunchTemplate",
        "BastionInstanceProfile",
        "BastionInstanceRole",
        "BraintrustSupportEC2InstanceConnectPolicy",
        "BastionInstanceConnectEndpoint",
        "BastionInstanceConnectSecurityGroup",
    ]:
        del contents["Resources"][key]

    for section in ["Resources", "Outputs"]:
        for key, body in contents[section].items():
            if body.get("Condition") == "UseManagedPostgres" or body.get("Condition") == "UseManagedClickhouse":
                del contents[section][key]

    for key in [
        "pubPrivateVPCID",
        "publicSubnet1ID",
        "privateSubnet1ID",
        "privateSubnet2ID",
        "privateSubnet3ID",
        "privateVPCSecurityGroup",
        "BraintrustSupportBastionInstanceID",
    ]:
        del contents["Outputs"][key]

    # Hack up Brainstore to use passed in VPC details
    contents["Resources"]["BrainstoreLaunchTemplate"]["Properties"]["LaunchTemplateData"]["SecurityGroupIds"] = [
        {"Ref": "SecurityGroupId"}
    ]

    for prefix in ["Brainstore", "BrainstoreWriter"]:
        contents["Resources"][f"{prefix}AutoScalingGroup"]["Properties"]["VPCZoneIdentifier"] = {"Ref": "SubnetIds"}
        contents["Resources"][f"{prefix}NLB"]["Properties"]["SecurityGroups"] = [{"Ref": "SecurityGroupId"}]
        contents["Resources"][f"{prefix}NLB"]["Properties"]["Subnets"] = {"Ref": "SubnetIds"}
        contents["Resources"][f"{prefix}TargetGroup"]["Properties"]["VpcId"] = {"Ref": "VpcId"}

    # Update UserData to use parameter references instead of resource references
    userdata = contents["Resources"]["BrainstoreLaunchTemplate"]["Properties"]["LaunchTemplateData"]["UserData"][
        "Fn::Base64"
    ]["Fn::Sub"]
    script_content = userdata[0]
    variable_mappings = userdata[1]

    # Remove the entire secrets retrieval section (both database and service token)
    script_content = re.sub(
        r"# Get database credentials and service token secret from Secrets Manager\s*DB_CREDS=\$\(aws secretsmanager.*?\)\s*DB_USERNAME=\$\(echo \$DB_CREDS.*?\)\s*DB_PASSWORD=\$\(echo \$DB_CREDS.*?\)\s*SERVICE_TOKEN_SECRET=\$\(aws secretsmanager.*?\)\s*",
        "",
        script_content,
    )
    script_content = re.sub(
        r"BRAINSTORE_METADATA_URI=postgres://\$DB_USERNAME:\$DB_PASSWORD@\${DatabaseHost}/postgres\s*BRAINSTORE_WAL_URI=postgres://\$DB_USERNAME:\$DB_PASSWORD@\${DatabaseHost}/postgres",
        "BRAINSTORE_METADATA_URI=${DatabaseUrl}\nBRAINSTORE_WAL_URI=${DatabaseUrl}",
        script_content,
    )
    # Replace the SERVICE_TOKEN_SECRET variable usage with CloudFormation parameter reference
    script_content = re.sub(
        r"SERVICE_TOKEN_SECRET_KEY=\$SERVICE_TOKEN_SECRET",
        "SERVICE_TOKEN_SECRET_KEY=${ServiceTokenSecret}",
        script_content,
    )
    del variable_mappings["DatabaseHost"]
    variable_mappings["DatabaseUrl"] = {"Ref": "PostgresUrl"}
    variable_mappings["RedisUrl"] = {"Ref": "RedisAlternativeUrl"}
    variable_mappings["ServiceTokenSecret"] = {"Ref": "ServiceTokenSecret"}
    contents["Resources"]["BrainstoreLaunchTemplate"]["Properties"]["LaunchTemplateData"]["UserData"]["Fn::Base64"][
        "Fn::Sub"
    ] = [script_content, variable_mappings]

    with open(dst_file, "w") as f:
        f.write(dump_yaml(contents))
