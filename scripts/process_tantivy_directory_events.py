#!/usr/bin/env python3
import argparse
import json
from collections import defaultdict

from intervaltree import IntervalTree


def bytes_read(files):
    # Merge all the ranges
    path_to_byte_ranges = defaultdict(IntervalTree)
    for file in files:
        with open(file, "rb") as f:
            data = json.load(f)
        for full_event in data:
            event_obj = full_event["event"]
            if not isinstance(event_obj, dict):
                continue
            elif event := event_obj.get("AtomicRead"):
                start, end = 0, event["file_len"]
                path_to_byte_ranges[event["path"]][start:end] = (start, end)
            elif event := event_obj.get("ReadBytes"):
                start, end = event["start"], event["end"]
                path_to_byte_ranges[event["path"]][start:end] = (start, end)

    # Compute total bytes per file.
    total_bytes_per_file = {
        path: sum(entry.data[1] - entry.data[0] for entry in tree) for path, tree in path_to_byte_ranges.items()
    }
    total_bytes = sum(total_bytes_per_file.values())
    return dict(
        total_bytes=total_bytes,
        total_bytes_per_file=total_bytes_per_file,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process Tantivy directory events")
    parser.add_argument("command", help="The command to execute", choices=["bytes-read"])
    parser.add_argument("files", help="The files to query", nargs="+")
    args = parser.parse_args()

    if args.command == "bytes-read":
        out = bytes_read(args.files)
    else:
        raise ValueError(f"Unknown command: {args.command}")
    print(json.dumps(out))
