import { Octokit } from "octokit";

export const ORG = "braintrustdata";
export const MAIN_REPO = "braintrust";
export const MAIN_BRANCH = "main";
export const MAIN_BRANCH_OVERRIDE: Record<string, string> = {
  ["duckdb-wasm"]: "master",
  "deploy-to-vercel-action": "master",
};

export function makeClient(): Octokit {
  return new Octokit({ auth: process.env.GITHUB_API_KEY });
}

export async function getAllRepos(client: Octokit) {
  const resp = await client.paginate("GET /orgs/{org}/repos", {
    org: ORG,
  });
  return resp.map((x) => x.name);
}
