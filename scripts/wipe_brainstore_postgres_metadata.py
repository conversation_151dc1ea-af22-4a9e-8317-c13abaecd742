#!/usr/bin/env python3

# Wipe all brainstore metadata stored in postgres. This can be useful for making
# a fresh start on brainstore without having to recreate the entire database.

import psycopg2
from braintrust_local import api_db_util


def main():
    confirm_proceed = input(
        f"This script will delete all brainstore metadata from the database. Enter YES to confirm: "
    ).strip()
    if confirm_proceed != "YES":
        print("Exiting")
        return

    with psycopg2.connect(api_db_util.get_api_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                select table_name
                from information_schema.tables
                where
                    table_schema = 'public'
                    and table_name like %s
                """,
                ("brainstore_%",),
            )
            for row in cursor.fetchall():
                table_name = row[0]
                if table_name == "brainstore_backfill_global_state":
                    cursor.execute(f"update {table_name} set frontier_sequence_id = 0")
                else:
                    cursor.execute(f"truncate table {table_name}")

    print("Success")


if __name__ == "__main__":
    main()
