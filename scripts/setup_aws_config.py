#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.9"
# ///

import configparser
import json
import os
import subprocess
from datetime import datetime, timezone
from typing import Dict, Optional, Tuple

ACCOUNTS = {
    "prod": {"id": "************", "name": "Production"},
    "sandbox": {"id": "************", "name": "Sandbox"},
    "interview": {"id": "************", "name": "Interview"},
}
# Default profile will be made into a copy of this account profile
DEFAULT_PROFILE = "prod"
DEFAULT_ACCOUNT_REGION = "us-east-1"

SSO_SESSION = "braintrust"
SSO_START_URL = "https://braintrustdata.awsapps.com/start/#"
SSO_REGION = "us-east-2"
AWS_CONFIG_PATH = os.path.expanduser("~/.aws/config")
SSO_CACHE_DIR = os.path.expanduser("~/.aws/sso/cache")


def ensure_aws_directories() -> None:
    aws_dir = os.path.dirname(AWS_CONFIG_PATH)
    if not os.path.exists(aws_dir):
        os.makedirs(aws_dir, mode=0o755)

    if not os.path.exists(SSO_CACHE_DIR):
        os.makedirs(SSO_CACHE_DIR, mode=0o755)


def check_aws_cli() -> None:
    try:
        subprocess.run(["aws", "--version"], check=True, capture_output=True, text=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        raise RuntimeError("AWS CLI is not available. Please install it first:\nmake install-aws-cli")


def ensure_sso_session(config: configparser.RawConfigParser) -> None:
    section = f"sso-session {SSO_SESSION}"
    if not config.has_section(section):
        config.add_section(section)
        config.set(section, "sso_start_url", SSO_START_URL)
        config.set(section, "sso_region", SSO_REGION)
        config.set(section, "sso_registration_scopes", "sso:account:access")
        print(f"✏️ Added SSO session block: [{section}]")


def sso_login() -> None:
    cmd = ["aws", "sso", "login", "--sso-session", SSO_SESSION]
    print(f"🔐 Running: {' '.join(cmd)}")
    subprocess.run(cmd, check=True)


def _list_account_roles(account_id: str, token: str) -> Optional[Dict]:
    """Call list-account-roles and return the parsed JSON response."""
    try:
        result = subprocess.run(
            [
                "aws",
                "sso",
                "list-account-roles",
                "--account-id",
                account_id,
                "--access-token",
                token,
                "--region",
                SSO_REGION,
            ],
            check=True,
            capture_output=True,
            text=True,
        )
        return json.loads(result.stdout)
    except (subprocess.CalledProcessError, json.JSONDecodeError):
        return None


def get_valid_access_token(account_id_to_test: str) -> Optional[str]:
    """Find a valid access token from the SSO cache."""
    if not os.path.exists(SSO_CACHE_DIR):
        return None

    for file in os.listdir(SSO_CACHE_DIR):
        if not file.endswith(".json"):
            continue
        path = os.path.join(SSO_CACHE_DIR, file)
        try:
            with open(path) as f:
                data = json.load(f)
        except (json.JSONDecodeError, OSError):
            continue

        if "accessToken" not in data or "expiresAt" not in data:
            continue

        expires = datetime.fromisoformat(data["expiresAt"].replace("Z", "+00:00"))
        if expires <= datetime.now(timezone.utc):
            continue

        token = data["accessToken"]
        # Test token validity by attempting to list account roles
        if _list_account_roles(account_id_to_test, token):
            return token
    return None


def get_role(account_id: str, account_name: str, token: str) -> Optional[str]:
    """Get the role name for an account, with user selection if multiple roles exist."""
    result = _list_account_roles(account_id, token)
    if not result:
        return None

    roles = result.get("roleList", [])
    if not roles:
        return None
    if len(roles) == 1:
        return roles[0]["roleName"]

    # Sort roles alphabetically by role name for consistent display
    roles.sort(key=lambda x: x["roleName"])

    print(f"Multiple roles found for {account_name} account ({account_id}):")
    for idx, role in enumerate(roles, 1):
        print(f"{idx}. {role['roleName']}")
    choice = input(f"Choose role [1-{len(roles)}]: ").strip()
    try:
        index = int(choice)
        if 1 <= index <= len(roles):
            return roles[index - 1]["roleName"]
    except ValueError:
        pass
    print(f"⚠️ Invalid role selection - skipping {account_name}")
    return None


def _configure_profile_section(
    config: configparser.RawConfigParser, profile_name: str, account_id: str, role_name: str
) -> None:
    section = f"profile {profile_name}"
    if not config.has_section(section):
        config.add_section(section)
    config.set(section, "sso_session", SSO_SESSION)
    config.set(section, "sso_account_id", account_id)
    config.set(section, "sso_role_name", role_name)
    config.set(section, "region", DEFAULT_ACCOUNT_REGION)
    config.set(section, "output", "json")


def _get_default_profile(profiles: Dict[str, Tuple[str, str]]) -> Optional[str]:
    """Determine which profile should be used as the default."""
    if not profiles:
        return None
    if DEFAULT_PROFILE in profiles:
        return DEFAULT_PROFILE
    # Use the first available profile as default if the preferred default is not available
    return next(iter(profiles.keys()))


def update_aws_config(profiles: Dict[str, Tuple[str, str]], config: configparser.RawConfigParser) -> None:
    """Update the AWS config file with the provided profiles."""
    # Configure all profiles
    for profile_key, (account_id, role_name) in profiles.items():
        _configure_profile_section(config, profile_key, account_id, role_name)

    # Add default profile - use configured default if available, otherwise use first available
    selected_default = _get_default_profile(profiles)
    if selected_default:
        default_account_id, default_role_name = profiles[selected_default]
        _configure_profile_section(config, "default", default_account_id, default_role_name)

    with open(AWS_CONFIG_PATH, "w") as configfile:
        config.write(configfile)

    print(f"✏️ Updated {AWS_CONFIG_PATH} with {len(profiles)} profile(s).")
    if selected_default:
        print(f"✏️ Set default profile to {selected_default}.")
    else:
        print("⚠️ No accessible profiles found - no default profile set.")


def get_accessible_profiles(token: str) -> Dict[str, Tuple[str, str]]:
    """Get all accessible profiles with their account IDs and role names."""
    profiles = {}
    for profile_key, account_info in ACCOUNTS.items():
        account_id = account_info["id"]
        account_name = account_info["name"]
        role = get_role(account_id, account_name, token)
        if role:
            print(f"Selected role: {role} for {account_name} ({account_id})")
            profiles[profile_key] = (account_id, role)
    return profiles


def display_configured_profiles(profiles: Dict[str, Tuple[str, str]]) -> None:
    """Display the configured profiles and usage examples."""
    print("📋 Configured profiles:")

    # Determine which profile is actually set as default
    actual_default = _get_default_profile(profiles)

    if actual_default and actual_default in profiles:
        account_info = ACCOUNTS[actual_default]
        print(
            f"  - default -> {account_info['name']} ({account_info['id']}) in {DEFAULT_ACCOUNT_REGION} as {profiles[actual_default][1]}"
        )

    for profile_key, account_info in ACCOUNTS.items():
        if profile_key in profiles:
            print(
                f"  - {profile_key} -> {account_info['name']} ({account_info['id']}) in {DEFAULT_ACCOUNT_REGION} as {profiles[profile_key][1]}"
            )
    print()
    print("✅ AWS config setup complete! Your AWS config and CLI are now ready to use.")
    print("📌 Example usage:")
    if actual_default:
        print(f"  # The default profile is {actual_default}:")
        print("  aws sts get-caller-identity")
    print("  # Using specific profiles:")
    for profile_key, account_info in ACCOUNTS.items():
        if profile_key in profiles:
            print(f"  aws --profile {profile_key} sts get-caller-identity")


def main() -> None:
    check_aws_cli()
    ensure_aws_directories()

    config = configparser.RawConfigParser()
    config.read(AWS_CONFIG_PATH)

    ensure_sso_session(config)
    with open(AWS_CONFIG_PATH, "w") as configfile:
        config.write(configfile)

    print("🔑 Checking for valid SSO tokens...")
    first_account_id = next(iter(ACCOUNTS.values()))["id"]
    token = get_valid_access_token(first_account_id)
    if not token:
        print("⚠️ No valid SSO token found. Initiating login...")
        sso_login()
        token = get_valid_access_token(first_account_id)
        if not token:
            raise RuntimeError("SSO login failed to produce a valid token.")

    print("🔍 Fetching roles for accounts...")
    profiles = get_accessible_profiles(token)
    print()
    update_aws_config(profiles, config)
    if not profiles:
        print("❌ No accessible accounts found. Please check your SSO permissions.")
        return

    display_configured_profiles(profiles)


if __name__ == "__main__":
    main()
