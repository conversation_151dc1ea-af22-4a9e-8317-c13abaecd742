#!/usr/bin/env python3

import argparse
import csv
import os
from datetime import datetime

import psycopg2


def main(args):
    with psycopg2.connect(args.db_url) as conn:
        with conn.cursor() as cursor:
            # Create table if it doesn't exist
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS free_credits_from_csv (
                    timestamp TIMESTAMP,
                    email TEXT,
                    full_name TEXT,
                    company TEXT,
                    braintrust_email TEXT,
                    org_name TEXT UNIQUE,
                    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Read CSV and insert rows
            with open(args.csv_file) as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        cursor.execute(
                            """
                            INSERT INTO free_credits_from_csv (
                                timestamp,
                                email,
                                full_name,
                                company,
                                braintrust_email,
                                org_name
                            ) VALUES (%s, %s, %s, %s, %s, %s)
                            ON CONFLICT (org_name) DO NOTHING
                        """,
                            (
                                datetime.strptime(row["Timestamp"], "%m/%d/%Y %H:%M:%S"),
                                row["Email Address"],
                                row["Full Name"],
                                row["Company Name"],
                                row["Email address associated with Braintrust account"],
                                row["Braintrust Org Name (case sensitive)"],
                            ),
                        )
                    except Exception as e:
                        print(f"Error inserting row {row}: {e}")

            conn.commit()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Sync free credits from CSV file",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--csv-file", required=True, help="Path to CSV file containing free credits data")
    parser.add_argument("--db-url", required=True)
    args = parser.parse_args()
    main(args)
