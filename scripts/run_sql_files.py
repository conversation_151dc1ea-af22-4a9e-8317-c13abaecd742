#!/usr/bin/env python

import argparse

import psycopg2
from braintrust_local.api_db_util import get_api_db_url
from braintrust_local.app_db_util import get_app_db_url

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Run the given SQL files as a batch against the specified DB",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("files", nargs="+", help="Files to run")
    parser.add_argument("--db", choices=["app", "api"], default="app", help="The DB to run SQL files against")
    args = parser.parse_args()

    full_contents = ""
    for file in args.files:
        with open(file) as f:
            full_contents += f.read() + "\n"

    db_url = None
    if args.db == "app":
        db_url = get_app_db_url()
    elif args.db == "api":
        db_url = get_api_db_url()
    else:
        raise Exception("Impossible")

    with psycopg2.connect(db_url) as conn:
        with conn.cursor() as cursor:
            cursor.execute(full_contents)
