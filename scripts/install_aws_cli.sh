#!/bin/bash

# Remove AWS CLI from virtualenv if it exists
if [[ "$(which aws)" == *"venv/bin/aws" ]]; then
    echo "Found AWS CLI in virtualenv. Removing..."
    pip uninstall -y awscli
fi

# Check if AWS CLI exists and its version
if ! command -v aws > /dev/null; then
    echo "AWS CLI not found. Installing..."
elif [ "$(aws --version | grep -o '^aws-cli/[0-9]' | cut -d'/' -f2)" -lt "2" ]; then
    echo "AWS CLI version 2.x or higher required. Upgrading..."
else
    echo "AWS CLI v2+ already installed. Skipping..."
    exit 0
fi

# Install based on OS type
case "$(uname -s)" in
    Linux*)
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
        unzip awscliv2.zip
        echo "Sudo required to install AWS CLI"
        sudo ./aws/install --update
        rm -rf aws awscliv2.zip
        ;;
    Darwin*)
        curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
        echo "Sudo required to install AWS CLI"
        sudo installer -pkg AWSCLIV2.pkg -target /
        rm AWSCLIV2.pkg
        ;;
    *)
        echo "Unsupported operating system"
        exit 1
        ;;
esac
