#!/usr/bin/env python3

import argparse
import os

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CODE_SNIPPET = """
import { %(function)s } from "%(import_path)s";
import { invokeServerActionHelper } from "#/app/api/actions/util";

export async function POST(request: Request) {
  return invokeServerActionHelper({
    request,
    fn: %(function)s,
  });
}
""".strip()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate a new server action route for the given function under app/app/api/actions"
    )
    parser.add_argument(
        "--function", type=str, help="The name of the function to generate a new server action route for"
    )
    parser.add_argument(
        "--import-path", type=str, help="The import path of the function to generate a new server action route for"
    )
    args = parser.parse_args()

    route_dir = os.path.join(ROOT_DIR, f"app/app/api/actions/{args.function}")
    route_file = os.path.join(route_dir, "route.ts")
    os.makedirs(route_dir)
    with open(route_file, "w") as f:
        f.write((CODE_SNIPPET % dict(function=args.function, import_path=args.import_path)) + "\n")
