import argparse
import json

import requests

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Fetch all rows of an object from Braintrust. Prints the rows to stdout as a JSON array",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--api-url", default="https://api.braintrust.dev", help="Url of the API server to fetch from")
    parser.add_argument("--auth-token", required=True, help="Authentication token.")
    parser.add_argument(
        "--object-type", choices=["experiment", "dataset", "project_logs"], required=True, help="Object type to fetch"
    )
    parser.add_argument("--object-id", required=True, help="Object id to fetch")
    parser.add_argument("--limit", default=100, type=int, help="Maximum number of elements to fetch in each request")
    parser.add_argument(
        "--version", type=str, help="Optional version string for fetching older versions of the object"
    )
    args = parser.parse_args()

    id_to_row = {}
    max_xact_id, max_root_span_id = None, None
    while True:
        resp = requests.post(
            f"{args.api_url}/v1/{args.object_type}/{args.object_id}/fetch",
            headers={
                "Authorization": f"Bearer {args.auth_token}",
                "Accept-Encoding": "gzip",
            },
            json=dict(
                limit=args.limit,
                max_root_span_id=max_root_span_id,
                max_xact_id=max_xact_id,
                version=args.version,
            ),
        )
        if not resp.ok:
            raise Exception(f"Error fetching rows {resp.status_code}: {resp.text}")

        all_rows = resp.json()["events"]
        if not all_rows:
            break
        for row in all_rows:
            if row["id"] not in id_to_row:
                id_to_row[row["id"]] = row
        max_xact_id, max_root_span_id = min([(row["_xact_id"], row["root_span_id"]) for row in all_rows])

    print(json.dumps(list(id_to_row.values())))
