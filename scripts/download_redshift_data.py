import json
import random
import sys
import time

import psycopg2

conn = psycopg2.connect(sys.argv[1])
cursor = conn.cursor()

start = time.time()
cursor.execute("SELECT data FROM braintrust_logs")
with open("backup.json", "w") as f:
    i = 0
    for row in cursor:
        i += 1
        if i % 10 == 0:
            print(i)
        print(row[0], file=f)

end = time.time()
print(end - start)


cursor.close()
conn.commit()
