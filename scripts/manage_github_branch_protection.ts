import { ArgumentParser, ArgumentDefaultsHelpFormatter } from "argparse";
import { Octokit } from "octokit";
import { z } from "zod";
import * as util from "./github_api_util";

const argsSchema = z.object({
  action: z.enum(["read", "write"]),
  repo: z.string(),
  verbose: z.boolean().optional(),
});

const BRANCH_PROTECTION_POLICY = {
  owner: util.ORG,
  required_status_checks: null,
  enforce_admins: null,
  required_pull_request_reviews: {
    required_approving_review_count: 1,
    require_code_owner_reviews: true,
  },
  restrictions: null,
} as const;

async function getBranchProtection(client: Octokit, repo: string) {
  const resp = await client.request(
    "GET /repos/{owner}/{repo}/branches/{branch}/protection",
    {
      owner: util.ORG,
      repo,
      branch: util.MAIN_BRANCH_OVERRIDE[repo] ?? util.MAIN_BRANCH,
    },
  );
  return resp;
}

async function setBranchProtection(client: Octokit, repo: string) {
  return await client.request(
    "PUT /repos/{owner}/{repo}/branches/{branch}/protection",
    {
      repo,
      branch: util.MAIN_BRANCH_OVERRIDE[repo] ?? util.MAIN_BRANCH,
      ...BRANCH_PROTECTION_POLICY,
    },
  );
}

async function main() {
  const parser = new ArgumentParser({
    description: "Manage github branch protection rules",
    formatter_class: ArgumentDefaultsHelpFormatter,
  });
  parser.add_argument("action", {
    help: "Which action to perform",
    type: "str",
    choices: ["read", "write"],
  });
  parser.add_argument("--repo", {
    help: "Which repo to act on. If 'all', acts on all repos",
    type: "str",
    required: true,
  });
  parser.add_argument("--verbose", {
    help: "If specified, print out extra detail",
    action: "store_true",
  });
  const argsRaw = parser.parse_args();
  const args = argsSchema.parse(argsRaw);
  const client = new Octokit({ auth: process.env.GITHUB_API_KEY });

  const repos =
    args.repo === "all" ? await util.getAllRepos(client) : [args.repo];
  console.log(`Performing ${args.action} on repos: ${JSON.stringify(repos)}`);

  if (args.action === "read") {
    for (const repo of repos) {
      console.log(
        `Branch protection for repo ${repo}:\n`,
        await getBranchProtection(client, repo),
      );
    }
  } else if (args.action === "write") {
    for (const repo of repos) {
      try {
        const result = await setBranchProtection(client, repo);
        if (args.verbose) {
          console.log(`Updated branch protection for repo ${repo}:\n`, result);
        } else {
          console.log(`Updated branch protection for repo ${repo}`);
        }
      } catch (e) {
        console.error(
          `Failed to update branch protection for repo ${repo}\n`,
          e,
        );
      }
    }
  }
}

main();
