#!/usr/bin/env python3

# Builds all packages in the root workspace package.json which are repo
# workspace packages.

import json
import os
import subprocess
from itertools import chain

ROOT_PACKAGE_JSON = os.path.join(os.path.dirname(os.path.dirname(__file__)), "package.json")


def get_root_workspace_packages():
    with open(ROOT_PACKAGE_JSON) as f:
        package_json = json.load(f)

    for k, v in chain(package_json.get("dependencies", {}).items(), package_json.get("devDependencies", {}).items()):
        if v.startswith("workspace:"):
            yield k


if __name__ == "__main__":
    filter_clauses = []
    for package in get_root_workspace_packages():
        filter_clauses.extend(["--filter", package])
    if filter_clauses:
        subprocess.run(["pnpm", "build"] + filter_clauses, check=True)
