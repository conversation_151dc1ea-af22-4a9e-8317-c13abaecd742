#!/usr/bin/env python3

import argparse
import glob
import os
import sys


def process_file(content):
    """Process markdown content line by line to fix code block indentation."""
    lines = content.splitlines()
    processed_lines = []
    code_block_lines = []
    in_code_block = False

    for line in lines:
        # Check for code block fence
        stripped = line.lstrip()
        if stripped.startswith("```"):
            if not in_code_block:
                # Start of code block
                in_code_block = True
                # Extract language if present
                lang = stripped[3:].strip()
                code_block_lines = [f"```{lang}" if lang else "```"]
            else:
                # End of code block
                in_code_block = False
                # Process the collected code block
                if len(code_block_lines) > 1:
                    # Find common indentation from non-empty lines
                    indents = []
                    for code_line in code_block_lines[1:]:  # Skip fence line
                        if code_line.strip():
                            indent = len(code_line) - len(code_line.lstrip())
                            indents.append(indent)

                    # Remove common indentation if found
                    if indents:
                        min_indent = min(indents)
                        for i in range(1, len(code_block_lines)):
                            if code_block_lines[i].strip():
                                code_block_lines[i] = code_block_lines[i][min_indent:]

                # Add closing fence and append to processed lines
                code_block_lines.append("```")
                processed_lines.extend(code_block_lines)
                code_block_lines = []
        else:
            if in_code_block:
                code_block_lines.append(line)
            else:
                processed_lines.append(line)

    # Handle unclosed code block
    if in_code_block:
        processed_lines.extend(code_block_lines)

    return "\n".join(processed_lines)


def main():
    parser = argparse.ArgumentParser(description="Fix indentation in markdown code blocks")
    parser.add_argument("files", nargs="+", help="Markdown files to process")
    parser.add_argument(
        "--dry-run", action="store_true", help="Show which files would be modified without making changes"
    )
    args = parser.parse_args()

    success = True
    for pattern in args.files:
        # Handle glob patterns
        files = glob.glob(pattern, recursive=True) if "*" in pattern else [pattern]

        for file_path in files:
            if not os.path.exists(file_path):
                print(f"File not found: {file_path}", file=sys.stderr)
                success = False
                continue

            if not file_path.lower().endswith((".md", ".mdx")):
                print(f"Skipping non-markdown file: {file_path}", file=sys.stderr)
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                new_content = process_file(content)

                if new_content != content:
                    if args.dry_run:
                        print(f"Would modify: {file_path}")
                    else:
                        with open(file_path, "w", encoding="utf-8") as f:
                            f.write(new_content)
                        print(f"Modified: {file_path}")
                else:
                    print(f"No changes needed: {file_path}")

            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}", file=sys.stderr)
                success = False

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
