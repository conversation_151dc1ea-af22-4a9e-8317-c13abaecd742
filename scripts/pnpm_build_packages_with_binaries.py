#!/usr/bin/env python3

# Figures out all packages in the workspace which have binaries and builds them.

import json
import os
import subprocess


def has_binaries(project_dir):
    package_json_file = os.path.join(project_dir, "package.json")
    if not os.path.exists(package_json_file):
        return False
    with open(package_json_file) as f:
        package_json = json.load(f)
    return "bin" in package_json and len(package_json["bin"]) > 0


if __name__ == "__main__":
    res = subprocess.run(["pnpm", "list", "-r", "--depth", "-1", "--json"], capture_output=True, check=True)
    package_infos = json.loads(res.stdout.decode())
    filter_clauses = []
    for p in package_infos:
        if has_binaries(p["path"]):
            filter_clauses.extend(["--filter", p["name"]])
    if filter_clauses:
        subprocess.run(["pnpm", "build"] + filter_clauses, check=True)
