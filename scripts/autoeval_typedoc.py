#!/usr/bin/env python3
import json
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

if __name__ == "__main__":
    with open(os.path.join(SCRIPT_DIR, "..", "sdk", "js", "typedoc.json"), "r") as f:
        typedoc_json = json.load(f)

    typedoc_json["out"] = "../app/content/docs/reference/autoevals/nodejs"
    typedoc_json["publicPath"] = "/docs/autoevals/nodejs/"
    typedoc_json["disableSources"] = False

    with open(os.path.join(SCRIPT_DIR, "..", "autoevals", "typedoc.json"), "w") as f:
        json.dump(typedoc_json, f, indent=2)
