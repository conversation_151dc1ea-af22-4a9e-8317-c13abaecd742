#!/usr/bin/env python3
"""This script builds all necessary docker images for deploying various forms of
braintrust as standalone docker containers.
"""

import argparse
import os
import platform
import shutil
import subprocess
import sys
import tempfile
import uuid

import git

SCRIPT_DIR = os.path.dirname(__file__)
REPO_ROOT = os.path.join(SCRIPT_DIR, "..")

DEFAULT_IMAGE_REPOSITORY_PREFIX = "public.ecr.aws/braintrust"
DEFAULT_API_FULL_BUILD_S3_BUCKET = "braintrust-standalone-api-full-build"
API_FULL_BUILD_IMAGE = "standalone-api-full-build"

REPOSITORY_NAMES = [
    "redis",
    "postgres",
    "standalone-api",
    "standalone-proxy",
    "standalone-realtime",
    "brainstore",
    "pulse",
    "otelcol",
]


def eprint(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


def get_buildenv(commit):
    env = os.environ.copy()
    env["GIT_COMMIT_HASH"] = commit
    env["COMPOSE_BAKE"] = "true"
    # Enable BuildKit for Docker Compose to support secrets
    env["DOCKER_BUILDKIT"] = "1"
    return env


def run_build(args):
    repo = git.Repo(REPO_ROOT)
    commit = str(repo.commit())
    if not args.allow_dirty:
        assert not repo.is_dirty(), "Cannot build images from a dirty repo. Pass --allow-dirty to permit"
    subprocess.run(
        ["docker", "compose", "-f", "docker_compose_standalone_build.yml", "build"] + args.services,
        cwd=REPO_ROOT,
        env=get_buildenv(commit),
        check=True,
    )


def run_api_full_build(args):
    throwaway_container_name = f"braintrust-standalone-api-full-build-{str(uuid.uuid4())}"
    tmpdirname = tempfile.mkdtemp()
    eprint("Creating container to extract full build")
    subprocess.run(
        ["docker", "create", "--name", throwaway_container_name, API_FULL_BUILD_IMAGE], cwd=tmpdirname, check=True
    )
    try:
        full_build_folder = "braintrust-standalone-api-full-build"
        eprint("Copying full build out of container")
        subprocess.run(
            ["docker", "cp", f"{throwaway_container_name}:/braintrust/", full_build_folder],
            cwd=tmpdirname,
            check=True,
        )
        eprint("Creating archive")
        shutil.make_archive(
            os.path.join(tmpdirname, full_build_folder), "zip", root_dir=tmpdirname, base_dir=full_build_folder
        )
        shutil.move(os.path.join(tmpdirname, f"{full_build_folder}.zip"), args.outfile)
    finally:
        subprocess.run(["docker", "rm", throwaway_container_name])


def _get_platform():
    ret = platform.machine()
    return "aarch64" if ret == "arm64" else ret


def run_publish(args):
    repo = git.Repo(REPO_ROOT)
    commit = args.override_commit or str(repo.commit())
    if not args.allow_dirty:
        assert not repo.is_dirty(), "Cannot publish images from a dirty repo. Pass --allow-dirty to permit"

    image_tag = f"{commit}-{_get_platform()}"

    for repo_basename in REPOSITORY_NAMES:
        if repo_basename in args.skip_repositories:
            continue
        full_repo = os.path.join(args.image_repository_prefix, repo_basename)
        fullpath = f"{full_repo}:{image_tag}"
        eprint(f"Tagging {repo_basename} as {fullpath}")
        subprocess.run(["docker", "tag", repo_basename, fullpath], check=True)
        if not args.dry_run:
            subprocess.run(["docker", "push", fullpath], check=True)


def run_publish_api_full_build(args):
    repo = git.Repo(REPO_ROOT)
    commit = args.override_commit or str(repo.commit())
    if not args.allow_dirty:
        assert not repo.is_dirty(), "Cannot publish api full build from a dirty repo. Pass --allow-dirty to permit"
    image_tag = f"{commit}-{_get_platform()}"

    output_paths = [f"braintrust-standalone-api-full-build-{tag}.zip" for tag in [image_tag]]
    for path in output_paths:
        fullpath = os.path.join(f"s3://{args.s3_bucket}", path)
        eprint(f"Publishing {args.file} to {fullpath}")
        if not args.dry_run:
            subprocess.run(["aws", "s3", "cp", args.file, fullpath], check=True)


def run_combine(args):
    repo = git.Repo(REPO_ROOT)
    if not args.allow_dirty:
        assert not repo.is_dirty(), "Cannot combine images from a dirty repo. Pass --allow-dirty to permit"

    input_tags = args.custom_tags
    if args.tag_sha:
        for arch in args.tag_arch:
            input_tags.append(f"{args.tag_sha}-{arch}")
    assert input_tags, "Must specify input tags using --tag-sha and --tag-arch or --custom-tags"

    output_tags = set()
    primary_output_tag = args.output_tag or args.tag_sha
    assert primary_output_tag, "Must specify output tag using --tag-sha or --output-tag"
    output_tags.add(primary_output_tag)
    if args.tag_latest:
        output_tags.add("latest")
    if args.additional_tags:
        # Filter out empty strings and whitespace-only strings
        additional_tags = [tag.strip() for tag in args.additional_tags.split(",") if tag.strip()]
        output_tags.update(additional_tags)

    for repo_basename in REPOSITORY_NAMES:
        if repo_basename in args.skip_repositories:
            continue
        full_repo = os.path.join(args.repository_prefix, repo_basename)
        input_fullpaths = [f"{full_repo}:{input_tag}" for input_tag in input_tags]
        output_fullpaths = [f"{full_repo}:{output_tag}" for output_tag in output_tags]
        for output_fullpath in output_fullpaths:
            eprint(f"Combining images {input_fullpaths} into one image {output_fullpath}")
            if not args.dry_run:
                subprocess.run(
                    ["docker", "buildx", "imagetools", "create", "-t", output_fullpath] + input_fullpaths, check=True
                )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Braintrust standalone docker",
        description="Build and publish docker images for deploying braintrust in a standalone fashion",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    subparsers = parser.add_subparsers(help="sub-command help")

    parser_build = subparsers.add_parser(
        "build",
        help="build images",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser_build.add_argument(
        "--services",
        nargs="+",
        default=[],
        help=f"The service names in docker_compose_standalone_build.yml to build. If not specified, build all services",
    )
    parser_build.add_argument(
        "--allow-dirty", action="store_true", help="If specified, allow building images from a dirty repo"
    )
    parser_build.set_defaults(func=run_build)

    parser_api_full_build = subparsers.add_parser(
        "api-full-build",
        help=f"Dump the full build of the api image. Make sure you build the {API_FULL_BUILD_IMAGE} image before running this",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser_api_full_build.add_argument(
        "outfile",
        help="path to output api full build zipfile. If not specified, will not emit a zipfile",
    )
    parser_api_full_build.set_defaults(func=run_api_full_build)

    parser_publish = subparsers.add_parser(
        "publish",
        help="publish docker build artifacts",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser_publish.add_argument(
        "--image-repository-prefix",
        default=DEFAULT_IMAGE_REPOSITORY_PREFIX,
        help="Prefix of repository to publish to. Docker must be logged into the repo being published to.",
    )
    parser_publish.add_argument(
        "--skip-repositories", nargs="*", default=[], help=f"Skip repositories out of {REPOSITORY_NAMES}"
    )
    parser_publish.add_argument(
        "--override-commit",
        help=f"Specify an override for the commit used in the tag",
    )
    parser_publish.add_argument(
        "--allow-dirty", action="store_true", help="If specified, allow publishing images from a dirty repo"
    )
    parser_publish.add_argument(
        "--dry-run",
        action="store_true",
        help="If specified, do not actually publish anything",
    )
    parser_publish.set_defaults(func=run_publish)

    parser_publish_api_full_build = subparsers.add_parser(
        "publish-api-full-build",
        help="publish api full build artifacts. Must be logged into amazon s3 with the aws cli",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser_publish_api_full_build.add_argument(
        "--file",
        required=True,
        help="zipfile to publish",
    )
    parser_publish_api_full_build.add_argument(
        "--s3-bucket",
        default=DEFAULT_API_FULL_BUILD_S3_BUCKET,
        help="Name of s3 bucket to publish the zipfile to",
    )
    parser_publish_api_full_build.add_argument(
        "--override-commit",
        help=f"Specify an override for the commit used in the tag",
    )
    parser_publish_api_full_build.add_argument(
        "--allow-dirty", action="store_true", help="If specified, allow publishing api_full_build from a dirty repo"
    )
    parser_publish_api_full_build.add_argument(
        "--dry-run", action="store_true", help="If specified, do not actually publish anything"
    )
    parser_publish_api_full_build.set_defaults(func=run_publish_api_full_build)

    parser_combine = subparsers.add_parser(
        "combine",
        help="combine images into one multi-platform image",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser_combine.add_argument(
        "--repository-prefix",
        default=DEFAULT_IMAGE_REPOSITORY_PREFIX,
        help="Prefix of repository to publish to. Docker must be logged into the repo being published to.",
    )
    parser_combine.add_argument(
        "--skip-repositories", nargs="*", default=[], help=f"Skip repositories out of {REPOSITORY_NAMES}"
    )
    parser_combine.add_argument(
        "--tag-sha",
        help="If specified, along with --tag-arch, combine images with the tag [tag-sha]-[tag-arch].",
    )
    parser_combine.add_argument(
        "--tag-arch",
        nargs="*",
        default=["aarch64", "x86_64"],
        help="List of architectures to combine. Must also specify --tag-sha",
    )
    parser_combine.add_argument(
        "--custom-tags",
        nargs="*",
        default=[],
        help="Custom list of tags to include in combined image",
    )
    parser_combine.add_argument(
        "--output-tag",
        help="Output tag of image. If not specified, defaults to --tag-sha",
    )
    parser_combine.add_argument(
        "--tag-latest",
        action="store_true",
        help="If specified, also tag the output image as latest",
    )
    parser_combine.add_argument(
        "--additional-tags",
        help="Comma-separated list of additional tags to apply to the output image",
    )
    parser_combine.add_argument(
        "--allow-dirty", action="store_true", help="If specified, allow combining images from a dirty repo"
    )
    parser_combine.add_argument(
        "--dry-run",
        action="store_true",
        help="If specified, do not actually publish anything",
    )
    parser_combine.set_defaults(func=run_combine)

    args = parser.parse_args()
    args.func(args)
