#! /usr/bin/env python3
# /// script
# requires-python = ">=3.10"
# dependencies = [
#   "psycopg2-binary",
#   "requests",
# ]
# ///
#
# Script to discover rows that were missed by the postgres -> brainstore
# backfill.

import argparse
import time
from typing import Any, Literal, Optional

import psycopg2


def fetch_missing_sequence_ids_batch(
    logs_table: str,
    pg_url: str,
    start_id: int,
    end_id: int,
    filter_by_object_types: Optional[set],
    filter_by_project_ids: Optional[set],
    filter_by_object_ids: Optional[set],
):
    with psycopg2.connect(pg_url) as conn:
        with conn.cursor() as cursor:
            query_params: list[Any] = [start_id, end_id]
            query = f"""
with
rows0 as (
select
    (case
        when experiment_id is not null then 'experiment'
        when dataset_id is not null then 'dataset'
        when prompt_session_id is not null and right(prompt_session_id, 2) = ':x' then 'playground_logs'
        when project_id is not null and log_id = 'g' then 'project_logs'
        else null
    end) object_type,
    (case
        when experiment_id is not null then 'experiment:' || experiment_id
        when dataset_id is not null then 'dataset:' || dataset_id
        when prompt_session_id is not null and right(prompt_session_id, 2) = ':x' then 'playground_logs:' || left(prompt_session_id, -2)
        when project_id is not null and log_id = 'g' then 'project_logs:' || project_id
    end) object_id,
    project_id,
    id,
    _xact_id xact_id,
    sequence_id
from {logs_table}
where
    sequence_id >= %s and sequence_id <= %s
    and project_id is not null
),
rows1 as (
select
    *,
    (object_id || ':' || id) row_id
from rows0
where object_id is not null
"""

            if filter_by_object_types is not None:
                query += f"\nand object_type = ANY(%s)\n"
                query_params.append(list(filter_by_object_types))

            if filter_by_project_ids is not None:
                query += f"\nand project_id = ANY(%s)\n"
                query_params.append(list(filter_by_project_ids))

            if filter_by_object_ids is not None:
                query += f"\nand object_id = ANY(%s)\n"
                query_params.append(list(filter_by_object_ids))

            query += """
),
rows2 as (
select
    rows1.*,
    (
        exists(
            select 1
            from
                brainstore_global_store_row_id_to_segment_id
                join brainstore_global_store_segment_id_to_wal_entries using (segment_id)
            where
                rows1.row_id = brainstore_global_store_row_id_to_segment_id.row_id
                and rows1.xact_id = brainstore_global_store_segment_id_to_wal_entries.xact_id
        ) or
        exists(
            select 1
            from
                brainstore_global_store_segment_id_to_row_info
                join brainstore_global_store_segment_id_to_wal_entries using (segment_id)
            where
                rows1.row_id = brainstore_global_store_segment_id_to_row_info.row_id
                and rows1.xact_id = brainstore_global_store_segment_id_to_wal_entries.xact_id
        )
    ) was_backfilled
from rows1
)
select array_agg(sequence_id order by sequence_id asc) filter (where not was_backfilled) missing_sequence_ids, count(*) total_num_rows from rows2
"""

            cursor.execute(query, query_params)
            res = cursor.fetchone()
            if res is None:
                return [], 0
            else:
                missing_sequence_ids, total_num_rows = res
                return missing_sequence_ids or [], total_num_rows


def run_find(
    logs_table: Literal["logs", "logs2"],
    pg_url: str,
    start_id_file: str,
    end_id: int,
    missing_sequence_ids_file: str,
    sequence_id_batch_size: int,
    filter_by_object_types: Optional[set],
    filter_by_project_ids: Optional[set],
    filter_by_object_ids: Optional[set],
    limit_by_frontier_sequence_id: bool,
    verbose: bool,
):
    while True:
        with open(start_id_file, "r") as f:
            start_id = int(f.read().strip())

        if start_id > end_id:
            print("Surpassed end ID, exiting.")
            break

        # Fetch the next batch of sequence IDs.
        upper_bound_sequence_id = min(start_id + sequence_id_batch_size, end_id)
        if limit_by_frontier_sequence_id:
            with psycopg2.connect(pg_url) as conn:
                with conn.cursor() as cursor:
                    # The historical sequence IDs are the true frontier sequence
                    # IDs now.
                    cursor.execute(
                        "SELECT historical_full_backfill_sequence_id, historical_full_backfill_sequence_id_2 FROM brainstore_backfill_global_state"
                    )
                    rows = cursor.fetchall()
                    frontier_sequence_id = rows[0][0]
                    frontier_sequence_id_2 = rows[0][1]
                    chosen_frontier_sequence_id = (
                        frontier_sequence_id_2 if logs_table == "logs2" else frontier_sequence_id
                    )
                    if chosen_frontier_sequence_id is not None:
                        upper_bound_sequence_id = min(upper_bound_sequence_id, chosen_frontier_sequence_id)
        if upper_bound_sequence_id <= start_id:
            print("No more progress to make, will sleep for 10 seconds.")
            time.sleep(10)
            continue

        missing_sequence_ids, total_num_rows = fetch_missing_sequence_ids_batch(
            logs_table,
            pg_url,
            start_id,
            upper_bound_sequence_id,
            filter_by_object_types,
            filter_by_project_ids,
            filter_by_object_ids,
        )
        print(
            f"Checking sequence IDs {start_id} to {upper_bound_sequence_id}. Found {len(missing_sequence_ids)} missing rows out of {total_num_rows} rows."
        )
        if verbose:
            print(f"Missing sequence IDs: {missing_sequence_ids}")

        # Store the missing sequence IDs.
        with open(missing_sequence_ids_file, "a") as f:
            for missing_sequence_id in missing_sequence_ids:
                f.write(f"{missing_sequence_id}\n")
        with open(start_id_file, "w") as f:
            f.write(str(upper_bound_sequence_id + 1))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Re-backfill logs data into Brainstore.", formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--logs-table",
        type=str,
        choices=["logs", "logs2"],
        default="logs",
        help="Which logs table to use.",
    )
    parser.add_argument("--pg-url", type=str, required=True, help="URL of the PostgreSQL database.")
    parser.add_argument(
        "--start-id-file",
        type=str,
        required=True,
        help="Filename containing the start sequence ID, inclusive. It should just be a single integer. This file will be overwritten as the script makes progress.",
    )
    parser.add_argument("--end-id", type=int, required=True, help="End sequence ID to process, inclusive.")
    parser.add_argument(
        "--output-missing-sequence-ids-file",
        type=str,
        required=True,
        help="Filename where we accumulate the list of sequence IDs that are missing. This file will be appended to as the script makes progress.",
    )
    parser.add_argument(
        "--sequence-id-batch-size",
        type=int,
        default=20000,
        help="Number of sequence IDs to fetch in each batch.",
    )
    parser.add_argument(
        "--filter-by-object-type",
        type=str,
        nargs="+",
        default=None,
        help="Filter by brainstore object types. If specified, only check objects with these object types. Can specify multiple types.",
    )
    parser.add_argument(
        "--filter-by-project-id",
        type=str,
        nargs="+",
        default=None,
        help="Filter by project IDs. If specified, only check objects with these project IDs. Can specify multiple IDs.",
    )
    parser.add_argument(
        "--filter-by-object-id",
        type=str,
        nargs="+",
        default=None,
        help="Filter by brainstore object IDs. If specified, only check objects with these object IDs. Can specify multiple IDs.",
    )
    parser.add_argument(
        "--limit-by-frontier-sequence-id",
        default=False,
        help="If true, limit each iteration to the frontier sequence ID corresponding to the logs table in use.",
        action="store_true",
    )
    parser.add_argument(
        "--verbose",
        default=False,
        help="Print verbose output.",
        action="store_true",
    )
    args = parser.parse_args()

    # Convert filter lists to sets for efficient lookup
    filter_by_object_types = set(args.filter_by_object_type) if args.filter_by_object_type else None
    filter_by_project_ids = set(args.filter_by_project_id) if args.filter_by_project_id else None
    filter_by_object_ids = set(args.filter_by_object_id) if args.filter_by_object_id else None

    run_find(
        args.logs_table,
        args.pg_url,
        args.start_id_file,
        args.end_id,
        args.output_missing_sequence_ids_file,
        args.sequence_id_batch_size,
        filter_by_object_types,
        filter_by_project_ids,
        filter_by_object_ids,
        args.limit_by_frontier_sequence_id,
        args.verbose,
    )
