#!/usr/bin/env -S uv run --script
# /// script
# dependencies = [
#   "boto3",
# ]
# ///

import sys

import boto3

# This script is used to generate a report of our security group rules for SOC2 and other compliance requirements
# It will print all security group rules grouped by VPC. This lets us snapshot it in a report and mark it up
# with our review comments justifying the rules.

# Fill out our production VPCs
VPC_IDS = ["vpc-0b293149b649d67a5", "vpc-0d318080634ed0037", "vpc-02c6806d92222b645", "vpc-0ea5a173ef752163e"]


def indent(line, level=1):
    return "  " * level + line


def get_vpc_names(ec2, vpc_ids):
    vpcs = ec2.describe_vpcs(VpcIds=vpc_ids)["Vpcs"]
    return {
        v["VpcId"]: next((t["Value"] for t in v.get("Tags", []) if t["Key"] == "Name"), "Unnamed VPC") for v in vpcs
    }


def format_rule_detail(rule, direction, level=3):
    lines = []
    proto = rule.get("IpProtocol")
    from_port = rule.get("FromPort")
    to_port = rule.get("ToPort")

    base = [
        indent(f"Direction: {direction.upper()}", level),
        indent(f"Protocol: {proto}", level),
    ]
    if from_port is not None:
        base.append(indent(f"FromPort: {from_port}", level))
    if to_port is not None:
        base.append(indent(f"ToPort: {to_port}", level))

    expanded = []

    for r in rule.get("IpRanges", []):
        expanded.append(
            "\n".join(
                base
                + [
                    indent(f"Source (IPv4 CIDR): {r.get('CidrIp')}", level),
                    indent(f"Description: {r.get('Description', 'None')}", level),
                ]
            )
        )

    for r in rule.get("Ipv6Ranges", []):
        expanded.append(
            "\n".join(
                base
                + [
                    indent(f"Source (IPv6 CIDR): {r.get('CidrIpv6')}", level),
                    indent(f"Description: {r.get('Description', 'None')}", level),
                ]
            )
        )

    for sg in rule.get("UserIdGroupPairs", []):
        expanded.append(
            "\n".join(
                base
                + [
                    indent(f"Source (SG): {sg.get('GroupId')}", level),
                    indent(f"Description: {sg.get('Description', 'None')}", level),
                ]
            )
        )

    for p in rule.get("PrefixListIds", []):
        expanded.append(
            "\n".join(
                base
                + [
                    indent(f"Source (Prefix List): {p.get('PrefixListId')}", level),
                    indent(f"Description: {p.get('Description', 'None')}", level),
                ]
            )
        )

    if not expanded:
        expanded.append("\n".join(base + [indent("Source: NONE", level)]))

    return "\n\n".join(expanded)


def main():
    ec2 = boto3.client("ec2")

    vpc_name_map = get_vpc_names(ec2, VPC_IDS)
    all_sgs = ec2.describe_security_groups(Filters=[{"Name": "vpc-id", "Values": VPC_IDS}])["SecurityGroups"]

    sgs_by_vpc = {}
    for sg in all_sgs:
        vpc_id = sg["VpcId"]
        sgs_by_vpc.setdefault(vpc_id, []).append(sg)

    for vpc_id in VPC_IDS:
        vpc_name = vpc_name_map.get(vpc_id, "Unknown VPC")
        print(f"\nVPC: {vpc_name} ({vpc_id})")

        for sg in sgs_by_vpc.get(vpc_id, []):
            print(indent(f"Security Group: {sg['GroupName']} ({sg['GroupId']})", 1))
            print(indent(f"Description: {sg.get('Description', 'None')}", 2))

            print(indent("Ingress Rules:", 2))
            if sg.get("IpPermissions"):
                for rule in sg["IpPermissions"]:
                    print(format_rule_detail(rule, "ingress", 3))
            else:
                print(indent("None", 3))

            print(indent("Egress Rules:", 2))
            if sg.get("IpPermissionsEgress"):
                for rule in sg["IpPermissionsEgress"]:
                    print(format_rule_detail(rule, "egress", 3))
            else:
                print(indent("None", 3))
            print()


if __name__ == "__main__":
    main()
