#!/usr/bin/env python3

# This script initializes python and javascript projects in a temp directory,
# and tries to install the latest versions of all the publicly-installable
# braintrust packages. It then runs package dependency checks to ensure the
# installed packages are all consistent with one-another.

import argparse
import json
import subprocess
import tempfile

from braintrust.util import eprint

PYTHON_PACKAGES = [
    "braintrust",
    "autoevals",
]

JS_PACKAGES = [
    "braintrust",
    "autoevals",
    "@braintrust/proxy",
]

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Check public package consistency",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--skip-python", action="store_true", help="If specified, skip checking python deps")
    parser.add_argument("--skip-js", action="store_true", help="If specified, skip checking js deps")
    args = parser.parse_args()

    tmpdirname = tempfile.mkdtemp()
    eprint(f"Initializing test project in {tmpdirname}")

    def run_cmd(cmd_str, **kwargs):
        return subprocess.run(cmd_str.split(), cwd=tmpdirname, check=True, **kwargs)

    def get_py_package_version(pkg):
        res = run_cmd(f"./venv/bin/pip list --format json", capture_output=True)
        info = json.loads(res.stdout.decode())
        return [x for x in info if x["name"] == pkg][0]["version"]

    if not args.skip_python:
        eprint(f"Checking python deps")
        run_cmd("python3 -m venv venv")
        # It seems pip will try to be too clever about selecting versions when
        # installing a group of packages, so we install the latest version of
        # each one individually, and store the package version at the end to
        # check that it's the same.
        pkg_to_version = {}
        for pkg in PYTHON_PACKAGES:
            run_cmd(
                f"./venv/bin/python -m pip install --force-reinstall --no-cache-dir --upgrade --upgrade-strategy eager {pkg}"
            )
            pkg_to_version[pkg] = get_py_package_version(pkg)

        run_cmd("./venv/bin/python -m pip check")
        for pkg in PYTHON_PACKAGES:
            current_version = get_py_package_version(pkg)
            assert (
                pkg_to_version[pkg] == current_version
            ), f"For package {pkg}, expected version {pkg_to_version[pkg]}, got {current_version}"

    if not args.skip_js:
        eprint(f"Checking js deps")
        run_cmd("npm install --save-dev npm-check")
        run_cmd("npm install --save-exact " + " ".join(f"{p}@latest" for p in JS_PACKAGES))
        run_cmd("npx npm-check")
