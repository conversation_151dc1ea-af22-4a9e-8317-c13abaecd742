import { decode } from "next-auth/jwt";

async function main() {
  const token = process.argv[2];
  if (!token) {
    console.error("Session token is required");
    process.exit(1);
  }

  const secret = process.env.NEXTAUTH_SECRET;
  if (!secret) {
    console.error("NEXTAUTH_SECRET is required");
    process.exit(1);
  }

  const decoded = await decode({
    token,
    secret,
    salt: "authjs.session-token",
  });

  console.log(JSON.stringify(decoded, null, 2));
}

main().catch(console.error);
