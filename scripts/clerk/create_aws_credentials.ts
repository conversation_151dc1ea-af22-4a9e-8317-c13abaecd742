import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PutUserPolicyCommand,
  Create<PERSON>ccessKeyCommand,
} from "@aws-sdk/client-iam";
import { config } from "dotenv";

config({ path: __dirname + "/.env.local", encoding: "UTF-8" });

const { AWS_REGION, COGNITO_USER_POOL_ID, AWS_ACCOUNT_ID } = process.env;

if (!AWS_REGION || !COGNITO_USER_POOL_ID || !AWS_ACCOUNT_ID) {
  throw new Error("Missing AWS_REGION or COGNITO_USER_POOL_ID in .env.local");
}

const iamClient = new IAMClient({ region: AWS_REGION });

const POLICY = {
  Version: "2012-10-17",
  Statement: [
    {
      Effect: "Allow",
      Action: "cognito-idp:AdminInitiateAuth",
      Resource: `arn:aws:cognito-idp:${AWS_REGION}:${AWS_ACCOUNT_ID}:userpool/${COGNITO_USER_POOL_ID}`,
    },
  ],
};

async function createIAMUserWithPolicy() {
  const username = `CognitoAdminInitiateAuthUser-${Date.now()}`;

  try {
    // Create IAM user
    await iamClient.send(new CreateUserCommand({ UserName: username }));
    console.log(`IAM user ${username} created successfully.`);

    // Attach inline policy to the user
    await iamClient.send(
      new PutUserPolicyCommand({
        UserName: username,
        PolicyName: "CognitoAdminInitiateAuthPolicy",
        PolicyDocument: JSON.stringify(POLICY),
      }),
    );
    console.log("Inline policy attached successfully.");

    // Create access key for the user
    const { AccessKey } = await iamClient.send(
      new CreateAccessKeyCommand({ UserName: username }),
    );

    if (AccessKey) {
      console.log("Access key created successfully.");
      console.log("Access Key ID:", AccessKey.AccessKeyId);
      console.log("Secret Access Key:", AccessKey.SecretAccessKey);
    } else {
      throw new Error("Failed to create access key.");
    }

    const resp = await fetch("https://api.clerk.com/v1/aws_credentials", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
      },
      body: JSON.stringify({
        access_key_id: AccessKey.AccessKeyId,
        secret_access_key: AccessKey.SecretAccessKey,
        user_pool_ids: [COGNITO_USER_POOL_ID],
      }),
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    console.log(await resp.text());
  } catch (error) {
    console.error("Error:", error);
  }
}

createIAMUserWithPolicy();
