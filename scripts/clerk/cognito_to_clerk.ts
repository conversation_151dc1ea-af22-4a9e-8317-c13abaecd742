// NOTE: This comes from https://gist.github.com/thiskevinwang/45a18beb5b96ab5d548dbe6b199aa22f

import { createClerkClient } from "@clerk/clerk-sdk-node";
import * as IDP from "@aws-sdk/client-cognito-identity-provider";

import { config } from "dotenv";
config({ path: __dirname + "/.env.local", encoding: "UTF-8" });

// NOTE: The IAM user should have permissions roughly equivalent to
// AmazonCognitoReadOnly.
// https://docs.aws.amazon.com/aws-managed-policy/latest/reference/AmazonCognitoReadOnly.html
const { AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION } = process.env;

if (!AWS_ACCESS_KEY_ID || !AWS_SECRET_ACCESS_KEY || !AWS_REGION) {
  throw new Error("Missing AWS credentials");
}

// Separately, double check that the clientId has
// AuthFlow: "ADMIN_USER_PASSWORD_AUTH" enabled.
const { COGNITO_USER_POOL_ID, COGNITO_CLIENT_ID, CLERK_SECRET_KEY } =
  process.env;

const clerk = createClerkClient({
  secretKey: CLERK_SECRET_KEY,
});

function assertNotEmpty<T>(value: T | undefined | null): T {
  if (value === null || value === undefined) {
    throw new Error("Value is empty");
  }
  return value;
}

const idpClient = new IDP.CognitoIdentityProviderClient({
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ACCESS_KEY_ID,
    secretAccessKey: AWS_SECRET_ACCESS_KEY,
  },
});

async function main() {
  let paginationToken: string | undefined = undefined;
  while (true) {
    const usersResponse = await idpClient.send(
      new IDP.ListUsersCommand({
        UserPoolId: COGNITO_USER_POOL_ID,
        PaginationToken: paginationToken,
        Limit: 60,
        Filter: 'cognito:user_status="CONFIRMED"',
      }),
    );
    if (!usersResponse.Users) {
      throw new Error("No users found");
    }

    paginationToken = usersResponse.PaginationToken;
    if (usersResponse.Users.length === 0) {
      break;
    }

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const emails = usersResponse.Users.map((u) =>
      assertNotEmpty(
        assertNotEmpty(u.Attributes?.find((a) => a.Name === "email")).Value,
      ),
    ).filter((e) => e !== undefined) as string[];

    const clerkEmails = await clerk.users.getUserList({
      emailAddress: emails,
    });
    const emailMap = Object.fromEntries(
      clerkEmails.data.map((u) => [u.emailAddresses[0].emailAddress, u]),
    );

    usersLoop: for (const cognitoUser of usersResponse.Users) {
      // Skip unconfirmed users or EXTERNAL_PROVIDER users (like Facebook, Google, etc)
      if (cognitoUser.UserStatus !== "CONFIRMED") {
        console.log(
          "Skipping user: User is not confirmed:",
          cognitoUser.Username,
          cognitoUser.UserStatus,
        );
        continue;
      }

      // This identifier must match the one that use used for sign in on the Cognito user pool.
      // In AWS, that this option is only configurable at the time of user creation.

      // Ultimately, `sub` should be accepting as a sign-in identifier, regardless of the
      // configured sign-in method (ie. email, username, etc)
      // const identifier = cognitoUser.Attributes?.find(
      //   (a) => a.Name === "username"
      // )!.Value!;
      const identifier = cognitoUser.Username;

      if (!identifier) {
        console.log("Skipping user: No identifier found:", cognitoUser);
        continue;
      }

      const email = assertNotEmpty(
        assertNotEmpty(cognitoUser.Attributes?.find((a) => a.Name === "email"))
          .Value,
      );
      if (!email) {
        console.log("Skipping user: No email found:", cognitoUser.Username);
        continue;
      }

      const existing = emailMap[email];
      if (existing) {
        console.log(
          "Skipping user: Email address is already configured in Clerk:",
          email,
        );
        continue;
      }

      try {
        await clerk.users.createUser({
          emailAddress: [email],
          passwordDigest: `awscognito#${COGNITO_USER_POOL_ID}#${COGNITO_CLIENT_ID}#${identifier}`,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          passwordHasher: "awscognito" as any,
        });

        console.log("Created clerk user for:", identifier);
        // Slow down to avoid rate limiting (20 requests per 10 seconds)
        // https://clerk.com/docs/reference/backend-api/tag/Users#operation/CreateUser
        await new Promise((resolve) => setTimeout(resolve, 250));
      } catch (err) {
        if (err.toString().includes("That email address is taken.")) {
          console.log(
            "Skipping user: Email address is already configured in Clerk:",
            email,
          );
          continue;
        } else if (err.toString().includes("Too many requests.")) {
          console.log("Skipping user: Too many requests.");
          await new Promise((resolve) => setTimeout(resolve, 1000));
          continue;
        }
        console.error(err);
        throw err;
      } finally {
      }
    }

    if (!paginationToken) {
      break;
    }
  }
}

main();
