#!/usr/bin/env python3

"""
Usage:

# Start the server with the /reset endpoint enabled
./scripts/connection_reset_server.py --reset

Three ways to trigger connection resets:

1. Using a signal (on Unix systems):
# Send SIGUSR1 to the process to reset all connections
kill -SIGUSR1 <server-pid>
2. Using the /reset endpoint (if started with --reset flag):
# Make a request to the /reset endpoint
curl http://localhost:8000/reset
3. Manually in your client test:
  a. Send a normal request: curl http://localhost:8000/
  b. During the 10-second wait, the server will print its PID
  c. In another terminal, find the server process in the list of Python processes
  d. Check the client socket in your netstat or ss output
  e. Send a reset signal or use one of the above methods
"""

import signal
import socket
import socketserver
import sys
import threading
import time
from http.server import BaseHTTPRequestHandler, HTTPServer

# Global storage for client connections
active_connections = []


class SlowServer(BaseHTTPRequestHandler):
    def do_GET(self):
        # Print received request (with client details)
        client_address = self.client_address
        print(f"Received request from {client_address[0]}:{client_address[1]} for path: {self.path}")

        # Store the client socket for potential forced RST
        client_socket = self.request
        active_connections.append(client_socket)

        # Wait for 10 seconds
        print("Starting 10 second wait...")
        try:
            time.sleep(10)
            print("Wait complete, sending response")

            # Send response
            self.send_response(200)
            self.send_header("Content-type", "text/plain")
            self.end_headers()
            self.wfile.write(b"Response after 10 seconds delay\n")

            # Remove from active connections since we've completed normally
            if client_socket in active_connections:
                active_connections.remove(client_socket)

            print("Response sent successfully")
        except Exception as e:
            print(f"Exception during request handling: {e}")
            if client_socket in active_connections:
                active_connections.remove(client_socket)


class ResetCapableServer(SlowServer):
    def do_GET(self):
        if self.path == "/reset":
            print("Reset request received")
            count = reset_connections()
            self.send_response(200)
            self.send_header("Content-type", "text/plain")
            self.end_headers()
            self.wfile.write(f"Reset {count} connections\n".encode())
        else:
            super().do_GET()


class ThreadedHTTPServer(socketserver.ThreadingMixIn, HTTPServer):
    """Handle requests in a separate thread."""

    daemon_threads = True


def reset_connections():
    """Force reset all active connections to simulate connection reset by peer"""
    count = 0
    for sock in list(active_connections):
        try:
            # Set the SO_LINGER socket option with a timeout of 0
            # This causes the socket to send a RST packet instead of the normal
            # FIN-ACK sequence when closed
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_LINGER, struct.pack("ii", 1, 0))
            sock.close()
            active_connections.remove(sock)
            count += 1
        except Exception as e:
            print(f"Error resetting connection: {e}")
    return count


def run_server(port=8000, server_cls=SlowServer):
    server_address = ("", port)
    httpd = ThreadedHTTPServer(server_address, server_cls)
    print(f"Starting server on port {port}...")

    # Handle signals
    def signal_handler(sig, frame):
        if sig == signal.SIGUSR1:
            # Custom signal to force connection resets
            count = reset_connections()
            print(f"\nReset {count} active connections")
        else:
            # SIGINT, SIGTERM
            print("\nShutting down server...")
            httpd.server_close()
            sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # If on Unix, also handle SIGUSR1
    if hasattr(signal, "SIGUSR1"):
        signal.signal(signal.SIGUSR1, signal_handler)
        print(f"Send SIGUSR1 to PID {os.getpid()} to force connection resets")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Server error: {e}")
    finally:
        httpd.server_close()
        print("Server stopped")


if __name__ == "__main__":
    import argparse
    import os
    import struct

    parser = argparse.ArgumentParser(description="Start a slow HTTP server with connection reset capability")
    parser.add_argument("-p", "--port", type=int, default=8000, help="Port to run the server on (default: 8000)")
    parser.add_argument("--reset", action="store_true", help="Add an endpoint /reset to force connection resets")
    args = parser.parse_args()

    print(f"Server PID: {os.getpid()}")
    run_server(args.port, ResetCapableServer if args.reset else SlowServer)
