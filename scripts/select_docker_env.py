#!/usr/bin/env python3

# This script is used to select the appropriate `.env` file for `make services`.

import subprocess

if __name__ == "__main__":
    volumes_res = subprocess.run(["docker", "volume", "ls", "-q"], check=True, capture_output=True)
    volumes = set(x.strip() for x in volumes_res.stdout.decode().split())
    if "supabase_db_app" in volumes and "supabase_config_app" in volumes:
        print("services/.env")
    else:
        print("services/.env.initial")
