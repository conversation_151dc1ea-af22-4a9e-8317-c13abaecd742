#!/usr/bin/env python3

import sys
from typing import List, Optional


class TabState:
    def __init__(self):
        self.in_tabs = False
        self.current_tabs: List[str] = []
        self.current_content: List[str] = []
        self.current_heading: Optional[str] = None
        self.buffer: List[str] = []  # Buffer for collecting content between headings


def process_line(line: str, state: TabState) -> Optional[str]:
    # Check if we're entering a tab block
    if '<div className="tabs">' in line:
        state.in_tabs = True
        return None

    # Check if we're exiting a tab block
    if state.in_tabs and "</div>" in line:
        state.in_tabs = False

        # If there's content in the buffer, add it to the current content
        if state.buffer and state.current_tabs:
            if len(state.current_content) < len(state.current_tabs):
                state.current_content.append("".join(state.buffer))
            else:
                state.current_content[-1] += "".join(state.buffer)
            state.buffer = []

        # Generate the CodeTabs output
        output = ["\n<CodeTabs>"]  # Add newline before CodeTabs
        for heading, content in zip(state.current_tabs, state.current_content):
            tab_type = "TSTab" if "TypeScript" in heading else "PYTab"
            output.extend(
                [
                    f"\n<{tab_type}>",
                    content.rstrip(),  # Remove trailing whitespace but preserve internal newlines
                    f"\n</{tab_type}>",
                ]
            )
        output.extend(["\n</CodeTabs>", "\n"])  # Add newline after CodeTabs

        # Reset state
        state.current_tabs = []
        state.current_content = []
        state.current_heading = None

        return "".join(output)

    # If we're in a tab block
    if state.in_tabs:
        # Check for heading
        if line.strip().startswith("###"):
            # If we have buffered content and a current heading, add it to current_content
            if state.buffer and state.current_heading:
                if len(state.current_content) < len(state.current_tabs):
                    state.current_content.append("".join(state.buffer))
                else:
                    state.current_content[-1] += "".join(state.buffer)
            state.buffer = []
            state.current_heading = line.strip()
            state.current_tabs.append(state.current_heading)
            return None

        # If we have a heading, collect content in buffer
        if state.current_heading:
            state.buffer.append(line)
            return None

        return None

    # If we're not in a tab block, return the line as-is
    return line


def convert_tabs(input_file: str):
    with open(input_file, "r") as f:
        lines = f.readlines()

    state = TabState()
    output_lines = []

    for line in lines:
        result = process_line(line, state)
        if result is not None:
            output_lines.append(result)

    with open(input_file, "w") as f:
        f.writelines(output_lines)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: convert_tabs.py <file>")
        sys.exit(1)

    convert_tabs(sys.argv[1])
