import { ArgumentParser } from "argparse";
import fs from "fs";
import { login } from "braintrust";

const parser = new ArgumentParser({
  description: "Run a btql query",
});

parser.add_argument("query", {
  help: "The file containing the query to run",
});

async function main() {
  const args = parser.parse_args();
  const queryPath = args.query;
  const queryString = fs.readFileSync(queryPath, "utf8");
  const query = JSON.parse(queryString);

  const state = await login();
  const apiUrl = state.apiUrl;
  const start = Date.now();
  console.log(`${apiUrl}/btql`);
  const results = await fetch(`${apiUrl}/btql`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${state.loginToken}`,
    },
    body: JSON.stringify({
      query,
      use_columnstore: true,
    }),
  });
  if (!results.ok) {
    throw new Error(
      `Failed to run query: ${results.statusText} ${await results.text()}`,
    );
  }
  const data = await results.json();
  console.log(JSON.stringify(data, null, 2));
  console.log(`Time taken: ${Date.now() - start}ms`);
}

main();
