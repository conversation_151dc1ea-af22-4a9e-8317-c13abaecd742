#!/usr/bin/env python3

import json
import os
import subprocess
import sys

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(SCRIPT_DIR, "..", "sdk", "autoevals", "py"))
sys.path.append(os.path.join(SCRIPT_DIR, "..", "sdk", "core", "py", "src"))

if __name__ == "__main__":
    subprocess.call([os.path.join(SCRIPT_DIR, "..", "autoevals", "scripts", "prepare_readme.py"), "js"])
    subprocess.call(f"rm -r {os.path.join(SCRIPT_DIR, '..', 'autoevals', 'jsdist')}/*", shell=True)
    subprocess.call(["npm", "run", "build"], cwd=os.path.join(SCRIPT_DIR, "..", "autoevals"))
