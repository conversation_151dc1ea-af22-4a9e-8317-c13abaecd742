#!/usr/bin/env python3
# /// script
# requires-python = ">=3.10"
# dependencies = [
#   "psycopg2-binary",
# ]
# ///
#
# Script to backfill logs data from source table to destination table using
# INSERT...SELECT with ON CONFLICT DO NOTHING. This allows for resumable
# backfilling of data between tables. Currently hardcoded to only copy data that
# isn't being backfilled to brainstore.

import argparse
import os
import sys
import time
from typing import Optional

import psycopg2


def run_backfill_batch(
    pg_url: str,
    source_table: str,
    destination_table: str,
    start_sequence_id: Optional[int],
    batch_size: int,
) -> tuple[int, int]:
    """
    Run a single batch of INSERT...SELECT.
    Returns (rows_inserted, max_sequence_id_processed).
    """
    with psycopg2.connect(pg_url) as conn:
        with conn.cursor() as cursor:
            # Build the WHERE clause based on whether we have a start_sequence_id
            params = []
            where_clauses = ["(log_id='p' OR (prompt_session_id IS NOT NULL AND NOT (prompt_session_id LIKE %s)))"]
            params.append("%:x")

            if start_sequence_id is not None:
                where_clauses.append("sequence_id > %s")
                params.append(start_sequence_id)

            # Add the upper bound
            upper_bound_sequence_id = None
            where_clauses.append("sequence_id <= %s")
            if start_sequence_id is not None:
                upper_bound_sequence_id = start_sequence_id + batch_size
            else:
                upper_bound_sequence_id = batch_size
            params.append(upper_bound_sequence_id)

            where_clause = " AND ".join(where_clauses)

            # Execute the INSERT...SELECT
            query = f"""
                INSERT INTO {destination_table} (sequence_id, data, audit_data)
                SELECT sequence_id, data, audit_data
                FROM {source_table}
                WHERE {where_clause}
                ON CONFLICT DO NOTHING
            """

            print("Running query:", query, "with params:", params, file=sys.stderr)
            cursor.execute(query, params)
            rows_inserted = cursor.rowcount

    return rows_inserted, upper_bound_sequence_id


def get_table_max_sequence_id(pg_url: str, table: str) -> Optional[int]:
    """Get the maximum sequence_id from a table."""
    with psycopg2.connect(pg_url) as conn:
        with conn.cursor() as cursor:
            cursor.execute(f"SELECT MAX(sequence_id) FROM {table}")
            result = cursor.fetchone()
            return result[0] if result and result[0] is not None else None


def run_backfill(
    pg_url: str,
    source_table: str,
    destination_table: str,
    start_sequence_id_file: str,
    end_sequence_id: int,
    batch_size: int,
):
    """Main backfill loop."""
    # Read initial start_sequence_id from file
    start_sequence_id = None
    if os.path.exists(start_sequence_id_file):
        with open(start_sequence_id_file, "r") as f:
            content = f.read().strip()
            if content:
                start_sequence_id = int(content)

    print(f"Starting backfill from {source_table} to {destination_table}")
    print(f"Start sequence ID: {start_sequence_id if start_sequence_id is not None else 'beginning'}")
    print(f"End sequence ID: {end_sequence_id}")
    print(f"Batch size: {batch_size}")
    print()

    total_rows_inserted = 0
    batch_count = 0

    while True:
        # Check if we've reached the end
        if start_sequence_id is not None and start_sequence_id >= end_sequence_id:
            print(f"\nReached end sequence ID {end_sequence_id}. Backfill complete!")
            break

        batch_start_time = time.time()

        # Run the batch
        try:
            rows_inserted, max_seq_processed = run_backfill_batch(
                pg_url=pg_url,
                source_table=source_table,
                destination_table=destination_table,
                start_sequence_id=start_sequence_id,
                batch_size=batch_size,
            )

            batch_duration = time.time() - batch_start_time
            total_rows_inserted += rows_inserted
            batch_count += 1

            # Calculate progress
            if start_sequence_id is not None:
                progress_pct = min(100.0, (max_seq_processed / end_sequence_id) * 100)
            else:
                progress_pct = min(100.0, (max_seq_processed / end_sequence_id) * 100)

            print(
                f"Batch {batch_count}: sequence_id {start_sequence_id or 0} -> {max_seq_processed} "
                f"({rows_inserted} rows inserted in {batch_duration:.2f}s) "
                f"Progress: {progress_pct:.1f}%"
            )

            # Update the start_sequence_id file
            with open(start_sequence_id_file, "w") as f:
                f.write(str(max_seq_processed))

            # Update start_sequence_id for next iteration
            start_sequence_id = max_seq_processed

            # If no rows were inserted and we haven't reached the end, we might be done
            if rows_inserted == 0 and max_seq_processed >= end_sequence_id:
                print(f"\nNo more rows to process. Backfill complete!")
                break

        except Exception as e:
            print(f"\nError during batch processing: {e}")
            print("Retrying in 5 seconds...")
            time.sleep(5)
            continue

    print(f"\nBackfill summary:")
    print(f"Total batches processed: {batch_count}")
    print(f"Total rows inserted: {total_rows_inserted}")


def main():
    parser = argparse.ArgumentParser(
        description="Backfill logs data from source to destination table",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument("--pg-url", type=str, required=True, help="PostgreSQL connection URL")

    parser.add_argument("--source-table", type=str, required=True, help="Source table name")

    parser.add_argument("--destination-table", type=str, required=True, help="Destination table name")

    parser.add_argument(
        "--start-sequence-id-file",
        type=str,
        required=True,
        help="File containing the starting sequence ID (exclusive, will be updated as progress is made). If the file doesn't exist it will start from the beginning",
    )

    parser.add_argument(
        "--end-sequence-id",
        type=int,
        required=True,
        help="Ending sequence ID (inclusive). If -1, use the max sequence id from the source table",
    )

    parser.add_argument(
        "--batch-size", type=int, default=10000, help="Number of sequence IDs to process in each batch"
    )

    args = parser.parse_args()

    # Validate that start_sequence_id_file directory exists
    start_file_dir = os.path.dirname(args.start_sequence_id_file)
    if start_file_dir and not os.path.exists(start_file_dir):
        print(f"Error: Directory for start_sequence_id_file does not exist: {start_file_dir}")
        sys.exit(1)

    # If end_sequence_id is -1, get the max from source table
    end_sequence_id = args.end_sequence_id
    if end_sequence_id == -1:
        max_seq = get_table_max_sequence_id(args.pg_url, args.source_table)
        if max_seq is None:
            print(f"Error: Could not determine max sequence_id from {args.source_table}")
            sys.exit(1)
        end_sequence_id = max_seq
        print(f"Using max sequence_id from {args.source_table}: {end_sequence_id}")

    run_backfill(
        pg_url=args.pg_url,
        source_table=args.source_table,
        destination_table=args.destination_table,
        start_sequence_id_file=args.start_sequence_id_file,
        end_sequence_id=end_sequence_id,
        batch_size=args.batch_size,
    )


if __name__ == "__main__":
    main()
