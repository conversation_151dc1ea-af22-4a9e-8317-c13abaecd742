#!/usr/bin/env python3

import os
import subprocess
import tempfile


def getenv():
    env = os.environ.copy()
    env["BRAINTRUST_TYPESPECS_MODE"] = "stainless"


if __name__ == "__main__":
    subprocess.run(["pnpm", "build", "--filter", "@braintrust/openapi-deployment"], check=True)
    subprocess.run(["pnpm", "build", "--filter", "@braintrust/stainless-deployment"], check=True)
    tmpdir = tempfile.mkdtemp()
    openapi_file = os.path.join(tmpdir, "openapi.json")
    with open(openapi_file, "w") as f:
        subprocess.run(
            ["npx", "generate-openapi-spec-stainless", "api", "--format", "json"], stdout=f, check=True, env=getenv()
        )
    config_file = os.path.join(tmpdir, "openapi.stainless.yml")
    with open(config_file, "w") as f:
        subprocess.run(
            ["npx", "generate-stainless-config", "--openapi-spec", openapi_file, "--format", "yaml"],
            stdout=f,
            check=True,
        )

    print(f"Wrote config files to {tmpdir}")
