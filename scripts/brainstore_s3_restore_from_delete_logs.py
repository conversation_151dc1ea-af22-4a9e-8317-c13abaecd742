# /// script
# dependencies = [
#   "boto3",
# ]
# ///

import argparse
import re

import boto3
from botocore.client import BaseClient
from botocore.exceptions import ClientError

"""
Restore deleted objects in a versioned S3 bucket by reading paths from delete logs
and removing the delete markers at those paths.

Usage:
    uv run scripts/brainstore_s3_restore_from_delete_logs.py \
        --bucket XXXXX \
        --prefix brainstore/index/delete-ops/ \
        --start  1735689600
"""


def parse_args():
    parser = argparse.ArgumentParser(description="Restore deleted objects in a versioned S3 bucket using delete logs.")
    parser.add_argument("--bucket", required=True, help="S3 bucket name")
    parser.add_argument(
        "--prefix",
        required=True,
        help="Prefix where delete logs live. This is probably something like brainstore/index/delete-ops/",
    )
    parser.add_argument(
        "--start", type=int, required=True, help="Start timestamp in seconds. Will be padded to 12 digits for lookup."
    )
    parser.add_argument(
        "--end",
        type=int,
        required=False,
        help="End timestamp in seconds. If not provided, process all logs after start timestamp.",
    )
    parser.add_argument(
        "--verbose",
        default=False,
        help="Print verbose output.",
        action="store_true",
    )
    return parser.parse_args()


def list_delete_log_keys(
    s3,
    bucket: str,
    prefix: str,
    start_ts: int,
    end_ts: int | None = None,
    verbose: bool = False,
):
    """
    Yield S3 keys for delete logs whose timestamp (parsed from the fname) is between start_ts and end_ts.
    """
    paginator = s3.get_paginator("list_objects_v2")
    found_logs = 0
    for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
        for obj in page.get("Contents", []):
            key = obj["Key"]
            fname = key.rsplit("/", 1)[-1]
            m = re.match(r"^(\d{12})\..+$", fname)
            if not m:
                if verbose:
                    print(f"WARNING: Found file with name that does not match delete_log regex: {key}")
                continue
            ts = int(m.group(1))
            if start_ts <= ts and (end_ts is None or ts <= end_ts):
                print(f"Found delete log: {key} (timestamp: {ts})")
                found_logs += 1
                yield key
    print(f"Total delete logs found: {found_logs}")


def restore_object(
    s3,
    bucket: str,
    obj_key: str,
    verbose: bool = False,
) -> bool:
    """
    Remove delete-marker versions for obj_key to restore the previously live object.
    Returns True if the object was restored, False if no delete marker was found.
    """
    if verbose:
        print(f"Attempting to restore: {obj_key}")
    versions = s3.list_object_versions(Bucket=bucket, Prefix=obj_key)
    removed = False
    for dm in versions.get("DeleteMarkers", []):
        if dm["Key"] == obj_key:
            vid = dm["VersionId"]
            if verbose:
                print(f"  → Removing delete marker: {obj_key} (version {vid})")
            s3.delete_object(Bucket=bucket, Key=obj_key, VersionId=vid)
            removed = True
    if verbose and not removed:
        print(f"  → No delete marker found for {obj_key}, skipping.")
    return removed


def restore_from_delete_log(
    s3,
    bucket: str,
    delete_log_key: str,
    verbose: bool = False,
) -> int:
    """
    Read a delete log and restore the objects at the listed prefixes.
    """
    resp = s3.get_object(Bucket=bucket, Key=delete_log_key)
    body = resp["Body"].read().decode("utf-8")
    lines = body.splitlines()
    print(f"Processing log {delete_log_key} ({len(lines)} entries)...")

    restored_count = 0
    for line_num, line in enumerate(lines, 1):
        key = line.strip()
        if key:
            print(f"[{line_num}/{len(lines)}] Processing entry: {key}")
            if restore_object(s3, bucket, key, verbose):
                restored_count += 1

    print(
        f"Finished processing delete log at `{delete_log_key}`. Restored {restored_count} objects out of {len(lines)} paths in the log."
    )
    return restored_count


def main():
    args = parse_args()
    s3 = boto3.client("s3")

    print(f"Scanning s3://{args.bucket}/{args.prefix} with start: {args.start}, end: {args.end}")

    log_count = 0
    restored_count = 0
    for log_key in list_delete_log_keys(s3, args.bucket, args.prefix, args.start, args.end, args.verbose):
        log_count += 1
        restored_count += restore_from_delete_log(s3, args.bucket, log_key, args.verbose)

    print(f"Restoration complete. Processed {log_count} delete log files and restored {restored_count} files.")


if __name__ == "__main__":
    main()
