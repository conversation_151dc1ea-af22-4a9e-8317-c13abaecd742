# Print out the turborepo cache hash for the given package.

import json
import subprocess
import sys

if __name__ == "__main__":
    package = sys.argv[1]
    turbo_res = subprocess.run(
        ["npx", "turbo", "run", "build", "--dry-run", "json", "--filter", package], check=True, capture_output=True
    )
    turbo_data = json.loads(turbo_res.stdout.decode())
    print([t for t in turbo_data["tasks"] if t["package"] == package][0]["hash"])
