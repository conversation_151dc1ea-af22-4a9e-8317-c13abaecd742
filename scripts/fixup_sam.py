#!/usr/bin/env python3

import json
import os
import sys

import yaml

try:
    from yaml import <PERSON><PERSON><PERSON> as Dumper
    from yaml import <PERSON><PERSON>oader as Loader
except ImportError:
    from yaml import Dumper, Loader


if __name__ == "__main__":
    target_file = sys.argv[1]

    with open(target_file, "r") as f:
        contents = f.read()

    with open("sam-packed-original.yaml", "w") as f:
        f.write(contents)

    contents = contents.replace("${APIHandler.Arn}", "${APIHandler.Arn}:live2").replace(
        "${ApiAuthorizer.Arn}", "${ApiAuthorizer.Arn}:live2"
    )

    template = yaml.load(contents, Loader=Loader)

    template["Resources"]["APIHandlerInvokePermission"]["Properties"]["FunctionName"] = {"Ref": "APIHandler.Alias"}

    # Add MethodSettings to RestAPI
    if "RestAPI" in template["Resources"]:
        template["Resources"]["RestAPI"]["Properties"]["MethodSettings"] = [
            {"HttpMethod": "*", "ResourcePath": "/*", "MetricsEnabled": True}
        ]

    with open(target_file, "w") as f:
        f.write(yaml.dump(template, Dumper=Dumper))
