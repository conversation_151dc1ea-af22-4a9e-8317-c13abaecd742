import * as readline from "node:readline/promises";

const API_KEY = process.env.BRAINTRUST_API_KEY;
const API_URL = "https://api.braintrust.dev";
const ORG_NAME = process.env.ORG_NAME || "Notion";
const REVERSE_RESTRICTION = !!process.env.REVERSE_RESTRICTION;

async function getOrg() {
  const resp = await fetch(`${API_URL}/v1/organization?org_name=${ORG_NAME}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${API_KEY}`,
    },
  });
  if (!resp.ok) {
    throw new Error(`Failed to fetch organizations: ${await resp.text()}`);
  }

  const allOrgs = (await resp.json())["objects"];
  if (allOrgs.length !== 1) {
    throw new Error(
      `Expected exactly one org called ${ORG_NAME}. Got\n", allOrgs`,
    );
  }
  return allOrgs[0];
}

async function getEngineersGroup() {
  const resp = await fetch(
    `${API_URL}/v1/group?group_name=Engineers&org_name=${ORG_NAME}`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
      },
    },
  );
  if (!resp.ok) {
    throw new Error(`Failed to fetch Engineers group: ${await resp.text()}`);
  }

  const allGroups = (await resp.json())["objects"];
  if (allGroups.length !== 1) {
    throw new Error(
      `Expected exactly one group called Engineers. Got\n", allGroups`,
    );
  }
  return allGroups[0];
}

async function getEngineerRole() {
  const resp = await fetch(`${API_URL}/v1/role?role_name=Engineer`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${API_KEY}`,
    },
  });
  if (!resp.ok) {
    throw new Error(`Failed to fetch Engineer role: ${await resp.text()}`);
  }

  const allRoles = (await resp.json())["objects"];
  if (allRoles.length !== 1) {
    throw new Error(
      `Expected exactly one role called Engineer. Got\n", allRoles`,
    );
  }
  return allRoles[0];
}

async function doEngineerRoleAcl({ org, engineersGroup, engineerRole }) {
  if (REVERSE_RESTRICTION) {
    const resp = await fetch(`${API_URL}/v1/acl`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        object_type: "org_project",
        object_id: org.id,
        group_id: engineersGroup.id,
        role_id: engineerRole.id,
      }),
    });
    if (!resp.ok) {
      throw new Error(
        `Failed to grant Engineer role to org ${ORG_NAME}: ${await resp.text()}`,
      );
    }
  } else {
    let resp = await fetch(
      `${API_URL}/v1/acl?object_type=org_project&object_id=${org.id}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${API_KEY}`,
        },
      },
    );
    if (!resp.ok) {
      throw new Error(`Failed to fetch ACLs: ${await resp.text()}`);
    }
    const matchingAcl = (await resp.json())["objects"].find(
      (acl) =>
        acl.group_id === engineersGroup.id &&
        acl.role_id === engineerRole.id &&
        !acl.restrict_object_type,
    );
    if (!matchingAcl) {
      throw new Error(
        `Could not find Engineer-role grant on Engineers group for org ${ORG_NAME}`,
      );
    }
    resp = await fetch(`${API_URL}/v1/acl/${matchingAcl.id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
      },
    });
    if (!resp.ok) {
      throw new Error(
        `Failed to delete Engineer grant on org ${ORG_NAME}: ${await resp.text()}`,
      );
    }
  }
}

async function doCreateProjectAcl({ org, engineersGroup }) {
  if (REVERSE_RESTRICTION) {
    let resp = await fetch(
      `${API_URL}/v1/acl?object_type=org_project&object_id=${org.id}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${API_KEY}`,
        },
      },
    );
    if (!resp.ok) {
      throw new Error(`Failed to fetch ACLs: ${await resp.text()}`);
    }
    const matchingAcl = (await resp.json())["objects"].find(
      (acl) =>
        acl.group_id === engineersGroup.id &&
        acl.permission === "create" &&
        acl.restrict_object_type === "project",
    );
    if (!matchingAcl) {
      throw new Error(
        `Could not find create project grant on Engineers group for org ${ORG_NAME}`,
      );
    }
    resp = await fetch(`${API_URL}/v1/acl/${matchingAcl.id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
      },
    });
    if (!resp.ok) {
      throw new Error(
        `Failed to delete create project grant on org ${ORG_NAME}: ${await resp.text()}`,
      );
    }
  } else {
    const resp = await fetch(`${API_URL}/v1/acl`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        object_type: "org_project",
        object_id: org.id,
        group_id: engineersGroup.id,
        permission: "create",
        restrict_object_type: "project",
      }),
    });
    if (!resp.ok) {
      throw new Error(
        `Failed to grant create project to org ${ORG_NAME}: ${await resp.text()}`,
      );
    }
  }
}

async function helper(rl) {
  const org = await getOrg();
  const engineerRole = await getEngineerRole();
  const engineersGroup = await getEngineersGroup();

  const confirmProjects = await rl.question(
    REVERSE_RESTRICTION
      ? "Do you want to widen the Engineers group to have access to all projects in the org? (yes/no): "
      : "Do you want to restrict the Engineers group to only have permission to create their own projects? (yes/no): ",
  );
  if (confirmProjects.toLowerCase() !== "yes") {
    console.log("Exiting...");
    return;
  }
  if (REVERSE_RESTRICTION) {
    await doEngineerRoleAcl({ org, engineersGroup, engineerRole });
    await doCreateProjectAcl({ org, engineersGroup });
  } else {
    await doCreateProjectAcl({ org, engineersGroup });
    await doEngineerRoleAcl({ org, engineersGroup, engineerRole });
  }
}

async function main() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  try {
    await helper(rl);
  } finally {
    rl.close();
  }
}

main();
