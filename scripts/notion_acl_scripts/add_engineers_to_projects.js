import * as readline from "node:readline/promises";

const API_KEY = process.env.BRAINTRUST_API_KEY;
const INCLUDE_PROJECTS_WITH_NAME = process.env.INCLUDE_PROJECTS_WITH_NAME;
const EXCLUDE_PROJECTS_WITH_NAME = process.env.EXCLUDE_PROJECTS_WITH_NAME;
const REMOVE_ACLS = !!process.env.REMOVE_ACLS;

const API_URL = "https://api.braintrust.dev";
const ORG_NAME = process.env.ORG_NAME || "Notion";

async function getEngineersGroup() {
  const resp = await fetch(
    `${API_URL}/v1/group?group_name=Engineers&org_name=${ORG_NAME}`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${API_KEY}`,
      },
    },
  );
  if (!resp.ok) {
    throw new Error(`Failed to fetch Engineers group: ${await resp.text()}`);
  }

  const allGroups = (await resp.json())["objects"];
  if (allGroups.length !== 1) {
    throw new Error(
      `Expected exactly one group called Engineers. Got\n", allGroups`,
    );
  }
  return allGroups[0];
}

async function getEngineerRole() {
  const resp = await fetch(`${API_URL}/v1/role?role_name=Engineer`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${API_KEY}`,
    },
  });
  if (!resp.ok) {
    throw new Error(`Failed to fetch Engineer role: ${await resp.text()}`);
  }

  const allRoles = (await resp.json())["objects"];
  if (allRoles.length !== 1) {
    throw new Error(
      `Expected exactly one role called Engineer. Got\n", allRoles`,
    );
  }
  return allRoles[0];
}

async function getProjects() {
  const resp = await fetch(`${API_URL}/v1/project?org_name=${ORG_NAME}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${API_KEY}`,
    },
  });
  if (!resp.ok) {
    throw new Error(`Failed to fetch projects: ${await resp.text()}`);
  }

  const includeRegex = INCLUDE_PROJECTS_WITH_NAME
    ? new RegExp(INCLUDE_PROJECTS_WITH_NAME, "i")
    : null;
  const excludeRegex = EXCLUDE_PROJECTS_WITH_NAME
    ? new RegExp(EXCLUDE_PROJECTS_WITH_NAME, "i")
    : null;
  const allProjects = (await resp.json())["objects"].filter((project) => {
    if (includeRegex && !includeRegex.test(project.name)) {
      return false;
    }
    if (excludeRegex && excludeRegex.test(project.name)) {
      return false;
    }
    return true;
  });
  return allProjects;
}

async function grantAcls({ engineerRole, engineersGroup, projects }) {
  for (const project of projects) {
    console.log(
      `${REMOVE_ACLS ? "Removing" : "Adding"} Engineer role ${REMOVE_ACLS ? "from" : "to"} project ${project.name}`,
    );
    if (REMOVE_ACLS) {
      let resp = await fetch(
        `${API_URL}/v1/acl?object_type=project&object_id=${project.id}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${API_KEY}`,
          },
        },
      );
      if (!resp.ok) {
        throw new Error(
          `Failed to fetch ACLs on project ${project.name}: ${await resp.text()}`,
        );
      }
      const allAcls = (await resp.json())["objects"];
      const matchingAcl = allAcls.find((acl) => {
        return (
          acl.group_id === engineersGroup.id &&
          acl.role_id === engineerRole.id &&
          !acl.restrict_object_type
        );
      });
      if (!matchingAcl) {
        console.log(
          `Could not find Engineer grant on project ${project.name}. Skipping...`,
        );
        continue;
      }
      resp = await fetch(`${API_URL}/v1/acl/${matchingAcl.id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${API_KEY}`,
        },
      });
      if (!resp.ok) {
        throw new Error(
          `Failed to delete Engineer grant on project ${project.name}: ${await resp.text()}`,
        );
      }
    } else {
      const resp = await fetch(`${API_URL}/v1/acl`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          object_type: "project",
          object_id: project.id,
          group_id: engineersGroup.id,
          role_id: engineerRole.id,
        }),
      });
      if (!resp.ok) {
        throw new Error(
          `Failed to grant Engineer role to project ${project.name}: ${await resp.text()}`,
        );
      }
    }
  }
}

async function helper(rl) {
  const engineerRole = await getEngineerRole();
  const engineersGroup = await getEngineersGroup();
  const projects = await getProjects();
  console.log("Got the following projects:\n", projects);

  const confirmProjects = await rl.question(
    `Do you want to ${REMOVE_ACLS ? "remove" : "add"} engineers ${REMOVE_ACLS ? "from" : "to"} these projects? (yes/no): `,
  );
  if (confirmProjects.toLowerCase() !== "yes") {
    console.log("Exiting...");
    return;
  }
  await grantAcls({ engineerRole, engineersGroup, projects });
}

async function main() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  try {
    await helper(rl);
  } finally {
    rl.close();
  }
}

main();
