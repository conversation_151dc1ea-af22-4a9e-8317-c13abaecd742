// Script create_brainstore_license creates a Brainstore license for an organization.

import pg from "pg";
import argparse from "argparse";
import { signAsync } from "@noble/ed25519";

// TODO(sachin): figure out how to factor this out into @braintrust/local/dev.
// It also needs to be tested.
export async function createBrainstoreLicense(
  db: pg.Client,
  privateKey: Buffer,
  orgId: string,
) {
  const payload = {
    licenseKeyVersion: 1,
    orgId,
    created: new Date().toISOString(),
  };
  const payloadBytes = Buffer.from(
    JSON.stringify(payload, Object.keys(payload).sort()),
  );

  const signature = Buffer.from(
    await signAsync(payloadBytes, privateKey),
  ).toString("base64");

  const license = Buffer.from(
    JSON.stringify({ ...payload, signature }),
  ).toString("base64");

  const result = await db.query(
    "insert into brainstore_licenses (license, org_id) values ($1, $2) returning id",
    [license, orgId],
  );
  return result.rows[0].id;
}

async function main() {
  const parser = new argparse.ArgumentParser({
    description: "Create a Brainstore license for an organization",
  });

  parser.add_argument("orgId", {
    help: "The ID of the organization to create a license for",
  });

  parser.add_argument("-d", "--db-url", {
    help: "The URL of the database to connect to",
    required: true,
    default: process.env.DB_URL,
  });

  parser.add_argument("-k", "--key", {
    help: "The ed25519 private key to use to sign the license payload",
    required: true,
  });

  const args = parser.parse_args();

  const key = Buffer.from(args.key, "base64");

  const client = new pg.Client({ connectionString: args.db_url });
  try {
    await client.connect();
    const licenseId = await createBrainstoreLicense(client, key, args.orgId);
    console.log("Created license", licenseId);
  } finally {
    await client.end();
  }
}

main().catch(console.error);
