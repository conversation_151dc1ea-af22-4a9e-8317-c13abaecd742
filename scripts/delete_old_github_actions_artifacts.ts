import { Octokit } from "@octokit/rest";
import * as util from "./github_api_util";

const OWNER = util.ORG;
const REPO = util.MAIN_REPO;
const NUM_DAYS = (process.env.NUM_DAYS && Number(process.env.NUM_DAYS)) || 10;
const DRY_RUN = (process.env.DRY_RUN ?? "").trim() === "1";
const BATCH_SIZE = 100;

// Function to delete old artifacts
async function deleteOldArtifacts(client: Octokit) {
  const now = new Date();
  const numDaysInMillis = NUM_DAYS * 24 * 60 * 60 * 1000;

  for (let page = 1; ; ++page) {
    // Fetch all artifacts
    const artifactsResponse = await client.actions.listArtifactsForRepo({
      owner: OWNER,
      repo: REPO,
      page,
      per_page: BATCH_SIZE,
    });

    const artifacts = artifactsResponse.data.artifacts;
    if (artifacts.length === 0) {
      break;
    }

    console.log(`Found ${artifacts.length} artifacts on page ${page}.`);

    // Filter artifacts older than NUM_DAYS days
    const oldArtifacts = artifacts.filter((artifact) => {
      if (!artifact.created_at) {
        return false;
      }
      const createdAt = new Date(artifact.created_at);
      return now.getTime() - createdAt.getTime() > numDaysInMillis;
    });

    console.log(
      `Found ${oldArtifacts.length} artifacts older than ${NUM_DAYS} days.`,
    );
    if (DRY_RUN) {
      continue;
    }
    await Promise.all(
      oldArtifacts.map(async (artifact) => {
        console.log(`Deleting artifact: ${artifact.name} (ID: ${artifact.id})`);
        try {
          await client.actions.deleteArtifact({
            owner: OWNER,
            repo: REPO,
            artifact_id: artifact.id,
          });
          console.log(`Deleted artifact: ${artifact.name}`);
        } catch (error) {
          console.error(`Failed to delete artifact ${artifact.name}:`, error);
        }
      }),
    );
  }
  if (!DRY_RUN) {
    console.log("Old artifacts cleanup completed.");
  }
}

async function main() {
  const client = new Octokit({
    auth: process.env.GITHUB_API_KEY,
  });
  await deleteOldArtifacts(client);
}

main();
