#! /usr/bin/env python3
# /// script
# requires-python = ">=3.10"
# dependencies = [
#   "psycopg2-binary",
#   "requests",
# ]
# ///
#
# Script to re-run process WAL into brainstore over a sequence ID range of data
# in the logs table. This can be used to re-backfill data that may have been
# missed by the normal backfiller.

import argparse
import json
import os
import tempfile
import time
from typing import Literal, Optional, Tuple

import psycopg2
import requests


def postgres_object_id_to_brainstore_object_type_object_id(object_id: str) -> Optional[Tuple[str, str]]:
    if object_id.startswith("experiment:"):
        return ("experiment", object_id[len("experiment:") :])
    elif object_id.startswith("dataset:"):
        return ("dataset", object_id[len("dataset:") :])
    elif object_id.startswith("prompt_session:") and object_id.endswith(":x"):
        return ("playground_logs", object_id[len("prompt_session:") : -2])
    elif object_id.startswith("global_log:"):
        return ("project_logs", object_id[len("global_log:") :])
    else:
        return None


def fetch_tracked_objects_batch(
    logs_table: str,
    pg_url: str,
    start_id: int,
    end_id: int,
    filter_tracked_option: Literal["only_tracking_entry", "only_brainstore_object"],
    filter_by_object_types: Optional[set],
    filter_by_project_ids: Optional[set],
    filter_by_object_ids: Optional[set],
):
    with psycopg2.connect(pg_url) as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                f"""
                SELECT
                    make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) object_id,
                    min(project_id) project_id
                FROM {logs_table}
                WHERE
                    sequence_id >= %s AND sequence_id <= %s
                    and project_id is not null
                    and make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) is not null
                GROUP BY 1
            """,
                (start_id, end_id),
            )
            rows = cursor.fetchall()
            postgres_object_type_project_ids = [(row[0], row[1]) for row in rows]

    object_infos = []
    for postgres_object_id, project_id in postgres_object_type_project_ids:
        res = postgres_object_id_to_brainstore_object_type_object_id(postgres_object_id)
        if res is None:
            continue

        object_type, object_id = res
        brainstore_object_id = f"{object_type}:{object_id}"
        if filter_by_object_types is not None and object_type not in filter_by_object_types:
            continue
        if filter_by_project_ids is not None and project_id not in filter_by_project_ids:
            continue
        if filter_by_object_ids is not None and brainstore_object_id not in filter_by_object_ids:
            continue

        object_infos.append(
            dict(
                postgres_object_id=postgres_object_id,
                project_id=project_id,
                object_type=object_type,
                brainstore_object_id=brainstore_object_id,
            )
        )

    if filter_tracked_option == "only_brainstore_object":
        with psycopg2.connect(pg_url) as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    WITH all_tuples AS (
                        SELECT *
                        FROM unnest(%s::text[], %s::text[], %s::text[]) AS t(project_id, object_type, brainstore_object_id)
                    )
                    SELECT brainstore_object_id
                    FROM brainstore_backfill_tracked_objects_to_brainstore_objects
                    WHERE (project_id, object_type, brainstore_object_id) in (SELECT project_id, object_type, brainstore_object_id FROM all_tuples)
                """,
                    (
                        [obj["project_id"] for obj in object_infos],
                        [obj["object_type"] for obj in object_infos],
                        [obj["brainstore_object_id"] for obj in object_infos],
                    ),
                )
                rows = cursor.fetchall()
                tracked_object_ids = set([row[0] for row in rows])
                object_infos = [obj for obj in object_infos if obj["brainstore_object_id"] in tracked_object_ids]
    elif filter_tracked_option == "only_tracking_entry":
        with psycopg2.connect(pg_url) as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    WITH all_tuples AS (
                        SELECT *
                        FROM unnest(%s::text[], %s::text[], %s::text[]) AS t(project_id, object_type, brainstore_object_id)
                    )
                    SELECT all_tuples.brainstore_object_id
                    FROM all_tuples join brainstore_backfill_tracked_objects using (project_id, object_type)
                """,
                    (
                        [obj["project_id"] for obj in object_infos],
                        [obj["object_type"] for obj in object_infos],
                        [obj["brainstore_object_id"] for obj in object_infos],
                    ),
                )
                rows = cursor.fetchall()
                tracked_object_ids = set([row[0] for row in rows])
                object_infos = [obj for obj in object_infos if obj["brainstore_object_id"] in tracked_object_ids]

    return object_infos


def run_backfill(
    logs_table: Literal["logs", "logs2"],
    pg_url: str,
    brainstore_url: str,
    start_id_file: str,
    end_id: int,
    sequence_id_batch_size: int,
    object_id_batch_size: int,
    filter_by_object_types: Optional[set],
    filter_by_project_ids: Optional[set],
    filter_by_object_ids: Optional[set],
    filter_tracked_option: Literal["only_tracking_entry", "only_brainstore_object"],
    limit_by_frontier_sequence_id: bool,
    verbose: bool,
):
    while True:
        with open(start_id_file, "r") as f:
            start_id = int(f.read().strip())

        if start_id > end_id:
            print("Surpassed end ID, exiting.")
            break

        # Fetch the next batch of sequence IDs.
        upper_bound_sequence_id = min(start_id + sequence_id_batch_size, end_id)
        if limit_by_frontier_sequence_id:
            with psycopg2.connect(pg_url) as conn:
                with conn.cursor() as cursor:
                    # The historical sequence IDs are the true frontier sequence
                    # IDs now.
                    cursor.execute(
                        "SELECT historical_full_backfill_sequence_id, historical_full_backfill_sequence_id_2 FROM brainstore_backfill_global_state"
                    )
                    rows = cursor.fetchall()
                    frontier_sequence_id = rows[0][0]
                    frontier_sequence_id_2 = rows[0][1]
                    chosen_frontier_sequence_id = (
                        frontier_sequence_id_2 if logs_table == "logs2" else frontier_sequence_id
                    )
                    if chosen_frontier_sequence_id is not None:
                        upper_bound_sequence_id = min(upper_bound_sequence_id, chosen_frontier_sequence_id)
        if upper_bound_sequence_id <= start_id:
            print("No more progress to make, will sleep for 10 seconds.")
            time.sleep(10)
            continue

        object_infos = fetch_tracked_objects_batch(
            logs_table,
            pg_url,
            start_id,
            upper_bound_sequence_id,
            filter_tracked_option,
            filter_by_object_types,
            filter_by_project_ids,
            filter_by_object_ids,
        )
        print(f"Processing sequence IDs {start_id} to {upper_bound_sequence_id}. Found {len(object_infos)} objects.")
        if verbose:
            print(f"Object ids: {[obj['brainstore_object_id'] for obj in object_infos]}")

        # Process the object IDs in batches.
        success = True
        for i in range(0, len(object_infos), object_id_batch_size):
            object_infos_batch = object_infos[i : i + object_id_batch_size]
            print(f"Processing object IDs {i} to {min(i + object_id_batch_size, len(object_infos))}.")

            # Make a request to the Brainstore API to process the object IDs.
            start_time = time.time()
            response = requests.post(
                f"{brainstore_url}/wal/process",
                json=dict(
                    start_sequence_id=start_id,
                    end_sequence_id=upper_bound_sequence_id,
                    object_ids=[obj["brainstore_object_id"] for obj in object_infos_batch],
                    start_xact_id=0,
                    source="rebackfill_logs_data",
                    read_logs2=(logs_table == "logs2"),
                ),
            )
            if not response.ok:
                print(
                    f"Error processing object IDs {i} to {min(i + object_id_batch_size, len(object_infos))}: {response.text}"
                )
                success = False
                break

            print(f"Processed {len(object_infos_batch)} objects in {time.time() - start_time} seconds.")

        # If we are only filtering by tracking entry, we need to add the objects to the tracking entry.
        if filter_tracked_option == "only_tracking_entry":
            try:
                with psycopg2.connect(pg_url) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(
                            """
                            INSERT INTO brainstore_backfill_tracked_objects_to_brainstore_objects (project_id, object_type, brainstore_object_id)
                            SELECT project_id, object_type, brainstore_object_id
                            FROM unnest(%s::text[], %s::text[], %s::text[]) AS t(project_id, object_type, brainstore_object_id)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                [obj["project_id"] for obj in object_infos],
                                [obj["object_type"] for obj in object_infos],
                                [obj["brainstore_object_id"] for obj in object_infos],
                            ),
                        )
            except Exception as e:
                print(f"Error adding objects to tracking entry: {e}")
                success = False

        if success:
            print(
                f"Successfully processed sequence ID range {start_id} to {upper_bound_sequence_id}. Updating start_id file"
            )
            with open(start_id_file, "w") as f:
                f.write(str(upper_bound_sequence_id + 1))
        else:
            print("Retrying after 5 seconds...")
            time.sleep(5)


def run_batch_backfill(
    logs_table: Literal["logs", "logs2"],
    pg_url: str,
    brainstore_url: str,
    batch_file: str,
    cursor_file: str,
    sequence_id_batch_size: int,
    object_id_batch_size: int,
    filter_by_object_types: Optional[set],
    filter_by_project_ids: Optional[set],
    filter_tracked_option: Literal["only_tracking_entry", "only_brainstore_object"],
    verbose: bool,
):
    """Run backfill in batch mode from a JSONL file."""
    # Read the current cursor position
    cursor_index = 0
    if os.path.exists(cursor_file):
        with open(cursor_file, "r") as f:
            cursor_content = f.read().strip()
            if cursor_content:
                cursor_index = int(cursor_content)

    # Read the batch file
    with open(batch_file, "r") as f:
        lines = f.readlines()

    print(f"Starting batch processing from index {cursor_index} of {len(lines)} total entries")

    # Create a single temporary file for start_id tracking that will be reused
    with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as tmp_file:
        temp_start_file = tmp_file.name

    try:
        # Process each line starting from cursor position
        for i, line in enumerate(lines[cursor_index:], start=cursor_index):
            batch_spec = json.loads(line.strip())
            start_id = batch_spec["start_id"]
            end_id = batch_spec["end_id"]
            object_ids = batch_spec.get("object_ids", [])

            print(f"\n=== Processing batch {i + 1}/{len(lines)} ===")
            print(
                f"Start ID: {start_id}, End ID: {end_id}, Object IDs: {object_ids[:5]}{'...' if len(object_ids) > 5 else ''}"
            )

            # Convert object_ids to set for filtering
            filter_by_object_ids = set(object_ids) if object_ids else None

            # Overwrite the temp file with the new start_id
            with open(temp_start_file, "w") as f:
                f.write(str(start_id))

            # Run the backfill for this batch entry
            run_backfill(
                logs_table=logs_table,
                pg_url=pg_url,
                brainstore_url=brainstore_url,
                start_id_file=temp_start_file,
                end_id=end_id,
                sequence_id_batch_size=sequence_id_batch_size,
                object_id_batch_size=object_id_batch_size,
                filter_by_object_types=filter_by_object_types,
                filter_by_project_ids=filter_by_project_ids,
                filter_by_object_ids=filter_by_object_ids,
                filter_tracked_option=filter_tracked_option,
                limit_by_frontier_sequence_id=False,  # Always False in batch mode
                verbose=verbose,
            )

            # Update cursor file after successful processing
            with open(cursor_file, "w") as f:
                f.write(str(i + 1))

            print(f"Successfully completed batch {i + 1}/{len(lines)}")
    finally:
        # Clean up the single temporary file
        if os.path.exists(temp_start_file):
            os.unlink(temp_start_file)


def add_common_arguments(parser):
    """Add common arguments to a parser."""
    parser.add_argument(
        "--logs-table",
        type=str,
        choices=["logs", "logs2"],
        default="logs",
        help="Which logs table to use.",
    )
    parser.add_argument("--pg-url", type=str, required=True, help="URL of the PostgreSQL database.")
    parser.add_argument("--brainstore-url", type=str, required=True, help="URL of the Brainstore webserver.")
    parser.add_argument(
        "--sequence-id-batch-size",
        type=int,
        default=20000,
        help="Number of sequence IDs to fetch in each batch.",
    )
    parser.add_argument(
        "--object-id-batch-size", type=int, default=25, help="Number of object IDs to backfill in each batch."
    )
    parser.add_argument(
        "--filter-by-object-type",
        type=str,
        nargs="+",
        default=None,
        help="Filter by brainstore object types. If specified, only backfill objects with these object types. Can specify multiple types.",
    )
    parser.add_argument(
        "--filter-by-project-id",
        type=str,
        nargs="+",
        default=None,
        help="Filter by project IDs. If specified, only backfill objects with these project IDs. Can specify multiple IDs.",
    )
    parser.add_argument(
        "--filter-tracked-option",
        type=str,
        default="only_tracking_entry",
        choices=["only_tracking_entry", "only_brainstore_object"],
        help="""
            Which objects to consider when re-backfilling.
            'only_tracking_entry' filters out objects which don't correspond to a tracking entry in
            brainstore_backfill_tracked_objects, but will still add new objects to
            brainstore_backfill_tracked_objects_to_brainstore_objects.
            'only_brainstore_objects' will filter out objects which aren't in
            brainstore_backfill_tracked_objects_to_brainstore_objects.
        """,
    )
    parser.add_argument(
        "--verbose",
        default=False,
        help="Print verbose output.",
        action="store_true",
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Re-backfill logs data into Brainstore.", formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Add subparsers for single and batch modes
    subparsers = parser.add_subparsers(dest="mode", help="Mode of operation", required=True)

    # Single mode parser (existing functionality)
    single_parser = subparsers.add_parser("single", help="Run backfill for a single range")
    add_common_arguments(single_parser)
    single_parser.add_argument(
        "--start-id-file",
        type=str,
        required=True,
        help="Filename containing the start sequence ID, inclusive. It should just be a single integer. This file will be overwritten as the script makes progress.",
    )
    single_parser.add_argument("--end-id", type=int, required=True, help="End sequence ID to process, inclusive.")
    single_parser.add_argument(
        "--filter-by-object-id",
        type=str,
        nargs="+",
        default=None,
        help="Filter by brainstore object IDs. If specified, only backfill objects with these object IDs. Can specify multiple IDs.",
    )
    single_parser.add_argument(
        "--limit-by-frontier-sequence-id",
        default=False,
        help="If true, limit each iteration to the frontier sequence ID corresponding to the logs table in use.",
        action="store_true",
    )

    # Batch mode parser
    batch_parser = subparsers.add_parser("batch", help="Run backfill from a JSONL batch file")
    add_common_arguments(batch_parser)
    batch_parser.add_argument(
        "--batch-file",
        type=str,
        required=True,
        help='JSONL file where each line contains {"start_id": N, "end_id": M, "object_ids": [...]}',
    )
    batch_parser.add_argument(
        "--cursor-file",
        type=str,
        required=True,
        help="File to track progress through the batch file (stores line index).",
    )

    args = parser.parse_args()

    if args.mode == "single":
        # Convert filter lists to sets for efficient lookup
        filter_by_object_ids = set(args.filter_by_object_id) if args.filter_by_object_id else None
        filter_by_object_types = set(args.filter_by_object_type) if args.filter_by_object_type else None
        filter_by_project_ids = set(args.filter_by_project_id) if args.filter_by_project_id else None

        run_backfill(
            args.logs_table,
            args.pg_url,
            args.brainstore_url,
            args.start_id_file,
            args.end_id,
            args.sequence_id_batch_size,
            args.object_id_batch_size,
            filter_by_object_types,
            filter_by_project_ids,
            filter_by_object_ids,
            args.filter_tracked_option,
            args.limit_by_frontier_sequence_id,
            args.verbose,
        )
    elif args.mode == "batch":
        # Convert filter lists to sets for efficient lookup
        filter_by_object_types = set(args.filter_by_object_type) if args.filter_by_object_type else None
        filter_by_project_ids = set(args.filter_by_project_id) if args.filter_by_project_id else None

        run_batch_backfill(
            args.logs_table,
            args.pg_url,
            args.brainstore_url,
            args.batch_file,
            args.cursor_file,
            args.sequence_id_batch_size,
            args.object_id_batch_size,
            filter_by_object_types,
            filter_by_project_ids,
            args.filter_tracked_option,
            args.verbose,
        )
