#!/usr/bin/env python

import re
import sys

if __name__ == "__main__":
    for input_file in sys.argv[1:]:
        with open(input_file, "r") as f:
            contents = f.read()

        # sed -i '' 's#/docs/autoevals/nodejs/modules\.md#/docs/autoevals/nodejs#g' {}
        contents = re.sub(
            r"/docs/(.*?)/nodejs/modules\.md",
            r"/docs/\1/nodejs",
            contents,
            flags=re.MULTILINE,
        )

        with open(input_file, "w") as f:
            f.write(contents)
