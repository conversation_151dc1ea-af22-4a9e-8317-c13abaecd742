import { ArgumentParser, ArgumentDefaultsHelpFormatter } from "argparse";
import * as fs from "fs";
import * as yaml from "yaml";

function main() {
  const parser = new ArgumentParser({
    description: "Convert a JSON file to YAML",
    formatter_class: ArgumentDefaultsHelpFormatter,
  });
  parser.add_argument("filepath", {
    help: "Path to the file to convert",
    type: "str",
  });
  const args = parser.parse_args();
  const contents = fs.readFileSync(args.filepath, "utf8");
  const object = JSON.parse(contents);
  const yamlContents = yaml.stringify(object);
  console.log(yamlContents);
}

main();
