#!/usr/bin/env python3

# This script generates a query for recomputing the _expanded_acls corresponding
# to a particular set of rows from the acls table. This operation occurs in
# several places in the SQL schema, but with slightly different filters on the
# ACL rows we wish to recompute from, so we use this script to generate the
# query.

import sys

if __name__ == "__main__":
    where_cond = sys.argv[1]

    query = f"""with
candidate_acls as (
    select *
    from acls
    where ({where_cond})
),
joined_acls as (
    select
        candidate_acls.*,
        _expanded_group_members.user_object_type expanded_user_object_type,
        _expanded_group_members.user_group_id expanded_user_group_id,
        _expanded_role_permissions.permission expanded_permission,
        _expanded_role_permissions.restrict_object_type expanded_restrict_object_type
    from
        candidate_acls
            left join _expanded_group_members using (group_id)
            left join _expanded_role_permissions using (role_id)
    where
        _expanded_role_permissions.role_id is null or _expanded_role_permissions.grant_object_type = 'permission'
),
coalesced_acls as (
select
    id acl_id,
    object_type,
    object_id,
    coalesce(expanded_user_object_type, user_object_type) as user_object_type,
    coalesce(expanded_user_group_id, user_id) as user_group_id,
    coalesce(expanded_permission, permission) as permission,
    coalesce(expanded_restrict_object_type, restrict_object_type) as restrict_object_type,
    _object_org_id
from
    joined_acls
),
final_acls as (
    select * from coalesced_acls
    where
        -- It is possible that the user specifies an empty group or role, in
        -- which case we don't need to include these entries in the expanded
        -- ACLs.
        user_object_type is not null
        and user_group_id is not null
        and permission is not null
),
deleted_acls as (
    delete from _expanded_acls where
    ({where_cond})
    and not exists(
        select 1 from final_acls
        where
            _expanded_acls.acl_id = final_acls.acl_id
            and _expanded_acls.object_type = final_acls.object_type
            and _expanded_acls.object_id = final_acls.object_id
            and _expanded_acls.user_object_type = final_acls.user_object_type
            and _expanded_acls.user_group_id = final_acls.user_group_id
            and _expanded_acls.permission = final_acls.permission
            and ((_expanded_acls.restrict_object_type isnull and final_acls.restrict_object_type isnull)
                 or (_expanded_acls.restrict_object_type = final_acls.restrict_object_type))
    )
    returning 1
),
inserted_acls as (
    insert into _expanded_acls select * from final_acls on conflict do nothing
    returning 1
)
select num_deleted.cnt + num_inserted.cnt into _out
from
    (select count(*) cnt from deleted_acls) "num_deleted"
    join (select count(*) cnt from inserted_acls) "num_inserted"
    on true;
return _out;
"""

    print(query)
