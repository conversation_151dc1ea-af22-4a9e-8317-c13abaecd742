#!/bin/bash

# Default Redis connection parameters
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-"6379"}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
KEY_PREFIX=""

# Help message
function show_help {
    echo "Usage: $0 [options]"
    echo
    echo "Calculate the total memory usage of Redis keys with a specific prefix"
    echo
    echo "Options:"
    echo "  -h, --host HOST        Redis host (default: localhost)"
    echo "  -p, --port PORT        Redis port (default: 6379)"
    echo "  -a, --auth PASSWORD    Redis password"
    echo "  -k, --key-prefix KEY   Key prefix to search for (default: ai_proxy)"
    echo "  --help                 Show this help message"
    echo
}

# Check for redis-cli
if ! command -v redis-cli &>/dev/null; then
    echo "Error: redis-cli not found"
    echo "This script requires the Redis command line client to be installed."
    echo
    echo "Installation instructions:"
    echo "  • Ubuntu/Debian: sudo apt-get install redis-tools"
    echo "  • CentOS/RHEL:   sudo yum install redis"
    echo "  • macOS:         brew install redis"
    echo "  • Windows:       Download from https://github.com/microsoftarchive/redis/releases"
    echo
    exit 1
fi

# Check for bc (used for calculations)
if ! command -v bc &>/dev/null; then
    echo "Error: bc not found"
    echo "This script requires the bc calculator for size formatting."
    echo
    echo "Installation instructions:"
    echo "  • Ubuntu/Debian: sudo apt-get install bc"
    echo "  • CentOS/RHEL:   sudo yum install bc"
    echo "  • macOS:         Typically pre-installed, if not: brew install bc"
    echo
    exit 1
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--host)
            REDIS_HOST="$2"
            shift 2
            ;;
        -p|--port)
            REDIS_PORT="$2"
            shift 2
            ;;
        -a|--auth)
            REDIS_PASSWORD="$2"
            shift 2
            ;;
        -k|--key-prefix)
            KEY_PREFIX="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Build Redis CLI command with password if provided
REDIS_CMD="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
if [[ -n "$REDIS_PASSWORD" ]]; then
    REDIS_CMD="$REDIS_CMD -a $REDIS_PASSWORD"
fi

echo "Connecting to Redis at $REDIS_HOST:$REDIS_PORT"
echo "Calculating total size of keys with prefix '$KEY_PREFIX'..."

# Get all keys with the specified prefix
keys=$($REDIS_CMD --raw keys "$KEY_PREFIX*")

if [ -z "$keys" ]; then
    echo "No keys found with prefix '$KEY_PREFIX'"
    exit 0
fi

# Initialize counters
total_size=0
key_count=0
largest_key=""
largest_size=0

# Process each key
echo
echo "Analyzing keys..."
echo "----------------"

for key in $keys; do
    # Get the memory usage for the key
    size=$($REDIS_CMD memory usage "$key")

    # Skip if we got an error
    if [[ ! "$size" =~ ^[0-9]+$ ]]; then
        echo "Error getting size for key: $key"
        continue
    fi

    # Update counters
    key_count=$((key_count + 1))
    total_size=$((total_size + size))

    # Track the largest key
    if [[ $size -gt $largest_size ]]; then
        largest_size=$size
        largest_key=$key
    fi

    # Show progress every 100 keys
    if [[ $((key_count % 100)) -eq 0 ]]; then
        echo -ne "Processed $key_count keys...\r"
    fi
done

# Format sizes for human readability
function format_size {
    local bytes=$1
    if [[ $bytes -lt 1024 ]]; then
        echo "${bytes} bytes"
    elif [[ $bytes -lt 1048576 ]]; then
        echo "$(printf "%.2f" $(echo "scale=2; $bytes/1024" | bc)) KB"
    elif [[ $bytes -lt 1073741824 ]]; then
        echo "$(printf "%.2f" $(echo "scale=2; $bytes/1048576" | bc)) MB"
    else
        echo "$(printf "%.2f" $(echo "scale=2; $bytes/1073741824" | bc)) GB"
    fi
}

total_size_formatted=$(format_size $total_size)
largest_size_formatted=$(format_size $largest_size)

# Display results
echo
echo "Results:"
echo "----------------"
echo "Total keys with prefix '$KEY_PREFIX': $key_count"
echo "Total memory usage: $total_size_formatted ($total_size bytes)"
echo "Average size per key: $(format_size $((total_size / key_count)))"
echo "Largest key: $largest_key with size $largest_size_formatted"
echo

# If key count is high, provide a performance warning
if [[ $key_count -gt 10000 ]]; then
    echo "Warning: A large number of keys ($key_count) were found."
    echo "Consider using Redis SCAN for better performance with large datasets."
fi
