import glob
import os
import re
import sys  # Import sys module
from concurrent import futures
from typing import Dict, Tuple
from urllib.parse import urlparse

import requests
from bs4 import BeautifulSoup
from markdown import markdown
from tenacity import RetryError, retry, retry_if_exception_type, retry_if_result, stop_after_attempt, wait_exponential

# List to collect broken and ignored links
broken_links_summary = []
# Base directory for absolute-style links
# TODO: For now, we try to match the behavior of Next.js router. Instead we
# could run the link checker by making requests to the dev server.
base_directory = ["app/content/", "app/public/"]
redirects = {}
production_domain = "https://braintrust.dev"  # Production domain for fallback

# HTTP status codes to ignore (do not mark as broken)
IGNORED_STATUS_CODES = {403, 429, 405, 999}


def load_redirects():
    """Load redirects from next.config.mjs."""
    redirects_file = "app/next.config.mjs"
    with open(redirects_file, "r", encoding="utf-8") as f:
        content = f.read()

    # Find redirect entries in the file
    redirect_pattern = re.compile(
        r'\{[^}]*source:\s*"([^"]+)",\s*destination:\s*"([^"]+)",\s*permanent:\s*(true|false)\s*\}', re.MULTILINE
    )
    matches = redirect_pattern.findall(content)

    # Build the redirects dictionary
    for match in matches:
        source = match[0]
        destination = match[1]
        redirects[source] = destination


def apply_redirects(link):
    """Check if the link matches any redirects and return the final destination."""
    for source, destination in redirects.items():
        # Handle dynamic segments (:path*)
        pattern = re.sub(r":path\*", ".*", source)  # Replace :path* with regex to match anything
        if re.match(pattern, link):
            return re.sub(pattern, destination, link)  # Replace the matched part of the link with the destination
    return link


def _is_invalid_http_status(resp: requests.Response) -> bool:
    return resp.status_code != 200 and resp.status_code not in IGNORED_STATUS_CODES


@retry(
    wait=wait_exponential(multiplier=1, min=1, max=4),
    stop=stop_after_attempt(5),
    retry=(retry_if_exception_type() | retry_if_result(_is_invalid_http_status)),
)
def _head(link):
    return requests.head(link, allow_redirects=True, timeout=5)


def extract_links_from_mdx(file_content):
    """Extract all links from the given markdown content."""
    html = markdown(file_content)
    soup = BeautifulSoup(html, "html.parser")
    return [a["href"] for a in soup.find_all("a", href=True)]


def check_local_path(relative_path: str) -> bool:
    # Normalize the path to remove any "./" or "../"
    absolute_path = os.path.abspath(relative_path)

    if os.path.exists(absolute_path):
        return True

    # If there is no extension.
    if not os.path.splitext(absolute_path)[1]:
        # Allow file with extension.
        for extension in (".md", ".mdx"):
            if os.path.exists(absolute_path + extension):
                return True

    return False


def check_link_status(link, current_file_path):
    """Check if a link is valid. Handles both external and relative links."""
    # Apply redirects if necessary
    link = apply_redirects(link)

    # Ignore any links that include "openai.com" or "anthropic.com"
    if "openai.com" in link:
        return "IGNORED (openai.com link)"
    if "anthropic.com" in link:
        return "IGNORED (anthropic.com link)"

    # Ignore "mailto:" links
    if link.startswith("mailto:"):
        return "IGNORED (mailto link)"

    # Check if the link is an external link (starting with http or https)
    if link.startswith("http://") or link.startswith("https://"):
        try:
            response = _head(link)
            status_code = response.status_code
            if status_code == 200:
                return "VALID (external)"
            elif status_code in IGNORED_STATUS_CODES:
                return f"IGNORED (external: {status_code})"
            else:
                return f"BROKEN (external: {status_code})"
        except RetryError as e:
            return f"BROKEN (external: {str(e.last_attempt.result().status_code)})"
        except Exception as e:
            return f"BROKEN (external: {str(e)})"

    # Strip any fragment identifier (e.g., #trials) from the link
    link_without_fragment = urlparse(link).path

    # Check if the link is absolute-style (starting with "/")
    if link.startswith("/"):
        # Resolve path relative to the base_directory
        for base in base_directory:
            relative_path = os.path.join(base, link_without_fragment.lstrip("/"))
            if check_local_path(relative_path):
                return "VALID (relative)"
    else:
        # Resolve the relative path based on the current file's location
        current_dir = os.path.dirname(current_file_path)
        relative_path = os.path.join(current_dir, link_without_fragment)
        if check_local_path(relative_path):
            return "VALID (relative)"

    # If file doesn't exist, try to resolve it to braintrust.dev as a fallback
    fallback_url = f"{production_domain}{link_without_fragment}"
    try:
        response = _head(fallback_url)
        status_code = response.status_code
        if status_code == 200:
            return f"VALID (resolved to {fallback_url})"
        elif status_code in IGNORED_STATUS_CODES:
            return f"IGNORED (external: {status_code}, tried {fallback_url})"
        else:
            return f"BROKEN (external: {status_code}, tried {fallback_url})"
    except Exception as e:
        return f"BROKEN (external: {str(e)}, tried {fallback_url})"


def main():
    print("Running link check...")

    # Load redirects from next.config.mjs
    load_redirects()

    # Find all .mdx files in the 'app/content/' directory only
    mdx_files = glob.glob("app/content/**/*.mdx", recursive=True)

    if not mdx_files:
        print("No .mdx files found.")
        return

    # Check links in each file
    result_futures: Dict[futures.Future[str], Tuple[str, str]] = {}
    for mdx_file in mdx_files:
        # Ignore any files in the app/content/docs/reference/api directory
        if "app/content/docs/reference/api" in mdx_file:
            return

        with open(mdx_file, "r", encoding="utf-8") as f:
            content = f.read()

        links = extract_links_from_mdx(content)
        with futures.ThreadPoolExecutor() as e:
            for link in links:
                fut = e.submit(check_link_status, link, mdx_file)
                result_futures[fut] = (link, mdx_file)

    for fut in futures.as_completed(result_futures):
        status = fut.result()
        if "BROKEN" in status:
            (link, mdx_file) = result_futures[fut]
            broken_links_summary.append((mdx_file, link, status))

    # Print summary of broken links
    if broken_links_summary:
        print("\nSummary of broken links:")
        for file, link, status in broken_links_summary:
            print(f"File: {file}\nLink: {link}\nStatus: {status}\n")
        sys.exit(1)  # Exit with status code 1 if there are broken links
    else:
        print("\nAll links are valid.")


if __name__ == "__main__":
    main()
