/**
 * You'll likely want to run this script from either a clean/new Org or one that hasn't been used for a while.
 * If you're running this against local, make sure you set the .env vars for TELEMETRY_* variables.
 */
import * as braintrust from "braintrust";

const project = braintrust.projects.create({ name: "billing_telemetry_audit" });

class NoopProgressReporter {
  public start() {}
  public stop() {}
  public increment() {}
}

// stuff bag that will be reported at the end of the script
type Results = {
  logBytes: number;
  scoresAndCustomMetrics: number;
  functionInvoke: number;
  events: number;
};

// should have an audit method per billable metric
// best to vary the number of logs and flushes to simulate different usage patterns
const checks = {
  /**
   * Insert logs, datasets, prompts, and functions with an estimated size (about 100KB each). Helpful example for testing log size
   * ingestion of various object types. Comparing what we account internally vs. what is reported to our billing vendor (Orb).
   */
  async logBytes(logger: braintrust.Logger<true>, results: Results) {
    const fluff = "1" + new Array(100_000).fill("0").join("");
    const text = `~${fluff.length} bytes\n${fluff}`;
    const bytes = text.length;

    // project log
    logger.log({
      input: text,
      output: "",
    });

    results.logBytes += bytes;

    await braintrust.flush();
    results.events += 1;

    // comment
    const span = logger.startSpan();

    span.log({
      input: "",
      output: "",
    });

    span.logFeedback({
      comment: text,
    });

    results.logBytes += bytes;

    await braintrust.flush();
    results.events += 1; // maybe 2?

    // dataset
    const dataset = braintrust.initDataset({
      project: project.name,
    });

    dataset.insert({
      input: text,
      output: "",
    });

    results.logBytes += bytes;

    await braintrust.flush();
    results.events += 1; // maybe 2?

    // experiment
    await braintrust.runEvaluator(
      null,
      {
        projectName: project.name!,
        evalName: "logBytes",
        data: [{ input: text }],
        task: () => "",
        scores: [],
      },
      new NoopProgressReporter(),
      [],
      undefined,
    );

    results.logBytes += bytes;

    await braintrust.flush();
    results.events += 1;

    // coming soon
    // const project =  braintrust.projects.create({
    //     name: projectName,
    //   });
    //
    // // prompts
    // project.prompts.create({
    //   name: `${projectName} prompt`,
    //   model: "gpt-4o-mini",
    //   messages: [
    //     {
    //       content: text,
    //       role: "system",
    //     },
    //   ],
    //   tools: [],
    // });

    // // functions
    // project.scorers.create({
    //   name: `${projectName} scorer`,
    //   model: "gpt-4o-mini",
    //   messages: [
    //     {
    //       role: "user",
    //       content: text,
    //     },
    //   ],
    //   model: "gpt-4o",
    //   useCot: true,
    //   choiceScores: {
    //     A: 1,
    //     B: 0,
    //   },
    // });

    // project.publish();
  },

  async scoresAndCustomMetrics(
    logger: braintrust.Logger<true>,
    results: Results,
  ) {
    const span = braintrust.startSpan({ name: "scoresAndCustomMetrics" });
    results.events += 1; // since we started a span

    span.log({
      input: "",
      output: "",
      scores: {
        one: 0,
        two: 0,
        three: 0,
        four: 0,
        five: 0,
      },
    });

    results.scoresAndCustomMetrics += 5;

    await braintrust.flush();
    results.events += 1;

    span.log({
      input: "",
      output: "",
      scores: {
        six: 1,
        seven: 0,
        eight: 0,
        nine: 0,
        ten: 0,
      },
    });

    results.scoresAndCustomMetrics += 5;

    span.log({
      input: "",
      output: "",
      metrics: {
        one: 0,
        two: 0,
        three: 0,
        four: 0,
        five: 0,
      },
    });

    results.scoresAndCustomMetrics += 5;

    await braintrust.flush();
    results.events += 1; // or 2?

    span.log({
      input: "",
      output: "",
      metrics: {
        six: 1,
        seven: 1,
        eight: 1,
        nine: 1,
        ten: 1,
      },
    });

    results.scoresAndCustomMetrics += 5;

    await braintrust.flush();
    results.events += 1;
  },

  async functionInvoke(logger: braintrust.Logger<true>, results: Results) {
    const functionSlug = "return1";
    const projectId = (await logger.project).id;

    const res = await braintrust
      ._internalGetGlobalState()
      .apiConn()
      .post_json("insert-functions", {
        functions: [
          {
            project_id: projectId,
            slug: functionSlug,
            name: functionSlug,
            description: "",
            function_data: {
              type: "code",
              data: {
                type: "inline",
                runtime_context: {
                  runtime: "python",
                  version: "3.12",
                },
                code: `def handler(*args, **kwargs):
                return 1.0`,
              },
            },
            function_type: "task",
            if_exists: "replace",
          },
        ],
      });

    if (res.status !== "success") {
      console.error(`Failed to upsert function: ${res.message}`);
      process.exit(1);
    }

    const fn = braintrust.initFunction({
      projectName: project.name!,
      slug: functionSlug,
    });

    await braintrust.runEvaluator(
      null,
      {
        projectName: project.name!,
        evalName: "functionInvoke task",
        data: [{ input: "" }],
        task: fn,
        scores: [],
      },
      new NoopProgressReporter(),
      [],
      undefined,
    );

    results.functionInvoke += 1;
    results.events += 2;

    await braintrust.flush();

    await braintrust.runEvaluator(
      null,
      {
        projectName: project.name!,
        evalName: "functionInvoke scores",
        data: [{ input: "" }],
        task: () => "",
        scores: [fn],
      },
      new NoopProgressReporter(),
      [],
      undefined,
    );

    results.functionInvoke += 1;

    await braintrust.flush();
    results.events += 2;
  },
};

async function main() {
  const logger = braintrust.initLogger({ projectName: project.name! });
  const allowed = process.argv.slice(2);

  const results: Results = {
    events: 0,
    functionInvoke: 0,
    logBytes: 0,
    scoresAndCustomMetrics: 0,
  };

  for (const [name, check] of Object.entries(checks)) {
    if (!allowed.length || allowed.includes(name)) {
      await check(logger, results);
    } else {
      console.log(`Skipping ${name}`);
    }
  }

  console.log(
    "Instructions: Check the resource counts in admin and compare with Orb.",
  );
  console.log("");
  console.log("Estimated expected metrics");
  console.table(results);
  console.log("");

  console.log(
    "Note: for aggregated events, you may need to wait at most 10 minutes.",
  );
}

main().catch(console.error);
