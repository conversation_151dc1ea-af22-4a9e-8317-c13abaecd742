// Script generate_ed25519_keypair generates an ed25519 keypair and
// outputs both the public and private keys as base64-encoded strings.

import * as ed from "@noble/ed25519";

const privateKey = await ed.utils.randomPrivateKey();
const publicKey = await ed.getPublicKeyAsync(privateKey);
console.log("Private key:", Buffer.from(privateKey).toString("base64"));
console.log("Public key:", Buffer.from(publicKey).toString("base64"));
