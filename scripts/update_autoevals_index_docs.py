#!/usr/bin/env python3

import sys


def update_docs(target_file):
    # Read the README.md file
    with open("autoevals/README.md", "r") as f:
        readme_contents = f.read()

    # Read the current index.mdx file
    with open(target_file, "r") as f:
        current_contents = f.read()

    # Find the marker
    marker = "{/* BOF DO NOT REMOVE */}"
    marker_pos = current_contents.find(marker)

    if marker_pos == -1:
        print(f"Error: Could not find marker '{marker}' in {target_file}")
        sys.exit(1)

    # Keep everything up to and including the marker, then add the README contents
    new_contents = current_contents[: marker_pos + len(marker)] + "\n\n" + readme_contents

    # Write the updated contents back to the file
    with open(target_file, "w") as f:
        f.write(new_contents)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: update_autoevals_index_docs.py <target_file>")
        sys.exit(1)

    update_docs(sys.argv[1])
