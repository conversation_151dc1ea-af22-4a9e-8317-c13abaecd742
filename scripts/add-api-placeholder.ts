import { readFileSync, writeFileSync } from "fs";
import { join } from "path";
import { globSync } from "glob";

const API_DIR = "app/pages/api";
const REQUIRED_IMPORT = `import { type NextApiRequest, type NextApiResponse } from "next";`;
const PLACEHOLDER_FUNCTION = `/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}`;

function processFile(filePath: string): void {
  console.log(`Processing: ${filePath}`);

  const content = readFileSync(filePath, "utf-8");
  let modified = false;
  let newContent = content;

  // Check if import already exists
  if (
    !content.includes("NextApiRequest") ||
    !content.includes("NextApiResponse")
  ) {
    // Add import at the top, after any existing imports
    newContent = `${REQUIRED_IMPORT}\n${content}`;
    modified = true;
  }

  // Check if default export already exists
  if (!newContent.includes("export default")) {
    // Add the placeholder function at the end
    newContent = newContent.trim() + "\n\n" + PLACEHOLDER_FUNCTION + "\n";
    modified = true;
  }

  if (modified) {
    writeFileSync(filePath, newContent);
    console.log(`  ✓ Modified ${filePath}`);
  } else {
    console.log(`  - No changes needed for ${filePath}`);
  }
}

function main(): void {
  console.log(`Checking files in ${API_DIR}...`);

  // Find all TypeScript files in the API directory
  const pattern = join(API_DIR, "**/*.ts");
  const files = globSync(pattern, { ignore: ["**/*.d.ts"] });

  if (files.length === 0) {
    console.log("No TypeScript files found in the API directory.");
    return;
  }

  console.log(`Found ${files.length} TypeScript files to process.\n`);

  files.forEach(processFile);

  console.log("\nProcessing complete!");
}

main();
