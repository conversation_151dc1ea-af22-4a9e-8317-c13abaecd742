# These files are exposed at https://gist.github.com/ankrgyl/26d7a4a5e1a35b21e0b88bf23b7aa06f
import argparse
import os
import sys
import time

from psycopg2 import connect

if "PG_URL" not in os.environ:
    print("Please set PG_URL to the database URL", file=sys.stderr)
    sys.exit(1)

conn = connect(os.environ["PG_URL"])


def sizeof_fmt(num, suffix="B"):
    for unit in ("", "Ki", "Mi", "Gi", "Ti", "Pi", "Ei", "Zi"):
        if abs(num) < 1024.0:
            return f"{num:3.1f}{unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f}Yi{suffix}"


def single_quote(s):
    return f"'{s}'" if s is not None else "NULL"


def make_object_id(project_id=None, experiment_id=None, dataset_id=None, prompt_session_id=None, log_id=None):
    return f"make_object_id({single_quote(project_id)}, {single_quote(experiment_id)}, {single_quote(dataset_id)}, {single_quote(prompt_session_id)}, {single_quote(log_id)})"


MAKE_OBJECT_ID_EXPR = "make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)"


def full_experiment_query(project_id, experiment_id):
    return f"""
SELECT "experiment"."id" AS "id"
, NULLIF(jsonb_extract_path_text("experiment"."data", 'dataset_record_id'), 'null') AS "dataset_record_id"
, "experiment"."_xact_id" AS "_xact_id"
, "experiment"."created"::timestamptz AS "created"
, "experiment"."project_id" AS "project_id"
, "experiment"."experiment_id" AS "experiment_id"
, NULLIF(jsonb_extract_path("experiment"."data", 'inputs'), 'null')::jsonb AS "input"
, NULLIF(jsonb_extract_path("experiment"."data", 'output'), 'null')::jsonb AS "output"
, NULLIF(jsonb_extract_path("experiment"."data", 'expected'), 'null')::jsonb AS "expected"
, NULLIF(jsonb_extract_path("experiment"."data", 'error'), 'null')::jsonb AS "error"
, "experiment"."scores" AS "scores"
, NULLIF(jsonb_extract_path("experiment"."data", 'metadata'), 'null')::jsonb AS "metadata"
, NULLIF(jsonb_extract_path("experiment"."data", 'tags'), 'null')::jsonb AS "tags"
, NULLIF(jsonb_extract_path("experiment"."data", 'metrics'), 'null')::jsonb AS "metrics"
, NULLIF(jsonb_extract_path("experiment"."data", 'context'), 'null')::jsonb AS "context"
, "experiment"."span_id" AS "span_id"
, NULLIF(jsonb_extract_path("experiment"."data", 'span_parents'), 'null')::jsonb AS "span_parents"
, "experiment"."root_span_id" AS "root_span_id"
, NULLIF(jsonb_extract_path("experiment"."data", 'span_attributes'), 'null')::jsonb AS "span_attributes"
, "experiment"."pagination_cursor_xact_id" AS "pagination_cursor_xact_id"
, "experiment"."pagination_cursor_root_span_id" AS "pagination_cursor_root_span_id"
FROM (

            WITH noop AS (SELECT 1),
            previous_candidate_root_spans AS (
            SELECT COALESCE(root_span_id, id) coalesced_root_span_id
            FROM "logs"
            WHERE NOT "_object_delete" AND ROW("_xact_id", COALESCE(root_span_id, id))::_xact_id_root_span_id >= (ROW('9223372036854775807', '')::_xact_id_root_span_id) AND TRUE AND make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id") = {make_object_id(project_id=project_id, experiment_id=experiment_id)} AND 'true'::boolean AND _xact_id = (
              SELECT MAX("_xact_id")
              FROM "logs" "_other_logs"
              WHERE
                  TRUE
                  AND make_object_id("_other_logs"."project_id", "_other_logs"."experiment_id", "_other_logs"."dataset_id", "_other_logs"."prompt_session_id", "_other_logs"."log_id") = make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id")
                  AND "_other_logs".id = "logs".id
          )
        ),
            candidate_root_spans AS (
                SELECT COALESCE(root_span_id, id) coalesced_root_span_id, "_xact_id"
                FROM "logs"
                WHERE NOT "_object_delete" AND ROW("_xact_id", COALESCE(root_span_id, id))::_xact_id_root_span_id < (ROW('9223372036854775807', '')::_xact_id_root_span_id) AND TRUE AND make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id") = {make_object_id(project_id=project_id, experiment_id=experiment_id)} AND 'true'::boolean AND _xact_id = (
              SELECT MAX("_xact_id")
              FROM "logs" "_other_logs"
              WHERE
                  TRUE
                  AND make_object_id("_other_logs"."project_id", "_other_logs"."experiment_id", "_other_logs"."dataset_id", "_other_logs"."prompt_session_id", "_other_logs"."log_id") = make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id")
                  AND "_other_logs".id = "logs".id
          ) AND COALESCE(root_span_id, id) NOT IN (SELECT coalesced_root_span_id FROM previous_candidate_root_spans)

            ),
            paginated_ids AS (
                SELECT coalesced_root_span_id, MAX("_xact_id") AS xact_id_max
                FROM candidate_root_spans
                GROUP BY coalesced_root_span_id

            ),
            pagination_cursor AS (
                SELECT * FROM (
                    SELECT xact_id_max "pagination_cursor_xact_id", coalesced_root_span_id "pagination_cursor_root_span_id"
                    FROM paginated_ids
                    UNION ALL
                    SELECT null "pagination_cursor_xact_id", null "pagination_cursor_root_span_id"
                ) "t"
                ORDER BY "pagination_cursor_xact_id" ASC NULLS LAST, "pagination_cursor_root_span_id" ASC NULLS LAST LIMIT 1
            )

        SELECT
            *
        FROM (
            SELECT DISTINCT ON (object_id, id)
                data AS data,
                id,
                COALESCE(span_id, id) AS "span_id",
                COALESCE(root_span_id, id) AS "root_span_id",
                ("_xact_id")::text AS "_xact_id",
                "_object_delete",
                scores,
                make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id") object_id,
                org_id,
                project_id,
                experiment_id,
                dataset_id,
                prompt_session_id,
                log_id,
                COALESCE(created, row_created::text) AS created


    , (pagination_cursor."pagination_cursor_xact_id")::text AS "pagination_cursor_xact_id"
    , pagination_cursor."pagination_cursor_root_span_id"

            FROM
                "logs"  AS "logs"
                     JOIN pagination_cursor ON true
            WHERE
                TRUE AND make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id") = {make_object_id(project_id=project_id, experiment_id=experiment_id)} AND COALESCE(root_span_id, id) IN (SELECT coalesced_root_span_id FROM paginated_ids)
            ORDER BY object_id, id, "_xact_id" DESC
        ) sub1
        WHERE
            NOT "_object_delete"
        ) AS "experiment"
"""


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--project-id", type=str, required=True)
    parser.add_argument("--experiment-id", type=str, required=True)
    args = parser.parse_args()

    print("Counting rows...")
    start = time.time()
    cur = conn.cursor()
    cur.execute(
        f"SELECT count(*) FROM logs WHERE {MAKE_OBJECT_ID_EXPR}={make_object_id(project_id=args.project_id, experiment_id=args.experiment_id)}"
    )
    print("Counted: ", cur.fetchone()[0])
    print(f"Counted rows in {time.time() - start:.2f}s")

    print("Fetching root span rows...")
    start = time.time()
    cur.execute(
        f"SELECT * FROM logs WHERE {MAKE_OBJECT_ID_EXPR}={make_object_id(project_id=args.project_id, experiment_id=args.experiment_id)} AND is_root"
    )
    rows = cur.fetchall()
    end = time.time()
    print(f"Fetched root spans in {end - start:.2f}s")
    total_size = sum(sum(len(str(r)) for r in row) for row in rows)
    print(f"Total size: {sizeof_fmt(total_size)}")

    print("Fetching rows...")
    start = time.time()
    cur.execute(
        f"SELECT * FROM logs WHERE {MAKE_OBJECT_ID_EXPR}={make_object_id(project_id=args.project_id, experiment_id=args.experiment_id)}"
    )
    rows = cur.fetchall()
    end = time.time()
    total_size = sum(sum(len(str(r)) for r in row) for row in rows)
    print(f"Fetched {len(rows)} rows in {end - start:.2f}s")
    print(f"Total size: {sizeof_fmt(total_size)}")

    print("Counting rows from full experiment query...")
    start = time.time()
    cur.execute("SELECT COUNT(*) FROM (" + full_experiment_query(args.project_id, args.experiment_id) + ") sub")
    print("Counted: ", cur.fetchone()[0])
    print(f"Counted rows in {time.time() - start:.2f}s")

    print("Fetching rows from full experiment query...")
    start = time.time()
    cur.execute(full_experiment_query(args.project_id, args.experiment_id))
    rows = cur.fetchall()
    end = time.time()
    total_size = sum(sum(len(str(r)) for r in row) for row in rows)
    print(f"Fetched {len(rows)} rows in {end - start:.2f}s")
    print(f"Total size: {sizeof_fmt(total_size)}")
