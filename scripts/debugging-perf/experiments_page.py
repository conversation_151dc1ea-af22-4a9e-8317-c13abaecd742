# These files are exposed at https://gist.github.com/ankrgyl/26d7a4a5e1a35b21e0b88bf23b7aa06f
import argparse
import os
import sys
import time

import requests
from psycopg2 import connect

if "PG_URL" not in os.environ:
    print("Please set PG_URL to the database URL", file=sys.stderr)
    sys.exit(1)

if "BRAINTRUST_API_KEY" not in os.environ:
    print("Please set BRAINTRUST_API_KEY to the API key", file=sys.stderr)
    sys.exit(1)

conn = connect(os.environ["PG_URL"])

BRAINTRUST_API_KEY = os.environ["BRAINTRUST_API_KEY"]
BRAINTRUST_APP_URL = os.environ.get("BRAINTRUST_APP_URL", "https://www.braintrust.dev")


def single_quote(s):
    return f"'{s}'" if s is not None else "NULL"


def make_object_id(project_id=None, experiment_id=None, dataset_id=None, prompt_session_id=None, log_id=None):
    return f"make_object_id({single_quote(project_id)}, {single_quote(experiment_id)}, {single_quote(dataset_id)}, {single_quote(prompt_session_id)}, {single_quote(log_id)})"


def full_experiment_query(project_id, experiment_ids):
    make_object_id_exprs = [make_object_id(project_id, experiment_id) for experiment_id in experiment_ids]
    return f"""
SELECT "experiment"."experiment_id" AS "experiment_id"
, "__unpivot_0"."key" AS "scores"
, "max"("experiment"."created"::timestamptz) AS "last_updated"
, "avg"(("__unpivot_0"."value" #>> '{{}}')::numeric) AS "avg"
FROM (

            WITH noop AS (SELECT 1),
            pagination_cursor AS (
                SELECT null "pagination_cursor_xact_id", null "pagination_cursor_root_span_id"
            )

        SELECT
            *
        FROM (
            SELECT DISTINCT ON (object_id, id)

                id,
                COALESCE(span_id, id) AS "span_id",
                COALESCE(root_span_id, id) AS "root_span_id",
                ("_xact_id")::text AS "_xact_id",
                "_object_delete",
                scores,
                make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id") object_id,
                org_id,
                project_id,
                experiment_id,
                dataset_id,
                prompt_session_id,
                log_id,
                COALESCE(created, row_created::text) AS created


    , (pagination_cursor."pagination_cursor_xact_id")::text AS "pagination_cursor_xact_id"
    , pagination_cursor."pagination_cursor_root_span_id"

            FROM
                "logs"  AS "logs"
                     JOIN pagination_cursor ON true
            WHERE
                TRUE AND make_object_id("logs"."project_id", "logs"."experiment_id", "logs"."dataset_id", "logs"."prompt_session_id", "logs"."log_id") IN ({", ".join(make_object_id_exprs)})
            ORDER BY object_id, id, "_xact_id" DESC
        ) sub1
        WHERE
            NOT "_object_delete"
        ) AS "experiment"
, jsonb_each("experiment"."scores") AS "__unpivot_0"
GROUP BY ROLLUP(1, 2)
HAVING GROUPING("experiment"."experiment_id")=0
ORDER BY 1, 2
"""


MAKE_OBJECT_ID_EXPR = "make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)"

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--project-id", type=str, required=True)
    args = parser.parse_args()

    # Log in, and get the API URL
    response = requests.post(
        f"{BRAINTRUST_APP_URL}/api/apikey/login",
        headers={"Authorization": f"Bearer {BRAINTRUST_API_KEY}"},
    )
    response.raise_for_status()
    d = response.json()
    api_url = os.environ.get("BRAINTRUST_API_URL", d["org_info"][0]["api_url"])
    print(f"API URL: {api_url}")

    # Fetch all of the experiments in the project
    start = time.time()
    response = requests.get(
        f"{api_url}/v1/experiment?project_id={args.project_id}",
        headers={"Authorization": f"Bearer {BRAINTRUST_API_KEY}"},
    )
    response.raise_for_status()
    end = time.time()
    experiments = response.json()["objects"]
    print(f"Fetched {len(experiments)} experiments in {end - start} seconds")

    # Now pick the last 500
    experiments.sort(key=lambda x: x["created"])
    last_500 = [x["id"] for x in experiments[-500:]]

    cur = conn.cursor()
    start = time.time()
    cur.execute(full_experiment_query(args.project_id, last_500))
    results = cur.fetchall()
    end = time.time()
    print(f"Fetched {len(experiments)} experiments summary in {end - start} seconds")

    # for result in results:
    #     print(result)
