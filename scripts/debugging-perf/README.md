# Debugging performance issues

These files run queries against your Postgres instance to help diagnose performance issues.

Please keep these files confidential, and do not publish them, as they contains sensitive details
about the inner workings of your Postgres instance.

## Setup

To run these files, you need to have the following environment variables set:

- `PG_URL`: The `postgres://` URL for your Postgres instance.
- `BRAINTRUST_API_KEY`: The API key for your Braintrust instance.
- `BRAINTRUST_API_URL` (optional): The base URL for your Braintrust instance. If not set, the URL will be derived automatically.
- `BRAINTRUST_APP_URL` (optional): The base URL for your Braintrust app. You can ignore this unless you have a "full" on-prem deployment.

You also need to install the dependencies in requirements.txt.

```bash

python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```
