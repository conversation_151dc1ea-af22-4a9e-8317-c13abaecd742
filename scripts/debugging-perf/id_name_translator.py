# These files are exposed at https://gist.github.com/ankrgyl/26d7a4a5e1a35b21e0b88bf23b7aa06f
import argparse
import json
import os
import sys
import time
from urllib.parse import quote

import requests
from psycopg2 import connect

if "BRAINTRUST_API_KEY" not in os.environ:
    print("Please set BRAINTRUST_API_KEY to the API key", file=sys.stderr)
    sys.exit(1)

BRAINTRUST_API_KEY = os.environ["BRAINTRUST_API_KEY"]
BRAINTRUST_APP_URL = os.environ.get("BRAINTRUST_APP_URL", "https://www.braintrust.dev")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--project-id", type=str, help="ID of the project")
    group.add_argument("--project-name", type=str, help="Name of the project")

    group = parser.add_mutually_exclusive_group(required=False)
    group.add_argument("--experiment-id", type=str, help="ID of the experiment")
    group.add_argument("--experiment-name", type=str, help="Name of the experiment")

    args = parser.parse_args()

    # Log in, and get the API URL
    response = requests.post(
        f"{BRAINTRUST_APP_URL}/api/apikey/login",
        headers={"Authorization": f"Bearer {BRAINTRUST_API_KEY}"},
    )
    response.raise_for_status()
    d = response.json()
    api_url = os.environ.get("BRAINTRUST_API_URL", d["org_info"][0]["api_url"])
    print(f"API URL: {api_url}")

    project_arg = (
        f"project_id={quote(args.project_id)}" if args.project_id else f"project_name={quote(args.project_name)}"
    )

    if args.experiment_id or args.experiment_name:
        experiment_arg = (
            f"ids={quote(args.experiment_id)}"
            if args.experiment_id
            else f"experiment_name={quote(args.experiment_name)}"
        )
        response = requests.get(
            f"{api_url}/v1/experiment?{project_arg}&{experiment_arg}",
            headers={"Authorization": f"Bearer {BRAINTRUST_API_KEY}"},
        )
        response.raise_for_status()
        experiments = response.json()["objects"]
        for experiment in experiments:
            print(f"Experiment {experiment['id']}: {experiment['name']}")
            print(f"  {json.dumps(experiment, indent=2)}")
