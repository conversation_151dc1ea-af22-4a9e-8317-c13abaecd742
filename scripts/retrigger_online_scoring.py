#!/usr/bin/env python3

import argparse
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

import requests


def process_batch(
    object_type: str,
    object_id: str,
    created_start_ts: str,
    created_end_ts: str,
    api_url: str,
    api_key: str,
    batch_size: int,
    cursor: Optional[str] = None,
) -> tuple[List[Dict[str, Any]], Optional[str]]:
    """Process a single batch of rows and return the rows and next cursor."""
    # Construct and execute BTQL query
    query = f"select: id, created from: {object_type}('{object_id}') spans filter: created >= '{created_start_ts}' and created <= '{created_end_ts}' limit: {batch_size}"
    if cursor:
        query += f" cursor: '{cursor}'"

    resp = requests.post(f"{api_url}/btql", json=dict(query=query), headers=dict(Authorization=f"Bearer {api_key}"))
    resp.raise_for_status()
    data = resp.json()
    rows = data["data"]
    next_cursor = data.get("cursor")

    # Construct empty updates with a force-retrigger-scoring control.
    if object_type == "project_logs":
        object_ids = dict(project_id=object_id, log_id="g")
    else:
        raise Exception(f"Invalid object type: {object_type}")

    update_rows = [
        dict(**object_ids, id=r["id"], _async_scoring_control=dict(kind="state_enabled_force_rescore"), _is_merge=True)
        for r in rows
    ]

    # Send updates
    if update_rows:
        resp = requests.post(
            f"{api_url}/logs3",
            json=dict(rows=update_rows, api_version=2),
            headers=dict(Authorization=f"Bearer {api_key}"),
        )
        resp.raise_for_status()

    return rows, next_cursor


def main():
    parser = argparse.ArgumentParser(
        description="Re-triggers online scoring on all rows in a given object by running blank updates on all the rows",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--object-type", required=True, choices=["project_logs"], help="Type of BTQL object")
    parser.add_argument("--object-id", required=True, help="ID of the object")
    parser.add_argument("--created-start-ts", required=True, help="Start timestamp (ISO8601)")
    parser.add_argument("--created-end-ts", required=True, help="End timestamp (ISO8601)")
    parser.add_argument("--cursor-file", required=True, help="File to store/load pagination cursor")
    parser.add_argument("--api-key", required=True, help="API key for authentication")
    parser.add_argument("--api-url", required=True, help="API URL")
    parser.add_argument("--batch-size", type=int, default=100, help="Number of rows per batch")

    args = parser.parse_args()

    # Validate timestamps
    try:
        datetime.fromisoformat(args.created_start_ts)
        datetime.fromisoformat(args.created_end_ts)
    except ValueError:
        parser.error("Timestamps must be in ISO8601 format")

    # Load cursor if file exists
    cursor = None
    if args.cursor_file and os.path.exists(args.cursor_file):
        with open(args.cursor_file, "r") as f:
            cursor = f.read().strip()

    total_rows = 0
    while True:
        print(f"Processing batch with cursor: {cursor}")
        rows, next_cursor = process_batch(
            args.object_type,
            args.object_id,
            args.created_start_ts,
            args.created_end_ts,
            args.api_url,
            args.api_key,
            args.batch_size,
            cursor,
        )

        num_rows = len(rows)
        total_rows += num_rows
        if num_rows > 0:
            print(f"Processed {num_rows} rows in this batch, between {rows[0]['created']} and {rows[-1]['created']}")

        if next_cursor:
            cursor = next_cursor
            if args.cursor_file:
                with open(args.cursor_file, "w") as f:
                    f.write(cursor)
        else:
            print(f"Finished processing all rows. Total rows processed: {total_rows}")
            if args.cursor_file and os.path.exists(args.cursor_file):
                os.remove(args.cursor_file)
            break


if __name__ == "__main__":
    main()
