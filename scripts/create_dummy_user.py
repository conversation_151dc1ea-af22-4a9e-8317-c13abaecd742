#!/usr/bin/env python3

import argparse
from uuid import uuid4

import psycopg2
from braintrust_local import app_db_util

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Create a user in an artificial organization. Print out the newly-created user's information",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--org-name", help="Name of the org to add the user to", default="braintrustdata.com")
    parser.add_argument(
        "--is-org-owner",
        help="Whether or not the user should be part of the org-owners group",
        action="store_true",
        default=False,
    )
    parser.add_argument(
        "--user-email",
        help="Email of the user to create. Defaults to a unique string",
        default=f"_dummy_user_{uuid4()}",
    )
    args = parser.parse_args()

    with psycopg2.connect(app_db_util.get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute("select id from organizations where name = %s", (args.org_name,))
            org_id = cursor.fetchone()[0]
            cursor.execute(
                "insert into users(auth_id, email) values (uuid_generate_v4(), %s) returning id, auth_id",
                (args.user_email,),
            )
            user_id, user_auth_id = cursor.fetchone()
            if args.is_org_owner:
                cursor.execute(
                    "select add_member_to_org_unchecked(%s, %s, array [get_group_id(%s, 'Owners')])",
                    (
                        user_id,
                        org_id,
                        org_id,
                    ),
                )
            else:
                cursor.execute(
                    "select add_member_to_org_unchecked(%s, %s, null)",
                    (
                        user_id,
                        org_id,
                    ),
                )
            cursor.execute("select create_api_key(%s, %s, %s)", (user_auth_id, org_id, "dummy"))
            api_key = cursor.fetchone()[0]

    user_info = dict(id=user_id, email=args.user_email, api_key=api_key)
    print("Created user with info:\n", user_info)
