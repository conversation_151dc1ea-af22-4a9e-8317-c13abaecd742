#!/usr/bin/env python3

import argparse
import dataclasses
import datetime
import fnmatch
import itertools
import os
import subprocess
from collections import defaultdict
from typing import List

import git

ROOT_DIR = os.path.realpath(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
CACHE_PATH = os.path.expanduser("~/.cache/braintrust/repos")

GLOBAL_EXCLUDE_PATTERNS = [
    "*package-lock.json",
    "*pnpm-lock.yaml",
    "*deno.lock",
    "*.png",
    "*.gif",
    "*.parquet",
    "*/__snapshots__/*",
]


@dataclasses.dataclass
class RepoSpec:
    base_path: str
    main_branch: str
    exclude_patterns: List[str]
    is_separate_repo: bool = False


REPO_SPECS = {
    "root": RepoSpec(
        base_path=ROOT_DIR,
        main_branch="origin/main",
        exclude_patterns=[
            "*openapi_spec.json",
            "api-ts/src/proxy/test-proxy/snapshots/*",
            "app/content/docs/reference/api/*",
            "app/content/docs/reference/autoevals/nodejs/*",
            "app/content/docs/reference/autoevals/python.md",
            "app/content/docs/reference/libs/nodejs/*",
            "app/content/docs/reference/libs/python.md",
            "app/public/*",
            "brainstore*Cargo.lock",
            "brainstore*/schema.json",
            "brainstore*.btql.expected",
            "test-proxy/cache.json",
            "tests/bt_services/expect_tests/*.json",
            "tests/bt_services/expect_tests/*.telemetry.py",
        ],
    ),
    "sdk": RepoSpec(
        base_path=os.path.join(ROOT_DIR, "sdk"),
        main_branch="origin/main",
        exclude_patterns=[],
    ),
    "autoevals": RepoSpec(
        base_path=os.path.join(ROOT_DIR, "autoevals"),
        main_branch="origin/main",
        exclude_patterns=[],
    ),
    "proxy": RepoSpec(
        base_path=os.path.join(ROOT_DIR, "proxy"),
        main_branch="origin/main",
        exclude_patterns=[],
    ),
    "terraform": RepoSpec(
        base_path="**************:braintrustdata/terraform-braintrust-data-plane.git",
        main_branch="origin/main",
        exclude_patterns=[],
        is_separate_repo=True,
    ),
}


def make_author_stats():
    return defaultdict(lambda: {"total_lines": 0, "lines_per_file": defaultdict(int)})


def collect_stats(repo_spec: RepoSpec, start_time, end_time, author_stats, custom_revision_pattern=None):
    if repo_spec.is_separate_repo:
        os.makedirs(CACHE_PATH, exist_ok=True)
        full_repo_path = os.path.join(CACHE_PATH, os.path.basename(repo_spec.base_path))
        if not os.path.exists(full_repo_path):
            subprocess.run(
                ["git", "clone", repo_spec.base_path, full_repo_path],
                check=True,
            )
        subprocess.run(
            ["git", "fetch", "origin"],
            cwd=full_repo_path,
            check=True,
        )
        subprocess.run(
            ["git", "reset", "--hard", "origin/main"],
            cwd=full_repo_path,
            check=True,
        )
    else:
        full_repo_path = repo_spec.base_path

    repo = git.Repo(full_repo_path)
    exclude_patterns = GLOBAL_EXCLUDE_PATTERNS + repo_spec.exclude_patterns
    commits = list(
        repo.iter_commits(
            custom_revision_pattern if custom_revision_pattern else repo_spec.main_branch,
            after=start_time,
            before=end_time,
        )
    )
    lines_per_author = defaultdict(int)
    for commit in commits:
        author_name = commit.author.name
        for filename, stats in commit.stats.files.items():
            if any(fnmatch.fnmatch(filename, pattern) for pattern in exclude_patterns):
                continue
            author_stats[author_name]["total_lines"] += stats["lines"]
            author_stats[author_name]["lines_per_file"][
                os.path.relpath(os.path.join(repo_spec.base_path, filename), ROOT_DIR)
            ] += stats["lines"]
    return lines_per_author


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Calculate commit stats per author",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--start-time", help="Start time for commits, in ISO 8601 format")
    parser.add_argument("--end-time", help="End time for commits, in ISO 8601 format")
    parser.add_argument(
        "--last-week",
        action="store_true",
        help="Shortcut for setting start_time to the beginning of Monday of the previous week and the end time to the end of Sunday of the previous week",
    )
    parser.add_argument("--num-top-files", type=int, help="Number of top filenames to show per author", default=5)
    parser.add_argument(
        "--data-benchmark-override",
        action="store_true",
        help="If true, compute a special result for the data-benchmark branch",
    )
    args = parser.parse_args()

    if args.last_week:
        assert args.start_time is None and args.end_time is None
        today_date = datetime.date.today()
        latest_monday_date = today_date - datetime.timedelta(days=(today_date.isoweekday() - 1))
        previous_monday_date = latest_monday_date - datetime.timedelta(days=7)
        start_time = datetime.datetime.combine(previous_monday_date, datetime.time())
        end_time = datetime.datetime.combine(latest_monday_date, datetime.time()) - datetime.timedelta(microseconds=1)
    elif args.data_benchmark_override:
        start_time = datetime.datetime.fromisoformat("2024-10-01T00:00:00")
        end_time = datetime.datetime.fromisoformat("2025-01-06T00:00:00")
    else:
        assert args.start_time is not None and args.end_time is not None
        start_time = datetime.datetime.fromisoformat(args.start_time)
        end_time = datetime.datetime.fromisoformat(args.end_time)

    author_stats = make_author_stats()

    if args.data_benchmark_override:
        collect_stats(
            REPO_SPECS["root"],
            start_time,
            end_time,
            author_stats,
            custom_revision_pattern="origin/manu/main-before-data-benchmark-preserved..origin/manu/data-benchmark-preserved",
        )
    else:
        for repo_spec in REPO_SPECS.values():
            collect_stats(repo_spec, start_time, end_time, author_stats)

    sorted_author_stats = {
        k: {
            "total_lines": v["total_lines"],
            "lines_per_file": dict(sorted(v["lines_per_file"].items(), key=lambda item: item[1], reverse=True)),
        }
        for k, v in sorted(author_stats.items(), key=lambda item: item[1]["total_lines"], reverse=True)
    }
    print(f"Commit stats between {start_time.isoformat()} and {end_time.isoformat()}")
    for author_name, stats in sorted_author_stats.items():
        print(f"{author_name}: {stats['total_lines']}")
        for filename, lines in itertools.islice(stats["lines_per_file"].items(), args.num_top_files):
            print(f"  {filename}: {lines}")
