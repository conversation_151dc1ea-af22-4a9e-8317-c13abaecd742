#!/usr/bin/env python

import argparse
import json
import time
from typing import Any, Dict, List

import braintrust
from braintrust_local.backfill_util import catchup_brainstore_backfill


def insert_benchmark(input_file: str, num_records: int, project_name: str) -> None:
    """
    Run the insert benchmark by inserting the same record multiple times.

    Args:
        input_file: Path to JSON file containing a single record
        num_records: Number of times to insert the record
        project_name: Project name to insert records into
    """
    # Read the input record
    with open(input_file, "r") as f:
        record = json.load(f)

    # Extract required fields
    fields_to_extract = ["input", "output", "error", "expected", "tags", "scores", "metrics", "metadata"]
    log_record = {k: record.get(k) for k in fields_to_extract}

    # Initialize logger
    logger = braintrust.init_logger(project=project_name)

    # Time the inserts
    start_time = time.time()

    for _ in range(num_records):
        logger.log(**log_record)

    # Flush at the end
    logger.flush()

    end_time = time.time()
    duration = end_time - start_time

    print(f"Insert benchmark completed:")
    print(f"- Records inserted: {num_records}")
    print(f"- Total time: {duration:.2f} seconds")
    print(f"- Average time per record: {(duration/num_records)*1000:.2f} ms")


def backfill_benchmark(max_timeout_seconds: int, batch_size: int = None) -> None:
    """
    Run the backfill benchmark by calling catchupBrainstore until fully caught up.

    Args:
        max_timeout_seconds: Maximum time in seconds to wait for backfill to complete
        batch_size: Optional batch size for the backfill operation
    """
    braintrust.login()
    start_time = time.time()

    args = {"timeout": max_timeout_seconds}
    if batch_size is not None:
        args["batch_size"] = batch_size
    while True:
        try:
            catchup_brainstore_backfill(args)
            break
        except Exception as e:
            print(f"Backfill errored out:\n{e}\nContinuing...")

    end_time = time.time()
    duration = end_time - start_time

    print(f"Backfill benchmark completed:")
    print(f"- Total time: {duration:.2f} seconds")


def main():
    parser = argparse.ArgumentParser(
        description="""Benchmark insert and backfill operations. Make sure to run with the following settings:
    # On the api-ts server:
    BRAINSTORE_DISABLE_ETL_LOOP=true
    BRAINSTORE_DISABLE_COMPACTION=true
    # On the brainstore node:
    BRAINSTORE_DISABLE_OPTIMIZATION_WORKER=true""",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    subparsers = parser.add_subparsers(dest="command", help="Benchmark mode")

    # Insert benchmark parser
    insert_parser = subparsers.add_parser("insert", help="Run insert benchmark")
    insert_parser.add_argument("input_file", help="JSON file containing a single record to insert")
    insert_parser.add_argument("num_records", type=int, help="Number of records to insert")
    insert_parser.add_argument("project_name", help="Project name to insert records into")

    # Backfill benchmark parser
    backfill_parser = subparsers.add_parser("backfill", help="Run backfill benchmark")
    backfill_parser.add_argument(
        "--max-timeout", type=int, default=300, help="Maximum time in seconds to wait for backfill (default: 300)"
    )
    backfill_parser.add_argument(
        "--batch-size", type=int, default=None, help="Optional batch size for the backfill operation"
    )

    args = parser.parse_args()

    if args.command == "insert":
        insert_benchmark(args.input_file, args.num_records, args.project_name)
    elif args.command == "backfill":
        backfill_benchmark(args.max_timeout, args.batch_size)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
