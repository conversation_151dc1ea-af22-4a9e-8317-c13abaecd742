#!/usr/bin/env python3

import os
import sys

if __name__ == "__main__":
    entries = {}

    target_dir = sys.argv[1]
    for root, dirs, files in os.walk(target_dir):
        for f in files:
            if not f.endswith(".md"):
                continue
            title = f.split(".")[0]
            contents = open(os.path.join(root, f), "r").read()

            with open(os.path.join(root, f), "w") as f:
                f.write(f"---\ntitle: {title}\n---\n\n{contents}")
