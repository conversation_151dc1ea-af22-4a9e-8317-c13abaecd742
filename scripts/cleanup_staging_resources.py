#!/usr/bin/env python3

import sys

import yaml

try:
    from yaml import <PERSON>umper as Dumper
    from yaml import <PERSON><PERSON>oader as Loader
except ImportError:
    from yaml import Dumper, Loader


if __name__ == "__main__":
    target_file = sys.argv[1]
    with open(target_file, "r") as f:
        contents = f.read()

    template = yaml.load(contents, Loader=Loader)

    del template["Resources"]["AutomationCron"]

    # redis is shared in staging and production. thus we only want one instance running the cron job
    del template["Resources"]["BillingCron"]

    with open(target_file, "w") as f:
        f.write(yaml.dump(template, Dumper=Dumper))
