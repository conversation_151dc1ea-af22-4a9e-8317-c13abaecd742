#!/usr/bin/env python3

import argparse
import warnings

import psycopg2

warnings.warn("This script is deprecated. You should use the /admin page instead.", DeprecationWarning, stacklevel=2)


def main(args):
    with psycopg2.connect(args.db_url) as conn:
        with conn.cursor() as cursor:
            cursor.execute("select id, name from organizations where name ilike %s", ("%" + args.org_name + "%",))
            matching_orgs = [dict(id=row[0], name=row[1]) for row in cursor.fetchall()]
            if not matching_orgs:
                raise Exception(f"Found no orgs matching name {args.org_name}")

            print("Found the following matching orgs:")
            for org in matching_orgs:
                print(org)
            org_id_to_modify = input("Enter the id of the org you wish to modify: ").strip()
            if not org_id_to_modify:
                print("Exiting")
                return

            cursor.execute("select * from resources where org_id = %s", (org_id_to_modify,))
            matching_resource = cursor.fetchone()
            if args.action == "add":
                if matching_resource:
                    raise Exception(
                        f"Resource definition already exists for org {org_id_to_modify}. Remove their resource limit first if you want to change it."
                    )
                confirm_add = input(
                    f"Enter YES to add a resource definition for {org_id_to_modify} at tier {args.tier}: "
                ).strip()
                if confirm_add == "YES":
                    cursor.execute("select insert_resource_definition(%s, %s)", (org_id_to_modify, args.tier))
                    print("Successfully added")
                else:
                    print("Exiting")
                    return
            elif args.action == "remove":
                if not matching_resource:
                    raise Exception(f"No resource definition found for org {org_id_to_modify}")
                print("Found matching resource definition:\n", matching_resource)
                confirm_delete = input("Enter YES to delete this resource definition: ").strip()
                if confirm_delete == "YES":
                    cursor.execute("delete from resources where org_id = %s", (org_id_to_modify,))
                    print("Successfully deleted")
                else:
                    print("Exiting")
                    return
            elif args.action == "raise" or args.action == "lower":
                if not matching_resource:
                    raise Exception(f"No resource definition found for org {org_id_to_modify}")
                print("Found matching resource definition:\n", matching_resource)
                confirm_modify = input(f"Enter YES to {args.action} this resource definition: ").strip()
                if confirm_modify == "YES":
                    resource_value = 1000 if args.action == "lower" else 100000
                    cursor.execute(
                        f"""UPDATE resources SET
  num_private_experiment_row_actions=(7,{resource_value}),
  num_private_experiment_row_actions_calendar_months=(1,{resource_value} * 4),
  num_production_log_row_actions=(7,{resource_value}),
  num_production_log_row_actions_calendar_months=(1,{resource_value} * 4),
  num_dataset_row_actions=(7,{resource_value}),
  num_dataset_row_actions_calendar_months=(1,{resource_value} * 4),
  num_log_bytes=(7,{resource_value} * 10),
  num_log_bytes_calendar_months=(1,{resource_value} * 10 * 4)
  WHERE org_id = %s""",
                        (org_id_to_modify,),
                    )
                    print("Successfully", "lowered" if args.action == "lower" else "raised")
                else:
                    print("Exiting")
                    return
            else:
                raise Exception(f"Invalid action {args.action}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Remove the resource limits on a particular org",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--action",
        choices=["add", "raise", "lower", "remove"],
        required=True,
        help="The operation to perform on the organization's resource definition.",
    )
    parser.add_argument(
        "--org-name",
        required=True,
        help="Name of the org to add the user to. Searches for partial, case-insensitive matches",
    )
    parser.add_argument("--db-url", required=True)
    parser.add_argument("--tier", choices=["free", "edu", "unlimited"], help="If action=add, specify the tier")
    args = parser.parse_args()
    if args.action == "add" and not args.tier:
        raise Exception("Must specify --tier when adding a resource definition")
    main(args)
