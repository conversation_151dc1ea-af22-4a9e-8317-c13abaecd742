#!/usr/bin/env python3

# Scrapes the package metadata information for all installed JS and PY packages.

import fnmatch
import json
import os.path
import subprocess
import sys
from importlib.metadata import distribution

import git
from packaging.requirements import Requirement

SCRIPT_DIR = os.path.dirname(__file__)
REPO_ROOT = os.path.join(SCRIPT_DIR, "..")


def collect_js_metadata_for_package(package_path):
    res = subprocess.run(["npx", "license-checker", "--json"], capture_output=True, check=True, cwd=package_path)
    return json.loads(res.stdout.decode())


def collect_all_js_metadata():
    # Find all braintrust packages in the repo.
    res = subprocess.run(
        ["pnpm", "list", "-r", "--depth", "-1", "--json"], capture_output=True, check=True, cwd=REPO_ROOT
    )
    package_infos = json.loads(res.stdout.decode())

    all_metadata = {}
    for p in package_infos:
        try:
            print(f"Collecting metadata for JS package {p['path']}", file=sys.stderr)
            all_metadata.update(collect_js_metadata_for_package(p["path"]))
        except Exception as e:
            print(f"Failed to get metadata for JS package {p['path']}: {e}", file=sys.stderr)

    return all_metadata


def collect_py_metadata_for_package(package):
    return dict(distribution(package).metadata)


def collect_all_py_metadata():
    # Find all pip packages in the repo.
    repo = git.Repo(REPO_ROOT)
    all_requirements_files = fnmatch.filter([e.path for e in repo.index.entries.values()], "*requirements.txt")

    all_packages = []
    for fpath in all_requirements_files:
        with open(os.path.join(REPO_ROOT, fpath)) as f:
            for line in f:
                line = line.strip()
                try:
                    # Skip empty lines and comments
                    if not line or line.startswith("#"):
                        continue

                    # Extract package name - handles various formats like:
                    # package==1.0, package>=1.0, package, etc.
                    package_name = Requirement(line).name
                    all_packages.append(package_name)
                except Exception as e:
                    # Skip lines that can't be parsed as requirements
                    print(f"Failed to parse line {line} in {f}: {e}", file=sys.stderr)
                    continue

    all_metadata = {}
    for p in all_packages:
        try:
            print(f"Collecting metadata for PY package {p}", file=sys.stderr)
            all_metadata[p] = collect_py_metadata_for_package(p)
        except Exception as e:
            print(f"Failed to get metadata for PY package {p}: {e}", file=sys.stderr)

    return all_metadata


if __name__ == "__main__":
    js_metadata = collect_all_js_metadata()
    py_metadata = collect_all_py_metadata()
    print(json.dumps(dict(js=js_metadata, py=py_metadata)))
