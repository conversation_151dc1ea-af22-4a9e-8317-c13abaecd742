#!/usr/bin/env python3

import os
import sys

import yaml

try:
    from yaml import <PERSON><PERSON><PERSON> as Dumper
    from yaml import <PERSON><PERSON><PERSON><PERSON> as Loader
except ImportError:
    from yaml import Dumper, Loader

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

TRACEABLE_FUNCTIONS = [
    "APIHandlerJS",
    "AIProxyFn",
    "ClickhouseCatchupEtl",
    "AutomationCron",
    "BillingCron",
]


def add_datadog_tracing(name, function_block):
    properties = function_block.get("Properties", {})
    function_block["Properties"] = properties

    assert properties["Runtime"] == "nodejs22.x"

    layers = properties.get("Layers", [])
    properties["Layers"] = layers
    layers.append(
        {
            "Fn::If": [
                "HasInternalObservabilityEnabled",
                {"Fn::Sub": "arn:aws:lambda:${AWS::Region}:464622532012:layer:Datadog-Node22-x:121"},
                {"Ref": "AWS::NoValue"},
            ]
        }
    )
    layers.append(
        {
            "Fn::If": [
                "HasInternalObservabilityEnabled",
                {"Fn::Sub": "arn:aws:lambda:${AWS::Region}:464622532012:layer:Datadog-Extension-ARM:77"},
                {"Ref": "AWS::NoValue"},
            ]
        }
    )

    current_handler = properties["Handler"]
    properties["Handler"] = {
        "Fn::If": [
            "HasInternalObservabilityEnabled",
            "/opt/nodejs/node_modules/datadog-lambda-js/handler.handler",
            current_handler,
        ]
    }

    environment = properties.get("Environment", {})
    properties["Environment"] = environment

    variables = environment.get("Variables", {})
    environment["Variables"] = variables

    variables.update(
        {
            "DD_SITE": {"Fn::If": ["HasInternalObservabilityEnabled", "us5.datadoghq.com", ""]},
            "DD_API_KEY": {"Fn::If": ["HasInternalObservabilityEnabled", {"Ref": "InternalObservabilityApiKey"}, ""]},
            "DD_ENV": {"Ref": "InternalObservabilityEnvName"},
            "DD_SERVICE": name,
            "DD_LAMBDA_HANDLER": current_handler,
            "OTLP_HTTP_ENDPOINT": {"Fn::If": ["HasInternalObservabilityEnabled", "http://localhost:4318", ""]},
        }
    )

    properties["LoggingConfig"] = {"LogFormat": "JSON"}

    return function_block


if __name__ == "__main__":
    target_file = sys.argv[1]

    sys.path.append(os.path.join(CURRENT_DIR, "..", "api"))
    import app

    rewrite_to_js = set()
    for name, handlers in app.app.routes.items():
        if any(h.view_function == app.proxy_to_js_api for h in handlers.values()):
            rewrite_to_js.add(name)

    with open(target_file, "r") as f:
        contents = f.read()

    template = yaml.load(contents, Loader=Loader)
    api_path = template["Resources"]["RestAPI"]["Properties"]["DefinitionBody"]["paths"]

    for path in rewrite_to_js:
        entry = api_path[path]
        for method, body in entry.items():
            if "x-amazon-apigateway-integration" in body:
                body["x-amazon-apigateway-integration"]["uri"] = {
                    "Fn::Sub": "arn:${AWS::Partition}:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${APIHandlerJS.Arn}/invocations"
                }

    etl_properties = template["Resources"]["ClickhouseCatchupEtl"]["Properties"]
    etl_properties.update(
        {
            "CodeUri": "../../api-ts/dist/lambda-etl/index.zip",
            "MemorySize": 1024,
            "Runtime": "nodejs22.x",
            "Architectures": ["arm64"],
            "Handler": "index.handler",
        }
    )

    automation_cron_properties = template["Resources"]["AutomationCron"]["Properties"]
    automation_cron_properties.update(
        {
            "CodeUri": "../../api-ts/dist/lambda-cron/index.zip",
            "MemorySize": 1024,
            "Runtime": "nodejs22.x",
            "Architectures": ["arm64"],
            "Handler": "index.handler",
        }
    )

    billing_cron_properties = template["Resources"]["BillingCron"]["Properties"]
    billing_cron_properties.update(
        {
            "CodeUri": "../../api-ts/dist/lambda-billing-cron/index.zip",
            "MemorySize": 1024,
            "Runtime": "nodejs22.x",
            "Architectures": ["arm64"],
            "Handler": "lambda.handler",
        }
    )

    for function_name in TRACEABLE_FUNCTIONS:
        template["Resources"][function_name] = add_datadog_tracing(function_name, template["Resources"][function_name])

    with open(target_file, "w") as f:
        f.write(yaml.dump(template, Dumper=Dumper))
