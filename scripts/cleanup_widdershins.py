#!/usr/bin/env python3

import argparse
import re
import sys
from io import StringIO

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("fname", type=str)
    args = parser.parse_args()

    with open(args.fname) as f:
        lines = f.readlines()

    out = StringIO()
    in_code = False
    in_code_tabs = False
    in_cors = False
    for line in lines:
        if line.startswith("## "):
            in_cors = "CORS" in line
        if in_cors:
            continue

        if not in_code:
            # Replace <a href="https://braintrustdata.com">Braintrust</a> with [Braintrust](https://braintrustdata.com)
            line = re.sub(r'<a href="([^"]*)">([^<]*)</a>', r"[\2](\1)", line)

            # Replace <br> with <br />
            line = re.sub(r"<br>", r"<br />", line)

            # Replace > 200 Response with **200 Response**
            line = re.sub(r">\s+(\d{3})\s+Response", r"**\1 Response**", line)

        if re.match(r"<.*CodeTabs.*>", line):
            in_code_tabs = not in_code_tabs

        if line.startswith("```"):
            in_code = not in_code

        if in_code_tabs and "```" in line:
            if in_code:
                line = "<Tab>\n\n" + line
            else:
                line = line + "\n\n</Tab>"

        out.write(line)

    with open(args.fname, "w") as f:
        f.write(out.getvalue())
