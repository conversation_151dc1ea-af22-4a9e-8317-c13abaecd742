#!/usr/bin/env python3
# python clone_project_logs.py --prod-api-key <PROD API KEY> --local-api-key <LOCAL API KEY> --project-name "Loop logs" --limit 1000

import argparse
import json
import os
import urllib.parse
from typing import Optional

import braintrust
from braintrust.logger import api_conn
from braintrust.util import <PERSON><PERSON><PERSON><PERSON><PERSON>


def get_project_id(project_name: str, api_conn_instance) -> str:
    """Get project ID from project name using REST API."""
    encoded_name = urllib.parse.quote(project_name)
    response = api_conn_instance.get_json(f"/v1/project?name={encoded_name}")

    if not response.get("objects") or len(response["objects"]) == 0:
        raise ValueError(f"Project '{project_name}' not found")

    return response["objects"][0]["id"]


def fetch_logs_via_btql(
    project_id: str, limit: int, api_conn_instance, use_pagination: bool = False, page_size: int = 1000
) -> list:
    """Fetch logs using BTQL query with optional pagination."""
    all_logs = []

    if use_pagination and limit > page_size:
        # Use pagination to fetch logs in batches
        cursor = None
        remaining_limit = limit

        while remaining_limit > 0:
            batch_limit = min(page_size, remaining_limit)

            query = f"""
    select: *
    from: project_logs('{project_id}') traces"""

            if cursor:
                query += f"\n    cursor: {cursor}"

            query += f"\n    limit: {batch_limit}"

            print(f"Executing paginated BTQL query (batch {len(all_logs)//page_size + 1}, limit: {batch_limit})")

            request_args = {"query": query.strip(), "fmt": "json"}
            response = api_conn_instance.post_json("/btql", request_args)

            batch_logs = response.get("data", [])
            cursor = response.get("cursor")

            if not batch_logs:
                print("No more logs available")
                break

            all_logs.extend(batch_logs)
            remaining_limit -= len(batch_logs)

            print(f"  Fetched {len(batch_logs)} logs (total: {len(all_logs)})")

            # If no cursor is returned, we've reached the end
            if not cursor:
                print("Reached end of results (no cursor returned)")
                break
    else:
        # Original single-query behavior
        query = f"""
    select: *
    from: project_logs('{project_id}') traces
    limit: {limit}
    """

        request_args = {"query": query.strip(), "fmt": "json"}
        response = api_conn_instance.post_json("/btql", request_args)
        all_logs = response.get("data", [])

    return all_logs


def clone_project_logs(
    prod_api_key: str,
    local_api_key: str,
    project_name: str,
    limit: int = 1000,
    local_project_name: Optional[str] = None,
):
    """Clone logs from production project to local project."""

    if not local_project_name:
        local_project_name = f"{project_name} [cloned]"

    # Set up production connection
    print(f"Connecting to production...")
    braintrust.login(api_key=prod_api_key, app_url="https://www.braintrust.dev", force_login=True)
    prod_conn = api_conn()

    # Get production project ID
    print(f"Finding project '{project_name}' in production...")
    try:
        prod_project_id = get_project_id(project_name, prod_conn)
        print(f"Found project ID: {prod_project_id}")
    except ValueError as e:
        print(f"Error: {e}")
        return

    # Fetch logs from production using BTQL
    print(f"Fetching up to {limit} logs from production...")
    logs = fetch_logs_via_btql(prod_project_id, limit, prod_conn, use_pagination=limit > 1000, page_size=1000)
    print(f"Fetched {len(logs)} logs from production")

    if not logs:
        print("No logs found to clone")
        return

    # Switch to local environment
    print(f"\nConnecting to local instance...")
    braintrust.login(force_login=True, api_key=local_api_key, app_url="http://localhost:3000")

    project_logger = braintrust.init_logger(project=local_project_name)

    # Get logger
    logger = braintrust.logger._state.global_bg_logger()

    # Process and insert logs
    print(f"Inserting logs into local project '{local_project_name}'...")
    for i, row in enumerate(logs):
        # Clean up the row data
        row_copy = row.copy()

        row_copy["project_id"] = project_logger.project.id

        # Remove fields that shouldn't be copied
        fields_to_remove = ["_xact_id", "created", "org_id"]
        for field in fields_to_remove:
            if field in row_copy:
                del row_copy[field]

        # Ensure span_parents is a list
        row_copy["span_parents"] = row_copy.get("span_parents") or []

        # Remove tags if it has span_parents (following the original logic)
        if len(row_copy.get("span_parents", [])) > 0 and "tags" in row_copy:
            del row_copy["tags"]

        # Log the row
        logger.log(LazyValue(lambda r=row_copy: r, use_mutex=False))

        if (i + 1) % 100 == 0:
            print(f"  Processed {i + 1} logs...")

    # Flush the logger
    logger.flush()

    print(f"\nSuccessfully cloned {len(logs)} logs to local project '{local_project_name}'")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Clone logs from a production Braintrust project to a local instance")
    parser.add_argument("--prod-api-key", type=str, required=True, help="API key for production Braintrust instance")
    parser.add_argument("--local-api-key", type=str, required=True, help="API key for local Braintrust instance")
    parser.add_argument("--project-name", type=str, required=True, help="Name of the project to clone from production")
    parser.add_argument("--limit", type=int, default=1000, help="Maximum number of logs to clone (default: 1000)")
    parser.add_argument(
        "--local-project-name",
        type=str,
        default=None,
        help="Name for the local project (default: '<project-name> [cloned]')",
    )

    args = parser.parse_args()

    clone_project_logs(
        prod_api_key=args.prod_api_key,
        local_api_key=args.local_api_key,
        project_name=args.project_name,
        limit=args.limit,
        local_project_name=args.local_project_name,
    )
