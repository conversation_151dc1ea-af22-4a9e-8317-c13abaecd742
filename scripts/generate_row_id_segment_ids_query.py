#!/usr/bin/env python3
import argparse
from uuid import uuid4

# Large object-ids
# Zapier-hosted: project_logs:b88a574e-16ea-420e-816e-6f0ba31d321a
# Braintrust-hosted: project_logs:2d8631cf-e534-46d2-b502-ce71eed7cb3b


def gen_query(object_id, num_row_ids, tbl_type):
    all_row_ids = [f"{object_id}:{uuid4()}" for _ in range(num_row_ids)]
    if tbl_type == "new":
        tbl_name = "brainstore_global_store_row_id_to_segment_id"
    elif tbl_type == "legacy":
        tbl_name = "brainstore_global_store_segment_id_to_row_info"
    else:
        raise Exception(f"Unknown table type {tbl_type}")
    return f"""
    select row_id, segment_id
    from {tbl_name}
    where row_id = any('{{ {",".join(all_row_ids)} }}');
    """


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--object-id", type=str, help="Object ID to query over")
    parser.add_argument("--num-row-ids", type=int, help="Number of row ids to query")
    parser.add_argument("--table-type", choices=["new", "legacy"], help="Table type to query")
    args = parser.parse_args()
    print(gen_query(args.object_id, args.num_row_ids, args.table_type))
