import json
import os
import sys
from io import StringIO

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

PG_URL = os.environ.get("PG_URL", "postgres://postgres:postgres@localhost:5532/postgres")

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))


if __name__ == "__main__":
    conn = psycopg2.connect(PG_URL)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    conn.autocommit = True

    ret = {}
    for object_type in ["dataset", "experiment", "prompt_session"]:
        cursor.execute(
            f"""
            SELECT DISTINCT {object_type}_id, data->>'user_id' user_id
            FROM logs
            WHERE {object_type}_id IS NOT NULL
            """
        )

        ret[object_type] = [{"id": object_id, "user_id": user_id} for (object_id, user_id) in cursor.fetchall()]

    with open("backfill.json", "w") as f:
        json.dump(ret, f, indent=2)
