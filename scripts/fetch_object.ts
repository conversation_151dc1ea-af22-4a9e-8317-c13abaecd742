async function main() {
  // Url of the API server to fetch from.
  const apiUrl = "https://api.braintrust.dev";
  // Authentication token.
  const authToken = "YOUR_AUTH_TOKEN";
  // Object type to fetch. One of "experiment", "dataset", "project_logs"
  const objectType = "YOUR_OBJECT_TYPE";
  // Object id to fetch.
  const objectId = "YOUR_OBJECT_ID";
  // Maximum number of elements to fetch in each request.
  const limit = 100;
  // Optional version string for fetching older versions of the object.
  const version: string | undefined = undefined;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const idToRow = new Map<string, any>();
  let max_xact_id: string | undefined = undefined;
  let max_root_span_id: string | undefined = undefined;
  while (true) {
    const resp = await fetch(`${apiUrl}/v1/${objectType}/${objectId}/fetch`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Accept-Encoding": "gzip",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        limit,
        max_root_span_id,
        max_xact_id,
        version,
      }),
    });

    if (!resp.ok) {
      throw new Error(
        `Error fetching rows ${resp.status}: ${await resp.text()}`,
      );
    }

    const allRows = (await resp.json()).events;
    if (allRows.length === 0) {
      break;
    }
    for (const row of allRows) {
      if (!idToRow.has(row.id)) {
        idToRow.set(row.id, row);
      }

      if (
        max_xact_id === undefined ||
        max_root_span_id === undefined ||
        row["_xact_id"] < max_xact_id ||
        (row["_xact_id"] === max_xact_id &&
          row["root_span_id"] < max_root_span_id)
      ) {
        max_xact_id = row["_xact_id"];
        max_root_span_id = row["root_span_id"];
      }
    }
  }

  console.log(JSON.stringify([...idToRow.values()]));
}

main();
