#!/usr/bin/env python3

# This script is used to pull images in a docker compose file in a retry loop.

import argparse

from braintrust_local.pull_docker_compose import pull_docker_compose

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Pull docker compose images in a retry loop",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("-f", required=True, help="Path to the docker compose file")
    args = parser.parse_args()
    pull_docker_compose(args.f)
