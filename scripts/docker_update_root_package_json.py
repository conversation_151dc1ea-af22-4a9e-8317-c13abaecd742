#!/usr/bin/env python3
"""
This script removes all workspace dependencies from the root package.json file,
so that running pnpm install doesn't complain about missing workspace
directories.
"""

import json

if __name__ == "__main__":
    PACKAGE_JSON_PATH = "/braintrust/package.json"
    with open(PACKAGE_JSON_PATH) as f:
        data = json.load(f)
    workspace_deps = {"workspace:*", "workspace:^"}
    data["dependencies"] = {k: v for k, v in data["dependencies"].items() if v not in workspace_deps}
    data["devDependencies"] = {k: v for k, v in data["devDependencies"].items() if v not in workspace_deps}
    with open(PACKAGE_JSON_PATH, "w") as f:
        json.dump(data, f)
