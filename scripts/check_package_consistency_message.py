#!/usr/bin/env python3

if __name__ == "__main__":
    print(
        """-------- START IMPORTANT MESSAGE --------

After you are done publishing everything, please make sure to run
`./scripts/check_package_consistency.py` until it's happy with both js and py
distributions. It might take a minute or so for the latest package versions to
propagate to the package registries, but eventually both distributions should be
able to successfully install the latest versions of all braintrust packages.

-------- END IMPORTANT MESSAGE --------
"""
    )
