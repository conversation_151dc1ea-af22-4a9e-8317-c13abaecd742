#!/usr/bin/env python3

import argparse
import os
import subprocess
import tempfile

import pyperclip
from braintrust_local.util import eprint

CACHE_DIR = os.path.join(os.path.expanduser("~"), ".cache", "braintrust", "unminify_stacktrace")


def grab_stack_trace(filename=None):
    if filename:
        with open(filename, "r") as f:
            return f.read()
    else:
        return pyperclip.paste()


def unminify_stacktrace(sourcemap_file, stack_trace):
    with tempfile.NamedTemporaryFile(mode="w") as f:
        f.write(stack_trace)
        f.flush()
        stack_trace_file = f.name

        result = subprocess.run(
            ["npx", "stacktracify", sourcemap_file, "-f", stack_trace_file], check=True, capture_output=True, text=True
        )
        stack_trace_unminified = result.stdout
    print(stack_trace_unminified)


def unminify_docker_api_ts(args):
    os.makedirs(CACHE_DIR, exist_ok=True)

    # Download and unzip the zipfile if it doesn't yet exist.
    zipfile = f"braintrust-standalone-api-full-build-{args.commit}-{args.arch}.zip"
    zip_cache_path = os.path.join(CACHE_DIR, zipfile)
    if not os.path.exists(zip_cache_path):
        eprint(f"Downloading api-ts zipfile to {zip_cache_path}")
        subprocess.run(
            ["aws", "s3", "cp", f"s3://braintrust-standalone-api-full-build/{zipfile}", zip_cache_path], check=True
        )
    unzipped_dir = os.path.join(CACHE_DIR, f"braintrust-standalone-api-full-build-{args.commit}-{args.arch}")
    if not os.path.exists(unzipped_dir):
        eprint(f"Unzipping to {zip_cache_path}")
        subprocess.run(["unzip", zip_cache_path, "-d", unzipped_dir], check=True)

    sourcemap_file = os.path.join(unzipped_dir, "braintrust-standalone-api-full-build", args.sourcemap)
    if not os.path.exists(sourcemap_file):
        raise Exception(f"Could not find sourcemap file {sourcemap_file} in {unzipped_dir}")
    eprint(f"Using sourcemap at {sourcemap_file}")

    stack_trace = grab_stack_trace(args.f)
    unminify_stacktrace(sourcemap_file, stack_trace)


def unminify_lambda_api_ts(args):
    os.makedirs(CACHE_DIR, exist_ok=True)

    # Clone the repo if it doesn't exist
    repo_dir = os.path.join(CACHE_DIR, f"braintrust-repo-{args.commit}")
    if not os.path.exists(repo_dir):
        eprint(f"Cloning braintrust repo to {repo_dir}")
        os.makedirs(repo_dir)
        subprocess.run(["git", "init"], cwd=repo_dir, check=True)
        subprocess.run(
            ["git", "remote", "add", "origin", "https://github.com/braintrustdata/braintrust"],
            cwd=repo_dir,
            check=True,
        )
        subprocess.run(["git", "fetch", "--depth", "1", "origin", args.commit], cwd=repo_dir, check=True)
        subprocess.run(["git", "checkout", "FETCH_HEAD"], cwd=repo_dir, check=True)

        eprint("Initializing submodules")
        subprocess.run(["git", "submodule", "update", "--init"], cwd=repo_dir, check=True)

        # Build the project
        eprint("Building the project")
        subprocess.run(["make", "develop"], cwd=repo_dir, check=True)
        subprocess.run(["pnpm", "build", "--filter", "@braintrust/api-ts"], cwd=repo_dir, check=True)

    # Use the default sourcemap path or the one specified in args
    sourcemap_file = os.path.join(repo_dir, args.sourcemap)
    if not os.path.exists(sourcemap_file):
        raise Exception(f"Could not find sourcemap file {sourcemap_file} in {repo_dir}")
    eprint(f"Using sourcemap at {sourcemap_file}")

    stack_trace = grab_stack_trace(args.f)
    unminify_stacktrace(sourcemap_file, stack_trace)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Unminify stack traces", formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("-f", help="File to read stack trace from. If unspecified, uses clipboard.")

    subparsers = parser.add_subparsers()
    parser_docker_api_ts = subparsers.add_parser(
        "docker_api_ts", help="Unminify a stack trace from a Docker api-ts build"
    )
    parser_docker_api_ts.add_argument("--commit", help="Git commit SHA of the docker image", required=True)
    parser_docker_api_ts.add_argument(
        "--arch", help="Build architecture", choices=["aarch64", "x86_64"], default="x86_64"
    )
    parser_docker_api_ts.add_argument(
        "--sourcemap",
        help="Relative path to the sourcemap file from the braintrust root directory",
        default="api-ts/dist/local/local.js.map",
    )
    parser_docker_api_ts.set_defaults(func=unminify_docker_api_ts)

    parser_lambda_api_ts = subparsers.add_parser(
        "lambda_api_ts", help="Unminify a stack trace from a lambda api-ts build"
    )
    parser_lambda_api_ts.add_argument("--commit", help="Git commit SHA of the lambda build", required=True)
    parser_lambda_api_ts.add_argument(
        "--sourcemap",
        help="Relative path to the sourcemap file from the braintrust root directory",
        default="api-ts/dist/lambda/index.js.map",
    )
    parser_lambda_api_ts.set_defaults(func=unminify_lambda_api_ts)

    args = parser.parse_args()
    args.func(args)
