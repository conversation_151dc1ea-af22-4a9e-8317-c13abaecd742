#!/usr/bin/env python3

import os
import subprocess

if __name__ == "__main__":
    res = subprocess.run("git config --file .gitmodules --get-regexp path".split(), check=True, capture_output=True)
    submodule_dirs = [l.split()[1] for l in res.stdout.decode().strip().split("\n")]
    all_dirs = ["."] + submodule_dirs

    for d in all_dirs:
        if os.path.exists(os.path.join(d, ".pre-commit-config.yaml")):
            subprocess.run("pre-commit install".split(), check=True, cwd=d)
