#!/usr/bin/env python3

import os
import subprocess
import sys

REPO_ROOT = os.path.realpath(os.path.join(os.path.dirname(__file__), ".."))

if __name__ == "__main__":
    try:
        subprocess.run(
            [
                "python",
                "local/py/src/braintrust_local/doc_snippets.py",
                "--write",
                "--fail-on-write",
                "--verbose",
                *sys.argv[1:],
            ],
            cwd=REPO_ROOT,
            check=True,
        )
    except subprocess.CalledProcessError as e:
        print("Doc snippet formatting changed some files")
        sys.exit(e.returncode)
