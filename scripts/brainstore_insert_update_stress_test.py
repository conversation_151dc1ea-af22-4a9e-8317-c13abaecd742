#!/usr/bin/env python3

"""
Stress test for Braintrust logging with parallel insert/updates.
"""

import argparse
import random
import threading
import time
import uuid
from queue import Queue
from typing import List, Optional

import braintrust

# Create a global UUID for this run
RUN_ID = str(uuid.uuid4())


def make_row_id(i: int) -> str:
    return f"{RUN_ID}-{i}"


def update_worker(logger: braintrust.Logger, update_queue: Queue):
    """Background worker that processes updates from the queue."""
    while True:
        update_row_index = update_queue.get()
        if update_row_index is None:  # Poison pill to stop the worker
            break
        try:
            row_id = make_row_id(update_row_index)
            logger.update_span(
                row_id,
                expected=f"updated_input_{update_row_index}",
                input=f"updated_input_{update_row_index}",
                output=f"updated_output_{update_row_index}",
                metadata={"row_id": row_id},
            )
        except Exception as e:
            print(f"Error in update worker: {e}")
        finally:
            update_queue.task_done()


def main():
    parser = argparse.ArgumentParser(
        description="Braintrust logging stress test", formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("project_name", help="Project name to log to")
    parser.add_argument("max_rows", type=int, help="Maximum number of rows to write")
    parser.add_argument(
        "--sampling-fraction",
        type=float,
        default=0.05,
        help="Fraction of rows to update (0-1)",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=1000,
        help="Number of rows to write before computing updates",
    )

    args = parser.parse_args()

    # Validate arguments
    if not 0 <= args.sampling_fraction <= 1:
        raise ValueError("Sampling fraction must be between 0 and 1")

    # Initialize logger
    logger = braintrust.init_logger(project=args.project_name)

    print(f"Starting stress test with run ID: {RUN_ID}")

    # Create update queue and start background worker
    update_queue = Queue()
    update_thread = threading.Thread(target=update_worker, args=(logger, update_queue), daemon=True)
    update_thread.start()

    try:
        # Process rows in batches
        for batch_start in range(0, args.max_rows, args.batch_size):
            batch_end = min(batch_start + args.batch_size, args.max_rows)
            print(f"Processing batch {batch_start} to {batch_end}")

            # Insert batch
            for i in range(batch_start, batch_end):
                row_id = make_row_id(i)
                logger.log(
                    id=row_id,
                    input=f"test_input_{i}",
                    output=f"test_output_{i}",
                    metadata={"row_id": row_id},
                )

            # Flush the logger after each batch
            logger.flush()
            print(f"Flushed batch {batch_start} to {batch_end}")

            # Sample rows for updates
            num_updates = int((batch_end - batch_start) * args.sampling_fraction)
            update_indices = random.sample(range(batch_start, batch_end), num_updates)

            # Queue updates
            for i in update_indices:
                update_queue.put(i)
            print(f"Queued {num_updates} updates for batch {batch_start} to {batch_end}")

    finally:
        # Signal the update worker to stop and wait for it
        update_queue.put(None)
        update_thread.join()
        logger.flush()
        print("Stress test completed")


if __name__ == "__main__":
    main()
