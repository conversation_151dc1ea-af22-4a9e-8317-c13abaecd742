#!/usr/bin/env python3

import concurrent.futures
import os
import subprocess
import sys


def get_required_by(package_name):
    print(f"Getting info for {package_name}", file=sys.stderr)
    try:
        proc = subprocess.run(["pip", "show", package_name], check=True, capture_output=True)
        lines = proc.stdout.decode().split("\n")
        for l in lines:
            l = l.strip()
            if l.startswith("Required-by"):
                results = [x.strip() for x in l[l.index(":") + 1 :].strip().split(",")]
                return [r for r in results if r]
    except Exception as e:
        print(f"Failed to fetch users of package {package_name}:\n", e, file=sys.stderr)
        return None


if __name__ == "__main__":
    all_packages = [
        a[: a.index("==")]
        for a in open(os.path.join(os.path.dirname(__file__), "..", "requirements_lock.txt")).readlines()
    ]
    with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(get_required_by, package) for package in all_packages]
        for package, future in zip(all_packages, futures):
            result = future.result()
            if result is None:
                continue
            elif result == []:
                print(package)
