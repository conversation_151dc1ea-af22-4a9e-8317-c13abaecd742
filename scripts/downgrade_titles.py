#!/usr/bin/env python3

import argparse
import re
from io import StringIO

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("fname", type=str)
    parser.add_argument("--ignore-first", action="store_true")
    args = parser.parse_args()

    with open(args.fname) as f:
        lines = f.readlines()

    out = StringIO()
    first = True
    in_code = False
    for line in lines:
        is_markdown_header = line.startswith("#")
        is_html_header = re.match(r"<h(\d)", line)
        if (is_markdown_header or is_html_header) and not in_code:
            if is_html_header:
                # Replace <h1 id="foo">Title</h1> with # Title
                headerName = int(is_html_header.group(1))
                line = "#" * headerName + " " + line.split(">")[1].split("<")[0] + "\n"

            if first and args.ignore_first:
                first = False
            else:
                out.write("#" + line)
                continue

        if line.startswith("```"):
            in_code = not in_code
        out.write(line)

    with open(args.fname, "w") as f:
        f.write(out.getvalue())
