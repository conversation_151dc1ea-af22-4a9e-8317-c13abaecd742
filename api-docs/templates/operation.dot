{{= data.tags.section }}

## {{= data.operationUniqueName}}

{{? data.operation.operationId}}
<a id="opId{{=data.operation.operationId}}"></a>
{{?}}

{{ data.methodUpper = data.method.verb.toUpperCase(); }}
{{ data.url = data.utils.slashes(data.baseUrl + data.method.path); }}
{{ data.parameters = data.operation.parameters; }}
{{ data.enums = []; }}
{{ data.utils.fakeProdCons(data); }}
{{ data.utils.fakeBodyParameter(data); }}
{{ data.utils.mergePathParameters(data); }}
{{ data.utils.getParameters(data); }}

{{? data.options.codeSamples || data.operation["x-code-samples"] }}
<H4>Code samples</H4>

<CodeTabs items={[{{~ data.header.language_tabs :e}}'{{=Object.values(e)[0]}}',{{~}} ]}>

{{= data.utils.getCodeSamples(data) }}

</CodeTabs>

{{?}}

`{{= data.methodUpper}} {{=data.method.path}}`

{{? data.operation.summary && !data.options.tocSummary}}*{{= data.operation.summary }}*{{?}}

{{? data.operation.description}}{{= data.operation.description }}{{?}}

{{? data.operation.requestBody}}
<H4>Body parameter</H4>

{{? data.bodyParameter.exampleValues.description }}
> {{= data.bodyParameter.exampleValues.description }}
{{?}}

{{= data.utils.getBodyParameterExamples(data) }}
{{?}}

{{? data.parameters && data.parameters.length }}
{{#def.parameters}}
{{?}}

{{#def.responses}}

{{#def.callbacks}}

{{ data.security = data.operation.security ? data.operation.security : data.api.security; }}
{{? data.security && data.security.length }}
{{#def.authentication}}
{{??}}
{{#def.authentication_none}}
{{?}}
{{= data.tags.endSection }}
