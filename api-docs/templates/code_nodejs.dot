const fetch = require('node-fetch');
{{?data.bodyParameter.present}}const inputBody = {{=data.bodyParameter.exampleValues.json}};{{?}}
{{?data.allHeaders.length}}const headers = {
{{~data.allHeaders :p:index}}  '{{=p.name}}':{{=p.exampleValues.json}}{{?index < data.allHeaders.length-1}},{{?}}
{{~}}};
{{?}}
fetch('{{=data.url}}{{=data.requiredQueryString}}',
{
  method: '{{=data.methodUpper}}'{{?data.bodyParameter.present || data.allHeaders.length}},{{?}}
{{?data.bodyParameter.present}}  body: JSON.stringify(inputBody){{?}}{{? data.bodyParameter.present && data.allHeaders.length}},{{?}}
{{?data.allHeaders.length}}  headers: headers{{?}}
})
.then(function(res) {
    return res.json();
}).then(function(body) {
    console.log(body);
});
