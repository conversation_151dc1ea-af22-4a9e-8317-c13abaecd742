{{? typeof data.operation.callbacks === 'object'}}

### Callbacks

{{ data.operationStack.push(data.operation); }}

{{ for (var c of Object.keys(data.operation.callbacks)) { }}

#### {{=c}}

{{ var callback = data.operation.callbacks && data.operation.callbacks[c]; }}

{{ for (var e in callback) { }}
{{ if (!e.startsWith('x-widdershins-')) { }}

**{{=e}}**

{{ var exp = callback[e]; }}

{{ for (var m in exp) { }}

{{ data.operation = exp[m]; }}
{{ data.method.operation = data.operation; }}

{{= data.templates.operation(data) }}

{{ } /* of methods */ }}

{{ } /* of expressions */ }}

{{ } /* of if */ }}

{{ } /* of callbacks */ }}

{{ data.operation = data.operationStack.pop(); }}
{{ data.method.operation = data.operation; }}

{{?}}
