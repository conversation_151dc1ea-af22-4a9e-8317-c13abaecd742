{{?data.allHeaders.length}}var headers = {
{{~data.allHeaders :p:index}}  '{{=p.name}}':{{=p.exampleValues.json}}{{?index < data.allHeaders.length-1}},{{?}}
{{~}}
};
{{?}}
$.ajax({
  url: '{{=data.url}}',
  method: '{{=data.method.verb}}',
{{?data.requiredQueryString}}  data: '{{=data.requiredQueryString}}',{{?}}
{{?data.allHeaders.length}}  headers: headers,{{?}}
  success: function(data) {
    console.log(JSON.stringify(data));
  }
})
