{{= data.tags.section }}

# Authentication

{{ for (var s in data.api.components.securitySchemes) { }}
{{ var sd = data.api.components.securitySchemes[s]; }}
{{? sd.type == 'apiKey' }}
* API Key ({{=s}})
    - Parameter Name: **{{=sd.name}}**, in: {{=sd.in}}. {{=sd.description || ''}}
{{?}}
{{? sd.type == 'http'}}
- HTTP Authentication, scheme: {{=sd.scheme}}{{? sd.description }}<br/>{{=sd.description}}{{?}}
{{?}}
{{? sd.type == 'oauth2'}}
- oAuth2 authentication. {{=sd.description || ''}}
{{ for (var f in sd.flows) { }}
{{ var flow = sd.flows[f]; }}
    - Flow: {{=f}}
{{? flow.authorizationUrl}}    - Authorization URL = [{{=flow.authorizationUrl}}]({{=flow.authorizationUrl}}){{?}}
{{? flow.tokenUrl}}    - Token URL = [{{=flow.tokenUrl}}]({{=flow.tokenUrl}}){{?}}
{{? flow.scopes && Object.keys(flow.scopes).length}}
|Scope|Scope Description|
|---|---|
{{ for (var sc in flow.scopes) { }}|{{=sc}}|{{=data.utils.join(flow.scopes[sc])}}|
{{ } /* of scopes */ }}
{{?}}
{{ } /* of flows */ }}
{{?}}
{{ } /* of securitySchemes */ }}

{{= data.tags.endSection }}
