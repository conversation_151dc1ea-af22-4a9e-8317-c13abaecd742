{{= data.tags.section }}
<H4 id="{{=data.operationUniqueSlug}}-parameters">Parameters</H4>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
{{~ data.parameters :p}}|{{=p.name}}|{{=p.in}}|{{=p.safeType}}|{{=p.required}}|{{=p.shortDesc || 'none'}}|
{{~}}

{{? data.longDescs }}
#### Detailed descriptions
{{~ data.parameters :p}}{{? p.shortDesc !== p.description}}
**{{=p.name}}**: {{=p.description}}{{?}}
{{~}}
{{?}}

{{~ data.parameters :p}}

{{? p.schema && p.schema.enum }}
{{~ p.schema.enum :e}}
{{ var entry = {}; entry.name = p.name; entry.value = e; data.enums.push(entry); }}
{{~}}
{{?}}

{{? p.schema && p.schema.items && p.schema.items.enum }}
{{~ p.schema.items.enum :e}}
{{ var entry = {}; entry.name = p.name; entry.value = e; data.enums.push(entry); }}
{{~}}
{{?}}

{{~}}

{{? data.enums && data.enums.length }}
#### Enumerated Values

|Parameter|Value|
|---|---|
{{~ data.enums :e}}|{{=e.name}}|{{=data.utils.toPrimitive(e.value)}}|
{{~}}
{{?}}
{{= data.tags.endSection }}
