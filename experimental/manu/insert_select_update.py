"""
Test to compare different locking/isolation configurations for a workload that
inserts new "versions" of a row by first selecting the latest existing version
of the row and then inserting a new row with an incremented version and value.
"""

import argparse
import multiprocessing
import random
import sys
import time

import psycopg2
from psycopg2.errors import SerializationFailure
from psycopg2.extras import RealDictCursor

TBLNAME = "_insert_select_update"

CREATE_TABLE_STMTS = [
    f"""
CREATE TABLE {TBLNAME} (
    id serial primary key,
    bucket_num int,
    version int,
    value int
)
""",
]

DROP_TABLE_STMT = f"""
DROP TABLE {TBLNAME}
"""


def insert_stmt(select_for_update, bucket_num, value_incr):
    return f"""
WITH
bucket_rows AS (
SELECT
    bucket_num, version, value
FROM
    {TBLNAME}
WHERE
    bucket_num = {bucket_num}
{'FOR UPDATE' if select_for_update else ''}
),
ordered_rows AS (
SELECT
    ROW_NUMBER() OVER (ORDER BY version DESC) row_number, bucket_num, version, value
FROM
    bucket_rows
),
latest_row AS (
SELECT
    bucket_num, version, value
FROM
    ordered_rows
WHERE
    row_number = 1
{'FOR UPDATE' if select_for_update else ''}
),
insert_data AS (
SELECT
    value
FROM
    UNNEST(ARRAY [{value_incr}] :: int[]) value
)
INSERT INTO {TBLNAME} (bucket_num, version, value)
SELECT
    {bucket_num}, COALESCE(latest.version + 1, 1) version, (new.value + COALESCE(latest.value, 0)) value
FROM
    insert_data new LEFT JOIN latest_row latest ON true
"""


NUM_BUCKETS = 10
INSERTS_PER_BUCKET = 1000


def spawn_inserter(insert_mode):
    select_for_update = insert_mode == "select_for_update"
    num_serialization_failures = 0

    insert_rows = [
        (bucket_num, value_incr)
        for bucket_num in range(NUM_BUCKETS)
        for value_incr in range(1, INSERTS_PER_BUCKET + 1)
    ]
    random.shuffle(insert_rows)

    def run_transaction(conn, cur, bucket_num, value_incr):
        if insert_mode == "serializable":
            cur.execute("set transaction isolation level serializable")
        else:
            cur.execute("set transaction isolation level read committed")
        stmt = insert_stmt(select_for_update=select_for_update, bucket_num=bucket_num, value_incr=value_incr)
        cur.execute(stmt)
        conn.commit()

    with psycopg2.connect("dbname=postgres") as conn:
        with conn.cursor() as cur:
            for bucket_num, value_incr in insert_rows:
                if insert_mode == "serializable":
                    while True:
                        try:
                            run_transaction(conn, cur, bucket_num, value_incr)
                        except SerializationFailure as e:
                            num_serialization_failures += 1
                            conn.rollback()
                else:
                    if insert_mode == "advisory_locking":
                        cur.execute(f"SELECT pg_advisory_xact_lock({bucket_num})")
                    run_transaction(conn, cur, bucket_num, value_incr)
    if num_serialization_failures:
        print("num_serialization_failures =", num_serialization_failures)


def setup_db():
    with psycopg2.connect("dbname=postgres") as conn:
        with conn.cursor() as cur:
            for stmt in CREATE_TABLE_STMTS:
                cur.execute(stmt)


def teardown_db():
    with psycopg2.connect("dbname=postgres") as conn:
        with conn.cursor() as cur:
            cur.execute(DROP_TABLE_STMT)


def run_experiment(insert_mode, num_procs):
    setup_db()
    try:
        procs = []
        for _ in range(num_procs):
            procs.append(multiprocessing.Process(target=spawn_inserter, args=(insert_mode,)))
        start_time = time.time()
        for p in procs:
            p.start()
        for p in procs:
            p.join()
        end_time = time.time()

        # Fetch the latest row version for each bucket. Pick out the row with
        # the smallest version amongst this set.
        with psycopg2.connect("dbname=postgres") as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(
                    f"""
WITH
ordered_rows AS (
SELECT
    ROW_NUMBER() OVER (PARTITION BY bucket_num ORDER BY version DESC) row_number, bucket_num, version, value
FROM
    {TBLNAME}
),
latest_row_per_bucket AS (
SELECT
    bucket_num, version, value
FROM
    ordered_rows
WHERE
    row_number = 1
)
SELECT
    bucket_num, version, value
FROM
    latest_row_per_bucket
ORDER BY
    version DESC,
    bucket_num ASC
LIMIT 1
"""
                )
                row = cur.fetchone()
        return dict(lowest_final_version_row=dict(row), delta_t=end_time - start_time)
    finally:
        teardown_db()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog="index_scaling_benchmark")
    parser.add_argument(
        "--insert_mode",
        required=True,
        choices=["no_locking", "select_for_update", "advisory_locking", "serializable"],
        help="The mode to run inserts in",
    )
    parser.add_argument("--num_procs", required=True, type=int, help="Number of processes")
    args = parser.parse_args()

    print(
        f"""Experiment details:
Insert mode: {args.insert_mode}
Num procs: {args.num_procs}""",
        file=sys.stderr,
    )
    result = run_experiment(args.insert_mode, args.num_procs)
    print(result)
