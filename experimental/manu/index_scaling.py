"""
Benchmark to compare insert throughput with a nonunique index to upsert
throughput with a unique index, with a workload tuned for very few unique key
conflicts. Measures scaling by number of concurrent connecting processes.

Run by manu on 09/14/2023:

$ python index_scaling.py --num_procs 1 2 4 8 --num_reps 3
Results (form [(num_procs, avg_throughput)]): [(1, 43458.6354565954), (2, 68092.15513241566), (4, 97561.235539146), (8, 110604.30729653477)]

$ python index_scaling.py --unique --num_procs 1 2 4 8 --num_reps 3
Results (form [(num_procs, avg_throughput)]): [(1, 37767.697532908314), (2, 57297.60104112431), (4, 81882.50142344946), (8, 97129.5770197562)]
"""

import argparse
import multiprocessing
import random
import sys
import time

import psycopg2

TBLNAME = "_index_scaling"

CREATE_TABLE_STMT = f"""
CREATE TABLE {TBLNAME} (
    id serial primary key,
    a int,
    other int
)
"""

DROP_TABLE_STMT = f"""
DROP TABLE {TBLNAME}
"""

CREATE_INDEX_STMT = dict(
    unique=f"CREATE UNIQUE INDEX ON {TBLNAME} (a)",
    nonunique=f"CREATE INDEX ON {TBLNAME} (a)",
)

INSERT_STMT = dict(
    unique=f"""
INSERT
    INTO {TBLNAME} (a, other)
VALUES
    (%s, %s)
ON CONFLICT (a) DO UPDATE
    SET other = {TBLNAME}.other + EXCLUDED.other
""",
    nonunique=f"""
INSERT
    INTO {TBLNAME} (a, other)
VALUES
    (%s, %s)
""",
)


def make_randint():
    return random.randint(0, int(2**30))


NUM_BATCHES = 1000
BATCH_SIZE = 100
NUM_INSERTS = NUM_BATCHES * BATCH_SIZE


def spawn_inserter(index_type):
    with psycopg2.connect("dbname=postgres") as conn:
        with conn.cursor() as cur:
            for _ in range(NUM_BATCHES):
                insert_values = [(make_randint(), make_randint()) for _ in range(BATCH_SIZE)]
                insert_values.sort(key=lambda r: r[0])
                for params in insert_values:
                    cur.execute(INSERT_STMT[index_type], params)
                conn.commit()


def setup_db(index_type):
    with psycopg2.connect("dbname=postgres") as conn:
        with conn.cursor() as cur:
            cur.execute(CREATE_TABLE_STMT)
            cur.execute(CREATE_INDEX_STMT[index_type])


def teardown_db(index_type):
    with psycopg2.connect("dbname=postgres") as conn:
        with conn.cursor() as cur:
            cur.execute(DROP_TABLE_STMT)


def run_experiment_rep(index_type, num_procs):
    setup_db(index_type)
    try:
        procs = []
        for _ in range(num_procs):
            procs.append(multiprocessing.Process(target=spawn_inserter, args=(index_type,)))
        start_time = time.time()
        for p in procs:
            p.start()
        for p in procs:
            p.join()
        end_time = time.time()
        return NUM_INSERTS * num_procs / (end_time - start_time)
    finally:
        teardown_db(index_type)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog="index_scaling_benchmark")
    parser.add_argument("--unique", action="store_true", help="If true, benchmark a unique index")
    parser.add_argument(
        "--num_procs",
        required=True,
        nargs="+",
        type=int,
        help="Number of processes. Specify multiple to run the experiment with different numbers of processes.",
    )
    parser.add_argument(
        "--num_reps",
        default=1,
        type=int,
        help="Repeat the experiment this many times and report the average throughput",
    )
    args = parser.parse_args()

    index_type = "unique" if args.unique else "nonunique"
    print(
        f"""Experiment details:
Index type: {index_type}
Each process will run {NUM_BATCHES} transactions each with {BATCH_SIZE} inserts (totaling {NUM_INSERTS} inserts)""",
        file=sys.stderr,
    )

    results = []
    for num_procs in args.num_procs:
        throughputs = []
        for rep in range(args.num_reps):
            print(f"Running rep {rep} of experiment with {num_procs} processes", file=sys.stderr)
            throughputs.append(run_experiment_rep(index_type, num_procs))
        results.append((num_procs, sum(throughputs) / len(throughputs)))

    print(f"Results (form [(num_procs, avg_throughput)]): {results}")
