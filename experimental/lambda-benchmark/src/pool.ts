import dotenv from "dotenv";
import fs from "fs";
import archiver from "archiver";
import {
  Lambda,
  CreateFunctionCommand,
  CreateFunctionCommandInput,
  CreateFunctionCommandOutput,
  DeleteFunctionCommandInput,
  DeleteFunctionCommand,
} from "@aws-sdk/client-lambda";
import { AssumeRoleCommand, STSClient } from "@aws-sdk/client-sts";
import path from "path";
import { v4 as uuidv4 } from "uuid";

if (fs.existsSync(".env.local")) {
  dotenv.config({ path: ".env.local" });
}
if (fs.existsSync(".env.development")) {
  dotenv.config({ path: ".env.development" });
}

const ACCESS_KEY_ID = process.env.QUARANTINE_USER_ACCESS_KEY!;
const SECRET_ACCESS_KEY = process.env.QUARANTINE_USER_SECRET_ACCESS_KEY!;

if (!ACCESS_KEY_ID || !SECRET_ACCESS_KEY) {
  throw new Error(
    "QUARANTINE_USER_ACCESS_KEY and QUARANTINE_USER_SECRET_ACCESS_KEY must be set",
  );
}

const API_HANDLER_ROLE_ARN = process.env.API_HANDLER_ROLE_ARN!;
const QUARANTINE_INVOKE_ROLE_ARN = process.env.QUARANTINE_INVOKE_ROLE_ARN!;
const QUARANTINE_FUNCTION_ROLE_ARN = process.env.QUARANTINE_FUNCTION_ROLE_ARN!;
const QUARANTINE_PRIVATE_SUBNET_1_ID =
  process.env.QUARANTINE_PRIVATE_SUBNET_1_ID!;
const QUARANTINE_PRIVATE_SUBNET_2_ID =
  process.env.QUARANTINE_PRIVATE_SUBNET_2_ID!;
const QUARANTINE_PRIVATE_SUBNET_3_ID =
  process.env.QUARANTINE_PRIVATE_SUBNET_3_ID!;
const QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP =
  process.env.QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP!;
const QUARANTINE_PUB_PRIVATE_VPC_ID =
  process.env.QUARANTINE_PUB_PRIVATE_VPC_ID!;

if (
  !API_HANDLER_ROLE_ARN ||
  !QUARANTINE_FUNCTION_ROLE_ARN ||
  !QUARANTINE_INVOKE_ROLE_ARN ||
  !QUARANTINE_PRIVATE_SUBNET_1_ID ||
  !QUARANTINE_PRIVATE_SUBNET_2_ID ||
  !QUARANTINE_PRIVATE_SUBNET_3_ID ||
  !QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP ||
  !QUARANTINE_PUB_PRIVATE_VPC_ID
) {
  throw new Error("One or more required environment variables are missing");
}

const payload = {
  userCode: `
    export async function handler({input, output, metadata}) {
        return {
            score: 0.5,
            metadata: {
                input,
                output,
                metadata,
            },
        };
    }
`,
  arg: {
    input: "test",
    output: "test-output",
    metadata: "test-metadata",
  },
};

async function main() {
  const sts = new STSClient({
    credentials: {
      accessKeyId: ACCESS_KEY_ID,
      secretAccessKey: SECRET_ACCESS_KEY,
    },
    region: "us-east-1", // Adjust this to your region if different
  });

  const assumeRoleCommand = new AssumeRoleCommand({
    RoleArn: API_HANDLER_ROLE_ARN,
    RoleSessionName: "APIHandlerSession",
    DurationSeconds: 3600, // 1 hour
  });

  const assumeRoleResponse = await sts.send(assumeRoleCommand);
  const tempCredentials = assumeRoleResponse.Credentials;

  let lambda: Lambda;
  try {
    if (!tempCredentials) {
      throw new Error("Failed to obtain temporary credentials");
    }
    if (
      !tempCredentials.AccessKeyId ||
      !tempCredentials.SecretAccessKey ||
      !tempCredentials.SessionToken
    ) {
      throw new Error("Failed to obtain temporary credentials");
    }

    // Update Lambda client with temporary credentials
    lambda = new Lambda({
      credentials: {
        accessKeyId: tempCredentials.AccessKeyId,
        secretAccessKey: tempCredentials.SecretAccessKey,
        sessionToken: tempCredentials.SessionToken,
      },
      region: "us-east-1",
    });

    console.log("Successfully assumed API Handler Role");
  } catch (error) {
    console.error("Error assuming API Handler Role:", error);
    throw error;
  }

  const zipPath = path.join("dist", "code.zip");
  const zipStream = fs.createWriteStream(zipPath);
  const archive = archiver("zip", {
    zlib: { level: 9 },
  });
  archive.pipe(zipStream);
  archive.file("dist/inline-runner.js", { name: "index.js" });
  await archive.finalize();

  const start = Date.now();
  await new Promise((resolve, reject) => {
    zipStream.on("error", reject);
    zipStream.on("close", resolve);
  });
  console.log(`Zipped bundle in ${Date.now() - start}ms`);

  const params: CreateFunctionCommandInput = {
    FunctionName: `QuarantineNodejs20Function-${uuidv4()}`,
    Runtime: "nodejs20.x",
    Role: QUARANTINE_FUNCTION_ROLE_ARN,
    Handler: "index.handler",
    Code: {
      ZipFile: await fs.promises.readFile(zipPath),
    },
    MemorySize: 1024,
    Timeout: 900,
    VpcConfig: {
      SecurityGroupIds: [QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP],
      SubnetIds: [
        QUARANTINE_PRIVATE_SUBNET_1_ID,
        QUARANTINE_PRIVATE_SUBNET_2_ID,
        QUARANTINE_PRIVATE_SUBNET_3_ID,
      ],
    },
    TracingConfig: {
      Mode: "Active",
    },
    Tags: {
      BraintrustQuarantine: "true",
      BraintrustVersion: "1",
      IsQuarantine: "true",
    },
  };

  let result: CreateFunctionCommandOutput | undefined;
  try {
    const start = Date.now();
    const command = new CreateFunctionCommand(params);
    result = await lambda.send(command);
    console.log(
      "Lambda function created successfully:",
      result.FunctionArn,
      "in",
      Date.now() - start,
      "ms",
    );
  } catch (error) {
    console.error("Error creating Lambda function:", error);
  }

  if (!result) {
    return;
  }

  // Wait for the function to be ready
  const startStatus = Date.now();
  let status = await lambda.getFunctionConfiguration({
    FunctionName: result.FunctionArn,
  });
  while (status.State !== "Active") {
    await new Promise((resolve) => setTimeout(resolve, 50));
    status = await lambda.getFunctionConfiguration({
      FunctionName: result.FunctionArn,
    });
  }
  console.log("Lambda function ready in", Date.now() - startStatus, "ms");

  // Now try invoking it
  // Assume the QUARANTINE_FUNCTION_ROLE_ARN has the necessary permissions to invoke the function

  const invokeSts = new STSClient({
    credentials: {
      accessKeyId: tempCredentials.AccessKeyId,
      secretAccessKey: tempCredentials.SecretAccessKey,
      sessionToken: tempCredentials.SessionToken,
    },
    region: "us-east-1", // Adjust this to your region if different
  });
  const assumeInvokeRoleCommand = new AssumeRoleCommand({
    RoleArn: QUARANTINE_INVOKE_ROLE_ARN,
    RoleSessionName: "QuarantineInvokeRoleSession",
    DurationSeconds: 3600, // 1 hour
  });

  const invokeTempCredentials = (await invokeSts.send(assumeInvokeRoleCommand))
    .Credentials;

  try {
    if (!invokeTempCredentials) {
      throw new Error("Failed to obtain temporary credentials");
    }
    if (
      !invokeTempCredentials.AccessKeyId ||
      !invokeTempCredentials.SecretAccessKey ||
      !invokeTempCredentials.SessionToken
    ) {
      throw new Error("Failed to obtain temporary credentials");
    }
    const invokeLambda = new Lambda({
      credentials: {
        accessKeyId: invokeTempCredentials.AccessKeyId,
        secretAccessKey: invokeTempCredentials.SecretAccessKey,
        sessionToken: invokeTempCredentials.SessionToken,
      },
      region: "us-east-1",
    });

    for (let i = 0; i < 10; i++) {
      const start = Date.now();
      const response = await invokeLambda.invokeWithResponseStream({
        FunctionName: result.FunctionArn,
        Payload: JSON.stringify(payload),
      });
      if (!response.EventStream) {
        throw new Error("No event stream in response.");
      }
      console.log(
        "Lambda function invoked successfully in",
        Date.now() - start,
        "ms",
      );

      const readStart = new Date();
      for await (const event of response.EventStream) {
        if (!event.PayloadChunk) {
          console.log("No payload chunk in event.");
        } else {
          console.log(
            " > ",
            new TextDecoder().decode(event.PayloadChunk.Payload),
          );
        }
      }
      console.log(
        "Read took " + (new Date().getTime() - readStart.getTime()) + "ms\n",
      );
    }
  } catch (error) {
    console.error("Error invoking Lambda function:", error);
  }

  const deleteParams: DeleteFunctionCommandInput = {
    FunctionName: result.FunctionArn,
  };

  try {
    const start = Date.now();
    await lambda.send(new DeleteFunctionCommand(deleteParams));
    console.log(
      "Lambda function deleted successfully:",
      "in",
      Date.now() - start,
      "ms",
    );
  } catch (error) {
    console.error("Error deleting Lambda function:", error);
  }
}

main();
