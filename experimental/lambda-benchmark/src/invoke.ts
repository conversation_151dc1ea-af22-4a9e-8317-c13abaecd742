import { Lambda } from "@aws-sdk/client-lambda";

async function main() {
  // Accepts a single argument which is a JSON dictionary
  // const args = JSON.parse(process.argv.slice(2).join(" "));

  const lambda = new Lambda({
    credentials: {
      accessKeyId: "********************",
      secretAccessKey: "yz8x3PkUDQjVKNMmiOsZ++kcLXwsDOWcoy6czLe3",
      sessionToken:
        "IQoJb3JpZ2luX2VjEIL//////////wEaCXVzLWVhc3QtMSJHMEUCIQC/khuELq9Ha65t8GkWAovj8iTKOFsOkjtnVLGdHB8pmQIgOYlKq6gg/Uv0B9TascUMC1Xm68Xo0XGQ2dip7zviyYgqnQMIGxAAGgw4NzI2MDgxOTU0ODEiDDtnnJY0udM9Y2L2oCr6ApP/QbR3bc5jleEefSSiOHDR0pBR88ABxzVzSqh2rgJ/MFfmP+I0Zl4jjzkznn71p/aAnsc8fO1YkdHUcWMNQfrd/n7pGi4ZBi7qxA5TYd0TMG9zAPVbvz+O8lT6e9XsGdCol7+IdokLP4cWGNwjJZ7yqK33pxMLsulOTbbEqxj0Me/JVBs/JLqiYoZVM+/TQPJL6ip1bmenXXKhUEgUfUCqtHvxfqJ6s+YQRG6Gix7A9CTYxXo+G8DEkTAzFzsYeyvjAxNI4QW2hbWfB5d9qvco3n6241YOpWJQUFM5sYV62KiWhVhCH+f9/t6GyeAAiMGeCCEer7CaTMBYABaQwTGNp3FK2wDHipaCCM85FTi5Ybl1J5RfORUW4TKZ+BPSprvCQhvk4caOU7Qk+LxOmKhWaGQIR4yQO8B2EqW/caXNb++JwkjSZ55LXNirQalQqNX7efTJ5fnngWat0Fw7VTTwUR7J35j8c5Unudf3bFd65X60q5hBM3oJRzD4uJKzBjqdAeL2MgDCBZd9eaAI4W5LonwiVHKIZ1VDx+LNbRF8qKD/ETkWapi1kcou2IhjiJVM+taKPUWMzIP130QmoelC5WiwpFx/Bi9+4/ozFGo+ipSSYxmNx5fBaD5yqHp3X7IwjvEHIItAdRI6/eUntCpD48Tf0T38svz+1bBulfMPjJ5ksoedYC+D3K0kgZ8ohsad2uhmrtPUzgu0XjQpjTo=",
    },
  });

  try {
    const invokeStart = new Date();
    console.log(`Invoking Lambda...`);
    const request = await lambda.invokeWithResponseStream({
      FunctionName:
        "arn:aws:lambda:us-east-1:872608195481:function:quarantine-test-QuarantineNodejs20Function-qCbKDYuBYu0K:20",
      Payload: JSON.stringify({
        arg: "FOO BAR Bing BAZ",
      }),
    });
    if (!request.EventStream) {
      throw new Error("No event stream in response.");
    }
    console.log(
      "Invoke took " + (new Date().getTime() - invokeStart.getTime()) + "ms\n",
    );

    const readStart = new Date();
    for await (const event of request.EventStream) {
      if (!event.PayloadChunk) {
        console.log("No payload chunk in event.");
      } else {
        console.log(new TextDecoder().decode(event.PayloadChunk.Payload));
      }
    }
    console.log(
      "Read took " + (new Date().getTime() - readStart.getTime()) + "ms\n",
    );
  } catch (error) {
    console.error("Error invoking Lambda:", error);
  }
}

main();
