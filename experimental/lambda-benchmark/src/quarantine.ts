import { APIGatewayProxyEventV2 } from "aws-lambda";
import { streamifyResponse, ResponseStream } from "lambda-stream";

export async function handler(
  event: APIGatewayProxyEventV2,
  responseStream: ResponseStream,
): Promise<void> {
  console.log("<PERSON><PERSON> got event:", event);
  responseStream.setContentType("text/plain");
  for (let i = 0; i < 1; i++) {
    responseStream.write("Hello, world!\n");
    // await new Promise((resolve) => setTimeout(resolve, 100));
  }
  responseStream.end();
}

streamifyResponse(handler);
