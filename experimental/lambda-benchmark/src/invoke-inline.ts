import { Lambda } from "@aws-sdk/client-lambda";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
async function invokeLambda(functionArn: string, payload: any) {
  const lambda = new Lambda({
    region: "us-east-1",
    // Assuming credentials are set in the environment or through AWS CLI
  });

  try {
    const start = Date.now();
    const response = await lambda.invokeWithResponseStream({
      FunctionName: functionArn,
      Payload: JSON.stringify(payload),
    });

    if (!response.EventStream) {
      throw new Error("No event stream in response.");
    }

    console.log(
      "Lambda function invoked successfully in",
      Date.now() - start,
      "ms",
    );

    const readStart = new Date();
    for await (const event of response.EventStream) {
      if (!event.PayloadChunk) {
        console.log("No payload chunk in event.");
      } else {
        console.log(new TextDecoder().decode(event.PayloadChunk.Payload));
      }
    }
    console.log(
      "Read took " + (new Date().getTime() - readStart.getTime()) + "ms\n",
    );
  } catch (error) {
    console.error("Error invoking Lambda function:", error);
  }
}

// Example usage
const functionArn =
  "arn:aws:lambda:us-east-1:872608195481:function:QuarantineNodejs20Function-ba2735fa-b1a5-4883-a526-40b6a27d41a0";
const payload = {
  userCode: `
    export async function handler({input, output, metadata}) {
        return {
            score: 0.5,
            metadata: {
                input,
                output,
                metadata,
            },
        };
    }
`,
  arg: {
    input: "test",
    output: "test-output",
    metadata: "test-metadata",
  },
};

invokeLambda(functionArn, payload);
