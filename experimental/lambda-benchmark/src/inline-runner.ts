import { streamifyResponse, ResponseStream } from "lambda-stream";
import { z } from "zod";
import vm from "vm";

const eventFormat = z.object({
  userCode: z.string(),
  arg: z.any(),
});

function doImport(str: string) {
  const dataUrl = `data:text/javascript;base64,${Buffer.from(str).toString("base64")}`;
  return import(dataUrl);
}

export async function handler(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  eventRaw: any,
  responseStream: ResponseStream,
): Promise<void> {
  responseStream.write("starting\n");
  responseStream.write(`${vm.SourceTextModule}\n`);
  const event = eventFormat.safeParse(eventRaw);

  if (!event.success) {
    responseStream.write(JSON.stringify(event.error));
    responseStream.end();
    return;
  }

  let start = Date.now();
  const module = await doImport(event.data.userCode);
  let end = Date.now();
  responseStream.write(`imported in ${end - start}ms\n`);

  const exportedFn = module.handler;
  console.log(exportedFn);

  start = Date.now();
  const result = await exportedFn(event.data.arg);
  end = Date.now();
  responseStream.write(`ran in ${end - start}ms\n`);
  console.log(result);

  responseStream.write(JSON.stringify(result));
  responseStream.end();
}

streamifyResponse(handler);
