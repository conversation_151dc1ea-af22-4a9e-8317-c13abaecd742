import { APIGatewayProxyEventV2 } from "aws-lambda";
import { initializeEnv } from "./env";
import { streamifyResponse, ResponseStream } from "lambda-stream";
import { Lambda } from "@aws-sdk/client-lambda";
import { STSClient, AssumeRoleCommand } from "@aws-sdk/client-sts";
import { v4 as uuidv4 } from "uuid";

initializeEnv(false);

export const REGION = "us-west-1";

// Initialize the AWS Lambda client
const lambda = new Lambda({
  region: REGION,
});

const sts = new STSClient({
  region: REGION,
});

export const functionName =
  "arn:aws:lambda:us-west-1:************:function:Quarantine";

export async function handler(
  event: APIGatewayProxyEventV2,
  responseStream: ResponseStream,
): Promise<void> {
  responseStream.setContentType("text/plain");

  const qs = event.rawQueryString;
  const parsed = new URLSearchParams(qs);
  let version = parsed.get("version");
  const publish = parsed.get("publish");
  if (version && publish) {
    responseStream.write("Cannot specify both version and publish.\n");
    responseStream.end();
    return;
  }
  const start = new Date();

  if (publish) {
    const publishStart = new Date();

    await lambda.updateFunctionConfiguration({
      FunctionName: functionName,
      Environment: {
        Variables: {
          HASH: uuidv4(),
        },
      },
    });
    responseStream.write(
      "Updated function configuration in " +
        (new Date().getTime() - publishStart.getTime()) +
        "ms\n",
    );

    for (let i = 0; i < 100; i++) {
      try {
        const published = await lambda.publishVersion({
          FunctionName: functionName,
        });
        if (!published.Version) {
          responseStream.write("No version in response.\n");
          throw new Error("No version in response.");
        }
        responseStream.write(
          `Published version ${published.Version} in ` +
            (new Date().getTime() - publishStart.getTime()) +
            "ms\n",
        );
        version = published.Version;

        break;
      } catch (error) {
        responseStream.write("Error publishing version: " + error + "\n");
        console.error("Error publishing version:", error);
        await new Promise((resolve) => setTimeout(resolve, 50));
      }
    }

    if (!version) {
      responseStream.write("Failed to publish version.\n");
      responseStream.end();
      return;
    }

    const aliasStart = Date.now();
    const aliasName = uuidv4();
    const alias = await lambda.createAlias({
      Name: aliasName,
      FunctionName: functionName,
      FunctionVersion: version,
    });

    if (!alias.Name) {
      responseStream.write("No alias name in response.\n");
      throw new Error("No alias name in response.");
    }
    responseStream.write(
      `Created alias ${alias.Name} in ` +
        (new Date().getTime() - aliasStart) +
        "ms\n",
    );

    const command = new AssumeRoleCommand({
      RoleArn: "arn:aws:iam::************:role/InvokeQuarantine",
      RoleSessionName: "QuarantineSession",
      DurationSeconds: 900,
    });

    try {
      const response = await sts.send(command);
      console.log(response);
      responseStream.write(
        `Assumed role: ${JSON.stringify(response.Credentials)}\n`,
      );
    } catch (error) {
      console.error("Error assuming role:", error);
      responseStream.write("Error assuming role: " + error + "\n");
    }

    // This does not quite work
    // const functionStart = Date.now();
    // const functionUrl = await lambda.createFunctionUrlConfig({
    //   FunctionName: functionName,
    //   Qualifier: alias.Name,
    //   InvokeMode: "RESPONSE_STREAM",
    //   AuthType: "AWS_IAM",
    // });
    // const functionUrlString = functionUrl.FunctionUrl;
    // if (!functionUrlString) {
    //   responseStream.write("No function URL in response.\n");
    //   throw new Error("No function URL in response.");
    // }
    // responseStream.write(
    //   `Created function URL ${functionUrlString} in ` +
    //     (new Date().getTime() - functionStart) +
    //     "ms\n",
    // );
  }

  try {
    const invokeStart = new Date();
    responseStream.write(`Invoking Lambda... (version ${version})\n`);
    const request = await lambda.invokeWithResponseStream({
      FunctionName:
        "arn:aws:lambda:us-west-1:************:function:Quarantine" +
        (version ? ":" + version : ""),
    });
    if (!request.EventStream) {
      throw new Error("No event stream in response.");
    }
    responseStream.write(
      "Invoke took " + (new Date().getTime() - invokeStart.getTime()) + "ms\n",
    );
    console.log(request);

    for await (const event of request.EventStream) {
      console.log("Received event:", event);
      if (!event.PayloadChunk) {
        console.log("No payload chunk in event.");
      } else {
        responseStream.write(
          new TextDecoder().decode(event.PayloadChunk.Payload),
        );
      }
    }
  } catch (error) {
    responseStream.write("Error invoking Lambda: " + error + "\n");
    console.error("Error invoking Lambda:", error);
  }

  responseStream.write(
    "Whole thing took " + (new Date().getTime() - start.getTime()) + "ms\n",
  );
  responseStream.end();
}

streamifyResponse(handler);
