import { STSClient, AssumeRoleCommand } from "@aws-sdk/client-sts";
import { REGION } from ".";

const sts = new STSClient({
  region: REGION,
});

// XXX To make this work, we need to use non-root IAM credentials.
async function main() {
  const command = new AssumeRoleCommand({
    RoleArn: "arn:aws:iam::872608195481:role/InvokeQuarantine",
    RoleSessionName: "QuarantineSession",
    DurationSeconds: 900,
  });
  try {
    const response = await sts.send(command);
    console.log(response);
  } catch (error) {
    console.error("Error assuming role:", error);
  }
}

main();
