import { exit } from "process";
import * as vm from "vm";
import { transform } from "sucrase";

const payload = {
  userCode: `
function handler(arg) {
    console.log(arg);
    return {
      score: 0.5,
      metadata; arg,
    };
}
`,
  arg: {
    input: "test",
    output: "test-output",
    metadata: "test-metadata",
  },
};

async function main() {
  const { userCode, arg } = payload;

  const vmCode = `
  ${userCode}

  handler
  `;

  let transpiledCode;
  try {
    transpiledCode = transform(vmCode, {
      transforms: ["typescript", "imports"],
    }).code;
  } catch (e) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    console.log(vmErrorToString(e as VMError));
    exit(1);
  }

  const context = vm.createContext({ Buffer }); // Contextify the object.
  let fn;
  try {
    fn = await vm.runInContext(transpiledCode, context);
  } catch (e) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    console.log(vmErrorToString(e as VMError));
    // console.log(typeof e);
    // throw e;
    exit(1);
  }
  console.log(fn);

  const result = await fn(arg);
  console.log(result);

  // Call the handler function with the provided argument
  //   const result = await handler.handler(arg);
  //   console.log(result);
}

main().catch(console.error);

interface VMError {
  stack: string;
}
function vmErrorToString(error: VMError): string {
  const lines = error.stack.split("\n");
  // Find the first line that starts with "\s*at"
  const atLine = lines.findIndex((line) => /^\s*at/.test(line));
  return lines.slice(0, atLine).join("\n").trim();
}
