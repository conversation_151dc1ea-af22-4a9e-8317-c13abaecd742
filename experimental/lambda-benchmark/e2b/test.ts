import { CodeInterpreter } from "@e2b/code-interpreter";

async function main() {
  let start = new Date();
  const sandbox = await CodeInterpreter.create();
  console.log(
    "Sandbox created in",
    new Date().getTime() - start.getTime(),
    "ms",
  );
  start = new Date();
  await sandbox.notebook.execCell("x = 1");
  console.log("Executed cell in", new Date().getTime() - start.getTime(), "ms");

  start = new Date();
  const execution = await sandbox.notebook.execCell("x+=1; x");
  console.log(execution.text); // outputs 2
  console.log("Executed cell in", new Date().getTime() - start.getTime(), "ms");

  start = new Date();
  await sandbox.close();
  console.log(
    "Sandbox closed in",
    new Date().getTime() - start.getTime(),
    "ms",
  );
}

main();
