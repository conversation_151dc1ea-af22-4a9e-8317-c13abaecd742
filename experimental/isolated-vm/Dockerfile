# docker build -t isolated-vm-builder .
# docker run --rm -v $(pwd):/output isolated-vm-builder sh -c "cp /app/dist/index.zip /output/"

# Build stage
FROM node:20-alpine AS builder

RUN apk add --no-cache python3 make g++ zip

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package.json package-lock.json* ./

# Install pnpm
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install

# Copy the src directory
COPY src ./src

# Copy any other necessary files for the build (e.g., build scripts, tsconfig.json)
COPY build.ts tsconfig.json ./

# Build the application
RUN pnpm run build

# Final stage
FROM alpine:latest

# Install bash
RUN apk add --no-cache bash

# Set the working directory in the container
WORKDIR /app

# Copy the built index.zip from the builder stage to the root of the container
COPY --from=builder /app/dist/index.zip /index.zip

# Set bash as the default shell
SHELL ["/bin/bash", "-c"]

# Command to run when the container starts
CMD ["/bin/bash"]
