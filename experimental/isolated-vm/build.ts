import esbuild from "esbuild";
import { nodeExternals } from "esbuild-plugin-node-externals";

esbuild
  .build({
    entryPoints: ["src/index.ts"],
    bundle: true,
    outfile: "dist/index.js",
    platform: "node",
    target: "es2020",
    minify: true,
    sourcemap: true,
    external: ["@aws-sdk/*"],
    plugins: [
      nodeExternals({
        include: ["isolated-vm"],
        packagePaths: ["./package.json"],
        // Force npm to use x64 architecture
        // npmOptions: {
        //   env: {
        //     ...process.env,
        //     npm_config_target_arch: "x64",
        //     npm_config_target_platform: "linux",
        //   },
        // },
      }),
    ],
    loader: {
      ".html": "text",
    },
  })
  .catch(() => process.exit(1));
