import { APIGatewayProxyEventV2 } from "aws-lambda";
import { streamifyResponse, ResponseStream } from "lambda-stream";
import * as esbuild from "esbuild";
import vm from "vm";

export async function handler(
  event: APIGatewayProxyEventV2,
  responseStream: ResponseStream,
): Promise<void> {
  responseStream.setContentType("text/plain");

  const start = new Date();

  const result = await esbuild.transform('console.log("Hello, world!");', {
    loader: "ts",
    target: "es2020",
  });
  const transpiledCode = result.code;
  const vmContext = vm.createContext();

  let fn;
  try {
    fn = await vm.runInContext(transpiledCode, vmContext);
  } catch (e) {
    // ... existing error handling ...
  }
  console.log(fn);

  responseStream.write(`RESULT ${result}\n`);
  responseStream.write(
    "Whole thing took " + (new Date().getTime() - start.getTime()) + "ms\n",
  );
  responseStream.end();
}

streamifyResponse(handler);
