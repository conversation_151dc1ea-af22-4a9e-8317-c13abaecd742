import ivm from "isolated-vm";
const isolate = new ivm.Isolate({ memoryLimit: 128 });

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export async function runIsolate(input: any): Promise<any> {
  const context = isolate.createContextSync();
  const jail = context.global;

  jail.setSync("global", jail.derefInto());

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  jail.setSync("log", function (...args: any[]) {
    console.log(...args);
  });

  // Set the input data in the context
  jail.setSync("input", input);

  // Evaluate a script that uses the input and returns a result
  const result = await context.eval(`
    // Your script here, using the 'input' variable
    // For example:
    const output = input * 2;
    output; // The last expression is the return value
  `);

  return result;
}
