{"name": "@braintrust/isolated-vm-experiments", "version": "1.0.0", "description": "", "main": "./dist/index.js", "binary": true, "scripts": {"build": "run-p build:*", "build:typecheck": "tsc --noEmit", "build:lambda": "esbuild --platform=node --external:esbuild --bundle src/index.ts --outfile=dist/index.js --minify --sourcemap --target=es2020", "postbuild": "mkdir -p dist/node_modules && cp -r -L node_modules/esbuild dist/node_modules/ && cd dist && zip -r index.zip index.js node_modules", "package": "docker build -t isolated-vm-builder .", "deploy": "run-s package deploy:*", "deploy:benchmark": "AWS_DEFAULT_REGION=us-west-1 aws lambda update-function-code --function-name arn:aws:lambda:us-west-1:872608195481:function:ResponseStreamingBenchmark --zip-file fileb://$PWD/dist/index.zip", "test": "time curl -i -N 'https://lrligvyxjjdh4udplxz6q6y7we0tvylk.lambda-url.us-west-1.on.aws/'"}, "author": "", "license": "ISC", "dependencies": {"aws-lambda": "^1.0.7", "isolated-vm": "^5.0.1", "lambda-stream": "^0.5.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.130", "@types/node": "^20.5.0", "@typescript-eslint/eslint-plugin": "^8.11.0", "esbuild": "^0.19.11", "esbuild-plugin-node-externals": "^1.0.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "npm-run-all": "^4.1.5", "typescript": "^5.0.4"}}