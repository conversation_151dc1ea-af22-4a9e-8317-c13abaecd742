{"name": "@braintrust/typespecs", "version": "0.0.1", "description": "Typespecs for Braintrust", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup --clean --dts", "watch": "tsup --watch --dts", "clean": "rm -r dist/*"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist/**/*"], "devDependencies": {"tsup": "^8.4.0", "tsx": "^3.14.0", "typescript": "^5.3.3"}, "dependencies": {"@asteasolutions/zod-to-openapi": "^6.3.1", "braintrust": "workspace:*", "zod": "^3.22.4"}}