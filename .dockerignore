# Ignore all directories except a whitelist of those we copy data from.
*
!api-schema
!api-ts
!app
!app-bootstrap
!autoevals
!brainstore
!btql
!forked/codemirror-copilot
!local/js
!openapi
!otel
!proxy/packages/proxy
!realtime
!scripts
!sdk
!typespecs

!package.json
!pnpm-workspace.yaml
!pnpm-lock.yaml
!turbo.json
!.npmrc
!.eslintrc.cjs
!VERSION

# Exclusions specific to .dockerignore (mainly just possibly-large files we
# don't need in the build context). These should be excluded from a fresh repo
# checkout by the `.gitignores`, but they may end up in the local filesystem.
.tar.gz
app/.next

# START root .gitignore

*.pyc
*.swp
*.swo
data
services/data
venv
.env
!services/.env
.direnv
.DS_STORE
.idea
node_modules
*.egg-info
.turbo
dist/
.stestr
.tmp
local/py/src/braintrust_local/.bt_unittest_args.db
.vercel
.auth/
playwright-report/
**/playwright/test-results/
.aider*
__pycache__

# END root .gitignore

# START brainstore .dockerignore

brainstore/Dockerfile
brainstore/examples
brainstore/target

# END brainstore .dockerignore
