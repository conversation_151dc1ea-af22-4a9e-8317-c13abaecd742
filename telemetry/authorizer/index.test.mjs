import { describe, it, beforeEach, afterEach } from "node:test";
import assert from "node:assert/strict";
import { handler } from "./index.mjs";

// Mock fetch globally
const mockFetch = async (url, options) => {
  const token = options.headers.Authorization.replace("Bearer ", "");

  if (token === "valid_token") {
    return {
      status: 200,
      json: async () => ({
        id: "user123",
        email: "<EMAIL>",
        organizations: [
          { id: "org1", name: "Org One" },
          { id: "org2", name: "Org Two" },
        ],
      }),
    };
  }

  return {
    status: 401,
    json: async () => ({ error: "Unauthorized" }),
  };
};

describe("API Gateway Authorizer Tests", async () => {
  let originalFetch;
  let originalEnv;
  let consoleOutput;

  beforeEach(() => {
    // Store original fetch and environment
    originalFetch = global.fetch;
    originalEnv = process.env;

    // Mock fetch
    global.fetch = mockFetch;

    // Mock environment
    process.env = { ...originalEnv, NODE_ENV: "test" };

    // Capture console output
    consoleOutput = {
      debug: [],
      warn: [],
      info: [],
    };

    ["debug", "warn", "info"].forEach((level) => {
      console[level] = (...args) => {
        consoleOutput[level].push(args);
      };
    });
  });

  afterEach(() => {
    // Restore original fetch and environment
    global.fetch = originalFetch;
    process.env = originalEnv;
  });

  it("should authorize valid tokens", async () => {
    const event = {
      authorizationToken: "Bearer valid_token",
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
    };

    const response = await handler(event);

    assert.equal(response.principalId, "user123");
    assert.equal(response.policyDocument.Statement[0].Effect, "Allow");
    assert.deepEqual(JSON.parse(response.context.orgIds), ["org1", "org2"]);
    assert.deepEqual(JSON.parse(response.context.orgNames), [
      "Org One",
      "Org Two",
    ]);
    assert.equal(response.context.userEmail, "<EMAIL>");
  });

  it("should deny invalid tokens", async () => {
    const event = {
      authorizationToken: "Bearer invalid_token",
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
    };

    const response = await handler(event);

    assert.equal(response.principalId, "user");
    assert.equal(response.policyDocument.Statement[0].Effect, "Deny");
  });

  it("should handle missing tokens", async () => {
    const event = {
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
    };

    const response = await handler(event);

    assert.equal(response.principalId, "user");
    assert.equal(response.policyDocument.Statement[0].Effect, "Deny");
  });

  it("should mask tokens in logs", async () => {
    process.env.DEBUG = "true";

    const token =
      "mysupersecrettokenmysupersecrettokenmysupersecrettokenmysupersecrettokenmysupersecrettoken";
    const event = {
      authorizationToken: `Bearer ${token}`,
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
    };

    await handler(event);

    // Check that the token was masked in logs
    const logEntry = consoleOutput.debug.find(
      (entry) =>
        entry[0] === "AUTHENTICATION" &&
        entry[1] === "Authenticating with token",
    );

    assert.ok(logEntry);
    assert.ok(!logEntry[2].includes(token));
    assert.ok(logEntry[2].includes("*****"));
  });

  it("should only log when DEBUG is true or not in test", async () => {
    process.env.DEBUG = "";
    process.env.NODE_ENV = "test";

    const event = {
      authorizationToken: "Bearer valid_token",
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
    };

    await handler(event);
    assert.ok(consoleOutput.debug.length == 0);

    process.env.NODE_ENV = "";
    await handler(event);
    assert.ok(consoleOutput.debug.length > 0);

    process.env.NODE_ENV = "production";
    await handler(event);
    assert.ok(consoleOutput.debug.length > 0);
  });

  it("should safely stringify objects in logs", async () => {
    process.env.DEBUG = "true";

    const event = {
      authorizationToken: "Bearer valid_token",
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
    };

    await handler(event);

    // Check that objects are properly stringified in logs
    const logEntry = consoleOutput.debug.find(
      (entry) =>
        entry[0] === "AUTHENTICATION" && entry[1] === "Authenticated user",
    );

    assert.ok(logEntry);
    assert.equal(logEntry[2], "user123");
  });

  it("should handle circular references in log objects", async () => {
    process.env.DEBUG = "true";

    const circularObj = { prop: "value" };
    circularObj.self = circularObj;

    const event = {
      authorizationToken: "Bearer valid_token",
      methodArn: "arn:aws:execute-api:region:account:api/stage/method/path",
      circular: circularObj,
    };

    // This should not throw
    await assert.doesNotReject(async () => {
      await handler(event);
    });
  });
});
