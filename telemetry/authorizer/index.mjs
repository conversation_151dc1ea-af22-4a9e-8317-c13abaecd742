const mask = (string, reveal = 5) =>
  string.length < reveal * 4
    ? "<CENSORED>"
    : string.slice(0, reveal) +
      new Array(string.length - reveal * 2).join("*") +
      string.slice(-1 * reveal);

const authenticate = async (token) => {
  if (!token) {
    throw new Error("Missing token");
  }

  log("debug", "authentication", "Authenticating with token", mask(token));

  const response = await fetch("https://www.braintrust.dev/api/self/me", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (response.status !== 200) {
    throw new Error("status code " + response.status);
  }

  return await response.json();
};

export const handler = async (event) => {
  try {
    const me = await authenticate(
      (event.authorizationToken || "").replace("Bearer ", "").trim(),
    );

    log("debug", "authentication", "Authenticated user", me.id);

    // Create a map of org IDs to names for easy lookup
    const orgs = new Map(me.organizations.map((o) => [o.id, o.name]));

    // Return both the IAM policy and the user context
    return {
      principalId: me.id, // Use the actual user ID instead of generic "user"
      policyDocument: {
        Version: "2012-10-17",
        Statement: [
          {
            Action: "execute-api:Invoke",
            Effect: "Allow",
            Resource: event.methodArn,
          },
        ],
      },
      // This context will be available in $context.authorizer.* in API Gateway
      context: {
        userId: me.id,
        userEmail: me.email,
        orgIds: JSON.stringify([...orgs.keys()]), // Must be string, can't pass arrays directly
        orgNames: JSON.stringify([...orgs.values()]),
      },
    };
  } catch (error) {
    log("warn", "authentication", "Request denied for ", error);
    return {
      principalId: "user",
      policyDocument: {
        Version: "2012-10-17",
        Statement: [
          {
            Action: "execute-api:Invoke",
            Effect: "Deny",
            Resource: event.methodArn,
          },
        ],
      },
    };
  }
};

const log = (level, namespace, ...args) => {
  if (process.env.DEBUG || process.env.NODE_ENV !== "test") {
    console[level](namespace.toUpperCase(), ...args.map(safe));
  }
};

const safe = (arg) => {
  if (arg instanceof Error) {
    arg = {
      name: arg.name,
      message: arg.message,
    };
  }

  if (typeof arg === "object" && arg !== null) {
    try {
      arg = JSON.stringify(arg);
    } catch {
      arg = String(arg);
    }
  }

  return arg;
};
