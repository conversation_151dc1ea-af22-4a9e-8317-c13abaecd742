export const handler = async (event) => {
  try {
    const authResult = validateAuthorization(event.requestContext);
    if (!authResult.isValid) {
      return {
        statusCode: authResult.error.statusCode,
        body: JSON.stringify({ error: { message: authResult.error.message } }),
      };
    }

    const parseResult = parseEvents(event.body);
    if (!parseResult.isValid) {
      return {
        statusCode: parseResult.error.statusCode,
        body: JSON.stringify({ error: { message: parseResult.error.message } }),
      };
    }

    const { validEvents, validationFailed, debug } = validateAndSeparateEvents(
      parseResult.events,
      authResult.orgIdSet,
    );

    if (validEvents.length > 0) {
      const firehoseResult = await sendToFirehose(validEvents);
      if (!firehoseResult.success) {
        return {
          statusCode: firehoseResult.error.statusCode,
          body: JSON.stringify({
            error: { message: firehoseResult.error.message },
          }),
        };
      }

      log("debug", "firehose", "events sent to firehose", firehoseResult.stats);
    }

    const isDebugMode = ["true", "1"].includes(
      event.queryStringParameters?.debug,
    );

    if (validationFailed.length > 0) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          validation_failed: validationFailed,
          ...(isDebugMode ? { debug } : {}),
        }),
      };
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        validation_failed: [],
        ...(isDebugMode ? { debug } : {}),
      }),
    };
  } catch (error) {
    log("error", "global", "Error processing request:", error);

    return {
      statusCode: error.statusCode || 500,
      body: JSON.stringify({
        error: {
          message: error.message || "Internal server error",
        },
      }),
    };
  }
};

export const validateAuthorization = (requestContext) => {
  if (!requestContext?.authorizer) {
    return {
      isValid: false,
      error: {
        statusCode: 401,
        message: "Missing authorization context.",
      },
    };
  }

  const orgIds = requestContext.authorizer.orgIds;
  if (!orgIds) {
    return {
      isValid: false,
      error: {
        statusCode: 401,
        message: "No organization IDs in context.",
      },
    };
  }

  try {
    const orgIdSet = new Set(JSON.parse(orgIds));
    return {
      isValid: true,
      orgIdSet,
    };
  } catch {
    return {
      isValid: false,
      error: {
        statusCode: 401,
        message: "Invalid organization IDs format.",
      },
    };
  }
};

export const parseEvents = (body) => {
  try {
    const parsed = JSON.parse(body);
    if (!parsed.events || !Array.isArray(parsed.events)) {
      return {
        isValid: false,
        error: {
          statusCode: 400,
          message: "Request must include an events array.",
        },
      };
    }
    return {
      isValid: true,
      events: parsed.events,
    };
  } catch {
    return {
      isValid: false,
      error: {
        statusCode: 400,
        message: "Invalid JSON in request body",
      },
    };
  }
};

export const validateAndSeparateEvents = (events, orgIdSet) => {
  const validEvents = [];
  const validationFailed = [];
  const debug = {
    duplicate: [],
    ingested: [],
  };

  for (const event of events) {
    if (
      event.external_customer_id &&
      !orgIdSet.has(event.external_customer_id)
    ) {
      log("warn", "validation", "Unauthorized event. Unexpected orgId", event);
      validationFailed.push({
        idempotency_key: event.idempotency_key,
        validation_errors: [
          "Unauthorized - external_customer_id not in user's organizations",
        ],
      });
      continue;
    }

    validEvents.push(event);
    debug.ingested.push(event.idempotency_key);
  }

  return { validEvents, validationFailed, debug };
};

let firehose;
let firehoseClient;

export const setFirehose = (firehoseModule) => {
  firehose = firehoseModule;
  firehoseClient = new firehoseModule.FirehoseClient();
};

export const sendToFirehose = async (events) => {
  if (events.length === 0) return { success: true };

  if (!firehose) {
    setFirehose(await import("@aws-sdk/client-firehose"));
  }

  try {
    const records = events.map((event) => ({
      Data: Buffer.from(JSON.stringify(event)),
    }));

    const command = new firehose.PutRecordBatchCommand({
      DeliveryStreamName:
        process.env.FIREHOSE_STREAM_NAME || "telemetry-events-ingestion-buffer",
      Records: records,
    });

    const result = await firehoseClient.send(command);

    return {
      success: true,
      stats: {
        failedCount: result.FailedPutCount || 0,
        successCount: records.length - (result.FailedPutCount || 0),
      },
    };
  } catch (error) {
    log("error", "firehose", "Failed to process events", error);
    return {
      success: false,
      error: {
        statusCode: 500,
        message: "Failed to process events",
      },
    };
  }
};

const log = (level, namespace, ...args) => {
  if (process.env.DEBUG || process.env.NODE_ENV !== "test") {
    console[level](namespace.toUpperCase(), ...args.map(safe));
  }
};

const safe = (arg) => {
  if (arg instanceof Error) {
    arg = {
      name: arg.name,
      message: arg.message,
    };
  }

  if (typeof arg === "object" && arg !== null) {
    try {
      arg = JSON.stringify(arg);
    } catch {
      arg = String(arg);
    }
  }

  return arg;
};
