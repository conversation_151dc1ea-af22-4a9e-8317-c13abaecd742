import { describe, it, beforeEach, afterEach } from "node:test";
import assert from "node:assert/strict";
import { handler, setFirehose } from "./index.mjs";

// Mock Firehose client and command
class MockFirehoseClient {
  constructor() {
    this.mockResponses = [];
    this.recordsSent = [];
  }

  setMockResponse(response) {
    this.mockResponses.push(response);
  }

  async send(command) {
    this.recordsSent.push(command);
    const response = this.mockResponses.shift() || { FailedPutCount: 0 };
    if (response instanceof Error) throw response;
    return response;
  }
}

class MockPutRecordBatchCommand {
  constructor(input) {
    this.input = input;
  }
}

describe("API Gateway Proxy Integration Tests", async () => {
  let firehoseClient;

  beforeEach(() => {
    // Setup fresh mock client for each test
    firehoseClient = new MockFirehoseClient();
    const mockFirehose = {
      FirehoseClient: function () {
        return firehoseClient;
      },
      PutRecordBatchCommand: MockPutRecordBatchCommand,
    };
    setFirehose(mockFirehose);
  });

  afterEach(() => {
    // Clean up mocks
    firehoseClient = null;
  });

  it("should successfully process valid events", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1", "org2"]),
        },
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org1",
            idempotency_key: "key1",
            data: "test1",
          },
          {
            external_customer_id: "org2",
            idempotency_key: "key2",
            data: "test2",
          },
        ],
      }),
    };

    const response = await handler(event);

    assert.equal(response.statusCode, 200);
    assert.deepEqual(JSON.parse(response.body), { validation_failed: [] });

    // Verify Firehose was called correctly
    assert.equal(firehoseClient.recordsSent.length, 1);
    const sentCommand = firehoseClient.recordsSent[0];
    assert.equal(sentCommand.input.Records.length, 2);
  });

  it("should handle unauthorized events", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org2", // Unauthorized org
            idempotency_key: "key1",
            data: "test1",
          },
        ],
      }),
    };

    const response = await handler(event);

    assert.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    assert.equal(body.validation_failed.length, 1);
    assert.equal(body.validation_failed[0].idempotency_key, "key1");
  });

  it("should handle invalid JSON in request body", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      body: "invalid json{",
    };

    const response = await handler(event);

    assert.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    assert.equal(body.error.message, "Invalid JSON in request body");
  });

  it("should handle missing authorization context", async () => {
    const event = {
      requestContext: {},
      body: JSON.stringify({ events: [] }),
    };

    const response = await handler(event);

    assert.equal(response.statusCode, 401);
    const body = JSON.parse(response.body);
    assert.equal(body.error.message, "Missing authorization context.");
  });

  it("should handle Firehose failures", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org1",
            idempotency_key: "key1",
            data: "test1",
          },
        ],
      }),
    };

    // Set up Firehose to fail
    firehoseClient.setMockResponse(new Error("Firehose error"));

    const response = await handler(event);

    assert.equal(response.statusCode, 500);
    const body = JSON.parse(response.body);
    assert.equal(body.error.message, "Failed to process events");
  });

  it("should handle debug mode", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      queryStringParameters: {
        debug: "true",
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org1",
            idempotency_key: "key1",
            data: "test1",
          },
        ],
      }),
    };

    const response = await handler(event);

    assert.equal(response.statusCode, 200);
    const body = JSON.parse(response.body);
    assert.ok(body.debug);
    assert.deepEqual(body.debug.ingested, ["key1"]);
  });

  it("should handle empty events array", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      body: JSON.stringify({
        events: [],
      }),
    };

    const response = await handler(event);
    assert.equal(response.statusCode, 200);
    assert.deepEqual(JSON.parse(response.body), { validation_failed: [] });
    assert.equal(firehoseClient.recordsSent.length, 0);
  });

  it("should handle undefined queryStringParameters", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org1",
            idempotency_key: "key1",
            data: "test1",
          },
        ],
      }),
    };

    const response = await handler(event);
    assert.equal(response.statusCode, 200);
    const body = JSON.parse(response.body);
    assert.deepEqual(body, { validation_failed: [] });
    assert.equal(body.debug, undefined);
  });

  it("should handle invalid orgIds JSON format", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: "invalid json{",
        },
      },
      body: JSON.stringify({
        events: [],
      }),
    };

    const response = await handler(event);
    assert.equal(response.statusCode, 401);
    const body = JSON.parse(response.body);
    assert.equal(body.error.message, "Invalid organization IDs format.");
  });

  it("should handle mixed valid and invalid events", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      queryStringParameters: {
        debug: "true",
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org1",
            idempotency_key: "key1",
            data: "test1",
          },
          {
            external_customer_id: "org2", // Unauthorized
            idempotency_key: "key2",
            data: "test2",
          },
          {
            external_customer_id: "org1",
            idempotency_key: "key3",
            data: "test3",
          },
        ],
      }),
    };

    const response = await handler(event);
    assert.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    assert.equal(body.validation_failed.length, 1);
    assert.equal(body.validation_failed[0].idempotency_key, "key2");
    assert.deepEqual(body.debug.ingested, ["key1", "key3"]);

    // Verify Firehose was called with valid events
    assert.equal(firehoseClient.recordsSent.length, 1);
    const sentCommand = firehoseClient.recordsSent[0];
    assert.equal(sentCommand.input.Records.length, 2);
  });

  it("should handle missing idempotency_key in events", async () => {
    const event = {
      requestContext: {
        authorizer: {
          orgIds: JSON.stringify(["org1"]),
        },
      },
      body: JSON.stringify({
        events: [
          {
            external_customer_id: "org1",
            data: "test1",
          },
        ],
      }),
    };

    const response = await handler(event);
    assert.equal(response.statusCode, 200);
    // Event should still be processed even without idempotency_key
    assert.equal(firehoseClient.recordsSent.length, 1);
  });
});
