# Telemetry Service

This service handles event ingestion for Braintrust's telemetry system. It consists of two main Lambda functions that work with API Gateway:

- `authorizer`: Handles authentication and authorization
- `handler`: Processes and forwards events to Kinesis Firehose

## Architecture

```
Client -> API Gateway -> Custom Authorizer Lambda -> Proxy Lambda -> <PERSON><PERSON><PERSON> -> S3
```

For detailed architecture diagrams, see:

- [Architecture.tldr](./Architecture.tldr) - Editable diagram source
- [architecture.png](./architecture.png) - Static architecture diagram

For detailed architecture information and setup instructions, see [RUNBOOK.md](./RUNBOOK.md).

## Local Development

### Prerequisites

- Node.js >= 20
- AWS CLI configured with appropriate credentials
- Access to the Braintrust AWS Console

### Development Workflow

#### API Gateway Authorizer

1. Make changes to `authorizer/index.mjs`
2. Deploy changes:
   - Go to AWS Lambda Console
   - Find the `telemetry-events-ingestion-authorizer` function
   - Update the function code by copying the contents of `index.mjs`
   - Test in AWS Console using the test event template

#### API Gateway Proxy

1. Make changes to `handler/index.mjs`
2. Test locally i.e. `node handler/index.test.mjs`
3. Deploy changes:
   - Go to AWS Lambda Console
   - Find the `telemetry-events-ingestion-handler` function
   - Update the function code by copying the contents of `index.mjs`
   - Test in AWS Console using the test event template

### Manual Testing

Test the complete flow using curl:

1. Test event ingestion:

```bash
curl -X POST \
  https://API_ID.execute-api.REGION.amazonaws.com/v1/events \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "events": [
        {
            "event_name": "LogInsertedEvent",
            "idempotency_key": "[\"2857d2cc-c738-48f5-9eb8-f9381f02a5a9\",\"project_logs\",\"2ae3aa4a-d8be-479f-8e5f-627eb60672ce\",\"1000194535869775896\"]",
            "timestamp": "2025-02-04T04:00:14.748Z",
            "external_customer_id": "eaacb1d9-bded-437b-a02a-7c4e369730a2",
            "properties": {
                "object_type": "project_logs",
                "object_id": "2ae3aa4a-d8be-479f-8e5f-627eb60672ce",
                "org_id": "eaacb1d9-bded-437b-a02a-7c4e369730a2",
                "row_id": "2857d2cc-c738-48f5-9eb8-f9381f02a5a9",
                "log_bytes": 646,
                "project_id": "2ae3aa4a-d8be-479f-8e5f-627eb60672ce",
                "xact_id": "1000194535869775896",
                "span_attributes_type": "task",
                "span_id": "8db4514d-abcc-4d5b-9bf5-a916b61c3011",
                "root_span_id": "8db4514d-abcc-4d5b-9bf5-a916b61c3011",
                "span_parents_csv": "__UNSET__",
                "user_id": "fac36c53-c882-458b-bf80-60d06c3e8a0d",
                "app_origin": "http://localhost:3000"
            }
        }
    ]
}'
```

3. Verify data flow:
   - Check CloudWatch logs for both Lambda functions
   - Wait ~15 minutes for Firehose buffer
   - Check S3 bucket for new files in path: `vendor=orb/year=YYYY/month=MM/day=DD/hour=HH/`
