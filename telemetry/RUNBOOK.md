# Telemetry Service Runbook

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Security Considerations](#security-considerations)
3. [Infrastructure Setup](#infrastructure-setup)
4. [Testing and Validation](#testing-and-validation)
5. [Monitoring and Alerting](#monitoring-and-alerting)
6. [Maintenance and Updates](#maintenance-and-updates)
7. [Troubleshooting Guide](#troubleshooting-guide)

## Architecture Overview

```
Client -> API Gateway -> Custom Authorizer Lambda -> Proxy Lambda -> Kinesis Firehose -> S3
```

Components:

- **S3 Bucket**: Stores Orb event data with 30-day retention

  - Encrypted at rest using SSE-S3
  - Organized by vendor and time-based partitioning
  - Automatic cleanup via lifecycle policies

- **Kinesis Firehose**: Buffers events before S3 delivery

  - Buffer size: 15k records (optimized for batch efficiency)
  - Buffer interval: 15 minutes (balances latency vs. cost)
  - Error handling with dedicated error prefix path

- **Proxy Lambda**: Validates and forwards events

  - Validates event format and permissions
  - Batches records for Firehose
  - Comprehensive error handling and logging

- **Authorizer Lambda**: Handles authentication

  - Validates tokens against Braintrust API
  - Provides organization-level access control
  - Caches auth results for 5 minutes (API Gateway default)

- **API Gateway**: REST API endpoint
  - Regional endpoint for lower latency
  - Token-based authentication
  - Request/response validation
  - Rate limiting and throttling protection

## Security Considerations

### Data Protection

- All data encrypted at rest using SSE-S3
- All transmissions over HTTPS (TLS 1.2+)
- Authorization tokens masked in logs
- Sensitive data handling compliant with security best practices

### Access Control

- Principle of least privilege for all IAM roles
- Token-based authentication required for all requests
- Organization-level authorization checks
- No public access to S3 bucket

### Monitoring and Audit

- CloudWatch logs retention set to 30 days
- API Gateway access logging enabled
- S3 access logging configured
- CloudTrail enabled for API activity

## Infrastructure Setup

### 1. S3 Bucket Setup

1. Go to **S3** > **Create bucket**

   ```
   Name: telemetry-prod-events-ingestion
   Region: us-east-1
   Block Public Access: Enable all blocking options
   Bucket Versioning: Disabled (cost optimization for short-lived data)
   Default Encryption: Enable (SSE-S3)
   Object Lock: Disabled (not needed for temporary storage)
   Tags:
     - Environment: prod
     - Service: telemetry
   ```

   > **Security Note**: Enabling SSE-S3 encryption is mandatory for data protection.

2. Create lifecycle rule:

   ```
   Name: 30-day-retention
   Rule scope: Apply to all objects
   Expiration: Current version
   Days after creation: 30
   Cleanup incomplete multipart uploads: Enable (1 day)
   ```

3. Copy the bucket ARN for later use:

   ```
   arn:aws:s3:::telemetry-prod-events-ingestion
   ```

4. Add a [policy](https://us-east-1.console.aws.amazon.com/s3/buckets/telemetry-prod-events-ingestion?region=us-east-1&bucketType=general&tab=permissions) to the bucket for Orb to put records:

```
{
      "Version": "2012-10-17",
      "Statement": [
         {
            "Effect": "Allow",
            "Principal": {
                  "AWS": "arn:aws:iam::970006758186:role/orb-braintrust-s3-access"
            },
            "Action": "s3:ListBucket",
            "Resource": "arn:aws:s3:::telemetry-prod-events-ingestion"
         },
         {
            "Effect": "Allow",
            "Principal": {
                  "AWS": "arn:aws:iam::970006758186:role/orb-braintrust-s3-access"
            },
            "Action": [
                  "s3:GetObject",
                  "s3:GetObjectAcl"
            ],
            "Resource": "arn:aws:s3:::telemetry-prod-events-ingestion/*"
         }
      ]
}

```

Now we need to create a bucket for the dead letter queue (events that Orb was unable to process).

1. Go to **S3** > **Create bucket**

   ```
   Name: telemetry-prod-events-dlq
   Region: us-east-1
   Block Public Access: Enable all blocking options
   Bucket Versioning: Disabled (cost optimization for short-lived data)
   Default Encryption: Enable (SSE-S3)
   Object Lock: Disabled (not needed for temporary storage)
   Tags:
     - Environment: prod
     - Service: telemetry
   ```

   > **Security Note**: Enabling SSE-S3 encryption is mandatory for data protection.

2. Copy the bucket ARN for later use:

   ```
   arn:aws:s3:::telemetry-prod-events-dlq
   ```

3. Add a [policy](https://us-east-1.console.aws.amazon.com/s3/bucket/telemetry-prod-events-dlq/property/policy/edit?region=us-east-1&bucketType=general) to the dead letter queue for Orb to put records:

```
{
      "Version": "2012-10-17",
      "Statement": [
         {
            "Effect": "Allow",
            "Principal": {
                  "AWS": "arn:aws:iam::970006758186:role/orb-braintrust-s3-access"
            },
            "Action": "s3:ListBucket",
            "Resource": "arn:aws:s3:::telemetry-prod-events-dlq"
         },
         {
            "Effect": "Allow",
            "Principal": {
                  "AWS": "arn:aws:iam::970006758186:role/orb-braintrust-s3-access"
            },
            "Action": [
                  "s3:GetObject",
                  "s3:GetObjectAcl",
                  "s3:PutObject",
                  "s3:PutObjectAcl"
            ],
            "Resource": [
                  "arn:aws:s3:::telemetry-prod-events-dlq/*"
            ]
         }
      ]
}
```

### 2. Kinesis Firehose Setup

1. Create Delivery Stream:

   1. Go to https://us-east-1.console.aws.amazon.com/firehose/home?region=us-east-1#/home
   2. Basic settings:
      Name: telemetry-events-ingestion-buffer
      Source: Direct PUT
      Destination: Amazon S3

   3. Delivery stream settings:
      S3 bucket: telemetry-events-ingestion
      New line delimiter: enabled.
      S3 prefix: /orb/!{timestamp:yyyy}/!{timestamp:MM}/!{timestamp:dd}/!{timestamp:HH}/
      Error prefix: /errors/orb/
      Buffer size: 5 MiB
      Buffer interval: 300 seconds
      IAM role: Create or update IAM role

   4. Click "Create delivery stream"
   5. Copy the delivery stream ARN for later use

   ```
   arn:aws:firehose:us-east-1:872608195481:deliverystream/telemetry-events-ingestion-buffer
   ```

### 3. Proxy Lambda Setup

1. Create Lambda Role:

   ```
   1. Go to IAM > Roles > Create role
   2. Select "Lambda" as the service
   3. Click "Next"
   4. Search and attach "AWSLambdaBasicExecutionRole"
   5. Click "Next"
   6. Name: telemetry-events-ingestion-handler-role
   7. Click "Create role"
   8. Find the new role and add inline policy:
      {
        "Version": "2012-10-17",
        "Statement": [{
          "Effect": "Allow",
          "Action": "firehose:PutRecordBatch",
          "Resource": "FIREHOSE_ARN_FROM_STEP_2"
        }]
      }
   9. Name the policy: FirehosePutRecords
   ```

2. Create Lambda Function:

   ```
   1. Go to Lambda > Create function
   2. Basic settings:
      Name: telemetry-events-ingestion-handler
      Runtime: Node.js 22.x
      Architecture: arm64
      Execution role: Use the role created above

   3. Environment variables:
      FIREHOSE_STREAM_NAME: telemetry-events-ingestion-buffer

   4. Code: Copy from [handler/index.mjs](handler/index.mjs)
   ```

3. Setup Monitoring:

   ````
   1. Setup Handler Monitoring:
      1. Go to CloudWatch > Log groups > /aws/lambda/telemetry-events-ingestion-handler
      2. Click "Metric filters" tab
      3. Create these metric filters:

      a. Firehose Errors:
         Filter pattern: ERROR FIREHOSE
         Metric namespace: TelemetryService
         Metric name: FirehoseErrors
         Metric value: 1
         Unit: Count

      b. Validation Failures:
         Filter pattern: WARN VALIDATION
         Metric namespace: TelemetryService
         Metric name: ValidationFailures
         Metric value: 1
         Unit: Count

      c. Global Errors:
         Filter pattern: ERROR GLOBAL
         Metric namespace: TelemetryService
         Metric name: GlobalErrors
         Metric value: 1
         Unit: Count

      d. Lambda Timeouts:
         Filter pattern: %[sS]tatus\s*:?\s*[tT]imeout%
         Metric namespace: TelemetryService
         Metric name: HandlerTimeouts
         Metric value: 1
         Unit: Count

   2. Create handler alarms:
      a. FirehoseErrors:
         ```json
         {
             "metrics": [
                 [ { "expression": "100 * errors / total", "label": "Firehose Error Rate", "id": "e1" } ],
                 [ "TelemetryService", "FirehoseErrors", { "id": "errors", "visible": false } ],
                 [ "AWS/Lambda", "Invocations", "FunctionName", "telementry-events-ingestion-api-handler", { "id": "total", "visible": false } ]
             ],
             "view": "timeSeries",
             "stacked": false,
             "period": 300,
             "region": "us-east-2",
             "stat": "Sum"
         }
         ```
         Additional settings:
         - Threshold: Greater than 5 (representing 5% error rate)
         - Evaluation periods: 3
         - Datapoints to alarm: 2
         - Missing data treatment: Ignore
         - Action: Create new SNS topic
           - Topic name: TelemetryFirehoseErrors
         - Description: "Alert when more than 5% of records are failing to write to Firehose in at least 2 out of 3 consecutive 5-minute windows"

      b. ValidationFailures:
         ```json
         {
             "metrics": [
                 [ { "expression": "100 * failures / total", "label": "Validation Failure Rate", "id": "e1" } ],
                 [ "TelemetryService", "ValidationFailures", { "id": "failures", "visible": false } ],
                 [ "AWS/Lambda", "Invocations", "FunctionName", "telementry-events-ingestion-api-handler", { "id": "total", "visible": false } ]
             ],
             "view": "timeSeries",
             "stacked": false,
             "period": 300,
             "region": "us-east-2",
             "stat": "Sum"
         }
         ```
         Additional settings:
         - Threshold: Greater than 30 (representing 30% validation failure rate)
         - Evaluation periods: 3
         - Datapoints to alarm: 2
         - Missing data treatment: Ignore
         - Action: Create new SNS topic
           - Topic name: TelemetryValidationFailures
           - Display name: Telemetry Validation Failures
         - Description: "Alert when more than 30% of requests are failing validation in at least 2 out of 3 consecutive 5-minute windows"

      c. HandlerTimeouts:
         ```json
         {
             "metrics": [
                 [ { "expression": "100 * timeouts / total", "label": "Handler Timeout Rate", "id": "e1" } ],
                 [ "TelemetryService", "HandlerTimeouts", { "id": "timeouts", "visible": false } ],
                 [ "AWS/Lambda", "Invocations", "FunctionName", "telementry-events-ingestion-api-handler", { "id": "total", "visible": false } ]
             ],
             "view": "timeSeries",
             "stacked": false,
             "period": 300,
             "region": "us-east-2",
             "stat": "Sum"
         }
         ```
         Additional settings:
         - Threshold: Greater than 1 (representing 1% timeout rate)
         - Evaluation periods: 3
         - Datapoints to alarm: 2
         - Missing data treatment: Ignore
         - Action: Create new SNS topic
           - Topic name: TelemetryHandlerTimeouts
           - Display name: Telemetry Handler Timeouts
         - Description: "Alert when more than 1% of handler invocations are timing out in at least 2 out of 3 consecutive 5-minute windows"
   ````

### 4. Authorizer Lambda Setup

1. Create Lambda Role:

   ```
   1. Go to IAM > Roles > Create role
   2. Select "Lambda" as the service
   3. Click "Next"
   4. Search and attach "AWSLambdaBasicExecutionRole"
   5. Click "Next"
   6. Name: telemetry-events-ingestion-authorizer-role
   7. Click "Create role"
   ```

2. Create Lambda Function:

   ```
   1. Go to Lambda > Create function
   2. Basic settings:
      Name: telemetry-events-ingestion-authorizer
      Runtime: Node.js 22.x
      Architecture: arm64
      Execution role: Use the role created above

   3. Code: Copy from [authorizer/index.mjs](authorizer/index.mjs)
   ```

3. Setup Monitoring:

   ````
   1. Setup Authorizer Monitoring:
      1. Go to CloudWatch > Log groups > /aws/lambda/telemetry-events-ingestion-authorizer
      2. Click "Metric filters" tab
      3. Create metric filters:

      a. Auth Failures:
         Filter pattern: "Request denied for"
         Metric namespace: TelemetryService
         Metric name: AuthFailures
         Metric value: 1
         Unit: Count

      b. Lambda Timeouts:
         Filter pattern: %[sS]tatus\s*:?\s*[tT]imeout%
         Metric namespace: TelemetryService
         Metric name: AuthorizerTimeouts
         Metric value: 1
         Unit: Count

   2. Create authorizer alarms:
      a. AuthFailures:
         ```json
         {
             "metrics": [
                 [ { "expression": "100 * failures / total", "label": "Auth Failure Rate", "id": "e1" } ],
                 [ "TelemetryService", "AuthFailures", { "id": "failures", "visible": false } ],
                 [ "AWS/Lambda", "Invocations", "FunctionName", "telementry-events-ingestion-api-authorizer", { "id": "total", "visible": false } ]
             ],
             "view": "timeSeries",
             "stacked": false,
             "period": 300,
             "region": "us-east-2",
             "stat": "Sum"
         }
         ```
         Additional settings:
         - Threshold: Greater than 20 (representing 20% failure rate)
         - Evaluation periods: 3
         - Datapoints to alarm: 2
         - Missing data treatment: Ignore
         - Action: Create new SNS topic
           - Topic name: TelemetryAuthFailures
           - Display name: Telemetry Auth Failures
         - Description: "Alert when more than 20% of authorization attempts are failing in at least 2 out of 3 consecutive 5-minute windows"

      b. AuthorizerTimeouts:
         ```json
         {
             "metrics": [
                 [ { "expression": "100 * timeouts / total", "label": "Authorizer Timeout Rate", "id": "e1" } ],
                 [ "TelemetryService", "AuthorizerTimeouts", { "id": "timeouts", "visible": false } ],
                 [ "AWS/Lambda", "Invocations", "FunctionName", "telementry-events-ingestion-api-authorizer", { "id": "total", "visible": false } ]
             ],
             "view": "timeSeries",
             "stacked": false,
             "period": 300,
             "region": "us-east-2",
             "stat": "Sum"
         }
         ```
         Additional settings:
         - Threshold: Greater than 1 (representing 1% timeout rate)
         - Evaluation periods: 3
         - Datapoints to alarm: 2
         - Missing data treatment: Ignore
         - Action: Create new SNS topic
           - Topic name: TelemetryAuthorizerTimeouts
           - Display name: Telemetry Authorizer Timeouts
         - Description: "Alert when more than 1% of authorizer invocations are timing out in at least 2 out of 3 consecutive 5-minute windows"
   ````

### 4.1 Configure AWS Chatbot for Slack:

```
1. Go to AWS Chatbot console
2. Click "Configure new client"
3. Select "Slack"
4. Choose channel: #service-telemetry
5. Configuration name: TelemetryAlerts
6. IAM role: Use ReadOnlyAccess policy
7. SNS topics: Select the topics you created in the previous steps
8. Click "Configure"
```

### 5. API Gateway IAM Setup

1. Create API Gateway Role:

   ```
   1. Go to IAM > Roles > Create role
   2. Select "API Gateway" as the service
   3. Click "Next"
   4. Name: telemetry-events-ingestion-apigateway-role
   5. Click "Create role"
   6. Find the new role and add inline policy:
      {
         "Version": "2012-10-17",
         "Statement": [
            {
               "Effect": "Allow",
               "Action": [
                  "lambda:InvokeFunction"
               ],
               "Resource": [
                  "arn:aws:lambda:us-east-1:872608195481:function:telemetry-events-ingestion-authorizer",
                  "arn:aws:lambda:us-east-1:872608195481:function:telemetry-events-ingestion-handler"
               ]
            }
         ]
      }
   7. Name the policy: LambdaInvoke
   8. Copy the role ARN for later use
   ```

   ```
   arn:aws:iam::872608195481:role/telemetry-events-ingestion-apigateway-role
   ```

2. Create Resource Policy:
   ```
   1. Go to IAM > Policies > Create policy
   2. Choose JSON and paste:
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Resource": "ROLE_ARN_FROM_STEP_1"
          }
        ]
      }
   3. Name: telemetry-events-ingestion-apigateway-trust
   4. Click "Create policy"
   ```

### 6. API Gateway Setup

1. Create REST API:

   ```
   1. Go to API Gateway > Create API
   2. Settings:
      Name: TelemetryEventsAPI
      Type: REST
      Endpoint Type: Regional
   3. Additional settings:
      API Gateway Execution Role: Use the role ARN from step 5.1
   ```

2. Create Authorizer:

   ```
   1. Click "Authorizers" > "Create New Authorizer"
   2. Name: BearerTokenAuth
   3. Type: Lambda
   4. Lambda Function: telemetry-events-ingestion-authorizer
   5. Token Source: Authorization
   6. Click "Create"
   7. When prompted to add Lambda permission, click "Allow"
   ```

Make sure to set timeout to 1 minute.

3. Create API Resource:

   ```
   1. Click "Resources" > "Create Resource"
   2. Resource Name: events
   3. Resource Path: /
   4. Click "Create Resource"
   ```

4. Create POST Method:

   ```
   1. With /events selected, click "Create Method" > "POST"
   2. Integration type: Lambda Function
   3. Use Lambda Proxy integration: Yes
   4. Lambda Function: telemetry-events-ingestion-handler
   4. HTTP request headers:
      - Name: Authorization
      - Required: Yes
      - Caching: No
   5. Click "Save"
   6. When prompted to add Lambda permission, click "Allow"
   ```

5. Make sure to set:

- the authorizer in the Method request.
- the integration request is a Lambda proxy integration

6. Deploy API:
   ```
   1. Click "Actions" > "Deploy API"
   2. Stage name: v1
   3. Click "Deploy"
   4. Copy the Invoke URL for testing
   ```

### 7. CloudWatch Dashboard Setup

1. Create Dashboard:

   ```
   1. Go to CloudWatch > Dashboards
   2. Click "Create dashboard"
   3. Name: TelemetryServiceDashboard
   ```

2. Dashboard Configuration:

   ```json
   {
     "widgets": [
       {
         "height": 10,
         "width": 9,
         "y": 9,
         "x": 0,
         "type": "metric",
         "properties": {
           "view": "timeSeries",
           "stacked": false,
           "metrics": [
             [
               "AWS/Lambda",
               "Duration",
               "FunctionName",
               "telementry-events-ingestion-api-handler",
               { "stat": "p50", "region": "us-east-2" }
             ],
             ["...", { "stat": "p90", "region": "us-east-2" }],
             ["...", { "stat": "p99", "region": "us-east-2" }],
             [".", "Errors", ".", ".", { "region": "us-east-2" }],
             [".", "Throttles", ".", ".", { "region": "us-east-2" }],
             [".", "ConcurrentExecutions", ".", ".", { "region": "us-east-2" }]
           ],
           "region": "us-east-2",
           "title": "Handler Performance",
           "period": 300
         }
       },
       {
         "type": "metric",
         "x": 9,
         "y": 9,
         "width": 8,
         "height": 10,
         "properties": {
           "view": "timeSeries",
           "stacked": false,
           "metrics": [
             ["TelemetryService", "HandlerTimeouts", { "region": "us-east-2" }],
             [".", "FirehoseErrors", { "region": "us-east-2" }],
             [".", "ValidationFailures", { "region": "us-east-2" }],
             [".", "GlobalErrors", { "region": "us-east-2" }]
           ],
           "region": "us-east-2",
           "title": "Handler Custom Metrics",
           "period": 300
         }
       },
       {
         "height": 10,
         "width": 9,
         "y": 19,
         "x": 0,
         "type": "metric",
         "properties": {
           "view": "timeSeries",
           "stacked": false,
           "metrics": [
             [
               "AWS/Lambda",
               "Duration",
               "FunctionName",
               "telementry-events-ingestion-api-authorizer",
               { "stat": "p50", "region": "us-east-2" }
             ],
             ["...", { "stat": "p90", "region": "us-east-2" }],
             ["...", { "stat": "p99", "region": "us-east-2" }],
             [".", "Errors", ".", ".", { "region": "us-east-2" }],
             [".", "Throttles", ".", ".", { "region": "us-east-2" }],
             [".", "ConcurrentExecutions", ".", ".", { "region": "us-east-2" }]
           ],
           "region": "us-east-2",
           "title": "Authorizer Performance",
           "period": 300
         }
       },
       {
         "type": "metric",
         "x": 9,
         "y": 19,
         "width": 8,
         "height": 10,
         "properties": {
           "view": "timeSeries",
           "stacked": false,
           "metrics": [
             [
               "TelemetryService",
               "AuthorizerTimeouts",
               { "region": "us-east-2" }
             ],
             [".", "AuthFailures", { "region": "us-east-2" }]
           ],
           "region": "us-east-2",
           "title": "Authorizer Custom Metrics",
           "period": 300
         }
       },
       {
         "type": "metric",
         "x": 0,
         "y": 0,
         "width": 17,
         "height": 9,
         "properties": {
           "view": "timeSeries",
           "stacked": false,
           "metrics": [
             [
               "AWS/ApiGateway",
               "Count",
               "ApiName",
               "telemetry-events-ingestion-api",
               "Stage",
               "v1"
             ],
             [".", "4XXError", ".", ".", ".", "."],
             [".", "5XXError", ".", ".", ".", "."],
             [".", "Latency", ".", ".", ".", ".", { "stat": "p50" }],
             ["...", { "stat": "p90" }],
             ["...", { "stat": "p99" }],
             [".", "IntegrationLatency", ".", ".", ".", ".", { "stat": "p50" }],
             ["...", { "stat": "p90" }],
             ["...", { "stat": "p99" }]
           ],
           "region": "us-east-2",
           "title": "API Gateway Metrics",
           "period": 300
         }
       }
     ]
   }
   ```

### 8. Testing

1. Test Authorization:

   Go to: https://us-east-1.console.aws.amazon.com/apigateway/main/apis/87qqbbajy9/authorizers/ki88l0?api=87qqbbajy9&experience=rest&region=us-east-1

   Use a token from braintrust.dev (see network tab. search for `data_plane` api request).

   You should see a 200 & the context is attached (esp. the `orgIds`).

2. Test Event Ingestion:

   ```bash
   curl -X POST \
     https://87qqbbajy9.execute-api.us-east-1.amazonaws.com/v1 \
     -H 'Authorization: Bearer YOUR_TOKEN' \
     -H 'Content-Type: application/json' \
     -d '{
    "events": [
        {
            "event_name": "LogInsertedEvent",
            "idempotency_key": "[\"2857d2cc-c738-48f5-9eb8-f9381f02a5a9\",\"project_logs\",\"2ae3aa4a-d8be-479f-8e5f-627eb60672ce\",\"1000194535869775896\"]",
            "timestamp": "2025-02-04T04:00:14.748Z",
            "external_customer_id": "eaacb1d9-bded-437b-a02a-7c4e369730a2",
            "properties": {
                "object_type": "project_logs",
                "object_id": "2ae3aa4a-d8be-479f-8e5f-627eb60672ce",
                "org_id": "eaacb1d9-bded-437b-a02a-7c4e369730a2",
                "row_id": "2857d2cc-c738-48f5-9eb8-f9381f02a5a9",
                "log_bytes": 646,
                "project_id": "2ae3aa4a-d8be-479f-8e5f-627eb60672ce",
                "xact_id": "1000194535869775896",
                "span_attributes_type": "task",
                "span_id": "8db4514d-abcc-4d5b-9bf5-a916b61c3011",
                "root_span_id": "8db4514d-abcc-4d5b-9bf5-a916b61c3011",
                "span_parents_csv": "__UNSET__",
                "user_id": "fac36c53-c882-458b-bf80-60d06c3e8a0d",
                "app_origin": "http://localhost:3000"
            }
        }
    ]
   }'
   ```

3. Verify Data Flow:
   ```
   1. Check CloudWatch logs for both Lambdas
   2. Wait ~15 minutes for Firehose buffer
   3. Check S3 bucket for new files:
      Path: vendor=orb/year=YYYY/month=MM/day=DD/hour=HH/
   ```
