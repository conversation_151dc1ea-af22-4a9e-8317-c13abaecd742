create table "public"."brainstore_global_store_root_span_id_to_segment_id" (
    "root_span_id" text not null,
    "segment_id" uuid not null
);


create table "public"."brainstore_global_store_row_id_to_segment_id" (
    "row_id" text not null,
    "segment_id" uuid not null
);


CREATE INDEX brainstore_global_store_root_span_id_to_segment__segment_id_idx ON public.brainstore_global_store_root_span_id_to_segment_id USING btree (segment_id);

CREATE UNIQUE INDEX brainstore_global_store_root_span_id_to_segment_id_pkey ON public.brainstore_global_store_root_span_id_to_segment_id USING btree (root_span_id, segment_id);

CREATE UNIQUE INDEX brainstore_global_store_row_id_to_segment_id_pkey ON public.brainstore_global_store_row_id_to_segment_id USING btree (row_id, segment_id);

CREATE INDEX brainstore_global_store_row_id_to_segment_id_segment_id_idx ON public.brainstore_global_store_row_id_to_segment_id USING btree (segment_id);

alter table "public"."brainstore_global_store_root_span_id_to_segment_id" add constraint "brainstore_global_store_root_span_id_to_segment_id_pkey" PRIMARY KEY using index "brainstore_global_store_root_span_id_to_segment_id_pkey";

alter table "public"."brainstore_global_store_row_id_to_segment_id" add constraint "brainstore_global_store_row_id_to_segment_id_pkey" PRIMARY KEY using index "brainstore_global_store_row_id_to_segment_id_pkey";
