-- Initial migration, which is the current state of the DB at the time we
-- installed the migration system. Production consumers should not need to apply
-- this migration when we deploy the migrations system, but if we are
-- reconstructing the DB from scratch locally we may want to run this.

CREATE TABLE logs (
    sequence_id serial primary key
  , row_created timestamp with time zone default current_timestamp
  , data jsonb

  , scores jsonb GENERATED ALWAYS AS (data->'scores') STORED

  , id text GENERATED ALWAYS AS (data->>'id') STORED
  , span_id text GENERATED ALWAYS AS (data->>'span_id') STORED
  , root_span_id text GENERATED ALWAYS AS (data->>'root_span_id') STORED
  , _xact_id bigint NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_xact_id')::bigint, 0)) STORED
  , _object_delete boolean NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_object_delete')::boolean, false)) STORED
  , created text GENERATED ALWAYS AS (data->>'created') STORED

  , project_id text GENERATED ALWAYS AS (data->>'project_id') STORED
  , experiment_id text GENERATED ALWAYS AS (data->>'experiment_id') STORED
  , dataset_id text GENERATED ALWAYS AS (data->>'dataset_id') STORED
  , prompt_session_id text GENERATED ALWAYS AS (data->>'prompt_session_id') STORED

  , _data_object_concat_id text GENERATED ALWAYS AS (
        COALESCE(data->>'experiment_id', '') || ':'
        || COALESCE(data->>'dataset_id', '') || ':'
        || COALESCE(data->>'prompt_session_id', '')) STORED
);

-- We should not have multiple versions of the same row in the same data
-- collection logged in the same transaction.
CREATE UNIQUE INDEX ON logs (_data_object_concat_id, id, _xact_id DESC);

-- For scanning data from a particular object.
CREATE INDEX ON logs (project_id, id, _xact_id desc);
CREATE INDEX ON logs (experiment_id, id, _xact_id desc);
CREATE INDEX ON logs (dataset_id, id, _xact_id desc);
CREATE INDEX ON logs (prompt_session_id, id, _xact_id desc);
