drop index if exists "public"."brainstore_global_store_segme_is_compacted_segment_id_xact__idx";

CREATE INDEX brainstore_global_store_segme_segment_id_xact_id_wal_filen_idx1 ON public.brainstore_global_store_segment_id_to_wal_entries USING btree (segment_id, xact_id, wal_filename) WHERE is_compacted;

CREATE INDEX brainstore_global_store_segme_segment_id_xact_id_wal_filena_idx ON public.brainstore_global_store_segment_id_to_wal_entries USING btree (segment_id, xact_id, wal_filename) WHERE (NOT is_compacted);

CREATE INDEX brainstore_global_store_segment_id__segment_id_wal_filename_idx ON public.brainstore_global_store_segment_id_to_wal_entries USING btree (segment_id, wal_filename);
