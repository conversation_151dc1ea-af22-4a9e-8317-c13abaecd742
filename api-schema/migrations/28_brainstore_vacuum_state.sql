alter table "public"."brainstore_global_store_segment_id_to_liveness" add column "last_written_ts" timestamp with time zone default '1980-01-01'::timestamptz;

alter table "public"."brainstore_global_store_segment_id_to_liveness" add column "vacuum_index_last_successful_start_ts" timestamp with time zone default '1970-01-01'::timestamptz;

alter table "public"."brainstore_global_store_segment_id_to_liveness" add column "vacuum_index_info" jsonb;

create index on public.brainstore_global_store_segment_id_to_liveness(is_live, (vacuum_index_last_successful_start_ts - last_written_ts));

update "public"."brainstore_global_store_segment_id_to_liveness"
set last_written_ts = '1980-01-01'::timestamptz
where last_written_ts is null;

update "public"."brainstore_global_store_segment_id_to_liveness"
set vacuum_index_last_successful_start_ts = '1970-01-01'::timestamptz
where vacuum_index_last_successful_start_ts is null;

alter table "public"."brainstore_global_store_segment_id_to_liveness"
alter column "last_written_ts" set not null,
alter column "vacuum_index_last_successful_start_ts" set not null;
