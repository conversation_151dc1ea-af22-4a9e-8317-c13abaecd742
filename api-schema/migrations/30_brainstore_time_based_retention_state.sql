create table brainstore_global_store_time_based_retention_state(
    id text primary key default 'singleton' check (id = 'singleton'),
    last_successful_start_ts timestamp with time zone,
    current_op_start_ts timestamp with time zone,
    object_id_cursor text,
    segment_id_cursor uuid,
    operation jsonb
);

-- For paginating through (object_id, segment_id) pairs to apply retention policies.
create index on brainstore_global_store_segment_id_to_liveness(object_id, segment_id) where is_live;
