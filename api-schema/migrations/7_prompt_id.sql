drop index if exists "public"."logs_make_object_id_id__xact_id_idx";
drop index if exists "public"."comments_make_object_id_id__xact_id_idx";
drop index if exists "public"."comments_make_object_id_origin_id__xact_id_idx";
drop index if exists "public"."logs_make_object_id_row_idx";
drop index if exists "public"."logs_make_object_id_coalesce_idx";

CREATE OR REPLACE FUNCTION make_object_id(
    project_id text, experiment_id text, dataset_id text, prompt_session_id text, log_id text)
RETURNS text
LANGUAGE SQL
AS $func$
SELECT COALESCE(
    'experiment:' || experiment_id,
    'dataset:' || dataset_id,
    'prompt_session:' || prompt_session_id,
    CASE
        WHEN log_id = 'g' THEN 'global_log:' || project_id
        WHEN log_id = 'p' THEN 'prompt:' || project_id
        ELSE 'log:' || log_id
    END)
$func$;

CREATE UNIQUE INDEX "logs_make_object_id_id__xact_id_idx" ON logs (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    id, _xact_id DESC);

CREATE UNIQUE INDEX "comments_make_object_id_id__xact_id_idx" ON comments (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    id, _xact_id DESC);

CREATE INDEX "comments_make_object_id_origin_id__xact_id_idx" ON comments (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    origin_id, _xact_id DESC);

CREATE INDEX "logs_make_object_id_row_idx" ON logs (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC)
    WHERE NOT _object_delete;

CREATE INDEX "logs_make_object_id_coalesce_idx" ON logs (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    (COALESCE(root_span_id, id)));
