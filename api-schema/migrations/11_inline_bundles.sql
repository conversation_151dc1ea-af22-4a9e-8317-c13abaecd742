DROP TABLE IF EXISTS code_bundles;
CREATE TABLE code_bundles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id text NOT NULL,
    user_id text NOT NULL,
    created_at timestamp NOT NULL DEFAULT now(),
    path text NOT NULL,
    code_hash text NOT NULL,
    lambda_function_arn text REFERENCES lambda_functions(arn) ON DELETE CASCADE,
    version text
);

CREATE UNIQUE INDEX code_bundles_path ON code_bundles (path);
