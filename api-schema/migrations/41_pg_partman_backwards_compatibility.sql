CREATE OR REPLACE FUNCTION public.setup_logs2_partman(p_schedule_cron_job boolean)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    _pg_partman_v_major int;
BEGIN
    -- Make sure the extension is installed and the schema exists.
    if not exists (select 1 from pg_extension where extname = 'pg_partman') then
        raise exception 'pg_partman extension not installed';
    end if;
    if not exists (select 1 from pg_namespace where nspname = 'partman') then
        raise exception 'partman schema not created';
    end if;

    -- Older versions of pg_partman < 5.0 require an explicit setting of
    -- `pg_type => 'native'`. In >= 5.0, `pg_type` means something entirely
    -- different so we have to omit it.
    select split_part(extversion, '.', 1)::int into _pg_partman_v_major
    from pg_extension
    where extname = 'pg_partman';

    if not exists (select 1 from partman.part_config where parent_table = 'public.logs2') then
      if _pg_partman_v_major >= 5 then
        perform partman.create_parent(
            p_parent_table => 'public.logs2',
            p_control => 'sequence_id',
            p_interval => '1000000000',
            p_template_table => 'public.logs2_template'
        );
      else
        perform partman.create_parent(
            p_parent_table => 'public.logs2',
            p_control => 'sequence_id',
            p_type => 'native',
            p_interval => '1000000000',
            p_template_table => 'public.logs2_template'
        );
      end if;
    end if;
    if p_schedule_cron_job then
        if not exists (select 1 from pg_namespace where nspname = 'cron') then
            raise exception 'cron schema not created';
        end if;
        if not exists (select 1 from cron.job where jobname = 'partman_maintenance_f0fdffe1') then
            perform cron.schedule(
                'partman_maintenance_f0fdffe1',
                '0 * * * *',
                'call partman.run_maintenance_proc();'
            );
        end if;
    end if;
END;
$function$
;
