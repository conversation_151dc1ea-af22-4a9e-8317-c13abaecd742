CREATE TYPE function_secret_object_type AS ENUM ('organization', 'project', 'function');

DROP TABLE IF EXISTS function_secrets;
CREATE TABLE function_secrets (
    id uuid not null primary key default gen_random_uuid(),
    object_type function_secret_object_type NOT NULL,
    object_id TEXT NOT NULL,
    secret_name TEXT NOT NULL,
    secret_value TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT now(),
    used_at TIMESTAMP NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX function_secrets_object_type_object_id_idx ON function_secrets (object_type, object_id, secret_name);
