alter table "public"."brainstore_global_store_segment_id_to_liveness" add column "derived_earliest_uncompacted_xact_id" bigint;

CREATE UNIQUE INDEX brainstore_global_store_segme_derived_earliest_uncompacted__idx ON public.brainstore_global_store_segment_id_to_liveness USING btree (derived_earliest_uncompacted_xact_id DESC, segment_id DESC) WHERE (is_live AND (derived_earliest_uncompacted_xact_id IS NOT NULL));

CREATE UNIQUE INDEX brainstore_global_store_segment_id_to_li_is_live_segment_id_idx ON public.brainstore_global_store_segment_id_to_liveness USING btree (is_live, segment_id);
