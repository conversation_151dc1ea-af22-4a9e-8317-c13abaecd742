drop index if exists "public"."logs__data_object_concat_id_id_span_id__xact_id_idx";

alter table "public"."logs" drop column "_data_object_concat_id";
alter table "public"."logs"
        add column "_data_object_concat_id" text GENERATED ALWAYS AS (
                COALESCE(data->>'org_id', '') || ':'
                || COALESCE(data->>'project_id', '') || ':'
                || COALESCE(data->>'experiment_id', '') || ':'
                || COALESCE(data->>'dataset_id', '') || ':'
                || COALESCE(data->>'prompt_session_id', '') || ':'
                || COALESCE(data->>'log_id', '')) STORED
        , add column "log_id" text generated always as ((data ->> 'log_id'::text)) stored
        , add column "org_id" text generated always as ((data ->> 'org_id'::text)) stored;

CREATE UNIQUE INDEX logs__data_object_concat_id_id__xact_id_idx ON public.logs USING btree (_data_object_concat_id, id, _xact_id DESC);

CREATE INDEX logs_project_id_id__xact_id_idx1 ON public.logs USING btree (project_id, id, _xact_id DESC) WHERE (log_id = 'g'::text);
CREATE INDEX logs_org_id_id__xact_id_idx ON public.logs USING btree (org_id, id, _xact_id DESC) WHERE (log_id = 'g'::text);

CREATE INDEX logs_data_idx ON public.logs USING gin (data);

CREATE INDEX logs_org_id__xact_id_root_span_id_idx ON public.logs USING btree (org_id, _xact_id DESC, root_span_id DESC) WHERE (log_id = 'g'::text);
CREATE INDEX logs_org_id_root_span_id_idx ON public.logs USING btree (org_id, root_span_id) WHERE (log_id = 'g'::text);

CREATE INDEX logs_project_id__xact_id_root_span_id_idx ON public.logs USING btree (project_id, _xact_id DESC, root_span_id DESC) WHERE (log_id = 'g'::text);
CREATE INDEX logs_project_id_root_span_id_idx ON public.logs USING btree (project_id, root_span_id) WHERE (log_id = 'g'::text);
