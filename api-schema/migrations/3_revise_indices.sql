drop index if exists "public"."logs__data_object_concat_id_id__xact_id_idx";

drop index if exists "public"."logs_dataset_id_id__xact_id_idx";

drop index if exists "public"."logs_experiment_id_id__xact_id_idx";

drop index if exists "public"."logs_org_id__xact_id_root_span_id_idx";

drop index if exists "public"."logs_org_id_root_span_id_idx";

drop index if exists "public"."logs_project_id__xact_id_root_span_id_idx";

drop index if exists "public"."logs_project_id_id__xact_id_idx1";

drop index if exists "public"."logs_project_id_root_span_id_idx";

drop index if exists "public"."logs_prompt_session_id_id__xact_id_idx";

create type "public"."_xact_id_root_span_id" as ("_xact_id" bigint, "root_span_id" text);

CREATE OR REPLACE FUNCTION public.make_object_id(project_id text, experiment_id text, dataset_id text, prompt_session_id text, log_id text)
 RETURNS text
 LANGUAGE sql
AS $function$
SELECT COALESCE(
    'experiment:' || experiment_id,
    'dataset:' || dataset_id,
    'prompt_session:' || prompt_session_id,
    'log:' || NULLIF(log_id, 'g'),
    'global_log:' || project_id)
$function$
;

CREATE INDEX logs_make_object_id_coalesce_idx ON public.logs USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), COALESCE(root_span_id, id));

CREATE UNIQUE INDEX logs_make_object_id_id__xact_id_idx ON public.logs USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), id, _xact_id DESC);

CREATE INDEX logs_make_object_id_row_idx ON public.logs USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC);

CREATE INDEX logs_org_id_coalesce_idx ON public.logs USING btree (org_id, COALESCE(root_span_id, id)) WHERE (log_id = 'g'::text);

CREATE INDEX logs_org_id_row_idx ON public.logs USING btree (org_id, (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC) WHERE (log_id = 'g'::text);
