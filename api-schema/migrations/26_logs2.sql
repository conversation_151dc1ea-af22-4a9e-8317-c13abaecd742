-- Create logs2 table.

create table "public"."logs2" (
    "sequence_id" bigserial primary key,
    "row_created" timestamp with time zone default CURRENT_TIMESTAMP,
    "data" jsonb,
    "audit_data" jsonb,
    "scores" jsonb generated always as ((data -> 'scores'::text)) stored,
    "id" text generated always as ((data ->> 'id'::text)) stored,
    "span_id" text generated always as ((data ->> 'span_id'::text)) stored,
    "root_span_id" text generated always as ((data ->> 'root_span_id'::text)) stored,
    "_xact_id" bigint not null generated always as (COALESCE(((data ->> '_xact_id'::text))::bigint, (0)::bigint)) stored,
    "_object_delete" boolean not null generated always as (COALESCE(((data ->> '_object_delete'::text))::boolean, false)) stored,
    "created" text generated always as ((data ->> 'created'::text)) stored,
    "org_id" text generated always as ((data ->> 'org_id'::text)) stored,
    "project_id" text generated always as ((data ->> 'project_id'::text)) stored,
    "experiment_id" text generated always as ((data ->> 'experiment_id'::text)) stored,
    "dataset_id" text generated always as ((data ->> 'dataset_id'::text)) stored,
    "prompt_session_id" text generated always as ((data ->> 'prompt_session_id'::text)) stored,
    "log_id" text generated always as ((data ->> 'log_id'::text)) stored
) partition by RANGE (sequence_id);


create table "public"."logs2_template" (
    "sequence_id" bigint not null,
    "row_created" timestamp with time zone,
    "data" jsonb,
    "audit_data" jsonb,
    "scores" jsonb,
    "id" text,
    "span_id" text,
    "root_span_id" text,
    "_xact_id" bigint not null,
    "_object_delete" boolean not null,
    "created" text,
    "org_id" text,
    "project_id" text,
    "experiment_id" text,
    "dataset_id" text,
    "prompt_session_id" text,
    "log_id" text
);

CREATE INDEX logs2_make_object_id_coalesce_idx ON public.logs2 USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), COALESCE(root_span_id, id));

CREATE INDEX logs2_make_object_id_row_idx ON public.logs2 USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC) WHERE (NOT _object_delete);

CREATE INDEX logs2_org_id_coalesce_idx ON public.logs2 USING btree (org_id, COALESCE(root_span_id, id)) WHERE (log_id = 'g'::text);

CREATE INDEX logs2_org_id_id__xact_id_idx ON public.logs2 USING btree (org_id, id, _xact_id DESC) WHERE (log_id = 'g'::text);

CREATE INDEX logs2_org_id_row_idx ON public.logs2 USING btree (org_id, (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC) WHERE ((log_id = 'g'::text) AND (NOT _object_delete));

CREATE INDEX logs2_project_id_id__xact_id_idx ON public.logs2 USING btree (project_id, id, _xact_id DESC);

CREATE UNIQUE INDEX logs2_template_make_object_id_id__xact_id_idx ON public.logs2_template USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), id, _xact_id DESC);

-- Update backfill global state table.

alter table "public"."brainstore_backfill_global_state" add column "frontier_sequence_id_2" bigint;

alter table "public"."brainstore_backfill_global_state" add column "historical_full_backfill_sequence_id_2" bigint;

-- Set any existing sequence ID columns to 0 if null.
update "public"."brainstore_backfill_global_state" set
    "frontier_sequence_id" = coalesce("frontier_sequence_id", 0),
    "frontier_sequence_id_2" = coalesce("frontier_sequence_id_2", 0),
    "historical_full_backfill_sequence_id" = coalesce("historical_full_backfill_sequence_id", 0),
    "historical_full_backfill_sequence_id_2" = coalesce("historical_full_backfill_sequence_id_2", 0),
    "comments_backfill_sequence_id" = coalesce("comments_backfill_sequence_id", 0);

-- Set the sequence ID columns to not null.
alter table "public"."brainstore_backfill_global_state" alter column "frontier_sequence_id" set not null;
alter table "public"."brainstore_backfill_global_state" alter column "frontier_sequence_id_2" set not null;
alter table "public"."brainstore_backfill_global_state" alter column "historical_full_backfill_sequence_id" set not null;
alter table "public"."brainstore_backfill_global_state" alter column "historical_full_backfill_sequence_id_2" set not null;
alter table "public"."brainstore_backfill_global_state" alter column "comments_backfill_sequence_id" set not null;

-- Update backfill tracked objects table.

drop index if exists "public"."brainstore_backfill_tracked_o_case_expr_is_backfilling_last_idx";

alter table "public"."brainstore_backfill_tracked_objects" add column "initial_sequence_id_2" bigint;

alter table "public"."brainstore_backfill_tracked_objects" add column "last_backfilled_ts_2" timestamp with time zone;

alter table "public"."brainstore_backfill_tracked_objects" add column "last_encountered_sequence_id_2" bigint;

alter table "public"."brainstore_backfill_tracked_objects" add column "last_processed_sequence_id_2" bigint;

-- Backfill the sequence ID tracking columns for logs2 to all 0 before marking them not null.

update "public"."brainstore_backfill_tracked_objects" set
    "initial_sequence_id_2" = 0,
    "last_encountered_sequence_id_2" = 0,
    "last_processed_sequence_id_2" = 0;

alter table "public"."brainstore_backfill_tracked_objects" alter column "initial_sequence_id_2" set not null;

alter table "public"."brainstore_backfill_tracked_objects" alter column "last_encountered_sequence_id_2" set not null;

alter table "public"."brainstore_backfill_tracked_objects" alter column "last_processed_sequence_id_2" set not null;

-- Recreate indices on the backfill tracked objects table.

CREATE INDEX brainstore_backfill_tracked_o_expr_expr1_last_processed_se_idx1 ON public.brainstore_backfill_tracked_objects USING btree (((last_processed_sequence_id_2 < last_encountered_sequence_id_2)), ((completed_initial_backfill_ts IS NULL)), last_processed_sequence_id_2, last_encountered_sequence_id_2) WHERE is_backfilling;

CREATE INDEX brainstore_backfill_tracked_o_expr_expr1_last_processed_seq_idx ON public.brainstore_backfill_tracked_objects USING btree (((last_processed_sequence_id < last_encountered_sequence_id)), ((completed_initial_backfill_ts IS NULL)), last_processed_sequence_id, last_encountered_sequence_id) WHERE is_backfilling;

CREATE INDEX brainstore_backfill_tracked_objects_expr_idx ON public.brainstore_backfill_tracked_objects USING btree ((1)) WHERE (is_backfilling AND (completed_initial_backfill_ts IS NULL));

CREATE INDEX brainstore_backfill_tracked_objects_expr_idx1 ON public.brainstore_backfill_tracked_objects USING btree ((1)) WHERE (is_backfilling AND (completed_initial_backfill_ts IS NULL) AND (last_processed_sequence_id >= last_encountered_sequence_id) AND (last_processed_sequence_id_2 >= last_encountered_sequence_id_2));
