-- Launch a cron job which iteratively de-duplicates WAL entries from the
-- brainstore_global_store_segment_id_to_wal_entries table. We insert the job
-- info into the 'run_migration_results' table so that we can monitor the status
-- of the backfill.
insert into run_migration_results(job_name, job_id)
select
    'deduplicate_wal_entries_b591f882',
    cron.schedule(
        'deduplicate_wal_entries_b591f882',
        '* * * * *',
        $$
            with
            wal_entries_with_duplicates as (
                select segment_id, xact_id
                from brainstore_global_store_segment_id_to_wal_entries
                group by 1, 2, (byte_range_end - byte_range_start), digest
                having count(*) > 1
                limit 100000
            ),
            distinct_wal_entries as (
                select distinct on (segment_id, xact_id, byte_length, digest)
                    segment_id, xact_id, (byte_range_end - byte_range_start) byte_length, digest, wal_filename
                from brainstore_global_store_segment_id_to_wal_entries
                    join wal_entries_with_duplicates using (segment_id, xact_id)
                order by segment_id, xact_id, byte_length, digest
            ),
            redundant_wal_entries as (
                select segment_id, xact_id, wal_filename
                from brainstore_global_store_segment_id_to_wal_entries
                    join wal_entries_with_duplicates using (segment_id, xact_id)
                where (segment_id, xact_id, wal_filename) not in (
                    select segment_id, xact_id, wal_filename from distinct_wal_entries
                )
            ),
            deleted_wal_entries as (
                delete from brainstore_global_store_segment_id_to_wal_entries
                using redundant_wal_entries
                where
                    (brainstore_global_store_segment_id_to_wal_entries.segment_id, brainstore_global_store_segment_id_to_wal_entries.xact_id, brainstore_global_store_segment_id_to_wal_entries.wal_filename) =
                        (redundant_wal_entries.segment_id, redundant_wal_entries.xact_id, redundant_wal_entries.wal_filename)
                returning 1
            )
            update run_migration_results
            set message = (
                select case
                    when (select count(*) from deleted_wal_entries) > 0 then ('Deleted ' || ((select count(*) from deleted_wal_entries)::text) || ' duplicates')
                    when (select cron.unschedule('deduplicate_wal_entries_b591f882')) then 'No more duplicates found, unscheduled cron job'
                    else 'Failed to unschedule cron job'
                end
            )
            where job_name = 'deduplicate_wal_entries_b591f882';
        $$
    )
;
