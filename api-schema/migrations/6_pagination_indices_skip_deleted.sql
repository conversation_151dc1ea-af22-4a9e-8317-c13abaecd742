drop index if exists "public"."logs_make_object_id_row_idx";

drop index if exists "public"."logs_org_id_row_idx";

CREATE INDEX logs_make_object_id_row_idx ON public.logs USING btree (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id), (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC) WHERE (NOT _object_delete);

CREATE INDEX logs_org_id_row_idx ON public.logs USING btree (org_id, (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC) WHERE ((log_id = 'g'::text) AND (NOT _object_delete));
