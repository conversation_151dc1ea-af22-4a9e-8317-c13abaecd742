drop index if exists "public"."brainstore_backfill_tracked_o_case_is_backfilling_last_proc_idx";

CREATE INDEX brainstore_backfill_tracked_o_case_expr_is_backfilling_last_idx ON public.brainstore_backfill_tracked_objects USING btree ((
CASE
    WHEN (last_processed_sequence_id < last_encountered_sequence_id) THEN '-1'::integer
    WHEN (last_processed_sequence_id > last_encountered_sequence_id) THEN 1
    ELSE 0
END), ((completed_initial_backfill_ts IS NULL)), is_backfilling, last_processed_sequence_id, last_encountered_sequence_id);
