create table "public"."brainstore_global_store_segment_id_to_column_statistics" (
    "segment_id" uuid not null,
    "field_name" text not null,
    "min_u64" numeric(20,0),
    "max_u64" numeric(20,0)
);


alter table "public"."brainstore_global_store_segment_id_to_metadata" alter column "minimum_pagination_key" set data type numeric(20,0) using "minimum_pagination_key"::numeric(20,0);

CREATE INDEX brainstore_global_store_segme_segment_id_field_name_max_u64_idx ON public.brainstore_global_store_segment_id_to_column_statistics USING btree (segment_id, field_name, max_u64);

CREATE INDEX brainstore_global_store_segme_segment_id_field_name_min_u64_idx ON public.brainstore_global_store_segment_id_to_column_statistics USING btree (segment_id, field_name, min_u64);

CREATE UNIQUE INDEX brainstore_global_store_segment_id_to_column_statistics_pkey ON public.brainstore_global_store_segment_id_to_column_statistics USING btree (segment_id, field_name);

alter table "public"."brainstore_global_store_segment_id_to_column_statistics" add constraint "brainstore_global_store_segment_id_to_column_statistics_pkey" PRIMARY KEY using index "brainstore_global_store_segment_id_to_column_statistics_pkey";
