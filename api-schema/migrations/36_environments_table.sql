-- table to store the different environments (e.g., dev, staging, prod).
create table environments (
  id uuid primary key default gen_random_uuid(),
  org_id text not null,
  name text not null,

  -- a url-friendly, unique identifier for the environment within an organization.
  slug text not null,

  description text,
  created timestamp with time zone default current_timestamp,
  deleted_at timestamp with time zone
);

create unique index environments_org_id_slug_deleted_at_idx
on environments (org_id, slug, deleted_at) nulls not distinct;

create index idx_environments_org_id on environments (org_id);
