alter table project_columns alter column object_type set data type text using object_type::text;
alter table project_columns alter column subtype set data type text using subtype::text;
alter table project_columns add column variant text;

update project_columns set variant = COALESCE(subtype, object_type)::text;
update project_columns set subtype = '' where subtype is null;

alter table project_columns alter column variant set not null;
alter table project_columns alter column subtype set default '';
alter table project_columns alter column subtype set not null;

drop type if exists project_column_object_type;

drop index if exists project_columns_object_type_object_id_idx;

create unique index project_columns_object_type_object_id_subtype_variant_name_idx on project_columns (object_type, object_id, subtype, variant, column_name);
