-- BEGIN org_prompt_log_rows

-- We are at the point where creating new indices on the 'logs' table is too
-- expensive, so instead, we use this table as an "external" index over the set
-- of prompts for each org.
create table org_prompt_log_rows(
  org_id text not null,
  logs_sequence_id serial not null,
  primary key (org_id, logs_sequence_id)
);

-- Set up a trigger to insert new prompt rows from logs into this table.

create function insert_org_prompt_log_row_trigger_f()
returns trigger
language plpgsql
as $$
begin
  insert into org_prompt_log_rows(org_id, logs_sequence_id)
  values (new.org_id, new.sequence_id)
  on conflict do nothing;
  return new;
end;
$$;

create trigger insert_org_prompt_log_row_trigger
after insert on logs
for each row
when (new.log_id = 'p')
execute function insert_org_prompt_log_row_trigger_f();

-- END org_prompt_log_rows

-- In a separate cron job, we backfill the org_prompt_log_rows table from the
-- existing prompts in 'logs'. We insert the job info into the
-- 'run_migration_results' table so that we can monitor the status of the
-- backfill.
insert into run_migration_results(job_name, job_id)
select
    'backfill_org_prompt_log_rows_ca2e7ecf',
    cron.schedule(
        'backfill_org_prompt_log_rows_ca2e7ecf',
        '* * * * *',
        $$
            insert into org_prompt_log_rows(org_id, logs_sequence_id)
            select org_id, sequence_id from logs
            where logs.log_id = 'p' and logs.org_id is not null and logs.sequence_id is not null
            on conflict do nothing;

            select cron.unschedule('backfill_org_prompt_log_rows_ca2e7ecf');
        $$
    )
;
