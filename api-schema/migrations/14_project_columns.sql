CREATE TYPE project_column_object_type AS ENUM ('project', 'experiment', 'dataset', 'project_log');

DROP TABLE IF EXISTS project_columns;
CREATE TABLE project_columns (
    id uuid not null primary key default gen_random_uuid(),
    object_id TEXT NOT NULL,
    object_type project_column_object_type NOT NULL,
    subtype project_column_object_type,
    column_name TEXT NOT NULL,
    column_expr TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp
);

CREATE UNIQUE INDEX project_columns_object_type_object_id_idx ON project_columns (object_type, object_id, subtype, column_name);
