 -- Associate objects with environments
create table environment_objects (
  id uuid primary key default gen_random_uuid(),
  object_type text not null,
  object_id text not null,
  object_version bigint not null,
  environment_id uuid not null references environments(id),
  created timestamp with time zone default current_timestamp,
  constraint uq_env_objects_env_type_id unique (environment_id, object_type, object_id)
);

create index idx_env_objects_environment_id on environment_objects (environment_id);
create index idx_env_objects_object_type_id on environment_objects (object_type, object_id);
