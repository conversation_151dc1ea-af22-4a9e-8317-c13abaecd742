create extension if not exists pg_cron;

create table run_migration_results(
    id serial primary key,
    job_name text unique,
    job_id bigint,
    message text);

create function run_migration_inner(
    expected_current_migration_id int,
    next_migration_id int,
    stmts text[])
returns text
language plpgsql
as $$
declare
    _stmt text;
    _current_migration_id int;
begin
    lock table migration_version;

    -- Check if the last_migration_id still matches the existing one.
    select last_migration_id
    into strict _current_migration_id
    from migration_version;

    if _current_migration_id <> expected_current_migration_id then
        return format('Skipping migrations: Expected migration id %s does not match actual %s.',
                      expected_current_migration_id, _current_migration_id);
    end if;

    -- Run all the migrations.
    foreach _stmt in array stmts loop
        execute _stmt;
    end loop;

    -- Update the migration version.
    update migration_version set last_migration_id = next_migration_id;

    return 'Success';
end;
$$;

create function run_migration(
    job_name text,
    expected_current_migration_id int,
    next_migration_id int,
    stmts text[],
    result_id integer)
returns void
language plpgsql
as $$
declare
    _result_message text;
begin
    -- Run the migration and capture the result.
    begin
        select run_migration_inner(expected_current_migration_id, next_migration_id, stmts)
        into strict _result_message;
    exception when others then
        _result_message := format('Exception: %s %s', SQLERRM, SQLSTATE);
    end;

    -- Store the result for recordkeeping.
    update run_migration_results
    set message = _result_message
    where id = result_id;

    -- Unschedule the cron job.
    perform cron.unschedule(job_name);
end;
$$;
