alter table "public"."brainstore_global_store_segment_id_to_liveness" add column "vacuum_segment_wal_last_successful_start_ts" timestamp with time zone default '1970-01-01'::timestamptz;

create index on public.brainstore_global_store_segment_id_to_liveness(is_live, (vacuum_segment_wal_last_successful_start_ts - last_written_ts));

update "public"."brainstore_global_store_segment_id_to_liveness"
set vacuum_segment_wal_last_successful_start_ts = '1970-01-01'::timestamptz
where vacuum_segment_wal_last_successful_start_ts is null;

alter table "public"."brainstore_global_store_segment_id_to_liveness"
alter column "vacuum_segment_wal_last_successful_start_ts" set not null;

alter table "public"."brainstore_global_store_segment_id_to_task_info"
add column "vacuum_segment_wal_info" jsonb;
