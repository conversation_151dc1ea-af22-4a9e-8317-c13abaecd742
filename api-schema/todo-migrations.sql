/* These migrations are for WIP features that are not ready-enough to be included in the main migrations. */

DROP TABLE IF EXISTS code_bundles;
CREATE TABLE code_bundles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id text NOT NULL,
    user_id text NOT NULL,
    lambda_function_id uuid,
    runtime_context jsonb NOT NULL,
    path text NOT NULL
);

CREATE UNIQUE INDEX code_bundles_path ON code_bundles (path);
CREATE INDEX code_bundles_lambda_function_id ON code_bundles (lambda_function_id);
