{"$schema": "https://turbo.build/schema.json", "globalEnv": ["BRAINTRUST_APP_URL", "ORG_NAME", "REDIS_HOST", "REDIS_PORT"], "globalPassThroughEnv": ["ORB_API_KEY", "ORB_WEBHOOK_SECRET", "SENTRY_AUTH_TOKEN"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "test": {"dependsOn": ["^build"], "outputs": []}, "lint": {"outputs": []}, "dev": {"cache": false}, "start": {"cache": false}, "clean": {"cache": false}, "watch": {}}, "remoteCache": {"timeout": 600}}