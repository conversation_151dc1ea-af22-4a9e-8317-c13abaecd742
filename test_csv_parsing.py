#!/usr/bin/env python3
"""
Test script to verify CSV parsing works correctly with the longmemeval_m_full.csv file.
This script reads the first few rows and shows how they would be parsed.
"""

import csv
import json
import sys
from typing import Any

# Increase CSV field size limit for large fields
csv.field_size_limit(sys.maxsize)


def try_json(s: str) -> Any:
    """Try to parse a string as JSON, return the string if it fails."""
    try:
        return json.loads(s)
    except (json.JSONDecodeError, TypeError):
        return s


def test_csv_parsing(csv_file_path: str = "longmemeval_m_full.csv", num_rows: int = 3):
    """Test parsing the first few rows of the CSV file."""
    
    print(f"Testing CSV parsing for: {csv_file_path}")
    print(f"Reading first {num_rows} rows...\n")
    
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        print(f"CSV Headers: {reader.fieldnames}")
        print("=" * 80)
        
        for i, row in enumerate(reader):
            if i >= num_rows:
                break
                
            print(f"\nRow {i + 1}:")
            print("-" * 40)
            
            # Parse each field
            input_data = try_json(row['input'])
            expected_data = try_json(row['expected'])
            metadata_data = try_json(row['metadata'])
            
            print(f"Input type: {type(input_data).__name__}")
            if isinstance(input_data, dict):
                print(f"Input keys: {list(input_data.keys())}")
            else:
                print(f"Input preview: {str(input_data)[:100]}...")
            
            print(f"\nExpected type: {type(expected_data).__name__}")
            if isinstance(expected_data, dict):
                print(f"Expected keys: {list(expected_data.keys())}")
            else:
                print(f"Expected preview: {str(expected_data)[:100]}...")
            
            print(f"\nMetadata type: {type(metadata_data).__name__}")
            if isinstance(metadata_data, dict):
                print(f"Metadata keys: {list(metadata_data.keys())}")
            else:
                print(f"Metadata preview: {str(metadata_data)[:100]}...")
            
            print("=" * 80)


if __name__ == "__main__":
    test_csv_parsing()
