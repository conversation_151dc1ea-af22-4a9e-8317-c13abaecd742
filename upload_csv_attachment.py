#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to upload CSV files as attachments to a Braintrust dataset.
This demonstrates how to create a dataset where each row contains a CSV file as an attachment.
"""

import os
import argparse
from braintrust import Attachment, init_dataset


def create_csv_attachment_dataset(project_name: str, dataset_name: str, csv_file_path: str):
    """
    Create a Braintrust dataset with CSV attachments.
    
    Args:
        project_name: Name of the Braintrust project
        dataset_name: Name of the dataset to create
        csv_file_path: Path to the CSV file to attach
    """
    
    # Verify the CSV file exists
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
    
    print(f"Creating dataset '{dataset_name}' in project '{project_name}'...")
    
    # Initialize the dataset
    dataset = init_dataset(project_name, dataset_name)
    
    # Get the filename from the path
    csv_filename = os.path.basename(csv_file_path)
    
    # Create sample rows with CSV attachments
    # In a real scenario, you might have different CSV files for each row
    # or different prompts/tasks related to the same CSV
    
    sample_rows = [
        {
            "input": {
                "task": "Analyze employee performance data and identify top performers",
                "data_file": Attachment(
                    filename=f"performance_data_{csv_filename}",
                    content_type="text/csv",
                    data=csv_file_path
                )
            },
            "expected": "Top performers are employees with performance_score > 8.5"
        },
        {
            "input": {
                "task": "Calculate average salary by department",
                "data_file": Attachment(
                    filename=f"salary_analysis_{csv_filename}",
                    content_type="text/csv", 
                    data=csv_file_path
                )
            },
            "expected": "Engineering: $78,000, Marketing: $57,000, Sales: $69,333, HR: $61,000"
        },
        {
            "input": {
                "task": "Find employees older than 35 years",
                "data_file": Attachment(
                    filename=f"age_filter_{csv_filename}",
                    content_type="text/csv",
                    data=csv_file_path
                )
            },
            "expected": "Bob Smith, David Wilson, Frank Miller, Henry Taylor, Jack Anderson, Liam Garcia, Noah Martinez"
        },
        {
            "input": {
                "task": "Count employees by department",
                "data_file": Attachment(
                    filename=f"department_count_{csv_filename}",
                    content_type="text/csv",
                    data=csv_file_path
                )
            },
            "expected": "Engineering: 5, Marketing: 3, Sales: 3, HR: 3"
        }
    ]
    
    # Insert all rows into the dataset
    for i, row in enumerate(sample_rows, 1):
        print(f"Inserting row {i}/{len(sample_rows)}...")
        dataset.insert(**row)
    
    # Flush to ensure all data is uploaded
    print("Flushing dataset...")
    dataset.flush()
    
    print(f"✅ Successfully created dataset with {len(sample_rows)} rows!")
    print(f"📊 Dataset summary:")
    print(dataset.summarize())
    
    return dataset


def create_csv_with_bytes_example(project_name: str, dataset_name: str):
    """
    Example showing how to create CSV attachments from in-memory data (bytes).
    """
    
    print(f"Creating dataset '{dataset_name}' with in-memory CSV data...")
    
    # Create CSV content as bytes
    csv_content = """product,price,category,stock
iPhone 15,999,Electronics,50
MacBook Pro,2499,Electronics,25
Office Chair,299,Furniture,100
Desk Lamp,79,Furniture,75
Coffee Mug,15,Kitchen,200""".encode('utf-8')
    
    dataset = init_dataset(project_name, dataset_name)
    
    # Insert row with in-memory CSV data
    dataset.insert(
        input={
            "task": "Analyze product inventory and pricing",
            "inventory_data": Attachment(
                filename="product_inventory.csv",
                content_type="text/csv",
                data=csv_content  # Using bytes directly instead of file path
            )
        },
        expected="Electronics have highest average price, Kitchen items have highest stock"
    )
    
    dataset.flush()
    print("✅ Successfully created dataset with in-memory CSV attachment!")
    print(dataset.summarize())
    
    return dataset


def main():
    parser = argparse.ArgumentParser(description="Upload CSV files as attachments to Braintrust dataset")
    parser.add_argument("project_name", help="Name of the Braintrust project")
    parser.add_argument("dataset_name", help="Name of the dataset to create")
    parser.add_argument("--csv-file", default="sample_data.csv", help="Path to CSV file (default: sample_data.csv)")
    parser.add_argument("--in-memory-example", action="store_true", help="Also create an example with in-memory CSV data")
    
    args = parser.parse_args()
    
    try:
        # Create dataset with CSV file attachment
        dataset = create_csv_attachment_dataset(
            args.project_name, 
            args.dataset_name, 
            args.csv_file
        )
        
        # Optionally create in-memory example
        if args.in_memory_example:
            print("\n" + "="*50)
            print("Creating additional example with in-memory CSV data...")
            create_csv_with_bytes_example(
                args.project_name,
                f"{args.dataset_name}_inmemory"
            )
        
        print(f"\n🎉 All done! You can now use these datasets in the Braintrust playground.")
        print(f"📝 The CSV attachments will be available for each row when running prompts.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
