"""
Example demonstrating cursor-based pagination with init_limited_dataset.

This script shows how to:
1. Fetch a limited number of records
2. Get the cursor for the next page
3. Use the cursor to fetch the next batch of records
4. Continue pagination until all records are processed
"""

from init_limited_dataset import init_limited_dataset

def paginate_dataset(project_name: str, dataset_name: str, page_size: int = 5):
    """
    Demonstrate pagination through a dataset using cursors.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        page_size: Number of records per page
    """
    
    print(f"=== Paginating through dataset '{dataset_name}' with page size {page_size} ===\n")
    
    cursor = None
    page_number = 1
    total_records = 0
    
    while True:
        print(f"--- Page {page_number} ---")
        
        # Fetch the next page
        dataset = init_limited_dataset(
            project=project_name,
            name=dataset_name,
            max_records=page_size,
            cursor=cursor  # Start from where we left off
        )
        
        # Collect records from this page
        records = []
        for record in dataset:
            records.append(record)
        
        # Display page info
        print(f"Records in this page: {len(records)}")
        total_records += len(records)
        
        # Show first record as example (if any)
        if records:
            print(f"First record: {records[0]}")
        
        # Check if there are more pages
        print(f"Has more data: {dataset.has_more}")
        if dataset.cursor:
            print(f"Next cursor: {dataset.cursor[:50]}...")  # Show first 50 chars
        else:
            print("Next cursor: None")
        
        print()
        
        # Break if no more data
        if not dataset.has_more:
            break
        
        # Get cursor for next page
        cursor = dataset.cursor
        page_number += 1
        
        # Safety check to avoid infinite loops
        if page_number > 100:
            print("Stopping after 100 pages for safety")
            break
    
    print(f"=== Pagination complete ===")
    print(f"Total pages: {page_number}")
    print(f"Total records: {total_records}")


def fetch_specific_page(project_name: str, dataset_name: str, target_page: int, page_size: int = 5):
    """
    Fetch a specific page by iterating through cursors.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        target_page: Which page to fetch (1-indexed)
        page_size: Number of records per page
    """
    
    print(f"=== Fetching page {target_page} ===\n")
    
    cursor = None
    current_page = 1
    
    while current_page <= target_page:
        dataset = init_limited_dataset(
            project=project_name,
            name=dataset_name,
            max_records=page_size,
            cursor=cursor
        )
        
        if current_page == target_page:
            # This is the page we want
            records = []
            for record in dataset:
                records.append(record)
            
            print(f"Page {target_page} contains {len(records)} records:")
            for i, record in enumerate(records):
                print(f"  Record {i + 1}: {record}")
            
            return records
        
        # Skip this page, just get the cursor
        list(dataset)  # Force fetching to get cursor
        
        if not dataset.has_more:
            print(f"Dataset only has {current_page - 1} pages, cannot fetch page {target_page}")
            return []
        
        cursor = dataset.cursor
        current_page += 1
    
    return []


def simple_pagination_example():
    """Simple example showing basic pagination usage."""
    
    print("=== Simple Pagination Example ===\n")
    
    # First page
    print("Fetching first 3 records...")
    dataset = init_limited_dataset(
        project="pedro-project1",
        name="themes",
        max_records=3
    )
    
    records = list(dataset)
    print(f"Got {len(records)} records")
    print(f"Has more: {dataset.has_more}")
    print(f"Cursor: {dataset.cursor[:30] if dataset.cursor else None}...")
    print()
    
    # Second page (if available)
    if dataset.has_more:
        print("Fetching next 3 records...")
        next_dataset = init_limited_dataset(
            project="pedro-project1",
            name="themes",
            max_records=3,
            cursor=dataset.cursor
        )
        
        next_records = list(next_dataset)
        print(f"Got {len(next_records)} records")
        print(f"Has more: {next_dataset.has_more}")
        print(f"Cursor: {next_dataset.cursor[:30] if next_dataset.cursor else None}...")
    else:
        print("No more records available")


if __name__ == "__main__":
    # Run the simple example
    simple_pagination_example()
    
    print("\n" + "="*60 + "\n")
    
    # Run full pagination
    paginate_dataset("pedro-project1", "themes", page_size=2)
    
    print("\n" + "="*60 + "\n")
    
    # Fetch a specific page
    fetch_specific_page("pedro-project1", "themes", target_page=2, page_size=3)
