"""
Minimal Remote Eval Test - Just to verify logging works

Run: braintrust eval minimal_test_remote_eval.py --dev
"""

from braintrust import Eval, init_dataset


def minimal_task(input_text: str, hooks):
    """Minimal task that just logs and returns."""
    # Log something simple
    hooks.span.log(
        input=input_text,
        metadata={"message": "This is a test log from remote eval"}
    )
    
    result = f"Echo: {input_text}"
    
    hooks.span.log(
        output=result,
        metadata={"message": "Task completed successfully"}
    )
    
    return result


# Minimal eval with no custom scorers
Eval(
    "Minimal Test Remote Eval",
    data=init_dataset("local dev", name="test"),
    task=minimal_task,
    scores=[],  # No scorers to avoid complications
    metadata={"test": "minimal_logging"}
)
