#!/usr/bin/env python3
"""
Example usage of the Braintrust Retention Client

This script demonstrates how to use the BraintrustRetentionClient to create
data retention policies programmatically.
"""

import os
from braintrust_retention_client import (
    BraintrustRetentionClient,
    create_project_logs_retention,
    create_experiment_retention,
    create_dataset_retention
)


def main():
    # Set your API key and project ID
    api_key = "sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU"  # Replace with your actual API key
    project_id = "4542abd4-537e-4ca1-b5a4-14964fe42b78"  # Replace with your actual project ID
    
    # Method 1: Using the client class directly
    print("=== Method 1: Using BraintrustRetentionClient ===")
    try:
        client = BraintrustRetentionClient(api_key=api_key)
        
        # Create a 90-day retention policy for project logs
        result = client.create_retention_policy(
            project_id=project_id,
            object_type="project_logs",
            retention_days=90,
            name="90-Day Log Retention Policy",
            description="Automatically delete project logs older than 90 days"
        )
        
        print(f"✅ Created retention policy:")
        print(f"   ID: {result.automation_id}")
        print(f"   Name: {result.name}")
        print(f"   Object Type: {result.object_type}")
        print(f"   Retention Days: {result.retention_days}")
        print(f"   Found Existing: {result.found_existing}")
        print()
        
    except Exception as e:
        print(f"❌ Error creating retention policy: {e}")
        print()
    
    # Method 2: Using convenience functions
    print("=== Method 2: Using convenience functions ===")
    
    # Example 1: Project logs retention (30 days)
    try:
        result = create_project_logs_retention(
            project_id=project_id,
            retention_days=30,
            api_key=api_key
        )
        print(f"✅ Created 30-day project logs retention: {result.automation_id}")
    except Exception as e:
        print(f"❌ Error creating project logs retention: {e}")
    
    # Example 2: Experiment retention (7 days)
    try:
        result = create_experiment_retention(
            project_id=project_id,
            retention_days=7,
            api_key=api_key
        )
        print(f"✅ Created 7-day experiment retention: {result.automation_id}")
    except Exception as e:
        print(f"❌ Error creating experiment retention: {e}")
    
    # Example 3: Dataset retention (365 days)
    try:
        result = create_dataset_retention(
            project_id=project_id,
            retention_days=365,
            api_key=api_key
        )
        print(f"✅ Created 365-day dataset retention: {result.automation_id}")
    except Exception as e:
        print(f"❌ Error creating dataset retention: {e}")
    
    print()
    
    # Method 3: Using environment variables
    print("=== Method 3: Using environment variables ===")
    
    # Set environment variable (in practice, you'd set this in your shell)
    os.environ["BRAINTRUST_API_KEY"] = api_key
    
    try:
        # Now you can create policies without passing the API key explicitly
        result = create_project_logs_retention(
            project_id=project_id,
            retention_days=60
        )
        print(f"✅ Created 60-day retention using env var: {result.automation_id}")
    except Exception as e:
        print(f"❌ Error creating retention with env var: {e}")
    
    # Method 4: Get existing automations
    print("\n=== Method 4: Getting existing automations ===")
    try:
        client = BraintrustRetentionClient(api_key=api_key)
        automations = client.get_project_automations(project_id=project_id)
        
        print(f"Found {len(automations)} automations for project {project_id}:")
        for automation in automations:
            config = automation.get("config", {})
            if config.get("event_type") == "retention":
                print(f"  - {automation['name']}: {config['object_type']} ({config['retention_days']} days)")
            else:
                print(f"  - {automation['name']}: {config.get('event_type', 'unknown')} automation")
                
    except Exception as e:
        print(f"❌ Error getting automations: {e}")


def create_multiple_policies_example():
    """Example of creating multiple retention policies for different data types."""
    
    api_key = "your-api-key-here"  # Replace with your actual API key
    project_id = "your-project-id-here"  # Replace with your actual project ID
    
    # Define retention policies for different data types
    policies = [
        {"object_type": "project_logs", "retention_days": 90, "name": "Quarterly Log Cleanup"},
        {"object_type": "experiment", "retention_days": 30, "name": "Monthly Experiment Cleanup"},
        {"object_type": "dataset", "retention_days": 365, "name": "Annual Dataset Cleanup"},
    ]
    
    client = BraintrustRetentionClient(api_key=api_key)
    
    print("Creating multiple retention policies...")
    for policy in policies:
        try:
            result = client.create_retention_policy(
                project_id=project_id,
                object_type=policy["object_type"],
                retention_days=policy["retention_days"],
                name=policy["name"]
            )
            print(f"✅ Created {policy['name']}: {result.automation_id}")
        except Exception as e:
            print(f"❌ Failed to create {policy['name']}: {e}")


if __name__ == "__main__":
    print("Braintrust Retention Client Examples")
    print("=" * 40)
    
    # Run the main examples
    main()
    
    # Uncomment the line below to run the multiple policies example
    # create_multiple_policies_example()
