# Mise will automatically read and use .tool-versions files as well as this file.

[env]
# Automatically activate our virtual environment
_.python.venv = "venv"

# See env.example to configure API keys.
_.file = ".env"

# For running unit tests involving orb.
ORB_WEBHOOK_SECRET = "not_a_real_webhook_secret"

# For running the logs2 migrations against the the api database.
INSERT_LOGS2 = "true"

[tasks.psql]
alias = "pg"
description = "Connect to data plane PostgreSQL"
run = "PGPASSWORD='postgres' psql --host 127.0.0.1 --port 5532 --user postgres postgres"

[tasks.redis]
alias = "rs"
description = "Connect to data plane Redis"
run = "redis-cli -p 6479"

[tasks.supabase]
alias = "sb"
description = "Connect to control plane Supabase"
run = "PGPASSWORD='postgres' psql --host 127.0.0.1 --port 54322 --user postgres postgres"

[tasks.expect-test]
alias = "et"
description = "Run an expect test [path_to_file]"
run = "python -m bt_unittest --expect-test-filter={{arg(name=\"file_name\", default=\"\")}} tests/bt_services/test_expect.py"

[tasks.tail2]
dir = "services/data/bt-logs"
run = "tail -n 0 -F api-ts.txt webapp.txt realtime.txt brainstore.txt proxy.txt"

[tasks.tail]
run = "./services/bt_services.py tail"
