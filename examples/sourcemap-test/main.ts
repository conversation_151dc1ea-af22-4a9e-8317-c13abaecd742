import { _internalGetGlobalState } from "braintrust";
import * as esbuild from "esbuild";
import * as fs from "fs/promises";
import { dirname } from "path";
import sourceMap from "source-map";

function buildOpts(fileName: string, outFile: string): esbuild.BuildOptions {
  return {
    entryPoints: [fileName],
    bundle: true,
    treeShaking: true,
    outfile: outFile,
    platform: "node",
    write: false,
    // Remove the leading "v" from process.version
    target: `node${process.version.slice(1)}`,
    external: ["node_modules/*"],
    plugins: [],
  };
}

function isNative(fn: Function): boolean {
  return /\{\s*\[native code\]\s*\}/.test(Function.prototype.toString.call(fn));
}

async function main() {
  const inFile = process.argv[process.argv.length - 1];
  const outFile = "foo.js";
  const buildOptions: esbuild.BuildOptions = {
    ...buildOpts(inFile, outFile),
    external: [],
    write: true,
    plugins: [],
    minify: true,
    sourcemap: true,
  };
  await esbuild.build(buildOptions);

  // Read the sourcemap
  const sourcemap = await fs.readFile(outFile + ".map", "utf8");
  const sourceMapJSON = JSON.parse(sourcemap);

  const generatedCode = await fs.readFile(outFile, "utf8");

  globalThis._evals = {
    evaluators: {},
    reporters: {},
  };
  globalThis._lazy_load = true;
  globalThis.__inherited_braintrust_state = _internalGetGlobalState();
  const __filename = inFile;
  const __dirname = dirname(__filename);
  new Function("require", "__filename", "__dirname", generatedCode)(
    require,
    __filename,
    __dirname,
  );

  const evaluator = Object.values(globalThis._evals.evaluators)[0];
  for (const scorer of evaluator.evaluator.scores) {
    if (isNative(scorer)) {
      console.log(scorer.name + " is native");
      continue;
    }

    const sourceCode = scorer.toString();
    console.log(sourceCode);
    let lineNumber = 0;
    let columnNumber = 0;
    for (const line of generatedCode.split("\n")) {
      const sourceDefinition = line.indexOf(sourceCode);
      if (sourceDefinition !== -1) {
        columnNumber = sourceDefinition;
        console.log("FOO BAR BING!!!", lineNumber, sourceDefinition);
        break;
      }
      lineNumber++;
    }
    const sourceMapConsumer = await new sourceMap.SourceMapConsumer(
      sourceMapJSON,
    );
    const originalPosition = sourceMapConsumer.originalPositionFor({
      line: lineNumber + 1,
      column: columnNumber,
    });

    if (originalPosition.source === inFile && originalPosition.line !== null) {
      console.log("Source definition found!", originalPosition);
      try {
        try {
          const originalFileContent = await fs.readFile(inFile, "utf-8");
          const originalLines = originalFileContent.split("\n");
          // Extract the function definition
          let functionDefinition = "";
          let bracketCount = 0;
          for (
            let i = originalPosition.line - 1;
            i < originalLines.length;
            i++
          ) {
            const line = originalLines[i];
            functionDefinition += line + "\n";
            bracketCount += (line.match(/{/g) || []).length;
            bracketCount -= (line.match(/}/g) || []).length;
            if (bracketCount === 0 && functionDefinition.trim().length > 0)
              break;
          }

          console.log("Original function definition:");
          console.log(functionDefinition.trim());
        } catch (error) {
          console.error("Error reading original source file:", error);
        }
      } catch (e) {
        console.log("Failed to read original code", e);
      }
    }
  }
}

main();
