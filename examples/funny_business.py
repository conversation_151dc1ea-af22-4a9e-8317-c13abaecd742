import random

import braintrust
import faker
from openai import OpenAI

logger = braintrust.init_logger(project="Online scoring")

faker = faker.Faker()
client = braintrust.wrap_openai(OpenAI())


if __name__ == "__main__":
    for i in range(5):
        statement = faker.sentence()
        print(statement)
        print(braintrust.invoke(project_name="Online scoring", slug="comedy-dffc", input=dict(statement=statement)))
        print("----")
