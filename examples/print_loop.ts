import * as braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({
  name: "Tools test",
});

async function timer({ n }: { n: number }) {
  for (let i = 0; i < n; i++) {
    await new Promise((resolve) => setTimeout(resolve, 500));
    console.log("Loop round", i);
  }
  return n;
}

project.tools.create({
  handler: timer,
  name: "Timer",
  ifExists: "replace",
  description: "A simple timer that can count up to a number.",
  parameters: z.object({
    n: z.number(),
  }),
});
