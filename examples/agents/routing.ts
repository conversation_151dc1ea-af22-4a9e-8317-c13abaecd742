import * as braintrust from "braintrust";

// @ts-ignore
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const graph: any = null;

const { IN, OUT } = graph;

const project = braintrust.projects.create({ name: "graph-examples" });

const router = project.prompts.create({
  name: "Question type",
  description: "Categorize the question into a type",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content: "Categorize the question into a type.",
    },
    { role: "user", content: "{{input}}" },
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "questionType",
        description: "Categorize the question into a type",
        parameters: {
          type: "object",
          properties: {
            questionType: {
              type: "string",
              enum: ["math", "history", "literature", "other"],
            },
          },
          required: ["questionType"],
        },
      },
    },
  ],
});

IN.then(router).then((input: { questionType: string }) =>
  graph.gate({
    condition: input.questionType,
    math: solveMathQuestion({ question: input.question }).then(OUT),
    history: solveHistoryQuestion({ question: input.question }).then(OUT),
    literature: solveLiteratureQuestion({ question: input.question }).then(OUT),
  }),
);
