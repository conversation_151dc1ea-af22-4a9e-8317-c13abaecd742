import braintrust
from faker import Faker

Faker.seed(1)
fake = Faker()

if __name__ == "__main__":
    for exp_idx in range(2):
        experiment = braintrust.init("large json diff")
        for i in range(10):
            with experiment.start_span() as span:
                span.log(input=i, output=i * 2)
                with span.start_span("retrieval") as span:
                    input = dict(k=10, restricts=[fake.text() for _ in range(20)])
                    output = [
                        dict(
                            content=fake.text(max_nb_chars=1000),
                            metadata=dict(
                                source=fake.text(),
                                url=fake.url(),
                            ),
                        )
                        for _ in range(1000)
                    ]
                    print("TOTAL CONTENT LENGTH", sum([len(x["content"]) for x in output]))
                    span.log(input=input, output=output)

        print(experiment.summarize())
