import { OpenAI } from "openai";
import { initLogger, loadPrompt, wrap<PERSON>penA<PERSON>, wrapTraced } from "braintrust";

initLogger({ projectName: "loaded prompt" }); // replace with your project name

// wrapOpenAI will make sure the client tracks usage of the prompt.
const client = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);

const runPrompt = wrapTraced(async function runPrompt() {
  const prompt = await loadPrompt({
    projectName: "loaded prompt", // replace with your project name
    slug: "prompt-22fb", // replace with your prompt slug
  });

  const built = prompt.build({
    tolan: { name: "<PERSON>" },
  });

  // Render with parameters
  const first = await client.chat.completions.create(built);

  return await client.chat.completions.create({
    ...built,
    messages: [
      ...built.messages,
      ...first.choices.map((c) => c.message),
      {
        role: "user",
        content: "Tell me a story",
      },
    ],
  });
});

async function main() {
  await runPrompt();
}

main();
