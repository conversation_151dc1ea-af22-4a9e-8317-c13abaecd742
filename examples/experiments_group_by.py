import random

import braintrust

if __name__ == "__main__":
    for experiment_index in range(3):
        experiment = braintrust.init(
            "tagged-scores-2",
            f"tagged-scores-exp-{experiment_index * 3}",
            metadata={
                "common-metadata": "common",
                "nested_metadata": {
                    "nested1": {
                        "nested2": "in" if experiment_index % 2 == 0 else "out",
                    },
                },
            },
        )

        for i in range(2):
            experiment.log(
                input=f"{i}",
                output=2,
                scores={
                    "random_score": random.random(),
                    "stable_score": (i + 1) * 0.25,
                },
                metadata={
                    "this": f"this{i}",
                    "that": "that",
                },
                tags=[f"tag-1-{i}", "common-tag"],
            )

        experiment = braintrust.init(
            "tagged-scores-2",
            f"tagged-scores-exp-{experiment_index * 3 + 1}",
            metadata={
                "common-metadata": "common",
                "nested_metadata": {
                    "nested1": {
                        "nested2": "in" if experiment_index % 2 == 0 else "out",
                    },
                },
            },
        )
        for i in range(2):
            experiment.log(
                input=f"{i}",
                output=2,
                scores={
                    "random_score": random.random(),
                    "stable_score": (i + 1) * 0.25,
                },
                metadata={
                    "this": f"this{i}",
                    "that": "that",
                },
                tags=[f"tag-2-{i}", "common-tag"],
            )

        experiment = braintrust.init("tagged-scores-2", f"tagged-scores-exp-{experiment_index * 3 + 2}")
        for i in range(2):
            experiment.log(
                input=f"{i}",
                output=2,
                scores={
                    "different_random_score": random.random(),
                    "different_stable_score": (i + 1) * 0.25,
                },
                metadata={
                    "this": f"this{i}",
                    "that": "that",
                },
                tags=[f"tag-3-{i}", "common-tag"],
            )

        print(experiment.summarize())
