import { traced, <PERSON><PERSON> } from "braintrust";

const exactMatch = (args: { output: string; expected?: string }) => {
  return {
    name: "Exact match",
    score: args.output === args.expected ? 1 : 0,
  };
};

Eval("comparison_mismatched_traces", {
  data: () => [
    { input: "Which country has the highest population?", expected: "China" },
  ],
  task: async (input) => {
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000));
    return traced(
      async (span) => {
        const messages = { messages: [{ role: "system", text: input }] };
        span.log({ input: messages });

        const result = {
          content: "China",
          latency: 1,
          prompt_tokens: 10,
          completion_tokens: 2,
        };

        span.log({
          output: result.content,
          metrics: {
            latency: result.latency,
            prompt_tokens: result.prompt_tokens,
            completion_tokens: result.completion_tokens,
          },
        });
        return result.content;
      },
      {
        name: "My AI model",
      },
    );
  },
  scores: [exactMatch],
  experimentName: "base",
});

const exactMatchComparison = (args: { output: string; expected?: string }) => {
  return {
    name: "Exact match",
    score: Math.random(),
  };
};

Eval("comparison_mismatched_traces", {
  data: () => [
    {
      input: "Which country has the highest population?",
      expected: "China-comparison",
    },
  ],
  task: async (input) => {
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000));
    await traced(
      async (span) => {
        span.log({ input: "this is a completely separate subtree" });
        span.log({ output: "hello world" });

        return Math.random();
      },
      {
        name: "new-subtree",
      },
    );
    // this matches the first trace
    for (let i = 0; i < 5; i++) {
      traced(
        async (span) => {
          const messages = { messages: [{ role: "system", text: input }] };
          span.log({ input: messages, expected: `China-comparison-${i}` });

          const result = {
            content: `China-comparison-${i}`,
            latency: 1,
            prompt_tokens: 10,
            completion_tokens: 2,
          };

          /* don't log this
        span.log({
          output: result.content,
          metrics: {
            latency: result.latency,
            prompt_tokens: result.prompt_tokens,
            completion_tokens: result.completion_tokens,
          },
        });
        */
          return result.content;
        },
        {
          name: "My AI model",
        },
      );
    }
    return "China-comparison";
  },
  scores: [exactMatch, exactMatchComparison],
  experimentName: "comparison",
});
