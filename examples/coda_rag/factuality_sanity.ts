import { Factuality } from "autoevals";
(async () => {
  const input = "How can I star a doc?";
  const output = `To star a doc, you can follow the steps below depending on the platform you are using:

1. Google Docs:
   - Open the document you want to star.
   - Click on the small gray star located in the upper-right corner of the file in the Google Docs homepage. If the star is empty, it means the document is not starred yet.
   - Once clicked, the star will turn yellow, indicating that the document is now starred.
   - You can access all your starred documents by clicking on "Starred" in the left sidebar of the Google Docs homepage.

2. Microsoft Word:
   - Open the document you want to star.
   - In the Word navigation pane on the left side, right-click on the document's name.
   - A context menu will appear, select "Add to Favourites" or "Add to Favorites." The option name may vary depending on your Word version.
   - The document will be added to your favorites list, usually accessible by clicking the "Favorites" tab in the navigation pane.

3. Other document editors or platforms might have different ways to star or bookmark a document. Look for options like "Favorite," "Bookmark," or "Star" within the user interface or consult the platform's documentation or help section for specific instructions.

Remember that starring a document usually allows for quick access or prioritization, making it easier to find and access your most important or frequently used files.`;

  const expected = `To star a doc, you can hover over its name in the doc list and click the star icon. Alternatively, you can star a doc from within the doc itself by hovering over the doc title in the upper left corner and clicking on the star.`;

  console.log(await Factuality({ input, output, expected }));
})();
