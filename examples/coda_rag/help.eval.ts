import { Eval } from "braintrust";
import { Factuality } from "autoevals";
import { QA_ANSWER_MODEL, buildData, openai } from "./setup";

Eval("Help Desk JS", {
  data: buildData,
  task: async (input) => {
    const response = await openai.chat.completions.create({
      model: QA_ANSWER_MODEL,
      messages: [
        {
          role: "user",
          content: `Please answer the following question:

Question: ${input}`,
        },
      ],
    });

    return response.choices![0].message!.content!;
  },
  scores: [Factuality],
});
