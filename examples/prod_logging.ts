import { initLogger } from "braintrust";

const logger = initLogger();
async function main() {
  for (let i = 0; i < 100; i++) {
    const ret = await logger.traced((span) =>
      span.traced(
        async (span) => {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.random() * 1000),
          );
          span.log({
            input: [{ role: "user", content: "hello world" }],
            output: "hi!",
            metrics: {
              tokens: Math.floor(Math.random() * 1000),
            },
            scores: {
              relevance: Math.random(),
            },
          });
          return "hi";
        },
        {
          name: "OpenAI Completion",
          event: {
            input: "hello world",
            output: "hi!",
            scores: {
              thumbs_up: Math.random() < 0.5 ? 0 : 1,
            },
          },
        },
      ),
    );
    console.log(ret);
  }
}

main();
