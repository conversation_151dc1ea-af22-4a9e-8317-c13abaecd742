from braintrust import <PERSON><PERSON>

from autoevals import LLMClassifier

no_apology = LLMClassifier(
    name="No apology",
    prompt_template="Does the response contain an apology? (Y/N)\n\n{{output}}",
    choice_scores={"Y": 0, "N": 1},
    use_cot=True,
)

Eval(
    "Say Hi Bot",  # Replace with your project name
    data=lambda: [
        {
            "input": "<PERSON>",
            "expected": "<PERSON> David",
        },
    ],  # Replace with your eval dataset
    task=lambda input: "Sorry " + input,  # Replace with your LLM call
    scores=[no_apology],
)
