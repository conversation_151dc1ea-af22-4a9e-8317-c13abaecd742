import {
  Eval,
  EvalHooks,
  BaseMetadata,
  EvalParameters,
  currentSpan,
} from "braintrust";

function task(
  _input: string,
  hooks: EvalHooks<unknown, BaseMetadata, EvalParameters>,
) {
  hooks.tags.push("cookies n cream");
  return "1, 2";
}

function factScorer({
  output,
  expected,
}: {
  output: string;
  expected: { facts: unknown[] };
}) {
  expected.facts.forEach((fact, i) => {
    currentSpan().traced(
      () => {
        currentSpan().log({
          scores: { "Fact finder": output.includes(String(fact)) ? 1 : 0 },
        });
      },
      { name: `fact_${i}` },
    );
  });
  return [];
}

Eval("Facts Extraction", {
  data: [
    {
      input: "What is the capital of the moon?",
      expected: { facts: [1, 2, 3] },
      tags: ["moon", "capital"],
    },
  ],
  task,
  scores: [factScorer],
});
