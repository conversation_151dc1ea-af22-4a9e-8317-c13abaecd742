import json
import os

import boto3
import requests

STAGING_ARN = "arn:aws:lambda:us-east-1:872608195481:function:bt-staging-AIProxyFn-A5FaTyUNMat6"

if __name__ == "__main__":
    resp = requests.post(
        "https://staging-api.braintrust.dev/function/invoke",
        json={
            "global_function": "Factuality",
            "input": {
                "output": "123",
                "expected": "1234",
            },
        },
        headers={
            "Authorization": "Bearer " + os.environ["BRAINTRUST_API_KEY"],
        },
    )

    resp.raise_for_status()
    print(resp.json())

    lambda_client = boto3.client("lambda")
    api_gateway_payload = {
        "version": "2.0",
        "routeKey": "$default",
        "rawPath": "/function/invoke",
        "headers": {"Authorization": "Bearer " + os.environ["BRAINTRUST_API_KEY"], "Content-Type": "application/json"},
        "requestContext": {"http": {"method": "POST", "path": "/function/invoke"}},
        "body": json.dumps(
            {
                "global_function": "Factuality",
                "input": {
                    "output": "123",
                    "expected": "1234",
                },
            }
        ),
        "isBase64Encoded": False,
    }
    result = lambda_client.invoke(
        FunctionName=STAGING_ARN,
        InvocationType="Event",
        Payload=json.dumps(api_gateway_payload),
    )

    print(result)
    print(result["Payload"].read())

    result = lambda_client.invoke_with_response_stream(
        FunctionName=STAGING_ARN,
        Payload=json.dumps(api_gateway_payload),
    )

    print(result)
    for event in result["EventStream"]:
        print(event)
