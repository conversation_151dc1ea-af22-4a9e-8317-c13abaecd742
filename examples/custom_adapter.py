import os

import braintrust
from requests.adapters import HTTPAdapter


class CustomHeaderAdapter(HTTPAdapter):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def send(self, request, **kwargs):
        # Modify the request here to add custom headers
        request.headers.update({"Authorization": "Bearer " + os.environ["BRAINTRUST_API_KEY"]})

        return super().send(request, **kwargs)


if __name__ == "__main__":
    braintrust.set_http_adapter(CustomHeaderAdapter(), CustomHeaderAdapter())
    braintrust.login(api_key="bogus")

    logger = braintrust.init_logger(project="custom adapter")
    logger.log(input=1, output=2)
    logger.flush()
