import json
import os

import braintrust
import requests

BRAINTRUST_API_KEY = os.environ["BRAINTRUST_API_KEY"]

logger = braintrust.init_logger(project="graph test")
project = logger.project
project_id = project.id


def _insert_function(**kwargs):
    resp = requests.post(
        f"http://localhost:8000/v1/function", json=kwargs, headers={"Authorization": f"Bearer {BRAINTRUST_API_KEY}"}
    )
    resp.raise_for_status()
    return resp.json()


def make_calculator_prompt():
    RESULT_PARAMS = {
        "type": "object",
        "properties": {
            "result": {"type": "number"},
        },
    }

    # Try running a prompt with the tool definition, but without the tool call
    prompt_data = {
        "prompt": {
            "type": "chat",
            "messages": [{"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}],
            "tools": json.dumps(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "calculator",
                            "description": "A simple calculator",
                            "parameters": RESULT_PARAMS,
                        },
                    }
                ]
            ),
        },
        "options": {
            "model": "gpt-4o",
            "temperature": 0,
        },
    }
    prompt_record = _insert_function(
        project_id=project_id,
        function_data={
            "type": "prompt",
        },
        prompt_data=prompt_data,
        name="Calculator prompt",
        slug="calculator-prompt",
    )
    resp = braintrust.invoke(
        project_name=project.name, slug="calculator-prompt", input={"formula": "1+2"}, stream=False
    )
    print(resp)


def make_explainer_prompt():
    # Try running a prompt with the tool definition, but without the tool call
    prompt_data = {
        "prompt": {
            "type": "chat",
            "messages": [
                {
                    "role": "user",
                    "content": "Convert the number into its string representation (eg. 1 -> 'one'). {{number}}",
                }
            ],
        },
        "options": {
            "model": "gpt-4o",
            "temperature": 0,
        },
    }
    prompt_record = _insert_function(
        project_id=project_id,
        function_data={
            "type": "prompt",
        },
        prompt_data=prompt_data,
        name="Explainer prompt",
        slug="explainer-prompt",
    )
    resp = braintrust.invoke(project_name=project.name, slug="explainer-prompt", input={"number": 1}, stream=False)
    print(resp)


def make_validator_prompt():
    # Try running a prompt with the tool definition, but without the tool call
    prompt_data = {
        "prompt": {
            "type": "chat",
            "messages": [
                {
                    "role": "user",
                    "content": "Check that the formula and its result are consistent. Formula: {{formula}}, result: {{number}}",
                }
            ],
        },
        "options": {
            "model": "gpt-4o",
            "temperature": 0,
        },
    }
    prompt_record = _insert_function(
        project_id=project_id,
        function_data={
            "type": "prompt",
        },
        prompt_data=prompt_data,
        name="Validator prompt",
        slug="validator-prompt",
    )
    resp = braintrust.invoke(
        project_name=project.name,
        slug="validator-prompt",
        input={"formula": "1+2", "number": 3},
        stream=False,
    )
    print(resp)


def make_conditional_graph():
    graph_data = {
        "type": "graph",
        "nodes": {
            "input": {"type": "input"},
            "output": {"type": "output"},
            "calculator": {
                "type": "function",
                "function": {
                    "project_name": project.name,
                    "slug": "calculator-prompt",
                },
            },
            "extract-result": {
                "type": "btql",
                "expr": "object.result",
            },
            "conditional": {
                "type": "if",
            },
            "true": {
                "type": "literal",
                "value": "is true",
            },
            "false": {
                "type": "literal",
                "value": "is false",
            },
        },
        "edges": {
            "input-calculator": {
                "source": "input",
                "target": "calculator",
                "variable": "formula",
            },
            "calculator-extract-result": {
                "source": "calculator",
                "target": "extract-result",
                "variable": "object",
            },
            "extract-result-conditional": {
                "source": "extract-result",
                "target": "conditional",
                "variable": "condition",
            },
            "conditional-true": {
                "source": "true",
                "target": "conditional",
                "variable": "true",
            },
            "conditional-false": {
                "source": "false",
                "target": "conditional",
                "variable": "false",
            },
            "conditional-output": {
                "source": "conditional",
                "target": "output",
                "variable": "value",
            },
        },
    }

    graph_record = _insert_function(
        project_id=project_id,
        function_data=graph_data,
        name="Calculator graph",
        slug="calculator-graph",
    )

    print("Testing 1+2")
    resp = braintrust.invoke(
        project_name=project.name, slug="calculator-graph", input={"formula": "1+2"}, stream=False
    )
    print(resp)

    print("Testing 0")
    resp = braintrust.invoke(project_name=project.name, slug="calculator-graph", input={"formula": "0"}, stream=False)
    print(resp)


def make_explainer_graph():
    graph_data = {
        "type": "graph",
        "nodes": {
            "input": {"type": "input"},
            "output": {"type": "output"},
            "calculator": {
                "type": "function",
                "function": {"project_name": project.name, "slug": "calculator-prompt"},
            },
            "extract-result": {
                "type": "btql",
                "expr": "object.result",
            },
            "explainer": {
                "type": "function",
                "function": {"project_name": project.name, "slug": "explainer-prompt"},
            },
        },
        "edges": {
            "input-calculator": {
                "source": "input",
                "target": "calculator",
                "variable": "formula",
            },
            "calculator-extract-result": {
                "source": "calculator",
                "target": "extract-result",
                "variable": "object",
            },
            "extract-result-explainer": {
                "source": "extract-result",
                "target": "explainer",
                "variable": "number",
            },
            "explainer-output": {
                "source": "explainer",
                "target": "output",
                "variable": "value",
            },
        },
    }

    graph_record = _insert_function(
        project_id=project_id,
        function_data=graph_data,
        name="Explainer graph",
        slug="explainer-graph",
    )

    print("Testing 1+2")
    resp = braintrust.invoke(project_name=project.name, slug="explainer-graph", input={"formula": "1+2"}, stream=False)
    print(resp)


def make_validator_graph():
    graph_data = {
        "type": "graph",
        "nodes": {
            "input": {"type": "input"},
            "output": {"type": "output"},
            "calculator": {
                "type": "function",
                "function": {"project_name": project.name, "slug": "calculator-prompt"},
            },
            "extract-result": {
                "type": "btql",
                "expr": "object.result",
            },
            "validator": {
                "type": "function",
                "function": {"project_name": project.name, "slug": "validator-prompt"},
            },
        },
        "edges": {
            "input-calculator": {
                "source": "input",
                "target": "calculator",
                "variable": "formula",
            },
            "calculator-extract-result": {
                "source": "calculator",
                "target": "extract-result",
                "variable": "object",
            },
            "input-validator": {
                "source": "input",
                "target": "validator",
                "variable": "formula",
            },
            "extract-result-validator": {
                "source": "extract-result",
                "target": "validator",
                "variable": "number",
            },
            "validator-output": {
                "source": "validator",
                "target": "output",
                "variable": "value",
            },
        },
    }

    graph_record = _insert_function(
        project_id=project_id,
        function_data=graph_data,
        name="Validator graph",
        slug="validator-graph",
    )

    print("Testing 1+2")
    resp = braintrust.invoke(
        project_name=project.name,
        slug="validator-graph",
        input={"formula": "1+2"},
        stream=False,
    )
    print(resp)


if __name__ == "__main__":
    logger = braintrust.init_logger(project="graph test")
    # make_calculator_prompt()
    # make_explainer_prompt()
    # make_validator_prompt()
    # print("conditional graph")
    # make_conditional_graph()
    # print("explainer graph")
    # make_explainer_graph()
    print("validator graph")
    make_validator_graph()
