import random

import braintrust
from faker import Faker

fake = Faker()
fake.seed = 123

SENTENCES = 100
ROWS = 5000

SCORE_NAMES = [fake.word() for i in range(36)]
INPUTS = [
    {
        "id": str(x),
        "input": {"question": fake.sentence()},
        "output": {"answer": fake.paragraph(nb_sentences=SENTENCES)},
        "metadata": {
            "tag": fake.word(),
            "document_id": str(x),
            "document_url": fake.url(),
            "document_title": fake.sentence(),
            "document_content": fake.paragraph(nb_sentences=SENTENCES),
        },
    }
    for x in range(ROWS)
]


def make_row(fake, i):
    return {
        "input": INPUTS[i],
        "output": {
            "macro": fake.paragraph(nb_sentences=20),
            "generated_reply": fake.paragraph(nb_sentences=2 * SENTENCES),
            "autograde_macro_result": {
                "accurate": fake.sentence(),
                "explanation": fake.paragraph(nb_sentences=SENTENCES),
            },
            "autograde_generated_reply_result": {
                "accurate": fake.sentence(),
                "explanation": fake.paragraph(nb_sentences=SENTENCES),
            },
        },
        "scores": {name: random.random() for name in SCORE_NAMES},
        "metadata": {
            "tag": fake.word(),
            "document_id": fake.uuid4(),
            "document_url": fake.url(),
            "intercom_tag": fake.word(),
            "document_title": fake.sentence(),
            "document_content": fake.paragraph(nb_sentences=5 * SENTENCES),
            "similar_embeddings": [
                {
                    "url": fake.sentence(),
                    "text": fake.paragraph(nb_sentences=SENTENCES),
                    "title": fake.sentence(),
                    "article_id": fake.uuid4(),
                    "similarity": {"rank": "XXX", "weighting": "XXX", "similarity": "XXX", "cosine_similarity": "XXX"},
                    "knowledge_base_id": "XXX",
                },
                {
                    "url": fake.sentence(),
                    "text": fake.paragraph(nb_sentences=SENTENCES),
                    "title": fake.sentence(),
                    "article_id": fake.uuid4(),
                    "similarity": {"rank": "XXX", "weighting": "XXX", "similarity": "XXX", "cosine_similarity": "XXX"},
                    "knowledge_base_id": "XXX",
                },
            ],
        },
    }


if __name__ == "__main__":
    exp = braintrust.init(project="big row")

    for i in range(ROWS):
        exp.log(**make_row(fake, i))

    print(exp.summarize())
