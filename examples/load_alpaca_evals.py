import argparse
import json
import os
import pprint
import sys
from urllib.parse import urlparse

import boto3
import braintrust
import duckdb
import pyarrow as pa
import requests
from pyarrow import parquet as pq

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
CACHE_DIR = os.path.join(SCRIPT_DIR, "cache")

s3 = boto3.client("s3")


def download_file():
    path = os.path.join(CACHE_DIR, "results")

    if not os.path.exists(path):
        s3.download_file("braintrust-data-staging", "ad-hoc/alpaca-evals/all-results.tar.gz", path + ".tar.gz")
        os.system(f"tar -xzf {path}.tar.gz -C {CACHE_DIR}")

    return path


def preference_score(p):
    if p == 0:
        return 0.5
    elif p == 2:
        return 1
    elif p == 1:
        return 0
    else:
        raise ValueError(f"Invalid preference score: {p}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Load alpaca evals.")
    parser.add_argument("--project", type=str, help="Project name", required=True)
    args = parser.parse_args()

    braintrust.login()

    os.makedirs(CACHE_DIR, exist_ok=True)
    result_path = download_file()

    models = {}
    for root, dirs, fnames in os.walk(result_path):
        for fname in fnames:
            if fname == "annotations.json":
                models[root.split("/")[-1]] = os.path.join(root, fname)

    for model, path in models.items():
        exp = braintrust.init(project=args.project, experiment=model, is_public=True)
        with open(path, "r") as f:
            data = json.load(f)

        for row in data:
            original_row = row.copy()
            row = row.copy()

            preference = row.pop("preference", None)
            preference_claude = row.pop("preference_claude", None)

            scores = {}
            if preference is not None:
                scores[f"preference_{row['annotator']}"] = preference_score(preference)
            if preference_claude is not None:
                scores[f"preference_claude"] = preference_score(preference_claude)

            try:
                record = {
                    "input": row.pop("instruction"),
                    "output": row.pop("output_2"),
                    "expected": row.pop("output_1"),
                    "scores": scores,
                    "metadata": row,
                }
            except:
                print(model)
                print(original_row)
                raise
            exp.log(**record)

        print(exp.summarize())
