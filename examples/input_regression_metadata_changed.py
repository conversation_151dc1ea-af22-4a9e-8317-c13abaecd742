import random

import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("input-regression-metadata-value-changed")

    scores = list(range(10))
    experiment.log(
        input="same-input1",
        output=2,
        scores={
            "score": random.random(),
        },
        metadata={"metadata": "will-change1"},
    )
    experiment.log(
        input="unchanged",
        output=2,
        scores={
            "score": random.random(),
            "unchanged_score": random.random(),
        },
        metadata={"metadata": "unchanged"},
    )
    experiment.log(
        input="unique1",
        output=2,
        scores={
            "score": random.random(),
            "unique1_score": random.random(),
        },
        metadata={"metadata": "unique1"},
    )

    print(experiment.summarize())

    experiment = braintrust.init("input-regression-metadata-value-changed")
    experiment.log(
        input="same-input1",
        output=2,
        scores={
            "score": random.random(),
        },
        metadata={"metadata": "will-change2"},
    )
    experiment.log(
        input="unchanged",
        output=2,
        scores={
            "score": random.random(),
            "unchanged_score": random.random(),
        },
        metadata={"metadata": "unchanged"},
    )
    experiment.log(
        input="unique2",
        output=2,
        scores={
            "score": random.random(),
            "unique2_score": random.random(),
        },
        metadata={"metadata": "unique2"},
    )

    print(experiment.summarize())
