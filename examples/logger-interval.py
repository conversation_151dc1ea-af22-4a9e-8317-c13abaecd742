import argparse
import json
import random
import sys
import time

import braintrust

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Logger example")
    parser.add_argument("-p", "--project", type=str, default="automations", help="Project name")
    parser.add_argument("-i", "--interval", type=int, default=1, help="Logging interval in seconds")
    parser.add_argument("-f", "--flush-interval", type=int, default=5, help="Flush interval in seconds")

    args = parser.parse_args()
    project = args.project
    interval = args.interval
    flush_interval = args.flush_interval

    try:
        logger = braintrust.init_logger(project=project)
    except Exception as e:
        print(f"Error initializing logger: {e}")
        sys.exit(1)

    t = 0
    while True:
        try:
            with logger.start_span() as span:
                n = random.randint(1, 100)
                out = "".join(["d" if random.random() > 0.5 else "c" for _ in range(10)])
                expected = "".join(["d" if random.random() > 0.5 else "c" for _ in range(10)])

                # Generate random metadata
                metadata = {"temperature": round(random.uniform(0.1, 2.0), 2), "model": random.choice(["a", "b", "c"])}

                print(f"Logging input: {n}, output: {out}, expected: {expected}")
                span.log(input=str(n), output=str(out), expected=str(expected), metadata=metadata)

            if t > flush_interval:
                try:
                    logger.flush()
                    print("Flushed")
                except Exception as e:
                    print(f"Error flushing logger: {e}")
                t = 0
            else:
                time.sleep(interval)
                t += interval
        except Exception as e:
            print(f"Error during logging: {e}")
            time.sleep(interval)  # Still wait before retrying
