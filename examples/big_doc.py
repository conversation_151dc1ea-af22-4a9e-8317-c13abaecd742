import argparse
import random

import braintrust
from faker import Faker

fake = Faker()
Faker.seed(4321)
random.seed(4321)


def fake_field():
    return random.choice(["random_number", "text", "list"])


def fake_value(field_type):
    if field_type == "list":
        return [fake_value("text") for _ in range(random.randint(1, 5))]
    else:
        return getattr(fake, field_type)()


input_fields = {fake.word(): fake_field() for _ in range(20)}
output_fields = {fake.word(): fake_field() for _ in range(5)}
score_fields = [fake.word() for _ in range(10)]
metadata_fields = {f"{fake.word()}-{i}": fake_field() for i in range(10000)}

if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("-n", "--num", type=int, default=10)
    parser.add_argument("-p", "--project", type=str, default="test-big-doc")

    args = parser.parse_args()

    inputs = [
        {
            "variables": {name: fake_value(field_type) for name, field_type in input_fields.items()},
            "prompt": fake.paragraph(20),
        }
        for _ in range(args.num)
    ]

    for _ in range(2):
        exp = braintrust.init(project=args.project)
        for i in range(args.num):
            exp.log(
                **{
                    "inputs": inputs[i],
                    "output": {name: fake_value(field_type) for name, field_type in output_fields.items()},
                    "expected": {name: fake_value(field_type) for name, field_type in output_fields.items()},
                    "scores": {name: random.random() for name in score_fields},
                    "metadata": {name: fake_value(field_type) for name, field_type in metadata_fields.items()},
                }
            )

    print(exp.summarize())
