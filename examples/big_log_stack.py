import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("airtable-long-repro")
    experiment.log(
        input="foo",
        output="""\
Track (most recent call last):
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 1282, in _get_module
    rn importlib.import_module("." + module_name, self.__name__)
  Fi/opt/homebrew/Cellar/python@3.10/3.10.12_1/Frameworks/Python.framework/Versions/3.10/lib/python3.10/importlib/__init__.py", line 126, in import_module
    rn _bootstrap._gcd_import(name[level:], package, level)
  Fi<frozen importlib._bootstrap>", line 1050, in _gcd_import
  Fi<frozen importlib._bootstrap>", line 1027, in _find_and_load
  Fi<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModutFoundError: No module named 'transformers.models.deprecated.open_llama.tokenization_open_llama'

The e exception was the direct cause of the following exception:

Track (most recent call last):
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/benchmark.py", line 96, in evaluate
     freezegun.freeze_time(frozen_time):
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/freezegun/api.py", line 633, in __enter__
    rn self.start()
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/freezegun/api.py", line 722, in start
    le_attrs = _get_cached_module_attributes(module)
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/freezegun/api.py", line 129, in _get_cached_module_attributes
    up_module_cache(module)
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/freezegun/api.py", line 108, in _setup_module_cache
    module_attributes = _get_module_attributes(module)
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/freezegun/api.py", line 97, in _get_module_attributes
    ibute_value = getattr(module, attribute_name)
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 1272, in __getattr__
    le = self._get_module(self._class_to_module[name])
  Fi/Users/<USER>/h/source/hyperbase/qa_prototype/.venv/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 1284, in _get_module
    e RuntimeError(
Runtrror: Failed to import transformers.models.deprecated.open_llama.tokenization_open_llama because of the following error (look up to see its traceback):
No me named 'transformers.models.deprecated.open_llama.tokenization_open_llama'""",
        scores={},
        metadata={
            "foo": "bar",
        },
    )

    print(experiment.summarize())
