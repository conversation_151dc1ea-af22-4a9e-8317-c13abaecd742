import os

from braintrust import init_logger, load_prompt, wrap_openai
from openai import OpenAI

logger = init_logger(project="your project name")


def run_prompt():
    # Replace with your project name and slug
    prompt = load_prompt(
        "your project name", "your prompt slug", defaults=dict(model="gpt-3.5-turbo", temperature=0.5)
    )

    # wrap_openai will make sure the client tracks usage of the prompt.
    client = wrap_openai(OpenAI(api_key=os.environ["OPENAI_API_KEY"]))

    # Render with parameters
    return client.chat.completions.create(**prompt.build(input="1+1"))
