import time

import braintrust
from braintrust.util import LazyValue

if __name__ == "__main__":
    logger = braintrust.init_logger(project="score pagination tiny updates")

    # Increase this number to make queries slower (helps understand index usage)
    for i in range(50):
        logger.log(input=i, output=2, scores={})

        if i % 100 == 0:
            print(i)

    logger.flush()
    time.sleep(1)

    ids = []
    for i in range(50):
        ids.append(logger.log(input=50 + i, output=2))
    logger.flush()

    for i in range(500):
        for id in ids[:5]:
            logger.log_feedback(id=id, scores={"A": 1, "B": 0.5, "C": 0.5, "D": 1, "Skip": 0})
        logger.flush()

    for id in ids:
        logger.bg_logger.log(
            LazyValue(
                lambda: {
                    "id": id,
                    "_object_delete": True,
                    "org_id": logger.org_id,
                    "project_id": logger.project.id,
                    "log_id": "g",
                },
                use_mutex=False,
            )
        )
