import { initExperiment, initLogger } from "braintrust";

const date = new Date().toISOString();
console.log("date", date);

export async function updateExperimentSpan() {
  const experiment = initExperiment("comparison_mismatched_traces", {
    experiment: "comparison",
    update: true,
  });
  experiment.updateSpan({
    id: "ee5638b7-d94b-48ad-ba2b-27b00e2edf7f",
    span_attributes: {
      type: "llm",
    },
    metrics: {
      start: new Date().getTime() - 200,
      end: new Date().getTime(),
    },
    input: [
      {
        content:
          "You are a helpful assistant that can solve math problems. Use the calculator tool when you need to perform calculations.",
        role: "system",
      },
      {
        content: `${date} test`,
        role: "user",
      },
    ],
    output: [
      {
        finish_reason: "tool_calls",
        index: 0,
        logprobs: null,
        message: {
          role: "assistant",
          tool_calls: [
            {
              function: {
                arguments: '{"a":9876,"b":4567,"op":"subtract"}',
                name: "calculator",
              },
              id: "call_hRKR9sayjDEaBHavwI4Sfe1I",
              index: 0,
              type: "function",
            },
          ],
        },
      },
    ],
  });
  await experiment.flush();
}

export async function updateLogSpan() {
  const logger = initLogger({ projectName: "logging_loop_2" });
  logger.updateSpan({
    id: "288aabbb-3579-402d-afe2-f1242f4d867c",
    input: [
      {
        content:
          "You are a helpful assistant that can solve math problems. Use the calculator tool when you need to perform calculations.",
        role: "system",
      },
      {
        content: `${date} yes`,
        role: "user",
      },
    ],
    metrics: {
      start: new Date().getTime() - 500,
      end: new Date().getTime(),
    },
    _is_merge: true,
  });
}

(async function () {
  await updateExperimentSpan();
  //await updateLogSpan();
  console.log("done");
})();
