import argparse from "argparse";
import { initLogger } from "braintrust";
const largeDocumentUrls = [
  "https://www.gutenberg.org/files/1342/1342-0.txt", // Pride and Prejudice by <PERSON>
  "https://www.gutenberg.org/files/11/11-0.txt", // <PERSON>'s Adventures in Wonderland by <PERSON>
  "https://www.gutenberg.org/files/84/84-0.txt", // Frankenstein by <PERSON>
  "https://www.gutenberg.org/files/1661/1661-0.txt", // The Adventures of <PERSON> by <PERSON>
  "https://www.gutenberg.org/files/2701/2701-0.txt", // <PERSON><PERSON> by <PERSON>
  "https://www.gutenberg.org/files/98/98-0.txt", // A Tale of Two Cities by <PERSON>
  "https://www.gutenberg.org/files/1952/1952-0.txt", // The Yellow Wallpaper by <PERSON>
  "https://www.gutenberg.org/files/1080/1080-0.txt", // A Modest Proposal by <PERSON>
  "https://www.gutenberg.org/files/74/74-0.txt", // The Adventures of <PERSON> by <PERSON>
  "https://www.gutenberg.org/files/2814/2814-0.txt", // Dubliners by <PERSON>
  "https://www.gutenberg.org/files/1232/1232-0.txt", // The Prince by Niccolò Machiavelli
  "https://www.gutenberg.org/files/2600/2600-0.txt", // War and Peace by Leo Tolstoy
  "https://www.gutenberg.org/files/345/345-0.txt", // Dracula by Bram Stoker
  "https://www.gutenberg.org/files/1400/1400-0.txt", // Great Expectations by Charles Dickens
  "https://www.gutenberg.org/files/174/174-0.txt", // The Picture of Dorian Gray by Oscar Wilde
  "https://www.gutenberg.org/files/2641/2641-0.txt", // A Room with a View by E. M. Forster
  "https://www.gutenberg.org/files/1184/1184-0.txt", // The Count of Monte Cristo by Alexandre Dumas
  "https://www.gutenberg.org/files/158/158-0.txt", // Emma by Jane Austen
  "https://www.gutenberg.org/files/768/768-0.txt", // Wuthering Heights by Emily Brontë
  "https://www.gutenberg.org/files/1260/1260-0.txt", // Jane Eyre by Charlotte Brontë
];

const _fetchedDocuments = new Map<string, string>();
async function fetchDocument(doc: string) {
  if (_fetchedDocuments.has(doc)) {
    return _fetchedDocuments.get(doc);
  }
  const document = await (await fetch(doc)).text();
  _fetchedDocuments.set(doc, document);
  return document.length > 950000 ? document.slice(0, 500000) : document;
}

async function generateRandomJSON(doc: string) {
  const document = await fetchDocument(doc);
  return {
    context: [
      "apple",
      "blue",
      "cat",
      "dog",
      "elephant",
      "frog",
      "green",
      "house",
      "igloo",
      "jacket",
      "kite",
      "lemon",
      "moon",
      "nest",
      "orange",
      "purple",
      "quilt",
      "rabbit",
      "sun",
      "tree",
    ],
    document: document,
  };
}

const logger = initLogger({
  projectName: "large documents",
});

function randomScore() {
  const bucket = Math.random();
  if (bucket < 0.3) {
    return Math.random();
  } else if (bucket < 0.6) {
    return 0;
  } else {
    return 1;
  }
}

async function logBook(i: number, numSpans: number) {
  await logger.traced(
    async (span) => {
      const document = await generateRandomJSON(
        largeDocumentUrls[i % largeDocumentUrls.length],
      );
      for (let j = 1; j < numSpans; j++) {
        await span.traced(async (span) => {
          const document = await generateRandomJSON(
            largeDocumentUrls[(i + j) % largeDocumentUrls.length],
          );
          span.log({
            input: document,
            output: document,
          });
        });
      }
      span.log({
        input: document,
        output: document,
        metadata: {
          foo: "bar",
        },
        scores: {
          foo: randomScore(),
          bar: randomScore(),
          baz: randomScore(),
          qux: randomScore(),
          quux: randomScore(),
          corge: randomScore(),
          grault: randomScore(),
          garply: randomScore(),
          waldo: randomScore(),
          fred: randomScore(),
          plugh: randomScore(),
          xyzzy: randomScore(),
          thud: randomScore(),
        },
      });
    },
    {
      event: {
        metrics: {
          start: Date.now() / 1000 - 24 * 3600 * 7 * Math.random(),
        },
      },
    },
  );
}

const parser = new argparse.ArgumentParser();
parser.add_argument("--num-traces", { type: "int", default: 100 });
parser.add_argument("--num-spans", { type: "int", default: 5 });

async function main() {
  const args = parser.parse_args();
  for (let i = 0; i < args.num_traces; i++) {
    await logBook(i, args.num_spans);
  }

  await logger.flush();
}

main();
