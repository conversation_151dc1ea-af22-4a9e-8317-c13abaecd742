import time

import braintrust
import openai

# NOTE: This script predates being able to create prompts through the
# SDK, so create a prompt in a project called "prompts test" named "p"

if __name__ == "__main__":
    start = time.time()
    prompt = braintrust.load_prompt("prompts test", "p", no_trace=True)
    print("Loaded in {:.2f} seconds".format(time.time() - start), prompt.prompt)

    # First, do not wrap the OpenAI client. The prompt should run!
    client = openai.OpenAI()
    print(client.chat.completions.create(**prompt.render(input="1+1")))

    # Now, wrap the OpenAI client. The prompt should run, and we should log the template
    # along with the response.
    prompt = braintrust.load_prompt("prompts test", "p")

    client = braintrust.wrap_openai(client)
    logger = braintrust.init_logger(project="prompts test")
    print(client.chat.completions.create(**prompt.render(input="1+1")))
