# NOTE: I would like this to be an expect test, but am stuck figuring out how to make
# the proxy work. Can convert after we sort out issues in https://github.com/braintrustdata/braintrust/pull/789
from braintrust import Eval, current_span, traced

from autoevals import EmbeddingSimilarity


@traced("foo")
def basic_foo():
    current_span().log(metadata=dict(message="In foo"))
    return "bar"


def basic_task(input, hooks):
    hooks.span.log(metadata=dict(message="In task"))
    return basic_foo()


Eval(
    "basic",
    data=lambda: [dict(input="a", expected="b")],
    task=basic_task,
    scores=[EmbeddingSimilarity],
    experiment_name="My basic eval",
    metadata={"foo": "bar"},
)
