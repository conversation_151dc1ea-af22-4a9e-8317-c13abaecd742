import { Eval, wrap<PERSON><PERSON><PERSON><PERSON> } from "braintrust";
import { ClosedQA } from "autoevals";
import { OpenAI } from "openai";

const openai = wrapOpenAI(new OpenAI());

const closedQA = (args: { input: string; output: string }) => {
  return ClosedQA({
    input: args.input,
    output: args.output,
    criteria:
      "Does the submission specify whether or not it can confidently answer the question?",
  });
};

Eval("QA bot", {
  data: () => [
    {
      input: "Which insect has the highest population?",
      expected: "ant",
    },
  ],
  task: async (input) => {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content:
            "Answer the following question. Specify how confident you are (or not)",
        },
        { role: "user", content: "Question: " + input },
      ],
      temperature: 0,
    });
    return response.choices[0].message.content || "Unknown";
  },
  scores: [closedQA],
});
