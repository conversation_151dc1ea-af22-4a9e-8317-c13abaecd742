import { openai } from "@ai-sdk/openai";
import { generateText, tool } from "ai";
import { initLogger, wrapAISDKModel } from "braintrust";
import { z } from "zod";

const logger = initLogger({
  projectName: "AI SDK Wrapper",
});

const model = wrapAISDKModel(openai("gpt-4o"));

async function main() {
  const { text } = await logger.traced(
    async (span) =>
      await generateText({
        model,
        maxSteps: 5,
        tools: {
          weather: tool({
            description: "Get the weather in a location",
            parameters: z.object({
              location: z
                .string()
                .describe("The location to get the weather for"),
            }),
            execute: async ({ location }: { location: string }) => ({
              location,
              temperature: 72 + Math.floor(Math.random() * 21) - 10,
            }),
          }),
        },
        prompt: "What is the weather in San Francisco?",
      }),
  );
  console.log(text);
}

main();
