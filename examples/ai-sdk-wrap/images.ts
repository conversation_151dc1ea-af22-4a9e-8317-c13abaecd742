import { anthropic } from "@ai-sdk/anthropic";
import { streamText } from "ai";
import { wrapAISDKModel } from "braintrust";

const model = wrapAISDKModel(anthropic("claude-3-5-sonnet-latest"));
// const model = anthropic("claude-3-5-sonnet-latest");

async function main() {
  const result = await streamText({
    model: model,
    messages: [
      {
        role: "system",
        content: "test",
      },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: "test",
          },
          {
            type: "image",
            image:
              "https://the7eagles.com/wp-content/uploads/2024/05/What-is-an-Image-URL.webp",
          },
        ],
      },
    ],
  });

  for await (const part of result.fullStream) {
    console.log(part);
  }
}

main();
