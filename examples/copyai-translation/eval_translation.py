import json

import braintrust
import openai

with open("20240612-translation-testset.jsonl.json", "r") as f:
    raw_data = [json.loads(line) for line in f if line.strip()]

print(raw_data)

client = braintrust.wrap_openai(openai.OpenAI())


@braintrust.traced
def translate(input):
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "system",
                "content": f"Translate the following text from {input['input_language']} to {input['output_language']}",
            },
            {"role": "user", "content": input["input_text"]},
        ],
    )
    return response.choices[0].message.content


@braintrust.traced
def criteria_scorer(output, metadata):
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "system",
                "content": metadata["evaluation_question"]
                + (" (Hint: " + metadata["hint"] + ")" if metadata["hint"] else ""),
            },
            {"role": "user", "content": output},
        ],
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "score",
                    "description": "Select the score. Must be a number between 0 and 1.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "score": {"type": "number"},
                        },
                    },
                },
            }
        ],
        tool_choice={"type": "function", "function": {"name": "score"}},
    )
    return json.loads(response.choices[0].message.tool_calls[0].function.arguments)["score"]


braintrust.Eval(
    "Translation",
    data=[
        {
            "input": {
                "input_language": r["input_language"],
                "output_language": r["output_language"],
                "input_text": r["input_text"],
            },
            "metadata": {
                "evaluation_question": r["evaluation_question"],
                "hint": r["hint"],
            },
        }
        for r in raw_data
    ],
    task=translate,
    scores=[criteria_scorer],
)
