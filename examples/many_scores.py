import random

import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("many-scores")

    scores = list(range(10))
    for i in range(20):
        experiment.log(
            input=1,
            output=2,
            scores={
                **{f"retrieved_doc_ids_{i}": 0.5 for i in scores},
                **{f"retrieved_doc_urls_{i}": 0.5 for i in scores},
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("many-scores")
    for i in range(20):
        random.shuffle(scores)
        experiment.log(
            input=1,
            output=2,
            scores={
                **{f"retrieved_doc_ids_{i}": 0.5 if random.random() > 0.5 else None for i in scores},
                **{f"retrieved_doc_urls_{i}": 0.5 for i in scores},
            },
        )

    print(experiment.summarize())
