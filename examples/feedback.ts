import { initLogger, wrap<PERSON><PERSON><PERSON><PERSON>, wrapTraced } from "braintrust";
import OpenAI from "openai";

const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const client = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);

const someLLMFunction = wrapTraced(async function someLLMFunction(
  input: string,
) {
  return await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "Classify the following text as a question or a statement.",
      },
      {
        role: "user",
        content: input,
      },
    ],
    model: "gpt-4o",
  });
});

export async function POST(input: string) {
  return logger.traced(async (span) => {
    const result = await someLLMFunction(input);
    span.log({ input, output: result });
    return {
      result,
      requestId: await span.export(),
    };
  });
}

export async function POSTFeedback(body: {
  requestId: string;
  comment: string;
  score: number;
  userId: string;
}) {
  logger.traced(
    async (span) => {
      logger.logFeedback({
        id: span.id, // Use the newly created span's id, instead of the original request's id
        comment: body.comment,
        scores: {
          correctness: body.score,
        },
        metadata: {
          user_id: body.userId,
        },
      });
    },
    {
      parent: body.requestId,
      name: "feedback",
    },
  );
}

async function main() {
  const result = await POST("Hello, world!");
  await POSTFeedback({
    requestId: result.requestId,
    comment: "This is a test comment",
    score: 0.4,
    userId: "123",
  });
}

main();
