from braintrust import <PERSON><PERSON>


def error_task(input):
    raise Exception("Simulated error")


Eval(
    "has-error",
    data=lambda: [
        {
            "input": "foo",
        },
    ],
    task=error_task,
    scores=[lambda: 1.0],
    experiment_name="has-error",
)

Eval(
    "null-score",
    data=lambda: [
        {
            "input": "foo",
        },
    ],
    task=error_task,
    scores=[lambda: None],
    experiment_name="null-score",
)
