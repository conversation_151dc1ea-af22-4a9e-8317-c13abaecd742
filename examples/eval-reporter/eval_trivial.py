from braintrust import Eval, Score

from autoevals import <PERSON><PERSON><PERSON><PERSON>


def exactMatch(input, output, expected):
    return Score(
        name="Exact match",
        score=1 if output == expected else 0,
    )


Eval(
    "My Evaluation",
    data=lambda: [
        {"input": "What is 1+1?", "expected": "2"},
    ],
    task=lambda input: "2.0",
    scores=[exactMatch, Levenshtein],
    experiment_name="exactMatch",
)
