import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";

const exactMatch = (args: {
  input: string;
  output: string;
  expected: string;
}) => {
  return {
    name: "Exact match",
    score: args.output === args.expected ? 1 : 0,
  };
};

Eval("My Evaluation", {
  data: () => [{ input: "What is 1+1?", expected: "2" }],
  task: async () => "2.0",
  scores: [exactMatch, <PERSON><PERSON>htein],
});
