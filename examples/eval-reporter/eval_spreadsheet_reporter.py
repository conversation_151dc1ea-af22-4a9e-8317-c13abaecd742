import csv
import datetime
import os
import sys
from io import String<PERSON>

from braintrust import <PERSON><PERSON>, Reporter

from autoevals import <PERSON><PERSON><PERSON><PERSON>, ValidJSO<PERSON>

Eval(
    "Reported project 1",
    data=[{"input": "Hello, world!", "expected": "Hi"}],
    task=lambda x: "Hi!",
    scores=[<PERSON><PERSON><PERSON><PERSON>, ValidJSON],
)


Eval(
    "Reported project 2",
    data=[{"input": "foo bar"}],
    task=lambda x: {"foo": "bar"},
    scores=[ValidJSON],
)


def report_eval(evaluator, result, opts):
    return {
        "project": result.summary.project_name,
        "experiment": result.summary.experiment_name,
        "experiment_link": result.summary.experiment_url,
        "scores": result.summary.scores,
    }


def report_run(results):
    # Generate a unique filename with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"report_{timestamp}.csv"

    # Create the CSV file
    with open(filename, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)

        # Write header
        writer.writerow(["Project", "Experiment", "Experiment Link", "Score Name", "Score Value", "Score Change"])

        # Write data
        for result in results:
            project = result["project"]
            experiment = result["experiment"]
            experiment_link = result["experiment_link"]

            for score_name, score_data in result["scores"].items():
                # ScoreSummary object has score and diff attributes, not get method
                score_value = score_data.score if hasattr(score_data, "score") else "N/A"
                score_change = (
                    score_data.diff if hasattr(score_data, "diff") and score_data.diff is not None else "N/A"
                )

                writer.writerow([project, experiment, experiment_link, score_name, score_value, score_change])

    # Print the filename
    print(f"Report saved to: {os.path.abspath(filename)}")

    return True


Reporter(
    "Spreadsheet reporter",  # Replace with your reporter name
    report_eval=report_eval,
    report_run=report_run,
)
