import * as braintrust from "braintrust";

const ITERS = 10;

async function main() {
  const logger = braintrust.initLogger({ projectName: "Tree field search" });
  for (let i = 0; i < ITERS; ++i) {
    const metaVal = i % 3;
    logger.log({
      input: "foo",
      output: "bar",
      expected: "baz",
      scores: { accuracy: 0.5 },
      metadata: {
        foo: metaVal,
        foo_foo: metaVal,
        "foo foo": metaVal,
        "foo.foo": metaVal,
      },
    });
    await new Promise((r) => setTimeout(r, 10));
  }
}

main();
