import random
import string
import uuid

import braintrust

logger = braintrust.init_logger("high_cardinality_metadata")

if __name__ == "__main__":
    metadata = {f"otp_key_{i}": {f"nested_key_{j}": f"value_{i}_{j}" for j in range(100)} for i in range(1000)}

    def generate_random_value_string(length=10):
        """Generate a random string of fixed length."""
        letters = string.ascii_lowercase
        return "".join(random.choice(letters) for i in range(length))

    input_list_for_log = [{uuid.uuid4().hex: generate_random_value_string()} for _ in range(100000)]

    logger.log(input=input_list_for_log, output="bar", metadata=metadata)
