import threading

import braintrust


def main():
    experiment = braintrust.init("missing_parent_span_repro")
    print("Created experiment", experiment.summarize().experiment_url)

    exported_span = None
    thread_two_barrier = threading.Event()
    thread_one_barrier = threading.Event()

    def thread_one():
        nonlocal exported_span

        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True
            with experiment.start_span() as root_span:
                root_span.log(input="foo", output="bar")
                # Create a "good" sub-span tree.
                with root_span.start_span("good-subspan") as subspan:
                    subspan.log(input="good", output="thing")
                    with subspan.start_span("good-subsubspan") as subsubspan:
                        subsubspan.log(input="bear", output="object")
                logger.flush()
                # Create a subspan and send it over to the other thread to build
                # atop before we publish it.
                with root_span.start_span("bad-subspan") as subspan:
                    subspan.log(input="hello", output="world")
                    exported_span = subspan.export()
                    thread_two_barrier.set()

            thread_one_barrier.wait()
            logger.flush()

    def thread_two():
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            thread_two_barrier.wait(timeout=10.0)
            with braintrust.start_span("bad-subsubspan", parent=exported_span) as subspan:
                subspan.log(input="baz", output="qux")
            logger.flush()

    threads = [threading.Thread(target=thread_one), threading.Thread(target=thread_two)]
    for t in threads:
        t.start()
    threads[1].join()
    input("Published root span and sub-sub span. Hit enter to publish the (middle) sub span:\n")
    thread_one_barrier.set()
    threads[0].join()


if __name__ == "__main__":
    main()
