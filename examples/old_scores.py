import time

import braintrust
from braintrust.util import LazyValue

if __name__ == "__main__":
    logger = braintrust.init_logger(project="score pagination old scores")

    # Increase this number to make queries slower (helps understand index usage)
    for i in range(50):
        logger.log(input=i, output=2, scores={"a": 1})

        if i % 100 == 0:
            print(i)

    logger.flush()
    time.sleep(1)

    for i in range(50):
        logger.log(input=50 + i, output=2, scores={"b": 1})
    logger.flush()
