import json
import os
import time

import braintrust
from faker import Faker

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
ROWS = 50000

if __name__ == "__main__":
    fake = Faker()
    log = braintrust.init_logger(project="metadata-search")
    for i in range(ROWS):
        with log.start_span("task") as span:
            span.log(
                input={"foo": fake.name()},
                output={"bar": fake.name()},
                metadata={
                    "some_constant": "123",
                    "user_id": fake.uuid4(),
                    "info": {
                        "user_name": fake.name(),
                        "user_email": fake.email(),
                    },
                },
                tags=list(set([fake.word() for _ in range(fake.random_int(1, 5))])),
            )

        if i % 1000 == 0:
            print(i)
            log.flush()


# Once you run this repro, you can test searches in the UI. If they're fast, great! If not,
# we may be missing an index. A request like this:

# {"query": {
#   "from": {
#     "op": "function",
#     "name": {
#       "op": "ident",
#       "name": [
#         "project_logs"
#       ]
#     },
#     "args": [
#       {
#         "op": "literal",
#         "value": "3b18e296-d269-4f9a-83a9-98f1a90ba06d"
#       }
#     ]
#   },
#   "select": [
#     {
#       "op": "star"
#     }
#   ],
#   "filter": {
#     "op": "eq",
#     "left": {
#       "op": "ident",
#       "name": [
#         "metadata",
#         "info",
#         "user_email"
#       ],
#       "loc": {
#         "start": {
#           "line": 1,
#           "col": 1
#         },
#         "end": {
#           "line": 1,
#           "col": 25
#         }
#       }
#     },
#     "right": {
#       "op": "literal",
#       "value": "<EMAIL>",
#       "loc": {
#         "start": {
#           "line": 1,
#           "col": 26
#         },
#         "end": {
#           "line": 1,
#           "col": 48
#         }
#       }
#     },
#     "loc": {
#       "start": {
#         "line": 1,
#         "col": 1
#       },
#       "end": {
#         "line": 1,
#         "col": 48
#       }
#     }
#   }
# }
# ,"fmt":"json", "_debug_explain": true
# }
#
# (with the correct project_id) should return something like this. M
# > "Index Cond": "(data @> '{\"metadata\": {\"info\": {\"user_email\": \"<EMAIL>\"}}}'::jsonb)"
# is how we know that the index is being used.
#
# {
#     "explain": [
#         {
#             "QUERY PLAN": [
#                 {
#                     "Plan": {
#                         "Node Type": "Subquery Scan",
#                         "Parallel Aware": false,
#                         "Async Capable": false,
#                         "Alias": "project_logs",
#                         "Startup Cost": 312.78,
#                         "Total Cost": 312.83,
#                         "Plan Rows": 1,
#                         "Plan Width": 771,
#                         "Plans": [
#                             {
#                                 "Node Type": "Subquery Scan",
#                                 "Parent Relationship": "Subquery",
#                                 "Parallel Aware": false,
#                                 "Async Capable": false,
#                                 "Alias": "sub1",
#                                 "Startup Cost": 312.78,
#                                 "Total Cost": 312.8,
#                                 "Plan Rows": 1,
#                                 "Plan Width": 1189,
#                                 "Filter": "(NOT sub1._object_delete)",
#                                 "Plans": [
#                                     {
#                                         "Node Type": "Bitmap Heap Scan",
#                                         "Parent Relationship": "InitPlan",
#                                         "Subplan Name": "CTE previous_candidate_root_spans",
#                                         "Parallel Aware": false,
#                                         "Async Capable": false,
#                                         "Relation Name": "logs",
#                                         "Alias": "logs_1",
#                                         "Startup Cost": 64.2,
#                                         "Total Cost": 155.07,
#                                         "Plan Rows": 1,
#                                         "Plan Width": 32,
#                                         "Recheck Cond": "(data @> '{\"metadata\": {\"info\": {\"user_email\": \"<EMAIL>\"}}}'::jsonb)",
#                                         "Filter": "((NOT _object_delete) AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id >= '(9223372036854775807,\"\")'::_xact_id_root_span_id) AND (COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) = 'global_log:3b18e296-d269-4f9a-83a9-98f1a90ba06d'::text) AND (_xact_id = (SubPlan 2)))",
#                                         "Plans": [
#                                             {
#                                                 "Node Type": "Bitmap Index Scan",
#                                                 "Parent Relationship": "Outer",
#                                                 "Parallel Aware": false,
#                                                 "Async Capable": false,
#                                                 "Index Name": "logs_data_idx",
#                                                 "Startup Cost": 0,
#                                                 "Total Cost": 64.2,
#                                                 "Plan Rows": 26,
#                                                 "Plan Width": 0,
#                                                 "Index Cond": "(data @> '{\"metadata\": {\"info\": {\"user_email\": \"<EMAIL>\"}}}'::jsonb)"
#                                             },
#                                             {
#                                                 "Node Type": "Result",
#                                                 "Parent Relationship": "SubPlan",
#                                                 "Subplan Name": "SubPlan 2",
#                                                 "Parallel Aware": false,
#                                                 "Async Capable": false,
#                                                 "Startup Cost": 2.44,
#                                                 "Total Cost": 2.45,
#                                                 "Plan Rows": 1,
#                                                 "Plan Width": 8,
#                                                 "Plans": [
#                                                     {
#                                                         "Node Type": "Limit",
#                                                         "Parent Relationship": "InitPlan",
#                                                         "Subplan Name": "InitPlan 1 (returns $1)",
#                                                         "Parallel Aware": false,
#                                                         "Async Capable": false,
#                                                         "Startup Cost": 0.42,
#                                                         "Total Cost": 2.44,
#                                                         "Plan Rows": 1,
#                                                         "Plan Width": 8,
#                                                         "Plans": [
#                                                             {
#                                                                 "Node Type": "Index Scan",
#                                                                 "Parent Relationship": "Outer",
#                                                                 "Parallel Aware": false,
#                                                                 "Async Capable": false,
#                                                                 "Scan Direction": "Forward",
#                                                                 "Index Name": "logs_make_object_id_id__xact_id_idx",
#                                                                 "Relation Name": "logs",
#                                                                 "Alias": "_other_logs",
#                                                                 "Startup Cost": 0.42,
#                                                                 "Total Cost": 2.44,
#                                                                 "Plan Rows": 1,
#                                                                 "Plan Width": 8,
#                                                                 "Index Cond": "((COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) = 'global_log:3b18e296-d269-4f9a-83a9-98f1a90ba06d'::text) AND (id = logs_1.id) AND (_xact_id IS NOT NULL))"
#                                                             }
#                                                         ]
#                                                     }
#                                                 ]
#                                             }
#                                         ]
#                                     },
#                                     {
#                                         "Node Type": "Limit",
#                                         "Parent Relationship": "InitPlan",
#                                         "Subplan Name": "CTE candidate_root_spans",
#                                         "Parallel Aware": false,
#                                         "Async Capable": false,
#                                         "Startup Cost": 155.16,
#                                         "Total Cost": 155.17,
#                                         "Plan Rows": 1,
#                                         "Plan Width": 72,
#                                         "Plans": [
#                                             {
#                                                 "Node Type": "Sort",
#                                                 "Parent Relationship": "Outer",
#                                                 "Parallel Aware": false,
#                                                 "Async Capable": false,
#                                                 "Startup Cost": 155.16,
#                                                 "Total Cost": 155.17,
#                                                 "Plan Rows": 1,
#                                                 "Plan Width": 72,
#                                                 "Sort Key": [
#                                                     "(ROW(logs_2._xact_id, COALESCE(logs_2.root_span_id, logs_2.id))::_xact_id_root_span_id) DESC"
#                                                 ],
#                                                 "Plans": [
#                                                     {
#                                                         "Node Type": "Bitmap Heap Scan",
#                                                         "Parent Relationship": "Outer",
#                                                         "Parallel Aware": false,
#                                                         "Async Capable": false,
#                                                         "Relation Name": "logs",
#                                                         "Alias": "logs_2",
#                                                         "Startup Cost": 64.22,
#                                                         "Total Cost": 155.15,
#                                                         "Plan Rows": 1,
#                                                         "Plan Width": 72,
#                                                         "Recheck Cond": "(data @> '{\"metadata\": {\"info\": {\"user_email\": \"<EMAIL>\"}}}'::jsonb)",
#                                                         "Filter": "((NOT _object_delete) AND (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id < '(9223372036854775807,\"\")'::_xact_id_root_span_id) AND (NOT (hashed SubPlan 6)) AND (COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) = 'global_log:3b18e296-d269-4f9a-83a9-98f1a90ba06d'::text) AND (_xact_id = (SubPlan 5)))",
#                                                         "Plans": [
#                                                             {
#                                                                 "Node Type": "Bitmap Index Scan",
#                                                                 "Parent Relationship": "Outer",
#                                                                 "Parallel Aware": false,
#                                                                 "Async Capable": false,
#                                                                 "Index Name": "logs_data_idx",
#                                                                 "Startup Cost": 0,
#                                                                 "Total Cost": 64.2,
#                                                                 "Plan Rows": 26,
#                                                                 "Plan Width": 0,
#                                                                 "Index Cond": "(data @> '{\"metadata\": {\"info\": {\"user_email\": \"<EMAIL>\"}}}'::jsonb)"
#                                                             },
#                                                             {
#                                                                 "Node Type": "CTE Scan",
#                                                                 "Parent Relationship": "SubPlan",
#                                                                 "Subplan Name": "SubPlan 6",
#                                                                 "Parallel Aware": false,
#                                                                 "Async Capable": false,
#                                                                 "CTE Name": "previous_candidate_root_spans",
#                                                                 "Alias": "previous_candidate_root_spans",
#                                                                 "Startup Cost": 0,
#                                                                 "Total Cost": 0.02,
#                                                                 "Plan Rows": 1,
#                                                                 "Plan Width": 32
#                                                             },
#                                                             {
#                                                                 "Node Type": "Result",
#                                                                 "Parent Relationship": "SubPlan",
#                                                                 "Subplan Name": "SubPlan 5",
#                                                                 "Parallel Aware": false,
#                                                                 "Async Capable": false,
#                                                                 "Startup Cost": 2.44,
#                                                                 "Total Cost": 2.45,
#                                                                 "Plan Rows": 1,
#                                                                 "Plan Width": 8,
#                                                                 "Plans": [
#                                                                     {
#                                                                         "Node Type": "Limit",
#                                                                         "Parent Relationship": "InitPlan",
#                                                                         "Subplan Name": "InitPlan 4 (returns $4)",
#                                                                         "Parallel Aware": false,
#                                                                         "Async Capable": false,
#                                                                         "Startup Cost": 0.42,
#                                                                         "Total Cost": 2.44,
#                                                                         "Plan Rows": 1,
#                                                                         "Plan Width": 8,
#                                                                         "Plans": [
#                                                                             {
#                                                                                 "Node Type": "Index Scan",
#                                                                                 "Parent Relationship": "Outer",
#                                                                                 "Parallel Aware": false,
#                                                                                 "Async Capable": false,
#                                                                                 "Scan Direction": "Forward",
#                                                                                 "Index Name": "logs_make_object_id_id__xact_id_idx",
#                                                                                 "Relation Name": "logs",
#                                                                                 "Alias": "_other_logs_1",
#                                                                                 "Startup Cost": 0.42,
#                                                                                 "Total Cost": 2.44,
#                                                                                 "Plan Rows": 1,
#                                                                                 "Plan Width": 8,
#                                                                                 "Index Cond": "((COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) = 'global_log:3b18e296-d269-4f9a-83a9-98f1a90ba06d'::text) AND (id = logs_2.id) AND (_xact_id IS NOT NULL))"
#                                                                             }
#                                                                         ]
#                                                                     }
#                                                                 ]
#                                                             }
#                                                         ]
#                                                     }
#                                                 ]
#                                             }
#                                         ]
#                                     },
#                                     {
#                                         "Node Type": "Unique",
#                                         "Parent Relationship": "Subquery",
#                                         "Parallel Aware": false,
#                                         "Async Capable": false,
#                                         "Startup Cost": 2.54,
#                                         "Total Cost": 2.55,
#                                         "Plan Rows": 1,
#                                         "Plan Width": 1189,
#                                         "Plans": [
#                                             {
#                                                 "Node Type": "Sort",
#                                                 "Parent Relationship": "Outer",
#                                                 "Parallel Aware": false,
#                                                 "Async Capable": false,
#                                                 "Startup Cost": 2.54,
#                                                 "Total Cost": 2.55,
#                                                 "Plan Rows": 1,
#                                                 "Plan Width": 1189,
#                                                 "Sort Key": [
#                                                     "logs.id",
#                                                     "logs._xact_id DESC"
#                                                 ],
#                                                 "Plans": [
#                                                     {
#                                                         "Node Type": "Nested Loop",
#                                                         "Parent Relationship": "Outer",
#                                                         "Parallel Aware": false,
#                                                         "Async Capable": false,
#                                                         "Join Type": "Inner",
#                                                         "Startup Cost": 0.47,
#                                                         "Total Cost": 2.53,
#                                                         "Plan Rows": 1,
#                                                         "Plan Width": 1189,
#                                                         "Inner Unique": false,
#                                                         "Plans": [
#                                                             {
#                                                                 "Node Type": "Limit",
#                                                                 "Parent Relationship": "Outer",
#                                                                 "Parallel Aware": false,
#                                                                 "Async Capable": false,
#                                                                 "Startup Cost": 0.04,
#                                                                 "Total Cost": 0.05,
#                                                                 "Plan Rows": 1,
#                                                                 "Plan Width": 40,
#                                                                 "Plans": [
#                                                                     {
#                                                                         "Node Type": "Sort",
#                                                                         "Parent Relationship": "Outer",
#                                                                         "Parallel Aware": false,
#                                                                         "Async Capable": false,
#                                                                         "Startup Cost": 0.04,
#                                                                         "Total Cost": 0.05,
#                                                                         "Plan Rows": 1,
#                                                                         "Plan Width": 40,
#                                                                         "Sort Key": [
#                                                                             "(max(candidate_root_spans._xact_id)) DESC",
#                                                                             "candidate_root_spans.coalesced_root_span_id DESC"
#                                                                         ],
#                                                                         "Plans": [
#                                                                             {
#                                                                                 "Node Type": "Aggregate",
#                                                                                 "Strategy": "Hashed",
#                                                                                 "Partial Mode": "Simple",
#                                                                                 "Parent Relationship": "Outer",
#                                                                                 "Parallel Aware": false,
#                                                                                 "Async Capable": false,
#                                                                                 "Startup Cost": 0.02,
#                                                                                 "Total Cost": 0.03,
#                                                                                 "Plan Rows": 1,
#                                                                                 "Plan Width": 40,
#                                                                                 "Group Key": [
#                                                                                     "candidate_root_spans.coalesced_root_span_id"
#                                                                                 ],
#                                                                                 "Planned Partitions": 0,
#                                                                                 "Plans": [
#                                                                                     {
#                                                                                         "Node Type": "CTE Scan",
#                                                                                         "Parent Relationship": "Outer",
#                                                                                         "Parallel Aware": false,
#                                                                                         "Async Capable": false,
#                                                                                         "CTE Name": "candidate_root_spans",
#                                                                                         "Alias": "candidate_root_spans",
#                                                                                         "Startup Cost": 0,
#                                                                                         "Total Cost": 0.02,
#                                                                                         "Plan Rows": 1,
#                                                                                         "Plan Width": 40
#                                                                                     }
#                                                                                 ]
#                                                                             }
#                                                                         ]
#                                                                     }
#                                                                 ]
#                                                             },
#                                                             {
#                                                                 "Node Type": "Index Scan",
#                                                                 "Parent Relationship": "Inner",
#                                                                 "Parallel Aware": false,
#                                                                 "Async Capable": false,
#                                                                 "Scan Direction": "Forward",
#                                                                 "Index Name": "logs_make_object_id_coalesce_idx",
#                                                                 "Relation Name": "logs",
#                                                                 "Alias": "logs",
#                                                                 "Startup Cost": 0.42,
#                                                                 "Total Cost": 2.44,
#                                                                 "Plan Rows": 1,
#                                                                 "Plan Width": 1180,
#                                                                 "Index Cond": "((COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) = 'global_log:3b18e296-d269-4f9a-83a9-98f1a90ba06d'::text) AND (COALESCE(root_span_id, id) = candidate_root_spans.coalesced_root_span_id))"
#                                                             }
#                                                         ]
#                                                     }
#                                                 ]
#                                             }
#                                         ]
#                                     }
#                                 ]
#                             }
#                         ]
#                     }
#                 }
#             ]
#         }
#     ]
# }
