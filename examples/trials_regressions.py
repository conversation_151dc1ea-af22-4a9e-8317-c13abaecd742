import argparse
import json
import os
import random

import braintrust
from faker import Faker

trial_count = 2

# inputs = ["AAA", "BBB", "CCC", "DDD", "EEE", "FFF"]
# inputs = ["TRIAL_1", "TRIAL_2"]
inputs = [f"TRIAL_{i}" for i in range(1, 501)]
scores = [
    "score1",
    "score2",
    "score3",
    "score4",
    "score5",
]

if __name__ == "__main__":
    fake = Faker()

    experiment = braintrust.init("trials-regressions", experiment="trials_regressions")
    for row in inputs:
        # trial count = 10
        for i in range(10):
            experiment.log(
                input=row,
                output=f"{row}_trial_{i}",
                expected=fake.xml(),
                metadata={"dataset": "in" if random.random() > 0.5 else "out", "value": row},
                scores={score: None if random.random() > 0.5 else random.random() for score in scores},
            )

    """
    experiment.log(
        input="NULL",
        output=fake.xml(),
        metadata={"dataset": "in" if random.random() > 0.5 else "out"},
        scores={score: None for score in scores},
    )
    """

    print(experiment.summarize())


"""

with e1 as (select data->>'inputs' as input, AVG((scores->'score1')::float) from logs where experiment_id='306dfa58-4e7d-4867-a929-355eb1375e22' group by 1 order by input),
e2 as (select data->>'inputs' as input, AVG((scores->'score1')::float) from logs where experiment_id='24d989f5-d8e5-4484-9d24-055947550585' group by 1 order by input)
select * from e1 join e2 on e1 IS NOT DISTINCT FROM e1;
"""
