import argparse
import os
from uuid import uuid4

import requests

API_URL = "https://api.braintrust.dev/"
headers = {"Authorization": "Bearer " + os.environ["BRAINTRUST_API_KEY"]}


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--project-id", type=str, required=True)
    parser.add_argument("--user-id", type=str, required=True)
    args = parser.parse_args()

    # Find all rows matching a certain metadata value.
    query = f"""
    select: id
    from: project_logs('{args.project_id}') traces
    filter: metadata.user_id = '{args.user_id}'
    """

    response = requests.post(f"{API_URL}/btql", headers=headers, json={"query": query}).json()
    ids = [x["id"] for x in response["data"]]
    print("Deleting", len(ids), "rows")

    delete_requests = [{"id": id, "_object_delete": True} for id in ids]
    response = requests.post(
        f"{API_URL}/v1/project_logs/{args.project_id}/insert", headers=headers, json={"events": delete_requests}
    ).json()
    row_ids = response["row_ids"]
    print("Deleted", len(row_ids), "rows")
