import * as braintrust from "braintrust";
import { Browserbase } from "@browserbasehq/sdk";
import { z } from "zod";

let browserbase: Browserbase;
async function load({ url }: { url: string }): Promise<{
  page: string;
}> {
  if (!browserbase) {
    browserbase = new Browserbase();
  }

  const text = await browserbase.load(url, {
    textContent: true,
  });
  if (typeof text !== "string") {
    let page = "";
    for await (const item of text) {
      page += item;
    }
    return { page };
  } else {
    return { page: text };
  }
}

const project = braintrust.projects.create({ name: "browse test" });

project.tools.create({
  handler: load,
  parameters: z.object({
    url: z.string(),
  }),
  returns: z.object({
    page: z.string(),
  }),
  name: "Load page",
  slug: "load-page",
  description: "Load a page from the internet",
  ifExists: "replace",
});
