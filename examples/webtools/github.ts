import * as braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({ name: "github" });

const getLatestCommit = project.tools.create({
  handler: async ({ org, repo }: { org: string; repo: string }) => {
    const url = `https://api.github.com/repos/${org}/${repo}/commits?per_page=1`;
    const response = await fetch(url);

    if (!response.ok) {
      return {
        code: response.status,
        message: response.statusText,
        data: await response.text(),
      };
    }

    const data = await response.json();
    return data[0];
  },
  name: "Get git info",
  description: "Get the latest commit in a repository",
  parameters: z.object({
    org: z.string(),
    repo: z.string(),
  }),
  ifExists: "replace",
});

project.prompts.create({
  name: "Get latest commit",
  messages: [
    {
      role: "system",
      content:
        "You can answer questions about Github. Make sure to just return the answer to the question" +
        " without explanations",
    },
    {
      role: "user",
      content: "{{{question}}}",
    },
  ],
  tools: [getLatestCommit],
  model: "gpt-4o",
  ifExists: "replace",
});
