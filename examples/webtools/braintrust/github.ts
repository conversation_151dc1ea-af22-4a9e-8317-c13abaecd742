// This file was automatically generated by braintrust pull. You can
// generate it again by running:
//  $ braintrust pull --project-name "github"
// Feel free to edit this file manually, but once you do, you should make sure to
// sync your changes with Braintrust by running:
//  $ braintrust push "braintrust/github.ts"

import braintrust from "braintrust";

const project = braintrust.projects.create({
  name: "github",
});

export const repoChecker_07b0 = project.prompts.create({
  name: "repo checker",
  slug: "repo-checker-07b0",
  model: "gpt-4o",
  messages: [
    {
      content:
        "Who created the last commit in the {{repo}} repository on Github?",
      role: "system",
    },
  ],

  tools: [{ type: "function", id: "1cd011e0-39c1-4c89-8925-e607016558d0" }],
});
