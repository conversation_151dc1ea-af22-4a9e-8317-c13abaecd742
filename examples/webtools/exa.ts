import braintrust from "braintrust";
import Exa from "exa-js";
import { z } from "zod";

const project = braintrust.projects.create({ name: "browse test" });

project.tools.create({
  handler: async ({ query }: { query: string }) => {
    const exa = new Exa(process.env.EXA_API_KEY);
    const result = await exa.searchAndContents(query, {
      type: "neural",
      useAutoprompt: true,
      numResults: 10,
      text: true,
    });
    return result.results.map((t) => ({
      title: t.title,
      url: t.url,
      text: t.text,
    }));
  },
  parameters: z.object({
    query: z.string(),
  }),
  returns: z.array(
    z.object({
      title: z.string().nullable(),
      url: z.string(),
      text: z.string(),
    }),
  ),
  name: "Search",
  slug: "search-exa",
  description: "Search the internet",
  ifExists: "replace",
});
