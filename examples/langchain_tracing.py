import braintrust
from braintrust.wrappers.langchain import BraintrustTracer
from langchain.chains import LLMMathChain
from langchain.chat_models import ChatOpenAI
from langchain_core.tracers.stdout import ConsoleCallbackHandler

logger = braintrust.init_logger("langchain_test")
tracer = BraintrustTracer()

llm = ChatOpenAI(model="gpt-3.5-turbo", callbacks=[tracer])
llm_math = LLMMathChain.from_llm(llm, callbacks=[tracer])
print(llm_math.invoke("What is 1+1?"))
