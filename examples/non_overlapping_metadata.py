import random

import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("metadata-breakdown-repro", "no-metadata")

    scores = list(range(5))
    for i in range(20):
        random.shuffle(scores)
        experiment.log(
            input=i,
            output=2,
            scores={
                **{f"retrieved_doc_urls_{i}": random.random() for i in scores},
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("metadata-breakdown-repro", "some-metadata")
    for i in range(5):
        random.shuffle(scores)
        experiment.log(
            input=i,
            output=2,
            scores={
                **{f"retrieved_doc_urls_{i}": random.random() for i in scores},
            },
            metadata={
                "this": "this",
                "that": "that",
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("metadata-breakdown-repro", "all-metadata")
    for i in range(5):
        random.shuffle(scores)
        experiment.log(
            input=i,
            output=2,
            scores={
                **{f"retrieved_doc_urls_{i}": random.random() for i in scores},
            },
            metadata={"this": "this", "that": "that", "other": {"other1": "other1val"}},
        )

    print(experiment.summarize())
