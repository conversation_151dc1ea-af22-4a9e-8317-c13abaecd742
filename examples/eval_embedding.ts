// NOTE: I would like this to be an expect test, but am stuck figuring out how to make
// the proxy work. Can convert after we sort out issues in https://github.com/braintrustdata/braintrust/pull/789

import { EmbeddingSimilarity } from "autoevals";
import { Eval } from "braintrust";

Eval("basic", {
  data: () => [{ input: "a", expected: "b" }],
  task: async (args, { span }) => {
    span.log({ metadata: { message: "In foo" } });
    return "bar";
  },
  scores: [EmbeddingSimilarity],
  experimentName: "My basic eval",
  metadata: { foo: "bar" },
});
