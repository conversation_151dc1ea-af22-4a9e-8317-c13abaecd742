#!/usr/bin/env python
"""
This script generates OTel spans with various errors, so you can see how they look in the UI.
"""

import os

import braintrust
from braintrust.span_identifier_v3 import SpanComponentsV3, SpanObjectTypeV3
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.trace import Status, StatusCode

# Configure Braintrust settings
BRAINTRUST_API_URL = os.getenv("BRAINTRUST_API_URL", "https://api.braintrustdata.com")
BRAINTRUST_API_KEY = os.getenv("BRAINTRUST_API_KEY")
PROJECT_NAME = "test-otel-experimental"


def setup_tracing():
    # Create the parent span component for Braintrust
    braintrust.login(app_url=BRAINTRUST_API_URL)

    logger = braintrust.init_logger(project=PROJECT_NAME)
    project_id = logger.project.id

    parent = SpanComponentsV3(
        object_type=SpanObjectTypeV3.PROJECT_LOGS,
        object_id=project_id,
    )

    # Configure the OTLP exporter
    exporter = OTLPSpanExporter(
        endpoint=f"{BRAINTRUST_API_URL}/otel/v1/traces",
        headers={
            "Authorization": f"Bearer {BRAINTRUST_API_KEY}",
            "x-bt-parent": parent.to_str(),
        },
    )

    # Set up the tracer provider
    provider = TracerProvider()
    processor = BatchSpanProcessor(exporter)
    provider.add_span_processor(processor)
    trace.set_tracer_provider(provider)

    return provider, trace.get_tracer(__name__)


def math():
    1 / 0


def make_some_traces():
    provider, tracer = setup_tracing()
    try:
        with tracer.start_as_current_span("otel-error-trace") as root_span:

            with tracer.start_as_current_span("record_exc") as child:
                try:
                    math()
                except Exception as e:
                    child.record_exception(e)

            with tracer.start_as_current_span("set_status") as child:
                child.set_status(Status(status_code=StatusCode.ERROR, description="oh-my"))

            with tracer.start_as_current_span("record_exc_and_set_status") as child:
                try:
                    math()
                except Exception as e:
                    child.record_exception(e)

            with tracer.start_as_current_span("multiple_exceptions") as child:
                child.record_exception(Exception("oh-my"))
                child.record_exception(Exception("oh-no"))
                child.record_exception(Exception("oh-yes"))

            with tracer.start_as_current_span("multiple_exceptions_and_status") as child:
                child.record_exception(Exception("oh-my"))
                child.record_exception(Exception("oh-no"))
                child.record_exception(Exception("oh-yes"))
                child.set_status(Status(status_code=StatusCode.ERROR, description="oh-my"))

            with tracer.start_as_current_span("no_exceptions") as child:
                pass  # all is well

    finally:
        # Make sure to flush any pending spans before exit
        provider.force_flush()


def query_btql():
    braintrust.login(app_url=BRAINTRUST_API_URL)
    logger = braintrust.init_logger(project=PROJECT_NAME)
    project_id = logger.project.id
    print(f"project_id: {project_id}")

    fetch_otel_spans_query = f"select: * from: project_logs('{project_id}')"
    print(f"fetch_otel_spans_query: {fetch_otel_spans_query}")
    ret = braintrust.app_conn().post_json(
        "/btql",
        dict(
            query=fetch_otel_spans_query,
            use_columnstore=False,
            use_brainstore=False,
        ),
    )
    print(ret)
    import pprint

    pprint.pprint(ret)


def main():
    make_some_traces()


if __name__ == "__main__":
    main()
