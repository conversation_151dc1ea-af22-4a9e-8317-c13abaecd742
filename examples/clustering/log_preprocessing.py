#!/usr/bin/env python3
"""
Shared log preprocessing functions for clustering and taxonomy discovery.
"""

import json
import time
from typing import Any, Dict, List, Optional, Tuple

import openai
from braintrust import traced, wrap_openai
from embed_logs import extract_user_content

client = wrap_openai(openai.OpenAI())


@traced
def extract_high_signal_content(
    log: Dict[str, Any], model: str = "gpt-5-nano", debug: bool = False, categorization_guide: Optional[str] = None
) -> Optional[str]:
    """Extract high-signal content from a log using GPT-5-nano/mini."""
    # First try to extract user content using existing logic
    user_content = ""

    # Debug: Show what fields are in the log
    if debug:
        print(f"\nDEBUG - Log fields: {list(log.keys())}")
        if log.get("input"):
            print(f"DEBUG - Input type: {type(log['input'])}")
            print(f"DEBUG - Input preview: {str(log['input'])[:200]}...")

    # Try different approaches to find user content
    if log.get("input"):
        user_content = extract_user_content(log["input"])
        if debug and not user_content:
            print("DEBUG - No user content extracted from 'input' field")

    if not user_content and log.get("messages"):
        user_content = extract_user_content(log["messages"])
        if debug and not user_content:
            print("DEBUG - No user content extracted from 'messages' field")

    # Try looking at raw input if extraction failed
    if not user_content and log.get("input"):
        # Maybe the input is already a simple string or has different structure
        input_data = log["input"]
        if isinstance(input_data, str):
            user_content = input_data[:1000]
        elif isinstance(input_data, dict):
            # Look for any text-like content
            for key in ["text", "content", "query", "prompt", "message"]:
                if key in input_data:
                    user_content = str(input_data[key])[:1000]
                    break

    # If no user content found, skip
    if not user_content:
        if debug:
            print("DEBUG - No user content found in log")
        return None

    # Also get output for context
    output_summary = ""
    if log.get("output"):
        output_text = extract_user_content(log["output"])
        if output_text:
            output_summary = output_text[:500]

    # Extract metadata hints
    intent_hints = []
    if log.get("metadata"):
        for key in ["action", "intent", "task", "operation", "feature", "endpoint"]:
            if key in log["metadata"]:
                intent_hints.append(f"{key}: {log['metadata'][key]}")

    # Construct prompt for extraction
    if categorization_guide:
        # Use structured output for filtering
        prompt = f"""Analyze this log entry in the context of: "{categorization_guide}"

User Input: {user_content[:1000]}
{f"Output Preview: {output_summary}" if output_summary else ""}
{f"Metadata: {', '.join(intent_hints)}" if intent_hints else ""}

Determine if this log is RELEVANT to the categorization guide. If relevant, extract the key information.

Output a JSON object with:
{{
  "relevant": true/false,
  "summary": "1-2 sentence summary if relevant, focusing on aspects related to the guide",
  "reason": "brief reason why it's relevant or not"
}}"""
    else:
        # Original behavior without filtering
        prompt = f"""Extract the user's original question or request from this log entry. Preserve the user's own words and intent.

User Input: {user_content[:1000]}
{f"Output Preview: {output_summary}" if output_summary else ""}
{f"Metadata: {', '.join(intent_hints)}" if intent_hints else ""}

Provide a 1-2 sentence summary that captures what the user originally wanted to know or accomplish, using their domain language when possible. Focus on their actual question/request, not the technical implementation."""

    # Retry logic with timeout
    max_retries = 3
    timeout_seconds = 30

    for attempt in range(max_retries):
        try:
            if categorization_guide:
                response = client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.3,
                    response_format={"type": "json_object"},
                    reasoning_effort="minimal",
                    timeout=timeout_seconds,
                )

                result = json.loads(response.choices[0].message.content)
                if debug and not result.get("relevant", True):
                    print(f"DEBUG - Filtered out as irrelevant: {result.get('reason', 'no reason')}")

                # Return summary if relevant, None if not
                if result.get("relevant", True):
                    return result.get("summary", user_content[:200])
                else:
                    return None
            else:
                # Original non-filtering behavior
                response = client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=1,
                    reasoning_effort="minimal",
                    timeout=timeout_seconds,
                )
                return response.choices[0].message.content.strip()

        except (openai.APITimeoutError, openai.APIConnectionError) as e:
            if attempt < max_retries - 1:
                wait_time = 2**attempt  # Exponential backoff: 1s, 2s, 4s
                if debug:
                    print(f"API timeout/connection error on attempt {attempt + 1}, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
                continue
            else:
                print(f"API timeout/connection error after {max_retries} attempts: {e}")
                return user_content[:200]  # Fallback to raw content

        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                if debug:
                    print(f"Error on attempt {attempt + 1}, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
                continue
            else:
                print(f"Error extracting high-signal content after {max_retries} attempts: {e}")
                return user_content[:200]  # Fallback to raw content

    # Should never reach here, but fallback just in case
    return user_content[:200]


def prepare_log_for_clustering(
    log: Dict[str, Any],
    use_preprocessing: bool = False,
    preprocessing_model: str = "gpt-5-nano",
    debug: bool = False,
    categorization_guide: Optional[str] = None,
) -> Optional[str]:
    """
    Prepare a log for clustering by either extracting raw content or preprocessing with AI.

    Args:
        log: The log entry to process
        use_preprocessing: Whether to use AI preprocessing (like taxonomy approach)
        preprocessing_model: Model to use for preprocessing
        debug: Enable debug output
        categorization_guide: Optional guide for categorization focus

    Returns:
        Processed log content or None if filtered out
    """
    if use_preprocessing:
        # Use the same preprocessing as taxonomy discovery
        return extract_high_signal_content(
            log, model=preprocessing_model, debug=debug, categorization_guide=categorization_guide
        )
    else:
        # Use simpler extraction without AI preprocessing
        user_content = ""

        # Extract user content
        if log.get("input"):
            user_content = extract_user_content(log["input"])

        if not user_content and log.get("messages"):
            user_content = extract_user_content(log["messages"])

        # Include output summary
        if log.get("output"):
            output_text = extract_user_content(log["output"])
            if output_text:
                user_content += f"\nOutput: {output_text[:500]}"

        # Include relevant metadata
        if log.get("metadata"):
            for key in ["action", "intent", "task", "operation", "feature", "endpoint"]:
                if key in log["metadata"]:
                    user_content += f"\n{key}: {log['metadata'][key]}"

        return user_content if user_content else None
