# Query Logs Script

A simple script that uses natural language queries to search through Braintrust logs with LLM-based filtering. Built on top of the existing clustering infrastructure.

## Features

- **Natural Language Queries**: Search logs using plain English queries
- **LLM-Powered Filtering**: Uses OpenAI models to determine relevance and provide insights
- **Reuses Clustering Infrastructure**: Leverages existing BTQL functions and pagination from `embed_logs.py`
- **Efficient Content Extraction**: Uses existing `extract_user_content` function to filter out system prompts and tool definitions
- **Configurable Limits**: Control how many logs to scan and how many matches to return
- **JSON Output**: Save results in structured format for further analysis

## Usage

### Basic Example

```bash
cd examples/clustering
python query_logs.py \
  --api-key YOUR_BRAINTRUST_API_KEY \
  --project-name "My Project" \
  --query "find me logs related to people importing the clerk sdk"
```

### Advanced Example

```bash
cd examples/clustering
python query_logs.py \
  --api-key YOUR_BRAINTRUST_API_KEY \
  --project-name "My Project" \
  --query "users having authentication errors" \
  --limit 2000 \
  --max-matches 15 \
  --page-size 50 \
  --filter "error IS NOT NULL" \
  --model "gpt-4o-mini" \
  --output "auth_errors.json" \
  --verbose
```

## Parameters

- `--api-key`: Braintrust API key (required)
- `--project-name`: Name of the project to search (required)
- `--query`: Natural language query (required)
- `--limit`: Maximum number of logs to scan (default: 1000)
- `--max-matches`: Maximum matches to return (default: 10)
- `--page-size`: Logs per batch (default: 100)
- `--filter`: Optional BTQL filter expression
- `--openai-api-key`: OpenAI API key (or set OPENAI_API_KEY env var)
- `--model`: OpenAI model to use (default: gpt-4o-mini)
- `--output`: Output JSON file (default: query_results.json)
- `--verbose`: Show detailed progress
- `--no-deduplicate`: Keep all spans from same trace (disable deduplication)

## Code Reuse

This script leverages existing functions from `embed_logs.py`:

- `get_project_id()`: Get project ID from project name
- `fetch_logs_via_btql()`: BTQL querying with pagination support
- `extract_user_content()`: Smart content extraction that filters out system prompts and tool definitions

This ensures consistency with other clustering scripts and reduces code duplication.

## Example Queries

- `"find me logs related to people importing the clerk sdk"`
- `"users experiencing timeout errors"`
- `"logs showing successful API calls to the payments endpoint"`
- `"traces where users are trying to upload images"`
- `"authentication failures or permission denied errors"`

## How It Works

1. **Fetch Logs**: Uses `fetch_logs_via_btql()` with pagination to fetch logs in batches
2. **Content Extraction**: Uses `extract_user_content()` to extract relevant content while filtering noise
3. **LLM Analysis**: For each log, asks the LLM:
   - Is this log relevant to the query?
   - If yes, how does it relate and what's the answer?
   - Reasoning for the decision
4. **Results Collection**: Collects matches until reaching the limit or scanning all requested logs
5. **Output**: Saves structured results and prints a summary

## Output Format

The script generates a JSON file with:

```json
{
  "query": "find me logs related to people importing the clerk sdk",
  "total_matches": 3,
  "matches": [
    {
      "log_id": "log_12345",
      "answer": "User imported Clerk SDK in React component",
      "reasoning": "Log shows import statement and usage of Clerk authentication",
      "content_preview": "User Input: import { useAuth } from '@clerk/nextjs'...",
      "log": {
        /* full log object */
      }
    }
  ],
  "timestamp": 1703123456.789
}
```

## Requirements

- Python 3.7+
- Braintrust API access
- OpenAI API access
- Required packages: `openai`, `braintrust`
