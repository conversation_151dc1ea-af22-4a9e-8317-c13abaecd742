# common_substrings.py
# Self-contained implementation: Generalized SAM + Aho–<PERSON><PERSON><PERSON>

from collections import defaultdict, deque
from typing import Dict, List, Tuple


def _popcount(x: int) -> int:
    try:
        return x.bit_count()  # Python 3.8+
    except AttributeError:
        return bin(x).count("1")


class GenSuffixAutomaton:
    """
    Generalized Suffix Automaton (SAM) to index many strings at once.
    Each state keeps a bitmask of the documents in which substrings represented
    by that state occur. We reset to the root between documents so substrings
    never cross document boundaries.
    """

    __slots__ = ("next", "link", "length", "first_pos", "docmask", "_last", "_concat_pos")

    def __init__(self):
        self.next: List[Dict[str, int]] = [defaultdict(int)]  # transitions: char -> state
        self.link: List[int] = [-1]  # suffix link
        self.length: List[int] = [0]  # longest length for this state
        self.first_pos: List[int] = [-1]  # end position in concatenated stream
        self.docmask: List[int] = [0]  # bitmask of docs where this state appears
        self._last = 0
        self._concat_pos = 0

    def _new_state(self, length: int) -> int:
        self.next.append(defaultdict(int))
        self.link.append(0)
        self.length.append(length)
        self.first_pos.append(-1)
        self.docmask.append(0)
        return len(self.length) - 1

    def reset_last(self):
        self._last = 0

    def extend(self, ch: str, doc_bit: int):
        """Extend automaton with one character from a specific document (doc_bit)."""
        self._concat_pos += 1
        p = self._last
        cur = self._new_state(self.length[p] + 1)
        self.first_pos[cur] = self._concat_pos - 1
        self.docmask[cur] |= doc_bit

        while p >= 0 and ch not in self.next[p]:
            self.next[p][ch] = cur
            p = self.link[p]
        if p == -1:
            self.link[cur] = 0
        else:
            q = self.next[p][ch]
            if self.length[p] + 1 == self.length[q]:
                self.link[cur] = q
            else:
                clone = self._new_state(self.length[p] + 1)
                self.next[clone] = self.next[q].copy()
                self.link[clone] = self.link[q]
                self.first_pos[clone] = self.first_pos[q]
                self.docmask[clone] = self.docmask[q]
                while p >= 0 and self.next[p].get(ch) == q:
                    self.next[p][ch] = clone
                    p = self.link[p]
                self.link[q] = self.link[cur] = clone
        self._last = cur

    def build(self, strings: List[str]):
        """
        Build SAM over all strings. Between strings, we reset last to root and
        conceptually insert a unique separator (handled by reset).
        """
        for i, s in enumerate(strings):
            bit = 1 << i
            self.reset_last()
            for ch in s:
                self.extend(ch, bit)
            self._concat_pos += 1  # virtual separator

    def propagate_docmasks(self):
        """Propagate doc masks from longer states to their suffix-link parents."""
        order = sorted(range(len(self.length)), key=lambda i: self.length[i], reverse=True)
        for v in order:
            if self.link[v] >= 0:
                self.docmask[self.link[v]] |= self.docmask[v]


class CommonSubstringFinder:
    """
    Uses a Generalized SAM to discover substrings that appear in at least `min_docs`
    different input strings. Returns a set of *maximal* substrings to avoid redundancy.
    """

    def __init__(self, strings: List[str]):
        self.strings = strings
        # For reconstruction we keep a concatenated buffer with unique separators.
        self.concat = []
        for s in strings:
            self.concat.extend(s)
            self.concat.append("\0")  # separator not expected in normal text
        self.concat = "".join(self.concat)

    def find(self, min_docs: int = 2, min_len: int = 5) -> List[Tuple[str, int]]:
        sam = GenSuffixAutomaton()
        sam.build(self.strings)
        sam.propagate_docmasks()

        # Candidate "maximal" states: state qualifies but its suffix parent doesn't.
        res_states = []
        for v in range(1, len(sam.length)):  # skip root
            support = _popcount(sam.docmask[v])
            if support >= min_docs and sam.length[v] >= min_len:
                p = sam.link[v]
                parent_ok = p >= 0 and _popcount(sam.docmask[p]) >= min_docs and sam.length[p] >= min_len
                if not parent_ok:
                    res_states.append((v, support))

        # Reconstruct representative substrings.
        substrings = []
        for v, support in res_states:
            end = sam.first_pos[v]
            L = sam.length[v]
            start = max(end - L + 1, 0)
            end = min(end, len(self.concat) - 1)
            sub = self.concat[start : end + 1]
            # Avoid crossing separators: pick the longest piece between them.
            if "\0" in sub:
                parts = sub.split("\0")
                sub = max(parts, key=len)
                if len(sub) < min_len:
                    continue
            substrings.append((sub, support))

        # Keep only text-maximal: drop any substring contained in a longer one.
        items = sorted(substrings, key=lambda kv: len(kv[0]), reverse=True)
        kept = []
        for s, sup in items:
            if any(s in t for t, _ in kept):
                continue
            kept.append((s, sup))
        return kept


class _ACNode:
    __slots__ = ("next", "fail", "out")

    def __init__(self):
        self.next = {}
        self.fail = 0
        self.out = []  # list of pattern indices


class AhoCorasick:
    def __init__(self, patterns: List[str]):
        self.patterns = patterns
        self.nodes = [_ACNode()]
        for idx, pat in enumerate(patterns):
            self._add(pat, idx)
        self._build()

    def _add(self, pat: str, idx: int):
        v = 0
        for ch in pat:
            if ch not in self.nodes[v].next:
                self.nodes[v].next[ch] = len(self.nodes)
                self.nodes.append(_ACNode())
            v = self.nodes[v].next[ch]
        self.nodes[v].out.append(idx)

    def _build(self):
        q = deque()
        for ch, v in self.nodes[0].next.items():
            self.nodes[v].fail = 0
            q.append(v)
        while q:
            v = q.popleft()
            for ch, u in self.nodes[v].next.items():
                f = self.nodes[v].fail
                while f and ch not in self.nodes[f].next:
                    f = self.nodes[f].fail
                self.nodes[u].fail = self.nodes[f].next[ch] if ch in self.nodes[f].next else 0
                self.nodes[u].out += self.nodes[self.nodes[u].fail].out
                q.append(u)

    def find_spans(self, text: str):
        """Return list of (start, end_inclusive, pattern_index)."""
        res = []
        v = 0
        for i, ch in enumerate(text):
            while v and ch not in self.nodes[v].next:
                v = self.nodes[v].fail
            if ch in self.nodes[v].next:
                v = self.nodes[v].next[ch]
            else:
                v = 0
            for idx in self.nodes[v].out:
                L = len(self.patterns[idx])
                res.append((i - L + 1, i, idx))
        return res


def remove_substrings(text: str, patterns: List[str]) -> str:
    """Remove every (possibly overlapping) occurrence of any pattern from text."""
    if not patterns:
        return text
    ac = AhoCorasick(patterns)
    spans = ac.find_spans(text)
    if not spans:
        return text
    # Merge overlaps so we slice once
    spans = [(s, e) for s, e, _ in spans if s >= 0]
    spans.sort()
    merged = []
    cur_s, cur_e = spans[0]
    for s, e in spans[1:]:
        if s <= cur_e + 1:
            cur_e = max(cur_e, e)
        else:
            merged.append((cur_s, cur_e))
            cur_s, cur_e = s, e
    merged.append((cur_s, cur_e))

    out = []
    prev = 0
    for s, e in merged:
        if s > prev:
            out.append(text[prev:s])
        prev = e + 1
    out.append(text[prev:])
    return "".join(out)


def find_and_remove_common_substrings(strings: List[str], *, min_docs: int = 2, min_len: int = 5):
    """
    Find substrings that occur in at least `min_docs` different strings and have length >= `min_len`,
    return (common_substrings, cleaned_strings).

    - common_substrings: list of (substring, support_count) with *maximal* substrings.
    - cleaned_strings: input strings with those substrings removed.
    """
    finder = CommonSubstringFinder(strings)
    common = finder.find(min_docs=min_docs, min_len=min_len)
    patterns = [s for s, _ in sorted(common, key=lambda kv: len(kv[0]), reverse=True)]
    cleaned = [remove_substrings(s, patterns) for s in strings]
    return common, cleaned


# --- demo ---
DEMO_TEXTS = [
    "The quick brown fox jumps over the lazy dog. And the fox was quick.",
    "A quick movement of the enemy will jeopardize five gunboats. The quick brown fox!",
    "Five quacking zephyrs jolt my wax bed. The quick brown fox is legendary.",
]


def _demo():
    common, cleaned = find_and_remove_common_substrings(DEMO_TEXTS, min_docs=2, min_len=10)
    print("Common substrings (substring, support):")
    for s, k in common:
        print(f"- {repr(s)}  (in {k} docs)")
    print("\nCleaned strings:")
    for i, s in enumerate(cleaned):
        print(f"[{i}] {s}")


if __name__ == "__main__":
    _demo()
