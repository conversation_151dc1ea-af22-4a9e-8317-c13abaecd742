#!/usr/bin/env python3
"""
Discover topic taxonomy from logs using GPT-5 series models.
Uses a tiered approach: nano/mini for extraction, full model for reasoning.
"""

import argparse
import hashlib
import json
import os
import random
import time
from concurrent.futures import as_completed
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import quote

import openai
from braintrust import TracedThreadPoolExecutor, init_logger, logger, login, start_span, traced, wrap_openai
from braintrust.logger import _internal_get_global_state

# Import shared functions from embed_logs
from embed_logs import (
    compute_cache_key,
    extract_user_content,
    fetch_logs_via_btql,
    get_cache_path,
    get_project_id,
    load_from_cache,
    save_to_cache,
)
from log_preprocessing import extract_high_signal_content


def get_project_info(project_name: str) -> Tuple[str, str]:
    """Get project ID and org name from project name."""
    response = _internal_get_global_state().api_conn().get_json(f"/v1/project?name={project_name}")
    if not response.get("objects"):
        raise ValueError(f"Project '{project_name}' not found")
    project = response["objects"][0]
    return project["id"], _internal_get_global_state().org_name


client = wrap_openai(openai.OpenAI())


def parse_args():
    parser = argparse.ArgumentParser(description="Discover topic taxonomy from logs using GPT-5 models")
    parser.add_argument("--api-key", required=True, help="Braintrust API key")
    parser.add_argument(
        "--app-url",
        default="https://www.braintrust.dev",
        help="Braintrust app URL (default: https://www.braintrust.dev)",
    )
    parser.add_argument("--project-name", required=True, help="Name of the project to analyze")
    parser.add_argument(
        "--filter", help="BTQL filter expression (e.g., \"metadata.email MATCH 'braintrustdata.com'\")"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=1000,
        help="Maximum number of logs to download (default: 1000)",
    )
    parser.add_argument(
        "--sample-size",
        type=int,
        default=100,
        help="Number of logs to sample for taxonomy discovery (default: 100)",
    )
    parser.add_argument(
        "--extraction-model",
        default="gpt-5-nano",
        help="Model for high-signal extraction (default: gpt-5-nano)",
    )
    parser.add_argument(
        "--reasoning-model",
        default="gpt-5",
        help="Model for taxonomy reasoning (default: gpt-5)",
    )
    parser.add_argument(
        "--classification-model",
        default="gpt-5-nano",
        help="Model for full log classification (default: gpt-5-nano)",
    )
    parser.add_argument(
        "--enable-thinking",
        action="store_true",
        help="Enable thinking/reasoning in responses",
    )
    parser.add_argument(
        "--output",
        default="taxonomy.json",
        help="Output file for taxonomy and results (default: taxonomy.json)",
    )
    parser.add_argument(
        "--cache-dir",
        default=".cache",
        help="Directory for caching intermediate results (default: .cache)",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable caching and force re-computation",
    )
    parser.add_argument(
        "--openai-api-key",
        help="OpenAI API key (defaults to OPENAI_API_KEY env var)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug output to diagnose extraction issues",
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=10,
        help="Maximum concurrent API calls (default: 10)",
    )
    parser.add_argument(
        "--categorization-guide",
        type=str,
        help="Guide the categorization focus (e.g., 'different kinds of UIs people are trying to generate')",
    )
    parser.add_argument(
        "--no-deduplicate",
        action="store_true",
        help="Disable deduplication by root_span_id (keep all spans from same trace)",
    )
    parser.add_argument(
        "--max-examples-for-taxonomy",
        type=int,
        default=200,
        help="Maximum examples to use for taxonomy discovery (default: 200)",
    )
    parser.add_argument(
        "--show-reasoning",
        action="store_true",
        help="Show classification reasoning for sample logs in output",
    )
    parser.add_argument(
        "--no-emojis",
        action="store_true",
        help="Disable emojis in output for plain text display",
    )
    return parser.parse_args()


@traced(notrace_io=True)
def extract_high_signal_batch(
    logs: List[Dict[str, Any]],
    model: str = "gpt-5-nano",
    cache_key: Optional[str] = None,
    cache_dir: Optional[str] = None,
    use_cache: bool = True,
    debug: bool = False,
    max_concurrent: int = 10,
    categorization_guide: Optional[str] = None,
) -> List[Tuple[Dict[str, Any], str]]:
    """Extract high-signal content from multiple logs using ThreadPoolExecutor."""
    # Check cache
    if use_cache and cache_key and cache_dir:
        cache_path = get_cache_path(cache_dir, cache_key, f"high_signal_{model}")
        cached_data = load_from_cache(cache_path)
        if cached_data is not None:
            # Reconstruct tuples from cached data
            return [(log, cached_data["signals"][i]) for i, log in enumerate(logs) if i < len(cached_data["signals"])]

    print(f"Extracting high-signal content from {len(logs)} logs using {model} (max {max_concurrent} concurrent)...")

    results = []
    completed = 0

    # Use ThreadPoolExecutor for parallel processing
    with TracedThreadPoolExecutor(max_workers=max_concurrent) as executor:
        # Submit all tasks
        future_to_log = {}
        for i, log in enumerate(logs):
            # Enable debug for first few logs
            debug_this = debug and i < 3
            future = executor.submit(extract_high_signal_content, log, model, debug_this, categorization_guide)
            future_to_log[future] = log

        # Process completed futures
        for future in as_completed(future_to_log):
            log = future_to_log[future]
            try:
                signal = future.result()
                if signal:
                    results.append((log, signal))
            except Exception as e:
                print(f"Error processing log: {e}")

            completed += 1
            if completed % 10 == 0 or completed >= len(logs) - 10:
                print(f"  Processed {completed}/{len(logs)} logs...")

    # Report filtering statistics if using categorization guide
    if categorization_guide:
        filtered_count = len(logs) - len(results)
        print(f"Extracted signals from {len(results)} logs ({filtered_count} filtered out as irrelevant)")
    else:
        print(f"Extracted signals from {len(results)} logs")

    if debug and len(results) == 0:
        print("\nDEBUG - Sample log structure:")
        if logs:
            sample_log = logs[0]
            print(f"  Fields: {list(sample_log.keys())}")
            for field in ["input", "output", "messages", "metadata"]:
                if field in sample_log:
                    print(f"  {field}: {str(sample_log[field])[:200]}...")

    # Save to cache
    if use_cache and cache_key and cache_dir and results:
        cache_path = get_cache_path(cache_dir, cache_key, f"high_signal_{model}")
        cache_data = {"signals": [signal for _, signal in results], "count": len(results)}
        save_to_cache(cache_path, cache_data)

    return results


@traced(notrace_io=True)
def discover_taxonomy_with_reasoning(
    high_signal_logs: List[Tuple[Dict[str, Any], str]],
    model: str = "gpt-5",
    enable_thinking: bool = False,
    categorization_guide: Optional[str] = None,
    max_examples_for_taxonomy: int = 200,  # Increased default from 50
) -> Dict[str, Any]:
    """Use reasoning model to discover taxonomy from high-signal content."""
    # Prepare summaries for the reasoning model with IDs
    summaries = []
    id_mapping = {}  # Track index to log ID mapping
    for i, (log, signal) in enumerate(high_signal_logs[:max_examples_for_taxonomy]):
        log_id = log.get("id", f"no-id-{i}")
        summaries.append(f"{i + 1}. {signal}")
        id_mapping[i + 1] = log_id

    # Build the categorization focus section
    categorization_focus = ""
    if categorization_guide:
        categorization_focus = f"""
CATEGORIZATION FOCUS:
You must create categories that answer: "{categorization_guide}"
- Focus your categories on this specific aspect
- Ignore generic categorizations like "implementation" or "development"
- Each category should be a specific answer to the guide question

For example, if the guide is "different kinds of UIs people are trying to generate":
- Good categories: "dashboard", "form builder", "data table", "chart visualization"
- Bad categories: "ui implementation", "frontend development", "component creation"
"""

    prompt = f"""Analyze these user queries/requests and create a topic taxonomy based on what users are actually trying to accomplish or learn about.

USER QUERIES/REQUESTS:
{chr(10).join(summaries)}
{categorization_focus}
CRITICAL INSTRUCTIONS:
- Focus on the ORIGINAL USER INTENT, not the technical implementation
- Categories should reflect what the user wanted to know/do, not how the system processed it
- For example: "tell me about frustrations with X" should be categorized as "X frustrations" not "information request"
- Categories should be domain-specific and meaningful to users

CRITICAL RULE: Each category must be ONE CONCEPT ONLY.
- NO "&" or "and" in category names
- If you're tempted to use "&", STOP and pick the PRIMARY concept
- Think hard: what is the CORE USER NEED? Choose that one thing.

TASK:
1. First, define clear INCLUSION CRITERIA based on the categorization focus
   - What specific signals/evidence must be present for a log to be included?
   - Be strict - most logs should NOT meet the criteria
   - Example: If focus is "frustrations", criteria might be "explicit expressions of frustration, confusion, or inability"
2. Identify 4-8 categories that capture distinct patterns ONLY for logs meeting the inclusion criteria
3. Each category name should be 1-2 words, lowercase, reflecting user's actual goal
4. Be specific to the domain/context - avoid generic terms like "questions" or "requests"
5. If activities overlap multiple concepts, choose the most important one
6. The description can explain nuances, but the name must be singular in focus
7. IMPORTANT: Only categorize examples that meet your inclusion criteria - many may not qualify

GOOD examples (user intent focused):
- "loop frustrations" (for "tell me about frustrations with loop")
- "bug debugging" (for "help me fix this error")
- "feature requests" (for "can you add X functionality")
- "performance optimization" (for "how to make X faster")

BAD examples (too generic or technical):
- "information requests" → What specific information?
- "prompt design" → Focus on what the user actually wanted
- "technical questions" → What domain/topic?
- "system queries" → What is the user trying to achieve?

OUTPUT FORMAT:
Provide a JSON taxonomy with this structure:
{{
  "inclusion_criteria": "Clear description of what qualifies a log for categorization. Most logs should NOT meet this criteria.",
  "taxonomy": {{
    "specific-tag-name": {{
      "description": "One sentence describing this category",
      "subcategories": ["subtag-1", "subtag-2"],
      "keywords": ["keyword1", "keyword2"],
      "example_count": <exact count of examples in this category>,
      "example_indices": [1, 5, 12, ...]  // List ALL index numbers that belong to this category (can be long)
    }}
  }},
  "insights": ["Key insight 1", "Key insight 2"],
  "coverage": "Estimated percentage of samples covered by taxonomy"
}}

When referencing specific examples in descriptions or insights, use the format "example #N" where N is the index number."""

    messages = [{"role": "user", "content": prompt}]

    # Add system message if thinking is enabled
    if enable_thinking:
        messages.insert(
            0,
            {
                "role": "system",
                "content": "You are analyzing user logs to discover patterns. Think step by step about the commonalities and differences between user intents before creating the taxonomy.",
            },
        )

    # Retry logic with timeout (longer for taxonomy reasoning)
    max_retries = 3
    timeout_seconds = 300  # 5 minutes for complex taxonomy analysis

    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=1,
                response_format={"type": "json_object"} if not enable_thinking else None,
                timeout=timeout_seconds,
            )

            content = response.choices[0].message.content

            # Try to extract JSON from the response
            try:
                # If the response contains thinking, extract the JSON part
                if "```json" in content:
                    json_start = content.find("```json") + 7
                    json_end = content.find("```", json_start)
                    content = content[json_start:json_end].strip()

                taxonomy = json.loads(content)
            except json.JSONDecodeError:
                # Fallback: try to parse as-is
                taxonomy = {"error": "Failed to parse taxonomy", "raw_response": content}

            # Add the ID mapping to the taxonomy
            taxonomy["id_mapping"] = id_mapping
            return taxonomy

        except (openai.APITimeoutError, openai.APIConnectionError) as e:
            if attempt < max_retries - 1:
                wait_time = 2**attempt  # Exponential backoff: 1s, 2s, 4s
                print(f"API timeout/connection error on attempt {attempt + 1}, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
                continue
            else:
                print(f"API timeout/connection error after {max_retries} attempts: {e}")
                return {"error": f"API timeout after {max_retries} attempts: {e}", "id_mapping": {}}

        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                print(f"Error on attempt {attempt + 1}, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
                continue
            else:
                print(f"Error discovering taxonomy after {max_retries} attempts: {e}")
                return {"error": str(e), "id_mapping": {}}

    # Should never reach here, but fallback just in case
    return {"error": "Unexpected error in retry logic", "id_mapping": {}}


def classify_single_log(
    log: Dict[str, Any],
    categories: List[str],
    category_desc: Dict[str, str],
    model: str,
    log_index: int,
    org_name: str,
    project_name: str,
    app_url: str = "https://www.braintrust.dev",
    inclusion_criteria: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """Classify a single log against the taxonomy categories."""
    # Extract user content
    user_content = ""
    if log.get("input"):
        user_content = extract_user_content(log["input"])
    if not user_content and log.get("messages"):
        user_content = extract_user_content(log["messages"])

    if not user_content:
        return None

    # Classification prompt with reasoning and inclusion criteria
    inclusion_section = ""
    if inclusion_criteria:
        inclusion_section = f"""
CRITICAL INCLUSION CRITERIA:
{inclusion_criteria}

IMPORTANT: Default to "NONE" unless this activity clearly meets the above criteria.
Most activities should be classified as "NONE" because they don't meet the specific criteria.
"""

    prompt = f"""Classify this user activity based on the strict criteria provided.
{inclusion_section}
Categories (ONLY use these if the activity meets the inclusion criteria):
{chr(10).join([f"- {cat}: {desc}" for cat, desc in category_desc.items()])}
- NONE: Activity doesn't meet the inclusion criteria or doesn't clearly fit any category

User Activity: {user_content[:500]}

Please provide your response in JSON format with both the classification and your reasoning:
{{
  "category": "category_name_or_NONE",
  "reasoning": "Brief explanation including: (1) whether it meets the inclusion criteria, and (2) if yes, why it fits the chosen category"
}}"""

    # Retry logic with timeout for each classification
    max_retries = 3
    timeout_seconds = 30

    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,  # Lower temperature for more consistent JSON
                timeout=timeout_seconds,
                response_format={"type": "json_object"},
            )

            content = response.choices[0].message.content.strip()

            # Parse JSON response
            try:
                result = json.loads(content)
                category = result.get("category", "UNKNOWN")
                reasoning = result.get("reasoning", "No reasoning provided")
            except json.JSONDecodeError:
                # Fallback: try to extract category from raw text
                category = content.strip()
                reasoning = "Failed to parse reasoning from response"

            # Generate Braintrust log link with URL encoding
            log_id = log.get("id", f"log_{log_index}")
            base_url = app_url.rstrip("/")
            log_link = f"{base_url}/app/{quote(org_name)}/p/{quote(project_name)}/logs?r={quote(log_id)}"

            return {
                "log_id": log_id,
                "category": category,
                "reasoning": reasoning,
                "user_input": user_content[:200],
                "braintrust_link": log_link,
            }

        except (openai.APITimeoutError, openai.APIConnectionError) as e:
            if attempt < max_retries - 1:
                wait_time = 2**attempt  # Exponential backoff: 1s, 2s, 4s
                time.sleep(wait_time)
                continue
            else:
                print(f"API timeout/connection error classifying log {log_index} after {max_retries} attempts: {e}")
                break  # Skip this log

        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2**attempt
                time.sleep(wait_time)
                continue
            else:
                print(f"Error classifying log {log_index} after {max_retries} attempts: {e}")
                break  # Skip this log

    return None


@traced(notrace_io=True)
def classify_logs_with_taxonomy(
    logs: List[Dict[str, Any]],
    taxonomy: Dict[str, Any],
    model: str = "gpt-5-mini",
    sample_size: Optional[int] = None,
    max_concurrent: int = 10,
    org_name: str = "unknown",
    project_name: str = "unknown",
    app_url: str = "https://www.braintrust.dev",
) -> List[Dict[str, Any]]:
    """Classify logs according to discovered taxonomy using parallel processing."""
    if "error" in taxonomy or "taxonomy" not in taxonomy:
        print("Invalid taxonomy, skipping classification")
        return []

    # Create a simplified taxonomy for classification
    categories = list(taxonomy["taxonomy"].keys())
    category_desc = {cat: taxonomy["taxonomy"][cat]["description"] for cat in categories}

    # Extract inclusion criteria if present
    inclusion_criteria = taxonomy.get("inclusion_criteria", None)

    # Sample logs if requested
    logs_to_classify = logs
    if sample_size and sample_size < len(logs):
        logs_to_classify = random.sample(logs, sample_size)

    print(f"Classifying {len(logs_to_classify)} logs using {model} (parallel with max {max_concurrent} concurrent)...")

    classified_logs = []
    completed = 0

    # Use ThreadPoolExecutor for parallel processing
    with TracedThreadPoolExecutor(max_workers=max_concurrent) as executor:
        # Submit all tasks
        future_to_index = {}
        for i, log in enumerate(logs_to_classify):
            future = executor.submit(
                classify_single_log,
                log,
                categories,
                category_desc,
                model,
                i,
                org_name,
                project_name,
                app_url,
                inclusion_criteria,
            )
            future_to_index[future] = i

        # Process completed futures
        for future in as_completed(future_to_index):
            log_index = future_to_index[future]
            try:
                result = future.result()
                if result:
                    classified_logs.append(result)
            except Exception as e:
                print(f"Error processing log {log_index}: {e}")

            completed += 1
            if completed % 50 == 0 or completed >= len(logs_to_classify) - 10:
                print(f"  Classified {completed}/{len(logs_to_classify)} logs...")

    print(f"Successfully classified {len(classified_logs)} out of {len(logs_to_classify)} logs")
    return classified_logs


@traced(notrace_io=True)
def save_taxonomy_results(
    taxonomy: Dict[str, Any],
    high_signal_logs: List[Tuple[Dict[str, Any], str]],
    classified_logs: Optional[List[Dict[str, Any]]],
    output_file: str,
):
    """Save taxonomy and classification results."""
    # Extract id_mapping from taxonomy if present
    id_mapping = taxonomy.pop("id_mapping", {})

    # Process taxonomy to include log IDs with example indices
    if "taxonomy" in taxonomy and id_mapping:
        for category, details in taxonomy["taxonomy"].items():
            if "example_indices" in details:
                # Add example_logs with IDs
                details["example_logs"] = [
                    {"index": idx, "log_id": id_mapping.get(idx, f"unknown-{idx}")}
                    for idx in details["example_indices"]
                ]

    results = {
        "taxonomy": taxonomy,
        "discovery_samples": len(high_signal_logs),
        "sample_signals": [
            {
                "log_id": log.get("id", f"no-id-{i}"),
                "signal": signal,
                "input_preview": str(log.get("input", ""))[:200] if log.get("input") else None,
            }
            for i, (log, signal) in enumerate(high_signal_logs[:20])  # Save first 20 as examples
        ],
    }

    if classified_logs:
        results["classifications"] = classified_logs

        # Add category statistics
        category_counts = {}
        for cl in classified_logs:
            cat = cl["category"]
            category_counts[cat] = category_counts.get(cat, 0) + 1

        results["category_distribution"] = category_counts

    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)

    print(f"\nResults saved to {output_file}")


@traced(notrace_io=True)
def print_taxonomy_summary(taxonomy: Dict[str, Any]):
    """Print a summary of the discovered taxonomy."""
    if "error" in taxonomy:
        print(f"\nError in taxonomy discovery: {taxonomy['error']}")
        return

    print("\n" + "=" * 80)
    print("DISCOVERED TAXONOMY")
    print("=" * 80)

    # Print inclusion criteria if present
    if taxonomy.get("inclusion_criteria"):
        print(f"\nINCLUSION CRITERIA:")
        print(f"{taxonomy['inclusion_criteria']}")
        print("-" * 80)

    if "taxonomy" in taxonomy:
        for category, details in taxonomy["taxonomy"].items():
            print(f"\n{category}:")
            print(f"  Description: {details.get('description', 'N/A')}")
            if details.get("subcategories"):
                print(f"  Subcategories: {', '.join(details['subcategories'])}")
            if details.get("keywords"):
                print(f"  Keywords: {', '.join(details['keywords'][:5])}")
            if details.get("example_count"):
                print(f"  Examples in sample: ~{details['example_count']}")
            if details.get("example_logs"):
                example_strs = [f"#{ex['index']} (ID: {ex['log_id']})" for ex in details["example_logs"][:5]]
                print(f"  Example logs: {', '.join(example_strs)}")

    if taxonomy.get("insights"):
        print("\nKey Insights:")
        for insight in taxonomy["insights"]:
            print(f"  - {insight}")

    if taxonomy.get("coverage"):
        print(f"\nEstimated Coverage: {taxonomy['coverage']}")

    # Calculate and show actual example distribution
    if "taxonomy" in taxonomy:
        total_examples = sum(details.get("example_count", 0) for details in taxonomy["taxonomy"].values())
        print(f"Total examples categorized: {total_examples}")


def main():
    args = parse_args()

    # Set OpenAI API key
    openai.api_key = args.openai_api_key or os.environ.get("OPENAI_API_KEY")
    if not openai.api_key:
        raise ValueError("OpenAI API key required (--openai-api-key or OPENAI_API_KEY env var)")

    # Compute cache key - include categorization guide, sample size, and max examples
    cache_string = f"{args.api_key}:{args.project_name}:{args.limit}:{args.filter}:{args.categorization_guide or ''}:{args.sample_size}:{args.extraction_model}:{args.max_examples_for_taxonomy}"
    cache_key = hashlib.sha256(cache_string.encode()).hexdigest()[:16]
    use_cache = not args.no_cache

    if use_cache:
        print(f"Cache key: {cache_key}")
        print(f"Cache directory: {args.cache_dir}")

    # Login to Braintrust
    print(f"Connecting to Braintrust at {args.app_url}...")
    login(api_key=args.api_key, app_url=args.app_url)

    # Get project ID and org name
    project_id, org_name = get_project_info(args.project_name)
    print(f"Found project: {project_id} (org: {org_name})")

    # Fetch logs (reusing cache from embed_logs.py)
    print(f"\nFetching up to {args.limit} logs...")
    logs = fetch_logs_via_btql(
        project_id,
        args.limit,
        args.filter,
        cache_key=cache_key,
        cache_dir=args.cache_dir,
        use_cache=use_cache,
        deduplicate=not args.no_deduplicate,
        use_pagination=args.limit > 1000,  # Enable pagination for large requests
        page_size=1000,
    )
    print(f"Retrieved {len(logs)} logs")

    if not logs:
        print("No logs found matching the criteria")
        return

    login(force_login=True)
    logger = init_logger("taxonomy discovery")

    with start_span("taxonomy_discovery"):
        # Sample logs for taxonomy discovery
        sample_size = min(args.sample_size, len(logs))
        sampled_logs = random.sample(logs, sample_size)
        print(f"\nSampled {len(sampled_logs)} logs for taxonomy discovery")

        # Extract high-signal content
        high_signal_logs = extract_high_signal_batch(
            sampled_logs,
            model=args.extraction_model,
            cache_key=cache_key,
            cache_dir=args.cache_dir,
            use_cache=use_cache,
            debug=args.debug,
            max_concurrent=args.max_concurrent,
            categorization_guide=args.categorization_guide,
        )

        if not high_signal_logs:
            print("No high-signal content extracted")
            return

        # Discover taxonomy using reasoning model (with caching)
        taxonomy_cache_key = f"{cache_key}_taxonomy_{args.reasoning_model}_{args.max_examples_for_taxonomy}"
        taxonomy_cache_path = get_cache_path(args.cache_dir, taxonomy_cache_key, "taxonomy")

        taxonomy = None
        if use_cache:
            taxonomy = load_from_cache(taxonomy_cache_path)
            if taxonomy is not None:
                print(f"Loaded cached taxonomy from {taxonomy_cache_path}")

        if taxonomy is None:
            print(f"\nDiscovering taxonomy using {args.reasoning_model}...")
            if args.categorization_guide:
                print(f"Categorization focus: {args.categorization_guide}")
            examples_for_taxonomy = min(len(high_signal_logs), args.max_examples_for_taxonomy)
            print(f"Using {examples_for_taxonomy} examples for taxonomy discovery")
            taxonomy = discover_taxonomy_with_reasoning(
                high_signal_logs,
                model=args.reasoning_model,
                enable_thinking=args.enable_thinking,
                categorization_guide=args.categorization_guide,
                max_examples_for_taxonomy=args.max_examples_for_taxonomy,
            )

            # Save taxonomy to cache
            if use_cache and taxonomy and "error" not in taxonomy:
                save_to_cache(taxonomy_cache_path, taxonomy)
                print(f"Saved taxonomy to cache: {taxonomy_cache_path}")
        else:
            # Still need examples_for_taxonomy for reporting
            examples_for_taxonomy = min(len(high_signal_logs), args.max_examples_for_taxonomy)

        # Print taxonomy summary
        print_taxonomy_summary(taxonomy)

        # Always classify all logs using nano model for accurate statistics
        classified_logs = None
        if "taxonomy" in taxonomy:
            print(f"\nClassifying all {len(logs)} logs using {args.classification_model} for accurate statistics...")
            classified_logs = classify_logs_with_taxonomy(
                logs,
                taxonomy,
                model=args.classification_model,  # Use nano/mini for classification
                max_concurrent=args.max_concurrent,
                org_name=org_name,
                project_name=args.project_name,
                app_url=args.app_url,
            )

            if classified_logs:
                print(f"Successfully classified {len(classified_logs)} logs")

                # Compare discovery sample vs full classification statistics
                print(f"\nComparison: Discovery Sample vs Full Classification")
                print(f"Discovery used {examples_for_taxonomy} examples from sample of {len(sampled_logs)}")
                print(f"Full classification processed {len(classified_logs)} logs using {args.classification_model}")

                # Show category distribution from full classification
                category_counts = {}
                for cl in classified_logs:
                    cat = cl["category"]
                    category_counts[cat] = category_counts.get(cat, 0) + 1

                print(f"\nFull Classification Results:")
                print("=" * 80)
                total_classified = len(classified_logs)
                for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
                    percentage = (count / total_classified) * 100
                    # Show sample link for this category
                    sample_log = next((cl for cl in classified_logs if cl["category"] == category), None)

                    # Format category header with clear visual distinction
                    category_icon = "" if args.no_emojis else "📊 "
                    print(f"\n{category_icon}CATEGORY: {category.upper()}")
                    print(f"   Count: {count} logs ({percentage:.1f}%)")
                    if sample_log and sample_log.get("braintrust_link"):
                        print(f"   Sample: {sample_log['braintrust_link']}")

                    # Show reasoning for sample if requested
                    if args.show_reasoning and sample_log and sample_log.get("reasoning"):
                        reasoning = sample_log["reasoning"]
                        user_input = sample_log.get("user_input", "")
                        query_icon = "" if args.no_emojis else "📝 "
                        reasoning_icon = "" if args.no_emojis else "💭 "
                        print(f"\n   {query_icon}Example User Query:")
                        print(f"      \"{user_input[:150]}{'...' if len(user_input) > 150 else ''}\"")
                        print(f"\n   {reasoning_icon}Classification Reasoning:")
                        # Wrap reasoning text for better readability
                        import textwrap

                        wrapped_reasoning = textwrap.fill(
                            reasoning, width=70, initial_indent="      ", subsequent_indent="      "
                        )
                        print(wrapped_reasoning)

                    print("-" * 80)

                # Compare with discovery taxonomy predictions
                if "taxonomy" in taxonomy:
                    print(f"\nDiscovery vs Classification Comparison:")
                    discovery_categories = set(taxonomy["taxonomy"].keys())
                    classification_categories = set(category_counts.keys())

                    print(f"  Categories in discovery: {len(discovery_categories)}")
                    print(f"  Categories in classification: {len(classification_categories)}")

                    # Show categories that appear in classification but not discovery
                    new_categories = classification_categories - discovery_categories
                    if new_categories:
                        print(f"  New categories found in classification: {new_categories}")

                    # Show discovery categories that got few/no classifications
                    missing_categories = discovery_categories - classification_categories
                    if missing_categories:
                        print(f"  Discovery categories with no classifications: {missing_categories}")
            else:
                print("Failed to classify logs - this indicates the nano model may have issues")

        # Save results
        save_taxonomy_results(taxonomy, high_signal_logs, classified_logs, args.output)


if __name__ == "__main__":
    main()
