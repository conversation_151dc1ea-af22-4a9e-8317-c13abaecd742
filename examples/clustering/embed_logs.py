#!/usr/bin/env python3
"""
Download logs from Braintrust, generate embeddings, and perform clustering analysis.
"""

import argparse
import hashlib
import json
import os
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import openai
from braintrust import init_logger, logger, login, start_span, traced, wrap_openai
from braintrust.logger import _internal_get_global_state
from sklearn.cluster import DBSCAN, KMeans
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score

client = wrap_openai(openai.OpenAI())


def parse_args():
    parser = argparse.ArgumentParser(description="Download logs, generate embeddings, and perform clustering")
    parser.add_argument("--api-key", required=True, help="Braintrust API key")
    parser.add_argument(
        "--app-url",
        default="https://www.braintrust.dev",
        help="Braintrust app URL (default: https://www.braintrust.dev)",
    )
    parser.add_argument("--project-name", required=True, help="Name of the project to download logs from")
    parser.add_argument(
        "--filter", help="BTQL filter expression (e.g., \"metadata.email MATCH 'braintrustdata.com'\")"
    )
    parser.add_argument("--limit", type=int, default=1000, help="Maximum number of logs to download (default: 1000)")
    parser.add_argument(
        "--output", default="embeddings.json", help="Output file for embeddings and logs (default: embeddings.json)"
    )
    parser.add_argument(
        "--openai-api-key", help="OpenAI API key for generating embeddings (defaults to OPENAI_API_KEY env var)"
    )
    parser.add_argument(
        "--embedding-model",
        default="text-embedding-3-small",
        help="OpenAI embedding model to use (default: text-embedding-3-small)",
    )
    parser.add_argument("--n-clusters", type=int, default=5, help="Number of clusters for K-means (default: 5)")
    parser.add_argument(
        "--clustering-method",
        choices=["kmeans", "dbscan"],
        default="kmeans",
        help="Clustering method to use (default: kmeans)",
    )
    parser.add_argument(
        "--cache-dir",
        default=".cache",
        help="Directory for caching intermediate results (default: .cache)",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable caching and force re-download/re-compute",
    )
    parser.add_argument(
        "--use-ai-intent",
        action="store_true",
        default=True,
        help="Use AI to analyze user intent for cluster naming (default: True)",
    )
    parser.add_argument(
        "--no-ai-intent",
        action="store_true",
        help="Disable AI intent analysis and use rule-based naming",
    )
    parser.add_argument(
        "--intent-focused-embeddings",
        action="store_true",
        help="Generate embeddings focused on user intent rather than technical details",
    )
    parser.add_argument(
        "--debug-extraction",
        action="store_true",
        help="Show examples of content extraction to debug noise filtering",
    )
    parser.add_argument(
        "--debug-intent",
        action="store_true",
        help="Show debug output for AI intent analysis to diagnose clustering issues",
    )
    parser.add_argument(
        "--no-deduplicate",
        action="store_true",
        help="Disable deduplication by root_span_id (keep all spans from same trace)",
    )
    parser.add_argument(
        "--categorization-guide",
        type=str,
        help="Guide the clustering focus (e.g., 'different kinds of UIs people are trying to generate')",
    )
    parser.add_argument(
        "--use-preprocessing",
        action="store_true",
        help="Use AI preprocessing like taxonomy approach (extracts high-signal content with GPT-5-nano)",
    )
    parser.add_argument(
        "--preprocessing-model",
        default="gpt-5-nano",
        help="Model for preprocessing logs (default: gpt-5-nano)",
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=10,
        help="Maximum concurrent API calls for preprocessing (default: 10)",
    )
    return parser.parse_args()


def compute_cache_key(
    api_key: str,
    project_name: str,
    limit: int,
    filter_expr: Optional[str],
    categorization_guide: Optional[str] = None,
    use_preprocessing: bool = False,
    preprocessing_model: Optional[str] = None,
    **kwargs,  # Accept additional parameters that might affect caching
) -> str:
    """Compute a cache key based on the parameters."""
    # Use only the last 8 chars of API key for privacy
    key_suffix = api_key[-8:] if len(api_key) > 8 else api_key

    # Build cache string with all parameters
    cache_parts = [
        key_suffix,
        project_name,
        str(limit),
        filter_expr or "no-filter",
        categorization_guide or "",
        str(use_preprocessing),
        preprocessing_model or "",
    ]

    # Add any additional kwargs that affect caching
    for key, value in sorted(kwargs.items()):
        if value is not None:
            cache_parts.append(f"{key}:{value}")

    cache_string = ":".join(cache_parts)
    return hashlib.sha256(cache_string.encode()).hexdigest()[:16]


def get_cache_path(cache_dir: str, cache_key: str, suffix: str) -> Path:
    """Get the cache file path for a given key and suffix."""
    cache_path = Path(cache_dir)
    cache_path.mkdir(exist_ok=True)
    return cache_path / f"{cache_key}_{suffix}.json"


def load_from_cache(cache_path: Path) -> Optional[Any]:
    """Load data from cache if it exists."""
    if cache_path.exists():
        print(f"Loading from cache: {cache_path}")
        with open(cache_path, "r") as f:
            return json.load(f)
    return None


def save_to_cache(cache_path: Path, data: Any):
    """Save data to cache."""
    print(f"Saving to cache: {cache_path}")
    with open(cache_path, "w") as f:
        json.dump(data, f, indent=2)


@traced
def get_project_id(project_name: str) -> str:
    """Get project ID from project name."""
    response = _internal_get_global_state().api_conn().get_json(f"/v1/project?name={project_name}")
    if not response.get("objects"):
        raise ValueError(f"Project '{project_name}' not found")
    return response["objects"][0]["id"]


def deduplicate_by_root_span(logs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Keep only one span per root_span_id to reduce noise from multiple spans in same trace.

    Prioritizes:
    1. Spans where span_id == root_span_id (i.e., the root span itself)
    2. Spans with user input/messages
    3. First span encountered
    """
    # Group logs by root_span_id
    traces = {}
    logs_without_root = []

    for log in logs:
        root_span_id = log.get("root_span_id")
        if root_span_id is None:
            # If no root_span_id, include the log
            logs_without_root.append(log)
        else:
            if root_span_id not in traces:
                traces[root_span_id] = []
            traces[root_span_id].append(log)

    # Select best span from each trace
    deduplicated = []

    for root_span_id, trace_logs in traces.items():
        if len(trace_logs) == 1:
            # Only one span, use it
            deduplicated.append(trace_logs[0])
        else:
            # Multiple spans, choose the best one
            best_log = None

            # First priority: root span itself
            for log in trace_logs:
                if log.get("span_id") == root_span_id:
                    best_log = log
                    break

            # Second priority: span with user content
            if best_log is None:
                for log in trace_logs:
                    if log.get("input") or log.get("messages"):
                        # Check if it has actual user content
                        user_content = extract_user_content(log.get("input", log.get("messages", "")))
                        if user_content:
                            best_log = log
                            break

            # Fallback: first span
            if best_log is None:
                best_log = trace_logs[0]

            deduplicated.append(best_log)

    # Add back logs without root_span_id
    deduplicated.extend(logs_without_root)

    if len(deduplicated) < len(logs):
        print(
            f"Deduplicated from {len(logs)} to {len(deduplicated)} logs (removed {len(logs) - len(deduplicated)} duplicate spans)"
        )

    return deduplicated


@traced(notrace_io=True)
def fetch_logs_via_btql(
    project_id: str,
    limit: int,
    filter_expr: Optional[str] = None,
    cache_key: Optional[str] = None,
    cache_dir: Optional[str] = None,
    use_cache: bool = True,
    deduplicate: bool = True,
    use_pagination: bool = False,
    page_size: int = 1000,
    state=None,
) -> List[Dict[str, Any]]:
    """Fetch logs using BTQL query with caching support and optional pagination."""
    # Check cache first
    if use_cache and cache_key and cache_dir:
        cache_path = get_cache_path(cache_dir, cache_key, "logs")
        cached_data = load_from_cache(cache_path)
        if cached_data is not None:
            return cached_data

    state = state or _internal_get_global_state()
    all_logs = []

    if use_pagination and limit > page_size:
        # Use pagination to fetch logs in batches
        cursor = None
        remaining_limit = limit

        while remaining_limit > 0:
            batch_limit = min(page_size, remaining_limit)

            query = f"""
    select: *
    from: project_logs('{project_id}') traces"""

            if filter_expr:
                query += f"\n    filter: {filter_expr}"

            if cursor:
                query += f"\n    cursor: '{cursor}'"

            query += f"\n    limit: {batch_limit}"

            print(
                f"Executing paginated BTQL query (batch {len(all_logs) // page_size + 1}, limit: {batch_limit}):\n{query}"
            )

            response = state.api_conn().post_json("/btql", {"query": query.strip(), "fmt": "json"})

            batch_logs = response.get("data", [])
            cursor = response.get("cursor")

            if not batch_logs:
                print("No more logs available")
                break

            all_logs.extend(batch_logs)
            remaining_limit -= len(batch_logs)

            print(f"  Fetched {len(batch_logs)} logs (total: {len(all_logs)})")

            # If no cursor is returned, we've reached the end
            if not cursor:
                print("Reached end of results (no cursor returned)")
                break
    else:
        # Original single-query behavior
        query = f"""
    select: *
    from: project_logs('{project_id}') traces"""

        if filter_expr:
            query += f"\n    filter: {filter_expr}"

        query += f"\n    limit: {limit}"

        print(f"Executing BTQL query:\n{query}")

        response = state.api_conn().post_json("/btql", {"query": query.strip(), "fmt": "json"})

        all_logs = response.get("data", [])

    # Deduplicate by root_span_id if requested
    if deduplicate:
        all_logs = deduplicate_by_root_span(all_logs)

    # Save to cache
    if use_cache and cache_key and cache_dir:
        cache_path = get_cache_path(cache_dir, cache_key, "logs")
        save_to_cache(cache_path, all_logs)

    return all_logs


def is_system_prompt(text: str) -> bool:
    """Detect if text is likely a system prompt."""
    if not isinstance(text, str):
        return False

    # Common system prompt indicators
    system_indicators = [
        "you are",
        "you must",
        "you should",
        "you will",
        "your role",
        "your task",
        "your purpose",
        "assistant:",
        "system:",
        "instructions:",
        "follow these",
        "adhere to",
        "remember to",
        "do not",
        "don't",
        "never",
        "always",
        "you are a",
        "you are an",
        "act as",
        "respond as",
        "behave as",
        "pretend to be",
    ]

    text_lower = text.lower()[:500]  # Check first 500 chars
    matches = sum(1 for indicator in system_indicators if indicator in text_lower)

    # If multiple indicators, likely a system prompt
    return matches >= 3


def is_tool_definition(text: str) -> bool:
    """Detect if text is likely a tool/function definition."""
    if not isinstance(text, str):
        return False

    # Common tool definition patterns
    tool_indicators = [
        '"type":',
        '"properties":',
        '"required":',
        '"parameters":',
        '"description":',
        '"$schema":',
        "function_calls",
        "tool_calls",
        "tools:",
        '"additionalProperties":',
        '"enum":',
        '"items":',
        "def ",
        "function ",
        "async def",
        "```python",
        "```javascript",
        "```typescript",
    ]

    text_sample = text[:1000]
    matches = sum(1 for indicator in tool_indicators if indicator in text_sample)

    # Check for JSON schema pattern
    if matches >= 3 or ('"type":' in text_sample and '"properties":' in text_sample):
        return True

    # Check if it's mostly JSON
    try:
        # Count JSON-like patterns
        json_patterns = len(re.findall(r'["{}\[\]:]', text_sample))
        if json_patterns / max(len(text_sample), 1) > 0.15:  # More than 15% JSON chars
            return True
    except:
        pass

    return False


def extract_user_content(data: Any) -> str:
    """Extract only user-generated content from various data structures."""
    if isinstance(data, str):
        if not is_system_prompt(data) and not is_tool_definition(data):
            return data
        return ""

    elif isinstance(data, list):
        # Handle message lists (common in chat logs)
        user_content = []
        for item in data:
            if isinstance(item, dict):
                # Skip system/assistant messages, focus on user messages
                role = item.get("role", "")
                content = item.get("content", item.get("message", item.get("text", "")))

                if role in ["user", "human"] and content:
                    user_content.append(str(content))
                elif not role and content and not is_system_prompt(str(content)):
                    # No role specified, but content doesn't look like system prompt
                    user_content.append(str(content))

        return " ".join(user_content)

    elif isinstance(data, dict):
        # Look for user content in various keys
        user_keys = [
            "user",
            "human",
            "query",
            "question",
            "prompt",
            "input",
            "user_input",
            "message",
            "text",
            "content",
            "request",
            "user_message",
        ]

        for key in user_keys:
            if key in data and data[key]:
                content = extract_user_content(data[key])
                if content:
                    return content

        # If no specific user key, check all values but filter system content
        user_content = []
        for key, value in data.items():
            if key not in ["system", "assistant", "tools", "functions", "schema"]:
                content = extract_user_content(value)
                if content and len(content) > 10:  # Ignore very short content
                    user_content.append(content)

        return " ".join(user_content)

    return ""


def create_log_text(log: Dict[str, Any], max_chars: int = 8000, focus_on_intent: bool = False) -> str:
    """Create a text representation of a log for embedding.

    Truncates to avoid hitting token limits (roughly 4 chars per token).
    If focus_on_intent is True, prioritizes user-facing content over technical details.
    """
    parts = []

    if focus_on_intent:
        # For intent-focused embeddings, extract only user content and filter noise

        # Extract user content from input, filtering out system prompts and tool definitions
        if log.get("input"):
            user_input = extract_user_content(log["input"])
            if user_input:
                parts.append(f"User: {user_input[:2000]}")

        # Also check for messages field (common in chat logs)
        if log.get("messages"):
            user_messages = extract_user_content(log["messages"])
            if user_messages and user_messages not in str(parts):
                parts.append(f"User messages: {user_messages[:2000]}")

        # Include relevant metadata that indicates user intent
        if log.get("metadata"):
            intent_keys = ["action", "intent", "task", "operation", "feature", "use_case", "purpose"]
            for key in intent_keys:
                if key in log["metadata"]:
                    parts.append(f"{key.capitalize()}: {log['metadata'][key]}")

            # Also include endpoint but clean it up
            if "endpoint" in log["metadata"]:
                endpoint = log["metadata"]["endpoint"]
                # Remove common prefixes
                endpoint = endpoint.replace("/api/v1/", "").replace("/api/", "")
                parts.append(f"Action: {endpoint}")

        # Output - but try to extract meaningful results only
        if log.get("output"):
            output_data = log["output"]
            # Try to extract user-visible content from output
            output_text = extract_user_content(output_data)
            if output_text:
                parts.append(f"Result: {output_text[:1000]}")
            else:
                # Fallback to summary if no clear user content
                output_str = json.dumps(output_data)
                if len(output_str) < 500 and not is_tool_definition(output_str):
                    parts.append(f"Result: {output_str}")

    else:
        # Original behavior for general embeddings
        if log.get("input"):
            input_str = json.dumps(log["input"])
            if len(input_str) > 2000:
                input_str = input_str[:2000] + "..."
            parts.append(f"Input: {input_str}")

        if log.get("output"):
            output_str = json.dumps(log["output"])
            if len(output_str) > 2000:
                output_str = output_str[:2000] + "..."
            parts.append(f"Output: {output_str}")

        if log.get("error"):
            error_str = str(log["error"])
            if len(error_str) > 1000:
                error_str = error_str[:1000] + "..."
            parts.append(f"Error: {error_str}")

        if log.get("metrics"):
            parts.append(f"Metrics: {json.dumps(log['metrics'])}")

        if log.get("metadata"):
            parts.append(f"Metadata: {json.dumps(log['metadata'])}")

        # Include span information
        if log.get("span_attributes"):
            span_str = json.dumps(log["span_attributes"])
            if len(span_str) > 500:
                span_str = span_str[:500] + "..."
            parts.append(f"Span: {span_str}")

    result = "\n".join(parts) if parts else json.dumps(log)

    # Final truncation to ensure we don't exceed limits
    if len(result) > max_chars:
        result = result[:max_chars] + "..."

    return result


@traced(notrace_io=True)
def generate_embeddings(
    logs: List[Dict[str, Any]],
    openai_api_key: str,
    model: str = "text-embedding-3-small",
    cache_key: Optional[str] = None,
    cache_dir: Optional[str] = None,
    use_cache: bool = True,
    focus_on_intent: bool = False,
    use_preprocessing: bool = False,
    preprocessing_model: str = "gpt-5-nano",
    categorization_guide: Optional[str] = None,
    debug: bool = False,
    max_concurrent: int = 10,
) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
    """Generate embeddings for logs using OpenAI with caching support.

    Returns:
        Tuple of (embeddings array, filtered logs list)
    """
    # Import preprocessing function
    from log_preprocessing import prepare_log_for_clustering

    # Check cache first
    if use_cache and cache_key and cache_dir:
        suffix = f"embeddings_{model.replace('/', '_')}"
        if focus_on_intent:
            suffix += "_intent"
        if use_preprocessing:
            suffix += f"_preprocessed_{preprocessing_model}"
        cache_path = get_cache_path(cache_dir, cache_key, suffix)
        cached_data = load_from_cache(cache_path)
        if cached_data is not None:
            # Return cached embeddings and logs
            return np.array(cached_data["embeddings"]), cached_data["logs"]

    # Process logs and create text representations
    if use_preprocessing:
        # Import parallel processing utilities
        from concurrent.futures import as_completed

        from braintrust import TracedThreadPoolExecutor
        from log_preprocessing import prepare_log_for_clustering

        print(f"Preprocessing {len(logs)} logs with {preprocessing_model} (parallel)...")
        preprocessing_start = time.time()
        processed_data = []
        filtered_logs = []
        completed = 0
        preprocessing_times = []

        # Use ThreadPoolExecutor for parallel processing
        with TracedThreadPoolExecutor(max_workers=max_concurrent) as executor:
            # Submit all tasks
            future_to_log = {}
            for i, log in enumerate(logs):
                debug_this = debug and i < 3  # Debug first 3
                future = executor.submit(
                    prepare_log_for_clustering,
                    log,
                    use_preprocessing=True,
                    preprocessing_model=preprocessing_model,
                    debug=debug_this,
                    categorization_guide=categorization_guide,
                )
                future_to_log[future] = log

            # Process completed futures
            for future in as_completed(future_to_log):
                log = future_to_log[future]
                log_start = time.time()
                try:
                    content = future.result()
                    if content:
                        processed_data.append(content)
                        filtered_logs.append(log)
                        preprocessing_times.append(time.time() - log_start)
                except Exception as e:
                    print(f"Error preprocessing log: {e}")

                completed += 1
                if completed % 50 == 0:
                    print(f"  Processed {completed}/{len(logs)} logs...")

        preprocessing_total = time.time() - preprocessing_start
        if preprocessing_times:
            avg_preprocessing_time = sum(preprocessing_times) / len(preprocessing_times)
            print(
                f"Preprocessing completed in {preprocessing_total:.2f}s total (avg {avg_preprocessing_time:.3f}s per log)"
            )

        texts = processed_data
        logs = filtered_logs
        if categorization_guide and len(filtered_logs) < len(future_to_log):
            filtered_count = len(future_to_log) - len(filtered_logs)
            print(f"After preprocessing: {len(texts)} logs remain ({filtered_count} filtered out as irrelevant)")
        else:
            print(f"After preprocessing: {len(texts)} logs remain")
    else:
        # Original behavior
        texts = [create_log_text(log, focus_on_intent=focus_on_intent) for log in logs]
        filtered_logs = logs

    print(
        f"Generating embeddings for {len(texts)} logs{' (preprocessed)' if use_preprocessing else ' (intent-focused)' if focus_on_intent else ''}..."
    )

    # Generate embeddings in batches
    embeddings = []
    batch_size = 20  # Reduced batch size to avoid token limits
    embedding_start = time.time()
    embedding_times = []

    for i in range(0, len(texts), batch_size):
        batch = texts[i : i + batch_size]
        batch_start = time.time()
        try:
            response = client.embeddings.create(input=batch, model=model)
            batch_embeddings = [item.embedding for item in response.data]
            embeddings.extend(batch_embeddings)
            batch_time = time.time() - batch_start
            embedding_times.extend([batch_time / len(batch)] * len(batch))  # Distribute time across batch items
            print(f"  Processed {min(i + batch_size, len(texts))}/{len(texts)} logs...")
        except openai.BadRequestError as e:
            # If we still hit token limits, process one by one
            print(f"  Batch too large, processing individually...")
            for j, text in enumerate(batch):
                individual_start = time.time()
                try:
                    response = client.embeddings.create(input=[text], model=model)
                    embeddings.extend([item.embedding for item in response.data])
                    embedding_times.append(time.time() - individual_start)
                except Exception as e2:
                    print(f"    Error processing log {i + j}: {e2}")
                    # Use zero embedding as fallback
                    embeddings.append([0.0] * 1536)  # Default dimension for text-embedding-3-small
                    embedding_times.append(time.time() - individual_start)

    embedding_total = time.time() - embedding_start
    if embedding_times:
        avg_embedding_time = sum(embedding_times) / len(embedding_times)
        print(f"Embedding completed in {embedding_total:.2f}s total (avg {avg_embedding_time:.3f}s per log)")

    embeddings_array = np.array(embeddings)

    # Save to cache
    if use_cache and cache_key and cache_dir:
        suffix = f"embeddings_{model.replace('/', '_')}"
        if focus_on_intent:
            suffix += "_intent"
        if use_preprocessing:
            suffix += f"_preprocessed_{preprocessing_model}"
        cache_path = get_cache_path(cache_dir, cache_key, suffix)
        save_to_cache(cache_path, {"embeddings": embeddings_array.tolist(), "logs": filtered_logs})

    return embeddings_array, filtered_logs


@traced
def perform_clustering(embeddings: np.ndarray, method: str = "kmeans", n_clusters: int = 5) -> np.ndarray:
    """Perform clustering on embeddings."""
    print(f"\nPerforming {method} clustering...")

    if method == "kmeans":
        clusterer = KMeans(n_clusters=n_clusters, random_state=42)
        labels = clusterer.fit_predict(embeddings)

        # Calculate silhouette score
        if len(set(labels)) > 1:
            score = silhouette_score(embeddings, labels)
            print(f"Silhouette score: {score:.3f}")

    elif method == "dbscan":
        # Use cosine distance for DBSCAN
        from sklearn.metrics.pairwise import cosine_distances

        distances = cosine_distances(embeddings)

        clusterer = DBSCAN(eps=0.3, min_samples=5, metric="precomputed")
        labels = clusterer.fit_predict(distances)

        n_clusters_found = len(set(labels)) - (1 if -1 in labels else 0)
        n_noise = list(labels).count(-1)
        print(f"Found {n_clusters_found} clusters and {n_noise} noise points")

    return labels


@traced
def analyze_user_intent_with_ai(
    cluster_logs: List[Dict[str, Any]],
    openai_api_key: str,
    sample_size: int = 10,
    categorization_guide: Optional[str] = None,
    debug: bool = False,
) -> Tuple[str, str]:
    """Use GPT to analyze a sample of logs and identify the user intent/use case."""

    # Sample logs from the cluster
    sample_logs = cluster_logs[:sample_size]

    # Create a prompt with log samples, filtering out noise
    log_summaries = []
    debug_info = []

    if debug:
        print(f"DEBUG: Processing {len(sample_logs)} sample logs for cluster analysis")

    for i, log in enumerate(sample_logs):
        summary_parts = []

        # Extract clean user content
        if log.get("input"):
            user_input = extract_user_content(log["input"])
            if user_input:
                summary_parts.append(f"User Input: {user_input[:500]}")
            else:
                # Debug: try raw input if extraction failed
                raw_input = str(log["input"])[:200]
                if raw_input and not is_tool_definition(raw_input):
                    summary_parts.append(f"Raw Input: {raw_input}")
                    debug_info.append(f"Log {i + 1}: User content extraction failed, using raw input")

        if log.get("messages"):
            user_messages = extract_user_content(log["messages"])
            if user_messages:
                summary_parts.append(f"User Messages: {user_messages[:500]}")
            else:
                # Debug: try to extract from raw messages
                if isinstance(log["messages"], list) and log["messages"]:
                    for msg in log["messages"]:
                        if isinstance(msg, dict) and msg.get("role") == "user":
                            raw_content = str(msg.get("content", ""))[:200]
                            if raw_content:
                                summary_parts.append(f"User Message: {raw_content}")
                                debug_info.append(f"Log {i + 1}: Used raw message content")
                                break

        # Include clean output
        if log.get("output"):
            output_text = extract_user_content(log["output"])
            if output_text:
                summary_parts.append(f"Output: {output_text[:300]}")
            else:
                # Try to get a short summary if no user content
                output_str = str(log["output"])[:200]
                if not is_tool_definition(output_str):
                    summary_parts.append(f"Output: {output_str}")

        if log.get("metadata"):
            # Include relevant metadata that might indicate intent
            relevant_keys = ["endpoint", "function", "action", "intent", "task", "operation", "feature", "use_case"]
            relevant_metadata = {k: v for k, v in log["metadata"].items() if k in relevant_keys}
            if relevant_metadata:
                summary_parts.append(f"Metadata: {json.dumps(relevant_metadata)}")

        if summary_parts:
            log_summaries.append(f"Log {i + 1}:\n" + "\n".join(summary_parts))
        else:
            debug_info.append(f"Log {i + 1}: No extractable content found - keys: {list(log.keys())}")

    # Debug output if no summaries were created
    if not log_summaries:
        if debug:
            print(f"DEBUG: No log summaries created for cluster analysis!")
            for info in debug_info[:3]:  # Show first 3 debug messages
                print(f"  {info}")
            print(f"  Sample log fields: {list(sample_logs[0].keys()) if sample_logs else 'No logs'}")
        return "Unknown Intent", "Cluster of logs with unclear user intent."

    # Build categorization focus if provided
    categorization_focus = ""
    if categorization_guide:
        categorization_focus = f"""
CATEGORIZATION FOCUS:
Analyze these logs specifically in the context of: "{categorization_guide}"
Focus your analysis on how these logs relate to this specific aspect.

"""

    prompt = f"""Analyze these log samples and identify the common user intent or use case pattern.
{categorization_focus}
{chr(10).join(log_summaries[:5])}  # Limit to 5 for token efficiency

CRITICAL RULE: The name must be ONE CONCEPT ONLY.
- NO "&" or "and" in the name
- If you're tempted to use "&", STOP and pick the PRIMARY concept
- Think hard: what is the CORE activity? Choose that one thing.

Based on these logs, provide:
1. A 1-2 word name for this use case, lowercase, like a tag
2. A one-sentence description of what users are trying to accomplish
3. Be SPECIFIC - avoid generic terms like "processing", "management", "implementation"
4. Focus on the domain/context{f" specifically related to: {categorization_guide}" if categorization_guide else ""}

GOOD examples:
- "dashboard creation" → "dashboard"
- "form building" → "forms"
- "chart generation" → "charts"
- "api debugging" → "debugging"

BAD examples:
- "data processing" → too generic
- "content & media" → no "&" allowed
- "UI Development" → not lowercase, too broad

Format your response as:
NAME: [1-2 word lowercase tag]
DESCRIPTION: [One sentence description]"""

    try:
        response = client.chat.completions.create(
            model="gpt-5-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=200,  # Increased token limit
            reasoning_effort="minimal",
        )

        content = response.choices[0].message.content
        if debug:
            print(f"DEBUG: AI response for cluster analysis: {content}")  # Debug output

        lines = content.strip().split("\n")

        name = "Unknown Intent"
        description = "Cluster of logs with unclear user intent."

        # More flexible parsing
        for line in lines:
            line = line.strip()
            if line.startswith("NAME:") or line.startswith("Name:"):
                name = line.split(":", 1)[1].strip()
                # Remove quotes if present
                name = name.strip("\"'")
            elif line.startswith("DESCRIPTION:") or line.startswith("Description:"):
                description = line.split(":", 1)[1].strip()
                # Remove quotes if present
                description = description.strip("\"'")

        # Fallback: try to extract from anywhere in the response
        if name == "Unknown Intent" and content:
            # Look for patterns like "tag: something" or just extract meaningful text
            import re

            tag_match = re.search(r"\b([a-z]+(?:\s+[a-z]+)?)\b", content.lower())
            if tag_match:
                potential_name = tag_match.group(1)
                if potential_name not in ["name", "description", "the", "and", "or", "for", "with", "this", "that"]:
                    name = potential_name
                    if debug:
                        print(f"DEBUG: Extracted fallback name: {name}")

        if debug:
            print(f"DEBUG: Final parsed - Name: '{name}', Description: '{description}'")
        return name, description

    except Exception as e:
        print(f"Error analyzing intent with AI: {e}")
        # Fall back to the original logic
        return generate_cluster_summary(cluster_logs, {})


def generate_cluster_summary(cluster_logs: List[Dict[str, Any]], cluster_analysis: Dict[str, Any]) -> Tuple[str, str]:
    """Generate a short name and description for a cluster based on its characteristics."""
    # Look at common patterns to generate name and description
    name = "general"
    description = "A collection of logs with mixed characteristics."

    # If cluster has common errors, use that as primary characteristic
    if cluster_analysis.get("common_errors"):
        error_types = list(cluster_analysis["common_errors"].keys())
        if "timeout" in str(error_types).lower():
            name = "timeouts"
            description = "Logs showing timeout-related failures and performance issues."
        elif "auth" in str(error_types).lower() or "permission" in str(error_types).lower():
            name = "auth"
            description = "Authentication and permission-related errors."
        elif "connection" in str(error_types).lower() or "network" in str(error_types).lower():
            name = "network"
            description = "Connection failures and network-related problems."
        elif "validation" in str(error_types).lower() or "invalid" in str(error_types).lower():
            name = "validation"
            description = "Input validation failures and invalid data errors."
        else:
            # Use first error type
            first_error = str(error_types[0])[:50]
            name = "errors"
            description = f"Logs with errors primarily: {first_error}..."

    # Look at metrics if available
    all_metrics = {}
    for log in cluster_logs[:10]:  # Sample first 10 to avoid processing all
        if log.get("metrics"):
            for k, v in log["metrics"].items():
                if k not in all_metrics:
                    all_metrics[k] = []
                all_metrics[k].append(v)

    if all_metrics:
        if "latency" in all_metrics or "duration" in all_metrics:
            latencies = all_metrics.get("latency", all_metrics.get("duration", []))
            avg_latency = sum(latencies) / len(latencies) if latencies else 0
            if avg_latency > 5000:
                name = "slow"
                description = f"High latency operations averaging {avg_latency:.0f}ms."
            elif avg_latency < 100:
                name = "fast"
                description = f"Quick operations averaging {avg_latency:.0f}ms."

    return name, description


@traced(notrace_io=True)
def analyze_clusters(
    logs: List[Dict[str, Any]],
    labels: np.ndarray,
    embeddings: np.ndarray,
    openai_api_key: Optional[str] = None,
    use_ai_intent: bool = True,
    categorization_guide: Optional[str] = None,
    debug_intent: bool = False,
) -> Dict[int, Dict[str, Any]]:
    """Analyze clusters to find trends and patterns."""
    cluster_analysis = {}

    unique_labels = set(labels)
    for label in unique_labels:
        if label == -1:  # Skip noise points in DBSCAN
            continue

        cluster_indices = np.where(labels == label)[0]
        cluster_logs = [logs[i] for i in cluster_indices]

        # Analyze cluster characteristics
        analysis = {
            "size": len(cluster_logs),
            "percentage": len(cluster_logs) / len(logs) * 100,
            "common_errors": {},
            "sample_logs": [],
        }

        # Count common errors
        error_counts = {}
        for log in cluster_logs:
            if log.get("error"):
                error = str(log["error"])
                error_counts[error] = error_counts.get(error, 0) + 1

        if error_counts:
            analysis["common_errors"] = dict(sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5])

        # Get sample logs (closest to cluster center)
        cluster_embeddings = embeddings[cluster_indices]
        center = np.mean(cluster_embeddings, axis=0)
        distances = np.linalg.norm(cluster_embeddings - center, axis=1)
        closest_indices = np.argsort(distances)[:3]

        analysis["sample_logs"] = [
            {
                "index": int(cluster_indices[i]),
                "id": cluster_logs[i].get("id", f"no-id-{cluster_indices[i]}"),
                "log": cluster_logs[i],
            }
            for i in closest_indices
        ]

        # Generate cluster name and description
        if use_ai_intent and openai_api_key:
            print(f"  Analyzing intent for cluster {label}...")
            name, description = analyze_user_intent_with_ai(
                cluster_logs, openai_api_key, categorization_guide=categorization_guide, debug=debug_intent
            )
        else:
            name, description = generate_cluster_summary(cluster_logs, analysis)

        analysis["name"] = name
        analysis["description"] = description

        cluster_analysis[int(label)] = analysis

    return cluster_analysis


@traced(notrace_io=True)
def save_results(
    logs: List[Dict[str, Any]],
    embeddings: np.ndarray,
    labels: np.ndarray,
    cluster_analysis: Dict[int, Dict[str, Any]],
    output_file: str,
):
    """Save embeddings, logs, and analysis to JSON file."""
    results = {
        "logs": logs,
        "embeddings": embeddings.tolist(),
        "labels": labels.tolist(),
        "cluster_analysis": cluster_analysis,
        "metadata": {
            "n_logs": len(logs),
            "n_clusters": len(set(labels)) - (1 if -1 in labels.tolist() else 0),
            "embedding_dim": embeddings.shape[1],
        },
    }

    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)

    print(f"\nResults saved to {output_file}")


@traced
def print_cluster_summary(cluster_analysis: Dict[int, Dict[str, Any]]):
    """Print a summary of the cluster analysis."""
    print("\n" + "=" * 80)
    print("CLUSTER ANALYSIS SUMMARY")
    print("=" * 80)

    for cluster_id, analysis in sorted(cluster_analysis.items()):
        print(f"\nCluster {cluster_id}: {analysis['name']}")
        print(f"  Description: {analysis['description']}")
        print(f"  Size: {analysis['size']} logs ({analysis['percentage']:.1f}%)")

        if analysis["common_errors"]:
            print("  Common errors:")
            for error, count in list(analysis["common_errors"].items())[:3]:
                print(f"    - {error[:100]}... ({count} occurrences)")

        print("  Sample log IDs:", [s["id"] for s in analysis["sample_logs"]])


def main():
    args = parse_args()

    # Set OpenAI API key
    openai_api_key = args.openai_api_key or os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("OpenAI API key required (--openai-api-key or OPENAI_API_KEY env var)")

    openai.api_key = openai_api_key

    # Compute cache key for this configuration
    cache_key = compute_cache_key(
        args.api_key,
        args.project_name,
        args.limit,
        args.filter,
        args.categorization_guide,
        args.use_preprocessing,
        args.preprocessing_model,
    )
    use_cache = not args.no_cache

    if use_cache:
        print(f"Cache key: {cache_key}")
        print(f"Cache directory: {args.cache_dir}")

    # Login to Braintrust
    print(f"Connecting to Braintrust at {args.app_url}...")
    login(api_key=args.api_key, app_url=args.app_url)

    # Get project ID
    project_id = get_project_id(args.project_name)
    print(f"Found project: {project_id}")

    # Fetch logs with caching
    print(f"\nFetching up to {args.limit} logs...")
    logs = fetch_logs_via_btql(
        project_id,
        args.limit,
        args.filter,
        cache_key=cache_key,
        cache_dir=args.cache_dir,
        use_cache=use_cache,
        deduplicate=not args.no_deduplicate,
        use_pagination=args.limit > 1000,  # Enable pagination for large requests
        page_size=1000,
    )
    print(f"Retrieved {len(logs)} logs")

    if not logs:
        print("No logs found matching the criteria")
        return

    # Initialize Braintrust logger
    login(force_login=True)
    logger = init_logger("taxonomy discovery")

    with start_span("clustering_analysis"):
        # Debug extraction if requested
        if args.debug_extraction and args.intent_focused_embeddings:
            print("\n" + "=" * 80)
            print("DEBUG: Content Extraction Examples")
            print("=" * 80)
            for i, log in enumerate(logs[:3]):  # Show first 3 logs
                print(f"\nLog {i + 1}:")
                print("-" * 40)

                # Show raw input
                if log.get("input"):
                    raw_input = str(log["input"])[:200]
                    print(f"Raw input: {raw_input}...")

                    # Show extracted content
                    extracted = extract_user_content(log["input"])
                    if extracted:
                        print(f"Extracted user content: {extracted[:200]}...")
                    else:
                        print("Extracted user content: [FILTERED OUT - likely system prompt or tool definition]")

                # Show messages extraction
                if log.get("messages"):
                    print(f"\nRaw messages: {len(log['messages'])} messages")
                    extracted = extract_user_content(log["messages"])
                    if extracted:
                        print(f"Extracted user messages: {extracted[:200]}...")
                    else:
                        print("Extracted user messages: [NO USER CONTENT FOUND]")

                print("-" * 40)
            print("\n")

        # Generate embeddings with caching
        embeddings, filtered_logs = generate_embeddings(
            logs,
            openai_api_key,
            args.embedding_model,
            cache_key=cache_key,
            cache_dir=args.cache_dir,
            use_cache=use_cache,
            focus_on_intent=args.intent_focused_embeddings,
            use_preprocessing=args.use_preprocessing,
            preprocessing_model=args.preprocessing_model,
            categorization_guide=args.categorization_guide,
            debug=args.debug_extraction,
            max_concurrent=args.max_concurrent,
        )

        # Update logs to filtered logs if preprocessing was used
        if args.use_preprocessing:
            logs = filtered_logs

        # Perform clustering (not cached since it's fast and we want to experiment)
        labels = perform_clustering(embeddings, args.clustering_method, args.n_clusters)

        # Analyze clusters
        use_ai_intent = not args.no_ai_intent
        if args.categorization_guide:
            print(f"\nCategorization focus: {args.categorization_guide}")
        cluster_analysis = analyze_clusters(
            logs,
            labels,
            embeddings,
            openai_api_key=openai_api_key if use_ai_intent else None,
            use_ai_intent=use_ai_intent,
            categorization_guide=args.categorization_guide,
            debug_intent=args.debug_intent,
        )

        # Print summary
        print_cluster_summary(cluster_analysis)

        # Save results
        save_results(logs, embeddings, labels, cluster_analysis, args.output)


if __name__ == "__main__":
    main()
