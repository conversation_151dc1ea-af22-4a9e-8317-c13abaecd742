#!/usr/bin/env python3
"""
Query logs using natural language queries and LLM-based filtering.

Example usage:
python query_logs.py --api-key <API_KEY> --project-name "My Project" --query "find me logs related to people importing the clerk sdk" --limit 1000 --max-matches 10
"""

import argparse
import json
import os
import time
import traceback
from concurrent.futures import as_completed
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import quote

import openai
from braintrust import (
    TracedThreadPoolExecutor,
    current_span,
    init_logger,
    logger,
    login,
    login_to_state,
    start_span,
    traced,
    wrap_openai,
)
from braintrust.logger import _internal_get_global_state

# Import the new efficient deduplication algorithm
from common_substrings import find_and_remove_common_substrings

# Import shared functions from embed_logs
from embed_logs import (
    extract_user_content,
    fetch_logs_via_btql,
    get_project_id,
)

# Import shared interactive components
from interactive_common import (
    format_match_for_display,
    interactive_review,
    prepare_feedback_examples,
    print_match_summary,
    save_results_with_feedback,
)


def get_project_info(state, project_name: str) -> Tuple[str, str]:
    """Get project ID and org name from project name."""
    response = state.api_conn().get_json(f"/v1/project?name={project_name}")
    if not response.get("objects"):
        raise ValueError(f"Project '{project_name}' not found")
    project = response["objects"][0]
    return project["id"], state.org_name


client = wrap_openai(openai.OpenAI())


def parse_args():
    parser = argparse.ArgumentParser(description="Query logs using natural language with LLM filtering")
    parser.add_argument("--api-key", required=True, help="Braintrust API key")
    parser.add_argument(
        "--app-url",
        default="https://www.braintrust.dev",
        help="Braintrust app URL (default: https://www.braintrust.dev)",
    )
    parser.add_argument("--project-name", required=True, help="Name of the project to search logs from")
    parser.add_argument("--query", required=True, help="Natural language query to search for in logs")
    parser.add_argument("--limit", type=int, default=1000, help="Maximum number of logs to scan (default: 1000)")
    parser.add_argument(
        "--max-matches", type=int, default=10, help="Maximum number of matching logs to return (default: 10)"
    )
    parser.add_argument(
        "--page-size", type=int, default=100, help="Number of logs to process in each batch (default: 100)"
    )
    parser.add_argument("--filter", help="Optional BTQL filter expression to pre-filter logs")
    parser.add_argument(
        "--openai-api-key", help="OpenAI API key for LLM filtering (defaults to OPENAI_API_KEY env var)"
    )
    parser.add_argument(
        "--model", default="gpt-4o-mini", help="OpenAI model to use for analysis (default: gpt-4o-mini)"
    )
    parser.add_argument(
        "--output", default="query_results.json", help="Output file for results (default: query_results.json)"
    )
    parser.add_argument("--verbose", action="store_true", help="Show detailed progress and reasoning")
    parser.add_argument(
        "--no-deduplicate",
        action="store_true",
        help="Disable deduplication by root_span_id (keep all spans from same trace)",
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=10,
        help="Maximum concurrent LLM calls (default: 10)",
    )
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enable interactive mode to review and rate matches",
    )
    return parser.parse_args()


def extract_log_content(log: Dict[str, Any]) -> str:
    """Extract relevant content from a log for LLM analysis."""
    content_parts = []

    # Extract user input content using the existing function
    if log.get("input"):
        user_input = extract_user_content(log["input"])
        if user_input:
            content_parts.append(f"User Input: {user_input[:1000]}")
        else:
            # Fallback to raw content if extraction fails
            if isinstance(log["input"], str):
                content_parts.append(f"Raw Input: {log['input'][:1000]}")
            else:
                content_parts.append(f"Raw Input: {json.dumps(log['input'])[:1000]}")

    # Extract output
    if log.get("output"):
        output_content = extract_user_content(log["output"])
        if output_content:
            content_parts.append(f"Output: {output_content[:1000]}")
        else:
            if isinstance(log["output"], str):
                content_parts.append(f"Raw Output: {log['output'][:1000]}")
            else:
                content_parts.append(f"Raw Output: {json.dumps(log['output'])[:1000]}")

    # Extract relevant metadata
    if log.get("metadata"):
        relevant_metadata = {}
        for key, value in log["metadata"].items():
            if key in ["endpoint", "function", "action", "method", "url", "error", "stack_trace", "email", "user_id"]:
                relevant_metadata[key] = value
        if relevant_metadata:
            content_parts.append(f"Metadata: {json.dumps(relevant_metadata)[:500]}")

    # Extract error information
    if log.get("error"):
        content_parts.append(f"Error: {str(log['error'])[:500]}")

    # Extract span attributes if they contain useful info
    if log.get("span_attributes"):
        span_attrs = log["span_attributes"]
        if isinstance(span_attrs, dict):
            useful_attrs = {
                k: v
                for k, v in span_attrs.items()
                if k in ["function.name", "code.function", "http.method", "http.url", "error.message"]
            }
            if useful_attrs:
                content_parts.append(f"Span Info: {json.dumps(useful_attrs)[:300]}")

    return "\n".join(content_parts) if content_parts else "No extractable content"


@traced
def analyze_log_relevance(
    log: Dict[str, Any],
    log_content: str,
    query: str,
    model: str = "gpt-4o-mini",
    verbose: bool = False,
    org_name: str = "unknown",
    project_name: str = "unknown",
    app_url: str = "https://www.braintrust.dev",
    feedback_examples: Optional[List[Dict[str, Any]]] = None,
) -> Optional[Dict[str, Any]]:
    """Use LLM to determine if a log is relevant to the query and provide analysis."""
    log_id = log.get("id", "unknown")
    permalink = current_span().permalink()

    if verbose:
        print(f"    Analyzing log {log_id}...")
        if feedback_examples:
            print(f"    Using {len(feedback_examples)} feedback examples")
            # Debug: Show a sample of the feedback being sent to LLM
            if len(feedback_examples) > 0:
                sample = feedback_examples[0]
                status = "CORRECT" if sample["correct"] else "INCORRECT"
                print(f"    Sample feedback: {status} - {sample.get('reason', 'no reason')}")

    # Build examples section if feedback is available
    examples_section = ""
    if feedback_examples and len(feedback_examples) > 0:
        examples_section = "\n\nEXAMPLES OF CORRECT/INCORRECT MATCHES:\n"
        for i, example in enumerate(feedback_examples[:10]):  # Limit to 10 examples
            relevance = "RELEVANT" if example["correct"] else "NOT RELEVANT"
            reason_text = example.get("reason", "").strip()

            examples_section += f"\nExample {i + 1} - {relevance}:\n"
            examples_section += f"Log ID: {example['log_id']}\n"
            examples_section += f"Content: {example.get('content_preview', 'N/A')[:200]}...\n"
            if reason_text:
                examples_section += f"Why this is {relevance}: {reason_text}\n"
            examples_section += "---\n"

        examples_section += "\nIMPORTANT: Use these examples to guide your decision making. Pay special attention to the reasons why logs were marked as NOT RELEVANT."

    # Build system message with query, examples, and instructions
    if feedback_examples and len(feedback_examples) > 0:
        system_message = f"""You are analyzing log entries to determine relevance to the following query:

QUERY: "{query}"{examples_section}

Your task:
Determine if this log is relevant to the query (YES or NO)

CRITICAL INSTRUCTIONS:
1. Carefully review the examples above - they show what has been previously marked as RELEVANT vs NOT RELEVANT
2. If a log is similar to any of the NOT RELEVANT examples, mark it as NOT RELEVANT
3. Pay special attention to the reasoning provided for why logs were marked incorrectly
4. Be strict about relevance - only mark as relevant if there's a clear connection to the query
5. Learn from the feedback patterns to avoid repeating mistakes

Return just YES or NO. Nothing else.
"""
    else:
        system_message = f"""You are analyzing log entries to determine relevance to the following query:

QUERY: "{query}"

Your task:
Determine if this log is relevant to the query (YES or NO)

Be strict about relevance - only mark as relevant if there's a clear connection to the query.

Return just YES or NO. Nothing else.
"""

    # User message contains only the log content to analyze
    user_prompt = f"""LOG CONTENT:
{log_content}"""

    try:
        messages = [{"role": "system", "content": system_message}, {"role": "user", "content": user_prompt}]

        response = client.responses.create(
            model=model,
            input=messages,
            max_output_tokens=25000,  # This should realistically be a lot lower, but its high to avoid truncation for now
            text={"verbosity": "low"},
            reasoning={"effort": "medium", "summary": "auto"},
        )

        reasoning_block = [o for o in response.output if o.type == "reasoning"]
        if reasoning_block and reasoning_block[-1].summary:
            reasoning_text = reasoning_block[-1].summary[0].text
        else:
            reasoning_text = "No reasoning provided"

        result = response.output[-1].content[-1].text
        relevant = result.strip().upper() == "YES"

        if relevant:
            with start_span("log_relevant", input={"log_id": log_id}):
                return format_match_for_display(
                    log=log,
                    content_preview=log_content,
                    org_name=org_name,
                    project_name=project_name,
                    app_url=app_url,
                    reasoning=reasoning_text,
                )

        if verbose:
            print(f"      Not relevant: {reasoning_text}")

        return None

    except Exception as e:
        traceback.print_exc()
        return None


def compute_deduplication_cache_key(logs: List[Dict[str, Any]], min_substring_length: int = 20) -> str:
    """Compute cache key for deduplication based on log content and parameters."""
    import hashlib

    # Create a hash of the log contents and deduplication parameters
    log_ids = [log.get("id", str(i)) for i, log in enumerate(logs)]
    log_contents = [extract_log_content(log) for log in logs]

    # Create a stable hash from log IDs, content lengths, and parameters
    cache_parts = [
        str(len(logs)),
        str(min_substring_length),
        str(sum(len(content) for content in log_contents)),  # Total content length
        hashlib.sha256("".join(log_ids).encode()).hexdigest()[:8],  # Short hash of log IDs
    ]

    cache_string = ":".join(cache_parts)
    return hashlib.sha256(cache_string.encode()).hexdigest()[:16]


def convert_and_deduplicate_log_content(
    logs: List[Dict[str, Any]], min_substring_length: int = 20, cache_dir: str = ".cache", use_cache: bool = True
) -> List[str]:
    """Extract log contents and remove common substrings using efficient SAM + Aho-Corasick algorithm."""
    contents = [extract_log_content(log) for log in logs]

    # Calculate 25% threshold (minimum documents a substring must appear in)
    # But ensure we need at least 2 documents for something to be considered "common"
    min_docs = max(2, len(contents) // 4)

    # If we don't have enough logs for meaningful deduplication, skip it
    if len(contents) < 4 or min_docs >= len(contents):
        print(f"Only {len(contents)} logs - skipping deduplication (need at least 4 logs for meaningful results)")
        return contents

    # Check cache first
    if use_cache:
        import json
        import time
        from pathlib import Path

        dedup_cache_key = compute_deduplication_cache_key(logs, min_substring_length)
        cache_path = Path(cache_dir)
        cache_path.mkdir(exist_ok=True)
        dedup_cache_path = cache_path / f"{dedup_cache_key}_dedup.json"

        if dedup_cache_path.exists():
            print("Loading deduplication results from cache...")
            with open(dedup_cache_path, "r") as f:
                cached_dedup = json.load(f)
            return cached_dedup["deduplicated_contents"]

    print(
        f"Analyzing {len(contents)} logs for common substrings (min docs: {min_docs}, min length: {min_substring_length})..."
    )

    try:
        # Use the efficient algorithm to find and remove common substrings
        common_substrings, deduplicated_contents = find_and_remove_common_substrings(
            contents, min_docs=min_docs, min_len=min_substring_length
        )

        print(f"Found {len(common_substrings)} maximal common substrings to remove")
        if common_substrings:
            print("Sample common substrings:")
            for i, (substring, support) in enumerate(common_substrings[:3]):
                print(f"  {i + 1}. ({len(substring)} chars, in {support} docs): {repr(substring[:50])}...")

    except Exception as e:
        print(f"Error in deduplication algorithm: {e}")
        print("Falling back to original contents")
        deduplicated_contents = contents

    # Calculate compression ratio
    original_total = sum(len(c) for c in contents)
    cleaned_total = sum(len(c) for c in deduplicated_contents)
    if original_total > 0:
        compression_ratio = (original_total - cleaned_total) / original_total
        print(f"Content size reduction: {compression_ratio:.1%} ({original_total} -> {cleaned_total} chars)")

    # Cache the results
    if use_cache:
        import json
        import time
        from pathlib import Path

        dedup_cache_key = compute_deduplication_cache_key(logs, min_substring_length)
        cache_path = Path(cache_dir)
        cache_path.mkdir(exist_ok=True)
        dedup_cache_path = cache_path / f"{dedup_cache_key}_dedup.json"

        print(f"Saving deduplication results to cache: {dedup_cache_path}")
        with open(dedup_cache_path, "w") as f:
            json.dump(
                {
                    "deduplicated_contents": deduplicated_contents,
                    "original_total": original_total,
                    "cleaned_total": cleaned_total,
                    "compression_ratio": compression_ratio,
                    "common_substrings_count": len(common_substrings),
                    "min_substring_length": min_substring_length,
                    "timestamp": time.time(),
                },
                f,
                indent=2,
            )

    return deduplicated_contents


@traced(notrace_io=True)
def query_logs_paginated(
    query_state,
    project_id: str,
    query: str,
    max_logs: int = 1000,
    max_matches: int = 10,
    page_size: int = 100,
    filter_expr: Optional[str] = None,
    model: str = "gpt-4o-mini",
    verbose: bool = False,
    deduplicate: bool = True,
    max_concurrent: int = 10,
    org_name: str = "unknown",
    project_name: str = "unknown",
    app_url: str = "https://www.braintrust.dev",
    feedback_examples: Optional[List[Dict[str, Any]]] = None,
) -> List[Dict[str, Any]]:
    """Query logs using natural language with pagination support and parallel LLM calls."""
    matches = []
    logs_processed = 0

    print(f'Searching for: "{query}"')
    print(f"Scanning up to {max_logs} logs, looking for {max_matches} matches...")
    if feedback_examples:
        print(f"Using {len(feedback_examples)} feedback examples to improve accuracy")

    # First, let's fetch a large batch of logs and process them in smaller chunks
    # This is simpler and avoids cursor pagination issues
    print(f"Fetching up to {max_logs} logs from project...")

    try:
        all_logs = fetch_logs_via_btql(
            project_id,
            max_logs,
            filter_expr,
            use_pagination=True,
            page_size=page_size,
            deduplicate=deduplicate,
            use_cache=False,  # Don't cache for query operations
            state=query_state,
        )
    except Exception as e:
        print(f"Error fetching logs: {e}")
        return []

    if not all_logs:
        print("No logs found in project")
        return []

    print(f"Retrieved {len(all_logs)} logs. Processing in batches of {page_size}...")

    with start_span("llm filter"):
        # Process logs in batches
        batch_number = 0
        for i in range(0, len(all_logs), page_size):
            if logs_processed >= max_logs or len(matches) >= max_matches:
                break

            batch_number += 1
            batch_logs = all_logs[i : i + page_size]

            with start_span("process_batch", input={"batch_number": batch_number}):
                print(
                    f"\nProcessing batch {batch_number} ({len(batch_logs)} logs) - processed: {logs_processed}/{len(all_logs)}, found: {len(matches)}/{max_matches}"
                )

                # Analyze logs in parallel using ThreadPoolExecutor
                batch_matches = []
                completed = 0

                deduplicated_contents = convert_and_deduplicate_log_content(batch_logs)

                with TracedThreadPoolExecutor(max_workers=max_concurrent) as executor:
                    # Submit all analysis tasks for this batch
                    future_to_log = {}
                    for i, log in enumerate(batch_logs):
                        future = executor.submit(
                            analyze_log_relevance,
                            log,
                            deduplicated_contents[i],
                            query,
                            model,
                            verbose,
                            org_name,
                            project_name,
                            app_url,
                            feedback_examples,
                        )
                        future_to_log[future] = log

                    # Process completed futures
                    for future in as_completed(future_to_log):
                        log = future_to_log[future]
                        try:
                            analysis = future.result()
                            if analysis:
                                with start_span("log_match_found", input={"log_id": analysis["log_id"]}):
                                    batch_matches.append(analysis)
                                    print(f"  ✓ Match found: {analysis['content_preview'][:100]}...")
                                    if verbose:
                                        print(f"    Reasoning: {analysis['reasoning']}")

                                    # Stop early if we have enough matches
                                    if len(matches) + len(batch_matches) >= max_matches:
                                        print(f"  Reached maximum matches ({max_matches}), stopping batch analysis")
                                        break
                        except Exception as e:
                            if verbose:
                                print(f"  Error analyzing log {log.get('id', 'unknown')}: {e}")

                        completed += 1
                        if verbose and (completed % 10 == 0 or completed >= len(batch_logs) - 10):
                            print(f"  Analyzed {completed}/{len(batch_logs)} logs in batch...")

                # Add batch matches to total matches
                matches.extend(batch_matches[: max_matches - len(matches)])  # Don't exceed max_matches

                logs_processed += len(batch_logs)

                print(f"  Batch {batch_number} complete: {len(batch_matches)} matches found")

                # If we have enough matches, stop
                if len(matches) >= max_matches:
                    print(f"Reached maximum matches ({max_matches}), stopping search")
                    break

        print(f"\nScan complete! Found {len(matches)} relevant logs out of {logs_processed} processed")
        return matches


@traced
def main():
    args = parse_args()

    # Set OpenAI API key
    openai_api_key = args.openai_api_key or os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("OpenAI API key required (--openai-api-key or OPENAI_API_KEY env var)")

    openai.api_key = openai_api_key

    # Login to Braintrust
    print(f"Connecting to Braintrust at {args.app_url}...")
    query_state = login_to_state(api_key=args.api_key, app_url=args.app_url)

    # Get project ID and org name
    try:
        project_id, org_name = get_project_info(query_state, args.project_name)
        print(f"Found project: {project_id} (org: {org_name})")
    except ValueError as e:
        print(f"Error: {e}")
        return

    # Initialize Braintrust logger
    login(force_login=True)
    logger = init_logger("taxonomy discovery")

    with start_span("log_query_analysis"):
        # Define rerun function for interactive mode
        def rerun_with_feedback(feedback_examples=None, **kwargs):
            """Re-run LLM-based filtering with feedback examples."""
            return query_logs_paginated(
                query_state=query_state,
                project_id=project_id,
                query=args.query,
                max_logs=args.limit,
                max_matches=args.max_matches,
                page_size=args.page_size,
                filter_expr=args.filter,
                model=args.model,
                verbose=args.verbose,
                deduplicate=not args.no_deduplicate,
                max_concurrent=args.max_concurrent,
                org_name=org_name,
                project_name=args.project_name,
                app_url=args.app_url,
                feedback_examples=feedback_examples,
            )

        # Perform query
        matches = query_logs_paginated(
            query_state=query_state,
            project_id=project_id,
            query=args.query,
            max_logs=args.limit,
            max_matches=args.max_matches,
            page_size=args.page_size,
            filter_expr=args.filter,
            model=args.model,
            verbose=args.verbose,
            deduplicate=not args.no_deduplicate,
            max_concurrent=args.max_concurrent,
            org_name=org_name,
            project_name=args.project_name,
            app_url=args.app_url,
        )

        # Print summary
        print_match_summary(matches, args.query, "LLM QUERY")

        # Interactive review if requested
        feedback_data = None
        if args.interactive and matches:
            feedback_data = interactive_review(
                matches=matches,
                query=args.query,
                rerun_func=rerun_with_feedback,
            )

        # Save results
        save_results_with_feedback(
            matches=matches,
            query=args.query,
            output_file=args.output,
            feedback_data=feedback_data,
        )


if __name__ == "__main__":
    main()
