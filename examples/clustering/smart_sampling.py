#!/usr/bin/env python3
"""
Smart sampling of logs using deduplication, embeddings, and clustering.

Fetches logs, deduplicates similar content, embeds documents in parallel,
clusters into configurable groups, and prints representative examples.

ONNX Runtime dependencies (for --use-onnx flag):
  uv add -U "onnx>=1.16.0" "onnxruntime>=1.18.0" "transformers>=4.44" "sentence-transformers>=3.0.0" "optimum[onnxruntime]>=1.21.0" "protobuf<5"

Example usage:
python smart_sampling.py --api-key <API_KEY> --project-name "My Project" --limit 1000 --clusters 10
"""

import argparse
import hashlib
import json
import os
import threading
import time
from concurrent.futures import as_completed
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import openai
from braintrust import (
    TracedThreadPoolExecutor,
    logger,
    login,
    login_to_state,
    start_span,
    traced,
    wrap_openai,
)

# Import shared functions
from embed_logs import (
    extract_user_content,
    fetch_logs_via_btql,
    get_project_id,
)
from query_logs import (
    convert_and_deduplicate_log_content,
    extract_log_content,
    get_project_info,
)
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score

client = wrap_openai(openai.OpenAI())

# Global variables for HF model (loaded once)
_hf_model = None
_hf_tokenizer = None
_hf_model_lock = threading.Lock()

# Global variables for ONNX model (loaded once)
_onnx_session = None
_onnx_tokenizer = None
_onnx_model_lock = threading.Lock()

# Global token usage tracking for OpenAI
_token_usage_lock = threading.Lock()
_total_tokens_used = 0


def parse_args():
    parser = argparse.ArgumentParser(description="Smart sampling of logs using clustering")
    parser.add_argument("--api-key", required=True, help="Braintrust API key")
    parser.add_argument(
        "--app-url",
        default="https://www.braintrust.dev",
        help="Braintrust app URL (default: https://www.braintrust.dev)",
    )
    parser.add_argument("--project-name", required=True, help="Name of the project to sample logs from")
    parser.add_argument("--limit", type=int, default=1000, help="Maximum number of logs to fetch (default: 1000)")
    parser.add_argument("--clusters", type=int, default=10, help="Number of clusters to create (default: 10)")
    parser.add_argument("--page-size", type=int, default=1000, help="Number of logs to fetch per page (default: 1000)")
    parser.add_argument("--filter", help="Optional BTQL filter expression to pre-filter logs")
    parser.add_argument("--openai-api-key", help="OpenAI API key for embeddings (defaults to OPENAI_API_KEY env var)")
    parser.add_argument(
        "--embedding-model",
        default="text-embedding-3-small",
        help="OpenAI embedding model (default: text-embedding-3-small)",
    )
    parser.add_argument(
        "--use-huggingface",
        action="store_true",
        help="Use Hugging Face transformers instead of OpenAI for embeddings",
    )
    parser.add_argument(
        "--hf-model",
        default="google/embeddinggemma-300m",
        help="Hugging Face model for embeddings (default: google/embeddinggemma-300m)",
    )
    parser.add_argument(
        "--quantize",
        action="store_true",
        help="Use 8-bit quantization for Hugging Face models (reduces memory usage)",
    )
    parser.add_argument(
        "--embedding-prefix",
        type=str,
        help="Phrase to prepend to each document before embedding (helps focus clustering on specific topics)",
    )
    parser.add_argument(
        "--use-onnx",
        action="store_true",
        help="Use ONNX Runtime for faster CPU inference (requires model export)",
    )
    parser.add_argument(
        "--onnx-model-path",
        type=str,
        help="Path to ONNX model directory (if not provided, will try to export automatically)",
    )
    parser.add_argument(
        "--output",
        default="smart_sampling_results.json",
        help="Output file for results (default: smart_sampling_results.json)",
    )
    parser.add_argument("--verbose", action="store_true", help="Show detailed progress")
    parser.add_argument(
        "--no-deduplicate",
        action="store_true",
        help="Disable deduplication by root_span_id (keep all spans from same trace)",
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=20,
        help="Maximum concurrent embedding calls (default: 20)",
    )
    parser.add_argument(
        "--examples-per-cluster",
        type=int,
        default=3,
        help="Number of examples to show per cluster (default: 3)",
    )
    parser.add_argument(
        "--min-substring-length",
        type=int,
        default=20,
        help="Minimum length for common substrings to remove (default: 20)",
    )
    parser.add_argument(
        "--hf-batch-size",
        type=int,
        default=128,
        help="Batch size for Hugging Face embedding generation (default: 128)",
    )
    parser.add_argument(
        "--cache-dir",
        default=".cache",
        help="Directory for caching intermediate results (default: .cache)",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable caching and force re-download/re-compute",
    )
    return parser.parse_args()


def compute_query_cache_key(
    api_key: str,
    project_name: str,
    limit: int,
    filter_expr: Optional[str],
    deduplicate: bool,
    page_size: int,
) -> str:
    """Compute a cache key for BTQL query results (independent of embedding model)."""
    # Use only the last 8 chars of API key for privacy
    key_suffix = api_key[-8:] if len(api_key) > 8 else api_key

    # Build cache string with only query-related parameters
    cache_parts = [
        key_suffix,
        project_name,
        str(limit),
        filter_expr or "no-filter",
        str(deduplicate),
        str(page_size),
    ]

    cache_string = ":".join(cache_parts)
    return hashlib.sha256(cache_string.encode()).hexdigest()[:16]


def compute_cache_key(
    api_key: str,
    project_name: str,
    limit: int,
    filter_expr: Optional[str],
    embedding_model: str,
    use_huggingface: bool,
    clusters: int,
    min_substring_length: int,
    **kwargs,
) -> str:
    """Compute a cache key based on the parameters (kept for compatibility)."""
    # Use only the last 8 chars of API key for privacy
    key_suffix = api_key[-8:] if len(api_key) > 8 else api_key

    # Build cache string with all parameters
    cache_parts = [
        key_suffix,
        project_name,
        str(limit),
        filter_expr or "no-filter",
        embedding_model,
        "hf" if use_huggingface else "openai",
        str(clusters),
        str(min_substring_length),
    ]

    # Add any additional kwargs that affect caching
    for key, value in sorted(kwargs.items()):
        if value is not None:
            cache_parts.append(f"{key}:{value}")

    cache_string = ":".join(cache_parts)
    return hashlib.sha256(cache_string.encode()).hexdigest()[:16]


def get_cache_path(cache_dir: str, cache_key: str, suffix: str) -> Path:
    """Get the cache file path for a given key and suffix."""
    cache_path = Path(cache_dir)
    cache_path.mkdir(exist_ok=True)
    return cache_path / f"{cache_key}_{suffix}.json"


def load_from_cache(cache_path: Path) -> Optional[Any]:
    """Load data from cache if it exists."""
    if cache_path.exists():
        print(f"Loading from cache: {cache_path}")
        with open(cache_path, "r") as f:
            return json.load(f)
    return None


def save_to_cache(cache_path: Path, data: Any):
    """Save data to cache."""
    print(f"Saving to cache: {cache_path}")
    with open(cache_path, "w") as f:
        json.dump(data, f, indent=2, default=str)


def load_hf_model(model_name: str, use_quantization: bool = False):
    """Load Hugging Face model with optional quantization."""
    global _hf_model, _hf_tokenizer

    # Use double-checked locking pattern for thread safety
    if _hf_model is None:
        with _hf_model_lock:
            # Check again inside the lock in case another thread loaded it
            if _hf_model is None:
                try:
                    from sentence_transformers import SentenceTransformer

                    if use_quantization:
                        print(f"Loading Hugging Face model with quantization: {model_name}")

                        try:
                            import platform

                            import torch

                            # Different quantization approaches based on platform
                            if platform.system() == "Darwin":  # macOS
                                # Use PyTorch's dynamic quantization (CPU-only, macOS compatible)
                                print("  Using PyTorch dynamic quantization (macOS compatible)")

                                # Load model and force CPU execution for quantization
                                _hf_model = SentenceTransformer(model_name, device="cpu")

                                # Apply dynamic quantization to the transformer modules
                                try:
                                    # Set quantization backend for CPU
                                    torch.backends.quantized.engine = "fbgemm"  # Use FBGEMM backend for CPU

                                    # Simple approach: quantize the entire model
                                    quantized_model = torch.quantization.quantize_dynamic(
                                        _hf_model,
                                        {torch.nn.Linear},  # Only Linear layers to avoid embedding issues
                                        dtype=torch.qint8,
                                    )
                                    _hf_model = quantized_model
                                    print(f"Successfully loaded {model_name} with dynamic quantization (CPU)")

                                except Exception as quant_e:
                                    print(f"Dynamic quantization failed ({quant_e}), trying alternative approach")
                                    try:
                                        # Alternative: manually replace linear layers in each module
                                        _hf_model = SentenceTransformer(model_name, device="cpu")

                                        def quantize_module_recursive(module):
                                            for name, child in module.named_children():
                                                if isinstance(child, torch.nn.Linear):
                                                    # Replace with quantized version
                                                    quantized_layer = torch.nn.quantized.dynamic.Linear.from_float(
                                                        child
                                                    )
                                                    setattr(module, name, quantized_layer)
                                                else:
                                                    quantize_module_recursive(child)

                                        quantize_module_recursive(_hf_model)
                                        print(f"Successfully loaded {model_name} with manual quantization (CPU)")

                                    except Exception as manual_e:
                                        print(f"All quantization methods failed, using standard CPU model")
                                        # Reload without quantization but keep on CPU for consistency
                                        _hf_model = SentenceTransformer(model_name, device="cpu")
                                        print(f"Successfully loaded {model_name} on CPU (non-quantized)")

                            else:  # Linux/Windows - try bitsandbytes 8-bit
                                print("  Attempting 8-bit quantization with bitsandbytes")
                                model_kwargs = {
                                    "model_kwargs": {
                                        "load_in_8bit": True,
                                        "device_map": "auto",
                                        "torch_dtype": torch.float16,
                                    }
                                }
                                _hf_model = SentenceTransformer(model_name, **model_kwargs)
                                print(f"Successfully loaded {model_name} with 8-bit quantization")

                        except Exception as e:
                            print(f"Quantization failed ({e}), loading standard model")
                            _hf_model = SentenceTransformer(model_name)
                            print(f"Successfully loaded {model_name} (non-quantized)")
                    else:
                        print(f"Loading Hugging Face model: {model_name}")
                        _hf_model = SentenceTransformer(model_name)
                        print(f"Successfully loaded {model_name}")

                except ImportError:
                    raise ImportError(
                        "sentence-transformers package required for Hugging Face models. Install with: pip install sentence-transformers"
                    )
                except Exception as e:
                    raise RuntimeError(f"Failed to load Hugging Face model {model_name}: {e}")

                _hf_model.max_seq_length = 128
    return _hf_model


def load_or_export_onnx_model(model_name: str, onnx_path: Optional[str] = None):
    """Load ONNX model or export from HuggingFace model."""
    global _onnx_session, _onnx_tokenizer

    # Use double-checked locking pattern for thread safety
    if _onnx_session is None:
        with _onnx_model_lock:
            if _onnx_session is None:
                try:
                    import os

                    import onnxruntime as ort
                    from transformers import AutoTokenizer

                    # Determine ONNX model path
                    if onnx_path:
                        onnx_model_dir = onnx_path
                    else:
                        # Default path based on model name
                        safe_model_name = model_name.replace("/", "_")
                        onnx_model_dir = f".cache/onnx_{safe_model_name}"

                    onnx_model_file = os.path.join(onnx_model_dir, "model.onnx")
                    onnx_quantized_file = os.path.join(onnx_model_dir, "model-int8.onnx")

                    # Check if quantized model exists, else regular model, else export
                    if os.path.exists(onnx_quantized_file):
                        print(f"Loading quantized ONNX model from {onnx_quantized_file}")
                        model_file = onnx_quantized_file
                    elif os.path.exists(onnx_model_file):
                        print(f"Loading ONNX model from {onnx_model_file}")
                        model_file = onnx_model_file
                    else:
                        # Export model to ONNX
                        print(f"Exporting {model_name} to ONNX format...")
                        try:
                            # Test imports step by step with detailed error reporting
                            print("Testing ONNX dependencies...")

                            try:
                                import optimum

                                print(f"✓ optimum imported successfully (version: {optimum.__version__})")
                            except ImportError as e:
                                raise ImportError(f"Failed to import optimum: {e}")

                            try:
                                import optimum.exporters

                                print("✓ optimum.exporters imported successfully")
                            except ImportError as e:
                                raise ImportError(f"Failed to import optimum.exporters: {e}")

                            try:
                                from optimum.exporters import onnx

                                print("✓ optimum.exporters.onnx imported successfully")
                            except ImportError as e:
                                # Try alternative import approach
                                try:
                                    from optimum.exporters.onnx import main_export

                                    onnx = type("obj", (object,), {"main_export": main_export})
                                    print(
                                        "✓ optimum.exporters.onnx.main_export imported successfully (alternative method)"
                                    )
                                except ImportError as e2:
                                    raise ImportError(
                                        f"Failed to import optimum.exporters.onnx (tried both methods): {e} | {e2}"
                                    )

                            try:
                                from pathlib import Path

                                print("✓ pathlib imported successfully")
                            except ImportError as e:
                                raise ImportError(f"Failed to import pathlib: {e}")

                            # Try quantization imports (optional)
                            try:
                                from onnxruntime.quantization import QuantType, quantize_dynamic

                                print("✓ onnxruntime.quantization imported successfully")
                                quantization_available = True
                            except ImportError as e:
                                print(f"Warning: quantization not available: {e}")
                                quantization_available = False

                            os.makedirs(onnx_model_dir, exist_ok=True)

                            print(f"Running ONNX export for {model_name}...")
                            onnx.main_export(
                                model_name_or_path=model_name,
                                output=Path(onnx_model_dir),
                                task="feature-extraction",
                                framework="pt",
                                opset=14,
                            )
                            model_file = onnx_model_file
                            print(f"✓ Successfully exported to {onnx_model_dir}")

                            # Try to quantize the exported model if quantization is available
                            if quantization_available:
                                try:
                                    print(f"Quantizing model to INT8...")
                                    quantize_dynamic(
                                        model_input=onnx_model_file,
                                        model_output=onnx_quantized_file,
                                        op_types_to_quantize=["MatMul", "Gemm", "Attention", "Add", "Mul"],
                                        weight_type=QuantType.QInt8,
                                    )
                                    model_file = onnx_quantized_file
                                    print(f"✓ Successfully quantized to {onnx_quantized_file}")
                                except Exception as e:
                                    print(f"Quantization failed ({e}), using non-quantized model")
                                    model_file = onnx_model_file
                            else:
                                print("Skipping quantization (dependencies not available)")
                                model_file = onnx_model_file

                        except ImportError as ie:
                            print(f"\nDETAILED IMPORT ERROR DIAGNOSIS:")
                            print(f"Error: {ie}")
                            print(f"Python path: {os.sys.path}")

                            # Try to get more info about optimum installation
                            try:
                                import inspect

                                import optimum

                                print(f"optimum location: {inspect.getfile(optimum)}")
                                print(f"optimum.__file__: {optimum.__file__}")
                                print(f"optimum dir contents: {dir(optimum)}")

                                if hasattr(optimum, "exporters"):
                                    print(f"optimum.exporters dir contents: {dir(optimum.exporters)}")
                                else:
                                    print("optimum.exporters attribute not found")
                            except Exception as e:
                                print(f"Could not inspect optimum: {e}")

                            raise ImportError(
                                f"ONNX dependencies not available: {ie}. Please install with: pip install optimum[exporters] onnxruntime"
                            )
                        except Exception as e:
                            raise RuntimeError(f"Failed to export {model_name} to ONNX: {e}")

                    # Load ONNX session
                    so = ort.SessionOptions()
                    so.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
                    so.intra_op_num_threads = 32  # Adjust for your CPU
                    so.inter_op_num_threads = 1

                    # Use ARM-optimized providers if available
                    providers = []
                    available_providers = ort.get_available_providers()
                    if "ACLExecutionProvider" in available_providers:
                        providers.append("ACLExecutionProvider")
                    providers.append("CPUExecutionProvider")

                    _onnx_session = ort.InferenceSession(model_file, sess_options=so, providers=providers)
                    _onnx_tokenizer = AutoTokenizer.from_pretrained(model_name, use_fast=True)

                    print(f"Successfully loaded ONNX model with providers: {_onnx_session.get_providers()}")

                except ImportError as e:
                    raise ImportError(
                        f"ONNX dependencies not available: {e}. Install with: pip install optimum onnxruntime"
                    )
                except Exception as e:
                    raise RuntimeError(f"Failed to load ONNX model {model_name}: {e}")

    return _onnx_session, _onnx_tokenizer


@traced
def embed_text_openai(text: str, model: str = "text-embedding-3-small", prefix: str = None) -> List[float]:
    """Generate embedding for text using OpenAI API."""
    try:
        # Add prefix if provided
        if prefix:
            text = f"{prefix} {text}"
        response = client.embeddings.create(
            model=model,
            input=text[:8192],
            encoding_format="float",  # Limit to avoid token limits
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Error embedding text with OpenAI: {e}")
        return None


@traced
def embed_text_hf(text: str, model_name: str, use_quantization: bool = False, prefix: str = None) -> List[float]:
    """Generate embedding for text using Hugging Face model."""
    try:
        hf_model = load_hf_model(model_name, use_quantization)
        # Add prefix if provided
        if prefix:
            text = f"{prefix} {text}"
        # Truncate text to reasonable length for embedding models
        truncated_text = text[:8192]
        embedding = hf_model.encode(truncated_text, convert_to_numpy=True)
        return embedding.tolist()
    except Exception as e:
        print(f"Error embedding text with Hugging Face: {e}")
        return None


@traced
def embed_text_onnx(text: str, model_name: str, prefix: str = None, onnx_path: str = None) -> List[float]:
    """Generate embedding for text using ONNX Runtime."""
    try:
        import numpy as np

        session, tokenizer = load_or_export_onnx_model(model_name, onnx_path)

        # Add prefix if provided
        if prefix:
            text = f"{prefix} {text}"

        # Tokenize
        encoded = tokenizer(
            text,
            padding=True,
            truncation=True,
            max_length=128,  # Aggressive sequence length limit
            return_tensors="np",
        )

        # Get input names from ONNX model
        input_names = [inp.name for inp in session.get_inputs()]

        # Prepare inputs
        onnx_inputs = {}
        if "input_ids" in input_names:
            onnx_inputs["input_ids"] = encoded["input_ids"].astype(np.int64)
        if "attention_mask" in input_names:
            onnx_inputs["attention_mask"] = encoded["attention_mask"].astype(np.int64)
        if "token_type_ids" in input_names and "token_type_ids" in encoded:
            onnx_inputs["token_type_ids"] = encoded["token_type_ids"].astype(np.int64)

        # Run inference
        outputs = session.run(None, onnx_inputs)
        last_hidden_state = outputs[0]  # [batch_size, seq_len, hidden_size]

        # Mean pooling with attention mask
        attention_mask = encoded["attention_mask"]
        mask_expanded = np.expand_dims(attention_mask, -1)
        sum_embeddings = np.sum(last_hidden_state * mask_expanded, axis=1)
        sum_mask = np.clip(np.sum(attention_mask, axis=1, keepdims=True), a_min=1e-9, a_max=None)
        mean_embedding = sum_embeddings / sum_mask

        # L2 normalize
        norm = np.linalg.norm(mean_embedding, axis=1, keepdims=True)
        normalized_embedding = mean_embedding / np.clip(norm, a_min=1e-12, a_max=None)

        return normalized_embedding[0].astype(np.float32).tolist()
    except Exception as e:
        print(f"Error embedding text with ONNX: {e}")
        return None


@traced
def embed_batch_onnx(
    texts: List[str], model_name: str, batch_size: int = 256, prefix: str = None, onnx_path: str = None
) -> List[Optional[List[float]]]:
    """Generate embeddings for multiple texts using ONNX Runtime in batches."""
    try:
        import numpy as np

        session, tokenizer = load_or_export_onnx_model(model_name, onnx_path)

        # Add prefix and prepare texts
        if prefix:
            texts = [f"{prefix} {text}" for text in texts]

        embeddings = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i : i + batch_size]

            # Batch tokenize
            encoded = tokenizer(
                batch_texts,
                padding=True,
                truncation=True,
                max_length=128,  # Aggressive sequence length limit
                return_tensors="np",
            )

            # Get input names from ONNX model
            input_names = [inp.name for inp in session.get_inputs()]

            # Prepare inputs
            onnx_inputs = {}
            if "input_ids" in input_names:
                onnx_inputs["input_ids"] = encoded["input_ids"].astype(np.int64)
            if "attention_mask" in input_names:
                onnx_inputs["attention_mask"] = encoded["attention_mask"].astype(np.int64)
            if "token_type_ids" in input_names and "token_type_ids" in encoded:
                onnx_inputs["token_type_ids"] = encoded["token_type_ids"].astype(np.int64)

            # Run inference
            outputs = session.run(None, onnx_inputs)
            last_hidden_state = outputs[0]  # [batch_size, seq_len, hidden_size]

            # Mean pooling with attention mask
            attention_mask = encoded["attention_mask"]
            mask_expanded = np.expand_dims(attention_mask, -1)
            sum_embeddings = np.sum(last_hidden_state * mask_expanded, axis=1)
            sum_mask = np.clip(np.sum(attention_mask, axis=1, keepdims=True), a_min=1e-9, a_max=None)
            mean_embeddings = sum_embeddings / sum_mask

            # L2 normalize
            norms = np.linalg.norm(mean_embeddings, axis=1, keepdims=True)
            normalized_embeddings = mean_embeddings / np.clip(norms, a_min=1e-12, a_max=None)

            # Convert to list and add to results
            batch_embeddings = [emb.astype(np.float32).tolist() for emb in normalized_embeddings]
            embeddings.extend(batch_embeddings)

        return embeddings
    except Exception as e:
        print(f"Error embedding batch with ONNX: {e}")
        return [None] * len(texts)


@traced
def embed_batch_hf(
    texts: List[str], model_name: str, batch_size: int = 32, use_quantization: bool = False, prefix: str = None
) -> List[Optional[List[float]]]:
    """Generate embeddings for multiple texts using Hugging Face model in batches."""
    try:
        hf_model = load_hf_model(model_name, use_quantization)
        # Add prefix and truncate texts to reasonable length for embedding models
        if prefix:
            texts = [f"{prefix} {text}" for text in texts]
        truncated_texts = [text[:8192] for text in texts]

        embeddings = []
        for i in range(0, len(truncated_texts), batch_size):
            batch = truncated_texts[i : i + batch_size]
            batch_embeddings = hf_model.encode(batch, convert_to_numpy=True, show_progress_bar=False)
            embeddings.extend([emb.tolist() for emb in batch_embeddings])

        return embeddings
    except Exception as e:
        print(f"Error embedding batch with Hugging Face: {e}")
        return [None] * len(texts)


def add_token_usage(tokens: int):
    """Thread-safe function to add tokens to global counter."""
    global _total_tokens_used
    with _token_usage_lock:
        _total_tokens_used += tokens


def get_token_usage() -> int:
    """Get current total token usage."""
    global _total_tokens_used
    with _token_usage_lock:
        return _total_tokens_used


def calculate_embedding_cost(total_tokens: int, model: str) -> float:
    """Calculate cost based on OpenAI embedding pricing."""
    # OpenAI embedding pricing (as of 2024)
    pricing = {
        "text-embedding-3-small": 0.00002 / 1000,  # $0.00002 per 1K tokens
        "text-embedding-3-large": 0.00013 / 1000,  # $0.00013 per 1K tokens
        "text-embedding-ada-002": 0.0001 / 1000,  # $0.0001 per 1K tokens
    }

    rate = pricing.get(model, pricing["text-embedding-3-small"])  # Default to small model rate
    return total_tokens * rate


@traced
def embed_batch_openai(
    texts: List[str], model: str = "text-embedding-3-small", batch_size: int = 100, prefix: str = None
) -> List[Optional[List[float]]]:
    """Generate embeddings for multiple texts using OpenAI API in batches."""
    try:
        embeddings = []
        total_batch_tokens = 0

        for i in range(0, len(texts), batch_size):
            batch = texts[i : i + batch_size]
            # Add prefix and truncate texts to avoid token limits
            if prefix:
                batch = [f"{prefix} {text}" for text in batch]
            truncated_batch = [text[:8192] for text in batch]

            response = client.embeddings.create(model=model, input=truncated_batch, encoding_format="float")

            # Track token usage
            if hasattr(response, "usage") and response.usage:
                batch_tokens = response.usage.total_tokens
                total_batch_tokens += batch_tokens
                add_token_usage(batch_tokens)

            batch_embeddings = [data.embedding for data in response.data]
            embeddings.extend(batch_embeddings)

        return embeddings
    except Exception as e:
        print(f"Error embedding batch with OpenAI: {e}")
        return [None] * len(texts)


@traced
def embed_text(
    text: str,
    model: str = "text-embedding-3-small",
    use_huggingface: bool = False,
    use_quantization: bool = False,
    prefix: str = None,
) -> List[float]:
    """Generate embedding for text using either OpenAI or Hugging Face."""
    if use_huggingface:
        return embed_text_hf(text, model, use_quantization, prefix)
    else:
        return embed_text_openai(text, model, prefix)


@traced(notrace_io=True)
def embed_documents_parallel(
    documents: List[str],
    model: str = "text-embedding-3-small",
    max_concurrent: int = 20,
    verbose: bool = False,
    use_huggingface: bool = False,
    **kwargs,
) -> List[Optional[List[float]]]:
    """Embed multiple documents using batched API calls."""
    backend = "Hugging Face" if use_huggingface else "OpenAI"
    print(f"Generating embeddings for {len(documents)} documents using {backend} model: {model}...")

    # Pre-load model if using HuggingFace (don't include load time in embedding timing)
    if use_huggingface:
        # Get quantization setting from kwargs or default
        use_quantization = kwargs.get("use_quantization", False)
        load_hf_model(model, use_quantization)  # This will load the model if not already loaded

    start_time = time.time()

    # Filter out empty documents and track their positions
    valid_documents = []
    valid_indices = []
    for i, doc in enumerate(documents):
        if doc.strip():
            valid_documents.append(doc)
            valid_indices.append(i)

    if not valid_documents:
        print("No valid documents to embed")
        return [None] * len(documents)

    if verbose:
        print(
            f"  Embedding {len(valid_documents)} valid documents (filtered {len(documents) - len(valid_documents)} empty)"
        )

    # Choose batch size and processing method based on backend
    use_quantization = kwargs.get("use_quantization", False)
    embedding_prefix = kwargs.get("embedding_prefix", None)
    hf_batch_size = kwargs.get("hf_batch_size", 128)
    use_onnx = kwargs.get("use_onnx", False)
    onnx_model_path = kwargs.get("onnx_model_path", None)

    if use_onnx:
        # ONNX Runtime: Very large batches with minimal concurrency for maximum CPU utilization
        batch_size = 512  # Large batches for ONNX optimization
        max_concurrent = min(max_concurrent, 1)  # Single stream with large batches works best
        embed_func = lambda texts, model, batch_size: embed_batch_onnx(
            texts, model, batch_size, embedding_prefix, onnx_model_path
        )
    elif use_huggingface:
        # HF models: Use larger batches with limited concurrency
        batch_size = hf_batch_size  # Configurable batch size for local processing
        max_concurrent = min(max_concurrent, 4)  # Allow more concurrency on powerful machines
        embed_func = lambda texts, model, batch_size: embed_batch_hf(
            texts, model, batch_size, use_quantization, embedding_prefix
        )
    else:
        # OpenAI: Use smaller batches with higher concurrency
        batch_size = 100  # OpenAI supports up to 2048 inputs per request
        embed_func = lambda texts, model, batch_size: embed_batch_openai(texts, model, batch_size, embedding_prefix)

    if verbose:
        backend = "ONNX Runtime" if use_onnx else ("Hugging Face" if use_huggingface else "OpenAI")
        print(f"  Using {backend} backend")
        print(f"  Using batch size: {batch_size}, max concurrent batches: {max_concurrent}")
        if embedding_prefix:
            print(f"  Using embedding prefix: '{embedding_prefix}'")

    # Split documents into chunks for parallel batch processing
    chunk_size = max(len(valid_documents) // max_concurrent, batch_size)
    chunks = [valid_documents[i : i + chunk_size] for i in range(0, len(valid_documents), chunk_size)]

    if verbose:
        print(f"  Processing {len(chunks)} chunks of ~{chunk_size} documents each")

    valid_embeddings = [None] * len(valid_documents)

    with TracedThreadPoolExecutor(max_workers=max_concurrent) as executor:
        # Submit batch embedding tasks
        future_to_range = {}
        for chunk_idx, chunk in enumerate(chunks):
            start_idx = chunk_idx * chunk_size
            future = executor.submit(embed_func, chunk, model, batch_size)
            future_to_range[future] = (start_idx, len(chunk))

        # Process completed futures
        completed_docs = 0
        for future in as_completed(future_to_range):
            start_idx, chunk_len = future_to_range[future]
            try:
                chunk_embeddings = future.result()
                # Place chunk embeddings in the correct positions
                for i, embedding in enumerate(chunk_embeddings):
                    if start_idx + i < len(valid_embeddings):
                        valid_embeddings[start_idx + i] = embedding
                completed_docs += chunk_len

                if verbose:
                    print(f"  Completed chunk: {completed_docs}/{len(valid_documents)} documents")

            except Exception as e:
                if verbose:
                    print(f"  Error embedding chunk starting at {start_idx}: {e}")
                # Mark chunk embeddings as None
                for i in range(chunk_len):
                    if start_idx + i < len(valid_embeddings):
                        valid_embeddings[start_idx + i] = None
                completed_docs += chunk_len

    # Map valid embeddings back to original document positions
    embeddings = [None] * len(documents)
    for valid_idx, embedding in zip(valid_indices, valid_embeddings):
        embeddings[valid_idx] = embedding

    # Count successful embeddings and calculate timing
    successful = sum(1 for e in embeddings if e is not None)
    embedding_time = time.time() - start_time

    print(f"Successfully embedded {successful}/{len(documents)} documents in {embedding_time:.1f}s")
    if successful > 0:
        avg_time = embedding_time / successful
        print(f"Average time per embedding: {avg_time:.3f}s")

        # Calculate throughput
        throughput = successful / embedding_time
        print(f"Throughput: {throughput:.1f} embeddings/second")

        # Show cost information for OpenAI
        if not use_huggingface:
            total_tokens = get_token_usage()
            if total_tokens > 0:
                cost = calculate_embedding_cost(total_tokens, model)
                print(f"Total tokens used: {total_tokens:,}")
                print(f"Estimated cost: ${cost:.4f}")

    return embeddings


@traced
def cluster_embeddings(
    embeddings: List[List[float]], n_clusters: int = 10, verbose: bool = False
) -> Tuple[np.ndarray, float]:
    """Cluster embeddings using K-means and return cluster labels and silhouette score."""
    print(f"Clustering {len(embeddings)} embeddings into {n_clusters} clusters...")

    # Convert to numpy array
    X = np.array(embeddings)

    # Adjust number of clusters if we have too few samples
    actual_clusters = min(n_clusters, len(embeddings))
    if actual_clusters < n_clusters:
        print(f"Reducing clusters from {n_clusters} to {actual_clusters} due to limited data")

    # Perform K-means clustering
    kmeans = KMeans(n_clusters=actual_clusters, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(X)

    # Calculate silhouette score if we have enough clusters
    silhouette = None
    if actual_clusters > 1 and len(embeddings) > actual_clusters:
        try:
            silhouette = silhouette_score(X, cluster_labels)
            if verbose:
                print(f"Silhouette score: {silhouette:.3f}")
        except Exception as e:
            if verbose:
                print(f"Could not calculate silhouette score: {e}")

    return cluster_labels, silhouette


@traced
def select_cluster_examples(
    logs: List[Dict[str, Any]],
    deduplicated_contents: List[str],
    cluster_labels: np.ndarray,
    n_examples: int = 3,
    verbose: bool = False,
) -> Dict[int, List[Dict[str, Any]]]:
    """Select representative examples from each cluster."""
    cluster_examples = {}

    # Group logs by cluster
    clusters = {}
    for i, label in enumerate(cluster_labels):
        if label not in clusters:
            clusters[label] = []
        clusters[label].append(i)

    print(f"Selecting {n_examples} examples from each of {len(clusters)} clusters...")

    for cluster_id, indices in clusters.items():
        if verbose:
            print(f"  Cluster {cluster_id}: {len(indices)} logs")

        # Select examples (for now, just take first N)
        # Could be enhanced with more sophisticated selection (e.g., closest to centroid)
        selected_indices = indices[:n_examples]

        examples = []
        for idx in selected_indices:
            log = logs[idx]
            examples.append(
                {
                    "log_id": log.get("id", "unknown"),
                    "original_content": extract_log_content(log),
                    "deduplicated_content": deduplicated_contents[idx],
                    "log": log,
                    "cluster_id": int(cluster_id),
                    "cluster_size": len(indices),
                }
            )

        cluster_examples[int(cluster_id)] = examples

    return cluster_examples


@traced
def save_results(
    cluster_examples: Dict[int, List[Dict[str, Any]]],
    silhouette_score: Optional[float],
    output_file: str,
    metadata: Dict[str, Any],
):
    """Save clustering results to a JSON file."""
    results = {
        "metadata": {**metadata, "silhouette_score": silhouette_score, "timestamp": time.time()},
        "clusters": cluster_examples,
        "summary": {
            "total_clusters": len(cluster_examples),
            "total_examples": sum(len(examples) for examples in cluster_examples.values()),
        },
    }

    with open(output_file, "w") as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\nResults saved to {output_file}")


@traced
def print_cluster_summary(cluster_examples: Dict[int, List[Dict[str, Any]]]):
    """Print a summary of the clustering results."""
    print(f"\n{'=' * 80}")
    print(f"CLUSTERING RESULTS SUMMARY")
    print(f"{'=' * 80}")

    for cluster_id in sorted(cluster_examples.keys()):
        examples = cluster_examples[cluster_id]
        cluster_size = examples[0]["cluster_size"] if examples else 0

        print(f"\n--- Cluster {cluster_id} ({cluster_size} total logs) ---")

        for i, example in enumerate(examples, 1):
            print(f"\nExample {i}:")
            print(f"  Log ID: {example['log_id']}")
            print(f"  Content: {example['deduplicated_content'][:200]}...")
            if len(example["deduplicated_content"]) > 200:
                print(f"  (truncated from {len(example['deduplicated_content'])} chars)")


@traced
def main():
    args = parse_args()

    # Set OpenAI API key if using OpenAI
    if not args.use_huggingface:
        openai_api_key = args.openai_api_key or os.environ.get("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError(
                "OpenAI API key required when not using Hugging Face (--openai-api-key or OPENAI_API_KEY env var)"
            )
        openai.api_key = openai_api_key

    # Login to Braintrust
    print(f"Connecting to Braintrust at {args.app_url}...")
    query_state = login_to_state(api_key=args.api_key, app_url=args.app_url)

    # Get project ID and org name
    try:
        project_id, org_name = get_project_info(query_state, args.project_name)
        print(f"Found project: {project_id} (org: {org_name})")
    except ValueError as e:
        print(f"Error: {e}")
        return

    # Initialize Braintrust logger
    login(force_login=True)

    with start_span("smart_log_sampling"):
        # Generate separate cache keys for query vs full pipeline
        embedding_model = args.hf_model if args.use_huggingface else args.embedding_model

        # Query cache key - only depends on BTQL parameters (reused across embedding models)
        query_cache_key = compute_query_cache_key(
            api_key=args.api_key,
            project_name=args.project_name,
            limit=args.limit,
            filter_expr=args.filter,
            deduplicate=not args.no_deduplicate,
            page_size=args.page_size,
        )

        use_cache = not args.no_cache

        # Fetch logs (with caching support from embed_logs)
        print(f"Fetching up to {args.limit} logs from project...")
        try:
            all_logs = fetch_logs_via_btql(
                project_id,
                args.limit,
                args.filter,
                cache_key=query_cache_key if use_cache else None,
                cache_dir=args.cache_dir if use_cache else None,
                use_cache=use_cache,
                use_pagination=args.limit > args.page_size,
                page_size=args.page_size,
                deduplicate=not args.no_deduplicate,
                state=query_state,
            )
        except Exception as e:
            print(f"Error fetching logs: {e}")
            return

        if not all_logs:
            print("No logs found in project")
            return

        print(f"Retrieved {len(all_logs)} logs")

        # Deduplicate content
        print("\nDeduplicating log content...")
        deduplicated_contents = convert_and_deduplicate_log_content(
            all_logs, min_substring_length=args.min_substring_length, cache_dir=args.cache_dir, use_cache=use_cache
        )

        # Filter out empty documents
        valid_indices = [i for i, content in enumerate(deduplicated_contents) if content.strip()]
        if len(valid_indices) < len(deduplicated_contents):
            print(f"Filtered out {len(deduplicated_contents) - len(valid_indices)} empty documents")
            all_logs = [all_logs[i] for i in valid_indices]
            deduplicated_contents = [deduplicated_contents[i] for i in valid_indices]

        if len(all_logs) == 0:
            print("No valid documents remaining after deduplication")
            return

        # Generate embeddings
        embeddings = embed_documents_parallel(
            deduplicated_contents,
            model=embedding_model,
            max_concurrent=args.max_concurrent,
            verbose=args.verbose,
            use_huggingface=args.use_huggingface,
            use_quantization=args.quantize,
            embedding_prefix=args.embedding_prefix,
            hf_batch_size=args.hf_batch_size,
            use_onnx=args.use_onnx,
            onnx_model_path=args.onnx_model_path,
        )

        # Filter out failed embeddings
        valid_embeddings = []
        valid_logs = []
        valid_contents = []
        for i, embedding in enumerate(embeddings):
            if embedding is not None:
                valid_embeddings.append(embedding)
                valid_logs.append(all_logs[i])
                valid_contents.append(deduplicated_contents[i])

        if len(valid_embeddings) == 0:
            print("No valid embeddings generated")
            return

        print(f"Using {len(valid_embeddings)} valid embeddings for clustering")

        # Cluster embeddings
        cluster_labels, silhouette = cluster_embeddings(
            valid_embeddings, n_clusters=args.clusters, verbose=args.verbose
        )

        # Select representative examples
        cluster_examples = select_cluster_examples(
            valid_logs, valid_contents, cluster_labels, n_examples=args.examples_per_cluster, verbose=args.verbose
        )

        # Save results
        metadata = {
            "project_name": args.project_name,
            "project_id": project_id,
            "org_name": org_name,
            "total_logs_fetched": len(all_logs),
            "total_logs_clustered": len(valid_logs),
            "embedding_model": embedding_model,
            "embedding_backend": "huggingface" if args.use_huggingface else "openai",
            "embedding_prefix": args.embedding_prefix,
            "clusters_requested": args.clusters,
            "clusters_created": len(cluster_examples),
            "examples_per_cluster": args.examples_per_cluster,
            "filter_expression": args.filter,
            "min_substring_length": args.min_substring_length,
        }

        # Add cost information for OpenAI
        if not args.use_huggingface:
            total_tokens = get_token_usage()
            if total_tokens > 0:
                cost = calculate_embedding_cost(total_tokens, embedding_model)
                metadata["total_tokens_used"] = total_tokens
                metadata["estimated_cost_usd"] = cost

        save_results(cluster_examples, silhouette, args.output, metadata)

        # Print summary
        print_cluster_summary(cluster_examples)

        if silhouette is not None:
            print(f"\nSilhouette Score: {silhouette:.3f} (higher is better, range: -1 to 1)")

        # Print cost summary for OpenAI
        if not args.use_huggingface:
            total_tokens = get_token_usage()
            if total_tokens > 0:
                cost = calculate_embedding_cost(total_tokens, embedding_model)
                print(f"\n{'=' * 80}")
                print(f"COST SUMMARY")
                print(f"{'=' * 80}")
                print(f"Model: {embedding_model}")
                print(f"Total tokens: {total_tokens:,}")
                print(f"Estimated cost: ${cost:.4f}")
                print(f"Average cost per embedding: ${cost / len(valid_logs):.6f}")
                print(f"{'=' * 80}")


if __name__ == "__main__":
    main()
