#!/usr/bin/env python3
"""
Zero-shot semantic filtering using only embeddings as a first pass.

Treats any natural-language query as an open-vocabulary filter with zero labels.
Uses query prototypes and cosine similarity for fast, robust filtering without LLM calls.

Example usage:
python embedding_filter.py --api-key <API_KEY> --project-name "My Project" --query "user is upset with assistant" --limit 1000 --top-pct 0.10
"""

import argparse
import hashlib
import json
import os
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import openai
from braintrust import (
    login,
    login_to_state,
    start_span,
    traced,
    wrap_openai,
)

# Import shared functions
from embed_logs import (
    extract_user_content,
    fetch_logs_via_btql,
    get_project_id,
)
from interactive_common import (
    format_match_for_display,
    interactive_review,
    print_match_summary,
    save_results_with_feedback,
)
from query_logs import (
    convert_and_deduplicate_log_content,
    extract_log_content,
    get_project_info,
)
from smart_sampling import (
    compute_query_cache_key,
    embed_batch_hf,
    embed_batch_onnx,
    embed_batch_openai,
    load_hf_model,
)

client = wrap_openai(openai.OpenAI())


def parse_args():
    parser = argparse.ArgumentParser(description="Zero-shot semantic filtering using embeddings")
    parser.add_argument("--api-key", required=True, help="Braintrust API key")
    parser.add_argument(
        "--app-url",
        default="https://www.braintrust.dev",
        help="Braintrust app URL (default: https://www.braintrust.dev)",
    )
    parser.add_argument("--project-name", required=True, help="Name of the project to filter logs from")
    parser.add_argument("--query", required=True, help="Natural language query to filter for")
    parser.add_argument("--limit", type=int, default=1000, help="Maximum number of logs to fetch (default: 1000)")
    parser.add_argument("--page-size", type=int, default=1000, help="Number of logs to fetch per page (default: 1000)")
    parser.add_argument("--filter", help="Optional BTQL filter expression to pre-filter logs")
    parser.add_argument("--openai-api-key", help="OpenAI API key for embeddings (defaults to OPENAI_API_KEY env var)")
    parser.add_argument(
        "--embedding-model",
        default="text-embedding-3-small",
        help="OpenAI embedding model (default: text-embedding-3-small)",
    )
    parser.add_argument(
        "--use-huggingface",
        action="store_true",
        help="Use Hugging Face transformers instead of OpenAI for embeddings",
    )
    parser.add_argument(
        "--hf-model",
        default="sentence-transformers/all-MiniLM-L6-v2",
        help="Hugging Face model for embeddings (default: sentence-transformers/all-MiniLM-L6-v2)",
    )
    parser.add_argument(
        "--quantize",
        action="store_true",
        help="Use quantization for Hugging Face models (reduces memory usage)",
    )
    parser.add_argument(
        "--use-onnx",
        action="store_true",
        help="Use ONNX Runtime for faster CPU inference (requires model export)",
    )
    parser.add_argument(
        "--onnx-model-path",
        type=str,
        help="Path to ONNX model directory (if not provided, will try to export automatically)",
    )
    parser.add_argument(
        "--top-k",
        type=int,
        help="Return top K matches (alternative to top-pct)",
    )
    parser.add_argument(
        "--top-pct",
        type=float,
        default=0.10,
        help="Return top percentage of matches (default: 0.10 = top 10%%)",
    )
    parser.add_argument(
        "--z-score-threshold",
        type=float,
        default=2.0,
        help="Z-score threshold for fallback selection (default: 2.0)",
    )
    parser.add_argument(
        "--cache-dir",
        default=".cache",
        help="Directory to cache embeddings and query results (default: .cache)",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable caching of embeddings and query results",
    )
    parser.add_argument(
        "--output",
        default="embedding_filter_results.json",
        help="Output file for results (default: embedding_filter_results.json)",
    )
    parser.add_argument("--verbose", action="store_true", help="Show detailed progress")
    parser.add_argument(
        "--no-deduplicate",
        action="store_true",
        help="Disable deduplication by root_span_id (keep all spans from same trace)",
    )
    parser.add_argument(
        "--min-substring-length",
        type=int,
        default=20,
        help="Minimum length for common substrings to remove (default: 20)",
    )
    parser.add_argument(
        "--hf-batch-size",
        type=int,
        default=128,
        help="Batch size for Hugging Face embedding generation (default: 128)",
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=20,
        help="Maximum concurrent embedding calls (default: 20)",
    )
    parser.add_argument(
        "--embedding-prefix",
        type=str,
        help="Phrase to prepend to each document before embedding (helps focus filtering on specific topics)",
    )
    parser.add_argument(
        "--use-confirmer",
        action="store_true",
        help="Use heuristic confirmers for specific query intents (experimental)",
    )
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enable interactive mode to review and rate matches",
    )
    return parser.parse_args()


def rewrite_query(query: str) -> List[str]:
    """Generate query rewrites to make prototype more robust."""
    query = query.strip()

    # Basic rewrites for different phrasings
    rewrites = [
        query,
        f"examples of {query}",
        f"find {query}",
    ]

    # Add negation variant if query contains "not"
    if "not " in query.lower() or "n't " in query.lower():
        rewrites.append(f"{query} (negative case)")

    # Add noun-phrase variant by removing common prefixes
    clean_query = query
    for prefix in ["find ", "show me ", "examples of ", "get ", "retrieve "]:
        if query.lower().startswith(prefix):
            clean_query = query[len(prefix) :].strip()
            break

    if clean_query != query:
        rewrites.append(clean_query)

    # Add context-aware variants for common patterns
    if "user" in query.lower() and (
        "upset" in query.lower() or "angry" in query.lower() or "frustrated" in query.lower()
    ):
        rewrites.extend(["negative user feedback", "user expressing dissatisfaction", "complaints about service"])

    if "error" in query.lower() or "bug" in query.lower() or "issue" in query.lower():
        rewrites.extend(["technical problems", "system failures", "error conditions"])

    if "payment" in query.lower() or "billing" in query.lower() or "subscription" in query.lower():
        rewrites.extend(["financial transactions", "billing inquiries", "payment processing"])

    # Remove duplicates while preserving order
    unique_rewrites = []
    seen = set()
    for rewrite in rewrites:
        if rewrite not in seen:
            unique_rewrites.append(rewrite)
            seen.add(rewrite)

    return unique_rewrites


def make_query_prototype(query: str, embed_func) -> np.ndarray:
    """Create a prototype embedding from query rewrites."""
    texts = rewrite_query(query)

    # Get embeddings for all rewrites
    embeddings = embed_func(texts)

    # Filter out None embeddings
    valid_embeddings = [emb for emb in embeddings if emb is not None]

    if not valid_embeddings:
        raise ValueError("Failed to generate any valid embeddings for query")

    # Convert to numpy and L2 normalize
    V = np.array(valid_embeddings, dtype=np.float32)
    V = V / (np.linalg.norm(V, axis=1, keepdims=True) + 1e-12)

    # Average and re-normalize
    prototype = V.mean(axis=0)
    prototype = prototype / (np.linalg.norm(prototype) + 1e-12)

    return prototype


def make_contrast_prototype(query: str, embed_func) -> np.ndarray:
    """Create a contrastive prototype by subtracting negative terms."""
    # Define negative contrasts for common query types
    NEG_BANK = {
        "upset": ["user is satisfied with the assistant", "positive feedback about assistant", "user is happy"],
        "angry": ["user is pleased", "user is calm", "positive interaction"],
        "frustrated": ["user is satisfied", "user is pleased", "smooth experience"],
        "error": ["successful operation", "working correctly", "no issues"],
        "bug": ["functioning properly", "working as expected", "successful execution"],
        "slow": ["fast response", "quick processing", "optimal performance"],
        "failed": ["successful completion", "working correctly", "completed successfully"],
    }

    # Get positive prototype
    pos_prototype = make_query_prototype(query, embed_func)

    # Find relevant negative terms
    neg_terms = []
    query_lower = query.lower()
    for key, terms in NEG_BANK.items():
        if key in query_lower:
            neg_terms.extend(terms)

    if not neg_terms:
        return pos_prototype

    # Get negative embeddings
    neg_embeddings = embed_func(neg_terms)
    valid_neg_embeddings = [emb for emb in neg_embeddings if emb is not None]

    if not valid_neg_embeddings:
        return pos_prototype

    # Create negative prototype
    Vn = np.array(valid_neg_embeddings, dtype=np.float32)
    Vn = Vn / (np.linalg.norm(Vn, axis=1, keepdims=True) + 1e-12)
    neg_prototype = Vn.mean(axis=0)
    neg_prototype = neg_prototype / (np.linalg.norm(neg_prototype) + 1e-12)

    # Subtract negative component with reduced weight
    contrastive = pos_prototype - 0.3 * neg_prototype
    contrastive = contrastive / (np.linalg.norm(contrastive) + 1e-12)

    return contrastive


def scores_against_prototype(embeddings: np.ndarray, prototype: np.ndarray) -> np.ndarray:
    """Compute cosine similarity scores against prototype."""
    # Ensure embeddings are L2 normalized
    norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
    normalized_embeddings = embeddings / (norms + 1e-12)

    # Cosine similarity is just dot product for normalized vectors
    scores = normalized_embeddings @ prototype.astype(normalized_embeddings.dtype)
    return scores


def select_candidates(
    scores: np.ndarray, top_k: Optional[int] = None, top_pct: Optional[float] = None, z_score_threshold: float = 2.0
):
    """Select candidates using distribution-aware selection."""
    if top_k:
        # Simple top-K selection
        if top_k >= len(scores):
            idx = np.arange(len(scores))
        else:
            idx = np.argpartition(scores, -top_k)[-top_k:]
        return idx[np.argsort(scores[idx])[::-1]]

    if top_pct:
        # Quantile-based selection
        threshold = np.quantile(scores, 1 - top_pct)
        idx = np.where(scores >= threshold)[0]
        return idx[np.argsort(scores[idx])[::-1]]

    # Fallback: z-score filter
    mu = scores.mean()
    sd = scores.std() + 1e-9
    z_scores = (scores - mu) / sd
    idx = np.where(z_scores >= z_score_threshold)[0]
    return idx[np.argsort(scores[idx])[::-1]]


# Heuristic confirmers for specific intents
def confirm_upset_user(text: str) -> bool:
    """Heuristic confirmer for upset/angry user queries."""
    NEG_POLAR = {
        "terrible",
        "awful",
        "useless",
        "broken",
        "worst",
        "frustrated",
        "angry",
        "annoyed",
        "hate",
        "horrible",
        "sucks",
        "stupid",
        "ridiculous",
    }
    ASSIST_PATTERN = re.compile(r"\b(assistant|chatbot|you|your|bot|ai|system|service|support)\b", re.I)

    # Tokenize and check for negative sentiment
    tokens = re.findall(r"[a-zA-Z']+", text.lower())
    neg_count = sum(1 for token in tokens if token in NEG_POLAR)

    # Check for ALL CAPS (indication of shouting)
    caps_words = re.findall(r"\b[A-Z]{3,}\b", text)
    caps_ratio = len(caps_words) / max(1, len(tokens))

    # Check for assistant/service mentions
    assistant_mention = bool(ASSIST_PATTERN.search(text))

    # Check for exclamation marks
    exclamation_count = text.count("!")

    # Scoring: need at least 2 indicators
    score = (
        (neg_count >= 2)  # Multiple negative words
        + (caps_ratio > 0.1)  # >10% caps words
        + assistant_mention  # Mentions assistant/service
        + (exclamation_count >= 2)  # Multiple exclamations
    )

    return score >= 2


def confirm_error_issue(text: str) -> bool:
    """Heuristic confirmer for error/bug/issue queries."""
    ERROR_TERMS = {
        "error",
        "exception",
        "traceback",
        "stack trace",
        "failed",
        "failure",
        "bug",
        "issue",
        "problem",
        "crash",
        "broke",
        "broken",
    }
    STATUS_CODES = re.compile(r"\b[45]\d\d\b")  # 4xx, 5xx HTTP status codes

    tokens = set(re.findall(r"[a-zA-Z']+", text.lower()))
    error_count = len(tokens.intersection(ERROR_TERMS))
    has_status_code = bool(STATUS_CODES.search(text))

    # Check for technical error patterns
    technical_patterns = [
        r"null\s+pointer",
        r"index\s+out\s+of\s+bounds",
        r"connection\s+refused",
        r"timeout",
        r"not\s+found",
        r"unauthorized",
        r"forbidden",
    ]

    technical_match = any(re.search(pattern, text.lower()) for pattern in technical_patterns)

    return error_count >= 1 or has_status_code or technical_match


CONFIRMER_MAP = {
    "upset": confirm_upset_user,
    "angry": confirm_upset_user,
    "frustrated": confirm_upset_user,
    "error": confirm_error_issue,
    "bug": confirm_error_issue,
    "issue": confirm_error_issue,
    "problem": confirm_error_issue,
    "failed": confirm_error_issue,
    "failure": confirm_error_issue,
}


def get_confirmer_for_query(query: str):
    """Get appropriate confirmer function for query, if any."""
    query_lower = query.lower()
    for key, confirmer in CONFIRMER_MAP.items():
        if key in query_lower:
            return confirmer
    return None


def compute_embedding_cache_key(
    texts: List[str],
    model: str,
    use_huggingface: bool,
    use_onnx: bool,
    embedding_prefix: Optional[str] = None,
    quantize: bool = False,
) -> str:
    """Compute cache key for embeddings."""
    # Create a hash of the texts and embedding parameters
    text_hash = hashlib.sha256("\n".join(texts).encode()).hexdigest()[:16]

    backend = "onnx" if use_onnx else ("hf" if use_huggingface else "openai")
    prefix_part = f"_prefix_{hashlib.sha256(embedding_prefix.encode()).hexdigest()[:8]}" if embedding_prefix else ""
    quantize_part = "_quantized" if quantize else ""

    cache_parts = [
        backend,
        model.replace("/", "_"),
        text_hash,
        str(len(texts)),
        prefix_part,
        quantize_part,
    ]

    cache_string = ":".join(cache_parts)
    return hashlib.sha256(cache_string.encode()).hexdigest()[:16]


@traced
def embed_documents_with_cache(
    texts: List[str],
    model: str,
    use_huggingface: bool = False,
    use_onnx: bool = False,
    embedding_prefix: Optional[str] = None,
    quantize: bool = False,
    hf_batch_size: int = 128,
    onnx_model_path: Optional[str] = None,
    cache_dir: str = ".cache",
    use_cache: bool = True,
    verbose: bool = False,
) -> np.ndarray:
    """Embed documents with caching support."""
    if not texts:
        return np.array([])

    # Check cache first
    if use_cache:
        cache_key = compute_embedding_cache_key(texts, model, use_huggingface, use_onnx, embedding_prefix, quantize)
        cache_path = Path(cache_dir)
        cache_path.mkdir(exist_ok=True)
        embedding_cache_path = cache_path / f"{cache_key}_embeddings.json"

        if embedding_cache_path.exists():
            if verbose:
                print(f"Loading embeddings from cache: {embedding_cache_path}")
            with open(embedding_cache_path, "r") as f:
                cached_data = json.load(f)
            return np.array(cached_data["embeddings"], dtype=np.float32)

    # Generate embeddings
    if verbose:
        backend = "ONNX Runtime" if use_onnx else ("Hugging Face" if use_huggingface else "OpenAI")
        print(f"Generating embeddings for {len(texts)} texts using {backend} model: {model}")

    # Pre-load model if using HuggingFace
    if use_huggingface:
        load_hf_model(model, quantize)

    start_time = time.time()

    # Choose embedding function
    if use_onnx:
        embeddings = embed_batch_onnx(texts, model, batch_size=512, prefix=embedding_prefix, onnx_path=onnx_model_path)
    elif use_huggingface:
        embeddings = embed_batch_hf(
            texts, model, batch_size=hf_batch_size, use_quantization=quantize, prefix=embedding_prefix
        )
    else:
        embeddings = embed_batch_openai(texts, model, batch_size=100, prefix=embedding_prefix)

    # Filter out None embeddings and convert to numpy
    valid_embeddings = []
    for emb in embeddings:
        if emb is not None:
            valid_embeddings.append(emb)
        else:
            # Use zero vector for failed embeddings
            if valid_embeddings:
                zero_emb = [0.0] * len(valid_embeddings[0])
            else:
                zero_emb = [0.0] * 384  # Default embedding size
            valid_embeddings.append(zero_emb)

    embeddings_array = np.array(valid_embeddings, dtype=np.float32)

    # L2 normalize embeddings
    norms = np.linalg.norm(embeddings_array, axis=1, keepdims=True)
    embeddings_array = embeddings_array / (norms + 1e-12)

    embedding_time = time.time() - start_time
    if verbose:
        print(f"Generated {len(embeddings_array)} embeddings in {embedding_time:.2f}s")
        print(f"Throughput: {len(embeddings_array) / embedding_time:.1f} embeddings/second")

    # Cache results
    if use_cache:
        if verbose:
            print(f"Saving embeddings to cache: {embedding_cache_path}")
        with open(embedding_cache_path, "w") as f:
            json.dump(
                {
                    "embeddings": embeddings_array.tolist(),
                    "model": model,
                    "backend": "onnx" if use_onnx else ("hf" if use_huggingface else "openai"),
                    "embedding_prefix": embedding_prefix,
                    "quantize": quantize,
                    "timestamp": time.time(),
                },
                f,
            )

    return embeddings_array


@traced
def zero_shot_filter(
    query: str,
    texts: List[str],
    logs: List[Dict[str, Any]],
    embed_func,
    top_k: Optional[int] = None,
    top_pct: Optional[float] = None,
    z_score_threshold: float = 2.0,
    use_confirmer: bool = False,
    verbose: bool = False,
    org_name: str = "unknown",
    project_name: str = "unknown",
    app_url: str = "https://www.braintrust.dev",
) -> List[Dict[str, Any]]:
    """Zero-shot filtering using embedding similarity."""
    if not texts:
        return []

    # 1) Create query prototype
    if verbose:
        print(f"Creating prototype for query: '{query}'")

    prototype = make_contrast_prototype(query, embed_func)

    # 2) Embed all documents
    if verbose:
        print(f"Embedding {len(texts)} documents...")

    embeddings_array = embed_func(texts)

    # 3) Score against prototype
    if verbose:
        print("Computing similarity scores...")

    scores = scores_against_prototype(embeddings_array, prototype)

    # 4) Select candidates
    candidate_indices = select_candidates(scores, top_k, top_pct, z_score_threshold)

    if verbose:
        print(f"Selected {len(candidate_indices)} candidates from {len(texts)} texts")
        if len(candidate_indices) > 0:
            print(f"Score range: {scores[candidate_indices].min():.3f} to {scores[candidate_indices].max():.3f}")

    # 5) Optional confirmer
    if use_confirmer:
        confirmer = get_confirmer_for_query(query)
        if confirmer:
            confirmed_indices = []
            for idx in candidate_indices:
                if confirmer(texts[idx]):
                    confirmed_indices.append(idx)
            if verbose:
                print(f"Confirmer filtered {len(candidate_indices)} -> {len(confirmed_indices)} candidates")
            candidate_indices = np.array(confirmed_indices)

    # 6) Format results for display
    matches = []
    for i, idx in enumerate(candidate_indices):
        match = format_match_for_display(
            log=logs[idx],
            content_preview=texts[idx],
            org_name=org_name,
            project_name=project_name,
            app_url=app_url,
            similarity_score=float(scores[idx]),
        )
        matches.append(match)

    return matches


@traced
def main():
    args = parse_args()

    # Set OpenAI API key if using OpenAI
    if not args.use_huggingface and not args.use_onnx:
        openai_api_key = args.openai_api_key or os.environ.get("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError(
                "OpenAI API key required when not using Hugging Face (--openai-api-key or OPENAI_API_KEY env var)"
            )
        openai.api_key = openai_api_key

    # Login to Braintrust
    print(f"Connecting to Braintrust at {args.app_url}...")
    query_state = login_to_state(api_key=args.api_key, app_url=args.app_url)

    # Get project ID and org name
    try:
        project_id, org_name = get_project_info(query_state, args.project_name)
        print(f"Found project: {project_id} (org: {org_name})")
    except ValueError as e:
        print(f"Error: {e}")
        return

    # Initialize Braintrust logger
    login(force_login=True)

    with start_span("embedding_filter"):
        # Generate cache key for query results (independent of embedding model)
        query_cache_key = compute_query_cache_key(
            api_key=args.api_key,
            project_name=args.project_name,
            limit=args.limit,
            filter_expr=args.filter,
            deduplicate=not args.no_deduplicate,
            page_size=args.page_size,
        )

        use_cache = not args.no_cache

        # Fetch logs
        print(f"Fetching up to {args.limit} logs from project...")
        try:
            all_logs = fetch_logs_via_btql(
                project_id,
                args.limit,
                args.filter,
                cache_key=query_cache_key if use_cache else None,
                cache_dir=args.cache_dir if use_cache else None,
                use_cache=use_cache,
                use_pagination=args.limit > args.page_size,
                page_size=args.page_size,
                deduplicate=not args.no_deduplicate,
                state=query_state,
            )
        except Exception as e:
            print(f"Error fetching logs: {e}")
            return

        if not all_logs:
            print("No logs found in project")
            return

        print(f"Retrieved {len(all_logs)} logs")

        # Deduplicate content
        print("\nDeduplicating log content...")
        deduplicated_contents = convert_and_deduplicate_log_content(
            all_logs,
            min_substring_length=args.min_substring_length,
            cache_dir=args.cache_dir,
            use_cache=use_cache,
        )

        # Filter out empty documents
        valid_indices = [i for i, content in enumerate(deduplicated_contents) if content.strip()]
        if len(valid_indices) < len(deduplicated_contents):
            print(f"Filtered out {len(deduplicated_contents) - len(valid_indices)} empty documents")
            all_logs = [all_logs[i] for i in valid_indices]
            deduplicated_contents = [deduplicated_contents[i] for i in valid_indices]

        if len(all_logs) == 0:
            print("No valid documents remaining after deduplication")
            return

        # Create embedding function
        embedding_model = args.hf_model if args.use_huggingface else args.embedding_model

        def embed_func(texts):
            return embed_documents_with_cache(
                texts=texts,
                model=embedding_model,
                use_huggingface=args.use_huggingface,
                use_onnx=args.use_onnx,
                embedding_prefix=args.embedding_prefix,
                quantize=args.quantize,
                hf_batch_size=args.hf_batch_size,
                onnx_model_path=args.onnx_model_path,
                cache_dir=args.cache_dir,
                use_cache=use_cache,
                verbose=args.verbose,
            )

        # Define rerun function for interactive mode
        def rerun_with_feedback(feedback_examples=None, **kwargs):
            """Re-run zero-shot filtering with feedback examples (not used in embedding mode yet)."""
            # For now, embedding-based filtering doesn't use feedback examples
            # But we can re-run with same parameters
            return zero_shot_filter(
                query=args.query,
                texts=deduplicated_contents,
                logs=all_logs,
                embed_func=embed_func,
                top_k=args.top_k,
                top_pct=args.top_pct,
                z_score_threshold=args.z_score_threshold,
                use_confirmer=args.use_confirmer,
                verbose=args.verbose,
                org_name=org_name,
                project_name=args.project_name,
                app_url=args.app_url,
            )

        # Perform zero-shot filtering
        print(f"\nPerforming zero-shot filtering...")
        matches = zero_shot_filter(
            query=args.query,
            texts=deduplicated_contents,
            logs=all_logs,
            embed_func=embed_func,
            top_k=args.top_k,
            top_pct=args.top_pct,
            z_score_threshold=args.z_score_threshold,
            use_confirmer=args.use_confirmer,
            verbose=args.verbose,
            org_name=org_name,
            project_name=args.project_name,
            app_url=args.app_url,
        )

        # Print results
        print_match_summary(matches, args.query, "EMBEDDING FILTER")

        # Interactive review if requested
        feedback_data = None
        if args.interactive and matches:
            feedback_data = interactive_review(
                matches=matches,
                query=args.query,
                rerun_func=rerun_with_feedback,
            )

        # Save results
        metadata = {
            "project_name": args.project_name,
            "embedding_model": embedding_model,
            "backend": "onnx" if args.use_onnx else ("huggingface" if args.use_huggingface else "openai"),
            "total_logs_fetched": len(all_logs),
            "selection_method": "top-k" if args.top_k else "top-pct" if args.top_pct else "z-score",
            "selection_value": args.top_k or args.top_pct or args.z_score_threshold,
            "use_confirmer": args.use_confirmer,
            "embedding_prefix": args.embedding_prefix,
        }

        save_results_with_feedback(
            matches=matches,
            query=args.query,
            output_file=args.output,
            feedback_data=feedback_data,
            metadata=metadata,
        )


if __name__ == "__main__":
    main()
