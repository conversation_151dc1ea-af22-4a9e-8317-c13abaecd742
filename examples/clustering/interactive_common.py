#!/usr/bin/env python3
"""
Shared interactive components for log filtering and analysis scripts.

Contains common functionality for reviewing matches, providing feedback,
and re-running searches with improved context.
"""

import time
import webbrowser
from typing import Any, Callable, Dict, List, Optional
from urllib.parse import quote

from braintrust import traced


def prepare_feedback_examples(
    matches: List[Dict[str, Any]], feedback_data: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Prepare feedback examples by merging matches with feedback data."""
    examples = []
    for feedback in feedback_data:
        # Find the corresponding match
        match = next((m for m in matches if m["log_id"] == feedback["log_id"]), None)
        if match:
            examples.append(
                {
                    "log_id": feedback["log_id"],
                    "correct": feedback["correct"],
                    "reason": feedback.get("reason", ""),
                    "content_preview": match.get("content_preview", ""),
                }
            )
    return examples


@traced
def interactive_review(
    matches: List[Dict[str, Any]], query: str, rerun_func: Optional[Callable] = None, **rerun_kwargs
) -> Dict[str, Any]:
    """
    Interactive REPL for reviewing and rating matches.

    Args:
        matches: List of match dictionaries with log_id, content_preview, etc.
        query: The search query
        rerun_func: Optional function to call for re-running search with feedback
        **rerun_kwargs: Additional kwargs to pass to rerun_func

    Returns:
        Dictionary with feedback data and summary
    """
    if not matches:
        print("No matches to review!")
        return {"feedback": []}

    print(f"\n{'=' * 80}")
    print(f"INTERACTIVE REVIEW MODE")
    print(f"{'=' * 80}")
    print(f'Query: "{query}"')
    print(f"Total matches: {len(matches)}")
    print("\nCommands:")
    print("  next       - Next match")
    print("  prev       - Previous match")
    print("  g <num>    - Go to match number")
    print("  y/yes      - Mark current match as CORRECT")
    print("  no         - Mark current match as INCORRECT")
    print("  y <reason> - Mark CORRECT with reason")
    print("  no <reason> - Mark INCORRECT with reason")
    print("  clear      - Clear current match rating")
    if rerun_func:
        print("  rerun      - Re-run search with feedback as examples")
    print("  s/show     - Show current match details again")
    print("  l/list     - List all matches with ratings")
    print("  u/url      - Open Braintrust URL (if available)")
    print("  h/help     - Show this help")
    print("  q/quit     - Quit review mode")
    print("=" * 80)

    current_idx = 0
    feedback_data = []

    def show_match(idx):
        if 0 <= idx < len(matches):
            match = matches[idx]
            print(f"\n--- Match {idx + 1} of {len(matches)} ---")
            print(f"Log ID: {match.get('log_id', 'unknown')}")

            # Show reasoning if available (from LLM-based filtering)
            if match.get("reasoning"):
                print(f"Reasoning: {match['reasoning']}")

            # Show similarity score if available (from embedding-based filtering)
            if match.get("similarity_score"):
                print(f"Similarity Score: {match['similarity_score']:.3f}")

            # Parse and display content in a cleaner format
            content_preview = match.get("content_preview", "N/A")

            # Split content into sections for better readability
            lines = content_preview.split("\n")

            print(f"\n📋 Content Details:")
            for line in lines[:10]:  # Show first 10 lines
                if line.startswith("User Input: "):
                    user_content = line[12:]  # Remove "User Input: " prefix
                    print(f"  👤 USER: {user_content[:150]}{'...' if len(user_content) > 150 else ''}")
                elif line.startswith("Output: "):
                    output_content = line[8:]  # Remove "Output: " prefix
                    print(f"  🤖 ASSISTANT: {output_content[:150]}{'...' if len(output_content) > 150 else ''}")
                elif line.startswith("Raw Input: "):
                    raw_content = line[11:]  # Remove "Raw Input: " prefix
                    print(f"  📝 RAW INPUT: {raw_content[:150]}{'...' if len(raw_content) > 150 else ''}")
                elif line.startswith("Raw Output: "):
                    raw_content = line[12:]  # Remove "Raw Output: " prefix
                    print(f"  📄 RAW OUTPUT: {raw_content[:150]}{'...' if len(raw_content) > 150 else ''}")
                elif line.startswith("Metadata: "):
                    metadata_content = line[10:]  # Remove "Metadata: " prefix
                    print(f"  🏷️  METADATA: {metadata_content[:100]}{'...' if len(metadata_content) > 100 else ''}")
                elif line.startswith("Error: "):
                    error_content = line[7:]  # Remove "Error: " prefix
                    print(f"  ❌ ERROR: {error_content[:100]}{'...' if len(error_content) > 100 else ''}")
                elif line.startswith("Span Info: "):
                    span_content = line[11:]  # Remove "Span Info: " prefix
                    print(f"  🔍 SPAN: {span_content[:100]}{'...' if len(span_content) > 100 else ''}")
                elif line.strip():  # Only show non-empty lines
                    print(f"  📄 {line[:150]}{'...' if len(line) > 150 else ''}")

            if match.get("braintrust_link"):
                print(f"URL: {match['braintrust_link']}")

            if match.get("permalink"):
                print(f"Span: {match['permalink']}")

            # Show current feedback if any
            existing_feedback = next((f for f in feedback_data if f["log_id"] == match.get("log_id")), None)
            if existing_feedback:
                rating = "✓ CORRECT" if existing_feedback["correct"] else "✗ INCORRECT"
                reason = f" - {existing_feedback['reason']}" if existing_feedback["reason"] else ""
                print(f"Current rating: {rating}{reason}")
            else:
                print("Current rating: (not rated)")
        else:
            print("Invalid match number")

    def add_feedback(log_id, correct, reason=""):
        # Remove existing feedback for this log
        nonlocal feedback_data
        feedback_data = [f for f in feedback_data if f["log_id"] != log_id]
        # Add new feedback
        feedback_data.append({"log_id": log_id, "correct": correct, "reason": reason, "timestamp": time.time()})

    # Show first match
    show_match(current_idx)

    while True:
        try:
            user_input = input(f"\n[{current_idx + 1}/{len(matches)}] > ").strip()
            if not user_input:
                continue

            parts = user_input.split(" ", 1)
            command = parts[0].lower()
            arg = parts[1] if len(parts) > 1 else ""

            if command in ["q", "quit"]:
                break
            elif command in ["next"]:
                current_idx = min(current_idx + 1, len(matches) - 1)
                show_match(current_idx)
            elif command in ["prev"]:
                current_idx = max(current_idx - 1, 0)
                show_match(current_idx)
            elif command in ["g", "go"]:
                try:
                    new_idx = int(arg) - 1  # Convert to 0-based
                    if 0 <= new_idx < len(matches):
                        current_idx = new_idx
                        show_match(current_idx)
                    else:
                        print(f"Invalid match number. Must be 1-{len(matches)}")
                except ValueError:
                    print("Invalid number format. Use: g <number>")
            elif command in ["y", "yes"]:
                match = matches[current_idx]
                add_feedback(match.get("log_id", "unknown"), True, arg)
                rating_text = f" (reason: {arg})" if arg else ""
                print(f"✓ Marked as CORRECT{rating_text}")
            elif command in ["no"]:
                match = matches[current_idx]
                add_feedback(match.get("log_id", "unknown"), False, arg)
                rating_text = f" (reason: {arg})" if arg else ""
                print(f"✗ Marked as INCORRECT{rating_text}")
            elif command in ["clear"]:
                match = matches[current_idx]
                feedback_data = [f for f in feedback_data if f["log_id"] != match.get("log_id", "unknown")]
                print("✓ Rating cleared")
                show_match(current_idx)  # Refresh display
            elif command in ["rerun"]:
                if not rerun_func:
                    print("Re-run functionality not available for this filtering method")
                elif not feedback_data:
                    print("No feedback examples available. Please rate some matches first.")
                else:
                    print(f"Re-running search with {len(feedback_data)} feedback examples...")

                    # Prepare feedback examples
                    feedback_examples = prepare_feedback_examples(matches, feedback_data)

                    if feedback_examples:
                        print(f"Re-running with {len(feedback_examples)} feedback examples:")
                        for i, ex in enumerate(feedback_examples[:3]):  # Show first 3 examples
                            status = "CORRECT" if ex["correct"] else "INCORRECT"
                            reason = f" ({ex.get('reason', 'no reason')})" if ex.get("reason") else ""
                            print(f"  {i + 1}. {status}: {ex['log_id']}{reason}")

                    # Re-run the query with feedback context
                    print("Starting new search with improved context...")
                    new_matches = rerun_func(feedback_examples=feedback_examples, **rerun_kwargs)

                    if new_matches:
                        print(f"Found {len(new_matches)} new matches. Replacing current results...")

                        # Preserve existing feedback for any logs that appear in the new results
                        preserved_feedback = []
                        new_log_ids = {match.get("log_id") for match in new_matches}
                        for feedback in feedback_data:
                            if feedback["log_id"] in new_log_ids:
                                preserved_feedback.append(feedback)

                        # Replace matches and update feedback data
                        matches.clear()
                        matches.extend(new_matches)
                        feedback_data.clear()
                        feedback_data.extend(preserved_feedback)  # Keep relevant feedback

                        print(
                            f"Preserved {len(preserved_feedback)} existing feedback entries for logs that reappeared"
                        )

                        current_idx = 0
                        show_match(current_idx)
                    else:
                        print("No new matches found with the updated context.")
            elif command in ["s", "show"]:
                show_match(current_idx)
            elif command in ["l", "list"]:
                print(f"\nAll matches:")
                for i, match in enumerate(matches):
                    existing_feedback = next((f for f in feedback_data if f["log_id"] == match.get("log_id")), None)
                    if existing_feedback:
                        rating = "✓" if existing_feedback["correct"] else "✗"
                        reason = f" ({existing_feedback['reason']})" if existing_feedback["reason"] else ""
                    else:
                        rating = "?"
                        reason = ""

                    # Show different preview based on match type
                    if match.get("reasoning"):
                        preview = match.get("reasoning", "")[:60]
                    else:
                        preview = match.get("content_preview", "")[:60]

                    print(f"  {i + 1:2d}. {rating} {match.get('log_id', 'unknown')} - {preview}...{reason}")
            elif command in ["u", "url"]:
                match = matches[current_idx]
                if match.get("braintrust_link"):
                    print(f"URL: {match['braintrust_link']}")
                    # Try to open URL if possible
                    try:
                        webbrowser.open(match["braintrust_link"])
                        print("Opened in browser")
                    except:
                        pass
                else:
                    print("No URL available for this match")
            elif command in ["h", "help"]:
                print("\nCommands:")
                print("  next       - Next match")
                print("  prev       - Previous match")
                print("  g <num>    - Go to match number")
                print("  y/yes      - Mark current match as CORRECT")
                print("  no         - Mark current match as INCORRECT")
                print("  y <reason> - Mark CORRECT with reason")
                print("  no <reason> - Mark INCORRECT with reason")
                print("  clear      - Clear current match rating")
                if rerun_func:
                    print("  rerun      - Re-run search with feedback as examples")
                print("  s/show     - Show current match details again")
                print("  l/list     - List all matches with ratings")
                print("  u/url      - Open Braintrust URL (if available)")
                print("  h/help     - Show this help")
                print("  q/quit     - Quit review mode")
            else:
                print(f"Unknown command '{command}'. Type 'h' for help.")

        except KeyboardInterrupt:
            print("\n\nExiting review mode...")
            break
        except EOFError:
            print("\n\nExiting review mode...")
            break

    # Summary
    correct_count = sum(1 for f in feedback_data if f["correct"])
    incorrect_count = sum(1 for f in feedback_data if not f["correct"])
    unrated_count = len(matches) - len(feedback_data)

    print(f"\nReview Summary:")
    print(f"  ✓ Correct: {correct_count}")
    print(f"  ✗ Incorrect: {incorrect_count}")
    print(f"  ? Unrated: {unrated_count}")

    return {
        "feedback": feedback_data,
        "summary": {"correct": correct_count, "incorrect": incorrect_count, "unrated": unrated_count},
    }


def generate_braintrust_link(log_id: str, org_name: str, project_name: str, app_url: str) -> str:
    """Generate Braintrust log link with URL encoding."""
    base_url = app_url.rstrip("/")
    return f"{base_url}/app/{quote(org_name)}/p/{quote(project_name)}/logs?r={quote(log_id)}"


def format_match_for_display(
    log: Dict[str, Any],
    content_preview: str,
    org_name: str,
    project_name: str,
    app_url: str,
    **extra_fields,
) -> Dict[str, Any]:
    """Format a log match for consistent display across scripts."""
    match = {
        "log_id": log.get("id", "unknown"),
        "log": log,
        "content_preview": content_preview[:200] + "..." if len(content_preview) > 200 else content_preview,
        "braintrust_link": generate_braintrust_link(log.get("id", "unknown"), org_name, project_name, app_url),
    }

    # Add any extra fields (like reasoning, similarity_score, etc.)
    match.update(extra_fields)

    return match


@traced
def save_results_with_feedback(
    matches: List[Dict[str, Any]],
    query: str,
    output_file: str,
    feedback_data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
):
    """Save query results with optional feedback data to a JSON file."""
    import json

    results = {"query": query, "total_matches": len(matches), "matches": matches, "timestamp": time.time()}

    if feedback_data:
        results["feedback"] = feedback_data

    if metadata:
        results["metadata"] = metadata

    with open(output_file, "w") as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\nResults saved to {output_file}")
    if feedback_data and feedback_data.get("summary"):
        summary = feedback_data["summary"]
        print(
            f"Feedback: {summary['correct']} correct, {summary['incorrect']} incorrect, {summary['unrated']} unrated"
        )


@traced
def print_match_summary(matches: List[Dict[str, Any]], query: str, filter_type: str = ""):
    """Print a summary of matches with appropriate formatting for different filter types."""
    print(f"\n{'=' * 80}")
    print(f"{filter_type.upper()} RESULTS" if filter_type else "SEARCH RESULTS")
    print(f"{'=' * 80}")
    print(f'Query: "{query}"')
    print(f"Total matches found: {len(matches)}")

    for i, match in enumerate(matches, 1):
        print(f"\n--- Match {i} ---")
        print(f"Log ID: {match['log_id']}")

        # Show reasoning for LLM-based matches
        if match.get("reasoning"):
            print(f"Reasoning: {match['reasoning']}")

        # Show similarity score for embedding-based matches
        if match.get("similarity_score"):
            print(f"Similarity Score: {match['similarity_score']:.3f}")

        print(f"Content preview: {match['content_preview']}")

        if match.get("braintrust_link"):
            print(f"Braintrust URL: {match['braintrust_link']}")
