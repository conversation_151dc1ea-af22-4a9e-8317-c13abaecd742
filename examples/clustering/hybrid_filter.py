#!/usr/bin/env python3
"""
Hybrid log filtering combining fast embedding pre-filtering with precise LLM analysis.

Runs embedding filter first as a fast screening pass, then LLM filter on the reduced set.
This gives you speed + precision: the embedding filter removes obviously irrelevant logs,
then the LLM does detailed analysis on a much smaller, more relevant dataset.

Example usage:
python hybrid_filter.py --api-key <API_KEY> --project-name "My Project" --query "user is upset with assistant" --limit 1000

Compare filtering approaches:
python hybrid_filter.py --api-key <KEY> --project-name "Project" --query "user upset with assistant" --limit 1000 --embedding-candidates 200
python hybrid_filter.py --api-key <KEY> --project-name "Project" --query "user upset with assistant" --limit 1000 --use-clustering-sampler --num-clusters 50
python hybrid_filter.py --api-key <KEY> --project-name "Project" --query "user upset with assistant" --limit 1000 --skip-embedding-filter
"""

import argparse
import os
import random
import time
from typing import Any, Dict, List, Optional

# Set tokenizers parallelism to avoid warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

import openai
from braintrust import (
    init_logger,
    login,
    login_to_state,
    start_span,
    traced,
)
from embed_logs import (
    fetch_logs_via_btql,
)

# Import components from both filtering scripts
from embedding_filter import (
    compute_query_cache_key,
    embed_documents_with_cache,
    zero_shot_filter,
)
from interactive_common import (
    interactive_review,
    print_match_summary,
    save_results_with_feedback,
)
from query_logs import (
    convert_and_deduplicate_log_content,
    get_project_info,
    query_logs_paginated,
)


def parse_args():
    parser = argparse.ArgumentParser(description="Hybrid log filtering: embedding pre-filter + LLM analysis")
    parser.add_argument("--api-key", required=True, help="Braintrust API key")
    parser.add_argument(
        "--app-url",
        default="https://www.braintrust.dev",
        help="Braintrust app URL (default: https://www.braintrust.dev)",
    )
    parser.add_argument("--project-name", required=True, help="Name of the project to search logs from")
    parser.add_argument("--query", required=True, help="Natural language query to search for in logs")
    parser.add_argument("--limit", type=int, default=1000, help="Maximum number of logs to scan (default: 1000)")
    parser.add_argument(
        "--max-matches", type=int, default=10, help="Maximum number of final matching logs to return (default: 10)"
    )
    parser.add_argument("--page-size", type=int, default=1000, help="Number of logs to fetch per page (default: 1000)")
    parser.add_argument("--filter", help="Optional BTQL filter expression to pre-filter logs")
    parser.add_argument(
        "--openai-api-key", help="OpenAI API key for embeddings and LLM (defaults to OPENAI_API_KEY env var)"
    )

    # Embedding filter options
    parser.add_argument(
        "--skip-embedding-filter",
        action="store_true",
        help="Skip embedding pre-filter and run LLM filter on all logs (for comparison)",
    )
    parser.add_argument(
        "--embedding-model",
        default="text-embedding-3-small",
        help="OpenAI embedding model (default: text-embedding-3-small)",
    )
    parser.add_argument(
        "--use-huggingface",
        action="store_true",
        help="Use Hugging Face transformers instead of OpenAI for embeddings",
    )
    parser.add_argument(
        "--hf-model",
        default="sentence-transformers/all-MiniLM-L6-v2",
        help="Hugging Face model for embeddings (default: sentence-transformers/all-MiniLM-L6-v2)",
    )
    parser.add_argument(
        "--quantize",
        action="store_true",
        help="Use quantization for Hugging Face models",
    )
    parser.add_argument(
        "--use-onnx",
        action="store_true",
        help="Use ONNX Runtime for faster CPU inference",
    )
    parser.add_argument(
        "--onnx-model-path",
        type=str,
        help="Path to ONNX model directory",
    )
    parser.add_argument(
        "--embedding-candidates",
        type=int,
        default=300,
        help="Number of top candidates to keep after embedding filter (default: 300)",
    )
    parser.add_argument(
        "--embedding-top-pct",
        type=float,
        help="Alternative: Percentage of logs to keep after embedding filter (overrides --embedding-candidates if specified)",
    )
    parser.add_argument(
        "--embedding-prefix",
        type=str,
        help="Phrase to prepend to each document before embedding",
    )
    parser.add_argument(
        "--use-confirmer",
        action="store_true",
        help="Use heuristic confirmers in embedding filter",
    )
    parser.add_argument(
        "--use-clustering-sampler",
        action="store_true",
        help="Use clustering-based sampling instead of similarity-based filtering",
    )
    parser.add_argument(
        "--num-clusters",
        type=int,
        default=50,
        help="Number of clusters for clustering-based sampling (default: 50)",
    )
    parser.add_argument(
        "--random-cluster-sampling",
        action="store_true",
        help="Use random sampling within clusters (default: similarity-ranked sampling)",
    )

    # LLM filter options
    parser.add_argument("--llm-model", default="gpt-5-nano", help="OpenAI model for LLM analysis")
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=10,
        help="Maximum concurrent LLM calls (default: 10)",
    )

    # General options
    parser.add_argument(
        "--cache-dir",
        default=".cache",
        help="Directory to cache embeddings and query results (default: .cache)",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable caching",
    )
    parser.add_argument(
        "--output",
        default="hybrid_filter_results.json",
        help="Output file for results (default: hybrid_filter_results.json)",
    )
    parser.add_argument("--verbose", action="store_true", help="Show detailed progress")
    parser.add_argument(
        "--no-deduplicate",
        action="store_true",
        help="Disable content deduplication",
    )
    parser.add_argument(
        "--min-substring-length",
        type=int,
        default=20,
        help="Minimum length for common substrings to remove (default: 20)",
    )
    parser.add_argument(
        "--hf-batch-size",
        type=int,
        default=256,
        help="Batch size for Hugging Face embedding generation (default: 256)",
    )
    parser.add_argument(
        "--max-embedding-concurrent",
        type=int,
        default=20,
        help="Maximum concurrent embedding calls (default: 20)",
    )
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Enable interactive mode to review and rate matches",
    )
    return parser.parse_args()


@traced(notrace_io=True)
def run_embedding_prefilter(
    query: str,
    logs: List[Dict[str, Any]],
    deduplicated_contents: List[str],
    embed_func,
    top_k: Optional[int] = None,
    top_pct: Optional[float] = None,
    use_confirmer: bool = False,
    org_name: str = "unknown",
    project_name: str = "unknown",
    app_url: str = "https://www.braintrust.dev",
    verbose: bool = False,
) -> tuple:
    """
    Run embedding-based pre-filtering to reduce the dataset.

    Returns:
        - filtered_logs: Reduced list of log dictionaries
        - kept_indices: Original indices of kept logs
        - filter_time: Time taken for filtering
    """
    print(f"\n🔍 EMBEDDING PRE-FILTER")
    print(f"{'=' * 50}")
    filter_desc = f"top {top_k} candidates" if top_k else f"top {top_pct:.1%}"
    print(f"Filtering {len(logs)} logs to {filter_desc} by semantic similarity...")

    start_time = time.time()

    # Show embedding progress
    print(f"  Generating embeddings for {len(deduplicated_contents)} documents...")

    # Run zero-shot embedding filter
    matches = zero_shot_filter(
        query=query,
        texts=deduplicated_contents,
        logs=logs,
        embed_func=embed_func,
        top_k=top_k,
        top_pct=top_pct,
        use_confirmer=use_confirmer,
        verbose=False,  # Reduce verbosity to avoid spam
        org_name=org_name,
        project_name=project_name,
        app_url=app_url,
    )

    filter_time = time.time() - start_time

    # Extract the filtered logs and their original indices
    kept_indices = []
    filtered_logs = []

    for match in matches:
        # Find original index of this log
        log_id = match["log_id"]
        for i, original_log in enumerate(logs):
            if original_log.get("id") == log_id:
                kept_indices.append(i)
                filtered_logs.append(original_log)
                break

    kept_count = len(filtered_logs)
    kept_pct = (kept_count / len(logs)) * 100 if logs else 0

    embeddings_per_sec = len(logs) / filter_time if filter_time > 0 else 0
    print(f"✓ Embedding filter completed in {filter_time:.2f}s ({embeddings_per_sec:.1f} embeddings/sec)")
    print(f"✓ Kept {kept_count}/{len(logs)} logs ({kept_pct:.1f}%)")
    print(f"✓ Reduced dataset by {100 - kept_pct:.1f}%")

    return filtered_logs, kept_indices, filter_time


@traced(notrace_io=True)
def run_embedding_clustering_sampler(
    query: str,
    logs: List[Dict[str, Any]],
    deduplicated_contents: List[str],
    embed_func,
    target_samples: int,
    num_clusters: int = 50,
    verbose: bool = False,
    random_sampling: bool = False,
) -> tuple:
    """
    Run embedding-based clustering to sample a diverse subset of logs.

    Args:
        logs: List of log dictionaries
        deduplicated_contents: List of deduplicated text content
        embed_func: Function to generate embeddings
        target_samples: Target number of samples to return
        num_clusters: Number of clusters to create (default: 50)
        verbose: Whether to show detailed progress

    Returns:
        - sampled_logs: Sampled list of log dictionaries
        - kept_indices: Original indices of sampled logs
        - filter_time: Time taken for clustering and sampling
    """
    print(f"\n🎯 EMBEDDING CLUSTERING SAMPLER")
    print(f"{'=' * 50}")
    print(f"Clustering {len(logs)} logs into {num_clusters} groups...")
    print(f"Sampling {target_samples} logs ({target_samples/len(logs)*100:.1f}%) for diverse coverage...")

    start_time = time.time()

    # Generate embeddings for all documents
    print(f"  Generating embeddings for {len(deduplicated_contents)} documents...")
    embeddings_array = embed_func(deduplicated_contents)

    # Import clustering utilities
    try:
        import random

        import numpy as np
        from sklearn.cluster import KMeans

        random.seed(42)  # For reproducible sampling

        # Convert to numpy array if needed
        if not isinstance(embeddings_array, np.ndarray):
            embeddings_array = np.array(embeddings_array)

        # Determine optimal number of clusters (don't exceed number of samples)
        effective_clusters = min(num_clusters, len(logs), target_samples)
        samples_per_cluster = max(1, target_samples // effective_clusters)

        print(f"  Using {effective_clusters} clusters with ~{samples_per_cluster} samples per cluster...")

        # Perform K-means clustering
        kmeans = KMeans(n_clusters=effective_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(embeddings_array)

        # Compute query similarity scores for ranking within clusters
        print(f"  Computing query similarity scores for ranking...")

        # Import the zero-shot filter components to compute query similarity
        try:
            from embedding_filter import make_contrast_prototype, scores_against_prototype

            # Create query prototype for similarity scoring
            query_prototype = make_contrast_prototype(query, embed_func)
            similarity_scores = scores_against_prototype(embeddings_array, query_prototype)

        except ImportError:
            # Fallback: use simple cosine similarity with query embedding
            query_embedding = embed_func([query])[0]
            query_norm = np.linalg.norm(query_embedding)

            # Compute cosine similarities
            similarity_scores = np.array(
                [np.dot(emb, query_embedding) / (np.linalg.norm(emb) * query_norm + 1e-12) for emb in embeddings_array]
            )

        # Sample from each cluster, prioritizing by similarity score
        sampled_indices = []
        cluster_stats = []

        for cluster_id in range(effective_clusters):
            # Get indices of logs in this cluster
            cluster_indices = [i for i, label in enumerate(cluster_labels) if label == cluster_id]
            cluster_size = len(cluster_indices)

            if cluster_size == 0:
                continue

            # Calculate how many samples to take from this cluster
            # Distribute remaining samples if we're in the last clusters
            remaining_clusters = effective_clusters - cluster_id
            remaining_samples = target_samples - len(sampled_indices)
            cluster_samples = min(
                cluster_size,
                max(1, remaining_samples // remaining_clusters) if remaining_clusters > 0 else remaining_samples,
            )

            # Sample from cluster (either by similarity ranking or randomly)
            if cluster_samples >= cluster_size:
                selected = cluster_indices  # Take all if we need more than available
            elif random_sampling:
                # Random sampling within cluster
                selected = random.sample(cluster_indices, cluster_samples)
            else:
                # Rank by similarity score within this cluster and take top samples
                cluster_scores = [(idx, similarity_scores[idx]) for idx in cluster_indices]
                cluster_scores.sort(key=lambda x: x[1], reverse=True)
                selected = [idx for idx, score in cluster_scores[:cluster_samples]]

            sampled_indices.extend(selected)
            cluster_stats.append((cluster_id, cluster_size, len(selected)))

            if verbose:
                avg_score = np.mean([similarity_scores[idx] for idx in selected])
                print(
                    f"    Cluster {cluster_id}: {len(selected)}/{cluster_size} logs sampled (avg similarity: {avg_score:.3f})"
                )

        # Ensure we don't exceed target (in case of rounding)
        if len(sampled_indices) > target_samples:
            sampled_indices = random.sample(sampled_indices, target_samples)

        # Sort all sampled indices by similarity score (highest first)
        sampled_scores = [(idx, similarity_scores[idx]) for idx in sampled_indices]
        sampled_scores.sort(key=lambda x: x[1], reverse=True)
        sampled_indices = [idx for idx, score in sampled_scores]

        # Extract sampled logs (now in similarity order)
        sampled_logs = [logs[i] for i in sampled_indices]

        filter_time = time.time() - start_time

        # Print statistics
        kept_count = len(sampled_logs)
        kept_pct = (kept_count / len(logs)) * 100 if logs else 0

        # Show similarity score statistics
        final_scores = [similarity_scores[idx] for idx in sampled_indices]
        score_range = f"{min(final_scores):.3f} to {max(final_scores):.3f}"
        avg_score = np.mean(final_scores)

        print(f"✓ Clustering completed in {filter_time:.2f}s")
        print(f"✓ Sampled {kept_count}/{len(logs)} logs ({kept_pct:.1f}%)")
        print(f"✓ Reduced dataset by {100 - kept_pct:.1f}% while maintaining diversity")
        print(f"✓ Similarity scores range: {score_range} (avg: {avg_score:.3f})")
        print(f"✓ Results sorted by relevance (most similar to query first)")

        if verbose:
            print(f"  Cluster distribution:")
            for cluster_id, total, sampled in cluster_stats[:10]:  # Show first 10
                print(f"    Cluster {cluster_id}: {sampled}/{total} logs")
            if len(cluster_stats) > 10:
                print(f"    ... and {len(cluster_stats) - 10} more clusters")

        return sampled_logs, sampled_indices, filter_time

    except ImportError as e:
        print(f"❌ Error: Missing required packages for clustering: {e}")
        print("   Please install: pip install scikit-learn numpy")
        print("   Falling back to random sampling...")

        # Fallback to simple random sampling
        target_samples = min(target_samples, len(logs))
        sampled_indices = random.sample(range(len(logs)), target_samples)
        sampled_logs = [logs[i] for i in sampled_indices]
        filter_time = time.time() - start_time

        print(f"✓ Random sampling completed: {len(sampled_logs)}/{len(logs)} logs selected")
        return sampled_logs, sampled_indices, filter_time


@traced(notrace_io=True)
def run_llm_analysis(
    query: str,
    logs: List[Dict[str, Any]],
    deduplicated_contents: List[str],
    max_matches: int,
    model: str,
    max_concurrent: int,
    org_name: str,
    project_name: str,
    app_url: str,
    verbose: bool = False,
    feedback_examples: Optional[List[Dict[str, Any]]] = None,
) -> tuple:
    """
    Run LLM-based analysis on the (potentially pre-filtered) logs.

    Returns:
        - matches: Final list of matching log dictionaries
        - llm_time: Time taken for LLM analysis
    """
    print(f"\n🤖 LLM ANALYSIS")
    print(f"{'=' * 50}")
    print(f"Running detailed LLM analysis on {len(logs)} logs...")
    print(f"Looking for top {max_matches} matches...")

    start_time = time.time()

    # We'll reuse the parallel analysis logic from query_logs_paginated
    # but adapt it to work on our pre-filtered dataset
    from concurrent.futures import as_completed

    from braintrust import TracedThreadPoolExecutor, start_span
    from query_logs import analyze_log_relevance

    matches = []
    docs_processed_to_find_matches = 0

    with TracedThreadPoolExecutor(max_workers=max_concurrent) as executor:
        # Submit all analysis tasks
        future_to_log = {}
        for i, log in enumerate(logs):
            future = executor.submit(
                analyze_log_relevance,
                log,
                deduplicated_contents[i],
                query,
                model,
                verbose,
                org_name,
                project_name,
                app_url,
                feedback_examples,
            )
            future_to_log[future] = log

        # Process completed futures
        completed = 0
        early_stop = False

        for future in as_completed(future_to_log):
            if early_stop:
                # Cancel remaining futures if we have enough matches
                future.cancel()
                continue

            log = future_to_log[future]
            try:
                analysis = future.result()
                docs_processed_to_find_matches += 1  # Count each document processed

                if analysis:
                    matches.append(analysis)
                    if verbose:
                        print(f"  ✓ Match found: {analysis['content_preview'][:80]}...")

                    print(
                        f"📊 BENCHMARK: Found {len(matches)} matches after processing {docs_processed_to_find_matches} documents"
                    )

                    # Stop early if we have enough matches
                    if len(matches) >= max_matches:
                        print(f"  Reached maximum matches ({max_matches}), stopping analysis")
                        print(
                            f"🎯 BENCHMARK RESULT: Found {max_matches} matches by analyzing {docs_processed_to_find_matches}/{len(logs)} documents ({docs_processed_to_find_matches/len(logs)*100:.1f}%)"
                        )
                        early_stop = True

                        # Cancel remaining futures to save compute
                        for remaining_future in future_to_log:
                            if remaining_future != future and not remaining_future.done():
                                remaining_future.cancel()
                        continue

            except Exception as e:
                if verbose:
                    print(f"  Error analyzing log {log.get('id', 'unknown')}: {e}")

            completed += 1
            if verbose and (completed % 10 == 0 or completed >= len(logs) - 5):
                print(f"  Analyzed {completed}/{len(logs)} logs...")

    # Sort by reasoning quality (you could also sort by confidence if available)
    matches = matches[:max_matches]  # Ensure we don't exceed max_matches

    llm_time = time.time() - start_time

    print(f"✓ LLM analysis completed in {llm_time:.2f}s")
    print(f"✓ Found {len(matches)} relevant matches")

    # Final benchmark summary if we didn't hit the early stop
    if len(matches) < max_matches:
        print(
            f"🎯 BENCHMARK RESULT: Found {len(matches)} matches by analyzing ALL {docs_processed_to_find_matches}/{len(logs)} documents (100%)"
        )

    return matches, llm_time, docs_processed_to_find_matches


@traced
def main():
    args = parse_args()

    # Set OpenAI API key
    openai_api_key = args.openai_api_key or os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("OpenAI API key required (--openai-api-key or OPENAI_API_KEY env var)")

    openai.api_key = openai_api_key

    # Login to Braintrust
    print(f"Connecting to Braintrust at {args.app_url}...")
    query_state = login_to_state(api_key=args.api_key, app_url=args.app_url)

    # Get project ID and org name
    try:
        project_id, org_name = get_project_info(query_state, args.project_name)
        print(f"Found project: {project_id} (org: {org_name})")
    except ValueError as e:
        print(f"Error: {e}")
        return

    # Initialize Braintrust logger
    login(force_login=True)
    logger = init_logger("taxonomy discovery")

    total_start_time = time.time()

    with start_span("hybrid_log_filtering"):
        # Generate cache key for query results
        query_cache_key = compute_query_cache_key(
            api_key=args.api_key,
            project_name=args.project_name,
            limit=args.limit,
            filter_expr=args.filter,
            deduplicate=not args.no_deduplicate,
            page_size=args.page_size,
        )

        use_cache = not args.no_cache

        # Fetch logs
        print(f"📥 FETCHING LOGS")
        print(f"{'=' * 50}")
        print(f"Fetching up to {args.limit} logs from project...")

        fetch_start = time.time()
        try:
            all_logs = fetch_logs_via_btql(
                project_id,
                args.limit,
                args.filter,
                cache_key=query_cache_key if use_cache else None,
                cache_dir=args.cache_dir if use_cache else None,
                use_cache=use_cache,
                use_pagination=args.limit > args.page_size,
                page_size=args.page_size,
                deduplicate=not args.no_deduplicate,
                state=query_state,
            )
        except Exception as e:
            print(f"Error fetching logs: {e}")
            return

        if not all_logs:
            print("No logs found in project")
            return

        fetch_time = time.time() - fetch_start
        print(f"✓ Retrieved {len(all_logs)} logs in {fetch_time:.2f}s")

        # Deduplicate content
        print(f"\n🔧 PREPROCESSING")
        print(f"{'=' * 50}")
        print("Deduplicating log content...")

        dedup_start = time.time()
        deduplicated_contents = convert_and_deduplicate_log_content(
            all_logs,
            min_substring_length=args.min_substring_length,
            cache_dir=args.cache_dir,
            use_cache=use_cache,
        )
        dedup_time = time.time() - dedup_start

        # Filter out empty documents
        valid_indices = [i for i, content in enumerate(deduplicated_contents) if content.strip()]
        if len(valid_indices) < len(deduplicated_contents):
            print(f"Filtered out {len(deduplicated_contents) - len(valid_indices)} empty documents")
            all_logs = [all_logs[i] for i in valid_indices]
            deduplicated_contents = [deduplicated_contents[i] for i in valid_indices]

        if len(all_logs) == 0:
            print("No valid documents remaining after deduplication")
            return

        print(f"✓ Preprocessing completed in {dedup_time:.2f}s")

        # Decide which logs to analyze with LLM
        logs_for_llm = all_logs
        contents_for_llm = deduplicated_contents
        embedding_time = 0.0

        # Initialize variables for metadata tracking
        top_k = None
        target_samples = None

        if not args.skip_embedding_filter:
            # Create embedding function
            embedding_model = args.hf_model if args.use_huggingface else args.embedding_model

            def embed_func(texts):
                return embed_documents_with_cache(
                    texts=texts,
                    model=embedding_model,
                    use_huggingface=args.use_huggingface,
                    use_onnx=args.use_onnx,
                    embedding_prefix=args.embedding_prefix,
                    quantize=args.quantize,
                    hf_batch_size=args.hf_batch_size,
                    onnx_model_path=args.onnx_model_path,
                    cache_dir=args.cache_dir,
                    use_cache=use_cache,  # Use same cache setting as other operations
                    verbose=args.verbose,
                )

            # Choose between clustering sampler or similarity-based pre-filter
            if args.use_clustering_sampler:
                # Calculate target samples - use embedding_candidates as target
                if args.embedding_top_pct:
                    target_samples = int(len(all_logs) * args.embedding_top_pct)
                else:
                    target_samples = min(args.embedding_candidates, len(all_logs))

                logs_for_llm, kept_indices, embedding_time = run_embedding_clustering_sampler(
                    query=args.query,
                    logs=all_logs,
                    deduplicated_contents=deduplicated_contents,
                    embed_func=embed_func,
                    target_samples=target_samples,
                    num_clusters=args.num_clusters,
                    verbose=args.verbose,
                    random_sampling=args.random_cluster_sampling,
                )
            else:
                # Original similarity-based pre-filter
                if args.embedding_top_pct:
                    top_k, top_pct = None, args.embedding_top_pct
                else:
                    top_k, top_pct = min(args.embedding_candidates, len(all_logs)), None

                logs_for_llm, kept_indices, embedding_time = run_embedding_prefilter(
                    query=args.query,
                    logs=all_logs,
                    deduplicated_contents=deduplicated_contents,
                    embed_func=embed_func,
                    top_k=top_k,
                    top_pct=top_pct,
                    use_confirmer=args.use_confirmer,
                    org_name=org_name,
                    project_name=args.project_name,
                    app_url=args.app_url,
                    verbose=args.verbose,
                )

            # Get corresponding deduplicated contents
            contents_for_llm = [deduplicated_contents[i] for i in kept_indices]
        else:
            print(f"\n⚡ SKIPPING EMBEDDING FILTER")
            print(f"{'=' * 50}")
            print(f"Running LLM analysis on all {len(all_logs)} logs (no pre-filtering)")

        # Run LLM analysis
        print(f"\n🤖 LLM ANALYSIS PHASE")
        print(f"{'=' * 50}")
        if not args.skip_embedding_filter:
            reduction_factor = len(all_logs) / len(logs_for_llm) if len(logs_for_llm) > 0 else 1
            print(
                f"📊 BENCHMARK: Pre-filtering reduced dataset from {len(all_logs)} to {len(logs_for_llm)} logs ({reduction_factor:.1f}x reduction)"
            )
        else:
            print(f"📊 BENCHMARK: No pre-filtering - analyzing all {len(logs_for_llm)} logs")

        final_matches, llm_time, docs_processed = run_llm_analysis(
            query=args.query,
            logs=logs_for_llm,
            deduplicated_contents=contents_for_llm,
            max_matches=args.max_matches,
            model=args.llm_model,
            max_concurrent=args.max_concurrent,
            org_name=org_name,
            project_name=args.project_name,
            app_url=args.app_url,
            verbose=args.verbose,
            feedback_examples=None,
        )

        # Print final summary
        total_time = time.time() - total_start_time

        print(f"\n📊 PERFORMANCE SUMMARY")
        print(f"{'=' * 50}")
        print(f"Total logs fetched: {len(all_logs)}")
        print(f"Fetch time: {fetch_time:.2f}s")
        print(f"Preprocessing time: {dedup_time:.2f}s")
        if not args.skip_embedding_filter:
            print(f"Embedding filter time: {embedding_time:.2f}s")
            print(
                f"Logs after embedding filter: {len(logs_for_llm)} ({(len(logs_for_llm) / len(all_logs) * 100):.1f}%)"
            )
        print(f"LLM analysis time: {llm_time:.2f}s")
        print(f"Final matches found: {len(final_matches)}")
        print(f"Total pipeline time: {total_time:.2f}s")

        # Benchmark: Document analysis efficiency
        print(f"\n🎯 DOCUMENT ANALYSIS EFFICIENCY")
        print(f"{'=' * 50}")
        docs_processed_pct = (docs_processed / len(logs_for_llm) * 100) if len(logs_for_llm) > 0 else 0
        print(
            f"Documents processed to find {len(final_matches)} matches: {docs_processed}/{len(logs_for_llm)} ({docs_processed_pct:.1f}%)"
        )

        if not args.skip_embedding_filter and len(all_logs) > 0:
            # Calculate total documents that would have been processed without embedding filter
            total_docs_without_filter = len(all_logs)
            efficiency_gain = (
                ((total_docs_without_filter - docs_processed) / total_docs_without_filter * 100)
                if total_docs_without_filter > 0
                else 0
            )
            print(
                f"Embedding pre-filter saved: {total_docs_without_filter - docs_processed}/{total_docs_without_filter} documents ({efficiency_gain:.1f}% reduction in LLM calls)"
            )

            speedup_estimate = (len(all_logs) / len(logs_for_llm)) if len(logs_for_llm) > 0 else 1
            print(
                f"Estimated LLM speedup: {speedup_estimate:.1f}x (analyzed {len(logs_for_llm)} instead of {len(all_logs)} logs)"
            )
        else:
            print(f"Without embedding pre-filter: analyzed {docs_processed}/{len(all_logs)} documents")
            print(f"Note: Run with embedding pre-filter to reduce LLM analysis workload")

        # Print results
        print_match_summary(final_matches, args.query, "HYBRID FILTER")

        # Interactive review if requested
        feedback_data = None
        if args.interactive and final_matches:

            def rerun_with_feedback(feedback_examples=None, **kwargs):
                """Re-run with feedback examples - for now just re-run LLM part."""
                # Could be enhanced to use feedback in both embedding and LLM stages
                result = run_llm_analysis(
                    query=args.query,
                    logs=logs_for_llm,
                    deduplicated_contents=contents_for_llm,
                    max_matches=args.max_matches,
                    model=args.llm_model,
                    max_concurrent=args.max_concurrent,
                    org_name=org_name,
                    project_name=args.project_name,
                    app_url=args.app_url,
                    verbose=args.verbose,
                    feedback_examples=feedback_examples,
                )
                return result[0]  # Just return matches, not timing or docs_processed

            feedback_data = interactive_review(
                matches=final_matches,
                query=args.query,
                rerun_func=rerun_with_feedback,
            )

        # Save results
        metadata = {
            "project_name": args.project_name,
            "total_logs_fetched": len(all_logs),
            "embedding_filter_enabled": not args.skip_embedding_filter,
            "embedding_candidates_requested": (
                target_samples if args.use_clustering_sampler else top_k if not args.skip_embedding_filter else None
            ),
            "logs_after_embedding_filter": len(logs_for_llm) if not args.skip_embedding_filter else len(all_logs),
            "embedding_reduction_pct": (
                (1 - len(logs_for_llm) / len(all_logs)) * 100
                if not args.skip_embedding_filter and len(all_logs) > 0
                else 0
            ),
            "fetch_time": fetch_time,
            "preprocessing_time": dedup_time,
            "embedding_time": embedding_time,
            "llm_time": llm_time,
            "total_time": total_time,
            "embedding_model": embedding_model if not args.skip_embedding_filter else None,
            "llm_model": args.llm_model,
        }

        save_results_with_feedback(
            matches=final_matches,
            query=args.query,
            output_file=args.output,
            feedback_data=feedback_data,
            metadata=metadata,
        )


if __name__ == "__main__":
    main()
