import argparse
import json
import os
import sys
from urllib.parse import urlparse

import boto3
import braintrust
import duckdb
import pyarrow as pa
import requests
from pyarrow import parquet as pq

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
CACHE_DIR = os.path.join(SCRIPT_DIR, "cache")

s3 = boto3.client("s3")


def download_file_id(id):
    key = f"anon/{id}.parquet"
    path = os.path.join(CACHE_DIR, id + ".parquet")

    if not os.path.exists(path):
        s3.download_file("braintrust-data-staging", key, path)

    return path


PROJECTS = {
    "zapier-zap-guesser": {
        "experiments": [
            "48732751-c6de-4cd2-a540-18accab523f0",
            "9a1e1d90-73d0-41d8-85ac-77a13640131b",
        ]
    }
}


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Load sample data.")
    args = parser.parse_args()

    braintrust.login()

    os.makedirs(CACHE_DIR, exist_ok=True)
    experiments = []
    datasets = []
    for project_name, project_spec in PROJECTS.items():
        for id in project_spec.get("experiments", []):
            experiments.append({"project_name": project_name, "id": id, "data": download_file_id(id)})

        for id in project_spec.get("datasets", {}):
            datasets.append({"project_name": project_name, "id": id, "data": download_file_id(id)})

    conn = duckdb.connect(":memory:")
    for experiment in experiments:
        exp = braintrust.init(project=f'repro-{experiment["project_name"]}', experiment=f"repro-{experiment['id']}")
        data = conn.query(f"SELECT t::JSON FROM parquet_scan('{experiment['data']}') t").fetchall()
        for row in data:
            kwargs = json.loads(row[0])
            exp.log(**kwargs, allow_large=True)
        print(exp.summarize())
