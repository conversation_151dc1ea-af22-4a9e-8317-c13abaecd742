from typing import Literal

import braintrust
from pydantic import BaseModel


class CalculatorInput(BaseModel):
    op: Literal["add", "subtract", "multiply", "divide"]
    a: int
    b: int


def calculator(op, a, b):
    print(f"Calculator called with {op}, {a}, {b}")
    if op == "add":
        return a + b
    elif op == "subtract":
        return a - b
    elif op == "multiply":
        return a * b
    elif op == "divide":
        return a / b


project = braintrust.projects.create(name="Tool Test")

tool = project.tools.create(
    name="Calculator",
    description="A simple calculator that can add, subtract, multiply, and divide.",
    handler=calculator,
    parameters=CalculatorInput,
    if_exists="replace",
)
