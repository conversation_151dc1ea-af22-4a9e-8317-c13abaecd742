import asyncio
import os

import openai

TASKS = 25

client = openai.AsyncClient(
    base_url="http://127.0.0.1:8787/v1",
    default_headers={"x-bt-use-cache": "never"},
    api_key=os.environ["BRAINTRUST_API_KEY"],
)


async def run_request():
    return await client.chat.completions.create(
        model="mistralai/Mixtral-8x7B-Instruct-v0.1",
        # model="mistral-small",
        messages=[{"role": "user", "content": "What is the capital of France?"}],
    )


async def main():
    tasks = [asyncio.create_task(run_request()) for i in range(TASKS)]
    return [await t for t in tasks]


if __name__ == "__main__":
    print(asyncio.run(main()))
