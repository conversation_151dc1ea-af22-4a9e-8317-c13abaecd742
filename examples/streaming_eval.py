import json
import os
import sys
import time
import uuid

import requests


def humanize_bytes(bytes):
    """Convert bytes to human readable string."""
    for unit in ["B", "KB", "MB", "GB"]:
        if bytes < 1024:
            return f"{bytes:.1f}{unit}"
        bytes /= 1024
    return f"{bytes:.1f}TB"


def monitor_stream():
    outfile = open("output.txt", "wb")
    url = "https://staging-api.braintrust.dev/function/eval"
    api_key = os.environ["BRAINTRUST_API_KEY"]
    headers = {
        "accept": "*/*",
        "authorization": f"Bearer {api_key}",
        "content-type": "application/json",
        "x-bt-org-name": "braintrustdata.com",
    }

    # payload = {
    #     "experiment_name": "Prompt A (gpt-4o)",
    #     "metadata": {"model": "gpt-4o", "use_cache": True, "temperature": 0, "max_tokens": 1024},
    #     "project_id": "",
    #     "data": {"dataset_id": "90a96b17-7217-48da-a4e2-a21bf7836c0c"},
    #     "task": {
    #         "name": "Prompt A (gpt-4o)",
    #         "prompt_session_id": "655fcb40-80c4-46d3-8654-336c81f1f7ab",
    #         "prompt_session_function_id": "2d346dd5-5f80-4831-9b1b-aa1a083d656f",
    #         "version": "1000194575439327937",
    #         "metadata": {"model": "gpt-4o", "use_cache": True, "temperature": 0, "max_tokens": 1024},
    #     },
    #     "scores": [],
    #     "stream": True,
    #     "trial_count": 1,
    #     "parent": {
    #         "object_type": "playground_logs",
    #         "object_id": "655fcb40-80c4-46d3-8654-336c81f1f7ab",
    #         "propagated_event": {"span_attributes": {"generation": "7a63c06c-cb6c-4e52-8c86-7479bf35a67b"}},
    #     },
    # }

    unique_name = uuid.uuid4().hex
    payload = {
        "experiment_name": f"Prompt A (gpt-4o) {unique_name}",
        "metadata": {"model": "gpt-4o", "temperature": 0, "max_tokens": 1024},
        "project_id": "",
        "data": {"dataset_id": "90fc33be-7fb6-4a52-8419-89d99b14d354"},
        "task": {
            "name": "Prompt A (gpt-4o)",
            "prompt_session_id": "b3bbb5ea-0f76-4945-8674-88d670d7ce98",
            "prompt_session_function_id": "891f80a1-e0aa-4c4c-8772-b8ce27a1a65d",
            "version": "1000194574951157631",
            "metadata": {"model": "gpt-4o", "use_cache": True, "temperature": 0, "max_tokens": 1024},
        },
        "scores": [],
        "stream": True,
        "trial_count": 1,
        # "max_concurrency": 1,
        "parent": {
            "object_type": "playground_logs",
            "object_id": "b3bbb5ea-0f76-4945-8674-88d670d7ce98",
            "propagated_event": {"span_attributes": {"generation": "c50555bb-d65e-4da7-9cbb-d95639ec3270"}},
        },
    }

    start_time = time.time()
    fifty_kb_chunks = 0
    total_bytes = 0
    bytes_since_last_pause = 0
    last_chunk_time = time.time()
    PAUSE_THRESHOLD = 0.3  # Consider a pause if no data for 1 second

    with requests.post(url, headers=headers, json=payload, stream=True) as response:
        for chunk in response.iter_content():
            outfile.write(chunk)
            current_time = time.time()

            if total_bytes > fifty_kb_chunks * 50 * 1024:
                fifty_kb_chunks += 1
                diff_time = current_time - start_time
                print(f"[{diff_time:.2f}s] Total bytes: {humanize_bytes(total_bytes)}")

            # Check if there was a pause before processing new chunk
            time_since_last_chunk = current_time - last_chunk_time
            if time_since_last_chunk > PAUSE_THRESHOLD:
                print("-" * 50)
                print(f"Pause detected! Duration: {time_since_last_chunk:.2f} seconds")
                print(f"Bytes received since last pause: {humanize_bytes(bytes_since_last_pause)}")
                print(f"Total bytes so far: {humanize_bytes(total_bytes)}")
                print("-" * 50)
                bytes_since_last_pause = 0

            if chunk:
                chunk_size = len(chunk)
                total_bytes += chunk_size
                bytes_since_last_pause += chunk_size
                last_chunk_time = current_time

        print("\nStream completed!")
        print("Bytes received since last pause:", humanize_bytes(bytes_since_last_pause))
        print(f"Total bytes received: {humanize_bytes(total_bytes)}")


if __name__ == "__main__":
    monitor_stream()
