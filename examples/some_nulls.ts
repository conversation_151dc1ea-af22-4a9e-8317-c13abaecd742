import * as argparse from "argparse";
import { init } from "braintrust";

async function main({ num }: { num: number }) {
  const experiment = init("some-nulls");
  for (let i = 0; i < num; i++) {
    experiment.log({ input: "0", output: "1", scores: { foo: Math.random() } });
    experiment.log({ input: null, output: "1", scores: {} });
  }
  await experiment.flush();
  console.log(await experiment.summarize());
}

const parser = new argparse.ArgumentParser();
parser.add_argument("--num", { default: 10, type: Number });

const args = parser.parse_args();

main({ num: args.num });
