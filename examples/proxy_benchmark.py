import os
import statistics
import time

import requests

# curl https://api.openai.com/v1/chat/completions \
#   -H "Content-Type: application/json" \
#   -H "Authorization: Bearer $OPENAI_API_KEY" \
#   -d '{
#     "model": "gpt-4o",
#     "messages": [
#       {
#         "role": "system",
#         "content": "You are a helpful assistant."
#       },
#       {
#         "role": "user",
#         "content": "Hello!"
#       }
#     ]
#   }'

endpoints = [
    ("openai api", "https://api.openai.com/v1", "OPENAI_API_KEY", {}),
    ("cloudflare proxy", "https://api.braintrust.dev/v1/proxy", "OPENAI_API_KEY", {"x-bt-use-cache": "never"}),
    ("cloudflare proxy", "https://api.braintrust.dev/v1/proxy", "BRAINTRUST_API_KEY", {"x-bt-use-cache": "never"}),
    (
        "cloudfront -> cloudflare",
        "https://d3p9hsahjc1cvu.cloudfront.net/v1/proxy",
        "BRAINTRUST_API_KEY",
        {"x-bt-use-cache": "never"},
    ),
    (
        "lambda url",
        "https://63igfg2qumac3a66ttxenao2ba0xeqms.lambda-url.us-east-1.on.aws/v1/proxy",
        "BRAINTRUST_API_KEY",
        {"x-bt-use-cache": "never"},
    ),
]

ITERS = 15
WARMUP = 5

if __name__ == "__main__":
    longest_name = max(len(name) for name, _, _, _ in endpoints)
    for name, api_url, api_key, headers in endpoints:
        times = []
        for i in range(ITERS + WARMUP):
            start = time.time()
            resp = requests.post(
                f"{api_url}/chat/completions",
                headers={"Authorization": f"Bearer {os.environ[api_key]}", **headers},
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [
                        {"role": "user", "content": "What is 1+1? Return the answer and a short explanation."}
                    ],
                    "seed": 123,
                    "stream": True,
                    "max_tokens": 256,
                },
                stream=True,
            )
            resp.raise_for_status()

            # Measure time to first byte
            first_byte_received = False
            for line in resp.iter_lines():
                if not first_byte_received:
                    time_to_first_byte = time.time() - start
                    first_byte_received = True
                if line:
                    # Process the line if needed
                    pass

            # Measure full end-to-end latency
            full_latency = time.time() - start
            times.append((time_to_first_byte, full_latency))

        # Exclude the first two
        times = [t for t in times[WARMUP:]]

        # Calculate statistics for time to first byte and full latency
        ttfb_times = [t[0] * 1000 for t in times]  # Convert to milliseconds
        full_times = [t[1] * 1000 for t in times]  # Convert to milliseconds

        # Print statistics for time to first byte
        print(
            f"{name:<{longest_name}} [{api_key:<18}] Time to First Byte (ms) "
            f"min: {min(ttfb_times):8.2f}, "
            f"max: {max(ttfb_times):8.2f}, "
            f"avg: {sum(ttfb_times) / len(ttfb_times):8.2f}, "
            f"stddev: {statistics.stdev(ttfb_times):8.2f}"
        )

        # Print statistics for full latency
        print(
            f"{name:<{longest_name}} [{api_key:<18}] Full Latency (ms)       "
            f"min: {min(full_times):8.2f}, "
            f"max: {max(full_times):8.2f}, "
            f"avg: {sum(full_times) / len(full_times):8.2f}, "
            f"stddev: {statistics.stdev(full_times):8.2f}"
        )

        print("")
