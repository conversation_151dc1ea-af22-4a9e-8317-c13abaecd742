#!/usr/bin/env python3
"""
Script to generate spans with configurable count, depth, and random payloads.

Usage:
    python generate_spans.py --count 100 --depth 1 --project "test-spans"
    python generate_spans.py --count 50 --depth 2 --project "test-spans"
"""

import argparse
import random
import sys
import uuid

import braintrust
from faker import Faker

fake = Faker()


def generate_random_payload(min_size, max_size):
    """Generate a random text payload between min_size and max_size bytes."""
    target_size = random.randint(min_size, max_size)

    # Start with some text
    payload = []
    current_size = 0

    while current_size < target_size:
        # Mix different types of fake data
        text_generators = [
            fake.text,
            fake.paragraph,
            lambda: fake.catch_phrase() + " " + fake.bs(),
            lambda: fake.company() + " - " + fake.company_suffix(),
            fake.address,
            fake.email,
            fake.phone_number,
            lambda: fake.date_time_this_year().isoformat(),
        ]

        generator = random.choice(text_generators)
        new_text = generator()
        payload.append(new_text)
        current_size += len(new_text) + 1  # +1 for the space we'll add

    # Join with spaces and trim to target size
    full_text = " ".join(payload)
    if len(full_text) > target_size:
        full_text = full_text[:target_size]

    return full_text


def create_spans_depth_1(logger, count, avg_span_size, thread_id):
    """Create spans with depth 1 (all direct children of root)."""
    conversation_history = []

    with logger.start_span(name="Root Span", type="llm") as root_span:
        root_input = [{"role": "user", "content": "Generate multiple LLM responses for testing purposes"}]
        root_output = [
            {
                "role": "assistant",
                "content": f"I will create {count} child LLM spans to demonstrate various AI capabilities",
            }
        ]

        root_span.log(
            input=root_input,
            output=root_output,
            metadata={"depth": 1, "total_spans": count, "thread_id": thread_id},
        )

        # Add root conversation to history
        conversation_history.extend(root_input + root_output)

        for i in range(count):
            with root_span.start_span(name=f"Child LLM Span {i+1}", type="llm") as child_span:
                payload = generate_random_payload(int(avg_span_size * 0.5), int(avg_span_size * 1.5))

                # Generate realistic LLM-style prompts and responses
                user_prompts = [
                    f"Explain the concept of {fake.word()} in simple terms",
                    f"Write a brief summary about {fake.company()}",
                    f"Analyze the following data: {fake.sentence()}",
                    f"Help me understand {fake.catch_phrase()}",
                    f"Generate a response about {fake.job()}",
                ]

                user_prompt = random.choice(user_prompts)
                new_user_message = {"role": "user", "content": user_prompt}
                new_assistant_message = {"role": "assistant", "content": payload}

                # Build input with conversation history + new user message
                span_input = conversation_history + [new_user_message]
                span_output = [new_assistant_message]

                child_span.log(
                    input=span_input,
                    output=span_output,
                    metrics={
                        "latency": random.uniform(0.1, 1.0),
                        "prompt_tokens": random.randint(1, 100000),
                        "completion_tokens": random.randint(1, 100000),
                    },
                    metadata={
                        "span_index": i + 1,
                        "payload_size": len(payload),
                        "span_type": "child",
                        "company": fake.company(),
                        "timestamp": fake.date_time_this_year().isoformat(),
                        "conversation_length": len(span_input),
                        "model": "gpt-4",
                    },
                )

                # Add new messages to conversation history for next span
                conversation_history.extend([new_user_message, new_assistant_message])


def create_spans_depth_2(logger, count, avg_span_size, thread_id):
    """Create spans with depth 2 (root -> intermediate -> leaf)."""
    # Calculate how to distribute spans across intermediate levels
    # For simplicity, we'll create sqrt(count) intermediate spans, each with sqrt(count) children
    import math

    intermediate_count = max(1, int(math.sqrt(count)))
    leaves_per_intermediate = max(1, count // intermediate_count)

    # Adjust if we have remainder
    remaining = count - (intermediate_count * leaves_per_intermediate)

    # Root conversation history
    root_conversation_history = []

    with logger.start_span(name="Root Span", type="llm") as root_span:
        root_input = [
            {"role": "user", "content": "Create a complex multi-layered LLM conversation with nested reasoning"}
        ]
        root_output = [
            {
                "role": "assistant",
                "content": f"I will create {intermediate_count} intermediate LLM spans with approximately {leaves_per_intermediate} leaves each, demonstrating hierarchical AI reasoning",
            }
        ]

        root_span.log(
            input=root_input,
            output=root_output,
            metadata={
                "depth": 2,
                "total_spans": count,
                "thread_id": thread_id,
                "intermediate_spans": intermediate_count,
                "leaves_per_intermediate": leaves_per_intermediate,
            },
        )

        # Add root conversation to history
        root_conversation_history.extend(root_input + root_output)

        spans_created = 0
        for i in range(intermediate_count):
            # Distribute remaining spans across the last few intermediates
            current_leaf_count = leaves_per_intermediate
            if remaining > 0 and i >= intermediate_count - remaining:
                current_leaf_count += 1

            # Start a new conversation branch for each intermediate span
            intermediate_conversation_history = root_conversation_history.copy()

            with root_span.start_span(name=f"Intermediate LLM Span {i+1}", type="llm") as intermediate_span:
                intermediate_payload = generate_random_payload(
                    int(avg_span_size * 0.5), int(avg_span_size * 1.5)
                )  # Smaller for intermediate

                # Generate intermediate LLM conversation
                intermediate_prompts = [
                    f"Break down the analysis of {fake.word()} into sub-components",
                    f"Research and organize information about {fake.company()}",
                    f"Plan the approach for {fake.catch_phrase()}",
                    f"Coordinate the workflow for {fake.job()} related tasks",
                ]

                intermediate_prompt = random.choice(intermediate_prompts)
                intermediate_user_message = {"role": "user", "content": intermediate_prompt}
                intermediate_assistant_message = {"role": "assistant", "content": intermediate_payload}

                # Build input with root conversation history + new user message
                intermediate_span_input = intermediate_conversation_history + [intermediate_user_message]
                intermediate_span_output = [intermediate_assistant_message]

                intermediate_span.log(
                    input=intermediate_span_input,
                    output=intermediate_span_output,
                    metadata={
                        "span_index": i + 1,
                        "span_type": "intermediate",
                        "children_count": current_leaf_count,
                        "payload_size": len(intermediate_payload),
                        "department": fake.job(),
                        "conversation_length": len(intermediate_span_input),
                    },
                )

                # Add intermediate conversation to branch history
                intermediate_conversation_history.extend([intermediate_user_message, intermediate_assistant_message])

                for j in range(current_leaf_count):
                    spans_created += 1
                    if spans_created > count:
                        break

                    with intermediate_span.start_span(name=f"Leaf LLM Span {i+1}.{j+1}", type="llm") as leaf_span:
                        payload = generate_random_payload(int(avg_span_size * 0.5), int(avg_span_size * 1.5))

                        # Generate leaf-level LLM conversation
                        leaf_prompts = [
                            f"Provide specific details about {fake.word()}",
                            f"Analyze this specific case: {fake.sentence()}",
                            f"Generate examples for {fake.catch_phrase()}",
                            f"Explain the technical aspects of {fake.job()}",
                            f"Summarize findings about {fake.company()}",
                        ]

                        leaf_prompt = random.choice(leaf_prompts)
                        leaf_user_message = {"role": "user", "content": leaf_prompt}
                        leaf_assistant_message = {"role": "assistant", "content": payload}

                        # Build input with full conversation history + new user message
                        leaf_span_input = intermediate_conversation_history + [leaf_user_message]
                        leaf_span_output = [leaf_assistant_message]

                        leaf_span.log(
                            input=leaf_span_input,
                            output=leaf_span_output,
                            metadata={
                                "span_index": f"{i+1}.{j+1}",
                                "span_type": "leaf",
                                "parent_intermediate": i + 1,
                                "payload_size": len(payload),
                                "person": fake.name(),
                                "location": fake.city(),
                                "score": random.uniform(0.1, 1.0),
                                "conversation_length": len(leaf_span_input),
                            },
                        )

                        # Add leaf conversation to branch history for next leaf
                        intermediate_conversation_history.extend([leaf_user_message, leaf_assistant_message])

                if spans_created >= count:
                    break


def main():
    parser = argparse.ArgumentParser(description="Generate spans with configurable count and depth")
    parser.add_argument("--count", type=int, default=10, help="Number of spans to create (default: 10)")
    parser.add_argument(
        "--depth", type=int, choices=[1, 2], default=1, help="Nesting depth: 1 (flat) or 2 (nested) (default: 1)"
    )
    parser.add_argument("--project", type=str, default="span-generator", help="Project name (default: span-generator)")
    parser.add_argument("--avg-span-size", type=int, default=2048, help="Average span size in bytes (default: 2048)")
    parser.add_argument("--thread-id", type=str, default=str(uuid.uuid4()), help="Thread ID to be put in metadata")

    args = parser.parse_args()

    print(f"Generating {args.count} spans with depth {args.depth} in project '{args.project}'...")

    # Initialize braintrust logger
    logger = braintrust.init_logger(project=args.project)

    if args.depth == 1:
        create_spans_depth_1(logger, args.count, args.avg_span_size, args.thread_id)
    elif args.depth == 2:
        create_spans_depth_2(logger, args.count, args.avg_span_size, args.thread_id)

    print(f"✅ Successfully created {args.count} spans with depth {args.depth}")
    print(f"📊 Project: {args.project}")


if __name__ == "__main__":
    main()
