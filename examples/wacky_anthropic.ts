import OpenAI from "openai";

const params = {
  model: "claude-instant-1.2",
  max_tokens: 4096,
  temperature: 0,
  messages: [
    {
      role: "system",
      content:
        "Answer user questions by using tools to gather information.\n" +
        "\n" +
        "To be as concise as possible, when giving explanations or summaries, do not bother to use proper English.\n" +
        "Rather, use the shortest possible text that conveys the meaning.\n" +
        'For example, instead of saying "The results of the second web search reveal that this person is a politician", say "Second web search reveals person is politician".',
    },
    {
      role: "user",
      content:
        "What is the best medal (Gold, Silver, or Bronze) that <PERSON> has ever been awarded at the International Mathematics Olympiad?",
    },
    {
      role: "assistant",
      content:
        "1. Describe current plan. 2. Aggregate potential answers with no info yet. 3. Use onlineLlm and webSearch to find <PERSON>'s best IMO medal. 4. If no high-confidence answers, gather more info.",
    },
    {
      role: "tool",
      tool_call_id: "call_FasxIEXRXROEDaizvMyjqRtY",
      content:
        "[\n" +
        "  {\n" +
        '    "source": "Perplexity LLM",\n' +
        `    "answer": "The best medal that <PERSON> has not been explicitly mentioned in the provided search results. The search results focus on the Vietnamese students and their performance at the 2023 International Mathematical Olympiad (IMO). They indicate that Vietnamese students won two Gold, two Silver, and two Bronze medals, but there is no direct reference to <PERSON> <PERSON>'s individual medal achievement.\\n\\nTo stay precise and concise within the search results given:\\n\\nThe best medal awarded to a Vietnamese student at the 2023 International Mathematical Olympiad was a Gold medal. This is in accordance with the information provided, where Pham Viet Hung and Nguyen An Thinh from Vietnam won Gold medals. Without specific references to Vincent Huang, we cannot determine the best medal he has ever been awarded at the IMO."\n` +
        "  },\n" +
        "  {\n" +
        '    "source": "Tavily LLM",\n' +
        '    "answer": "Vincent Huang has been awarded two Gold medals at the International Mathematics Olympiad."\n' +
        "  }\n" +
        "]",
    },
    {
      role: "user",
      content:
        "Before you continue, summarize any relevant information that you just received from the tools.\n" +
        "Be as concise as possible while still including all relevant details and specific values.\n" +
        "\n" +
        "Note that tools like `webSearch` and `scrapeUrl` are not sources.\n" +
        "When summarizing `webSearch` results, the URL/link of each result would be its `source`.\n" +
        "Do not condense the `source` value at all. For example, if it is a URL, include the full URL instead of just the domain.\n" +
        "\n" +
        "You will output a summary of information returned by the most recent tool calls, grouped by source.\n" +
        "Do not output any raw text or markdown formatting. You must only output a valid JSON array.\n" +
        "Each object in the array must have the following shape, specified in TypeScript:\n" +
        "```\n" +
        "{\n" +
        "  source: string; // The source of this information\n" +
        "  information: string; // Information gleaned from this source\n" +
        "}\n" +
        "```\n" +
        "\n" +
        "MAKE SURE YOU DO NOT OUTPUT ANY RAW TEXT OR MARKDOWN FORMATTING.\n" +
        "\n" +
        "Example bad output (DO NOT DO THIS):\n" +
        '"""\n' +
        "Here is a summary of the information returned by the most recent tool calls:\n" +
        "\n" +
        "```json\n" +
        "[\n" +
        "  {\n" +
        '    "source": ...,\n' +
        '    "information": ...\n' +
        "  }\n" +
        "]\n" +
        "```\n" +
        '"""\n' +
        'This example is bad because it includes raw text ("Here is a summary...") and markdown formatting ("```json").\n' +
        "\n" +
        "Example good output (DO THIS):\n" +
        '"""\n' +
        "[\n" +
        "  {\n" +
        '    "source": ...,\n' +
        '    "information": ...\n' +
        "  }\n" +
        "]\n" +
        '"""\n' +
        "This example is good because it only includes a valid JSON array and nothing else.",
    },
  ],
};

const client = new OpenAI({
  baseURL: "http://127.0.0.1:8787/v1",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

async function main() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  console.log(await client.chat.completions.create(params as any));
}

main();
