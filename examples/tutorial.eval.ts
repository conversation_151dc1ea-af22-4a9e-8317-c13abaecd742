import { Factuality } from "autoevals";
import { Eval, currentSpan } from "braintrust";

function myFunction({
  output,
  expected,
}: {
  output: string;
  expected: string;
}) {
  return output === expected ? 1 : 0;
}

Eval(
  "<PERSON> Hi Bot", // Replace with your project name
  {
    data: () => {
      return [
        {
          input: "<PERSON>",
          expected: "<PERSON> David",
        },
      ]; // Replace with your eval dataset
    },
    task: (input, { metadata }) => {
      metadata.flavor = "apple";

      currentSpan().log({ metadata: { res: "hello" } });
      return "Hi " + input; // Replace with your LLM call
    },
    scores: [Factuality, myFunction],
  },
);
