import random
import time

import braintrust
import openai

logger = braintrust.init_logger(project="log openai")
client = braintrust.wrap_openai(openai.OpenAI())

if __name__ == "__main__":
    for i in range(100):
        print(i)
        print(
            client.chat.completions.create(
                model="gpt-3.5-turbo", messages=[{"role": "user", "content": "What is 1+1?"}], temperature=0
            )
        )
        time.sleep(random.random() / 5)
