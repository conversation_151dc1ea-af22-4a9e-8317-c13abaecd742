from typing import List

import openai
from autoevals import <PERSON><PERSON><PERSON>ein
from braintrust import <PERSON><PERSON>, init_dataset, wrap_openai

# Wrap the OpenAI client for automatic logging
client = wrap_openai(openai.AsyncOpenAI())


async def task(input, hooks):
    """Task function that uses parameters from hooks."""
    parameters = hooks.parameters

    # Use the parameters - access them directly like in TypeScript
    prefix = parameters.get("prefix", "this is a math problem")
    if parameters.get("include_prefix", False):
        prompt_input = f"{prefix}:{input}"
    else:
        prompt_input = input

    # Build and execute the prompt using the main prompt parameter
    completion = await client.chat.completions.create(**parameters["main"].build(input=prompt_input))

    return completion.choices[0].message.content or ""


# Run the evaluation with parameters
# Note: Python currently doesn't have built-in parameter validation like TypeScript's Zod,
# so parameters are accessed directly from the hooks object. You can add validation
# in your task function as needed.
Eval(
    "Simple eval",
    data=init_dataset("local dev", "sanity"),
    task=task,
    scores=[Levenshtein],
    parameters={
        # Prompt parameters
        "main": {
            "type": "prompt",
            "description": "This is the main prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        "another": {
            "type": "prompt",
            "description": "This is another prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        # Note: Unlike TypeScript which uses Zod schemas, Python parameters
        # are currently passed through directly without schema validation
        # You can implement validation in your task function if needed
    },
)
