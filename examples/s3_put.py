# Tests https://aws.amazon.com/about-aws/whats-new/2024/08/amazon-s3-conditional-writes/
# via If-None-Match on S3 and Minio
import argparse

import boto3
from botocore.exceptions import ClientError


def test_s3_if_none_match(minio):
    # Initialize S3 client
    if minio:
        s3 = boto3.client(
            "s3",
            endpoint_url="http://localhost:10000",
            aws_access_key_id="minio_root_user",
            aws_secret_access_key="minio_root_password",
        )
    else:
        s3 = boto3.client("s3")

    # Create a unique bucket name
    bucket_name = f"braintrust-test-bucket-if-none-match"
    key = "test-object"
    content = b"Hello, World!"

    new_key = "new-test-object"
    try:
        # Create the bucket
        try:
            s3.create_bucket(Bucket=bucket_name)
        except ClientError as e:
            print(f"Bucket already exists: {e}")
            pass

        # Upload the object for the first time
        s3.put_object(Bucket=bucket_name, Key=key, Body=content)

        # Try to upload again with If-None-Match set to *
        try:
            s3.put_object(Bucket=bucket_name, Key=key, Body=content, IfNoneMatch="*")
            print("Test failed: Object was uploaded despite If-None-Match='*'")
        except ClientError as e:
            if e.response["Error"]["Code"] == "PreconditionFailed":
                print("Test passed: If-None-Match='*' prevented overwrite of existing object")
            else:
                print(f"Test failed: Unexpected error: {e}")

        # Try to upload a new object with If-None-Match set to *
        s3.put_object(Bucket=bucket_name, Key=new_key, Body=content, IfNoneMatch="*")
        print("Test passed: New object uploaded successfully with If-None-Match='*'")

    except Exception as e:
        print(f"Test failed: Unexpected error: {e}")

    finally:
        # Clean up: delete objects and bucket
        s3.delete_object(Bucket=bucket_name, Key=key)
        s3.delete_object(Bucket=bucket_name, Key=new_key)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--minio", action="store_true")
    args = parser.parse_args()
    test_s3_if_none_match(minio=args.minio)
