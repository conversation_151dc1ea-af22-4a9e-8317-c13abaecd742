import braintrust
import requests
from pydantic import BaseModel

project = braintrust.projects.create(name="github")


class Args(BaseModel):
    org: str
    repo: str


def handler(org, repo):
    url = f"https://api.github.com/repos/{org}/{repo}/commits?per_page=1"
    resp = requests.get(url)
    resp.raise_for_status()
    data = resp.json()
    if len(data) > 0:
        return data[0]
    return None


latest_commit = project.tools.create(
    handler=handler,
    name="Get latest commit",
    slug="get-latest-commit",
    description="Get the latest commit in a repository",
    parameters=Args,
)

project.prompts.create(
    model="gpt-4o-mini",
    name="Commit bot",
    slug="commit-bot",
    messages=[
        {
            "role": "system",
            "content": "You are a helpful assistant that can help with GitHub.",
        },
        {
            "role": "user",
            "content": "{{{question}}}",
        },
    ],
    tools=[latest_commit],
)
