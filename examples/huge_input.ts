import { initDataset } from "braintrust";
import { faker } from "@faker-js/faker";

let currentSize = 0;
const TARGET_SIZE = 5_000_000;

function generateHugeInput() {
  const input = {
    foo: "bar",
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
    data: [] as any[],
  };

  while (currentSize < TARGET_SIZE) {
    const result = generateRandomEntry({}, 0);
    currentSize += JSON.stringify(result).length;
    input.data.push(result);
  }

  return input;
}

function generateRandomEntry(base: Record<string, unknown>, depth: number) {
  if (depth >= 3 && Math.random() < 0.8) {
    return faker.lorem.sentence(1);
  }
  /*
  if (Math.random() < 0.2) {
    //return faker.lorem.sentence(5);
    return faker.lorem.sentence(1);
  }
    */

  const keys = Math.floor(Math.random() * 6);
  for (let i = 0; i < keys; i++) {
    if (i === 2) {
      base["long"] = Object.fromEntries(
        Array.from(
          { length: Math.floor(Math.random() * 200 + 2) },
          //() => faker.lorem.paragraph(Math.floor(Math.random() * 200)),
          //() => faker.lorem.paragraph(Math.floor(Math.random() * 20)),
          //() => faker.lorem.paragraph(Math.floor(Math.random() * 20)),
          (_, i) => [
            faker.string.alphanumeric(16),
            {
              one: faker.lorem.paragraph(Math.floor(Math.random() * 2 + 1)),
              two: faker.lorem.paragraph(Math.floor(Math.random() * 2 + 1)),
            },
          ],
          //() => faker.lorem.words(Math.floor(Math.random() * 1 + 1)),
          //).join("\n");
        ),
      );
      continue;
    }
    base[faker.string.alphanumeric(30)] = Array.from(
      { length: Math.floor(Math.random() * 5 + 2) },
      //() => faker.lorem.paragraph(Math.floor(Math.random() * 200)),
      //() => faker.lorem.paragraph(Math.floor(Math.random() * 20)),
      //() => faker.lorem.paragraph(Math.floor(Math.random() * 20)),
      () => faker.lorem.paragraph(Math.floor(Math.random() * 10)),
      //() => faker.lorem.words(Math.floor(Math.random() * 1 + 1)),
      //).join("\n");
    ).join(" ");
  }
  /*
  currentSize += JSON.stringify(base).length;
  if (currentSize > TARGET_SIZE) {
    return base;
  }
    */

  const nestedKey = faker.string.alphanumeric(30);
  base[nestedKey] = generateRandomEntry({}, depth + 1);

  return base;
}

async function main() {
  const dataset = initDataset("crazy yaml", {
    dataset: `huge input ${faker.string.alphanumeric(10)}`,
  });

  dataset.insert({
    input: generateHugeInput(),
  });
  await dataset.flush();
}

main().catch(console.error);
