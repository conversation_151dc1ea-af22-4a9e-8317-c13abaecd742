# This script helps us test rate limits. You can plug in a branch's URL or just use the default URL.
import time
from multiprocessing import Pool

import braintrust


def try_login(i):
    start = time.time()
    braintrust.login(force_login=True, app_url="https://bt-cache-lookups.vercel.app")
    # braintrust.login(force_login=True)
    print(i, time.time() - start)


if __name__ == "__main__":
    with Pool(10) as p:
        p.map(try_login, range(200))
