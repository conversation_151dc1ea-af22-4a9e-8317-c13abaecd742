import json
import sys
from datetime import datetime


def parse_events(filename, pause_threshold_ms=300):
    """Parse SSE events from a file and detect pauses between timestamps in the id field."""

    events = []
    last_time = None
    total_pauses = 0
    longest_pause = 0
    longest_pause_start = None
    first_event_time = None
    event_count_before_pause = 0

    # Read the file line by line to handle SSE format
    with open(filename, "r") as f:
        for line in f:
            if line.startswith("id: "):
                # Extract timestamp from id field
                # Format example: "id: 2025-02-11T04:55:54.506Z (95047ms)"
                try:
                    timestamp_str = line.split("id: ")[1].split(" ")[0]  # Get the ISO timestamp
                    current_time = int(datetime.fromisoformat(timestamp_str.replace("Z", "+00:00")).timestamp() * 1000)

                    if first_event_time is None:
                        first_event_time = current_time

                    events.append({"eventTime": current_time})

                    if last_time is not None:
                        time_diff = current_time - last_time

                        if time_diff >= pause_threshold_ms:
                            total_pauses += 1
                            seconds_since_start = (last_time - first_event_time) / 1000
                            print(f"\nPause detected after {seconds_since_start:.2f} seconds from start!")
                            print(f"Number of events before this pause: {event_count_before_pause}")
                            print(f"Duration: {time_diff/1000:.2f} seconds")
                            print(f"From: {datetime.fromtimestamp(last_time/1000).isoformat()}")
                            print(f"To:   {datetime.fromtimestamp(current_time/1000).isoformat()}")
                            print("-" * 50)

                            if time_diff > longest_pause:
                                longest_pause = time_diff
                                longest_pause_start = last_time

                    last_time = current_time
                    event_count_before_pause += 1
                except Exception as e:
                    print(f"Warning: Failed to parse timestamp: {e}", file=sys.stderr)
                    continue

    # Sort events by eventTime to ensure chronological order
    events.sort(key=lambda x: x["eventTime"])

    # Print summary
    print("\nSummary:")
    print(f"Total number of events: {len(events)}")
    print(f"Total number of pauses: {total_pauses}")
    print(f"Longest pause: {longest_pause/1000:.2f} seconds")
    if longest_pause_start:
        print(f"Longest pause started at: {datetime.fromtimestamp(longest_pause_start/1000).isoformat()}")

    # Calculate average time between events
    if len(events) > 1:
        total_time = events[-1]["eventTime"] - events[0]["eventTime"]
        avg_time = total_time / (len(events) - 1)
        print(f"Average time between events: {avg_time:.2f}ms")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze_events.py <events_file.json>")
        sys.exit(1)

    parse_events(sys.argv[1])
