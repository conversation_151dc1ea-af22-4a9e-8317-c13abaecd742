import { initLogger, wrap<PERSON>astraAgent, BraintrustMiddleware } from "braintrust";
import { Agent } from "@mastra/core/agent";
import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { openai } from "@ai-sdk/openai";
import { wrapLanguageModel } from "ai";

// Initialize Braintrust logging (no-op if omitted)
initLogger({
  projectName: "Mastra Agent",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Simple calculator tool
const calculator = createTool({
  id: "calculator",
  description: "Add two numbers",
  inputSchema: z.object({ a: z.number(), b: z.number() }),
  outputSchema: z.object({ result: z.number() }),
  execute: async ({ context }) => ({ result: context.a + context.b }),
});

const agent = new Agent({
  name: "Tooling Agent",
  instructions: "Use tools when helpful.",
  model: wrapLanguageModel({
    model: openai("gpt-4o-mini"),
    middleware: BraintrustMiddleware(),
  }),
  tools: { calculator },
});

// Wrap agent in place for tracing
const wrappedAgent = wrapMastraAgent(agent, { span_name: "toolAgent" });

async function main() {
  // Use generateVNext (AI SDK v5 method) with tools
  const res = await wrappedAgent.generateVNext(
    "What is 21 + 21? Use tools if needed.",
  );
  console.log(res?.text ?? res);

  // Use streamVNext for streaming responses
  const stream = await wrappedAgent.streamVNext(
    "What is 21 + 21? Use tools if needed.",
  );
  console.log(await stream.text);
}

main();
