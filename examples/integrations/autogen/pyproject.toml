[project]
name = "autogen"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "autogen-agentchat>=0.7.4",
    "autogen-core>=0.7.4",
    "autogen-ext[openai]>=0.7.4",
    "braintrust[otel]>=0.2.7",
    "opentelemetry-instrumentation>=0.58b0",
    "opentelemetry-instrumentation-openai>=0.47.2",
    "opentelemetry-sdk>=1.37.0",
    "python-dotenv>=1.1.1",
]
