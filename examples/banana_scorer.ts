import { Score } from "autoevals";

const bananaScorer = ({ output, expected, input }): Score => {
  return { name: "banana_scorer", score: output.includes("banana") ? 1 : 0 };
};

(async () => {
  const input = "What is 1 banana + 2 bananas?";
  const output = "3";
  const expected = "3 bananas";

  const result = await bananaScorer({ output, expected, input });
  console.log(`Banana score: ${result.score}`);
})();
