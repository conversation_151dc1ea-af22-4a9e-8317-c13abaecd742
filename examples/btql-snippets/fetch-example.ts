async function runQuery() {
  let cursor = null;
  while (true) {
    const response: Response = await fetch("http://localhost:8000/btql", {
      method: "POST",
      body: JSON.stringify({
        query:
          `select: *
from: project_logs('43341ee9-39d4-4676-9e4e-b7eefb5125b3') summary
limit: 100` + (cursor ? `\n| cursor: '${cursor}'` : ""),
        use_brainstore: true,
        brainstore_realtime: true, // Include the latest realtime data, but a bit slower.
      }),
      headers: {
        Authorization: "Bearer " + process.env.BRAINTRUST_API_KEY, // Substitute your API key here
        "Content-Type": "application/json",
      },
    });
    if (!response.ok) {
      throw new Error(
        `Failed to fetch: ${response.status} (${response.statusText}) ${await response.text()}`,
      );
    }
    const { data, cursor: newCursor } = await response.json();

    for (const row of data) {
      console.log(row);
    }
    cursor = newCursor;

    if (data.length === 0) {
      break;
    }
  }
}

runQuery();
