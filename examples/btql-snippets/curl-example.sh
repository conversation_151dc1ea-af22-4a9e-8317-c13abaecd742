#!/bin/bash

# This simple request fetches a single page.
# Use TypeScript or Python to perform pagination.
curl -X POST 'http://localhost:8000/btql' \
  -H "Authorization: Bearer $BRAINTRUST_API_KEY" \
  -H 'Content-Type: application/json' \
  --data @- << EOF
{
  "query": "select: *\nfrom: project_logs('43341ee9-39d4-4676-9e4e-b7eefb5125b3') summary\nlimit: 100",
  "use_brainstore": true,
  "brainstore_realtime": true
}
EOF
