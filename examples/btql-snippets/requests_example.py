# /// script
# dependencies = [
#   "requests",
# ]
# ///
import os

import requests


def run_query():
    cursor = None
    while True:
        response = requests.post(
            "http://localhost:8000/btql",
            json={
                "query": """select: *
from: project_logs('43341ee9-39d4-4676-9e4e-b7eefb5125b3') summary
limit: 100"""
                + (f"\n| cursor: '{cursor}'" if cursor else ""),
                "use_brainstore": True,
                "brainstore_realtime": True,  # Include the latest realtime data, but a bit slower.
            },
            headers={
                # Substitute your API key here
                "Authorization": "Bearer "
                + os.environ["BRAINTRUST_API_KEY"],
            },
        )
        response.raise_for_status()

        response_json = response.json()

        data = response_json.get("data", [])
        cursor = response_json.get("cursor")

        for row in data:
            print(row)

        if not data:
            break


if __name__ == "__main__":
    run_query()
