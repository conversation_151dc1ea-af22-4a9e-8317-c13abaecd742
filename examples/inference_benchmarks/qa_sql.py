import os
import time

import openai


def gen_prompt(query):
    return (
        '''You are helping a user search for rows in a SQL table called "experiments". The user will provide a search query for you to interpret. If the query has semantic meaning and refers to one or more columns (or keys within JSON columns) in the table, you should translate the query into a valid SQL filter and/or sort clause, and call the "SQL" function with your SQL filter and/or sort clause as arguments. If it is unclear how to translate the query into valid SQL, you should fall back to calling the default "MATCH" function, which runs a simple substring match.

To help you learn how to interpret the user's query, I will provide you with the JSON schema for the "experiments" table, which lists and describes the available fields. Here is the schema:

{"$defs": {"ExperimentSource": {"properties": {"commit": {"description": "Git commit hash", "title": "Commit", "type": "string"}, "branch": {"description": "Git branch name", "title": "Branch", "type": "string"}, "tag": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Git commit tag", "title": "Tag"}, "commit_time": {"description": "Git commit time", "title": "Commit Time", "type": "integer"}, "author_name": {"description": "Git commit author name", "title": "Author Name", "type": "string"}, "author_email": {"description": "Git commit author email address", "title": "Author Email", "type": "string"}, "commit_message": {"description": "Git commit message", "title": "Commit Message", "type": "string"}, "dirty": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Whether the git state was dirty when the experiment was run. If false, the git state was clean", "title": "Dirty"}}, "required": ["commit", "branch", "tag", "commit_time", "author_name", "author_email", "commit_message", "dirty"], "title": "ExperimentSource", "type": "object"}}, "properties": {"id": {"description": "Experiment ID, unique", "title": "Id", "type": "string"}, "name": {"description": "Experiment name", "title": "Name", "type": "string"}, "last_updated": {"description": "Timestamp marking when the experiment was last updated", "title": "Last Updated", "type": "integer"}, "creator": {"additionalProperties": {"type": "string"}, "description": "Information about the creator of the experiment", "title": "Creator", "type": "object"}, "source": {"allOf": [{"$ref": "#/$defs/ExperimentSource"}], "description": "Git state of the experiment"}, "metadata": {"description": "User-provided custom metadata. Ignore this field unless the query mentions a metadata key specifically", "title": "Metadata", "type": "object"}, "avg_factuality_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Avg Factuality Score"}}, "required": ["id", "name", "last_updated", "creator", "source", "metadata", "avg_factuality_score"], "title": "Experiment", "type": "object"}

If the query involves some notion of relative time, like age or recency, you should refer to the experiment's "last_updated" timestamp, and compare it to the current time
"""NOW()""" if it pertains to the query. For example, if the user provides """Query: Experiments from the past 2 days""", you should generate a filter on the "last_updated" column as follows: """last_updated >= NOW() - INTERVAL '2 days'""".

Do not reference columns that do not exist in the provided experiment schema. A query like """weather = 'rainy'""" clearly has semantic meaning, but there is no "weather" column in the schema or anything conceptually related to the weather, so a filter that references it will cause a SQL syntax error! Always double-check that any columns referenced in your SQL actually exist in the provided schema, and fall back to calling the "MATCH" function when in doubt.

Here are some sample queries and associated function calls, along with a brief explanation of each.

<Example>
Query: asdf
Function call: MATCH
Explanation: The query has no semantic meaning, so it is interpreted as a simple substring match.

<Example>
Query: experiments more than 7 hours old
Function call: SQL
Arguments: """{"filter": "last_updated < NOW() - INTERVAL '7 hours'", "sort": null}"""
Explanation: The query is interpreted as a filter on the "last_updated" column, comparing it to 7 hours before the current time of """NOW()""". The query does not have any sort semantics, so the "sort" field is null.

<Example>
Query: creator email is "<EMAIL>"
Function call: SQL
Arguments: """{"filter": "(creator->>'email') = '<EMAIL>'", "sort": null}"""
Explanation: The query is interpreted as a filter on the "email" field of the "creator" JSON column. The query does not have any sort semantics, so the "sort" field is null.

<Example>
Query: has avg_factuality_score
Function call: SQL
Arguments: """{"filter": "avg_factuality_score IS NOT NULL", "sort": null}"""
Explanation: An experiment has a value for avg_factuality_score if and only if the value is not null, so I use a simple """IS NOT NULL""" filter.

<Example>
Query: between 30 and 50 examples, sorted by most recent
Function call: SQL
Arguments: """{"filter": "num_examples >= 30 AND num_examples <= 50", "sort": "last_updated DESC"}"""
Explanation: To filter down to a numeric range, I use an AND clause to combine two simple inequality filters. To make the most recent updates appear first, I apply a descending sort on the "last_updated" column.

<Example>
Query: experiment name is foo or bar, metadata.color: red
Function call: SQL
Arguments: """{"filter": "(name = 'foo' OR name = 'bar') AND (metadata->>'color') = 'red'", "sort": null}"""
Explanation: Since the "name" filter must match either 'foo' or 'bar', we construct an OR filter. Since AND takes precedence over OR, I wrap the OR clause in parentheses to establish proper order of operations.

<Example>
Query: metadata.rag is false
Function call: SQL
Arguments: """{"filter": "(metadata->>'rag')=false", "sort": null}"""
Explanation: The query is interpreted as a filter on the "rag" key within the "metadata" JSON column.

<Example>
Query: grader='human'
Function call: MATCH
Explanation: The query seems intended to be a filter, but there is no "grader" column in the experiment schema. While the "metadata" column could have a "grader" key, we can't know for sure, so we fall back to the MATCH function.

<Example>
Query: no metadata["color"], most recent first
Function call: SQL
Arguments: """{"filter": "(metadata->>'color') IS NULL", "sort": "last_updated DESC"}"""
Explanation: The query is interpreted as a filter on the "metadata->>'color'" key in the "metadata" JSON column, alongside a descending sort on the "last_updated" column to make the most recent updates appear first. We wrap """metadata->>'color'""" in parentheses to make the intended order of operations clear.

<Example>
Query: fewest examples
Function call: SQL
Arguments: """{"filter": null, "sort": "num_examples ASC"}"""
Explanation: The query is interpreted as a sort on the "num_examples" column, with an ascending sort so that the rows with the fewest examples appear first.

<Example>
Query: clean git state
Function call: SQL
Arguments: """{"filter": "(source->>'dirty') = false", "sort": null}"""
Explanation: The query is interpreted as a filter on the "dirty" key within the "source" JSON column, which refers to the git state of the experiment. Any experiment with "dirty" set to false is considered clean.

<Example>
Query: order by size
Function call: MATCH
Explanation: The query seems intended to be a sort, but there is no "size" column in the experiment schema, and there are no other fields that could be interpreted as a size. Thus, we fall back to simple substring match instead of calling the SQL function.

<Example>
Query: limit 20
Function call: MATCH
Explanation: The query appears to be a limit clause, but the "SQL" function can only be called with filter and sort clauses, so we fall back to a simple substring match.

<Example>
Query: commit starts with 12j4z67 or git author 'austin m'
Function call: SQL
Arguments: """{"filter": "(source->>'commit') ILIKE '12j4z67%' OR (source->>'author_name') = 'austin m'", "sort": null}"""
Explanation: The query is interpreted as an OR filter on the "commit" and "author_name" keys within the "source" JSON column, which contains the git info. I use the ILIKE operator to match the "commit" against the provided prefix.


The search query will be provided in the following format:

Query: '''
        + query
        + """

When the user provides a query, you should examine it and call the appropriate function. Remember, if the query has semantic meaning and you can translate it into a valid SQL filter and/or sort clause, call the "SQL" function and pass it your filter and/or sort clause as arguments. Otherwise, fall back to a simple substring match and call the "MATCH" function instead. In either case, you should also pass an explanation to the function. Again, it is crucial that you only call the "SQL" function with valid SQL query syntax; when in doubt, call the "MATCH" function instead.
"""
    )


QUERY = "Which experiment has the fewest examples?"


MODELS = ["gpt-3.5-turbo", "claude-2.1"]  # "mistralai/Mixtral-8x7B-Instruct-v0.1", "mistral-small"]
WARMUPS = 2
ITERS = 10


def run_prompt(query, model):
    prompt = gen_prompt(query)
    resp = client.chat.completions.create(
        model=model,
        messages=[
            dict(role="user", content=prompt),
        ],
        max_tokens=1024,
        temperature=0,
    )
    return resp


if __name__ == "__main__":
    client = openai.OpenAI(
        base_url="https://api.braintrust.dev/v1/proxy",
        api_key=os.environ["BRAINTRUST_API_KEY"],
        default_headers={"x-bt-use-cache": "never"},
    )

    for m in MODELS:
        warmup_start = time.time()
        for x in range(WARMUPS):
            run_prompt(QUERY, m)
        warmup_end = time.time()

        start = time.time()
        for x in range(ITERS):
            run_prompt(QUERY, m)
        end = time.time()

        print(f"{m}: {end - start} seconds, {warmup_end - warmup_start} seconds warmup")
        print(f"{m}: {(end - start)/ITERS} seconds per iteration")
