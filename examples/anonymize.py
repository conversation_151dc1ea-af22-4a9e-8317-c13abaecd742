import argparse
import os
import sys
from urllib.parse import urlparse

import boto3
import braintrust
import duckdb
import pyarrow as pa
from faker import Faker
from pyarrow import parquet as pq

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
sys.path.append(SCRIPT_DIR)

import randomize

CACHE_DIR = os.path.join(SCRIPT_DIR, "cache")
s3 = boto3.client("s3")


def randomize_most_fields(d):
    Faker.seed(1231212312)
    input = randomize.randomize(d["input"], True)
    r = randomize.randomize(d, True)
    r["input"] = input
    return r


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Anonymize an experiment or dataset.")
    parser.add_argument("url", help="URL of the experiment or dataset to anonymize.")
    parser.add_argument("--api-key", help="BrainTrust API Key", default=os.environ["BRAINTRUST_API_KEY"])

    args = parser.parse_args()

    # Parse the URL
    url = urlparse(args.url)
    api_url = url.scheme + "://" + url.hostname
    path_pieces = url.path.split("/")
    org_name = path_pieces[2]
    project_name = path_pieces[4]

    if path_pieces[5] == "d":
        object_type = "dataset"
        object_name = path_pieces[6]
    else:
        object_type = "experiment"
        object_name = path_pieces[5]

    braintrust.login(app_url=api_url, api_key=args.api_key, org_name=org_name)
    project = braintrust.api_conn().get(f"/crud/projects", params={"name": project_name}).json()[0]
    object = (
        braintrust.api_conn()
        .get(f"/crud/{object_type}s", params={"project_id": project["id"], "name": object_name})
        .json()[0]
    )

    data = braintrust._ensure_object(object_type, object["id"])
    conn = duckdb.connect(":memory:")
    results = conn.query(f"SELECT * FROM parquet_scan('{data}')").arrow().to_pylist()

    for row in results:
        for key in [
            "id",
            "_xact_id",
            "_object_delete",
            "created",
            "project_id",
            "user_id",
            "experiment_id",
            "dataset_id",
        ]:
            if key in row:
                del row[key]

    randomized = [randomize_most_fields(d) for d in results]
    table = pa.Table.from_pylist(randomized)

    os.makedirs(CACHE_DIR, exist_ok=True)
    pq.write_table(table, os.path.join(CACHE_DIR, f"{object['id']}.parquet"), compression="zstd")
    s3.upload_file(
        os.path.join(CACHE_DIR, f"{object['id']}.parquet"),
        "braintrust-data-staging",
        f"anon/{object['id']}.parquet",
    )
