import { Factuality, Humor, Security, Summary, init } from "autoevals";
import { Eval } from "braintrust";
import { AzureOpenAI } from "openai";

// important or autoevals will try to use openai irregardless of passed in args
delete process.env.OPENAI_API_KEY;

init({
  client: new AzureOpenAI({
    apiKey: process.env.AZURE_OPENAI_API_KEY!,
    apiVersion: process.env.AZURE_OPENAI_API_VERSION!,
    endpoint: process.env.AZURE_OPENAI_ENDPOINT!,
    deployment: "gpt-4o-mini",
  }),
});

Eval("Multi-Scorer Example", {
  data: () => {
    return [
      {
        input: {
          input: "What is the capital of France?",
          context: ["Paris is the capital and largest city of France."],
          output:
            "Tokyo has over 37 million residents in its metropolitan area.",
        },
        expected: "Paris is the capital of France.",
      },
      {
        input: {
          input: "What is the population of Tokyo?",
          context: [
            "Tokyo is the most populous metropolitan area in the world, with over 37 million residents.",
          ],
          output:
            "Tokyo's metropolitan area has a population of over 37 million people.",
        },
        expected:
          "Tokyo's metropolitan area has a population of over 37 million people.",
      },
    ];
  },
  task: () => {
    return "";
  },
  scores: [
    async ({ input, expected }) => {
      return Factuality({
        output: input.output,
        input: input.input,
        expected,
        model: "gpt-4o-mini",
      });
    },
    async ({ input }) => {
      return Humor({
        output: input.output,
        model: "gpt-4o-mini",
      });
    },
    async ({ input }) => {
      return Security({
        output: input.output,
        model: "gpt-4o-mini",
      });
    },
    async ({ input }) => {
      return Summary({
        output: input.output,
        input: input.input,
        model: "gpt-4o-mini",
      });
    },
  ],
});
