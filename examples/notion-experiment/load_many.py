import argparse
import json
import os
import random

import braintrust
from faker import Faker

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="create a notion-style experiment")
    parser.add_argument("--rows", default=1000, type=int)

    args = parser.parse_args()

    fake = Faker()

    dataset = braintrust.init_dataset(project="large-dataset", name="large-dataset", use_output=False)

    total_payload_size = 0
    for i in range(args.rows):
        payload = dict(input=fake.xml(nb_elements=100) * 200, output=fake.xml(nb_elements=100))
        total_payload_size += len(json.dumps(payload))
        dataset.insert(payload)

        if i % 100 == 0:
            print(f"Inserted {i} rows")
            print(f"Total payload size: {total_payload_size}")
            print(f"Avg payload size: {total_payload_size / (i + 1)}")
