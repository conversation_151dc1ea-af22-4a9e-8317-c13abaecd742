import argparse
import json
import os
import random

import braintrust
from faker import Faker

CUR_DIR = os.path.dirname(os.path.abspath(__file__))

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="create a notion-style experiment")
    parser.add_argument("--schema", default=os.path.join(CUR_DIR, "notion_schema.json"))
    parser.add_argument("--trial-count", default=10, type=int)
    parser.add_argument("--name", default="repro", type=str)

    args = parser.parse_args()
    fake = Faker()

    with open(args.schema, "r") as f:
        schema = json.load(f)

    datasets, scores, scores_per_dataset, inputs = (
        schema["datasets"],
        schema["scores"],
        schema["scores_per_dataset"],
        schema["inputs"],
    )

    experiment = braintrust.init("Many-Scores-And-Datasets", experiment=args.name)
    for row in inputs:
        dataset = random.choice(datasets)
        for _ in range(args.trial_count):
            experiment.log(
                input=row,
                output=fake.xml(nb_elements=100),
                metadata={"dataset": dataset},
                scores={score: random.randint(0, 1) for score in scores_per_dataset[dataset]},
            )

    print(experiment.summarize())
