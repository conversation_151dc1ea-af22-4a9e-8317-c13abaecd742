import argparse
import json
import os
import random

import braintrust
from faker import Faker

CUR_DIR = os.path.dirname(os.path.abspath(__file__))

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="create a notion-style experiment")
    parser.add_argument("--rows", default=100, type=int)
    parser.add_argument("--datasets", default=100, type=int)
    parser.add_argument("--scores", default=50, type=int)

    args = parser.parse_args()

    fake = Faker()

    datasets = list(set(fake.word() for _ in range(args.datasets)))
    scores = list(set(fake.word() for _ in range(args.scores)))
    scores_per_dataset = {d: random.sample(scores, random.randint(1, len(scores))) for d in datasets}

    inputs = [
        [
            {
                "id": 0,
                "type": "context",
                "value": fake.xml(nb_elements=100) * 10,
            }
            for idx in range(random.randint(1, 5))
        ]
        for _ in range(args.rows)
    ]

    with open(os.path.join(CUR_DIR, "notion_schema.json"), "w") as f:
        json.dump(
            {"datasets": datasets, "scores": scores, "scores_per_dataset": scores_per_dataset, "inputs": inputs},
            f,
            indent=2,
        )
