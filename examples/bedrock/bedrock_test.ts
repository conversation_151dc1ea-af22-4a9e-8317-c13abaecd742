import {
  BedrockRuntimeClient,
  InvokeModelCommand,
  InvokeModelWithResponseStreamCommand,
} from "@aws-sdk/client-bedrock-runtime";

const brt = new BedrockRuntimeClient({});
async function main() {
  const params = {
    modelId: "anthropic.claude-3-sonnet-20240229-v1:0",
    contentType: "application/json",
    accept: "application/json",
    body: {
      anthropic_version: "bedrock-2023-05-31",
      max_tokens: 1000,
      messages: [
        {
          role: "user",
          content: "What is 1+1",
        },
      ],
    },
  };

  /*
response = brt.invoke_model(
    body=json.dumps(params["body"]),
    modelId=params["modelId"],
    accept=params["accept"],
    contentType=params["contentType"],
)

response_body = json.loads(response.get("body").read())

# text
print(response_body)
*/
  const input = {
    body: new TextEncoder().encode(JSON.stringify(params["body"])),
    contentType: params["contentType"],
    modelId: params["modelId"],
  };
  {
    const command = new InvokeModelCommand(input);
    const response = await brt.send(command);
    console.log(response);
    const body = new TextDecoder().decode(response.body);
    console.log(body);
  }
  {
    const command = new InvokeModelWithResponseStreamCommand(input);
    const response = await brt.send(command);
    console.log(response);
    const stream = response.body;

    if (stream) {
      for await (const chunk of stream) {
        if (chunk.chunk?.bytes) {
          console.log(JSON.parse(new TextDecoder().decode(chunk.chunk.bytes)));
        }
      }
    }
  }
}

main();
