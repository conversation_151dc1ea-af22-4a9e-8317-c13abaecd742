import json

import boto3

brt = boto3.client(service_name="bedrock-runtime")

params = {
    "modelId": "anthropic.claude-3-sonnet-20240229-v1:0",
    "contentType": "application/json",
    "accept": "application/json",
    "body": {
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 1000,
        "messages": [
            {
                "role": "user",
                "content": "What is 1+1",
            }
        ],
    },
}

response = brt.invoke_model(
    body=json.dumps(params["body"]),
    modelId=params["modelId"],
    accept=params["accept"],
    contentType=params["contentType"],
)

response_body = json.loads(response.get("body").read())

# text
print(response_body)
