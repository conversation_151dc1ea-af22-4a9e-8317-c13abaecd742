import { initLogger, startSpan, traced, withParent } from "braintrust";

async function main() {
  const logger = initLogger({ projectName: "parent_precedence_logs" });

  // Test 1: Logger as parent
  logger.startSpan({ name: "logger_child" }).end();

  // Test 2: Current span beats logger
  await traced(
    async () => {
      startSpan({ name: "current_span_child" }).end();
    },
    { name: "current_span" },
  );

  // Test 3: Propagated parent beats logger
  const parent = logger.startSpan({ name: "propagated_parent" });
  const parentStr = await parent.export();
  parent.end();

  await withParent(parentStr, () => {
    startSpan({ name: "propagated_child" }).end();
  });

  // Test 4: Current span beats propagated parent
  await withParent(parentStr, async () => {
    await traced(
      async () => {
        startSpan({ name: "current_beats_propagated" }).end();
      },
      { name: "current_span_wins" },
    );
  });

  // Test 5: Explicit parent beats everything
  const explicit = logger.startSpan({ name: "explicit_parent" });
  const explicitStr = await explicit.export();
  explicit.end();

  await withParent(parentStr, async () => {
    await traced(
      async () => {
        startSpan({ name: "explicit_child", parent: explicitStr }).end();
      },
      { name: "ignored_current" },
    );
  });
}

main();
