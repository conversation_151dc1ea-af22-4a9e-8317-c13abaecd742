import { NumericDiff } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";

async function main() {
  await <PERSON><PERSON>("print-test", {
    data: Array.from({ length: 100 }, (_, i) => ({ input: i, expected: i })),
    task: (input) => input,
    scores: [NumericDiff],
    experimentName: "1",
  });
  console.log(
    await <PERSON><PERSON>("print-test", {
      data: Array.from({ length: 100 }, (_, i) => ({ input: i, expected: i })),
      task: (input) => input,
      scores: [NumericDiff],
      experimentName: "2",
    }),
  );
  console.log(
    `${await <PERSON>l("print-test", {
      data: Array.from({ length: 100 }, (_, i) => ({ input: i, expected: i })),
      task: (input) => input,
      scores: [NumericDiff],
      experimentName: "2",
    })}`,
  );
}

main();
