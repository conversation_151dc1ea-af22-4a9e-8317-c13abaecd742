import { openai } from "@ai-sdk/openai";
import { generateText, tool } from "ai";
import { z } from "zod";
import { AnswerRelevancy, Moderation } from "autoevals";
import { initLogger, wrapAISDKModel, wrapTraced } from "braintrust";

// Initialize Braintrust logging
const logger = initLogger({ projectName: "agentic-tools-logs" });

// Wrap the model so model calls are automatically traced
const model = wrapAISDKModel(openai("gpt-4o-mini"));

// Helper functions wrapped with tracing to demonstrate nested spans
const checkFreezing = wrapTraced(
  async function checkFreezing({ fahrenheit }: { fahrenheit: number }) {
    return fahrenheit <= 32;
  },
  { type: "function" },
);

const convertToFahrenheit = wrapTraced(
  async function convertToFahrenheit({ celsius }: { celsius: number }) {
    // F = C * 9/5 + 32
    const fahrenheit = celsius * (9 / 5) + 32;
    const freezing = await checkFreezing({ fahrenheit });
    return { fahrenheit, freezing };
  },
  { type: "function" },
);

async function main() {
  await logger.traced(async (span) => {
    const userInstruction =
      "For San Francisco, CA and New York, NY: get the weather for both, report in Fahrenheit, and say if either is freezing. Then convert 100 USD to EUR and add 25 to the result. Prefer calling multiple tools.";

    const { text } = await generateText({
      model,
      maxSteps: 10,
      system:
        "You are a helpful agent. Use tools for factual data, calculations, and conversions. Use multiple tools when needed.",
      prompt: userInstruction,
      tools: {
        // Tool 1: Get weather for a city (returns Celsius; we convert to Fahrenheit via a nested traced helper)
        getWeather: tool({
          description:
            "Get the weather for a city. Returns temperature in Celsius.",
          parameters: z.object({
            city: z
              .string()
              .describe(
                "City name with region if helpful, e.g., 'San Francisco, CA'",
              ),
          }),
          execute: async ({ city }: { city: string }) => {
            // Fake data for demo purposes
            const base = 20; // 20C baseline
            const variance = Math.floor(Math.random() * 7) - 3; // +/- 3C
            const celsius = base + variance;
            const { fahrenheit, freezing } = await convertToFahrenheit({
              celsius,
            });
            return { city, celsius, fahrenheit, freezing };
          },
        }),

        // Tool 2: Currency conversion
        convertCurrency: tool({
          description: "Convert between currencies using a fixed demo rate.",
          parameters: z.object({
            amount: z.number(),
            from: z.string().describe("Currency code, e.g., USD"),
            to: z.string().describe("Currency code, e.g., EUR"),
          }),
          execute: async ({
            amount,
            from,
            to,
          }: {
            amount: number;
            from: string;
            to: string;
          }) => {
            // Demo static rates
            const rates: Record<string, number> = {
              "USD:EUR": 0.92,
              "EUR:USD": 1.09,
            };
            const key = `${from}:${to}`;
            const rate = rates[key] ?? 1;
            const converted = amount * rate;
            return { amount, from, to, rate, converted };
          },
        }),

        // Tool 3: Add numbers (simple calculator)
        addNumbers: tool({
          description: "Add two numbers.",
          parameters: z.object({ a: z.number(), b: z.number() }),
          execute: async ({ a, b }: { a: number; b: number }) => ({
            sum: a + b,
          }),
        }),
      },
    });

    console.log("\n===== Agent Output =====\n");
    console.log(text);

    // Run AutoEvals scorers within their own child spans so the scorer's
    // internal chat-completion spans nest under them
    const openAiApiKey = process.env.OPENAI_API_KEY;

    await span.traced(
      async (scoreSpan) => {
        const result = await Moderation({ output: text, openAiApiKey });
        scoreSpan.log({
          scores: { Moderation: result.score },
          metadata: { Moderation: result.metadata },
        });
        return result;
      },
      {
        name: "Moderation",
        spanAttributes: { type: "score" },
        event: { input: { output: text } },
      },
    );

    await span.traced(
      async (scoreSpan) => {
        const result = await AnswerRelevancy({
          input: userInstruction,
          context: userInstruction,
          output: text,
          openAiApiKey,
        });
        scoreSpan.log({
          scores: { AnswerRelevancy: result.score },
          metadata: { AnswerRelevancy: result.metadata },
        });
        return result;
      },
      {
        name: "AnswerRelevancy",
        spanAttributes: { type: "score" },
        event: {
          input: {
            input: userInstruction,
            context: userInstruction,
            output: text,
          },
        },
      },
    );
  });
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
