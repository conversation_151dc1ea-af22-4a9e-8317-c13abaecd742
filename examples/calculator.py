from typing import Literal

import braintrust
import requests
from pydantic import BaseModel, RootModel

project = braintrust.projects.create(name="calculator")


class CalculatorInput(BaseModel):
    op: Literal["add", "subtract", "multiply", "divide"]
    a: float
    b: float


class CalculatorOutput(RootModel[float]):
    pass


def calculator(op, a, b):
    match op:
        case "add":
            return a + b
        case "subtract":
            return a - b
        case "multiply":
            return a * b
        case "divide":
            return a / b


project.tools.create(
    handler=calculator,
    name="Calculator method",
    slug="calculator-2",
    description="A simple calculator that can add, subtract, multiply, and divide.",
    parameters=CalculatorInput,  # You can also provide raw JSON schema here if you prefer
    returns=CalculatorOutput,
    if_exists="replace",
)
