# This mirrors the test_weighted_scores test but populates your org so it's easier to play with things
import os

import braintrust
import requests

if __name__ == "__main__":
    braintrust.login()
    experiment = braintrust.init(project="weighted_summary")

    weighted_avg_def = dict(
        project_id=experiment.project.id,
        name="My avg",
        score_type="weighted",
        categories={
            "score1": 1,
            "score2": 3,
        },
    )
    resp = braintrust.logger._state.api_conn().post(f"/v1/project_score", json=weighted_avg_def)

    min_score_def = dict(
        project_id=experiment.project.id,
        name="My min",
        score_type="minimum",
        categories=[
            "score1",
            "score2",
        ],
    )
    resp = braintrust.logger._state.api_conn().post(f"/v1/project_score", json=min_score_def)

    experiment.log(input=1, output=2, scores={})
    summary = experiment.summarize()

    experiment = braintrust.init(project="weighted_summary")
    experiment.log(input=1, output=2, scores={"score1": None, "score2": None, "score3": None})
    summary = experiment.summarize()

    experiment = braintrust.init(project="weighted_summary")
    experiment.log(input=1, output=2, scores={"score1": 1, "score2": 0.5, "score3": 0})
    summary = experiment.summarize()

    experiment = braintrust.init(project="weighted_summary")
    experiment.log(input=1, output=2, scores={"score1": 1, "score3": None})
    summary = experiment.summarize()

    experiment = braintrust.init(project="weighted_summary")
    experiment.log(input=1, output=2, scores={"score1": None, "score2": 0.5, "score3": None})
    summary = experiment.summarize()

    experiment = braintrust.init(project="weighted_summary")
    with experiment.start_span() as root_span:
        with root_span.start_span() as span:
            span.log(input="a", output="out", scores={"score1": 0.5})
        with root_span.start_span() as span:
            span.log(input="a", output="out", scores={"score2": 1})

    summary = experiment.summarize()

    logger = braintrust.init_logger(project="weighted_summary")
    with logger.start_span() as root_span:
        with root_span.start_span() as span:
            span.log(input="a", output="out", scores={"score1": 0.5})
        with root_span.start_span() as span:
            span.log(input="a", output="out", scores={"score2": 1})

    with logger.start_span() as root_span:
        with root_span.start_span() as span:
            span.log(input="a", output="out")
        with root_span.start_span() as span:
            span.log(input="a", output="out")

    logger.flush()
