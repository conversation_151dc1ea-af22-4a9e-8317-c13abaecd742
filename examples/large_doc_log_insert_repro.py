import braintrust

repro = [{"id": n, "type": "foo", "actions": range(10), "value": "asdf" * (200000 // 4)} for n in range(5)]

logger = braintrust.init_logger(project="Large doc")

logger.log(input=repro)
logger.flush()

# Now add this to a dataset, or load the page and run another insert. All you need is a way to
# get this record into the realtime log. If the bug reproes, you'll see the log fail to insert in
# the browser console.
