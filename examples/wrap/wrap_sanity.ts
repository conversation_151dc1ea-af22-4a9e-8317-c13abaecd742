import { OpenAI } from "openai";
import { wrap<PERSON>pen<PERSON><PERSON>, init, initLogger, traced } from "braintrust";

const openai = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);
const logger = initLogger({ projectName: "wrapper_sanity" });

async function main() {
  // First, create a completion without initializing braintrust
  const completion = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: "What is 1+1?" }],
  });
  console.log(completion.choices[0]?.message.content);

  // Then, create a completion with braintrust initialized. Don't mark it as
  // current, to keep the logger as the globally-active object.
  const experiment = init("test", { setCurrent: false });
  const input = "What is 1+1?";
  await experiment.traced(async (span) => {
    const output = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: input }],
    });
    span.log({
      input,
      output: output.choices[0]?.message.content,
      scores: { foo: 1 },
    });
  });

  console.log(await experiment.summarize());

  await traced(
    async (span) => {
      const completion = await openai.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "What is 1+1",
          },
        ],
        model: "gpt-3.5-turbo",
      });
      console.log(completion.choices[0]);
      span.log({
        input: "What is 1+1",
        output: completion.choices[0]?.message.content,
      });
    },
    {
      event: {
        metadata: { stream: false },
      },
    },
  );

  await logger.traced(
    async (span) => {
      const completion = await openai.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "What is 1+1",
          },
        ],
        model: "gpt-3.5-turbo",
      });
      console.log(completion.choices[0]);
      span.log({
        input: "What is 1+1",
        output: completion.choices[0]?.message.content,
      });
    },
    {
      event: {
        metadata: { stream: false },
      },
    },
  );

  await logger.traced(
    async () => {
      const completionStreaming = await openai.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "What is 1+1",
          },
        ],
        model: "gpt-3.5-turbo",
        stream: true,
      });
      for await (const part of completionStreaming) {
        console.log(part.choices[0]?.delta?.content || "");
      }
    },
    {
      event: {
        input: "What is 1+1",
        metadata: { stream: true },
      },
    },
  );
}

main();
