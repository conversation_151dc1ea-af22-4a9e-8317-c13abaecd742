import braintrust
import openai

openai = braintrust.wrap_openai(openai)
logger = braintrust.init_logger(project="wrapper_sanity")


@braintrust.traced
def test_input(input):
    return (
        openai.ChatCompletion.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": input}])
        .choices[0]
        .message.content
    )


if __name__ == "__main__":
    completion = test_input("What is 1+1?")
    print(completion)
