import asyncio

import braintrust
import openai

nopenai = braintrust.wrap_openai(openai.OpenAI())
aopenai = braintrust.wrap_openai(openai.AsyncOpenAI())
logger = braintrust.init_logger(project="wrapper_sanity")


@braintrust.traced
def test_input(input):
    return (
        nopenai.chat.completions.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": input}])
        .choices[0]
        .message.content
    )


async def async_sanity():
    with logger.start_span(input="What is 2+1", metadata={"stream": False}) as span:
        completion = await aopenai.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "What is 2+1",
                }
            ],
            model="gpt-3.5-turbo",
        )
        print(completion)

    with logger.start_span(input="What is 3+1", metadata={"stream": False}) as span:
        completion = await aopenai.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "What is 3+1",
                }
            ],
            model="gpt-3.5-turbo",
            stream=True,
        )
        async for item in completion:
            print(item)


if __name__ == "__main__":
    completion = test_input("What is 1+1?")
    print(completion)

    with logger:
        completion = test_input("What is 10+1?")
        print(completion)

    experiment = braintrust.init("test")
    input = "What is 1+1?"
    with experiment.start_span() as span:
        completion = test_input(input)
        span.log(input=input, output=completion, scores={"foo": 1})
    print(experiment.summarize())

    with logger.start_span(input="What is 2+1", metadata={"stream": False}) as span:
        completion = nopenai.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "What is 2+1",
                }
            ],
            model="gpt-3.5-turbo",
        )
        print(completion)

    with logger.start_span(input="What is 3+1", metadata={"stream": False}) as span:
        completion = nopenai.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "What is 3+1",
                }
            ],
            model="gpt-3.5-turbo",
            stream=True,
        )
        for item in completion:
            print(item)

    asyncio.run(async_sanity())
