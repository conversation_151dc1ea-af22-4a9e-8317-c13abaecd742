import { Eval } from "braintrust";
import { LevenshteinScorer } from "autoevals";

function customScorer() {
  return {
    name: `double quotes "test" single quotes 'test' score`,
    score: 1,
  };
}

Eval("double quote custom scorer", {
  data: async () => {
    return [
      {
        input: "a",
        expected: "b",
        metadata: {
          foo: "bar",
        },
      },
    ];
  },
  task: () => {
    return "bar";
  },
  scores: [LevenshteinScorer, customScorer],
  metadata: {
    foo: "bar",
  },
});

Eval("double quote custom scorer", {
  data: async () => {
    return [
      {
        input: "a",
        expected: "b",
        metadata: {
          foo: "bar",
        },
      },
    ];
  },
  task: () => {
    return "bar";
  },
  scores: [LevenshteinScorer, customScorer],
  metadata: {
    foo: "bar",
  },
});
