import json
import os
import time

import braintrust

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
ITERS = 10000

if __name__ == "__main__":
    with open(os.path.join(SCRIPT_DIR, "big_log_repro.json")) as f:
        data = json.load(f)

    log = braintrust.init_logger(project="big_log_repro")
    for i in range(ITERS):
        if i % 100 == 0:
            print(i)
        with log.start_span("task") as span:
            span.log(input=data["input"])
            with span.start_span("OpenAI Completion") as subspan:
                subspan.log(input=data["openai_input"], output=data["openai_output"], metadata=data["openai_metadata"])
            span.log(output=data["output"])
