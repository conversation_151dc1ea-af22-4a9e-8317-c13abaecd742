import argparse
import json
import os
import shutil
from urllib.parse import urlparse

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
CACHE_DIR = os.path.join(SCRIPT_DIR, "cache")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Load alpaca evals.")
    parser.add_argument("--alpaca-evals-repo", type=str, required=True)
    parser.add_argument("--claude-scores", type=str, required=True)
    args = parser.parse_args()

    models = {}
    for root, dirs, fnames in os.walk(args.claude_scores):
        for fname in fnames:
            if fname == "annotations.json":
                models[root.split("/")[-1]] = os.path.join(root, fname)

    for model, path in models.items():
        with open(path, "r") as f:
            data = json.load(f)

        claude_annotations = [x for x in data if x["annotator"] == "claude"]

        if len(claude_annotations) == 0:
            continue

        repo_path = os.path.join(args.alpaca_evals_repo, "results", model, "annotations.json")

        if not os.path.exists(repo_path):
            alpaca_annotations = {}
        else:
            with open(repo_path, "r") as f:
                alpaca_annotations = {row["instruction"]: row for row in json.load(f)}

        for row in claude_annotations:
            alpaca_row = alpaca_annotations.get(row["instruction"])
            row["preference_claude"] = row.pop("preference")

            if alpaca_row:
                alpaca_row["preference_claude"] = row["preference_claude"]
            else:
                alpaca_annotations[row["instruction"]] = row.copy()

        with open(repo_path, "w") as f:
            json.dump(list(alpaca_annotations.values()), f, indent=2)
