from braintrust import Eval, current_span, traced


@traced
async def call_model(input):
    messages = dict(
        messages=[
            dict(role="system", text=input),
        ]
    )
    current_span().log(input=messages)

    # Replace this with a model call
    result = {
        "content": "China",
        "latency": 1,
        "prompt_tokens": 10,
        "completion_tokens": 2,
    }
    current_span().log(
        output=result["content"],
        metrics=dict(
            latency=result["latency"],
            prompt_tokens=result["prompt_tokens"],
            completion_tokens=result["completion_tokens"],
        ),
    )
    return result["content"]


async def run_input(input):
    return await call_model(input)


def exact_match(input, expected, output):
    return 1 if output == expected else 0


Eval(
    "My Evaluation",
    data=[dict(input="Which country has the highest population?", expected="China")],
    task=run_input,
    scores=[exact_match],
)

Eval(
    "My Evaluation",
    data=[dict(input="Which country has the highest population?", expected="China")],
    task=run_input,
    scores=[exact_match],
    experiment_name="take 2",
)


def fail_fn(input):
    raise Exception("oops")


Eval(
    "My Evaluation",
    data=[dict(input="Which country has the highest population?", expected="China")],
    task=fail_fn,
    scores=[exact_match],
    experiment_name="take fail",
)
