import argparse
import json

import braintrust
from braintrust.util import <PERSON><PERSON><PERSON><PERSON><PERSON>


def run_log(logger, row):
    logger.log(LazyValue(lambda: row, use_mutex=False))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--project", type=str, default="repro")
    parser.add_argument("--experiment", type=str, default=None)
    parser.add_argument("data", type=str, default=None)
    args = parser.parse_args()

    braintrust.login()
    experiment = braintrust.init(project=args.project, experiment=args.experiment)

    with open(args.data, "r") as f:
        try:
            data = json.load(f)
        except Exception as e:
            with open(args.data, "r") as f:
                data = [json.loads(line) for line in f if line.strip()]

    logger = braintrust.logger._state.global_bg_logger()
    normalized_rows = []
    for row in data:
        row["experiment_id"] = experiment.id
        del row["project_id"]
        del row["_xact_id"]
        del row["created"]
        row["span_parents"] = row.get("span_parents") or []
        if len(row.get("span_parents")) > 0:
            del row["tags"]

        # This forces the value to be captured properly b/c of function scope (and the lack
        # of scope in the for loop)
        run_log(logger, row)

    logger.flush()
    print(experiment.summarize())
