"""
Code for obtaining OAuth tokens for calling the Entra service.
"""

import functools
import logging
import os
from threading import Lock
from typing import Any

import requests
from dotenv import load_dotenv
from msal import PublicClientApplication

load_dotenv()

_LOGIN_AUTHORITY = "https://login.microsoftonline.com/"
EVALS_CLIENT_SECRET = os.environ.get("EVALS_CLIENT_SECRET", "")
EVALS_CLIENT_ID = os.environ.get("EVALS_CLIENT_ID", "")
EVALS_TENANT_ID = os.environ.get("EVALS_TENANT_ID", "")
ENTRA_AUTH_URL = f"https://login.microsoftonline.com/{EVALS_TENANT_ID}/oauth2/v2.0/token"
ENTRA_DEFAULT_SCOPE = os.environ.get("ENTRA_DEFAULT_SCOPE", "https://sydney.bing.com/sydney/.default")


@functools.cache
def _public_client_application() -> PublicClientApplication:  # type: ignore[unused-ignore,no-any-unimported,misc]
    assert EVALS_CLIENT_ID, "SIMPLE_EVALS_CLIENT_ID must be set"
    assert EVALS_TENANT_ID, "SIMPLE_EVALS_TENANT_ID must be set"
    return PublicClientApplication(
        client_id=EVALS_CLIENT_ID,
        authority=_LOGIN_AUTHORITY + EVALS_TENANT_ID,
    )


def _extract_token_and_log_error(response: dict[str, Any]) -> str | None:
    """Returns the access token from the response, or None if there was an error. Logs the error if there was one."""
    if "error" in response:
        logging.error(f"Error refreshing token: {response['error']}")
        return None
    if "access_token" not in response:
        logging.error("No access token or error in response")
        return None
    return response["access_token"]


class EntraToken:
    """
    Helper class for obtaining an auth token for the Sydney API. Callers should use the `EntraToken.refresh_token()` function,
    which sets the `bearer_token` field with the correct authorization key to use in the header for HTTP requests.
    This attempts to obtain a service-to-service auth token for the SimpleEvalsSydney app, but if that fails for some reason, will default
    to user-based authentication.
    """

    scope_to_token: dict[str, str] = {}
    lock = Lock()

    def __init__(self, scope: str = ENTRA_DEFAULT_SCOPE):
        self.scope = scope

    @staticmethod
    def _set_token(access_token: str, scope: str) -> None:
        EntraToken.lock.acquire()
        EntraToken.scope_to_token[scope] = "Bearer " + access_token
        EntraToken.lock.release()

    @property
    def bearer_token(self) -> str | None:
        return EntraToken.scope_to_token.get(self.scope)

    def _get_interactive_token(self) -> bool:
        logging.info("Refreshing AAD token via interactive mode for scope %s", self.scope)
        accounts = _public_client_application().get_accounts()
        result = None
        for account in accounts:
            result = _public_client_application().acquire_token_silent([self.scope], account=account)
            if result:
                break
        if not result:
            result = _public_client_application().acquire_token_interactive([self.scope])

        access_token = _extract_token_and_log_error(result)
        if access_token:
            EntraToken._set_token(access_token, self.scope)
            return True
        return False

    def _get_api_based_token(self) -> bool:
        logging.debug("Refreshing AAD token via API")

        assert ENTRA_AUTH_URL, "EVALS_SYDNEY_AUTH_URL must be set for API-based auth"
        print(ENTRA_AUTH_URL)
        result = requests.post(
            ENTRA_AUTH_URL,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            data={
                "client_id": EVALS_CLIENT_ID,
                "tenant": EVALS_TENANT_ID,
                "scope": self.scope,
                "grant_type": "client_credentials",
                "client_secret": EVALS_CLIENT_SECRET,
            },
        )
        if result.status_code != 200:
            logging.error("Failed to get token: %s", result.text)
            return False

        access_token = _extract_token_and_log_error(result.json())
        if access_token:
            EntraToken._set_token(access_token, self.scope)
            return True
        return False

    def refresh_token(self) -> bool:
        """Refreshes the token, either via API-based auth or interactive auth.

        Returns: True if successful, False otherwise.
        """
        if not EVALS_CLIENT_SECRET:
            logging.warning(
                "`EVALS_CLIENT_SECRET` is not set, will try interactive authorization. Source this variable if this was supposed to be set."
            )
        return self._get_api_based_token() if EVALS_CLIENT_SECRET else self._get_interactive_token()


if __name__ == "__main__":
    token = EntraToken()
    print(token.refresh_token())
    print(token.bearer_token)
