import argparse
import json
import sys
import threading
import time
from datetime import datetime

import braintrust
from braintrust.util import LazyValue


class LoggingStats:
    def __init__(self):
        self.total_rows = 0
        self.start_time = datetime.now()
        self.last_check_rows = 0
        self.last_check_time = self.start_time
        self.running = True

    def update_rows(self, new_total):
        self.total_rows = new_total

    def monitor(self):
        while self.running:
            time.sleep(1.0)  # Wake up roughly every second
            now = datetime.now()

            # Calculate rates
            time_diff = (now - self.last_check_time).total_seconds()
            rows_diff = self.total_rows - self.last_check_rows
            current_rate = rows_diff / time_diff if time_diff > 0 else 0

            total_time = (now - self.start_time).total_seconds()
            average_rate = self.total_rows / total_time if total_time > 0 else 0

            print(
                f"STATS: {self.total_rows} total rows, {current_rate:.1f} rows/sec current, "
                f"{average_rate:.1f} rows/sec average",
                file=sys.stderr,
            )

            # Update last check values
            self.last_check_rows = self.total_rows
            self.last_check_time = now

    def stop(self):
        self.running = False


def run_log(logger, row):
    logger.log(LazyValue(lambda: row, use_mutex=False))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--project", type=str, default="repro")
    parser.add_argument("data", nargs="*")
    parser.add_argument(
        "--flush-interval", type=int, default=10, help="Flush after this many rows. If 0, flush at the very end."
    )
    args = parser.parse_args()

    braintrust.login()
    project_logger = braintrust.init_logger(project=args.project)
    logger = braintrust.logger._state.global_bg_logger()

    # Initialize and start stats monitoring
    stats = LoggingStats()
    monitor_thread = threading.Thread(target=stats.monitor, daemon=True)
    monitor_thread.start()

    rows = 0
    for i, data_file in enumerate(args.data):
        with open(data_file, "r") as f:
            data = [json.loads(line) for line in f if line.strip()]

        for row in data:
            del row["org_id"]
            del row["project_id"]
            del row["_xact_id"]
            del row["created"]

            row["project_id"] = project_logger.project.id
            row["log_id"] = "g"

            row["span_parents"] = row.get("span_parents") or []
            if len(row.get("span_parents")) > 0 and "tags" in row:
                del row["tags"]

            run_log(logger, row)
            rows += 1
            stats.update_rows(rows)

            if args.flush_interval > 0 and rows % args.flush_interval == 0:
                logger.flush()

    print("Finished inserting logs. Now is the final flush.")
    logger.flush()
    print("DONE!", file=sys.stderr)
