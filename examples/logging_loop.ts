import * as braintrust from "braintrust";

const ITERS = 100000;

async function main() {
  const logger = braintrust.initLogger({ projectName: "logging_loop" });
  for (let i = 0; i < ITERS; ++i) {
    logger.log({
      input: "foo",
      output: "bar",
      expected: "baz",
      scores: { accuracy: 0.5 },
    });
    await new Promise((r) => setTimeout(r, 10));
    if (i % 100 == 0) {
      console.log(i);
    }
  }
}

main();
