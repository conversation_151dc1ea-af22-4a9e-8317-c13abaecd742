from braintrust import Eva<PERSON>, current_span, traced

from autoevals import LevenshteinScorer


def basic_task_error(input, hooks):
    return 1 / 0


def basic_task(input, hooks):
    return {"extracted_data": {"value": {"type": "USD", "value": 11666.67}}}


Eval(
    "basic-error",
    data=lambda: [dict(input="a", expected="b")],
    task=basic_task_error,
    scores=[LevenshteinScorer],
    experiment_name="My basic eval",
)

Eval(
    "basic",
    data=lambda: [dict(input="a", expected="b")],
    task=basic_task,
    scores=[LevenshteinScorer],
    experiment_name="My basic eval",
)
