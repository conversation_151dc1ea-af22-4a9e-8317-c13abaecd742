import { <PERSON>actuality, <PERSON><PERSON><PERSON>ein, Summary } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";

async function superScorer(args: {
  input: string;
  expected: string;
  output: string;
}) {
  return Promise.all([Factuality(args), <PERSON><PERSON><PERSON>ein(args), Summary(args)]);
}

async function main() {
  console.log(
    await <PERSON><PERSON>("Say Hi Bot", {
      data: () =>
        Array(10).fill({
          input: "<PERSON>",
          expected: "Hi David",
        }),
      task: (input: string) => "Hi " + input, // Replace with your LLM call
      scores: [Factuality, Levenshtein, Summary],
    }),
  );

  console.log(
    await <PERSON><PERSON>("Say Hi Bot", {
      data: () =>
        Array(10).fill({
          input: "<PERSON>",
          expected: "Hi David",
        }),
      task: (input: string) => "Hi " + input, // Replace with your LLM call
      scores: [superScorer],
      experimentName: "superScorer",
    }),
  );
}

main();
