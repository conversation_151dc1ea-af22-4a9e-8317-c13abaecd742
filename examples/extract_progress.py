import json
import sys


def extract_sse_events(input_filename, output_filename):
    """Extract raw SSE events."""

    events = []

    # Read and parse the JSON file
    with open(input_filename, "r") as f:
        try:
            data = json.load(f)
            if isinstance(data, list):
                events.extend(data)
            else:
                events.append(data)
        except json.JSONDecodeError:
            print(f"Warning: Failed to parse JSON file", file=sys.stderr)
            return

    # Write the SSE payloads directly to file
    with open(output_filename, "w") as f:
        for event in events:
            message = event["message"]
            if "Writing SSE event" in message:
                sse_start = message.find("event: ")
                if sse_start != -1:
                    f.write(message[sse_start:].strip() + "\n")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python extract_progress.py <input_file.json> <output_file.txt>")
        sys.exit(1)

    extract_sse_events(sys.argv[1], sys.argv[2])
