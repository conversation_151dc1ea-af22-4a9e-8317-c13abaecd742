import { OpenAI } from "openai";
import "@braintrust/proxy/types";

async function main() {
  const openai = new OpenAI({
    baseURL: `${process.env.BRAINTRUST_API_URL || "https://api.braintrust.dev"}/v1/proxy`,
    apiKey: process.env.BRAINTRUST_API_KEY,
  });

  const response = await openai.chat.completions.create({
    model: "claude-3-7-sonnet-latest",
    reasoning_effort: "high", // or null to disable
    stream: false,
    messages: [
      {
        role: "user",
        content: "How many rs in 'ferrocarril'",
      },
      {
        role: "assistant",
        content: "There are 4 letter 'r's in the word \"ferrocarril\".",
        refusal: null,
        reasoning: [
          {
            id: "",
            content:
              "To count the number of 'r's in the word 'ferrocarril', I'll just go through the word letter by letter.\n\n'ferrocarril' has the following letters:\nf-e-r-r-o-c-a-r-r-i-l\n\nLooking at each letter:\n- 'f': not an 'r'\n- 'e': not an 'r'\n- 'r': This is an 'r', so that's 1.\n- 'r': This is an 'r', so that's 2.\n- 'o': not an 'r'\n- 'c': not an 'r'\n- 'a': not an 'r'\n- 'r': This is an 'r', so that's 3.\n- 'r': This is an 'r', so that's 4.\n- 'i': not an 'r'\n- 'l': not an 'r'\n\nSo there are 4 'r's in the word 'ferrocarril'.",
          },
        ],
      },
      {
        role: "user",
        content: "How many e in what you said?",
      },
    ],
  });

  console.log(JSON.stringify(response.choices, null, 2));

  const stream = await openai.chat.completions.create({
    model: "claude-3-7-sonnet-latest",
    reasoning_effort: "high",
    stream: true,
    messages: [
      {
        role: "user",
        content: "Explain quantum entanglement in simple terms.",
      },
    ],
  });

  for await (const chunk of stream) {
    const delta = chunk.choices[0]?.delta;

    // Handle regular content
    if (delta?.content) {
      process.stdout.write(delta.content);
    }

    // Handle reasoning deltas
    if (delta?.reasoning) {
      console.log("\nReasoning step:", delta.reasoning);
    }
  }

  console.log(JSON.stringify(response.choices, null, 2));
}

main().catch(console.error);
