/**
 * OpenAI reasoning examples using Braintrust.
 *
 * This expands to cover chat completions (non-streaming + streaming)
 * and the Responses API (non-streaming + streaming) in two ways:
 *
 * 1) Via Braintrust proxy (no wrap): uses BRAINTRUST_API_KEY.
 * 2) Using wrapOpenAI (direct OpenAI): uses OPENAI_API_KEY.
 */

import { OpenAI } from "openai";
import { initLogger, wrapOpenAI } from "braintrust";
import "@braintrust/proxy/types";

async function demoProxy() {
  console.log("\n=== OpenAI via Braintrust proxy ===");
  initLogger({ projectName: "reasoning (ts - proxy)" });

  const openai = new OpenAI({
    baseURL: `${process.env.BRAINTRUST_API_URL || "https://api.braintrust.dev"}/v1/proxy`,
    apiKey: process.env.BRAINTRUST_API_KEY,
  });

  // Chat completion (non-streaming)
  console.log("\n-- chat.completions.create (non-streaming, proxy) --");
  const resp = await openai.chat.completions.create({
    model: "o4-mini",
    reasoning_effort: "high",
    stream: false,
    messages: [
      {
        role: "user",
        content: `I want you to solve this in five steps:
                        1. List all countries in South America.
                        2. Filter the list to only those that border the Pacific Ocean.
                        3. For each, find the capital city.
                        4. Estimate the population of each capital.
                        5. Rank the countries from largest to smallest capital population.`,
      },
    ],
  });
  console.log(JSON.stringify(resp.choices, null, 2));
}

async function demoWrap() {
  console.log("\n=== OpenAI with wrapOpenAI (direct) ===");
  initLogger({ projectName: "reasoning (ts - wrap)" });

  const raw = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  const client = wrapOpenAI(raw);

  console.log("\n-- responses.create (non-streaming, wrap) --");
  const response = await client.responses.create({
    model: "o4-mini",
    reasoning: { effort: "high", summary: "auto" },
    input: [
      {
        role: "user",
        content: `I want you to solve this in five steps:
                        1. List all countries in South America.
                        2. Filter the list to only those that border the Pacific Ocean.
                        3. For each, find the capital city.
                        4. Estimate the population of each capital.
                        5. Rank the countries from largest to smallest capital population.`,
      },
    ],
    text: { format: { type: "text" } },
    stream: false,
  });
  console.log("responses response:", response);

  console.log("\n-- responses.create (streaming, wrap) --");
  const rstream = await client.responses.create({
    model: "o4-mini",
    reasoning: { effort: "high", summary: "auto" },
    input: [
      {
        role: "user",
        content: `I want you to solve this in five steps:
                        1. List all countries in South America.
                        2. Filter the list to only those that border the Pacific Ocean.
                        3. For each, find the capital city.
                        4. Estimate the population of each capital.
                        5. Rank the countries from largest to smallest capital population.`,
      },
    ],
    text: { format: { type: "text" } },
    stream: true,
  });
  for await (const event of rstream) {
    if (event?.type) console.log("event:", event.type);
    if (event?.type === "response.completed") {
      console.log("final output:", event.response.output);
    }
  }
}

async function main() {
  await demoProxy();
  await demoWrap();
}

main().catch((e) => {
  console.error(e);
  process.exitCode = 1;
});
