"""
OpenAI reasoning examples using Braintrust (Python).

Covers chat completions (non-streaming + streaming) and the Responses API
(non-streaming + streaming) in two ways:

1) Via Braintrust proxy (no wrap): uses BRAINTRUST_API_KEY.
2) Using wrap_openai (direct OpenAI): uses OPENAI_API_KEY.

Run:
  python examples/reasoning/openai.py
"""

from __future__ import annotations

import json
import os
import os as _os

# Avoid shadowing the third-party `openai` package by this file name.
# Temporarily remove this directory from sys.path while importing OpenAI SDK.
import sys
import time
from typing import Any

import braintrust

_THIS_DIR = _os.path.dirname(__file__)
_REMOVED = False
if _THIS_DIR in sys.path:
    sys.path.remove(_THIS_DIR)
    _REMOVED = True
try:
    from openai import OpenAI
finally:
    if _REMOVED:
        sys.path.insert(0, _THIS_DIR)


def demo_proxy() -> None:
    print("\n=== OpenAI via Braintrust proxy ===")
    braintrust.init_logger(project="reasoning (py - proxy)")

    base_url = f"{os.getenv('BRAINTRUST_API_URL') or 'https://api.braintrust.dev'}/v1/proxy"
    client = OpenAI(base_url=base_url, api_key=os.getenv("BRAINTRUST_API_KEY"))

    # Chat completions (non-streaming)
    print("\n-- chat.completions.create (non-streaming, proxy) --")
    chat_resp = client.chat.completions.create(
        model="o4-mini",
        reasoning_effort="high",
        stream=False,
        messages=[
            {
                "role": "user",
                "content": (
                    "Solve this step by step: If a train travels 120 miles in 2 hours, "
                    "and then increases its speed by 25% for the next 3 hours, how many "
                    "total miles did it travel? Think through this carefully and show your "
                    "reasoning step by step."
                ),
            }
        ],
    )
    print("chat response:", chat_resp)


def demo_wrap() -> None:
    print("\n=== OpenAI with wrap_openai (direct) ===")
    braintrust.init_logger(project="reasoning (py - wrap)")

    client = braintrust.wrap_openai(OpenAI())

    # Responses API (non-streaming)
    print("\n-- responses.create (non-streaming, wrap, summary) --")
    response = client.responses.create(
        model="o4-mini",
        reasoning={"effort": "high", "summary": "auto"},
        input=[
            {
                "role": "user",
                "content": (
                    """I want you to solve this in five steps:
                        1. List all countries in South America.
                        2. Filter the list to only those that border the Pacific Ocean.
                        3. For each, find the capital city.
                        4. Estimate the population of each capital.
                        5. Rank the countries from largest to smallest capital population."""
                ),
            }
        ],
        text={"format": {"type": "text"}, "verbosity": "medium"},
        stream=False,
    )
    print("responses response:", response)

    # Responses API (streaming)
    print("\n-- responses.create (streaming, wrap, summary) --")
    rstream = client.responses.create(
        model="o4-mini",
        reasoning={"effort": "high", "summary": "auto"},
        input=[
            {
                "role": "user",
                "content": (
                    """I want you to solve this in five steps:
                        1. List all countries in South America.
                        2. Filter the list to only those that border the Pacific Ocean.
                        3. For each, find the capital city.
                        4. Estimate the population of each capital.
                        5. Rank the countries from largest to smallest capital population."""
                ),
            }
        ],
        text={"format": {"type": "text"}, "verbosity": "medium"},
        stream=True,
    )
    for event in rstream:
        response = getattr(event, "response", None)
        if response and getattr(event, "type", None) == "response.completed":
            print("final output:", getattr(response, "output", None))


if __name__ == "__main__":
    demo_proxy()
    demo_wrap()
