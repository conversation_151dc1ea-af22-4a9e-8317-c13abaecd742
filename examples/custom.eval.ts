import { Eval, wrap<PERSON><PERSON><PERSON><PERSON> } from "braintrust";
import OpenAI from "openai";

const client = wrapOpenAI(new OpenAI());

function validClass({ output }: { output: string }) {
  const classes = ["fruit", "vegetable", "meat"];
  return classes.includes(output.toLowerCase()) ? 1 : 0;
}

Eval(
  "Custom eval", // Replace with your project name
  {
    data: () =>
      ["tomato", "BANANA", "carrot", "Apple", "orange"].map((input) => ({
        input,
      })),
    task: async (input) => {
      const ret = await client.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "What is this? Just print the class.",
          },
          {
            role: "user",
            content: input,
          },
        ],
      });
      return ret.choices[0].message.content ?? "";
    },
    scores: [validClass],
  },
);
