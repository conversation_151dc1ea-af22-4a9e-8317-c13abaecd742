import json
import os

import redis
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT

conn = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT)

PROJECT_ID = os.environ["PROJECT_ID"]


def make_async_scoring_key():
    return json.dumps(["async_scoring_cache_keys", "project_logs", PROJECT_ID], separators=(",", ":"))


def set_async_scoring(functions, selection_rate):
    key = make_async_scoring_key()
    value = json.dumps(
        [
            dict(
                object_type="project_logs",
                object_id=PROJECT_ID,
                function_ids=functions,
                selection_rate=selection_rate,
            )
        ]
    )
    conn.set(key, value, ex=3600)


if __name__ == "__main__":
    set_async_scoring(
        functions=[
            dict(global_function="Humor"),
        ],
        selection_rate=1,
    )
