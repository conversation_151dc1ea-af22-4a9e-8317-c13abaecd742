import os

import openai
from braintrust import Eval
from braintrust.oai import wrap_openai

from autoevals import Factuality, LLMClient, init

# avoid confusing the library with the openai key, if set
del os.environ["OPENAI_API_KEY"]


AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")

if AZURE_OPENAI_ENDPOINT is None or AZURE_OPENAI_API_VERSION is None or AZURE_OPENAI_API_KEY is None:
    raise ValueError("Missing credentials.")

openai_obj = wrap_openai(
    openai.AzureOpenAI(
        azure_endpoint=AZURE_OPENAI_ENDPOINT,
        azure_deployment="gpt-4o-mini",
        api_version=AZURE_OPENAI_API_VERSION,
        api_key=AZURE_OPENAI_API_KEY,
    )
)

init(
    client=LLMClient(
        openai=openai_obj,
        complete=openai_obj.chat.completions.create,
        embed=openai_obj.embeddings.create,
        moderation=openai_obj.moderations.create,
        RateLimitError=openai.RateLimitError,
    )
)


def task(*args, **kwargs):
    return ""


async def score(input_data, expected):
    return Factuality(model="gpt-4o-mini").eval(
        output=input_data["output"],
        input=input_data["input"],
        expected=expected,
    )


Eval(
    "Factuality Example",
    data=[
        {
            "input": {
                "input": "What is the capital of France?",
                "context": ["Paris is the capital and largest city of France."],
                "output": "Tokyo has over 37 million residents in its metropolitan area.",
            },
            "expected": "Paris is the capital of France.",
        },
        {
            "input": {
                "input": "What is the population of Tokyo?",
                "context": [
                    "Tokyo is the most populous metropolitan area in the world, with over 37 million residents.",
                ],
                "output": "Tokyo's metropolitan area has a population of over 37 million people.",
            },
            "expected": "Tokyo's metropolitan area has a population of over 37 million people.",
        },
    ],
    task=task,
    scores=[score],
)
