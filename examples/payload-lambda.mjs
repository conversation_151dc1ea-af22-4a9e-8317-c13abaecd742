export const handler = awslambda.streamifyResponse(
  async (event, responseStream, context) => {
    responseStream.setContentType("text/plain");

    const gistUrl =
      "https://gist.githubusercontent.com/ankrgyl/5c0452b4b216dc07e13be84b8249a28c/raw";

    const response = await fetch(gistUrl);
    const text = await response.text();

    // Split the text into event-data pairs
    const events = text
      .trim()
      .split("\n\n")
      .filter((block) => block.length > 0)
      .map((block) => {
        const [eventLine, dataLine] = block.split("\n");
        return {
          event: eventLine.replace("event: ", ""),
          data: dataLine.replace("data: ", ""),
        };
      });

    for (const evt of events) {
      const isOk = responseStream.write(
        `event: ${evt.event}\ndata: ${evt.data}\n\n`,
      );
      if (!isOk) {
        console.error("SHOULD DRAIN");
      } else {
        console.log("IM GOOD");
      }
    }

    responseStream.end();
  },
);
