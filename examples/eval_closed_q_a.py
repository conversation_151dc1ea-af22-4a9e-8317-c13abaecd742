from braintrust import Eva<PERSON>, wrap_openai
from openai import OpenAI

from autoevals import ClosedQA

openai = wrap_openai(OpenAI())


def closed_q_a(input, output):
    # NOTE: You need to instantiate the scorer class before passing
    # arguments to it directly.
    return ClosedQA()(
        input=input,
        output=output,
        criteria="Does the submission specify whether or not it can confidently answer the question?",
    )


Eval(
    "QA bot",
    data=lambda: [
        {
            "input": "Which insect has the highest population?",
            "expected": "ant",
        },
    ],
    task=lambda input: openai.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {
                "role": "system",
                "content": "Answer the following question. Specify how confident you are (or not)",
            },
            {"role": "user", "content": "Question: " + input},
        ],
    )
    .choices[0]
    .message.content
    or "Unknown",
    scores=[closed_q_a],
)
