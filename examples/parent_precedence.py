import asyncio

from braintrust import init_logger, start_span, traced
from braintrust.logger import parent_context


async def main():
    logger = init_logger(project="parent_precedence_logs")

    # Test 1: Logger as parent
    logger.start_span(name="logger_child").end()

    # Test 2: Current span beats logger
    @traced(name="current_span")
    async def test_current():
        start_span(name="current_span_child").end()

    await test_current()

    # Test 3: Propagated parent beats logger
    parent = logger.start_span(name="propagated_parent")
    parent_str = parent.export()
    parent.end()

    with parent_context(parent_str):
        start_span(name="propagated_child").end()

    # Test 4: Current span beats propagated parent
    with parent_context(parent_str):

        @traced(name="current_span_wins")
        async def test_current_wins():
            start_span(name="current_beats_propagated").end()

        await test_current_wins()

    # Test 5: Explicit parent beats everything
    explicit = logger.start_span(name="explicit_parent")
    explicit_str = explicit.export()
    explicit.end()

    with parent_context(parent_str):

        @traced(name="ignored_current")
        async def test_explicit():
            start_span(name="explicit_child", parent=explicit_str).end()

        await test_explicit()


if __name__ == "__main__":
    asyncio.run(main())
