import asyncio
import itertools
import json
import os
from typing import Any

import braintrust
import jsonref
import pinecone
import requests
from openai import Async<PERSON>penAI
from pinecone import Pinecone
from pydantic import BaseModel

BRAINTRUST_API_KEY = os.environ.get("BRAINTRUST_API_KEY")  # Or hardcode this to your API key
OPENAI_BASE_URL = "http://localhost:8000/v1/proxy"  # You can use your own base URL / proxy

braintrust.login()  # This is optional, but makes it easier to grab the api url (and other variables) later on

client = braintrust.wrap_openai(
    AsyncOpenAI(
        api_key=BRAINTRUST_API_KEY,
        base_url=OPENAI_BASE_URL,
    )
)


def has_path(d, path):
    curr = d
    for p in path:
        if p not in curr:
            return False
        curr = curr[p]
    return True


def make_description(op):
    return f"""# {op['summary']}

{op['description']}

Params:
{"\n".join([f"- {name}: {p.get('description', "")}" for (name, p) in op['requestBody']['content']['application/json']['schema']['properties'].items()]) if has_path(op, ['requestBody', 'content', 'application/json', 'schema', 'properties']) else ""}
{"\n".join([f"- {p.get("name")}: {p.get('description', "")}" for p in op['parameters'] if p.get("name")]) if has_path(op, ['parameters']) else ""}

Returns:
{"\n".join([f"- {name}: {p.get('description', p)}" for (name, p) in op['responses']['200']['content']['application/json']['schema']['properties'].items()]) if has_path(op, ['responses', '200', 'content', 'application/json', 'schema', 'properties']) else "empty"}
"""


class Document(BaseModel):
    path: str
    op: str
    definition: Any
    description: str


async def make_embedding(doc: Document):
    return (await client.embeddings.create(input=doc.description, model="text-embedding-3-small")).data[0].embedding


def batch(iterable, size):
    it = iter(iterable)
    while True:
        chunk = tuple(itertools.islice(it, size))
        if not chunk:
            break
        yield chunk


pc = Pinecone(api_key=os.environ.get("PINECONE_API_KEY"))


async def main():
    base_spec = requests.get(
        "https://raw.githubusercontent.com/braintrustdata/braintrust-openapi/main/openapi/spec.json"
    ).json()

    # Flatten out refs so we have self-contained descriptions
    spec = jsonref.loads(jsonref.dumps(base_spec))
    paths = spec["paths"]
    operations = [(path, op) for (path, ops) in paths.items() for (op_type, op) in ops.items() if op_type != "options"]

    print("Paths:", len(paths))
    print("Operations:", len(operations))

    documents = [
        Document(path=path, op=op_type, definition=json.loads(jsonref.dumps(op)), description=make_description(op))
        for (path, ops) in paths.items()
        for (op_type, op) in ops.items()
        if op_type != "options"
    ]

    embeddings = await asyncio.gather(*[make_embedding(doc) for doc in documents])

    pc.delete_index("quickstart")
    pc.create_index(
        name="quickstart",
        dimension=1536,
        metric="cosine",
        spec=pinecone.ServerlessSpec(cloud="aws", region="us-east-1"),
        deletion_protection="disabled",
    )

    index = pc.Index("quickstart")

    for b in batch(zip(documents, embeddings), 20):
        index.upsert(
            [
                {
                    "id": f"{doc.path}-{doc.op}",
                    "values": embedding,
                    "metadata": {
                        "path": doc.path,
                        "op": doc.op,
                        "description": doc.description,
                        "definition": json.dumps(doc.definition),
                    },
                }
                for doc, embedding in b
            ]
        )


if __name__ == "__main__":
    asyncio.run(main())
