import braintrust from "braintrust";

const project = braintrust.projects.create({
  name: "RAG app",
});

const ifExists = "replace";

// For prompts, the common use case is to initialize a project, and then tweak things in the UI,
// but for tools, you want to keep pushing from your codebase into the UI.
export const qaPrompt = project.prompt.create({
  name: "QA prompt",
  model: "gpt-4o",
  // messages: [
  //   {
  //     role: "system",
  //     content:
  //       "You are a helpful assistant that can search through the library and answer questions.",
  //   },
  //   {
  //     role: "user",
  //     content: "{{{question}}}",
  //   },
  // ],
  prompt: "{{{question}}}",
  ifExists,
});
