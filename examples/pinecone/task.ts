import { <PERSON><PERSON>htein } from "autoevals";

const project = createProject("Say Hi Bot");

const task = project.task(async function myTask(input: string) {
  return "Hi " + input; // Replace with your LLM call
});

export const anotherTask = project.task(
  async function myTask(input: string) {
    return "Hi " + input; // Replace with your LLM call
  },
  {
    name: "Another task",
  },
);

project.eval({
  data: () => {
    return [
      {
        input: "Foo",
        expected: "Hi Foo",
      },
      {
        input: "Bar",
        expected: "Hello Bar",
      },
    ]; // Replace with your eval dataset
  },
  task,
  scores: [<PERSON><PERSON>htein],
});
