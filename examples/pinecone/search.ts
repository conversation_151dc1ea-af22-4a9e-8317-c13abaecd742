import { wrap<PERSON><PERSON><PERSON><PERSON> } from "braintrust";
import { OpenAI } from "openai";
import { Pinecone } from "@pinecone-database/pinecone";
import { z } from "zod";
import * as braintrust from "braintrust";

const client = wrapOpenAI(new OpenAI());

if (!process.env.PINECONE_API_KEY) {
  throw new Error("PINECONE_API_KEY is not set");
}
const pc = new Pinecone({ apiKey: process.env.PINECONE_API_KEY });
const index = pc.index<{
  definition: string;
  description: string;
  op: string;
  path: string;
}>("quickstart");

const project = braintrust.projects.create({ name: "api search bot" });

async function search({ query }: { query: string }): Promise<
  {
    definition: unknown;
    description: string;
    op: string;
    path: string;
  }[]
> {
  const response = await client.embeddings.create({
    input: query,
    model: "text-embedding-3-small",
  });
  const embedding = response.data[0].embedding;
  const queryResponse = await braintrust.currentSpan().traced(
    async (span) => {
      const result = await index.query({
        vector: embedding,
        topK: 3,
        includeMetadata: true,
      });
      span.log({ output: result });
      return result;
    },
    {
      name: "Pinecone search",
      type: "function",
    },
  );
  return queryResponse.matches.map((match) => ({
    description: "",
    op: "",
    path: "",
    ...match.metadata,
    definition:
      typeof match.metadata?.definition === "string"
        ? JSON.parse(match.metadata.definition)
        : undefined,
  }));
}

export const searchTool = project.tools.create({
  handler: search,
  parameters: z.object({
    query: z.string(),
  }),
  returns: z.array(
    z.object({
      definition: z.unknown(),
      description: z.string(),
      op: z.string(),
      path: z.string(),
    }),
  ),
  ifExists: "replace",
});
