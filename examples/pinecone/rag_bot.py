import asyncio
import json
import os
import time
from typing import List

from braintrust import init_logger, traced
from create_embeddings import Document, client
from pinecone import Pinecone
from pydantic import BaseModel, Field

logger = init_logger(project="api agent test")


class SearchResult(BaseModel):
    document: Document
    index: int
    similarity: float


class SearchResults(BaseModel):
    results: List[SearchResult]


class SearchQuery(BaseModel):
    query: str
    top_k: int = Field(default=3, le=5)


# This @traced decorator will trace this function in Braintrust
@traced
async def search(query: SearchQuery):
    print("\033[93mSearch is running...\033[0m")
    pc = Pinecone(api_key=os.environ.get("PINECONE_API_KEY"))
    index = pc.Index("quickstart")
    start_time = time.time()
    query_embedding = (
        (await client.embeddings.create(input=query.query, model="text-embedding-3-small")).data[0].embedding
    )
    print(f"\033[93mEmbedding took {time.time() - start_time} seconds\033[0m")
    results = index.query(vector=query_embedding, top_k=query.top_k, include_metadata=True)
    print(f"\033[93mQuery took {time.time() - start_time} seconds\033[0m")
    return SearchResults(
        results=[
            SearchResult(
                document=Document(
                    path=result.metadata["path"],
                    op=result.metadata["op"],
                    definition=json.loads(result.metadata["definition"]),
                    description=result.metadata["description"],
                ),
                index=i,
                similarity=result.score,
            )
            for i, result in enumerate(results.matches)
        ]
    )


SYSTEM_PROMPT = """
You are a helpful assistant that can answer questions about Braintrust, a tool for
developing AI applications. Braintrust can help with evals, observability, and prompt
development.
"""

tool_registry = {
    "search": (SearchQuery, search),
}

tools = [
    {
        "type": "function",
        "function": {
            "name": "search",
            "description": "Search for API endpoints related to the query",
            "parameters": SearchQuery.model_json_schema(),
        },
    },
]

MODEL = "gpt-4o"
MAX_TOOL_STEPS = 3


@traced
async def perform_chat_step(message, history=None):
    chat_history = list(history or [{"role": "system", "content": SYSTEM_PROMPT}]) + [
        {"role": "user", "content": message}
    ]

    for _ in range(MAX_TOOL_STEPS):
        result = (
            (
                await client.chat.completions.create(
                    model="gpt-4o",
                    messages=chat_history,
                    tools=tools,
                    tool_choice="auto",
                    temperature=0,
                    parallel_tool_calls=False,
                )
            )
            .choices[0]
            .message
        )

        chat_history.append(result)

        if not result.tool_calls:
            print("\033[93mNo tool calls found\033[0m")
            break

        tool_call = result.tool_calls[0]
        ArgClass, tool_func = tool_registry[tool_call.function.name]
        args = tool_call.function.arguments
        args = ArgClass.model_validate_json(args)
        result = await tool_func(args)

        chat_history.append({"role": "tool", "tool_call_id": tool_call.id, "content": json.dumps(result.model_dump())})
    else:
        raise Exception("Ran out of tool steps")

    return chat_history


if __name__ == "__main__":
    history = None
    while True:
        try:
            query = input("\033[94mEnter your query: \033[0m")
            history = asyncio.run(perform_chat_step(query, history))
            print(history[-1].content)
        except EOFError:
            print("\nExiting...")
            break
