import json
import os

import braintrust
import requests

BRAINTRUST_API_KEY = os.environ["BRAINTRUST_API_KEY"]

logger = braintrust.init_logger(project="function test")
project = logger.project
project_id = project.id


def _insert_function(**kwargs):
    resp = requests.post(
        f"http://localhost:8000/v1/function", json=kwargs, headers={"Authorization": f"Bearer {BRAINTRUST_API_KEY}"}
    )
    resp.raise_for_status()
    return resp.json()


CALCULATOR_CODE = """
async function handler({
    op,
    a,
    b,
  }: {
    op: "add" | "subtract" | "multiply" | "divide";
    a: number;
    b: number;
  }) {
    switch (op) {
      case "add":
        return a + b;
      case "subtract":
        return a - b;
      case "multiply":
        return a * b;
      case "divide":
        return a / b;
      default:
        throw new Error("Invalid operation");
    }
  }
"""

CALCULATOR_PARAMS = {
    "type": "object",
    "properties": {
        "op": {"type": "string", "enum": ["add", "subtract", "multiply", "divide"]},
        "a": {"type": "number"},
        "b": {"type": "number"},
    },
}

# function_record = _insert_function(
#     project_id=project_id,
#     function_data={
#         "type": "code",
#         "data": {
#             "type": "inline",
#             "runtime_context": {"runtime": "node", "version": "20"},
#             "code": CALCULATOR_CODE,
#         },
#     },
#     function_schema={
#         "parameters": CALCULATOR_PARAMS,
#         "returns": {"type": "number"},
#     },
#     name="Calculator",
#     slug="calculator",
# )

# # Try running the function with some basic arguments
# resp = braintrust.invoke(
#     project_name=project.name, slug="calculator", input={"op": "add", "a": 1, "b": 2}, stream=False
# )
# print("calculator code function")
# print(resp)

# # Try running a prompt with the tool definition, but without the tool call
# prompt_data = {
#     "prompt": {
#         "type": "chat",
#         "messages": [{"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}],
#         "tools": json.dumps(
#             [
#                 {
#                     "type": "function",
#                     "function": {
#                         "name": "calculator",
#                         "description": "A simple calculator",
#                         "parameters": CALCULATOR_PARAMS,
#                     },
#                 }
#             ]
#         ),
#     },
#     "options": {
#         "model": "gpt-4o",
#     },
# }
# prompt_record = _insert_function(
#     project_id=project_id,
#     function_data={
#         "type": "prompt",
#     },
#     prompt_data=prompt_data,
#     name="Calculator prompt",
#     slug="calculator-prompt",
# )
# resp = braintrust.invoke(project_name=project.name, slug="calculator-prompt", input={"formula": "1+2"}, stream=False)
# print("with traditional tools")
# print(resp)

# # Now try installing the structured tool instead of the traditional tool
# old_tools = prompt_data["prompt"].pop("tools")
# prompt_data["structured_tools"] = [{"type": "function", "id": function_record["id"]}]
# prompt_record = _insert_function(
#     project_id=project.id,
#     function_data={
#         "type": "prompt",
#     },
#     prompt_data=prompt_data,
#     name="Calculator prompt",
#     slug="calculator-prompt",
# )

resp = braintrust.invoke(project_name=project.name, slug="calculator-prompt", input={"formula": "1+2"}, stream=True)
for chunk in resp.copy():
    print(chunk)
print("with structured tools")
print(resp.final_value())

# # Now try to add a traditional tool and a structured tool
# prompt_data["prompt"]["messages"] = [
#     {
#         "role": "user",
#         "content": """I will give you a {{formula}}. Compute it, and tell me whether it corresponds to a winning lottery ticket.""",
#     }
# ]
# prompt_data["prompt"]["tools"] = json.dumps(
#     [
#         {
#             "type": "function",
#             "function": {
#                 "name": "lottery_checker",
#                 "description": "Call this when you want to check if a number is a winning lottery ticket",
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "number": {"type": "number"},
#                     },
#                 },
#             },
#         }
#     ]
# )

# prompt_record = _insert_function(
#     project_id=project.id,
#     function_data={
#         "type": "prompt",
#     },
#     prompt_data=prompt_data,
#     name="Calculator prompt",
#     slug="calculator-prompt",
# )

# resp = braintrust.invoke(
#     project_name=project.name, slug="calculator-prompt", input={"formula": "1+2"}, stream=False, mode="parallel"
# )
# print("with both")
# print(resp)
