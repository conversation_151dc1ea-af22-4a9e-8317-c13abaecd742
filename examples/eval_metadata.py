import openai
from braintrust import <PERSON><PERSON>, wrap_openai
from faker import Faker

from autoevals import Factuality, Levenshtein

client = wrap_openai(openai.OpenAI())


def task(input: str) -> str:
    return (
        client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": f"Describe {input} in 10-30 words"}],
        )
        .choices[0]
        .message.content
    )


fake = Faker()


def process_data():
    for row in raw_data:
        # make the input super long, like at least 50 words
        row["input"] = fake.sentence(nb_words=50)
        yield row


raw_data = [
    {
        "input": "<PERSON>",
        "expected": "<PERSON> <PERSON>",
        "metadata": {"category": "formal", "language": "English", "user_type": "returning"},
        "tags": ["greeting", "english", "formal"],
    },
    {
        "input": "<PERSON>",
        "expected": "Hi <PERSON>",
        "metadata": {"category": "formal", "language": "English", "user_type": "new"},
        "tags": ["greeting", "english", "formal", "new_user"],
    },
    {
        "input": "Juan",
        "expected": "Hi Juan",
        "metadata": {"category": "formal", "language": "Spanish", "user_type": "returning"},
        "tags": ["greeting", "spanish", "formal"],
    },
    {
        "input": "Maria",
        "expected": "Hi Maria",
        "metadata": {"category": "casual", "language": "Spanish", "user_type": "new"},
        "tags": ["greeting", "spanish", "casual", "new_user"],
    },
    {
        "input": "Bob",
        "expected": "Hi Bob",
        "metadata": {"category": "casual", "language": "English", "user_type": "returning"},
        "tags": ["greeting", "english", "casual"],
    },
    {
        "input": "Emma",
        "expected": "Hi Emma",
        "metadata": {"category": "formal", "language": "English", "user_type": "new"},
        "tags": ["greeting", "english", "formal", "new_user"],
    },
    {
        "input": "Alex",
        "expected": "Hi Alex",
        "metadata": {"category": "casual", "language": "English", "user_type": "returning"},
        "tags": ["greeting", "english", "casual"],
    },
    {
        "input": "Sofia",
        "expected": "Hi Sofia",
        "metadata": {"category": "formal", "language": "Spanish", "user_type": "new"},
        "tags": ["greeting", "spanish", "formal", "new_user"],
    },
    {
        "input": "James",
        "expected": "Hi James",
        "metadata": {"category": "casual", "language": "English", "user_type": "returning"},
        "tags": ["greeting", "english", "casual"],
    },
    {
        "input": "Ana",
        "expected": "Hi Ana",
        "metadata": {"category": "casual", "language": "Spanish", "user_type": "new"},
        "tags": ["greeting", "spanish", "casual", "new_user"],
    },
]

Eval(
    "Metadata groups",
    data=process_data,
    task=task,
    scores=[Factuality, Levenshtein],
    metadata={
        "prompt": "This is the prompt.",
        "note": "Metadata note",
        "originalRunDate": "2024-02-23T10:08:43Z",
        "runs": 55,
    },
)
