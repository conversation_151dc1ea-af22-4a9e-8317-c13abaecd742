# Strands SDK Example

A minimal example application using the strands-sdk Python package.

## Setup

```bash
mise trust
make install
```

## Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

- `BRAINTRUST_API_KEY`: Your Braintrust API key (required for telemetry)
- `BRAINTRUST_PROJECT_NAME`: Project name (defaults to "strands-example")
- `BRAINTRUST_API_URL`: Base URL for Braintrust API (defaults to "https://api.braintrust.dev", can be set to "http://localhost:8000" for local development)
- `OPENAI_API_KEY`: Your OpenAI API key (required for the LLM)

## Usage

```bash
python main.py
# or
make run
```
