import os

import braintrust
from braintrust import wrap_openai
from openai import OpenAI
from strands import Agent
from strands.models.openai import OpenAIModel
from strands.telemetry import StrandsTelemetry

# Configure environment
api_url = os.getenv("BRAINTRUST_API_URL", "https://api.braintrust.dev")
api_key = os.getenv("BRAINTRUST_API_KEY")
project_name = os.getenv("BRAINTRUST_PROJECT_NAME", "simple-comparison")

if api_url:
    os.environ["BRAINTRUST_API_URL"] = api_url
if api_key:
    os.environ["BRAINTRUST_API_KEY"] = api_key

# Initialize Braintrust
bt_project = braintrust.init_logger(project=project_name)
print(f"Using project: {project_name}")

# Configure Strands telemetry
os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = f"{api_url}/otel"
headers = []
if api_key:
    headers.append(f"authorization=Bearer {api_key}")
if project_name and bt_project:
    headers.append(f"x-bt-parent=project_name:{project_name}")
if headers:
    os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = ",".join(headers)
    print(f"OTEL headers: {os.environ['OTEL_EXPORTER_OTLP_HEADERS']}")

strands_telemetry = StrandsTelemetry()
strands_telemetry.setup_otlp_exporter()
strands_telemetry.setup_console_exporter()  # Add console output to see what's being sent

# Tool definition
from strands import tool


@tool
def word_count(text: str) -> int:
    """Count words in text."""
    return len(text.split())


# Setup models with identical parameters
model_params = {
    "model_id": "gpt-4o-mini",
    "params": {
        "max_tokens": 100,
        "temperature": 0.5,
    },
}

# Strands setup
strands_model = OpenAIModel(client_args={}, **model_params)
strands_agent = Agent(
    model=strands_model, tools=[word_count], system_prompt="You are a helpful assistant. When using tools, be concise."
)

# OpenAI tool definition
word_count_tool = {
    "type": "function",
    "function": {
        "name": "word_count",
        "description": "Count words in text",
        "parameters": {
            "type": "object",
            "properties": {"text": {"type": "string", "description": "The text to count words in"}},
            "required": ["text"],
        },
    },
}


def call_openai_with_tools(prompt):
    """Call OpenAI with tool capability"""
    response = openai_client.chat.completions.create(
        model=model_params["model_id"],
        max_tokens=model_params["params"]["max_tokens"],
        temperature=model_params["params"]["temperature"],
        messages=[
            {"role": "system", "content": "You are a helpful assistant. When using tools, be concise."},
            {"role": "user", "content": prompt},
        ],
        tools=[word_count_tool],
    )

    message = response.choices[0].message

    # Handle tool calls if present
    if message.tool_calls:
        for tool_call in message.tool_calls:
            if tool_call.function.name == "word_count":
                import json

                args = json.loads(tool_call.function.arguments)
                result = word_count(args["text"])
                print(f"Tool call: word_count({args['text']}) = {result}")

        # For simplicity, just return the assistant's response with tool calls
        return f"Tool used: {message.content or 'Called word_count tool'}"

    return message.content


# OpenAI setup
openai_client = OpenAI()
openai_client = wrap_openai(openai_client)

# Tool-using prompt
prompt = "Count the words in this phrase: 'The quick brown fox jumps'"

print("\n" + "=" * 50)
print("STRANDS CALL")
print("=" * 50)
strands_response = strands_agent(prompt)
print(f"Response: {strands_response}")

print("\n" + "=" * 50)
print("WRAP_OPENAI CALL")
print("=" * 50)
openai_response = call_openai_with_tools(prompt)
print(f"Response: {openai_response}")

print("\nBoth calls completed. Check Braintrust UI for comparison.")
