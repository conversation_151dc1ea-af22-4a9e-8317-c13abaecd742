import os

import braintrust
from strands import Agent, tool
from strands.models.openai import OpenAIModel
from strands.telemetry import StrandsTelemetry

# Configure Braintrust OTEL exporter
api_url = os.getenv("BRAINTRUST_API_URL", "https://api.braintrust.dev")
os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = f"{api_url}/otel"
api_key = os.getenv("BRAINTRUST_API_KEY")
project_name = os.getenv("BRAINTRUST_PROJECT_NAME", "strands-example")  # Default project name

# Initialize Braintrust project to ensure it exists
try:
    bt_project = braintrust.init(project=project_name)
    print(f"Using Braintrust project: {project_name}")
except Exception as e:
    print(f"Warning: Could not initialize Braintrust project: {e}")
    bt_project = None

headers = []
if api_key:
    headers.append(f"authorization=Bearer {api_key}")
if project_name and bt_project:
    headers.append(f"x-bt-parent=project_name:{project_name}")

if headers:
    os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = ",".join(headers)
    print(f"Configured OTEL headers for Braintrust export")
else:
    print("Warning: Braintrust OTEL export not configured - missing API key or project")

strands_telemetry = StrandsTelemetry()
strands_telemetry.setup_otlp_exporter()
strands_telemetry.setup_console_exporter()  # Also print to console for demo

model = OpenAIModel(
    client_args={},
    model_id="gpt-4o",
    params={
        "max_tokens": 1000,
        "temperature": 0.7,
    },
)


@tool
def word_count(text: str) -> int:
    """Count words in text.

    This docstring is used by the LLM to understand the tool's purpose.
    """
    return len(text.split())


agent = Agent(
    model=model,
    tools=[word_count],
    system_prompt="You are a helpful assistant. When using tools, be concise and just report the result without repeating the input text.",
)

questions = [
    "Hello! Can you help me analyze some text?",
    "Count the words in this phrase: 'The quick brown fox jumps'",
    "Now count the words in this longer text: 'This is a much longer sentence with many more words for testing our word counting tool'",
]

for i, question in enumerate(questions, 1):
    print("-" * 50)
    print(question)
    response = agent(question)
    print("")
