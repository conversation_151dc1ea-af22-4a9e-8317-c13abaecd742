import os

from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from strands import Agent
from strands.models.litellm import LiteLLMModel
from strands_tools import calculator

# Configure environment
project_name = os.getenv("BRAINTRUST_PROJECT_NAME", "calculator-example")

# Set up environment variables for Braintrust
os.environ.setdefault("BRAINTRUST_PARENT", f"project_name:{project_name}")
print(f"Using project: {project_name}")

# Configure the global OTel tracer provider
provider = TracerProvider()
trace.set_tracer_provider(provider)

# Send spans to Braintrust using the built-in span processor
provider.add_span_processor(BraintrustSpanProcessor())

model = LiteLLMModel(model_id="openai/gpt-4o")

agent = Agent(
    model=model,
    system_prompt="You are an AI agent. Help answer questions with the tools at your disposal.",
    tools=[calculator],
)

result = agent("What is 123987 * 23498234?")
print(f"Result: {result}")
