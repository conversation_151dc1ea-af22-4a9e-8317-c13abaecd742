import os

import braintrust
from braintrust import wrap_openai
from openai import OpenAI

# Use same environment setup as strands example
api_url = os.getenv("BRAINTRUST_API_URL", "https://api.braintrust.dev")
api_key = os.getenv("BRAINTRUST_API_KEY")
project_name = os.getenv("BRAINTRUST_PROJECT_NAME", "strands-example")  # Same project as strands

# Set environment variables for Braintrust (same as strands example)
if api_url:
    os.environ["BRAINTRUST_API_URL"] = api_url
if api_key:
    os.environ["BRAINTRUST_API_KEY"] = api_key

# Initialize Braintrust project
bt_project = braintrust.init_logger(project=project_name)
print(f"Using Braintrust project: {project_name}")
print(f"Using Braintrust API URL: {api_url}")

# Initialize OpenAI client and wrap it with Braintrust
client = OpenAI()
client = wrap_openai(client)


def word_count(text: str) -> int:
    """Count words in text."""
    return len(text.split())


# Tool definition for OpenAI
word_count_tool = {
    "type": "function",
    "function": {
        "name": "word_count",
        "description": "Count words in text",
        "parameters": {
            "type": "object",
            "properties": {"text": {"type": "string", "description": "The text to count words in"}},
            "required": ["text"],
        },
    },
}


def call_openai_with_tools(messages):
    """Call OpenAI with tool capability, handling tool calls"""
    response = client.chat.completions.create(
        model="gpt-4o", messages=messages, tools=[word_count_tool], temperature=0.7, max_tokens=1000
    )

    message = response.choices[0].message

    # Handle tool calls if present
    if message.tool_calls:
        # Add assistant message with tool calls to conversation
        messages.append({"role": "assistant", "content": message.content, "tool_calls": message.tool_calls})

        # Execute each tool call
        for tool_call in message.tool_calls:
            if tool_call.function.name == "word_count":
                import json

                args = json.loads(tool_call.function.arguments)
                result = word_count(args["text"])

                # Add tool result to conversation
                messages.append({"role": "tool", "tool_call_id": tool_call.id, "content": str(result)})

        # Get final response after tool execution
        final_response = client.chat.completions.create(
            model="gpt-4o", messages=messages, temperature=0.7, max_tokens=1000
        )
        return final_response.choices[0].message.content

    return message.content


# Same system prompt as Strands example
system_message = {
    "role": "system",
    "content": "You are a helpful assistant. When using tools, be concise and just report the result without repeating the input text.",
}

# Same questions as Strands example
questions = [
    "Hello! Can you help me analyze some text?",
    "Count the words in this phrase: 'The quick brown fox jumps'",
    "Now count the words in this longer text: 'This is a much longer sentence with many more words for testing our word counting tool'",
]

# Track conversation history
conversation = [system_message]

for i, question in enumerate(questions, 1):
    print("-" * 50)
    print(question)

    # Add user message to conversation
    conversation.append({"role": "user", "content": question})

    # Get response
    response = call_openai_with_tools(conversation.copy())
    print(response)

    # Add assistant response to ongoing conversation
    conversation.append({"role": "assistant", "content": response})
    print("")
