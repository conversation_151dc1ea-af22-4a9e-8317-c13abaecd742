import uuid

import braintrust
from braintrust import init_logger
from braintrust.util import LazyValue

if __name__ == "__main__":
    project = init_logger("no end")

    logger = braintrust.logger._state.global_bg_logger()
    row = {
        "id": uuid.uuid4().hex,
        "input": "hello",
        "metrics": {"foo": 1},
        "project_id": project.project.id,
        "log_id": "g",
    }
    logger.log(LazyValue(lambda: row, use_mutex=False))
    logger.flush()

    # span = logger.start_span("test")
    # span.log(input="hello", output="world")
    # exit(0)
