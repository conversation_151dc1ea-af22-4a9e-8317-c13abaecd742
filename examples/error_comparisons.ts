import { init, type Experiment } from "braintrust";

async function run() {
  const data = [
    [
      {
        span: {
          input: "valid input",
          output: "valid output",
          scores: {
            my_score: 0.5,
            other_score: Math.random(),
          },
          metadata: {
            hello: "foo",
          },
        },
      },
      {
        span: {
          input: "valid input",
          output: "valid output",
          scores: {
            my_score: 0.75,
            other_score: Math.random(),
          },
          metadata: {
            hello: "foo",
          },
        },
      },
    ],
    [
      {
        span: {
          input: "score failed",
          output: "output",
          scores: {
            my_score: 0.5,
            other_score: Math.random(),
          },
          metadata: {
            hello: "foo",
          },
        },
        willFail: true,
      },
      {
        span: {
          input: "score failed",
          output: "output",
          scores: {
            my_score: 0.75,
            other_score: Math.random(),
          },
          metadata: {
            hello: "foo",
          },
        },
        willFail: true,
      },
    ],
    [
      {
        willFail: true,
      },
      {
        willFail: true,
      },
    ],
    [
      {
        span: {
          input: "matched fail",
        },
        willFail: true,
      },
      {
        span: {
          input: "matched fail",
        },
        willFail: true,
      },
    ],
    [
      {
        span: {
          input: "unmatched fail 1",
        },
        willFail: true,
      },
      {
        span: {
          input: "unmatched fail 1",
        },
      },
    ],
    [
      {
        span: {
          input: "unmatched fail 2",
        },
      },
      {
        span: {
          input: "unmatched fail 2",
        },
        willFail: true,
      },
    ],
    [
      {
        span: {
          input: "valid input e1",
          output: "output",
        },
      },
      {
        skip: true,
      },
    ],
    [
      {
        span: {
          input: "error e1",
          scores: {
            my_score: 0.24,
            other_score: Math.random(),
          },
        },
        willFail: true,
      },
      {
        skip: true,
      },
    ],
  ];

  async function runData(exp: Experiment, index: number) {
    await Promise.allSettled(
      data.map(async (d) => {
        if ("skip" in d[index]) {
          return;
        }
        await exp.traced(async (span) => {
          if ("span" in d[index] && d[index].span) {
            span.log(d[index].span);
          }
          if ("willFail" in d[index] && d[index].willFail) {
            throw new Error("I will fail");
          }
        });
      }),
    );
  }

  runData(init("errors"), 0);
  runData(init("errors"), 1);
}

run();
