import random

import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("metadata-breakdown-repro-non-matching-inputs", "main-experiment")

    scores = list(range(5))
    for i in range(20):
        random.shuffle(scores)
        experiment.log(
            input=i,
            output=2,
            scores={
                **{f"retrieved_doc_urls_{i}": random.random() for i in scores},
            },
            metadata={
                "this": "this",
                "that": "that",
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("metadata-breakdown-repro-non-matching-inputs", "matching-experiment")
    for i in range(5):
        random.shuffle(scores)
        experiment.log(
            input=i,
            output=2,
            scores={
                **{f"retrieved_doc_urls_{j}": (None if i < 2 else random.random()) for j in scores},
            },
            metadata={
                "this": "this",
                "that": "that",
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("metadata-breakdown-repro-non-matching-inputs", "semi-overlapping-experiment")
    for i in range(14, 26):
        random.shuffle(scores)
        experiment.log(
            input=i,
            output=i,
            scores={
                **{f"retrieved_doc_urls_{j}": (None if i % 5 == 0 else random.random()) for j in scores},
            },
            metadata={
                "this": "this",
                "that": "that",
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("metadata-breakdown-repro-non-matching-inputs", "no-matching-inputs")
    for i in range(5):
        random.shuffle(scores)
        experiment.log(
            input=f"non-matching-{i}",
            output=2,
            scores={
                **{f"retrieved_doc_urls_{j}": random.random() for j in scores},
            },
            metadata={
                "this": "this",
                "that": "that",
            },
        )

    print(experiment.summarize())
