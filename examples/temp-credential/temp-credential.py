import os

import requests

PROXY_URL = os.getenv("BRAINTRUST_PROXY_URL", "https://braintrustproxy.com/v1")
# Braintrust API key starting with `sk-...`.
BRAINTRUST_API_KEY = os.getenv("BRAINTRUST_API_KEY")


def main():
    response = requests.post(
        f"{PROXY_URL}/credentials",
        headers={
            "Authorization": f"Bearer {BRAINTRUST_API_KEY}",
        },
        json={
            # Leave unset to allow all models.
            "model": "gpt-4o-realtime-preview-2024-10-01",
            # TTL for starting the request. Once started, the request can stream
            # for as long as needed.
            "ttl_seconds": 60 * 10,  # 10 minutes.
            "logging": {
                "project_name": "My project",
            },
        },
    )

    if response.status_code != 200:
        raise Exception(f"Failed to request temporary credentials: {response.text}")

    temp_credential = response.json().get("key")
    print(f"Authorization: Bearer {temp_credential}")


if __name__ == "__main__":
    main()
