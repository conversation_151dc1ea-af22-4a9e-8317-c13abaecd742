import { OpenAI } from "openai";
const client = new OpenAI({
  baseURL: "https://api.braintrust.dev/v1/proxy",
  apiKey: "YOUR_TEMPORARY_API_KEY",
  // It is safe to store temporary credentials in the browser because they have
  // limited lifetime and access.
  dangerouslyAllowBrowser: true,
});

async function main() {
  const response = await client.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [{ role: "user", content: "What is a proxy?" }],
  });
  console.log(response.choices[0].message.content);
}

main();
