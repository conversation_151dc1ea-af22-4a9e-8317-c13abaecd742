import { createOpenAI } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { initLogger, wrapAISDKModel } from "braintrust";
import { z } from "zod";

const openai = createOpenAI({
  baseURL: process.env.OPENAI_BASE_URL || "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.OPENAI_API_KEY,
  compatibility: "strict",
});
initLogger({ projectName: "ai-sdk-retries" });
async function main() {
  const model = wrapAISDKModel(openai.chat("gpt-4o"));
  const response = await model.doGenerate({
    inputFormat: "messages",
    mode: {
      type: "regular",
    },
    prompt: [
      {
        role: "user",
        content: [{ type: "text", text: "What is the capital of France?" }],
      },
    ],
  });
  console.log(response.text);

  const { object } = await generateObject({
    model,
    schema: z.object({
      recipe: z.object({
        name: z.string(),
        ingredients: z.array(z.string()),
        steps: z.array(z.string()),
      }),
    }),
    prompt: "Generate a lasagna recipe.",
  });

  console.log(object);
}

main().catch(console.error);
