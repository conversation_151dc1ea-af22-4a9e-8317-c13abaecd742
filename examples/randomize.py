import argparse
import json
import random
import sys

import pyarrow as pa
import pyarrow.parquet as pq
from faker import Faker

fake = Faker()


def deepcopy(d):
    return json.loads(json.dumps(d))


def randomize(d, full):
    if isinstance(d, dict):
        keys = list(d.keys())
        if len(keys) == 0:
            return d

        randomize_keys = random.sample(keys, k=len(keys) if full else random.randint(1, len(keys)))
        for k in randomize_keys:
            d[k] = randomize(d[k], full)
        return d
    elif isinstance(d, list):
        indices = range(len(d))

        if len(indices) == 0:
            return d

        randomize_indices = random.sample(indices, k=(len(indices) if full else random.randint(1, len(indices))))
        for i in randomize_indices:
            d[i] = randomize(d[i], full)
        return d
    elif isinstance(d, str):
        return fake.text(max_nb_chars=max(5, len(d)))
    elif isinstance(d, int):
        if d == 0 or d == 1:
            return random.choice([0, 1])

        return random.randint(0, 100000000)
    elif isinstance(d, float):
        return random.random()
    elif isinstance(d, bool):
        return random.randint(1, 2) == 2
    else:
        return d


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("-n", "--num", type=int, default=9)
    parser.add_argument(
        "--full",
        action="store_true",
        help="Randomize every single field (instead of a random set",
    )
    parser.add_argument("--seed", type=int, default=4321)
    args = parser.parse_args()

    Faker.seed(args.seed)
    random.seed(args.seed)

    for line in sys.stdin:
        data = json.loads(line)
        all_rows = [deepcopy(data) for _ in range(args.num)]
        all_rows = [randomize(d, args.full) for d in all_rows]
        print(line.strip())
        for row in all_rows:
            print(json.dumps(row))
