# Log Retention Script

This script manages log retention by deleting old logs from both PostgreSQL and ClickHouse databases based on specified criteria. You can view the latest version of the script [here](https://gist.github.com/ankrgyl/2978c88c1505b18a3542dc52a803ae51).

## Environment Variables

Required environment variables:

- `PG_URL`: PostgreSQL connection string for the main database
- `CLICKHOUSE_PG_URL` (optional): ClickHouse connection string. If not provided, ClickHouse deletion will be skipped

## Usage

```bash
uv run apply_retention.py [options]
```

### Options

- `--object-id` (required): The object ID whose logs to delete (format: `type:uuid`)
- `--older-than` (required): Delete logs older than this many days
- `--target-records` (required): Maximum number of records to delete in one iteration
- `--iterations` (default: 1): Maximum number of iterations to run
- `--min-sequence-id` (optional): Minimum sequence ID to start from
- `--dry-run`: If set, only print what would be deleted without actually deleting

### Examples

1. Dry run to preview deletion (90 days retention, 20k records per batch):

```bash
uv run apply_retention.py \
    --object-id global_log:41d8234a-0127-4c9d-a39a-348705066ccf \
    --target-records 20000 \
    --older-than 90 \
    --iterations 1 \
    --dry-run
```

2. Execute deletion with multiple iterations:

```bash
uv run apply_retention.py \
    --object-id global_log:41d8234a-0127-4c9d-a39a-348705066ccf \
    --target-records 20000 \
    --older-than 90 \
    --iterations 1000 \
    --min-sequence-id 13330334
```

### Sample Output

```
[15/100] Deleting 20000 logs in Postgres (sequence id 13330334->13350334), from 2024-05-01 05:04:23 to 2024-05-01 08:27:25
-- Deleted 20000 logs in 16.76 seconds from Postgres (1193.34 logs/s)
[15/100] Deleting 8306 logs in Clickhouse (up to sequence id 13350334), from 2024-05-01 05:04:23 to 2024-05-01 08:27:25
-- Deleted 8306 logs in 1.76 seconds from Clickhouse (4718.08 logs/s)
```

## How It Works

1. The script finds a range of sequence IDs to delete based on the specified criteria
2. It first deletes the logs from PostgreSQL within the sequence ID range
3. If ClickHouse connection is configured, it then deletes corresponding logs from ClickHouse
4. The process repeats for the specified number of iterations

## Performance Considerations

- Use `--target-records` to control the batch size. Smaller batches are safer but slower
- The script reports deletion speed in logs/second for both databases
- Use `--min-sequence-id` to resume deletion from a specific point if needed
- `--dry-run` is recommended before running actual deletions
