import argparse
import os
import time
from datetime import datetime, timedelta, timezone

from dotenv import load_dotenv
from psycopg2 import connect


def find_sequence_id_range(object_id, min_sequence_id, cutoff_date, max_records, conn, dialect):
    # First, find the minimum sequence_id for this object
    query = f"""
        SELECT min(sequence_id) from logs WHERE
            make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) = '{object_id}'
            {f"AND sequence_id > {min_sequence_id}" if min_sequence_id is not None else ""}
    """
    conn.execute(query)
    row = conn.fetchone()
    if not row:
        return None, None  # No records found

    start_sequence_id = row[0]
    total_records = 0
    last_sequence_id = int(start_sequence_id)

    while total_records < max_records:
        # Query for records within our window that are older than cutoff_date
        query = f"""
            SELECT MAX(sequence_id), MAX({"row_created" if dialect == "postgres" else "created_dt"})::text, COUNT(*)
            FROM logs
            WHERE
                sequence_id >= {last_sequence_id} + 1
                AND sequence_id < {last_sequence_id + max_records - total_records}
                AND make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) = '{object_id}'
                AND {"row_created" if dialect == "postgres" else "created_dt"} < '{cutoff_date.isoformat()}'
        """
        conn.execute(query)
        rows = conn.fetchall()

        row = rows[0]

        # Update our counts and position
        num_found = int(row[2])
        if num_found == 0:
            break

        total_records += num_found
        last_sequence_id = int(row[0])

    if total_records == 0:
        return None, None

    return int(start_sequence_id), int(last_sequence_id)


if __name__ == "__main__":
    load_dotenv()

    parser = argparse.ArgumentParser(description="Delete old logs from a specified project")

    parser.add_argument("--object-id", required=True, help="The object ID whose logs to delete from")
    parser.add_argument("--older-than", type=int, required=True, help="Delete logs older than this many days")
    parser.add_argument(
        "--target-records", type=int, required=True, help="Maximum number of records to delete in one iteration"
    )
    parser.add_argument("--iterations", type=int, default=1, help="Maximum number of iterations to run (default: 1)")
    parser.add_argument("--min-sequence-id", type=int, default=None, help="Minimum sequence ID to start from")
    parser.add_argument(
        "--dry-run", action="store_true", help="If set, only print what would be deleted without actually deleting"
    )

    args = parser.parse_args()

    min_sequence_id = args.min_sequence_id
    for i in range(args.iterations):
        # Calculate the cutoff date
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=args.older_than)

        conn = connect(os.getenv("PG_URL"))
        cursor = conn.cursor()
        sequence_id_range = find_sequence_id_range(
            args.object_id, min_sequence_id, cutoff_date, args.target_records, cursor, "postgres"
        )
        min_sequence_id = sequence_id_range[1]

        if sequence_id_range[0] is None:
            print("No logs to delete")
            exit(0)

        cursor.execute(
            f"SELECT COUNT(*), MIN(row_created), MAX(row_created) FROM logs WHERE sequence_id >= {sequence_id_range[0]} AND sequence_id <= {sequence_id_range[1]} AND make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) = '{args.object_id}'"
        )
        row = cursor.fetchone()
        num_records = row[0]
        min_created = row[1]
        max_created = row[2]

        print(
            f"[{i+1}/{args.iterations}] Deleting {num_records} logs in Postgres (sequence id {sequence_id_range[0]}->{sequence_id_range[1]}), from {min_created} to {max_created}"
        )
        if not args.dry_run:
            start_time = time.time()
            cursor.execute(
                f"""
            DELETE FROM logs
            WHERE sequence_id >= {sequence_id_range[0]} AND sequence_id <= {sequence_id_range[1]} AND make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) = '{args.object_id}'
            AND row_created < '{cutoff_date.isoformat()}'
            """
            )
            conn.commit()
            end_time = time.time()
            logs_per_second = num_records / (end_time - start_time) if end_time - start_time > 0 else 1
            print(
                f"-- Deleted {num_records} logs in {end_time - start_time} seconds from Postgres ({logs_per_second:.2f} logs/s)"
            )

        if "CLICKHOUSE_PG_URL" not in os.environ:
            continue

        clickhouse_conn = connect(os.getenv("CLICKHOUSE_PG_URL"))
        clickhouse_cursor = clickhouse_conn.cursor()

        clickhouse_cursor.execute(
            f"SELECT COUNT(*), MIN(created_dt), MAX(created_dt) FROM logs WHERE object_id = '{args.object_id}' AND sequence_id <= {sequence_id_range[1]}"
        )
        row = clickhouse_cursor.fetchone()
        ch_num_records = int(row[0])
        ch_min_created = row[1]
        ch_max_created = row[2]

        print(
            f"[{i+1}/{args.iterations}] Deleting {ch_num_records} logs in Clickhouse (up to sequence id {sequence_id_range[1]}), from {ch_min_created} to {ch_max_created}"
        )
        if not args.dry_run:
            start_time = time.time()
            clickhouse_cursor.execute(
                f"""
                DELETE FROM logs WHERE object_id = '{args.object_id}' AND sequence_id <= {sequence_id_range[1]}
            """
            )
            clickhouse_conn.commit()
            end_time = time.time()
            logs_per_second = ch_num_records / (end_time - start_time) if end_time - start_time > 0 else 1
            print(
                f"-- Deleted {ch_num_records} logs in {end_time - start_time} seconds from Clickhouse ({logs_per_second:.2f} logs/s)"
            )
