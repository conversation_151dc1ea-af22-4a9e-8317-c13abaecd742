import random

import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("mixmatched-scores-metadata")

    for i in range(4):
        experiment.log(
            input=f"input{i}",
            output=2,
            scores={
                "random_0_score-1": random.random() if i % 4 > 1 else 0,
                "random_0_score-2": random.random() if i % 2 == 0 else 0,
                "random_none_score-1": random.random() if i % 4 > 1 else None,
                "random_none_score-2": random.random() if i % 2 == 0 else None,
                "stable_score": 0.5,
                "null_comparison_score": None if i == 0 else 1,
            },
            metadata={
                "first": "here" if i == 3 else "not here",
                "all": "yep",
                "unique1": "1",
                "second": {
                    "secondkey": "secondkey" if i % 3 == 0 else None,
                },
                "null_comparison": "in" if i == 0 else "out",
                # to test the overlapping edge case
                "duration": "duration",
            },
        )

    print(experiment.summarize())

    experiment = braintrust.init("mixmatched-scores-metadata")
    for i in range(4):
        experiment.log(
            input=f"input{i}",
            output=2,
            scores={
                "random_0_score-1": random.random() if i % 4 > 1 else 0,
                "random_0_score-2": random.random() if i % 2 == 0 else 0,
                "random_none_score-1": random.random() if i % 4 > 1 else None,
                "random_none_score-2": random.random() if i % 2 == 0 else None,
                "stable_score": 0.5,
                "null_comparison_score": 1,
            },
            metadata={
                "first": "here" if i == 3 else "not here",
                "all": "yep",
                "unique2": "2",
                "second": {
                    "secondkey": "secondkey" if i % 3 == 0 else None,
                },
                "null_comparison": "in",
                # to test the overlapping edge case
                "duration": "duration",
            },
        )

    print(experiment.summarize())
