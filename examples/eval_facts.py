from braintrust import Eval, Score
from braintrust.logger import current_span


def task(input, hooks):
    for flavor in ["chocolate", "vanilla", "strawberry"]:
        hooks.tags.append(flavor)
    return "1, 2"


def fact_scorer(output, expected):
    for i, fact in enumerate(expected["facts"]):
        with current_span().start_span(name=f"fact_{i}") as span:
            span.log(scores={"Fact finder": 1 if str(fact) in output else 0})
    return []


Eval(
    "Facts Extraction",
    data=[{"input": "What is the capital of the moon?", "expected": {"facts": [1, 2, 3]}}],
    task=task,
    scores=[fact_scorer],
)
