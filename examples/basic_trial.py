import braintrust
from faker import Faker

fake = Faker()

INPUT_COUNT = 50
TRIALS = 10

if __name__ == "__main__":
    name = fake.name()
    data = [(fake.sentence(), fake.sentence()) for _ in range(INPUT_COUNT)]
    prev_exp_id = None
    for _ in range(2):
        exp = braintrust.init("multi-way-trial", name)
        for i in range(INPUT_COUNT):
            sentence, expected = data[i]
            for j in range(TRIALS):
                with exp.start_span("foo") as span:
                    output = fake.sentence()

                    with span.start_span("llm") as llm_span:
                        llm_span.log(input="PLEASE GENERATE A SENTENCE FROM\n" + sentence, output=output)

                    span.log(
                        input={"question": sentence},
                        output=fake.sentence(),
                        expected=expected,
                        scores={
                            "closeness": fake.random.random(),
                            "correctness": fake.random.random(),
                        },
                        metadata={
                            "input": i,
                            "trial": j,
                        },
                    )

        print(exp.summarize(comparison_experiment_id=prev_exp_id))
        prev_exp_id = exp.id
