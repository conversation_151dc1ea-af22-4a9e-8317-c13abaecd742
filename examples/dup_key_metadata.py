import json

import braintrust

logger = braintrust.init_logger("dup key meta")
logger.log(
    input=1,
    output=2,
    metadata=json.loads(
        '{"Created By": {"type": "schema", "tagName": "schema-property-created-by", "children": [], "attributes": {"name": "Created By"}}, "Created by": {"type": "schema", "tagName": "schema-property-created-by", "children": [], "attributes": {"name": "Created by"}}}'
    ),
)
