# Example usage:
# cat ../scratch/small-data.json | python load_dataset.py zapier small --output-field expected --metadata-field meta

import argparse
import json
import sys

import braintrust

if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("project_name")
    parser.add_argument("dataset_name", default=None)
    parser.add_argument("--input-field", default="input")
    parser.add_argument("--output-field", default="output")
    parser.add_argument("--metadata-field", default="metadata")
    args = parser.parse_args()

    dataset = braintrust.init_dataset(args.project_name, args.dataset_name)
    input_field, output_field, metadata_field = (None, None, None)

    i = 0
    for line in sys.stdin:
        data = json.loads(line)
        if input_field is None:
            # Assume all rows have the same schema
            if args.input_field in data:
                input_field = args.input_field
            else:
                raise ValueError(f"Input field {args.input_field} not found in row")

            if args.output_field in data:
                output_field = args.output_field
            else:
                raise ValueError(f"Output field {args.output_field} not found in row")

            if args.metadata_field in data:
                metadata_field = args.metadata_field
            else:
                raise ValueError(f"Metadata field {args.metadata_field} not found in row")

        input, output, metadata = (
            data[input_field],
            data[output_field],
            data[metadata_field],
        )
        dataset.log(input=input, output=output, metadata=metadata)
