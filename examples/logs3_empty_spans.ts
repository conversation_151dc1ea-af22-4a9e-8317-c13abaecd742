import { v4 as uuidv4 } from "uuid";

const project_id = "cc9e2700-3bec-4b3c-8211-274ced8693ed";
const user_id = "ffb3ba6d-bce3-4864-b78e-e7177775fc56";
const parentId = uuidv4();
const childId = uuidv4();

async function makeLog(id: string, parents?: string[]) {
  const res = await fetch("http://127.0.0.1:8000/logs3", {
    method: "POST",
    body: JSON.stringify({
      token: process.env.BRAINTRUST_API_KEY,
      data: {
        rows: [
          {
            _is_merge: false,
            metrics: { random_metric: null },
            project_id,
            log_id: "g",
            id,
            user_id,
            created: new Date().toISOString(),
            span_id: id,
            root_span_id: parents ? parents[0] : id,
            ...(parents ? { span_parents: parents } : undefined),
            _audit_source: "app",
            input: id,
            _audit_metadata: {
              user_id,
            },
          },
        ],
        api_version: 2,
      },
    }),
  });

  console.log("done", id);
  return res;
}
await makeLog(parentId);
await makeLog(childId, [parentId]);
console.log("parent:", parentId, "child:", childId);
