import { openai } from "@ai-sdk/openai";
import { generateText } from "ai";

export async function POST(req: Request) {
  const { prompt } = await req.json();

  const { text } = await generateText({
    model: openai("gpt-4o-mini"),
    maxTokens: 20,
    prompt,
    experimental_telemetry: {
      isEnabled: true,
      functionId: "berry",
      metadata: { example: "cherry" },
    },
  });

  return new Response(JSON.stringify({ text }), {
    headers: { "Content-Type": "application/json" },
  });
}
