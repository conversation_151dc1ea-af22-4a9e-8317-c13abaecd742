{"name": "otel-demo-app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ai-sdk/openai": "^0.0.14", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.53.0", "@opentelemetry/core": "^1.19.0", "@opentelemetry/exporter-trace-otlp-http": "^0.52.1", "@opentelemetry/exporter-trace-otlp-proto": "^0.52.1", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/otlp-transformer": "^0.53.0", "@opentelemetry/sdk-logs": "^0.53.0", "@opentelemetry/sdk-trace-base": "^1.19.0", "@opentelemetry/sdk-trace-node": "^1.26.0", "@opentelemetry/semantic-conventions": "^1.27.0", "@vercel/otel": "^1.10.0", "ai": "3.4.1", "dotenv": "^16.3.1", "next": "^15.1.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/ajv": "^1.0.0", "@types/react": "^19.0.2", "@typescript-eslint/eslint-plugin": "^6.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "jsdom": "^25.0.0", "npm-run-all": "^4.1.5", "tailwindcss": "^3.3.2", "tsup": "^8.4.0", "typescript": "^5.0.4"}}