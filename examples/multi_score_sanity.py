from braintrust import <PERSON><PERSON>

from autoevals import Factuality, <PERSON><PERSON><PERSON><PERSON>, Summary


async def super_scorer(*args, **kwargs):
    futures = [
        Factuality().eval_async(*args, **kwargs),
        Levenshtein().eval_async(*args, **kwargs),
        Summary().eval_async(*args, **kwargs),
    ]
    return [await f for f in futures]


# <PERSON><PERSON>(
#     "Say Hi Bot",
#     data=lambda: [
#         {
#             "input": "<PERSON>",
#             "expected": "<PERSON> <PERSON>",
#         },
#     ]
#     * 10,
#     task=lambda input: "Hi " + input,  # Replace with your LLM call
#     scores=[Factuality, Levenshtein, Summary],
# )

Eval(
    "Say Hi Bot",
    data=lambda: [
        {
            "input": "<PERSON>",
            "expected": "<PERSON> David",
        },
    ]
    * 10,
    task=lambda input: "Hi " + input,  # Replace with your LLM call
    scores=[super_scorer],
    experiment_name="super_scorer",
)
