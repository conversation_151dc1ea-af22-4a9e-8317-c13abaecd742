import argparse
import random
import time

from braintrust import init_experiment


def sanity_test():
    ## SANITY TEST
    experiment = init_experiment("slow_experiment")
    print("The first test just adds a few scores. The initial state is empty.")
    print("Follow along at:")
    print(experiment.summarize().experiment_url + "?c=")

    print("\n\n")

    print("Inserting a row with no scores...")
    experiment.log(input="No scores", output="Nothing", scores={"foo": 0} if args.same_scores else {})

    _ = input("Press Enter to continue...")

    print("Inserting a row with score 'foo'...")
    experiment.log(input="Scores", output="foo", scores={"foo": 0})

    _ = input("Press Enter to continue...")

    print("Inserting a row with score 'bar'...")
    experiment.log(input="Scores", output="bar", scores={"foo": 0} if args.same_scores else {"bar": 0})


def same_row_test():
    print("\n\nThe next test keeps updating the same row, but adds a new score each time.")
    experiment = init_experiment("slow_experiment")
    summary = experiment.summarize()
    print("Follow along at:", summary.experiment_url + "?c=")

    print("\n\n")
    id = "foo"
    print("Inserting a row with no score")
    experiment.log(id=id, input=id, output=id, scores={})

    _ = input("Press Enter to continue...")

    print("Updating the row with score 'foo'...")
    experiment.log(id=id, input=id, output=id, scores={"foo": 0})

    _ = input("Press Enter to continue...")

    print("Updating the row with score 'bar'. Foo should disappear...")
    experiment.log(id=id, input=id, output=id, scores={"bar": 0})

    _ = input("Press Enter to continue...")

    print("Updating the row with multiple scores...")
    experiment.log(id=id, input=id, output=id, scores={"foo": 0, "bar": 0, "baz": 0})


def comparison_test():
    print(
        "\n\nThe next test is a comparison test. It will create two experiments, with mostly overlapping rows, and update them both simultaneously."
    )
    experiment1 = init_experiment("comparison_experiment")
    experiment2 = init_experiment("comparison_experiment")

    experiment1_url = experiment1.summarize().experiment_url
    print("Follow along at:")
    print(experiment1_url + "?c=" + experiment2.name)

    print("Inserting a row with no score into the first experiment...")
    experiment1.log(input="foo", output="foo", scores={})
    experiment1.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a score 'foo' into the second experiment...")
    experiment2.log(input="foo", output="foo", scores={"foo": 0.5})
    experiment2.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a score 'bar' into the first experiment...")
    experiment1.log(input="bar", output="bar", scores={"bar": 0})
    experiment1.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a score 'baz' into the second experiment...")
    experiment2.log(input="bar", output="bar", scores={"baz": 0})
    experiment2.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a score 'boo' into the first experiment...")
    experiment1.log(input="boo", output="boo", scores={"boo": 0})
    experiment1.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a hgigher score 'boo' into the second experiment...")
    experiment2.log(input="boo", output="boo", scores={"boo": 0.5})
    experiment2.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a score 'asdf' into the second experiment...")
    experiment2.log(input="asdf", output="asdf", scores={"asdf": 0})
    experiment2.flush()
    _ = input("Press Enter to continue...")

    print("Inserting a row with a higher score 'asdf' into the first experiment...")
    experiment1.log(input="asdf", output="asdf", scores={"asdf": 0.5})
    experiment1.flush()


def stress_test():
    print("\n\nThe next test is a stress test. It will insert 100 rows with random scores.")
    experiment = init_experiment("slow_experiment")
    summary = experiment.summarize()
    print("Follow along at:", summary.experiment_url + "?c=")
    _ = input("Press Enter to start...")

    scores = [
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
    ]
    for i in range(100):
        score_set = random.sample(scores, random.randint(1, 5))
        experiment.log(
            input=f"Stress {i}", output=f"Stress {i}", scores={score: random.random() for score in score_set}
        )
        time.sleep(0.1)


if __name__ == "__main__":
    test_map = {"sanity": sanity_test, "same_row": same_row_test, "comparison": comparison_test, "stress": stress_test}

    parser = argparse.ArgumentParser()
    parser.add_argument("--same-scores", action="store_true")
    parser.add_argument(
        "--tests",
        nargs="+",
        choices=test_map.keys(),
        help="Specify which tests to run. If not specified, all tests will run.",
    )
    args = parser.parse_args()

    tests_to_run = args.tests if args.tests else test_map.keys()
    for test_name in tests_to_run:
        print(f"\nRunning {test_name} test...")
        test_map[test_name]()
