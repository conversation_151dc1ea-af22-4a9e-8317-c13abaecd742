import argparse
import json

import braintrust

if __name__ == "__main__":
    braintrust.login()
    parser = argparse.ArgumentParser()
    parser.add_argument("object_type", type=str)
    parser.add_argument("object_id", type=str)
    parser.add_argument("--normalize-for-brainstore", action="store_true")
    args = parser.parse_args()

    response = braintrust.logger.api_conn().post(
        "/btql",
        json={
            "query": f"select: * | from: {args.object_type}('{args.object_id}')",
            "use_brainstore": True,
            "fmt": "jsonl",
        },
        allow_redirects=True,
    )

    response_data = response.text

    if args.normalize_for_brainstore:
        rows = []
        for line in response_data.split("\n"):
            if line.strip():
                row = json.loads(line)
                del row["_pagination_key"]
                rows.append(row)

        response_data = "\n".join(json.dumps(row) for row in rows)

    with open(f"{args.object_type}_{args.object_id}.jsonl", "w") as f:
        f.write(response_data)
