import random
import time
from math import ceil

import braintrust
import openai

logger = braintrust.init_logger(project="log openai")
client = braintrust.wrap_openai(openai.OpenAI())

from braintrust import init_logger

logger = init_logger(project="log openai")

scores = [0, 0.5, 1]
models = ["gpt-3.5-turbo", "gpt-4o-mini"]
bools = [True, False]


def chance(choices):
    return choices[ceil(random.random() * 10) % len(choices)]


if __name__ == "__main__":
    for i in range(10):
        model = chance(models)
        score = chance(scores)
        testing = chance(bools)

        with logger.start_span(
            name="root",
            tags=["random"],
            metadata={"testing": testing, "random": random.random()},
        ) as span:
            print(i)

            print(
                client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "user", "content": "Tell me a story about braintrustdata in less than 10 words"}
                    ],
                    temperature=1,
                )
            )

            span.log(input="foo", output="bar", expected="baz", scores=dict(accuracy=score))

            time.sleep(random.random() / 5 * 10)
