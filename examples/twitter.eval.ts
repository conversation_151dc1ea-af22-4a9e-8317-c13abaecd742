import { Eval } from "braintrust";

const tweetIds = ["1839455435251916921"];

Eval("Twitter rewrite", {
  data: tweetIds.map((id) => ({ input: id })),
  task: async (input) => {
    return "foo";
  },
  scores: [],
});

Eval("Table data", {
  data: [
    {
      input: [
        {
          id: 1,
          username: "user1",
          tweet: "Hello, world!",
          likes: 10,
          retweets: 2,
        },
        {
          id: 2,
          username: "user2",
          tweet: "Just another day!",
          likes: 15,
          retweets: 3,
        },
        {
          id: 3,
          username: "user3",
          tweet: "Coding is fun!",
          likes: 20,
          retweets: 5,
        },
        {
          id: 4,
          username: "user4",
          tweet: "Beautiful weather today!",
          likes: 8,
          retweets: 1,
        },
        {
          id: 5,
          username: "user5",
          tweet: "Learning something new!",
          likes: 12,
          retweets: 4,
        },
      ],
    },
  ],
  task: async (input) => {
    return "foo";
  },
  scores: [],
});
