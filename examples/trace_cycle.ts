import { initLogger } from "braintrust";
import { v4 as uuidv4 } from "uuid";

const logger = initLogger({ projectName: "trace cycles" });
async function logCycle() {
  logger.traced(
    async (rootSpan) => {
      const cycleStartSpanId = uuidv4();
      const cycleEndSpanId = uuidv4();
      const cycleStartSpan = rootSpan.startSpanWithParents(
        cycleStartSpanId,
        [cycleEndSpanId],
        {
          name: "cycle_start",
        },
      );

      cycleStartSpan.startSpan({
        name: "cycle_end",
        spanId: cycleEndSpanId,
        parentSpanIds: {
          spanId: cycleStartSpanId,
          rootSpanId: rootSpan.spanId,
        },
      });

      const selfCycleSpanId = uuidv4();
      rootSpan.startSpanWithParents(selfCycleSpanId, [selfCycleSpanId], {
        name: "self_cycle",
      });

      console.log("root id:", rootSpan.id, "root_span_id:", rootSpan.spanId);
    },
    {
      name: `root_${Math.floor(Math.random() * 100)}`,
      event: { input: "root" },
    },
  );
}

await logCycle();

await logger.flush();
