#!/usr/bin/env python
"""Simple example demonstrating text file attachments in Braintrust"""

import braintrust
from braintrust import Attachment

# Initialize Braintrust
braintrust.login()

# Create a simple text attachment from string content
text_content = """This is a simple text attachment example.

It demonstrates how to create and log text attachments in Braintrust.
You can include multiple lines, special characters (!@#$%^&*),
and even Unicode: 你好世界 🎉

This text will be viewable directly in the Braintrust UI."""

text_attachment = Attachment(filename="example_note.txt", content_type="text/plain", data=text_content.encode("utf-8"))

# Create another text attachment from a file
with open("sample_log.txt", "w") as f:
    f.write(
        """2024-01-15 10:00:00 - Application started
2024-01-15 10:00:05 - Connected to database
2024-01-15 10:00:10 - Processing user request
2024-01-15 10:00:15 - Request completed successfully
2024-01-15 10:00:20 - Cleaning up resources"""
    )

file_attachment = Attachment(
    filename="sample_log.txt", content_type="text/plain", data="sample_log.txt"  # Can pass file path instead of bytes
)

# Create a dataset with text attachments
dataset = braintrust.init_dataset(project="text-attachment-demo", name="text-samples")

# Insert multiple examples with text attachments
dataset.insert(
    input={
        "instruction": "Summarize this text",
        "document": Attachment(
            filename="document.txt",
            content_type="text/plain",
            data="Machine learning is a subset of artificial intelligence...".encode("utf-8"),
        ),
    },
    expected={"summary": "ML is a branch of AI focused on learning from data"},
)

dataset.insert(
    input={
        "instruction": "Extract key points",
        "meeting_notes": Attachment(
            filename="meeting_notes.txt",
            content_type="text/plain",
            data="""Meeting Notes - Q1 Planning
- Increase revenue by 20%
- Launch new product line
- Expand to 3 new markets""".encode(
                "utf-8"
            ),
        ),
    },
    expected={"key_points": ["20% revenue increase", "New product launch", "Market expansion"]},
)

print("✅ Successfully created text attachments in dataset!")
print(dataset.summarize())

# Cleanup
import os

if os.path.exists("sample_log.txt"):
    os.remove("sample_log.txt")
