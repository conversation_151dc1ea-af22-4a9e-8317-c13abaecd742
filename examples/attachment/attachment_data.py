import os
from typing import Any, Dict, Iterable

from braintrust import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Eval<PERSON>ase

from autoevals import NumericDiff


def load_pdfs() -> Iterable[EvalCase[Dict[str, Any], int]]:
    for filename in ["example.pdf"]:
        yield EvalCase(
            input={
                "file": Attachment(
                    filename=filename,
                    content_type="application/pdf",
                    # The file on your filesystem or the file's bytes.
                    data=os.path.join("files", filename),
                )
            },
            # This is a toy example where we check that the file size is what we expect.
            expected=469513,
        )


def get_file_size(input: Dict[str, Any]) -> int:
    return len(input["file"].data)


# Our evaluation uses a `NumericDiff` scorer to check the file size.
Eval(
    "attachments test",
    data=load_pdfs(),
    task=get_file_size,
    scores=[NumericDiff],
)
