import os
from typing import Any, Dict

from braintrust import Attach<PERSON>, Eval, init_dataset

from autoevals import NumericDiff


def create_pdf_dataset() -> None:
    dataset = init_dataset("Project with PDFs", "My PDF Dataset")
    for filename in ["example.pdf"]:
        dataset.insert(
            input={
                "file": Attachment(
                    filename=filename,
                    content_type="application/pdf",
                    # The file on your filesystem or the file's bytes.
                    data=os.path.join("files", filename),
                )
            },
            # This is a toy example where we check that the file size is what we expect.
            expected=469513,
        )
    dataset.flush()


def get_file_size(input: Dict[str, Any]) -> int:
    """Download the attachment and get its length."""
    return len(input["file"].data)


# First create a dataset with attachments.
create_pdf_dataset()

# Later, we can refer to the dataset in an experiment. Upon access, the
# attachment data will be automatically downloaded from Braintrust.
Eval(
    "Project with PDFs",
    data=init_dataset("Project with PDFs", "My PDF Dataset"),
    task=get_file_size,
    scores=[NumericDiff],
)
