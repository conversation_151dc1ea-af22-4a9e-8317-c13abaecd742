import { NumericDiff } from "autoevals";
import { Attachment, initDataset, <PERSON>l } from "braintrust";
import path from "node:path";

async function createPdfDataset(): Promise<void> {
  const dataset = initDataset({
    project: "Project with PDFs",
    dataset: "My PDF Dataset",
  });
  for (const filename of ["example.pdf"]) {
    dataset.insert({
      input: {
        file: new Attachment({
          filename,
          contentType: "application/pdf",
          data: path.join("files", filename),
        }),
      },
    });
  }
  await dataset.flush();
}
async function getFileSize(input: { file: Attachment }): Promise<number> {
  return (await input.file.data()).size;
}

// First create a dataset with attachments.
createPdfDataset();

// Later, we can refer to the dataset by name to load it from Braintrust.
Eval("Project with PDFs", {
  data: initDataset({
    project: "Project with PDFs",
    dataset: "My PDF Dataset",
  }),
  task: getFileSize,
  scores: [NumericDiff],
});
