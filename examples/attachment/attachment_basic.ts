// A sample program to insert several types of attachment.

import { Attachment, initLogger } from "braintrust";

const logger = initLogger({
  projectName: "project with attachments",
  asyncFlush: true,
});

async function main() {
  const image = new Attachment({
    data: "examples/attachment/chaos.jpg",
    filename: "attachment.jpg",
    contentType: "image/jpeg",
  });

  const audio = new Attachment({
    data: "examples/attachment/strawberry.wav",
    filename: "model response.wav",
    contentType: "audio/wav",
  });

  logger.log({
    input: {
      question: "What is this?",
      context: image,
    },
    output: {
      text: "text response",
      audio: audio,
    },
  });

  logger.log({
    input: {
      question: "What is this?",
      context: new Attachment({
        data: "examples/attachment/landscape.jpg",
        filename: "user_input.jpg",
        contentType: "image/jpeg",
      }),
    },
    output: "Example response.",
  });

  const id = logger.log({
    input: {
      question: "What is this?",
      context: [image, image, "text", { some: "object", attachment: image }],
      legacy_context: {
        "0": "https://picsum.photos/seed/a/200.jpg",
        "1": {
          image_url: "https://picsum.photos/seed/a/200.jpg",
        },
      },
    },
    output: {
      text: "text response",
      audio: audio,
      audio2: audio,
    },
    metadata: {
      comment: "Multiple identical attachments",
    },
  });

  logger.logFeedback({
    id,
    scores: {
      metric1: 0.5,
      metric2: 1.0,
    },
    expected: audio,
    metadata: {
      comment2: "attachment as top level field",
    },
  });

  logger.log({
    input: {
      raster: new Attachment({
        data: "examples/attachment/red_dot.png",
        filename: "red_dot.png",
        contentType: "image/png",
      }),
      vector: new Attachment({
        data: "examples/attachment/red_dot.svg",
        filename: "red_dot.svg",
        contentType: "image/svg+xml",
      }),
      pdf: new Attachment({
        data: "examples/attachment/smart_pointer_interview_question.pdf",
        filename: "Smart Pointer Interview Question.pdf",
        contentType: "application/pdf",
      }),
    },
  });

  logger.log({
    input: new Attachment({
      data: "app/package.json",
      filename: "package.json",
      contentType: "application/json",
    }),
  });

  console.log("[main] flushing");
  await logger.flush();
}

main();
