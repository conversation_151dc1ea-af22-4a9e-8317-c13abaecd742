import * as braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({
  name: "Tools test",
});

async function calculator({
  op,
  a,
  b,
}: {
  op: "add" | "subtract" | "multiply" | "divide";
  a: number;
  b: number;
}) {
  process.stdout.write("process stdout write!");
  let result: number;
  switch (op) {
    case "add":
      result = a + b;
      break;
    case "subtract":
      result = a - b;
      break;
    case "multiply":
      result = a * b;
      break;
    case "divide":
      result = a / b;
      break;
    default:
      throw new Error("Invalid operation");
  }
  braintrust.currentSpan().log({ metadata: { foo: "bar" } });
  console.error("Returning result", result);
  return [
    {
      op,
      description: `Result of ${a} ${op} ${b}`,
      definition: result,
    },
  ];
}

project.tools.create({
  handler: calculator,
  name: "Calculator",
  ifExists: "replace",
  description:
    "A simple calculator that can add, subtract, multiply, and divide.",
  parameters: z.object({
    op: z.enum(["add", "subtract", "multiply", "divide"]),
    a: z.number(),
    b: z.number(),
  }),
  returns: z.array(
    z.object({
      definition: z.number(),
      description: z.string(),
      op: z.string(),
    }),
  ),
});
