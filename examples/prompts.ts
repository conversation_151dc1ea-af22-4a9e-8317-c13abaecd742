import { OpenAI } from "openai";
import { initLogger, loadPrompt, wrap<PERSON>penA<PERSON> } from "braintrust";

initLogger({ projectName: "your project name" });

// wrapOpenAI will make sure the client tracks usage of the prompt.
const client = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);

export async function runPrompt() {
  // Replace with your project name and slug
  const prompt = await loadPrompt({
    projectName: "your project name",
    slug: "your prompt slug",
    defaults: {
      // Parameters to use if not specified
      model: "gpt-3.5-turbo",
      temperature: 0.5,
    },
  });

  // Render with parameters
  return client.chat.completions.create(
    prompt.build({
      question: "1+1",
    }),
  );
}

export async function flavors() {
  const prompt = await loadPrompt({
    projectName: "your project name",
    slug: "your prompt slug",
    defaults: {
      // Parameters to use if not specified
      model: "gpt-3.5-turbo",
      temperature: 0.5,
    },
  });

  const chatParams = prompt.build(
    {
      question: "1+1",
    },
    {
      // This is the default
      flavor: "chat",
    },
  );

  const completionParams = prompt.build(
    {
      input: "1+1",
    },
    {
      // Pass "completion" to get completion-shaped parameters
      flavor: "completion",
    },
  );

  // Render with parameters
  client.chat.completions.create(chatParams);
  client.completions.create(completionParams);
}
