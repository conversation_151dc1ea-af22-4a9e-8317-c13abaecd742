# This script helps you test out that summary queries work on the experiment page.
# Open up the random_score project, and then:
#
# 1. Run this script a few times.
# 1a. Every few runs, refresh the page manually
# 2. Delete all experiments
# 3. Refresh the page
# 4. Run it once more
# 5. Refresh the page
#
# There should be no errors.

import braintrust
import faker

fake = faker.Faker()

if __name__ == "__main__":
    experiment = braintrust.init("random_score")
    experiment.log(input=1, output=2, scores={fake.word(): 0.5})
    print(experiment.summarize())
