import argparse
import os
import time

import braintrust
import requests


def load_experiment(id):
    top_level_fields = [
        "id",
        "created",
        "project_id",
        "experiment_id",
        "input",
        "output",
        "expected",
        "error",
        "scores",
        "metadata",
        "tags",
        "metrics",
        "context",
        "span_id",
        "span_parents",
        "root_span_id",
        "span_attributes",
        "is_root",
        "origin",
    ]

    deferred_fields = ["input", "output", "expected", "metadata", "error", "context", "origin"]

    api_key = os.environ["BRAINTRUST_API_KEY"]
    start = time.time()
    response = braintrust.logger.api_conn().post(
        "/btql",
        json={
            "query": {
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["experiment"]},
                    "args": [{"op": "literal", "value": id}],
                },
                "select": [
                    {
                        "alias": f,
                        "expr": {"btql": f if f not in deferred_fields else f"is_root ? {f} : null"},
                    }
                    for f in top_level_fields
                ],
                "filter": {"op": "literal", "value": True},
            },
            "use_match_search_index": False,
            "api_version": 1,
            "fmt": "parquet",
            "disable_limit": True,
            # "overflow_results": False,
        },
        allow_redirects=False,
    )
    print(f"Response time: {time.time() - start}")
    start = time.time()

    if not response.ok:
        print(response.content)
        raise Exception(f"Request failed: {response.status_code}")

    with open("../scratch/foo.data", "wb") as f:
        f.write(response.content)

    print(f"Write time: {time.time() - start}")


if __name__ == "__main__":
    braintrust.login()

    parser = argparse.ArgumentParser()
    parser.add_argument("id")
    args = parser.parse_args()
    load_experiment(args.id)
