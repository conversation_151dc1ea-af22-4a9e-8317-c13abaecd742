import argparse
import csv
import json
import sys

import braintrust


def try_json(s):
    try:
        return json.loads(s)
    except:
        return s


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("project_name")
    parser.add_argument("dataset_name", default=None)
    parser.add_argument("--input-field", default="input")
    parser.add_argument("--output-field", default="output")
    parser.add_argument("--metadata-field", default="metadata")
    args = parser.parse_args()

    dataset = braintrust.init_dataset(args.project_name, args.dataset_name)
    input_field, output_field, metadata_field, extra_metadata_fields = (
        None,
        None,
        None,
        [],
    )

    reader = csv.reader(sys.stdin)
    header = next(reader)

    for i, field in enumerate(header):
        if field == args.input_field:
            input_field = i
        elif field == args.output_field:
            output_field = i
        elif field == args.metadata_field:
            metadata_field = i
        else:
            extra_metadata_fields.append(i)

    if input_field is None:
        raise ValueError(f"Input field {args.input_field} not found in header")
    if output_field is None:
        raise ValueError(f"Output field {args.output_field} not found in header")
    if metadata_field is None:
        raise ValueError(f"Metadata field {args.metadata_field} not found in header")

    for line in reader:
        input, output, metadata = (
            try_json(line[input_field]),
            try_json(line[output_field]),
            try_json(line[metadata_field]),
        )
        extra_metadata = {header[i]: try_json(line[i]) for i in extra_metadata_fields}
        metadata.update(extra_metadata)

        dataset.log(input=input, output=output, metadata=metadata, allow_large=True)
