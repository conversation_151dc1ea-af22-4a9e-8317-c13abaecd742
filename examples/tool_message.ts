import { initLogger, wrap<PERSON>penA<PERSON> } from "braintrust";
import { OpenAI } from "openai";
import { ChatCompletionTool } from "openai/resources";

const client = wrapOpenAI(
  new OpenAI({
    defaultHeaders: { "x-bt-use-cache": "always" },
  }),
);

initLogger({ projectName: "tool calls" });

async function main() {
  const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
    { role: "system", content: "You are a bot." },
    { role: "user", content: "What is the weather in San Francisco?" },
  ];
  const tools: ChatCompletionTool[] = [
    {
      type: "function",
      function: {
        name: "get_current_weather",
        description: "Get the current weather",
        parameters: {
          type: "object",
          properties: {
            location: {
              type: "string",
              description: "The city and state, e.g. San Francisco, CA",
            },
            format: {
              type: "string",
              enum: ["celsius", "fahrenheit"],
              description:
                "The temperature unit to use. Infer this from the users location.",
            },
          },
          required: ["location", "format"],
        },
      },
    },
  ] as const;

  let response = await client.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages,
    tools,
  });

  console.log(JSON.stringify(response, null, 2));

  messages.push(response.choices[0].message);
  messages.push({
    role: "tool",
    content: "30 degrees Fahrenheit",
    tool_call_id: response.choices[0].message.tool_calls![0].id,
  });
  messages.push({
    role: "user",
    content: "Is this cold?",
  });

  response = await client.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages,
    tools,
  });

  console.log(JSON.stringify(response, null, 2));
}

main();
