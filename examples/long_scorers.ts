import { Eval, currentSpan, traced } from "braintrust";

function customScorerShort(name: string) {
  return () => {
    return {
      name: `a_${name}`,
      score: Math.random(),
    };
  };
}

function customScorerLong(name: string) {
  return () => {
    return {
      name: `looooooooooooooooooooooooooooooooooong_${name}`,
      score: Math.random(),
    };
  };
}

Eval("long_scorers", {
  data: async () => {
    return [
      {
        input: "a",
        expected: "b",
      },
    ];
  },
  task: (_, { span }) => {
    span.log({ metadata: { message: "In task" } });
    return traced(
      () => {
        currentSpan().log({ metadata: { message: "In foo" } });
        return "bar";
      },
      { name: "foo" },
    );
  },
  scores: [
    customScorerShort("1"),
    customScorerShort("2"),
    customScorerShort("3"),
    customScorerShort("4"),
    customScorerShort("5"),
    customScorerShort("6"),
    customScorerShort("7"),
    customScorer<PERSON>hort("8"),
    customScorer<PERSON>ong("9"),
    customScorer<PERSON>ong("10"),
    customScorerLong("11"),
    customScorerLong("12"),
  ],
  experimentName: "My basic eval",
  metadata: {
    foo: "bar",
  },
});
