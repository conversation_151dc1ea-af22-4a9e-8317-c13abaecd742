import { login } from "braintrust";
async function main() {
  const state = await login();

  const project_id = "d0bd3b3c-20b8-46c5-8bb9-d4a3f489bf77";
  const xact_id = undefined;
  const batch_size = 100;
  const format = "parquet";
  const bucket = "btql-export";
  const key_prefix = "results/";

  const result = await state.apiConn().post("/trigger-btql-export", {
    org_id: state.orgId!,
    project_id,
    automation_id: "123",
    xact_id,
    batch_size,
    format,
    bucket,
    key_prefix,
  });

  const resp = await result.json();
  console.log(resp);
}

main().catch((error) => {
  console.error(`Unhandled error: ${error}`);
  process.exit(1);
});
