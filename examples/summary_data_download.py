import argparse
import os
import sys
import time

import braintrust
import requests


def humanize_bytes(size):
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} PB"


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--experiment-id", type=str, required=True)
    parser.add_argument("--fmt", type=str, default="parquet", choices=["parquet", "json"])
    parser.add_argument("--no-brainstore", action="store_true")
    args = parser.parse_args()

    braintrust.login()
    conn = braintrust.logger.api_conn()
    start = time.time()
    api_key = os.environ["BRAINTRUST_API_KEY"]

    # res = requests.post(
    #     "https://staging-api.braintrust.dev/btql",
    res = conn.post(
        "/btql",
        json={
            "query": f"from: experiment('{args.experiment_id}') summary | select: *",
            "fmt": args.fmt,
            "use_brainstore": not args.no_brainstore,
            "disable_limit": True,
        },
        headers={"Accept-Encoding": "gzip", "Authorization": f"Bearer {api_key}"},
        stream=True,
    )
    if not res.ok:
        print(res.text, file=sys.stderr)
        res.raise_for_status()
    data = res.raw.read()

    with open(f"summary_data.{args.fmt}", "wb") as f:
        f.write(data)

    print("Summary data size:")
    print(humanize_bytes(len(data)))
    print(f"Time taken: {time.time() - start:.2f} seconds")
