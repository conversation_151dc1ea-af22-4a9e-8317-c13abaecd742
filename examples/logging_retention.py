import datetime
import random
import sys
import time

import braintrust
from braintrust_local.api_db_util import log3_raw

global_xact_id = 0


def get_xact_id_from_datetime(dt: datetime):
    global global_xact_id

    # adapted from api-ts/src/xact_id.ts to generate time-traveling xact_ids
    ts = int(dt.timestamp())
    counter = global_xact_id & 0xFFFF  # mask to 16 bits
    result = str((0x0DE1 << 48) | ((ts & 0xFFFFFFFFFFFF) << 16) | counter)
    global_xact_id += 1
    return result


def make_timetravel_logger(*args):
    class TimeTravelLogger:
        def __init__(self, logger):
            self.project_id = logger.project.id
            self._logger = logger

        def _log(self, *args, **kwargs):
            return self._logger.log(*args, **kwargs)

        def log(self, **kwargs):
            dt = datetime.datetime.now()
            if "backdate" in kwargs:
                dt = dt - kwargs["backdate"]
                del kwargs["backdate"]

            return log3_raw(
                dict(
                    project_id=self.project_id,
                    _bt_internal_override_xact_id=get_xact_id_from_datetime(dt),
                    log_id="g",
                    **kwargs,
                )
            )

    return TimeTravelLogger(*args)


# Log backdated rows so you can test retention policies
if __name__ == "__main__":
    logger = make_timetravel_logger(braintrust.init_logger(project="retention"))

    while True:
        i = random.randint(10000, 99999)

        now = datetime.datetime.now()
        days = random.choice([0, 1, 7, 30, 90, 365])
        if days != 0:
            delta = datetime.timedelta(days=days, hours=1)
            input = f"{i} ({delta.days} ago)"
        else:
            delta = None
            input = f"{i} (now)"

        output = "bar" if i % 2 == 0 else "foo"
        if i % 3 == 0:
            expected = "bar"
        elif i % 3 == 1:
            expected = "baz"
        else:
            expected = "quux"

        kwargs = dict(
            input=input,
            output=output,
            expected=expected,
        )
        if delta:
            kwargs["backdate"] = delta

        logger.log(**kwargs)
        print(delta, input, output, expected)
        time.sleep(1)
