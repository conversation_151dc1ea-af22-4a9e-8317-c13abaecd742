import argparse
import math
import random

import braintrust
import tik<PERSON>en
from faker import <PERSON>aker


def generate_n_tokens(tokenizer, fake, tokens):
    words = fake.sentence(tokens * 2)
    tokens = tokenizer.encode(words)[:tokens]
    return tokenizer.decode(tokens), len(tokens)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Experiment group by with metrics")

    parser.add_argument(
        "--seed",
        type=int,
        default=42,
        help="Random seed",
    )

    parser.add_argument(
        "--encoding-model",
        type=str,
        default="gpt-3.5-turbo",
        help="Encoding model",
    )

    parser.add_argument(
        "--tokens-per-request",
        type=int,
        default=1000,
        help="Target number of tokens per request",
    )

    parser.add_argument(
        "--jitter",
        type=float,
        default=0.1,
        help="Jitter in the number of tokens per request",
    )

    parser.add_argument(
        "--project-name",
        type=str,
        default="group-by-with-metrics",
        help="Project name",
    )

    args = parser.parse_args()

    fake = Faker()

    Faker.seed(args.seed)
    random.seed(args.seed)

    # Split it up so that 50% of the tokens go to a system prompt,
    # 20% to a user prompt, and 30% to the assistant response.
    tokenizer = tiktoken.encoding_for_model(args.encoding_model)

    system_prompt, system_prompt_tokens = generate_n_tokens(tokenizer, fake, math.ceil(0.5 * args.tokens_per_request))

    user_tokens_target = math.ceil(0.2 * args.tokens_per_request)
    completion_tokens_target = math.ceil(0.3 * args.tokens_per_request)

    input_text, input_text_tokens = generate_n_tokens(
        tokenizer,
        fake,
        math.ceil(user_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
    )

    output_text, output_text_tokens = generate_n_tokens(
        tokenizer,
        fake,
        math.ceil(completion_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
    )

    pretend_duration = random.random() * 120

    for experiment_index in range(3):
        experiment = braintrust.init(
            args.project_name,
            f"tagged-scores-exp-{experiment_index * 3}",
            metadata={
                "common-metadata": "common",
                "nested_metadata": {
                    "nested1": {
                        "nested2": "in" if experiment_index % 2 == 0 else "out",
                    },
                },
            },
        )

        for i in range(2):
            experiment.log(
                input=f"{i}",
                output=2,
                scores={
                    "random_score": random.random(),
                    "stable_score": (i + 1) * 0.25,
                },
                metadata={"this": f"this{i}", "that": "that", "model": args.encoding_model, "temperature": 0.7},
                metrics={
                    "prompt_tokens": system_prompt_tokens + input_text_tokens,
                    "completion_tokens": output_text_tokens,
                    "total_tokens": system_prompt_tokens + input_text_tokens + output_text_tokens,
                    "tokens": system_prompt_tokens + input_text_tokens + output_text_tokens,
                    "time_to_first_token": pretend_duration / random.randint(2, 10),
                },
                tags=[f"tag-1-{i}", "common-tag"],
            )

        system_prompt, system_prompt_tokens = generate_n_tokens(
            tokenizer, fake, math.ceil(0.5 * args.tokens_per_request)
        )

        user_tokens_target = math.ceil(0.2 * args.tokens_per_request)
        completion_tokens_target = math.ceil(0.3 * args.tokens_per_request)

        input_text, input_text_tokens = generate_n_tokens(
            tokenizer,
            fake,
            math.ceil(user_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
        )

        output_text, output_text_tokens = generate_n_tokens(
            tokenizer,
            fake,
            math.ceil(completion_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
        )

        experiment = braintrust.init(
            args.project_name,
            f"tagged-scores-exp-{experiment_index * 3 + 1}",
            metadata={
                "common-metadata": "common",
                "nested_metadata": {
                    "nested1": {
                        "nested2": "in" if experiment_index % 2 == 0 else "out",
                    },
                },
            },
        )
        system_prompt, system_prompt_tokens = generate_n_tokens(
            tokenizer, fake, math.ceil(0.5 * args.tokens_per_request)
        )

        user_tokens_target = math.ceil(0.2 * args.tokens_per_request)
        completion_tokens_target = math.ceil(0.3 * args.tokens_per_request)

        input_text, input_text_tokens = generate_n_tokens(
            tokenizer,
            fake,
            math.ceil(user_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
        )

        output_text, output_text_tokens = generate_n_tokens(
            tokenizer,
            fake,
            math.ceil(completion_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
        )

        for i in range(2):
            experiment.log(
                input=f"{i}",
                output=2,
                scores={
                    "random_score": random.random(),
                    "stable_score": (i + 1) * 0.25,
                },
                metadata={
                    "this": f"this{i}",
                    "that": "that",
                },
                metrics={
                    "prompt_tokens": system_prompt_tokens + input_text_tokens,
                    "completion_tokens": output_text_tokens,
                    "total_tokens": system_prompt_tokens + input_text_tokens + output_text_tokens,
                    "tokens": system_prompt_tokens + input_text_tokens + output_text_tokens,
                    "time_to_first_token": pretend_duration / random.randint(2, 10),
                },
                tags=[f"tag-2-{i}", "common-tag"],
            )

        system_prompt, system_prompt_tokens = generate_n_tokens(
            tokenizer, fake, math.ceil(0.5 * args.tokens_per_request)
        )

        user_tokens_target = math.ceil(0.2 * args.tokens_per_request)
        completion_tokens_target = math.ceil(0.3 * args.tokens_per_request)

        input_text, input_text_tokens = generate_n_tokens(
            tokenizer,
            fake,
            math.ceil(user_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
        )

        output_text, output_text_tokens = generate_n_tokens(
            tokenizer,
            fake,
            math.ceil(completion_tokens_target * (1 + args.jitter * (random.random() - 0.5))),
        )
        experiment = braintrust.init(args.project_name, f"tagged-scores-exp-{experiment_index * 3 + 2}")
        for i in range(2):
            experiment.log(
                input=f"{i}",
                output=2,
                scores={
                    "different_random_score": random.random(),
                    "different_stable_score": (i + 1) * 0.25,
                },
                metadata={
                    "this": f"this{i}",
                    "that": "that",
                },
                metrics={
                    "prompt_tokens": system_prompt_tokens + input_text_tokens,
                    "completion_tokens": output_text_tokens,
                    "total_tokens": system_prompt_tokens + input_text_tokens + output_text_tokens,
                    "tokens": system_prompt_tokens + input_text_tokens + output_text_tokens,
                    "time_to_first_token": pretend_duration / random.randint(2, 10),
                },
                tags=[f"tag-3-{i}", "common-tag"],
            )

        print(experiment.summarize())
