import random

import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("openai_nulls_0_repro")

    for i in range(10):
        experiment.log(
            input=i,
            output=1 if i < 5 else 0,
            expected=1 if i < 5 else 0,
            scores={
                "all_1s": 1,
                "half_0s": 1 if i < 5 else 0,
                "half_nulls": 1 if i < 5 else None,
                "all_nulls": None,
                "all_0s": 0,
            },
        )

    print(experiment.summarize())
