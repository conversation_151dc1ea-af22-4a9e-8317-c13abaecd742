import * as braintrust from "braintrust";
import { v4 as uuidv4 } from "uuid";

const NUM_THREADS = 15;

const threadId = uuidv4();

async function main() {
  const logger = braintrust.initLogger({ projectName: "logging_loop_2" });
  logger.traced(
    (span) => {
      span.log({
        input: "nested",
        output: `bar`,
        expected: "baz",
        scores: { accuracy: Math.random() },
        metadata: {
          threadId,
        },
      });

      for (let i = 0; i < 10; ++i) {
        span.traced(
          (span1) => {
            span1.log({ metadata: { message: "In foo" } });

            for (let j = 0; j < 10; ++j) {
              span1.traced(
                (span2) => {
                  span2.log({
                    input: "child",
                  });
                  span2.traced(
                    (span3) => {
                      span3.log({
                        input: "nested child",
                      });
                    },
                    {
                      name: `nested ${j} (foo ${i})`,
                      type: "task",
                    },
                  );
                },
                {
                  name: `span ${j} (foo ${i})`,
                  type: "task",
                },
              );
            }

            return "bar";
          },
          { name: `foo ${i}`, type: "task" },
        );
      }

      return span.traced(
        (span1) => {
          span1.log({ metadata: { message: "In last trace" } });

          for (let j = 0; j < 10; ++j) {
            span1.startSpan({
              name: `span ${j}`,
              type: "task",
            });
          }

          return "bar";
        },
        { name: `last trace`, type: "task" },
      );
    },
    { name: "nested a bunch", type: "eval" },
  );

  for (let i = 0; i < NUM_THREADS; ++i) {
    logger.traced(
      (span) => {
        span.log({
          input: "foo3",
          output: `bar ${i}`,
          expected: "baz",
          scores: { accuracy: Math.random() },
          tags: [`threadid_${threadId}`],
          metadata: {
            threadId,
          },
        });
        return span.traced(
          (span1) => {
            span1.log({ metadata: { message: "In foo" } });

            span1.traced((span2) => {
              span2.log({ input: `span2 ${i}` });
              span2.traced((span3) => {
                span3.log({ input: `span3 ${i}` });
                span3.traced(async (span4) => {
                  span4.log({ input: `span4 ${i}` });
                  await new Promise((r) => setTimeout(r, 1000));
                });
              });
            });
            return "bar";
          },
          { name: `foo ${i}`, type: "task" },
        );
      },
      { name: "root eval", type: "eval" },
    );
    await new Promise((r) => setTimeout(r, 400 + Math.random() * 200));
    if (i % 100 == 0) {
      console.log(i);
    }
  }
}

main();
