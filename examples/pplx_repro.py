import braintrust

if __name__ == "__main__":
    input = {
        "context": "[User Message]\nI bought this new coffee maker and someone replied with 'BWCMK' in initials. Since this was on an online community, they probably used initials to avoid advertising suspicion. What could this product be?\n[End of User Message]\n[Assistant Message]\nThe initials 'BWCMK' could stand for various coffee maker brands. While I can't pinpoint the exact product, popular brands include 'Breville', 'Keurig', and 'Nespresso'. These brands are frequently discussed in online communities.\n[End of Assistant Message]\n[User Message]\nCan you look up BWCMK?\n[End of User Message]",
        "entry_uuid": "7a9bb51d-82f1-4892-a23f-1c7e8068d432",
        "user_query": "That's not right at all",
        "search_results": "",
        "candidate_model": "gpt-4o-2024-08-06",
        "prompt_messages": [
            {
                "role": "system",
                "content": 'You are a helpful AI companion\nYour replies should be:\n- Precise, excellent, and professionally composed\n- Educational, rational, practical, and properly structured\n- Upbeat, captivating, fun, and interactive\nIf someone requests you to structure your reply, you can utilize level 2 and 3 headers such as "## Header"\nInformation cutoff: 2023-12\n\nRespond in the same language as the user\'s inquiry.\n\nPresent date: Saturday, November 16, 2024, 3 PM KST',
            },
            {
                "role": "user",
                "content": "I bought this new coffee maker and someone replied with 'BWCMK' in initials. Since this was on an online community, they probably used initials to avoid advertising suspicion. What could this product be?",
            },
            {
                "role": "assistant",
                "content": "The initials 'BWCMK' could stand for various coffee maker brands. While I can't pinpoint the exact product, popular brands include 'Breville', 'Keurig', and 'Nespresso'. These brands are frequently discussed in online communities.",
            },
            {"role": "user", "content": "Can you look up BWCMK?"},
            {
                "role": "assistant",
                "content": "After looking into it, 'BWCMK' might stand for 'BrewMaster Coffee Maker Kit'. However, this is just a guess based on common coffee maker terminology. Without more context, it's difficult to determine the exact product being referenced.",
            },
            {"role": "user", "content": "That's not right at all"},
        ],
        "reference_model": "gpt-4o-2024-08-06",
        "candidate_answer": "I apologize for the confusion. The initials 'BWCMK' could potentially refer to a specific coffee maker model, but without additional context or information, it's challenging to determine the exact product. Perhaps there are other clues or details that could help identify the specific product being discussed?",
        "reference_answer": "I apologize for my previous incorrect interpretations. While I'm unable to definitively identify what 'BWCMK' stands for, it's likely a specific coffee maker model from a well-known brand. In these situations, it might be helpful to wait for additional community members who recognize these initials to provide more information, or you could try searching for similar discussions in related forums where others might have decoded these initials. If you have any other details or questions, please feel free to ask!",
    }

    output = """```json
{
  "answer_1_formatting_score": 100,
  "answer_1_formatting_explanation": "The response maintains proper structure with clear paragraphs and avoids any formatting issues like incorrect list usage.",
  "answer_1_presentation_score": 75,
  "answer_1_presentation_explanation": "The response acknowledges the confusion and offers a humble correction, though it could be more direct in admitting the initial mistake.",
  "answer_1_factuality_score": 75,
  "answer_1_factuality_explanation": "The response appropriately admits uncertainty about the meaning of BWCMK, but could have been more direct in acknowledging the previous incorrect guesses.",
  "answer_1_instruction_score": 75,
  "answer_1_instruction_explanation": "The response addresses the user's feedback about being incorrect, but could have been more explicit in walking back the previous speculation.",
  "answer_2_formatting_score": 100,
  "answer_2_formatting_explanation": "The response uses clear paragraphs and appropriate structure throughout without any formatting problems.",
  "answer_2_presentation_score": 100,
  "answer_2_presentation_explanation": "The response clearly acknowledges the previous mistakes and provides a balanced, well-structured explanation.",
  "answer_2_factuality_score": 100,
  "answer_2_factuality_explanation": "The response accurately admits uncertainty while suggesting practical next steps for finding the correct information.",
  "answer_2_instruction_score": 100,
  "answer_2_instruction_explanation": "The response fully addresses the correction needed while providing helpful suggestions for moving forward."
}
```
"""

    exp = braintrust.init_experiment(
        project="pplx_repro",
    )

    with exp.start_span("root") as span:
        span.log(input=input, output=output, scores={})
        with span.start_span("foo") as span2:
            span2.log(
                input={"question": "What were the key findings about treatment duration?"},
                output={"response": "The study found no significant effects after 12 weeks"},
                scores={"relevance": 0.8},
            )
        with span.start_span("bar") as span3:
            span3.log(
                input="foo",
                output="bar",
                scores={"relevance": 0.8},
            )
    print(exp.summarize())

    input = {
        "user_request": "How did verbal ability impact the results?",
        "model_response": "Based on the provided text, oxytocin therapy refers to the use of oxytocin (a hormone known for promoting social bonding) as an off-label intervention for individuals on the autism spectrum, with the aim of improving social behavior. In this study, it was specifically administered as a nasal spray. However, the text indicates that previous studies have not been able to confirm its effectiveness, and this particular large randomized controlled trial found no detectable difference in social interactions or behaviors between those who received oxytocin therapy and those who received a placebo over a 24-week period.",
        "context_document": "Background: Individuals on the autism spectrum experience various challenges related to social behaviors and may\noften display increased irritability and hyperactivity. Some studies have suggested that reduced levels of a hormone\ncalled oxytocin, which is known for its role in promoting social bonding, may be responsible for difculties in social\ninteractions in autism. Oxytocin therapy has been used of-label in some individuals on the autism spectrum as a\npotential intervention to improve social behavior, but previous studies have not been able to confrm its efcacy.\nEarlier clinical trials examining oxytocin in autism have shown widely varying results. This large randomized\ncontrolled trial sought to resolve the previous contradictory fndings and determine whether extended use of\noxytocin can help to improve social behaviors in children and teenagers on the autism spectrum.\nMethods & Findings: Tis study evaluated whether a nasal oxytocin spray could afect social interactions and\nother behaviors (e.g., irritability, social withdrawal, and hyperactivity) in children and adolescents on the autism\nspectrum during a 24-week clinical trial. Individuals between the ages of 3 and 17 were assessed by trained\nresearchers and were selected for participation if they met the criteria for autism. Participants were then randomly\nassigned to receive either a nasal oxytocin spray or a placebo (i.e., a comparison nasal spray that did not contain\noxytocin) every day at a series of gradually increasing doses. Participants received social interaction scores every\n4 weeks based on multiple assessments that were completed by caregivers or the participant. Separate analyses\nwere performed in groups of individuals with minimal verbal fuency and high verbal fuency. Tis study found\nno diference in social interaction scores between the oxytocin group and the placebo group and no diference\nbetween the groups with difering levels of verbal ability.\nImplications: Te fndings of this study demonstrate that extended use of a nasal oxytocin spray over a 24-week\nperiod does not make a detectable diference in measured social interactions or behaviors in children and adolescents\nwith autism. While this study showed no observable social beneft with the use of intranasal oxytocin, there are\nremaining questions around issues such as the ideal dose, whether current formulations are able to penetrate the\nblood-brain barrier, and whether a longer intervention time course could reveal efects. In addition, future studies\nthat use techniques such as brain imaging may reveal new information on how oxytocin might be used in autism. ",
        "response_is_valid": False,
        "system_instruction": "Answer the user query using only the information in the provided text.",
        "gpt-4o_quality_score": 0,
        "total_grounding_score": 0,
        "gpt-4o_grounding_score": 0,
        "successful_judge_count": 0,
        "gpt-4o_quality_judgement": "## Analysis\n\n### Instructions in the User Query\n1. **Primary Instruction**: Explain how verbal ability impacted the results.\n   - This is the main focus of the query, as the user is specifically asking about the impact of verbal ability on the results.\n2. **Implied Instruction**: Provide a relevant and coherent explanation.\n   - The response should be relevant to the topic of verbal ability and its impact on results, and it should be coherent and understandable.\n\n### Ranking of Instructions\n1. **Primary Instruction**: Explain how verbal ability impacted the results.\n2. **Implied Instruction**: Provide a relevant and coherent explanation.\n\n### Evaluation of the Test Response\n1. **Primary Instruction**: The test response does not address how verbal ability impacted the results. Instead, it discusses oxytocin therapy and its effectiveness in a study related to autism spectrum disorder. There is no mention of verbal ability or its impact on the results, which is the core of the user's query. Therefore, the test response does not meet this instruction.\n   \n2. **Implied Instruction**: While the test response is coherent and provides a detailed explanation of oxytocin therapy, it is not relevant to the user's query about verbal ability. Thus, it does not meet the implied instruction of providing a relevant explanation.\n\n### Evaluation of the Baseline Response\n1. **Primary Instruction**: Similar to the test response, the baseline response does not address the impact of verbal ability on the results. It focuses on oxytocin therapy and its use in autism spectrum disorder without mentioning verbal ability. Therefore, it does not meet this instruction.\n   \n2. **Implied Instruction**: The baseline response is coherent and provides a brief explanation of oxytocin therapy, but it is not relevant to the user's query about verbal ability. Thus, it does not meet the implied instruction of providing a relevant explanation.\n\n### Reflection\nBoth the test and baseline responses fail to address the user's query about verbal ability. They focus on a different topic (oxytocin therapy) and do not provide the requested information. The rubric criteria and guidelines are clear, and the analysis aligns with them.\n\n## Final Verdict\n```json\n{\n  \"Instruction Following\": \"Major Issue(s)\"\n}\n```",
        "gpt-4o_grounding_judgement": "",
        "gemini-1-5-pro_quality_score": 0,
        "gemini-1-5-pro_grounding_score": 0,
        "claude-3-5-sonnet_quality_score": 0,
        "gemini-1-5-pro_quality_judgement": 'Analysis:\n\nInstructions in the user query:\n\n1. Explain how verbal ability impacted the results.\n\nThis instruction is very important as it is the sole request of the user.  It asks for a specific analysis related to "verbal ability" and its influence on "the results".  This implies the user is referring to some results mentioned in a broader context not included in the provided query.\n\nRanking of Instructions by Importance:\n\n1. Explain how verbal ability impacted the results. (Highest)\n\n\nEvaluation of Test Response:\n\n1. **Explain how verbal ability impacted the results:** Does not meet. The test response discusses oxytocin therapy and its lack of effectiveness in a study. It makes no mention of verbal ability or its impact on any results.\n\nEvaluation of Baseline Response:\n\n1. **Explain how verbal ability impacted the results:** Does not meet. The baseline response describes the administration of oxytocin therapy in a trial but does not address the user\'s question about verbal ability and its impact.\n\n\nReflection:\n\nThe rubric is clear in its expectations.  The issue lies in the lack of context. The user query refers to "results" and "verbal ability" without providing the original text or study being referenced.  Without this context, neither the test nor baseline response can adequately address the query.  If the original text were provided, a proper evaluation could be conducted.\n\n\nFinal Verdict:\n\n```json\n{\n  "Instruction Following": "Major Issue(s)"\n}\n```\n',
        "claude-3-5-sonnet_grounding_score": 0,
        "gemini-1-5-pro_grounding_judgement": "",
        "claude-3-5-sonnet_quality_judgement": 'Analysis:\n\nInstructions from the user query:\n1. Explain how verbal ability impacted the results\n\nThis is a direct question asking for specific information about the relationship between verbal ability and study results.\n\nTask type: Information retrieval and explanation\nImplied instructions:\n- Locate information about verbal ability in the context\n- Explain its relationship to the study outcomes\n\nRanking of importance:\n1. Address the impact of verbal ability on results (VERY important as it\'s the specific focus of the query)\n\nEvaluation of responses:\n\nTest Response:\n- Does NOT meet the instruction: The response completely fails to address verbal ability or its impact on results. Instead, it provides general information about oxytocin therapy and its effectiveness in autism treatment. There is no mention of verbal ability anywhere in the response.\n\nBaseline Response:\n- Does NOT meet the instruction: Similar to the test response, it fails to address verbal ability or its impact on results. It also provides general information about oxytocin therapy without addressing the specific question asked.\n\nReflection:\nBoth responses appear to be answering a different question entirely, possibly "What is oxytocin therapy and what were its effects?" rather than addressing the question about verbal ability\'s impact on results. This suggests either:\n1. The responses were generated for a different question\n2. The context about verbal ability\'s impact was missing from the provided information\n3. There was a system error in understanding the query\n\nFinal Verdict:\n```json\n{\n  "Instruction Following": "Major Issue(s)"\n}\n```\n\nThe major issue classification is warranted because both responses completely fail to address the central question about verbal ability\'s impact on results, instead providing irrelevant information about oxytocin therapy in general.',
        "claude-3-5-sonnet_grounding_judgement": "",
    }

    exp = braintrust.init_experiment(
        project="pplx pointwise repro",
    )
    exp.log(input=input, output="", scores={})
    print(exp.summarize())
