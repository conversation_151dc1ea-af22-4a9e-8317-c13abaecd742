{"name": "braintrust", "version": "1.0.3", "description": "Braintrust helps ship working AI apps with evals, tracing, and monitoring. Automate these LLM tasks by connecting Braintrust to your other tools.", "scripts": {"test": "vitest --run", "test:watch": "vitest", "clean": "pnpm rimraf ./dist ./build", "build": "pnpm clean && tsc", "push": "pnpm build && pnpm zapier push", "_zapier-build": "pnpm build"}, "dependencies": {"@braintrust/core": "0.0.88", "zapier-platform-cli": "^17.0.4", "zapier-platform-core": "17.0.4", "zod": "3.22.4"}, "devDependencies": {"rimraf": "^6.0.1", "typescript": "5.6.2", "vitest": "^2.1.2"}, "private": true, "exports": "./dist/index.js", "type": "module"}