# Braintrust Zapier Integration

This project implements a Zapier integration for Braintrust, allowing you to connect your Braintrust organization with other apps via Zapier.

It provides triggers to automate workflows based on events happening in your Braintrust organization, such as the creation of new projects or logs.

This integration is built using the Zapier Platform CLI and TypeScript.

## Setup

1.  **Install dependencies**:
    ```bash
    pnpm install
    ```
2.  **Authentication**: This integration uses an API Key for authentication. You will need to provide your Braintrust API Key when setting up the connection in Zapier. You can generate an API Key in your Braintrust Organization's [API Keys settings page](https://www.braintrust.dev/app/settings?subroute=api-keys).

## Development

- **Running tests**: The tests for this integration are written using Vitest.
  ```bash
  pnpm vitest
  ```

## Deployment

To deploy this integration to Zapier:
our Integration Title"

````

1.  **Link** to an existing integration on Zapier (if applicable):
    ```bash
    pnpm zapier link
    ```
2.  **Push** your local changes to Zapier:
    ```bash
    pnpm push
    ```

Find out more on the latest Zapier Platform CLI documentation: https://github.com/zapier/zapier-platform/blob/main/packages/cli/README.md.
````
