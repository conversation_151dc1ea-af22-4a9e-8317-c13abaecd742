import { z } from "zod";
import { withZ } from "./types.js";

export const parse = <T extends z.ZodSchema>(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any,
  schema: T,
):
  | { success: true; data: z.infer<T> }
  | { success: false; error: Record<string, string> } => {
  try {
    return { success: true, data: schema.parse(data) };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.reduce((acc, curr) => {
          const path = curr.path.join(".");
          return {
            ...acc,
            [path]: curr.message,
          };
        }, {}),
      };
    }
    throw error;
  }
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Tail<T extends any[]> = T extends [any, ...infer U] ? U : [];

export const trace =
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  <A extends [object, ...any[]], R>(name: string, fn: (...args: A) => R) =>
    async (
      firstArg: withZ<A[0]>,
      ...restArgs: Tail<A>
    ): Promise<Awaited<R>> => {
      const logger = firstArg?.z?.console || console;

      // @ts-expect-error
      // eslint-disable-next-line
      const originalArgs = [firstArg, ...restArgs] as A;

      let result;
      try {
        result = await fn(...originalArgs);
        return result;
      } catch (e) {
        result = e;
        throw e;
      } finally {
        logger.log(
          `[${name}]`,
          "input",
          sanitize(originalArgs),
          "output",
          sanitize(result),
        );
      }
    };

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const sanitize = (context: any): Record<string, unknown> => {
  return flatten(serialize(context));
};

const flatten = (obj: unknown, prefix = ""): Record<string, unknown> => {
  if (!isObject(obj) && !Array.isArray(obj)) {
    return prefix ? { [prefix]: obj } : {};
  }

  return Object.entries(obj).reduce(
    (acc: Record<string, unknown>, [key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;

      if (value === undefined) {
        return acc;
      }

      if (isObject(value) || Array.isArray(value)) {
        return { ...acc, ...flatten(value, newKey) };
      }

      acc[newKey] = value;
      return acc;
    },
    {},
  );
};

const serialize = (value: unknown): unknown => {
  if (value === null || value === undefined) {
    return undefined;
  }

  if (Array.isArray(value)) {
    return value.map(serialize);
  }

  if (value instanceof Error) {
    return {
      name: value.name,
      message: value.message,
    };
  }

  if (value instanceof Date) {
    return value.toISOString();
  }

  if (value instanceof URL || value instanceof URLSearchParams) {
    return value.toString();
  }

  if (isObject(value)) {
    const result: Record<string, unknown> = {};
    for (const [k, v] of Object.entries(value)) {
      const serialized = serialize(v);
      if (serialized !== undefined) {
        result[k] = serialized;
      }
    }
    return result;
  }

  return value;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isObject(value: any): value is { [key: string]: any } {
  return value instanceof Object && !(value instanceof Array);
}
