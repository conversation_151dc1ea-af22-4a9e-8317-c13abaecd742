import zapier, { defineApp } from "zapier-platform-core";

import packageJson from "../package.json" with { type: "json" };

import authentication from "./authentication.js";
import {
  addBearerHeader,
  addContentTypeHeader,
  maybeAddDataPlaneUrl,
} from "./middleware.js";
import new_event from "./triggers/new_event.js";
import new_project from "./triggers/new_project.js";

export default defineApp({
  version: packageJson.version,
  platformVersion: zapier.version,

  authentication,
  beforeRequest: [addBearerHeader, addContentTypeHeader, maybeAddDataPlaneUrl],

  triggers: {
    new_event,
    new_project,
  },
});
