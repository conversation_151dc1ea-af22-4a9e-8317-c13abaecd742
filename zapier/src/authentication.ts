import type { Authentication } from "zapier-platform-core";
import { getErrorContext } from "./braintrust.js";

const authentication: Authentication = {
  type: "session",
  test: async (z, _): Promise<{ orgName: string; userEmail: string }> =>
    getErrorContext({
      z,
    }),
  sessionConfig: {
    perform: async (z, _): Promise<{ orgName: string; userEmail: string }> =>
      getErrorContext({
        z,
      }),
  },
  fields: [
    {
      helpText: `Enter your Braintrust **API Key** or **Service Token**. [Click here](https://www.braintrust.dev/app/settings?subroute=api-keys) to open the "API Keys" settings page in Braintrust. Use an existing API Key/Service Token or create a new one.`,
      computed: false,
      key: "api_key",
      required: true,
      label: "API Key",
      type: "password",
    },
  ],
  connectionLabel: "{{orgName}} ({{userEmail}})",
};

export default authentication;
