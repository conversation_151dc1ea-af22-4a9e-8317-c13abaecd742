import type { BeforeRequestMiddleware, ZObject } from "zapier-platform-core";
import { login } from "./braintrust.js";
import { DATAPLANE_URL } from "./constants.js";
import { trace } from "./utils.js";

const getApiUrlCacheKey = (orgName: string) => `org_info_${orgName}_api_url`;

const getDataPlaneUrl = trace(
  "getDataPlaneUrl",
  async ({ orgName, z }: { orgName: string; z: ZObject }) => {
    const cacheKey = getApiUrlCacheKey(orgName);
    const cachedApiUrl = await z.cache.get(cacheKey);
    if (cachedApiUrl) {
      return cachedApiUrl;
    }

    const { org_info } = await login({ z });
    const apiUrl = org_info?.[0]?.api_url ?? "https://api.braintrust.dev";

    await z.cache.set(cacheKey, apiUrl, 5 * 60);

    return apiUrl;
  },
);

export const maybeAddDataPlaneUrl: BeforeRequestMiddleware = async (
  request,
  z,
  bundle,
) => {
  if (!request.url) {
    throw new Error("URL is required");
  }

  if (!z.request) {
    // @ts-ignore
    z.request = async (options: Parameters<ZObject["request"]>[0]) => {
      // since this is a fake z.request we need to run our middleware ourselves
      addBearerHeader(options, z, bundle);
      addContentTypeHeader(options, z, bundle);

      return fetch(options.url, {
        method: options.method,
        headers: options.headers,
        body: options.body && String(options.body),
      }).then(async (res) => {
        if (!res.ok && !options.skipThrowForStatus) {
          throw new z.errors.Error(await res.text());
        }
        return {
          data: await res.json(),
        };
      });
    };
  }

  z.console.log("bundle", JSON.stringify(bundle, null, 2));

  if (request.url.startsWith(DATAPLANE_URL)) {
    if (!bundle.authData.orgName) {
      throw new z.errors.HaltedError(
        "Organization name is required. Reconnect.",
      );
    }

    const apiUrl = await getDataPlaneUrl({
      z,
      orgName: bundle.authData.orgName,
    });

    if (!apiUrl) {
      throw new z.errors.HaltedError("Could not resolve data plane URL");
    }

    request.url = request.url.replace(DATAPLANE_URL, apiUrl);
  }

  return request;
};

export const addBearerHeader: BeforeRequestMiddleware = (
  request,
  _,
  bundle,
) => {
  if (!request.headers?.Authorization) {
    if (!bundle.authData.api_key) {
      throw new Error("API key is required");
    }

    request.headers = {
      ...request.headers,
      Authorization: `Bearer ${bundle.authData.api_key}`,
    };
  }
  return request;
};

export const addContentTypeHeader: BeforeRequestMiddleware = (
  request,
  _,
  __,
) => {
  if (!request.headers?.["Content-Type"]) {
    request.headers = {
      ...request.headers,
      "Content-Type": "application/json",
    };
  }
  return request;
};
