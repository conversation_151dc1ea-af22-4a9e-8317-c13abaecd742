import { createAppTester } from "zapier-platform-core";

import App from "./index";
import { expect, it } from "vitest";

const appTester = createAppTester(App);

it("generates an authorize URL", async () => {
  const bundle = {
    authData: {
      api_key: process.env.BRAINTRUST_API_KEY,
      orgName: process.env.BRAINTRUST_ORG_NAME,
    },
  };

  if (!App.authentication) {
    throw new Error("Authentication is not configured");
  }

  // @ts-expect-error
  const user = await appTester(App.authentication.test, bundle);

  expect(user).toEqual({
    orgName: expect.any(String),
    userEmail: expect.any(String),
  });
});
