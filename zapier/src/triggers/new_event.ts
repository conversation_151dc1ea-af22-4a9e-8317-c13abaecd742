import { Bundle, define<PERSON><PERSON>ger } from "zapier-platform-core";
import { deleteAutomation, registerAutomation } from "../braintrust.js";

export default defineTrigger({
  operation: {
    type: "hook",
    performSubscribe: async (
      z,
      bundle: Bundle<{
        name?: string;
        description?: string;
        project_id?: string;
        interval?: string;
      }>,
    ) => {
      const intervalSeconds = parseInt(bundle.inputData.interval || "0", 10);
      if (isNaN(intervalSeconds)) {
        throw new z.errors.Error(
          "Please select a valid interval from the dropdown options.",
        );
      }

      const automation = await registerAutomation({
        name: bundle.inputData.name!,
        description: bundle.inputData.description!,
        project_id: bundle.inputData.project_id!,
        config: {
          event_type: "logs",
          btql_filter: "",
          interval_seconds: intervalSeconds,
          action: {
            type: "webhook",
            url: bundle.targetUrl!,
          },
        },
        z,
      });

      return {
        id: automation.id,
        name: automation.name,
      };
    },
    inputFields: [
      {
        key: "project_id",
        type: "string",
        label: "Project",
        required: true,
        list: false,
        altersDynamicFields: false,
        dynamic: "new_project.id.name",
        helpText: "Select the project to monitor for automation events.",
      },
      {
        key: "name",
        type: "string",
        label: "Automation Name",
        required: true,
        list: false,
        altersDynamicFields: false,
        helpText: "Enter a descriptive name for this automation.",
      },
      {
        key: "description",
        type: "string",
        label: "Description",
        required: false,
        list: false,
        altersDynamicFields: false,
        helpText: "Optional description to help identify this automation.",
      },
      {
        key: "event_type",
        label: "Event Type",
        type: "string",
        choices: [
          {
            value: "logs",
            label: "Log event",
            sample: "logs",
          },
          // {
          //   value: "btql_export",
          //   label: "BTQL export",
          //   sample: "btql_export",
          // },
        ],
        required: true,
        list: false,
        altersDynamicFields: true,
        helpText: "Choose the type of events this automation should monitor.",
      },
      (_, bundle) => {
        if (bundle.inputData.event_type === "logs") {
          return [
            {
              key: "interval",
              type: "string",
              label: "Interval",
              choices: [
                {
                  value: String(5 * 60),
                  label: "5 minutes",
                  sample: String(5 * 60),
                },
                {
                  value: String(30 * 60),
                  label: "30 minutes",
                  sample: String(30 * 60),
                },
                {
                  value: String(60 * 60),
                  label: "1 hour",
                  sample: String(60 * 60),
                },
                {
                  value: String(4 * 60 * 60),
                  label: "4 hours",
                  sample: String(4 * 60 * 60),
                },
                {
                  value: String(12 * 60 * 60),
                  label: "12 hours",
                  sample: String(12 * 60 * 60),
                },
                {
                  value: String(24 * 60 * 60),
                  label: "1 day",
                  sample: String(24 * 60 * 60),
                },
              ],
              required: true,
              list: false,
              altersDynamicFields: false,
              helpText: "How often should Zapier check for new events?",
            },
          ];
        }

        return [];
      },
    ],
    performUnsubscribe: async (z, bundle) => {
      if (!bundle.subscribeData?.id) {
        throw new z.errors.Error("Automation ID is missing");
      }

      await deleteAutomation({
        z,
        id: bundle.subscribeData.id,
      });
    },
    perform: (_, { authData, ...bundle }) => {
      return [{ id: String(Date.now()), ...bundle.cleanedRequest }];
    },
    performList: (_, __) => {
      return [
        {
          id: "123456",
          organization: {
            id: "eaacb1d9-bded-437b-a02a-7c4e369730a4",
            name: "Sample Organization Name",
          },
          project: {
            id: "f0343ce9-308c-45c7-ac6a-19b9586328a8",
            name: "Sample Project Name",
          },
          automation: {
            id: "4b25e343-43e0-418e-a58e-2d6a095ac172",
            name: "Sample Automation Name",
            description: null,
            event_type: "logs",
            btql_filter: null,
            interval_seconds: 300,
            url: "https://www.braintrust.dev/app/Sample Organization Name/p/Sample Project Name/configuration/alerts?aid=4b25e343-43e0-418e-a58e-2d6a095ac172",
          },
          details: {
            is_test: true,
            message: "foo: 2 logs triggered alert in the last 5 minutes",
            time_start: "2025-06-08T04:58:57.081Z",
            time_end: "2025-06-08T05:03:57.081Z",
            count: 2,
            related_logs_url:
              "https://www.braintrust.dev/app/Sample Organization Name/p/Sample Project Name/logs?search=...",
          },
        },
      ];
    },
    sample: {
      id: "123456",
      organization: {
        id: "eaacb1d9-bded-437b-a02a-7c4e369730a4",
        name: "Sample Organization Name",
      },
      project: {
        id: "f0343ce9-308c-45c7-ac6a-19b9586328a8",
        name: "Sample Project Name",
      },
      automation: {
        id: "4b25e343-43e0-418e-a58e-2d6a095ac172",
        name: "Sample Automation Name",
        description: null,
        event_type: "logs",
        btql_filter: null,
        interval_seconds: 300,
        url: "https://www.braintrust.dev/app/Sample Organization Name/p/Sample Project Name/configuration/alerts?aid=4b25e343-43e0-418e-a58e-2d6a095ac172",
      },
      details: {
        is_test: true,
        message: "foo: 2 logs triggered alert in the last 5 minutes",
        time_start: "2025-06-08T04:58:57.081Z",
        time_end: "2025-06-08T05:03:57.081Z",
        count: 2,
        related_logs_url:
          "https://www.braintrust.dev/app/Sample Organization Name/p/Sample Project Name/logs?search=...",
      },
    },
  },
  display: {
    description: "Triggers when an automation creates a new event.",
    hidden: false,
    label: "New Event",
  },
  key: "new_event",
  noun: "Event",
});
