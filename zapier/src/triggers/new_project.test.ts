import { createAppTester } from "zapier-platform-core";

import App from "../index";
import { expect, it } from "vitest";

const appTester = createAppTester(App);

it("generates an authorize URL", async () => {
  const bundle = {
    authData: {
      api_key: process.env.BRAINTRUST_API_KEY,
      orgName: process.env.BRAINTRUST_ORG_NAME,
    },
  };

  const projects = await appTester(
    // @ts-expect-error
    App.triggers.new_project.operation.perform,
    // @ts-expect-error
    bundle,
  );

  expect(projects?.[0]).toEqual({
    id: expect.any(String),
    name: expect.any(String),
    created: expect.any(String),
  });
});
