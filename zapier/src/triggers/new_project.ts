import { defineTrigger } from "zapier-platform-core";
import { getProjects } from "../braintrust.js";

export default defineTrigger({
  operation: {
    perform: async (z, _) => getProjects({ z }),
    inputFields: [],
    sample: {
      id: "123",
      name: "Sample Project",
      created: "2023-01-01T00:00:00Z",
    },
  },
  display: {
    description: "Triggers when a new project is created.",
    hidden: false,
    label: "New Project",
  },
  key: "new_project",
  noun: "Project",
});
