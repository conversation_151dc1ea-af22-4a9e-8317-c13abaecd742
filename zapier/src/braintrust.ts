import {
  createProjectAutomationSchema,
  type ProjectAutomation,
  type Project,
} from "@braintrust/core/typespecs";
import { z } from "zod";
import { parse, trace } from "./utils.js";
import { CONTROLPLANE_URL, DATAPLANE_URL } from "./constants.js";
import { withZ } from "./types.js";

export const getErrorContext = trace(
  "getErrorContext",
  async ({
    z: zapier,
  }: withZ<null>): Promise<{
    orgName: string;
    userEmail: string;
  }> => {
    const response = await zapier.request({
      url: `${CONTROLPLANE_URL}/api/self/get_error_context`,
      method: "POST",
      body: JSON.stringify({}),
    });

    return {
      userEmail: response.data.userEmail,
      orgName: response.data.orgName,
    };
  },
);

type CreateProjectAutomation = z.infer<typeof createProjectAutomationSchema>;

export const registerAutomation = trace(
  "registerAutomation",
  async ({
    z: zapier,
    ...input
  }: withZ<CreateProjectAutomation>): Promise<ProjectAutomation> => {
    const response = await zapier.request({
      url: `${DATAPLANE_URL}/api/project_automation/register`,
      method: "POST",
      body: JSON.stringify({
        project_automation_name: input.name,
        project_id: input.project_id,
        description: input.description,
        config: input.config,
      }),
    });

    return response.data.project_automation;
  },
);

export const deleteAutomation = trace(
  "deleteAutomation",
  async ({ z: zapier, ...input }: withZ<{ id: string }>): Promise<void> => {
    const response = await zapier.request({
      url: `${DATAPLANE_URL}/api/project_automation/delete_id`,
      method: "POST",
      body: JSON.stringify({
        id: input.id,
      }),
    });

    return response.data;
  },
);

export const login = trace("login", async ({ z: zapier }: withZ<null>) => {
  const response = await zapier.request({
    url: `${CONTROLPLANE_URL}/api/apikey/login`,
    method: "GET",
  });

  const result = parse(
    response.data,
    z.object({
      org_info: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          api_url: z.string().nullish(),
        }),
      ),
    }),
  );

  if (!result.success) {
    throw new zapier.errors.ResponseError(response);
  }

  return result.data;
});

export const getProjects = trace(
  "getProjects",
  async ({ z: zapier }: withZ<null>) => {
    const response = await zapier.request({
      url: `${DATAPLANE_URL}/api/project/get`,
      method: "POST",
      body: JSON.stringify({}),
    });

    return response.data.map((project: Project) => ({
      id: project.id,
      name: project.name,
      created: project.created,
    }));
  },
);
