set -e

# Configure ports (defaults if not set)
export TS_API_HOST=${TS_API_HOST:-"0.0.0.0"}
export TS_API_PORT=${TS_API_PORT:-"8000"}
export TS_API_HEALTHSERVER_HOST=${TS_API_HEALTHSERVER_HOST:-"0.0.0.0"}
export TS_API_HEALTHSERVER_PORT=${TS_API_HEALTHSERVER_PORT:-"8001"}


# Build prerequisites
if [ "$FULL_BUILD" = "true" ]; then
    pnpm build --filter "@braintrust/api-ts"
else
    pnpm build --filter "@braintrust/api-ts^..."
fi

# Run the service
pnpm --filter "@braintrust/api-ts" dev
