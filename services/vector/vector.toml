[api]
enabled = true

[sources.otlp]
type = "opentelemetry"
grpc.address = "127.0.0.1:4317"
http.address = "127.0.0.1:4318"

[sinks.dd_metrics]
type = "datadog_metrics"
inputs = ["otlp.metrics"]         # ← direct edge, no transform
default_api_key = "${DD_API_KEY}"
site = "us5.datadoghq.com"        # change to your Datadog site (us, us3, eu, …)

[sinks.dd_traces]
type = "datadog_traces"
inputs = ["otlp.traces"]          # ← direct edge, no transform
default_api_key = "${DD_API_KEY}"
site = "us5.datadoghq.com"        # change to your Datadog site (us, us3, eu, …)
