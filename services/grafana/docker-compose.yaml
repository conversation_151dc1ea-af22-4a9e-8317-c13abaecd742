services:
  # Tempo runs as user 10001, and docker compose creates the volume as root.
  # As such, we need to chown the volume in order for <PERSON><PERSON> to start correctly.
  init:
    image: &tempoImage grafana/tempo:2.6.0
    user: root
    entrypoint:
      - "chown"
      - "10001:10001"
      - "/var/tempo"
    volumes:
      - ./tempo-data:/var/tempo

  memcached:
    image: memcached:1.6.29
    container_name: memcached
    ports:
      - "11211:11211"
    environment:
      - MEMCACHED_MAX_MEMORY=64m # Set the maximum memory usage
      - MEMCACHED_THREADS=4 # Number of threads to use

  tempo:
    image: *tempoImage
    command: ["-config.file=/etc/tempo.yaml"]
    volumes:
      - ./tempo.yaml:/etc/tempo.yaml
      - ./tempo-data:/var/tempo
    ports:
      - "14268:14268" # jaeger ingest
      - "3200:3200" # tempo
      - "9095:9095" # tempo grpc
      - "4317:4317" # otlp grpc
      - "4318:4318" # otlp http
      - "9411:9411" # zipkin
    depends_on:
      - init
      - memcached

  #  k6-tracing:
  #    image: ghcr.io/grafana/xk6-client-tracing:v0.0.5
  #    environment:
  #      - ENDPOINT=tempo:4317
  #    restart: always
  #    depends_on:
  #      - tempo

  prometheus:
    image: prom/prometheus:latest
    command:
      - --config.file=/etc/prometheus.yaml
      - --web.enable-remote-write-receiver
      - --enable-feature=exemplar-storage
      - --enable-feature=native-histograms
    volumes:
      - ./prometheus.yaml:/etc/prometheus.yaml
    ports:
      - "9090:9090"

  grafana:
    image: grafana/grafana:11.2.0
    volumes:
      - ./grafana-datasources.yaml:/etc/grafana/provisioning/datasources/datasources.yaml
    environment:
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
      - GF_AUTH_DISABLE_LOGIN_FORM=true
      - GF_FEATURE_TOGGLES_ENABLE=traceqlEditor metricsSummary
    ports:
      - "3010:3000"
