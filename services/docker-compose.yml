services:
  redis:
    build: ./redis
    ports:
      - 6479:6379
    extra_hosts:
      - "host.docker.internal:host-gateway"

  postgres:
    build: ./postgres
    ports:
      - 5532:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    volumes:
      - ../data/postgres:/var/lib/postgresql/data
    # Use the copied config files.
    command: postgres -c config_file=/etc/postgresql.conf -c hba_file=/etc/pg_hba.conf
    extra_hosts:
      - "host.docker.internal:host-gateway"

  minio:
    build: ./minio
    ports:
      # Main port to access minio REST API.
      - 10000:9000
      # Port for minio web UI.
      - 10001:9001
    volumes:
      - ../data/minio:/data
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      MINIO_ROOT_USER: minio_root_user
      MINIO_ROOT_PASSWORD: minio_root_password

  supabase-db:
    image: public.ecr.aws/supabase/postgres:*********
    ports:
      - "54322:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    volumes:
      - supabase_db_app:/var/lib/postgresql/data
      - supabase_config_app:/etc/postgresql-custom

  serverless-redis-http:
    ports:
      - "8079:80"
    image: hiett/serverless-redis-http:latest
    environment:
      SRH_MODE: env
      SRH_TOKEN: example_token
      SRH_CONNECTION_STRING: "redis://host.docker.internal:6479"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Omit this so that if we launch redis via a different compose file (e.g.
    # when running docker tests), we can still use this service.
    #
    # depends_on:
    #   - redis

volumes:
  supabase_db_app:
    external: ${SUPABASE_VOLUMES_EXTERNAL}
    name: supabase_db_app
  supabase_config_app:
    external: ${SUPABASE_VOLUMES_EXTERNAL}
    name: supabase_config_app
