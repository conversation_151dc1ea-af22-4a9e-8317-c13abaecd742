#!/bin/bash

# Check if watchexec is installed
if command -v watchexec &> /dev/null; then
    WATCHEXEC_INSTALLED=true
else
    WATCHEXEC_INSTALLED=false
    echo "watchexec not installed, using cargo run instead. You can install it with 'cargo install watchexec-cli', and rust changes will automatically refresh brainstore" >&2
fi

# Check if port 4318 is open for local OLTP
if nc -z localhost 4318; then
    LOCAL_OLTP_ARGS="--local-oltp"
else
    LOCAL_OLTP_ARGS=""
fi

# Set environment variables
export AWS_ENDPOINT_URL="http://localhost:10000"
export AWS_ACCESS_KEY_ID="minio_root_user"
export AWS_SECRET_ACCESS_KEY="minio_root_password"
export AWS_ALLOW_HTTP="true"
export BRAINSTORE_OBJECT_STORE_CACHE_MEMORY_LIMIT="1MB"
export BRAINSTORE_OBJECT_STORE_CACHE_FILE_SIZE="1GB"
export BRAINSTORE_INDEX_WRITER_VALIDATE_AFTER_COMPLETION="true"
export BRAINSTORE_LOCKS_MANAGER_ENABLE_BOOKKEEPING="true"

# Build prerequisites
cd "$(dirname "$0")/.."  # Go to repo root
make brainstore/examples/braintrust/schema.json
cd brainstore
cargo build --bin brainstore ${RELEASE:+--release}
cd ..
make brainstore-init-buckets-tests

# Run brainstore
cd brainstore
if [ "$WATCHEXEC_INSTALLED" = true ]; then
    watchexec -e "rs,toml,yaml,json" -r "cargo run ${RELEASE:+--release} --bin brainstore web --config examples/braintrust/brainstore.yaml --schema examples/braintrust/schema.json -v --suppress-verbose-info ${LOCAL_OLTP_ARGS}"
else
    cargo run ${RELEASE:+--release} --bin brainstore web --config examples/braintrust/brainstore.yaml --schema examples/braintrust/schema.json -v --suppress-verbose-info ${LOCAL_OLTP_ARGS}
fi
