FROM public.ecr.aws/docker/library/postgres:15.5

# Add postgres repo
RUN apt-get update
RUN apt-get install ca-certificates
RUN apt install -y postgresql-common
RUN /usr/share/postgresql-common/pgdg/apt.postgresql.org.sh -y

# Install pg_cron
RUN apt-get install -y postgresql-15-cron
# On some machines we need to force an update to get the partman extension, and
# the cached update on people's existing installs is not enough.
RUN apt-get update
RUN apt-get install -y postgresql-15-partman

# Copy the config files into the docker image.
COPY postgresql.conf /etc/postgresql.conf
COPY pg_hba.conf /etc/pg_hba.conf

HEALTHCHECK --interval=5s --timeout=30s --retries=100 CMD pg_isready -U $POSTGRES_USER
