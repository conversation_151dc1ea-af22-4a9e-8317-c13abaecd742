FROM public.ecr.aws/docker/library/redis:7.0

HEALTHCHECK --interval=5s --timeout=30s --retries=100 CMD redis-cli --raw incr ping

# In order to run redis with SSL, you need to generate TLS certificates. For
# testing, this can be done using the following script:
#
#   curl -O https://raw.githubusercontent.com/redis/redis/unstable/utils/gen-test-certs.sh
#
# After running that script to generate the certificates, place them in the
# `tls` directory relative to this Dockerfile, and they will be copied into the
# container.
#
#COPY ./redis.conf /usr/local/etc/redis/redis.conf
#COPY ./tls/redis.crt /usr/local/etc/redis/certs/redis.crt
#COPY ./tls/redis.key /usr/local/etc/redis/certs/redis.key
#COPY ./tls/ca.crt /usr/local/etc/redis/certs/ca.crt
#
#RUN chmod 600 /usr/local/etc/redis/certs/redis.key && \
#    chmod 644 /usr/local/etc/redis/certs/redis.crt && \
#    chmod 644 /usr/local/etc/redis/certs/ca.crt
#
#RUN chown redis:redis /usr/local/etc/redis/certs/redis.key && \
#    chown redis:redis /usr/local/etc/redis/certs/redis.crt && \
#    chown redis:redis /usr/local/etc/redis/certs/ca.crt
#
#CMD ["redis-server", "/usr/local/etc/redis/redis.conf"]
#
## Modify healthcheck to use SSL
#HEALTHCHECK --interval=5s --timeout=30s --retries=100 \
#  CMD redis-cli --tls --cert /usr/local/etc/redis/certs/redis.crt \
#                --key /usr/local/etc/redis/certs/redis.key \
#                --cacert /usr/local/etc/redis/certs/ca.crt \
#                --raw incr ping
