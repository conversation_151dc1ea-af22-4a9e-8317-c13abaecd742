#!/bin/bash

SRC_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

ps1_old="$PS1"
if [ -e $SRC_ROOT/venv/bin/activate ]; then
    source $SRC_ROOT/venv/bin/activate
fi
export PATH="${SRC_ROOT}/target/debug/:$PATH"
export BRAINTRUST_DEV=1
export PS1="(bt) $ps1_old"
export ORB_WEBHOOK_SECRET=not_a_real_webhook_secret
export INSERT_LOGS2=true

alias localpg='psql --host 127.0.0.1 --port 5532 --user postgres postgres'
alias localsb="psql 'postgresql://postgres:postgres@localhost:54322/postgres'"
alias localch='psql --host 127.0.0.1 --port 9005 --user default default'
alias localrs='redis-cli -p 6479'
