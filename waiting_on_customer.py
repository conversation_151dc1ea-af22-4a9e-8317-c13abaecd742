import braintrust
from pydantic import BaseModel
from typing import Optional


class WaitingOnCustomerInput(BaseModel):
    reason: str
    ticket_id: Optional[str] = None
    follow_up_date: Optional[str] = None


def waiting_on_customer(reason: str, ticket_id: Optional[str] = None, follow_up_date: Optional[str] = None) -> str:
    """
    Dummy implementation of waiting_on_customer tool.
    This tool would normally mark a support ticket as waiting for customer response.
    """
    print(f"Marking ticket as waiting on customer")
    print(f"Reason: {reason}")
    if ticket_id:
        print(f"Ticket ID: {ticket_id}")
    if follow_up_date:
        print(f"Follow-up date: {follow_up_date}")
    
    # Return a dummy response indicating the status was updated
    status_msg = f"Ticket marked as waiting on customer. Reason: {reason}"
    if ticket_id:
        status_msg += f" (Ticket ID: {ticket_id})"
    if follow_up_date:
        status_msg += f" (Follow-up scheduled: {follow_up_date})"
    
    return status_msg


project = braintrust.projects.create(name="pedro-project1")

waiting_on_customer_tool = project.tools.create(
    handler=waiting_on_customer,
    name="Waiting on Customer",
    slug="waiting-on-customer", 
    description="Mark a support ticket as waiting for customer response or action.",
    parameters=WaitingOnCustomerInput,
    if_exists="replace",
)
