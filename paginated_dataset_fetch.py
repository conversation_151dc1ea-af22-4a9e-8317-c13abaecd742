import braintrust

def fetch_dataset_paginated(project_name, dataset_name, page_size=100):
    """
    Fetch dataset records in pages using BTQL limit.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset  
        page_size: Number of records per page
    
    Yields:
        Individual dataset records
    """
    offset = 0
    
    while True:
        # Create dataset with BTQL limit and offset
        dataset = braintrust.init_dataset(
            project=project_name,
            name=dataset_name,
            _internal_btql={
                "limit": page_size,
                "offset": offset
            }
        )
        
        records = list(dataset)
        
        if not records:
            break
            
        for record in records:
            yield record
            
        # If we got fewer records than page_size, we've reached the end
        if len(records) < page_size:
            break
            
        offset += page_size

def fetch_dataset_with_pagination_key(project_name, dataset_name, page_size=100, start_pagination_key=None):
    """
    Fetch dataset records using pagination key for more efficient pagination.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        page_size: Number of records per page
        start_pagination_key: Optional starting pagination key
    
    Yields:
        Individual dataset records
    """
    last_pagination_key = start_pagination_key
    
    while True:
        btql_filter = {"limit": page_size}
        
        # Add pagination key filter if we have one
        if last_pagination_key:
            btql_filter["filter"] = {
                "op": ">",
                "left": {"op": "ident", "name": ["_pagination_key"]},
                "right": {"op": "literal", "value": last_pagination_key}
            }
        
        # Add sorting by pagination key for consistent ordering
        btql_filter["sort"] = [{"op": "ident", "name": ["_pagination_key"]}]
        
        dataset = braintrust.init_dataset(
            project=project_name,
            name=dataset_name,
            _internal_btql=btql_filter
        )
        
        records = list(dataset)
        
        if not records:
            break
            
        for record in records:
            yield record
            
        # Update pagination key for next iteration
        if records:
            last_pagination_key = records[-1].get("_pagination_key")
        
        # If we got fewer records than page_size, we've reached the end
        if len(records) < page_size:
            break

# Example usage
if __name__ == "__main__":
    project_name = "pedro-brainstore1"
    dataset_name = "Dataset 1"
    
    print("=== Method 1: Simple pagination with limit/offset ===")
    count = 0
    for record in fetch_dataset_paginated(project_name, dataset_name, page_size=10):
        count += 1
        print(f"Record {count}: {record.get('id', 'no-id')}")
        
        # Stop after 20 records for demo
        if count >= 20:
            break
    
    print(f"\n=== Method 2: Pagination with pagination key ===")
    count = 0
    for record in fetch_dataset_with_pagination_key(project_name, dataset_name, page_size=10):
        count += 1
        print(f"Record {count}: {record.get('id', 'no-id')}")
        
        # Stop after 20 records for demo  
        if count >= 20:
            break
