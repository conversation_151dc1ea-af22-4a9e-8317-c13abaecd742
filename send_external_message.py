import braintrust
from pydantic import BaseModel


class SendExternalMessageInput(BaseModel):
    message: str
    recipient: str = "customer"
    format: str = "html"


def send_external_message(message: str, recipient: str = "customer", format: str = "html") -> str:
    """
    Dummy implementation of send_external_message tool.
    This tool would normally send a message to an external recipient (like a customer).
    """
    print(f"Sending message to {recipient} in {format} format:")
    print(f"Message: {message}")
    
    # Return a dummy response indicating the message was sent
    return f"Message sent successfully to {recipient}"


project = braintrust.projects.create(name="pedro-project1")

send_external_message_tool = project.tools.create(
    handler=send_external_message,
    name="Send External Message",
    slug="send-external-message",
    description="Send a message to an external recipient, typically the customer, in the specified format.",
    parameters=SendExternalMessageInput,
    if_exists="replace",
)
