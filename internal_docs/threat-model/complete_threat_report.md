# Braintrust infrastructure threat model

This threat model describes the Braintrust hybrid deployment, which includes a control plane (deployed globally by Braintrust) and data plane (deployed in customer's Azure account).
The data plane includes critical components such as the API Service (Docker container), Postgres Database, Redis Cache, and Brainstore (Docker container) for event storage and analytics, all deployed in Azure.
The control plane hosts the external UI (Vercel), auth (Clerk), and metadata (Supabase).
Users' browsers and SDK clients communicate directly with both the control plane and data plane. The data plane reaches out to the control plane for RBAC/auth checks.
This architecture follows best practices as described in [Braintrust Advanced Self-hosting](https://www.braintrust.dev/docs/guides/self-hosting/advanced) and [OWASP PyTM](https://owasp.org/www-project-pytm/).

## Data Flow Diagram

![Threat Model Diagram](threat_model_diagram.png)

## Architecture Components

### Data Plane Boundary

- **Private Subnets**:

- **Azure Storage**: Azure Blob Storage used by Brainstore for data persistence

  - **Threats**:
    - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
      - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
    - BT-T-01: An attacker could attempt to modify data in transit or at rest
      - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

- **Realtime Service Container**: A Docker container that receives write events and publishes to subscribers with an ephemeral Azure Cache for Redis instance
  - **Threats**:
    - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
      - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
    - BT-D-01: The API service could be overwhelmed with excessive requests
      - **Mitigations**: Limit access to the API service to only the necessary IP addresses. Use a DDOS protection service.
    - BT-E-01: An attacker could attempt to bypass role-based access controls to access data they shouldn't have permission to view
      - **Mitigations**: Ensure that each user independently has the least privilege required to perform their role. Do not proliferate admin accounts or their API keys.

### Control Plane Boundary

- **Clerk**: User identity and SSO with less on Clerk

- **Vercel**: Web application and JavaScript frontend. Handles authentication and RBAC.

- **Supabase**: Metadata is stored on Supabase, a PostgreSQL database which also lives in the control plane

### Private Subnets Boundary

- **API Service Container**: A Docker container that handles API requests, authentication, logging data, writing experiments, and functions as a LLM call router

  - **Threats**:
    - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
      - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
    - BT-D-01: The API service could be overwhelmed with excessive requests
      - **Mitigations**: Limit access to the API service to only the necessary IP addresses. Use a DDOS protection service.
    - BT-E-01: An attacker could attempt to bypass role-based access controls to access data they shouldn't have permission to view
      - **Mitigations**: Ensure that each user independently has the least privilege required to perform their role. Do not proliferate admin accounts or their API keys.

- **Postgres Database**: Azure Database for PostgreSQL used for metadata storage, data reads, and analytics

  - **Threats**:
    - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
      - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
    - BT-T-01: An attacker could attempt to modify data in transit or at rest
      - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
    - BT-R-01: An attacker could modify or delete logs to cover their tracks
      - **Mitigations**: Proxy all network traffic to the control plane and use audit headers (a Braintrust feature) to independently audit log all requests.

- **Redis Cache**: Azure Cache for Redis used for caching and session management

  - **Threats**:
    - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
      - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
    - BT-T-01: An attacker could attempt to modify data in transit or at rest
      - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

- **Brainstore Container**: A separate Docker container that provides search and analytics capabilities
  - **Threats**:
    - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
      - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
    - BT-D-01: The API service could be overwhelmed with excessive requests
      - **Mitigations**: Limit access to the API service to only the necessary IP addresses. Use a DDOS protection service.
    - BT-E-01: An attacker could attempt to bypass role-based access controls to access data they shouldn't have permission to view
      - **Mitigations**: Ensure that each user independently has the least privilege required to perform their role. Do not proliferate admin accounts or their API keys.

## External Entities

## Actors

## Data Flows

### User Browser → Vercel

- **Description**: User's browser loads and interacts with the web UI
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
    - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

### User Browser → Clerk

- **Description**: User's browser authenticates with Clerk
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
    - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

### User Browser → API Service Container

- **Description**: User's browser communicates directly with the data plane API
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
    - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

### Realtime Service Container → User Browser

- **Description**: Realtime service sends events to subscribed browser clients
- **Protocol**: WebSocket
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
  - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
    - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.

### API Service Container → Postgres Database

- **Description**: API Service communicates with Postgres Database
- **Protocol**: PostgreSQL
- **Port**: 5432
- **Encrypted**: True
- **Data**: Database queries and results
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### API Service Container → Redis Cache

- **Description**: API Service uses Redis for caching
- **Protocol**: Redis Protocol
- **Port**: 6379
- **Encrypted**: True
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### API Service Container → Brainstore Container

- **Description**: API Service sends data to Brainstore for analytics and search
- **Protocol**: HTTP
- **Port**: 80
- **Encrypted**: False
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### Brainstore Container → Azure Storage

- **Description**: Brainstore stores and retrieves data from Azure Storage
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### SDK Client → API Service Container

- **Description**: SDK clients communicate with the data plane
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
    - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

### API Service Container → Realtime Service Container

- **Description**: API Service publishes events to the realtime service
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### SDK Client → Vercel

- **Description**: SDK clients communicate with the control plane to discover their organizations, API URL, etc.
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users
    - **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.

### Vercel → Supabase

- **Description**: Vercel stores and retrieves metadata from Supabase PostgreSQL database
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Data**: User metadata, project settings, organization data
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### API Service Container → Vercel

- **Description**: Data plane reaches out to control plane to perform RBAC/auth checks
- **Protocol**: HTTPS
- **Port**: 443
- **Encrypted**: True
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-S-02: An attacker might try to spoof the control plane during auth checks
    - **Mitigations**: Use the BRAINTRUST_APP_URL variable to force SDK communication to go through the data plane, which can proxy control plane requests in a trusted manner.
  - BT-E-01: An attacker could attempt to bypass role-based access controls to access data they shouldn't have permission to view
    - **Mitigations**: Ensure that each user independently has the least privilege required to perform their role. Do not proliferate admin accounts or their API keys.

### Brainstore Container → Postgres Database

- **Description**: Brainstore reads data and stores metadata in Postgres Database
- **Protocol**: PostgreSQL
- **Port**: 5432
- **Encrypted**: True
- **Data**: Data reads and metadata storage
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

### Brainstore Container → Redis Cache

- **Description**: Brainstore uses Redis for distributed locking
- **Protocol**: Redis Protocol
- **Port**: 6379
- **Encrypted**: True
- **Threats**:
  - BT-T-01: An attacker could attempt to modify data in transit or at rest
    - **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
  - BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage
    - **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.

## Summary of Threats

### BT-D-01: The API service could be overwhelmed with excessive requests

- **Mitigations**: Limit access to the API service to only the necessary IP addresses. Use a DDOS protection service.
- **Affected Components**:
  - API Service Container
  - Brainstore Container
  - Realtime Service Container

### BT-E-01: An attacker could attempt to bypass role-based access controls to access data they shouldn't have permission to view

- **Mitigations**: Ensure that each user independently has the least privilege required to perform their role. Do not proliferate admin accounts or their API keys.
- **Affected Components**:
  - API Service Container
  - Brainstore Container
  - Realtime Service Container
  - Data plane reaches out to control plane to perform RBAC/auth checks
- **Affected Data Flows**:
  - API Service Container → Vercel

### BT-I-01: Sensitive data could be exposed through misconfigured access controls or unencrypted storage

- **Mitigations**: Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.
- **Affected Components**:
  - Postgres Database
  - Redis Cache
  - Brainstore Container
  - Azure Storage
  - Realtime Service Container
  - Realtime service sends events to subscribed browser clients
  - API Service communicates with Postgres Database
  - API Service uses Redis for caching
  - API Service sends data to Brainstore for analytics and search
  - Brainstore stores and retrieves data from Azure Storage
  - API Service publishes events to the realtime service
  - Vercel stores and retrieves metadata from Supabase PostgreSQL database
  - Brainstore reads data and stores metadata in Postgres Database
  - Brainstore uses Redis for distributed locking
- **Affected Data Flows**:
  - Realtime Service Container → User Browser
  - API Service Container → Postgres Database
  - API Service Container → Redis Cache
  - API Service Container → Brainstore Container
  - Brainstore Container → Azure Storage
  - API Service Container → Realtime Service Container
  - Vercel → Supabase
  - Brainstore Container → Postgres Database
  - Brainstore Container → Redis Cache

### BT-R-01: An attacker could modify or delete logs to cover their tracks

- **Mitigations**: Proxy all network traffic to the control plane and use audit headers (a Braintrust feature) to independently audit log all requests.
- **Affected Components**:
  - Postgres Database

### BT-S-01: An attacker could steal authentication tokens to impersonate legitimate users

- **Mitigations**: Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.
- **Affected Components**:
  - API Service Container
  - User's browser loads and interacts with the web UI
  - User's browser authenticates with Clerk
  - User's browser communicates directly with the data plane API
  - Realtime service sends events to subscribed browser clients
  - SDK clients communicate with the data plane
  - SDK clients communicate with the control plane to discover their organizations, API URL, etc.
- **Affected Data Flows**:
  - User Browser → Vercel
  - User Browser → Clerk
  - User Browser → API Service Container
  - Realtime Service Container → User Browser
  - SDK Client → API Service Container
  - SDK Client → Vercel

### BT-S-02: An attacker might try to spoof the control plane during auth checks

- **Mitigations**: Use the BRAINTRUST_APP_URL variable to force SDK communication to go through the data plane, which can proxy control plane requests in a trusted manner.
- **Affected Components**:
  - Data plane reaches out to control plane to perform RBAC/auth checks
- **Affected Data Flows**:
  - API Service Container → Vercel

### BT-T-01: An attacker could attempt to modify data in transit or at rest

- **Mitigations**: Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.
- **Affected Components**:
  - Postgres Database
  - Redis Cache
  - Azure Storage
  - User's browser loads and interacts with the web UI
  - User's browser authenticates with Clerk
  - User's browser communicates directly with the data plane API
  - Realtime service sends events to subscribed browser clients
  - API Service communicates with Postgres Database
  - API Service uses Redis for caching
  - API Service sends data to Brainstore for analytics and search
  - Brainstore stores and retrieves data from Azure Storage
  - SDK clients communicate with the data plane
  - API Service publishes events to the realtime service
  - SDK clients communicate with the control plane to discover their organizations, API URL, etc.
  - Vercel stores and retrieves metadata from Supabase PostgreSQL database
  - Data plane reaches out to control plane to perform RBAC/auth checks
  - Brainstore reads data and stores metadata in Postgres Database
  - Brainstore uses Redis for distributed locking
- **Affected Data Flows**:
  - User Browser → Vercel
  - User Browser → Clerk
  - User Browser → API Service Container
  - Realtime Service Container → User Browser
  - API Service Container → Postgres Database
  - API Service Container → Redis Cache
  - API Service Container → Brainstore Container
  - Brainstore Container → Azure Storage
  - SDK Client → API Service Container
  - API Service Container → Realtime Service Container
  - SDK Client → Vercel
  - Vercel → Supabase
  - API Service Container → Vercel
  - Brainstore Container → Postgres Database
  - Brainstore Container → Redis Cache
