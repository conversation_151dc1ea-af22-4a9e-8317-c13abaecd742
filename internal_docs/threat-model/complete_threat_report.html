<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>complete_threat_report</title>
  <style>
    html {
      color: #1a1a1a;
      background-color: #fdfdfd;
    }
    body {
      margin: 0 auto;
      max-width: 36em;
      padding-left: 50px;
      padding-right: 50px;
      padding-top: 50px;
      padding-bottom: 50px;
      hyphens: auto;
      overflow-wrap: break-word;
      text-rendering: optimizeLegibility;
      font-kerning: normal;
    }
    @media (max-width: 600px) {
      body {
        font-size: 0.9em;
        padding: 12px;
      }
      h1 {
        font-size: 1.8em;
      }
    }
    @media print {
      html {
        background-color: white;
      }
      body {
        background-color: transparent;
        color: black;
        font-size: 12pt;
      }
      p, h2, h3 {
        orphans: 3;
        widows: 3;
      }
      h2, h3, h4 {
        page-break-after: avoid;
      }
    }
    p {
      margin: 1em 0;
    }
    a {
      color: #1a1a1a;
    }
    a:visited {
      color: #1a1a1a;
    }
    img {
      max-width: 100%;
    }
    svg {
      height: auto;
      max-width: 100%;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.4em;
    }
    h5, h6 {
      font-size: 1em;
      font-style: italic;
    }
    h6 {
      font-weight: normal;
    }
    ol, ul {
      padding-left: 1.7em;
      margin-top: 1em;
    }
    li > ol, li > ul {
      margin-top: 0;
    }
    blockquote {
      margin: 1em 0 1em 1.7em;
      padding-left: 1em;
      border-left: 2px solid #e6e6e6;
      color: #606060;
    }
    code {
      font-family: Menlo, Monaco, Consolas, 'Lucida Console', monospace;
      font-size: 85%;
      margin: 0;
      hyphens: manual;
    }
    pre {
      margin: 1em 0;
      overflow: auto;
    }
    pre code {
      padding: 0;
      overflow: visible;
      overflow-wrap: normal;
    }
    .sourceCode {
     background-color: transparent;
     overflow: visible;
    }
    hr {
      border: none;
      border-top: 1px solid #1a1a1a;
      height: 1px;
      margin: 1em 0;
    }
    table {
      margin: 1em 0;
      border-collapse: collapse;
      width: 100%;
      overflow-x: auto;
      display: block;
      font-variant-numeric: lining-nums tabular-nums;
    }
    table caption {
      margin-bottom: 0.75em;
    }
    tbody {
      margin-top: 0.5em;
      border-top: 1px solid #1a1a1a;
      border-bottom: 1px solid #1a1a1a;
    }
    th {
      border-top: 1px solid #1a1a1a;
      padding: 0.25em 0.5em 0.25em 0.5em;
    }
    td {
      padding: 0.125em 0.5em 0.25em 0.5em;
    }
    header {
      margin-bottom: 4em;
      text-align: center;
    }
    #TOC li {
      list-style: none;
    }
    #TOC ul {
      padding-left: 1.3em;
    }
    #TOC > ul {
      padding-left: 0;
    }
    #TOC a:not(:hover) {
      text-decoration: none;
    }
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
  </style>
</head>
<body>
<h1 id="braintrust-infrastructure-threat-model">Braintrust
infrastructure threat model</h1>
<p>This threat model describes the Braintrust hybrid deployment, which
includes a control plane (deployed globally by Braintrust) and data
plane (deployed in customer’s Azure account). The data plane includes
critical components such as the API Service (Docker container), Postgres
Database, Redis Cache, and Brainstore (Docker container) for event
storage and analytics, all deployed in Azure. The control plane hosts
the external UI (Vercel), auth (Clerk), and metadata (Supabase). Users’
browsers and SDK clients communicate directly with both the control
plane and data plane. The data plane reaches out to the control plane
for RBAC/auth checks. This architecture follows best practices as
described in <a
href="https://www.braintrust.dev/docs/guides/self-hosting/advanced">Braintrust
Advanced Self-hosting</a> and <a
href="https://owasp.org/www-project-pytm/">OWASP PyTM</a>.</p>
<h2 id="data-flow-diagram">Data Flow Diagram</h2>
<figure>
<img src="threat_model_diagram.png" alt="Threat Model Diagram" />
<figcaption aria-hidden="true">Threat Model Diagram</figcaption>
</figure>
<h2 id="architecture-components">Architecture Components</h2>
<h3 id="data-plane-boundary">Data Plane Boundary</h3>
<ul>
<li><p><strong>Private Subnets</strong>:</p></li>
<li><p><strong>Azure Storage</strong>: Azure Blob Storage used by
Brainstore for data persistence</p>
<ul>
<li><strong>Threats</strong>:
<ul>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul></li>
<li><p><strong>Realtime Service Container</strong>: A Docker container
that receives write events and publishes to subscribers with an
ephemeral Azure Cache for Redis instance</p>
<ul>
<li><strong>Threats</strong>:
<ul>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
<li>BT-D-01: The API service could be overwhelmed with excessive
requests
<ul>
<li><strong>Mitigations</strong>: Limit access to the API service to
only the necessary IP addresses. Use a DDOS protection service.</li>
</ul></li>
<li>BT-E-01: An attacker could attempt to bypass role-based access
controls to access data they shouldn’t have permission to view
<ul>
<li><strong>Mitigations</strong>: Ensure that each user independently
has the least privilege required to perform their role. Do not
proliferate admin accounts or their API keys.</li>
</ul></li>
</ul></li>
</ul></li>
</ul>
<h3 id="control-plane-boundary">Control Plane Boundary</h3>
<ul>
<li><p><strong>Clerk</strong>: User identity and SSO with less on
Clerk</p></li>
<li><p><strong>Vercel</strong>: Web application and JavaScript frontend.
Handles authentication and RBAC.</p></li>
<li><p><strong>Supabase</strong>: Metadata is stored on Supabase, a
PostgreSQL database which also lives in the control plane</p></li>
</ul>
<h3 id="private-subnets-boundary">Private Subnets Boundary</h3>
<ul>
<li><strong>API Service Container</strong>: A Docker container that
handles API requests, authentication, logging data, writing experiments,
and functions as a LLM call router
<ul>
<li><strong>Threats</strong>:
<ul>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
<li>BT-D-01: The API service could be overwhelmed with excessive
requests
<ul>
<li><strong>Mitigations</strong>: Limit access to the API service to
only the necessary IP addresses. Use a DDOS protection service.</li>
</ul></li>
<li>BT-E-01: An attacker could attempt to bypass role-based access
controls to access data they shouldn’t have permission to view
<ul>
<li><strong>Mitigations</strong>: Ensure that each user independently
has the least privilege required to perform their role. Do not
proliferate admin accounts or their API keys.</li>
</ul></li>
</ul></li>
</ul></li>
<li><strong>Postgres Database</strong>: Azure Database for PostgreSQL
used for metadata storage, data reads, and analytics
<ul>
<li><strong>Threats</strong>:
<ul>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-R-01: An attacker could modify or delete logs to cover their
tracks
<ul>
<li><strong>Mitigations</strong>: Proxy all network traffic to the
control plane and use audit headers (a Braintrust feature) to
independently audit log all requests.</li>
</ul></li>
</ul></li>
</ul></li>
<li><strong>Redis Cache</strong>: Azure Cache for Redis used for caching
and session management
<ul>
<li><strong>Threats</strong>:
<ul>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul></li>
<li><strong>Brainstore Container</strong>: A separate Docker container
that provides search and analytics capabilities
<ul>
<li><strong>Threats</strong>:
<ul>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
<li>BT-D-01: The API service could be overwhelmed with excessive
requests
<ul>
<li><strong>Mitigations</strong>: Limit access to the API service to
only the necessary IP addresses. Use a DDOS protection service.</li>
</ul></li>
<li>BT-E-01: An attacker could attempt to bypass role-based access
controls to access data they shouldn’t have permission to view
<ul>
<li><strong>Mitigations</strong>: Ensure that each user independently
has the least privilege required to perform their role. Do not
proliferate admin accounts or their API keys.</li>
</ul></li>
</ul></li>
</ul></li>
</ul>
<h2 id="external-entities">External Entities</h2>
<h2 id="actors">Actors</h2>
<h2 id="data-flows">Data Flows</h2>
<h3 id="user-browser-vercel">User Browser → Vercel</h3>
<ul>
<li><strong>Description</strong>: User’s browser loads and interacts
with the web UI</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="user-browser-clerk">User Browser → Clerk</h3>
<ul>
<li><strong>Description</strong>: User’s browser authenticates with
Clerk</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="user-browser-api-service-container">User Browser → API Service
Container</h3>
<ul>
<li><strong>Description</strong>: User’s browser communicates directly
with the data plane API</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="realtime-service-container-user-browser">Realtime Service
Container → User Browser</h3>
<ul>
<li><strong>Description</strong>: Realtime service sends events to
subscribed browser clients</li>
<li><strong>Protocol</strong>: WebSocket</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="api-service-container-postgres-database">API Service Container →
Postgres Database</h3>
<ul>
<li><strong>Description</strong>: API Service communicates with Postgres
Database</li>
<li><strong>Protocol</strong>: PostgreSQL</li>
<li><strong>Port</strong>: 5432</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Data</strong>: Database queries and results</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="api-service-container-redis-cache">API Service Container → Redis
Cache</h3>
<ul>
<li><strong>Description</strong>: API Service uses Redis for
caching</li>
<li><strong>Protocol</strong>: Redis Protocol</li>
<li><strong>Port</strong>: 6379</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="api-service-container-brainstore-container">API Service
Container → Brainstore Container</h3>
<ul>
<li><strong>Description</strong>: API Service sends data to Brainstore
for analytics and search</li>
<li><strong>Protocol</strong>: HTTP</li>
<li><strong>Port</strong>: 80</li>
<li><strong>Encrypted</strong>: False</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="brainstore-container-azure-storage">Brainstore Container → Azure
Storage</h3>
<ul>
<li><strong>Description</strong>: Brainstore stores and retrieves data
from Azure Storage</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="sdk-client-api-service-container">SDK Client → API Service
Container</h3>
<ul>
<li><strong>Description</strong>: SDK clients communicate with the data
plane</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="api-service-container-realtime-service-container">API Service
Container → Realtime Service Container</h3>
<ul>
<li><strong>Description</strong>: API Service publishes events to the
realtime service</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="sdk-client-vercel">SDK Client → Vercel</h3>
<ul>
<li><strong>Description</strong>: SDK clients communicate with the
control plane to discover their organizations, API URL, etc.</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-S-01: An attacker could steal authentication tokens to
impersonate legitimate users
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
</ul></li>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="vercel-supabase">Vercel → Supabase</h3>
<ul>
<li><strong>Description</strong>: Vercel stores and retrieves metadata
from Supabase PostgreSQL database</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Data</strong>: User metadata, project settings, organization
data</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="api-service-container-vercel">API Service Container →
Vercel</h3>
<ul>
<li><strong>Description</strong>: Data plane reaches out to control
plane to perform RBAC/auth checks</li>
<li><strong>Protocol</strong>: HTTPS</li>
<li><strong>Port</strong>: 443</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-S-02: An attacker might try to spoof the control plane during
auth checks
<ul>
<li><strong>Mitigations</strong>: Use the BRAINTRUST_APP_URL variable to
force SDK communication to go through the data plane, which can proxy
control plane requests in a trusted manner.</li>
</ul></li>
<li>BT-E-01: An attacker could attempt to bypass role-based access
controls to access data they shouldn’t have permission to view
<ul>
<li><strong>Mitigations</strong>: Ensure that each user independently
has the least privilege required to perform their role. Do not
proliferate admin accounts or their API keys.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="brainstore-container-postgres-database">Brainstore Container →
Postgres Database</h3>
<ul>
<li><strong>Description</strong>: Brainstore reads data and stores
metadata in Postgres Database</li>
<li><strong>Protocol</strong>: PostgreSQL</li>
<li><strong>Port</strong>: 5432</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Data</strong>: Data reads and metadata storage</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="brainstore-container-redis-cache">Brainstore Container → Redis
Cache</h3>
<ul>
<li><strong>Description</strong>: Brainstore uses Redis for distributed
locking</li>
<li><strong>Protocol</strong>: Redis Protocol</li>
<li><strong>Port</strong>: 6379</li>
<li><strong>Encrypted</strong>: True</li>
<li><strong>Threats</strong>:
<ul>
<li>BT-T-01: An attacker could attempt to modify data in transit or at
rest
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
</ul></li>
<li>BT-I-01: Sensitive data could be exposed through misconfigured
access controls or unencrypted storage
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
</ul></li>
</ul></li>
</ul>
<h2 id="summary-of-threats">Summary of Threats</h2>
<h3
id="bt-d-01-the-api-service-could-be-overwhelmed-with-excessive-requests">BT-D-01:
The API service could be overwhelmed with excessive requests</h3>
<ul>
<li><strong>Mitigations</strong>: Limit access to the API service to
only the necessary IP addresses. Use a DDOS protection service.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>API Service Container</li>
<li>Brainstore Container</li>
<li>Realtime Service Container</li>
</ul></li>
</ul>
<h3
id="bt-e-01-an-attacker-could-attempt-to-bypass-role-based-access-controls-to-access-data-they-shouldnt-have-permission-to-view">BT-E-01:
An attacker could attempt to bypass role-based access controls to access
data they shouldn’t have permission to view</h3>
<ul>
<li><strong>Mitigations</strong>: Ensure that each user independently
has the least privilege required to perform their role. Do not
proliferate admin accounts or their API keys.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>API Service Container</li>
<li>Brainstore Container</li>
<li>Realtime Service Container</li>
<li>Data plane reaches out to control plane to perform RBAC/auth
checks</li>
</ul></li>
<li><strong>Affected Data Flows</strong>:
<ul>
<li>API Service Container → Vercel</li>
</ul></li>
</ul>
<h3
id="bt-i-01-sensitive-data-could-be-exposed-through-misconfigured-access-controls-or-unencrypted-storage">BT-I-01:
Sensitive data could be exposed through misconfigured access controls or
unencrypted storage</h3>
<ul>
<li><strong>Mitigations</strong>: Use role-based access control (RBAC)
to control access to sensitive data. Use Azure Storage Service
Encryption to encrypt data at rest. Use a retention policy to delete
data after it is no longer needed.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>Postgres Database</li>
<li>Redis Cache</li>
<li>Brainstore Container</li>
<li>Azure Storage</li>
<li>Realtime Service Container</li>
<li>Realtime service sends events to subscribed browser clients</li>
<li>API Service communicates with Postgres Database</li>
<li>API Service uses Redis for caching</li>
<li>API Service sends data to Brainstore for analytics and search</li>
<li>Brainstore stores and retrieves data from Azure Storage</li>
<li>API Service publishes events to the realtime service</li>
<li>Vercel stores and retrieves metadata from Supabase PostgreSQL
database</li>
<li>Brainstore reads data and stores metadata in Postgres Database</li>
<li>Brainstore uses Redis for distributed locking</li>
</ul></li>
<li><strong>Affected Data Flows</strong>:
<ul>
<li>Realtime Service Container → User Browser</li>
<li>API Service Container → Postgres Database</li>
<li>API Service Container → Redis Cache</li>
<li>API Service Container → Brainstore Container</li>
<li>Brainstore Container → Azure Storage</li>
<li>API Service Container → Realtime Service Container</li>
<li>Vercel → Supabase</li>
<li>Brainstore Container → Postgres Database</li>
<li>Brainstore Container → Redis Cache</li>
</ul></li>
</ul>
<h3
id="bt-r-01-an-attacker-could-modify-or-delete-logs-to-cover-their-tracks">BT-R-01:
An attacker could modify or delete logs to cover their tracks</h3>
<ul>
<li><strong>Mitigations</strong>: Proxy all network traffic to the
control plane and use audit headers (a Braintrust feature) to
independently audit log all requests.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>Postgres Database</li>
</ul></li>
</ul>
<h3
id="bt-s-01-an-attacker-could-steal-authentication-tokens-to-impersonate-legitimate-users">BT-S-01:
An attacker could steal authentication tokens to impersonate legitimate
users</h3>
<ul>
<li><strong>Mitigations</strong>: Use SSO with short-lived tokens. Avoid
using API keys for authentication. Some customers prevent API keys
altogether and use service-level authentication. Braintrust allows you
to do this by resolving your own auth and using impersonation.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>API Service Container</li>
<li>User’s browser loads and interacts with the web UI</li>
<li>User’s browser authenticates with Clerk</li>
<li>User’s browser communicates directly with the data plane API</li>
<li>Realtime service sends events to subscribed browser clients</li>
<li>SDK clients communicate with the data plane</li>
<li>SDK clients communicate with the control plane to discover their
organizations, API URL, etc.</li>
</ul></li>
<li><strong>Affected Data Flows</strong>:
<ul>
<li>User Browser → Vercel</li>
<li>User Browser → Clerk</li>
<li>User Browser → API Service Container</li>
<li>Realtime Service Container → User Browser</li>
<li>SDK Client → API Service Container</li>
<li>SDK Client → Vercel</li>
</ul></li>
</ul>
<h3
id="bt-s-02-an-attacker-might-try-to-spoof-the-control-plane-during-auth-checks">BT-S-02:
An attacker might try to spoof the control plane during auth checks</h3>
<ul>
<li><strong>Mitigations</strong>: Use the BRAINTRUST_APP_URL variable to
force SDK communication to go through the data plane, which can proxy
control plane requests in a trusted manner.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>Data plane reaches out to control plane to perform RBAC/auth
checks</li>
</ul></li>
<li><strong>Affected Data Flows</strong>:
<ul>
<li>API Service Container → Vercel</li>
</ul></li>
</ul>
<h3
id="bt-t-01-an-attacker-could-attempt-to-modify-data-in-transit-or-at-rest">BT-T-01:
An attacker could attempt to modify data in transit or at rest</h3>
<ul>
<li><strong>Mitigations</strong>: Host the data plane behind a TLS-only
endpoint. Run the data plane components in a private subnet within a
VNet.</li>
<li><strong>Affected Components</strong>:
<ul>
<li>Postgres Database</li>
<li>Redis Cache</li>
<li>Azure Storage</li>
<li>User’s browser loads and interacts with the web UI</li>
<li>User’s browser authenticates with Clerk</li>
<li>User’s browser communicates directly with the data plane API</li>
<li>Realtime service sends events to subscribed browser clients</li>
<li>API Service communicates with Postgres Database</li>
<li>API Service uses Redis for caching</li>
<li>API Service sends data to Brainstore for analytics and search</li>
<li>Brainstore stores and retrieves data from Azure Storage</li>
<li>SDK clients communicate with the data plane</li>
<li>API Service publishes events to the realtime service</li>
<li>SDK clients communicate with the control plane to discover their
organizations, API URL, etc.</li>
<li>Vercel stores and retrieves metadata from Supabase PostgreSQL
database</li>
<li>Data plane reaches out to control plane to perform RBAC/auth
checks</li>
<li>Brainstore reads data and stores metadata in Postgres Database</li>
<li>Brainstore uses Redis for distributed locking</li>
</ul></li>
<li><strong>Affected Data Flows</strong>:
<ul>
<li>User Browser → Vercel</li>
<li>User Browser → Clerk</li>
<li>User Browser → API Service Container</li>
<li>Realtime Service Container → User Browser</li>
<li>API Service Container → Postgres Database</li>
<li>API Service Container → Redis Cache</li>
<li>API Service Container → Brainstore Container</li>
<li>Brainstore Container → Azure Storage</li>
<li>SDK Client → API Service Container</li>
<li>API Service Container → Realtime Service Container</li>
<li>SDK Client → Vercel</li>
<li>Vercel → Supabase</li>
<li>API Service Container → Vercel</li>
<li>Brainstore Container → Postgres Database</li>
<li>Brainstore Container → Redis Cache</li>
</ul></li>
</ul>
</body>
</html>
