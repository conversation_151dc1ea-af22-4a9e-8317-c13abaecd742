# Data Object Model

## Overview

In this document, we describe the organization and representation of various
data objects in braintrust, such as experiments, datasets, traces, etc. We do
not cover in detail the metadata-level information, currently managed centrally
in supabase. Just the structure of the actual data, managed either in our
central multi-tenant cloud formation or in the customer’s own VPC.

This document should be kept up to date as we make significant changes /
additions to the data model.

## Database Schema

The database schema is maintained in a [sql](/api-schema/schema.sql) file. We
have a single table `logs` which stores all the data, and some auxiliary tables
to manage migrations. Each row is stored as a JSON blob in the `data` field. All
other fields (excluding `sequence_id` and `row_created`) are generated as a
function of the `data` blob. This design gives us some flexibility to evolve the
schema without running frequent migrations. A few considerations for generated
fields:

- Generating new columns is costly from a migrations perspective, so we should
  be careful about changing them frequently.

- It is important to remember that the source of truth is still the `data` blob,
  which is what is actually sent to consumers. So any transformations to the
  extracted fields (such as `COALESCE`) will not make it to consumers. Thus
  generated columns should not be used as a way to "backfill" data.

- The main benefit of generated columns is to speed up queries and indices over
  specific parts of the data blob, as they are essentially a pre-computed
  expression. Given the cost of adding new generated columns, consider searching
  and indexing over expressions computed dynamically (e.g. see the indices over
  `COALESCE(root_span_id, id)`).

## Common Concepts

This section describes parts of the data model common to all objects.

### Row IDs and Transaction IDs

Each data object consists of the subset of rows in `logs` with the same
`object_id` (defined by the `make_object_id` function). Each logical row has a
unique `id` within its data object, and is versioned by a globally-increasing
`_xact_id`. The latest "logical" version of the data object consists of the
latest version of each unique row `id`. Or in SQL:

```sql
SELECT DISTINCT ON (id)
    data,
    id,
    _xact_id
FROM
    logs
WHERE
    make_object_id([logs_columns]) = make_object_id([query_args])
ORDER BY
    id, _xact_id DESC
```

Updates to the data object simply append rows with greater transaction IDs. By
maintaining the full history, we can trivially version the dataset by adding a
filter `_xact_id <= [version_id]` to the above query.

### Span IDs

Orthogonally from the concept of individually-versioned rows, we maintain a
notion of linkages between rows called _traces_. Traces are useful for workflows
which want to keep track of intermediate results in a large nested/chained
computation. Formally, a trace is a single-root DAG of rows. A _span_ is a
single row within a trace.

We maintain a UUID identifier `span_id` on each row separately from the row
`id`. Relationships between rows in the trace are maintained in a `span_parents`
field on each row. This design allows us to distinguish between updating a row
as part of the same trace and updating a row as part of an independent trace.

To illustrate, imagine we only stored an `id`, and `span_parents` consisted of
references to parent row IDs. We start with the following data object (of type
`[(id, _xact_id, span_parents, data)]`):

```python
[
    ('row0', 0, [], 'A'),
    ('row1', 0, ['row0'], 'B'),
]
```

The data object currently has a trace `(row0, A) -> (row1, B)`. We can update
the contents of `row0` in that trace by creating a new row:

```python
[
    ('row0', 0, [], 'A'),
    ('row1', 0, ['row0'], 'B'),
    ('row0', 1, [], 'C'),
]
```

We would now have the data object `(row0, C) -> (row1, B)`. But there is no way
to update `row0` with an entirely new trace, because `row1` will always be
linked to the latest version of `row0`. If we introduce an independent
`span_id`, we can distinguish between the two update operations. Imagine we
started with the following data object (of type `[(id, _xact_id, span_id, span_parents, data)]`):

```python
[
    ('row0', 0, 'span_id0', [], 'A'),
    ('row1', 0, 'span_id1', ['span_id0'], 'B'),
]
```

We can still update the contents of `row0` in that trace:

```python
[
    ('row0', 0, 'span_id0', [], 'A'),
    ('row1', 0, 'span_id1', ['span_id0'], 'B'),
    ('row0', 1, 'span_id0', [], 'C'),
]
```

But we can also easily create a new version of the row as an independent trace:

```python
[
    ('row0', 0, 'span_id0', [], 'A'),
    ('row1', 0, 'span_id1', ['span_id0'], 'B'),
    ('row0', 1, 'span_id0', [], 'C'),
    ('row0', 2, 'span_id2', [], 'D')
]
```

Now when we query for the data object, we’ll get `(row0, D)` and `(row1, B)`,
but there will be no parent-child relationship between them, and we can safely
ignore `row1` , as its `span_parents` will not be present among any of the rows’
`span_ids`.

Finally, in order to allow quickly grouping spans as part of a single trace, we
store a `root_span_id` in each row, which is the `span_id` of the root of the
trace that the span belongs to.

## Data Object Types

Each data object type has a leaf-level unique ID, and some sort of parent object
ID. These IDs are maintained in the app-level metadata DB.

- Experiments: uniquely identified by an `experiment_id`, within a parent
  `project_id`.

- Datasets: uniquely identified by an `dataset_id`, within a parent
  `project_id`.

- Prompt sessions: uniquely identified by a `prompt_session_id` within a parent
  `org_id`.

- Logs: uniquely identified by a `log_id` within a parent `project_id`.
  Currently we only support project-global logs, where the `log_id` is always
  `'g'`. We still maintain a separate `log_id` for a few reasons:

  - Lets us positively identify rows as having type "log". We could use the lack
    of other IDs, but having a log ID seems cleaner.

  - In the future, if we wish to have multiple logs per project, we can use the
    `log_id` to distinguish them.

## Data Ingestion

Rows are inserted in batches, where the main entry point is the `_run_log_data`
function. At a high level, this function does several things:

- Sanitizes and validates the rows coming in — fill in missing `ids`, make sure
  the user has permission to update the data objects they are touching, etc.

- Performs a resource check — some users have limits on how many rows of
  different object types they can insert, so we must check and update that prior
  to inserting.

- Inserts the rows into the database

- Send the (encrypted) rows to our central vercel deployment, which broadcasts
  them to all active users of the frontend, so they can see live updates.

Here we will only discuss the row insertion process. Notably, we support two
modes of insertion: merge and replace. A merge allows users to log the contents
of a row incrementally, so that each incremental update takes the latest version
of the row, deep-merges the JSON with the new row, and re-inserts the
fully-merged row as the latest version. A replace simply inserts the new row
as-is.

The first step in insertion is merging the rows (see `merge_row_batch`). The
goal of merging is to end up with just one instance of each row `id` per data
object in the batch of rows, in case someone published multiple updates to a row
within a batch. This is necessary because we assign the same transaction ID to
all rows in the batch, so if there are multiple rows with the same `id` and
`_xact_id` in the same data object, they cannot be ordered at query time.

Next we insert the rows into the DB (see `log_pg`), first by grabbing the latest
version of each row with the same `(object_id, id)` as any row we wish to merge,
then merging the rows in python, and finally inserting the merged batch of rows
into the DB. Empirically, we experimented with doing the JSON merge in python
and in a postgres UDF, and found the python version to be slightly more
performant, though there is still room for experimentation (e.g. trying a
javascript UDF).

## Queries

### Scanning a Data Object

Function: `pg_scan_objects_query`

#### Inputs

- `object_type`: Data object type.

- `object_ids`: The object IDs of this object type to scan. We allow multiple
  object ids for queries that need to collect rows across multiple data objects
  (e.g. scanning all experiments in a project or all logs in an org).

- `version`: The maximum transaction ID to consider, if we wish to read an older
  version of the data.

- `search`: Additional parameters for "paginated" search across a large data
  object. We cover searching in more detail separately.

#### Outputs

- A scan query which emits a set of rows with unique `(object_id, id)`.

##### `unique_id_per_xact_id`

We cannot have multiple instances of the same row `id` within a single
transaction (as alluded to in the "Data Ingestion" section), because ordering
the rows would not be possible.

This is enforced by the unique index constraint on `(object_id, id, _xact_id)`.

### Collecting Rows Within a Trace

Once we have completed the object scan query, and have a versioned view of a
data object (or set of data objects), we often want to query relationships
between rows in a trace. For instance, we may want to collect the set of root
span rows within the object, or find the parent row of a particular span in a
trace. The former query requires searching for all rows where `is_root`, and the latter requires searching for all rows whose `span_id`
occurs in the span row’s `span_parents` list.

#### Requirements

##### `unique_logical_span_id`

We must not have the same `span_id` for different "logical" rows in the scan,
otherwise the parent of a particular span could be ambiguous. Only different
versions of the same logical row may have the same `span_id`.

This is _not_ enforced in the DB. It is unlikely to be violated because span_ids
are generated by the SDK as UUIDs, but we could enforce it by maintaining a
separate table:

```sql
CREATE TABLE enforce_unique_logical_span_id(
    id text,
    span_id text unique
)
```

Before we want to insert new rows, we can check for conflicts by querying

```sql
WITH rows_to_insert AS (
    SELECT id, span_id
    FROM unnest([data])
)
SELECT span_id
FROM rows_to_insert JOIN enforce_unique_logical_span_id USING (span_id)
WHERE rows_to_insert.id <> enforce_unique_logical_span_id.id
```

##### `immutable_span_id`

When updating a row via merging (rather than replacement), we must not change
the `span_id`, because other rows may have linkages to it that get invalidated.

This is _not_ enforced in the DB. We could enforce it at insert time, by
checking that any rows that we merge have the same `span_id` (and
`root_span_id`) as the last version of the row.

### Searching Within a Data Object

Function: `pg_scan_objects_query` (the `search.non_empty()` block).

We also support adding search predicates and pagination to the scan query.
Search was introduced to support production logging (log objects), which tend to
grow very large as they are continuously written to in production, so we cannot
realistically do all searching/pagination in the frontend. The `SearchParams`
control pagination by an offset (the `max_xact_id` and `root_span_id` ) and a
`limit`.

At a high level, paginated search works by filtering down the set rows to those
which match the predicates, and returning pages in order from latest to earliest
`_xact_id`. Because the scan query cannot return partial traces, we boil down
the page into a set of `root_span_ids`, where each `root_span_id` uniquely
identifies a trace, and then conduct the scan as before, just filtered to the
page’s `root_span_id`s.

Note that this pagination query may return the same `root_span_id` across
multiple pages, because we first pre-limit the set of matching rows before
paginating for efficiency purposes. This should not be an issue in practice,
because rows are automatically deduplicated on the frontend.

#### Requirements

[`immutable_span_id`](#immutable_span_id): If the span ID were to change across
row versions, we might return partial traces in the paginated scan query.
