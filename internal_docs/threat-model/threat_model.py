#!/usr/bin/env python3
# To run:
# pip install pytm
# python generate_complete_report.py


from pytm.pytm import (
    TM,
    Actor,
    Boundary,
    Data,
    Dataflow,
    Datastore,
    ExternalEntity,
    Process,
    Server,
    Threat,
)


# Define custom STRIDE threats
class BraintrustThreats:
    # Spoofing threats
    TOKEN_THEFT = Threat(
        SID="BT-S-01",
        description="An attacker could steal authentication tokens to impersonate legitimate users",
        stride_category="Spoofing",
        mitigations="Use SSO with short-lived tokens. Avoid using API keys for authentication. Some customers prevent API keys altogether and use service-level authentication. Braintrust allows you to do this by resolving your own auth and using impersonation.",
    )

    CONTROL_PLANE_SPOOFING = Threat(
        SID="BT-S-02",
        description="An attacker might try to spoof the control plane during auth checks",
        stride_category="Spoofing",
        mitigations="Use the BRAINTRUST_APP_URL variable to force SDK communication to go through the data plane, which can proxy control plane requests in a trusted manner.",
    )

    # Tampering threats
    DATA_TAMPERING = Threat(
        SID="BT-T-01",
        description="An attacker could attempt to modify data in transit or at rest",
        stride_category="Tampering",
        mitigations="Host the data plane behind a TLS-only endpoint. Run the data plane components in a private subnet within a VNet.",
    )

    # Repudiation threats
    LOG_TAMPERING = Threat(
        SID="BT-R-01",
        description="An attacker could modify or delete logs to cover their tracks",
        stride_category="Repudiation",
        mitigations="Proxy all network traffic to the control plane and use audit headers (a Braintrust feature) to independently audit log all requests.",
    )

    # Information Disclosure threats
    DATA_EXPOSURE = Threat(
        SID="BT-I-01",
        description="Sensitive data could be exposed through misconfigured access controls or unencrypted storage",
        stride_category="Information Disclosure",
        mitigations="Use role-based access control (RBAC) to control access to sensitive data. Use Azure Storage Service Encryption to encrypt data at rest. Use a retention policy to delete data after it is no longer needed.",
    )

    # Denial of Service threats
    API_DOS = Threat(
        SID="BT-D-01",
        description="The API service could be overwhelmed with excessive requests",
        stride_category="Denial of Service",
        mitigations="Limit access to the API service to only the necessary IP addresses. Use a DDOS protection service.",
    )

    # Elevation of Privilege threats
    RBAC_BYPASS = Threat(
        SID="BT-E-01",
        description="An attacker could attempt to bypass role-based access controls to access data they shouldn't have permission to view",
        stride_category="Elevation of Privilege",
        mitigations="Ensure that each user independently has the least privilege required to perform their role. Do not proliferate admin accounts or their API keys.",
    )


# Create a threat model for Braintrust Infrastructure
tm = TM("Braintrust infrastructure threat model")
tm.description = """This threat model describes the Braintrust hybrid deployment, which includes a control plane (deployed globally by Braintrust) and data plane (deployed in customer's Azure account).
    The data plane includes critical components such as the API Service (Docker container), Postgres Database, Redis Cache, and Brainstore (Docker container) for event storage and analytics, all deployed in Azure.
    The control plane hosts the external UI (Vercel), auth (Clerk), and metadata (Supabase).
    Users' browsers and SDK clients communicate directly with both the control plane and data plane. The data plane reaches out to the control plane for RBAC/auth checks.
    This architecture follows best practices as described in [Braintrust Advanced Self-hosting](https://www.braintrust.dev/docs/guides/self-hosting/advanced) and [OWASP PyTM](https://owasp.org/www-project-pytm/)."""

# Define boundaries
# Data Plane includes internal services and databases
data_plane = Boundary("Data Plane")
# Control Plane represents the external interface (UI, metadata) which is hosted separately
control_plane = Boundary("Control Plane")
# Private Subnets within the Data Plane (Azure Subnet)
private_subnets = Boundary("Private Subnets")
private_subnets.inBoundary = data_plane

# Data Plane elements
api_service = Server("API Service Container")
api_service.description = "A Docker container that handles API requests, authentication, logging data, writing experiments, and functions as a LLM call router"
api_service.OS = "Docker Container (Linux)"
api_service.isHardened = True
api_service.inBoundary = private_subnets
api_service.handlesResources = True
api_service.implementsAuthenticationScheme = True
api_service.implementsNonce = True
api_service.threats = [BraintrustThreats.TOKEN_THEFT, BraintrustThreats.API_DOS, BraintrustThreats.RBAC_BYPASS]

db = Datastore("Postgres Database")
db.description = "Azure Database for PostgreSQL used for metadata storage, data reads, and analytics"
db.OS = "Azure Service"
db.isHardened = True
db.isSql = True
db.inBoundary = private_subnets
db.inScope = True
db.isEncrypted = True
db.threats = [BraintrustThreats.DATA_EXPOSURE, BraintrustThreats.DATA_TAMPERING, BraintrustThreats.LOG_TAMPERING]

redis = Datastore("Redis Cache")
redis.description = "Azure Cache for Redis used for caching and session management"
redis.OS = "Azure Service"
redis.isHardened = True
redis.inBoundary = private_subnets
redis.isEncrypted = False
redis.threats = [BraintrustThreats.DATA_EXPOSURE, BraintrustThreats.DATA_TAMPERING]

# Brainstore as a separate container
brainstore_container = Server("Brainstore Container")
brainstore_container.description = "A separate Docker container that provides search and analytics capabilities"
brainstore_container.OS = "Docker Container (Linux)"
brainstore_container.isHardened = True
brainstore_container.inBoundary = private_subnets
brainstore_container.implementsAuthenticationScheme = True
brainstore_container.threats = [
    BraintrustThreats.DATA_EXPOSURE,
    BraintrustThreats.API_DOS,
    BraintrustThreats.RBAC_BYPASS,
]

# Azure Storage for Brainstore data
azure_storage = Datastore("Azure Storage")
azure_storage.description = "Azure Blob Storage used by Brainstore for data persistence"
azure_storage.OS = "Azure Service"
azure_storage.isHardened = True
azure_storage.inBoundary = data_plane
azure_storage.isEncrypted = True
azure_storage.threats = [BraintrustThreats.DATA_EXPOSURE, BraintrustThreats.DATA_TAMPERING]

# Realtime service
realtime_service = Server("Realtime Service Container")
realtime_service.description = "A Docker container that receives write events and publishes to subscribers with an ephemeral Azure Cache for Redis instance"
realtime_service.OS = "Docker Container (Linux)"
realtime_service.isHardened = True
realtime_service.inBoundary = data_plane
realtime_service.threats = [BraintrustThreats.DATA_EXPOSURE, BraintrustThreats.API_DOS, BraintrustThreats.RBAC_BYPASS]

# Control Plane elements
# Represents the externally hosted web interface/UI that handles metadata, project settings, etc.
clerk = ExternalEntity("Clerk")
clerk.description = "User identity and SSO with less on Clerk"
clerk.inBoundary = control_plane

vercel = ExternalEntity("Vercel")
vercel.description = "Web application and JavaScript frontend. Handles authentication and RBAC."
vercel.inBoundary = control_plane

supabase = ExternalEntity("Supabase")
supabase.description = "Metadata is stored on Supabase, a PostgreSQL database which also lives in the control plane"
supabase.inBoundary = control_plane

# External users and clients
sdk_client = Actor("SDK Client")
sdk_client.description = "SDK clients communicate with the data plane to log and fetch data"

# Replace the Web Client with a Browser actor to make it clearer
browser = Actor("User Browser")
browser.description = "User's web browser that communicates with both the control plane (for UI) and directly with the data plane (for data operations)"

# Define Dataflows
# Flow from Browser to Control Plane (Vercel)
flow_browser_to_vercel = Dataflow(browser, vercel, "User's browser loads and interacts with the web UI")
flow_browser_to_vercel.protocol = "HTTPS"
flow_browser_to_vercel.dstPort = 443
flow_browser_to_vercel.isEncrypted = True
flow_browser_to_vercel.threats = [BraintrustThreats.TOKEN_THEFT, BraintrustThreats.DATA_TAMPERING]

# Flow from Browser to Control Plane (Clerk)
flow_browser_to_clerk = Dataflow(browser, clerk, "User's browser authenticates with Clerk")
flow_browser_to_clerk.protocol = "HTTPS"
flow_browser_to_clerk.dstPort = 443
flow_browser_to_clerk.isEncrypted = True
flow_browser_to_clerk.threats = [BraintrustThreats.TOKEN_THEFT, BraintrustThreats.DATA_TAMPERING]

# Flow from Browser to Data Plane
flow_browser_to_api = Dataflow(browser, api_service, "User's browser communicates directly with the data plane API")
flow_browser_to_api.protocol = "HTTPS"
flow_browser_to_api.dstPort = 443
flow_browser_to_api.isEncrypted = True
flow_browser_to_api.authenticatesDestination = True
flow_browser_to_api.threats = [BraintrustThreats.TOKEN_THEFT, BraintrustThreats.DATA_TAMPERING]

# Flow from Realtime Service to Browser
flow_realtime_to_browser = Dataflow(
    realtime_service, browser, "Realtime service sends events to subscribed browser clients"
)
flow_realtime_to_browser.protocol = "WebSocket"
flow_realtime_to_browser.dstPort = 443
flow_realtime_to_browser.isEncrypted = True
flow_realtime_to_browser.threats = [
    BraintrustThreats.DATA_TAMPERING,
    BraintrustThreats.DATA_EXPOSURE,
    BraintrustThreats.TOKEN_THEFT,
]

# Flow from API Service to Postgres Database
flow_api_to_db = Dataflow(api_service, db, "API Service communicates with Postgres Database")
flow_api_to_db.protocol = "PostgreSQL"
flow_api_to_db.dstPort = 5432
flow_api_to_db.isEncrypted = True
# Create a Data object instead of using a string
db_data = Data("Database queries and results")
flow_api_to_db.data = db_data
flow_api_to_db.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from API Service to Redis Cache
flow_api_to_redis = Dataflow(api_service, redis, "API Service uses Redis for caching")
flow_api_to_redis.protocol = "Redis Protocol"
flow_api_to_redis.dstPort = 6379
flow_api_to_redis.isEncrypted = True
flow_api_to_redis.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from API Service to Brainstore Container
flow_api_to_brainstore = Dataflow(
    api_service,
    brainstore_container,
    "API Service sends data to Brainstore for analytics and search",
)
flow_api_to_brainstore.protocol = "HTTP"
flow_api_to_brainstore.dstPort = 80
flow_api_to_brainstore.isEncrypted = False
flow_api_to_brainstore.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from Brainstore Container to Azure Storage
flow_brainstore_to_storage = Dataflow(
    brainstore_container,
    azure_storage,
    "Brainstore stores and retrieves data from Azure Storage",
)
flow_brainstore_to_storage.protocol = "HTTPS"
flow_brainstore_to_storage.dstPort = 443
flow_brainstore_to_storage.isEncrypted = True
flow_brainstore_to_storage.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from SDK Client to API Service
flow_sdk_to_api = Dataflow(sdk_client, api_service, "SDK clients communicate with the data plane")
flow_sdk_to_api.protocol = "HTTPS"
flow_sdk_to_api.dstPort = 443
flow_sdk_to_api.isEncrypted = True
flow_sdk_to_api.authenticatesDestination = True
flow_sdk_to_api.threats = [BraintrustThreats.TOKEN_THEFT, BraintrustThreats.DATA_TAMPERING]

# Flow from API Service to Realtime Service
flow_api_to_realtime = Dataflow(
    api_service,
    realtime_service,
    "API Service publishes events to the realtime service",
)
flow_api_to_realtime.protocol = "HTTPS"
flow_api_to_realtime.dstPort = 443
flow_api_to_realtime.isEncrypted = True
flow_api_to_realtime.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from SDK Client to Control Plane
flow_sdk_to_control = Dataflow(
    sdk_client,
    vercel,
    "SDK clients communicate with the control plane to discover their organizations, API URL, etc.",
)
flow_sdk_to_control.protocol = "HTTPS"
flow_sdk_to_control.dstPort = 443
flow_sdk_to_control.isEncrypted = True
flow_sdk_to_control.threats = [BraintrustThreats.TOKEN_THEFT, BraintrustThreats.DATA_TAMPERING]

# Flow from Vercel to Supabase
flow_vercel_to_supabase = Dataflow(
    vercel,
    supabase,
    "Vercel stores and retrieves metadata from Supabase PostgreSQL database",
)
flow_vercel_to_supabase.protocol = "HTTPS"
flow_vercel_to_supabase.dstPort = 443
flow_vercel_to_supabase.isEncrypted = True
# Create a Data object instead of using a string
supabase_data = Data("User metadata, project settings, organization data")
flow_vercel_to_supabase.data = supabase_data
flow_vercel_to_supabase.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from API Service to Control Plane for auth checks
flow_api_to_vercel = Dataflow(
    api_service,
    vercel,
    "Data plane reaches out to control plane to perform RBAC/auth checks",
)
flow_api_to_vercel.protocol = "HTTPS"
flow_api_to_vercel.dstPort = 443
flow_api_to_vercel.isEncrypted = True
flow_api_to_vercel.threats = [
    BraintrustThreats.DATA_TAMPERING,
    BraintrustThreats.CONTROL_PLANE_SPOOFING,
    BraintrustThreats.RBAC_BYPASS,
]

# Flow from Brainstore Container to Postgres Database
flow_brainstore_to_db = Dataflow(
    brainstore_container, db, "Brainstore reads data and stores metadata in Postgres Database"
)
flow_brainstore_to_db.protocol = "PostgreSQL"
flow_brainstore_to_db.dstPort = 5432
flow_brainstore_to_db.isEncrypted = True
# Create a Data object for the data being transferred
brainstore_db_data = Data("Data reads and metadata storage")
flow_brainstore_to_db.data = brainstore_db_data
flow_brainstore_to_db.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Flow from Brainstore Container to Redis Cache
flow_brainstore_to_redis = Dataflow(brainstore_container, redis, "Brainstore uses Redis for distributed locking")
flow_brainstore_to_redis.protocol = "Redis Protocol"
flow_brainstore_to_redis.dstPort = 6379
flow_brainstore_to_redis.isEncrypted = True
flow_brainstore_to_redis.threats = [BraintrustThreats.DATA_TAMPERING, BraintrustThreats.DATA_EXPOSURE]

# Process the threat model to generate diagrams and reports
tm.process()
