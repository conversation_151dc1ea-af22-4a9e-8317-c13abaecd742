#!/usr/bin/env python3
"""
Generate a comprehensive threat model report with both diagram and threat list.
This script:
1. Generates a data flow diagram with threats highlighted
2. Creates a detailed threat report
3. Combines them into a single PDF document
"""

import os
import subprocess
import sys

from threat_model import BraintrustThreats, tm


def generate_report():
    """Generate a comprehensive threat report in Markdown format."""
    report = []

    # Title and description
    report.append(f"# {tm.name}")
    report.append("")
    report.append(tm.description)
    report.append("")

    # Add the diagram
    report.append("## Data Flow Diagram")
    report.append("")
    report.append("![Threat Model Diagram](threat_model_diagram.png)")
    report.append("")

    # Boundaries and components
    report.append("## Architecture Components")
    report.append("")

    for boundary in tm._boundaries:
        report.append(f"### {boundary.name} Boundary")
        report.append("")

        # Find elements in this boundary
        for element in tm._elements:
            if hasattr(element, "inBoundary") and element.inBoundary == boundary:
                report.append(f"- **{element.name}**: {element.description}")

                # Add threats for this element
                if hasattr(element, "threats") and element.threats:
                    report.append("  - **Threats**:")
                    for threat in element.threats:
                        report.append(f"    - {threat.id}: {threat.description}")
                        report.append(f"      - **Mitigations**: {threat.mitigations}")
                report.append("")

        report.append("")

    # External entities
    report.append("## External Entities")
    report.append("")

    for element in tm._elements:
        if (
            hasattr(element, "inBoundary")
            and element.inBoundary is None
            and hasattr(element, "type")
            and element.type == "ExternalEntity"
        ):
            report.append(f"- **{element.name}**: {element.description}")

            # Add threats for this element
            if hasattr(element, "threats") and element.threats:
                report.append("  - **Threats**:")
                for threat in element.threats:
                    report.append(f"    - {threat.id}: {threat.description}")
                    report.append(f"      - **Mitigations**: {threat.mitigations}")
            report.append("")

    # Actors
    report.append("## Actors")
    report.append("")

    for element in tm._elements:
        if hasattr(element, "type") and element.type == "Actor":
            report.append(f"- **{element.name}**: {element.description}")

            # Add threats for this element
            if hasattr(element, "threats") and element.threats:
                report.append("  - **Threats**:")
                for threat in element.threats:
                    report.append(f"    - {threat.id}: {threat.description}")
                    report.append(f"      - **Mitigations**: {threat.mitigations}")
            report.append("")

    # Data flows
    report.append("## Data Flows")
    report.append("")

    for flow in tm._flows:
        report.append(f"### {flow.source.name} → {flow.sink.name}")
        report.append("")
        report.append(f"- **Description**: {flow.name}")
        report.append(f"- **Protocol**: {flow.protocol}")
        report.append(f"- **Port**: {flow.dstPort}")
        report.append(f"- **Encrypted**: {flow.isEncrypted}")

        if hasattr(flow, "data") and flow.data:
            if hasattr(flow.data, "name"):
                report.append(f"- **Data**: {flow.data.name}")
            else:
                report.append(f"- **Data**: {flow.data}")

        # Add threats for this flow
        if hasattr(flow, "threats") and flow.threats:
            report.append("- **Threats**:")
            for threat in flow.threats:
                report.append(f"  - {threat.id}: {threat.description}")
                report.append(f"    - **Mitigations**: {threat.mitigations}")

        report.append("")

    # Summary of all threats
    report.append("## Summary of Threats")
    report.append("")

    # Get all unique threats
    all_threats = set()

    # Add threats from elements
    for element in tm._elements:
        if hasattr(element, "threats") and element.threats:
            for threat in element.threats:
                all_threats.add(threat)

    # Add threats from dataflows
    for flow in tm._flows:
        if hasattr(flow, "threats") and flow.threats:
            for threat in flow.threats:
                all_threats.add(threat)

    # Sort threats by ID
    sorted_threats = sorted(all_threats, key=lambda t: t.id)

    for threat in sorted_threats:
        report.append(f"### {threat.id}: {threat.description}")
        report.append("")
        report.append(f"- **Mitigations**: {threat.mitigations}")

        # Find affected components
        affected_components = []
        for element in tm._elements:
            if hasattr(element, "threats") and element.threats and threat in element.threats:
                affected_components.append(element.name)

        if affected_components:
            report.append("- **Affected Components**:")
            for component in affected_components:
                report.append(f"  - {component}")

        # Find affected data flows
        affected_flows = []
        for flow in tm._flows:
            if hasattr(flow, "threats") and flow.threats and threat in flow.threats:
                affected_flows.append(f"{flow.source.name} → {flow.sink.name}")

        if affected_flows:
            report.append("- **Affected Data Flows**:")
            for flow in affected_flows:
                report.append(f"  - {flow}")

        report.append("")

    return "\n".join(report)


def generate_diagram():
    """Generate the data flow diagram with threats highlighted."""
    print("Generating threat model diagram...")

    # Generate the diagram with standard colors (no dark mode)
    cmd = "python threat_model.py --dfd --colormap | dot -Tpng -o threat_model_diagram.png"

    try:
        subprocess.run(cmd, shell=True, check=True)
        print("Diagram generated: threat_model_diagram.png")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error generating diagram: {e}")
        return False


def generate_pdf(md_file, pdf_file):
    """Convert markdown to PDF using pandoc with different PDF engines."""
    # Try different PDF engines that might be available
    engines = ["ms", "html5"]  # ms = ConTeXt, html5 = HTML5 output

    for engine in engines:
        try:
            # Convert to PDF using pandoc with the specified engine
            cmd = f"pandoc {md_file} --pdf-engine={engine} -o {pdf_file}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"PDF report generated using {engine} engine: {pdf_file}")
                return True
        except Exception:
            continue

    # If all engines fail, try a direct conversion to HTML
    try:
        html_file = md_file.replace(".md", ".html")
        cmd = f"pandoc {md_file} -s -o {html_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"HTML report generated: {html_file}")
            print("You can open this HTML file in a browser and print to PDF if needed.")

            # Try to open the HTML file
            try:
                subprocess.run(["open", html_file], check=False)
            except:
                pass

            return True
    except Exception as e:
        print(f"Error generating HTML: {e}")

    # If all else fails, just open the markdown file
    print("Could not generate PDF. You can still view the Markdown report and the diagram separately.")
    print(f"Markdown report is available at: {md_file}")
    print(f"Diagram is available at: threat_model_diagram.png")

    # Try to open the markdown file as a fallback
    try:
        subprocess.run(["open", md_file], check=False)
    except:
        pass

    return False


if __name__ == "__main__":
    # Generate the diagram
    diagram_success = generate_diagram()

    # Generate the report
    report = generate_report()

    # Write to file
    output_md = "complete_threat_report.md"
    if len(sys.argv) > 1:
        output_md = sys.argv[1]

    with open(output_md, "w") as f:
        f.write(report)

    print(f"Complete threat report generated: {output_md}")

    # Convert to PDF
    output_pdf = output_md.replace(".md", ".pdf")
    generate_pdf(output_md, output_pdf)

    print("\nTo view the complete report:")
    print(f"1. Markdown: open {output_md}")
    print(f"2. PDF (if generated): open {output_pdf}")
