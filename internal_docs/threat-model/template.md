# Braintrust Terraform Infrastructure Threat Model

## Overview

{{ tm.description }}

## Architecture Components

{% for b in tm.boundaries %}

### {{ b.name }} Boundary

{% for e in tm.elements %}{% if e.inBoundary == b %}

- **{{ e.name }}**: {{ e.description }}
  {% if e.threats %}
  - **Threats**:
    {% for t in e.threats %} - {{ t.SID }}: {{ t.description }} ({{ t.stride_category }}) - **Mitigations**: {{ t.mitigations }}
    {% endfor %}
    {% endif %}
    {% endif %}{% endfor %}
    {% endfor %}

## External Entities

{% for e in tm.elements %}{% if e.inBoundary is none and e.type == "ExternalEntity" %}

- **{{ e.name }}**: {{ e.description }}
  {% if e.threats %}
  - **Threats**:
    {% for t in e.threats %} - {{ t.SID }}: {{ t.description }} ({{ t.stride_category }}) - **Mitigations**: {{ t.mitigations }}
    {% endfor %}
    {% endif %}
    {% endif %}{% endfor %}

## Actors

{% for e in tm.elements %}{% if e.type == "Actor" %}

- **{{ e.name }}**: {{ e.description }}
  {% if e.threats %}
  - **Threats**:
    {% for t in e.threats %} - {{ t.SID }}: {{ t.description }} ({{ t.stride_category }}) - **Mitigations**: {{ t.mitigations }}
    {% endfor %}
    {% endif %}
    {% endif %}{% endfor %}

## Data Flows

{% for f in tm.dataflows %}

### {{ f.source.name }} → {{ f.sink.name }}

- **Description**: {{ f.name }}
- **Protocol**: {{ f.protocol }}
- **Port**: {{ f.dstPort }}
- **Encrypted**: {{ f.isEncrypted }}
  {% if f.data %}
- **Data**: {{ f.data }}
  {% endif %}
  {% if f.threats %}
- **Threats**:
  {% for t in f.threats %}
  - {{ t.SID }}: {{ t.description }} ({{ t.stride_category }}) - **Mitigations**: {{ t.mitigations }}
    {% endfor %}
    {% endif %}
    {% endfor %}

## Summary of Threats

{% for t in tm.threats %}

### {{ t.SID }}: {{ t.description }} ({{ t.stride_category }})

- **Mitigations**: {{ t.mitigations }}
- **Affected Components**:
  {% for e in tm.elements %}{% if t in e.threats %}
  - {{ e.name }}
    {% endif %}{% endfor %}
- **Affected Data Flows**:
  {% for f in tm.dataflows %}{% if t in f.threats %}
  - {{ f.source.name }} → {{ f.sink.name }}
    {% endif %}{% endfor %}
    {% endfor %}
