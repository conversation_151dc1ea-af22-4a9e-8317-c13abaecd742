{"name": "test-proxy", "main": "./dist/index.js", "scripts": {"build": "esbuild --platform=node --bundle index.ts --outfile=dist/index.js --sourcemap --target=es2020", "watch": "esbuild --platform=node --bundle index.ts --outfile=dist/index.js --sourcemap --target=es2020 --watch=forever", "dev": "run-p build dev:serve", "dev:serve": "nodemon dist/index.js", "start": "node dist/index.js"}, "dependencies": {"@braintrust/local": "workspace:*", "@braintrust/proxy": "workspace:*", "cors": "^2.8.5", "esbuild": "^0.19.10", "express": "^4.19.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "nodemon": "^3.0.2", "npm-run-all": "^4.1.5"}}