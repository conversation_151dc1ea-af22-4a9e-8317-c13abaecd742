# CSV Attachments in Braintrust

This example demonstrates how to upload CSV files as attachments to Braintrust datasets using the Python SDK.

## Files

- `sample_data.csv` - A dummy CSV file with employee data
- `upload_csv_attachment.py` - Python script to upload CSV attachments to a dataset
- `README_csv_attachments.md` - This documentation file

## Setup

1. Make sure you have the Braintrust SDK installed:
```bash
pip install braintrust
```

2. Set up your Braintrust API key (if not already done):
```bash
export BRAINTRUST_API_KEY="your-api-key-here"
```

## Usage

### Basic Usage

Upload the sample CSV file to a new dataset:

```bash
python upload_csv_attachment.py "My Project" "CSV Dataset"
```

### Custom CSV File

Upload your own CSV file:

```bash
python upload_csv_attachment.py "My Project" "Custom Dataset" --csv-file /path/to/your/file.csv
```

### Include In-Memory Example

Create both file-based and in-memory CSV attachment examples:

```bash
python upload_csv_attachment.py "My Project" "CSV Dataset" --in-memory-example
```

## What the Script Does

1. **Creates a Braintrust dataset** with the specified project and dataset names
2. **Uploads CSV files as attachments** using the `Attachment` class
3. **Creates multiple rows** with different tasks/prompts that reference the same CSV data
4. **Demonstrates two approaches**:
   - File-based attachments (reading from disk)
   - In-memory attachments (using bytes directly)

## Dataset Structure

Each row in the created dataset contains:
- `input.task`: A description of what to do with the CSV data
- `input.data_file`: The CSV file as an attachment
- `expected`: The expected output/analysis

## Using in Playground

After running the script:

1. Go to the Braintrust web interface
2. Navigate to your project and dataset
3. Open the Playground
4. Select your dataset
5. The CSV attachments will be available for each row
6. You can reference the CSV data in your prompts

## Example Prompts

When using the dataset in the playground, you can create prompts like:

```
Analyze the attached CSV file and {{task}}. 

The CSV file contains employee data with the following columns:
- id: Employee ID
- name: Employee name  
- age: Employee age
- department: Department name
- salary: Annual salary
- performance_score: Performance rating (1-10)

Please provide a detailed analysis.
```

## CSV Content Types

The script uses `"text/csv"` as the content type, which is properly recognized by Braintrust for CSV files.

## Error Handling

The script includes error handling for:
- Missing CSV files
- Braintrust API errors
- File reading issues

## Customization

You can modify the script to:
- Add more sample rows with different tasks
- Use different CSV files for each row
- Include additional metadata
- Change the expected outputs based on your use case
