# This docker compose file can be used to build the various services we publish
# for our self-hosted deployment. It does not include any configuration for
# connecting together or running the services. To generate docker compose files
# for running the services, try the 'generate-docker-compose' command, which
# uses the local/py/src/braintrust_local/generate_docker_compose.py script.
#
# To build the services, simply run `docker compose -f
# docker_compose_standalone_build.yml build`. To tag each image with the commit,
# provide `GIT_COMMIT_HASH=[commit]` as an environment variable to the build
# command. You may also run `./scripts/deploy_docker.py build`, which will do
# essentially the same thing.
#
# For brainstore, if BRAINSTORE_PROFILING_API_KEY environment variable is set,
# debug symbols will be automatically uploaded to Polar Signals Cloud during
# the build process using Docker BuildKit secrets.

services:
  redis:
    build:
      context: ./services/redis
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
    image: redis
  postgres:
    build:
      context: ./services/postgres
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
    image: postgres
  otelcol:
    build:
      context: ./services/otelcol
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
    image: otelcol
  standalone-api:
    build:
      context: .
      dockerfile: api-ts/Dockerfile
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
      args:
        GIT_COMMIT_HASH: "${GIT_COMMIT_HASH:-}"
    image: standalone-api
  standalone-api-full-build:
    build:
      context: .
      dockerfile: api-ts/Dockerfile
      target: builder
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
      args:
        GIT_COMMIT_HASH: "${GIT_COMMIT_HASH:-}"
    image: standalone-api-full-build
  standalone-proxy:
    build:
      context: .
      dockerfile: api-ts/Dockerfile
      target: runner_proxy
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
      args:
        GIT_COMMIT_HASH: "${GIT_COMMIT_HASH:-}"
    image: standalone-proxy
  standalone-realtime:
    build:
      context: .
      dockerfile: realtime/Dockerfile
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
    image: standalone-realtime
  brainstore:
    build:
      context: ./brainstore
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
      args:
        GIT_COMMIT_HASH: "${GIT_COMMIT_HASH:-}"
      secrets:
        - polar_signals_api_key
    image: brainstore
  pulse:
    build:
      context: ./brainstore
      dockerfile: Dockerfile.pulse
      labels:
        commit_hash: "${GIT_COMMIT_HASH:-}"
      args:
        GIT_COMMIT_HASH: "${GIT_COMMIT_HASH:-}"
    image: pulse

# Define the secret - it will use the environment variable if present
# Note: Docker BuildKit must be enabled (DOCKER_BUILDKIT=1) for secrets to work
secrets:
  polar_signals_api_key:
    environment: "BRAINSTORE_PROFILING_API_KEY"
