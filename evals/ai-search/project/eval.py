import dataclasses
import datetime
import json
from typing import Any, Callable, Dict, List, Optional

import chevron
import duckdb
from autoevals import Sql
from braintrust import current_span, traced
from braintrust.score import Score, Scorer
from braintrust.serializable_data_class import SerializableDataClass
from Levenshtein import distance
from pydantic import BaseModel


@dataclasses.dataclass
class SearchOutput(SerializableDataClass):
    match: bool = False
    filter: Optional[str] = None
    sort: Optional[str] = None
    explanation: Optional[str] = None
    error: Optional[str] = None


@dataclasses.dataclass
class Example(SerializableDataClass):
    input: str
    expected: SearchOutput


def render_example(example: Example, data: Dict[str, Any]) -> Example:
    return Example(
        input=_render_optional(example.input, data),
        expected=SearchOutput(
            match=example.expected.match,
            filter=_render_optional(example.expected.filter, data),
            sort=_render_optional(example.expected.sort, data),
            explanation=_render_optional(example.expected.explanation, data),
        ),
    )


def _render_optional(text: Optional[str], data: Dict[str, Any]) -> Optional[str]:
    return chevron.render(text.strip(), data, warn=True) if text is not None else None


class AutoSearch(Scorer):
    def __init__(self, client_opts):
        self.client_opts = client_opts

    def _run_eval_sync(
        self,
        output,
        expected=None,
        **kwargs,
    ):
        if expected is None:
            raise ValueError("AutoSearch requires an expected value")
        input = kwargs.get("input")
        if input is None or not isinstance(input, str):
            raise ValueError("AutoSearch requires an input value of type str")

        name = "AutoSearch"
        output = SearchOutput(**output)
        expected = SearchOutput(**expected)

        if output.match != expected.match:
            current_span().log(scores=dict(function_choice=0))
            return Score(name=name, score=0)
        current_span().log(scores=dict(function_choice=1))
        if expected.match:
            return Score(name=name, score=1)

        filter_score = None
        if output.filter and expected.filter:
            result = _auto_filter(
                client_opts=self.client_opts,
                input=input,
                output=output.filter,
                expected=expected.filter,
            )
            filter_score = result.score or 0
        elif output.filter or expected.filter:
            filter_score = 0
        current_span().log(scores=dict(filter=filter_score))

        sort_score = None
        if output.sort and expected.sort:
            result = _auto_sort(
                client_opts=self.client_opts,
                input=input,
                output=output.sort,
                expected=expected.sort,
            )
            sort_score = result.score or 0
        elif output.sort or expected.sort:
            sort_score = 0
        current_span().log(scores=dict(sort=sort_score))

        scores = [s for s in [filter_score, sort_score] if s is not None]
        if len(scores) == 0:
            return Score(name=name, score=0, error="Bad example: no filter or sort for SQL function call")
        return Score(name=name, score=sum(scores) / len(scores))


@traced("AutoFilter")
def _auto_filter(client_opts, **kwargs):
    return Sql(**client_opts)(**kwargs)


@traced("AutoSort")
def _auto_sort(client_opts, **kwargs):
    return Sql(**client_opts)(**kwargs)


class SimpleSearch(Scorer):
    def __init__(self, test_filter, test_sort):
        self.test_filter = test_filter
        self.test_sort = test_sort

    def _run_eval_sync(
        self,
        output,
        expected=None,
        **kwargs,
    ):
        if expected is None:
            raise ValueError("SimpleSearch requires an expected value")

        name = "SimpleSearch"
        output = SearchOutput(**output)
        expected = SearchOutput(**expected)

        if output.match != expected.match:
            current_span().log(scores=dict(function_choice=0))
            return Score(name=name, score=0)

        current_span().log(scores=dict(function_choice=1))
        if expected.match:
            return Score(name=name, score=1)

        filter_score = None
        if output.filter and expected.filter:
            with current_span().start_span("SimpleFilter") as span:
                filter_score = score_clause(output.filter, expected.filter, "filter", self.test_filter)
        elif output.filter or expected.filter:
            filter_score = 0
        current_span().log(scores=dict(filter=filter_score))

        sort_score = None
        if output.sort and expected.sort:
            with current_span().start_span("SimpleSort") as span:
                sort_score = score_clause(output.sort, expected.sort, "sort", self.test_sort)
        elif output.sort or expected.sort:
            sort_score = 0
        current_span().log(scores=dict(sort=sort_score))

        scores = [s for s in [filter_score, sort_score] if s is not None]
        if len(scores) == 0:
            return Score(name=name, score=0, error="Bad example: no filter or sort for SQL function call")
        return Score(name=name, score=sum(scores) / len(scores))


def score_clause(
    output: Optional[str], expected: Optional[str], clause_type: str, test_clause: Callable[[str], str]
) -> float:
    assert clause_type in ["filter", "sort"]
    roundtrip = _roundtrip_sort if clause_type == "sort" else _roundtrip_filter

    if output == expected:
        current_span().log(scores=dict(exact_match=1))
        return 1
    current_span().log(scores=dict(exact_match=0))
    if roundtrip(output) == roundtrip(expected):
        current_span().log(scores=dict(roundtrip_match=1))
        return 1
    current_span().log(scores=dict(roundtrip_match=0))

    # If the queries aren't equivalent after roundtripping, it's not immediately clear
    # whether they are semantically equivalent. Let's at least check that the generated
    # clause is valid SQL by running it on `test_clause`. If that passes, then just
    # fall back to using the Levenshtein distance.
    try:
        clause = test_clause(output)
        current_span().log(metadata=dict(test_clause=clause))
        duckdb.sql(clause).fetchall()
    except Exception as e:
        current_span().log(scores=dict(valid_clause=0))
        return 0
    current_span().log(scores=dict(valid_clause=1))

    max_len = max(len(x) for x in [output, expected])
    if max_len == 0:
        current_span().log(metadata=dict(error="Bad example: empty clause"))
        return 0
    return 1 - (distance(output, expected) / max_len)


def _single_quote(s):
    return f"""'{s.replace("'", "''")}'"""


def _roundtrip_filter(s):
    return duckdb.sql(
        f"""
        SELECT json_deserialize_sql(json_serialize_sql({_single_quote(f"SELECT 1 WHERE {s}")}))
    """
    ).fetchall()[0][0]


def _roundtrip_sort(s):
    return duckdb.sql(
        f"""
        SELECT json_deserialize_sql(json_serialize_sql({_single_quote(f"SELECT 1 ORDER BY {s}")}))
    """
    ).fetchall()[0][0]
