import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import chevron
from braintrust import init_logger, traced
from pydantic import BaseModel, Field, create_model


class ExperimentSource(BaseModel):
    commit: str = Field(
        ...,
        description="Git commit hash. Any prefix of this hash at least 7 characters long should be considered an exact match, so use a substring filter rather than string equality to check the commit, e.g. `(source->>'commit') ILIKE '{COMMIT}%'`",
    )
    branch: str = Field(..., description="Git branch name")
    tag: Optional[str] = Field(..., description="Git commit tag")
    commit_time: int = Field(..., description="Git commit timestamp")
    author_name: str = Field(..., description="Author of git commit")
    author_email: str = Field(..., description="Email address of git commit author")
    commit_message: str = Field(..., description="Git commit message")
    dirty: Optional[bool] = Field(
        ...,
        description="Whether the git state was dirty when the experiment was run. If false, the git state was clean",
    )


class Experiment(BaseModel):
    id: str = Field(..., description="Experiment ID, unique")
    name: str = Field(..., description="Name of the experiment")
    last_updated: int = Field(
        ...,
        description="Timestamp marking when the experiment was last updated. If the query deals with some notion of relative time, like age or recency, refer to this timestamp and, if appropriate, compare it to the current time `get_current_time()` by adding or subtracting an interval.",
    )
    creator: Dict[str, str] = Field(..., description="Information about the experiment creator")
    source: ExperimentSource = Field(..., description="Git state that the experiment was run on")
    metadata: Dict[str, Any] = Field(
        ...,
        description="Custom metadata provided by the user. Ignore this field unless the query mentions metadata or refers to a metadata key specifically",
    )


def get_experiment_schema(ai_schema_columns: List[str]):
    ai_schema_fields = {column: (Optional[float], ...) for column in ai_schema_columns}
    FullExperiment = create_model("Experiment", __base__=Experiment, **ai_schema_fields)

    experiment_model_json_schema = json.dumps(FullExperiment.model_json_schema())
    return experiment_model_json_schema


class Match(BaseModel):
    explanation: str = Field(..., description="Explanation of why I called the MATCH function")


class SQL(BaseModel):
    filter: Optional[str] = Field(..., description="SQL filter clause")
    sort: Optional[str] = Field(..., description="SQL sort clause")
    explanation: str = Field(
        ..., description="Explanation of why I called the SQL function and how I chose the filter and/or sort clauses"
    )


def get_output_functions():
    return [
        {
            "name": "MATCH",
            "description": "Interpret the query as a simple substring match",
            "parameters": Match.model_json_schema(),
        },
        {
            "name": "SQL",
            "description": "Interpret the query as a SQL filter and/or sort clause",
            "parameters": SQL.model_json_schema(),
        },
    ]


def build_project_search_completion_kwargs(
    *,
    query: str,
    model: str,
    prompt: str,
    ai_schema_columns: List[str],
    **kwargs,
):
    schema = get_experiment_schema(ai_schema_columns=ai_schema_columns)
    data = dict(schema=schema)
    system_message = chevron.render(prompt.strip(), data, warn=True)

    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": f"Query: {query}"},
    ]
    tools = [{"type": "function", "function": f} for f in get_output_functions()]

    return dict(
        model=model,
        temperature=0,
        messages=messages,
        tools=tools,
    )
