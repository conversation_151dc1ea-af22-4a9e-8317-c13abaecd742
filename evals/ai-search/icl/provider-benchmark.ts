import { AutoSearch } from "ai-search.eval";
import { Eva<PERSON> } from "braintrust";
import { loadDataset } from "dataset";
import { runAISearchLLaMa } from "task";

// const providers = [
//   {
//     provider: "Fireworks",
//     models: [
//       "accounts/fireworks/models/llama-v3p1-8b-instruct",
//       "accounts/fireworks/models/llama-v3p1-70b-instruct",
//       "accounts/fireworks/models/llama-v3p1-405b-instruct",
//     ],
//   },
//   {
//     provider: "Together",
//     models: [
//       "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
//       "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
//       "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
//     ],
//   },
//   {
//     provider: "Groq",
//     models: [
//       "llama-3.1-8b-instant",
//       "llama-3.1-70b-versatile",
//       "llama-3.1-405b-reasoning",
//     ],
//   },
//   {
//     provider: "Lepton",
//     models: ["llama3-1-8b", "llama3-1-70b", "llama3-1-405b"],
//   },
//   {
//     provider: "vLLM Sanity",
//     models: ["meta-llama/Meta-Llama-3.1-405B-Instruct-FP8"],
//   },
// ];

const providers = [
  {
    provider: "Provider 1",
    models: [
      "accounts/fireworks/models/llama-v3p1-8b-instruct",
      "accounts/fireworks/models/llama-v3p1-70b-instruct",
      "accounts/fireworks/models/llama-v3p1-405b-instruct",
    ],
  },
  {
    provider: "Provider 2",
    models: [
      "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
      "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
      "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
    ],
  },
  {
    provider: "Provider 3",
    models: ["llama3-1-8b", "llama3-1-70b", "llama3-1-405b"],
  },
  {
    provider: "Provider 4",
    models: [
      "llama-3.1-8b-instant",
      "llama-3.1-70b-versatile",
      // "llama-3.1-405b-reasoning",
    ],
  },
  {
    provider: "Self-hosted vLLM",
    models: ["meta-llama/Meta-Llama-3.1-405B-Instruct-FP8"],
  },
];

// We can parallelize across providers, because the rate limits are independent

async function runProviderBenchmark(provider: (typeof providers)[number]) {
  if (provider.provider === "Groq") {
    return;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const evals: Promise<any>[] = [];
  for (const model of provider.models) {
    const size = model.toLowerCase().includes("8b")
      ? "8b"
      : model.toLowerCase().includes("70b")
        ? "70b"
        : "405b";

    evals.push(
      Eval("ai-search-model-comparison-4", {
        data: loadDataset,
        task: async (input: string) =>
          runAISearchLLaMa({
            model,
            input,
          }),
        scores: [AutoSearch],
        metadata: {
          size,
          provider: provider.provider,
          model,
        },
        experimentName: `${provider.provider} (${size})`,
        maxConcurrency: 3,
        trialCount: 3,
      }),
    );
  }
  await Promise.all(evals);
}

async function main() {
  await Promise.all(providers.map(runProviderBenchmark));
}

main();
