import { currentSpan, traced } from "braintrust";
import { Scorer } from "braintrust/util";
import { LLMClassifierArgs, Sql } from "autoevals";
import { AISearchOutput } from "./dataset";

export const AutoSearch: Scorer<
  AISearchOutput,
  LLMClassifierArgs<{ input: string }>
> = async ({ output, expected, ...args }) => {
  if (expected === undefined) {
    throw new Error("AutoSearch requires an expected value");
  }

  const name = "AutoSearch";

  if (output.match !== expected.match) {
    return { name, score: 0 };
  }

  if (expected.match) {
    return { name, score: 1 };
  }

  let filterScore: number | null = null;
  if (output.filter && expected.filter) {
    const outputFilter = output.filter;
    const expectedFilter = expected.filter;
    filterScore =
      (
        await traced(
          async (span) => {
            const sqlOutput = await Sql({
              ...args,
              output: outputFilter,
              expected: expectedFilter,
            });
            span.log({
              input: { output: output.filter, expected: expected.filter },
              output: sqlOutput,
            });
            return sqlOutput;
          },
          { name: "AutoFilter" },
        )
      ).score || 0;
  } else if (output.filter || expected.filter) {
    filterScore = 0;
  }
  currentSpan().log({ scores: { filter: filterScore } });

  let sortScore: number | null = null;
  if (output.sort && expected.sort) {
    const outputSort = output.sort;
    const expectedSort = expected.sort;
    sortScore =
      (
        await traced(
          async (span) => {
            const sqlOutput = await Sql({
              ...args,
              output: outputSort,
              expected: expectedSort,
            });
            span.log({
              input: { output: output.sort, expected: expected.sort },
              output: sqlOutput,
            });
            return sqlOutput;
          },
          { name: "AutoSort" },
        )
      ).score || 0;
  } else if (output.sort || expected.sort) {
    sortScore = 0;
  }
  currentSpan().log({ scores: { sort: sortScore } });

  const scores: number[] = [];
  for (const score of [filterScore, sortScore]) {
    if (score !== null) {
      scores.push(score);
    }
  }
  if (scores.length === 0) {
    return {
      name,
      score: 0,
      error: "Bad example: no filter or sort for expected SQL function call",
    };
  }
  return { name, score: scores.reduce((a, b) => a + b, 0) / scores.length };
};

// Eval("ai-search-icl", {
//   data: loadDataset,
//   task: async (input) => runAISearchOpenAI({ model: "gpt-4o", input }),
//   scores: [AutoSearch],
//   metadata: {
//     model: "gpt-4o",
//   },
//   experimentName: "gpt-4o-base-all-few-shots",
// });

// Eval("ai-search-icl", {
//   data: loadDataset,
//   task: async (input) => runAISearchOpenAI({ model: "gpt-4o-mini", input }),
//   scores: [AutoSearch],
//   metadata: {
//     model: "gpt-4o-mini",
//   },
//   experimentName: "gpt-4o-mini-base-all-few-shots",
// });

// Eval("ai-search-icl", {
//   data: loadDataset,
//   task: async (input) => runAISearchLLaMa({ model: LLAMA31_8B, input }),
//   scores: [AutoSearch],
//   metadata: {
//     model: "meta-llama/Meta-Llama-3-8B-Instruct-Turbo",
//   },
//   experimentName: `${LLAMA31_8B}-base`,
//   maxConcurrency: 2,
// });

// Eval("ai-search-icl", {
//   data: loadDataset,
//   task: async (input) =>
//     runAISearchLLaMa({
//       model: "accounts/fireworks/models/llama-v3p1-8b-instruct",
//       input,
//     }),
//   scores: [AutoSearch],
//   metadata: {
//     model: "accounts/fireworks/models/llama-v3p1-8b-instruct",
//   },
//   experimentName: `fireworks-llama-v3p1-8b-instruct-base`,
//   maxConcurrency: 2,
// });

// Eval("ai-search-icl", {
//   data: loadDataset,
//   task: async (input) => runAISearchLLaMa({ model: LLAMA31_70B, input }),
//   scores: [AutoSearch],
//   metadata: {
//     model: LLAMA31_70B,
//   },
//   experimentName: `${LLAMA31_70B}-base`,
//   maxConcurrency: 2,
// });
