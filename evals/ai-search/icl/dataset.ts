import path from "path";
import mustache from "mustache";
import fs from "fs/promises";

export const aiSchemaColumns = [
  "Factuality",
  "nullable",
  "Correctness",
  "Relevance",
];

const PROJECT_EXAMPLES_FILE = path.join(__dirname, "../project/examples.json");

export interface AISearchResult {
  match: boolean;
  filter?: string;
  sort?: string;
  tags?: string[];
}

export interface AISearchOutput extends AISearchResult {
  explanation?: string;
}

export interface Example {
  input: string;
  expected: AISearchOutput;
}

export function renderExample(
  example: Example,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: Record<string, any>,
): Example {
  const {
    input,
    expected: { match, filter, sort, tags, explanation },
  } = example;
  return {
    input: mustache.render(input, data),
    expected: {
      match: match,
      filter: filter && mustache.render(filter, data),
      sort: sort && mustache.render(sort, data),
      tags: tags && tags.map((t) => mustache.render(t, data)),
      explanation: explanation && mustache.render(explanation, data),
    },
  };
}

export async function loadDataset() {
  const contents = await fs.readFile(PROJECT_EXAMPLES_FILE, "utf-8");
  const examples = JSON.parse(contents);
  return examples.examples.map((example: Example) =>
    renderExample(example, { aiSchemaColumns }),
  );
}
