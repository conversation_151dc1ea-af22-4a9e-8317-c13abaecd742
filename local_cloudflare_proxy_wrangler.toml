# This is a copy of proxy/apis/cloudflare/wrangler-template.toml with the
# following changes:
#
#   1. Update 'main' to point to the correct index.ts path relative to this file
#   location.
#
#   2. Fill in an 'id' for the kv_namespaces binding.
#
#   3. Set BRAINTRUST_APP_URL and WHITELISTED_ORIGINS.

name = "proxy"
main = "proxy/apis/cloudflare/src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat_v2"]

kv_namespaces = [
    # Configure this id to map to the id returned from
    #   wrangler kv:namespace create ai_proxy
    { binding = "ai_proxy", id = "b5f2f0416b054d0c9a71cbea2daed84b" },
]

[durable_objects]
bindings = [
    { name = "METRICS_AGGREGATOR", class_name = "PrometheusMetricAggregator" },
]

[[migrations]]
tag = "v1"                                   # Should be unique for each entry
new_classes = ["PrometheusMetricAggregator"] # Array of new classes


# Variable bindings. These are arbitrary, plaintext strings (similar to environment variables)
# Note: Use secrets to store sensitive data.
# Docs: https://developers.cloudflare.com/workers/platform/environment-variables
[vars]
# --- Braintrust proxy args
# Configure this username and password to protect your Prometheus metrics endpoint.
BRAINTRUST_APP_URL = "http://localhost:3000"
WHITELISTED_ORIGINS = "http://localhost:3000"
# PROMETHEUS_SCRAPE_USER = ""
# PROMETHEUS_SCRAPE_PASSWORD = ""

# Configure this to disable the prometheus endpoint. Note that even with this flag enabled, the proxy will not
# "push" any of its own telemetry. Instead, it will only report telemetry when scraped, behind a username and
# password you can configure.
# DISABLE_METRICS = true

# Split metric aggregations into multiple shards. This is useful if you expect a lot of throughput and want
# to avoid hitting rate limits on the durable objects used to aggregate metrics.
# METRICS_SHARDS = 10

# --- Standard Cloudflare args
# Bind an R2 Bucket. Use R2 to store arbitrarily large blobs of data, such as files.
# Docs: https://developers.cloudflare.com/r2/api/workers/workers-api-usage/
# [[r2_buckets]]
# binding = "MY_BUCKET"
# bucket_name = "my-bucket"

# Bind a Queue producer. Use this binding to schedule an arbitrary task that may be processed later by a Queue consumer.
# Docs: https://developers.cloudflare.com/queues/get-started
# [[queues.producers]]
# binding = "MY_QUEUE"
# queue = "my-queue"

# Bind a Queue consumer. Queue Consumers can retrieve tasks scheduled by Producers to act on them.
# Docs: https://developers.cloudflare.com/queues/get-started
# [[queues.consumers]]
# queue = "my-queue"

# Bind another Worker service. Use this binding to call another Worker without network overhead.
# Docs: https://developers.cloudflare.com/workers/platform/services
# [[services]]
# binding = "MY_SERVICE"
# service = "my-service"

# Bind a Durable Object. Durable objects are a scale-to-zero compute primitive based on the actor model.
# Durable Objects can live for as long as needed. Use these when you need a long-running "server", such as in realtime apps.
# Docs: https://developers.cloudflare.com/workers/runtime-apis/durable-objects
# [[durable_objects.bindings]]
# name = "MY_DURABLE_OBJECT"
# class_name = "MyDurableObject"

# Durable Object migrations.
# Docs: https://developers.cloudflare.com/workers/learning/using-durable-objects#configure-durable-object-classes-with-migrations
# [[migrations]]
# tag = "v1"
# new_classes = ["MyDurableObject"]
