import requests
import json
import os
from typing import Iterator, Dict, Any, Optional

def paginate_dataset_api(
    project_name: str, 
    dataset_name: str, 
    page_size: int = 100,
    api_key: Optional[str] = None,
    api_url: str = "https://www.braintrust.dev"
) -> Iterator[Dict[str, Any]]:
    """
    Paginate through dataset records using the Braintrust API directly.
    
    This gives you the most control over pagination and memory usage.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        page_size: Number of records to fetch per request
        api_key: Braintrust API key (defaults to BRAINTRUST_API_KEY env var)
        api_url: Braintrust API URL
    
    Yields:
        Individual dataset records
    """
    if api_key is None:
        api_key = os.environ.get("BRAINTRUST_API_KEY")
        if not api_key:
            raise ValueError("API key must be provided or set in BRAINTRUST_API_KEY environment variable")
    
    headers = {
        "Accept": "application/json",
        "Accept-Encoding": "gzip", 
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # First, get dataset metadata
    dataset_url = f"{api_url}/v1/dataset"
    params = {
        "project_name": project_name,
        "dataset_name": dataset_name
    }
    
    response = requests.get(dataset_url, headers=headers, params=params)
    response.raise_for_status()
    
    dataset_info = response.json()
    dataset_id = dataset_info["id"]
    
    # Now paginate through the records using BTQL
    cursor = None
    
    while True:
        btql_query = {
            "query": {
                "select": [{"op": "star"}],
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["dataset"]},
                    "args": [{"op": "literal", "value": dataset_id}]
                },
                "limit": page_size
            },
            "use_columnstore": False,
            "brainstore_realtime": True
        }
        
        if cursor:
            btql_query["query"]["cursor"] = cursor
        
        btql_response = requests.post(
            f"{api_url}/btql",
            headers=headers,
            json=btql_query
        )
        btql_response.raise_for_status()
        
        result = btql_response.json()
        records = result.get("data", [])
        
        if not records:
            break
            
        for record in records:
            yield record
            
        cursor = result.get("cursor")
        if not cursor:
            break

def paginate_dataset_simple_btql(
    project_name: str,
    dataset_name: str, 
    page_size: int = 100,
    api_key: Optional[str] = None,
    api_url: str = "https://www.braintrust.dev"
) -> Iterator[Dict[str, Any]]:
    """
    Simpler pagination using BTQL string queries.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
        page_size: Number of records to fetch per request
        api_key: Braintrust API key
        api_url: Braintrust API URL
    
    Yields:
        Individual dataset records
    """
    if api_key is None:
        api_key = os.environ.get("BRAINTRUST_API_KEY")
        if not api_key:
            raise ValueError("API key must be provided or set in BRAINTRUST_API_KEY environment variable")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    cursor = None
    
    while True:
        # Build BTQL query string
        query = f"select: * | from: dataset('{project_name}', '{dataset_name}') | limit: {page_size}"
        if cursor:
            query += f" | cursor: '{cursor}'"
        
        payload = {
            "query": query,
            "use_brainstore": True,
            "brainstore_realtime": True
        }
        
        response = requests.post(
            f"{api_url}/btql",
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        
        result = response.json()
        records = result.get("data", [])
        
        if not records:
            break
            
        for record in records:
            yield record
            
        cursor = result.get("cursor")
        if not cursor:
            break

# Example usage
if __name__ == "__main__":
    project_name = "pedro-brainstore1"
    dataset_name = "Dataset 1"
    
    print("=== API Pagination Method 1: Structured BTQL ===")
    count = 0
    try:
        for record in paginate_dataset_api(project_name, dataset_name, page_size=5):
            count += 1
            print(f"Record {count}: {record.get('id', 'no-id')}")
            
            # Stop after 10 records for demo
            if count >= 10:
                break
    except Exception as e:
        print(f"Error with method 1: {e}")
    
    print(f"\n=== API Pagination Method 2: String BTQL ===")
    count = 0
    try:
        for record in paginate_dataset_simple_btql(project_name, dataset_name, page_size=5):
            count += 1
            print(f"Record {count}: {record.get('id', 'no-id')}")
            
            # Stop after 10 records for demo
            if count >= 10:
                break
    except Exception as e:
        print(f"Error with method 2: {e}")
