import braintrust
from pydantic import BaseModel


class AskClarifyingQuestionInput(BaseModel):
    question: str
    context: str = ""


def ask_clarifying_question(question: str, context: str = "") -> str:
    """
    Dummy implementation of ask_clarifying_question tool.
    This tool would normally ask a clarifying question to the customer.
    """
    print(f"Asking clarifying question: {question}")
    if context:
        print(f"Context: {context}")
    
    # Return a dummy response indicating the question was asked
    return f"Clarifying question asked: '{question}'"


project = braintrust.projects.create(name="pedro-project1")

ask_clarifying_question_tool = project.tools.create(
    handler=ask_clarifying_question,
    name="Ask Clarifying Question",
    slug="ask-clarifying-question",
    description="Ask a clarifying question to the customer to better understand their request or issue.",
    parameters=AskClarifyingQuestionInput,
    if_exists="replace",
)
