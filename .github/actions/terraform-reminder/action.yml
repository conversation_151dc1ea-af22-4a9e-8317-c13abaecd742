name: "Terraform Reminder"
description: "Checks for modified files that require Terraform updates and comments on PRs"

inputs:
  bot-app-id:
    description: "GitHub App ID for bot token"
    required: true
  bot-private-key:
    description: "GitHub App private key for bot token"
    required: true

runs:
  using: "composite"
  steps:
    - name: Get bot token
      id: bot-token
      uses: actions/create-github-app-token@v1
      with:
        app-id: ${{ inputs.bot-app-id }}
        private-key: ${{ inputs.bot-private-key }}

    - name: Check for modified files
      id: check-files
      uses: actions/github-script@v7
      with:
        github-token: ${{ steps.bot-token.outputs.token }}
        script: |
          const files = await github.paginate(
            github.rest.pulls.listFiles,
            {
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number
            }
          );
          const terraformFiles = [
            "api/app.py",
            "api/merge-base.yaml",
            "scripts/merge_base.py",
            "scripts/merge_vpc.py",
            "scripts/inject_js_api.py"
          ];
          const modified = files
            .map(f => f.filename)
            .filter(f => terraformFiles.includes(f));
          core.setOutput('terraform_reminder_needed', modified.length > 0 ? 'true' : 'false');
          core.setOutput('modified_files', modified.join(' '));

    - name: Comment on PR if Terraform reminder needed
      if: steps.check-files.outputs.terraform_reminder_needed == 'true'
      uses: actions/github-script@v7
      with:
        github-token: ${{ steps.bot-token.outputs.token }}
        script: |
          const modifiedFiles = process.env.MODIFIED_FILES.split(' ');

          // Check if a Terraform reminder comment already exists
          const existingComments = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number
          });

          const existingComment = existingComments.data.find(comment =>
            comment.user.type === 'Bot' &&
            comment.body.includes('Terraform Update Required')
          );

          const comment = `## 🔧 Terraform Update Required

          The following files have been modified in this PR and may require equivalent Terraform module updates:

          ${modifiedFiles.map(file => `- \`${file}\``).join('\n')}

          **Please ensure you either:**
          1. Update the corresponding [Terraform module](https://github.com/braintrustdata/terraform-aws-braintrust-data-plane), OR
          2. File an Infra ticket in Linear to get the changes included.`;

          if (existingComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: existingComment.id,
              body: comment
            });
            console.log('Updated existing Terraform reminder comment');
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: comment
            });
            console.log('Added Terraform reminder comment to PR');
          }
      env:
        MODIFIED_FILES: ${{ steps.check-files.outputs.modified_files }}
