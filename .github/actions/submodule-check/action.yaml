name: "Submodule Check"
description: "Check if submodule commits are on their respective origin/main branches"

inputs:
  submodules:
    description: "Comma-separated list of submodules to check (default: sdk,proxy)"
    required: false
    default: "sdk,proxy"

outputs:
  all_on_main:
    description: "Whether all checked submodules are on origin/main"
    value: ${{ steps.check.outputs.all_on_main }}
  results:
    description: "JSON object with results for each submodule"
    value: ${{ steps.check.outputs.results }}

runs:
  using: "composite"
  steps:
    - name: Initialize submodules
      shell: bash
      run: |
        git submodule update --init --recursive

    - name: Check submodules
      id: check
      shell: bash
      run: |
        # Parse input submodules
        IFS=',' read -ra SUBMODULES <<< "${{ inputs.submodules }}"

        # Initialize variables
        ALL_ON_MAIN=true
        RESULTS="{}"

        echo "🔍 Checking submodules: ${{ inputs.submodules }}"
        echo ""

        # Check each submodule
        for submodule in "${SUBMODULES[@]}"; do
          # Trim whitespace
          submodule=$(echo "$submodule" | xargs)

          echo "📦 Checking submodule: $submodule"

          if [ ! -d "$submodule" ]; then
            echo "❌ Submodule directory '$submodule' does not exist"
            RESULTS=$(echo "$RESULTS" | jq --arg sub "$submodule" --arg status "missing" --arg message "Directory does not exist" '.[$sub] = {status: $status, message: $message}')
            ALL_ON_MAIN=false
            continue
          fi

          cd "$submodule"

          # Get current commit info
          CURRENT_COMMIT=$(git rev-parse HEAD)
          CURRENT_SHORT=$(git rev-parse --short HEAD)

          # Fetch latest from origin
          echo "  📡 Fetching latest from origin..."
          if ! git fetch origin main 2>/dev/null; then
            echo "  ⚠️  Failed to fetch origin/main, trying to fetch origin..."
            git fetch origin 2>/dev/null || echo "  ⚠️  Failed to fetch from origin"
          fi

          # Check if current commit is on origin/main
          if git merge-base --is-ancestor HEAD origin/main 2>/dev/null; then
            echo "  ✅ Commit $CURRENT_SHORT is on origin/main"
            RESULTS=$(echo "$RESULTS" | jq --arg sub "$submodule" --arg status "on_main" --arg commit "$CURRENT_SHORT" --arg message "Commit is on origin/main" '.[$sub] = {status: $status, commit: $commit, message: $message}')
          else
            echo "  ❌ Commit $CURRENT_SHORT is NOT on origin/main"

            # Get more details about the divergence
            if git show-ref --verify --quiet refs/remotes/origin/main; then
              AHEAD_BEHIND=$(git rev-list --left-right --count HEAD...origin/main 2>/dev/null || echo "? ?")
              AHEAD=$(echo "$AHEAD_BEHIND" | cut -d' ' -f1)
              BEHIND=$(echo "$AHEAD_BEHIND" | cut -d' ' -f2)

              if [ "$AHEAD" = "0" ] && [ "$BEHIND" != "0" ]; then
                MESSAGE="Commit is $BEHIND commits behind origin/main"
              elif [ "$AHEAD" != "0" ] && [ "$BEHIND" = "0" ]; then
                MESSAGE="Commit is $AHEAD commits ahead of origin/main"
              elif [ "$AHEAD" != "0" ] && [ "$BEHIND" != "0" ]; then
                MESSAGE="Commit has diverged: $AHEAD ahead, $BEHIND behind origin/main"
              else
                MESSAGE="Commit relationship to origin/main unclear"
              fi
            else
              MESSAGE="origin/main reference not found"
            fi

            echo "    📊 $MESSAGE"
            RESULTS=$(echo "$RESULTS" | jq --arg sub "$submodule" --arg status "not_on_main" --arg commit "$CURRENT_SHORT" --arg message "$MESSAGE" '.[$sub] = {status: $status, commit: $commit, message: $message}')
            ALL_ON_MAIN=false
          fi

          cd ..
          echo ""
        done

        # Set outputs (properly escape JSON for GitHub Actions)
        echo "all_on_main=$ALL_ON_MAIN" >> $GITHUB_OUTPUT
        # Use EOF delimiter to handle multiline JSON safely
        {
          echo "results<<EOF"
          echo "$RESULTS"
          echo "EOF"
        } >> $GITHUB_OUTPUT

        # Create formatted summary
        echo "📋 Summary:"
        echo "$RESULTS" | jq -r 'to_entries[] |
          if .value.status == "on_main" then
            "  ✅ \(.key): \(.value.message)"
          elif .value.status == "missing" then
            "  ❓ \(.key): \(.value.message)"
          else
            "  ❌ \(.key): \(.value.message)"
          end'

        # Create GitHub Actions summary
        {
          echo "## 📦 Submodule Check Results"
          echo ""
          if [ "$ALL_ON_MAIN" = "true" ]; then
            echo "🎉 **All submodules are on origin/main!**"
          else
            echo "⚠️ **Some submodules are not on origin/main**"
          fi
          echo ""
          echo "| Submodule | Status | Commit | Details |"
          echo "|-----------|--------|--------|---------|"
          echo "$RESULTS" | jq -r 'to_entries[] |
            "| \(.key) | \(if .value.status == "on_main" then "✅ On main" elif .value.status == "missing" then "❓ Missing" else "❌ Not on main" end) | `\(.value.commit // "N/A")` | \(.value.message) |"'
        } >> $GITHUB_STEP_SUMMARY

        # Final result
        if [ "$ALL_ON_MAIN" = "true" ]; then
          echo ""
          echo "🎉 All submodules are on origin/main!"
        else
          echo ""
          echo "💥 Some submodules are not on origin/main - failing check"
          exit 1
        fi
