name: "Check Version Change"
description: Checks if the VERSION file was modified in the current push

outputs:
  version_changed:
    description: "Whether the VERSION file was modified"
    value: ${{ steps.check.outputs.version_changed }}

runs:
  using: "composite"
  steps:
    - name: Check for VERSION change
      id: check
      uses: actions/github-script@v6
      with:
        script: |
          try {
            let base, head;

            if (context.eventName === 'push') {
              base = context.payload.before;
              head = context.payload.after;
            } else if (context.eventName === 'pull_request') {
              base = context.payload.pull_request.base.sha;
              head = context.payload.pull_request.head.sha;
            } else {
              console.log(`Unsupported event type: ${context.eventName}, returning false`);
              core.setOutput('version_changed', 'false');
              return;
            }

            if (!base || !head) {
              throw new Error('Could not determine base and head commits');
            }

            const { data: commits } = await github.rest.repos.compareCommits({
              owner: context.repo.owner,
              repo: context.repo.repo,
              base: base,
              head: head
            });

            console.log('Checking files changed in push:');
            const changedFiles = commits.files.map(file => file.filename);
            console.log(changedFiles);

            const versionChanged = changedFiles.includes('VERSION');
            console.log(`VERSION was ${versionChanged ? 'modified' : 'not modified'}`);

            core.setOutput('version_changed', versionChanged.toString());
          } catch (error) {
            console.error('Error checking version change:', error.message);
            core.setOutput('version_changed', 'false');
            core.setFailed(error.message);
          }
