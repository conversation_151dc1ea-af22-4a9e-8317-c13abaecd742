# This should be run at the end of the workflow, generally gated by:
# ```
# if: ${{ (success() || failure()) && github.event_name == 'workflow_dispatch' && inputs.debug_enabled }}
# ```
name: Start terminal session
description: Starts a terminal session for debugging purposes
runs:
  using: "composite"
  steps:
    - name: Start terminal session
      uses: mxschmitt/action-tmate@v3
      with:
        limit-access-to-actor: true
