name: Submodule Check

on:
  workflow_dispatch:
    inputs:
      submodules:
        description: "Comma-separated list of submodules to check"
        required: false
        default: "sdk,proxy"

permissions:
  contents: read

jobs:
  check-submodules:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: true
          fetch-depth: 0

      - name: Check submodules
        id: submodule-check
        uses: ./.github/actions/submodule-check
        with:
          submodules: ${{ inputs.submodules || 'sdk,proxy' }}

      - name: Display detailed results
        if: always()
        run: |
          echo "### 📊 Detailed JSON Results" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo '${{ steps.submodule-check.outputs.results }}' | jq '.' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
