name: Publish Branch Docker Build
on:
  workflow_dispatch:
    inputs:
      docker_tag:
        description: "Publish images with this docker tag. If not provided, the image will only be tagged with the git SHA."
        type: string
        required: false
      publish_git_sha:
        type: string
        description: "Publish images tags with this git SHA. Defaults to the last commit on the branch."
        required: false
      run_serial:
        type: boolean
        description: "Run docker tests in serial mode rather than parallel"
        required: false
        default: false
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  docker:
    uses: ./.github/workflows/reusable-build-test-docker.yaml
    permissions:
      id-token: write
      contents: read
    with:
      release_as_latest: false
      publish_release_tag: ${{ inputs.docker_tag }}
      publish_git_sha: ${{ inputs.publish_git_sha || github.sha }}
      run_serial: ${{ inputs.run_serial }}
      debug_enabled: ${{ inputs.debug_enabled }}
      skip_tests: ${{ inputs.skip_tests }}
    secrets:
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
