name: Build, Test, and Publish Docker
on:
  workflow_call:
    inputs:
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      run_serial:
        type: boolean
        description: "Run tests in serial mode rather than parallel"
        required: false
        default: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false
      release_as_latest:
        type: boolean
        description: "Release live to users as 'latest'"
        required: false
        default: false
      publish_release_tag:
        type: string
        description: "Publish with custom release tag. Optional"
        required: false
      publish_git_sha:
        type: string
        description: "Publish tagged with git SHA."
        required: false
    outputs:
      docker_tags:
        description: "Published Docker tags"
        value: ${{ jobs.docker-combine-multiarch.outputs.docker_tags }}
    secrets:
      DD_API_KEY:
        required: true
      POLAR_SIGNALS_API_KEY:
        required: true

jobs:
  docker-build-test:
    runs-on: ${{ matrix.arch == 'arm64' && 'warp-ubuntu-2404-arm64-16x' || 'warp-ubuntu-2404-x64-16x' }}
    strategy:
      fail-fast: false
      matrix:
        arch:
          - x86
          - arm64
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Shallow clone
          submodules: recursive

      - name: Setup buildx
        uses: docker/setup-buildx-action@v3

      - name: Install Braintrust dependencies
        uses: ./.github/actions/deps

      - name: Build docker images
        shell: bash
        env:
          BRAINSTORE_PROFILING_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
        run: |
          eval "$(mise activate)"
          ./scripts/deploy_docker.py build

      - name: Extract full API build
        shell: bash
        run: |
          eval "$(mise activate)"
          ./scripts/deploy_docker.py api-full-build ~/braintrust-standalone-api-full-build.zip

      - name: Run tests
        # Always skip ARM tests since we're doing testing in x86 already
        if: ${{ matrix.arch != 'arm64' }}
        shell: bash
        env:
          DD_CIVISIBILITY_AGENTLESS_ENABLED: true
          DD_API_KEY: ${{ secrets.DD_API_KEY }}
          DD_SITE: us5.datadoghq.com
          DD_ENV: ci
          FORCE_COLOR: 1
        run: |
          if [[ "${{ inputs.skip_tests }}" == "true" ]]; then
            echo "Skipping as requested"
            exit 0
          fi
          eval "$(mise activate)"
          cp app/.env.development app/.env.local
          TEST_ARGS=" ${{ inputs.run_serial && '--serial -- tests/bt_services' || '-- tests/bt_services' }} " python scripts/run_tests_standalone_docker.py --dump-logs docker_logs.txt
        timeout-minutes: 50

      - name: Upload docker logs
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: logfiles-${{ matrix.arch }}
          path: docker_logs.txt

      - name: Configure AWS credentials
        if: ${{ inputs.publish_git_sha }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::872608195481:role/github_ecr_full_access
          aws-region: us-east-1

      - name: Login to docker
        if: ${{ inputs.publish_git_sha }}
        shell: bash
        run: |
          eval "$(mise activate)"
          aws ecr-public get-login-password | docker login --username AWS --password-stdin public.ecr.aws

      - name: Publish docker images
        if: ${{ inputs.publish_git_sha }}
        shell: bash
        run: |
          eval "$(mise activate)"
          ./scripts/deploy_docker.py publish --allow-dirty --override-commit "${{ inputs.publish_git_sha }}"
          ./scripts/deploy_docker.py publish-api-full-build --allow-dirty --override-commit "${{ inputs.publish_git_sha }}" --file ~/braintrust-standalone-api-full-build.zip

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}

  docker-combine-multiarch:
    if: ${{ inputs.publish_git_sha }}
    needs: docker-build-test
    runs-on: warp-ubuntu-2404-x64-4x
    permissions:
      id-token: write
      contents: read
    outputs:
      docker_tags: ${{ steps.combine-images.outputs.tags }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Shallow clone
          submodules: recursive

      - name: Install Braintrust dependencies
        uses: ./.github/actions/deps

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::872608195481:role/github_ecr_full_access
          aws-region: us-east-1

      - name: Login to docker
        shell: bash
        run: |
          eval "$(mise activate)"
          aws ecr-public get-login-password | docker login --username AWS --password-stdin public.ecr.aws

      - name: Combine docker images
        id: combine-images
        shell: bash
        run: |
          eval "$(mise activate)"
          declare -a tags=()
          if [ "${{ inputs.release_as_latest }}" = true ]; then
            tags+=("latest")
          fi
          if [ -n "${{ inputs.publish_release_tag }}" ]; then
            tags+=("${{ inputs.publish_release_tag }}")
          fi
          # Converts to command delimited
          IFS=, additional_tags="${tags[*]}"
          ./scripts/deploy_docker.py combine --tag-sha "${{ github.sha }}" --additional-tags "$additional_tags"

          echo "## Published Docker Tags" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          for tag in "${tags[@]}"; do
            echo "- \`$tag\`" >> $GITHUB_STEP_SUMMARY
          done
          echo "- \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY

          # Output docker tags for use in release notes
          tags+=("${{ github.sha }}")
          IFS=, ALL_TAGS="${tags[*]}"
          echo "tags=$ALL_TAGS" >> $GITHUB_OUTPUT

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}
