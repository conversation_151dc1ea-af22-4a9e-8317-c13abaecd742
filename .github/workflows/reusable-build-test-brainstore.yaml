name: Build and Test Brainstore
on:
  workflow_call:
    inputs:
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false

jobs:
  build-and-test-brainstore:
    runs-on: warp-ubuntu-2404-x64-16x
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          submodules: recursive

      - name: Install Braintrust dependencies
        uses: ./.github/actions/deps

      - name: Build BTQL
        shell: bash
        run: |
          eval "$(mise activate)"
          pnpm build --filter @braintrust/btql

      - name: Build CLI
        shell: bash
        run: |
          eval "$(mise activate)"
          cd brainstore && cargo build --release

      - name: Run tests
        shell: bash
        run: |
          if [[ "${{ inputs.skip_tests }}" == "true" ]]; then
            echo "Skipping as requested"
            exit 0
          fi
          eval "$(mise activate)"
          cd brainstore
          cargo test --release
          cargo test --release -- --include-ignored test_worker_lifetime
          cargo test --release -- --include-ignored test_command_proc_sanity
          cargo test --release -- --include-ignored test_command_proc_multiple_workers
        timeout-minutes: 20

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}
