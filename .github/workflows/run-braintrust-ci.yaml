name: Run Braintrust CI
on:
  workflow_dispatch:
    inputs:
      publish_with_git_sha:
        type: boolean
        description: "Publish artifacts with git SHA."
        required: false
        default: true
      release_as_latest:
        description: "Release live to users as 'latest'"
        type: boolean
        required: false
        default: false
      publish_release_tag:
        description: "Publish with release tag:"
        type: string
        required: false
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      run_serial:
        type: boolean
        description: "Run tests in serial mode rather than parallel"
        required: false
        default: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

permissions:
  id-token: write
  contents: read

jobs:
  run-braintrust-ci:
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      release_as_latest: ${{ inputs.release_as_latest }}
      publish_release_tag: ${{ inputs.publish_release_tag }}
      publish_git_sha: ${{ inputs.publish_with_git_sha && github.sha || '' }}
      run_serial: ${{ inputs.run_serial }}
      skip_tests: ${{ inputs.skip_tests }}
      debug_enabled: ${{ inputs.debug_enabled }}
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
