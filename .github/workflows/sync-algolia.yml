name: Sync Algolia

on:
  workflow_dispatch:
    inputs:
      dry_run:
        description: Run in dry-run mode (does not write to Algolia)
        type: boolean
        default: false
  push:
    paths:
      - app/content/docs/**
      - app/content/blog/**
  pull_request:
    paths:
      - app/content/docs/**
      - app/content/blog/**

permissions:
  contents: read

concurrency:
  group: sync-algolia
  cancel-in-progress: false

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: pnpm

      - name: Install dependencies (workspace)
        run: pnpm install --frozen-lockfile

      - name: Select Algolia index (prod on main, preview otherwise)
        if: github.event_name == 'push'
        run: |
          if [ "${GITHUB_REF}" = "refs/heads/main" ]; then
            echo "ALGOLIA_INDEX_NAME=${{ secrets.ALGOLIA_INDEX_NAME_PROD }}" >> $GITHUB_ENV
          else
            echo "ALGOLIA_INDEX_NAME=${{ secrets.ALGOLIA_INDEX_NAME_PREVIEW }}" >> $GITHUB_ENV
          fi

      - name: Sync to Algolia (dry run)
        if: inputs.dry_run == true || github.event_name == 'pull_request'
        working-directory: app
        run: pnpm run sync-docs-to-algolia -- --dry-run

      - name: Sync to Algolia
        if: inputs.dry_run != true && github.event_name == 'push'
        working-directory: app
        env:
          ALGOLIA_APP_ID: ${{ secrets.ALGOLIA_APP_ID }}
          ALGOLIA_ADMIN_API_KEY: ${{ secrets.ALGOLIA_ADMIN_API_KEY }}
        run: pnpm run sync-docs-to-algolia
