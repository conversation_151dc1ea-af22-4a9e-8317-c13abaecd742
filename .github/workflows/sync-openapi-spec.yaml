name: Sync OpenAPI spec to public repo

on:
  push:
    branches:
      - main
    paths:
      - VERSION

  workflow_dispatch:
    inputs:
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

permissions:
  contents: write
  pull-requests: write

jobs:
  publish:
    runs-on: ubuntu-latest
    env:
      ORG: braintrustdata
      PUBLIC_REPO: braintrust-openapi
      BASE_BRANCH: main
      BRANCH_NAME: chore/sync-openapi
      COMMIT_MESSAGE: "chore: sync OpenAPI spec"
      PR_TITLE: "Sync OpenAPI spec"
      PR_BODY: "Automated sync from internal repo."
      REVIEWER: manugoyal
      SRC: openapi/openapi
      DEST: openapi

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Shallow clone
          submodules: recursive
      - uses: ./.github/actions/deps

      - name: Get bot token
        id: bot-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.GH_BOT_APP_ID }}
          private-key: ${{ secrets.GH_BOT_APP_PRIVATE_KEY }}
          owner: braintrustdata
          repositories: braintrust,${{ env.PUBLIC_REPO }}

      - name: Generate OpenAPI
        run: make openapi-spec

      - name: Checkout public repo
        uses: actions/checkout@v4
        with:
          repository: ${{ env.ORG }}/${{ env.PUBLIC_REPO }}
          token: ${{ steps.bot-token.outputs.token }}
          path: public
          fetch-depth: 0
          ref: ${{ env.BASE_BRANCH }}

      - name: Copy generated artifacts
        run: |
          set -euo pipefail
          mkdir -p "public/$(dirname "${DEST}")"
          if [ -d "${SRC}" ]; then
            rsync -a --delete "${SRC}/" "public/${DEST%/}/"
          else
            cp -f "${SRC}" "public/${DEST}"
          fi

      - name: Check for changes
        id: changes
        working-directory: public
        run: |
          if git diff --quiet; then
            echo "changed=false" >> $GITHUB_OUTPUT
          else
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Create PR (commit & push handled by action)
        uses: peter-evans/create-pull-request@v6
        if: ${{ !(steps.changes.outputs.changed == 'false') }}
        with:
          token: ${{ steps.bot-token.outputs.token }}
          path: public
          base: ${{ env.BASE_BRANCH }}
          branch: ${{ env.BRANCH_NAME }}
          commit-message: ${{ env.COMMIT_MESSAGE }}
          title: ${{ env.PR_TITLE }}
          body: ${{ env.PR_BODY }}
          reviewers: ${{ env.REVIEWER }}
          labels: auto-sync
          signoff: false
          committer: "braintrust-bot[bot] <215900051+${{ steps.bot-token.outputs.app-slug }}[bot]@users.noreply.github.com>"
          author: "braintrust-bot[bot] <215900051+${{ steps.bot-token.outputs.app-slug }}[bot]@users.noreply.github.com>"

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}
