name: Release
on:
  push:
    branches:
      - main
    paths:
      - VERSION
  workflow_dispatch:
    inputs:
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      run_serial:
        type: boolean
        description: "Run tests in serial mode rather than parallel"
        required: false
        default: false
      docker_run_serial:
        type: boolean
        description: "Run docker tests in serial mode rather than parallel"
        required: false
        default: true
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false
      custom_version:
        type: string
        description: "Custom version tag. (The VERSION file will not be updated)"
        required: false
        default: ""
      prerelease:
        type: boolean
        description: "Mark as pre-release and add alpha suffix to the version"
        required: false
        default: false

permissions:
  id-token: write # OIDC permissions for AWS auth
  contents: write # Required for creating releases

jobs:
  create-draft-release:
    runs-on: ubuntu-latest
    outputs:
      release_id: ${{ steps.create-draft-release.outputs.release_id }}
      version: ${{ steps.create-draft-release.outputs.version }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: "VERSION" # Only checkout the VERSION file

      - name: Create GitHub Release
        id: create-draft-release
        uses: actions/github-script@v7
        env:
          PRERELEASE: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.prerelease || 'false' }}
          CUSTOM_VERSION: ${{ github.event.inputs.custom_version }}
        with:
          script: |
            const fs = require('fs');
            let version = fs.readFileSync('VERSION', 'utf8').trim();
            const prerelease = process.env.PRERELEASE === 'true';
            const customVersion = process.env.CUSTOM_VERSION;
            if (customVersion) {
              version = customVersion;
            }
            if (prerelease) {
              version = version + '-alpha';
            }
            core.setOutput('version', version);
            try {
              const response = await github.rest.repos.createRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                tag_name: version,
                name: version,
                body: `Release ${version}`,
                generate_release_notes: true,
                target_commitish: "main",
                draft: true,
                prerelease: prerelease
              });
              core.info(`Created release: ${response.data.id}`);
              core.setOutput('release_id', response.data.id);
            } catch (error) {
              core.setFailed(`Failed to create release: ${error.message}`);
              throw error;
            }

  ci:
    needs: [create-draft-release]
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      # Github doesn't support ternary operators so this logic is ugly
      # Also INPUTS ARE ALWAYS STRINGS and you have to use fromJSON to convert to boolean. WTF
      release_as_latest: true
      publish_release_tag: ${{ needs.create-draft-release.outputs.version }}
      publish_git_sha: ${{ github.sha }}
      run_serial: ${{ github.event_name == 'workflow_dispatch' && fromJSON(github.event.inputs.run_serial) || false }}
      docker_run_serial: ${{ github.event_name == 'push' && true || fromJSON(github.event.inputs.docker_run_serial) }}
      skip_tests: ${{ github.event_name == 'push' && true || fromJSON(github.event.inputs.skip_tests) }}
      debug_enabled: ${{ github.event_name == 'workflow_dispatch' && fromJSON(github.event.inputs.debug_enabled) || false }}
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

  publish-release:
    needs: [create-draft-release, ci]
    runs-on: ubuntu-latest
    steps:
      - name: Update release with artifact information and publish
        uses: actions/github-script@v7
        env:
          RELEASE_ID: ${{ needs.create-draft-release.outputs.release_id }}
          VERSION: ${{ needs.create-draft-release.outputs.version }}
          CLOUDFORMATION_TEMPLATES: ${{ needs.ci.outputs.cloudformation_templates }}
          DOCKER_TAGS: ${{ needs.ci.outputs.docker_tags }}
        with:
          script: |
            const releaseId = process.env.RELEASE_ID;
            const version = process.env.VERSION;
            const cloudformationTemplates = process.env.CLOUDFORMATION_TEMPLATES;
            const dockerTags = process.env.DOCKER_TAGS;

            try {
              // First, get the current release to preserve its existing body
              const currentRelease = await github.rest.repos.getRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: releaseId
              });

              let releaseBody = currentRelease.data.body || '';

              let artifactInfo = '';

              if (dockerTags && dockerTags.trim() !== '') {
                artifactInfo += `\n\n## Docker Tags\n\n`;
                artifactInfo += `The following Docker images have been published to https://gallery.ecr.aws/braintrust/\n\n`;
                const tags = dockerTags.split(',');
                for (const tag of tags) {
                  if (tag.trim()) {
                    artifactInfo += `- \`${tag.trim()}\`\n`;
                  }
                }
              }

              if (cloudformationTemplates && cloudformationTemplates.trim() !== '') {
                artifactInfo += `\n\n## CloudFormation Templates\n\n`;
                artifactInfo += '```\n';
                artifactInfo += `${cloudformationTemplates}`;
                artifactInfo += '```\n';
              }

              // Append artifact information to existing body
              if (artifactInfo) {
                releaseBody += artifactInfo;
              }

              await github.rest.repos.updateRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: releaseId,
                body: releaseBody,
                draft: false
              });
              core.info(`Successfully published release ${releaseId} with artifact information appended`);
            } catch (error) {
              core.setFailed(`Failed to publish release: ${error.message}`);
              throw error;
            }
