name: <PERSON> and <PERSON> Stale PRs
on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          operations-per-run: 100

          days-before-pr-stale: 7
          days-before-pr-close: 7
          delete-branch: true

          stale-pr-label: "stale"
          stale-pr-message: "This PR has been automatically marked as stale because it has not had any activity in the last 7 days. It will be closed in 7 days if no further activity occurs. To prevent this, you can add a comment, update the PR, or remove the stale label."
          close-pr-message: "This PR is being closed automatically because it has been stale for 7 days with no activity. Feel free to reopen if you would like to continue working on this."

          # Disable stale checks for issues, only process PRs.
          days-before-issue-stale: -1
          days-before-issue-close: -1
