# syntax=docker/dockerfile:1
FROM rust:1.86-slim-bookworm AS builder
RUN apt-get update && apt-get upgrade -y && apt-get install -y build-essential curl pkg-config lsof libssl-dev wget binutils jq xxhash

ARG GIT_COMMIT_HASH
ENV GIT_COMMIT_HASH=${GIT_COMMIT_HASH}

WORKDIR /usr/src/brainstore
COPY . .

# Build with debug symbols
RUN <<EOT
    arch=$(uname -m)
    echo "Building brainstore for $arch"
    case "$arch" in
        x86_64)
            RUSTFLAGS="-C debuginfo=2 -C target-cpu=haswell -C target-feature=+aes,+sse2 --cfg tokio_unstable" cargo build --release --no-default-features --features distribute,enable_memprof
        ;;
        arm*|aarch64*)
            RUSTFLAGS="-C debuginfo=2 -C target-cpu=neoverse-n1 --cfg tokio_unstable" cargo build --release --no-default-features --features distribute,enable_memprof
        ;;
        *)
            echo "Unknown architecture: $arch"
            exit 1
        ;;
    esac
EOT

# Upload debug symbols if API key is provided via secret
# Using the bash script approach as recommended by Polar Signals
RUN --mount=type=secret,id=polar_signals_api_key,target=/run/secrets/polar_signals_api_key <<EOT
    if [ -f /run/secrets/polar_signals_api_key ] && [ -s /run/secrets/polar_signals_api_key ]; then
        echo "=========================================="
        echo "POLAR SIGNALS DEBUG SYMBOL UPLOAD"
        echo "=========================================="

        TOKEN=$(cat /run/secrets/polar_signals_api_key)

        echo "[1/5] Extracting debug symbols..."
        objcopy --only-keep-debug /usr/src/brainstore/target/release/brainstore /usr/src/brainstore/target/release/brainstore.debug

        # Get build info
        build_id=$(readelf -n /usr/src/brainstore/target/release/brainstore.debug | grep Build | awk '{print $3}')
        hash=$(xxhsum /usr/src/brainstore/target/release/brainstore.debug | awk '{print $1}')
        size=$(wc -c < /usr/src/brainstore/target/release/brainstore.debug)
        size_mb=$((size / 1024 / 1024))

        echo ""
        echo "Debug symbol info:"
        echo "  Build ID: $build_id"
        echo "  Hash: $hash"
        echo "  Size: ${size_mb}MB ($size bytes)"
        echo ""

        # Check if we should initiate upload
        echo "[2/5] Checking if upload is needed..."
        curl -s -H "Authorization: Bearer $TOKEN" \
             -H "Content-Type: application/json" \
             -d "{\"build_id\":\"$build_id\", \"force\": true}" \
             https://api.polarsignals.com/api/parca/debuginfo/shouldinitiateupload

        echo
        echo

        # Initiate upload
        echo "[3/5] Initiating upload..."
        response=$(curl -s -H "Authorization: Bearer $TOKEN" \
                       -H "Content-Type: application/json" \
                       -d "{\"build_id\":\"$build_id\", \"hash\": \"$hash\", \"size\": $size, \"force\": true}" \
                       https://api.polarsignals.com/api/parca/debuginfo/initiateupload)

        signedURL=$(echo $response | jq -r '.uploadInstructions.signedUrl')
        uploadID=$(echo $response | jq -r '.uploadInstructions.uploadId')

        if [ "$signedURL" != "null" ] && [ "$uploadID" != "null" ]; then
            echo "Upload approved. Upload ID: $uploadID"
            echo ""

            # Upload the debug symbols with progress bar
            echo "[4/5] Uploading ${size_mb}MB of debug symbols..."
            echo "=========================================="
            curl --progress-bar \
                 --upload-file /usr/src/brainstore/target/release/brainstore.debug \
                 "$signedURL" \
                 -o /dev/null
            echo "=========================================="
            echo ""

            # Mark upload as finished
            echo "[5/5] Finalizing upload..."
            curl -s -H "Authorization: Bearer $TOKEN" \
                 -H "Content-Type: application/json" \
                 -d "{\"build_id\":\"${build_id}\", \"upload_id\": \"${uploadID}\"}" \
                 https://api.polarsignals.com/api/parca/debuginfo/markuploadfinished

            echo ""
            echo "✅ Successfully uploaded debug symbols to Polar Signals!"
            echo "=========================================="
        else
            echo ""
            echo "❌ Failed to get upload URL from Polar Signals"
            echo "Response: $response"
            echo "=========================================="
        fi

        rm -f /usr/src/brainstore/target/release/brainstore.debug
    else
        echo "Polar Signals API key not provided, skipping debuginfo upload"
    fi
EOT

# Always strip debug symbols from the production binary to reduce size
RUN echo "Stripping debug symbols from binary..." && \
    objcopy --strip-debug /usr/src/brainstore/target/release/brainstore


FROM debian:bookworm-slim AS brainstore
RUN apt-get update && apt-get upgrade -y && apt-get install -y curl pkg-config libssl-dev ca-certificates lsof nvme-cli
COPY --from=builder /usr/src/brainstore/target/release/brainstore /usr/local/bin/brainstore

# Optionally mount a config file at /etc/brainstore.yaml
ENV BRAINSTORE_CONFIG=/etc/brainstore.yaml

EXPOSE 4000
HEALTHCHECK --interval=2s --timeout=5s --retries=3 \
    CMD curl --fail -L http://localhost:4000/

ENTRYPOINT ["brainstore"]
CMD ["web"]
