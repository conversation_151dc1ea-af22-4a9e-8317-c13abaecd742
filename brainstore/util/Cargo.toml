[package]
name = "util"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = { workspace = true }
arc-swap = { workspace = true }
byte-unit = { workspace = true }
chrono = { workspace = true }
itertools = { workspace = true }
log = { workspace = true }
once_cell = { workspace = true }
ptree = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_yaml = { workspace = true }
tracing = { path = "../tracing" }
url = { workspace = true }
uuid = { workspace = true }
ptree_derive = { path = "../ptree_derive" }
sha2 = { workspace = true }
bigjson = { path = "../bigjson" }
