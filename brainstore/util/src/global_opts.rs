// Global configuration options which should be initialized at the start of the program.

use std::sync::atomic::AtomicBool;

use once_cell::sync::Lazy;

// Corresponds to BaseArgs::less_verbose_info.
pub static SUPPRESS_VERBOSE_INFO: Lazy<AtomicBool> = Lazy::new(|| AtomicBool::new(false));

pub fn suppress_verbose_info() -> bool {
    SUPPRESS_VERBOSE_INFO.load(std::sync::atomic::Ordering::Relaxed)
}
