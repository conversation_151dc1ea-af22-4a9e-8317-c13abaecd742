use std::sync::atomic::{AtomicU64, Ordering};

/// A counter which keeps track of both the number of increments and the maximum value it has seen.
/// Useful for tracking how many concurrent tasks hit a critical section at the same time.
#[derive(Default, Debug)]
pub struct MaxCounter {
    counter: AtomicU64,
    max: AtomicU64,
}

impl MaxCounter {
    pub fn incr(&self) {
        self.incr_n(1);
    }

    pub fn incr_n(&self, n: u64) {
        let prev = self.counter.fetch_add(n, Ordering::SeqCst);
        self.max.fetch_max(prev + n, Ordering::SeqCst);
    }

    pub fn decr(&self) {
        self.decr_n(1);
    }

    pub fn decr_n(&self, n: u64) {
        self.counter.fetch_sub(n, Ordering::SeqCst);
    }

    pub fn counter(&self) -> u64 {
        self.counter.load(Ordering::SeqCst)
    }

    pub fn max(&self) -> u64 {
        self.max.load(Ordering::SeqCst)
    }
}
