use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use url::Url;

use crate::schema::{Schema, SchemaFile};
use crate::url_util::str_to_url;

pub const SEGMENTS_SUBDIR: &str = "segments";

// Example config (brainstore.yaml):
//
// version: 0.1
// metadata_uri: file://./metadata
// index_uri: file://./index

pub const GLOBAL_CONFIG_VERSION: &str = "0.1";

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct ConfigFile {
    pub version: Option<String>,
    pub schema: Option<SchemaFile>,

    #[serde(flatten)]
    pub storage: Option<StorageConfigFile>,
}

#[derive(Debug, <PERSON>lone, Default, Serialize, Deserialize)]
pub struct StorageConfigFile {
    pub metadata_uri: String,
    pub wal_uri: String,
    pub realtime_wal_uri: Option<String>,
    pub index_uri: String,
    pub locks_uri: Option<String>,
    pub xact_manager_uri: Option<String>,
    pub locks_manager_enable_bookkeeping: Option<bool>,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct Config {
    pub version: Option<String>,
    pub schema: Option<Schema>,
    pub storage: Option<StorageConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub metadata_uri: Url,
    pub wal_uri: Url,
    pub realtime_wal_uri: Option<Url>,
    pub index_uri: Url,
    pub locks_uri: Url,
    pub locks_manager_enable_bookkeeping: bool,
    pub xact_manager_uri: Url,
}

impl Config {
    pub fn from_file(path: &str) -> Result<Self> {
        let config_str = fs::read_to_string(Path::new(path))
            .with_context(|| format!("Failed to open config file: {}", path))?;
        let config_file: ConfigFile = if path.ends_with(".json") {
            serde_json::from_str(&config_str)
                .with_context(|| format!("Failed to parse json config file: {}", path))?
        } else {
            serde_yaml::from_str(&config_str)
                .with_context(|| format!("Failed to parse yaml config file: {}", path))?
        };

        let base_path = Path::new(path)
            .parent()
            .with_context(|| format!("Failed to get base path for: {}", path))?
            .to_str()
            .unwrap();

        Ok(Config {
            version: config_file.version,
            schema: config_file
                .schema
                .map(Schema::from_schema_file)
                .transpose()?,
            storage: config_file
                .storage
                .as_ref()
                .map(|s| {
                    Ok::<_, anyhow::Error>(StorageConfig {
                        metadata_uri: str_to_url(&s.metadata_uri, Some(base_path))?,
                        wal_uri: str_to_url(&s.wal_uri, Some(base_path))?,
                        realtime_wal_uri: s
                            .realtime_wal_uri
                            .as_ref()
                            .map(|u| str_to_url(u, Some(base_path)))
                            .transpose()?,
                        index_uri: str_to_url(&s.index_uri, Some(base_path))?,
                        locks_uri: s.locks_uri.as_ref().map_or_else(
                            || Ok(Url::parse("memory://").unwrap()),
                            |l| str_to_url(l, Some(base_path)),
                        )?,
                        locks_manager_enable_bookkeeping: s
                            .locks_manager_enable_bookkeeping
                            .unwrap_or(false),
                        xact_manager_uri: s.xact_manager_uri.as_ref().map_or_else(
                            || Ok(Url::parse("memory://").unwrap()),
                            |l| str_to_url(l, Some(base_path)),
                        )?,
                    })
                })
                .transpose()?,
        })
    }
}

impl ConfigFile {
    pub fn to_string(&self) -> Result<String> {
        Ok(serde_yaml::to_string(self)?)
    }
}
