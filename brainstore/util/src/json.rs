use std::{borrow::Cow, collections::HashSet};

use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use serde_json::{map::Entry, Map, Value};

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
#[serde(untagged)]
pub enum PathPiece {
    Key(String),
    Index(i64),
}

impl PathPiece {
    pub fn to_string(&self) -> String {
        match self {
            PathPiece::Key(s) => s.clone(),
            PathPiece::Index(i) => i.to_string(),
        }
    }
}

pub fn field_name_to_json_path(field_name: &[PathPiece]) -> (Vec<String>, bool) {
    let mut contains_array_index = false;
    let path = field_name
        .iter()
        .filter_map(|p| match p {
            PathPiece::Key(s) => Some(s.clone()),
            PathPiece::Index(_) => {
                contains_array_index = true;
                None
            }
        })
        .collect();
    (path, contains_array_index)
}

pub fn extract_key(p: &PathPiece) -> Option<&str> {
    match p {
        PathPiece::Key(s) => Some(s),
        PathPiece::Index(_) => None,
    }
}

/// Deep-merge the `from` value into the `into` value. The rules of deep merging are as follows:
/// - If both `into` and `from` are objects, then recursively deep-merge values between common keys.
/// - Otherwise, replace the `into` value with the `from` value.
pub fn deep_merge(into: &mut Value, from: &Value) {
    deep_merge_helper(into, from, None);
}

pub fn deep_merge_maps(into: &mut Map<String, Value>, from: &Map<String, Value>) {
    deep_merge_maps_helper(into, from, None);
}

// These are the same as the deep merge functions above, but they accept a `stop_deep_merge_paths`
// set, which is a set of paths that should not be deep merged beyond.
//
// E.g. if the `stop_deep_merge_paths` set contains the path ["a", "b", "c"], then if you are
// merging the following objects: into={"a": {"b": {"c": {"d": 1}}}}, from={"a": {"b": {"c": {"e":
// 2}}}}, then the resulting object will be {"a": {"b": {"c": {"e": 2}}}}.
pub fn deep_merge_with_stop_paths(
    into: &mut Value,
    from: &Value,
    stop_deep_merge_paths: &HashSet<Vec<&str>>,
) {
    deep_merge_helper(
        into,
        from,
        Some(MergePathsState {
            cur_path: vec![],
            stop_deep_merge_paths,
        }),
    );
}

pub fn deep_merge_maps_with_stop_paths(
    into: &mut Map<String, Value>,
    from: &Map<String, Value>,
    stop_deep_merge_paths: &HashSet<Vec<&str>>,
) {
    deep_merge_maps_helper(
        into,
        from,
        Some(MergePathsState {
            cur_path: vec![],
            stop_deep_merge_paths,
        }),
    );
}

struct MergePathsState<'a> {
    cur_path: Vec<&'a str>,
    stop_deep_merge_paths: &'a HashSet<Vec<&'a str>>,
}

fn deep_merge_helper<'a>(
    into: &mut Value,
    from: &'a Value,
    merge_paths_state: Option<MergePathsState<'a>>,
) -> Option<MergePathsState<'a>> {
    match (into, from) {
        (Value::Object(into_obj), Value::Object(from_obj)) => {
            return deep_merge_maps_helper(into_obj, from_obj, merge_paths_state);
        }
        (into, from) => {
            *into = from.clone();
            merge_paths_state
        }
    }
}

fn deep_merge_maps_helper<'a>(
    into: &mut Map<String, Value>,
    from: &'a Map<String, Value>,
    mut merge_paths_state: Option<MergePathsState<'a>>,
) -> Option<MergePathsState<'a>> {
    for (key, from_value) in from.iter() {
        if let Some(merge_paths) = merge_paths_state.as_mut() {
            merge_paths.cur_path.push(key);
        }
        match into.entry(key.clone()) {
            Entry::Occupied(mut into_entry) => {
                let should_deep_merge = if let Some(merge_paths) = merge_paths_state.as_mut() {
                    !merge_paths
                        .stop_deep_merge_paths
                        .contains(&merge_paths.cur_path)
                } else {
                    true
                };
                if should_deep_merge {
                    merge_paths_state =
                        deep_merge_helper(into_entry.get_mut(), from_value, merge_paths_state);
                } else {
                    *into_entry.get_mut() = from_value.clone();
                }
            }
            Entry::Vacant(into_entry) => {
                into_entry.insert(from_value.clone());
            }
        }
        if let Some(merge_paths) = merge_paths_state.as_mut() {
            merge_paths.cur_path.pop();
        }
    }
    merge_paths_state
}

// Tries to find a value at a given path of object keys and array indices, otherwise returns None.
pub fn value_at_path_pieces_cow<'a>(
    value: Cow<'a, Value>,
    path: &[PathPiece],
) -> Option<Cow<'a, Value>> {
    match value {
        Cow::Borrowed(v) => value_at_path_pieces(v, path).map(Cow::Borrowed),
        Cow::Owned(v) => value_at_path_pieces_owned(v, path).map(Cow::Owned),
    }
}

fn value_at_path_pieces<'a>(value: &'a Value, path: &[PathPiece]) -> Option<&'a Value> {
    let mut current = value;
    for component in path {
        match component {
            PathPiece::Key(key) => {
                if key.is_empty() {
                    continue;
                }
                current = current.get(key)?;
            }
            PathPiece::Index(idx) => {
                let wrapped_idx = (if *idx >= 0 {
                    *idx
                } else {
                    current.as_array()?.len() as i64 + *idx
                }) as usize;
                current = current.get(wrapped_idx)?;
            }
        }
    }
    Some(current)
}

#[cfg(test)]
pub fn value_at_path_pieces_for_testing<'a>(
    value: &'a Value,
    path: &[PathPiece],
) -> Option<&'a Value> {
    value_at_path_pieces(value, path)
}

fn value_at_path_pieces_owned(value: Value, path: &[PathPiece]) -> Option<Value> {
    let mut current = value;
    for component in path {
        match component {
            PathPiece::Key(key) => {
                if key.is_empty() {
                    continue;
                }
                current = match current {
                    Value::Object(mut o) => o.remove(key)?,
                    _ => return None,
                };
            }
            PathPiece::Index(idx) => {
                let wrapped_idx = (if *idx >= 0 {
                    *idx
                } else {
                    current.as_array()?.len() as i64 + *idx
                }) as usize;
                current = match current {
                    Value::Array(mut a) => {
                        if wrapped_idx >= a.len() {
                            return None;
                        }
                        a.swap_remove(wrapped_idx)
                    }
                    _ => return None,
                };
            }
        }
    }
    Some(current)
}

#[cfg(test)]
pub fn value_at_path_pieces_owned_for_testing(value: Value, path: &[PathPiece]) -> Option<Value> {
    value_at_path_pieces_owned(value, path)
}

// Tries to find a value at a given path of object keys, otherwise returns None.
pub fn value_at_path<'a>(
    value: &'a serde_json::Value,
    path: &[String],
) -> Option<&'a serde_json::Value> {
    let mut current = value;
    for key in path {
        if key.is_empty() {
            continue;
        }
        current = match current.get(key) {
            Some(value) => value,
            None => return None,
        };
    }
    Some(current)
}

// Tries to set a value at the given path, creating intermediate objects as necessary. Fails if we
// encounter a non-object along the path.
pub fn set_value_at_path(
    obj: &mut serde_json::Value,
    path: &[String],
    value: serde_json::Value,
) -> Result<()> {
    let mut current = obj;
    for key in path {
        if key.is_empty() {
            continue;
        }
        let current_obj = current
            .as_object_mut()
            .ok_or_else(|| anyhow!("Expected object"))?;
        current = current_obj
            .entry(key.clone())
            .or_insert(serde_json::Value::Object(Map::new()));
    }
    *current = value;
    Ok(())
}

// Deserialize a byte vector into a JSON value with no recursion limit. Should only be used when
// dealing with potentially large objects.
pub fn deserialize_json_no_recursion_limit(
    data: &[u8],
) -> Result<serde_json::Value, serde_json::Error> {
    let mut deserializer = serde_json::Deserializer::from_slice(data);
    deserializer.disable_recursion_limit();
    match serde_json::Value::deserialize(&mut deserializer) {
        Ok(v) => Ok(v),
        Err(e) => {
            log::debug!("Error deserializing JSON: {}. Falling back to bigjson.", e);
            deserialize_bigjson_no_recursion_limit(data).ok_or(e)
        }
    }
}

pub fn compute_non_empty_paths(value: &Value) -> Vec<Vec<String>> {
    let mut paths = Vec::new();
    match value {
        Value::Object(obj) => {
            let mut has_non_empty_children = false;
            for (key, value) in obj {
                let subpaths = compute_non_empty_paths(value);
                for subpath in subpaths {
                    let mut path = vec![key.clone()];
                    path.extend(subpath);
                    paths.push(path);

                    has_non_empty_children = true;
                }
            }
            if has_non_empty_children {
                paths.push(vec![]);
            }
        }
        Value::Array(arr) => {
            if !arr.is_empty() {
                paths.push(vec![]);
            }
            for (i, value) in arr.iter().enumerate() {
                let subpaths = compute_non_empty_paths(value);
                for subpath in subpaths {
                    let mut path = vec![i.to_string()];
                    path.extend(subpath);
                    paths.push(path);
                }
            }
        }
        Value::Null => {
            return vec![];
        }
        _ => {
            // This empty path element indicates that there is a leaf-value, but no further children.
            paths.push(vec![]);
        }
    }
    paths
}

pub fn serialize_path<T: AsRef<str>, I: IntoIterator<Item = T>>(path: I) -> String {
    path.into_iter()
        .map(|s| s.as_ref().replace(".", "\\."))
        .collect::<Vec<_>>()
        .join(".")
}

pub fn deepest_object_value(data: &Map<String, Value>) -> Option<(Vec<String>, &Value)> {
    let mut out: Option<(Vec<String>, &Value)> = None;

    let mut stack = vec![(vec![], data)];
    while let Some((path, obj)) = stack.pop() {
        for (key, value) in obj {
            let mut new_path = path.clone();
            new_path.push(key.clone());
            match value {
                Value::Object(obj) => {
                    stack.push((new_path, obj));
                }
                _ => match &out {
                    None => out = Some((new_path, value)),
                    Some((existing_path, _)) => {
                        if new_path.len() > existing_path.len() {
                            out = Some((new_path, value));
                        }
                    }
                },
            }
        }
    }

    out
}

fn deserialize_bigjson_no_recursion_limit(data: &[u8]) -> Option<serde_json::Value> {
    let mut deserializer = bigjson::Deserializer::from_slice(data);
    deserializer.disable_recursion_limit();
    let bigvalue = match bigjson::Value::deserialize(&mut deserializer) {
        Ok(bigvalue) => bigvalue,
        Err(e) => {
            log::debug!(
                "Error deserializing JSON: {}. Falling back to serde_json.",
                e
            );
            return None;
        }
    };
    Some(bigvalue_to_value(bigvalue))
}

fn bigvalue_to_value(bigvalue: bigjson::Value) -> serde_json::Value {
    match bigvalue {
        bigjson::Value::Number(n) => {
            let num: serde_json::Number = match serde_json::from_str(n.as_str()) {
                Ok(smallnum) => smallnum,
                Err(_) => {
                    return serde_json::Value::String(n.to_string());
                }
            };
            serde_json::Value::Number(num)
        }
        bigjson::Value::String(s) => serde_json::Value::String(s),
        bigjson::Value::Bool(b) => serde_json::Value::Bool(b),
        bigjson::Value::Array(a) => {
            serde_json::Value::Array(a.into_iter().map(bigvalue_to_value).collect())
        }
        bigjson::Value::Object(o) => serde_json::Value::Object(
            o.into_iter()
                .map(|(k, v)| (k, bigvalue_to_value(v)))
                .collect(),
        ),
        bigjson::Value::Null => serde_json::Value::Null,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_compute_paths() {
        let value = json!({
            "a": 1,
            "b": "hello",
            "c": {
                "d": true,
                "e": [1, 2, 3],
                "f": null,
            },
            "g": [
                {"h": 4},
                5,
                null,
                [6, 7]
            ],
            "i": [],
        });

        let paths = compute_non_empty_paths(&value);
        assert_eq!(paths.len(), 16);
        assert!(paths.contains(&vec!["a".to_string()]));
        assert!(paths.contains(&vec!["b".to_string()]));
        assert!(paths.contains(&vec!["c".to_string()]));
        assert!(paths.contains(&vec!["c".to_string(), "d".to_string()]));
        assert!(paths.contains(&vec!["c".to_string(), "e".to_string(), "0".to_string()]));
        assert!(paths.contains(&vec!["c".to_string(), "e".to_string(), "1".to_string()]));
        assert!(paths.contains(&vec!["c".to_string(), "e".to_string(), "2".to_string()]));
        assert!(!paths.contains(&vec!["c".to_string(), "f".to_string()]));
        assert!(paths.contains(&vec!["g".to_string(), "0".to_string(), "h".to_string()]));
        assert!(paths.contains(&vec!["g".to_string(), "1".to_string()]));
        assert!(!paths.contains(&vec!["g".to_string(), "2".to_string()]));
        assert!(paths.contains(&vec!["g".to_string(), "3".to_string(), "0".to_string()]));
        assert!(paths.contains(&vec!["g".to_string(), "3".to_string(), "1".to_string()]));
        assert!(!paths.contains(&vec!["i".to_string()]));

        let value = json!({
            "a": null,
            "b": [null, 1, null],
            "c": {
                "d": null,
            }
        });
        let paths = compute_non_empty_paths(&value);
        assert_eq!(paths.len(), 3);
        assert!(!paths.contains(&vec!["a".to_string()]));
        assert!(paths.contains(&vec!["b".to_string()]));
        assert!(!paths.contains(&vec!["c".to_string()]));
        assert!(!paths.contains(&vec!["b".to_string(), "0".to_string()]));
        assert!(paths.contains(&vec!["b".to_string(), "1".to_string()]));
        assert!(!paths.contains(&vec!["b".to_string(), "2".to_string()]));
        assert!(!paths.contains(&vec!["c".to_string(), "d".to_string()]));

        let value = json!({});
        let paths = compute_non_empty_paths(&value);
        assert_eq!(paths.len(), 0);

        let value = json!([]);
        let paths = compute_non_empty_paths(&value);
        assert_eq!(paths.len(), 0);

        let value = json!(1);
        let paths = compute_non_empty_paths(&value);
        assert_eq!(paths.len(), 1);
        assert!(paths.contains(&vec![]));

        let value = json!(null);
        let paths = compute_non_empty_paths(&value);
        assert_eq!(paths.len(), 0);
    }

    #[test]
    fn test_serialize_path() {
        assert_eq!(serialize_path::<&String, &Vec<String>>(&vec![]), "");
        assert_eq!(serialize_path(&vec!["a".to_string()]), "a");
        assert_eq!(serialize_path(&vec!["a.b".to_string()]), "a\\.b");
        assert_eq!(
            serialize_path(&vec!["a".to_string(), "b".to_string()]),
            "a.b"
        );
        assert_eq!(
            serialize_path(&vec!["a".to_string(), "b.c".to_string()]),
            "a.b\\.c"
        );
        assert_eq!(
            serialize_path(&vec!["a.b".to_string(), "c".to_string()]),
            "a\\.b.c"
        );
        assert_eq!(
            serialize_path(&vec!["a.b".to_string(), "c.d".to_string()]),
            "a\\.b.c\\.d"
        );

        // Make sure these don't conflict with each other
        let path1 = serialize_path(&vec!["a.b".to_string()]);
        let path2 = serialize_path(&vec!["a".to_string(), "b".to_string()]);
        assert_ne!(path1, path2);

        let path3 = serialize_path(&vec!["a.b.c".to_string()]);
        let path4 = serialize_path(&vec!["a.b".to_string(), "c".to_string()]);
        let path5 = serialize_path(&vec!["a".to_string(), "b.c".to_string()]);
        let path6 = serialize_path(&vec!["a".to_string(), "b".to_string(), "c".to_string()]);
        assert_ne!(path3, path4);
        assert_ne!(path3, path5);
        assert_ne!(path3, path6);
        assert_ne!(path4, path5);
        assert_ne!(path4, path6);
        assert_ne!(path5, path6);
    }

    #[test]
    fn test_bignum() {
        let data = r#"{"Value": 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000, "small_num": 123}"#;
        let value = deserialize_json_no_recursion_limit(data.as_bytes()).unwrap();
        assert_eq!(value["small_num"], json!(123));
        assert_eq!(value["Value"], json!("179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"));
    }
}
