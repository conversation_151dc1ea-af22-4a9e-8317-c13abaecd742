use std::collections::HashSet;

use super::ptree::MakePTree;
use anyhow::{anyhow, Result};
use ptree::TreeBuilder;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Schema {
    name: String,
    fields: Vec<Field>,

    // Schemas must be "invertible". A schema is invertible if there exists a "stored" tantivy
    // field on each top-level schema field. Invertibility is necessary for reconstructing the
    // original document from what we store in the search index, which is required for operations
    // like merge-updates. To avoid ambiguity in reconstructing the original value, we require that
    // all "stored" fields have the same type.
    //
    // To this end, we validate and store a index pointer to an invertible tantivy field
    // corresponding to each toplevel field.
    invert_tantivy_field_indices: Vec<usize>,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct ConstructSchemaOpts {
    // If true, automatically assign field_ts values to fields that don't have them. This is useful
    // for testing scenarios where strictly ordering fields does not matter.
    #[serde(default)]
    pub auto_assign_field_ts: bool,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SchemaFile {
    pub name: String,
    pub fields: Vec<Field>,
    #[serde(default)]
    pub opts: ConstructSchemaOpts,
}

impl Schema {
    pub fn new(name: String, fields: Vec<Field>, opts: ConstructSchemaOpts) -> Result<Self> {
        let fields = if opts.auto_assign_field_ts {
            let mut field_ts_counter = fields
                .iter()
                .flat_map(|f| f.tantivy.iter().map(|f| f.field_ts))
                .max()
                .unwrap_or(0)
                + 1;
            fields
                .into_iter()
                .map(|mut field| {
                    for f in &mut field.tantivy {
                        if f.field_ts == 0 {
                            f.field_ts = field_ts_counter;
                            field_ts_counter += 1;
                        }
                    }
                    field
                })
                .collect()
        } else {
            fields
        };

        // Check field_ts.
        let mut all_field_ts: HashSet<u64> = HashSet::new();
        for field in &fields {
            for f in &field.tantivy {
                if f.field_ts == 0 {
                    return Err(anyhow!(
                        "Field \"{}.{}\" has field_ts 0.",
                        field.name,
                        f.name
                    ));
                }
                if !all_field_ts.insert(f.field_ts) {
                    return Err(anyhow!(
                        "Field \"{}.{}\" has duplicate field_ts: {}",
                        field.name,
                        f.name,
                        f.field_ts
                    ));
                }
            }
        }

        let invert_tantivy_field_indices = fields
            .iter()
            .map(|field| {
                let mut stored_field_info: Option<(usize, TantivyType)> = None;
                for (i, f) in field.tantivy.iter().enumerate() {
                    if f.field_type.stored() {
                        match &stored_field_info {
                            None => stored_field_info = Some((i, f.field_type.clone())),
                            Some((_, stored_field_type)) => {
                                if std::mem::discriminant(stored_field_type) != std::mem::discriminant(&f.field_type) {
                                    return Err(anyhow!(
                                        "Field \"{}\" has multiple stored tantivy fields with different types.",
                                        field.name
                                    ));
                                }
                            }
                        }
                    }
                }
                stored_field_info.map(|(i, _)| i).ok_or_else(|| {
                    anyhow!(
                        "Field \"{}\" does not have a stored tantivy field.",
                        field.name
                    )
                })
            })
            .collect::<Result<Vec<usize>>>()?;
        Ok(Self {
            name,
            fields,
            invert_tantivy_field_indices,
        })
    }

    pub fn from_schema_file(serialized: SchemaFile) -> Result<Self> {
        Self::new(serialized.name, serialized.fields, serialized.opts)
    }

    pub fn name(&self) -> &String {
        &self.name
    }

    pub fn fields(&self) -> &Vec<Field> {
        &self.fields
    }

    pub fn invert_tantivy_field_indices(&self) -> &Vec<usize> {
        &self.invert_tantivy_field_indices
    }

    pub fn find_field(&self, name: &str) -> Option<&Field> {
        self.fields.iter().find(|f| f.name == name)
    }
}

impl Default for Schema {
    fn default() -> Self {
        Self::new("".to_string(), vec![], ConstructSchemaOpts::default()).unwrap()
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Field {
    pub name: String,
    pub tantivy: Vec<TantivyField>,
}

impl Field {
    pub fn find_tantivy_field(&self, opts: TantivyFieldFilterOpts) -> Option<&TantivyField> {
        self.tantivy.iter().find(|f| {
            (match opts.tokenized {
                None => true,
                Some(b) => f.field_type.tokenized() == b,
            }) && (match opts.stored {
                None => true,
                Some(b) => f.field_type.stored() == b,
            }) && (match opts.fast {
                None => true,
                Some(b) => f.field_type.fast() == b,
            })
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct TantivyFieldFilterOpts {
    pub tokenized: Option<bool>,
    pub stored: Option<bool>,
    pub fast: Option<bool>,
}

impl TantivyFieldFilterOpts {
    pub fn new() -> Self {
        TantivyFieldFilterOpts {
            tokenized: None,
            stored: None,
            fast: None,
        }
    }

    pub fn is_tokenized(mut self) -> Self {
        self.tokenized = Some(true);
        self
    }
    pub fn is_not_tokenized(mut self) -> Self {
        self.tokenized = Some(false);
        self
    }
    pub fn is_stored(mut self) -> Self {
        self.stored = Some(true);
        self
    }
    pub fn is_not_stored(mut self) -> Self {
        self.stored = Some(false);
        self
    }
    pub fn is_fast(mut self) -> Self {
        self.fast = Some(true);
        self
    }
    pub fn is_not_fast(mut self) -> Self {
        self.fast = Some(false);
        self
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct TantivyField {
    pub name: String,

    // field_ts is a 64-bit number representing the global timestamp that the field was added.
    // Conventionally this is the number of milliseconds since the Unix epoch. One way to obtain
    // this is by running `python -c 'import time; print(int(time.time() * 1000))'`.
    //
    // We need a field number so that fields can be materialized into the tantivy schema in
    // field-number order but declared in this higher-level schema in an arbitrary order.
    //
    // NOTE: the field value is defaulted to 0. When constructing schemas, we will reject any
    // `field_ts` values of 0, unless the `auto_assign_field_ts` flag is set to true, in which case
    // we will auto-determine the values during construction time.
    #[serde(default)]
    pub field_ts: u64,

    // If set to true, allow repeated values for this field (and return single values as an array).
    #[serde(default)]
    pub repeated: bool,

    #[serde(rename = "type")]
    pub field_type: TantivyType,

    // If set to true, when this field is a fast field, it cannot be used in a projection.
    #[serde(default)]
    pub lossy_fast_field: bool,
}

// This mirrors https://docs.rs/tantivy/latest/tantivy/schema/enum.FieldType.html but
// our own implementation lets us control the serialization format.
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum TantivyType {
    #[serde(rename = "str")]
    Str(TextOptions),
    #[serde(rename = "u64")]
    U64(BaseOptions),
    #[serde(rename = "i64")]
    I64(BaseOptions),
    #[serde(rename = "f64")]
    F64(BaseOptions),
    #[serde(rename = "bool")]
    Bool(BaseOptions),
    #[serde(rename = "date")]
    Date(BaseOptions),
    #[serde(rename = "facet")]
    Facet(FacetOptions),
    #[serde(rename = "bytes")]
    Bytes(BaseOptions),
    #[serde(rename = "json")]
    Json(TextOptions),
    #[serde(rename = "ip_addr")]
    IpAddr(BaseOptions),
}

impl MakePTree for TantivyType {
    fn label(&self) -> String {
        format!("{:?}", self)
    }
    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

#[derive(Debug, Clone, Copy)]
pub enum TantivyFastValueType {
    Bool,
    I64,
    U64,
    F64,
    DateTime,
}

impl MakePTree for TantivyFastValueType {
    fn label(&self) -> String {
        format!("{:?}", self)
    }
    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl TantivyType {
    pub fn tokenized(&self) -> bool {
        match self {
            TantivyType::Str(options) | TantivyType::Json(options) => options.tokenize,
            _ => true,
        }
    }

    pub fn stored(&self) -> bool {
        match self {
            TantivyType::Str(options) | TantivyType::Json(options) => options.stored,
            TantivyType::U64(options)
            | TantivyType::I64(options)
            | TantivyType::F64(options)
            | TantivyType::Bool(options)
            | TantivyType::Date(options)
            | TantivyType::Bytes(options)
            | TantivyType::IpAddr(options) => options.stored,
            TantivyType::Facet(options) => options.stored,
        }
    }

    pub fn fast(&self) -> bool {
        match self {
            TantivyType::Str(options) | TantivyType::Json(options) => options.fast,
            TantivyType::U64(options)
            | TantivyType::I64(options)
            | TantivyType::F64(options)
            | TantivyType::Bool(options)
            | TantivyType::Date(options)
            | TantivyType::Bytes(options)
            | TantivyType::IpAddr(options) => options.fast,
            TantivyType::Facet(_) => false,
        }
    }

    pub fn fast_field_value_type(&self) -> Option<TantivyFastValueType> {
        Some(match self {
            TantivyType::Bool(_) => TantivyFastValueType::Bool,
            TantivyType::U64(_) => TantivyFastValueType::U64,
            TantivyType::I64(_) => TantivyFastValueType::I64,
            TantivyType::F64(_) => TantivyFastValueType::F64,
            TantivyType::Date(_) => TantivyFastValueType::DateTime,
            _ => return None,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq, Eq)]
pub struct BaseOptions {
    pub stored: bool,
    #[serde(default)]
    pub fast: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq, Eq)]
pub struct TextOptions {
    pub stored: bool,
    #[serde(default)]
    pub fast: bool,
    #[serde(default)]
    pub tokenize: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq, Eq)]
pub struct FacetOptions {
    pub stored: bool,
}

impl MakePTree for TantivyField {
    fn label(&self) -> String {
        format!("Tantivy field \"{}\"", self.name)
    }
    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}
