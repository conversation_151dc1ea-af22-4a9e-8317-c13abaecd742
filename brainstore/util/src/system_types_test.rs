use crate::system_types::ObjectType;

use super::system_types::{FullObjectIdOwned, FullRowIdOwned, ObjectIdOwned};

fn test_serde_roundtrip<
    T: serde::Serialize
        + serde::de::DeserializeOwned
        + std::fmt::Display
        + std::str::FromStr
        + std::fmt::Debug
        + PartialEq,
>(
    value: T,
) where
    <T as std::str::FromStr>::Err: std::fmt::Debug,
{
    let serialized = serde_json::to_string(&value).unwrap();
    let deserialized = serde_json::from_str::<T>(&serialized).unwrap();
    assert_eq!(value, deserialized);

    let value_str = value.to_string();
    let value2 = value_str.parse::<T>().unwrap();
    assert_eq!(value, value2);
}

#[test]
fn test_object_id() {
    for object_id in &[
        ObjectIdOwned::new("foo".to_string()).unwrap(),
        ObjectIdOwned::new("foo bar baz".to_string()).unwrap(),
    ] {
        test_serde_roundtrip(object_id.clone());
        assert_eq!(object_id.to_string(), object_id.as_ref().to_string());
    }

    assert!(ObjectIdOwned::new("foo:bar".to_string()).is_err());
    assert!(ObjectIdOwned::new("foo bear:bar".to_string()).is_err());
}

#[test]
fn test_full_object_id() {
    for full_object_id in &[
        FullObjectIdOwned {
            object_type: ObjectType::Experiment,
            object_id: ObjectIdOwned::new("foo".to_string()).unwrap(),
        },
        FullObjectIdOwned {
            object_type: ObjectType::Dataset,
            object_id: ObjectIdOwned::new("bar baz boop".to_string()).unwrap(),
        },
    ] {
        test_serde_roundtrip(full_object_id.clone());
        assert_eq!(
            full_object_id.to_string(),
            full_object_id.as_ref().to_string()
        );
    }
}

#[test]
fn test_full_row_id() {
    for full_row_id in &[
        FullRowIdOwned {
            object_type: ObjectType::Experiment,
            object_id: ObjectIdOwned::new("foo bear".to_string()).unwrap(),
            id: "goobear bob".to_string(),
        },
        FullRowIdOwned {
            object_type: ObjectType::Experiment,
            object_id: ObjectIdOwned::new("foo bear".to_string()).unwrap(),
            id: "go:the:way".to_string(),
        },
    ] {
        test_serde_roundtrip(full_row_id.clone());
        assert_eq!(full_row_id.to_string(), full_row_id.as_ref().to_string());
    }
}
