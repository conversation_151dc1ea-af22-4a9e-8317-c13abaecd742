use std::io::Write;

/// Assert equality between two HashMaps.
pub fn assert_hashmap_eq<
    K: std::hash::Hash + Eq + std::fmt::Debug,
    V: PartialEq + std::fmt::Debug,
>(
    left: &std::collections::HashMap<K, V>,
    right: &std::collections::HashMap<K, V>,
) {
    assert_eq!(left.len(), right.len());
    for (k, v) in left {
        assert_eq!(Some(v), right.get(k), "Mismatch on key {:?}", k);
    }
}

// A fake "breakpoint" which just asks for input from STDIN before continuing.
pub fn stdin_breakpoint() {
    print!("Breakpoint: press enter to continue...");
    std::io::stdout().flush().unwrap();
    let mut buf = String::new();
    std::io::stdin().read_line(&mut buf).unwrap();
}
