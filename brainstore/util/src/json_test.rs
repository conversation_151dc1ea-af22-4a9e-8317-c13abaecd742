use crate::json::{
    deep_merge, deep_merge_maps_with_stop_paths, deep_merge_with_stop_paths,
    deserialize_json_no_recursion_limit, set_value_at_path, value_at_path,
    value_at_path_pieces_for_testing, value_at_path_pieces_owned_for_testing, PathPiece,
};
use serde_json::{json, Map, Value};
use std::collections::HashSet;

#[test]
fn test_deep_merge_objects() {
    let mut into = json!({
        "a": 1,
        "b": {
            "c": 2,
            "d": 3
        }
    });
    let from = json!({
        "b": {
            "c": 4,
            "e": 5
        },
        "f": 6
    });
    deep_merge(&mut into, &from);
    assert_eq!(
        into,
        json!({
            "a": 1,
            "b": {
                "c": 4,
                "d": 3,
                "e": 5
            },
            "f": 6
        })
    );
}

#[test]
fn test_deep_merge_arrays() {
    let mut into = json!([1, 2, 3]);
    let from = json!([4, 5, 6]);
    deep_merge(&mut into, &from);
    assert_eq!(into, json!([4, 5, 6]));
}

#[test]
fn test_deep_merge_primitives() {
    let mut into = json!(1);
    let from = json!(2);
    deep_merge(&mut into, &from);
    assert_eq!(into, json!(2));
}

#[test]
fn test_deep_merge_mixed_types() {
    let mut into = json!({
        "a": 1,
        "b": [1, 2, 3],
        "c": {
            "d": 4
        }
    });
    let from = json!({
        "b": {
            "e": 5
        },
        "c": [6, 7, 8]
    });
    deep_merge(&mut into, &from);
    assert_eq!(
        into,
        json!({
            "a": 1,
            "b": {
                "e": 5
            },
            "c": [6, 7, 8]
        })
    );
}

#[test]
fn test_deep_merge_null_values() {
    let mut into = json!({
        "a": 1,
        "b": null
    });
    let from = json!({
        "b": 2,
        "c": null
    });
    deep_merge(&mut into, &from);
    assert_eq!(
        into,
        json!({
            "a": 1,
            "b": 2,
            "c": null
        })
    );
}

#[test]
fn test_get_set_value_at_path() {
    let obj = json!({
        "a": "b",
        "c": {
            "d": "e"
        }
    });

    assert_eq!(value_at_path(&obj, &[]), Some(&obj));
    assert_eq!(
        value_at_path(&obj, &["a".to_string(), "b".to_string()]),
        None
    );
    assert_eq!(
        value_at_path(&obj, &["c".to_string()]),
        Some(&json!({ "d": "e" }))
    );
    assert_eq!(
        value_at_path(&obj, &["c".to_string(), "d".to_string()]),
        Some(&json!("e"))
    );

    let mut new_obj = json!({});
    assert!(set_value_at_path(&mut new_obj, &["a".to_string()], json!("b")).is_ok());
    assert!(set_value_at_path(
        &mut new_obj,
        &["a".to_string(), "b".to_string()],
        json!("c")
    )
    .is_err());
    assert!(set_value_at_path(
        &mut new_obj,
        &["c".to_string(), "d".to_string()],
        json!("e")
    )
    .is_ok());
    assert_eq!(obj, new_obj);
}

#[test]
fn test_value_at_path_pieces() {
    let obj = json!({
        "a": [1, 2, {"name": "alice"}],
        "b": {
            "strings": ["foo", "bar", "baz"],
            "nested": {
                "users": [{"id": "u1"}, {"id": "u2"}],
                "layers": [
                    [10, 20],
                    [30, [40, 50]],
                    {"object": [60, 70, 80]}
                ]
            }
        }
    });

    assert_eq!(value_at_path_pieces_for_testing(&obj, &[]), Some(&obj));

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("strings".to_string()),
                PathPiece::Index(-1)
            ]
        ),
        Some(&json!("baz"))
    );

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("strings".to_string()),
                PathPiece::Index(-2)
            ]
        ),
        Some(&json!("bar"))
    );

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("strings".to_string()),
                PathPiece::Index(-4)
            ]
        ),
        None
    );

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(1),
                PathPiece::Index(1),
                PathPiece::Index(0)
            ]
        ),
        Some(&json!(40))
    );

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(1),
                PathPiece::Index(1)
            ]
        ),
        Some(&json!([40, 50]))
    );

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(2)
            ]
        ),
        Some(&json!({"object": [60, 70, 80]}))
    );

    assert_eq!(
        value_at_path_pieces_for_testing(
            &obj,
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(-1),
                PathPiece::Key("object".to_string()),
                PathPiece::Index(1)
            ]
        ),
        Some(&json!(70))
    );
}

#[test]
fn test_value_at_path_pieces_owned() {
    let obj = json!({
        "a": [1, 2, {"name": "alice"}],
        "b": {
            "strings": ["foo", "bar", "baz"],
            "nested": {
                "users": [{"id": "u1"}, {"id": "u2"}],
                "layers": [
                    [10, 20],
                    [30, [40, 50]],
                    {"object": [60, 70, 80]}
                ]
            }
        }
    });

    assert_eq!(
        value_at_path_pieces_owned_for_testing(obj.clone(), &[]),
        Some(obj.clone())
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("strings".to_string()),
                PathPiece::Index(-1)
            ]
        ),
        Some(json!("baz"))
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("strings".to_string()),
                PathPiece::Index(-2)
            ]
        ),
        Some(json!("bar"))
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("strings".to_string()),
                PathPiece::Index(-4)
            ]
        ),
        None
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(1),
                PathPiece::Index(1),
                PathPiece::Index(0)
            ]
        ),
        Some(json!(40))
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(1),
                PathPiece::Index(1)
            ]
        ),
        Some(json!([40, 50]))
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(2)
            ]
        ),
        Some(json!({"object": [60, 70, 80]}))
    );

    assert_eq!(
        value_at_path_pieces_owned_for_testing(
            obj.clone(),
            &[
                PathPiece::Key("b".to_string()),
                PathPiece::Key("nested".to_string()),
                PathPiece::Key("layers".to_string()),
                PathPiece::Index(-1),
                PathPiece::Key("object".to_string()),
                PathPiece::Index(1)
            ]
        ),
        Some(json!(70))
    );
}

#[test]
fn test_serde_large_object() {
    let value = {
        let mut value = serde_json::Value::Null;
        for _ in 0..1000 {
            value = serde_json::Value::Object([("key".to_string(), value)].into_iter().collect());
        }
        value
    };
    let obj_str = serde_json::to_string(&value).unwrap();
    let deserialized = deserialize_json_no_recursion_limit(obj_str.as_bytes()).unwrap();
    assert_eq!(value, deserialized);
}

#[test]
fn test_deep_merge_with_stop_paths() {
    // Test case 1: Basic merge with stop path at shallow level - testing both functions
    {
        // A) Testing deep_merge_with_stop_paths
        let mut into_value = json!({
            "a": {
                "b": {
                    "c": 1,
                    "d": 2
                }
            },
            "x": 10
        });

        let from_value = json!({
            "a": {
                "b": {
                    "c": 3,
                    "e": 4
                }
            },
            "y": 20
        });

        // Create a stop path for ["a", "b"]
        let mut stop_paths = HashSet::new();
        stop_paths.insert(vec!["a", "b"]);

        deep_merge_with_stop_paths(&mut into_value, &from_value, &stop_paths);

        // The path "a.b" should NOT be deeply merged - it should be completely overwritten
        assert_eq!(
            into_value,
            json!({
                "a": {
                    "b": {
                        "c": 3,
                        "e": 4
                    }
                },
                "x": 10,
                "y": 20
            })
        );

        // B) Testing deep_merge_maps_with_stop_paths with the same scenario
        let mut into_map = Map::new();
        into_map.insert(
            "a".to_string(),
            json!({
                "b": {
                    "c": 1,
                    "d": 2
                }
            }),
        );
        into_map.insert("x".to_string(), json!(10));

        let mut from_map = Map::new();
        from_map.insert(
            "a".to_string(),
            json!({
                "b": {
                    "c": 3,
                    "e": 4
                }
            }),
        );
        from_map.insert("y".to_string(), json!(20));

        deep_merge_maps_with_stop_paths(&mut into_map, &from_map, &stop_paths);

        // Convert back to Value for comparison
        let map_result = Value::Object(into_map);

        // The results should be identical between both functions
        assert_eq!(map_result, into_value);
    }

    // Test case 2: Multiple stop paths
    {
        let mut into = json!({
            "a": {
                "b": {
                    "c": 1,
                    "d": 2
                }
            },
            "x": {
                "y": {
                    "z": 3
                }
            }
        });

        let from = json!({
            "a": {
                "b": {
                    "c": 10,
                    "e": 20
                }
            },
            "x": {
                "y": {
                    "z": 30,
                    "w": 40
                }
            }
        });

        // Create stop paths for ["a", "b"] and ["x"]
        let mut stop_paths = HashSet::new();
        stop_paths.insert(vec!["a", "b"]);
        stop_paths.insert(vec!["x"]);

        deep_merge_with_stop_paths(&mut into, &from, &stop_paths);

        // Both "a.b" and "x" paths should NOT be deeply merged but completely overwritten
        assert_eq!(
            into,
            json!({
                "a": {
                    "b": {
                        "c": 10,
                        "e": 20
                    }
                },
                "x": {
                    "y": {
                        "z": 30,
                        "w": 40
                    }
                }
            })
        );
    }

    // Test case 3: Stop path with arrays and non-object values
    {
        let mut into = json!({
            "a": [1, 2, 3],
            "b": "hello",
            "c": {
                "d": [4, 5, 6]
            }
        });

        let from = json!({
            "a": [7, 8, 9],
            "b": "world",
            "c": {
                "d": [10, 11, 12]
            }
        });

        // Create a stop path for ["c"]
        let mut stop_paths = HashSet::new();
        stop_paths.insert(vec!["c"]);

        deep_merge_with_stop_paths(&mut into, &from, &stop_paths);

        // "c" path should NOT be deeply merged but completely overwritten, and "a" and "b" should also be replaced
        assert_eq!(
            into,
            json!({
                "a": [7, 8, 9],
                "b": "world",
                "c": {
                    "d": [10, 11, 12]
                }
            })
        );
    }

    // Test case 4: Empty stop paths
    {
        let mut into = json!({
            "a": {
                "b": {
                    "c": 1
                }
            }
        });

        let from = json!({
            "a": {
                "b": {
                    "d": 2
                }
            }
        });

        // Empty set of stop paths
        let stop_paths = HashSet::new();

        deep_merge_with_stop_paths(&mut into, &from, &stop_paths);

        // Should behave like normal deep_merge
        assert_eq!(
            into,
            json!({
                "a": {
                    "b": {
                        "c": 1,
                        "d": 2
                    }
                }
            })
        );
    }

    // Test case 5: Nested path beyond stop path
    {
        let mut into = json!({
            "a": {
                "b": {
                    "c": {
                        "d": 1
                    }
                }
            }
        });

        let from = json!({
            "a": {
                "b": {
                    "c": {
                        "e": 2
                    }
                }
            }
        });

        // Create a stop path for ["a", "b", "c"]
        let mut stop_paths = HashSet::new();
        stop_paths.insert(vec!["a", "b", "c"]);

        deep_merge_with_stop_paths(&mut into, &from, &stop_paths);

        // The path "a.b.c" should NOT be deeply merged but completely overwritten
        assert_eq!(
            into,
            json!({
                "a": {
                    "b": {
                        "c": {
                            "e": 2
                        }
                    }
                }
            })
        );
    }

    // Test case 6: Compare with and without stop paths
    {
        // First with stop paths
        let mut into_map1 = Map::new();
        into_map1.insert(
            "a".to_string(),
            json!({
                "b": {
                    "c": 1
                }
            }),
        );

        let mut from_map = Map::new();
        from_map.insert(
            "a".to_string(),
            json!({
                "b": {
                    "d": 2
                }
            }),
        );

        // Create a stop path for ["a", "b"]
        let mut stop_paths = HashSet::new();
        stop_paths.insert(vec!["a", "b"]);

        deep_merge_maps_with_stop_paths(&mut into_map1, &from_map, &stop_paths);

        // Now without stop paths (regular deep merge)
        let mut into_map2 = Map::new();
        into_map2.insert(
            "a".to_string(),
            json!({
                "b": {
                    "c": 1
                }
            }),
        );

        deep_merge_maps_with_stop_paths(&mut into_map2, &from_map, &HashSet::new());

        // Convert to Values for comparison
        let into_value1 = Value::Object(into_map1);
        let into_value2 = Value::Object(into_map2);

        // First should completely overwrite a.b.c
        assert_eq!(
            into_value1,
            json!({
                "a": {
                    "b": {
                        "d": 2
                    }
                }
            })
        );

        // Second should merge a.b.d
        assert_eq!(
            into_value2,
            json!({
                "a": {
                    "b": {
                        "c": 1,
                        "d": 2
                    }
                }
            })
        );
    }

    // Test case 7: Complex nested structure
    {
        let mut into_map = Map::new();
        into_map.insert(
            "a".to_string(),
            json!({
                "b": {
                    "c": {
                        "d": 1,
                        "e": 2
                    },
                    "f": 3
                },
                "g": 4
            }),
        );

        let mut from_map = Map::new();
        from_map.insert(
            "a".to_string(),
            json!({
                "b": {
                    "c": {
                        "d": 10,
                        "h": 20
                    },
                    "f": 30
                },
                "g": 40,
                "i": 50
            }),
        );

        // Create stop paths at different levels
        let mut stop_paths = HashSet::new();
        stop_paths.insert(vec!["a", "b", "c"]); // Stop at a.b.c

        deep_merge_maps_with_stop_paths(&mut into_map, &from_map, &stop_paths);

        // Convert to Value for comparison
        let into_value = Value::Object(into_map);

        // a.b.c should be completely overwritten.
        assert_eq!(
            into_value,
            json!({
                "a": {
                    "b": {
                        "c": {
                            "d": 10,
                            "h": 20
                        },
                        "f": 30
                    },
                    "g": 40,
                    "i": 50
                }
            })
        );
    }
}

#[test]
fn test_deepest_object_value() {
    use crate::json::deepest_object_value;
    use serde_json::Map;

    // Empty object
    let empty_obj = Map::new();
    assert_eq!(deepest_object_value(&empty_obj), None);

    // Flat object
    let mut flat_obj = Map::new();
    flat_obj.insert("a".to_string(), json!(1));
    flat_obj.insert("b".to_string(), json!("hello"));
    let (path, value) = deepest_object_value(&flat_obj).unwrap();
    assert!(path == vec!["a"] || path == vec!["b"]);
    assert!(value == &json!(1) || value == &json!("hello"));

    // Nested object
    let nested_json = json!({
        "a": 1,
        "b": {
            "c": 2,
            "d": {
                "e": 3,
                "f": {
                    "g": 4
                }
            }
        },
        "h": {
            "i": 5
        }
    });

    let nested_obj = nested_json.as_object().unwrap();
    let (path, value) = deepest_object_value(nested_obj).unwrap();
    assert_eq!(path, vec!["b", "d", "f", "g"]);
    assert_eq!(value, &json!(4));

    // Object with multiple paths of same depth
    let multi_depth_json = json!({
        "a": {
            "b": {
                "c": 1
            }
        },
        "x": {
            "y": {
                "z": 2
            }
        }
    });

    let multi_depth_obj = multi_depth_json.as_object().unwrap();
    let (path, value) = deepest_object_value(multi_depth_obj).unwrap();
    // Either path could be returned since they're the same depth
    assert!(
        (path == vec!["a", "b", "c"] && value == &json!(1))
            || (path == vec!["x", "y", "z"] && value == &json!(2))
    );

    // Object with array-valued leaves
    let array_leaves_json = json!({
        "a": {
            "b": {
                "c": [1, 2, 3]
            }
        },
        "d": {
            "e": [4, 5]
        },
        "f": {
            "g": {
                "h": {
                    "i": [6, 7, 8, 9]
                }
            }
        }
    });

    let array_leaves_obj = array_leaves_json.as_object().unwrap();
    let (path, value) = deepest_object_value(array_leaves_obj).unwrap();
    assert_eq!(path, vec!["f", "g", "h", "i"]);
    assert_eq!(value, &json!([6, 7, 8, 9]));
}
