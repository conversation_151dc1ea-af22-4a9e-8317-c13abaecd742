use itertools::Itertools;
use serde::Serialize;
use std::{
    borrow::Cow,
    collections::HashMap,
    future::Future,
    sync::{atomic::AtomicU64, Arc, Mutex},
    time::{Duration, Instant},
};
use tracing::Instrument;

use crate::ptree::{MakePTree, TreeBuilder};

#[derive(Debug)]
pub struct TracedNode {
    pub name: Cow<'static, str>,
    pub start: Instant,
    pub duration_nanos: AtomicU64,
    pub children: Mutex<Vec<Arc<TracedNode>>>,
    pub counters: Mutex<HashMap<String, u64>>,
}

impl TracedNode {
    pub fn new(name: impl Into<Cow<'static, str>>) -> Arc<Self> {
        Arc::new(Self {
            name: name.into(),
            start: Instant::now(),
            duration_nanos: AtomicU64::new(0),
            children: Mutex::new(vec![]),
            counters: Mutex::new(HashMap::new()),
        })
    }

    pub fn new_child(&self, name: impl Into<Cow<'static, str>>) -> Arc<TracedNode> {
        let child = Self::new(name);
        if let Ok(mut children) = self.children.lock() {
            // Tracing can still occur as the program exits or crashes, so we don't want to
            // spam or panic if we can't acquire the lock.
            children.push(child.clone());
        }
        child
    }

    pub fn end(&self) {
        let end = Instant::now();
        self.duration_nanos.fetch_add(
            end.duration_since(self.start).as_nanos() as u64,
            std::sync::atomic::Ordering::Relaxed,
        );
    }

    pub fn into_tree(self: &Arc<Self>) -> TracedTree {
        let merged = merge_traced_nodes(&[self.clone()]);
        compute_traced_stats(merged)
    }
}

pub trait EnterTraceGuard: Sized {
    fn enter(&self) -> TracedGuard;
    fn increment_counter(&self, name: impl Into<Cow<'static, str>>, value: u64);
}

impl EnterTraceGuard for TracedNode {
    fn enter(&self) -> TracedGuard {
        TracedGuard { tracer: Some(self) }
    }

    fn increment_counter(&self, name: impl Into<Cow<'static, str>>, value: u64) {
        let name: Cow<'static, str> = name.into();
        let mut counters = self.counters.lock().unwrap();
        *counters.entry(name.to_string()).or_insert(0) += value;
    }
}

impl EnterTraceGuard for Option<Arc<TracedNode>> {
    fn enter(&self) -> TracedGuard {
        TracedGuard {
            tracer: self.as_ref().map(|tracer| tracer.as_ref()),
        }
    }

    fn increment_counter(&self, name: impl Into<Cow<'static, str>>, value: u64) {
        if let Some(tracer) = self.as_ref() {
            tracer.increment_counter(name, value);
        }
    }
}

#[derive(Debug)]
pub struct TracedGuard<'a> {
    tracer: Option<&'a TracedNode>,
}

impl Drop for TracedGuard<'_> {
    fn drop(&mut self) {
        if let Some(tracer) = self.tracer {
            tracer.end();
        }
    }
}

macro_rules! create_span_for_level {
    ($level:expr, $name:expr) => {
        match $level {
            log::Level::Error => tracing::error_span!("op", otel.name = $name.as_ref()),
            log::Level::Warn => tracing::warn_span!("op", otel.name = $name.as_ref()),
            log::Level::Info => tracing::info_span!("op", otel.name = $name.as_ref()),
            log::Level::Debug => tracing::debug_span!("op", otel.name = $name.as_ref()),
            log::Level::Trace => tracing::trace_span!("op", otel.name = $name.as_ref()),
        }
    };
}

static mut OVERRIDE_ALWAYS_TRACE: bool = false;
static INIT: std::sync::Once = std::sync::Once::new();

pub fn set_override_always_trace(value: bool) {
    INIT.call_once(|| {
        // SAFETY: This is safe because we're ensuring single-threaded access via Once
        unsafe { OVERRIDE_ALWAYS_TRACE = value };
    });
}

pub fn trace_if<R>(
    level: log::Level,
    tracer: &Option<Arc<TracedNode>>,
    name: impl Into<Cow<'static, str>>,
    f: impl FnOnce(Option<Arc<TracedNode>>) -> R,
) -> R {
    if log::log_enabled!(level) || unsafe { OVERRIDE_ALWAYS_TRACE } {
        let name: Cow<'static, str> = name.into();
        let span = create_span_for_level!(level, name);
        let _span_guard = span.enter();

        let child = tracer.as_ref().map(|tracer| tracer.new_child(name));
        let _guard = child.as_ref().map(|child| child.enter());
        let result = f(child.clone());
        result
    } else {
        f(None)
    }
}

pub async fn trace_if_async<R, F>(
    level: log::Level,
    tracer: &Option<Arc<TracedNode>>,
    name: impl Into<Cow<'static, str>>,
    f: impl FnOnce(Option<Arc<TracedNode>>) -> F,
) -> R
where
    F: Future<Output = R>,
{
    if log::log_enabled!(level) || unsafe { OVERRIDE_ALWAYS_TRACE } {
        let name: Cow<'static, str> = name.into();
        let span = create_span_for_level!(level, name);

        let child = tracer.as_ref().map(|tracer| tracer.new_child(name));
        let _guard = child.as_ref().map(|child| child.enter());

        let result = f(child.clone()).instrument(span).await;
        result
    } else {
        f(None).await
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct MergedTree {
    pub name: Cow<'static, str>,
    pub executions: usize,
    pub durations: Vec<u64>,
    pub counters: HashMap<String, Vec<u64>>,
    pub children: Vec<MergedTree>,
}

#[derive(Debug, Clone, Serialize)]
pub struct TracedTree {
    pub name: String,
    pub stats: TracedStats,
    pub children: Vec<TracedTree>,
}

#[derive(Debug, Clone, Serialize)]
pub struct TracedStats {
    pub executions: usize,
    pub total_duration: Duration,
    pub average_duration: Duration,
    pub duration_percentiles: Option<DurationPercentiles>,
    pub counter_stats: HashMap<String, CounterStats>,
}

#[derive(Debug, Clone, Serialize)]
pub struct DurationPercentiles {
    pub p0: Duration,
    pub p25: Duration,
    pub p50: Duration,
    pub p75: Duration,
    pub p90: Duration,
    pub p95: Duration,
    pub p99: Duration,
}

#[derive(Debug, Clone, Serialize)]
pub struct CounterStats {
    pub total: u64,
    pub percentiles: Option<CounterPercentiles>,
}

#[derive(Debug, Clone, Serialize)]
pub struct CounterPercentiles {
    pub p0: u64,
    pub p25: u64,
    pub p50: u64,
    pub p75: u64,
    pub p90: u64,
    pub p95: u64,
    pub p99: u64,
}

fn merge_traced_nodes(group: &[Arc<TracedNode>]) -> MergedTree {
    let mut child_group_order = HashMap::new();
    let mut child_groups: HashMap<String, Vec<Arc<TracedNode>>> = HashMap::new();

    // Group children by name and preserve order
    for node in group {
        let children = node.children.lock().unwrap();
        for c in children.iter() {
            child_group_order
                .entry(c.name.to_string())
                .or_insert(child_groups.len());
            child_groups
                .entry(c.name.to_string())
                .or_default()
                .push(c.clone());
        }
    }

    let mut child_group_order_vec = child_group_order.into_iter().collect_vec();
    child_group_order_vec.sort_by_key(|(_, order)| *order);

    let merged_children: Vec<MergedTree> = child_group_order_vec
        .into_iter()
        .map(|(name, _)| merge_traced_nodes(&child_groups[&name]))
        .collect();

    let mut merged_counters: HashMap<String, Vec<u64>> = HashMap::new();
    for node in group {
        if let Ok(counters) = node.counters.lock() {
            for (counter_name, &value) in counters.iter() {
                merged_counters
                    .entry(counter_name.clone())
                    .or_default()
                    .push(value);
            }
        }
    }

    MergedTree {
        name: group[0].name.clone(),
        executions: group.len(),
        durations: group
            .iter()
            .map(|n| n.duration_nanos.load(std::sync::atomic::Ordering::Relaxed))
            .collect(),
        counters: merged_counters,
        children: merged_children,
    }
}

fn compute_traced_stats(mut merged: MergedTree) -> TracedTree {
    let executions = merged.executions;
    let total_duration = Duration::from_nanos(merged.durations.iter().sum::<u64>());
    let average_duration =
        Duration::from_nanos((total_duration.as_nanos() as f64 / executions as f64) as u64);

    let duration_percentiles = if executions > 1 {
        Some(DurationPercentiles {
            p50: Duration::from_nanos(*merged.durations.select_nth_unstable(executions / 2).1),
            p0: Duration::from_nanos(*merged.durations.select_nth_unstable(0).1),
            p25: Duration::from_nanos(*merged.durations.select_nth_unstable(executions / 4).1),
            p75: Duration::from_nanos(*merged.durations.select_nth_unstable(executions * 3 / 4).1),
            p90: Duration::from_nanos(*merged.durations.select_nth_unstable(executions * 9 / 10).1),
            p95: Duration::from_nanos(
                *merged
                    .durations
                    .select_nth_unstable(executions * 95 / 100)
                    .1,
            ),
            p99: Duration::from_nanos(
                *merged
                    .durations
                    .select_nth_unstable(executions * 99 / 100)
                    .1,
            ),
        })
    } else {
        None
    };

    let counter_stats = merged
        .counters
        .into_iter()
        .map(|(name, mut values)| {
            let total = values.iter().sum();
            let percentiles = if values.len() > 1 {
                let executions = values.len();
                Some(CounterPercentiles {
                    p50: *values.select_nth_unstable(executions / 2).1,
                    p0: *values.select_nth_unstable(0).1,
                    p25: *values.select_nth_unstable(executions / 4).1,
                    p75: *values.select_nth_unstable(executions * 3 / 4).1,
                    p90: *values.select_nth_unstable(executions * 9 / 10).1,
                    p95: *values.select_nth_unstable(executions * 95 / 100).1,
                    p99: *values.select_nth_unstable(executions * 99 / 100).1,
                })
            } else {
                None
            };
            (name, CounterStats { total, percentiles })
        })
        .collect();

    TracedTree {
        name: merged.name.to_string(),
        stats: TracedStats {
            executions,
            total_duration,
            average_duration,
            duration_percentiles,
            counter_stats,
        },
        children: merged
            .children
            .into_iter()
            .map(|c| compute_traced_stats(c))
            .collect(),
    }
}

impl MakePTree for TracedTree {
    fn label(&self) -> String {
        let counter_summary = self
            .stats
            .counter_stats
            .iter()
            .sorted_by(|a, b| a.0.cmp(b.0))
            .map(|(name, stats)| format!("{}: {}", name, stats.total))
            .collect::<Vec<_>>()
            .join(", ");

        let duration_str = if self.stats.executions == 1 {
            format!("{:?}", self.stats.total_duration)
        } else {
            format!(
                "{:?} / {}x, avg: {:?}",
                self.stats.total_duration, self.stats.executions, self.stats.average_duration
            )
        };

        if counter_summary.is_empty() {
            format!("{:?} ({})", self.name, duration_str)
        } else {
            format!("{:?} ({}, {})", self.name, duration_str, counter_summary)
        }
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        // Add duration percentiles if available
        if let Some(p) = &self.stats.duration_percentiles {
            builder.add_empty_child(format!(
                "[Stats] Duration: p0: {:?}, p25: {:?}, p50: {:?}, p75: {:?}, p90: {:?}, p95: {:?}, p99: {:?}",
                p.p0, p.p25, p.p50, p.p75, p.p90, p.p95, p.p99
            ));
        }

        // Add counter percentiles if available
        for (counter_name, counter_stats) in &self.stats.counter_stats {
            if let Some(p) = &counter_stats.percentiles {
                builder.add_empty_child(format!(
                    "[Stats] {}: p0: {}, p25: {}, p50: {}, p75: {}, p90: {}, p95: {}, p99: {}",
                    counter_name, p.p0, p.p25, p.p50, p.p75, p.p90, p.p95, p.p99
                ));
            }
        }

        // Recursively build children
        for child in &self.children {
            self.add_child(builder, child);
        }
    }
}

#[cfg(test)]
mod tests {
    use ptree::{write_tree_with, PrintConfig};

    use super::*;

    #[test]
    fn test_ptree_with_duplicate_children_and_counters() {
        let root = TracedNode::new("root");

        // Create 3 children with the same name but different counters
        let child1 = root.new_child("repeated_child");
        child1.increment_counter("counter_a", 1);
        child1.increment_counter("counter_b", 10);

        let child2 = root.new_child("repeated_child");
        child2.increment_counter("counter_a", 2);
        child2.increment_counter("counter_c", 20);

        let child3 = root.new_child("repeated_child");
        child3.increment_counter("counter_b", 30);
        child3.increment_counter("counter_c", 40);

        // Add some artificial duration
        child1.end();
        child2.end();
        child3.end();
        root.end();

        // Create a tree builder and generate the ptree
        let mut builder = TreeBuilder::new("root".to_string());
        root.into_tree().make_ptree(&mut builder);

        let tree = builder.build();
        let mut string_writer = Vec::new();
        write_tree_with(
            &tree,
            &mut string_writer,
            &PrintConfig {
                indent: 3,
                ..Default::default()
            },
        )
        .unwrap();
        let tree_str = String::from_utf8(string_writer.to_vec()).unwrap();
        println!("{}", tree_str);

        // If we got here without panicking, the test passed
        // The actual output would be visible when running the test with --nocapture
    }

    #[test]
    fn test_two_level_recursion() {
        let root = TracedNode::new("root");

        // Create first level children with same name
        let child1 = root.new_child("level1");
        let child2 = root.new_child("level1");

        // Create second level children with same name under different parents
        let grandchild1 = child1.new_child("level2");
        let grandchild2 = child1.new_child("level2");
        let grandchild3 = child2.new_child("level2");

        // root
        // ├── level1
        // │   ├── level2
        // │   │   ├── count: 1
        // │   │   ├── count: 2
        // │   ├── level2
        // │   │   ├── count: 3
        // First, we

        // Add some counters to differentiate the nodes
        grandchild1.increment_counter("count", 1);
        grandchild2.increment_counter("count", 2);
        grandchild3.increment_counter("count", 3);

        // End all spans to record durations
        grandchild1.end();
        grandchild2.end();
        grandchild3.end();
        child1.end();
        child2.end();
        root.end();

        // Convert to tree - this should merge the nodes with same names
        let tree = root.into_tree();

        // Verify structure
        assert_eq!(tree.name, "root");
        assert_eq!(tree.children.len(), 1); // All "level1" nodes should be merged
        assert_eq!(tree.children[0].name, "level1");
        assert_eq!(tree.children[0].children.len(), 1); // All "level2" nodes should be merged
        assert_eq!(tree.children[0].children[0].name, "level2");

        // Verify the merged stats
        assert_eq!(tree.children[0].stats.executions, 2); // Two level1 nodes
        assert_eq!(tree.children[0].children[0].stats.executions, 3); // Three level2 nodes

        // Verify counter totals
        let level2_counter_stats = &tree.children[0].children[0].stats.counter_stats;
        assert_eq!(level2_counter_stats["count"].total, 6); // 1 + 2 + 3
    }
}
