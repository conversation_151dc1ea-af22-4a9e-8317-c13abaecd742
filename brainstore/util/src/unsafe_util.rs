/// Sometimes we need to transmute a type that contains internal references to a static-lifetime
/// version for temporary use (e.g. passing to a spawn_blocking closure that is immediately
/// awaited). Rust makes this difficult to do over generic types, so we provide this trait to do
/// it.
pub trait UnsafeMakeStatic {
    type Target: 'static;

    /// Transmutes the type to a version with static lifetimes.
    ///
    /// # Safety
    /// Only use this when you are certain the object will be dropped within the original object's
    /// lifetime.
    unsafe fn unsafe_make_static(&self) -> &'static Self::Target;
}
