use std::collections::HashMap;

use anyhow::{Context, Result};
pub use url::Url;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum ObjectStoreType {
    Local,
    S3,
    GCS,
    Azure,
    Other,
}

pub fn str_to_url(url: &str, base_path: Option<&str>) -> Result<Url> {
    if url.contains("://") {
        Ok(url.parse::<Url>()?)
    } else {
        let base_path = match base_path {
            Some(path) => path.to_owned(),
            None => {
                let current_dir = std::env::current_dir()?;
                current_dir.to_str().unwrap().to_string()
            }
        };
        let base_dir = std::path::Path::new(&base_path);
        let full_path = std::path::absolute(base_dir.join(url))?;
        format!("file://{}", full_path.as_os_str().to_str().unwrap())
            .parse::<Url>()
            .context("Failed to parse URL")
    }
}

pub fn url_join(base: &str, path: &str) -> String {
    let mut url = base.to_string();
    if !url.ends_with('/') {
        url.push('/');
    }
    if path.starts_with('/') {
        url.push_str(&path[1..]);
    } else {
        url.push_str(path);
    }
    url
}

// Headers are comma separated key=value pairs
pub fn parse_headers_arg(header_str: &str) -> Result<HashMap<String, String>> {
    let mut result = HashMap::new();
    for pair in header_str.split(',') {
        if pair.is_empty() {
            continue;
        }
        let (key, value) = pair
            .trim()
            .split_once('=')
            .ok_or_else(|| anyhow::anyhow!("Invalid header format (no =)"))?;
        result.insert(key.trim().to_string(), value.trim().to_string());
    }
    Ok(result)
}
