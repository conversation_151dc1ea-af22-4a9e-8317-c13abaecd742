use std::sync::Arc;

use util::uuid::Uuid;

use crate::{
    global_store::{GlobalStore, LastIndexOperation, LastIndexOperationDetails, MemoryGlobalStore},
    status_updater::{StatusUpdate, StatusUpdater},
};

#[tokio::test]
async fn test_basic() {
    let store = Arc::new(MemoryGlobalStore::default());

    // Create an operation.
    let seg = Uuid::new_v4();
    let seg1 = Uuid::new_v4();
    let seg2 = Uuid::new_v4();
    let op_token = Uuid::new_v4();
    let operation = LastIndexOperation {
        finished: Some(true),
        estimated_progress: Some(1.0),
        stage: Some("completed".to_string()),
        error: None,
        details: Some(LastIndexOperationDetails::Merge {
            merges: vec![vec![seg1.to_string(), seg2.to_string()]],
        }),
    };
    store
        .upsert_last_index_operation(seg, operation.clone(), op_token, Default::default())
        .await
        .unwrap();

    // Verify the operation was stored
    let result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation, operation);
    assert!(result.start <= result.last_updated);

    let last_started = result.start;

    let status_updater = StatusUpdater::new(store.clone(), seg);
    let _guard = status_updater.ensure_next_update();
    // Make sure we didn't update anything yet
    tokio::time::sleep(std::time::Duration::from_millis(50)).await;
    {
        let same_result = store
            .query_last_index_operations(&[seg])
            .await
            .unwrap()
            .remove(0)
            .unwrap();
        assert_eq!(result, same_result);
    }
    {
        let same_result = store
            .query_last_index_operations(&[seg])
            .await
            .unwrap()
            .remove(0)
            .unwrap();
        assert_eq!(result, same_result);
    }

    // Initial operation should set start time
    let initial_op = LastIndexOperation {
        stage: Some("initial".to_string()),
        ..Default::default()
    };
    let counter = status_updater.update(initial_op.clone());
    status_updater.wait_until(counter).await;

    // Get the result and verify start time was set
    let initial_result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(initial_result.operation.stage, Some("initial".to_string()));
    let start_time = initial_result.start;

    assert!(start_time >= last_started);

    // Update with new operation
    let updated_op = LastIndexOperation {
        stage: Some("updated".to_string()),
        details: Some(LastIndexOperationDetails::Compact {
            num_wal_entries: 100,
        }),
        ..Default::default()
    };
    status_updater.update(updated_op);
    status_updater.finish().await.unwrap();

    // Verify operation. start time shouldn't change but last_updated time should.
    let updated_result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(
        updated_result.operation,
        LastIndexOperation {
            finished: Some(true),
            estimated_progress: Some(1.0),
            stage: Some("finished".to_string()),
            error: None,
            details: Some(LastIndexOperationDetails::Compact {
                num_wal_entries: 100,
            }),
        }
    );
    assert_eq!(updated_result.start, start_time);
    assert_eq!(updated_result.current_op_token, None);
    assert!(updated_result.last_updated >= initial_result.last_updated);
}

#[tokio::test]
async fn test_final_update() {
    let store = Arc::new(MemoryGlobalStore::default());
    let seg = Uuid::new_v4();

    for i in 0..10 {
        {
            let status_updater = StatusUpdater::new(store.clone(), seg);
            let _guard = status_updater.ensure_next_update();

            for _ in 0..i {
                status_updater.update(LastIndexOperation {
                    stage: Some("initial".to_string()),
                    ..Default::default()
                });
                tokio::time::sleep(std::time::Duration::from_millis(10)).await; // Introduce a little jitter so that the background thread runs
            }

            status_updater.update(LastIndexOperation {
                stage: Some("final".to_string()),
                error: Some("foobar".to_string()),
                ..Default::default()
            });

            status_updater.finish().await.unwrap();
        }

        let result = store
            .query_last_index_operations(&[seg])
            .await
            .unwrap()
            .remove(0)
            .unwrap();
        assert_eq!(result.operation.stage, Some("finished".to_string()));
        assert_eq!(result.operation.error, Some("foobar".to_string()));
    }
}

#[tokio::test]
async fn test_clear_ensure_next_update() {
    let store = Arc::new(MemoryGlobalStore::default());

    // Create an operation.
    let seg = Uuid::new_v4();
    let status_updater = StatusUpdater::new(store.clone(), seg);
    let counter = status_updater.update(LastIndexOperation {
        stage: Some("initial".to_string()),
        ..Default::default()
    });
    status_updater.wait_until(counter).await;

    // Create a new status updater on the same segment. A standard update should fail.
    let status_updater2 = StatusUpdater::new(store.clone(), seg);
    let counter2 = status_updater2.update(LastIndexOperation {
        stage: Some("initial2".to_string()),
        ..Default::default()
    });
    status_updater2.wait_until(counter2).await;

    // Get the result and verify start time was set
    let result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation.stage, Some("initial".to_string()));

    // If we ensure the next update but drop the guard before running update, it should still not
    // do anything.
    {
        let _guard = status_updater2.ensure_next_update();
        drop(_guard);
        let counter2 = status_updater2.update(LastIndexOperation {
            stage: Some("initial2".to_string()),
            ..Default::default()
        });
        status_updater2.wait_until(counter2).await;
    }
    let result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation.stage, Some("initial".to_string()));

    // But if we don't drop the guard, it will update.
    {
        let _guard = status_updater2.ensure_next_update();
        let counter2 = status_updater2.update(LastIndexOperation {
            stage: Some("initial2".to_string()),
            ..Default::default()
        });
        status_updater2.wait_until(counter2).await;
    }
    let result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation.stage, Some("initial2".to_string()));

    // Now that we've overridden with a new operation, the first status updater should no longer do
    // anything.
    status_updater.finish().await.unwrap();
    let result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation.stage, Some("initial2".to_string()));

    // But finishing the second updater should clear out the operation.
    status_updater2.finish().await.unwrap();
    let result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation.stage, Some("finished".to_string()));
}

#[tokio::test]
async fn test_heartbeat_updates() {
    let store = Arc::new(MemoryGlobalStore::default());
    let seg = Uuid::new_v4();

    let status_updater = StatusUpdater::new(store.clone(), seg);
    let _guard = status_updater.ensure_next_update();

    // Make initial update
    let initial_op = LastIndexOperation {
        stage: Some("initial".to_string()),
        ..Default::default()
    };
    let counter = status_updater.update(initial_op);
    status_updater.wait_until(counter).await;

    // Get the initial timestamp
    let initial_result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    let initial_timestamp = initial_result.last_updated;

    // Wait for longer than the heartbeat interval
    tokio::time::sleep(std::time::Duration::from_millis(500)).await;

    // Check that the operation was updated (timestamp changed) even without explicit updates
    let updated_result = store
        .query_last_index_operations(&[seg])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert!(updated_result.last_updated > initial_timestamp);

    // Clean up
    status_updater.finish().await.unwrap();
}
