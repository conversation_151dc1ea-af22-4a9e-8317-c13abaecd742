use std::{collections::BTreeMap, path::Path, sync::Arc};

use crate::{
    instrumented::{CacheEntryStats, Instrumented},
    timer::TimerManager,
};

use super::AsyncDirectoryArc;

#[derive(<PERSON><PERSON>, Debug)]
pub struct FallbackDirectory {
    pub directories: Vec<AsyncDirectoryArc>,
}

impl FallbackDirectory {
    pub fn new(directories: Vec<AsyncDirectoryArc>) -> Self {
        Self { directories }
    }
}

impl tantivy::directory::Directory for FallbackDirectory {
    fn get_file_handle(
        &self,
        path: &Path,
    ) -> Result<Arc<dyn tantivy::directory::FileHandle>, tantivy::directory::error::OpenReadError>
    {
        for directory in &self.directories {
            match directory.get_file_handle(path) {
                Ok(handle) => {
                    return Ok(handle);
                }
                Err(tantivy::directory::error::OpenReadError::FileDoesNotExist(_)) => continue,
                Err(e) => return Err(e),
            }
        }
        Err(tantivy::directory::error::OpenReadError::FileDoesNotExist(
            path.to_path_buf(),
        ))
    }

    fn delete(&self, path: &Path) -> Result<(), tantivy::directory::error::DeleteError> {
        for directory in &self.directories {
            directory.delete(path)?;
        }
        Ok(())
    }

    fn exists(&self, path: &Path) -> Result<bool, tantivy::directory::error::OpenReadError> {
        for directory in &self.directories {
            if directory.exists(path)? {
                return Ok(true);
            }
        }
        Ok(false)
    }

    fn open_write(
        &self,
        _path: &Path,
    ) -> Result<tantivy::directory::WritePtr, tantivy::directory::error::OpenWriteError> {
        panic!("FallbackDirectory does not support open_write");
    }

    fn atomic_read(
        &self,
        path: &Path,
    ) -> Result<Vec<u8>, tantivy::directory::error::OpenReadError> {
        for directory in &self.directories {
            match directory.atomic_read(path) {
                Ok(data) => return Ok(data),
                Err(tantivy::directory::error::OpenReadError::FileDoesNotExist(_)) => continue,
                Err(e) => return Err(e),
            }
        }
        Err(tantivy::directory::error::OpenReadError::FileDoesNotExist(
            path.to_path_buf(),
        ))
    }

    fn atomic_write(&self, _path: &Path, _data: &[u8]) -> std::io::Result<()> {
        panic!("FallbackDirectory does not support atomic_write");
    }

    fn sync_directory(&self) -> std::io::Result<()> {
        for directory in &self.directories {
            directory.sync_directory()?;
        }
        Ok(())
    }

    fn watch(
        &self,
        _callback: tantivy::directory::WatchCallback,
    ) -> tantivy::Result<tantivy::directory::WatchHandle> {
        Ok(tantivy::directory::WatchHandle::empty())
    }
}

impl Instrumented for FallbackDirectory {
    fn enable_timing(&self) {
        for directory in &self.directories {
            directory.enable_timing();
        }
    }

    fn reset_timing(&self) {
        for directory in &self.directories {
            directory.reset_timing();
        }
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        self.directories.iter().flat_map(|d| d.timers()).collect()
    }

    fn cache_metrics(&self) -> BTreeMap<String, CacheEntryStats> {
        self.directories
            .iter()
            .flat_map(|d| d.cache_metrics())
            .collect()
    }
}
