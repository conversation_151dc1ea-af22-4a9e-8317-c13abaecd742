use arc_swap::Arc<PERSON><PERSON><PERSON>O<PERSON>;
use async_trait::async_trait;
use async_util::spawn_blocking_util::spawn_blocking_with_async_timeout;
use clap::{ArgAction, Parser, ValueEnum};
use futures::{future::join_all, stream::BoxStream, StreamExt};
use lazy_static::lazy_static;
use otel_common::opentelemetry::metrics::{Counter, Gauge, Histogram};
use rand::Rng;
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use stable_deref_trait::StableDeref;
use std::{
    collections::{BTreeMap, HashSet, VecDeque},
    hash::BuildHasher,
    ops::Deref,
    os::unix::fs::FileExt,
    path::{Path, PathBuf},
    pin::Pin,
    sync::{atomic::AtomicUsize, Arc, Mutex, Weak},
    time::{SystemTime, UNIX_EPOCH},
};
use tantivy::{common::OwnedBytes, Has<PERSON><PERSON>};
use thiserror::Error;
use tracing::instrument;
use util::unsafe_util::UnsafeMakeStatic;

use crate::{
    directory::{
        async_directory::PinnedAsyncWriteHandler, tracing_directory::TracedRangeDirectory,
    },
    hash_map::{
        raw_entry_from_hash, raw_entry_mut_from_hash, Entry, HashMapExt, RawEntryCompatible,
        RawEntryMut,
    },
    instrumented::{CacheEntryStats, Instrumented},
    static_sync_runtime::STATIC_SYNC_RUNTIME,
    time,
    timer::TimerManager,
};

use super::async_directory::{
    AsyncDirectory, AsyncDirectoryArc, AsyncFileHandle, AsyncWriteHandler,
};

/// gxhash is a very fast, non-cryptographic hash implementation
///   https://github.com/ogxd/gxhash
/// Since we read a lot of byte ranges in tight loops, and continually re-hash
/// the filename each time, this actually makes a non-trivial difference (a little over 10%)
pub type FastHashMap<K, V> = crate::hash_map::HashMap<K, V, gxhash::GxBuildHasher>;

pub const PAGE_SIZE: usize = 128 * 1024;

lazy_static! {
    pub static ref CACHED_DIRECTORY_METERS: CachedDirectoryMeters =
        CachedDirectoryMeters::default();
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct FileCacheOpts {
    #[arg(long("object-store-cache-memory-limit"), env = "BRAINSTORE_OBJECT_STORE_CACHE_MEMORY_LIMIT", value_parser = util::ByteSize::parse_to_usize, default_value_t = default_cache_memory_limit(), help=format!("The total memory budget for the file cache (defaults to {})", util::ByteSize::from(default_cache_memory_limit())))]
    pub memory_limit: usize,
    #[arg(long("object-store-cache-file-size"), env = "BRAINSTORE_OBJECT_STORE_CACHE_FILE_SIZE", value_parser = util::ByteSize::parse_to_usize, default_value_t = default_cache_file_size(), help=format!("The maximum size of a file in the cache (defaults to {})", util::ByteSize::from(default_cache_file_size())))]
    pub file_size: usize,
    #[arg(long("object-store-cache-page-size"), env = "BRAINSTORE_OBJECT_STORE_CACHE_PAGE_SIZE", value_parser = util::ByteSize::parse_to_usize, default_value_t = util::ByteSize::parse_to_usize("128KB").unwrap(), help="The page size for the file cache (defaults to 128KB)")]
    pub page_size: usize,
    #[arg(
        long("debug-object-store-cache-flush-type"),
        env = "BRAINSTORE_OBJECT_STORE_CACHE_FLUSH_TYPE",
        default_value = "background",
        help = "The flush type for the file cache (defaults to background)"
    )]
    pub flush_type: FlushType,
    #[arg(
        long,
        env = "BRAINSTORE_CACHE_DIR",
        help = "The directory to store the file cache in (defaults to the system temp directory)"
    )]
    pub cache_dir: Option<PathBuf>,

    #[arg(
        long,
        help = "Whether to enable stats for the file cache. This slows down performance about 2x.",
        env = "BRAINSTORE_FILE_CACHE_STATS_ENABLED",
        default_value_t = false,
        action = ArgAction::SetTrue
    )]
    pub stats_enabled: bool,

    #[arg(
        long,
        help = "The batch size for the delete stream. This is used to batch deletes to the cache file.",
        env = "BRAINSTORE_FILE_CACHE_DELETE_STREAM_BATCH_SIZE",
        default_value_t = default_delete_stream_batch_size(),
    )]
    pub delete_stream_batch_size: usize,

    #[arg(
        long,
        help = "Use the mmap directory for indexing operations",
        default_value_t = default_use_mmap_directory_writes(),
        env = "BRAINSTORE_USE_MMAP_DIRECTORY_WRITES"
    )]
    #[serde(default = "default_use_mmap_directory_writes")]
    pub use_mmap_directory_writes: bool,

    #[arg(
        long,
        help = "Use the mmap directory for reads",
        default_value_t = default_use_mmap_directory_reads(),
        env = "BRAINSTORE_USE_MMAP_DIRECTORY_READS"
    )]
    #[serde(default = "default_use_mmap_directory_reads")]
    pub use_mmap_directory_reads: bool,

    #[clap(skip)]
    #[serde(skip)]
    pub testing_opts: CacheFileTestingOptions,
}

impl Default for FileCacheOpts {
    fn default() -> Self {
        Self {
            memory_limit: default_cache_memory_limit(),
            file_size: default_cache_file_size(),
            // We should measure this more rigorously, but it seemed from testing (with print statements)
            // that Tantivy rarely asks for more than 64 KB at a time.
            page_size: PAGE_SIZE,
            flush_type: FlushType::Background,
            cache_dir: None,
            stats_enabled: false,
            delete_stream_batch_size: default_delete_stream_batch_size(),
            testing_opts: CacheFileTestingOptions::default(),
            use_mmap_directory_writes: default_use_mmap_directory_writes(),
            use_mmap_directory_reads: default_use_mmap_directory_reads(),
        }
    }
}

fn default_cache_memory_limit() -> usize {
    1024 * 1024 * 1024 // 1 GB
}

fn default_cache_file_size() -> usize {
    1024 * 1024 * 1024 * 50 // 50 GB
}

fn default_delete_stream_batch_size() -> usize {
    1000
}

fn default_use_mmap_directory_writes() -> bool {
    true
}

fn default_use_mmap_directory_reads() -> bool {
    false
}

pub type FileCacheDirectory = CacheDirectory<DirectCacheFile<CacheDirectoryKey>>;

#[derive(Clone)]
pub struct CacheDirectory<F: CacheFile<CacheDirectoryKey>> {
    inner: Arc<CacheDirectoryInner<F>>,
}

impl<F: CacheFile<CacheDirectoryKey>> std::fmt::Debug for CacheDirectory<F> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.inner.fmt(f)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct CacheDirectoryKey(pub PathBuf, pub std::ops::Range<usize>);

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct CacheDirectoryKeyRef<'a>(pub &'a Path, pub std::ops::Range<usize>);

impl RawEntryCompatible<CacheDirectoryKey> for CacheDirectoryKeyRef<'_> {
    fn convert_to_key(&self) -> CacheDirectoryKey {
        CacheDirectoryKey(self.0.to_path_buf(), self.1.clone())
    }

    fn is_match(&self, other: &CacheDirectoryKey) -> bool {
        self.0 == other.0 && self.1 == other.1
    }
}

impl<'a> UnsafeMakeStatic for CacheDirectoryKeyRef<'a> {
    type Target = CacheDirectoryKeyRef<'static>;

    unsafe fn unsafe_make_static(&self) -> &'static Self::Target {
        std::mem::transmute(self)
    }
}

pub const BRAINSTORE_CACHE_DIR_PREFIX: &str = "brainstore-cache";

struct CacheDirectoryInner<F: CacheFile<CacheDirectoryKey>> {
    cold_directory: AsyncDirectoryArc,

    cache_file: F,
    file_counter: AtomicUsize,

    cold_files:
        Mutex<FastHashMap<PathBuf, Arc<tokio::sync::Mutex<Option<Arc<dyn AsyncFileHandle>>>>>>,
    opts: FileCacheOpts,

    debug_timer: Arc<TimerManager>,

    // The _temp_dir allows us to ensure cache_path exists if it's defined in a temp directory
    cache_path: std::path::PathBuf,
    _temp_dir: Option<Arc<tempfile::TempDir>>,
}

impl<F: CacheFile<CacheDirectoryKey>> std::fmt::Debug for CacheDirectoryInner<F> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("FileCacheDirectory")
            .field("cache_path", &self.cache_path)
            .finish()
    }
}

impl<F: CacheFile<CacheDirectoryKey>> CacheDirectory<F> {
    pub fn new(
        cold_directory: AsyncDirectoryArc,
        temp_dir: Option<Arc<tempfile::TempDir>>,
        opts: FileCacheOpts,
    ) -> Result<Self> {
        let temp_dir = if let Some(existing_dir) = temp_dir {
            Some(existing_dir)
        } else if let Some(cache_path) = opts.cache_dir.as_ref() {
            std::fs::create_dir_all(cache_path)?;
            Some(Arc::new(tempfile::tempdir_in(cache_path)?))
        } else {
            // Create a brainstore-cache directory in the system temp dir
            let system_temp = std::env::temp_dir();
            let cache_dir = system_temp.join(BRAINSTORE_CACHE_DIR_PREFIX);
            std::fs::create_dir_all(&cache_dir)?;
            Some(Arc::new(tempfile::tempdir_in(&cache_dir)?))
        };

        let cache_path = temp_dir.as_ref().unwrap().path().to_path_buf();

        Ok(Self {
            inner: Arc::new(CacheDirectoryInner {
                cold_directory,
                cache_path: cache_path.clone(),
                _temp_dir: temp_dir.clone(),
                cache_file: F::new(
                    &cache_path.join("initial.cache"),
                    temp_dir,
                    opts.file_size,
                    opts.memory_limit,
                    opts.stats_enabled,
                    opts.testing_opts.clone(),
                )?,
                file_counter: AtomicUsize::new(0),
                cold_files: Mutex::new(FastHashMap::new()),
                opts,
                debug_timer: TimerManager::new("FileCacheDirectory"),
            }),
        })
    }

    pub fn stats(&self) -> BTreeMap<String, CacheEntryStats> {
        self.inner.stats()
    }

    #[allow(unused)]
    pub fn clear_all(&mut self) -> Result<()> {
        let inner_mut = Arc::get_mut(&mut self.inner);
        match inner_mut {
            Some(inner) => inner.clear_all(),
            None => Err(util::anyhow::anyhow!("Cannot clear all (multiple references)").into()),
        }
    }
}

impl<F: CacheFile<CacheDirectoryKey>> Instrumented for CacheDirectory<F> {
    fn enable_timing(&self) {
        self.inner.cold_directory.enable_timing();
        self.inner.debug_timer.enable_granular_timing();
    }

    fn reset_timing(&self) {
        self.inner.debug_timer.reset();
        self.inner.cold_directory.reset_timing();
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        vec![self.inner.debug_timer.clone()]
            .into_iter()
            .chain(self.inner.cold_directory.timers())
            .collect()
    }

    fn cache_metrics(&self) -> BTreeMap<String, CacheEntryStats> {
        self.stats()
    }

    fn reset_caches(&self) {
        self.inner.cache_file.clear_all();
    }
}

impl<F: CacheFile<CacheDirectoryKey>> CacheDirectoryInner<F> {
    pub fn stats(&self) -> BTreeMap<String, CacheEntryStats> {
        self.cache_file
            .stats()
            .into_iter()
            .map(|(p, f)| (format!("{:?}", p), f))
            .collect::<BTreeMap<_, _>>()
    }

    pub fn clear_all(&mut self) -> Result<()> {
        self.cache_file.clear_all();
        self.cache_file = self.make_next_cache_file()?;
        Ok(())
    }

    fn make_next_cache_file(&self) -> Result<F> {
        // We need to increment a unique counter here, because there's a subtle race condition
        // where we might create a cache file, let it do some writes, etc. (which happen in the
        // background), clear it, and then create it again.
        let file_counter = self
            .file_counter
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed);

        let cache_file_name = self.cache_path.join(create_normalized_filename(
            Path::new("file.cache"),
            file_counter,
        ));

        Ok(F::new(
            &cache_file_name,
            self._temp_dir.clone(),
            self.opts.file_size,
            self.opts.memory_limit,
            self.opts.stats_enabled,
            self.opts.testing_opts.clone(),
        )?)
    }
}

// NOTE: We skip instrumenting the errors here, because some of the errors like
// FileDoesNotExist are handled as expected and we don't want to spam logs.
#[async_trait]
impl<F: CacheFile<CacheDirectoryKey>> AsyncDirectory for CacheDirectory<F> {
    #[instrument(skip(self), level = "debug")]
    async fn async_get_file_handle(
        &self,
        path: &std::path::Path,
        len: Option<u64>,
    ) -> Result<Arc<dyn AsyncFileHandle>, tantivy::directory::error::OpenReadError> {
        time!(self.inner.debug_timer, "get_file_handle");

        let cold_reader_mutex = {
            let mut cold_files = self.inner.cold_files.lock().unwrap();
            if !cold_files.contains_key(path) {
                cold_files.insert(path.to_path_buf(), Arc::new(tokio::sync::Mutex::new(None)));
            }
            cold_files.get(path).unwrap().clone()
        };
        let mut opt_cold_reader = cold_reader_mutex.lock().await;

        let cold_reader = match opt_cold_reader.deref() {
            Some(existing) => existing.clone(),
            None => {
                // Important that we dropped the outer lock on cold_files
                // because this will block on I/O.
                let reader = self
                    .inner
                    .cold_directory
                    .async_get_file_handle(path, len)
                    .await?;

                *opt_cold_reader = Some(reader.clone());
                reader
            }
        };

        Ok(Arc::new(HotCacheFileHandle {
            file_name: path.to_path_buf(),
            cold_reader,
            cache_file: self.inner.cache_file.clone(),
            page_size: self.inner.opts.page_size,
            timer: self.inner.debug_timer.clone(),
            flush_type: self.inner.opts.flush_type,
            last_page: Arc::new(ArcSwapOption::new(None)),
        }))
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_delete(
        &self,
        path: &std::path::Path,
    ) -> Result<(), tantivy::directory::error::DeleteError> {
        time!(self.inner.debug_timer, "delete");

        self.inner.cold_files.lock().unwrap().remove(path);
        self.inner.cold_directory.async_delete(path).await
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_exists(
        &self,
        path: &std::path::Path,
    ) -> Result<bool, tantivy::directory::error::OpenReadError> {
        time!(self.inner.debug_timer, "exists");
        Ok(self.inner.cold_files.lock().unwrap().contains_key(path)
            || self.inner.cold_directory.async_exists(path).await?)
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_open_write(
        &self,
        path: &std::path::Path,
    ) -> Result<Pin<Box<dyn AsyncWriteHandler>>, tantivy::directory::error::OpenWriteError> {
        time!(self.inner.debug_timer, "open_write");
        log::debug!("opening write for {:?}", path);

        self.inner.cold_directory.async_open_write(path).await
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_atomic_read(
        &self,
        path: &std::path::Path,
    ) -> Result<Vec<u8>, tantivy::directory::error::OpenReadError> {
        time!(self.inner.debug_timer, "atomic_read");
        self.inner.cold_directory.async_atomic_read(path).await
    }

    #[instrument(skip(self, data), fields(len = data.len()), level = "debug")]
    async fn async_atomic_write(&self, path: &std::path::Path, data: &[u8]) -> std::io::Result<()> {
        time!(self.inner.debug_timer, "atomic_write");

        self.inner
            .cold_directory
            .async_atomic_write(path, data)
            .await?;

        let mut offset = 0;
        while offset < data.len() {
            let chunk_size = std::cmp::min(self.inner.opts.page_size, data.len() - offset);
            let key = CacheDirectoryKey(path.to_path_buf(), offset..offset + chunk_size);
            if let Err(e) = self
                .inner
                .cache_file
                .write_page_to_cache(
                    key,
                    Arc::new(OwnedBytes::new(data[offset..offset + chunk_size].to_vec())),
                    self.inner.opts.flush_type,
                    self.inner.debug_timer.clone(),
                )
                .await
            {
                log::warn!("Error writing page to cache: {:?}", e);
            }
            offset += chunk_size;
        }
        Ok(())
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_sync_directory(&self) -> std::io::Result<()> {
        time!(self.inner.debug_timer, "sync_directory");
        self.inner.cold_directory.async_sync_directory().await
    }

    #[instrument(skip(self, locations), level = "debug")]
    fn delete_stream<'a>(
        &'a self,
        locations: BoxStream<'a, Result<PathBuf, object_store::Error>>,
    ) -> BoxStream<'a, Result<object_store::path::Path, object_store::Error>> {
        time!(self.inner.debug_timer, "delete_stream");

        let locations = locations
            .chunks(self.inner.opts.delete_stream_batch_size)
            .flat_map(|paths| {
                let mut files = self.inner.cold_files.lock().unwrap();
                for path in paths.iter().filter_map(|result| result.as_ref().ok()) {
                    files.remove(path);
                }
                futures::stream::iter(paths)
            })
            .boxed();

        self.inner.cold_directory.delete_stream(locations)
    }

    #[instrument(skip(self), level = "debug")]
    async fn load_redirects(&self, path: &Path) -> util::anyhow::Result<bool> {
        let redirect_page_key = CacheDirectoryKey(path.to_path_buf(), 0..DUMMY_REDIRECT_PAGE.len());

        // See "create a little page" below.
        match self.inner.cache_file.get_cached_entry(&redirect_page_key) {
            BufferOrCacheEntry::CacheEntry(_) | BufferOrCacheEntry::Buffer(_) => {
                return Ok(true);
            }
            BufferOrCacheEntry::None => {}
        };

        self.inner
            .cache_file
            .incr_stat(&redirect_page_key, StatType::ColdRead);
        let contents = match self.async_atomic_read(path).await {
            Ok(contents) => contents,
            Err(tantivy::directory::error::OpenReadError::FileDoesNotExist(_)) => {
                log::warn!("No redirects found for {:?}", path);
                return Ok(false);
            }
            Err(e) => {
                return Err(util::anyhow::anyhow!(e));
            }
        };

        // Create a little page that we can use to remember that we've loaded the redirects. The idea is that this
        // page is in front of the actual redirects in the ring buffer, so if it's still in the cache, it's highly likely
        // that they are too.
        //
        // This logic resembles the logic in `HotCacheFileHandle::cold_read()`, in that it takes a lock on the metadata to prevent
        // two concurrent cold reads on this file.
        let cold_read_lock = {
            let mut meta = self.inner.cache_file.meta().write().unwrap();
            let entry = raw_entry_mut_from_hash(&mut meta.active_cold_reads, &redirect_page_key);

            match entry {
                RawEntryMut::Occupied(entry) => entry.get().clone(),
                RawEntryMut::Vacant(entry) => {
                    let lock = Arc::new(tokio::sync::Mutex::new(()));
                    entry.insert(redirect_page_key.convert_to_key(), lock.clone());
                    lock
                }
            }
        };
        let _guard = ColdReadLockGuard {
            key: &redirect_page_key,
            inner: self.inner.cache_file.clone(),
            _guard: cold_read_lock.lock().await,
            _phantom: std::marker::PhantomData,
        };
        {
            // Once we acquire the lock, check the metadata once more, in case it was written concurrently
            // by another lock holder. Use the cache file interface directly, so we don't mess with the statistics.
            let meta = self
                .inner
                .cache_file
                .meta()
                .read()
                .expect("Failed to acquire metadata lock");

            if let Some(_) = meta.get_cache_entry(&redirect_page_key) {
                return Ok(true);
            }
        }

        // Unlike the cold read path, we need to do this before we write the other pages to the cache, so that we can ensure
        // that the redirect page precedes them.
        self.inner
            .cache_file
            .write_page_to_cache(
                redirect_page_key.clone(),
                DUMMY_REDIRECT_PAGE.clone(),
                self.inner.opts.flush_type,
                self.inner.debug_timer.clone(),
            )
            .await?;

        let redirects = match TracedRangeDirectory::load_traced_redirects(contents) {
            Ok(redirects) => redirects,
            Err(e) => {
                log::warn!("Invalid redirects file (will skip) {:?}: {:?}", path, e);
                return Ok(false);
            }
        };

        let mut futures = Vec::new();
        for (path, ranges) in redirects.into_iter() {
            // Although we could do this in a future, there's no point because there's no I/O
            // involved (since we provide the length).
            self.async_get_file_handle(&path, Some(ranges.file_len))
                .await
                .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;

            for (range, data) in ranges.ranges.into_iter() {
                let path = path.to_path_buf();
                let range = range.clone();
                futures.push(async move {
                    self.inner
                        .cache_file
                        .write_page_to_cache(
                            CacheDirectoryKey(path, range),
                            Arc::new(OwnedBytes::new(data)),
                            self.inner.opts.flush_type,
                            self.inner.debug_timer.clone(),
                        )
                        .await?;
                    Ok::<(), CacheError>(())
                });
            }
        }

        join_all(futures)
            .await
            .into_iter()
            .collect::<Result<(), _>>()?;

        Ok(true)
    }
}

lazy_static! {
    pub static ref DUMMY_REDIRECT_PAGE: Arc<OwnedBytes> = Arc::new(OwnedBytes::new(vec![0; 8]));
}

#[derive(Debug, Copy, Clone, ValueEnum, Serialize, Deserialize)]
pub enum FlushType {
    #[serde(rename = "sync")]
    Sync,
    #[serde(rename = "background")]
    Background,
    #[serde(rename = "slow")]
    Slow,
}

impl std::fmt::Display for FlushType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:?}", self)
    }
}

pub struct CachedDirectoryMeters {
    pub disk_hits: Counter<u64>,
    pub misses: Counter<u64>,

    pub cold_reads: Counter<u64>,
    pub cold_read_bytes: Histogram<u64>,
    pub cold_read_duration_ms: Histogram<u64>,
    pub hot_read_bytes: Histogram<u64>,
    pub hot_read_duration_ms: Histogram<u64>,
    pub hot_read_errors: Counter<u64>,

    pub cold_writes: Counter<u64>,
    pub cold_write_bytes: Histogram<u64>,
    pub cold_write_duration_ms: Histogram<u64>,
    pub hot_writes: Counter<u64>,
    pub hot_write_bytes: Histogram<u64>,
    pub hot_write_duration_ms: Histogram<u64>,
    pub hot_write_errors: Counter<u64>,

    pub cached_buffers: Gauge<u64>,
    pub cached_bytes: Gauge<u64>,
    pub cache_file_evictions: Counter<u64>,
    pub cache_file_full_errors: Counter<u64>,
}
impl Default for CachedDirectoryMeters {
    fn default() -> CachedDirectoryMeters {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        CachedDirectoryMeters {
            disk_hits: meter
                .u64_counter("brainstore.storage.cached_directory.disk_hits")
                .build(),
            misses: meter
                .u64_counter("brainstore.storage.cached_directory.misses")
                .build(),
            cold_reads: meter
                .u64_counter("brainstore.storage.cached_directory.cold_reads")
                .build(),
            cold_read_bytes: meter
                .u64_histogram("brainstore.storage.cached_directory.cold_read_bytes")
                .build(),
            cold_read_duration_ms: meter
                .u64_histogram("brainstore.storage.cached_directory.cold_read_duration_ms")
                .build(),
            hot_read_bytes: meter
                .u64_histogram("brainstore.storage.cached_directory.disk_backfill_read_bytes")
                .build(),
            hot_read_duration_ms: meter
                .u64_histogram("brainstore.storage.cached_directory.disk_backfill_read_duration_ms")
                .build(),
            hot_read_errors: meter
                .u64_counter("brainstore.storage.cached_directory.hot_read_errors")
                .build(),
            cold_writes: meter
                .u64_counter("brainstore.storage.cached_directory.cold_writes")
                .build(),
            cold_write_bytes: meter
                .u64_histogram("brainstore.storage.cached_directory.cold_write_bytes")
                .build(),
            cold_write_duration_ms: meter
                .u64_histogram("brainstore.storage.cached_directory.cold_write_duration_ms")
                .build(),
            hot_writes: meter
                .u64_counter("brainstore.storage.cached_directory.hot_writes")
                .build(),
            hot_write_bytes: meter
                .u64_histogram("brainstore.storage.cached_directory.hot_write_bytes")
                .build(),
            hot_write_duration_ms: meter
                .u64_histogram("brainstore.storage.cached_directory.hot_write_duration_ms")
                .build(),
            hot_write_errors: meter
                .u64_counter("brainstore.storage.cached_directory.hot_write_errors")
                .build(),
            cached_buffers: meter
                .u64_gauge("brainstore.storage.cached_directory.cached_buffers")
                .build(),
            cached_bytes: meter
                .u64_gauge("brainstore.storage.cached_directory.cached_bytes")
                .build(),
            cache_file_evictions: meter
                .u64_counter("brainstore.storage.cached_directory.cache_file_evictions")
                .build(),
            cache_file_full_errors: meter
                .u64_counter("brainstore.storage.cached_directory.cache_file_full_errors")
                .build(),
        }
    }
}

#[derive(Clone)]
struct HotCacheFileHandle<F: CacheFile<CacheDirectoryKey>> {
    cold_reader: Arc<dyn AsyncFileHandle>,
    file_name: PathBuf,
    cache_file: F,
    page_size: usize,
    last_page: Arc<ArcSwapOption<(std::ops::Range<usize>, Arc<OwnedBytes>)>>,
    timer: Arc<TimerManager>,
    flush_type: FlushType,
}

impl<F: CacheFile<CacheDirectoryKey>> std::fmt::Debug for HotCacheFileHandle<F> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("HotCacheFileHandle")
            .field("file_name", &self.file_name)
            .finish()
    }
}

impl<F: CacheFile<CacheDirectoryKey>> HasLen for HotCacheFileHandle<F> {
    fn len(&self) -> usize {
        self.cold_reader.len()
    }
}

#[async_trait]
impl<F: CacheFile<CacheDirectoryKey>> AsyncFileHandle for HotCacheFileHandle<F> {
    async fn async_read_bytes(
        &self,
        range: std::ops::Range<usize>,
    ) -> std::io::Result<tantivy::directory::OwnedBytes> {
        self.async_read_bytes_cached(range, self.cache_file.should_cache_large_reads())
            .await
    }

    fn sync_read_bytes(
        &self,
        range: std::ops::Range<usize>,
    ) -> std::io::Result<tantivy::directory::OwnedBytes> {
        let rounded_range = self.get_rounded_range(&range)?;
        let key = CacheDirectoryKeyRef(&self.file_name, rounded_range.clone());

        if let Some(cached) = self.check_if_cached(&key, &range) {
            return Ok(cached);
        }

        // First, check if the range is cached.
        let cached_buffer = self.cache_file.sync_get_cached_page(&key, &self.timer)?;

        if let Some(cached_buffer) = cached_buffer {
            let effective_range =
                range.start - rounded_range.start..range.end - rounded_range.start;
            debug_assert!(effective_range.start <= effective_range.end);
            debug_assert!(effective_range.len() <= cached_buffer.len());

            self.last_page
                .store(Some(Arc::new((rounded_range, cached_buffer.clone()))));

            return Ok(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
                cached_buffer,
                effective_range,
            )));
        }

        // Next, check if the unrounded range is cached. This can happen if we're reading a redirect.
        // Since we're about to check again, undo the previous miss
        self.cache_file.incr_stat_value(&key, StatType::Miss, -1);
        let unrounded_key = CacheDirectoryKeyRef(&self.file_name, range.clone());
        let cached_buffer = self
            .cache_file
            .sync_get_cached_page(&unrounded_key, &self.timer)?;

        if let Some(cached_buffer) = cached_buffer {
            let len = cached_buffer.len();
            return Ok(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
                cached_buffer,
                0..len,
            )));
        }

        // Fall back to async mode
        STATIC_SYNC_RUNTIME.block_on(self.cold_read(
            &range,
            &rounded_range,
            self.cache_file.should_cache_large_reads(),
        ))
    }
}

impl<F: CacheFile<CacheDirectoryKey>> HotCacheFileHandle<F> {
    async fn swap_with_cached_buffer(
        &self,
        rounded_range: std::ops::Range<usize>,
        content_arc: Arc<OwnedBytes>,
    ) -> std::io::Result<Arc<OwnedBytes>> {
        let result = self
            .cache_file
            .write_page_to_cache(
                CacheDirectoryKey(self.file_name.clone(), rounded_range.clone()),
                content_arc.clone(),
                FlushType::Sync,
                self.timer.clone(),
            )
            .await;

        Ok(match result {
            Ok(buf) => buf,
            Err(CacheError::NotEnoughSpace { .. } | CacheError::NextChunkActive)
            | Err(CacheError::NextChunkInUse) => content_arc,
            Err(e) => {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Error writing page to cache: {:?}", e),
                ))
            }
        })
    }

    // The only difference between this and async_read_bytes is that it can replace the result
    // with a cached buffer
    async fn async_read_bytes_cached(
        &self,
        range: std::ops::Range<usize>,
        replace_with_cached: bool,
    ) -> std::io::Result<tantivy::directory::OwnedBytes> {
        let rounded_range = self.get_rounded_range(&range)?;
        let key = CacheDirectoryKeyRef(&self.file_name, rounded_range.clone());

        if let Some(cached) = self.check_if_cached(&key, &range) {
            return Ok(cached);
        }

        // First, check if the range is cached.
        let cached_buffer = self
            .cache_file
            .async_get_cached_page(unsafe { key.unsafe_make_static() }, &self.timer)
            .await?;

        if let Some(cached_buffer) = cached_buffer {
            let effective_range =
                range.start - rounded_range.start..range.end - rounded_range.start;
            debug_assert!(effective_range.start <= effective_range.end);
            debug_assert!(effective_range.len() <= cached_buffer.len());

            self.last_page.store(Some(Arc::new((
                rounded_range,
                // This is cloning the inner OwnedBytes, which is itself backed by an Arc, so this is cheap.
                cached_buffer.clone(),
            ))));

            return Ok(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
                cached_buffer,
                effective_range,
            )));
        }

        // Next, check if the unrounded range is cached. This can happen if we're reading a redirect.

        // Since we're about to check again, undo the previous miss
        self.cache_file.incr_stat_value(&key, StatType::Miss, -1);

        let unrounded_key = CacheDirectoryKeyRef(&self.file_name, range.clone());
        let cached_buffer = self
            .cache_file
            .async_get_cached_page(unsafe { unrounded_key.unsafe_make_static() }, &self.timer)
            .await?;

        if let Some(cached_buffer) = cached_buffer {
            let len = cached_buffer.len();
            return Ok(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
                cached_buffer,
                0..len,
            )));
        }

        let ret = self
            .cold_read(&range, &rounded_range, replace_with_cached)
            .await?;

        Ok(ret)
    }

    #[inline]
    fn check_if_cached(
        &self,
        key: &CacheDirectoryKeyRef,
        range: &std::ops::Range<usize>,
    ) -> Option<tantivy::directory::OwnedBytes> {
        if let Some(existing) = self.last_page.load().as_ref() {
            let (last_range, last_page) = existing.as_ref();
            if last_range.start <= range.start && range.end <= last_range.end {
                self.cache_file.incr_stat(key, StatType::MemHit);
                return Some(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
                    last_page.clone(),
                    range.start - last_range.start..range.end - last_range.start,
                )));
            }
        }
        None
    }

    #[inline]
    fn get_rounded_range(
        &self,
        range: &std::ops::Range<usize>,
    ) -> std::io::Result<std::ops::Range<usize>> {
        if range.end > self.len() {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                "Range extends beyond the end of the file",
            ));
        }

        time!(self.timer, "read_bytes");

        let mut rounded_range = round_to_page_size(range, self.page_size);
        rounded_range.end = std::cmp::min(rounded_range.end, self.len());
        debug_assert!(rounded_range.start <= rounded_range.end);
        debug_assert!(range.end <= rounded_range.end);

        Ok(rounded_range)
    }

    #[inline(never)]
    async fn cold_read_large_range(
        &self,
        range: &std::ops::Range<usize>,
        rounded_range: &std::ops::Range<usize>,
    ) -> std::io::Result<tantivy::directory::OwnedBytes>
    where
        Self: Send + Sync,
    {
        let start = std::time::Instant::now();
        // If the requested range is larger than a page, then read each page in parallel and concatenate them together.
        // The individual pages may be cached, or if not, will map to cold reads that are page-aligned and will be cached.
        let mut contents: Vec<u8> = Vec::with_capacity(rounded_range.len());
        let mut futures = Vec::with_capacity(rounded_range.len() / self.page_size);
        for offset in (rounded_range).clone().step_by(self.page_size) {
            let sub_range = offset..std::cmp::min(offset + self.page_size, rounded_range.end);
            // Recursively call itself, which ensures that we'll cache each piece, and future large reads can
            // reuse them. Do not swap the results with cached buffers, because we're about to cache them, so we
            // don't mind using the raw buffers directly. They will be deallocated very soon.
            futures.push(self.async_read_bytes_cached(sub_range, false));
        }

        for result in join_all(futures).await {
            contents.extend(result?.into_iter());
        }

        log::debug!(
            "[{:?}] cold_read_large_range {:?} ({:?}) {:?}\t{}kb\t took {:?}",
            std::thread::current().id(),
            self.file_name,
            range,
            rounded_range,
            (if rounded_range.len() >= self.page_size {
                rounded_range.len() as f64
            } else {
                self.page_size as f64
            }) / 1024.0,
            start.elapsed()
        );

        return Ok(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
            if self.cache_file.should_cache_large_reads() {
                self.swap_with_cached_buffer(
                    rounded_range.clone(),
                    Arc::new(OwnedBytes::new(contents)),
                )
                .await?
            } else {
                Arc::new(OwnedBytes::new(contents))
            },
            range.start - rounded_range.start..range.end - rounded_range.start,
        )));
    }

    #[inline(never)]
    async fn cold_read(
        &self,
        range: &std::ops::Range<usize>,
        rounded_range: &std::ops::Range<usize>,
        replace_with_cached: bool,
    ) -> std::io::Result<tantivy::directory::OwnedBytes> {
        if rounded_range.len() > self.page_size {
            return self.cold_read_large_range(range, rounded_range).await;
        }

        // Read page_size bytes in parallel from the cold directory, and concatenate the bytes together
        time!(self.timer, "cold read");
        let start = std::time::Instant::now();

        let borrowed_key = CacheDirectoryKeyRef(&self.file_name, rounded_range.clone());
        let cold_read_lock = {
            let mut meta = self.cache_file.meta().write().unwrap();
            let entry = raw_entry_mut_from_hash(&mut meta.active_cold_reads, &borrowed_key);

            match entry {
                RawEntryMut::Occupied(entry) => entry.get().clone(),
                RawEntryMut::Vacant(entry) => {
                    let lock = Arc::new(tokio::sync::Mutex::new(()));
                    entry.insert(borrowed_key.convert_to_key(), lock.clone());
                    lock
                }
            }
        };
        let _guard = ColdReadLockGuard {
            key: &borrowed_key,
            inner: self.cache_file.clone(),
            _guard: cold_read_lock.lock().await,
            _phantom: std::marker::PhantomData,
        };

        {
            // Once we acquire the lock, check the metadata once more, in case it was written concurrently
            // by another lock holder. Use the cache file interface directly, so we don't mess with the statistics.
            let meta = self
                .cache_file
                .meta()
                .read()
                .expect("Failed to acquire metadata lock");

            if let Some(cache_entry) = meta.get_cache_entry(&borrowed_key) {
                if let Some(buf) = cache_entry.cached_buf.upgrade() {
                    return Ok(OwnedBytes::new(OwnedBytesArcRange(
                        buf,
                        range.start - rounded_range.start..range.end - rounded_range.start,
                    )));
                }
            }
        }

        self.cache_file.incr_stat(&borrowed_key, StatType::ColdRead);
        let contents = Arc::new(
            self.cold_reader
                .async_read_bytes(rounded_range.clone())
                .await?,
        );
        debug_assert!(contents.len() == rounded_range.len());

        if log::log_enabled!(log::Level::Debug) {
            log::debug!(
                "[{:?}] cold read {:?} ({:?}) {:?}\t{}kb\t took {:?}",
                std::thread::current().id(),
                self.file_name,
                range,
                rounded_range,
                (if rounded_range.len() >= self.page_size {
                    rounded_range.len() as f64
                } else {
                    self.page_size as f64
                }) / 1024.0,
                start.elapsed()
            );
        }
        CACHED_DIRECTORY_METERS
            .cold_read_bytes
            .record(rounded_range.len() as u64, &[]);
        CACHED_DIRECTORY_METERS
            .cold_read_duration_ms
            .record(start.elapsed().as_millis() as u64, &[]);

        let cache_file = self.cache_file.clone();

        let copied_timer = self.timer.clone();
        let flush_type = self.flush_type;
        let owned_key = CacheDirectoryKey(self.file_name.clone(), rounded_range.clone());

        let result = cache_file
            // This clone is just copying the Arc so should be cheap
            .write_page_to_cache(
                owned_key,
                contents.clone(),
                if replace_with_cached {
                    FlushType::Sync
                } else {
                    flush_type
                },
                copied_timer,
            )
            .await;
        if let Err(e) = result {
            log::warn!("Error flushing cache contents: {:?}", e);
        }

        self.last_page
            .store(Some(Arc::new((rounded_range.clone(), contents.clone()))));

        Ok(tantivy::directory::OwnedBytes::new(OwnedBytesArcRange(
            contents,
            range.start - rounded_range.start..range.end - rounded_range.start,
        )))
    }
}

// NOTE: The CachingWriter is no longer used, but is kept around for reference. Theoretically, if you are
// reading and writing in the same directory, it's nice because it pre-caches stuff you're about to read. In
// practice, we've split up readers/writers to be on separate nodes, and this just bloats the memory and disk
// cache, so it does more harm than good.
struct CachingWriter<F: CacheFile<CacheDirectoryKey>> {
    file_name: PathBuf,

    cold_writer: PinnedAsyncWriteHandler,
    cache_file: F,

    buffer: VecDeque<u8>,
    offset: usize,

    page_size: usize,
    flush_type: FlushType,
    timer: Arc<TimerManager>,
}

#[async_trait]
impl<F: CacheFile<CacheDirectoryKey> + Unpin> AsyncWriteHandler for CachingWriter<F> {
    async fn write<'a>(
        mut self: std::pin::Pin<&'a mut Self>,
        buf: &'a [u8],
    ) -> std::io::Result<usize> {
        let bytes_written = self.cold_writer.as_mut().write(buf).await?;

        self.buffer.extend(buf[..bytes_written].iter());
        if self.buffer.len() >= self.page_size {
            let key = CacheDirectoryKey(
                self.file_name.clone(),
                self.offset..self.offset + self.page_size,
            );
            self.offset += self.page_size;

            let page_size = self.page_size;

            let buffered = self.buffer.drain(0..page_size).collect::<Vec<u8>>();
            if let Err(e) = self
                .cache_file
                .write_page_to_cache(
                    key,
                    Arc::new(OwnedBytes::new(buffered)),
                    self.flush_type,
                    self.timer.clone(),
                )
                .await
            {
                log::warn!("Error flushing cache contents: {:?}", e);
            }
        }

        Ok(bytes_written)
    }

    async fn flush(mut self: std::pin::Pin<&mut Self>) -> std::io::Result<()> {
        self.cold_writer.as_mut().flush().await
    }

    async fn shutdown(mut self: std::pin::Pin<&mut Self>) -> std::io::Result<()> {
        self.cold_writer.as_mut().shutdown().await?;

        let key = CacheDirectoryKey(
            self.file_name.clone(),
            self.offset..self.offset + self.buffer.len(),
        );

        let buffered = self.buffer.drain(..).collect::<Vec<u8>>();
        if let Err(e) = self
            .cache_file
            .write_page_to_cache(
                key,
                Arc::new(OwnedBytes::new(buffered)),
                self.flush_type,
                self.timer.clone(),
            )
            .await
        {
            log::warn!("Error flushing cache contents: {:?}", e);
        }

        Ok(())
    }
}

// CacheEntry, CacheMeta, and CacheFile are the core implementation
// of a ringbuffer cache, that is loosely based off of
//  https://jasony.me/publication/sosp23-s3fifo.pdf
//
// Each cache is a single file that grows up to a certain size.
// We separately store a list of ranges and the corresponding
// offsets in the file. When we run out of space, we wrap around.

pub trait CacheKey: Eq + std::hash::Hash + Clone + std::fmt::Debug {}

impl CacheKey for CacheDirectoryKey {}
impl CacheKey for std::ops::Range<usize> {}
impl CacheKey for std::ops::Range<i32> {}

#[derive(Clone, Debug)]
pub struct CacheEntry<K: CacheKey> {
    pub id: u64,
    pub key: K,
    pub physical_range: std::ops::Range<usize>,
    pub cached_buf: Weak<OwnedBytes>,
}

#[derive(Error, Debug)]
pub enum CacheError {
    #[error("Not enough space (requested {requested}, available {available})")]
    NotEnoughSpace { requested: usize, available: usize },

    #[error("Next available chunk is actively being written to")]
    NextChunkActive,

    #[error("Next available chunk is potentially being read")]
    NextChunkInUse,

    #[error(transparent)]
    IO(#[from] std::io::Error),

    #[error(transparent)]
    Anyhow(#[from] util::anyhow::Error),
}

type Result<T, E = CacheError> = std::result::Result<T, E>;

#[derive(Debug, Clone)]
pub struct CacheMeta<K: CacheKey> {
    // This is an efficient way to index by range. The key is the "logical range"
    // that we want to cache.
    pub pages: FastHashMap<K, CacheEntry<K>>,
    active_cold_reads: FastHashMap<K, Arc<tokio::sync::Mutex<()>>>,
    // This is contiguous in the file. The chunks are stored in order of their
    // start offset in the file.
    pub chunks: VecDeque<CacheEntry<K>>,
    // This keeps track of the buffers that are currently being actively written to.
    pub actively_writing: HashSet<u64>,
    // This keeps track of the buffers that are currently cached.
    // Once something is removed from here, it's reference count gets
    // decremented and if it reaches 0, the buffer is deleted (invalidating
    // the weak ptr in the cache entry).
    cached_buffers: VecDeque<Arc<OwnedBytes>>,
    cached_bytes: usize,

    // This is incremented each time we allocate a new buffer
    next_id: u64,
    pub file_size: usize,
    pub memory_limit: usize,

    // Reject active buffers for mmap'd files, because they can't be overwritten.
    reject_active_buffers: bool,
}

impl<K: CacheKey> CacheMeta<K> {
    pub fn new(file_size: usize, memory_limit: usize, reject_active_buffers: bool) -> Self {
        Self {
            pages: FastHashMap::new(),
            active_cold_reads: FastHashMap::new(),
            chunks: VecDeque::new(),
            actively_writing: HashSet::new(),
            cached_buffers: VecDeque::new(),
            cached_bytes: 0,
            next_id: 0,
            file_size,
            memory_limit,
            reject_active_buffers,
        }
    }

    pub fn allocate_chunk(
        &mut self,
        key: K,
        cached_buf: &Arc<OwnedBytes>,
    ) -> Result<CacheEntry<K>, CacheError> {
        let cached_buf_len = cached_buf.len();
        if cached_buf_len > self.file_size {
            return Err(CacheError::NotEnoughSpace {
                requested: cached_buf.len(),
                available: self.file_size,
            });
        }

        let id = self.next_id;
        self.next_id += 1;
        let mut entry = CacheEntry {
            id,
            key: key.clone(),
            physical_range: 0..cached_buf_len,
            cached_buf: Arc::downgrade(cached_buf),
        };

        // Theoretically, we could "wrap" a single buffer around the file, but
        // this will make everything more complicated. There are basically two
        // cases:
        // ----------------------------------------------------------------
        //    ^-- start                                 ^-- end
        // and
        // ----------------------------------------------------------------
        //     ^-- end (wrapped)                     ^-- start
        //
        // In the first case, the available space is _after_ end or _before_ start.
        // In the second case, the available space is _after_ start or _before_ end.
        // In both cases, if we need more space, we push start forward, until there are
        // no buffers left in the file.
        loop {
            let start_chunk = self.chunks.front();
            let end_chunk = self.chunks.back();

            let start_offset = start_chunk.map(|c| c.physical_range.start).unwrap_or(0);
            let end_offset = end_chunk.map(|c| c.physical_range.end).unwrap_or(0);

            let (before_start_space, after_end_space) =
                // start is before end if the first chunk's starting pointer is strictly earlier than the last chunk's ending
                // pointer, or if the overall list is empty (note: if the list is non-empty and the two pointers are equal, then
                // the list is completely full).
                if start_offset < end_offset || self.chunks.is_empty() {
                    (start_offset, self.file_size - end_offset)
                } else {
                    (start_offset - end_offset, start_offset - end_offset)
                };

            let new_range = if cached_buf_len <= before_start_space {
                Some(start_offset - cached_buf_len..start_offset)
            } else if cached_buf_len <= after_end_space {
                Some(end_offset..end_offset + cached_buf_len)
            } else {
                None
            };

            if let Some(new_range) = new_range {
                entry.physical_range = new_range;

                self.chunks.push_back(entry.clone());

                self.actively_writing.insert(entry.id);
                return Ok(self.pages.entry(key).insert(entry).get().clone());
            }

            // Otherwise, we need to evict the first chunk, as long as it's not actively being written to.
            if let Some(start_chunk) = self.chunks.front() {
                if self.actively_writing.contains(&start_chunk.id) {
                    CACHED_DIRECTORY_METERS.cache_file_full_errors.add(1, &[]);
                    return Err(CacheError::NextChunkActive);
                }

                if self.reject_active_buffers && start_chunk.cached_buf.strong_count() > 0 {
                    // Someone is still reading this chunk, so we can't evict it.
                    CACHED_DIRECTORY_METERS.cache_file_full_errors.add(1, &[]);
                    return Err(CacheError::NextChunkInUse);
                }
            }

            if let Some(first_chunk) = self.chunks.pop_front() {
                CACHED_DIRECTORY_METERS.cache_file_evictions.add(1, &[]);
                self.remove_cache_entry(first_chunk);
            } else {
                unreachable!("No chunks in the cache, but we should have been able to fit");
            }
        }
    }

    pub fn get_cache_entry<Q>(&self, key: &Q) -> Option<&CacheEntry<K>>
    where
        Q: RawEntryCompatible<K>,
    {
        let entry = raw_entry_from_hash(&self.pages, key);
        match entry {
            Some(entry) => Some(entry.1),
            None => None,
        }
    }

    pub fn get_cache_entry_mut<Q>(&mut self, key: &Q) -> Option<&mut CacheEntry<K>>
    where
        Q: RawEntryCompatible<K>,
    {
        use crate::hash_map::RawEntryMut;
        let entry = raw_entry_mut_from_hash(&mut self.pages, key);
        match entry {
            RawEntryMut::Occupied(entry) => Some(entry.into_mut()),
            RawEntryMut::Vacant(_) => None,
        }
    }

    pub fn remove_cache_entry(&mut self, entry: CacheEntry<K>) {
        if let Entry::Occupied(cached_entry) = self.pages.entry(entry.key.clone()) {
            if entry.id == cached_entry.get().id {
                cached_entry.remove();
            }
        }
    }

    pub fn cache_buffer(&mut self, buf: Arc<OwnedBytes>) {
        let buf_len = buf.len();

        if buf_len > self.memory_limit {
            return;
        }
        while self.cached_bytes + buf_len > self.memory_limit {
            let buf = self.cached_buffers.pop_front().unwrap();
            self.cached_bytes -= buf.len();
        }

        self.cached_buffers.push_back(buf);
        self.cached_bytes += buf_len;

        CACHED_DIRECTORY_METERS
            .cached_buffers
            .record(self.cached_buffers.len() as u64, &[]);
        CACHED_DIRECTORY_METERS
            .cached_bytes
            .record(self.cached_bytes as u64, &[]);
    }

    pub fn keys(&self) -> Vec<K> {
        self.pages.keys().cloned().collect()
    }
}

#[derive(Debug, Default, Clone)]
pub struct CacheFileTestingOptions {
    pub async_write_at_partial_write_rate: f64,
}

#[async_trait]
pub trait CacheFile<K: CacheKey + Send + Sync + 'static>:
    Clone + Send + Sync + 'static + Unpin
{
    fn new(
        file_name: &std::path::Path,
        tmp_dir: Option<Arc<tempfile::TempDir>>,
        file_size: usize,
        memory_limit: usize,
        stats_enabled: bool,
        testing_opts: CacheFileTestingOptions,
    ) -> Result<Self, std::io::Error>
    where
        Self: Sized;

    fn get_cached_entry<Q>(&self, key: &Q) -> BufferOrCacheEntry<K>
    where
        Q: RawEntryCompatible<K>,
    {
        let meta = self.meta().read().expect("Failed to acquire metadata lock");
        match meta.get_cache_entry(key) {
            Some(cache_entry) => match cache_entry.cached_buf.upgrade() {
                Some(buf) => {
                    self.incr_stat(key, StatType::MemHit);
                    BufferOrCacheEntry::Buffer(buf)
                }
                None => {
                    // This is the case where we've seen the chunk before, but it's been evicted
                    // from memory. So we read it from disk, cache it in memory, and then return it.
                    BufferOrCacheEntry::CacheEntry(cache_entry.clone())
                }
            },
            None => {
                self.incr_stat(key, StatType::Miss);
                BufferOrCacheEntry::None
            }
        }
    }

    fn sync_get_cached_page<Q>(
        &self,
        key: &Q,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K>;

    async fn async_get_cached_page<Q, U>(
        &self,
        key: &Q,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K> + UnsafeMakeStatic<Target = U> + Send + Sync + 'static,
        U: RawEntryCompatible<K> + Send + Sync + 'static;

    async fn write_page_to_cache(
        &self,
        key: K,
        buf: Arc<OwnedBytes>,
        flush_type: FlushType,
        timer: Arc<TimerManager>,
    ) -> Result<Arc<OwnedBytes>>;

    fn meta(&self) -> &std::sync::RwLock<CacheMeta<K>>;

    fn clear_memory(&self);
    fn clear_all(&self);

    fn keys(&self) -> Vec<K> {
        self.meta().read().unwrap().keys()
    }

    fn stats(&self) -> FastHashMap<K, CacheEntryStats>;
    fn incr_stat<Q>(&self, key: &Q, stat_type: StatType)
    where
        Q: RawEntryCompatible<K>;
    fn incr_stat_value<Q>(&self, key: &Q, stat_type: StatType, value: i64)
    where
        Q: RawEntryCompatible<K>;

    // This is only set for mmap and it lets it serve large reads from the mmap.
    fn should_cache_large_reads(&self) -> bool {
        false
    }
}

#[derive(Clone)]
pub struct DirectCacheFile<K: CacheKey>(Arc<DirectCacheFileInner<K>>);

struct DirectCacheFileInner<K: CacheKey> {
    meta: std::sync::RwLock<CacheMeta<K>>,
    stats: CacheFileStats<K>,
    file: std::fs::File,
    #[allow(unused)] // Used by tests
    file_path: std::path::PathBuf,
    // This is an easy way to increment a reference to the tmp dir so it's not deleted
    // while these files are open.
    _tmp_dir: Option<Arc<tempfile::TempDir>>,
    testing_opts: CacheFileTestingOptions,
}

impl<K: CacheKey> std::fmt::Debug for DirectCacheFile<K> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "DirectCacheFile")
    }
}

#[derive(Debug, Clone)]
pub enum StatType {
    DiskHit,
    MemHit,
    Miss,
    ColdRead,
}

#[derive(Debug, Clone)]
pub enum BufferOrCacheEntry<K: CacheKey> {
    Buffer(Arc<OwnedBytes>),
    CacheEntry(CacheEntry<K>),
    None,
}

struct ColdReadLockGuard<
    'a,
    K: CacheKey + Send + Sync + 'static,
    Q: RawEntryCompatible<K> + 'a,
    F: CacheFile<K>,
> {
    key: &'a Q,
    inner: F,
    _guard: tokio::sync::MutexGuard<'a, ()>,
    _phantom: std::marker::PhantomData<K>,
}

impl<'a, K: CacheKey + Send + Sync + 'static, Q: RawEntryCompatible<K> + 'a, F: CacheFile<K>> Drop
    for ColdReadLockGuard<'a, K, Q, F>
{
    fn drop(&mut self) {
        let mut meta = self.inner.meta().write().unwrap();
        let entry = raw_entry_mut_from_hash(&mut meta.active_cold_reads, self.key);
        match entry {
            RawEntryMut::Occupied(entry) => {
                entry.remove();
            }
            RawEntryMut::Vacant(_) => {}
        }
    }
}

#[async_trait]
impl<K: CacheKey + Send + Sync + 'static> CacheFile<K> for DirectCacheFile<K> {
    fn new(
        file_name: &std::path::Path,
        tmp_dir: Option<Arc<tempfile::TempDir>>,
        file_size: usize,
        memory_limit: usize,
        stats_enabled: bool,
        testing_opts: CacheFileTestingOptions,
    ) -> Result<Self, std::io::Error> {
        // NOTE: We could technically do this async, but since it happens once at the beginning of the program,
        // it's not a big deal.
        let file = std::fs::OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .open(file_name)?;

        Ok(Self(Arc::new(DirectCacheFileInner {
            meta: std::sync::RwLock::new(CacheMeta::new(
                file_size,
                memory_limit,
                false, /* reject_active_buffers */
            )),
            stats: CacheFileStats::new(stats_enabled),
            file,
            file_path: file_name.to_path_buf(),
            _tmp_dir: tmp_dir,
            testing_opts,
        })))
    }

    fn sync_get_cached_page<Q>(
        &self,
        key: &Q,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K>,
    {
        let cache_entry = match self.get_cached_entry(key) {
            BufferOrCacheEntry::Buffer(buf) => return Ok(Some(buf)),
            BufferOrCacheEntry::CacheEntry(cache_entry) => cache_entry,
            BufferOrCacheEntry::None => return Ok(None),
        };

        self.sync_backfill_cached_page(key, cache_entry, timer)
    }

    async fn async_get_cached_page<Q, U>(
        &self,
        key: &Q,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K> + UnsafeMakeStatic<Target = U> + Send + Sync + 'static,
        U: RawEntryCompatible<K> + Send + Sync + 'static,
    {
        let cache_entry = match self.get_cached_entry(key) {
            BufferOrCacheEntry::Buffer(buf) => return Ok(Some(buf)),
            BufferOrCacheEntry::CacheEntry(cache_entry) => cache_entry,
            BufferOrCacheEntry::None => return Ok(None),
        };

        let timer = timer.clone();

        // It's safe to transmute these to a static reference because we're not going to
        // use them after the function returns.
        let me = unsafe { std::mem::transmute::<_, &'static DirectCacheFile<K>>(self) };
        let static_key = unsafe { key.unsafe_make_static() };
        STATIC_SYNC_RUNTIME
            .spawn_blocking(move || me.sync_backfill_cached_page(static_key, cache_entry, &timer))
            .await?
    }

    async fn write_page_to_cache(
        &self,
        key: K,
        buf: Arc<OwnedBytes>,
        flush_type: FlushType,
        timer: Arc<TimerManager>,
    ) -> Result<Arc<OwnedBytes>> {
        let cache_entry = {
            let mut meta = self
                .0
                .meta
                .write()
                .expect("Failed to acquire metadata lock");
            let cache_entry = meta.allocate_chunk(key, &buf)?;
            meta.cache_buffer(buf.clone());
            cache_entry
        };

        let cache_file = self.clone();
        let cache_entry_id = cache_entry.id;

        let return_buf = buf.clone();

        // This is quite subtle, but since we capture the `Arc<Vec<u8>>` buf in
        // this closure, it is guaranteed that any concurrent reads of this page
        // will get an in-memory cache hit until this background task completes.
        // This is because the stored cache entry has a weak pointer to the same
        // buffer, so until buffer is destroyed, the cache entry pointer will be
        // valid.
        let handle = tokio::spawn(async move {
            time!(timer, "write_page_to_cache");

            if matches!(flush_type, FlushType::Slow) {
                let sleep_time = {
                    let mut rng = rand::thread_rng();
                    rng.gen_range(0..100)
                };
                tokio::time::sleep(tokio::time::Duration::from_millis(sleep_time)).await;
            }

            // Capture the result, but do not return it until we remove the cache entry from the actively writing set.
            let start = std::time::Instant::now();
            let result: Result<()> = match cache_file
                .async_write_at(buf.as_ref(), cache_entry.physical_range.start as u64)
                .await
            {
                Ok(num_bytes_written) => {
                    if num_bytes_written != buf.len() {
                        Err(util::anyhow::anyhow!("Failed to write all bytes to cache file").into())
                    } else {
                        Ok(())
                    }
                }
                Err(e) => Err(e.into()),
            };
            CACHED_DIRECTORY_METERS.hot_writes.add(1, &[]);
            CACHED_DIRECTORY_METERS
                .hot_write_duration_ms
                .record(start.elapsed().as_millis() as u64, &[]);
            CACHED_DIRECTORY_METERS
                .hot_write_bytes
                .record(buf.len() as u64, &[]);

            if result.is_err() {
                CACHED_DIRECTORY_METERS.hot_write_errors.add(1, &[]);

                cache_file
                    .0
                    .meta
                    .write()
                    .expect("Failed to acquire metadata lock")
                    .remove_cache_entry(cache_entry);
            }

            {
                let mut meta = cache_file
                    .0
                    .meta
                    .write()
                    .expect("Failed to acquire metadata lock");
                meta.actively_writing.remove(&cache_entry_id);
            }

            result
        });

        if matches!(flush_type, FlushType::Sync) {
            let block_result = handle.await;
            if let Err(e) = block_result {
                log::warn!("Error joining background write: {:?}", e);
            }
        }
        Ok(return_buf)
    }

    fn clear_memory(&self) {
        let mut meta = self
            .0
            .meta
            .write()
            .expect("Failed to acquire metadata lock");
        meta.cached_buffers.clear();
        meta.cached_buffers.shrink_to_fit();
    }

    fn clear_all(&self) {
        let mut meta = self
            .0
            .meta
            .write()
            .expect("Failed to acquire metadata lock");
        let file_size = meta.file_size;
        let memory_limit = meta.memory_limit;
        *meta = CacheMeta::new(
            file_size,
            memory_limit,
            false, /* reject_active_buffers */
        );
    }

    fn meta(&self) -> &std::sync::RwLock<CacheMeta<K>> {
        &self.0.meta
    }

    fn stats(&self) -> FastHashMap<K, CacheEntryStats> {
        self.0.stats.stats()
    }

    fn incr_stat<Q>(&self, key: &Q, stat_type: StatType)
    where
        Q: RawEntryCompatible<K>,
    {
        self.0.stats.incr_stat(key, stat_type);
    }

    fn incr_stat_value<Q>(&self, key: &Q, stat_type: StatType, value: i64)
    where
        Q: RawEntryCompatible<K>,
    {
        self.0.stats.incr_stat_value(key, stat_type, value);
    }
}

impl<K: CacheKey + Send + Sync + 'static> DirectCacheFile<K> {
    // There's no async version of read_at or write_at, so we need to use a blocking call.
    fn read_at(&self, buf: &mut [u8], offset: u64) -> std::io::Result<usize> {
        match self.0.file.read_at(buf, offset) {
            Ok(res) => Ok(res),
            Err(e) => {
                CACHED_DIRECTORY_METERS.hot_read_errors.add(1, &[]);
                Err(e)
            }
        }
    }

    async fn async_write_at(&self, buf: &[u8], offset: u64) -> std::io::Result<usize> {
        if self.0.testing_opts.async_write_at_partial_write_rate > 0.0 {
            let mut rng = rand::thread_rng();
            let random_number = rng.gen_range(0.0..1.0);
            if random_number < self.0.testing_opts.async_write_at_partial_write_rate {
                return Ok(0);
            }
        }

        // It's safe to transmute the file to a static reference because we're not going to
        // use it after the function returns.
        let file =
            unsafe { std::mem::transmute::<&std::fs::File, &'static std::fs::File>(&self.0.file) };
        let buf = unsafe { std::mem::transmute::<&[u8], &[u8]>(buf) };
        let res = spawn_blocking_with_async_timeout(
            STATIC_SYNC_RUNTIME.handle(),
            move || file.write_at(buf, offset),
            Default::default(),
            || "CacheFile::write_at".into(),
        )
        .await;
        match res {
            Ok(res) => res?,
            Err(e) => Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                e.to_string(),
            )),
        }
    }

    fn sync_backfill_cached_page<Q>(
        &self,
        key: &Q,
        cache_entry: CacheEntry<K>,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K>,
    {
        time!(timer, "disk backfill cache");
        let start = std::time::Instant::now();

        let target_len = cache_entry.physical_range.len();
        let mut raw_buf = vec![0; target_len];
        let read_bytes = self.read_at(&mut raw_buf, cache_entry.physical_range.start as u64)?;

        let buf = Arc::new(OwnedBytes::new(raw_buf));
        debug_assert_eq!(read_bytes, target_len, "Read bytes mismatch");

        let mut meta = self
            .0
            .meta
            .write()
            .expect("Failed to acquire metadata lock");
        let new_entry = meta.get_cache_entry_mut(key);
        let is_valid = match new_entry {
            None => false,
            Some(new_entry) => {
                if new_entry.id == cache_entry.id {
                    new_entry.cached_buf = Arc::downgrade(&buf);
                    meta.cache_buffer(buf.clone());
                    true
                } else {
                    false
                }
            }
        };

        CACHED_DIRECTORY_METERS
            .hot_read_bytes
            .record(target_len as u64, &[]);
        CACHED_DIRECTORY_METERS
            .hot_read_duration_ms
            .record(start.elapsed().as_millis() as u64, &[]);

        Ok(if is_valid {
            self.0.stats.incr_stat(key, StatType::DiskHit);
            Some(buf)
        } else {
            self.0.stats.incr_stat(key, StatType::Miss);
            None
        })
    }
}

pub struct CacheFileStats<K: CacheKey> {
    stats: Mutex<FastHashMap<K, CacheEntryStats>>,
    enabled: bool,
}

impl<K: CacheKey + Send + Sync + 'static> CacheFileStats<K> {
    pub fn new(enabled: bool) -> Self {
        Self {
            stats: Mutex::new(FastHashMap::new()),
            enabled,
        }
    }

    #[inline]
    pub fn incr_stat_value<Q>(&self, key: &Q, stat_type: StatType, value: i64)
    where
        Q: RawEntryCompatible<K>,
    {
        if value > 0 {
            match stat_type {
                StatType::DiskHit => CACHED_DIRECTORY_METERS.disk_hits.add(value as u64, &[]),
                StatType::MemHit => {}
                StatType::Miss => CACHED_DIRECTORY_METERS.misses.add(value as u64, &[]),
                StatType::ColdRead => CACHED_DIRECTORY_METERS.cold_reads.add(value as u64, &[]),
            }
        }

        if !self.enabled {
            return;
        }

        let mut stats = self.stats.lock().expect("stats lock");

        let hash_key = stats.hasher().hash_one(key);
        let s = stats
            .raw_entry_mut()
            .from_hash(hash_key, |k| key.is_match(k))
            .or_insert_with(|| {
                (
                    key.convert_to_key(),
                    CacheEntryStats {
                        num_disk_hits: 0,
                        num_mem_hits: 0,
                        num_misses: 0,
                        num_cold_reads: 0,
                        last_hit: 0,
                    },
                )
            })
            .1;

        match stat_type {
            StatType::DiskHit => s.num_disk_hits += value,
            StatType::MemHit => s.num_mem_hits += value,
            StatType::Miss => s.num_misses += value,
            StatType::ColdRead => s.num_cold_reads += value,
        };
        if !matches!(stat_type, StatType::Miss) {
            s.last_hit = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .expect("Time went backwards")
                .as_millis() as i64;
        }
    }

    #[inline]
    pub fn incr_stat<Q>(&self, key: &Q, stat_type: StatType)
    where
        Q: RawEntryCompatible<K>,
    {
        self.incr_stat_value(key, stat_type, 1);
    }

    pub fn stats(&self) -> FastHashMap<K, CacheEntryStats> {
        self.stats.lock().unwrap().clone()
    }
}

pub fn round_to_page_size(
    range: &std::ops::Range<usize>,
    page_size: usize,
) -> std::ops::Range<usize> {
    let start = range.start - (range.start % page_size);
    let end = range.end + page_size - 1 - ((range.end - 1) % page_size);
    start..end
}

// Add these functions outside of the impl block
pub fn create_normalized_filename(path: &Path, file_counter: usize) -> PathBuf {
    let sha256_hash = sha256_hash(path);
    let file_name = path
        .file_name()
        .and_then(|os_str| os_str.to_str())
        .unwrap_or("unknown");

    PathBuf::from(format!("{}-{}-{}", sha256_hash, file_counter, file_name))
}

fn sha256_hash(path: &Path) -> String {
    let mut hasher = Sha256::new();
    hasher.update(path.to_string_lossy().as_bytes());
    format!("{:x}", hasher.finalize())
}

pub struct OwnedBytesArcRange(
    pub Arc<tantivy::directory::OwnedBytes>,
    pub std::ops::Range<usize>,
);

impl Deref for OwnedBytesArcRange {
    type Target = [u8];

    fn deref(&self) -> &[u8] {
        &self.0.deref()[self.1.start..self.1.end]
    }
}

unsafe impl StableDeref for OwnedBytesArcRange {}

pub struct OwnedBytesRange(pub OwnedBytes, pub std::ops::Range<usize>);

impl Deref for OwnedBytesRange {
    type Target = [u8];

    fn deref(&self) -> &[u8] {
        &self.0.deref()[self.1.start..self.1.end]
    }
}

unsafe impl StableDeref for OwnedBytesRange {}

#[cfg(test)]
mod tests {
    use crate::{
        directory::{mmap_directory::MmapCacheFile, object_store_directory::ObjectStoreDirectory},
        instrumented::aggregate_stats,
    };

    use super::*;

    use object_store::local::LocalFileSystem;
    use rand::Rng;
    use tantivy::Directory;
    use tempfile::TempDir;

    #[test]
    fn test_round_to_page_size() {
        const PAGE_SIZE: usize = 4096;

        // Test case 1: Range within a single page
        assert_eq!(round_to_page_size(&(100..200), PAGE_SIZE), 0..4096);

        // Test case 2: Range spanning multiple pages
        assert_eq!(round_to_page_size(&(4000..8200), PAGE_SIZE), 0..12288);

        // Test case 3: Start aligned with page boundary
        assert_eq!(round_to_page_size(&(4096..6000), PAGE_SIZE), 4096..8192);

        // Test case 4: End aligned with page boundary
        assert_eq!(round_to_page_size(&(2000..8192), PAGE_SIZE), 0..8192);

        assert_eq!(round_to_page_size(&(2000..8193), PAGE_SIZE), 0..12288);

        // Test case 5: Both start and end aligned with page boundaries
        assert_eq!(round_to_page_size(&(4096..8192), PAGE_SIZE), 4096..8192);

        // Test case 6: Range smaller than a page
        assert_eq!(round_to_page_size(&(50..60), PAGE_SIZE), 0..4096);

        // Test case 7: Large range spanning many pages
        assert_eq!(round_to_page_size(&(8192..20480), PAGE_SIZE), 8192..20480);

        // Test case 8: Start and end in the middle of different pages
        assert_eq!(round_to_page_size(&(2050..6140), PAGE_SIZE), 0..8192);

        // Test case 9: Zero-length range
        assert_eq!(round_to_page_size(&(1000..1000), PAGE_SIZE), 0..4096);

        // Test case 10: Range at the very end of a page
        assert_eq!(round_to_page_size(&(4095..4097), PAGE_SIZE), 0..8192);
    }

    #[tokio::test]
    async fn test_cache_file_sanity() {
        let timer = TimerManager::new("FileCacheDirectory");
        let temp_dir = tempfile::tempdir().unwrap();
        let cache_path = temp_dir.path().join("test_cache_file");
        let cache_file: DirectCacheFile<std::ops::Range<i32>> = DirectCacheFile::new(
            &cache_path,
            Some(temp_dir.into()),
            1024 * 1024,
            1024 * 1024,
            true,
            CacheFileTestingOptions::default(),
        )
        .unwrap();

        // Try reading empty data
        let data = cache_file.sync_get_cached_page(&(0..100), &timer).unwrap();
        assert_eq!(data, None);

        let stats = cache_file.stats().values().next().cloned().unwrap();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 0);
        assert_eq!(stats.num_misses, 1);

        // Write some data
        let data = vec![1; 100];
        cache_file
            .write_page_to_cache(
                0..100,
                Arc::new(OwnedBytes::new(data.clone())),
                FlushType::Sync,
                timer.clone(),
            )
            .await
            .unwrap();

        // Read it back
        let read_data = cache_file
            .sync_get_cached_page(&(0..100), &timer)
            .unwrap()
            .unwrap();
        assert_eq!(read_data.as_slice(), &data);

        let stats = cache_file.stats().values().next().cloned().unwrap();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 1);
        assert_eq!(stats.num_misses, 1);

        drop(read_data);
        cache_file.clear_memory();

        let read_data = cache_file
            .sync_get_cached_page(&(0..100), &timer)
            .unwrap()
            .unwrap();
        assert_eq!(read_data.as_slice(), &data);

        let stats = cache_file.stats().values().next().cloned().unwrap();
        assert_eq!(stats.num_disk_hits, 1);
        assert_eq!(stats.num_mem_hits, 1);
        assert_eq!(stats.num_misses, 1);

        let cache_file_path = cache_file.0.file_path.clone();
        assert!(cache_file_path.exists());
        drop(cache_file);
        assert!(!cache_file_path.exists());
    }

    async fn page_size_test<F: CacheFile<CacheDirectoryKey>>(final_is_disk_hit: bool) {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory (using MmapDirectory as a stand-in)
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a CacheDirectory that wraps the ObjectStorage
        let cache_opts = FileCacheOpts {
            memory_limit: 1024 * 1024,   // 1 MB
            file_size: 1024 * 1024,      // 1 MB
            page_size: 10,               // As specified in the instructions
            flush_type: FlushType::Sync, // So that the stats are correct
            stats_enabled: true,
            ..Default::default()
        };
        let mut cache_dir = CacheDirectory::<F>::new(object_storage, None, cache_opts).unwrap();

        // Create a file with numbers 0 to 99
        let data = (0..100).map(|i| i as u8).collect::<Vec<u8>>();
        let path = Path::new("test_file");
        cache_dir.async_atomic_write(path, &data).await.unwrap();
        cache_dir.clear_all().unwrap();

        let full_data = cache_dir.async_atomic_read(path).await.unwrap();
        assert_eq!(full_data.len(), 100);
        for (i, &byte) in full_data.iter().enumerate() {
            assert_eq!(byte, i as u8);
        }

        // Read bytes 3-5
        let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();
        let get_stats = || aggregate_stats(cache_dir.stats().values());

        let three_five = handle.async_read_bytes(3..6).await.unwrap();
        assert_eq!(three_five.len(), 3);
        assert_eq!(three_five[0], 3);
        assert_eq!(three_five[1], 4);
        assert_eq!(three_five[2], 5);

        let stats = get_stats();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 0);
        assert_eq!(stats.num_misses, 1);

        // Read bytes 7-9
        let seven_nine = handle.async_read_bytes(7..10).await.unwrap();
        assert_eq!(seven_nine.len(), 3);
        assert_eq!(seven_nine[0], 7);
        assert_eq!(seven_nine[1], 8);
        assert_eq!(seven_nine[2], 9);

        let stats = get_stats();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 1);
        assert_eq!(stats.num_misses, 1);

        // Read bytes 60-99
        let sixty_ninety_nine = handle.async_read_bytes(60..100).await.unwrap();
        assert_eq!(sixty_ninety_nine.len(), 40);
        for i in 60..100 {
            assert_eq!(sixty_ninety_nine[i - 60], i as u8);
        }

        let stats = get_stats();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 1);
        assert_eq!(stats.num_misses, 6);

        // Now, just read bytes 95-99
        drop(handle);
        let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();
        let ninety_five_ninety_nine = handle.async_read_bytes(95..100).await.unwrap();
        assert_eq!(ninety_five_ninety_nine.len(), 5);
        for i in 95..100 {
            assert_eq!(ninety_five_ninety_nine[i - 95], i as u8);
        }

        let stats = get_stats();
        assert_eq!(stats.num_disk_hits, if final_is_disk_hit { 1 } else { 0 });
        assert_eq!(stats.num_mem_hits, if final_is_disk_hit { 1 } else { 2 });
        assert_eq!(stats.num_misses, 6);
    }

    #[tokio::test]
    async fn page_size_test_direct() {
        page_size_test::<DirectCacheFile<CacheDirectoryKey>>(false).await;
    }

    #[tokio::test]
    async fn page_size_test_mmap() {
        page_size_test::<MmapCacheFile<CacheDirectoryKey>>(true).await;
    }

    async fn multi_page_test<F: CacheFile<CacheDirectoryKey>>() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory (using MmapDirectory as a stand-in)
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a CacheDirectory that wraps the ObjectStorage
        let cache_opts = FileCacheOpts {
            memory_limit: 1024 * 1024,   // 1 MB
            file_size: 1024 * 1024,      // 1 MB
            page_size: 10,               // As specified in the instructions
            flush_type: FlushType::Sync, // So that the stats are correct
            stats_enabled: true,
            ..Default::default()
        };
        let mut cache_dir = CacheDirectory::<F>::new(object_storage, None, cache_opts).unwrap();

        // Create a file with numbers 0 to 99
        let data = (0..100).map(|i| i as u8).collect::<Vec<u8>>();
        let path = Path::new("test_file");
        cache_dir.async_atomic_write(path, &data).await.unwrap();
        cache_dir.clear_all().unwrap();

        let get_stats = || aggregate_stats(cache_dir.stats().values());

        // Request bytes 95-98 and 80-85
        let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();
        let ninety_five_ninety_eight = handle.async_read_bytes(95..99).await.unwrap();
        assert_eq!(ninety_five_ninety_eight.len(), 4);
        for i in 0..4 {
            assert_eq!(ninety_five_ninety_eight[i], 95 + i as u8);
        }

        let eighty_eighty_five = handle.async_read_bytes(80..85).await.unwrap();
        assert_eq!(eighty_eighty_five.len(), 5);
        for i in 0..5 {
            assert_eq!(eighty_eighty_five[i], 80 + i as u8);
        }

        let stats = get_stats();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 0);
        assert_eq!(stats.num_misses, 2);

        // Now request bytes from the beginning of the file (bytes 4-8)
        let four_eight = handle.async_read_bytes(4..8).await.unwrap();
        assert_eq!(four_eight.len(), 4);
        for i in 0..4 {
            assert_eq!(four_eight[i], 4 + i as u8);
        }

        let stats = get_stats();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 0);
        assert_eq!(stats.num_misses, 3);
    }

    #[tokio::test]
    async fn multi_page_test_direct() {
        multi_page_test::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn multi_page_test_mmap() {
        multi_page_test::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    async fn concurrent_cold_reads<F: CacheFile<CacheDirectoryKey>>(check_cold_reads: bool) {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory (using MmapDirectory as a stand-in)
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a FileCacheDirectory that wraps the ObjectStorage
        let cache_opts = FileCacheOpts {
            memory_limit: 1024 * 1024, // 1 MB
            file_size: 1024 * 1024,    // 1 MB
            page_size: 10,             // As specified in the instructions
            flush_type: FlushType::Slow,
            stats_enabled: true,
            ..Default::default()
        };
        let mut cache_dir = CacheDirectory::<F>::new(object_storage, None, cache_opts).unwrap();

        // Create a file with numbers 0 to 99
        let data = (0..100).map(|i| i as u8).collect::<Vec<u8>>();
        let path = Path::new("test_file");
        cache_dir.async_atomic_write(path, &data).await.unwrap();
        cache_dir.clear_all().unwrap();

        let get_stats = || aggregate_stats(cache_dir.stats().values());

        // Kick off 10 concurrent cold reads for the same range (bytes 0-9)
        let handles = (0..10)
            .map(|_| {
                let cache_dir = cache_dir.clone();
                tokio::spawn(async move {
                    let handle = cache_dir.async_get_file_handle(&path, None).await.unwrap();
                    handle.async_read_bytes(0..9).await.unwrap()
                })
            })
            .collect::<Vec<_>>();

        // Wait for all the reads to complete
        for handle in handles {
            handle.await.unwrap();
        }

        let stats = get_stats();
        if check_cold_reads {
            assert_eq!(stats.num_cold_reads, 1);
        } else {
            assert!(stats.num_cold_reads > 0);
        }
    }

    #[tokio::test]
    async fn concurrent_cold_reads_direct() {
        concurrent_cold_reads::<DirectCacheFile<CacheDirectoryKey>>(true).await;
    }

    #[tokio::test]
    async fn concurrent_cold_reads_mmap() {
        concurrent_cold_reads::<MmapCacheFile<CacheDirectoryKey>>(
            false, /* may have a lot more cold reads */
        )
        .await;
    }

    #[tokio::test]
    async fn test_memory_limit() {
        for memory_limit in [0, 5, 10, 15, 20] {
            let temp_dir = TempDir::new().unwrap();
            let temp_path = temp_dir.path();

            let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
            let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

            let cache_opts = FileCacheOpts {
                memory_limit,
                file_size: 1024 * 1024,
                page_size: 10,
                flush_type: FlushType::Sync,
                stats_enabled: true,
                ..Default::default()
            };

            let mut cache_dir = FileCacheDirectory::new(object_storage, None, cache_opts).unwrap();

            let path = Path::new("test_file");
            let data = (0..100).map(|i| i as u8).collect::<Vec<u8>>();
            cache_dir.async_atomic_write(path, &data).await.unwrap();
            cache_dir.clear_all().unwrap();

            let get_stats = || aggregate_stats(cache_dir.stats().values());

            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read some of a single page
            {
                let read_data = handle.async_read_bytes(6..8).await.unwrap();
                assert_eq!(read_data.len(), 2);
                for i in 0..2 {
                    assert_eq!(read_data[i], 6 + i as u8);
                }
            }

            // Now recreate the handle, so we're not working with the value cached in the handle directly.
            drop(handle);
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read it again
            {
                let read_data = handle.async_read_bytes(4..6).await.unwrap();
                assert_eq!(read_data.len(), 2);
                for i in 0..2 {
                    assert_eq!(read_data[i], 4 + i as u8);
                }

                let stats = get_stats();
                assert_eq!(stats.num_misses, 1);
                match memory_limit {
                    0 | 5 => assert_eq!(stats.num_disk_hits, 1),
                    10 | 15 | 20 => assert_eq!(stats.num_mem_hits, 1),
                    _ => unreachable!(),
                }
            }

            // Read bytes 52-55
            {
                let read_data = handle.async_read_bytes(52..55).await.unwrap();
                assert_eq!(read_data.len(), 3);
                for i in 0..3 {
                    assert_eq!(read_data[i], 52 + i as u8);
                }

                let stats = get_stats();
                assert_eq!(stats.num_misses, 2);
            }

            // Now, read bytes from the first page again
            {
                let read_data = handle.async_read_bytes(1..4).await.unwrap();
                assert_eq!(read_data.len(), 3);
                for i in 0..3 {
                    assert_eq!(read_data[i], 1 + i as u8);
                }

                let stats = get_stats();
                assert_eq!(stats.num_misses, 2);

                match memory_limit {
                    0 | 5 => assert_eq!(stats.num_disk_hits, 2),
                    10 | 15 => {
                        assert_eq!(stats.num_mem_hits, 1);
                        assert_eq!(stats.num_disk_hits, 1)
                    }
                    20 => assert_eq!(stats.num_mem_hits, 2),
                    _ => unreachable!(),
                }
            }

            drop(handle);
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read the same region. It should be a memory hit.
            {
                let read_data = handle.async_read_bytes(2..8).await.unwrap();
                assert_eq!(read_data.len(), 6);
                for i in 0..6 {
                    assert_eq!(read_data[i], 2 + i as u8);
                }

                let stats = get_stats();
                match memory_limit {
                    0 | 5 => assert_eq!(stats.num_disk_hits, 3),
                    10 | 15 => assert_eq!(stats.num_mem_hits, 2),
                    20 => assert_eq!(stats.num_mem_hits, 3),
                    _ => unreachable!(),
                }
            }
        }
    }

    async fn test_disk_limit<F>()
    where
        F: CacheFile<CacheDirectoryKey>,
    {
        for file_size in [0, 5, 10, 15, 20] {
            let temp_dir = TempDir::new().unwrap();
            let temp_path = temp_dir.path();

            let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
            let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

            let cache_opts = FileCacheOpts {
                memory_limit: 0,
                file_size,
                page_size: 10,
                flush_type: FlushType::Sync,
                stats_enabled: true,
                ..Default::default()
            };

            let mut cache_dir = CacheDirectory::<F>::new(object_storage, None, cache_opts).unwrap();

            let path = Path::new("test_file");
            let data = (0..100).map(|i| i as u8).collect::<Vec<u8>>();
            cache_dir.async_atomic_write(path, &data).await.unwrap();
            cache_dir.clear_all().unwrap();

            let get_stats = || aggregate_stats(cache_dir.stats().values());

            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read some of a single page
            {
                let read_data = handle.async_read_bytes(6..8).await.unwrap();
                assert_eq!(read_data.len(), 2);
                for i in 0..2 {
                    assert_eq!(read_data[i], 6 + i as u8);
                }
            }

            // Read it again with the same handle. This will be a mem hit because it uses the last page logic.
            {
                let read_data = handle.async_read_bytes(4..6).await.unwrap();
                assert_eq!(read_data.len(), 2);
                for i in 0..2 {
                    assert_eq!(read_data[i], 4 + i as u8);
                }

                let stats = get_stats();
                assert_eq!(stats.num_mem_hits, 1);
                assert_eq!(stats.num_misses, 1);
            }

            // Now recreate the handle, so we're not working with the cached value.
            drop(handle);
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read it again
            {
                let read_data = handle.async_read_bytes(4..6).await.unwrap();
                assert_eq!(read_data.len(), 2);
                for i in 0..2 {
                    assert_eq!(read_data[i], 4 + i as u8);
                }

                let stats = get_stats();
                match file_size {
                    0 | 5 => {
                        assert_eq!(stats.num_mem_hits, 1);
                        assert_eq!(stats.num_disk_hits, 0);
                        assert_eq!(stats.num_misses, 2);
                    }
                    10 | 15 | 20 => {
                        assert_eq!(stats.num_mem_hits, 1);
                        assert_eq!(stats.num_disk_hits, 1);
                        assert_eq!(stats.num_misses, 1);
                    }
                    _ => unreachable!(),
                }
            }

            // Drop the handle again, so that it's not hanging onto a reference to the buffer.
            drop(handle);
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read bytes 52-55
            {
                let read_data = handle.async_read_bytes(52..55).await.unwrap();
                assert_eq!(read_data.len(), 3);
                for i in 0..3 {
                    assert_eq!(read_data[i], 52 + i as u8);
                }

                let stats = get_stats();
                match file_size {
                    0 | 5 => {
                        assert_eq!(stats.num_misses, 3);
                    }
                    10 | 15 | 20 => {
                        assert_eq!(stats.num_misses, 2);
                    }
                    _ => unreachable!(),
                }
            }

            drop(handle);
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Now, read bytes from the first page again
            {
                let read_data = handle.async_read_bytes(1..4).await.unwrap();
                assert_eq!(read_data.len(), 3);
                for i in 0..3 {
                    assert_eq!(read_data[i], 1 + i as u8);
                }

                let stats = get_stats();

                match file_size {
                    0 | 5 => {
                        assert_eq!(stats.num_misses, 4);
                        assert_eq!(stats.num_disk_hits, 0)
                    }
                    10 | 15 => {
                        // This is a miss, because we wrapped around and cached the second page
                        assert_eq!(stats.num_misses, 3);
                        assert_eq!(stats.num_disk_hits, 1)
                    }
                    20 => {
                        assert_eq!(stats.num_misses, 2);
                        assert_eq!(stats.num_disk_hits, 2)
                    }
                    _ => unreachable!(),
                }
            }

            // Now read from 23-28. This should be a miss for all cases, and for the 20 case,
            // it'll evict the first page.
            {
                let read_data = handle.async_read_bytes(23..28).await.unwrap();
                assert_eq!(read_data.len(), 5);
                for i in 0..5 {
                    assert_eq!(read_data[i], 23 + i as u8);
                }

                let stats = get_stats();
                match file_size {
                    0 | 5 => assert_eq!(stats.num_misses, 5),
                    10 | 15 => assert_eq!(stats.num_misses, 4),
                    20 => assert_eq!(stats.num_misses, 3),
                    _ => unreachable!(),
                }
            }

            // Now read some bytes from the second page again. This should be a hit for the 20
            // byte cache, and a miss for the others.
            {
                let read_data = handle.async_read_bytes(51..56).await.unwrap();
                assert_eq!(read_data.len(), 5);
                for i in 0..5 {
                    assert_eq!(read_data[i], 51 + i as u8);
                }

                let stats = get_stats();
                match file_size {
                    0 | 5 => assert_eq!(stats.num_misses, 6),
                    10 | 15 => assert_eq!(stats.num_misses, 5),
                    20 => {
                        assert_eq!(stats.num_misses, 3);
                        assert_eq!(stats.num_disk_hits, 3);
                    }
                    _ => unreachable!(),
                }
            }
        }
    }

    #[tokio::test]
    async fn test_disk_limit_direct() {
        test_disk_limit::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn test_disk_limit_mmap() {
        test_disk_limit::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    async fn test_eviction_ordering<F: CacheFile<CacheDirectoryKey>>() {
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        let cache_opts = FileCacheOpts {
            memory_limit: 0,
            file_size: 20,
            page_size: 10,
            flush_type: FlushType::Sync,
            stats_enabled: true,
            ..Default::default()
        };

        let mut cache_dir = CacheDirectory::<F>::new(object_storage, None, cache_opts).unwrap();

        let path = Path::new("test_file");
        let data = (0..100).map(|i| i as u8).collect::<Vec<u8>>();
        cache_dir.async_atomic_write(path, &data).await.unwrap();
        cache_dir.clear_all().unwrap();

        let get_stats = || aggregate_stats(cache_dir.stats().values());

        let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

        {
            let read_data = handle.async_read_bytes(6..8).await.unwrap();
            assert_eq!(read_data.len(), 2);
            for i in 0..2 {
                assert_eq!(read_data[i], 6 + i as u8);
            }
        }

        {
            let read_data = handle.async_read_bytes(10..15).await.unwrap();
            assert_eq!(read_data.len(), 5);
            for i in 0..5 {
                assert_eq!(read_data[i], 10 + i as u8);
            }
        }

        // Now, read one more entry, which will evict the 6..8 page
        {
            let read_data = handle.async_read_bytes(31..34).await.unwrap();
            assert_eq!(read_data.len(), 3);
            for i in 0..3 {
                assert_eq!(read_data[i], 31 + i as u8);
            }
        }

        let stats = get_stats();
        let num_misses_before = stats.num_misses;

        // Read 0..2, and make sure it is a cache miss
        {
            let read_data = handle.async_read_bytes(0..2).await.unwrap();
            assert_eq!(read_data.len(), 2);
            for i in 0..2 {
                assert_eq!(read_data[i], i as u8);
            }
        }

        let stats = get_stats();
        assert_eq!(stats.num_misses, num_misses_before + 1);

        let num_misses_before = stats.num_misses;

        // Make sure that 31..34 is still a hit
        {
            let read_data = handle.async_read_bytes(31..34).await.unwrap();
            assert_eq!(read_data.len(), 3);
            for i in 0..3 {
                assert_eq!(read_data[i], 31 + i as u8);
            }
        }

        let stats = get_stats();
        assert_eq!(stats.num_misses, num_misses_before);
    }

    #[tokio::test]
    async fn test_eviction_ordering_direct() {
        test_eviction_ordering::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn test_eviction_ordering_mmap() {
        test_eviction_ordering::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    async fn test_random_access<F: CacheFile<CacheDirectoryKey>>(
        opts: FileCacheOpts,
        file_size: usize,
        parallel: bool,
        iterations: usize,
        leak_buffers: bool,
    ) {
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        let mut cache_dir = CacheDirectory::<F>::new(object_storage, None, opts.clone()).unwrap();

        let path = Path::new("test_file");
        let data = (0..file_size).map(|i| i as u8).collect::<Vec<u8>>();
        cache_dir.async_atomic_write(path, &data).await.unwrap();
        cache_dir.clear_all().unwrap();

        // Affects caching behavior
        let leaked_buffers = Arc::new(Mutex::new(Vec::new()));

        let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();
        let mut futures = Vec::new();
        for _ in 0..iterations {
            let handle = handle.clone();
            let leaked_buffers = leaked_buffers.clone();
            let run_iter = tokio::spawn(async move {
                let within_page = rand::thread_rng().gen_bool(0.8);
                let start = rand::thread_rng().gen_range(0..file_size - 1);
                let mut range = if within_page {
                    start
                        ..start
                            + 1
                            + rand::thread_rng()
                                .gen_range(0..opts.page_size - (start % opts.page_size))
                } else {
                    start..(start + rand::thread_rng().gen_range(1..opts.page_size * 2))
                };
                if range.end > file_size {
                    range.end = file_size;
                }

                let range_len = range.len();
                let read_data = handle.async_read_bytes(range.clone()).await.unwrap();
                assert_eq!(read_data.len(), range_len);
                for i in 0..range_len {
                    assert_eq!(
                        read_data[i],
                        (start + i) as u8,
                        "reading range {:?}",
                        &range
                    );
                }

                if leak_buffers {
                    leaked_buffers
                        .lock()
                        .unwrap()
                        .push((range.clone(), read_data));
                }
            });
            if parallel {
                futures.push(run_iter);
            } else {
                run_iter.await.unwrap();
            }
        }
        join_all(futures)
            .await
            .into_iter()
            .collect::<Result<(), _>>()
            .unwrap();
    }

    async fn test_random_access_serial<F: CacheFile<CacheDirectoryKey>>() {
        for memory_limit in [0, 10, 20, 50, 100, 200] {
            for file_size in [0, 10, 20, 50, 100, 200] {
                for leak_buffers in [false, true] {
                    test_random_access::<F>(
                        FileCacheOpts {
                            memory_limit,
                            file_size,
                            page_size: 10,
                            flush_type: FlushType::Sync,
                            stats_enabled: true,
                            ..Default::default()
                        },
                        100,
                        false,
                        1000,
                        leak_buffers,
                    )
                    .await;
                }
            }
        }
    }

    #[tokio::test]
    async fn test_random_access_serial_direct() {
        test_random_access_serial::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn test_random_access_serial_mmap() {
        test_random_access_serial::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    async fn test_random_access_parallel<F: CacheFile<CacheDirectoryKey>>() {
        for flush_type in [FlushType::Sync, FlushType::Slow, FlushType::Background] {
            for memory_limit in [0, 10, 20, 50, 100, 200] {
                for file_size in [0, 10, 20, 50, 100, 200] {
                    for leak_buffers in [false, true] {
                        test_random_access::<F>(
                            FileCacheOpts {
                                memory_limit,
                                file_size,
                                page_size: 10,
                                flush_type,
                                stats_enabled: true,
                                ..Default::default()
                            },
                            100,
                            true,
                            1000,
                            leak_buffers,
                        )
                        .await;
                    }
                }
            }
        }
    }

    #[tokio::test]
    async fn test_random_access_parallel_direct() {
        test_random_access_parallel::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn test_random_access_parallel_mmap() {
        test_random_access_parallel::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    // Run with:
    //  cargo test --release -- --include-ignored test_random_access_parallel_large --nocapture
    // or to generate a flamegraph:
    //  CARGO_PROFILE_RELEASE_DEBUG=true cargo flamegraph --release --root --unit-test -- test_random_access_parallel_large --include-ignored --nocapture
    #[test]
    #[ignore]
    fn test_random_access_parallel_large() {
        use rand::RngCore;
        use std::time::Instant;

        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create object storage directory
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));
        let cached_dir = AsyncDirectoryArc::new(
            FileCacheDirectory::new(object_storage, None, FileCacheOpts::default()).unwrap(),
        );

        // Create 100MB of random data
        let mut rng = rand::thread_rng();
        let mut data = vec![0u8; 100 * 1024 * 1024];
        rng.fill_bytes(&mut data);

        // Write the data
        let path = Path::new("test_file");
        cached_dir.atomic_write(path, &data).unwrap();

        // Get file handle and measure iteration time
        let handle = cached_dir.get_file_handle(path).unwrap();
        let start = Instant::now();

        // Iterate 2 bytes at a time
        for offset in (0..data.len()).step_by(2) {
            let end = std::cmp::min(offset + 2, data.len());
            let bytes = handle.read_bytes(offset..end).unwrap();
            assert_eq!(&bytes[..], &data[offset..end]);
        }

        let duration = start.elapsed();
        eprintln!(
            "Time to iterate through 100MB file 2 bytes at a time: {:?}",
            duration
        );
    }

    async fn test_traced_redirects_caching<F: CacheFile<CacheDirectoryKey>>() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a TracedRangeDirectory that wraps the ObjectStorage
        let traced_dir = TracedRangeDirectory::new(object_storage.clone());

        // Create test files and read from them to generate traces
        let paths = [Path::new("file1"), Path::new("file2"), Path::new("file3")];
        let contents = [
            b"First file content".to_vec(),
            b"Second file content".to_vec(),
            b"Third file content".to_vec(),
        ];

        // Write files and read from them to generate traces
        for (path, content) in paths.iter().zip(contents.iter()) {
            traced_dir.async_atomic_write(path, content).await.unwrap();
            let handle = traced_dir.async_get_file_handle(path, None).await.unwrap();

            // Read different ranges from each file
            let read_data = handle.async_read_bytes(0..5).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[0..5]);

            let read_data = handle.async_read_bytes(6..11).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[6..11]);
        }

        // Generate redirects
        let redirects = traced_dir.make_traced_redirects().await.unwrap();

        // Create a new cached directory with the redirects
        let cache_opts = FileCacheOpts {
            memory_limit: 1024,          // 1 KB
            file_size: 1024,             // 1 KB
            page_size: 10,               // Small page size to force eviction
            flush_type: FlushType::Sync, // So that the stats are correct
            stats_enabled: true,
            ..Default::default()
        };

        let cache_dir = CacheDirectory::<F>::new(object_storage.clone(), None, cache_opts).unwrap();
        // Load the redirects
        let redirect_path = Path::new("redirects");
        cache_dir
            .async_atomic_write(redirect_path, &redirects)
            .await
            .unwrap();
        cache_dir.load_redirects(redirect_path).await.unwrap();

        // Read the same ranges again - these should be hits
        for (path, content) in paths.iter().zip(contents.iter()) {
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read the same ranges
            let read_data = handle.async_read_bytes(0..5).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[0..5]);

            let read_data = handle.async_read_bytes(6..11).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[6..11]);
        }

        // Verify we have hits
        let stats = cache_dir.stats();
        let total_hits: i64 = stats
            .values()
            .map(|s| s.num_mem_hits + s.num_disk_hits)
            .sum();
        assert!(total_hits > 0);

        // The only miss should be the redirect file
        for (fname, stats) in stats.iter() {
            // This is a hack, because the stats are a stringified version of the key
            if fname.contains(redirect_path.to_str().unwrap()) {
                assert_eq!(stats.num_misses, 1);
            } else {
                assert_eq!(stats.num_misses, 0);
            }
        }

        // Now read enough data to evict the cached pages
        let eviction_data = vec![0u8; 1024]; // 1KB of data
        let eviction_path = Path::new("eviction_file");
        cache_dir
            .async_atomic_write(eviction_path, &eviction_data)
            .await
            .unwrap();
        let handle = cache_dir
            .async_get_file_handle(eviction_path, None)
            .await
            .unwrap();

        // Read the entire file to force eviction
        let _ = handle
            .async_read_bytes(0..eviction_data.len())
            .await
            .unwrap();

        let misses_before = stats.values().map(|s| s.num_misses).sum();

        // Read the original ranges again - these should be misses now
        for (path, content) in paths.iter().zip(contents.iter()) {
            let handle = cache_dir.async_get_file_handle(path, None).await.unwrap();

            // Read the same ranges
            let read_data = handle.async_read_bytes(0..5).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[0..5]);

            let read_data = handle.async_read_bytes(6..11).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[6..11]);
        }

        // Verify we have misses
        let stats = cache_dir.stats();
        let total_misses: i64 = stats.values().map(|s| s.num_misses).sum();
        assert!(total_misses > misses_before);
    }

    #[tokio::test]
    async fn test_traced_redirects_caching_direct() {
        test_traced_redirects_caching::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn test_traced_redirects_caching_mmap() {
        test_traced_redirects_caching::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    async fn test_partial_write_handling<F: CacheFile<CacheDirectoryKey>>() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create object storage directory. Set a nonzero partial write rate to
        // exercise the partial write checking code.
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));
        let cached_dir = AsyncDirectoryArc::new(
            CacheDirectory::<F>::new(
                object_storage,
                None,
                FileCacheOpts {
                    // Ensures that subsequent reads will read what we wrote to disk
                    // rather than what we cached in memory.
                    memory_limit: 0,
                    flush_type: FlushType::Sync,
                    testing_opts: CacheFileTestingOptions {
                        async_write_at_partial_write_rate: 0.5,
                    },
                    ..Default::default()
                },
            )
            .unwrap(),
        );

        let _result = join_all((0..10).map(|_| async {
            let uuid = util::uuid::Uuid::new_v4();
            let path = format!("{}.txt", uuid);
            let content = format!("Hello world {}", uuid);
            let content_bytes = content.as_bytes();

            // Write the content
            cached_dir
                .async_atomic_write(Path::new(&path), content_bytes)
                .await
                .unwrap();

            // Read and verify
            let read_handle = cached_dir
                .async_get_file_handle(Path::new(&path), None)
                .await
                .unwrap();
            let read_data = read_handle
                .async_read_bytes(0..content_bytes.len())
                .await
                .unwrap();
            assert_eq!(read_data.as_ref(), content_bytes);
        }))
        .await;
    }

    #[tokio::test]
    async fn test_partial_write_handling_direct() {
        test_partial_write_handling::<DirectCacheFile<CacheDirectoryKey>>().await;
    }

    #[tokio::test]
    async fn test_partial_write_handling_mmap() {
        test_partial_write_handling::<MmapCacheFile<CacheDirectoryKey>>().await;
    }

    #[test]
    fn test_retain_performance_with_many_entries() {
        use std::time::{Duration, Instant};

        // Test actual evictions with a simple setup
        let file_size = 1024 * 1024;
        let block_size = 1;
        let mut meta = CacheMeta::<CacheDirectoryKey>::new(file_size, file_size, false);

        println!("Testing with {}B cache file", file_size);

        // Populate the cache with 1byte entries
        let start = Instant::now();
        for i in 0..file_size {
            let key = CacheDirectoryKey(PathBuf::from(format!("evict_test_{}", i)), 0..block_size);
            let buf = Arc::new(OwnedBytes::new(vec![99u8; block_size]));

            let cache_entry = meta.allocate_chunk(key, &buf).unwrap();
            meta.actively_writing.remove(&cache_entry.id);
        }

        eprintln!("Populated cache in {:?}", start.elapsed());

        let big_block_size = block_size * 10;

        // Now read several 1 byte entries and see how long it takes
        let mut eviction_times = Vec::new();
        for i in 0..1000 {
            let key = CacheDirectoryKey(
                PathBuf::from(format!("evict_test_2_{}", i)),
                0..big_block_size,
            );
            let buf = Arc::new(OwnedBytes::new(vec![99u8; big_block_size]));
            let start = Instant::now();
            let _result = meta.allocate_chunk(key, &buf);
            let elapsed = start.elapsed();
            eviction_times.push(elapsed);
        }

        let total: Duration = eviction_times.iter().sum();
        let avg = total / (eviction_times.len() as u32);
        eprintln!(
            "Total time {:?}. Average time to cache with eviction (1 byte) after {} entries: {:?}",
            total, file_size, avg
        );
    }
}
