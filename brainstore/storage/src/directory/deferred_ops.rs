use crate::static_sync_runtime::STATIC_SYNC_RUNTIME;

use tantivy::directory::{INDEX_WRITER_LOCK, META_LOCK};

use super::AsyncDirectoryArc;

#[derive(Debug, Clone)]
pub struct DeferredOpsDirectory(pub AsyncDirectoryArc);

const ALLOW_DEFERRED_WRITE: [&str; 1] = [".managed.json"];

impl tantivy::directory::Directory for DeferredOpsDirectory {
    fn get_file_handle(
        &self,
        path: &std::path::Path,
    ) -> Result<
        std::sync::Arc<dyn tantivy::directory::FileHandle>,
        tantivy::directory::error::OpenReadError,
    > {
        self.0.get_file_handle(path)
    }

    fn delete(
        &self,
        _path: &std::path::Path,
    ) -> Result<(), tantivy::directory::error::DeleteError> {
        // Don't delete files. We will garbage collect them later once we can prove they are no
        // longer being read.
        //
        // // OLD: Delete the file in the background. Although we're supposed to return an error if the
        // // file does not exist, in practice, it does not seem to matter.
        // let inner = self.0.clone();
        // let path = path.to_path_buf();
        // // For fire-and-forget tasks, we don't need the timeout.
        // STATIC_SYNC_RUNTIME.spawn_blocking(move || {
        //     let result = inner.delete(&path);
        //     if let Err(e) = result {
        //         log::warn!("Error deleting file: {:?}", e);
        //     }
        // });
        Ok(())
    }

    fn exists(
        &self,
        path: &std::path::Path,
    ) -> Result<bool, tantivy::directory::error::OpenReadError> {
        self.0.exists(path)
    }

    fn open_write(
        &self,
        path: &std::path::Path,
    ) -> Result<tantivy::directory::WritePtr, tantivy::directory::error::OpenWriteError> {
        self.0.open_write(path)
    }

    fn atomic_read(
        &self,
        path: &std::path::Path,
    ) -> Result<Vec<u8>, tantivy::directory::error::OpenReadError> {
        self.0.atomic_read(path)
    }

    fn atomic_write(&self, path: &std::path::Path, data: &[u8]) -> std::io::Result<()> {
        let file_name = path.file_name().unwrap().to_str().unwrap();
        if ALLOW_DEFERRED_WRITE.contains(&file_name) {
            let inner = self.0.clone();
            let copy_path = path.to_path_buf();
            let copy_data = data.to_vec();
            let span = tracing::Span::current();
            // For fire-and-forget tasks, we don't need the timeout.
            STATIC_SYNC_RUNTIME.spawn_blocking(move || {
                let _guard = span.enter();
                let result = inner.atomic_write(&copy_path, &copy_data);
                if let Err(e) = result {
                    log::warn!("Error writing file: {:?}", e);
                }
            });
            Ok(())
        } else {
            self.0.atomic_write(path, data)
        }
    }

    fn sync_directory(&self) -> std::io::Result<()> {
        self.0.sync_directory()
    }

    fn acquire_lock(
        &self,
        lock: &tantivy::directory::Lock,
    ) -> Result<tantivy::directory::DirectoryLock, tantivy::directory::error::LockError> {
        // Skip over the META_LOCK and INDEX_WRITER_LOCK. There shouldn't be any other locks, but
        // if we do find one, we log a warning and acquire it.
        if std::ptr::eq(&*META_LOCK, lock) || std::ptr::eq(&*INDEX_WRITER_LOCK, lock) {
            Ok(tantivy::directory::DirectoryLock::from(Box::new(())))
        } else {
            log::warn!("Acquiring tantivy lock: {:?}", lock);
            self.0.acquire_lock(lock)
        }
    }

    fn watch(
        &self,
        watch_callback: tantivy::directory::WatchCallback,
    ) -> tantivy::Result<tantivy::directory::WatchHandle> {
        self.0.watch(watch_callback)
    }
}
