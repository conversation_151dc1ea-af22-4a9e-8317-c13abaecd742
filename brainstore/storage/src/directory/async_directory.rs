use futures::stream::BoxStream;
use std::{
    fmt, io,
    ops::Range,
    path::{Path, PathBuf},
    pin::Pin,
    sync::Arc,
};

use async_trait::async_trait;
use tantivy::common::OwnedBytes;
use tantivy::{
    directory::{
        error::{DeleteError, OpenReadError, OpenWriteError},
        FileHandle,
    },
    HasLen,
};
use tracing::Instrument;

use crate::{
    instrumented::{AsDynInstrumented, Instrumented},
    static_sync_runtime::STATIC_SYNC_RUNTIME,
};

/// An async directory has exactly the same interface as a normal directory, but all operations
/// are executed asynchronously. Any async directory also automatically gets a normal directory,
/// since we just wrap the normal directory functions with spawn_blocking commands on the
/// STATIC_SYNC_RUNTIME.
#[async_trait]
pub trait AsyncDirectory:
    Instrumented + AsDynInstrumented + fmt::Debug + Send + Sync + 'static
{
    async fn async_get_file_handle(
        &self,
        path: &Path,
        len: Option<u64>,
    ) -> Result<Arc<dyn AsyncFileHandle>, OpenReadError>;

    async fn async_delete(&self, path: &Path) -> Result<(), DeleteError>;

    async fn async_exists(&self, path: &Path) -> Result<bool, OpenReadError>;

    async fn async_open_write(
        &self,
        path: &Path,
    ) -> Result<PinnedAsyncWriteHandler, OpenWriteError>;

    async fn async_atomic_read(&self, path: &Path) -> Result<Vec<u8>, OpenReadError>;

    async fn async_atomic_write(&self, path: &Path, data: &[u8]) -> io::Result<()>;

    async fn async_sync_directory(&self) -> io::Result<()>;

    fn delete_stream<'a>(
        &'a self,
        locations: BoxStream<'a, Result<PathBuf, object_store::Error>>,
    ) -> BoxStream<'a, Result<object_store::path::Path, object_store::Error>>;

    // This function is really only implemented by the cached directory, and it reads a traced redirect
    // file from the specified path, which it can use to avoid cold reads on those byte ranges in the future.
    async fn load_redirects(&self, path: &Path) -> util::anyhow::Result<bool>;
}

#[async_trait]
pub trait AsyncFileHandle: 'static + Send + Sync + HasLen + fmt::Debug {
    async fn async_read_bytes(&self, _byte_range: Range<usize>) -> io::Result<OwnedBytes>;

    // Certain implementations may want to override this in case they can return bytes without falling
    // back to async operations (e.g. check if the bytes are in memory).
    fn sync_read_bytes(&self, byte_range: Range<usize>) -> io::Result<OwnedBytes> {
        STATIC_SYNC_RUNTIME.block_on(self.async_read_bytes(byte_range))
    }
}

// This wrapper allows us to do a blanket implementation (below) for any
// T: AsyncFileHandle.
pub struct AsyncFileHandleWrapper(Arc<dyn AsyncFileHandle>);

impl AsyncFileHandleWrapper {
    pub fn new(handle: Arc<dyn AsyncFileHandle>) -> Self {
        Self(handle)
    }
}

impl std::fmt::Debug for AsyncFileHandleWrapper {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        std::fmt::Debug::fmt(&self.0, f)
    }
}

impl std::ops::Deref for AsyncFileHandleWrapper {
    type Target = Arc<dyn AsyncFileHandle>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

#[async_trait]
impl AsyncFileHandle for AsyncFileHandleWrapper {
    async fn async_read_bytes(&self, byte_range: Range<usize>) -> io::Result<OwnedBytes> {
        self.0.async_read_bytes(byte_range).await
    }

    fn sync_read_bytes(&self, byte_range: Range<usize>) -> io::Result<OwnedBytes> {
        self.0.sync_read_bytes(byte_range)
    }
}

impl HasLen for AsyncFileHandleWrapper {
    fn len(&self) -> usize {
        self.0.len()
    }
}

#[async_trait]
impl FileHandle for AsyncFileHandleWrapper {
    fn read_bytes(&self, _byte_range: Range<usize>) -> io::Result<OwnedBytes> {
        self.sync_read_bytes(_byte_range)
    }

    async fn read_bytes_async(&self, _byte_range: Range<usize>) -> io::Result<OwnedBytes> {
        self.async_read_bytes(_byte_range).await
    }
}

#[async_trait]
pub trait AsyncWriteHandler: Send + Sync {
    async fn write<'a>(self: std::pin::Pin<&'a mut Self>, src: &'a [u8]) -> io::Result<usize>;

    async fn flush(self: std::pin::Pin<&mut Self>) -> io::Result<()>;

    async fn shutdown(self: std::pin::Pin<&mut Self>) -> io::Result<()>;
}

pub type PinnedAsyncWriteHandler = Pin<Box<dyn AsyncWriteHandler>>;

pub struct AsyncWriteWrapper {
    pub writer: PinnedAsyncWriteHandler,
    pub is_shutdown: bool,
}

impl AsyncWriteWrapper {
    pub fn new(writer: PinnedAsyncWriteHandler) -> Self {
        Self {
            writer,
            is_shutdown: false,
        }
    }
}

impl std::ops::Deref for AsyncWriteWrapper {
    type Target = PinnedAsyncWriteHandler;

    fn deref(&self) -> &Self::Target {
        &self.writer
    }
}

impl std::ops::DerefMut for AsyncWriteWrapper {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.writer
    }
}

#[async_trait]
impl AsyncWriteHandler for AsyncWriteWrapper {
    async fn write<'a>(mut self: std::pin::Pin<&'a mut Self>, src: &'a [u8]) -> io::Result<usize> {
        self.writer.as_mut().write(src).await
    }

    async fn flush(mut self: std::pin::Pin<&mut Self>) -> io::Result<()> {
        self.writer.as_mut().flush().await
    }

    async fn shutdown(mut self: std::pin::Pin<&mut Self>) -> io::Result<()> {
        self.writer.as_mut().shutdown().await
    }
}

impl std::io::Write for AsyncWriteWrapper {
    fn write(&mut self, buf: &[u8]) -> std::io::Result<usize> {
        let buf = bytes::Bytes::from(buf.to_vec());

        let write_future = self.writer.as_mut().write(&buf);

        // Block on the future
        let len = STATIC_SYNC_RUNTIME
            .block_on(write_future.instrument(tracing::Span::current()))
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;

        Ok(len)
    }

    fn flush(&mut self) -> std::io::Result<()> {
        let flush_future = self.writer.as_mut().flush();
        STATIC_SYNC_RUNTIME
            .block_on(flush_future.instrument(tracing::Span::current()))
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;
        Ok(())
    }
}

// According to https://docs.rs/tantivy/latest/tantivy/directory/trait.Directory.html#tymethod.open_write,
// you do not need to rely on Drop.
impl tantivy::directory::TerminatingWrite for AsyncWriteWrapper {
    fn terminate_ref(
        &mut self,
        _: tantivy::directory::AntiCallToken,
    ) -> Result<(), std::io::Error> {
        if self.is_shutdown {
            return Ok(());
        }
        let shutdown_future = self.writer.as_mut().shutdown();
        STATIC_SYNC_RUNTIME
            .block_on(shutdown_future.instrument(tracing::Span::current()))
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;
        self.is_shutdown = true;
        Ok(())
    }
}

#[derive(Clone)]
pub struct AsyncDirectoryArc(Arc<dyn AsyncDirectory>);

impl AsyncDirectoryArc {
    pub fn new<T: AsyncDirectory>(directory: T) -> Self {
        Self(Arc::new(directory))
    }

    pub fn get_inner(&self) -> &dyn AsyncDirectory {
        self.0.as_ref()
    }
}

impl AsRef<dyn AsyncDirectory> for AsyncDirectoryArc {
    fn as_ref(&self) -> &dyn AsyncDirectory {
        self.0.as_ref()
    }
}

impl std::ops::Deref for AsyncDirectoryArc {
    type Target = Arc<dyn AsyncDirectory>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl std::ops::DerefMut for AsyncDirectoryArc {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

impl fmt::Debug for AsyncDirectoryArc {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        std::fmt::Debug::fmt(&self.0, f)
    }
}

/// IMPORTANT: The tantivy interface is synchronous and blocking, so running tantivy operations
/// inside async code is tricky because we don't want to block the main event loop. In fact, Rust
/// will panic if we try to run AsyncDirectory operations inside an existing async context, because
/// these operations run `STATIC_SYNC_RUNTIME.block_on`. To work around this, any async code which
/// runs tantivy operations involving an AsyncDirectory instance will need to wrap that code in a
/// `spawn_blocking_with_async_timeout(&Handle::current(), ...)`, which runs the synchronous code
/// in a separate thread pool that doesn't block the main event loop. This is desirable from a
/// performance standpoint and also stops Rust from panicking.
impl tantivy::directory::Directory for AsyncDirectoryArc {
    fn get_file_handle(
        &self,
        path: &Path,
    ) -> Result<Arc<dyn tantivy::directory::FileHandle>, tantivy::directory::error::OpenReadError>
    {
        let async_file_handle = STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_get_file_handle(path, None)
                .instrument(tracing::Span::current()),
        )?;
        Ok(Arc::new(AsyncFileHandleWrapper::new(async_file_handle)))
    }

    fn delete(&self, path: &Path) -> Result<(), DeleteError> {
        STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_delete(path)
                .instrument(tracing::Span::current()),
        )
    }

    fn exists(&self, path: &Path) -> Result<bool, OpenReadError> {
        STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_exists(path)
                .instrument(tracing::Span::current()),
        )
    }

    fn open_write(&self, path: &Path) -> Result<tantivy::directory::WritePtr, OpenWriteError> {
        let async_write_wrapper = STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_open_write(path)
                .instrument(tracing::Span::current()),
        )?;
        Ok(std::io::BufWriter::new(Box::new(AsyncWriteWrapper::new(
            async_write_wrapper,
        ))))
    }

    fn atomic_read(&self, path: &Path) -> Result<Vec<u8>, OpenReadError> {
        STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_atomic_read(path)
                .instrument(tracing::Span::current()),
        )
    }

    fn atomic_write(&self, path: &Path, data: &[u8]) -> io::Result<()> {
        STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_atomic_write(path, data)
                .instrument(tracing::Span::current()),
        )
    }

    fn sync_directory(&self) -> io::Result<()> {
        // Async directories do not support sync_directory
        STATIC_SYNC_RUNTIME.block_on(
            self.0
                .async_sync_directory()
                .instrument(tracing::Span::current()),
        )
    }

    fn watch(
        &self,
        _watch_callback: tantivy::directory::WatchCallback,
    ) -> tantivy::Result<tantivy::directory::WatchHandle> {
        // NOTE: This method allows us to do watched (event-driven) updates to the metastore, which
        // will be useful once we properly implement it. We should make this use pg notify once we
        // implement that.
        // https://docs.rs/tantivy/latest/tantivy/directory/trait.Directory.html#tymethod.watch
        Ok(tantivy::directory::WatchHandle::empty())
    }
}

impl Instrumented for AsyncDirectoryArc {
    fn enable_timing(&self) {
        self.0.enable_timing();
    }

    fn reset_timing(&self) {
        self.0.reset_timing();
    }

    fn timers(&self) -> Vec<Arc<crate::timer::TimerManager>> {
        self.0.timers()
    }
}
