use std::sync::Arc;

use tantivy::directory::{MmapDirectory, RamDirectory};

use crate::{instrumented::Instrumented, timer::TimerManager};

impl Instrumented for MmapDirectory {
    fn enable_timing(&self) {}

    fn reset_timing(&self) {}

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        vec![]
    }
}

impl Instrumented for RamDirectory {
    fn enable_timing(&self) {}

    fn reset_timing(&self) {}

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        vec![]
    }
}
