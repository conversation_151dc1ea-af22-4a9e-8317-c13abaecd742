use std::{collections::HashMap, sync::Arc};

use crate::{
    config_with_store::StoreInfo, global_store::GlobalStore, tantivy_index::IndexMetaJson,
    tantivy_index_wrapper::ReadonlyTantivyIndexWrapper,
};
use util::{anyhow::Result, uuid::Uuid};

/// Makes a readonly tantivy index wrapper using segment index metas from the global store.
/// This is generally the correct behavior and aligns with what IndexWalReader does, so it's
/// a good idea to use this in unit tests if you need to read data from tantivy.
pub async fn make_readonly_tantivy_index_wrapper(
    global_store: Arc<dyn GlobalStore>,
    index_store: &StoreInfo,
    full_schema: util::schema::Schema,
    segment_ids: &[Uuid],
) -> Result<ReadonlyTantivyIndexWrapper> {
    let segment_metadatas = global_store.query_segment_metadatas(segment_ids).await?;
    let segment_id_to_index_meta: HashMap<Uuid, IndexMetaJson> = segment_ids
        .into_iter()
        .zip(segment_metadatas.into_iter())
        .filter_map(|(segment_id, meta)| {
            meta.last_compacted_index_meta
                .map(|meta| (*segment_id, meta.tantivy_meta))
        })
        .collect();

    ReadonlyTantivyIndexWrapper::new(
        index_store.directory.clone(),
        full_schema,
        &index_store.prefix,
        segment_id_to_index_meta,
    )
    .await
}
