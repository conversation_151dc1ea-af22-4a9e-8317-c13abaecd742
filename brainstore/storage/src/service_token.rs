use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Key, Nonce,
};
use base64::{engine::general_purpose::STANDARD as BASE64, Engine};
use sha2::{Digest, Sha256};

use serde::Deserialize;
use util::anyhow::{anyhow, Result};

use crate::postgres_pool::PostgresPool;

#[derive(Deserialize, Debug)]
struct EncryptedMessage {
    iv: String,
    data: String,
}

fn get_service_token_secret_key() -> Result<String> {
    let secret_key = std::env::var("SERVICE_TOKEN_SECRET_KEY")
        .or_else(|_| std::env::var("FUNCTION_SECRET_KEY"))
        .map_err(|_| anyhow!("Neither SERVICE_TOKEN_SECRET_KEY nor FUNCTION_SECRET_KEY is set"))?;

    let mut hasher = Sha256::new();
    hasher.update(secret_key.as_bytes());
    let result = hasher.finalize();
    Ok(BASE64.encode(result))
}

/// Decrypt a message using AES-GCM with the given key, IV, and encrypted data.
fn decrypt_message(key_string: &str, iv: &str, message: &str) -> Result<String> {
    let key_bytes = BASE64
        .decode(key_string)
        .map_err(|e| anyhow!("Failed to decode key: {}", e))?;

    let iv_bytes = BASE64
        .decode(iv)
        .map_err(|e| anyhow!("Failed to decode IV: {}", e))?;

    let encrypted_bytes = BASE64
        .decode(message)
        .map_err(|e| anyhow!("Failed to decode encrypted message: {}", e))?;

    if iv_bytes.len() != 12 {
        return Err(anyhow!(
            "IV must be exactly 12 bytes, got {}",
            iv_bytes.len()
        ));
    }

    let key = Key::<Aes256Gcm>::from_slice(&key_bytes);
    let nonce = Nonce::from_slice(&iv_bytes);
    let cipher = Aes256Gcm::new(key);
    let decrypted_bytes = cipher
        .decrypt(nonce, encrypted_bytes.as_ref())
        .map_err(|e| anyhow!("Failed to decrypt message: {}", e))?;

    String::from_utf8(decrypted_bytes)
        .map_err(|e| anyhow!("Failed to convert decrypted bytes to string: {}", e))
}

pub async fn query_service_token(pg_conn: &PostgresPool, name: &str) -> Result<Option<String>> {
    let query = r#"
        SELECT
            service_token_encrypted
        FROM
            service_tokens
        WHERE
            name = $1
    "#;

    let client = pg_conn.get_client().await?;
    let rows = client.query(query, &[&name]).await?;
    if rows.len() > 1 {
        return Err(anyhow!(
            "Expected at most one row for service token lookup, got {}",
            rows.len()
        ));
    }
    if rows.is_empty() {
        return Ok(None);
    }

    let row = &rows[0];
    let encrypted_token_json: String = row.get(0);
    let encrypted_message: EncryptedMessage = serde_json::from_str(&encrypted_token_json)
        .map_err(|e| anyhow!("Failed to parse encrypted token JSON: {}", e))?;

    let secret_key = get_service_token_secret_key()?;
    let decrypted_token =
        decrypt_message(&secret_key, &encrypted_message.iv, &encrypted_message.data)?;

    Ok(Some(decrypted_token))
}
