use std::collections::HashMap;

use tantivy::{DocAddress, TantivyDocument};

pub fn read_all_documents(reader: &tantivy::IndexReader) -> HashMap<DocAddress, TantivyDocument> {
    let mut docs: HashMap<DocAddress, TantivyDocument> = HashMap::new();
    let searcher = reader.searcher();
    let collector = tantivy::collector::DocSetCollector {};
    let addresses = searcher
        .search(&tantivy::query::AllQuery {}, &collector)
        .unwrap();
    for doc_address in addresses {
        let doc: tantivy::schema::document::TantivyDocument = searcher.doc(doc_address).unwrap();
        docs.insert(doc_address, doc);
    }
    docs
}
