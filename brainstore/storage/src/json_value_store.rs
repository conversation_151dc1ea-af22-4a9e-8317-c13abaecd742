use std::path::Path;
use util::{
    anyhow::Result,
    serde_json::{self, Value},
};

use crate::directory::async_directory::AsyncDirectory;

/// Read a JSON value from an ObjectStore. Returns None if the file at the path does not exist.
pub async fn read_json_value<T: AsyncDirectory + ?Sized>(
    directory: &T,
    path: &Path,
) -> Result<Option<Value>> {
    let read_result = directory.async_atomic_read(path).await;
    match read_result {
        Ok(bytes) => Ok(Some(serde_json::from_slice::<Value>(&bytes)?)),
        Err(e) => match e {
            tantivy::directory::error::OpenReadError::FileDoesNotExist(_) => Ok(None),
            _ => Err(e.into()),
        },
    }
}

/// Write a JSON value to an ObjectStore.
pub async fn write_json_value<T: AsyncDirectory + ?Sized>(
    directory: &T,
    path: &Path,
    value: &Value,
) -> Result<()> {
    let bytes = serde_json::to_vec(value)?;
    directory.async_atomic_write(path, &bytes).await?;
    Ok(())
}
