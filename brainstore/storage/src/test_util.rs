use std::{collections::HashMap, str::FromStr, sync::Arc};

use async_util::single_item_stream::SingleItemStream;
use futures::stream::BoxStream;
use tempfile::TempDir;
use testcontainers::{ContainerAsync, GenericImage, ImageExt};
use testcontainers_modules::{
    postgres::{self, Postgres},
    redis::{self, Redis},
    testcontainers::runners::AsyncRun<PERSON>,
};
use util::{
    anyhow::{anyhow, Context, Result},
    config::StorageConfig,
    system_types::FullRowIdOwned,
    url::Url,
    xact::TransactionId,
};

use crate::{
    config_with_store::{ConfigWithStore, ConfigWithStoreOpts, StoreInfo},
    directory::cached_directory::FileCacheOpts,
    index_document::IndexDocument,
    resource_limited_object_store::ResourceLimitedObjectStore,
    wal::{AsDynWalMetadata, WalData, WalDataDetail, WalEntryVariant},
    wal_entry::WalEntry,
};

pub struct TmpDirStore {
    pub tmp_dir: TempDir,
    pub store_info: StoreInfo,
}

impl TmpDirStore {
    pub fn new() -> Self {
        let tmp_dir = TempDir::new().unwrap();
        let temp_path = tmp_dir.path().to_owned();
        let url = Url::from_directory_path(temp_path).unwrap();
        let store_info = StoreInfo::new(&url, FileCacheOpts::default()).unwrap();
        TmpDirStore {
            tmp_dir,
            store_info,
        }
    }
}

pub struct TmpDirConfigWithStore {
    pub tmp_dir: TempDir,
    pub storage_config: StorageConfig,
    pub config: ConfigWithStore,
}

impl TmpDirConfigWithStore {
    pub fn new() -> Self {
        TmpDirConfigWithStore::new_with_file_cache_opts(Default::default())
    }

    pub fn new_with_file_cache_opts(file_cache_opts: FileCacheOpts) -> Self {
        let tmp_dir = TempDir::new().unwrap();
        let temp_path = tmp_dir.path().to_owned();
        let wal_uri = Url::from_file_path(temp_path.join("wal")).unwrap();
        let config = StorageConfig {
            metadata_uri: Url::from_file_path(temp_path.join("metadata")).unwrap(),
            wal_uri: wal_uri.clone(),
            realtime_wal_uri: Some(wal_uri),
            index_uri: Url::from_file_path(temp_path.join("index")).unwrap(),
            locks_uri: Url::from_str("memory://").unwrap(),
            locks_manager_enable_bookkeeping: false,
            xact_manager_uri: Url::from_str("memory://").unwrap(),
        };
        eprintln!("Created tmp dir: {:?}", temp_path);
        TmpDirConfigWithStore {
            tmp_dir,
            storage_config: config.clone(),
            config: ConfigWithStore::from_config(
                config,
                ConfigWithStoreOpts {
                    file_cache_opts,
                    ..Default::default()
                },
            )
            .unwrap(),
        }
    }
}

pub struct PostgresContainer {
    pub _instance: ContainerAsync<Postgres>,
    pub connection_url: Url,
}

impl PostgresContainer {
    pub async fn new() -> Self {
        // Initialize test environment (including rustls)
        #[cfg(test)]
        crate::test_init::test_init::init();

        let instance = start_container_with_retry(|| {
            postgres::Postgres::default().with_tag("15-alpine").start()
        })
        .await
        .unwrap();
        let connection_url: Url = format!(
            "postgres://postgres:postgres@{}:{}/postgres",
            instance.get_host().await.unwrap(),
            instance.get_host_port_ipv4(5432).await.unwrap()
        )
        .parse()
        .unwrap();
        eprintln!("Initialized postgres container at {}", connection_url);
        Self {
            _instance: instance,
            connection_url,
        }
    }

    pub async fn run_migration(&self, migration: &str) -> Result<()> {
        let (client, conn) =
            tokio_postgres::connect(self.connection_url.as_str(), tokio_postgres::NoTls).await?;
        tokio::spawn(async move {
            if let Err(e) = conn.await {
                log::error!("postgres connection error: {:?}", e);
            }
        });
        client.batch_execute(migration).await?;
        Ok(())
    }
}

pub struct RedisContainer {
    pub _instance: ContainerAsync<Redis>,
    pub connection_url: Url,
}

impl RedisContainer {
    pub async fn new() -> Self {
        // Initialize test environment (including rustls)
        #[cfg(test)]
        crate::test_init::test_init::init();

        let instance = start_container_with_retry(|| redis::Redis::default().start())
            .await
            .unwrap();
        let connection_url: Url = format!(
            "redis://{}:{}",
            instance.get_host().await.unwrap(),
            instance.get_host_port_ipv4(6379).await.unwrap()
        )
        .parse()
        .unwrap();
        Self {
            _instance: instance,
            connection_url,
        }
    }
}

pub struct MinioContainer {
    pub _instance: ContainerAsync<GenericImage>,
    pub endpoint_url: String,
    pub bucket_name: String,
    pub access_key: String,
    pub secret_key: String,
}

impl MinioContainer {
    pub async fn new() -> Self {
        let bucket_name = "test-bucket".to_string();

        // Use newer MinIO with conditional write support
        let instance = start_container_with_retry(|| async {
            let container = GenericImage::new("minio/minio", "RELEASE.2024-09-13T20-26-02Z")
                .with_exposed_port(9000.into())
                .with_env_var("MINIO_ROOT_USER", "minioadmin")
                .with_env_var("MINIO_ROOT_PASSWORD", "minioadmin")
                .with_cmd(vec!["server", "/data", "--console-address", ":9001"])
                .start()
                .await?;

            // Wait for MinIO to be ready by checking if port is accessible
            let host = container.get_host().await?;
            let port = container.get_host_port_ipv4(9000).await?;

            // Poll until MinIO is ready
            for i in 0..60 {
                match tokio::net::TcpStream::connect(format!("{}:{}", host, port)).await {
                    Ok(_) => {
                        // Extra wait to ensure MinIO is fully initialized
                        tokio::time::sleep(std::time::Duration::from_secs(2)).await;
                        return Ok(container);
                    }
                    Err(_) if i < 59 => {
                        tokio::time::sleep(std::time::Duration::from_millis(500)).await;
                    }
                    Err(e) => return Err(e.into()),
                }
            }

            Err(std::io::Error::new(
                std::io::ErrorKind::TimedOut,
                "MinIO failed to start within 30 seconds",
            )
            .into())
        })
        .await
        .unwrap();

        let host = instance.get_host().await.unwrap();
        let port = instance.get_host_port_ipv4(9000).await.unwrap();
        let endpoint_url = format!("http://{}:{}", host, port);

        // Default MinIO credentials
        let access_key = "minioadmin".to_string();
        let secret_key = "minioadmin".to_string();

        let container = Self {
            _instance: instance,
            endpoint_url,
            bucket_name,
            access_key,
            secret_key,
        };

        // Create the bucket after container is ready
        container.ensure_bucket_exists_no_retry().await.unwrap();

        // Set AWS environment variables for object_store
        std::env::set_var("AWS_ACCESS_KEY_ID", &container.access_key);
        std::env::set_var("AWS_SECRET_ACCESS_KEY", &container.secret_key);
        std::env::set_var("AWS_ENDPOINT", &container.endpoint_url);
        std::env::set_var("AWS_ALLOW_HTTP", "true");

        container
    }

    async fn ensure_bucket_exists_no_retry(&self) -> Result<()> {
        use testcontainers::core::ExecCommand;

        // Set up mc alias inside the container
        let setup_alias_cmd = ExecCommand::new(vec![
            "mc",
            "alias",
            "set",
            "local",
            "http://localhost:9000",
            &self.access_key,
            &self.secret_key,
        ]);
        check_exec_result_succeeded(self._instance.exec(setup_alias_cmd).await?)
            .await
            .with_context(|| "mc alias")?;

        // Create the bucket
        let create_bucket_cmd = ExecCommand::new(vec![
            "mc",
            "mb",
            &format!("local/{}", self.bucket_name),
            "--ignore-existing",
        ]);
        check_exec_result_succeeded(self._instance.exec(create_bucket_cmd).await?)
            .await
            .with_context(|| "mc mb")?;

        // Double-check that the bucket really exists.
        let stat_bucket_cmd =
            ExecCommand::new(vec!["mc", "stat", &format!("local/{}", self.bucket_name)]);
        check_exec_result_succeeded(self._instance.exec(stat_bucket_cmd).await?)
            .await
            .with_context(|| "mc stat")?;

        Ok(())
    }

    pub fn create_object_store(
        &self,
    ) -> Result<Arc<dyn object_store::ObjectStore>, object_store::Error> {
        let s3_builder = object_store::aws::AmazonS3Builder::new()
            .with_bucket_name(&self.bucket_name)
            .with_endpoint(&self.endpoint_url)
            .with_access_key_id(&self.access_key)
            .with_secret_access_key(&self.secret_key)
            .with_allow_http(true);

        Ok(Arc::new(ResourceLimitedObjectStore::new(Box::new(
            s3_builder.build()?,
        ))))
    }
}

pub fn group_wal_entries(entries: Vec<WalEntry>) -> Vec<(TransactionId, Vec<WalEntry>)> {
    let mut grouped = HashMap::new();
    for entry in entries {
        grouped
            .entry(entry._xact_id)
            .or_insert_with(Vec::new)
            .push(entry);
    }
    let mut out = grouped.into_iter().collect::<Vec<_>>();
    out.sort_by_key(|(xact_id, _)| *xact_id);
    out
}

pub fn make_xact_wal_entry_variants(
    input: Vec<(TransactionId, Vec<WalEntry>)>,
) -> Vec<(TransactionId, Vec<WalEntryVariant>)> {
    input
        .into_iter()
        .map(|(xact_id, entries)| {
            (
                xact_id,
                entries.into_iter().map(WalEntryVariant::Full).collect(),
            )
        })
        .collect()
}

pub fn sorted_xact_wal_entries(mut wal_entries: Vec<WalEntry>) -> Vec<WalEntry> {
    wal_entries.sort_by(|a, b| (a._xact_id, a.full_row_id()).cmp(&(b._xact_id, b.full_row_id())));
    wal_entries
}

pub fn sorted_wal_stream(
    mut wal_stream: Vec<(TransactionId, Vec<WalEntry>)>,
) -> Vec<(TransactionId, Vec<WalEntry>)> {
    wal_stream.sort_by(|a, b| a.0.cmp(&b.0));
    wal_stream
        .into_iter()
        .map(|(xact_id, wal_entries)| (xact_id, sorted_xact_wal_entries(wal_entries)))
        .collect()
}

pub async fn collect_wal_stream<T: AsDynWalMetadata>(
    mut stream: SingleItemStream<BoxStream<'_, Result<WalData<WalEntry, T>>>>,
) -> Result<Vec<(TransactionId, Vec<WalEntry>)>> {
    let mut all_wal_entries = Vec::new();
    while let Some(item) = stream.next().await {
        all_wal_entries.extend(
            item.item?
                .entries
                .into_iter()
                .map(|e| (e.0, e.1))
                .collect::<Vec<_>>(),
        );
    }

    Ok(sorted_wal_stream(all_wal_entries))
}

#[derive(Default, Debug, Clone)]
pub struct CollectWalStreamOpts {
    pub expect_exhausted_max_num_bytes: Option<bool>,
}

pub async fn collect_wal_stream_with_opts<T: AsDynWalMetadata>(
    mut stream: SingleItemStream<BoxStream<'_, Result<WalData<WalEntry, T>>>>,
    opts: CollectWalStreamOpts,
) -> Result<Vec<(TransactionId, Vec<WalEntry>)>> {
    let mut all_wal_entries = Vec::new();
    let mut actual_exhausted_max_num_bytes = false;
    while let Some(item) = stream.next().await {
        let wal_data = item.item?;
        if wal_data.detail == WalDataDetail::ExhaustedMaxNumBytes {
            actual_exhausted_max_num_bytes = true;
        }
        all_wal_entries.extend(
            wal_data
                .entries
                .into_iter()
                .map(|e| (e.0, e.1))
                .collect::<Vec<_>>(),
        );
    }

    if let Some(expect_exhausted_max_num_bytes) = opts.expect_exhausted_max_num_bytes {
        assert_eq!(
            actual_exhausted_max_num_bytes,
            expect_exhausted_max_num_bytes
        );
    }

    Ok(sorted_wal_stream(all_wal_entries))
}

async fn start_container_with_retry<I, Fut, MakeFut>(
    make_fut: MakeFut,
) -> testcontainers::core::error::Result<ContainerAsync<I>>
where
    I: testcontainers::Image,
    Fut: std::future::Future<Output = testcontainers::core::error::Result<ContainerAsync<I>>>,
    MakeFut: Fn() -> Fut,
{
    let mut last_err = None;
    for i in 0..3 {
        match make_fut().await {
            Ok(container) => return Ok(container),
            Err(err) => {
                eprintln!("failed to start container: {:?}", err);
                last_err = Some(err);
            }
        }
        if i < 2 {
            eprintln!("retrying in 1 second...");
            tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        }
    }
    Err(last_err.unwrap())
}

pub fn make_compacted_wal_entries(
    wal_entries: Vec<WalEntry>,
) -> HashMap<FullRowIdOwned, IndexDocument> {
    let mut out: HashMap<FullRowIdOwned, WalEntry> = HashMap::new();
    for wal_entry in wal_entries {
        out.entry(wal_entry.full_row_id().to_owned())
            .and_modify(|existing_entry| existing_entry.merge(wal_entry.clone()).unwrap())
            .or_insert(wal_entry);
    }
    out.into_iter()
        .map(|(k, v)| (k, IndexDocument { wal_entry: v }.to_sanitized().unwrap()))
        .collect()
}

async fn check_exec_result_succeeded(
    mut exec_result: testcontainers::core::ExecResult,
) -> Result<()> {
    let start_time = std::time::Instant::now();
    loop {
        let exit_code = exec_result.exit_code().await?;
        match exit_code {
            Some(exit_code) => {
                if exit_code == 0 {
                    return Ok(());
                } else {
                    let stdout = String::from_utf8(exec_result.stdout_to_vec().await?)?;
                    let stderr = String::from_utf8(exec_result.stderr_to_vec().await?)?;
                    return Err(anyhow!(
                        "Command failed with exit code: {:?}.\nstdout:\n{}\nstderr:\n{}",
                        exit_code,
                        stdout,
                        stderr
                    ));
                }
            }
            None => {
                if start_time.elapsed().as_secs() > 10 {
                    return Err(anyhow!("Failed to get exit code within 10s"));
                } else {
                    tokio::time::sleep(std::time::Duration::from_millis(100)).await;
                }
            }
        }
    }
}
