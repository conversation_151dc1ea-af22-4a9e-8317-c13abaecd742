use std::collections::HashSet;

use util::{
    anyhow::Result,
    bail,
    schema::{self, BaseOptions, TantivyField, TantivyType, TextOptions},
    serde_json::{self},
    system_types::{PAGINATION_KEY_FIELD_TYPE, STRING_ID_FIELD_TYPE},
};

use crate::{
    tantivy_index::{invert_document, make_document, FieldAdder},
    wal_entry::WalEntry,
};
use tantivy::schema::document::TantivyDocument;

/// Bridge between the in-memory document representation and one which can be converted into a
/// Tantivy document for the index.
#[derive(Default, Debug, Clone, PartialEq, Eq)]
pub struct IndexDocument {
    pub wal_entry: WalEntry,
}

impl IndexDocument {
    pub fn merge_wal_entry(&mut self, from: &WalEntry) -> Result<()> {
        self.wal_entry.merge(from.clone())?;
        Ok(())
    }

    pub fn sanitize(&mut self) -> Result<()> {
        // At the point of indexing, these system control fields are irrelevant, so we blank them
        // out.
        self.wal_entry._is_merge = None;
        self.wal_entry._replace_sticky_system_fields = None;
        self.wal_entry.sanitize();

        if self.wal_entry._object_delete.unwrap_or(false) {
            bail!("Do not index a delete entry");
        }
        if self.wal_entry._is_standalone_comment.unwrap_or(false) {
            // Comments are expected to always attach to some existing row, so they should never be
            // part of the index on their own. In cases where we do find a standalone comment in
            // the WAL that we cannot find a match for, we should ignore it, because either that
            // row will come into the WAL later (at an xact_id <= the comment's xact_id) and get
            // re-indexed, or it is a spurious piece of data.
            bail!("Do not index a standalone comment");
        }

        Ok(())
    }

    pub fn to_sanitized(mut self) -> Result<Self> {
        self.sanitize()?;
        Ok(self)
    }

    pub fn is_root(&self) -> bool {
        self.wal_entry.span_parents.is_empty()
    }

    pub fn from_json_value(mut value: serde_json::Value) -> Result<Self> {
        let value_object = value.as_object_mut().unwrap();

        // Remove derived system fields.
        value_object.remove("_full_row_id");
        value_object.remove("is_root");

        Ok(IndexDocument {
            wal_entry: WalEntry::new(value)?,
        })
    }

    pub fn to_json_value(mut self) -> Result<serde_json::Value> {
        self.sanitize()?;
        let is_root = self.is_root();
        let full_row_id = self.wal_entry.full_row_id().to_string();
        let mut value = self.wal_entry.to_value();
        let value_object = value.as_object_mut().unwrap();
        // Insert derived system fields.
        value_object.insert(
            "_full_row_id".to_string(),
            serde_json::Value::String(full_row_id),
        );
        value_object.insert("is_root".to_string(), serde_json::Value::Bool(is_root));

        Ok(value)
    }

    pub fn from_tantivy_document(
        doc: &TantivyDocument,
        invert_fields: &[Box<dyn FieldAdder>],
    ) -> Result<Self> {
        IndexDocument::from_json_value(invert_document(doc, invert_fields)?)
    }

    pub fn to_tantivy_document(self, fields: &[Box<dyn FieldAdder>]) -> Result<TantivyDocument> {
        make_document(&self.to_json_value()?, fields)
    }
}

// Return a new schema with the system fields added.
pub fn make_full_schema(schema: &schema::Schema) -> Result<schema::Schema> {
    let mut all_fields = Vec::new();

    // First, add each system field. If the user-defined schema has its own
    // varant of it, use that.
    let mut skip_fields = HashSet::new();
    for system_field in SYSTEM_FIELDS.iter() {
        if let Some(field) = schema.fields().iter().find(|f| f.name == system_field.name) {
            // Ignore `field_ts` when making comparisons, since that is almost certainly different
            // between the user-defined field and the system one, and we don't care about that.
            if !field
                .tantivy
                .iter()
                .map(|f| TantivyField {
                    field_ts: 0,
                    ..f.clone()
                })
                .eq(system_field.tantivy.iter().map(|f| TantivyField {
                    field_ts: 0,
                    ..f.clone()
                }))
            {
                bail!(
                    "User-defined {} field has different tantivy definition than system one",
                    field.name
                );
            }

            if !field.tantivy.iter().eq(system_field.tantivy.iter()) {
                log::warn!("Using user-defined {} instead of system one", field.name);
            }
            skip_fields.insert(field.name.clone());
            all_fields.push(field.clone());
        } else {
            all_fields.push(system_field.clone());
        }
    }

    // Then, add the user-defined fields.
    for field in schema.fields().iter() {
        if skip_fields.contains(&field.name) {
            continue;
        }
        all_fields.push(field.clone());
    }
    schema::Schema::new(schema.name().clone(), all_fields, Default::default())
}

// Keep this in sync with to/from_tantivy_document.
fn make_system_fields() -> Vec<schema::Field> {
    vec![
        schema::Field {
            name: "id".to_string(),
            tantivy: vec![TantivyField {
                name: "id".to_string(),
                field_ts: 1736212233535,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: true,
                    fast: true,
                    tokenize: false,
                }),
            }],
        },
        schema::Field {
            name: "created".to_string(),
            tantivy: vec![TantivyField {
                name: "created".to_string(),
                field_ts: 1736212233538,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Date(BaseOptions {
                    stored: true,
                    fast: true,
                }),
            }],
        },
        schema::Field {
            name: "_pagination_key".to_string(),
            tantivy: vec![TantivyField {
                name: "_pagination_key".to_string(),
                field_ts: 1736212233540,
                repeated: false,
                lossy_fast_field: false,
                field_type: PAGINATION_KEY_FIELD_TYPE.clone(),
            }],
        },
        schema::Field {
            name: "_xact_id".to_string(),
            tantivy: vec![TantivyField {
                name: "_xact_id".to_string(),
                field_ts: 1736212233543,
                repeated: false,
                lossy_fast_field: false,
                field_type: PAGINATION_KEY_FIELD_TYPE.clone(),
            }],
        },
        schema::Field {
            name: "_object_type".to_string(),
            tantivy: vec![TantivyField {
                name: "_object_type".to_string(),
                field_ts: 1736212233545,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: true,
                    fast: true,
                    tokenize: false,
                }),
            }],
        },
        schema::Field {
            name: "_object_id".to_string(),
            tantivy: vec![TantivyField {
                name: "_object_id".to_string(),
                field_ts: 1736212233548,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: true,
                    fast: true,
                    tokenize: false,
                }),
            }],
        },
        schema::Field {
            name: "root_span_id".to_string(),
            tantivy: vec![TantivyField {
                name: "root_span_id".to_string(),
                field_ts: 1736212233550,
                repeated: false,
                lossy_fast_field: false,
                field_type: STRING_ID_FIELD_TYPE.clone(),
            }],
        },
        schema::Field {
            name: "span_id".to_string(),
            tantivy: vec![TantivyField {
                name: "span_id".to_string(),
                field_ts: 1736212233553,
                repeated: false,
                lossy_fast_field: false,
                field_type: STRING_ID_FIELD_TYPE.clone(),
            }],
        },
        schema::Field {
            name: "span_parents".to_string(),
            tantivy: vec![TantivyField {
                name: "span_parents".to_string(),
                field_ts: 1736212233555,
                repeated: true,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: true,
                    fast: true,
                    tokenize: false,
                }),
            }],
        },
        schema::Field {
            name: "is_root".to_string(),
            tantivy: vec![TantivyField {
                name: "is_root".to_string(),
                field_ts: 1736212233558,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Bool(BaseOptions {
                    stored: true,
                    fast: true,
                }),
            }],
        },
        schema::Field {
            name: "_full_row_id".to_string(),
            tantivy: vec![TantivyField {
                name: "_full_row_id".to_string(),
                field_ts: 1736212233563,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: true,
                    fast: true,
                    tokenize: false,
                }),
            }],
        },
    ]
}

pub static SYSTEM_FIELDS: once_cell::sync::Lazy<Vec<schema::Field>> =
    once_cell::sync::Lazy::new(make_system_fields);
