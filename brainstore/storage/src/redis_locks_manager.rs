use std::{collections::HashMap, sync::Arc, time::Duration};

use async_trait::async_trait;
use backoff::{future::retry, ExponentialBackoff};
use futures::future::join_all;
use tracing::instrument;
use util::{
    anyhow::{anyhow, Result},
    bail,
    url::Url,
};

use crate::global_locks_manager::{
    GlobalLockReadGuard, GlobalLockWriteGuard, GlobalLocksManager, LockState,
};
use crate::healthcheck_util::validate_redis_connection;

pub const DEFAULT_LOCK_PREFIX: &str = "brainstore_lock_";
pub const DEFAULT_LOCK_TTL_SECONDS: u64 = 60;
const LOCK_MAX_ACQUIRE_WINDOW_SECONDS: u64 = 3600;

#[derive(Debug, Clone)]
pub struct RedisLocksManager {
    lock_manager: rslock::LockManager,
    book_keeper: Option<Arc<redis::Client>>,
    prefix: String,
    lock_ttl: Duration,
    primary_redis_url: Url,
}

impl RedisLocksManager {
    pub fn new(urls: Vec<Url>) -> Result<Self> {
        Self::new_with_opts(
            urls,
            DEFAULT_LOCK_PREFIX.to_string(),
            Duration::from_secs(DEFAULT_LOCK_TTL_SECONDS),
            false,
        )
    }

    pub fn new_with_opts(
        urls: Vec<Url>,
        prefix: String,
        lock_ttl: Duration,
        book_keep: bool,
    ) -> Result<Self> {
        let primary_redis_url = urls.first().unwrap().clone();

        Ok(Self {
            book_keeper: if book_keep {
                Some(Arc::new(redis::Client::open(primary_redis_url.clone())?))
            } else {
                None
            },
            lock_manager: rslock::LockManager::new(urls),
            prefix,
            lock_ttl,
            primary_redis_url,
        })
    }

    fn get_lock_name(&self, name: &str) -> String {
        format!("{}::{}", self.prefix, name)
    }
}

#[async_trait]
impl GlobalLocksManager for RedisLocksManager {
    #[instrument(err, skip(name), name = "RedisLocksManager::write")]
    async fn write_impl(&self, name: &str) -> Result<Box<dyn GlobalLockWriteGuard>> {
        let backoff_config = locks_backoff_config(LOCK_MAX_ACQUIRE_WINDOW_SECONDS);

        let lock_manager = &self.lock_manager;
        let lock_name = self.get_lock_name(name);
        let lock_ttl = self.lock_ttl;

        let operation = || async {
            match lock_manager.lock(lock_name.as_bytes(), lock_ttl).await {
                Ok(l) => Ok(Arc::new(l)),
                Err(rslock::LockError::TtlTooLarge) => {
                    Err(backoff::Error::permanent(anyhow!("Lock TTL too large")))
                }
                Err(e) => Err(backoff::Error::transient(anyhow!(
                    "Failed to acquire lock: {:?}",
                    e
                ))),
            }
        };

        let lock = retry(backoff_config, operation).await?;

        Ok(Box::new(
            RedisLockGuard::new(lock, self.book_keeper.clone(), self.lock_ttl, &self.prefix)
                .await?,
        ))
    }

    #[instrument(err, skip(name), name = "RedisLocksManager::try_write")]
    async fn try_write(&self, name: &str) -> Result<Option<Box<dyn GlobalLockWriteGuard>>> {
        match self
            .lock_manager
            .lock(self.get_lock_name(name).as_bytes(), self.lock_ttl)
            .await
        {
            Ok(l) => Ok(Some(Box::new(
                RedisLockGuard::new(
                    Arc::new(l),
                    self.book_keeper.clone(),
                    self.lock_ttl,
                    &self.prefix,
                )
                .await?,
            ))),
            Err(rslock::LockError::TtlTooLarge) => Err(anyhow!("Lock TTL too large")),
            Err(_) => Ok(None),
        }
    }

    async fn snapshot_lock_state(&self) -> Result<HashMap<String, LockState>> {
        let book_keeper = match &self.book_keeper {
            Some(book_keeper) => book_keeper,
            None => bail!("Book keeper not enabled"),
        };
        let mut con = book_keeper.get_multiplexed_async_connection().await?;
        let set_key = format!("{}_locked", self.prefix);

        // Just get the members without scores
        let result: Vec<String> = redis::cmd("ZRANGE")
            .arg(&set_key)
            .arg(0)
            .arg("-1") // Get all elements
            .query_async(&mut con)
            .await?;

        let result: Vec<String> = result
            .into_iter()
            .map(|key| {
                key.strip_prefix(&format!("{}::", self.prefix))
                    .unwrap()
                    .to_string()
            })
            .collect();

        let try_acquires = join_all(result.iter().map(|key| self.try_write(key.as_str())))
            .await
            .into_iter()
            .collect::<Result<Vec<_>>>()?;

        let mut ret = HashMap::new();
        for (key, lock) in result.into_iter().zip(try_acquires) {
            if lock.is_none() {
                ret.insert(key, LockState::Write);
            }
        }

        Ok(ret)
    }

    async fn read_impl(&self, _name: &str) -> Result<Box<dyn GlobalLockReadGuard>> {
        todo!("Redis read locks are not supported")
    }

    async fn try_read(&self, _name: &str) -> Result<Option<Box<dyn GlobalLockReadGuard>>> {
        todo!("Redis read locks are not supported")
    }

    fn supports_read_locks(&self) -> bool {
        false
    }

    async fn status(&self) -> Result<String> {
        let client = redis::Client::open(self.primary_redis_url.clone())?;
        validate_redis_connection(&client).await?;
        Ok("RedisLocksManager is ok".into())
    }
}

struct RedisLockGuard {
    lock: Arc<rslock::Lock>,
    refresh_task: Option<tokio::task::JoinHandle<()>>,
    shutdown_signal: Arc<tokio::sync::Notify>,
}

impl RedisLockGuard {
    async fn new(
        lock: Arc<rslock::Lock>,
        book_keeper: Option<Arc<redis::Client>>,
        lock_ttl: Duration,
        prefix: &str,
    ) -> Result<Self> {
        if let Some(book_keeper) = book_keeper {
            track_lock_usage(lock.clone(), book_keeper, prefix).await?;
        }
        let shutdown_signal = Arc::new(tokio::sync::Notify::new());
        Ok(Self {
            lock: lock.clone(),
            shutdown_signal: shutdown_signal.clone(),
            refresh_task: Some(tokio::spawn(async move {
                loop {
                    match lock.lock_manager.extend(&lock, lock_ttl).await {
                        Ok(_) => {}
                        Err(e) => {
                            log::error!(
                                "Error extending lock: {:?}. This could result in catastrophic failure. Aborting the process.",
                                e
                            );
                            std::process::abort();
                        }
                    }
                    tokio::select! {
                        _ = shutdown_signal.notified() => {
                            break;
                        }
                        _ = tokio::time::sleep(std::cmp::min(Duration::from_secs(5), lock_ttl / 4)) => {
                            continue;
                        }
                    }
                }
            })),
        })
    }
}

impl Drop for RedisLockGuard {
    fn drop(&mut self) {
        self.shutdown_signal.notify_one();
        let refresh_task = self.refresh_task.take();

        let lock = self.lock.clone();
        tokio::spawn(async move {
            if let Some(refresh_task) = refresh_task {
                refresh_task.abort();
                if let Err(e) = refresh_task.await {
                    if !e.is_cancelled() {
                        log::warn!("Error aborting refresh task for redis lock: {:?}", e);
                    }
                }
            }
            lock.lock_manager.unlock(&lock).await;
        });
    }
}

async fn track_lock_usage(
    lock: Arc<rslock::Lock>,
    book_keeper: Arc<redis::Client>,
    prefix: &str,
) -> Result<()> {
    let mut con = book_keeper.get_multiplexed_async_connection().await?;
    let set_key = format!("{}_locked", prefix);

    // Current timestamp in seconds
    let now = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();

    let _: ((), ()) = redis::pipe()
        .atomic()
        .cmd("ZADD")
        .arg(&set_key)
        .arg(now)
        .arg(&lock.resource)
        .cmd("EXPIRE")
        .arg(&set_key)
        .arg(LOCK_MAX_ACQUIRE_WINDOW_SECONDS)
        .query_async(&mut con)
        .await?;

    // Clean up old entries, but don't block on this
    tokio::spawn(async move {
        match redis::cmd("ZREMRANGEBYSCORE")
            .arg(&set_key)
            .arg("-inf")
            .arg(now - LOCK_MAX_ACQUIRE_WINDOW_SECONDS)
            .query_async(&mut con)
            .await
        {
            Ok(()) => (),
            Err(e) => {
                log::warn!("Error cleaning up old redis lock entries: {:?}", e);
            }
        };
    });
    Ok(())
}

impl GlobalLockWriteGuard for RedisLockGuard {}

#[cfg(test)]
mod tests {
    use std::time::Duration;

    use crate::{redis_locks_manager::RedisLocksManager, test_util::RedisContainer};
    use rslock::LockManager;

    use super::*;

    #[tokio::test]
    async fn test_redis_locking_sanity() {
        let container = RedisContainer::new().await;
        eprintln!(
            "Initialized redis container at {}",
            container.connection_url
        );

        let rl = LockManager::new(vec![container.connection_url.as_str()]);

        let lock = rl
            .lock("mutex".as_bytes(), Duration::from_millis(60000))
            .await
            .unwrap();

        eprintln!("Lock acquired (on the first try)");

        for _ in 0..3 {
            let start = std::time::Instant::now();
            let lock2 = rl
                .lock("mutex".as_bytes(), Duration::from_millis(10000))
                .await;
            assert!(lock2.is_err());
            eprintln!(
                "Lock failed to acquire after {:?} (on the second try)",
                start.elapsed()
            );
        }

        rl.unlock(&lock).await;

        let _lock3 = rl
            .lock("mutex".as_bytes(), Duration::from_millis(1000))
            .await
            .unwrap();
        eprintln!("Lock acquired (on the third try)");
    }

    #[tokio::test]
    async fn test_redis_global_lock_sanity() {
        let container = RedisContainer::new().await;
        eprintln!(
            "Initialized redis container at {}",
            container.connection_url
        );

        let retries: usize = 5;
        let ttls: Vec<u64> = vec![DEFAULT_LOCK_TTL_SECONDS * 1000, 10000, 1000];

        for _ in 0..retries {
            for ttl_ms in ttls.iter() {
                let locks_manager = RedisLocksManager::new_with_opts(
                    vec![container.connection_url.clone()],
                    format!("brainstore_lock_test_short_ttl_{}", ttl_ms),
                    Duration::from_millis(*ttl_ms),
                    true,
                )
                .unwrap();

                eprintln!("TTL: {}", ttl_ms);

                let start = std::time::Instant::now();
                let lock = locks_manager.write("test").await.unwrap();
                eprintln!("Lock acquired");

                let lock2 = locks_manager.try_write("test").await.unwrap();
                eprintln!(
                    "Lock2 acquisition after {:?} (ttl: {})",
                    start.elapsed(),
                    ttl_ms
                );
                assert!(lock2.is_none());

                let start = std::time::Instant::now();
                eprintln!("Snapshotting lock state");
                let held = locks_manager.snapshot_lock_state().await.unwrap();
                eprintln!("Snapshotting lock state took {:?}", start.elapsed());
                assert_eq!(held.len(), 1);
                assert_eq!(held["test"], LockState::Write);

                let start = std::time::Instant::now();
                tokio::spawn(async move {
                    tokio::time::sleep(Duration::from_millis(500)).await;
                    drop(lock);
                });

                let lock3 = locks_manager.write("test").await.unwrap();
                let start_elapsed = start.elapsed();
                assert!(
                    // just a little buffer
                    start_elapsed >= Duration::from_millis(250),
                    "start_elapsed: {:?}. ttl_ms: {}",
                    start_elapsed,
                    ttl_ms
                );
                drop(lock3);

                let held = locks_manager.snapshot_lock_state().await.unwrap();
                assert_eq!(held.len(), 0);
            }
        }
    }
}

pub fn locks_backoff_config(max_acquire_window_seconds: u64) -> ExponentialBackoff {
    ExponentialBackoff {
        initial_interval: Duration::from_millis(10),
        randomization_factor: 0.5, // Add jitter (±50%)
        multiplier: 2.0,
        max_interval: Duration::from_secs(5),
        max_elapsed_time: Some(Duration::from_secs(max_acquire_window_seconds)),
        ..Default::default()
    }
}
