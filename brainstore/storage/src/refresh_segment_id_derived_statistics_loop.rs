use lazy_static::lazy_static;
use otel_common::opentelemetry::metrics::Counter;
use std::sync::Arc;
use util::{anyhow::Result, uuid::Uuid};

use crate::{
    global_locks_manager::GlobalLocksManager,
    global_store::{GlobalStore, ListSegmentIdsGlobalOptionalInput, SegmentIdPaginationArgs},
};

struct Meters {
    num_segments: Counter<u64>,
}

impl Meters {
    pub fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            num_segments: meter
                .u64_counter(
                    "brainstore.storage.refresh_segment_id_derived_statistics.num_segments",
                )
                .build(),
        }
    }
}

lazy_static! {
    static ref METERS: Meters = Meters::new();
}

const BATCH_SIZE: usize = 1000;
const ITER_INTERVAL_MS: u64 = 1000;
const LOOP_SLEEP_MS: u64 = 1000 * 60 * 60 * 24;

// Loops through batches of live segments in the DB and runs
// GlobalStore::refresh_segment_id_derived_statistics on them. Terminates after a single pass
// through all segments.
pub async fn run_loop(
    global_store: Arc<dyn GlobalStore>,
    locks_manager: Arc<dyn GlobalLocksManager>,
) -> Result<()> {
    let _lock = locks_manager
        .try_write("refresh_segment_id_derived_statistics_loop")
        .await?;
    if _lock.is_none() {
        log::info!(
            "[refresh_segment_id_derived_statistics_loop] Lock already held by another worker. Sleeping for {} ms",
            LOOP_SLEEP_MS
        );
        tokio::time::sleep(tokio::time::Duration::from_millis(LOOP_SLEEP_MS)).await;
        return Ok(());
    }

    let mut segment_id_cursor: Option<Uuid> = None;
    let mut total_num_segments = 0;
    let mut num_loop_iterations = 0;
    let mut tick_interval =
        tokio::time::interval(tokio::time::Duration::from_millis(ITER_INTERVAL_MS));
    let start_time = std::time::Instant::now();
    loop {
        tick_interval.tick().await;
        let segment_ids = global_store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor,
                    limit: BATCH_SIZE,
                }),
                is_live: true,
            }))
            .await?;
        let last_segment_id = match segment_ids.last() {
            Some(segment_id) => *segment_id,
            None => {
                break;
            }
        };

        global_store
            .refresh_segment_id_derived_statistics(&segment_ids)
            .await?;
        METERS.num_segments.add(segment_ids.len() as u64, &[]);
        segment_id_cursor = Some(last_segment_id);
        total_num_segments += segment_ids.len();
        num_loop_iterations += 1;
    }

    // Sleep for a short time to create some space between loop iterations.
    log::info!(
        "[refresh_segment_id_derived_statistics_loop] Refreshed segment ID derived statistics for {} segments over {} loop iterations. Took a total of {} ms. Sleeping for {} ms before next loop iteration.",
        total_num_segments,
        num_loop_iterations,
        start_time.elapsed().as_millis(),
        LOOP_SLEEP_MS
    );
    tokio::time::sleep(tokio::time::Duration::from_millis(LOOP_SLEEP_MS)).await;

    Ok(())
}
