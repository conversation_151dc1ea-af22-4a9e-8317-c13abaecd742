use std::collections::HashSet;

use crate::{
    index_document::{make_full_schema, IndexDocument},
    tantivy_index::{make_tantivy_schema, TantivyIndexScope, TantivyIndexWriterOpts},
    tantivy_index_wrapper::ReadWriteTantivyIndexWrapper,
    test_util::TmpDirStore,
    wal_entry::WalEntry,
};
use once_cell::sync::Lazy;
use tantivy::{collector::DocSetCollector, schema::OwnedValue, TantivyDocument};
use util::{
    chrono::DateTime,
    schema::{
        BaseOptions, ConstructSchemaOpts, Field, Schema, TantivyField, TantivyType, TextOptions,
    },
    serde_json::json,
    system_types::{ObjectIdOwned, ObjectType},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    let base_options = BaseOptions {
        stored: true,
        fast: false,
    };
    Schema::new(
        "test".to_string(),
        vec![
            Field {
                name: "field1".to_string(),
                tantivy: vec![TantivyField {
                    name: "field1".to_string(),
                    field_ts: 0,
                    field_type: TantivyType::Str(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field2".to_string(),
                tantivy: vec![TantivyField {
                    name: "field2".to_string(),
                    field_ts: 0,
                    field_type: TantivyType::I64(base_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field3".to_string(),
                tantivy: vec![TantivyField {
                    name: "field3".to_string(),
                    field_ts: 0,
                    field_type: TantivyType::Json(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
        ],
        ConstructSchemaOpts {
            auto_assign_field_ts: true,
            ..Default::default()
        },
    )
    .unwrap()
}

#[test]
fn test_index_document_roundtrip() {
    let tantivy_schema = make_tantivy_schema(&make_full_schema(&basic_schema()).unwrap()).unwrap();
    let doc = IndexDocument {
        wal_entry: WalEntry {
            id: "foobar".to_string(),
            created: DateTime::from_timestamp_millis(-100).unwrap(),
            _pagination_key: PaginationKey(42),
            _xact_id: TransactionId(u64::MAX),
            _object_type: ObjectType::PromptSession,
            _object_id: ObjectIdOwned::new("goobears".to_string()).unwrap(),
            _is_merge: Some(true),
            _replace_sticky_system_fields: Some(true),
            root_span_id: "r0".to_string(),
            span_id: "s1".to_string(),
            data: json!({
                "field1": "value1",
                "field2": 42,
                "field3": json!([{ "foo": ["bar", "dog"] }, "goop"]),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    }
    .to_sanitized()
    .unwrap();
    let doc2 = IndexDocument::from_tantivy_document(
        &doc.clone()
            .to_tantivy_document(&tantivy_schema.fields)
            .unwrap(),
        &tantivy_schema.invert_fields,
    )
    .unwrap()
    .to_sanitized()
    .unwrap();
    assert_eq!(doc, doc2);

    // Test disambiguating between null and missing fields.
    let doc = IndexDocument {
        wal_entry: WalEntry {
            _is_merge: None,
            data: json!({
                "field1": "value1",
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    }
    .to_sanitized()
    .unwrap();
    let doc2 = IndexDocument::from_tantivy_document(
        &doc.clone()
            .to_tantivy_document(&tantivy_schema.fields)
            .unwrap(),
        &tantivy_schema.invert_fields,
    )
    .unwrap()
    .to_sanitized()
    .unwrap();
    assert_eq!(doc, doc2);
}

#[test]
fn test_is_root() {
    let tantivy_schema = make_tantivy_schema(&make_full_schema(&basic_schema()).unwrap()).unwrap();
    let doc = IndexDocument::default();
    assert!(doc.is_root());

    let doc = doc.to_tantivy_document(&tantivy_schema.fields).unwrap();
    let rt = IndexDocument::from_tantivy_document(&doc, &tantivy_schema.invert_fields).unwrap();
    assert!(rt.is_root());

    let doc = IndexDocument {
        wal_entry: WalEntry {
            span_parents: vec!["foo".to_string()],
            ..Default::default()
        },
    };
    assert!(!doc.is_root());

    let doc = doc.to_tantivy_document(&tantivy_schema.fields).unwrap();
    let rt = IndexDocument::from_tantivy_document(&doc, &tantivy_schema.invert_fields).unwrap();
    assert!(!rt.is_root());
}

#[test]
fn test_object_delete() {
    let tantivy_schema = make_tantivy_schema(&make_full_schema(&basic_schema()).unwrap()).unwrap();
    let doc = IndexDocument {
        wal_entry: WalEntry {
            _object_delete: Some(true),
            ..Default::default()
        },
    };
    assert!(doc.wal_entry._object_delete.unwrap());
    assert!(doc.to_tantivy_document(&tantivy_schema.fields).is_err());
}

#[test]
fn test_invalid_field_types() {
    let tantivy_schema = make_tantivy_schema(&make_full_schema(&basic_schema()).unwrap()).unwrap();
    // String-type schema fields are quite permissive.
    let doc = IndexDocument {
        wal_entry: WalEntry {
            data: json!({
                "field1": json!({"foo": "bar"}),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    };
    assert!(doc.to_tantivy_document(&tantivy_schema.fields).is_ok());
    // But integer-typed fields are not.
    let doc = IndexDocument {
        wal_entry: WalEntry {
            data: json!({
                "field2": json!({"foo": "bar"}),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    };
    assert!(doc.to_tantivy_document(&tantivy_schema.fields).is_err());
}

#[test]
fn test_full_schema_idempotent() {
    let schema = basic_schema();
    let full_schema = make_full_schema(&schema).unwrap();
    let full_schema2 = make_full_schema(&full_schema).unwrap();
    for (field1, field2) in full_schema
        .fields()
        .iter()
        .zip(full_schema2.fields().iter())
    {
        assert_eq!(field1.name, field2.name);
        assert_eq!(field1.tantivy.len(), field2.tantivy.len());
        for (tantivy1, tantivy2) in field1.tantivy.iter().zip(field2.tantivy.iter()) {
            assert_eq!(tantivy1.name, tantivy2.name);
            assert_eq!(
                format!("{:?}", tantivy1.field_type),
                format!("{:?}", tantivy2.field_type)
            );
        }
    }
}

struct WriteTantivyFixture {
    pub _tmp_dir_store: TmpDirStore,
    pub index: ReadWriteTantivyIndexWrapper,
}

static WRITE_TANTIVY_FIXTURE_SEGMENT_ID: Lazy<Uuid> = Lazy::new(Uuid::new_v4);

impl WriteTantivyFixture {
    pub fn new() -> Self {
        let tmp_dir_store = TmpDirStore::new();
        let index = ReadWriteTantivyIndexWrapper::new_blocking(
            tmp_dir_store.store_info.directory.clone(),
            make_full_schema(&WriteTantivyFixture::schema()).unwrap(),
            &tmp_dir_store.store_info.prefix,
            &TantivyIndexScope::Segment(*WRITE_TANTIVY_FIXTURE_SEGMENT_ID),
        )
        .unwrap();
        Self {
            _tmp_dir_store: tmp_dir_store,
            index,
        }
    }

    pub fn schema() -> Schema {
        Schema::new(
            "test".to_string(),
            vec![
                Field {
                    name: "field1".to_string(),
                    tantivy: vec![TantivyField {
                        name: "field1".to_string(),
                        field_ts: 0,
                        field_type: TantivyType::Str(TextOptions {
                            stored: true,
                            fast: false,
                            tokenize: false,
                        }),
                        repeated: false,
                        lossy_fast_field: false,
                    }],
                },
                Field {
                    name: "field2".to_string(),
                    tantivy: vec![TantivyField {
                        name: "field2".to_string(),
                        field_ts: 0,
                        field_type: TantivyType::I64(BaseOptions {
                            stored: true,
                            fast: true,
                        }),
                        repeated: false,
                        lossy_fast_field: false,
                    }],
                },
            ],
            ConstructSchemaOpts {
                auto_assign_field_ts: true,
                ..Default::default()
            },
        )
        .unwrap()
    }

    pub fn query_parser(&self) -> tantivy::query::QueryParser {
        tantivy::query::QueryParser::new(
            self.index.index.schema(),
            vec![],
            tantivy::tokenizer::TokenizerManager::default(),
        )
    }

    pub fn run_query(&self, query: &str) -> HashSet<tantivy::DocAddress> {
        let query_parser = self.query_parser();
        let query = query_parser.parse_query(query).unwrap();
        let reader = self.index.make_reader_blocking().unwrap();
        let collector = DocSetCollector {};
        reader.searcher().search(&query, &collector).unwrap()
    }

    pub fn get_doc_address(&self, query: &str) -> tantivy::DocAddress {
        let docs = self.run_query(query);
        assert_eq!(docs.len(), 1);
        *docs.iter().next().unwrap()
    }

    pub fn make_tantivy_doc(&self, wal_entry: WalEntry) -> TantivyDocument {
        IndexDocument { wal_entry }
            .to_tantivy_document(&self.index.writable_schema.fields)
            .unwrap()
    }

    pub fn get_field(&self, field_name: &str) -> tantivy::schema::Field {
        self.index.index.schema().get_field(field_name).unwrap()
    }
}

#[test]
fn test_basic_write() {
    let fixture = WriteTantivyFixture::new();
    {
        let mut writer = fixture
            .index
            .make_writer_blocking(&TantivyIndexWriterOpts::default())
            .unwrap();
        writer
            .add_document(
                fixture.make_tantivy_doc(WalEntry {
                    id: "row0".to_string(),
                    created: DateTime::from_timestamp_millis(100).unwrap(),
                    _xact_id: TransactionId(200),
                    _object_id: ObjectIdOwned::new("obj0".to_string()).unwrap(),
                    data: json!({"field1": "value0", "field2": 42, "field3": "goober"})
                        .as_object()
                        .unwrap()
                        .clone(),
                    ..Default::default()
                }),
            )
            .unwrap();
        writer
            .add_document(
                fixture.make_tantivy_doc(WalEntry {
                    id: "row1".to_string(),
                    created: DateTime::from_timestamp_millis(200).unwrap(),
                    _xact_id: TransactionId(300),
                    _object_id: ObjectIdOwned::new("obj0".to_string()).unwrap(),
                    data: json!({"field1": "value1 thing", "field2": 43, "field4": "bloop"})
                        .as_object()
                        .unwrap()
                        .clone(),
                    ..Default::default()
                }),
            )
            .unwrap();
        writer.commit().unwrap();
    }

    {
        let reader = fixture.index.make_reader_blocking().unwrap();
        assert_eq!(reader.searcher().num_docs(), 2);
    }

    // Get the addresses of the two documents.
    let row0_address = fixture.get_doc_address("id:row0");
    let row1_address = fixture.get_doc_address("id:row1");

    // Check that the documents have some of the fields we set.
    {
        let reader = fixture.index.make_reader_blocking().unwrap();
        let searcher = reader.searcher();

        let doc: TantivyDocument = searcher.doc(row0_address).unwrap();
        assert_eq!(
            doc.get_first(fixture.get_field("id")),
            Some(&OwnedValue::Str("row0".to_string()))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("_xact_id")),
            Some(&OwnedValue::U64(200))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("field1")),
            Some(&OwnedValue::Str("value0".to_string()))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("field2")),
            Some(&OwnedValue::I64(42))
        );

        let doc: TantivyDocument = searcher.doc(row1_address).unwrap();
        assert_eq!(
            doc.get_first(fixture.get_field("id")),
            Some(&OwnedValue::Str("row1".to_string()))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("_object_type")),
            Some(&OwnedValue::Str(ObjectType::default().to_string()))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("_object_id")),
            Some(&OwnedValue::Str("obj0".to_string()))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("field1")),
            Some(&OwnedValue::Str("value1 thing".to_string()))
        );
        assert_eq!(
            doc.get_first(fixture.get_field("field2")),
            Some(&OwnedValue::I64(43))
        );
    }

    // Try a few basic queries.
    assert_eq!(
        fixture.run_query("field1:value0"),
        [row0_address].into_iter().collect()
    );
    assert_eq!(
        fixture.run_query("field1:\"value1 thing\""),
        [row1_address].into_iter().collect()
    );
    assert_eq!(
        fixture.run_query(&format!("_object_type:{}", ObjectType::default())),
        [row0_address, row1_address].into_iter().collect()
    );
    assert_eq!(
        fixture.run_query("_object_id:obj0"),
        [row0_address, row1_address].into_iter().collect()
    );
}

#[test]
fn test_concurrent_write_on_index_no_error() {
    let fixture = WriteTantivyFixture::new();
    let _writer1 = fixture
        .index
        .make_writer_blocking(&TantivyIndexWriterOpts::default())
        .unwrap();
    let _writer2 = fixture
        .index
        .make_writer_blocking(&TantivyIndexWriterOpts::default())
        .unwrap();
}

#[test]
fn test_write_is_root() {
    for is_root in [true, false] {
        let fixture = WriteTantivyFixture::new();
        let mut writer = fixture
            .index
            .make_writer_blocking(&TantivyIndexWriterOpts::default())
            .unwrap();

        let doc = fixture.make_tantivy_doc(WalEntry {
            id: "row0".to_string(),
            span_parents: if is_root {
                vec![]
            } else {
                vec!["foo".to_string()]
            },
            ..Default::default()
        });
        writer.add_document(doc).unwrap();
        writer.commit().unwrap();

        let is_root_field = fixture.get_field("is_root");

        let reader = fixture.index.make_reader_blocking().unwrap();
        let searcher = reader.searcher();
        assert_eq!(searcher.num_docs(), 1);
        let row0_address = fixture.get_doc_address("id:row0");
        let doc: TantivyDocument = searcher.doc(row0_address).unwrap();
        assert_eq!(
            doc.get_first(is_root_field),
            Some(&OwnedValue::Bool(is_root))
        );
    }
}
