use lazy_static::lazy_static;
use std::{collections::HashMap, pin::Pin};

use async_trait::async_trait;
use util::{
    anyhow::{anyhow, Result},
    url::Url,
};

use crate::healthcheck_util::validate_postgres_connection;
use crate::{
    global_locks_manager::{
        GlobalLockReadGuard, GlobalLockWriteGuard, GlobalLocksManager, LockState,
    },
    postgres_pool::PostgresPool,
};

const GLOBAL_LOCKS_TABLE: &str = "brainstore_global_locks_manager_locks";
const GLOBAL_LOCKS_STATE_TABLE: &str = "brainstore_global_locks_manager_last_lock_state";

lazy_static! {
    static ref ACQUIRE_READ_QUERY: String = format!(
        "SELECT * FROM {} WHERE name = $1 FOR SHARE",
        GLOBAL_LOCKS_TABLE
    );
    static ref ACQUIRE_WRITE_QUERY: String = format!(
        "SELECT * FROM {} WHERE name = $1 FOR UPDATE",
        GLOBAL_LOCKS_TABLE
    );
    static ref TRY_READ_QUERY: String = format!(
        "SELECT * FROM {} WHERE name = $1 FOR SHARE NOWAIT",
        GLOBAL_LOCKS_TABLE
    );
    static ref TRY_WRITE_QUERY: String = format!(
        "SELECT * FROM {} WHERE name = $1 FOR UPDATE NOWAIT",
        GLOBAL_LOCKS_TABLE
    );
    static ref POPULATE_QUERY: String = format!(
        "INSERT INTO {} (name) VALUES ($1) ON CONFLICT DO NOTHING",
        GLOBAL_LOCKS_TABLE
    );
    static ref GET_LOCK_NAMES_AND_STATES_QUERY: String = format!(
        "SELECT a.name, b.last_lock_state FROM {} a LEFT JOIN {} b ON a.name = b.name",
        GLOBAL_LOCKS_TABLE, GLOBAL_LOCKS_STATE_TABLE
    );
    static ref SET_LAST_LOCK_STATE_QUERY: String = format!(
        "INSERT INTO {} (name, last_lock_state) VALUES ($1, $2) ON CONFLICT (name) DO UPDATE SET last_lock_state = EXCLUDED.last_lock_state",
        GLOBAL_LOCKS_STATE_TABLE
    );
}

#[derive(Debug, Clone)]
pub struct PostgresGlobalLocksManager {
    pool: PostgresPool,
}

pub const DEFAULT_MAX_PG_LOCKS: usize = 256;

impl PostgresGlobalLocksManager {
    pub fn new(url: &Url, max_locks: usize) -> Result<Self> {
        // This effectively caps the number of concurrent locks to be max_locks (+ one
        // maintenance connection).
        Ok(Self {
            pool: PostgresPool::new(url, max_locks + 1)?,
        })
    }

    async fn acquire_lock(
        &self,
        name: &str,
        mode: LockState,
        block: bool,
    ) -> Result<Option<PostgresLockGuard>> {
        let lock_query = match (mode, block) {
            (LockState::Read, true) => &*ACQUIRE_READ_QUERY,
            (LockState::Read, false) => &*TRY_READ_QUERY,
            (LockState::Write, true) => &*ACQUIRE_WRITE_QUERY,
            (LockState::Write, false) => &*TRY_WRITE_QUERY,
        };

        let mut ret = PostgresLockGuard {
            transaction: None,
            client: Box::pin(self.pool.get_client().await?),
        };

        let mut transaction = ret.client.transaction().await?;
        let mut row = None;
        for _ in 0..2 {
            let mut res = match transaction.query(lock_query, &[&name]).await {
                Ok(res) => res,
                Err(e) => {
                    if matches!(e.code().map(|c| c.code()), Some("55P03")) && !block {
                        return Ok(None);
                    }
                    return Err(e.into());
                }
            };
            if res.len() == 0 {
                transaction.query(&*POPULATE_QUERY, &[&name]).await?;
                transaction.commit().await?;
                transaction = ret.client.transaction().await?;
            } else if res.len() == 1 {
                row = Some(res.remove(0));
            } else {
                return Err(anyhow!("multiple locks found for name: {}", name));
            }
        }
        if matches!(row, None) {
            return Err(anyhow!(
                "Unable to acquire lock after three tries (this is likely a bug)"
            ));
        }

        // SAFETY: We know that the transaction is tied to this client, and the client is dropped
        // after the transaction in this struct, so we can safely transmute the transaction to a
        // static reference.
        ret.transaction = Some(unsafe { std::mem::transmute(transaction) });

        Ok(Some(ret))
    }
}

#[async_trait]
impl GlobalLocksManager for PostgresGlobalLocksManager {
    async fn read_impl(&self, name: &str) -> Result<Box<dyn GlobalLockReadGuard>> {
        match self.acquire_lock(name, LockState::Read, true).await? {
            Some(lock) => Ok(Box::new(
                PostgresReadLockGuard::new(self.pool.clone(), name.to_string(), lock).await?,
            )),
            None => Err(anyhow!(
                "unexpected: failed to acquire read lock after 2 tries"
            )),
        }
    }

    async fn try_read(&self, name: &str) -> Result<Option<Box<dyn GlobalLockReadGuard>>> {
        match self.acquire_lock(name, LockState::Read, false).await? {
            Some(lock) => Ok(Some(Box::new(
                PostgresReadLockGuard::new(self.pool.clone(), name.to_string(), lock).await?,
            ))),
            None => Ok(None),
        }
    }

    async fn write_impl(&self, name: &str) -> Result<Box<dyn GlobalLockWriteGuard>> {
        match self.acquire_lock(name, LockState::Write, true).await? {
            Some(lock) => Ok(Box::new(
                PostgresWriteLockGuard::new(self.pool.clone(), name.to_string(), lock).await?,
            )),
            None => Err(anyhow!(
                "unexpected: failed to acquire write lock after 2 tries"
            )),
        }
    }

    async fn try_write(&self, name: &str) -> Result<Option<Box<dyn GlobalLockWriteGuard>>> {
        match self.acquire_lock(name, LockState::Write, false).await? {
            Some(lock) => Ok(Some(Box::new(
                PostgresWriteLockGuard::new(self.pool.clone(), name.to_string(), lock).await?,
            ))),
            None => Ok(None),
        }
    }

    async fn snapshot_lock_state(&self) -> Result<HashMap<String, LockState>> {
        let client = self.pool.get_client().await?;
        let lock_names = client
            .query(&*GET_LOCK_NAMES_AND_STATES_QUERY, &[])
            .await?
            .into_iter()
            .map(|row| {
                (
                    row.get::<_, &str>("name").to_string(),
                    row.get::<_, &str>("last_lock_state").to_string(),
                )
            })
            .collect::<Vec<_>>();

        let mut ret = HashMap::new();
        for (name, state) in lock_names {
            let state: Option<LockState> = match self.try_write(&name).await? {
                Some(_) => {
                    // We successfully took the write-lock, meaning the lock was not taken.
                    None
                }
                None => match state.as_str() {
                    "write" => Some(LockState::Write),
                    "read" => Some(LockState::Read),
                    _ => return Err(anyhow!("invalid lock state: {}", state)),
                },
            };
            if let Some(state) = state {
                ret.insert(name, state);
            }
        }
        Ok(ret)
    }

    async fn status(&self) -> Result<String> {
        let client = self.pool.get_client().await?;
        validate_postgres_connection(client).await?;
        Ok("PostgresGlobalLocksManager is ok".into())
    }
}

struct PostgresLockGuard {
    // In Rust, fields are dropped in the same order as they are declared.
    // See https://doc.rust-lang.org/reference/destructors.html
    transaction: Option<deadpool_postgres::Transaction<'static>>,
    client: Pin<Box<deadpool_postgres::Object>>,
}

struct PostgresReadLockGuard {
    _lock_guard: PostgresLockGuard,
}

impl PostgresReadLockGuard {
    pub async fn new(
        pool: PostgresPool,
        name: String,
        lock_guard: PostgresLockGuard,
    ) -> Result<Self> {
        let client = pool.get().await?;
        client
            .query(&*SET_LAST_LOCK_STATE_QUERY, &[&name, &"read"])
            .await?;
        Ok(Self {
            _lock_guard: lock_guard,
        })
    }
}

struct PostgresWriteLockGuard {
    _lock_guard: PostgresLockGuard,
}

impl PostgresWriteLockGuard {
    pub async fn new(
        pool: PostgresPool,
        name: String,
        lock_guard: PostgresLockGuard,
    ) -> Result<Self> {
        let client = pool.get().await?;
        client
            .query(&*SET_LAST_LOCK_STATE_QUERY, &[&name, &"write"])
            .await?;
        Ok(Self {
            _lock_guard: lock_guard,
        })
    }
}

impl GlobalLockReadGuard for PostgresReadLockGuard {}
impl GlobalLockWriteGuard for PostgresWriteLockGuard {}

#[cfg(test)]
mod tests {
    use crate::{
        global_store_test::get_postgres_global_store_migration, test_init::test_init,
        test_util::PostgresContainer,
    };

    use super::*;

    #[tokio::test]
    async fn test_postgres_locking_sanity() {
        test_init::init();
        let container = PostgresContainer::new().await;
        container
            .run_migration(&get_postgres_global_store_migration())
            .await
            .unwrap();

        let locks_manager =
            PostgresGlobalLocksManager::new(&container.connection_url, DEFAULT_MAX_PG_LOCKS)
                .unwrap();
        let read_lock = locks_manager.read("test").await.unwrap();
        let read_lock2 = locks_manager.read("test").await.unwrap();

        let try_write_lock = locks_manager.try_write("test").await.unwrap();
        assert!(matches!(try_write_lock, None));

        let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
        assert_eq!(snapshot.len(), 1);
        assert_eq!(snapshot["test"], LockState::Read);

        drop(read_lock);
        drop(read_lock2);

        // This is a bit of an inexact science, but we can't be sure that the lock is fully released synchronously
        // with the `drop`, because it involves closing a transaction (which happens over the network, and likely through
        // some async stuff in `tokio_postgres`). So we wait for a bit to make sure the lock is released.
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
        assert_eq!(snapshot.len(), 0);

        let write_lock = locks_manager.write("test").await.unwrap();
        let try_write_lock = locks_manager.try_write("test").await.unwrap();
        assert!(matches!(try_write_lock, None));

        let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
        assert_eq!(snapshot.len(), 1);
        assert_eq!(snapshot["test"], LockState::Write);

        drop(write_lock);

        let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
        assert_eq!(snapshot.len(), 0);

        let locks_manager = PostgresGlobalLocksManager::new(&container.connection_url, 2).unwrap();
        let _read_lock = locks_manager.read("test").await.unwrap();
        let _read_lock2 = locks_manager.read("test").await.unwrap();

        let read_lock3 = locks_manager.read("test").await;
        assert!(matches!(read_lock3, Err(_)));
    }
}
