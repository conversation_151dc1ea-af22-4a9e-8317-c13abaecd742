use serde_json::json;
use tantivy::doc;
use util::schema::{Field, Schema, TantivyField, TantivyType, TextOptions};

use crate::{
    limits::calculate_memory_budget_per_indexing_operation_for_test,
    tantivy_index::{
        check_and_set_meta_schema, collect_meta_json, invert_document, make_tantivy_schema,
        write_meta_json, TantivyIndexScope, TantivyIndexWriterOpts,
    },
    tantivy_index_test_util::read_all_documents,
    tantivy_index_wrapper::ReadWriteTantivyIndexWrapper,
    test_util::TmpDirStore,
};

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    Schema::new(
        "test".to_string(),
        vec![Field {
            name: "field1".to_string(),
            tantivy: vec![TantivyField {
                name: "field1".to_string(),
                field_ts: 1,
                field_type: TantivyType::Str(text_options.clone()),
                repeated: false,
                lossy_fast_field: false,
            }],
        }],
        Default::default(),
    )
    .unwrap()
}

fn basic_schema2() -> Schema {
    let orig_schema = basic_schema();
    let mut new_fields = orig_schema.fields().clone();
    let field1 = new_fields[0].clone();
    new_fields.push(Field {
        name: "field2".to_string(),
        tantivy: vec![TantivyField {
            name: "field2".to_string(),
            field_ts: 2,
            ..field1.tantivy[0].clone()
        }],
    });
    Schema::new(orig_schema.name().clone(), new_fields, Default::default()).unwrap()
}

#[test]
fn test_write_with_schema_update() {
    let tmp_dir_store = TmpDirStore::new();
    let index_scope = TantivyIndexScope::Segment(Default::default());
    eprintln!("Writing to tmp dir {:?}", tmp_dir_store.tmp_dir.path());
    {
        let index = ReadWriteTantivyIndexWrapper::new_blocking(
            tmp_dir_store.store_info.directory.clone(),
            basic_schema(),
            &tmp_dir_store.store_info.prefix,
            &index_scope,
        )
        .unwrap();

        // Write a document, and check that we can read it back.
        {
            let mut writer = index.make_writer_blocking(&Default::default()).unwrap();
            let field1 = index.tantivy_schema().get_field("field1").unwrap();
            writer
                .add_document(doc!(
                    field1 => "hello"
                ))
                .unwrap();
            writer.commit().unwrap();
        }
        let docs = read_all_documents(&index.index.reader().unwrap());
        let docs = docs
            .into_values()
            .map(|doc| invert_document(&doc, &index.writable_schema.invert_fields).unwrap())
            .collect::<Vec<_>>();
        assert_eq!(docs, vec![json!({"field1":"hello"})]);
    }

    // If we try to create an index with an updated schema, it should fail.
    assert!(ReadWriteTantivyIndexWrapper::new_blocking(
        tmp_dir_store.store_info.directory.clone(),
        basic_schema2(),
        &tmp_dir_store.store_info.prefix,
        &index_scope,
    )
    .is_err());

    // But if we "flash" the index metadata with the updated schema, it should work.
    let async_runtime = tokio::runtime::Runtime::new().unwrap();
    async_runtime.block_on(async {
        let index_meta = collect_meta_json(
            tmp_dir_store.store_info.directory.as_ref(),
            &index_scope.path(&tmp_dir_store.store_info.prefix),
        )
        .await
        .unwrap()
        .unwrap();
        let index_meta = check_and_set_meta_schema(
            index_meta,
            &make_tantivy_schema(&basic_schema2()).unwrap().schema,
        )
        .unwrap();
        write_meta_json(
            tmp_dir_store.store_info.directory.as_ref(),
            &index_scope.path(&tmp_dir_store.store_info.prefix),
            &index_meta,
        )
        .await
        .unwrap();
    });

    {
        let index = ReadWriteTantivyIndexWrapper::new_blocking(
            tmp_dir_store.store_info.directory.clone(),
            basic_schema2(),
            &tmp_dir_store.store_info.prefix,
            &TantivyIndexScope::Segment(Default::default()),
        )
        .unwrap();

        // Write a document, and check that we can read it back.
        {
            let mut writer = index.make_writer_blocking(&Default::default()).unwrap();
            let field1 = index.tantivy_schema().get_field("field1").unwrap();
            let field2 = index.tantivy_schema().get_field("field2").unwrap();
            writer
                .add_document(doc!(
                    field1 => "goodbye",
                    field2 => "friend"
                ))
                .unwrap();
            writer.commit().unwrap();
        }
        let docs = read_all_documents(&index.index.reader().unwrap());
        let mut docs = docs
            .into_values()
            .map(|doc| invert_document(&doc, &index.writable_schema.invert_fields).unwrap())
            .collect::<Vec<_>>();
        docs.sort_by(|a, b| {
            a["field1"]
                .as_str()
                .unwrap()
                .cmp(b["field1"].as_str().unwrap())
        });
        assert_eq!(
            docs,
            vec![
                json!({"field1":"goodbye", "field2": "friend"}),
                json!({"field1":"hello"})
            ]
        );
    }
}

#[test]
fn test_create_writer_with_bounded_memory_budgets() {
    let tmp_dir_store = TmpDirStore::new();
    let index_scope = TantivyIndexScope::Segment(Default::default());
    const SHOULD_SUCCEED: bool = true;

    let try_make_writer_with_budget = |num_writer_threads: usize,
                                       memory_budget_per_indexing_operation: usize,
                                       should_succeed: bool| {
        let index = ReadWriteTantivyIndexWrapper::new_blocking(
            tmp_dir_store.store_info.directory.clone(),
            basic_schema(),
            &tmp_dir_store.store_info.prefix,
            &index_scope,
        )
        .unwrap();
        let res = index.make_writer_blocking(&TantivyIndexWriterOpts {
            num_writer_threads,
            memory_budget_per_indexing_operation,
            ..Default::default()
        });
        if should_succeed {
            assert!(
                res.is_ok(),
                "num_writer_threads = {}, memory_budget_per_indexing_operation = {}, err = {:?}",
                num_writer_threads,
                memory_budget_per_indexing_operation,
                res.err().unwrap()
            );
        } else {
            assert!(
                res.is_err(),
                "num_writer_threads = {}, memory_budget_per_indexing_operation = {}",
                num_writer_threads,
                memory_budget_per_indexing_operation
            );
        }
    };

    // Make sure that our defaults work.
    try_make_writer_with_budget(
        TantivyIndexWriterOpts::default().num_writer_threads,
        TantivyIndexWriterOpts::default().memory_budget_per_indexing_operation,
        SHOULD_SUCCEED,
    );

    // Make sure that certainly-out-of-bounds values fail.
    let raw_memory_budgets = [usize::MIN + 1, usize::MAX - 1];
    for raw_memory_budget in raw_memory_budgets {
        try_make_writer_with_budget(1, raw_memory_budget, !SHOULD_SUCCEED);
    }

    // But if we clamp the out-of-bounds values, it will work.
    for raw_memory_budget in raw_memory_budgets {
        let system_memory = raw_memory_budget;
        const LARGE_VALUE: usize = (u32::MAX / 2) as usize;
        const SMALL_VALUE: usize = 2;
        for max_concurrent_operations in [LARGE_VALUE, SMALL_VALUE] {
            for threads_per_operation in [1, 2, 4, 8] {
                let memory_budget = calculate_memory_budget_per_indexing_operation_for_test(
                    system_memory,
                    max_concurrent_operations,
                    threads_per_operation,
                );
                try_make_writer_with_budget(threads_per_operation, memory_budget, SHOULD_SUCCEED);
            }
        }
    }
}
