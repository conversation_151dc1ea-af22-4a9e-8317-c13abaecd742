use std::sync::Arc;

use util::{anyhow::Result, itertools::Itertools};

use crate::{
    global_store::{BackfillTrackingEntry, GlobalStore, TestingOnlyBackfillBrainstoreObjectAtom},
    postgres_wal::{PostgresWAL, PostgresWalStreamBounded},
    wal::{WALScope, Wal},
    wal_entry::WalEntry,
};

fn brainstore_object_atom_to_wal_entry(
    brainstore_object_atom: &TestingOnlyBackfillBrainstoreObjectAtom,
) -> WalEntry {
    WalEntry {
        id: util::uuid::Uuid::new_v4().to_string(),
        _xact_id: brainstore_object_atom.xact_id,
        _object_id: brainstore_object_atom.object_id.object_id.clone(),
        _object_type: brainstore_object_atom.object_id.object_type,
        ..Default::default()
    }
}

pub async fn insert_object_atoms_into_wal(
    object_atoms: Vec<TestingOnlyBackfillBrainstoreObjectAtom>,
    tracking_entries: Vec<BackfillTrackingEntry>,
    wal: Arc<dyn Wal>,
    global_store: Arc<dyn GlobalStore>,
    inject_dummy_row_refs: bool,
) -> Result<()> {
    // For postgres wal, we don't need to insert into the WAL because
    // the global store implementation of
    // `testing_only_insert_backfill_data` will handle inserting into the
    // logs tables.
    if wal.as_ref().downcast_ref::<PostgresWAL>().is_none()
        && wal
            .as_ref()
            .downcast_ref::<PostgresWalStreamBounded>()
            .is_none()
    {
        let wal_entries = object_atoms
            .iter()
            .map(brainstore_object_atom_to_wal_entry)
            .collect::<Vec<_>>();
        // Group by object id.
        let grouped_wal_entries = wal_entries
            .into_iter()
            .map(|entry| (entry.full_object_id().to_owned(), entry))
            .into_group_map();
        for (object_id, entries) in grouped_wal_entries {
            let object_wal_token = global_store
                .query_object_metadatas(&[object_id.as_ref()])
                .await?
                .remove(0)
                .wal_token;
            let wal_scope = WALScope::ObjectId(object_id.as_ref(), object_wal_token);
            wal.insert(wal_scope, entries).await?;
        }
    }

    global_store
        .testing_only_insert_backfill_data(tracking_entries, object_atoms, inject_dummy_row_refs)
        .await?;

    Ok(())
}
