use std::{collections::HashMap, time::Instant};

use clap::Parser;
use futures::future::{try_join3, try_join_all};
use lazy_static::lazy_static;
use otel_common::opentelemetry::{
    global,
    metrics::{Counter, Histogram},
    KeyValue,
};
use serde::{Deserialize, Serialize};
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Duration, Utc},
    schema::Schema,
    system_types::{make_object_schema, FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
};

use crate::{
    config_with_store::ConfigWithStore,
    global_store::{
        DeleteFromSegmentStats, TaskInfo, TimeBasedRetentionCursor, TimeBasedRetentionInfo,
        TimeBasedRetentionOperation, TimeBasedRetentionState, TimeBasedRetentionStats,
    },
    index_document::make_full_schema,
    retention::{
        delete_from_segments_up_to_xact_id, DeleteFromSegmentsInput,
        DeleteFromSegmentsOptionalInput, DeleteFromSegmentsOptions,
    },
    retention_policy_lookup::{
        compute_min_retained_xact_id, resolve_retention_policies, ControlPlaneContext,
        ResolveRetentionPoliciesInput, ResolveRetentionPoliciesOptionalInput,
        ResolveRetentionPoliciesOptions, ResolvedRetentionTarget, RetentionObject,
        RetentionPolicyConfig, RetentionTrackingId,
    },
    retention_util::RetentionObjectType,
    timer::{OtelCounterGuard, OtelTimer},
    wal::{DeleteFromWalStats, DeleteUpToXactIdInput, DeleteUpToXactIdOptions, WALScope},
};

#[derive(Clone)]
pub struct TimeBasedRetentionInput<'a> {
    // If object_ids is None, retention will run over all objects with an active retention policy.
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub config: &'a ConfigWithStore,
    pub config_file_schema: Option<&'a Schema>,
    pub control_plane_ctx: Option<&'a ControlPlaneContext>,
    pub dry_run: bool,
}

#[derive(Clone, Debug, Default)]
pub struct TimeBasedRetentionOptionalInput<'a> {
    pub testing_force_error_for_object_id: Option<FullObjectId<'a>>,
    pub testing_force_error_for_segment_id: Option<Uuid>,
    // If provided, use these time-based retention policies instead of calling the API to resolve them.
    pub testing_object_id_to_retention_days: Option<HashMap<FullObjectId<'a>, i64>>,
}

#[derive(Clone, Debug, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionOptions {
    #[arg(
        long,
        default_value_t = default_object_batch_size(),
        help = "Batch size for resolving object retention policies.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_OBJECT_BATCH_SIZE",
    )]
    pub time_based_retention_object_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_segment_batch_size(),
        help = "Number of segments to run time-based retention on in each iteration.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_SEGMENT_BATCH_SIZE",
    )]
    pub time_based_retention_segment_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_interval_seconds(),
        help = "Minimum wait between iterations of the time-based retention worker.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_INTERVAL_SECONDS",
    )]
    pub time_based_retention_interval_seconds: i64,
    #[arg(
        long,
        default_value_t = default_disable_experiments_and_datasets(),
        help = "Skip retention for experiments and datasets (only process project logs).",
        env = "BRAINSTORE_TIME_BASED_RETENTION_DISABLE_EXPERIMENTS_AND_DATASETS",
    )]
    pub time_based_retention_disable_experiments_and_datasets: bool,
    #[arg(
        long,
        default_value_t = default_disable_postgres_wal_deletes(),
        help = "Disable retention for the PostgreSQL WAL.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_DISABLE_POSTGRES_WAL_DELETES",
    )]
    pub time_based_retention_disable_postgres_wal_deletes: bool,
    #[arg(
        long,
        default_value_t = default_postgres_wal_deletion_batch_size(),
        help = "Number of unique IDs per delete query from the Postgres WAL.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_POSTGRES_WAL_BATCH_SIZE",
    )]
    pub time_based_retention_postgres_wal_deletion_batch_size: i64,
    #[arg(
        long,
        default_value_t = default_postgres_wal_max_num_rows_per_object(),
        help = "Maximum number of rows to delete per object during Postgres WAL retention operations.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_POSTGRES_WAL_MAX_NUM_ROWS_PER_OBJECT",
    )]
    pub time_based_retention_postgres_wal_max_num_rows_per_object: u64,
    #[arg(
        long,
        default_value_t = default_disable_realtime_wal_deletes(),
        help = "Disable retention for the realtime WAL.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_DISABLE_REALTIME_WAL_DELETES",
    )]
    pub time_based_retention_disable_realtime_wal_deletes: bool,
    #[arg(
        long,
        default_value_t = default_realtime_wal_deletion_batch_size(),
        help = "Batch size for realtime WAL deletes.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_REALTIME_WAL_BATCH_SIZE",
    )]
    pub time_based_retention_realtime_wal_deletion_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_realtime_wal_max_num_files_per_object(),
        help = "Maximum number of files to delete per object during realtime WAL retention operations.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_REALTIME_WAL_MAX_NUM_FILES_PER_OBJECT",
    )]
    pub time_based_retention_realtime_wal_max_num_files_per_object: u64,
    #[arg(
        long,
        default_value_t = default_wal_deletion_log_batch_size(),
        help = "Batch size for WAL deletion log files.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_WAL_DELETION_LOG_BATCH_SIZE",
    )]
    pub time_based_retention_wal_deletion_log_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_deleted_object_grace_period_days(),
        help = "How long to wait after an object is deleted before retention will automatically run on it.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_DELETED_OBJECT_GRACE_PERIOD_DAYS",
    )]
    pub time_based_retention_deleted_object_grace_period_days: i64,
}

impl Default for TimeBasedRetentionOptions {
    fn default() -> Self {
        Self {
            time_based_retention_object_batch_size: default_object_batch_size(),
            time_based_retention_segment_batch_size: default_segment_batch_size(),
            time_based_retention_interval_seconds: default_interval_seconds(),
            time_based_retention_disable_experiments_and_datasets:
                default_disable_experiments_and_datasets(),
            time_based_retention_disable_postgres_wal_deletes: default_disable_postgres_wal_deletes(
            ),
            time_based_retention_postgres_wal_deletion_batch_size:
                default_postgres_wal_deletion_batch_size(),
            time_based_retention_postgres_wal_max_num_rows_per_object:
                default_postgres_wal_max_num_rows_per_object(),
            time_based_retention_disable_realtime_wal_deletes: default_disable_realtime_wal_deletes(
            ),
            time_based_retention_realtime_wal_deletion_batch_size:
                default_realtime_wal_deletion_batch_size(),
            time_based_retention_realtime_wal_max_num_files_per_object:
                default_realtime_wal_max_num_files_per_object(),
            time_based_retention_wal_deletion_log_batch_size: default_wal_deletion_log_batch_size(),
            time_based_retention_deleted_object_grace_period_days:
                default_deleted_object_grace_period_days(),
        }
    }
}

fn default_object_batch_size() -> usize {
    // NOTE: This was determined on staging while testing with very few retention policies.
    // We may want to tune this default further at some point.
    100
}

fn default_segment_batch_size() -> usize {
    10
}

fn default_interval_seconds() -> i64 {
    // NOTE: As of 8/8/25 the retention loop takes ~4 minutes to complete on our
    // data plane with ~1mm objects so a 1 hour interval is reasonable for now.
    60 * 60 // 1 hour
}

fn default_disable_experiments_and_datasets() -> bool {
    false
}

fn default_disable_postgres_wal_deletes() -> bool {
    true
}

fn default_postgres_wal_deletion_batch_size() -> i64 {
    5000
}

fn default_postgres_wal_max_num_rows_per_object() -> u64 {
    50000
}

fn default_disable_realtime_wal_deletes() -> bool {
    false
}

fn default_realtime_wal_deletion_batch_size() -> usize {
    10000
}

fn default_realtime_wal_max_num_files_per_object() -> u64 {
    1000000
}

fn default_wal_deletion_log_batch_size() -> usize {
    1000
}

pub fn default_deleted_object_grace_period_days() -> i64 {
    7
}

fn retention_policy_attributes(tracking_id: &RetentionTrackingId) -> Vec<KeyValue> {
    vec![
        KeyValue::new("project_id", tracking_id.project_id.clone()),
        KeyValue::new("object_type", tracking_id.object_type.to_string()),
    ]
}

pub struct RetentionMeters {
    pub runs_started: Counter<u64>,
    pub runs_resumed: Counter<u64>,
    pub run_count: Counter<u64>,
    pub run_duration: Histogram<u64>,
    pub run_errors: Counter<u64>,

    pub iteration: OtelTimer,

    pub objects_considered: Counter<u64>,
    pub objects_processed: Counter<u64>,
    pub segments_processed: Counter<u64>,

    pub segment_batch: OtelTimer,
    pub segment_errors: Counter<u64>,
    pub segment_wal_entries_deleted: Counter<u64>,
    pub segment_index_docs_deleted: Counter<u64>,
    pub segment_write_locks: Counter<u64>,

    pub postgres_wal_batch: OtelTimer,
    pub postgres_wal_rows_deleted: Counter<u64>,
    pub postgres_wal_errors: Counter<u64>,

    pub realtime_wal_batch: OtelTimer,
    pub realtime_wal_files_deleted: Counter<u64>,
    pub realtime_wal_bytes_deleted: Counter<u64>,
    pub realtime_wal_errors: Counter<u64>,
}

impl Default for RetentionMeters {
    fn default() -> Self {
        let meter = global::meter("brainstore");
        Self {
            runs_started: meter
                .u64_counter("brainstore.storage.time_based_retention.runs_started")
                .with_description("Number of time-based retention runs started")
                .build(),
            runs_resumed: meter
                .u64_counter("brainstore.storage.time_based_retention.runs_resumed")
                .with_description("Number of time-based retention runs resumed from a previous checkpoint")
                .build(),
            run_count: meter
                .u64_counter("brainstore.storage.time_based_retention.run.count")
                .build(),
            run_duration: meter
                .u64_histogram("brainstore.storage.time_based_retention.run.duration_ms")
                .build(),
            run_errors: meter
                .u64_counter("brainstore.storage.time_based_retention.run.errors")
                .build(),

            iteration: OtelTimer::new(&meter, "brainstore.storage.time_based_retention.iteration"),

            objects_considered: meter
                .u64_counter("brainstore.storage.time_based_retention.objects_considered")
                .build(),
            objects_processed: meter
                .u64_counter("brainstore.storage.time_based_retention.objects_processed")
                .with_description("Total number of objects with active time-based retention policies that were processed")
                .build(),
            segments_processed: meter
                .u64_counter("brainstore.storage.time_based_retention.segments_processed")
                .build(),

            segment_batch: OtelTimer::new(&meter, "brainstore.storage.time_based_retention.segment.batch"),
            segment_errors: meter
                .u64_counter("brainstore.storage.time_based_retention.segment.errors")
                .build(),
            segment_wal_entries_deleted: meter
                .u64_counter("brainstore.storage.time_based_retention.segment.wal_entries_deleted")
                .build(),
            segment_index_docs_deleted: meter
                .u64_counter("brainstore.storage.time_based_retention.segment.index_docs_deleted")
                .build(),
            segment_write_locks: meter
                .u64_counter("brainstore.storage.time_based_retention.segment.write_locks")
                .with_description("Number of write locks acquired during index deletion operations")
                .build(),

            postgres_wal_batch: OtelTimer::new(&meter, "brainstore.storage.time_based_retention.postgres_wal.batch"),
            postgres_wal_rows_deleted: meter
                .u64_counter("brainstore.storage.time_based_retention.postgres_wal.rows_deleted")
                .build(),
            postgres_wal_errors: meter
                .u64_counter("brainstore.storage.time_based_retention.postgres_wal.errors")
                .build(),

            realtime_wal_batch: OtelTimer::new(&meter, "brainstore.storage.time_based_retention.realtime_wal.batch"),
            realtime_wal_files_deleted: meter
                .u64_counter("brainstore.storage.time_based_retention.realtime_wal.files_deleted")
                .build(),
            realtime_wal_bytes_deleted: meter
                .u64_counter("brainstore.storage.time_based_retention.realtime_wal.bytes_deleted")
                .build(),
            realtime_wal_errors: meter
                .u64_counter("brainstore.storage.time_based_retention.realtime_wal.errors")
                .build(),
        }
    }
}

lazy_static! {
    pub static ref RETENTION_METERS: RetentionMeters = RetentionMeters::default();
}

fn log_retention_stats(
    scope: &str,
    state: &TimeBasedRetentionState,
    stats: &TimeBasedRetentionStats,
) {
    tracing::info!(
        last_successful_start_ts = ?state.last_successful_start_ts,
        current_op_start_ts = ?state.current_op_start_ts,
        cursor = ?state.cursor,
        operation = ?state.operation,
        scope = scope,
        elapsed_ms = stats.elapsed_ms,
        num_objects = stats.num_processed_objects,
        num_segments = stats.num_processed_segments,
        num_deleted_wal_entries = ?stats.segment_stats.wal_stats.num_deleted_wal_entries,
        num_deleted_index_docs = ?stats.segment_stats.index_stats.num_deleted_index_docs,
        num_write_locks = ?stats.segment_stats.index_stats.num_write_locks,
        num_postgres_wal_deletes = stats.postgres_wal_stats.num_deletes,
        num_realtime_wal_deletes = stats.realtime_wal_stats.num_deletes,
        "Completed time-based retention {}",
        scope,
    );
}

#[tracing::instrument(err, skip(input, optional_input), fields(object_ids = ?input.object_ids))]
pub async fn time_based_retention_stateless<'a>(
    input: TimeBasedRetentionInput<'a>,
    optional_input: TimeBasedRetentionOptionalInput<'a>,
    options: &TimeBasedRetentionOptions,
) -> Result<TimeBasedRetentionStats> {
    // Validate that object_ids is provided and not empty
    let object_ids = input
        .object_ids
        .ok_or_else(|| anyhow!("object_ids is required for stateless retention operations"))?;
    if object_ids.is_empty() {
        tracing::debug!("No object IDs provided, skipping stateless time-based retention");
        return Ok(TimeBasedRetentionStats::default());
    }

    tracing::info!(
        num_objects = object_ids.len(),
        "Running stateless time-based retention",
    );

    let mut local_state = TimeBasedRetentionState {
        last_successful_start_ts: None,
        current_op_start_ts: Some(Utc::now()),
        cursor: None,
        operation: TimeBasedRetentionOperation::default(),
    };
    let mut total_stats = TimeBasedRetentionStats::default();

    loop {
        let result = retention_loop_iter(
            &mut local_state,
            RetentionLoopIterArgs {
                input: &input,
                optional_input: &optional_input,
                options,
            },
        )
        .await?;

        total_stats = total_stats + result.stats;

        if result.finished {
            break;
        }
    }

    tracing::info!(
        num_processed_objects = total_stats.num_processed_objects,
        num_processed_segments = total_stats.num_processed_segments,
        elapsed_ms = total_stats.elapsed_ms,
        wal_entries_deleted = total_stats.segment_stats.wal_stats.num_deleted_wal_entries,
        index_docs_deleted = total_stats.segment_stats.index_stats.num_deleted_index_docs,
        postgres_wal_rows_deleted = total_stats.postgres_wal_stats.num_deletes,
        realtime_wal_files_deleted = total_stats.realtime_wal_stats.num_deletes,
        dry_run = input.dry_run,
        "Completed stateless time-based retention run"
    );

    Ok(total_stats)
}

const LOCK_NAME: &str = "time_based_retention";

/// Run time-based retention on objects with active retention policies.
#[tracing::instrument(err, skip(input, optional_input), fields(object_ids = ?input.object_ids, dry_run = input.dry_run))]
pub async fn time_based_retention<'a>(
    input: TimeBasedRetentionInput<'a>,
    optional_input: TimeBasedRetentionOptionalInput<'a>,
    options: &TimeBasedRetentionOptions,
) -> Result<TimeBasedRetentionStats> {
    // Procedure:
    //
    // 1. If `current_op_start_ts` is null, there is no partially-completed run to resume, so start
    //    a new run. Reset the retention state and set `current_op_start_ts` to the current timestamp.
    // 2. Begin loop, resuming from the (object_id, segment_id) cursor, which may be null.
    //     - If the segment_id cursor is not nil, continue work on the current object by retrieving
    //       a new batch of segments for this object, up to a maximum of `segment_batch_size` segments.
    //       If this fills up a full batch of segments, skip to step 3.
    //     - If we need more segments, fetch object_ids past the object_id cursor. Look up their
    //       retention policies, discard objects without active retention policies, then fetch
    //       (object_id, segment_id) pairs for those objects. Continue in a loop until we fill up
    //       this batch of segments.
    //    We now have a batch of (object_id, retention_days, segment_ids) entries containing a maximum
    //    of `segment_batch_size` total segments.
    // 3. Process each entry in parallel; segment retention can be handled with a single call to
    //    `purge_segments_up_to_xact_id` per object. Also run postgres WAL and realtime WAL on each
    //    unique object in the batch.
    //     - If all of these tasks complete successfully, update the cursor. If any fail, write the
    //       error to the `operation` field and exit.
    // 4. Once we run out of segments to process, exit the loop.
    //     - To mark this run as completed, set last_successful_start_ts to this run's current_op_start_ts,
    //       then reset current_op_start_ts to null. Record any useful information from the completed run
    //       to the `operation` field.

    if let Some(object_ids) = input.object_ids {
        if object_ids.is_empty() {
            tracing::warn!("No object IDs provided, skipping time-based retention");
            return Ok(TimeBasedRetentionStats::default());
        }
    }

    let _lock = match input.config.locks_manager.try_write(LOCK_NAME).await? {
        Some(lock) => lock,
        None => {
            tracing::debug!(
                "Skipping time-based retention because another worker is already running it"
            );
            return Ok(TimeBasedRetentionStats::default());
        }
    };

    let input = &input;
    let optional_input = &optional_input;

    let mut state = input
        .config
        .global_store
        .query_time_based_retention_state()
        .await?;

    if state.current_op_start_ts.is_none() {
        // If we're starting a new run, first check that it's been long enough since the last successful
        // run's start time (last_successful_start_ts). If it has, then kick off a new run by setting
        // `current_op_start_ts` to the current time.
        let new_start_ts = Utc::now();
        let last_successful_start_ts = state.last_successful_start_ts;
        let duration_since_last_run =
            last_successful_start_ts.map(|ts| new_start_ts.signed_duration_since(ts));
        let interval_seconds = Duration::seconds(options.time_based_retention_interval_seconds);

        if let Some(duration_since_last_run) = duration_since_last_run {
            if duration_since_last_run < interval_seconds {
                let remaining_sleep_seconds =
                    (interval_seconds - duration_since_last_run).num_seconds();
                tracing::debug!(
                    duration_since_last_run = duration_since_last_run.num_seconds(),
                    remaining_sleep_seconds = remaining_sleep_seconds,
                    "Skipping this iteration of time-based retention",
                );
                return Ok(TimeBasedRetentionStats::default());
            }
        }

        state.current_op_start_ts = Some(new_start_ts);
        state.cursor = None;
        state.operation = TimeBasedRetentionOperation::default();

        input
            .config
            .global_store
            .upsert_time_based_retention_state(&state)
            .await?;

        RETENTION_METERS.runs_started.add(1, &[]);

        tracing::info!(
            start_ts = ?state.current_op_start_ts,
            "Starting new time-based retention run",
        );
    } else {
        RETENTION_METERS.runs_resumed.add(1, &[]);
        tracing::info!(
            start_ts = ?state.current_op_start_ts,
            "Continuing time-based retention run",
        );
    }

    let mut stats = state.operation.stats;

    loop {
        let result = retention_loop_iter(
            &mut state,
            RetentionLoopIterArgs {
                input,
                optional_input,
                options,
            },
        )
        .await;

        match result {
            Err(ref e) => {
                RETENTION_METERS.run_errors.add(1, &[]);
                tracing::error!(
                    last_successful_start_ts = ?state.last_successful_start_ts,
                    current_op_start_ts = ?state.current_op_start_ts,
                    cursor = ?state.cursor,
                    operation = ?state.operation,
                    error = ?e,
                    "Error running time-based retention",
                );
                state.operation = TimeBasedRetentionOperation {
                    error: Some(e.to_string()),
                    completed_ts: None,
                    stats,
                };
            }
            Ok(ref output) => {
                stats = stats + output.stats;

                log_retention_stats("iteration", &state, &output.stats);

                state.operation = TimeBasedRetentionOperation {
                    error: None,
                    completed_ts: None,
                    stats,
                };
            }
        }

        input
            .config
            .global_store
            .upsert_time_based_retention_state(&state)
            .await?;

        if result?.finished {
            break;
        }
    }

    // We're done. Set last_successful_start_ts to current_op_start_ts, then reset
    // current_op_start_ts to null to signify completion.
    let run_start_ts = state.current_op_start_ts;
    let run_end_ts = Utc::now();

    state.last_successful_start_ts = state.current_op_start_ts;
    state.current_op_start_ts = None;
    state.operation = TimeBasedRetentionOperation {
        error: None,
        completed_ts: Some(Utc::now()),
        stats,
    };
    input
        .config
        .global_store
        .upsert_time_based_retention_state(&state)
        .await?;

    RETENTION_METERS.run_count.add(1, &[]);
    if let Some(start_ts) = run_start_ts {
        let run_duration_ms = (run_end_ts - start_ts).num_milliseconds();
        RETENTION_METERS
            .run_duration
            .record(run_duration_ms as u64, &[]);
    }
    log_retention_stats("loop", &state, &stats);

    Ok(stats)
}

struct ObjectSegmentsBatch {
    object_id: FullObjectIdOwned,
    retention_target: ResolvedRetentionTarget,
    segment_ids: Vec<Uuid>,
    start_ts: DateTime<Utc>,
}

struct RetentionLoopIterArgs<'a> {
    input: &'a TimeBasedRetentionInput<'a>,
    optional_input: &'a TimeBasedRetentionOptionalInput<'a>,
    options: &'a TimeBasedRetentionOptions,
}

struct RetentionLoopIterOutput {
    finished: bool,
    stats: TimeBasedRetentionStats,
}

#[tracing::instrument(
    err,
    skip(state, args),
    fields(
        dry_run = args.input.dry_run,
        start_ts = ?state.current_op_start_ts,
        cursor = ?state.cursor,
    )
)]
async fn retention_loop_iter<'a>(
    state: &'a mut TimeBasedRetentionState,
    args: RetentionLoopIterArgs<'a>,
) -> Result<RetentionLoopIterOutput> {
    let start_time = Instant::now();
    let _iteration_timer = OtelCounterGuard::new(&RETENTION_METERS.iteration);

    let input = args.input;
    let optional_input = args.optional_input;
    let options = args.options;

    let object_batch_size = options.time_based_retention_object_batch_size;
    let segment_batch_size = options.time_based_retention_segment_batch_size;

    let start_ts = Utc::now();

    let mut object_segments_batches: Vec<ObjectSegmentsBatch> = Vec::new();
    let mut accumulated_retention_targets: HashMap<FullObjectIdOwned, ResolvedRetentionTarget> =
        HashMap::new();

    let mut num_processed_objects = 0;
    let mut num_processed_segments = 0;

    // If we have an non-null object cursor, first try to fill up the batch with additional
    // segments from that object.
    if let Some(cursor) = state.cursor.clone() {
        let current_object_segments = input
            .config
            .global_store
            .list_object_segment_ids(
                cursor.object_id.as_ref(),
                cursor.segment_id,
                segment_batch_size,
            )
            .await?;

        if current_object_segments.is_empty() {
            num_processed_objects += 1;
            tracing::debug!(
                object_id = %cursor.object_id,
                "No remaining segments for object",
            );
        } else {
            // We still have segments to process for this object, so get the retention policy and add a
            // batch of segments to per_object_batches.
            let object_ids = [cursor.object_id.as_ref()];
            let object_id_to_retention_targets = get_retention_targets_for_objects(
                input.control_plane_ctx,
                &object_ids,
                start_ts,
                optional_input.testing_object_id_to_retention_days.as_ref(),
                options.time_based_retention_disable_experiments_and_datasets,
                options.time_based_retention_deleted_object_grace_period_days,
            )
            .await?;
            let retention_target = object_id_to_retention_targets.get(&cursor.object_id);

            if let Some(retention_target) = retention_target {
                let num_current_object_segments = current_object_segments.len();
                num_processed_segments += num_current_object_segments;

                object_segments_batches.push(ObjectSegmentsBatch {
                    object_id: cursor.object_id.clone(),
                    retention_target: retention_target.clone(),
                    segment_ids: current_object_segments,
                    start_ts,
                });
                accumulated_retention_targets
                    .insert(cursor.object_id.clone(), retention_target.clone());

                tracing::debug!(
                    object_id = %cursor.object_id,
                    num_segments = num_current_object_segments,
                    min_retained_xact_id = %retention_target.min_retained_xact_id,
                    "Added segments to batch from object with retention policy",
                );
            }
        }
    }

    // If we need more segments, get them from additional objects in a loop until we've
    // reached the segments batch size.
    let mut last_object_id = state.cursor.as_ref().map(|cursor| cursor.object_id.clone());
    while num_processed_segments < segment_batch_size {
        let objects = input
            .config
            .global_store
            .list_object_ids(
                last_object_id.as_ref().map(|o| o.as_ref()),
                object_batch_size,
                input.object_ids,
            )
            .await?;
        last_object_id = objects.last().cloned();
        if last_object_id.is_none() {
            break;
        }

        RETENTION_METERS
            .objects_considered
            .add(objects.len() as u64, &[]);

        tracing::debug!(
            num_objects = objects.len(),
            "Fetched additional objects to fill batch",
        );

        // Get retention policies for the batch of objects, then discard any objects that
        // don't have active retention policies.
        let object_refs: Vec<FullObjectId> = objects.iter().map(|o| o.as_ref()).collect();
        let mut object_id_to_retention_targets = get_retention_targets_for_objects(
            input.control_plane_ctx,
            &object_refs,
            start_ts,
            optional_input.testing_object_id_to_retention_days.as_ref(),
            options.time_based_retention_disable_experiments_and_datasets,
            options.time_based_retention_deleted_object_grace_period_days,
        )
        .await?;

        let object_ids = object_id_to_retention_targets
            .keys()
            .map(|o| o.as_ref())
            .collect::<Vec<_>>();
        if object_ids.is_empty() {
            continue;
        }

        for (object_id, retention_target) in &object_id_to_retention_targets {
            accumulated_retention_targets.insert(object_id.clone(), retention_target.clone());
        }

        let num_additional_segments_needed = segment_batch_size - num_processed_segments;
        let object_ids_segment_ids = input
            .config
            .global_store
            .list_object_ids_segment_ids(&object_ids, num_additional_segments_needed)
            .await?;

        let mut object_id_to_segment_ids: HashMap<FullObjectIdOwned, Vec<Uuid>> = HashMap::new();
        for (object_id, segment_id) in object_ids_segment_ids {
            object_id_to_segment_ids
                .entry(object_id)
                .or_default()
                .push(segment_id);
        }

        for (object_id, segment_ids) in object_id_to_segment_ids {
            let retention_target = match object_id_to_retention_targets.remove(&object_id) {
                Some(retention_target) => retention_target,
                None => {
                    return Err(anyhow!(
                        "Unexpected: missing retention target for object ID {}. This should be impossible.",
                        object_id
                    ))
                }
            };
            let num_segments = segment_ids.len();
            num_processed_segments += num_segments;

            tracing::debug!(
                num_segments = num_segments,
                object_id = %object_id,
                retention_days = retention_target.policy_config.retention_days(),
                min_retained_xact_id = %retention_target.min_retained_xact_id,
                "Added segments to batch from object with retention policy",
            );

            object_segments_batches.push(ObjectSegmentsBatch {
                object_id: object_id.to_owned(),
                retention_target,
                segment_ids,
                start_ts,
            });
        }
    }

    let (batch_stats, postgres_wal_stats, realtime_wal_stats) = try_join3(
        // Retention for segments
        try_join_all(
            object_segments_batches
                .iter()
                .map(|batch| process_object_segments_batch(input, optional_input, options, batch)),
        ),
        // Retention for postgres WAL
        delete_from_postgres_wal_for_objects(input, options, &accumulated_retention_targets),
        // Retention for realtime WAL
        delete_from_realtime_wal_for_objects(input, options, &accumulated_retention_targets),
    )
    .await?;
    let segment_stats: DeleteFromSegmentStats = batch_stats.into_iter().sum();

    tracing::debug!(
        num_batches = object_segments_batches.len(),
        num_segments = num_processed_segments,
        "Ran time-based retention on objects",
    );

    if !object_segments_batches.is_empty() {
        // We finished processing all objects except the last one, which will be checked for
        // additional segments on the next iteration before moving on.
        num_processed_objects += object_segments_batches.len() - 1;

        // Record objects_processed metric per retention target (excluding the last object)
        for batch in &object_segments_batches[..object_segments_batches.len() - 1] {
            let attrs = retention_policy_attributes(&batch.retention_target.tracking_id);
            RETENTION_METERS.objects_processed.add(1, &attrs);
        }

        // Update the cursor to the last processed (object_id, segment_id) pair.
        let last_batch = object_segments_batches
            .last()
            .expect("Impossible: per_object_batches is empty");
        let last_segment_id = last_batch
            .segment_ids
            .last()
            .expect("Impossible: batch has empty segment_ids");
        state.cursor = Some(TimeBasedRetentionCursor {
            object_id: last_batch.object_id.to_owned(),
            segment_id: *last_segment_id,
        });
    }

    for batch in &object_segments_batches {
        let attrs = retention_policy_attributes(&batch.retention_target.tracking_id);
        RETENTION_METERS
            .segments_processed
            .add(batch.segment_ids.len() as u64, &attrs);
    }

    let elapsed_ms = start_time.elapsed().as_millis() as u64;
    Ok(RetentionLoopIterOutput {
        finished: object_segments_batches.is_empty(),
        stats: TimeBasedRetentionStats {
            num_processed_objects: num_processed_objects as u64,
            num_processed_segments: num_processed_segments as u64,
            elapsed_ms,
            segment_stats,
            postgres_wal_stats,
            realtime_wal_stats,
        },
    })
}

#[tracing::instrument(
    err,
    skip(control_plane_ctx, object_ids, testing_object_id_to_retention_days),
    fields(
        num_object_ids = object_ids.len(),
    )
)]
async fn get_retention_targets_for_objects<'a, 'b>(
    control_plane_ctx: Option<&'a ControlPlaneContext>,
    object_ids: &'b [FullObjectId<'b>],
    start_ts: DateTime<Utc>,
    testing_object_id_to_retention_days: Option<&HashMap<FullObjectId<'b>, i64>>,
    disable_experiments_and_datasets: bool,
    deleted_object_grace_period_days: i64,
) -> Result<HashMap<FullObjectIdOwned, ResolvedRetentionTarget>> {
    if let Some(ref testing_policies) = testing_object_id_to_retention_days {
        Ok(object_ids
            .iter()
            .filter_map(|&object_id| {
                testing_policies.get(&object_id).map(|&retention_days| {
                    let tracking_id = RetentionTrackingId {
                        // This is not necessarily accurate, but this block is purely for testing
                        // and we don't use this field in existing tests.
                        project_id: object_id.object_id.to_string(),
                        object_type: object_id.object_type,
                    };
                    let min_retained_xact_id =
                        compute_min_retained_xact_id(start_ts, retention_days);
                    let policy_config = RetentionPolicyConfig::new(None, retention_days).unwrap();
                    let retention_target = ResolvedRetentionTarget {
                        policy_config,
                        min_retained_xact_id,
                        tracking_id,
                    };
                    (object_id.to_owned(), retention_target)
                })
            })
            .collect())
    } else {
        let control_plane_ctx = control_plane_ctx.ok_or(anyhow!(
            "Control plane context is required for retention policy resolution"
        ))?;
        let retention_objects: Vec<_> = object_ids
            .iter()
            .filter_map(|&object_id| {
                RetentionObjectType::try_from(object_id.object_type)
                    .ok()
                    .map(|retention_type| RetentionObject {
                        object_type: retention_type,
                        object_id: object_id.object_id.to_owned(),
                    })
            })
            .collect();
        let retention_targets = resolve_retention_policies(
            ResolveRetentionPoliciesInput {
                control_plane_ctx,
                objects: retention_objects,
                start_ts: Some(start_ts),
            },
            ResolveRetentionPoliciesOptionalInput {
                disable_experiments_and_datasets,
            },
            ResolveRetentionPoliciesOptions {
                deleted_object_grace_period_days,
            },
        )
        .await?;

        tracing::debug!(
            num_object_ids = object_ids.len(),
            num_retention_targets = retention_targets.len(),
            "Resolved retention policies for object IDs"
        );

        Ok(retention_targets)
    }
}

/// Process segments for a single object with a single retention policy.
#[tracing::instrument(
    err,
    skip(input, optional_input, batch),
    fields(
        dry_run = input.dry_run,
        object_id = %batch.object_id,
        num_segments = batch.segment_ids.len(),
    )
)]
async fn process_object_segments_batch(
    input: &TimeBasedRetentionInput<'_>,
    optional_input: &TimeBasedRetentionOptionalInput<'_>,
    options: &TimeBasedRetentionOptions,
    batch: &ObjectSegmentsBatch,
) -> Result<DeleteFromSegmentStats> {
    let _timer = OtelCounterGuard::new(&RETENTION_METERS.segment_batch);

    let attrs = retention_policy_attributes(&batch.retention_target.tracking_id);

    if optional_input.testing_force_error_for_object_id == Some(batch.object_id.as_ref()) {
        return Err(anyhow!("Forced error for object ID {}", batch.object_id));
    }
    for &segment_id in &batch.segment_ids {
        if optional_input.testing_force_error_for_segment_id == Some(segment_id) {
            return Err(anyhow!("Forced error for segment ID {}", segment_id));
        }
    }

    let schema = &match input.config_file_schema {
        Some(schema) => make_full_schema(schema)?,
        None => make_full_schema(&make_object_schema(batch.object_id.object_type)?)?,
    };

    let min_retained_xact_id = batch.retention_target.min_retained_xact_id;

    tracing::info!(
        object_id = %batch.object_id,
        num_segments = batch.segment_ids.len(),
        min_retained_xact_id = %min_retained_xact_id,
        "Running time-based retention on segments batch",
    );

    let result = delete_from_segments_up_to_xact_id(
        DeleteFromSegmentsInput {
            segment_ids: &batch.segment_ids,
            index_store: &input.config.index,
            schema,
            global_store: input.config.global_store.clone(),
            locks_manager: input.config.locks_manager.clone(),
            min_retained_xact_id,
            dry_run: input.dry_run,
        },
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await;

    let (batch_stats, completed_ts, error) = match &result {
        Ok(stats) => {
            RETENTION_METERS
                .segment_wal_entries_deleted
                .add(stats.wal_stats.num_deleted_wal_entries, &attrs);
            RETENTION_METERS
                .segment_index_docs_deleted
                .add(stats.index_stats.num_deleted_index_docs, &attrs);
            RETENTION_METERS
                .segment_write_locks
                .add(stats.index_stats.num_write_locks, &attrs);

            (Some(stats.clone()), Some(Utc::now()), None)
        }
        Err(e) => {
            RETENTION_METERS.segment_errors.add(1, &attrs);
            (None, None, Some(e.to_string()))
        }
    };

    tracing::info!(
        batch_stats = ?batch_stats,
        completed_ts = ?completed_ts,
        error = ?error,
        "Completed time-based retention on segments batch",
    );

    input
        .config
        .global_store
        .upsert_segment_task_info(
            &batch.segment_ids,
            &TaskInfo::TimeBasedRetention(TimeBasedRetentionInfo {
                start_ts: batch.start_ts,
                completed_ts,
                retention_days: batch.retention_target.policy_config.retention_days(),
                min_retained_xact_id,
                error,
                batch_stats,
            }),
        )
        .await?;

    result
}

#[tracing::instrument(
    err,
    skip(input, object_id_to_retention_targets),
    fields(
        dry_run = input.dry_run,
        num_objects = object_id_to_retention_targets.len(),
    )
)]
async fn delete_from_postgres_wal_for_objects(
    input: &TimeBasedRetentionInput<'_>,
    options: &TimeBasedRetentionOptions,
    object_id_to_retention_targets: &HashMap<FullObjectIdOwned, ResolvedRetentionTarget>,
) -> Result<DeleteFromWalStats> {
    if options.time_based_retention_disable_postgres_wal_deletes {
        tracing::debug!("Skipping postgres WAL retention because it is disabled");
        return Ok(DeleteFromWalStats::default());
    }

    let _timer = OtelCounterGuard::new(&RETENTION_METERS.postgres_wal_batch);
    tracing::info!(
        object_ids = ?object_id_to_retention_targets.keys(),
        "Running postgres WAL retention on objects",
    );

    let mut stats = DeleteFromWalStats::default();

    // We are not running these concurrently to avoid putting too much pressure on postgres.
    for (object_id, retention_target) in object_id_to_retention_targets.iter() {
        let attrs = retention_policy_attributes(&retention_target.tracking_id);
        let output = match input
            .config
            .wal
            .delete_up_to_xact_id(
                DeleteUpToXactIdInput {
                    scope: WALScope::ObjectId(object_id.as_ref(), Uuid::nil()),
                    min_retained_xact_id: retention_target.min_retained_xact_id,
                    dry_run: input.dry_run,
                },
                &DeleteUpToXactIdOptions {
                    batch_size: options.time_based_retention_postgres_wal_deletion_batch_size,
                    max_num_rows: options.time_based_retention_postgres_wal_max_num_rows_per_object,
                    deletion_log_batch_size: options
                        .time_based_retention_wal_deletion_log_batch_size,
                },
            )
            .await
        {
            Ok(output) => {
                RETENTION_METERS
                    .postgres_wal_rows_deleted
                    .add(output.num_deletes, &attrs);
                output
            }
            Err(e) => {
                RETENTION_METERS.postgres_wal_errors.add(1, &attrs);
                return Err(e);
            }
        };

        stats = stats + output;
    }

    tracing::info!(
        num_objects = object_id_to_retention_targets.len(),
        num_deletes = stats.num_deletes,
        "Ran retention on postgres WAL for objects"
    );

    Ok(stats)
}

#[tracing::instrument(
    err,
    skip(input, object_id_to_retention_targets),
    fields(
        dry_run = input.dry_run,
        num_objects = object_id_to_retention_targets.len(),
    )
)]
async fn delete_from_realtime_wal_for_objects(
    input: &TimeBasedRetentionInput<'_>,
    options: &TimeBasedRetentionOptions,
    object_id_to_retention_targets: &HashMap<FullObjectIdOwned, ResolvedRetentionTarget>,
) -> Result<DeleteFromWalStats> {
    if options.time_based_retention_disable_realtime_wal_deletes {
        tracing::debug!("Skipping realtime WAL retention because it is disabled");
        return Ok(DeleteFromWalStats::default());
    }

    let Some(realtime_wal) = &input.config.realtime_wal else {
        return Err(anyhow!(
            "No realtime WAL configured for realtime WAL retention"
        ));
    };

    let _timer = OtelCounterGuard::new(&RETENTION_METERS.realtime_wal_batch);
    tracing::info!(
        object_ids = ?object_id_to_retention_targets.keys(),
        "Running realtime WAL retention on objects",
    );

    let object_ids: Vec<_> = object_id_to_retention_targets.keys().collect();
    let object_metadatas = input
        .config
        .global_store
        .query_object_metadatas(&object_ids.iter().map(|id| id.as_ref()).collect::<Vec<_>>())
        .await?;
    let object_id_to_wal_token: HashMap<FullObjectId<'_>, Uuid> = object_ids
        .iter()
        .zip(object_metadatas.into_iter())
        .map(|(object_id, metadata)| (object_id.as_ref(), metadata.wal_token))
        .collect();

    let mut stats = DeleteFromWalStats::default();

    for (object_id, retention_target) in object_id_to_retention_targets.iter() {
        let attrs = retention_policy_attributes(&retention_target.tracking_id);
        let wal_token = match object_id_to_wal_token.get(&object_id.as_ref()) {
            Some(token) => *token,
            None => {
                RETENTION_METERS.realtime_wal_errors.add(1, &attrs);
                return Err(anyhow!(
                    "UNEXPECTED: no object metadata found for object ID '{}' during realtime WAL deletion",
                    object_id
                ));
            }
        };
        let wal_scope = WALScope::ObjectId(object_id.as_ref(), wal_token);

        let output = match realtime_wal
            .delete_up_to_xact_id(
                DeleteUpToXactIdInput {
                    scope: wal_scope,
                    min_retained_xact_id: retention_target.min_retained_xact_id,
                    dry_run: input.dry_run,
                },
                &DeleteUpToXactIdOptions {
                    batch_size: options.time_based_retention_realtime_wal_deletion_batch_size
                        as i64,
                    max_num_rows: options
                        .time_based_retention_realtime_wal_max_num_files_per_object,
                    deletion_log_batch_size: options
                        .time_based_retention_wal_deletion_log_batch_size,
                },
            )
            .await
        {
            Ok(output) => {
                RETENTION_METERS
                    .realtime_wal_files_deleted
                    .add(output.num_deletes, &attrs);
                RETENTION_METERS
                    .realtime_wal_bytes_deleted
                    .add(output.total_bytes, &attrs);
                output
            }
            Err(e) => {
                RETENTION_METERS.realtime_wal_errors.add(1, &attrs);
                return Err(e);
            }
        };
        stats = stats + output;

        tracing::debug!(
            object_id = %object_id,
            num_deletes = output.num_deletes,
            "Ran realtime WAL retention on object"
        );
    }

    tracing::info!(
        num_objects = object_id_to_retention_targets.len(),
        num_deletes = stats.num_deletes,
        "Ran realtime WAL retention on batch of objects"
    );

    Ok(stats)
}
