/// hashbrown is a drop-in replacement for the standard library HashMap that supports the raw_entry
/// API, which is useful for querying a hashtable with non-owned versions `Q` of the key type `K`,
/// when <PERSON> cannot implement `Borrow<Q>`.
///
/// A common example is when the key type is an "owned" version of a struct (e.g.
/// `FullObjectIdOwned`) and we want to query it with a `FullObjectId<'a>`.
pub type HashMap<K, V, S = hashbrown::DefaultHashBuilder> = hashbrown::HashMap<K, V, S>;
pub type RawEntryMut<'a, K, V, S> = hashbrown::hash_map::RawEntryMut<'a, K, V, S>;
pub type Entry<'a, K, V, S> = hashbrown::hash_map::Entry<'a, K, V, S>;

/// A convenience trait that provides access to the `new()` and `with_capacity()` methods for the
/// [`HashMap`] type alias.
pub trait HashMapExt {
    /// Constructs a new HashMap.
    fn new() -> Self;
    /// Constructs a new HashMap with a given initial capacity.
    fn with_capacity(capacity: usize) -> Self;
}

impl<K, V, S> HashMapExt for HashMap<K, V, S>
where
    S: std::hash::BuildHasher + Default,
{
    fn new() -> Self {
        HashMap::with_hasher(S::default())
    }

    fn with_capacity(capacity: usize) -> Self {
        HashMap::with_capacity_and_hasher(capacity, S::default())
    }
}

/// Types which implement this trait can use the raw_entry API to query a hashtable with key type
/// `K`.
pub trait RawEntryCompatible<K>: Eq + std::hash::Hash {
    fn convert_to_key(&self) -> K;
    fn is_match(&self, other: &K) -> bool;
}

impl<K, Q> RawEntryCompatible<K> for Q
where
    Q: ToOwned<Owned = K> + Eq + std::hash::Hash,
    K: std::borrow::Borrow<Q>,
{
    fn convert_to_key(&self) -> K {
        self.to_owned()
    }

    fn is_match(&self, other: &K) -> bool {
        self == other.borrow()
    }
}

pub fn raw_entry_from_hash<'a, K, V, S, Q>(
    map: &'a HashMap<K, V, S>,
    key: &Q,
) -> Option<(&'a K, &'a V)>
where
    Q: RawEntryCompatible<K>,
    S: std::hash::BuildHasher,
{
    map.raw_entry()
        .from_hash(map.hasher().hash_one(key), |k| key.is_match(k))
}

pub fn raw_entry_mut_from_hash<'a, K, V, S, Q>(
    map: &'a mut HashMap<K, V, S>,
    key: &Q,
) -> RawEntryMut<'a, K, V, S>
where
    Q: RawEntryCompatible<K>,
    S: std::hash::BuildHasher,
{
    let hash_value = map.hasher().hash_one(key);
    map.raw_entry_mut()
        .from_hash(hash_value, |k| key.is_match(k))
}
