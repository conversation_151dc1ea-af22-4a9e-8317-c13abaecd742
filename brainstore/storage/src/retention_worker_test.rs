use serde_json::json;

use util::{
    anyhow::Result,
    chrono::{DateTime, Duration, Utc},
    itertools::iproduct,
    system_types::{FullObjectId, FullObjectIdOwned, ObjectId, ObjectType},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    global_store::{
        TestingOnlyBackfillBrainstoreObjectAtom, TimeBasedRetentionCursor, TimeBasedRetentionState,
    },
    process_wal::{compact_segment_wal, process_object_wal, ProcessObjectWalInput},
    retention_test::{run_test_with_global_stores, PostgresWalRetentionTestArgs, TestFixture},
    retention_worker::{
        time_based_retention, time_based_retention_stateless, TimeBasedRetentionInput,
        TimeBasedRetentionOptionalInput, TimeBasedRetentionOptions,
    },
    test_util::{collect_wal_stream, make_compacted_wal_entries},
    wal::{wal_stream, WALScope, WalMetadataStreamOptionalInput},
    wal_entry::WalEntry,
    wal_test_util::insert_object_atoms_into_wal,
};

struct WalEntriesArgs<'a> {
    full_object_id: FullObjectIdOwned,
    ts: DateTime<Utc>,
    row_id_suffix: &'a str,
}

fn basic_wal_entries(args: WalEntriesArgs<'_>) -> Vec<WalEntry> {
    let timestamp = args.ts.timestamp() as u64;
    let xact_id_0 = TransactionId::from_timestamp(timestamp, 0);
    let xact_id_1 = TransactionId::from_timestamp(timestamp, 1);

    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: xact_id_0,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            _is_merge: Some(true),
            id: format!("row0-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value0-{}", args.row_id_suffix),
                "field2": 0,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: xact_id_0,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: format!("row1-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value1-{}", args.row_id_suffix),
                "field2": 1,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(3000),
            _is_merge: Some(true),
            id: format!("row1-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("new-value1-{}", args.row_id_suffix),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id,
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            _is_merge: Some(true),
            id: format!("row2-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value2-{}", args.row_id_suffix),
                "field2": 2,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn compacted_wal_entries(args: WalEntriesArgs<'_>) -> Vec<WalEntry> {
    let timestamp = args.ts.timestamp() as u64;
    let xact_id_0 = TransactionId::from_timestamp(timestamp, 0);
    let xact_id_1 = TransactionId::from_timestamp(timestamp, 1);

    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: xact_id_0,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            id: format!("row0-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value0-{}", args.row_id_suffix),
                "field2": 0,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id.clone(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: format!("row1-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("new-value1-{}", args.row_id_suffix),
                "field2": 1,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: xact_id_1,
            _object_type: args.full_object_id.object_type,
            _object_id: args.full_object_id.object_id,
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            id: format!("row2-{}", args.row_id_suffix),
            data: json!({
                "field1": format!("value2-{}", args.row_id_suffix),
                "field2": 2,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

#[tokio::test]
async fn test_time_based_retention_worker() -> Result<()> {
    run_test_with_global_stores(test_time_based_retention_worker_inner).await
}

async fn test_time_based_retention_worker_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let object_id = FullObjectId::default();
    let segment_id = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;

    // Create WAL entries with timestamps 31 and 29 days ago
    let now = Utc::now();
    let old_ts = now - Duration::days(31);
    let new_ts = now - Duration::days(29);
    let old_entries = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: old_ts,
        row_id_suffix: "31d",
    });
    let new_entries = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: new_ts,
        row_id_suffix: "29d",
    });
    fixture.write_wal_to_segment(segment_id, old_entries).await;
    fixture.write_wal_to_segment(segment_id, new_entries).await;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4); // 4 transactions (2 per batch) with 2 entries each
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    let mut expected_compacted_wal_entries = compacted_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: old_ts,
        row_id_suffix: "31d",
    });
    expected_compacted_wal_entries.extend(compacted_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: new_ts,
        row_id_suffix: "29d",
    }));
    let expected_docs = make_compacted_wal_entries(expected_compacted_wal_entries);
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 6);
    assert_hashmap_eq(&docs, &expected_docs);

    // Run retention with 32 day policy. Nothing is more than 32 days old so the WAL and index
    // should not be affected.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 32)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions::default(),
    )
    .await?;
    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4);
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    assert_eq!(wal_entries[0].1[0].id, "row0-31d");
    assert_eq!(wal_entries[0].1[1].id, "row1-31d");
    assert_eq!(wal_entries[1].1[0].id, "row1-31d");
    assert_eq!(wal_entries[1].1[1].id, "row2-31d");
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 6);
    assert_hashmap_eq(&docs, &expected_docs);

    // Apply a 30-day retention policy. Because retention just ran, the worker shouldn't
    // wake up again just yet.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions::default(),
    )
    .await?;
    assert_eq!(output.num_processed_objects, 0);
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4);
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    assert_eq!(wal_entries[0].1[0].id, "row0-31d");
    assert_eq!(wal_entries[0].1[1].id, "row1-31d");
    assert_eq!(wal_entries[1].1[0].id, "row1-31d");
    assert_eq!(wal_entries[1].1[1].id, "row2-31d");
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 6);
    assert_hashmap_eq(&docs, &expected_docs);

    // Now run with a zero interval for the retention worker, but do a dry run.
    // We should see the correct number of planned deletes but nothing should
    // actually be deleted.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: true,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(
        output
            .segment_stats
            .wal_stats
            .planned_num_deleted_wal_entries,
        2
    );
    assert_eq!(
        output
            .segment_stats
            .index_stats
            .planned_num_deleted_index_docs,
        3
    );
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4);
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }

    // Since we're working with a dataset, setting `disable_experiments_and_datasets` should prevent
    // retention from deleting anything.
    let object_id_experiment = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: object_id.object_id,
    };
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id_experiment]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id_experiment, 30)].into_iter().collect(),
            ),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_disable_experiments_and_datasets: true,
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 0); // Should skip experiments when disabled
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 0);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 0);
    assert_eq!(
        output
            .segment_stats
            .wal_stats
            .planned_num_deleted_wal_entries,
        0
    );
    assert_eq!(
        output
            .segment_stats
            .index_stats
            .planned_num_deleted_index_docs,
        0
    );
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 0);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 4); // Data should remain unchanged
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }

    // Now do a real run with a zero interval for the retention worker. The 30 day
    // old rows should be deleted out of the segment WAL as well as the tantivy index.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 2);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 3);
    assert_eq!(
        output
            .segment_stats
            .wal_stats
            .planned_num_deleted_wal_entries,
        2
    );
    assert_eq!(
        output
            .segment_stats
            .index_stats
            .planned_num_deleted_index_docs,
        3
    );
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 1);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2); // Only 29 day old entries remain
    for wal_entry in wal_entries.iter() {
        assert_eq!(wal_entry.1.len(), 2);
    }
    assert_eq!(wal_entries[0].1[0].id, "row0-29d");
    assert_eq!(wal_entries[0].1[1].id, "row1-29d");
    assert_eq!(wal_entries[1].1[0].id, "row1-29d");
    assert_eq!(wal_entries[1].1[1].id, "row2-29d");
    let expected_compacted_wal_entries = compacted_wal_entries(WalEntriesArgs {
        full_object_id: object_id.to_owned(),
        ts: new_ts,
        row_id_suffix: "29d",
    });
    let expected_docs = make_compacted_wal_entries(expected_compacted_wal_entries);
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    assert_hashmap_eq(&docs, &expected_docs);

    // Now apply a 28-day retention policy. All rows should be deleted from the WAL
    // and tantivy index.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 28)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await?;

    assert_eq!(output.num_processed_objects, 1);
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.segment_stats.wal_stats.num_deleted_wal_entries, 2);
    assert_eq!(output.segment_stats.index_stats.num_deleted_index_docs, 3);
    assert_eq!(output.segment_stats.index_stats.num_write_locks, 1);
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert!(wal_entries.is_empty());

    let docs = fixture.read_segment_docs(segment_id).await;
    assert!(docs.is_empty());

    Ok(())
}

#[tokio::test]
async fn test_time_based_retention_cursor() -> Result<()> {
    run_test_with_global_stores(test_time_based_retention_cursor_inner).await
}

async fn test_time_based_retention_cursor_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    // Create three objects with segments
    let object_id1 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj1").unwrap(),
    };

    let object_id2 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj2").unwrap(),
    };
    let object_id3 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj3").unwrap(),
    };

    let mut segment_ids = vec![
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
    ];
    segment_ids.sort();
    let [segment_id1, segment_id2, segment_id3, segment_id4, segment_id5] =
        segment_ids.try_into().unwrap();

    // Initialize segments for each object
    fixture
        .initialize_segment_metadata_in_object(object_id1, segment_id1)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id2, segment_id2)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id2, segment_id3)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id3, segment_id4)
        .await;
    fixture
        .initialize_segment_metadata_in_object(object_id3, segment_id5)
        .await;

    // Add some old data to all segments
    let now = Utc::now();
    let thirty_one_days_ago = now - Duration::days(31);

    let entries1 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id1.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "1",
    });
    let entries2 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id2.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "2",
    });
    let entries3 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id2.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "3",
    });
    let entries4 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id3.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "4",
    });
    let entries5 = basic_wal_entries(WalEntriesArgs {
        full_object_id: object_id3.to_owned(),
        ts: thirty_one_days_ago,
        row_id_suffix: "5",
    });

    fixture.write_wal_to_segment(segment_id1, entries1).await;
    fixture.write_wal_to_segment(segment_id2, entries2).await;
    fixture.write_wal_to_segment(segment_id3, entries3).await;
    fixture.write_wal_to_segment(segment_id4, entries4).await;
    fixture.write_wal_to_segment(segment_id5, entries5).await;

    for object_id in [object_id1, object_id2, object_id3] {
        process_object_wal(
            ProcessObjectWalInput {
                object_id: object_id,
                config: &fixture.config(),
            },
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    for segment_id in [
        segment_id1,
        segment_id2,
        segment_id3,
        segment_id4,
        segment_id5,
    ] {
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    // Force an error on object_id1 with a batch size of 1. The cursor shouldn't
    // move since we didn't complete any objects.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: Some(object_id1),
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert!(state.last_successful_start_ts.is_none());
    assert!(state.cursor.is_none());
    assert!(state.current_op_start_ts.is_some());
    let current_op_start_ts = state.current_op_start_ts.unwrap();
    assert!(state.operation.error.is_some());

    // Force an error on object_id2 when using an object batch size of 2. Since we
    // still made it past object_id1, the cursor should move to (object_id1, nil).
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 1), (object_id2, 7), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: Some(object_id2),
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 2,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(
        state.cursor,
        Some(TimeBasedRetentionCursor {
            object_id: object_id1.to_owned(),
            segment_id: segment_id1,
        })
    );
    assert!(state.last_successful_start_ts.is_none());
    assert_eq!(state.current_op_start_ts, Some(current_op_start_ts));
    assert!(state.operation.error.is_some());

    // Now run the retention loop to completion.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 60), (object_id3, 90)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_ok());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(state.last_successful_start_ts, Some(current_op_start_ts));
    let last_successful_start_ts = state.last_successful_start_ts;
    // After completion, current_op_start_ts should be set to null.
    assert!(state.current_op_start_ts.is_none());
    assert!(state.operation.completed_ts.is_some());

    // Reset the time-based retention state to prepare for the next batch of tests,
    // but leave last_successful_start_ts intact.
    fixture
        .config()
        .global_store
        .upsert_time_based_retention_state(&TimeBasedRetentionState {
            last_successful_start_ts,
            ..Default::default()
        })
        .await?;

    let before_start_ts = Utc::now();

    // Force an error at the segment level for segment_id3 when using a batch size of 2.
    // The cursor should advance to (object_id1, segment_id1) since the first object completed.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: None,
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: Some(segment_id3),
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 2,
            // Set the interval to 0 to run the retention loop immediately.
            time_based_retention_interval_seconds: 0,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(
        state.cursor,
        Some(TimeBasedRetentionCursor {
            object_id: object_id2.to_owned(),
            segment_id: segment_id2,
        })
    );
    assert_eq!(state.last_successful_start_ts, last_successful_start_ts);
    assert!(state.operation.error.is_some());
    assert!(state.operation.completed_ts.is_none());

    // Mark the current_op_start_ts of this failed run. It should remain the current_op_start_ts until the run is fully completed.
    let current_op_start_ts = state.current_op_start_ts;
    assert!(current_op_start_ts.is_some());
    assert!(current_op_start_ts.unwrap() > before_start_ts);

    // Force an error for segment_id5 but use a batch size of 1.
    // The cursor should advance to (object_id3, segment_id4) since we processed object_id2 completely
    // and started on object_id3.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: None,
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: Some(segment_id5),
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 1,
            time_based_retention_segment_batch_size: 1,
            ..Default::default()
        },
    )
    .await;
    assert!(output.is_err());
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    assert_eq!(
        state.cursor,
        Some(TimeBasedRetentionCursor {
            object_id: object_id3.to_owned(),
            segment_id: segment_id4,
        })
    );
    assert_eq!(state.last_successful_start_ts, last_successful_start_ts);
    assert_eq!(state.current_op_start_ts, current_op_start_ts);

    let before_complete_ts = Utc::now();

    // Complete the remaining objects and test that the cursor advances
    // to the end and the state is marked complete.
    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id1, object_id2, object_id3]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some(
                [(object_id1, 30), (object_id2, 30), (object_id3, 30)]
                    .into_iter()
                    .collect(),
            ),
            testing_force_error_for_object_id: None,
            testing_force_error_for_segment_id: None,
        },
        &TimeBasedRetentionOptions {
            time_based_retention_object_batch_size: 10,
            time_based_retention_segment_batch_size: 10,
            ..Default::default()
        },
    )
    .await?;
    assert_eq!(output.num_processed_objects, 3); // All objects have now been processed.
    assert_eq!(output.num_processed_segments, 5); // All segments have now been processed.
    let state = fixture
        .config()
        .global_store
        .query_time_based_retention_state()
        .await?;
    // The last_successful_start_ts should now be updated.
    assert_eq!(state.last_successful_start_ts, current_op_start_ts);
    assert!(state.current_op_start_ts.is_none());
    // The completed_ts should have been marked during the last run.
    assert!(state.operation.completed_ts.unwrap() > before_complete_ts);
    // The most recent error should have been cleared by the successful iteration.
    assert!(state.operation.error.is_none());

    // Verify all segment WALs and tantivy indices are empty.
    for segment_id in [
        segment_id1,
        segment_id2,
        segment_id3,
        segment_id4,
        segment_id5,
    ] {
        let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
        assert!(wal_entries.is_empty());

        let docs = fixture.read_segment_docs(segment_id).await;
        assert!(docs.is_empty());
    }

    Ok(())
}

#[tokio::test]
async fn test_postgres_wal_retention_worker() -> Result<()> {
    for (use_logs2, object_type) in iproduct!(
        [false, true],
        [
            ObjectType::ProjectLogs,
            ObjectType::Experiment,
            ObjectType::Dataset
        ]
    ) {
        test_postgres_wal_retention_worker_inner(PostgresWalRetentionTestArgs {
            use_logs2,
            object_type,
        })
        .await?;
    }
    Ok(())
}

async fn test_postgres_wal_retention_worker_inner(
    args: PostgresWalRetentionTestArgs,
) -> Result<()> {
    let fixture = TestFixture::new_with_postgres_wal(args.use_logs2).await;
    let global_store = fixture.config().global_store.clone();
    let postgres_wal = fixture.config().wal.clone();

    let project_id = Uuid::new_v4().to_string();
    let object_id = FullObjectId {
        object_type: args.object_type,
        object_id: ObjectId::new(&project_id).unwrap(),
    };

    let now = Utc::now();
    let old_timestamp = (now - Duration::days(31)).timestamp() as u64; // 31 days ago
    let recent_timestamp = (now - Duration::days(29)).timestamp() as u64; // 29 days ago

    let old_xact_id = TransactionId::from_timestamp(old_timestamp, 0);
    let recent_xact_id = TransactionId::from_timestamp(recent_timestamp, 0);

    // These objects will be deleted after applying a 30-day retention policy.
    let old_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 100,
            xact_id: old_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 101,
            xact_id: old_xact_id,
        },
    ];
    // These objects shouldn't be affected.
    let new_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 200,
            xact_id: recent_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: args.use_logs2,
            sequence_id: 201,
            xact_id: recent_xact_id,
        },
    ];
    let all_objects = [old_objects.clone(), new_objects.clone()].concat();
    let num_objects = all_objects.len();

    // Insert the objects into the Postgres WAL.
    insert_object_atoms_into_wal(
        all_objects,
        vec![],
        postgres_wal.clone(),
        global_store.clone(),
        false,
    )
    .await?;

    // Initialize metadata so the retention worker will pick up this object.
    let segment_id = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;
    process_object_wal(
        ProcessObjectWalInput {
            object_id: object_id,
            config: &fixture.config(),
        },
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let num_entries = wal_entries
        .iter()
        .map(|(_, entries)| entries.len())
        .sum::<usize>();
    assert_eq!(
        num_entries, num_objects,
        "Expected to see all inserted WAL entries before retention",
    );

    // Run time-based retention with a 30-day retention policy.
    let retention_result = time_based_retention_stateless(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 30)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions {
            time_based_retention_disable_postgres_wal_deletes: false,
            ..Default::default()
        },
    )
    .await?;
    assert!(
        retention_result
            .segment_stats
            .wal_stats
            .num_deleted_wal_entries
            > 0
    );
    assert!(
        retention_result
            .segment_stats
            .index_stats
            .num_deleted_index_docs
            > 0
    );
    assert_eq!(
        retention_result.postgres_wal_stats.num_deletes,
        old_objects.len() as u64,
        "Expected to delete postgres WAL entries older than the retention cutoff",
    );

    // Verify that retention ran correctly on the postgres WAL.
    let wal_entries_after = collect_wal_stream(wal_stream(
        postgres_wal
            .wal_metadata_stream(
                WALScope::ObjectId(object_id, Uuid::nil()),
                WalMetadataStreamOptionalInput::default(),
            )
            .await?,
        Default::default(),
    ))
    .await?;
    let old_entries_after = wal_entries_after
        .iter()
        .find(|(xact_id, _)| *xact_id == old_xact_id)
        .map(|(_, entries)| entries.len())
        .unwrap_or(0);
    let recent_entries_after = wal_entries_after
        .iter()
        .find(|(xact_id, _)| *xact_id == recent_xact_id)
        .map(|(_, entries)| entries.len())
        .unwrap_or(0);
    assert_eq!(
        old_entries_after, 0,
        "31-day-old transaction should have no entries after applying a 30-day retention policy",
    );
    assert_eq!(
        recent_entries_after,
        new_objects.len(),
        "29-day-old transaction should have all entries after applying a 30-day retention policy",
    );

    Ok(())
}

#[tokio::test]
async fn test_realtime_wal_retention_worker() -> Result<()> {
    let fixture = TestFixture::new(false).await; // Don't need postgres WAL for this test

    let global_store = fixture.config().global_store.clone();
    let realtime_wal = fixture.config().realtime_wal.clone().unwrap();

    let project_id = Uuid::new_v4().to_string();
    let object_id = FullObjectId {
        object_type: ObjectType::ProjectLogs,
        object_id: ObjectId::new(&project_id).unwrap(),
    };

    // Start with some 92-day-old data in the realtime WAL.
    let now = Utc::now();
    let very_old_timestamp = (now - Duration::days(92)).timestamp() as u64;
    let very_old_xact_id = TransactionId::from_timestamp(very_old_timestamp, 0);

    let initial_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: false,
            sequence_id: 100,
            xact_id: very_old_xact_id,
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: project_id.clone(),
            object_id: object_id.to_owned(),
            is_logs2: true,
            sequence_id: 101,
            xact_id: very_old_xact_id,
        },
    ];
    insert_object_atoms_into_wal(
        initial_objects,
        vec![],
        realtime_wal.clone(),
        global_store.clone(),
        false,
    )
    .await?;

    let segment_id = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;

    let process_result = process_object_wal(
        ProcessObjectWalInput {
            object_id: object_id,
            config: &fixture.config(),
        },
        Default::default(),
        Default::default(),
    )
    .await?;
    assert!(
        !process_result.modified_segment_ids.is_empty(),
        "process_object_wal should have modified some segments"
    );
    let compact_result = compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;
    assert!(
        compact_result.num_wal_entries_compacted > 0,
        "compact_segment_wal should have compacted some WAL entries"
    );
    // Verify we can read the data so far.
    let entries_after_compaction = fixture
        .read_index_wal_entries(&[object_id.to_owned()])
        .await;
    assert_eq!(
        entries_after_compaction.len(),
        2,
        "IndexWalReader should show 2 compacted entries after processing realtime WAL data"
    );

    // Add more data to the realtime WAL.
    let old_realtime_timestamp = (now - Duration::days(91)).timestamp() as u64; // 91 days ago - should be deleted
    let recent_realtime_timestamp = (now - Duration::days(89)).timestamp() as u64; // 89 days ago - should be preserved
    let old_realtime_xact_id = TransactionId::from_timestamp(old_realtime_timestamp, 0);
    let recent_realtime_xact_id = TransactionId::from_timestamp(recent_realtime_timestamp, 0);

    let old_realtime_objects = vec![TestingOnlyBackfillBrainstoreObjectAtom {
        project_id: project_id.clone(),
        object_id: object_id.to_owned(),
        is_logs2: false,
        sequence_id: 200,
        xact_id: old_realtime_xact_id,
    }];
    let recent_realtime_objects = vec![TestingOnlyBackfillBrainstoreObjectAtom {
        project_id: project_id.clone(),
        object_id: object_id.to_owned(),
        is_logs2: false,
        sequence_id: 300,
        xact_id: recent_realtime_xact_id,
    }];
    let all_realtime_objects = [
        old_realtime_objects.clone(),
        recent_realtime_objects.clone(),
    ]
    .concat();

    insert_object_atoms_into_wal(
        all_realtime_objects,
        vec![],
        realtime_wal.clone(),
        global_store.clone(),
        false,
    )
    .await?;

    // The WAL should show 4 entries: the 2 from earlier and the 2 from realtime.
    let entries_before_retention = fixture
        .read_index_wal_entries(&[object_id.to_owned()])
        .await;
    assert_eq!(
        entries_before_retention.len(),
        4,
        "IndexWalReader should return all entries so far",
    );

    // Run stateless time-based-retention with a 90-day retention policy.
    let retention_result = time_based_retention_stateless(
        TimeBasedRetentionInput {
            object_ids: Some(&[object_id]),
            config: &fixture.config(),
            config_file_schema: Some(&fixture.make_full_schema()),
            control_plane_ctx: None,
            dry_run: false,
        },
        TimeBasedRetentionOptionalInput {
            testing_object_id_to_retention_days: Some([(object_id, 90)].into_iter().collect()),
            ..Default::default()
        },
        &TimeBasedRetentionOptions::default(),
    )
    .await?;
    assert_eq!(
        retention_result.num_processed_objects, 1,
        "Should have processed 1 object"
    );
    assert!(
        retention_result
            .segment_stats
            .wal_stats
            .num_deleted_wal_entries
            > 0,
        "Should have deleted at least one realtime WAL entry",
    );

    // Verify that the deletes are reflected in the data.
    let entries_after_retention = fixture
        .read_index_wal_entries(&[object_id.to_owned()])
        .await;
    assert_eq!(
        entries_after_retention.len(), 1,
        "IndexWalReader should show 1 entry after retention (only the recent 89-day realtime entry)",
    );
    let remaining_xact_ids: Vec<_> = entries_after_retention
        .values()
        .map(|doc| doc.wal_entry._xact_id)
        .collect();
    assert!(
        remaining_xact_ids.contains(&recent_realtime_xact_id),
        "89-day realtime data should be preserved after 90-day retention"
    );
    assert!(
        !remaining_xact_ids.contains(&very_old_xact_id),
        "92-day compacted data should be deleted by 90-day retention"
    );
    assert!(
        !remaining_xact_ids.contains(&old_realtime_xact_id),
        "91-day realtime data should be deleted by 90-day retention"
    );

    Ok(())
}
