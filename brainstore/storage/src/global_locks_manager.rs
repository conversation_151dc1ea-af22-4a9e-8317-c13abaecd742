use async_trait::async_trait;
use async_util::{await_with_async_timeout, AsyncTimeout};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, RwLockReadGuard, RwLockWriteGuard};

use util::anyhow::Result;

/// A GlobalLocksManager provides a locks directory for use across the entire system, keyed by
/// string. The returned lock guards can outlive the locks manager itself.
#[async_trait]
pub trait GlobalLocksManager: std::fmt::Debug + Send + Sync {
    async fn read_impl(&self, name: &str) -> Result<Box<dyn GlobalLockReadGuard>>;
    async fn try_read(&self, name: &str) -> Result<Option<Box<dyn GlobalLockReadGuard>>>;
    async fn write_impl(&self, name: &str) -> Result<Box<dyn GlobalLockWriteGuard>>;
    async fn try_write(&self, name: &str) -> Result<Option<Box<dyn GlobalLockWriteGuard>>>;

    async fn snapshot_lock_state(&self) -> Result<HashMap<String, LockState>>;

    fn supports_read_locks(&self) -> bool {
        true
    }

    async fn read(&self, name: &str) -> Result<Box<dyn GlobalLockReadGuard>> {
        Ok(await_with_async_timeout(
            || format!("read lock for {}", name).into(),
            self.read_impl(name),
            AsyncTimeout::default(),
            None,
        )
        .await??)
    }

    async fn write(&self, name: &str) -> Result<Box<dyn GlobalLockWriteGuard>> {
        Ok(await_with_async_timeout(
            || format!("write lock for {}", name).into(),
            self.write_impl(name),
            AsyncTimeout::default(),
            None,
        )
        .await??)
    }

    async fn status(&self) -> Result<String>;
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LockState {
    Read,
    Write,
}

/// Generic trait for read and write lock guards. When the object is dropped, the lock will be
/// released.
pub trait GlobalLockReadGuard: Send + Sync {}
pub trait GlobalLockWriteGuard: Send + Sync {}

#[derive(Debug, Default)]
pub struct MemoryGlobalLocksManager {
    locks: RwLock<HashMap<String, Arc<RwLock<()>>>>,
    last_lock_states: RwLock<HashMap<String, LockState>>,
}

#[derive(Debug)]
struct MemoryGlobalReadLockGuard {
    _lock: Arc<RwLock<()>>,
    _guard: RwLockReadGuard<'static, ()>,
}

#[derive(Debug)]
struct MemoryGlobalWriteLockGuard {
    _lock: Arc<RwLock<()>>,
    _guard: RwLockWriteGuard<'static, ()>,
}

impl MemoryGlobalLocksManager {
    async fn get_lock(&self, name: &str) -> Result<Arc<RwLock<()>>> {
        let mut guard = self.locks.write().await;
        let map = &mut *guard;
        if !map.contains_key(name) {
            map.insert(name.to_string(), Arc::new(RwLock::new(())));
        }
        Ok(map.get(name).unwrap().clone())
    }

    fn make_read_lock_guard(
        lock: Arc<RwLock<()>>,
        guard: RwLockReadGuard<'_, ()>,
    ) -> Box<MemoryGlobalReadLockGuard> {
        // Safe because the guard points into the lock, and the returned structure cannot be
        // decomposed to drop individual fields.
        let guard = unsafe {
            std::mem::transmute::<RwLockReadGuard<'_, ()>, RwLockReadGuard<'static, ()>>(guard)
        };
        Box::new(MemoryGlobalReadLockGuard {
            _lock: lock,
            _guard: guard,
        })
    }

    fn make_write_lock_guard(
        lock: Arc<RwLock<()>>,
        guard: RwLockWriteGuard<'_, ()>,
    ) -> Box<MemoryGlobalWriteLockGuard> {
        // Safe because the guard points into the lock, and the returned structure cannot be
        // decomposed to drop individual fields.
        let guard = unsafe {
            std::mem::transmute::<RwLockWriteGuard<'_, ()>, RwLockWriteGuard<'static, ()>>(guard)
        };
        Box::new(MemoryGlobalWriteLockGuard {
            _lock: lock,
            _guard: guard,
        })
    }

    async fn set_last_lock_state(&self, name: &str, state: LockState) {
        let mut guard = self.last_lock_states.write().await;
        guard.insert(name.to_string(), state);
    }
}

#[async_trait]
impl GlobalLocksManager for MemoryGlobalLocksManager {
    async fn read_impl(&self, name: &str) -> Result<Box<dyn GlobalLockReadGuard>> {
        let lock = self.get_lock(name).await?;
        let guard = lock.read().await;
        self.set_last_lock_state(name, LockState::Read).await;
        Ok(MemoryGlobalLocksManager::make_read_lock_guard(
            lock.clone(),
            guard,
        ))
    }

    async fn try_read(&self, name: &str) -> Result<Option<Box<dyn GlobalLockReadGuard>>> {
        let lock = self.get_lock(name).await?;
        let guard = lock.try_read();
        match guard {
            Ok(guard) => {
                self.set_last_lock_state(name, LockState::Read).await;
                Ok(Some(MemoryGlobalLocksManager::make_read_lock_guard(
                    lock.clone(),
                    guard,
                )))
            }
            Err(_) => Ok(None),
        }
    }

    async fn write_impl(&self, name: &str) -> Result<Box<dyn GlobalLockWriteGuard>> {
        let lock = self.get_lock(name).await?;
        let guard = lock.write().await;
        self.set_last_lock_state(name, LockState::Write).await;
        Ok(MemoryGlobalLocksManager::make_write_lock_guard(
            lock.clone(),
            guard,
        ))
    }

    async fn try_write(&self, name: &str) -> Result<Option<Box<dyn GlobalLockWriteGuard>>> {
        let lock = self.get_lock(name).await?;
        let guard = lock.try_write();
        match guard {
            Ok(guard) => {
                self.set_last_lock_state(name, LockState::Write).await;
                Ok(Some(MemoryGlobalLocksManager::make_write_lock_guard(
                    lock.clone(),
                    guard,
                )))
            }
            Err(_) => Ok(None),
        }
    }

    async fn snapshot_lock_state(&self) -> Result<HashMap<String, LockState>> {
        let locks_snapshot: Vec<(String, Arc<RwLock<()>>)> = {
            let locks_guard = self.locks.read().await;
            locks_guard
                .iter()
                .map(|(k, v)| (k.clone(), v.clone()))
                .collect()
        };
        let mut out: HashMap<String, LockState> = HashMap::new();
        for (name, lock) in locks_snapshot {
            let state: Option<LockState> = match lock.try_write() {
                Ok(_) => {
                    // We successfully took the write-lock, meaning the lock was not taken.
                    None
                }
                Err(_) => {
                    // The lock is currently taken, so estimate its state from the last locked map.
                    // If not set, just skip it.
                    let _guard = self.last_lock_states.read().await;
                    _guard.get(&name).cloned()
                }
            };
            if let Some(state) = state {
                out.insert(name, state);
            }
        }
        Ok(out)
    }

    async fn status(&self) -> Result<String> {
        Ok("MemoryGlobalLocksManager is ok".into())
    }
}

impl GlobalLockReadGuard for MemoryGlobalReadLockGuard {}
impl GlobalLockWriteGuard for MemoryGlobalWriteLockGuard {}
