use async_stream::try_stream;
use futures::{stream::BoxStream, Stream, StreamExt, TryStreamExt};
use object_store::ObjectStore;
use std::{marker::Unpin, path::PathBuf, sync::Arc};
use util::{
    anyhow::{anyhow, Result},
    uuid::Uuid,
};

pub(crate) const DELETION_LOG_DIR: &str = "delete_ops";
pub(crate) const DRY_RUN_DELETION_LOG_DIR: &str = "dry_run_delete_ops";

/// Generates a unique filename for a deletion log file. These filenames are time-ordered lexicographically.
pub(crate) fn generate_deletion_log_fname(time: std::time::SystemTime) -> String {
    let timestamp = time
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let uuid = Uuid::new_v4();
    format!("{:012}.{}.log", timestamp, uuid)
}

#[derive(<PERSON><PERSON>, Debug)]
pub(crate) struct DeletionLogArgs<'a> {
    pub store: Arc<dyn ObjectStore>,
    /// The prefix path for log writes.
    pub deletion_log_prefix: &'a PathBuf,
    /// Whether this is a dry run (determines which directory to write deletion logs to).
    pub dry_run: bool,
    /// Max number of paths that can be written to each log file.
    pub batch_size: usize,
}

/// Extension trait to handle logging of all file paths that pass through the stream.  This is
/// intended to be used by callers of `delete_stream` so that the deleted object store files get
/// logged and, in the event of an unintended delete operation, can later be restored.
pub(crate) trait DeletionLogStreamExt:
    Stream<Item = Result<PathBuf, object_store::Error>> + Send + Unpin + Sized
{
    /// This method takes a stream of PathBuf items and:
    /// 1. Batches them into chunks.
    /// 2. For each chunk, writes a deletion log file containing the paths.
    /// 3. Passes all the original paths through unchanged.
    ///
    /// The deletion log files are written to:
    /// - `{deletion_log_prefix}/DELETION_LOG_DIR/` for actual deletes
    /// - `{deletion_log_prefix}/DRY_RUN_DELETION_LOG_DIR/` for dry runs
    fn with_deletion_log<'a>(
        self,
        args: DeletionLogArgs<'_>,
    ) -> BoxStream<'a, Result<PathBuf, object_store::Error>>
    where
        Self: 'a,
    {
        let store = args.store.clone();

        // Choose the log directory based on whether we're performing a dry run.
        let deletion_log_dir_name = if args.dry_run {
            DRY_RUN_DELETION_LOG_DIR
        } else {
            DELETION_LOG_DIR
        };
        let deletion_log_dir = args.deletion_log_prefix.join(deletion_log_dir_name);

        try_stream! {
            let mut chunked_stream = self.try_chunks(args.batch_size);
            while let Some(chunk_result) = chunked_stream.next().await {
                let paths = chunk_result.map_err(|e| object_store::Error::Generic {
                    store: "deletion_log",
                    source: anyhow!("Failed to batch paths for deletion logging: {}", e).into(),
                })?;

                if paths.is_empty() {
                    continue;
                }

                let fname = generate_deletion_log_fname(std::time::SystemTime::now());
                let file_content = paths
                    .iter()
                    .map(|p| match p.to_str() {
                        Some(s) => Ok(s.to_string()),
                        None => Err(object_store::Error::Generic {
                            store: "deletion_log",
                            source: anyhow!("Failed to convert path to string").into(),
                        }),
                    })
                    .collect::<Result<Vec<_>, _>>()?
                    .join("\n");

                let deletion_log_path = deletion_log_dir.join(&fname);
                let deletion_log_path_str = deletion_log_path
                    .to_str()
                    .ok_or(object_store::Error::Generic {
                        store: "deletion_log",
                        source: anyhow!("Failed to convert deletion log path to string").into(),
                    })?;
                let object_path = object_store::path::Path::from(deletion_log_path_str);

                store
                    .put(&object_path, file_content.into())
                    .await
                    .map_err(|e| object_store::Error::Generic {
                        store: "deletion_log",
                        source: anyhow!("Failed to write deletion log file: {}", e).into(),
                    })?;

                for path in paths {
                    yield path;
                }
            }
        }
        .boxed()
    }
}

impl<S> DeletionLogStreamExt for S where
    S: Stream<Item = Result<PathBuf, object_store::Error>> + Send + Unpin
{
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use crate::config_with_store::StoreInfo;
    use futures::TryStreamExt;
    use object_store::{path::Path, ObjectMeta};
    use std::time::{SystemTime, UNIX_EPOCH};
    use util::anyhow::{anyhow, Result};

    /// Extract the timestamp from a deletion log filename.
    pub fn extract_deletion_log_timestamp(fname: &str) -> Result<u64> {
        let parts: Vec<&str> = fname.split(".").collect();
        let timestamp = parts[0].parse::<u64>();
        if timestamp.is_err() {
            return Err(anyhow!(
                "Failed to parse timestamp from filename: {}",
                fname
            ));
        }
        Ok(timestamp.unwrap())
    }

    pub async fn list_deletion_log_files(
        index_store: &StoreInfo,
        deletion_log_dir: &str,
    ) -> Result<Vec<ObjectMeta>> {
        let deletion_log_path = index_store.prefix.join(deletion_log_dir);
        let deletion_log_path_str = deletion_log_path
            .to_str()
            .ok_or_else(|| anyhow!("Failed to convert prefix to string"))?;
        let deletion_log_prefix = Path::from(deletion_log_path_str);
        let files = index_store
            .store
            .list(Some(&deletion_log_prefix))
            .try_collect::<Vec<_>>()
            .await
            .map_err(|e| anyhow!("Failed to list deletion log files: {}", e))?;
        Ok(files)
    }

    /// Read all file paths from deletion log files in the specified directory.
    pub async fn read_deletion_log_paths(
        index_store: &StoreInfo,
        deletion_log_dir: &str,
    ) -> Result<Vec<String>> {
        let files = list_deletion_log_files(index_store, deletion_log_dir).await?;

        let mut paths = Vec::new();
        for file_meta in files {
            let content = index_store
                .store
                .get(&file_meta.location)
                .await?
                .bytes()
                .await?;
            let content_str = String::from_utf8(content.to_vec())?;
            for line in content_str.lines() {
                if !line.trim().is_empty() {
                    paths.push(line.to_string());
                }
            }
        }
        Ok(paths)
    }

    #[test]
    fn test_deletion_log_fname_lexicographic_ordering() -> Result<()> {
        let times = vec![
            // Timestamp = 0
            UNIX_EPOCH,
            // Timestamp = 100
            UNIX_EPOCH + std::time::Duration::from_secs(100),
            // Timestamp = 1000
            UNIX_EPOCH + std::time::Duration::from_secs(1000),
            SystemTime::now() - std::time::Duration::from_secs(1),
            SystemTime::now(),
            // Rolls over into 11 digits
            UNIX_EPOCH + std::time::Duration::from_secs(99_999_999_999),
        ];

        let mut fnames = Vec::new();
        for time in &times {
            fnames.push(generate_deletion_log_fname(*time));
        }

        // Verify that the filenames are in ascending lexicographic order.
        for i in 0..fnames.len() - 1 {
            assert!(
                fnames[i] < fnames[i + 1],
                "Filenames should be in ascending order"
            );
        }

        // Verify that the timestamp can be reconstructed.
        for (fname, time) in fnames.iter().zip(times.iter()) {
            let timestamp = extract_deletion_log_timestamp(fname)?;
            assert_eq!(
                timestamp,
                time.duration_since(UNIX_EPOCH).unwrap().as_secs()
            );
        }

        Ok(())
    }
}
