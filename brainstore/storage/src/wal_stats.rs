use std::sync::atomic::AtomicU64;

use once_cell::sync::Lazy;
use util::{
    anyhow::{anyhow, Result},
    max_counter::<PERSON><PERSON>ounter,
};

use crate::{limits::global_limits, wal::WalBytesGuard};

pub static MAX_WAL_IN_FLIGHT_BYTES: Lazy<MaxCounter> = Lazy::new(Default::default);
pub static MAX_WAL_BATCH_SIZE: Lazy<AtomicU64> = Lazy::new(Default::default);
pub static TOTAL_WAL_READ_BYTES: Lazy<AtomicU64> = Lazy::new(Default::default);

// A guard to ensure that the total number of in-flight bytes read from the WAL does not exceed the
// global limit capacity.
pub struct InFlightBytesGuard {
    num_bytes: usize,
    _permit: Option<tokio::sync::SemaphorePermit<'static>>,
}

impl InFlightBytesGuard {
    pub async fn new(num_bytes: usize) -> Result<Self> {
        if num_bytes == 0 {
            return Ok(Self {
                num_bytes,
                _permit: None,
            });
        }
        let limit = &global_limits().wal_max_inflight_bytes;
        let permit_bytes = if limit.capacity() < num_bytes {
            tracing::warn!(
                "Requested bytes {} exceeds the WAL max capacity of {} bytes. Will upper-bound permit to the capacity",
                num_bytes,
                limit.capacity()
            );
            u32::try_from(limit.capacity()).unwrap()
        } else {
            u32::try_from(num_bytes).map_err(|_| {
                anyhow!(
                    "WAL entry size {} bytes is too large to fit in u32 (max: {} bytes)",
                    num_bytes,
                    u32::MAX
                )
            })?
        };
        let permit = limit.start_many(permit_bytes).await?;
        MAX_WAL_IN_FLIGHT_BYTES.incr_n(num_bytes as u64);
        MAX_WAL_BATCH_SIZE.fetch_max(num_bytes as u64, std::sync::atomic::Ordering::Relaxed);
        TOTAL_WAL_READ_BYTES.fetch_add(num_bytes as u64, std::sync::atomic::Ordering::Relaxed);
        Ok(Self {
            num_bytes,
            _permit: Some(permit),
        })
    }

    pub fn num_bytes(&self) -> usize {
        self.num_bytes
    }
}

impl Drop for InFlightBytesGuard {
    fn drop(&mut self) {
        if self.num_bytes > 0 {
            MAX_WAL_IN_FLIGHT_BYTES.decr_n(self.num_bytes as u64);
        }
    }
}

impl WalBytesGuard for InFlightBytesGuard {
    fn num_bytes(&self) -> usize {
        self.num_bytes
    }
}
