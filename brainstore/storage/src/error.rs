use thiserror::Error;

#[derive(Error, Debug)]
pub enum StorageError {
    #[error("Invalid value '{value}' at path: {path:?}. Expected type: {expected:?}")]
    InvalidTypeAtPath {
        path: Vec<String>,
        value: String,
        expected: util::schema::TantivyType,
    },

    #[error("Invalid value '{value}' for repeated field at path: {path:?}. Expected array of {base_type:?}")]
    ExpectedRepeatedValueAtPath {
        path: Vec<String>,
        value: String,
        base_type: util::schema::TantivyType,
    },
}
