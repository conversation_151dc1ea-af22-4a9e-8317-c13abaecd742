use serde::{Deserialize, Serialize};
use util::{anyhow::Result, system_types::ObjectType};

#[derive(Debug, <PERSON><PERSON>, Hash, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum RetentionObjectType {
    ProjectLogs,
    Experiment,
    Dataset,
}

impl From<&RetentionObjectType> for ObjectType {
    fn from(retention_type: &RetentionObjectType) -> Self {
        match retention_type {
            RetentionObjectType::ProjectLogs => ObjectType::ProjectLogs,
            RetentionObjectType::Experiment => ObjectType::Experiment,
            RetentionObjectType::Dataset => ObjectType::Dataset,
        }
    }
}

impl TryFrom<ObjectType> for RetentionObjectType {
    type Error = util::anyhow::Error;

    fn try_from(object_type: ObjectType) -> Result<Self, Self::Error> {
        match object_type {
            ObjectType::ProjectLogs => Ok(RetentionObjectType::ProjectLogs),
            ObjectType::Experiment => Ok(RetentionObjectType::Experiment),
            ObjectType::Dataset => Ok(RetentionObjectType::Dataset),
            _ => Err(util::anyhow::anyhow!(
                "Object type {:?} is not supported for retention policies",
                object_type
            )),
        }
    }
}

impl std::fmt::Display for RetentionObjectType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            RetentionObjectType::ProjectLogs => "project_logs",
            RetentionObjectType::Experiment => "experiment",
            RetentionObjectType::Dataset => "dataset",
        };
        write!(f, "{}", s)
    }
}

impl RetentionObjectType {
    pub fn acl_object_type(&self) -> &'static str {
        match self {
            RetentionObjectType::ProjectLogs => "project_log",
            RetentionObjectType::Experiment => "experiment",
            RetentionObjectType::Dataset => "dataset",
        }
    }

    /// Returns true if retention should only run on objects that are already deleted in the control
    /// plane.
    pub fn retention_requires_prior_control_plane_deletion(&self) -> bool {
        match self {
            RetentionObjectType::Experiment => true,
            RetentionObjectType::ProjectLogs | RetentionObjectType::Dataset => false,
        }
    }
}
