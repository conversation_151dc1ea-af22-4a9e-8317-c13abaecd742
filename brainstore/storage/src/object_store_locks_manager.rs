use async_trait::async_trait;
use backoff::future::retry;
use base64::{prelude::BASE64_STANDARD, Engine};
use bytes::Bytes;
use futures::future::join_all;
use futures::StreamExt;
use lazy_static::lazy_static;
use object_store::{PutResult, UpdateVersion};
use otel_common::opentelemetry::metrics::Counter;
use serde::{Deserialize, Serialize};
use tracing::instrument;
use util::anyhow::anyhow;
use util::chrono::Utc;
use util::uuid;
use util::uuid::Uuid;
use util::Result;

use object_store::{
    self, path::Path as ObjectStorePath, ObjectStore, PutMode, PutOptions, PutPayload,
};
use std::sync::Mutex;
use std::{collections::HashMap, sync::Arc, time::Duration};

use crate::global_locks_manager::{
    GlobalLockReadGuard, GlobalLockWriteGuard, GlobalLocksManager, LockState,
};
use crate::redis_locks_manager::locks_backoff_config;

pub const DEFAULT_LOCK_PREFIX: &str = "lock";

// These are the same as RedisLocksManager, but copy/pasted so we can tune them independently.
pub const DEFAULT_LOCK_TTL_SECONDS: u64 = 60;
const LOCK_MAX_ACQUIRE_WINDOW_SECONDS: u64 = 3600;

#[derive(Debug, Clone)]
pub struct ObjectStoreLocksManager {
    pub object_store: Arc<dyn ObjectStore>,
    pub prefix: String,
    lock_ttl: Duration,
}

impl ObjectStoreLocksManager {
    pub fn new(store: Arc<dyn ObjectStore>) -> Self {
        Self::new_with_opts(
            store,
            DEFAULT_LOCK_PREFIX.to_string(),
            Duration::from_secs(DEFAULT_LOCK_TTL_SECONDS),
        )
    }

    pub fn new_with_opts(
        object_store: Arc<dyn ObjectStore>,
        prefix: String,
        lock_ttl: Duration,
    ) -> Self {
        Self {
            object_store,
            prefix,
            lock_ttl,
        }
    }

    fn get_lock_name(&self, name: &str) -> ObjectStorePath {
        let name_base64 = BASE64_STANDARD.encode(name);
        ObjectStorePath::from_iter(
            self.prefix
                .split('/')
                .chain(std::iter::once(name_base64.as_str())),
        )
    }
}

#[async_trait]
impl GlobalLocksManager for ObjectStoreLocksManager {
    #[instrument(err, skip(name), name = "ObjectStoreLocksManager::write")]
    async fn write_impl(&self, name: &str) -> Result<Box<dyn GlobalLockWriteGuard>> {
        let backoff_config = locks_backoff_config(LOCK_MAX_ACQUIRE_WINDOW_SECONDS);

        let operation = || async {
            match self.try_write(name).await {
                Ok(Some(lock)) => Ok(lock),
                Ok(None) => Err(backoff::Error::transient(anyhow!(
                    "Lock held by another process"
                ))),
                Err(e) => Err(backoff::Error::permanent(e)),
            }
        };

        let lock = retry(backoff_config, operation).await?;
        Ok(lock)
    }

    #[instrument(err, skip(name), name = "ObjectStoreLocksManager::try_write")]
    async fn try_write(&self, name: &str) -> Result<Option<Box<dyn GlobalLockWriteGuard>>> {
        let path = self.get_lock_name(name);
        // First, try to create the lock file. If it already exists, then we need to check if it's
        // expired. If it is not expired, we need to compare-and-swap to acquire it. IF it's not expired, then
        // someone else has it.
        match put_opts_with_retry(
            &self.object_store,
            &path,
            PutPayload::from_bytes(LocksContent::new_bytes(self.lock_ttl)?),
            PutOptions::from(PutMode::Create),
        )
        .await
        {
            Ok(result) => {
                OBJECT_STORE_LOCKS_MANAGER_METERS.acquisitions.add(1, &[]);
                return Ok(Some(Box::new(ObjectStoreLockGuard::new(
                    self.object_store.clone(),
                    path.clone(),
                    result,
                    self.lock_ttl,
                ))));
            }
            Err(e) => match e {
                object_store::Error::AlreadyExists { .. }
                | object_store::Error::Precondition { .. } => {
                    // If it already exists, then we need to try and CAS it.
                }
                e => {
                    return Err(e.into());
                }
            },
        }

        // Now, let's read the lock file that's there and check if it's expired.
        // If the file doesn't exist, then the lock may have been released since we checked just
        // above, but that's fine -- it was held by someone else when we did try to acquire it, so
        // we just return None.
        let lock_content_result = match self.object_store.get(&path).await {
            Ok(lock_content) => lock_content,
            Err(object_store::Error::NotFound { .. }) => return Ok(None),
            Err(e) => return Err(e.into()),
        };

        let update_mode = UpdateVersion {
            version: lock_content_result.meta.version.clone(),
            e_tag: lock_content_result.meta.e_tag.clone(),
        };

        let lock_content = LocksContent::from_bytes(lock_content_result.bytes().await?)?;
        if !lock_content.is_expired() {
            OBJECT_STORE_LOCKS_MANAGER_METERS
                .acquisition_failures
                .add(1, &[]);
            return Ok(None);
        }

        // If it is expired, then try to CAS it.
        match put_opts_with_retry(
            &self.object_store,
            &path,
            PutPayload::from_bytes(LocksContent::new_bytes(self.lock_ttl)?),
            PutOptions::from(PutMode::Update(update_mode)),
        )
        .await
        {
            Ok(result) => {
                OBJECT_STORE_LOCKS_MANAGER_METERS.acquisitions.add(1, &[]);
                Ok(Some(Box::new(ObjectStoreLockGuard::new(
                    self.object_store.clone(),
                    path.clone(),
                    result,
                    self.lock_ttl,
                ))))
            }
            Err(e) => match e {
                object_store::Error::Precondition { .. } => {
                    OBJECT_STORE_LOCKS_MANAGER_METERS
                        .acquisition_failures
                        .add(1, &[]);
                    // If the CAS fails, then someone else must have acquired it.
                    Ok(None)
                }
                e => Err(e.into()),
            },
        }
    }

    async fn snapshot_lock_state(&self) -> Result<HashMap<String, LockState>> {
        let mut lock_states = HashMap::new();

        // List all files in the locks prefix
        let prefix_path = ObjectStorePath::from(self.prefix.as_str());
        let mut list_stream = self.object_store.list(Some(&prefix_path));

        let mut futures = Vec::new();
        while let Some(meta_result) = list_stream.next().await {
            let meta = meta_result?;

            // Extract the lock name from the path
            let lock_name = meta
                .location
                .as_ref()
                .strip_prefix(&format!("{}/", self.prefix))
                .unwrap_or(meta.location.as_ref())
                .to_string();

            // Skip if it's just the prefix itself
            if lock_name.is_empty() {
                continue;
            }

            // Read the lock file content to check if it's expired
            let object_store = self.object_store.clone();
            futures.push(async move {
                match object_store.get(&meta.location).await {
                    Ok(result) => {
                        match LocksContent::from_bytes(result.bytes().await?) {
                            Ok(lock_content) => {
                                if !lock_content.is_expired() {
                                    // Lock is still valid
                                    return Ok::<_, util::anyhow::Error>(Some((
                                        lock_name,
                                        LockState::Write,
                                    )));
                                }
                            }
                            Err(e) => {
                                log::warn!("Failed to parse lock content for {}: {}", lock_name, e);
                            }
                        }
                    }
                    Err(object_store::Error::NotFound { .. }) => {
                        // Lock was deleted between list and get, that's fine
                    }
                    Err(e) => {
                        log::warn!("Failed to read lock content for {}: {}", lock_name, e);
                    }
                }
                Ok::<_, util::anyhow::Error>(None)
            });
        }

        let results = join_all(futures).await;
        for result in results {
            if let Some((lock_name, lock_state)) = result? {
                let decoded_name = BASE64_STANDARD.decode(lock_name.as_bytes())?;
                lock_states.insert(
                    String::from_utf8_lossy(&decoded_name).to_string(),
                    lock_state,
                );
            }
        }

        Ok(lock_states)
    }

    async fn read_impl(&self, _name: &str) -> Result<Box<dyn GlobalLockReadGuard>> {
        todo!("Object store read locks are not supported")
    }

    async fn try_read(&self, _name: &str) -> Result<Option<Box<dyn GlobalLockReadGuard>>> {
        todo!("Object store read locks are not supported")
    }

    fn supports_read_locks(&self) -> bool {
        false
    }

    async fn status(&self) -> Result<String> {
        // Test lock functionality by acquiring and releasing a test lock
        let test_lock_name = format!("_status_check_{}", uuid::Uuid::new_v4());

        // Try to acquire a test lock
        match self.try_write(&test_lock_name).await {
            Ok(Some(lock)) => {
                // Successfully acquired, now release it
                drop(lock);
                Ok(format!(
                    "ObjectStoreLocksManager is ok (prefix: {})",
                    self.prefix
                ))
            }
            Ok(None) => {
                // This shouldn't happen for a random UUID
                Err(anyhow!("Failed to acquire test lock - unexpected conflict"))
            }
            Err(e) => Err(anyhow!("Failed to test lock acquisition: {}", e)),
        }
    }
}

struct ObjectStoreLockGuard {
    refresh_task: Option<tokio::task::JoinHandle<()>>,
    shutdown_signal: Arc<tokio::sync::Notify>,
}

impl GlobalLockWriteGuard for ObjectStoreLockGuard {}

impl ObjectStoreLockGuard {
    fn new(
        object_store: Arc<dyn ObjectStore>,
        path: ObjectStorePath,
        put_result: PutResult,
        lock_ttl: Duration,
    ) -> Self {
        let shutdown_signal = Arc::new(tokio::sync::Notify::new());
        let put_result = Arc::new(Mutex::new(put_result));
        Self {
            shutdown_signal: shutdown_signal.clone(),
            refresh_task: Some(tokio::spawn(async move {
                loop {
                    match Self::refresh(&object_store, &path, &put_result, lock_ttl).await {
                        Ok(_) => {}
                        Err(e) => {
                            log::error!(
                                "Error extending object store lock: {:?}. This could result in catastrophic failure. Aborting the process.",
                                e
                            );
                            std::process::abort();
                        }
                    }
                    tokio::select! {
                        _ = shutdown_signal.notified() => {
                            break;
                        }
                        _ = tokio::time::sleep(std::cmp::min(Duration::from_secs(5), lock_ttl / 4)) => {
                            continue;
                        }
                    }
                }

                // Ideally we should do a conditional delete here. But since we know we have the
                // lock, it's probably okay to just delete it.
                if let Err(e) = object_store.delete(&path).await {
                    log::warn!("Error deleting object store lock: {:?}. This means that it may be unavailable until the ttl expires.", e);
                }
            })),
        }
    }

    async fn refresh(
        object_store: &Arc<dyn ObjectStore>,
        path: &ObjectStorePath,
        put_result: &Arc<Mutex<PutResult>>,
        lock_ttl: Duration,
    ) -> Result<()> {
        let starting_put_result = put_result.lock().unwrap().clone();
        let result = put_opts_with_retry(
            object_store,
            path,
            PutPayload::from_bytes(LocksContent::new_bytes(lock_ttl)?),
            PutOptions::from(PutMode::Update(UpdateVersion::from(starting_put_result))),
        )
        .await
        .map_err(|e| anyhow!(e))?;

        {
            let mut put_result = put_result.lock().unwrap();
            *put_result = result;
        }

        Ok(())
    }
}

impl Drop for ObjectStoreLockGuard {
    fn drop(&mut self) {
        self.shutdown_signal.notify_one();
        let refresh_task = self.refresh_task.take();
        tokio::spawn(async move {
            if let Some(refresh_task) = refresh_task {
                // Do not abort the task (like we do in the Redis manager). We need to make sure
                // the delete file logic runs.
                if let Err(e) = refresh_task.await {
                    if !e.is_cancelled() {
                        log::warn!("Error aborting refresh task for object store lock: {:?}", e);
                    }
                }
            }
        });
    }
}

const VERSION: u32 = 1;

#[derive(Serialize, Deserialize)]
struct LocksContent {
    version: u32,
    expires_at: i64,
    // This adds some randomness to the content, so that the ETag is not
    // accidentally the same for two concurrent locks.
    nonce: Uuid,
}

impl LocksContent {
    fn new(ttl: Duration) -> Self {
        LocksContent {
            version: VERSION,
            expires_at: Utc::now().timestamp() + ttl.as_secs() as i64,
            nonce: uuid::Uuid::new_v4(),
        }
    }

    fn new_bytes(ttl: Duration) -> Result<Bytes> {
        Ok(Bytes::from(serde_json::to_vec(&LocksContent::new(ttl))?))
    }

    fn from_bytes(bytes: Bytes) -> Result<Self> {
        let result: Self = serde_json::from_slice(&bytes)?;
        if result.version != VERSION {
            return Err(anyhow!("Unsupported version: {}", result.version));
        }
        Ok(result)
    }

    fn is_expired(&self) -> bool {
        self.expires_at < Utc::now().timestamp()
    }
}

struct ObjectStoreLockMeters {
    acquisitions: Counter<u64>,
    acquisition_failures: Counter<u64>,
}

impl ObjectStoreLockMeters {
    pub fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            acquisitions: meter
                .u64_counter("brainstore.storage.object_store_locks_manager.acquisitions")
                .build(),
            acquisition_failures: meter
                .u64_counter("brainstore.storage.object_store_locks_manager.acquisition_failures")
                .build(),
        }
    }
}

/// Wrapper around put_opts that retries on generic S3 errors but not on compare-and-swap errors
async fn put_opts_with_retry(
    object_store: &Arc<dyn ObjectStore>,
    path: &ObjectStorePath,
    payload: PutPayload,
    options: PutOptions,
) -> std::result::Result<PutResult, object_store::Error> {
    // Retry for up to a minute
    let backoff_config = backoff::ExponentialBackoff {
        initial_interval: Duration::from_millis(100),
        randomization_factor: 0.5,
        multiplier: 2.0,
        max_interval: Duration::from_secs(10),
        max_elapsed_time: Some(Duration::from_secs(60)),
        ..Default::default()
    };

    let operation = || async {
        match object_store
            .put_opts(path, payload.clone(), options.clone())
            .await
        {
            Ok(result) => Ok(result),
            Err(e) => {
                match &e {
                    // Don't retry on compare-and-swap errors - these should propagate immediately
                    object_store::Error::AlreadyExists { .. }
                    | object_store::Error::Precondition { .. } => Err(backoff::Error::permanent(e)),
                    // Retry on generic errors (like HTTP errors, network issues, etc.)
                    object_store::Error::Generic { .. } => {
                        log::warn!("Retrying lock acquisition due to generic error: {}", e);
                        Err(backoff::Error::transient(e))
                    }
                    // For other errors, don't retry
                    _ => Err(backoff::Error::permanent(e)),
                }
            }
        }
    };

    retry(backoff_config, operation).await
}

lazy_static! {
    static ref OBJECT_STORE_LOCKS_MANAGER_METERS: ObjectStoreLockMeters =
        ObjectStoreLockMeters::new();
}
