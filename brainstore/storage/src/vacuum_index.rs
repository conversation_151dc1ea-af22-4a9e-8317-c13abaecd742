use async_stream::try_stream;
use clap::Parser;
use futures::stream::{BoxStream, StreamExt, TryStreamExt};
use lazy_static::lazy_static;
use object_store::ObjectMeta;
use otel_common::opentelemetry::metrics::Counter;
use serde::{Deserialize, Serialize};
use std::{path::PathBuf, sync::Arc};
use tantivy::SegmentId;
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Duration, Utc},
    schema::Schema,
    system_types::FullObjectId,
    uuid::Uuid,
};

use crate::{
    config_with_store::StoreInfo,
    deletion_log::{DeletionLogArgs, DeletionLogStreamExt},
    global_locks_manager::GlobalLocksManager,
    global_store::{
        GlobalStore, ListSegmentIdsGlobalOptionalInput, SegmentIdPaginationArgs, SegmentMetadata,
        TaskInfo, VacuumIndexInfo,
    },
    tantivy_footer::make_footer_fname,
    tantivy_index::{extract_segment_id, TantivyIndexScope},
    timer::{OtelCounterGuard, OtelTimer},
    vacuum::{CommonVacuumOptions, VacuumType},
};

type Error = util::anyhow::Error;

pub struct VacuumIndexMeters {
    pub run: OtelTimer,
    pub run_errors: Counter<u64>,
    pub iteration: OtelTimer,

    pub segments_considered: Counter<u64>,
    pub segments_processed: Counter<u64>,

    pub batch: OtelTimer,
    pub delete_stream_errors: Counter<u64>,

    pub files_deleted: Counter<u64>,
    pub bytes_deleted: Counter<u64>,
}

impl Default for VacuumIndexMeters {
    fn default() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            run: OtelTimer::new(&meter, "brainstore.storage.vacuum_index.run"),
            run_errors: meter
                .u64_counter("brainstore.storage.vacuum_index.run.errors")
                .build(),
            iteration: OtelTimer::new(&meter, "brainstore.storage.vacuum_index.iteration"),

            segments_considered: meter
                .u64_counter("brainstore.storage.vacuum_index.segments_considered")
                .build(),
            segments_processed: meter
                .u64_counter("brainstore.storage.vacuum_index.segments_processed")
                .build(),

            batch: OtelTimer::new(&meter, "brainstore.storage.vacuum_index.batch"),
            delete_stream_errors: meter
                .u64_counter("brainstore.storage.vacuum_index.delete_stream.errors")
                .build(),

            files_deleted: meter
                .u64_counter("brainstore.storage.vacuum_index.files_deleted")
                .build(),
            bytes_deleted: meter
                .u64_counter("brainstore.storage.vacuum_index.bytes_deleted")
                .build(),
        }
    }
}

lazy_static! {
    pub static ref VACUUM_INDEX_METERS: VacuumIndexMeters = VacuumIndexMeters::default();
}

#[derive(Clone, Debug)]
pub struct VacuumIndexInput<'a> {
    // If object_ids is None, all objects will be vacuumed.
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub global_store: Arc<dyn GlobalStore>,
    pub index_store: StoreInfo,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub config_file_schema: Option<Schema>,
    pub dry_run: bool,
    pub segment_ids: Option<Vec<Uuid>>,
}

#[derive(Clone, Debug, Default)]
pub struct VacuumIndexOptionalInput {
    pub segment_id_cursor: Option<Uuid>,
    pub testing_force_query_segment_metadatas_error_for_segment_id: Option<Uuid>,
    pub testing_force_list_error_for_segment_id: Option<Uuid>,
}

#[derive(Clone, Debug, Parser, Serialize, Deserialize)]
pub struct VacuumIndexOptions {
    #[arg(
        long,
        default_value_t = default_vacuum_index_segment_batch_size(),
        help = "Segment batch size for vacuuming index files.",
        env = "BRAINSTORE_VACUUM_INDEX_SEGMENT_BATCH_SIZE",
    )]
    #[serde(default = "default_vacuum_index_segment_batch_size")]
    pub vacuum_index_segment_batch_size: usize,
    // Minimum wait between vacuum operations on a segment.
    #[arg(
        long,
        default_value_t = default_vacuum_index_period_seconds(),
        help = "Minimum wait between vacuum operations on a segment.",
        env = "BRAINSTORE_VACUUM_INDEX_PERIOD_SECONDS",
    )]
    #[serde(default = "default_vacuum_index_period_seconds")]
    pub vacuum_index_period_seconds: i64,
    // If false, only vacuum files that match the tantivy index filename format. Otherwise,
    // delete unrecognized files in the index directory too.
    #[arg(
        long,
        default_value_t = false,
        help = "Delete unrecognized files.",
        env = "BRAINSTORE_VACUUM_INDEX_DELETE_UNRECOGNIZED_FILES"
    )]
    #[serde(default)]
    pub vacuum_index_delete_unrecognized_files: bool,
}

impl Default for VacuumIndexOptions {
    fn default() -> Self {
        Self {
            vacuum_index_segment_batch_size: default_vacuum_index_segment_batch_size(),
            vacuum_index_period_seconds: default_vacuum_index_period_seconds(),
            vacuum_index_delete_unrecognized_files: false,
        }
    }
}

fn default_vacuum_index_segment_batch_size() -> usize {
    10
}

fn default_vacuum_index_period_seconds() -> i64 {
    15 * 60 // 15 minutes
}

#[derive(Clone, Debug, Default, Parser, Serialize, Deserialize)]
pub struct VacuumIndexFullOptions {
    #[command(flatten)]
    #[serde(flatten)]
    pub common_vacuum_opts: CommonVacuumOptions,
    #[command(flatten)]
    #[serde(flatten)]
    pub vacuum_index_opts: VacuumIndexOptions,
}

#[derive(Serialize)]
pub struct VacuumIndexStatelessOutput {
    pub success: bool,
    pub segment_id_cursor: Option<Uuid>,
    pub num_processed_segments: usize,
    pub planned_num_deletes: usize,
    pub planned_total_bytes: u64,
    pub num_deleted_files: usize,
    pub error: Option<String>,
}

#[tracing::instrument(skip(input, optional_input))]
pub async fn vacuum_index_stateless<'a>(
    input: VacuumIndexInput<'a>,
    optional_input: VacuumIndexOptionalInput,
    options: VacuumIndexFullOptions,
) -> VacuumIndexStatelessOutput {
    let input = &input;
    let optional_input = &optional_input;

    let initial_cursor = optional_input.segment_id_cursor;

    let segment_ids_stream: BoxStream<'_, Result<Uuid, Error>> = try_stream! {
        let mut cursor = initial_cursor;

        loop {
            if let Some(segment_ids) = input.segment_ids.as_ref() {
                for segment_id in segment_ids {
                    yield *segment_id;
                }
                break;
            }
            match input.object_ids {
                Some(object_ids) => {
                    let mut segment_ids = input
                        .global_store
                        .list_segment_ids(object_ids, None)
                        .await?
                        .into_iter()
                        .flatten()
                        .collect::<Vec<Uuid>>();

                    // Apply a segment_id filter if we have an active cursor, then sort
                    // by ascending segment ID.
                    if let Some(cursor_id) = cursor {
                        segment_ids = segment_ids
                            .into_iter()
                            .filter(|id| id > &cursor_id)
                            .collect();
                    }
                    segment_ids.sort();

                    for segment_id in segment_ids {
                        yield segment_id;
                    }

                    // Currently `list_segment_ids` returns the entire result set,
                    // so we're done after one iteration. Once we implement pagination
                    // for this query, we'll be able to run this query in batches like
                    // we do for `list_segment_id_global`.
                    break;
                },
                None => {
                    let segment_ids = input
                        .global_store
                        .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                            pagination_args: Some(SegmentIdPaginationArgs {
                                segment_id_cursor: cursor,
                                limit: options.vacuum_index_opts.vacuum_index_segment_batch_size,
                            }),
                            ..Default::default()
                        }))
                        .await?;

                    if segment_ids.is_empty() {
                        break;
                    }

                    cursor = segment_ids.last().copied();

                    for segment_id in segment_ids {
                        yield segment_id;
                    }
                }
            };
        }
    }
    .boxed();

    let mut chunked_stream =
        segment_ids_stream.try_chunks(options.vacuum_index_opts.vacuum_index_segment_batch_size);

    let mut num_processed_segments = 0;
    let mut planned_num_deletes = 0;
    let mut planned_total_bytes = 0;
    let mut num_deleted_files = 0;
    let mut latest_cursor = initial_cursor;
    let mut iteration = 0;

    while let Some(chunk_result) = chunked_stream.next().await {
        match chunk_result {
            Ok(batch) => {
                if batch.is_empty() {
                    continue;
                }

                let batch_size = batch.len();
                let next_cursor = batch.last().copied();
                tracing::info!("[i={}] Vacuuming {} segments...", iteration, batch_size);

                let result = vacuum_index_batch(VacuumIndexBatchArgs {
                    global_store: input.global_store.clone(),
                    index_store: &input.index_store,
                    segment_ids: batch,
                    max_file_last_modified: Utc::now()
                        - Duration::seconds(
                            options
                                .common_vacuum_opts
                                .vacuum_deletion_grace_period_seconds
                                as i64,
                        ),
                    delete_unrecognized_files: options
                        .vacuum_index_opts
                        .vacuum_index_delete_unrecognized_files,
                    deletion_log_batch_size: options
                        .common_vacuum_opts
                        .vacuum_deletion_log_batch_size,
                    dry_run: input.dry_run,
                    testing_force_query_segment_metadatas_error_for_segment_id: optional_input
                        .testing_force_query_segment_metadatas_error_for_segment_id,
                    testing_force_list_error_for_segment_id: optional_input
                        .testing_force_list_error_for_segment_id,
                })
                .await;
                if let Err(e) = result {
                    tracing::error!("Error vacuuming index segments: {}", e);
                    return VacuumIndexStatelessOutput {
                        success: false,
                        segment_id_cursor: latest_cursor,
                        num_processed_segments,
                        planned_num_deletes,
                        planned_total_bytes,
                        num_deleted_files,
                        error: Some(e.to_string()),
                    };
                }

                let output = result.unwrap();
                num_processed_segments += batch_size;
                planned_num_deletes += output.planned_num_deletes;
                planned_total_bytes += output.planned_total_bytes;
                num_deleted_files += output.num_deleted_files;
                tracing::info!(
                    "[i={}] Processed {} segments and deleted {} files (total segments: {}, total deleted files: {})",
                    iteration,
                    batch_size,
                    output.num_deleted_files,
                    num_processed_segments,
                    num_deleted_files,
                );

                latest_cursor = next_cursor;
            }
            Err(e) => {
                tracing::error!("Error processing segment batch: {}", e);
                return VacuumIndexStatelessOutput {
                    success: false,
                    segment_id_cursor: latest_cursor,
                    num_processed_segments,
                    planned_num_deletes,
                    planned_total_bytes,
                    num_deleted_files,
                    error: Some(e.to_string()),
                };
            }
        }

        iteration += 1;
    }

    if input.dry_run {
        tracing::info!(
            "Dry run: found a total of {} files totaling {} bytes across {} segments that would be deleted.",
            planned_num_deletes,
            planned_total_bytes,
            num_processed_segments,
        );
    } else {
        tracing::info!(
            "Vacuum complete. In total, processed {} segments and deleted {} files.",
            num_processed_segments,
            num_deleted_files
        );
    }

    VacuumIndexStatelessOutput {
        success: true,
        segment_id_cursor: latest_cursor,
        num_processed_segments,
        planned_num_deletes,
        planned_total_bytes,
        num_deleted_files,
        error: None,
    }
}

#[derive(Default, Serialize)]
pub struct VacuumIndexOutput {
    pub num_processed_segments: usize,
    pub planned_num_deletes: usize,
    pub planned_total_bytes: u64,
    pub num_deleted_files: usize,
}

/// Vacuum stale index files, starting with the segments whose last successful vacuum is most behind
/// the last write to the segment. Segments that have never been vacuumed are prioritized first.
///
/// We skip a segment if either of the following is true:
///
///  1. We haven't waited long enough (vacuum_period_seconds) since this segment's last vacuum operation.
///  2. The last successful vacuum occurred enough time after the last write to the segment that
///     we can assume no additional index files were written to the segment. This is guaranteed if:
///
///     last_successful_start_ts - last_written_ts > deletion_grace_period_seconds + last_written_slop_seconds
///
/// Eligible segments are processed in batches. If a segment errors, we log the error to the global
/// store. Otherwise, we update `last_successful_start_ts` for every segment in the batch.
#[tracing::instrument(err, skip(input, optional_input), fields(object_ids = ?input.object_ids, dry_run = input.dry_run))]
pub async fn vacuum_index<'a>(
    input: VacuumIndexInput<'a>,
    optional_input: VacuumIndexOptionalInput,
    options: &VacuumIndexFullOptions,
) -> Result<VacuumIndexOutput> {
    if let Some(object_ids) = input.object_ids {
        if object_ids.is_empty() {
            tracing::debug!("No object IDs provided, skipping vacuum_index");
            return Ok(VacuumIndexOutput {
                num_processed_segments: 0,
                planned_num_deletes: 0,
                planned_total_bytes: 0,
                num_deleted_files: 0,
            });
        }
    }

    let vacuum_type = VacuumType::VacuumIndex;
    let _lock = match input
        .locks_manager
        .try_write(&vacuum_type.lock_name())
        .await?
    {
        Some(lock) => lock,
        None => {
            tracing::debug!("Skipping vacuum-index because another worker is already running it");
            return Ok(VacuumIndexOutput::default());
        }
    };

    let start_ts = Utc::now();
    let _run_timer = OtelCounterGuard::new(&VACUUM_INDEX_METERS.run);
    tracing::info!(
        start_ts = ?start_ts,
        "Starting vacuum-index loop"
    );

    let input = &input;
    let optional_input = &optional_input;
    let max_last_successful_start_minus_last_written_seconds = options
        .common_vacuum_opts
        .vacuum_deletion_grace_period_seconds
        + options.common_vacuum_opts.vacuum_last_written_slop_seconds;

    let mut i = 0;
    let mut num_processed_segments = 0;
    let mut planned_num_deletes = 0;
    let mut planned_total_bytes = 0;
    let mut num_deleted_files = 0;
    loop {
        let _iteration_timer = OtelCounterGuard::new(&VACUUM_INDEX_METERS.iteration);

        let batch_size = options.vacuum_index_opts.vacuum_index_segment_batch_size;
        let segment_ids = input
            .global_store
            .query_vacuum_segment_ids(
                vacuum_type,
                batch_size,
                max_last_successful_start_minus_last_written_seconds,
                options.vacuum_index_opts.vacuum_index_period_seconds,
                start_ts,
                input.object_ids,
            )
            .await?;
        if segment_ids.is_empty() {
            tracing::debug!("No remaining segments are eligible for vacuum-index, exiting loop",);
            break;
        }

        let num_segment_ids = segment_ids.len();
        let max_file_last_modified = start_ts
            - Duration::seconds(
                options
                    .common_vacuum_opts
                    .vacuum_deletion_grace_period_seconds,
            );

        let batch_output = match vacuum_index_batch(VacuumIndexBatchArgs {
            global_store: input.global_store.clone(),
            index_store: &input.index_store,
            segment_ids: segment_ids.clone(),
            max_file_last_modified,
            delete_unrecognized_files: options
                .vacuum_index_opts
                .vacuum_index_delete_unrecognized_files,
            deletion_log_batch_size: options.common_vacuum_opts.vacuum_deletion_log_batch_size,
            dry_run: input.dry_run,
            testing_force_query_segment_metadatas_error_for_segment_id: optional_input
                .testing_force_query_segment_metadatas_error_for_segment_id,
            testing_force_list_error_for_segment_id: optional_input
                .testing_force_list_error_for_segment_id,
        })
        .await
        {
            Ok(output) => output,
            Err(e) => {
                input
                    .global_store
                    .upsert_segment_task_info(
                        &segment_ids,
                        &TaskInfo::VacuumIndex(VacuumIndexInfo {
                            start_ts: Some(start_ts),
                            error: Some(serde_json::Value::String(e.to_string())),
                            ..Default::default()
                        }),
                    )
                    .await?;
                return Err(e);
            }
        };

        tracing::debug!(
            iteration = i,
            num_segment_ids = num_segment_ids,
            num_deleted_files = batch_output.num_deleted_files,
            "Completed vacuum-index batch",
        );

        num_processed_segments += num_segment_ids;
        planned_num_deletes += batch_output.planned_num_deletes;
        planned_total_bytes += batch_output.planned_total_bytes;
        num_deleted_files += batch_output.num_deleted_files;

        // The batch succeeded, so bump `last_successful_start_ts` for the included segments
        // and update the task info.
        input
            .global_store
            .upsert_segment_vacuum_last_successful_start_ts(
                &segment_ids,
                VacuumType::VacuumIndex,
                start_ts,
            )
            .await?;
        input
            .global_store
            .upsert_segment_task_info(
                &segment_ids,
                &TaskInfo::VacuumIndex(VacuumIndexInfo {
                    start_ts: Some(start_ts),
                    completed_ts: Some(Utc::now()),
                    num_deleted_files_batch: Some(batch_output.num_deleted_files),
                    ..Default::default()
                }),
            )
            .await?;

        i += 1;
    }

    tracing::info!(
        num_processed_segments = num_processed_segments,
        num_deleted_files = num_deleted_files,
        total_bytes = planned_total_bytes,
        "Completed vacuum-index loop",
    );

    Ok(VacuumIndexOutput {
        num_processed_segments,
        planned_num_deletes,
        planned_total_bytes,
        num_deleted_files,
    })
}

#[derive(Clone, Debug)]
struct VacuumIndexBatchArgs<'a> {
    global_store: Arc<dyn GlobalStore>,
    index_store: &'a StoreInfo,
    segment_ids: Vec<Uuid>,
    max_file_last_modified: DateTime<Utc>,
    delete_unrecognized_files: bool,
    deletion_log_batch_size: usize,
    dry_run: bool,
    testing_force_query_segment_metadatas_error_for_segment_id: Option<Uuid>,
    testing_force_list_error_for_segment_id: Option<Uuid>,
}

struct VacuumIndexBatchOutput {
    planned_num_deletes: usize,
    planned_total_bytes: u64,
    num_deleted_files: usize,
}

#[tracing::instrument(
    err,
    skip(args),
    fields(
        dry_run = args.dry_run,
        num_segment_ids = args.segment_ids.len(),
        max_file_last_modified = ?args.max_file_last_modified,
        delete_unrecognized_files = args.delete_unrecognized_files,
    ),
)]
async fn vacuum_index_batch(
    args: VacuumIndexBatchArgs<'_>,
) -> Result<VacuumIndexBatchOutput, Error> {
    let res = vacuum_index_batch_inner(args).await;
    if let Err(ref e) = res {
        tracing::error!(
            error = ?e,
            "Error vacuuming segments",
        );
        VACUUM_INDEX_METERS.run_errors.add(1, &[]);
    }
    res
}

async fn vacuum_index_batch_inner(
    args: VacuumIndexBatchArgs<'_>,
) -> Result<VacuumIndexBatchOutput, Error> {
    let _batch_timer = OtelCounterGuard::new(&VACUUM_INDEX_METERS.batch);
    let num_segments = args.segment_ids.len();
    VACUUM_INDEX_METERS
        .segments_considered
        .add(num_segments as u64, &[]);

    let active_index_chunk_uuids = args
        .global_store
        .query_segment_metadatas(&args.segment_ids)
        .await?
        .into_iter()
        .map(|segment_metadata| get_active_index_info(segment_metadata))
        .collect::<Result<Vec<Option<ActiveIndexInfo>>>>()?;

    if let Some(segment_id) = args.testing_force_query_segment_metadatas_error_for_segment_id {
        if args.segment_ids.contains(&segment_id) {
            return Err(object_store::Error::Generic {
                store: "testing_force_query_segment_metadatas_error",
                source: anyhow!("Forced error for testing purposes").into(),
            })?;
        }
    }

    let segment_ids_and_active_index_chunk_uuids = args
        .segment_ids
        .into_iter()
        .zip(active_index_chunk_uuids)
        .filter_map(|(segment_id, active_uuids)| active_uuids.map(|uuids| (segment_id, uuids)));

    let index_store = &args.index_store;

    let mut planned_num_deletes = 0;
    let mut planned_total_bytes = 0;

    let vacuumable_index_files_stream: BoxStream<'_, Result<PathBuf, object_store::Error>> = {
        let planned_num_deletes = &mut planned_num_deletes;
        let planned_total_bytes = &mut planned_total_bytes;

        try_stream! {
            for (segment_id, active_index_chunk_uuids) in segment_ids_and_active_index_chunk_uuids {
                tracing::debug!("Starting vacuum-index on segment: {}", segment_id);

                let index_scope = TantivyIndexScope::Segment(segment_id);
                let index_path = index_scope.path(&index_store.prefix);

                let testing_force_list_error = args.testing_force_list_error_for_segment_id == Some(segment_id);

                let index_path_str = index_path
                    .to_str()
                    .ok_or(object_store::Error::Generic {
                        store: "index_path",
                        source: anyhow!("Invalid index path").into(),
                    })?
                    .to_string();
                let index_prefix = object_store::path::Path::from(index_path_str);

                let mut listing = index_store.store.list(Some(&index_prefix));
                while let Some(object_meta) = listing.try_next().await? {
                    if should_vacuum_index_object(args.max_file_last_modified, &object_meta, &active_index_chunk_uuids, args.delete_unrecognized_files) {
                        *planned_num_deletes += 1;
                        *planned_total_bytes += object_meta.size as u64;
                        yield PathBuf::from(object_meta.location.to_string());
                    }
                }

                if testing_force_list_error {
                    Err(object_store::Error::Generic {
                        store: "testing_force_list_error",
                        source: anyhow!("Forced listing error for testing purposes").into(),
                    })?;
                }
            }
        }
        .boxed()
    };

    let paths_to_delete_stream = vacuumable_index_files_stream.with_deletion_log(DeletionLogArgs {
        store: args.index_store.store.clone(),
        deletion_log_prefix: &args.index_store.prefix,
        dry_run: args.dry_run,
        batch_size: args.deletion_log_batch_size,
    });

    if args.dry_run {
        let _ = paths_to_delete_stream.try_collect::<Vec<_>>().await?;
        tracing::info!(
            num_segments,
            planned_num_deletes,
            planned_total_bytes,
            "Dry run for vacuum-index batch: would delete unused index files",
        );
        return Ok(VacuumIndexBatchOutput {
            planned_num_deletes,
            planned_total_bytes,
            num_deleted_files: 0,
        });
    }

    let delete_stream = args
        .index_store
        .directory
        .delete_stream(paths_to_delete_stream);

    let (num_deleted_files, num_errors) = delete_stream
        .fold((0, 0), |(deleted_count, error_count), res| async move {
            match res {
                Ok(_) => (deleted_count + 1, error_count),
                Err(e) => {
                    tracing::warn!("vacuum-index batch delete stream error: {:?}", e);
                    (deleted_count, error_count + 1)
                }
            }
        })
        .await;

    VACUUM_INDEX_METERS
        .files_deleted
        .add(num_deleted_files as u64, &[]);

    if num_errors > 0 {
        VACUUM_INDEX_METERS
            .delete_stream_errors
            .add(num_errors as u64, &[]);
        tracing::warn!(
            deleted_files = num_deleted_files,
            errors = num_errors,
            planned_deletes = planned_num_deletes,
            planned_bytes = planned_total_bytes,
            "Vacuum-index batch completed with errors"
        );
        return Err(anyhow!("vacuum-index batch completed with errors"));
    }

    VACUUM_INDEX_METERS
        .segments_processed
        .add(num_segments as u64, &[]);
    VACUUM_INDEX_METERS
        .bytes_deleted
        .add(planned_total_bytes, &[]);
    tracing::debug!(
        deleted_files = num_deleted_files,
        deleted_bytes = planned_total_bytes,
        "Vacuum-index batch completed successfully"
    );

    Ok(VacuumIndexBatchOutput {
        planned_num_deletes,
        planned_total_bytes,
        num_deleted_files,
    })
}

struct ActiveIndexInfo {
    active_index_chunk_uuids: Vec<SegmentId>,
    footer_file_path: String,
}

fn get_active_index_info(segment_metadata: SegmentMetadata) -> Result<Option<ActiveIndexInfo>> {
    let index_meta = segment_metadata.last_compacted_index_meta;
    if let Some(index_meta) = index_meta {
        let active_index_chunk_uuids: Vec<SegmentId> = index_meta
            .tantivy_meta
            .segments
            .iter()
            .map(|segment_meta| segment_meta.segment_id)
            .collect();
        let footer_file_path = make_footer_fname(&index_meta.tantivy_meta).and_then(|path| {
            path.as_os_str()
                .to_str()
                .map(|s| s.to_string())
                .ok_or(util::anyhow::anyhow!("Failed to make footer fname"))
        })?;
        Ok(Some(ActiveIndexInfo {
            active_index_chunk_uuids,
            footer_file_path,
        }))
    } else {
        Ok(None)
    }
}

fn should_vacuum_index_object(
    max_file_last_modified: DateTime<Utc>,
    object_meta: &ObjectMeta,
    active_index_info: &ActiveIndexInfo,
    delete_unrecognized_files: bool,
) -> bool {
    let filename = match object_meta.location.filename() {
        None => return false,
        Some("meta.json") => return false,
        Some(".managed.json") => return false,
        Some(filename) if filename == active_index_info.footer_file_path => return false,
        Some(filename) => filename,
    };

    let chunk_uuid_opt = extract_segment_id(&std::path::Path::new(filename));

    // As a safety measure, if `delete_unrecognized_files` is false, only delete files that appear
    // to be valid tantivy index files.
    if !delete_unrecognized_files {
        if chunk_uuid_opt.is_none() && !filename.ends_with(".footer") {
            tracing::debug!("Skipping unrecognized file: {}", filename);
            return false;
        }
    }

    // Only vacuum files that don't appear in the index meta and were last
    // modified before the file deletion cutoff (grace period).
    if object_meta.last_modified > max_file_last_modified
        || chunk_uuid_opt
            .map(|chunk_uuid| {
                active_index_info
                    .active_index_chunk_uuids
                    .contains(&chunk_uuid)
            })
            .unwrap_or(false)
    {
        tracing::debug!("Skipping index file {} because it's referenced in the index meta or was modified after the cutoff for file deletion", filename);
        return false;
    }

    tracing::debug!(
        "Found vacuum-eligible index file: {}",
        object_meta.location.to_string()
    );

    true
}
