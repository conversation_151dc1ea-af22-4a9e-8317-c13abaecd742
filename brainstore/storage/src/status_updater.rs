use async_trait::async_trait;
use std::sync::{atomic::AtomicUsize, Arc, Mutex};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;

use tracing::Instrument;
use util::{
    anyhow::{anyhow, Result},
    uuid::Uuid,
};

use crate::global_store::{GlobalStore, LastIndexOpTokenOpts, LastIndexOperation};

/// Main trait for the StatusUpdater, which allows publishing updates to an operation over a
/// segment in a background async task.
#[async_trait]
pub trait StatusUpdate {
    type EnsureNextUpdateGuard;

    /// When initialized, the updates published by the StatusUpdater may not be applied if there is
    /// another operation in progress (ideally holding a lock). Call this method once you are sure
    /// that this status updater is the one with exclusive access to the segment, and it will ensure
    /// the next update is applied. From then on, updates from this StatusUpdater will be applied
    /// until you call `finish`, or until somebody else calls update with `ensure_next_update`.
    ///
    /// Returns a guard object which calls `clear_ensure_next_update` when dropped, to ensure that
    /// the overriding behavior will not persist beyond the lifetime of the guard.
    fn ensure_next_update(&self) -> Self::EnsureNextUpdateGuard;

    /// In the event that you lose exclusive access to the resource before the status updater
    /// finishes, you can call this method to clear out any pending `ensure_next_update` call.
    fn clear_ensure_next_update(&self);

    /// Publish a merge update to the StatusUpdater. Returns a counter token which can be used to
    /// wait until all updates up to this one have been published.
    fn update(&self, op: LastIndexOperation) -> usize;

    /// Wait until the counter token is reached or surpassed.
    async fn wait_until(&self, counter: usize);

    /// Publish a final "finished" update to the StatusUpdater and clean up the object. This method
    /// should be called exactly once at the end of its lifetime.
    async fn finish(mut self) -> Result<()>;
}

/// A simple background task that queues and processes status updates.
#[derive(Debug)]
pub struct StatusUpdater {
    inner: Arc<StatusUpdaterInner>,
    background_task: Option<JoinHandle<()>>,
}

#[cfg(not(test))]
static HEARTBEAT_INTERVAL_MS: std::time::Duration = std::time::Duration::from_secs(30);

#[cfg(test)]
static HEARTBEAT_INTERVAL_MS: std::time::Duration = std::time::Duration::from_millis(10);

#[derive(Debug)]
struct StatusUpdaterInner {
    global_store: Arc<dyn GlobalStore>,
    segment_id: Uuid,
    op_token: Uuid,
    curr_op: Mutex<CurrentOperation>,
    notify_updated_op: tokio::sync::Notify,
    updated_counter: AtomicUsize,
    notify_completed_update: tokio::sync::Notify,
}

impl StatusUpdater {
    pub fn new(global_store: Arc<dyn GlobalStore>, segment_id: Uuid) -> Self {
        let inner = Arc::new(StatusUpdaterInner {
            global_store,
            segment_id,
            op_token: Uuid::new_v4(),
            curr_op: Mutex::new(CurrentOperation::default()),
            notify_updated_op: tokio::sync::Notify::new(),
            notify_completed_update: tokio::sync::Notify::new(),
            updated_counter: AtomicUsize::new(0),
        });
        Self {
            inner: inner.clone(),
            background_task: Some(tokio::spawn(
                async move { Self::update_task(inner).await }
                    .instrument(tracing::info_span!("status updater")),
            )),
        }
    }

    pub fn op_token(&self) -> Uuid {
        self.inner.op_token
    }

    async fn update_task(state: Arc<StatusUpdaterInner>) {
        loop {
            tokio::select! {
                    _ = state.notify_updated_op.notified() => {
                    let current_operation = {
                        let mut guard = state.curr_op.lock().unwrap();
                        // Skip if we've already published this operation.
                        if guard.counter
                            <= state
                                .updated_counter
                                .load(std::sync::atomic::Ordering::Relaxed)
                        {
                            continue;
                        }
                        let ret = (*guard).clone();
                        // Clear the update options so they don't get applied to the next one.
                        guard.opts = LastIndexOpTokenOpts::default();
                        ret
                    };
                    match state
                        .global_store
                        .upsert_last_index_operation(
                            state.segment_id,
                            current_operation.op,
                            state.op_token,
                            current_operation.opts,
                        )
                        .await
                    {
                        Ok(_) => {}
                        Err(e) => {
                            log::warn!("error updating status: {:?}", e);
                        }
                    }
                    state.updated_counter.store(
                        current_operation.counter,
                        std::sync::atomic::Ordering::Relaxed,
                    );
                    state.notify_completed_update.notify_one();
                    // We only clear the op token on the final publish.
                    if current_operation.opts.clear_value {
                        break;
                    }
                }
                _ = tokio::time::sleep(HEARTBEAT_INTERVAL_MS) => {
                    match state.global_store.bump_last_index_operation_updated_ts(state.segment_id, state.op_token).await {
                        Ok(_) => {}
                        Err(e) => {
                            log::warn!("error updating status (heartbeat): {:?}", e);
                        }
                    }
                }
            }
        }
    }

    fn update_helper(&self, op: LastIndexOperation, opts: LastIndexOpTokenOpts) -> usize {
        let counter = {
            let mut curr_op = self.inner.curr_op.lock().unwrap();
            curr_op.merge(op, opts)
        };
        self.inner.notify_updated_op.notify_one();
        counter
    }
}

pub struct EnsureNextUpdateGuard {
    status_updater: Arc<StatusUpdaterInner>,
}

impl Drop for EnsureNextUpdateGuard {
    fn drop(&mut self) {
        self.status_updater
            .curr_op
            .lock()
            .unwrap()
            .opts
            .always_update = false;
    }
}

#[async_trait]
impl StatusUpdate for StatusUpdater {
    type EnsureNextUpdateGuard = EnsureNextUpdateGuard;

    fn ensure_next_update(&self) -> Self::EnsureNextUpdateGuard {
        // Don't use update_helper because we don't want to bump the counter / trigger the loop.
        let mut curr_op = self.inner.curr_op.lock().unwrap();
        curr_op.opts.always_update = true;
        EnsureNextUpdateGuard {
            status_updater: self.inner.clone(),
        }
    }

    fn clear_ensure_next_update(&self) {
        let mut curr_op = self.inner.curr_op.lock().unwrap();
        curr_op.opts.always_update = false;
    }

    fn update(&self, op: LastIndexOperation) -> usize {
        self.update_helper(op, Default::default())
    }

    async fn wait_until(&self, counter: usize) {
        loop {
            if self
                .inner
                .updated_counter
                .load(std::sync::atomic::Ordering::Relaxed)
                >= counter
            {
                return;
            }
            self.inner.notify_completed_update.notified().await;
        }
    }

    async fn finish(mut self) -> Result<()> {
        let background_task = self
            .background_task
            .take()
            .ok_or_else(|| anyhow!("StatusUpdater already finished"))?;
        let counter = self.update_helper(
            LastIndexOperation {
                stage: Some("finished".to_string()),
                estimated_progress: Some(1.0),
                finished: Some(true),
                ..Default::default()
            },
            LastIndexOpTokenOpts {
                clear_value: true,
                ..Default::default()
            },
        );
        self.wait_until(counter).await;
        Ok(background_task.await?)
    }
}

#[async_trait]
impl StatusUpdate for Option<StatusUpdater> {
    type EnsureNextUpdateGuard = Option<EnsureNextUpdateGuard>;

    fn ensure_next_update(&self) -> Self::EnsureNextUpdateGuard {
        self.as_ref()
            .map(|status_updater| status_updater.ensure_next_update())
    }

    fn clear_ensure_next_update(&self) {
        if let Some(status_updater) = self {
            status_updater.clear_ensure_next_update();
        }
    }

    fn update(&self, op: LastIndexOperation) -> usize {
        if let Some(status_updater) = self {
            status_updater.update(op)
        } else {
            0
        }
    }

    async fn wait_until(&self, counter: usize) {
        if let Some(status_updater) = self {
            status_updater.wait_until(counter).await;
        }
    }

    async fn finish(mut self) -> Result<()> {
        if let Some(status_updater) = self {
            status_updater.finish().await
        } else {
            Ok(())
        }
    }
}

impl Drop for StatusUpdater {
    fn drop(&mut self) {
        if self.background_task.is_some() {
            panic!("StatusUpdater dropped without calling finish");
        }
    }
}

pub struct ProgressCounter<'a> {
    status_updater: &'a Option<StatusUpdater>,
    numerator: AtomicUsize,
    denominator: AtomicUsize,
}

impl<'a> ProgressCounter<'a> {
    pub fn new(status_updater: &'a Option<StatusUpdater>) -> Self {
        Self {
            status_updater,
            numerator: AtomicUsize::new(0),
            denominator: AtomicUsize::new(0),
        }
    }

    pub fn add_total(&self, total: usize) {
        self.denominator
            .fetch_add(total, std::sync::atomic::Ordering::Relaxed);
        self.update();
    }

    pub fn add_processed(&self, processed: usize) {
        self.numerator
            .fetch_add(processed, std::sync::atomic::Ordering::Relaxed);
        self.update();
    }

    fn update(&self) {
        if self.denominator.load(std::sync::atomic::Ordering::Relaxed) > 0 {
            self.status_updater.update(LastIndexOperation {
                estimated_progress: Some(
                    self.numerator.load(std::sync::atomic::Ordering::Relaxed) as f64
                        / self.denominator.load(std::sync::atomic::Ordering::Relaxed) as f64,
                ),
                ..Default::default()
            });
        }
    }
}

fn merge_last_index_op(lhs: &mut LastIndexOperation, rhs: LastIndexOperation) {
    if let Some(stage) = rhs.stage {
        lhs.stage = Some(stage);
    }
    if let Some(error) = rhs.error {
        lhs.error = Some(error);
    }
    if let Some(details) = rhs.details {
        lhs.details = Some(details);
    }
    if let Some(finished) = rhs.finished {
        lhs.finished = Some(finished);
    }
    if let Some(estimated_progress) = rhs.estimated_progress {
        lhs.estimated_progress = Some(estimated_progress);
    }
}

#[derive(Clone, Default, Debug)]
struct CurrentOperation {
    op: LastIndexOperation,
    counter: usize,
    opts: LastIndexOpTokenOpts,
}

impl CurrentOperation {
    fn merge(&mut self, other: LastIndexOperation, opts: LastIndexOpTokenOpts) -> usize {
        merge_last_index_op(&mut self.op, other);
        self.counter += 1;
        self.opts.always_update |= opts.always_update;
        self.opts.clear_value |= opts.clear_value;
        self.counter
    }
}
