use crate::global_locks_manager::{GlobalLocksManager, LockState, MemoryGlobalLocksManager};
use crate::global_store_test::get_postgres_global_store_migration;
use crate::object_store_locks_manager::ObjectStoreLocksManager;
use crate::postgres_locks_manager::{PostgresGlobalLocksManager, DEFAULT_MAX_PG_LOCKS};
use crate::redis_locks_manager::{
    RedisLocksManager, DEFAULT_LOCK_PREFIX, DEFAULT_LOCK_TTL_SECONDS,
};
use crate::test_util::{MinioContainer, PostgresContainer, RedisContainer};

use async_util::test_util::MaxCounterGuard;
use base64::{prelude::BASE64_STANDARD, Engine};
use futures::{self, join};
use object_store::ObjectStore;
use std::sync::Arc;
use std::time::Duration;
use std::{collections::HashMap, future::Future};
use util::max_counter::MaxCounter;
use util::test_util::assert_hashmap_eq;
use util::uuid::Uuid;

async fn read_lock_task(
    locks: Arc<dyn GlobalLocksManager>,
    name: String,
    counter: Arc<MaxCounter>,
) {
    let _guard = locks.read(&name).await.unwrap();
    let _counter_guard = MaxCounterGuard::new(&counter).await;
}

async fn write_lock_task(
    locks: Arc<dyn GlobalLocksManager>,
    name: String,
    counter: Arc<MaxCounter>,
) {
    let _guard = locks.write(&name).await.unwrap();
    let _counter_guard = MaxCounterGuard::new(&counter).await;
}

async fn try_read_lock_task(
    locks: Arc<dyn GlobalLocksManager>,
    name: String,
    counter: Arc<MaxCounter>,
) {
    let _guard = locks.try_read(&name).await.unwrap();
    let _counter_guard: Option<MaxCounterGuard>;
    if _guard.is_some() {
        _counter_guard = Some(MaxCounterGuard::new(&counter).await);
    }
}

async fn try_write_lock_task(
    locks: Arc<dyn GlobalLocksManager>,
    name: String,
    counter: Arc<MaxCounter>,
) {
    let _guard = locks.try_write(&name).await.unwrap();
    let _counter_guard: Option<MaxCounterGuard>;
    if _guard.is_some() {
        _counter_guard = Some(MaxCounterGuard::new(&counter).await);
    }
}

async fn test_global_locks_combo<Func1, Func2, Fut1, Fut2>(
    locks: Arc<dyn GlobalLocksManager>,
    name: &str,
    a_fn: Func1,
    b_fn: Func2,
    expected_max: u64,
) where
    Func1: Fn(Arc<dyn GlobalLocksManager>, String, Arc<MaxCounter>) -> Fut1,
    Func2: Fn(Arc<dyn GlobalLocksManager>, String, Arc<MaxCounter>) -> Fut2,
    Fut1: Future<Output = ()>,
    Fut2: Future<Output = ()>,
{
    let counter = Arc::new(MaxCounter::default());
    let a = a_fn(locks.clone(), name.to_string(), counter.clone());
    let b = b_fn(locks.clone(), name.to_string(), counter.clone());
    let _ = join!(a, b);
    assert_eq!(counter.counter(), 0);
    assert_eq!(counter.max(), expected_max);
}

async fn test_global_locks_manager_contention(locks: Arc<dyn GlobalLocksManager>) {
    // Read-read.
    if locks.supports_read_locks() {
        test_global_locks_combo(
            locks.clone(),
            "read-read",
            read_lock_task,
            read_lock_task,
            2,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "try-read-read",
            try_read_lock_task,
            read_lock_task,
            2,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "read-try-read",
            read_lock_task,
            try_read_lock_task,
            2,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "try-read-try-read",
            try_read_lock_task,
            try_read_lock_task,
            2,
        )
        .await;

        // Read-write.
        test_global_locks_combo(
            locks.clone(),
            "read-write",
            read_lock_task,
            write_lock_task,
            1,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "try-read-write",
            try_read_lock_task,
            write_lock_task,
            1,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "read-try-write",
            read_lock_task,
            try_write_lock_task,
            1,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "try-read-try-write",
            try_read_lock_task,
            try_write_lock_task,
            1,
        )
        .await;

        // Write-read.
        test_global_locks_combo(
            locks.clone(),
            "write-read",
            write_lock_task,
            read_lock_task,
            1,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "write-try-read",
            write_lock_task,
            try_read_lock_task,
            1,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "try-write-read",
            try_write_lock_task,
            read_lock_task,
            1,
        )
        .await;
        test_global_locks_combo(
            locks.clone(),
            "try-write-try-read",
            try_write_lock_task,
            try_read_lock_task,
            1,
        )
        .await;
    }

    // Write-write.
    test_global_locks_combo(
        locks.clone(),
        "write-write",
        write_lock_task,
        write_lock_task,
        1,
    )
    .await;
    test_global_locks_combo(
        locks.clone(),
        "try-write-write",
        try_write_lock_task,
        write_lock_task,
        1,
    )
    .await;
    test_global_locks_combo(
        locks.clone(),
        "write-try-write",
        write_lock_task,
        try_write_lock_task,
        1,
    )
    .await;
    test_global_locks_combo(
        locks.clone(),
        "try-write-try-write",
        try_write_lock_task,
        try_write_lock_task,
        1,
    )
    .await;
}

async fn test_global_locks_manager_snapshot(locks: Arc<dyn GlobalLocksManager>) {
    {
        let _guard0 = locks.read("lock0").await.unwrap();
        {
            let _guard1 = locks.write("lock1").await.unwrap();
            assert_hashmap_eq(
                &locks.snapshot_lock_state().await.unwrap(),
                &[
                    ("lock0".to_string(), LockState::Read),
                    ("lock1".to_string(), LockState::Write),
                ]
                .into_iter()
                .collect(),
            );
        }
        assert_hashmap_eq(
            &locks.snapshot_lock_state().await.unwrap(),
            &[("lock0".to_string(), LockState::Read)]
                .into_iter()
                .collect(),
        );
        {
            let _guard1 = locks.read("lock1").await.unwrap();
            assert_hashmap_eq(
                &locks.snapshot_lock_state().await.unwrap(),
                &[
                    ("lock0".to_string(), LockState::Read),
                    ("lock1".to_string(), LockState::Read),
                ]
                .into_iter()
                .collect(),
            );
        }
        assert_hashmap_eq(
            &locks.snapshot_lock_state().await.unwrap(),
            &[("lock0".to_string(), LockState::Read)]
                .into_iter()
                .collect(),
        );
    }
    assert_hashmap_eq(&locks.snapshot_lock_state().await.unwrap(), &HashMap::new());
}

async fn test_global_locks_manager(locks: Arc<dyn GlobalLocksManager>) {
    // Status check should succeed.
    locks.status().await.unwrap();

    test_global_locks_manager_contention(locks.clone()).await;
    if locks.supports_read_locks() {
        test_global_locks_manager_snapshot(locks.clone()).await;
    }

    // Test that status works and returns a meaningful message
    let status = locks.status().await.unwrap();
    assert!(!status.is_empty());

    // Acquire a lock and verify status still works
    let _guard = locks.write("status-test-lock").await.unwrap();
    let status_with_lock = locks.status().await.unwrap();
    assert!(!status_with_lock.is_empty());
}

#[tokio::test]
async fn test_memory_global_locks_manager() {
    let locks = Arc::new(MemoryGlobalLocksManager::default());
    test_global_locks_manager(locks).await;
}

#[tokio::test]
async fn test_postgres_global_locks_manager() {
    let container = PostgresContainer::new().await;
    container
        .run_migration(&get_postgres_global_store_migration())
        .await
        .unwrap();

    let locks_manager = Arc::new(
        PostgresGlobalLocksManager::new(&container.connection_url, DEFAULT_MAX_PG_LOCKS).unwrap(),
    );
    test_global_locks_manager(locks_manager).await;
}

#[tokio::test]
async fn test_redis_global_locks_manager() {
    let container = RedisContainer::new().await;
    let locks_manager = Arc::new(
        RedisLocksManager::new_with_opts(
            vec![container.connection_url.clone()],
            DEFAULT_LOCK_PREFIX.to_string(),
            Duration::from_secs(DEFAULT_LOCK_TTL_SECONDS),
            true,
        )
        .unwrap(),
    );
    test_global_locks_manager(locks_manager).await;
}

#[tokio::test]
async fn test_object_store_global_locks_manager() {
    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();
    let locks_manager = Arc::new(ObjectStoreLocksManager::new(object_store));

    test_global_locks_manager(locks_manager).await;
}

#[tokio::test]
async fn test_object_store_locks_with_custom_ttl() {
    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();
    let locks_manager = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store,
        "custom-locks".to_string(),
        Duration::from_secs(2), // Short TTL for testing
    ));

    // Test that lock is acquired successfully
    let guard = locks_manager.try_write("test-lock").await.unwrap();
    assert!(guard.is_some());

    // Verify snapshot shows the lock
    let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
    assert_eq!(snapshot.len(), 1);
    assert_eq!(snapshot.get("test-lock"), Some(&LockState::Write));

    // Drop the guard to release the lock
    drop(guard);

    // Give time for the background task to clean up
    tokio::time::sleep(Duration::from_millis(100)).await;

    // Verify lock is released
    let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
    assert_eq!(snapshot.len(), 0);
}

#[tokio::test]
async fn test_object_store_locks_refresh() {
    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();

    // Use a short TTL to test refresh mechanism
    let locks_manager = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store,
        "refresh-test".to_string(),
        Duration::from_secs(1),
    ));

    // Acquire a lock
    let guard = locks_manager.write("refresh-lock").await.unwrap();

    // Wait longer than the TTL to ensure refresh is working
    tokio::time::sleep(Duration::from_secs(2)).await;

    // Lock should still be held due to refresh
    let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
    assert_eq!(snapshot.len(), 1);
    assert_eq!(snapshot.get("refresh-lock"), Some(&LockState::Write));

    // Try to acquire the same lock from another task - should fail
    let guard2 = locks_manager.try_write("refresh-lock").await.unwrap();
    assert!(guard2.is_none());

    // Drop the guard
    drop(guard);

    // Give time for cleanup
    tokio::time::sleep(Duration::from_millis(200)).await;

    // Lock should be released now
    let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
    assert_eq!(snapshot.len(), 0);
}

#[tokio::test]
async fn test_object_store_locks_expired_lock_takeover() {
    // This test specifically tests the CAS logic for taking over an expired lock
    use object_store::{PutMode, PutOptions, PutPayload};
    use util::chrono::Utc;

    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();

    // Manually create an expired lock file to simulate a crashed process
    let expired_content = serde_json::json!({
        "version": 1,
        "expires_at": Utc::now().timestamp() - 100  // Expired 100 seconds ago
    });
    let path = object_store::path::Path::from_iter(["expired-test", "expired-lock"]);
    object_store
        .put_opts(
            &path,
            PutPayload::from(serde_json::to_vec(&expired_content).unwrap()),
            PutOptions::from(PutMode::Create),
        )
        .await
        .unwrap();

    // Create a lock manager
    let locks_manager = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store.clone(),
        "expired-test".to_string(),
        Duration::from_secs(60),
    ));

    // Should be able to take over the expired lock via CAS
    let guard = locks_manager.try_write("expired-lock").await.unwrap();
    assert!(guard.is_some());

    // Verify the lock is now held with fresh expiration
    let snapshot = locks_manager.snapshot_lock_state().await.unwrap();
    assert_eq!(snapshot.len(), 1);
    assert_eq!(snapshot.get("expired-lock"), Some(&LockState::Write));

    // Another manager shouldn't be able to acquire it now
    let locks_manager2 = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store.clone(),
        "expired-test".to_string(),
        Duration::from_secs(60),
    ));
    let guard2 = locks_manager2.try_write("expired-lock").await.unwrap();
    assert!(guard2.is_none());
}

#[tokio::test]
async fn test_object_store_locks_concurrent_cas() {
    // Test that only one process wins when multiple try to CAS an expired lock
    use object_store::{PutMode, PutOptions, PutPayload};
    use util::chrono::Utc;

    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();

    // Manually create an expired lock
    let expired_content = serde_json::json!({
        "version": 1,
        "expires_at": Utc::now().timestamp() - 10  // Expired 10 seconds ago
    });
    let path = object_store::path::Path::from_iter(["cas-test", "race-lock"]);
    object_store
        .put_opts(
            &path,
            PutPayload::from(serde_json::to_vec(&expired_content).unwrap()),
            PutOptions::from(PutMode::Create),
        )
        .await
        .unwrap();

    // Create multiple managers that will race to acquire the expired lock
    let managers: Vec<_> = (0..5)
        .map(|_| {
            Arc::new(ObjectStoreLocksManager::new_with_opts(
                object_store.clone(),
                "cas-test".to_string(),
                Duration::from_secs(60),
            ))
        })
        .collect();

    // All managers try to acquire the expired lock concurrently
    let handles: Vec<_> = managers
        .iter()
        .map(|manager| {
            let m = manager.clone();
            tokio::spawn(async move { m.try_write("race-lock").await.unwrap() })
        })
        .collect();

    // Wait for all attempts to complete
    let results: Vec<_> = futures::future::join_all(handles)
        .await
        .into_iter()
        .map(|r| r.unwrap())
        .collect();

    // Exactly one should succeed
    let successful_count = results.iter().filter(|r| r.is_some()).count();
    assert_eq!(successful_count, 1);
}

#[tokio::test]
async fn test_object_store_locks_no_expired_takeover() {
    // Test that non-expired locks cannot be taken over
    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();

    let locks_manager1 = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store.clone(),
        "active-test".to_string(),
        Duration::from_secs(60), // Long TTL
    ));

    let locks_manager2 = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store.clone(),
        "active-test".to_string(),
        Duration::from_secs(60),
    ));

    // First manager acquires lock
    let _guard1 = locks_manager1.write("active-lock").await.unwrap();

    // Second manager tries but fails (lock is not expired)
    let guard2 = locks_manager2.try_write("active-lock").await.unwrap();
    assert!(guard2.is_none());

    // Even after some time (but less than TTL), still can't acquire
    tokio::time::sleep(Duration::from_millis(500)).await;
    let guard2_retry = locks_manager2.try_write("active-lock").await.unwrap();
    assert!(guard2_retry.is_none());
}

#[tokio::test]
async fn test_object_store_locks_create_vs_update_paths() {
    // Test both code paths: create (new lock) and update (expired lock)
    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();
    let locks_manager = Arc::new(ObjectStoreLocksManager::new(object_store));

    // Path 1: Create - lock doesn't exist yet
    let guard1 = locks_manager.try_write("new-lock").await.unwrap();
    assert!(guard1.is_some());
    drop(guard1);

    // Wait for cleanup
    tokio::time::sleep(Duration::from_millis(100)).await;

    // Path 1 again: Create - lock was deleted
    let guard2 = locks_manager.try_write("new-lock").await.unwrap();
    assert!(guard2.is_some());

    // Path 2: Update will be tested by the expired lock tests above
}

#[tokio::test]
async fn test_object_store_locks_simulated_process_crash() {
    // Simulate a process that crashes while holding a lock
    // Since we can't actually stop the refresh task without dropping the guard,
    // we'll create a lock file manually that represents a crashed process
    use object_store::{PutMode, PutOptions, PutPayload};
    use util::chrono::Utc;

    let minio = MinioContainer::new().await;
    let object_store = minio.create_object_store().unwrap();

    // Manually create a lock file that would have been left by a crashed process
    // It has a recent timestamp but no active refresh task
    let recent_but_stale = serde_json::json!({
        "version": 1,
        "expires_at": Utc::now().timestamp() + 2,  // Expires in 2 seconds
        "nonce": Uuid::new_v4().to_string(),
    });

    let name_base64 = BASE64_STANDARD.encode("crash-lock");
    let path = object_store::path::Path::from_iter(["crash-test", name_base64.as_str()]);
    object_store
        .put_opts(
            &path,
            PutPayload::from(serde_json::to_vec(&recent_but_stale).unwrap()),
            PutOptions::from(PutMode::Create),
        )
        .await
        .unwrap();

    // Another process can't take it immediately (not expired yet)
    let locks_manager1 = Arc::new(ObjectStoreLocksManager::new_with_opts(
        object_store.clone(),
        "crash-test".to_string(),
        Duration::from_secs(60),
    ));

    let guard1 = locks_manager1.try_write("crash-lock").await.unwrap();
    assert!(guard1.is_none()); // Can't acquire - not expired

    // Wait for the lock to expire
    tokio::time::sleep(Duration::from_secs(3)).await;

    // Now another process should be able to take over the expired lock
    let guard2 = locks_manager1.try_write("crash-lock").await.unwrap();
    assert!(guard2.is_some());

    // Verify new manager holds the lock
    let snapshot = locks_manager1.snapshot_lock_state().await.unwrap();
    assert_eq!(snapshot.len(), 1);
    assert_eq!(snapshot.get("crash-lock"), Some(&LockState::Write));
}
