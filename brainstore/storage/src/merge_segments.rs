// NOTE: Merging segments as implemented doesn't work for a variety of reasons
// littered through comments. We will probably need to think of a different
// approach if we end up needing this.

use std::{
    collections::HashSet,
    path::{Path, PathBuf},
    sync::Arc,
};
use tokio_stream::StreamExt;
use util::{
    anyhow::{anyhow, Result},
    functional::merge_options,
    futures::{future::join_all, join, TryStreamExt},
    system_types::FullObjectId,
    test_util::TwoWaySyncPointSendAndWait,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    config_with_store::StoreInfo,
    global_locks_manager::{GlobalLockWriteGuard, GlobalLocksManager},
    global_store::{GlobalStore, LastCompactedIndexMeta, SegmentMetadataUpdate},
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    process_wal::{compact_segment_wal, CompactSegmentWalInput, CompactSegmentWalOptions},
    tantivy_index::{
        check_and_set_meta_schema, collect_meta_json, merge_index_meta, write_meta_json,
        IndexMetaJson, TantivyIndexScope,
    },
    wal::WALScope,
};

pub struct MergeSegmentsInput<'a> {
    pub object_id: FullObjectId<'a>,
    pub src_segment_ids: &'a [Uuid],
    pub dst_segment_id: Uuid,
    pub index_store: StoreInfo,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub global_store: Arc<dyn GlobalStore>,
    pub compact_segment_wal_schema: util::schema::Schema,
    pub compact_segment_wal_options: CompactSegmentWalOptions,
}

#[derive(Default)]
pub struct MergeSegmentsOptionalInput {
    pub testing_sync_points: MergeSegmentsTestingSyncPoints,
}

#[derive(Default)]
pub struct MergeSegmentsTestingSyncPoints {
    pub before_phase_1: Option<TwoWaySyncPointSendAndWait>,
    pub before_phase_2: Option<TwoWaySyncPointSendAndWait>,
    pub phase_2_before_update_segment_liveness: Option<TwoWaySyncPointSendAndWait>,
}

// Merge the currently-live source segments into a new destination segment, copying over all
// segment data and initializing the destination segment metadata appropriately. At the end of the
// function, the source segments will have been marked non-live and the destination segment added
// as live, with all writes to the source row IDs now flowing to the new destination segment.
//
// Note: we do not support merging into an existing destination segment for several reasons:
//
//      - Fault tolerance: if the merge fails midway, we'll have a bunch of junk data in the
//      destination. This could be problematic since now we have multiple live segments with
//      duplicates of some rows.
//
//      - Metadata management: the WAL data from the source segments can have arbitrary transaction
//      IDs. This makes it tricky to keep track of a single stream of un-compacted WAL entries for
//      the destination segment, since it could consist of arbitrary ranges of xact_ids from the
//      different sources. For this reason, we make sure to compact all source segments before
//      copying into the destination. If the destination segment was live, we'd have to lock it
//      while doing all this compaction and copying to ensure we don't add uncompacted entries to
//      the destination WAL which might create disjoint un-compacted WAL entry streams that could
//      be expensive to re-compact. Furthermore, we could run into deadlock issues if we lock the
//      destination segment and then later lock the source segments when we're ready to do final
//      copies.
//
#[allow(clippy::needless_lifetimes)] // We actually need the lifetime down below.
pub async fn merge_segments<'a>(
    input: MergeSegmentsInput<'a>,
    optional_input: MergeSegmentsOptionalInput,
) -> Result<()> {
    // Procedure:
    //
    // 0. Check that the destination segment does not exist (or is not live) in the global store.
    //
    // 1. Phase 1 - unlocked merge (this could take a while):
    //  - Concurrently:
    //      - Copy WALs from source segments to the destination.
    //      - Run compaction on the source segments to process as many un-compacted WAL entries as
    //      possible.
    //      - Copy over the row_id/root_span_id info from the source segments to the destination.
    //
    // 2. Phase 2 - locked merge (this should be quick):
    //  - Obtain write locks for the source WALs
    //  - Concurrently:
    //      - Re-copy row_id/root_span_id info
    //          - This copy could take some time if there are many rows and postgres does a full
    //          table scan. We could speed this up in a few ways:
    //              - Force an index-only scan on the table. We could use `SET enable_seqscan=off`.
    //              - Install temporary triggers in the unlocked phase which copy over new row
    //              infos from the source segments to the destination, which should let us skip
    //              this second copy altogether.
    //      - Re-compact source segments
    //  - Concurrently:
    //      - Copy fully-compacted indices from source segments to the destination.
    //      - Re-copy WALs.
    //  - Populate a SegmentMetadata for the destination, which is max(last_compacted_xact_id),
    //  destination_meta_json, min(minimum_pagination_key), and sum(num_rows) across all the source
    //  segment metadatas.
    //  - Mark the source segments as deleted in the global store and add the destination segment
    //  as live.
    if input.src_segment_ids.is_empty() {
        return Err(anyhow!("No source segments provided"));
    }

    if let Ok(info) = input
        .global_store
        .query_segment_liveness(&[input.dst_segment_id])
        .await
    {
        if info[0].is_live {
            return Err(anyhow!(
                "Destination segment {} already exists and is live",
                input.dst_segment_id
            ));
        }
    }

    let index_store = input.index_store;
    let merge_unlocked_input = MergeSegmentsUnlockedInput {
        src_segment_ids: input.src_segment_ids,
        dst_segment_id: input.dst_segment_id,
        index_store: &index_store,
        global_store: input.global_store.clone(),
    };

    let run_compaction = |segment_id: Uuid| {
        let index_store = index_store.clone();
        let schema = input.compact_segment_wal_schema.clone();
        let global_store = input.global_store.clone();
        let locks_manager = input.locks_manager;
        let options = input.compact_segment_wal_options.clone();
        async move {
            compact_segment_wal(
                CompactSegmentWalInput {
                    segment_id,
                    index_store,
                    schema,
                    global_store,
                    locks_manager,
                },
                Default::default(),
                options,
            )
            .await?;
            Ok(())
        }
    };

    // Phase 1.
    {
        if let Some(sync_point) = optional_input.testing_sync_points.before_phase_1 {
            sync_point.send_and_wait().await;
        }

        // Copy WALs, copy segment membership, and run compaction over all source segments at once.
        let (res0, res1, res2) = join!(
            merge_segment_wals_unlocked(merge_unlocked_input.clone()),
            input
                .global_store
                .copy_id_segment_membership(input.src_segment_ids, input.dst_segment_id,),
            join_all(
                input
                    .src_segment_ids
                    .iter()
                    .map(|segment_id| run_compaction(*segment_id))
            ),
        );
        res0?;
        res1?;
        res2.into_iter().collect::<Result<()>>()?;
    }

    // Phase 2.
    {
        if let Some(sync_point) = optional_input.testing_sync_points.before_phase_2 {
            sync_point.send_and_wait().await;
        }

        // Obtain locks.
        let mut lock_names: Vec<String> = input
            .src_segment_ids
            .iter()
            .map(|x| WALScope::Segment(*x).lock_name())
            .collect();
        lock_names.sort();
        let mut locks: Vec<Box<dyn GlobalLockWriteGuard>> = Vec::new();
        for lock_name in &lock_names {
            locks.push(input.locks_manager.write(lock_name).await?);
        }

        // Concurrently re-copy segment membership metadata and re-run compaction.
        let (res0, res1) = join!(
            input
                .global_store
                .copy_id_segment_membership(input.src_segment_ids, input.dst_segment_id,),
            join_all(
                input
                    .src_segment_ids
                    .iter()
                    .map(|segment_id| run_compaction(*segment_id))
            ),
        );
        res0?;
        res1.into_iter().collect::<Result<()>>()?;

        // Concurrently merge the compacted segments and re-copy WALs.
        let (res0, res1) = join!(
            merge_tantivy_indices_unlocked(merge_unlocked_input.clone()),
            merge_segment_wals_unlocked(merge_unlocked_input.clone()),
        );
        let dst_tantivy_meta = res0?;
        res1?;

        // Fetch the segment metadatas for the source segments. Use it to construct a destination
        // segment metadata.
        let src_segment_metadatas = input
            .global_store
            .query_segment_metadatas(input.src_segment_ids)
            .await?;
        let merged_segment_meta = src_segment_metadatas.iter().fold(
            MergedSegmentMetadata {
                last_compacted_xact_id: None,
                minimum_pagination_key: PaginationKey(u64::MAX),
                num_rows: 0,
            },
            |acc, src_segment_metadata| MergedSegmentMetadata {
                last_compacted_xact_id: merge_options(
                    acc.last_compacted_xact_id,
                    src_segment_metadata
                        .last_compacted_index_meta
                        .as_ref()
                        .map(|meta| meta.xact_id),
                    std::cmp::max,
                ),
                minimum_pagination_key: std::cmp::min(
                    acc.minimum_pagination_key,
                    src_segment_metadata.minimum_pagination_key,
                ),
                num_rows: acc.num_rows + src_segment_metadata.num_rows,
            },
        );
        assert!(merged_segment_meta.minimum_pagination_key < PaginationKey(u64::MAX));
        let dst_segment_metadata_update = SegmentMetadataUpdate {
            last_compacted_index_meta: Some((
                None,
                merged_segment_meta
                    .last_compacted_xact_id
                    .map(|xact_id| LastCompactedIndexMeta {
                        xact_id,
                        tantivy_meta: dst_tantivy_meta,
                    }),
            )),
            minimum_pagination_key: Some((
                PaginationKey(0),
                merged_segment_meta.minimum_pagination_key,
            )),
            add_num_rows: Some(merged_segment_meta.num_rows),
        };
        input
            .global_store
            .upsert_segment_metadatas(
                [(input.dst_segment_id, dst_segment_metadata_update)]
                    .into_iter()
                    .collect(),
            )
            .await?;

        if let Some(sync_point) = optional_input
            .testing_sync_points
            .phase_2_before_update_segment_liveness
        {
            sync_point.send_and_wait().await;
        }

        // Mark the destination segment as live and remove the source segments.
        input
            .global_store
            .update_segment_ids(&[(
                input.object_id,
                &[input.dst_segment_id],
                input.src_segment_ids,
            )])
            .await?;
    }

    Ok(())
}

#[derive(Clone, Debug)]
pub struct MergeSegmentsUnlockedInput<'a> {
    pub src_segment_ids: &'a [Uuid],
    pub dst_segment_id: Uuid,
    pub index_store: &'a StoreInfo,
    pub global_store: Arc<dyn GlobalStore>,
}

async fn merge_tantivy_indices_unlocked(
    input: MergeSegmentsUnlockedInput<'_>,
) -> Result<IndexMetaJson> {
    // Procedure:
    //
    // 1. Check for meta.json schema compatibility between all source indices: the schemas must
    //    match in all prefixes between themselves and the schema with the greatest number of
    //    fields. We use the longest schema for the destination.
    //
    // 2. Construct a meta.json which combines all segments across all source indices.
    //
    // 3. Copy any new files from the source indices to the destination. We assume that index files
    //    are immutable and unique per segment, so it should be possible to run this function
    //    multiple times over the same destination and keep adding new segments.
    //
    // 4. Write the new meta.json into the destination. Return the new meta.json.

    let segment_index_paths = input
        .src_segment_ids
        .iter()
        .map(|segment_id| TantivyIndexScope::Segment(*segment_id).path(&input.index_store.prefix))
        .collect::<Vec<_>>();

    let src_segment_metas: Vec<(&Path, IndexMetaJson)> =
        join_all(segment_index_paths.iter().map(|segment_index_path| {
            collect_meta_json(input.index_store.directory.as_ref(), segment_index_path)
        }))
        .await
        .into_iter()
        .zip(segment_index_paths.iter())
        .filter_map(|(meta, segment_index_path)| {
            meta.transpose()
                .map(|meta| meta.map(|meta| (segment_index_path.as_path(), meta)))
        })
        .collect::<Result<_>>()?;

    let longest_schema_entry = src_segment_metas
        .iter()
        .max_by_key(|(_, meta)| meta.schema.num_fields());
    let longest_schema = match longest_schema_entry {
        Some((_, meta)) => meta.schema.clone(),
        None => return Ok(IndexMetaJson::default()),
    };

    let src_segment_metas = src_segment_metas
        .into_iter()
        .map(|(segment_index_path, meta)| {
            Ok((
                segment_index_path,
                check_and_set_meta_schema(meta, &longest_schema)?,
            ))
        })
        .collect::<Result<Vec<_>>>()?;
    let merged_metadata = src_segment_metas
        .iter()
        .try_fold(None, |acc, (_, meta)| match acc {
            None => Ok(Some(meta.clone())),
            Some(acc) => Some(merge_index_meta(acc, meta)).transpose(),
        })?
        .unwrap();

    let dst_index_path =
        TantivyIndexScope::Segment(input.dst_segment_id).path(&input.index_store.prefix);
    let dst_index_files = collect_directory_paths(input.index_store, &dst_index_path).await?;

    // Copy all files from the source segments to the destination. Skip segments that are already
    // in the destination.
    let _ = join_all(src_segment_metas.iter().map(|(src_index_path, _)| {
        let index_store = input.index_store;
        let dst_index_path = &dst_index_path;
        let dst_index_files = &dst_index_files;
        async move {
            let paths = collect_directory_paths(index_store, src_index_path).await?;
            join_all(
                paths
                    .into_iter()
                    .filter(|p| !dst_index_files.contains(p.as_path()))
                    .map(|p| {
                        let dst_index_path = &dst_index_path;
                        async move {
                            let res = input
                                .index_store
                                .store
                                .copy(
                                    &make_object_store_path(&src_index_path.join(&p)),
                                    &make_object_store_path(&dst_index_path.join(&p)),
                                )
                                .await;
                            Ok(res?)
                        }
                    }),
            )
            .await
            .into_iter()
            .collect::<Result<()>>()
        }
    }))
    .await
    .into_iter()
    .collect::<Result<Vec<_>, _>>()?;

    write_meta_json(
        input.index_store.directory.as_ref(),
        &dst_index_path,
        &merged_metadata,
    )
    .await?;

    Ok(merged_metadata)
}

async fn merge_segment_wals_unlocked(input: MergeSegmentsUnlockedInput<'_>) -> Result<()> {
    // Procedure:
    //
    // 1. List out all files in the source segment WALs and the destination segment WAL. At some
    //    point, if the WALs get too large, we should be able to paginate these searches.
    //
    // 2. Copy any new files from the source indices to the destination. To avoid conflicting WAL
    //    files between different segments, we add the segment ID to the extension of the WAL file.
    //    So that if the WAL file is [filename].jsonl, it becomes [filename].[segment_id].jsonl. We
    //    can continue appending segment IDs to the filename as the segment is moved across
    //    multiple segments.
    //
    //  In parallel with 1 and 2, re-copy the segment WAL metadata from the source segments to the
    //  destination.

    let copy_wal_metadata_fut = tokio::spawn({
        let global_store = input.global_store.clone();
        let src_segment_ids = input.src_segment_ids.to_vec();
        let dst_segment_id = input.dst_segment_id;
        async move {
            global_store
                .copy_segment_wal_entries(&src_segment_ids, dst_segment_id)
                .await
        }
    });

    let segment_wal = ObjectAndGlobalStoreWal {
        object_store: input.index_store.store.clone(),
        global_store: input.global_store.clone(),
        directory: input.index_store.directory.clone(),
        store_prefix: input.index_store.prefix.clone(),
        store_type: input.index_store.store_type,
    };

    let src_segment_wal_directories: Vec<PathBuf> = input
        .src_segment_ids
        .iter()
        .map(|segment_id| segment_wal.wal_directory(WALScope::Segment(*segment_id)))
        .collect();
    let dst_segment_wal_directory =
        segment_wal.wal_directory(WALScope::Segment(input.dst_segment_id));

    let (src_segment_wal_files, dst_segment_wal_files) = join!(
        join_all(
            src_segment_wal_directories
                .iter()
                .map(|src_segment_wal_directory| {
                    collect_directory_paths(input.index_store, src_segment_wal_directory)
                })
        ),
        collect_directory_paths(input.index_store, &dst_segment_wal_directory),
    );
    let src_segment_wal_files = src_segment_wal_files
        .into_iter()
        .collect::<Result<Vec<_>>>()?;
    let dst_segment_wal_files = dst_segment_wal_files?;

    join_all(
        src_segment_wal_directories
            .iter()
            .zip(src_segment_wal_files.iter())
            .flat_map(|(src_segment_wal_directory, src_segment_wal_files)| {
                src_segment_wal_files.iter().map(|src_segment_wal_file| {
                    copy_segment_wal_file(
                        src_segment_wal_directory,
                        &dst_segment_wal_directory,
                        src_segment_wal_file,
                        &dst_segment_wal_files,
                        input.index_store,
                    )
                })
            }),
    )
    .await
    .into_iter()
    .collect::<Result<()>>()?;

    copy_wal_metadata_fut.await??;
    Ok(())
}

fn make_object_store_path(path: &Path) -> object_store::path::Path {
    object_store::path::Path::from(path.to_str().unwrap())
}

async fn collect_directory_paths(
    index_store: &StoreInfo,
    directory_path: &Path,
) -> Result<HashSet<PathBuf>> {
    let index_files = index_store
        .store
        .list(Some(&make_object_store_path(directory_path)))
        .map(|meta| -> Result<PathBuf> {
            let meta = meta?;
            let loc: String = meta.location.into();
            let full_path = PathBuf::from(loc);
            let rel_path = full_path.strip_prefix(directory_path)?;
            Ok(rel_path.to_path_buf())
        })
        .try_collect()
        .await?;
    Ok(index_files)
}

async fn copy_segment_wal_file(
    src_segment_wal_directory: &Path,
    dst_segment_wal_directory: &Path,
    segment_wal_file: &Path,
    dst_segment_wal_files: &HashSet<PathBuf>,
    index_store: &StoreInfo,
) -> Result<()> {
    // Add the segment_id to the WAL file name to disambiguate between different segments.
    if dst_segment_wal_files.contains(segment_wal_file) {
        return Ok(());
    }
    let src_segment_wal_fullpath = src_segment_wal_directory.join(segment_wal_file);
    let dst_segment_wal_fullpath = dst_segment_wal_directory.join(segment_wal_file);
    index_store
        .store
        .copy(
            &make_object_store_path(&src_segment_wal_fullpath),
            &make_object_store_path(&dst_segment_wal_fullpath),
        )
        .await?;
    Ok(())
}

struct MergedSegmentMetadata {
    last_compacted_xact_id: Option<TransactionId>,
    minimum_pagination_key: PaginationKey,
    num_rows: u64,
}

#[cfg(test)]
pub async fn merge_tantivy_indices_unlocked_for_testing(
    input: MergeSegmentsUnlockedInput<'_>,
) -> Result<IndexMetaJson> {
    merge_tantivy_indices_unlocked(input).await
}

#[cfg(test)]
pub async fn merge_segment_wals_unlocked_for_testing(
    input: MergeSegmentsUnlockedInput<'_>,
) -> Result<()> {
    merge_segment_wals_unlocked(input).await
}
