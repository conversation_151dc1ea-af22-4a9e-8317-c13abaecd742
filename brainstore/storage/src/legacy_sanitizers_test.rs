use util::{
    chrono::{DateTime, Duration, TimeZone, Utc},
    serde_json::json,
};

use crate::legacy_sanitizers::{normalize_dt_field, normalize_list_field, normalize_str_field};

#[test]
fn test_normalize_dt_field() {
    let test_cases = vec![
        (
            "2025-01-31T12:34:56Z",
            Utc.with_ymd_and_hms(2025, 1, 31, 12, 34, 56).unwrap(),
        ),
        (
            "2025-01-31T12:34:56+00:00",
            Utc.with_ymd_and_hms(2025, 1, 31, 12, 34, 56).unwrap(),
        ),
        (
            "2025-01-31T13:34:56+01:00",
            Utc.with_ymd_and_hms(2025, 1, 31, 12, 34, 56).unwrap(),
        ),
        (
            "2025-01-31 12:34:56 UTC",
            Utc.with_ymd_and_hms(2025, 1, 31, 12, 34, 56).unwrap(),
        ),
        (
            "2024-06-27 17:21:50.429+00",
            Utc.from_utc_datetime(
                &(Utc
                    .with_ymd_and_hms(2024, 6, 27, 17, 21, 50)
                    .unwrap()
                    .naive_utc()
                    + Duration::milliseconds(429)),
            ),
        ),
    ];

    for (input, expected) in test_cases {
        let mut value = json!({
            "foo": input,
        });
        normalize_dt_field(value.as_object_mut().unwrap(), "foo").unwrap();
        let foo_parsed: DateTime<Utc> = serde_json::from_value(value["foo"].clone()).unwrap();
        assert_eq!(foo_parsed, expected);
    }

    // But a totally bogus timestamp will fail.
    {
        let mut value = json!({
            "bar": "foobar",
        });
        assert!(normalize_dt_field(value.as_object_mut().unwrap(), "bar").is_err());
        assert_eq!(value["bar"].as_str(), Some("foobar"));
    }
}

#[test]
fn test_normalize_str_field() {
    let test_cases = vec![
        (json!("foo"), "foo"),
        (json!(10), "10"),
        (json!({ "foo": "bar" }), r#"{"foo":"bar"}"#),
    ];

    for (input, expected) in test_cases {
        let mut value = json!({
            "foo": input,
        });
        normalize_str_field(value.as_object_mut().unwrap(), "foo").unwrap();
        let foo_parsed: String = serde_json::from_value(value["foo"].clone()).unwrap();
        assert_eq!(foo_parsed, expected);
    }
}

#[test]
fn test_normalize_list_field() {
    let test_cases = vec![
        (json!(["foo"]), json!(["foo"])),
        (json!("foo"), json!(["foo"])),
        (json!({ "foo": "bar" }), json!([{"foo": "bar"}])),
        (json!(null), json!(null)),
    ];

    for (input, expected) in test_cases {
        let mut value = json!({
            "foo": input,
        });
        normalize_list_field(value.as_object_mut().unwrap(), "foo").unwrap();
        assert_eq!(value["foo"], expected);
    }
}
