use std::ops::Deref;

use deadpool_postgres::PoolError;
use util::{
    anyhow::{Context, Result},
    url::Url,
};

#[derive(Debug)]
struct DangerousAcceptAnyCertificate;

impl rustls::client::danger::ServerCertVerifier for DangerousAcceptAnyCertificate {
    fn verify_server_cert(
        &self,
        _end_entity: &rustls::pki_types::CertificateDer<'_>,
        _intermediates: &[rustls::pki_types::CertificateDer<'_>],
        _server_name: &rustls::pki_types::ServerName<'_>,
        _ocsp_response: &[u8],
        _now: rustls::pki_types::UnixTime,
    ) -> Result<rustls::client::danger::ServerCertVerified, rustls::Error> {
        Ok(rustls::client::danger::ServerCertVerified::assertion())
    }

    fn verify_tls12_signature(
        &self,
        _message: &[u8],
        _cert: &rustls::pki_types::CertificateDer<'_>,
        _dss: &rustls::DigitallySignedStruct,
    ) -> Result<rustls::client::danger::HandshakeSignatureValid, rustls::Error> {
        Ok(rustls::client::danger::HandshakeSignatureValid::assertion())
    }

    fn verify_tls13_signature(
        &self,
        _message: &[u8],
        _cert: &rustls::pki_types::CertificateDer<'_>,
        _dss: &rustls::DigitallySignedStruct,
    ) -> Result<rustls::client::danger::HandshakeSignatureValid, rustls::Error> {
        Ok(rustls::client::danger::HandshakeSignatureValid::assertion())
    }

    fn supported_verify_schemes(&self) -> Vec<rustls::SignatureScheme> {
        rustls::crypto::ring::default_provider()
            .signature_verification_algorithms
            .supported_schemes()
    }
}

#[derive(Debug, Clone)]
pub struct PostgresPool {
    pool: deadpool_postgres::Pool,
}

pub struct PostgresPoolOptions {
    pub wait_timeout: std::time::Duration,
}

impl Default for PostgresPoolOptions {
    fn default() -> Self {
        Self {
            wait_timeout: std::time::Duration::from_secs(10),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct PostgresPoolGetClientOptions {
    pub log_error: bool,
}

impl PostgresPool {
    pub fn new(url: &Url, max_size: usize) -> Result<Self> {
        PostgresPool::new_with_opts(url, max_size, PostgresPoolOptions::default())
    }

    pub fn status(&self) -> deadpool_postgres::Status {
        self.pool.status()
    }

    pub fn new_with_opts(url: &Url, max_size: usize, options: PostgresPoolOptions) -> Result<Self> {
        let mut config = deadpool_postgres::Config::new();
        config.url = Some(url.to_string());
        config.manager = Some(deadpool_postgres::ManagerConfig {
            recycling_method: deadpool_postgres::RecyclingMethod::Fast,
        });
        // This effectively caps the number of concurrent locks to be max_locks (+ one
        // maintenance connection).
        config.pool = Some(deadpool_postgres::PoolConfig {
            max_size,
            timeouts: deadpool_postgres::Timeouts {
                wait: Some(options.wait_timeout),
                ..Default::default()
            },
            ..Default::default()
        });

        // Create a TLS config that accepts any certificate. This is not the optimal behavior and
        // we should eventually make it stricter.
        let tls_config = rustls::ClientConfig::builder()
            .dangerous()
            .with_custom_certificate_verifier(std::sync::Arc::new(DangerousAcceptAnyCertificate))
            .with_no_client_auth();
        let tls = tokio_postgres_rustls::MakeRustlsConnect::new(tls_config);

        let pool = config
            .create_pool(Some(deadpool_postgres::Runtime::Tokio1), tls)
            .context("failed to create postgres pool")?;

        Ok(Self { pool })
    }

    pub async fn get_client(&self) -> Result<deadpool_postgres::Object, PoolError> {
        self.get_client_with_opts(PostgresPoolGetClientOptions { log_error: true })
            .await
    }

    pub async fn get_client_with_opts(
        &self,
        opts: PostgresPoolGetClientOptions,
    ) -> Result<deadpool_postgres::Object, PoolError> {
        use crate::postgres_global_store::POOL_METRICS;
        use std::time::Instant;

        let start = Instant::now();
        let client = self.pool.get().await;
        let wait_time_ms = start.elapsed().as_millis() as u64;

        // Record wait time
        POOL_METRICS
            .connection_wait_time_ms
            .record(wait_time_ms, &[]);

        match client {
            Ok(client) => Ok(client),
            Err(e) => {
                // Check if it's a pool exhaustion error
                if matches!(e, PoolError::Timeout(_)) {
                    POOL_METRICS.pool_exhaustion_count.add(1, &[]);
                }

                if opts.log_error {
                    log::error!("Failed to get postgres client: {:?}", e);
                }
                Err(e)
            }
        }
    }
}

impl Deref for PostgresPool {
    type Target = deadpool_postgres::Pool;

    fn deref(&self) -> &Self::Target {
        &self.pool
    }
}
