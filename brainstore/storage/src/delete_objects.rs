use util::{anyhow::Result, system_types::FullObjectId, uuid::Uuid};

use crate::global_store::GlobalStore;

pub struct DeleteObjectInput<'a> {
    pub object_ids: &'a [FullObjectId<'a>],
    pub global_store: &'a dyn GlobalStore,
}

/// Delete a set of objects from the metadata store. This is essentially a "soft" delete, in that
/// the objects' data are not actually deleted, but instead all metadata for the objects and any
/// segment associations are deleted, so future readers/writers will not have access to them.
///
/// Separately, you may invoke the vacuum function to clean up any storage data that is no longer
/// associated with any live metadata.
///
/// NOTE: if there are reads/writes to the objects concurrent with their deletion, the reads/writes
/// might fail when trying to access metadata. But they may also suceeed if interleaved in just the
/// right way. In general we don't block writing to a deleted object, since being deleted is
/// logically equivalent to being empty. So in this case you may just have to run delete again
/// after the writes are done.
pub async fn delete_objects(input: DeleteObjectInput<'_>) -> Result<()> {
    // First we list out all the live segments for the objects, and mark them all as deleted. Then
    // we purge any object-specific metadata.
    let segment_ids = input
        .global_store
        .list_segment_ids(input.object_ids, None)
        .await?;
    let update_args = input
        .object_ids
        .iter()
        .zip(segment_ids.iter())
        .map(|(o, segs)| -> (FullObjectId<'_>, &[Uuid], &[Uuid]) { (*o, &[], segs) })
        .collect::<Vec<_>>();
    input.global_store.update_segment_ids(&update_args).await?;
    input
        .global_store
        .purge_object_metadatas(input.object_ids)
        .await?;
    Ok(())
}

// NOTE FOR THE FUTURE: when implementing vacuuming, be very careful not to delete non-visible
// segments which are currently being filled, e.g. during segment merging or creating a new segment
// during wal processing.
//
// One easy way to accomplish this is to only delete segments which have explicitly been marked
// non-live, and then purge them only after they have been fully vacuumed. This risks leaving
// around zombie directories which do not exist in any metadata, but it is difficult in principle
// to distinguish between such directories and directories which are in the process of being
// created.
