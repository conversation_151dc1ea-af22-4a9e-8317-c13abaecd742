// This module contains sanitization functions that are used to "massage" legacy data coming into
// the system, generally when backfilling old data from postgres.

use util::anyhow::{anyhow, Context, Result};

pub fn normalize_dt_field(
    value: &mut serde_json::Map<String, serde_json::Value>,
    key: &str,
) -> Result<()> {
    let extracted_value = match value.get_mut(key) {
        Some(x) => x,
        None => return Ok(()),
    };
    let extracted_str = extracted_value
        .as_str()
        .ok_or_else(|| anyhow!("Expected string for {}", key))?;
    let extracted_dt = dateparser::parse(extracted_str)
        .with_context(|| format!("Failed to parse {} timestamp: {}", key, extracted_str))?;
    *extracted_value = serde_json::to_value(extracted_dt)
        .with_context(|| format!("Failed to serialize {} timestamp: {}", key, extracted_str))?;
    Ok(())
}

pub fn normalize_str_field(
    value: &mut serde_json::Map<String, serde_json::Value>,
    key: &str,
) -> Result<()> {
    let extracted_value = match value.get_mut(key) {
        Some(x) => x,
        None => return Ok(()),
    };
    if !extracted_value.is_string() {
        *extracted_value = serde_json::Value::String(
            serde_json::to_string(extracted_value)
                .with_context(|| format!("Failed to serialize {} string", key))?,
        );
    }
    Ok(())
}

pub fn normalize_list_field(
    value: &mut serde_json::Map<String, serde_json::Value>,
    key: &str,
) -> Result<()> {
    let extracted_value = match value.get_mut(key) {
        Some(x) => x,
        None => return Ok(()),
    };
    if !extracted_value.is_null() && !extracted_value.is_array() {
        *extracted_value = serde_json::Value::Array(vec![std::mem::take(extracted_value)]);
    }
    Ok(())
}
