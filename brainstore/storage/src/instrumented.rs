use std::{collections::BTreeMap, sync::Arc};

use crate::timer::TimerManager;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct CacheEntryStats {
    pub num_disk_hits: i64,
    pub num_mem_hits: i64,
    pub num_misses: i64,
    pub num_cold_reads: i64,
    pub last_hit: i64,
}

pub trait Instrumented {
    fn enable_timing(&self);
    fn reset_timing(&self);
    fn timers(&self) -> Vec<Arc<TimerManager>>;
    fn cache_metrics(&self) -> BTreeMap<String, CacheEntryStats> {
        BTreeMap::new()
    }
    fn reset_caches(&self) {}
}

pub trait AsDynInstrumented: Instrumented {
    fn as_dyn_instrumented(&self) -> &dyn Instrumented;
}

impl<T: Instrumented> AsDynInstrumented for T {
    fn as_dyn_instrumented(&self) -> &dyn Instrumented {
        self
    }
}

pub fn format_timers(obj: &dyn Instrumented) {
    let timers = obj.timers();
    for timer in timers.iter() {
        log::info!("\n");
        log::info!("{} durations:", timer.name());
        log::info!("-----------------------------");
        timer.log_all_durations();
    }
}

pub fn format_cache_metrics(obj: &dyn Instrumented) {
    let cache_metrics = obj.cache_metrics();

    let max_label_len = std::cmp::max(
        cache_metrics.keys().map(|s| s.len()).max().unwrap_or(30),
        30,
    );

    let aggregate_stats = aggregate_stats(cache_metrics.values().map(|s| s));
    log::info!(
        "{:<width$} {:>15} {:>10} {:>20}",
        "Path",
        "Disk Hits",
        "Mem Hits",
        "Misses",
        width = max_label_len,
    );
    log::info!("{:-<1$}", "", max_label_len + 15 + 10 + 20 + 4);
    log::info!(
        "{:<width$} {:>15} {:>10} {:>20}\n",
        "Aggregate",
        &aggregate_stats.num_disk_hits.to_string(),
        &aggregate_stats.num_mem_hits.to_string(),
        &aggregate_stats.num_misses.to_string(),
        width = max_label_len,
    );

    if log::log_enabled!(log::Level::Debug) {
        log::debug!("Cache metrics:");
        log::debug!("-----------------------------");
        for (label, stats) in cache_metrics.iter() {
            log::debug!(
                "{:<width$} {:>15} {:>10} {:>20}",
                label,
                &stats.num_disk_hits.to_string(),
                &stats.num_mem_hits.to_string(),
                &stats.num_misses.to_string(),
                width = max_label_len,
            );
        }
    }
}

pub fn aggregate_stats<'a, Iter: Iterator<Item = &'a CacheEntryStats>>(
    stats: Iter,
) -> CacheEntryStats {
    let mut ret = CacheEntryStats {
        num_disk_hits: 0,
        num_mem_hits: 0,
        num_misses: 0,
        num_cold_reads: 0,
        last_hit: 0,
    };
    for s in stats {
        ret.num_disk_hits += s.num_disk_hits;
        ret.num_mem_hits += s.num_mem_hits;
        ret.num_misses += s.num_misses;
        ret.num_cold_reads += s.num_cold_reads;
        ret.last_hit = std::cmp::max(ret.last_hit, s.last_hit);
    }

    ret
}
