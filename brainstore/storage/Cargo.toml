[package]
name = "storage"
version = "0.1.0"
edition = "2021"

[dependencies]
async-stream = { workspace = true }
backoff = { workspace = true }
base64 = { workspace = true }
bincode = { workspace = true }
byte-unit = { workspace = true }
bytes = { workspace = true }
clap = { workspace = true }
deadpool-postgres = { workspace = true }
downcast-rs = { workspace = true }
env_logger = { workspace = true }
flate2 = { workspace = true }
hashbrown = { workspace = true }
lazy_static = { workspace = true }
log = { workspace = true }
once_cell = { workspace = true }
otel_common = { path = "../otel_common" }
rand = { workspace = true }
redis = { workspace = true }
rslock = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
sha1 = { workspace = true }
sha2 = { workspace = true }
stable_deref_trait = { workspace = true }
tantivy = { path = "../tantivy" }
tempfile = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tokio-postgres = { workspace = true }
tokio-stream = { workspace = true }
tracing = { path = "../tracing" }
util = { path = "../util" }
tokio-postgres-rustls = { workspace = true }
rustls = { workspace = true }
webpki-roots = { workspace = true }
gxhash = { workspace = true }
dateparser = { workspace = true }
rust_decimal = { workspace = true }
object_store = { workspace = true }
memmap2 = { workspace = true }
arc-swap = { workspace = true }
async-channel = { workspace = true }
aes-gcm = { workspace = true }
async-trait = { workspace = true }
futures = { workspace = true }
async_util = { path = "../async_util" }
reqwest = { workspace = true }

[dev-dependencies]
testcontainers = { workspace = true }
testcontainers-modules = { workspace = true }
criterion = { workspace = true }

[[bench]]
name = "wal_entry_bench"
harness = false
