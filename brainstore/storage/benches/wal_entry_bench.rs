/*
Benchmark results from 2025-05-13 on Apple M3 Pro chip.

wal_entry_serialize_to_string
                        time:   [1.8026 ms 1.8262 ms 1.8580 ms]
                        change: [-0.5765% +1.2443% +3.0317%] (p = 0.22 > 0.05)
                        No change in performance detected.
Found 14 outliers among 100 measurements (14.00%)
  8 (8.00%) high mild
  6 (6.00%) high severe

wal_entry_deserialize_from_string
                        time:   [643.19 µs 649.13 µs 655.60 µs]
                        change: [-8.0251% -4.5657% -0.6169%] (p = 0.01 < 0.05)
                        Change within noise threshold.
Found 5 outliers among 100 measurements (5.00%)
  3 (3.00%) high mild
  2 (2.00%) high severe

wal_entry_system_fields_serialize
                        time:   [321.27 ns 325.75 ns 333.83 ns]
                        change: [-51.197% -49.685% -48.246%] (p = 0.00 < 0.05)
                        Performance has improved.
Found 4 outliers among 100 measurements (4.00%)
  3 (3.00%) high mild
  1 (1.00%) high severe

wal_entry_system_fields_deserialize
                        time:   [402.73 µs 402.90 µs 403.11 µs]
                        change: [+69298% +71356% +73029%] (p = 0.00 < 0.05)
                        Performance has regressed.
Found 13 outliers among 100 measurements (13.00%)
  7 (7.00%) high mild
  6 (6.00%) high severe

*/

use criterion::{criterion_group, criterion_main, Criterion};
use serde_json::{json, Map, Value};
use util::{
    chrono::Utc,
    system_types::{ObjectIdOwned, ObjectType},
    xact::{PaginationKey, TransactionId},
};

use storage::wal_entry::{WalEntry, WalEntrySystemFields};

// Helper function to create a deeply nested JSON structure with large string payloads
fn create_nested_data(depth: usize, width: usize, string_size: usize) -> Map<String, Value> {
    let mut map = Map::new();

    // Create a large string payload
    let large_string = "a".repeat(string_size);

    // Add some top-level fields
    for i in 0..width {
        map.insert(format!("field_{}", i), json!(large_string.clone()));
    }

    // Recursively add nested objects if we haven't reached the maximum depth
    if depth > 0 {
        let nested_map = create_nested_data(depth - 1, width, string_size);
        map.insert("nested".to_string(), Value::Object(nested_map));
    }

    map
}

// Create a sample WalEntry with nested data
fn create_sample_wal_entry() -> WalEntry {
    // Create a nested data structure with 10 levels of nesting, 5 fields per
    // level, and 100KB strings. This equates to about 5MB of data.
    let nested_data = create_nested_data(10, 5, 100 * 1000);

    WalEntry {
        id: "test_id_123".to_string(),
        created: Utc::now(),
        _pagination_key: PaginationKey(123),
        _xact_id: TransactionId(456),
        _object_type: ObjectType::Project,
        _object_id: ObjectIdOwned::new("project_xyz".to_string()).unwrap(),
        root_span_id: "root_span_id_1".to_string(),
        span_id: "span_id_2".to_string(),
        data: nested_data,
        ..Default::default()
    }
}

// Benchmark serialization of WalEntry to string
fn bench_wal_entry_serialize(c: &mut Criterion) {
    let wal_entry = create_sample_wal_entry();

    c.bench_function("wal_entry_serialize_to_string", |b| {
        b.iter_batched(
            || wal_entry.clone(),
            |wal_entry| {
                let value = wal_entry.to_value();
                let _ = serde_json::to_string(&value).unwrap();
            },
            criterion::BatchSize::LargeInput,
        );
    });
}

// Benchmark deserialization of WalEntry from a string
fn bench_wal_entry_deserialize(c: &mut Criterion) {
    let wal_entry = create_sample_wal_entry();
    let value = wal_entry.to_value();
    let json_string = serde_json::to_string(&value).unwrap();

    c.bench_function("wal_entry_deserialize_from_string", |b| {
        b.iter(|| {
            let value: Value = serde_json::from_str(&json_string).unwrap();
            let _ = WalEntry::new(value).unwrap();
        })
    });
}

// Benchmark WalEntrySystemFields serialization
fn bench_wal_entry_system_fields_serialize(c: &mut Criterion) {
    let wal_entry = create_sample_wal_entry();
    let value = wal_entry.to_value();
    let wal_entry_system_fields: WalEntrySystemFields = serde_json::from_value(value).unwrap();

    c.bench_function("wal_entry_system_fields_serialize", |b| {
        b.iter(|| {
            let _ = serde_json::to_string(&wal_entry_system_fields).unwrap();
        })
    });
}

// Benchmark WalEntrySystemFields deserialization
fn bench_wal_entry_system_fields_deserialize(c: &mut Criterion) {
    let wal_entry = create_sample_wal_entry();
    let value = wal_entry.to_value();
    let serialized = serde_json::to_string(&value).unwrap();

    c.bench_function("wal_entry_system_fields_deserialize", |b| {
        b.iter(|| {
            let _: WalEntrySystemFields = serde_json::from_str(&serialized).unwrap();
        })
    });
}

pub fn criterion_benchmark(c: &mut Criterion) {
    bench_wal_entry_serialize(c);
    bench_wal_entry_deserialize(c);
    bench_wal_entry_system_fields_serialize(c);
    bench_wal_entry_system_fields_deserialize(c);
}

criterion_group!(benches, criterion_benchmark);
criterion_main!(benches);
