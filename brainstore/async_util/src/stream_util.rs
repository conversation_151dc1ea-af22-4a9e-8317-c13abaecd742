use futures::stream::BoxStream;
use futures::{Stream, StreamExt};
use std::cmp::Ordering;
use std::collections::BinaryHeap;

/// Merges multiple streams into a single ordered stream based on a comparison function.
/// The comparison function should return Ordering::Less if the first item should come before the second.
pub fn merge_ordered_streams<T, SIter, S, F>(streams: SIter, cmp: F) -> BoxStream<'static, T>
where
    T: Send + 'static,
    S: Stream<Item = T> + Send + 'static + Unpin,
    SIter: IntoIterator<Item = S>,
    F: Fn(&T, &T) -> Ordering + Send + Sync + 'static,
{
    let mut streams = streams.into_iter();
    match streams.size_hint() {
        (0, Some(0)) => {
            return futures::stream::empty().boxed();
        }
        (1, Some(1)) => {
            return streams.next().unwrap().boxed();
        }
        _ => {}
    };
    let mut streams = streams
        .into_iter()
        .map(|s| s.peekable())
        .collect::<Vec<_>>();

    async_stream::stream! {
        let mut heap = BinaryHeap::with_capacity(streams.len());
        // Initialize heap with first item from each stream
        for (idx, stream) in streams.iter_mut().enumerate() {
            if let Some(next) = stream.next().await {
                heap.push(OrderedStreamItem {
                    item: next,
                    stream_idx: idx,
                    cmp: &cmp,
                });
            }
        }

        while let Some(OrderedStreamItem { item, stream_idx, .. }) = heap.pop() {
            yield item;

            // Get next item from the stream that produced the yielded item
            if let Some(next) = streams[stream_idx].next().await {
                heap.push(OrderedStreamItem {
                    item: next,
                    stream_idx,
                    cmp: &cmp,
                });
            }
        }
    }
    .boxed()
}

struct OrderedStreamItem<'a, T, F> {
    item: T,
    stream_idx: usize,
    cmp: &'a F,
}

impl<'a, T, F> Eq for OrderedStreamItem<'a, T, F> where F: Fn(&T, &T) -> Ordering {}

impl<'a, T, F> PartialEq for OrderedStreamItem<'a, T, F>
where
    F: Fn(&T, &T) -> Ordering,
{
    fn eq(&self, other: &Self) -> bool {
        (self.cmp)(&self.item, &other.item) == Ordering::Equal
    }
}

impl<'a, T, F> PartialOrd for OrderedStreamItem<'a, T, F>
where
    F: Fn(&T, &T) -> Ordering,
{
    // It's reversed because we want a min-heap.
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some((self.cmp)(&other.item, &self.item))
    }
}

impl<'a, T, F> Ord for OrderedStreamItem<'a, T, F>
where
    F: Fn(&T, &T) -> Ordering,
{
    // It's reversed because we want a min-heap.
    fn cmp(&self, other: &Self) -> Ordering {
        (self.cmp)(&other.item, &self.item)
    }
}
