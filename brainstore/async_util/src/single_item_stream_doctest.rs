/// ```compile_fail
/// use async_stream::stream;
/// use futures::StreamExt;
///
/// use util::single_item_stream::SingleItemStream;
///
/// #[tokio::main]
/// async fn single_item_stream() {
///     let my_stream = stream! {
///         yield(10);
///     }.boxed();
///
///     let mut single_item_stream = SingleItemStream::new(my_stream);
///     let x0 = single_item_stream.next().await;
///     let x1 = single_item_stream.next().await;
///     assert!(x0.is_some());
///     assert!(x1.is_none());
/// }
/// ```
fn _test_single_item_stream_no_multi_borrow() {}
