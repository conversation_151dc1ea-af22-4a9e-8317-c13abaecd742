use crate::await_spawn_blocking;

struct Wrapper<'a> {
    x: &'a i32,
}

struct MutWrapper<'a> {
    x: &'a mut i32,
}

#[tokio::test]
async fn test_await_spawn_blocking() {
    // Immutable borrow.
    {
        let a = 20;
        {
            let a_ref = &a;
            let result = await_spawn_blocking!(|a: &i32| { *a + 10 }, a_ref)
                .unwrap()
                .unwrap();
            assert_eq!(result, 30);
        }
    }

    // Immutable borrow of type with internal reference.
    {
        let a = 20;
        {
            let wrapper = Wrapper { x: &a };
            {
                let wrapper_ref = &wrapper;
                let result =
                    await_spawn_blocking!(|wrapper: &Wrapper| { *wrapper.x + 10 }, wrapper_ref)
                        .unwrap()
                        .unwrap();
                assert_eq!(result, 30);
            }
        }
    }

    // Mutable borrow.
    {
        let mut a = 20;
        {
            let a_ref = &mut a;
            await_spawn_blocking!(|a: &mut i32| {
                *a += 10;
            }; a_ref)
            .unwrap()
            .unwrap();
            assert_eq!(a, 30);
        }
    }

    // Mutable borrow of type with internal reference.
    {
        let mut a = 20;
        {
            let mut wrapper = MutWrapper { x: &mut a };
            {
                let wrapper_ref = &mut wrapper;
                await_spawn_blocking!(|wrapper: &mut MutWrapper| {
                    *wrapper.x += 10;
                }; wrapper_ref)
                .unwrap()
                .unwrap();
                assert_eq!(a, 30);
            }
        }
    }

    // Immutable and mutable borrow.
    {
        let a = 20;
        let mut b = 10;
        {
            let a_ref = &a;
            let b_ref = &mut b;
            await_spawn_blocking!(|a: &i32, b: &mut i32| {
                *b += *a;
            }, a_ref; b_ref)
            .unwrap()
            .unwrap();
            assert_eq!(b, 30);
        }
    }
}
