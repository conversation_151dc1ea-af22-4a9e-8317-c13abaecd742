[package]
name = "agent"
version = "0.1.0"
edition = "2024"

[dependencies]
aws-config = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true }
colored = { workspace = true }
log = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }

otel_common = { path = "../otel_common" }
util = { path = "../util" }
tracing = { path = "../tracing" }

tracing-log = { workspace = true }
tracing-opentelemetry = { workspace = true }
opentelemetry-stdout = { workspace = true }
opentelemetry_sdk = { workspace = true }
opentelemetry-semantic-conventions = { workspace = true }
opentelemetry-appender-tracing = { workspace = true }
tracing-subscriber = { workspace = true }
opentelemetry-otlp = { workspace = true }
json-subscriber = { workspace = true }
opentelemetry-resource-detectors = { workspace = true }
hostname = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
reqwest = { workspace = true }

# Memory profiling
base64 = { workspace = true, optional = true }
# Symbolized profiles are more expensive to compute and less accurate (no line numbers).
jemalloc_pprof = { workspace = true, optional = true }

[target.'cfg(target_os = "linux")'.dependencies]
procfs = { workspace = true, optional = true }

[features]
enable_memprof = ["jemalloc_pprof", "procfs", "base64"]
