use std::fs;
use std::path::Path;
use std::sync::OnceLock;

use opentelemetry_sdk::resource::{Resource, ResourceDetector};
use opentelemetry_semantic_conventions::resource as semconv;
use otel_common::opentelemetry::KeyValue;

#[derive(Debug, Clone)]
struct DockerInfo {
    container_id: String,
    container_name: Option<String>,
    image_name: Option<String>,
    image_tag: Option<String>,
}

static DOCKER_INFO: OnceLock<Option<DockerInfo>> = OnceLock::new();

pub struct DockerResourceDetector;

impl ResourceDetector for DockerResourceDetector {
    fn detect(&self) -> Resource {
        let docker_info = DOCKER_INFO.get_or_init(|| detect_docker_info());

        match docker_info {
            Some(info) => {
                let mut attributes = vec![KeyValue::new(
                    semconv::CONTAINER_ID,
                    info.container_id.clone(),
                )];

                if let Some(name) = &info.container_name {
                    attributes.push(KeyValue::new(semconv::CONTAINER_NAME, name.clone()));
                }

                if let Some(image_name) = &info.image_name {
                    attributes.push(KeyValue::new(
                        semconv::CONTAINER_IMAGE_NAME,
                        image_name.clone(),
                    ));
                }

                if let Some(image_tag) = &info.image_tag {
                    attributes.push(KeyValue::new(
                        semconv::CONTAINER_IMAGE_TAGS,
                        image_tag.clone(),
                    ));
                }

                Resource::builder_empty()
                    .with_attributes(attributes)
                    .build()
            }
            None => Resource::builder_empty().build(),
        }
    }
}

fn detect_docker_info() -> Option<DockerInfo> {
    // Try to detect Docker container by checking cgroup information
    let container_id = detect_container_id()?;

    // Try to get container name and image info from environment variables
    let container_name = std::env::var("HOSTNAME")
        .ok()
        .or_else(|| std::env::var("CONTAINER_NAME").ok());

    let image_name = std::env::var("CONTAINER_IMAGE")
        .ok()
        .or_else(|| std::env::var("IMAGE_NAME").ok());

    let image_tag = std::env::var("CONTAINER_IMAGE_TAG")
        .ok()
        .or_else(|| std::env::var("IMAGE_TAG").ok())
        .or_else(|| {
            // Try to extract tag from full image name
            image_name
                .as_ref()
                .and_then(|name| name.split(':').nth(1).map(|tag| tag.to_string()))
        });

    Some(DockerInfo {
        container_id,
        container_name,
        image_name,
        image_tag,
    })
}

fn detect_container_id() -> Option<String> {
    // Try different methods to detect container ID

    // Method 1: Check /proc/self/cgroup (works for most container runtimes)
    if let Some(id) = detect_from_cgroup() {
        return Some(id);
    }

    // Method 2: Check /proc/self/mountinfo (alternative method)
    if let Some(id) = detect_from_mountinfo() {
        return Some(id);
    }

    // Method 3: Check /.dockerenv file (Docker-specific)
    if Path::new("/.dockerenv").exists() {
        // If we can't get the ID from cgroup but .dockerenv exists,
        // try to read container ID from hostname as fallback
        if let Ok(hostname) = std::env::var("HOSTNAME") {
            if hostname.len() >= 12 {
                return Some(hostname);
            }
        }
    }

    None
}

fn detect_from_cgroup() -> Option<String> {
    let cgroup_content = fs::read_to_string("/proc/self/cgroup").ok()?;

    for line in cgroup_content.lines() {
        // Look for container ID in cgroup paths
        if let Some(container_id) = extract_container_id_from_path(line) {
            return Some(container_id);
        }
    }

    None
}

fn detect_from_mountinfo() -> Option<String> {
    let mountinfo_content = fs::read_to_string("/proc/self/mountinfo").ok()?;

    for line in mountinfo_content.lines() {
        if line.contains("/docker/containers/") {
            // Extract container ID from Docker mount path
            if let Some(start) = line.find("/docker/containers/") {
                let path_part = &line[start + "/docker/containers/".len()..];
                if let Some(end) = path_part.find('/') {
                    let container_id = &path_part[..end];
                    if container_id.len() >= 12 {
                        return Some(container_id.to_string());
                    }
                }
            }
        }
    }

    None
}

fn extract_container_id_from_path(cgroup_line: &str) -> Option<String> {
    // Docker format: /docker/<container_id>
    if let Some(start) = cgroup_line.find("/docker/") {
        let path_part = &cgroup_line[start + "/docker/".len()..];
        if let Some(end) = path_part.find('/') {
            let container_id = &path_part[..end];
            if container_id.len() >= 12 {
                return Some(container_id.to_string());
            }
        } else if path_part.len() >= 12 {
            return Some(path_part.to_string());
        }
    }

    // Containerd format: /system.slice/containerd.service/kubepods-<...>-<container_id>.scope
    if cgroup_line.contains("containerd.service") && cgroup_line.contains(".scope") {
        if let Some(scope_start) = cgroup_line.rfind('/') {
            let scope_part = &cgroup_line[scope_start + 1..];
            if let Some(dash_pos) = scope_part.rfind('-') {
                let potential_id = &scope_part[dash_pos + 1..];
                if let Some(dot_pos) = potential_id.find('.') {
                    let container_id = &potential_id[..dot_pos];
                    if container_id.len() >= 12 {
                        return Some(container_id.to_string());
                    }
                }
            }
        }
    }

    // Podman format: /machine.slice/libpod-<container_id>.scope
    if cgroup_line.contains("libpod-") && cgroup_line.contains(".scope") {
        if let Some(start) = cgroup_line.find("libpod-") {
            let id_part = &cgroup_line[start + "libpod-".len()..];
            if let Some(end) = id_part.find(".scope") {
                let container_id = &id_part[..end];
                if container_id.len() >= 12 {
                    return Some(container_id.to_string());
                }
            }
        }
    }

    None
}
