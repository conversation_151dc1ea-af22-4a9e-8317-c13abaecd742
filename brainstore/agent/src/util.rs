use crate::tracing_opentelemetry::OpenTelemetrySpanExt;
use otel_common::opentelemetry::trace::{TraceContextExt, TraceId};

pub fn derive_trace_id(span: &tracing::Span) -> Option<TraceId> {
    // Each of the first two creates some sort of temporary value/guard, so we need to split
    // this long chain of calls into multiple lines.
    let ctx = span.context();
    let ctx_span = ctx.span();
    let ctx_span_context = ctx_span.span_context();
    if ctx_span_context.is_valid() {
        Some(ctx_span_context.trace_id())
    } else {
        None
    }
}
