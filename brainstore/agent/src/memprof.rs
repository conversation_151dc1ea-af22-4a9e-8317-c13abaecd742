use serde::{Deserialize, Serialize};

pub struct MemprofFlusher {
    pub should_flush: tokio::sync::mpsc::Receiver<()>,
    pub has_flushed: tokio::sync::mpsc::Sender<()>,
}
pub struct MemprofFlushHandle {
    pub should_flush: tokio::sync::mpsc::Sender<()>,
    pub has_flushed: tokio::sync::mpsc::Receiver<()>,
}

impl MemprofFlushHandle {
    pub async fn flush(&mut self) {
        if let Err(e) = self.should_flush.send(()).await {
            log::warn!("Error sending memory profiler flush signal: {}", e);
            return;
        }
        let _ = self.has_flushed.recv().await;
    }
}

pub fn make_flusher() -> (Memp<PERSON><PERSON><PERSON><PERSON><PERSON>, MemprofFlushHandle) {
    let (should_flush_sender, should_flush_receiver) = tokio::sync::mpsc::channel(1);
    let (has_flushed_sender, has_flushed_receiver) = tokio::sync::mpsc::channel(1);
    (
        MemprofFlusher {
            should_flush: should_flush_receiver,
            has_flushed: has_flushed_sender,
        },
        MemprofFlushHandle {
            should_flush: should_flush_sender,
            has_flushed: has_flushed_receiver,
        },
    )
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct MemprofInput {
    pub memprof_dir: Option<std::path::PathBuf>,
    pub memprof_interval_seconds: u64,
    pub api_url: Option<String>,
    pub api_key: Option<String>,
    pub resource_labels: Vec<MemprofHttpLabel>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct MemprofHttpLabel {
    pub name: String,
    pub value: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MemprofHttpLabelSet {
    pub labels: Vec<MemprofHttpLabel>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MemprofHttpRawSample {
    pub raw_profile: String, // base64 encoded
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MemprofProfileSeries {
    pub labels: MemprofHttpLabelSet,
    pub samples: Vec<MemprofHttpRawSample>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MemprofWriteRawRequest {
    pub series: Vec<MemprofProfileSeries>,
}

#[cfg(feature = "enable_memprof")]
pub use crate::memprof_internals::{dump_heap_to_file, profile_heap, run_memprof};
