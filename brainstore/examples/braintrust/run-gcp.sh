#!/bin/bash

# Read variables from .env.local if it exists
if [ -f .env.local ]; then
    echo "Reading variables from .env.local"
    while IFS='=' read -r key value
    do
        # Remove leading/trailing whitespace and quotes from the value
        value=$(echo "$value" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' -e 's/^"//' -e 's/"$//')
        # Export the variable
        export "$key=$value"
        echo "Exported $key"
    done < .env.local
else
    echo "Note: .env.local file not found, skipping"
fi

ulimit -n 100000

# cargo clean -p tantivy --release
cargo build --release

export GOOGLE_APPLICATION_CREDENTIALS=key.json

../../target/release/brainstore web \
    --config gcp.yaml \
    --schema schema.json \
    --object-store-cache-memory-limit 1gb \
    --object-store-cache-file-size 50gb \
    --cache-dir /mnt/tmp/brainstore \
    -v
