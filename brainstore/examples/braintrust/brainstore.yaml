version: 1
metadata_uri: postgres://postgres:postgres@localhost:5532/postgres
index_uri: s3://code-bundles/brainstore/index
wal_uri: postgres://postgres:postgres@localhost:5532/postgres
# Keep this in sync with the BRAINSTORE_REALTIME_WAL env vars in
# api-ts/.env.development.
realtime_wal_uri: s3://code-bundles/brainstore/wal
# Note: for connecting to redis via SSL, use rediss://. To skip client-side
# certificate validation, include the fragment '#insecure' at the end of the
# URL.
locks_uri: s3://code-bundles/brainstore/locks
xact_manager_uri: redis://localhost:6479
