[{"error": null, "query": "/*!result_meta -- Basic rate sampling (25% of 100 = ~25 rows)\nlength >= 20 and length <= 35\n*/\nfrom: experiment('singleton') | select: id | sample: 25% seed 1", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Basic count sampling (exactly 10 rows)\nlength == 10\n*/\nfrom: experiment('singleton') | select: id | sample: 10 seed 2", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Rate sampling (30% of 100 = ~30 rows)\nlength >= 20 and length <= 40\n*/\nfrom: experiment('singleton') | select: id | sample: 30% seed 3", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Simple rate sampling (60% of 100 = ~60 rows)\nlength >= 50 and length <= 70\n*/\nfrom: experiment('singleton') | select: id | sample: 60% seed 4", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Higher rate sampling (80% of 100 = ~80 rows)\nlength >= 70 and length <= 90\n*/\nfrom: experiment('singleton') | select: id | sample: 80% seed 5", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Count sampling (exactly 20 rows)\nlength == 20\n*/\nfrom: experiment('singleton') | select: id | sample: 20 seed 6", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Combined rate sampling (35% = ~35 rows)\nlength >= 25 and length <= 45\n*/\nfrom: experiment('singleton') | select: id | sample: 35% seed 7", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Rate sampling with selection (45% = ~45 rows)\nlength >= 35 and length <= 55\n*/\nfrom: experiment('singleton') | select: id, scores.foo | sample: 45% seed 8", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)\nlength <= 20\n*/\nfrom: experiment('singleton') | filter: scores.foo > -0.5 | select: id | sample: 50% seed 9 | limit: 20", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Basic rate sampling (25% of 37 = ~9 rows)\nlength >= 5 and length <= 14\n*/\nfrom: experiment('singleton') summary | select: id | sample: 25% seed 1", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Basic count sampling (exactly 10 rows)\nlength == 10\n*/\nfrom: experiment('singleton') summary | select: id | sample: 10 seed 2", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Rate sampling (30% of 37 = ~11 rows)\nlength >= 7 and length <= 16\n*/\nfrom: experiment('singleton') summary | select: id | sample: 30% seed 3", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Simple rate sampling (60% of 37 = ~22 rows)\nlength >= 15 and length <= 29\n*/\nfrom: experiment('singleton') summary | select: id | sample: 60% seed 4", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Higher rate sampling (80% of 37 = ~30 rows)\nlength >= 25 and length <= 35\n*/\nfrom: experiment('singleton') summary | select: id | sample: 80% seed 5", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Count sampling (exactly 20 rows)\nlength == 20\n*/\nfrom: experiment('singleton') summary | select: id | sample: 20 seed 6", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Combined rate sampling (35% of 37 = ~13 rows)\nlength >= 9 and length <= 18\n*/\nfrom: experiment('singleton') summary | select: id | sample: 35% seed 7", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Rate sampling with selection (45% of 37 = ~17 rows)\nlength >= 12 and length <= 22\n*/\nfrom: experiment('singleton') summary | select: id, scores.foo | sample: 45% seed 8", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)\nlength <= 20\n*/\nfrom: experiment('singleton') summary | filter: scores.foo > -0.5 | select: id | sample: 50% seed 9 | limit: 20", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)\nlength == 0\n*/\nfrom: experiment('singleton') spans | select: id | sample: 0%", "result_rows": [], "skip": false, "skip_result_content": true}, {"error": null, "query": "/*!result_meta -- Complex query with filter and sampling (filter reduces data, then sample 50%, limit to 20)\nlength == 0\n*/\nfrom: experiment('singleton') summary | select: id | sample: 0%", "result_rows": [], "skip": false, "skip_result_content": true}]