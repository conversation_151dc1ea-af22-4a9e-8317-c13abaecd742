version: 1
schema:
  name: test
  fields:
    - name: text
      tantivy:
        - name: text_str
          type: !str
            stored: true
            fast: true
            tokenize: true
    - name: number
      tantivy:
        - name: number_num
          type: !f64
            stored: true
            fast: true
    - name: object
      tantivy:
        - name: object_o
          type: !json
            stored: true
            tokenize: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
