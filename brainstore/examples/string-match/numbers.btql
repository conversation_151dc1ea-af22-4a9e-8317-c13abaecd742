select: * | filter: number match 1;
select: * | filter: number match 1.0;
select: * | filter: number match 1.5;
select: * | filter: number match "1";
select: * | filter: number match "1.0";
select: * | filter: number match "1.5";

select: * | filter: number = 1;
select: * | filter: number = 1.0;
select: * | filter: number = 1.5;
select: * | filter: number = "1";
select: * | filter: number = "1.0";
select: * | filter: number = "1.5";

select: * | filter: number != 1;
select: * | filter: number != 1.0;
select: * | filter: number != 1.5;
select: * | filter: number != "1";
select: * | filter: number != "1.0";
select: * | filter: number != "1.5";

select: * | filter: number match "asdf";


select: * | filter: object.number match 1;
select: * | filter: object.number match 1.0;
select: * | filter: object.number match 1.5;
select: * | filter: object.number match "1";
select: * | filter: object.number match "1.0";
select: * | filter: object.number match "1.5";

select: * | filter: object.number = 1;
select: * | filter: object.number = 1.0;
select: * | filter: object.number = 1.5;
select: * | filter: object.number = "1";
select: * | filter: object.number = "1.0";
select: * | filter: object.number = "1.5";

select: * | filter: object.number != 1;
select: * | filter: object.number != 1.0;
select: * | filter: object.number != 1.5;
select: * | filter: object.number != "1";
select: * | filter: object.number != "1.0";
select: * | filter: object.number != "1.5";

select: * | filter: object.number match "asdf";


select: * | filter: number match 1 and number match 2;
select: * | filter: number match 1 or number match 2;
select: * | filter: number match 1.5 and number match 2.5;
select: * | filter: number match 1.5 or number match 2.5;
select: * | filter: number match "1" and number match "2";
select: * | filter: number match "1" or number match "2";

select: * | filter: object.number match 1 and object.number match 2;
select: * | filter: object.number match 1 or object.number match 2;
select: * | filter: object.number match 1.5 and object.number match 2.5;
select: * | filter: object.number match 1.5 or object.number match 2.5;
select: * | filter: object.number match "1" and object.number match "2";
select: * | filter: object.number match "1" or object.number match "2";

select: * | filter: number = 1 and number = 2;
select: * | filter: number = 1 or number = 2;
select: * | filter: number = 1.5 and number = 2.5;
select: * | filter: number = 1.5 or number = 2.5;
select: * | filter: number = "1" and number = "2";
select: * | filter: number = "1" or number = "2";

select: * | filter: object.number = 1 and object.number = 2;
select: * | filter: object.number = 1 or object.number = 2;
select: * | filter: object.number = 1.5 and object.number = 2.5;
select: * | filter: object.number = 1.5 or object.number = 2.5;
select: * | filter: object.number = "1" and object.number = "2";
select: * | filter: object.number = "1" or object.number = "2";

select: * | filter: number match 1 and number match 2 or number match 3;
select: * | filter: (number match 1 and number match 2) or number match 3;
select: * | filter: object.number match 1 and object.number match 2 or object.number match 3;
select: * | filter: (object.number match 1 and object.number match 2) or object.number match 3;
