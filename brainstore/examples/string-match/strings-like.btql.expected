[{"error": null, "query": "// Basic like tests with text field\nselect: * | filter: text like \"hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"%world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"%hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"hello world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"Hello world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text like \"%red%car%\"", "result_rows": [{"number": 2.0, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3.0, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"asf%dfa\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"%mountains%\"", "result_rows": [{"number": null, "object": null, "text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text like \"a%wonderful\"", "result_rows": [{"number": 2.0, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3.0, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "// Basic ilike tests with text field (case insensitive)\nselect: * | filter: text ilike \"hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"%world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"%hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"hello world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"Hello world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"%Red%car%\"", "result_rows": [{"number": 2.0, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3.0, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"Asf%dfa\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"%Mountains%\"", "result_rows": [{"number": null, "object": null, "text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text ilike \"a%Wonderful\"", "result_rows": [{"number": 2.0, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3.0, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "// like tests with object.text field\nselect: * | filter: object.text like \"hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"%world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"%hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"hello world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"Hello world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"%big%cat%\"", "result_rows": [{"number": 4.0, "object": {"text": "a big yellow cat"}, "text": null}], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"%yellow%cat\"", "result_rows": [{"number": 4.0, "object": {"text": "a big yellow cat"}, "text": null}], "skip": false}, {"error": null, "query": "select: * | filter: object.text like \"3%3\"", "result_rows": [{"number": 2.0, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "// ilike tests with object.text field\nselect: * | filter: object.text ilike \"hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"%world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"%hello%\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"hello world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"Hello world\"", "result_rows": [{"number": 1.0, "object": {"number": 1.0, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"%Big%cat%\"", "result_rows": [{"number": 4.0, "object": {"text": "a big yellow cat"}, "text": null}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"%Yellow%cat\"", "result_rows": [{"number": 4.0, "object": {"text": "a big yellow cat"}, "text": null}], "skip": false}, {"error": null, "query": "select: * | filter: object.text ilike \"3%3\"", "result_rows": [{"number": 2.0, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}]