version: 1
schema:
  name: test
  fields:
    - name: text
      tantivy:
        - name: text
          type: !str
            stored: true
            fast: true
            tokenize: true
        - name: text_notok
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: number
      tantivy:
        - name: number
          type: !f64
            stored: true
            fast: true
    - name: object
      tantivy:
        - name: object
          type: !json
            stored: true
            tokenize: true
        - name: object_notok
          type: !json
            stored: true
            tokenize: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/both/metadata
index_uri: ./index/both/data
wal_uri: ./index/both/wal
realtime_wal_uri: ./index/both/wal
