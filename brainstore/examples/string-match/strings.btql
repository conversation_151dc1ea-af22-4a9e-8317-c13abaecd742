select: * | filter: text match "hello";
select: * | filter: text match "hello world";
select: * | filter: text match "hello-world";
select: * | filter: text match "1";
select: * | filter: text match "1.0";
select: * | filter: text match 1;
select: * | filter: text match 1.0;
select: * | filter: text match "asf";
select: * | filter: text match "asf-dfa";
select: * | filter: text match "dfa-asf";
select: * | filter: text match "car bright";
select: * | filter: text match "red car";

select: * | filter: text not match "hello";
select: * | filter: text not match "hello world";
select: * | filter: text not match "hello-world";
select: * | filter: text not match "1";
select: * | filter: text not match "1.0";
select: * | filter: text not match 1;
select: * | filter: text not match 1.0;
select: * | filter: text not match "asf";
select: * | filter: text not match "asf-dfa";
select: * | filter: text not match "dfa-asf";
select: * | filter: text not match "car bright";
select: * | filter: text not match "red car";

select: * | filter: NOT (text match "hello" AND number > 1);
select: * | filter: NOT (text match "hello" OR number > 1);

select: * | filter: text = "hello";
select: * | filter: text = "hello world";
select: * | filter: text = "hello-world";

select: * | filter: object.text match "hello";
select: * | filter: object.text match "hello world";
select: * | filter: object.text match "hello-world";
select: * | filter: object.text match "1";
select: * | filter: object.text match "1.0";
select: * | filter: object.text match 1;
select: * | filter: object.text match 1.0;
select: * | filter: object.text match "asf";
select: * | filter: object.text match "asf-dfa";
select: * | filter: object.text match "dfa-asf";
select: * | filter: object.text match "big cat";
select: * | filter: object.text match "yellow cat";

select: * | filter: object.text = "hello";
select: * | filter: object.text = "hello world";
select: * | filter: object.text = "hello-world";

select: * | filter: object.text=3;
select: * | filter: object.text="3";

select: * | filter: text match "hello" and text match "world";
select: * | filter: text match "hello" or text match "world";
select: * | filter: text match "cat" and text match "big" or text match "yellow";
select: * | filter: (text match "cat" and text match "big") or text match "yellow";

select: * | filter: object.text match "hello" and object.text match "world";
select: * | filter: object.text match "hello" or object.text match "world";
select: * | filter: object.text match "cat" and object.text match "big" or object.text match "yellow";
select: * | filter: (object.text match "cat" and object.text match "big") or object.text match "yellow";

select: * | filter: text = "hello" and text = "world";
select: * | filter: text = "hello" or text = "world";
select: * | filter: object.text = "hello" and object.text = "world";
select: * | filter: object.text = "hello" or object.text = "world";

select: * | filter: text >= "hello";
select: * | filter: text > "hello";
select: * | filter: text <= "hello";
select: * | filter: text < "hello";
select: * | filter: object.text >= "hello";
select: * | filter: object.text > "hello";
select: * | filter: object.text <= "hello";
select: * | filter: object.text < "hello";

select: * | filter: text != "hello";
select: * | filter: text != "hello world";
select: * | filter: object.text != "asf";
select: * | filter: object.text != "asf-dfa";
select: * | filter: text != "hello world" and text != "asf-dfa";
