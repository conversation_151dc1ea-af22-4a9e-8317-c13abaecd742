// Basic like tests with text field
select: * | filter: text like "hello%";
select: * | filter: text like "%world";
select: * | filter: text like "%hello%";
select: * | filter: text like "hello world";
select: * | filter: text like "Hello world";
select: * | filter: text like "%red%car%";
select: * | filter: text like "asf%dfa";
select: * | filter: text like "%mountains%";
select: * | filter: text like "a%wonderful";

// Basic ilike tests with text field (case insensitive)
select: * | filter: text ilike "hello%";
select: * | filter: text ilike "%world";
select: * | filter: text ilike "%hello%";
select: * | filter: text ilike "hello world";
select: * | filter: text ilike "Hello world";
select: * | filter: text ilike "%Red%car%";
select: * | filter: text ilike "Asf%dfa";
select: * | filter: text ilike "%Mountains%";
select: * | filter: text ilike "a%Wonderful";

// like tests with object.text field
select: * | filter: object.text like "hello%";
select: * | filter: object.text like "%world";
select: * | filter: object.text like "%hello%";
select: * | filter: object.text like "hello world";
select: * | filter: object.text like "Hello world";
select: * | filter: object.text like "%big%cat%";
select: * | filter: object.text like "%yellow%cat";
select: * | filter: object.text like "3%3";

// ilike tests with object.text field
select: * | filter: object.text ilike "hello%";
select: * | filter: object.text ilike "%world";
select: * | filter: object.text ilike "%hello%";
select: * | filter: object.text ilike "hello world";
select: * | filter: object.text ilike "Hello world";
select: * | filter: object.text ilike "%Big%cat%";
select: * | filter: object.text ilike "%Yellow%cat";
select: * | filter: object.text ilike "3%3";
