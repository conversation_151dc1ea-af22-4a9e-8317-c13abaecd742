[{"error": null, "query": "select: * | filter: number match 1", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number match 1.0", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number match 1.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: number match \"1\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number match \"1.0\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number match \"1.5\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: number = 1", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number = 1.0", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number = 1.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: number = \"1\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number = \"1.0\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: number = \"1.5\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: number != 1", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: number != 1.0", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: number != 1.5", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: number != \"1\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: number != \"1.0\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: number != \"1.5\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: number match \"asdf\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1.0", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match \"1\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match \"1.0\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match \"1.5\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1.0", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = \"1\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = \"1.0\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = \"1.5\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number != 1", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number != 1.0", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number != 1.5", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number != \"1\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number != \"1.0\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number != \"1.5\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match \"asdf\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number match 1 and number match 2", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number match 1 or number match 2", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: number match 1.5 and number match 2.5", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number match 1.5 or number match 2.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: number match \"1\" and number match \"2\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number match \"1\" or number match \"2\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1 and object.number match 2", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1 or object.number match 2", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1.5 and object.number match 2.5", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1.5 or object.number match 2.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match \"1\" and object.number match \"2\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number match \"1\" or object.number match \"2\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: number = 1 and number = 2", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number = 1 or number = 2", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: number = 1.5 and number = 2.5", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number = 1.5 or number = 2.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: number = \"1\" and number = \"2\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: number = \"1\" or number = \"2\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1 and object.number = 2", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1 or object.number = 2", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1.5 and object.number = 2.5", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number = 1.5 or object.number = 2.5", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number = \"1\" and object.number = \"2\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.number = \"1\" or object.number = \"2\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: number match 1 and number match 2 or number match 3", "result_rows": [{"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: (number match 1 and number match 2) or number match 3", "result_rows": [{"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.number match 1 and object.number match 2 or object.number match 3", "result_rows": [{"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: (object.number match 1 and object.number match 2) or object.number match 3", "result_rows": [{"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}]