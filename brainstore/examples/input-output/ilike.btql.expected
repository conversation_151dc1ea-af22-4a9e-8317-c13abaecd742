[{"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'foo'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'fo%'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'foo%'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE '%foo%'", "result_rows": [{"id": 1, "input": "foo"}, {"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE '%oo%'", "result_rows": [{"id": 1, "input": "foo"}, {"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'foo'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'fo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'foo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%foo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%oo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE '%request%'", "result_rows": [{"id": 4, "input": "new request"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'new request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'new%request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%new%request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'foo'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'fo%'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'foo%'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE '%foo%'", "result_rows": [{"id": 1, "input": "foo"}, {"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE '%oo%'", "result_rows": [{"id": 1, "input": "foo"}, {"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'foo'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'fo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'foo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%foo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%oo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE '%request%'", "result_rows": [{"id": 4, "input": "new request"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'new request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'new%request'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%new%request%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'FOO'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'FO%'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'FOO%'", "result_rows": [{"id": 1, "input": "foo"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE '%FOO%'", "result_rows": [{"id": 1, "input": "foo"}, {"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE '%oo%'", "result_rows": [{"id": 1, "input": "foo"}, {"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'FOO'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'FO%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'FOO%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%FOO%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%oo%'", "result_rows": [{"id": 2, "input": {"empty": "", "field": "foo"}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'REQUEST'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE 'REQUEST%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input ILIKE '%REQUEST%'", "result_rows": [{"id": 4, "input": "new request"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'REQUEST'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'REQUEST%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%REQUEST%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'FOO'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'FO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'FOO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE '%FOO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE '%OO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'FOO'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'FO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'FOO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%FOO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%OO%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'REQUEST'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE 'REQUEST%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input LIKE '%REQUEST%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'REQUEST'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE 'REQUEST%'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, input | filter: input.field LIKE '%REQUEST%'", "result_rows": [], "skip": false}]