from: dataset('singleton') | select: id, input | filter: input ILIKE 'foo';
from: dataset('singleton') | select: id, input | filter: input ILIKE 'fo%';
from: dataset('singleton') | select: id, input | filter: input ILIKE 'foo%';
from: dataset('singleton') | select: id, input | filter: input ILIKE '%foo%';
from: dataset('singleton') | select: id, input | filter: input ILIKE '%oo%';

from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'foo';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'fo%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'foo%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%foo%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%oo%';

from: dataset('singleton') | select: id, input | filter: input ILIKE 'request';
from: dataset('singleton') | select: id, input | filter: input ILIKE 'request%';
from: dataset('singleton') | select: id, input | filter: input ILIKE '%request%';

from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'request';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'request%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%request%';

from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'new request';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'new%request';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%new%request%';


from: dataset('singleton') | select: id, input | filter: input LIKE 'foo';
from: dataset('singleton') | select: id, input | filter: input LIKE 'fo%';
from: dataset('singleton') | select: id, input | filter: input LIKE 'foo%';
from: dataset('singleton') | select: id, input | filter: input LIKE '%foo%';
from: dataset('singleton') | select: id, input | filter: input LIKE '%oo%';

from: dataset('singleton') | select: id, input | filter: input.field LIKE 'foo';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'fo%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'foo%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%foo%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%oo%';

from: dataset('singleton') | select: id, input | filter: input LIKE 'request';
from: dataset('singleton') | select: id, input | filter: input LIKE 'request%';
from: dataset('singleton') | select: id, input | filter: input LIKE '%request%';

from: dataset('singleton') | select: id, input | filter: input.field LIKE 'request';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'request%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%request%';

from: dataset('singleton') | select: id, input | filter: input.field LIKE 'new request';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'new%request';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%new%request%';


from: dataset('singleton') | select: id, input | filter: input ILIKE 'FOO';
from: dataset('singleton') | select: id, input | filter: input ILIKE 'FO%';
from: dataset('singleton') | select: id, input | filter: input ILIKE 'FOO%';
from: dataset('singleton') | select: id, input | filter: input ILIKE '%FOO%';
from: dataset('singleton') | select: id, input | filter: input ILIKE '%oo%';

from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'FOO';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'FO%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'FOO%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%FOO%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%oo%';

from: dataset('singleton') | select: id, input | filter: input ILIKE 'REQUEST';
from: dataset('singleton') | select: id, input | filter: input ILIKE 'REQUEST%';
from: dataset('singleton') | select: id, input | filter: input ILIKE '%REQUEST%';

from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'REQUEST';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE 'REQUEST%';
from: dataset('singleton') | select: id, input | filter: input.field ILIKE '%REQUEST%';

from: dataset('singleton') | select: id, input | filter: input LIKE 'FOO';
from: dataset('singleton') | select: id, input | filter: input LIKE 'FO%';
from: dataset('singleton') | select: id, input | filter: input LIKE 'FOO%';
from: dataset('singleton') | select: id, input | filter: input LIKE '%FOO%';
from: dataset('singleton') | select: id, input | filter: input LIKE '%OO%';

from: dataset('singleton') | select: id, input | filter: input.field LIKE 'FOO';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'FO%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'FOO%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%FOO%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%OO%';

from: dataset('singleton') | select: id, input | filter: input LIKE 'REQUEST';
from: dataset('singleton') | select: id, input | filter: input LIKE 'REQUEST%';
from: dataset('singleton') | select: id, input | filter: input LIKE '%REQUEST%';

from: dataset('singleton') | select: id, input | filter: input.field LIKE 'REQUEST';
from: dataset('singleton') | select: id, input | filter: input.field LIKE 'REQUEST%';
from: dataset('singleton') | select: id, input | filter: input.field LIKE '%REQUEST%';
