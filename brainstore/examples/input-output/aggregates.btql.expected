[{"error": null, "query": "from: dataset('singleton') | measures: count(1) as total, count(metadata) as non_null, count(is_root ? metadata : null) as non_null_filter", "result_rows": [{"non_null": 6, "non_null_filter": 3, "total": 7}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | measures: sum(metadata is null) as m_null", "result_rows": [{"m_null": 1}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | measures: sum(metadata is not null) as m_not_null", "result_rows": [{"m_not_null": 6}], "skip": false}, {"error": null, "query": "-- span_attributes has the same schema as error\nfrom: dataset('singleton') | measures: count(1) as total, count(span_attributes) as non_null, count(is_root ? span_attributes : null) as non_null_filter", "result_rows": [{"non_null": 6, "non_null_filter": 3, "total": 7}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | measures: sum(span_attributes is null) as m_null", "result_rows": [{"m_null": 1}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | measures: sum(span_attributes is not null) as m_not_null", "result_rows": [{"m_not_null": 6}], "skip": false}]