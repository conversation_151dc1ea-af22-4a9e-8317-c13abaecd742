from: dataset('singleton') | select: id | filter: input='foo';
from: dataset('singleton') | select: id | filter: input.field='foo';

-- These should run the ternary expressions
from: dataset('singleton') spans | select: id, is_root ? input : null as r;
from: dataset('singleton') spans | select: id, is_root ? null : input as r;

-- These should optimize away the ternary expression from the plan
from: dataset('singleton') traces | select: id, is_root ? input : null as r | filter: id=1;
from: dataset('singleton') traces | select: id, is_root ? null : input as r | filter: id=1;

from: dataset('singleton') spans | select: sha256(null);
from: dataset('singleton') spans | select: id, sha256(id);

from: dataset('singleton') spans | select: id | filter: input.empty IS NULL;
from: dataset('singleton') spans | select: id | filter: input.empty IS NOT NULL;
from: dataset('singleton') spans | select: id | filter: input.empty = '';

/*!optimizer -- should not generate a Filter node

[.. | objects | select(has("Filter"))] | length == 0

 */
from: dataset('singleton') spans | select: id | filter: input.empty != '';

/*!optimizer -- should not generate a Filter node

[.. | objects | select(has("Filter"))] | length == 0

 */
from: dataset('singleton') spans | select: id | filter: input.empty is not null and input.empty != '';


/*!optimizer -- should not generate a Filter node

[.. | objects | select(has("Filter"))] | length == 0

 */
from: dataset('singleton') spans | select: id | filter: input is not null and input != '';
