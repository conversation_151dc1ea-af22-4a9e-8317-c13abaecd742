from: dataset('singleton') | measures: count(1) as total, count(metadata) as non_null, count(is_root ? metadata : null) as non_null_filter;
from: dataset('singleton') | measures: sum(metadata is null) as m_null;
from: dataset('singleton') | measures: sum(metadata is not null) as m_not_null;

-- span_attributes has the same schema as error
from: dataset('singleton') | measures: count(1) as total, count(span_attributes) as non_null, count(is_root ? span_attributes : null) as non_null_filter;
from: dataset('singleton') | measures: sum(span_attributes is null) as m_null;
from: dataset('singleton') | measures: sum(span_attributes is not null) as m_not_null;
