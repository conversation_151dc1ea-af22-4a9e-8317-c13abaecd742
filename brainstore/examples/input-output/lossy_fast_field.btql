from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] is null;
from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] is not null;
from: dataset('singleton') | select: id, metadata.list[-1] | filter: metadata.list[-1] is null;
from: dataset('singleton') | select: id, metadata.list[-1] | filter: metadata.list[-1] is not null;
from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] = 5;
from: dataset('singleton') | select: id, metadata.list[1] | filter: metadata.list[1] = 'a';
from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] = -3;
from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] = 'baz';

from: dataset('singleton') | select: id, metadata.list[3].z | filter: metadata.list[3].z is null;
from: dataset('singleton') | select: id, metadata.list[3].z | filter: metadata.list[-1].z is null;
from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b is null;
from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b is not null;
from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b is null;
from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b is not null;
from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b = "c";
from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b != "c";
from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b = "z";
from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b != "z";
from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b = "c";
from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b != "c";
from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b = "z";
from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b != "z";

from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo ILIKE "%red%";
from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo ILIKE "%blue%";
from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo ILIKE "%red%";
from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo ILIKE "%blue%";

from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo match "red";
from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo match "blue";
from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo match "red";
from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo match "blue";

from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] ILIKE "%red%";
from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] ILIKE "%blue%";
from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] ILIKE "%red%";
from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] ILIKE "%blue%";

from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] match "red";
from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] match "blue";
from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] match "red";
from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] match "blue";

from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] >= 5;
from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] < 4.9;
from: dataset('singleton') | select: id, metadata.list[-4] | filter: metadata.list[-4] > 5.0;
from: dataset('singleton') | select: id, metadata.list[-4] | filter: metadata.list[-4] >= 4.9;

from: dataset('singleton') | select: id, metadata.list[3].num | filter: metadata.list[3].num > 0;
from: dataset('singleton') | select: id, metadata.list[3].num | filter: metadata.list[3].num <= 1;
from: dataset('singleton') | select: id, metadata.list[-1].num | filter: metadata.list[-1].num >= 0;
from: dataset('singleton') | select: id, metadata.list[-1].num | filter: metadata.list[-1].num < -1;

from: dataset('singleton') | select: id, coalesce(metadata.list[0], 'foo');
from: dataset('singleton') | select: id, coalesce(metadata.list[-1], 'foo');
from: dataset('singleton') | select: id, coalesce(metadata.list[0], 'bar') | filter: coalesce(metadata.list[0], 'bar') = 'bar';
from: dataset('singleton') | select: id, coalesce(metadata.list[-1], 'bar') | filter: coalesce(metadata.list[-1], 'bar') = 'bar';

select: metadata.model | from: dataset('singleton') traces;
