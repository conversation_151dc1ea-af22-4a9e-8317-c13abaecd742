{"id": "1", "root_span_id": "1", "span_id": "1", "span_parents": [], "span_attributes": { "name": "asdf", "env": "prod", "service": "api", "region": "us-east-1"}, "input": "foo", "output": "bar", "scores": {"confidence": 0.95, "relevance": 0.87}, "metrics": {"latency_ms": 42, "tokens": 12}, "metadata": {"model": "gpt-4", "temperature": 0.7, "version": "1.0", "list": [{}, "a", -3, {"b": "c", "num": 0}]}}
{"id": "2", "root_span_id": "1", "span_id": "2", "span_parents": ["1"], "span_attributes": { "name": "asdf", "env": "prod", "priority": "high", "retry_count": 2}, "input": {"field": "foo", "empty": ""}, "output": {"field": "bar"}, "scores": {"confidence": 0.82, "relevance": 0.91}, "metrics": {"latency_ms": 38, "tokens": 15}, "metadata": {"model": "gpt-4", "temperature": 0.7, "version": "1.0"}}
{"id": "3", "root_span_id": "1", "span_id": "3", "span_parents": ["2"], "span_attributes": { "name": "asdf", "env": "staging", "component": "worker", "queue": "default"}, "input": "process nested", "output": "processed", "scores": {"confidence": 0.78, "relevance": 0.85}, "metrics": {"latency_ms": 25, "tokens": 8}, "metadata": {"model": "gpt-3.5-turbo", "temperature": 0.5, "version": "1.0"}}
{"id": "4", "root_span_id": "4", "span_id": "4", "span_parents": [], "span_attributes": { "name": "asdf", "env": "prod", "user_id": "123", "request_type": "sync"}, "input": "new request", "output": "response", "scores": {"confidence": 0.92, "relevance": 0.89}, "metrics": {"latency_ms": 35, "tokens": 10}, "metadata": {"model": "gpt-4", "temperature": 0.7, "version": "1.0"}}
{"id": "5", "root_span_id": "4", "span_id": "5", "span_parents": ["4"], "span_attributes": { "name": "asdf", "env": "prod", "batch_size": 100, "status": "success"}, "input": "sub task", "output": "completed", "scores": {"confidence": 0.88, "relevance": 0.93}, "metrics": {"latency_ms": 28, "tokens": 9}, "metadata": {"model": "gpt-4", "temperature": 0.7, "version": "1.0"}}
{"id": "6", "root_span_id": "6", "span_id": "6", "span_parents": [], "span_attributes": { "name": "asdf", "env": "dev", "experiment_id": "abc123", "feature_flags": ["beta", "new_ui"]}, "metadata": {"model": "gpt-4", "list": [5, {"foo": "big red car", "num": 123}, "small blue truck", {}]}}
{"id": "7", "root_span_id": "7", "span_id": "7", "span_parents": [] }
