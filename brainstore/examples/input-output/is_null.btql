from: dataset('singleton') | select: id | filter: input IS NULL;
from: dataset('singleton') | select: id | filter: input.field IS NULL;

from: dataset('singleton') | select: id | filter: input IS NOT NULL;
from: dataset('singleton') | select: id | filter: input.field IS NOT NULL;

from: dataset('singleton') | select: id | filter: input;
from: dataset('singleton') | select: id | filter: input.field;
from: dataset('singleton') | select: id | filter: id;
