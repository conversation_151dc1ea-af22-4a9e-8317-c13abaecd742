[{"error": null, "query": "select: * | filter: foo='bar bar' | limit: 1", "result_rows": [{"a": 2, "foo": "bar bar", "object": {"a": 39, "b": 4}}], "skip": false}, {"error": null, "query": "select: * | filter: foo match 'bar' | limit: 1 | sort: a", "result_rows": [{"a": 1, "foo": "bar", "object": {"a": 40, "b": 2}}], "skip": false}, {"error": null, "query": "select: * | filter: foo='bar bar' | limit: 0", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: foo match 'bar' | limit: 0 | sort: a", "result_rows": [], "skip": false}]