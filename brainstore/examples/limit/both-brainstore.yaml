version: 1
schema:
  name: test
  fields:
    - name: a
      tantivy:
        - name: a
          type: !u64
            stored: true
            fast: true
        - name: a_slow
          type: !u64
            stored: true
            fast: false
    - name: foo
      tantivy:
        - name: foo
          type: !str
            stored: true
            tokenize: true
    - name: object
      tantivy:
        - name: object
          type: !json
            stored: true
            tokenize: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/both/metadata
index_uri: ./index/both/data
wal_uri: ./index/both/wal
realtime_wal_uri: ./index/both/wal
