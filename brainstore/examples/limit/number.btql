select: *;

select: * | sort: a | limit: 1;
select: * | sort: a desc | limit: 1;

select: * | sort: a | limit: 5;
select: * | sort: a desc | limit: 5;

select: * | sort: a | limit: 100;
select: * | sort: a desc | limit: 100;


select: * | sort: object.a, a | limit: 1;
select: * | sort: object.a desc, a | limit: 1;
select: * | sort: object.a , a desc | limit: 1;

select: * | sort: object.a, a | limit: 5;
select: * | sort: object.a desc, a | limit: 5;
select: * | sort: object.a , a desc | limit: 5;

select: * | sort: object.a, a | limit: 100;
select: * | sort: object.a desc, a | limit: 100;
select: * | sort: object.a , a desc | limit: 100;
