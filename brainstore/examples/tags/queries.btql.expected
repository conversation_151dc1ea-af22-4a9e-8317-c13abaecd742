[{"error": null, "query": "select: *", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3, "tags": ["a"]}, {"a": 4, "tags": ["a"]}, {"a": 5, "tags": ["a", "b"]}, {"a": 6, "tags": ["ab"]}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes []", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 5}, {"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes null", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 5}, {"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes [null]", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: tags includes 'a'", "result_rows": [{"a": 3}, {"a": 4}, {"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes 'b'", "result_rows": [{"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes 'ab'", "result_rows": [{"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes 'ba'", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: tags includes 'cab'", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: not tags includes 'b'", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['a']", "result_rows": [{"a": 3}, {"a": 4}, {"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['b']", "result_rows": [{"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['ab']", "result_rows": [{"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['ba']", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['cab']", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: not tags includes ['b']", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['a', 'b']", "result_rows": [{"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['b', 'a']", "result_rows": [{"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['a', 'b', 'c']", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: tags includes ['a', 'b', null]", "result_rows": [], "skip": false}, {"error": null, "query": "select: a | filter: [] includes tags", "result_rows": [{"a": 1}, {"a": 2}], "skip": false}, {"error": null, "query": "select: a | filter: null includes tags", "result_rows": [{"a": 1}, {"a": 2}], "skip": false}, {"error": null, "query": "select: a | filter: [null] includes tags", "result_rows": [{"a": 1}, {"a": 2}], "skip": false}, {"error": null, "query": "select: a | filter: 'a' includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}], "skip": false}, {"error": null, "query": "select: a | filter: 'b' includes tags", "result_rows": [{"a": 1}, {"a": 2}], "skip": false}, {"error": null, "query": "select: a | filter: ['a'] includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}], "skip": false}, {"error": null, "query": "select: a | filter: ['b'] includes tags", "result_rows": [{"a": 1}, {"a": 2}], "skip": false}, {"error": null, "query": "select: a | filter: ['ab'] includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 6}], "skip": false}, {"error": null, "query": "select: a | filter: ['a', 'b'] includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: ['b', 'a'] includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: ['a', 'b', 'c'] includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 5}], "skip": false}, {"error": null, "query": "select: a | filter: ['a', 'b', null] includes tags", "result_rows": [{"a": 1}, {"a": 2}, {"a": 3}, {"a": 4}, {"a": 5}], "skip": false}, {"error": null, "query": "select: a, tags, len(a), len(tags)", "result_rows": [{"a": 1, "len(a)": 0, "len(tags)": 0}, {"a": 2, "len(a)": 0, "len(tags)": 0}, {"a": 3, "len(a)": 0, "len(tags)": 1, "tags": ["a"]}, {"a": 4, "len(a)": 0, "len(tags)": 1, "tags": ["a"]}, {"a": 5, "len(a)": 0, "len(tags)": 2, "tags": ["a", "b"]}, {"a": 6, "len(a)": 0, "len(tags)": 1, "tags": ["ab"]}], "skip": false}, {"error": null, "query": "select: a | filter: len(tags) is null", "result_rows": [], "skip": false}, {"error": null, "query": "measures: count(1) | filter: len(tags) = 0", "result_rows": [{"count(1)": 2}], "skip": false}, {"error": null, "query": "measures: count(1) | filter: len(tags) = 1", "result_rows": [{"count(1)": 3}], "skip": false}, {"error": null, "query": "measures: count(1) | filter: len(tags) >= 2", "result_rows": [{"count(1)": 1}], "skip": false}, {"error": null, "query": "dimensions: len(tags) | measures: count(1)", "result_rows": [{"count(1)": 1, "len(tags)": 2}, {"count(1)": 2, "len(tags)": 0}, {"count(1)": 3, "len(tags)": 1}], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"type\":\"string\"}}", "query": "-- These are currently unsupported because we can't index into fields of type `array`.\n-- We'll have to update the BTQL binder to support this.\nselect: a, tags[0] | filter: tags[1] = 'a'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"type\":\"string\"}} ... at line 1, column 31:\n1:  select: a, tags[-1] | filter: tags[-1] = 'b'\n                                  ^^^^^^^^", "query": "select: a, tags[-1] | filter: tags[-1] = 'b'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"type\":\"string\"}} ... at line 1, column 30:\n1:  select: a, tags[0] | filter: tags[0] is null\n                                 ^^^^^^^", "query": "select: a, tags[0] | filter: tags[0] is null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"type\":\"string\"}} ... at line 1, column 30:\n1:  select: a, tags[0] | filter: tags[0] is not null\n                                 ^^^^^^^", "query": "select: a, tags[0] | filter: tags[0] is not null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"type\":\"string\"}} ... at line 1, column 31:\n1:  select: a, tags[-2] | filter: tags[-2] is null\n                                  ^^^^^^^^", "query": "select: a, tags[-2] | filter: tags[-2] is null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"type\":\"string\"}} ... at line 1, column 31:\n1:  select: a, tags[-2] | filter: tags[-2] is not null\n                                  ^^^^^^^^", "query": "select: a, tags[-2] | filter: tags[-2] is not null", "result_rows": [], "skip": false}]