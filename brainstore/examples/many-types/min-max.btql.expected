[{"error": null, "query": "measures: min(null)", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(null)", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: min(bool)", "result_rows": [{"min(bool)": false}], "skip": false}, {"error": null, "query": "measures: max(bool)", "result_rows": [{"max(bool)": true}], "skip": false}, {"error": null, "query": "measures: min(bool) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(bool) | filter: id <= \"row2\"", "result_rows": [{"max(bool)": true}], "skip": false}, {"error": null, "query": "measures: min(bool) | filter: id <= \"row3\"", "result_rows": [{"min(bool)": false}], "skip": false}, {"error": null, "query": "measures: max(bool) | filter: id <= \"row4\"", "result_rows": [{"max(bool)": true}], "skip": false}, {"error": null, "query": "measures: min(bool) | filter: id <= \"row5\"", "result_rows": [{"min(bool)": false}], "skip": false}, {"error": null, "query": "measures: max(bool) | filter: id <= \"row6\"", "result_rows": [{"max(bool)": true}], "skip": false}, {"error": null, "query": "measures: min(bool) | filter: id <= \"row7\"", "result_rows": [{"min(bool)": false}], "skip": false}, {"error": null, "query": "measures: min(int)", "result_rows": [{"min(int)": -7}], "skip": false}, {"error": null, "query": "measures: max(int)", "result_rows": [{"max(int)": 1000}], "skip": false}, {"error": null, "query": "measures: min(int) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(int) | filter: id <= \"row2\"", "result_rows": [{"max(int)": 42}], "skip": false}, {"error": null, "query": "measures: min(int) | filter: id <= \"row3\"", "result_rows": [{"min(int)": -7}], "skip": false}, {"error": null, "query": "measures: max(int) | filter: id <= \"row4\"", "result_rows": [{"max(int)": 1000}], "skip": false}, {"error": null, "query": "measures: min(int) | filter: id <= \"row5\"", "result_rows": [{"min(int)": -7}], "skip": false}, {"error": null, "query": "measures: max(int) | filter: id <= \"row6\"", "result_rows": [{"max(int)": 1000}], "skip": false}, {"error": null, "query": "measures: min(int) | filter: id <= \"row7\"", "result_rows": [{"min(int)": -7}], "skip": false}, {"error": null, "query": "measures: min(float)", "result_rows": [{"min(float)": -1.414}], "skip": false}, {"error": null, "query": "measures: max(float)", "result_rows": [{"max(float)": 3.14}], "skip": false}, {"error": null, "query": "measures: min(float) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(float) | filter: id <= \"row2\"", "result_rows": [{"max(float)": 3.14}], "skip": false}, {"error": null, "query": "measures: min(float) | filter: id <= \"row3\"", "result_rows": [{"min(float)": 2.718}], "skip": false}, {"error": null, "query": "measures: max(float) | filter: id <= \"row4\"", "result_rows": [{"max(float)": 3.14}], "skip": false}, {"error": null, "query": "measures: min(float) | filter: id <= \"row5\"", "result_rows": [{"min(float)": -1.414}], "skip": false}, {"error": null, "query": "measures: max(float) | filter: id <= \"row6\"", "result_rows": [{"max(float)": 3.14}], "skip": false}, {"error": null, "query": "measures: min(float) | filter: id <= \"row7\"", "result_rows": [{"min(float)": -1.414}], "skip": false}, {"error": null, "query": "measures: min(str)", "result_rows": [{"min(str)": "alpha"}], "skip": false}, {"error": null, "query": "measures: max(str)", "result_rows": [{"max(str)": "💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩"}], "skip": false}, {"error": null, "query": "measures: min(str) | filter: false", "result_rows": [null], "skip": false}, {"error": null, "query": "measures: max(str) | filter: id <= \"row2\"", "result_rows": [{"max(str)": "zebra"}], "skip": false}, {"error": null, "query": "measures: min(str) | filter: id <= \"row3\"", "result_rows": [{"min(str)": "alpha"}], "skip": false}, {"error": null, "query": "measures: max(str) | filter: id <= \"row4\"", "result_rows": [{"max(str)": "zebra"}], "skip": false}, {"error": null, "query": "measures: min(str) | filter: id <= \"row5\"", "result_rows": [{"min(str)": "alpha"}], "skip": false}, {"error": null, "query": "measures: max(str) | filter: id <= \"row6\"", "result_rows": [{"max(str)": "zebra"}], "skip": false}, {"error": null, "query": "measures: min(str) | filter: id <= \"row7\"", "result_rows": [{"min(str)": "alpha"}], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- These are broken if arr, obj, or unknown are fast fields\n-- See https://linear.app/braintrustdata/issue/BRA-2304/group-by-and-aggregate-on-json-fast-values-broken\n\n-- measures: min(arr)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(arr)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(arr) | filter: false", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(arr) | filter: id <= \"row2\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(arr) | filter: id <= \"row3\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(arr) | filter: id <= \"row4\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(arr) | filter: id <= \"row5\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(arr) | filter: id <= \"row6\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(arr) | filter: id <= \"row7\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "--\n-- measures: min(obj)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(obj)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(obj) | filter: false", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(obj) | filter: id <= \"row2\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(obj) | filter: id <= \"row3\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(obj) | filter: id <= \"row4\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(obj) | filter: id <= \"row5\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(obj) | filter: id <= \"row6\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(obj) | filter: id <= \"row7\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "--\n-- measures: min(unknown)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(unknown)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(unknown) | filter: false", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(unknown) | filter: id <= \"row2\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(unknown) | filter: id <= \"row3\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(unknown) | filter: id <= \"row4\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(unknown) | filter: id <= \"row5\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: max(unknown) | filter: id <= \"row6\"", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Must have either select or dimensions", "query": "-- measures: min(unknown) | filter: id <= \"row7\"", "result_rows": [], "skip": false}]