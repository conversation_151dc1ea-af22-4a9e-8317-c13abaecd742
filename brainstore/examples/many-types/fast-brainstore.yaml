version: 1
schema:
  name: many-types
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
    - name: bool
      tantivy:
        - name: bool
          type: !bool
            stored: true
            fast: true
    - name: int
      tantivy:
        - name: int
          type: !i64
            stored: true
            fast: true
    - name: float
      tantivy:
        - name: float
          type: !f64
            stored: true
            fast: true
    - name: str
      tantivy:
        - name: str
          type: !str
            stored: true
            fast: true
    - name: arr
      tantivy:
        - name: arr_json
          type: !json
            stored: true
            fast: true
        - name: arr_col
          type: !json
            stored: false
            fast: false
            tokenize: true
    - name: obj
      tantivy:
        - name: obj_json
          type: !json
            stored: true
            fast: true
        - name: obj_col
          type: !json
            stored: false
            fast: false
            tokenize: true
    - name: unknown
      tantivy:
        - name: unknown_json
          lossy_fast_field: true
          type: !json
            stored: true
            fast: true
        - name: unknown_col
          type: !json
            stored: false
            fast: false
            tokenize: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/fast/metadata
index_uri: ./index/fast/data
wal_uri: ./index/fast/wal
realtime_wal_uri: ./index/fast/wal
