{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "bool": {"type": "boolean"}, "int": {"type": "integer"}, "float": {"type": "number"}, "str": {"type": "string"}, "arr": {"anyOf": [{"type": "array", "items": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "boolean"}, {"type": "null"}]}}, {"type": "null"}]}, "obj": {"type": "object", "additionalProperties": {}}, "unknown": {}}, "required": ["id"]}}