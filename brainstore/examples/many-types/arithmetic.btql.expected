[{"error": null, "query": "select: id, int, float | filter: int + float > 3", "result_rows": [{"float": -1.414, "id": "row4", "int": 1000}, {"float": 3.14, "id": "row2", "int": 42}], "skip": false}, {"error": null, "query": "select: id, float - int", "result_rows": [{"float - int": -1001.414, "id": "row4"}, {"float - int": -38.86, "id": "row2"}, {"float - int": 0, "id": "row5"}, {"float - int": 9.718, "id": "row3"}, {"id": "row1"}, {"id": "row6"}, {"id": "row7"}, {"id": "row8"}], "skip": false}, {"error": null, "query": "measures: avg(float + int)", "result_rows": [{"avg(float + int)": 259.861}], "skip": false}, {"error": null, "query": "measures: sum(int + float + int)", "result_rows": [{"sum(int + float + int)": 2074.444}], "skip": false}, {"error": null, "query": "measures: sum(int + unknown)", "result_rows": [{"sum(int + unknown)": 1049.14}], "skip": false}]