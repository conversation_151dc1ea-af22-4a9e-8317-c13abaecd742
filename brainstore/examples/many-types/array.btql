select: id | filter: arr is null;
select: id | filter: arr is not null;

select: id | filter: len(arr) = 0;
select: id | filter: len(arr) = 2;
select: id | filter: len(arr) = 3;

-- The array indexing queries below are currently unsupported because
-- we can't index into fields of type `array`. We'll have to update
-- the BTQL binder to support this.

select: id | filter: arr[0] is null;
select: id | filter: arr[0] is not null;
select: id | filter: arr[1] is null;
select: id | filter: arr[1] is not null;

select: id | filter: arr[2] is null;
select: id | filter: arr[2] is not null;
select: id | filter: arr[-3] is null;
select: id | filter: arr[-3] is not null;

select: id | filter: arr[0] = 1;
select: id | filter: arr[1] = 2;
select: id | filter: arr[2] = 3;

select: id | filter: arr[-3] = 1;
select: id | filter: arr[-2] = 2;
select: id | filter: arr[-1] = 3;

select: id | filter: arr[0] = 'a';
select: id | filter: arr[1] = 'b';

select: id | filter: arr[0];
select: id | filter: arr[1];
select: id | filter: arr[2];
select: id | filter: arr[-1];
select: id | filter: arr[-2];
select: id | filter: arr[-3];
