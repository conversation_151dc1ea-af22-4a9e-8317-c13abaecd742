[{"error": null, "query": "select: id | filter: arr is null", "result_rows": [{"id": "row1"}, {"id": "row5"}, {"id": "row6"}, {"id": "row7"}, {"id": "row8"}], "skip": false}, {"error": null, "query": "select: id | filter: arr is not null", "result_rows": [{"id": "row2"}, {"id": "row3"}, {"id": "row4"}], "skip": false}, {"error": null, "query": "select: id | filter: len(arr) = 0", "result_rows": [{"id": "row1"}, {"id": "row5"}, {"id": "row6"}, {"id": "row7"}, {"id": "row8"}], "skip": false}, {"error": null, "query": "select: id | filter: len(arr) = 2", "result_rows": [{"id": "row3"}, {"id": "row4"}], "skip": false}, {"error": null, "query": "select: id | filter: len(arr) = 3", "result_rows": [{"id": "row2"}], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}}", "query": "-- The array indexing queries below are currently unsupported because\n-- we can't index into fields of type `array`. We'll have to update\n-- the BTQL binder to support this.\n\nselect: id | filter: arr[0] is null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[0] is not null\n                         ^^^^^^", "query": "select: id | filter: arr[0] is not null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[1] is null\n                         ^^^^^^", "query": "select: id | filter: arr[1] is null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[1] is not null\n                         ^^^^^^", "query": "select: id | filter: arr[1] is not null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[2] is null\n                         ^^^^^^", "query": "select: id | filter: arr[2] is null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[2] is not null\n                         ^^^^^^", "query": "select: id | filter: arr[2] is not null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-3\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-3] is null\n                         ^^^^^^^", "query": "select: id | filter: arr[-3] is null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-3\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-3] is not null\n                         ^^^^^^^", "query": "select: id | filter: arr[-3] is not null", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[0] = 1\n                         ^^^^^^", "query": "select: id | filter: arr[0] = 1", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[1] = 2\n                         ^^^^^^", "query": "select: id | filter: arr[1] = 2", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[2] = 3\n                         ^^^^^^", "query": "select: id | filter: arr[2] = 3", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-3\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-3] = 1\n                         ^^^^^^^", "query": "select: id | filter: arr[-3] = 1", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-2] = 2\n                         ^^^^^^^", "query": "select: id | filter: arr[-2] = 2", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-1] = 3\n                         ^^^^^^^", "query": "select: id | filter: arr[-1] = 3", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[0] = 'a'\n                         ^^^^^^", "query": "select: id | filter: arr[0] = 'a'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[1] = 'b'\n                         ^^^^^^", "query": "select: id | filter: arr[1] = 'b'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"0\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[0]\n                         ^^^^^^", "query": "select: id | filter: arr[0]", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[1]\n                         ^^^^^^", "query": "select: id | filter: arr[1]", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[2]\n                         ^^^^^^", "query": "select: id | filter: arr[2]", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-1\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-1]\n                         ^^^^^^^", "query": "select: id | filter: arr[-1]", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-2\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-2]\n                         ^^^^^^^", "query": "select: id | filter: arr[-2]", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Trying to access field \"-3\" in a non-object (array) {\"type\":\"array\",\"items\":{\"anyOf\":[{\"type\":\"number\"},{\"type\":\"string\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]}} ... at line 1, column 22:\n1:  select: id | filter: arr[-3]\n                         ^^^^^^^", "query": "select: id | filter: arr[-3]", "result_rows": [], "skip": false}]