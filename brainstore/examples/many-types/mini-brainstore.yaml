version: 1
schema:
  name: test
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
    - name: bool
      tantivy:
        - name: bool
          type: !bool
            stored: true
    - name: int
      tantivy:
        - name: int
          type: !i64
            stored: true
    - name: float
      tantivy:
        - name: float
          type: !f64
            stored: true
    - name: str
      tantivy:
        - name: str
          type: !str
            stored: true
    - name: arr
      tantivy:
        - name: arr
          type: !json
            stored: true
    - name: obj
      tantivy:
        - name: obj
          type: !json
            stored: true
    - name: unknown
      tantivy:
        - name: unknown
          type: !json
            stored: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/mini/metadata
index_uri: ./index/mini/data
wal_uri: ./index/mini/wal
realtime_wal_uri: ./index/mini/wal
