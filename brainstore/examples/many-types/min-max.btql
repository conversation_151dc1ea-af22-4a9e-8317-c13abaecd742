measures: min(null);
measures: max(null);

measures: min(bool);
measures: max(bool);
measures: min(bool) | filter: false;
measures: max(bool) | filter: id <= "row2";
measures: min(bool) | filter: id <= "row3";
measures: max(bool) | filter: id <= "row4";
measures: min(bool) | filter: id <= "row5";
measures: max(bool) | filter: id <= "row6";
measures: min(bool) | filter: id <= "row7";

measures: min(int);
measures: max(int);
measures: min(int) | filter: false;
measures: max(int) | filter: id <= "row2";
measures: min(int) | filter: id <= "row3";
measures: max(int) | filter: id <= "row4";
measures: min(int) | filter: id <= "row5";
measures: max(int) | filter: id <= "row6";
measures: min(int) | filter: id <= "row7";

measures: min(float);
measures: max(float);
measures: min(float) | filter: false;
measures: max(float) | filter: id <= "row2";
measures: min(float) | filter: id <= "row3";
measures: max(float) | filter: id <= "row4";
measures: min(float) | filter: id <= "row5";
measures: max(float) | filter: id <= "row6";
measures: min(float) | filter: id <= "row7";

measures: min(str);
measures: max(str);
measures: min(str) | filter: false;
measures: max(str) | filter: id <= "row2";
measures: min(str) | filter: id <= "row3";
measures: max(str) | filter: id <= "row4";
measures: min(str) | filter: id <= "row5";
measures: max(str) | filter: id <= "row6";
measures: min(str) | filter: id <= "row7";

-- These are broken if arr, obj, or unknown are fast fields
-- See https://linear.app/braintrustdata/issue/BRA-2304/group-by-and-aggregate-on-json-fast-values-broken

-- measures: min(arr);
-- measures: max(arr);
-- measures: min(arr) | filter: false;
-- measures: max(arr) | filter: id <= "row2";
-- measures: min(arr) | filter: id <= "row3";
-- measures: max(arr) | filter: id <= "row4";
-- measures: min(arr) | filter: id <= "row5";
-- measures: max(arr) | filter: id <= "row6";
-- measures: min(arr) | filter: id <= "row7";
--
-- measures: min(obj);
-- measures: max(obj);
-- measures: min(obj) | filter: false;
-- measures: max(obj) | filter: id <= "row2";
-- measures: min(obj) | filter: id <= "row3";
-- measures: max(obj) | filter: id <= "row4";
-- measures: min(obj) | filter: id <= "row5";
-- measures: max(obj) | filter: id <= "row6";
-- measures: min(obj) | filter: id <= "row7";
--
-- measures: min(unknown);
-- measures: max(unknown);
-- measures: min(unknown) | filter: false;
-- measures: max(unknown) | filter: id <= "row2";
-- measures: min(unknown) | filter: id <= "row3";
-- measures: max(unknown) | filter: id <= "row4";
-- measures: min(unknown) | filter: id <= "row5";
-- measures: max(unknown) | filter: id <= "row6";
-- measures: min(unknown) | filter: id <= "row7";
