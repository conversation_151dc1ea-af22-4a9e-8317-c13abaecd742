{"schema": {"name": "dataset", "fields": [{"name": "org_id", "tantivy": [{"name": "org_id", "type": {"str": {"stored": true, "fast": true, "tokenize": false}}}]}, {"name": "project_id", "tantivy": [{"name": "project_id", "type": {"str": {"stored": true, "fast": true, "tokenize": false}}}]}, {"name": "span_parents", "tantivy": [{"name": "span_parents", "type": {"json": {"stored": true, "fast": false, "tokenize": false}}}]}, {"name": "span_attributes", "tantivy": [{"name": "span_attributes", "type": {"json": {"stored": true, "fast": false, "tokenize": false}}}]}, {"name": "is_root", "tantivy": [{"name": "is_root", "type": {"bool": {"stored": true, "fast": true}}}]}, {"name": "tags", "tantivy": [{"name": "tags", "type": {"str": {"stored": true, "fast": true, "tokenize": false}}}]}, {"name": "origin", "tantivy": [{"name": "origin", "type": {"json": {"stored": true, "fast": false, "tokenize": false}}}]}, {"name": "dataset_id", "tantivy": [{"name": "dataset_id", "type": {"str": {"stored": true, "fast": true, "tokenize": false}}}]}, {"name": "input", "tantivy": [{"name": "input_json", "type": {"json": {"stored": true, "fast": false, "tokenize": false}}}, {"name": "input_text", "type": {"str": {"stored": false, "fast": false, "tokenize": true}}}]}, {"name": "expected", "tantivy": [{"name": "expected_json", "type": {"json": {"stored": true, "fast": false, "tokenize": false}}}, {"name": "expected_text", "type": {"str": {"stored": false, "fast": false, "tokenize": true}}}]}, {"name": "metadata", "tantivy": [{"name": "metadata_json", "type": {"json": {"stored": true, "fast": false, "tokenize": false}}}, {"name": "metadata_text", "type": {"str": {"stored": false, "fast": false, "tokenize": true}}}]}]}, "version": "0.1.0", "metadata_uri": "./index/base/metadata", "wal_uri": "./index/base/wal", "index_uri": "./index/base/tantivy"}