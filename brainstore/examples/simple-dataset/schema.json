{"$schema": "http://json-schema.org/draft-07/schema#", "required": ["experiment", "dataset", "prompt_session", "project_logs", "project", "project_prompts", "org_prompts", "project_functions", "org_functions"], "additionalProperties": false, "properties": {"experiment": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier for the experiment event. If you don't provide one, BrainTrust will generate one for you"}, "dataset_record_id": {"type": ["string", "null"], "description": "If the experiment is associated to a dataset, this is the event-level dataset id this experiment event is tied to"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the experiment (see the `version` parameter)"}, "created": {"type": "string", "format": "date-time", "description": "The timestamp the experiment event was created"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the experiment belongs under"}, "experiment_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the experiment"}, "input": {"description": "The arguments that uniquely define a test case (an arbitrary, JSON serializable object). Later on, Braintrust will use the `input` to know whether two test cases are the same between experiments, so they should not contain experiment-specific state. A simple rule of thumb is that if you run the same experiment twice, the `input` should be identical"}, "output": {"description": "The output of your application, including post-processing (an arbitrary, JSON serializable object), that allows you to determine whether the result is correct or not. For example, in an app that generates SQL queries, the `output` should be the _result_ of the SQL query generated by the model, not the query itself, because there may be multiple valid queries that answer a single question"}, "expected": {"description": "The ground truth value (an arbitrary, JSON serializable object) that you'd compare to `output` to determine if your `output` value is correct or not. Braintrust currently does not compare `output` to `expected` for you, since there are so many different ways to do that correctly. Instead, these values are just used to help you navigate your experiments while digging into analyses. However, we may later use these values to re-score outputs or fine-tune your models"}, "error": {"description": "The error that occurred, if any."}, "scores": {"anyOf": [{"type": "object", "additionalProperties": {"anyOf": [{"not": {}}, {"anyOf": [{"type": "number", "minimum": 0, "maximum": 1}, {"type": "null"}]}]}}, {"type": "null"}], "description": "A dictionary of numeric values (between 0 and 1) to log. The scores should give you a variety of signals that help you determine how accurate the outputs are compared to what you expect and diagnose failures. For example, a summarization app might have one score that tells you how accurate the summary is, and another that measures the word similarity between the generated and grouth truth summary. The word similarity score could help you determine whether the summarization was covering similar concepts or not. You can use these scores to help you sort, filter, and compare experiments"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "A dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags to log"}, "metrics": {"anyOf": [{"type": "object", "properties": {"start": {"type": ["number", "null"], "description": "A unix timestamp recording when the section of code which produced the experiment event started"}, "end": {"type": ["number", "null"], "description": "A unix timestamp recording when the section of code which produced the experiment event finished"}, "prompt_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The number of tokens in the prompt used to generate the experiment event (only set if this is an LLM span)"}, "completion_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The number of tokens in the completion generated by the model (only set if this is an LLM span)"}, "tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The total number of tokens in the input and output of the experiment event."}}, "additionalProperties": {}}, {"type": "null"}], "description": "Metrics are numerical measurements tracking the execution of the code that produced the experiment event. Use \"start\" and \"end\" to track the time span over which the experiment event was produced"}, "context": {"anyOf": [{"type": "object", "properties": {"caller_functionname": {"type": ["string", "null"], "description": "The function in code which created the experiment event"}, "caller_filename": {"type": ["string", "null"], "description": "Name of the file in code where the experiment event was created"}, "caller_lineno": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Line of code where the experiment event was created"}}, "additionalProperties": {}}, {"type": "null"}], "description": "Context is additional information about the code that produced the experiment event. It is essentially the textual counterpart to `metrics`. Use the `caller_*` attributes to track the location in code which produced the experiment event"}, "span_id": {"type": "string", "description": "A unique identifier used to link different experiment events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing"}, "span_parents": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "An array of the parent `span_ids` of this experiment event. This should be empty for the root span of a trace, and should most often contain just one parent element for subspans"}, "root_span_id": {"type": "string", "description": "The `span_id` of the root of the trace this experiment event belongs to"}, "span_attributes": {"anyOf": [{"type": "object", "properties": {"name": {"type": ["string", "null"], "description": "Name of the span, for display purposes only"}, "type": {"anyOf": [{"type": "string", "enum": ["llm", "score", "function", "eval", "task", "tool"]}, {"type": "null"}], "description": "Type of the span, for display purposes only"}}, "additionalProperties": {}}, {"type": "null"}], "description": "Human-identifying attributes of the span, such as name, type, etc."}, "is_root": {"type": ["boolean", "null"], "description": "Whether this span is a root span"}}, "required": ["id", "_xact_id", "created", "project_id", "experiment_id", "span_id", "root_span_id"], "additionalProperties": false}}, "dataset": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier for the dataset event. If you don't provide one, BrainTrust will generate one for you"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the dataset (see the `version` parameter)"}, "created": {"type": "string", "format": "date-time", "description": "The timestamp the dataset event was created"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the dataset belongs under"}, "dataset_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the dataset"}, "input": {"description": "The argument that uniquely define an input case (an arbitrary, JSON serializable object)"}, "expected": {"description": "The output of your application, including post-processing (an arbitrary, JSON serializable object)"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "A dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags to log"}, "span_id": {"type": "string", "description": "A unique identifier used to link different dataset events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing"}, "root_span_id": {"type": "string", "description": "The `span_id` of the root of the trace this dataset event belongs to"}, "is_root": {"type": ["boolean", "null"], "description": "Whether this span is a root span"}, "origin": {"anyOf": [{"type": "object", "properties": {"object_type": {"type": "string", "enum": ["experiment", "dataset", "prompt", "function", "prompt_session", "project_logs"], "description": "Type of the object the event is originating from."}, "object_id": {"type": "string", "format": "uuid", "description": "ID of the object the event is originating from."}, "id": {"type": "string", "description": "ID of the original event."}, "_xact_id": {"type": "string", "description": "Transaction ID of the original event."}}, "required": ["object_type", "object_id", "id", "_xact_id"], "additionalProperties": false}, {"type": "null"}], "description": "Indicates the event was copied from another object."}}, "required": ["id", "_xact_id", "created", "project_id", "dataset_id", "span_id", "root_span_id"], "additionalProperties": false}}, "prompt_session": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier for the prompt session event. If you don't provide one, BrainTrust will generate one for you"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the prompt session (see the `version` parameter)"}, "created": {"type": "string", "format": "date-time", "description": "The timestamp the prompt session event was created"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the prompt belongs under"}, "prompt_session_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the prompt"}, "prompt_session_data": {"description": "Data about the prompt session"}, "prompt_data": {"description": "Data about the prompt"}, "object_data": {"description": "Data about the mapped data"}, "completion": {"description": "Data about the completion"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags to log"}}, "required": ["id", "_xact_id", "created", "project_id", "prompt_session_id"], "additionalProperties": false}}, "project_logs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier for the project logs event. If you don't provide one, BrainTrust will generate one for you"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the project logs (see the `version` parameter)"}, "created": {"type": "string", "format": "date-time", "description": "The timestamp the project logs event was created"}, "org_id": {"type": "string", "format": "uuid", "description": "Unique id for the organization that the project belongs under"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project"}, "log_id": {"type": "string", "const": "g", "description": "A literal 'g' which identifies the log as a project log"}, "input": {"description": "The arguments that uniquely define a user input (an arbitrary, JSON serializable object)."}, "output": {"description": "The output of your application, including post-processing (an arbitrary, JSON serializable object), that allows you to determine whether the result is correct or not. For example, in an app that generates SQL queries, the `output` should be the _result_ of the SQL query generated by the model, not the query itself, because there may be multiple valid queries that answer a single question."}, "expected": {"description": "The ground truth value (an arbitrary, JSON serializable object) that you'd compare to `output` to determine if your `output` value is correct or not. Braintrust currently does not compare `output` to `expected` for you, since there are so many different ways to do that correctly. Instead, these values are just used to help you navigate while digging into analyses. However, we may later use these values to re-score outputs or fine-tune your models."}, "error": {"description": "The error that occurred, if any."}, "scores": {"anyOf": [{"type": "object", "additionalProperties": {"anyOf": [{"not": {}}, {"anyOf": [{"type": "number", "minimum": 0, "maximum": 1}, {"type": "null"}]}]}}, {"type": "null"}], "description": "A dictionary of numeric values (between 0 and 1) to log. The scores should give you a variety of signals that help you determine how accurate the outputs are compared to what you expect and diagnose failures. For example, a summarization app might have one score that tells you how accurate the summary is, and another that measures the word similarity between the generated and grouth truth summary. The word similarity score could help you determine whether the summarization was covering similar concepts or not. You can use these scores to help you sort, filter, and compare logs."}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "A dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags to log"}, "metrics": {"anyOf": [{"type": "object", "properties": {"start": {"type": ["number", "null"], "description": "A unix timestamp recording when the section of code which produced the project logs event started"}, "end": {"type": ["number", "null"], "description": "A unix timestamp recording when the section of code which produced the project logs event finished"}, "prompt_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The number of tokens in the prompt used to generate the project logs event (only set if this is an LLM span)"}, "completion_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The number of tokens in the completion generated by the model (only set if this is an LLM span)"}, "tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The total number of tokens in the input and output of the project logs event."}}, "additionalProperties": {}}, {"type": "null"}], "description": "Metrics are numerical measurements tracking the execution of the code that produced the project logs event. Use \"start\" and \"end\" to track the time span over which the project logs event was produced"}, "context": {"anyOf": [{"type": "object", "properties": {"caller_functionname": {"type": ["string", "null"], "description": "The function in code which created the project logs event"}, "caller_filename": {"type": ["string", "null"], "description": "Name of the file in code where the project logs event was created"}, "caller_lineno": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Line of code where the project logs event was created"}}, "additionalProperties": {}}, {"type": "null"}], "description": "Context is additional information about the code that produced the project logs event. It is essentially the textual counterpart to `metrics`. Use the `caller_*` attributes to track the location in code which produced the project logs event"}, "span_id": {"type": "string", "description": "A unique identifier used to link different project logs events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing"}, "span_parents": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "An array of the parent `span_ids` of this project logs event. This should be empty for the root span of a trace, and should most often contain just one parent element for subspans"}, "root_span_id": {"type": "string", "description": "The `span_id` of the root of the trace this project logs event belongs to"}, "is_root": {"type": ["boolean", "null"], "description": "Whether this span is a root span"}, "span_attributes": {"anyOf": [{"type": "object", "properties": {"name": {"type": ["string", "null"], "description": "Name of the span, for display purposes only"}, "type": {"anyOf": [{"type": "string", "enum": ["llm", "score", "function", "eval", "task", "tool"]}, {"type": "null"}], "description": "Type of the span, for display purposes only"}}, "additionalProperties": {}}, {"type": "null"}], "description": "Human-identifying attributes of the span, such as name, type, etc."}, "origin": {"anyOf": [{"type": "object", "properties": {"object_type": {"type": "string", "enum": ["experiment", "dataset", "prompt", "function", "prompt_session", "project_logs"], "description": "Type of the object the event is originating from."}, "object_id": {"type": "string", "format": "uuid", "description": "ID of the object the event is originating from."}, "id": {"type": "string", "description": "ID of the original event."}, "_xact_id": {"type": "string", "description": "Transaction ID of the original event."}}, "required": ["object_type", "object_id", "id", "_xact_id"], "additionalProperties": false}, {"type": "null"}], "description": "Indicates the event was copied from another object."}}, "required": ["id", "_xact_id", "created", "org_id", "project_id", "log_id", "span_id", "root_span_id"], "additionalProperties": false}}, "project": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "A unique identifier for the experiment event. If you don't provide one, BrainTrust will generate one for you"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the experiment (see the `version` parameter)"}, "created": {"type": "string", "format": "date-time", "description": "The timestamp the experiment event was created"}, "org_id": {"type": "string", "format": "uuid", "description": "Unique id for the organization that the project belongs under"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the experiment belongs under"}, "log_id": {"type": "string", "const": "g", "description": "A literal 'g' which identifies the log as a project log"}, "input": {"description": "The arguments that uniquely define a test case (an arbitrary, JSON serializable object). Later on, Braintrust will use the `input` to know whether two test cases are the same between experiments, so they should not contain experiment-specific state. A simple rule of thumb is that if you run the same experiment twice, the `input` should be identical"}, "output": {"description": "The output of your application, including post-processing (an arbitrary, JSON serializable object), that allows you to determine whether the result is correct or not. For example, in an app that generates SQL queries, the `output` should be the _result_ of the SQL query generated by the model, not the query itself, because there may be multiple valid queries that answer a single question"}, "expected": {"description": "The ground truth value (an arbitrary, JSON serializable object) that you'd compare to `output` to determine if your `output` value is correct or not. Braintrust currently does not compare `output` to `expected` for you, since there are so many different ways to do that correctly. Instead, these values are just used to help you navigate your experiments while digging into analyses. However, we may later use these values to re-score outputs or fine-tune your models"}, "error": {"description": "The error that occurred, if any."}, "scores": {"anyOf": [{"type": "object", "additionalProperties": {"anyOf": [{"not": {}}, {"anyOf": [{"type": "number", "minimum": 0, "maximum": 1}, {"type": "null"}]}]}}, {"type": "null"}], "description": "A dictionary of numeric values (between 0 and 1) to log. The scores should give you a variety of signals that help you determine how accurate the outputs are compared to what you expect and diagnose failures. For example, a summarization app might have one score that tells you how accurate the summary is, and another that measures the word similarity between the generated and grouth truth summary. The word similarity score could help you determine whether the summarization was covering similar concepts or not. You can use these scores to help you sort, filter, and compare experiments"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "A dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags to log"}, "metrics": {"anyOf": [{"type": "object", "properties": {"start": {"type": ["number", "null"], "description": "A unix timestamp recording when the section of code which produced the experiment event started"}, "end": {"type": ["number", "null"], "description": "A unix timestamp recording when the section of code which produced the experiment event finished"}, "prompt_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The number of tokens in the prompt used to generate the experiment event (only set if this is an LLM span)"}, "completion_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The number of tokens in the completion generated by the model (only set if this is an LLM span)"}, "tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "The total number of tokens in the input and output of the experiment event."}}, "additionalProperties": {}}, {"type": "null"}], "description": "Metrics are numerical measurements tracking the execution of the code that produced the experiment event. Use \"start\" and \"end\" to track the time span over which the experiment event was produced"}, "context": {"anyOf": [{"type": "object", "properties": {"caller_functionname": {"type": ["string", "null"], "description": "The function in code which created the experiment event"}, "caller_filename": {"type": ["string", "null"], "description": "Name of the file in code where the experiment event was created"}, "caller_lineno": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Line of code where the experiment event was created"}}, "additionalProperties": {}}, {"type": "null"}], "description": "Context is additional information about the code that produced the experiment event. It is essentially the textual counterpart to `metrics`. Use the `caller_*` attributes to track the location in code which produced the experiment event"}, "span_id": {"type": "string", "description": "A unique identifier used to link different experiment events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing"}, "span_parents": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "An array of the parent `span_ids` of this experiment event. This should be empty for the root span of a trace, and should most often contain just one parent element for subspans"}, "root_span_id": {"type": "string", "description": "The `span_id` of the root of the trace this experiment event belongs to"}, "is_root": {"type": ["boolean", "null"], "description": "Whether this span is a root span"}, "span_attributes": {"anyOf": [{"type": "object", "properties": {"name": {"type": ["string", "null"], "description": "Name of the span, for display purposes only"}, "type": {"anyOf": [{"type": "string", "enum": ["llm", "score", "function", "eval", "task", "tool"]}, {"type": "null"}], "description": "Type of the span, for display purposes only"}}, "additionalProperties": {}}, {"type": "null"}], "description": "Human-identifying attributes of the span, such as name, type, etc."}, "origin": {"anyOf": [{"type": "object", "properties": {"object_type": {"type": "string", "enum": ["experiment", "dataset", "prompt", "function", "prompt_session", "project_logs"], "description": "Type of the object the event is originating from."}, "object_id": {"type": "string", "format": "uuid", "description": "ID of the object the event is originating from."}, "id": {"type": "string", "description": "ID of the original event."}, "_xact_id": {"type": "string", "description": "Transaction ID of the original event."}}, "required": ["object_type", "object_id", "id", "_xact_id"], "additionalProperties": false}, {"type": "null"}], "description": "Indicates the event was copied from another object."}, "dataset_record_id": {"type": ["string", "null"], "description": "If the experiment is associated to a dataset, this is the event-level dataset id this experiment event is tied to"}, "experiment_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the experiment"}}, "required": ["id", "_xact_id", "created", "org_id", "project_id", "log_id", "span_id", "root_span_id", "experiment_id"], "additionalProperties": false}}, "project_prompts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the prompt"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the prompt (see the `version` parameter)"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the prompt belongs under"}, "log_id": {"type": "string", "const": "p", "description": "A literal 'p' which identifies the object as a project prompt"}, "org_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the organization"}, "name": {"type": "string", "description": "Name of the prompt"}, "slug": {"type": "string", "description": "Unique identifier for the prompt"}, "description": {"type": ["string", "null"], "description": "Textual description of the prompt"}, "created": {"anyOf": [{"type": "string", "format": "date-time", "description": "Creation date"}, {"type": "null"}], "description": "Date of prompt creation"}, "prompt_data": {"anyOf": [{"type": "object", "properties": {"prompt": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "completion"}, "content": {"type": "string"}}, "required": ["type", "content"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "chat"}, "messages": {"type": "array", "items": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "system"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"anyOf": [{"type": "string", "default": ""}, {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"text": {"type": "string", "default": ""}, "type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"image_url": {"type": "object", "properties": {"url": {"type": "string"}, "detail": {"type": "string", "enum": ["auto", "low", "high"]}}, "required": ["url"], "additionalProperties": false}, "type": {"type": "string", "const": "image_url"}}, "required": ["image_url", "type"], "additionalProperties": false}]}}]}, "role": {"type": "string", "const": "user"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"role": {"type": "string", "const": "assistant"}, "content": {"type": ["string", "null"]}, "function_call": {"anyOf": [{"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, {"type": "null"}]}, "name": {"type": ["string", "null"]}, "tool_calls": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "function": {"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, "type": {"type": "string", "const": "function"}}, "required": ["id", "function", "type"], "additionalProperties": false}}, {"type": "null"}]}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "tool"}, "tool_call_id": {"type": "string", "default": ""}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "name": {"type": "string"}, "role": {"type": "string", "const": "function"}}, "required": ["name", "role"], "additionalProperties": false}]}, {"type": "object", "properties": {"role": {"type": "string", "enum": ["model"]}, "content": {"type": ["string", "null"]}}, "required": ["role"], "additionalProperties": false}]}}, "tools": {"type": "string"}}, "required": ["type", "messages"], "additionalProperties": false}]}, {"type": "null"}]}, "options": {"anyOf": [{"type": "object", "properties": {"model": {"type": "string"}, "params": {"anyOf": [{"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "max_tokens": {"type": "number"}, "frequency_penalty": {"type": "number"}, "presence_penalty": {"type": "number"}, "response_format": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "json_object"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "json_schema"}, "json_schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "schema": {"type": "object", "additionalProperties": {}}, "strict": {"type": ["boolean", "null"]}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "json_schema"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}]}, {"type": "null"}]}, "tool_choice": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "string", "const": "required"}, {"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "function": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "function"], "additionalProperties": false}]}, "function_call": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}]}, "n": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "max_tokens": {"type": "number"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "top_k": {"type": "number"}, "stop_sequences": {"type": "array", "items": {"type": "string"}}, "max_tokens_to_sample": {"type": "number", "description": "This is a legacy parameter that should not be used."}}, "required": ["max_tokens", "temperature"], "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "maxOutputTokens": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}}, "additionalProperties": true}]}, "position": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}, "parser": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "llm_classifier"}, "use_cot": {"type": "boolean"}, "choice_scores": {"type": "object", "additionalProperties": {"type": "number", "minimum": 0, "maximum": 1}}}, "required": ["type", "use_cot", "choice_scores"], "additionalProperties": false}, {"type": "null"}]}, "tool_functions": {"anyOf": [{"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "id": {"type": "string"}}, "required": ["type", "id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "global"}, "name": {"type": "string"}}, "required": ["type", "name"], "additionalProperties": false}]}}, {"type": "null"}]}, "origin": {"anyOf": [{"type": "object", "properties": {"prompt_id": {"type": "string"}, "project_id": {"type": "string"}, "prompt_version": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}}, "additionalProperties": false}, {"type": "null"}], "description": "The prompt, model, and its parameters"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags for the prompt"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "User-controlled metadata about the prompt"}, "function_type": {"anyOf": [{"type": "string", "enum": ["llm", "scorer", "task", "tool"]}, {"type": "null"}]}}, "required": ["id", "_xact_id", "project_id", "log_id", "org_id", "name", "slug"], "additionalProperties": false}}, "org_prompts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the prompt"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the prompt (see the `version` parameter)"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the prompt belongs under"}, "log_id": {"type": "string", "const": "p", "description": "A literal 'p' which identifies the object as a project prompt"}, "org_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the organization"}, "name": {"type": "string", "description": "Name of the prompt"}, "slug": {"type": "string", "description": "Unique identifier for the prompt"}, "description": {"type": ["string", "null"], "description": "Textual description of the prompt"}, "created": {"anyOf": [{"type": "string", "format": "date-time", "description": "Creation date"}, {"type": "null"}], "description": "Date of prompt creation"}, "prompt_data": {"anyOf": [{"type": "object", "properties": {"prompt": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "completion"}, "content": {"type": "string"}}, "required": ["type", "content"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "chat"}, "messages": {"type": "array", "items": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "system"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"anyOf": [{"type": "string", "default": ""}, {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"text": {"type": "string", "default": ""}, "type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"image_url": {"type": "object", "properties": {"url": {"type": "string"}, "detail": {"type": "string", "enum": ["auto", "low", "high"]}}, "required": ["url"], "additionalProperties": false}, "type": {"type": "string", "const": "image_url"}}, "required": ["image_url", "type"], "additionalProperties": false}]}}]}, "role": {"type": "string", "const": "user"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"role": {"type": "string", "const": "assistant"}, "content": {"type": ["string", "null"]}, "function_call": {"anyOf": [{"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, {"type": "null"}]}, "name": {"type": ["string", "null"]}, "tool_calls": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "function": {"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, "type": {"type": "string", "const": "function"}}, "required": ["id", "function", "type"], "additionalProperties": false}}, {"type": "null"}]}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "tool"}, "tool_call_id": {"type": "string", "default": ""}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "name": {"type": "string"}, "role": {"type": "string", "const": "function"}}, "required": ["name", "role"], "additionalProperties": false}]}, {"type": "object", "properties": {"role": {"type": "string", "enum": ["model"]}, "content": {"type": ["string", "null"]}}, "required": ["role"], "additionalProperties": false}]}}, "tools": {"type": "string"}}, "required": ["type", "messages"], "additionalProperties": false}]}, {"type": "null"}]}, "options": {"anyOf": [{"type": "object", "properties": {"model": {"type": "string"}, "params": {"anyOf": [{"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "max_tokens": {"type": "number"}, "frequency_penalty": {"type": "number"}, "presence_penalty": {"type": "number"}, "response_format": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "json_object"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "json_schema"}, "json_schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "schema": {"type": "object", "additionalProperties": {}}, "strict": {"type": ["boolean", "null"]}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "json_schema"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}]}, {"type": "null"}]}, "tool_choice": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "string", "const": "required"}, {"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "function": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "function"], "additionalProperties": false}]}, "function_call": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}]}, "n": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "max_tokens": {"type": "number"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "top_k": {"type": "number"}, "stop_sequences": {"type": "array", "items": {"type": "string"}}, "max_tokens_to_sample": {"type": "number", "description": "This is a legacy parameter that should not be used."}}, "required": ["max_tokens", "temperature"], "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "maxOutputTokens": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}}, "additionalProperties": true}]}, "position": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}, "parser": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "llm_classifier"}, "use_cot": {"type": "boolean"}, "choice_scores": {"type": "object", "additionalProperties": {"type": "number", "minimum": 0, "maximum": 1}}}, "required": ["type", "use_cot", "choice_scores"], "additionalProperties": false}, {"type": "null"}]}, "tool_functions": {"anyOf": [{"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "id": {"type": "string"}}, "required": ["type", "id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "global"}, "name": {"type": "string"}}, "required": ["type", "name"], "additionalProperties": false}]}}, {"type": "null"}]}, "origin": {"anyOf": [{"type": "object", "properties": {"prompt_id": {"type": "string"}, "project_id": {"type": "string"}, "prompt_version": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}}, "additionalProperties": false}, {"type": "null"}], "description": "The prompt, model, and its parameters"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags for the prompt"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "User-controlled metadata about the prompt"}, "function_type": {"anyOf": [{"type": "string", "enum": ["llm", "scorer", "task", "tool"]}, {"type": "null"}]}}, "required": ["id", "_xact_id", "project_id", "log_id", "org_id", "name", "slug"], "additionalProperties": false}}, "project_functions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the prompt"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the prompt (see the `version` parameter)"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the prompt belongs under"}, "log_id": {"type": "string", "const": "p", "description": "A literal 'p' which identifies the object as a project prompt"}, "org_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the organization"}, "name": {"type": "string", "description": "Name of the prompt"}, "slug": {"type": "string", "description": "Unique identifier for the prompt"}, "description": {"type": ["string", "null"], "description": "Textual description of the prompt"}, "created": {"anyOf": [{"type": "string", "format": "date-time", "description": "Creation date"}, {"type": "null"}], "description": "Date of prompt creation"}, "prompt_data": {"anyOf": [{"type": "object", "properties": {"prompt": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "completion"}, "content": {"type": "string"}}, "required": ["type", "content"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "chat"}, "messages": {"type": "array", "items": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "system"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"anyOf": [{"type": "string", "default": ""}, {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"text": {"type": "string", "default": ""}, "type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"image_url": {"type": "object", "properties": {"url": {"type": "string"}, "detail": {"type": "string", "enum": ["auto", "low", "high"]}}, "required": ["url"], "additionalProperties": false}, "type": {"type": "string", "const": "image_url"}}, "required": ["image_url", "type"], "additionalProperties": false}]}}]}, "role": {"type": "string", "const": "user"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"role": {"type": "string", "const": "assistant"}, "content": {"type": ["string", "null"]}, "function_call": {"anyOf": [{"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, {"type": "null"}]}, "name": {"type": ["string", "null"]}, "tool_calls": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "function": {"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, "type": {"type": "string", "const": "function"}}, "required": ["id", "function", "type"], "additionalProperties": false}}, {"type": "null"}]}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "tool"}, "tool_call_id": {"type": "string", "default": ""}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "name": {"type": "string"}, "role": {"type": "string", "const": "function"}}, "required": ["name", "role"], "additionalProperties": false}]}, {"type": "object", "properties": {"role": {"type": "string", "enum": ["model"]}, "content": {"type": ["string", "null"]}}, "required": ["role"], "additionalProperties": false}]}}, "tools": {"type": "string"}}, "required": ["type", "messages"], "additionalProperties": false}]}, {"type": "null"}]}, "options": {"anyOf": [{"type": "object", "properties": {"model": {"type": "string"}, "params": {"anyOf": [{"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "max_tokens": {"type": "number"}, "frequency_penalty": {"type": "number"}, "presence_penalty": {"type": "number"}, "response_format": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "json_object"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "json_schema"}, "json_schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "schema": {"type": "object", "additionalProperties": {}}, "strict": {"type": ["boolean", "null"]}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "json_schema"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}]}, {"type": "null"}]}, "tool_choice": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "string", "const": "required"}, {"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "function": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "function"], "additionalProperties": false}]}, "function_call": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}]}, "n": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "max_tokens": {"type": "number"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "top_k": {"type": "number"}, "stop_sequences": {"type": "array", "items": {"type": "string"}}, "max_tokens_to_sample": {"type": "number", "description": "This is a legacy parameter that should not be used."}}, "required": ["max_tokens", "temperature"], "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "maxOutputTokens": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}}, "additionalProperties": true}]}, "position": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}, "parser": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "llm_classifier"}, "use_cot": {"type": "boolean"}, "choice_scores": {"type": "object", "additionalProperties": {"type": "number", "minimum": 0, "maximum": 1}}}, "required": ["type", "use_cot", "choice_scores"], "additionalProperties": false}, {"type": "null"}]}, "tool_functions": {"anyOf": [{"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "id": {"type": "string"}}, "required": ["type", "id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "global"}, "name": {"type": "string"}}, "required": ["type", "name"], "additionalProperties": false}]}}, {"type": "null"}]}, "origin": {"anyOf": [{"type": "object", "properties": {"prompt_id": {"type": "string"}, "project_id": {"type": "string"}, "prompt_version": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}}, "additionalProperties": false}, {"type": "null"}], "description": "The prompt, model, and its parameters"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags for the prompt"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "User-controlled metadata about the prompt"}, "function_type": {"anyOf": [{"type": "string", "enum": ["llm", "scorer", "task", "tool"]}, {"type": "null"}]}, "function_data": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "prompt"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "code"}, "data": {"anyOf": [{"allOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bundle"}}, "required": ["type"]}, {"type": "object", "properties": {"runtime_context": {"type": "object", "properties": {"runtime": {"type": "string", "enum": ["node", "python"]}, "version": {"type": "string"}}, "required": ["runtime", "version"], "additionalProperties": false}, "location": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "experiment"}, "eval_name": {"type": "string"}, "position": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "task"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "scorer"}, "index": {"type": "integer", "minimum": 0}}, "required": ["type", "index"], "additionalProperties": false}]}}, "required": ["type", "eval_name", "position"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "index": {"type": "integer", "minimum": 0}}, "required": ["type", "index"], "additionalProperties": false}]}, "bundle_id": {"type": "string"}, "preview": {"type": ["string", "null"], "description": "A preview of the code"}}, "required": ["runtime_context", "location", "bundle_id"]}]}, {"type": "object", "properties": {"type": {"type": "string", "const": "inline"}, "runtime_context": {"type": "object", "properties": {"runtime": {"type": "string", "enum": ["node", "python"]}, "version": {"type": "string"}}, "required": ["runtime", "version"], "additionalProperties": false}, "code": {"type": "string"}}, "required": ["type", "runtime_context", "code"], "additionalProperties": false}]}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "global"}, "name": {"type": "string"}}, "required": ["type", "name"], "additionalProperties": false}]}, "origin": {"anyOf": [{"type": "object", "properties": {"object_type": {"type": "string", "enum": ["organization", "project", "experiment", "dataset", "prompt", "prompt_session", "group", "role", "org_member", "project_log", "org_project"], "description": "The object type that the ACL applies to"}, "object_id": {"type": "string", "format": "uuid", "description": "Id of the object the function is originating from"}, "internal": {"type": ["boolean", "null"], "description": "The function exists for internal purposes and should not be displayed in the list of functions."}}, "required": ["object_type", "object_id"], "additionalProperties": false}, {"type": "null"}]}, "function_schema": {"anyOf": [{"type": "object", "properties": {"parameters": {}, "returns": {}}, "additionalProperties": false}, {"type": "null"}], "description": "JSON schema for the function's parameters and return type"}}, "required": ["id", "_xact_id", "project_id", "log_id", "org_id", "name", "slug", "function_data"], "additionalProperties": false}}, "org_functions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the prompt"}, "_xact_id": {"type": "string", "description": "The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the prompt (see the `version` parameter)"}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project that the prompt belongs under"}, "log_id": {"type": "string", "const": "p", "description": "A literal 'p' which identifies the object as a project prompt"}, "org_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the organization"}, "name": {"type": "string", "description": "Name of the prompt"}, "slug": {"type": "string", "description": "Unique identifier for the prompt"}, "description": {"type": ["string", "null"], "description": "Textual description of the prompt"}, "created": {"anyOf": [{"type": "string", "format": "date-time", "description": "Creation date"}, {"type": "null"}], "description": "Date of prompt creation"}, "prompt_data": {"anyOf": [{"type": "object", "properties": {"prompt": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "completion"}, "content": {"type": "string"}}, "required": ["type", "content"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "chat"}, "messages": {"type": "array", "items": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "system"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"anyOf": [{"type": "string", "default": ""}, {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"text": {"type": "string", "default": ""}, "type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"image_url": {"type": "object", "properties": {"url": {"type": "string"}, "detail": {"type": "string", "enum": ["auto", "low", "high"]}}, "required": ["url"], "additionalProperties": false}, "type": {"type": "string", "const": "image_url"}}, "required": ["image_url", "type"], "additionalProperties": false}]}}]}, "role": {"type": "string", "const": "user"}, "name": {"type": "string"}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"role": {"type": "string", "const": "assistant"}, "content": {"type": ["string", "null"]}, "function_call": {"anyOf": [{"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, {"type": "null"}]}, "name": {"type": ["string", "null"]}, "tool_calls": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "function": {"type": "object", "properties": {"arguments": {"type": "string"}, "name": {"type": "string"}}, "required": ["arguments", "name"], "additionalProperties": false}, "type": {"type": "string", "const": "function"}}, "required": ["id", "function", "type"], "additionalProperties": false}}, {"type": "null"}]}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "role": {"type": "string", "const": "tool"}, "tool_call_id": {"type": "string", "default": ""}}, "required": ["role"], "additionalProperties": false}, {"type": "object", "properties": {"content": {"type": "string", "default": ""}, "name": {"type": "string"}, "role": {"type": "string", "const": "function"}}, "required": ["name", "role"], "additionalProperties": false}]}, {"type": "object", "properties": {"role": {"type": "string", "enum": ["model"]}, "content": {"type": ["string", "null"]}}, "required": ["role"], "additionalProperties": false}]}}, "tools": {"type": "string"}}, "required": ["type", "messages"], "additionalProperties": false}]}, {"type": "null"}]}, "options": {"anyOf": [{"type": "object", "properties": {"model": {"type": "string"}, "params": {"anyOf": [{"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "max_tokens": {"type": "number"}, "frequency_penalty": {"type": "number"}, "presence_penalty": {"type": "number"}, "response_format": {"anyOf": [{"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "json_object"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "json_schema"}, "json_schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "schema": {"type": "object", "additionalProperties": {}}, "strict": {"type": ["boolean", "null"]}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "json_schema"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "text"}}, "required": ["type"], "additionalProperties": false}]}, {"type": "null"}]}, "tool_choice": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "string", "const": "required"}, {"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "function": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["type", "function"], "additionalProperties": false}]}, "function_call": {"anyOf": [{"type": "string", "const": "auto"}, {"type": "string", "const": "none"}, {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}]}, "n": {"type": "number"}, "stop": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "max_tokens": {"type": "number"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "top_k": {"type": "number"}, "stop_sequences": {"type": "array", "items": {"type": "string"}}, "max_tokens_to_sample": {"type": "number", "description": "This is a legacy parameter that should not be used."}}, "required": ["max_tokens", "temperature"], "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "maxOutputTokens": {"type": "number"}, "topP": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}, "temperature": {"type": "number"}, "topK": {"type": "number"}}, "additionalProperties": true}, {"type": "object", "properties": {"use_cache": {"type": "boolean"}}, "additionalProperties": true}]}, "position": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}, "parser": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "llm_classifier"}, "use_cot": {"type": "boolean"}, "choice_scores": {"type": "object", "additionalProperties": {"type": "number", "minimum": 0, "maximum": 1}}}, "required": ["type", "use_cot", "choice_scores"], "additionalProperties": false}, {"type": "null"}]}, "tool_functions": {"anyOf": [{"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "id": {"type": "string"}}, "required": ["type", "id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "global"}, "name": {"type": "string"}}, "required": ["type", "name"], "additionalProperties": false}]}}, {"type": "null"}]}, "origin": {"anyOf": [{"type": "object", "properties": {"prompt_id": {"type": "string"}, "project_id": {"type": "string"}, "prompt_version": {"type": "string"}}, "additionalProperties": false}, {"type": "null"}]}}, "additionalProperties": false}, {"type": "null"}], "description": "The prompt, model, and its parameters"}, "tags": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "A list of tags for the prompt"}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "null"}], "description": "User-controlled metadata about the prompt"}, "function_type": {"anyOf": [{"type": "string", "enum": ["llm", "scorer", "task", "tool"]}, {"type": "null"}]}, "function_data": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "prompt"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "code"}, "data": {"anyOf": [{"allOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bundle"}}, "required": ["type"]}, {"type": "object", "properties": {"runtime_context": {"type": "object", "properties": {"runtime": {"type": "string", "enum": ["node", "python"]}, "version": {"type": "string"}}, "required": ["runtime", "version"], "additionalProperties": false}, "location": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "experiment"}, "eval_name": {"type": "string"}, "position": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "task"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "scorer"}, "index": {"type": "integer", "minimum": 0}}, "required": ["type", "index"], "additionalProperties": false}]}}, "required": ["type", "eval_name", "position"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "function"}, "index": {"type": "integer", "minimum": 0}}, "required": ["type", "index"], "additionalProperties": false}]}, "bundle_id": {"type": "string"}, "preview": {"type": ["string", "null"], "description": "A preview of the code"}}, "required": ["runtime_context", "location", "bundle_id"]}]}, {"type": "object", "properties": {"type": {"type": "string", "const": "inline"}, "runtime_context": {"type": "object", "properties": {"runtime": {"type": "string", "enum": ["node", "python"]}, "version": {"type": "string"}}, "required": ["runtime", "version"], "additionalProperties": false}, "code": {"type": "string"}}, "required": ["type", "runtime_context", "code"], "additionalProperties": false}]}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "global"}, "name": {"type": "string"}}, "required": ["type", "name"], "additionalProperties": false}]}, "origin": {"anyOf": [{"type": "object", "properties": {"object_type": {"type": "string", "enum": ["organization", "project", "experiment", "dataset", "prompt", "prompt_session", "group", "role", "org_member", "project_log", "org_project"], "description": "The object type that the ACL applies to"}, "object_id": {"type": "string", "format": "uuid", "description": "Id of the object the function is originating from"}, "internal": {"type": ["boolean", "null"], "description": "The function exists for internal purposes and should not be displayed in the list of functions."}}, "required": ["object_type", "object_id"], "additionalProperties": false}, {"type": "null"}]}, "function_schema": {"anyOf": [{"type": "object", "properties": {"parameters": {}, "returns": {}}, "additionalProperties": false}, {"type": "null"}], "description": "JSON schema for the function's parameters and return type"}}, "required": ["id", "_xact_id", "project_id", "log_id", "org_id", "name", "slug", "function_data"], "additionalProperties": false}}}, "type": "object"}