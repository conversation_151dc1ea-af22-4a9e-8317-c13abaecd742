#!/bin/bash

# To run the benchmark, create a `.env.local` with the following variables:
# * BRAINSTORE_PROFILING_API_KEY=<polarsignals api key>

# You then need to populate a big segment by running:
#   ./run_bench.sh insert --num-docs 100000 --avg-doc-size 1mb --batch-size 1000
# Once that completes, you can run
#   ./run_bench.sh merge
# as many times as you want.

# It can be quite useful to analyze the brainstore process with perf while running the benchmark.
# You can do this by running:
#   sudo perf record -g -p <brainstore pid>
# and then analyzing the results with:
#   sudo perf report


# Read AWS credentials from ~/.aws/credentials
if [ -f ~/.aws/credentials ]; then
    # Extract the default profile credentials
    AWS_ACCESS_KEY_ID=$(grep -A3 "\[default\]" ~/.aws/credentials | grep "aws_access_key_id" | cut -d "=" -f2 | tr -d " ")
    AWS_SECRET_ACCESS_KEY=$(grep -A3 "\[default\]" ~/.aws/credentials | grep "aws_secret_access_key" | cut -d "=" -f2 | tr -d " ")
    AWS_REGION=$(grep -A3 "\[default\]" ~/.aws/credentials | grep "region" | cut -d "=" -f2 | tr -d " ")

    # Export the AWS environment variables
    export AWS_ACCESS_KEY_ID
    export AWS_SECRET_ACCESS_KEY
    export AWS_REGION

    echo "AWS credentials have been set from ~/.aws/credentials"
else
    echo "Error: ~/.aws/credentials file not found"
    exit 1
fi

# Read variables from .env.local if it exists
if [ -f .env.local ]; then
    echo "Reading variables from .env.local"
    while IFS='=' read -r key value
    do
        # Remove leading/trailing whitespace and quotes from the value
        value=$(echo "$value" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' -e 's/^"//' -e 's/"$//')
        # Export the variable
        export "$key=$value"
        echo "Exported $key"
    done < .env.local
else
    echo "Note: .env.local file not found, skipping"
fi


# Find the phase argument and collect other arguments
PHASE=""
OTHER_ARGS=()

for arg in "$@"; do
    case $arg in
        insert|merge|both)
            if [ -n "$PHASE" ]; then
                echo "Error: Multiple phase arguments provided. Use only one of: insert, merge, or both"
                exit 1
            fi
            PHASE=$arg
            ;;
        *)
            OTHER_ARGS+=("$arg")
            ;;
    esac
done

# Check if phase was provided
if [ -z "$PHASE" ]; then
    echo "Usage: $0 [options] <phase> [more options]"
    echo "  phase: insert, merge, or both"
    echo "  Phase can appear anywhere in the arguments"
    exit 1
fi

# Set the phases flag based on the argument
case $PHASE in
    insert)
        PHASES_FLAG="--phases insert"
        ;;
    merge)
        PHASES_FLAG="--phases merge"
        ;;
    both)
        PHASES_FLAG="--phases insert,merge"
        ;;
    *)
        echo "Error: Invalid phase '$PHASE'. Must be 'insert', 'merge', or 'both'"
        exit 1
        ;;
esac

# Remove index directory if phase includes insert
if [ "$PHASE" = "insert" ] || [ "$PHASE" = "both" ]; then
    rm -rf /mnt/tmp/bench/index
    # aws s3 rm --recursive s3://brainstore-load-test/merge-bench/index
else
    rm -r /mnt/tmp/bench/index && cp -r /mnt/tmp/bench/index-backup /mnt/tmp/bench/index
fi

ulimit -n 100000
export BRAINSTORE_INDEX_WRITER_FORCE_NO_MERGES=true
export BRAINSTORE_NUM_THREADS_PER_INDEX_OPERATION=32

/usr/bin/time -v cargo run --release -F enable_memprof --bin brainstore -- bench merge-large-docs \
    --cache-dir /mnt/tmp/brainstore -v -c test-brainstore-s3.yaml \
    $PHASES_FLAG \
    "${OTHER_ARGS[@]}"


if [ "$PHASE" = "insert" ]; then
    rm -rf /mnt/tmp/bench/index/base/wal/object-store-objects /mnt/tmp/bench/index-backup
    cp -r /mnt/tmp/bench/index /mnt/tmp/bench/index-backup
fi
