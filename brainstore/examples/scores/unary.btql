-- selects
select: null, -null, not null;
select: not true, not false;
select: not scores.foo;
select: -scores.bar;
select: -counts.foo;
select: scores.bar is null;
select: scores.bar is not null;
select: counts.bar is null;
select: counts.bar is not null;
select: scores.foo = 0.25;
select: counts.foo = -7;
select: scores.foo > -1;
select: not (scores.foo = 0.75) as value;
select: not (counts.foo = -7) as value;
select: not (counts.foo < 10) as value;

-- filters
select: -2 | filter: not true;
select: -2 | filter: not false;
select: * | filter: not (scores.foo = 0);
select: * | filter: not (scores.bar = 0.5);

select: * | filter: -scores.foo;
select: * | filter: -scores.bar;

-- isnull, isnotnull
select: * | filter: null is null;
select: * | filter: scores.bar is null;
select: * | filter: scores.bar is not null;
select: * | filter: counts.bar is null;
select: * | filter: counts.bar is not null;

-- combos
select: * | filter: not not false;
select: * | filter: not (scores.bar is null);
select: * | filter: not (scores.bar is not null);

-- json nulls
select: metadata.nullable | filter: metadata.nullable is null;
select: metadata.nullable | filter: metadata.nullable is not null;
