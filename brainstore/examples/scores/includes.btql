select: id, tags includes 'a', not tags includes 'a', tags includes ['d'], tags includes ['a', 'b'], tags includes ['b', 'a'], tags includes ['a', 'b', 'c'];

select: id | filter: tags includes 'a';
select: id | filter: tags includes 'b';
select: id | filter: tags includes 'c';
select: id | filter: tags includes ['d'];
select: id | filter: tags includes ['a', 'b'];
select: id | filter: tags includes ['b', 'a'];
select: id | filter: tags includes ['a', 'b', 'c'];

select: id, metadata.array includes 1, metadata.array includes 12, metadata.array includes [2], metadata.array includes [3, 2, 1], metadata.array includes [12];

select: id | filter: metadata.array includes 1;
select: id | filter: metadata.array includes 12;
select: id | filter: metadata.array includes [2];
select: id | filter: metadata.array includes [3, 2, 1];
select: id | filter: metadata.array includes [12];

select: id, 'a' includes tags, not 'a' includes tags, 'd' includes tags, ['d'] includes tags, ['a', 'b'] includes tags, ['b', 'a'] includes tags, ['a', 'b', 'c'] includes tags, ['a', 'b', 'c', 'd'] includes tags;

select: id | filter: 'a' includes tags;
select: id | filter: 'd' includes tags;
select: id | filter: ['d'] includes tags;
select: id | filter: ['a', 'b'] includes tags;
select: id | filter: ['b', 'a'] includes tags;
select: id | filter: ['a', 'b', 'c'] includes tags;
select: id | filter: ['a', 'b', 'c', 'd'] includes tags;

select: id, 1 includes metadata.array, [2] includes metadata.array, [3, 2, 1] includes metadata.array, [4, 3, 2, 1] includes metadata.array;

select: id | filter: 1 includes metadata.array;
select: id | filter: [2] includes metadata.array;
select: id | filter: [3, 2, 1] includes metadata.array;
select: id | filter: [4, 3, 2, 1] includes metadata.array;
