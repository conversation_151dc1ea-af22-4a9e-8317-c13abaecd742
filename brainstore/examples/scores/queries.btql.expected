[{"error": null, "query": "select: *", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"float": 0, "split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid5", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}, "scores": {"foo": 0.1}}, {"id": "uuid6", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}, "scores": {"baz": 0.75}}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id as foo", "result_rows": [{"foo": "asf-dfa"}, {"foo": "dfa-asf"}, {"foo": "uuid3"}, {"foo": "uuid5"}, {"foo": "uuid6"}, {"foo": "uuid7"}], "skip": false}, {"error": null, "query": "select: scores", "result_rows": [null, {"scores": {"bar": 0.5, "foo": 0}}, {"scores": {"baz": 0.75}}, {"scores": {"foo": 0.1}}, {"scores": {"foo": 0.25}}, {"scores": {"foo": 1}}], "skip": false}, {"error": null, "query": "select: metadata", "result_rows": [null, {"metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"metadata": {"float": 0, "split": "test"}}, {"metadata": {"float": 300.2, "nullable": false, "split": "train"}}], "skip": false}, {"error": null, "query": "select: id | filter: id = 1", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: id = \"asf\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: id match \"asf\"", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id | filter: id = \"asf-dfa\"", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id | filter: id match \"asf-dfa\"", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id as bar | filter: id match \"asf-dfa\"", "result_rows": [{"bar": "asf-dfa"}], "skip": false}, {"error": null, "query": "-- This makes sure columnar projection works\nselect: scores", "result_rows": [null, {"scores": {"bar": 0.5, "foo": 0}}, {"scores": {"baz": 0.75}}, {"scores": {"foo": 0.1}}, {"scores": {"foo": 0.25}}, {"scores": {"foo": 1}}], "skip": false}, {"error": null, "query": "select: scores.foo AS foo", "result_rows": [null, null, {"foo": 0.1}, {"foo": 0.25}, {"foo": 0}, {"foo": 1}], "skip": false}, {"error": null, "query": "select: scores.foo AS foo, scores", "result_rows": [null, {"foo": 0, "scores": {"bar": 0.5, "foo": 0}}, {"foo": 0.1, "scores": {"foo": 0.1}}, {"foo": 0.25, "scores": {"foo": 0.25}}, {"foo": 1, "scores": {"foo": 1}}, {"scores": {"baz": 0.75}}], "skip": false}, {"error": null, "query": "select: metadata", "result_rows": [null, {"metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"metadata": {"float": 0, "split": "test"}}, {"metadata": {"float": 300.2, "nullable": false, "split": "train"}}], "skip": false}, {"error": null, "query": "select: tags", "result_rows": [null, null, null, {"tags": ["a", "b"]}, {"tags": ["a", "c"]}, {"tags": ["d"]}], "skip": false}]