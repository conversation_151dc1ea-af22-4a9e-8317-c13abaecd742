select: id | filter: id is null;
select: id, tags | filter: tags is null;
select: id, scores | filter: scores is null;
select: id, counts | filter: counts is null;
select: id, metadata | filter: metadata is null;

select: id, scores.foo | filter: scores.foo is null;
select: id, scores.bar | filter: scores.bar is null;

select: id, counts.foo | filter: counts.foo is null;
select: id, counts.bar | filter: counts.bar is null;
select: id, counts.huge | filter: counts.huge is null;

select: id, metadata.split | filter: metadata.split is null;
select: id, metadata.description | filter: metadata.description is null;
select: id, metadata.nullable | filter: metadata.nullable is null;

select: * | filter: scores;
select: * | filter: scores IS NOT NULL;
select: * | filter: scores.foo;
select: * | filter: scores.foo IS NOT NULL;
select: * | filter: scores.bar;
select: * | filter: scores.bar IS NOT NULL;
select: * | filter: id;
select: * | filter: id IS NOT NULL;
