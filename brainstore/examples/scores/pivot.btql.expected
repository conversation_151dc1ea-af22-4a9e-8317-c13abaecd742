[{"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | measures: sum(value)", "result_rows": [{"score": {"bar": {"sum(value)": 0.5}, "baz": {"sum(value)": 0.75}, "foo": {"sum(value)": 1.35}}, "sum(value)": 2.6}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | dimensions: id | measures: sum(value)", "result_rows": [{"id": "asf-dfa", "score": {"foo": {"sum(value)": 1}}, "sum(value)": 1}, {"id": "dfa-asf", "score": {"bar": {"sum(value)": 0.5}, "foo": {"sum(value)": 0}}, "sum(value)": 0.5}, {"id": "uuid3", "score": {"foo": {"sum(value)": 0.25}}, "sum(value)": 0.25}, {"id": "uuid5", "score": {"foo": {"sum(value)": 0.1}}, "sum(value)": 0.1}, {"id": "uuid6", "score": {"baz": {"sum(value)": 0.75}}, "sum(value)": 0.75}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | dimensions: score | measures: sum(value)", "result_rows": [null, {"score": {"bar": {"sum(value)": 0.5}}, "sum(value)": 0.5}, {"score": {"baz": {"sum(value)": 0.75}}, "sum(value)": 0.75}, {"score": {"foo": {"sum(value)": 1.35}}, "sum(value)": 1.35}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | dimensions: score, id | measures: sum(value)", "result_rows": [{"id": "asf-dfa", "score": {"foo": {"sum(value)": 1}}, "sum(value)": 1}, {"id": "dfa-asf", "score": {"bar": {"sum(value)": 0.5}}, "sum(value)": 0.5}, {"id": "dfa-asf", "score": {"foo": {"sum(value)": 0}}, "sum(value)": 0}, {"id": "uuid3", "score": {"foo": {"sum(value)": 0.25}}, "sum(value)": 0.25}, {"id": "uuid5", "score": {"foo": {"sum(value)": 0.1}}, "sum(value)": 0.1}, {"id": "uuid6", "score": {"baz": {"sum(value)": 0.75}}, "sum(value)": 0.75}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | measures: sum(value)+1", "result_rows": [{"score": {"bar": {"sum(value)+1": 1.5}, "baz": {"sum(value)+1": 1.75}, "foo": {"sum(value)+1": 2.35}}, "sum(value)+1": 3.6}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | measures: count(1)", "result_rows": [{"count(1)": 7, "score": {"bar": {"count(1)": 1}, "baz": {"count(1)": 1}, "foo": {"count(1)": 4}, "null": {"count(1)": 1}}}], "skip": false}]