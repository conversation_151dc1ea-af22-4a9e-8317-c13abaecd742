[{"error": null, "query": "select: id | filter: id is null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, tags | filter: tags is null", "result_rows": [{"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, scores | filter: scores is null", "result_rows": [{"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, counts | filter: counts is null", "result_rows": [{"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, metadata | filter: metadata is null", "result_rows": [{"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo is null", "result_rows": [{"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar is null", "result_rows": [{"id": "asf-dfa"}, {"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, counts.foo | filter: counts.foo is null", "result_rows": [{"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: counts.bar is null", "result_rows": [{"id": "asf-dfa"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, counts.huge | filter: counts.huge is null", "result_rows": [{"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, metadata.split | filter: metadata.split is null", "result_rows": [{"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, metadata.description | filter: metadata.description is null", "result_rows": [{"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, metadata.nullable | filter: metadata.nullable is null", "result_rows": [{"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: * | filter: scores", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"float": 0, "split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid5", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}, "scores": {"foo": 0.1}}, {"id": "uuid6", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}, "scores": {"baz": 0.75}}], "skip": false}, {"error": null, "query": "select: * | filter: scores IS NOT NULL", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"float": 0, "split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid5", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}, "scores": {"foo": 0.1}}, {"id": "uuid6", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}, "scores": {"baz": 0.75}}], "skip": false}, {"error": null, "query": "select: * | filter: scores.foo", "result_rows": [{"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"float": 0, "split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid5", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}, "scores": {"foo": 0.1}}], "skip": false}, {"error": null, "query": "select: * | filter: scores.foo IS NOT NULL", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"float": 0, "split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid5", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}, "scores": {"foo": 0.1}}], "skip": false}, {"error": null, "query": "select: * | filter: scores.bar", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}], "skip": false}, {"error": null, "query": "select: * | filter: scores.bar IS NOT NULL", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}], "skip": false}, {"error": null, "query": "select: * | filter: id", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: id IS NOT NULL", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"float": 300.2, "nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"float": 0, "split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid5", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}, "scores": {"foo": 0.1}}, {"id": "uuid6", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}, "scores": {"baz": 0.75}}, {"id": "uuid7"}], "skip": false}]