-- arithmetic ops
select: 1 + 2;
select: 5 - 3;
select: 4 * 2;
select: 1 + 2.5;
select: 3.5 + 2;
select: 5.0 - 2;
select: 7 - 3.5;
select: 2 * 3.5;
select: 4.5 * 2;
select: 10 / 3;
select: 10.0 / 3;
select: 4 / 2.0;
select: scores.foo + 1;
select: scores.bar - 2;
select: scores.foo * 3;
select: scores.bar / 2;
select: scores.foo + scores.bar;
select: scores.foo - scores.bar;
select: scores.foo * scores.bar;
select: scores.foo / scores.bar;
select: counts.foo % counts.bar;

-- data-specific arithmetic
select: scores.foo + 0.5;
select: scores.bar - 0.25;
select: scores.foo * 2;
select: scores.bar / 2;
select: scores.foo + scores.bar;
select: scores.foo - scores.bar;
select: scores.foo * scores.bar;
select: scores.foo / scores.bar;
select: counts.foo % counts.bar;

-- combo ops
select: -(scores.bar + 0.5) as value;
select: -(scores.foo * 0.5) as value;
select: null + scores.foo;
select: scores.bar + null;
select: null * scores.foo;
select: scores.foo / null;
select: null % counts.foo;
select: -(scores.foo + scores.bar) * 0.5 as value;

-- edge cases
select: scores.foo / 0;
select: counts.foo % 0;
select: -(-scores.foo) as value;
select: (null is null) + (scores.foo is not null) as value;

-- filters with arithmetic ops
select: id, scores.foo | filter: scores.foo = 1;
select: id, scores.foo | filter: scores.foo = 1.0;
select: id, scores.foo | filter: scores.foo + scores.bar > 0.75;
select: id, scores.foo | filter: scores.foo * 2 < 0.5;
select: id, scores.bar | filter: scores.bar / 2 > 0.2;
select: id, counts.foo | filter: counts.foo % 2 = 0;
select: id, scores.foo | filter: -(scores.foo - scores.bar) >= 0;
select: id, scores.foo | filter: 0.5 + counts.foo;
select: id, scores.bar | filter: 2 - counts.bar is not null;

-- boolean ops
select: true and true;
select: true and false;
select: false and false;
select: true or true;
select: true or false;
select: false or false;

-- boolean ops with comparisons
select: 1 > 0 and 2 > 1;
select: 1 < 0 or 2 > 1;
select: scores.foo > 0.5 and scores.bar > 0.25;
select: scores.foo < 0.3 or scores.bar > 0.4;
select: scores.foo + 0.5 > 0.75 and scores.bar < 1;
select: scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2;

-- filters with boolean ops
select: id | filter: true and true;
select: id | filter: true or false;
select: id | filter: 1 > 0 and 2 < 3;
select: id | filter: (1 + 2) > 2 or (2 * 2) < 5;
select: id, scores.foo | filter: scores.foo > 0.5 and scores.bar < 0.75;
select: id, scores.bar | filter: scores.foo < 0.3 or scores.bar > 0.4;

-- constant folding
select: id, scores.foo | filter: scores.foo = 0.05 * 5;
select: id, scores.foo | filter: 2 - 1 = scores.foo;
select: id, scores.foo | filter: scores.foo = 0.0 + 0.0;
select: id, scores.foo | filter: scores.foo = 0.3 + 0.7;
select: id, scores.foo | filter: scores.foo = 0.3 + 0.8;
select: id, scores.foo | filter: scores.foo = 1.0 * 3.0;
select: id, scores.bar | filter: 0.5 * 1 = scores.bar;
select: id, counts.foo | filter: counts.foo = (-7 + 0) * 1;
select: id, counts.bar | filter: counts.bar = 0;
select: id, counts.bar | filter: counts.bar = 0.0;
select: id, counts.bar | filter: counts.bar = 4 - 2;
select: id, counts.bar | filter: counts.bar = 1.2 + 0.8;
select: id, counts.bar | filter: counts.bar = 1.0 * 2.0;

-- booleans and valid strings cast to numbers
select: id, scores.foo | filter: scores.foo = "0.25";
select: id, scores.foo | filter: scores.foo = true;
select: id, scores.foo | filter: scores.foo = false;
select: id, scores.foo | filter: scores.foo = true + false;
select: id, scores.foo | filter: scores.foo = true * "2" - "1.75";
select: id, scores.foo | filter: scores.foo = false + 0.25;
select: id, scores.foo | filter: scores.foo = "-1" + true + true;

-- invalid strings treated as null in arithmetic expressions
select: id | filter: ("abc" + 1) is null;
select: id | filter: ("123abc" * 2) is not null;
select: id | filter: (true + "xyz") is not null;
select: id | filter: ("" - 1) is not null;
select: id | filter: (" " * 2) is null;

-- dynamic type casting arithmetic
select: id, scores, counts | filter: scores.foo + counts.foo > 3;
select: id, scores.foo - counts.bar;
measures: avg(scores.foo + counts.foo);
measures: sum(scores.foo + counts.foo + scores.bar);
