select: id, scores.foo | filter: scores.foo = 1;
select: id, scores.foo | filter: scores.foo = 0;
select: id, scores.bar | filter: scores.bar = 0.5;
select: id, scores.bar | filter: scores.bar = 0.25;

select: id, scores.foo | filter: scores.foo >= 0.25;
select: id, scores.foo | filter: scores.foo <= 0.25;
select: id, scores.foo | filter: scores.foo > 0.25;
select: id, scores.foo | filter: scores.foo < 1;

select: id, scores.bar | filter: scores.bar >= 0.5;
select: id, scores.bar | filter: scores.bar <= 0.5;
select: id, scores.bar | filter: scores.bar > 0.5;
select: id, scores.bar | filter: scores.bar < 0.5;

select: id, scores.foo | sort: scores.foo, id | limit: 3;
select: id, scores.foo | sort: scores.foo desc, id | limit: 3;
select: id, scores.bar | sort: scores.bar, id | limit: 3;
select: id, scores.bar | sort: scores.bar desc, id | limit: 3;
