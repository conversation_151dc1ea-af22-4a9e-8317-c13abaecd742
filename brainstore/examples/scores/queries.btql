select: *;
select: id;
select: id as foo;
select: scores;
select: metadata;

select: id | filter: id = 1;

select: id | filter: id = "asf";
select: id | filter: id match "asf";
select: id | filter: id = "asf-dfa";
select: id | filter: id match "asf-dfa";

select: id as bar | filter: id match "asf-dfa";

-- This makes sure columnar projection works
select: scores;
select: scores.foo AS foo;
select: scores.foo AS foo, scores;
select: metadata;
select: tags;
