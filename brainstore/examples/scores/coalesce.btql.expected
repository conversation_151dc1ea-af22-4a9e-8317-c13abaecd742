[{"error": null, "query": "-- {foo: 0, bar: 0.5}. This should return no rows even with pushdown.\n/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nselect: id, scores | filter: coalesce(scores.bar, scores.foo) = 0", "result_rows": [], "skip": false}, {"error": null, "query": "-- This should return rows\n/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nselect: id, scores | filter: coalesce(scores.foo, scores.bar) = 0", "result_rows": [{"id": "dfa-asf", "scores": {"bar": 0.5, "foo": 0}}], "skip": false}, {"error": null, "query": "/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nmeasures: count(1) | filter: coalesce(scores.bar, scores.foo) = 0.5", "result_rows": [{"count(1)": 1}], "skip": false}, {"error": null, "query": "/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nselect: scores, id | filter: coalesce(scores.bar, scores.foo) = 0.5", "result_rows": [{"id": "dfa-asf", "scores": {"bar": 0.5, "foo": 0}}], "skip": false}, {"error": null, "query": "/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nselect: scores, id | filter: coalesce(scores.bar, scores.foo) < 0.5", "result_rows": [{"id": "uuid3", "scores": {"foo": 0.25}}, {"id": "uuid5", "scores": {"foo": 0.1}}], "skip": false}, {"error": null, "query": "/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nselect: scores, id | filter: 0.5 <= coalesce(scores.bar, scores.foo)", "result_rows": [{"id": "asf-dfa", "scores": {"foo": 1}}, {"id": "dfa-asf", "scores": {"bar": 0.5, "foo": 0}}], "skip": false}, {"error": null, "query": "/*!optimizer -- coalesce should be pushed down to tantivy\n[.. | objects | select(has(\"TantivySearch\")) | .TantivySearch | .. | select(type == \"object\" and has(\"BooleanQuery\"))] | length > 0\n*/\nselect: scores, id | filter: 0.5 <= coalesce(scores.bar, scores.foo) and scores.baz is not null", "result_rows": [], "skip": false}, {"error": null, "query": "/*!optimizer -- We can only pushdown field cmp literal\n[.. | select(. == \"AllQuery\")] | length == 1\n*/\nmeasures: count(1) | filter: coalesce(scores.bar, 0.4) = 0.5", "result_rows": [{"count(1)": 1}], "skip": false}, {"error": null, "query": "/*!optimizer -- We can only pushdown field cmp literal\n[.. | select(. == \"AllQuery\")] | length == 1\n*/\nmeasures: count(1) | filter: coalesce(scores.bar, scores.foo) = scores.baz", "result_rows": [{"count(1)": 0}], "skip": false}, {"error": null, "query": "/*!optimizer -- Can't pushdown or filters\n[.. | select(. == \"AllQuery\")] | length == 1\n*/\nmeasures: count(1) | filter: coalesce(scores.bar, scores.foo) = 0.5 or scores.baz is not null", "result_rows": [{"count(1)": 2}], "skip": false}]