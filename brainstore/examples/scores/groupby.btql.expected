[{"error": null, "query": "dimensions: metadata.split", "result_rows": [null, {"split": "test"}, {"split": "train"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split, id", "result_rows": [{"id": "asf-dfa", "split": "train"}, {"id": "dfa-asf", "split": "train"}, {"id": "uuid3", "split": "test"}, {"id": "uuid5", "split": "test"}, {"id": "uuid6", "split": "test"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split, id | filter: metadata.split='test'", "result_rows": [{"id": "uuid3", "split": "test"}, {"id": "uuid5", "split": "test"}, {"id": "uuid6", "split": "test"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split, id | sort: id", "result_rows": [{"id": "asf-dfa", "split": "train"}, {"id": "dfa-asf", "split": "train"}, {"id": "uuid3", "split": "test"}, {"id": "uuid5", "split": "test"}, {"id": "uuid6", "split": "test"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split as s, id | sort: s, id", "result_rows": [{"id": "uuid3", "s": "test"}, {"id": "uuid5", "s": "test"}, {"id": "uuid6", "s": "test"}, {"id": "asf-dfa", "s": "train"}, {"id": "dfa-asf", "s": "train"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split as s, id | sort: s desc, id", "result_rows": [{"id": "uuid7"}, {"id": "asf-dfa", "s": "train"}, {"id": "dfa-asf", "s": "train"}, {"id": "uuid3", "s": "test"}, {"id": "uuid5", "s": "test"}, {"id": "uuid6", "s": "test"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: count(1)", "result_rows": [{"count(1)": 1}, {"count(1)": 2, "split": "train"}, {"count(1)": 3, "split": "test"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: count(1) as foo", "result_rows": [{"foo": 1}, {"foo": 2, "split": "train"}, {"foo": 3, "split": "test"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: count(1) + 1", "result_rows": [{"count(1) + 1": 2}, {"count(1) + 1": 3, "split": "train"}, {"count(1) + 1": 4, "split": "test"}], "skip": false}, {"error": null, "query": "-- should be 0\ndimensions: metadata.split | measures: count(null)", "result_rows": [{"count(null)": 0, "split": "test"}, {"count(null)": 0, "split": "train"}, {"count(null)": 0}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: sum(scores.foo)", "result_rows": [null, {"split": "test", "sum(scores.foo)": 0.35}, {"split": "train", "sum(scores.foo)": 1}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: sum(counts.bar)", "result_rows": [null, {"split": "test", "sum(counts.bar)": 2}, {"split": "train", "sum(counts.bar)": 0}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: sum(null)", "result_rows": [null, {"split": "test"}, {"split": "train"}], "skip": false}, {"error": null, "query": "dimensions: metadata.split | measures: sum(scores.foo) AS num_scores | sort: num_scores", "result_rows": [{"num_scores": 0.35, "split": "test"}, {"num_scores": 1, "split": "train"}, null], "skip": false}]