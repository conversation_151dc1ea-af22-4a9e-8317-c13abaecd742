dimensions: metadata.split;
dimensions: metadata.split, id;
dimensions: metadata.split, id | filter: metadata.split='test';

dimensions: metadata.split, id | sort: id;
dimensions: metadata.split as s, id | sort: s, id;
dimensions: metadata.split as s, id | sort: s desc, id;

dimensions: metadata.split | measures: count(1);
dimensions: metadata.split | measures: count(1) as foo;
dimensions: metadata.split | measures: count(1) + 1;

-- should be 0
dimensions: metadata.split | measures: count(null);

dimensions: metadata.split | measures: sum(scores.foo);
dimensions: metadata.split | measures: sum(counts.bar);
dimensions: metadata.split | measures: sum(null);

dimensions: metadata.split | measures: sum(scores.foo) AS num_scores | sort: num_scores;
