unpivot: scores AS (score, value) | pivot: score | measures: sum(value);
unpivot: scores AS (score, value) | pivot: score | dimensions: id | measures: sum(value);
unpivot: scores AS (score, value) | pivot: score | dimensions: score | measures: sum(value);
unpivot: scores AS (score, value) | pivot: score | dimensions: score, id | measures: sum(value);
unpivot: scores AS (score, value) | pivot: score | measures: sum(value)+1;
unpivot: scores AS (score, value) | pivot: score | measures: count(1)
