[{"error": null, "query": "select: id, insert(null) as obj", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, insert(null, \"foo\", \"bar\") as obj", "result_rows": [{"id": "asf-dfa", "obj": {"foo": "bar"}}, {"id": "dfa-asf", "obj": {"foo": "bar"}}, {"id": "uuid3", "obj": {"foo": "bar"}}, {"id": "uuid5", "obj": {"foo": "bar"}}, {"id": "uuid6", "obj": {"foo": "bar"}}, {"id": "uuid7", "obj": {"foo": "bar"}}], "skip": false}, {"error": null, "query": "select: id, insert({}) as obj", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, insert({}, \"num1\", 42, \"num2\", -2.0) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"num1": 42, "num2": -2}}, {"id": "dfa-asf", "obj": {"num1": 42, "num2": -2}}, {"id": "uuid3", "obj": {"num1": 42, "num2": -2}}, {"id": "uuid5", "obj": {"num1": 42, "num2": -2}}, {"id": "uuid6", "obj": {"num1": 42, "num2": -2}}, {"id": "uuid7", "obj": {"num1": 42, "num2": -2}}], "skip": false}, {"error": null, "query": "select: id, insert(metadata) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"id": "dfa-asf", "obj": {"float": 300.2, "nullable": false, "split": "train"}}, {"id": "uuid3", "obj": {"float": 0, "split": "test"}}, {"id": "uuid5", "obj": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"id": "uuid6", "obj": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "select: id, insert(metadata, \"foo\", \"bar\", \"a\", \"b\") as obj", "result_rows": [{"id": "asf-dfa", "obj": {"a": "b", "caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "foo": "bar", "nullable": true, "split": "train"}}, {"id": "dfa-asf", "obj": {"a": "b", "float": 300.2, "foo": "bar", "nullable": false, "split": "train"}}, {"id": "uuid3", "obj": {"a": "b", "float": 0, "foo": "bar", "split": "test"}}, {"id": "uuid5", "obj": {"a": "b", "array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "foo": "bar", "split": "test"}}, {"id": "uuid6", "obj": {"a": "b", "array": [1, 2, 3], "caps": "WoRLD HElLO", "foo": "bar", "split": "test"}}, {"id": "uuid7", "obj": {"a": "b", "foo": "bar"}}], "skip": false}, {"error": null, "query": "select: id, insert(metadata, \"missing\", null, \"array\", [1, 2, 3]) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"array": [1, 2, 3], "caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"id": "dfa-asf", "obj": {"array": [1, 2, 3], "float": 300.2, "nullable": false, "split": "train"}}, {"id": "uuid3", "obj": {"array": [1, 2, 3], "float": 0, "split": "test"}}, {"id": "uuid5", "obj": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"id": "uuid6", "obj": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid7", "obj": {"array": [1, 2, 3]}}], "skip": false}, {"error": null, "query": "select: id, insert(metadata, \"a\", [0], \"b\", [1, 2, 3]) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"a": [0], "b": [1, 2, 3], "caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"id": "dfa-asf", "obj": {"a": [0], "b": [1, 2, 3], "float": 300.2, "nullable": false, "split": "train"}}, {"id": "uuid3", "obj": {"a": [0], "b": [1, 2, 3], "float": 0, "split": "test"}}, {"id": "uuid5", "obj": {"a": [0], "array": [1, 2, 3], "b": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"id": "uuid6", "obj": {"a": [0], "array": [1, 2, 3], "b": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid7", "obj": {"a": [0], "b": [1, 2, 3]}}], "skip": false}, {"error": null, "query": "select: id, insert(metadata, \"missing\", null, \"object\", {\"foo\": \"bar\"}) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "object": {"foo": "bar"}, "split": "train"}}, {"id": "dfa-asf", "obj": {"float": 300.2, "nullable": false, "object": {"foo": "bar"}, "split": "train"}}, {"id": "uuid3", "obj": {"float": 0, "object": {"foo": "bar"}, "split": "test"}}, {"id": "uuid5", "obj": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "object": {"foo": "bar"}, "split": "test"}}, {"id": "uuid6", "obj": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "object": {"foo": "bar"}, "split": "test"}}, {"id": "uuid7", "obj": {"object": {"foo": "bar"}}}], "skip": false}, {"error": null, "query": "select: id, insert(metadata, \"a\", {\"foo\": \"bar\"}, \"b\", {\"baz\": \"qux\"}) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"a": {"foo": "bar"}, "b": {"baz": "qux"}, "caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"id": "dfa-asf", "obj": {"a": {"foo": "bar"}, "b": {"baz": "qux"}, "float": 300.2, "nullable": false, "split": "train"}}, {"id": "uuid3", "obj": {"a": {"foo": "bar"}, "b": {"baz": "qux"}, "float": 0, "split": "test"}}, {"id": "uuid5", "obj": {"a": {"foo": "bar"}, "array": [1, 2, 3], "b": {"baz": "qux"}, "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"id": "uuid6", "obj": {"a": {"foo": "bar"}, "array": [1, 2, 3], "b": {"baz": "qux"}, "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid7", "obj": {"a": {"foo": "bar"}, "b": {"baz": "qux"}}}], "skip": false}, {"error": null, "query": "select: id, insert(scores, \"english\", 85, \"physics\", 92) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"english": 85, "foo": 1, "physics": 92}}, {"id": "dfa-asf", "obj": {"bar": 0.5, "english": 85, "foo": 0, "physics": 92}}, {"id": "uuid3", "obj": {"english": 85, "foo": 0.25, "physics": 92}}, {"id": "uuid5", "obj": {"english": 85, "foo": 0.1, "physics": 92}}, {"id": "uuid6", "obj": {"baz": 0.75, "english": 85, "physics": 92}}, {"id": "uuid7", "obj": {"english": 85, "physics": 92}}], "skip": false}, {"error": null, "query": "select: id, insert(counts, \"new_count\", 50, \"total\", 1000) as obj", "result_rows": [{"id": "asf-dfa", "obj": {"foo": 10, "huge": 5, "new_count": 50, "total": 1000}}, {"id": "dfa-asf", "obj": {"bar": 0, "foo": 3, "huge": 9223372036854775807, "new_count": 50, "total": 1000}}, {"id": "uuid3", "obj": {"bar": 2, "foo": -7, "huge": -3, "new_count": 50, "total": 1000}}, {"id": "uuid5", "obj": {"new_count": 50, "total": 1000}}, {"id": "uuid6", "obj": {"new_count": 50, "total": 1000}}, {"id": "uuid7", "obj": {"new_count": 50, "total": 1000}}], "skip": false}, {"error": "btql bind failed: Error: Expected 2 arguments (received 0) for function: insert", "query": "-- Error cases\nselect: id, insert() as obj", "result_rows": [], "skip": false}, {"error": "Internal error: Expected object", "query": "select: id, insert(id, \"key\", \"value\") as obj", "result_rows": [], "skip": false}, {"error": "Internal error: Expected object", "query": "select: id, insert(tags, \"key\", \"value\") as obj", "result_rows": [], "skip": false}, {"error": "Internal error: INSERT requires an odd number of arguments (base objects + key-value pairs)", "query": "select: id, insert(metadata, \"key\") as obj", "result_rows": [], "skip": false}, {"error": "Internal error: insert function keys must be strings", "query": "select: id, insert(metadata, null, \"value\") as obj", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, insert(metadata, 123, \"value\") as obj", "result_rows": [{"id": "asf-dfa", "obj": {"123": "value", "caps": "HELLO WORLD", "description": "a red car", "float": 1.5, "nullable": true, "split": "train"}}, {"id": "dfa-asf", "obj": {"123": "value", "float": 300.2, "nullable": false, "split": "train"}}, {"id": "uuid3", "obj": {"123": "value", "float": 0, "split": "test"}}, {"id": "uuid5", "obj": {"123": "value", "array": [1, 2, 3], "caps": "WoRLD HElLO", "float": 13.1, "split": "test"}}, {"id": "uuid6", "obj": {"123": "value", "array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid7", "obj": {"123": "value"}}], "skip": false}]