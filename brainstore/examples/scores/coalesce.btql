-- {foo: 0, bar: 0.5}. This should return no rows even with pushdown.
/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: id, scores | filter: coalesce(scores.bar, scores.foo) = 0;

-- This should return rows
/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: id, scores | filter: coalesce(scores.foo, scores.bar) = 0;

/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
measures: count(1) | filter: coalesce(scores.bar, scores.foo) = 0.5;

/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: scores, id | filter: coalesce(scores.bar, scores.foo) = 0.5;

/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: scores, id | filter: coalesce(scores.bar, scores.foo) < 0.5;

/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: scores, id | filter: 0.5 <= coalesce(scores.bar, scores.foo);

/*!optimizer -- coalesce should be pushed down to tantivy
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: scores, id | filter: 0.5 <= coalesce(scores.bar, scores.foo) and scores.baz is not null;

/*!optimizer -- We can only pushdown field cmp literal
[.. | select(. == "AllQuery")] | length == 1
*/
measures: count(1) | filter: coalesce(scores.bar, 0.4) = 0.5;

/*!optimizer -- We can only pushdown field cmp literal
[.. | select(. == "AllQuery")] | length == 1
*/
measures: count(1) | filter: coalesce(scores.bar, scores.foo) = scores.baz;

/*!optimizer -- Can't pushdown or filters
[.. | select(. == "AllQuery")] | length == 1
*/
measures: count(1) | filter: coalesce(scores.bar, scores.foo) = 0.5 or scores.baz is not null;
