select: id, insert(null) as obj;
select: id, insert(null, "foo", "bar") as obj;

select: id, insert({}) as obj;
select: id, insert({}, "num1", 42, "num2", -2.0) as obj;

select: id, insert(metadata) as obj;
select: id, insert(metadata, "foo", "bar", "a", "b") as obj;

select: id, insert(metadata, "missing", null, "array", [1, 2, 3]) as obj;
select: id, insert(metadata, "a", [0], "b", [1, 2, 3]) as obj;

select: id, insert(metadata, "missing", null, "object", {"foo": "bar"}) as obj;
select: id, insert(metadata, "a", {"foo": "bar"}, "b", {"baz": "qux"}) as obj;

select: id, insert(scores, "english", 85, "physics", 92) as obj;
select: id, insert(counts, "new_count", 50, "total", 1000) as obj;

-- Error cases
select: id, insert() as obj;
select: id, insert(id, "key", "value") as obj;
select: id, insert(tags, "key", "value") as obj;
select: id, insert(metadata, "key") as obj;
select: id, insert(metadata, null, "value") as obj;
select: id, insert(metadata, 123, "value") as obj;
