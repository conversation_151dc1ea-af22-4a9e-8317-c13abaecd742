measures: count(1);

-- This should still return a single row with 0
measures: count(1) | filter: false;

measures: count(1)*2;
measures: count(null);
measures: count(null) + count(1);
measures: count(scores.foo), count(scores.bar);

measures: sum(null);
measures: sum(scores.foo);
measures: sum(scores.bar);
measures: sum(scores.foo)*2;
measures: sum(scores.foo) | filter: false;
measures: sum(scores.foo) | filter: scores.bar = 0.5;
measures: sum(scores.bar) | filter: scores.foo = 0;
measures: sum(scores.bar) | filter: scores.foo = 1;
measures: sum(scores.bar) | filter: scores.foo = 1.0;
measures: sum(scores.foo) | filter: scores.foo = 7 - 6;
measures: sum(scores.foo) | filter: scores.foo = 0.5 + 0.5;
measures: sum(counts.foo);
measures: sum(counts.bar);
measures: sum(counts.bar)*3;
measures: sum(counts.foo) | filter: false;
measures: sum(counts.foo) | filter: counts.bar = 2;
measures: sum(counts.bar) | filter: counts.foo = 3;
measures: sum(counts.bar) | filter: counts.foo = 10;
measures: sum(counts.bar) | filter: counts.foo = 10.0;
measures: sum(counts.foo) | filter: counts.foo = 5 * 2;
measures: sum(counts.foo) | filter: counts.foo = 1.2 + 8.8;
-- Sum > i64 max (forces u64)
measures: sum(counts.huge);

measures: avg(null);
measures: avg(scores.foo);
measures: avg(scores.bar);
measures: avg(scores.foo)*2;
measures: avg(scores.foo) | filter: false;
measures: avg(scores.foo) | filter: scores.bar = 0.5;
measures: avg(scores.bar) | filter: scores.foo = 0;
measures: avg(scores.bar) | filter: scores.foo = 1;
measures: avg(scores.bar) | filter: scores.foo = 1.0;
measures: avg(scores.foo) | filter: scores.foo = 7 - 6;
measures: avg(scores.foo) | filter: scores.foo = 0.5 + 0.5;
measures: avg(counts.foo);
measures: avg(counts.bar);
measures: avg(counts.bar)*3;
measures: avg(counts.foo) | filter: false;
measures: avg(counts.foo) | filter: counts.bar = 2;
measures: avg(counts.bar) | filter: counts.foo = 3;
measures: avg(counts.bar) | filter: counts.foo = 10;
measures: avg(counts.bar) | filter: counts.foo = 10.0;
measures: avg(counts.foo) | filter: counts.foo = 5 * 2;
measures: avg(counts.foo) | filter: counts.foo = 1.2 + 8.8;
measures: avg(counts.huge);

measures: min(null);
measures: min(scores.foo);
measures: min(scores.bar);
measures: min(scores.foo)*2;
measures: min(scores.foo) | filter: false;
measures: min(scores.foo) | filter: scores.bar = 0.5;
measures: min(scores.bar) | filter: scores.foo = 0;
measures: min(scores.bar) | filter: scores.foo = 1;
measures: min(scores.bar) | filter: scores.foo = 1.0;
measures: min(scores.foo) | filter: scores.foo = 7 - 6;
measures: min(scores.foo) | filter: scores.foo = 0.5 + 0.5;
measures: min(counts.foo);
measures: min(counts.bar);
measures: min(counts.bar)*3;
measures: min(counts.foo) | filter: false;
measures: min(counts.foo) | filter: counts.bar = 2;
measures: min(counts.bar) | filter: counts.foo = 3;
measures: min(counts.bar) | filter: counts.foo = 10;
measures: min(counts.bar) | filter: counts.foo = 10.0;
measures: min(counts.foo) | filter: counts.foo = 5 * 2;
measures: min(counts.foo) | filter: counts.foo = 1.2 + 8.8;
measures: min(counts.huge);

measures: max(null);
measures: max(scores.foo);
measures: max(scores.bar);
measures: max(scores.foo)*2;
measures: max(scores.foo) | filter: false;
measures: max(scores.foo) | filter: scores.bar = 0.5;
measures: max(scores.bar) | filter: scores.foo = 0;
measures: max(scores.bar) | filter: scores.foo = 1;
measures: max(scores.bar) | filter: scores.foo = 1.0;
measures: max(scores.foo) | filter: scores.foo = 7 - 6;
measures: max(scores.foo) | filter: scores.foo = 0.5 + 0.5;
measures: max(counts.foo);
measures: max(counts.bar);
measures: max(counts.bar)*3;
measures: max(counts.foo) | filter: false;
measures: max(counts.foo) | filter: counts.bar = 2;
measures: max(counts.bar) | filter: counts.foo = 3;
measures: max(counts.bar) | filter: counts.foo = 10;
measures: max(counts.bar) | filter: counts.foo = 10;
measures: max(counts.bar) | filter: counts.foo = 10.0;
measures: max(counts.foo) | filter: counts.foo = 5 * 2;
measures: max(counts.foo) | filter: counts.foo = 1.2 + 8.8;
measures: max(counts.huge);

measures: percentile(scores.foo, 0.5) as p50;
measures: percentile(scores.foo, 0.9) as p90;
measures: percentile(scores.foo, 0.0) as p0;

/*!result -- avg and sum match for predicate on a single row
 .[0].s == .[0].c
 */
measures: sum(id='dfa-asf' ? scores.bar : NULL) AS s, avg(id='dfa-asf' ? scores.bar : NULL) AS c;
