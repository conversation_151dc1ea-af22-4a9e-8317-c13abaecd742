[{"error": null, "query": "select: * | filter: object.top.array = []", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.top.array = [1, \"two\", 3.14, true]", "result_rows": [{"id": 23, "object": {"shared": {"category": "production", "priority": 2}, "top": {"array": [1, "two", 3.14, true], "boolean": false, "number": 42, "object": {"nested": "value"}, "string": "text"}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.array = [5, \"six\", 2.718, false]", "result_rows": [{"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.object = {\"nested\": \"value\"}", "result_rows": [{"id": 23, "object": {"shared": {"category": "production", "priority": 2}, "top": {"array": [1, "two", 3.14, true], "boolean": false, "number": 42, "object": {"nested": "value"}, "string": "text"}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.object = {\"nested\": \"key\"}", "result_rows": [{"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}], "skip": false}]