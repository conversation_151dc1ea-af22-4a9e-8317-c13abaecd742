select: id, len(object.array), len(object.nested_array), len(object.top.nested_arrays), len(object.tags);

select: id, object.array | filter: len(object.array) = 0;
select: id, object.array | filter: len(object.array) = 2;
select: id, object.array | filter: len(object.array) = 4;

select: id, object.nested_array | filter: len(object.nested_array) = 0;
select: id, object.nested_array | filter: len(object.nested_array) = 3;

select: id, object.top.nested_arrays | filter: len(object.top.nested_arrays) = 0;
select: id, object.top.nested_arrays | filter: len(object.top.nested_arrays) = 2;

select: id, object.tags | filter: len(object.tags) < 3;
select: id, object.tags | filter: len(object.tags) >= 3;
