[{"error": null, "query": "select: * | filter: object.top.field like \"a bright car shuffled over the road\"", "result_rows": [{"id": 1, "object": {"array": ["a dusty road", {"nested": 123}], "top": {"field": "a bright car shuffled over the road"}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.field like \"A bright car shuffled over the road\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.top.field ilike \"A BRIGHT CAR shuffled over the road\"", "result_rows": [{"id": 1, "object": {"array": ["a dusty road", {"nested": 123}], "top": {"field": "a bright car shuffled over the road"}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%hello world%\"", "result_rows": [{"id": 5, "object": {"array": [null, 42, "text"], "shared": {"id": "ABC123", "status": "active"}, "top": {"string": "hello world", "sub_object": {"a": 1, "b": {"c": "nested", "d": false}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.string like \"%hello world%\"", "result_rows": [{"id": 5, "object": {"array": [null, 42, "text"], "shared": {"id": "ABC123", "status": "active"}, "top": {"string": "hello world", "sub_object": {"a": 1, "b": {"c": "nested", "d": false}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.string like \"%HELLO WORLD%\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.top.string ilike \"%HELLO WORLD%\"", "result_rows": [{"id": 5, "object": {"array": [null, 42, "text"], "shared": {"id": "ABC123", "status": "active"}, "top": {"string": "hello world", "sub_object": {"a": 1, "b": {"c": "nested", "d": false}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%example%\"", "result_rows": [{"id": 11, "object": {"shared": {"category": "test", "id": "XYZ789"}, "tags": ["important", "urgent", "critical"], "top": {"ip": "***********", "range": {"max": 100, "min": 0}, "url": "https://example.com"}}}, {"id": 3, "object": {"nested_array": [1, 2, [3, 4, {"deep": "structure"}]], "shared": {"category": "example", "id": "ABC123"}, "top": {"boolean": true, "date": "2023-05-15T14:30:00Z", "number": 42.5}}}, {"id": 7, "object": {"shared": {"category": "example", "priority": 1}, "top": {"date_array": ["2023-05-16T09:00:00Z", "2023-05-17T10:30:00Z"], "decimal": 3.14159}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.shared like \"%example%\"", "result_rows": [{"id": 3, "object": {"nested_array": [1, 2, [3, 4, {"deep": "structure"}]], "shared": {"category": "example", "id": "ABC123"}, "top": {"boolean": true, "date": "2023-05-15T14:30:00Z", "number": 42.5}}}, {"id": 7, "object": {"shared": {"category": "example", "priority": 1}, "top": {"date_array": ["2023-05-16T09:00:00Z", "2023-05-17T10:30:00Z"], "decimal": 3.14159}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.shared.category like \"example\"", "result_rows": [{"id": 3, "object": {"nested_array": [1, 2, [3, 4, {"deep": "structure"}]], "shared": {"category": "example", "id": "ABC123"}, "top": {"boolean": true, "date": "2023-05-15T14:30:00Z", "number": 42.5}}}, {"id": 7, "object": {"shared": {"category": "example", "priority": 1}, "top": {"date_array": ["2023-05-16T09:00:00Z", "2023-05-17T10:30:00Z"], "decimal": 3.14159}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.shared.category like \"EXAMPLE\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.shared.category ilike \"EXAMPLE\"", "result_rows": [{"id": 3, "object": {"nested_array": [1, 2, [3, 4, {"deep": "structure"}]], "shared": {"category": "example", "id": "ABC123"}, "top": {"boolean": true, "date": "2023-05-15T14:30:00Z", "number": 42.5}}}, {"id": 7, "object": {"shared": {"category": "example", "priority": 1}, "top": {"date_array": ["2023-05-16T09:00:00Z", "2023-05-17T10:30:00Z"], "decimal": 3.14159}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%complexity%\"", "result_rows": [{"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top like \"%complexity%\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.nested_array like \"%complexity%\"", "result_rows": [{"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%2024-11-20T09:45:00Z%\"", "result_rows": [{"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top like \"%2024-11-20T09:45:00Z%\"", "result_rows": [{"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.date like \"2024-11-20T09:45:00Z\"", "result_rows": [{"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%2024-11-20T09:45:00+00:00%\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.top like \"%2024-11-20T09:45:00+00:00%\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.top.date like \"2024-11-20T09:45:00+00:00\"", "result_rows": [{"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%true%\"", "result_rows": [{"id": 10, "object": {"shared": {"priority": 4, "status": "pending"}, "top": {"boolean_array": [false, true, false], "mixed_array": [5, "six", 7.5, {"eight": 8}]}}}, {"id": 19, "object": {"shared": {"category": "production", "id": "DEF456"}, "top": {"config": {"enabled": true, "max_retries": 3, "timeout": 30.5}, "results": [{"id": 1, "status": "success"}, {"error": "timeout", "id": 2, "status": "failure"}]}}}, {"id": 23, "object": {"shared": {"category": "production", "priority": 2}, "top": {"array": [1, "two", 3.14, true], "boolean": false, "number": 42, "object": {"nested": "value"}, "string": "text"}}}, {"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}, {"id": 25, "object": {"shared": {"priority": 3, "status": "active"}, "top": {"date_object": {"duration": 86399.5, "end": "2023-05-21T23:59:59Z", "start": "2023-05-20T00:00:00Z"}, "flags": {"active": true, "premium": true, "verified": false}}}}, {"id": 26, "object": {"shared": {"priority": 1, "status": "inactive"}, "top": {"date_object": {"duration": 172799, "end": "2025-01-01T23:59:59Z", "start": "2024-12-31T00:00:00Z"}, "flags": {"active": false, "premium": false, "verified": true}}}}, {"id": 3, "object": {"nested_array": [1, 2, [3, 4, {"deep": "structure"}]], "shared": {"category": "example", "id": "ABC123"}, "top": {"boolean": true, "date": "2023-05-15T14:30:00Z", "number": 42.5}}}, {"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}, {"id": 9, "object": {"shared": {"priority": 2, "status": "active"}, "top": {"boolean_array": [true, false, true], "mixed_array": [1, "two", 3, {"four": 4}]}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top like \"%true%\"", "result_rows": [{"id": 10, "object": {"shared": {"priority": 4, "status": "pending"}, "top": {"boolean_array": [false, true, false], "mixed_array": [5, "six", 7.5, {"eight": 8}]}}}, {"id": 19, "object": {"shared": {"category": "production", "id": "DEF456"}, "top": {"config": {"enabled": true, "max_retries": 3, "timeout": 30.5}, "results": [{"id": 1, "status": "success"}, {"error": "timeout", "id": 2, "status": "failure"}]}}}, {"id": 23, "object": {"shared": {"category": "production", "priority": 2}, "top": {"array": [1, "two", 3.14, true], "boolean": false, "number": 42, "object": {"nested": "value"}, "string": "text"}}}, {"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}, {"id": 25, "object": {"shared": {"priority": 3, "status": "active"}, "top": {"date_object": {"duration": 86399.5, "end": "2023-05-21T23:59:59Z", "start": "2023-05-20T00:00:00Z"}, "flags": {"active": true, "premium": true, "verified": false}}}}, {"id": 26, "object": {"shared": {"priority": 1, "status": "inactive"}, "top": {"date_object": {"duration": 172799, "end": "2025-01-01T23:59:59Z", "start": "2024-12-31T00:00:00Z"}, "flags": {"active": false, "premium": false, "verified": true}}}}, {"id": 3, "object": {"nested_array": [1, 2, [3, 4, {"deep": "structure"}]], "shared": {"category": "example", "id": "ABC123"}, "top": {"boolean": true, "date": "2023-05-15T14:30:00Z", "number": 42.5}}}, {"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}, {"id": 9, "object": {"shared": {"priority": 2, "status": "active"}, "top": {"boolean_array": [true, false, true], "mixed_array": [1, "two", 3, {"four": 4}]}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.sub_object like \"%true%\"", "result_rows": [{"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%9%\"", "result_rows": [{"id": 11, "object": {"shared": {"category": "test", "id": "XYZ789"}, "tags": ["important", "urgent", "critical"], "top": {"ip": "***********", "range": {"max": 100, "min": 0}, "url": "https://example.com"}}}, {"id": 13, "object": {"shared": {"id": "XYZ789", "status": "pending"}, "top": {"coordinates": {"lat": 37.7749, "lon": -122.4194}, "metadata": {"created_at": "2023-05-18T12:00:00Z", "updated_at": "2023-05-18T15:30:00Z"}}}}, {"id": 16, "object": {"hobbies": ["painting", "swimming"], "shared": {"category": "demo", "priority": 5}, "top": {"address": {"city": "Somewhere", "street": "456 Oak Ave", "zip": 67890}, "age": 25, "name": "<PERSON>"}}}, {"id": 17, "object": {"shared": {"priority": 1, "status": "pending"}, "timestamp": "2023-05-19T08:45:00Z", "top": {"measurements": [{"type": "temperature", "unit": "<PERSON><PERSON><PERSON>", "value": 22.5}, {"type": "humidity", "unit": "percent", "value": 60}]}}}, {"id": 18, "object": {"shared": {"priority": 2, "status": "approved"}, "timestamp": "2024-09-10T14:30:00Z", "top": {"measurements": [{"type": "pressure", "unit": "hPa", "value": 1013.25}, {"type": "wind_speed", "unit": "km/h", "value": 15}]}}}, {"id": 20, "object": {"shared": {"category": "development", "id": "GHI789"}, "top": {"config": {"enabled": false, "max_retries": 5, "timeout": 60}, "results": [{"id": 3, "status": "pending"}, {"data": "completed", "id": 4, "status": "success"}]}}}, {"id": 22, "object": {"shared": {"id": "JKL012", "status": "inactive"}, "top": {"deep_object": {"w": {"x": {"y": {"z": "extremely deep"}}}}, "nested_arrays": [[[9, 10], [11, 12]], [[13, 14], [15, 16]]]}}}, {"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}, {"id": 25, "object": {"shared": {"priority": 3, "status": "active"}, "top": {"date_object": {"duration": 86399.5, "end": "2023-05-21T23:59:59Z", "start": "2023-05-20T00:00:00Z"}, "flags": {"active": true, "premium": true, "verified": false}}}}, {"id": 26, "object": {"shared": {"priority": 1, "status": "inactive"}, "top": {"date_object": {"duration": 172799, "end": "2025-01-01T23:59:59Z", "start": "2024-12-31T00:00:00Z"}, "flags": {"active": false, "premium": false, "verified": true}}}}, {"id": 4, "object": {"nested_array": [5, 6, [7, 8, {"deep": "complexity"}]], "shared": {"category": "test", "id": "XYZ789"}, "top": {"boolean": false, "date": "2024-11-20T09:45:00Z", "number": 78.9}}}, {"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}, {"id": 7, "object": {"shared": {"category": "example", "priority": 1}, "top": {"date_array": ["2023-05-16T09:00:00Z", "2023-05-17T10:30:00Z"], "decimal": 3.14159}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%99%\"", "result_rows": [{"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}, {"id": 25, "object": {"shared": {"priority": 3, "status": "active"}, "top": {"date_object": {"duration": 86399.5, "end": "2023-05-21T23:59:59Z", "start": "2023-05-20T00:00:00Z"}, "flags": {"active": true, "premium": true, "verified": false}}}}, {"id": 26, "object": {"shared": {"priority": 1, "status": "inactive"}, "top": {"date_object": {"duration": 172799, "end": "2025-01-01T23:59:59Z", "start": "2024-12-31T00:00:00Z"}, "flags": {"active": false, "premium": false, "verified": true}}}}, {"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.array like \"%9%\"", "result_rows": [{"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.array like \"%99%\"", "result_rows": [{"id": 6, "object": {"array": [true, 99, "code"], "shared": {"id": "DEF456", "status": "inactive"}, "top": {"string": "goodbye universe", "sub_object": {"a": 9, "b": {"c": "layered", "d": true}}}}}], "skip": false}, {"error": null, "query": "select: * | filter: object like \"%2.718%\"", "result_rows": [{"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}, {"id": 8, "object": {"shared": {"category": "sample", "priority": 3}, "top": {"date_array": ["2024-12-25T00:00:00Z", "2024-12-26T12:00:00Z"], "decimal": 2.71828}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top like \"%2.718%\"", "result_rows": [{"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}, {"id": 8, "object": {"shared": {"category": "sample", "priority": 3}, "top": {"date_array": ["2024-12-25T00:00:00Z", "2024-12-26T12:00:00Z"], "decimal": 2.71828}}}], "skip": false}, {"error": null, "query": "select: * | filter: object.top.array like \"%2.718%\"", "result_rows": [{"id": 24, "object": {"shared": {"category": "testing", "priority": 4}, "top": {"array": [5, "six", 2.718, false], "boolean": true, "number": 99, "object": {"nested": "key"}, "string": "data"}}}], "skip": false}]