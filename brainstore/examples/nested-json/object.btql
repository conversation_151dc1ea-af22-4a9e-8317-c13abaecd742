select: * | filter: object match 'car';
select: * | filter: object.field match 'car';
select: * | filter: object.top.field match 'car';

select: * | filter: object match 'bright car';
select: * | filter: object.field match 'bright car';
select: * | filter: object.top.field match 'bright car';

select: * | filter: object match 'colorful sky';
select: * | filter: object.field match 'colorful sky';
select: * | filter: object.top.field match 'colorful sky';

select: * | filter: object match 'example';
select: * | filter: object.shared match 'example';
select: * | filter: object.shared.category match 'example';

select: * | filter: object match 'complexity';
-- should be empty
select: * | filter: object.top match 'complexity';
-- should match 1 row
select: * | filter: object.nested_array match 'complexity';

-- TODO: Eventually should support this, but for now it'll return no rows
select: * | filter: object.nested_array.shared match 'complexity';

-- This will return no rows because of the timestamp formatting.
select: * | filter: object match '2024-11-20T09:45:00Z';

select: * | filter: object match '2024-11-20T09:45:00+00:00';
select: * | filter: object.top match '2024-11-20T09:45:00+00:00';
select: * | filter: object.top.date match '2024-11-20T09:45:00+00:00';

select: * | filter: object match true;
select: * | filter: object.top match true;
select: * | filter: object.top.sub_object match true;

select: * | filter: object match 9;
select: * | filter: object match 99;
select: * | filter: object.array match 9;
select: * | filter: object.array match 99;

select: * | filter: object match 2.718;
select: * | filter: object.top match 2.718;
select: * | filter: object.top.array match 2.718;

select: * | filter: object = 1;
select: * | filter: object.shared = 1;
select: * | filter: object.shared.priority = 1;

select: * | filter: object IS NULL;
select: * | filter: object IS NOT NULL;
