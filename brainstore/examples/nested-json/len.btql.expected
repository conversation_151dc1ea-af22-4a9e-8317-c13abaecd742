[{"error": null, "query": "select: id, len(object.array), len(object.nested_array), len(object.top.nested_arrays), len(object.tags)", "result_rows": [{"id": 1, "len(object.array)": 2, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 10, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 11, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 3, "len(object.top.nested_arrays)": 0}, {"id": 12, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 3, "len(object.top.nested_arrays)": 0}, {"id": 13, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 14, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 15, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 16, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 17, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 18, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 19, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 2, "len(object.array)": 2, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 20, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 21, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 2}, {"id": 22, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 2}, {"id": 23, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 24, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 25, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 26, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 27, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 28, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 3, "len(object.array)": 0, "len(object.nested_array)": 3, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 4, "len(object.array)": 0, "len(object.nested_array)": 3, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 5, "len(object.array)": 3, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 6, "len(object.array)": 3, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 7, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 8, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}, {"id": 9, "len(object.array)": 0, "len(object.nested_array)": 0, "len(object.tags)": 0, "len(object.top.nested_arrays)": 0}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: len(object.array) = 0", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: len(object.array) = 2", "result_rows": [{"array": ["a dusty road", {"nested": 123}], "id": 1}, {"array": ["a shiny lake", {"nested": 456}], "id": 2}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: len(object.array) = 4", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.nested_array | filter: len(object.nested_array) = 0", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 5}, {"id": 6}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id, object.nested_array | filter: len(object.nested_array) = 3", "result_rows": [{"id": 3, "nested_array": [1, 2, [3, 4, {"deep": "structure"}]]}, {"id": 4, "nested_array": [5, 6, [7, 8, {"deep": "complexity"}]]}], "skip": false}, {"error": null, "query": "select: id, object.top.nested_arrays | filter: len(object.top.nested_arrays) = 0", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 5}, {"id": 6}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id, object.top.nested_arrays | filter: len(object.top.nested_arrays) = 2", "result_rows": [{"id": 21, "nested_arrays": [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]}, {"id": 22, "nested_arrays": [[[9, 10], [11, 12]], [[13, 14], [15, 16]]]}], "skip": false}, {"error": null, "query": "select: id, object.tags | filter: len(object.tags) < 3", "result_rows": [{"id": 10}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 5}, {"id": 6}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id, object.tags | filter: len(object.tags) >= 3", "result_rows": [{"id": 11, "tags": ["important", "urgent", "critical"]}, {"id": 12, "tags": ["low", "normal", "high"]}], "skip": false}]