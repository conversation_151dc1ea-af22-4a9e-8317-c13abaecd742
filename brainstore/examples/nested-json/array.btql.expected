[{"error": null, "query": "select: id | filter: object.array is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array is not null", "result_rows": [{"id": 1}, {"id": 2}, {"id": 5}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[0] is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 5}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[0] is not null", "result_rows": [{"id": 1}, {"id": 2}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[1] is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[1] is not null", "result_rows": [{"id": 1}, {"id": 2}, {"id": 5}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[2] is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[2] is not null", "result_rows": [{"id": 5}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-3] is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 5}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-3] is not null", "result_rows": [{"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-2] is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-2] is not null", "result_rows": [{"id": 1}, {"id": 2}, {"id": 5}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-1] is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-1] is not null", "result_rows": [{"id": 1}, {"id": 2}, {"id": 5}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[0] = 'a dusty road'", "result_rows": [{"id": 1}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-1] = 'text'", "result_rows": [{"id": 5}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[1].nested = 123", "result_rows": [{"id": 1}], "skip": false}, {"error": null, "query": "select: id | filter: object.array[-2].nested = 123", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: len(object.array) = 0", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 3}, {"id": 4}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: len(object.array) = 3", "result_rows": [{"id": 5}, {"id": 6}], "skip": false}, {"error": null, "query": "select: id | filter: len(object.array) > 3", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: object.nested_array[2][2].deep = 'structure'", "result_rows": [{"id": 3}], "skip": false}, {"error": null, "query": "select: id | filter: object.nested_array[-1][-1].deep = 'complexity'", "result_rows": [{"id": 4}], "skip": false}, {"error": null, "query": "select: id | filter: object.nested_array[0] = 1", "result_rows": [{"id": 3}], "skip": false}, {"error": null, "query": "select: id | filter: object.nested_array[-3] = 1", "result_rows": [{"id": 3}], "skip": false}, {"error": null, "query": "select: id | filter: object.nested_array[2][1] = 4", "result_rows": [{"id": 3}], "skip": false}, {"error": null, "query": "select: id | filter: object.nested_array[-1][-3] = 7", "result_rows": [{"id": 4}], "skip": false}, {"error": null, "query": "select: id | filter: len(object.nested_array) = 0", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 5}, {"id": 6}, {"id": 7}, {"id": 8}, {"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: len(object.nested_array) = 3", "result_rows": [{"id": 3}, {"id": 4}], "skip": false}, {"error": null, "query": "select: id | filter: len(object.nested_array[2]) = 3", "result_rows": [{"id": 3}, {"id": 4}], "skip": false}, {"error": null, "query": "select: id | filter: len(object.nested_array[2]) > 3", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: object.top.nested_arrays[0][0][0] = 1", "result_rows": [{"id": 21}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.nested_arrays[-2][-2][-2] = 9", "result_rows": [{"id": 22}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.nested_arrays[1][1][1] = 8", "result_rows": [{"id": 21}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.nested_arrays[-1][-1][-1] = 16", "result_rows": [{"id": 22}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.nested_arrays[1][0][0] = 5", "result_rows": [{"id": 21}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.nested_arrays[-1][-2][0] = 13", "result_rows": [{"id": 22}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.mixed_array[0] = 1", "result_rows": [{"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.mixed_array[1] = 'two'", "result_rows": [{"id": 9}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.mixed_array[-2] = 7.5", "result_rows": [{"id": 10}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.mixed_array[-1].four is null", "result_rows": [{"id": 10}, {"id": 11}, {"id": 12}, {"id": 13}, {"id": 14}, {"id": 15}, {"id": 16}, {"id": 17}, {"id": 18}, {"id": 19}, {"id": 1}, {"id": 20}, {"id": 21}, {"id": 22}, {"id": 23}, {"id": 24}, {"id": 25}, {"id": 26}, {"id": 27}, {"id": 28}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 5}, {"id": 6}, {"id": 7}, {"id": 8}], "skip": false}, {"error": null, "query": "select: id | filter: object.top.mixed_array[-1].four is not null", "result_rows": [{"id": 9}], "skip": false}]