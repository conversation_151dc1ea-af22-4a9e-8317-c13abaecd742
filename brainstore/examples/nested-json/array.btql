select: id | filter: object.array is null;
select: id | filter: object.array is not null;

select: id | filter: object.array[0] is null;
select: id | filter: object.array[0] is not null;
select: id | filter: object.array[1] is null;
select: id | filter: object.array[1] is not null;
select: id | filter: object.array[2] is null;
select: id | filter: object.array[2] is not null;
select: id | filter: object.array[-3] is null;
select: id | filter: object.array[-3] is not null;
select: id | filter: object.array[-2] is null;
select: id | filter: object.array[-2] is not null;
select: id | filter: object.array[-1] is null;
select: id | filter: object.array[-1] is not null;

select: id | filter: object.array[0] = 'a dusty road';
select: id | filter: object.array[-1] = 'text';
select: id | filter: object.array[1].nested = 123;
select: id | filter: object.array[-2].nested = 123;
select: id | filter: len(object.array) = 0;
select: id | filter: len(object.array) = 3;
select: id | filter: len(object.array) > 3;

select: id | filter: object.nested_array[2][2].deep = 'structure';
select: id | filter: object.nested_array[-1][-1].deep = 'complexity';
select: id | filter: object.nested_array[0] = 1;
select: id | filter: object.nested_array[-3] = 1;
select: id | filter: object.nested_array[2][1] = 4;
select: id | filter: object.nested_array[-1][-3] = 7;
select: id | filter: len(object.nested_array) = 0;
select: id | filter: len(object.nested_array) = 3;
select: id | filter: len(object.nested_array[2]) = 3;
select: id | filter: len(object.nested_array[2]) > 3;

select: id | filter: object.top.nested_arrays[0][0][0] = 1;
select: id | filter: object.top.nested_arrays[-2][-2][-2] = 9;
select: id | filter: object.top.nested_arrays[1][1][1] = 8;
select: id | filter: object.top.nested_arrays[-1][-1][-1] = 16;
select: id | filter: object.top.nested_arrays[1][0][0] = 5;
select: id | filter: object.top.nested_arrays[-1][-2][0] = 13;

select: id | filter: object.top.mixed_array[0] = 1;
select: id | filter: object.top.mixed_array[1] = 'two';
select: id | filter: object.top.mixed_array[-2] = 7.5;
select: id | filter: object.top.mixed_array[-1].four is null;
select: id | filter: object.top.mixed_array[-1].four is not null;
