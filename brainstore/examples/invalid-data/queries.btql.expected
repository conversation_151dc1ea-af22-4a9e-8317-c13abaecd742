[{"error": null, "query": "select: span_attributes | from: experiment('singleton')", "result_rows": [{"span_attributes": {"encounterId": "253ffc8d-4a33-43ec-814a-3a32cdea347a", "exec_counter": 59550, "name": "runNoteSection", "rawInputs": {"name": ["transcript", "patient_age", "patient_sex", "patient_full_name", "clinician_name"], "value": ["<REDACTED PHI>", "<REDACTED PHI>", "<REDACTED PHI>", "<REDACTED PHI>"]}, "type": "task"}}, {"span_attributes": {"exec_counter": 59554, "name": "chat completion", "type": "llm"}}], "skip": false, "skip_result_content": false}]