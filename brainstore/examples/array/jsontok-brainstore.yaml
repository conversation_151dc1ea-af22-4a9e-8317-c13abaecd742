version: 1
schema:
  name: test
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
            tokenized: false
    - name: object
      tantivy:
        - name: top_object
          type: !json
            stored: true
            tokenize: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/jsontok/metadata
index_uri: ./index/jsontok/data
wal_uri: ./index/jsontok/wal
realtime_wal_uri: ./index/jsontok/wal
