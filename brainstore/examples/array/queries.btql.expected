[{"error": null, "query": "select: id | filter: object.array is null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: object.array is not null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array.bar is null", "result_rows": [{"array": ["foo1", "foo2", "foo3"], "id": "5"}, {"array": ["foo3", "foo2", "foo1"], "id": "6"}, {"array": ["palm", 2, {"foo": "iced green tea"}], "id": "1"}, {"array": ["tree", [-3, {"a": "big red car"}, 6]], "id": "2"}, {"array": [-3, 0.5, 4.5], "id": "4"}, {"array": [null, true, false], "id": "3"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array.bar is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, len(object.array) | filter: len(object.array) = 0", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, len(object.array) | filter: len(object.array) = 2", "result_rows": [{"id": "2", "len(object.array)": 2}], "skip": false}, {"error": null, "query": "select: id, len(object.array) | filter: len(object.array) = 3", "result_rows": [{"id": "1", "len(object.array)": 3}, {"id": "3", "len(object.array)": 3}, {"id": "4", "len(object.array)": 3}, {"id": "5", "len(object.array)": 3}, {"id": "6", "len(object.array)": 3}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[0] = -3", "result_rows": [{"array": [-3, 0.5, 4.5], "id": "4"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[0] < -3", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[1] > 1", "result_rows": [{"array": ["palm", 2, {"foo": "iced green tea"}], "id": "1"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[1] <= 1", "result_rows": [{"array": [-3, 0.5, 4.5], "id": "4"}, {"array": [null, true, false], "id": "3"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[2] = 4.5", "result_rows": [{"array": [-3, 0.5, 4.5], "id": "4"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[-3] <= -3.0", "result_rows": [{"array": [-3, 0.5, 4.5], "id": "4"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[-3] > -3.0", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[-2] >= 2.0", "result_rows": [{"array": ["palm", 2, {"foo": "iced green tea"}], "id": "1"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[-2] < 2.0", "result_rows": [{"array": [-3, 0.5, 4.5], "id": "4"}, {"array": [null, true, false], "id": "3"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array[-1] > 4", "result_rows": [{"array": [-3, 0.5, 4.5], "id": "4"}], "skip": false}, {"error": null, "query": "select: id, object.array[0] | filter: object.array[0] = 'tree'", "result_rows": [{"id": "2", "object.array[0]": "tree"}], "skip": false}, {"error": null, "query": "select: id, object.array[0] | filter: object.array[0] is null", "result_rows": [{"id": "3"}], "skip": false}, {"error": null, "query": "select: id, object.array[0] | filter: object.array[0] is not null", "result_rows": [{"id": "1", "object.array[0]": "palm"}, {"id": "2", "object.array[0]": "tree"}, {"id": "4", "object.array[0]": -3}, {"id": "5", "object.array[0]": "foo1"}, {"id": "6", "object.array[0]": "foo3"}], "skip": false}, {"error": null, "query": "select: id, object.array[0] | filter: object.array[1] = 2.0", "result_rows": [{"id": "1", "object.array[0]": "palm"}], "skip": false}, {"error": null, "query": "select: id, object.array[0] | filter: object.array[1] < 2.0", "result_rows": [{"id": "3"}, {"id": "4", "object.array[0]": -3}], "skip": false}, {"error": null, "query": "select: id, object.array[1] | filter: object.array[1] is null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[1] | filter: object.array[1] is not null", "result_rows": [{"id": "1", "object.array[1]": 2}, {"id": "2", "object.array[1]": [-3, {"a": "big red car"}, 6]}, {"id": "3", "object.array[1]": true}, {"id": "4", "object.array[1]": 0.5}, {"id": "5", "object.array[1]": "foo2"}, {"id": "6", "object.array[1]": "foo2"}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2] like '%green%'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2] ilike '%Green%'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2] is null", "result_rows": [{"id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2] is not null", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}, {"id": "3", "object.array[2]": false}, {"id": "4", "object.array[2]": 4.5}, {"id": "5", "object.array[2]": "foo3"}, {"id": "6", "object.array[2]": "foo1"}], "skip": false}, {"error": null, "query": "select: id, object.array[0] | filter: object.array[0]", "result_rows": [{"id": "4", "object.array[0]": -3}], "skip": false}, {"error": null, "query": "select: id, object.array[1] | filter: object.array[1]", "result_rows": [{"id": "1", "object.array[1]": 2}, {"id": "2", "object.array[1]": [-3, {"a": "big red car"}, 6]}, {"id": "3", "object.array[1]": true}, {"id": "4", "object.array[1]": 0.5}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2]", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}, {"id": "4", "object.array[2]": 4.5}], "skip": false}, {"error": null, "query": "select: id, object.array[-3] | filter: object.array[-3] = 'palm'", "result_rows": [{"id": "1", "object.array[-3]": "palm"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3] | filter: object.array[-3] is null", "result_rows": [{"id": "2"}, {"id": "3"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3] | filter: object.array[-3] is not null", "result_rows": [{"id": "1", "object.array[-3]": "palm"}, {"id": "4", "object.array[-3]": -3}, {"id": "5", "object.array[-3]": "foo1"}, {"id": "6", "object.array[-3]": "foo3"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3] | filter: object.array[-2] = 'tree'", "result_rows": [{"id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3] | filter: object.array[-2] < 1", "result_rows": [{"id": "4", "object.array[-3]": -3}], "skip": false}, {"error": null, "query": "select: id, object.array[-2] | filter: object.array[-2] is null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2] | filter: object.array[-2] is not null", "result_rows": [{"id": "1", "object.array[-2]": 2}, {"id": "2", "object.array[-2]": "tree"}, {"id": "3", "object.array[-2]": true}, {"id": "4", "object.array[-2]": 0.5}, {"id": "5", "object.array[-2]": "foo2"}, {"id": "6", "object.array[-2]": "foo2"}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1] like '%iced%'", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1] ilike '%CAR%'", "result_rows": [{"id": "2", "object.array[-1]": [-3, {"a": "big red car"}, 6]}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1] is null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1] is not null", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}, {"id": "2", "object.array[-1]": [-3, {"a": "big red car"}, 6]}, {"id": "3", "object.array[-1]": false}, {"id": "4", "object.array[-1]": 4.5}, {"id": "5", "object.array[-1]": "foo3"}, {"id": "6", "object.array[-1]": "foo1"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3] | filter: object.array[-3]", "result_rows": [{"id": "4", "object.array[-3]": -3}], "skip": false}, {"error": null, "query": "select: id, object.array[-2] | filter: object.array[-2]", "result_rows": [{"id": "1", "object.array[-2]": 2}, {"id": "3", "object.array[-2]": true}, {"id": "4", "object.array[-2]": 0.5}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1]", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}, {"id": "2", "object.array[-1]": [-3, {"a": "big red car"}, 6]}, {"id": "4", "object.array[-1]": 4.5}], "skip": false}, {"error": null, "query": "select: id, len(object.array[0]) | filter: len(object.array[0]) = 0", "result_rows": [{"id": "1", "len(object.array[0])": 0}, {"id": "2", "len(object.array[0])": 0}, {"id": "3", "len(object.array[0])": 0}, {"id": "4", "len(object.array[0])": 0}, {"id": "5", "len(object.array[0])": 0}, {"id": "6", "len(object.array[0])": 0}], "skip": false}, {"error": null, "query": "select: id, len(object.array[0]) | filter: len(object.array[0]) = 1", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, len(object.array[1]) | filter: len(object.array[1]) = 0", "result_rows": [{"id": "1", "len(object.array[1])": 0}, {"id": "3", "len(object.array[1])": 0}, {"id": "4", "len(object.array[1])": 0}, {"id": "5", "len(object.array[1])": 0}, {"id": "6", "len(object.array[1])": 0}], "skip": false}, {"error": null, "query": "select: id, len(object.array[1]) | filter: len(object.array[1]) = 3", "result_rows": [{"id": "2", "len(object.array[1])": 3}], "skip": false}, {"error": null, "query": "select: id, len(object.array[-2]) | filter: len(object.array[-2]) = 0", "result_rows": [{"id": "1", "len(object.array[-2])": 0}, {"id": "2", "len(object.array[-2])": 0}, {"id": "3", "len(object.array[-2])": 0}, {"id": "4", "len(object.array[-2])": 0}, {"id": "5", "len(object.array[-2])": 0}, {"id": "6", "len(object.array[-2])": 0}], "skip": false}, {"error": null, "query": "select: id, len(object.array[-2]) | filter: len(object.array[-2]) = 1", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, len(object.array[-1]) | filter: len(object.array[-1]) = 0", "result_rows": [{"id": "1", "len(object.array[-1])": 0}, {"id": "3", "len(object.array[-1])": 0}, {"id": "4", "len(object.array[-1])": 0}, {"id": "5", "len(object.array[-1])": 0}, {"id": "6", "len(object.array[-1])": 0}], "skip": false}, {"error": null, "query": "select: id, len(object.array[-1]) | filter: len(object.array[-1]) = 3", "result_rows": [{"id": "2", "len(object.array[-1])": 3}], "skip": false}, {"error": null, "query": "// Prefix and suffix search can only match exact array indices. Wildcard matches can find anything in the array\nselect: id, object.array | filter: object.array ilike '%foo%'", "result_rows": [{"array": ["foo1", "foo2", "foo3"], "id": "5"}, {"array": ["foo3", "foo2", "foo1"], "id": "6"}, {"array": ["palm", 2, {"foo": "iced green tea"}], "id": "1"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array ilike 'foo1'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2] ilike 'foo1'", "result_rows": [{"id": "6", "object.array[2]": "foo1"}], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array ilike 'foo%'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array | filter: object.array ilike '%1'", "result_rows": [], "skip": false}]