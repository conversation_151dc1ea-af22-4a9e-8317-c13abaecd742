[{"error": null, "query": "select: id, object.array[1].a | filter: object.array[1].a is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}], "skip": false}, {"error": null, "query": "select: id, object.array[1].a | filter: object.array[1].a is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[1].a | filter: object.array[1].a = 'big red car'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2].a | filter: object.array[-2].a is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}], "skip": false}, {"error": null, "query": "select: id, object.array[-2].a | filter: object.array[-2].a is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2].a | filter: object.array[-2].a = 'big red car'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[1][-1] | filter: object.array[1][-1] is null", "result_rows": [{"id": "1"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}], "skip": false}, {"error": null, "query": "select: id, object.array[1][-1] | filter: object.array[1][-1] is not null", "result_rows": [{"id": "2", "object.array[1][-1]": 6}], "skip": false}, {"error": null, "query": "select: id, object.array[1][-1] | filter: object.array[1][-1] < 6", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[1][-1] | filter: object.array[1][-1] >= 6", "result_rows": [{"id": "2", "object.array[1][-1]": 6}], "skip": false}, {"error": null, "query": "select: id, object.array[1][-3] | filter: object.array[1][-3] = 0", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[1][-3] | filter: object.array[1][-3] = -3", "result_rows": [{"id": "2", "object.array[1][-3]": -3}], "skip": false}, {"error": null, "query": "select: id, object.array[1][0].a | filter: object.array[1][0].a = 'big red car'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[1][1].a | filter: object.array[1][1].a = 'big red car'", "result_rows": [{"a": "big red car", "id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[1][1].a | filter: object.array[1][1].a like 'big red car'", "result_rows": [{"a": "big red car", "id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[1][1].a | filter: object.array[1][1].a like '%red%'", "result_rows": [{"a": "big red car", "id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[1][1].a | filter: object.array[1][1].a ilike 'BIG red CAR'", "result_rows": [{"a": "big red car", "id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[1][1].a | filter: object.array[1][1].a ilike '%RED%'", "result_rows": [{"a": "big red car", "id": "2"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3].bar | filter: object.array[-3].bar is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}], "skip": false}, {"error": null, "query": "select: id, object.array[-3].bar | filter: object.array[-3].bar is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][2] | filter: object.array[-2][2] is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}], "skip": false}, {"error": null, "query": "select: id, object.array[-2][2] | filter: object.array[-2][2] is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][2] | filter: object.array[-2][2] < 6", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][2] | filter: object.array[-2][2] >= 6", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][0] | filter: object.array[-2][0] = 0", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][0] | filter: object.array[-2][0] = -3", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a like 'big%'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a like '%car'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a like 'big%car'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a ilike '%BIG'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a ilike 'CAR%'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a ilike 'BIG%CAR'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo is null", "result_rows": [{"id": "2"}, {"id": "3", "object.array[2]": false}, {"id": "4", "object.array[2]": 4.5}, {"id": "5", "object.array[2]": "foo3"}, {"id": "6", "object.array[2]": "foo1"}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo is not null", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo = 'iced green tea'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo like 'iced%'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo like '%green tea'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo like '%green%'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo like '%Green%'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[2] | filter: object.array[2].foo ilike '%Green%'", "result_rows": [{"id": "1", "object.array[2]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo is null", "result_rows": [{"id": "2", "object.array[-1]": [-3, {"a": "big red car"}, 6]}, {"id": "3", "object.array[-1]": false}, {"id": "4", "object.array[-1]": 4.5}, {"id": "5", "object.array[-1]": "foo3"}, {"id": "6", "object.array[-1]": "foo1"}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo is not null", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo = 'iced green tea'", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo ilike 'Green'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo ilike '%Green'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo ilike 'Green%'", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo ilike '%Green%'", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}], "skip": false}, {"error": null, "query": "select: id, object.array[-1] | filter: object.array[-1].foo ilike 'ICED%TEA'", "result_rows": [{"id": "1", "object.array[-1]": {"foo": "iced green tea"}}], "skip": false}]