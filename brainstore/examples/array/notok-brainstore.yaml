version: 1
schema:
  name: test
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
            tokenized: false
    - name: object
      tantivy:
        - name: top_object
          type: !json
            stored: true
            tokenize: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/notok/metadata
index_uri: ./index/notok/data
wal_uri: ./index/notok/wal
realtime_wal_uri: ./index/notok/wal
