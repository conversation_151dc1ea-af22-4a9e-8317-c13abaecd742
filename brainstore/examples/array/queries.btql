select: id | filter: object.array is null;
select: id | filter: object.array is not null;
select: id, object.array | filter: object.array.bar is null;
select: id, object.array | filter: object.array.bar is not null;

select: id, len(object.array) | filter: len(object.array) = 0;
select: id, len(object.array) | filter: len(object.array) = 2;
select: id, len(object.array) | filter: len(object.array) = 3;

select: id, object.array | filter: object.array[0] = -3;
select: id, object.array | filter: object.array[0] < -3;
select: id, object.array | filter: object.array[1] > 1;
select: id, object.array | filter: object.array[1] <= 1;
select: id, object.array | filter: object.array[2] = 4.5;

select: id, object.array | filter: object.array[-3] <= -3.0;
select: id, object.array | filter: object.array[-3] > -3.0;
select: id, object.array | filter: object.array[-2] >= 2.0;
select: id, object.array | filter: object.array[-2] < 2.0;
select: id, object.array | filter: object.array[-1] > 4;

select: id, object.array[0] | filter: object.array[0] = 'tree';
select: id, object.array[0] | filter: object.array[0] is null;
select: id, object.array[0] | filter: object.array[0] is not null;
select: id, object.array[0] | filter: object.array[1] = 2.0;
select: id, object.array[0] | filter: object.array[1] < 2.0;
select: id, object.array[1] | filter: object.array[1] is null;
select: id, object.array[1] | filter: object.array[1] is not null;
select: id, object.array[2] | filter: object.array[2] like '%green%';
select: id, object.array[2] | filter: object.array[2] ilike '%Green%';
select: id, object.array[2] | filter: object.array[2] is null;
select: id, object.array[2] | filter: object.array[2] is not null;
select: id, object.array[0] | filter: object.array[0];
select: id, object.array[1] | filter: object.array[1];
select: id, object.array[2] | filter: object.array[2];

select: id, object.array[-3] | filter: object.array[-3] = 'palm';
select: id, object.array[-3] | filter: object.array[-3] is null;
select: id, object.array[-3] | filter: object.array[-3] is not null;
select: id, object.array[-3] | filter: object.array[-2] = 'tree';
select: id, object.array[-3] | filter: object.array[-2] < 1;
select: id, object.array[-2] | filter: object.array[-2] is null;
select: id, object.array[-2] | filter: object.array[-2] is not null;
select: id, object.array[-1] | filter: object.array[-1] like '%iced%';
select: id, object.array[-1] | filter: object.array[-1] ilike '%CAR%';
select: id, object.array[-1] | filter: object.array[-1] is null;
select: id, object.array[-1] | filter: object.array[-1] is not null;
select: id, object.array[-3] | filter: object.array[-3];
select: id, object.array[-2] | filter: object.array[-2];
select: id, object.array[-1] | filter: object.array[-1];

select: id, len(object.array[0]) | filter: len(object.array[0]) = 0;
select: id, len(object.array[0]) | filter: len(object.array[0]) = 1;
select: id, len(object.array[1]) | filter: len(object.array[1]) = 0;
select: id, len(object.array[1]) | filter: len(object.array[1]) = 3;

select: id, len(object.array[-2]) | filter: len(object.array[-2]) = 0;
select: id, len(object.array[-2]) | filter: len(object.array[-2]) = 1;
select: id, len(object.array[-1]) | filter: len(object.array[-1]) = 0;
select: id, len(object.array[-1]) | filter: len(object.array[-1]) = 3;

// Prefix and suffix search can only match exact array indices. Wildcard matches can find anything in the array
select: id, object.array | filter: object.array ilike '%foo%';
select: id, object.array | filter: object.array ilike 'foo1';
select: id, object.array[2] | filter: object.array[2] ilike 'foo1';
select: id, object.array | filter: object.array ilike 'foo%';
select: id, object.array | filter: object.array ilike '%1';
