
select: id, object.array[1].a | filter: object.array[1].a is null;
select: id, object.array[1].a | filter: object.array[1].a is not null;
select: id, object.array[1].a | filter: object.array[1].a = 'big red car';

select: id, object.array[-2].a | filter: object.array[-2].a is null;
select: id, object.array[-2].a | filter: object.array[-2].a is not null;
select: id, object.array[-2].a | filter: object.array[-2].a = 'big red car';

select: id, object.array[1][-1] | filter: object.array[1][-1] is null;
select: id, object.array[1][-1] | filter: object.array[1][-1] is not null;
select: id, object.array[1][-1] | filter: object.array[1][-1] < 6;
select: id, object.array[1][-1] | filter: object.array[1][-1] >= 6;
select: id, object.array[1][-3] | filter: object.array[1][-3] = 0;
select: id, object.array[1][-3] | filter: object.array[1][-3] = -3;
select: id, object.array[1][0].a | filter: object.array[1][0].a = 'big red car';
select: id, object.array[1][1].a | filter: object.array[1][1].a = 'big red car';
select: id, object.array[1][1].a | filter: object.array[1][1].a like 'big red car';
select: id, object.array[1][1].a | filter: object.array[1][1].a like '%red%';
select: id, object.array[1][1].a | filter: object.array[1][1].a ilike 'BIG red CAR';
select: id, object.array[1][1].a | filter: object.array[1][1].a ilike '%RED%';

select: id, object.array[-3].bar | filter: object.array[-3].bar is null;
select: id, object.array[-3].bar | filter: object.array[-3].bar is not null;
select: id, object.array[-2][2] | filter: object.array[-2][2] is null;
select: id, object.array[-2][2] | filter: object.array[-2][2] is not null;
select: id, object.array[-2][2] | filter: object.array[-2][2] < 6;
select: id, object.array[-2][2] | filter: object.array[-2][2] >= 6;
select: id, object.array[-2][0] | filter: object.array[-2][0] = 0;
select: id, object.array[-2][0] | filter: object.array[-2][0] = -3;
select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a like 'big%';
select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a like '%car';
select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a like 'big%car';
select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a ilike '%BIG';
select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a ilike 'CAR%';
select: id, object.array[-2][-1].a | filter: object.array[-2][-1].a ilike 'BIG%CAR';

select: id, object.array[2] | filter: object.array[2].foo is null;
select: id, object.array[2] | filter: object.array[2].foo is not null;
select: id, object.array[2] | filter: object.array[2].foo = 'iced green tea';
select: id, object.array[2] | filter: object.array[2].foo like 'iced%';
select: id, object.array[2] | filter: object.array[2].foo like '%green tea';
select: id, object.array[2] | filter: object.array[2].foo like '%green%';
select: id, object.array[2] | filter: object.array[2].foo like '%Green%';
select: id, object.array[2] | filter: object.array[2].foo ilike '%Green%';
select: id, object.array[-1] | filter: object.array[-1].foo is null;
select: id, object.array[-1] | filter: object.array[-1].foo is not null;
select: id, object.array[-1] | filter: object.array[-1].foo = 'iced green tea';
select: id, object.array[-1] | filter: object.array[-1].foo ilike 'Green';
select: id, object.array[-1] | filter: object.array[-1].foo ilike '%Green';
select: id, object.array[-1] | filter: object.array[-1].foo ilike 'Green%';
select: id, object.array[-1] | filter: object.array[-1].foo ilike '%Green%';
select: id, object.array[-1] | filter: object.array[-1].foo ilike 'ICED%TEA';
