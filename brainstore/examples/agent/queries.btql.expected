[{"error": null, "query": "select: * | from: experiment('singleton') summary", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": 101, "comparison_key": "38a8b8db684c300be6b421ace73aaebbbe04f7fa958864d24daf847547262557", "created": "2024-01-15T10:00:00Z", "id": "req-001-root", "input": {"query": "What is the weather in San Francisco?"}, "metadata": {"session_id": "session-456", "user_id": "user-123"}, "metrics": {"completion_tokens": 0, "errors": 0, "llm_calls": 2, "llm_errors": 0, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 1, "tool_errors": 0, "total_tokens": 0}, "output": {"response": "The weather in San Francisco is currently 65°F and partly cloudy."}, "root_span_id": "req-001-root", "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-001-root", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "request", "remote": 0, "type": "request"}}, {"_pagination_key": "p00000000000006881284", "_xact_id": 105, "comparison_key": "4e5173468d4c9b03437e7b262d20da49c07ce4e75d648db79fdf3ef87db89c47", "created": "2024-01-15T10:01:00Z", "id": "req-002-root", "input": {"query": "Calculate the sum of 1234 and 5678"}, "metadata": {"session_id": "session-457", "user_id": "user-124"}, "metrics": {"completion_tokens": 0, "errors": 0, "llm_calls": 2, "llm_errors": 0, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 1, "tool_errors": 0, "total_tokens": 0}, "output": {"response": "The sum of 1234 and 5678 is 6912."}, "root_span_id": "req-002-root", "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-002-root", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "request", "remote": 0, "type": "request"}}, {"_pagination_key": "p00000000000007143432", "_xact_id": 109, "comparison_key": "ed7af14a72a30617af97803e71d06eac910dad7f9efd4deb872aed88c205e17f", "created": "2024-01-15T10:02:00Z", "error": "Failed to retrieve stock price", "id": "req-003-root", "input": {"query": "Get stock price for INVALID_SYMBOL"}, "metadata": {"session_id": "session-458", "user_id": "user-125"}, "metrics": {"completion_tokens": 0, "errors": 2, "llm_calls": 2, "llm_errors": 0, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 1, "tool_errors": 1, "total_tokens": 0}, "output": {"response": "I encountered an error while trying to get the stock price. The symbol 'INVALID_SYMBOL' was not found."}, "root_span_id": "req-003-root", "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-003-root", "span_type_info": {"cached": 0, "has_error": true, "in_progress": false, "name": "request", "remote": 0, "type": "request"}}, {"_pagination_key": "p00000000000007405580", "_xact_id": 113, "comparison_key": "a6d2023564d7a223697d9436e08c141f5c64c02288b974b033845bf853179b26", "created": "2024-01-15T10:03:00Z", "id": "req-004-root", "input": {"query": "Search for 'machine learning' and summarize the results"}, "metadata": {"session_id": "session-459", "user_id": "user-126"}, "metrics": {"completion_tokens": 0, "errors": 0, "llm_calls": 2, "llm_errors": 0, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 1, "tool_errors": 0, "total_tokens": 0}, "output": "{\"response\":\"I found 3 relevant results about machine learning. The top results discuss supervised learning algorithms, neura...", "root_span_id": "req-004-root", "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-004-root", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "request", "remote": 0, "type": "request"}}, {"_pagination_key": "p00000000000007667728", "_xact_id": 117, "comparison_key": "371d4a2bc7c04e0e2b03203e1f6f509ac1147117bb5015a30a19914e87715e0a", "created": "2024-01-15T10:04:00Z", "error": "Translation service temporarily unavailable", "id": "req-005-root", "input": {"query": "Translate 'Hello world' to French, then check if it's correct"}, "metadata": {"session_id": "session-460", "user_id": "user-127"}, "metrics": {"completion_tokens": 0, "errors": 3, "llm_calls": 2, "llm_errors": 1, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 1, "tool_errors": 1, "total_tokens": 0}, "root_span_id": "req-005-root", "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-005-root", "span_type_info": {"cached": 0, "has_error": true, "in_progress": false, "name": "request", "remote": 0, "type": "request"}}, {"_pagination_key": "p00000000000007929876", "_xact_id": 121, "comparison_key": "83d43c74d3f1c1f52337267b71ec76d19b7d6e1aff3fd5ebe857aa4e2daecc08", "created": "2024-01-15T10:05:00Z", "id": "req-006-root", "input": {"query": "What's the distance between New York and Los Angeles?"}, "metadata": {"session_id": "session-461", "user_id": "user-128"}, "metrics": {"completion_tokens": 0, "errors": 0, "llm_calls": 2, "llm_errors": 0, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 2, "tool_errors": 0, "total_tokens": 0}, "output": "{\"response\":\"The distance between New York and Los Angeles is approximately 2,789 miles (4,489 kilometers) by car, or about 2...", "root_span_id": "req-006-root", "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-006-root", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "request", "remote": 0, "type": "request"}, "tags": ["geography", "distance"]}, {"_pagination_key": "p00000000000008257561", "_xact_id": 126, "comparison_key": "382844de63697b7cfb330c0cac7405f7d507613ce2a0df0cd00cc6fbad85ee4d", "created": "2024-01-15T10:06:00Z", "id": "req-007-root", "input": {"query": "Create a reminder for tomorrow at 3 PM"}, "metadata": {"session_id": "session-462", "user_id": "user-129"}, "metrics": {"completion_tokens": 0, "errors": 0, "llm_calls": 2, "llm_errors": 0, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 0, "tool_calls": 2, "tool_errors": 0, "total_tokens": 0}, "output": {"response": "I've created a reminder for tomorrow at 3:00 PM. You'll receive a notification at that time."}, "root_span_id": "req-007-root", "scores": {"success": 1, "user_satisfaction": 0.95}, "span_attributes": {"name": "request", "type": "request"}, "span_id": "req-007-root", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "request", "remote": 0, "type": "request"}}], "skip": false}]