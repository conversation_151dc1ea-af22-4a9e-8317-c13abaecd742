// Query 1: Get all agent requests
select: * | from: experiment('singleton') summary;

// Query 2: Get only LLM spans
select: * | from: experiment('singleton') summary | filter: span_attributes.type = 'llm';

// Query 3: Get only tool spans
select: * | from: experiment('singleton') summary | filter: span_attributes.type = 'tool';

// Query 4: Get failed requests
select: * | from: experiment('singleton') summary | filter: error IS NOT null;

// Query 5: Get requests with specific tags
select: * | from: experiment('singleton') summary | filter: tags includes ['geography'];

// Query 6: Custom columns with metadata
select: * | from: experiment('singleton') summary | custom_columns: metadata.model as Model, metadata.user_id as User | filter: span_attributes.type = 'llm';

// Query 7: Get tool errors
select: * | from: experiment('singleton') summary | filter: span_attributes.type = 'tool' and error IS NOT null;

// Query 8: Requests with scores
select: * | from: experiment('singleton') summary | filter: scores IS NOT null;

-- We originally had != null for the above queries, but making that work consistently is a can of worms
-- select: * | from: experiment('singleton') summary | filter: span_attributes.type = 'tool' and error != null;
-- select: * | from: experiment('singleton') summary | filter: scores != null;
