-- No match
/*!result -- No match should be null
  .[0].s == -1
*/
measures: COALESCE(sum(is_root), -1) AS s | filter: input='foo' | from: experiment('singleton') traces;

-- This should return 4 rows
/*!result -- This should return 4 spans across 2 traces
  length == 4
 */
from: experiment('singleton') traces | filter: _xact_id=100 OR _xact_id=109 | select: id;

/*!result -- The count should corroborate that
  .[0].c == 2
*/
from: experiment('singleton') spans | filter: _xact_id=100 OR _xact_id=109 | measures: count(1) as c;

/*!result -- In traces mode, it should be 4 though
  .[0].c == 4
*/
from: experiment('singleton') traces | filter: _xact_id=100 OR _xact_id=109 | measures: count(1) as c;

/*!result -- Empty match
  .[0].c == 0
*/
measures: count(1) AS c | from: experiment('singleton') traces | filter: created > NOW();

/*!result -- Only match the one span
  .[0].c == 1
*/
measures: count(1) AS c | from: experiment('singleton') spans | filter: metadata.model='gpt-4';

/*!result -- Match both spans
  .[0].c == 2
*/
measures: count(1) AS c | from: experiment('singleton') traces | filter: metadata.model='gpt-4';

/*!result -- Only match the one span
  .[0].c == 1
*/
measures: count(1) AS c | from: experiment('singleton') spans | filter: metadata.model MATCH 'gpt-4';

/*!result -- Match both spans
  .[0].c == 2
*/
/*!execution -- Pushes down columnar fields. If Retrieve full documents is in the tree, something very bad has happened

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
measures: count(1) AS c | from: experiment('singleton') traces | filter: metadata.model MATCH 'gpt-4';

-- This should return 4 rows
/*!result -- This should return 4 spans across 2 traces
  length == 4
 */
from: experiment('singleton') traces | filter: _xact_id IN [100, 109] | select: id;

/*!result -- The count should corroborate that
  .[0].c == 2
*/
from: experiment('singleton') spans | filter: _xact_id IN [100, 109] | measures: count(1) as c;

/*!result -- In traces mode, it should be 4 though
  .[0].c == 4
*/
from: experiment('singleton') traces | filter: _xact_id IN [100, 109] | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 8
*/
from: experiment('singleton') spans | filter: _xact_id NOT IN [100, 109] | measures: count(1) as c;

/*!result -- In traces mode, it should be 9 though
  .[0].c == 9
*/
from: experiment('singleton') traces | filter: _xact_id NOT IN [100, 109] | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 2
*/
from: experiment('singleton') spans | filter: [100, 109] INCLUDES _xact_id | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 2
*/
from: experiment('singleton') spans | filter: ["100", "109"] INCLUDES _xact_id | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 5
*/
from: experiment('singleton') spans| filter: ["gpt"] INCLUDES metadata.model | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 0
*/
from: experiment('singleton') spans | filter: metadata.model IN ["gpt"] | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 6
*/
from: experiment('singleton') spans| filter: ["gpt", "4"] INCLUDES metadata.model | measures: count(1) as c;

/*!result -- The count should corroborate that
  .[0].c == 0
*/
from: experiment('singleton') spans | filter: metadata.model IN ["gpt", "4"] | measures: count(1) as c;


/*!result -- The count should corroborate that
  .[0].c == 10
*/
from: experiment('singleton') spans | filter: metadata.model INCLUDES [] | measures: count(1) as c;


/*!result -- The count should corroborate that
  .[0].c == 5
*/
from: experiment('singleton') spans | filter: [] INCLUDES metadata.model | measures: count(1) as c;

-- Should error
from: experiment('singleton') spans | filter: metadata.model IN [] | measures: count(1) as c;
from: experiment('singleton') traces | filter: _xact_id IN "100,109" | measures: count(1) as c;
from: experiment('singleton') traces | filter: _xact_id IN metadata.model | measures: count(1) as c;
from: experiment('singleton') traces | filter: ["100", "109"] IN metadata.model | measures: count(1) as c;
from: experiment('singleton') traces | filter: _xact_id IN concat("100", "109") | measures: count(1) as c;
from: experiment('singleton') traces | filter: _xact_id IN {"a": 100, "b": 109} | measures: count(1) as c;
