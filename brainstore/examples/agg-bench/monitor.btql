-- token query
/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time | measures: sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0) AS prompt_tokens, sum(metrics.prompt_cached_tokens) AS prompt_cached_tokens, sum(metrics.prompt_cache_creation_tokens) AS prompt_cache_creation_tokens, sum(metrics.completion_tokens) AS completion_tokens, count(1) AS count;

-- cost query
/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time, metadata.model AS model | measures: sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0) AS prompt_tokens, sum(metrics.prompt_cached_tokens) AS prompt_cached_tokens, sum(metrics.prompt_cache_creation_tokens) AS prompt_cache_creation_tokens, sum(metrics.completion_tokens) AS completion_tokens, count(1) AS count;

-- latency query
/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time | measures: sum(is_root) AS c, percentile(metrics.end-metrics.start, 0.5) AS p50, percentile(is_root ? metrics.end-metrics.start : null, 0.5) AS p50_root, percentile(is_root ? metrics.end-metrics.start : null, 0.95) AS p95_root;

-- ttft query
/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time | measures: percentile(metrics.time_to_first_token, 0.5) AS p50_time_to_first_token, percentile(metrics.time_to_first_token, 0.95) AS p95_time_to_first_token;

-- request count query
/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time | measures: sum(is_root) AS count, sum(is_root ? 1 : 0) as ternary_count, count(1) AS spans, sum(span_attributes.type = 'llm' ? 1 : 0) AS llm_spans, sum(span_attributes.type = 'tool' ? 1 : 0) AS tool_count;
