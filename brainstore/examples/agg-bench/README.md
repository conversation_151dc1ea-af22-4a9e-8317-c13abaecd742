# Group by benchmark

This benchmark generates sample data that we lets us benchmark group by performance.

## Generating and loading the data

```bash

python generate.py
cargo run --release --bin brainstore -- wal -c bench.yaml load data/* --normalize
```

## Running the benchmark

To run a query with timing metrics, run:

```bash
time cargo flamegraph --root --release --bin brainstore -- btql query -c bench.yaml 'dimensions: day(created) | measures: count(1) | filter: created >= NOW() - INTERVAL 7 DAY' -v
```

Here are a few other queries you might want to run:

```sql
measures: count(1)
```

```sql
measures: sum(is_root)
```

```sql
measures: sum(is_root ? 1 : 0)
```

```sql
measures: count(1)
dimensions: day(created) | measures: count(1)
```
