[{"error": null, "query": "select: *", "result_rows": [{"metadata": {"a": [{"foo": 1}, {"bar": 2}], "model": "gpt-4o"}}, {"metadata": {"completion_tokens": 320, "model": "gpt-4", "prompt_tokens": 450, "total_cost": 0.023}, "scores": {"a": 1}}, {"metadata": {"max_tokens": 1000, "model": "gpt-3.5-turbo", "temperature": 0.7}}, {"metadata": {"model": "claude-2", "streaming": true, "tags": ["draft", "experimental"]}}], "skip": false}, {"error": null, "query": "-- We know this is a string, so it should use a fast field.\nselect: metadata.model", "result_rows": [{"model": "claude-2"}, {"model": "gpt-3.5-turbo"}, {"model": "gpt-4"}, {"model": "gpt-4o"}], "skip": false}, {"error": null, "query": "-- We don't know this field's type, so it should not use a fast field.\nselect: metadata.temperature", "result_rows": [null, null, null, {"temperature": 0.7}], "skip": false}]