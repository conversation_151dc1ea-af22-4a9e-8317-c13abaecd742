version: 1
schema:
  name: test
  fields:
    - name: metadata
      tantivy:
        - name: metadata_json
          type: !json
            stored: true
            tokenize: true
            fast: true
          lossy_fast_field: true
    - name: scores
      tantivy:
        - name: scores_json
          type: !json
            stored: true
            tokenize: true
            fast: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
