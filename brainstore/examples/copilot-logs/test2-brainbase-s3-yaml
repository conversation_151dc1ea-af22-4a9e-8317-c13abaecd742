version: 1
schema:
  name: copilot-logs
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: _xact_id
      tantivy:
        - name: _xact_id
          type: !u64
            stored: true
            fast: true
            tokenize: false
    - name: log_id
      tantivy:
        - name: log_id
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: org_id
      tantivy:
        - name: org_id
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: project_id
      tantivy:
        - name: project_id
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: span_id
      tantivy:
        - name: span_id
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: root_span_id
      tantivy:
        - name: root_span_id
          type: !str
            stored: true
            fast: true
            tokenize: false
    - name: span_parents
      tantivy:
        - name: span_parents
          type: !json
            stored: true
            tokenize: false
    - name: span_attributes
      tantivy:
        - name: span_attributes
          type: !json
            stored: true
            tokenize: false
    - name: created
      tantivy:
        - name: created
          type: !date
            stored: true
            fast: true
            tokenize: false
    - name: input
      tantivy:
        - name: input_json
          type: !json
            stored: true
            tokenize: false
        - name: input_text
          type: !str
            tokenize: true
            stored: false
    - name: output
      tantivy:
        - name: output_json
          type: !json
            stored: true
            tokenize: false
        - name: output_text
          type: !str
            tokenize: true
            stored: false
    - name: metadata
      tantivy:
        - name: metadata_json
          type: !json
            stored: true
            tokenize: false
        - name: metadata_text
          type: !str
            tokenize: true
            stored: false
    - name: metrics
      tantivy:
        - name: metrics_json
          type: !json
            stored: true
            tokenize: false
    - name: scores
      tantivy:
        - name: scores_json
          type: !json
            stored: true
            tokenize: false
    - name: context
      tantivy:
        - name: context_json
          type: !json
            stored: true
            tokenize: false

metadata_uri: s3://braintrust-data-benchmark/load-flat/index-2/base/metadata
index_uri: s3://braintrust-data-benchmark/load-flat/index-2/base/data
wal_uri: s3://braintrust-data-benchmark/load-flat/index-2/base/wal
