#!/bin/bash

# Read AWS credentials from ~/.aws/credentials
if [ -f ~/.aws/credentials ]; then
    # Extract the default profile credentials
    AWS_ACCESS_KEY_ID=$(grep -A3 "\[default\]" ~/.aws/credentials | grep "aws_access_key_id" | cut -d "=" -f2 | tr -d " ")
    AWS_SECRET_ACCESS_KEY=$(grep -A3 "\[default\]" ~/.aws/credentials | grep "aws_secret_access_key" | cut -d "=" -f2 | tr -d " ")
    AWS_REGION=$(grep -A3 "\[default\]" ~/.aws/credentials | grep "region" | cut -d "=" -f2 | tr -d " ")

    # Export the AWS environment variables
    export AWS_ACCESS_KEY_ID
    export AWS_SECRET_ACCESS_KEY
    export AWS_REGION

    echo "AWS credentials have been set from ~/.aws/credentials"
else
    echo "Error: ~/.aws/credentials file not found"
    exit 1
fi

# Read variables from .env.local if it exists
if [ -f .env.local ]; then
    echo "Reading variables from .env.local"
    while IFS='=' read -r key value
    do
        # Remove leading/trailing whitespace and quotes from the value
        value=$(echo "$value" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' -e 's/^"//' -e 's/"$//')
        # Export the variable
        export "$key=$value"
        echo "Exported $key"
    done < .env.local
else
    echo "Note: .env.local file not found, skipping"
fi
