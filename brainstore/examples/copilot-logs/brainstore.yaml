version: 1
schema:
  name: copilot-logs
  fields:
    - name: span_attributes
      tantivy:
        - name: span_attributes
          type: !json
            stored: true
            tokenize: false
    - name: created
      tantivy:
        - name: created
          type: !date
            stored: true
            fast: true
            tokenize: false
    - name: input
      tantivy:
        - name: input_json
          type: !json
            stored: true
            fast: true
            tokenize: false
        - name: input_text
          type: !str
            tokenize: true
            stored: false
    - name: output
      tantivy:
        - name: output_json
          type: !json
            stored: true
            fast: true
            tokenize: false
        - name: output_text
          type: !str
            tokenize: true
            stored: false
    - name: metadata
      tantivy:
        - name: metadata_json
          type: !json
            stored: true
            fast: true
            tokenize: false
        - name: metadata_text
          type: !str
            tokenize: true
            stored: false
    - name: metrics
      tantivy:
        - name: metrics_json
          type: !json
            stored: true
            fast: true
            tokenize: false
    - name: scores
      tantivy:
        - name: scores_json
          type: !json
            stored: true
            fast: true
            tokenize: false
    - name: context
      tantivy:
        - name: context_json
          type: !json
            stored: true
            fast: true
            tokenize: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
