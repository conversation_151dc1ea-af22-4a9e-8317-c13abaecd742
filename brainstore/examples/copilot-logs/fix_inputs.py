import argparse
import json


def fix_inputs(file_path):
    with open(file_path, "r") as file:
        lines = file.readlines()

    fixed_lines = []
    for line in lines:
        try:
            data = json.loads(line)
            if "inputs" in data:
                data["input"] = data.pop("inputs")
            fixed_lines.append(json.dumps(data) + "\n")
        except json.JSONDecodeError:
            fixed_lines.append(line)

    with open(file_path, "w") as file:
        file.writelines(fixed_lines)


def main():
    parser = argparse.ArgumentParser(description='Fix "inputs" key to "input" in JSON files.')
    parser.add_argument("files", nargs="+", help="JSON files to process")
    args = parser.parse_args()

    for file_path in args.files:
        fix_inputs(file_path)


if __name__ == "__main__":
    main()
