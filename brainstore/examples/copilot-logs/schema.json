{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "_xact_id": {"type": "number"}, "log_id": {"type": "string"}, "org_id": {"type": "string"}, "project_id": {"type": "string"}, "span_id": {"type": "string"}, "root_span_id": {"type": "string"}, "span_parents": {"type": "string"}, "span_attributes": {"type": "string"}, "created": {"type": "string", "format": "date-time"}, "input": {"type": "object", "additionalProperties": {}}, "output": {"type": "object", "additionalProperties": {}}, "metadata": {"type": "object", "additionalProperties": {}}, "metrics": {"type": "object", "additionalProperties": {}}, "scores": {"type": "object", "additionalProperties": {}}, "context": {"type": "object", "additionalProperties": {}}}, "required": ["id", "_xact_id", "log_id", "org_id", "project_id", "span_id", "root_span_id", "created"]}}