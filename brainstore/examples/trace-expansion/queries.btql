select: * | from: dataset("singleton") spans | filter: s MATCH "foo";
select: * | from: dataset("singleton") traces | filter: s MATCH "foo";

select: * | from: dataset("singleton") spans | filter: s MATCH "foo bar";
select: * | from: dataset("singleton") traces | filter: s MATCH "foo bar";

-- This should also match everything but requires running a filter outside
select: * | from: dataset("singleton") spans | filter: s="baz";
select: * | from: dataset("singleton") traces | filter: s="baz";
