version: 1
schema:
  name: test
  fields:
    - name: i
      tantivy:
        - name: i
          type: !u64
            stored: true
            fast: true
    - name: s
      tantivy:
        - name: s
          type: !str
            stored: true
            tokenize: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/notok/metadata
index_uri: ./index/notok/data
wal_uri: ./index/notok/wal
realtime_wal_uri: ./index/notok/wal
