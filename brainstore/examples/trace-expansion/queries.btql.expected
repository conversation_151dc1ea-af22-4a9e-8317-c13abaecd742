[{"error": null, "query": "select: * | from: dataset(\"singleton\") spans | filter: s MATCH \"foo\"", "result_rows": [{"i": null, "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1", "span_parents": null}, {"i": null, "id": "3", "root_span_id": "1", "s": "foo bar", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "select: * | from: dataset(\"singleton\") traces | filter: s MATCH \"foo\"", "result_rows": [{"i": null, "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1", "span_parents": null}, {"i": null, "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"i": null, "id": "3", "root_span_id": "1", "s": "foo bar", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "select: * | from: dataset(\"singleton\") spans | filter: s MATCH \"foo bar\"", "result_rows": [{"i": null, "id": "3", "root_span_id": "1", "s": "foo bar", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "select: * | from: dataset(\"singleton\") traces | filter: s MATCH \"foo bar\"", "result_rows": [{"i": null, "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1", "span_parents": null}, {"i": null, "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"i": null, "id": "3", "root_span_id": "1", "s": "foo bar", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "-- This should also match everything but requires running a filter outside\nselect: * | from: dataset(\"singleton\") spans | filter: s=\"baz\"", "result_rows": [{"i": null, "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "query": "select: * | from: dataset(\"singleton\") traces | filter: s=\"baz\"", "result_rows": [{"i": null, "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1", "span_parents": null}, {"i": null, "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"i": null, "id": "3", "root_span_id": "1", "s": "foo bar", "span_id": "3", "span_parents": ["2"]}], "skip": false}]