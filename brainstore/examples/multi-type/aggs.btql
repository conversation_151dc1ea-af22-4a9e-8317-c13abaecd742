measures: count(object);
measures: sum(object IS NOT NULL);
measures: sum(object IS NULL);

-- We have messed up semantics for projecting object.foo if it's in an array or not
-- because of columnstore invertibility. When we fix that, we can change events.jsonl
-- to put 'foo' in the array instead of array_foo
measures: count(object.foo);
measures: sum(object.foo IS NOT NULL);
measures: sum(object.foo IS NULL);

-- Make sure normal expressions don't get converted to expect checks
measures: count(object = 42 ? 1 : NULL);

measures: count(object.foo) + count(object.foo);
measures: count(object.foo) + sum(object.foo IS NULL);

-- Add a bunch of explicit type casts to ensure nothing breaks
measures: count(to_string(object));
measures: count(to_number(object));
measures: count(to_integer(object));
measures: count(to_boolean(object));
measures: count(to_date(object));
measures: count(to_datetime(object));

measures: sum(to_integer(object IS NOT NULL));
measures: sum(to_number(object IS NULL));

measures: sum(to_integer(to_boolean(object IS NULL)));
measures: sum(to_string(to_integer(object IS NULL)));

measures: count(to_string(object.foo));
measures: count(to_number(object.foo));
measures: count(to_boolean(object.foo));
measures: count(to_date(object.foo));
measures: count(to_datetime(object.foo));

measures: sum(to_integer(object.foo IS NOT NULL));
measures: sum(to_number(object.foo IS NULL));

measures: sum(to_integer(to_boolean(object.foo IS NULL)));
measures: sum(to_string(to_integer(object.foo IS NULL)));

measures: count(to_string(to_number(object = 42 ? 1 : NULL)));

measures: count(to_integer(object.foo)) + count(to_number(object.foo));
measures: count(to_integer(to_string(object.foo))) + sum(to_integer(to_boolean(object.foo IS NULL)));

measures: sum(to_integer(to_string(object) IS NULL));
measures: sum(to_integer(to_number(object) IS NULL));
measures: sum(to_integer(to_boolean(object) IS NULL));
measures: sum(to_integer(to_date(object) IS NULL));
measures: sum(to_integer(to_datetime(object) IS NULL));

measures: count(to_string(to_number(to_string(object))));
measures: count(to_integer(to_boolean(to_number(object))));
measures: count(to_string(to_date(to_boolean(object))));
measures: count(to_string(to_datetime(to_date(object))));

measures: sum(to_integer(to_string(to_number(object.foo)) IS NULL));
measures: sum(to_integer(to_boolean(to_number(object.foo)) IS NULL));
measures: sum(to_integer(to_date(to_boolean(object.foo)) IS NULL));
measures: sum(to_integer(to_datetime(to_date(object.foo)) IS NULL));
