[{"error": null, "query": "measures: count(object)", "result_rows": [{"count(object)": 8}], "skip": false}, {"error": null, "query": "measures: sum(object IS NOT NULL)", "result_rows": [{"sum(object IS NOT NULL)": 8}], "skip": false}, {"error": null, "query": "measures: sum(object IS NULL)", "result_rows": [{"sum(object IS NULL)": 1}], "skip": false}, {"error": null, "query": "-- We have messed up semantics for projecting object.foo if it's in an array or not\n-- because of columnstore invertibility. When we fix that, we can change events.jsonl\n-- to put 'foo' in the array instead of array_foo\nmeasures: count(object.foo)", "result_rows": [{"count(object.foo)": 1}], "skip": false}, {"error": null, "query": "measures: sum(object.foo IS NOT NULL)", "result_rows": [{"sum(object.foo IS NOT NULL)": 1}], "skip": false}, {"error": null, "query": "measures: sum(object.foo IS NULL)", "result_rows": [{"sum(object.foo IS NULL)": 8}], "skip": false}, {"error": null, "query": "-- Make sure normal expressions don't get converted to expect checks\nmeasures: count(object = 42 ? 1 : NULL)", "result_rows": [{"count(object = 42 ? 1 : NULL)": 1}], "skip": false}, {"error": null, "query": "measures: count(object.foo) + count(object.foo)", "result_rows": [{"count(object.foo) + count(object.foo)": 2}], "skip": false}, {"error": null, "query": "measures: count(object.foo) + sum(object.foo IS NULL)", "result_rows": [{"count(object.foo) + sum(object.foo IS NULL)": 9}], "skip": false}, {"error": null, "query": "-- Add a bunch of explicit type casts to ensure nothing breaks\nmeasures: count(to_string(object))", "result_rows": [{"count(to_string(object))": 8}], "skip": false}, {"error": null, "query": "measures: count(to_number(object))", "result_rows": [{"count(to_number(object))": 3}], "skip": false}, {"error": null, "query": "measures: count(to_integer(object))", "result_rows": [{"count(to_integer(object))": 3}], "skip": false}, {"error": null, "query": "measures: count(to_boolean(object))", "result_rows": [{"count(to_boolean(object))": 7}], "skip": false}, {"error": null, "query": "measures: count(to_date(object))", "result_rows": [{"count(to_date(object))": 0}], "skip": false}, {"error": null, "query": "measures: count(to_datetime(object))", "result_rows": [{"count(to_datetime(object))": 2}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(object IS NOT NULL))", "result_rows": [{"sum(to_integer(object IS NOT NULL))": 8}], "skip": false}, {"error": null, "query": "measures: sum(to_number(object IS NULL))", "result_rows": [{"sum(to_number(object IS NULL))": 1}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_boolean(object IS NULL)))", "result_rows": [{"sum(to_integer(to_boolean(object IS NULL)))": 1}], "skip": false}, {"error": null, "query": "measures: sum(to_string(to_integer(object IS NULL)))", "result_rows": [{"sum(to_string(to_integer(object IS NULL)))": 1}], "skip": false}, {"error": null, "query": "measures: count(to_string(object.foo))", "result_rows": [{"count(to_string(object.foo))": 1}], "skip": false}, {"error": null, "query": "measures: count(to_number(object.foo))", "result_rows": [{"count(to_number(object.foo))": 0}], "skip": false}, {"error": null, "query": "measures: count(to_boolean(object.foo))", "result_rows": [{"count(to_boolean(object.foo))": 0}], "skip": false}, {"error": null, "query": "measures: count(to_date(object.foo))", "result_rows": [{"count(to_date(object.foo))": 0}], "skip": false}, {"error": null, "query": "measures: count(to_datetime(object.foo))", "result_rows": [{"count(to_datetime(object.foo))": 0}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(object.foo IS NOT NULL))", "result_rows": [{"sum(to_integer(object.foo IS NOT NULL))": 1}], "skip": false}, {"error": null, "query": "measures: sum(to_number(object.foo IS NULL))", "result_rows": [{"sum(to_number(object.foo IS NULL))": 8}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_boolean(object.foo IS NULL)))", "result_rows": [{"sum(to_integer(to_boolean(object.foo IS NULL)))": 8}], "skip": false}, {"error": null, "query": "measures: sum(to_string(to_integer(object.foo IS NULL)))", "result_rows": [{"sum(to_string(to_integer(object.foo IS NULL)))": 8}], "skip": false}, {"error": null, "query": "measures: count(to_string(to_number(object = 42 ? 1 : NULL)))", "result_rows": [{"count(to_string(to_number(object = 42 ? 1 : NULL)))": 1}], "skip": false}, {"error": null, "query": "measures: count(to_integer(object.foo)) + count(to_number(object.foo))", "result_rows": [{"count(to_integer(object.foo)) + count(to_number(object.foo))": 0}], "skip": false}, {"error": null, "query": "measures: count(to_integer(to_string(object.foo))) + sum(to_integer(to_boolean(object.foo IS NULL)))", "result_rows": [{"count(to_integer(to_string(object.foo))) + sum(to_integer(to_boolean(object.foo IS NULL)))": 8}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_string(object) IS NULL))", "result_rows": [{"sum(to_integer(to_string(object) IS NULL))": 1}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_number(object) IS NULL))", "result_rows": [{"sum(to_integer(to_number(object) IS NULL))": 6}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_boolean(object) IS NULL))", "result_rows": [{"sum(to_integer(to_boolean(object) IS NULL))": 2}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_date(object) IS NULL))", "result_rows": [{"sum(to_integer(to_date(object) IS NULL))": 9}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_datetime(object) IS NULL))", "result_rows": [{"sum(to_integer(to_datetime(object) IS NULL))": 7}], "skip": false}, {"error": null, "query": "measures: count(to_string(to_number(to_string(object))))", "result_rows": [{"count(to_string(to_number(to_string(object))))": 2}], "skip": false}, {"error": null, "query": "measures: count(to_integer(to_boolean(to_number(object))))", "result_rows": [{"count(to_integer(to_boolean(to_number(object))))": 3}], "skip": false}, {"error": null, "query": "measures: count(to_string(to_date(to_boolean(object))))", "result_rows": [{"count(to_string(to_date(to_boolean(object))))": 0}], "skip": false}, {"error": null, "query": "measures: count(to_string(to_datetime(to_date(object))))", "result_rows": [{"count(to_string(to_datetime(to_date(object))))": 0}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_string(to_number(object.foo)) IS NULL))", "result_rows": [{"sum(to_integer(to_string(to_number(object.foo)) IS NULL))": 9}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_boolean(to_number(object.foo)) IS NULL))", "result_rows": [{"sum(to_integer(to_boolean(to_number(object.foo)) IS NULL))": 9}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_date(to_boolean(object.foo)) IS NULL))", "result_rows": [{"sum(to_integer(to_date(to_boolean(object.foo)) IS NULL))": 9}], "skip": false}, {"error": null, "query": "measures: sum(to_integer(to_datetime(to_date(object.foo)) IS NULL))", "result_rows": [{"sum(to_integer(to_datetime(to_date(object.foo)) IS NULL))": 9}], "skip": false}]