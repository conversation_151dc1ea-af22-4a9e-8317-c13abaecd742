from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key asc;
from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAA;
from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAADAAE;
from: dataset('singleton') spans | select: * | limit: 4 | sort: _pagination_key asc;
from: dataset('singleton') spans | select: * | limit: 5 | sort: _pagination_key asc;
from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key desc;
from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAADAAE;
from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAA;
from: dataset('singleton') spans | select: * | limit: 4 | sort: _pagination_key desc;
from: dataset('singleton') spans | select: * | limit: 5 | sort: _pagination_key desc;

from: dataset('singleton') | select: * | limit: 1 | filter: s contains "foo" | sort: _pagination_key asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s contains "foo" | sort: _pagination_key desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s contains "zzz" | sort: _pagination_key asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s contains "zzz" | sort: _pagination_key desc;

from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key asc;
from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key asc | cursor: AAAAAAABAAA;
from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key asc | cursor: AAAAAAADAAE;
from: dataset('singleton') spans | select: * | limit: 4 | filter: s not contains "zzz" | sort: _pagination_key asc;
from: dataset('singleton') spans | select: * | limit: 5 | filter: s not contains "zzz" | sort: _pagination_key asc;
from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc;
from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc | cursor: AAAAAAADAAE;
from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc | cursor: AAAAAAABAAA;
from: dataset('singleton') spans | select: * | limit: 4 | filter: s not contains "zzz" | sort: _pagination_key desc;
from: dataset('singleton') spans | select: * | limit: 5 | filter: s not contains "zzz" | sort: _pagination_key desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _pagination_key asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _pagination_key asc | cursor: AAAAAAABAAI;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _pagination_key desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _pagination_key desc | cursor: AAAAAAAAAAE;

from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key asc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key asc | cursor: AAAAAAABAAA;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key asc | cursor: AAAAAAADAAE;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc | cursor: AAAAAAADAAE;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc | cursor: AAAAAAABAAA;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc | cursor: AAAAAAAAAAI;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _pagination_key desc | cursor: AAAAAAAAAAE;
