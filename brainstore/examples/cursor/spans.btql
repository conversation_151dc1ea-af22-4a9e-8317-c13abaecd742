/*!result -- This should return just one row (transaction id 3)
length == 1 and .[0]._xact_id == 3
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc;
/*!result -- This should return just one row (transaction id 2)
length == 1 and .[0]._xact_id == 2
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAADAAE;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 1
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAACAAA;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 1
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAI;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 1
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAE;

-- Test the same thing, but ascending
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 1
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 1
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAA;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 1
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAE;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 2
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAI;
/*!result -- This should return just one row (transaction id 1)
length == 1 and .[0]._xact_id == 3
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAACAAA;

-- We're done paginating so this should return nothing
from: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAA;

from: dataset('singleton') spans | select: * | filter: s='bar baz' | sort: _pagination_key desc;
-- This should return nothing
from: dataset('singleton') spans | select: * | filter: s='bar baz' | sort: _pagination_key desc | cursor: AAAAAAACAAA;

-- Filter on a pagination key
from: dataset('singleton') | select: * | filter: _pagination_key = 'p00000000000000065536';
from: dataset('singleton') | select: * | filter: _pagination_key != 'p00000000000000065536';
from: dataset('singleton') | select: * | filter: _pagination_key > 'p00000000000000065536';
from: dataset('singleton') | select: * | filter: _pagination_key < 'p00000000000000065536';

-- These produce different results with pushdown vs. not, because pushed down, they match
-- the underlying u64 representation of the pagination key. We could theoretically solve
-- this but it's not worth it.
-- from: dataset('singleton') | select: * | filter: _pagination_key = '00000000000000065536';
-- from: dataset('singleton') | select: * | filter: _pagination_key != '00000000000000065536';
-- from: dataset('singleton') | select: * | filter: _pagination_key > '00000000000000065536';
-- from: dataset('singleton') | select: * | filter: _pagination_key < '00000000000000065536';

/*!result --
length == 1 and .[0]._xact_id == 3
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc;
/*!result --
length == 1 and .[0]._xact_id == 2
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAM;
/*!result -- This should return three rows, even though limit is 1, because there are three rows with the same xact_id
length == 3
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAI;

/*!result --
length == 3
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc;
/*!result --
length == 1 and .[0]._xact_id == 2
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAE;
/*!result --
length == 1 and .[0]._xact_id == 3
*/
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAI;

from: dataset('singleton') | select: * | filter: root_span_id='4';
