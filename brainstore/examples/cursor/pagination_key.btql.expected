[{"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAA", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAADAAE", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "query": "from: dataset('singleton') spans | select: * | limit: 4 | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "from: dataset('singleton') spans | select: * | limit: 5 | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAADAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAA", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "query": "from: dataset('singleton') spans | select: * | limit: 4 | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "from: dataset('singleton') spans | select: * | limit: 5 | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s contains \"foo\" | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s contains \"foo\" | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s contains \"zzz\" | sort: _pagination_key asc", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s contains \"zzz\" | sort: _pagination_key desc", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key asc | cursor: AAAAAAABAAA", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key asc | cursor: AAAAAAADAAE", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "query": "from: dataset('singleton') spans | select: * | limit: 4 | filter: s not contains \"zzz\" | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "from: dataset('singleton') spans | select: * | limit: 5 | filter: s not contains \"zzz\" | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "query": "from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc | cursor: AAAAAAADAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc | cursor: AAAAAAABAAA", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "query": "from: dataset('singleton') spans | select: * | limit: 4 | filter: s not contains \"zzz\" | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "from: dataset('singleton') spans | select: * | limit: 5 | filter: s not contains \"zzz\" | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s not contains \"foo\" | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s not contains \"foo\" | sort: _pagination_key asc | cursor: AAAAAAABAAI", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s not contains \"foo\" | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | limit: 1 | filter: s not contains \"foo\" | sort: _pagination_key desc | cursor: AAAAAAAAAAE", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key asc | cursor: AAAAAAABAAA", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key asc | cursor: AAAAAAADAAE", "result_rows": [], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc | cursor: AAAAAAADAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc | cursor: AAAAAAABAAA", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc | cursor: AAAAAAAAAAI", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains \"zzz\" | sort: _pagination_key desc | cursor: AAAAAAAAAAE", "result_rows": [], "skip": false}]