version: 1
schema:
  name: test
  fields:
    - name: i
      tantivy:
        - name: i
          type: !u64
            stored: true
            fast: true
    - name: s
      tantivy:
        - name: s
          type: !str
            stored: true
            tokenize: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
