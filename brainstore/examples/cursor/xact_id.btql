from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAI;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAM;
from: dataset('singleton') | select: * | limit: 2 | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 2 | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 4 | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAM;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAI;
from: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 2 | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 2 | sort: _xact_id desc | cursor: AAAAAAAAAAM;
from: dataset('singleton') | select: * | limit: 4 | sort: _xact_id desc;

from: dataset('singleton') | select: * | limit: 1 | filter: s contains "foo" | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s contains "foo" | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s contains "zzz" | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s contains "zzz" | sort: _xact_id desc;

from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc | cursor: AAAAAAAAAAI;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc | cursor: AAAAAAAAAAM;
from: dataset('singleton') | select: * | limit: 2 | filter: s not contains "zzz" | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 2 | filter: s not contains "zzz" | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 4 | filter: s not contains "zzz" | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc | cursor: AAAAAAAAAAM;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc | cursor: AAAAAAAAAAI;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 2 | filter: s not contains "zzz" | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 2 | filter: s not contains "zzz" | sort: _xact_id desc | cursor: AAAAAAAAAAM;
from: dataset('singleton') | select: * | limit: 4 | filter: s not contains "zzz" | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _xact_id asc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _xact_id desc;
from: dataset('singleton') | select: * | limit: 1 | filter: s not contains "foo" | sort: _xact_id desc | cursor: AAAAAAAAAAE;

from: dataset('singleton') traces | select: * | limit: 1 | filter: s contains "foo" | sort: _xact_id asc;
from: dataset('singleton') traces | select: * | limit: 2 | filter: s contains "foo" | sort: _xact_id asc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s contains "baz" | sort: _xact_id asc;
from: dataset('singleton') traces | select: * | limit: 2 | filter: s contains "baz" | sort: _xact_id asc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s contains "foo" | sort: _xact_id desc;
from: dataset('singleton') traces | select: * | limit: 2 | filter: s contains "foo" | sort: _xact_id desc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s contains "baz" | sort: _xact_id desc;
from: dataset('singleton') traces | select: * | limit: 2 | filter: s contains "baz" | sort: _xact_id desc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id asc | cursor: AAAAAAAAAAI;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc | cursor: AAAAAAAAAAI;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "zzz" | sort: _xact_id desc | cursor: AAAAAAAAAAE;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "bar" | sort: _xact_id asc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "bar" | sort: _xact_id asc | cursor: AAAAAAAAAAE;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "bar" | sort: _xact_id desc;
from: dataset('singleton') traces | select: * | limit: 1 | filter: s not contains "bar" | sort: _xact_id desc | cursor: AAAAAAAAAAE;
