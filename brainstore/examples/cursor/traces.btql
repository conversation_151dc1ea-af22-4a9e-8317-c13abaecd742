-- This tests that limits properly apply to traces rather than spans
select: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 2;

-- This should return 3 rows (all corresponding to root_span_id='1')
select: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 1;

-- This should return 1 row (corresponding to root_span_id='4')
select: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 1 | cursor: AAAAAAADAAE;

-- This should return no rows, since we exhausted the first root span already
select: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 1 | cursor: AAAAAAACAAA;

-- Even though we don't project root_span_id, this should work
select: span_parents | from: dataset("singleton") traces;
unpivot: span_parents as sp | select: sp | from: dataset("singleton") traces;

/*!result -- This should return both traces (one of them has 3 spans)
length == 4
*/
from: dataset('singleton') traces | select: * | limit: 1 | sort: _xact_id asc;

/*!result -- This should return the remaining trace
length == 1
*/
from: dataset('singleton') traces | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAE;

/*!result -- This should return both traces (one of them has 3 spans)
length == 0
*/
from: dataset('singleton') traces | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAI;
