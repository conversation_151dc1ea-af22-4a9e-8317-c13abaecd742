version: 1
schema:
  name: test
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
    - name: scores
      tantivy:
        - name: scores
          type: !json
            stored: true
    - name: counts
      tantivy:
        - name: counts
          type: !json
            stored: true
    - name: metadata
      tantivy:
        - name: metadata_tok
          type: !json
            tokenize: true
            stored: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/mini/metadata
index_uri: ./index/mini/data
wal_uri: ./index/mini/wal
realtime_wal_uri: ./index/mini/wal
