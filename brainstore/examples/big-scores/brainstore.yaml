version: 1
schema:
  name: test
  fields:
    - name: id
      tantivy:
        - name: id
          type: !str
            stored: true
            fast: true
    - name: scores
      tantivy:
        - name: scores
          type: !json
            stored: true
            fast: true
    - name: counts
      tantivy:
        - name: counts
          type: !json
            stored: true
            fast: true
    - name: metadata
      tantivy:
        - name: metadata_untok
          type: !json
            stored: true
            fast: true
        - name: metadata_tok
          type: !json
            tokenize: true
            stored: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
