{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "scores": {"type": "object", "additionalProperties": {"type": "number", "minimum": 0, "maximum": 1}, "default": {}}, "counts": {"type": "object", "additionalProperties": {"type": "integer"}, "default": {}}, "metadata": {"type": "object", "additionalProperties": {}}}, "required": ["id"]}}