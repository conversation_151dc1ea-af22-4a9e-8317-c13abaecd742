[{"error": null, "next_page_using_returned_cursor": [{"id": "0e8fd3ce-100e-4eb3-bab1-8e67b18c1685"}, {"id": "3a12d5c4-51cf-45f5-a418-4dfdea4d8dd8"}, {"id": "9299d49e-5d73-446f-972a-a054969110e2"}, {"id": "98a5da2f-8e94-41c1-ae54-a8f856f9b10e"}, {"id": "9ee5eae6-4022-4acc-a02f-9e7d495163a7"}, {"id": "d5f6967c-6f29-4d1d-b62c-4651bc7897c3"}], "query": "-- These queries are not selective, so they will have a large batch size\n\n/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"batch_size\"))] | .[0].batch_size == 1000\n\n*/\nselect: id | from: experiment('singleton') traces | limit: 1 | sort: _pagination_key DESC", "result_rows": [{"id": "35899e75-53de-4c15-bd01-8b21f9b75b31"}, {"id": "4f948a0e-a702-4c6c-8d7a-c286af4d1c23"}, {"id": "babdc1ac-2c2f-4289-98ff-33549005bc58"}, {"id": "c8e2699f-c9a4-48d8-8e6f-ccc447d8a793"}, {"id": "d7841d8a-f9f5-46fd-8e3a-4e234ca1e85a"}, {"id": "db33663a-58b6-410c-8b47-8855ced39e02"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"id": "0e8fd3ce-100e-4eb3-bab1-8e67b18c1685"}, {"id": "3a12d5c4-51cf-45f5-a418-4dfdea4d8dd8"}, {"id": "9299d49e-5d73-446f-972a-a054969110e2"}, {"id": "98a5da2f-8e94-41c1-ae54-a8f856f9b10e"}, {"id": "9ee5eae6-4022-4acc-a02f-9e7d495163a7"}, {"id": "d5f6967c-6f29-4d1d-b62c-4651bc7897c3"}], "query": "/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"batch_size\"))] | .[0].batch_size == 1000\n\n*/\nselect: id | from: experiment('singleton') traces | limit: 1 | filter: created < NOW() - INTERVAL 1 HOUR | sort: _pagination_key DESC", "result_rows": [{"id": "35899e75-53de-4c15-bd01-8b21f9b75b31"}, {"id": "4f948a0e-a702-4c6c-8d7a-c286af4d1c23"}, {"id": "babdc1ac-2c2f-4289-98ff-33549005bc58"}, {"id": "c8e2699f-c9a4-48d8-8e6f-ccc447d8a793"}, {"id": "d7841d8a-f9f5-46fd-8e3a-4e234ca1e85a"}, {"id": "db33663a-58b6-410c-8b47-8855ced39e02"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"id": "0e8fd3ce-100e-4eb3-bab1-8e67b18c1685"}, {"id": "3a12d5c4-51cf-45f5-a418-4dfdea4d8dd8"}, {"id": "9299d49e-5d73-446f-972a-a054969110e2"}, {"id": "98a5da2f-8e94-41c1-ae54-a8f856f9b10e"}, {"id": "9ee5eae6-4022-4acc-a02f-9e7d495163a7"}, {"id": "d5f6967c-6f29-4d1d-b62c-4651bc7897c3"}], "query": "/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"batch_size\"))] | .[0].batch_size == 1000\n\n*/\nselect: id | from: experiment('singleton') traces | limit: 1 | filter: created < NOW() - INTERVAL 1 HOUR OR input=1 | sort: _pagination_key DESC", "result_rows": [{"id": "35899e75-53de-4c15-bd01-8b21f9b75b31"}, {"id": "4f948a0e-a702-4c6c-8d7a-c286af4d1c23"}, {"id": "babdc1ac-2c2f-4289-98ff-33549005bc58"}, {"id": "c8e2699f-c9a4-48d8-8e6f-ccc447d8a793"}, {"id": "d7841d8a-f9f5-46fd-8e3a-4e234ca1e85a"}, {"id": "db33663a-58b6-410c-8b47-8855ced39e02"}], "skip": false}, {"error": null, "query": "-- These queries should be selective though\n\n/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"batch_size\"))] | .[0].batch_size == 10\n\n*/\nselect: id | from: experiment('singleton') traces | limit: 1 | filter: input=1 | sort: _pagination_key DESC", "result_rows": [], "skip": false}, {"error": null, "query": "/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"batch_size\"))] | .[0].batch_size == 10\n\n*/\nselect: id | from: experiment('singleton') traces | limit: 1 | filter: input=1 OR input=2 | sort: _pagination_key DESC", "result_rows": [], "skip": false}, {"error": null, "query": "/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"batch_size\"))] | .[0].batch_size == 10\n\n*/\nselect: id | from: experiment('singleton') traces | limit: 1 | filter: input=1 AND input=2 | sort: _pagination_key DESC", "result_rows": [], "skip": false}]