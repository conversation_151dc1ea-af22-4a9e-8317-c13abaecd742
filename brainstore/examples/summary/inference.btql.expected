[{"error": null, "query": "infer: scores | from: experiment('singleton')", "result_rows": [{"name": ["scores", "Factuality"], "top_values": [{"count": 1, "value": 0}, {"count": 9, "value": 1}], "type": {"type": "number"}}, {"name": ["scores", "Levenshtein"], "top_values": [{"count": 1, "value": 0.021739}, {"count": 1, "value": 0.024096}, {"count": 1, "value": 0.024631}, {"count": 1, "value": 0.024752}, {"count": 1, "value": 0.027624}, {"count": 1, "value": 0.029268}, {"count": 1, "value": 0.031746}, {"count": 1, "value": 0.032258}, {"count": 1, "value": 0.034146}, {"count": 1, "value": 0.036585}], "type": {"type": "number"}}], "skip": false}, {"error": null, "query": "infer: metadata | from: experiment('singleton') | filter: is_root", "result_rows": [{"name": ["metadata", "category"], "top_values": [{"count": 5, "value": "casual"}, {"count": 5, "value": "formal"}], "type": {"type": "string"}}, {"name": ["metadata", "language"], "top_values": [{"count": 4, "value": "Spanish"}, {"count": 6, "value": "English"}], "type": {"type": "string"}}, {"name": ["metadata", "user_type"], "top_values": [{"count": 5, "value": "new"}, {"count": 5, "value": "returning"}], "type": {"type": "string"}}], "skip": false}, {"error": null, "query": "/*!result -- This should return 0 rows (no inference budget)\n  length == 0\n */\ninfer: input | from: experiment('singleton') | inference_budget: 0", "result_rows": [], "skip": false}, {"error": null, "query": "/*!result -- This should return 1 row\n  length == 1\n */\ninfer: input | from: experiment('singleton') | inference_budget: 1", "result_rows": [{"name": ["input"], "top_values": [{"count": 2, "value": "Center different west community myself major travel southern consumer company together view say scene various grow very line ..."}], "type": {"type": "string"}}], "skip": false}]