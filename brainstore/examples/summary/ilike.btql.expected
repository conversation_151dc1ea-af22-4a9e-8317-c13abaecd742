[{"error": null, "query": "// Basic tests showing comparison on key or value work\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE '%comparing%'", "result_rows": [{"count(input)": 10}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%content%'", "result_rows": [{"count(input)": 20}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%rol%'", "result_rows": [{"count(input)": 20}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%submitted%expert%'", "result_rows": [{"count(input)": 10}], "skip": false}, {"error": null, "query": "// Prefix search only matches on values in the exact path\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE 'Center%'", "result_rows": [{"count(input)": 2}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE 'Director%'", "result_rows": [{"count(input)": 2}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata.language ILIKE 'English%'", "result_rows": [{"count(input)": 12}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input[0].content ILIKE 'You are%'", "result_rows": [{"count(input)": 10}], "skip": false}, {"error": null, "query": "// These don't match\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE 'You are%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE 'English%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE 'language%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "// This doesn't match because content only exists within arrays, so input.content is null, whereas input[0].content is non null\nmeasures: count(input) | from: experiment('singleton') | filter: input.content ILIKE 'You are%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "// Suffix search only matches values in the exact path\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE '%away.'", "result_rows": [{"count(input)": 2}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%structure.'", "result_rows": [{"count(input)": 2}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata.language ILIKE '%English'", "result_rows": [{"count(input)": 12}], "skip": false}, {"error": null, "query": "// These don't match, suffix search will not match anything besides values in the exact path\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE '%metadata'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE '%language'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%English'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE '%English'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "// searching for key + value works as long as you have a wildcard prefix and suffix\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE '%content%comparing%'", "result_rows": [{"count(input)": 10}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%role%use%'", "result_rows": [{"count(input)": 20}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%rol%user%'", "result_rows": [{"count(input)": 20}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%metadata%English%'", "result_rows": [{"count(input)": 12}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%metadata%language%'", "result_rows": [{"count(input)": 20}], "skip": false}, {"error": null, "query": "// These won't match anything\nmeasures: count(input) | from: experiment('singleton') | filter: input[0].content ILIKE '%user%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.role ILIKE '%comparing%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.notexist ILIKE '%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata.noexist ILIKE '%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.rol ILIKE '%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.metadata.lan ILIKE '%guage%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "// some simple case sensitive tests\nmeasures: count(input) | from: experiment('singleton') | filter: input[0].content LIKE 'You are%'", "result_rows": [{"count(input)": 10}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input[0].content LIKE 'you are%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input.user LIKE '%Role%'", "result_rows": [{"count(input)": 0}], "skip": false}, {"error": null, "query": "// search for unicode characters\nmeasures: count(input) | from: experiment('singleton') | filter: input ILIKE '%आज%'", "result_rows": [{"count(input)": 1}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%युग%'", "result_rows": [{"count(input)": 1}], "skip": false}, {"error": null, "query": "measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%तकनीक%'", "result_rows": [{"count(input)": 1}], "skip": false}]