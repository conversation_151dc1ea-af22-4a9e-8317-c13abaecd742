-- Tests for filtering by id and root_span_id from experiment('singleton') traces
-- Using events from brainstore/examples/summary/events.jsonl

-- Valid IDs and Root Span IDs for tests:
-- valid_id_1 = "de4890e6-8100-422b-9793-3c0f31c4092a" (root_span_id: "6c4053be-dedb-4353-abb7-0fe4039090be")
-- valid_rsid_1 = "6c4053be-dedb-4353-abb7-0fe4039090be"
-- valid_id_2 = "b5866b68-9d13-42ac-8906-ca2db1447820" (root_span_id: "967a194a-6e07-4ca5-8b2d-e07771a355bf")
-- valid_rsid_2 = "967a194a-6e07-4ca5-8b2d-e07771a355bf"
-- invalid_id = "00000000-0000-0000-0000-000000000000"
-- invalid_rsid = "11111111-1111-1111-1111-111111111111"

-- Test 1: Filter by a single valid id
/*!optimizer -- Check for single id filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["de4890e6-8100-422b-9793-3c0f31c4092a"] and ( .IdFilter.root_span_ids == null or (.IdFilter.root_span_ids | length == 0) ) ) | length == 1
*/
/*!execution -- Expect 1 segment as id exists
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a';

-- Test 2: Filter by a single valid root_span_id
/*!optimizer -- Check for single root_span_id filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.root_span_ids == ["6c4053be-dedb-4353-abb7-0fe4039090be"] and ( .IdFilter.ids == null or (.IdFilter.ids | length == 0) ) ) | length == 1
*/
/*!execution -- Expect 1 segment as root_span_id exists
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be';

-- Test 3: OR of two valid ids
/*!optimizer -- Check for two ids in filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids | length == 2 and contains(["de4890e6-8100-422b-9793-3c0f31c4092a"]) and contains(["b5866b68-9d13-42ac-8906-ca2db1447820"])) | length == 1
*/
/*!execution -- Expect 1 segment as both ids exist
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or id='b5866b68-9d13-42ac-8906-ca2db1447820';

-- Test 4: OR of two valid root_span_ids
/*!optimizer -- Check for two root_span_ids in filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.root_span_ids | length == 2 and contains(["6c4053be-dedb-4353-abb7-0fe4039090be"]) and contains(["967a194a-6e07-4ca5-8b2d-e07771a355bf"])) | length == 1
*/
/*!execution -- Expect 1 segment as both root_span_ids exist
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be' or root_span_id='967a194a-6e07-4ca5-8b2d-e07771a355bf';

-- Test 5: OR of a valid id and a (different group) valid root_span_id
/*!optimizer -- Check for id and root_span_id in the same filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["de4890e6-8100-422b-9793-3c0f31c4092a"] and .IdFilter.root_span_ids == ["967a194a-6e07-4ca5-8b2d-e07771a355bf"]) | length == 1
*/
/*!execution -- Expect 1 segment as both conditions can find matching events
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or root_span_id='967a194a-6e07-4ca5-8b2d-e07771a355bf';

-- Test 6: OR of a valid id and its own root_span_id
/*!optimizer -- Check for id and its own root_span_id in the same filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["de4890e6-8100-422b-9793-3c0f31c4092a"] and .IdFilter.root_span_ids == ["6c4053be-dedb-4353-abb7-0fe4039090be"]) | length == 1
*/
/*!execution -- Expect 1 segment
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be';

-- Test 7: OR of a valid id and an invalid root_span_id
/*!optimizer -- Check for valid id and invalid root_span_id
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["de4890e6-8100-422b-9793-3c0f31c4092a"] and .IdFilter.root_span_ids == ["11111111-1111-1111-1111-111111111111"]) | length == 1
*/
/*!execution -- Expect 1 segment due to valid id
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or root_span_id='11111111-1111-1111-1111-111111111111';

-- Test 8: OR of an invalid id and a valid root_span_id
/*!optimizer -- Check for invalid id and valid root_span_id
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["00000000-0000-0000-0000-000000000000"] and .IdFilter.root_span_ids == ["6c4053be-dedb-4353-abb7-0fe4039090be"]) | length == 1
*/
/*!execution -- Expect 1 segment due to valid root_span_id
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: experiment('singleton') summary | filter: id='00000000-0000-0000-0000-000000000000' or root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be';

-- Test 9: OR of an invalid id and an invalid root_span_id
/*!optimizer -- Check for invalid id and invalid root_span_id
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["00000000-0000-0000-0000-000000000000"] and .IdFilter.root_span_ids == ["11111111-1111-1111-1111-111111111111"]) | length == 1
*/
/*!execution -- Expect 0 segments as neither exists
  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 0
*/
select: * | from: experiment('singleton') summary | filter: id='00000000-0000-0000-0000-000000000000' or root_span_id='11111111-1111-1111-1111-111111111111';
