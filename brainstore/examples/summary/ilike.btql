// Basic tests showing comparison on key or value work
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%comparing%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%content%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%rol%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%submitted%expert%';

// Prefix search only matches on values in the exact path
measures: count(input) | from: experiment('singleton') | filter: input ILIKE 'Center%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE 'Director%';
measures: count(input) | from: experiment('singleton') | filter: input.metadata.language ILIKE 'English%';
measures: count(input) | from: experiment('singleton') | filter: input[0].content ILIKE 'You are%';

// These don't match
measures: count(input) | from: experiment('singleton') | filter: input ILIKE 'You are%';
measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE 'English%';
measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE 'language%';

// This doesn't match because content only exists within arrays, so input.content is null, whereas input[0].content is non null
measures: count(input) | from: experiment('singleton') | filter: input.content ILIKE 'You are%';

// Suffix search only matches values in the exact path
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%away.';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%structure.';
measures: count(input) | from: experiment('singleton') | filter: input.metadata.language ILIKE '%English';

// These don't match, suffix search will not match anything besides values in the exact path
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%metadata';
measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE '%language';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%English';
measures: count(input) | from: experiment('singleton') | filter: input.metadata ILIKE '%English';

// searching for key + value works as long as you have a wildcard prefix and suffix
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%content%comparing%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%role%use%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%rol%user%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%metadata%English%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%metadata%language%';

// These won't match anything
measures: count(input) | from: experiment('singleton') | filter: input[0].content ILIKE '%user%';
measures: count(input) | from: experiment('singleton') | filter: input.role ILIKE '%comparing%';
measures: count(input) | from: experiment('singleton') | filter: input.notexist ILIKE '%';
measures: count(input) | from: experiment('singleton') | filter: input.metadata.noexist ILIKE '%';
measures: count(input) | from: experiment('singleton') | filter: input.rol ILIKE '%';
measures: count(input) | from: experiment('singleton') | filter: input.metadata.lan ILIKE '%guage%';

// some simple case sensitive tests
measures: count(input) | from: experiment('singleton') | filter: input[0].content LIKE 'You are%';
measures: count(input) | from: experiment('singleton') | filter: input[0].content LIKE 'you are%';
measures: count(input) | from: experiment('singleton') | filter: input.user LIKE '%Role%';

// search for unicode characters
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%आज%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%युग%';
measures: count(input) | from: experiment('singleton') | filter: input ILIKE '%तकनीक%';
