-- These queries are not selective, so they will have a large batch size

/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("batch_size"))] | .[0].batch_size == 1000

*/
select: id | from: experiment('singleton') traces | limit: 1 | sort: _pagination_key DESC;


/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("batch_size"))] | .[0].batch_size == 1000

*/
select: id | from: experiment('singleton') traces | limit: 1 | filter: created < NOW() - INTERVAL 1 HOUR | sort: _pagination_key DESC;


/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("batch_size"))] | .[0].batch_size == 1000

*/
select: id | from: experiment('singleton') traces | limit: 1 | filter: created < NOW() - INTERVAL 1 HOUR OR input=1 | sort: _pagination_key DESC;


-- These queries should be selective though

/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("batch_size"))] | .[0].batch_size == 10

*/
select: id | from: experiment('singleton') traces | limit: 1 | filter: input=1 | sort: _pagination_key DESC;

/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("batch_size"))] | .[0].batch_size == 10

*/
select: id | from: experiment('singleton') traces | limit: 1 | filter: input=1 OR input=2 | sort: _pagination_key DESC;

/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("batch_size"))] | .[0].batch_size == 10

*/
select: id | from: experiment('singleton') traces | limit: 1 | filter: input=1 AND input=2 | sort: _pagination_key DESC;
