/*!result -- This should return 1 row, which is the one span that has a matching pagination key range
    length == 1
*/
select: root_span_id, _pagination_key, is_root | from: experiment('singleton') | filter: _pagination_key > 'p07472183629959135234';


/*!result -- However, in summary mode, it should not match, because the root trace's pagination key is not in the range
    length == 0
*/
select: root_span_id, _pagination_key, is_root | from: experiment('singleton') summary | filter: _pagination_key > 'p07472183629959135234';

/*!result -- This picks the two traces that match this range (it's a sanity test)
    length == 2
*/
select: root_span_id, _pagination_key, is_root | from: experiment('singleton') summary | filter: _pagination_key > 'p07472183629958873088';


/*!result -- Similar to above
    length == 1
*/
select: root_span_id, _pagination_key, is_root | from: experiment('singleton') spans | filter: _xact_id > '1000194608726806580';

/*!result -- Similar to above
    length == 0
*/
select: root_span_id, _pagination_key, is_root | from: experiment('singleton') summary | filter: _xact_id > '1000194608726806580';
