/*!optimizer -- Make sure that the search has segment filters

    [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters | length == 1

*/
select: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752';

/*!optimizer -- Make sure that the search combines filters if there's an AND

    [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters | length == 2

*/
select: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752' and _pagination_key > 'p00000000000006815752';

/*!optimizer -- Make sure that the search loses filters if there's an OR

    [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters | length == 0

*/
select: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752' or _pagination_key > 'p00000000000006815752';


/*!execution -- Make sure _xact_id filters eliminate segments

  [.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 0

*/
select: * | from: dataset('singleton') | filter: _xact_id > 110;


/*!optimizer -- Make sure that the search captures equality filters on id

  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["1"]) | length == 1

*/
/*!execution -- Make sure we eliminate a bunch of segments

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1

*/
select: * | from: dataset('singleton') | filter: id='1';

/*!optimizer -- Make sure that the search captures equality filters on id

  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["1"]) | length == 1

*/
/*!execution -- Make sure we eliminate a bunch of segments

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1

*/
select: * | from: dataset('singleton') | filter: id='1' | sort: _pagination_key desc;

-- Same, for a real-time row
/*!optimizer -- Make sure that the search captures equality filters on id

  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["23"]) | length == 1

*/
/*!execution -- Make sure we eliminate a bunch of segments

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 0

*/
select: * | from: dataset('singleton') | filter: id='23';

/*!optimizer -- Make sure that the search captures equality filters on id

  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == ["23"]) | length == 1

*/
/*!execution -- Make sure we eliminate a bunch of segments

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 0

*/
select: * | from: dataset('singleton') | filter: id='23' | sort: _pagination_key desc;

-- Test ORing two valid IDs
/*!optimizer -- Make sure that the search captures both IDs in an OR filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids | length == 2 and contains(["1"]) and contains(["2"])) | length == 1
*/
/*!execution -- Make sure we get segments for both valid IDs
[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: dataset('singleton') | filter: id='1' or id='2';

-- Test ORing a valid ID with an invalid ID
/*!optimizer -- Make sure that the search captures both valid and invalid IDs in an OR filter
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids | length == 2 and contains(["1"]) and contains(["999"])) | length == 1
*/
/*!execution -- Make sure we get 1 output segment for the valid ID
[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .num_output_segments.total == 1
*/
select: * | from: dataset('singleton') | filter: id='1' or id='999';

-- Test ORing an ID filter with a broadly true condition
/*!optimizer -- Make sure that an OR with a broadly true condition results in no segment filters
  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.segment_filters | length == 0
*/
select: * | from: dataset('singleton') | filter: id='1' or _xact_id > '0';
