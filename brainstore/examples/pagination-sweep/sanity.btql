select: * | from: dataset('singleton');
select: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752';
select: * | from: dataset('singleton') | filter: _pagination_key >= 'p00000000000006815752';
select: * | from: dataset('singleton') | sort: _pagination_key ASC;
select: * | from: dataset('singleton') | sort: _pagination_key DESC;
select: * | from: dataset('singleton') | sort: _pagination_key ASC | limit: 2;
select: * | from: dataset('singleton') | sort: _pagination_key DESC | limit: 2;

select: * | from: dataset('singleton') traces;
select: * | from: dataset('singleton') traces | filter: _pagination_key < 'p00000000000006815752';
select: * | from: dataset('singleton') traces | filter: _pagination_key >= 'p00000000000006815752';
select: * | from: dataset('singleton') traces | sort: _pagination_key ASC;
select: * | from: dataset('singleton') traces | sort: _pagination_key DESC;
select: * | from: dataset('singleton') traces | sort: _pagination_key ASC | limit: 2;
select: * | from: dataset('singleton') traces | sort: _pagination_key DESC | limit: 2;


measures: count(1) | from: dataset('singleton');
measures: count(1) | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752';
measures: count(1) | from: dataset('singleton') | filter: _pagination_key >= 'p00000000000006815752';
measures: count(1) | from: dataset('singleton') | sort: _pagination_key ASC;
measures: count(1) | from: dataset('singleton') | sort: _pagination_key DESC;
measures: count(1) | from: dataset('singleton') | sort: _pagination_key ASC | limit: 2;
measures: count(1) | from: dataset('singleton') | sort: _pagination_key DESC | limit: 2;
