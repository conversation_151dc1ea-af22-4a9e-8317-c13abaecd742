[{"error": null, "query": "/*!optimizer -- Make sure that the search has segment filters\n\n    [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters | length == 1\n\n*/\nselect: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752'", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}], "skip": false}, {"error": null, "query": "/*!optimizer -- Make sure that the search combines filters if there's an AND\n\n    [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters | length == 2\n\n*/\nselect: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752' and _pagination_key > 'p00000000000006815752'", "result_rows": [], "skip": false}, {"error": null, "query": "/*!optimizer -- Make sure that the search loses filters if there's an OR\n\n    [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters | length == 0\n\n*/\nselect: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752' or _pagination_key > 'p00000000000006815752'", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}, {"error": null, "query": "/*!execution -- Make sure _xact_id filters eliminate segments\n\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 0\n\n*/\nselect: * | from: dataset('singleton') | filter: _xact_id > 110", "result_rows": [], "skip": false}, {"error": null, "query": "/*!optimizer -- Make sure that the search captures equality filters on id\n\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"1\"]) | length == 1\n\n*/\n/*!execution -- Make sure we eliminate a bunch of segments\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n\n*/\nselect: * | from: dataset('singleton') | filter: id='1'", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "/*!optimizer -- Make sure that the search captures equality filters on id\n\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"1\"]) | length == 1\n\n*/\n/*!execution -- Make sure we eliminate a bunch of segments\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n\n*/\nselect: * | from: dataset('singleton') | filter: id='1' | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "-- Same, for a real-time row\n/*!optimizer -- Make sure that the search captures equality filters on id\n\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"23\"]) | length == 1\n\n*/\n/*!execution -- Make sure we eliminate a bunch of segments\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 0\n\n*/\nselect: * | from: dataset('singleton') | filter: id='23'", "result_rows": [{"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "/*!optimizer -- Make sure that the search captures equality filters on id\n\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"23\"]) | length == 1\n\n*/\n/*!execution -- Make sure we eliminate a bunch of segments\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 0\n\n*/\nselect: * | from: dataset('singleton') | filter: id='23' | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}, {"error": null, "query": "-- Test ORing two valid IDs\n/*!optimizer -- Make sure that the search captures both IDs in an OR filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids | length == 2 and contains([\"1\"]) and contains([\"2\"])) | length == 1\n*/\n/*!execution -- Make sure we get segments for both valid IDs\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: dataset('singleton') | filter: id='1' or id='2'", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "query": "-- Test ORing a valid ID with an invalid ID\n/*!optimizer -- Make sure that the search captures both valid and invalid IDs in an OR filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids | length == 2 and contains([\"1\"]) and contains([\"999\"])) | length == 1\n*/\n/*!execution -- Make sure we get 1 output segment for the valid ID\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: dataset('singleton') | filter: id='1' or id='999'", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "-- Test ORing an ID filter with a broadly true condition\n/*!optimizer -- Make sure that an OR with a broadly true condition results in no segment filters\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters | length == 0\n*/\nselect: * | from: dataset('singleton') | filter: id='1' or _xact_id > '0'", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}]