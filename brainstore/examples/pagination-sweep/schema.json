{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"dataset": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "_xact_id": {"type": "string"}, "_pagination_key": {"type": "string"}, "root_span_id": {"type": "string"}, "span_id": {"type": "string"}, "created": {"type": "string", "format": "date-time"}, "span_parents": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}]}, "i": {"type": "number"}, "s": {"type": "string"}}}}}}