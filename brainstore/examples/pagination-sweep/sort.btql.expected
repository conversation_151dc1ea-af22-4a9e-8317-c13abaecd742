[{"error": null, "next_page_using_returned_cursor": [], "query": "/*!execution -- Make sure that the sort has a small first batch\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .first_batch_size.total == 1\n\n*/\nselect: * | from: dataset('singleton') | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "/*!execution -- Make sure that the sort has a small first batch\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .first_batch_size.total == 1\n\n*/\nselect: * | from: dataset('singleton') | sort: created desc", "result_rows": [{"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "/*!execution -- Make sure that the sort has a small first batch\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .first_batch_size.total == 1\n\n*/\nselect: * | from: dataset('singleton') | sort: _xact_id desc", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "/*!execution -- Make sure that the sort has a small first batch\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .first_batch_size.total == 1\n\n*/\nselect: * | from: dataset('singleton') | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}, {"error": null, "query": "/*!execution -- Make sure that the sort has a small first batch\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .first_batch_size.total == 1\n\n*/\nselect: * | from: dataset('singleton') | sort: created asc", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "/*!execution -- Make sure that the sort has a small first batch\n\n[.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .first_batch_size.total == 1\n\n*/\nselect: * | from: dataset('singleton') | sort: _xact_id asc", "result_rows": [{"_pagination_key": "p00000000000006619136", "_xact_id": "101", "created": "2025-01-01T00:00:00Z", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000006619137", "_xact_id": "101", "created": "2025-01-01T00:00:01Z", "id": "2", "root_span_id": "1", "s": "bar", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000006619138", "_xact_id": "101", "created": "2025-01-01T00:00:02Z", "id": "3", "root_span_id": "1", "s": "baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000006684675", "_xact_id": "102", "created": "2025-01-02T00:00:00Z", "id": "4", "root_span_id": "2", "s": "hello", "span_id": "4"}, {"_pagination_key": "p00000000000006684676", "_xact_id": "102", "created": "2025-01-02T00:00:01Z", "id": "5", "root_span_id": "2", "s": "world", "span_id": "5", "span_parents": ["4"]}, {"_pagination_key": "p00000000000006750213", "_xact_id": "103", "created": "2025-01-03T00:00:01Z", "id": "6", "root_span_id": "3", "s": "query", "span_id": "6"}, {"_pagination_key": "p00000000000006750214", "_xact_id": "103", "created": "2025-01-03T00:00:02Z", "id": "7", "root_span_id": "3", "s": "plan", "span_id": "7", "span_parents": ["6"]}, {"_pagination_key": "p00000000000006750215", "_xact_id": "103", "created": "2025-01-03T00:00:03Z", "id": "8", "root_span_id": "3", "s": "execute", "span_id": "8", "span_parents": ["7"]}, {"_pagination_key": "p00000000000006815752", "_xact_id": "104", "created": "2025-01-04T00:00:00Z", "id": "9", "root_span_id": "4", "s": "start", "span_id": "9"}, {"_pagination_key": "p00000000000006815753", "_xact_id": "104", "created": "2025-01-04T00:00:01Z", "id": "10", "root_span_id": "4", "s": "process", "span_id": "10", "span_parents": ["9"]}, {"_pagination_key": "p00000000000006815754", "_xact_id": "104", "created": "2025-01-04T00:00:02Z", "id": "11", "root_span_id": "4", "s": "finish", "span_id": "11", "span_parents": ["10"]}, {"_pagination_key": "p00000000000006881291", "_xact_id": "105", "created": "2025-01-05T00:00:00Z", "id": "12", "root_span_id": "5", "s": "init", "span_id": "12"}, {"_pagination_key": "p00000000000006881292", "_xact_id": "105", "created": "2025-01-05T00:00:01Z", "id": "13", "root_span_id": "5", "s": "load", "span_id": "13", "span_parents": ["12"]}, {"_pagination_key": "p00000000000006946829", "_xact_id": "106", "created": "2025-01-06T00:00:01Z", "id": "14", "root_span_id": "6", "s": "request", "span_id": "14"}, {"_pagination_key": "p00000000000006946830", "_xact_id": "106", "created": "2025-01-06T00:00:02Z", "id": "15", "root_span_id": "6", "s": "validate", "span_id": "15", "span_parents": ["14"]}, {"_pagination_key": "p00000000000006946831", "_xact_id": "106", "created": "2025-01-06T00:00:03Z", "id": "16", "root_span_id": "6", "s": "respond", "span_id": "16", "span_parents": ["15"]}, {"_pagination_key": "p00000000000007012368", "_xact_id": "107", "created": "2025-01-07T00:00:00Z", "id": "17", "root_span_id": "7", "s": "connect", "span_id": "17"}, {"_pagination_key": "p00000000000007012369", "_xact_id": "107", "created": "2025-01-07T00:00:01Z", "id": "18", "root_span_id": "7", "s": "authenticate", "span_id": "18", "span_parents": ["17"]}, {"_pagination_key": "p00000000000007077906", "_xact_id": "108", "created": "2025-01-08T00:00:00Z", "id": "19", "root_span_id": "8", "s": "read", "span_id": "19"}, {"_pagination_key": "p00000000000007077907", "_xact_id": "108", "created": "2025-01-08T00:00:01Z", "id": "20", "root_span_id": "8", "s": "write", "span_id": "20", "span_parents": ["19"]}, {"_pagination_key": "p00000000000007143444", "_xact_id": "109", "created": "2025-01-09T00:00:00Z", "id": "21", "root_span_id": "9", "s": "open", "span_id": "21"}, {"_pagination_key": "p00000000000007143445", "_xact_id": "109", "created": "2025-01-09T00:00:01Z", "id": "22", "root_span_id": "21", "s": "close", "span_id": "22", "span_parents": ["21"]}, {"_pagination_key": "p00000000000007208960", "_xact_id": "110", "created": "2025-01-10T00:00:00Z", "id": "23", "root_span_id": "10", "s": "close", "span_id": "23", "span_parents": ["23"]}], "skip": false}]