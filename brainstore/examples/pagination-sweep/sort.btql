/*!execution -- Make sure that the sort has a small first batch

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .first_batch_size.total == 1

*/
select: * | from: dataset('singleton') | sort: _pagination_key desc;

/*!execution -- Make sure that the sort has a small first batch

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .first_batch_size.total == 1

*/
select: * | from: dataset('singleton') | sort: created desc;

/*!execution -- Make sure that the sort has a small first batch

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .first_batch_size.total == 1

*/
select: * | from: dataset('singleton') | sort: _xact_id desc;


/*!execution -- Make sure that the sort has a small first batch

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .first_batch_size.total == 1

*/
select: * | from: dataset('singleton') | sort: _pagination_key asc;

/*!execution -- Make sure that the sort has a small first batch

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .first_batch_size.total == 1

*/
select: * | from: dataset('singleton') | sort: created asc;

/*!execution -- Make sure that the sort has a small first batch

[.. | objects | select(.name == "Compute segment batches")][0].stats.counter_stats | .first_batch_size.total == 1

*/
select: * | from: dataset('singleton') | sort: _xact_id asc;
