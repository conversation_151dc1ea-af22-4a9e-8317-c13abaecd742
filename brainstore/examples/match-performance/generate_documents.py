import json
import random
from pathlib import Path

from faker import Faker

# Initialize Faker
fake = Faker()


def generate_large_html_document():
    """Generate a large HTML document with random content"""

    # Generate a complex HTML structure
    html = f"""<!DOCTYPE html>
<html>
<head>
    <title>{fake.catch_phrase()}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        {fake.text(max_nb_chars=4000)}
    </style>
</head>
<body>
    <header>
        <h1>{fake.company()}</h1>
        <nav>
            {' '.join([f'<a href="#">{fake.word()}</a>' for _ in range(10)])}
        </nav>
    </header>

    <main>
        <article>
            <h2>{fake.catch_phrase()}</h2>
            {''.join([f'<p>{fake.paragraph(nb_sentences=10)}</p>' for _ in range(20)])}
        </article>

        <section>
            <h3>{fake.catch_phrase()}</h3>
            <ul>
                {''.join([f'<li>{fake.sentence()}</li>' for _ in range(30)])}
            </ul>
        </section>

        <div class="content">
            {''.join([f'<div class="block"><h4>{fake.company_suffix()}</h4><p>{fake.text(max_nb_chars=1000)}</p></div>' for _ in range(5)])}
        </div>
    </main>

    <footer>
        <p>{fake.text(max_nb_chars=500)}</p>
    </footer>
</body>
</html>"""

    return html


def generate_documents(num_docs=100):
    """Generate documents and write to docs.jsonl"""

    output_file = Path("docs.jsonl")

    with output_file.open("w") as f:
        for _ in range(num_docs):
            doc = {
                "input": {
                    "document": generate_large_html_document(),
                    "metadata": {
                        "timestamp": fake.date_time().isoformat(),
                        "author": fake.name(),
                        "category": fake.random_element(["blog", "news", "article", "product"]),
                        "tags": [fake.word() for _ in range(5)],
                        "version": "1.0",
                    },
                }
            }
            f.write(json.dumps(doc) + "\n")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generate sample HTML documents")
    parser.add_argument(
        "-n", "--num-docs", type=int, default=100, help="Number of documents to generate (default: 100)"
    )

    args = parser.parse_args()
    generate_documents(args.num_docs)
