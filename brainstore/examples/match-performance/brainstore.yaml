version: 1
schema:
  name: copilot-logs
  fields:
    - name: input
      tantivy:
        - name: input_json
          type: !json
            stored: true
            tokenize: false
        - name: input_text
          type: !str
            tokenize: true
            stored: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
