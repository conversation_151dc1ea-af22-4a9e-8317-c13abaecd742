version: 1
schema:
  name: test
  fields:
    - name: timestamp
      tantivy:
        - name: timestamp
          type: !date
            stored: true
            fast: true
    - name: object
      tantivy:
        - name: object
          type: !json
            stored: true
            tokenize: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/base/metadata
index_uri: ./index/base/data
wal_uri: ./index/base/wal
realtime_wal_uri: ./index/base/wal
