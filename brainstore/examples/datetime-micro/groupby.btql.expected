[{"error": null, "query": "dimensions: second(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 24, "second(timestamp)": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "dimensions: second(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 24, "second(object.timestamp)": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "dimensions: second(timestamp - interval 1 microsecond) | measures: count(1)", "result_rows": [{"count(1)": 15, "second(timestamp - interval 1 microsecond)": "2024-02-29T16:23:00Z"}, {"count(1)": 9, "second(timestamp - interval 1 microsecond)": "2024-02-29T16:22:59Z"}], "skip": false}, {"error": null, "query": "dimensions: second(object.timestamp - interval 1 microsecond) | measures: count(1)", "result_rows": [{"count(1)": 15, "second(object.timestamp - interval 1 microsecond)": "2024-02-29T16:23:00Z"}, {"count(1)": 9, "second(object.timestamp - interval 1 microsecond)": "2024-02-29T16:22:59Z"}], "skip": false}, {"error": null, "query": "dimensions: second(timestamp - interval 1 millisecond) | measures: count(1)", "result_rows": [{"count(1)": 12, "second(timestamp - interval 1 millisecond)": "2024-02-29T16:22:59Z"}, {"count(1)": 12, "second(timestamp - interval 1 millisecond)": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "dimensions: second(object.timestamp - interval 1 millisecond) | measures: count(1)", "result_rows": [{"count(1)": 12, "second(object.timestamp - interval 1 millisecond)": "2024-02-29T16:22:59Z"}, {"count(1)": 12, "second(object.timestamp - interval 1 millisecond)": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "dimensions: second(timestamp - interval 100 millisecond) | measures: count(1)", "result_rows": [{"count(1)": 18, "second(timestamp - interval 100 millisecond)": "2024-02-29T16:22:59Z"}, {"count(1)": 6, "second(timestamp - interval 100 millisecond)": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "dimensions: second(object.timestamp - interval 100 millisecond) | measures: count(1)", "result_rows": [{"count(1)": 18, "second(object.timestamp - interval 100 millisecond)": "2024-02-29T16:22:59Z"}, {"count(1)": 6, "second(object.timestamp - interval 100 millisecond)": "2024-02-29T16:23:00Z"}], "skip": false}]