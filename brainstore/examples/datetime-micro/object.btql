select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp <= '2024-02-29';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp > '2024-02-29';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T17:23:00+01:00';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T15:23:00-01:00';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.000Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.000000Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.000001Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.00000100Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.000001001Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.001Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.001000Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.1Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp = '2024-02-29T16:23:00.100Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T16:23:00Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T17:23:00+01:00';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T15:23:00-01:00';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp <= '2024-02-29T16:23:00Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T16:23:00.000Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T16:23:00.000000Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T16:23:00.000001Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T16:23:00.00000100Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp < '2024-02-29T16:23:00.000001001Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp > '2024-02-29T16:23:00.001Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp > '2024-02-29T16:23:00.001000Z';

select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp > '2024-02-29T16:23:00.1Z';
select: id, object.timestamp | sort: object.timestamp desc, id | filter: object.timestamp > '2024-02-29T16:23:00.100Z';
