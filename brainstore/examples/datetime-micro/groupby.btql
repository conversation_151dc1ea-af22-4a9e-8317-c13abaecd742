dimensions: second(timestamp) | measures: count(1);
dimensions: second(object.timestamp) | measures: count(1);

dimensions: second(timestamp - interval 1 microsecond) | measures: count(1);
dimensions: second(object.timestamp - interval 1 microsecond) | measures: count(1);

dimensions: second(timestamp - interval 1 millisecond) | measures: count(1);
dimensions: second(object.timestamp - interval 1 millisecond) | measures: count(1);

dimensions: second(timestamp - interval 100 millisecond) | measures: count(1);
dimensions: second(object.timestamp - interval 100 millisecond) | measures: count(1);
