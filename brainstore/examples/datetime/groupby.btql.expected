[{"error": null, "query": "dimensions: year(timestamp) | filter: timestamp > \"2020-01-01T00:00:00Z\"", "result_rows": [{"year(timestamp)": "2022-01-01T00:00:00Z"}, {"year(timestamp)": "2023-01-01T00:00:00Z"}, {"year(timestamp)": "2024-01-01T00:00:00Z"}, {"year(timestamp)": "2025-01-01T00:00:00Z"}, {"year(timestamp)": "2027-01-01T00:00:00Z"}, {"year(timestamp)": "2028-01-01T00:00:00Z"}, {"year(timestamp)": "2030-01-01T00:00:00Z"}, {"year(timestamp)": "2031-01-01T00:00:00Z"}, {"year(timestamp)": "2032-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: year(object.timestamp), id | filter: timestamp < \"2020-01-01T00:00:00Z\"", "result_rows": [{"id": 11, "year(object.timestamp)": "2017-01-01T00:00:00Z"}, {"id": 8, "year(object.timestamp)": "2019-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: year(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "year(timestamp)": "2017-01-01T00:00:00Z"}, {"count(1)": 1, "year(timestamp)": "2019-01-01T00:00:00Z"}, {"count(1)": 1, "year(timestamp)": "2022-01-01T00:00:00Z"}, {"count(1)": 1, "year(timestamp)": "2025-01-01T00:00:00Z"}, {"count(1)": 1, "year(timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "year(timestamp)": "2030-01-01T00:00:00Z"}, {"count(1)": 1, "year(timestamp)": "2031-01-01T00:00:00Z"}, {"count(1)": 2, "year(timestamp)": "2023-01-01T00:00:00Z"}, {"count(1)": 2, "year(timestamp)": "2027-01-01T00:00:00Z"}, {"count(1)": 4, "year(timestamp)": "2032-01-01T00:00:00Z"}, {"count(1)": 5, "year(timestamp)": "2024-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: year(object.timestamp) | filter: object.timestamp > \"2020-01-01T00:00:00Z\"", "result_rows": [{"year(object.timestamp)": "2022-01-01T00:00:00Z"}, {"year(object.timestamp)": "2023-01-01T00:00:00Z"}, {"year(object.timestamp)": "2024-01-01T00:00:00Z"}, {"year(object.timestamp)": "2025-01-01T00:00:00Z"}, {"year(object.timestamp)": "2027-01-01T00:00:00Z"}, {"year(object.timestamp)": "2028-01-01T00:00:00Z"}, {"year(object.timestamp)": "2030-01-01T00:00:00Z"}, {"year(object.timestamp)": "2031-01-01T00:00:00Z"}, {"year(object.timestamp)": "2032-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: year(object.timestamp), id | filter: object.timestamp < \"2020-01-01T00:00:00Z\"", "result_rows": [{"id": 11, "year(object.timestamp)": "2017-01-01T00:00:00Z"}, {"id": 8, "year(object.timestamp)": "2019-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: year(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "year(object.timestamp)": "2017-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2019-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2022-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2025-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2030-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2031-01-01T00:00:00Z"}, {"count(1)": 1, "year(object.timestamp)": "2032-01-01T00:00:00Z"}, {"count(1)": 2, "year(object.timestamp)": "2023-01-01T00:00:00Z"}, {"count(1)": 2, "year(object.timestamp)": "2027-01-01T00:00:00Z"}, {"count(1)": 3}, {"count(1)": 5, "year(object.timestamp)": "2024-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: month(timestamp) | measures: count(1) | filter: timestamp < \"2025-01-01T00:00:00Z\"", "result_rows": [{"count(1)": 1, "month(timestamp)": "2017-12-01T00:00:00Z"}, {"count(1)": 1, "month(timestamp)": "2019-04-01T00:00:00Z"}, {"count(1)": 1, "month(timestamp)": "2022-03-01T00:00:00Z"}, {"count(1)": 1, "month(timestamp)": "2023-02-01T00:00:00Z"}, {"count(1)": 1, "month(timestamp)": "2023-11-01T00:00:00Z"}, {"count(1)": 1, "month(timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "month(timestamp)": "2024-12-01T00:00:00Z"}, {"count(1)": 3, "month(timestamp)": "2024-02-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: month(object.timestamp) | measures: count(1) | filter: object.timestamp < \"2025-01-01T00:00:00Z\"", "result_rows": [{"count(1)": 1, "month(object.timestamp)": "2017-12-01T00:00:00Z"}, {"count(1)": 1, "month(object.timestamp)": "2019-04-01T00:00:00Z"}, {"count(1)": 1, "month(object.timestamp)": "2022-03-01T00:00:00Z"}, {"count(1)": 1, "month(object.timestamp)": "2023-02-01T00:00:00Z"}, {"count(1)": 1, "month(object.timestamp)": "2023-11-01T00:00:00Z"}, {"count(1)": 1, "month(object.timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "month(object.timestamp)": "2024-12-01T00:00:00Z"}, {"count(1)": 3, "month(object.timestamp)": "2024-02-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: week(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "week(timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2019-04-01T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2022-03-14T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2023-02-27T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2023-11-13T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2024-12-30T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2025-06-30T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2026-12-28T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2027-09-27T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2027-12-27T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2030-11-11T00:00:00Z"}, {"count(1)": 1, "week(timestamp)": "2031-05-19T00:00:00Z"}, {"count(1)": 4, "week(timestamp)": "2024-02-26T00:00:00Z"}, {"count(1)": 4, "week(timestamp)": "2032-02-09T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: week(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "week(object.timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2019-04-01T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2022-03-14T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2023-02-27T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2023-11-13T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2024-12-30T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2025-06-30T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2026-12-28T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2027-09-27T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2027-12-27T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2030-11-11T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2031-05-19T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp)": "2032-02-09T00:00:00Z"}, {"count(1)": 3}, {"count(1)": 4, "week(object.timestamp)": "2024-02-26T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: week(timestamp - interval 4 day) | measures: count(1)", "result_rows": [{"count(1)": 1, "week(timestamp - interval 4 day)": "2017-12-18T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2019-03-25T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2022-03-07T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2023-02-20T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2023-11-06T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2024-02-26T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2024-12-23T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2025-06-23T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2026-12-28T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2027-09-20T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2027-12-27T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2030-11-04T00:00:00Z"}, {"count(1)": 1, "week(timestamp - interval 4 day)": "2031-05-12T00:00:00Z"}, {"count(1)": 3, "week(timestamp - interval 4 day)": "2024-02-19T00:00:00Z"}, {"count(1)": 4, "week(timestamp - interval 4 day)": "2032-02-09T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: week(object.timestamp - interval 4 day) | measures: count(1)", "result_rows": [{"count(1)": 1, "week(object.timestamp - interval 4 day)": "2017-12-18T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2019-03-25T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2022-03-07T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2023-02-20T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2023-11-06T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2024-02-26T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2024-12-23T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2025-06-23T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2026-12-28T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2027-09-20T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2027-12-27T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2030-11-04T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2031-05-12T00:00:00Z"}, {"count(1)": 1, "week(object.timestamp - interval 4 day)": "2032-02-09T00:00:00Z"}, {"count(1)": 3, "week(object.timestamp - interval 4 day)": "2024-02-19T00:00:00Z"}, {"count(1)": 3}], "skip": false}, {"error": null, "query": "dimensions: day(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "day(timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2019-04-01T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2022-03-14T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2023-02-28T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2023-11-15T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2024-12-31T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2025-07-01T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2027-01-01T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2027-09-30T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2030-11-11T00:00:00Z"}, {"count(1)": 1, "day(timestamp)": "2031-05-20T00:00:00Z"}, {"count(1)": 3, "day(timestamp)": "2024-02-29T00:00:00Z"}, {"count(1)": 4, "day(timestamp)": "2032-02-15T00:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: day(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "day(object.timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2019-04-01T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2022-03-14T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2023-02-28T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2023-11-15T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2024-12-31T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2025-07-01T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2027-01-01T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2027-09-30T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2030-11-11T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2031-05-20T00:00:00Z"}, {"count(1)": 1, "day(object.timestamp)": "2032-02-15T00:00:00Z"}, {"count(1)": 3, "day(object.timestamp)": "2024-02-29T00:00:00Z"}, {"count(1)": 3}], "skip": false}, {"error": null, "query": "dimensions: hour(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "hour(timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2019-04-01T21:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2022-03-14T12:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2023-02-28T16:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2023-11-15T07:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2024-02-29T17:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2024-12-31T23:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2025-07-01T07:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2027-01-01T04:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2027-09-30T00:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2030-11-11T11:00:00Z"}, {"count(1)": 1, "hour(timestamp)": "2031-05-20T08:00:00Z"}, {"count(1)": 2, "hour(timestamp)": "2024-02-29T16:00:00Z"}, {"count(1)": 4, "hour(timestamp)": "2032-02-15T09:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: hour(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "hour(object.timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2019-04-01T21:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2022-03-14T12:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2023-02-28T16:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2023-11-15T07:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2024-02-29T17:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2024-12-31T23:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2025-07-01T07:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2027-01-01T04:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2027-09-30T00:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2030-11-11T11:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2031-05-20T08:00:00Z"}, {"count(1)": 1, "hour(object.timestamp)": "2032-02-15T09:00:00Z"}, {"count(1)": 2, "hour(object.timestamp)": "2024-02-29T16:00:00Z"}, {"count(1)": 3}], "skip": false}, {"error": null, "query": "dimensions: minute(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "minute(timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "minute(timestamp)": "2019-04-01T21:30:00Z"}, {"count(1)": 1, "minute(timestamp)": "2022-03-14T12:30:00Z"}, {"count(1)": 1, "minute(timestamp)": "2023-02-28T16:23:00Z"}, {"count(1)": 1, "minute(timestamp)": "2023-11-15T07:45:00Z"}, {"count(1)": 1, "minute(timestamp)": "2024-02-29T17:00:00Z"}, {"count(1)": 1, "minute(timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "minute(timestamp)": "2024-12-31T23:59:00Z"}, {"count(1)": 1, "minute(timestamp)": "2025-07-01T07:00:00Z"}, {"count(1)": 1, "minute(timestamp)": "2027-01-01T04:59:00Z"}, {"count(1)": 1, "minute(timestamp)": "2027-09-30T00:45:00Z"}, {"count(1)": 1, "minute(timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "minute(timestamp)": "2030-11-11T11:11:00Z"}, {"count(1)": 1, "minute(timestamp)": "2031-05-20T08:45:00Z"}, {"count(1)": 2, "minute(timestamp)": "2024-02-29T16:23:00Z"}, {"count(1)": 4, "minute(timestamp)": "2032-02-15T09:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: minute(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "minute(object.timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2019-04-01T21:30:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2022-03-14T12:30:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2023-02-28T16:23:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2023-11-15T07:45:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2024-02-29T17:00:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2024-12-31T23:59:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2025-07-01T07:00:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2027-01-01T04:59:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2027-09-30T00:45:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2030-11-11T11:11:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2031-05-20T08:45:00Z"}, {"count(1)": 1, "minute(object.timestamp)": "2032-02-15T09:00:00Z"}, {"count(1)": 2, "minute(object.timestamp)": "2024-02-29T16:23:00Z"}, {"count(1)": 3}], "skip": false}, {"error": null, "query": "dimensions: second(timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "second(timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "second(timestamp)": "2019-04-01T21:30:00Z"}, {"count(1)": 1, "second(timestamp)": "2022-03-14T12:30:45Z"}, {"count(1)": 1, "second(timestamp)": "2023-02-28T16:23:00Z"}, {"count(1)": 1, "second(timestamp)": "2023-11-15T07:45:30Z"}, {"count(1)": 1, "second(timestamp)": "2024-02-29T16:23:00Z"}, {"count(1)": 1, "second(timestamp)": "2024-02-29T16:23:30Z"}, {"count(1)": 1, "second(timestamp)": "2024-02-29T17:00:00Z"}, {"count(1)": 1, "second(timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "second(timestamp)": "2024-12-31T23:59:59Z"}, {"count(1)": 1, "second(timestamp)": "2025-07-01T07:00:00Z"}, {"count(1)": 1, "second(timestamp)": "2027-01-01T04:59:59Z"}, {"count(1)": 1, "second(timestamp)": "2027-09-30T00:45:00Z"}, {"count(1)": 1, "second(timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "second(timestamp)": "2030-11-11T11:11:11Z"}, {"count(1)": 1, "second(timestamp)": "2031-05-20T08:45:00Z"}, {"count(1)": 4, "second(timestamp)": "2032-02-15T09:00:00Z"}], "skip": false}, {"error": null, "query": "dimensions: second(object.timestamp) | measures: count(1)", "result_rows": [{"count(1)": 1, "second(object.timestamp)": "2017-12-25T00:00:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2019-04-01T21:30:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2022-03-14T12:30:45Z"}, {"count(1)": 1, "second(object.timestamp)": "2023-02-28T16:23:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2023-11-15T07:45:30Z"}, {"count(1)": 1, "second(object.timestamp)": "2024-02-29T16:23:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2024-02-29T16:23:30Z"}, {"count(1)": 1, "second(object.timestamp)": "2024-02-29T17:00:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2024-03-01T00:00:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2024-12-31T23:59:59Z"}, {"count(1)": 1, "second(object.timestamp)": "2025-07-01T07:00:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2027-01-01T04:59:59Z"}, {"count(1)": 1, "second(object.timestamp)": "2027-09-30T00:45:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2028-01-01T00:00:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2030-11-11T11:11:11Z"}, {"count(1)": 1, "second(object.timestamp)": "2031-05-20T08:45:00Z"}, {"count(1)": 1, "second(object.timestamp)": "2032-02-15T09:00:00Z"}, {"count(1)": 3}], "skip": false}]