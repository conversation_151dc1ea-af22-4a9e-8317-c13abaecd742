select: id, year(timestamp);
select: id, month(timestamp);
select: id, week(timestamp);
select: id, day(timestamp);
select: id, hour(timestamp);
select: id, minute(timestamp);
select: id, second(timestamp);

select: day(current_timestamp) = current_date | filter: id = 1;
select: day(current_timestamp) = current_date + interval 1 day | filter: id = 1;
select: id | filter: day(current_timestamp - interval 1 day) = current_date - interval 1 day and (id = "1" or id = "2");
select: id | filter: day(current_timestamp - interval 1 day) = current_date - interval 2 day and (id = "1" or id = "2");

select: id, second(timestamp - interval 200 millisecond) | filter: id = 4;
select: id, second(timestamp + interval 900000 microsecond) | filter: id = 4;
select: id, second(object.timestamp - interval 200 millisecond) | filter: id = 4;
select: id, second(object.timestamp + interval 900000 microsecond) | filter: id = 4;

select: id, week(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, week(timestamp + interval 67 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, week(timestamp + interval 68 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, day(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, day(timestamp - interval 4 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, day(timestamp - interval 5 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, hour(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, hour(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00';
select: id, hour(timestamp + interval 1 second) | filter: timestamp = '2026-12-31T23:59:59-05:00';

select: id, day('2024-03-31T00:00:00Z' - interval 1 month) | filter: timestamp = '2024-02-29T16:23:00Z';
select: id, day('2024-02-01T00:00:00+01:00' + interval 1 month) | filter: timestamp = '2024-02-29T16:23:00Z';
select: id, month('2024-03-01T00:00:00Z' - interval 1 day) | filter: timestamp = '2024-02-29T16:23:00Z';
select: id, hour(timestamp - interval 1 hour) | filter: timestamp = '2024-02-29T16:23:00Z';
select: id, minute(timestamp + interval 30 second) | filter: timestamp = '2024-02-29T16:23:00Z';
