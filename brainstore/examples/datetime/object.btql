select: id, timestamp | filter: object.timestamp = '2024-02-29T16:23:00Z';
select: id, timestamp | filter: object.timestamp = '2024-02-29T16:23:00+00:00';

select: id, object.timestamp | filter: object.timestamp='2023-11-15T08:45:30+01:00';

select: id, timestamp | filter: object.timestamp >= '2024-02-29T16:23:00Z';
select: id, timestamp | filter: object.timestamp >= '2024-02-29T16:23:00+00:00';

select: id, timestamp | filter: object.timestamp <= '2024-02-29T16:23:00Z';
select: id, timestamp | filter: object.timestamp <= '2024-02-29T16:23:00+00:00';

select: id, timestamp | filter: object.timestamp > '2024-02-29T16:23:00Z';
select: id, timestamp | filter: object.timestamp > '2024-02-29T16:23:00+00:00';

select: id, timestamp | filter: object.timestamp < '2024-02-29T16:23:00Z';
select: id, timestamp | filter: object.timestamp < '2024-02-29T16:23:00+00:00';

select: id, timestamp | filter: object.timestamp >= '2024-02-29';
select: id, timestamp | filter: object.timestamp >= '2024-02-29';

select: id, timestamp | sort: object.timestamp, id | limit: 5;
select: id, timestamp | sort: object.timestamp desc, id | limit: 5;

select: id, object.timestamp AS T | sort: T, id | limit: 5;

select: id, object.timestamp | filter: object.timestamp match '2024-02-29T16:23:00Z';
select: id, object.timestamp | filter: object.timestamp match '2024-02-29T16:23:00+00:00';
select: id, object.timestamp | filter: object.timestamp match '2024-02-29T16:23:00+01:00';
