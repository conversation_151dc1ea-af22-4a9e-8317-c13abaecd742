version: 1
schema:
  name: test
  fields:
    - name: timestamp
      tantivy:
        - name: timestamp
          type: !date
            stored: true
            fast: true
    - name: object
      tantivy:
        - name: object
          type: !json
            stored: true
            tokenize: false
            fast: true
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/fast/metadata
index_uri: ./index/fast/data
wal_uri: ./index/fast/wal
realtime_wal_uri: ./index/fast/wal
