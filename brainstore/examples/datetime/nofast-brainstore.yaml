version: 1
schema:
  name: test
  fields:
    - name: timestamp
      tantivy:
        - name: timestamp
          type: !date
            stored: true
            fast: true
    - name: object
      tantivy:
        - name: object
          type: !json
            stored: true
            tokenize: false
  opts:
    auto_assign_field_ts: true
metadata_uri: ./index/nofast/metadata
index_uri: ./index/nofast/data
wal_uri: ./index/nofast/wal
realtime_wal_uri: ./index/nofast/wal
