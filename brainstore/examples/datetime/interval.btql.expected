[{"error": null, "query": "-- Should equal 2024-02-29T16:23:00Z (id: 1)\nselect: id, timestamp | filter: timestamp = '2020-02-29T16:23:00Z' + interval 3 year", "result_rows": [{"id": 16, "timestamp": "2023-02-28T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2024-01-29T16:23:00Z' - interval 11 month", "result_rows": [{"id": 16, "timestamp": "2023-02-28T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2023-12-31T16:23:00Z' + interval 2 month", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2025-03-31T16:23:00Z' - interval 13 month", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2024-01-30T14:23:00-02:00' + interval 1 month", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2020-02-29T16:23:00Z' + interval 3 year", "result_rows": [{"id": 16, "timestamp": "2023-02-28T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2024-01-29T16:23:00Z' - interval 11 month", "result_rows": [{"id": 16, "timestamp": "2023-02-28T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2023-12-31T16:23:00Z' + interval 2 month", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2025-03-31T16:23:00Z' - interval 13 month", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2024-01-30T14:23:00-02:00' + interval 1 month", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}], "skip": false}, {"error": null, "query": "-- Should equal 2023-11-15T08:45:30+01:00 (id: 2)\nselect: id, timestamp | filter: timestamp = '2023-11-17T08:45:30+01:00' - interval 2 day", "result_rows": [{"id": 2, "timestamp": "2023-11-15T07:45:30Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2023-11-15T06:45:31-01:00' - interval 1 second", "result_rows": [{"id": 2, "timestamp": "2023-11-15T07:45:30Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2023-11-05T08:45:30+01:00' + interval 10 day", "result_rows": [{"id": 2, "timestamp": "2023-11-15T07:45:30Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2023-11-14T23:45:29-08:00' + interval 1 second", "result_rows": [{"id": 2, "timestamp": "2023-11-15T07:45:30Z"}], "skip": false}, {"error": null, "query": "-- Should equal 2026-12-31T23:59:59-05:00 (id: 5)\nselect: id, timestamp | filter: timestamp = '2027-01-01T23:59:59-05:00' - interval 1 day", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2027-01-01T09:59:59-05:00' - interval 10 hour", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2027-01-01T05:00:00Z' - interval 1 second", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2027-01-01T05:00:00Z' - interval 1000000 microsecond", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2026-12-31T23:59:00-05:00' + interval 59000 millisecond", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2027-01-01T23:59:59-05:00' - interval 1 day", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2027-01-01T09:59:59-05:00' - interval 10 hour", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2027-01-01T05:00:00Z' - interval 1 second", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2027-01-01T05:00:00Z' - interval 1000000 microsecond", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2026-12-31T23:59:00-05:00' + interval 59000 millisecond", "result_rows": [{"id": 5, "timestamp": "2027-01-01T04:59:59Z"}], "skip": false}, {"error": null, "query": "-- Should equal 2027-09-30T10:15:00+09:30 (id: 6)\nselect: id, timestamp | filter: timestamp = '2027-09-30T00:45:00Z'", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp < '2027-09-30T10:15:00+09:30'", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp > '2027-09-30T10:15:00+09:30'", "result_rows": [{"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 13, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 14, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 15, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = interval 9 hour + '2027-09-30T01:15:00+09:30'", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = interval 1 month + '2027-08-31T10:15:00+09:30'", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2027-12-31T10:15:00+09:30' - interval 3 month", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2027-09-30T00:45:00Z'", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp < '2027-09-30T10:15:00+09:30'", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp > '2027-09-30T10:15:00+09:30'", "result_rows": [{"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = interval 9 hour + '2027-09-30T01:15:00+09:30'", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = interval 1 month + '2027-08-31T10:15:00+09:30'", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, object.timestamp | filter: object.timestamp = '2027-12-31T10:15:00+09:30' - interval 3 month", "result_rows": [{"id": 6, "timestamp": "2027-09-30T00:45:00Z"}], "skip": false}, {"error": null, "query": "-- Should equal 2031-05-20T14:30:00+05:45 (id: 10)\nselect: id, timestamp | filter: timestamp = '2031-05-20T14:29:59+05:45' + interval 1 second", "result_rows": [{"id": 10, "timestamp": "2031-05-20T08:45:00Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp = '2031-05-20T14:29:00+05:45' + interval 60000 millisecond", "result_rows": [{"id": 10, "timestamp": "2031-05-20T08:45:00Z"}], "skip": false}, {"error": null, "query": "-- Should equal 2017-12-25T00:00:00Z (id: 11)\nselect: id, timestamp | filter: timestamp >= '2017-12-24T12:00:00Z' + interval 12 hour", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 13, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 14, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 15, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 6, "timestamp": "2027-09-30T00:45:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp <= '2017-12-25T12:00:00Z' - interval 12 hour", "result_rows": [{"id": 11, "timestamp": "2017-12-25T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- Tests using current_timestamp and current_timestamp() (should pass until 2030+)\nselect: id, timestamp | filter: timestamp < current_timestamp + interval 10 year", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 13, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 14, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 15, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 6, "timestamp": "2027-09-30T00:45:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp > current_timestamp + interval 10 year", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp > current_timestamp() - interval 15 year", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 13, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 14, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 15, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 6, "timestamp": "2027-09-30T00:45:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp < current_timestamp() - interval 15 year", "result_rows": [], "skip": false}, {"error": null, "query": "-- Tests using current_date and current_date() (should pass until 2030+)\nselect: id, timestamp | filter: timestamp < current_date + interval 10 year", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 13, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 14, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 15, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 6, "timestamp": "2027-09-30T00:45:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp > current_date + interval 10 year", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp > current_date() - interval 15 year", "result_rows": [{"id": 1, "timestamp": "2024-02-29T16:23:00Z"}, {"id": 10, "timestamp": "2031-05-20T08:45:00Z"}, {"id": 11, "timestamp": "2017-12-25T00:00:00Z"}, {"id": 12, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 13, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 14, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 15, "timestamp": "2032-02-15T09:00:00Z"}, {"id": 16, "timestamp": "2023-02-28T16:23:00Z"}, {"id": 17, "timestamp": "2024-02-29T16:23:30Z"}, {"id": 18, "timestamp": "2024-02-29T17:00:00Z"}, {"id": 19, "timestamp": "2024-03-01T00:00:00Z"}, {"id": 2, "timestamp": "2023-11-15T07:45:30Z"}, {"id": 20, "timestamp": "2024-12-31T23:59:59Z"}, {"id": 3, "timestamp": "2025-07-01T07:00:00Z"}, {"id": 4, "timestamp": "2022-03-14T12:30:45.123456Z"}, {"id": 5, "timestamp": "2027-01-01T04:59:59Z"}, {"id": 6, "timestamp": "2027-09-30T00:45:00Z"}, {"id": 7, "timestamp": "2028-01-01T00:00:00Z"}, {"id": 8, "timestamp": "2019-04-01T21:30:00Z"}, {"id": 9, "timestamp": "2030-11-11T11:11:11.111Z"}], "skip": false}, {"error": null, "query": "select: id, timestamp | filter: timestamp < current_date() - interval 15 year", "result_rows": [], "skip": false}]