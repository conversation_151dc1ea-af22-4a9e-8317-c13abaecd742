[{"error": null, "query": "select: id, year(timestamp)", "result_rows": [{"id": 1, "year(timestamp)": "2024-01-01T00:00:00Z"}, {"id": 10, "year(timestamp)": "2031-01-01T00:00:00Z"}, {"id": 11, "year(timestamp)": "2017-01-01T00:00:00Z"}, {"id": 12, "year(timestamp)": "2032-01-01T00:00:00Z"}, {"id": 13, "year(timestamp)": "2032-01-01T00:00:00Z"}, {"id": 14, "year(timestamp)": "2032-01-01T00:00:00Z"}, {"id": 15, "year(timestamp)": "2032-01-01T00:00:00Z"}, {"id": 16, "year(timestamp)": "2023-01-01T00:00:00Z"}, {"id": 17, "year(timestamp)": "2024-01-01T00:00:00Z"}, {"id": 18, "year(timestamp)": "2024-01-01T00:00:00Z"}, {"id": 19, "year(timestamp)": "2024-01-01T00:00:00Z"}, {"id": 2, "year(timestamp)": "2023-01-01T00:00:00Z"}, {"id": 20, "year(timestamp)": "2024-01-01T00:00:00Z"}, {"id": 3, "year(timestamp)": "2025-01-01T00:00:00Z"}, {"id": 4, "year(timestamp)": "2022-01-01T00:00:00Z"}, {"id": 5, "year(timestamp)": "2027-01-01T00:00:00Z"}, {"id": 6, "year(timestamp)": "2027-01-01T00:00:00Z"}, {"id": 7, "year(timestamp)": "2028-01-01T00:00:00Z"}, {"id": 8, "year(timestamp)": "2019-01-01T00:00:00Z"}, {"id": 9, "year(timestamp)": "2030-01-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, month(timestamp)", "result_rows": [{"id": 1, "month(timestamp)": "2024-02-01T00:00:00Z"}, {"id": 10, "month(timestamp)": "2031-05-01T00:00:00Z"}, {"id": 11, "month(timestamp)": "2017-12-01T00:00:00Z"}, {"id": 12, "month(timestamp)": "2032-02-01T00:00:00Z"}, {"id": 13, "month(timestamp)": "2032-02-01T00:00:00Z"}, {"id": 14, "month(timestamp)": "2032-02-01T00:00:00Z"}, {"id": 15, "month(timestamp)": "2032-02-01T00:00:00Z"}, {"id": 16, "month(timestamp)": "2023-02-01T00:00:00Z"}, {"id": 17, "month(timestamp)": "2024-02-01T00:00:00Z"}, {"id": 18, "month(timestamp)": "2024-02-01T00:00:00Z"}, {"id": 19, "month(timestamp)": "2024-03-01T00:00:00Z"}, {"id": 2, "month(timestamp)": "2023-11-01T00:00:00Z"}, {"id": 20, "month(timestamp)": "2024-12-01T00:00:00Z"}, {"id": 3, "month(timestamp)": "2025-07-01T00:00:00Z"}, {"id": 4, "month(timestamp)": "2022-03-01T00:00:00Z"}, {"id": 5, "month(timestamp)": "2027-01-01T00:00:00Z"}, {"id": 6, "month(timestamp)": "2027-09-01T00:00:00Z"}, {"id": 7, "month(timestamp)": "2028-01-01T00:00:00Z"}, {"id": 8, "month(timestamp)": "2019-04-01T00:00:00Z"}, {"id": 9, "month(timestamp)": "2030-11-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, week(timestamp)", "result_rows": [{"id": 1, "week(timestamp)": "2024-02-26T00:00:00Z"}, {"id": 10, "week(timestamp)": "2031-05-19T00:00:00Z"}, {"id": 11, "week(timestamp)": "2017-12-25T00:00:00Z"}, {"id": 12, "week(timestamp)": "2032-02-09T00:00:00Z"}, {"id": 13, "week(timestamp)": "2032-02-09T00:00:00Z"}, {"id": 14, "week(timestamp)": "2032-02-09T00:00:00Z"}, {"id": 15, "week(timestamp)": "2032-02-09T00:00:00Z"}, {"id": 16, "week(timestamp)": "2023-02-27T00:00:00Z"}, {"id": 17, "week(timestamp)": "2024-02-26T00:00:00Z"}, {"id": 18, "week(timestamp)": "2024-02-26T00:00:00Z"}, {"id": 19, "week(timestamp)": "2024-02-26T00:00:00Z"}, {"id": 2, "week(timestamp)": "2023-11-13T00:00:00Z"}, {"id": 20, "week(timestamp)": "2024-12-30T00:00:00Z"}, {"id": 3, "week(timestamp)": "2025-06-30T00:00:00Z"}, {"id": 4, "week(timestamp)": "2022-03-14T00:00:00Z"}, {"id": 5, "week(timestamp)": "2026-12-28T00:00:00Z"}, {"id": 6, "week(timestamp)": "2027-09-27T00:00:00Z"}, {"id": 7, "week(timestamp)": "2027-12-27T00:00:00Z"}, {"id": 8, "week(timestamp)": "2019-04-01T00:00:00Z"}, {"id": 9, "week(timestamp)": "2030-11-11T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, day(timestamp)", "result_rows": [{"day(timestamp)": "2017-12-25T00:00:00Z", "id": 11}, {"day(timestamp)": "2019-04-01T00:00:00Z", "id": 8}, {"day(timestamp)": "2022-03-14T00:00:00Z", "id": 4}, {"day(timestamp)": "2023-02-28T00:00:00Z", "id": 16}, {"day(timestamp)": "2023-11-15T00:00:00Z", "id": 2}, {"day(timestamp)": "2024-02-29T00:00:00Z", "id": 17}, {"day(timestamp)": "2024-02-29T00:00:00Z", "id": 18}, {"day(timestamp)": "2024-02-29T00:00:00Z", "id": 1}, {"day(timestamp)": "2024-03-01T00:00:00Z", "id": 19}, {"day(timestamp)": "2024-12-31T00:00:00Z", "id": 20}, {"day(timestamp)": "2025-07-01T00:00:00Z", "id": 3}, {"day(timestamp)": "2027-01-01T00:00:00Z", "id": 5}, {"day(timestamp)": "2027-09-30T00:00:00Z", "id": 6}, {"day(timestamp)": "2028-01-01T00:00:00Z", "id": 7}, {"day(timestamp)": "2030-11-11T00:00:00Z", "id": 9}, {"day(timestamp)": "2031-05-20T00:00:00Z", "id": 10}, {"day(timestamp)": "2032-02-15T00:00:00Z", "id": 12}, {"day(timestamp)": "2032-02-15T00:00:00Z", "id": 13}, {"day(timestamp)": "2032-02-15T00:00:00Z", "id": 14}, {"day(timestamp)": "2032-02-15T00:00:00Z", "id": 15}], "skip": false}, {"error": null, "query": "select: id, hour(timestamp)", "result_rows": [{"hour(timestamp)": "2017-12-25T00:00:00Z", "id": 11}, {"hour(timestamp)": "2019-04-01T21:00:00Z", "id": 8}, {"hour(timestamp)": "2022-03-14T12:00:00Z", "id": 4}, {"hour(timestamp)": "2023-02-28T16:00:00Z", "id": 16}, {"hour(timestamp)": "2023-11-15T07:00:00Z", "id": 2}, {"hour(timestamp)": "2024-02-29T16:00:00Z", "id": 17}, {"hour(timestamp)": "2024-02-29T16:00:00Z", "id": 1}, {"hour(timestamp)": "2024-02-29T17:00:00Z", "id": 18}, {"hour(timestamp)": "2024-03-01T00:00:00Z", "id": 19}, {"hour(timestamp)": "2024-12-31T23:00:00Z", "id": 20}, {"hour(timestamp)": "2025-07-01T07:00:00Z", "id": 3}, {"hour(timestamp)": "2027-01-01T04:00:00Z", "id": 5}, {"hour(timestamp)": "2027-09-30T00:00:00Z", "id": 6}, {"hour(timestamp)": "2028-01-01T00:00:00Z", "id": 7}, {"hour(timestamp)": "2030-11-11T11:00:00Z", "id": 9}, {"hour(timestamp)": "2031-05-20T08:00:00Z", "id": 10}, {"hour(timestamp)": "2032-02-15T09:00:00Z", "id": 12}, {"hour(timestamp)": "2032-02-15T09:00:00Z", "id": 13}, {"hour(timestamp)": "2032-02-15T09:00:00Z", "id": 14}, {"hour(timestamp)": "2032-02-15T09:00:00Z", "id": 15}], "skip": false}, {"error": null, "query": "select: id, minute(timestamp)", "result_rows": [{"id": 1, "minute(timestamp)": "2024-02-29T16:23:00Z"}, {"id": 10, "minute(timestamp)": "2031-05-20T08:45:00Z"}, {"id": 11, "minute(timestamp)": "2017-12-25T00:00:00Z"}, {"id": 12, "minute(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 13, "minute(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 14, "minute(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 15, "minute(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 16, "minute(timestamp)": "2023-02-28T16:23:00Z"}, {"id": 17, "minute(timestamp)": "2024-02-29T16:23:00Z"}, {"id": 18, "minute(timestamp)": "2024-02-29T17:00:00Z"}, {"id": 19, "minute(timestamp)": "2024-03-01T00:00:00Z"}, {"id": 2, "minute(timestamp)": "2023-11-15T07:45:00Z"}, {"id": 20, "minute(timestamp)": "2024-12-31T23:59:00Z"}, {"id": 3, "minute(timestamp)": "2025-07-01T07:00:00Z"}, {"id": 4, "minute(timestamp)": "2022-03-14T12:30:00Z"}, {"id": 5, "minute(timestamp)": "2027-01-01T04:59:00Z"}, {"id": 6, "minute(timestamp)": "2027-09-30T00:45:00Z"}, {"id": 7, "minute(timestamp)": "2028-01-01T00:00:00Z"}, {"id": 8, "minute(timestamp)": "2019-04-01T21:30:00Z"}, {"id": 9, "minute(timestamp)": "2030-11-11T11:11:00Z"}], "skip": false}, {"error": null, "query": "select: id, second(timestamp)", "result_rows": [{"id": 1, "second(timestamp)": "2024-02-29T16:23:00Z"}, {"id": 10, "second(timestamp)": "2031-05-20T08:45:00Z"}, {"id": 11, "second(timestamp)": "2017-12-25T00:00:00Z"}, {"id": 12, "second(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 13, "second(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 14, "second(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 15, "second(timestamp)": "2032-02-15T09:00:00Z"}, {"id": 16, "second(timestamp)": "2023-02-28T16:23:00Z"}, {"id": 17, "second(timestamp)": "2024-02-29T16:23:30Z"}, {"id": 18, "second(timestamp)": "2024-02-29T17:00:00Z"}, {"id": 19, "second(timestamp)": "2024-03-01T00:00:00Z"}, {"id": 2, "second(timestamp)": "2023-11-15T07:45:30Z"}, {"id": 20, "second(timestamp)": "2024-12-31T23:59:59Z"}, {"id": 3, "second(timestamp)": "2025-07-01T07:00:00Z"}, {"id": 4, "second(timestamp)": "2022-03-14T12:30:45Z"}, {"id": 5, "second(timestamp)": "2027-01-01T04:59:59Z"}, {"id": 6, "second(timestamp)": "2027-09-30T00:45:00Z"}, {"id": 7, "second(timestamp)": "2028-01-01T00:00:00Z"}, {"id": 8, "second(timestamp)": "2019-04-01T21:30:00Z"}, {"id": 9, "second(timestamp)": "2030-11-11T11:11:11Z"}], "skip": false}, {"error": null, "query": "select: day(current_timestamp) = current_date | filter: id = 1", "result_rows": [{"day(current_timestamp) = current_date": true}], "skip": false}, {"error": null, "query": "select: day(current_timestamp) = current_date + interval 1 day | filter: id = 1", "result_rows": [{"day(current_timestamp) = current_date + interval 1 day": false}], "skip": false}, {"error": null, "query": "select: id | filter: day(current_timestamp - interval 1 day) = current_date - interval 1 day and (id = \"1\" or id = \"2\")", "result_rows": [{"id": 1}, {"id": 2}], "skip": false}, {"error": null, "query": "select: id | filter: day(current_timestamp - interval 1 day) = current_date - interval 2 day and (id = \"1\" or id = \"2\")", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, second(timestamp - interval 200 millisecond) | filter: id = 4", "result_rows": [{"id": 4, "second(timestamp - interval 200 millisecond)": "2022-03-14T12:30:44Z"}], "skip": false}, {"error": null, "query": "select: id, second(timestamp + interval 900000 microsecond) | filter: id = 4", "result_rows": [{"id": 4, "second(timestamp + interval 900000 microsecond)": "2022-03-14T12:30:46Z"}], "skip": false}, {"error": null, "query": "select: id, second(object.timestamp - interval 200 millisecond) | filter: id = 4", "result_rows": [{"id": 4, "second(object.timestamp - interval 200 millisecond)": "2022-03-14T12:30:44Z"}], "skip": false}, {"error": null, "query": "select: id, second(object.timestamp + interval 900000 microsecond) | filter: id = 4", "result_rows": [{"id": 4, "second(object.timestamp + interval 900000 microsecond)": "2022-03-14T12:30:46Z"}], "skip": false}, {"error": null, "query": "select: id, week(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"id": 5, "week(timestamp)": "2026-12-28T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, week(timestamp + interval 67 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"id": 5, "week(timestamp + interval 67 hour)": "2026-12-28T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, week(timestamp + interval 68 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"id": 5, "week(timestamp + interval 68 hour)": "2027-01-04T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, day(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"day(timestamp)": "2027-01-01T00:00:00Z", "id": 5}], "skip": false}, {"error": null, "query": "select: id, day(timestamp - interval 4 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"day(timestamp - interval 4 hour)": "2027-01-01T00:00:00Z", "id": 5}], "skip": false}, {"error": null, "query": "select: id, day(timestamp - interval 5 hour) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"day(timestamp - interval 5 hour)": "2026-12-31T00:00:00Z", "id": 5}], "skip": false}, {"error": null, "query": "select: id, hour(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"hour(timestamp)": "2027-01-01T04:00:00Z", "id": 5}], "skip": false}, {"error": null, "query": "select: id, hour(timestamp) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"hour(timestamp)": "2027-01-01T04:00:00Z", "id": 5}], "skip": false}, {"error": null, "query": "select: id, hour(timestamp + interval 1 second) | filter: timestamp = '2026-12-31T23:59:59-05:00'", "result_rows": [{"hour(timestamp + interval 1 second)": "2027-01-01T05:00:00Z", "id": 5}], "skip": false}, {"error": null, "query": "select: id, day('2024-03-31T00:00:00Z' - interval 1 month) | filter: timestamp = '2024-02-29T16:23:00Z'", "result_rows": [{"day('2024-03-31T00:00:00Z' - interval 1 month)": "2024-02-29T00:00:00Z", "id": 1}], "skip": false}, {"error": null, "query": "select: id, day('2024-02-01T00:00:00+01:00' + interval 1 month) | filter: timestamp = '2024-02-29T16:23:00Z'", "result_rows": [{"day('2024-02-01T00:00:00+01:00' + interval 1 month)": "2024-02-29T00:00:00Z", "id": 1}], "skip": false}, {"error": null, "query": "select: id, month('2024-03-01T00:00:00Z' - interval 1 day) | filter: timestamp = '2024-02-29T16:23:00Z'", "result_rows": [{"id": 1, "month('2024-03-01T00:00:00Z' - interval 1 day)": "2024-02-01T00:00:00Z"}], "skip": false}, {"error": null, "query": "select: id, hour(timestamp - interval 1 hour) | filter: timestamp = '2024-02-29T16:23:00Z'", "result_rows": [{"hour(timestamp - interval 1 hour)": "2024-02-29T15:00:00Z", "id": 1}], "skip": false}, {"error": null, "query": "select: id, minute(timestamp + interval 30 second) | filter: timestamp = '2024-02-29T16:23:00Z'", "result_rows": [{"id": 1, "minute(timestamp + interval 30 second)": "2024-02-29T16:23:00Z"}], "skip": false}]