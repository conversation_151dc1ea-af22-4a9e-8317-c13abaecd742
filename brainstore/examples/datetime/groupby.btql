dimensions: year(timestamp) | filter: timestamp > "2020-01-01T00:00:00Z";
dimensions: year(object.timestamp), id | filter: timestamp < "2020-01-01T00:00:00Z";
dimensions: year(timestamp) | measures: count(1);

dimensions: year(object.timestamp) | filter: object.timestamp > "2020-01-01T00:00:00Z";
dimensions: year(object.timestamp), id | filter: object.timestamp < "2020-01-01T00:00:00Z";
dimensions: year(object.timestamp) | measures: count(1);

dimensions: month(timestamp) | measures: count(1) | filter: timestamp < "2025-01-01T00:00:00Z";
dimensions: month(object.timestamp) | measures: count(1) | filter: object.timestamp < "2025-01-01T00:00:00Z";

dimensions: week(timestamp) | measures: count(1);
dimensions: week(object.timestamp) | measures: count(1);

dimensions: week(timestamp - interval 4 day) | measures: count(1);
dimensions: week(object.timestamp - interval 4 day) | measures: count(1);

dimensions: day(timestamp) | measures: count(1);
dimensions: day(object.timestamp) | measures: count(1);

dimensions: hour(timestamp) | measures: count(1);
dimensions: hour(object.timestamp) | measures: count(1);

dimensions: minute(timestamp) | measures: count(1);
dimensions: minute(object.timestamp) | measures: count(1);

dimensions: second(timestamp) | measures: count(1);
dimensions: second(object.timestamp) | measures: count(1);
