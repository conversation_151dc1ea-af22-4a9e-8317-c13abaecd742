-- Should equal 2024-02-29T16:23:00Z (id: 1)
select: id, timestamp | filter: timestamp = '2020-02-29T16:23:00Z' + interval 3 year;
select: id, timestamp | filter: timestamp = '2024-01-29T16:23:00Z' - interval 11 month;
select: id, timestamp | filter: timestamp = '2023-12-31T16:23:00Z' + interval 2 month;
select: id, timestamp | filter: timestamp = '2025-03-31T16:23:00Z' - interval 13 month;
select: id, timestamp | filter: timestamp = '2024-01-30T14:23:00-02:00' + interval 1 month;
select: id, object.timestamp | filter: object.timestamp = '2020-02-29T16:23:00Z' + interval 3 year;
select: id, object.timestamp | filter: object.timestamp = '2024-01-29T16:23:00Z' - interval 11 month;
select: id, object.timestamp | filter: object.timestamp = '2023-12-31T16:23:00Z' + interval 2 month;
select: id, object.timestamp | filter: object.timestamp = '2025-03-31T16:23:00Z' - interval 13 month;
select: id, object.timestamp | filter: object.timestamp = '2024-01-30T14:23:00-02:00' + interval 1 month;

-- Should equal 2023-11-15T08:45:30+01:00 (id: 2)
select: id, timestamp | filter: timestamp = '2023-11-17T08:45:30+01:00' - interval 2 day;
select: id, timestamp | filter: timestamp = '2023-11-15T06:45:31-01:00' - interval 1 second;
select: id, object.timestamp | filter: object.timestamp = '2023-11-05T08:45:30+01:00' + interval 10 day;
select: id, object.timestamp | filter: object.timestamp = '2023-11-14T23:45:29-08:00' + interval 1 second;

-- Should equal 2026-12-31T23:59:59-05:00 (id: 5)
select: id, timestamp | filter: timestamp = '2027-01-01T23:59:59-05:00' - interval 1 day;
select: id, timestamp | filter: timestamp = '2027-01-01T09:59:59-05:00' - interval 10 hour;
select: id, timestamp | filter: timestamp = '2027-01-01T05:00:00Z' - interval 1 second;
select: id, timestamp | filter: timestamp = '2027-01-01T05:00:00Z' - interval 1000000 microsecond;
select: id, timestamp | filter: timestamp = '2026-12-31T23:59:00-05:00' + interval 59000 millisecond;
select: id, object.timestamp | filter: object.timestamp = '2027-01-01T23:59:59-05:00' - interval 1 day;
select: id, object.timestamp | filter: object.timestamp = '2027-01-01T09:59:59-05:00' - interval 10 hour;
select: id, object.timestamp | filter: object.timestamp = '2027-01-01T05:00:00Z' - interval 1 second;
select: id, object.timestamp | filter: object.timestamp = '2027-01-01T05:00:00Z' - interval 1000000 microsecond;
select: id, object.timestamp | filter: object.timestamp = '2026-12-31T23:59:00-05:00' + interval 59000 millisecond;

-- Should equal 2027-09-30T10:15:00+09:30 (id: 6)
select: id, timestamp | filter: timestamp = '2027-09-30T00:45:00Z';
select: id, timestamp | filter: timestamp < '2027-09-30T10:15:00+09:30';
select: id, timestamp | filter: timestamp > '2027-09-30T10:15:00+09:30';
select: id, timestamp | filter: timestamp = interval 9 hour + '2027-09-30T01:15:00+09:30';
select: id, timestamp | filter: timestamp = interval 1 month + '2027-08-31T10:15:00+09:30';
select: id, timestamp | filter: timestamp = '2027-12-31T10:15:00+09:30' - interval 3 month;
select: id, object.timestamp | filter: object.timestamp = '2027-09-30T00:45:00Z';
select: id, object.timestamp | filter: object.timestamp < '2027-09-30T10:15:00+09:30';
select: id, object.timestamp | filter: object.timestamp > '2027-09-30T10:15:00+09:30';
select: id, object.timestamp | filter: object.timestamp = interval 9 hour + '2027-09-30T01:15:00+09:30';
select: id, object.timestamp | filter: object.timestamp = interval 1 month + '2027-08-31T10:15:00+09:30';
select: id, object.timestamp | filter: object.timestamp = '2027-12-31T10:15:00+09:30' - interval 3 month;

-- Should equal 2031-05-20T14:30:00+05:45 (id: 10)
select: id, timestamp | filter: timestamp = '2031-05-20T14:29:59+05:45' + interval 1 second;
select: id, timestamp | filter: timestamp = '2031-05-20T14:29:00+05:45' + interval 60000 millisecond;

-- Should equal 2017-12-25T00:00:00Z (id: 11)
select: id, timestamp | filter: timestamp >= '2017-12-24T12:00:00Z' + interval 12 hour;
select: id, timestamp | filter: timestamp <= '2017-12-25T12:00:00Z' - interval 12 hour;

-- Tests using current_timestamp and current_timestamp() (should pass until 2030+)
select: id, timestamp | filter: timestamp < current_timestamp + interval 10 year;
select: id, timestamp | filter: timestamp > current_timestamp + interval 10 year;
select: id, timestamp | filter: timestamp > current_timestamp() - interval 15 year;
select: id, timestamp | filter: timestamp < current_timestamp() - interval 15 year;

-- Tests using current_date and current_date() (should pass until 2030+)
select: id, timestamp | filter: timestamp < current_date + interval 10 year;
select: id, timestamp | filter: timestamp > current_date + interval 10 year;
select: id, timestamp | filter: timestamp > current_date() - interval 15 year;
select: id, timestamp | filter: timestamp < current_date() - interval 15 year;
