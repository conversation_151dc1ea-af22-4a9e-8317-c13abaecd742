FROM rust:1.86-slim-bookworm AS builder
RUN apt-get update && apt-get upgrade -y && apt-get install -y build-essential curl pkg-config lsof libssl-dev wget binutils jq
ARG GIT_COMMIT_HASH
ENV GIT_COMMIT_HASH=${GIT_COMMIT_HASH}
WORKDIR /usr/src/brainstore
COPY . .
RUN cargo build --release --bin pulse

## Runtime image
FROM debian:bookworm-slim AS pulse
RUN apt-get update && apt-get upgrade -y && apt-get install -y curl pkg-config libssl-dev ca-certificates
COPY --from=builder /usr/src/brainstore/target/release/pulse /usr/local/bin/pulse
HEALTHCHECK --interval=2s --timeout=5s --retries=3 \
    CMD curl --fail -L http://localhost:4319/ || exit 1
EXPOSE 4319
ENTRYPOINT ["pulse"]
