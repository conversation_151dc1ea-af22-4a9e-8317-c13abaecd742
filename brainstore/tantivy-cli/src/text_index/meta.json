{"index_settings": {"docstore_compression": "zstd", "docstore_blocksize": 16384}, "segments": [{"segment_id": "8e7282a6-a8ca-4de5-9d15-6a75092f4654", "max_doc": 4, "deletes": null}], "schema": [{"name": "i", "type": "u64", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "s", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": false}}, {"name": "id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "created", "type": "date", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true, "precision": "nanoseconds"}}, {"name": "_pagination_key", "type": "u64", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "_xact_id", "type": "u64", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "_object_type", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "_object_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "root_span_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "span_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "span_parents", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "is_root", "type": "bool", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "_full_row_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}], "opstamp": 6}