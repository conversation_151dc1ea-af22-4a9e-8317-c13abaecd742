use std::{collections::HashSet, io::BufRead};

use clap::Parser;
use tantivy::{
    doc,
    schema::{Schema, FAST, STORED, TEXT},
    Index, IndexWriter,
};
use util::anyhow::Result;

#[derive(<PERSON><PERSON><PERSON>, Debug, Clone)]
pub struct CreateIndexArgs {
    #[arg(required = true, help = "The path to the file to load")]
    path: String,

    #[arg(help = "Output directory for the index")]
    output_dir: Option<String>,
}

fn find_all_scores(file_name: &str) -> Result<HashSet<String>> {
    let file = std::fs::File::open(file_name)?;
    let reader = std::io::BufReader::new(file);
    let lines = reader.lines();

    let mut all_scores = HashSet::new();
    for line in lines {
        let line = line?;
        let value: serde_json::Value = serde_json::from_str(&line)?;
        let scores = value
            .as_object()
            .unwrap()
            .get("scores")
            .unwrap()
            .as_object()
            .unwrap();

        for key in scores.keys() {
            all_scores.insert(key.to_string());
        }
    }

    Ok(all_scores)
}

pub fn create(args: CreateIndexArgs) -> Result<()> {
    // Read the file line by line, and deserialize each into a JSON value
    let file = std::fs::File::open(&args.path)?;
    let reader = std::io::BufReader::new(file);
    let lines = reader.lines();

    let output_dir = args.output_dir.unwrap_or_else(|| {
        std::env::current_dir()
            .unwrap()
            .join("tantivy-index")
            .to_string_lossy()
            .to_string()
    });

    // Make the output directory if it doesn't exist
    std::fs::create_dir_all(&output_dir)?;

    let mut schema_builder = Schema::builder();
    schema_builder.add_text_field("text", TEXT);
    schema_builder.add_json_field("json", TEXT | STORED);

    let all_scores = find_all_scores(&args.path)?;
    for score in all_scores.iter() {
        schema_builder.add_f64_field(&format!("score_{}", score), STORED | FAST);
    }

    let schema = schema_builder.build();

    let index = Index::create_in_dir(&output_dir, schema.clone())?;

    let mut index_writer: IndexWriter = index.writer(1_000_000_000)?;

    let text_content = schema.get_field("text").unwrap();
    let json_data = schema.get_field("json").unwrap();

    for line in lines {
        let line = line?;
        let value: serde_json::Value = serde_json::from_str(&line)?;
        // TODO: Process the JSON value as needed

        let mut doc = doc!(text_content => line, json_data => value.clone());
        for score in all_scores.iter() {
            let score_field = schema.get_field(&format!("score_{}", score)).unwrap();
            let score_value = value
                .as_object()
                .unwrap()
                .get("scores")
                .unwrap()
                .as_object()
                .unwrap()
                .get(score);
            if let Some(score) = score_value {
                doc.add_f64(score_field, score.as_f64().unwrap());
            }
        }

        index_writer.add_document(doc)?;
    }

    index_writer.commit()?;
    index_writer.garbage_collect_files().wait()?;

    index_writer.wait_merging_threads()?;

    Ok(())
}
