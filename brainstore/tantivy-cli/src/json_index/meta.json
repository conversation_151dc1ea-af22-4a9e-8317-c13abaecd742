{"index_settings": {"docstore_compression": "zstd", "docstore_blocksize": 16384}, "segments": [{"segment_id": "22be0fa7-adb3-41ef-9f97-152ef181c0cf", "max_doc": 60, "deletes": null}], "schema": [{"name": "org_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "project_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "span_attributes_json", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "span_attributes_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "tags", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "tags_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "origin", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "experiment_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "prompt_session_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "log_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "input_json", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": false, "expand_dots_enabled": false}}, {"name": "input_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "output_json", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": false, "expand_dots_enabled": false}}, {"name": "output_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "expected_json", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": false, "expand_dots_enabled": false}}, {"name": "expected_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "error_json", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "error_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "scores", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "metadata_json", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "metadata_text", "type": "text", "options": {"indexing": {"record": "position", "fieldnorms": true, "tokenizer": "default"}, "stored": false, "fast": false}}, {"name": "metrics", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "context", "type": "json_object", "options": {"stored": true, "indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "fast": true, "expand_dots_enabled": false}}, {"name": "id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "created", "type": "date", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true, "precision": "nanoseconds"}}, {"name": "_pagination_key", "type": "u64", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "_xact_id", "type": "u64", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "_object_type", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "_object_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "root_span_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "span_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "span_parents", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}, {"name": "is_root", "type": "bool", "options": {"indexed": true, "fieldnorms": false, "fast": true, "stored": true}}, {"name": "_full_row_id", "type": "text", "options": {"indexing": {"record": "basic", "fieldnorms": true, "tokenizer": "raw"}, "stored": true, "fast": true}}], "opstamp": 62}