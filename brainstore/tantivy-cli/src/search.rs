use std::collections::HashMap;

use clap::<PERSON>rse<PERSON>;
use tantivy::{collector::TopDocs, query::QueryParser, Index, ReloadPolicy, TantivyDocument};

use util::anyhow::Result;

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct SearchArgs {
    #[arg(short, long, help = "The path to the index", name = "index")]
    data: Option<String>,

    #[arg(short, long, help = "Whether to use fuzzy search")]
    fuzzy: bool,

    #[arg(help = "Query to search for")]
    query: String,
}

pub fn search(args: SearchArgs) -> Result<()> {
    let index_path = args.data.unwrap_or_else(|| {
        std::env::current_dir()
            .unwrap()
            .to_string_lossy()
            .to_string()
    });

    let index = Index::open_in_dir(&index_path)?;
    let reader = index
        .reader_builder()
        .reload_policy(ReloadPolicy::OnCommitWithDelay)
        .try_into()?;

    let searcher = reader.searcher();

    let schema = index.schema();

    let fields: Vec<(String, tantivy::schema::Field)> = schema
        .fields()
        .into_iter()
        .map(|(f, e)| (e.name().to_string(), f))
        .collect();

    let mut query_parser =
        QueryParser::for_index(&index, fields.iter().map(|(_, f)| *f).collect::<Vec<_>>());

    if args.fuzzy {
        for (_, field) in &fields {
            query_parser.set_field_fuzzy(*field, true, 1, true);
        }
    }

    let query = query_parser.parse_query(&args.query)?;
    eprintln!("query: {:#?}", query);
    let top_docs = searcher.search(&query, &TopDocs::with_limit(10))?;

    eprintln!("Found {} results\n", top_docs.len());

    for (_score, doc_address) in top_docs {
        let retrieved_doc: TantivyDocument = searcher.doc(doc_address)?;

        let mut field_values = HashMap::new();
        for (name, field) in &fields {
            let value = retrieved_doc.get_first(*field);
            if let Some(value) = value {
                field_values.insert(name.clone(), value);
            }
        }

        eprintln!("{}", serde_json::to_string(&field_values)?);
    }

    Ok(())
}
