[package]
name = "tantivy-cli"
version = "0.1.0"
edition = "2021"

[dependencies]
clap = { workspace = true }
util = { path = "../util" }
serde = { workspace = true }
serde_json = { workspace = true }
tantivy = { path = "../tantivy" }
rand = { workspace = true }
rustc-hash = { workspace = true }
regex-automata = { workspace = true }
tantivy-fst = { workspace = true }
btql = { version = "0.1.0", path = "../btql" }
query = { version = "0.1.0", path = "../query" }
