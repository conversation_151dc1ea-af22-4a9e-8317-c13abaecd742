use proc_macro::TokenStream;
use quote::quote;
use syn::{
    parse::{Parse, ParseStream},
    parse_macro_input, Token,
};

struct TypeInfo {
    name: syn::Ident,
    value_type: syn::Path,
    rust_type: syn::Type,
}

// Parser for N-dimensional input: n, (type1), (type2), ...
struct NDimInput {
    n: usize,
    types: Vec<TypeInfo>,
}

impl Parse for NDimInput {
    fn parse(input: ParseStream) -> syn::Result<Self> {
        // Parse the dimension count
        let n_lit: syn::LitInt = input.parse()?;
        let n = n_lit.base10_parse::<usize>()?;
        input.parse::<Token![,]>()?;

        // Parse the type list
        let mut types = Vec::new();
        while !input.is_empty() {
            // Parse (name, value_type, rust_type)
            let content;
            syn::parenthesized!(content in input);

            let name: syn::Ident = content.parse()?;
            content.parse::<Token![,]>()?;
            let value_type: syn::Path = content.parse()?;
            content.parse::<Token![,]>()?;
            let rust_type: syn::Type = content.parse()?;

            types.push(TypeInfo {
                name,
                value_type,
                rust_type,
            });

            // Parse trailing comma if present
            if !input.is_empty() {
                input.parse::<Token![,]>()?;
            }
        }

        Ok(NDimInput { n, types })
    }
}

#[proc_macro]
pub fn generate_up_to_n_dim_cross_product(input: TokenStream) -> TokenStream {
    // Parse the input as (max_n, type_list)
    let input_parsed = parse_macro_input!(input as NDimInput);
    let max_n = input_parsed.n;
    let types = input_parsed.types;

    // Generate code for all dimensions from 2 up to max_n
    let mut all_outputs = Vec::new();

    for n in 2..=max_n {
        let output = generate_n_dim_code(n, &types);
        all_outputs.push(output);
    }

    let combined = quote! {
        #(#all_outputs)*
    };

    TokenStream::from(combined)
}

fn generate_n_dim_code(n: usize, types: &[TypeInfo]) -> proc_macro2::TokenStream {
    // Generate all N-way cross product enum variants
    let enum_variants = generate_n_dim_enum_variants(n, types);

    // Generate dispatch match arms
    let dispatch_arms = generate_n_dim_dispatch_arms(n, types);

    // Generate collect_block_fallible match arms
    let collect_arms = generate_n_dim_collect_arms(n, types);

    // Generate make_n_dim_collector match arms
    let make_collector_arms = generate_n_dim_make_collector_arms(n, types);

    // Generate names based on N
    let enum_name = match n {
        2 => quote! { TwoDimDynamicSegmentCollector },
        3 => quote! { ThreeDimDynamicSegmentCollector },
        4 => quote! { FourDimDynamicSegmentCollector },
        _ => {
            let name = syn::Ident::new(
                &format!("Dim{}DynamicSegmentCollector", n),
                proc_macro2::Span::call_site(),
            );
            quote! { #name }
        }
    };

    let wrapper_name = match n {
        2 => quote! { TwoDimWrapper },
        3 => quote! { ThreeDimWrapper },
        4 => quote! { FourDimWrapper },
        _ => {
            let name = syn::Ident::new(&format!("Dim{}Wrapper", n), proc_macro2::Span::call_site());
            quote! { #name }
        }
    };

    let collector_name = syn::Ident::new(
        &format!("TupleDimSegmentCollector{}", n),
        proc_macro2::Span::call_site(),
    );
    let method_name = match n {
        2 => syn::Ident::new("make_two_dim_collector", proc_macro2::Span::call_site()),
        3 => syn::Ident::new("make_three_dim_collector", proc_macro2::Span::call_site()),
        4 => syn::Ident::new("make_four_dim_collector", proc_macro2::Span::call_site()),
        _ => syn::Ident::new(
            &format!("make_{}_dim_collector", n),
            proc_macro2::Span::call_site(),
        ),
    };
    let dispatch_macro_name = match n {
        2 => syn::Ident::new("dispatch_two_dim", proc_macro2::Span::call_site()),
        3 => syn::Ident::new("dispatch_three_dim", proc_macro2::Span::call_site()),
        4 => syn::Ident::new("dispatch_four_dim", proc_macro2::Span::call_site()),
        _ => syn::Ident::new(
            &format!("dispatch_{}_dim", n),
            proc_macro2::Span::call_site(),
        ),
    };

    // Generate dimension parameters (dim1, dim2, ..., dimN)
    let dim_params: Vec<_> = (0..n)
        .map(|i| {
            let name = syn::Ident::new(&format!("dim{}", i + 1), proc_macro2::Span::call_site());
            quote! { #name: ColumnarExpr }
        })
        .collect();

    let dim_names: Vec<_> = (0..n)
        .map(|i| syn::Ident::new(&format!("dim{}", i + 1), proc_macro2::Span::call_site()))
        .collect();

    // Generate wrapper struct for N > 2 if needed
    let wrapper_struct = if n > 2 {
        let type_params: Vec<_> = (0..n)
            .map(|i| {
                let t = syn::Ident::new(&format!("T{}", i + 1), proc_macro2::Span::call_site());
                quote! { #t: PrimitiveColumnarType }
            })
            .collect();

        let type_names: Vec<_> = (0..n)
            .map(|i| syn::Ident::new(&format!("T{}", i + 1), proc_macro2::Span::call_site()))
            .collect();

        let indices: Vec<_> = (0..n).map(syn::Index::from).collect();

        quote! {
            // Wrapper to convert N-dimensional tuple fruits to Vec fruits
            pub struct #wrapper_name<#(#type_params),*> {
                inner: #collector_name<#(#type_names),*>,
            }

            impl<#(#type_params),*> FallibleCollector for #wrapper_name<#(#type_names),*> {
                type Fruit = FastHashMap<Vec<Value>, Vec<ValueAggregator>>;

                fn collect_block_fallible(
                    &mut self,
                    docs: &[tantivy::DocId],
                ) -> Result<(), tantivy::TantivyError> {
                    self.inner.collect_block_fallible(docs)
                }

                fn into_fruit(self) -> Self::Fruit {
                    let fruit = self.inner.into_fruit();
                    FastHashMap::from_iter(
                        fruit
                            .into_iter()
                            .map(|(tuple_key, aggs)| {
                                let vec_key = vec![
                                    #(tuple_key.#indices.into_value()),*
                                ];
                                (vec_key, aggs)
                            })
                    )
                }

                fn fruit_len(fruit: &Self::Fruit) -> usize {
                    fruit.len()
                }
            }
        }
    } else {
        quote! {}
    };

    quote! {
        #wrapper_struct

        // Generate the enum with all N-way combinations
        pub enum #enum_name {
            #(#enum_variants)*
        }

        // Generate dispatch macro for cleaner match statements
        macro_rules! #dispatch_macro_name {
            ($self:expr, $method:ident) => {
                match $self {
                    #(#dispatch_arms)*
                }
            };
        }

        impl FallibleCollector for #enum_name {
            type Fruit = FastHashMap<Vec<Value>, Vec<ValueAggregator>>;

            fn collect_block_fallible(
                &mut self,
                docs: &[tantivy::DocId],
            ) -> Result<(), tantivy::TantivyError> {
                match self {
                    #(#collect_arms)*
                }
            }

            fn into_fruit(self) -> Self::Fruit {
                #dispatch_macro_name!(self, into_fruit)
            }

            fn fruit_len(fruit: &Self::Fruit) -> usize {
                fruit.len()
            }
        }

        // Generate the make_n_dim_collector method
        impl TupleDimDynamicCollector {
            fn #method_name(
                &self,
                ctx: ColumnarExprContext,
                #(#dim_params,)*
                columns: &Vec<Option<DynamicColumn>>,
                unpivot_fields: &Vec<Vec<UnpivotColumnarInfo>>,
            ) -> tantivy::Result<#enum_name> {
                use #enum_name::*;

                macro_rules! make_collector {
                    ($($type:ty),+) => {{
                        let collector = #collector_name::<$(Option<$type>),+> {
                            ctx,
                            dims: (#(#dim_names.clone()),*),
                            aggs: make_static_aggregators(&self.aggs, columns, unpivot_fields)?,
                            columns: columns.clone(),
                            buckets: FastHashMap::default(),
                            agg_exprs: Vec::with_capacity(self.aggs.len()),
                            unpivot_fields: unpivot_fields.clone(),
                            tracer: self.tracer.clone(),
                            _phantom: std::marker::PhantomData,
                        };
                        #wrapper_name { inner: collector }
                    }};
                }

                #(#make_collector_arms)*
            }
        }
    }
}

fn generate_n_dim_enum_variants(n: usize, types: &[TypeInfo]) -> Vec<proc_macro2::TokenStream> {
    let mut variants = Vec::new();

    // Generate all N-way cross products recursively
    fn generate_combinations(
        n: usize,
        types: &[TypeInfo],
        current: Vec<&TypeInfo>,
        variants: &mut Vec<proc_macro2::TokenStream>,
    ) {
        if current.len() == n {
            // Generate variant name by concatenating type names
            let variant_name = syn::Ident::new(
                &current
                    .iter()
                    .map(|t| t.name.to_string())
                    .collect::<String>(),
                proc_macro2::Span::call_site(),
            );

            // Generate type parameters
            let type_params: Vec<_> = current
                .iter()
                .map(|t| {
                    let rust_type = &t.rust_type;
                    quote! { Option<#rust_type> }
                })
                .collect();

            // Use appropriate wrapper based on N
            let wrapper_name = match n {
                2 => quote! { TwoDimWrapper },
                3 => quote! { ThreeDimWrapper },
                4 => quote! { FourDimWrapper },
                _ => {
                    let name = syn::Ident::new(
                        &format!("Dim{}Wrapper", n),
                        proc_macro2::Span::call_site(),
                    );
                    quote! { #name }
                }
            };

            variants.push(quote! {
                #variant_name(#wrapper_name<#(#type_params),*>),
            });
            return;
        }

        // Generate all combinations
        for type_info in types {
            let mut new_current = current.clone();
            new_current.push(type_info);
            generate_combinations(n, types, new_current, variants);
        }
    }

    generate_combinations(n, types, vec![], &mut variants);
    variants
}

fn generate_n_dim_dispatch_arms(n: usize, types: &[TypeInfo]) -> Vec<proc_macro2::TokenStream> {
    let mut arms = Vec::new();
    let enum_name = match n {
        2 => quote! { TwoDimDynamicSegmentCollector },
        3 => quote! { ThreeDimDynamicSegmentCollector },
        4 => quote! { FourDimDynamicSegmentCollector },
        _ => {
            let name = syn::Ident::new(
                &format!("Dim{}DynamicSegmentCollector", n),
                proc_macro2::Span::call_site(),
            );
            quote! { #name }
        }
    };

    fn generate_combinations_arms(
        n: usize,
        types: &[TypeInfo],
        current: Vec<&TypeInfo>,
        arms: &mut Vec<proc_macro2::TokenStream>,
        enum_name: &proc_macro2::TokenStream,
    ) {
        if current.len() == n {
            let variant_name = syn::Ident::new(
                &current
                    .iter()
                    .map(|t| t.name.to_string())
                    .collect::<String>(),
                proc_macro2::Span::call_site(),
            );

            arms.push(quote! {
                #enum_name::#variant_name(c) => c.$method(),
            });
            return;
        }

        for type_info in types {
            let mut new_current = current.clone();
            new_current.push(type_info);
            generate_combinations_arms(n, types, new_current, arms, enum_name);
        }
    }

    generate_combinations_arms(n, types, vec![], &mut arms, &enum_name);
    arms
}

fn generate_n_dim_collect_arms(n: usize, types: &[TypeInfo]) -> Vec<proc_macro2::TokenStream> {
    let mut arms = Vec::new();
    let enum_name = match n {
        2 => quote! { TwoDimDynamicSegmentCollector },
        3 => quote! { ThreeDimDynamicSegmentCollector },
        4 => quote! { FourDimDynamicSegmentCollector },
        _ => {
            let name = syn::Ident::new(
                &format!("Dim{}DynamicSegmentCollector", n),
                proc_macro2::Span::call_site(),
            );
            quote! { #name }
        }
    };

    fn generate_combinations_collect(
        n: usize,
        types: &[TypeInfo],
        current: Vec<&TypeInfo>,
        arms: &mut Vec<proc_macro2::TokenStream>,
        enum_name: &proc_macro2::TokenStream,
    ) {
        if current.len() == n {
            let variant_name = syn::Ident::new(
                &current
                    .iter()
                    .map(|t| t.name.to_string())
                    .collect::<String>(),
                proc_macro2::Span::call_site(),
            );

            arms.push(quote! {
                #enum_name::#variant_name(c) => c.collect_block_fallible(docs),
            });
            return;
        }

        for type_info in types {
            let mut new_current = current.clone();
            new_current.push(type_info);
            generate_combinations_collect(n, types, new_current, arms, enum_name);
        }
    }

    generate_combinations_collect(n, types, vec![], &mut arms, &enum_name);
    arms
}

fn generate_n_dim_make_collector_arms(
    n: usize,
    types: &[TypeInfo],
) -> Vec<proc_macro2::TokenStream> {
    if n == 2 {
        // Special case for 2D to keep existing behavior
        return generate_2d_make_collector_arms(types);
    }

    // For N > 2, generate a more complex match
    let match_expr = generate_n_dim_match_expression(n, types);
    vec![match_expr]
}

fn generate_2d_make_collector_arms(types: &[TypeInfo]) -> Vec<proc_macro2::TokenStream> {
    let mut arms = Vec::new();

    for type1 in types {
        for type2 in types {
            let variant_name = syn::Ident::new(
                &format!("{}{}", type1.name, type2.name),
                proc_macro2::Span::call_site(),
            );
            let value_type1 = &type1.value_type;
            let value_type2 = &type2.value_type;
            let rust_type1 = &type1.rust_type;
            let rust_type2 = &type2.rust_type;

            arms.push(quote! {
                (#value_type1, #value_type2) => Ok(#variant_name(make_collector!(#rust_type1, #rust_type2))),
            });
        }
    }

    // Add catch-all for Dynamic types
    arms.push(quote! {
        (ColumnarValueType::Dynamic, _) | (_, ColumnarValueType::Dynamic) => {
            Err(tantivy::TantivyError::InternalError(
                "Dynamic dimension type should have been resolved".to_string(),
            ))
        }
    });

    // Generate the actual match expression with dim1 and dim2
    vec![quote! {
        match (dim1.columnar_type(), dim2.columnar_type()) {
            #(#arms)*
        }
    }]
}

fn generate_n_dim_match_expression(n: usize, types: &[TypeInfo]) -> proc_macro2::TokenStream {
    // Generate dimension names (dim1, dim2, ..., dimN)
    let dim_names: Vec<_> = (0..n)
        .map(|i| syn::Ident::new(&format!("dim{}", i + 1), proc_macro2::Span::call_site()))
        .collect();

    // Generate all N-way combinations recursively
    let mut arms = Vec::new();

    fn generate_match_arms_recursive(
        n: usize,
        types: &[TypeInfo],
        current_types: Vec<&TypeInfo>,
        arms: &mut Vec<proc_macro2::TokenStream>,
    ) {
        if current_types.len() == n {
            // Generate variant name by concatenating all type names
            let variant_name = syn::Ident::new(
                &current_types
                    .iter()
                    .map(|t| t.name.to_string())
                    .collect::<String>(),
                proc_macro2::Span::call_site(),
            );

            // Generate the value type tuple pattern
            let value_types: Vec<_> = current_types.iter().map(|t| &t.value_type).collect();

            // Generate the rust type parameters
            let rust_types: Vec<_> = current_types
                .iter()
                .map(|t| {
                    let rust_type = &t.rust_type;
                    quote! { #rust_type }
                })
                .collect();

            arms.push(quote! {
                (#(#value_types),*) => Ok(#variant_name(make_collector!(#(#rust_types),*))),
            });
            return;
        }

        // Recursively generate all combinations
        for type_info in types {
            let mut new_current = current_types.clone();
            new_current.push(type_info);
            generate_match_arms_recursive(n, types, new_current, arms);
        }
    }

    generate_match_arms_recursive(n, types, vec![], &mut arms);

    // Add catch-all for Dynamic types
    let dynamic_pattern = if n == 1 {
        quote! { (ColumnarValueType::Dynamic) }
    } else {
        // Generate pattern like: (_, _, Dynamic) | (_, Dynamic, _) | (Dynamic, _, _) etc.
        let mut patterns = Vec::new();
        for i in 0..n {
            let pattern_parts: Vec<_> = (0..n)
                .map(|j| {
                    if i == j {
                        quote! { ColumnarValueType::Dynamic }
                    } else {
                        quote! { _ }
                    }
                })
                .collect();
            patterns.push(quote! { (#(#pattern_parts),*) });
        }
        quote! { #(#patterns)|* }
    };

    arms.push(quote! {
        #dynamic_pattern => {
            Err(tantivy::TantivyError::InternalError(
                "Dynamic dimension type should have been resolved".to_string(),
            ))
        }
    });

    quote! {
        match (#(#dim_names.columnar_type()),*) {
            #(#arms)*
        }
    }
}
