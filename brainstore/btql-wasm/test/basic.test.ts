import { describe, beforeAll, test, expect } from "vitest";
import * as btql_wasm from "../dist/btql_wasm.js";

describe("btql-wasm", () => {
  beforeAll(() => {
    btql_wasm.init();
  });

  describe("errors", () => {
    test("parse raw expr", () => {
      const invalid_raw_exprs = [Symbol("foo"), new Set()];
      for (const raw_expr of invalid_raw_exprs) {
        expect(() => btql_wasm.interpret_bound_expr(raw_expr, {})).toThrow(
          "Failed to parse raw expr",
        );
      }
    });

    test("parse expr", () => {
      const invalid_exprs = [
        null,
        undefined,
        "",
        {},
        [],
        "foo",
        true,
        false,
        12,
        NaN,
        Infinity,
        new Date(),
        new Map(),
        new Error(),
      ];
      for (const expr of invalid_exprs) {
        expect(() => btql_wasm.interpret_bound_expr(expr, {})).toThrow(
          "Failed to parse expr",
        );
      }
    });

    test("parse value", () => {
      const invalid_values = [Symbol("foo"), new Set()];
      const expr = {
        op: "literal",
        value: 1,
        type: "integer",
      };
      for (const value of invalid_values) {
        expect(
          // @ts-ignore  -- deliberately check invalid types
          () => btql_wasm.interpret_bound_expr(expr, value),
        ).toThrow("Failed to parse value");
      }
    });

    test("interpret", () => {
      const expr = {
        op: "not",
        expr: {
          op: "literal",
          value: "foo",
          type: "string",
        },
      };
      expect(() => btql_wasm.interpret_bound_expr(expr, {})).toThrow(
        "Failed to interpret expr",
      );
    });
  });

  test("plus", () => {
    const result = btql_wasm.interpret_bound_expr(
      {
        op: "add",
        left: {
          op: "literal",
          value: 1,
          type: "integer",
        },
        right: {
          op: "literal",
          value: 2,
          type: "integer",
        },
      },
      {},
    );
    expect(result).toBe(3);
  });

  test("match", () => {
    const cases = [
      ["foobar", "foo", true],
      ["foobar", "bar", true],
      ["foobar", "foobar", true],
      ["foobar", "", true],
      ["foobar", null, false],
      ["foobar", undefined, false],
      [null, "foo", undefined],
      [new Date().toISOString(), new Date().toISOString().slice(0, 10), true],
    ];
    for (const [left, right, expected] of cases) {
      const expr = {
        op: "match",
        left: {
          op: "literal",
          value: left,
          type: "string",
        },
        right: {
          op: "literal",
          value: right,
          type: "string",
        },
      };
      expect(btql_wasm.interpret_bound_expr(expr, {})).toBe(expected);
    }
  });
});
