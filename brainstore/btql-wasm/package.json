{"name": "@braintrust/btql-wasm", "description": "WASM bindings for BTQL", "version": "0.1.0", "scripts": {"build": "wasm-pack build --target nodejs --release --out-dir dist --no-pack", "test": "vitest"}, "files": ["dist/btql_wasm_bg.wasm", "dist/btql_wasm.js", "dist/btql_wasm_bg.js", "dist/btql_wasm.d.ts"], "main": "dist/btql_wasm.js", "types": "dist/btql_wasm.d.ts", "sideEffects": ["./dist/btql_wasm.js"], "devDependencies": {"ts-jest": "^29.3.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.9"}}