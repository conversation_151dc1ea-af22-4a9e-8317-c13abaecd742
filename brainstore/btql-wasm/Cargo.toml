[package]
name = "btql-wasm"
version = "0.1.0"
edition = "2021"
description = "WASM bindings for BTQL"

[dependencies]
getrandom = { workspace = true }
wasm-bindgen = { workspace = true }
util = { path = "../util" }
btql = { path = "../btql", default-features = false, features = ["wasm"] }
serde-wasm-bindgen = { workspace = true }
console_error_panic_hook = { workspace = true }

[lib]
crate-type = ["cdylib"]
