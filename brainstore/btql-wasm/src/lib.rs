use std::{borrow::Cow, panic};

use util::serde_json;
use wasm_bindgen::prelude::*;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    pub fn log(s: &str);
}

#[wasm_bindgen]
pub fn init() {
    panic::set_hook(Box::new(console_error_panic_hook::hook));
}

#[wasm_bindgen(skip_typescript)]
pub fn interpret_bound_expr(
    expr: JsValue,
    value: JsValue,
) -> Result<JsValue, serde_wasm_bindgen::Error> {
    let raw_expr = serde_wasm_bindgen::from_value(expr)
        .map_err(|_| serde_wasm_bindgen::Error::new("Failed to parse raw expr"))?;
    let expr: btql::binder::json_ast::BoundExpr = serde_json::from_value(raw_expr)
        .map_err(|_| serde_wasm_bindgen::Error::new("Failed to parse expr"))?;
    let bound_expr: btql::binder::ast::Expr = btql::binder::ast::Expr::try_from(expr)
        .map_err(|_| serde_wasm_bindgen::Error::new("Failed to convert expr"))?;

    let ctx = btql::interpreter::context::ExprContext::new(None);
    let value = serde_wasm_bindgen::from_value(value)
        .map_err(|_| serde_wasm_bindgen::Error::new("Failed to parse value"))?;
    let rows = vec![Cow::Owned(value)];
    let mut results = btql::interpreter::expr::interpret_expr(&ctx, &bound_expr, &rows)
        .map_err(|_| serde_wasm_bindgen::Error::new("Failed to interpret expr"))?;

    let result = results.remove(0);
    serde_wasm_bindgen::to_value(result.as_ref())
}

#[wasm_bindgen(typescript_custom_section)]
const INTERPRET_BOUND_EXPR: &'static str = r#"
import { BoundExpr } from '@braintrust/btql';
export function interpret_bound_expr(expr: BoundExpr, value: Record<string, unknown>): unknown;
"#;
