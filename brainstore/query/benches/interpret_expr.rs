use btql::binder::ast::{Expr, Field, Function, Literal, TernaryCond};
use btql::interpreter::context::ExprContext;
use btql::interpreter::expr::interpret_expr;
use btql::schema::ScalarType;
use criterion::{criterion_group, criterion_main, Criterion};
use std::borrow::Cow;
use util::json::PathPiece;
use util::Value;

// The following benchmarks were run on arm64 (M3 Pro) before and after landing
// "Reduce some cloning in interpreter" (https://github.com/braintrustdata/braintrust/pull/4499).
//
// Before
// (on https://github.com/braintrustdata/braintrust/commit/23f7029258c117e801aafba6d5ce9709060acf61):
//
//     interpret_expr_perf_literal
//                             time:   [33.977 ms 34.343 ms 34.725 ms]
//     Found 2 outliers among 100 measurements (2.00%)
//       2 (2.00%) high mild
//
//     interpret_expr_perf_literal_field
//                             time:   [46.217 ms 46.823 ms 47.442 ms]
//
//     interpret_expr_perf_ternary_strings
//                             time:   [44.615 ms 45.150 ms 45.720 ms]
//     Found 6 outliers among 100 measurements (6.00%)
//       6 (6.00%) high mild
//
//     interpret_expr_perf_ternary_fields
//                             time:   [45.681 ms 46.289 ms 46.950 ms]
//     Found 4 outliers among 100 measurements (4.00%)
//       3 (3.00%) high mild
//       1 (1.00%) high severe
//
//     Benchmarking interpret_expr_perf_insert_basic: Warming up for 3.0000 s
//     Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 8.2s, or reduce sample count to 60.
//     interpret_expr_perf_insert_basic
//                             time:   [79.640 ms 80.479 ms 81.325 ms]
//     Found 1 outliers among 100 measurements (1.00%)
//       1 (1.00%) low mild
//
//     Benchmarking interpret_expr_perf_insert_nested: Warming up for 3.0000 s
//     Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 7.9s, or reduce sample count to 60.
//     interpret_expr_perf_insert_nested
//                             time:   [81.187 ms 82.167 ms 83.206 ms]
//     Found 4 outliers among 100 measurements (4.00%)
//       4 (4.00%) high mild
//
// After applying https://github.com/braintrustdata/braintrust/pull/4499:
//
//     interpret_expr_perf_literal
//                             time:   [33.920 ms 34.551 ms 35.231 ms]
//                             change: [-1.7744% +0.6054% +2.8675%] (p = 0.59 > 0.05)
//                             No change in performance detected.
//     Found 2 outliers among 100 measurements (2.00%)
//       2 (2.00%) high mild
//
//     interpret_expr_perf_literal_field
//                             time:   [30.796 ms 31.197 ms 31.596 ms]
//                             change: [-34.628% -33.373% -32.179%] (p = 0.00 < 0.05)
//                             Performance has improved.
//
//     interpret_expr_perf_ternary_strings
//                             time:   [30.641 ms 31.211 ms 31.826 ms]
//                             change: [-32.596% -30.872% -29.260%] (p = 0.00 < 0.05)
//                             Performance has improved.
//     Found 5 outliers among 100 measurements (5.00%)
//       3 (3.00%) high mild
//       2 (2.00%) high severe
//
//     interpret_expr_perf_ternary_fields
//                             time:   [31.462 ms 31.816 ms 32.181 ms]
//                             change: [-32.551% -31.267% -30.075%] (p = 0.00 < 0.05)
//                             Performance has improved.
//     Found 7 outliers among 100 measurements (7.00%)
//       2 (2.00%) low mild
//       5 (5.00%) high mild
//
//     Benchmarking interpret_expr_perf_insert_basic: Warming up for 3.0000 s
//     Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 5.1s, or reduce sample count to 90.
//     interpret_expr_perf_insert_basic
//                             time:   [43.889 ms 44.549 ms 45.207 ms]
//                             change: [-45.652% -44.645% -43.637%] (p = 0.00 < 0.05)
//                             Performance has improved.
//
//     Benchmarking interpret_expr_perf_insert_nested: Warming up for 3.0000 s
//     Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 5.2s, or reduce sample count to 90.
//     interpret_expr_perf_insert_nested
//                             time:   [48.523 ms 49.472 ms 50.629 ms]
//                             change: [-41.207% -39.790% -38.192%] (p = 0.00 < 0.05)
//                             Performance has improved.
//     Found 8 outliers among 100 measurements (8.00%)
//       1 (1.00%) low mild
//       5 (5.00%) high mild
//       2 (2.00%) high severe

pub fn criterion_benchmark(c: &mut Criterion) {
    c.bench_function("interpret_expr_perf_literal", |b| {
        b.iter(|| interpret_expr_perf_literal())
    });
    c.bench_function("interpret_expr_perf_literal_field", |b| {
        b.iter(|| interpret_expr_perf_literal_field())
    });
    c.bench_function("interpret_expr_perf_ternary_strings", |b| {
        b.iter(|| interpret_expr_perf_ternary_strings())
    });
    c.bench_function("interpret_expr_perf_ternary_fields", |b| {
        b.iter(|| interpret_expr_perf_ternary_fields())
    });
    c.bench_function("interpret_expr_perf_insert_basic", |b| {
        b.iter(|| interpret_expr_perf_insert_basic())
    });
    c.bench_function("interpret_expr_perf_insert_nested", |b| {
        b.iter(|| interpret_expr_perf_insert_nested())
    });
}

fn interpret_expr_perf_literal() {
    let str1: String = "This is string one! ".repeat(100_000); // ~2MB
    let str2: String = "This is string two! ".repeat(100_000); // ~2MB

    let ctx = ExprContext::new(None);
    let num_rows = 100;

    let mut row_map = serde_json::Map::new();
    row_map.insert("str1".to_string(), Value::String(str1.clone()));
    row_map.insert("str2".to_string(), Value::String(str2.clone()));
    let row = Value::Object(row_map);

    let rows: Vec<_> = std::iter::repeat(Cow::Owned(row)).take(num_rows).collect();

    let literal_expr = Expr::Literal(Literal {
        value: Value::String(str1),
        expr_type: ScalarType::String,
    });

    let result = interpret_expr(&ctx, &literal_expr, &rows).expect("Literal evaluation failed");
    assert_eq!(result.len(), num_rows);
}

fn interpret_expr_perf_literal_field() {
    let str1: String = "This is string one! ".repeat(100_000);
    let str2: String = "This is string two! ".repeat(100_000);

    let ctx = ExprContext::new(None);
    let num_rows = 100;

    let mut row_map = serde_json::Map::new();
    row_map.insert("str1".to_string(), Value::String(str1.clone()));
    row_map.insert("str2".to_string(), Value::String(str2.clone()));
    let row = Value::Object(row_map);

    let rows: Vec<_> = std::iter::repeat(Cow::Owned(row)).take(num_rows).collect();

    let field_expr = Expr::Field(Field {
        name: vec![PathPiece::Key("str1".to_string())],
        expr_type: serde_json::json!({ "type": "string" }),
        scalar_type: ScalarType::String,
        source: None,
    });

    let result = interpret_expr(&ctx, &field_expr, &rows).expect("Field evaluation failed");
    assert_eq!(result.len(), num_rows);
}

fn interpret_expr_perf_ternary_strings() {
    let str1: String = "This is string one! ".repeat(100_000);
    let str2: String = "This is string two! ".repeat(100_000);

    let ctx = ExprContext::new(None);
    let num_rows = 100;

    let mut row_map = serde_json::Map::new();
    row_map.insert("str1".to_string(), Value::String(str1.clone()));
    row_map.insert("str2".to_string(), Value::String(str2.clone()));
    let row = Value::Object(row_map);

    let rows: Vec<_> = std::iter::repeat(Cow::Owned(row)).take(num_rows).collect();

    let ternary_expr = Expr::Ternary {
        conds: vec![TernaryCond {
            cond: Box::new(Expr::Literal(Literal {
                value: Value::Bool(true),
                expr_type: ScalarType::Boolean,
            })),
            then: Box::new(Expr::Literal(Literal {
                value: Value::String(str1.clone()),
                expr_type: ScalarType::String,
            })),
        }],
        else_expr: Box::new(Expr::Literal(Literal {
            value: Value::String(str2.clone()),
            expr_type: ScalarType::String,
        })),
    };

    let result = interpret_expr(&ctx, &ternary_expr, &rows).expect("Ternary evaluation failed");
    assert_eq!(result.len(), num_rows);
}

fn interpret_expr_perf_ternary_fields() {
    let str1: String = "This is string one! ".repeat(100_000);
    let str2: String = "This is string two! ".repeat(100_000);

    let ctx = ExprContext::new(None);
    let num_rows = 100;

    let mut row_map = serde_json::Map::new();
    row_map.insert("str1".to_string(), Value::String(str1.clone()));
    row_map.insert("str2".to_string(), Value::String(str2.clone()));
    let row = Value::Object(row_map);

    let rows: Vec<_> = std::iter::repeat(Cow::Owned(row)).take(num_rows).collect();

    let ternary_field_expr = Expr::Ternary {
        conds: vec![TernaryCond {
            cond: Box::new(Expr::Literal(Literal {
                value: Value::Bool(true),
                expr_type: ScalarType::Boolean,
            })),
            then: Box::new(Expr::Field(Field {
                name: vec![PathPiece::Key("str1".to_string())],
                expr_type: serde_json::json!({ "type": "string" }),
                scalar_type: ScalarType::String,
                source: None,
            })),
        }],
        else_expr: Box::new(Expr::Field(Field {
            name: vec![PathPiece::Key("str2".to_string())],
            expr_type: serde_json::json!({ "type": "string" }),
            scalar_type: ScalarType::String,
            source: None,
        })),
    };

    let result =
        interpret_expr(&ctx, &ternary_field_expr, &rows).expect("Ternary field evaluation failed");
    assert_eq!(result.len(), num_rows);
}

fn interpret_expr_perf_insert_basic() {
    let large_model_str = "GPT-4 is a large language model! ".repeat(100_000);

    let ctx = ExprContext::new(None);
    let num_rows = 100;

    let mut metadata = serde_json::Map::new();
    metadata.insert("model".to_string(), Value::String(large_model_str.clone()));
    let mut row_map = serde_json::Map::new();
    row_map.insert("metadata".to_string(), Value::Object(metadata));
    let row = Value::Object(row_map);

    let rows: Vec<_> = std::iter::repeat(Cow::Owned(row)).take(num_rows).collect();

    let insert_field_expr = Expr::Function(Function {
        name: "insert".to_string(),
        args: vec![
            Box::new(Expr::Literal(Literal {
                value: Value::Object(serde_json::Map::new()),
                expr_type: ScalarType::Object,
            })),
            Box::new(Expr::Literal(Literal {
                value: Value::String("model".to_string()),
                expr_type: ScalarType::String,
            })),
            Box::new(Expr::Field(Field {
                name: vec![
                    PathPiece::Key("metadata".to_string()),
                    PathPiece::Key("model".to_string()),
                ],
                expr_type: serde_json::json!({ "type": "string" }),
                scalar_type: ScalarType::String,
                source: None,
            })),
        ],
    });

    let result =
        interpret_expr(&ctx, &insert_field_expr, &rows).expect("Insert field evaluation failed");
    assert_eq!(result.len(), num_rows);
}

fn interpret_expr_perf_insert_nested() {
    let large_model_str = "GPT-4 is a large language model! ".repeat(100_000);

    let ctx = ExprContext::new(None);
    let num_rows = 100;

    let mut metadata = serde_json::Map::new();
    metadata.insert("model".to_string(), Value::String(large_model_str.clone()));
    let mut row_map = serde_json::Map::new();
    row_map.insert("metadata".to_string(), Value::Object(metadata));
    let row = Value::Object(row_map);

    let rows: Vec<_> = std::iter::repeat(Cow::Owned(row)).take(num_rows).collect();

    let nested_insert_expr = Expr::Function(Function {
        name: "insert".to_string(),
        args: vec![
            Box::new(Expr::Literal(Literal {
                value: Value::Object(serde_json::Map::new()),
                expr_type: ScalarType::Object,
            })),
            Box::new(Expr::Literal(Literal {
                value: Value::String("model".to_string()),
                expr_type: ScalarType::String,
            })),
            Box::new(Expr::Function(Function {
                name: "insert".to_string(),
                args: vec![
                    Box::new(Expr::Literal(Literal {
                        value: Value::Object(serde_json::Map::new()),
                        expr_type: ScalarType::Object,
                    })),
                    Box::new(Expr::Literal(Literal {
                        value: Value::String("type".to_string()),
                        expr_type: ScalarType::String,
                    })),
                    Box::new(Expr::Literal(Literal {
                        value: Value::String(large_model_str),
                        expr_type: ScalarType::String,
                    })),
                ],
            })),
        ],
    });

    let result =
        interpret_expr(&ctx, &nested_insert_expr, &rows).expect("Nested insert evaluation failed");
    assert_eq!(result.len(), num_rows);
}

criterion_group!(benches, criterion_benchmark);
criterion_main!(benches);
