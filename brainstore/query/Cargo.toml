[package]
name = "query"
version = "0.1.0"
edition = "2021"

[features]
default = []
expect-tests = ["dep:jaq-core", "dep:jaq-json", "dep:jaq-std"]

[dependencies]
query-macros = { path = "../query-macros" }
btql = { path = "../btql" }
util = { path = "../util" }
agent = { path = "../agent" }
tantivy = { path = "../tantivy" }
serde = { workspace = true }
serde_json = { workspace = true }
storage = { path = "../storage" }
thiserror = { workspace = true }
chrono = { workspace = true }
base64 = { workspace = true }
rayon = { workspace = true }
tempfile = { workspace = true }
log = { workspace = true }
env_logger = { workspace = true }
similar = { workspace = true }
similar-asserts = { workspace = true }
regex = { workspace = true }
regex-automata = { workspace = true }
time = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tracing = { path = "../tracing" }
async-stream = { workspace = true }
async-trait = { workspace = true }
clap = { workspace = true }
lazy_static = { workspace = true }
itertools = { workspace = true }
sha2 = { workspace = true }
rand = { workspace = true }
arc-swap = { workspace = true }
tantivy-fst = { workspace = true }
sketches-ddsketch = { workspace = true }
jaq-core = { workspace = true, optional = true }
jaq-json = { workspace = true, optional = true }
jaq-std = { workspace = true, optional = true }
futures = { workspace = true }
async_util = { path = "../async_util" }

hashbrown = { workspace = true }
gxhash = { workspace = true }

[dev-dependencies]
rand = { workspace = true }
rand_distr = { workspace = true }
# These have to be duplicated because they're used if we're running tests or have the expect tests feature.
jaq-core = { workspace = true }
jaq-json = { workspace = true }
jaq-std = { workspace = true }
criterion = { workspace = true }

[[bench]]
name = "interpret_expr"
harness = false
