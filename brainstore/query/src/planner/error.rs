use thiserror::Error;

#[derive(Error, Debug)]
pub enum PlannerError {
    #[error("Unsupported tantivy operation: {name}")]
    UnsupportedTantivyOp {
        name: String,
        op: Option<Box<btql::binder::Expr>>,
    },

    #[error("Unsupported operation: {name}")]
    Unsupported {
        name: String,
        op: Option<Box<btql::binder::Expr>>,
    },

    #[error("Invalid expression: {msg}")]
    InvalidOptimizedExpr { msg: String },

    #[error("Invalid schema: {msg}")]
    InvalidSchema { msg: String },

    #[error(transparent)]
    Tantivy(#[from] tantivy::TantivyError),

    #[error(transparent)]
    Typesystem(#[from] btql::typesystem::error::TypesystemError),

    #[error(transparent)]
    Anyhow(#[from] util::anyhow::Error),
}

pub type Result<T> = std::result::Result<T, PlannerError>;
