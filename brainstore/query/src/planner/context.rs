use storage::tantivy_index::make_tantivy_schema;

use super::error::{PlannerError, Result};

pub struct PlanContext {
    tantivy_schema: tantivy::schema::Schema,
    schema: util::schema::<PERSON>hem<PERSON>,
    tokenizer_manager: tantivy::tokenizer::TokenizerManager,
}

impl<'a> PlanContext {
    pub fn new(schema: util::schema::Schema) -> Result<Self> {
        let tantivy_schema = make_tantivy_schema(&schema)?;
        Ok(Self {
            schema,
            tantivy_schema: tantivy_schema.schema,
            tokenizer_manager: tantivy::tokenizer::TokenizerManager::default(),
        })
    }

    pub fn schema(&self) -> &util::schema::Schema {
        &self.schema
    }

    pub fn tantivy_schema(&self) -> Result<&tantivy::schema::Schema> {
        Ok(&self.tantivy_schema)
    }

    pub fn get_tantivy_field(&self, field: &str) -> Result<tantivy::schema::Field> {
        let tantivy_schema = self.tantivy_schema();
        tantivy_schema?
            .get_field(field)
            .map_err(|_| PlannerError::InvalidSchema {
                msg: format!("tantivy field not found: {}", field),
            })
    }

    pub fn tokenizer_manager(&self) -> &tantivy::tokenizer::TokenizerManager {
        &self.tokenizer_manager
    }

    pub fn get_tokenizer(
        &self,
        field: tantivy::schema::Field,
    ) -> Result<Option<tantivy::tokenizer::TextAnalyzer>> {
        let tantivy_schema = self.tantivy_schema()?;
        let storage_field_entry = tantivy_schema.get_field_entry(field);
        let indexing_options = match storage_field_entry.field_type() {
            tantivy::schema::FieldType::Str(text_options) => text_options.get_indexing_options(),
            tantivy::schema::FieldType::JsonObject(text_options) => {
                text_options.get_text_indexing_options()
            }
            _ => None,
        };
        let tokenizer = match indexing_options {
            Some(opts) => self.tokenizer_manager().get(opts.tokenizer()),
            None => None,
        };
        Ok(tokenizer)
    }
}
