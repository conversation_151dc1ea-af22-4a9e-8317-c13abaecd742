use crate::optimizer::ast::OptimizedPlan;

use super::ast::*;
use super::context::PlanContext;
use super::error::Result;
use super::groupby::plan_groupby_query;
use super::tantivy::{
    plan_tantivy_aggregate_query, plan_tantivy_expand_traces_query,
    plan_tantivy_schema_inference_query, plan_tantivy_search_query,
};

pub fn plan(ctx: &PlanContext, query: &OptimizedPlan) -> Result<Box<PlannedQuery>> {
    Ok(Box::new(match query {
        OptimizedPlan::Noop(_) => PlannedQuery::Noop(NoopQuery {}),
        OptimizedPlan::Filter(local_plan) => PlannedQuery::Filter(FilterQuery {
            from: plan(ctx, &local_plan.from)?,
            filter: local_plan.filter.clone(),
        }),
        OptimizedPlan::GroupBy(groupby_plan) => {
            PlannedQuery::GroupBy(plan_groupby_query(ctx, groupby_plan)?)
        }
        OptimizedPlan::Project(limiter_plan) => PlannedQuery::Project(ProjectQuery {
            from: plan(ctx, &limiter_plan.from)?,
            limit: limiter_plan.limit,
            sort: limiter_plan.sort.clone(),
            projection: limiter_plan.projection.clone(),
            cursor_field: limiter_plan.cursor_field,
            is_top_level_limiter: limiter_plan.is_top_level_limiter,
        }),
        OptimizedPlan::Unpivot(unpivot_plan) => PlannedQuery::Unpivot(UnpivotQuery {
            from: plan(ctx, &unpivot_plan.from)?,
            unpivot: unpivot_plan.unpivot.clone(),
        }),
        OptimizedPlan::TantivySearch(tantivy_plan) => {
            PlannedQuery::TantivySearch(plan_tantivy_search_query(ctx, tantivy_plan)?)
        }
        OptimizedPlan::TantivySchemaInference(tantivy_plan) => {
            PlannedQuery::TantivySchemaInference(plan_tantivy_schema_inference_query(
                ctx,
                tantivy_plan,
            )?)
        }
        OptimizedPlan::TantivyAggregate(tantivy_plan) => {
            PlannedQuery::TantivyAggregate(plan_tantivy_aggregate_query(ctx, tantivy_plan)?)
        }
        OptimizedPlan::TantivyExpandTraces(tantivy_plan) => {
            PlannedQuery::TantivyExpandTraces(plan_tantivy_expand_traces_query(ctx, tantivy_plan)?)
        }
    }))
}
