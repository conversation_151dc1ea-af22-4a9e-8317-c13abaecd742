use super::util::is_constant_foldable;
use btql::binder::ast::{ArithmeticOp, Expr, Field, Function, Literal, ScalarType};
use serde_json::{json, Value};
use util::json::PathPiece;

fn make_literal(value: Value, expr_type: ScalarType) -> Expr {
    Expr::Literal(Literal { value, expr_type })
}

fn make_field(name: &str, field_type: &str) -> Expr {
    Expr::Field(Field::new(
        vec![PathPiece::Key(name.to_string())],
        json!({
            "type": field_type
        }),
        None,
    ))
}

#[test]
fn test_is_constant_foldable() {
    let test_cases = vec![
        (
            make_literal(json!(42), ScalarType::Integer),
            true,
            "literal",
        ),
        (make_field("column_name", "integer"), false, "field"),
        (
            Expr::Arithmetic {
                op: ArithmeticOp::Add,
                left: Box::new(make_literal(json!(5), ScalarType::Integer)),
                right: Box::new(make_literal(json!(3), ScalarType::Integer)),
            },
            true,
            "arithmetic op with literals",
        ),
        (
            Expr::Arithmetic {
                op: ArithmeticOp::Add,
                left: Box::new(make_literal(json!(5), ScalarType::Integer)),
                right: Box::new(make_field("column_name", "integer")),
            },
            false,
            "arithmetic op with field",
        ),
        (
            Expr::Function(Function {
                name: "YEAR".to_string(),
                args: vec![Box::new(make_literal(
                    json!("2024-01-01"),
                    ScalarType::String,
                ))],
            }),
            true,
            "YEAR function with literal",
        ),
        (
            Expr::Function(Function {
                name: "MONTH".to_string(),
                args: vec![Box::new(make_field("date_column", "string"))],
            }),
            false,
            "MONTH function with field",
        ),
    ];

    for (expr, expected, message) in test_cases {
        assert_eq!(is_constant_foldable(&expr), expected, "Failed: {}", message,);
    }
}
