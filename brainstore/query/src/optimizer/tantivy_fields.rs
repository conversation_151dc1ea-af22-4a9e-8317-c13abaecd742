use std::collections::HashMap;

use btql::{
    binder::{
        ast::{Function, Projection, SortExpr, SortItem},
        types::weakest_scalar_type,
        Expr,
    },
    schema::ScalarType,
};
use util::json::PathPiece;

#[derive(<PERSON><PERSON>, Debug, PartialEq)]
pub struct RequiredTantivyField {
    pub field_type: ScalarType,
    pub only_exists: bool,
}

impl RequiredTantivyField {
    pub fn new(field_type: ScalarType) -> Self {
        Self {
            field_type,
            only_exists: false,
        }
    }
}

// A "required" field is any field that needs to be projected from Tantivy to complete the query.
#[derive(<PERSON>lone, Debug)]
pub struct RequiredTantivyFieldMap(HashMap<Vec<PathPiece>, RequiredTantivyField>);

impl RequiredTantivyFieldMap {
    pub fn new() -> Self {
        Self(HashMap::new())
    }
    pub fn insert(&mut self, field: Vec<PathPiece>, required_field: RequiredTantivyField) {
        use std::collections::hash_map::Entry;
        match self.0.entry(field) {
            Entry::Vacant(v) => {
                v.insert(required_field);
            }
            Entry::Occupied(mut o) => {
                // If either field requires more than just an existence check, then we need to project
                // the whole field.
                let only_exists = o.get().only_exists;
                o.get_mut().only_exists = only_exists && required_field.only_exists;
            }
        }
    }
    pub fn extend(&mut self, other: RequiredTantivyFieldMap) {
        for (field, required_field) in other.0 {
            self.insert(field, required_field);
        }
    }

    pub fn len(&self) -> usize {
        self.0.len()
    }

    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }

    pub fn keys(&self) -> impl Iterator<Item = &Vec<PathPiece>> {
        self.0.keys()
    }

    pub fn values(&self) -> impl Iterator<Item = &RequiredTantivyField> {
        self.0.values()
    }
}

impl IntoIterator for RequiredTantivyFieldMap {
    type Item = (Vec<PathPiece>, RequiredTantivyField);
    type IntoIter = std::collections::hash_map::IntoIter<Vec<PathPiece>, RequiredTantivyField>;

    fn into_iter(self) -> Self::IntoIter {
        self.0.into_iter()
    }
}

impl<'a> IntoIterator for &'a RequiredTantivyFieldMap {
    type Item = (&'a Vec<PathPiece>, &'a RequiredTantivyField);
    type IntoIter = std::collections::hash_map::Iter<'a, Vec<PathPiece>, RequiredTantivyField>;

    fn into_iter(self) -> Self::IntoIter {
        self.0.iter()
    }
}

pub fn find_sort_item_tantivy_fields(
    sort_item: &SortItem,
    projection: &Projection,
) -> super::error::Result<RequiredTantivyFieldMap> {
    Ok(match &sort_item.expr {
        SortExpr::Alias(alias) => {
            let projection_expr = match projection.find_alias(alias) {
                Some(expr) => expr,
                None => {
                    return Err(super::error::OptimizerError::Unsupported {
                        name: format!("Sort alias '{alias}' not found in projection"),
                        op: None,
                    });
                }
            };
            find_required_tantivy_fields(&projection_expr.expr)
        }
        SortExpr::Expr(expr) => find_required_tantivy_fields(expr),
    })
}

pub fn find_required_tantivy_fields(expr: &Expr) -> RequiredTantivyFieldMap {
    let mut fields = RequiredTantivyFieldMap::new();
    match expr {
        Expr::Field(field) => {
            if let None = field.source {
                fields.insert(
                    field.name.clone(),
                    RequiredTantivyField::new(weakest_scalar_type(&field.expr_type)),
                );
            }
        }
        Expr::Function(Function { name, args }) => {
            // We could theoretically rewrite SUM(field IS NULL) to COUNT(field), but for now,
            // let's just write COUNT(field) in the app.
            let is_count = name == "count";
            for arg in args {
                match arg.as_ref() {
                    Expr::Field(f) if is_count && f.source.is_none() => {
                        fields.insert(
                            f.name.clone(),
                            RequiredTantivyField {
                                field_type: weakest_scalar_type(&f.expr_type),
                                only_exists: true,
                            },
                        );
                    }
                    _ => {
                        fields.extend(find_required_tantivy_fields(arg));
                    }
                }
            }
        }
        Expr::Literal(_) => {}
        Expr::Interval { .. } => {}
        Expr::Comparison { left, right, .. } => {
            fields.extend(find_required_tantivy_fields(left));
            fields.extend(find_required_tantivy_fields(right));
        }
        Expr::Includes { haystack, needle } => {
            fields.extend(find_required_tantivy_fields(haystack));
            fields.extend(find_required_tantivy_fields(needle));
        }
        Expr::Boolean { children, .. } => {
            for child in children {
                fields.extend(find_required_tantivy_fields(child));
            }
        }
        Expr::Ternary { conds, else_expr } => {
            for cond in conds {
                fields.extend(find_required_tantivy_fields(&cond.cond));
                fields.extend(find_required_tantivy_fields(&cond.then));
            }
            fields.extend(find_required_tantivy_fields(else_expr));
        }
        Expr::Arithmetic { left, right, .. } => {
            fields.extend(find_required_tantivy_fields(left));
            fields.extend(find_required_tantivy_fields(right));
        }
        Expr::Unary { expr, .. } => {
            fields.extend(find_required_tantivy_fields(expr));
        }
    };
    fields
}
