use std::borrow::Cow;

use btql::binder::{
    ast::{Function, Literal},
    Expr,
};

use btql::typesystem;

use super::error::Result;

pub fn optimize_expr<'a>(expr: Cow<'a, Expr>) -> Result<Cow<'a, Expr>> {
    match expr.as_ref() {
        Expr::Literal(literal) => {
            let (normalized, _) =
                typesystem::coerce::normalize_to_scalar_type(Cow::Borrowed(&literal.value))?;
            Ok(Cow::Owned(Expr::Literal(Literal {
                value: normalized.into_owned(),
                expr_type: literal.expr_type,
            })))
        }
        Expr::Interval { .. } | Expr::Field(_) => Ok(expr),
        Expr::Function(Function { name, args }) => {
            let optimized_args = args
                .iter()
                .map(|arg| optimize_expr(Cow::Borrowed(arg)))
                .collect::<Result<Vec<_>>>()?;
            Ok(Cow::Owned(Expr::Function(Function {
                name: name.clone(),
                args: optimized_args
                    .into_iter()
                    .map(|arg| Box::new(arg.into_owned()))
                    .collect(),
            })))
        }
        Expr::Comparison { op, left, right } => {
            let optimized_left = optimize_expr(Cow::Borrowed(left))?;
            let optimized_right = optimize_expr(Cow::Borrowed(right))?;
            Ok(Cow::Owned(Expr::Comparison {
                op: op.clone(),
                left: Box::new(optimized_left.into_owned()),
                right: Box::new(optimized_right.into_owned()),
            }))
        }
        Expr::Includes { haystack, needle } => {
            let optimized_haystack = optimize_expr(Cow::Borrowed(haystack))?;
            let optimized_needle = optimize_expr(Cow::Borrowed(needle))?;
            Ok(Cow::Owned(Expr::Includes {
                haystack: Box::new(optimized_haystack.into_owned()),
                needle: Box::new(optimized_needle.into_owned()),
            }))
        }
        Expr::Boolean { op, children } => {
            let optimized_children = children
                .iter()
                .map(|child| optimize_expr(Cow::Borrowed(child)))
                .collect::<Result<Vec<_>>>()?;
            Ok(Cow::Owned(Expr::Boolean {
                op: op.clone(),
                children: optimized_children
                    .into_iter()
                    .map(|child| Box::new(child.into_owned()))
                    .collect(),
            }))
        }
        Expr::Ternary { conds, else_expr } => {
            let optimized_else_expr = optimize_expr(Cow::Borrowed(else_expr))?;
            Ok(Cow::Owned(Expr::Ternary {
                conds: conds.clone(),
                else_expr: Box::new(optimized_else_expr.into_owned()),
            }))
        }
        Expr::Arithmetic { op, left, right } => {
            let optimized_left = optimize_expr(Cow::Borrowed(left))?;
            let optimized_right = optimize_expr(Cow::Borrowed(right))?;
            Ok(Cow::Owned(Expr::Arithmetic {
                op: op.clone(),
                left: Box::new(optimized_left.into_owned()),
                right: Box::new(optimized_right.into_owned()),
            }))
        }
        Expr::Unary { op, expr } => {
            let optimized_expr = optimize_expr(Cow::Borrowed(expr))?;
            Ok(Cow::Owned(Expr::Unary {
                op: op.clone(),
                expr: Box::new(optimized_expr.into_owned()),
            }))
        }
    }
}
