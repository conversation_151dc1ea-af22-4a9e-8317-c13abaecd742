use super::ast::OptimizedPlan;
use super::error::Result;
use btql::binder;
use btql::interpreter::context::ExprContext;
use clap::Parser;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON>q, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct OptimizerOpts {
    #[arg(long, action = clap::ArgAction::Set, default_value_t = false, default_missing_value = "true", num_args = 0..=1)]
    #[serde(default)]
    pub no_pushdown: bool,
}

pub struct OptimizerContext<'a> {
    pub schema: &'a util::schema::Schema,
    pub tokenizer_manager: std::cell::RefCell<Option<tantivy::tokenizer::TokenizerManager>>,
    pub opts: OptimizerOpts,
}

impl<'a> OptimizerContext<'a> {
    pub fn new(schema: &'a util::schema::Schema, opts: OptimizerOpts) -> Self {
        OptimizerContext {
            schema,
            tokenizer_manager: std::cell::RefCell::new(None),
            opts,
        }
    }

    pub fn tokenizer_manager(&self) -> Result<tantivy::tokenizer::TokenizerManager> {
        let mut value = self.tokenizer_manager.borrow_mut();
        if let None = value.as_mut() {
            let tokenizer_manager = tantivy::tokenizer::TokenizerManager::default();
            *value = Some(tokenizer_manager);
        }
        Ok(value.as_ref().unwrap().clone())
    }
}

pub fn optimize(
    schema: &util::schema::Schema,
    query: &binder::ast::Query,
    opts: OptimizerOpts,
    expr_ctx: &ExprContext,
) -> Result<Box<OptimizedPlan>> {
    let mut ctx = OptimizerContext::new(schema, opts);
    super::tantivy::create_tantivy_pushdown_query(&mut ctx, query, expr_ctx)
}
