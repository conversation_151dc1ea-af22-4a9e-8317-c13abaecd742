use std::borrow::Cow;

use btql::{
    binder::ast::{Expr, Literal, TransformExpr},
    interpreter::{context::ExprContext, expr::interpret_expr},
};
use util::Value;

use crate::interpreter::error::Result;
use btql::typesystem::coerce::normalize_to_scalar_type;

pub fn is_agg_function(name: &str) -> bool {
    // TODO We should really import / mirror the full function manifest here.
    matches!(name, "count" | "sum" | "avg" | "min" | "max" | "percentile")
}

pub fn is_constant_foldable(expr: &Expr) -> bool {
    let mut has_fields_or_aggs = false;
    let mut check_field = |expr: &Expr| match expr {
        Expr::Field(_) => has_fields_or_aggs = true,
        Expr::Function(f) if is_agg_function(&f.name) => has_fields_or_aggs = true,
        _ => {}
    };
    expr.traverse(&mut check_field);
    !has_fields_or_aggs
}

fn evaluate_constant_expr(ctx: &ExprContext, expr: &Expr) -> Result<Expr> {
    if matches!(expr, Expr::Literal(_) | Expr::Interval { .. }) {
        return Ok(expr.clone());
    }

    let result = interpret_expr(&ctx, expr, &[Cow::Owned(Value::Null)])?;
    let value = result.into_iter().next().unwrap();
    let (normalized_value, expr_type) = normalize_to_scalar_type(value.clone())?;

    Ok(Expr::Literal(Literal {
        value: normalized_value.into_owned(),
        expr_type,
    }))
}

pub fn constant_fold_expr(ctx: &ExprContext, expr: &Expr) -> Result<Expr> {
    if is_constant_foldable(expr) {
        return evaluate_constant_expr(ctx, expr);
    }

    let mut found_error = None;
    let result = expr.clone().transform(&mut |e| {
        if found_error.is_some() || !is_constant_foldable(&e) {
            e
        } else {
            match evaluate_constant_expr(ctx, &e) {
                Ok(folded) => folded,
                Err(err) => {
                    found_error = Some(err);
                    e
                }
            }
        }
    });

    if let Some(err) = found_error {
        Err(err)
    } else {
        Ok(result)
    }
}
