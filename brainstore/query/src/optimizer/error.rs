use thiserror::Error;

#[derive(Error, Debug)]
pub enum OptimizerError {
    #[error("Unsupported operation: {name}")]
    Unsupported {
        name: String,
        op: Option<Box<btql::binder::ast::Expr>>,
    },

    #[error("Type error: {msg}")]
    TypeError {
        msg: String,
        op: Option<Box<btql::binder::Expr>>,
    },

    #[error("Invalid bind expression: {msg}")]
    InvalidBindExpr {
        msg: String,
        op: Option<Box<btql::binder::Expr>>,
    },

    #[error("Invalid schema: {msg}")]
    InvalidSchema { msg: String },

    #[error(transparent)]
    Typesystem(#[from] btql::typesystem::error::TypesystemError),

    #[error(transparent)]
    Anyhow(#[from] util::anyhow::Error),
}

pub type Result<T, E = OptimizerError> = std::result::Result<T, E>;
