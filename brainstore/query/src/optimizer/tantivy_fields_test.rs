use super::tantivy_fields::{
    find_required_tantivy_fields, RequiredTantivyField, RequiredTantivyFieldMap,
};
use btql::binder::ast::{
    ArithmeticOp, BooleanOp, ComparisonOp, Expr, Field, Function, Literal, TernaryCond, UnaryOp,
};
use btql::schema::ScalarType;
use serde_json::{json, Value};
use util::json::PathPiece;

// Helper functions for creating test expressions
fn make_literal(value: Value, expr_type: ScalarType) -> Expr {
    Expr::Literal(Literal { value, expr_type })
}

fn make_field(name: &str, field_type: ScalarType) -> Expr {
    Expr::Field(Field::new(
        vec![PathPiece::Key(name.to_string())],
        json!({
            "type": field_type
        }),
        None,
    ))
}

fn make_path(path: Vec<&str>) -> Vec<PathPiece> {
    path.into_iter()
        .map(|s| PathPiece::Key(s.to_string()))
        .collect()
}

fn make_function(name: &str, args: Vec<Expr>) -> Expr {
    Expr::Function(Function {
        name: name.to_string(),
        args: args.into_iter().map(Box::new).collect(),
    })
}

fn make_comparison(op: ComparisonOp, left: Expr, right: Expr) -> Expr {
    Expr::Comparison {
        op,
        left: Box::new(left),
        right: Box::new(right),
    }
}

fn make_arithmetic(op: ArithmeticOp, left: Expr, right: Expr) -> Expr {
    Expr::Arithmetic {
        op,
        left: Box::new(left),
        right: Box::new(right),
    }
}

fn make_boolean(op: BooleanOp, children: Vec<Expr>) -> Expr {
    Expr::Boolean {
        op,
        children: children.into_iter().map(Box::new).collect(),
    }
}

fn make_unary(op: UnaryOp, expr: Expr) -> Expr {
    Expr::Unary {
        op,
        expr: Box::new(expr),
    }
}

fn make_ternary(conditions: Vec<(Expr, Expr)>, else_expr: Expr) -> Expr {
    Expr::Ternary {
        conds: conditions
            .into_iter()
            .map(|(cond, then)| TernaryCond {
                cond: Box::new(cond),
                then: Box::new(then),
            })
            .collect(),
        else_expr: Box::new(else_expr),
    }
}

#[test]
fn test_required_tantivy_field_map_insert_new_field() {
    let mut map = RequiredTantivyFieldMap::new();
    let field_path = make_path(vec!["test_field"]);
    let required_field = RequiredTantivyField::new(ScalarType::String);

    map.insert(field_path.clone(), required_field.clone());

    assert_eq!(map.len(), 1);
    assert!(!map.is_empty());
    assert_eq!(map.keys().next().unwrap(), &field_path);
    assert_eq!(map.values().next().unwrap(), &required_field);
}

#[test]
fn test_required_tantivy_field_map_insert_existing_field() {
    let mut map = RequiredTantivyFieldMap::new();
    let field_path = make_path(vec!["test_field"]);

    // Insert first field with only_exists = false
    let first_field = RequiredTantivyField {
        field_type: ScalarType::String,
        only_exists: false,
    };
    map.insert(field_path.clone(), first_field);

    // Insert second field with only_exists = true
    let second_field = RequiredTantivyField {
        field_type: ScalarType::String,
        only_exists: true,
    };
    map.insert(field_path.clone(), second_field);

    // Should still have only 1 field, but only_exists should be false (AND of false and true)
    assert_eq!(map.len(), 1);
    let result_field = map.values().next().unwrap();
    assert_eq!(result_field.field_type, ScalarType::String);
    assert_eq!(result_field.only_exists, false);
}

#[test]
fn test_required_tantivy_field_map_insert_both_only_exists_true() {
    let mut map = RequiredTantivyFieldMap::new();
    let field_path = make_path(vec!["test_field"]);

    // Insert first field with only_exists = true
    let first_field = RequiredTantivyField {
        field_type: ScalarType::String,
        only_exists: true,
    };
    map.insert(field_path.clone(), first_field);

    // Insert second field with only_exists = true
    let second_field = RequiredTantivyField {
        field_type: ScalarType::String,
        only_exists: true,
    };
    map.insert(field_path.clone(), second_field);

    // Should still have only 1 field, only_exists should remain true (AND of true and true)
    assert_eq!(map.len(), 1);
    let result_field = map.values().next().unwrap();
    assert_eq!(result_field.field_type, ScalarType::String);
    assert_eq!(result_field.only_exists, true);
}

#[test]
fn test_required_tantivy_field_map_extend() {
    let mut map1 = RequiredTantivyFieldMap::new();
    let mut map2 = RequiredTantivyFieldMap::new();

    let field1_path = make_path(vec!["field1"]);
    let field2_path = make_path(vec!["field2"]);
    let field3_path = make_path(vec!["field1"]); // Same as field1

    map1.insert(
        field1_path.clone(),
        RequiredTantivyField::new(ScalarType::String),
    );
    map2.insert(
        field2_path.clone(),
        RequiredTantivyField::new(ScalarType::Integer),
    );
    map2.insert(
        field3_path.clone(),
        RequiredTantivyField {
            field_type: ScalarType::String,
            only_exists: true,
        },
    );

    map1.extend(map2);

    assert_eq!(map1.len(), 2);

    // Check that field1 was merged correctly (only_exists should be false)
    for (path, field) in &map1 {
        if path == &field1_path {
            assert_eq!(field.only_exists, false);
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_simple_field() {
    let expr = make_field("test_field", ScalarType::String);
    let result = find_required_tantivy_fields(&expr);

    assert_eq!(result.len(), 1);
    let field_path = make_path(vec!["test_field"]);
    assert!(result.keys().any(|k| k == &field_path));

    for (path, field) in result {
        if path == field_path {
            assert_eq!(field.field_type, ScalarType::String);
            assert_eq!(field.only_exists, false);
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_count_direct_field() {
    let field_expr = make_field("test_field", ScalarType::String);
    let count_expr = make_function("count", vec![field_expr]);
    let result = find_required_tantivy_fields(&count_expr);

    assert_eq!(result.len(), 1);
    let field_path = make_path(vec!["test_field"]);

    for (path, field) in result {
        if path == field_path {
            assert_eq!(field.field_type, ScalarType::String);
            assert_eq!(field.only_exists, true); // COUNT of direct field should set only_exists = true
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_count_coalesce() {
    // Test COUNT(COALESCE(foo, 'hello')) - the foo field should NOT have only_exists = true
    let field_expr = make_field("foo", ScalarType::String);
    let literal_expr = make_literal(json!("hello"), ScalarType::String);
    let coalesce_expr = make_function("coalesce", vec![field_expr, literal_expr]);
    let count_expr = make_function("count", vec![coalesce_expr]);

    let result = find_required_tantivy_fields(&count_expr);

    assert_eq!(result.len(), 1);
    let field_path = make_path(vec!["foo"]);

    for (path, field) in result {
        if path == field_path {
            assert_eq!(field.field_type, ScalarType::String);
            assert_eq!(field.only_exists, false); // Should NOT be only_exists because it's inside COALESCE
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_count_arithmetic() {
    // Test COUNT(field + 5) - the field should NOT have only_exists = true
    let field_expr = make_field("num_field", ScalarType::Integer);
    let literal_expr = make_literal(json!(5), ScalarType::Integer);
    let arithmetic_expr = make_arithmetic(ArithmeticOp::Add, field_expr, literal_expr);
    let count_expr = make_function("count", vec![arithmetic_expr]);

    let result = find_required_tantivy_fields(&count_expr);

    assert_eq!(result.len(), 1);
    let field_path = make_path(vec!["num_field"]);

    for (path, field) in result {
        if path == field_path {
            assert_eq!(field.field_type, ScalarType::Integer);
            assert_eq!(field.only_exists, false); // Should NOT be only_exists because it's inside arithmetic
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_non_count_function() {
    // Test SUM(field) - should not set only_exists = true
    let field_expr = make_field("test_field", ScalarType::Integer);
    let sum_expr = make_function("sum", vec![field_expr]);
    let result = find_required_tantivy_fields(&sum_expr);

    assert_eq!(result.len(), 1);
    let field_path = make_path(vec!["test_field"]);

    for (path, field) in result {
        if path == field_path {
            assert_eq!(field.field_type, ScalarType::Integer);
            assert_eq!(field.only_exists, false); // SUM is not COUNT, so only_exists should be false
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_comparison() {
    let left_field = make_field("left_field", ScalarType::String);
    let right_field = make_field("right_field", ScalarType::String);
    let comparison = make_comparison(ComparisonOp::Eq, left_field, right_field);

    let result = find_required_tantivy_fields(&comparison);

    assert_eq!(result.len(), 2);
    let left_path = make_path(vec!["left_field"]);
    let right_path = make_path(vec!["right_field"]);

    let paths: Vec<_> = result.keys().collect();
    assert!(paths.contains(&&left_path));
    assert!(paths.contains(&&right_path));

    for (_, field) in result {
        assert_eq!(field.field_type, ScalarType::String);
        assert_eq!(field.only_exists, false);
    }
}

#[test]
fn test_find_required_tantivy_fields_boolean_and() {
    let field1 = make_field("field1", ScalarType::Boolean);
    let field2 = make_field("field2", ScalarType::Boolean);
    let boolean_expr = make_boolean(BooleanOp::And, vec![field1, field2]);

    let result = find_required_tantivy_fields(&boolean_expr);

    assert_eq!(result.len(), 2);
    let field1_path = make_path(vec!["field1"]);
    let field2_path = make_path(vec!["field2"]);

    let paths: Vec<_> = result.keys().collect();
    assert!(paths.contains(&&field1_path));
    assert!(paths.contains(&&field2_path));
}

#[test]
fn test_find_required_tantivy_fields_ternary() {
    let condition_field = make_field("condition_field", ScalarType::Boolean);
    let then_field = make_field("then_field", ScalarType::String);
    let else_field = make_field("else_field", ScalarType::String);

    let ternary = make_ternary(vec![(condition_field, then_field)], else_field);

    let result = find_required_tantivy_fields(&ternary);

    assert_eq!(result.len(), 3);
    let condition_path = make_path(vec!["condition_field"]);
    let then_path = make_path(vec!["then_field"]);
    let else_path = make_path(vec!["else_field"]);

    let paths: Vec<_> = result.keys().collect();
    assert!(paths.contains(&&condition_path));
    assert!(paths.contains(&&then_path));
    assert!(paths.contains(&&else_path));
}

#[test]
fn test_find_required_tantivy_fields_unary() {
    let field_expr = make_field("test_field", ScalarType::String);
    let unary_expr = make_unary(UnaryOp::IsNull, field_expr);

    let result = find_required_tantivy_fields(&unary_expr);

    assert_eq!(result.len(), 1);
    let field_path = make_path(vec!["test_field"]);

    for (path, field) in result {
        if path == field_path {
            assert_eq!(field.field_type, ScalarType::String);
            assert_eq!(field.only_exists, false);
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_includes() {
    let haystack_field = make_field("haystack", ScalarType::Array);
    let needle_field = make_field("needle", ScalarType::String);

    let includes_expr = Expr::Includes {
        haystack: Box::new(haystack_field),
        needle: Box::new(needle_field),
    };

    let result = find_required_tantivy_fields(&includes_expr);

    assert_eq!(result.len(), 2);
    let haystack_path = make_path(vec!["haystack"]);
    let needle_path = make_path(vec!["needle"]);

    let paths: Vec<_> = result.keys().collect();
    assert!(paths.contains(&&haystack_path));
    assert!(paths.contains(&&needle_path));
}

#[test]
fn test_find_required_tantivy_fields_literals_ignored() {
    let literal_expr = make_literal(json!("test"), ScalarType::String);
    let result = find_required_tantivy_fields(&literal_expr);

    assert_eq!(result.len(), 0);
    assert!(result.is_empty());
}

#[test]
fn test_find_required_tantivy_fields_interval_ignored() {
    let interval_expr = Expr::Interval {
        value: 1,
        unit: btql::parser::json_ast::IntervalUnit::Day,
    };
    let result = find_required_tantivy_fields(&interval_expr);

    assert_eq!(result.len(), 0);
    assert!(result.is_empty());
}

#[test]
fn test_find_required_tantivy_fields_complex_nested() {
    // Test a complex nested expression: COUNT(field1) + SUM(COALESCE(field2, field3))
    let field1 = make_field("field1", ScalarType::Integer);
    let field2 = make_field("field2", ScalarType::Integer);
    let field3 = make_field("field3", ScalarType::Integer);

    let count_expr = make_function("count", vec![field1]);
    let coalesce_expr = make_function("coalesce", vec![field2, field3]);
    let sum_expr = make_function("sum", vec![coalesce_expr]);
    let arithmetic_expr = make_arithmetic(ArithmeticOp::Add, count_expr, sum_expr);

    let result = find_required_tantivy_fields(&arithmetic_expr);

    assert_eq!(result.len(), 3);
    let field1_path = make_path(vec!["field1"]);
    let field2_path = make_path(vec!["field2"]);
    let field3_path = make_path(vec!["field3"]);

    for (path, field) in result {
        if path == field1_path {
            assert_eq!(field.only_exists, true); // COUNT of direct field
        } else if path == field2_path || path == field3_path {
            assert_eq!(field.only_exists, false); // Inside COALESCE and SUM
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_multiple_count_direct() {
    // Test COUNT(field1) AND COUNT(field2) - both should have only_exists = true
    let field1 = make_field("field1", ScalarType::Integer);
    let field2 = make_field("field2", ScalarType::Integer);

    let count1 = make_function("count", vec![field1]);
    let count2 = make_function("count", vec![field2]);
    let boolean_expr = make_boolean(BooleanOp::And, vec![count1, count2]);

    let result = find_required_tantivy_fields(&boolean_expr);

    assert_eq!(result.len(), 2);
    let field1_path = make_path(vec!["field1"]);
    let field2_path = make_path(vec!["field2"]);

    for (path, field) in result {
        if path == field1_path || path == field2_path {
            assert_eq!(field.only_exists, true); // Both are direct fields in COUNT
        }
    }
}

#[test]
fn test_find_required_tantivy_fields_mixed_count_usage() {
    // Test COUNT(field1) AND field1 > 5 - field1 should have only_exists = false
    // because it's used both in COUNT (only_exists=true) and comparison (only_exists=false)
    let field1_count = make_field("field1", ScalarType::Integer);
    let field1_compare = make_field("field1", ScalarType::Integer);
    let literal_5 = make_literal(json!(5), ScalarType::Integer);

    let count_expr = make_function("count", vec![field1_count]);
    let comparison_expr = make_comparison(ComparisonOp::Gt, field1_compare, literal_5);
    let boolean_expr = make_boolean(BooleanOp::And, vec![count_expr, comparison_expr]);

    let result = find_required_tantivy_fields(&boolean_expr);

    assert_eq!(result.len(), 1);
    let field1_path = make_path(vec!["field1"]);

    for (path, field) in result {
        if path == field1_path {
            assert_eq!(field.only_exists, false); // Should be false due to merging logic
        }
    }
}
