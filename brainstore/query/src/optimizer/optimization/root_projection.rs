use btql::binder::ast::{<PERSON><PERSON>, Field, UnpivotExpr};
use btql::binder::Expr;
use util::json::PathPiece;

use crate::interpreter::tantivy::expand::IS_ROOT_FIELD;
use crate::optimizer::ast::TantivyProjectedField;
use crate::optimizer::error::Result;
use crate::optimizer::optimizer::OptimizerContext;
use crate::optimizer::tantivy::make_tantivy_projection;
use crate::optimizer::tantivy_fields::{find_required_tantivy_fields, RequiredTantivyFieldMap};

// This function attempts to optimize queries like
//  select: is_root ? input : null, ...
// by not even fetching the field from non-root records. For each projection item,
// it checks if it matches this ternary pattern, and if so, splits up the required
// projection fields into root and span projections.
//
// NOTE that this optimization is really only useful if the span projection can be
// served from columnar storage, which ends up being much faster (esp. for small fields)
// than fetching the entire document (with a potentially large input/output field).
pub fn split_root_projection(
    ctx: &OptimizerContext,
    projection: Vec<TantivyProjectedField>,
    select: &[Alias],
    unpivot: &[UnpivotExpr],
) -> Result<(Vec<TantivyProjectedField>, Vec<TantivyProjectedField>)> {
    if unpivot.len() > 0 || select.len() == 0 {
        return Ok((projection.clone(), projection));
    }

    // If each of the select aliases is a single ternary expression with an `is_root`
    // condition, then we can split the projection into root and span projections.
    let mut root_projection = RequiredTantivyFieldMap::new();
    let mut span_projection = RequiredTantivyFieldMap::new();
    for alias in select.iter() {
        if let Some((cond, then, else_expr)) = is_ternary_root_projection(alias) {
            // Both need the condition field
            root_projection.extend(find_required_tantivy_fields(cond));
            span_projection.extend(find_required_tantivy_fields(cond));

            root_projection.extend(find_required_tantivy_fields(then));
            span_projection.extend(find_required_tantivy_fields(else_expr));
        } else {
            let fields = find_required_tantivy_fields(&alias.expr);
            root_projection.extend(fields.clone());
            span_projection.extend(fields);
        }
    }

    Ok((
        make_tantivy_projection(ctx, &root_projection)?,
        make_tantivy_projection(ctx, &span_projection)?,
    ))
}

fn is_ternary_root_projection(alias: &Alias) -> Option<(&Expr, &Expr, &Expr)> {
    match alias.expr.as_ref() {
        Expr::Ternary { conds, else_expr } => {
            if conds.len() != 1 {
                return None;
            }

            let name = if let Expr::Field(Field { name, .. }) = conds[0].cond.as_ref() {
                name
            } else {
                return None;
            };

            if name.len() != 1 || name[0] != PathPiece::Key(IS_ROOT_FIELD.to_string()) {
                return None;
            }

            Some((&conds[0].cond, &conds[0].then, else_expr))
        }
        _ => None,
    }
}
