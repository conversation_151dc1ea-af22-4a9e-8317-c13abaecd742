// Tests for score predicate pushdown optimization
//
// Key concept from analysis:
// For range predicates (>, >=, <, <=) and extrema equality (=0, =1) on scores/metrics fields,
// we can push the filter down to span level first as a necessary condition, then re-evaluate
// after aggregation. This works because these are monotone predicates - the average can only
// satisfy the predicate if at least one span satisfies it.
//
// Example: If we want avg(scores.foo) > 0.5, we first filter spans where scores.foo > 0.5,
// then compute the average only for traces that have at least one matching span, and finally
// re-apply the filter on the computed average.
//
// This optimization dramatically reduces the aggregation workload by eliminating traces early
// that cannot possibly satisfy the predicate.

use super::summary_filters::split_post_aggregation_filters;
use crate::optimizer::optimizer::{OptimizerContext, OptimizerOpts};
use btql::binder::ast::{BooleanOp, ComparisonOp, Expr, Field, Literal};
use btql::binder::types::scalar_type_to_logical_schema;
use btql::schema::ScalarType;
use serde_json::json;
use std::collections::HashSet;
use util::json::PathPiece;
use util::schema::Schema;

fn make_field(path: Vec<&str>) -> Box<Expr> {
    Box::new(Expr::Field(Field::new(
        path.into_iter()
            .map(|s| PathPiece::Key(s.to_string()))
            .collect(),
        scalar_type_to_logical_schema(ScalarType::Number),
        None,
    )))
}

fn make_literal(value: f64) -> Box<Expr> {
    Box::new(Expr::Literal(Literal {
        value: json!(value),
        expr_type: ScalarType::Number,
    }))
}

fn make_string_literal(value: &str) -> Box<Expr> {
    Box::new(Expr::Literal(Literal {
        value: json!(value),
        expr_type: ScalarType::String,
    }))
}

fn make_test_context() -> OptimizerContext<'static> {
    // Create a minimal schema for testing
    let schema = Box::leak(Box::new(Schema::default()));
    OptimizerContext::new(schema, OptimizerOpts::default())
}

fn make_comparison(op: ComparisonOp, left: Box<Expr>, right: Box<Expr>) -> Expr {
    Expr::Comparison { op, left, right }
}

fn make_and(children: Vec<Box<Expr>>) -> Expr {
    Expr::Boolean {
        op: BooleanOp::And,
        children,
    }
}

#[test]
fn test_score_predicate_pushdown_greater_than() {
    // Test case: scores.foo > 0.5
    // This should be pushed down to span level first, then re-evaluated after aggregation
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.5),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    // For scores fields with range predicates, the filter should be in BOTH base and post-aggregation
    // This implements the two-pass strategy: filter at span level first, then re-evaluate after aggregation
    assert_eq!(base_filter, Some(filter.clone()));
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_score_predicate_pushdown_equals_zero() {
    // Test case: scores.foo = 0
    // This can be pushed down because 0 is an extrema value
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["scores", "foo"]),
        make_literal(0.0),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    // For scores fields with extrema values (= 0), filter should be in BOTH base and post-aggregation
    // This allows early filtering of traces that have at least one span = 0
    assert_eq!(base_filter, Some(filter.clone()));
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_score_predicate_pushdown_equals_one() {
    // Test case: scores.foo = 1
    // This can be pushed down because 1 is an extrema value
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["scores", "foo"]),
        make_literal(1.0),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    // For scores fields with extrema values (= 1), filter should be in BOTH base and post-aggregation
    // This allows early filtering of traces that have at least one span = 1
    assert_eq!(base_filter, Some(filter.clone()));
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_score_predicate_pushdown_less_than() {
    // Test case: scores.foo < 0.8
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Lt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.8),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    // Range predicates on scores are pushed down to both levels
    assert_eq!(base_filter, Some(filter.clone()));
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_metrics_field_post_aggregation() {
    // Test that metrics fields are also treated as post-aggregation fields
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["metrics", "bar"]),
        make_literal(100.0),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    assert!(base_filter.is_none());
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_non_aggregation_field_base_filter() {
    // Test that non-aggregation fields go to base filter only
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["status"]),
        make_string_literal("active"),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    assert_eq!(base_filter, Some(filter));
    assert!(post_agg_filter.is_none());
}

#[test]
fn test_mixed_filters_and_condition() {
    // Test: (scores.foo > 0.5 AND status = "active")
    // scores.foo should go to post-aggregation, status should go to base
    let ctx = make_test_context();
    let score_filter = Box::new(make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.5),
    ));
    let status_filter = Box::new(make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["status"]),
        make_string_literal("active"),
    ));

    let combined_filter = make_and(vec![score_filter.clone(), status_filter.clone()]);

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(combined_filter), None, &weighted_scores)
            .unwrap();

    // With the new optimization:
    // - Base filter has BOTH conditions (score predicate is pushed down)
    // - Post-aggregation filter only has the score condition (needs re-evaluation after aggregation)
    assert_eq!(
        base_filter,
        Some(make_and(vec![score_filter.clone(), status_filter.clone()]))
    );
    assert_eq!(post_agg_filter, Some(*score_filter));
}

#[test]
fn test_multiple_score_predicates() {
    // Test: (scores.foo > 0.5 AND scores.bar < 0.3)
    // Both should be in post-aggregation
    let ctx = make_test_context();
    let score_filter1 = Box::new(make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.5),
    ));
    let score_filter2 = Box::new(make_comparison(
        ComparisonOp::Lt,
        make_field(vec!["scores", "bar"]),
        make_literal(0.3),
    ));

    let combined_filter = make_and(vec![score_filter1.clone(), score_filter2.clone()]);

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(combined_filter.clone()), None, &weighted_scores)
            .unwrap();

    // Both score predicates are pushed down to base filter AND kept in post-aggregation
    assert_eq!(base_filter, Some(combined_filter.clone()));
    assert_eq!(post_agg_filter, Some(combined_filter));
}

#[test]
fn test_paginated_field_behavior() {
    // Test that paginated fields (_pagination_key, _xact_id, created) are handled specially
    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["_xact_id"]),
        make_literal(1000.0),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    // Paginated fields should appear in both base and post-aggregation
    assert_eq!(base_filter, Some(filter.clone()));
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_empty_filter() {
    let ctx = make_test_context();
    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, None, None, &weighted_scores).unwrap();

    assert!(base_filter.is_none());
    assert!(post_agg_filter.is_none());
}

#[test]
fn test_score_predicate_range_filters() {
    // Test all comparison operators for score predicates
    let ctx = make_test_context();
    let operators = vec![
        ComparisonOp::Gt,
        ComparisonOp::Ge,
        ComparisonOp::Lt,
        ComparisonOp::Le,
    ];

    for op in operators {
        let filter = make_comparison(op, make_field(vec!["scores", "test"]), make_literal(0.7));

        let weighted_scores = HashSet::new();
        let (base_filter, post_agg_filter) =
            split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores)
                .unwrap();

        // All range predicates on scores are pushed down to BOTH levels
        assert_eq!(base_filter, Some(filter.clone()));
        assert_eq!(post_agg_filter, Some(filter));
    }
}

#[test]
fn test_nested_boolean_expressions() {
    // Test: ((scores.foo > 0.5 OR scores.bar < 0.3) AND status = "active")
    let ctx = make_test_context();
    let score_filter1 = Box::new(make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.5),
    ));
    let score_filter2 = Box::new(make_comparison(
        ComparisonOp::Lt,
        make_field(vec!["scores", "bar"]),
        make_literal(0.3),
    ));
    let status_filter = Box::new(make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["status"]),
        make_string_literal("active"),
    ));

    let or_expr = Box::new(Expr::Boolean {
        op: BooleanOp::Or,
        children: vec![score_filter1, score_filter2],
    });

    let and_expr = make_and(vec![or_expr.clone(), status_filter.clone()]);

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(and_expr.clone()), None, &weighted_scores)
            .unwrap();

    // With the new optimization:
    // - Base filter can have the entire expression
    // - Post-aggregation filter only has the OR expression with score conditions
    assert_eq!(base_filter, Some(and_expr));
    assert_eq!(post_agg_filter, Some(*or_expr));
}

#[test]
fn test_boolean_or_no_pushdown() {
    // Test: (scores.foo > 0.5 OR metrics.duration > 100)
    let ctx = make_test_context();
    let filter1 = Box::new(make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.5),
    ));
    let filter2 = Box::new(make_comparison(
        ComparisonOp::Lt,
        make_field(vec!["metrics", "duration"]),
        make_literal(100.0),
    ));

    let or_expr = Box::new(Expr::Boolean {
        op: BooleanOp::Or,
        children: vec![filter1, filter2],
    });

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(*or_expr.clone()), None, &weighted_scores)
            .unwrap();

    // metrics.duration can't be pushed down, so the entire expression shouldn't be pushed down.
    assert_eq!(base_filter, None);
    assert_eq!(post_agg_filter, Some(*or_expr));
}

#[test]
fn test_chatgpt_example_score_pushdown() {
    // This test directly implements the example from ChatGPT analysis:
    // "lets say there are two spans, with foo 0.25 and 0.8. Then scores.foo > 0.5
    // will match the one with 0.8 but also match the average"
    //
    // The key insight is that for scores.foo > 0.5:
    // 1. At span level: only the span with 0.8 matches
    // 2. At trace level: avg(0.25, 0.8) = 0.525 also matches
    //
    // This demonstrates why we can use span-level filtering as a necessary condition:
    // if NO spans match scores.foo > 0.5, then the average cannot be > 0.5

    let ctx = make_test_context();
    let filter = make_comparison(
        ComparisonOp::Gt,
        make_field(vec!["scores", "foo"]),
        make_literal(0.5),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(filter.clone()), None, &weighted_scores).unwrap();

    // The new implementation correctly pushes the filter to BOTH levels:
    // 1. Base filter: applied at span level to find traces with any span > 0.5
    // 2. Post-aggregation filter: re-applied after averaging to get final result
    assert_eq!(base_filter, Some(filter.clone()));
    assert_eq!(post_agg_filter, Some(filter));
}

#[test]
fn test_extrema_values_optimization() {
    // Test the special optimization for extrema values (0 and 1)
    // For avg(scores.foo) = 0, ALL spans must be 0
    // For avg(scores.foo) = 1, ALL spans must be 1
    // So finding ANY span with the extrema value is a necessary condition

    let ctx = make_test_context();

    // Test = 0 case
    let zero_filter = make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["scores", "accuracy"]),
        make_literal(0.0),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(zero_filter.clone()), None, &weighted_scores)
            .unwrap();
    assert_eq!(base_filter, Some(zero_filter.clone()));
    assert_eq!(post_agg_filter, Some(zero_filter));

    // Test = 1 case
    let one_filter = make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["scores", "accuracy"]),
        make_literal(1.0),
    );

    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(one_filter.clone()), None, &weighted_scores)
            .unwrap();
    assert_eq!(base_filter, Some(one_filter.clone()));
    assert_eq!(post_agg_filter, Some(one_filter));

    // Test != 0 case
    let not_zero_filter = make_comparison(
        ComparisonOp::Ne,
        make_field(vec!["scores", "foo"]),
        make_literal(0.0),
    );

    let weighted_scores = HashSet::new();
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(not_zero_filter.clone()), None, &weighted_scores)
            .unwrap();

    assert_eq!(base_filter, Some(not_zero_filter.clone()));
    assert_eq!(post_agg_filter, Some(not_zero_filter));

    // Test != 1 case
    let not_one_filter = make_comparison(
        ComparisonOp::Ne,
        make_field(vec!["scores", "foo"]),
        make_literal(1.0),
    );

    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(not_one_filter.clone()), None, &weighted_scores)
            .unwrap();

    assert_eq!(base_filter, Some(not_one_filter.clone()));
    assert_eq!(post_agg_filter, Some(not_one_filter));
}

#[test]
fn test_non_extrema_values_pushdown() {
    // Test that non-extrema values do not get pushdown optimization

    // Test = 0.5 case
    let ctx = make_test_context();
    let weighted_scores = HashSet::new();

    let half_filter = make_comparison(
        ComparisonOp::Eq,
        make_field(vec!["scores", "accuracy"]),
        make_literal(0.5),
    );
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(half_filter.clone()), None, &weighted_scores)
            .unwrap();

    assert!(base_filter.is_none(), "0.5 should not get base pushdown");
    assert_eq!(post_agg_filter, Some(half_filter));

    // Test != 0.5 case
    let not_half_filter = make_comparison(
        ComparisonOp::Ne,
        make_field(vec!["scores", "accuracy"]),
        make_literal(0.5),
    );
    let (base_filter, post_agg_filter) =
        split_post_aggregation_filters(&ctx, Some(not_half_filter.clone()), None, &weighted_scores)
            .unwrap();
    assert!(base_filter.is_none(), "0.5 should not get base pushdown");
    assert_eq!(post_agg_filter, Some(not_half_filter));
}
