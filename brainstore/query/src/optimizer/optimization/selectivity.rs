use crate::optimizer::ast::{BooleanQueryOp, OptimizedTantivySearch};

pub fn is_selective(filter: &OptimizedTantivySearch) -> bool {
    match filter {
        OptimizedTantivySearch::AllQuery => false,
        OptimizedTantivySearch::EmptyQuery => true,
        OptimizedTantivySearch::BooleanQuery(ops) => {
            let mut ret = true;
            for (op, search) in ops {
                match (op, is_selective(search)) {
                    // <selective> OR <selective> => <selective>
                    // <selective> OR <not selective> => <not selective>
                    (BooleanQueryOp::Should, selective) => ret = ret && selective,
                    // <selective> AND <selective> => <selective>
                    // <selective> AND <not selective> => <selective>
                    (BooleanQueryOp::Must | BooleanQueryOp::MustNot, selective) => {
                        ret = ret || selective
                    }
                }
            }
            ret
        }
        OptimizedTantivySearch::RegexQuery { .. } => true,
        OptimizedTantivySearch::ExistsQuery { .. } => false,
        OptimizedTantivySearch::JSONExistsQuery { .. } => false,
        OptimizedTantivySearch::TermQuery { .. } => true,
        OptimizedTantivySearch::RangeQuery { .. } => false,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_selective() {
        use crate::optimizer::ast::BooleanQueryOp;

        // Basic query types
        assert!(!is_selective(&OptimizedTantivySearch::AllQuery));
        assert!(is_selective(&OptimizedTantivySearch::EmptyQuery));
        assert!(is_selective(&OptimizedTantivySearch::RegexQuery {
            field: create_tantivy_field("test"),
            json_path: vec![],
            pattern: ".*".to_string(),
            case_insensitive: true
        }));
        assert!(!is_selective(&OptimizedTantivySearch::ExistsQuery {
            field: create_tantivy_field("test")
        }));
        assert!(!is_selective(&OptimizedTantivySearch::JSONExistsQuery {
            field: create_tantivy_field("test"),
            json_path: vec![]
        }));
        assert!(is_selective(&OptimizedTantivySearch::TermQuery(
            create_term_query("test", "value")
        )));
        assert!(!is_selective(&OptimizedTantivySearch::RangeQuery {
            field: create_tantivy_field("test"),
            json_path: vec![],
            tantivy_type: tantivy::schema::Type::Str,
            lower: std::ops::Bound::Unbounded,
            upper: std::ops::Bound::Unbounded,
            columnar: false
        }));

        // Boolean combinations - Should (OR) operations
        let term1 = OptimizedTantivySearch::TermQuery(create_term_query("field1", "value1"));
        let term2 = OptimizedTantivySearch::TermQuery(create_term_query("field2", "value2"));
        let exists = OptimizedTantivySearch::ExistsQuery {
            field: create_tantivy_field("field3"),
        };

        // <selective> OR <selective> => <selective>
        let should_selective_and_selective = OptimizedTantivySearch::BooleanQuery(vec![
            (BooleanQueryOp::Should, term1.clone()),
            (BooleanQueryOp::Should, term2.clone()),
        ]);
        assert!(is_selective(&should_selective_and_selective));

        // <selective> OR <not selective> => <not selective>
        let should_selective_and_nonselective = OptimizedTantivySearch::BooleanQuery(vec![
            (BooleanQueryOp::Should, term1.clone()),
            (BooleanQueryOp::Should, exists.clone()),
        ]);
        assert!(!is_selective(&should_selective_and_nonselective));

        // Boolean combinations - Must (AND) operations

        // <selective> AND <selective> => <selective>
        let must_selective_and_selective = OptimizedTantivySearch::BooleanQuery(vec![
            (BooleanQueryOp::Must, term1.clone()),
            (BooleanQueryOp::Must, term2.clone()),
        ]);
        assert!(is_selective(&must_selective_and_selective));

        // <selective> AND <not selective> => <selective>
        let must_selective_and_nonselective = OptimizedTantivySearch::BooleanQuery(vec![
            (BooleanQueryOp::Must, term1.clone()),
            (BooleanQueryOp::Must, exists.clone()),
        ]);
        assert!(is_selective(&must_selective_and_nonselective));

        // MustNot tests
        let must_not_selective =
            OptimizedTantivySearch::BooleanQuery(vec![(BooleanQueryOp::MustNot, term1.clone())]);
        assert!(is_selective(&must_not_selective));

        // Complex nested cases
        let nested = OptimizedTantivySearch::BooleanQuery(vec![
            (BooleanQueryOp::Must, term1.clone()),
            (
                BooleanQueryOp::Should,
                OptimizedTantivySearch::BooleanQuery(vec![
                    (BooleanQueryOp::Must, term2),
                    (BooleanQueryOp::MustNot, exists),
                ]),
            ),
        ]);
        assert!(is_selective(&nested));
    }

    // Helper functions to create test instances
    fn create_tantivy_field(name: &str) -> util::schema::TantivyField {
        util::schema::TantivyField {
            name: name.to_string(),
            field_ts: 0,
            repeated: false,
            lossy_fast_field: false,
            field_type: create_tantivy_type(),
        }
    }

    fn create_tantivy_type() -> util::schema::TantivyType {
        util::schema::TantivyType::Str(util::schema::TextOptions {
            stored: true,
            fast: false,
            tokenize: false,
        })
    }

    fn create_term_query(field: &str, value: &str) -> crate::optimizer::ast::TermQuery {
        crate::optimizer::ast::TermQuery {
            field: create_tantivy_field(field),
            json_path: vec![],
            tantivy_type: tantivy::schema::Type::Str,
            value: util::serde_json::Value::String(value.to_string()),
        }
    }
}
