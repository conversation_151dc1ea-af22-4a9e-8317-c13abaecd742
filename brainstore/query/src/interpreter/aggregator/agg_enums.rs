use tantivy::DateTime;
use util::{ptree::MakePTree, xact::PaginationKey, Value};

use super::aggregate::*;
use super::sum::DynamicSum;
use crate::interpreter::columnar::value::{BytesOrdinal, Null, StringOrdinal};
use crate::interpreter::columnar::{
    value::ColumnarExprContext, ColumnarValueType, OpaqueBuffer, PtrOffset,
};
use crate::interpreter::error::Result;

// Define a macro to generate the StaticAggregator enum and implementations
//
// Parameters for each aggregator entry:
// 1. $variant:ident - The enum variant name for StaticAggregator (e.g., NullConstantCount)
// 2. $agg_ty:ident - The aggregator type/function (e.g., ConstantCount, ExprCount)
// 3. $inner_ty:ty - The Rust type that this aggregator operates on (e.g., Null, bool, i64)
// 4. $columnar_ty:pat - The ColumnarValueType pattern(s) that map to this variant (e.g., Null, I64 | DateTime)
//
// The macro automatically generates Percentile variants for each unique type
macro_rules! define_static_aggregator {
    (
        aggregators: [ $( ($variant:ident, $agg_ty:ident, $inner_ty:ty, $columnar_ty:pat) ),+ $(,)? ]
        percentile_types: [ $( ($type_prefix:ident, $type_ty:ty, $type_columnar:pat) ),+ $(,)? ],
    ) => {
        #[derive(Debug, Clone)]
        pub enum StaticAggregator {
            $( $variant($agg_ty<$inner_ty>), )+
            // Percentile variants generated from percentile_types list
            $( $type_prefix(Percentile<$type_ty>), )+
        }

        impl MakePTree for StaticAggregator {
            fn label(&self) -> String {
                match self {
                    $( StaticAggregator::$variant(..) => stringify!($variant).to_string(), )+
                    // Percentile cases
                    $( StaticAggregator::$type_prefix(..) => stringify!($type_prefix).to_string(), )+
                }
            }

            fn make_ptree(&self, builder: &mut util::ptree::TreeBuilder) {
                match self {
                    $( StaticAggregator::$variant(inner) => inner.make_ptree(builder), )+
                    // Percentile cases
                    $( StaticAggregator::$type_prefix(inner) => inner.make_ptree(builder), )+
                }
            }
        }

        /* optional ergonomics: allow `foo.into()` */
        $(
            impl From<$agg_ty<$inner_ty>> for StaticAggregator {
                fn from(v: $agg_ty<$inner_ty>) -> Self {
                    StaticAggregator::$variant(v)
                }
            }
        )+

        // From implementations for Percentile types
        $(
            impl From<Percentile<$type_ty>> for StaticAggregator {
                fn from(v: Percentile<$type_ty>) -> Self {
                    StaticAggregator::$type_prefix(v)
                }
            }
        )+

        impl StaticAggregator {
            pub fn aggregate(&mut self, ctx: &ColumnarExprContext, buf: &mut OpaqueBuffer) -> Result<()> {
                match self {
                    $( StaticAggregator::$variant(inner) => {
                        let slice = buf.as_mut_slice::<Option<$inner_ty>>();
                        inner.aggregate(&ctx, slice)
                    } )+
                    // Percentile cases
                    $( StaticAggregator::$type_prefix(inner) => {
                        let slice = buf.as_mut_slice::<Option<$type_ty>>();
                        inner.aggregate(&ctx, slice)
                    } )+
                }
            }

            pub fn aggregate_value(&mut self, ctx: &ColumnarExprContext, buf: &mut OpaqueBuffer, idx: usize) -> Result<()> {
                match self {
                    $( StaticAggregator::$variant(inner) => {
                        let value = buf.get::<Option<$inner_ty>>(idx);
                        inner.aggregate_value(&ctx, value)
                    } )+
                    // Percentile cases
                    $( StaticAggregator::$type_prefix(inner) => {
                        let value = buf.get::<Option<$type_ty>>(idx);
                        inner.aggregate_value(&ctx, value)
                    } )+
                }
            }

            pub fn combine(&mut self, other: Self) -> Result<()> {
                match (self, other) {
                    $( (StaticAggregator::$variant(a), StaticAggregator::$variant(b)) => {
                        a.combine(b)
                    } )+
                    // Percentile cases
                    $( (StaticAggregator::$type_prefix(a), StaticAggregator::$type_prefix(b)) => {
                        a.combine(b)
                    } )+
                    (a, b) => {
                        panic!("cannot combine aggregators of different types ({:?} vs {:?})", a, b);
                    }
                }
            }

            pub fn collect(&self) -> Result<Value> {
                match self {
                    $( StaticAggregator::$variant(inner) => inner.collect(), )+
                    // Percentile cases
                    $( StaticAggregator::$type_prefix(inner) => inner.collect(), )+
                }
            }

            pub fn into_value_aggregator(self) -> ValueAggregator {
                match self {
                    $( StaticAggregator::$variant(inner) => inner.into_value_aggregator(), )+
                    // Percentile cases
                    $( StaticAggregator::$type_prefix(inner) => inner.into_value_aggregator(), )+
                }
            }

            pub fn from_function_and_type(
                func: &AggregateFunction,
                columnar_type: ColumnarValueType,
            ) -> Option<StaticAggregator> {
                use ColumnarValueType::*;
                Some(match (func, columnar_type) {
                    $(
                        (AggregateFunction::$agg_ty, $columnar_ty) => {
                            StaticAggregator::$variant($agg_ty::default())
                        }
                    )+
                    // Percentile cases
                    $(
                        (AggregateFunction::Percentile { percentile }, $type_columnar) => {
                            StaticAggregator::$type_prefix(Percentile::new(*percentile))
                        }
                    )+
                    (_, Dynamic) => {
                        panic!("Should have been optimized away");
                    }
                })
            }
        }
    };
}

pub type DynamicValue<'a> = std::borrow::Cow<'a, util::Value>;
macro_rules! define_dynamic_value_agg {
    (
        $( ($agg:ident, $dyn_agg:ty) ),+ $(,)?
    ) => {
        /* ---------- enum with one variant per aggregator ---------- */
        #[derive(Debug, Clone, MakePTree)]
        pub enum ValueAggregator {
            $( $agg($dyn_agg), )+
            Percentile(Percentile<DynamicValue<'static>>),
        }

        impl ValueAggregator {
            pub fn new(agg: AggregateFunction) -> Self {
                match agg {
                    $(
                        AggregateFunction::$agg => ValueAggregator::$agg(<$dyn_agg>::default()),
                    )+
                    AggregateFunction::Percentile { percentile } => {
                        ValueAggregator::Percentile(Percentile::<DynamicValue<'static>>::new(percentile))
                    }
                }
            }
        }

        /* ---------- method delegations ---------------------------- */
        impl Aggregator for ValueAggregator {
            type Item<'a> = std::borrow::Cow<'a, Value>;

            fn aggregate(
                &mut self,
                ctx: &ColumnarExprContext,
                input: &[Self::Item<'_>],
            ) -> Result<()> {
                match self {
                    $( ValueAggregator::$agg(inner) => inner.aggregate(ctx, input), )+
                    ValueAggregator::Percentile(inner) => inner.aggregate(ctx, input),
                }
            }

            fn into_value_aggregator(self) -> ValueAggregator {
                self
            }
        }

        impl AggregatorBase for ValueAggregator {
            fn combine(&mut self, other: Self) -> Result<()> {
                match (self, other) {
                    $( (ValueAggregator::$agg(a), ValueAggregator::$agg(b)) => a.combine(b), )+
                    (ValueAggregator::Percentile(a), ValueAggregator::Percentile(b)) => a.combine(b),
                    _ => Err(util::anyhow::anyhow!(
                            "cannot combine different types of aggregators"
                         ).into()),
                }
            }

            fn collect(&self) -> Result<Value> {
                match self {
                    $( ValueAggregator::$agg(inner) => inner.collect(), )+
                    ValueAggregator::Percentile(inner) => inner.collect(),
                }
            }
        }
    };
}

/* --------------------------------------------------------- */
/* 2.  Invoke it once – list the concrete aggregator types   */
/* --------------------------------------------------------- */
define_dynamic_value_agg! {
    (ConstantCount, ConstantCount<DynamicValue<'static>>),
    (ExprCount,     ExprCount<DynamicValue<'static>>),
    (Sum,           DynamicSum),
    (Avg,           DynamicAvg),
    (Min,           DynamicMin),
    (Max,           DynamicMax),
}

// Define all StaticAggregator variants
define_static_aggregator! {
    aggregators: [
        (NullConstantCount,     ConstantCount,  Null,           Null),
        (BoolConstantCount,     ConstantCount,  bool,           Bool),
        (I64ConstantCount,      ConstantCount,  i64,            I64),
        (DTConstantCount,       ConstantCount,  DateTime,       DateTime),
        (U64ConstantCount,      ConstantCount,  u64,            U64),
        (PagKeyConstantCount,   ConstantCount,  PaginationKey,  PaginationKey),
        (F64ConstantCount,      ConstantCount,  f64,            F64),
        (StrPtrConstantCount,   ConstantCount,  PtrOffset,      StringPtr),
        (StrOrdConstantCount,   ConstantCount,  StringOrdinal,  StringOrdinal),
        (BytesOrdConstantCount, ConstantCount,  BytesOrdinal,   BytesOrdinal),

        (NullExprCount,         ExprCount,      Null,           Null),
        (BoolExprCount,         ExprCount,      bool,           Bool),
        (I64ExprCount,          ExprCount,      i64,            I64),
        (DTExprCount,           ExprCount,      DateTime,       DateTime),
        (U64ExprCount,          ExprCount,      u64,            U64),
        (PagKeyExprCount,       ExprCount,      PaginationKey,  PaginationKey),
        (F64ExprCount,          ExprCount,      f64,            F64),
        (StrPtrExprCount,       ExprCount,      PtrOffset,      StringPtr),
        (StrOrdExprCount,       ExprCount,      StringOrdinal,  StringOrdinal),
        (BytesOrdExprCount,     ExprCount,      BytesOrdinal,   BytesOrdinal),

        (NullSum,               Sum,            Null,           Null),
        (BoolSum,               Sum,            bool,           Bool),
        (I64Sum,                Sum,            i64,            I64),
        (U64Sum,                Sum,            u64,            U64),
        (PagKeySum,             Sum,            PaginationKey,  PaginationKey),
        (DTSum,                 Sum,            DateTime,       DateTime),
        (F64Sum,                Sum,            f64,            F64),
        (StrPtrSum,             Sum,            PtrOffset,      StringPtr),
        (StrOrdSum,             Sum,            StringOrdinal,  StringOrdinal),
        (BytesOrdSum,           Sum,            BytesOrdinal,   BytesOrdinal),

        (NullAvg,               Avg,            Null,           Null),
        (BoolAvg,               Avg,            bool,           Bool),
        (I64Avg,                Avg,            i64,            I64),
        (U64Avg,                Avg,            u64,            U64),
        (PagKeyAvg,             Avg,            PaginationKey,  PaginationKey),
        (DTAvg,                 Avg,            DateTime,       DateTime),
        (FAvg,                  Avg,            f64,            F64),
        (StrPtrAvg,             Avg,            PtrOffset,      StringPtr),
        (StrOrdAvg,             Avg,            StringOrdinal,  StringOrdinal),
        (BytesOrdAvg,           Avg,            BytesOrdinal,   BytesOrdinal),

        (NullMin,               Min,            Null,           Null),
        (BoolMin,               Min,            bool,           Bool),
        (I64Min,                Min,            i64,            I64),
        (U64Min,                Min,            u64,            U64),
        (PagKeyMin,             Min,            PaginationKey,  PaginationKey),
        (DTMin,                 Min,            DateTime,       DateTime),
        (F64Min,                Min,            f64,            F64),
        (StrPtrMin,             Min,            PtrOffset,      StringPtr),
        (StrOrdMin,             Min,            StringOrdinal,  StringOrdinal),
        (BytesOrdMin,           Min,            BytesOrdinal,   BytesOrdinal),

        (NullMax,               Max,            Null,           Null),
        (BoolMax,               Max,            bool,           Bool),
        (I64Max,                Max,            i64,            I64),
        (U64Max,                Max,            u64,            U64),
        (PagKeyMax,             Max,            PaginationKey,  PaginationKey),
        (DTMax,                 Max,            DateTime,       DateTime),
        (F64Max,                Max,            f64,            F64),
        (StrPtrMax,             Max,            PtrOffset,      StringPtr),
        (StrOrdMax,             Max,            StringOrdinal,  StringOrdinal),
        (BytesOrdMax,           Max,            BytesOrdinal,   BytesOrdinal)
    ]
    percentile_types: [
        (NullPercentile, Null, Null),
        (BoolPercentile, bool, Bool),
        (I64Percentile, i64, I64),
        (U64Percentile, u64, U64),
        (PagKeyPercentile, PaginationKey, PaginationKey),
        (DTPercentile, DateTime, DateTime),
        (F64Percentile, f64, F64),
        (StrPtrPercentile, PtrOffset, StringPtr),
        (StrOrdPercentile, StringOrdinal, StringOrdinal),
        (BytesOrdPercentile, BytesOrdinal, BytesOrdinal)
    ],
}

#[derive(Debug, Clone, MakePTree)]
pub enum AggregateFunction {
    ConstantCount,
    ExprCount,
    Sum,
    Min,
    Max,
    Avg,
    Percentile { percentile: f64 },
}

impl AggregateFunction {
    pub fn new_static_aggregator(
        &self,
        columnar_type: ColumnarValueType,
    ) -> Option<StaticAggregator> {
        StaticAggregator::from_function_and_type(self, columnar_type)
    }
}
