use util::ptree::MakePTree;

use crate::interpreter::columnar::expr::ColumnarExpr;

use super::agg_enums::AggregateFunction;

#[derive(Debug, <PERSON>lone, MakePTree)]
pub struct ColumnarAggregator {
    pub agg: AggregateFunction,
    pub expr: ColumnarExpr,
}

impl ColumnarAggregator {
    pub fn new(agg: AggregateFunction, expr: ColumnarExpr) -> Option<Self> {
        Some(Self { agg, expr })
    }
}
