use std::{borrow::Cow, sync::Arc};

use btql::interpreter::expr::interpret_expr;
use util::{ptree::MakePTree, Value};

use super::{agg_enums::AggregateFunction, aggregate::*, ValueAggregator};
use crate::interpreter::{columnar::value::ColumnarExprContext, error::Result};

#[derive(Debu<PERSON>, <PERSON><PERSON>, MakePTree)]
pub struct ExprAggregator {
    pub agg: ValueAggregator,
    expr: Arc<btql::binder::ast::Expr>,
}

impl ExprAggregator {
    pub fn new(agg: AggregateFunction, expr: &btql::binder::ast::Expr) -> Self {
        let agg = ValueAggregator::new(agg);
        Self {
            agg,
            expr: Arc::new(expr.clone()),
        }
    }
}

impl Aggregator for ExprAggregator {
    type Item<'a> = Cow<'a, Value>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        let result = interpret_expr(ctx.expr_ctx(), &self.expr, input)?;

        self.agg.aggregate(ctx, &result)
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        self.agg
    }
}

impl AggregatorBase for ExprAggregator {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.agg.combine(other.agg)
    }

    fn collect(&self) -> Result<Value> {
        self.agg.collect()
    }
}
