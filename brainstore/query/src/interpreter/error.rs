use thiserror::Error;

use crate::interpreter::context::CancellationStatus;

#[derive(Erro<PERSON>, Debug)]
pub enum InterpreterError {
    #[error("Unsupported operation: {name}")]
    Unsupported {
        name: String,
        op: Option<Box<btql::binder::Expr>>,
    },

    #[error("Internal error: {0}")]
    InternalError(String),

    #[error(transparent)]
    Tantivy(#[from] tantivy::TantivyError),

    #[error(transparent)]
    Typesystem(#[from] btql::typesystem::error::TypesystemError),

    #[error(transparent)]
    Anyhow(#[from] util::anyhow::Error),

    #[error(transparent)]
    JoinError(#[from] tokio::task::JoinError),

    #[error("Query cancelled: {reason:?}")]
    Cancelled { reason: CancellationStatus },
}

pub type Result<T, E = InterpreterError> = std::result::Result<T, E>;
