use btql::binder::BindRepl;
use btql::interpreter::context::ExprContext;
use futures::future::join_all;
use serde_json::json;
use std::collections::HashSet;
use std::sync::Arc;
use storage::config_with_store::ConfigWithStore;
use storage::index_document::make_full_schema;
use storage::process_wal::{
    CompactSegmentWalInput, CompactSegmentWalOptions, ProcessObjectWalInput,
    ProcessObjectWalOptions,
};
use storage::wal::{fill_default_object_id, wal_insert_unnormalized};
use storage::xact_manager::MemoryTransactionManager;
use tempfile::tempdir;
use tokio_stream::StreamExt;
use util::anyhow::Result;
use util::config::{Config, StorageConfig};
use util::system_types::make_object_schema;
use util::url::Url;
use util::url_util::str_to_url;
use util::Value;

use crate::interpreter::context::InterpreterOpts;
use crate::interpreter::op::Operator;
use crate::interpreter::InterpreterContext;
use crate::optimizer::optimizer::OptimizerOpts;
use crate::plan;
use crate::planner::context::PlanContext;

fn clean_index(config: ConfigWithStore) -> Result<()> {
    config.index.remove_local()?;
    config.wal.remove_local()?;
    if let Some(metadata) = &config.global_store_info {
        metadata.remove_local()?;
    }
    Ok(())
}

struct TestSetup {
    config_with_store: ConfigWithStore,
    full_schema: util::schema::Schema,
    binder: BindRepl,
    _temp_dir: tempfile::TempDir, // Keep temp dir alive
}

impl TestSetup {
    async fn new() -> Result<Self> {
        // Create temporary directory
        let temp_dir = tempdir()?;

        let config = Config {
            storage: Some(StorageConfig {
                index_uri: str_to_url(
                    &format!("file://{}/index", temp_dir.path().to_str().unwrap()),
                    None,
                )?,
                wal_uri: str_to_url(
                    &format!("file://{}/wal", temp_dir.path().to_str().unwrap()),
                    None,
                )?,
                realtime_wal_uri: Some(str_to_url(
                    &format!("file://{}/wal", temp_dir.path().to_str().unwrap()),
                    None,
                )?),
                metadata_uri: str_to_url(
                    &format!("file://{}/metadata", temp_dir.path().to_str().unwrap()),
                    None,
                )?,
                locks_uri: Url::parse("memory://").unwrap(),
                locks_manager_enable_bookkeeping: false,
                xact_manager_uri: Url::parse("memory://").unwrap(),
            }),
            schema: Some(make_object_schema(
                util::system_types::ObjectType::Experiment,
            )?),
            ..Default::default()
        };

        let config_with_store =
            ConfigWithStore::from_config(config.storage.unwrap(), Default::default())?;

        clean_index(config_with_store.clone())?;

        let full_schema = make_full_schema(&config.schema.unwrap())?;

        // Create a temporary schema file for the binder
        let schema_file = temp_dir.path().join("schema.json");
        std::fs::write(
            &schema_file,
            include_str!("../../../../examples/sampling/schema.json"),
        )?;

        let binder = BindRepl::new(schema_file.to_str().unwrap())?;

        Ok(Self {
            config_with_store,
            full_schema,
            binder,
            _temp_dir: temp_dir,
        })
    }

    async fn load_test_data(&self, num_rows: usize) -> Result<()> {
        let mut values = Vec::new();

        // Generate deterministic test data
        for i in 0..num_rows {
            let trace_id = format!("trace_{:03}", i % 20); // 20 unique traces, 5 spans each
            let experiment_id = format!("exp_{}", i % 5); // 5 experiments

            let value = json!({
                "id": format!("span_{:03}", i),
                "root_span_id": trace_id,
                "experiment_id": experiment_id,
                "project_id": "test_project",
                "_xact_id": 10000 + i,
                "created": format!("2023-01-01T{:02}:00:00Z", i % 24),
                "input": format!("test input {}", i),
                "output": format!("test output {}", i),
                "scores": {
                    "accuracy": (i as f64) / (num_rows as f64),
                },
            });

            values.push(fill_default_object_id(
                value,
                util::system_types::FullObjectId {
                    object_type: util::system_types::ObjectType::Experiment,
                    object_id: util::system_types::ObjectId::new("test")?,
                },
            ));
        }

        // Insert into WAL
        let xact_manager = MemoryTransactionManager::default();
        wal_insert_unnormalized(
            self.config_with_store.wal.as_ref(),
            &*self.config_with_store.global_store,
            values,
            &xact_manager,
        )
        .await?;

        // Process WAL
        let process_output = storage::process_wal::process_object_wal(
            ProcessObjectWalInput {
                object_id: util::system_types::FullObjectId {
                    object_type: util::system_types::ObjectType::Experiment,
                    object_id: util::system_types::ObjectId::new("test")?,
                },
                config: &self.config_with_store,
            },
            Default::default(),
            ProcessObjectWalOptions::default(),
        )
        .await?;

        // Compact segments
        join_all(
            process_output
                .modified_segment_ids
                .iter()
                .map(|segment_id| async {
                    storage::process_wal::compact_segment_wal(
                        CompactSegmentWalInput {
                            segment_id: *segment_id,
                            index_store: self.config_with_store.index.clone(),
                            schema: self.full_schema.clone(),
                            global_store: self.config_with_store.global_store.clone(),
                            locks_manager: self.config_with_store.locks_manager.clone(),
                        },
                        Default::default(),
                        CompactSegmentWalOptions::default(),
                    )
                    .await?;
                    Ok::<(), util::anyhow::Error>(())
                }),
        )
        .await
        .into_iter()
        .collect::<Result<()>>()?;

        Ok(())
    }

    async fn execute_query(&mut self, query_str: &str) -> Result<Vec<Value>> {
        let bound_query = self.binder.bind(query_str)?;
        let expr_ctx = ExprContext::new(Some(0));

        let optimized_query = crate::optimizer::optimize(
            &self.full_schema,
            &bound_query,
            OptimizerOpts::default(),
            &expr_ctx,
        )?;

        let plan_ctx = PlanContext::new(self.full_schema.clone())?;
        let plan = plan(&plan_ctx, &optimized_query)?;

        let ctx = InterpreterContext::new(
            self.config_with_store.clone(),
            self.full_schema.clone(),
            Arc::new(tantivy::Executor::SingleThread),
            tokio::runtime::Handle::current(),
            InterpreterOpts::default(),
            expr_ctx,
            None,
            None,
            None,
        );

        let mut result = plan.interpret(ctx)?;
        let mut rows = Vec::new();
        while let Some(row) = result.next().await {
            rows.push(row?);
        }

        Ok(rows)
    }

    async fn execute_query_with_cursor(
        &mut self,
        query_str: &str,
    ) -> Result<(Vec<Value>, Option<String>)> {
        let bound_query = self.binder.bind(query_str)?;
        let expr_ctx = ExprContext::new(Some(0));

        let optimized_query = crate::optimizer::optimize(
            &self.full_schema,
            &bound_query,
            OptimizerOpts::default(),
            &expr_ctx,
        )?;

        let plan_ctx = PlanContext::new(self.full_schema.clone())?;
        let plan = plan(&plan_ctx, &optimized_query)?;

        let ctx = InterpreterContext::new(
            self.config_with_store.clone(),
            self.full_schema.clone(),
            Arc::new(tantivy::Executor::SingleThread),
            tokio::runtime::Handle::current(),
            InterpreterOpts::default(),
            expr_ctx,
            None,
            None,
            None,
        );

        let mut result = plan.interpret(ctx.clone())?;
        let mut rows = Vec::new();
        while let Some(row) = result.next().await {
            rows.push(row?);
        }

        // Get cursor from context
        let cursor = ctx.get_cursor().map(|c| c.to_string());

        Ok((rows, cursor))
    }

    async fn load_test_data_with_offset(&self, num_rows: usize, id_offset: usize) -> Result<()> {
        let mut values = Vec::new();

        // Generate more deterministic test data with offset
        for i in 0..num_rows {
            let actual_id = id_offset + i;
            let trace_id = format!("trace_{:03}", (actual_id) % 20); // Still 20 unique traces
            let experiment_id = format!("exp_{}", (actual_id) % 5); // Still 5 experiments

            let value = json!({
                "id": format!("span_{:03}", actual_id),
                "root_span_id": trace_id,
                "experiment_id": experiment_id,
                "project_id": "test_project",
                "_xact_id": 20000 + actual_id, // Higher _xact_id values for new data
                "created": format!("2023-01-02T{:02}:00:00Z", (actual_id) % 24), // Different day
                "input": format!("test input {}", actual_id),
                "output": format!("test output {}", actual_id),
                "scores": {
                    "accuracy": (actual_id as f64) / (200.0), // Adjust for total of 200 rows
                },
            });

            values.push(fill_default_object_id(
                value,
                util::system_types::FullObjectId {
                    object_type: util::system_types::ObjectType::Experiment,
                    object_id: util::system_types::ObjectId::new("test")?,
                },
            ));
        }

        // Insert into WAL
        let xact_manager = MemoryTransactionManager::default();
        wal_insert_unnormalized(
            self.config_with_store.wal.as_ref(),
            &*self.config_with_store.global_store,
            values,
            &xact_manager,
        )
        .await?;

        // Process WAL
        let process_output = storage::process_wal::process_object_wal(
            ProcessObjectWalInput {
                object_id: util::system_types::FullObjectId {
                    object_type: util::system_types::ObjectType::Experiment,
                    object_id: util::system_types::ObjectId::new("test")?,
                },
                config: &self.config_with_store,
            },
            Default::default(),
            ProcessObjectWalOptions::default(),
        )
        .await?;

        // Compact segments
        join_all(
            process_output
                .modified_segment_ids
                .iter()
                .map(|segment_id| async {
                    storage::process_wal::compact_segment_wal(
                        CompactSegmentWalInput {
                            segment_id: *segment_id,
                            index_store: self.config_with_store.index.clone(),
                            schema: self.full_schema.clone(),
                            global_store: self.config_with_store.global_store.clone(),
                            locks_manager: self.config_with_store.locks_manager.clone(),
                        },
                        Default::default(),
                        CompactSegmentWalOptions::default(),
                    )
                    .await?;
                    Ok::<(), util::anyhow::Error>(())
                }),
        )
        .await
        .into_iter()
        .collect::<Result<()>>()?;

        Ok(())
    }
}

#[tokio::test]
async fn test_sample_rate_deterministic() -> Result<()> {
    let mut setup = TestSetup::new().await?;
    setup.load_test_data(100).await?;

    // Test rate sampling with fixed seed
    let query = "select: * | from: experiment('test') | sample: 10% seed 12345";
    let results = setup.execute_query(query).await?;

    // Should get approximately 10% of rows (may vary due to probabilistic nature)
    assert!(
        results.len() >= 5 && results.len() <= 15,
        "Rate sampling should return ~10 rows, got {}",
        results.len()
    );

    // Same query should return same results with same seed
    let results2 = setup.execute_query(query).await?;
    assert_eq!(
        results.len(),
        results2.len(),
        "Same seed should produce same count"
    );

    // Extract IDs and verify they're identical
    let extract_ids = |rows: &[Value]| -> Vec<String> {
        let mut ids: Vec<String> = rows
            .iter()
            .filter_map(|row| row.get("id")?.as_str().map(String::from))
            .collect();
        ids.sort(); // Sort for consistent comparison
        ids
    };

    let ids1 = extract_ids(&results);
    let ids2 = extract_ids(&results2);

    assert_eq!(ids1, ids2, "Same seed should produce identical IDs");

    Ok(())
}

#[tokio::test]
async fn test_sample_count_exact() -> Result<()> {
    let mut setup = TestSetup::new().await?;
    setup.load_test_data(100).await?;

    // Test count sampling
    let query = "select: * | from: experiment('test') | sample: 10 seed 12345";
    let results = setup.execute_query(query).await?;

    // Should get exactly 10 rows
    assert_eq!(
        results.len(),
        10,
        "Count sampling should return exactly 10 rows"
    );

    // Same query should return same results with same seed
    let results2 = setup.execute_query(query).await?;
    assert_eq!(
        results.len(),
        results2.len(),
        "Same seed should produce same count"
    );

    // Extract IDs and verify they're identical
    let extract_ids = |rows: &[Value]| -> Vec<String> {
        let mut ids: Vec<String> = rows
            .iter()
            .filter_map(|row| row.get("id")?.as_str().map(String::from))
            .collect();
        ids.sort(); // Sort for consistent comparison
        ids
    };

    let ids1 = extract_ids(&results);
    let ids2 = extract_ids(&results2);

    assert_eq!(ids1, ids2, "Same seed should produce identical IDs");

    // Test larger count
    let query = "select: * | from: experiment('test') | sample: 25 seed 12345";
    let results = setup.execute_query(query).await?;
    assert_eq!(
        results.len(),
        25,
        "Count sampling should return exactly 25 rows"
    );

    Ok(())
}

#[tokio::test]
async fn test_sample_with_deduplication() -> Result<()> {
    let mut setup = TestSetup::new().await?;
    setup.load_test_data(100).await?;

    // Test basic sampling without deduplication for now
    // TODO: Add deduplication test when BTQL parser supports it
    let query = "select: * | from: experiment('test') | sample: 15 seed 12345";
    let results = setup.execute_query(query).await?;

    // Should get exactly 15 rows for count sampling
    assert_eq!(
        results.len(),
        15,
        "Count sampling should return exactly 15 rows"
    );

    // Check that we have some variety in root_span_id values (we have 20 unique traces)
    let mut seen_trace_ids = HashSet::new();
    for row in &results {
        if let Some(trace_id) = row.get("root_span_id") {
            if let Some(trace_str) = trace_id.as_str() {
                seen_trace_ids.insert(trace_str.to_string());
            }
        }
    }

    // We should have sampled from multiple traces
    assert!(
        seen_trace_ids.len() > 1,
        "Should sample from multiple traces"
    );

    Ok(())
}

#[tokio::test]
async fn test_sample_seed_consistency() -> Result<()> {
    let mut setup = TestSetup::new().await?;
    setup.load_test_data(100).await?;

    let query = "select: * | from: experiment('test') | sample: 10 seed 42";

    // Run same query multiple times
    let results1 = setup.execute_query(query).await?;
    let results2 = setup.execute_query(query).await?;
    let results3 = setup.execute_query(query).await?;

    // All should return same count
    assert_eq!(results1.len(), 10);
    assert_eq!(results2.len(), 10);
    assert_eq!(results3.len(), 10);

    // Extract IDs for comparison
    let extract_ids = |rows: &[Value]| -> Vec<String> {
        rows.iter()
            .filter_map(|row| row.get("id")?.as_str().map(String::from))
            .collect()
    };

    let ids1 = extract_ids(&results1);
    let ids2 = extract_ids(&results2);
    let ids3 = extract_ids(&results3);

    // Same seed should produce same results
    assert_eq!(ids1, ids2, "Same seed should produce identical results");
    assert_eq!(ids2, ids3, "Same seed should produce identical results");

    Ok(())
}

#[tokio::test]
async fn test_sample_different_seeds() -> Result<()> {
    let mut setup = TestSetup::new().await?;
    setup.load_test_data(100).await?;

    let query1 = "select: * | from: experiment('test') | sample: 10 seed 111";
    let query2 = "select: * | from: experiment('test') | sample: 10 seed 222";

    let results1 = setup.execute_query(query1).await?;
    let results2 = setup.execute_query(query2).await?;

    assert_eq!(results1.len(), 10);
    assert_eq!(results2.len(), 10);

    let extract_ids = |rows: &[Value]| -> HashSet<String> {
        rows.iter()
            .filter_map(|row| row.get("id")?.as_str().map(String::from))
            .collect()
    };

    let ids1 = extract_ids(&results1);
    let ids2 = extract_ids(&results2);

    // Different seeds should likely produce different results
    // (There's a small chance they could be the same, but very unlikely)
    assert_ne!(
        ids1, ids2,
        "Different seeds should produce different results"
    );

    Ok(())
}

#[tokio::test]
async fn test_sample_boundary_conditions() -> Result<()> {
    let mut setup = TestSetup::new().await?;
    setup.load_test_data(100).await?;

    // Test sampling more than available
    let query = "select: * | from: experiment('test') | sample: 150 seed 12345";
    let results = setup.execute_query(query).await?;
    assert_eq!(results.len(), 100, "Cannot sample more than available rows");

    // Test sampling with rate > 1.0 (should clamp to 1.0)
    let query = "select: * | from: experiment('test') | sample: 150% seed 12345";
    let results = setup.execute_query(query).await?;
    assert_eq!(results.len(), 100, "Rate > 1.0 should return all rows");

    // Test very small sample
    let query = "select: * | from: experiment('test') | sample: 1 seed 12345";
    let results = setup.execute_query(query).await?;
    assert_eq!(results.len(), 1, "Should return exactly 1 row");

    Ok(())
}

#[tokio::test]
async fn test_exact_sample_pagination_bug() -> Result<()> {
    for seed in 0..10 {
        let mut setup = TestSetup::new().await?;
        setup.load_test_data(100).await?;

        // Test the EXACT scenario you described:
        // 1. Sample 10%, get results + cursor
        // 2. Use that cursor with same sample 10% -> should get NOTHING (the bug)

        // Step 1: Sample 10% with small limit to force pagination
        let query1 =
        format!("select: * | from: experiment('test') | sample: 10% seed {} | sort: _pagination_key asc", seed);
        let (results1, cursor1) = setup.execute_query_with_cursor(&query1).await?;

        // We need to ensure we have a meaningful number of results
        assert!(
            !results1.is_empty(),
            "First query should return some results"
        );

        let cursor = cursor1.unwrap();

        let query2 = format!("select: * | from: experiment('test') | sample: 10% seed {} | sort: _pagination_key asc | cursor: '{}'", seed, cursor);
        let (results2, _) = setup.execute_query_with_cursor(&query2).await?;

        // This is where the bug should manifest:
        // If sampling is working CORRECTLY, the second query should return 0 results
        // because we already sampled 10% of the total data in the first query
        if !results2.is_empty() {
            panic!(
                "SAMPLING PAGINATION BUG DETECTED: Second query returned {} results when it should return 0. \
                 Sampling is incorrectly re-applied to cursor-filtered data instead of respecting \
                 the global percentage limit.", results2.len()
            );
        }
    }

    Ok(())
}

#[tokio::test]
async fn test_sample_with_new_data_after_pagination() -> Result<()> {
    for seed in 0..10 {
        let mut setup = TestSetup::new().await?;
        setup.load_test_data(100).await?;

        // Step 1: Sample 10% from initial 100 rows
        let query1 =
        format!("select: * | from: experiment('test') | sample: 10% seed {} | sort: _pagination_key asc", seed);
        let (results1, cursor1) = setup.execute_query_with_cursor(&query1).await?;

        let cursor1 = cursor1.expect("should have gotten a cursor");

        // Step 2: Use cursor to get rest of the 10% sample (should be 0 if sampling works correctly)
        let query2 = format!("select: * | from: experiment('test') | sample: 10% seed {} | sort: _pagination_key asc | cursor: '{}'", seed, cursor1);
        let (results2, _) = setup.execute_query_with_cursor(&query2).await?;

        if !results2.is_empty() {
            panic!(
                "Got {} results with cursor on original data",
                results2.len()
            );
        }

        // Step 3: Add 100 MORE rows with later _xact_id values
        setup.load_test_data_with_offset(100, 100).await?; // 100 more rows, starting from ID 100

        // Step 4: Sample 10% again - should now get ~10 results from the NEW data
        let query3 = format!("select: * | from: experiment('test') | sample: 10% seed {} | sort: _pagination_key asc | cursor: '{}'", seed, cursor1);
        let results3 = setup.execute_query(&query3).await?;

        // Verify no overlapping IDs between queries 1&2 (original data) and query 3 (with new data)
        let ids1: std::collections::HashSet<String> = results1
            .iter()
            .filter_map(|r| r.get("id")?.as_str().map(String::from))
            .collect();
        let ids2: std::collections::HashSet<String> = results2
            .iter()
            .filter_map(|r| r.get("id")?.as_str().map(String::from))
            .collect();
        let ids3: std::collections::HashSet<String> = results3
            .iter()
            .filter_map(|r| r.get("id")?.as_str().map(String::from))
            .collect();

        // Combine IDs from queries 1 and 2 (original data queries)
        let original_data_ids: std::collections::HashSet<String> =
            ids1.union(&ids2).cloned().collect();

        let overlap_count = original_data_ids.intersection(&ids3).count();

        if overlap_count > 0 {
            panic!(
            "Sampling consistency violated: {} IDs appear in both original data queries and new data query. \
             This indicates sampling is not properly tracking what was already sampled.",
            overlap_count
        );
        }

        // This is the key test: we should get ~10% of the NEW data (so around 10 results)
        // because the sampling should include the new data in its sample
        if results3.len() >= 5 && results3.len() <= 15 {
            // Verify that some of the results are from the new data (IDs >= 100)
            let new_data_count = results3
                .iter()
                .filter_map(|r| r.get("id")?.as_str())
                .filter(|id| id.starts_with("span_1")) // Our new data has IDs like span_100, span_101, etc.
                .count();

            if new_data_count == 0 {
                panic!("No results from new data - sampling may not be seeing new rows");
            }
        } else {
            panic!(
                "Got {} results, expected ~10 (new data after cursor). \
                 This suggests sampling behavior with new data is incorrect",
                results3.len()
            );
        }
    }
    Ok(())
}
