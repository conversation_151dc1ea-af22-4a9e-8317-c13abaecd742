use std::{collections::HashMap, sync::Arc};

use crate::interpreter::columnar::hash_map::FastHashMap;
use crate::interpreter::tantivy::aggregate::{get_fast_fields_impl, UnpivotProjectedFieldWithName};
use crate::optimizer::ast::{TantivyUnpivotProjectionExpr, UnpivotProjectedField};
use crate::planner::tantivy::JSON_PATH_SEGMENT_SEP;
use storage::tantivy_index::JSON_ROOT_FIELD;
use tantivy::{
    columnar::{
        column_index::Set, BytesColumn, Column, ColumnIndex, ColumnarReader, DynamicColumn,
        DynamicColumnHandle, StrColumn,
    },
    schema::OwnedValue,
};
use util::tracer::trace_if;

#[inline(always)]
pub fn collect_column_for_doc(column_handle: &DynamicColumn, doc: u32) -> Option<OwnedValue> {
    match column_handle {
        DynamicColumn::Bool(column) => collect_column_for_doc_primitive(column, doc),
        DynamicColumn::I64(column) => collect_column_for_doc_primitive(column, doc),
        DynamicColumn::U64(column) => collect_column_for_doc_primitive(column, doc),
        DynamicColumn::F64(column) => collect_column_for_doc_primitive(column, doc),
        DynamicColumn::IpAddr(column) => collect_column_for_doc_primitive(column, doc),
        DynamicColumn::DateTime(column) => collect_column_for_doc_primitive(column, doc),
        DynamicColumn::Bytes(column) => collect_column_for_doc_bytes(column, doc),
        DynamicColumn::Str(column) => collect_column_for_doc_str(column, doc),
    }
}

#[inline(always)]
pub fn collect_column_for_doc_batch(
    column_handle: &DynamicColumn,
    docs: &[u32],
) -> std::io::Result<Vec<Option<OwnedValue>>> {
    Ok(match column_handle {
        DynamicColumn::Bool(column) => collect_column_for_doc_batch_primitive(column, docs),
        DynamicColumn::I64(column) => collect_column_for_doc_batch_primitive(column, docs),
        DynamicColumn::U64(column) => collect_column_for_doc_batch_primitive(column, docs),
        DynamicColumn::F64(column) => collect_column_for_doc_batch_primitive(column, docs),
        DynamicColumn::IpAddr(column) => collect_column_for_doc_batch_primitive(column, docs),
        DynamicColumn::DateTime(column) => collect_column_for_doc_batch_primitive(column, docs),
        DynamicColumn::Bytes(column) => batch_get_byte_values(column, docs, false)?,
        DynamicColumn::Str(column) => batch_get_byte_values(column, docs, true)?,
    })
}

pub fn column_to_type(column_handle: &DynamicColumn) -> tantivy::schema::Type {
    match column_handle {
        DynamicColumn::Bool(_) => tantivy::schema::Type::Bool,
        DynamicColumn::I64(_) => tantivy::schema::Type::I64,
        DynamicColumn::U64(_) => tantivy::schema::Type::U64,
        DynamicColumn::F64(_) => tantivy::schema::Type::F64,
        DynamicColumn::IpAddr(_) => tantivy::schema::Type::IpAddr,
        DynamicColumn::DateTime(_) => tantivy::schema::Type::Date,
        DynamicColumn::Bytes(_) => tantivy::schema::Type::Bytes,
        DynamicColumn::Str(_) => tantivy::schema::Type::Str,
    }
}

pub fn collect_column_for_doc_batch_primitive<
    'a,
    T: PartialOrd + Copy + std::fmt::Debug + Send + Sync + 'static + Into<OwnedValue>,
>(
    column_handle: &'a Column<T>,
    docs: &[u32],
) -> Vec<Option<OwnedValue>> {
    docs.iter()
        .map(|doc| collect_column_for_doc_primitive(column_handle, *doc))
        .collect()
}

pub fn collect_column_for_doc_batch_bytes(
    column_handle: &BytesColumn,
    docs: &[u32],
) -> std::io::Result<Vec<Option<OwnedValue>>> {
    batch_get_byte_values(column_handle, docs, false)
}

pub fn collect_column_for_doc_batch_str(
    column_handle: &StrColumn,
    docs: &[u32],
) -> std::io::Result<Vec<Option<OwnedValue>>> {
    batch_get_byte_values(column_handle, docs, true)
}

#[inline(always)]
pub fn collect_column_for_doc_primitive<
    'a,
    T: PartialOrd + Copy + std::fmt::Debug + Send + Sync + 'static + Into<OwnedValue>,
>(
    column_handle: &'a Column<T>,
    doc: u32,
) -> Option<OwnedValue> {
    let mut iter = column_handle.values_for_doc(doc).map(|value| value.into());
    let first: OwnedValue = if let Some(value) = iter.next() {
        value
    } else {
        // NOTE: if the column has no values for this row, we can't know whether the original value was null
        // or omitted. So, we just return None, but it's up to the caller to deal with that ambiguity.
        return None;
    };
    Some(match iter.next() {
        None => first,
        Some(value) => OwnedValue::Array([first, value].into_iter().chain(iter).collect()),
    })
}

#[inline(always)]
fn collect_column_for_doc_bytes(column_handle: &BytesColumn, doc: u32) -> Option<OwnedValue> {
    let mut iter = column_handle
        .term_ords(doc)
        .map(|ord| {
            let mut res = Vec::new();
            column_handle.ord_to_bytes(ord, &mut res)?;
            Ok::<_, std::io::Error>(OwnedValue::Bytes(res))
        })
        .map(|res| res.unwrap_or(OwnedValue::Null));
    let first: OwnedValue = if let Some(value) = iter.next() {
        value
    } else {
        return None;
    };
    if matches!(first, OwnedValue::Null) {
        log::warn!("No value found for column");
    }
    Some(match iter.next() {
        None => first,
        Some(value) => OwnedValue::Array([first, value].into_iter().chain(iter).collect()),
    })
}

#[inline(always)]
fn collect_column_for_doc_str(column_handle: &StrColumn, doc: u32) -> Option<OwnedValue> {
    let mut iter = column_handle
        .term_ords(doc)
        .map(|ord| {
            let mut res = String::new();
            column_handle.ord_to_str(ord, &mut res)?;
            Ok::<_, std::io::Error>(OwnedValue::Str(res))
        })
        .map(|res| res.unwrap_or(OwnedValue::Null));
    let first: OwnedValue = if let Some(value) = iter.next() {
        value
    } else {
        return None;
    };
    if matches!(first, OwnedValue::Null) {
        log::warn!("No value found for column");
    }
    Some(match iter.next() {
        None => first,
        Some(value) => OwnedValue::Array([first, value].into_iter().chain(iter).collect()),
    })
}

fn batch_get_byte_values(
    column: &BytesColumn,
    docs: &[u32],
    to_string: bool,
) -> std::io::Result<Vec<Option<OwnedValue>>> {
    let dictionary = column.dictionary();
    let mut all_term_ords = Vec::new();
    let mut doc_address_to_ord_indices = HashMap::new();

    for doc in docs {
        let start_idx = all_term_ords.len();
        all_term_ords.extend(column.term_ords(*doc));

        doc_address_to_ord_indices.insert(*doc, start_idx..all_term_ords.len());
    }
    let mut results = batch_get_terms(dictionary, &all_term_ords)?;

    let mut values = Vec::new();
    for doc in docs {
        let matching_range = doc_address_to_ord_indices.get(doc).unwrap().clone();
        let mut doc_values = Vec::new();
        for i in matching_range {
            let v = std::mem::replace(&mut results[i], None);
            if let Some(v) = v {
                let value = if to_string {
                    OwnedValue::Str(match String::from_utf8(v) {
                        Ok(s) => s,
                        Err(e) => {
                            let valid_up_to = e.utf8_error().valid_up_to();
                            let mut bytes = e.into_bytes();
                            bytes.truncate(valid_up_to);
                            String::from_utf8(bytes).map_err(|e| {
                                std::io::Error::new(
                                    std::io::ErrorKind::InvalidData,
                                    format!(
                                        "Failed to convert columnstore bytes to UTF-8 string: {}",
                                        e
                                    ),
                                )
                            })?
                        }
                    })
                } else {
                    OwnedValue::Bytes(v)
                };

                doc_values.push(value);
            }
        }

        if doc_values.len() == 0 {
            values.push(None);
        } else if doc_values.len() == 1 {
            values.push(Some(doc_values.remove(0)));
        } else {
            values.push(Some(OwnedValue::Array(doc_values)));
        }
    }
    Ok(values)
}

// This function is based on https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/sstable/src/dictionary.rs#L324
// and it addresses an issue with how Tantivy accesses columnar strings/bytes. The issue is that `ord_to_term`:
// * Finds the block containing the ordinal (relatively fast)
// * Reads the block via a FileSlice (relatively fast, if cached)
// * Decompresses the block (VERY slow)
// * Decodes the block up to the ordinal (somewhat slow)
//
// Doing this ~20k times is very slow, like 100ms, even for tiny strings like span_id. This function is a batched
// version of that operation, which takes a bunch of ordinals, and skips around to the blocks that contain them,
// knocking away as many ordinals as relevant while decoding a block. Because of how it's written, it should be no slower
// than `ord_to_term` for a single ordinal, and it's much faster (4ms for 20k ordinals) when you're requesting a bunch.
fn batch_get_terms(
    dictionary: &tantivy::columnar::Dictionary,
    term_ords: &[u64],
) -> std::io::Result<Vec<Option<Vec<u8>>>> {
    if term_ords.is_empty() {
        return Ok(vec![]);
    }

    // Create mapping from ordinal to result indices
    let ord_to_indices: std::collections::HashMap<u64, Vec<usize>> = term_ords
        .iter()
        .enumerate()
        .fold(HashMap::new(), |mut map, (idx, &ord)| {
            map.entry(ord).or_default().push(idx);
            map
        });

    // Sort the ordinals in descending order and loop over them backwards. This is preferable to
    // iterating in forwards order because then we'd have to use a VecDeque rather than a Vec.
    let mut rev_sorted_ords = ord_to_indices.keys().cloned().collect::<Vec<_>>();
    rev_sorted_ords.sort_by(|a, b| b.cmp(a));

    let mut results = vec![None; term_ords.len()];

    let mut curr_sstable_delta_reader_ordinal = 0;
    let mut curr_sstable_delta_reader = None;
    let mut bytes = Vec::new();
    while let Some(ord) = rev_sorted_ords.last().cloned() {
        let mut sstable_delta_reader = match curr_sstable_delta_reader {
            Some(sstable_delta_reader) => sstable_delta_reader,
            None => {
                let block_addr = dictionary.sstable_index.get_block_with_ord(ord);
                let first_ordinal = block_addr.first_ordinal;

                bytes.clear();
                curr_sstable_delta_reader_ordinal = first_ordinal;

                dictionary.sstable_delta_reader_block(block_addr)?
            }
        };

        // This code resembles the logic in `ord_to_term` (https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/sstable/src/dictionary.rs#L331)
        // which opens a block, and then delta-decodes it until it finds the ordinal.
        //
        // Instead, we proceed from the current ordinal until the the next ordinal, and once we hit that, we save the value and continue.
        // This allows us to avoid re-decoding the block for each ordinal.
        //
        // The innards of the loop are specific to the block's delta encoding. Each call to `advance` progresses to the next string, and the
        // `truncate()`/`extend_from_slice()` calls implement the actual delta decoding.
        let mut exhausted = false;
        while curr_sstable_delta_reader_ordinal <= ord {
            if !sstable_delta_reader.advance()? {
                // We didn't find the ordinal in the current block, so let's null it out
                // and continue
                exhausted = true;
                break;
            }
            bytes.truncate(sstable_delta_reader.common_prefix_len());
            bytes.extend_from_slice(sstable_delta_reader.suffix());
            curr_sstable_delta_reader_ordinal += 1;
        }

        if exhausted {
            // If we couldn't find the ordinal in the current block, we'll try the next one
            curr_sstable_delta_reader = None;
            continue;
        } else {
            // We found the ordinal in the current block, so let's add it to the results and clean up our housekeeping
            // loop variables.
            if let Some(indices) = ord_to_indices.get(&ord) {
                for idx in indices {
                    results[*idx] = Some(bytes.clone());
                }
            }

            rev_sorted_ords.pop();
            curr_sstable_delta_reader = Some(sstable_delta_reader);
        }
    }

    Ok(results)
}

pub fn get_columns_matching_prefix(
    columnar: &Arc<ColumnarReader>,
    path: &str,
) -> Result<Vec<(String, DynamicColumnHandle)>, std::io::Error> {
    let mut end_str = path.to_string();
    end_str.push((JSON_PATH_SEGMENT_SEP as u8 + 1) as char);

    columnar.read_columns_range(&path, &end_str)
}

// Opens a column reader for each unpivot field in the segment. For an unpivot expr
// scores as (score, value), we will open a column reader for every field of "scores" that
// exists in the segment.
pub fn open_unpivot_readers(
    unpivot_exprs: &[TantivyUnpivotProjectionExpr],
    segment_reader: &tantivy::SegmentReader,
    tracer: Option<std::sync::Arc<util::tracer::TracedNode>>,
) -> Result<Vec<Vec<(UnpivotProjectedFieldWithName, DynamicColumn)>>, tantivy::TantivyError> {
    trace_if(
        log::Level::Info,
        &tracer,
        "Open unpivot readers",
        |_child| {
            let fast_fields = segment_reader.fast_fields();
            let fast_fields_impl = get_fast_fields_impl(fast_fields);
            let columnstore_readers = &fast_fields_impl.columnar;

            let mut unpivots = vec![Vec::new(); unpivot_exprs.len()];

            for (
                i,
                TantivyUnpivotProjectionExpr {
                    unpivot_field,
                    tantivy_field,
                    ..
                },
            ) in unpivot_exprs.iter().enumerate()
            {
                let mut prefix = tantivy_field.top_level_field.clone();
                if matches!(unpivot_field, UnpivotProjectedField::Object { .. }) {
                    prefix.push(JSON_PATH_SEGMENT_SEP as char);
                    prefix.push_str(JSON_ROOT_FIELD);
                }
                let columns = get_columns_matching_prefix(columnstore_readers, &prefix)?;
                debug_assert!(columns.len() > 0);
                for (name, col) in columns {
                    let unpivot_expr = match unpivot_field {
                        UnpivotProjectedField::Array { item } => {
                            UnpivotProjectedFieldWithName::Array {
                                item_name: item.clone(),
                            }
                        }
                        UnpivotProjectedField::Object { key, value } => {
                            let name_piece = name
                                .split(JSON_PATH_SEGMENT_SEP as char)
                                .next_back()
                                .expect("at least one name piece");
                            UnpivotProjectedFieldWithName::Object {
                                key_name: key.clone(),
                                value_name: value.clone(),
                                key_value: name_piece.to_string(),
                            }
                        }
                    };
                    let opened_col = col
                        .open()
                        .map_err(|e| tantivy::TantivyError::IoError(std::sync::Arc::new(e)))?;
                    unpivots[i].push((unpivot_expr, opened_col));
                }
            }

            Ok::<_, tantivy::TantivyError>(unpivots)
        },
    )
}

// This function can be used as a substitute to `first_vals`, when you want to get
// all the values for a field. Unlike `first_vals` which only gets the first value
// per document, this gets all values and returns both the values and a mapping
// of which document each value came from.
pub fn primitive_column_all_vals<T: PartialOrd + Copy + std::fmt::Debug + Send + Sync + 'static>(
    column_handle: &Column<T>,
    docs: &[u32],
) -> (Vec<usize>, Vec<T>) {
    match &column_handle.index {
        ColumnIndex::Empty { .. } => (Vec::new(), Vec::new()),
        ColumnIndex::Full => {
            let mut buffer = vec![None; docs.len()];
            column_handle.values.get_vals_opt(docs, &mut buffer);
            let mut docidx_mapping = Vec::with_capacity(docs.len());
            let mut values = Vec::with_capacity(docs.len());

            for (i, opt_val) in buffer.into_iter().enumerate() {
                if let Some(val) = opt_val {
                    docidx_mapping.push(i);
                    values.push(val);
                }
            }
            (docidx_mapping, values)
        }
        ColumnIndex::Optional(optional_index) => {
            // For both optional and multivalued, we won't necessarily end with at least num docs values,
            // but because batch sizes are only 64, it is more efficient to allocate the doc size up front than
            // to resize the vector as needed.
            let mut docidx_mapping = Vec::with_capacity(docs.len());
            let mut values = Vec::with_capacity(docs.len());

            for (i, docid) in docs.iter().enumerate() {
                if let Some(rowid) = optional_index.rank_if_exists(*docid) {
                    let value = column_handle.values.get_val(rowid);
                    docidx_mapping.push(i);
                    values.push(value);
                }
            }
            (docidx_mapping, values)
        }
        ColumnIndex::Multivalued(multivalued_index) => {
            let mut docidx_mapping = Vec::with_capacity(docs.len());
            let mut values = Vec::with_capacity(docs.len());

            for (i, docid) in docs.iter().enumerate() {
                let range = multivalued_index.range(*docid);
                for j in range.start..range.end {
                    let value = column_handle.values.get_val(j);
                    docidx_mapping.push(i);
                    values.push(value);
                }
            }
            (docidx_mapping, values)
        }
    }
}

// This function is based on https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/sstable/src/dictionary.rs#L324
// and it addresses an issue with how Tantivy accesses columnar strings/bytes. The issue is that `ord_to_term`:
// * Finds the block containing the ordinal (relatively fast)
// * Reads the block via a FileSlice (relatively fast, if cached)
// * Decompresses the block (VERY slow)
// * Decodes the block up to the ordinal (somewhat slow)
//
// Doing this ~20k times is very slow, like 100ms, even for tiny strings like span_id. This function is a batched
// version of that operation, which takes a bunch of ordinals, and skips around to the blocks that contain them,
// knocking away as many ordinals as relevant while decoding a block. Because of how it's written, it should be no slower
// than `ord_to_term` for a single ordinal, and it's much faster (4ms for 20k ordinals) when you're requesting a bunch.
pub fn batch_ordinals_to_buffers(
    ordinals: &mut Vec<u64>,
    scratch_space: &mut Vec<u8>,
    buffers: &mut FastHashMap<u64, Vec<u8>>,
    dictionary: &tantivy::columnar::Dictionary,
) -> std::io::Result<()> {
    // Sort the ordinals in descending order and loop over them backwards. This is preferable to
    // iterating in forwards order because then we'd have to use a VecDeque rather than a Vec.
    ordinals.sort_by(|a, b| b.cmp(a));

    let mut curr_sstable_delta_reader_ordinal = 0;
    let mut curr_sstable_delta_reader = None;

    scratch_space.clear();

    let mut last_seen = None;
    while let Some(ord) = ordinals.last().cloned() {
        if last_seen.is_some() && last_seen.unwrap() == ord {
            ordinals.pop();
            continue;
        }
        last_seen = Some(ord);

        let mut sstable_delta_reader = match curr_sstable_delta_reader {
            Some(sstable_delta_reader) => sstable_delta_reader,
            None => {
                let block_addr = dictionary.sstable_index.get_block_with_ord(ord);
                let first_ordinal = block_addr.first_ordinal;

                scratch_space.clear();
                curr_sstable_delta_reader_ordinal = first_ordinal;

                dictionary.sstable_delta_reader_block(block_addr)?
            }
        };

        // This code resembles the logic in `ord_to_term` (https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/sstable/src/dictionary.rs#L331)
        // which opens a block, and then delta-decodes it until it finds the ordinal.
        //
        // Instead, we proceed from the current ordinal until the the next ordinal, and once we hit that, we save the value and continue.
        // This allows us to avoid re-decoding the block for each ordinal.
        //
        // The innards of the loop are specific to the block's delta encoding. Each call to `advance` progresses to the next string, and the
        // `truncate()`/`extend_from_slice()` calls implement the actual delta decoding.
        let mut exhausted = false;
        while curr_sstable_delta_reader_ordinal <= ord {
            if !sstable_delta_reader.advance()? {
                // We didn't find the ordinal in the current block, so let's null it out
                // and continue
                exhausted = true;
                break;
            }
            scratch_space.truncate(sstable_delta_reader.common_prefix_len());
            scratch_space.extend_from_slice(sstable_delta_reader.suffix());
            curr_sstable_delta_reader_ordinal += 1;
        }

        if exhausted {
            // If we couldn't find the ordinal in the current block, we'll try the next one
            curr_sstable_delta_reader = None;
            continue;
        } else {
            buffers.insert(ord, scratch_space.clone());

            ordinals.pop();
            curr_sstable_delta_reader = Some(sstable_delta_reader);
        }
    }

    Ok(())
}

pub fn batch_ordinals_decompress(
    ordinals: &mut Vec<u64>,
    dictionary: &tantivy::columnar::Dictionary,
) -> std::io::Result<Vec<Vec<u8>>> {
    // Sort the ordinals in descending order and loop over them backwards. This is preferable to
    // iterating in forwards order because then we'd have to use a VecDeque rather than a Vec.
    ordinals.sort_by(|a, b| b.cmp(a));

    let mut curr_sstable_delta_reader_ordinal = 0;
    let mut curr_sstable_delta_reader = None;

    let mut scratch_space = Vec::new();
    let mut result = Vec::new();

    while let Some(ord) = ordinals.last().cloned() {
        let mut sstable_delta_reader = match curr_sstable_delta_reader {
            Some(sstable_delta_reader) => sstable_delta_reader,
            None => {
                let block_addr = dictionary.sstable_index.get_block_with_ord(ord);
                let first_ordinal = block_addr.first_ordinal;

                scratch_space.clear();
                curr_sstable_delta_reader_ordinal = first_ordinal;

                dictionary.sstable_delta_reader_block(block_addr)?
            }
        };

        // This code resembles the logic in `ord_to_term` (https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/sstable/src/dictionary.rs#L331)
        // which opens a block, and then delta-decodes it until it finds the ordinal.
        //
        // Instead, we proceed from the current ordinal until the the next ordinal, and once we hit that, we save the value and continue.
        // This allows us to avoid re-decoding the block for each ordinal.
        //
        // The innards of the loop are specific to the block's delta encoding. Each call to `advance` progresses to the next string, and the
        // `truncate()`/`extend_from_slice()` calls implement the actual delta decoding.
        let mut exhausted = false;
        while curr_sstable_delta_reader_ordinal <= ord {
            if !sstable_delta_reader.advance()? {
                // We didn't find the ordinal in the current block, so let's null it out
                // and continue
                exhausted = true;
                break;
            }
            scratch_space.truncate(sstable_delta_reader.common_prefix_len());
            scratch_space.extend_from_slice(sstable_delta_reader.suffix());
            curr_sstable_delta_reader_ordinal += 1;
        }

        if exhausted {
            // If we couldn't find the ordinal in the current block, we'll try the next one
            curr_sstable_delta_reader = None;
            continue;
        } else {
            result.push(scratch_space.clone());

            ordinals.pop();
            curr_sstable_delta_reader = Some(sstable_delta_reader);
        }
    }

    Ok(result)
}
