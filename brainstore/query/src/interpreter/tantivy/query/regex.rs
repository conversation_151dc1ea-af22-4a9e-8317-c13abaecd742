use std::error::Error;

use btql::interpreter::regex::{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JSON_END_OF_PATH_CHAR, JSON_PATH_SEGMENT_SEP_CHAR,
};
use storage::tantivy_index::JSON_ROOT_FIELD;
use tantivy::query::{AutomatonWeight, Query, Weight};
use tantivy::schema::Field;
use tantivy_fst::Automaton as FstAutomaton;

fn generate_pattern(
    pattern: &str,
    case_insensitive: bool,
    json_path: &[String],
    is_json_field: bool,
) -> String {
    let is_wildcard_search = pattern.starts_with(".*") && pattern.ends_with(".*");
    let end_or_path_sep = format!("[{}{}]", JSON_END_OF_PATH_CHAR, JSON_PATH_SEGMENT_SEP_CHAR);

    let mut path_prefix = String::new();

    if is_json_field {
        path_prefix.push_str(JSON_ROOT_FIELD);

        for part in json_path.iter() {
            path_prefix.push(JSON_PATH_SEGMENT_SEP_CHAR);
            path_prefix.push_str(part);
        }

        // Wildcard searches should be able to match nested paths and also values, so we can see a json path separator or end of path.
        // Otherwise, we need to only search for values at this exact path, so we need to see the end of path marker, followed by 's' denoting a string value.
        if is_wildcard_search {
            path_prefix.push_str(&end_or_path_sep);
        } else {
            path_prefix.push(JSON_END_OF_PATH_CHAR);
            path_prefix.push('s');
        }
    }

    let final_pattern = if case_insensitive {
        format!("(?i){}{}", path_prefix, pattern)
    } else {
        format!("{}{}", path_prefix, pattern)
    };

    final_pattern
}

// Wrapper to implement FstAutomaton for ControlCharWrapper in this crate
#[derive(Clone, Debug)]
pub struct TantivyControlCharWrapper(ControlCharWrapper);

impl TantivyControlCharWrapper {
    pub fn new(pattern: &str) -> Result<Self, Box<dyn Error>> {
        Ok(Self(ControlCharWrapper::new(pattern)?))
    }

    pub fn sanitized_pattern(&self) -> &str {
        self.0.sanitized_pattern()
    }
}

// This automaton is a wrapper around the regex DFA, which handles control characters found in tantivy terms cleanly
impl FstAutomaton for TantivyControlCharWrapper {
    type State = regex_automata::util::primitives::StateID;

    fn start(&self) -> Self::State {
        self.0.start()
    }

    fn is_match(&self, state: &Self::State) -> bool {
        self.0.is_match(state)
    }

    fn can_match(&self, state: &Self::State) -> bool {
        self.0.can_match(state)
    }

    fn will_always_match(&self, state: &Self::State) -> bool {
        self.0.will_always_match(state)
    }

    fn accept(&self, state: &Self::State, byte: u8) -> Self::State {
        self.0.accept(state, byte)
    }
}

#[derive(Clone)]
pub struct RegexQuery {
    field: Field,
    automaton: TantivyControlCharWrapper,
}

// Creates a regex query from a field and a pattern. This acts as a wrapper around a RegexAutomaton, which we need because
// Regex does not natively support control characters.
impl RegexQuery {
    pub fn new(
        field: Field,
        pattern: &str,
        case_insensitive: bool,
        json_path: &[String],
        is_json_field: bool,
    ) -> Result<Self, Box<dyn Error>> {
        let normalized_pattern =
            generate_pattern(pattern, case_insensitive, json_path, is_json_field);
        let automaton = TantivyControlCharWrapper::new(&normalized_pattern)?;
        Ok(Self { field, automaton })
    }
}

impl Query for RegexQuery {
    fn weight(
        &self,
        _enable_scoring: tantivy::query::EnableScoring,
    ) -> tantivy::Result<Box<dyn Weight>> {
        let weight = AutomatonWeight::new(self.field, self.automaton.clone());
        Ok(Box::new(weight))
    }

    fn query_terms<'a>(&'a self, _visitor: &mut dyn FnMut(&'a tantivy::Term, bool)) {
        // No specific terms to extract for automaton queries
    }
}

impl std::fmt::Debug for RegexQuery {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "RegexQuery ({})", self.automaton.sanitized_pattern())
    }
}
