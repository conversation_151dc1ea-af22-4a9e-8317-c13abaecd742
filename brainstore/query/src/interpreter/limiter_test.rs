#[cfg(test)]
mod project_batch_repro {
    use crate::interpreter::limiter::project_batch;
    use btql::binder::ast::<PERSON><PERSON>;
    use btql::interpreter::context::ExprContext;
    use serde::Deserialize;
    use std::borrow::Cow;
    use std::fs;
    use std::time::Instant;
    use util::Value;

    #[derive(Deserialize)]
    struct ProjectBatchArgs {
        projection: Vec<Alias>,
        rows: Vec<Value>,
    }

    #[test]
    #[ignore]
    fn test_project_batch_perf() {
        /*
        Paste this code into `project_batch` (limiter.rs) to generate a repro file:
        {
            use std::fs;
            let export = serde_json::json!({
                "projection": projection,
                "rows": rows.iter().map(|r| r.as_ref()).collect::<Vec<_>>(),
            });
            fs::write(
                "project_batch_repro.json",
                serde_json::to_string_pretty(&export).unwrap(),
            ).expect("Failed to write repro file");
        }
        */
        let ctx = ExprContext::new(None);
        let num_copies = 100;
        let file_contents =
            fs::read_to_string("project_batch_repro.json").expect("Failed to read repro file");

        let args: ProjectBatchArgs =
            serde_json::from_str(&file_contents).expect("Failed to parse repro file");

        let rows: Vec<Cow<Value>> =
            std::iter::repeat(args.rows.iter().map(|v| Cow::Owned(v.clone())))
                .take(num_copies)
                .flatten()
                .collect();

        let start = Instant::now();
        let _result = project_batch(&ctx, &args.projection, rows);
        let elapsed = start.elapsed();

        println!("Total elapsed time ({} copies): {:?}", num_copies, elapsed);
    }
}
