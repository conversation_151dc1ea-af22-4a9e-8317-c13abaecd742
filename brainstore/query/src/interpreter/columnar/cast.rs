use btql::typesystem::CastInto;
use util::xact::PaginationKey;

use super::{
    value::{BytesOrdinal, ColumnarExprContext, Null, StringOrdinal},
    PtrOffset,
};

pub trait ColumnarCast<T> {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<T>;
}

pub trait CastableColumnarType:
    std::fmt::Debug
    + ColumnarCast<Null>
    + ColumnarCast<bool>
    + ColumnarCast<u64>
    + ColumnarCast<PaginationKey>
    + ColumnarCast<i64>
    + ColumnarCast<f64>
    + ColumnarCast<tantivy::DateTime>
    + ColumnarCast<PtrOffset>
{
}

impl CastableColumnarType for Null {}

impl<F, T> ColumnarCast<T> for F
where
    F: CastInto<T>,
{
    fn columnar_cast(&self, _ctx: &mut ColumnarExprContext) -> Option<T> {
        self.cast().ok()
    }
}

macro_rules! impl_basic_casts {
    ($($t:ty),*) => {
        $(
            impl ColumnarCast<$t> for Null {
                fn columnar_cast(&self, _ctx: &mut ColumnarExprContext) -> Option<$t> {
                    None
                }
            }
            impl ColumnarCast<Null> for $t {
                fn columnar_cast(&self, _ctx: &mut ColumnarExprContext) -> Option<Null> {
                    None
                }
            }

            impl CastableColumnarType for $t {}
        )*
    };
}

impl_basic_casts!(
    bool,
    u64,
    i64,
    PaginationKey,
    f64,
    tantivy::DateTime,
    PtrOffset,
    StringOrdinal,
    BytesOrdinal
);

macro_rules! impl_string_casts {
    ($($t:ty),*) => {
        $(
            impl ColumnarCast<$t> for StringOrdinal {
                fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<$t> {
                    let s = ctx.get_string_from_ordinal(self);
                    s.parse().ok()
                }
            }

            impl ColumnarCast<$t> for PtrOffset {
                fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<$t> {
                    let s = self.as_str(ctx);
                    s.parse().ok()
                }
            }

            impl ColumnarCast<$t> for BytesOrdinal {
                fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<$t> {
                    let s = ctx.get_bytes_from_ordinal(self);
                    let s = match std::str::from_utf8(s) {
                        Ok(s) => s,
                        Err(_) => return None
                    };
                    s.parse().ok()
                }
            }

            impl ColumnarCast<PtrOffset> for $t {
                fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<PtrOffset> {
                    Some(ctx.insert_value_as_expr_string(self))
                }
            }
        )*
    };
}

impl_string_casts!(bool, u64, i64, f64, PaginationKey);

impl ColumnarCast<PtrOffset> for tantivy::DateTime {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<PtrOffset> {
        ctx.insert_datetime_string(self)
    }
}

impl ColumnarCast<PtrOffset> for StringOrdinal {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<PtrOffset> {
        Some(ctx.string_ordinal_to_ptr_offset(*self))
    }
}

impl ColumnarCast<PtrOffset> for BytesOrdinal {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<PtrOffset> {
        ctx.bytes_ordinal_to_ptr_offset(*self)
    }
}

impl ColumnarCast<tantivy::DateTime> for StringOrdinal {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<tantivy::DateTime> {
        let s = ctx.get_string_from_ordinal(self);
        s.cast().ok()
    }
}

impl ColumnarCast<tantivy::DateTime> for PtrOffset {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<tantivy::DateTime> {
        let s = self.as_str(ctx);
        s.cast().ok()
    }
}

impl ColumnarCast<tantivy::DateTime> for BytesOrdinal {
    fn columnar_cast(&self, ctx: &mut ColumnarExprContext) -> Option<tantivy::DateTime> {
        let s = ctx.get_bytes_from_ordinal(self);
        let s = match std::str::from_utf8(s) {
            Ok(s) => s,
            Err(_) => return None,
        };
        s.cast().ok()
    }
}
