use tantivy::COLLECT_BLOCK_BUFFER_LEN;
use util::ptree::MakePTree;

use crate::{dispatch_literal_visitor, interpreter::columnar::visitor::ColumnarVisitor};

use super::{value::ColumnarExpr<PERSON><PERSON>x<PERSON>, ColumnarExpr, ColumnarValueType, OpaqueBuffer};

#[derive(Debu<PERSON>, C<PERSON>, MakePTree)]
pub struct Ternary {
    pub conds: Vec<ColumnarCond>,
    pub else_expr: Box<ColumnarExpr>,
    pub value_type: super::value::ColumnarValueType,

    #[ptree(skip)]
    pub cond_bufs: Vec<OpaqueBuffer>,
    #[ptree(skip)]
    pub then_bufs: Vec<OpaqueBuffer>,
    #[ptree(skip)]
    pub else_buf: OpaqueBuffer,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
    #[ptree(skip)]
    pub mask: Vec<bool>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, MakePTree)]
pub struct ColumnarCond {
    pub cond: Box<ColumnarExpr>,
    pub then: Box<ColumnarExpr>,
}

impl Ternary {
    pub fn new(
        conds: Vec<ColumnarCond>,
        else_expr: ColumnarExpr,
        value_type: ColumnarValueType,
    ) -> Self {
        Self {
            cond_bufs: conds
                .iter()
                .map(|_| OpaqueBuffer::new(ColumnarValueType::Bool))
                .collect(),
            then_bufs: conds
                .iter()
                .map(|c| OpaqueBuffer::new(c.then.columnar_type()))
                .collect(),
            else_buf: OpaqueBuffer::new(else_expr.columnar_type()),
            buf: OpaqueBuffer::new(value_type),
            mask: vec![false; COLLECT_BLOCK_BUFFER_LEN], // Initialize with an empty mask, will be resized later

            conds,
            else_expr: Box::new(else_expr),
            value_type,
        }
    }

    pub fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &super::expr::BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        struct ValueVisitor<'a> {
            ternary: &'a mut Ternary,
            ctx: &'a mut ColumnarExprContext,
            columns: &'a super::expr::BatchColumns,
            docs: &'a [tantivy::DocId],
        }

        impl ColumnarVisitor<tantivy::Result<()>> for ValueVisitor<'_> {
            fn handle_default<T: super::value::PrimitiveColumnarType>(
                &mut self,
            ) -> tantivy::Result<()> {
                self.ternary
                    .interpret_batch_for_value::<T>(self.ctx, &self.columns, self.docs)
            }

            fn handle_dynamic(&mut self) -> tantivy::Result<()> {
                panic!(
                    "Dynamic types should have been resolved before running a ternary operation"
                );
            }
        }

        dispatch_literal_visitor!(
            ValueVisitor {
                ternary: self,
                ctx,
                columns,
                docs
            },
            self.value_type
        )?;

        Ok(&mut self.buf)
    }

    pub fn interpret_batch_for_value<T: super::value::PrimitiveColumnarType>(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &super::expr::BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<(), tantivy::TantivyError> {
        self.mask.resize(docs.len(), false);
        self.buf.resize::<Option<T>>(docs.len());

        let out_buf = self.buf.as_mut_slice::<Option<T>>();
        for i in 0..docs.len() {
            self.mask[i] = false; // Initialize mask to false
            out_buf[i] = None; // Initialize output buffer to None
        }

        for cond in self.conds.iter_mut() {
            let cond_bools = cond.cond.interpret_batch(ctx, columns, docs)?;
            let then_vals = cond.then.interpret_batch(ctx, columns, docs)?;

            let cond_buf = cond_bools.as_slice::<Option<bool>>();
            for doc_idx in 0..docs.len() {
                if self.mask[doc_idx] {
                    continue; // Skip if already set
                } else if let Some(true) = cond_buf[doc_idx] {
                    self.mask[doc_idx] = true; // Mark as set

                    // then_vals is either null or the same type as T
                    match then_vals.columnar_type() {
                        ColumnarValueType::Null => {
                            out_buf[doc_idx] = None; // Set to None if then is null
                        }
                        t if t == T::columnar_type() => {
                            // Safe to cast since we know T matches the type of then_vals
                            out_buf[doc_idx] = then_vals.as_slice::<Option<T>>()[doc_idx].clone();
                        }
                        _ => {
                            return Err(tantivy::TantivyError::InvalidArgument(format!(
                                "Type mismatch: expected {:?}, got {:?}",
                                T::columnar_type(),
                                then_vals.columnar_type()
                            )));
                        }
                    }
                }
            }
        }

        for doc_idx in 0..docs.len() {
            if !self.mask[doc_idx] {
                // If not set, evaluate the else expression
                let else_buf = self.else_expr.interpret_batch(ctx, columns, docs)?;
                match else_buf.columnar_type() {
                    ColumnarValueType::Null => {
                        out_buf[doc_idx] = None; // Set to None if else is null
                    }
                    t if t == T::columnar_type() => {
                        // Safe to cast since we know T matches the type of else_buf
                        out_buf[doc_idx] = else_buf.as_slice::<Option<T>>()[doc_idx].clone();
                    }
                    _ => {
                        return Err(tantivy::TantivyError::InvalidArgument(format!(
                            "Type mismatch: expected {:?}, got {:?}",
                            T::columnar_type(),
                            else_buf.columnar_type()
                        )));
                    }
                }
            }
        }

        Ok(())
    }
}
