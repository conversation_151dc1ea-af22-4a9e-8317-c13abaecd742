use tantivy::DateTime;
use util::xact::PaginationKey;

use super::{
    value::{BytesOrdinal, Null, StringOrdinal},
    PrimitiveColumnarType, PtrOffset,
};

pub trait ColumnarVisitor<R> {
    // The user MUST implement this method. It's the "default" case.
    fn handle_default<T: PrimitiveColumnarType>(&mut self) -> R;

    // Dynamic must also be handled separately.
    fn handle_dynamic(&mut self) -> R;

    // Every other method is optional and defaults to the fallback.
    fn handle_null(&mut self) -> R {
        self.handle_default::<Null>()
    }
    fn handle_bool(&mut self) -> R {
        self.handle_default::<bool>()
    }
    fn handle_i64(&mut self) -> R {
        self.handle_default::<i64>()
    }
    fn handle_u64(&mut self) -> R {
        self.handle_default::<u64>()
    }
    fn handle_pagination_key(&mut self) -> R {
        self.handle_default::<PaginationKey>()
    }
    fn handle_f64(&mut self) -> R {
        self.handle_default::<f64>()
    }
    fn handle_datetime(&mut self) -> R {
        self.handle_default::<DateTime>()
    }
    fn handle_string_ptr(&mut self) -> R {
        self.handle_default::<PtrOffset>()
    }
    fn handle_string_ord(&mut self) -> R {
        self.handle_default::<StringOrdinal>()
    }
    fn handle_bytes_ord(&mut self) -> R {
        self.handle_default::<BytesOrdinal>()
    }
}

#[macro_export]
macro_rules! dispatch_literal_visitor {
    ($visitor:expr, $value_type:expr $(,)?) => {
        match $value_type {
            ColumnarValueType::Null => $visitor.handle_null(),
            ColumnarValueType::Bool => $visitor.handle_bool(),
            ColumnarValueType::I64 => $visitor.handle_i64(),
            ColumnarValueType::U64 => $visitor.handle_u64(),
            ColumnarValueType::PaginationKey => $visitor.handle_pagination_key(),
            ColumnarValueType::F64 => $visitor.handle_f64(),
            ColumnarValueType::DateTime => $visitor.handle_datetime(),
            ColumnarValueType::StringPtr => $visitor.handle_string_ptr(),
            ColumnarValueType::StringOrdinal => $visitor.handle_string_ord(),
            ColumnarValueType::BytesOrdinal => $visitor.handle_bytes_ord(),
            ColumnarValueType::Dynamic => $visitor.handle_dynamic(),
        }
    };
}
