use btql::binder::ast::ArithmeticOp;
use itertools::izip;
use util::ptree::MakePTree;

use crate::{
    dispatch_literal_visitor,
    interpreter::columnar::{visitor::ColumnarVisitor, PrimitiveColumnarType},
};

use super::{
    expr::BatchColumns, value::ColumnarExprContext, ColumnarExpr, ColumnarValueType, OpaqueBuffer,
};

#[derive(Debug, <PERSON><PERSON>, MakePTree)]
pub struct Arithmetic {
    pub op: ArithmeticOp,
    pub left: Box<ColumnarExpr>,
    pub right: Box<ColumnarExpr>,
    pub value_type: ColumnarValueType,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
}

impl Arithmetic {
    pub fn new(
        op: ArithmeticOp,
        left: ColumnarExpr,
        right: ColumnarExpr,
        value_type: ColumnarValueType,
    ) -> Self {
        Self {
            op,
            left: Box::new(left),
            right: Box::new(right),
            value_type,
            buf: OpaqueBuffer::new(value_type),
        }
    }

    pub fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        pub struct ValueVisitor<'a> {
            arithmetic: &'a mut Arithmetic,
            ctx: &'a mut ColumnarExprContext,
            columns: &'a BatchColumns,
            docs: &'a [tantivy::DocId],
        }
        impl ColumnarVisitor<tantivy::Result<()>> for ValueVisitor<'_> {
            fn handle_default<T: PrimitiveColumnarType>(&mut self) -> tantivy::Result<()> {
                self.arithmetic
                    .interpret_batch_for_value::<T>(self.ctx, &self.columns, self.docs)
            }

            fn handle_dynamic(&mut self) -> tantivy::Result<()> {
                panic!("Dynamic types should have been resolved before running an arithmetic operation");
            }
        }
        dispatch_literal_visitor!(
            ValueVisitor {
                arithmetic: self,
                ctx,
                columns,
                docs
            },
            self.value_type
        )?;

        Ok(&mut self.buf)
    }

    pub fn interpret_batch_for_value<T: PrimitiveColumnarType>(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<(), tantivy::TantivyError> {
        let left_buf = self.left.interpret_batch(ctx, columns, docs)?;
        let right_buf = self.right.interpret_batch(ctx, columns, docs)?;

        self.buf.resize::<Option<T>>(docs.len());

        match (
            &self.op,
            left_buf.columnar_type(),
            right_buf.columnar_type(),
        ) {
            (_, ColumnarValueType::Null, _) | (_, _, ColumnarValueType::Null) => {
                let out_buf = self.buf.as_mut_slice::<Option<T>>();
                for ptr in out_buf {
                    *ptr = None;
                }
            }
            (ArithmeticOp::Add, ColumnarValueType::I64, ColumnarValueType::I64) => {
                ColumnarArithmetic::add(
                    left_buf.as_slice::<Option<i64>>(),
                    right_buf.as_slice::<Option<i64>>(),
                    self.buf.as_mut_slice::<Option<i64>>(),
                );
            }
            (ArithmeticOp::Sub, ColumnarValueType::I64, ColumnarValueType::I64) => {
                ColumnarArithmetic::sub(
                    left_buf.as_slice::<Option<i64>>(),
                    right_buf.as_slice::<Option<i64>>(),
                    self.buf.as_mut_slice::<Option<i64>>(),
                );
            }
            (ArithmeticOp::Add, ColumnarValueType::U64, ColumnarValueType::U64) => {
                ColumnarArithmetic::add(
                    left_buf.as_slice::<Option<u64>>(),
                    right_buf.as_slice::<Option<u64>>(),
                    self.buf.as_mut_slice::<Option<u64>>(),
                );
            }
            (ArithmeticOp::Sub, ColumnarValueType::U64, ColumnarValueType::U64) => {
                ColumnarArithmetic::sub(
                    left_buf.as_slice::<Option<u64>>(),
                    right_buf.as_slice::<Option<u64>>(),
                    self.buf.as_mut_slice::<Option<i64>>(),
                );
            }
            (ArithmeticOp::Add, ColumnarValueType::F64, ColumnarValueType::F64) => {
                ColumnarArithmetic::add(
                    left_buf.as_slice::<Option<f64>>(),
                    right_buf.as_slice::<Option<f64>>(),
                    self.buf.as_mut_slice::<Option<f64>>(),
                );
            }
            (ArithmeticOp::Sub, ColumnarValueType::F64, ColumnarValueType::F64) => {
                ColumnarArithmetic::sub(
                    left_buf.as_slice::<Option<f64>>(),
                    right_buf.as_slice::<Option<f64>>(),
                    self.buf.as_mut_slice::<Option<f64>>(),
                );
            }
            _ => {
                panic!(
                    "Unsupported arithmetic operation {:?} for types: {:?} and {:?}",
                    self.op,
                    left_buf.columnar_type(),
                    right_buf.columnar_type()
                );
            }
        }

        Ok(())
    }
}

pub trait ColumnarArithmetic: Sized {
    // Subtractions can produce a different type than the original, eg two u64s produce an i64
    type SubTarget: PrimitiveColumnarType;

    fn add(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self>]);
    fn sub(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self::SubTarget>]);
}

impl ColumnarArithmetic for u64 {
    type SubTarget = i64;

    fn add(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self>]) {
        for (l, r, o) in izip!(left, right, out) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(l + r),
                _ => None,
            };
        }
    }

    fn sub(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self::SubTarget>]) {
        for (l, r, o) in izip!(left, right, out) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some((*l as i64) - (*r as i64)),
                _ => None,
            };
        }
    }
}

impl ColumnarArithmetic for i64 {
    type SubTarget = i64;

    fn add(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self>]) {
        for (l, r, o) in izip!(left, right, out) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(l + r),
                _ => None,
            };
        }
    }

    fn sub(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self::SubTarget>]) {
        for (l, r, o) in izip!(left, right, out) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(l - r),
                _ => None,
            };
        }
    }
}

impl ColumnarArithmetic for f64 {
    type SubTarget = f64;

    fn add(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self>]) {
        for (l, r, o) in izip!(left, right, out) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(l + r),
                _ => None,
            };
        }
    }

    fn sub(left: &[Option<Self>], right: &[Option<Self>], out: &mut [Option<Self::SubTarget>]) {
        for (l, r, o) in izip!(left, right, out) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(l - r),
                _ => None,
            };
        }
    }
}
