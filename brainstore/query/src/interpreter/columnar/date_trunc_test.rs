#[cfg(test)]
mod tests {
    use super::super::expr::{NANOS_PER_DAY, NANOS_PER_HOUR, NANOS_PER_MINUTE, NANOS_PER_SECOND};

    #[test]
    fn test_truncation_constants() {
        // Verify our constants are correct
        assert_eq!(NANOS_PER_SECOND, 1_000_000_000);
        assert_eq!(NANOS_PER_MINUTE, 60 * 1_000_000_000);
        assert_eq!(NANOS_PER_HOUR, 60 * 60 * 1_000_000_000);
        assert_eq!(NANOS_PER_DAY, 24 * 60 * 60 * 1_000_000_000);
    }

    #[test]
    fn test_modulo_truncation_logic() {
        // Test the core modulo truncation logic used for second/minute/hour/day

        // Example timestamp: 2024-03-15 14:35:42.123456789
        let timestamp_nanos: i64 = 1710513342123456789;

        // Test second truncation (should zero out nanoseconds)
        let second_truncated = (timestamp_nanos / NANOS_PER_SECOND) * NANOS_PER_SECOND;
        assert_eq!(second_truncated % NANOS_PER_SECOND, 0);
        assert_eq!(second_truncated, 1710513342000000000);

        // Test minute truncation (should zero out seconds and nanoseconds)
        let minute_truncated = (timestamp_nanos / NANOS_PER_MINUTE) * NANOS_PER_MINUTE;
        assert_eq!(minute_truncated % NANOS_PER_MINUTE, 0);
        assert_eq!(minute_truncated, 1710513300000000000);

        // Test hour truncation (should zero out minutes, seconds and nanoseconds)
        let hour_truncated = (timestamp_nanos / NANOS_PER_HOUR) * NANOS_PER_HOUR;
        assert_eq!(hour_truncated % NANOS_PER_HOUR, 0);
        assert_eq!(hour_truncated, 1710511200000000000);

        // Test day truncation
        let day_truncated = (timestamp_nanos / NANOS_PER_DAY) * NANOS_PER_DAY;
        assert_eq!(day_truncated % NANOS_PER_DAY, 0);
    }

    #[test]
    fn test_timezone_offset_logic() {
        // Test the timezone offset logic for modulo-based truncation

        // Example: 2024-03-15 00:30:00 UTC
        let utc_timestamp_nanos: i64 = 1710460800000000000;

        // With +300 minute offset (UTC-5), the local time is 2024-03-14 19:00:00
        let tz_offset_minutes: i16 = 300;
        let offset_nanos = (tz_offset_minutes as i64) * NANOS_PER_MINUTE;

        // Step 1: Convert to local time
        let local_nanos = utc_timestamp_nanos - offset_nanos;

        // Step 2: Truncate to day in local time
        let truncated_local = (local_nanos / NANOS_PER_DAY) * NANOS_PER_DAY;

        // Step 3: Convert back to UTC
        let truncated_utc = truncated_local + offset_nanos;

        // The result should be 2024-03-14 05:00:00 UTC (which is midnight in UTC-5)
        // Let's verify the hour is 5
        let hours_since_day = (truncated_utc % NANOS_PER_DAY) / NANOS_PER_HOUR;
        assert_eq!(hours_since_day, 5);
    }

    #[test]
    fn test_week_truncation_logic() {
        // Test that week truncation correctly identifies Monday
        // Using the time crate's weekday logic

        // Example: Wednesday (2024-03-20) should have days_since_monday = 2
        // Monday is 2024-03-18

        // For this test, we'll just verify the math:
        // If today is Wednesday (2 days since Monday), subtracting 2 days gets us to Monday
        let days_since_monday = 2;
        let wednesday_day_number = 20;
        let monday_day_number = wednesday_day_number - days_since_monday;
        assert_eq!(monday_day_number, 18);
    }
}
