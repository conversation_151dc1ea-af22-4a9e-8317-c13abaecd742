use btql::binder::ast::ComparisonOp;
use itertools::izip;
use util::ptree::MakePTree;

use crate::{
    dispatch_literal_visitor,
    interpreter::columnar::{visitor::ColumnarVisitor, PrimitiveColumnarType},
};

use super::{
    expr::BatchColumns,
    value::{ColumnarExprContext, StringOrdinal},
    ColumnarExpr, ColumnarValueType, OpaqueBuffer, PtrOffset,
};

#[derive(Debug, <PERSON><PERSON>, MakePTree)]
pub struct Comparison {
    pub op: ComparisonOp,
    pub left: Box<ColumnarExpr>,
    pub right: Box<ColumnarExpr>,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
}

pub type ComparisonOutputType = bool;

impl Comparison {
    pub fn new(op: ComparisonOp, left: ColumnarExpr, right: ColumnarExpr) -> Self {
        Self {
            op,
            left: Box::new(left),
            right: Box::new(right),
            buf: OpaqueBuffer::new(ComparisonOutputType::columnar_type()),
        }
    }

    pub fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        let left_buf = self.left.interpret_batch(ctx, columns, docs)?;
        let right_buf = self.right.interpret_batch(ctx, columns, docs)?;

        self.buf.resize::<Option<ComparisonOutputType>>(docs.len());
        let out_buf = self.buf.as_mut_slice::<Option<ComparisonOutputType>>();

        let left_type = left_buf.columnar_type();
        let right_type = right_buf.columnar_type();

        match (left_type, right_type) {
            (ColumnarValueType::Null, _) | (_, ColumnarValueType::Null) => {
                for o in out_buf.iter_mut() {
                    *o = None; // If both sides are null, the result is null
                }
                return Ok(&mut self.buf);
            }
            (ColumnarValueType::Dynamic, _) | (_, ColumnarValueType::Dynamic) => {
                return Err(tantivy::TantivyError::InvalidArgument(
                    "Dynamic types should have been resolved before running a comparison operation"
                        .to_string(),
                ));
            }
            (left_type, right_type) if left_type == right_type => {}
            (ColumnarValueType::StringPtr, ColumnarValueType::StringOrdinal)
            | (ColumnarValueType::StringOrdinal, ColumnarValueType::StringPtr) => {
                // Allow comparison between StringPtr and StringOrdinal
            }
            _ => {
                return Err(tantivy::TantivyError::InternalError(format!(
                    "Left and right buffers must have the same columnar type for comparison operations, got {:?} and {:?}",
                    left_type, right_type
                )));
            }
        };

        pub struct ValueVisitor<'a> {
            op: ComparisonOp,
            ctx: &'a ColumnarExprContext,
            left: &'a OpaqueBuffer,
            right: &'a OpaqueBuffer,
            out: &'a mut [Option<ComparisonOutputType>],
        }
        impl ColumnarVisitor<tantivy::Result<()>> for ValueVisitor<'_> {
            fn handle_default<T: PrimitiveColumnarType>(&mut self) -> tantivy::Result<()> {
                match self.op {
                    ComparisonOp::Eq => {
                        Comparison::interpret_eq::<T, T>(self.ctx, self.left, self.right, self.out)
                    }
                    ComparisonOp::Ne => {
                        Comparison::interpret_ne::<T, T>(self.ctx, self.left, self.right, self.out)
                    }
                    _ => Err(tantivy::TantivyError::InvalidArgument(format!(
                        "Unsupported comparison operation: {:?} for type {:?}",
                        self.op,
                        T::columnar_type()
                    ))),
                }
            }

            fn handle_string_ptr(&mut self) -> tantivy::Result<()> {
                match (self.op, self.right.columnar_type()) {
                    (ComparisonOp::Eq, ColumnarValueType::StringOrdinal) => {
                        Comparison::interpret_eq::<PtrOffset, StringOrdinal>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    (ComparisonOp::Ne, ColumnarValueType::StringOrdinal) => {
                        Comparison::interpret_ne::<PtrOffset, StringOrdinal>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    (ComparisonOp::Eq, ColumnarValueType::StringPtr) => {
                        Comparison::interpret_eq::<PtrOffset, PtrOffset>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    (ComparisonOp::Ne, ColumnarValueType::StringPtr) => {
                        Comparison::interpret_ne::<PtrOffset, PtrOffset>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    _ => Err(tantivy::TantivyError::InvalidArgument(format!(
                        "Unsupported comparison operation: {:?} for type StringPtr",
                        self.op
                    ))),
                }
            }

            fn handle_string_ord(&mut self) -> tantivy::Result<()> {
                match (self.op, self.right.columnar_type()) {
                    (ComparisonOp::Eq, ColumnarValueType::StringPtr) => {
                        Comparison::interpret_eq::<StringOrdinal, PtrOffset>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    (ComparisonOp::Ne, ColumnarValueType::StringPtr) => {
                        Comparison::interpret_ne::<StringOrdinal, PtrOffset>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    (ComparisonOp::Eq, ColumnarValueType::StringOrdinal) => {
                        Comparison::interpret_eq::<StringOrdinal, StringOrdinal>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    (ComparisonOp::Ne, ColumnarValueType::StringOrdinal) => {
                        Comparison::interpret_ne::<StringOrdinal, StringOrdinal>(
                            self.ctx, self.left, self.right, self.out,
                        )
                    }
                    _ => Err(tantivy::TantivyError::InvalidArgument(format!(
                        "Unsupported comparison operation: {:?} for type StringPtr",
                        self.op
                    ))),
                }
            }

            fn handle_dynamic(&mut self) -> tantivy::Result<()> {
                panic!("Dynamic types should have been resolved before running an arithmetic operation");
            }
        }
        dispatch_literal_visitor!(
            ValueVisitor {
                ctx,
                op: self.op,
                left: left_buf,
                right: right_buf,
                out: out_buf,
            },
            left_type,
        )?;

        Ok(&mut self.buf)
    }

    pub fn interpret_eq<L, R>(
        ctx: &ColumnarExprContext,
        left: &OpaqueBuffer,
        right: &OpaqueBuffer,
        out: &mut [Option<ComparisonOutputType>],
    ) -> Result<(), tantivy::TantivyError>
    where
        L: PrimitiveColumnarType + ColumnarComparison<R>,
        R: PrimitiveColumnarType,
    {
        for (l, r, o) in izip!(
            left.as_slice::<Option<L>>(),
            right.as_slice::<Option<R>>(),
            out
        ) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(ColumnarComparison::eq(l, r, ctx)),
                _ => None,
            };
        }

        Ok(())
    }

    pub fn interpret_ne<L, R>(
        ctx: &ColumnarExprContext,
        left: &OpaqueBuffer,
        right: &OpaqueBuffer,
        out: &mut [Option<ComparisonOutputType>],
    ) -> Result<(), tantivy::TantivyError>
    where
        L: PrimitiveColumnarType + ColumnarComparison<R>,
        R: PrimitiveColumnarType,
    {
        for (l, r, o) in izip!(
            left.as_slice::<Option<L>>(),
            right.as_slice::<Option<R>>(),
            out
        ) {
            *o = match (l, r) {
                (Some(l), Some(r)) => Some(ColumnarComparison::ne(l, r, ctx)),
                _ => None,
            };
        }

        Ok(())
    }
}

pub trait ColumnarComparison<With>: Sized {
    fn eq(&self, other: &With, ctx: &ColumnarExprContext) -> bool;
    fn ne(&self, other: &With, ctx: &ColumnarExprContext) -> bool;
}

impl<T: PrimitiveColumnarType> ColumnarComparison<T> for T {
    #[inline(always)]
    fn eq(&self, other: &T, _ctx: &ColumnarExprContext) -> bool {
        self == other
    }

    #[inline(always)]
    fn ne(&self, other: &T, _ctx: &ColumnarExprContext) -> bool {
        self != other
    }
}

impl ColumnarComparison<PtrOffset> for StringOrdinal {
    #[inline(always)]
    fn eq(&self, other: &PtrOffset, ctx: &ColumnarExprContext) -> bool {
        ctx.get_string_from_ordinal(self) == ctx.get_string(other)
    }

    #[inline(always)]
    fn ne(&self, other: &PtrOffset, ctx: &ColumnarExprContext) -> bool {
        ctx.get_string_from_ordinal(self) != ctx.get_string(other)
    }
}

impl ColumnarComparison<StringOrdinal> for PtrOffset {
    #[inline(always)]
    fn eq(&self, other: &StringOrdinal, ctx: &ColumnarExprContext) -> bool {
        ctx.get_string(self) == ctx.get_string_from_ordinal(other)
    }

    #[inline(always)]
    fn ne(&self, other: &StringOrdinal, ctx: &ColumnarExprContext) -> bool {
        ctx.get_string(self) != ctx.get_string_from_ordinal(other)
    }
}
