use std::{collections::HashMap, sync::Arc};

use tantivy::DateTime;
use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::DynamicColumn,
    SegmentReader,
};
use util::xact::PaginationKey;
use util::{
    tracer::{trace_if, EnterTraceGuard, TracedNode},
    Value,
};

use crate::interpreter::aggregator::aggregate::AggregatorBase;
use crate::interpreter::tantivy::aggregate::{
    open_columnstore_exists_path_readers, UnpivotColumnarInfo,
};
use crate::{
    interpreter::columnar::{
        expr::{ColumnarExpr, OpaqueBuffer},
        value::OwnedColumnarType,
    },
    interpreter::{
        aggregator::{columnar_aggregator::ColumnarAggregator, StaticAggregator, ValueAggregator},
        InterpreterContext,
    },
    optimizer::ast::{TantivyProjectedField, TantivyUnpivotProjectionExpr},
};

use crate::interpreter::tantivy::{
    aggregate::split_projected_fields_for_columnstore, search::open_columnstore_field_readers,
};

use super::hash_map::{Entry, FastHashMap, RawEntryMut};
use super::value::{BytesOrdinal, ColumnarExprContext, ColumnarHashable, Null, StringOrdinal};
use super::{ColumnarValueType, PrimitiveColumnarType, PtrOffset};
use crate::interpreter::tantivy::columnstore::open_unpivot_readers;
use std::hash::{BuildHasher, Hasher};

pub struct NoDimsCollector {
    ctx: Arc<InterpreterContext>,
    tracer: Option<Arc<TracedNode>>,
    projection: Vec<TantivyProjectedField>,
    aggs: Vec<ColumnarAggregator>,
    unpivot: Vec<TantivyUnpivotProjectionExpr>,
}

impl NoDimsCollector {
    pub fn new(
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        projection: Vec<TantivyProjectedField>,
        aggs: Vec<ColumnarAggregator>,
        unpivot: Vec<TantivyUnpivotProjectionExpr>,
    ) -> Self {
        Self {
            ctx,
            tracer,
            projection,
            aggs,
            unpivot,
        }
    }
}

impl Collector for NoDimsCollector {
    type Fruit = HashMap<Vec<Value>, Vec<ValueAggregator>>;
    type Child = FallibleSegmentCollector<NoDimsSegmentCollector>;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let (columns, unpivot_fields) = derive_columns(
            &self.projection,
            &self.unpivot,
            segment_reader,
            self.tracer.clone(),
        )?;

        let ctx = ColumnarExprContext::from_interpreter_ctx(&self.ctx);
        Ok(FallibleSegmentCollector::new(
            NoDimsSegmentCollector {
                ctx,
                aggs: make_static_aggregators(&self.aggs, &columns, &unpivot_fields)?,
                columns,
                unpivot_fields,
                tracer: self.tracer.clone(),
            },
            self.tracer
                .as_ref()
                .map(|tracer| tracer.new_child("Segment collector")),
        ))
    }

    fn merge_fruits(
        &self,
        segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        trace_if(log::Level::Info, &self.tracer, "Merge buckets", |_child| {
            let mut segment_fruits_iter = segment_fruits.into_iter();
            let mut base = match segment_fruits_iter.next() {
                Some(v) => v?
                    .into_iter()
                    .map(|a| a.into_value_aggregator())
                    .collect::<Vec<_>>(),
                None => return Ok(HashMap::new()),
            };

            for fruit in segment_fruits_iter {
                for (base_agg, fruit_agg) in base.iter_mut().zip(fruit?) {
                    base_agg
                        .combine(fruit_agg.into_value_aggregator())
                        .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
                }
            }
            let result = HashMap::from([(vec![], (base.into_iter().collect()))]);
            Ok(result)
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}
pub struct NoDimsSegmentCollector {
    ctx: ColumnarExprContext,
    columns: Vec<Option<DynamicColumn>>,
    aggs: Vec<(ColumnarExpr, StaticAggregator)>,
    unpivot_fields: Vec<Vec<UnpivotColumnarInfo>>,
    tracer: Option<Arc<TracedNode>>,
}

impl FallibleCollector for NoDimsSegmentCollector {
    type Fruit = Vec<StaticAggregator>;

    fn collect_block_fallible(
        &mut self,
        docs: &[tantivy::DocId],
    ) -> Result<(), tantivy::TantivyError> {
        // First interpret unpivot expressions using the indexed columns
        let expanded_docs = if !self.unpivot_fields.is_empty() {
            Some(super::unpivot::generate_unpivot_rows(
                &self.columns,
                &self.unpivot_fields,
                &mut self.ctx,
                docs,
                self.tracer.clone(),
            )?)
        } else {
            None
        };
        let docs = expanded_docs.as_ref().map_or(docs, |v| v.as_slice());

        // Then interpret regular aggregation expressions
        for (expr, agg) in &mut self.aggs {
            let buf = expr.interpret_batch(&mut self.ctx, &self.columns, docs)?;
            agg.aggregate(&self.ctx, buf)
                .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
        }

        Ok(())
    }

    fn into_fruit(self) -> Self::Fruit {
        self.aggs.into_iter().map(|(_, agg)| agg).collect()
    }

    fn fruit_len(_fruit: &Self::Fruit) -> usize {
        1
    }
}

macro_rules! impl_tuple_dim_collector {
    (
        $collector_name:ident,
        $segment_collector_name:ident,
        [$($T:ident),+],
        [$($idx:tt),+],
        $tuple_type:ty
    ) => {
        pub struct $collector_name<$($T: PrimitiveColumnarType),+> {
            ctx: Arc<InterpreterContext>,
            tracer: Option<Arc<TracedNode>>,
            projection: Vec<TantivyProjectedField>,
            dims: $tuple_type,
            aggs: Vec<ColumnarAggregator>,
            unpivot: Vec<TantivyUnpivotProjectionExpr>,
            _phantom: std::marker::PhantomData<($($T,)+)>,
        }

        impl<$($T: PrimitiveColumnarType),+> $collector_name<$($T),+> {
            pub fn new(
                ctx: Arc<InterpreterContext>,
                tracer: Option<Arc<TracedNode>>,
                projection: Vec<TantivyProjectedField>,
                dims: $tuple_type,
                aggs: Vec<ColumnarAggregator>,
                unpivot: Vec<TantivyUnpivotProjectionExpr>,
            ) -> Self {
                Self {
                    ctx,
                    tracer,
                    projection,
                    dims,
                    aggs,
                    unpivot,
                    _phantom: std::marker::PhantomData,
                }
            }
        }

        pub struct $segment_collector_name<$($T: PrimitiveColumnarType),+> {
            ctx: ColumnarExprContext,
            columns: Vec<Option<DynamicColumn>>,
            dims: $tuple_type,
            aggs: Vec<(ColumnarExpr, StaticAggregator)>,
            buckets: FastHashMap<($($T,)+), Vec<StaticAggregator>>,
            agg_exprs: Vec<&'static mut OpaqueBuffer>,
            unpivot_fields: Vec<Vec<UnpivotColumnarInfo>>,
            tracer: Option<Arc<TracedNode>>,
            _phantom: std::marker::PhantomData<($($T,)+)>,
        }

        impl<$($T: PrimitiveColumnarType + 'static),+> Collector for $collector_name<$($T),+> {
            type Fruit = HashMap<Vec<Value>, Vec<ValueAggregator>>;
            type Child = FallibleSegmentCollector<$segment_collector_name<$($T),+>>;

            fn for_segment(
                &self,
                _segment_local_id: u32,
                segment_reader: &SegmentReader,
            ) -> tantivy::Result<Self::Child> {
                let (columns, unpivot_fields) =
                    derive_columns(&self.projection, &self.unpivot, segment_reader, self.tracer.clone())?;
                debug_assert!(!unpivot_fields.is_empty());

                let ctx = ColumnarExprContext::from_interpreter_ctx(&self.ctx);
                Ok(FallibleSegmentCollector::new(
                    $segment_collector_name {
                        ctx,
                        aggs: make_static_aggregators(&self.aggs, &columns, &unpivot_fields)?,
                        dims: ($(self.dims.$idx.make_static(&columns, &unpivot_fields)?,)+),
                        columns,
                        buckets: FastHashMap::default(),
                        agg_exprs: Vec::with_capacity(self.aggs.len()),
                        unpivot_fields,
                        tracer: self.tracer.clone(),
                        _phantom: std::marker::PhantomData,
                    },
                    self.tracer
                        .as_ref()
                        .map(|tracer| tracer.new_child("Segment collector")),
                ))
            }

            fn merge_fruits(
                &self,
                segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
            ) -> tantivy::Result<Self::Fruit> {
                let empty_columnar_context = ColumnarExprContext::from_interpreter_ctx(&self.ctx);
                let mut buckets = FastHashMap::<($($T::Owned,)+), Vec<ValueAggregator>>::default();
                let hasher = buckets.hasher().clone();
                trace_if(log::Level::Info, &self.tracer, "Merge buckets", |_child| {
                    for segment_fruit in segment_fruits {
                        let segment_fruit = segment_fruit?;
                        for (dims, aggregators) in segment_fruit {
                            let hash_value = {
                                let mut hash_state = hasher.build_hasher();
                                $(
                                    dims.$idx.hash_key(&empty_columnar_context, &mut hash_state);
                                )+
                                hash_state.finish()
                            };

                            match buckets
                                .raw_entry_mut()
                                .from_hash(hash_value, |k| $(k.$idx.matches(&dims.$idx))&&+)
                            {
                                RawEntryMut::Vacant(entry) => {
                                    let make_hash_value = |k: &($($T::Owned,)+)| {
                                        let mut hash_state = hasher.build_hasher();
                                        $(
                                            k.$idx.hash_key(&empty_columnar_context, &mut hash_state);
                                        )+
                                        hash_state.finish()
                                    };
                                    entry.insert_with_hasher(hash_value, dims, aggregators, make_hash_value);
                                }
                                RawEntryMut::Occupied(mut entry) => {
                                    for (base_agg, new_agg) in entry.get_mut().iter_mut().zip(aggregators) {
                                        base_agg.combine(new_agg).map_err(|e| {
                                            tantivy::TantivyError::InternalError(e.to_string())
                                        })?;
                                    }
                                }
                            }
                        }
                    }

                    Ok(buckets
                        .into_iter()
                        .map(|(k, v)| {
                            (
                                vec![$(k.$idx.into_value()),+],
                                v,
                            )
                        })
                        .collect::<HashMap<Vec<Value>, Vec<ValueAggregator>>>())
                })
            }

            fn requires_scoring(&self) -> bool {
                false
            }
        }

        impl<$($T: PrimitiveColumnarType),+> FallibleCollector for $segment_collector_name<$($T),+> {
            type Fruit = FastHashMap<($($T::Owned,)+), Vec<ValueAggregator>>;

            fn collect_block_fallible(
                &mut self,
                docs: &[tantivy::DocId],
            ) -> Result<(), tantivy::TantivyError> {
                // First interpret unpivot expressions using the pre-opened readers
                let expanded_docs = if !self.unpivot_fields.is_empty() {
                    Some(super::unpivot::generate_unpivot_rows(
                        &self.columns,
                        &self.unpivot_fields,
                        &mut self.ctx,
                        docs,
                        self.tracer.clone(),
                    )?)
                } else {
                    None
                };
                let docs = expanded_docs.as_ref().map_or(docs, |v| v.as_slice());
                // Interpret all dimensions
                let dim_values = (
                    $(
                        self.dims.$idx.interpret_batch(&mut self.ctx, &self.columns, docs)?,
                    )+
                );

                // Get slices outside the loop
                let dim_slices = (
                    $(
                        dim_values.$idx.as_slice::<$T>(),
                    )+
                );

                // Assert that all dimension slices have the same length as docs
                $(
                    debug_assert_eq!(
                        dim_slices.$idx.len(),
                        docs.len(),
                        "Dimension slice {} length ({}) doesn't match docs length ({})",
                        $idx,
                        dim_slices.$idx.len(),
                        docs.len()
                    );
                )+

                self.agg_exprs.truncate(0);
                for (expr, _) in self.aggs.iter_mut() {
                    self.agg_exprs.push(unsafe {
                        std::mem::transmute::<&mut OpaqueBuffer, &'static mut OpaqueBuffer>(
                            expr.interpret_batch(&mut self.ctx, &self.columns, docs)?,
                        )
                    });
                }

                let hash_builder = self.buckets.hasher().clone();

                for i in 0..docs.len() {
                    // Use borrowed values for lookup
                    let borrowed_key = (
                        $(
                            &dim_slices.$idx[i],
                        )+
                    );

                    let hash_value = {
                        let mut hash_state = hash_builder.build_hasher();
                        $(
                            borrowed_key.$idx.hash_key(&self.ctx, &mut hash_state);
                        )+
                        hash_state.finish()
                    };

                    let mut_entry = match self
                        .buckets
                        .raw_entry_mut()
                        .from_hash(hash_value, |k| $(borrowed_key.$idx == &k.$idx)&&+)
                    {
                        RawEntryMut::Occupied(entry) => entry.into_mut(),
                        RawEntryMut::Vacant(entry) => {
                            // Only clone when we need to insert
                            let owned_key = (
                                $(
                                    dim_slices.$idx[i].clone(),
                                )+
                            );
                            let make_hash_value = |k: &($($T,)+)| -> u64 {
                                let mut hash_state = hash_builder.build_hasher();
                                $(
                                    k.$idx.hash_key(&self.ctx, &mut hash_state);
                                )+
                                hash_state.finish()
                            };
                            entry
                                .insert_with_hasher(
                                    hash_value,
                                    owned_key,
                                    self.aggs.iter().map(|(_, agg)| agg.clone()).collect(),
                                    make_hash_value,
                                )
                                .1
                        }
                    };

                    for (agg_idx, agg) in mut_entry.iter_mut().enumerate() {
                        agg.aggregate_value(&self.ctx, self.agg_exprs[agg_idx], i)
                            .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
                    }
                }

                Ok(())
            }

            fn into_fruit(self) -> Self::Fruit {
                let mut ret: Self::Fruit = FastHashMap::default();
                let hash_builder = ret.hasher().clone();
                for (k, v) in self.buckets {
                    let hash_value = {
                        let mut hash_state = hash_builder.build_hasher();
                        $(
                            k.$idx.hash_key(&self.ctx, &mut hash_state);
                        )+
                        hash_state.finish()
                    };
                    let owned_value = (
                        $(
                            k.$idx.to_owned_val(&self.ctx),
                        )+
                    );
                    match ret
                        .raw_entry_mut()
                        .from_hash(hash_value, |o| $(k.$idx.matches_owned(&self.ctx, &o.$idx))&&+)
                    {
                        RawEntryMut::Vacant(entry) => {
                            let make_hash_value = |k: &($($T::Owned,)+)| -> u64 {
                                let mut hash_state = hash_builder.build_hasher();
                                $(
                                    k.$idx.hash_key(&self.ctx, &mut hash_state);
                                )+
                                hash_state.finish()
                            };
                            entry.insert_with_hasher(hash_value, owned_value, v.into_iter().map(|a| a.into_value_aggregator()).collect(), make_hash_value);
                        }
                        RawEntryMut::Occupied(_) => {
                            panic!("duplicate key: {:?}", owned_value);
                        }
                    }
                }
                ret
            }

            fn fruit_len(fruit: &Self::Fruit) -> usize {
                fruit.len()
            }
        }
    };
}

// Generate implementations for 1 and 2 dimensions
impl_tuple_dim_collector!(
    TupleDimCollector1,
    TupleDimSegmentCollector1,
    [T0],
    [0],
    (ColumnarExpr,)
);

impl_tuple_dim_collector!(
    TupleDimCollector2,
    TupleDimSegmentCollector2,
    [T0, T1],
    [0, 1],
    (ColumnarExpr, ColumnarExpr)
);

// Type aliases for backward compatibility
pub type SingleDimCollector<B> = TupleDimCollector1<B>;
pub type SingleDimSegmentCollector<B> = TupleDimSegmentCollector1<B>;

// Additional constructor for backward compatibility with SingleDimCollector API
impl<T0: PrimitiveColumnarType> TupleDimCollector1<T0> {
    pub fn new_single(
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        projection: Vec<TantivyProjectedField>,
        dim: ColumnarExpr,
        aggs: Vec<ColumnarAggregator>,
        unpivot: Vec<TantivyUnpivotProjectionExpr>,
    ) -> Self {
        Self::new(ctx, tracer, projection, (dim,), aggs, unpivot)
    }
}

// Renamed dynamic collectors to use Tuple naming
pub struct TupleDimDynamicCollector {
    ctx: Arc<InterpreterContext>,
    tracer: Option<Arc<TracedNode>>,
    projection: Vec<TantivyProjectedField>,
    dims: Vec<ColumnarExpr>,
    aggs: Vec<ColumnarAggregator>,
    unpivot: Vec<TantivyUnpivotProjectionExpr>,
}

impl TupleDimDynamicCollector {
    pub fn new(
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        projection: Vec<TantivyProjectedField>,
        dims: Vec<ColumnarExpr>,
        aggs: Vec<ColumnarAggregator>,
        unpivot: Vec<TantivyUnpivotProjectionExpr>,
    ) -> Self {
        Self {
            ctx,
            tracer,
            projection,
            dims,
            aggs,
            unpivot,
        }
    }
}

impl Collector for TupleDimDynamicCollector {
    type Fruit = HashMap<Vec<Value>, Vec<ValueAggregator>>;
    type Child = FallibleSegmentCollector<TupleDimDynamicSegmentCollector>;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let (columns, unpivot_fields) = derive_columns(
            &self.projection,
            &self.unpivot,
            segment_reader,
            self.tracer.clone(),
        )?;

        let ctx = ColumnarExprContext::from_interpreter_ctx(&self.ctx);

        // Make static dimensions
        let static_dims: Vec<_> = self
            .dims
            .iter()
            .map(|dim| dim.make_static(&columns, &unpivot_fields))
            .collect::<tantivy::Result<_>>()?;

        match static_dims.len() {
            1 => {
                let static_dim = static_dims.into_iter().next().unwrap();

                let segment_collector = match static_dim.columnar_type() {
                    ColumnarValueType::Null => TupleDimDynamicSegmentCollector::Null(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::Bool => TupleDimDynamicSegmentCollector::Bool(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::I64 => TupleDimDynamicSegmentCollector::I64(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::U64 => TupleDimDynamicSegmentCollector::U64(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::PaginationKey => {
                        TupleDimDynamicSegmentCollector::PaginationKey(self.make_collector(
                            ctx,
                            static_dim,
                            columns,
                            unpivot_fields,
                        )?)
                    }
                    ColumnarValueType::F64 => TupleDimDynamicSegmentCollector::F64(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::DateTime => TupleDimDynamicSegmentCollector::DateTime(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::StringPtr => TupleDimDynamicSegmentCollector::StringPtr(
                        self.make_collector(ctx, static_dim, columns, unpivot_fields)?,
                    ),
                    ColumnarValueType::StringOrdinal => {
                        TupleDimDynamicSegmentCollector::StringOrdinal(self.make_collector(
                            ctx,
                            static_dim,
                            columns,
                            unpivot_fields,
                        )?)
                    }
                    ColumnarValueType::BytesOrdinal => {
                        todo!()
                    }
                    ColumnarValueType::Dynamic => {
                        return Err(tantivy::TantivyError::InternalError(
                            "Dynamic column type should have been resolved".to_string(),
                        ));
                    }
                };

                Ok(FallibleSegmentCollector::new(
                    segment_collector,
                    self.tracer
                        .as_ref()
                        .map(|tracer| tracer.new_child("Segment collector")),
                ))
            }
            2 => {
                let mut dims_iter = static_dims.into_iter();
                let dim1 = dims_iter.next().unwrap();
                let dim2 = dims_iter.next().unwrap();

                let two_dim_collector =
                    self.make_two_dim_collector(ctx, dim1, dim2, &columns, &unpivot_fields)?;
                Ok(FallibleSegmentCollector::new(
                    TupleDimDynamicSegmentCollector::TwoDim(two_dim_collector),
                    self.tracer
                        .as_ref()
                        .map(|tracer| tracer.new_child("Two-dimensional segment collector")),
                ))
            }
            n if n > MAX_OPTIMIZED_DIMENSIONS => {
                Err(tantivy::TantivyError::InternalError(format!(
                    "Dynamic collector does not support {} dimensions (max: {})",
                    n, MAX_OPTIMIZED_DIMENSIONS
                )))
            }
            _ => unreachable!(
                "Dimension count {} should have been handled",
                static_dims.len()
            ),
        }
    }

    fn merge_fruits(
        &self,
        segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        let mut buckets = FastHashMap::<Vec<Value>, Vec<ValueAggregator>>::default();
        trace_if(log::Level::Info, &self.tracer, "Merge buckets", |_child| {
            for segment_fruit in segment_fruits {
                let segment_fruit = segment_fruit?;
                for (dim, aggregators) in segment_fruit {
                    match buckets.entry(dim) {
                        Entry::Vacant(entry) => {
                            entry.insert(aggregators);
                        }
                        Entry::Occupied(mut entry) => {
                            for (base_agg, new_agg) in entry.get_mut().iter_mut().zip(aggregators) {
                                base_agg.combine(new_agg).map_err(|e| {
                                    tantivy::TantivyError::InternalError(e.to_string())
                                })?;
                            }
                        }
                    }
                }
            }

            Ok(buckets
                .into_iter()
                .collect::<HashMap<Vec<Value>, Vec<ValueAggregator>>>())
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}

macro_rules! define_single_dim_segment_collectors {
    (
        $( ($agg:ident, $collector:ident, $segment_collector:ident, $borrowed:ty) ),+ $(,)?
    ) => {

        $(
            pub type $collector = SingleDimCollector<Option<$borrowed>>;
            pub type $segment_collector = SingleDimSegmentCollector<Option<$borrowed>>;
        )+


        pub enum TupleDimDynamicSegmentCollector {
            $(
                $agg($segment_collector),
            )+
            TwoDim(TwoDimDynamicSegmentCollector),
        }

        impl FallibleCollector for TupleDimDynamicSegmentCollector {
            type Fruit = FastHashMap<Vec<Value>, Vec<ValueAggregator>>;

            fn collect_block_fallible(
                &mut self,
                docs: &[tantivy::DocId],
            ) -> Result<(), tantivy::TantivyError> {
                match self {
                    $(
                        TupleDimDynamicSegmentCollector::$agg(collector) => {
                            collector.collect_block_fallible(docs)
                        }
                    )+
                    TupleDimDynamicSegmentCollector::TwoDim(collector) => {
                        collector.collect_block_fallible(docs)
                    }
                }
            }

            fn into_fruit(self) -> Self::Fruit {
                match self {
                    $(
                        TupleDimDynamicSegmentCollector::$agg(collector) => {
                            Self::make_dynamic_fruit(collector.into_fruit())
                        }
                    )+
                    TupleDimDynamicSegmentCollector::TwoDim(collector) => {
                        collector.into_fruit()
                    }
                }
            }

            fn fruit_len(fruit: &Self::Fruit) -> usize {
                fruit.len()
            }
        }

        pub fn execute_single_dim_group_by(
            searcher: &tantivy::Searcher,
            query: &dyn tantivy::query::Query,
            ctx: Arc<InterpreterContext>,
            child: Option<Arc<TracedNode>>,
            projection: Vec<TantivyProjectedField>,
            mut dims: Vec<ColumnarExpr>,
            aggs: Vec<ColumnarAggregator>,
            unpivot: Vec<TantivyUnpivotProjectionExpr>,
        ) -> tantivy::Result<HashMap<Vec<Value>, Vec<ValueAggregator>>> {
            if dims.len() == 1 {
                let dim = dims.remove(0);
                match dim.columnar_type() {
                    $(
                        ColumnarValueType::$agg => {
                            return searcher.search_with_executor(
                                query,
                                &$collector::new(ctx.clone(), child, projection, (dim,), aggs, unpivot),
                                &ctx.executor,
                                tantivy::query::EnableScoring::disabled_from_searcher(&searcher),
                            );
                        }
                    )+
                    ColumnarValueType::Dynamic => {
                        // Fall through below
                    }
                }
            }

            searcher.search_with_executor(
                query,
                &TupleDimDynamicCollector::new(ctx.clone(), child, projection, dims, aggs, unpivot),
                &ctx.executor,
                tantivy::query::EnableScoring::disabled_from_searcher(&searcher),
            )
        }

    }
}

// Wrapper to convert tuple fruits to Vec fruits (defined first)
pub struct TwoDimWrapper<T1: PrimitiveColumnarType, T2: PrimitiveColumnarType> {
    inner: TupleDimSegmentCollector2<T1, T2>,
}

impl<T1: PrimitiveColumnarType, T2: PrimitiveColumnarType> FallibleCollector
    for TwoDimWrapper<T1, T2>
{
    type Fruit = FastHashMap<Vec<Value>, Vec<ValueAggregator>>;

    fn collect_block_fallible(
        &mut self,
        docs: &[tantivy::DocId],
    ) -> Result<(), tantivy::TantivyError> {
        self.inner.collect_block_fallible(docs)
    }

    fn into_fruit(self) -> Self::Fruit {
        let fruit = self.inner.into_fruit();
        FastHashMap::from_iter(
            fruit
                .into_iter()
                .map(|((v1, v2), aggs)| (vec![v1.into_value(), v2.into_value()], aggs)),
        )
    }

    fn fruit_len(fruit: &Self::Fruit) -> usize {
        fruit.len()
    }
}

// Maximum number of dimensions we optimize for
const MAX_OPTIMIZED_DIMENSIONS: usize = 2;

// Generate all collectors up to MAX_OPTIMIZED_DIMENSIONS
// This generates 2D (9x9 = 81) combinations
query_macros::generate_up_to_n_dim_cross_product!(
    2, // MAX_OPTIMIZED_DIMENSIONS
    (NullType, ColumnarValueType::Null, Null),
    (BoolType, ColumnarValueType::Bool, bool),
    (I64Type, ColumnarValueType::I64, i64),
    (U64Type, ColumnarValueType::U64, u64),
    (
        PaginationKeyType,
        ColumnarValueType::PaginationKey,
        util::xact::PaginationKey
    ),
    (F64Type, ColumnarValueType::F64, f64),
    (DateTimeType, ColumnarValueType::DateTime, tantivy::DateTime),
    (StringPtrType, ColumnarValueType::StringPtr, PtrOffset),
    (
        StringOrdinalType,
        ColumnarValueType::StringOrdinal,
        StringOrdinal
    ),
    (
        BytesOrdinalType,
        ColumnarValueType::BytesOrdinal,
        BytesOrdinal
    ),
);

define_single_dim_segment_collectors! {
    (Null,          NullCollector,          NullSegmentCollector,           Null),
    (Bool,          BoolCollector,          BoolSegmentCollector,           bool),
    (I64,           I64Collector,           I64SegmentCollector,            i64),
    (U64,           U64Collector,           U64SegmentCollector,            u64),
    (PaginationKey, PaginationKeyCollector, PaginationKeySegmentCollector,  PaginationKey),
    (F64,           F64Collector,           F64SegmentCollector,            f64),
    (DateTime,      DateTimeCollector,      DateTimeSegmentCollector,       DateTime),
    (StringPtr,     StringPtrCollector,     StringPtrSegmentCollector,      PtrOffset),
    (StringOrdinal, StringOrdinalCollector, StringOrdinalSegmentCollector,  StringOrdinal),
    (BytesOrdinal,  BytesOrdinalCollector,  BytesOrdinalSegmentCollector,   BytesOrdinal),
}

impl TupleDimDynamicCollector {
    fn make_collector<B: PrimitiveColumnarType>(
        &self,
        ctx: ColumnarExprContext,
        dim: ColumnarExpr,
        columns: Vec<Option<DynamicColumn>>,
        unpivot_fields: Vec<Vec<UnpivotColumnarInfo>>,
    ) -> tantivy::Result<TupleDimSegmentCollector1<B>> {
        Ok(TupleDimSegmentCollector1::<B> {
            ctx,
            dims: (dim,),
            aggs: make_static_aggregators(&self.aggs, &columns, &unpivot_fields)?,
            columns,
            buckets: FastHashMap::default(),
            agg_exprs: Vec::with_capacity(self.aggs.len()),
            unpivot_fields,
            tracer: self.tracer.clone(),
            _phantom: std::marker::PhantomData,
        })
    }
}

impl TupleDimDynamicSegmentCollector {
    fn make_dynamic_fruit<D: OwnedColumnarType>(
        values: FastHashMap<(D,), Vec<ValueAggregator>>,
    ) -> FastHashMap<Vec<Value>, Vec<ValueAggregator>> {
        FastHashMap::from_iter(values.into_iter().map(|(k, v)| (vec![k.0.into_value()], v)))
    }
}

// Public function for executing multi-dimensional group by
pub fn execute_tuple_dim_group_by(
    searcher: &tantivy::Searcher,
    query: &dyn tantivy::query::Query,
    ctx: Arc<InterpreterContext>,
    child: Option<Arc<TracedNode>>,
    projection: Vec<TantivyProjectedField>,
    dims: Vec<ColumnarExpr>,
    aggs: Vec<ColumnarAggregator>,
    unpivot: Vec<TantivyUnpivotProjectionExpr>,
) -> tantivy::Result<HashMap<Vec<Value>, Vec<ValueAggregator>>> {
    searcher.search_with_executor(
        query,
        &TupleDimDynamicCollector::new(ctx.clone(), child, projection, dims, aggs, unpivot),
        &ctx.executor,
        tantivy::query::EnableScoring::disabled_from_searcher(&searcher),
    )
}

fn derive_columns(
    projection: &Vec<TantivyProjectedField>,
    unpivots: &Vec<TantivyUnpivotProjectionExpr>,
    segment_reader: &tantivy::SegmentReader,
    tracer: Option<Arc<TracedNode>>,
) -> Result<(Vec<Option<DynamicColumn>>, Vec<Vec<UnpivotColumnarInfo>>), tantivy::TantivyError> {
    let (top_level_fields, json_fields, json_path_fields) =
        split_projected_fields_for_columnstore(projection);

    let columnstore_readers = open_columnstore_field_readers(
        &top_level_fields,
        &json_fields,
        segment_reader,
        tracer.clone(),
    )?;

    let exists_path_readers =
        open_columnstore_exists_path_readers(&json_path_fields, segment_reader, tracer.clone())?;

    let mut columns_map = HashMap::new();
    for (field, _path, column, _is_pagination_key) in columnstore_readers {
        match columns_map.entry(&field.alias) {
            std::collections::hash_map::Entry::Occupied(_) => {
                // If the column already exists, we should not overwrite it
                return Err(tantivy::TantivyError::AggregationError(
                    tantivy::aggregation::AggregationError::InvalidRequest(format!(
                    "Multiple subpaths or types for {:?}. Cannot run aggregation on this field.",
                    field.alias
                )),
                ));
            }
            std::collections::hash_map::Entry::Vacant(entry) => {
                entry.insert(column);
            }
        }
    }

    for (col, paths) in &exists_path_readers {
        for (alias_path, _) in paths {
            columns_map.insert(&alias_path[0], col.clone());
        }
    }

    if columns_map.len() > projection.len() {
        return Err(tantivy::TantivyError::InternalError(format!(
            "Columnar aggregation expected {} columns, got {}",
            projection.len(),
            columns_map.len()
        )));
    }

    let mut columns = projection
        .iter()
        .map(|f| columns_map.remove(&f.alias))
        .collect::<Vec<_>>();

    let unpivot_readers = open_unpivot_readers(unpivots, segment_reader, tracer.clone())?;

    let mut unpivot_fields = Vec::new();
    for unpivot_group in unpivot_readers {
        let consolidated_type =
            crate::interpreter::columnar::unpivot::derive_unpivot_field_type(&unpivot_group);
        let mut indexed_group = Vec::new();
        for (field_with_name, column) in unpivot_group {
            let column_index = columns.len();
            columns.push(Some(column));
            indexed_group.push(UnpivotColumnarInfo {
                unpivot_field: field_with_name,
                derived_type: consolidated_type,
                column_index,
            });
        }
        unpivot_fields.push(indexed_group);
    }

    Ok((columns, unpivot_fields))
}

pub struct FallibleSegmentCollector<T: FallibleCollector> {
    collector: T,

    tracer: Option<Arc<TracedNode>>,
    processed: u64,

    error: Option<tantivy::TantivyError>,
}

impl<T: FallibleCollector> FallibleSegmentCollector<T> {
    pub fn new(collector: T, tracer: Option<Arc<TracedNode>>) -> Self {
        Self {
            collector,
            tracer,
            processed: 0,
            error: None,
        }
    }
}

impl<T: FallibleCollector + 'static> SegmentCollector for FallibleSegmentCollector<T>
where
    T::Fruit: Send,
{
    type Fruit = Result<T::Fruit, tantivy::TantivyError>;

    fn collect(&mut self, doc: tantivy::DocId, _score: tantivy::Score) {
        self.collect_block(&[doc]);
    }

    fn collect_block(&mut self, docs: &[tantivy::DocId]) {
        if self.error.is_some() {
            return;
        }

        match self.collector.collect_block_fallible(docs) {
            Ok(()) => {
                self.processed += docs.len() as u64;
            }
            Err(e) => {
                self.error = Some(e);
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        if let Some(error) = self.error {
            return Err(error);
        }

        // Track metrics
        self.tracer
            .increment_counter("docs_processed", self.processed);

        let fruit = self.collector.into_fruit();

        // Track bucket count
        self.tracer
            .increment_counter("buckets", T::fruit_len(&fruit) as u64);

        // End the tracer
        if let Some(tracer) = self.tracer {
            tracer.end();
        }

        Ok(fruit)
    }
}

pub trait FallibleCollector {
    type Fruit;

    fn collect_block_fallible(
        &mut self,
        docs: &[tantivy::DocId],
    ) -> Result<(), tantivy::TantivyError>;

    fn into_fruit(self) -> Self::Fruit;

    fn fruit_len(fruit: &Self::Fruit) -> usize;
}

fn make_static_aggregators(
    aggs: &[ColumnarAggregator],
    columns: &Vec<Option<DynamicColumn>>,
    unpivot_fields: &Vec<Vec<UnpivotColumnarInfo>>,
) -> tantivy::Result<Vec<(ColumnarExpr, StaticAggregator)>> {
    aggs.iter()
        .map(|agg| {
            let static_expr = agg.expr.make_static(columns, unpivot_fields)?;
            let static_agg = agg
                .agg
                .new_static_aggregator(static_expr.columnar_type())
                .ok_or_else(|| {
                    tantivy::TantivyError::InternalError(
                        "Failed to create static aggregator".to_string(),
                    )
                })?;
            Ok((static_expr, static_agg))
        })
        .collect()
}
