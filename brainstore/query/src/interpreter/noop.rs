use std::sync::Arc;

use async_trait::async_trait;
use tokio::sync::mpsc;
use util::tracer::TracedNode;

use crate::planner::ast::NoopQuery;

use super::error::Result;
use super::{InterpreterContext, Operator, StreamValue};

#[async_trait]
impl Operator for NoopQuery {
    fn name(&self) -> &'static str {
        "Noop"
    }

    async fn execute(
        self,
        _ctx: Arc<InterpreterContext>,
        _tracer: Option<Arc<TracedNode>>,
        _tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        Ok(())
    }
}
