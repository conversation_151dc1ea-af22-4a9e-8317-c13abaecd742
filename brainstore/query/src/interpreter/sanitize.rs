// This module contains functions to sanitize the output of queries. Generally these are useful for
// ensuring uniform JSON output when querying across the WAL and tantivy.

use chrono::{DateTime, SecondsFormat, Utc};
use util::anyhow::{anyhow, Context, Result};

pub fn normalize_dt_field(value: &mut serde_json::Value, key: &str) -> Result<()> {
    let value_map = match value.as_object_mut() {
        Some(map) => map,
        None => return Ok(()), // If the value is not an object, we can't normalize it.
    };
    let extracted_value = match value_map.get_mut(key) {
        Some(x) => x,
        None => return Ok(()),
    };
    let extracted_str = extracted_value
        .as_str()
        .ok_or_else(|| anyhow!("Expected string for {} (got {:?})", key, extracted_value))?;
    let extracted_dt: DateTime<Utc> = extracted_str
        .parse()
        .with_context(|| format!("Failed to parse {} timestamp: {}", key, extracted_str))?;
    let formatted_dt = extracted_dt.to_rfc3339_opts(SecondsFormat::Millis, true);
    *extracted_value = serde_json::to_value(formatted_dt)
        .with_context(|| format!("Failed to serialize {} timestamp: {}", key, extracted_str))?;
    Ok(())
}
