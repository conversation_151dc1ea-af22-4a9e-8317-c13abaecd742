use jaq_core::{load, Ctx, RcIter};
use jaq_json::Val;
use load::{Arena, File, Loader};
use serde_json::json;

// From https://docs.rs/jaq-core/latest/jaq_core/index.html
pub fn run_jaq(query: &str, input: &serde_json::Value) -> util::anyhow::Result<serde_json::Value> {
    let program = File {
        code: query,
        path: (),
    };

    let loader = Loader::new(jaq_std::defs().chain(jaq_json::defs()));
    let arena = Arena::default();

    // parse the filter
    let modules = loader.load(&arena, program).unwrap();

    // compile the filter
    let filter = jaq_core::Compiler::default()
        .with_funs(jaq_std::funs().chain(jaq_json::funs()))
        .compile(modules)
        .unwrap();

    let inputs = RcIter::new(core::iter::empty());

    // iterator over the output values
    let mut out = filter.run((Ctx::new([], &inputs), Val::from(input.clone())));

    let mut values = Vec::new();
    while let Some(Ok(val)) = out.next() {
        values.push(val.into());
    }

    Ok(if values.len() == 1 {
        values.remove(0)
    } else {
        json!(values)
    })
}
