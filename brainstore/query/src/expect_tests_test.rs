#[test]
fn test_number_comparison() {
    use super::expect_tests::round_value;
    use btql::typesystem::coerce::normalize_value_for_comparison;
    use serde_json::{Number, Value};

    let test_cases = vec![
        (
            Value::Number(Number::from_f64(1e-13).unwrap()),
            Value::Number(Number::from_f64(0.0).unwrap()),
        ),
        (
            Value::Number(Number::from_f64(-1e-13).unwrap()),
            Value::Number(0.into()),
        ),
        (
            Value::Number(Number::from_f64(42.0000000000001).unwrap()),
            Value::Number(42.into()),
        ),
        (
            Value::Number(Number::from_f64(-3.0000000000005).unwrap()),
            Value::Number((-3).into()),
        ),
        (
            Value::Number(Number::from_f64(16.9999999999995).unwrap()),
            Value::Number(17.into()),
        ),
        (
            Value::Number(Number::from_f64(1.1234567890123456).unwrap()),
            Value::Number(Number::from_f64(1.123456789012).unwrap()),
        ),
        (
            Value::Number(Number::from_f64(-1.1234567890123456).unwrap()),
            Value::Number(Number::from_f64(-1.123456789012).unwrap()),
        ),
    ];

    for (input, expected) in test_cases {
        let result = normalize_value_for_comparison(round_value(input.clone()));
        let expected = normalize_value_for_comparison(round_value(expected.clone()));
        assert_eq!(
            result, expected,
            "Failed for input {}: expected {}, got {}",
            input, expected, result
        );
    }
}
