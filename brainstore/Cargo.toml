[workspace]

members = [
    "util",
    "async_util",
    "tracing",
    "tantivy",
    "btql",
    "query",
    "cli",
    "ptree_derive",
    "tantivy-cli",
    "btql-wasm",
    "bigjson",
    "otel_common",
    "agent",
    "pulse",
    "query-macros",
]
resolver = "2"

[profile.release]
debug = 2

# Declare "aliases" for our dependencies here, so that different crates
# can use the same versions of common packages. This helps avoid drift between
# common deps.
[workspace.dependencies]
actix-web = "4.9.0"
aes-gcm = "0.10.3"
anyhow = { version = "1.0.89", features = ["backtrace"] }
arc-swap = "1.7.1"
async-channel = "2.4.0"
async-stream = "0.3.6"
async-trait = "0.1.83"
aws-config = "1.8.0"
backoff = { version = "0.4", features = ["tokio"] }
base64 = "0.22.1"
# This fork is really annoying, but if we use serde_json directly, <PERSON>ust will
# deduplicate the package and force everything (across crates) to use the same
# feature flags. So, I forked it, and just found the commit corresponding to
# v1.0.140
# (https://github.com/serde-rs/json/commit/762783414e6c4f8d670c9d87eb04913efb80d3be)
big_serde_json = { package = "serde_json", git = "https://github.com/ankrgyl/serde-json.git", rev = "762783414e6c4f8d670c9d87eb04913efb80d3be", features = ["unbounded_depth", "arbitrary_precision"] }
bincode = "=2.0.0"
byte-unit = "5.1.4"
bytes = "1.7.2"
chrono = { version = "0.4.38", features = ["serde"] }
clap = { version = "4.5.20", features = ["derive", "env"] }
colored = "3.0.0"
console_error_panic_hook = "0.1.7"
criterion = "0.5"
dateparser = "0.2.1"
deadpool = "0.12.1"
deadpool-postgres = "0.14.0"
downcast-rs = "1.2.1"
env_logger = "0.11.5"
flate2 = "1.0.34"
fork = "0.2.0"
futures = "0.3.30"
# This is a workaround for the wasm-bindgen issue with getrandom.
getrandom = { version = "0.2.15", features = ["js"] }
gxhash = "3.4.1"
hashbrown = { version = "0.15.2", features = ["raw-entry"] }
hostname = "0.4.1"
itertools = "0.13.0"
jaq-core = { version = "2.1.0" }
jaq-json = { version = "1.1.0", features = ["serde_json"] }
jaq-std = { version = "2.1.0" }
jemalloc_pprof = { version = "0.7", features = [] }
json-subscriber = { version = "0.2.6", features = ["tracing-opentelemetry-0-31"] }
lazy_static = "1.5.0"
log = "0.4.22"
lru = "0.12.5"
memmap2 = "0.9.5"
nix = { version = "0.29.0", features = ["process", "poll"] }
num_cpus = "1.16.0"
# This commit includes retries for connection errors. Once 0.12.4 is out, we can go back to the official release.
object_store = { git = "https://github.com/apache/arrow-rs-object-store.git", rev="1e6c78ed08c04a84e4e508fdd0aa94f4936b917d", features = ["aws", "gcp", "azure"] }
once_cell = "1.20.2"
opentelemetry = { version = "0.30.0", features = ["trace"] }
opentelemetry-appender-tracing = "0.30.1"
opentelemetry-otlp = { version = "0.30.0", features = ["http-proto", "trace"] }
opentelemetry-proto = { version = "0.30.0", features = ["metrics"] }
opentelemetry-resource-detectors = "0.9.0"
opentelemetry-semantic-conventions = "0.30.0"
opentelemetry-stdout = { version = "0.30.0", features = ["trace"] }
opentelemetry_sdk = { version = "0.30.0", features = ["rt-tokio", "testing"] }
proc-macro2 = "1.0"
procfs = { version = "0.17.0" }
prost = "0.13.5"
prost-types = "0.13.5"
ptree = "0.5.0"
quote = "1.0"
rand = "0.8.5"
rand_distr = "0.4"
rayon = "1.10.0"
redis = { version = "0.29.1", features = ["tls-rustls", "tokio-rustls-comp", "tokio-comp"] }
regex = "1.11.0"
regex-automata = "0.4.9"
reqwest = { version = "0.12.7", default-features = false, features = ["blocking", "json", "rustls-tls"] }
ring = { version = "0.17.8" }
rslock = { git = "https://github.com/ankrgyl/rslock.git", rev = "9f5b577ab1b93d88c41f3f513a2bfb19ac065a88", features = ["tls-rustls", "tokio-rustls-comp", "tokio-comp"] }
rust_decimal = { version = "1.36.0", features = ["db-postgres"] }
rustc-hash = "2.0.0"
rustls = { version = "0.23.31", features = ["ring"] }
serde = { version = "1.0.210", features = ["derive"] }
serde-wasm-bindgen = "0.6.5"
serde_json = { version = "1.0.128", default-features = false, features = ["std", "unbounded_depth"] }
serde_yaml = "0.9.34"
sha1 = "0.10"
sha2 = "0.10.8"
similar = "2.6.0"
similar-asserts = "1.6.0"
sketches-ddsketch = "0.3.0"
stable_deref_trait = "1.2.0"
syn = { version = "2.0", features = ["full"] }
sysinfo = "0.35.1"
tantivy-fst = "0.5.0"
tantivy_common = { package = "tantivy-common", git = "https://github.com/braintrustdata/tantivy.git", rev = "8b6f84f891caae8f07a9353e344ebf65f071e118" }
tempfile = "3.13.0"
testcontainers = "0.23.1"
testcontainers-modules = { version = "0.11.3", features = ["postgres", "redis", "minio"] }
thiserror = "1.0"
tikv-jemallocator = { version = "0.6.0", features = ["profiling", "unprefixed_malloc_on_supported_platforms"] }
time = { version = "0.3.37", features = ["formatting", "macros"] }
tokio = { version = "1.40.0", features = ["sync", "rt", "time", "macros", "rt-multi-thread", "process"] }
tokio-postgres = { version = "0.7.12", features = ["with-serde_json-1", "with-chrono-0_4", "with-uuid-1"] }
tokio-postgres-rustls = "0.13.0"
tokio-stream = "0.1.16"
tonic = { version = "0.12.3", features = ["tls", "tls-roots"] }
tonic-build = { version = "0.12.3", features = ["prost"] }
tracing-log = "0.2.0"
tracing-opentelemetry = "0.31.0"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "json"] }
url = { version = "2.5.2", features = ["serde"] }
uuid = { version = "1.10.0", features = ["v4", "js"] }
walkdir = "2.5.0"
wasm-bindgen = "0.2.100"
webpki-roots = "0.26"
