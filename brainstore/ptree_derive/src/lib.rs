use proc_macro::TokenStream;
use quote::{quote, ToTokens};
use syn::{parse_macro_input, Data, DeriveInput, Fields, LitStr};

#[proc_macro_derive(MakePTree, attributes(ptree))]
pub fn derive_make_ptree(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);

    // Check if this is a label-only implementation (just label + empty body)
    let is_label_only = input
        .attrs
        .iter()
        .find(|attr| attr.path().is_ident("ptree"))
        .map(|attr| {
            if let Ok(ident) = attr.parse_args::<syn::Ident>() {
                ident.to_string() == "label_only"
            } else {
                false
            }
        })
        .unwrap_or(false);

    // Parse label from attribute if present (when not label_only)
    let label = if !is_label_only {
        input
            .attrs
            .iter()
            .find(|attr| attr.path().is_ident("ptree"))
            .and_then(|attr| attr.parse_args::<LitStr>().ok())
            .map(|lit| lit.value())
    } else {
        None
    };

    let implementation = if is_label_only {
        implement_label_only(&input)
    } else {
        match &input.data {
            Data::Struct(data) => implement_struct(&input, data.fields.clone(), label),
            Data::Enum(data) => implement_enum(&input, &data.variants, label),
            Data::Union(_) => panic!("MakePTree cannot be derived for unions"),
        }
    };

    // Useful for debugging
    // println!("{}", implementation.to_string());

    implementation.into()
}

fn implement_label_only(input: &DeriveInput) -> proc_macro2::TokenStream {
    let name = &input.ident;
    let name_str = capitalize(name.to_string());
    let generics = &input.generics;
    let (impl_generics, ty_generics, where_clause) = generics.split_for_impl();

    quote! {
        impl #impl_generics util::ptree::MakePTree for #name #ty_generics #where_clause {
            fn label(&self) -> String {
                #name_str.to_string()
            }

            fn make_ptree(&self, _builder: &mut util::ptree::TreeBuilder) {}
        }
    }
}

fn implement_struct(
    input: &DeriveInput,
    fields: Fields,
    label: Option<String>,
) -> proc_macro2::TokenStream {
    let name = &input.ident;
    let name_str = capitalize(name.to_string());
    let generics = &input.generics;
    let (impl_generics, ty_generics, where_clause) = generics.split_for_impl();

    let label_impl = match label {
        Some(label) => quote! { #label.to_string() },
        None => quote! { #name_str.to_string() },
    };

    let make_ptree_impl = match fields {
        Fields::Named(fields) => {
            let field_implementations = fields.named.iter().map(|field| {
                let field_name = field.ident.as_ref().unwrap();
                let field_label = capitalize(field_name.to_string());

                match get_field_attr(field) {
                    Some(FieldAttr::Skip) => quote! {},
                    Some(FieldAttr::Display(text)) => quote! {
                        builder.begin_child(#field_label.to_string());
                        builder.add_empty_child(#text.to_string());
                        builder.end_child();
                    },
                    Some(FieldAttr::Fn(func_name)) => {
                        let func_ident =
                            syn::Ident::new(&func_name, proc_macro2::Span::call_site());
                        quote! {
                            builder.begin_child(#field_label.to_string());
                            #func_ident(builder, &self.#field_name);
                            builder.end_child();
                        }
                    }
                    None => {
                        if field.ty.to_token_stream().to_string().starts_with("Option") {
                            quote! {
                                if let Some(field) = &self.#field_name {
                                    builder.begin_child(#field_label.to_string());
                                    self.add_child(builder, field);
                                    builder.end_child();
                                }
                            }
                        } else {
                            quote! {
                                if !self.#field_name.should_skip() {
                                    builder.begin_child(#field_label.to_string());
                                    self.add_child(builder, &self.#field_name);
                                    builder.end_child();
                                }
                            }
                        }
                    }
                }
            });

            quote! {
                fn make_ptree(&self, builder: &mut util::ptree::TreeBuilder) {
                    #(#field_implementations)*
                }
            }
        }
        Fields::Unnamed(_) => {
            quote! {
                fn make_ptree(&self, builder: &mut util::ptree::TreeBuilder) {
                    builder.add_empty_child(self.label());
                }
            }
        }
        Fields::Unit => {
            quote! {
                fn make_ptree(&self, builder: &mut util::ptree::TreeBuilder) {
                    builder.add_empty_child(self.label());
                }
            }
        }
    };

    quote! {
        impl #impl_generics util::ptree::MakePTree for #name #ty_generics #where_clause {
            fn label(&self) -> String {
                #label_impl
            }

            #make_ptree_impl
        }
    }
}

fn capitalize(s: String) -> String {
    let mut chars = s.chars();
    match chars.next() {
        None => String::new(),
        Some(first) => first
            .to_uppercase()
            .chain(chars)
            .collect::<String>()
            .replace('_', " "),
    }
}

fn implement_enum(
    input: &DeriveInput,
    variants: &syn::punctuated::Punctuated<syn::Variant, syn::token::Comma>,
    label: Option<String>,
) -> proc_macro2::TokenStream {
    let name = &input.ident;
    let generics = &input.generics;
    let (impl_generics, ty_generics, where_clause) = generics.split_for_impl();
    let variant_matches = variants.iter().map(|variant| {
        let variant_name = &variant.ident;
        let pattern = match &variant.fields {
            Fields::Named(fields) => {
                let field_names: Vec<_> = fields
                    .named
                    .iter()
                    .map(|f| f.ident.as_ref().unwrap())
                    .collect();
                quote! { #name::#variant_name { #(#field_names),* } }
            }
            Fields::Unnamed(_) => {
                quote! { #name::#variant_name(inner) }
            }
            Fields::Unit => {
                quote! { #name::#variant_name }
            }
        };

        let implementation = match &variant.fields {
            Fields::Named(fields) => {
                let field_implementations = fields.named.iter().map(|field| {
                    let field_name = field.ident.as_ref().unwrap();
                    let field_label = capitalize(field_name.to_string());
                    quote! {
                        builder.begin_child(#field_label.to_string());
                        self.add_child(builder, #field_name);
                        builder.end_child();
                    }
                });
                quote! { #(#field_implementations)* }
            }
            Fields::Unnamed(_) => {
                quote! {
                    self.add_child(builder, inner);
                }
            }
            Fields::Unit => {
                let variant_label = capitalize(variant_name.to_string());
                quote! {
                    builder.add_empty_child(#variant_label.to_string());
                }
            }
        };

        quote! {
            #pattern => {
                #implementation
            }
        }
    });

    let name_str = capitalize(name.to_string());
    let label_impl = match label {
        Some(label) => quote! { #label.to_string() },
        None => quote! { #name_str.to_string() },
    };

    quote! {
        impl #impl_generics util::ptree::MakePTree for #name #ty_generics #where_clause {
            fn label(&self) -> String {
                #label_impl
            }

            fn passthrough(&self) -> bool {
                true
            }

            fn make_ptree(&self, builder: &mut util::ptree::TreeBuilder) {
                match self {
                    #(#variant_matches)*
                }
            }
        }
    }
}

// Helper function to parse field attributes
fn get_field_attr(field: &syn::Field) -> Option<FieldAttr> {
    field
        .attrs
        .iter()
        .find(|attr| attr.path().is_ident("ptree"))
        .map(|attr| {
            // Parse the attribute content
            match &attr.meta {
                syn::Meta::List(list) => {
                    let tokens = &list.tokens;
                    let token_stream = proc_macro2::TokenStream::from(tokens.clone());
                    let tokens: Vec<_> = token_stream.into_iter().collect();

                    // Handle fn = "..." format
                    if tokens.len() == 3 {
                        if let (
                            proc_macro2::TokenTree::Ident(ident),
                            proc_macro2::TokenTree::Punct(eq),
                            proc_macro2::TokenTree::Ident(func_name),
                        ) = (&tokens[0], &tokens[1], &tokens[2])
                        {
                            if ident.to_string() == "fn" && eq.as_char() == '=' {
                                return FieldAttr::Fn(func_name.to_string());
                            }
                        }
                    }

                    // Handle single identifier (like "skip")
                    if tokens.len() == 1 {
                        if let proc_macro2::TokenTree::Ident(ident) = &tokens[0] {
                            if ident.to_string() == "skip" {
                                return FieldAttr::Skip;
                            }
                        }
                    }

                    // Handle string literal format
                    if tokens.len() == 1 {
                        if let proc_macro2::TokenTree::Literal(lit) = &tokens[0] {
                            let lit_str = lit.to_string().trim_matches('"').to_string();
                            if lit_str.starts_with("display = ") {
                                return FieldAttr::Display(lit_str[9..].to_string());
                            }
                        }
                    }

                    panic!("Invalid attribute format");
                }
                _ => panic!("Invalid attribute"),
            }
        })
}

#[derive(Debug)]
enum FieldAttr {
    Skip,
    Display(String),
    Fn(String),
}
