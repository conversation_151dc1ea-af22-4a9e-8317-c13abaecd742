use serde::{Deserialize, Serialize};
use std::collections::HashSet;

use crate::binder::types::type_rank;

use super::json::JSONSchema;

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ScalarType {
    #[serde(rename = "unknown")]
    Unknown,
    #[serde(rename = "array")]
    Array,
    #[serde(rename = "boolean")]
    <PERSON><PERSON><PERSON>,
    #[serde(rename = "integer")]
    Integer,
    #[serde(rename = "null")]
    Null,
    #[serde(rename = "number")]
    Number,
    #[serde(rename = "object")]
    Object,
    #[serde(rename = "string")]
    String,
    #[serde(rename = "date")]
    Date,
    #[serde(rename = "datetime")]
    DateTime,
    #[serde(rename = "interval")]
    Interval,
}

impl PartialOrd for ScalarType {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(type_rank(*self).cmp(&type_rank(*other)))
    }
}

impl Ord for ScalarType {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        type_rank(*self).cmp(&type_rank(*other))
    }
}

impl ScalarType {
    pub fn to_logical_schema(&self) -> JSONSchema {
        match self {
            ScalarType::Unknown => serde_json::json!({}),
            ScalarType::Array
            | ScalarType::Boolean
            | ScalarType::Integer
            | ScalarType::Object
            | ScalarType::Null
            | ScalarType::Number
            | ScalarType::String => {
                serde_json::json!({ "type": self })
            }
            ScalarType::Date => serde_json::json!({ "type": "string", "format": "date" }),
            ScalarType::DateTime => serde_json::json!({ "type": "string", "format": "date-time" }),
            ScalarType::Interval => serde_json::json!({ "type": "string", "format": "duration" }),
        }
    }

    pub fn is_object_like(&self) -> bool {
        matches!(
            self,
            ScalarType::Unknown | ScalarType::Array | ScalarType::Object
        )
    }
}

pub fn derive_scalar_types(schema: &JSONSchema) -> Vec<ScalarType> {
    if !schema.is_object() {
        return if schema.as_bool().unwrap_or(false) {
            vec![
                ScalarType::Unknown,
                ScalarType::Array,
                ScalarType::Boolean,
                ScalarType::Integer,
                ScalarType::Null,
                ScalarType::Number,
                ScalarType::Object,
                ScalarType::String,
                ScalarType::Date,
                ScalarType::DateTime,
                ScalarType::Interval,
            ]
        } else {
            vec![ScalarType::Null]
        };
    }

    let obj = schema.as_object().unwrap();

    if obj.contains_key("type") {
        let type_value = &obj["type"];
        if type_value.is_array() {
            let type_array = type_value.as_array().unwrap();
            return type_array
                .iter()
                .flat_map(|t| {
                    if t.is_string() {
                        vec![string_to_scalar_type(t.as_str().unwrap())]
                    } else {
                        derive_scalar_types(t)
                    }
                })
                .collect::<HashSet<_>>()
                .into_iter()
                .collect();
        } else if type_value.is_string() {
            let type_str = type_value.as_str().unwrap();
            return vec![string_to_scalar_type(type_str)];
        }
    }

    if obj.contains_key("anyOf") || obj.contains_key("oneOf") {
        let array = obj
            .get("anyOf")
            .or(obj.get("oneOf"))
            .unwrap()
            .as_array()
            .unwrap();
        return array
            .iter()
            .flat_map(|t| derive_scalar_types(t))
            .collect::<HashSet<_>>()
            .into_iter()
            .collect();
    }

    if obj.contains_key("allOf") {
        let array = obj["allOf"].as_array().unwrap();
        let mut result: HashSet<ScalarType> = HashSet::new();
        for t in array {
            let types = derive_scalar_types(t);
            if result.is_empty() {
                result = types.into_iter().collect();
            } else {
                result.retain(|x| types.contains(x));
            }
        }
        return result.into_iter().collect();
    }

    if obj.contains_key("properties") || obj.contains_key("additionalProperties") {
        return vec![ScalarType::Object];
    }

    if obj.contains_key("items") || obj.contains_key("additionalItems") {
        return vec![ScalarType::Array];
    }

    vec![
        ScalarType::Unknown,
        ScalarType::Array,
        ScalarType::Boolean,
        ScalarType::Integer,
        ScalarType::Null,
        ScalarType::Number,
        ScalarType::Object,
        ScalarType::String,
        ScalarType::Date,
        ScalarType::DateTime,
        ScalarType::Interval,
    ]
}

fn string_to_scalar_type(s: &str) -> ScalarType {
    match s {
        "string" => ScalarType::String,
        "number" => ScalarType::Number,
        "integer" => ScalarType::Integer,
        "boolean" => ScalarType::Boolean,
        "array" => ScalarType::Array,
        "object" => ScalarType::Object,
        "null" => ScalarType::Null,
        _ => ScalarType::Unknown,
    }
}

pub fn is_scalar(schema: &JSONSchema) -> bool {
    let simple_types = derive_scalar_types(schema);
    !simple_types.contains(&ScalarType::Array)
        && !simple_types.contains(&ScalarType::Object)
        && !simple_types.contains(&ScalarType::Unknown)
}
