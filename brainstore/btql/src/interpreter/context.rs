#[cfg(feature = "tantivy")]
use std::sync::Arc;

use time::OffsetDateTime;

#[derive(Clone)]
pub struct ExprContext {
    pub tz_offset: Option<i16>,
    pub current_timestamp: OffsetDateTime,

    #[cfg(feature = "tantivy")]
    pub executor: Arc<tantivy::Executor>,
    #[cfg(feature = "tantivy")]
    pub tokenizer_manager: tantivy::tokenizer::TokenizerManager,
}

impl ExprContext {
    #[cfg(feature = "tantivy")]
    pub fn new(tz_offset: Option<i16>) -> Self {
        Self::new_with_executor(Arc::new(tantivy::Executor::SingleThread), tz_offset)
    }

    #[cfg(feature = "tantivy")]
    pub fn new_with_executor(executor: Arc<tantivy::Executor>, tz_offset: Option<i16>) -> Self {
        Self {
            current_timestamp: OffsetDateTime::now_utc(),
            tz_offset,

            executor,
            tokenizer_manager: tantivy::tokenizer::TokenizerManager::default(),
        }
    }

    #[cfg(not(feature = "tantivy"))]
    pub fn new(tz_offset: Option<i16>) -> Self {
        Self {
            current_timestamp: OffsetDateTime::now_utc(),
            tz_offset,
        }
    }

    #[cfg(feature = "tantivy")]
    pub fn get_tokenizer(&self, field: &str) -> Option<tantivy::tokenizer::TextAnalyzer> {
        self.tokenizer_manager.get(field)
    }
}
