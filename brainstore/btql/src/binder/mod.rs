pub mod ast;
pub mod functions;
pub mod json_ast;
pub mod types;

use std::{
    io::{BufR<PERSON>, Write},
    path::PathBuf,
    process::{Child, Command, Stdio},
};

pub use ast::{Expr, Query};
use serde::Deserialize;
use util::bail;

pub fn bind_query(query: &str, schema: &str) -> util::Result<Query> {
    BindRepl::new(schema)?.bind(query)
}

pub struct BindRepl {
    process: Child,
}

#[derive(Deserialize)]
struct Response {
    ok: Option<json_ast::BoundQuery>,
    error: Option<String>,
}

impl BindRepl {
    pub fn new(schema: &str) -> util::Result<Self> {
        // From the position of this binary, run
        // node ../../../btql/cli/dist/index.js bind --query "SELECT * FROM logs" --schema ./examples/scores/schema.json
        let btql_cli_path =
            PathBuf::from(env!("CARGO_MANIFEST_DIR")).join("../../btql/cli/dist/index.js");
        let process = Command::new("node")
            .arg(btql_cli_path)
            .arg("bind-repl")
            .arg("--schema")
            .arg(schema)
            .arg("--skip-field-casts")
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .spawn()?;
        Ok(Self { process })
    }

    pub fn bind(&mut self, query: &str) -> util::Result<Query> {
        let query_line = format!("{}\n", serde_json::to_string(query)?);
        self.process
            .stdin
            .as_mut()
            .unwrap()
            .write_all(query_line.as_bytes())?;
        let mut output = String::new();
        std::io::BufReader::new(&mut self.process.stdout.as_mut().unwrap())
            .read_line(&mut output)?;

        let response: Response = serde_json::from_str(&output)?;
        if let Some(error) = response.error {
            bail!("btql bind failed: {}", error);
        }
        match response.ok {
            Some(bound_query) => Ok(ast::Query::try_from(bound_query)?),
            None => bail!(response
                .error
                .unwrap_or_else(|| "btql bind failed".to_string())),
        }
    }
}
