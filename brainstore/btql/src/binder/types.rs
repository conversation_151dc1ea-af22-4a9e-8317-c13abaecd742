use serde_json::json;

use crate::{binder::ast::ArithmeticOp, schema::scalar::derive_scalar_types};

use super::{
    ast::{LogicalSchema, ScalarType, UnaryOp},
    functions::FUNCTIONS,
    Expr,
};

pub fn scalar_type_to_logical_schema(scalar_type: ScalarType) -> LogicalSchema {
    match scalar_type {
        ScalarType::Unknown => serde_json::json!({}),
        ScalarType::Array
        | ScalarType::Boolean
        | ScalarType::Integer
        | ScalarType::Object
        | ScalarType::Null
        | ScalarType::Number
        | ScalarType::String => {
            serde_json::json!({ "type": scalar_type })
        }
        ScalarType::Date => serde_json::json!({ "type": "string", "format": "date" }),
        ScalarType::DateTime => serde_json::json!({ "type": "string", "format": "date-time" }),
        ScalarType::Interval => serde_json::json!({ "type": "string", "format": "duration" }),
    }
}

pub fn weakest_scalar_type(schema: &LogicalSchema) -> ScalarType {
    let mut scalar_types = derive_scalar_types(schema);
    scalar_types.sort_by_key(|t| type_rank(*t));
    if scalar_types.is_empty() {
        ScalarType::Null
    } else {
        scalar_types.remove(0)
    }
}

pub fn coerce_scalar_types(
    a: ScalarType,
    b: ScalarType,
) -> (Option<ScalarType>, Option<ScalarType>) {
    if a == b {
        return (None, None);
    }

    let mut rank_a = type_rank(a);
    let mut rank_b = type_rank(b);

    let number_types = [ScalarType::Number, ScalarType::Integer, ScalarType::Boolean];
    if number_types.contains(&a) && number_types.contains(&b) {
        rank_a *= -1;
        rank_b *= -1;
    }

    if (a == ScalarType::Interval && b != ScalarType::Interval)
        || (b == ScalarType::Interval && a != ScalarType::Interval)
    {
        return (
            if a == ScalarType::DateTime {
                None
            } else {
                Some(ScalarType::DateTime)
            },
            None,
        );
    }

    match (rank_a, rank_b) {
        (rank_a, rank_b) if rank_a == rank_b => (None, None),
        (rank_a, rank_b) if rank_a > rank_b => (None, Some(a)),
        _ => (Some(b), None),
    }
}

// Returns the type rank for ScalarType, following the same pattern as ColumnarValueType.
// See query/src/interpreter/columnar/value.rs::columnar_value_type_rank.
pub fn type_rank(type_: ScalarType) -> i32 {
    match type_ {
        ScalarType::Null => 10,
        ScalarType::Interval => 9,
        ScalarType::DateTime => 8,
        ScalarType::Date => 7,
        ScalarType::Boolean => 6,
        ScalarType::Integer => 5,
        ScalarType::Number => 4,
        ScalarType::String => 3,
        ScalarType::Array => 2,
        ScalarType::Object => 1,
        ScalarType::Unknown => 0,
    }
}

fn is_date_like_and_interval(date_like: ScalarType, left: ScalarType, right: ScalarType) -> bool {
    (left == date_like && matches!(right, ScalarType::Interval))
        || (matches!(left, ScalarType::Interval) && right == date_like)
}

pub fn get_expr_schema(expr: &Expr) -> LogicalSchema {
    match expr {
        Expr::Literal(literal) => scalar_type_to_logical_schema(literal.expr_type),
        Expr::Interval { .. } => scalar_type_to_logical_schema(ScalarType::Interval),
        Expr::Field(field) => field.expr_type.clone(),
        Expr::Function(function) => {
            let fn_def = match FUNCTIONS.get(&function.name) {
                Some(def) => def,
                None => panic!("Unsupported function: {}", function.name),
            };
            let named: Vec<_> = fn_def
                .args
                .named
                .iter()
                .enumerate()
                .map(|(i, (key, _))| (key.clone(), get_expr_schema(function.args[i].as_ref())))
                .collect();
            let variadic: Vec<_> = function.args[fn_def.args.named.len()..]
                .iter()
                .map(|e| get_expr_schema(e.as_ref()))
                .collect();

            (fn_def.make_type.as_ref())(&named, &variadic)
        }
        Expr::Comparison { .. }
        | Expr::Includes { .. }
        | Expr::Boolean { .. }
        | Expr::Unary {
            op: UnaryOp::Not | UnaryOp::IsNull | UnaryOp::IsNotNull,
            ..
        } => scalar_type_to_logical_schema(ScalarType::Boolean),
        Expr::Ternary { conds, else_expr } => {
            let mut schemas = Vec::new();
            for cond in conds {
                schemas.push(get_expr_schema(cond.then.as_ref()));
            }
            schemas.push(get_expr_schema(else_expr.as_ref()));
            json!({
                "anyOf": schemas,
            })
        }
        Expr::Arithmetic { op, left, right } => {
            let left_type = get_expr_scalar_type(left.as_ref());
            let right_type = get_expr_scalar_type(right.as_ref());
            if matches!(left_type, ScalarType::Integer | ScalarType::Boolean)
                && !matches!(op, ArithmeticOp::Div)
            {
                scalar_type_to_logical_schema(ScalarType::Integer)
            } else if is_date_like_and_interval(ScalarType::DateTime, left_type, right_type) {
                scalar_type_to_logical_schema(ScalarType::DateTime)
            } else if is_date_like_and_interval(ScalarType::Date, left_type, right_type) {
                scalar_type_to_logical_schema(ScalarType::Date)
            } else {
                scalar_type_to_logical_schema(ScalarType::Number)
            }
        }
        Expr::Unary {
            op: UnaryOp::Neg,
            expr,
        } => get_expr_schema(expr.as_ref()),
        Expr::Unary {
            op: UnaryOp::Cast { cast_type },
            ..
        } => scalar_type_to_logical_schema(*cast_type),
    }
}

pub fn get_expr_scalar_type(expr: &Expr) -> ScalarType {
    weakest_scalar_type(&get_expr_schema(expr))
}
