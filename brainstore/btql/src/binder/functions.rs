use lazy_static::lazy_static;
use serde_json::json;
use std::collections::BTreeMap;

use super::ast::LogicalSchema;

lazy_static! {
    pub static ref FUNCTIONS: BTreeMap<String, FunctionSpec> = [
        ("second", make_datetime_trunc_function()),
        ("minute", make_datetime_trunc_function()),
        ("hour", make_datetime_trunc_function()),
        ("day", make_datetime_trunc_function()),
        ("week", make_datetime_trunc_function()),
        ("month", make_datetime_trunc_function()),
        ("year", make_datetime_trunc_function()),
        (
            "current_timestamp",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new([], []),
                make_type: Box::new(make_datetime_type),
            }
        ),
        (
            "current_date",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new([], []),
                make_type: Box::new(make_date_type),
            }
        ),
        (
            "sha256",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new([("expr".to_string(), vec![json!({})])], []),
                make_type: Box::new(make_string_type),
            }
        ),
        (
            "insert",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new(
                    [("expr".to_string(), vec![json!({})])],
                    vec![json!({"type": "string"}), json!({})]
                ),
                make_type: Box::new(|args, _| args[0].1.clone()),
            }
        ),
        ("coalesce", make_coalesce_like_function()),
        ("least", make_coalesce_like_function()),
        ("greatest", make_coalesce_like_function()),
        (
            "concat",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new([], vec![json!({"type": "string"}), json!({})]),
                make_type: Box::new(make_string_type),
            }
        ),
        (
            "nullif",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new(
                    [
                        ("left".to_string(), vec![json!({})]),
                        ("right".to_string(), vec![json!({})]),
                    ],
                    []
                ),
                make_type: Box::new(make_nullif_type),
            }
        ),
        (
            "lower",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new(
                    [("expr".to_string(), vec![json!({"type": "string"})])],
                    []
                ),
                make_type: Box::new(make_string_type),
            }
        ),
        (
            "upper",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new(
                    [("expr".to_string(), vec![json!({"type": "string"})])],
                    []
                ),
                make_type: Box::new(make_string_type),
            }
        ),
        (
            "json_extract",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new(
                    [("expr".to_string(), vec![json!({"type": "string"})])],
                    vec![json!({"type": "string"})]
                ),
                make_type: Box::new(make_any_type),
            }
        ),
        (
            "count",
            FunctionSpec {
                is_agg: true,
                args: FunctionArgSpec::new([("expr".to_string(), vec![json!({"default": 1})])], []),
                make_type: Box::new(make_integer_type),
            }
        ),
        (
            "sum",
            FunctionSpec {
                is_agg: true,
                args: FunctionArgSpec::new(
                    [(
                        "expr".to_string(),
                        vec![json!({"type": "number"}), json!({"type": "integer"})]
                    )],
                    []
                ),
                make_type: Box::new(make_sum_type),
            }
        ),
        (
            "avg",
            FunctionSpec {
                is_agg: true,
                args: FunctionArgSpec::new(
                    [(
                        "expr".to_string(),
                        vec![json!({"type": "number"}), json!({"type": "integer"})]
                    )],
                    []
                ),
                make_type: Box::new(make_nullable_number_type),
            }
        ),
        (
            "min",
            FunctionSpec {
                is_agg: true,
                args: FunctionArgSpec::new([("expr".to_string(), vec![json!({})])], []),
                make_type: Box::new(|args, _| args[0].1.clone()),
            }
        ),
        (
            "max",
            FunctionSpec {
                is_agg: true,
                args: FunctionArgSpec::new([("expr".to_string(), vec![json!({})])], []),
                make_type: Box::new(|args, _| args[0].1.clone()),
            }
        ),
        (
            "percentile",
            FunctionSpec {
                is_agg: true,
                args: FunctionArgSpec::new(
                    [
                        (
                            "expr".to_string(),
                            vec![json!({"type": "number"}), json!({"type": "integer"})]
                        ),
                        ("p".to_string(), vec![json!({"type": "number"})]),
                    ],
                    []
                ),
                make_type: Box::new(make_nullable_number_type),
            }
        ),
        (
            "len",
            FunctionSpec {
                is_agg: false,
                args: FunctionArgSpec::new(
                    [("expr".to_string(), vec![json!({"type": "array"})])],
                    []
                ),
                make_type: Box::new(make_nullable_integer_type),
            }
        ),
    ]
    .into_iter()
    .map(|(k, v)| (k.to_string(), v))
    .collect();
    pub static ref FUNCTION_ALIAS: BTreeMap<String, String> = [("now", "current_timestamp"),]
        .into_iter()
        .map(|(k, v)| (k.to_string(), v.to_string()))
        .collect();
}

pub struct FunctionSpec {
    pub is_agg: bool,
    pub args: FunctionArgSpec,
    pub make_type:
        Box<dyn Fn(&[(String, LogicalSchema)], &[LogicalSchema]) -> LogicalSchema + Sync + 'static>,
}

pub struct FunctionArgSpec {
    pub named: Vec<(String, Vec<LogicalSchema>)>,
    pub variadic: Vec<LogicalSchema>,
}

impl FunctionArgSpec {
    pub fn new(
        named: impl IntoIterator<Item = (String, Vec<LogicalSchema>)>,
        variadic: impl IntoIterator<Item = LogicalSchema>,
    ) -> Self {
        Self {
            named: named.into_iter().collect(),
            variadic: variadic.into_iter().collect(),
        }
    }
}

fn make_datetime_trunc_function() -> FunctionSpec {
    FunctionSpec {
        is_agg: false,
        args: FunctionArgSpec::new(
            [(
                "expr".to_string(),
                vec![json!({"type": "string", "format": "date-time"})],
            )],
            [],
        ),
        make_type: Box::new(make_datetime_type),
    }
}

fn make_datetime_type(_args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    json!({"type": "string", "format": "date-time"})
}

fn make_date_type(_args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    json!({"type": "string", "format": "date"})
}

fn make_string_type(_args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    json!({"type": "string"})
}

fn make_integer_type(_args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    json!({"type": "integer"})
}

fn make_nullable_integer_type(
    _args: &[(String, LogicalSchema)],
    _var: &[LogicalSchema],
) -> LogicalSchema {
    json!({"type": ["integer", "null"]})
}

fn make_nullable_number_type(
    _args: &[(String, LogicalSchema)],
    _var: &[LogicalSchema],
) -> LogicalSchema {
    json!({"type": ["number", "null"]})
}

fn make_any_type(_args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    json!({})
}

fn make_coalesce_like_function() -> FunctionSpec {
    FunctionSpec {
        is_agg: false,
        args: FunctionArgSpec::new([], vec![json!({})]),
        make_type: Box::new(|_, variadic| {
            // For now, return a generic type. In the TypeScript version, this uses
            // coerceAllTypes to find the weakest common type among all arguments.
            // This would require implementing the type coercion logic.
            if variadic.is_empty() {
                json!({})
            } else {
                // Simple implementation: just return the first argument's type
                variadic[0].clone()
            }
        }),
    }
}

fn make_nullif_type(args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    // In the TypeScript version, this coerces the types of left and right arguments
    // and returns the weakest common type with null added.
    // For now, we'll return the left argument's type.
    if let Some((_, schema)) = args.get(0) {
        schema.clone()
    } else {
        json!({})
    }
}

fn make_sum_type(args: &[(String, LogicalSchema)], _var: &[LogicalSchema]) -> LogicalSchema {
    // Check if the expr argument is an integer type
    if let Some((_, schema)) = args.get(0) {
        if let Some(type_val) = schema.get("type") {
            if type_val == "integer" {
                return json!({"type": ["integer", "null"]});
            }
        }
    }
    json!({"type": ["number", "null"]})
}
