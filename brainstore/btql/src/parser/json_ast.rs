use serde::{Deserialize, Serialize};
use util::ptree::{<PERSON><PERSON><PERSON><PERSON>, TreeBuilder};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialize, Deserialize)]
pub struct Position {
    line: u32,
    col: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Location {
    start: Position,
    end: Position,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "op")]
pub enum Expr {
    #[serde(rename = "literal")]
    Literal {
        value: LiteralValue,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "interval")]
    Interval {
        value: i64,
        unit: IntervalUnit,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "ident")]
    Ident {
        name: Vec<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "star")]
    Star {
        #[serde(skip_serializing_if = "Option::is_none")]
        replace: Option<std::collections::HashMap<String, Box<Expr>>>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "function")]
    Function {
        name: Box<Expr>,
        args: Vec<Box<Expr>>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "eq")]
    Eq {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "is")]
    Is {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "ne")]
    Ne {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "lt")]
    Lt {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "le")]
    Le {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "gt")]
    Gt {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "ge")]
    Ge {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "ilike")]
    Ilike {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "like")]
    Like {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "match")]
    Match {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "includes")]
    Includes {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "and")]
    And {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "or")]
    Or {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "if")]
    If {
        conds: Vec<TernaryCond>,
        else_expr: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "add")]
    Add {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "sub")]
    Sub {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "mul")]
    Mul {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "div")]
    Div {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "mod")]
    Mod {
        left: Box<Expr>,
        right: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "neg")]
    Neg {
        expr: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "not")]
    Not {
        expr: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "isnull")]
    IsNull {
        expr: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "isnotnull")]
    IsNotnull {
        expr: Box<Expr>,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
    #[serde(rename = "btql")]
    BtqlSnippet {
        btql: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        loc: Option<Location>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum LiteralValue {
    Null,
    Boolean(bool),
    Integer(i64),
    Number(f64),
    String(String),
    DateTime(String),
    Array(Vec<LiteralValue>),
    Object(std::collections::HashMap<String, LiteralValue>),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum IntervalUnit {
    Year,
    Month,
    Day,
    Hour,
    Minute,
    Second,
    Millisecond,
    Microsecond,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComparisonOp {
    Eq,
    Is,
    Ne,
    Lt,
    Le,
    Gt,
    Ge,
    Ilike,
    Like,
    Match,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BooleanOp {
    And,
    Or,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TernaryCond {
    cond: Box<Expr>,
    then: Box<Expr>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UnaryArithmeticOp {
    Neg,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UnaryOp {
    Not,
    Isnull,
    Isnotnull,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ArithmeticOp {
    Add,
    Sub,
    Mul,
    Div,
    Mod,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AliasExpr {
    expr: Box<Expr>,
    alias: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnpivotAliasExpr {
    expr: Box<Expr>,
    alias: UnpivotAlias,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum UnpivotAlias {
    Single(String),
    Pair(String, String),
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum SortDirection {
    #[serde(rename = "asc")]
    Asc,
    #[serde(rename = "desc")]
    Desc,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortExpr {
    expr: Box<Expr>,
    dir: SortDirection,
    #[serde(skip_serializing_if = "Option::is_none")]
    loc: Option<Location>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum SelectItem {
    AliasExpr(AliasExpr),
    Star(Expr),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum FromItem {
    Ident(Expr),
    Function(Expr),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedQuery {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub dimensions: Option<Vec<AliasExpr>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pivot: Option<Vec<AliasExpr>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub unpivot: Option<Vec<UnpivotAliasExpr>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub measures: Option<Vec<AliasExpr>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub select: Option<Vec<SelectItem>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub filter: Option<Box<Expr>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub from: Option<FromItem>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sort: Option<Vec<SortExpr>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub limit: Option<i64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub cursor: Option<String>,
}

impl MakePTree for SortDirection {
    fn label(&self) -> String {
        format!("{:?}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}
