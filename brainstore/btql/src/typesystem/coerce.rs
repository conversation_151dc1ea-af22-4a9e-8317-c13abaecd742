use lazy_static::lazy_static;
use regex::Regex;
use serde_json::Number;
use std::borrow::Cow;
use time::macros::format_description;
use util::serde_json::Value;

use crate::binder::types::coerce_scalar_types;
use crate::schema::ScalarType;

use super::error::{Result, TypesystemError};
use super::CastInto;

lazy_static! {
    static ref DATE_REGEX: Regex = Regex::new(r"^\d{4}-\d{2}-\d{2}$").unwrap();
}

pub fn normalize_to_scalar_type<'a>(value: Cow<'a, Value>) -> Result<(Cow<'a, Value>, ScalarType)> {
    let scalar_type = value.scalar_type();
    Ok((
        cast_to_scalar_type_impl(value, scalar_type, false)?,
        scalar_type,
    ))
}

fn normalize_to_scalar_type_for_comparison<'a>(
    value: Cow<'a, Value>,
) -> Result<(Cow<'a, Value>, ScalarType)> {
    let scalar_type = value.scalar_type();
    Ok((
        cast_to_scalar_type_impl(value, scalar_type, true)?,
        scalar_type,
    ))
}

pub fn coerce<'a>(a: &'a Value, b: &'a Value) -> Result<(Cow<'a, Value>, Cow<'a, Value>)> {
    let (a_cow, a_type) = normalize_to_scalar_type_for_comparison(Cow::Borrowed(a))?;
    let (b_cow, b_type) = normalize_to_scalar_type_for_comparison(Cow::Borrowed(b))?;

    let (coerced_a, coerced_b) = coerce_scalar_types(a_type, b_type);

    Ok(match (coerced_a, coerced_b) {
        (None, None) => (a_cow, b_cow),
        (Some(coerced_a), None) => (cast_to_scalar_type_impl(a_cow, coerced_a, true)?, b_cow),
        (None, Some(coerced_b)) => (a_cow, cast_to_scalar_type_impl(b_cow, coerced_b, true)?),
        (Some(coerced_a), Some(coerced_b)) => (
            cast_to_scalar_type_impl(a_cow, coerced_a, true)?,
            cast_to_scalar_type_impl(b_cow, coerced_b, true)?,
        ),
    })
}

pub fn cast_to_scalar_type<'a>(
    value: Cow<'a, Value>,
    scalar_type: ScalarType,
) -> Result<Cow<'a, Value>> {
    cast_to_scalar_type_impl(value, scalar_type, false)
}

fn cast_to_scalar_type_impl<'a>(
    value: Cow<'a, Value>,
    scalar_type: ScalarType,
    make_comparable: bool,
) -> Result<Cow<'a, Value>> {
    if let Value::Null = value.as_ref() {
        return Ok(value);
    }
    Ok(match scalar_type {
        ScalarType::Null => Cow::Owned(Value::Null),
        ScalarType::Boolean => match value.as_ref() {
            Value::Bool(_) => value,
            _ => Cow::Owned(Value::Bool(value.as_ref().cast()?)),
        },
        ScalarType::Integer => match value.as_ref() {
            // We could generalize this to better support numbers between INT64_MAX and UINT64_MAX
            Value::Number(n) if n.is_i64() => value,
            _ => {
                let i64_value: i64 = value.as_ref().cast()?;
                Cow::Owned(Value::Number(Number::from(i64_value)))
            }
        },
        ScalarType::Number => match value.as_ref() {
            Value::Number(n) if n.is_f64() => value,
            _ => {
                let f64_value: f64 = value.as_ref().cast()?;
                Cow::Owned(Value::Number(Number::from_f64(f64_value).ok_or_else(
                    || TypesystemError::InvalidCast {
                        from: format!("{:?}", value.scalar_type()),
                        to: format!("{:?}", scalar_type),
                        value: value.into_owned(),
                    },
                )?))
            }
        },
        ScalarType::String => match value.as_ref() {
            Value::String(_) => value,
            _ => Cow::Owned(Value::String(value.as_ref().cast()?)),
        },
        ScalarType::Array => match value.as_ref() {
            Value::Array(_) => value,
            _ => {
                return Err(TypesystemError::InvalidCast {
                    from: format!("{:?}", value.scalar_type()),
                    to: format!("{:?}", scalar_type),
                    value: value.into_owned(),
                })
            }
        },
        ScalarType::Object => match value.as_ref() {
            Value::Object(_) => value,
            _ => {
                return Err(TypesystemError::InvalidCast {
                    from: format!("{:?}", value.scalar_type()),
                    to: format!("{:?}", scalar_type),
                    value: value.into_owned(),
                })
            }
        },
        ScalarType::Date => {
            // XXX TODO: Parse dates properly. Right now, we just assume they're the same as string representation
            if let Value::String(s) = value.as_ref() {
                if DATE_REGEX.is_match(s) {
                    return Ok(value);
                }
            }
            return Err(TypesystemError::InvalidCast {
                from: format!("{:?}", value.scalar_type()),
                to: format!("{:?}", scalar_type),
                value: value.into_owned(),
            });
        }
        ScalarType::DateTime => {
            if make_comparable {
                let date_value: tantivy_common::DateTime = value.as_ref().cast()?;
                let date_string = cast_datetime_to_comparable_string(&date_value)?;
                Cow::Owned(Value::String(date_string))
            } else {
                if let Value::String(s) = value.as_ref() {
                    if DATE_REGEX.is_match(s) {
                        return Ok(value);
                    }
                }
                let date_value: tantivy_common::DateTime = value.as_ref().cast()?;
                Cow::Owned(Value::String(date_value.cast()?))
            }
        }
        ScalarType::Interval => {
            return Err(TypesystemError::Unsupported {
                msg: "Interval value conversions".to_string(),
                value: value.into_owned(),
            })
        }
        ScalarType::Unknown => value,
    })
}

fn cast_datetime_to_comparable_string(date: &tantivy_common::DateTime) -> Result<String> {
    let nanos = date.into_timestamp_nanos();
    let datetime =
        time::OffsetDateTime::from_unix_timestamp_nanos(nanos as i128).map_err(|_| {
            TypesystemError::InvalidCast {
                from: "datetime".to_string(),
                to: "string".to_string(),
                value: date.into_timestamp_secs().into(),
            }
        })?;

    datetime
        .format(format_description!(
            "[year]-[month]-[day]T[hour]:[minute]:[second].[subsecond digits:9]Z"
        ))
        .map_err(|_| TypesystemError::InvalidCast {
            from: "datetime".to_string(),
            to: "string".to_string(),
            value: date.into_timestamp_secs().into(),
        })
}

pub trait HasScalarType {
    fn scalar_type(&self) -> ScalarType;
}

impl HasScalarType for util::serde_json::Value {
    fn scalar_type(&self) -> ScalarType {
        match self {
            util::serde_json::Value::Null => ScalarType::Null,
            util::serde_json::Value::Bool(_) => ScalarType::Boolean,
            util::serde_json::Value::Number(_) => {
                if self.is_i64() || self.is_u64() {
                    ScalarType::Integer
                } else {
                    ScalarType::Number
                }
            }
            util::serde_json::Value::String(_) => {
                match CastInto::<tantivy_common::DateTime>::cast(self) {
                    // XXX: This is very sad and slow. We should bake this into the value type.
                    Ok(_) => ScalarType::DateTime,
                    Err(_) => ScalarType::String,
                }
            }
            util::serde_json::Value::Array(_) => ScalarType::Array,
            util::serde_json::Value::Object(_) => ScalarType::Object,
        }
    }
}

pub fn normalize_value_for_comparison(value: Value) -> Value {
    match value {
        // Semantically, we do not distinguish between:
        // - null
        // - {}
        // - { "foo": null }
        Value::Object(map) => {
            // Remove null values
            let mut normalized_map = serde_json::Map::new();
            for (key, value) in map.iter() {
                let normalized_value = normalize_value_for_comparison(value.clone());
                if !matches!(normalized_value, Value::Null) {
                    normalized_map.insert(key.clone(), normalized_value);
                }
            }
            if normalized_map.is_empty() {
                Value::Null
            } else {
                Value::Object(normalized_map)
            }
        }
        // Semantically, we do not distinguish between:
        // - null
        // - []
        Value::Array(arr) => {
            let array = arr
                .into_iter()
                .map(normalize_value_for_comparison)
                .collect::<Vec<_>>();

            // We can't distinguish in columnar settings between no elements and null.
            if array.is_empty() {
                Value::Null
            } else {
                Value::Array(array)
            }
        }
        Value::Number(num) => {
            if !num.is_f64() {
                return Value::Number(num);
            }
            // If the i64 version matches the f64 version, use the i64 version
            let num_f64 = num.as_f64();
            let num_i64 = num.as_f64().map(|n| (n as i64) as f64);
            match (num_i64, num_f64) {
                (Some(n), Some(f)) if n == f => Value::Number((f as i64).into()),
                _ => Value::Number(num),
            }
        }
        Value::String(s) => {
            if let Ok(date) = CastInto::<tantivy_common::DateTime>::cast(&s) {
                return Value::String(date.cast().unwrap());
            }
            if let Ok(i) = s.parse::<u64>() {
                if i.to_string() == s {
                    // If the string can be parsed as a u64 and matches the original string, we return it as a number
                    return Value::Number(Number::from(i));
                }
            } else if let Ok(i) = s.parse::<i64>() {
                if i.to_string() == s {
                    // If the string can be parsed as an i64 and matches the original string, we return it as a number
                    return Value::Number(Number::from(i));
                }
            } else if let Ok(f) = s.parse::<f64>() {
                if f.to_string() == s {
                    // If the string can be parsed as a f64 and matches the original string, we return it as a number
                    return Value::Number(Number::from_f64(f).unwrap());
                }
            }
            Value::String(s)
        }
        _ => value,
    }
}
