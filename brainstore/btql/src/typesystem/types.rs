use time::{Duration, OffsetDateTime};

#[derive(Debug, <PERSON>lone)]
pub enum CalendarDuration {
    Fixed(Duration),
    Months(i32),
    Years(i32),
}

impl CalendarDuration {
    pub fn negate(&self) -> CalendarDuration {
        match self {
            CalendarDuration::Fixed(dur) => CalendarDuration::Fixed(-*dur),
            CalendarDuration::Months(m) => CalendarDuration::Months(-*m),
            CalendarDuration::Years(y) => CalendarDuration::Years(-*y),
        }
    }
}

#[derive(Debug)]
pub enum ArithmeticType {
    Null,
    Integer(i64),
    Number(f64),
    DateTime(OffsetDateTime),
    Duration(CalendarDuration),
}
