#[test]
fn test_cast_date() {
    use crate::typesystem::CastInto;
    use tantivy::DateTime;

    enum Expected {
        Error,
        Exact,             // Tests if roundtrip matches input exactly
        Roundtrip(String), // Tests if roundtrip matches expected string
    }

    let test_cases = vec![
        // RFC3339 datetime strings that should match exactly
        ("2023-05-15T14:30:00Z", Expected::Exact),
        (
            "2023-05-15T14:30:00+00:00",
            Expected::Roundtrip("2023-05-15T14:30:00Z".to_string()),
        ),
        (
            "2023-05-15T14:30:00.123+00:00",
            Expected::Roundtrip("2023-05-15T14:30:00.123Z".to_string()),
        ),
        // Timezone conversions (normalized to UTC)
        (
            "2023-05-15T14:30:00+02:00",
            Expected::Roundtrip("2023-05-15T12:30:00Z".to_string()),
        ),
        (
            "2023-05-15T14:30:00-05:00",
            Expected::Roundtrip("2023-05-15T19:30:00Z".to_string()),
        ),
        // Plain dates (should convert to midnight UTC)
        (
            "2023-05-15",
            Expected::Roundtrip("2023-05-15T00:00:00Z".to_string()),
        ),
        (
            "2023-12-31",
            Expected::Roundtrip("2023-12-31T00:00:00Z".to_string()),
        ),
        (
            "2024-02-29",
            Expected::Roundtrip("2024-02-29T00:00:00Z".to_string()),
        ), // Leap year
        // Invalid inputs
        ("2023-13-45", Expected::Error), // Invalid month/day
        ("not a date", Expected::Error), // Not a date at all
        ("2023-05-15T25:00:00Z", Expected::Error), // Invalid hour
        ("2023-02-29", Expected::Error), // Invalid leap year date
        ("2023-04-31", Expected::Error), // Invalid day for month
    ];

    for (input, expected) in test_cases {
        let result: Result<DateTime, _> = input.cast();

        match expected {
            Expected::Error => {
                assert!(result.is_err(), "Expected error for input: {}", input);
            }
            Expected::Exact => {
                let dt = result.unwrap_or_else(|e| panic!("Failed to parse {}: {}", input, e));
                let as_str: String = dt.cast().unwrap();
                assert_eq!(input, as_str, "Failed exact roundtrip for input: {}", input);
            }
            Expected::Roundtrip(expected_str) => {
                let dt = result.unwrap_or_else(|e| panic!("Failed to parse {}: {}", input, e));
                let as_str: String = dt.cast().unwrap();
                assert_eq!(
                    expected_str, as_str,
                    "Failed roundtrip for input: {}",
                    input
                );
            }
        }
    }
}
