use time::format_description::well_known::Rfc3339;
use time::{Duration, OffsetDateTime};
use util::anyhow::Context;
use util::xact::PaginationKey;

use super::error::Result;
use super::types::{ArithmeticType, CalendarDuration};

// The more advanced version of this trait would return a CoW<T> instead of T
pub trait CastInto<T> {
    fn cast(&self) -> Result<T>;
}

pub trait CastFrom<T>: Sized {
    fn cast_from(value: &T) -> Result<Self>;
}

impl<T, U> CastFrom<T> for U
where
    T: CastInto<U>,
{
    fn cast_from(value: &T) -> Result<Self> {
        value.cast()
    }
}

impl<T> CastInto<T> for T
where
    T: Clone,
{
    fn cast(&self) -> Result<T> {
        Ok(self.clone())
    }
}

macro_rules! impl_cast_into_string {
    ($($t:ty),*) => {
        $(
            impl CastInto<String> for $t {
                fn cast(&self) -> Result<String> {
                    Ok(self.to_string())
                }
            }
        )*
    };
}

impl_cast_into_string!(u64, i64, f64, bool);

macro_rules! impl_cast_into_numeric {
    ($from:ty, $to:ty) => {
        impl CastInto<$to> for $from {
            fn cast(&self) -> Result<$to> {
                (*self).try_into().map_err(|_| {
                    crate::typesystem::error::TypesystemError::InvalidCast {
                        from: stringify!($from).to_string(),
                        to: stringify!($to).to_string(),
                        value: util::serde_json::Value::Number(self.clone().into()),
                    }
                })
            }
        }
    };
}

macro_rules! impl_cast_into_as {
    ($from:ty, $to:ty) => {
        impl CastInto<$to> for $from {
            #[inline(always)]
            fn cast(&self) -> Result<$to> {
                Ok(*self as $to)
            }
        }
    };
}

impl_cast_into_numeric!(i64, u64);
impl_cast_into_as!(i64, f64);
impl_cast_into_numeric!(u64, i64);
impl_cast_into_as!(u64, f64);
impl_cast_into_as!(f64, i64);
impl_cast_into_as!(f64, u64);
impl_cast_into_as!(bool, u64);
impl_cast_into_as!(bool, i64);

impl CastInto<f64> for bool {
    fn cast(&self) -> Result<f64> {
        Ok(*self as i64 as f64)
    }
}

impl CastInto<bool> for u64 {
    fn cast(&self) -> Result<bool> {
        Ok(*self != 0)
    }
}
impl CastInto<bool> for i64 {
    fn cast(&self) -> Result<bool> {
        Ok(*self != 0)
    }
}
impl CastInto<bool> for f64 {
    fn cast(&self) -> Result<bool> {
        Ok(*self != 0.0)
    }
}
impl CastInto<bool> for tantivy_common::DateTime {
    fn cast(&self) -> Result<bool> {
        Ok(self.into_timestamp_secs() != 0)
    }
}
impl CastInto<tantivy_common::DateTime> for bool {
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        Ok(tantivy_common::DateTime::from_timestamp_secs(if *self {
            1
        } else {
            0
        }))
    }
}

macro_rules! impl_cast_into_numeric_from_string {
    ($($t:ty),*) => {
        $(
            impl CastInto<$t> for str {
                fn cast(&self) -> Result<$t> {
                    Ok(self.parse().with_context(|| format!("Failed to cast string to {}", stringify!($t)))?)
                }
            }

            impl CastInto<$t> for String {
                fn cast(&self) -> Result<$t> {
                    Ok(self.parse().with_context(|| format!("Failed to cast string to {}", stringify!($t)))?)
                }
            }
        )*
    };
}

impl_cast_into_numeric_from_string!(u64, i64, f64, bool);

impl CastInto<String> for tantivy_common::DateTime {
    fn cast(&self) -> Result<String> {
        let nanos = self.into_timestamp_nanos();
        let datetime =
            time::OffsetDateTime::from_unix_timestamp_nanos(nanos as i128).map_err(|_| {
                crate::typesystem::error::TypesystemError::InvalidCast {
                    from: "datetime".to_string(),
                    to: "string".to_string(),
                    value: self.into_timestamp_secs().into(),
                }
            })?;

        datetime.format(&Rfc3339).map_err(|_| {
            crate::typesystem::error::TypesystemError::InvalidCast {
                from: "datetime".to_string(),
                to: "string".to_string(),
                value: self.into_timestamp_secs().into(),
            }
        })
    }
}

impl CastInto<tantivy_common::DateTime> for str {
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        let datetime = if let Ok(dt) = time::OffsetDateTime::parse(self, &Rfc3339) {
            dt
        } else if let Ok(date) = time::Date::parse(
            self,
            &time::format_description::parse("[year]-[month]-[day]").map_err(|_| {
                crate::typesystem::error::TypesystemError::InvalidCast {
                    from: "string".to_string(),
                    to: "datetime".to_string(),
                    value: self.into(),
                }
            })?,
        ) {
            // Convert naive date to DateTime by setting time to midnight UTC
            date.with_time(time::Time::MIDNIGHT).assume_utc()
        } else {
            return Err(crate::typesystem::error::TypesystemError::InvalidCast {
                from: "string".to_string(),
                to: "datetime".to_string(),
                value: self.into(),
            });
        };

        Ok(tantivy_common::DateTime::from_timestamp_nanos(
            datetime.unix_timestamp_nanos() as i64,
        ))
    }
}

impl CastInto<tantivy_common::DateTime> for String {
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        self.as_str().cast()
    }
}

impl CastInto<tantivy_common::DateTime> for i64 {
    #[inline(always)]
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        Ok(tantivy_common::DateTime::from_timestamp_secs(*self))
    }
}

impl CastInto<tantivy_common::DateTime> for u64 {
    #[inline(always)]
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        Ok(tantivy_common::DateTime::from_timestamp_secs(*self as i64))
    }
}

impl CastInto<tantivy_common::DateTime> for f64 {
    #[inline(always)]
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        // Gotta be careful about this -- it likely overflows
        Ok(tantivy_common::DateTime::from_timestamp_nanos(
            (*self * 1_000_000_000.0) as i64,
        ))
    }
}

impl CastInto<i64> for tantivy_common::DateTime {
    #[inline(always)]
    fn cast(&self) -> Result<i64> {
        Ok(self.into_timestamp_secs())
    }
}

impl CastInto<u64> for tantivy_common::DateTime {
    #[inline(always)]
    fn cast(&self) -> Result<u64> {
        Ok(self.into_timestamp_secs() as u64)
    }
}

impl CastInto<f64> for tantivy_common::DateTime {
    #[inline(always)]
    fn cast(&self) -> Result<f64> {
        Ok(self.into_timestamp_nanos() as f64 / 1_000_000_000.0)
    }
}

impl CastInto<String> for util::serde_json::Value {
    fn cast(&self) -> Result<String> {
        match self {
            util::serde_json::Value::String(s) => Ok(s.clone()),
            util::serde_json::Value::Number(n) => Ok(n.to_string()),
            util::serde_json::Value::Bool(b) => Ok(b.to_string()),
            util::serde_json::Value::Array(val) if !value_is_null(self) => {
                Ok(serde_json::to_string(&val).context("Failed to cast array to string")?)
            }
            util::serde_json::Value::Object(val) if !value_is_null(self) => {
                Ok(serde_json::to_string(&val).context("Cannot cast object to string")?)
            }
            _ => Ok("null".to_string()),
        }
    }
}

macro_rules! impl_cast_into_numeric_for_value {
    ($($t:ty),+) => {
        $(
            impl CastInto<$t> for util::serde_json::Value {
                fn cast(&self) -> Result<$t> {
                    match self {
                        util::serde_json::Value::Number(n) => {
                            if let Some(u) = n.as_u64() {
                                Ok(u as $t)
                            } else if let Some(i) = n.as_i64() {
                                Ok(i as $t)
                            } else if let Some(f) = n.as_f64() {
                                Ok(f as $t)
                            } else {
                                Err(crate::typesystem::error::TypesystemError::InvalidCast {
                                    from: "number".to_string(),
                                    to: stringify!($t).to_string(),
                                    value: self.clone(),
                                })
                            }
                        }
                        util::serde_json::Value::Bool(b) => Ok(*b as i64 as $t),
                        util::serde_json::Value::String(s) => Ok(s.cast()?),
                        _ => Err(crate::typesystem::error::TypesystemError::InvalidCast {
                            from: "value".to_string(),
                            to: stringify!($t).to_string(),
                            value: self.clone(),
                        }),
                    }
                }
            }
        )+
    };
}

impl_cast_into_numeric_for_value!(u64, i64, f64);

impl CastInto<bool> for util::serde_json::Value {
    fn cast(&self) -> Result<bool> {
        match self {
            util::serde_json::Value::Bool(b) => Ok(*b),
            util::serde_json::Value::Number(n) => Ok(n.as_f64().unwrap_or(0.0) != 0.0),
            util::serde_json::Value::String(s) => match s.to_lowercase().as_str() {
                "false" | "0" | "" => Ok(false),
                "true" | "1" => Ok(true),
                _ => Err(crate::typesystem::error::TypesystemError::InvalidCast {
                    from: "string".to_string(),
                    to: "bool".to_string(),
                    value: self.clone(),
                }),
            },
            util::serde_json::Value::Null => Ok(false),
            util::serde_json::Value::Array(_) | util::serde_json::Value::Object(_) => {
                Ok(!value_is_null(self))
            }
        }
    }
}

impl CastInto<tantivy_common::DateTime> for util::serde_json::Value {
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        match self {
            util::serde_json::Value::String(s) => Ok(s.cast()?),
            util::serde_json::Value::Number(n) => {
                if let Some(u) = n.as_u64() {
                    Ok(u.cast()?)
                } else if let Some(i) = n.as_i64() {
                    Ok(i.cast()?)
                } else if let Some(f) = n.as_f64() {
                    Ok(f.cast()?)
                } else {
                    Err(crate::typesystem::error::TypesystemError::InvalidCast {
                        from: "number".to_string(),
                        to: "datetime".to_string(),
                        value: self.clone(),
                    })
                }
            }
            _ => Err(crate::typesystem::error::TypesystemError::InvalidCast {
                from: "value".to_string(),
                to: "datetime".to_string(),
                value: self.clone(),
            }),
        }
    }
}

impl CastInto<()> for util::serde_json::Value {
    fn cast(&self) -> Result<()> {
        Ok(())
    }
}

impl CastInto<serde_json::Value> for u64 {
    fn cast(&self) -> Result<serde_json::Value> {
        Ok((*self).into())
    }
}

impl CastInto<i128> for util::serde_json::Value {
    fn cast(&self) -> Result<i128> {
        match self {
            util::serde_json::Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    Ok(i as i128)
                } else if let Some(u) = n.as_u64() {
                    Ok(u as i128)
                } else {
                    Err(crate::typesystem::error::TypesystemError::InvalidCast {
                        from: "number".to_string(),
                        to: "i128".to_string(),
                        value: self.clone(),
                    })
                }
            }
            _ => Err(crate::typesystem::error::TypesystemError::InvalidCast {
                from: "value".to_string(),
                to: "i128".to_string(),
                value: self.clone(),
            }),
        }
    }
}

impl CastInto<serde_json::Value> for i128 {
    fn cast(&self) -> Result<serde_json::Value> {
        if let Ok(i) = i64::try_from(*self) {
            Ok(serde_json::Value::Number(i.into()))
        } else if let Ok(u) = u64::try_from(*self) {
            Ok(serde_json::Value::Number(u.into()))
        } else {
            Err(crate::typesystem::error::TypesystemError::InvalidCast {
                from: "i128".to_string(),
                to: "Value".to_string(),
                value: util::serde_json::Value::String(self.to_string()),
            })
        }
    }
}

impl CastInto<serde_json::Value> for () {
    fn cast(&self) -> Result<serde_json::Value> {
        Ok(().into())
    }
}

impl CastInto<serde_json::Value> for bool {
    fn cast(&self) -> Result<serde_json::Value> {
        Ok((*self).into())
    }
}

impl CastInto<serde_json::Value> for f64 {
    fn cast(&self) -> Result<serde_json::Value> {
        Ok((*self).into())
    }
}

impl CastInto<serde_json::Value> for String {
    fn cast(&self) -> Result<serde_json::Value> {
        Ok(self.as_str().into())
    }
}

pub fn value_is_null(value: &util::serde_json::Value) -> bool {
    match value {
        util::serde_json::Value::Null => true,
        util::serde_json::Value::Array(arr) => arr.is_empty(),
        util::serde_json::Value::Object(map) => map.is_empty() || map.values().all(value_is_null),
        _ => false,
    }
}

impl CastInto<CalendarDuration> for String {
    fn cast(&self) -> Result<CalendarDuration> {
        let parts: Vec<&str> = self.trim().splitn(2, ' ').collect();
        if parts.len() != 2 {
            return Err(crate::typesystem::error::TypesystemError::InvalidCast {
                from: "string".to_string(),
                to: "CalendarDuration".to_string(),
                value: util::serde_json::Value::String(self.clone()),
            });
        }

        let value: i32 = parts[0].parse().map_err(|_| {
            crate::typesystem::error::TypesystemError::InvalidCast {
                from: "string".to_string(),
                to: "CalendarDuration".to_string(),
                value: util::serde_json::Value::String(self.clone()),
            }
        })?;

        match parts[1].trim().to_lowercase().as_str() {
            "microsecond" | "microseconds" => Ok(CalendarDuration::Fixed(Duration::microseconds(
                value as i64,
            ))),
            "millisecond" | "milliseconds" => Ok(CalendarDuration::Fixed(Duration::milliseconds(
                value as i64,
            ))),
            "second" | "seconds" => Ok(CalendarDuration::Fixed(Duration::seconds(value as i64))),
            "minute" | "minutes" => Ok(CalendarDuration::Fixed(Duration::minutes(value as i64))),
            "hour" | "hours" => Ok(CalendarDuration::Fixed(Duration::hours(value as i64))),
            "day" | "days" => Ok(CalendarDuration::Fixed(Duration::days(value as i64))),
            "month" | "months" => Ok(CalendarDuration::Months(value)),
            "year" | "years" => Ok(CalendarDuration::Years(value)),
            _ => Err(crate::typesystem::error::TypesystemError::InvalidCast {
                from: "string".to_string(),
                to: "CalendarDuration".to_string(),
                value: util::serde_json::Value::String(self.clone()),
            }),
        }
    }
}

impl CastInto<ArithmeticType> for serde_json::Value {
    fn cast(&self) -> Result<ArithmeticType> {
        match self {
            serde_json::Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    Ok(ArithmeticType::Integer(i))
                } else if let Some(f) = n.as_f64() {
                    Ok(ArithmeticType::Number(f))
                } else {
                    Err(crate::typesystem::error::TypesystemError::InvalidCast {
                        from: "number".to_string(),
                        to: "ArithmeticType".to_string(),
                        value: self.clone(),
                    })
                }
            }
            serde_json::Value::Bool(_) => Ok(ArithmeticType::Number(self.cast().unwrap())),
            serde_json::Value::String(s) => {
                if let Ok(dt) = OffsetDateTime::parse(s, &Rfc3339) {
                    Ok(ArithmeticType::DateTime(dt))
                } else if let Ok(duration) = s.cast() {
                    Ok(ArithmeticType::Duration(duration))
                } else if let Ok(num) = s.cast() {
                    Ok(ArithmeticType::Number(num))
                } else {
                    Ok(ArithmeticType::Null)
                }
            }
            _ => Ok(ArithmeticType::Null),
        }
    }
}

impl CastInto<tantivy_common::DateTime> for PaginationKey {
    fn cast(&self) -> Result<tantivy_common::DateTime> {
        let timestamp = self.0 as i64;
        Ok(tantivy_common::DateTime::from_timestamp_secs(timestamp))
    }
}
impl CastInto<PaginationKey> for tantivy_common::DateTime {
    fn cast(&self) -> Result<PaginationKey> {
        let timestamp = self.into_timestamp_secs();
        Ok(PaginationKey(timestamp as u64))
    }
}

impl CastInto<f64> for PaginationKey {
    fn cast(&self) -> Result<f64> {
        Ok(self.0 as f64)
    }
}
impl CastInto<PaginationKey> for f64 {
    fn cast(&self) -> Result<PaginationKey> {
        Ok(PaginationKey(*self as u64))
    }
}

impl CastInto<String> for PaginationKey {
    fn cast(&self) -> Result<String> {
        Ok(self.0.to_string())
    }
}
impl CastInto<PaginationKey> for String {
    fn cast(&self) -> Result<PaginationKey> {
        self.parse::<u64>().map(PaginationKey).map_err(|_| {
            crate::typesystem::error::TypesystemError::InvalidCast {
                from: "string".to_string(),
                to: "PaginationKey".to_string(),
                value: util::serde_json::Value::String(self.clone()),
            }
        })
    }
}

impl CastInto<i64> for PaginationKey {
    fn cast(&self) -> Result<i64> {
        Ok(self.0 as i64)
    }
}
impl CastInto<PaginationKey> for i64 {
    fn cast(&self) -> Result<PaginationKey> {
        Ok(PaginationKey(*self as u64))
    }
}

impl CastInto<u64> for PaginationKey {
    fn cast(&self) -> Result<u64> {
        Ok(self.0)
    }
}
impl CastInto<PaginationKey> for u64 {
    fn cast(&self) -> Result<PaginationKey> {
        Ok(PaginationKey(*self))
    }
}

impl CastInto<bool> for PaginationKey {
    fn cast(&self) -> Result<bool> {
        Ok(self.0 != 0)
    }
}
impl CastInto<PaginationKey> for bool {
    fn cast(&self) -> Result<PaginationKey> {
        Ok(PaginationKey(if *self { 1 } else { 0 }))
    }
}
