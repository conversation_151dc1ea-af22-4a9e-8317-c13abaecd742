use thiserror::Error;

#[derive(Error, Debug)]
pub enum TypesystemError {
    #[error("Invalid cast {from} to {to}: {value:?}")]
    InvalidCast {
        from: String,
        to: String,
        value: util::serde_json::Value,
    },

    #[error("Unsupported: {msg}")]
    Unsupported {
        msg: String,
        value: util::serde_json::Value,
    },

    #[error("Internal error: {0}")]
    InternalError(String),

    #[error(transparent)]
    Anyhow(#[from] util::anyhow::Error),
}

pub type Result<T, E = TypesystemError> = std::result::Result<T, E>;
