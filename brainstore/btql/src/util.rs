use base64::{engine::general_purpose::STANDARD_NO_PAD as BASE64_STANDARD, Engine as Base64Engine};
use serde::{Deserialize, Serialize};
use util::{
    anyhow::Context,
    ptree::{MakePTree, TreeBuilder},
};

use crate::binder::ast::SortDirection;

/// A CursorDirection::Max cursor corresponds to an ASC sort and means "the cursor value is the
/// maximum value of the field". Therefore, it can be used as a lower bound for future queries.
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize, PartialEq, Eq, MakePTree)]
pub enum CursorDirection {
    <PERSON>,
    <PERSON>,
}

impl From<SortDirection> for CursorDirection {
    fn from(dir: SortDirection) -> Self {
        match dir {
            SortDirection::Asc => CursorDirection::Max,
            SortDirection::Desc => CursorDirection::Min,
        }
    }
}

pub type CursorValue = u64;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, <PERSON>ialEq, Eq)]
pub struct Cursor {
    pub cursor_value: CursorValue,
}

impl Cursor {
    pub fn new(cursor_value: u64) -> Self {
        Cursor { cursor_value }
    }

    pub fn collect(&mut self, other: CursorValue, agg: CursorDirection) {
        match agg {
            CursorDirection::Max => {
                self.cursor_value = self.cursor_value.max(other);
            }
            CursorDirection::Min => {
                self.cursor_value = self.cursor_value.min(other);
            }
        }
    }
}

impl std::fmt::Display for Cursor {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        if self.cursor_value == u64::MAX {
            write!(f, "{}", "null")
        } else {
            let s = BASE64_STANDARD.encode(self.cursor_value.to_be_bytes().to_vec());
            write!(f, "{}", s)
        }
    }
}

impl Into<serde_json::Value> for Cursor {
    fn into(self) -> serde_json::Value {
        if self.cursor_value == u64::MAX {
            serde_json::Value::Null
        } else {
            serde_json::Value::String(self.to_string())
        }
    }
}

impl TryFrom<&str> for Cursor {
    type Error = util::anyhow::Error;
    fn try_from(value: &str) -> Result<Self, Self::Error> {
        let v = BASE64_STANDARD
            .decode(value)
            .with_context(|| "Cursor is not valid base64")?;
        if v.len() != 8 {
            return Err(util::anyhow::anyhow!("Invalid cursor {}", value));
        }
        Ok(Cursor {
            cursor_value: u64::from_be_bytes(v.try_into().unwrap()),
        })
    }
}

impl TryFrom<&String> for Cursor {
    type Error = util::anyhow::Error;
    fn try_from(value: &String) -> Result<Self, Self::Error> {
        Cursor::try_from(value.as_str())
    }
}

impl MakePTree for Cursor {
    fn label(&self) -> String {
        "Cursor".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        builder.add_empty_child(format!("Cursor value: {}", self.cursor_value));
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn cursor_sanity_test() {
        let cursor_value = 281474976710656_u64;
        let cursor = Cursor { cursor_value };
        let cursor_string = format!("{}", cursor);
        let parsed = Cursor::try_from(&cursor_string).unwrap();
        assert_eq!(parsed, cursor);
    }
}
