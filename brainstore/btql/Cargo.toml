[package]
name = "btql"
version = "0.1.0"
edition = "2021"

[dependencies]
tantivy = { path = "../tantivy", optional = true }
regex-automata = { workspace = true }
util = { path = "../util" }

lazy_static = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
time = { workspace = true }
log = { workspace = true }
base64 = { workspace = true }
thiserror = { workspace = true }
regex = { workspace = true }
tantivy_common = { workspace = true }


[features]
default = ["tantivy"]
tantivy = ["dep:tantivy"]
wasm = ["time/wasm-bindgen"]
