use std::env;
use std::fs;
use std::path::Path;
use std::process::Command;

fn main() {
    let out_dir = env::var("OUT_DIR").expect("OUT_DIR not set");
    let git_commit = env::var("GIT_COMMIT_HASH").unwrap_or_else(|_| {
        String::from_utf8(
            Command::new("git")
                .args(["rev-parse", "HEAD"])
                .output()
                .expect("Failed to execute git command")
                .stdout,
        )
        .expect("Invalid UTF-8 in git output")
        .trim()
        .to_string()
    });

    let dest_path = Path::new(&out_dir).join("git_commit.rs");
    fs::write(
        dest_path,
        format!("pub const GIT_COMMIT: &str = \"{}\";\n", git_commit),
    )
    .expect("Failed to write git_commit.rs");
}
