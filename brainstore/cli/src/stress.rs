use agent::tracing_subscriber::Registry;
use std::time::Duration;
use storage::MAX_STATIC_THREADS;

use serde::{Deserialize, Serialize};
use tracing::instrument;
use util::{anyhow::Context, Value};

use crate::executor_pool::MAX_POOL_THREADS;

#[derive(Debug, Serialize, Deserialize)]
pub struct StressArgs {}

#[tokio::test]
pub async fn thread_stress_test() {
    stress().await.unwrap();
}

pub async fn stress() -> util::anyhow::Result<Value> {
    let subscriber = Registry::default();
    tracing::subscriber::set_global_default(subscriber)?;

    // Make sure that up to the maximum number of pooled threads + static sync threads,
    // we do not run out of threads.
    // To make this fail, add 192 threads (remove the buffer + 1 more set of threads)
    // You should see errors like "Thread count overflowed the configured max count."
    let target_max_threads = MAX_POOL_THREADS + MAX_STATIC_THREADS;
    let threads = 64;
    let current_span = tracing::Span::current();

    let mut executors = Vec::new();

    for i in 0..target_max_threads / threads {
        log::info!("Batch {}", i);
        let pool = ::query::interpreter::context::Executor::multi_thread(threads, "stress")
            .context("Failed to create multi-threaded executor")?;

        pool.map(
            |_| {
                let _guard = current_span.enter();
                stressful_task();
                Ok::<_, tantivy::TantivyError>(())
            },
            vec![0; threads * 2].into_iter(),
        )
        .expect("failed to join");

        executors.push(pool);
    }

    Ok(Value::Null)
}

#[instrument]
fn stressful_task() {
    std::thread::sleep(Duration::from_millis(10));
}
