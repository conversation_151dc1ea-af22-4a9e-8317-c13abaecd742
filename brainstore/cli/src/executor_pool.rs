use deadpool::managed::{self, <PERSON><PERSON><PERSON><PERSON>};
use std::sync::Arc;
use util::anyhow::Context;

// According to https://codebrowser.dev/tokio/crates/sharded-slab-0.1.7/src/shard.rs.html, which is used by the tracing library,
// the maximum number of threads is 4096 (https://github.com/hawkw/sharded-slab/blob/main/src/cfg.rs#L140). I'm not exactly sure
// how this manifests. It might be 4096 threads concurrently creating spans, or something else, but to be safe, we limit the
// number of threads here.
pub const MAX_SYSTEM_THREADS: usize = 4096;

pub struct Manager {
    num_threads: usize,
    name: &'static str,
}

impl managed::Manager for Manager {
    type Type = Arc<query::interpreter::context::Executor>;
    type Error = util::anyhow::Error;

    async fn create(&self) -> Result<Arc<query::interpreter::context::Executor>, Self::Error> {
        let executor = if self.num_threads > 1 {
            ::query::interpreter::context::Executor::multi_thread(self.num_threads, self.name)
                .context("Failed to create multi-threaded executor")?
        } else {
            ::query::interpreter::context::Executor::single_thread()
        };
        Ok(Arc::new(executor))
    }

    async fn recycle(
        &self,
        _: &mut Arc<query::interpreter::context::Executor>,
        _: &managed::Metrics,
    ) -> managed::RecycleResult<Self::Error> {
        Ok(())
    }
}

const THREAD_BUFFER: usize = 128;
pub const MAX_POOL_THREADS: usize =
    MAX_SYSTEM_THREADS - storage::MAX_STATIC_THREADS - THREAD_BUFFER;

pub type ExecutorPool = managed::Pool<Manager>;
pub fn make_executor_pool(num_threads: usize, name: &'static str) -> PoolBuilder<Manager> {
    let manager = Manager { num_threads, name };
    let max_size = MAX_POOL_THREADS / num_threads;
    if max_size == 0 {
        panic!(
            "Too many threads configured (at most {} threads are supported)",
            MAX_POOL_THREADS
        );
    }
    managed::Pool::builder(manager).max_size(max_size)
}
