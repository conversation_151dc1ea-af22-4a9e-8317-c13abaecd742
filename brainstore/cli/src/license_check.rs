use base64::{prelude::BASE64_STANDARD, Engine};
use reqwest::blocking::Client;
use ring::{
    rand::{SecureRandom, SystemRandom},
    signature::{self, UnparsedPublicKey},
};
use serde::Deserialize;
use util::anyhow::Result;

#[derive(Debug, Deserialize)]
struct AuthorizeResponse {
    nonce: String,
    authorized: bool,
    signature: String,
}

fn check_license(app_url: &str, license: Option<&str>) -> Result<()> {
    // NOTE: While this mechanism could be vulnerable to tampering via public key replacement in the binary,
    // we accept this risk since it would require deliberate malicious modification by the user.
    let license = license.ok_or_else(|| {
        util::anyhow::anyhow!("license must be provided via command line or environment variable")
    })?;

    // Generate a 32-byte cryptographically secure random nonce.
    let mut nonce = [0u8; 32];
    let rng = SystemRandom::new();
    rng.fill(&mut nonce)?;
    let nonce = BASE64_STANDARD.encode(nonce);

    let client = Client::new();
    let response = client
        .post(format!("{}/api/brainstore/authorize", app_url))
        .json(&serde_json::json!({
            "version": 1,
            "nonce": nonce,
            "license": license,
        }))
        .send()?;
    if !response.status().is_success() {
        return Err(util::anyhow::anyhow!(
            "failed to validate license: {}",
            response.status()
        ));
    }

    let AuthorizeResponse {
        nonce: response_nonce,
        authorized,
        signature: sig,
    } = response.json()?;

    // The logic to construct the signed payload should match the logic in the server at
    // //app/pages/api/brainstore/authorize.ts.
    let signed_payload = serde_json::json!({
        "nonce": nonce,
        "authorized": authorized,
    })
    .to_string();

    // The base64-encoded ed25519 public key for verifying brainstore authorization responses.
    // It is split into parts to make it nontrivial to replace in the binary.
    let brainstore_authorize_public_key = [
        String::from("d5c2NDlP"),
        String::from("8VHGWTTi"),
        String::from("WIxF/2uX"),
        String::from("21BLpk0b"),
        String::from("19iZsduP"),
        String::from("ZSs="),
    ]
    .join("");
    UnparsedPublicKey::new(
        &signature::ED25519,
        &BASE64_STANDARD.decode(brainstore_authorize_public_key)?,
    )
    .verify(
        signed_payload.as_bytes(),
        BASE64_STANDARD.decode(sig)?.as_ref(),
    )?;

    if nonce != response_nonce {
        return Err(util::anyhow::anyhow!("failed to verify response"));
    }

    if authorized {
        Ok(())
    } else {
        Err(util::anyhow::anyhow!(
            "license is not authorized for Brainstore"
        ))
    }
}

pub fn check_license_with_backoff(app_url: &str, license_key: Option<&String>) -> Result<()> {
    use backoff::{retry, Error as BackoffError, ExponentialBackoff};
    use std::time::Duration;

    let backoff_policy = ExponentialBackoff {
        max_elapsed_time: Some(Duration::from_secs(60)),
        initial_interval: Duration::from_millis(100),
        max_interval: Duration::from_secs(5),
        ..Default::default()
    };

    let op = || {
        check_license(&app_url, license_key.as_ref().map(|s| s.as_str())).map_err(|e| {
            eprintln!("Failed to verify Brainstore license: {:?}", e);
            BackoffError::transient(e)
        }) // Treat all license check errors as transient
    };

    if let Err(e) = retry(backoff_policy, op) {
        match e {
            BackoffError::Permanent(e) => {
                return Err(e.into());
            }
            BackoffError::Transient { err, .. } => {
                return Err(err.into());
            }
        }
    }

    Ok(())
}
