use std::collections::HashMap;
use std::sync::Arc;
use std::sync::Mutex;

use storage::postgres_pool::PostgresPool;
use util::{
    anyhow::{anyhow, Result},
    url::Url,
};

use crate::base::AppState;

// This is intended to be used for one-off queries that don't use the global store interface.
lazy_static::lazy_static! {
    static ref PG_POOLS: Mutex<HashMap<Url, PostgresPool>> = Mutex::new(HashMap::new());
}

pub fn get_pg_conn(app_state: Arc<AppState>) -> Result<PostgresPool> {
    let global_store_url = &app_state.storage_config.metadata_uri;
    if !(global_store_url.scheme() == "postgres" || global_store_url.scheme() == "postgresql") {
        return Err(anyhow!(
            "Expected postgres storage type but got {} (url: {})",
            global_store_url.scheme(),
            global_store_url
        ));
    }

    let pool = PG_POOLS
        .lock()
        .unwrap()
        .entry(global_store_url.clone())
        .or_insert_with(|| {
            PostgresPool::new(global_store_url, 1).expect("Failed to connect to postgres")
        })
        .clone();

    Ok(pool)
}
