use std::sync::Arc;

use clap::{Parser, Subcommand, ValueEnum};
use serde::{Deserialize, Serialize};
use storage::storage_benchmarks::StorageBenchOpts;
use util::{anyhow::Result, Value};

use crate::{
    base::{AppState, BaseArgs, CLIArgs},
    memprof::MemprofFlushHandle,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum BenchCommands {
    MergeLargeDocs(CLIArgs<MergeBenchArgs>),
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, ValueEnum, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Phase {
    Insert,
    Merge,
}

impl Phase {
    fn all() -> Vec<Phase> {
        vec![Phase::Insert, Phase::Merge]
    }
}

#[derive(Parse<PERSON>, Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MergeBenchArgs {
    #[command(flatten)]
    pub storage_bench_opts: StorageBenchOpts,

    #[arg(
        long,
        value_enum,
        num_args = 1..,
        value_delimiter = ',',
        default_values_t = Phase::all(),
        env = "BRAINSTORE_BENCH_PHASES",
        help = "Phases to run (comma-separated list of: insert, merge)"
    )]
    pub phases: Vec<Phase>,
}

pub fn base_args(args: &BenchCommands) -> &BaseArgs {
    match args {
        BenchCommands::MergeLargeDocs(a) => &a.base,
    }
}

pub async fn main(
    args: BenchCommands,
    memprof_flush_handle: MemprofFlushHandle,
) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        BenchCommands::MergeLargeDocs(a) => {
            merge_bench(app_state, a.args, memprof_flush_handle).await
        }
    }
}

async fn merge_bench(
    app_state: Arc<AppState>,
    args: MergeBenchArgs,
    #[allow(unused)] mut memprof_flush_handle: MemprofFlushHandle,
) -> Result<util::Value> {
    let input = storage::storage_benchmarks::StorageBenchInput {
        config: app_state.config.clone(),
        file_cache_opts: app_state.base_args.file_cache_opts.clone(),
    };
    if args.phases.contains(&Phase::Insert) {
        log::info!("Running insert phase");
        storage::storage_benchmarks::stress_test_large_docs_insert(
            &input,
            &args.storage_bench_opts,
        )
        .await?;
    }
    if args.phases.contains(&Phase::Merge) {
        log::info!("Running merge phase");
        storage::storage_benchmarks::stress_test_large_docs_merge(&input, &args.storage_bench_opts)
            .await?;
    }
    #[cfg(feature = "enable_memprof")]
    {
        log::info!("Flushing memory profile");
        memprof_flush_handle.flush().await;
    }

    Ok(Value::Null)
}
