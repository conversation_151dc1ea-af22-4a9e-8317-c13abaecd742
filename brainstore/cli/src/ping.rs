use clap::Parser;
use rand::Rng;
use serde::{Deserialize, Serialize};

use util::anyhow::Result;

#[derive(Pa<PERSON><PERSON>, Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PingCommand {
    #[arg(short, long)]
    pub message: Option<String>,

    #[arg(short, long)]
    pub watch_file: Option<String>,
}

pub async fn main(cmd: PingCommand) -> Result<util::Value> {
    if let Some(watch_file) = cmd.watch_file {
        while std::path::Path::new(&watch_file).exists() {
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }
    } else {
        std::thread::sleep(std::time::Duration::from_millis(
            rand::thread_rng().gen_range(0..100),
        ));
    }
    Ok(util::Value::String(format!(
        "Pong: {}",
        cmd.message.unwrap_or_else(
            || std::env::var("BRAINSTORE_PING_DEFAULT_MESSAGE").unwrap_or("".to_string())
        )
    )))
}
