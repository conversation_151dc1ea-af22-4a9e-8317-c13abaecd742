use std::str::FromStr;

use clap::Parse<PERSON>;
use serde::{Deserialize, Serialize};
use serde_json::json;
use util::xact::TransactionId;

use crate::base::CLIArgs;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct XactCommandInput {
    pub xact_id: String,
}

pub type XactCommand = CLIArgs<XactCommandInput>;

pub async fn main(cmd: XactCommand) -> Result<util::Value, util::anyhow::Error> {
    let xact = TransactionId::from_str(&cmd.args.xact_id)?;
    log::info!("xact: {}", xact);
    let (timestamp, counter) = xact.to_timestamp_and_counter();
    let datetime = chrono::DateTime::from_timestamp(timestamp as i64, 0)
        .unwrap()
        .with_timezone(&chrono::Local);
    Ok(json!({
        "time": datetime.format("%Y-%m-%d %H:%M:%S %Z").to_string(),
        "counter": counter,
    }))
}
