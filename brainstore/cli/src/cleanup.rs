use futures::future::join_all;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use std::time::{Duration, SystemTime};
use storage::directory::cached_directory::BRAINSTORE_CACHE_DIR_PREFIX;

use clap::Parser;

use crate::base::{BaseArgs, CLIArgs};
use util::anyhow::Result;

const LSOF_CMD: &str = "lsof";

pub async fn check_can_run_cleanup() -> Result<()> {
    let output = match tokio::process::Command::new(LSOF_CMD)
        .arg("-v")
        .output()
        .await
    {
        Ok(output) => output,
        Err(e) => {
            return Err(util::anyhow::anyhow!("{} failed to run: {}", LSOF_CMD, e));
        }
    };
    if !output.status.success() {
        return Err(util::anyhow::anyhow!(
            "{} failed to run:\nstdout: {}\nstderr: {}",
            LSOF_CMD,
            String::from_utf8_lossy(&output.stdout),
            String::from_utf8_lossy(&output.stderr)
        ));
    }
    Ok(())
}

async fn inspect_tmpdir(
    base_path: &Path,
    idle_window: std::time::Duration,
) -> Result<Vec<TmpFileInfo>> {
    let mut results = Vec::new();

    let entries: Vec<_> = walkdir::WalkDir::new(base_path)
        .into_iter()
        .filter_map(|e| match e {
            Ok(e) => Some(e),
            Err(e) => {
                log::warn!("Error walking directory: {:?}", e);
                None
            }
        })
        .filter(|e| {
            e.file_name()
                .to_str()
                .map_or(false, |s| s.ends_with(".cache"))
        })
        .map(|e| e.path().to_path_buf())
        .collect();

    let futures = entries.into_iter().map(|path| async move {
        // Check file creation time
        let metadata = fs::metadata(&path)?;
        let creation_time = metadata.created()?;
        let age = SystemTime::now().duration_since(creation_time)?;

        // Check if file is in use using lsof
        let output = match tokio::process::Command::new(LSOF_CMD)
            .arg(path.to_str().unwrap())
            .output()
            .await
        {
            Ok(output) => output,
            Err(e) => {
                return Err(util::anyhow::anyhow!("{} failed to run: {}", LSOF_CMD, e));
            }
        };

        let status = if !output.status.success() && age >= idle_window {
            TmpFileStatus::Idle
        } else {
            TmpFileStatus::InUse
        };

        Ok::<_, util::anyhow::Error>(TmpFileInfo { path, status })
    });

    results.extend(join_all(futures).await.into_iter().filter_map(|r| match r {
        Ok(r) => Some(r),
        Err(e) => {
            log::warn!("Error inspecting file: {:?}", e);
            None
        }
    }));

    Ok(results)
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TmpFileStatus {
    Idle,
    InUse,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TmpFileInfo {
    path: PathBuf,
    status: TmpFileStatus,
}

impl std::fmt::Display for TmpFileInfo {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{:<60} {}",
            self.path.display(),
            match self.status {
                TmpFileStatus::Idle => "IDLE",
                TmpFileStatus::InUse => "IN USE",
            }
        )
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, Parser)]
pub struct CleanupCommand {
    #[arg(long, help = "Delete files that are no longer in use")]
    pub delete: bool,

    #[arg(
        long,
        help = "Only delete files that have been idle for at least this many seconds",
        default_value_t = default_idle_seconds(),
    )]
    pub idle_seconds: u64,
}

pub fn default_idle_seconds() -> u64 {
    60
}

async fn run_cleanup(
    base_args: &BaseArgs,
    delete: bool,
    idle: std::time::Duration,
    print_info: bool,
) -> Result<Vec<TmpFileInfo>> {
    let temp_dir = match &base_args.file_cache_opts.cache_dir {
        Some(cache_dir) => cache_dir.clone(),
        None => {
            let system_temp = std::env::temp_dir();
            system_temp.join(BRAINSTORE_CACHE_DIR_PREFIX)
        }
    };

    let statuses = inspect_tmpdir(&temp_dir, idle).await?;

    if print_info {
        for status in statuses.iter() {
            println!("{}", status);
        }

        if statuses.is_empty() {
            println!("No temporary files found in {}", temp_dir.display());
        }
    }

    if delete {
        join_all(
            statuses
                .iter()
                .filter(|status| matches!(status.status, TmpFileStatus::Idle))
                .map(|status| {
                    let path = status.path.clone();
                    async move {
                        log::info!("Deleting unused file: {:?}", path);
                        tokio::fs::remove_file(&path).await?;

                        // Check if the parent directory is empty and delete it if so
                        if let Some(parent_dir) = path.parent() {
                            match tokio::fs::read_dir(parent_dir).await {
                                Ok(mut entries) => {
                                    if entries.next_entry().await?.is_none() {
                                        log::info!("Deleting empty directory: {:?}", parent_dir);
                                        tokio::fs::remove_dir(parent_dir).await?;
                                    }
                                }
                                Err(_) => {
                                    // Directory might not exist or we don't have permission, ignore
                                }
                            }
                        }

                        Ok::<_, util::anyhow::Error>(())
                    }
                }),
        )
        .await
        .into_iter()
        .collect::<Result<Vec<_>, _>>()?;
    }

    Ok(statuses)
}

pub async fn main(args: CLIArgs<CleanupCommand>) -> Result<util::Value> {
    run_cleanup(
        &args.base,
        args.args.delete,
        std::time::Duration::from_secs(args.args.idle_seconds),
        true,
    )
    .await?;
    Ok(util::Value::Null)
}

pub fn launch_cleanup_thread(base_args: BaseArgs, cleanup_command: CleanupCommand) {
    tokio::spawn(async move {
        loop {
            log::debug!("Running cleanup");
            match run_cleanup(
                &base_args,
                cleanup_command.delete,
                std::time::Duration::from_secs(cleanup_command.idle_seconds),
                false,
            )
            .await
            {
                Ok(_) => {}
                Err(e) => {
                    log::warn!("Error running cleanup: {:?}", e);
                }
            };

            tokio::time::sleep(Duration::from_secs(cleanup_command.idle_seconds)).await;
        }
    });
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs::File;
    use std::io::Write;
    use storage::directory::cached_directory::FileCacheOpts;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_cleanup() {
        // Create a temporary directory
        let temp_dir = tempdir().unwrap();
        let cache_file_path = temp_dir.path().join("test.cache");

        // Create and keep open a cache file
        let file_content = b"test content";
        let mut file = File::create(&cache_file_path).unwrap();
        file.write_all(file_content).unwrap();

        // Run cleanup with file open
        let base_args = BaseArgs {
            file_cache_opts: FileCacheOpts {
                cache_dir: Some(temp_dir.path().to_path_buf()),
                ..Default::default()
            },
            ..Default::default()
        };

        let idle_duration = std::time::Duration::from_millis(100);

        // First check - file should be in use
        let results = run_cleanup(&base_args, false, idle_duration, false)
            .await
            .unwrap();
        assert_eq!(results.len(), 1);
        assert!(matches!(results[0].status, TmpFileStatus::InUse));

        // Drop the file handle
        drop(file);

        let start_time = std::time::Instant::now();

        loop {
            if start_time.elapsed() > std::time::Duration::from_secs(60) {
                panic!("Did not resolve after 60s");
            }

            // Wait for the idle duration
            tokio::time::sleep(Duration::from_millis(150)).await;

            // Second check - file should be idle
            let results = run_cleanup(&base_args, false, idle_duration, false)
                .await
                .unwrap();
            assert_eq!(results.len(), 1);
            if matches!(results[0].status, TmpFileStatus::Idle) {
                break;
            }
        }

        // Final check - file should be deleted
        let results = run_cleanup(&base_args, true, idle_duration, false)
            .await
            .unwrap();

        // Verify file was deleted
        assert!(!cache_file_path.exists());
        assert_eq!(results.len(), 1); // The results still include the file that was just deleted

        // Run it again, and it should be empty
        let results = run_cleanup(&base_args, true, idle_duration, false)
            .await
            .unwrap();
        assert_eq!(results.len(), 0);
    }
}
