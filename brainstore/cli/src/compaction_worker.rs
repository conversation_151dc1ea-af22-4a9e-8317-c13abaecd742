use std::sync::Arc;

use storage::{
    compaction_loop::CompactionLoop, limits::global_limits, process_wal::CompactSegmentWalOptions,
};
use tokio::task::JoinSet;
use util::{Result, Value};

use crate::{
    base::{AppState, CLIArgs, SegmentIdArgs},
    wal::{CompactWalFullArgs, CompactWalInputArgs, WalCommands},
};

// After this point, just reject compactions, which could be used to signal back pressure.
pub const MAX_QUEUED_COMPACTIONS: usize = 1000;

pub async fn run_compaction_loop(
    compaction_loop: CompactionLoop,
    app_state: Arc<AppState>,
    opts: CompactSegmentWalOptions,
) {
    let mut compactions = tokio::task::JoinSet::new();
    loop {
        tokio::select! {
            _ = compaction_loop.notified() => {
                spawn_compactions(&compaction_loop, app_state.clone(), &opts, &mut compactions);
            }
            Some(result) = compactions.join_next() => {
                match result {
                    Ok(Ok(_)) => (), // Task completed successfully
                    Ok(Err(e)) => tracing::error!("Async compaction error: {}", e),
                    Err(e) => tracing::error!("Async compaction panicked: {}", e),
                }

                spawn_compactions(&compaction_loop, app_state.clone(), &opts, &mut compactions);
            }
        }
    }
}

fn spawn_compactions(
    compaction_loop: &CompactionLoop,
    app_state: Arc<AppState>,
    opts: &CompactSegmentWalOptions,
    compactions: &mut JoinSet<Result<Value>>,
) {
    while compactions.len() < global_limits().index_operations.capacity() {
        let (segment, queue_time) = match compaction_loop.pop_front() {
            Some(segment) => segment,
            None => break,
        };

        log::info!(
            "Spawned compaction segment_id={}, queue_time={:?}",
            segment,
            queue_time
        );
        let cmd = crate::Commands::Wal(WalCommands::Compact(CLIArgs {
            base: app_state.base_args.clone(),
            args: CompactWalFullArgs {
                compact_wal_opts: opts.clone(),
                compact_wal_input: CompactWalInputArgs {
                    segment_id_args: SegmentIdArgs {
                        segment_ids: vec![segment],
                        all: false,
                        object_id: None,
                    },
                    start_xact_id: None,
                    end_xact_id: None,
                    try_acquire: true,
                    queue: false,
                },
            },
        }));
        let state = app_state.clone();
        compactions
            .spawn(async move { state.command_proc.as_ref().unwrap().run_command(cmd).await });
    }
}
