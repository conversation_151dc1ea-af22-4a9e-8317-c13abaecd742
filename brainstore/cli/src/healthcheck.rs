use crate::base::{AppState, WebOpts};

use anyhow::Result;
use otel_common::process_info::{get_disk_info, get_memory_and_thread_info, get_uptime};
use serde::Serialize;
use serde_json::json;
use util::{anyhow, Value};

fn obfuscate_url(url: &str) -> String {
    if let Some(protocol_end) = url.find("://") {
        format!("{}://*****", &url[..protocol_end])
    } else {
        "*****".to_string()
    }
}

fn should_redact(s: &str) -> bool {
    let s_lower = s.to_lowercase();
    s_lower.contains("bearer ")
        || s_lower.contains("token")
        || s_lower.contains("key")
        || s_lower.contains("secret")
        || s_lower.contains("pass")
}

fn obfuscate_value(value: &Value) -> Value {
    match value {
        Value::String(s) => {
            // Check if it's a URL-like string
            if s.contains("://") {
                Value::String(obfuscate_url(s))
            } else {
                if should_redact(s) {
                    // Obfuscate potential secrets in headers and values
                    Value::String("*****".to_string())
                } else {
                    value.clone()
                }
            }
        }
        Value::Object(map) => {
            let mut new_map = serde_json::Map::new();
            for (key, val) in map {
                // Check if the key name suggests sensitive data
                if should_redact(key) {
                    new_map.insert(key.clone(), Value::String("*****".to_string()));
                } else if key.to_lowercase().contains("uri") || key.to_lowercase().contains("url") {
                    new_map.insert(key.clone(), obfuscate_value(val));
                } else {
                    new_map.insert(key.clone(), obfuscate_value(val));
                }
            }
            Value::Object(new_map)
        }
        Value::Array(arr) => Value::Array(arr.iter().map(obfuscate_value).collect()),
        _ => value.clone(),
    }
}

fn serialize_with_obfuscation<T: Serialize>(value: &T) -> Result<Value> {
    let json_value = serde_json::to_value(value)?;
    Ok(obfuscate_value(&json_value))
}

fn wrap_error(result: Result<String>) -> serde_json::Value {
    match result {
        Ok(msg) => json!({
            "success": true,
            "message": msg,
        }),
        Err(e) => json!({
            "success": false,
            "error": format!("{:#}", e),
        }),
    }
}

pub fn get_config_args(app_state: &AppState, web_opts: Option<&WebOpts>) -> Value {
    let mut args = json!({});

    // Add base args with obfuscation
    if let Ok(base_args_json) = serialize_with_obfuscation(&app_state.base_args) {
        args["base"] = base_args_json;
    }

    // Add storage config with obfuscation
    if let Ok(storage_config_json) = serialize_with_obfuscation(&app_state.storage_config) {
        args["storage_config"] = storage_config_json;
    }

    // Add web opts if provided
    if let Some(opts) = web_opts {
        if let Ok(web_opts_json) = serialize_with_obfuscation(opts) {
            args["web_opts"] = web_opts_json;
        }
    }

    args
}

#[tracing::instrument(level = "debug", skip(app_state))]
pub async fn run_healthcheck(app_state: &AppState) -> Result<Value> {
    let metadata_status = app_state.config.global_store.status().await;
    let index_status = app_state.config.index.status().await;
    let locks_status = app_state.config.locks_manager.status().await;
    let wal_status = app_state.config.wal.status().await;
    let realtime_wal_status = match app_state.config.realtime_wal.as_ref() {
        Some(realtime_wal) => realtime_wal.status().await,
        None => Ok("realtime_wal is not configured".to_string()),
    };
    let xact_manager_status = app_state.config.xact_manager.status().await;

    let healthcheck_status = metadata_status.is_ok()
        && index_status.is_ok()
        && locks_status.is_ok()
        && wal_status.is_ok()
        && realtime_wal_status.is_ok()
        && xact_manager_status.is_ok();

    let uptime = get_uptime().unwrap_or(0);
    let memory_and_threads = get_memory_and_thread_info();

    Ok(json!({
        "status": if healthcheck_status { "ok" } else { "error" },
        "uptime": uptime,
        "services": {
            "metadata": wrap_error(metadata_status),
            "index": wrap_error(index_status),
            "locks": wrap_error(locks_status),
            "wal": wrap_error(wal_status),
            "realtime_wal": wrap_error(realtime_wal_status),
            "xact_manager": wrap_error(xact_manager_status),
        },
        "system": {
            "memory": json!({
                "total": memory_and_threads.total,
                "used": memory_and_threads.used,
                "proc": memory_and_threads.proc,
            }),
            "process": json!({
                "threads": memory_and_threads.threads,
            }),
            "disk": get_disk_info(
                app_state
                    .base_args
                    .file_cache_opts
                    .cache_dir
                    .as_ref()
                    .unwrap_or(&std::env::temp_dir()),
            ),
        },
    }))
}
