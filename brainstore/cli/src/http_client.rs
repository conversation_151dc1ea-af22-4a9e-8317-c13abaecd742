use std::sync::Mutex;

use reqwest::Client;
use util::anyhow::{anyhow, Result};

lazy_static::lazy_static! {
    static ref HTTP_CLIENT: Mutex<Option<Client>> = Mutex::new(None);
}

fn initialize_client() -> Result<Client> {
    Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .map_err(|e| anyhow!("Failed to create HTTP client: {}", e))
}

pub fn get_http_client() -> Result<Client> {
    let mut guard = HTTP_CLIENT
        .lock()
        .map_err(|e| anyhow!("Failed to acquire HTTP client lock: {}", e))?;

    if guard.is_none() {
        *guard = Some(initialize_client()?);
    }

    guard
        .as_ref()
        .cloned()
        .ok_or_else(|| anyhow!("HTTP client initialization failed"))
}
