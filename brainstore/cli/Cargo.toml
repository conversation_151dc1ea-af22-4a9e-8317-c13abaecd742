[package]
name = "brainstore"
version = "0.1.0"
edition = "2021"
authors = ["Braintrust engineering <<EMAIL>>"]
description = "The Brainstore command line interface"
build = "build.rs"

[dependencies]
btql = { path = "../btql" }
otel_common = { path = "../otel_common" }
agent = { path = "../agent" }
storage = { path = "../storage" }
util = { path = "../util" }
query = { path = "../query" }
tracing = { path = "../tracing" }

clap = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
log = { workspace = true }
tokio-stream = { workspace = true }
num_cpus = { workspace = true }
serde = { workspace = true }
actix-web = { workspace = true }
async-stream = { workspace = true }
chrono = { workspace = true }
tantivy = { path = "../tantivy" }
fork = { workspace = true }
nix = { workspace = true }
tempfile = { workspace = true }
rand = { workspace = true }
walkdir = { workspace = true }
rustls = { workspace = true }
ring = { workspace = true, optional = true }
base64 = { workspace = true, optional = true }
backoff = { workspace = true, optional = true }
deadpool = { workspace = true }
lazy_static = { workspace = true }
futures = { workspace = true }
reqwest = { workspace = true }
async_util = { path = "../async_util" }
async-trait = { workspace = true }

[target.'cfg(not(target_env = "msvc"))'.dependencies]
tikv-jemallocator = { workspace = true }

# Continuous Profiling
tonic = { workspace = true, optional = true }
prost = { workspace = true }
prost-types = { workspace = true }



[build-dependencies]
tonic-build = { workspace = true, optional = true }

[features]
default = ["expect-tests", "enable_memprof", "bench"]
distribute = ["ring", "base64", "backoff"]
expect-tests = ["query/expect-tests"]
enable_memprof = ["agent/enable_memprof"]
bench = []
