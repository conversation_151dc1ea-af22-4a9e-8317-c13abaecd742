# Pulse

Pulse is a service that fundamentally:

- Ingests various kinds of metrics
- Authorizes requests using Braintrust license keys (fka Brainstore license keys)
- Forwards them to one or more services or internal databases

To run it, simply run:

```
watchexec cargo run --bin pulse -- -v
```

And make sure your Brainstore is configured to send metrics to it, eg:

```
 BRAINSTORE_LICENSE_KEY=<LICENSE KEY> watchexec -e rs -r  cargo run -F distribute --release  --bin brainstore web --config examples/braintrust/brainstore.yaml --schema examples/braintrust/schema.json  -v  --pretty-logging=true   --otlp-http-endpoint http://localhost:4319/otel
```

(the key things are setting `BRAINSTORE_LICENSE_KEY` and `--otlp-http-endpoint`. Also make sure `BRAINTRUST_APP_URL` is either unset or points to www.braintrust.dev).

## Running OTel collector

To run the OTel collector, simply run:

```
DD_API_KEY=<DATADOG_API_KEY> docker compose -f services/otelcol-compose.yml  up
```

This binds the collector on port 4318.

## Running Vector (deprecated)

NOTE: Vector [doesn't support OTel traces->Datadog traces yet](https://github.com/vectordotdev/vector/issues/22659).

Currently, we do not have the ability to post OTel metrics directly to Datadog, so we rely on Vector.
While we could use the Datadog agent, it adds a bunch of its own attributes and makes the final
result confusing.

Vector is not part of an official service, so it's only runnable ad-hoc.

From the root of the repo, run:

```
$ DD_API_KEY=<SOME KEY> vector -c services/vector/vector.toml
```

This will run Vector on port 4318, so make sure that you do not have another collector (eg. the Datadog agent)
running on that port.

## Nginx Reverse Proxy Setup

To deploy Pulse behind an nginx reverse proxy with SSL/TLS termination, you'll need to configure nginx and obtain SSL certificates using Let's Encrypt.

### Initial Setup

First, install the required packages on your server:

```bash
sudo apt update && sudo apt upgrade -y
sudo apt install nginx -y
sudo apt install certbot python3-certbot-nginx -y
```

Create the necessary directories and set permissions:

```bash
sudo mkdir -p /var/www/html/.well-known/acme-challenge
sudo chown -R www-data:www-data /var/www/html
```

### Nginx Configuration

Create an nginx configuration file at `/etc/nginx/sites-available/pulse.braintrustdata.com` with the following content:

```nginx
server {
    client_max_body_size 100M;

    listen 80;
    server_name pulse.braintrustdata.com;

    location / {
        proxy_pass http://localhost:4319;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /.well-known/acme-challenge/ { root /var/www/html; }
}
```

Enable the site and test the configuration:

```bash
sudo ln -s /etc/nginx/sites-available/pulse.braintrustdata.com /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl start nginx
sudo systemctl enable nginx
```

### SSL Certificate Setup

Generate an SSL certificate using Let's Encrypt:

```bash
sudo certbot --nginx -d pulse.braintrustdata.com  --redirect --agree-tos -m <EMAIL>
```

Test and enable automatic certificate renewal:

```bash
sudo certbot renew --dry-run
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer
```
