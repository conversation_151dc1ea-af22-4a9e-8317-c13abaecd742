[package]
name = "pulse"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-web = { workspace = true }
clap = { workspace = true }
env_logger = { workspace = true }
log = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
lru = { workspace = true }
reqwest = { workspace = true }

# OpenTelemetry dependencies
opentelemetry = { workspace = true }
opentelemetry-proto = { workspace = true }
prost = { workspace = true }

agent = { path = "../agent" }
util = { path = "../util" }
