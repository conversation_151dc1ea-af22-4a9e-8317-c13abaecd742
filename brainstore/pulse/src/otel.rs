use opentelemetry_proto::tonic::{
    collector::{
        logs::v1::ExportLogsServiceRequest, metrics::v1::ExportMetricsServiceRequest,
        trace::v1::ExportTraceServiceRequest,
    },
    common::v1::{AnyValue, KeyValue, any_value},
    metrics::v1::metric,
};

pub fn add_org_id_to_export_metrics_request(
    payload: &mut ExportMetricsServiceRequest,
    org_id: &str,
    org_name: Option<&str>,
) {
    for resource_metric in &mut payload.resource_metrics {
        for scope_metric in &mut resource_metric.scope_metrics {
            for metric in &mut scope_metric.metrics {
                if let Some(data) = &mut metric.data {
                    add_org_id_to_metric(data, org_id, org_name);
                }
            }
        }
    }
}

fn add_org_id_to_metric(data: &mut metric::Data, org_id: &str, org_name: Option<&str>) {
    match data {
        metric::Data::Gauge(gauge) => {
            for point in &mut gauge.data_points {
                point
                    .attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
        metric::Data::Sum(sum) => {
            for point in &mut sum.data_points {
                point
                    .attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
        metric::Data::Histogram(histogram) => {
            for point in &mut histogram.data_points {
                point
                    .attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
        metric::Data::ExponentialHistogram(exponential_histogram) => {
            for point in &mut exponential_histogram.data_points {
                point
                    .attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
        metric::Data::Summary(summary) => {
            for point in &mut summary.data_points {
                point
                    .attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
    }
}

pub fn add_org_id_to_export_traces_request(
    payload: &mut ExportTraceServiceRequest,
    org_id: &str,
    org_name: Option<&str>,
) {
    for resource_spans in &mut payload.resource_spans {
        for scope_spans in &mut resource_spans.scope_spans {
            for span in &mut scope_spans.spans {
                span.attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
    }
}

pub fn add_org_id_to_export_logs_request(
    payload: &mut ExportLogsServiceRequest,
    org_id: &str,
    org_name: Option<&str>,
) {
    for resource_logs in &mut payload.resource_logs {
        if let Some(resource) = &mut resource_logs.resource {
            resource
                .attributes
                .extend(make_extra_attributes(org_id, org_name));
        }
        for scope_logs in &mut resource_logs.scope_logs {
            for record in &mut scope_logs.log_records {
                record
                    .attributes
                    .extend(make_extra_attributes(org_id, org_name));
            }
        }
    }
}

#[inline]
fn make_extra_attributes(org_id: &str, org_name: Option<&str>) -> Vec<KeyValue> {
    let mut attrs = vec![
        KeyValue {
            key: "bt_org_id".to_string(),
            value: Some(AnyValue {
                value: Some(any_value::Value::StringValue(org_id.to_string())),
            }),
        },
        KeyValue {
            key: "env".to_string(),
            value: Some(AnyValue {
                value: Some(any_value::Value::StringValue("customer".to_string())),
            }),
        },
    ];

    if let Some(name) = org_name {
        attrs.push(KeyValue {
            key: "bt_org_name".to_string(),
            value: Some(AnyValue {
                value: Some(any_value::Value::StringValue(name.to_string())),
            }),
        });
    }

    attrs
}
