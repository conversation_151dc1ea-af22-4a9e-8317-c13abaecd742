use opentelemetry::KeyValue;
use opentelemetry::global::{self};
use opentelemetry::metrics::Counter;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PulseMeters {
    requests: Counter<u64>,
    bytes: Counter<u64>,
}

impl PulseMeters {
    pub fn new() -> Self {
        let meter = global::meter("pulse");
        let requests = meter.u64_counter("requests").build();
        let bytes = meter.u64_counter("bytes").build();
        Self { requests, bytes }
    }

    pub fn record_request(&self, request_type: &'static str, org_id: &str, bytes: usize) {
        let attributes = [
            KeyValue::new("type", request_type),
            KeyValue::new("org_id", org_id.to_string()),
        ];
        self.requests.add(1, &attributes);
        self.bytes.add(bytes as u64, &attributes);
    }
}
