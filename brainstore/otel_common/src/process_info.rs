use serde::Serialize;
use std::{
    collections::{HashMap, HashSet},
    path::Path,
};
use sysinfo::{get_current_pid, Pid, System};

use util::{anyhow, Result};

#[cfg(target_os = "linux")]
fn get_raw_system_memory() -> usize {
    let machine_memory = sysinfo::System::new_with_specifics(
        sysinfo::RefreshKind::nothing()
            .with_memory(sysinfo::MemoryRefreshKind::nothing().with_ram().with_swap()),
    );
    let total_memory = machine_memory.total_memory() as usize;
    let total_swap = machine_memory.total_swap() as usize;

    let ignore_swap = std::env::var("BRAINSTORE_SYSTEM_MEMORY_IGNORE_SWAP")
        .map(|v| v.parse::<bool>().unwrap_or(false))
        .unwrap_or(false);

    let actual_memory = if ignore_swap {
        total_memory
    } else {
        assert!(
            total_swap <= total_memory,
            "Swap space ({} bytes) exceeds total memory ({} bytes). This is not supported.",
            total_swap,
            total_memory
        );
        total_memory - total_swap
    };
    actual_memory
}

#[cfg(target_os = "macos")]
fn get_raw_system_memory() -> usize {
    let machine_memory = sysinfo::System::new_with_specifics(
        sysinfo::RefreshKind::nothing()
            .with_memory(sysinfo::MemoryRefreshKind::nothing().with_ram()),
    );
    let actual_memory = machine_memory.total_memory() as usize;
    actual_memory
}

pub fn get_system_memory() -> usize {
    let system_memory = get_raw_system_memory();
    let actual_memory = (system_memory as f64 * 0.8) as usize;
    actual_memory
}

#[derive(Serialize)]
pub struct MemoryAndThreadInfo {
    pub total: usize,
    pub used: usize,
    pub proc: Option<usize>,
    pub threads: Option<usize>,
}

pub fn get_memory_and_thread_info() -> MemoryAndThreadInfo {
    let sys = System::new_all();
    let machine_memory = sysinfo::System::new_with_specifics(
        sysinfo::RefreshKind::nothing()
            .with_memory(sysinfo::MemoryRefreshKind::nothing().with_ram()),
    );
    let memory_and_threads = get_memory_usage_with_children(&sys).ok();
    MemoryAndThreadInfo {
        total: get_raw_system_memory(),
        used: machine_memory.used_memory() as usize,
        proc: memory_and_threads.as_ref().map(|v| v.memory as usize),
        threads: memory_and_threads.as_ref().and_then(|v| v.threads),
    }
}
#[derive(Serialize)]
pub struct DiskInfo {
    pub total: usize,
    pub free: usize,
}

pub fn get_disk_info(path: &Path) -> Option<DiskInfo> {
    let disks = sysinfo::Disks::new_with_refreshed_list();
    let disk = disks
        .iter()
        .filter(|d| path.starts_with(d.mount_point()))
        .max_by_key(|d| d.mount_point().as_os_str().len());

    disk.map(|d| DiskInfo {
        total: d.total_space() as usize,
        free: d.available_space() as usize,
    })
}

pub fn get_uptime() -> Result<u64> {
    let sys = System::new_all();
    match get_current_pid() {
        Ok(pid) => match sys.process(pid) {
            Some(process) => Ok(process.run_time()),
            None => Err(anyhow::anyhow!("Failed to get process uptime")),
        },
        Err(e) => Err(anyhow::anyhow!("Failed to get process uptime: {}", e)),
    }
}

pub struct MemoryAndThreads {
    pub memory: u64,
    pub threads: Option<usize>,
}

pub fn get_memory_usage_with_children(sys: &System) -> Result<MemoryAndThreads> {
    match get_current_pid() {
        Ok(root_pid) => {
            let processes = sys.processes();

            // Build a map of parent to children
            let mut child_map: HashMap<_, Vec<_>> = HashMap::new();
            // Track process groups (to handle threads)
            let mut process_groups: HashMap<Pid, Pid> = HashMap::new();

            let mut num_root_threads: Option<usize> = None;

            for (pid, process) in processes {
                // On Linux, threads share the same process group ID
                // We'll use this to identify unique processes
                let thread_group_id = match process.parent() {
                    Some(parent) => {
                        // If this is a thread, it will have a parent
                        if let Some(_parent_process) = processes.get(&parent) {
                            // Check if this is a thread by checking name similarity or other criteria
                            // For simplicity, we'll consider it a thread of the parent
                            parent
                        } else {
                            // If parent doesn't exist, treat as its own process
                            *pid
                        }
                    }
                    None => *pid, // No parent, this is a main process
                };

                process_groups.insert(*pid, thread_group_id);

                if let Some(parent_pid) = process.parent() {
                    if !child_map
                        .values()
                        .any(|children: &Vec<Pid>| children.contains(pid))
                    {
                        // Only add as a child if not already accounted for
                        child_map.entry(parent_pid).or_default().push(*pid);
                    }
                }

                if *pid == root_pid {
                    num_root_threads = process.tasks().map(|s| s.len());
                }
            }

            // Recursively collect all descendant PIDs
            let mut all_pids = HashSet::new();
            all_pids.insert(root_pid);
            collect_descendants(&child_map, root_pid, &mut all_pids);

            // Sum up memory for all processes, avoiding double-counting threads
            let mut total_memory = 0;
            let mut counted_groups = HashSet::new();

            for pid in all_pids {
                if let Some(process) = processes.get(&pid) {
                    // Get the process group ID (main process ID)
                    let group_id = process_groups.get(&pid).unwrap_or(&pid);

                    // Only count memory once per process group
                    if !counted_groups.contains(group_id) {
                        counted_groups.insert(*group_id);
                        total_memory += process.memory();
                    }
                }
            }

            Ok(MemoryAndThreads {
                memory: total_memory,
                threads: num_root_threads,
            })
        }
        Err(e) => Err(anyhow::anyhow!("Failed to get process memory usage: {}", e)),
    }
}

// Helper function to recursively collect all descendant PIDs
fn collect_descendants(
    child_map: &HashMap<sysinfo::Pid, Vec<sysinfo::Pid>>,
    current_pid: sysinfo::Pid,
    all_pids: &mut HashSet<sysinfo::Pid>,
) {
    if let Some(children) = child_map.get(&current_pid) {
        for &child_pid in children {
            all_pids.insert(child_pid);
            collect_descendants(child_map, child_pid, all_pids);
        }
    }
}
