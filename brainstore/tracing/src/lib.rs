// Re-exports the bits of tracing that we use throughout the codebase
pub use ::tracing_external::{
    debug, debug_span, dispatcher, enabled, error, error_span, event, field, if_log_enabled, info,
    info_span, instrument, level_enabled, level_filters, span, subscriber, trace, trace_span, warn,
    warn_span, Level, Span,
};

// This Instrument/Instrumented implementation correctly wraps traits like Stream, whereas the built-in
// tracing module ones do not. Since they both contain implementations of this trait, I think it makes life
// easier if we only export these.
pub use tracing_futures::{Instrument, Instrumented};
