#!/usr/bin/env python3
"""
Braintrust Data Retention Client

A Python client for creating and managing data retention policies in Braintrust.
"""

import os
import requests
from typing import Optional, Literal, Dict, Any, List
from dataclasses import dataclass


@dataclass
class RetentionPolicyResponse:
    """Response from creating a retention policy."""
    automation_id: str
    project_id: str
    name: str
    description: Optional[str]
    object_type: str
    retention_days: int
    created: str
    found_existing: bool


class BraintrustRetentionClient:
    """Client for managing Braintrust data retention policies."""
    
    def __init__(self, api_key: Optional[str] = None, api_url: str = "https://api.braintrust.dev"):
        """
        Initialize the Braintrust retention client.
        
        Args:
            api_key: Braintrust API key. If not provided, will look for BRAINTRUST_API_KEY env var.
            api_url: Base URL for the Braintrust API.
        """
        self.api_key = api_key or os.getenv("BRAINTRUST_API_KEY")
        if not self.api_key:
            raise ValueError("API key is required. Provide it directly or set BRAINTRUST_API_KEY environment variable.")
        
        self.api_url = api_url.rstrip("/")
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def create_retention_policy(
        self,
        project_id: str,
        object_type: Literal["project_logs", "experiment", "dataset"],
        retention_days: int,
        name: Optional[str] = None,
        description: Optional[str] = None
    ) -> RetentionPolicyResponse:
        """
        Create a data retention policy for a Braintrust project.
        
        Args:
            project_id: The Braintrust project ID
            object_type: Type of data to apply retention to ("project_logs", "experiment", or "dataset")
            retention_days: Number of days to retain data (minimum 0)
            name: Optional name for the retention policy. If not provided, will auto-generate.
            description: Optional description for the retention policy.
            
        Returns:
            RetentionPolicyResponse with details of the created policy.
            
        Raises:
            requests.HTTPError: If the API request fails
            ValueError: If invalid parameters are provided
        """
        if retention_days < 0:
            raise ValueError("retention_days must be 0 or greater")
        
        if object_type not in ["project_logs", "experiment", "dataset"]:
            raise ValueError("object_type must be one of: project_logs, experiment, dataset")
        
        # Auto-generate name if not provided
        if not name:
            name = f"{retention_days}-Day {object_type.replace('_', ' ').title()} Retention Policy"
        
        # Auto-generate description if not provided
        if not description:
            description = f"Automatically delete {object_type.replace('_', ' ')} older than {retention_days} days"
        
        payload = {
            "project_automation_name": name,
            "description": description,
            "project_id": project_id,
            "config": {
                "event_type": "retention",
                "object_type": object_type,
                "retention_days": retention_days
            }
        }
        
        url = f"{self.api_url}/api/project_automation/register"
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            automation = data["project_automation"]
            
            return RetentionPolicyResponse(
                automation_id=automation["id"],
                project_id=automation["project_id"],
                name=automation["name"],
                description=automation.get("description"),
                object_type=automation["config"]["object_type"],
                retention_days=automation["config"]["retention_days"],
                created=automation["created"],
                found_existing=data.get("found_existing", False)
            )
            
        except requests.HTTPError as e:
            error_msg = f"Failed to create retention policy: {e}"
            if e.response is not None:
                try:
                    error_detail = e.response.json()
                    error_msg += f" - {error_detail}"
                except:
                    error_msg += f" - {e.response.text}"
            raise requests.HTTPError(error_msg) from e
    
    def get_project_automations(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get existing project automations.
        
        Args:
            project_id: Optional project ID to filter by
            
        Returns:
            List of automation dictionaries
        """
        url = f"{self.api_url}/api/project_automation/get"
        payload = {}
        if project_id:
            payload["project_id"] = project_id
            
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.HTTPError as e:
            error_msg = f"Failed to get project automations: {e}"
            if e.response is not None:
                try:
                    error_detail = e.response.json()
                    error_msg += f" - {error_detail}"
                except:
                    error_msg += f" - {e.response.text}"
            raise requests.HTTPError(error_msg) from e


# Convenience functions for common use cases
def create_project_logs_retention(
    project_id: str,
    retention_days: int,
    api_key: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None
) -> RetentionPolicyResponse:
    """
    Convenience function to create a project logs retention policy.
    
    Args:
        project_id: The Braintrust project ID
        retention_days: Number of days to retain project logs
        api_key: Optional API key (will use BRAINTRUST_API_KEY env var if not provided)
        name: Optional custom name for the policy
        description: Optional custom description
        
    Returns:
        RetentionPolicyResponse with details of the created policy
    """
    client = BraintrustRetentionClient(api_key=api_key)
    return client.create_retention_policy(
        project_id=project_id,
        object_type="project_logs",
        retention_days=retention_days,
        name=name,
        description=description
    )


def create_experiment_retention(
    project_id: str,
    retention_days: int,
    api_key: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None
) -> RetentionPolicyResponse:
    """
    Convenience function to create an experiment retention policy.
    
    Args:
        project_id: The Braintrust project ID
        retention_days: Number of days to retain experiments
        api_key: Optional API key (will use BRAINTRUST_API_KEY env var if not provided)
        name: Optional custom name for the policy
        description: Optional custom description
        
    Returns:
        RetentionPolicyResponse with details of the created policy
    """
    client = BraintrustRetentionClient(api_key=api_key)
    return client.create_retention_policy(
        project_id=project_id,
        object_type="experiment",
        retention_days=retention_days,
        name=name,
        description=description
    )


def create_dataset_retention(
    project_id: str,
    retention_days: int,
    api_key: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None
) -> RetentionPolicyResponse:
    """
    Convenience function to create a dataset retention policy.
    
    Args:
        project_id: The Braintrust project ID
        retention_days: Number of days to retain datasets
        api_key: Optional API key (will use BRAINTRUST_API_KEY env var if not provided)
        name: Optional custom name for the policy
        description: Optional custom description
        
    Returns:
        RetentionPolicyResponse with details of the created policy
    """
    client = BraintrustRetentionClient(api_key=api_key)
    return client.create_retention_policy(
        project_id=project_id,
        object_type="dataset",
        retention_days=retention_days,
        name=name,
        description=description
    )


if __name__ == "__main__":
    # Example usage
    import sys
    
    # You can set your API key as an environment variable:
    # export BRAINTRUST_API_KEY="your-api-key-here"
    
    if len(sys.argv) < 4:
        print("Usage: python braintrust_retention_client.py <project_id> <object_type> <retention_days>")
        print("Example: python braintrust_retention_client.py 4542abd4-537e-4ca1-b5a4-14964fe42b78 project_logs 90")
        sys.exit(1)
    
    project_id = sys.argv[1]
    object_type = sys.argv[2]
    retention_days = int(sys.argv[3])
    
    try:
        client = BraintrustRetentionClient()
        result = client.create_retention_policy(
            project_id=project_id,
            object_type=object_type,
            retention_days=retention_days
        )
        
        print(f"✅ Successfully created retention policy!")
        print(f"   ID: {result.automation_id}")
        print(f"   Name: {result.name}")
        print(f"   Object Type: {result.object_type}")
        print(f"   Retention Days: {result.retention_days}")
        print(f"   Found Existing: {result.found_existing}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
