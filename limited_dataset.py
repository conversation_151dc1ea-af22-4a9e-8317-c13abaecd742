import braintrust
from typing import Optional, Dict, Any, Union, List, Iterator, cast
from braintrust.logger import (
    Dataset,
    ObjectFetcher,
    DatasetEvent,
    LazyValue,
    ProjectDatasetMetadata,
    ObjectMetadata,
    BraintrustState,
    _state,
    DEFAULT_IS_LEGACY_DATASET,
    ensure_dataset_record,
    _enrich_attachments,
    ObjectIterator,
    response_raise_for_status,
    INTERNAL_BTQL_LIMIT,
    MAX_BTQL_ITERATIONS
)


class LimitedObjectFetcher(ObjectFetcher[DatasetEvent]):
    """
    A custom ObjectFetcher that respects user-specified limits in _internal_btql.
    """
    
    def __init__(
        self,
        object_type: str,
        pinned_version: Union[None, int, str] = None,
        mutate_record: Optional[callable] = None,
        _internal_btql: Optional[Dict[str, Any]] = None,
        max_records: Optional[int] = None,
    ):
        super().__init__(object_type, pinned_version, mutate_record, _internal_btql)
        self.max_records = max_records
        
    def _refetch(self) -> List[DatasetEvent]:
        """
        Custom refetch that respects the max_records limit.
        """
        state = self._get_state()
        if self._fetched_data is None:
            cursor = None
            data = None
            iterations = 0
            records_fetched = 0
            
            while True:
                # Calculate how many more records we need
                remaining_limit = None
                if self.max_records is not None:
                    remaining_limit = self.max_records - records_fetched
                    if remaining_limit <= 0:
                        break
                
                # Use the smaller of INTERNAL_BTQL_LIMIT or remaining_limit
                current_limit = INTERNAL_BTQL_LIMIT
                if remaining_limit is not None:
                    current_limit = min(INTERNAL_BTQL_LIMIT, remaining_limit)
                
                resp = state.api_conn().post(
                    f"btql",
                    json={
                        "query": {
                            **(self._internal_btql or {}),
                            "select": [{"op": "star"}],
                            "from": {
                                "op": "function",
                                "name": {
                                    "op": "ident",
                                    "name": [self.object_type],
                                },
                                "args": [
                                    {
                                        "op": "literal",
                                        "value": self.id,
                                    },
                                ],
                            },
                            "cursor": cursor,
                            "limit": current_limit,  # Use our calculated limit
                        },
                        "use_columnstore": False,
                        "brainstore_realtime": True,
                    },
                    headers={
                        "Accept-Encoding": "gzip",
                    },
                )
                response_raise_for_status(resp)
                resp_json = resp.json()
                batch_data = cast(List[DatasetEvent], resp_json["data"])
                data = (data or []) + batch_data
                records_fetched += len(batch_data)
                
                # Check if we should stop
                if not resp_json.get("cursor", None):
                    break
                if self.max_records is not None and records_fetched >= self.max_records:
                    break
                if len(batch_data) < current_limit:
                    break
                    
                cursor = resp_json.get("cursor", None)
                iterations += 1
                if iterations > MAX_BTQL_ITERATIONS:
                    raise RuntimeError("Too many BTQL iterations")

            if not isinstance(data, list):
                raise ValueError(f"Expected a list in the response, got {type(data)}")
            
            # Apply limit if specified
            if self.max_records is not None:
                data = data[:self.max_records]
                
            if self._mutate_record is not None:
                self._fetched_data = [self._mutate_record(r) for r in data]
            else:
                self._fetched_data = data

        return self._fetched_data


class LimitedDataset(Dataset):
    """
    A Dataset that properly respects record limits.
    """
    
    def __init__(
        self,
        lazy_metadata: LazyValue[ProjectDatasetMetadata],
        version: Union[None, int, str] = None,
        legacy: bool = DEFAULT_IS_LEGACY_DATASET,
        _internal_btql: Optional[Dict[str, Any]] = None,
        state: Optional[BraintrustState] = None,
        max_records: Optional[int] = None,
    ):
        if legacy:
            print(
                f"""Records will be fetched from this dataset in the legacy format, with the "expected" field renamed to "output". Please update your code to use "expected", and use `braintrust.init_dataset()` with `use_output=False`, which will become the default in a future version of Braintrust."""
            )

        def mutate_record(r: DatasetEvent) -> DatasetEvent:
            _enrich_attachments(cast(Dict[str, Any], r))
            return ensure_dataset_record(r, legacy)

        self._lazy_metadata = lazy_metadata
        self.new_records = 0
        self.max_records = max_records

        # Use our custom LimitedObjectFetcher instead of ObjectFetcher
        LimitedObjectFetcher.__init__(
            self,
            object_type="dataset",
            pinned_version=version,
            mutate_record=mutate_record,
            _internal_btql=_internal_btql,
            max_records=max_records,
        )

        self.state = state or _state


def init_limited_dataset(
    project: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None,
    version: Optional[Union[str, int]] = None,
    app_url: Optional[str] = None,
    api_key: Optional[str] = None,
    org_name: Optional[str] = None,
    project_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    use_output: bool = DEFAULT_IS_LEGACY_DATASET,
    _internal_btql: Optional[Dict[str, Any]] = None,
    state: Optional[BraintrustState] = None,
    max_records: Optional[int] = None,
) -> LimitedDataset:
    """
    Create a new limited dataset that respects the max_records parameter.
    
    This function works exactly like braintrust.init_dataset() but properly
    limits the number of records fetched.
    
    Args:
        max_records: Maximum number of records to fetch. If None, fetches all records.
        All other parameters are the same as braintrust.init_dataset()
    
    Returns:
        A LimitedDataset object that behaves like a regular Dataset but respects limits.
    """
    
    def compute_metadata():
        login_state = state or _state
        login_state.login(
            org_name=org_name,
            api_key=api_key,
            app_url=app_url,
        )

        args = {
            "org_id": login_state.org_id,
            "project_name": project,
            "project_id": project_id,
            "dataset_name": name,
            "description": description,
            "metadata": metadata,
        }
        response = login_state.app_conn().post_json("api/dataset/register", args)

        return ProjectDatasetMetadata(
            project=ObjectMetadata(
                id=response["project"]["id"],
                name=response["project"]["name"],
                full_info=response["project"],
            ),
            dataset=ObjectMetadata(
                id=response["dataset"]["id"],
                name=response["dataset"]["name"],
                full_info=response["dataset"],
            ),
        )

    return LimitedDataset(
        lazy_metadata=LazyValue(compute_metadata, use_mutex=True),
        version=version,
        legacy=use_output,
        _internal_btql=_internal_btql,
        state=state,
        max_records=max_records,
    )


# Example usage:
if __name__ == "__main__":
    # This will only fetch 1 record
    dataset = init_limited_dataset(
        project="pedro-project1", 
        name="themes", 
        max_records=1
    )
    
    records = []
    for record in dataset:
        records.append(record)
    
    print(f"Fetched {len(records)} records")
    for i, record in enumerate(records):
        print(f"Record {i}: {record}")
