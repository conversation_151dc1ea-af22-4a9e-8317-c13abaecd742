"""
Remote Eval Logging Demo

This example specifically demonstrates what happens when you create logs
in your remote eval code. Run this to see how all logs are captured!

To run:
1. braintrust eval logging_demo_remote_eval.py --dev
2. Add http://localhost:8300 to your remote eval sources
3. Run from playground with data like: [{"input": "Hello world", "expected": "Hi there"}]
"""

from braintrust import Eval, init_dataset, current_span, traced
from autoevals import LevenshteinScorer


@traced("step_1_analysis")
def analyze_input(input_text: str):
    """This function creates its own span with logs."""
    # Log analysis details
    current_span().log(
        input=input_text,
        metadata={
            "step": "input_analysis",
            "char_count": len(input_text),
            "word_count": len(input_text.split()),
            "has_question_mark": "?" in input_text
        }
    )
    
    # Simulate some analysis
    analysis = {
        "type": "question" if "?" in input_text else "statement",
        "complexity": "simple" if len(input_text.split()) < 10 else "complex",
        "length_category": "short" if len(input_text) < 50 else "long"
    }
    
    # Log the analysis result
    current_span().log(
        output=analysis,
        metadata={"analysis_complete": True}
    )
    
    return analysis


@traced("step_2_processing")
def process_text(input_text: str, analysis: dict):
    """Another traced function that creates additional logs."""
    current_span().log(
        input={"text": input_text, "analysis": analysis},
        metadata={"step": "text_processing"}
    )
    
    # Simulate processing based on analysis
    if analysis["type"] == "question":
        processed = f"QUESTION: {input_text}"
    else:
        processed = f"STATEMENT: {input_text}"
    
    # Log processing result
    current_span().log(
        output=processed,
        metrics={"processing_time": 0.1},
        metadata={"processing_rule": f"applied_{analysis['type']}_formatting"}
    )
    
    return processed


def logging_demo_task(input_text: str, hooks):
    """
    Main task that demonstrates multiple types of logging.
    """
    # 1. Log task start
    hooks.span.log(
        metadata={"step": "task_start", "input_received": True}
    )
    
    # 2. Call traced functions (these create child spans with their own logs)
    analysis = analyze_input(input_text)
    processed_text = process_text(input_text, analysis)
    
    # 3. Create a custom span for business logic
    with current_span().start_span("business_logic") as logic_span:
        logic_span.log(
            input={"processed_text": processed_text, "analysis": analysis},
            metadata={"step": "business_logic_start"}
        )
        
        # Simulate some business logic
        if analysis["complexity"] == "complex":
            result = f"COMPLEX RESPONSE: {processed_text.upper()}"
            logic_span.log(
                metadata={"applied_transformation": "uppercase_for_complex"}
            )
        else:
            result = f"SIMPLE RESPONSE: {processed_text.title()}"
            logic_span.log(
                metadata={"applied_transformation": "title_case_for_simple"}
            )
        
        logic_span.log(
            output=result,
            metrics={"business_logic_time": 0.05}
        )
    
    # 4. Log final task result
    hooks.span.log(
        output=result,
        metadata={
            "step": "task_complete",
            "total_processing_steps": 3,
            "final_result_length": len(result)
        }
    )
    
    return result


def logging_scorer(input: str, output: str, expected: str = None, **kwargs):
    """
    Custom scorer that also creates logs.
    Even scorer logs are captured!
    """
    with current_span().start_span("custom_scoring") as score_span:
        score_span.log(
            input={"eval_input": input, "output": output, "expected": expected},
            metadata={"scorer": "logging_demo_scorer"}
        )
        
        # Simple scoring logic
        score = 1.0 if expected and expected.lower() in output.lower() else 0.5
        
        score_span.log(
            output={"score": score},
            metadata={
                "scoring_logic": "check_if_expected_in_output",
                "match_found": expected and expected.lower() in output.lower() if expected else False
            }
        )
        
        return {
            "name": "logging_demo_score",
            "score": score,
            "metadata": {
                "rationale": f"Expected '{expected}' {'found' if score == 1.0 else 'not found'} in output"
            }
        }


# Define the remote evaluation
Eval(
    "Logging Demo Remote Eval",
    data=init_dataset("local dev", name="logging_demo"),
    task=logging_demo_task,
    scores=[
        LevenshteinScorer,  # Built-in scorer
        logging_scorer,     # Custom scorer with logging
    ],
    metadata={
        "description": "Demonstrates comprehensive logging in remote evals",
        "logging_features": [
            "main_task_logs",
            "traced_function_logs", 
            "custom_span_logs",
            "scorer_logs",
            "error_logs",
            "metrics_logging"
        ]
    }
)

"""
When you run this remote eval, you'll see in Braintrust:

📊 Main Evaluation Span
├── 📝 step_1_analysis (traced function)
│   ├── Input analysis logs
│   └── Analysis result logs
├── 📝 step_2_processing (traced function)  
│   ├── Processing input logs
│   └── Processing result logs
├── 📝 business_logic (custom span)
│   ├── Business logic start logs
│   ├── Transformation logs
│   └── Business logic result logs
├── 📝 custom_scoring (scorer span)
│   ├── Scoring input logs
│   └── Scoring result logs
└── 📝 Task completion logs

All of these logs, metrics, and metadata are automatically captured
and sent to Braintrust, giving you full visibility into your evaluation
pipeline execution!
"""
