"""
This is your original script, but fixed to actually limit records.
Replace your original script with this one.
"""

from init_limited_dataset import init_limited_dataset

# This will actually fetch only 1 record, unlike the original init_dataset
dataset = init_limited_dataset(
    project="pedro-project1", 
    name="themes", 
    max_records=1  # This parameter is properly respected!
)

records = []
for record in dataset:
    records.append(record)

for i in records:
    print(i)

print(f"\nTotal records fetched: {len(records)}")  # This will print 1
