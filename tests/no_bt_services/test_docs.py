import multiprocessing
import os
import unittest

from braintrust_local.doc_snippets import REPO_ROOT, load_docs, process_snippet
from braintrust_local.doc_snippets import setup as setup_doc_snippets
from parameterized import parameterized


def doctest_parameterized_name_func(testcase_func, param_num, param):
    rel_path = os.path.relpath(param.args[0], REPO_ROOT)
    return "%s_%s" % (
        testcase_func.__name__,
        parameterized.to_safe_name(f"{rel_path.rsplit('.', 1)[0]}_{param.args[1]}"),
    )


class DocsTest(unittest.TestCase):
    @parameterized.expand(
        [
            (fpath, i, snippet)
            for (fpath, file) in load_docs().items()
            for (i, snippet) in enumerate(file.code_snippets)
            if snippet.lang is not None
        ],
        name_func=doctest_parameterized_name_func,
        skip_on_empty=True,
    )
    def test_docs(self, fpath, idx, snippet):
        # If this test fails, then here's how to debug and fix it:
        #
        # There is a CLI tool (run `make develop` to initialize it if needed) called
        # `doc-snippets` (defined in local/py/src/braintrust_local/doc_snippets.py).
        # You can point it at an individual file or directory of files, and use the
        # --ops command to specify which operations to run. You can also use `--write`
        # to actually fix the files, where possible.
        setup_doc_snippets(concurrency=multiprocessing.cpu_count(), verbose=False)
        result = process_snippet((fpath, snippet, idx, ["format", "compile"]))
        self.assertTrue(result.unchanged, f"Snippet {fpath} {idx} was reformatted")
        if not result.compiled:
            error = []
            error.append(f"Failed to compile snippet {fpath} {idx}")
            error.append(str(snippet.code))
            error.append("ERRORS:")
            error.append("")
            error.extend(result.compile_errors)
            self.fail("\n".join(error))
