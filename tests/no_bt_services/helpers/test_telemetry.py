import unittest
from typing import cast

from tests.helpers.telemetry import Context, Event, current_context, idempotency, prop


class TelemetryHelpersTest(unittest.TestCase):
    def test_idempotency_comparison(self):
        # Test basic equality
        self.assertTrue(idempotency("a", "b") == '["a","b"]')
        self.assertFalse(idempotency("a", "b") == '["a","c"]')
        self.assertFalse(idempotency("a") == '["a","b"]')

        # Test with different types of arguments
        self.assertTrue(idempotency("test", "123", "xyz") == '["test","123","xyz"]')
        self.assertTrue(idempotency() == "[]")  # Empty case

        # Test string representation
        self.assertEqual(str(idempotency("a", "b", "c")), "idempotency('a', 'b', 'c')")
        self.assertEqual(str(idempotency()), "idempotency()")
        self.assertEqual(str(idempotency("single")), "idempotency('single')")

    def test_prop_extraction(self):
        # Test basic property extraction
        event = cast(Event, {"properties": {"key": "value"}})
        with current_context(Context(event=event)):
            self.assertEqual(prop("key"), "value")

        # Test nested values
        event = cast(Event, {"properties": {"nested": {"a": 1}}})
        with current_context(Context(event=event)):
            self.assertEqual(prop("nested"), {"a": 1})

        # Test with different value types
        event = cast(
            Event,
            {"properties": {"string": "text", "number": 123, "boolean": True, "list": [1, 2, 3], "dict": {"a": 1}}},
        )
        with current_context(Context(event=event)):
            self.assertEqual(prop("string"), "text")
            self.assertEqual(prop("number"), 123)
            self.assertEqual(prop("boolean"), True)
            self.assertEqual(prop("list"), [1, 2, 3])
            self.assertEqual(prop("dict"), {"a": 1})

            # Test error cases
            with self.assertRaises(KeyError):
                self.assertEqual(prop("non_existent"), "")

            with self.assertRaises(KeyError):
                self.assertEqual(prop("key"), "")
