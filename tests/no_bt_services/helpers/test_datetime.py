import unittest
from datetime import datetime

from tests.helpers.datetime import iso8601, timestamp


class DatetimeTest(unittest.TestCase):
    def test_iso8601_comparison(self):
        # Test with expected value
        self.assertTrue(iso8601("2024-03-14T00:00:00Z") == "2024-03-14T00:00:00Z")
        self.assertTrue(iso8601("2024-03-14T00:00:00+00:00") == "2024-03-14T00:00:00+00:00")
        self.assertFalse(iso8601("2024-03-14T00:00:00Z") == "2024-03-14T00:00:00+01:00")

        # Test format validation without expected value
        self.assertTrue(iso8601() == "2024-03-14T15:30:00Z")  # UTC with Z
        self.assertTrue(iso8601() == "2024-03-14T15:30:00+00:00")  # UTC with +00:00
        self.assertFalse(iso8601() == "2024-03-14T15:30:00-07:00")  # PST (non-UTC)
        self.assertFalse(iso8601() == "2024-03-14T15:30:00+01:00")  # CET (non-UTC)
        self.assertFalse(iso8601() == "2024-03-14T15:30:00")  # No timezone
        self.assertFalse(iso8601() == "2024-03-14")  # Date only
        self.assertFalse(iso8601() == "not-a-date")
        self.assertFalse(iso8601() == "2024/03/14")

        # Test non-string comparison
        self.assertFalse(iso8601() == 123)
        self.assertFalse(iso8601() == None)

        # Test representation
        self.assertEqual(str(iso8601()), "iso8601()")
        self.assertEqual(str(iso8601("2024-03-14T00:00:00Z")), "iso8601(2024-03-14T00:00:00Z)")

    def test_timestamp_comparison(self):
        now = datetime.now().timestamp()

        # Test with expected value
        self.assertTrue(timestamp("1577836800") == "1577836800")  # 2020-01-01
        self.assertFalse(timestamp("1577836800") == "1893456000")  # 2030-01-01

        # Test format validation without expected value
        self.assertTrue(timestamp() == str(now))  # Current timestamp
        self.assertTrue(timestamp() == "1577836800")  # Valid timestamp (2020-01-01)
        self.assertTrue(timestamp() == "1577836800.123")  # Valid float timestamp
        self.assertFalse(timestamp() == "1234567890")  # Too early (2009)
        self.assertFalse(timestamp() == "1893456000")  # Too late (2030)
        self.assertFalse(timestamp() == "not-a-timestamp")
        self.assertFalse(timestamp() == "-1")  # Invalid negative time

        # Test non-string comparison
        self.assertFalse(timestamp() == 123)
        self.assertFalse(timestamp() == None)

        # Test representation
        self.assertEqual(str(timestamp()), "timestamp()")
        self.assertEqual(str(timestamp("1577836800")), "timestamp(1577836800)")
