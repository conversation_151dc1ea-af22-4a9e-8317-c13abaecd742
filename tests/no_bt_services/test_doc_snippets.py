import unittest

from braintrust_local.doc_snippets import CodeSnippet, Language, extract_code_snippets_with_language

SNIPPETS = [
    (
        """
```python
def hello_world():
    print("Hello, world!")
```
""",
        CodeSnippet(
            lang_str="python",
            lang=Language.PYTHON,
            code="""\
def hello_world():
    print("Hello, world!")""",
            tags=[],
        ),
    ),
    (
        """
```python #skip-compile #foo
def hello_world():
    print("Hello, world!")
```
""",
        CodeSnippet(
            lang_str="python #skip-compile #foo",
            lang=Language.PYTHON,
            code="""\
def hello_world():
    print("Hello, world!")""",
            tags=["skip-compile", "foo"],
        ),
    ),
    (
        """
```python filename="foo.py" #skip-compile #foo
def hello_world():
    print("Hello, world!")
```
""",
        CodeSnippet(
            lang_str='python filename="foo.py" #skip-compile #foo',
            lang=Language.PYTHON,
            code="""\
def hello_world():
    print("Hello, world!")""",
            tags=["skip-compile", "foo"],
        ),
    ),
    (
        """
```python #bar filename="foo.py" #skip-compile #foo
def hello_world():
    print("Hello, world!")
```
""",
        CodeSnippet(
            lang_str='python #bar filename="foo.py" #skip-compile #foo',
            lang=Language.PYTHON,
            code="""\
def hello_world():
    print("Hello, world!")""",
            tags=["bar", "skip-compile", "foo"],
        ),
    ),
]


class DocSnippetTest(unittest.TestCase):
    def test_doc_snippets(self):
        for text, expected in SNIPPETS:
            file = extract_code_snippets_with_language(text)
            self.assertEqual(len(file.code_snippets), 1)
            self.assertEqual(file.code_snippets[0], expected)
