import hmac
import json
import os
from datetime import datetime, timezone
from functools import lru_cache
from hashlib import sha256
from typing import Literal, NotRequired, TypedDict

import requests
from braintrust import app_conn

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase
from tests.helpers.resources import clear_resource_limits, get_resource_limits, set_resource_limit


class BillingOrbWebhooksTest(BraintrustAppTestBase):
    def test_subscription_plan_changed_free_to_pro(self):
        set_resource_limit(self.org_id, "num_dataset_row_actions_calendar_months", None)  # note the explicit unlimited
        set_resource_limit(self.org_id, "num_dataset_row_actions_calendar_months", None)  # note the explicit unlimited
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 250_000_000))
        set_resource_limit(self.org_id, "num_log_bytes", (7, 250_000_000))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 250_000))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 250_000))
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", (1, 250_000))
        set_resource_limit(self.org_id, "num_production_log_row_actions", (7, 250_000))

        post_orb_webhook(
            {
                "type": "subscription.plan_changed",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_pro_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 250_000),
                "num_production_log_row_actions": (7, 250_000),
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 1_000_000),
                "num_production_log_row_actions_calendar_months": (1, 1_000_000),
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

    def test_subscription_plan_changed_free_to_free(self):
        # assume that we shouldn't reset higher limits
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 1_000_000))
        set_resource_limit(self.org_id, "num_production_log_row_actions", (7, 250_000))
        # note implicit None (unlimited)
        set_resource_limit(self.org_id, "num_log_bytes", None)
        # assume that we shouldn't reset higher limits
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 1_000_000))
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", (1, 250_000))
        # note implicit None (unlimited)
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", None)

        post_orb_webhook(
            {
                "type": "subscription.plan_changed",
                "properties": {
                    "previous_plan_id": get_free_plan()["id"],
                },
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_free_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 1_000_000),
                "num_production_log_row_actions": (7, 250_000),
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 1_000_000),
                "num_production_log_row_actions_calendar_months": (1, 250_000),
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

    def test_subscription_plan_changed_pro_to_pro(self):
        # assume that if prior limits, then we will upgrade them as needed
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 250_000))
        set_resource_limit(self.org_id, "num_production_log_row_actions", (7, 250_000))
        # note implicit None (unlimited)
        set_resource_limit(self.org_id, "num_log_bytes", (7, 1_000_000_000))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 250_000))
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", (1, 250_000))
        # note implicit None (unlimited)
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 1_000_000_000))

        post_orb_webhook(
            {
                "type": "subscription.plan_changed",
                "properties": {
                    # note required otherwise we will assume we should lower limits
                    "previous_plan_id": get_pro_plan()["id"],
                },
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_pro_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 250_000),
                "num_production_log_row_actions": (7, 250_000),
                # if previously unlimited should be kept unlimited
                "num_dataset_row_actions": None,
                # reset to unlimited since pro has unlimited
                "num_log_bytes": None,
                # these should be upgraded
                "num_private_experiment_row_actions_calendar_months": (1, 1_000_000),
                "num_production_log_row_actions_calendar_months": (1, 1_000_000),
                # if previously unlimited should be kept unlimited
                "num_dataset_row_actions_calendar_months": None,
                # reset to unlimited since pro has unlimited
                "num_log_bytes_calendar_months": None,
            },
        )

    maxDiff = None

    def test_subscription_plan_changed_pro_to_free(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 1_000_000))
        # note implicit None (unlimited)
        set_resource_limit(self.org_id, "num_production_log_row_actions", None)
        set_resource_limit(self.org_id, "num_log_bytes", None)
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 1_000_000))
        # note implicit None (unlimited)
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", None)
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", None)

        post_orb_webhook(
            {
                "type": "subscription.plan_changed",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_free_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 62_500),
                "num_production_log_row_actions": (7, 62_500),
                "num_dataset_row_actions": (7, 62_500),
                "num_log_bytes": (7, 62_500_000),
                # regardless of prior unlimited, we will force them back to free plan limits
                "num_private_experiment_row_actions_calendar_months": (1, 250_000),
                "num_production_log_row_actions_calendar_months": (1, 250_000),
                "num_dataset_row_actions_calendar_months": (1, 250_000),
                "num_log_bytes_calendar_months": (1, 250_000_000),
            },
        )

    def test_subscription_started_unlimited(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 10))
        set_resource_limit(self.org_id, "num_log_bytes", (7, 10))
        set_resource_limit(self.org_id, "num_dataset_row_actions", (7, 10))
        set_resource_limit(self.org_id, "num_production_log_row_actions", (7, 10))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 10))
        set_resource_limit(self.org_id, "num_dataset_row_actions_calendar_months", (1, 10))
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", (1, 10))

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": {
                        "id": "enterprise",
                        "metadata": {
                            "logs.sanity": "unlimited",
                            "evals.sanity": "unlimited",
                            "datasets.sanity": "unlimited",
                            "scores.sanity": "unlimited",
                            "logs.bytes": "unlimited",
                        },
                    },
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": None,
                "num_production_log_row_actions": None,
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": None,
                "num_production_log_row_actions_calendar_months": None,
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

    def test_subscription_started_paid(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 250_000))
        set_resource_limit(self.org_id, "num_log_bytes", (7, 250_000_000))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 250_000))
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 250_000_000))

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_pro_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 250_000),
                "num_production_log_row_actions": None,
                "num_dataset_row_actions": None,
                # note we should be clearing this, though
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 1_000_000),
                "num_production_log_row_actions_calendar_months": None,
                "num_dataset_row_actions_calendar_months": None,
                # note we should be clearing this, though
                "num_log_bytes_calendar_months": None,
            },
        )

    def test_subscription_started_free(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 10))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))
        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 10),
                "num_production_log_row_actions": None,
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 10),
                "num_production_log_row_actions_calendar_months": None,
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_free_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions_calendar_months": (1, 250_000),
                # note previous unlimited is still unlimited because we assumed prior decisions/records are correct
                "num_production_log_row_actions": None,
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions": (7, 62_500),
                # note previous unlimited is still unlimited because we assumed prior decisions/records are correct
                "num_production_log_row_actions_calendar_months": None,
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

    def test_subscription_started_free_ignores_non_week_windows(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 10))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))
        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 10),
                "num_production_log_row_actions": None,
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 10),
                "num_production_log_row_actions_calendar_months": None,
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_free_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 62_500),
                "num_production_log_row_actions": None,
                "num_dataset_row_actions": None,
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 250_000),
                "num_production_log_row_actions_calendar_months": None,
                "num_dataset_row_actions_calendar_months": None,
                "num_log_bytes_calendar_months": None,
            },
        )

    def test_subscription_started_unknown_to_free(self):
        clear_resource_limits(self.org_id)

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_free_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 62_500),
                "num_production_log_row_actions": (7, 62_500),
                "num_dataset_row_actions": (7, 62_500),
                "num_log_bytes": (7, 62_500_000),
                "num_private_experiment_row_actions_calendar_months": (1, 250_000),
                "num_production_log_row_actions_calendar_months": (1, 250_000),
                "num_dataset_row_actions_calendar_months": (1, 250_000),
                "num_log_bytes_calendar_months": (1, 250_000_000),
            },
        )

    def test_subscription_started_unknown_to_family(self):
        clear_resource_limits(self.org_id)

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_free_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 62_500),
                "num_production_log_row_actions": (7, 62_500),
                "num_dataset_row_actions": (7, 62_500),
                "num_log_bytes": (7, 62_500_000),
                "num_private_experiment_row_actions_calendar_months": (1, 250_000),
                "num_production_log_row_actions_calendar_months": (1, 250_000),
                "num_dataset_row_actions_calendar_months": (1, 250_000),
                "num_log_bytes_calendar_months": (1, 250_000_000),
            },
        )

    def test_subscription_started_unknown_to_pro(self):
        clear_resource_limits(self.org_id)

        post_orb_webhook(
            {
                "type": "subscription.started",
                "subscription": {
                    "customer": {
                        "external_customer_id": self.org_id,
                    },
                    "plan": get_pro_plan(),
                },
            }
        )

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_private_experiment_row_actions": (7, 250_000),
                "num_production_log_row_actions": (7, 250_000),
                "num_dataset_row_actions": (7, 250_000),
                "num_log_bytes": None,
                "num_private_experiment_row_actions_calendar_months": (1, 1_000_000),
                "num_production_log_row_actions_calendar_months": (1, 1_000_000),
                "num_dataset_row_actions_calendar_months": (1, 1_000_000),
                "num_log_bytes_calendar_months": None,
            },
        )


class OrbCustomer(TypedDict):
    external_customer_id: str


class OrbPlan(TypedDict):
    id: str
    metadata: dict[str, str]


class OrbSubscription(TypedDict):
    customer: OrbCustomer
    plan: OrbPlan


class OrbPayload(TypedDict):
    id: NotRequired[str]
    created_at: NotRequired[str]
    type: Literal["subscription.started"] | Literal["subscription.plan_changed"]
    properties: NotRequired[dict]
    subscription: OrbSubscription


def post_orb_webhook(payload: OrbPayload):
    payload.setdefault("id", "test_id")
    payload.setdefault("properties", {})
    payload.setdefault("created_at", datetime.now(timezone.utc).isoformat())

    body = json.dumps(payload)
    timestamp = datetime.now(timezone.utc).isoformat()

    webhook_secret = os.getenv("ORB_WEBHOOK_SECRET")
    assert webhook_secret, "ORB_WEBHOOK_SECRET must be set in the environment"

    secret = bytes(webhook_secret, "utf-8")
    prefix = f"v1:{timestamp}:"
    message = prefix.encode("utf-8") + body.encode("utf-8")
    hmac_object = hmac.new(secret, message, sha256)

    headers = {
        "X-Orb-Timestamp": timestamp,
        "X-Orb-Signature": f"v1={hmac_object.hexdigest()}",
    }

    resp = requests.post(f"{LOCAL_APP_URL}/api/webhooks/orb", data=body, headers=headers)

    if resp.status_code != 200:
        raise Exception(f"Failed to post orb webhook: {resp.status_code} {resp.text}")

    return resp


@lru_cache
def is_development():
    return app_conn().post("/api/self/env", json={}).json()["env"] == "development"


def get_free_plan() -> OrbPlan:
    return {
        "id": "XNZGjyARNVREdY78" if is_development() else "gPoP9fDHzDm8CgUF",
        "metadata": {
            "datasets.sanity": str(250_000),
            "evals.sanity": str(250_000),
            "logs.sanity": str(250_000),
            "logs.bytes": str(250_000_000),
        },
    }


def get_pro_plan() -> OrbPlan:
    return {
        "id": "F3cWrgBNNbQBnoL3" if is_development() else "6nrSUKpBp9TieqpB",
        "metadata": {
            "datasets.sanity": str(1_000_000),
            "evals.sanity": str(1_000_000),
            "logs.sanity": str(1_000_000),
            "logs.bytes": "unlimited",
        },
    }
