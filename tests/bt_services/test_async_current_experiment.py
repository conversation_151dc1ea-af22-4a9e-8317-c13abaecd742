import asyncio

import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


class AsyncExperimentsTest(BraintrustAppTestBase):
    def test_basic(self):
        async def run_experiment():
            experiment = braintrust.init("p")
            for i in range(10):
                experiment.log(input=i, output=i, scores=dict(correct=1))
                await asyncio.sleep(0.01)
            experiment.flush()
            return experiment.id

        async def run_all_experiments():
            return await asyncio.gather(*[run_experiment() for _ in range(10)])

        exp_ids = asyncio.run(run_all_experiments())
        for id in exp_ids:
            rows = get_object_json("experiment", id)
            rows.sort(key=lambda row: row["input"])
            self.assertEqual(10, len(rows))
            for i, row in enumerate(rows):
                self.assertEqual(i, row["input"])
                self.assertEqual(dict(correct=1), row["scores"])
