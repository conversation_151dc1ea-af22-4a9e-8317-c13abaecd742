import copy
import datetime
import glob
import hashlib
import importlib
import importlib.util
import io
import json
import os
import subprocess
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from itertools import zip_longest
from pathlib import Path
from typing import Any, Optional, cast

import requests
from braintrust_local.api_db_util import get_object_json
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_APP_URL, TEST_ARGS, BraintrustAppTestBase, get_test_proxy_base_url
from tests.helpers.telemetry import (
    Context,
    Event,
    JSONEncoder,
    Properties,
    aggregate_events,
    clear_events,
    current_context,
    dedupe_events,
    get_events,
    telemetry_event_aggregation,
)

# Search the expect-tests directory for all testable files. The expected JSON
# output is expected to live at the same directory as the file +
# EXPECTED_SUFFIX.

EXPECTED_SUFFIX = ".expected.json"
EXPECT_TESTS_DIR = os.path.join(os.path.dirname(__file__), "expect_tests")
ROOT_DIR = os.path.join(os.path.dirname(__file__), "../..")


def glob_scripts(ext):
    return [os.path.abspath(p) for p in glob.glob(os.path.join(EXPECT_TESTS_DIR, "**/" + ext), recursive=True)]


def parameterized_name_func(testcase_func, param_num, param):
    del param_num

    return "%s_%s" % (
        testcase_func.__name__,
        parameterized.to_safe_name(os.path.relpath(param.args[1], EXPECT_TESTS_DIR)),
    )


# Contains a list of expect tests which require S3 access to work. In some
# testing environments we don't bundle S3 so we need to skip these tests.
SKIP_S3_TESTS = [
    "tests/bt_services/expect_tests/attachment.ts",
    "tests/bt_services/expect_tests/attachment.py",
    "tests/bt_services/expect_tests/use_multimodal_prompt.py",
    "tests/bt_services/expect_tests/use_multimodal_prompt.ts",
    "tests/bt_services/expect_tests/otel/vercel_ai_sdk.ts",
]

EVAL_PY_SCRIPTS = glob_scripts("eval_*.py")
PLAIN_PY_SCRIPTS = [s for s in glob_scripts("*.py") if s not in EVAL_PY_SCRIPTS]
EVAL_TS_SCRIPTS = glob_scripts("*.eval.ts")
PLAIN_TS_SCRIPTS = [s for s in glob_scripts("*.ts") if s not in EVAL_TS_SCRIPTS]
ALL_SCRIPTS = [
    *[("eval_py", s) for s in EVAL_PY_SCRIPTS],
    *[("plain_py", s) for s in PLAIN_PY_SCRIPTS],
    *[("eval_ts", s) for s in EVAL_TS_SCRIPTS],
    *[("plain_ts", s) for s in PLAIN_TS_SCRIPTS],
]

if TEST_ARGS.get("expect_test_filter"):
    SCRIPT_FILTERS = set(os.path.abspath(f) for f in TEST_ARGS.get("expect_test_filter"))
    FILTERED_SCRIPTS = [s for s in ALL_SCRIPTS if s[1] in SCRIPT_FILTERS]
else:
    FILTERED_SCRIPTS = ALL_SCRIPTS

# Filter out telemetry tests
FILTERED_SCRIPTS = [s for s in FILTERED_SCRIPTS if not s[1].endswith(".telemetry.py")]

if BraintrustAppTestBase.skip_s3():
    SKIP_S3_FULL_PATHS = set(os.path.realpath(os.path.join(ROOT_DIR, f)) for f in SKIP_S3_TESTS)
    FILTERED_SCRIPTS = [s for s in FILTERED_SCRIPTS if s[1] not in SKIP_S3_FULL_PATHS]


# The following (nested) fields are randomly-generated, time-sensitive, or
# filesystem-dependent, so they cannot be used to sort rows lexicographically.
with open(os.path.join(os.path.dirname(__file__), "test_expect_nondeterministic_fields.json")) as f:
    NONDETERMINISTIC_FIELDS = [tuple(x) for x in json.load(f)]

PRESENCE_FIELDS = [
    "error",
]

REMOVE_FIELDS = [
    "_pagination_key",
]

# However we still wish to maintain "semantic" linkages between rows, which use
# some of these nondeterministic IDs to identify rows. So we build content
# hashes mapping each non-deterministic ID to a "deterministic" version of the
# row they belong to. Thus it is okay for one row to contain multiple IDs, and
# also for multiple IDS to map to the same content hash, but it is not okay for
# the same ID to map to different content hashes.
CONTENT_HASH_FIELDS = [
    ("id",),
    ("span_id",),
]

# Certain tests may wish to exclude certain nondeterministic fields, if they
# override them specifically. We allow doing this on a per-test basis.
PER_TEST_DETERMINISTIC_FIELDS = {
    "top_level_metrics.ts": set(
        [
            ("metrics", "start"),
            ("metrics", "end"),
        ]
    )
}


def remove_attachment_keys(row) -> None:
    """Attachments can be nested anywhere, so we need to explore recursively."""
    if type(row) == dict:
        if row.get("type") == "braintrust_attachment" and "key" in row:
            del row["key"]
            return
        for child in row.values():
            remove_attachment_keys(child)
    elif type(row) == list:
        for child in row:
            remove_attachment_keys(child)


class ParseAsDatetimeError(Exception):
    pass


def parse_as_datetime(val: str) -> datetime.datetime:
    VALID_FORMATS = [
        "%Y-%m-%dT%H:%M:%S.%fZ",  # Example: 2024-10-31T19:02:09.000Z
        "%Y-%m-%dT%H:%M:%SZ",  # Example: 2024-10-31T19:02:09Z
        "%Y-%m-%dT%H:%M:%S.%f",  # Example: 2024-10-31T19:02:09.000
        "%Y-%m-%dT%H:%M:%S",  # Example: 2024-10-31T19:02:09
    ]

    for fmt in VALID_FORMATS:
        try:
            return datetime.datetime.strptime(val, fmt)
        except ValueError:
            pass

    raise ParseAsDatetimeError()


def sanitize_datetimes(row: Any) -> Any:
    """Datetimes can be formatted in a variety of ways (e.g. brainstore
    compaction will auto-detect timestamps within JSON and then we'll reformat
    them in a canonical way, which may differ from the original row).

    Thus we walk through the object, try to parse each string-typed value as a
    datetime, and if successful, format it in a standard way.
    """

    if type(row) == dict:
        for key, value in row.items():
            row[key] = sanitize_datetimes(value)
    elif type(row) == list:
        for i, value in enumerate(row):
            row[i] = sanitize_datetimes(value)
    elif type(row) == str:
        try:
            dt = parse_as_datetime(row)
            row = dt.isoformat()
        except ParseAsDatetimeError:
            pass

    return row


def sanitize_floats(row: Any) -> Any:
    """Floats can differ at very high precisions across different query
    execution frameworks for a variety of reasons (rust JSON deserialization,
    order of operations when computing aggregates, etc). To paper over all of
    this, we round float-like numbers to a fixed number of decimal places.
    """

    if type(row) == dict:
        for key, value in row.items():
            row[key] = sanitize_floats(value)
    elif type(row) == list:
        for i, value in enumerate(row):
            row[i] = sanitize_floats(value)
    elif type(row) == float:
        row = round(row, 6)

    return row


# Drill down into the object to the innermost-object containing the last entry
# of field_names. If there is no matching field along the way, returns None.
def get_nested_obj(d, field_names):
    assert type(d) == dict
    assert len(field_names) > 0
    curr = d
    for field in field_names[:-1]:
        curr = curr.get(field)
        if curr is None or type(curr) != dict:
            return None
    if curr.get(field_names[-1]) is None:
        return None
    return curr


def make_deterministic(orig_row, id_to_content_hash, deterministic_fields=None):
    row = copy.deepcopy(orig_row)

    remove_attachment_keys(row)
    row = sanitize_datetimes(row)
    row = sanitize_floats(row)

    for field_names in NONDETERMINISTIC_FIELDS:
        if deterministic_fields is not None and field_names in deterministic_fields:
            continue
        nested_obj = get_nested_obj(row, field_names)
        if nested_obj is None:
            continue
        val = nested_obj[field_names[-1]]
        if type(val) == list:
            # Assume all elements of the list are nondeterministic. E.g.
            # span_parents. But the length of the list is deterministic.
            newval = []
            for item in val:
                if item in id_to_content_hash:
                    newval.append(id_to_content_hash[item])
                else:
                    newval.append("")
            nested_obj[field_names[-1]] = newval
        elif type(val) == dict:
            # Assume all the values of the dictionary are nondeterministic, but
            # the keys are deterministic.
            newval = {}
            for k, v in val.items():
                if v in id_to_content_hash:
                    newval[k] = id_to_content_hash[v]
                else:
                    newval[k] = ""
            nested_obj[field_names[-1]] = newval
        else:
            if val in id_to_content_hash:
                nested_obj[field_names[-1]] = id_to_content_hash[val]
            else:
                del nested_obj[field_names[-1]]

    for field_name in PRESENCE_FIELDS:
        field_value = row.get(field_name)
        if not field_value and field_name in row:
            del row[field_name]
        elif field_value:
            row[field_name] = f"<has_{field_name}>"

    for field_name in REMOVE_FIELDS:
        if field_name in row:
            del row[field_name]

    return row


def determinize_rows(orig_rows, deterministic_fields=None):
    # First build the mapping from ID to deterministic content hash. We
    # accomplish this by computing a fully-whited-out version of each row, then
    # mapping each of the IDs in CONTENT_HASH_FIELDS in each row to the
    # corresponding row's content hash.
    fully_whited_out_rows = [
        (json.dumps(make_deterministic(r, {}, deterministic_fields), sort_keys=True), r) for r in orig_rows
    ]
    id_to_content_hash = {}
    for fully_whited_out_row, row in fully_whited_out_rows:
        content_hash = hashlib.sha256(fully_whited_out_row.encode()).hexdigest()
        for field_names in CONTENT_HASH_FIELDS:
            nested_obj = get_nested_obj(row, field_names)
            if nested_obj is None:
                continue
            id = nested_obj[field_names[-1]]
            assert type(id) != list and type(id) != dict
            if id in id_to_content_hash:
                assert (
                    id_to_content_hash[id] == content_hash
                ), f"Multiple values for nested field {field_names} in rows:\n{orig_rows}"
            id_to_content_hash[id] = content_hash

    # Next we can build a determinsitic version of each row which whites out all
    # nondeterminsitic fields *except* those in `id_to_content_hash`. We sort
    # the lines by (row.get("input"), their stringified representation), as a
    # reasonably-stable-but-deterministic ordering.
    determinized_rows = [make_deterministic(row, id_to_content_hash, deterministic_fields) for row in orig_rows]
    determinized_rows.sort(key=lambda r: (json.dumps(r.get("input"), sort_keys=True), json.dumps(r, sort_keys=True)))
    return determinized_rows


def compute_difference(output_rows, expected_rows):
    # Create a mapping from content hash to element and content hash to
    # frequency of element in each of the lists.
    def compute_mappings(rows):
        hash_to_elem = {}
        hash_to_freq = defaultdict(int)
        for row in rows:
            content_hash = hashlib.sha256(json.dumps(row, sort_keys=True).encode()).hexdigest()
            hash_to_elem[content_hash] = row
            hash_to_freq[content_hash] += 1
        return hash_to_elem, hash_to_freq

    output_hash_to_elem, output_hash_to_freq = compute_mappings(output_rows)
    expected_hash_to_elem, expected_hash_to_freq = compute_mappings(expected_rows)

    # Discount the common elements.
    for hash_to_freq in [output_hash_to_freq, expected_hash_to_freq]:
        for hash, freq in hash_to_freq.items():
            for i in range(freq):
                if output_hash_to_freq[hash] > 0 and expected_hash_to_freq[hash] > 0:
                    output_hash_to_freq[hash] -= 1
                    expected_hash_to_freq[hash] -= 1

    # Add the remaining ones.
    only_in_output = []
    only_in_expected = []
    for hash, freq in output_hash_to_freq.items():
        for i in range(freq):
            only_in_output.append(output_hash_to_elem[hash])
    for hash, freq in expected_hash_to_freq.items():
        for i in range(freq):
            only_in_expected.append(expected_hash_to_elem[hash])
    return dict(only_in_output=only_in_output, only_in_expected=only_in_expected)


class ExpectTest(BraintrustAppTestBase):
    maxDiff = None

    def _find_all_objects(self):
        ret = set()
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT id FROM projects WHERE org_id = %s", (self.org_id,))
                for row in cursor:
                    ret.add(("project_logs", row[0]))
                cursor.execute(
                    """
                    SELECT experiments.id
                    FROM experiments
                        JOIN projects ON (experiments.project_id = projects.id)
                    WHERE org_id = %s
                    """,
                    (self.org_id,),
                )
                for row in cursor:
                    ret.add(("experiment", row[0]))
                cursor.execute(
                    """
                    SELECT datasets.id
                    FROM datasets
                        JOIN projects ON (datasets.project_id = projects.id)
                    WHERE org_id = %s
                    """,
                    (self.org_id,),
                )
                for row in cursor:
                    ret.add(("dataset", row[0]))
                cursor.execute(
                    """
                    SELECT prompt_sessions.id
                    FROM prompt_sessions
                        JOIN projects ON (prompt_sessions.project_id = projects.id)
                    WHERE projects.org_id = %s
                    """,
                    (self.org_id,),
                )
                for row in cursor:
                    ret.add(("prompt_session", row[0]))
        return ret

    def _get_all_object_rows(self):
        results = []
        with ThreadPoolExecutor(16) as executor:
            for obj_type, id in self._find_all_objects():
                results.append(executor.submit(get_object_json, obj_type, id))
                results.append(executor.submit(get_object_json, obj_type, id, audit_log=True))
        rows = []
        for res in results:
            for row in res.result():
                assert type(row) == dict, row
                # Exclude all the audit entries, since they can be
                # nondeterministic depending on how logging is batched.
                if row.get("audit_data"):
                    continue
                # Exclude OTEL fetch.POST spans, since the Vercel AI SDK
                # sometimes generates an extra one (see `vercel_ai_sdk.ts`).
                # These spans are not relevant to the purpose of the tests.
                if (row.get("metadata") or {}).get("operation.name") == "fetch.POST":
                    continue
                rows.append(row)
        return rows

    @parameterized.expand(
        [(test_type, file_path) for test_type, file_path in FILTERED_SCRIPTS],
        name_func=parameterized_name_func,
        skip_on_empty=True,
    )
    def test_expect(self, test_type, file_path):
        self.run_expect_test(test_type, file_path)

    def run_expect_test(self, test_type: str, file_path: str):
        deterministic_fields = PER_TEST_DETERMINISTIC_FIELDS.get(os.path.relpath(file_path, EXPECT_TESTS_DIR))
        root_file_path = os.path.relpath(file_path, ROOT_DIR)

        if TEST_ARGS.get("expect_test_resanitize"):
            with open(file_path + EXPECTED_SUFFIX) as f:
                expected_rows = json.load(f)
            expected_rows = determinize_rows(expected_rows, deterministic_fields)
            expected_str = json.dumps(expected_rows, indent=4, sort_keys=True)
            with open(file_path + EXPECTED_SUFFIX, "w") as f:
                f.write(expected_str + "\n")

            return

        RUN_CMDS = {
            "eval_py": lambda: subprocess.run(
                [
                    "braintrust",
                    "eval",
                    "--verbose",
                    "--num-workers",
                    "4",
                    "--terminate-on-failure",
                    "--org-name",
                    self.org_name,
                    "--app-url",
                    LOCAL_APP_URL,
                    "--api-key",
                    self.org_api_key,
                    "--env-file",
                    os.path.join(EXPECT_TESTS_DIR, ".env.local"),
                    root_file_path,
                ],
                cwd=ROOT_DIR,
                check=True,
                env=self._get_testenv(),
            ),
            "plain_py": lambda: subprocess.run(
                ["python", root_file_path], cwd=ROOT_DIR, check=True, env=self._get_testenv()
            ),
            "eval_ts": lambda: subprocess.run(
                [
                    "npx",
                    "braintrust",
                    "eval",
                    "--verbose",
                    "--terminate-on-failure",
                    "--org-name",
                    self.org_name,
                    root_file_path,
                    "--app-url",
                    LOCAL_APP_URL,
                    "--api-key",
                    self.org_api_key,
                    "--env-file",
                    os.path.join(EXPECT_TESTS_DIR, ".env.local"),
                ],
                cwd=ROOT_DIR,
                check=True,
                env=self._get_testenv(),
            ),
            "plain_ts": lambda: subprocess.run(
                ["npx", "tsx", root_file_path],
                cwd=ROOT_DIR,
                check=True,
                env=self._get_testenv(),
            ),
        }

        with telemetry_event_aggregation(self.org_id, enabled=False):
            RUN_CMDS[test_type]()

        # Close out all pending flushable states (so they dump to logs).
        BraintrustAppTestBase.flush_proxy_promises()

        output_rows = determinize_rows(self._get_all_object_rows(), deterministic_fields)
        output_str = json.dumps(output_rows, indent=4, sort_keys=True)

        if TEST_ARGS.get("update"):
            with open(file_path + EXPECTED_SUFFIX, "w") as f:
                f.write(output_str + "\n")
            requests.get(f"{get_test_proxy_base_url()}/proxy/dump-cache").raise_for_status()
        else:
            with open(file_path + EXPECTED_SUFFIX) as f:
                expected_rows = json.load(f)

            if output_rows != expected_rows:
                differences = compute_difference(output_rows, expected_rows)
                assert (
                    sum(len(l) for l in differences.values()) > 0
                ), f"Unexpected: didn't find any differences between {output_rows} and {expected_rows}"
                row_to_str = lambda row: json.dumps(row, indent=4, sort_keys=True)
                with io.StringIO() as buf:
                    buf.write("Mismatch between output and expected\n")
                    if TEST_ARGS.get("verbose"):
                        buf.write("Rows in output only:\n")
                        for r in differences["only_in_output"]:
                            buf.write(row_to_str(r) + "\n")
                        buf.write("Rows in expected only:\n")
                        for r in differences["only_in_expected"]:
                            buf.write(row_to_str(r) + "\n")
                    else:
                        if len(differences["only_in_output"]) > 0:
                            buf.write(
                                f"First row in output but not expected:\n{row_to_str(differences['only_in_output'][0])}\n"
                            )
                        elif len(differences["only_in_expected"]) > 0:
                            buf.write(
                                f"First row in expected but not output:\n{row_to_str(differences['only_in_expected'][0])}\n"
                            )
                        else:
                            assert False
                    output = buf.getvalue()
                self.assertTrue(False, output)

        if has_telemetry_test(file_path):
            actual_events = get_events(self.org_id)

            telemetry_file_path = f"{file_path}.telemetry.py"
            test = import_module_from_file_path(telemetry_file_path)

            generated_events = test.task()

            if not isinstance(generated_events, dict):
                raise ValueError(f"Malformed expected events.. need to migrate {telemetry_file_path}")

            def compare(self, expected, actual):
                if len(expected) == 0:
                    self.assertEqual(actual, expected, "expecting no events")
                else:
                    self.assert_events_in(actual, expected, f"\n\n{telemetry_file_path}")

            try:
                compare(self, generated_events["individual"], dedupe_events(actual_events))
                compare(self, generated_events["aggregated"], aggregate_events(self.org_id, actual_events))
            finally:
                clear_events(self.org_id)

    def assert_events_in(
        self,
        actual_events: list[Event],
        expected_events: list[Event],
        msg: str = "",
    ) -> None:
        remaining_expected = expected_events.copy()

        def index_of_expected_event(actual: Event):
            for i, expected in enumerate(remaining_expected):
                try:
                    self.assert_events_equal([actual], [expected], f"{msg}\nFailed at event '{i}'")
                    return i
                except CompareError:
                    continue
            return -1

        for actual in actual_events:
            index = index_of_expected_event(actual)
            if index != -1:
                remaining_expected.pop(index)

        if len(remaining_expected) > 0:
            self.fail(
                f"{msg}\nExpected events not found:\nactual: {json.dumps(actual_events, indent=2)}\n\nexpected: {json.dumps(remaining_expected, cls=JSONEncoder, indent=2)}"
            )

    def assert_events_equal(
        self,
        actual_events: list[Event],
        expected_events: list[Event],
        msg: str = "",
    ) -> None:
        def compare(actual: Event | Properties, expected: Event | Properties, msg: str) -> None:
            for k, v in expected.items():
                if isinstance(v, dict):
                    compare(cast(Properties, actual[k]), cast(Properties, v), msg)
                else:
                    if actual[k] != v:
                        raise CompareError(f"{msg} property '{k}': {actual[k]} != {v}.")

        for i, (actual_event, expected_event) in enumerate(
            zip_longest(actual_events, expected_events, fillvalue=None)
        ):
            if expected_event is None or actual_event is None:
                if actual_event is None:
                    raise CompareError(f"{msg}\nActual event is None")
                else:
                    raise CompareError(f"{msg}\nExpected event is None")
            else:
                with current_context(
                    Context(
                        org_id=self.org_id,
                        user_id=self.user_id,
                        app_url=LOCAL_APP_URL,
                        proxy_url=get_test_proxy_base_url(),
                        event=actual_event,
                    )
                ):
                    compare(actual_event, expected_event, f"{msg}\nFailed at event '{i}'")


def import_module_from_file_path(file_path: str, module_name: Optional[str] = None):
    """Import a module from a file path.

    Args:
        file_path: The full path to the Python file
        module_name: Optional name for the module. If None, uses the file name

    Returns:
        The imported module
    """
    if module_name is None:
        module_name = os.path.splitext(os.path.basename(file_path))[0]

    spec = importlib.util.spec_from_file_location(module_name, file_path)
    if spec is None:
        raise ImportError(f"Could not load spec for module at {file_path}")

    module = importlib.util.module_from_spec(spec)
    if spec.loader is None:
        raise ImportError(f"Could not load module at {file_path}")

    spec.loader.exec_module(module)
    return module


def has_telemetry_test(script_path: str):
    return Path(script_path + ".telemetry.py").exists()


# used to avoid catching AssertionError which may thrown within the code
class CompareError(Exception):
    pass
