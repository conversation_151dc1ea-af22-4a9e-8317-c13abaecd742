import os

import braintrust
import requests

from tests.braintrust_app_test_base import BraintrustAppTestBase


class ProxyTestBase(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.logger = braintrust.init_logger(project="proxy test")
        self.proxy_url = braintrust.logger._state.proxy_url


class ProxyTempCredentialTest(ProxyTestBase):
    def test_create_temp_credential(self):
        """Test that a temporary credential can be issued."""
        BRAINTRUST_API_KEY = os.environ.get("BRAINTRUST_API_KEY")
        response = requests.post(
            f"{self.proxy_url}/v1/credentials",
            headers={
                "Authorization": f"Bearer {BRAINTRUST_API_KEY}",
            },
            json={
                "model": "gpt-4o",
                "ttl_seconds": 60 * 10,  # 10 minutes.
            },
        )

        self.assertEqual(response.status_code, 200, f"Failed to request temporary credentials: {response.text}")

        temp_credential = response.json().get("key")
        self.assertRegex(temp_credential, r"(^[A-Za-z0-9-_]*\.[A-Za-z0-9-_]*\.[A-Za-z0-9-_]*$)")

    def test_no_upgrade(self):
        """
        Ensure that a temp credential cannot be used to issue another temp
        credential.
        """

        # Issue a temp credential.
        BRAINTRUST_API_KEY = os.environ.get("BRAINTRUST_API_KEY")
        response = requests.post(
            f"{self.proxy_url}/v1/credentials",
            headers={
                "Authorization": f"Bearer {BRAINTRUST_API_KEY}",
            },
            json={
                "model": "gpt-4o",
                "ttl_seconds": 60 * 10,  # 10 minutes.
            },
        )

        self.assertEqual(response.status_code, 200, f"Failed to request temporary credentials: {response.text}")
        temp_credential = response.json().get("key")

        # Issue an identical temp credential.
        response = requests.post(
            f"{self.proxy_url}/v1/credentials",
            headers={
                "Authorization": f"Bearer {temp_credential}",
            },
            json={
                "model": "gpt-4o",
                "ttl_seconds": 60 * 10,  # 10 minutes.
            },
        )
        self.assertNotEqual(response.status_code, 200, "Request was expected to fail")

        # Issue a temp credential with more permissions.
        response = requests.post(
            f"{self.proxy_url}/v1/credentials",
            headers={
                "Authorization": f"Bearer {temp_credential}",
            },
            json={
                "model": None,
                "ttl_seconds": 60 * 60,  # 60 minutes.
            },
        )
        self.assertNotEqual(response.status_code, 200, "Request was expected to fail")
