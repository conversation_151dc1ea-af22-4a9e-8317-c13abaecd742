import errno
import http.server
import json
import random
import socket
import threading
import time
import unittest
from contextlib import closing
from datetime import datetime, timed<PERSON>ta
from typing import Callable
from urllib.parse import parse_qs, urlparse
from uuid import uuid4

import braintrust
from braintrust.logger import app_conn
from braintrust_local.api_db_util import log3_raw
from braintrust_local.constants import TEST_CRON_SECRET
from braintrust_local.vacuum_test_util import vacuum_segment_wal_for_testing

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase

LOCAL_BRAINSTORE_URL = "http://localhost:4000"


class AutomationTestBase(BraintrustAppTestBase):
    def _create_automation(self, project_id, automation, api_key=None):
        request_kwargs = {} if not api_key else {"headers": {"Authorization": f"Bearer {api_key}"}}
        return self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/project_automation/register",
            json=dict(
                project_automation_name=automation["name"],
                description=automation.get("description", ""),
                project_id=project_id,
                config=automation["config"],
            ),
            expect_error=False,
            allow_500_errors=False,
            skip_enable_audit=False,
            **request_kwargs,
        )

    def _set_automations(self, project_id, automations):
        existing_automations = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_automation", params={"project_id": project_id}
        ).json()
        for c in existing_automations["objects"]:
            self.run_request("delete", f"{LOCAL_API_URL}/v1/project_automation/{c['id']}")

        ids = []
        for a in automations:
            resp = self._create_automation(project_id, a)
            ids.append(resp.json()["project_automation"]["id"])
        return ids

    def _update_automation(self, project_id, automation, api_key=None):
        request_kwargs = {} if not api_key else {"headers": {"Authorization": f"Bearer {api_key}"}}
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/project_automation/{automation['id']}",
            json=dict(
                name=automation["name"],
                description=automation.get("description", ""),
                config=automation["config"],
            ),
            expect_error=False,
            allow_500_errors=False,
            skip_enable_audit=False,
            **request_kwargs,
        )

    def _flush(self):
        braintrust.flush()
        BraintrustAppTestBase.flush_proxy_promises()


class WebhookHandler(http.server.BaseHTTPRequestHandler):
    response_func: Callable[[], tuple[int, str]] | None = None

    webhook_requests = []
    webhook_responses = []
    delete_requests = []

    @classmethod
    def webhook_summary(cls):
        return [
            (
                a["automation"]["name"],
                a["details"]["count"],
                a["details"]["is_test"],
            )
            for a in cls.webhook_requests
        ]

    @classmethod
    def clear(cls):
        cls.webhook_requests = []
        cls.webhook_responses = []
        cls.response_func = None

    @classmethod
    def set_response(cls, response_func):
        cls.response_func = response_func

    def do_POST(self):
        content_length = int(self.headers["Content-Length"])
        post_data = self.rfile.read(content_length)
        WebhookHandler.webhook_requests.append(json.loads(post_data))
        if WebhookHandler.response_func:
            response = WebhookHandler.response_func()
        else:
            response = (200, "OK")
        self.send_response(*response)
        self.end_headers()
        WebhookHandler.webhook_responses.append(response)

    def do_DELETE(self):
        WebhookHandler.delete_requests.append({})

    # Silence the logging output
    def log_message(self, format, *args):
        pass


def get_open_port():
    for attempts in range(5):
        port = random.randint(3001, 4999)
        with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
            res = sock.connect_ex(("localhost", port))
            if res == errno.ECONNREFUSED:
                return port
    raise RuntimeError("Could not find an open port")


class AutomationTest(AutomationTestBase):
    def setUp(self):
        super().setUp()

        # All of these tests depend on webhook services being set up.
        if BraintrustAppTestBase.skip_webhook():
            raise unittest.SkipTest("")

        # Start webhook server
        self.server_port = get_open_port()
        self.server = http.server.HTTPServer(("localhost", self.server_port), WebhookHandler)
        self.server_url = f"http://{self.server.server_address[0]}:{self.server.server_address[1]}"
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True  # Don't hang if test fails
        self.server_thread.start()
        WebhookHandler.clear()

    def tearDown(self):
        # Stop webhook server
        self.server.shutdown()
        self.server.server_close()
        self.server_thread.join()

    def test_basic(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="basic",
                description="a basic automation",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
        # automation should not trigger on logs which don't match the filter
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="goo", output="bar", expected="baz", metadata=dict(x="goo"))
        # automation should not trigger on logs which are missing filter fields
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="goo", output="bar", expected="baz", metadata=dict(this_is_not_x="foo"))
        self._flush()

        self.maxDiff = None
        webhooks = WebhookHandler.webhook_requests
        self.assertEqual(len(webhooks), 1)
        self.assertEqual(set(webhooks[0].keys()), {"organization", "project", "automation", "details"})
        self.assertEqual(
            webhooks[0]["automation"],
            dict(
                id=automation_ids[0],
                name=automations[0]["name"],
                description=automations[0]["description"],
                event_type=automations[0]["config"]["event_type"],
                btql_filter=automations[0]["config"]["btql_filter"],
                interval_seconds=automations[0]["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/alerts?aid={automation_ids[0]}",
            ),
        )
        self.assertEqual(
            set(webhooks[0]["details"].keys()),
            {"is_test", "message", "time_start", "time_end", "count", "related_logs_url"},
        )
        self.assertEqual(webhooks[0]["details"]["is_test"], False)
        self.assertEqual(
            webhooks[0]["details"]["message"],
            f"{automations[0]['name']}: 1 logs triggered alert in the past 1 second",
        )
        self.assertEqual(webhooks[0]["details"]["count"], 1)

        # its annoying to url encode with the same whitespace as node so decode and compare the raw clauses
        filter_clause = parse_qs(urlparse(webhooks[0]["details"]["related_logs_url"]).query)["search"][0]
        time_start = webhooks[0]["details"]["time_start"]
        time_end = webhooks[0]["details"]["time_end"]
        self.assertEqual(
            json.loads(filter_clause),
            {
                "filter": [
                    {
                        "text": automations[0]["config"]["btql_filter"],
                        "label": automations[0]["config"]["btql_filter"],
                        "originType": "btql",
                    },
                    {
                        "text": f'created >= "{time_start}" AND created < "{time_end}"',
                        "label": f'created >= "{time_start}" AND created < "{time_end}"',
                        "originType": "btql",
                    },
                ]
            },
        )

    def test_project_automation_delete(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="basic",
                description="a basic automation",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        self.assertEqual(len(WebhookHandler.delete_requests), 0)

        res = app_conn().delete("/api/project_automation/delete_id", json={"id": automation_ids[0]})
        res.raise_for_status()

        self.assertEqual(len(WebhookHandler.delete_requests), 1)

    def test_empty(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="empty_filter",
                config=dict(
                    event_type="logs",
                    btql_filter="",
                    interval_seconds=5,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        self._flush()

        self.maxDiff = None
        webhooks = WebhookHandler.webhook_requests
        self.assertEqual(len(webhooks), 1)
        self.assertEqual(set(webhooks[0].keys()), {"organization", "project", "automation", "details"})
        self.assertEqual(
            webhooks[0]["automation"],
            dict(
                id=automation_ids[0],
                name=automations[0]["name"],
                description=automations[0].get("description", ""),
                event_type=automations[0]["config"]["event_type"],
                btql_filter=None,
                interval_seconds=automations[0]["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/alerts?aid={automation_ids[0]}",
            ),
        )
        self.assertEqual(
            set(webhooks[0]["details"].keys()),
            {"is_test", "message", "time_start", "time_end", "count", "related_logs_url"},
        )
        self.assertEqual(webhooks[0]["details"]["is_test"], False)
        self.assertEqual(
            webhooks[0]["details"]["message"],
            f"{automations[0]['name']}: 1 logs triggered alert in the past 5 seconds",
        )
        self.assertEqual(webhooks[0]["details"]["count"], 1)

        # its annoying to url encode with the same whitespace as node so decode and compare the raw clauses
        filter_clause = parse_qs(urlparse(webhooks[0]["details"]["related_logs_url"]).query)["search"][0]
        time_start = webhooks[0]["details"]["time_start"]
        time_end = webhooks[0]["details"]["time_end"]
        self.assertEqual(
            json.loads(filter_clause),
            {
                "filter": [
                    {
                        "text": f'created >= "{time_start}" AND created < "{time_end}"',
                        "label": f'created >= "{time_start}" AND created < "{time_end}"',
                        "originType": "btql",
                    },
                ]
            },
        )

    def test_interval(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="interval",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(WebhookHandler.webhook_summary(), [(automations[0]["name"], 1, False)])

        previous_webhooks = WebhookHandler.webhook_summary()
        # logging again should not trigger automation because of interval
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="goo", output="gar", expected="bar", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(WebhookHandler.webhook_summary(), previous_webhooks)

    def test_multi_project_insert(self):
        logger0 = braintrust.init_logger("test0")
        logger1 = braintrust.init_logger("test1")
        logger2 = braintrust.init_logger("test2")
        automations = [
            dict(
                name="multi_project_insert",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        self._set_automations(logger0.project.id, automations)
        self._set_automations(logger1.project.id, automations)

        # Use sync flush mode so that we can write to all three projects in a
        # single request.
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            for l in [logger0, logger1, logger2]:
                l.log(id="row", input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
            custom_bg_logger.flush()
        self._flush()

        # Check that the automation triggered in first 2 projects
        self.assertEqual(
            WebhookHandler.webhook_summary(),
            [
                (automations[0]["name"], 1, False),
                (automations[0]["name"], 1, False),
            ],
        )

    def test_multiple_automations(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="automation1",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
            dict(
                name="automation2",
                config=dict(
                    event_type="logs",
                    btql_filter="scores.Exactmatch < 0.5",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
            dict(
                name="automation3",
                config=dict(
                    event_type="logs",
                    btql_filter="input = '12'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
        ]
        self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="baz", scores=dict(Exactmatch=0.25))
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="12", expected="baz")
        with braintrust.start_span(id="row3") as span3:
            span3.log(input="foo", output="bar", expected="baz")
        self._flush()

        self.assertEqual(
            set(WebhookHandler.webhook_summary()),
            {
                ("automation1", 1, False),
                ("automation2", 1, False),
                ("automation3", 1, False),
            },
        )

    def test_automation_cache(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id

        # Initial automation with filter for x='foo'
        automations = [
            dict(
                name="cache_test",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="test", output="result", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test", 1, False)])

        self._update_automation(
            project_id,
            dict(
                id=automation_ids[0],
                name="cache_test_updated",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'bar'",
                    interval_seconds=60 * 60,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
        )
        WebhookHandler.clear()

        # Log with metadata.x = 'bar', should trigger webhook with updated filter
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="test", output="result", metadata=dict(x="bar"))
        self._flush()

        # Verify webhook was received with updated filter
        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test_updated", 1, False)])

        with braintrust.start_span(id="row2") as span2:
            span2.log(input="test", output="result", metadata=dict(x="bar"))
        self._flush()

        # not triggered again because of last run cache
        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test_updated", 1, False)])

        self._update_automation(
            project_id,
            dict(
                id=automation_ids[0],
                name="cache_test_updated_smaller_interval",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'bar'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
        )
        WebhookHandler.clear()

        time.sleep(1)
        with braintrust.start_span(id="row3") as span3:
            span3.log(input="test", output="result", metadata=dict(x="bar"))
        self._flush()

        # should trigger despite last run cache because the updated interval is smaller
        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test_updated_smaller_interval", 1, False)])

    def test_automation_action_retry(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="automation_action_retry",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)
        i = 0

        def response_func():
            nonlocal i
            if i < 2:
                i = i + 1
                return (500, "Internal Server Error")
            return (200, "OK")

        WebhookHandler.set_response(response_func)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(
            WebhookHandler.webhook_summary(),
            [
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
            ],
        )
        self.assertEqual(
            WebhookHandler.webhook_responses,
            [
                (500, "Internal Server Error"),
                (500, "Internal Server Error"),
                (200, "OK"),
            ],
        )

        WebhookHandler.clear()
        WebhookHandler.set_response(lambda: (404, "Not Found"))

        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(
            WebhookHandler.webhook_summary(),
            [
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
            ],
        )
        self.assertEqual(
            WebhookHandler.webhook_responses,
            [
                (404, "Not Found"),
                (404, "Not Found"),
                (404, "Not Found"),
            ],
        )

    def test_test_automation(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="baz", scores=dict(Exactmatch=0.25))
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="foo", output="bar", expected="baz", metadata=dict(y=12))
        with braintrust.start_span(id="row3") as span3:
            span3.log(input="foo", output="bar", expected="baz")
        self._flush()

        unsaved_automation = dict(
            name="test_automation_unsaved",
            description="",
            config=dict(
                event_type="logs",
                btql_filter="metadata.x = 'foo'",
                interval_seconds=60,
                action=dict(type="webhook", url=self.server_url),
            ),
        )

        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **unsaved_automation),
        ).json()
        self.assertEqual(result["kind"], "success")
        payload = result["payload"]
        self.assertEqual(payload["organization"]["id"], self.org_id)
        self.assertEqual(payload["project"]["id"], logger.project.id)
        self.assertEqual(
            payload["automation"],
            dict(
                id=None,
                name=unsaved_automation["name"],
                description=unsaved_automation["description"],
                event_type=unsaved_automation["config"]["event_type"],
                btql_filter=unsaved_automation["config"]["btql_filter"],
                interval_seconds=unsaved_automation["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/alerts?aid=new",
            ),
        )
        self.assertEqual(payload["details"]["is_test"], True)
        self.assertEqual(
            payload["details"]["message"],
            f"{unsaved_automation['name']}: 1 logs triggered alert in the past 1 minute",
        )
        self.assertEqual(payload["details"]["count"], 1)
        self.assertIsNotNone(payload["details"]["time_start"])
        self.assertIsNotNone(payload["details"]["time_end"])
        self.assertIsNotNone(payload["details"]["related_logs_url"])
        self.assertEqual(WebhookHandler.webhook_summary(), [(unsaved_automation["name"], 1, True)])

        WebhookHandler.clear()
        unsaved_automation["config"]["btql_filter"] = "metadata.x = 'quux'"
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **unsaved_automation),
        ).json()
        self.assertEqual(
            result,
            {
                "kind": "error",
                "message": "No matching rows found for the provided BTQL filter and interval. Adjust the filter or interval or log rows to test again.",
            },
        )
        self.assertEqual(WebhookHandler.webhook_summary(), [])

        saved_automation = dict(**unsaved_automation)
        saved_automation["name"] = "test_automation_saved"
        automation_ids = self._set_automations(project_id, [saved_automation])
        saved_automation["id"] = automation_ids[0]
        saved_automation["config"]["btql_filter"] = "metadata.x = 'foo'"

        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **saved_automation),
        ).json()
        self.assertEqual(result["kind"], "success")
        self.assertEqual(
            result["payload"]["automation"],
            dict(
                id=automation_ids[0],
                name=saved_automation["name"],
                description=saved_automation["description"],
                event_type=saved_automation["config"]["event_type"],
                btql_filter=saved_automation["config"]["btql_filter"],
                interval_seconds=saved_automation["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/alerts?aid={automation_ids[0]}",
            ),
        )
        self.assertEqual(result["payload"]["details"]["is_test"], True)
        self.assertEqual(
            result["payload"]["details"]["message"],
            f"{saved_automation['name']}: 1 logs triggered alert in the past 1 minute",
        )
        self.assertEqual(result["payload"]["details"]["count"], 1)
        self.assertIsNotNone(result["payload"]["details"]["time_start"])
        self.assertIsNotNone(result["payload"]["details"]["time_end"])
        self.assertEqual(WebhookHandler.webhook_summary(), [(saved_automation["name"], 1, True)])

        WebhookHandler.clear()
        saved_automation["config"]["btql_filter"] = "metadata.x = 'quux'"
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **saved_automation),
        ).json()
        self.assertEqual(
            result,
            {
                "kind": "error",
                "message": "No matching rows found for the provided BTQL filter and interval. Adjust the filter or interval or log rows to test again.",
            },
        )
        self.assertEqual(WebhookHandler.webhook_summary(), [])

        saved_automation["config"]["btql_filter"] = ""
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **saved_automation),
        ).json()
        self.assertEqual(result["kind"], "success")
        self.assertEqual(WebhookHandler.webhook_summary(), [(saved_automation["name"], 4, True)])


class RetentionTest(AutomationTestBase):
    def setUp(self):
        super().setUp()
        self.sysadmin_api_key = self.ensureSysadminApiKeyExists()
        self.global_xact_id = 0

    def get_retention_policies(self, objects):
        return self.run_request(
            "post",
            f"{LOCAL_API_URL}/retention/fetch-policies",
            json=dict(objects=objects),
        ).json()

    def get_xact_id_from_datetime(self, dt: datetime):
        # adapted from api-ts/src/xact_id.ts to generate time-traveling xact_ids
        ts = int(dt.timestamp())
        counter = self.global_xact_id & 0xFFFF  # mask to 16 bits
        result = str((0x0DE1 << 48) | ((ts & 0xFFFFFFFFFFFF) << 16) | counter)
        self.global_xact_id += 1
        return result

    def test_retention_policy_validation(self):
        logger = braintrust.init_logger("logs")
        project_id = logger.project.id
        # Test basic retention policy creation works
        automations = [
            dict(
                name="test_retention",
                description="test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=90,
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        # Test duplicate name returns existing automation
        resp = self._create_automation(
            project_id,
            dict(
                name="test_retention",
                description="test retention policy duplicate",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=180,
                ),
            ),
        ).json()
        assert resp["found_existing"] is True
        assert resp["project_automation"]["name"] == "test_retention"
        assert resp["project_automation"]["description"] == "test retention policy"
        assert resp["project_automation"]["config"]["event_type"] == "retention"
        assert resp["project_automation"]["config"]["object_type"] == "project_logs"
        assert resp["project_automation"]["config"]["retention_days"] == 90

        # Test duplicate policy on same object fails
        with self.assertRaises(Exception) as cm:
            self._create_automation(
                project_id,
                dict(
                    name="test_retention_2",
                    description="test retention policy 2",
                    config=dict(
                        event_type="retention",
                        object_type="project_logs",
                        retention_days=365,
                    ),
                ),
            )
        assert "Project automation test_retention already exists with this retention configuration" in str(
            cm.exception
        )

        dataset = self.registerDataset(dict(project_id=project_id))["dataset"]
        self._create_automation(
            project_id,
            dict(
                name="test_retention_dataset_wildcard",
                description="test retention dataset policy wildcard",
                config=dict(
                    event_type="retention",
                    object_type="dataset",
                    retention_days=90,
                ),
            ),
        )

        # test fails if user does not have project update permissions
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        with self.assertRaises(Exception) as cm:
            self._create_automation(
                project_id,
                dict(
                    name="test_retention_dataset_wildcard2",
                    description="test retention dataset policy wildcard",
                    config=dict(event_type="retention", object_type="dataset", retention_days=90),
                ),
                api_key=user_api_key,
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project does not exist", str(cm.exception))

        with self.assertRaises(Exception) as cm:
            self._update_automation(
                project_id,
                dict(
                    id=automation_ids[0],
                    name="test_retention_dataset_wildcard3",
                    description="test retention dataset policy wildcard",
                    config=dict(event_type="retention", object_type="dataset", retention_days=90),
                ),
                api_key=user_api_key,
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project_automation does not exist", str(cm.exception))

    def test_retention_policy_acl(self):
        logger1 = braintrust.init_logger("logs")
        project_id1 = logger1.project.id

        # nonexistent project id
        with self.assertRaises(Exception) as cm:
            self._set_automations(
                str(uuid4()),
                [
                    dict(
                        name="test_retention_basic",
                        description="test retention policy basic",
                        config=dict(event_type="retention", object_type="project_logs", retention_days=180),
                    )
                ],
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project does not exist", str(cm.exception))

        # project id outside of org
        org_id2, org_name2 = self.createOrg()
        user2_id, _, user2_api_key = self.createUserInOrg(org_id2)
        braintrust.login(app_url=LOCAL_APP_URL, api_key=user2_api_key, org_name=org_name2, force_login=True)
        logger2 = braintrust.init_logger("logs", org_name=org_name2)
        project_id2 = logger2.project.id

        with self.assertRaises(Exception) as cm:
            self._set_automations(
                project_id2,
                [
                    dict(
                        name="test_retention_basic",
                        description="test retention policy basic",
                        config=dict(event_type="retention", object_type="project_logs", retention_days=180),
                    )
                ],
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project does not exist", str(cm.exception))

    def test_retention_policy_resolution(self):
        logger = braintrust.init_logger("logs")
        project_id = logger.project.id

        dataset = self.registerDataset(dict(project_id=logger.project.id))["dataset"]
        experiment = self.registerExperiment(dict(project_id=logger.project.id))["experiment"]
        automations = [
            dict(
                name="basic",
                description="a basic retention policy on logs",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=180,
                ),
            )
        ]
        automation_ids = self._set_automations(logger.project.id, automations)

        # no policy for other object types
        objects = [
            {"object_type": "experiment", "object_id": experiment["id"]},
            {"object_type": "dataset", "object_id": dataset["id"]},
        ]
        result = self.get_retention_policies(objects)
        assert len(result) == 0

        # Test that policy doesn't resolve for different project_id
        another_project = self.registerProject(dict(org_id=self.org_id, project_name="another_project"))["project"]
        objects = [{"object_type": "project_logs", "object_id": another_project["id"]}]
        result = self.get_retention_policies(objects)
        assert len(result) == 0

        # Test that policy resolves for matching project
        objects = [{"object_type": "project_logs", "object_id": project_id}]
        result = self.get_retention_policies(objects)
        assert result == {
            f"{o['object_type']}:{o['object_id']}": {"automation_id": automation_ids[0], "retention_days": 180}
            for o in objects
        }

        # delete policy and verify results are empty again
        self._set_automations(project_id, [])
        objects = [{"object_type": "project_logs", "object_id": project_id}]
        result = self.get_retention_policies(objects)
        assert len(result) == 0

    def test_retention_basic(self):
        foo_project_name = "foo"
        foo_logger = braintrust.init_logger(foo_project_name)
        foo_project_id = foo_logger.project.id

        bar_project_name = "bar"
        bar_logger = braintrust.init_logger(bar_project_name)
        bar_project_id = bar_logger.project.id

        project_name_to_id = {
            foo_project_name: foo_project_id,
            bar_project_name: bar_project_id,
        }

        week_ago_xact_id = self.get_xact_id_from_datetime(datetime.now() - timedelta(days=8))
        month_ago_xact_id = self.get_xact_id_from_datetime(datetime.now() - timedelta(days=31))

        for project_name, project_id in project_name_to_id.items():
            object_id_dict = {"log_id": "g"}
            log3_raw(
                dict(
                    project_id=project_id,
                    id="row0",
                    input=f"month_ago_{project_name}",
                    _bt_internal_override_xact_id=month_ago_xact_id,
                    **object_id_dict,
                )
            )
            log3_raw(
                dict(
                    project_id=project_id,
                    id="row1",
                    input=f"week_ago_{project_name}",
                    _bt_internal_override_xact_id=week_ago_xact_id,
                    **object_id_dict,
                )
            )
            log3_raw(dict(project_id=project_id, id="row2", input=f"now_{project_name}", **object_id_dict))
            self._flush()

        self.catchupBrainstore()

        def get_project_name_to_rows():
            project_name_to_rows = {}
            for project_name, project_id in project_name_to_id.items():
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/btql",
                    json=dict(
                        query=f"select: id, _xact_id, created, input | from: project_logs('{project_id}') spans",
                    ),
                ).json()
                project_name_to_rows[project_name] = resp["data"]
            return project_name_to_rows

        # We haven't inserted any retention policies yet, so neither project should be affected.
        initial_rows = get_project_name_to_rows()
        for project_name, rows in initial_rows.items():
            self.assertEqual(len(rows), 3)
            self.assertEqual(rows[0]["input"], f"now_{project_name}")
            self.assertEqual(rows[1]["input"], f"week_ago_{project_name}")
            self.assertEqual(rows[2]["input"], f"month_ago_{project_name}")

        # Insert a 1-day retention policy for the "foo" project that applies to experiments.
        # Since all of the rows we inserted are for project_logs, they should be untouched.
        automations = [
            dict(
                name="test_retention_basic",
                description="retention policy for foo",
                config=dict(
                    event_type="retention",
                    object_type="experiment",
                    retention_days=1,
                ),
            )
        ]
        automation_ids = self._set_automations(foo_project_id, automations)
        assert len(automation_ids) == 1

        # Run retention on project_logs:foo. This should have no effect since policy is for
        # experiments, not project_logs.
        response1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/brainstore/time_based_retention",
            json={"object_ids": [f"project_logs:{foo_project_id}"]},
            headers={"Authorization": f"Bearer {self.sysadmin_api_key}"},
            expect_error=False,
            allow_500_errors=False,
        )

        stats1 = response1.json()
        self.assertEqual(stats1["success"], True)
        self.assertEqual(stats1["num_processed_objects"], 0)
        self.assertEqual(stats1["num_processed_segments"], 0)
        self.assertIsNone(stats1["error"])

        # Vacuum segment WAL after no-op retention operation - should find zero entries to purge.
        vacuum_response_noop = vacuum_segment_wal_for_testing([f"project_logs:{foo_project_id}"])
        self.assertTrue(vacuum_response_noop["success"])
        self.assertEqual(
            vacuum_response_noop["num_purged_entries"],
            0,
            "Should have purged zero WAL entries when retention deleted nothing",
        )
        self.assertEqual(
            vacuum_response_noop["num_deleted_files"],
            0,
            "Should have deleted zero files when retention deleted nothing",
        )

        same_rows = get_project_name_to_rows()
        for project_name, rows in same_rows.items():
            self.assertEqual(len(rows), 3)
            self.assertEqual(rows[0]["input"], f"now_{project_name}")
            self.assertEqual(rows[1]["input"], f"week_ago_{project_name}")
            self.assertEqual(rows[2]["input"], f"month_ago_{project_name}")

        # Now insert a 30-day retention policy for foo. Only the first row should be purged.
        automations = [
            dict(
                name="test_retention_basic",
                description="retention policy for foo",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            )
        ]
        automation_ids = self._set_automations(foo_project_id, automations)
        assert len(automation_ids) == 1

        # Run retention on project_logs:foo with a 30-day retention policy.
        response2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/brainstore/time_based_retention",
            json={"object_ids": [f"project_logs:{foo_project_id}"]},
            headers={"Authorization": f"Bearer {self.sysadmin_api_key}"},
            expect_error=False,
            allow_500_errors=False,
        )

        # Should have processed 1 object and at least 1 segment.
        stats2 = response2.json()
        self.assertEqual(stats2["success"], True)
        self.assertEqual(stats2["num_processed_objects"], 1)
        self.assertGreaterEqual(stats2["num_processed_segments"], 1)
        self.assertIsNone(stats2["error"])
        # Should have some deletions due to month-ago data being purged.
        self.assertGreaterEqual(stats2["num_deleted_wal_entries"], 1)

        # Segment WAL vacuum should purge some WAL entries.
        vacuum_response_after_30day = vacuum_segment_wal_for_testing([f"project_logs:{foo_project_id}"])
        self.assertTrue(vacuum_response_after_30day["success"])
        self.assertGreater(
            vacuum_response_after_30day["num_purged_entries"],
            0,
            "Should have purged some WAL entries after retention deleted month-old data",
        )

        # Verify that the 30-day retention policy has been applied to foo.
        foo_rows = get_project_name_to_rows()["foo"]
        self.assertEqual(len(foo_rows), 2)
        self.assertEqual(foo_rows[0]["input"], f"now_foo")
        self.assertEqual(foo_rows[1]["input"], f"week_ago_foo")

        # Verify that the 30-day retention policy has not been applied to bar.
        bar_rows = get_project_name_to_rows()["bar"]
        self.assertEqual(len(bar_rows), 3)
        self.assertEqual(bar_rows[0]["input"], f"now_bar")
        self.assertEqual(bar_rows[1]["input"], f"week_ago_bar")
        self.assertEqual(bar_rows[2]["input"], f"month_ago_bar")

        # Now update foo's retention policy to a 7-day window.
        automations = [
            dict(
                name="test_retention_basic",
                description="retention policy for foo",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=7,
                ),
            )
        ]
        automation_ids = self._set_automations(foo_project_id, automations)
        assert len(automation_ids) == 1

        # Run retention on project_logs:foo with a 7-day retention policy.
        sysadmin_api_key = self.ensureSysadminApiKeyExists()
        response3 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/brainstore/time_based_retention",
            json={"object_ids": [f"project_logs:{foo_project_id}"]},
            headers={"Authorization": f"Bearer {sysadmin_api_key}"},
            expect_error=False,
            allow_500_errors=False,
        )

        # Should have processed only 1 object.
        stats3 = response3.json()
        self.assertEqual(stats3["success"], True)
        self.assertIsNone(stats3["error"])
        self.assertEqual(stats3["num_processed_objects"], 1)
        self.assertGreaterEqual(stats3["num_processed_segments"], 1)
        self.assertGreaterEqual(stats3["num_deleted_wal_entries"], 1)

        # Vacuum the segment WAL for `foo`. This should purge the WAL entries retention just deleted.
        vacuum_response_after_7day = vacuum_segment_wal_for_testing([f"project_logs:{foo_project_id}"])
        self.assertTrue(vacuum_response_after_7day["success"])
        self.assertGreater(
            vacuum_response_after_7day["num_purged_entries"],
            0,
            "Should have purged some WAL entries after retention deleted week-old data",
        )

        # Verify that the 7-day retention policy has been applied to foo.
        foo_rows = get_project_name_to_rows()["foo"]
        self.assertEqual(len(foo_rows), 1)
        self.assertEqual(foo_rows[0]["input"], f"now_foo")

        # Vacuum the segment WAL for `bar`. This should do nothing since we didn't run retention on it.
        vacuum_response_bar = vacuum_segment_wal_for_testing([f"project_logs:{bar_project_id}"])
        self.assertTrue(vacuum_response_bar["success"])
        self.assertEqual(
            vacuum_response_bar["num_purged_entries"],
            0,
            "Should have purged zero WAL entries for project_logs:bar (did not run retention on it)",
        )
        self.assertEqual(
            vacuum_response_bar["num_deleted_files"],
            0,
            "Should have deleted zero files for project_logs:bar (did not run retention on it)",
        )

        # Verify that the 7-day retention policy has not been applied to bar.
        bar_rows = get_project_name_to_rows()["bar"]
        self.assertEqual(len(bar_rows), 3)
        self.assertEqual(bar_rows[0]["input"], f"now_bar")
        self.assertEqual(bar_rows[1]["input"], f"week_ago_bar")
        self.assertEqual(bar_rows[2]["input"], f"month_ago_bar")

    def test_retention_object_types(self):
        year_ago_xact_id = self.get_xact_id_from_datetime(datetime.now() - timedelta(days=365, seconds=1))
        month_ago_xact_id = self.get_xact_id_from_datetime(datetime.now() - timedelta(days=30, seconds=1))
        week_ago_xact_id = self.get_xact_id_from_datetime(datetime.now() - timedelta(days=7, seconds=1))

        object_type_to_id = {}
        for object_type in ["experiment", "dataset", "project_logs"]:
            if object_type == "experiment":
                obj = braintrust.init(project="p")
                object_id = obj.id
                project_id = obj.project_id
            elif object_type == "dataset":
                obj = braintrust.init_dataset(project="p")
                object_id = obj.id
                project_id = obj.project_id
            elif object_type == "project_logs":
                obj = braintrust.init_logger(project="p")
                object_id = obj.id
                project_id = obj.id
            else:
                raise ValueError(f"Unexpected object type: {object_type}")

            object_type_to_id[object_type] = object_id
            object_id_dict = {"log_id": "g"} if object_type == "project_logs" else {f"{object_type}_id": object_id}

            log3_raw(
                dict(
                    project_id=project_id,
                    id="row0",
                    input=f"year_ago_{object_type}",
                    _bt_internal_override_xact_id=year_ago_xact_id,
                    **object_id_dict,
                )
            )
            log3_raw(
                dict(
                    project_id=project_id,
                    id="row1",
                    input=f"month_ago_{object_type}",
                    _bt_internal_override_xact_id=month_ago_xact_id,
                    **object_id_dict,
                )
            )
            log3_raw(
                dict(
                    project_id=project_id,
                    id="row2",
                    input=f"week_ago_{object_type}",
                    _bt_internal_override_xact_id=week_ago_xact_id,
                    **object_id_dict,
                )
            )
            log3_raw(
                dict(
                    project_id=project_id,
                    id="row3",
                    input=f"now_{object_type}",
                    **object_id_dict,
                )
            )
            self._flush()

        project_id = object_type_to_id["project_logs"]
        self.catchupBrainstore()

        def get_object_type_rows(object_type):
            object_id = object_type_to_id[object_type]
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json=dict(
                    query=f"select: id, _xact_id, created, input | from: {object_type}('{object_id}') spans",
                ),
            ).json()
            return resp["data"]

        # We haven't inserted any retention policies yet, so all rows should be present.
        for object_type in ["experiment", "dataset", "project_logs"]:
            rows = get_object_type_rows(object_type)
            self.assertEqual(len(rows), 4)
            self.assertEqual(rows[0]["input"], f"now_{object_type}")
            self.assertEqual(rows[1]["input"], f"week_ago_{object_type}")
            self.assertEqual(rows[2]["input"], f"month_ago_{object_type}")
            self.assertEqual(rows[3]["input"], f"year_ago_{object_type}")

        object_type_to_retention_days = {
            "project_logs": 365,
            "experiment": 7,
            "dataset": 30,
        }

        for object_type in ["project_logs", "experiment", "dataset"]:
            object_id = object_type_to_id[object_type]

            rows = get_object_type_rows(object_type)
            self.assertEqual(len(rows), 4)
            self.assertEqual(rows[0]["input"], f"now_{object_type}")
            self.assertEqual(rows[1]["input"], f"week_ago_{object_type}")
            self.assertEqual(rows[2]["input"], f"month_ago_{object_type}")
            self.assertEqual(rows[3]["input"], f"year_ago_{object_type}")

            # The object hasn't been deleted, so setting the deleted object grace period to zero
            # shouldn't have any effect.
            resp = self.run_request(
                "post",
                f"{LOCAL_BRAINSTORE_URL}/time_based_retention",
                json=dict(
                    object_ids=[f"{object_type}:{object_id}"],
                    testing_deleted_object_grace_period_days=0,
                ),
            ).json()
            self.assertEqual(resp["success"], True)
            self.assertEqual(resp["num_processed_objects"], 0, f"Should have processed 0 objects for {object_type}")
            self.assertEqual(resp["num_processed_segments"], 0)
            rows = get_object_type_rows(object_type)
            self.assertEqual(len(rows), 4)
            self.assertEqual(rows[0]["input"], f"now_{object_type}")
            self.assertEqual(rows[1]["input"], f"week_ago_{object_type}")
            self.assertEqual(rows[2]["input"], f"month_ago_{object_type}")
            self.assertEqual(rows[3]["input"], f"year_ago_{object_type}")

            # Insert a retention policy for this object.
            retention_days = object_type_to_retention_days[object_type]
            automations = [
                dict(
                    name=f"test_retention_{object_type}",
                    description=f"{retention_days}-day retention",
                    config=dict(
                        event_type="retention",
                        object_type=object_type,
                        retention_days=retention_days,
                    ),
                )
            ]
            automation_ids = self._set_automations(project_id, automations)
            assert len(automation_ids) == 1

            if object_type == "experiment":
                # For experiments, running retention on a live object shouldn't delete anything.
                response_exp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/brainstore/time_based_retention",
                    json={"object_ids": [f"{object_type}:{object_id}"]},
                    headers={"Authorization": f"Bearer {self.sysadmin_api_key}"},
                    expect_error=False,
                    allow_500_errors=False,
                )
                stats = response_exp.json()
                self.assertEqual(stats["success"], True)
                self.assertEqual(stats["num_processed_objects"], 0)
                self.assertEqual(stats["num_processed_segments"], 0)
                self.assertIsNone(stats["error"])
                rows = get_object_type_rows(object_type)
                self.assertEqual(len(rows), 4)
                self.assertEqual(rows[0]["input"], f"now_{object_type}")
                self.assertEqual(rows[1]["input"], f"week_ago_{object_type}")
                self.assertEqual(rows[2]["input"], f"month_ago_{object_type}")
                self.assertEqual(rows[3]["input"], f"year_ago_{object_type}")

                # Now delete the experiment from the control plane so future retention runs are free to
                # purge the rows from the data plane.
                self.run_request(
                    "delete",
                    f"{LOCAL_API_URL}/v1/{object_type}/{object_id}",
                    headers={"Authorization": f"Bearer {self.org_api_key}"},
                )

            # Run retention on the object. This should delete rows up to the retention cutoff.
            response_exp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/time_based_retention",
                json={"object_ids": [f"{object_type}:{object_id}"]},
                headers={"Authorization": f"Bearer {self.sysadmin_api_key}"},
            )
            stats = response_exp.json()
            self.assertEqual(stats["success"], True)
            self.assertEqual(stats["num_processed_objects"], 1)
            self.assertEqual(stats["num_processed_segments"], 1)
            self.assertIsNone(stats["error"])

            rows = get_object_type_rows(object_type)
            if object_type == "project_logs":
                self.assertEqual(len(rows), 3)
                self.assertEqual(rows[0]["input"], f"now_{object_type}")
                self.assertEqual(rows[1]["input"], f"week_ago_{object_type}")
                self.assertEqual(rows[2]["input"], f"month_ago_{object_type}")
            elif object_type == "experiment":
                self.assertEqual(len(rows), 1)
                self.assertEqual(rows[0]["input"], f"now_{object_type}")
            elif object_type == "dataset":
                self.assertEqual(len(rows), 2)
                self.assertEqual(rows[0]["input"], f"now_{object_type}")
                self.assertEqual(rows[1]["input"], f"week_ago_{object_type}")
            else:
                raise ValueError(f"Unexpected object type: {object_type}")

            if object_type == "project_logs":
                continue
            # Now delete the object and run retention with a zero deleted object grace period.
            # All of the underlying data should get wiped. (The experiment was already deleted
            # earlier so we don't have to delete it again.)
            if object_type != "experiment":
                object_type_str = "project" if object_type == "project_logs" else object_type
                self.run_request("delete", f"{LOCAL_API_URL}/v1/{object_type_str}/{object_id}")

            resp = self.run_request(
                "post",
                f"{LOCAL_BRAINSTORE_URL}/time_based_retention",
                json=dict(
                    object_ids=[f"{object_type}:{object_id}"],
                    testing_deleted_object_grace_period_days=0,
                ),
            ).json()
            self.assertEqual(resp["success"], True)
            self.assertEqual(resp["num_processed_objects"], 1, f"Should have processed 1 object for {object_type}")
            self.assertEqual(resp["num_processed_segments"], 1)
            rows = get_object_type_rows(object_type)
            self.assertEqual(len(rows), 0, f"Should have 0 rows for {object_type}")

            # Un-delete the project so its children aren't affected by the delete in subsequent loop iterations.
            if object_type == "project_logs":
                with self.connect_app_db() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("UPDATE projects SET deleted_at = NULL WHERE id = %s", [project_id])


class RetentionCronTest(RetentionTest):
    def run_cron_endpoint(self, **kwargs):
        """Helper to call the retention cron endpoint"""
        endpoint = f"{LOCAL_APP_URL}/api/retention/metadata/cron"
        headers = kwargs.pop("headers", {})

        # Determine method: GET for Vercel cron, POST for sysadmin with params
        is_vercel_cron = headers.get("user-agent") == "vercel-cron/1.0"
        has_params = any(key not in ["headers"] for key in kwargs.keys())

        if is_vercel_cron:
            # Vercel cron uses GET and should have no params
            if has_params:
                raise ValueError("Vercel cron requests cannot have parameters")
            return self.run_request("get", endpoint, headers=headers)
        else:
            # Sysadmin requests use POST if they have params, GET if no params
            method = "post" if has_params else "get"

            # Default to using sysadmin token for non-Vercel requests
            if "Authorization" not in headers:
                sysadmin_key = self.ensureSysadminApiKeyExists()
                headers["Authorization"] = f"Bearer {sysadmin_key}"

            if method == "post":
                # Use POST with JSON body for parameters
                return self.run_request("post", endpoint, headers=headers, json=kwargs)
            else:
                # Use GET with no parameters
                return self.run_request("get", endpoint, headers=headers)

    def create_old_experiment(self, project_id, days_ago):
        """Create an experiment and then backdate it"""
        # Use braintrust SDK to create experiment with unique name
        import uuid
        from datetime import datetime, timedelta

        experiment = braintrust.init(
            project_id=project_id, experiment=f"exp_{days_ago}_days_old_{str(uuid.uuid4())[:8]}"
        )

        # Add some sample data to make the test more realistic
        experiment.log(input="What is 2+2?", output="4", scores={"accuracy": 1.0})
        experiment.log(input="What is the capital of France?", output="Paris", scores={"accuracy": 1.0})
        experiment.log(input="What is 3*7?", output="21", scores={"accuracy": 1.0})

        # Then backdate it by updating the database directly
        old_date = datetime.now() - timedelta(days=days_ago)
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("UPDATE experiments SET created = %s WHERE id = %s", [old_date, experiment.id])

        return experiment

    def test_retention_cron_auth(self):
        endpoint = f"{LOCAL_APP_URL}/api/retention/metadata/cron"

        # Test 1: Vercel cron with correct secret (GET method) - should work
        response = self.run_request(
            "get", endpoint, headers={"user-agent": "vercel-cron/1.0", "authorization": f"Bearer {TEST_CRON_SECRET}"}
        )
        self.assertEqual(response.status_code, 200)

        # Test 2: Vercel cron with wrong secret - should fail (NO FALLBACK)
        response = self.run_request(
            "get",
            endpoint,
            headers={"user-agent": "vercel-cron/1.0", "authorization": "Bearer wrong-secret"},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)

        # Test 3: Vercel cron with params - should fail
        response = self.run_request(
            "get",
            endpoint,
            params={"dry_run": "true"},
            headers={"user-agent": "vercel-cron/1.0", "authorization": f"Bearer {TEST_CRON_SECRET}"},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)

        # Test 4: GET method without Vercel user-agent - should fail
        response = self.run_request("get", endpoint, expect_error=True)
        self.assertEqual(response.status_code, 401)

        # Test 5: Sysadmin POST with params - should work
        response = self.run_cron_endpoint(dry_run=True)
        self.assertEqual(response.status_code, 200)

        # Test 6: Sysadmin POST with no params - should work
        sysadmin_key = self.ensureSysadminApiKeyExists()
        response = self.run_request("post", endpoint, headers={"Authorization": f"Bearer {sysadmin_key}"}, json={})
        self.assertEqual(response.status_code, 200)

        # Test 7: Regular user POST - should fail
        response = self.run_request("post", endpoint, json={"dry_run": True}, expect_error=True)
        self.assertEqual(response.status_code, 403)

    def test_retention_cron_dry_run(self):
        logger = braintrust.init_logger("retention-test")
        project_id = logger.project.id

        # Set retention period for experiments
        automations = [
            {
                "name": "exp-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 30},
            },
        ]
        self._set_automations(project_id, automations)

        # Create test objects with backdated timestamps
        self.create_old_experiment(project_id, days_ago=45)  # Should delete
        self.create_old_experiment(project_id, days_ago=20)  # Should NOT delete

        # Run dry_run
        response = self.run_cron_endpoint(dry_run=True)
        result = response.json()

        self.assertEqual(result["num_processed_experiments"], 1)
        self.assertEqual(result["num_deleted_experiments"], 0)  # dry run
        self.assertIsNone(result["deleted_at"])

    def test_retention_cron_deletion(self):
        logger = braintrust.init_logger("retention-delete-test")
        project_id = logger.project.id

        # Create retention policy
        automations = [
            {
                "name": "exp-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 7},
            },
        ]
        self._set_automations(project_id, automations)

        # Create old experiment
        old_exp = self.create_old_experiment(project_id, days_ago=14)

        # Run with dry_run=false
        response = self.run_cron_endpoint(dry_run=False)
        result = response.json()

        self.assertEqual(result["num_deleted_experiments"], 1)
        self.assertIsNotNone(result["deleted_at"])

        # Verify objects are actually soft-deleted by checking database directly
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT deleted_at FROM experiments WHERE id = %s", [old_exp.id])
                exp_row = cursor.fetchone()

                self.assertIsNotNone(exp_row[0])

                # Verify they have the same deletion timestamp (both should be UTC)
                exp_utc = exp_row[0].strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
                self.assertEqual(exp_utc, result["deleted_at"])

    def test_retention_cron_batch_size(self):
        logger = braintrust.init_logger("batch-test")
        project_id = logger.project.id

        # Create retention policy
        automations = [
            {
                "name": "experiment-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 7},
            }
        ]
        self._set_automations(project_id, automations)

        # Create 5 old experiments
        for i in range(5):
            self.create_old_experiment(project_id, days_ago=14)

        # Verify all objects were created
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT COUNT(*) FROM experiments WHERE project_id = %s AND deleted_at IS NULL", [project_id]
                )
                exp_count = cursor.fetchone()[0]

                self.assertEqual(exp_count, 5, f"Expected 5 experiments, found {exp_count}")

        # Test batch_size=3 (should delete 3 experiments)
        response = self.run_cron_endpoint(dry_run=False, batch_size=3)
        result = response.json()

        self.assertEqual(result["num_deleted_experiments"], 3)

        # Run again to delete remaining
        response = self.run_cron_endpoint(dry_run=False)
        result = response.json()

        self.assertEqual(result["num_deleted_experiments"], 2)

    def test_retention_cron_no_policy(self):
        """Test that objects in projects without retention policies are not deleted"""
        project = self.registerProject({"project_name": "no-policy-test"})["project"]

        # Create old object but NO retention policy
        self.create_old_experiment(project["id"], days_ago=365)

        # Run retention
        response = self.run_cron_endpoint(dry_run=True)
        result = response.json()

        # Nothing should be processed since no retention policies exist
        self.assertEqual(result["num_processed_experiments"], 0)

    def test_retention_cron_boundary_conditions(self):
        """Test edge cases around retention boundaries"""
        project = self.registerProject({"project_name": "boundary-test"})["project"]

        automations = [
            {
                "name": "exp-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 30},
            }
        ]
        self._set_automations(project["id"], automations)

        # Create experiments at various ages
        self.create_old_experiment(project["id"], days_ago=29)  # Should NOT delete (within retention)
        self.create_old_experiment(project["id"], days_ago=31)  # Should delete (past retention)

        # Test with already deleted object
        old_exp = self.create_old_experiment(project["id"], days_ago=35)
        # Manually soft-delete it
        self.run_request("delete", f"{LOCAL_API_URL}/v1/experiment/{old_exp.id}")

        response = self.run_cron_endpoint(dry_run=True)
        result = response.json()

        # Should only find 1 experiment to delete (not the already deleted one)
        self.assertEqual(result["num_processed_experiments"], 1)

    def test_retention_cron_multiple_orgs(self):
        """Test that multiple orgs with different policies are isolated correctly"""
        # Create a second org and user
        org2_id, org2_name = self.createOrg()
        user2_id, user2_auth_id, org2_api_key = self.createUserInOrg(org2_id)

        # Login as user2 and create project in org2
        braintrust.login(org_name=org2_name, app_url=LOCAL_APP_URL, api_key=org2_api_key, force_login=True)
        logger2 = braintrust.init_logger("org2-project")
        project2_id = logger2.project.id

        # Switch back to original org
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True)
        logger1 = braintrust.init_logger("org1-project")
        project1_id = logger1.project.id

        # Set up different retention policies for each org
        # Org1: 10 days retention for experiments
        automations1 = [
            {
                "name": "org1-exp-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 10},
            }
        ]
        self._set_automations(project1_id, automations1)

        # Org2: 30 days retention for experiments (login as org2 user first)
        braintrust.login(org_name=org2_name, app_url=LOCAL_APP_URL, api_key=org2_api_key, force_login=True)
        # Use org2 API key for automation creation
        old_api_key = self.org_api_key
        self.org_api_key = org2_api_key
        automations2 = [
            {
                "name": "org2-exp-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 30},
            }
        ]
        self._set_automations(project2_id, automations2)
        # Restore original API key
        self.org_api_key = old_api_key

        # Switch back to org1 for creating experiments
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True)

        # Create old experiments in both orgs
        # Org1: 15 days old (should be deleted - past 10 day retention)
        org1_exp = self.create_old_experiment(project1_id, days_ago=15)

        # Switch to org2 to create experiment
        braintrust.login(org_name=org2_name, app_url=LOCAL_APP_URL, api_key=org2_api_key, force_login=True)
        # Org2: 20 days old (should NOT be deleted - within 30 day retention)
        org2_exp = self.create_old_experiment(project2_id, days_ago=20)

        # Switch back to org1
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True)

        # Run retention WITHOUT org_id filter (should process both orgs)
        response = self.run_cron_endpoint(dry_run=False)
        result = response.json()

        # Should delete 1 experiment from org1
        self.assertEqual(result["num_deleted_experiments"], 1)

        # Verify org isolation by checking database directly
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                # Check org1 experiment is deleted
                cursor.execute("SELECT deleted_at FROM experiments WHERE id = %s", [org1_exp.id])
                org1_row = cursor.fetchone()
                self.assertIsNotNone(org1_row[0], "Org1 experiment should be deleted")

                # Check org2 experiment is NOT deleted
                cursor.execute("SELECT deleted_at FROM experiments WHERE id = %s", [org2_exp.id])
                org2_row = cursor.fetchone()
                self.assertIsNone(org2_row[0], "Org2 experiment should NOT be deleted")

    def test_retention_cron_org_id_filter(self):
        """Test that org_id filter only processes specified orgs"""
        # Create a second org and user
        org2_id, org2_name = self.createOrg()
        user2_id, user2_auth_id, org2_api_key = self.createUserInOrg(org2_id)

        # Login as user2 and create project in org2
        braintrust.login(org_name=org2_name, app_url=LOCAL_APP_URL, api_key=org2_api_key, force_login=True)
        logger2 = braintrust.init_logger("org2-filter-project")
        project2_id = logger2.project.id

        # Switch back to original org
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True)
        logger1 = braintrust.init_logger("org1-filter-project")
        project1_id = logger1.project.id

        # Set up same retention policies for both orgs (7 days)
        automations = [
            {
                "name": "exp-retention",
                "config": {"event_type": "retention", "object_type": "experiment", "retention_days": 7},
            }
        ]

        # Org1 automations
        self._set_automations(project1_id, automations)

        # Org2 automations (login as org2 user)
        braintrust.login(org_name=org2_name, app_url=LOCAL_APP_URL, api_key=org2_api_key, force_login=True)
        old_api_key = self.org_api_key
        self.org_api_key = org2_api_key
        self._set_automations(project2_id, automations)
        self.org_api_key = old_api_key

        # Switch back to org1
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True)

        # Create old experiments in both orgs (both 14 days old - past retention)
        org1_exp = self.create_old_experiment(project1_id, days_ago=14)

        # Switch to org2 to create experiment
        braintrust.login(org_name=org2_name, app_url=LOCAL_APP_URL, api_key=org2_api_key, force_login=True)
        org2_exp = self.create_old_experiment(project2_id, days_ago=14)

        # Switch back to org1
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True)

        # Run retention with org_id filter for ONLY org1
        response = self.run_cron_endpoint(dry_run=False, org_id=self.org_id)
        result = response.json()

        # Should delete 1 experiment (only from org1)
        self.assertEqual(result["num_deleted_experiments"], 1)

        # Verify filtering worked correctly
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                # Check org1 experiment is deleted
                cursor.execute("SELECT deleted_at FROM experiments WHERE id = %s", [org1_exp.id])
                org1_row = cursor.fetchone()
                self.assertIsNotNone(org1_row[0], "Org1 experiment should be deleted")

                # Check org2 experiment is NOT deleted (filtered out)
                cursor.execute("SELECT deleted_at FROM experiments WHERE id = %s", [org2_exp.id])
                org2_row = cursor.fetchone()
                self.assertIsNone(org2_row[0], "Org2 experiment should NOT be deleted (filtered out)")

        # Now run with org_id filter for ONLY org2
        response = self.run_cron_endpoint(dry_run=False, org_id=org2_id)
        result = response.json()

        # Should delete 1 experiment (only from org2 this time)
        self.assertEqual(result["num_deleted_experiments"], 1)

        # Verify org2 experiment is now deleted
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT deleted_at FROM experiments WHERE id = %s", [org2_exp.id])
                org2_row = cursor.fetchone()
                self.assertIsNotNone(org2_row[0], "Org2 experiment should now be deleted")
