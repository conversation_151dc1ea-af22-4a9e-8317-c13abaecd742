/**
 * Tests for Braintrust function invocation.
 */

import { describe, expect, test } from "vitest";
import { LOCAL_API_URL, LOCAL_APP_URL } from "./setup";

describe("structured output templating", () => {
  const promptData = {
    prompt: {
      type: "chat",
      messages: [
        {
          role: "system",
          content:
            "Please compute {{input.expression}}. Return the result in JSON.",
        },
      ],
    },
    options: {
      model: "gpt-4o",
      params: {
        response_format: {
          type: "json_schema",
          json_schema: {
            name: "schema",
            schema: "{{input.schema}}",
            strict: true,
          },
        },
      },
    },
  };
  const input = {
    expression: "2 + 3",
    schema: {
      type: "object",
      properties: {
        final_answer: {
          type: "string",
        },
      },
      required: ["final_answer"],
      additionalProperties: false,
    },
  };

  describe("invoke", () => {
    test("inline prompt", async ({ orgApiKey, proxyUrl }) => {
      const result = await fetch(`${proxyUrl}/function/invoke`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inline_prompt: promptData,
          input,
        }),
      }).then((r) => r.json());
      expect(result).toEqual({ final_answer: "5" });
    });

    test("project name and slug", async ({ orgApiKey, proxyUrl }) => {
      const project = await fetch(`${LOCAL_API_URL}/v1/project`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: "p" }),
      }).then((r) => r.json());
      const prompt = await fetch(`${LOCAL_API_URL}/v1/prompt`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_id: project.id,
          name: "Calculator",
          slug: "calculator",
          prompt_data: promptData,
        }),
      }).then((r) => r.json());

      const result = await fetch(`${proxyUrl}/function/invoke`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_name: project.name,
          slug: prompt.slug,
          input,
        }),
      }).then((r) => r.json());
      expect(result).toEqual({ final_answer: "5" });
    });

    test("prompt session id", async ({ orgName, orgApiKey, proxyUrl }) => {
      const promptSession = await fetch(
        `${LOCAL_APP_URL}/api/prompt_session/register`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${orgApiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            org_name: orgName,
            project_name: "p",
            session_name: "s",
          }),
        },
      ).then((r) => r.json());
      const logResp = await fetch(`${LOCAL_API_URL}/logs3`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          api_version: 2,
          rows: [
            {
              prompt_session_id: promptSession.id,
              prompt_data: promptData,
            },
          ],
        }),
      }).then((r) => r.json());
      const result = await fetch(`${proxyUrl}/function/invoke`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt_session_id: promptSession.id,
          prompt_session_function_id: logResp.ids[0],
          input,
        }),
      }).then((r) => r.json());
      expect(result).toEqual({ final_answer: "5" });
    });

    test("using SDK invoke with function_id", async ({
      orgApiKey,
      proxyUrl,
    }) => {
      // Import the SDK's invoke function
      const { invoke } = await import("braintrust");

      // Create a function in the database
      const project = await fetch(`${LOCAL_API_URL}/v1/project`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: "p_function_id" }),
      }).then((r) => r.json());

      const prompt = await fetch(`${LOCAL_API_URL}/v1/prompt`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_id: project.id,
          name: "CalculatorWithId",
          slug: "calculator-with-id",
          prompt_data: promptData,
        }),
      }).then((r) => r.json());

      // Use the SDK's invoke method with function_id
      const result = await invoke({
        function_id: prompt.id, // Use the prompt's ID as the function_id
        input,
        apiKey: orgApiKey,
        appUrl: LOCAL_APP_URL, // Use the local app URL for testing
      });

      expect(result).toEqual({ final_answer: "5" });
    });
  });

  describe("eval", () => {
    test("inline prompt", async ({ orgApiKey, proxyUrl }) => {
      const project = await fetch(`${LOCAL_API_URL}/v1/project`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: "p" }),
      }).then((r) => r.json());
      const result = await fetch(`${proxyUrl}/function/eval`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_id: project.id,
          data: {
            data: [
              {
                input,
                expected: { final_answer: "5" },
              },
            ],
          },
          task: {
            inline_prompt: promptData,
          },
          scores: [{ global_function: "ExactMatch" }],
        }),
      }).then((r) => r.json());
      expect(result.scores.ExactMatch.score).toEqual(1);
    });

    test("project name and slug", async ({ orgApiKey, proxyUrl }) => {
      const project = await fetch(`${LOCAL_API_URL}/v1/project`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: "p" }),
      }).then((r) => r.json());
      const prompt = await fetch(`${LOCAL_API_URL}/v1/prompt`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_id: project.id,
          name: "Calculator",
          slug: "calculator",
          prompt_data: promptData,
        }),
      }).then((r) => r.json());
      const result = await fetch(`${proxyUrl}/function/eval`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_id: project.id,
          data: {
            data: [
              {
                input,
                expected: { final_answer: "5" },
              },
            ],
          },
          task: {
            project_name: project.name,
            slug: prompt.slug,
          },
          scores: [{ global_function: "ExactMatch" }],
        }),
      }).then((r) => r.json());
      expect(result.scores.ExactMatch.score).toEqual(1);
    });

    test("prompt session id", async ({ orgName, orgApiKey, proxyUrl }) => {
      const project = await fetch(`${LOCAL_API_URL}/v1/project`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: "p" }),
      }).then((r) => r.json());
      const promptSession = await fetch(
        `${LOCAL_APP_URL}/api/prompt_session/register`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${orgApiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            org_name: orgName,
            project_name: project.name,
            session_name: "s",
          }),
        },
      ).then((r) => r.json());
      const logResp = await fetch(`${LOCAL_API_URL}/logs3`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          api_version: 2,
          rows: [
            {
              prompt_session_id: promptSession.id,
              prompt_data: promptData,
            },
          ],
        }),
      }).then((r) => r.json());
      const result = await fetch(`${proxyUrl}/function/eval`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${orgApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          project_id: project.id,
          data: {
            data: [
              {
                input,
                expected: { final_answer: "5" },
              },
            ],
          },
          task: {
            prompt_session_id: promptSession.id,
            prompt_session_function_id: logResp.ids[0],
          },
          scores: [{ global_function: "ExactMatch" }],
        }),
      }).then((r) => r.json());
      expect(result.scores.ExactMatch.score).toEqual(1);
    });
  });
});
