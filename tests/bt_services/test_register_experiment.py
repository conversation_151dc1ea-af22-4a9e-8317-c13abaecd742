import time
from concurrent.futures import Thr<PERSON>PoolExecutor

import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


def getProjectInfo(resp):
    return {k: resp["project"][k] for k in ["name", "user_id"]}


def getExperimentInfo(resp):
    return {
        k: resp["experiment"][k]
        for k in [
            "project_id",
            "name",
            "description",
            "repo_info",
            "base_exp_id",
            "dataset_id",
            "dataset_version",
            "user_id",
            "metadata",
        ]
    }


class RegisterExperimentTest(BraintrustAppTestBase):
    def test_basic(self):
        dataset_args = dict(
            project_name="_test_register_experiment",
            dataset_name="_test_basic",
        )
        dataset_resp = self.registerDataset(dataset_args)

        args = dict(
            project_name="_test_register_experiment",
            experiment_name="_test_basic",
            description="A basic experiment",
            repo_info=dict(
                commit="abc",
                branch="def",
            ),
            dataset_id=dataset_resp["dataset"]["id"],
            dataset_version="4",
            metadata=dict(
                foo="bar",
            ),
        )
        resp = self.registerExperiment(args)

        self.assertEqual(getProjectInfo(resp), dict(name=args["project_name"], user_id=self.user_id))

        self.assertEqual(
            getExperimentInfo(resp),
            dict(
                project_id=resp["project"]["id"],
                name=args["experiment_name"],
                description=args["description"],
                repo_info=args["repo_info"],
                base_exp_id=None,
                dataset_id=args["dataset_id"],
                dataset_version=args["dataset_version"],
                user_id=self.user_id,
                metadata=args["metadata"],
            ),
        )

        # If the experiment already exists, passing `update=True` will just
        # return the same one.
        args["description"] = "A different experiment"
        args["metadata"] = dict(foo="baz", size="small")
        args["update"] = True
        resp2 = self.registerExperiment(args)
        self.assertEqual(resp2, resp)

    def test_base_experiment_named(self):
        base_args = dict(
            project_name="_test_register_experiment",
            experiment_name="_base_experiment",
        )
        base_resp = self.registerExperiment(base_args)
        self.assertEqual(base_resp["experiment"]["name"], base_args["experiment_name"])

        args = base_args.copy()
        args["experiment_name"] = "_experiment"
        args["base_experiment"] = "_base_experiment"
        resp = self.registerExperiment(args)
        self.assertEqual(resp["experiment"]["base_exp_id"], base_resp["experiment"]["id"])

        # Cannot specify base experiment that doesn't exist.
        with self.assertRaises(Exception):
            args["base_experiment"] = "_nonexistent"
            self.registerExperiment(args)

    def test_base_experiment_commits(self):
        base_args = [
            dict(
                project_name="_test_register_experiment" if i < 2 else "_test_register_experiment2",
                experiment_name=f"_experiment_{i}",
                repo_info=dict(
                    commit="abc" if i % 2 == 0 else "def",
                ),
            )
            for i in range(4)
        ]

        base_responses = []
        for args in base_args:
            base_responses.append(self.registerExperiment(args))
            time.sleep(0.01)

        args = dict(
            project_name="_test_register_experiment",
            experiment_name="_test",
            ancestor_commits=["abc", "def"],
        )
        resp = self.registerExperiment(args)

        # We should pick the most recent base experiment on commit 'abc' in project '_test_register_experiment',
        # which should be base at index 0.
        self.assertEqual(resp["experiment"]["base_exp_id"], base_responses[0]["experiment"]["id"])

    def test_default_name_branch(self):
        args = dict(
            project_name="_test_register_experiment",
            repo_info=dict(
                branch="xyz",
            ),
        )
        resp = self.registerExperiment(args)
        self.assertRegex(resp["experiment"]["name"], "^xyz-.*$")

    def test_default_name_user_email(self):
        user_email = braintrust.user_info()["email"]
        args = dict(
            project_name="_test_register_experiment",
        )
        resp = self.registerExperiment(args)
        self.assertRegex(resp["experiment"]["name"], rf"^{user_email}-\d+$")

    def test_append_uuid_to_conflicting_name(self):
        self.registerExperiment(
            dict(
                project_name="_test_register_experiment",
                experiment_name="_exp",
            )
        )
        resp = self.registerExperiment(
            dict(
                project_name="_test_register_experiment",
                experiment_name="_exp",
            )
        )
        self.assertRegex(resp["experiment"]["name"], r"^_exp-\w+$")

    def test_concurrent_experiment_registration(self):
        NUM_EXPERIMENTS = 100
        experiment_names = ["e0", "e1", "e2", "e3", "e4", "e5"]
        with ThreadPoolExecutor(NUM_EXPERIMENTS) as executor:
            results = []
            for i in range(NUM_EXPERIMENTS):
                results.append(
                    executor.submit(
                        self.registerExperiment,
                        dict(
                            project_name="_concurrent_register_experiment",
                            experiment_name=experiment_names[i % len(experiment_names)],
                        ),
                    )
                )
            for r in results:
                r.result()

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*)
                    from experiments join projects on experiments.project_id = projects.id
                    where projects.org_id = %s
                    """,
                    [self.org_id],
                )
                actual_num_experiments = cursor.fetchone()[0]
        self.assertEqual(NUM_EXPERIMENTS, actual_num_experiments)
