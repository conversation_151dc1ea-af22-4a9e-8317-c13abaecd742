from uuid import uuid4

import braintrust
import requests

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class VersionTest(BraintrustAppTestBase):
    def test_version_noauth(self):
        # Make sure both version endpoints are available and return a commit hash, without
        # having to be authenticated.
        res = requests.get(f"{LOCAL_API_URL}/version")
        self.assertEqual(res.status_code, 200)
        self.assertIsNotNone(res.json()["commit"])

        res = requests.get(f"{LOCAL_API_URL}/brainstore/version")
        self.assertEqual(res.status_code, 200)
        self.assertIsNotNone(res.json()["commit"])
