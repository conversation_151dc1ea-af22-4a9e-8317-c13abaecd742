import braintrust
import requests
from braintrust_local.constants import ANON_USER_ID

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class PublicQueriesTest(BraintrustAppTestBase):
    def test_basic(self):
        other_org_id, _ = self.createOrg()
        _, __, other_org_api_key = self.createUserInOrg(other_org_id)

        experiment = braintrust.init("p", description="something cool")
        experiment.log(input="hello", output="world", scores={})
        experiment.flush()

        self.assertEqual(experiment.public, False)

        # We should not be able to read the experiment as an anonymous user,
        # neither the metadata nor the experiment data itself.
        with self.assertRaises(Exception):
            resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}")
            resp.raise_for_status()
        with self.assertRaises(Exception):
            resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}/fetch")
            resp.raise_for_status()

        # If we update the experiment to public, it should be accessible.
        resp = requests.patch(
            f"{LOCAL_API_URL}/v1/experiment/{experiment.id}",
            json=dict(public=True),
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, resp.text)
        resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}")
        self.assertTrue(resp.ok, resp.text)
        obj = resp.json()
        self.assertEqual(
            {k: v for k, v in obj.items() if k in ["public", "description"]},
            dict(public=True, description="something cool"),
        )
        resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}/fetch")
        self.assertTrue(resp.ok, resp.text)
        rows = resp.json()["events"]
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]["input"], "hello")
        self.assertEqual(rows[0]["output"], "world")

        # It should also be accessible by the authenticated user.
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/experiment/{experiment.id}", headers={"Authorization": f"Bearer {self.org_api_key}"}
        )
        self.assertTrue(resp.ok, resp.text)

        # It should also be accessible by an authenticated user in a different org.
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/experiment/{experiment.id}", headers={"Authorization": f"Bearer {other_org_api_key}"}
        )
        self.assertTrue(resp.ok, resp.text)

        # Searching by fully-qualified name should also work.
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/experiment",
            params=dict(org_name=self.org_name, project_name=experiment.project.name, experiment_name=experiment.name),
        )
        self.assertTrue(resp.ok, resp.text)
        self.assertEqual(resp.json()["objects"], [obj])

        # But if the name is not fully-qualified, it won't work.
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/experiment", params=dict(org_name=self.org_name, experiment_name=experiment.name)
        )
        self.assertTrue(resp.ok, resp.text)
        self.assertEqual(resp.json()["objects"], [])
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/experiment",
            params=dict(project_name=experiment.project.name, experiment_name=experiment.name),
        )
        self.assertTrue(resp.ok, resp.text)
        self.assertEqual(resp.json()["objects"], [])

        # The anonymous user should not be able to update the experiment to
        # non-public.
        with self.assertRaises(Exception):
            resp = requests.patch(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}", json=dict(public=False))
            resp.raise_for_status()

        # If we update the experiment back to non-public, it should be
        # inaccessible.
        resp = requests.patch(
            f"{LOCAL_API_URL}/v1/experiment/{experiment.id}",
            json=dict(public=False),
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, resp.text)
        with self.assertRaises(Exception):
            resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}")
            resp.raise_for_status()
        with self.assertRaises(Exception):
            resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}/fetch")
            resp.raise_for_status()

    def test_nonwhitelisted_nonorg_nonanon(self):
        other_org_id, _ = self.createOrg()
        _, __, other_org_api_key = self.createUserInOrg(other_org_id)

        resp = requests.post(
            f"{LOCAL_API_URL}/v1/project",
            json=dict(name="project"),
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        project_id = resp.json()["id"]
        self.grant_acl(
            body=dict(object_type="project", object_id=project_id, user_id=ANON_USER_ID, permission="read"),
        )

        # It should be accessible by an anonymous user.
        resp = requests.get(f"{LOCAL_API_URL}/v1/project/{project_id}")

        # It should also be accessible by the authenticated user.
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/project/{project_id}", headers={"Authorization": f"Bearer {self.org_api_key}"}
        )
        self.assertTrue(resp.ok, resp.text)

        # It should not be accessible by an authenticated user in a different org.
        resp = requests.get(
            f"{LOCAL_API_URL}/v1/project/{project_id}", headers={"Authorization": f"Bearer {other_org_api_key}"}
        )
        self.assertEqual(resp.status_code, 403)

    def test_nolist_public_experiments_without_id(self):
        experiment = braintrust.init("p", description="something cool", is_public=True)
        experiment.log(input="hello", output="world", scores={})
        experiment.flush()

        resp = requests.get(
            f"{LOCAL_API_URL}/v1/experiment",
            headers={"Authorization": f"Bearer: {self.org_api_key}"},
            params=dict(org_name=self.org_name),
        )
        experiment_json = resp.json()["objects"][0]
        self.assertEqual(experiment.id, experiment_json["id"])

        resp = requests.get(f"{LOCAL_API_URL}/v1/experiment")
        self.assertEqual(resp.json()["objects"], [])
        resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}")
        self.assertEqual(resp.json(), experiment_json)

        # A user who is part of a different org should have the same experience
        # as the anonymous user.
        other_org_id, other_org_name = self.createOrg()
        other_user_id, _, other_user_api_key = self.createUserInOrg(other_org_id)
        other_user_headers = {"Authorization": f"Bearer {other_user_api_key}"}

        resp = requests.get(f"{LOCAL_API_URL}/v1/experiment", headers=other_user_headers)
        self.assertTrue(resp.ok, resp.text)
        self.assertEqual(resp.json()["objects"], [])

        resp = requests.get(f"{LOCAL_API_URL}/v1/experiment/{experiment.id}", headers=other_user_headers)
        resp.raise_for_status()

    def test_list_roles(self):
        resp = requests.get(f"{LOCAL_API_URL}/v1/role")
        resp.raise_for_status()
        all_roles = resp.json()["objects"]
        all_role_names = set(r["name"] for r in all_roles)
        # There might be other system roles being created concurrently by unit
        # tests, which we don't care about.
        self.assertTrue(all_role_names.issuperset({"Owner", "Engineer", "Viewer"}), all_role_names)
