from typing import Any

import braintrust
from braintrust.xact_ids import prettify_xact
from braintrust_local.api_db_util import get_object_json
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, make_v1_url
from tests.bt_services.test_functions import CALCULATOR_PARAMS, FunctionTestBase


class PushPromptsTest(FunctionTestBase):
    def setUp(self):
        super().setUp()

    @parameterized.expand(
        [
            (has_options, has_raw_tools, has_function_tools, lang)
            for has_options in [False, True]
            for has_raw_tools in [False, True]
            for has_function_tools in [False, True]
            for lang in ["typescript"]
        ]
    )
    def test_push_prompts(self, has_options, has_raw_tools, has_function_tools, lang):
        logger = braintrust.init_logger(project="test-push-prompts")
        project_id = logger.project.id
        project = braintrust.projects.create(name="test-push-prompts")
        messages: Any = [{"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}]
        prompt = project.prompts.create(
            name="test-prompt",
            slug="test-prompt",
            model="claude-3-5-sonnet-latest",
            messages=messages,
            if_exists="error",
        )

        if has_options:
            prompt.params = {"temperature": "0.1", "max_tokens": 100}

        prompt.tools = []
        if has_raw_tools:
            prompt.tools.append(
                {
                    "type": "function",
                    "function": {
                        "name": "calculator",
                        "description": "A simple calculator",
                        "parameters": CALCULATOR_PARAMS,
                    },
                }
            )

        if has_function_tools:
            calculator = self._insert_calculator(lang)
            prompt.tools.append(
                {
                    "type": "function",
                    "id": calculator["id"],
                }
            )

        project.publish()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                from: project_prompts('{project_id}')
                | select: *
                | filter: slug='test-prompt' """
            },
        )
        prompts = resp.json()["data"]
        self.assertEqual(len(prompts), 1)
        self.assertEqual(prompts[0]["slug"], "test-prompt")
        self.assertEqual(prompts[0]["prompt_data"]["prompt"]["messages"], messages)

        old_xact_id = prompts[0]["_xact_id"]

        prompt.if_exists = "replace"
        project.publish()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                from: project_prompts('{project_id}')
                | select: *
                | filter: slug='test-prompt' """
            },
        )
        prompts = resp.json()["data"]
        self.assertEqual(len(prompts), 1)

        prompt = prompts[0]
        self.assertEqual(prompt["slug"], "test-prompt")
        new_xact_id = prompt["_xact_id"]
        self.assertNotEqual(old_xact_id, new_xact_id)
