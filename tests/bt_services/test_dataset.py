import braintrust

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase


class CreateExperimentWithDatasetTest(BraintrustAppTestBase):
    def test_dataset_btql(self):
        dataset = braintrust.init_dataset(project="basic_dataset_py")
        for i in range(10):
            dataset.insert(
                input=i * 20,
                output={"result": i + 1, "error": None},
                metadata={
                    "foo": i % 2,
                    "bar": "aaaa",
                },
            )

        dataset.flush()

        filtered_dataset = braintrust.init_dataset(
            "basic_dataset_py",
            dataset.name,
            _internal_btql={
                "filter": {
                    "op": "ilike",
                    "left": {
                        "op": "ident",
                        "name": ["input"],
                    },
                    "right": {
                        "op": "literal",
                        "value": "%1%",
                    },
                }
            },
        )

        rows = []
        for record in filtered_dataset:
            self.assertIsNotNone(record.get("input"))
            self.assertEqual("1" in f"{record.get('input')}", True)
            rows.append(record)

        self.assertEqual(len(rows), 5)

    def test_dataset_btql_limit(self):
        dataset = braintrust.init_dataset(project="basic_dataset_limit_py")
        expected_row_count = braintrust.INTERNAL_BTQL_LIMIT + 10
        for i in range(expected_row_count):
            dataset.insert(
                input=i,
                expected=None,
            )

        dataset.flush()

        filtered_dataset = braintrust.init_dataset(
            "basic_dataset_limit_py",
            dataset.name,
            _internal_btql={},
        )

        row_count = 0
        for record in filtered_dataset:
            row_count += 1
        self.assertEqual(row_count, expected_row_count)

    def test_fetch_large_dataset(self):
        dataset = braintrust.init_dataset(project="large_dataset")
        # 10 KB. The payload limit for overflowing to S3 is about 10MB, so we
        # insert 1000 of these rows.
        large_payload = "a" * 1024 * 10
        for i in range(1000):
            dataset.insert(input=i, expected=large_payload)
        dataset.flush()

        # Reduce the retry policy for the connection. This should be fine
        # because we'll re-login for the next test.
        braintrust.api_conn().adapter = braintrust.RetryRequestExceptionsAdapter(
            base_num_retries=2, backoff_factor=0.1
        )
        braintrust.api_conn()._reset()
        sorted_events = sorted(list(dataset), key=lambda x: x["input"])
        self.assertEqual(len(sorted_events), 1000)
        for i, row in enumerate(sorted_events):
            self.assertEqual(row["input"], i)
            self.assertEqual(row["expected"], large_payload)

    def test_dataset_read_only_permission(self):
        """Test that a user with read-only permission on a dataset cannot init it"""
        # Create a project and dataset with the main user
        project_name = "dataset_permissions_test"
        dataset_name = "test_read_only_dataset"
        dataset = braintrust.init_dataset(project=project_name, name=dataset_name)

        # Log some data to the dataset
        for i in range(5):
            dataset.insert(input={"value": i}, expected={"result": i * 2})
        dataset.flush()

        # Create a new user who is not an org owner
        new_user_id, _, new_user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Grant read permission on the project to the new user
        self.grant_acl(
            {"object_type": "project", "object_id": dataset.project.id, "user_id": new_user_id, "permission": "read"}
        )

        # Grant read-only permission on the dataset to the new user
        self.grant_acl(
            {"object_type": "dataset", "object_id": dataset.id, "user_id": new_user_id, "permission": "read"}
        )

        # Login with the new user's credentials
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=new_user_api_key, force_login=True)

        # Try to init the dataset with the new user
        read_dataset = braintrust.init_dataset(project="dataset_permissions_test", name=dataset_name)

        # Now the user with read permission should be able to read the dataset
        rows = list(read_dataset)
        self.assertEqual(len(rows), 5)

        # Verify the data is correct
        for i, row in enumerate(sorted(rows, key=lambda x: x["input"]["value"])):
            self.assertEqual(row["input"]["value"], i)
            self.assertEqual(row["expected"]["result"], i * 2)

        # Now test that a completely unprivileged user cannot read the dataset
        unprivileged_user_id, _, unprivileged_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Login with the unprivileged user's credentials
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=unprivileged_api_key, force_login=True)

        # This user has no permissions on the project or dataset
        # They should not be able to init the dataset
        with self.assertRaises(Exception) as exc_info:
            unprivileged_dataset = braintrust.init_dataset(project="dataset_permissions_test", name=dataset_name)
            # Try to read it
            list(unprivileged_dataset)

        # Verify the error is about missing access
        error_msg = str(exc_info.exception)
        self.assertIn("missing", error_msg.lower())
