import json
import os
import subprocess
import unittest
from unittest.mock import ANY

import braintrust
from braintrust import load_prompt
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
from tests.bt_services.test_bundled_code import TEST_DIR
from tests.bt_services.test_functions import FunctionTestBase
from tests.bt_services.test_prompt import PromptTestBase


class FunctionHooksTest(FunctionTestBase):
    @parameterized.expand(
        [
            (lang, field, input_is_object)
            for lang in ["py", "ts", "llm"]
            for field in ["metadata", "expected"]
            for input_is_object in [False, True]
        ]
    )
    def test_function_hooks(self, lang, field, input_is_object):
        dataset = braintrust.init_dataset("dataset-project", "dataset-name")
        dataset.insert(input={"obj": 1} if input_is_object else 1, expected={"foo": "bar"}, metadata={"foo": "baz"})
        dataset.flush()

        task_function = None
        runtime = None
        prompt_data = None
        if lang == "py":
            runtime = {"runtime": "python", "version": "3.11"}
            if field == "expected":
                task_function = """
def handler(input, hooks):
    return hooks.expected['foo']
        """
            else:
                task_function = """
def handler(input, hooks):
    return hooks.metadata['foo']
        """
        elif lang == "ts":
            runtime = {"runtime": "node", "version": "20"}
            if field == "expected":
                task_function = """
function handler(input, hooks) {
    return hooks.expected.foo;
}
            """
            else:
                task_function = """
function handler(input, hooks) {
    return hooks.metadata.foo;
}
            """
        elif lang == "llm":
            if field == "expected":
                prompt = """Return the following string, nothing else: {{expected.foo}}"""
            else:
                prompt = """Return the following string, nothing else: {{metadata.foo}}"""

            prompt_data = {
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": prompt}],
                },
                "options": {
                    "model": "gpt-4o",
                },
            }

        function_info = self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "code",
                "data": {
                    "type": "inline",
                    "runtime_context": runtime,
                    "code": task_function,
                },
            }
            if lang != "llm"
            else None,
            prompt_data=prompt_data,
            name="my task",
            slug="my-task",
        )

        summary = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": self.project.id,
                "data": {"dataset_id": dataset.id},
                "task": {
                    "function_id": function_info["id"],
                },
                "scores": [
                    {"global_function": "ExactMatch"},
                ],
            },
        )
        summary = summary.json()
        experiment_id = summary["experimentId"]
        self.flush_proxy_promises()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={"query": f"from: experiment('{experiment_id}') | select: *"},
        )
        data = resp.json()["data"]
        root_span = next(span for span in data if span["is_root"] == True)

        self.assertEqual(root_span["output"], "bar" if field == "expected" else "baz")
        # We don't actually return the original expected value
        self.assertEqual(summary["scores"]["ExactMatch"]["score"], 0)

    @parameterized.expand([(lang) for lang in ["py", "ts", "llm"]])
    def test_bundled_code_hooks(self, lang):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("S3 is not available")
        if lang != "ts":
            raise unittest.SkipTest("Only TS supports bundling evals")

        task_file = os.path.join(TEST_DIR, "bundled_test_function_hooks", "hooks.eval.ts")
        process = subprocess.Popen(
            ["npx", "braintrust", "eval", task_file, "--bundle", "--jsonl", "--push"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        stdout, stderr = process.communicate()
        ret = process.returncode

        if ret != 0:
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        output = stdout
        summary = json.loads(output)

        project_id = summary["projectId"]
        experiment_id = summary["experimentId"]

        # Find the functions corresponding to the experiment
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                  from: project_functions('{project_id}')
                | select: *
                | filter: origin.object_type='experiment' AND origin.object_id='{experiment_id}' """
            },
        )
        functions = resp.json()["data"]
        assert len(functions) == 2, functions

        task_function = [f for f in functions if f["function_data"]["data"]["location"]["position"]["type"] == "task"][
            0
        ]
        self.assertEqual(task_function["function_type"], "task")

        dataset = braintrust.init_dataset("hooks_test", "hooks_test")
        dataset.insert(input={"obj": 1}, expected="the answer", metadata={"coin": "heads"})
        dataset.flush()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/eval",
            json={
                "project_id": project_id,
                "data": {"dataset_id": dataset.id},
                "task": {"function_id": task_function["id"]},
                "scores": [{"global_function": "ExactMatch"}],
            },
        )

        self.assertEqual(resp.json()["scores"]["ExactMatch"]["score"], 1)


class PromptHooksTest(PromptTestBase):
    def setUp(self):
        super().setUp()
        self.logger = braintrust.init_logger(project="dataset_in_eval")
        self.project = self.logger.project

    def test_basic(self):
        slug = "my-prompt"
        self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="my prompt",
            slug=slug,
        )
        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(
            prompt.build(),
            {
                "messages": [{"content": "What is 1+1", "role": "user"}],
                "model": "gpt-3.5-turbo",
                "span_info": {
                    "metadata": {
                        "prompt": {
                            "id": ANY,
                            "project_id": self.project.id,
                            "variables": {},
                            "version": ANY,
                        }
                    }
                },
            },
        )

    def test_prompt_hooks(self):
        slug = "image-url-6980"
        self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "tools": "",
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": """{{context}}

                                 {{query}}""".strip(),
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {"url": "{{image_url}}"},
                                },
                            ],
                        }
                    ],
                },
                "options": {"model": "gpt-4o"},
            },
            name="Image URL",
            slug=slug,
        )

        prompt = load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(
            prompt.build(
                context="""what is this
             an image of""".strip(),
                query="foo",
                image_url="https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg",
            ),
            {
                "messages": [
                    {
                        "content": [
                            {
                                "text": "what is this\n"
                                "             an image of\n"
                                "\n"
                                "                                 foo",
                                "type": "text",
                            },
                            {
                                "image_url": {
                                    "url": "https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg"
                                },
                                "type": "image_url",
                            },
                        ],
                        "role": "user",
                    }
                ],
                "model": "gpt-4o",
                "span_info": {
                    "metadata": {
                        "prompt": {
                            "id": ANY,
                            "project_id": self.project.id,
                            "variables": {
                                "context": "what is this\n" "             " "an image of",
                                "image_url": "https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg",
                                "query": "foo",
                            },
                            "version": ANY,
                        }
                    }
                },
            },
        )
