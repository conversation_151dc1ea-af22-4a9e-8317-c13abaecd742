import json
from uuid import uuid4

import braintrust
import redis
import requests
from braintrust_local.constants import (
    BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER,
    LOCAL_REDIS_HOST,
    LOCAL_REDIS_PORT,
)

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url


def make_environment_url(*args):
    """Create URL for environment endpoints"""
    ret = f"{LOCAL_API_URL}/environment"
    for arg in args:
        ret += f"/{arg}"
    return ret


def make_environment_object_url(*args):
    """Create URL for environment object endpoints"""
    ret = f"{LOCAL_API_URL}/environment-object"
    for arg in args:
        ret += f"/{arg}"
    return ret


class PromptEnvironmentIntegrationTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        # Create a project for prompt testing
        self.logger = braintrust.init_logger(project="prompt-env-integration-test")
        self.project = self.logger.project

        # Create test environments
        self.dev_env = self._create_environment("dev", "Development Environment")
        self.prod_env = self._create_environment("prod", "Production Environment")

        # Create test prompts
        self.test_prompt_1 = self._create_prompt("test-prompt-1", "Test Prompt 1")
        self.test_prompt_2 = self._create_prompt("test-prompt-2", "Test Prompt 2")

        # Initialize Redis connection for cache testing
        self.conn = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0)

    def _check_cache_hit(self, token):
        """Check if a request was served from cache using the cache token"""
        resp = self.conn.get(token)
        if not resp:
            return False
        else:
            return resp.decode() == "true"

    def _create_environment(self, slug, name):
        """Helper to create a test environment"""
        env_data = {"name": name, "slug": slug, "description": f"{name} for testing"}
        resp = self.run_request("post", make_environment_url(), json=env_data)
        return resp.json()

    def _create_prompt(self, slug, name="Test Prompt"):
        """Helper to create a test prompt"""
        prompt_data = {
            "project_id": self.project.id,
            "prompt_data": {
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Test prompt content"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            "name": name,
            "slug": slug,
        }
        resp = self.run_request("post", make_v1_url("prompt"), json=prompt_data)
        if not resp.ok:
            print(f"Failed to create prompt: {resp.status_code} - {resp.text}")
            resp.raise_for_status()
        return resp.json()

    def _update_prompt(self, prompt_id, content="Updated content"):
        """Helper to update a prompt and get new version"""
        update_data = {
            "prompt_data": {
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": content}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            }
        }
        resp = self.run_request("patch", make_v1_url("prompt", prompt_id), json=update_data)
        return resp.json()

    def _create_environment_association(self, prompt_id, environment_slug, version):
        """Helper to create environment-prompt association"""
        association_data = {"object_version": version}
        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", prompt_id),
            json={"environment_slug": environment_slug, **association_data},
        )
        return resp.json()

    def _upsert_environment_association(self, prompt_id, environment_slug, version):
        """Helper to upsert environment-prompt association"""
        association_data = {"object_version": version}
        resp = self.run_request(
            "put", make_environment_object_url("prompt", prompt_id, environment_slug), json=association_data
        )
        return resp.json()

    # === Association CRUD Tests ===

    def test_create_environment_object_association(self):
        """Test creating a new environment object association"""
        association_data = {
            "object_version": "1",
            "environment_slug": self.dev_env["slug"],
        }

        resp = self.run_request(
            "post", make_environment_object_url("prompt", self.test_prompt_1["id"]), json=association_data
        )
        association = resp.json()

        self.assertEqual(association["object_type"], "prompt")
        self.assertEqual(association["object_id"], self.test_prompt_1["id"])
        self.assertEqual(association["object_version"], "1")
        self.assertEqual(association["environment_slug"], self.dev_env["slug"])
        self.assertIsNotNone(association["id"])
        self.assertIsNotNone(association["created"])

    def test_create_environment_object_association_with_org_name(self):
        """Test creating association with explicit org_name"""
        association_data = {
            "object_version": "2",
            "environment_slug": self.prod_env["slug"],
            "org_name": self.org_name,
        }

        resp = self.run_request(
            "post", make_environment_object_url("prompt", self.test_prompt_2["id"]), json=association_data
        )
        association = resp.json()

        self.assertEqual(association["object_type"], "prompt")
        self.assertEqual(association["object_id"], self.test_prompt_2["id"])
        self.assertEqual(association["object_version"], "2")

    def test_create_environment_object_association_missing_fields(self):
        """Test creating association without required fields fails"""
        # Missing object_version
        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            json={"environment_slug": self.dev_env["slug"]},
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)

        # Missing environment_slug
        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            json={"object_version": "1"},
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)

    def test_create_environment_object_association_invalid_object_version(self):
        """Test creating association with invalid object_version values fails"""
        invalid_versions = [
            "not-a-number",
            "1.5",
            "-1",
            "",
            "abc123",
            "123abc",
        ]

        for invalid_version in invalid_versions:
            association_data = {
                "object_version": invalid_version,
                "environment_slug": self.dev_env["slug"],
            }

            resp = self.run_request(
                "post",
                make_environment_object_url("prompt", self.test_prompt_1["id"]),
                json=association_data,
                expect_error=True,
            )
            self.assertGreaterEqual(resp.status_code, 400, f"Failed for invalid version: {invalid_version}")
            self.assertIn("Must be a valid bigint string", resp.text, f"Wrong error for: {invalid_version}")

    def test_upsert_environment_object_association_invalid_object_version(self):
        """Test upserting association with invalid object_version values fails"""
        # First create a valid association
        valid_data = {
            "object_version": "1",
            "environment_slug": self.dev_env["slug"],
        }
        self._create_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "1")

        # Now try to upsert with invalid versions
        invalid_versions = ["not-a-number", "1.5", "abc"]

        for invalid_version in invalid_versions:
            upsert_data = {"object_version": invalid_version}

            resp = self.run_request(
                "put",
                make_environment_object_url("prompt", self.test_prompt_1["id"], self.dev_env["slug"]),
                json=upsert_data,
                expect_error=True,
            )
            self.assertGreaterEqual(resp.status_code, 400, f"Failed for invalid version: {invalid_version}")
            self.assertIn("Must be a valid bigint string", resp.text, f"Wrong error for: {invalid_version}")

    def test_create_environment_object_association_valid_bigint_strings(self):
        """Test creating association with valid bigint string values succeeds"""
        valid_versions = [
            "0",
            "1",
            "9223372036854775807",
        ]

        for i, valid_version in enumerate(valid_versions):
            # Use different prompt for each test to avoid conflicts
            test_prompt = self._create_prompt(f"bigint-test-{i}")

            association_data = {
                "object_version": valid_version,
                "environment_slug": self.dev_env["slug"],
            }

            resp = self.run_request(
                "post",
                make_environment_object_url("prompt", test_prompt["id"]),
                json=association_data,
            )
            self.assertEqual(resp.status_code, 200, f"Failed for valid version: {valid_version}")

            association = resp.json()
            self.assertEqual(association["object_version"], valid_version, f"Version mismatch for: {valid_version}")

    def test_create_environment_object_association_nonexistent_environment(self):
        """Test creating association with non-existent environment fails"""
        association_data = {
            "object_version": "1",
            "environment_slug": "nonexistent-env",
        }

        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            json=association_data,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("not found", resp.text)

    def test_create_environment_object_association_duplicate(self):
        """Test creating duplicate association fails"""
        association_data = {
            "object_version": "1",
            "environment_slug": self.dev_env["slug"],
        }

        # Create first association
        self.run_request(
            "post", make_environment_object_url("prompt", self.test_prompt_1["id"]), json=association_data
        )

        # Try to create duplicate association
        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            json=association_data,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("Object already has version associated", resp.text)

    def test_list_environment_object_associations(self):
        """Test listing all associations for an object"""
        # Create multiple environments
        env2_data = {"name": "Staging", "slug": "staging"}
        env2_resp = self.run_request("post", make_environment_url(), json=env2_data)
        env2 = env2_resp.json()

        # Create associations for the same object in different environments
        associations = [
            {"object_version": "1", "environment_slug": self.dev_env["slug"]},
            {"object_version": "2", "environment_slug": env2["slug"]},
        ]

        for assoc_data in associations:
            self._create_environment_association(
                self.test_prompt_1["id"], assoc_data["environment_slug"], assoc_data["object_version"]
            )

        # List associations
        resp = self.run_request("get", make_environment_object_url("prompt", self.test_prompt_1["id"]))
        result = resp.json()

        self.assertIn("objects", result)
        self.assertEqual(len(result["objects"]), 2)

        # Should be ordered by created DESC
        associations = result["objects"]
        self.assertEqual(associations[0]["object_version"], "2")
        self.assertEqual(associations[1]["object_version"], "1")

        # Check environment_slug is included
        env_slugs = {assoc["environment_slug"] for assoc in associations}
        self.assertEqual(env_slugs, {"dev", "staging"})

    def test_get_specific_environment_object_association(self):
        """Test getting a specific association by object + environment"""
        # Create association
        self._create_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "3")

        # Get specific association
        resp = self.run_request(
            "get",
            make_environment_object_url("prompt", self.test_prompt_1["id"], self.dev_env["slug"]),
        )
        association = resp.json()

        self.assertEqual(association["object_type"], "prompt")
        self.assertEqual(association["object_id"], self.test_prompt_1["id"])
        self.assertEqual(association["object_version"], "3")

    def test_get_specific_environment_object_association_not_found(self):
        """Test getting non-existent association with non-existent prompt returns permission error"""
        resp = self.run_request(
            "get",
            make_environment_object_url("prompt", "nonexistent-prompt", self.dev_env["slug"]),
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        # Should get permission error for non-existent prompt (don't leak whether prompt exists)
        self.assertIn("Missing read access", resp.text)

    def test_upsert_environment_object_association(self):
        """Test upserting (PUT) an association"""
        # Create initial association using upsert (PUT) - tests creating when doesn't exist
        self._upsert_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "1")

        # Upsert with new version
        association = self._upsert_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "2")

        self.assertEqual(association["object_version"], "2")
        self.assertEqual(association["object_type"], "prompt")
        self.assertEqual(association["object_id"], self.test_prompt_1["id"])

        # Verify old association was replaced
        list_resp = self.run_request("get", make_environment_object_url("prompt", self.test_prompt_1["id"]))
        associations = list_resp.json()["objects"]
        self.assertEqual(len(associations), 1)
        self.assertEqual(associations[0]["object_version"], "2")

    def test_delete_environment_object_association(self):
        """Test deleting an association"""
        # Create association
        self._create_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "1")

        # Delete association
        resp = self.run_request(
            "delete",
            make_environment_object_url("prompt", self.test_prompt_1["id"], self.dev_env["slug"]),
        )
        deleted_association = resp.json()

        self.assertEqual(deleted_association["object_type"], "prompt")
        self.assertEqual(deleted_association["object_id"], self.test_prompt_1["id"])
        self.assertEqual(deleted_association["object_version"], "1")

        # Verify association is gone
        list_resp = self.run_request("get", make_environment_object_url("prompt", self.test_prompt_1["id"]))
        associations = list_resp.json()["objects"]
        self.assertEqual(len(associations), 0)

    def test_delete_environment_object_association_not_found(self):
        """Test deleting non-existent association with non-existent prompt returns permission error"""
        resp = self.run_request(
            "delete",
            make_environment_object_url("prompt", "nonexistent-prompt", self.dev_env["slug"]),
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        # Should get permission error for non-existent prompt (don't leak whether prompt exists)
        self.assertIn("Missing read access", resp.text)

    # === Prompt Retrieval Integration Tests ===

    def test_basic_environment_resolution(self):
        """Test that environment parameter resolves to correct version"""
        # Create a prompt
        prompt = self._create_prompt("env-resolution-test")
        initial_version = prompt["_xact_id"]

        # Update the prompt to create a new version
        updated_prompt = self._update_prompt(prompt["id"], "Updated content for dev")
        dev_version = updated_prompt["_xact_id"]

        # Update again for prod version
        prod_updated = self._update_prompt(prompt["id"], "Updated content for prod")
        prod_version = prod_updated["_xact_id"]

        # Associate dev version with dev environment
        self._create_environment_association(prompt["id"], self.dev_env["slug"], dev_version)

        # Associate prod version with prod environment
        self._create_environment_association(prompt["id"], self.prod_env["slug"], prod_version)

        # Test loading prompt with environment parameter
        dev_prompt_resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={"project_id": self.project.id, "slug": "env-resolution-test", "environment": self.dev_env["slug"]},
        )
        dev_prompt_data = dev_prompt_resp.json()["objects"]

        # Verify we get the dev version
        self.assertEqual(len(dev_prompt_data), 1)
        self.assertEqual(dev_prompt_data[0]["_xact_id"], dev_version)
        self.assertIn("Updated content for dev", str(dev_prompt_data[0]["prompt_data"]))

        # Test prod environment
        prod_prompt_resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "env-resolution-test",
                "environment": self.prod_env["slug"],
            },
        )
        prod_prompt_data = prod_prompt_resp.json()["objects"]

        # Verify we get the prod version
        self.assertEqual(len(prod_prompt_data), 1)
        self.assertEqual(prod_prompt_data[0]["_xact_id"], prod_version)
        self.assertIn("Updated content for prod", str(prod_prompt_data[0]["prompt_data"]))

        # Verify the versions are different
        self.assertNotEqual(dev_prompt_data[0]["_xact_id"], prod_prompt_data[0]["_xact_id"])

    def test_prompt_id_endpoint_environment_resolution(self):
        """Test that /v1/prompt/:id endpoint resolves environment parameter correctly"""
        # Create a prompt
        prompt = self._create_prompt("prompt-id-env-test")
        initial_version = prompt["_xact_id"]

        # Update the prompt to create a new version
        updated_prompt = self._update_prompt(prompt["id"], "Updated content for dev environment")
        dev_version = updated_prompt["_xact_id"]

        # Update again for prod version
        prod_updated = self._update_prompt(prompt["id"], "Updated content for prod environment")
        prod_version = prod_updated["_xact_id"]

        # Associate dev version with dev environment
        self._create_environment_association(prompt["id"], self.dev_env["slug"], dev_version)

        # Associate prod version with prod environment
        self._create_environment_association(prompt["id"], self.prod_env["slug"], prod_version)

        # Test loading prompt by ID with dev environment parameter
        dev_prompt_resp = self.run_request(
            "get",
            make_v1_url("prompt", prompt["id"]),
            params={"environment": self.dev_env["slug"]},
        )
        dev_prompt_data = dev_prompt_resp.json()

        # Verify we get the dev version
        self.assertEqual(dev_prompt_data["_xact_id"], dev_version)
        self.assertIn("Updated content for dev environment", str(dev_prompt_data["prompt_data"]))

        # Test loading prompt by ID with prod environment parameter
        prod_prompt_resp = self.run_request(
            "get",
            make_v1_url("prompt", prompt["id"]),
            params={"environment": self.prod_env["slug"]},
        )
        prod_prompt_data = prod_prompt_resp.json()

        # Verify we get the prod version
        self.assertEqual(prod_prompt_data["_xact_id"], prod_version)
        self.assertIn("Updated content for prod environment", str(prod_prompt_data["prompt_data"]))

        # Verify the versions are different
        self.assertNotEqual(dev_prompt_data["_xact_id"], prod_prompt_data["_xact_id"])

        # Test without environment parameter (should get latest version)
        latest_prompt_resp = self.run_request(
            "get",
            make_v1_url("prompt", prompt["id"]),
        )
        latest_prompt_data = latest_prompt_resp.json()
        self.assertEqual(latest_prompt_data["_xact_id"], prod_version)  # Should get the latest version

    def test_nonexistent_environment_association(self):
        """Test behavior when environment association doesn't exist"""
        # Create a prompt
        prompt = self._create_prompt("no-env-association-test")

        # Try to get association that doesn't exist
        resp = self.run_request(
            "get", make_environment_object_url("prompt", prompt["id"], "nonexistent-env"), expect_error=True
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("No association found", resp.text)

    def test_multiple_environment_associations(self):
        """Test prompt with multiple environment associations"""
        # Create a prompt
        prompt = self._create_prompt("multi-env-association-test")

        # Create different versions
        v1 = prompt["_xact_id"]
        v2_prompt = self._update_prompt(prompt["id"], "Version 2 content")
        v2 = v2_prompt["_xact_id"]
        v3_prompt = self._update_prompt(prompt["id"], "Version 3 content")
        v3 = v3_prompt["_xact_id"]

        # Associate different versions with different environments
        self._create_environment_association(prompt["id"], self.dev_env["slug"], v2)
        self._create_environment_association(prompt["id"], self.prod_env["slug"], v3)

        # List all associations
        list_resp = self.run_request("get", make_environment_object_url("prompt", prompt["id"]))
        associations = list_resp.json()["objects"]

        self.assertEqual(len(associations), 2)

        # Verify correct associations
        env_to_version = {assoc["environment_slug"]: assoc["object_version"] for assoc in associations}
        self.assertEqual(env_to_version[self.dev_env["slug"]], v2)
        self.assertEqual(env_to_version[self.prod_env["slug"]], v3)

    def test_environment_association_update(self):
        """Test updating environment association to point to new version"""
        # Create a prompt
        prompt = self._create_prompt("association-update-test")
        v1 = prompt["_xact_id"]

        # Create initial association
        self._upsert_environment_association(prompt["id"], self.dev_env["slug"], v1)

        # Update prompt to create new version
        updated = self._update_prompt(prompt["id"], "Updated content")
        v2 = updated["_xact_id"]

        # Update association to point to new version
        updated_association = self._upsert_environment_association(prompt["id"], self.dev_env["slug"], v2)

        self.assertEqual(updated_association["object_version"], v2)

        # Verify old association is gone
        get_resp = self.run_request("get", make_environment_object_url("prompt", prompt["id"], self.dev_env["slug"]))
        current_association = get_resp.json()
        self.assertEqual(current_association["object_version"], v2)

    # === Security and Edge Cases ===

    def test_cross_org_isolation(self):
        """Test that associations are isolated between organizations"""
        # Create association in current org
        self._create_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "1")

        # Create another org and user
        other_org_id, other_org_name = self.createOrg()
        _, _, other_api_key = self.createUserInOrg(other_org_id)
        other_headers = {"Authorization": f"Bearer {other_api_key}"}

        # Create environment in other org with same slug
        env_data = {"name": "Other Dev Environment", "slug": "dev"}
        other_env_resp = self.run_request("post", make_environment_url(), json=env_data, headers=other_headers)
        other_env = other_env_resp.json()

        # List associations from other org should fail with permission error
        resp = self.run_request(
            "get",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            headers=other_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        # Should get permission error, not leak information about whether prompt exists
        self.assertIn("Missing read access", resp.text)

        # Try to create association in other org with same prompt ID (should fail)
        other_association_data = {
            "object_version": "2",
            "environment_slug": other_env["slug"],
            "org_name": other_org_name,
        }

        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            json=other_association_data,
            headers=other_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        # Should get permission error when trying to access prompt from another org
        self.assertIn("Missing read access", resp.text)

        # Test prompt retrieval across orgs
        resp = self.run_request(
            "get",
            make_environment_object_url("prompt", self.test_prompt_1["id"], "dev"),
            headers=other_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("Missing read access to prompt", resp.text)

    def test_environment_object_with_deleted_environment(self):
        """Test behavior when environment is deleted"""
        # Create association
        self._create_environment_association(self.test_prompt_1["id"], self.dev_env["slug"], "1")

        # Delete the environment
        self.run_request("delete", make_environment_url(self.dev_env["id"]))

        # Listing associations should return empty (environment is deleted)
        resp = self.run_request("get", make_environment_object_url("prompt", self.test_prompt_1["id"]))
        result = resp.json()
        self.assertEqual(len(result["objects"]), 0)

        # Getting specific association should fail
        resp = self.run_request(
            "get",
            make_environment_object_url("prompt", self.test_prompt_1["id"], self.dev_env["slug"]),
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("No association found", resp.text)

    def test_unauthorized_access(self):
        """Test access without proper permissions"""
        # Create an unprivileged user
        unprivileged_user_id, _, unprivileged_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        unprivileged_headers = {"Authorization": f"Bearer {unprivileged_api_key}"}

        association_data = {
            "object_version": "1",
            "environment_slug": self.dev_env["slug"],
        }

        # Try to create association (should fail - needs update permission)
        resp = self.run_request(
            "post",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            json=association_data,
            headers=unprivileged_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)

        # List should also fail (no read permission)
        resp = self.run_request(
            "get",
            make_environment_object_url("prompt", self.test_prompt_1["id"]),
            headers=unprivileged_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)

    def test_environment_association_delete_cache_invalidation(self):
        """Test that deleting an environment association invalidates the prompt cache for all fetch methods"""
        # Create a prompt
        prompt = self._create_prompt("cache-invalidation-test")
        initial_version = prompt["_xact_id"]
        prompt_id = prompt["id"]
        project_id = self.project.id
        project_name = self.project.name
        slug = "cache-invalidation-test"

        # Associate prompt with dev environment
        self._upsert_environment_association(prompt_id, self.dev_env["slug"], initial_version)

        # --- 1. Fetch by project_id + slug ---
        resp_proj_id = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_id": project_id,
                "slug": slug,
                "environment": self.dev_env["slug"],
            },
        )
        result_proj_id = resp_proj_id.json()["objects"]
        self.assertEqual(len(result_proj_id), 1)
        self.assertEqual(result_proj_id[0]["_xact_id"], initial_version)

        # --- 2. Fetch by prompt_id ---
        resp_prompt_id = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "prompt_id": prompt_id,
                "environment": self.dev_env["slug"],
            },
        )
        result_prompt_id = resp_prompt_id.json()["objects"]
        self.assertEqual(len(result_prompt_id), 1)
        self.assertEqual(result_prompt_id[0]["_xact_id"], initial_version)

        # --- 3. Fetch by project_name + slug ---
        resp_proj_name = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_name": project_name,
                "slug": slug,
                "environment": self.dev_env["slug"],
            },
        )
        result_proj_name = resp_proj_name.json()["objects"]
        self.assertEqual(len(result_proj_name), 1)
        self.assertEqual(result_proj_name[0]["_xact_id"], initial_version)

        # Now delete the environment association
        delete_resp = self.run_request(
            "delete", make_environment_object_url("prompt", prompt_id, self.dev_env["slug"])
        )
        self.assertEqual(delete_resp.status_code, 200)
        deleted_assoc = delete_resp.json()
        self.assertEqual(deleted_assoc["object_version"], initial_version)

        # --- 1. Fetch by project_id + slug (should fail) ---
        resp_proj_id_2 = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_id": project_id,
                "slug": slug,
                "environment": self.dev_env["slug"],
            },
            expect_error=True,
        )
        self.assertIn("No association found", resp_proj_id_2.text)

        # --- 2. Fetch by prompt_id (should fail) ---
        resp_prompt_id_2 = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "prompt_id": prompt_id,
                "environment": self.dev_env["slug"],
            },
            expect_error=True,
        )
        self.assertIn("No association found", resp_prompt_id_2.text)

        # --- 3. Fetch by project_name + slug (should fail) ---
        resp_proj_name_2 = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_name": project_name,
                "slug": slug,
                "environment": self.dev_env["slug"],
            },
            expect_error=True,
        )
        self.assertIn("No association found", resp_proj_name_2.text)

        # Verify the association is actually gone by trying to get it directly
        assoc_resp = self.run_request(
            "get",
            make_environment_object_url("prompt", prompt_id, self.dev_env["slug"]),
            expect_error=True,
        )
        self.assertGreaterEqual(assoc_resp.status_code, 400)
        self.assertIn("No association found", assoc_resp.text)

    def test_environment_deletion_cache_invalidation(self):
        """Test that deleting an environment invalidates all prompt caches for that environment"""
        # Create a prompt
        prompt = self._create_prompt("env-deletion-cache-test")
        initial_version = prompt["_xact_id"]

        # Create a temporary environment for this test (don't use shared dev_env)
        temp_env = self._create_environment("temp-deletion-test", "Temp Environment for Deletion")

        # Associate prompt with the temporary environment
        self._upsert_environment_association(prompt["id"], temp_env["slug"], initial_version)

        # First request - should populate the cache
        first_resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "env-deletion-cache-test",
                "environment": temp_env["slug"],
            },
        )
        first_result = first_resp.json()["objects"]
        self.assertEqual(len(first_result), 1, "Should get exactly one prompt")
        self.assertEqual(first_result[0]["_xact_id"], initial_version, "Should get correct prompt version")

        # Now delete the environment itself (not just the association)
        delete_env_resp = self.run_request("delete", make_environment_url(temp_env["id"]))
        self.assertEqual(delete_env_resp.status_code, 200, "Environment deletion should succeed")
        deleted_env = delete_env_resp.json()
        self.assertEqual(deleted_env["id"], temp_env["id"], "Deleted environment should match temp environment")

        # Try to fetch the prompt with environment again - this should fail now that the environment is deleted
        second_resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "env-deletion-cache-test",
                "environment": temp_env["slug"],
            },
            expect_error=True,
        )

        # The request should fail because the environment no longer exists
        self.assertGreaterEqual(second_resp.status_code, 400)
        self.assertIn("No association found", second_resp.text)

        # Verify the environment is actually deleted by trying to get associations
        assoc_resp = self.run_request(
            "get",
            make_environment_object_url("prompt", prompt["id"], temp_env["slug"]),
            expect_error=True,
        )
        self.assertGreaterEqual(assoc_resp.status_code, 400)
        self.assertIn("No association found", assoc_resp.text)

    def test_environment_upsert_cache_invalidation(self):
        """Test that upserting an environment association invalidates the prompt cache"""
        # Need to use direct requests here to make the redis cache redis header work correctly

        # Create a prompt
        prompt = self._create_prompt("upsert-cache-invalidation-test")
        initial_version = prompt["_xact_id"]
        prompt_id = prompt["id"]
        project_id = self.project.id
        slug = "upsert-cache-invalidation-test"

        # Create a temporary environment for this test
        temp_env = self._create_environment("temp-upsert-test", "Temp Environment for Upsert")

        # Create initial environment association
        self._upsert_environment_association(prompt_id, temp_env["slug"], initial_version)

        # First fetch - should populate the cache (using direct requests like working test)
        cache_token_1 = str(uuid4())
        first_resp = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": project_id,
                "slug": slug,
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_1,
            },
        )
        first_result = first_resp.json()["objects"]
        self.assertEqual(len(first_result), 1)
        self.assertEqual(first_result[0]["_xact_id"], initial_version)
        self.assertFalse(self._check_cache_hit(cache_token_1), "First fetch should not be cached")

        # Second fetch - should be cached
        cache_token_2 = str(uuid4())
        second_resp = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": project_id,
                "slug": slug,
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_2,
            },
        )
        second_result = second_resp.json()["objects"]
        self.assertEqual(len(second_result), 1)
        self.assertEqual(second_result[0]["_xact_id"], initial_version)
        self.assertTrue(self._check_cache_hit(cache_token_2), "Second fetch should be cached")

        # Update the prompt to create a new version
        updated_prompt = self._update_prompt(prompt_id, "Updated content for cache test")
        new_version = updated_prompt["_xact_id"]

        # Upsert the environment association with the new version - this should invalidate the cache
        self._upsert_environment_association(prompt_id, temp_env["slug"], new_version)

        # Third fetch - should not be cached due to upsert invalidation
        cache_token_3 = str(uuid4())
        third_resp = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": project_id,
                "slug": slug,
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_3,
            },
        )
        third_result = third_resp.json()["objects"]
        self.assertEqual(len(third_result), 1)
        self.assertEqual(third_result[0]["_xact_id"], new_version)
        self.assertFalse(self._check_cache_hit(cache_token_3), "Fetch after upsert should not be cached")

        # Fourth fetch - should be cached again
        cache_token_4 = str(uuid4())
        fourth_resp = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": project_id,
                "slug": slug,
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_4,
            },
        )
        fourth_result = fourth_resp.json()["objects"]
        self.assertEqual(len(fourth_result), 1)
        self.assertEqual(fourth_result[0]["_xact_id"], new_version)
        self.assertTrue(self._check_cache_hit(cache_token_4), "Second time fetching after upsert should be cached")

    def test_multiple_prompts_with_environment_error(self):
        """Test that using environment parameter with multiple prompts throws an error"""
        # Create two prompts
        prompt1 = self._create_prompt("multi-prompt-test-1")
        prompt2 = self._create_prompt("multi-prompt-test-2")

        # Associate both with dev environment
        self._create_environment_association(prompt1["id"], self.dev_env["slug"], prompt1["_xact_id"])
        self._create_environment_association(prompt2["id"], self.dev_env["slug"], prompt2["_xact_id"])

        # Try to fetch multiple prompts with environment parameter - should fail
        # This simulates a query that would return multiple prompts
        resp = self.run_request(
            "get",
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "environment": self.dev_env["slug"],
                # Not specifying slug means it could return multiple prompts
            },
            expect_error=True,
        )

        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("Cannot specify environment when retrieving multiple prompts", resp.text)

    def test_selective_cache_invalidation_different_prompts_same_environment(self):
        """Test that changing environment association for one prompt only invalidates that prompt's cache"""
        # Create two different prompts
        prompt1 = self._create_prompt("selective-cache-test-1", "First Prompt")
        prompt2 = self._create_prompt("selective-cache-test-2", "Second Prompt")

        # Create a temporary environment for this test
        temp_env = self._create_environment("temp-selective-test", "Temp Environment for Selective Test")

        # Associate both prompts with the same environment
        self._upsert_environment_association(prompt1["id"], temp_env["slug"], prompt1["_xact_id"])
        self._upsert_environment_association(prompt2["id"], temp_env["slug"], prompt2["_xact_id"])

        # First fetch for prompt1 - should populate the cache
        cache_token_1a = str(uuid4())
        first_resp_1 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-1",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_1a,
            },
        )
        first_result_1 = first_resp_1.json()["objects"]
        self.assertEqual(len(first_result_1), 1)
        self.assertEqual(first_result_1[0]["_xact_id"], prompt1["_xact_id"])
        self.assertFalse(self._check_cache_hit(cache_token_1a), "First fetch of prompt1 should not be cached")

        # First fetch for prompt2 - should populate the cache
        cache_token_2a = str(uuid4())
        first_resp_2 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-2",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_2a,
            },
        )
        first_result_2 = first_resp_2.json()["objects"]
        self.assertEqual(len(first_result_2), 1)
        self.assertEqual(first_result_2[0]["_xact_id"], prompt2["_xact_id"])
        self.assertFalse(self._check_cache_hit(cache_token_2a), "First fetch of prompt2 should not be cached")

        # Second fetch for both prompts - should be cached
        cache_token_1b = str(uuid4())
        second_resp_1 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-1",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_1b,
            },
        )
        self.assertEqual(second_resp_1.status_code, 200)
        self.assertTrue(self._check_cache_hit(cache_token_1b), "Second fetch of prompt1 should be cached")

        cache_token_2b = str(uuid4())
        second_resp_2 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-2",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_2b,
            },
        )
        self.assertEqual(second_resp_2.status_code, 200)
        self.assertTrue(self._check_cache_hit(cache_token_2b), "Second fetch of prompt2 should be cached")

        # Update prompt1 to create a new version
        updated_prompt1 = self._update_prompt(prompt1["id"], "Updated content for prompt1")
        new_version_1 = updated_prompt1["_xact_id"]

        # Upsert the environment association for prompt1 only - this should invalidate only prompt1's cache
        self._upsert_environment_association(prompt1["id"], temp_env["slug"], new_version_1)

        # Fetch prompt1 again - should NOT be cached due to upsert invalidation
        cache_token_1c = str(uuid4())
        third_resp_1 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-1",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_1c,
            },
        )
        third_result_1 = third_resp_1.json()["objects"]
        self.assertEqual(len(third_result_1), 1)
        self.assertEqual(third_result_1[0]["_xact_id"], new_version_1)
        self.assertFalse(self._check_cache_hit(cache_token_1c), "Fetch of prompt1 after upsert should not be cached")

        # Fetch prompt2 again - should STILL be cached (not affected by prompt1's change)
        cache_token_2c = str(uuid4())
        third_resp_2 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-2",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_2c,
            },
        )
        third_result_2 = third_resp_2.json()["objects"]
        self.assertEqual(len(third_result_2), 1)
        self.assertEqual(third_result_2[0]["_xact_id"], prompt2["_xact_id"])  # Still the original version
        self.assertTrue(
            self._check_cache_hit(cache_token_2c), "Fetch of prompt2 after prompt1 upsert should still be cached"
        )

        # Verify that prompt1 cache is working again on subsequent fetches
        cache_token_1d = str(uuid4())
        fourth_resp_1 = requests.get(
            make_v1_url("prompt"),
            params={
                "project_id": self.project.id,
                "slug": "selective-cache-test-1",
                "environment": temp_env["slug"],
            },
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token_1d,
            },
        )
        fourth_result_1 = fourth_resp_1.json()["objects"]
        self.assertEqual(len(fourth_result_1), 1)
        self.assertEqual(fourth_result_1[0]["_xact_id"], new_version_1)
        self.assertTrue(self._check_cache_hit(cache_token_1d), "Second fetch of prompt1 after upsert should be cached")
