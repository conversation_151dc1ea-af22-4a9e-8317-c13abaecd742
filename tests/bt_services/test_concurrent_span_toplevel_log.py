import asyncio

import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase

LOG_KWARGS = dict(input="a", output="b", scores=dict(foo=0.5))


async def async_log_in_span(toplevel_obj, event=None):
    with toplevel_obj.start_span() as span:
        if event is not None:
            await event.wait()
        span.log(**LOG_KWARGS)


async def async_log_outside_span(toplevel_obj, event=None):
    if event is not None:
        await event.wait()
    toplevel_obj.log(**LOG_KWARGS)


def create_toplevel_objects():
    return [("experiment", braintrust.init("p")), ("project_logs", braintrust.init_logger("p"))]


def get_object_rows(object_type, object_id):
    rows = get_object_json(object_type, object_id)
    return [{k: v for k, v in row.items() if k in LOG_KWARGS.keys()} for row in rows]


class ConcurrentSpanToplevelLogTest(BraintrustAppTestBase):
    def test_concurrent_log_sync(self):
        for object_type, toplevel_obj in create_toplevel_objects():
            toplevel_obj.log(**LOG_KWARGS)

            # Raises if there's an active span.
            with toplevel_obj.start_span() as span:
                with self.assertRaisesRegex(Exception, r"Cannot run toplevel .*\.log"):
                    toplevel_obj.log(**LOG_KWARGS)
                span.log(**LOG_KWARGS)
                with span.start_span() as subspan:
                    subspan.log(**LOG_KWARGS)

            # Also raises if the span is not active.
            span = toplevel_obj.start_span()
            with self.assertRaisesRegex(Exception, r"Cannot run toplevel .*\.log"):
                toplevel_obj.log(**LOG_KWARGS)
            span.log(**LOG_KWARGS)
            span.end()

            toplevel_obj.flush()

            self.assertEqual(get_object_rows(object_type, toplevel_obj.id), [LOG_KWARGS for _ in range(4)])

    def test_concurrent_log_async(self):
        async def run_test():
            for object_type, toplevel_obj in create_toplevel_objects():
                with toplevel_obj.start_span() as span:
                    # Raises if there's an active span.
                    with self.assertRaisesRegex(Exception, r"Cannot run toplevel .*\.log"):
                        await async_log_outside_span(toplevel_obj)
                    span.log(**LOG_KWARGS)
                    await async_log_in_span(span)

                # Also raises if the span is not active.
                span = toplevel_obj.start_span()
                with self.assertRaisesRegex(Exception, r"Cannot run toplevel .*\.log"):
                    await async_log_outside_span(toplevel_obj)
                span.log(**LOG_KWARGS)
                span.end()

                toplevel_obj.flush()

                self.assertEqual(get_object_rows(object_type, toplevel_obj.id), [LOG_KWARGS for _ in range(3)])

        asyncio.run(run_test())

    def test_concurrent_log_async2(self):
        async def run_test():
            for object_type, toplevel_obj in create_toplevel_objects():
                # We can create a span in one coroutine and toplevel log in
                # another coroutine. Only the span will succeed.
                event = asyncio.Event()
                res = asyncio.gather(
                    async_log_in_span(toplevel_obj, event), async_log_outside_span(toplevel_obj, event)
                )
                event.set()
                with self.assertRaisesRegex(Exception, r"Cannot run toplevel .*\.log"):
                    await res
                toplevel_obj.flush()
                self.assertEqual(get_object_rows(object_type, toplevel_obj.id), [LOG_KWARGS for _ in range(1)])

        asyncio.run(run_test())
