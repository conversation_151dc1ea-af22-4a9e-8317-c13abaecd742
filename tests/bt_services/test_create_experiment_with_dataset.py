import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


class CreateExperimentWithDatasetTest(BraintrustAppTestBase):
    def test_basic(self):
        dataset = braintrust.init_dataset("p", "dataset")
        dataset.insert(input="a", expected="b")
        dataset.flush()
        experiment = braintrust.init("p", dataset=dataset)
        self.assertEqual(dataset.id, experiment.dataset_id)
        self.assertEqual(dataset.version, experiment.dataset_version)
