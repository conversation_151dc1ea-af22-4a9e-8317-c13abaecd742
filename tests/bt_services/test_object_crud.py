import braintrust
from braintrust.git_fields import RepoInfo

from tests.braintrust_app_test_base import BraintrustAppTestBase


class ObjectCrudTest(BraintrustAppTestBase):
    def test_experiment_explicit_creation(self):
        experiment0 = braintrust.init("p", "foo")
        self.assertEqual(experiment0.project.name, "p")
        self.assertEqual(experiment0.name, "foo")
        experiment1 = braintrust.init_experiment("p")
        self.assertEqual(experiment1.project.name, "p")

        experiment2 = braintrust.init(
            project_id=experiment0.project.id,
            base_experiment=experiment0.name,
            base_experiment_id=experiment1.id,
            repo_info=RepoInfo(
                commit="foobar",
                dirty=True,
            ),
        )
        self.assertEqual(experiment2.project.name, experiment0.project.name)
        self.assertEqual(experiment2.project.id, experiment0.project.id)
        self.assertEqual(experiment2.base_exp_id, experiment1.id)
        self.assertEqual(experiment2.repo_info["commit"], "foobar")
        self.assertEqual(experiment2.repo_info["dirty"], True)

        experiment3 = braintrust.init(project_id=experiment2.project.id, experiment=experiment2.name, open=True)
        self.assertEqual(experiment3.id, experiment2.id)

    def test_dataset_explicit_creation(self):
        dataset0 = braintrust.init_dataset("p", "foo")
        self.assertEqual(dataset0.project.name, "p")
        self.assertEqual(dataset0.name, "foo")
        dataset1 = braintrust.init_dataset(
            name="something",
            project_id=dataset0.project.id,
        )
        self.assertEqual(dataset1.project.name, dataset0.project.name)
        self.assertEqual(dataset1.project.id, dataset0.project.id)
        self.assertEqual(dataset1.name, "something")
