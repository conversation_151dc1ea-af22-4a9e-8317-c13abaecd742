import unittest
from typing import List

import requests
from pydantic import BaseModel

from tests.braintrust_app_test_base import BraintrustAppTestBase


def make_url(path):
    # Constants should match the env vars in
    # services/bt_services.py:_api_ts_compute_extra_args_kwargs.
    return f"http://localhost:8790/{path}"


def make_brainstore_url(path):
    return f"http://localhost:4000/{path}"


class SystemStatsSchema(BaseModel):
    class Postgres(BaseModel):
        class PoolProperties(BaseModel):
            totalCount: int
            idleCount: int
            waitingCount: int

        connectionUrl: str
        poolProperties: PoolProperties

    postgres: List[Postgres]


class HealthServerTest(unittest.TestCase):
    def test_system_stats(self):
        resp = requests.get(make_url("system-stats"))
        self.assertTrue(resp.ok, resp.text)
        SystemStatsSchema.model_validate_json(resp.text)

    def test_healthcheck_skip_realtime(self):
        resp = requests.get(make_url("status"))
        self.assertTrue(resp.ok, resp.text)
        for service, status in resp.json().items():
            if "bucket" in service.lower() and BraintrustAppTestBase.skip_s3():
                continue
            if "brainstore" in service.lower() and BraintrustAppTestBase.skip_brainstore():
                continue
            if "realtime" in service.lower():
                continue
            if "clickhouse" in service.lower():
                continue
            self.assertTrue(status["success"], f"{service} is not healthy: {status}")

    def test_healthcheck_realtime(self):
        resp = requests.get(make_url("status"))
        self.assertTrue(resp.ok, resp.text)
        for service, status in resp.json().items():
            if "realtime" not in service.lower():
                continue
            self.assertTrue(status["success"], f"{service} is not healthy: {status}")

    def test_brainstore_healthcheck(self):
        if BraintrustAppTestBase.skip_brainstore():
            raise unittest.SkipTest("Brainstore is not enabled")

        resp = requests.get(make_brainstore_url("status"))
        self.assertTrue(resp.ok, resp.text)
        data = resp.json()
        self.assertEqual(data["status"], "ok")
        for service, status in data["services"].items():
            self.assertTrue(status["success"], f"{service} is not healthy: {status}")
