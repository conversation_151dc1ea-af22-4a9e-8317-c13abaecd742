import braintrust
import requests
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class BadRequestErrorsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

    def test_error_logging(self):
        logger = braintrust.init_logger(project="error")

        with self.assertRaises(ValueError):
            with logger.start_span("root") as span:
                span.log(input=1)
                with span.start_span("child"):
                    raise ValueError("foo")

        with logger.start_span("root") as span:
            span.log(input=2)
            try:
                with span.start_span("child"):
                    raise ValueError("foo")
            except ValueError as e:
                span.log(output=2)

        braintrust.flush()

        logger_rows = get_object_json("project_logs", logger.id)

        input_1 = [row for row in logger_rows if row["input"] == 1][0]
        input_2 = [row for row in logger_rows if row["input"] == 2][0]

        self.assertIsNotNone(input_1.get("error"))
        self.assertIsNone(input_2.get("error"))
