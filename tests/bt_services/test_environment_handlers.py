import json
from uuid import uuid4

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url


def make_environment_url(*args):
    """Create URL for environment endpoints (non-v1)"""
    ret = f"{LOCAL_API_URL}/environment"
    for arg in args:
        ret += f"/{arg}"
    return ret


class EnvironmentHandlersTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

    def test_create_environment(self):
        """Test creating a new environment"""
        environment_data = {"name": "Development", "slug": "dev", "description": "Development environment"}

        resp = self.run_request("post", make_environment_url(), json=environment_data)
        environment = resp.json()

        self.assertEqual(environment["name"], "Development")
        self.assertEqual(environment["slug"], "dev")
        self.assertEqual(environment["description"], "Development environment")
        self.assertEqual(environment["org_id"], self.org_id)
        self.assertIsNotNone(environment["id"])
        self.assertIsNotNone(environment["created"])
        self.assertIsNone(environment["deleted_at"])

    def test_create_environment_missing_required_fields(self):
        """Test creating environment without required fields fails"""
        # Missing name
        resp = self.run_request("post", make_environment_url(), json={"slug": "dev"}, expect_error=True)
        self.assertGreaterEqual(resp.status_code, 400)

        # Missing slug
        resp = self.run_request("post", make_environment_url(), json={"name": "Development"}, expect_error=True)
        self.assertGreaterEqual(resp.status_code, 400)

    def test_create_environment_duplicate_slug(self):
        """Test creating environment with duplicate slug fails"""
        environment_data = {"name": "Development", "slug": "dev", "description": "Development environment"}

        # Create first environment
        self.run_request("post", make_environment_url(), json=environment_data)

        # Try to create second environment with same slug
        duplicate_data = {"name": "Different Name", "slug": "dev", "description": "Different description"}
        resp = self.run_request("post", make_environment_url(), json=duplicate_data, expect_error=True)
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("slug already exists", resp.text)

    def test_read_environments(self):
        """Test reading all environments"""
        # Create multiple environments
        environments = [
            {"name": "Development", "slug": "dev", "description": "Dev env"},
            {"name": "Staging", "slug": "staging", "description": "Staging env"},
            {"name": "Production", "slug": "prod", "description": "Prod env"},
        ]

        created_environments = []
        for env_data in environments:
            resp = self.run_request("post", make_environment_url(), json=env_data)
            created_environments.append(resp.json())

        # Read all environments
        resp = self.run_request("get", make_environment_url())
        result = resp.json()

        self.assertIn("objects", result)
        self.assertEqual(len(result["objects"]), 3)

        # Should be ordered by created DESC
        returned_environments = result["objects"]
        self.assertEqual(returned_environments[0]["slug"], "prod")
        self.assertEqual(returned_environments[1]["slug"], "staging")
        self.assertEqual(returned_environments[2]["slug"], "dev")

    def test_read_environments_with_name_filter(self):
        """Test reading environments filtered by name"""
        # Create environments
        env1_data = {"name": "Development", "slug": "dev"}
        env2_data = {"name": "Production", "slug": "prod"}

        self.run_request("post", make_environment_url(), json=env1_data)
        self.run_request("post", make_environment_url(), json=env2_data)

        # Filter by name
        resp = self.run_request("get", make_environment_url(), params={"name": "Development"})
        result = resp.json()

        self.assertEqual(len(result["objects"]), 1)
        self.assertEqual(result["objects"][0]["name"], "Development")

    def test_read_environments_with_ids_filter(self):
        """Test reading environments filtered by IDs"""
        # Create environments
        env1_resp = self.run_request("post", make_environment_url(), json={"name": "Dev", "slug": "dev"})
        env2_resp = self.run_request("post", make_environment_url(), json={"name": "Prod", "slug": "prod"})
        env3_resp = self.run_request("post", make_environment_url(), json={"name": "Test", "slug": "test"})

        env1_id = env1_resp.json()["id"]
        env2_id = env2_resp.json()["id"]

        # Filter by single ID
        resp = self.run_request("get", make_environment_url(), params={"ids": env1_id})
        result = resp.json()
        self.assertEqual(len(result["objects"]), 1)
        self.assertEqual(result["objects"][0]["id"], env1_id)

        # Filter by multiple IDs
        resp = self.run_request("get", make_environment_url(), params={"ids": [env1_id, env2_id]})
        result = resp.json()
        self.assertEqual(len(result["objects"]), 2)
        returned_ids = {env["id"] for env in result["objects"]}
        self.assertEqual(returned_ids, {env1_id, env2_id})

    def test_read_environment_by_id(self):
        """Test reading a specific environment by ID"""
        # Create environment
        environment_data = {"name": "Development", "slug": "dev", "description": "Dev environment"}
        create_resp = self.run_request("post", make_environment_url(), json=environment_data)
        created_env = create_resp.json()

        # Read by ID
        resp = self.run_request("get", make_environment_url(created_env["id"]))
        environment = resp.json()

        self.assertEqual(environment["id"], created_env["id"])
        self.assertEqual(environment["name"], "Development")
        self.assertEqual(environment["slug"], "dev")
        self.assertEqual(environment["description"], "Dev environment")

    def test_read_environment_by_id_not_found(self):
        """Test reading non-existent environment returns 404"""
        fake_id = str(uuid4())
        resp = self.run_request("get", make_environment_url(fake_id), expect_error=True)
        self.assertEqual(resp.status_code, 403)

    def test_update_environment_patch(self):
        """Test updating environment with PATCH"""
        # Create environment
        environment_data = {"name": "Development", "slug": "dev", "description": "Old description"}
        create_resp = self.run_request("post", make_environment_url(), json=environment_data)
        created_env = create_resp.json()

        # Update with PATCH
        update_data = {"name": "Updated Development", "description": "New description"}
        resp = self.run_request("patch", make_environment_url(created_env["id"]), json=update_data)
        updated_env = resp.json()

        self.assertEqual(updated_env["id"], created_env["id"])
        self.assertEqual(updated_env["name"], "Updated Development")
        self.assertEqual(updated_env["slug"], "dev")  # Should remain unchanged
        self.assertEqual(updated_env["description"], "New description")

    def test_update_environment_slug(self):
        """Test updating environment slug"""
        # Create environment
        create_resp = self.run_request("post", make_environment_url(), json={"name": "Dev", "slug": "dev"})
        created_env = create_resp.json()

        # Update slug
        resp = self.run_request("patch", make_environment_url(created_env["id"]), json={"slug": "development"})
        updated_env = resp.json()

        self.assertEqual(updated_env["slug"], "development")

    def test_update_environment_slug_duplicate(self):
        """Test updating environment slug to duplicate value fails"""
        # Create two environments
        self.run_request("post", make_environment_url(), json={"name": "Dev", "slug": "dev"})
        create_resp2 = self.run_request("post", make_environment_url(), json={"name": "Test", "slug": "test"})
        env2 = create_resp2.json()

        # Try to update second environment's slug to match first
        resp = self.run_request("patch", make_environment_url(env2["id"]), json={"slug": "dev"}, expect_error=True)
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("slug already exists", resp.text)

    def test_update_environment_no_fields(self):
        """Test updating environment with no fields fails"""
        # Create environment
        create_resp = self.run_request("post", make_environment_url(), json={"name": "Dev", "slug": "dev"})
        created_env = create_resp.json()

        # Try to update with no fields
        resp = self.run_request("patch", make_environment_url(created_env["id"]), json={}, expect_error=True)
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertIn("Must specify at least one field to update", resp.text)

    def test_delete_environment(self):
        """Test deleting environment (soft delete)"""
        # Create environment
        create_resp = self.run_request("post", make_environment_url(), json={"name": "Dev", "slug": "dev"})
        created_env = create_resp.json()

        # Delete environment
        resp = self.run_request("delete", make_environment_url(created_env["id"]))
        deleted_env = resp.json()

        self.assertEqual(deleted_env["id"], created_env["id"])
        self.assertIsNotNone(deleted_env["deleted_at"])

        # Should not appear in list anymore
        list_resp = self.run_request("get", make_environment_url())
        environments = list_resp.json()["objects"]
        self.assertEqual(len(environments), 0)

        # Should not be accessible by ID
        resp = self.run_request("get", make_environment_url(created_env["id"]), expect_error=True)
        self.assertEqual(resp.status_code, 403)

    def test_delete_environment_not_found(self):
        """Test deleting non-existent environment returns 404"""
        fake_id = str(uuid4())
        resp = self.run_request("delete", make_environment_url(fake_id), expect_error=True)
        self.assertEqual(resp.status_code, 403)

    def test_invalid_method(self):
        """Test invalid HTTP method returns error"""
        # Create environment first
        create_resp = self.run_request("post", make_environment_url(), json={"name": "Dev", "slug": "dev"})
        created_env = create_resp.json()

        # Try PUT method on environment ID endpoint (should fail)
        resp = self.run_request(
            "put", make_environment_url(created_env["id"]), json={"name": "Updated"}, expect_error=True
        )
        self.assertGreaterEqual(resp.status_code, 400)
        # Express returns "Cannot PUT" for unsupported methods
        self.assertIn("Cannot PUT", resp.text)

    def test_unauthorized_access(self):
        """Test access without proper permissions"""
        # Create an unprivileged user
        unprivileged_user_id, _, unprivileged_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        unprivileged_headers = {"Authorization": f"Bearer {unprivileged_api_key}"}

        # Try to create environment (should fail - needs update permission)
        resp = self.run_request(
            "post",
            make_environment_url(),
            json={"name": "Dev", "slug": "dev"},
            headers=unprivileged_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)

        # List should now SUCCEED (only needs org membership for read access)
        resp = self.run_request("get", make_environment_url(), headers=unprivileged_headers)
        result = resp.json()
        self.assertIn("objects", result)

    def test_cross_org_isolation(self):
        """Test that environments are isolated between organizations"""
        # Create environment in current org
        env_data = {"name": "Development", "slug": "dev"}
        self.run_request("post", make_environment_url(), json=env_data)

        # Create another org and user
        other_org_id, _ = self.createOrg()
        _, _, other_api_key = self.createUserInOrg(other_org_id)
        other_headers = {"Authorization": f"Bearer {other_api_key}"}

        # List environments from other org should be empty
        resp = self.run_request("get", make_environment_url(), headers=other_headers)
        result = resp.json()
        self.assertEqual(len(result["objects"]), 0)

        # Create environment in other org with same slug (should work)
        resp = self.run_request("post", make_environment_url(), json=env_data, headers=other_headers)
        other_env = resp.json()
        self.assertEqual(other_env["slug"], "dev")
        self.assertEqual(other_env["org_id"], other_org_id)

    def test_environment_with_minimal_data(self):
        """Test creating environment with only required fields"""
        environment_data = {"name": "Minimal", "slug": "minimal"}

        resp = self.run_request("post", make_environment_url(), json=environment_data)
        environment = resp.json()

        self.assertEqual(environment["name"], "Minimal")
        self.assertEqual(environment["slug"], "minimal")
        self.assertIsNone(environment["description"])
        self.assertEqual(environment["org_id"], self.org_id)

    def test_environment_description_update_to_null(self):
        """Test updating environment description to null"""
        # Create environment with description
        create_resp = self.run_request(
            "post",
            make_environment_url(),
            json={"name": "Dev", "slug": "dev", "description": "Original description"},
        )
        created_env = create_resp.json()

        # Update description to null
        resp = self.run_request("patch", make_environment_url(created_env["id"]), json={"description": None})
        updated_env = resp.json()

        self.assertIsNone(updated_env["description"])

    def test_cross_org_access_denial_by_id(self):
        """Test that user cannot access environment by ID from different organization"""
        # Create environment in current org
        env_data = {"name": "Development", "slug": "dev", "description": "Dev environment"}
        create_resp = self.run_request("post", make_environment_url(), json=env_data)
        created_env = create_resp.json()

        # Create another org and user
        other_org_id, _ = self.createOrg()
        _, _, other_api_key = self.createUserInOrg(other_org_id)
        other_headers = {"Authorization": f"Bearer {other_api_key}"}

        # Try to access the environment by ID from different org (should fail)
        resp = self.run_request(
            "get",
            make_environment_url(created_env["id"]),
            headers=other_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)

    def test_org_member_read_access(self):
        """Test that org members can read environments without explicit permissions"""
        # Create an unprivileged user (not in org owners)
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user_headers = {"Authorization": f"Bearer {user_api_key}"}

        # User should be able to access environments (org membership is sufficient for read)
        resp = self.run_request("get", make_environment_url(), headers=user_headers)
        result = resp.json()
        self.assertIn("objects", result)

        # Create an environment with privileged user first
        env_data = {"name": "Test Environment", "slug": "test-env", "description": "Test description"}
        create_resp = self.run_request("post", make_environment_url(), json=env_data)
        created_env = create_resp.json()

        # User should be able to read specific environment by ID (org membership sufficient)
        resp = self.run_request("get", make_environment_url(created_env["id"]), headers=user_headers)
        environment = resp.json()
        self.assertEqual(environment["id"], created_env["id"])

        # User should NOT be able to create environments (needs update permission)
        resp = self.run_request(
            "post",
            make_environment_url(),
            json={"name": "User Environment", "slug": "user-env"},
            headers=user_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)

        # Grant org_project update permissions to the user
        self.grant_acl(
            {"object_type": "org_project", "object_id": self.org_id, "user_id": user_id, "permission": "update"}
        )

        # Now user should be able to create environments
        user_env_data = {"name": "User Environment", "slug": "user-env", "description": "Created by user"}
        resp = self.run_request("post", make_environment_url(), json=user_env_data, headers=user_headers)
        user_env = resp.json()
        self.assertEqual(user_env["name"], "User Environment")
        self.assertEqual(user_env["slug"], "user-env")
        self.assertEqual(user_env["org_id"], self.org_id)

        # User should be able to update environments
        update_data = {"description": "Updated by user"}
        resp = self.run_request("patch", make_environment_url(user_env["id"]), json=update_data, headers=user_headers)
        updated_env = resp.json()
        self.assertEqual(updated_env["description"], "Updated by user")

        # User should be able to delete environments
        resp = self.run_request("delete", make_environment_url(user_env["id"]), headers=user_headers)
        deleted_env = resp.json()
        self.assertIsNotNone(deleted_env["deleted_at"])

    def test_non_org_member_access_denied(self):
        """Test that users who are not org members cannot read environments"""
        # Create environment in current org
        env_data = {"name": "Development", "slug": "dev"}
        create_resp = self.run_request("post", make_environment_url(), json=env_data)
        created_env = create_resp.json()

        # Create another org and user (user not in current org)
        other_org_id, _ = self.createOrg()
        _, _, other_api_key = self.createUserInOrg(other_org_id)
        other_headers = {"Authorization": f"Bearer {other_api_key}"}

        # User from different org should NOT be able to list environments
        resp = self.run_request("get", make_environment_url(), headers=other_headers)
        result = resp.json()
        self.assertEqual(len(result["objects"]), 0)

        # User from different org should NOT be able to access environment by ID
        resp = self.run_request(
            "get",
            make_environment_url(created_env["id"]),
            headers=other_headers,
            expect_error=True,
        )
        self.assertGreaterEqual(resp.status_code, 400)
        self.assertLess(resp.status_code, 500)
