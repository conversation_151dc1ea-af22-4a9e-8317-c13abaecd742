from uuid import uuid4

import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


class ExperimentOpenTest(BraintrustAppTestBase):
    def test_basic_open(self):
        experiment = braintrust.init("p", description="something cool")
        for i in range(10):
            experiment.log(input=i, output=i, scores={})
        experiment.flush()

        ro_experiment = braintrust.init("p", experiment=experiment.name, open=True)
        self.assertEqual(braintrust.current_experiment(), experiment)
        rows = list(ro_experiment)
        inputs = set([r["input"] for r in rows])
        outputs = set([r["output"] for r in rows])

        self.assertEqual(inputs, set(range(10)))
        self.assertEqual(outputs, set(range(10)))

        # update also allows you to read the data. It will not overwrite the
        # existing metadata.
        braintrust.init("p", experiment=experiment.name, description="something uncool", update=True)
        rw_experiment = braintrust.current_experiment()
        rows = list(rw_experiment)
        inputs = set([r["input"] for r in rows])
        outputs = set([r["output"] for r in rows])

        self.assertEqual(inputs, set(range(10)))
        self.assertEqual(outputs, set(range(10)))
        self.assertEqual(rw_experiment.description, "something cool")

    def test_broken_open(self):
        with self.assertRaises(ValueError) as e:
            list(braintrust.init("p", experiment=uuid4().hex, open=True).fetch())
        self.assertTrue("not found" in str(e.exception))

        exp = braintrust.init("p", experiment=uuid4().hex)
        with self.assertRaises(ValueError) as e:
            list(braintrust.init("q", experiment=exp.name, open=True).fetch())
        self.assertTrue("not found" in str(e.exception))

        with self.assertRaises(ValueError) as e:
            braintrust.init("p", experiment=uuid4().hex, open=True, update=True)
        self.assertTrue("Cannot" in str(e.exception))
