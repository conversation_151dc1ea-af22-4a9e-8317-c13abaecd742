import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class SdkLoggingTest(BraintrustAppTestBase):
    def test_sdk_logging(self):
        logger = braintrust.init_logger(project="sdk logging test")

        with logger.start_span("test_sdk_logging") as span:
            span_id = span.id
            fs = []
            with braintrust.TracedThreadPoolExecutor(max_workers=2) as e:
                fs.append(e.submit(braintrust.traced(lambda: 1)))
                fs.append(e.submit(braintrust.traced(lambda: 2)))
            for f in fs:
                f.result()
        logger.flush()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={"query": f"from: project_logs('{logger.project.id}') traces | select: * | filter: id='{span_id}'"},
        )
        spans = resp.json()["data"]
        self.assertEqual(len(spans), 3)
