import braintrust
import requests
from braintrust.http_headers import BT_FOUND_EXISTING_HEADER

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def make_url(object_type, id=None, action=None):
    ret = f"{LOCAL_API_URL}/v1/{object_type}"
    if id:
        ret += f"/{id}"
    if action:
        ret += f"/{action}"
    return ret


def get_request_headers(api_key):
    return dict(Authorization=f"Bearer {api_key}")


def make_comparable_view(view, omit_id=False):
    fields_to_omit = {"created", "deleted_at", "user_id"}
    if omit_id:
        fields_to_omit.add("id")
    return {k: v for k, v in view.items() if k not in fields_to_omit}


class ViewTest(BraintrustAppTestBase):
    def _checked_request(self, verb, api_key, url, json=None, expect_error=False, expect_conflict=False):
        if verb == "get":
            kwargs = {"params": json}
        else:
            kwargs = {"json": json}
        resp = getattr(requests, verb)(url, **{"headers": get_request_headers(api_key), **kwargs})
        msg = f"request json: {json}"
        if expect_error:
            self.assertFalse(resp.ok, msg=msg)
        else:
            self.assertTrue(resp.ok, msg=f"request json: {json}, status code {resp.status_code}, error: {resp.text}")
        if expect_conflict:
            self.assertIn(BT_FOUND_EXISTING_HEADER, resp.headers, msg=msg)
        else:
            self.assertNotIn(BT_FOUND_EXISTING_HEADER, resp.headers, msg=msg)
        return resp

    def _assert_views_equal(self, view1, view2, omit_id=False, msg=None):
        self.assertEqual(
            make_comparable_view(view1, omit_id=omit_id),
            make_comparable_view(view2, omit_id=omit_id),
            msg=msg,
        )

    def _assert_view_lists_equal(self, views1, views2, omit_id=False, msg=None):
        self.assertEqual(
            [make_comparable_view(v, omit_id=omit_id) for v in views1],
            [make_comparable_view(v, omit_id=omit_id) for v in views2],
            msg=msg,
        )

    def _test_permissions_helper(self, parent_object_type):
        parent_object_spec = dict(object_type=parent_object_type)
        project, view_type = None, None
        if parent_object_type == "org_project":
            parent_object_spec["object_id"] = self.org_id
            view_type = "projects"
        else:
            project = self._checked_request(
                "post", self.org_api_key, make_url("project"), json=dict(name="test")
            ).json()
            if parent_object_type == "project":
                parent_object_spec["object_id"] = project["id"]
                view_type = "logs"
            elif parent_object_type == "experiment":
                experiment = self._checked_request(
                    "post", self.org_api_key, make_url("experiment"), json=dict(name="test", project_id=project["id"])
                ).json()
                parent_object_spec["object_id"] = experiment["id"]
                view_type = "experiment"
            elif parent_object_type == "dataset":
                dataset = self._checked_request(
                    "post", self.org_api_key, make_url("dataset"), json=dict(name="test", project_id=project["id"])
                ).json()
                parent_object_spec["object_id"] = dataset["id"]
                view_type = "dataset"
            else:
                raise ValueError(f"Unknown parent object type {parent_object_type}")

        msg = f"parent object {parent_object_spec}"
        view = self._checked_request(
            "post",
            self.org_api_key,
            make_url("view"),
            json=dict(**parent_object_spec, view_type=view_type, name="test"),
        ).json()
        get_views_resp = self._checked_request(
            "get",
            self.org_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self._assert_view_lists_equal(get_views_resp["objects"], [view], msg=msg)
        get_view_resp = self._checked_request(
            "get",
            self.org_api_key,
            make_url("view", id=view["id"]),
            json=parent_object_spec,
        ).json()
        self._assert_views_equal(get_view_resp, view, msg=msg)
        patch_view = self._checked_request(
            "patch",
            self.org_api_key,
            make_url("view", id=view["id"]),
            json=dict(**parent_object_spec, name="test2"),
        ).json()
        self.assertEqual(patch_view["name"], "test2", msg=msg)
        delete_view = self._checked_request(
            "delete",
            self.org_api_key,
            make_url("view", id=view["id"]),
            json=parent_object_spec,
        ).json()
        self.assertEqual(delete_view["name"], "test2", msg=msg)

        # Insert a new view for the upcoming RBAC tests.
        view2 = self._checked_request(
            "post",
            self.org_api_key,
            make_url("view"),
            json=dict(**parent_object_spec, view_type=view_type, name="test2"),
        ).json()

        # A user with no project permissions should not be able to read or create a
        # view within the project.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        # parent_object_spec = dict(object_type="project", object_id=project["id"])

        get_views_restricted = self._checked_request(
            "get",
            user_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self.assertEqual(get_views_restricted, dict(objects=[]), msg=msg)
        self._checked_request(
            "get",
            user_api_key,
            make_url("view", id=view2["id"]),
            json=parent_object_spec,
            expect_error=True,
        )

        # Grant the user read access to the project. They should be able to read the view now,
        # but should still not be able to create, update, or delete views under this project.
        self.grant_acl(
            dict(
                **parent_object_spec,
                user_id=user_id,
                permission="read",
                restrict_object_type=parent_object_spec["object_type"],
            ),
        )
        get_views = self._checked_request(
            "get",
            user_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [view2], msg=msg)
        get_view = self._checked_request(
            "get",
            user_api_key,
            make_url("view", id=view2["id"]),
            json=parent_object_spec,
        ).json()
        self._assert_views_equal(get_view, view2, msg=msg)

        self._checked_request(
            "post",
            user_api_key,
            make_url("view"),
            json=dict(**parent_object_spec, view_type=view_type, name="test"),
            expect_error=True,
        )
        self._checked_request(
            "post",
            user_api_key,
            make_url("view"),
            json=dict(**parent_object_spec, view_type=view_type, name="test2"),
            expect_error=True,
        )
        self._checked_request(
            "patch",
            user_api_key,
            make_url("view", id=view2["id"]),
            json=dict(**parent_object_spec, name="test3"),
            expect_error=True,
        )
        self._checked_request(
            "delete",
            user_api_key,
            make_url("view", id=view2["id"]),
            json=parent_object_spec,
            expect_error=True,
        )

        # Grant the user update access to the project.
        self.grant_acl(
            dict(
                **parent_object_spec,
                user_id=user_id,
                permission="update",
                restrict_object_type=parent_object_spec["object_type"],
            ),
        )
        view3 = self._checked_request(
            "post",
            user_api_key,
            make_url("view"),
            json=dict(**parent_object_spec, view_type=view_type, name="test3"),
        ).json()
        get_views = self._checked_request(
            "get",
            user_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [view3, view2], msg=msg)
        self._checked_request(
            "get",
            user_api_key,
            make_url("view", id=view3["id"]),
            json=parent_object_spec,
        )
        # Should conflict
        self._checked_request(
            "patch",
            user_api_key,
            make_url("view", id=view3["id"]),
            json=dict(**parent_object_spec, name="test2"),
            expect_error=True,
        )
        # Should not conflict
        self._checked_request(
            "patch",
            user_api_key,
            make_url("view", id=view3["id"]),
            json=dict(**parent_object_spec, name="test"),
        )
        self._checked_request(
            "delete",
            user_api_key,
            make_url("view", id=view3["id"]),
            json=parent_object_spec,
        )
        get_views = self._checked_request(
            "get",
            user_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [view2], msg=msg)

        # Delete the objects we created.
        if project is None:
            self._checked_request(
                "delete", self.org_api_key, make_url("view", id=view2["id"]), json=parent_object_spec
            )
        else:
            self._checked_request("delete", self.org_api_key, make_url("project", project["id"]))
        # self.assertEqual(list_all_objects(), {})
        get_views = self._checked_request(
            "get",
            self.org_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [], msg=msg)

    def test_organization_view_permissions(self):
        self._test_permissions_helper("org_project")

    def test_project_view_permissions(self):
        self._test_permissions_helper("project")

    def test_experiment_view_permissions(self):
        self._test_permissions_helper("experiment")

    def test_dataset_view_permissions(self):
        self._test_permissions_helper("dataset")

    def test_updates(self):
        project = self._checked_request("post", self.org_api_key, make_url("project"), json=dict(name="test")).json()
        parent_object_spec = dict(object_type="project", object_id=project["id"])

        # Insert `tomato` and ensure the returned view is identical (sans `id` and `created`).
        tomato_def = dict(
            **parent_object_spec,
            view_type="dataset",
            name="red",
            view_data=dict(search={"filter": ["name = 'tomato'"]}),
            options=dict(columnVisibility={"id": False}),
        )
        tomato = self._checked_request(
            "post",
            self.org_api_key,
            make_url("view"),
            json=tomato_def,
        ).json()
        self._assert_views_equal(tomato, tomato_def, omit_id=True)

        # Insert a view with the same parent object, view_type, and name.
        # This request should conflict and return the existing view.
        cherry_def = dict(
            **parent_object_spec,
            view_type="dataset",
            name="red",
            view_data=dict(search={"filter": ["name = 'cherry'"]}),
            options=dict(columnVisibility={"id": True}),
        )
        cherry_post_resp = self._checked_request(
            "post",
            self.org_api_key,
            make_url("view"),
            json=cherry_def,
            expect_conflict=True,
        ).json()
        self._assert_views_equal(cherry_post_resp, tomato)

        # Insert a view with the same parent object and view_type but a different name.
        # This request should not conflict, so we should get the new view back.
        radish_def = dict(
            **parent_object_spec,
            view_type="experiments",
            name="red",
            view_data=dict(search={"filter": ["name = 'radish'"]}),
            options=dict(columnSizing={"0": 99}),
        )
        radish = self._checked_request(
            "post",
            self.org_api_key,
            make_url("view"),
            json=radish_def,
            expect_conflict=False,
        ).json()
        self._assert_views_equal(radish, radish_def, omit_id=True)

        # Run some GET requests with various filters to ensure they work as expected.
        get_views = self._checked_request("get", self.org_api_key, make_url("view"), json=parent_object_spec).json()
        self._assert_view_lists_equal(get_views["objects"], [radish, tomato])
        get_views = self._checked_request(
            "get", self.org_api_key, make_url("view"), json=dict(**parent_object_spec, view_type="experiments")
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [radish])
        get_views = self._checked_request(
            "get", self.org_api_key, make_url("view"), json=dict(**parent_object_spec, view_name="red")
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [radish, tomato])
        get_views = self._checked_request(
            "get", self.org_api_key, make_url("view"), json=dict(**parent_object_spec, view_name="purple")
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [])

        # Insert a conflicting view again but use PUT instead of POST.
        # We should get the new view back since PUT should overwrite the existing view.
        apple_def = dict(
            **parent_object_spec,
            view_type="dataset",
            name="red",
            view_data=dict(search={"filter": ["name = 'apple'"]}),
            options=dict(columnVisibility={"output": False}),
        )
        apple = self._checked_request(
            "put",
            self.org_api_key,
            make_url("view"),
            json=apple_def,
            expect_conflict=True,
        ).json()
        self._assert_views_equal(apple, apple_def, omit_id=True)
        get_views = self._checked_request(
            "get", self.org_api_key, make_url("view"), json=dict(**parent_object_spec)
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [radish, apple])

        # POST a view with a different name. This should not conflict.
        kale_def = dict(
            **parent_object_spec,
            view_type="dataset",
            name="green",
            view_data=dict(search={"filter": ["name = 'kale'"]}),
            options=dict(columnOrder=["1", "0"]),
        )
        kale = self._checked_request(
            "post",
            self.org_api_key,
            make_url("view"),
            json=kale_def,
        ).json()
        self._assert_views_equal(kale, kale_def, omit_id=True)

        # The following PATCH attempts to rename the view to "red" and change the `view_data`
        # and `options` fields. This operation should conflict with `apple` since the
        # `view_type` and `name` fields would be the same, so the PATCH should error.
        self._checked_request(
            "patch",
            self.org_api_key,
            make_url("view", id=kale["id"]),
            json=dict(
                **parent_object_spec,
                name="red",
                view_data=dict(search={"filter": ["expected = 'kale'"]}),
                options=dict(columnVisibility={"name": False}),
            ),
            expect_error=True,
        )
        # ...so the list of views should be unaffected.
        get_dataset_views = self._checked_request(
            "get",
            self.org_api_key,
            make_url("view"),
            json=dict(**parent_object_spec, view_type="dataset"),
        ).json()
        self._assert_view_lists_equal(get_dataset_views["objects"], [kale, apple])

        # PATCH the name but leave the other fields blank. `view_data` and `options`
        # should not be overwritten since they are omitted from the request.
        lime_def = dict(**kale_def)
        lime_def["name"] = "sour"
        lime = self._checked_request(
            "patch",
            self.org_api_key,
            make_url("view", id=kale["id"]),
            json=dict(
                **parent_object_spec,
                name=lime_def["name"],
            ),
        ).json()
        self._assert_views_equal(lime, lime_def, omit_id=True)

        # Insert blank objects into `view_data` and `options`.
        lemon_def = dict(**lime_def)
        lemon_def["view_type"] = "logs"
        lemon_def["view_data"] = dict()
        lemon_def["options"] = dict()
        lemon = self._checked_request(
            "patch",
            self.org_api_key,
            make_url("view", id=kale["id"]),
            json=dict(
                **parent_object_spec,
                view_type=lemon_def["view_type"],
                view_data=lemon_def["view_data"],
                options=lemon_def["options"],
            ),
        ).json()
        self._assert_views_equal(lemon, lemon_def, omit_id=True)

        # Repopulate `view_data` and `options` with new values.
        kiwi_def = dict(**lemon_def)
        kiwi_def["name"] = "kiwi"
        kiwi_def["view_type"] = "dataset"
        kiwi_def["view_data"] = dict(search={"filter": ["skin = 'fuzzy'"], "sort": ["num_examples DESC"]})
        kiwi_def["options"] = dict(
            columnSizing={"0": 100, "1": 50}, columnOrder=["0", "1"], columnVisibility={"1": False}
        )
        kiwi = self._checked_request(
            "patch",
            self.org_api_key,
            make_url("view", id=lime["id"]),
            json=kiwi_def,
        ).json()
        self._assert_views_equal(kiwi, kiwi_def, omit_id=True)

        # Confirm that the list of all views is as expected.
        get_views = self._checked_request(
            "get",
            self.org_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self._assert_view_lists_equal(get_views["objects"], [kiwi, radish, apple])

        # Delete the objects we created.
        self._checked_request("delete", self.org_api_key, make_url("project", project["id"]))
        get_views = self._checked_request(
            "get",
            self.org_api_key,
            make_url("view"),
            json=parent_object_spec,
        ).json()
        self.assertEqual(len(get_views["objects"]), 0)
