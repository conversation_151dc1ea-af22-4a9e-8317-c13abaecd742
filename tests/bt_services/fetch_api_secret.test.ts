import { describe, it, expect } from "vitest";
import { LOCAL_APP_URL, withAppDbClient } from "./setup";

describe("fetch /api/secret", () => {
  it("should process additionalHeaders with email template", async ({
    userEmail,
    orgId,
    orgName,
    orgApi<PERSON>ey,
  }) => {
    const secretId = await withAppDbClient(async (client) => {
      const result = await client.query(
        `INSERT INTO secrets.org_secrets(org_id, type, name, secret, metadata)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id`,
        [
          orgId,
          "openai",
          "OPENAI_API_KEY",
          "sk-test123abc",
          JSON.stringify({
            additionalHeaders: {
              "X-User-Email": "{{email}}",
            },
            customModels: {},
          }),
        ],
      );
      return result.rows[0].id;
    });

    const resp = await fetch(`${LOCAL_APP_URL}/api/secret`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${orgApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o",
        org_name: orgName,
        mode: "full",
      }),
    });
    expect(await resp.json()).toEqual([
      {
        user_email: userEmail,
        org_name: orgName,
        id: secretId,
        type: "openai",
        name: "OPENAI_API_KEY",
        secret: "sk-test123abc",
        metadata: {
          additionalHeaders: {
            "X-User-Email": userEmail,
          },
          customModels: {},
        },
      },
    ]);
  });

  it("should handle missing additionalHeaders", async ({
    userEmail,
    orgId,
    orgName,
    orgApiKey,
  }) => {
    const secretId = await withAppDbClient(async (client) => {
      const result = await client.query(
        `INSERT INTO secrets.org_secrets(org_id, type, name, secret, metadata)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id`,
        [
          orgId,
          "openai",
          "OPENAI_API_KEY",
          "sk-test123abc",
          JSON.stringify({
            customModels: {},
          }),
        ],
      );
      return result.rows[0].id;
    });
    const resp = await fetch(`${LOCAL_APP_URL}/api/secret`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${orgApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o",
        org_name: orgName,
        mode: "full",
      }),
    });

    expect(await resp.json()).toEqual([
      {
        user_email: userEmail,
        org_name: orgName,
        id: secretId,
        type: "openai",
        name: "OPENAI_API_KEY",
        secret: "sk-test123abc",
        metadata: {
          customModels: {},
        },
      },
    ]);
  });

  it("should handle missing metadata", async ({
    userEmail,
    orgId,
    orgName,
    orgApiKey,
  }) => {
    const secretId = await withAppDbClient(async (client) => {
      const result = await client.query(
        `INSERT INTO secrets.org_secrets(org_id, type, name, secret)
         VALUES ($1, $2, $3, $4)
         RETURNING id`,
        [orgId, "openai", "OPENAI_API_KEY", "sk-test123abc"],
      );
      return result.rows[0].id;
    });
    const resp = await fetch(`${LOCAL_APP_URL}/api/secret`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${orgApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o",
        org_name: orgName,
        mode: "full",
      }),
    });

    expect(await resp.json()).toEqual([
      {
        user_email: userEmail,
        org_name: orgName,
        id: secretId,
        type: "openai",
        name: "OPENAI_API_KEY",
        secret: "sk-test123abc",
      },
    ]);
  });

  it("should handle non-full mode response", async ({
    orgId,
    orgName,
    orgApiKey,
  }) => {
    await withAppDbClient(async (client) => {
      await client.query(
        `INSERT INTO secrets.org_secrets(org_id, type, name, secret, metadata)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id`,
        [
          orgId,
          "openai",
          "OPENAI_API_KEY",
          "sk-test123abc",
          JSON.stringify({
            additionalHeaders: {
              "X-User-Email": "{{email}}",
            },
          }),
        ],
      );
    });
    const resp = await fetch(`${LOCAL_APP_URL}/api/secret`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${orgApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o",
        org_name: orgName,
        mode: "default",
      }),
    });

    expect(await resp.json()).toEqual({
      secret: "sk-test123abc",
    });
  });

  it("should handle no matching secrets", async ({ orgApiKey, orgName }) => {
    const resp = await fetch(`${LOCAL_APP_URL}/api/secret`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${orgApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o",
        org_name: orgName,
        mode: "full",
      }),
    });

    expect(await resp.json()).toEqual([]);
  });
});
