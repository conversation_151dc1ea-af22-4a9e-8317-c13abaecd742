import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class OrgPermissionActionsTest(BraintrustAppTestBase):
    def get_org_permissions(self, api_key, user_object_type, user_group_id, expect_error=False):
        resp = self.run_server_action(
            api_key=api_key,
            function_name="getOrgPermissions",
            function_args=dict(orgId=self.org_id, userObjectType=user_object_type, userGroupId=user_group_id),
            expect_error=expect_error,
        )
        if not expect_error:
            return resp.json()

    def test_org_owner_permissions(self):
        res = self.get_org_permissions(self.org_api_key, "user", self.user_id)
        for key, checkbox in res["project"].items():
            self.assertTrue(checkbox["is_checked"], key)
            self.assertFalse(checkbox["is_mutable"], key)
            self.assertFalse(checkbox["has_partial_permissions"], key)
            self.assertEqual(checkbox["roles"], ["Owner"])
            self.assertEqual(checkbox["inherited_groups"], ["Owners"])
            self.assertEqual(checkbox["inherited_parent_objects"], ["organization"])
            self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])
        for key, checkbox in res["organization"].items():
            self.assertTrue(checkbox["is_checked"], key)
            self.assertFalse(checkbox["is_mutable"], key)
            self.assertFalse(checkbox["has_partial_permissions"], key)
            self.assertEqual(checkbox["roles"], ["Owner"])
            self.assertEqual(checkbox["inherited_groups"], ["Owners"])
            self.assertEqual(checkbox["inherited_parent_objects"], [])
            self.assertTrue(checkbox["unrestricted_grant_for_restricted_permission"])

    def test_unprivileged_user_permissions(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # If the unprivileged user makes the request, it should fail.
        self.get_org_permissions(user_api_key, "user", user_id, expect_error=True)

        # They should have no permissions.
        res = self.get_org_permissions(self.org_api_key, "user", user_id)
        for outerKey, data in res.items():
            for key, checkbox in data.items():
                self.assertFalse(checkbox["is_checked"], key)
                self.assertTrue(checkbox["is_mutable"], key)
                self.assertFalse(checkbox["has_partial_permissions"], key)
                self.assertEqual(checkbox["roles"], [])
                self.assertEqual(checkbox["inherited_groups"], [])
                self.assertEqual(checkbox["inherited_parent_objects"], [])
                self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

        # Grant them partial permission on manage organization access.
        self.grant_acl(
            dict(
                object_type="organization",
                object_id=self.org_id,
                user_id=user_id,
                permission="create_acls",
            ),
        )
        res = self.get_org_permissions(self.org_api_key, "user", user_id)
        checkbox = res["organization"]["manageAccess"]
        self.assertFalse(checkbox["is_checked"])
        self.assertFalse(checkbox["is_mutable"])
        self.assertTrue(checkbox["has_partial_permissions"])
        self.assertEqual(checkbox["roles"], [])
        self.assertEqual(checkbox["inherited_groups"], [])
        self.assertEqual(checkbox["inherited_parent_objects"], [])
        self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

        # Grant them the remaining permissions.
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=(
                    [
                        *[
                            dict(
                                object_type="organization",
                                object_id=self.org_id,
                                user_id=user_id,
                                permission=p,
                            )
                            for p in ["read_acls", "update_acls", "delete_acls"]
                        ],
                        *[
                            dict(
                                object_type="organization",
                                object_id=self.org_id,
                                user_id=user_id,
                                permission=p,
                                restrict_object_type="group",
                            )
                            for p in [
                                "create",
                                "read",
                                "update",
                                "delete",
                            ]
                        ],
                    ]
                )
            ),
        )
        res = self.get_org_permissions(self.org_api_key, "user", user_id)
        checkbox = res["organization"]["manageAccess"]
        self.assertTrue(checkbox["is_checked"])
        self.assertTrue(checkbox["is_mutable"])
        self.assertFalse(checkbox["has_partial_permissions"])

        # Remove their direct permission grants and add it through the 'Owner'
        # role.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select id from roles where org_id is null and name='Owner'")
                owner_role_id = cursor.fetchone()[0]
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=[
                    dict(object_type="organization", object_id=self.org_id, user_id=user_id, role_id=owner_role_id)
                ],
                remove_acls=[
                    dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission=p)
                    for p in ["create_acls", "read_acls", "update_acls", "delete_acls"]
                ],
            ),
        )
        res = self.get_org_permissions(self.org_api_key, "user", user_id)
        checkbox = res["organization"]["manageAccess"]
        self.assertTrue(checkbox["is_checked"])
        self.assertFalse(checkbox["is_mutable"])
        self.assertFalse(checkbox["has_partial_permissions"])
        self.assertEqual(checkbox["roles"], ["Owner"])
        self.assertEqual(checkbox["inherited_groups"], [])
        self.assertEqual(checkbox["inherited_parent_objects"], [])
        self.assertTrue(checkbox["unrestricted_grant_for_restricted_permission"])

    def test_group_permissions(self):
        group = self.run_request("post", f"{LOCAL_API_URL}/v1/group", json=dict(name="test_group")).json()

        # They should have no permissions.
        res = self.get_org_permissions(self.org_api_key, "group", group["id"])
        for outerKey, data in res.items():
            for key, checkbox in data.items():
                self.assertFalse(checkbox["is_checked"], key)
                self.assertTrue(checkbox["is_mutable"], key)
                self.assertFalse(checkbox["has_partial_permissions"], key)
                self.assertEqual(checkbox["roles"], [])
                self.assertEqual(checkbox["inherited_groups"], [])
                self.assertEqual(checkbox["inherited_parent_objects"], [])
                self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

        # Add a permission.
        self.grant_acl(
            dict(object_type="org_project", object_id=self.org_id, group_id=group["id"], permission="read"),
        )
        res = self.get_org_permissions(self.org_api_key, "group", group["id"])
        checkbox = res["project"]["read"]
        self.assertTrue(checkbox["is_checked"])
        self.assertTrue(checkbox["is_mutable"])
        self.assertFalse(checkbox["has_partial_permissions"])
        self.assertEqual(checkbox["roles"], [])
        self.assertEqual(checkbox["inherited_groups"], [])
        self.assertEqual(checkbox["inherited_parent_objects"], [])
        self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

    def test_org_projects_permissions(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Grant them restricted permission on 'org_projects' (basically
        # useless).
        self.grant_acl(
            dict(
                object_type="org_project",
                object_id=self.org_id,
                user_id=user_id,
                permission="read",
                restrict_object_type="org_project",
            ),
        )
        res = self.get_org_permissions(self.org_api_key, "user", user_id)
        checkbox = res["project"]["read"]
        self.assertFalse(checkbox["is_checked"])
        self.assertTrue(checkbox["is_mutable"])
        self.assertFalse(checkbox["has_partial_permissions"])
        self.assertEqual(checkbox["roles"], [])
        self.assertEqual(checkbox["inherited_groups"], [])
        self.assertEqual(checkbox["inherited_parent_objects"], [])
        self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])


class ProjectPermissionActionsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="test")).json()
        self.project_id = self.project["id"]

    def get_object_permissions(self, api_key, user_object_type, user_group_id, expect_error=False):
        resp = self.run_server_action(
            api_key=api_key,
            function_name="getProjectPermissions",
            function_args=dict(projectId=self.project_id, userObjectType=user_object_type, userGroupId=user_group_id),
            expect_error=expect_error,
        )
        if not expect_error:
            return resp.json()

    def test_org_owner_permissions(self):
        res = self.get_object_permissions(self.org_api_key, "user", self.user_id)
        for object_type in ["experiment", "dataset", "prompt", "prompt_session"]:
            for key, checkbox in res[object_type].items():
                self.assertTrue(checkbox["is_checked"], key)
                self.assertFalse(checkbox["is_mutable"], key)
                self.assertFalse(checkbox["has_partial_permissions"], key)
                self.assertEqual(checkbox["roles"], ["Owner"])
                self.assertEqual(checkbox["inherited_groups"], ["Owners"])
                self.assertEqual(checkbox["inherited_parent_objects"], ["organization"])
                self.assertTrue(checkbox["unrestricted_grant_for_restricted_permission"])

    def test_unprivileged_user_permissions(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # If the unprivileged user makes the request, it should error.
        self.get_object_permissions(user_api_key, "user", user_id, expect_error=True)

        # They should have no permissions.
        res = self.get_object_permissions(self.org_api_key, "user", user_id)
        for outerKey, data in res.items():
            for key, checkbox in data.items():
                self.assertFalse(checkbox["is_checked"], key)
                self.assertTrue(checkbox["is_mutable"], key)
                self.assertFalse(checkbox["has_partial_permissions"], key)
                self.assertEqual(checkbox["roles"], [])
                self.assertEqual(checkbox["inherited_groups"], [])
                self.assertEqual(checkbox["inherited_parent_objects"], [])
                self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

        # Grant them permissions on a few object types.
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=[
                    dict(
                        object_type="project",
                        object_id=self.project_id,
                        user_id=user_id,
                        permission="create",
                        restrict_object_type="experiment",
                    ),
                    dict(
                        object_type="project",
                        object_id=self.project_id,
                        user_id=user_id,
                        permission="read",
                    ),
                    dict(
                        object_type="project",
                        object_id=self.project_id,
                        user_id=user_id,
                        permission="create_acls",
                        restrict_object_type="dataset",
                    ),
                ],
            ),
        )

        res = self.get_object_permissions(self.org_api_key, "user", user_id)
        for object_type in res.keys():
            for key, checkbox in res[object_type].items():
                if object_type == "experiment" and key == "create":
                    self.assertTrue(checkbox["is_checked"], key)
                    self.assertTrue(checkbox["is_mutable"], key)
                    self.assertFalse(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], [])
                    self.assertEqual(checkbox["inherited_groups"], [])
                    self.assertEqual(checkbox["inherited_parent_objects"], [])
                    self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])
                elif object_type == "dataset" and key == "manageAccess":
                    self.assertFalse(checkbox["is_checked"], key)
                    self.assertFalse(checkbox["is_mutable"], key)
                    self.assertTrue(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], [])
                    self.assertEqual(checkbox["inherited_groups"], [])
                    self.assertEqual(checkbox["inherited_parent_objects"], [])
                    self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])
                elif key == "read":
                    self.assertTrue(checkbox["is_checked"], key)
                    self.assertFalse(checkbox["is_mutable"], key)
                    self.assertFalse(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], [])
                    self.assertEqual(checkbox["inherited_groups"], [])
                    self.assertEqual(checkbox["inherited_parent_objects"], [])
                    self.assertTrue(checkbox["unrestricted_grant_for_restricted_permission"])
                else:
                    self.assertFalse(checkbox["is_checked"], key)
                    self.assertTrue(checkbox["is_mutable"], key)
                    self.assertFalse(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], [])
                    self.assertEqual(checkbox["inherited_groups"], [])
                    self.assertEqual(checkbox["inherited_parent_objects"], [])
                    self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])


class ObjectPermissionsActionsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="test")).json()
        self.project_id = self.project["id"]

        self.experiment = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.project_id, name="test_exp")
        ).json()
        self.experiment_id = self.experiment["id"]
        self.dataset = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/dataset", json=dict(project_id=self.project_id, name="test_dat")
        ).json()
        self.dataset_id = self.dataset["id"]

    def get_object_type(self, object_id):
        return {
            self.experiment_id: "experiment",
            self.dataset_id: "dataset",
        }[object_id]

    def get_object_permissions(self, api_key, object_id, user_object_type, user_group_id, expect_error=False):
        resp = self.run_server_action(
            api_key=api_key,
            function_name="getObjectPermissions",
            function_args=dict(
                objectType=self.get_object_type(object_id),
                objectId=object_id,
                userObjectType=user_object_type,
                userGroupId=user_group_id,
            ),
            expect_error=expect_error,
        )
        if not expect_error:
            return resp.json()

    def test_org_owner_permissions(self):
        # Try out querying a separate org owner, so we can distinguish between
        # the one who created the parent project and one who didn't.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id)
        for query_user_id in [self.user_id, user_id]:
            query_api_key = {self.user_id: self.org_api_key, user_id: user_api_key}[query_user_id]
            for object_id in [self.experiment_id, self.dataset_id]:
                res = self.get_object_permissions(query_api_key, object_id, "user", query_user_id)
                for key, checkbox in res.items():
                    self.assertTrue(checkbox["is_checked"], key)
                    self.assertFalse(checkbox["is_mutable"], key)
                    self.assertFalse(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], ["Owner"])
                    self.assertEqual(checkbox["inherited_groups"], ["Owners"])
                    if query_user_id == self.user_id:
                        # We inherit from the project because we created the project
                        # too.
                        self.assertEqual(set(checkbox["inherited_parent_objects"]), {"organization", "project"})
                    else:
                        self.assertEqual(checkbox["inherited_parent_objects"], ["organization"])
                    self.assertTrue(checkbox["unrestricted_grant_for_restricted_permission"])

    def test_unprivileged_user_permissions(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        for object_id in [self.experiment_id, self.dataset_id]:
            object_type = self.get_object_type(object_id)

            # If the unprivileged user makes the request, it should error.
            self.get_object_permissions(user_api_key, object_id, "user", user_id, expect_error=True)

            # They should have no permissions.
            res = self.get_object_permissions(self.org_api_key, object_id, "user", user_id)
            for key, checkbox in res.items():
                self.assertFalse(checkbox["is_checked"], key)
                self.assertTrue(checkbox["is_mutable"], key)
                self.assertFalse(checkbox["has_partial_permissions"], key)
                self.assertEqual(checkbox["roles"], [])
                self.assertEqual(checkbox["inherited_groups"], [])
                self.assertEqual(checkbox["inherited_parent_objects"], [])
                self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

            # Grant them a few permissions.
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/v1/acl/batch_update",
                json=dict(
                    add_acls=[
                        dict(
                            object_type=object_type,
                            object_id=object_id,
                            user_id=user_id,
                            permission="create",
                            restrict_object_type=object_type,
                        ),
                        dict(
                            object_type=object_type,
                            object_id=object_id,
                            user_id=user_id,
                            permission="read",
                            restrict_object_type=object_type,
                        ),
                    ],
                ),
            )

            res = self.get_object_permissions(self.org_api_key, object_id, "user", user_id)
            for key, checkbox in res.items():
                if key in ["create", "read"]:
                    self.assertTrue(checkbox["is_checked"], key)
                    self.assertTrue(checkbox["is_mutable"], key)
                    self.assertFalse(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], [])
                    self.assertEqual(checkbox["inherited_groups"], [])
                    self.assertEqual(checkbox["inherited_parent_objects"], [])
                    self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])
                else:
                    self.assertFalse(checkbox["is_checked"], key)
                    self.assertTrue(checkbox["is_mutable"], key)
                    self.assertFalse(checkbox["has_partial_permissions"], key)
                    self.assertEqual(checkbox["roles"], [])
                    self.assertEqual(checkbox["inherited_groups"], [])
                    self.assertEqual(checkbox["inherited_parent_objects"], [])
                    self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

            # Grant them a few redundant permissions to grey out the checkbox.
            self.grant_acl(
                dict(
                    object_type="project",
                    object_id=self.project_id,
                    user_id=user_id,
                    permission="read",
                    restrict_object_type=object_type,
                ),
            )
            res = self.get_object_permissions(self.org_api_key, object_id, "user", user_id)
            checkbox = res["read"]
            self.assertTrue(checkbox["is_checked"], key)
            self.assertFalse(checkbox["is_mutable"], key)
            self.assertFalse(checkbox["has_partial_permissions"], key)
            self.assertEqual(checkbox["roles"], [])
            self.assertEqual(checkbox["inherited_groups"], [])
            self.assertEqual(checkbox["inherited_parent_objects"], ["project"])
            self.assertFalse(checkbox["unrestricted_grant_for_restricted_permission"])

            self.grant_acl(
                dict(object_type=object_type, object_id=object_id, user_id=user_id, permission="read"),
            )
            res = self.get_object_permissions(self.org_api_key, object_id, "user", user_id)
            checkbox = res["read"]
            self.assertTrue(checkbox["is_checked"], key)
            self.assertFalse(checkbox["is_mutable"], key)
            self.assertFalse(checkbox["has_partial_permissions"], key)
            self.assertEqual(checkbox["roles"], [])
            self.assertEqual(checkbox["inherited_groups"], [])
            self.assertEqual(checkbox["inherited_parent_objects"], ["project"])
            self.assertTrue(checkbox["unrestricted_grant_for_restricted_permission"])


class UserGroupWithObjectPermissionsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="test")).json()
        self.project_id = self.project["id"]

        self.user0_id, _, self.user0_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.user1_id, _, __ = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.user2_id, _, __ = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        self.group0 = self.run_request("post", f"{LOCAL_API_URL}/v1/group", json=dict(name="group0")).json()
        self.group0_id = self.group0["id"]
        self.group1 = self.run_request("post", f"{LOCAL_API_URL}/v1/group", json=dict(name="group1")).json()
        self.group1_id = self.group1["id"]

    def make_grant(self, **kwargs):
        return self.grant_acl(body=dict(object_type="project", object_id=self.project_id, **kwargs))

    def get_user_group_with_object_permissions(self, api_key, override_function_args=None):
        resp = self.run_server_action(
            api_key=api_key,
            function_name="getUserGroupWithObjectPermissions",
            function_args=override_function_args
            or dict(
                objectType="project",
                objectId=self.project_id,
                skipRestrictObjectTypeCheck=True,
            ),
        ).json()

        def list_to_set(x):
            if isinstance(x, list):
                return set(x)
            else:
                return x

        return {
            (x["user_object_type"], x["user_group_id"]): {
                k: list_to_set(v) for k, v in x.items() if k not in ["user_object_type", "user_group_id"]
            }
            for x in resp
        }

    def test_basic(self):
        # Add user0 to group0, and group0 containing group1. group0 gets a
        # direct grant on the object.
        self.run_request(
            "patch", f"{LOCAL_API_URL}/v1/group/{self.group0_id}", json=dict(add_member_users=[self.user0_id])
        )
        self.run_request(
            "patch", f"{LOCAL_API_URL}/v1/group/{self.group0_id}", json=dict(add_member_groups=[self.group1_id])
        )
        self.make_grant(group_id=self.group0_id, permission="read", restrict_object_type="experiment")

        # user1 gets a direct grant on the object.
        self.make_grant(user_id=self.user1_id, permission="read", restrict_object_type="dataset")

        # user2 gets both a direct grant and an unrestricted permission grant on
        # the object.
        self.make_grant(user_id=self.user2_id, permission="update", restrict_object_type="project_log")
        self.make_grant(user_id=self.user2_id, permission="update")

        # Querying with an unprivileged API key will return an empty result.
        items = self.get_user_group_with_object_permissions(self.user0_api_key)
        self.assertEqual(items, {})

        items = self.get_user_group_with_object_permissions(self.org_api_key)
        expected_items = {
            ("user", self.user_id): dict(
                has_direct_permission=False,
                roles={"Owner"},
                inherited_groups={"Owners"},
                inherited_parent_objects={"organization"},
                has_unrestricted_permissions=True,
            ),
            ("user", self.user0_id): dict(
                has_direct_permission=False,
                roles=set(),
                inherited_groups={self.group0["name"]},
                inherited_parent_objects=set(),
                has_unrestricted_permissions=False,
            ),
            ("user", self.user1_id): dict(
                has_direct_permission=True,
                roles=set(),
                inherited_groups=set(),
                inherited_parent_objects=set(),
                has_unrestricted_permissions=False,
            ),
            ("user", self.user2_id): dict(
                has_direct_permission=True,
                roles=set(),
                inherited_groups=set(),
                inherited_parent_objects=set(),
                has_unrestricted_permissions=True,
            ),
            ("group", self.group0_id): dict(
                has_direct_permission=True,
                roles=set(),
                inherited_groups=set(),
                inherited_parent_objects=set(),
                has_unrestricted_permissions=False,
            ),
            ("group", self.group1_id): dict(
                has_direct_permission=False,
                roles=set(),
                inherited_groups={self.group0["name"]},
                inherited_parent_objects=set(),
                has_unrestricted_permissions=False,
            ),
        }
        for key in expected_items.keys():
            self.assertEqual(items.get(key), expected_items[key], key)

    def test_subobject_direct_access(self):
        # Create a dataset underneath the project. Give user0 only access to
        # project-level data. Then query user/group permissions for the object.
        dataset = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/dataset", json=dict(name="test", project_id=self.project["id"])
        ).json()
        self.make_grant(user_id=self.user0_id, permission="read", restrict_object_type="project")
        items = self.get_user_group_with_object_permissions(
            self.org_api_key, override_function_args=dict(objectType="dataset", objectId=dataset["id"])
        )
        self.assertIn(("user", self.user_id), items)
        self.assertNotIn(("user", self.user0_id), items)

        # If we grant them all-datasets permission on the project, they should
        # show up as inherited.
        self.make_grant(user_id=self.user0_id, permission="read", restrict_object_type="dataset")
        items = self.get_user_group_with_object_permissions(
            self.org_api_key, override_function_args=dict(objectType="dataset", objectId=dataset["id"])
        )
        self.assertEqual(
            items.get(("user", self.user0_id)),
            dict(
                has_direct_permission=False,
                roles=set(),
                inherited_groups=set(),
                inherited_parent_objects={"project"},
                has_unrestricted_permissions=False,
            ),
        )

    def test_skip_restrict_object_type_check(self):
        # Give user0 access to datasets within the project. Then query
        # user/group permissions for the object. If we do include the "restrict
        # object type" check, they should not show up. But if we do skip it,
        # then they should show up.
        self.make_grant(user_id=self.user0_id, permission="read", restrict_object_type="dataset")
        items = self.get_user_group_with_object_permissions(
            self.org_api_key,
            override_function_args=dict(
                objectType="project", objectId=self.project["id"], skipRestrictObjectTypeCheck=False
            ),
        )
        self.assertIn(("user", self.user_id), items)
        self.assertNotIn(("user", self.user0_id), items)
        items = self.get_user_group_with_object_permissions(self.org_api_key)
        self.assertEqual(
            items.get(("user", self.user0_id)),
            dict(
                has_direct_permission=True,
                roles=set(),
                inherited_groups=set(),
                inherited_parent_objects=set(),
                has_unrestricted_permissions=False,
            ),
        )
