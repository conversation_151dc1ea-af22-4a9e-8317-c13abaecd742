from uuid import uuid4

import braintrust
import requests

from tests.braintrust_app_test_base import LOCAL_API_URL
from tests.bt_services.experiment_test_base import ExperimentTestBase

OPTIMIZATION_PROJECT_ID = "d341311b-2103-4065-a607-34f2263dd548"


class OptimizationLoggingTest(ExperimentTestBase):
    def test_optimization_logging(self):
        other_project = braintrust.init_logger(project="p")
        other_org_id, _ = self.createOrg()
        user_id, _, user_api_key = self.createUserInOrg(other_org_id)
        self.addUserToOrg(user_id, other_org_id)

        span_id = str(uuid4())
        event = {
            "id": str(uuid4()),
            "span_id": span_id,
            "root_span_id": span_id,
            "input": "a",
        }

        # Try logging in the optimization project and make sure it works
        resp = requests.post(
            f"{LOCAL_API_URL}/logs3",
            headers={"Authorization": f"Bearer {user_api_key}"},
            json={
                "rows": [
                    {
                        **event,
                        "project_id": OPTIMIZATION_PROJECT_ID,
                        "log_id": "g",
                    }
                ],
                "api_version": 2,
            },
        )
        self.assertTrue(resp.ok)

        # Try logging to the other project.
        resp = requests.post(
            f"{LOCAL_API_URL}/logs3",
            headers={"Authorization": f"Bearer {user_api_key}"},
            json={
                "rows": [
                    {
                        **event,
                        "project_id": other_project.project.id,
                        "log_id": "g",
                    }
                ],
                "api_version": 2,
            },
        )
        self.assertFalse(resp.ok)
        self.assertEqual(resp.status_code, 403)

        # Try reading logs from each project. Both should fail.
        for project_id in [OPTIMIZATION_PROJECT_ID, other_project.project.id]:
            resp = requests.post(
                f"{LOCAL_API_URL}/btql",
                headers={"Authorization": f"Bearer {user_api_key}"},
                json={"query": f"select: * | from: project_logs('{project_id}')"},
            )
            self.assertFalse(resp.ok)
            self.assertEqual(resp.status_code, 403)
