import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class MoveEntitiesTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.p0 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="p0")).json()
        self.p1 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="p1")).json()
        self.e0 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p0["id"], name="e0")
        ).json()
        self.e1 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p0["id"])
        ).json()
        self.e2 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p0["id"])
        ).json()

    def test_org_owner(self):
        # Should be able to move both experiments into the second project.
        resp = self.run_server_action(
            self.org_api_key,
            "moveEntities",
            dict(
                entityType="experiment",
                entityIds=[self.e0["id"], self.e1["id"]],
                orgName=self.org_name,
                entityName=self.p1["name"],
            ),
        ).json()
        self.assertEqual(resp, dict(status="success"))

        e0_new = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e0['id']}").json()
        self.assertEqual(e0_new["project_id"], self.p1["id"])
        e1_new = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e1['id']}").json()
        self.assertEqual(e1_new["project_id"], self.p1["id"])
        e2_new = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e2['id']}").json()
        self.assertEqual(e2_new["project_id"], self.p0["id"])

    def test_name_conflict(self):
        self.p2 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="p2")).json()

        # create an experiment in p2 with the same name (e0) as one in p0
        self.e3 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p2["id"], name="e0")
        ).json()

        # try to move e0 from p0 to p2
        resp = self.run_server_action(
            self.org_api_key,
            "moveEntities",
            dict(
                entityType="experiment",
                entityIds=[self.e0["id"]],
                orgName=self.org_name,
                entityName=self.p2["name"],
            ),
        ).json()
        self.assertEqual(resp["status"], "success")

        # Verify the moved experiment got renamed with project attribution due to conflict
        e0_new = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e0['id']}").json()
        self.assertEqual(e0_new["project_id"], self.p2["id"])
        self.assertEqual(e0_new["name"], "e0 (moved from p0)")

        # Verify the existing experiment kept its original name
        e3_after = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e3['id']}").json()
        self.assertEqual(e3_after["name"], "e0")

    def test_unprivileged_user(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        def do_move(expect_error):
            resp = self.run_server_action(
                user_api_key,
                "moveEntities",
                dict(
                    entityType="experiment",
                    entityIds=[self.e0["id"], self.e1["id"]],
                    orgName=self.org_name,
                    entityName=self.p1["name"],
                ),
            ).json()
            if expect_error:
                self.assertEqual(resp["status"], "error")
            else:
                self.assertEqual(resp, dict(status="success"))

        # At first should not succeed.
        do_move(expect_error=True)

        e0_update_acl = dict(object_type="experiment", object_id=self.e0["id"], user_id=user_id, permission="update")
        e1_update_acl = dict(object_type="experiment", object_id=self.e1["id"], user_id=user_id, permission="update")
        p1_create_acl = dict(object_type="project", object_id=self.p1["id"], user_id=user_id, permission="create")

        def grant_all_acls():
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/v1/acl/batch_update",
                json=dict(add_acls=[e0_update_acl, e1_update_acl, p1_create_acl]),
            )

        def remove_one_acl(acl):
            self.run_request("post", f"{LOCAL_API_URL}/v1/acl/batch_update", json=dict(remove_acls=[acl]))

        # After granting all ACLs, it should succeed.
        grant_all_acls()
        do_move(expect_error=False)

        # Remove each ACL at a time and make sure it fails.

        remove_one_acl(e0_update_acl)
        do_move(expect_error=True)
        grant_all_acls()

        remove_one_acl(e1_update_acl)
        do_move(expect_error=True)
        grant_all_acls()

        remove_one_acl(p1_create_acl)
        do_move(expect_error=True)
        grant_all_acls()

    def test_multiple_name_conflicts(self):
        # Create two projects
        self.p2 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="p2")).json()
        self.p1 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="p1")).json()

        # Create first "e0" in p2
        self.e3 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p2["id"], name="e0")
        ).json()

        # Create "e0" in p1 to create conflict
        self.e4 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p1["id"], name="e0")
        ).json()

        # Move e0 from p2 to p1 - should become "e0 (moved from p2)" due to conflict
        resp = self.run_server_action(
            self.org_api_key,
            "moveEntities",
            dict(
                entityType="experiment",
                entityIds=[self.e3["id"]],
                orgName=self.org_name,
                entityName=self.p1["name"],
            ),
        ).json()
        self.assertEqual(resp["status"], "success")

        # Verify first moved experiment got renamed with project attribution
        e3_new = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e3['id']}").json()
        self.assertEqual(e3_new["project_id"], self.p1["id"])
        self.assertEqual(e3_new["name"], "e0 (moved from p2)")

        # Create another "e0" in p2
        self.e5 = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=self.p2["id"], name="e0")
        ).json()

        # Move second e0 from p2 to p1 - should become "e0 {uuid} (moved from p2)" since "e0 (moved from p2)" exists
        resp = self.run_server_action(
            self.org_api_key,
            "moveEntities",
            dict(
                entityType="experiment",
                entityIds=[self.e5["id"]],
                orgName=self.org_name,
                entityName=self.p1["name"],
            ),
        ).json()
        self.assertEqual(resp["status"], "success")

        # Verify second moved experiment got renamed with UUID and project attribution
        e5_new = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e5['id']}").json()
        self.assertEqual(e5_new["project_id"], self.p1["id"])
        self.assertTrue(e5_new["name"].startswith("e0 "))
        self.assertTrue(" (moved from p2)" in e5_new["name"])
        # Check that there's an 8-character UUID between the name and project attribution
        name_parts = e5_new["name"].split(" (moved from p2)")
        uuid_part = name_parts[0].split("e0 ")[1]
        self.assertEqual(len(uuid_part), 8)

        # Verify first moved experiment still has its name
        e3_final = self.run_request("get", f"{LOCAL_API_URL}/v1/experiment/{self.e3['id']}").json()
        self.assertEqual(e3_final["name"], "e0 (moved from p2)")
