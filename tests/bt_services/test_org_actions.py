import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class OrgActionsTest(BraintrustAppTestBase):
    def _get_user_email(self, user_id):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select email from users where id = %s", (user_id,))
                return cursor.fetchone()[0]

    def test_project_summary(self):
        e00 = braintrust.init("p")
        e01 = braintrust.init("p")
        d00 = braintrust.init_dataset("p", "d00")

        e10 = braintrust.init("p2")
        d10 = braintrust.init_dataset("p2", "d10")

        for obj in [e00, e01, d00, e10, d10]:
            obj.id

        def get_project_summary(api_key):
            resp = self.run_server_action(
                api_key=api_key,
                function_name="getProjectSummary",
                function_args=dict(org_name=self.org_name),
            ).json()
            return {
                item["project_id"]: {
                    k: v
                    for k, v in item.items()
                    if k not in ["project_id", "project_created_at", "created_by_name", "created_by_avatar_url"]
                }
                for item in resp
            }

        project_summary = get_project_summary(self.org_api_key)
        self.assertEqual(
            project_summary,
            {
                e00.project.id: dict(
                    project_name=e00.project.name,
                    num_experiments=2,
                    num_playgrounds=0,
                    num_datasets=1,
                    project_created_by=self.user_id,
                    created_by_email=self._get_user_email(self.user_id),
                ),
                e10.project.id: dict(
                    project_name=e10.project.name,
                    num_experiments=1,
                    num_playgrounds=0,
                    num_datasets=1,
                    project_created_by=self.user_id,
                    created_by_email=self._get_user_email(self.user_id),
                ),
            },
        )

        # Create a user who only has read access to the second project.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.grant_acl(dict(object_type="project", object_id=e10.project.id, user_id=user_id, permission="read"))
        project_summary = get_project_summary(user_api_key)
        self.assertEqual(
            project_summary,
            {
                e10.project.id: dict(
                    project_name=e10.project.name,
                    num_experiments=1,
                    num_playgrounds=0,
                    num_datasets=1,
                    project_created_by=self.user_id,
                    created_by_email=self._get_user_email(self.user_id),
                ),
            },
        )
