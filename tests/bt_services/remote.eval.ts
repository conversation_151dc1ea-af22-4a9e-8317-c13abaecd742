import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { Eval, initDataset } from "braintrust";
import { z } from "zod";

Eval("Simple eval", {
  data: initDataset("local dev", { dataset: "sanity" }),
  task: async (input, { parameters }) => {
    return parameters.prefix + ": " + input;
  },
  scores: [<PERSON><PERSON>htein],
  parameters: {
    main: {
      type: "prompt",
      name: "Main prompt",
      description: "This is the main prompt",
      default: {
        messages: [
          {
            role: "user",
            content: "{{input}}",
          },
        ],
        model: "gpt-4o",
      },
    },
    another: {
      type: "prompt",
      name: "Another prompt",
      description: "This is another prompt",
      default: {
        messages: [
          {
            role: "user",
            content: "{{input}}",
          },
        ],
        model: "gpt-4o",
      },
    },
    include_prefix: z
      .boolean()
      .default(false)
      .describe("Include a contextual prefix"),
    prefix: z
      .string()
      .describe("The prefix to include")
      .default("this is a math problem"),
    array_of_objects: z
      .array(
        z.object({
          name: z.string(),
          age: z.number(),
        }),
      )
      .default([
        { name: "<PERSON>", age: 30 },
        { name: "<PERSON>", age: 25 },
      ]),
  },
});
