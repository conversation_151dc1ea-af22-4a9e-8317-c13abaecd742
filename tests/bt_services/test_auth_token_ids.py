"""Tests for the auth_token_ids API endpoint."""

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class AuthTokenIdsTest(BraintrustAppTestBase):
    def test_auth_token_ids_without_authorization(self):
        """Test that auth_token_ids works for anonymous users."""
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={},  # Explicitly pass empty headers to override default auth
        )

        # Should return 200 with anonymous user info
        assert response.status_code == 200
        data = response.json()

        # Should return a null org_id
        assert "user_id" in data
        assert "org_id" in data
        assert data["org_id"] is None

    def test_auth_token_ids_with_invalid_token(self):
        """Test that auth_token_ids rejects invalid tokens."""
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={"Authorization": "Bearer invalid_token"},
            expect_error=True,
        )

        # Should return 401 for invalid token
        assert response.status_code in [401, 403]

    def test_auth_token_ids_returns_correct_org_id_for_different_orgs(self):
        """Test that auth_token_ids returns the correct org_id for different organizations."""
        # Create another organization
        other_org_id, other_org_name = self.createOrg()
        other_user_id, _, other_org_api_key = self.createUserInOrg(other_org_id)

        # Test with the original organization
        response1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )

        assert response1.status_code == 200
        data1 = response1.json()
        assert data1["org_id"] == self.org_id
        assert data1["user_id"] == self.user_id

        # Test with the second organization
        response2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={"Authorization": f"Bearer {other_org_api_key}"},
        )

        assert response2.status_code == 200
        data2 = response2.json()
        assert data2["org_id"] == other_org_id
        assert data2["user_id"] == other_user_id

        # The org_ids and user_ids should be different
        assert data1["org_id"] != data2["org_id"]
        assert data1["user_id"] != data2["user_id"]

    def test_auth_token_ids_user_in_multiple_orgs(self):
        """Test that auth_token_ids returns the correct org_id when user is in multiple orgs."""
        # Create another organization
        other_org_id, other_org_name = self.createOrg()

        # Add the existing user to the second organization
        self.addUserToOrg(self.user_id, other_org_id)

        # Create an API key for the user that's scoped to the original org
        user_api_key_org1 = self.createUserOrgApiKey(self.user_id, self.org_id)

        # Create an API key for the user that's scoped to the second org
        user_api_key_org2 = self.createUserOrgApiKey(self.user_id, other_org_id)

        # Test with org1-scoped API key - should return org1
        response1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={"Authorization": f"Bearer {user_api_key_org1}"},
        )

        assert response1.status_code == 200
        data1 = response1.json()
        assert data1["org_id"] == self.org_id
        assert data1["user_id"] == self.user_id

        # Test with org2-scoped API key - should return org2
        response2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={"Authorization": f"Bearer {user_api_key_org2}"},
        )

        assert response2.status_code == 200
        data2 = response2.json()
        assert data2["org_id"] == other_org_id
        assert data2["user_id"] == self.user_id

        # Verify same user_id across different organizations
        assert data1["user_id"] == data2["user_id"]
        assert data1["org_id"] != data2["org_id"]

    def test_auth_token_ids_unscoped_api_key_with_org_name(self):
        """Test that auth_token_ids works with unscoped API keys and org_name parameter."""
        # Create another organization
        other_org_id, other_org_name = self.createOrg()

        # Add the existing user to the second organization
        self.addUserToOrg(self.user_id, other_org_id)

        # Create an unscoped API key (org_id = None)
        unscoped_api_key = self.createUserOrgApiKey(self.user_id, None)

        # Test without org_name - should return null org_id
        response1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={},
            headers={"Authorization": f"Bearer {unscoped_api_key}"},
        )

        assert response1.status_code == 200
        data1 = response1.json()
        assert data1["org_id"] is None
        assert data1["user_id"] == self.user_id

        # Test with org_name for the first organization
        response2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={"org_name": self.org_name},
            headers={"Authorization": f"Bearer {unscoped_api_key}"},
        )

        assert response2.status_code == 200
        data2 = response2.json()
        assert data2["org_id"] == self.org_id
        assert data2["user_id"] == self.user_id

        # Test with org_name for the second organization
        response3 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={"org_name": other_org_name},
            headers={"Authorization": f"Bearer {unscoped_api_key}"},
        )

        assert response3.status_code == 200
        data3 = response3.json()
        assert data3["org_id"] == other_org_id
        assert data3["user_id"] == self.user_id

        # Test with org_name for a non-existent organization
        response4 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/auth_token_ids",
            json={"org_name": "nonexistent_org"},
            headers={"Authorization": f"Bearer {unscoped_api_key}"},
            expect_error=True,
        )

        # Should return an error for non-existent org
        assert response4.status_code == 403
