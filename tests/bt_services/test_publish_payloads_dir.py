import os
import tempfile
import uuid

import braintrust
from braintrust.util import <PERSON><PERSON><PERSON><PERSON><PERSON>

from tests.braintrust_app_test_base import BraintrustAppTestBase


class PublishPayloadsDir(BraintrustAppTestBase):
    def test_basic(self):
        row_id = str(uuid.uuid4())
        input_str = "foobar"
        with tempfile.TemporaryDirectory() as tmpdir_failed, tempfile.TemporaryDirectory() as tmpdir_all:
            with braintrust._internal_with_custom_background_logger() as logger:
                logger.sync_flush = True
                logger.failed_publish_payloads_dir = tmpdir_failed
                logger.all_publish_payloads_dir = tmpdir_all
                logger.log(
                    LazyValue(
                        lambda: dict(id=row_id, input=input_str, experiment_id="bogus", _is_merge=False),
                        use_mutex=False,
                    )
                )
                with self.assertRaises(Exception):
                    braintrust.flush()
                e = braintrust.init("p")
                e.log(id=row_id, input=input_str, output=input_str, scores=dict())
                braintrust.flush()

            filenames = os.listdir(tmpdir_failed)
            self.assertEqual(len(filenames), 1)
            with open(os.path.join(tmpdir_failed, filenames[0])) as f:
                file_contents = f.read()
            self.assertIn(row_id, file_contents)
            self.assertIn(input_str, file_contents)
            failed_file_contents = file_contents

            filenames = os.listdir(tmpdir_all)
            self.assertEqual(len(filenames), 2)
            all_file_contents = []
            for fpath in filenames:
                with open(os.path.join(tmpdir_all, fpath)) as f:
                    all_file_contents.append(f.read())
            for file_contents in all_file_contents:
                self.assertIn(row_id, file_contents)
                self.assertIn(input_str, file_contents)
            self.assertIn(failed_file_contents, all_file_contents)
