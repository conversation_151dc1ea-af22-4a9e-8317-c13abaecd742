import dataclasses
import unittest
from uuid import uuid4

from braintrust.span_identifier_v2 import SpanComponentsV2, SpanObjectTypeV2, SpanRowIdsV2
from braintrust.span_identifier_v3 import SpanComponentsV3, SpanObjectTypeV3


class SpanComponentsV3Test(unittest.TestCase):
    def test_no_row_id(self):
        for object_type in SpanObjectTypeV3:
            object_id = str(uuid4())
            compute_object_metadata_args = dict(foo="bar", hello=dict(goodbye="yes"))
            components = SpanComponentsV3(object_type=object_type, object_id=object_id)
            self.assertEqual(SpanComponentsV3.from_str(components.to_str()), components)
            components = SpanComponentsV3(
                object_type=object_type, compute_object_metadata_args=compute_object_metadata_args
            )
            self.assertEqual(SpanComponentsV3.from_str(components.to_str()), components)
            with self.assertRaises(Exception):
                SpanComponentsV3(
                    object_type=object_type,
                    object_id=object_id,
                    compute_object_metadata_args=compute_object_metadata_args,
                )

    def test_with_row_id(self):
        object_id, row_id, span_id, root_span_id = ["a", str(uuid4()), "c", str(uuid4())]
        components = SpanComponentsV3(
            object_type=SpanObjectTypeV3.EXPERIMENT,
            object_id=object_id,
            row_id=row_id,
            span_id=span_id,
            root_span_id=root_span_id,
        )
        self.assertEqual(SpanComponentsV3.from_str(components.to_str()), components)

        # Must provide all or none of the row IDs.
        with self.assertRaises(Exception):
            SpanComponentsV3(
                object_type=SpanObjectTypeV3.EXPERIMENT,
                object_id=object_id,
                row_id=row_id,
                span_id=span_id,
            )
        with self.assertRaises(Exception):
            SpanComponentsV3(
                object_type=SpanObjectTypeV3.EXPERIMENT,
                object_id=object_id,
                row_id=row_id,
                root_span_id=root_span_id,
            )
        with self.assertRaises(Exception):
            SpanComponentsV3(
                object_type=SpanObjectTypeV3.EXPERIMENT,
                object_id=object_id,
                span_id=span_id,
                root_span_id=root_span_id,
            )

    def test_backwards_compatible(self):
        object_id, row_id, span_id, root_span_id = [str(uuid4()) for _ in range(4)]
        span_components_old = SpanComponentsV2(
            object_type=SpanObjectTypeV2.EXPERIMENT,
            object_id=object_id,
            row_ids=SpanRowIdsV2(row_id=row_id, span_id=span_id, root_span_id=root_span_id),
        )
        span_components_new = SpanComponentsV3.from_str(span_components_old.to_str())
        self.assertEqual(span_components_new.object_type.value, span_components_old.object_type.value)
        self.assertEqual(span_components_new.object_id, span_components_old.object_id)
        self.assertEqual(span_components_new.row_id, span_components_old.row_ids.row_id)
        self.assertEqual(span_components_new.span_id, span_components_old.row_ids.span_id)
        self.assertEqual(span_components_new.root_span_id, span_components_old.row_ids.root_span_id)
        self.assertIsNone(span_components_new.compute_object_metadata_args)
