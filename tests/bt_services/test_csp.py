import json

import requests
from braintrust import app_conn

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase
from tests.helpers.clerk import (
    ClerkBraintrustAppTestBase,
    create_clerk_user,
    delete_clerk_user,
    get_clerk_cookies,
    get_clerk_session,
)
from tests.helpers.csp import csp, get_csp_from_response, get_org_csp, set_org_csp


class CSPTests(BraintrustAppTestBase):
    def test__api_endpoints(self):
        res = app_conn().post("/api/self/me", json={})
        res.raise_for_status()
        self.assertEqual(
            res.json(),
            {
                "email": self.user_email,
                "organizations": [{"id": self.org_id, "name": self.org_name}],
                "id": self.user_id,
            },
        )

        self.assertEqual(
            get_csp_from_response(res),
            {
                # TODO: assert on connect-src, frame-ancestors, and form-action
                "Content-Security-Policy": csp(),
                "Content-Security-Policy-Report-Only": None,
                "Report-To": None,
                "Report-Endpoints": None,
            },
        )

    def test_no_csp_files(self):
        res = app_conn().get(
            "/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdeveloper-workflow.06fa57ba.png&w=1200&q=75"
        )
        res.raise_for_status()

        self.assertEqual(
            get_csp_from_response(res),
            {
                "Content-Security-Policy": csp({"script-src": ["'none'"], "frame-src": ["'none'"], "sandbox": []}),
                "Content-Security-Policy-Report-Only": None,
                "Report-To": None,
                "Report-Endpoints": None,
            },
        )

    def test_global_csp_homepage_and_other_anonymous_pages(self):
        res = app_conn().get("/")
        res.raise_for_status()

        self.assertEqual(
            get_csp_from_response(res),
            {
                # TODO(ibolmo): make a more specific assertion
                "Content-Security-Policy": csp(),
                "Content-Security-Policy-Report-Only": None,
                "Report-To": None,
                "Report-Endpoints": None,
            },
        )


def get_headers(api_key: str):
    return {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}


class LoggedInCSPTests(ClerkBraintrustAppTestBase):
    def test_global_and_org_csp(self):
        set_org_csp(self.org_id, "org", "org report only")
        self.assertEqual(get_org_csp(self.org_id), ("org", "org report only"))

        session = requests.Session()

        res = session.get(f"{LOCAL_APP_URL}/app/{self.org_name}", cookies=get_clerk_cookies(self.clerk_jwt))

        self.assertNotIn("You are signed out", res.text)
        self.assertNotEqual(res.headers.get("X-Clerk-Auth-Status"), "signed-out")

        org_csp = get_csp_from_response(res)

        self.assertEqual(org_csp["Content-Security-Policy"], "org")
        self.assertEqual(org_csp["Content-Security-Policy-Report-Only"], "org report only")
