import * as braintrust from "braintrust";
import { expect, test } from "vitest";

test("spanExportInvalidApiUrl", async () => {
  // Create a logger with a bogus appUrl
  const logger = braintrust.initLogger({
    projectName: "p",
    appUrl: "http://invalid-url",
  });

  // Attempt to fetch the logger's project id, expecting it to fail
  await expect(async () => {
    return (await logger.project).id;
  }).rejects.toThrow();

  // Attempt to export the logger, expecting it to succeed
  const exportedLogger = await logger.export();
  expect(exportedLogger).toBeDefined();
});
