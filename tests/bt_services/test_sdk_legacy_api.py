import json
import unittest

from braintrust.object import ensure_dataset_record


class LegacyAPITest(unittest.TestCase):
    def test_ensure_dataset_record(self):
        cases = [
            (
                {"input": "a", "output": "b", "expected": "c"},
                True,
                {"input": "a", "output": "b", "expected": "c"},
            ),
            (
                {"input": "a", "output": "b", "expected": "c"},
                False,
                {"input": "a", "output": "b", "expected": "c"},
            ),
            (
                {"input": "a", "expected": "b"},
                True,
                {"input": "a", "output": "b"},
            ),
            (
                {"input": "a", "expected": "b"},
                False,
                {"input": "a", "expected": "b"},
            ),
            (
                {"input": "a", "output": "b"},
                True,
                {"input": "a", "output": "b"},
            ),
            (
                {"input": "a", "output": "b"},
                False,
                {"input": "a", "expected": "b"},
            ),
        ]
        for row, legacy, expected in cases:
            with self.subTest(row=row, legacy=legacy):
                actual = ensure_dataset_record(row, legacy)
                self.assertEqual(actual, expected)
