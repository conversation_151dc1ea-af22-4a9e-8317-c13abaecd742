from typing import Dict

import braintrust
from braintrust.logger import init_logger

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
from tests.helpers.telemetry import clear_events, telemetry_event_aggregation

SYSADMIN_USER_EMAIL = "<EMAIL>"


def get_request_headers(api_key: str) -> Dict[str, str]:
    return {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}


class TelemetryAPIsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        # Set up sysadmin user and API key for billing status tests
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("insert into users(email) values (%s) on conflict do nothing", (SYSADMIN_USER_EMAIL,))
                cursor.execute("select id from users where email = %s", (SYSADMIN_USER_EMAIL,))
                [sysadmin_user_id] = cursor.fetchone() or []
                cursor.execute(
                    "delete from members where user_id = %s and org_id = %s", (sysadmin_user_id, self.org_id)
                )

        # Create another org for the sysadmin user
        self.other_org_id, _ = self.createOrg()
        self.addUserToOrg(sysadmin_user_id, self.other_org_id, remove_from_org_owners=True)
        self.sysadmin_api_key = self.createUserOrgApiKey(sysadmin_user_id, self.other_org_id)

        # Clear any existing telemetry events
        clear_events(self.org_id)

    def test_empty_config_for_org(self):
        # Ensure there is no billing record for this organization.
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                # Delete any potential existing billing config for a clean test.
                cursor.execute("DELETE FROM org_billing WHERE org_id = %s", (self.org_id,))

        response = braintrust.app_conn().post(
            "api/telemetry/configs",
            json={"org_ids": [self.org_id]},
            headers=get_request_headers(self.org_api_key),
        )

        self.assertEqual(response.json(), {"configs": {}})

    def test_config_with_telemetry(self):
        expected_url = "http://example.com/telemetry"

        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                # Ensure a clean billing record then insert one.
                cursor.execute("DELETE FROM org_billing WHERE org_id = %s", (self.org_id,))
                cursor.execute(
                    "INSERT INTO org_billing (org_id, telemetry_url) VALUES (%s, %s)",
                    (self.org_id, expected_url),
                )

        response = braintrust.app_conn().post(
            "api/telemetry/configs",
            json={"org_ids": [self.org_id]},
            headers=get_request_headers(self.org_api_key),
        )

        self.assertEqual(response.json(), {"configs": {self.org_id: {"url": expected_url, "secret": ""}}})

    def test_non_member_failure(self):
        expected_url = "http://example.com/telemetry"

        other_org_id, _ = self.createOrg()
        other_member_id, _, __ = self.createUserInOrg(other_org_id)
        non_member_token = self.createUserOrgApiKey(other_member_id, other_org_id)

        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("DELETE FROM org_billing WHERE org_id = %s", (self.org_id,))
                cursor.execute(
                    "INSERT INTO org_billing (org_id, telemetry_url) VALUES (%s, %s)",
                    (self.org_id, expected_url),
                )

                cursor.execute("DELETE FROM org_billing WHERE org_id = %s", (other_org_id,))
                cursor.execute(
                    "INSERT INTO org_billing (org_id, telemetry_url) VALUES (%s, %s)",
                    (other_org_id, expected_url),
                )

        response = braintrust.app_conn().post(
            "api/telemetry/configs",
            json={"org_ids": [other_org_id, self.org_id]},
            headers=get_request_headers(non_member_token),
        )

        self.assertEqual(
            response.json(),
            {
                "configs": {
                    other_org_id: {"url": expected_url, "secret": ""},
                    # note the missing self.org_id in the response
                }
            },
        )

    def test_billing_status_requires_sysadmin(self):
        """Test that billing status endpoint requires sysadmin privileges"""
        logger = init_logger(project="telemetry_test")

        # This should fail with access denied for non-sysadmin user
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/status",
            json={"orgIds": [logger.org_id]},
            headers=get_request_headers(self.org_api_key),
            expect_error=True,
        )

        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertIn("Missing sysadmin access", error_data.get("Message", ""))

    def test_billing_status_single_org_with_events(self):
        """Test billing status for single org with logged events"""
        logger = init_logger(project="telemetry_billing_test")

        # Log some test data with telemetry aggregation enabled
        with telemetry_event_aggregation(logger.org_id, enabled=True):
            for i in range(5):
                logger.log(
                    input=f"test_input_{i}",
                    output=f"test_output_{i}",
                    scores={"accuracy": 0.9, "latency": 0.1 + i * 0.01},
                )
            logger.flush()

        # Events will be naturally pending for billing status check

        # Check billing status as sysadmin
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/status",
            json={"orgIds": [logger.org_id]},
            headers=get_request_headers(self.sysadmin_api_key),
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify response structure
        self.assertIn("enabled", data)
        self.assertIn("configs", data)
        self.assertIn("aggregation", data)

        # Check that configs is a dictionary
        self.assertIsInstance(data["configs"], dict)

        # Verify that our logged events are reflected in the aggregation
        # Note: The billing system aggregates events by time windows, not individual log entries
        if data["aggregation"] and "orgs" in data["aggregation"]:
            orgs_data = data["aggregation"]["orgs"]

            # The logger's org should have at least 1 aggregated event from our logging
            if logger.org_id in orgs_data:
                actual_events = orgs_data[logger.org_id]["events"]
                self.assertGreaterEqual(
                    actual_events,
                    1,
                    f"Expected at least 1 aggregated event for org {logger.org_id}, got {actual_events}",
                )

    def test_billing_status_multiple_orgs_with_events(self):
        """Test billing status showing multiple orgs with events"""
        # Create a couple experiments in the main org to generate some events
        with telemetry_event_aggregation(self.org_id, enabled=True):
            exp1 = braintrust.init(project="billing_test_proj1")
            for i in range(3):
                exp1.log(input=f"test_input_{i}", output=f"test_output_{i}", scores={"accuracy": 0.8})
            exp1.flush()

            exp2 = braintrust.init(project="billing_test_proj2")
            for i in range(5):
                exp2.log(input=f"test_input_{i}", output=f"test_output_{i}", scores={"accuracy": 0.9})
            exp2.flush()

        # Create a second org with some events
        other_org_id, _ = self.createOrg()
        clear_events(other_org_id)
        other_user_id, _, other_api_key = self.createUserInOrg(other_org_id)

        # Log events to the other org using its API key with force_login
        with telemetry_event_aggregation(other_org_id, enabled=True):
            other_logger = init_logger(project="billing_test_other_org", api_key=other_api_key, force_login=True)
            for i in range(2):
                other_logger.log(input=f"other_input_{i}", output=f"other_output_{i}", scores={"accuracy": 0.7})
            other_logger.flush()

        # Events will be naturally pending for billing status check

        # Test billing status with the main org as "selected"
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/status",
            json={"orgIds": [self.org_id]},  # main org is the "selected" org
            headers=get_request_headers(self.sysadmin_api_key),
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify response structure
        self.assertIn("enabled", data)
        self.assertIn("configs", data)
        self.assertIn("aggregation", data)

        self.assertIn("state", data["aggregation"])
        self.assertIn("pending", data["aggregation"])
        self.assertIn("orgs", data["aggregation"])

        pending = data["aggregation"]["pending"]
        self.assertIn("windows", pending)
        self.assertIn("events", pending)

        # Verify that events from both orgs are reflected in the aggregation
        # Note: The billing system aggregates events by time windows across organizations
        if "orgs" in data["aggregation"]:
            orgs_data = data["aggregation"]["orgs"]

            # Main org should have at least 1 aggregated event from our experiments
            if self.org_id in orgs_data:
                main_org_events = orgs_data[self.org_id]["events"]
                self.assertGreaterEqual(
                    main_org_events,
                    1,
                    f"Expected at least 1 aggregated event for main org {self.org_id}, got {main_org_events}",
                )

            # Other org should have at least 1 aggregated event from our logger
            if other_org_id in orgs_data:
                other_org_events = orgs_data[other_org_id]["events"]
                self.assertGreaterEqual(
                    other_org_events,
                    1,
                    f"Expected at least 1 aggregated event for other org {other_org_id}, got {other_org_events}",
                )

            # Verify we have at least some events tracked (timing-dependent, so be lenient)
            orgs_with_events = [org_id for org_id, org_data in orgs_data.items() if org_data["events"] > 0]
            self.assertGreaterEqual(
                len(orgs_with_events),
                1,
                f"Expected at least 1 org with events, got {len(orgs_with_events)} orgs: {orgs_with_events}",
            )

        # Test with multiple selected orgs
        response_multi = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/status",
            json={"orgIds": [self.org_id, other_org_id]},
            headers=get_request_headers(self.sysadmin_api_key),
        )

        self.assertEqual(response_multi.status_code, 200)
        data_multi = response_multi.json()

        # Should have same structure and event counts
        self.assertIn("enabled", data_multi)
        self.assertIn("configs", data_multi)
        self.assertIn("aggregation", data_multi)

        # Verify the multi-org response has consistent event counts
        if data_multi["aggregation"] and "orgs" in data_multi["aggregation"]:
            multi_orgs_data = data_multi["aggregation"]["orgs"]

            # Verify that selecting multiple orgs returns consistent aggregation data
            if self.org_id in multi_orgs_data:
                main_org_events = multi_orgs_data[self.org_id]["events"]
                self.assertGreaterEqual(
                    main_org_events,
                    1,
                    f"Expected at least 1 aggregated event for main org {self.org_id} in multi-org response, got {main_org_events}",
                )
            if other_org_id in multi_orgs_data:
                other_org_events = multi_orgs_data[other_org_id]["events"]
                self.assertGreaterEqual(
                    other_org_events,
                    1,
                    f"Expected at least 1 aggregated event for other org {other_org_id} in multi-org response, got {other_org_events}",
                )

    def test_billing_refresh_cache_invalidation(self):
        """Test that billing cache refresh works properly"""
        logger = init_logger(project="telemetry_cache_test")

        # Log some data with telemetry aggregation enabled
        with telemetry_event_aggregation(logger.org_id, enabled=True):
            for i in range(3):
                logger.log(input=f"cache_test_{i}", output=f"result_{i}")
            logger.flush()

        # Events will be naturally pending for billing status check

        # Get initial status
        initial_response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/status",
            json={"orgIds": [logger.org_id]},
            headers=get_request_headers(self.sysadmin_api_key),
        )
        self.assertEqual(initial_response.status_code, 200)

        # Invalidate cache
        refresh_response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/refresh",
            json={"orgIds": [logger.org_id]},
            headers=get_request_headers(self.sysadmin_api_key),
        )
        self.assertEqual(refresh_response.status_code, 200)
        self.assertEqual(refresh_response.json(), {})

        # Get status again (should still work after cache invalidation)
        final_response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/status",
            json={"orgIds": [logger.org_id]},
            headers=get_request_headers(self.sysadmin_api_key),
        )
        self.assertEqual(final_response.status_code, 200)

        # Both responses should have the same structure
        initial_data = initial_response.json()
        final_data = final_response.json()
        self.assertEqual(set(initial_data.keys()), set(final_data.keys()))

    def test_billing_refresh_requires_sysadmin(self):
        """Test that billing cache refresh requires sysadmin privileges"""
        logger = init_logger(project="telemetry_refresh_test")

        # This should fail with access denied for non-sysadmin user
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/billing/refresh",
            json={"orgIds": [logger.org_id]},
            headers=get_request_headers(self.org_api_key),
            expect_error=True,
        )

        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertIn("Permission denied", error_data.get("Message", ""))
