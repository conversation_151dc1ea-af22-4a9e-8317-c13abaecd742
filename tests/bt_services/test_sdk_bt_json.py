import json
import unittest
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import chevron
from braintrust import bt_dumps, init_logger, traced
from pydantic import BaseModel, Field, create_model


class ModelA(BaseModel):
    fruit: str = Field(..., description="Fruit")
    color: Optional[str] = Field(..., description="Color")
    num: int = Field(..., description="Num")
    metadata: Dict[str, Any] = Field(..., description="ModelA Metadata")


class ModelB(BaseModel):
    id: str = Field(..., description="ID, unique")
    name: str = Field(..., description="Name")
    child: ModelA = Field(..., description="Child")
    metadata: Dict[str, Any] = Field(..., description="ModelB Metadata")


@dataclass
class ObjectA:
    id: str
    foo: List[str]
    metadata: Dict[str, Any]


@dataclass
class ObjectB:
    id: str
    child: ObjectA


@dataclass
class ObjectC:
    data: Any


class DummyClass:
    pass


class JSONTest(unittest.TestCase):
    @property
    def _serializable_cases(self):
        return [
            (None, None),
            (2, 2),
            ("hello", "hello"),
            ({"foo": "bar"}, {"foo": "bar"}),
            ([1, 2, 3], [1, 2, 3]),
            ((1, 2, 3), (1, 2, 3)),
            (
                ModelA(fruit="apple", color="red", num=5, metadata={"key": "value"}),
                {"fruit": "apple", "color": "red", "num": 5, "metadata": {"key": "value"}},
            ),
            (
                ModelB(
                    id="123",
                    name="name",
                    child=ModelA(fruit="apple", color="red", num=5, metadata={"key": "value"}),
                    metadata={"key": "value"},
                ),
                {
                    "id": "123",
                    "name": "name",
                    "child": {"fruit": "apple", "color": "red", "num": 5, "metadata": {"key": "value"}},
                    "metadata": {"key": "value"},
                },
            ),
            (
                ObjectA(id="123", foo=["bar", "baz"], metadata={"key": "value"}),
                {"id": "123", "foo": ["bar", "baz"], "metadata": {"key": "value"}},
            ),
            (
                ["baz", ObjectA(id="123", foo=["bar", "baz"], metadata={"key": "value"}), 2, 3],
                ["baz", {"id": "123", "foo": ["bar", "baz"], "metadata": {"key": "value"}}, 2, 3],
            ),
            (
                ObjectB(id="123", child=ObjectA(id="456", foo=["bar", "baz"], metadata={"key": "value"})),
                {"id": "123", "child": {"id": "456", "foo": ["bar", "baz"], "metadata": {"key": "value"}}},
            ),
            (
                {
                    "foo": ObjectC(
                        data=ModelA(
                            fruit="apple",
                            color="red",
                            num=5,
                            metadata={"key": ObjectA(id="123", foo=["bar", "baz"], metadata={"key": "value"})},
                        )
                    )
                },
                {
                    "foo": {
                        "data": {
                            "fruit": "apple",
                            "color": "red",
                            "num": 5,
                            "metadata": {"key": {"id": "123", "foo": ["bar", "baz"], "metadata": {"key": "value"}}},
                        }
                    }
                },
            ),
        ]

    @property
    def _unserializable_cases(self):
        return [
            Exception,
            DummyClass(),
            traced,
            init_logger,
            lambda x: x,
        ]

    def test_json_dump(self):
        for obj, expected in self._serializable_cases:
            self.assertEqual(bt_dumps(obj), json.dumps(expected))
        for obj in self._unserializable_cases:
            self.assertEqual(bt_dumps(obj), json.dumps(str(obj)))
