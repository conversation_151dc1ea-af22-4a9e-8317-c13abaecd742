from uuid import uuid4

import braintrust
from braintrust.db_fields import IS_MERGE_FIELD, OBJECT_DELETE_FIELD, PARENT_ID_FIELD
from braintrust.util import LazyValue
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


class SpanParentIdTest(BraintrustAppTestBase):
    def test_missing_span_ids(self):
        # Verify that posting a span that references a parent
        # without root_span_ids explicitly specified succeeds.
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            for flush_level in range(3):
                experiment = braintrust.init(project="missing_span_ids")
                project_id = experiment.project.id
                experiment_id = experiment.id

                parent_id = str(uuid4())
                parent_span_id = str(uuid4())

                custom_bg_logger.log(
                    LazyValue(
                        lambda: {
                            "id": parent_id,
                            "experiment_id": experiment_id,
                            "project_id": project_id,
                            "span_id": parent_span_id,
                            "root_span_id": parent_span_id,
                            "input": 1,
                            "output": 2,
                            "scores": {"a": 0.5},
                            "span_attributes": {"name": "parent"},
                        },
                        use_mutex=False,
                    ),
                )

                if flush_level > 0:
                    experiment.flush()

                custom_bg_logger.log(
                    LazyValue(
                        lambda: {
                            "id": parent_id,
                            "experiment_id": experiment_id,
                            "project_id": project_id,
                            "input": 2,
                            IS_MERGE_FIELD: True,
                        },
                        use_mutex=False,
                    ),
                )

                if flush_level > 1:
                    experiment.flush()

                feedback_id = str(uuid4())
                feedback_span_id = str(uuid4())
                custom_bg_logger.log(
                    LazyValue(
                        lambda: {
                            "id": feedback_id,
                            "experiment_id": experiment_id,
                            "project_id": project_id,
                            "span_id": feedback_span_id,
                            "root_span_id": feedback_span_id,
                            "scores": {"b": 0.6},
                            "span_attributes": {"name": "child"},
                            PARENT_ID_FIELD: parent_id,
                        },
                        use_mutex=False,
                    )
                )

                experiment.flush()

                rows = get_object_json("experiment", experiment.id)
                self.assertEqual(len(rows), 2)

                parent_row = [r for r in rows if r["id"] == parent_id][0]
                self.assertEqual(parent_row["id"], parent_id)
                self.assertEqual(parent_row["scores"]["a"], 0.5)

                child_row = [r for r in rows if r["id"] == feedback_id][0]
                self.assertEqual(child_row["id"], feedback_id)
                self.assertEqual(child_row["root_span_id"], parent_span_id)
                self.assertEqual(child_row["span_parents"][0], parent_span_id)
                self.assertEqual(child_row["scores"]["b"], 0.6)

    def test_deleted_parent_span(self):
        # Verify that posting a span that references a deleted parent
        # successfully orphans the child span

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            for tweak_span_id in [False, True]:
                for flush_level in range(3):
                    experiment = braintrust.init(project="missing_span_ids")
                    project_id = experiment.project.id
                    experiment_id = experiment.id

                    parent_id = str(uuid4())
                    parent_span_id = str(uuid4())

                    custom_bg_logger.log(
                        LazyValue(
                            lambda: {
                                "id": parent_id,
                                "experiment_id": experiment_id,
                                "project_id": project_id,
                                "span_id": parent_span_id,
                                "root_span_id": parent_span_id,
                                "input": 1,
                                "output": 2,
                                "scores": {"a": 0.5},
                                "span_attributes": {"name": "parent"},
                            },
                            use_mutex=False,
                        )
                    )

                    if flush_level > 0:
                        experiment.flush()

                    update = {
                        "id": parent_id,
                        "experiment_id": experiment_id,
                        "project_id": project_id,
                        IS_MERGE_FIELD: True,
                        OBJECT_DELETE_FIELD: True,
                    }
                    if tweak_span_id:
                        parent_span_id = str(uuid4())
                        update.update(
                            {
                                "span_id": parent_span_id,
                                "root_span_id": parent_span_id,
                            }
                        )
                    custom_bg_logger.log(
                        LazyValue(
                            lambda: {
                                "id": parent_id,
                                "experiment_id": experiment_id,
                                "project_id": project_id,
                                IS_MERGE_FIELD: True,
                                OBJECT_DELETE_FIELD: True,
                            },
                            use_mutex=False,
                        )
                    )

                    if flush_level > 1:
                        experiment.flush()

                    feedback_id = str(uuid4())
                    feedback_span_id = str(uuid4())
                    custom_bg_logger.log(
                        LazyValue(
                            lambda: {
                                "id": feedback_id,
                                "experiment_id": experiment_id,
                                "project_id": project_id,
                                "span_id": feedback_span_id,
                                "root_span_id": feedback_span_id,
                                "scores": {"b": 0.6},
                                "span_attributes": {"name": "child"},
                                PARENT_ID_FIELD: parent_id,
                            },
                            use_mutex=False,
                        )
                    )

                    experiment.flush()

                    rows = get_object_json("experiment", experiment.id)
                    self.assertEqual(len(rows), 1)

                    child_row = [r for r in rows if r["id"] == feedback_id][0]
                    self.assertEqual(child_row["id"], feedback_id)
                    self.assertEqual(child_row["root_span_id"], feedback_span_id)
                    self.assertIsNone(child_row["span_parents"])
                    self.assertEqual(child_row["scores"]["b"], 0.6)
                    self.assertEqual(child_row["metadata"]["invalid_parent_id"], parent_id)

    def test_export_parent(self):
        experiment = braintrust.init("p")
        logger = braintrust.init_logger("p")

        exported_experiment = experiment.export()
        with experiment.start_span(id="id:1") as root_span:
            exported_experiment_root_span = root_span.export()

        exported_logger = logger.export()
        with logger.start_span(id="id:1") as root_span:
            exported_logger_root_span = root_span.export()

        with experiment.start_span(parent=exported_experiment, id="2") as root_span2:
            with root_span2.start_span(parent=exported_experiment, id="3") as root_span3:
                pass
        with experiment.start_span(parent=exported_experiment_root_span, id="4") as subspan:
            pass

        with logger.start_span(parent=exported_logger, id="2") as root_span2:
            with root_span2.start_span(parent=exported_logger, id="3") as root_span3:
                pass
        with logger.start_span(parent=exported_logger_root_span, id="4") as subspan:
            pass

        with braintrust.start_span(parent=exported_experiment, id="5") as span:
            pass
        with braintrust.start_span(parent=exported_experiment_root_span, id="6") as span:
            pass
        with braintrust.start_span(parent=exported_logger, id="5") as span:
            pass
        with braintrust.start_span(parent=exported_logger_root_span, id="6") as span:
            pass

        experiment.flush()
        logger.flush()

        experiment_rows = get_object_json("experiment", experiment.id)
        logger_rows = get_object_json("project_logs", logger.id)

        expected_id_to_parent_id = {
            "id:1": None,
            "2": None,
            "3": None,
            "4": "id:1",
            "5": None,
            "6": "id:1",
        }

        for rows, object_type in [(experiment_rows, "experiment"), (logger_rows, "logger")]:
            span_id_to_id = {r["span_id"]: r["id"] for r in rows}
            id_to_parent_id = {
                r["id"]: span_id_to_id[r["span_parents"][0]] if r["span_parents"] else None for r in rows
            }
            self.assertEqual(
                id_to_parent_id,
                expected_id_to_parent_id,
                f"Failed for object type {object_type}. span_id_to_id = {span_id_to_id}. id_to_parent_id = {id_to_parent_id}",
            )

    def test_start_span_parent_invalid(self):
        experiment = braintrust.init("p")
        logger = braintrust.init_logger("p")
        experiment2 = braintrust.init("p")
        logger2 = braintrust.init_logger("p2")

        exported_experiment = experiment.export()
        exported_logger = logger.export()
        exported_experiment2 = experiment2.export()
        exported_logger2 = logger2.export()

        with experiment.start_span(id="1") as root_span:
            braintrust.flush()
            exported_experiment_root_span = root_span.export()
            with self.assertRaises(Exception):
                root_span.start_span(parent=exported_logger)
            with root_span.start_span(parent=exported_experiment2) as invalid_span:
                invalid_span.log(input="hello")
            braintrust.flush()

        with logger.start_span(id="1") as root_span:
            braintrust.flush()
            exported_logger_root_span = root_span.export()
            with self.assertRaises(Exception):
                root_span.start_span(parent=exported_experiment)
            with root_span.start_span(parent=exported_logger2) as invalid_span:
                invalid_span.log(input="hello")
            braintrust.flush()

        with self.assertRaises(Exception):
            experiment.start_span(parent=exported_logger)
        with self.assertRaises(Exception):
            experiment.start_span(parent=exported_logger_root_span)
        with self.assertRaises(Exception):
            logger.start_span(parent=exported_experiment)
        with self.assertRaises(Exception):
            logger.start_span(parent=exported_experiment_root_span)
        with self.assertRaises(Exception):
            experiment.start_span(parent=exported_experiment, parent_id="1")
        with self.assertRaises(Exception):
            logger.start_span(parent=exported_logger, parent_id="1")
        with self.assertRaises(Exception):
            braintrust.start_span(parent=exported_logger, parent_id="1")

        experiment_rows = get_object_json("experiment", experiment.id)
        logger_rows = get_object_json("project_logs", logger.id)
        self.assertEqual([r["id"] for r in experiment_rows], ["1"])
        self.assertEqual([r["id"] for r in logger_rows], ["1"])

    def test_update_span(self):
        experiment = braintrust.init("p_u")

        with experiment.start_span(id="1") as root_span:
            root_span.log(input="hello", output="goodbye", scores={"foo": 1})
        experiment.summarize()

        experiment_row = get_object_json("experiment", experiment.id)[0]
        self.assertEqual(experiment_row["output"], "goodbye")
        self.assertIsNone(experiment_row["expected"], None)

        experiment.update_span(id="1", output="hello", expected="goodbye", scores={"foo": 0})
        experiment.flush()
        experiment_row = get_object_json("experiment", experiment.id)[0]

        self.assertEqual(experiment_row["output"], "hello")
        self.assertEqual(experiment_row["expected"], "goodbye")
        self.assertEqual(experiment_row["scores"], {"foo": 0})

        logger = braintrust.init_logger("p_u")
        with logger.start_span(id="1") as root_span:
            root_span.log(input="hello", output="goodbye", scores={"foo": 1})
            logger_root_span_exp = root_span.export()
        logger.flush()

        logger_row = get_object_json("project_logs", logger.id)[0]
        self.assertEqual(logger_row["output"], "goodbye")
        self.assertIsNone(logger_row["expected"])

        logger.update_span(id="1", output="hello", expected="goodbye", scores={"foo": 0})
        logger.flush()
        logger_row = get_object_json("project_logs", logger.id)[0]
        self.assertEqual(logger_row["output"], "hello")
        self.assertEqual(logger_row["expected"], "goodbye")
        self.assertEqual(logger_row["scores"], {"foo": 0})

        braintrust.update_span(exported=logger_root_span_exp, output="foo")
        with self.assertRaises(ValueError):
            braintrust.update_span(id="foo", exported=logger_root_span_exp, output="foo")

        braintrust.flush()
        logger_row = get_object_json("project_logs", logger.id)[0]
        self.assertEqual(logger_row["output"], "foo")
        self.assertEqual(logger_row["expected"], "goodbye")

    def test_span_export_invalid_api_url(self):
        # Create a logger with a bogus app_url
        logger = braintrust.init_logger("p", app_url="http://invalid-url")

        # Attempt to fetch the logger's project id, expecting it to fail
        with self.assertRaises(Exception):
            _ = logger.project.id

        # Attempt to export the logger, expecting it to succeed
        exported_logger = logger.export()
        self.assertIsNotNone(exported_logger)
