# Required in api-ts/.env.development for these tests:
# RATELIMIT_API_LOGS_ORG=c5292878-410f-4bbb-bffc-c61f9e624805=5
# RATELIMIT_API_LOGS_ORG_WINDOW_SECS=10
# RATELIMIT_API_LOGS_ORG_ENFORCE=true
#
# These tests use org_id = 'c5292878-410f-4bbb-bffc-c61f9e624805'

import braintrust
from braintrust import init_logger
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


class TestApiRateLimit(BraintrustAppTestBase):
    rate_limit_org_id = "c5292878-410f-4bbb-bffc-c61f9e624805"
    max_requests = 5

    def _count_logs_in_project(self, project_id):
        """Count the number of logs in a project."""
        logs = get_object_json("project_logs", project_id)
        return len(logs)

    def test_rate_limit_basic(self):

        # Note: this test will fail if it's run multiple times in a row because:
        # - we have a hardcoded org id that matches config
        # - i have no clean way of resetting the rate limit state between tests. We could
        #   hit the redis directly, but that seems brittle knowing this won't be an issue
        #   in CI.

        self.delete_org(self.rate_limit_org_id)
        self.setUpFull(org_id=self.rate_limit_org_id)

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.num_tries = 1
            custom_bg_logger.sync_flush = True
            logger = init_logger(project="test-api-rate-limits")
            project_id = logger.id
            assert logger._get_state().org_id == self.rate_limit_org_id

            # Verify no logs exist before test
            assert self._count_logs_in_project(project_id) == 0

            # First batch within limits
            for _ in range(self.max_requests):
                span = logger.start_span(name="test-span")
                span.end()
                span.flush()

            # Verify we have exactly max_requests logs
            assert self._count_logs_in_project(project_id) == self.max_requests

            # Write double the limit to ensure we exceed it
            hit_error = False
            for _ in range(self.max_requests * 2):
                try:
                    span = logger.start_span(name="test-span")
                    span.end()
                    span.flush()
                except Exception as e:
                    # Check that it contains a 429 error - look in nested exceptions if needed
                    if hasattr(e, "exceptions"):
                        # ExceptionGroup case
                        nested_errors = [str(exc) for exc in e.exceptions]
                        assert any(
                            "429" in err for err in nested_errors
                        ), f"Expected 429 in nested exceptions: {nested_errors}"
                    else:
                        assert "429" in str(e), f"Expected 429 status code in error message, got: {e}"
                    hit_error = True
                    break

            assert hit_error, "Expected to hit rate limit error when exceeding max_requests"
            # Verify we still only have max_requests logs (no additional logs after rate limit)
            assert self._count_logs_in_project(project_id) == self.max_requests

    def test_no_rate_limit(self):
        # no rate limit on this org.
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            logger = init_logger(project="test-api-no-rate-limits", async_flush=False)
            project_id = logger.id

            # Verify no logs exist before test
            assert self._count_logs_in_project(project_id) == 0

            # Send 100 spans - should all succeed
            for i in range(100):
                span = logger.start_span(name=f"test-span-{i}")
                span.end()
                span.flush()  # Should not raise any errors

            # Verify we have exactly 100 logs
            assert self._count_logs_in_project(project_id) == 100
