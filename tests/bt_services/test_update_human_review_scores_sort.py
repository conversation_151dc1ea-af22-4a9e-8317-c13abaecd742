import uuid

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class SetAllHumanReviewScoresSortTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="test")).json()
        self.project_id = self.project["id"]

        self.score_1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project_score",
            json=dict(project_id=self.project_id, name="score_1", score_type="slider"),
        ).json()
        self.score_2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project_score",
            json=dict(project_id=self.project_id, name="score_2", score_type="categorical"),
        ).json()
        self.score_3 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project_score",
            json=dict(project_id=self.project_id, name="score_3", score_type="slider"),
        ).json()
        self.non_human_review_score_1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project_score",
            json=dict(project_id=self.project_id, name="non_human_review_score", score_type="weighted"),
        ).json()
        self.non_human_review_score_2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project_score",
            json=dict(project_id=self.project_id, name="non_human_review_score", score_type="minimum"),
        ).json()

    def test_set_all_human_review_scores_sort(self):
        self.run_server_action(
            self.org_api_key,
            "setAllHumanReviewScoresSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={
                    self.score_1["id"]: "lexorank1",
                    self.score_2["id"]: "lexorank2",
                    self.score_3["id"]: "lexorank3",
                },
            ),
        ).json()
        updated_score_1 = self.run_request("get", f"{LOCAL_API_URL}/v1/project_score/{self.score_1['id']}").json()
        updated_score_2 = self.run_request("get", f"{LOCAL_API_URL}/v1/project_score/{self.score_2['id']}").json()
        updated_score_3 = self.run_request("get", f"{LOCAL_API_URL}/v1/project_score/{self.score_3['id']}").json()

        # Make sure we updated the position of the human review scores
        self.assertEqual(updated_score_1["position"], "lexorank1")
        self.assertEqual(updated_score_2["position"], "lexorank2")
        self.assertEqual(updated_score_3["position"], "lexorank3")

        # Make sure we did not touch the non-human review scores
        updated_non_human_review_score_1 = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_score/{self.non_human_review_score_1['id']}"
        ).json()
        updated_non_human_review_score_2 = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_score/{self.non_human_review_score_2['id']}"
        ).json()

        self.assertEqual(updated_non_human_review_score_1["position"], None)
        self.assertEqual(updated_non_human_review_score_2["position"], None)

    def test_set_all_human_review_scores_sort_not_enough_scores_provided(self):
        self.run_server_action(
            self.org_api_key,
            "setAllHumanReviewScoresSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={self.score_1["id"]: "lexorank1", self.score_2["id"]: "lexorank2"},
            ),
            expect_error=True,
        )

    def test_set_all_human_review_scores_sort_too_many_scores_provided(self):
        self.run_server_action(
            self.org_api_key,
            "setAllHumanReviewScoresSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={
                    self.score_1["id"]: "lexorank1",
                    self.score_2["id"]: "lexorank2",
                    self.score_3["id"]: "lexorank3",
                    "score_4": "lexorank4",
                },
            ),
            expect_error=True,
        )

    def test_set_all_human_review_scores_sort_wrong_scores_provided(self):
        self.run_server_action(
            self.org_api_key,
            "setAllHumanReviewScoresSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={
                    str(uuid.uuid4()): "lexorank1",
                    str(uuid.uuid4()): "lexorank2",
                    str(uuid.uuid4()): "lexorank3",
                },
            ),
            expect_error=True,
        )

    def test_set_human_review_score_sort(self):
        self.run_server_action(
            self.org_api_key,
            "setHumanReviewScoreSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={self.score_1["id"]: "lexorank1"},
            ),
        ).json()
        updated_score_1 = self.run_request("get", f"{LOCAL_API_URL}/v1/project_score/{self.score_1['id']}").json()
        updated_score_2 = self.run_request("get", f"{LOCAL_API_URL}/v1/project_score/{self.score_2['id']}").json()
        updated_score_3 = self.run_request("get", f"{LOCAL_API_URL}/v1/project_score/{self.score_3['id']}").json()

        self.assertEqual(updated_score_1["position"], "lexorank1")
        self.assertEqual(updated_score_2["position"], None)
        self.assertEqual(updated_score_3["position"], None)

        updated_non_human_review_score_1 = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_score/{self.non_human_review_score_1['id']}"
        ).json()
        updated_non_human_review_score_2 = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_score/{self.non_human_review_score_2['id']}"
        ).json()

        self.assertEqual(updated_non_human_review_score_1["position"], None)
        self.assertEqual(updated_non_human_review_score_2["position"], None)

    def test_set_human_review_score_sort_too_many_scores_provided(self):
        self.run_server_action(
            self.org_api_key,
            "setHumanReviewScoreSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={
                    self.score_1["id"]: "lexorank1",
                    self.score_2["id"]: "lexorank2",
                    self.score_3["id"]: "lexorank3",
                },
            ),
            expect_error=True,
        )

    def test_set_human_review_score_sort_wrong_score_provided(self):
        self.run_server_action(
            self.org_api_key,
            "setHumanReviewScoreSortOrder",
            dict(
                org_name=self.org_name,
                project_name=self.project["name"],
                position_descriptions={str(uuid.uuid4()): "lexorank1"},
            ),
            expect_error=True,
        )
