import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


class NoopSpanTest(BraintrustAppTestBase):
    def test_basic(self):
        @braintrust.traced
        def foo(x):
            self.assertEqual(braintrust.NOOP_SPAN, braintrust.current_span())
            braintrust.current_span().log(input="hello")
            with braintrust.current_span().start_span("subspan") as subspan:
                subspan.log(inputs="goodbye")
            braintrust.current_span().log(output="yes")
            return x + 1

        self.assertEqual(10, foo(9))

    def test_export_noop_span(self):
        with braintrust.start_span() as noop_span:
            self.assertEqual(noop_span, braintrust.NOOP_SPAN)
            noop_exported = noop_span.export()

        with braintrust.start_span(parent=noop_exported) as another_noop_span:
            self.assertEqual(another_noop_span, braintrust.NOOP_SPAN)

        experiment = braintrust.init("p")
        with experiment.start_span(parent=noop_exported) as root_span:
            root_span.log(input="part of experiment")
        experiment.flush()

        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]["input"], "part of experiment")
