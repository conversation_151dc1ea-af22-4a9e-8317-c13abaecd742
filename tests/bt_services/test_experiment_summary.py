import time

import braintrust
import redis
import requests
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, make_v1_url
from tests.bt_services.experiment_test_base import ExperimentTestBase


class ExperimentSummaryTest(ExperimentTestBase):
    def setUp(self):
        super().setUp()
        self.redis = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0)

    def test_has_id(self):
        experiment = braintrust.init(project="p")
        with experiment.start_span() as root_span:
            with root_span.start_span() as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 0, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-3.5-turbo"),
                )
            with root_span.start_span() as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 1, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-3.5-turbo"),
                )
            root_span.log(input="a", output="out", scores=dict(score0=0.0))
        summary = experiment.summarize()
        self.assertIsNotNone(summary.experiment_id)  # check that the property exists
        self.assertEqual(summary.experiment_id, experiment.id)
        self.assertEqual(summary.project_id, experiment.project.id)

        self.verify_brainstore_summary(experiment)

    def test_metric_values(self):
        experiment = braintrust.init(project="p")
        with experiment.start_span() as root_span:
            with root_span.start_span() as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 0, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-3.5-turbo"),
                )
            with root_span.start_span() as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 1, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-3.5-turbo"),
                )
            root_span.log(input="a", output="out", scores=dict(score0=0.0))
        experiment2 = braintrust.init(project="p")
        with experiment2.start_span() as root_span:
            with root_span.start_span() as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 0, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-4"),
                )
            with root_span.start_span() as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 1, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-4"),
                )
            root_span.log(input="a", output="out", scores=dict(score0=0.0))
        m1 = experiment.summarize().metrics
        m2 = experiment2.summarize().metrics

        self.assertEqual(m2["estimated_cost"].name, "estimated_cost")
        self.assertIsInstance(m2["estimated_cost"].metric, float)
        self.assertEqual(m2["estimated_cost"].unit, "$")
        self.assertEqual(m2["estimated_cost"].improvements, 0)
        self.assertEqual(m2["estimated_cost"].regressions, 1)
        self.assertIsInstance(m2["estimated_cost"].diff, float)
        self.assertAlmostEqual(m2["estimated_cost"].diff, m2["estimated_cost"].metric - m1["estimated_cost"].metric)

        self.assertEqual(m2["prompt_tokens"].name, "prompt_tokens")
        self.assertIsInstance(m2["prompt_tokens"].metric, int)
        self.assertEqual(m2["prompt_tokens"].metric, 1000)
        self.assertEqual(m2["prompt_tokens"].unit, "tok")
        self.assertEqual(m2["prompt_tokens"].improvements, 0)
        self.assertEqual(m2["prompt_tokens"].regressions, 0)
        self.assertIsInstance(m2["prompt_tokens"].diff, int)
        self.assertEqual(m2["prompt_tokens"].diff, 0)

        self.verify_brainstore_summary(experiment)
        self.verify_brainstore_summary(experiment2)

    def test_weighted_scores(self):
        experiment = braintrust.init(project="weighted_summary")
        headers = dict(Authorization="Bearer " + self.org_api_key)

        weighted_avg_def = dict(
            project_id=experiment.project.id,
            name="My avg",
            score_type="weighted",
            categories={
                "score1": 1,
                "score2": 3,
            },
        )
        resp = requests.post(make_v1_url("project_score"), json=weighted_avg_def, headers=headers)
        self.assertTrue(resp.ok, resp.text)

        min_score_def = dict(
            project_id=experiment.project.id,
            name="My min",
            score_type="minimum",
            categories=[
                "score1",
                "score2",
            ],
        )
        resp = requests.post(make_v1_url("project_score"), json=min_score_def, headers=headers)
        self.assertTrue(resp.ok, resp.text)

        max_score_def = dict(
            project_id=experiment.project.id,
            name="My max",
            score_type="maximum",
            categories=[
                "score1",
                "score2",
            ],
        )
        resp = requests.post(make_v1_url("project_score"), json=max_score_def, headers=headers)
        self.assertTrue(resp.ok, resp.text)

        experiment.log(input=1, output=2, scores={})
        summary = experiment.summarize()
        self.assertIsNone(summary.scores.get("score1"))
        self.assertIsNone(summary.scores.get("score2"))
        self.assertIsNone(summary.scores.get("My avg"))
        self.assertIsNone(summary.scores.get("My min"))
        self.assertIsNone(summary.scores.get("My max"))

        self.verify_brainstore_summary(experiment)

        experiment = braintrust.init(project="weighted_summary")
        experiment.log(input=1, output=2, scores={"score1": None, "score2": None, "score3": None})
        summary = experiment.summarize()
        self.assertIsNone(summary.scores.get("score1"))
        self.assertIsNone(summary.scores.get("score2"))
        self.assertIsNone(summary.scores.get("My avg"))
        self.assertIsNone(summary.scores.get("My min"))
        self.assertIsNone(summary.scores.get("My max"))

        self.verify_brainstore_summary(experiment)

        experiment = braintrust.init(project="weighted_summary")
        experiment.log(input=1, output=2, scores={"score1": 1, "score2": 0.5, "score3": 0})
        summary = experiment.summarize()
        self.assertEqual(summary.scores["score1"].score, 1)
        self.assertEqual(summary.scores["score2"].score, 0.5)
        self.assertEqual(summary.scores["score3"].score, 0)
        self.assertEqual(summary.scores["My avg"].score, (0.5 * 3 + 1) / 4)
        self.assertEqual(summary.scores["My min"].score, 0.5)
        self.assertEqual(summary.scores["My max"].score, 1)

        self.verify_brainstore_summary(experiment)

        experiment = braintrust.init(project="weighted_summary")
        experiment.log(input=1, output=2, scores={"score1": 1, "score3": None})
        summary = experiment.summarize()
        self.assertEqual(summary.scores["score1"].score, 1)
        self.assertIsNone(summary.scores.get("score2"))
        self.assertEqual(summary.scores["My avg"].score, 1)
        self.assertEqual(summary.scores["My min"].score, 1)
        self.assertEqual(summary.scores["My max"].score, 1)

        self.verify_brainstore_summary(experiment)

        experiment = braintrust.init(project="weighted_summary")
        experiment.log(input=1, output=2, scores={"score1": None, "score2": 0.5, "score3": None})
        summary = experiment.summarize()
        self.assertEqual(summary.scores["score2"].score, 0.5)
        self.assertIsNone(summary.scores.get("score1"))
        self.assertEqual(summary.scores["My avg"].score, 0.5)
        self.assertEqual(summary.scores["My min"].score, 0.5)
        self.assertEqual(summary.scores["My max"].score, 0.5)

        self.verify_brainstore_summary(experiment)

        experiment = braintrust.init(project="weighted_summary")
        with experiment.start_span() as root_span:
            with root_span.start_span() as span:
                span.log(input="a", output="out", scores={"score1": 0.5})
            with root_span.start_span() as span:
                span.log(input="a", output="out", scores={"score2": 1})

        summary = experiment.summarize()
        self.assertEqual(summary.scores["score1"].score, 0.5)
        self.assertEqual(summary.scores["My avg"].score, (0.5 + 1 * 3) / 4)
        self.assertEqual(summary.scores["My min"].score, 0.5)
        self.assertEqual(summary.scores["My max"].score, 1)

        self.verify_brainstore_summary(experiment)

    def test_comparison_key(self):
        experiment = braintrust.init("project_diff")
        experiment.log(input={"key": "a", "noise": "asdf"}, output="1", scores={"foo": 0.5})

        experiment = braintrust.init("project_diff")
        experiment.log(input={"key": "a", "noise": "fasdfasf"}, output="2", scores={"foo": 1})
        summary = experiment.summarize()
        self.assertEqual(summary.scores["foo"].improvements, 0)

        resp = requests.patch(
            make_v1_url("project", experiment.project.id),
            json={
                "settings": {
                    "comparison_key": "input.key",
                },
            },
            headers=dict(Authorization="Bearer " + self.org_api_key),
        )
        self.assertTrue(resp.ok, resp.text)

        summary = experiment.summarize()
        self.assertEqual(summary.scores["foo"].improvements, 1)

        self.verify_brainstore_summary(experiment)

    def test_delete_base_experiment(self):
        e0 = braintrust.init("p")
        e0.log(input="foo", output="bar", scores=dict(good=0.5))
        e1 = braintrust.init("p")
        e1.log(input="foo", output="bar", scores=dict(good=0.2))
        e2 = braintrust.init("p", base_experiment_id=e1.id)
        e2.log(input="foo", output="bar", scores=dict(good=0.8))

        # Make sure to flush these experiments.
        e0.summarize()
        e1.summarize()

        summary = e2.summarize()
        self.assertAlmostEqual(summary.scores["good"].diff, 0.6)

        # Now delete experiment 1.
        self.run_request("delete", f"{LOCAL_API_URL}/v1/experiment/{e1.id}")

        # Flush the object cache to force a re-fetch of the object info.
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/flush-object-cache",
            json=dict(org_id=self.org_id, object_type="organization", object_id=self.org_id),
        )

        # Now the summary should compare against e0.
        summary = e2.summarize()
        self.assertAlmostEqual(summary.scores["good"].diff, 0.3)

        self.verify_brainstore_summary(e0)
        self.verify_brainstore_summary(e2)

    def test_get_base_experiment_not_from_different_project(self):
        # Create an experiment in one project.
        e0 = braintrust.init("p")
        e0.log(input="foo", output="bar", scores=dict(good=0.5))
        # Make sure e0 is created first.
        braintrust.flush()

        # Create some experiments in another project.
        for _ in range(10):
            e_other = braintrust.init("p2")
            e_other.log(input="foo", output="bar", scores=dict(good=0.5))
        braintrust.flush()

        e1 = braintrust.init("p")
        e1.log(input="foo", output="bar", scores=dict(good=0.2))
        braintrust.flush()

        # Create some experiments in another project.
        for _ in range(10):
            e_other = braintrust.init("p2")
            e_other.log(input="foo", output="bar", scores=dict(good=0.5))
            e_other_exp_id = e_other.id
            e_other_project_id = e_other.project.id
        braintrust.flush()

        # Set the baseline experiment for p2 to e_other.
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/project/{e_other_project_id}",
            json={
                "settings": {"baseline_experiment_id": e_other_exp_id},
            },
        )

        # Make sure that the base experiment for e1 is e0.
        resp = self.run_request("post", f"{LOCAL_APP_URL}/api/base_experiment/get_id", json={"id": e1.id})
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["base_exp_id"], e0.id)

    def test_duration(self):
        # First, test an experiment using Eval(). The duration metric should not include the 1 second of slow scoring.
        result = braintrust.Eval(
            "test_duration",
            data=[{"input": "a", "expected": "b"}],
            task=lambda input: "b",
            scores=[slow_scorer],
        )
        print(result.summary)
        self.assertLess(result.summary.metrics["duration"].metric, 1)

        # Next, run an experiment without Eval() spans. The duration metric should include the 2 seconds of slow scoring.
        experiment = braintrust.init("test_duration")
        with experiment.start_span() as span:
            span.log(input="a", output="b", scores=dict(slow_scorer=slow_scorer("b", "b")))
        summary = experiment.summarize()
        self.assertGreater(summary.metrics["duration"].metric, 1)
        self.assertIsNone(summary.metrics.get("__bt_internal_cached"))

    def test_cached_metric(self):
        experiment = braintrust.init("test_cached_metric")

        with experiment.start_span(type="llm") as span:
            span.log(input="a", output="b", scores=dict(slow_scorer=slow_scorer("b", "b")))
        summary = experiment.summarize()
        self.assertIsNone(summary.metrics.get("__bt_internal_cached"))

    def test_many_scores(self):
        experiment = braintrust.init("many scores")

        scores = [f"score_{i}" for i in range(100)]

        with experiment.start_span("task") as span:
            span.log(input="a", output="b", scores={name: 0.5 for name in scores})

        summary = experiment.summarize()
        for score in scores:
            self.assertEqual(summary.scores[score].score, 0.5)

    def test_explicit_baseline(self):
        baseline_experiment = braintrust.init("baseline")
        baseline_experiment.log(input="a", output="b", scores=dict(score0=0.5))
        baseline_experiment.summarize()

        self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/project/patch_id",
            json={
                "id": baseline_experiment.project.id,
                "settings": {"baseline_experiment_id": baseline_experiment.id},
            },
        )

        for score in [1, 0.9]:
            experiment = braintrust.init("baseline")
            experiment.log(input="a", output="b", scores=dict(score0=score))
            summary = experiment.summarize()
            self.assertEqual(summary.comparison_experiment_name, baseline_experiment.name)
            self.assertGreater(summary.scores["score0"].improvements or 0, 0)

    def test_set_baseline_experiment(self):
        # Create first experiment (baseline)
        experiment1 = braintrust.init("baseline", "exp1")
        experiment1.log(input="a", output="b", scores=dict(score0=0.5))
        experiment1.summarize()

        # Create second experiment
        experiment2 = braintrust.init("baseline", "exp2")
        experiment2.log(input="a", output="b", scores=dict(score0=0.7))
        experiment2.summarize()

        # Set the first experiment as baseline
        self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/project/patch_id",
            json={
                "id": experiment1.project.id,
                "settings": {"baseline_experiment_id": experiment1.id},
            },
        )

        # Create third experiment and verify it compares to the first (baseline)
        experiment3 = braintrust.init("baseline", "exp3")
        experiment3.log(input="a", output="b", scores=dict(score0=0.8))
        summary3 = experiment3.summarize()
        self.assertEqual(summary3.comparison_experiment_name, experiment1.name)
        self.assertAlmostEqual(summary3.scores["score0"].diff, 0.3)  # 0.8 - 0.5 #type: ignore
        self.assertGreater(summary3.scores["score0"].improvements or 0, 0)

        # Delete the first experiment (baseline)
        self.run_request("delete", f"{LOCAL_API_URL}/v1/experiment/{experiment1.id}")

        # Flush the object cache to force a re-fetch of the object info
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/flush-object-cache",
            json=dict(org_id=self.org_id, object_type="organization", object_id=self.org_id),
        )

        # Create a new experiment and verify it compares to experiment3 (most recent)
        experiment4 = braintrust.init("baseline", "exp4")
        experiment4.log(input="a", output="b", scores=dict(score0=0.6))
        summary4 = experiment4.summarize()
        self.assertEqual(summary4.comparison_experiment_name, experiment3.name)
        self.assertAlmostEqual(summary4.scores["score0"].diff, -0.2)  # 0.6 - 0.8 #type: ignore
        self.assertEqual(summary4.scores["score0"].improvements or 0, 0)
        self.assertEqual(summary4.scores["score0"].regressions or 0, 1)

        # Verify brainstore summaries
        self.verify_brainstore_summary(experiment2)
        self.verify_brainstore_summary(experiment3)
        self.verify_brainstore_summary(experiment4)

    def test_conflicting_column_name(self):
        experiment = braintrust.init("conflicting_column_name")
        requests.post(
            f"{LOCAL_API_URL}/v1/column",
            json={
                "object_id": experiment.project.id,
                "object_type": "project",
                "subtype": "experiment",
                "variant": "experiment",
                "name": "Output",
                "expr": "output",
            },
            headers=dict(Authorization="Bearer " + self.org_api_key),
        )

        experiment.log(input="a", output="b", scores=dict(score0=0.5))
        summary = experiment.summarize()
        self.assertEqual(summary.scores["score0"].score, 0.5)

        # Load the summary from the database.
        self.verify_brainstore_summary(experiment)

        # Also, load it as parquet
        args = dict(query=f"""select: * | from: experiment('{experiment.id}') summary""", fmt="parquet")
        self.run_request("post", f"{LOCAL_API_URL}/btql", json=args)

    def test_no_score_summary(self):
        def task(input):
            return input * 2

        def test_scorer():
            return 1

        result = braintrust.Eval(
            "test_no_score_summary",
            data=[braintrust.EvalCase(input=1, expected=2)],
            task=task,
            scores=[test_scorer],
            summarize_scores=False,
        )

        # Verify individual results still have scores
        self.assertEqual(result.results[0].scores, {"test_scorer": 1})
        # But summary should have empty scores and metrics
        self.assertEqual(result.summary.scores, {})
        self.assertEqual(result.summary.metrics, {})

    def test_trial_overcounting(self):
        experiment1 = braintrust.init("trial_overcounting")
        experiment2 = braintrust.init("trial_overcounting")

    def test_experiment_comparison_with_identical_inputs(self):
        # Create first experiment with 2 identical input values
        experiment1 = braintrust.init("comparison_test_1")
        for _ in range(2):
            experiment1.log(
                input="This is a test input with exactly twenty tokens in it to match the requirement.",
                output="Some output",
                metrics={"prompt_tokens": 20},
                scores={"foo": 0.5},
            )

        # Create second experiment with the same 2 identical input values
        experiment2 = braintrust.init("comparison_test_2")
        for _ in range(2):
            experiment2.log(
                input="This is a test input with exactly twenty tokens in it to match the requirement.",
                output="Some different output",
                metrics={"prompt_tokens": 20},
                scores={"foo": 0.5},
            )

        experiment1.flush()
        experiment2.flush()

        # Call the experiment-comparison2 endpoint with full=true
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/experiment-comparison2",
            json={"experiment_id": experiment1.id, "base_experiment_id": experiment2.id, "full": True},
        )

        self.assertEqual(response.status_code, 200, f"Failed with response: {response.text}")
        comparison_data = response.json()

        # Each metric should be exactly the same for the two experiments, except the timing metrics
        ignore_metrics = [
            "start",
            "end",
            "duration",
            "llm_duration",
            "__bt_internal_cached",
            "prompt_tokens",
            "completion_tokens",
            "total_tokens",
            "estimated_cost",
            "time_to_first_token",
        ]
        for metric in comparison_data.keys():
            if metric in ignore_metrics:
                continue
            data = comparison_data[metric]
            self.assertEqual(data["avg"], data["compareAvg"], f"Metric {metric} avg is not equal")
            self.assertEqual(data["diffAvg"], 0, f"Metric {metric} diffAvg is not 0")
            self.assertEqual(data["min"], data["compareMin"], f"Metric {metric} min is not equal")
            self.assertEqual(data["diffMin"], 0, f"Metric {metric} diffMin is not 0")
            self.assertEqual(data["max"], data["compareMax"], f"Metric {metric} max is not equal")
            self.assertEqual(data["diffMax"], 0, f"Metric {metric} diffMax is not 0")
            self.assertEqual(data["sum"], data["compareSum"], f"Metric {metric} sum is not equal")
            self.assertEqual(data["diffSum"], 0, f"Metric {metric} diffSum is not 0")

        print(comparison_data.keys())

    def test_big_summary(self):
        LIMIT = 1010  # This is a bit bigger than the default limit of 1000
        experiment = braintrust.init("big_summary")
        for i in range(LIMIT):
            if i < LIMIT / 2:
                score = 1
            else:
                score = 0
            experiment.log(input="a", output="b", scores=dict(score0=score))

        summary = experiment.summarize()
        self.assertAlmostEqual(summary.scores["score0"].score, 0.5)

    def test_summarize_public_experiment(self):
        experiment = braintrust.init("p", description="something cool", is_public=True)
        experiment.log(input="hello", output="world", scores={})
        experiment.flush()

        # Create a custom column on this experiment.
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/column",
            json=dict(
                object_id=experiment.project.id,
                object_type="project",
                subtype="experiment",
                variant="experiment",
                name="my_custom_column",
                expr="input",
            ),
        )

        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"""select: * | from: experiment('{experiment.id}') summary""",
                fmt="json",
            ),
        ).json()
        self.assertEqual(len(response["data"]), 1)
        self.assertIn("my_custom_column", response["data"][0])

        # Try requesting as an anonymous user. Should work and still include the
        # custom column.
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"""select: * | from: experiment('{experiment.id}') summary""",
                fmt="json",
            ),
            headers=dict(Authorization="Bearer null"),
        ).json()
        self.assertEqual(len(response["data"]), 1)
        self.assertIn("my_custom_column", response["data"][0])

    def test_llm_span_metrics_underflow(self):
        experiment = braintrust.init(project="underflow_test")
        with experiment.start_span() as root_span:
            with root_span.start_span(type="llm") as llm_span:
                llm_span.log(
                    input="test prompt",
                    output="test response",
                    metrics={
                        "prompt_tokens": 100,
                        "prompt_cached_tokens": 300,
                    },
                    metadata={"model": "claude-sonnet-4-20250514"},
                )

        summary = experiment.summarize()
        self.assertTrue(summary.metrics["estimated_cost"].metric < 100)
        self.verify_brainstore_summary(experiment)


def slow_scorer(output, expected):
    time.sleep(2)
    return 1.0
