import time
from uuid import uuid4

import braintrust
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase
from tests.bt_services.test_functions import FunctionTestBase


class PlayxTest(FunctionTestBase):
    @parameterized.expand([(False,), (True,)])
    def test_playx(self, use_brainstore):
        project_name = "playx-test"
        dataset_name = "playx-test"

        playground_meta = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/prompt_session/register",
            json=dict(
                org_name=self.org_name,
                project_name=project_name,
                session_name="foo",
            ),
        ).json()
        prompt_session_id = playground_meta["id"]

        dataset = braintrust.init_dataset(project_name, dataset_name)
        for i in range(10):
            dataset.insert(input=i, expected=f"output {i}")
        braintrust.flush()

        project_id = dataset.project.id

        # Unlike the playground, we'll use a "global" function, but that doesn't matter.
        function_record = self._insert_function(
            project_id=project_id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Return exactly the following string: output {{input}}"}],
                },
                "options": {
                    "model": "gpt-4o",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug="echo-prompt",
        )

        generation = uuid4().hex
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/eval",
            json={
                "project_id": project_id,
                "data": {"project_name": project_name, "dataset_name": dataset_name},
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [
                    {"global_function": "Levenshtein"},
                    {"global_function": "Factuality"},
                ],
                "parent": {
                    "object_type": "playground_logs",
                    "object_id": prompt_session_id,
                    "propagated_event": {
                        "span_attributes": {
                            "generation": generation,
                        },
                    },
                },
            },
        )

        # Once the eval completes, query all the data from playground logs
        query_args = {}
        if use_brainstore:
            query_args = self.brainstore_query_args()

            # If you want to test that backfilling works correctly, you can uncomment these lines. In CI,
            # it's too flaky to do this though.
            # self.catchup_with_compaction_then_force_vacuum(f"playground_logs:{prompt_session_id}")
            # # Turning off realtime ensures that we test the postgres backfill logic
            # query_args = self.brainstore_query_args(realtime=False)
        else:
            query_args["use_brainstore"] = False  # TODO(BRAINSTORE): Fix this to use brainstore

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"from: playground_logs('{prompt_session_id}') | select: id, is_root, input, _xact_id, origin, span_attributes.generation AS generation",
                **query_args,
            },
        )

        data = resp.json()["data"]
        id_to_xact_id = {}
        for row in data:
            self.assertEqual(row["generation"], generation)
            id_to_xact_id[row["id"]] = row["_xact_id"]

        first_row = [x for x in data if x["is_root"] and x["input"] == 0][0]

        self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/eval",
            json={
                "project_id": project_id,
                "data": {
                    "data": [
                        {
                            "id": first_row["origin"]["id"],
                            "upsert_id": first_row["id"],
                            "input": 0,
                            "expected": "output 0",
                        }
                    ]
                },
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [
                    {"global_function": "Levenshtein"},
                    {"global_function": "Factuality"},
                ],
                "parent": {
                    "object_type": "playground_logs",
                    "object_id": prompt_session_id,
                    "propagated_event": {
                        "origin": {
                            "object_type": "dataset",
                            "object_id": dataset.id,
                            "id": first_row["origin"]["id"],
                            "xact_id": first_row["origin"]["_xact_id"],
                        },
                        "span_attributes": {
                            "generation": generation,
                        },
                    },
                },
            },
        )

        # See comment above. You can uncomment this if you want to test that backfilling works correctly.
        # if use_brainstore:
        #     self.catchup_with_compaction_then_force_vacuum(f"playground_logs:{prompt_session_id}")

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"from: playground_logs('{prompt_session_id}') | select: id, is_root, _xact_id, origin, span_attributes.generation AS generation",
                **query_args,
            },
        )
        data = resp.json()["data"]

        for row in data:
            self.assertEqual(row["generation"], generation)

            if not row["is_root"]:
                continue

            if row["id"] == first_row["id"]:
                self.assertNotEqual(row["_xact_id"], id_to_xact_id[row["id"]])
            else:
                self.assertEqual(row["_xact_id"], id_to_xact_id[row["id"]])

    def catchup_with_compaction(self, object_id):
        self.catchupBrainstoreWithCompaction(object_id)

    def catchup_with_compaction_then_force_vacuum(self, object_id):
        self.catchup_with_compaction(object_id)
        self.vacuumBrainstore([object_id])
