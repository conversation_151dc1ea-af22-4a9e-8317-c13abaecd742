import unittest
from typing import Literal

import requests
from braintrust import Attachment, ReadonlyAttachment, init, init_dataset
from parameterized import parameterized

from tests.braintrust_app_test_base import BraintrustAppTestBase


class AttachmentReadTest(BraintrustAppTestBase):
    """
    This tests the read path of attachments. Note that the write path is covered
    by expect tests.
    """

    def setUp(self):
        super().setUp()

        # All of these tests depend on attachments through S3 being set up.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("")

    @parameterized.expand(["Experiment", "ReadonlyExperiment", "Dataset"])
    def test_iterate(self, name: Literal["Experiment", "ReadonlyExperiment", "Dataset"]):
        original_attachment = Attachment(
            data=b"hello world",
            filename="greeting.txt",
            content_type="text/plain",
        )
        input = {
            "attachment": original_attachment,
        }

        if name == "Experiment" or name == "ReadonlyExperiment":
            container = init("project name", name)
            id = container.log(input=input, output="ignored", scores={})
            container.flush()
            if name == "Experiment":
                # Re-use the same object to read the Experiment.
                pass
            if name == "ReadonlyExperiment":
                container = init("project name", name, open=True)
        elif name == "Dataset":
            container = init_dataset("project name", "dataset name")
            id = container.insert(input=input)
            container.flush()
            container = init_dataset("project name", "dataset name")
        else:
            raise ValueError(name)

        container_it = iter(container)
        record = next(container_it)

        self.assertEqual(record["id"], id)

        attachment = (record.get("input") or {}).get("attachment")
        assert isinstance(attachment, ReadonlyAttachment)

        self.assertIsNot(attachment, original_attachment)
        self.assertDictEqual(attachment.reference, original_attachment.reference)
        self.assertEqual(attachment.data, b"hello world")
        self.assertDictEqual(attachment.status(), {"upload_status": "done"})
        fetched_data = requests.get(attachment.metadata()["downloadUrl"])
        self.assertEqual(attachment.data, fetched_data.content)

        with self.assertRaises(StopIteration):
            record = next(container_it)
