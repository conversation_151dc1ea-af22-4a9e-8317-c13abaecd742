import braintrust
import requests

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase


class LegacyEndpointsTest(BraintrustAppTestBase):
    def _checked_request(self, verb, *args, **kwargs):
        resp = getattr(requests, verb)(*args, **{"headers": {"Authorization": f"Bearer {self.org_api_key}"}, **kwargs})
        self.assertTrue(resp.ok, f"{resp.status_code} error: {resp.text}")
        return resp

    def test_crud_base_experiment(self):
        e0 = braintrust.init("p")
        e0.log(input="foo", output="bar", scores=dict(good=0.5))
        e0.flush()

        e1 = braintrust.init("p")
        e1.log(input="foo", output="bar", scores=dict(good=0.9))
        e1.flush()

        # Make sure the direct-to-app base_experiment result matches the API
        # result.
        app_resp = self._checked_request(
            "get", f"{LOCAL_APP_URL}/api/base_experiment/get_id", json=dict(id=e1.id)
        ).json()
        api_resp = self._checked_request("get", f"{LOCAL_API_URL}/crud/base_experiments", params=dict(id=e1.id)).json()
        self.assertEqual([app_resp], api_resp)
        self.assertEqual(app_resp["id"], e1.id)
        self.assertEqual(app_resp["base_exp_id"], e0.id)
