import braintrust

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase


class ErrorContextTest(BraintrustAppTestBase):
    def test_error_context(self):
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/self/get_error_context",
            json={},
        ).json()
        self.assertEqual(
            resp,
            {
                "userId": self.user_id,
                "userEmail": self.user_email,
                "orgName": self.org_name,
            },
        )
