import braintrust
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase


class CustomHeaderAdapter(HTTPAdapter):
    def __init__(self, api_key):
        super().__init__()

        self.api_key = api_key

    def send(self, request, **kwargs):
        # Modify the request here to add custom headers
        request.headers.update({"Authorization": "Bearer " + self.api_key})

        return super().send(request, **kwargs)


class CustomAdapter(BraintrustAppTestBase):
    def test_custom_adapter(self):
        adapter = CustomHeaderAdapter(api_key=self.org_api_key)
        try:
            braintrust.set_http_adapter(adapter)
            braintrust.login(app_url=LOCAL_APP_URL, api_key="bogus", force_login=True)

            experiment = braintrust.init_experiment(project="custom adapter")
            experiment.log(input=1, output=2, scores={})
            experiment.flush()
            summary = experiment.summarize()

            r = braintrust.init_experiment("custom adapter", experiment=summary.experiment_name, open=True)
            self.assertEqual(len(list(r)), 1)
        finally:
            braintrust.set_http_adapter(None)

        self.assertRaises(Exception, lambda: braintrust.login(api_key="bogus2", force_login=True))
