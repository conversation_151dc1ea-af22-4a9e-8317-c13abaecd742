import braintrust
import pydantic

project = braintrust.projects.create(name="prompt with tools")


def add(a: int, b: int) -> int:
    return a + b


class Input(pydantic.BaseModel):
    a: int
    b: int


calculator = project.tools.create(
    handler=add,
    name="Adder",
    slug="adder",
    description="A simple adder",
    parameters=Input,
)

project.prompts.create(
    name="Calculator",
    slug="calculator",
    description="A simple calculator",
    model="gpt-4o",
    messages=[
        {
            "role": "user",
            "content": "What is {{formula}}? Just return the number, nothing else.",
        }
    ],
    tools=[calculator],
)
