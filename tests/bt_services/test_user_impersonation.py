import braintrust
import requests
from braintrust.http_headers import BT_IMPERSONATE_USER

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase


def make_url(suffix):
    return f"{LOCAL_API_URL}/v1/{suffix}"


def get_request_headers(api_key):
    return dict(Authorization=f"Bearer {api_key}")


class UserImpersonationTest(BraintrustAppTestBase):
    def _checked_request(self, verb, *args, api_key=None, expect_error=False, **kwargs):
        resp = getattr(requests, verb)(
            *args, **{"headers": get_request_headers(api_key or self.org_api_key), **kwargs}
        )
        if expect_error:
            self.assertFalse(resp.ok)
        else:
            self.assertTrue(resp.ok, f"{resp.status_code} error: {resp.text}")
        return resp

    def get_owner_role_id(self):
        results = self._checked_request("get", make_url("role"), params=dict(role_name="Owner")).json()
        self.assertEqual(len(results["objects"]), 1)
        self.assertEqual(results["objects"][0]["name"], "Owner")
        return results["objects"][0]["id"]

    def test_impersonate_less_privileged_user(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        project0 = self._checked_request("post", make_url("project"), json=dict(name="test0")).json()
        project1 = self._checked_request("post", make_url("project"), json=dict(name="test1")).json()
        ROW_CONTENTS = dict(events=[dict(input="foo", output="bar")])

        # The root user can write to both projects.
        self._checked_request("post", make_url(f"project_logs/{project0['id']}/insert"), json=ROW_CONTENTS)
        self._checked_request("post", make_url(f"project_logs/{project1['id']}/insert"), json=ROW_CONTENTS)

        # User should only have permission to write to project0.
        self._checked_request(
            "post",
            make_url("acl"),
            json=dict(object_type="project", object_id=project0["id"], user_id=user_id, permission="update"),
        )
        self._checked_request(
            "post", make_url(f"project_logs/{project0['id']}/insert"), json=ROW_CONTENTS, api_key=user_api_key
        )
        self._checked_request(
            "post",
            make_url(f"project_logs/{project1['id']}/insert"),
            json=ROW_CONTENTS,
            api_key=user_api_key,
            expect_error=True,
        )

        # The root user impersonating the less-privileged user should have their
        # same privileges. We can use either the user_id or the user's email.
        impersonate_headers0 = {"Authorization": f"Bearer {self.org_api_key}", BT_IMPERSONATE_USER: user_id}
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select email from users where id = %s", (user_id,))
                user_email = cursor.fetchone()[0]
        impersonate_headers1 = {"Authorization": f"Bearer {self.org_api_key}", BT_IMPERSONATE_USER: user_email}

        # User impersonation only works if the impersonating user is granted
        # 'Owner' access.
        for impersonate_headers in [impersonate_headers0, impersonate_headers1]:
            self._checked_request(
                "post",
                make_url(f"project_logs/{project0['id']}/insert"),
                json=ROW_CONTENTS,
                headers=impersonate_headers,
                expect_error=True,
            )
        self._checked_request(
            "post",
            make_url("acl"),
            json=dict(
                object_type="organization",
                object_id=self.org_id,
                user_id=self.user_id,
                role_id=self.get_owner_role_id(),
            ),
        )
        for impersonate_headers in [impersonate_headers0, impersonate_headers1]:
            self._checked_request(
                "post",
                make_url(f"project_logs/{project0['id']}/insert"),
                json=ROW_CONTENTS,
                headers=impersonate_headers,
            )
            self._checked_request(
                "post",
                make_url(f"project_logs/{project1['id']}/insert"),
                json=ROW_CONTENTS,
                headers=impersonate_headers,
                expect_error=True,
            )

    def test_impersonation_requires_full_org_membership(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id)
        self._checked_request(
            "post",
            make_url("acl"),
            json=dict(
                object_type="organization",
                object_id=self.org_id,
                user_id=self.user_id,
                role_id=self.get_owner_role_id(),
            ),
        )
        project = self._checked_request("post", make_url("project"), json=dict(name="test")).json()

        # The root user should be able to impersonate the other user, but not
        # the other way around, since the other user is not directly granted
        # ownership.
        root_impersonate_headers = {"Authorization": f"Bearer {self.org_api_key}", BT_IMPERSONATE_USER: user_id}
        user_impersonate_headers = {"Authorization": f"Bearer {user_api_key}", BT_IMPERSONATE_USER: self.user_id}

        self._checked_request("get", make_url(f"project/{project['id']}"), headers=root_impersonate_headers)
        resp = self._checked_request(
            "get", make_url(f"project/{project['id']}"), headers=user_impersonate_headers, expect_error=True
        )
        self.assertIn("Insufficient privileges to impersonate user", resp.text)

        # But if we add the other user to a different org that our original user
        # is not part of, then they can no longer impersonate, even though we
        # are only querying for the original object.
        other_org_id, _ = self.createOrg()
        self.addUserToOrg(user_id, other_org_id)
        self._checked_request(
            "get", make_url(f"project/{project['id']}"), headers=root_impersonate_headers, expect_error=True
        )

        # But if we add the original user to this new org and grant them
        # org-level ownership, it should be possible again.
        self.addUserToOrg(self.user_id, other_org_id)
        self_user_other_org_api_key = self.createUserOrgApiKey(self.user_id, other_org_id)
        self._checked_request(
            "post",
            make_url("acl"),
            api_key=self_user_other_org_api_key,
            json=dict(
                object_type="organization",
                object_id=other_org_id,
                user_id=self.user_id,
                role_id=self.get_owner_role_id(),
            ),
        )
        self._checked_request("get", make_url(f"project/{project['id']}"), headers=root_impersonate_headers)

        # Vacuous truth: we should be able to impersonate a user who is not part
        # of any org, but we will fail to access the object due to insufficient
        # privilege (a different error message).
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("delete from members where user_id = %s", (user_id,))
        resp = self._checked_request(
            "get", make_url(f"project/{project['id']}"), headers=root_impersonate_headers, expect_error=True
        )
        self.assertNotIn("Insufficient privileges to impersonate user", resp.text)

    def test_impersonation_against_app_directly(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id)
        self._checked_request(
            "post",
            make_url("acl"),
            json=dict(
                object_type="organization",
                object_id=self.org_id,
                user_id=self.user_id,
                role_id=self.get_owner_role_id(),
            ),
        )
        project = self._checked_request("post", make_url("project"), json=dict(name="test")).json()

        # Should be able to get object info as the other user.
        impersonate_headers = {"Authorization": f"Bearer {self.org_api_key}", BT_IMPERSONATE_USER: user_id}
        resp = requests.post(
            f"{LOCAL_APP_URL}/api/self/get_object_info",
            headers=impersonate_headers,
            json=dict(object_type="project", object_ids=[project["id"]]),
        )
        self.assertTrue(resp.ok, resp.text)
        object_resp = resp.json()
        self.assertEqual(len(object_resp), 1)
        self.assertEqual(object_resp[0]["object_id"], project["id"])
