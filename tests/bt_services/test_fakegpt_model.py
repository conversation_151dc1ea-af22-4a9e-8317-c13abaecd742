import unittest

import braintrust
import requests

from tests.braintrust_app_test_base import LOCAL_APP_URL, TEST_ARGS, BraintrustAppTestBase, get_test_proxy_base_url

FAKE_MODEL = "ft:gpt-3.5-turbo-0125:braintrust-data::9Rac3rrg"


class FakeGPTModelTest(BraintrustAppTestBase):
    """Test the fakegpt model registry when BRAINTRUST_ENABLE_TEST_MODELS is set in the app"""

    def setUp(self):
        super().setUp()
        self.proxy_url = braintrust.logger._state.proxy_url

    def tearDown(self):
        if TEST_ARGS.get("update"):
            requests.get(f"{get_test_proxy_base_url()}/proxy/dump-cache")
        super().tearDown()

    def test_fakegpt_model_availability(self):
        """Test that we can check if the fakegpt model is available in the model list"""
        # Fetch the model list from the app
        resp = requests.get(f"{LOCAL_APP_URL}/api/models/model_list.json")
        self.assertEqual(resp.status_code, 200)

        models = resp.json()
        self.assertIn(FAKE_MODEL, models)

        resp = self.run_request(
            "post",
            f"{get_test_proxy_base_url()}/v1/auto",
            json={
                "model": FAKE_MODEL,
                "temperature": 0,
                "messages": [{"role": "system", "content": "what is 1+100"}],
            },
        )
        print(resp.content)
        self.assertTrue(resp.ok, msg=f"Failed to run request with fakegpt model: {resp.text}")


if __name__ == "__main__":
    unittest.main()
