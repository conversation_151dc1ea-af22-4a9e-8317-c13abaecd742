import dataclasses
import unittest
from uuid import uuid4

from braintrust.span_identifier_v1 import SpanComponentsV1, SpanObjectTypeV1, SpanRowIdsV1
from braintrust.span_identifier_v2 import SpanComponentsV2, SpanObjectTypeV2, SpanRowIdsV2


class SpanComponentsV2Test(unittest.TestCase):
    def test_span_row_ids(self):
        # None of the components can be an empty string.
        SpanRowIdsV2(row_id="x", span_id="x", root_span_id="x")
        with self.assertRaises(Exception):
            SpanRowIdsV2(row_id="", span_id="x", root_span_id="x")
        with self.assertRaises(Exception):
            SpanRowIdsV2(row_id="x", span_id="", root_span_id="x")
        with self.assertRaises(Exception):
            SpanRowIdsV2(row_id="x", span_id="x", root_span_id="")

    def test_no_row_id(self):
        for object_type in SpanObjectTypeV2:
            object_id = str(uuid4())
            compute_object_metadata_args = dict(foo="bar", hello=dict(goodbye="yes"))
            components = SpanComponentsV2(object_type=object_type, object_id=object_id)
            self.assertEqual(SpanComponentsV2.from_str(components.to_str()), components)
            components = SpanComponentsV2(
                object_type=object_type, compute_object_metadata_args=compute_object_metadata_args
            )
            self.assertEqual(SpanComponentsV2.from_str(components.to_str()), components)
            components = SpanComponentsV2(
                object_type=object_type, object_id=object_id, compute_object_metadata_args=compute_object_metadata_args
            )
            self.assertEqual(SpanComponentsV2.from_str(components.to_str()), components)

    def test_with_row_id(self):
        object_id, row_id, span_id, root_span_id = [str(uuid4()) for _ in range(4)]
        compute_object_metadata_args = dict(foo="bar", hello=dict(goodbye="yes"))
        components = SpanComponentsV2(
            object_type=SpanObjectTypeV2.EXPERIMENT,
            object_id=object_id,
            compute_object_metadata_args=compute_object_metadata_args,
            row_ids=SpanRowIdsV2(row_id=row_id, span_id=span_id, root_span_id=root_span_id),
        )
        self.assertEqual(SpanComponentsV2.from_str(components.to_str()), components)

        # Should also work if row_id is not a UUID.
        components = SpanComponentsV2(
            object_type=SpanObjectTypeV2.EXPERIMENT,
            object_id=object_id,
            compute_object_metadata_args=compute_object_metadata_args,
            row_ids=SpanRowIdsV2(row_id="hello world", span_id=span_id, root_span_id=root_span_id),
        )
        self.assertEqual(SpanComponentsV2.from_str(components.to_str()), components)

        # But all the other IDs must be a UUID.
        def roundtrip_bad_component(object_id=None, **row_id_kwargs):
            x = str(uuid4())
            if object_id is None:
                object_id = x
            components = SpanComponentsV2(
                object_type=SpanObjectTypeV2.EXPERIMENT,
                object_id=object_id,
                compute_object_metadata_args=compute_object_metadata_args,
                row_ids=SpanRowIdsV2(**{**dict(row_id=x, span_id=x, root_span_id=x), **row_id_kwargs}),
            )
            SpanComponentsV2.from_str(components.to_str())

        with self.assertRaises(Exception):
            roundtrip_bad_component(object_id="hello")
        with self.assertRaises(Exception):
            roundtrip_bad_component(span_id="hello")
        with self.assertRaises(Exception):
            roundtrip_bad_component(root_span_id="hello")

    def test_backwards_compatible(self):
        object_id, row_id, span_id, root_span_id = [str(uuid4()) for _ in range(4)]
        span_components_old = SpanComponentsV1(
            object_type=SpanObjectTypeV1.EXPERIMENT,
            object_id=object_id,
            row_ids=SpanRowIdsV1(row_id=row_id, span_id=span_id, root_span_id=root_span_id),
        )
        span_components_new = SpanComponentsV2.from_str(span_components_old.to_str())
        self.assertEqual(span_components_new.object_type.value, span_components_old.object_type.value)
        self.assertEqual(span_components_new.object_id, span_components_old.object_id)
        self.assertEqual(
            dataclasses.asdict(span_components_new.row_ids), dataclasses.asdict(span_components_old.row_ids)
        )
        self.assertIsNone(span_components_new.compute_object_metadata_args)
