import io
import time

import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


class SyncFlushTest(BraintrustAppTestBase):
    def test_fail_login_async_flush(self):
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            out_f = io.StringIO()
            custom_bg_logger.outfile = out_f
            custom_bg_logger.num_tries = 3
            experiment = braintrust.init(project_id="bogus")
            # We start a span instead of logging an experiment row, because the
            # latter could potentially get split over two logging statements,
            # which messes up our assertion at the end.
            experiment.start_span(input="a", output="b", scores=dict())
            experiment.flush()
            with self.assertRaises(Exception):
                _ = experiment.id

        self.assertEqual(2, out_f.getvalue().count("Retrying"), out_f.getvalue())

    def test_fail_login_sync_flush(self):
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            out_f = io.StringIO()
            custom_bg_logger.outfile = out_f
            custom_bg_logger.num_tries = 2
            custom_bg_logger.sync_flush = True

            experiment = braintrust.init(project_id="bogus")
            experiment.log(input="a", output="b", scores=dict())
            # Sleep to make sure no background thread tries (and fails) to flush our
            # record.
            time.sleep(0.1)
            with self.assertRaises(Exception):
                experiment.flush()
        self.assertEqual(1, out_f.getvalue().count("Retrying"), out_f.getvalue())
