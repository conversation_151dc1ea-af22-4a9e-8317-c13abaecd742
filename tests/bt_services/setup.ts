import { afterEach, beforeEach } from "vitest";
import { v4 as uuidv4 } from "uuid";
import { getTestProxyServerConfig } from "@braintrust/local/dev";
import { Pool, PoolClient } from "pg";

export const LOCAL_API_URL = "http://localhost:8000";
export const LOCAL_APP_URL = "http://localhost:3000";

async function createTestOrg(client: PoolClient, proxyUrl: string) {
  const result = await client.query(
    `INSERT INTO organizations (name, proxy_url)
     VALUES ($1, $2)
     RETURNING id, name`,
    [`_unit_test_org_${uuidv4()}`, proxyUrl],
  );

  const orgId = result.rows[0].id;
  const orgName = result.rows[0].name;

  if (process.env.UPDATE) {
    const secrets = [
      ["openai", "OPENAI_API_KEY"],
      ["anthropic", "ANTHROPIC_API_KEY"],
    ];

    for (const [secretType, secretName] of secrets) {
      const apiKey = process.env[secretName];
      if (apiKey) {
        await client.query(
          `INSERT INTO secrets.org_secrets(org_id, type, name, secret)
           VALUES ($1, $2, $3, $4)`,
          [orgId, secretType, secretName, apiKey],
        );
      }
    }
  }

  return { orgId, orgName };
}

async function createUserInOrg(client: PoolClient, orgId: string) {
  const email = `_unit_test_user_${uuidv4()}`;
  const userResult = await client.query(
    `INSERT INTO users(email) VALUES ($1) RETURNING id`,
    [email],
  );
  const userId = userResult.rows[0].id;

  await client.query(
    `SELECT add_member_to_org_unchecked($1, $2, array[get_group_id($2, 'Owners')])`,
    [userId, orgId],
  );

  const keyResult = await client.query(
    `SELECT create_api_key(users.auth_id, $1, 'test') FROM users WHERE id=$2`,
    [orgId, userId],
  );

  return {
    userId,
    apiKey: keyResult.rows[0].create_api_key,
    email,
  };
}

async function tearDownDb(client: PoolClient, orgId: string) {
  await client.query(`DELETE FROM secrets.org_secrets WHERE org_id = $1`, [
    orgId,
  ]);
  await client.query(`DELETE FROM organizations WHERE id = $1`, [orgId]);
}

declare module "vitest" {
  export interface TestContext {
    orgId: string;
    orgName: string;
    userId: string;
    orgApiKey: string;
    userEmail: string;
    proxyUrl: string;
  }
}

const APP_DB_URL =
  process.env.DATABASE_URL ||
  "postgresql://postgres:postgres@localhost:54322/postgres";

const appDb = new Pool({ connectionString: APP_DB_URL });

export const withAppDbClient = async <T>(
  fn: (client: PoolClient) => Promise<T>,
) => {
  let client: PoolClient | undefined = undefined;
  try {
    client = await appDb.connect();
    return await fn(client);
  } finally {
    await client?.release();
  }
};

beforeEach(async (context) => {
  await withAppDbClient(async (client) => {
    const { host, port } = await getTestProxyServerConfig();
    const proxyUrl = `http://${host}:${port}`;
    const { orgId, orgName } = await createTestOrg(client, proxyUrl);
    const { userId, apiKey, email } = await createUserInOrg(client, orgId);

    Object.assign(context, {
      orgId,
      orgName,
      userId,
      orgApiKey: apiKey,
      userEmail: email,
      proxyUrl,
    });
  });
});

afterEach(async ({ orgId, proxyUrl }) => {
  await withAppDbClient(async (client) => {
    await tearDownDb(client, orgId);
  });
  if (process.env.UPDATE) {
    await fetch(`${proxyUrl}/proxy/dump-cache`);
  }
});

export function skipS3() {
  return Boolean(process.env.BT_UNITTEST_SKIP_S3);
}

export function skipBrainstore() {
  return Boolean(process.env.BT_UNITTEST_SKIP_BRAINSTORE);
}
