import braintrust

from tests.bt_services.prompt_import_tools_project.subdir.multiplier import multiplier

from ..exponentiator import exponentiator
from .adder import adder

project = braintrust.projects.create(name="prompt with tools")


project.prompts.create(
    name="Calculator",
    slug="calculator",
    description="A simple calculator",
    model="gpt-4o",
    messages=[
        {
            "role": "user",
            "content": "What is {{formula}}? Just return the number, nothing else.",
        }
    ],
    tools=[adder, multiplier, exponentiator],
)
