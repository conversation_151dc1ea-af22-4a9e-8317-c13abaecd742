import json
import os
import subprocess
import tempfile
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor
from inspect import currentframe, getframeinfo
from uuid import uuid4

import braintrust
import git
from braintrust import gitutil
from braintrust.db_fields import IS_MERGE_FIELD
from braintrust.gitutil import truncate_to_byte_limit
from braintrust_local.api_db_util import get_object_json, log3_raw, log_raw

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def clear_git_caches():
    for attr in dir(gitutil):
        if hasattr(getattr(gitutil, attr), "cache_clear"):
            getattr(gitutil, attr).cache_clear()


class ExperimentInitTest(BraintrustAppTestBase):
    def test_init_bogus_base(self):
        braintrust.init(project="p", base_experiment="foobar")

    # This function should be invoked with the CWD set to an empty temporary
    # directory.
    def _run_init_repo_info_test(self):
        # Create a git repo with an empty initial commit and a remote 'origin'
        # pointing to the local repo itself.
        subprocess.run(["git", "init"])
        subprocess.run(["git", "branch", "-m", "main"])
        subprocess.run(["git", "config", "user.email", "<EMAIL>"])
        subprocess.run(["git", "config", "user.name", "foo"])
        subprocess.run(["git", "commit", "-a", "--allow-empty", "-m", "initial"])
        subprocess.run(["git", "remote", "add", "origin", ".git"])
        subprocess.run(["git", "push", "--set-upstream", "origin", "main"])
        tmpfilename = f"test_init_repo_info.txt"
        with open(tmpfilename, "w") as f:
            f.write("test")
        repo = git.Repo(".")
        repo.index.add([tmpfilename])
        raw_git_diff = subprocess.check_output(["git", "diff", "HEAD"]).decode()
        expected_git_diff = truncate_to_byte_limit(raw_git_diff).strip() or None

        experiment = braintrust.init(project="p")
        experiment_id = experiment.data["id"]
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select repo_info from experiments
                    where id = %s
                    """,
                    [experiment_id],
                )
                result = cursor.fetchone()
        git_diff = result[0].get("git_diff")
        if git_diff is not None:
            git_diff = git_diff.strip()

        self.assertEqual(expected_git_diff, git_diff)

    def test_init_repo_info(self):
        # This test simply checks that the logged `git diff` matches the one we
        # get by running `git diff HEAD` directly. We initialize a git repo in a
        # temporary directory. To ensure the repo is at least somewhat dirty, we
        # temporarily create a file and check it in.
        with tempfile.TemporaryDirectory() as tmpdirname:
            orig_cwd = os.getcwd()
            os.chdir(tmpdirname)
            clear_git_caches()
            try:
                self._run_init_repo_info_test()
            finally:
                clear_git_caches()
                os.chdir(orig_cwd)

    def test_init_metadata(self):
        metadata = {"foo": "bar", "baz": 1}
        experiment = braintrust.init(project="p", metadata=metadata)
        self.assertEqual(metadata, experiment.metadata)
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select metadata from experiments
                    where id = %s
                    """,
                    [experiment.id],
                )
                result = cursor.fetchone()
        self.assertEqual(metadata, result[0])


class ExperimentLoggingTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.experiment = braintrust.init(project="p")

    def tearDown(self):
        self.experiment.flush()
        super().tearDown()

    def test_basic(self):
        for i in range(100):
            self.experiment.log(id=str(i), input=i, output=str(i + 1), scores=dict(correct=1))
        self.experiment.summarize()
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(100, len(rows))
        rows.sort(key=lambda r: int(r["id"]))
        for i, row in enumerate(rows):
            self.assertEqual(str(i), row["id"])
            self.assertEqual(i, row["input"])
            self.assertEqual(str(i + 1), row["output"])
            self.assertEqual(dict(correct=1), row["scores"])

    def test_nulls(self):
        for i in range(10):
            self.experiment.log(
                input=i,
                output=1 if i < 5 else 0,
                expected=1 if i < 5 else 0,
                scores={
                    "all_1s": 1,
                    "half_0s": 1 if i < 5 else 0,
                    "half_nulls": 1 if i < 5 else None,
                    "all_nulls": None,
                    "all_0s": 0,
                },
            )

        results = self.experiment.summarize()
        self.assertEqual(results.scores["all_1s"].score, 1)
        self.assertEqual(results.scores["half_0s"].score, 0.5)
        self.assertEqual(results.scores["half_nulls"].score, 1)
        self.assertNotIn("all_nulls", results.scores)
        self.assertEqual(results.scores["all_0s"].score, 0)

        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(10, len(rows))
        for _, row in enumerate(rows):
            i = row["input"]
            self.assertEqual(1 if i < 5 else 0, row["output"])
            self.assertEqual(1 if i < 5 else 0, row["expected"])
            self.assertEqual(
                {
                    "all_1s": 1,
                    "half_0s": 1 if i < 5 else 0,
                    "half_nulls": 1 if i < 5 else None,
                    "all_nulls": None,
                    "all_0s": 0,
                },
                row["scores"],
            )

    def test_create_span(self):
        for i in range(100):
            with self.experiment.start_span(id=str(i), input=i, scores=dict(correct=1)):
                pass
        self.experiment.summarize()
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(100, len(rows))
        rows.sort(key=lambda r: int(r["id"]))
        for i, row in enumerate(rows):
            self.assertEqual(str(i), row["id"])
            self.assertEqual(i, row["input"])

    def test_span_incremental(self):
        # Try to ensure our spans get logged in separate batches by logging
        # across all spans.
        span_lineno = getframeinfo(currentframe()).lineno + 1
        spans = [self.experiment.start_span(name=str(i), set_current=False) for i in range(100)]
        for i, span in enumerate(spans):
            span.log(input=i)
        for span in spans:
            span.log(scores=dict(correct=0, incorrect=1))
        for span in spans:
            span.log(scores=dict(correct=1))
        for span in spans:
            span.end()
        self.experiment.summarize()

        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(rows), len(spans))
        rows.sort(key=lambda r: int(r["input"]))
        for i, row in enumerate(rows):
            self.assertEqual(i, row["input"])
            self.assertEqual(dict(correct=1, incorrect=1), row["scores"])
            metrics = row["metrics"]
            self.assertEqual(type(metrics["start"]), float)
            self.assertEqual(type(metrics["end"]), float)
            self.assertLessEqual(metrics["start"], metrics["end"])
            context = row["context"]
            self.assertEqual(context["caller_functionname"], "<listcomp>")
            self.assertEqual(context["caller_filename"], __file__)
            self.assertEqual(context["caller_lineno"], span_lineno)

    def test_no_incremental_id_update(self):
        for i in range(10):
            with self.assertRaises(Exception):
                with self.experiment.start_span(id=str(i), input=i, scores=dict(correct=1)) as span:
                    span.log(id=str(i + 1))

    def test_no_forbidden_keys(self):
        for forbidden_key in ["span_id", "experiment_id", "_xact_id", "_object_delete", "other"]:
            with self.assertRaises(Exception):
                self.experiment.log(input=0, scores=dict(correct=1), **{forbidden_key: "hello"})

    def test_only_shallow_numbers_metrics_logging(self):
        for invalid_value in ["bar", {}, {"bar": 2}]:
            with self.assertRaises(Exception):
                self.experiment.log(input=0, scores=dict(correct=1), metrics={"foo": invalid_value})

    def test_no_nan(self):
        for invalid_value in [float("nan"), float("-nan"), float("inf"), float("-inf")]:
            with self.assertRaises(Exception):
                self.experiment.log(input={"a": invalid_value}, output="bar", scores=dict(correct=1))

    def test_merge_always_latest_row(self):
        with self.experiment.start_span() as span:
            id = span.id

            span.log(input=dict(a=12))
            self.experiment.log(
                id=id,
                input=dict(b=10),
                output=dict(c=11),
                scores=dict(correct=1),
                allow_concurrent_with_spans=True,
            )
            self.experiment.summarize()

            rows = get_object_json("experiment", self.experiment.id)
            self.assertEqual(len(rows), 1)
            self.assertEqual(dict(b=10), rows[0]["input"])
            self.assertEqual(dict(c=11), rows[0]["output"])
            last_end_time = rows[0]["metrics"]["end"]

        self.experiment.summarize()
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(rows), 1)
        self.assertEqual(dict(b=10), rows[0]["input"])
        self.assertEqual(dict(correct=1), rows[0]["scores"])
        self.assertNotEqual(last_end_time, rows[0]["metrics"]["end"])

    def test_nomerge_with_deleted_row(self):
        with self.experiment.start_span() as span:
            id = span.id

            span.log(input="from_span")
            self.experiment.log(
                id=id,
                input="full_row",
                output="output",
                scores=dict(correct=1),
                allow_concurrent_with_spans=True,
            )
            self.experiment.summarize()

            rows = get_object_json("experiment", self.experiment.id)
            self.assertEqual(len(rows), 1)
            self.assertEqual("full_row", rows[0]["input"])
            self.assertEqual("output", rows[0]["output"])

            self.assertEqual(
                [id],
                log_raw(
                    dict(
                        project_id=self.experiment.project_id,
                        experiment_id=self.experiment.id,
                        id=id,
                        _object_delete=True,
                    )
                ),
            )

            rows = get_object_json("experiment", self.experiment.id)
            self.assertEqual(len(rows), 0)

        self.experiment.summarize()
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(rows), 1)
        self.assertEqual(None, rows[0]["input"])
        metrics = rows[0]["metrics"]
        self.assertEqual(1, len(metrics))
        self.assertIn("end", metrics)

    def test_clobber_nonspan_row(self):
        (
            log_raw(
                dict(
                    project_id=self.experiment.project_id,
                    experiment_id=self.experiment.id,
                    id="x",
                    input=dict(a=12),
                )
            ),
        )
        (
            log_raw(
                dict(
                    project_id=self.experiment.project_id,
                    experiment_id=self.experiment.id,
                    id="x",
                    **{IS_MERGE_FIELD: False},
                    input=dict(b=10),
                )
            ),
        )
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(rows), 1)
        self.assertEqual(dict(b=10), rows[0]["input"])

    def test_merge_nonspan_row(self):
        log_raw(
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="x",
                input=dict(a=12),
            )
        )
        log_raw(
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="x",
                **{IS_MERGE_FIELD: True},
                input=dict(b=10),
            )
        )
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(rows), 1)
        self.assertEqual(dict(a=12, b=10), rows[0]["input"])

    def test_clobber_with_inbatch_merge(self):
        log_raw(
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="x",
                input=dict(a=12),
            )
        )
        log_raw(
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="x",
                input=dict(b=10),
            ),
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="x",
                **{IS_MERGE_FIELD: True},
                input=dict(c=20),
            ),
        )
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(rows), 1)
        self.assertEqual(dict(b=10, c=20), rows[0]["input"])

    def test_same_id_different_object_types(self):
        dataset = braintrust.init_dataset(project="p")
        span_id = str(uuid4())
        root_span_id = str(uuid4())

        self.assertEqual(
            ["test"] * 2,
            log_raw(
                dict(
                    project_id=self.experiment.project_id,
                    experiment_id=self.experiment.id,
                    id="test",
                    span_id=span_id,
                    root_span_id=root_span_id,
                    input="experiment_input",
                ),
                dict(
                    project_id=dataset.project_id,
                    dataset_id=dataset.id,
                    id="test",
                    span_id=span_id,
                    root_span_id=root_span_id,
                    input="dataset_input",
                ),
            ),
        )

        self.assertEqual(
            ["test"] * 2,
            log_raw(
                dict(
                    project_id=self.experiment.project_id,
                    experiment_id=self.experiment.id,
                    id="test",
                    span_id=span_id,
                    root_span_id=root_span_id,
                    input="experiment_input2",
                ),
                dict(
                    project_id=dataset.project_id,
                    dataset_id=dataset.id,
                    id="test",
                    span_id=span_id,
                    root_span_id=root_span_id,
                    input="dataset_input2",
                ),
            ),
        )

        experiment_rows = get_object_json("experiment", self.experiment.id)
        dataset_rows = get_object_json("dataset", dataset.id)
        self.assertEqual(len(experiment_rows), 1)
        self.assertEqual(len(dataset_rows), 1)
        self.assertEqual("experiment_input2", experiment_rows[0]["input"])
        self.assertEqual("dataset_input2", dataset_rows[0]["input"])

    def test_distributed_span_logging(self):
        ids = ["i0", "i1", "i2", "i3", "i4", "i5"]
        span_id = str(uuid4())
        root_span_id = str(uuid4())

        def do_log(thread_id):
            log_raw(
                *[
                    dict(
                        project_id=self.experiment.project_id,
                        experiment_id=self.experiment.id,
                        id=id,
                        span_id=span_id,
                        root_span_id=root_span_id,
                        input={str(thread_id): True},
                        **{IS_MERGE_FIELD: True},
                    )
                    for id in ids
                ]
            )

        NUM_LOGGERS = 100
        with ThreadPoolExecutor(NUM_LOGGERS) as executor:
            results = []
            for i in range(NUM_LOGGERS):
                results.append(executor.submit(do_log, i))
            for r in results:
                r.result()

        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(len(ids), len(rows))
        rows.sort(key=lambda r: r["id"])
        for id, row in zip(ids, rows):
            self.assertEqual(id, row["id"])
            self.assertEqual({str(i): True for i in range(NUM_LOGGERS)}, row["input"])

    def test_current_experiment(self):
        self.assertEqual(self.experiment, braintrust.current_experiment())
        experiment = braintrust.init(project="p")
        self.assertEqual(experiment, braintrust.current_experiment())

    def test_span_not_current_unless_in_context_manager(self):
        span = self.experiment.start_span("subspan", set_current=True)
        self.assertNotEqual(span, braintrust.current_span())
        with span:
            self.assertEqual(span, braintrust.current_span())
        self.assertNotEqual(span, braintrust.current_span())

        with self.experiment.start_span("subspan") as span:
            self.assertEqual(span, braintrust.current_span())
        self.assertNotEqual(span, braintrust.current_span())

    def test_small_queue_size(self):
        # Motivated by a deadlock between the producer and consumer threads when
        # publishing many items.
        try:
            QUEUE_SIZE_ENV = "BRAINTRUST_QUEUE_SIZE"
            prev_queue_size_env = os.environ.get(QUEUE_SIZE_ENV)
            os.environ[QUEUE_SIZE_ENV] = "1"

            NUM_ROWS = 10
            experiment = braintrust.init(project="p")
            experiment_id = experiment.id
            for i in range(NUM_ROWS):
                experiment.log(input="hello", output="goodbye", scores=dict(correct=1))
            experiment.summarize()
            rows = get_object_json("experiment", experiment_id)
            self.assertEqual(NUM_ROWS, len(rows))
        finally:
            if prev_queue_size_env is not None:
                os.environ[QUEUE_SIZE_ENV] = prev_queue_size_env
            else:
                del os.environ[QUEUE_SIZE_ENV]

    def test_empty_publish(self):
        resp = log_raw()
        self.assertEqual([], resp)

    def test_log_from_multiple_threads(self):
        def do_log():
            self.experiment.log(input="a", output="b", scores=dict())

        NUM_LOGGERS = 100
        results = []
        with ThreadPoolExecutor(NUM_LOGGERS) as executor:
            for i in range(NUM_LOGGERS):
                results.append(executor.submit(do_log))
            for r in results:
                r.result()
        self.experiment.summarize()
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(NUM_LOGGERS, len(rows))

    def test_not_json(self):
        class A:
            def __init__(self):
                self.a = 1

        try:
            self.experiment.log(input=A(), output=A(), scores=dict(correct=1))
        except Exception as e:
            assert "All logged values" in str(e)
        self.experiment.summarize()

    def test_distinct_root_span_id(self):
        span_id = str(uuid4())
        root_span_id = str(uuid4())

        span_id2 = str(uuid4())
        log_raw(
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="row0",
                span_id=span_id,
                root_span_id=root_span_id,
                span_parents=[],
                input={"foo": "bar"},
            ),
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="row1",
                span_id=span_id2,
                root_span_id=root_span_id,
                _parent_id="row0",
                input={"foo": "bar"},
            ),
        )

    def test_log_custom_span_id(self):
        log_raw(
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="row",
                span_id="row",
                root_span_id="trace",
                span_parents=[],
                input={"foo": "bar"},
            ),
            dict(
                project_id=self.experiment.project_id,
                experiment_id=self.experiment.id,
                id="sub_row",
                span_id="sub_row",
                root_span_id="trace",
                span_parents=["row"],
                input={"foo": "bar"},
            ),
        )
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(2, len(rows))
        id_to_row = {r["id"]: r for r in rows}
        self.assertEqual("row", id_to_row["row"]["span_id"])
        self.assertEqual("trace", id_to_row["row"]["root_span_id"])
        self.assertEqual("sub_row", id_to_row["sub_row"]["span_id"])
        self.assertEqual("trace", id_to_row["sub_row"]["root_span_id"])

    def test_dual_table_merge_updates(self):
        """Test that merge updates work correctly across both logs and logs2 tables."""
        # Test across different object types
        for object_type in ["experiment", "dataset", "project_logs"]:
            # Initialize the appropriate object based on type
            if object_type == "experiment":
                obj = braintrust.init(project="p")
                object_id = obj.id
                project_id = obj.project_id
            elif object_type == "dataset":
                obj = braintrust.init_dataset(project="p")
                object_id = obj.id
                project_id = obj.project_id
            else:  # project_logs
                obj = braintrust.init_logger(project="p")
                object_id = obj.id
                project_id = obj.id

            row_id = str(uuid4())
            base_input = {"base": "value"}
            merge_input1 = {"merge1": "value"}
            merge_input2 = {"merge2": "value"}
            object_id_dict = {"log_id": "g"} if object_type == "project_logs" else {f"{object_type}_id": object_id}

            # Step 1: Insert initial row into logs table
            log3_raw(
                dict(
                    project_id=project_id,
                    **object_id_dict,
                    id=row_id,
                    input=base_input,
                    **{IS_MERGE_FIELD: False},
                ),
                force_disable_logs2=True,
            )

            # Step 2: Merge update into logs table
            log3_raw(
                dict(
                    project_id=project_id,
                    **object_id_dict,
                    id=row_id,
                    input=merge_input1,
                    **{IS_MERGE_FIELD: True},
                ),
                force_disable_logs2=True,
            )

            # Step 3: Merge update that should go to logs2 table
            log3_raw(
                dict(
                    project_id=project_id,
                    **object_id_dict,
                    id=row_id,
                    input=merge_input2,
                    **{IS_MERGE_FIELD: True},
                ),
            )

            # Query the final state
            rows = get_object_json(object_type, object_id)
            self.assertEqual(1, len(rows))
            final_row = rows[0]

            # Verify the final merged state
            expected_input = {
                "base": "value",
                "merge1": "value",
                "merge2": "value",
            }
            self.assertEqual(expected_input, final_row["input"])

            audit_log_rows = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={
                    "query": {
                        "from": {
                            "op": "function",
                            "name": {"op": "ident", "name": [object_type]},
                            "args": [{"op": "literal", "value": object_id}],
                        },
                        "filter": {
                            "op": "eq",
                            "left": {"op": "ident", "name": ["id"]},
                            "right": {"op": "literal", "value": row_id},
                        },
                        "select": [{"op": "star"}],
                    },
                    "fmt": "json",
                    "audit_log": True,
                },
            ).json()["data"]
            self.assertEqual(3, len(audit_log_rows))
            self.assertEqual(3, len(set(r["_xact_id"] for r in audit_log_rows)))
            audit_log_rows.sort(key=lambda r: r["_xact_id"])
            self.assertEqual(audit_log_rows[0]["audit_data"].get("action"), "upsert")
            self.assertEqual(audit_log_rows[1]["audit_data"].get("action"), "merge")
            self.assertEqual(audit_log_rows[2]["audit_data"].get("action"), "merge")
