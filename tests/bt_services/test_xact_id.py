import unittest

from braintrust.xact_ids import load_pretty_xact, prettify_xact


def gen_xact_id(ts, xact_id):
    return (0x0DE1 << 48) | ((ts & 0xFFFFFFFFFFFF) << 16) | (xact_id & 0xFFFF)


class XactIdTest(unittest.TestCase):
    def test_sanity(self):
        cases = [
            ["1000192671193355184", "7a670c06c4787a30"],
            ["1000192656880881099", "81cd05ee665fdfb3"],
            ["1000192649085046089", "39a12b7b05fb91c1"],
            ["1000192689925586944", "0c05c446e60d0000"],
        ]

        for [original, pretty] in cases:
            encoded = prettify_xact(original)
            self.assertEqual(encoded, pretty)
            self.assertEqual(load_pretty_xact(encoded), original)

    def test_crazy(self):
        # A transaction id is (0x0DE1 << 48) | ((ts & 0xFFFFFFFFFFFF) << 16) | (xact_id & 0xFFFF)
        # where xact_id is just a counter, and ts is the timestamp in seconds. So it's useful to test
        # crazy values of ts and xact_id
        # (ts, xact_id)
        cases = [
            (0, 0),
            (0, 1000),
            (1710209616, 1000000),
            (10419873099, 1),  # Year 2300
            (32509761099, 2),  # Year 3000
            (0x0F00000000, 0),
            (0x0F00000001, 0),
            (0x0F00000011, 0),
            (0x0FFFFFFFFF, 0),
            (0x0FFFFFFFFF, 100),
            (0x0FFFFFFFFF, 200),
            (0x0FFFFFFFFF, 300),
            (0x0FFFFFFFFF, 301),
            (0x0FFFFFFFFF, 302),
        ]

        for ts, xact_id in cases:
            original = str(gen_xact_id(ts, xact_id))
            pretty = prettify_xact(original)
            self.assertEqual(load_pretty_xact(pretty), original, f"ts={hex(ts)}, xact_id={gen_xact_id(ts, xact_id)}")

    def test_16_chars(self):
        for ts in range(1710474735, 1710474735 + 1000):
            for counter in range(0, 1000):
                xact_id = gen_xact_id(ts, counter)
                pretty = prettify_xact(xact_id)
                self.assertEqual(len(pretty), 16, f"ts={hex(ts)}, xact_id={xact_id}")
