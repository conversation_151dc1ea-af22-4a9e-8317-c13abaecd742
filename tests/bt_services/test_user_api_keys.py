from uuid import uuid4

import braintrust
from braintrust_local.util import ensure_not_none

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class UserApiKeysTest(BraintrustAppTestBase):
    def test_basic(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id)
        given_name, family_name, email = str(uuid4()), str(uuid4()), self.getUserEmail(user_id)

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "update users set given_name=%s, family_name=%s where id=%s::uuid",
                    (given_name, family_name, user_id),
                )

        # A user can only fetch and delete their own API keys.

        self_user_api_keys = self.run_server_action(self.org_api_key, "fetchApiKeys", dict()).json()
        self.assertEqual(len(self_user_api_keys), 1)
        assert self_user_api_keys[0]["user_email"] == self.getUserEmail(self.user_id)
        assert self_user_api_keys[0]["user_given_name"] is None
        assert self_user_api_keys[0]["user_family_name"] is None

        other_user_api_keys = self.run_server_action(user_api_key, "fetchApiKeys", dict()).json()
        self.assertEqual(len(other_user_api_keys), 1)
        assert other_user_api_keys[0]["user_email"] == email
        assert other_user_api_keys[0]["user_given_name"] == given_name
        assert other_user_api_keys[0]["user_family_name"] == family_name

        with self.assertRaises(Exception):
            self.run_server_action(self.org_api_key, "deleteApiKey", dict(api_key_id=other_user_api_keys[0]["id"]))
        with self.assertRaises(Exception):
            self.run_server_action(user_api_key, "deleteApiKey", dict(api_key_id=self_user_api_keys[0]["id"]))

        self.run_server_action(self.org_api_key, "deleteApiKey", dict(api_key_id=self_user_api_keys[0]["id"]))
        self.run_server_action(user_api_key, "deleteApiKey", dict(api_key_id=other_user_api_keys[0]["id"]))

    def test_get_unscoped_api_key(self):
        _unscoped_api_key = self.createUserOrgApiKey(self.user_id, None)
        resp = self.run_request("get", f"{LOCAL_API_URL}/v1/api_key").json()
        self.assertEqual(len(resp["objects"]), 2)
        self.assertEqual(set(o["user_id"] for o in resp["objects"]), {self.user_id})
        self.assertEqual(set(o["org_id"] for o in resp["objects"]), {self.org_id, None})


class OwnerApiKeysTest(BraintrustAppTestBase):
    def test_basic(self):
        org_api_key_id = self.run_server_action(self.org_api_key, "fetchApiKeys", dict()).json()[0]["id"]

        user_id1, _, user1_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user1_api_key_id = self.run_server_action(user1_api_key, "fetchApiKeys", dict()).json()[0]["id"]
        user_id2, _, user2_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user2_api_key_id = self.run_server_action(user2_api_key, "fetchApiKeys", dict()).json()[0]["id"]
        user_id3, _, user3_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user3_api_key_id = self.run_server_action(user3_api_key, "fetchApiKeys", dict()).json()[0]["id"]

        user_details = [
            dict(given_name=str(uuid4()), family_name=str(uuid4()), email=self.getUserEmail(user_id), user_id=user_id)
            for user_id in [user_id1, user_id2, user_id3]
        ]

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                for user in user_details:
                    cursor.execute(
                        "update users set given_name=%s, family_name=%s where id=%s::uuid",
                        (user["given_name"], user["family_name"], user["user_id"]),
                    )

        org_api_keys = self.run_server_action(
            self.org_api_key, "fetchApiKeysAsOrgOwner", dict(org_id=self.org_id, user_type="user")
        ).json()
        assert set(
            [(x["id"], x["user_given_name"], x["user_family_name"], x["user_email"]) for x in org_api_keys]
        ) == {
            (org_api_key_id, None, None, self.getUserEmail(self.user_id)),
            (
                user1_api_key_id,
                user_details[0]["given_name"],
                user_details[0]["family_name"],
                user_details[0]["email"],
            ),
            (
                user2_api_key_id,
                user_details[1]["given_name"],
                user_details[1]["family_name"],
                user_details[1]["email"],
            ),
            (
                user3_api_key_id,
                user_details[2]["given_name"],
                user_details[2]["family_name"],
                user_details[2]["email"],
            ),
        }

        for unprivileged_api_key in [user1_api_key, user2_api_key, user3_api_key]:
            resp = self.run_server_action(
                unprivileged_api_key, "fetchApiKeysAsOrgOwner", dict(org_id=self.org_id)
            ).json()
            assert resp == []

            for delete_key_id in [user1_api_key_id, user2_api_key_id, user3_api_key_id]:
                resp = self.run_server_action(
                    unprivileged_api_key,
                    "deleteApiKeyAsOrgOwner",
                    dict(org_id=self.org_id, api_key_id=delete_key_id),
                    expect_error=True,
                )
                assert resp.status_code == 400
                assert resp.text.startswith("No API keys found or missing organization ownership")

        for key_id in [user1_api_key_id, user2_api_key_id, user3_api_key_id]:
            self.run_server_action(
                self.org_api_key, "deleteApiKeyAsOrgOwner", dict(org_id=self.org_id, api_key_id=key_id)
            )

        org2_id, org2_name = self.createOrg()
        self.addUserToOrg(user_id1, org2_id)
        user1_org2_api_key = self.createUserOrgApiKey(user_id1, org2_id)

        user1_org2_api_keys = self.run_server_action(user1_org2_api_key, "fetchApiKeys", dict()).json()
        assert len(user1_org2_api_keys) == 1
        assert user1_org2_api_keys[0]["org_id"] == org2_id
        assert user1_org2_api_keys[0]["user_id"] == user_id1
        assert user1_org2_api_keys[0]["id"] != user1_api_key_id

    def test_no_delete_unscoped_api_key(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "select create_api_key(users.auth_id, NULL, 'test-unscoped') from users where id=%s",
                    (self.user_id,),
                )
                unscoped_api_key = ensure_not_none(cursor.fetchone())[0]

        keys = self.run_server_action(unscoped_api_key, "fetchApiKeys", dict()).json()
        unscoped_api_key_id = [x["id"] for x in keys if x["name"] == "test-unscoped"][0]
        resp = self.run_server_action(
            self.org_api_key,
            "deleteApiKeyAsOrgOwner",
            dict(org_id=self.org_id, api_key_id=unscoped_api_key_id),
            expect_error=True,
        )
        assert resp.status_code == 400
        assert resp.text.startswith("No API keys found or missing organization ownership")
