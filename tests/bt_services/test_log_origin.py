import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class LogOriginTest(BraintrustAppTestBase):
    def test_invalid_origin_schema(self):
        """
        Test inserting invalid origins into a dataset.
        """
        dataset = braintrust.init_dataset("p", "dataset")
        dataset.flush()

        def _insert(origin, expect_error: bool):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/logs3",
                json=dict(
                    api_version=2,
                    rows=[dict(id="my id", dataset_id=dataset.id, origin=origin)],
                ),
                expect_error=expect_error,
            )

        _insert(None, expect_error=False)
        _insert({}, expect_error=False)
        _insert("invalid", expect_error=True)

        _insert(
            {
                "object_type": "experiment",
                "object_id": "95fb3fc8-9c71-4051-9b6c-67fe7421468b",
                "id": "some id",
                "_xact_id": "123",
            },
            expect_error=False,
        )
        _insert(
            {
                "object_type": "experiment",
                "object_id": "95fb3fc8-9c71-4051-9b6c-67fe7421468b",
                "id": "some id",
                "_xact_id": "123",
                "extra_field": "extra value",
            },
            expect_error=False,
        )
        _insert(
            {
                "object_type": "invalid",
                "object_id": "95fb3fc8-9c71-4051-9b6c-67fe7421468b",
                "id": "some id",
                "_xact_id": "123",
            },
            expect_error=True,
        )
        _insert(
            {
                "object_type": "experiment",
                "object_id": "not a uuid",
                "id": "some id",
                "_xact_id": "123",
            },
            expect_error=True,
        )
        _insert(
            {
                "object_type": "experiment",
                "object_id": "95fb3fc8-9c71-4051-9b6c-67fe7421468b",
                "id": "some id",
                "_xact_id": 123,
            },
            expect_error=True,
        )
