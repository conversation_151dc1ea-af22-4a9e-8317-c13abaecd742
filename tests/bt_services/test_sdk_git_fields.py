import unittest

from braintrust import GitMetadataSettings, RepoInfo


class MergeGitMetadataTest(unittest.TestCase):
    def test_basic(self):
        a = GitMetadataSettings(
            collect="all",
        )
        b = GitMetadataSettings(
            collect="none",
        )
        c = GitMetadataSettings(
            collect="some",
        )
        assert c == GitMetadataSettings(collect="some", fields=[])
        d = GitMetadataSettings(
            collect="some",
            fields=None,
        )
        e = GitMetadataSettings(
            collect="some",
            fields=["commit", "branch", "author_name", "author_email"],
        )
        f = GitMetadataSettings(
            collect="some",
            fields=["commit", "branch", "commit_message", "commit_time"],
        )
        g = GitMetadataSettings(
            collect="some",
            fields=["tag", "dirty", "git_diff"],
        )
        h = GitMetadataSettings(
            collect="some",
            fields=["tag", "author_name"],
        )

        def check(first, second, expected):
            left = GitMetadataSettings.merge(first, second)
            self.assertEqual(left.collect, expected.collect)
            self.assertEqual(set(left.fields or []), set(expected.fields or []))
            right = GitMetadataSettings.merge(second, first)
            self.assertEqual(right.collect, expected.collect)
            self.assertEqual(set(right.fields or []), set(expected.fields or []))

        check(a, a, a)
        check(a, b, b)
        check(a, c, c)
        check(a, d, c)
        check(b, b, b)
        check(b, c, b)
        check(b, d, b)
        check(c, c, b)
        check(c, d, b)
        check(d, e, b)
        check(e, f, GitMetadataSettings(collect="some", fields=["commit", "branch"]))
        check(e, g, b)
        check(e, h, GitMetadataSettings(collect="some", fields=["author_name"]))
        check(f, g, b)
