import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


class CustomMetricsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.experiment = braintrust.init(project="p")

    def test_custom_start_end(self):
        self.experiment.log(input="foo", output="bar", scores={}, metrics={"foo": 1, "start": 123, "end": 456})
        self.experiment.summarize()
        rows = get_object_json("experiment", self.experiment.id)
        self.assertEqual(1, len(rows))
        metrics = rows[0]["metrics"]
        self.assertEqual(1, metrics["foo"])
        self.assertEqual(123, metrics["start"])
        self.assertEqual(456, metrics["end"])

    def test_null_start_end(self):
        with self.assertRaises(Exception):
            self.experiment.log(input="foo", output="bar", scores=dict(foo=0.5), metrics={"start": None})

        with self.assertRaises(Exception):
            self.experiment.log(input="foo", output="bar", scores={}, metrics={"start": None, "end": None})
