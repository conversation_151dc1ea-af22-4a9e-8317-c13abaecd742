import json
import os
import re
import subprocess
import tempfile
from concurrent.futures import Thr<PERSON>PoolExecutor
from inspect import currentframe, getframeinfo
from uuid import uuid4

import braintrust
import git
from braintrust import gitutil
from braintrust.db_fields import IS_MERGE_FIELD
from braintrust.gitutil import truncate_to_byte_limit
from braintrust_local.api_db_util import get_object_json, log3_raw, log_raw

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def masking_function_sensitive(data):
    """Replace sensitive information with redacted values."""
    if isinstance(data, dict):
        masked = {}
        for k, v in data.items():
            if k in ["password", "api_key", "secret", "token"]:
                masked[k] = "REDACTED"
            elif k == "ssn":
                masked[k] = "XXX-XX-XXXX"
            elif k == "email":
                masked[k] = "<EMAIL>"
            elif isinstance(v, str) and "sensitive" in v:
                # Only replace "sensitive" when it appears as its own word
                words = v.split()
                masked_words = ["REDACTED" if word == "sensitive" else word for word in words]
                masked[k] = " ".join(masked_words)
            elif isinstance(v, dict):
                masked[k] = masking_function_sensitive(v)
            elif isinstance(v, list):
                masked[k] = [
                    (
                        masking_function_sensitive(item)
                        if isinstance(item, (dict, list))
                        else ("REDACTED" if item == "sensitive" else item) if isinstance(item, str) else item
                    )
                    for item in v
                ]
            else:
                masked[k] = v
        return masked
    elif isinstance(data, list):
        return [
            (
                masking_function_sensitive(item)
                if isinstance(item, (dict, list))
                else ("REDACTED" if item == "sensitive" else item) if isinstance(item, str) else item
            )
            for item in data
        ]
    elif isinstance(data, str) and "sensitive" in data:
        words = data.split()
        masked_words = ["REDACTED" if word == "sensitive" else word for word in words]
        return " ".join(masked_words)
    return data


def masking_function_pii(data):
    """Replace PII (personally identifiable information) with masked values."""

    def _mask_pii(data, path=()):
        if isinstance(data, dict):
            masked = {}
            for k, v in data.items():
                current_path = path + (k,)
                # Don't mask span_attributes.name since it's a span identifier, not PII
                if k in ["name", "full_name", "first_name", "last_name"] and (
                    len(path) == 0 or path[-1] != "span_attributes"
                ):
                    masked[k] = "NAME_REDACTED"
                elif k in ["phone", "phone_number"]:
                    masked[k] = "XXX-XXX-XXXX"
                elif k == "address":
                    masked[k] = "ADDRESS_REDACTED"
                elif k == "credit_card":
                    masked[k] = "XXXX-XXXX-XXXX-XXXX"
                elif isinstance(v, dict):
                    masked[k] = _mask_pii(v, current_path)
                elif isinstance(v, list):
                    masked[k] = [
                        _mask_pii(item, current_path) if isinstance(item, (dict, list)) else item for item in v
                    ]
                else:
                    masked[k] = v
            return masked
        elif isinstance(data, list):
            return [_mask_pii(item, path) if isinstance(item, (dict, list)) else item for item in data]
        return data

    return _mask_pii(data)


@braintrust.traced
def process_user_data(user_data):
    """Simulate processing user data with nested spans."""
    with braintrust.current_span().start_span("validate_input") as span:
        span.log(input=user_data, output={"validated": True})

    with braintrust.current_span().start_span("enrich_data") as span:
        enriched = {**user_data, "enriched": True, "api_key": "sk-1234567890"}
        span.log(input=user_data, output=enriched)
        return enriched


@braintrust.traced
def nested_function_with_secrets(data):
    """Function that logs sensitive data at multiple levels."""
    result = {"processed": data, "token": "bearer-secret-token"}
    braintrust.current_span().log(output=result, metadata={"password": "super-secret", "safe_data": "this is ok"})
    return result


class MaskingTest(BraintrustAppTestBase):
    def tearDown(self):
        """Clean up after each test by clearing the masking function."""
        braintrust.set_masking_function(None)
        super().tearDown()

    def test_experiment_masking_basic(self):
        """Test basic masking functionality with experiments."""
        braintrust.set_masking_function(masking_function_sensitive)
        experiment = braintrust.init_experiment("test_project")

        # Test basic log
        experiment.log(
            input={"query": "Find sensitive information", "api_key": "sk-12345"},
            output={"result": "Here is sensitive data", "password": "secret123"},
            scores={"accuracy": 0.95},
            metadata={"token": "auth-token", "safe": "non-sensitive"},
        )

        experiment.flush()
        events = get_object_json("experiment", experiment.id)

        # Verify masking was applied
        self.assertEqual(len(events), 1)
        event = events[0]

        self.assertEqual(event["input"]["query"], "Find REDACTED information")
        self.assertEqual(event["input"]["api_key"], "REDACTED")
        self.assertEqual(event["output"]["result"], "Here is REDACTED data")
        self.assertEqual(event["output"]["password"], "REDACTED")
        self.assertEqual(event["metadata"]["token"], "REDACTED")
        self.assertEqual(event["metadata"]["safe"], "non-sensitive")
        self.assertEqual(event["scores"]["accuracy"], 0.95)

    def test_experiment_masking_with_spans(self):
        """Test masking propagates through nested spans."""
        braintrust.set_masking_function(masking_function_sensitive)
        experiment = braintrust.init_experiment("test_project")

        with experiment.start_span("root_span") as root:
            root.log(input={"message": "This is sensitive", "secret": "top-secret"}, metadata={"api_key": "key123"})

            with root.start_span("child_span") as child:
                child.log(output={"data": "sensitive output", "token": "bearer-123"}, scores={"quality": 0.8})

                with child.start_span("grandchild_span") as grandchild:
                    grandchild.log(
                        input={"nested": {"password": "nested-pass", "info": "safe"}},
                        output="Result with sensitive info",
                    )

        experiment.flush()
        events = get_object_json("experiment", experiment.id)

        # Find events by span name
        root_event = next(e for e in events if e.get("span_attributes", {}).get("name") == "root_span")
        child_event = next(e for e in events if e.get("span_attributes", {}).get("name") == "child_span")
        grandchild_event = next(e for e in events if e.get("span_attributes", {}).get("name") == "grandchild_span")

        # Verify root span masking
        self.assertEqual(root_event["input"]["message"], "This is REDACTED")
        self.assertEqual(root_event["input"]["secret"], "REDACTED")
        self.assertEqual(root_event["metadata"]["api_key"], "REDACTED")

        # Verify child span masking
        self.assertEqual(child_event["output"]["data"], "REDACTED output")
        self.assertEqual(child_event["output"]["token"], "REDACTED")
        self.assertEqual(child_event["scores"]["quality"], 0.8)

        # Verify grandchild span masking
        self.assertEqual(grandchild_event["input"]["nested"]["password"], "REDACTED")
        self.assertEqual(grandchild_event["input"]["nested"]["info"], "safe")
        self.assertEqual(grandchild_event["output"], "Result with REDACTED info")

    def test_experiment_masking_with_traced_functions(self):
        """Test masking works with @traced decorator."""
        braintrust.set_masking_function(masking_function_pii)
        experiment = braintrust.init_experiment("test_project")

        with experiment.start_span("main") as span:
            user_data = {"name": "John Doe", "email": "<EMAIL>", "phone": "555-1234", "user_id": "12345"}
            result = process_user_data(user_data)
            span.log(input=user_data, output=result)

        experiment.flush()
        events = get_object_json("experiment", experiment.id)

        # Debug: print events to see structure
        self.assertGreater(len(events), 0, "No events found in experiment")

        # Find main span and nested spans
        main_event = next((e for e in events if e.get("span_attributes", {}).get("name") == "main"), None)
        validate_event = next(
            (e for e in events if e.get("span_attributes", {}).get("name") == "validate_input"), None
        )
        enrich_event = next((e for e in events if e.get("span_attributes", {}).get("name") == "enrich_data"), None)

        # Check if we found the events
        self.assertIsNotNone(
            main_event,
            f"Could not find main span. Available spans: {[e.get('span_attributes', {}).get('name') for e in events]}",
        )
        self.assertIsNotNone(validate_event, "Could not find validate_input span")
        self.assertIsNotNone(enrich_event, "Could not find enrich_data span")

        # Verify PII masking in all spans
        for event in [main_event, validate_event, enrich_event]:
            if "input" in event and "name" in event["input"]:
                self.assertEqual(event["input"]["name"], "NAME_REDACTED")
                self.assertEqual(event["input"]["phone"], "XXX-XXX-XXXX")
                # email is not in the PII masking function, so it should remain
                self.assertEqual(event["input"]["email"], "<EMAIL>")
                self.assertEqual(event["input"]["user_id"], "12345")

    def test_dataset_masking(self):
        """Test masking functionality with datasets."""
        braintrust.set_masking_function(masking_function_sensitive)
        dataset = braintrust.init_dataset("test_dataset")

        # Insert multiple records with sensitive data
        dataset.insert(
            input={"prompt": "Process this sensitive request", "api_key": "key-123"},
            expected={"response": "Here's the sensitive response", "token": "auth-token"},
            metadata={"password": "pass123", "score": 0.9},
        )

        dataset.insert(
            input={"query": "Another query", "secret": "secret-value"},
            expected={"data": "Response data", "api_key": "another-key"},
            tags=["sensitive", "test"],
        )

        dataset.flush()
        events = get_object_json("dataset", dataset.id)

        self.assertEqual(len(events), 2)

        # Find events by their unique characteristics
        event_with_prompt = None
        event_with_query = None

        for event in events:
            if "prompt" in event.get("input", {}):
                event_with_prompt = event
            elif "query" in event.get("input", {}):
                event_with_query = event

        self.assertIsNotNone(event_with_prompt, "Could not find event with prompt")
        self.assertIsNotNone(event_with_query, "Could not find event with query")

        # Verify first record (the one with prompt)
        self.assertEqual(event_with_prompt["input"]["prompt"], "Process this REDACTED request")
        self.assertEqual(event_with_prompt["input"]["api_key"], "REDACTED")
        self.assertEqual(event_with_prompt["expected"]["response"], "Here's the REDACTED response")
        self.assertEqual(event_with_prompt["expected"]["token"], "REDACTED")
        self.assertEqual(event_with_prompt["metadata"]["password"], "REDACTED")
        self.assertEqual(event_with_prompt["metadata"]["score"], 0.9)

        # Verify second record (the one with query)
        self.assertEqual(event_with_query["input"]["secret"], "REDACTED")
        self.assertEqual(event_with_query["expected"]["api_key"], "REDACTED")
        # Tags are not masked since they're not in the list of masked fields
        self.assertIn("sensitive", event_with_query["tags"])

    def test_logger_masking(self):
        """Test masking functionality with logger."""
        braintrust.set_masking_function(masking_function_pii)
        logger = braintrust.init_logger("test_logger")

        # Log with PII data
        logger.log(
            input={"name": "Jane Smith", "address": "123 Main St", "safe_id": "ABC123"},
            output={"greeting": "Hello Jane Smith", "credit_card": "1234-5678-9012-3456"},
            metadata={"phone": "555-9876", "department": "Engineering"},
        )

        # Test with spans
        with logger.start_span("user_operation") as span:
            span.log(
                input={"first_name": "Bob", "last_name": "Johnson", "action": "login"},
                output={"status": "success", "full_name": "Bob Johnson"},
            )

            # Test nested span
            with span.start_span("validate_phone") as nested:
                nested.log(input={"phone_number": "555-1111"}, output={"valid": True})

        logger.flush()

        # Since logger doesn't have get_object_json, we'll verify through logged data
        # by checking that masking was applied during logging
        # In a real test, you might capture the logs or check the backend

        # For now, we just ensure no exceptions were thrown
        self.assertTrue(True)

    def test_logger_masking_with_traced(self):
        """Test logger masking with traced functions."""
        braintrust.set_masking_function(masking_function_sensitive)
        logger = braintrust.init_logger("test_logger")

        with logger.start_span("main_operation") as span:
            result = nested_function_with_secrets({"input": "test data"})
            span.log(output=result)

        logger.flush()

        # Verify no exceptions and masking was applied
        self.assertTrue(True)

    def test_multiple_masking_functions(self):
        """Test changing masking functions between different objects."""
        # Experiment with sensitive masking
        braintrust.set_masking_function(masking_function_sensitive)
        exp1 = braintrust.init_experiment("exp1")
        exp1.log(
            input={"api_key": "key123", "data": "sensitive info"},
            output={"password": "pass", "result": "ok"},
            scores={"quality": 0.9},
        )
        exp1.flush()  # Flush immediately to apply sensitive masking

        # Change to PII masking for second experiment
        braintrust.set_masking_function(masking_function_pii)
        exp2 = braintrust.init_experiment("exp2")
        exp2.log(
            input={"name": "John Doe", "api_key": "key456"},
            output={"phone": "555-1234", "status": "complete"},
            scores={"quality": 0.8},
        )
        exp2.flush()  # Flush immediately to apply PII masking

        # Verify different masking applied
        events1 = get_object_json("experiment", exp1.id)
        events2 = get_object_json("experiment", exp2.id)

        # exp1 should mask api_key and password but not name
        event1 = events1[0]
        self.assertEqual(event1["input"]["api_key"], "REDACTED")
        self.assertEqual(event1["output"]["password"], "REDACTED")

        # exp2 should mask name and phone but not api_key
        event2 = events2[0]
        self.assertEqual(event2["input"]["name"], "NAME_REDACTED")
        self.assertEqual(event2["input"]["api_key"], "key456")  # Not masked by PII function
        self.assertEqual(event2["output"]["phone"], "XXX-XXX-XXXX")

    def test_no_masking_function(self):
        """Test that data is not masked when no masking function is provided."""
        braintrust.set_masking_function(None)  # Ensure no masking
        experiment = braintrust.init_experiment("test_no_masking")

        sensitive_data = {"api_key": "sk-12345", "password": "secret123", "name": "John Doe"}

        experiment.log(input=sensitive_data, output=sensitive_data, scores={"accuracy": 1.0})
        experiment.flush()

        events = get_object_json("experiment", experiment.id)
        event = events[0]

        # Verify no masking occurred
        self.assertEqual(event["input"]["api_key"], "sk-12345")
        self.assertEqual(event["input"]["password"], "secret123")
        self.assertEqual(event["input"]["name"], "John Doe")

    def test_masking_with_complex_data_structures(self):
        """Test masking with nested and complex data structures."""

        def complex_masking(data):
            if isinstance(data, dict):
                masked = {}
                for k, v in data.items():
                    if k == "secrets":
                        masked[k] = ["REDACTED" for _ in v] if isinstance(v, list) else "REDACTED"
                    elif k == "tokens" and isinstance(v, dict):
                        masked[k] = {key: "REDACTED" for key in v}
                    elif isinstance(v, dict):
                        masked[k] = complex_masking(v)
                    elif isinstance(v, list):
                        masked[k] = [complex_masking(item) if isinstance(item, (dict, list)) else item for item in v]
                    else:
                        masked[k] = v
                return masked
            elif isinstance(data, list):
                return [complex_masking(item) if isinstance(item, (dict, list)) else item for item in data]
            return data

        braintrust.set_masking_function(complex_masking)
        experiment = braintrust.init_experiment("complex_test")

        complex_data = {
            "users": [{"id": 1, "secrets": ["secret1", "secret2"]}, {"id": 2, "secrets": ["secret3", "secret4"]}],
            "tokens": {"access": "access-token-123", "refresh": "refresh-token-456"},
            "nested": {"level1": {"level2": {"secrets": "deep-secret", "public": "public-data"}}},
            "safe_data": "this should not be masked",
        }

        experiment.log(input=complex_data, output={"status": "processed"}, scores={"completeness": 1.0})
        experiment.flush()

        events = get_object_json("experiment", experiment.id)
        event = events[0]

        # Verify complex masking
        self.assertEqual(event["input"]["users"][0]["secrets"], ["REDACTED", "REDACTED"])
        self.assertEqual(event["input"]["users"][1]["secrets"], ["REDACTED", "REDACTED"])
        self.assertEqual(event["input"]["tokens"], {"access": "REDACTED", "refresh": "REDACTED"})
        self.assertEqual(event["input"]["nested"]["level1"]["level2"]["secrets"], "REDACTED")
        self.assertEqual(event["input"]["nested"]["level1"]["level2"]["public"], "public-data")
        self.assertEqual(event["input"]["safe_data"], "this should not be masked")

    def test_masking_with_concurrent_logging(self):
        """Test masking works correctly with concurrent logging."""
        braintrust.set_masking_function(masking_function_sensitive)
        experiment = braintrust.init_experiment("concurrent_test")

        def log_data(i):
            with experiment.start_span(f"span_{i}") as span:
                span.log(
                    input={"thread_id": i, "api_key": f"key-{i}", "data": f"contains sensitive data {i}"},
                    output={"result": f"output-{i}", "token": f"token-{i}"},
                )

        # Log data concurrently
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(log_data, i) for i in range(10)]
            for future in futures:
                future.result()

        experiment.flush()
        events = get_object_json("experiment", experiment.id)

        # Verify all events were masked correctly
        self.assertEqual(len(events), 10)
        for event in events:
            if "input" in event:
                self.assertEqual(event["input"]["api_key"], "REDACTED")
                self.assertEqual(event["input"]["data"].split()[1], "REDACTED")  # "contains REDACTED data {i}"
            if "output" in event:
                self.assertEqual(event["output"]["token"], "REDACTED")

    def test_masking_preserves_data_types(self):
        """Test that masking preserves data types and doesn't break serialization."""

        def type_preserving_masking(data):
            if isinstance(data, dict):
                masked = {}
                for k, v in data.items():
                    if k == "secret_number":
                        masked[k] = -1  # Replace with number
                    elif k == "secret_bool":
                        masked[k] = False  # Replace with boolean
                    elif k == "secret_null":
                        masked[k] = None  # Replace with null
                    elif k == "secret_list":
                        masked[k] = []  # Replace with empty list
                    elif isinstance(v, dict):
                        masked[k] = type_preserving_masking(v)
                    elif isinstance(v, list):
                        masked[k] = [
                            type_preserving_masking(item) if isinstance(item, (dict, list)) else item for item in v
                        ]
                    else:
                        masked[k] = v
                return masked
            return data

        braintrust.set_masking_function(type_preserving_masking)
        experiment = braintrust.init_experiment("type_test")

        experiment.log(
            input={
                "secret_number": 42,
                "secret_bool": True,
                "secret_null": "should be null",
                "secret_list": ["a", "b", "c"],
                "normal_data": {"nested": "value"},
            },
            output={"result": "processed"},
            scores={"accuracy": 0.95},
        )

        experiment.flush()
        events = get_object_json("experiment", experiment.id)
        event = events[0]

        # Verify types are preserved
        self.assertEqual(event["input"]["secret_number"], -1)
        self.assertIsInstance(event["input"]["secret_number"], int)
        self.assertEqual(event["input"]["secret_bool"], False)
        self.assertIsInstance(event["input"]["secret_bool"], bool)
        self.assertIsNone(event["input"]["secret_null"])
        self.assertEqual(event["input"]["secret_list"], [])
        self.assertIsInstance(event["input"]["secret_list"], list)
        self.assertEqual(event["input"]["normal_data"], {"nested": "value"})

    def test_global_set_masking_function(self):
        """Test the global set_masking_function API."""
        # Test 1: Clear any existing masking
        braintrust.set_masking_function(None)

        exp1 = braintrust.init_experiment("global_test_1")
        exp1.log(input={"password": "visible"}, output={"secret": "also_visible"}, scores={"score": 1.0})
        exp1.flush()

        events1 = get_object_json("experiment", exp1.id)
        # No masking should be applied
        self.assertEqual(events1[0]["input"]["password"], "visible")
        self.assertEqual(events1[0]["output"]["secret"], "also_visible")

        # Test 2: Set masking globally
        braintrust.set_masking_function(masking_function_sensitive)

        exp2 = braintrust.init_experiment("global_test_2")
        exp2.log(input={"password": "hidden"}, output={"secret": "masked"}, scores={"score": 1.0})
        exp2.flush()

        events2 = get_object_json("experiment", exp2.id)
        # Masking should be applied
        self.assertEqual(events2[0]["input"]["password"], "REDACTED")
        self.assertEqual(events2[0]["output"]["secret"], "REDACTED")

        # Test 3: Change masking function
        braintrust.set_masking_function(masking_function_pii)

        exp3 = braintrust.init_experiment("global_test_3")
        exp3.log(
            input={"name": "John Doe", "password": "still_visible"}, output={"result": "done"}, scores={"score": 1.0}
        )
        exp3.flush()

        events3 = get_object_json("experiment", exp3.id)
        # PII masking should be applied (masks name but not password)
        self.assertEqual(events3[0]["input"]["name"], "NAME_REDACTED")
        self.assertEqual(events3[0]["input"]["password"], "still_visible")  # PII function doesn't mask passwords

        # Clear masking at the end
        braintrust.set_masking_function(None)

    def test_masking_function_with_error(self):
        """Test that masking errors are handled gracefully and stack traces are captured."""

        def broken_masking_function(data):
            """A masking function that throws errors for certain data types."""
            if isinstance(data, dict):
                # This will throw an error when trying to iterate
                for key in data:
                    if key == "password":
                        # Simulate a complex error
                        raise ValueError(f"Cannot mask sensitive field '{key}' - internal masking error")
                    elif key == "error_dict":
                        # Special key to trigger error for testing
                        raise RuntimeError("Dict processing error")
                    elif key == "accuracy":
                        # Trigger error for scores and metrics fields
                        raise TypeError("Cannot process numeric score")
                    elif key == "latency":
                        # Trigger error for metrics field
                        raise ArithmeticError("Cannot process latency metric")
                return data
            elif isinstance(data, str):
                if "secret" in data.lower():
                    # Another type of error
                    result = 1 / 0  # ZeroDivisionError
                return data
            elif isinstance(data, list):
                # Try to access non-existent index
                if len(data) > 0:
                    _ = data[100]  # IndexError
                return data
            return data

        # Set the broken masking function
        braintrust.set_masking_function(broken_masking_function)

        # Test with experiment
        experiment = braintrust.init_experiment("test_masking_errors")

        # Log data that will trigger various errors
        experiment.log(
            input={"password": "my-password", "user": "test"},
            output="This contains SECRET information",
            expected=["item1", "item2"],
            metadata={"safe": "data"},
            scores={"accuracy": 0.9},
        )

        experiment.flush()

        # Check the logged data
        events = get_object_json("experiment", experiment.id)
        self.assertEqual(len(events), 1)
        event = events[0]

        # Verify error handling
        # The input should have an error message because of the password field
        self.assertEqual(event["input"], "ERROR: Failed to mask field 'input' - ValueError")

        # The output should have an error message because of division by zero
        self.assertEqual(event["output"], "ERROR: Failed to mask field 'output' - ZeroDivisionError")

        # The expected should have an error message because of index error
        self.assertEqual(event["expected"], "ERROR: Failed to mask field 'expected' - IndexError")

        # Metadata should be fine since it doesn't trigger any errors
        self.assertEqual(event["metadata"], {"safe": "data"})

        # Scores should be dropped (set to None) and error should be logged
        self.assertIsNone(event.get("scores"))
        self.assertIn("error", event)
        self.assertEqual(event["error"], "ERROR: Failed to mask field 'scores' - TypeError")

        # Test with logger and nested spans
        logger = braintrust.init_logger("test_masking_errors_logger")

        with logger.start_span("parent") as parent:
            parent.log(input={"api_key": "key123", "password": "secret"}, metadata={"request_id": "req-123"})

            with parent.start_span("child") as child:
                child.log(output="Result with secret data", expected=[1, 2, 3])

        logger.flush()

        # Since logger data isn't easily retrievable in the test,
        # we mainly verify that no exceptions were thrown during the process
        self.assertTrue(True)

        # Test with dataset
        dataset = braintrust.init_dataset("test_masking_errors_dataset")

        dataset.insert(
            input={"command": "login", "password": "test123"},
            expected="success with secret",  # String to trigger ZeroDivisionError
            metadata={"error_dict": "trigger_error"},  # Dict with error_dict key to trigger RuntimeError
        )

        dataset.flush()

        # Check dataset events
        dataset_events = get_object_json("dataset", dataset.id)
        self.assertEqual(len(dataset_events), 1)
        dataset_event = dataset_events[0]

        # Verify error handling in dataset
        self.assertEqual(dataset_event["input"], "ERROR: Failed to mask field 'input' - ValueError")
        self.assertEqual(dataset_event["expected"], "ERROR: Failed to mask field 'expected' - ZeroDivisionError")
        self.assertEqual(dataset_event["metadata"], {"error": "ERROR: Failed to mask field 'metadata' - RuntimeError"})

        # Test with metrics that triggers an error
        experiment2 = braintrust.init_experiment("test_masking_errors_metrics")
        experiment2.log(
            input={"data": "test"},
            output="result",
            scores={"score": 1.0},  # Safe score
            metrics={"latency": 100},  # This will trigger ArithmeticError
        )
        experiment2.flush()

        events2 = get_object_json("experiment", experiment2.id)
        self.assertEqual(len(events2), 1)
        event2 = events2[0]

        # Metrics should be dropped (set to None) and error should be logged
        self.assertIsNone(event2.get("metrics"))
        self.assertIn("error", event2)
        self.assertEqual(event2["error"], "ERROR: Failed to mask field 'metrics' - ArithmeticError")

        # Test with both scores and metrics failing
        experiment3 = braintrust.init_experiment("test_masking_errors_both")
        experiment3.log(
            input={"data": "test"},
            output="result",
            scores={"accuracy": 0.95},  # This will trigger TypeError
            metrics={"accuracy": 0.95, "latency": 100},  # Both will trigger errors
        )
        experiment3.flush()

        events3 = get_object_json("experiment", experiment3.id)
        self.assertEqual(len(events3), 1)
        event3 = events3[0]

        # Both should be dropped (set to None) and errors should be concatenated
        self.assertIsNone(event3.get("scores"))
        self.assertIsNone(event3.get("metrics"))
        self.assertIn("error", event3)
        self.assertIn("ERROR: Failed to mask field 'scores' - TypeError", event3["error"])
        # Metrics will trigger on first key that causes error (could be either accuracy or latency)
        self.assertTrue(
            "ERROR: Failed to mask field 'metrics' - TypeError" in event3["error"]
            or "ERROR: Failed to mask field 'metrics' - ArithmeticError" in event3["error"]
        )
        self.assertIn("; ", event3["error"])  # Check that errors are joined

        # Clear masking at the end
        braintrust.set_masking_function(None)
