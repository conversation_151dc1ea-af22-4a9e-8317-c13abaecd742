from datetime import datetime, timed<PERSON>ta
from uuid import uuid4

import braintrust
import pytz
from braintrust_local.api_db_util import log_raw
from jsonschema import validate as json_schema_validate
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
from tests.bt_services.time_buckets_expected import (
    PACIFIC_DAY_EXPECTED,
    PACIFIC_MONTH_EXPECTED,
    PACIFIC_YEAR_EXPECTED,
    SYDNEY_DAY_EXPECTED,
    SYDNEY_MONTH_EXPECTED,
    SYDNEY_YEAR_EXPECTED,
    UTC_DAY_EXPECTED,
    UTC_HOUR_EXPECTED,
    UTC_MINUTE_EXPECTED,
    UTC_MONTH_EXPECTED,
    UTC_SECONDS_EXPECTED,
    UTC_YEAR_EXPECTED,
)

PARAMETERS = [("postgres",), ("duckdb",)] + ([] if BraintrustAppTestBase.skip_brainstore() else [("brainstore",)])


class DrilldownTimeBucketsTest(BraintrustAppTestBase):
    def _log_rows(self, start_date, end_date, interval, project_id, experiment_id):
        current_date = start_date
        while current_date <= end_date:
            span_id = str(uuid4())
            timestamp = current_date.isoformat().replace("+00:00", "Z")
            log_raw(
                dict(
                    project_id=project_id,
                    experiment_id=experiment_id,
                    id=str(uuid4()),
                    span_id=span_id,
                    root_span_id=span_id,
                    # Use the timestamp as input so we can verify the bucketing using the input field
                    input=timestamp,
                    created=timestamp,
                )
            )
            current_date += interval

    def setUp(self):
        super().setUp()

        # For the years, months, dates, and hours tests,
        # log one row every hour from 5pm on 12/31/2024 to 5pm on 01/01/2025
        self.long_experiment = braintrust.init(project="long_project")
        self.long_project_id = self.long_experiment.project.id
        long_start_date = datetime(2024, 12, 31, 17, tzinfo=pytz.UTC)
        long_end_date = datetime(2025, 1, 1, 17, tzinfo=pytz.UTC)
        long_interval = timedelta(hours=1)
        self._log_rows(
            start_date=long_start_date,
            end_date=long_end_date,
            interval=long_interval,
            project_id=self.long_project_id,
            experiment_id=self.long_experiment.id,
        )

        # For the minutes test, log one row every 30 seconds from
        # 11:55pm on 12/31/2024 to 12:05am on 01/01/2025
        self.minutes_experiment = braintrust.init(project="minutes_project")
        self.minutes_project_id = self.minutes_experiment.project.id
        minutes_start_date = datetime(2024, 12, 31, 23, 55, tzinfo=pytz.UTC)
        minutes_end_date = datetime(2025, 1, 1, 0, 5, tzinfo=pytz.UTC)
        minutes_interval = timedelta(seconds=30)
        self._log_rows(
            start_date=minutes_start_date,
            end_date=minutes_end_date,
            interval=minutes_interval,
            project_id=self.minutes_project_id,
            experiment_id=self.minutes_experiment.id,
        )

        # For the seconds test, log one row every 200 milliseconds from
        # 11:59:58 on 12/31/2024 to 12:00:05am on 01/01/2025
        self.seconds_experiment = braintrust.init(project="seconds_project")
        self.seconds_project_id = self.seconds_experiment.project.id
        seconds_start_date = datetime(2024, 12, 31, 23, 59, 58, tzinfo=pytz.UTC)
        seconds_end_date = datetime(2025, 1, 1, 0, 0, 5, tzinfo=pytz.UTC)
        seconds_interval = timedelta(milliseconds=200)
        self._log_rows(
            start_date=seconds_start_date,
            end_date=seconds_end_date,
            interval=seconds_interval,
            project_id=self.seconds_project_id,
            experiment_id=self.seconds_experiment.id,
        )

        braintrust.flush()

    def _build_query(self, time_bucket, project_id, after_time):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": project_id}],
            },
            "dimensions": [
                {
                    "alias": "time",
                    "expr": {
                        "op": "function",
                        "name": {"op": "ident", "name": [time_bucket]},
                        "args": [{"op": "ident", "name": ["created"]}],
                    },
                },
                {"alias": "input", "expr": {"btql": "input"}},
            ],
            "filter": {
                "op": "gt",
                "left": {"op": "ident", "name": ["created"]},
                "right": {"op": "literal", "value": after_time},
            },
            "sort": [{"expr": {"btql": "input"}, "dir": "asc"}],
        }
        return query

    def _run_drilldown(self, query, mode, allow_error=False, tz_offset=None):
        ret = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": query,
                **self.make_mode_args(mode),
                **({"tz_offset": tz_offset} if tz_offset is not None else {}),
            },
        )
        if not allow_error:
            self.assertTrue(ret.ok, ret.text)

        response = ret.json()

        data = response.get("data")
        schema = response.get("schema")
        json_schema_validate(data, schema)

        return response

    def normalize_timestamp(self, timestamp):
        """
        Normalize timestamp strings to a consistent format by:
        1. Converting any space-separated datetime to T-separated
        2. Stripping the Z suffix if present
        3. Padding with zeros to 9 decimal places
        """
        timestamp = timestamp.replace(" ", "T").rstrip("Z")
        if "." in timestamp:
            main, subseconds = timestamp.rsplit(".", 1)
        else:
            main, subseconds = timestamp, ""
        return f"{main}.{subseconds.ljust(9, '0')}"

    def normalize_results(self, results):
        normalized = []
        for item in results:
            normalized_item = item.copy()
            for field in ["time", "input"]:
                normalized_item[field] = self.normalize_timestamp(item[field])
            normalized.append(normalized_item)

        # Sort by input timestamp ascending
        return sorted(normalized, key=lambda x: x["input"])

    def _verify(
        self,
        query,
        mode,
        utc_expected,
        pacific_expected,
        sydney_expected,
    ):
        utc_result = self.normalize_results(self._run_drilldown(query, mode)["data"])
        self.assertEqual(utc_result, self.normalize_results(utc_expected))

        pacific_result = self.normalize_results(self._run_drilldown(query, mode, tz_offset=420)["data"])
        self.assertEqual(pacific_result, self.normalize_results(pacific_expected))

        sydney_result = self.normalize_results(self._run_drilldown(query, mode, tz_offset=-600)["data"])
        self.assertEqual(sydney_result, self.normalize_results(sydney_expected))

    @parameterized.expand(PARAMETERS)
    def test_timestamp_bucket_years(self, mode):
        query = self._build_query(
            time_bucket="year", project_id=self.long_project_id, after_time="2024-08-31T16:00:00.000Z"
        )
        self._verify(
            query=query,
            mode=mode,
            utc_expected=UTC_YEAR_EXPECTED,
            pacific_expected=PACIFIC_YEAR_EXPECTED,
            sydney_expected=SYDNEY_YEAR_EXPECTED,
        )

    @parameterized.expand(PARAMETERS)
    def test_timestamp_bucket_months(self, mode):
        query = self._build_query(
            time_bucket="month", project_id=self.long_project_id, after_time="2024-08-31T16:00:00.000Z"
        )

        self._verify(
            query=query,
            mode=mode,
            utc_expected=UTC_MONTH_EXPECTED,
            pacific_expected=PACIFIC_MONTH_EXPECTED,
            sydney_expected=SYDNEY_MONTH_EXPECTED,
        )

    @parameterized.expand(PARAMETERS)
    def test_timestamp_bucket_days(self, mode):
        query = self._build_query(
            time_bucket="day", project_id=self.long_project_id, after_time="2024-08-31T16:00:00.000Z"
        )

        self._verify(
            query=query,
            mode=mode,
            utc_expected=UTC_DAY_EXPECTED,
            pacific_expected=PACIFIC_DAY_EXPECTED,
            sydney_expected=SYDNEY_DAY_EXPECTED,
        )

    @parameterized.expand(PARAMETERS)
    def test_timestamp_bucket_hours(self, mode):
        query = self._build_query(
            time_bucket="hour", project_id=self.long_project_id, after_time="2024-08-31T16:00:00.000Z"
        )

        self._verify(
            query=query,
            mode=mode,
            utc_expected=UTC_HOUR_EXPECTED,
            pacific_expected=UTC_HOUR_EXPECTED,
            sydney_expected=UTC_HOUR_EXPECTED,
        )

    @parameterized.expand(PARAMETERS)
    def test_timestamp_bucket_minutes(self, mode):
        query = self._build_query(
            time_bucket="minute", project_id=self.minutes_project_id, after_time="2024-12-31T23:56:00.000Z"
        )

        self._verify(
            query=query,
            mode=mode,
            utc_expected=UTC_MINUTE_EXPECTED,
            pacific_expected=UTC_MINUTE_EXPECTED,
            sydney_expected=UTC_MINUTE_EXPECTED,
        )

    @parameterized.expand(PARAMETERS)
    def test_timestamp_bucket_seconds(self, mode):
        query = self._build_query(
            time_bucket="second", project_id=self.seconds_project_id, after_time="2024-12-31T23:59:58.800000Z"
        )

        self._verify(
            query=query,
            mode=mode,
            utc_expected=UTC_SECONDS_EXPECTED,
            pacific_expected=UTC_SECONDS_EXPECTED,
            sydney_expected=UTC_SECONDS_EXPECTED,
        )
