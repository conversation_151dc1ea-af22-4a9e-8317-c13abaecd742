import { test } from "vitest";
import fs from "fs";
import path from "path";
import { sync as globSync } from "glob";
import { expect } from "vitest";
import { braintrustRootPath } from "@braintrust/local/dev";
import { z } from "zod";
import { LOCAL_APP_URL } from "./setup";
const NONDETERMINISTIC_FIELDS_PATH = path.join(
  `${braintrustRootPath()}/tests/bt_services`,
  "test_expect_nondeterministic_fields.json",
);

// Metrics to ignore (not real metrics, e.g. added by test code)
const IGNORED_METRICS = ["foo"];

function getAllExpectedJsonFilesFromRoot(): string[] {
  const rootDir = path.resolve(braintrustRootPath());
  const pattern = "**/*.expected.json";
  return globSync(pattern, {
    cwd: rootDir,
    absolute: true,
    ignore: [
      "**/.*",
      "**/node_modules/**",
      "**/__pycache__/**",
      "**/build/**",
      "**/data/**",
      "**/debug/**",
      "**/dist/**",
      "**/downloads/**",
      "**/eggs/**",
      "**/env/**",
      "**/instance/**",
      "**/jsdist/**",
      "**/jspm_packages/**",
      "**/lib/**",
      "**/lib64/**",
      "**/local/**",
      "**/modules/**",
      "**/notebooks/**",
      "**/out/**",
      "**/parts/**",
      "**/profile_default/**",
      "**/public/**",
      "**/sdist/**",
      "**/services/data/**",
      "**/target/**",
      "**/types/dist/**",
      "**/var/**",
      "**/venv.bak/**",
      "**/venv/**",
      "**/web_modules/**",
      "**/wheels/**",
    ],
  });
}

function getMetricsFromNondeterministicFields(): Set<string> {
  const fieldsRaw = JSON.parse(
    fs.readFileSync(NONDETERMINISTIC_FIELDS_PATH, "utf-8"),
  );
  const fields = z.array(z.array(z.string())).parse(fieldsRaw);
  const metrics = new Set<string>();
  for (const arr of fields) {
    if (arr.length === 2 && arr[0] === "metrics") {
      metrics.add(arr[1]);
    }
  }
  return metrics;
}

async function getMetricsFromExpectedJson(file: string): Promise<Set<string>> {
  const data = JSON.parse(await fs.promises.readFile(file, "utf-8"));
  const rows = z
    .array(z.object({ metrics: z.record(z.number()).optional() }))
    .parse(data);

  const metrics = new Set<string>();
  for (const row of rows) {
    if (row.metrics) {
      for (const key of Object.keys(row.metrics)) {
        metrics.add(key);
      }
    }
  }
  return metrics;
}

test("standardMetricsCrawl", async () => {
  const usedMetrics = new Set<string>();

  for (const m of getMetricsFromNondeterministicFields()) {
    usedMetrics.add(m);
  }

  const files = getAllExpectedJsonFilesFromRoot();
  const metricsSets = await Promise.all(
    files.map((file) => getMetricsFromExpectedJson(file)),
  );

  for (const metrics of metricsSets) {
    for (const m of metrics) {
      usedMetrics.add(m);
    }
  }

  const res = await fetch(`${LOCAL_APP_URL}/api/standard-metrics`);

  if (!res.ok)
    throw new Error(`Failed to fetch standard metrics: ${res.status}`);

  const standardMetrics: string[] = await res.json();
  const standardSet = new Set(standardMetrics);

  // Check for intersection between IGNORED_METRICS and standard metrics
  const intersection = IGNORED_METRICS.filter((m) => standardSet.has(m));
  expect(
    intersection,
    `IGNORED_METRICS contains metrics that are also in STANDARD_METRICS:\n` +
      intersection.map((m) => `  - ${m}`).join("\n"),
  ).toHaveLength(0);

  const missing = Array.from(usedMetrics)
    .filter((m) => !standardSet.has(m))
    .filter((m) => !IGNORED_METRICS.includes(m));

  if (missing.length > 0) {
    expect(
      missing,
      `The following metrics are used in tests but missing from the standard metrics list:\n` +
        missing.map((m) => `  - ${m}`).join("\n"),
    ).toHaveLength(0);
  }
});
