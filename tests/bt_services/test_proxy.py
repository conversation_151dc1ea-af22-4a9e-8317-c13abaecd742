import unittest

import braintrust
import openai
import requests
from openai.types.chat import Chat<PERSON><PERSON>pletionToolParam
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, TEST_ARGS, BraintrustAppTestBase, make_v1_url


class ProxyTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.proxy_url = braintrust.logger._state.proxy_url
        self.openai_base_url = f"{self.proxy_url}/v1"

    def tearDown(self):
        requests.get(f"{self.proxy_url}/proxy/dump-cache")
        super().tearDown()

    def run_request(self, *args, **kwargs):
        return super().run_request(*args, **{**kwargs, "headers": dict(Authorization=f"Bearer {self.org_api_key}")})

    def run_auto(self, **kwargs):
        return self.run_request("post", f"{self.proxy_url}/v1/auto", json=kwargs)

    @parameterized.expand(
        [
            ("gpt-3.5-turbo", "OPENAI_API_KEY"),
            ("gpt-4o", "OPENAI_API_KEY"),
            ("claude-3-haiku-20240307", "ANTHROPIC_API_KEY"),
        ]
    )
    def test_used_endpoint_header(self, model, provider):
        resp = self.run_auto(
            messages=[{"role": "user", "content": "What is 1+1?"}],
            model=model,
            temperature=0,
            stream=True,
            max_tokens=1024,
        )
        self.assertEqual(resp.headers["x-bt-used-endpoint"], provider)

    @parameterized.expand(
        [
            (object_type, root, parent_type, stream)
            for object_type in ["experiment", "project_logs"]
            for root in [True, False]
            for parent_type in ["id", "export"]
            for stream in [True, False]
        ]
    )
    def test_tracing(self, object_type, root, parent_type, stream):
        if object_type == "experiment" and root:
            raise unittest.SkipTest("Experiment roots cannot be LLM calls")

        if not root and parent_type == "id":
            raise unittest.SkipTest("Spans cannot be traced by id")

        parent = None
        parent_id = None
        if object_type == "experiment":
            parent = braintrust.init_experiment(project="p")
            parent_id = parent.id
        elif object_type == "project_logs":
            parent = braintrust.init_logger(project="p")
            parent_id = parent.project.id

        if not root:
            parent = parent.start_span("foo")

        parent_header = None

        if parent_type == "id":
            parent_header = f"{'experiment_id' if object_type == 'experiment' else 'project_id'}:{parent_id}"
        elif parent_type == "export":
            parent_header = parent.export()

        client = openai.OpenAI(
            base_url=self.openai_base_url,
            api_key=self.org_api_key,
            default_headers={"x-bt-parent": parent_header} if parent_header else None,
        )

        resp = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": "What is 1+1?"}],
            temperature=0,
            stream=stream,
        )

        if stream:
            for chunk in resp:
                # Exhaust the stream
                pass

        braintrust.logger.flush()
        self.run_request("post", f"{self.proxy_url}/debug/flush")

        resp = self.run_request(
            "post", f"{LOCAL_API_URL}/btql", json={"query": f"select: * | from: {object_type}('{parent_id}')"}
        ).json()

        data = resp["data"]
        print(data)

        self.assertTrue(len(data) > 0)
        self.assertTrue([x for x in data if x["input"] == [{"role": "user", "content": "What is 1+1?"}]])

    @parameterized.expand([(stream, wrap) for stream in [False, True] for wrap in [False, True]])
    def test_multi_tool(self, stream, wrap):
        # Log directly to a project
        parent = braintrust.init_logger(project="p")

        if wrap:
            # Use wrap_openai for tracing - need to use the logger context
            with parent:
                client = braintrust.wrap_openai(
                    openai.OpenAI(
                        base_url=self.openai_base_url,
                        api_key=self.org_api_key,
                    )
                )
        else:
            # Use proxy for tracing
            parent_header = parent.export()
            client = openai.OpenAI(
                base_url=self.openai_base_url,
                api_key=self.org_api_key,
                default_headers={"x-bt-parent": parent_header},
            )

        # Define tools that can be called in parallel
        tools: list[ChatCompletionToolParam] = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get the weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "The location to get weather for"}
                        },
                        "required": ["location"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "get_time",
                    "description": "Get the current time for a timezone",
                    "parameters": {
                        "type": "object",
                        "properties": {"timezone": {"type": "string", "description": "The timezone to get time for"}},
                        "required": ["timezone"],
                    },
                },
            },
        ]

        # Make a request that should trigger parallel tool calls
        resp = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": "What's the weather in New York and the time in Tokyo?"}],
            tools=tools,
            temperature=0,
            stream=stream,
        )

        if stream:
            # Consume the stream
            for chunk in resp:  # type: ignore
                # Exhaust the stream
                pass

        braintrust.logger.flush()
        self.run_request("post", f"{self.proxy_url}/debug/flush")

        # Query the logged data
        resp = self.run_request(
            "post", f"{LOCAL_API_URL}/btql", json={"query": f"select: * | from: project_logs('{parent.project.id}')"}
        ).json()

        data = resp["data"]
        print(data)

        # Verify that the tools were logged
        self.assertTrue(len(data) > 0)
        # Look for entries that contain tool calls
        tool_calls = [
            x
            for x in data
            if x.get("output")
            and isinstance(x["output"], list)
            and len(x["output"]) > 0
            and x["output"][0].get("message")
            and x["output"][0]["message"].get("tool_calls")
        ]
        self.assertTrue(len(tool_calls) > 0)

        # Verify we have both tools called
        first_entry = tool_calls[0]
        actual_tool_calls = first_entry["output"][0]["message"]["tool_calls"]
        self.assertEqual(len(actual_tool_calls), 2)

        # Verify the tool names
        tool_names = [call["function"]["name"] for call in actual_tool_calls]
        self.assertIn("get_weather", tool_names)
        self.assertIn("get_time", tool_names)
