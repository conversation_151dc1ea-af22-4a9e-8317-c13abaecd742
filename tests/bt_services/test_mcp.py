#!/usr/bin/env python3
"""
Comprehensive test suite for the Braintrust MCP server.

This test suite validates:
- OAuth authentication requirements
- Tool listing and calling
- Object resolution functionality
- Documentation search
- Error handling

The tests are designed to work with both full test infrastructure and standalone.
"""

import asyncio
import json
import os
import sys
import unittest
from typing import Any, Dict, Optional

import braintrust
import pytest

# Handle imports for both standalone and pytest execution
try:
    from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
except ImportError:
    # Fallback for standalone execution
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)  # tests directory
    root_dir = os.path.dirname(parent_dir)  # project root
    sys.path.insert(0, root_dir)
    from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase

try:
    import httpx
except ImportError:
    print("Please install httpx: pip install httpx")
    sys.exit(1)


def parse_mcp_response(response: httpx.Response) -> Dict[str, Any]:
    """Parse MCP response, handling both SSE and regular JSON formats."""
    content = response.text.strip()
    if content.startswith("event: message\ndata: "):
        # Extract JSON from SSE format
        json_data = content.split("data: ", 1)[1].strip()
        return json.loads(json_data)
    else:
        # Fallback to regular JSON parsing
        return response.json()


class MCPClient:
    """Simple HTTP-based MCP client for testing."""

    def __init__(self, base_url: str = LOCAL_API_URL, access_token: Optional[str] = None):
        self.base_url = base_url
        self.access_token = access_token

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call an MCP tool."""
        if not self.access_token:
            raise ValueError("Access token required")

        request_data = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {"name": tool_name, "arguments": arguments},
            "id": 1,
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{self.base_url}/mcp",
                json=request_data,
                headers={
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream",
                    "mcp-protocol-version": "2025-06-18",
                },
            )

            response.raise_for_status()
            return parse_mcp_response(response)

    async def list_tools(self) -> Dict[str, Any]:
        """List available tools."""
        request_data = {"jsonrpc": "2.0", "method": "tools/list", "params": {}, "id": 1}

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "mcp-protocol-version": "2025-06-18",
        }

        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(f"{self.base_url}/mcp", json=request_data, headers=headers)
            response.raise_for_status()
            return parse_mcp_response(response)

    async def list_resources(self) -> Dict[str, Any]:
        """List available resources."""
        request_data = {"jsonrpc": "2.0", "method": "resources/list", "params": {}, "id": 1}

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "mcp-protocol-version": "2025-06-18",
        }

        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(f"{self.base_url}/mcp", json=request_data, headers=headers)
            response.raise_for_status()
            return parse_mcp_response(response)

    async def read_resource(self, uri: str) -> Dict[str, Any]:
        """Read a specific resource."""
        request_data = {"jsonrpc": "2.0", "method": "resources/read", "params": {"uri": uri}, "id": 1}

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "mcp-protocol-version": "2025-06-18",
        }

        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(f"{self.base_url}/mcp", json=request_data, headers=headers)
            response.raise_for_status()
            return parse_mcp_response(response)


class TestMCP(BraintrustAppTestBase):
    """Comprehensive MCP test suite."""

    def setUp(self):
        """Set up test environment, handling infrastructure failures gracefully."""
        try:
            super().setUp()
            self.test_setup_successful = True
        except Exception as e:
            print(f"Test infrastructure setup failed: {e}")
            self.test_setup_successful = False
            self.org_api_key = None

    def test_authentication_required(self):
        """Test that MCP server requires authentication."""

        async def _test():
            client = MCPClient()

            with pytest.raises(httpx.HTTPStatusError) as exc_info:
                await client.list_tools()

            assert exc_info.value.response.status_code == 401
            response_data = exc_info.value.response.json()
            assert response_data.get("error", {}).get("code") == -32001
            assert "Authentication required" in response_data.get("error", {}).get("message", "")

        asyncio.run(_test())

    def test_list_tools(self):
        """Test listing available MCP tools."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            response = await client.list_tools()

            assert "result" in response
            assert "tools" in response["result"]

            tools = response["result"]["tools"]
            tool_names = [tool["name"] for tool in tools]

            # Verify expected tools exist
            assert "resolve_object" in tool_names
            assert "search_docs" in tool_names
            assert "list_recent_objects" in tool_names
            assert "btql_query" in tool_names
            assert "infer_schema" in tool_names
            assert "summarize_experiment" in tool_names
            assert "generate_permalink" in tool_names

            # Check resolve_object tool schema
            resolve_tool = next(t for t in tools if t["name"] == "resolve_object")
            assert "description" in resolve_tool
            assert "inputSchema" in resolve_tool

            # Verify expected parameters
            properties = resolve_tool["inputSchema"]["properties"]
            assert "object_type" in properties
            assert "project_name" in properties
            assert "object_name" in properties
            assert "object_id" in properties

        asyncio.run(_test())

    def test_resolve_project_logs(self):
        """Test resolving project logs by project name."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create a test project using braintrust.init
            experiment = braintrust.init("mcp-test-project")
            project_id = experiment.project.id
            project_name = experiment.project.name

            client = MCPClient(access_token=self.org_api_key)

            # Test resolving by project name (use "project_logs" as object_type)
            response = await client.call_tool(
                "resolve_object", {"object_type": "project_logs", "project_name": project_name}
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1
            assert content[0]["type"] == "text"

            text_content = content[0]["text"]

            # Should get a valid JSON response with the project information
            if text_content.startswith("{"):
                result_data = json.loads(text_content)
                assert result_data["object_type"] == "project_logs"
                assert result_data["project_name"] == project_name
                # Should include the resolved object_id (project_id for project_logs)
                assert "object_id" in result_data
                assert (
                    result_data["object_id"] == project_id
                ), f"Expected project_id {project_id}, got {result_data.get('object_id')}"
            else:
                # If we get a plain text error, the functionality might be broken
                self.fail(f"Expected JSON response but got plain text: {text_content}")

        asyncio.run(_test())

    def test_resolve_project_logs_multiple(self):
        """Test resolving multiple different project logs."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create multiple projects to ensure we're getting the right one
            experiment1 = braintrust.init("mcp-project-logs-test-1")
            experiment2 = braintrust.init("mcp-project-logs-test-2")

            project1_name = experiment1.project.name
            project1_id = experiment1.project.id
            project2_name = experiment2.project.name
            project2_id = experiment2.project.id

            client = MCPClient(access_token=self.org_api_key)

            # Initialize variables to avoid NameError
            result1 = None
            result2 = None

            # Test resolving project1 logs
            response1 = await client.call_tool(
                "resolve_object", {"object_type": "project_logs", "project_name": project1_name}
            )

            assert "result" in response1
            content1 = response1["result"]["content"]
            text_content1 = content1[0]["text"]

            if text_content1.startswith("{"):
                result1 = json.loads(text_content1)
                assert result1["object_type"] == "project_logs"
                assert result1["project_name"] == project1_name
                assert result1["object_id"] == project1_id
            else:
                self.fail(f"project_logs resolution failed for {project1_name}: {text_content1}")

            # Test resolving project2 logs
            response2 = await client.call_tool(
                "resolve_object", {"object_type": "project_logs", "project_name": project2_name}
            )

            assert "result" in response2
            content2 = response2["result"]["content"]
            text_content2 = content2[0]["text"]

            if text_content2.startswith("{"):
                result2 = json.loads(text_content2)
                assert result2["object_type"] == "project_logs"
                assert result2["project_name"] == project2_name
                assert result2["object_id"] == project2_id
            else:
                self.fail(f"project_logs resolution failed for {project2_name}: {text_content2}")

            # Ensure we got different projects
            assert result1 is not None and result2 is not None, "Results should be defined"
            assert result1["object_id"] != result2["object_id"], "Should resolve to different projects"

        asyncio.run(_test())

    def test_resolve_prompts_and_functions(self):
        """Test resolving project prompts and functions."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create a test project
            experiment = braintrust.init("mcp-prompts-functions-test")
            project_name = experiment.project.name
            project_id = experiment.project.id

            client = MCPClient(access_token=self.org_api_key)

            # Test project_prompts resolution
            response_prompts = await client.call_tool(
                "resolve_object", {"object_type": "project_prompts", "project_name": project_name}
            )

            assert "result" in response_prompts
            content_prompts = response_prompts["result"]["content"]
            text_content_prompts = content_prompts[0]["text"]

            # Check if project_prompts resolution works
            if text_content_prompts.startswith("{"):
                result_prompts = json.loads(text_content_prompts)
                assert result_prompts["object_type"] == "project_prompts"
                assert result_prompts["project_name"] == project_name
                # For project_prompts, object_id should be the project_id
                if "object_id" in result_prompts:
                    assert result_prompts["object_id"] == project_id
            else:
                # May return plain text if no prompts exist, that's okay
                assert isinstance(text_content_prompts, str) and len(text_content_prompts) > 0

            # Test project_functions resolution
            response_functions = await client.call_tool(
                "resolve_object", {"object_type": "project_functions", "project_name": project_name}
            )

            assert "result" in response_functions
            content_functions = response_functions["result"]["content"]
            text_content_functions = content_functions[0]["text"]

            # Check if project_functions resolution works
            if text_content_functions.startswith("{"):
                result_functions = json.loads(text_content_functions)
                assert result_functions["object_type"] == "project_functions"
                assert result_functions["project_name"] == project_name
                # For project_functions, object_id should be the project_id
                if "object_id" in result_functions:
                    assert result_functions["object_id"] == project_id
            else:
                # May return plain text if no functions exist, that's okay
                assert isinstance(text_content_functions, str) and len(text_content_functions) > 0

        asyncio.run(_test())

    def test_resolve_playground_and_sessions(self):
        """Test resolving playground logs and prompt sessions."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create a test project
            experiment = braintrust.init("mcp-playground-prompt-test")
            project_name = experiment.project.name

            client = MCPClient(access_token=self.org_api_key)

            # Test playground_logs resolution
            try:
                response_playground = await client.call_tool(
                    "resolve_object", {"object_type": "playground_logs", "project_name": project_name}
                )

                assert "result" in response_playground
                content_playground = response_playground["result"]["content"]
                text_content_playground = content_playground[0]["text"]

                # Should get some response (JSON or plain text)
                assert isinstance(text_content_playground, str) and len(text_content_playground) > 0

                if text_content_playground.startswith("{"):
                    result_playground = json.loads(text_content_playground)
                    assert result_playground["object_type"] == "playground_logs"
                    assert result_playground["project_name"] == project_name

            except Exception as e:
                # playground_logs might require specific setup or might not be fully implemented
                assert "MCP error" in str(e) or "HTTPStatusError" in str(e.__class__.__name__)

            # Test prompt_session resolution
            try:
                response_prompt_session = await client.call_tool(
                    "resolve_object", {"object_type": "prompt_session", "project_name": project_name}
                )

                assert "result" in response_prompt_session
                content_prompt_session = response_prompt_session["result"]["content"]
                text_content_prompt_session = content_prompt_session[0]["text"]

                # Should get some response (JSON or plain text)
                assert isinstance(text_content_prompt_session, str) and len(text_content_prompt_session) > 0

                if text_content_prompt_session.startswith("{"):
                    result_prompt_session = json.loads(text_content_prompt_session)
                    assert result_prompt_session["object_type"] == "prompt_session"
                    assert result_prompt_session["project_name"] == project_name

            except Exception as e:
                # prompt_session might require specific setup or might not be fully implemented
                assert "MCP error" in str(e) or "HTTPStatusError" in str(e.__class__.__name__)

        asyncio.run(_test())

    def test_resolve_experiment(self):
        """Test resolving experiments by project and name."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test project and experiment using braintrust.init
            experiment = braintrust.init("mcp-exp-test-project", "mcp-test-experiment")
            project_name = experiment.project.name
            experiment_name = experiment.name

            client = MCPClient(access_token=self.org_api_key)

            response = await client.call_tool(
                "resolve_object",
                {"object_type": "experiment", "project_name": project_name, "object_name": experiment_name},
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1

            # Parse the JSON response
            result_data = json.loads(content[0]["text"])
            assert result_data["object_type"] == "experiment"
            assert result_data["project_name"] == project_name
            assert result_data["object_name"] == experiment_name
            # Should include resolved object_id
            assert "object_id" in result_data

        asyncio.run(_test())

    def test_resolve_dataset(self):
        """Test resolving datasets by project and name."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test dataset using braintrust.init_dataset
            dataset = braintrust.init_dataset("mcp-dataset-test-project", "mcp-test-dataset")
            project_name = dataset.project.name
            dataset_name = dataset.name

            client = MCPClient(access_token=self.org_api_key)

            response = await client.call_tool(
                "resolve_object",
                {"object_type": "dataset", "project_name": project_name, "object_name": dataset_name},
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1

            # Parse the JSON response
            result_data = json.loads(content[0]["text"])
            assert result_data["object_type"] == "dataset"
            assert result_data["project_name"] == project_name
            assert result_data["object_name"] == dataset_name
            # Should include resolved object_id
            assert "object_id" in result_data

        asyncio.run(_test())

    def test_resolve_by_id(self):
        """Test resolving objects by ID rather than name."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test experiment
            experiment = braintrust.init("mcp-id-test-project", "mcp-id-test-experiment")
            experiment_id = experiment.id

            client = MCPClient(access_token=self.org_api_key)

            # Test resolving by experiment ID
            response = await client.call_tool(
                "resolve_object",
                {"object_type": "experiment", "object_id": experiment_id},
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1

            # Parse the JSON response
            result_data = json.loads(content[0]["text"])
            assert result_data["object_type"] == "experiment"
            assert result_data["object_id"] == experiment_id
            # Should include resolved project_name and object_name
            assert "project_name" in result_data
            assert "object_name" in result_data

        asyncio.run(_test())

    def test_resolve_all_types(self):
        """Test resolving all supported object types."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            # Create a test project for all types
            experiment = braintrust.init("mcp-all-types-test", "test-experiment")
            dataset = braintrust.init_dataset("mcp-all-types-test", "test-dataset")

            project_name = experiment.project.name

            # Test different object types
            test_cases = [
                {
                    "object_type": "project_logs",
                    "project_name": project_name,
                    "expected_keys": ["object_type", "project_name", "object_id"],
                },
                {
                    "object_type": "experiment",
                    "project_name": project_name,
                    "object_name": "test-experiment",
                    "expected_keys": ["object_type", "project_name", "object_name", "object_id"],
                },
                {
                    "object_type": "dataset",
                    "project_name": project_name,
                    "object_name": "test-dataset",
                    "expected_keys": ["object_type", "project_name", "object_name", "object_id"],
                },
                # These types may not have easily creatable objects in tests, but we can test they're supported
                {
                    "object_type": "project_prompts",
                    "project_name": project_name,
                    "expected_keys": ["object_type", "project_name"],
                },
                {
                    "object_type": "project_functions",
                    "project_name": project_name,
                    "expected_keys": ["object_type", "project_name"],
                },
            ]

            for test_case in test_cases:
                params = {k: v for k, v in test_case.items() if k not in ["expected_keys"]}

                try:
                    response = await client.call_tool("resolve_object", params)

                    assert "result" in response
                    content = response["result"]["content"]
                    assert len(content) == 1

                    text_content = content[0]["text"]

                    # Handle both JSON and plain text responses
                    if text_content.startswith("{"):
                        result_data = json.loads(text_content)
                        assert result_data["object_type"] == test_case["object_type"]

                        # Check that expected keys are present (some may be None for non-existent objects)
                        for key in test_case["expected_keys"]:
                            assert key in result_data

                    else:
                        # Plain text response (e.g., error messages)
                        assert isinstance(text_content, str)
                        assert len(text_content) > 0

                except Exception as e:
                    # Some object types might not be fully supported or might require specific setup
                    # For now, just ensure they don't cause server errors
                    assert "MCP error" in str(e) or "HTTPStatusError" in str(
                        e.__class__.__name__
                    ), f"Unexpected error for {test_case['object_type']}: {e}"

        asyncio.run(_test())

    def test_search_docs(self):
        """Test searching documentation."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            response = await client.call_tool("search_docs", {"query": "experiment", "top_k": 3})

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1
            assert content[0]["type"] == "text"

            # Should return formatted documentation
            text_content = content[0]["text"]
            assert isinstance(text_content, str)
            assert len(text_content) > 0

        asyncio.run(_test())

    def test_list_recent_objects(self):
        """Test listing recent objects of different types."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create some test objects
            experiment = braintrust.init("mcp-list-test-project", "test-experiment")
            dataset = braintrust.init_dataset("mcp-list-test-project", "test-dataset")
            project_name = experiment.project.name

            client = MCPClient(access_token=self.org_api_key)

            # Test listing projects
            response_projects = await client.call_tool("list_recent_objects", {"object_type": "project", "limit": 5})

            assert "result" in response_projects
            content_projects = response_projects["result"]["content"]
            assert len(content_projects) == 1
            assert content_projects[0]["type"] == "text"

            text_content_projects = content_projects[0]["text"]
            assert isinstance(text_content_projects, str)
            assert len(text_content_projects) > 0

            # Should be valid JSON
            projects_data = json.loads(text_content_projects)
            assert "objects" in projects_data or isinstance(projects_data, list)

            # Test listing experiments within a project
            response_experiments = await client.call_tool(
                "list_recent_objects", {"object_type": "experiment", "project_name": project_name, "limit": 5}
            )

            assert "result" in response_experiments
            content_experiments = response_experiments["result"]["content"]
            assert len(content_experiments) == 1
            assert content_experiments[0]["type"] == "text"

            text_content_experiments = content_experiments[0]["text"]
            experiments_data = json.loads(text_content_experiments)
            assert "objects" in experiments_data or isinstance(experiments_data, list)

            # Test listing datasets within a project
            response_datasets = await client.call_tool(
                "list_recent_objects", {"object_type": "dataset", "project_name": project_name, "limit": 5}
            )

            assert "result" in response_datasets
            content_datasets = response_datasets["result"]["content"]
            assert len(content_datasets) == 1
            assert content_datasets[0]["type"] == "text"

            text_content_datasets = content_datasets[0]["text"]
            datasets_data = json.loads(text_content_datasets)
            assert "objects" in datasets_data or isinstance(datasets_data, list)

        asyncio.run(_test())

    def test_list_recent_objects_all_types(self):
        """Test list_recent_objects supports all object types."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create a test project for testing
            experiment = braintrust.init("mcp-list-all-types", "test-exp")
            project_name = experiment.project.name

            client = MCPClient(access_token=self.org_api_key)

            # Test different object types that list_recent_objects should support
            test_cases = [
                {"object_type": "project", "requires_project": False},
                {"object_type": "experiment", "requires_project": True},
                {"object_type": "dataset", "requires_project": True},
                {"object_type": "prompt", "requires_project": True},
                {"object_type": "function", "requires_project": True},
            ]

            for test_case in test_cases:
                params = {"object_type": test_case["object_type"], "limit": 3}

                # Add project_name for types that require it
                if test_case["requires_project"]:
                    params["project_name"] = project_name

                try:
                    response = await client.call_tool("list_recent_objects", params)

                    assert "result" in response
                    content = response["result"]["content"]
                    assert len(content) == 1
                    assert content[0]["type"] == "text"

                    text_content = content[0]["text"]
                    assert isinstance(text_content, str)
                    assert len(text_content) > 0

                    # Should be valid JSON
                    data = json.loads(text_content)
                    # API might return {"objects": [...]} or just [...]
                    assert "objects" in data or isinstance(data, list)

                except Exception as e:
                    # Some object types might not be fully supported yet
                    # But they shouldn't cause server errors
                    assert "MCP error" in str(e) or "HTTPStatusError" in str(
                        e.__class__.__name__
                    ), f"Unexpected error for {test_case['object_type']}: {e}"

        asyncio.run(_test())

    def test_list_recent_objects_limits(self):
        """Test list_recent_objects respects limit parameter."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            # Test different limits
            for limit in [1, 5, 10, 20]:
                response = await client.call_tool("list_recent_objects", {"object_type": "project", "limit": limit})

                assert "result" in response
                content = response["result"]["content"]
                text_content = content[0]["text"]
                data = json.loads(text_content)

                # Verify the response structure
                if "objects" in data:
                    objects = data["objects"]
                elif isinstance(data, list):
                    objects = data
                else:
                    objects = []

                # Should not exceed the requested limit
                assert len(objects) <= limit, f"Returned {len(objects)} objects but limit was {limit}"

            # Test default limit (should be 10 according to schema)
            response_default = await client.call_tool("list_recent_objects", {"object_type": "project"})

            assert "result" in response_default
            content_default = response_default["result"]["content"]
            text_content_default = content_default[0]["text"]
            data_default = json.loads(text_content_default)

            if "objects" in data_default:
                objects_default = data_default["objects"]
            elif isinstance(data_default, list):
                objects_default = data_default
            else:
                objects_default = []

            # Should default to 10 or fewer
            assert len(objects_default) <= 10

        asyncio.run(_test())

    def test_resolve_nonexistent(self):
        """Test resolving nonexistent objects."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            # Try to resolve a nonexistent project
            response = await client.call_tool(
                "resolve_object", {"object_type": "project_logs", "project_name": "nonexistent-project-name"}
            )

            # Should complete but may return empty/null results or error messages
            assert "result" in response
            content = response["result"]["content"]
            text_content = content[0]["text"]

            # The API behavior may vary - it might return JSON results or plain text error
            if text_content.startswith("{"):
                # JSON response
                result_data = json.loads(text_content)
                assert result_data["object_type"] == "project_logs"
                assert result_data["project_name"] == "nonexistent-project-name"
            else:
                # Plain text error message (e.g. "Missing read access")
                assert isinstance(text_content, str)
                assert len(text_content) > 0

        asyncio.run(_test())

    def test_all_object_types_supported(self):
        """Test that all object types are accepted by the API."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            # Test all supported object types from the schema
            supported_types = [
                "experiment",
                "dataset",
                "prompt_session",
                "playground_logs",
                "project_logs",
                "project_prompts",
                "project_functions",
            ]

            for object_type in supported_types:
                try:
                    # Try to resolve a non-existent object of each type
                    response = await client.call_tool(
                        "resolve_object", {"object_type": object_type, "project_name": "nonexistent-project"}
                    )

                    # Should get a valid response (even if the object doesn't exist)
                    assert "result" in response
                    content = response["result"]["content"]
                    assert len(content) == 1

                    # Content can be either JSON or plain text error
                    text_content = content[0]["text"]
                    assert isinstance(text_content, str)
                    assert len(text_content) > 0

                except httpx.HTTPStatusError as e:
                    # If we get an HTTP error, it shouldn't be a validation error (400)
                    # It should be authorization (401/403) or server error (5xx)
                    assert (
                        e.response.status_code != 400
                    ), f"Object type '{object_type}' not supported by resolve_object tool"
                    # For this test, authorization errors are acceptable
                    assert e.response.status_code in [401, 403, 500, 502, 503, 504]

        asyncio.run(_test())

    def test_infer_schema_tool(self):
        """Test the infer_schema tool functionality."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test experiment with some data
            experiment = braintrust.init("mcp-infer-schema-test", "test-experiment")

            # Log some test data with various field types
            experiment.log(
                input={"question": "What is the capital of France?"},
                output="Paris",
                expected="Paris",
                metadata={"model": "gpt-4", "user_id": "test_user_123", "session_id": "session_456"},
                scores={"Accuracy": 1.0, "Helpfulness": 0.9},
            )
            experiment.log(
                input={"question": "What is 2+2?"},
                output="4",
                expected="4",
                metadata={"model": "gpt-3.5", "user_id": "test_user_456", "session_id": "session_789"},
                scores={"Accuracy": 1.0, "Helpfulness": 0.8},
            )

            # Ensure logs are flushed
            experiment.flush()

            client = MCPClient(access_token=self.org_api_key)

            # Test basic schema inference
            response = await client.call_tool(
                "infer_schema", {"object_type": "experiment", "object_ids": [experiment.id]}
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1
            assert content[0]["type"] == "text"

            schema_text = content[0]["text"]
            assert isinstance(schema_text, str)
            assert len(schema_text) > 0

            # Should include common field patterns or be "No fields found."
            # (experiment data may not be immediately available for schema inference)
            if schema_text != "No fields found.":
                assert "input" in schema_text or "metadata" in schema_text or "scores" in schema_text
            else:
                # If no fields found, that's also a valid response for newly created experiments
                assert schema_text == "No fields found."

            # Test with sample values
            response_with_samples = await client.call_tool(
                "infer_schema",
                {"object_type": "experiment", "object_ids": [experiment.id], "include_sample_values": True},
            )

            assert "result" in response_with_samples
            sample_content = response_with_samples["result"]["content"][0]["text"]

            # With sample values, should have similar or more detailed output
            assert isinstance(sample_content, str)
            assert len(sample_content) > 0

            # Test with specific fields
            response_filtered = await client.call_tool(
                "infer_schema",
                {"object_type": "experiment", "object_ids": [experiment.id], "fields": ["metadata", "scores"]},
            )

            assert "result" in response_filtered
            filtered_content = response_filtered["result"]["content"][0]["text"]
            assert isinstance(filtered_content, str)

        asyncio.run(_test())

    def test_btql_query_tool(self):
        """Test the btql_query tool functionality."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test experiment with data
            experiment = braintrust.init("mcp-btql-test", "test-experiment")

            # Log test data
            experiment.log(
                input={"question": "Test question 1"},
                output="Test answer 1",
                metadata={"model": "gpt-4", "category": "test"},
                scores={"Accuracy": 0.9},
            )
            experiment.log(
                input={"question": "Test question 2"},
                output="Test answer 2",
                metadata={"model": "gpt-3.5", "category": "test"},
                scores={"Accuracy": 0.8},
            )

            # Ensure logs are flushed
            experiment.flush()

            client = MCPClient(access_token=self.org_api_key)

            # Test basic query
            response = await client.call_tool(
                "btql_query",
                {
                    "explanation": "Getting all data from the test experiment to verify basic query functionality",
                    "query": "select: *",
                    "object_type": "experiment",
                    "object_ids": [experiment.id],
                    "shape": "spans",
                },
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1
            assert content[0]["type"] == "text"

            query_result = json.loads(content[0]["text"])
            assert "data" in query_result
            assert "schema" in query_result

            # Should have data from our logged entries
            data = query_result["data"]
            assert isinstance(data, list)
            assert len(data) >= 2  # At least our 2 logged entries

            # Test filtered query
            filtered_response = await client.call_tool(
                "btql_query",
                {
                    "explanation": "Filtering experiment data to only show GPT-4 model results",
                    "query": "select: input, output, metadata.model | filter: metadata.model = 'gpt-4'",
                    "object_type": "experiment",
                    "object_ids": [experiment.id],
                    "shape": "spans",
                },
            )

            assert "result" in filtered_response
            filtered_result = json.loads(filtered_response["result"]["content"][0]["text"])
            filtered_data = filtered_result["data"]

            # Should have filtered results
            assert isinstance(filtered_data, list)
            assert len(filtered_data) >= 1

            # Test aggregation query
            agg_response = await client.call_tool(
                "btql_query",
                {
                    "explanation": "Aggregating experiment data by model to see performance statistics",
                    "query": "dimensions: metadata.model | measures: count(1) as calls, avg(scores.Accuracy) as avg_accuracy",
                    "object_type": "experiment",
                    "object_ids": [experiment.id],
                    "shape": "spans",
                },
            )

            assert "result" in agg_response
            agg_result = json.loads(agg_response["result"]["content"][0]["text"])
            agg_data = agg_result["data"]

            # Should have aggregated results by model
            assert isinstance(agg_data, list)
            assert len(agg_data) >= 1

        asyncio.run(_test())

    def test_summarize_experiment_tool(self):
        """Test the summarize_experiment tool functionality."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test experiment
            experiment = braintrust.init("mcp-summarize-test", "test-experiment")

            # Log some test data with scores
            experiment.log(
                input={"question": "Test 1"},
                output="Answer 1",
                expected="Answer 1",
                scores={"Accuracy": 1.0, "Helpfulness": 0.9},
            )
            experiment.log(
                input={"question": "Test 2"},
                output="Answer 2",
                expected="Answer 2",
                scores={"Accuracy": 0.8, "Helpfulness": 0.7},
            )

            # Ensure logs are flushed
            experiment.flush()

            client = MCPClient(access_token=self.org_api_key)

            # Test basic experiment summary
            response = await client.call_tool("summarize_experiment", {"experiment_id": experiment.id})

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1
            assert content[0]["type"] == "text"

            summary_text = content[0]["text"]
            summary_data = json.loads(summary_text)

            # Should have summary structure
            assert isinstance(summary_data, dict)
            # Common fields in experiment summaries
            expected_fields = ["experiment_name", "project_name", "scores", "metrics"]
            found_fields = [field for field in expected_fields if field in summary_data]
            assert len(found_fields) > 0, f"Expected some summary fields, got: {list(summary_data.keys())}"

            # Create comparison experiment
            comparison_exp = braintrust.init("mcp-summarize-test", "comparison-experiment")
            comparison_exp.log(
                input={"question": "Test 1"},
                output="Different Answer 1",
                expected="Answer 1",
                scores={"Accuracy": 0.7, "Helpfulness": 0.8},
            )

            # Ensure logs are flushed
            comparison_exp.flush()

            # Test comparison summary
            comparison_response = await client.call_tool(
                "summarize_experiment", {"experiment_id": experiment.id, "comparison_experiment_id": comparison_exp.id}
            )

            assert "result" in comparison_response
            comparison_content = comparison_response["result"]["content"][0]["text"]
            comparison_data = json.loads(comparison_content)

            # Should have comparison data
            assert isinstance(comparison_data, dict)
            # Comparison summaries often have additional fields
            assert len(comparison_data) > 0

        asyncio.run(_test())

    def test_generate_permalink_tool(self):
        """Test the generate_permalink tool functionality."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            # Create test experiment
            experiment = braintrust.init("mcp-permalink-test", "test-experiment")

            client = MCPClient(access_token=self.org_api_key)

            # Test permalink generation by object_id
            response = await client.call_tool(
                "generate_permalink", {"object_type": "experiment", "object_id": experiment.id}
            )

            assert "result" in response
            content = response["result"]["content"]
            assert len(content) == 1
            assert content[0]["type"] == "text"

            permalink = content[0]["text"]
            assert isinstance(permalink, str)
            assert permalink.startswith("http")
            assert "/app/object?" in permalink
            assert f"object_type=experiment" in permalink
            assert f"object_id={experiment.id}" in permalink

            # Test permalink generation by name
            response_by_name = await client.call_tool(
                "generate_permalink",
                {"object_type": "experiment", "project_name": experiment.project.name, "object_name": experiment.name},
            )

            assert "result" in response_by_name
            permalink_by_name = response_by_name["result"]["content"][0]["text"]

            # Should generate the same permalink
            assert permalink == permalink_by_name

            # Test with row_id parameter
            response_with_row = await client.call_tool(
                "generate_permalink",
                {"object_type": "experiment", "object_id": experiment.id, "row_id": "test-row-id-123"},
            )

            assert "result" in response_with_row
            permalink_with_row = response_with_row["result"]["content"][0]["text"]
            assert "id=test-row-id-123" in permalink_with_row

        asyncio.run(_test())

    def test_error_handling(self):
        """Test error handling for invalid tool calls."""
        if not self.test_setup_successful:
            self.skipTest("Test infrastructure setup failed")

        async def _test():
            client = MCPClient(access_token=self.org_api_key)

            # Call nonexistent tool
            response = await client.call_tool("nonexistent_tool", {})

            # Should get a JSON-RPC error response
            assert "error" in response
            assert response["error"]["code"] is not None

            # Test error handling for btql_query with invalid syntax
            try:
                await client.call_tool(
                    "btql_query",
                    {
                        "explanation": "Testing error handling with invalid BTQL syntax",
                        "query": "invalid btql syntax here",
                        "object_type": "experiment",
                        "object_ids": ["non-existent-id"],
                    },
                )
            except Exception as e:
                # Should handle BTQL syntax errors gracefully
                assert "error" in str(e) or "HTTP" in str(e)

            # Test error handling for infer_schema with invalid object_ids
            try:
                await client.call_tool(
                    "infer_schema", {"object_type": "experiment", "object_ids": ["completely-invalid-id-123"]}
                )
            except Exception as e:
                # Should handle invalid object IDs
                assert "error" in str(e) or "HTTP" in str(e)

            # Test error handling for summarize_experiment with non-existent ID
            try:
                await client.call_tool("summarize_experiment", {"experiment_id": "non-existent-experiment-id-456"})
            except Exception as e:
                # Should handle non-existent experiments
                assert "error" in str(e) or "HTTP" in str(e)

        asyncio.run(_test())


class TestMCPStandalone(unittest.TestCase):
    """Standalone MCP tests that don't require full test infrastructure."""

    def test_server_connectivity(self):
        """Test basic server connectivity."""

        async def _test():
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    # Test OAuth discovery endpoint
                    response = await client.get("http://localhost:8000/.well-known/oauth-authorization-server")
                    if response.status_code == 200:
                        data = response.json()
                        assert "authorization_endpoint" in data
                        return True
                    else:
                        self.fail(f"OAuth discovery failed: {response.status_code}")
            except Exception as e:
                self.fail(f"Server connectivity error: {e}")

        asyncio.run(_test())

    def test_auth_required_standalone(self):
        """Test authentication requirement without full infrastructure."""

        async def _test():
            client = MCPClient()

            try:
                await client.list_tools()
                self.fail("Expected HTTPStatusError for unauthenticated request")
            except httpx.HTTPStatusError as e:
                self.assertEqual(e.response.status_code, 401)
                # Parse the SSE response if needed
                try:
                    response_data = parse_mcp_response(e.response)
                except:
                    response_data = e.response.json()
                self.assertEqual(response_data.get("error", {}).get("code"), -32001)
                self.assertIn("Authentication required", response_data.get("error", {}).get("message", ""))

        asyncio.run(_test())

    def test_with_env_token(self):
        """Test MCP with environment variable token."""

        async def _test():
            token = os.getenv("BRAINTRUST_API_KEY", "").strip()

            if not token:
                self.skipTest("No BRAINTRUST_API_KEY environment variable set")

            client = MCPClient(access_token=token)

            # Test listing tools
            response = await client.list_tools()
            self.assertIn("result", response)
            tools = response["result"].get("tools", [])
            self.assertGreater(len(tools), 0)

            # Verify expected tools exist
            tool_names = [tool["name"] for tool in tools]
            self.assertIn("resolve_object", tool_names)
            self.assertIn("search_docs", tool_names)
            self.assertIn("list_recent_objects", tool_names)
            self.assertIn("btql_query", tool_names)
            self.assertIn("infer_schema", tool_names)
            self.assertIn("summarize_experiment", tool_names)
            self.assertIn("generate_permalink", tool_names)

        asyncio.run(_test())


if __name__ == "__main__":
    unittest.main()
