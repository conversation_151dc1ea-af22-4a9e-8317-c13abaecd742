from datetime import datetime, timezone
from time import sleep
from unittest.mock import ANY

import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase
from tests.helpers.resources import (
    billing_period,
    clear_dataplane_resource_check_cache,
    clear_mocked_date,
    clear_resource_counts,
    clear_resource_limits,
    get_mocked_date,
    get_resource_counts,
    get_resource_limits,
    set_mocked_date,
    set_resource_limit,
    skip_if_calendar_months,
    skip_unless_calendar_months,
    udf_update_resource_counts,
)
from tests.helpers.telemetry import aggregate_events, clear_events, get_events, telemetry_event_aggregation

LOG_SIZE_TOLERANCE = 0.20  # 20% percent


class ResourceCountTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        clear_events(self.org_id)
        clear_resource_counts(self.org_id)
        clear_mocked_date(self.org_id)
        clear_dataplane_resource_check_cache(self.org_api_key)

    def test_nolimit(self):
        experiment = braintrust.init(project="_test_resource_count")
        for i in range(100):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 100)

    @skip_if_calendar_months
    def test_withlimit_num_private_experiment_row_actions_days(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (1, 10))

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            experiment = braintrust.init(project="_test_resource_count", org_name=self.org_name)

            for i in range(10):
                experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
            experiment.summarize()
            rows = get_object_json("experiment", experiment.id)
            self.assertEqual(len(rows), 10)

            for i in range(1):
                experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})

            with self.assertRaises(Exception) as cm:
                experiment.summarize()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)

    @skip_unless_calendar_months
    def test_withlimit_num_private_experiment_row_actions(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            experiment = braintrust.init(project="_test_resource_count", org_name=self.org_name)

            for i in range(10):
                experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
            experiment.summarize()
            rows = get_object_json("experiment", experiment.id)
            self.assertEqual(len(rows), 10)

            for i in range(1):
                experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})

            with self.assertRaises(Exception) as cm:
                experiment.summarize()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)

    @skip_unless_calendar_months
    def test_withlimit_num_production_log_row_actions(self):
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", (1, 10))

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            logger = braintrust.init_logger(project="jokes")
            for i in range(10):
                logger.log(input="tell me a joke", output="knock knock")
            logger.flush()
            rows = get_object_json("project_logs", logger.project.id)
            self.assertEqual(len(rows), 10)

            logger.log(input="test", output="fail")
            with self.assertRaises(Exception) as cm:
                logger.flush()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(rows), 10)

    @skip_unless_calendar_months
    def test_withlimit_num_dataset_row_actions(self):
        set_resource_limit(self.org_id, "num_dataset_row_actions_calendar_months", (1, 10))

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            dataset = braintrust.init_dataset(project="_test_resource_count", name="Test Dataset")
            for i in range(10):
                dataset.insert(input=i, output={"result": i + 1, "error": None})
            dataset.flush()
            rows = get_object_json("dataset", dataset.id)
            self.assertEqual(len(rows), 10)

            dataset.insert(input="test", output="fail")
            with self.assertRaises(Exception) as cm:
                dataset.flush()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("dataset", dataset.id)
        self.assertEqual(len(rows), 10)

    @skip_unless_calendar_months
    def test_withlimit_num_log_bytes_project_logs(self):
        clear_resource_counts(self.org_id)
        clear_dataplane_resource_check_cache(self.org_api_key)
        # 2000 bytes of payload data plus JSON overhead ~ 2800 bytes
        # we'll log 3 times with a limit of 8000 bytes.
        # the first 2 should work, the third should hit the limit
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 8000))

        with telemetry_event_aggregation(
            self.org_id, enabled=False
        ), braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            input = "a" * 1000
            output = "b" * 1000

            custom_bg_logger.sync_flush = True

            logger = braintrust.init_logger(project="_test_withlimit_num_log_bytes")

            logger.log(input=input, output=output)
            logger.log(input=input, output=output)

            logger.flush()

            # TODO: not 100% sure why this is required
            sleep(1)

            events = get_events(self.org_id)

            aggregated = aggregate_events(self.org_id, events)
            summary = aggregated[0]["properties"]

            counts = get_resource_counts(self.org_id)

            local_logs = counts[billing_period()]["num_log_bytes_calendar_months"]
            orb_logs = summary["log_bytes"]

            self.assertEqual(
                local_logs,
                orb_logs,
                "Orb reported log bytes should always be equal to internal log bytes",
            )

            rows = get_object_json("project_logs", logger.project.id)
            self.assertEqual(len(rows), 2)

            logger.log(input=input, output=output)
            with self.assertRaises(Exception) as cm:
                logger.flush()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(rows), 2)

    @skip_unless_calendar_months
    def test_withlimit_num_log_bytes_comments(self):
        clear_resource_counts(self.org_id)
        clear_dataplane_resource_check_cache(self.org_api_key)
        # 2000 bytes of payload data plus JSON overhead ~ 2800 bytes
        # we'll log 3 times with a limit of 8000 bytes.
        # the first 2 should work, the third should hit the limit
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 8000))

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            input = "a"
            output = "b"
            comment = "c" * 2000

            custom_bg_logger.sync_flush = True

            logger = braintrust.init_logger(project="_test_withlimit_num_log_bytes")

            request_id = logger.log(input=input, output=output)
            logger.log_feedback(id=request_id, scores={"foo": 0.5}, comment=comment)

            request_id = logger.log(input=input, output=output)
            logger.log_feedback(id=request_id, scores={"foo": 0.5}, comment=comment)

            logger.flush()

            rows = get_object_json("project_logs", logger.project.id)
            self.assertEqual(len(rows), 2)

            request_id = logger.log(input=input, output=output)
            logger.log_feedback(id=request_id, scores={"foo": 0.5}, comment=comment)

            with self.assertRaises(Exception) as cm:
                logger.flush()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(rows), 2)

    @skip_unless_calendar_months
    def test_withlimit_num_log_bytes_datasets(self):
        clear_resource_counts(self.org_id)
        clear_dataplane_resource_check_cache(self.org_api_key)
        # 1000 bytes per input and output plus JSON overhead = 2668 bytes
        # we'll log 3 times with a limit of 5400
        # the first 2 should work, the third should hit the limit
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 5400))

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            input = "a" * 1000
            output = "b" * 1000

            custom_bg_logger.sync_flush = True

            dataset = braintrust.init_dataset("num_log_bytes_dataset")

            rows = get_object_json("dataset", dataset.id)
            self.assertEqual(len(rows), 0)

            dataset.insert(input=input, output=output)
            dataset.insert(input=input, output=output)

            dataset.flush()

            rows = get_object_json("dataset", dataset.id)
            self.assertEqual(len(rows), 2)

            dataset.insert(input=input, output=output)

            with self.assertRaises(Exception) as cm:
                dataset.flush()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )
        rows = get_object_json("dataset", dataset.id)
        self.assertEqual(len(rows), 2)

    @skip_unless_calendar_months
    def test_windowing(self):
        set_mocked_date(self.org_id, "2023-01-01")

        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))
        set_resource_limit(self.org_id, "num_production_log_row_actions_calendar_months", (1, 8))
        set_resource_limit(self.org_id, "num_dataset_row_actions_calendar_months", (1, 6))

        experiment = braintrust.init(project="_test_resource_count", org_name=self.org_name)
        for i in range(10):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)

        logger = braintrust.init_logger(project="_twist", org_name=self.org_name)
        for i in range(8):
            logger.log(input="tell me a joke", output="knock knock")
        logger.flush()
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(rows), 8)

        dataset = braintrust.init_dataset(project="_test_resource_count", name="Test Dataset")
        for i in range(6):
            dataset.insert(input=i, output={"result": i + 1, "error": None})
        dataset.flush()
        rows = get_object_json("dataset", dataset.id)
        self.assertEqual(len(rows), 6)

        set_mocked_date(self.org_id, "2023-02-01")

        for i in range(10):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 20)

        for i in range(8):
            logger.log(input="tell me a joke", output="knock knock")
        logger.flush()
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(rows), 16)

        for i in range(6):
            dataset.insert(input=i, output={"result": i + 1, "error": None})
        dataset.flush()
        rows = get_object_json("dataset", dataset.id)
        self.assertEqual(len(rows), 12)

    @skip_unless_calendar_months
    def test_membership_check(self):
        experiment = braintrust.init(project="_test_resource_count")
        for i in range(100):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 100)

        # Updating the resource count should succeed.
        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    experiments={
                        experiment.id: dict(num_row_actions=10),
                    },
                ),
                num_shards=10,
            ),
        )

        # Remove the user from membership of the org.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    delete from members
                    where org_id = %s and user_id = %s
                    """,
                    [self.org_id, self.user_id],
                )

        # Updating the resource count should fail.
        with self.assertRaises(Exception):
            braintrust.app_conn().post_json(
                "api/insert-logs/resource-check",
                dict(
                    input=dict(
                        experiments={
                            experiment.id: dict(num_row_actions=10),
                        },
                    ),
                    num_shards=10,
                ),
            )

    @skip_unless_calendar_months
    def test_resource_check_logs_missing_num_row_bytes(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 200))

        experiment = braintrust.init(project="_test_resource_check_logs_missing_num_row_bytes")
        for i in range(100):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 100)

        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        # note missing num_row_bytes
                        self.org_id: dict(num_row_actions=10),
                    },
                ),
                num_shards=10,
            ),
        )

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                billing_period(): {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 100,
                    "num_production_log_row_actions_calendar_months": 10,
                }
            },
        )

    @skip_unless_calendar_months
    def test_resource_check_logs_with_extra_field(self):
        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        self.org_id: dict(
                            num_other_random_field=10,
                        ),
                    },
                ),
                num_shards=10,
            ),
        )

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                billing_period(): {
                    "num_log_bytes_calendar_months": 0,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

    @skip_unless_calendar_months
    def test_resource_check_logs_with_empty_logs(self):
        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        self.org_id: dict(),
                    },
                ),
                num_shards=10,
            ),
        )

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                billing_period(): {
                    "num_log_bytes_calendar_months": 0,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

    @skip_if_calendar_months
    def test_resource_check_extra_fields(self):
        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        self.org_id: dict(
                            num_other_random_field=10,
                        ),
                    },
                ),
                num_shards=10,
            ),
        )

        today = datetime.now(timezone.utc).date()

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "select * from resource_counts where org_id = %s",
                    (self.org_id,),
                )
                res = cursor.fetchall()
                self.assertEqual(
                    res,
                    [
                        (ANY, self.org_id, "num_production_log_row_actions", today, ANY, 0),
                        (ANY, self.org_id, "num_log_bytes", today, ANY, 0),
                    ],
                )

    @skip_unless_calendar_months
    def test_resource_check_extra_fields_calendar_months(self):
        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        self.org_id: dict(
                            num_other_random_field=10,
                        ),
                    },
                ),
                num_shards=10,
            ),
        )

        today = datetime.now(timezone.utc).replace(day=1).date()

        actual = get_resource_counts(self.org_id)
        expected = {
            today.strftime("%Y-%m-%d"): {
                "num_production_log_row_actions_calendar_months": 0,
                "num_log_bytes_calendar_months": 0,
            }
        }
        self.assertEqual(actual, expected)

    def test_resource_check_resilient_bad_input(self):
        res = braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        # note missing num_row_bytes
                        self.org_id: dict(),
                    },
                ),
                num_shards=-1,
            ),
        )

        self.assertEqual(
            res,
            {"is_unlimited": {}},
        )

    def test_resource_check_resilient_bad_input_with_orgs(self):
        res = braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        # note missing num_row_bytes
                        self.org_id: dict(),
                    },
                    additional_org_ids=[self.org_id],
                ),
                num_shards=-1,
            ),
        )

        self.assertEqual(
            res,
            {
                "is_unlimited": {
                    self.org_id: True,
                }
            },
        )

    @skip_unless_calendar_months
    def test_resource_check_logs_missing_num_row_actions(self):
        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(
                    logs={
                        # note missing num_row_actions
                        self.org_id: dict(num_row_bytes=10),
                    },
                ),
                num_shards=10,
            ),
        )

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                billing_period(): {
                    "num_log_bytes_calendar_months": 10,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

    @skip_unless_calendar_months
    def test_resource_check_unlimited_empty(self):
        self.assertEqual(get_resource_limits(self.org_id), {})

        experiment = braintrust.init(project="_test_resource_count")
        for i in range(100):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 100)

        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(),
                num_shards=10,
            ),
        )

        # the first few logs goes through but the other ones are not counted because the org is unlimited!
        counts = get_resource_counts(self.org_id)
        self.assertLess(counts[billing_period()]["num_private_experiment_row_actions_calendar_months"], 100)

    @skip_unless_calendar_months
    def test_resource_check_empty(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 200))

        experiment = braintrust.init(project="_test_resource_count")
        for i in range(100):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 100)

        braintrust.app_conn().post_json(
            "api/insert-logs/resource-check",
            dict(
                input=dict(),
                num_shards=10,
            ),
        )

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                billing_period(): {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 100,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

    def test_udf_invocation_with_empty_input(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select auth_id from users where id = %s", (self.user_id,))
                auth_id = (cursor.fetchone() or [""])[0]

        self.assertEqual(udf_update_resource_counts(auth_id, dict()), {"is_unlimited": {}})
        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: True}},
        )

    @skip_if_calendar_months
    def test_unlimited_days(self):
        clear_resource_limits(self.org_id)

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select auth_id from users where id = %s", (self.user_id,))
                auth_id = (cursor.fetchone() or [""])[0]

        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: True}},
        )

        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (1, 10))

        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: False}},
        )

        clear_resource_limits(self.org_id)

        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))

        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: True}},  # still true if we didn't set non calendar_months
        )

    @skip_unless_calendar_months
    def test_unlimited_calendar_months(self):
        clear_resource_limits(self.org_id)

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select auth_id from users where id = %s", (self.user_id,))
                auth_id = (cursor.fetchone() or [""])[0]

        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: True}},
        )

        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))

        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: False}},
        )

        clear_resource_limits(self.org_id)

        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (1, 10))

        self.assertEqual(
            udf_update_resource_counts(auth_id, {"additional_org_ids": [self.org_id]}),
            {"is_unlimited": {self.org_id: True}},  # still true if we didn't set calendar_months
        )

    @skip_unless_calendar_months
    def test_billable_month_window(self):
        set_mocked_date(self.org_id, "2025-02-01")
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))

        # Should be able to use all 10 actions on the 1st of the month
        experiment = braintrust.init(project="_test_billable_month", org_name=self.org_name)
        for i in range(10):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)

        # Try to add one more, should fail
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            experiment.log(input="extra", output="extra", expected="extra", scores={"correct": 1})
            with self.assertRaises(Exception) as cm:
                experiment.summarize()

        self.assertTrue(
            all(
                x in repr(cm.exception)
                for x in [
                    "You have reached your plan limits",
                    "Violations of resource constraint",
                    "Retrying",
                ]
            ),
            repr(cm.exception),
        )

        clear_dataplane_resource_check_cache(self.org_api_key)
        set_mocked_date(self.org_id, "2025-02-15")

        # Should still be at the limit, can't add more
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            experiment.log(input="extra", output="extra", expected="extra", scores={"correct": 1})
            with self.assertRaises(Exception) as cm:
                experiment.summarize()

        clear_dataplane_resource_check_cache(self.org_api_key)
        set_mocked_date(self.org_id, "2025-03-01")

        # Should be able to use 10 more actions in the new month
        experiment2 = braintrust.init(project="_test_billable_month_new", org_name=self.org_name)
        for i in range(10):
            experiment2.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment2.summarize()

        rows = get_object_json("experiment", experiment2.id)
        self.assertEqual(len(rows), 10)

    @skip_unless_calendar_months
    def test_billable_month_partial_month(self):
        set_mocked_date(self.org_id, "2025-02-20")
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))

        # Should be able to use all 10 actions even though we're late in the month
        experiment = braintrust.init(project="_test_billable_month_partial", org_name=self.org_name)
        for i in range(10):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)

        # Try to add one more, should fail
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            experiment.log(input="extra", output="extra", expected="extra", scores={"correct": 1})
            with self.assertRaises(Exception):
                experiment.summarize()

        clear_dataplane_resource_check_cache(self.org_api_key)
        set_mocked_date(self.org_id, "2025-03-01")

        # Should be able to use 10 more actions in the new month
        experiment2 = braintrust.init(project="_test_billable_month_new_partial", org_name=self.org_name)
        for i in range(10):
            experiment2.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment2.summarize()
        rows = get_object_json("experiment", experiment2.id)
        self.assertEqual(len(rows), 10)

    @skip_unless_calendar_months
    def test_billable_month_leap_year(self):
        set_mocked_date(self.org_id, "2024-02-28")
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 2))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 10))

        # Use 5 actions on Feb 28
        experiment = braintrust.init(project="_test_leap_year", org_name=self.org_name)
        for i in range(5):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)

        self.assertEqual(len(rows), 5)
        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2024-02-01": {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 5,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

        set_mocked_date(self.org_id, "2024-02-29")

        # Should be able to use 5 more actions on Feb 29
        for i in range(5):
            experiment.log(input=f"leap_{i}", output=f"leap_{i}", expected=f"leap_{i}", scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)
        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2024-02-01": {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 10,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

        # Try to add one more, should fail
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            experiment.log(input="extra", output="extra", expected="extra", scores={"correct": 1})
            with self.assertRaises(Exception):
                experiment.summarize()

        # note we still same resource counts due to failure
        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2024-02-01": {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 10,
                    "num_production_log_row_actions_calendar_months": 0,
                }
            },
        )

        clear_dataplane_resource_check_cache(self.org_api_key)
        set_mocked_date(self.org_id, "2024-03-01")
        self.assertEqual(get_mocked_date(self.org_id), "2024-03-01")

        # Should be able to use 10 more actions in March
        experiment2 = braintrust.init(project="_test_leap_year_march", org_name=self.org_name)
        for i in range(10):
            experiment2.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment2.summarize()
        rows = get_object_json("experiment", experiment2.id)

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2024-02-01": {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 10,
                    "num_production_log_row_actions_calendar_months": 0,
                },
                "2024-03-01": {
                    "num_log_bytes_calendar_months": ANY,
                    "num_private_experiment_row_actions_calendar_months": 10,
                    "num_production_log_row_actions_calendar_months": 0,
                },
            },
        )

        self.assertEqual(len(rows), 10)

    @skip_if_calendar_months
    def test_billable_period_days(self):
        set_mocked_date(self.org_id, "2025-04-01")
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 10))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 1000))

        experiment = braintrust.init(project="_test_billable_period_days", org_name=self.org_name)
        for i in range(5):
            experiment.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)

        self.assertEqual(len(rows), 5)
        self.assertEqual(get_resource_counts(self.org_id), {"2025-04-01": {"num_private_experiment_row_actions": 5}})

        set_mocked_date(self.org_id, "2025-04-03")

        # Should be able to use 5 more actions
        for i in range(5):
            experiment.log(input=f"leap_{i}", output=f"leap_{i}", expected=f"leap_{i}", scores={"correct": 1})
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(len(rows), 10)
        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2025-04-01": {"num_private_experiment_row_actions": 5},
                "2025-04-03": {"num_private_experiment_row_actions": 5},
            },
        )

        # Try to add one more, should fail
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            experiment.log(input="extra", output="extra", expected="extra", scores={"correct": 1})
            with self.assertRaises(Exception):
                experiment.summarize()

        # note we still same resource counts due to failure
        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2025-04-01": {"num_private_experiment_row_actions": 5},
                "2025-04-03": {"num_private_experiment_row_actions": 5},
            },
        )

        clear_dataplane_resource_check_cache(self.org_api_key)
        # note must be 7 days later than the last bucket!
        set_mocked_date(self.org_id, "2025-04-11")
        self.assertEqual(get_mocked_date(self.org_id), "2025-04-11")

        # Should be able to use 10 more actions in March
        experiment2 = braintrust.init(project="_test_billable_period_days", org_name=self.org_name)
        for i in range(10):
            experiment2.log(input=str(i), output=str(i), expected=str(i), scores={"correct": 1})
        experiment2.summarize()
        rows = get_object_json("experiment", experiment2.id)
        self.assertEqual(len(rows), 10)

        self.assertEqual(
            get_resource_counts(self.org_id),
            {
                "2025-04-01": {"num_private_experiment_row_actions": 5},
                "2025-04-03": {"num_private_experiment_row_actions": 5},
                "2025-04-11": {"num_private_experiment_row_actions": 10},
            },
        )

    def test_input_data_resilient_input_error(self):
        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "logs": {
                        self.org_id: {
                            "num_row_actions": 10,
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-udf-input-error": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.headers.get("x-bt-debug-resource-check-error"), "udf-input-error")
        self.assertEqual(res.json(), {"is_unlimited": {}})

    def test_input_data_resilient_input_error_with_orgs(self):
        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "logs": {
                        self.org_id: {
                            "num_row_actions": 10,
                        },
                    },
                    "additional_org_ids": [self.org_id],
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-udf-input-error": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.headers.get("x-bt-debug-resource-check-error"), "udf-input-error")
        self.assertEqual(
            res.json(),
            {
                "is_unlimited": {
                    self.org_id: True,
                }
            },
        )

    def test_input_data_resilient_random_input_error(self):
        experiment = braintrust.init(project="_test_resource_count")

        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 10,
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-udf-input-error": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.headers.get("x-bt-debug-resource-check-error"), "udf-input-error")
        self.assertEqual(res.json(), {"is_unlimited": {}})

    def test_output_data_resilient(self):
        experiment = braintrust.init(project="_test_resource_count")

        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 10,
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-udf-output-error": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.headers.get("x-bt-debug-resource-check-error"), "udf-output-error")
        self.assertEqual(res.json(), {"is_unlimited": {}})

    def test_output_data_resilient_with_orgs(self):
        experiment = braintrust.init(project="_test_resource_count")

        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 10,
                        },
                    },
                    "additional_org_ids": [self.org_id],
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-udf-output-error": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.headers.get("x-bt-debug-resource-check-error"), "udf-output-error")
        self.assertEqual(
            res.json(),
            {
                "is_unlimited": {
                    self.org_id: True,
                }
            },
        )

    def test_runtime_error_resilient(self):
        experiment = braintrust.init(project="_test_resource_count")

        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 10,
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-udf-runtime-error": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.headers.get("x-bt-debug-resource-check-error"), "udf-runtime-error")
        self.assertEqual(res.json(), {"is_unlimited": {}})

    @skip_if_calendar_months
    def test_resource_check_disable(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    insert into resources (org_id, num_private_experiment_row_actions)
                    values (%s, %s)
                    """,
                    [
                        self.org_id,
                        (1, 10),
                    ],
                )

        experiment = braintrust.init(project="_test_resource_count", org_name=self.org_name)

        # confirm backend would return error
        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 15,  # more than 10!
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-disable-resource-check": "",
            },
        )

        self.assertIn("Violations of resource constraint num_private_experiment_row_actions:", res.text)

        # confirm backend, if disabled, would not return error
        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 15,  # more than 10!
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-disable-resource-check": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.json(), {"is_unlimited": {}})

    @skip_unless_calendar_months
    def test_resource_check_disable_calendar_months(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    insert into resources (org_id, num_private_experiment_row_actions_calendar_months)
                    values (%s, %s)
                    """,
                    [
                        self.org_id,
                        (1, 10),
                    ],
                )

        experiment = braintrust.init(project="_test_resource_count", org_name=self.org_name)

        # confirm backend would return error
        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 15,  # more than 10!
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-disable-resource-check": "",
            },
        )

        self.assertIn("Violations of resource constraint num_private_experiment_row_actions", res.text)

        # confirm backend, if disabled, would not return error
        res = braintrust.app_conn().post(
            "/api/insert-logs/resource-check",
            json={
                "input": {
                    "experiments": {
                        experiment.id: {
                            "num_row_actions": 15,  # more than 10!
                        },
                    },
                },
                "num_shards": 10,
            },
            headers={
                "x-bt-debug-disable-resource-check": "1",
            },
        )
        res.raise_for_status()

        self.assertEqual(res.json(), {"is_unlimited": {}})
