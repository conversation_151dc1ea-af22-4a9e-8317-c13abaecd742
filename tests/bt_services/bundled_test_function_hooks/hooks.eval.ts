import { Eval } from "braintrust";
import { ExactMatch } from "autoevals";

Eval("hooks_test", {
  data: [
    {
      input: {
        obj: 1,
      },
      expected: "the answer",
      metadata: {
        coin: "heads",
      },
    },
  ],
  task: (input, hooks) => {
    if (hooks.metadata.coin === "heads") {
      return hooks.expected;
    }
    return "tails";
  },
  scores: [ExactMatch],
});
