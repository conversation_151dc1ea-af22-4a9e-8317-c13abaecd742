import json
import os
import re
import unittest
import uuid
from datetime import datetime
from itertools import chain, combinations
from pprint import pprint

import braintrust
import pyarrow
import pyarrow.parquet as pq
import requests
from jsonschema import validate as json_schema_validate
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url

NUM_EXPERIMENT_ROWS = 10
NUM_LOG_ROWS = 5

# We can no longer test query backends other than brainstore because we would
# prefer to test the row-ref-based inserts which make it impossible to use other
# query backends.
PARAMETERS = []
WITH_BRAINSTORE = PARAMETERS if BraintrustAppTestBase.skip_brainstore() else PARAMETERS + [("brainstore",)]


class DrilldownTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.experiment = braintrust.init(project="p")

        experiment_ids: list[str | None] = [None] * NUM_EXPERIMENT_ROWS
        log_ids = [None] * NUM_LOG_ROWS
        for n in range(2):
            for i in range(NUM_EXPERIMENT_ROWS):
                experiment_ids[i] = self.experiment.log(
                    id=experiment_ids[i],
                    input=f"{i}",
                    output=f"{i}",
                    scores={"foo": 0.5, "bar": i / NUM_EXPERIMENT_ROWS},
                    metrics={"prompt_tokens": 2} if n > 0 else {},
                    metadata={"user_id": "foo"},
                    tags=["foo" if i % 2 == 0 else "bar"],
                )
            self.experiment.flush()

            self.logger = braintrust.init_logger("p")
            for i in range(NUM_LOG_ROWS):
                log_ids[i] = self.logger.log(  # type: ignore
                    id=log_ids[i],
                    input=f"{i}",
                    output=f"{i}",
                    scores={"foo": 0.5, "bar": 0.2},
                    metrics={"completion_tokens": 3} if n > 0 else {},
                    metadata={"user_id": "foo"},
                    tags=["foo", "bar"],
                )
        self.logger.flush()

        self.project_id = self.logger.project.id

    def _get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def _run_drilldown(
        self,
        query,
        mode,
        allow_error=False,
        tz_offset=None,
        inference_depth=None,
        explain=False,
        wait_for_compaction_and_force_vacuum=False,
    ):
        if mode == "brainstore" and wait_for_compaction_and_force_vacuum:
            object_ids = [f"experiment:{self.experiment.id}", f"project_logs:{self.project_id}"]
            for object_id in object_ids:
                self.catchupBrainstoreWithCompaction(object_id)
            self.vacuumBrainstore(object_ids)

        ret = requests.post(
            f"{LOCAL_API_URL}/btql",
            json={
                "query": query,
                **self.make_mode_args(mode),
                **({"tz_offset": tz_offset} if tz_offset is not None else {}),
                **({"inference_depth": inference_depth} if inference_depth is not None else {}),
                **({"_debug_explain": True} if explain else {}),
            },
            headers=self._get_request_headers(),
        )
        if not allow_error:
            self.assertTrue(ret.ok, ret.text)

        response = ret.json()

        if ret.ok and not explain:
            data = response.get("data")
            schema = response.get("schema")
            json_schema_validate(data, schema)

            if mode == "brainstore":
                self.assertTrue(ret.headers.get("x-bt-internal-trace-id") is not None)
                self.assertIn("realtime_state", response)
                self.assertIn("freshness_state", response)

        return response

    @parameterized.expand(WITH_BRAINSTORE)
    def test_trivial_dataset(self, mode):
        dataset = braintrust.init_dataset(project="test_trivial_dataset")
        for i in range(5):
            dataset.insert(
                input=i,
                output=f"{i}",
            )
        dataset.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["dataset"]},
                "args": [{"op": "literal", "value": dataset.id}],
            },
            "select": [{"op": "star"}],
            "sort": [{"expr": {"btql": "input"}, "dir": "asc"}],
        }
        result = self._run_drilldown(query, mode, wait_for_compaction_and_force_vacuum=True)["data"]
        self.assertEqual(5, len(result))

        for i, row in enumerate(result):
            self.assertEqual(i, row["input"])
            self.assertEqual(f"{i}", row["expected"])

    @parameterized.expand(WITH_BRAINSTORE)
    def test_select_many(self, mode):
        experiment = braintrust.init(project="test_select_many")

        log = dict(
            input="foo",
            output="bar",
            scores={"foo": 0.5},
            metrics={"prompt_tokens": 2},
            metadata={"user_id": "foo"},
        )

        num_child_spans_per_root_span = 2

        for i in range(NUM_EXPERIMENT_ROWS):
            with experiment.start_span() as root_span:
                root_span.log(**log)
                for j in range(num_child_spans_per_root_span):
                    with root_span.start_span() as child_span:
                        child_span.log(**log)
        experiment.flush()

        logger = braintrust.init_logger("test_select_many")
        for i in range(NUM_LOG_ROWS):
            with logger.start_span() as root_span:
                root_span.log(**log)
                for j in range(num_child_spans_per_root_span):
                    with root_span.start_span() as child_span:
                        child_span.log(**log)
        logger.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": logger.project.id}],
            },
            "dimensions": [
                {"alias": "input", "expr": {"btql": "input"}},
                {"alias": "output", "expr": {"btql": "output"}},
                {"alias": "error", "expr": {"btql": "error"}},
                {"alias": "tags", "expr": {"btql": "tags"}},
                {"alias": "foo", "expr": {"btql": "scores.foo"}},
                {"alias": "metrics", "expr": {"btql": "metrics.prompt_tokens"}},
                {"alias": "context", "expr": {"btql": "context"}},
                {"alias": "span_id", "expr": {"btql": "span_id"}},
                {"alias": "span_parents", "expr": {"btql": "span_parents"}},
                {"alias": "root_span_id", "expr": {"btql": "root_span_id"}},
                {"alias": "is_root", "expr": {"btql": "is_root"}},
                {"alias": "span_attributes", "expr": {"btql": "span_attributes"}},
                {"alias": "origin", "expr": {"btql": "origin"}},
            ],
        }
        result = self._run_drilldown(query, mode, wait_for_compaction_and_force_vacuum=True)["data"]

        num_root_spans = NUM_EXPERIMENT_ROWS + NUM_LOG_ROWS
        num_child_spans = num_root_spans * num_child_spans_per_root_span

        self.assertEqual(num_root_spans, len([row for row in result if row["is_root"]]))
        self.assertEqual(num_child_spans, len([row for row in result if not row["is_root"]]))

    @parameterized.expand(WITH_BRAINSTORE)
    def test_token_counts(self, mode):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "dimensions": [{"alias": "project_id", "expr": {"btql": "project_id"}}],
            "measures": [
                {"alias": "prompt_tokens", "expr": {"btql": "sum(metrics.prompt_tokens)"}},
                {"alias": "completion_tokens", "expr": {"btql": "sum(metrics.completion_tokens)"}},
                {"alias": "tokens", "expr": {"btql": "sum(metrics.completion_tokens + metrics.prompt_tokens)"}},
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "num_logs", "expr": {"btql": "sum(is_root AND log_id='g')"}},
            ],
        }
        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(1, len(result))
        row = result[0]
        self.assertEqual(self.project_id, row["project_id"])
        self.assertEqual(NUM_EXPERIMENT_ROWS + NUM_LOG_ROWS, row["num_records"])
        self.assertEqual(NUM_LOG_ROWS, row["num_logs"])
        self.assertEqual(2 * NUM_EXPERIMENT_ROWS, row["prompt_tokens"])
        self.assertEqual(3 * NUM_LOG_ROWS, row["completion_tokens"])

    @parameterized.expand(WITH_BRAINSTORE)
    def test_schema_inference(self, mode):
        for inference_depth in [None, 0, 1]:
            query = {
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["project"]},
                    "args": [{"op": "literal", "value": self.project_id}],
                },
                "dimensions": [{"alias": "user_id", "expr": {"btql": "metadata.user_id"}}],
                "measures": [{"alias": "num_records", "expr": {"btql": "count(1)"}}],
            }
            result = self._run_drilldown(query, mode, inference_depth=inference_depth)
            self.assertEqual(1, len(result["data"]))
            self.assertEqual(
                result["schema"]["items"]["properties"]["user_id"].get("type"),
                "string" if inference_depth and inference_depth > 0 else None,
            )

    @parameterized.expand(WITH_BRAINSTORE)
    def test_invalid_project_id(self, mode):
        bogus = str(self.project_id)
        if bogus[0] == "f":
            bogus = "0" + bogus[1:]
        else:
            bogus = "f" + bogus[1:]

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": bogus}],
            },
            "dimensions": [],
            "measures": [{"alias": "num_records", "expr": {"btql": "count(1)"}}],
        }

        result = self._run_drilldown(query, mode, allow_error=True)
        self.assertEqual(result["Code"], "ForbiddenError", result)

    @parameterized.expand(WITH_BRAINSTORE)
    def test_empty_date_range(self, mode):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "dimensions": [{"alias": "project_id", "expr": {"btql": "project_id"}}],
            "measures": [
                {"alias": "prompt_tokens", "expr": {"btql": "sum(metrics.prompt_tokens)"}},
                {"alias": "completion_tokens", "expr": {"btql": "sum(metrics.completion_tokens)"}},
                {"alias": "tokens", "expr": {"btql": "sum(metrics.completion_tokens + metrics.prompt_tokens)"}},
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "num_logs", "expr": {"btql": "sum(is_root AND log_id='g')"}},
            ],
            "filter": {"btql": "created <= '2023-01-01'"},
        }

        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(0, len(result))

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "dimensions": [{"alias": "project_id", "expr": {"btql": "project_id"}}],
            "measures": [
                {"alias": "prompt_tokens", "expr": {"btql": "sum(metrics.prompt_tokens)"}},
                {"alias": "completion_tokens", "expr": {"btql": "sum(metrics.completion_tokens)"}},
                {"alias": "tokens", "expr": {"btql": "sum(metrics.completion_tokens + metrics.prompt_tokens)"}},
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "num_logs", "expr": {"btql": "sum(is_root AND log_id='g')"}},
            ],
            # Hopefully this test breaks one day :)
            "filter": {"btql": "created >= '2030-01-01'"},
        }

        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(0, len(result))

    @parameterized.expand(WITH_BRAINSTORE)
    def test_empty_query(self, mode):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "dimensions": [],
            "measures": [],
        }
        self.assertRaises(Exception, lambda: self._run_drilldown(query, mode))

    @parameterized.expand(WITH_BRAINSTORE)
    def test_empty_dims(self, mode):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "dimensions": [],
            "measures": [
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "num_logs", "expr": {"btql": "sum(is_root AND log_id='g')"}},
            ],
        }

        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(1, len(result))
        row = result[0]
        self.assertEqual(NUM_EXPERIMENT_ROWS + NUM_LOG_ROWS, row["num_records"])
        self.assertEqual(NUM_LOG_ROWS, row["num_logs"])

    @parameterized.expand(WITH_BRAINSTORE)
    def test_sorting(self, mode):
        # Sort by the input column
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "dimensions": [{"alias": "i", "expr": {"btql": "input"}}],
            "measures": [{"alias": "num_records", "expr": {"btql": "count(1)"}}],
            "sort": [{"expr": {"btql": "i"}, "dir": "asc"}],
        }

        results = self._run_drilldown(query, mode)["data"]
        results_input = [r["i"] for r in results]
        self.assertEqual(results_input, sorted(results_input), results_input)

        query["sort"][0]["dir"] = "desc"
        results = self._run_drilldown(query, mode)["data"]
        results_input = [r["i"] for r in results]
        self.assertEqual(results_input, sorted(results_input, reverse=True), results_input)

        # The first several values should have a higher count, so this result _should not_
        # be sorted by input.
        query["sort"] = [{"expr": {"btql": "num_records"}, "dir": "asc"}, {"expr": {"btql": "i"}, "dir": "asc"}]
        results = self._run_drilldown(query, mode)["data"]
        results_input = [r["i"] for r in results]

        max_records = max(NUM_EXPERIMENT_ROWS, NUM_LOG_ROWS)
        min_records = min(NUM_EXPERIMENT_ROWS, NUM_LOG_ROWS)
        mixed_order = [str(x) for x in list(range(max_records)[min_records:]) + list(range(min_records))]

        self.assertEqual(results_input, mixed_order)

    @parameterized.expand(WITH_BRAINSTORE)
    def test_time_filter(self, mode):
        # Filter by the input column
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project"]},
                "args": [{"op": "literal", "value": self.project_id}],
            },
            "measures": [{"alias": "num_records", "expr": {"btql": "count(1)"}}],
            "filter": {"btql": "created >= '2020-01-01T00:00:00Z'"},
        }

        results = self._run_drilldown(query, mode)["data"]
        self.assertEqual(results[0]["num_records"], NUM_EXPERIMENT_ROWS + NUM_LOG_ROWS)

    @parameterized.expand(WITH_BRAINSTORE)
    def test_pivot(self, mode):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": self.experiment.id}],
            },
            "unpivot": [{"alias": ["score", "value"], "expr": {"btql": "scores"}}],
            "pivot": [{"alias": "score", "expr": {"btql": "score"}}],
            "measures": [
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "avg", "expr": {"btql": "avg(value)"}},
            ],
        }

        response = self._run_drilldown(query, mode)
        results = round_floats(response["data"])
        self.assertEqual(
            results,
            [
                {
                    "num_records": 20,
                    "avg": 0.475,
                    "score": {"bar": {"num_records": 10, "avg": 0.45}, "foo": {"num_records": 10, "avg": 0.5}},
                }
            ],
        )
        self.assertEqual(
            set(response["schema"]["items"]["properties"]["score"]["propertyNames"]["enum"]),
            {"bar", "foo"},
        )

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": self.experiment.id}],
            },
            "unpivot": [
                {"alias": ["score", "value"], "expr": {"btql": "scores"}},
                {"alias": "tag", "expr": {"btql": "tags"}},
            ],
            "pivot": [{"alias": "tag", "expr": {"btql": "tag"}}, {"alias": "score", "expr": {"btql": "score"}}],
            "measures": [
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "avg", "expr": {"btql": "avg(value)"}},
            ],
        }

        response = self._run_drilldown(query, mode)
        results = round_floats(response["data"])
        self.assertEqual(
            set(response["schema"]["items"]["properties"]["tag"]["propertyNames"]["enum"]),
            {"bar", "foo"},
        )
        self.assertEqual(
            set(
                response["schema"]["items"]["properties"]["tag"]["additionalProperties"]["properties"]["score"][
                    "propertyNames"
                ]["enum"]
            ),
            {"bar", "foo"},
        )

        self.assertEqual(
            results,
            [
                {
                    "num_records": 20,
                    "avg": 0.475,
                    "tag": {
                        "bar": {
                            "score": {"bar": {"num_records": 5, "avg": 0.5}, "foo": {"num_records": 5, "avg": 0.5}},
                            "num_records": 10,
                            "avg": 0.5,
                        },
                        "foo": {
                            "score": {"bar": {"num_records": 5, "avg": 0.4}, "foo": {"num_records": 5, "avg": 0.5}},
                            "num_records": 10,
                            "avg": 0.45,
                        },
                    },
                }
            ],
        )

        if mode != "brainstore":
            # TODO: brainstore does not support unpivoting constants
            query = {
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["experiment"]},
                    "args": [{"op": "literal", "value": self.experiment.id}],
                },
                "unpivot": [
                    {"alias": ["score", "value"], "expr": {"btql": "scores"}},
                    {
                        "alias": "tag",
                        "expr": {"btql": "['1', 'asdf']"},
                    },  # Ideally we test [1, 'asdf'] but Clickhouse has trouble
                ],
                "pivot": [{"alias": "tag", "expr": {"btql": "tag"}}, {"alias": "score", "expr": {"btql": "score"}}],
                "measures": [
                    {"alias": "num_records", "expr": {"btql": "count(1)"}},
                    {"alias": "avg", "expr": {"btql": "avg(value)"}},
                ],
            }

            results = round_floats(self._run_drilldown(query, mode)["data"])
            self.assertEqual(
                results,
                [
                    {
                        "num_records": 40,
                        "avg": 0.475,
                        "tag": {
                            "1": {
                                "score": {
                                    "bar": {"num_records": 10, "avg": 0.45},
                                    "foo": {"num_records": 10, "avg": 0.5},
                                },
                                "num_records": 20,
                                "avg": 0.475,
                            },
                            "asdf": {
                                "score": {
                                    "bar": {"num_records": 10, "avg": 0.45},
                                    "foo": {"num_records": 10, "avg": 0.5},
                                },
                                "num_records": 20,
                                "avg": 0.475,
                            },
                        },
                    }
                ],
            )

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": self.experiment.id}],
            },
            "unpivot": [
                {"alias": ["metadata_key", "value"], "expr": {"btql": "metadata"}},
            ],
            "select": [{"alias": "metadata_key", "expr": {"btql": "metadata_key"}}],
        }

        results = round_floats(self._run_drilldown(query, mode)["data"])
        for row in results:  # type: ignore
            self.assertEqual(
                row,
                {
                    "metadata_key": "user_id",
                },
            )

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": self.experiment.id}],
            },
            "unpivot": [
                {"alias": ["span_attributes_key", "value"], "expr": {"btql": "span_attributes"}},
            ],
            "select": [{"alias": "span_attributes_key", "expr": {"btql": "span_attributes_key"}}],
        }

        results = round_floats(self._run_drilldown(query, mode)["data"])
        for row in results:  # type: ignore
            span_attributes_key = row.get("span_attributes_key")
            self.assertIn(span_attributes_key, ["name", "exec_counter", "type"])
            self.assertEqual(
                row,
                {
                    "span_attributes_key": span_attributes_key,
                },
            )

    # NOTE(austin): This test isn't enabled for Brainstore yet because
    # Brainstore currently only supports unpivoting on a single field.
    @parameterized.expand(PARAMETERS, skip_on_empty=True)
    def test_extra_scores(self, mode):
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": self.experiment.id}],
            },
            "unpivot": [
                {
                    "alias": ["score", "value"],
                    "expr": {
                        "btql": "INSERT(scores, 'foobar', (COALESCE(scores.foo, 0)+COALESCE(scores.bar, 0)+COALESCE(NULL, 0))/(1+1+COALESCE(NULL, 0)))"
                    },
                }
            ],
            "pivot": [{"alias": "score", "expr": {"btql": "score"}}],
            "measures": [
                {"alias": "num_records", "expr": {"btql": "count(1)"}},
                {"alias": "avg", "expr": {"btql": "avg(value)"}},
            ],
        }

        results = round_floats(self._run_drilldown(query, mode)["data"])
        self.assertEqual(
            results,
            [
                {
                    "num_records": 30,
                    "avg": 0.475,
                    "score": {
                        "bar": {"num_records": 10, "avg": 0.45},
                        "foo": {"num_records": 10, "avg": 0.5},
                        "foobar": {"avg": 0.475, "num_records": 10},
                    },
                }
            ],
        )

    def test_comparison(self):
        # Generate a bunch of queries and test postgres vs. brainstore. This test is a bit
        # challenging to debug, if you notice errors. The best way to do it is to comment out
        # a bunch of the dimensions, measure fields, and score fields.
        dims = [
            {
                "alias": "bucket",
                "expr": {"btql": "hour(created)"},
            },
            {
                "alias": "project",
                "expr": {"btql": "project_id"},
            },
            {
                "alias": "user_id",
                "expr": {"btql": "metadata.user_id"},
            },
        ]

        measures = [{"alias": "count", "expr": {"btql": "count(1)"}}]
        for arg in [
            {"root": "metrics", "name": "prompt_tokens"},
            {"root": "metrics", "name": "completion_tokens"},
            {"root": "metrics", "name": "tokens"},
            {"root": "metrics", "name": "start"},
            {"root": "metrics", "name": "end"},
            {"root": "metrics", "name": "time_to_first_token"},
            {"root": "metrics", "name": "duration"},
            {"root": "scores", "name": "foo"},
            {"root": "scores", "name": "bar"},
        ]:
            measures.extend(
                [
                    {"alias": f"sum_{arg['name']}", "expr": {"btql": f"sum({arg['root']}.{arg['name']})"}},
                    {"alias": f"sum_{arg['name']}", "expr": {"btql": f"sum({arg['root']}.{arg['name']})"}},
                    {"alias": f"sum_{arg['name']}", "expr": {"btql": f"sum({arg['root']}.{arg['name']})"}},
                    {"alias": f"sum_{arg['name']}", "expr": {"btql": f"sum({arg['root']}.{arg['name']})"}},
                    {"alias": f"p50_{arg['name']}", "expr": {"btql": f"percentile({arg['root']}.{arg['name']}, 0.5)"}},
                    {"alias": f"p90_{arg['name']}", "expr": {"btql": f"percentile({arg['root']}.{arg['name']}, 0.9)"}},
                ]
            )

        for m in [[], measures]:
            for dim_combo in powerset(dims):
                query = {
                    "from": {
                        "op": "function",
                        "name": {"op": "ident", "name": ["project"]},
                        "args": [{"op": "literal", "value": self.project_id}],
                    },
                    "dimensions": dim_combo,
                    "measures": m,
                }

                if len(dim_combo) == 0 and len(m) == 0:
                    self.assertRaises(Exception, lambda: self._run_drilldown(query, "postgres"))
                    continue

                # pprint(query)

                result_pg = self._run_drilldown(query, "postgres")["data"]
                # print("PG", result_pg)

                if not BraintrustAppTestBase.skip_brainstore():
                    result_bs = self._run_drilldown(query, "brainstore")["data"]
                    # print("BS", result_bs)
                    self.assertEqual(len(result_pg), len(result_bs))

    @parameterized.expand(WITH_BRAINSTORE)
    def test_concat(self, mode):
        logger = braintrust.init_logger("lower_test")
        log_id = logger.log(
            metadata={
                "lower": "FOO BAR",
                "upper": "foo bar",
            },
        )
        logger.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
            },
            "select": [
                {
                    "alias": "all",
                    "expr": {"btql": "concat(metadata.lower, '.', metadata.unknown, '.', metadata.upper)"},
                }
            ],
        }
        result = self._run_drilldown(query, mode)["data"]

        # nulls are ignored
        self.assertEqual(result, [{"all": "FOO BAR..foo bar"}])

    @parameterized.expand(WITH_BRAINSTORE)
    def test_lower_upper(self, mode):
        logger = braintrust.init_logger("lower_test")
        log_id = logger.log(
            metadata={
                "lower": "FOO BAR",
                "upper": "foo bar",
            },
        )
        logger.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
            },
            "select": [
                {"alias": "lower", "expr": {"btql": "lower(metadata.lower)"}},
                {"alias": "upper", "expr": {"btql": "upper(metadata.upper)"}},
            ],
        }
        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(result, [{"lower": "foo bar", "upper": "FOO BAR"}])

    @parameterized.expand(WITH_BRAINSTORE)
    def test_array_length(self, mode):
        logger = braintrust.init_logger("array_length_test")
        log_id = logger.log(
            metadata={
                "one": ["A"],
                "zero": [],
                "empty_explicit": None,
            },
        )
        logger.flush()

        project_id = logger.project.id
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
            },
            "select": [
                {"alias": "one", "expr": {"btql": "len(metadata.one)"}},
                {"alias": "zero", "expr": {"btql": "len(metadata.zero)"}},
                {"alias": "empty", "expr": {"btql": "len(metadata.empty)"}},
                {"alias": "empty_explicit", "expr": {"btql": "len(metadata.empty_explicit)"}},
            ],
        }
        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(len(result), 1)
        row = result[0]

        # NOTE(austin): `[]` and `null` are semantically equivalent in Brainstore,
        # and we expect `len` to return 0 for both.
        length_of_null = 0 if mode == "brainstore" else None

        self.assertEqual(row["one"], 1)
        self.assertEqual(row["zero"], 0)
        self.assertEqual(row["empty"], length_of_null)
        self.assertEqual(row["empty_explicit"], length_of_null)

    @parameterized.expand(
        WITH_BRAINSTORE
        + [
            ("postgres",),
            ("duckdb",),
        ]
    )
    def test_sha256(self, mode):
        logger = braintrust.init_logger("sha256_test")
        log_id = logger.log(
            metadata={
                "null_value": None,
                "null_string": "null",
                "number": 42,
                "boolean": True,
                "array": ["test"],
                "empty_object": {},
            },
        )
        logger.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
            },
            "select": [
                {"alias": "null_literal", "expr": {"btql": "sha256(null)"}},
                {"alias": "null_value", "expr": {"btql": "sha256(metadata.null_value)"}},
                {"alias": "null_string", "expr": {"btql": "sha256(metadata.null_string)"}},
                {"alias": "number", "expr": {"btql": "sha256(metadata.number)"}},
                {"alias": "boolean", "expr": {"btql": "sha256(metadata.boolean)"}},
                {"alias": "array", "expr": {"btql": "sha256(metadata.array)"}},
                {"alias": "empty_object", "expr": {"btql": "sha256(metadata.empty_object)"}},
            ],
        }
        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(
            result,
            [
                {
                    "null_literal": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b",
                    "null_value": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b",
                    "null_string": "f072cbec3bf8841871d4284230c5e983dc211a56837aed862487148f947d1a1f",
                    "number": "73475cb40a568e8da8a045ced110137e159f890ac4da883b6b17dc651b3a8049",
                    "boolean": "b5bea41b6c623f7c09f1bf24dcae58ebab3c0cdd90ad966bc43a45b44867e12b",
                    "array": "ecfd160805b1b0481fd0793c745be3b45d2054582de1c4df5d9b8fa4d78e7fbc",
                    "empty_object": "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a",
                }
            ],
        )

    @parameterized.expand(
        [
            # This is only implemented for the UI at the moment
            ("duckdb",),
        ]
    )
    def test_json_extract(self, mode):
        self.skipTest("duckdb query backend doesn't work")
        logger = braintrust.init_logger("json_extract_test")

        # Test different JSON string formats
        log_id = logger.log(
            metadata={
                "object": {"a": 1, "b": "hello"},
                "simple": '{"a": 1, "b": "hello"}',
                "multiline": """
                {
                    "a": 1,
                    "b": "hello"
                }
                """,
                "backticks": '```\n{"a": 1, "b": "hello"}\n```',
                "backticks_json": '```json\n{"a": 1, "b": "hello"}\n```',
                "with_text": 'here\'s some json:\n```\n{"a": 1, "b": "hello"}\n```',
                "empty_null": None,
                "empty_string": "",
                "invalid_json": "not a valid json",
            }
        )
        logger.flush()

        project_id = logger.project.id
        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
            },
            "select": [
                {"alias": "object_a", "expr": {"btql": "json_extract(metadata.object, 'a')"}},
                {"alias": "object_b", "expr": {"btql": "json_extract(metadata.object, 'b')"}},
                {"alias": "simple_a", "expr": {"btql": "json_extract(metadata.simple, 'a')"}},
                {"alias": "simple_b", "expr": {"btql": "json_extract(metadata.simple, 'b')"}},
                {"alias": "multiline_a", "expr": {"btql": "json_extract(metadata.multiline, 'a')"}},
                {"alias": "multiline_b", "expr": {"btql": "json_extract(metadata.multiline, 'b')"}},
                {"alias": "backticks_a", "expr": {"btql": "json_extract(metadata.backticks, 'a')"}},
                {"alias": "backticks_b", "expr": {"btql": "json_extract(metadata.backticks, 'b')"}},
                {"alias": "backticks_json_a", "expr": {"btql": "json_extract(metadata.backticks_json, 'a')"}},
                {"alias": "backticks_json_b", "expr": {"btql": "json_extract(metadata.backticks_json, 'b')"}},
                {"alias": "with_text_a", "expr": {"btql": "json_extract(metadata.with_text, 'a')"}},
                {"alias": "with_text_b", "expr": {"btql": "json_extract(metadata.with_text, 'b')"}},
                {"alias": "empty_null", "expr": {"btql": "json_extract(metadata.empty_null, 'a')"}},
                {"alias": "empty_string", "expr": {"btql": "json_extract(metadata.empty_string, 'a')"}},
                {"alias": "invalid_json", "expr": {"btql": "json_extract(metadata.invalid_json, 'a')"}},
            ],
        }
        result = self._run_drilldown(query, mode)["data"]
        self.assertEqual(len(result), 1)
        row = result[0]

        # Verify all formats extract correctly
        for prefix in ["object", "simple", "multiline", "backticks", "backticks_json", "with_text"]:
            self.assertEqual(row[f"{prefix}_a"], 1)
            self.assertEqual(row[f"{prefix}_b"], "hello")

        for prefix in ["empty_null", "empty_string", "invalid_json"]:
            self.assertEqual(row[prefix], None)

    @parameterized.expand(WITH_BRAINSTORE)
    def test_metadata_map(self, mode):
        logger = braintrust.init_logger("drilldown_test")
        project_id = logger.project.id
        insert_url = make_v1_url("project_logs", project_id, "insert")
        headers = dict(Authorization=f"Bearer {self.org_api_key}")

        event = dict(
            id=str(uuid.uuid4()),
            metadata={
                "user_id": "foo",
                "foo": ["bar", "baz"],
                "bool_true": True,
                "bool_false": False,
                "num": 123,
                "nested": {"a": "A", "b": ["B", "C"], "empty": None},
                "list": [
                    {"key": ["a", "b"], "bool": True, "num": 123},
                    {"key": ["c"], "bool": False, "num": 456},
                    {},
                ],
                "empty_explicit": None,
            },
        )

        resp = requests.post(insert_url, json={"events": [event]}, headers=headers)
        self.assertTrue(resp.ok, resp.text)

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": project_id}],
            },
            "select": [
                {"alias": "id", "expr": {"btql": "id"}},
                {"alias": "user_id", "expr": {"btql": "metadata.user_id"}},
                {"alias": "foo", "expr": {"btql": "metadata.foo"}},
                {"alias": "bool_true", "expr": {"btql": "metadata.bool_true"}},
                {"alias": "bool_false", "expr": {"btql": "metadata.bool_false"}},
                {"alias": "num", "expr": {"btql": "metadata.num"}},
                {"alias": "nested", "expr": {"btql": "metadata.nested"}},
                {"alias": "a", "expr": {"btql": "metadata.nested.a"}},
                {"alias": "b", "expr": {"btql": "metadata.nested.b"}},
                {"alias": "list", "expr": {"btql": "metadata.list"}},
                {"alias": "list_0", "expr": {"btql": "metadata.list[0]"}},
                {"alias": "list_0_key_1", "expr": {"btql": "metadata.list[0].key[1]"}},
                {"alias": "list_1_num", "expr": {"btql": "metadata.list[1].num"}},
                {"alias": "list_2", "expr": {"btql": "metadata.list[2]"}},
                {"alias": "empty_explicit", "expr": {"btql": "metadata.empty_explicit"}},
                {"alias": "empty_nester", "expr": {"btql": "metadata.nested.empty"}},
            ],
        }
        resp = requests.post(
            f"{LOCAL_API_URL}/btql",
            json={
                "query": query,
                "fmt": "json",
                **self.make_mode_args(mode),
            },
            headers=headers,
        )
        assert resp.ok, resp.text

        self.assertEqual(
            resp.json()["data"],
            [
                {
                    "id": event["id"],
                    "user_id": "foo",
                    "foo": ["bar", "baz"],
                    "bool_true": True,
                    "bool_false": False,
                    "num": 123,
                    "nested": {"a": "A", "b": ["B", "C"], "empty": None},
                    "a": "A",
                    "b": ["B", "C"],
                    "list": [
                        {"key": ["a", "b"], "bool": True, "num": 123},
                        {"key": ["c"], "bool": False, "num": 456},
                        {},
                    ],
                    "list_0": {"key": ["a", "b"], "bool": True, "num": 123},
                    "list_0_key_1": "b",
                    "list_1_num": 456,
                    "list_2": {},
                    "empty_explicit": None,
                    "empty_nester": None,
                }
            ],
        )

    @parameterized.expand(WITH_BRAINSTORE)
    def test_drilldown_queries(self, mode):
        logger = braintrust.init_logger("drilldown_test")
        project_id = logger.project.id
        insert_url = make_v1_url("project_logs", project_id, "insert")
        headers = dict(Authorization=f"Bearer {self.org_api_key}")

        # Create a few logs
        num_items = 3
        ids = [str(uuid.uuid4()) for _ in range(num_items)]
        events = [
            dict(
                id=ids[i],
                input=i,
                output=f"{i}",
                expected=["foo", None],
                scores={
                    "foo": 0.5 if i == 0 else None,
                    "bar": 0.5 if i == 1 else None,
                    "dot.syntax": 0,
                },
                metadata={
                    "user_id": "foo",
                    "foo": ["bar", "baz"],
                    "bool": True,
                    "num": 123,
                    "nested": {"a": "A", "b": ["B", "C"]},
                    "list": [
                        {"key": ["a", "b"], "bool": True, "num": 123},
                        {"key": ["c"], "bool": False, "num": 456},
                        {},
                    ],
                    "empty_explicit": None,
                    "empty_nester": {"empty": None},
                },
                metrics={"start": 1, "end": i, "completion_tokens": 3},
                tags=["foo", "bar"] if i == 0 else ["bar"] if i == 1 else [],
                error="Noooo" if i == 0 else None,
                span_attributes={"purpose": "foo"} if i == 0 else None,
            )
            for i in range(num_items)
        ]

        resp = requests.post(insert_url, json={"events": events}, headers=headers)
        self.assertTrue(resp.ok, resp.text)

        FILTERS = [
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[-2].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[-1].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[0].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[1].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "metadata.list[-2].num"},
                    "right": {"op": "literal", "value": 456},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "metadata.list[-1].num"},
                    "right": {"op": "literal", "value": 456},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "metadata.list[0].num"},
                    "right": {"op": "literal", "value": 456},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "metadata.list[1].num"},
                    "right": {"op": "literal", "value": 456},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.list[-2].key"},
                    "needle": {"op": "literal", "value": ["c"]},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.list[-1].key"},
                    "needle": {"op": "literal", "value": ["c"]},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.list[0].key"},
                    "needle": {"op": "literal", "value": ["c"]},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.list[1].key"},
                    "needle": {"op": "literal", "value": ["c"]},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.list[2].key"},
                    "needle": {"op": "literal", "value": ["c"]},
                },
                [],
                "postgres",
            ),
            ({"op": "eq", "left": {"btql": "id"}, "right": {"op": "literal", "value": ids[0]}}, [0], "postgres"),
            ({"op": "eq", "left": {"btql": "id"}, "right": {"op": "literal", "value": ids[1]}}, [1], "postgres"),
            ({"op": "eq", "left": {"btql": "id"}, "right": {"op": "literal", "value": ids[2]}}, [2], "postgres"),
            ({"op": "gt", "left": {"btql": "input"}, "right": {"op": "literal", "value": 1}}, [2], "postgres"),
            ({"op": "eq", "left": {"btql": "output"}, "right": {"op": "literal", "value": "1"}}, [1], "postgres"),
            # Filter on span attributes
            (
                {"op": "eq", "left": {"btql": "span_attributes.purpose"}, "right": {"op": "literal", "value": "foo"}},
                [0],
                "postgres",
            ),
            (
                {"op": "ne", "left": {"btql": "span_attributes.purpose"}, "right": {"op": "literal", "value": "foo"}},
                [],  # Matches nothing because of nullability
                "postgres",
            ),
            (
                {
                    "op": "ne",
                    "left": {"btql": "COALESCE(span_attributes.purpose, 'default')"},
                    "right": {"op": "literal", "value": "foo"},
                },
                [1, 2],
                "postgres",
            ),
            # This test makes sure that integers are cast to numbers, not the other way around.
            ({"op": "eq", "left": {"btql": "scores.foo"}, "right": {"op": "literal", "value": 1}}, [], "postgres"),
            (
                {"op": "eq", "left": {"btql": "scores.foo"}, "right": {"op": "literal", "value": 0.5}},
                [0],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "scores.foo"}, "right": {"btql": "50%"}},
                [0],
                "postgres",
            ),
            ({"op": "lt", "left": {"btql": "scores.foo"}, "right": {"op": "literal", "value": 0.5}}, [], "postgres"),
            (
                {"op": "ge", "left": {"btql": "scores.foo"}, "right": {"op": "literal", "value": 0.5}},
                [0],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": 'scores."dot.syntax"'}, "right": {"op": "literal", "value": 0}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "ge", "left": {"btql": "created"}, "right": {"op": "literal", "value": "2023-01-01"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "tags"}, "needle": {"op": "literal", "value": ["foo"]}},
                [0],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {
                        "op": "includes",
                        "haystack": {"btql": "tags"},
                        "needle": {"op": "literal", "value": ["foo"]},
                    },
                },
                [1, 2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "tags"}, "needle": {"op": "literal", "value": ["bar"]}},
                [0, 1],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {
                        "op": "includes",
                        "haystack": {"btql": "tags"},
                        "needle": {"op": "literal", "value": ["bar"]},
                    },
                },
                [2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "tags"}, "needle": {"op": "literal", "value": "foo"}},
                [0],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {
                        "op": "includes",
                        "haystack": {"btql": "tags"},
                        "needle": {"op": "literal", "value": "foo"},
                    },
                },
                [1, 2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "id"}, "needle": {"op": "literal", "value": ids}},
                [],
                "postgres",
            ),
            (
                {"op": "includes", "needle": {"btql": "id"}, "haystack": {"op": "literal", "value": ids}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "ge", "left": {"btql": "created"}, "right": {"btql": "now() - INTERVAL 1 DAY"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "ge", "left": {"btql": "created"}, "right": {"btql": "now() + INTERVAL 1 DAY"}},
                [],
                "postgres",
            ),
            (
                {"op": "ge", "left": {"btql": "created"}, "right": {"btql": "current_date - INTERVAL 1 DAY"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "ge", "left": {"btql": "created"}, "right": {"btql": "current_date + INTERVAL 1 DAY"}},
                [],
                "postgres",
            ),
            (
                {"op": "ge", "left": {"btql": "duration"}, "right": {"btql": "1"}},
                [2],
                "postgres",
            ),
            (
                {"op": "literal", "value": True},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "like", "left": {"btql": "metadata"}, "right": {"op": "literal", "value": "%f%"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "like", "left": {"btql": "metadata"}, "right": {"op": "literal", "value": "%F%"}},
                [],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {"op": "like", "left": {"btql": "metadata"}, "right": {"op": "literal", "value": "%f%"}},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {"op": "like", "left": {"btql": "metadata"}, "right": {"op": "literal", "value": "%F%"}},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "metadata"}, "needle": {"btql": "['poo']"}},
                [],
                "postgres",
            ),
            (
                {"op": "not", "expr": {"btql": "metadata ILIKE 'poo'"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.foo"}, "right": {"op": "literal", "value": ["baz", "bar"]}},
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.foo"}, "right": {"op": "literal", "value": ["bar", "baz"]}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.bool"}, "right": {"op": "literal", "value": False}},
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.bool"}, "right": {"op": "literal", "value": True}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.num"}, "right": {"op": "literal", "value": 456}},
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.num"}, "right": {"op": "literal", "value": 123}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.nested.a"}, "right": {"op": "literal", "value": "Z"}},
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.nested.a"}, "right": {"op": "literal", "value": "A"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.nested.b"}, "right": {"btql": "['B']"}},
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "metadata.nested.b"}, "right": {"btql": "['B', 'C']"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "metadata.nested.b"}, "needle": {"btql": "['Y']"}},
                [],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "metadata.nested.b"}, "needle": {"btql": "['C']"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {"op": "includes", "haystack": {"btql": "metadata.nested.b"}, "needle": {"btql": "['Y']"}},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "not",
                    "expr": {"op": "includes", "haystack": {"btql": "metadata.nested.b"}, "needle": {"btql": "['C']"}},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.foo"},
                    "needle": {"op": "literal", "value": ["bar", "baz"]},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.foo"},
                    "needle": {"op": "literal", "value": ["baz", "bar"]},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.foo"},
                    "needle": {"op": "literal", "value": ["bar"]},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "includes",
                    "haystack": {"btql": "metadata.foo"},
                    "needle": {"op": "literal", "value": ["boo"]},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.user_id"},
                    "right": {"op": "literal", "value": "%f%"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "metadata.user_id"},
                    "right": {"op": "literal", "value": "foo"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "includes", "haystack": {"btql": "metadata.foo"}, "needle": {"btql": "['bar']"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "not", "expr": {"btql": "metadata.user_id = 'bar'"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "and",
                    "left": {"btql": "tags includes 'bar'"},
                    "right": {"btql": "metadata.foo = ['bar', 'baz']"},
                },
                [0, 1],
                "postgres",
            ),
            (
                {
                    "op": "or",
                    "left": {"btql": "tags includes 'cat'"},
                    "right": {"btql": "coalesce(metadata.empty, 'foo') = 'foo'"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnull", "expr": {"btql": "metadata.empty"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnull", "expr": {"btql": "metadata.empty_explicit"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnull", "expr": {"btql": "metadata.empty_nester.empty"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnotnull", "expr": {"btql": "error"}},
                [0],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[-2].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[-1].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[0].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[1].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "ilike",
                    "left": {"btql": "metadata.list[2].key"},
                    "right": {"op": "literal", "value": "%C%"},
                },
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "len(expected)"}, "right": {"op": "literal", "value": 0}},
                [],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "len(metadata.foo)"}, "right": {"op": "literal", "value": 2}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "coalesce(len(metadata.empty), 0)"},
                    "right": {"op": "literal", "value": 0},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnull", "expr": {"btql": "metadata.user_id"}},
                [],
                "postgres",
            ),
            (
                {"op": "isnotnull", "expr": {"btql": "metadata.user_id"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnull", "expr": {"btql": "metadata.field_that_does_not_exist"}},
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "isnotnull", "expr": {"btql": "metadata.field_that_does_not_exist"}},
                [],
                "postgres",
            ),
            (
                {
                    "op": "eq",
                    "left": {"btql": "coalesce(len(metadata.empty_explicit), 0)"},
                    "right": {"op": "literal", "value": 0},
                },
                [0, 1, 2],
                "postgres",
            ),
            (
                {"op": "eq", "left": {"btql": "len(metadata.nested.b)"}, "right": {"op": "literal", "value": 2}},
                [0, 1, 2],
                "postgres",
            ),
            # This case is broken. Fixing it likely requires making the haystack search look for both a scalar and an array.
            # (
            #     {"op": "includes", "haystack": {"btql": "expected"}, "needle": {"op": "literal", "value": None}},
            #     [0, 1, 2],
            #     "postgres",
            # ),
        ]

        for f in FILTERS:
            filter, expected, expected_auto_dialect = f
            print(filter)
            resp = requests.post(
                f"{LOCAL_API_URL}/btql",
                json={
                    "query": {
                        "from": {
                            "op": "function",
                            "name": {"op": "ident", "name": ["project_logs"]},
                            "args": [{"op": "literal", "value": project_id}],
                        },
                        "filter": filter,
                        "select": [
                            {"alias": "input", "expr": {"btql": "input"}},
                            {"alias": "id", "expr": {"btql": "id"}},
                        ],
                    },
                    "fmt": "json",
                    **self.make_mode_args(mode),
                },
                headers=headers,
            )

            assert resp.ok, resp.text

            print(resp.json())
            raw_json = resp.json()

            ids = [r["input"] for r in raw_json["data"]]
            row_ids = [r["id"] for r in raw_json["data"]]
            self.assertEqual(set(expected), set(ids), f"(mode={mode}) {filter} {expected} {ids}")

            if mode in ("postgres", "auto"):
                dialect = raw_json["dialect"]
                expected_dialect = expected_auto_dialect if mode == "auto" else mode
                self.assertEqual(expected_dialect, dialect, f"(mode={mode}) {filter} {expected_dialect}")

            # Now run the query against the audit log (filtering only for the
            # requested row IDs) and ensure that the same number of rows are
            # returned.
            if mode != "duckdb":
                # Build up a chain of OR expressions for each row ID
                id_filter = {"op": "literal", "value": False}
                for row_id in row_ids:
                    id_filter = {
                        "op": "or",
                        "left": id_filter,
                        "right": {
                            "op": "eq",
                            "left": {"op": "ident", "name": ["id"]},
                            "right": {"op": "literal", "value": row_id},
                        },
                    }

                audit_resp = requests.post(
                    f"{LOCAL_API_URL}/btql",
                    json={
                        "query": {
                            "from": {
                                "op": "function",
                                "name": {"op": "ident", "name": ["project_logs"]},
                                "args": [{"op": "literal", "value": project_id}],
                            },
                            "filter": id_filter,
                            "select": [{"op": "star"}],
                        },
                        "fmt": "json",
                        "audit_log": True,
                        **self.make_mode_args(mode),
                    },
                    headers=headers,
                )
                assert audit_resp.ok, audit_resp.text
                audit_data = audit_resp.json()["data"]
                self.assertEqual(len(audit_data), len(ids), "audit check")

        SORTS = [
            ([{"expr": {"btql": "input"}, "dir": "asc"}], [0, 1, 2]),
            (
                [{"expr": {"btql": "scores.foo"}, "dir": "asc"}, {"expr": {"btql": "input"}, "dir": "asc"}],
                [0, 1, 2],
            ),
            (
                [{"expr": {"btql": "scores.bar"}, "dir": "asc"}, {"expr": {"btql": "input"}, "dir": "asc"}],
                [1, 0, 2],
            ),
            (
                [{"expr": {"btql": "scores.foo IS NULL ? input : scores.foo"}, "dir": "asc"}],
                [0, 1, 2],
            ),
            (
                [{"expr": {"btql": "scores.foo IS NULL ? input : scores.foo"}, "dir": "desc"}],
                [2, 1, 0],
            ),
        ]
        for sort, expected in SORTS:
            resp = requests.post(
                f"{LOCAL_API_URL}/btql",
                json={
                    "query": {
                        "from": {
                            "op": "function",
                            "name": {"op": "ident", "name": ["project_logs"]},
                            "args": [{"op": "literal", "value": project_id}],
                        },
                        "sort": sort,
                        "select": [
                            {"alias": "input", "expr": {"btql": "input"}},
                            {"alias": "user_id", "expr": {"btql": "metadata.user_id"}},
                        ],
                    },
                    "fmt": "json",
                    **self.make_mode_args(mode),
                },
                headers=headers,
            )

            assert resp.ok, resp.text

            ids = [r["input"] for r in resp.json()["data"]]
            self.assertEqual(expected, ids, f"(mode={mode}) {sort} {expected} {ids}")

    # It is unfortunately very hard to force postgres to use a gin index.
    # def test_explain(self):
    #     filters = [
    #         ({"op": "eq", "left": {"btql": "id"}, "right": {"op": "literal", "value": "foo"}}, "gin"),
    #         ({"op": "eq", "left": {"btql": "metadata.user_id"}, "right": {"op": "literal", "value": "foo"}}, "gin"),
    #         (
    #             {"op": "includes", "needle": {"btql": "id"}, "haystack": {"op": "literal", "value": ["foo", "bar"]}},
    #             "gin",
    #         ),
    #         (
    #             {"op": "includes", "haystack": {"btql": "tags"}, "needle": {"op": "literal", "value": "foo"}},
    #             "gin",
    #         ),
    #     ]

    #     for f, match_type in filters:
    #         query = {
    #             "from": {
    #                 "op": "function",
    #                 "name": {"op": "ident", "name": ["project_logs"]},
    #                 "args": [{"op": "literal", "value": self.project_id}],
    #             },
    #             "filter": f,
    #             "select": [{"op": "star"}],
    #         }
    #         ret = requests.post(
    #             f"{LOCAL_API_URL}/btql",
    #             json={
    #                 "query": query,
    #                 "_debug_explain": True,
    #             },
    #             headers=self._get_request_headers(),
    #         )
    #         print(ret.json())

    @parameterized.expand(WITH_BRAINSTORE)
    def test_pivot_with_group(self, mode):
        # Inspired by the experiment summary query.
        experiments = [braintrust.init("p") for _ in range(5)]
        NUM_ROWS_PER_EXPERIMENT = 10
        experiment_id_to_avg_score = {}
        experiment_id_to_foo_score = {}
        for experiment_idx, e in enumerate(experiments):
            all_scores = []
            has_foo_score = experiment_idx % 2 == 0
            for i in range(NUM_ROWS_PER_EXPERIMENT):
                score = experiment_idx / len(experiments) * i / NUM_ROWS_PER_EXPERIMENT
                all_scores.append(score)
                scores = {"accuracy": score}
                if has_foo_score:
                    scores["foo"] = score
                e.log(input=i, output=2 * i, expected=3 * i, scores=scores)
            experiment_id_to_avg_score[e.id] = round_floats(sum(all_scores) / len(all_scores))
            if has_foo_score:
                experiment_id_to_foo_score[e.id] = round_floats(sum(all_scores) / len(all_scores))
        braintrust.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": e.id} for e in experiments],
            },
            "unpivot": [{"alias": ["score", "value"], "expr": {"btql": "scores"}}],
            "measures": [
                {"alias": "num_examples", "expr": {"btql": "count(1)"}},
                {"alias": "avg", "expr": {"btql": "avg(value)"}},
            ],
            "dimensions": [
                {"alias": "experiment_id", "expr": {"btql": "experiment_id"}},
            ],
            "pivot": [
                {"alias": "scores", "expr": {"btql": "score"}},
            ],
        }

        results = round_floats(self._run_drilldown(query, mode)["data"])
        self.assertEqual(
            {r["experiment_id"]: r for r in results},  # type: ignore
            {
                experiment_id: dict(
                    experiment_id=experiment_id,
                    avg=avg,
                    # NOTE: This intentionally proves why just computing COUNT(*) can't work, and we have to either
                    # make this COUNT(DISTINCT) or compute an un-pivoted count in a separate query.
                    num_examples=NUM_ROWS_PER_EXPERIMENT * (1 + int(experiment_id in experiment_id_to_foo_score)),
                    scores=dict(
                        accuracy=dict(
                            avg=avg,
                            num_examples=10,
                        ),
                        **(
                            {
                                "foo": dict(avg=experiment_id_to_foo_score[experiment_id], num_examples=10),
                            }
                            if experiment_id in experiment_id_to_foo_score
                            else {}
                        ),
                    ),
                )
                for experiment_id, avg in experiment_id_to_avg_score.items()
            },
        )

    @parameterized.expand(WITH_BRAINSTORE)
    def test_preview_mode(self, mode):
        experiment = braintrust.init(project="preview_mode")
        for i in range(3):
            with experiment.start_span("root") as span:
                span.log(input=f"root_{i}", output=f"root_{i}", scores={"root": 0.5})

                with span.start_span("child") as child_span:
                    child_span.log(input=f"child_{i}", output=f"child_{i}", scores={"child": 0.5})

        experiment.flush()

        preview_mode_fields = ["input", "output", "expected"]
        preview_mode_projection = [
            {
                "op": "star",
                "replace": {
                    **{f: {"btql": f"is_root ? {f} : null"} for f in preview_mode_fields},
                    "metadata": {"btql": "is_root ? metadata : insert({}, 'model', metadata.model)"},
                },
            }
        ]

        # First, project the full result set
        query = {
            "select": preview_mode_projection,
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["experiment"]},
                "args": [{"op": "literal", "value": experiment.id}],
                "shape": "traces",
            },
        }

        results = self._run_drilldown(query, mode)["data"]
        self.assertEqual(len(results), 6)
        for r in results:
            if len(r.get("span_parents") or []) == 0:
                self.assertTrue(r["input"] is not None)
                self.assertTrue(r["output"] is not None)
                self.assertTrue(r["scores"] is not None)
            else:
                self.assertIsNone(r["input"])
                self.assertIsNone(r["output"])
                self.assertTrue(r["scores"] is not None)

        # Next, query and filter based on the span's input value. Despite projecting out to NULL,
        # we should still match the appropriate row.
        query["filter"] = {"btql": "input='child_0'"}

        results = self._run_drilldown(query, mode)["data"]

        # DuckDB doesn't support trace/span expansion so will only match 1 row (the actual child span)
        self.assertEqual(len(results), 2 if mode != "duckdb" else 1)

        child_span = [r for r in results if len(r.get("span_parents") or []) == 1][0]
        self.assertTrue(child_span["input"] is None)
        self.assertTrue(child_span["output"] is None)
        self.assertEqual(child_span["scores"], {"child": 0.5})

        if mode == "brainstore":
            results = self._run_drilldown(query, mode, explain=True)["explain"]
            # Use regular expressions to search for the patterns with a single line break
            lines = results.splitlines()
            span_columnar_line = [i for i, line in enumerate(lines) if "Span is columnar" in line]
            self.assertEqual(len(span_columnar_line), 1)
            self.assertTrue("true" in lines[span_columnar_line[0] + 1])

            root_columnar_line = [i for i, line in enumerate(lines) if "Root is columnar" in line]
            self.assertEqual(len(root_columnar_line), 1)
            self.assertTrue("false" in lines[root_columnar_line[0] + 1])

    @parameterized.expand(["brainstore"])
    def test_summary_mode(self, mode):
        logger = braintrust.init_logger("summary_mode_test")

        long_field = "This is a very long field that should be truncated when preview_length is set. " * 10

        logger.log(
            input=long_field,
            output=long_field,
            expected=long_field,
            scores={"accuracy": 0.8},
            metadata={"description": long_field, "index": 2},
        )
        logger.flush()

        colA = self._create_column(logger.project.id, "description_val", "metadata.description")
        colB = self._create_column(logger.project.id, "index_val", "metadata.index")
        colC = self._create_column(logger.project.id, "invalid_val", "accuracy")
        colD = self._create_column(logger.project.id, "no_val", "metadata.no_val")
        colE = self._create_column(logger.project.id, "invalid_btql", "metadata.description + metadata.description")

        self._test_default_preview_length(logger, mode)
        self._test_custom_preview_lengths(logger, mode)
        self._test_zero_preview_length(logger, mode)
        self._test_long_preview_length(logger, mode)
        self._test_preview_length_text_fields(logger, mode)
        self._test_filtering(logger, mode)

        for colId in [colA, colB, colC, colD, colE]:
            requests.delete(
                f"{LOCAL_API_URL}/v1/column/{colId}",
                headers=self._get_request_headers(),
            )

    def _create_column(self, project_id, name, expr):
        col = requests.post(
            f"{LOCAL_API_URL}/v1/column",
            json={
                "object_id": project_id,
                "object_type": "project",
                "subtype": "project_log",
                "variant": "project_log",
                "name": name,
                "expr": expr,
            },
            headers=self._get_request_headers(),
        )
        return col.json()["id"]

    def _get_base_query(self, logger):
        return {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
                "shape": "summary",
            },
            "select": [{"op": "star"}],
            "comparison_key": {"btql": "input"},
        }

    def _test_default_preview_length(self, logger, mode):
        """Test that default preview length (125 chars) works correctly."""
        query = self._get_base_query(logger)
        results = self._run_drilldown(query, mode)["data"]

        self.assertEqual(len(results), 1)
        self.assertEqual(len(results[0]["input"]), 128)  # 125 chars + "..."
        self.assertEqual(len(results[0]["output"]), 128)
        self.assertEqual(len(results[0]["expected"]), 128)
        self.assertEqual(len(results[0]["metadata"]), 128)
        self.assertEqual(len(results[0]["description_val"]), 128)

    def _test_custom_preview_lengths(self, logger, mode):
        query = self._get_base_query(logger)
        query["preview_length"] = 50
        results = self._run_drilldown(query, mode)["data"]

        self.assertEqual(len(results), 1)
        self.assertEqual(len(results[0]["input"]), 53)  # 50 chars + "..."
        self.assertEqual(len(results[0]["output"]), 53)
        self.assertEqual(len(results[0]["expected"]), 53)
        self.assertEqual(len(results[0]["metadata"]), 53)

    def _test_zero_preview_length(self, logger, mode):
        """Test that preview length of 0 returns '...'."""
        query = self._get_base_query(logger)
        query["preview_length"] = 0
        results = self._run_drilldown(query, mode)["data"]

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["input"], "...")
        self.assertEqual(results[0]["output"], "...")
        self.assertEqual(results[0]["expected"], "...")
        self.assertEqual(results[0]["metadata"], "...")

    def _test_long_preview_length(self, logger, mode):
        query = self._get_base_query(logger)
        query["preview_length"] = 1000
        results = self._run_drilldown(query, mode)["data"]

        self.assertEqual(len(results), 1)
        long_field = "This is a very long field that should be truncated when preview_length is set. " * 10
        self.assertEqual(results[0]["input"], long_field)
        self.assertEqual(results[0]["output"], long_field)
        self.assertEqual(results[0]["expected"], long_field)
        self.assertEqual(results[0]["metadata"], {"description": long_field, "index": 2})

    def _test_preview_length_text_fields(self, logger, mode):
        """Test that preview length only affects text fields."""
        query = self._get_base_query(logger)
        query["preview_length"] = 50
        results = self._run_drilldown(query, mode)["data"]

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["scores"], {"accuracy": 0.8})
        self.assertEqual(results[0]["no_val"], None)
        self.assertEqual(results[0]["invalid_val"], "<Invalid column>")
        self.assertEqual(results[0]["invalid_btql"], None)

    def _test_filtering(self, logger, mode):
        """Test filtering behavior in summary mode."""
        query = self._get_base_query(logger)

        query["filter"] = {"btql": "index_val > 2"}
        results = self._run_drilldown(query, mode)["data"]
        self.assertEqual(len(results), 0)

        query["filter"] = {"btql": "index_val = 2"}
        results = self._run_drilldown(query, mode)["data"]
        self.assertEqual(len(results), 1)

    @parameterized.expand([(db, fmt) for db in ["brainstore"] for fmt in ["json", "parquet"]])
    def test_summary_schema(self, mode, fmt):
        logger = braintrust.init_logger("summary_schema_test")

        long_field = "This is a very long field that should be truncated when preview_length is set. " * 10

        requests.post(
            f"{LOCAL_API_URL}/api/project_score/register",
            json=dict(
                project_id=logger.project.id,
                project_score_name="quality",
                score_type="categorical",
                categories=[{"name": "good", "value": 1}, {"name": "bad", "value": 0}],
                config={"multi_select": False},
            ),
            headers=self._get_request_headers(),
        )

        log_id = logger.log(
            input=long_field,
            output=long_field,
            expected=long_field,
            scores={"accuracy": 0.8, "Accuracy": 0.8},
            metadata={"description": long_field, "index": 2},
        )
        logger.flush()

        query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": logger.project.id}],
                "shape": "summary",
            },
            "select": [{"op": "star"}],
            "comparison_key": {"btql": "input"},
        }

        response = requests.post(
            f"{LOCAL_API_URL}/btql",
            json={
                "query": query,
                "fmt": fmt,
                **self.make_mode_args(mode),
            },
            headers=self._get_request_headers(),
            allow_redirects=True,
        )
        self.assertTrue(response.ok, response.text)

        if fmt == "json":
            schema = response.json()["schema"]
            # Schema should include project config scores and be case sensitive
            self.assertIn("quality", schema["items"]["properties"]["scores"]["properties"])
            self.assertEqual(len(schema["items"]["properties"]["scores"]["properties"]), 3)

        elif fmt == "parquet":
            parquet_bytes = response.content
            schema = pq.read_schema(pyarrow.BufferReader(parquet_bytes))
            score_names = [field.name for field in schema.field("scores").flatten()]
            # Schema should include project config scores and be case insensitive for duckdb
            self.assertIn("scores.quality", score_names)
            self.assertEqual(len(score_names), 2)
        else:
            raise ValueError(f"Unknown format: {fmt}")

    @parameterized.expand(["brainstore"])
    def test_custom_column_queries(self, mode):
        logger = braintrust.init_logger("summary_mode_test")

        logger.log(
            input="Hi",
            output="Hi",
            expected="Hi",
            scores={"accuracy": 0.8},
            metadata={"index": 2},
        )
        logger.flush()

        self._test_default(logger, mode)
        self._test_summary_fields_error(logger, mode)
        self._test_select_error(logger, mode)

    def _test_default(self, logger, mode):
        query = self._get_base_query(logger)
        results = self._run_drilldown(
            {
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["project_logs"]},
                    "args": [{"op": "literal", "value": logger.project.id}],
                    "shape": "spans",
                },
                "select": [{"op": "star"}],
                "custom_columns": [
                    {
                        "alias": "metadata_index",
                        "expr": {"btql": "metadata.index"},
                    }
                ],
            },
            mode,
        )["data"]

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["metadata_index"], 2)

    def _test_summary_fields_error(self, logger, mode):
        query = self._get_base_query(logger)
        results = self._run_drilldown(
            {
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["project_logs"]},
                    "args": [{"op": "literal", "value": logger.project.id}],
                    "shape": "spans",
                },
                "select": [{"op": "star"}],
                "preview_length": 100,
                "custom_columns": [
                    {
                        "alias": "metadata_index",
                        "expr": {"btql": "metadata.index"},
                    }
                ],
            },
            mode,
            True,
        )

        self.assertEqual(results["Code"], "BadRequestError")
        self.assertTrue(
            "Summary fields (comparison_key, weighted_scores, preview_length) are only supported for traces"
            in results["Message"]
        )

    def _test_select_error(self, logger, mode):
        query = self._get_base_query(logger)
        results = self._run_drilldown(
            {
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": ["project_logs"]},
                    "args": [{"op": "literal", "value": logger.project.id}],
                    "shape": "spans",
                },
                "dimensions": [
                    {
                        "alias": "index",
                        "expr": {"btql": "metadata.index"},
                    }
                ],
                "custom_columns": [
                    {
                        "alias": "metadata_index",
                        "expr": {"btql": "metadata.index"},
                    }
                ],
            },
            mode,
            True,
        )

        self.assertEqual(results["Code"], "BadRequestError")
        self.assertTrue("custom_columns are only supported for select queries" in results["Message"])


FORMATS = ["json", "jsonl", "parquet"]
OVERFLOW = [False, True]
FORMAT_PARAMETERS = [(db, fmt, overflow) for db in WITH_BRAINSTORE for fmt in FORMATS for overflow in OVERFLOW]


class FormatTest(BraintrustAppTestBase):
    def setUp(self):  # type: ignore
        super().setUp()
        self.dataset = braintrust.init_dataset(project="format_test", use_output=False)
        self.rows = {}
        for n in range(NUM_EXPERIMENT_ROWS):
            row = {"input": n, "expected": NUM_EXPERIMENT_ROWS - n}
            row_id = self.dataset.insert(**row)  # type: ignore
            self.dataset.flush()
            self.rows[row_id] = row

        self.active_row_ids = set()
        for n, row_id in enumerate(self.rows.keys()):
            if n % 2 == 0:
                self.dataset.delete(row_id)
            else:
                self.active_row_ids.add(row_id)

        self.dataset.flush()
        self.project_id = self.dataset.project.id

    def _get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def make_mode_args(self, mode):
        mode_args = {"use_brainstore": False}  # TODO(BRAINSTORE): Fix this to use brainstore
        if mode == "duckdb":
            mode_args["_debug_use_duckdb"] = True
        elif mode == "brainstore":
            mode_args.update(self.brainstore_query_args())
        return mode_args

    def _run_query(self, query, mode, allow_error=False, tz_offset=None, fmt="json", overflow=False):
        ret = requests.post(
            f"{LOCAL_API_URL}/btql",
            json={
                "query": query,
                "fmt": fmt,
                **self.make_mode_args(mode),
                **({"tz_offset": tz_offset} if tz_offset is not None else {}),
                **({"overflow_results": overflow} if overflow else {}),
            },
            headers=self._get_request_headers(),
            allow_redirects=True,
        )
        if not allow_error:
            self.assertTrue(ret.ok, ret.text)

        cursor = ret.headers.get("x-bt-cursor", ret.headers.get("x-amz-meta-bt-cursor"))

        if fmt == "json":
            d = ret.json()
            rows = d["data"]
            cursor = d.get("cursor")

            schema = d.get("schema")
            self.assertIsNotNone(schema)
            self.assertNotIn("pagination_cursor_xact_id", schema["items"]["properties"])
            self.assertNotIn("pagination_cursor_root_span_id", schema["items"]["properties"])

            json_schema_validate(rows, schema)

        elif fmt == "jsonl":
            rows = [json.loads(line) for line in ret.text.splitlines() if line.strip()]
        elif fmt == "parquet":
            parquet_bytes = ret.content
            rows = [
                {**row, "expected": json.loads(row["expected"]), "input": json.loads(row["input"])}
                for row in pq.read_table(pyarrow.BufferReader(parquet_bytes)).to_pylist()
            ]
        else:
            raise ValueError(f"Unknown format: {fmt}")

        return rows, cursor

    def _strip_row(self, row):
        return {"input": row["input"], "expected": row["expected"]}

    @parameterized.expand(FORMAT_PARAMETERS)
    def test_select_star(self, mode, fmt, overflow):
        print(f"mode={mode} fmt={fmt} overflow={overflow}")
        all_rows, _ = self._run_query(
            query=f"from: dataset('{self.dataset.id}') | select: *",
            mode=mode,
            fmt=fmt,
            overflow=overflow,
        )

        for row in all_rows:
            row_id = row["id"]
            self.assertIn(row_id, self.rows)
            self.assertEqual(self._strip_row(row), self.rows[row_id])

    def test_s3(self):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("Skipping S3 test")
        ret = requests.post(
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"from: dataset('{self.dataset.id}') | select: *",
                "overflow_results": True,
            },
            headers=self._get_request_headers(),
            allow_redirects=False,
        )
        self.assertEqual(ret.status_code, 303)

    @parameterized.expand(FORMAT_PARAMETERS)
    def test_sort_limit(self, mode, fmt, overflow):
        print(f"mode={mode} fmt={fmt} overflow={overflow}")
        all_rows, _ = self._run_query(
            query=f"from: dataset('{self.dataset.id}') | select: * | limit: 1 | sort: expected asc",
            mode=mode,
            fmt=fmt,
            overflow=overflow,
        )

        self.assertEqual(len(all_rows), 1)
        row = self._strip_row(all_rows[0])
        self.assertEqual(row["input"], NUM_EXPERIMENT_ROWS - 1)
        self.assertEqual(row["expected"], 1)

    @parameterized.expand(FORMAT_PARAMETERS)
    def test_paginate(self, mode, fmt, overflow):
        limit = 2
        cursor = None
        all_rows = []
        for _ in range(100):
            print(f"mode={mode} fmt={fmt} overflow={overflow} cursor={cursor}")
            query = f"from: dataset('{self.dataset.id}') | select: * | limit: {limit}"
            if cursor:
                query += f" | cursor: '{cursor}'"

            rows, cursor = self._run_query(query, mode, fmt=fmt, overflow=overflow)
            self.assertLessEqual(len(rows), limit)

            if len(rows) == 0:
                break

            all_rows.extend(rows)
        else:
            self.fail("Too many pages")

        self.assertEqual(len(all_rows), len(self.active_row_ids))
        self.assertEqual(set(row["id"] for row in all_rows), self.active_row_ids)


def powerset(iterable):
    "powerset([1,2,3]) --> () (1,) (2,) (3,) (1,2) (1,3) (2,3) (1,2,3)"
    s = list(iterable)
    return chain.from_iterable(combinations(s, r) for r in range(len(s) + 1))


def round_floats(d, precision=4):
    if isinstance(d, dict):
        return {k: round_floats(v, precision) for k, v in d.items()}
    elif isinstance(d, list):
        return [round_floats(v, precision) for v in d]
    elif isinstance(d, float):
        return round(d, precision)
    else:
        return d
