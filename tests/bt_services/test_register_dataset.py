from concurrent.futures import ThreadPoolExecutor

import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


def getProjectInfo(resp):
    return {k: resp["project"][k] for k in ["name", "user_id"]}


def getDatasetInfo(resp):
    return {k: resp["dataset"][k] for k in ["project_id", "name", "description", "user_id"]}


class RegisterDatasetTest(BraintrustAppTestBase):
    def test_basic(self):
        args = dict(
            project_name="_test_register_dataset",
            dataset_name="_basic_dataset",
        )
        resp = self.registerDataset(args)

        project_info = {k: resp["project"][k] for k in ["name", "user_id"]}
        self.assertEqual(getProjectInfo(resp), dict(name=args["project_name"], user_id=self.user_id))
        self.assertEqual(
            getDatasetInfo(resp),
            dict(project_id=resp["project"]["id"], name=args["dataset_name"], description=None, user_id=self.user_id),
        )

        # Performing exactly the same request should yield the same response.
        resp2 = self.registerDataset(args)
        resp_no_found_existing = {**resp, "found_existing": None}
        resp2_no_found_existing = {**resp2, "found_existing": None}
        self.assertEqual(resp_no_found_existing, resp2_no_found_existing)
        self.assertFalse(resp["found_existing"])
        self.assertTrue(resp2["found_existing"])

    def test_resource_check(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    insert into resources (org_id, forbid_insert_datasets)
                    values (%s, %s)
                    """,
                    [
                        self.org_id,
                        True,
                    ],
                )

        with self.assertRaises(Exception):
            self.registerDataset(
                dict(
                    project_name="_test_register_dataset",
                    dataset_name="_basic_dataset",
                )
            )

    def test_concurrent_dataset_registration(self):
        NUM_DATASETS = 100
        dataset_names = ["d0", "d1", "d2", "d3", "d4", "d5"]
        with ThreadPoolExecutor(NUM_DATASETS) as executor:
            results = []
            for i in range(NUM_DATASETS):
                results.append(
                    executor.submit(
                        self.registerDataset,
                        dict(
                            project_name="_concurrent_register_dataset",
                            dataset_name=dataset_names[i % len(dataset_names)],
                        ),
                    )
                )
            for r in results:
                r.result()

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*)
                    from datasets join projects on datasets.project_id = projects.id
                    where projects.org_id = %s
                    """,
                    [self.org_id],
                )
                actual_num_datasets = cursor.fetchone()[0]
        self.assertEqual(len(dataset_names), actual_num_datasets)
