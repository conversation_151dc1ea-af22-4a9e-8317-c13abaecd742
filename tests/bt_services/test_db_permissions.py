from tests.braintrust_app_test_base import BraintrustAppTestBase


class DbPermissionsTest(BraintrustAppTestBase):
    def test_table_rls(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select relname
                    from
                        pg_class
                        join pg_catalog.pg_namespace n on n.oid = pg_class.relnamespace
                    where
                        n.nspname='public'
                        and relkind = 'r'
                        and not relrowsecurity
                """
                )
                violations = [x[0] for x in cursor.fetchall()]
                self.assertEqual(violations, [], f"The following tables lack an RLS policy: {violations}")

    def test_udf_execute_privileges(self):
        ANON_UDFS = {
            "check_org_name_conflict",
        }
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select distinct routine_name
                    from information_schema.role_routine_grants
                    where
                        routine_schema='public'
                        and lower(privilege_type)='execute'
                        and lower(grantee) in ('anon')
                """
                )
                violations = []
                expected_anon = set()
                for row in cursor:
                    routine_name = row[0]
                    if routine_name in ANON_UDFS:
                        expected_anon.add(routine_name)
                    else:
                        violations.append(routine_name)
                self.assertEqual(
                    expected_anon, ANON_UDFS, "Not all UDFs expected to have anonymous execution access have it"
                )
                self.assertEqual(violations, [], f"The following UDFs grant anonymous execution: {violations}")
