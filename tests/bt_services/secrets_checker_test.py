import os

import braintrust
from pydantic import BaseModel

project = braintrust.projects.create(name="function test")


class Input(BaseModel):
    pass


def handler():
    import os

    if os.environ.get("SECRET"):
        return os.environ["SECRET"]
    else:
        raise Exception("SECRET environment variable not set")


replace_behavior = os.environ.get("IF_EXISTS")

project.tools.create(
    handler=handler,
    name="Secrets Checker (bundled)",
    slug="secret-checker-bundled",
    if_exists=replace_behavior,
    parameters=Input,
)
