from uuid import uuid4

import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def fix_add_members(res):
    res["added_users"] = {x["id"]: x["email"] for x in res["added_users"]}
    return res


class InviteUsersTest(BraintrustAppTestBase):
    def get_user_email(self, user_id):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select email from users where id = %s", (user_id,))
                return cursor.fetchone()[0]

    def initialize_user(self, email):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select id from users where email = %s", (email,))
                user_id = cursor.fetchone()[0]
                self.additional_users.add(user_id)
                # Set an auth_id for this user.
                cursor.execute("update users set auth_id=uuid_generate_v4() where id = %s", (user_id,))
                conn.commit()
                # Create an API key for them.
                api_key = self.createUserOrgApiKey(user_id, self.org_id)
                return user_id, api_key

    def test_basic(self):
        user0_email = self.createUserEmail()
        user1_email = self.createUserEmail()

        result = fix_add_members(
            self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(emails=[user0_email, user1_email], group_name="Owners")),
            ).json()
        )
        user0_id, user0_api_key = self.initialize_user(user0_email)
        user1_id, user1_api_key = self.initialize_user(user1_email)
        self.assertEqual(result, dict(added_users={user0_id: user0_email, user1_id: user1_email}))

        # Adding the users again should succeed but return no added users.
        result = self.run_server_action(
            self.org_api_key,
            "addMembers",
            dict(orgId=self.org_id, users=dict(emails=[user0_email, user1_email])),
        ).json()
        self.assertEqual(result, dict(added_users=[]))

        # Evict user1 from Owners, but give them project creation permissions.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "delete from group_users where group_id = get_group_id(%s, 'Owners') and user_id = %s",
                    (self.org_id, user1_id),
                )
        self.grant_acl(
            dict(object_type="org_project", object_id=self.org_id, user_id=user1_id, permission="create"),
        )

        # They should both be able to create projects.
        def create_project(api_key, expect_error=False):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/v1/project",
                headers={"Authorization": f"Bearer {api_key}"},
                json=dict(name=f"proj_{uuid4()}"),
                expect_error=expect_error,
            )

        create_project(user0_api_key)
        create_project(user1_api_key)

        # user0 should also be able to invite another user and remove them, but
        # user1 should not be able to add or remove users.
        user2_email = self.createUserEmail()
        result = fix_add_members(
            self.run_server_action(
                user0_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(emails=[user2_email], group_name="Owners")),
            ).json()
        )
        user2_id, user2_api_key = self.initialize_user(user2_email)
        self.assertEqual(result, dict(added_users={user2_id: user2_email}))

        # The new user should also be able to create a project in the org.
        create_project(user2_api_key)

        self.run_server_action(
            user1_api_key, "removeMembers", dict(orgId=self.org_id, users=dict(ids=[user2_id])), expect_error=True
        )
        self.run_server_action(
            user1_api_key,
            "addMembers",
            dict(orgId=self.org_id, users=dict(emails=[user2_email + "foo"])),
            expect_error=True,
        )
        result = self.run_server_action(
            user0_api_key, "removeMembers", dict(orgId=self.org_id, users=dict(ids=[user2_id]))
        ).json()
        self.assertEqual(result, None)

        # After removal, they should not be able to create a project.
        create_project(user2_api_key, expect_error=True)

    def test_unpriviliged_user_cannot_invite_others_to_owners(self):
        user0_id, _, user0_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.grant_acl(
            dict(object_type="org_member", object_id=self.org_id, user_id=user0_id, permission="create"),
        )

        def is_in_owners(user_id):
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        select
                            exists(
                                select 1 from group_users
                                where
                                    group_id = get_group_id(%s, 'Owners')
                                    and user_id = %s
                            ) is_owner
                    """,
                        (self.org_id, user_id),
                    )
                    return cursor.fetchone()[0]

        # If user0 tries to add user1 with no group id (defaulting to owners),
        # the operation should fail, because user0 does not have permission to
        # add to 'Owners'.
        user1_email = self.createUserEmail()
        self.run_server_action(
            user0_api_key,
            "addMembers",
            dict(orgId=self.org_id, users=dict(emails=[user1_email], group_name="Owners")),
            expect_error=True,
        )
        # But it should work fine to add them to no group.
        result = fix_add_members(
            self.run_server_action(
                user0_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(emails=[user1_email])),
            ).json()
        )
        user1_id, _ = self.initialize_user(user1_email)
        self.assertEqual(result, dict(added_users={user1_id: user1_email}))
        self.assertFalse(is_in_owners(user1_id))

        # But if we give user0 update access on the Owners group, they should
        # be able to add another user to Owners.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select get_group_id(%s, 'Owners')", (self.org_id,))
                owner_group_id = cursor.fetchone()[0]
        self.grant_acl(
            dict(object_type="group", object_id=owner_group_id, user_id=user0_id, permission="update"),
        )

        user2_email = self.createUserEmail()
        result = fix_add_members(
            self.run_server_action(
                user0_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(emails=[user2_email], group_name="Owners")),
            ).json()
        )
        user2_id, _ = self.initialize_user(user2_email)
        self.assertEqual(result, dict(added_users={user2_id: user2_email}))
        self.assertTrue(is_in_owners(user2_id))

    def test_add_id_and_emails(self):
        user0_id, _ = self.createUser()
        user0_email = self.get_user_email(user0_id)
        user1_email = self.createUserEmail()

        result = fix_add_members(
            self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(ids=[user0_id], emails=[user1_email], group_name="Owners")),
            ).json()
        )
        user1_id, _ = self.initialize_user(user1_email)
        self.assertEqual(result, dict(added_users={user0_id: user0_email, user1_id: user1_email}))

    def test_add_nonexistent_id(self):
        self.run_server_action(
            self.org_api_key,
            "addMembers",
            dict(orgId=self.org_id, users=dict(ids=[str(uuid4())])),
            expect_error=True,
        )

    def test_specify_group_id_and_name(self):
        user0_id, _ = self.createUser()
        user0_email = self.get_user_email(user0_id)

        # No partial addition to group, if you specify a nonexistent group_id.
        new_group_id = str(uuid4())
        self.run_server_action(
            self.org_api_key,
            "addMembers",
            dict(orgId=self.org_id, users=dict(ids=[user0_id], group_ids=[new_group_id], group_names=["Owners"])),
            expect_error=True,
        )

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*)
                    from group_users
                    where user_id = %s
                    """,
                    (user0_id,),
                )
                self.assertEqual(cursor.fetchone()[0], 0)

        # But it works if you specify a valid group_id.
        new_group_id = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/group",
            json=dict(
                name="test group",
            ),
        ).json()["id"]

        result = fix_add_members(
            self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(ids=[user0_id], group_ids=[new_group_id], group_names=["Owners"])),
            ).json()
        )
        self.assertEqual(result, dict(added_users={user0_id: user0_email}))

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*)
                    from group_users
                    where
                        (group_id = %s or group_id = get_group_id(%s, 'Owners'))
                        and user_id = %s
                    """,
                    (new_group_id, self.org_id, user0_id),
                )
                self.assertEqual(cursor.fetchone()[0], 2)

    def test_send_email(self):
        user0_id, _ = self.createUser()
        user0_email = self.get_user_email(user0_id)
        # The invite should occur with no error, since resend is not configured
        # locally.
        result = fix_add_members(
            self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(ids=[user0_id], send_invite_emails=True)),
            ).json()
        )
        self.assertEqual(result, dict(added_users={user0_id: user0_email}))

    def test_batch_create_service_accounts(self):
        user0_id, _ = self.createUser()
        user1_id, _ = self.createUser()

        user2_email = self.createUserEmail()
        user3_email = self.createUserEmail()

        service_account_prefix = f"_test_service_account_{uuid4()}"
        result = fix_add_members(
            self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(
                    orgId=self.org_id,
                    users=dict(
                        service_accounts=[
                            dict(name=f"{service_account_prefix}-1", token_name="test-token"),
                            dict(name=f"{service_account_prefix}-2", token_name="test-token-2"),
                        ],
                        user_ids=[user0_id, user1_id],
                        emails=[user2_email, user3_email],
                    ),
                ),
            ).json()
        )
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select id, email from users where
                            (starts_with(given_name, %s) and user_type = 'service_account') OR
                            (email in (%s, %s) and user_type = 'user')
                """,
                    (service_account_prefix, user2_email, user3_email),
                )
                users = cursor.fetchall()
                self.assertEqual(len(users), 4)
                self.assertEqual(result, dict(added_users={x[0]: x[1] for x in users}))

    def test_rest_api(self):
        user0_id, _, user0_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        user1_email = self.createUserEmail()
        user2_id, _, __ = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # user0 should not be able to invite / remove anybody, because they have
        # no permissions.
        user0_headers = {"Authorization": f"Bearer {user0_api_key}"}
        request_body = dict(
            invite_users=dict(emails=[user1_email]),
            remove_users=dict(ids=[user2_id]),
        )
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers=user0_headers,
            json=request_body,
            expect_error=True,
        )

        # Should still not work if they only have "add members" permission.
        self.grant_acl(
            dict(object_type="org_member", object_id=self.org_id, user_id=user0_id, permission="create"),
        )
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers=user0_headers,
            json=request_body,
            expect_error=True,
        )

        # Make sure nothing happened.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select 1
                    from members join users on members.user_id = users.id
                    where org_id = %s and users.email = %s
                """,
                    (self.org_id, user1_email),
                )
                self.assertIsNone(cursor.fetchone())

                cursor.execute(
                    """
                    select 1 from members
                    where org_id = %s and user_id = %s
                """,
                    (self.org_id, user2_id),
                )
                self.assertEqual(cursor.fetchone()[0], 1)

        # Should work once we add "remove members" permission.
        self.grant_acl(
            dict(object_type="org_member", object_id=self.org_id, user_id=user0_id, permission="delete"),
        )
        result = self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers=user0_headers,
            json=request_body,
        ).json()
        self.assertEqual(result, dict(status="success", org_id=self.org_id))

        # Make sure it took place.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select 1
                    from members join users on members.user_id = users.id
                    where org_id = %s and users.email = %s
                """,
                    (self.org_id, user1_email),
                )
                self.assertEqual(cursor.fetchone()[0], 1)

                cursor.execute(
                    """
                    select 1 from members
                    where org_id = %s and user_id = %s
                """,
                    (self.org_id, user2_id),
                )
                self.assertIsNone(cursor.fetchone())

        # Do the inverse operation.
        inverse_request_body = dict(
            invite_users=dict(ids=[user2_id]),
            remove_users=dict(emails=[user1_email]),
        )
        result = self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers=user0_headers,
            json=inverse_request_body,
        ).json()
        self.assertEqual(result, dict(status="success", org_id=self.org_id))

        # Make sure it took place.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select 1
                    from members join users on members.user_id = users.id
                    where org_id = %s and users.email = %s
                """,
                    (self.org_id, user1_email),
                )
                self.assertIsNone(cursor.fetchone())

                cursor.execute(
                    """
                    select 1 from members
                    where org_id = %s and user_id = %s
                """,
                    (self.org_id, user2_id),
                )
                self.assertEqual(cursor.fetchone()[0], 1)

        # It should fail if we try to invite a user to the owners group, since
        # we don't have permission.
        group_request_body = dict(
            invite_users=dict(emails=[user1_email], group_name="Owners"),
        )
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers=user0_headers,
            json=group_request_body,
            expect_error=True,
        )

    def test_rest_api_errors(self):
        user0_id, _, user0_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        other_org_id, other_org_name = self.createOrg()

        def make_request(additional_params, expect_error=False):
            self.run_request(
                "patch",
                f"{LOCAL_API_URL}/v1/organization/members",
                headers={"Authorization": f"Bearer {self.org_api_key}"},
                json={**dict(invite_users=dict(ids=[user0_id])), **additional_params},
                expect_error=expect_error,
            )

        # It should work with no org spec.
        make_request({})

        # But if we try to add them with a different explicit org_id/org_name,
        # it should not work.
        make_request(dict(org_id=other_org_id), expect_error=True)
        make_request(dict(org_name=other_org_name), expect_error=True)

        # If we try with our own org_id/org_name/both, it should work.
        make_request(dict(org_id=self.org_id))
        make_request(dict(org_name=self.org_name))
        make_request(dict(org_id=self.org_id, org_name=self.org_name))

        # If we try with conflicting params, it should fail.
        make_request(dict(org_id=self.org_id, org_name=other_org_name), expect_error=True)
        make_request(dict(org_id=other_org_id, org_name=self.org_name), expect_error=True)

    def test_unprivileged_user_can_remove_self(self):
        # Create two unprivileged users in the org
        user1_id, _, user1_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user2_id, _, user2_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user2_email = self.get_user_email(user2_id)

        # Verify both users are initially members
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*) from members
                    where org_id = %s and user_id in (%s, %s)
                    """,
                    (self.org_id, user1_id, user2_id),
                )
                self.assertEqual(cursor.fetchone()[0], 2)

        # User1 should not be able to remove user2
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers={"Authorization": f"Bearer {user1_api_key}"},
            json=dict(remove_users=dict(ids=[user2_id])),
            expect_error=True,
        )
        # User1 should be able to remove themselves
        result = self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers={"Authorization": f"Bearer {user1_api_key}"},
            json=dict(remove_users=dict(ids=[user1_id])),
        ).json()
        self.assertEqual(result, dict(status="success", org_id=self.org_id))

        # User2 should be able to remove themselves using their email
        result = self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            headers={"Authorization": f"Bearer {user2_api_key}"},
            json=dict(remove_users=dict(emails=[user2_email])),
        ).json()
        self.assertEqual(result, dict(status="success", org_id=self.org_id))

        # Verify the final state - both users should be removed
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*) from members
                    where org_id = %s and user_id = %s
                    """,
                    (self.org_id, user1_id),
                )
                self.assertEqual(cursor.fetchone()[0], 0)

                cursor.execute(
                    """
                    select count(*) from members
                    where org_id = %s and user_id = %s
                    """,
                    (self.org_id, user2_id),
                )
                self.assertEqual(cursor.fetchone()[0], 0)

    def test_removed_user_loses_access(self):
        # Create a new user in the owners group
        user0_email = self.createUserEmail()
        result = fix_add_members(
            self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(emails=[user0_email], group_name="Owners")),
            ).json()
        )
        user0_id, user0_api_key = self.initialize_user(user0_email)
        self.assertEqual(result, dict(added_users={user0_id: user0_email}))

        # Have the new user create a project
        project = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project",
            headers={"Authorization": f"Bearer {user0_api_key}"},
            json=dict(name=f"proj_{uuid4()}"),
        ).json()

        # Verify they can read the project
        self.run_request(
            "get",
            f"{LOCAL_API_URL}/v1/project/{project['id']}",
            headers={"Authorization": f"Bearer {user0_api_key}"},
        )

        # Verify they have ACLs on the project
        acls = self.run_request(
            "get",
            f"{LOCAL_API_URL}/v1/acl",
            headers={"Authorization": f"Bearer {user0_api_key}"},
            params=dict(object_type="project", object_id=project["id"]),
        ).json()["objects"]
        self.assertEqual(len(acls), 1)
        self.assertEqual(acls[0]["user_id"], user0_id)

        # Remove the user from the org
        self.run_server_action(
            self.org_api_key,
            "removeMembers",
            dict(orgId=self.org_id, users=dict(ids=[user0_id])),
        )

        # Verify they can no longer read the project
        self.run_request(
            "get",
            f"{LOCAL_API_URL}/v1/project/{project['id']}",
            headers={"Authorization": f"Bearer {user0_api_key}"},
            expect_error=True,
        )

        # Verify they no longer have any ACLs on the project
        acls = self.run_request(
            "get",
            f"{LOCAL_API_URL}/v1/acl",
            params=dict(object_type="project", object_id=project["id"]),
        ).json()["objects"]
        self.assertEqual(acls, [])
