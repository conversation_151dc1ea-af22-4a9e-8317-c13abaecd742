// This file is not a standalone test, but is called from test_function_tools.py
// to test bundling.
import { ifExistsEnum } from "@braintrust/typespecs";
import braintrust from "braintrust";

const project = braintrust.projects.create({
  name: "function test",
});

const replaceBehavior = ifExistsEnum.optional().parse(process.env.IF_EXISTS);

project.tools.create({
  handler: async () => {
    if (process.env.SECRET) {
      return process.env.SECRET;
    } else {
      throw new Error("SECRET environment variable not set");
    }
  },
  name: "Secrets Checker (bundled)",
  slug: "secret-checker-bundled",
  ifExists: replaceBehavior,
});
