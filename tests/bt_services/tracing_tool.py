import braintrust
from pydantic import BaseModel

project = braintrust.projects.create(name="function test")


class Input(BaseModel):
    pass


def handler():
    with braintrust.start_span() as span:
        span.log(metadata={"foo": "bar"})
        return "hello"


project.tools.create(
    handler=handler,
    name="Tracing Tool",
    slug="tracing-tool",
    if_exists="replace",
    parameters=Input,
)
