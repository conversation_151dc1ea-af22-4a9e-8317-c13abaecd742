import base64
import hashlib
import time
import unittest
from typing import Literal, Optional

import braintrust
import requests
from braintrust.http_headers import BT_PARENT
from braintrust.otel import BraintrustSpanProcessor
from braintrust.span_identifier_v3 import SpanComponentsV3, SpanObjectTypeV3
from braintrust.util import <PERSON><PERSON>V<PERSON>ue
from google.protobuf import json_format
from opentelemetry import trace
from opentelemetry.proto.collector.trace.v1.trace_service_pb2 import (
    ExportTraceServiceRequest,
    ExportTraceServiceResponse,
)
from opentelemetry.proto.trace.v1.trace_pb2 import TracesData
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.trace import Status, StatusCode
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase

OTEL_ENDPOINT = f"{LOCAL_API_URL}/otel/v1/traces"


BRAINTRUST_PARENT_ATTRIBUTE = "braintrust.parent"


PARENT_HEADER_FORMATS = (
    "object_id_prefix",
    "object_name_prefix",
    "span_components_object_id",
    "span_components_object_metadata",
)


def make_otel_test_parameters():
    params = []
    for protocol in ("json", "protobuf"):
        for parent_object_type in SpanObjectTypeV3:
            for has_parent_span in (True, False):
                for parent_header_format in PARENT_HEADER_FORMATS:
                    if (
                        parent_object_type == SpanObjectTypeV3.EXPERIMENT
                        or parent_object_type == SpanObjectTypeV3.PLAYGROUND_LOGS
                    ) and parent_header_format in (
                        "object_name_prefix",
                        "span_components_object_metadata",
                    ):
                        # Experiments must be specified by ID, not name or slug.
                        continue
                    if has_parent_span and parent_header_format in ("object_name_prefix", "object_id_prefix"):
                        # Parent spans must be specified by span slug.
                        continue
                    params.append(
                        (
                            protocol,
                            parent_object_type,
                            has_parent_span,
                            parent_header_format,
                        )
                    )
    return params


def otel_parameterized_name_func(testcase_func, param_num, param):
    return "%s_%s" % (
        testcase_func.__name__,
        parameterized.to_safe_name("_".join(str(x) for x in param.args)),
    )


def generate_otel_id(name, num_bytes):
    return base64.b64encode(hashlib.sha256(name.encode()).digest()[:num_bytes]).decode()


ZERO_BYTES_PARENT_SPAN_ID = base64.b64encode(b"\x00" * 8).decode()


def make_trace(
    trace_name,
    base_name,
    child_name,
    use_zero_byte_parent_span_id_for_root_span=False,
):
    trace_id = generate_otel_id(trace_name, 16)
    base_span_id = generate_otel_id(base_name, 8)
    child_span_id = generate_otel_id(child_name, 8)

    return {
        "resourceSpans": [
            {
                "scopeSpans": [
                    {
                        "spans": [
                            {
                                "traceId": trace_id,
                                "spanId": child_span_id,
                                "parentSpanId": base_span_id,
                                "name": child_name,
                                "startTimeUnixNano": "1729544889170000000",
                                "endTimeUnixNano": "1729544889197359375",
                            },
                            {
                                "traceId": trace_id,
                                "spanId": base_span_id,
                                "name": base_name,
                                "startTimeUnixNano": "1729544889169000000",
                                "endTimeUnixNano": "1729544889198165292",
                                **(
                                    {"parentSpanId": ZERO_BYTES_PARENT_SPAN_ID}
                                    if use_zero_byte_parent_span_id_for_root_span
                                    else {}
                                ),
                            },
                        ],
                    },
                ],
            },
        ],
    }


def make_url(suffix):
    return f"{LOCAL_API_URL}/v1/{suffix}"


def get_request_headers(
    api_key,
    parent_header: Optional[str] = None,
    content_type: Optional[str] = None,
):
    headers = dict(Authorization=f"Bearer {api_key}")
    if content_type:
        headers["Content-Type"] = content_type
    if parent_header:
        headers[BT_PARENT] = parent_header
    return headers


class OtelTest(BraintrustAppTestBase):
    def _checked_request(self, verb, api_key, *args, expect_error=False, **kwargs):
        return self.run_request(verb, *args, expect_error=expect_error, headers=get_request_headers(api_key), **kwargs)

    def _checked_otel_request(
        self,
        api_key,
        trace_name,
        parent_span_components,
        parent_header,
        protocol,
        parent_object_type,
        has_parent_span,
        parent_header_format,
        send_invalid_trace=False,
        expect_error=False,
        use_zero_byte_parent_span_id_for_root_span=False,
    ):
        base_span_name = f"{trace_name}-base"
        child_span_name = f"{trace_name}-child"

        data = None
        if send_invalid_trace:
            data = {"foo": "bar"} if protocol == "json" else b"invalid"
        else:
            trace = make_trace(
                trace_name,
                base_span_name,
                child_span_name,
                use_zero_byte_parent_span_id_for_root_span,
            )
            if protocol == "protobuf":
                trace_proto = ExportTraceServiceRequest()
                json_format.ParseDict(trace, trace_proto)
                data = trace_proto.SerializeToString()
            else:
                data = trace
        assert data is not None

        data_kwargs = dict(json=data) if protocol == "json" else dict(data=data)
        content_type = "application/json" if protocol == "json" else "application/x-protobuf"
        self.run_request(
            "post",
            OTEL_ENDPOINT,
            expect_error=expect_error,
            headers=get_request_headers(
                api_key,
                parent_header,
                content_type=content_type,
            ),
            **data_kwargs,
        )

        fetch_otel_spans_query = {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": [str(parent_object_type)]},
                "args": [{"op": "literal", "value": parent_span_components.object_id}],
            },
            "dimensions": [
                {"alias": "name", "expr": {"btql": "name"}},
                {"alias": "span_id", "expr": {"btql": "span_id"}},
                {"alias": "span_parents", "expr": {"btql": "span_parents"}},
                {"alias": "root_span_id", "expr": {"btql": "root_span_id"}},
                {"alias": "is_root", "expr": {"btql": "is_root"}},
            ],
            "filter": {"btql": f"name = '{base_span_name}' or name = '{child_span_name}'"},
        }
        ret = requests.post(
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=fetch_otel_spans_query,
                use_columnstore=False,
                use_brainstore=False,  # TODO(BRAINSTORE): Fix this to use brainstore
            ),
            headers=get_request_headers(self.org_api_key),
        )
        data = ret.json()["data"]

        if expect_error:
            if len(data) > 0:
                raise Exception(f"Expected to create no spans for errored OTEL request, got: {data}")
            return

        base_span_candidates = [span for span in data if span["name"] == base_span_name]
        if len(base_span_candidates) != 1:
            raise Exception(f"Expected exactly one base span, got: {base_span_candidates}")
        base_span = base_span_candidates[0]

        child_span_candidates = [span for span in data if span["name"] == child_span_name]
        if len(child_span_candidates) != 1:
            raise Exception(f"Expected exactly one child span, got: {child_span_candidates}")
        child_span = child_span_candidates[0]

        root_span_id = base_span.get("root_span_id")
        assert root_span_id is not None
        assert child_span.get("root_span_id") == root_span_id

        if has_parent_span:
            assert root_span_id == parent_span_components.root_span_id
            base_span_span_parents = base_span.get("span_parents")
            assert len(base_span_span_parents) == 1
            assert base_span_span_parents[0] == parent_span_components.span_id
        else:
            assert len(base_span.get("span_parents") or []) == 0

        child_span_span_parents = child_span.get("span_parents")
        assert len(child_span_span_parents) == 1
        assert child_span_span_parents[0] == base_span.get("span_id")

    def _create_parent(
        self,
        project_name,
        parent_object_type,
        has_parent_span,
        parent_header_format,
    ):
        assert parent_header_format in PARENT_HEADER_FORMATS
        span_components, parent_header = None, None

        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            logger = braintrust.init_logger(project=project_name)
            if parent_object_type == SpanObjectTypeV3.EXPERIMENT:
                experiment = braintrust.init(project=project_name)
                parent_object = experiment
            elif parent_object_type == SpanObjectTypeV3.PLAYGROUND_LOGS:
                playground_meta = self.run_request(
                    "post",
                    f"{LOCAL_APP_URL}/api/prompt_session/register",
                    json=dict(
                        org_name=self.org_name,
                        project_name=project_name,
                        session_name="otel-test",
                    ),
                ).json()
                prompt_session_id = playground_meta["id"]
                parent_object = DummyPlaygroundObject(prompt_session_id, logger.project)
            else:
                parent_object = logger

            def _export_parent(parent):
                # Force-resolve the parent object_id if we want to put it in the parent span slug.
                if parent_header_format == "span_components_object_id":
                    parent_object._lazy_id.get()

                header = parent.export()
                # Check that the exported header contains the expected object fields.
                exported_span = SpanComponentsV3.from_str(header)
                if parent_header_format == "span_components_object_id":
                    assert exported_span.object_id is not None
                else:
                    assert exported_span.compute_object_metadata_args is not None
                parent_span_components = SpanComponentsV3(
                    object_type=parent_object_type,
                    object_id=parent_object.id,
                    row_id=exported_span.row_id,
                    span_id=exported_span.span_id,
                    root_span_id=exported_span.root_span_id,
                )
                return parent_span_components, header

            if has_parent_span:
                assert parent_header_format in ("span_components_object_id", "span_components_object_metadata")
                with parent_object.start_span("root") as root:
                    with root.start_span(name="subspan") as subspan:
                        span_components, parent_header = _export_parent(subspan)
            else:
                if parent_header_format in (
                    "span_components_object_metadata",
                    "span_components_object_id",
                ):
                    span_components, parent_header = _export_parent(parent_object)
                elif parent_header_format == "object_name_prefix":
                    assert parent_object_type == SpanObjectTypeV3.PROJECT_LOGS
                    parent_header = f"project_name:{parent_object.project.name}"
                    span_components = SpanComponentsV3(
                        object_type=parent_object_type,
                        object_id=parent_object.id,
                    )
                elif parent_header_format == "object_id_prefix":
                    object_type_prefix = (
                        "experiment"
                        if parent_object_type == SpanObjectTypeV3.EXPERIMENT
                        else "playground"
                        if parent_object_type == SpanObjectTypeV3.PLAYGROUND_LOGS
                        else "project"
                    )
                    parent_header = f"{object_type_prefix}_id:{parent_object.id}"
                    span_components = SpanComponentsV3(
                        object_type=parent_object_type,
                        object_id=parent_object.id,
                    )
                else:
                    raise ValueError(f"Invalid parent_header_format: {parent_header_format}")

        assert span_components is not None
        assert span_components.object_id is not None
        assert parent_header is not None
        return (span_components, parent_header)

    @parameterized.expand(
        make_otel_test_parameters(),
        name_func=otel_parameterized_name_func,
    )
    def test_otel_api_key(self, *args):
        protocol, parent_object_type, has_parent_span, parent_header_format = args

        project_name = f"otel_api_key_project_{protocol}"

        parent_span_components, parent_header = self._create_parent(
            project_name,
            parent_object_type,
            has_parent_span,
            parent_header_format,
        )

        self._checked_otel_request(
            "Bogus API Key",
            "bogus",
            parent_span_components,
            parent_header,
            *args,
            expect_error=True,
        )

        self._checked_otel_request(
            self.org_api_key,
            "valid",
            parent_span_components,
            parent_header,
            *args,
            expect_error=False,
        )

    @parameterized.expand(
        make_otel_test_parameters(),
        name_func=otel_parameterized_name_func,
    )
    def test_otel_permissions(self, *args):
        protocol, parent_object_type, has_parent_span, parent_header_format = args

        project_name = f"otel_permissions_project_{protocol}"

        parent_span_components, parent_header = self._create_parent(
            project_name,
            parent_object_type,
            has_parent_span,
            parent_header_format,
        )

        # A user with the org api key should be able to send traces.
        self._checked_otel_request(
            self.org_api_key,
            "org-trace",
            parent_span_components,
            parent_header,
            *args,
        )

        # Invalid data should not be accepted.
        self._checked_otel_request(
            self.org_api_key,
            "org-trace-invalid",
            parent_span_components,
            parent_header,
            *args,
            send_invalid_trace=True,
            expect_error=True,
        )

        # A user with no update permissions on the parent object should not be able to send traces.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self._checked_otel_request(
            user_api_key,
            "user-trace-unauthorized",
            parent_span_components,
            parent_header,
            *args,
            expect_error=True,
        )

        # But if we grant them an update ACL, they should be able to send traces.
        def _grant_acl(
            acl_object_type,
            object_id,
            user_id,
            permission,
        ):
            acl_obj = self._checked_request(
                "post",
                self.org_api_key,
                make_url("acl"),
                json=dict(
                    object_type=acl_object_type,
                    object_id=parent_span_components.object_id,
                    user_id=user_id,
                    permission=permission,
                ),
            ).json()
            self.assertEqual(acl_obj["object_type"], acl_object_type)
            self.assertEqual(acl_obj["object_id"], parent_span_components.object_id)
            self.assertEqual(acl_obj["user_id"], user_id)
            self.assertEqual(acl_obj["permission"], permission)
            self.assertEqual(acl_obj["_object_org_id"], self.org_id)

        _grant_acl(
            "experiment"
            if parent_object_type == SpanObjectTypeV3.EXPERIMENT
            else "project_log"
            if parent_object_type == SpanObjectTypeV3.PROJECT_LOGS
            else "prompt_session",
            parent_span_components.object_id,
            user_id,
            "update",
        )

        # If the user is providing object metadata or object name instead of an ID,
        # they also need read permissions on the parent object for the ID lookup.
        if parent_header_format in ("object_name_prefix", "span_components_object_metadata"):
            _grant_acl(
                "experiment" if parent_object_type == SpanObjectTypeV3.EXPERIMENT else "project",
                parent_span_components.object_id,
                user_id,
                "read",
            )

        self._checked_otel_request(
            user_api_key,
            "user-trace",
            parent_span_components,
            parent_header,
            *args,
        )

    @parameterized.expand(
        make_otel_test_parameters(),
        name_func=otel_parameterized_name_func,
    )
    def test_otel_zero_byte_parent_span_id(self, *args):
        protocol, parent_object_type, has_parent_span, parent_header_format = args

        project_name = f"otel_zero_byte_parent_project_{protocol}"

        parent_span_components, parent_header = self._create_parent(
            project_name,
            parent_object_type,
            has_parent_span,
            parent_header_format,
        )

        self._checked_otel_request(
            self.org_api_key,
            "zero-byte-parent",
            parent_span_components,
            parent_header,
            protocol,
            parent_object_type,
            has_parent_span,
            parent_header_format,
            use_zero_byte_parent_span_id_for_root_span=True,
        )


class DummyPlaygroundObject:
    def __init__(self, prompt_session_id, project):
        self.id = prompt_session_id
        self.project = project
        self._lazy_id = LazyValue(lambda: self.id, use_mutex=False)

    def export(self):
        return SpanComponentsV3(object_type=SpanObjectTypeV3.PLAYGROUND_LOGS, object_id=self.id).to_str()

    def start_span(self, *args, **kwargs):
        raise unittest.SkipTest("DummyPlaygroundObject does not support start_span")


class TestOTelClient(BraintrustAppTestBase):
    def test_otel_record_exception(self):
        project_id, provider, tracer = self._setup_otel_tracer()

        spans = _get_spans(self.org_api_key, project_id)
        assert not spans

        # assert we catch exceptions
        with tracer.start_as_current_span("root") as span:
            span.set_attribute("k1", "v1")
            try:
                raise Exception("oh-no-my-error")
            except Exception as e:
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR, str(e)))
        provider.force_flush()
        span = _wait_for_one_span(self.org_api_key, project_id)
        assert span["error"]
        assert isinstance(span["error"], str)
        assert "Exception" in span["error"]
        assert "oh-no-my-error" in span["error"]

    def test_otel_error_status(self):
        project_id, provider, tracer = self._setup_otel_tracer()

        spans = _get_spans(self.org_api_key, project_id)
        assert not spans
        # assert we catch status errors
        with tracer.start_as_current_span("root") as span:
            span.set_attribute("k1", "v1")
            span.set_status(Status(StatusCode.ERROR, "status-err-msg"))
        provider.force_flush()
        span = _wait_for_one_span(self.org_api_key, project_id)
        assert span["error"]
        assert isinstance(span["error"], str)
        assert "status-err-msg" in span["error"]

    def test_otel_error_status_ok(self):
        project_id, provider, tracer = self._setup_otel_tracer()
        spans = _get_spans(self.org_api_key, project_id)
        assert not spans
        # assert we catch status errors
        with tracer.start_as_current_span("root") as span:
            span.set_status(Status(StatusCode.OK))
        provider.force_flush()

        span = _wait_for_one_span(self.org_api_key, project_id)
        assert not span["error"]

    def test_otel_error_status_no_description(self):
        project_id, provider, tracer = self._setup_otel_tracer()
        spans = _get_spans(self.org_api_key, project_id)
        assert not spans
        # assert we catch status errors
        with tracer.start_as_current_span("root") as span:
            span.set_status(Status(StatusCode.ERROR))
        provider.force_flush()

        span = _wait_for_one_span(self.org_api_key, project_id)
        assert span["error"]
        assert "error" in span["error"]

    def test_error_status_with_multiple_exceptions(self):
        project_id, provider, tracer = self._setup_otel_tracer()
        # assert we can catch lots of errors
        with tracer.start_as_current_span("root") as span:
            span.set_attribute("k1", "v1")
            span.record_exception(Exception("e-one"))
            span.record_exception(Exception("e-two"))
            span.record_exception(Exception("e-three"))
            span.add_event(
                "unrelated-test-event",
                attributes={
                    "k1": "1",
                    "k2": 2,
                    "k3": [3, 4, 5],
                    "k4": False,
                },
            )
        provider.force_flush()

        span = _wait_for_one_span(self.org_api_key, project_id)

        assert span["error"]
        assert isinstance(span["error"], list)
        assert len(span["error"]) == 3
        assert "one" in span["error"][0]
        assert "two" in span["error"][1]
        assert "three" in span["error"][2]

    def test_otel_client_basics(self):
        project_name = "otel-basics"
        project_id, provider, tracer = self._setup_otel_tracer()

        spans = _get_spans(self.org_api_key, project_id)
        assert not spans

        # Create some opentelemetry spans
        start = time.time()
        with tracer.start_as_current_span("name1") as span:
            span.set_attribute("k1", "v1")
        end = time.time()
        provider.force_flush()

        span = _wait_for_one_span(self.org_api_key, project_id)

        assert span["org_id"] == self.org_id
        assert span["project_id"] == project_id
        metrics = span["metrics"]
        assert start < metrics["start"] < metrics["end"] < end
        attrs = span["span_attributes"]
        assert attrs["name"] == "name1"
        assert span["span_id"]
        assert span["root_span_id"] is not None
        assert span["is_root"] is True
        assert span["metadata"]["k1"] == "v1"
        assert span.get("error") is None

    def test_otel_tags(self):
        project_id, provider, tracer = self._setup_otel_tracer()

        spans = _get_spans(self.org_api_key, project_id)
        assert not spans

        # Create a span with tags
        with tracer.start_as_current_span("tagged_span") as span:
            span.set_attribute("braintrust.tags", ["test", "integration", "otel"])
        provider.force_flush()

        span = _wait_for_one_span(self.org_api_key, project_id)

        # Verify tags are present and correct
        assert "tags" in span
        assert span["tags"] == ["test", "integration", "otel"]
        assert span["span_attributes"]["name"] == "tagged_span"

    def _setup_otel_tracer(self):
        """sets up an otel tracer with the parent in the header"""
        project_name = self._testMethodName
        logger = braintrust.init_logger(project=project_name)
        project_id = logger.project.id
        parent_str = _project_parent_str(project_id)
        provider, tracer = _setup_otel_tracer(self.org_api_key, project_name, parent_str)
        return project_id, provider, tracer


class TestOTelClientSpanParent(BraintrustAppTestBase):
    def test_otel_parent_on_span(self):
        """Test basic span-level parent functionality"""
        project_id, provider, tracer = self._setup_otel_tracer()
        spans = _get_spans(self.org_api_key, project_id)
        assert not spans

        parent = _project_parent_str(project_id)

        with tracer.start_as_current_span("root") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, parent)
            span.set_attribute("k1", "v1")
        provider.force_flush()

        span = _wait_for_one_span(self.org_api_key, project_id)
        assert span["metadata"]["k1"] == "v1"

    def test_otel_no_parent_fails(self):
        """Test that spans without any parent (header or attribute) are rejected"""
        # Setup tracer without parent header
        project_name = self._testMethodName
        provider, tracer = _setup_otel_tracer(self.org_api_key, project_name)

        # Create span without parent attribute
        with tracer.start_as_current_span("orphan_span") as span:
            span.set_attribute("k1", "v1")
        provider.force_flush()

        # Wait briefly and check that no spans were created
        time.sleep(0.5)
        # We can't easily check if spans were rejected without access to logs,
        # but we can verify no spans made it to the database
        spans = _get_spans(self.org_api_key, self._get_or_create_project().id)
        assert len(spans) == 0, "Expected no spans to be created without parent"

    def test_otel_mixed_parent_success_and_failure(self):
        """Test trace with some spans having valid parents and others failing ACL"""
        project_id, provider, tracer = self._setup_otel_tracer()

        # Create a second project that this user doesn't have access to
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Create another project for testing unauthorized access
        logger2 = braintrust.init_logger(project=f"{self._testMethodName}_unauthorized")
        project_id_2 = logger2.project.id

        valid_parent = _project_parent_str(project_id)
        invalid_parent = _project_parent_str(project_id_2)

        # Create spans with mixed parents using the restricted user's API key
        provider_user, tracer_user = _setup_otel_tracer(user_api_key, self._testMethodName)

        # This span should succeed (if we grant access to first project)
        with tracer_user.start_as_current_span("valid_parent_span") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, valid_parent)
            span.set_attribute("test", "valid")

        # This span should fail due to ACL
        with tracer_user.start_as_current_span("invalid_parent_span") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, invalid_parent)
            span.set_attribute("test", "invalid")

        provider_user.force_flush()

        # Without ACL permissions, no spans should be created
        time.sleep(0.5)
        spans = _get_spans(self.org_api_key, project_id)
        assert len(spans) == 0, "Expected no spans without ACL permissions"

        # Grant ACL for first project only
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/acl",
            json=dict(
                object_type="project_log",
                object_id=project_id,
                user_id=user_id,
                permission="update",
            ),
            headers=get_request_headers(self.org_api_key),
        )

        # Now try again - only the valid parent span should succeed
        with tracer_user.start_as_current_span("valid_parent_span_2") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, valid_parent)
            span.set_attribute("test", "valid_retry")

        with tracer_user.start_as_current_span("invalid_parent_span_2") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, invalid_parent)
            span.set_attribute("test", "invalid_retry")

        provider_user.force_flush()

        # Should have exactly one span now (the valid one)
        span = _wait_for_one_span(self.org_api_key, project_id)
        assert span["metadata"]["test"] == "valid_retry"

    def test_otel_mixed_projects_and_experiments(self):
        """Test trace with spans targeting different object types (projects and experiments)"""
        project_id, provider, tracer = self._setup_otel_tracer()

        # Create an experiment in the same project
        experiment = braintrust.init(project=self._testMethodName)
        experiment_id = experiment.id

        project_parent = _project_parent_str(project_id)
        experiment_parent = SpanComponentsV3(
            object_type=SpanObjectTypeV3.EXPERIMENT,
            object_id=experiment_id,
        ).to_str()

        # Create spans for both project and experiment
        with tracer.start_as_current_span("project_span") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, project_parent)
            span.set_attribute("target", "project")

        with tracer.start_as_current_span("experiment_span") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, experiment_parent)
            span.set_attribute("target", "experiment")

        provider.force_flush()

        # Check project spans
        project_spans = _get_spans(self.org_api_key, project_id)
        project_span = next(s for s in project_spans if s["metadata"]["target"] == "project")
        assert project_span["project_id"] == project_id
        assert project_span["log_id"] == "g"  # project logs
        # Project spans don't have experiment_id field

        # Check experiment spans
        experiment_spans = _get_spans_for_experiment(self.org_api_key, experiment_id)
        experiment_span = next(s for s in experiment_spans if s["metadata"]["target"] == "experiment")
        assert experiment_span["experiment_id"] == experiment_id
        assert experiment_span["project_id"] == project_id  # Should inherit project_id
        # Experiment spans don't have log_id field (or it's different from "g")

    def test_otel_header_fallback_to_span_attribute(self):
        """Test that header parent is used as fallback when span doesn't have parent attribute"""
        project_id, provider, tracer = self._setup_otel_tracer()
        parent = _project_parent_str(project_id)

        # Create spans, some with and some without the parent attribute
        with tracer.start_as_current_span("span_with_attribute") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, parent)
            span.set_attribute("source", "attribute")

        with tracer.start_as_current_span("span_without_attribute") as span:
            # This should use the header parent as fallback
            span.set_attribute("source", "header_fallback")

        provider.force_flush()

        spans = _wait_for_spans(self.org_api_key, project_id)
        assert len(spans) == 2

        # Both spans should end up in the same project
        for span in spans:
            assert span["project_id"] == project_id
            assert span["log_id"] == "g"

    def test_otel_span_attribute_overrides_header(self):
        """Test that span-level parent attribute overrides header parent"""
        project_id, provider, tracer = self._setup_otel_tracer()

        # Create a second project
        logger2 = braintrust.init_logger(project=f"{self._testMethodName}_override")
        project_id_2 = logger2.project.id

        header_parent = _project_parent_str(project_id)  # Header points to first project
        span_parent = _project_parent_str(project_id_2)  # Span attribute points to second project

        with tracer.start_as_current_span("override_span") as span:
            span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, span_parent)
            span.set_attribute("test", "override")

        provider.force_flush()

        # Span should end up in second project (from attribute), not first (from header)
        spans_project_1 = _get_spans(self.org_api_key, project_id)
        spans_project_2 = _get_spans(self.org_api_key, project_id_2)

        assert len(spans_project_1) == 0, "Span should not be in header parent project"
        assert len(spans_project_2) == 1, "Span should be in attribute parent project"
        assert spans_project_2[0]["metadata"]["test"] == "override"

    def test_otel_invalid_parent_format(self):
        """Test handling of invalid parent format in span attribute"""
        project_id, provider, tracer = self._setup_otel_tracer()

        # Try various invalid parent formats - only some will fall back to header
        # Others might be rejected by the server due to permission checks
        valid_fallback_parents = [
            "",  # Empty string should fall back
            None,  # None should fall back
        ]

        for i, invalid_parent in enumerate(valid_fallback_parents):
            with tracer.start_as_current_span(f"fallback_span_{i}") as span:
                if invalid_parent is not None:
                    span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, invalid_parent)
                span.set_attribute("test", f"fallback_{i}")

        provider.force_flush()

        # Should fall back to header parent for these spans
        spans = _wait_for_spans(self.org_api_key, project_id)
        assert len(spans) >= len(valid_fallback_parents)

        for span in spans:
            assert span["project_id"] == project_id
            assert span["log_id"] == "g"

    def test_otel_complex_trace_hierarchy(self):
        """Test complex trace with nested spans and different parents"""
        project_id, provider, tracer = self._setup_otel_tracer()

        # Create experiment for some spans
        experiment = braintrust.init(project=self._testMethodName)
        experiment_id = experiment.id

        project_parent = _project_parent_str(project_id)
        experiment_parent = SpanComponentsV3(
            object_type=SpanObjectTypeV3.EXPERIMENT,
            object_id=experiment_id,
        ).to_str()

        # Create a complex trace with nested spans
        with tracer.start_as_current_span("root_project_span") as root_span:
            root_span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, project_parent)
            root_span.set_attribute("level", "root")
            root_span.set_attribute("target", "project")

            with tracer.start_as_current_span("child_experiment_span") as child_span:
                child_span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, experiment_parent)
                child_span.set_attribute("level", "child")
                child_span.set_attribute("target", "experiment")

                with tracer.start_as_current_span("grandchild_project_span") as grandchild_span:
                    grandchild_span.set_attribute(BRAINTRUST_PARENT_ATTRIBUTE, project_parent)
                    grandchild_span.set_attribute("level", "grandchild")
                    grandchild_span.set_attribute("target", "project")

        provider.force_flush()

        # Check spans ended up in correct places
        project_spans = _get_spans(self.org_api_key, project_id)
        experiment_spans = _get_spans_for_experiment(self.org_api_key, experiment_id)

        # Should have 2 project spans and 1 experiment span
        project_span_targets = [s["metadata"]["target"] for s in project_spans if s["metadata"]["target"] == "project"]
        experiment_span_targets = [
            s["metadata"]["target"] for s in experiment_spans if s["metadata"]["target"] == "experiment"
        ]

        assert len(project_span_targets) == 2, f"Expected 2 project spans, got {len(project_span_targets)}"
        assert len(experiment_span_targets) == 1, f"Expected 1 experiment span, got {len(experiment_span_targets)}"

    def _setup_otel_tracer(self):
        project_name = self._testMethodName
        logger = braintrust.init_logger(project=project_name)
        project_id = logger.project.id
        parent_str = _project_parent_str(project_id)
        provider, tracer = _setup_otel_tracer(self.org_api_key, project_name, parent_str)
        return project_id, provider, tracer

    def _get_or_create_project(self):
        """Helper to get or create a project for the current test"""
        logger = braintrust.init_logger(project=self._testMethodName)
        return logger.project


def _project_parent_str(project_id):
    return SpanComponentsV3(
        object_type=SpanObjectTypeV3.PROJECT_LOGS,
        object_id=project_id,
    ).to_str()


def _setup_otel_tracer(api_key, project_name, parent_header=None):
    """
    Returns a tuple of (provider, tracer) for a test setup.
    """
    # Set up a non-global tracer for testing
    provider = TracerProvider()

    # Configure the BraintrustSpanProcessor
    processor = BraintrustSpanProcessor(
        api_key=api_key,
        parent=parent_header,
        api_url=LOCAL_API_URL,
    )

    provider.add_span_processor(processor)
    return provider, provider.get_tracer(project_name)


def _wait_for_one_span(org_api_key, project_id):
    spans = _wait_for_spans(org_api_key, project_id)
    assert len(spans) == 1
    return spans[0]


def _wait_for_spans(org_api_key, project_id):
    # It seems to take a brief moment for the spans to be available, so this is a little
    # hack to try to be as fast as possible. Returns the first set of spans it finds.
    attempts = 50
    sleep_seconds = 0.1
    spans = []
    for _ in range(attempts):
        spans = _get_spans(org_api_key, project_id)
        if spans:
            return spans
        time.sleep(sleep_seconds)
    return spans


def _get_spans(org_api_key, project_id):
    q = f"select: * from: project_logs('{project_id}') spans"

    ret = requests.post(
        f"{LOCAL_API_URL}/btql",
        json=dict(
            query=q,
            use_columnstore=False,
            use_brainstore=True,
        ),
        headers=get_request_headers(org_api_key),
    )
    assert ret.status_code == 200
    return ret.json()["data"]


def _get_spans_for_experiment(org_api_key, experiment_id):
    q = f"select: * from: experiment('{experiment_id}') spans"

    ret = requests.post(
        f"{LOCAL_API_URL}/btql",
        json=dict(
            query=q,
            use_columnstore=False,
            use_brainstore=True,
        ),
        headers=get_request_headers(org_api_key),
    )
    assert ret.status_code == 200
    return ret.json()["data"]
