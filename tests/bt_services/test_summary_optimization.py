import time
from uuid import uuid4

import braintrust
import redis
import requests
from braintrust.db_fields import IS_MERGE_FIELD, OBJECT_DELETE_FIELD
from braintrust.span_identifier_v3 import SpanComponentsV3, SpanObjectTypeV3
from braintrust_local.api_db_util import log_raw
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, make_v1_url
from tests.bt_services.experiment_test_base import ExperimentTestBase


class ExperimentSummaryOptimizationTest(ExperimentTestBase):
    def setUp(self):
        super().setUp()

    def test_missing_root_span(self):
        experiment = braintrust.init(project="p")

        # Create a normal span
        with experiment.start_span(id="1") as root_span:
            root_span.log(input="a", output="out", scores=dict(score0=0.0))
            with root_span.start_span() as llm_span:
                llm_span.log(input="a", output="out")

        fake_span_id = str(uuid4())
        span_parents = SpanComponentsV3(
            object_type=SpanObjectTypeV3.EXPERIMENT,
            object_id=experiment.id,
            row_id="2",
            span_id=fake_span_id,
            root_span_id=fake_span_id,
        )
        span_parents_str = span_parents.to_str()

        with experiment.start_span(parent=span_parents_str) as sub_span:
            sub_span.log(input="a", output="out", scores=dict(score0=0.0))

        summary = experiment.summarize()
        self.assertIsNotNone(summary.experiment_id)  # check that the property exists
        self.assertEqual(summary.experiment_id, experiment.id)
        self.assertEqual(summary.project_id, experiment.project.id)

        # Next, query the data via btql in summary mode
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"""select: * | from: experiment('{experiment.id}') summary""",
                fmt="json",
            ),
        )

    @parameterized.expand(
        [
            ("experiment",),
            ("project_logs",),
        ]
    )
    def test_empty_summary(self, object_type: str):
        if object_type == "project_logs":
            root = braintrust.init_logger(project="p")
            root_id = root.project.id
            from_name = f"project_logs('{root_id}')"
        else:
            root = braintrust.init_experiment(project="p")
            root_id = root.id
            from_name = f"experiment('{root_id}')"

        queries = [f"select: * | from: {from_name} summary", f"infer: * | from: {from_name}"]

        for query in queries:
            response = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json=dict(
                    query=query,
                    fmt="json",
                ),
            ).json()
            data = response["data"]
            self.assertEqual(len(data), 0)

    def test_negative_tags(self):
        logger = braintrust.init_logger(project="p")
        with logger.start_span() as span:
            span.log(input="a", output="out", tags=["tag1", "tag2"])
            with span.start_span() as child_span:
                child_span.log(input="a", output="out")

        logger.flush()

        project_id = logger.project.id
        rows = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * | from: project_logs('{project_id}') summary | filter: tags not includes ['tag1']",
                fmt="json",
            ),
        ).json()["data"]
        self.assertEqual(len(rows), 0)

        rows = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * | from: project_logs('{project_id}') summary | filter: tags includes ['tag1']",
                fmt="json",
            ),
        ).json()["data"]
        self.assertEqual(len(rows), 1)

    @parameterized.expand(
        [
            (object_type, num_rows)
            for object_type in ["experiment", "project_logs"]
            # 1,000 is the threshold (MAX_ID_FETCH_SIZE) after which we just
            # fetch all comments.
            for num_rows in [100, 1100]
        ]
    )
    def test_comments(self, object_type: str, num_rows: int):
        NUM_COMMENTS = 10
        # Create a 1,000 row object and put 10 comments on it
        if object_type == "project_logs":
            root = braintrust.init_logger(project="p")
            root_id = root.project.id
            from_name = f"project_logs('{root_id}')"
        else:
            root = braintrust.init_experiment(project="p")
            root_id = root.id
            from_name = f"experiment('{root_id}')"

        for i in range(num_rows):
            with root.start_span() as span:
                span.log(input="a", output="out")

                if i < NUM_COMMENTS:
                    span.log_feedback(comment="heyyyy")

        root.flush()

        # Query the summary data
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * | from: {from_name} summary | limit: {num_rows}",
                fmt="json",
                disable_limit=True,
                _testing_only_skip_limit_checks=True,
            ),
        ).json()
        data = response["data"]

        self.assertEqual(len(data), num_rows)

        has_comments = 0
        for row in data:
            if row.get("comments"):
                has_comments += len(row["comments"])

        self.assertEqual(has_comments, NUM_COMMENTS)

    @parameterized.expand([(object_type) for object_type in ["experiment"]])
    def test_comments_filtering(self, object_type: str):
        root = braintrust.init_experiment(project="p")
        root_id = root.id
        from_name = f"experiment('{root_id}')"
        NUM_COMMENTS = 10

        row_to_delete = None
        with root.start_span() as span:
            span.log(input="a", output="out")

            for j in range(NUM_COMMENTS):
                span.log_feedback(comment=f"{j}")
                root.flush()

        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * | from: {from_name} summary | limit: 1",
                fmt="json",
                disable_limit=True,
            ),
        ).json()
        data = response["data"]

        self.assertEqual(len(data), 1)

        if data[0].get("comments"):
            # Verify comments are ordered by creation date in descending order
            for i in range(len(data[0]["comments"]) - 1):
                current_comment = data[0]["comments"][i]
                next_comment = data[0]["comments"][i + 1]
                self.assertGreaterEqual(
                    current_comment["created"],
                    next_comment["created"],
                    f"Comment {i} should be more recent than or equal to comment {i + 1}",
                )
            row_to_delete = data[0]["comments"][0]

        log_raw(
            dict(
                id=row_to_delete["id"],
                origin=row_to_delete["origin"],
                experiment_id=root_id,
                comment={},
                created=row_to_delete["created"],
                **{
                    OBJECT_DELETE_FIELD: True,
                    IS_MERGE_FIELD: True,
                },
            ),
        )

        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: * | from: {from_name} summary | limit: 1",
                fmt="json",
                disable_limit=True,
            ),
        ).json()
        data = response["data"]

        self.assertEqual(len(data), 1)

        comments = []
        if data[0].get("comments"):
            for comment in data[0]["comments"]:
                comments.append(comment["comment"]["text"])

        self.assertEqual(len(comments), NUM_COMMENTS - 1)
