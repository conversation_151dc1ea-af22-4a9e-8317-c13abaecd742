import threading
from uuid import uuid4

import braintrust
from braintrust.span_identifier_v3 import SpanComponentsV3
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


class DistributedTracingTest(BraintrustAppTestBase):
    def test_basic(self):
        # `braintrust._internal_with_custom_background_logger` overrides the
        # background logger on a per-thread basis. So we can simulate
        # distributed logging by overriding the logger in two different threads,
        # and enable synchronous flush on each one.

        experiment_id = None
        root_span_id = None
        subspan_id = None
        exported_span = None
        exported_span_ready = threading.Event()
        subspan_published = threading.Event()

        def thread_one():
            nonlocal experiment_id, root_span_id, exported_span

            with braintrust._internal_with_custom_background_logger() as logger:
                logger.sync_flush = True

                experiment = braintrust.init("p")
                experiment_id = experiment.id
                with experiment.start_span() as root_span:
                    root_span_id = root_span.id
                    root_span.log(input="foo", output="bar")
                    exported_span = root_span.export()
                    exported_span_ready.set()

                subspan_published.wait(timeout=10.0)
                logger.flush()

        def thread_two():
            nonlocal subspan_id

            with braintrust._internal_with_custom_background_logger() as logger:
                logger.sync_flush = True

                exported_span_ready.wait(timeout=10.0)
                with braintrust.start_span(parent=exported_span) as subspan:
                    subspan_id = subspan.id
                    subspan.log(input="baz", output="qux")
                logger.flush()
                subspan_published.set()

        threads = [threading.Thread(target=thread_one), threading.Thread(target=thread_two)]
        for t in threads:
            t.start()
        for t in threads:
            t.join()

        rows = get_object_json("experiment", experiment_id)
        id_to_row = {r["id"]: r for r in rows}
        self.assertEqual(len(id_to_row), 2)
        root_row = id_to_row[root_span_id]
        subspan_row = id_to_row[subspan_id]
        self.assertEqual(root_row["input"], "foo")
        self.assertEqual(root_row["output"], "bar")
        self.assertEqual(root_row["span_id"], root_row["root_span_id"])
        self.assertEqual(subspan_row["input"], "baz")
        self.assertEqual(subspan_row["output"], "qux")
        self.assertNotEqual(subspan_row["span_id"], subspan_row["root_span_id"])
        self.assertEqual(subspan_row["root_span_id"], root_row["root_span_id"])
        self.assertEqual(subspan_row["span_parents"], [root_row["span_id"]])

    def test_logger_export_nonblocking(self):
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            l = braintrust.init_logger(project_id="invalid_id")
            l.log(input="0")
            parent0 = l.export()
            with l.start_span() as span:
                span.log(input="1")
                parent1 = span.export()
            with l.start_span(parent=parent0) as span:
                span.log(input="2")
            with l.start_span(parent=parent1) as span:
                span.log(input="3")

            with self.assertRaises(Exception):
                logger.flush()

    def test_logger_export_use_id_after_flushing(self):
        project_name = str(uuid4())
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            l = braintrust.init_logger(project=project_name)
            l.log(input="0")
            export_str = l.export()
            components = SpanComponentsV3.from_str(export_str)
            self.assertIsNone(components.object_id)
            self.assertEqual(components.compute_object_metadata_args, dict(project_name=project_name, project_id=None))
            logger.flush()
            export_str = l.export()
            components = SpanComponentsV3.from_str(export_str)
            self.assertIsNotNone(components.object_id)
            self.assertIsNone(components.compute_object_metadata_args)

    def test_resolve_span_components_to_object_id(self):
        project_name = str(uuid4())
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            l = braintrust.init_logger(project=project_name)
            l.log(input="0")
            # Check resolving before flush.
            export_str = l.export()
            components = SpanComponentsV3.from_str(export_str)
            self.assertIsNone(components.object_id)
            self.assertEqual(braintrust.span_components_to_object_id(components), l.id)
            logger.flush()
            # Check resolving after flush.
            export_str = l.export()
            components = SpanComponentsV3.from_str(export_str)
            self.assertIsNotNone(components.object_id)
            self.assertEqual(braintrust.span_components_to_object_id(components), l.id)

    def test_propagated_event(self):
        experiment = braintrust.init("p")

        with experiment.start_span(name="a", propagated_event=dict(metrics=dict(foo=0.1))) as span_a:
            span_a_slug = span_a.export()

            with span_a.start_span(name="b") as span_b:
                span_b_slug = span_b.export()

            with span_a.start_span(name="c", propagated_event=dict(metrics=dict(foo=0.2))) as span_c:
                pass

        with braintrust.start_span(name="d", parent=span_a_slug) as span_d:
            with braintrust.start_span(name="e") as span_e:
                with braintrust.start_span(name="f", parent=span_b_slug, propagated_event=dict()) as span_f:
                    pass

        braintrust.flush()

        rows = get_object_json("experiment", experiment.id)
        name_to_row = {r["span_attributes"]["name"]: r for r in rows}
        name_to_props = {
            r["span_attributes"]["name"]: {
                "span_parent_id": r["span_parents"][0] if r["span_parents"] else None,
                "root_span_id": r["root_span_id"],
                "metrics_keys": set(r["metrics"].keys()),
                "metrics_foo": r["metrics"].get("foo"),
            }
            for r in rows
        }
        get_span_id = lambda row_name: name_to_row[row_name]["span_id"]
        expected_metrics_keys = {"start", "end", "foo"}
        self.assertEqual(
            name_to_props,
            {
                "a": {
                    "span_parent_id": None,
                    "root_span_id": get_span_id("a"),
                    "metrics_keys": expected_metrics_keys,
                    "metrics_foo": 0.1,
                },
                "b": {
                    "span_parent_id": get_span_id("a"),
                    "root_span_id": get_span_id("a"),
                    "metrics_keys": expected_metrics_keys,
                    "metrics_foo": 0.1,
                },
                "c": {
                    "span_parent_id": get_span_id("a"),
                    "root_span_id": get_span_id("a"),
                    "metrics_keys": expected_metrics_keys,
                    "metrics_foo": 0.2,
                },
                "d": {
                    "span_parent_id": get_span_id("a"),
                    "root_span_id": get_span_id("a"),
                    "metrics_keys": expected_metrics_keys,
                    "metrics_foo": 0.1,
                },
                "e": {
                    "span_parent_id": get_span_id("d"),
                    "root_span_id": get_span_id("a"),
                    "metrics_keys": expected_metrics_keys,
                    "metrics_foo": 0.1,
                },
                "f": {
                    "span_parent_id": get_span_id("b"),
                    "root_span_id": get_span_id("a"),
                    "metrics_keys": {"start", "end"},
                    "metrics_foo": None,
                },
            },
        )

    def test_propagated_span_attributes(self):
        experiment = braintrust.init("p")
        with experiment.start_span(name="a", propagated_event=dict(span_attributes=dict(foo="bar"))) as span_a:
            span_a_slug = span_a.export()

        # Should not raise.
        with braintrust.start_span(name="b", parent=span_a_slug):
            pass

        braintrust.flush()

        rows = get_object_json("experiment", experiment.id)
        for r in rows:
            if r["span_attributes"]["name"] == "a" or r["span_attributes"]["name"] == "b":
                self.assertEqual(r["span_attributes"]["foo"], "bar")
