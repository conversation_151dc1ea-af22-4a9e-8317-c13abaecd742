from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from threading import Event

import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


@braintrust.traced
def foo(input):
    input[0] = "bar"
    return len(input[0])


@braintrust.traced
def long_running_function(began_event, resume_event):
    began_event.set()
    resume_event.wait()
    return "ok"


class TracedDecoratorTest(BraintrustAppTestBase):
    def test_log_mutable_input(self):
        experiment = braintrust.init("p")
        foo(["x"])
        experiment.flush()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(1, len(rows))
        self.assertEqual(rows[0]["input"], dict(input=["x"]))
        self.assertEqual(rows[0]["output"], 3)

    def test_log_input_before_running_function(self):
        experiment = braintrust.init("p")
        began_function_event = Event()
        resume_function_event = Event()
        with ThreadPoolExecutor(1) as executor:
            result = executor.submit(long_running_function, began_function_event, resume_function_event)
            began_function_event.wait()
            try:
                experiment.flush()
                rows = get_object_json("experiment", experiment.id)
                self.assertEqual(1, len(rows))
                self.assertEqual(len(rows[0]["input"]), 2)
                self.assertEqual(rows[0]["output"], None)
            finally:
                resume_function_event.set()
        experiment.flush()
        rows = get_object_json("experiment", experiment.id)
        self.assertEqual(1, len(rows))
        self.assertEqual(len(rows[0]["input"]), 2)
        self.assertEqual(rows[0]["output"], "ok")
