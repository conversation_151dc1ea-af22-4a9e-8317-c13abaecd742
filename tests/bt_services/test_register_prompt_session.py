from concurrent.futures import Thr<PERSON>PoolExecutor

import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


def getPromptSessionInfo(resp):
    return {k: resp[k] for k in ["name", "description", "user_id"]}


class RegisterPromptSessionTest(BraintrustAppTestBase):
    def test_basic(self):
        args = dict(
            project_name="foo",
            session_name="_register_prompt_session_test_basic",
        )
        resp = self.registerPromptSession(args)

        self.assertEqual(
            getPromptSessionInfo(resp), dict(name=args["session_name"], description=None, user_id=self.user_id)
        )

        # Performing exactly the same request should yield the same prompt
        # session.
        resp2 = self.registerPromptSession(args)
        self.assertEqual(resp, resp2)

    def test_concurrent_prompt_session_registration(self):
        NUM_PROMPT_SESSIONS = 100
        prompt_session_names = ["p0", "p1", "p2", "p3", "p4", "p5"]
        with ThreadPoolExecutor(NUM_PROMPT_SESSIONS) as executor:
            results = []
            for i in range(NUM_PROMPT_SESSIONS):
                results.append(
                    executor.submit(
                        self.registerPromptSession,
                        dict(
                            project_name="concurrent_registration",
                            session_name=prompt_session_names[i % len(prompt_session_names)],
                        ),
                    )
                )
            for r in results:
                r.result()

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select count(*)
                    from prompt_sessions
                    JOIN projects ON prompt_sessions.project_id = projects.id
                    where projects.name = %s
                    """,
                    ["concurrent_registration"],
                )
                actual_num_prompt_sessions = cursor.fetchone()[0]
        self.assertEqual(len(prompt_session_names), actual_num_prompt_sessions)
