import unittest

from braintrust.util import _urljoin, merge_dicts, merge_dicts_with_paths


class MergeDictsTest(unittest.TestCase):
    def test_basic(self):
        a = dict(
            x=10,
            y="hello",
            z=dict(
                a="yes",
                b="no",
                c=[1, 2, 3],
            ),
            n=dict(a=12),
        )
        b = dict(
            y="goodbye",
            q="new",
            z=dict(
                b="maybe",
                c=99,
                d="something",
            ),
            n=None,
        )
        merge_dicts(a, b)
        self.assertEqual(
            dict(
                x=10,
                y="goodbye",
                z=dict(
                    a="yes",
                    b="maybe",
                    c=99,
                    d="something",
                ),
                n=None,
                q="new",
            ),
            a,
        )

    def test_requires_dicts(self):
        a = dict(x=12, y=10)
        with self.assertRaises(ValueError):
            merge_dicts(a, 10)
        with self.assertRaises(ValueError):
            merge_dicts([1, 2, 3], a)

    def test_merge_paths(self):
        def ab():
            return dict(x=10, y="hello", z=dict(a="yes", b="no", c=[1, 2, 3],), n=dict(a=12),), dict(
                y="goodbye",
                q="new",
                z=dict(
                    b="maybe",
                    c=99,
                    d="something",
                ),
                n=None,
            )

        full_merge, b = ab()
        merge_dicts(full_merge, b)

        a, b = ab()
        merge_dicts_with_paths(a, b, (), [])
        self.assertEqual(a, full_merge)

        a, b = ab()
        merge_dicts_with_paths(a, b, (), set([("z",)]))
        self.assertEqual(a, dict(x=10, y="goodbye", z=dict(b="maybe", c=99, d="something"), n=None, q="new"))

        a, b = ab()
        a["a"] = dict(y=dict(a=10, b=20))
        b["a"] = dict(y=dict(a=20, c=30))
        merge_dicts_with_paths(a, b, (), set([("z",), ("a", "y")]))
        self.assertEqual(
            a,
            dict(
                x=10, y="goodbye", z=dict(b="maybe", c=99, d="something"), n=None, q="new", a=dict(y=dict(a=20, c=30))
            ),
        )

        a, b = ab()
        merge_dicts_with_paths(a, b, (), set([("z", "b")]))
        self.assertEqual(a, full_merge)


class UrlJoinTest(unittest.TestCase):
    def test_basic(self):
        self.assertEqual(_urljoin("/a", "/b", "/c"), "a/b/c")
        self.assertEqual(_urljoin("a", "b", "c"), "a/b/c")
        self.assertEqual(_urljoin("/a/", "/b/", "/c/"), "a/b/c/")
        self.assertEqual(_urljoin("a/", "b/", "c/"), "a/b/c/")
        self.assertEqual(_urljoin("", "a", "b", "c"), "a/b/c")
        self.assertEqual(_urljoin("a", "", "c"), "a/c")
        self.assertEqual(_urljoin("/", "a", "b", "c"), "a/b/c")
        self.assertEqual(_urljoin("http://example.com", "api", "v1"), "http://example.com/api/v1")
        self.assertEqual(_urljoin("http://example.com/", "/api/", "/v1/"), "http://example.com/api/v1/")
        self.assertEqual(_urljoin(), "")
        self.assertEqual(_urljoin("a"), "a")
