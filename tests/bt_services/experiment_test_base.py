import difflib
import json
from datetime import datetime

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase

BRAINSTORE_ONLY_KEYS = ["_pagination_key"]
JS_ONLY_KEYS = ["span_parents", "is_root", "experiment_id", "project_id", "context"]
DIFFERENT_KEYS = ["comparison_key"]

# Brainstore only projects these span attributes
SPAN_ATTRIBUTES = ["name", "type"]

# Brainstore (correctly) nullifies these if they're absent.
COALESCE_METRICS = ["cached"]


class ExperimentTestBase(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.maxDiff = None

    # We won't need this test forever, but for now, it's an important way for us to test that the
    # summary computations in brainstore return the same results as the javascript implementation.
    def verify_brainstore_summary(self, experiment):
        with_postgres = self.fetch_experiment_summary(experiment.id, use_brainstore=False)
        with_brainstore = self.fetch_experiment_summary(experiment.id, use_brainstore=True)

        try:
            self.assertEqual(with_postgres, with_brainstore)
        except AssertionError:
            postgres_formatted = json.dumps(with_postgres, indent=2, sort_keys=True).splitlines()
            brainstore_formatted = json.dumps(with_brainstore, indent=2, sort_keys=True).splitlines()
            diff = difflib.unified_diff(
                postgres_formatted, brainstore_formatted, fromfile="postgres", tofile="brainstore"
            )
            print("\nDifferences between Postgres and Brainstore outputs:")
            print("\n".join(diff))
            raise

    def fetch_experiment_summary(self, experiment_id, use_brainstore=False):
        args = dict(query=f"""select: * | from: experiment('{experiment_id}') summary""")
        if use_brainstore:
            args.update(self.brainstore_query_args())
        result = self.run_request("post", f"{LOCAL_API_URL}/btql", json=args).json()
        result.pop("realtime_state", None)
        result.pop("freshness_state", None)

        # Sort result['data'] by id
        result["data"].sort(key=lambda x: x["id"])

        # Remove some difficult to compare fields
        empty_scores = []
        for row in result["data"]:
            # Normalize timestamps by parsing and reformatting
            if "created" in row and row["created"]:
                dt = datetime.fromisoformat(row["created"].replace("Z", "+00:00"))
                row["created"] = dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-4] + "Z"  # Trim to 2 decimal places

            # Only brainstore has these
            for key in BRAINSTORE_ONLY_KEYS + JS_ONLY_KEYS + DIFFERENT_KEYS:
                if key in row:
                    row.pop(key)

            # strip span attributes to just these attributes
            row["span_attributes"] = {
                key: row["span_attributes"][key] for key in SPAN_ATTRIBUTES if key in row["span_attributes"]
            }

            for key in COALESCE_METRICS:
                if key in row["metrics"]:
                    row["metrics"][key] = row["metrics"][key] or 0

            # Round all the metrics to two decimal places
            for key in row["metrics"]:
                if isinstance(row["metrics"][key], float):
                    row["metrics"][key] = round(row["metrics"][key], 2)

            # If any of the fields are empty {}, normalize them to None
            for key in row:
                if row[key] == {} or row[key] == []:
                    row[key] = None

            # Strip None scores
            if "scores" in row and row["scores"]:
                for key in row["scores"]:
                    if row["scores"][key] is None:
                        empty_scores.append(key)
                for key in empty_scores:
                    row["scores"].pop(key)

        # Remove empty scores from the schema
        scores_schema = result["schema"]["items"]["properties"]["scores"]["properties"]
        for key in empty_scores:
            if key in scores_schema:
                scores_schema.pop(key)

        # Remove result['dialect'] and result['cursor']
        if "dialect" in result:
            result.pop("dialect")
        if "cursor" in result:
            result.pop("cursor")

        return result
