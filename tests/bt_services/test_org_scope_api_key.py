from uuid import uuid4

import braintrust

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase


class OrgScopeAPIKeyTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        # Create a new org with a new user, whose API key is scoped to this new
        # org.
        self.extra_org_id, self.extra_org_name = self.createOrg()
        self.extra_user_id, _, self.extra_org_api_key = self.createUserInOrg(self.extra_org_id)

        # Create a general (unscoped) API key for the original org's user. Add
        # the new user to the original org, and the original user to the new
        # org, so that both users are in both orgs, but only the original user
        # has an API key that lets them log into both.
        self.general_api_key = None
        self.general_api_key_name = str(uuid4())
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    f"""
                    SELECT create_api_key(users.auth_id, null, %s)
                    FROM users
                    WHERE id=%s""",
                    (
                        self.general_api_key_name,
                        self.user_id,
                    ),
                )
                result = cursor.fetchone()
                self.general_api_key = result[0]

                self.addUserToOrg(self.user_id, self.extra_org_id, cursor=cursor)
                self.addUserToOrg(self.extra_user_id, self.org_id, cursor=cursor)

    def test_no_login(self):
        # We should be able to log into the new org with the scoped API key
        braintrust.login(
            org_name=self.extra_org_name, app_url=LOCAL_APP_URL, api_key=self.extra_org_api_key, force_login=True
        )

        # And with the general API key, which is not scoped to any particular org
        braintrust.login(
            org_name=self.extra_org_name, app_url=LOCAL_APP_URL, api_key=self.general_api_key, force_login=True
        )

        # But we should not be able to log into the original org with the new API key
        with self.assertRaises(ValueError):
            braintrust.login(
                org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.extra_org_api_key, force_login=True
            )

    def test_no_secrets(self):
        # Create a secret named "original_org" in the original org
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO secrets.org_secrets (type, name, secret, org_id) VALUES (%s, %s, %s, %s)",
                    ("secret_test", "original_org", "original_org_secret", self.org_id),
                )

                cursor.execute(
                    "INSERT INTO secrets.org_secrets (type, name, secret, org_id) VALUES (%s, %s, %s, %s)",
                    ("secret_test", "new_org", "new_org_secret", self.extra_org_id),
                )

                conn.commit()

        # The general API key should be able to retrieve both secrets
        braintrust.login(org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.general_api_key, force_login=True)
        secrets = set(
            [
                s["name"]
                for s in braintrust.app_conn().post_json("/api/secret", {"types": ["secret_test"], "mode": "full"})
            ]
        )
        self.assertEqual(secrets, set(["original_org", "new_org"]))

        # But the scoped API key should only be able to retrieve the new org's
        # secret.
        braintrust.login(
            org_name=self.extra_org_name, app_url=LOCAL_APP_URL, api_key=self.extra_org_api_key, force_login=True
        )
        secrets = set(
            [
                s["name"]
                for s in braintrust.app_conn().post_json("/api/secret", {"types": ["secret_test"], "mode": "full"})
            ]
        )
        self.assertEqual(secrets, set(["new_org"]))
