import braintrust
import pydantic

project = braintrust.projects.create(name="scorer")


class Input(pydantic.BaseModel):
    output: str
    expected: str


def handler(output: str, expected: str) -> int:
    return 1 if output == expected else 0


project.scorers.create(
    name="Equality scorer",
    slug="equality-scorer",
    description="An equality scorer",
    parameters=Input,
    handler=handler,
)
