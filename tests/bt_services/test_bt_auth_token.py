import braintrust
import requests
from parameterized import parameterized

from tests.braintrust_app_test_base import TEST_ARGS, BraintrustAppTestBase, make_v1_url

MODES = ["standard", "bt-auth", "bt-auth-bogus"]
PARAMETERS = [(mode,) for mode in MODES]


class BTAuthTokenTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.proxy_url = braintrust.logger._state.proxy_url
        self.openai_base_url = f"{self.proxy_url}/v1"

    def tearDown(self):
        if TEST_ARGS.get("update"):
            requests.get(f"{self.proxy_url}/proxy/dump-cache")
        super().tearDown()

    def _get_request_headers(self, mode):
        if mode == "standard":
            return dict(Authorization=f"Bearer {self.org_api_key}")
        elif mode == "bt-auth":
            # Certain endpoints, like /summarize, fail without any Authorization header. There are no
            # practical cases where we'll send x-bt-auth-token without one, so I think it's fine to test
            # the "null" behavior instead.
            return {"x-bt-auth-token": self.org_api_key, "Authorization": "Bearer null"}
        elif mode == "bt-auth-bogus":
            return {"x-bt-auth-token": self.org_api_key, "Authorization": f"Bearer bogus"}
        else:
            raise ValueError(f"Invalid mode: {mode}")

    def run_request_with_mode(self, mode, *args, **kwargs):
        return super().run_request(*args, **{**kwargs, "headers": self._get_request_headers(mode)})

    # NOTE: This is a very slim version of what we do in test_rest_api. I thought it would be simpler and
    # easier to pull out a more targeted version that hits all the right spots.
    @parameterized.expand(PARAMETERS)
    def test_basic_crud(self, mode):
        # Create a project
        project = self.run_request_with_mode(
            mode, "post", make_v1_url("project"), json={"name": "test-project"}
        ).json()
        # Create an experiment, read the rows from it
        experiment = self.run_request_with_mode(
            mode, "post", make_v1_url("experiment"), json={"name": "test-experiment", "project_id": project["id"]}
        ).json()

        self.run_request_with_mode(
            mode,
            "post",
            make_v1_url("experiment", experiment["id"], "insert"),
            json=dict(
                events=[
                    dict(id="row0", input="foo0", expected="bar0", scores={"foo": 0.5}),
                ]
            ),
        )

        summary = self.run_request_with_mode(
            mode,
            "get",
            make_v1_url("experiment", experiment["id"], "summarize"),
            params=dict(summarize_scores="true"),
        ).json()
        print(summary)
        assert summary["scores"]["foo"]["score"] == 0.5

    @parameterized.expand(PARAMETERS)
    def test_basic_function(self, mode):
        project = self.run_request_with_mode(
            mode, "post", make_v1_url("project"), json={"name": "function-test"}
        ).json()

        slug = "my-prompt"
        function_record = self.run_request_with_mode(
            mode,
            "post",
            make_v1_url("function"),
            json=dict(
                project_id=project["id"],
                prompt_data={
                    "prompt": {
                        "type": "chat",
                        "messages": [
                            {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                        ],
                    },
                    "options": {
                        "model": "gpt-3.5-turbo",
                    },
                },
                function_data={
                    "type": "prompt",
                },
                name="my prompt",
                slug=slug,
            ),
        ).json()

        prompt1 = self.run_request_with_mode(mode, "get", make_v1_url("prompt", function_record["id"])).json()
        prompt2 = self.run_request_with_mode(
            mode, "get", make_v1_url("prompt"), params=dict(project_name=project["name"], slug=slug)
        ).json()
        self.assertEqual(prompt1, prompt2["objects"][0])

        resp = self.run_request_with_mode(
            mode,
            "post",
            f"{self.proxy_url}/function/invoke",
            json={
                "project_name": project["name"],
                "slug": slug,
                "input": {"formula": "1+1"},
            },
        ).json()

        self.assertEqual(resp, "2")

    @parameterized.expand(PARAMETERS)
    def test_basic_chat_completion(self, mode):
        # Run it via the proxy
        # Note: this is approximately, but not exactly, what the function call runs. If you
        # delete the proxy cache and re-run this, it may fail if the answers stop lining up.
        resp = self.run_request_with_mode(
            mode,
            "post",
            f"{self.openai_base_url}/chat/completions",
            json=dict(
                messages=[{"role": "user", "content": "What is 1+1?"}],
                model="gpt-3.5-turbo",
            ),
        ).json()
        self.assertEqual(resp["usage"]["prompt_tokens"], 14)
