import asyncio
from uuid import uuid4

import braintrust
import requests

from autoevals import <PERSON><PERSON><PERSON><PERSON>
from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class ExperimentOpenTest(BraintrustAppTestBase):
    def test_basic_open(self):
        dataset = braintrust.init_dataset("p", name="ds123", use_output=False)

        for i in range(10):
            dataset.insert(input=f"{i}", expected=f"{i}", tags=["tag1", "tag2"])

        dataset.flush()

        for row in dataset:
            assert row["tags"] == ["tag1", "tag2"], row["tags"]

        result = braintrust.Eval(
            "p",
            data=braintrust.init_dataset("p", name="ds123", use_output=False),
            task=lambda x: x,
            scores=[Levenshtein],
        )

        experiment = braintrust.init_experiment("p", experiment=result.summary.experiment_name, open=True)

        for row in experiment:
            if row["root_span_id"] == row["span_id"]:
                assert row["tags"] == ["tag1", "tag2"], row["tags"]
            else:
                assert row["tags"] is None

    def test_dict_tag(self):
        experiment = braintrust.init_experiment("p")
        self.assertRaises(ValueError, lambda: experiment.log(tags={"tag1": "value1", "tag2": "value2"}))

    def test_patch_experiment_tags(self):
        """Test adding and removing tags from experiments using the patch API"""
        # Create an experiment with initial tags using the register API directly
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/register",
            json={
                "project_name": "p",
                "experiment_name": f"test_experiment_{uuid4()}",
                "tags": ["initial_tag1", "initial_tag2"],
            },
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to create experiment: {resp.text}")
        experiment_resp = resp.json()
        experiment_id = experiment_resp["experiment"]["id"]

        # Verify initial tags were set
        initial_tags = set(experiment_resp["experiment"].get("tags", []))
        expected_initial_tags = set(["initial_tag1", "initial_tag2"])
        self.assertEqual(initial_tags, expected_initial_tags, f"Expected {expected_initial_tags}, got {initial_tags}")

        # Test adding tags
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/patch_id",
            json={"id": experiment_id, "add_tags": ["new_tag1", "new_tag2"]},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to add tags: {resp.text}")
        patched_experiment = resp.json()

        # Verify tags were added (should contain both initial and new tags)
        expected_tags = set(["initial_tag1", "initial_tag2", "new_tag1", "new_tag2"])
        actual_tags = set(patched_experiment.get("tags", []))
        self.assertEqual(actual_tags, expected_tags, f"Expected {expected_tags}, got {actual_tags}")

        # Test removing specific tags
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/patch_id",
            json={"id": experiment_id, "remove_tags": ["initial_tag1", "new_tag1"]},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to remove tags: {resp.text}")
        patched_experiment = resp.json()

        # Verify specific tags were removed
        expected_tags = set(["initial_tag2", "new_tag2"])
        actual_tags = set(patched_experiment.get("tags", []))
        self.assertEqual(actual_tags, expected_tags, f"Expected {expected_tags}, got {actual_tags}")

        # Test error when trying to use multiple tag parameters simultaneously
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/patch_id",
            json={"id": experiment_id, "tags": ["complete_replacement"], "add_tags": ["should_fail"]},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertFalse(resp.ok, "Should fail when multiple tag parameters are provided")
        self.assertEqual(resp.status_code, 400)

    def test_patch_experiment_complete_tag_replacement(self):
        """Test completely replacing experiment tags using the patch API"""
        # Create an experiment with initial tags using the register API directly
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/register",
            json={
                "project_name": "p",
                "experiment_name": f"test_experiment_{uuid4()}",
                "tags": ["old_tag1", "old_tag2"],
            },
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to create experiment: {resp.text}")
        experiment_resp = resp.json()
        experiment_id = experiment_resp["experiment"]["id"]

        # Verify initial tags were set
        initial_tags = set(experiment_resp["experiment"].get("tags", []))
        expected_initial_tags = set(["old_tag1", "old_tag2"])
        self.assertEqual(initial_tags, expected_initial_tags, f"Expected {expected_initial_tags}, got {initial_tags}")

        # Test complete tag replacement
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/patch_id",
            json={"id": experiment_id, "tags": ["replacement_tag1", "replacement_tag2", "replacement_tag3"]},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to replace tags: {resp.text}")
        patched_experiment = resp.json()

        # Verify tags were completely replaced
        expected_tags = set(["replacement_tag1", "replacement_tag2", "replacement_tag3"])
        actual_tags = set(patched_experiment.get("tags", []))
        self.assertEqual(actual_tags, expected_tags, f"Expected {expected_tags}, got {actual_tags}")

        # Test setting tags to empty array
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/patch_id",
            json={"id": experiment_id, "tags": []},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to clear tags: {resp.text}")
        patched_experiment = resp.json()

        # Verify tags were cleared
        self.assertEqual(patched_experiment.get("tags", []), [], "Tags should be empty")

    def test_create_experiment_with_api_no_tags(self):
        """Test creating an experiment directly via API without initial tags"""
        # Create an experiment without tags using the register API directly
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/register",
            json={
                "project_name": "p",
                "experiment_name": f"test_experiment_{uuid4()}",
                "description": "Test experiment created via API",
            },
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to create experiment: {resp.text}")
        experiment_resp = resp.json()
        experiment_id = experiment_resp["experiment"]["id"]

        # Verify no tags initially
        initial_tags = experiment_resp["experiment"].get("tags", [])
        self.assertEqual(initial_tags, None, "Should have no tags initially")

        # Now add tags via patch
        resp = requests.post(
            f"{LOCAL_API_URL}/api/experiment/patch_id",
            json={"id": experiment_id, "add_tags": ["first_tag", "second_tag"]},
            headers={"Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertTrue(resp.ok, f"Failed to add tags: {resp.text}")
        patched_experiment = resp.json()

        # Verify tags were added
        expected_tags = set(["first_tag", "second_tag"])
        actual_tags = set(patched_experiment.get("tags", []))
        self.assertEqual(actual_tags, expected_tags, f"Expected {expected_tags}, got {actual_tags}")
