import braintrust


def calculator(op, a, b):
    print(f"Calculator called with {op}, {a}, {b}")
    if op == "add":
        return a + b
    elif op == "subtract":
        return a - b
    elif op == "multiply":
        return a * b
    elif op == "divide":
        return a / b


project = braintrust.projects.create(name="tool no params")

tool = project.tools.create(
    name="Calculator",
    description="A simple calculator that can add, subtract, multiply, and divide.",
    handler=calculator,
    if_exists="replace",
)
