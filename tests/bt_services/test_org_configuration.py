import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, TEST_ARGS, BraintrustAppTestBase


class OrgConfigurationsTest(BraintrustAppTestBase):
    def test_proxy_url(self):
        org_info = self.run_request("get", f"{LOCAL_APP_URL}/api/apikey/login").json()["org_info"]
        self.assertEqual(len(org_info), 1)

        self.assertIsNotNone(org_info[0]["api_url"])
        self.assertIsNotNone(org_info[0]["realtime_url"])
        self.assertIsNotNone(org_info[0]["proxy_url"])

        org_id = org_info[0]["id"]

        org_info = self.run_request("get", f"{LOCAL_API_URL}/v1/organization/{org_id}").json()
        self.assertIsNone(org_info["api_url"])
        self.assertIsNone(org_info["realtime_url"])
        self.assertIsNotNone(org_info["proxy_url"])  # This should be http://localhost:8001 (the test proxy)

        # Configure the api url, realtime url, and proxy url to be bogus values via a REST API request
        org_info = self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/{org_id}",
            json={
                "api_url": "http://api.bogus",
                "realtime_url": "ws://realtime.bogus",
                "proxy_url": "http://proxy.bogus",
            },
        ).json()

        self.assertEqual(org_info["api_url"], "http://api.bogus")
        self.assertEqual(org_info["realtime_url"], "ws://realtime.bogus")
        self.assertEqual(org_info["proxy_url"], "http://proxy.bogus")

        # Check that the api url, realtime url, and proxy url are bogus values
        org_info = self.run_request("get", f"{LOCAL_APP_URL}/api/apikey/login").json()["org_info"]
        self.assertEqual(org_info[0]["api_url"], "http://api.bogus")
        self.assertEqual(org_info[0]["realtime_url"], "ws://realtime.bogus")
        self.assertEqual(org_info[0]["proxy_url"], "http://proxy.bogus")

        # Make sure the GET request also returns the bogus values
        org_info = self.run_request("get", f"{LOCAL_API_URL}/v1/organization/{org_id}").json()
        self.assertEqual(org_info["api_url"], "http://api.bogus")
        self.assertEqual(org_info["realtime_url"], "ws://realtime.bogus")
        self.assertEqual(org_info["proxy_url"], "http://proxy.bogus")

    def test_unprivileged_user(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/{self.org_id}",
            headers={"Authorization": f"Bearer {user_api_key}"},
            json=dict(proxy_url="http://bogus"),
            expect_error=True,
        )
        self.grant_acl(
            dict(
                object_type="organization",
                object_id=self.org_id,
                user_id=user_id,
                permission="update",
                restrict_object_type="organization",
            ),
        )
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/{self.org_id}",
            headers={"Authorization": f"Bearer {user_api_key}"},
            json=dict(proxy_url="http://bogus"),
        )
