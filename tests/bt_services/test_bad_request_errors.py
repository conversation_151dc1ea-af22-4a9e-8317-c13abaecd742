import requests

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class BadRequestErrorsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.headers = {"Authorization": f"Bearer {self.org_api_key}"}

    def _check_sanitized_message(self, text, sensitive_text=None):
        self.assertNotIn(self.org_api_key, text)
        if sensitive_text is not None:
            self.assertNotIn(sensitive_text, text)

    def test_invalid_route(self):
        for route in ["typo", "foo/bar", "version/bad"]:
            resp = requests.get(f"{LOCAL_API_URL}/{route}", headers=self.headers)
            self._check_sanitized_message(resp.text)
            self.assertRegex(resp.text, "Cannot GET")

    def test_invalid_method(self):
        resp = requests.post(f"{LOCAL_API_URL}/ping", headers=self.headers)
        self._check_sanitized_message(resp.text)
        self.assertRegex(resp.text, "Cannot POST")

    def test_invalid_api_key(self):
        bad_api_key = self.org_api_key[:-1] + ("x" if self.org_api_key[-1] != "x" else "q")
        resp = requests.get(f"{LOCAL_API_URL}/ping", headers={"Authorization": f"Bearer {bad_api_key}"})
        self._check_sanitized_message(resp.text, sensitive_text=bad_api_key)
        self.assertIn("Invalid API Key", resp.text)
