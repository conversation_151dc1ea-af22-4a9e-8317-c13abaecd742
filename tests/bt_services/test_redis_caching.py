from uuid import uuid4

import braintrust
import redis
import requests
from braintrust.util import merge_dicts
from braintrust_local.constants import (
    BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER,
    BT_AUTOMATION_WAS_CACHED_REDIS_TOKEN_HEADER,
    BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER,
    BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER,
    LOCAL_REDIS_HOST,
    LOCAL_REDIS_PORT,
)

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def make_url(suffix):
    return f"{LOCAL_API_URL}/v1/{suffix}"


class RedisCachingTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.conn = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0)
        self.bt_data_plane_service_token = self.ensureDataPlaneServiceTokenExists()

    def get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def _checked_request(self, verb, *args, **kwargs):
        resp = getattr(requests, verb)(*args, **merge_dicts({"headers": self.get_request_headers()}, kwargs))
        self.assertTrue(resp.ok, f"{resp.status_code} error: {resp.text}")
        return resp

    def _check_cache_hit(self, token):
        resp = self.conn.get(token)
        if not resp:
            return False
        else:
            return resp.decode() == "true"

    def test_prompt(self):
        project = self._checked_request("post", make_url("project"), json=dict(name="p")).json()
        prompt = self._checked_request(
            "post", make_url("prompt"), json=dict(project_id=project["id"], name="my-prompt", slug="my-prompt-slug")
        ).json()
        prompt_id = prompt["id"]

        # Fetching it by name should retrieve the prompt but not be a cache hit.
        def fetch_prompt_by_name(cache_token):
            return self._checked_request(
                "get",
                make_url("prompt"),
                params=dict(project_name="p", slug="my-prompt-slug"),
                headers={BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token},
            ).json()["objects"][0]

        cache_token = str(uuid4())
        orig_prompt = fetch_prompt_by_name(cache_token)
        self.assertEqual(orig_prompt["id"], prompt_id)
        self.assertFalse(self._check_cache_hit(cache_token))
        # The same fetch should now be a hit.
        cache_token = str(uuid4())
        prompt = fetch_prompt_by_name(cache_token)
        self.assertEqual(prompt, orig_prompt)
        self.assertTrue(self._check_cache_hit(cache_token))

        # Fetching it by prompt id should not be a cache hit.
        def fetch_prompt_by_id(cache_token):
            return self._checked_request(
                "get",
                make_url(f"prompt/{prompt_id}"),
                headers={BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token},
            ).json()

        cache_token = str(uuid4())
        prompt = fetch_prompt_by_id(cache_token)
        self.assertEqual(prompt, orig_prompt)
        self.assertFalse(self._check_cache_hit(cache_token))
        # The same fetch should now be a hit.
        cache_token = str(uuid4())
        prompt = fetch_prompt_by_id(cache_token)
        self.assertEqual(prompt, orig_prompt)
        self.assertTrue(self._check_cache_hit(cache_token))

        # If we modify the prompt, it should invalidate the cache.
        self._checked_request("patch", make_url(f"prompt/{prompt_id}"), json=dict(description="why then"))
        cache_token = str(uuid4())
        orig_prompt = fetch_prompt_by_name(cache_token)
        self.assertEqual(orig_prompt["id"], prompt_id)
        self.assertEqual(orig_prompt["description"], "why then")
        self.assertFalse(self._check_cache_hit(cache_token))
        # But re-fetching should work again.
        cache_token = str(uuid4())
        prompt = fetch_prompt_by_name(cache_token)
        self.assertEqual(prompt, orig_prompt)
        self.assertTrue(self._check_cache_hit(cache_token))
        # Same with id-based fetch.
        cache_token = str(uuid4())
        prompt = fetch_prompt_by_id(cache_token)
        self.assertEqual(prompt, orig_prompt)
        self.assertFalse(self._check_cache_hit(cache_token))
        cache_token = str(uuid4())
        prompt = fetch_prompt_by_id(cache_token)
        self.assertEqual(prompt, orig_prompt)
        self.assertTrue(self._check_cache_hit(cache_token))

    def test_object_cache(self):
        e0 = braintrust.init("p")
        e1 = braintrust.init("p")
        d0 = braintrust.init_dataset("p", "my_dataset")

        def fetch_object(object_type, object_id):
            cache_token = str(uuid4())
            self._checked_request(
                "get",
                make_url(f"{object_type}/{object_id}/fetch"),
                headers={
                    BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER: cache_token,
                },
            )
            return self._check_cache_hit(cache_token)

        # Fetching any of the objects should not be a cache hit at first. But
        # the next fetch should be a cache hit for all.
        for expect_hit in [False, True]:
            self.assertEqual(fetch_object("experiment", e0.id), expect_hit)
            self.assertEqual(fetch_object("experiment", e1.id), expect_hit)
            self.assertEqual(fetch_object("dataset", d0.id), expect_hit)

        # Clear the cache for a single experiment.
        self._checked_request(
            "post",
            f"{LOCAL_API_URL}/flush-object-cache",
            json=dict(org_id=braintrust.org_id(), object_type="experiment", object_id=e0.id),
        )
        self.assertEqual(fetch_object("experiment", e0.id), False)
        self.assertEqual(fetch_object("experiment", e1.id), True)
        self.assertEqual(fetch_object("dataset", d0.id), True)

        # Clear the cache for all objects in the org..
        self._checked_request(
            "post",
            f"{LOCAL_API_URL}/flush-object-cache",
            json=dict(org_id=braintrust.org_id(), object_type="organization", object_id=braintrust.org_id()),
        )
        self.assertEqual(fetch_object("experiment", e0.id), False)
        self.assertEqual(fetch_object("experiment", e1.id), False)
        self.assertEqual(fetch_object("dataset", d0.id), False)

    def test_resource_limit_unlimited(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("insert into resources (org_id) values (%s)", (self.org_id,))

        # The first time we insert something, it should not be cached.
        project = self._checked_request("post", make_url("project"), json=dict(name="p")).json()
        was_cached_token = str(uuid4())
        self._checked_request(
            "post",
            make_url(f"project_logs/{project['id']}/insert"),
            headers={BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
            json=dict(
                events=[
                    dict(),
                ]
            ),
        )
        self.assertFalse(self._check_cache_hit(was_cached_token))

        # But the second time, it should.
        was_cached_token = str(uuid4())
        self._checked_request(
            "post",
            make_url(f"project_logs/{project['id']}/insert"),
            headers={BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
            json=dict(
                events=[
                    dict(id="bar"),
                ]
            ),
        )
        self.assertTrue(self._check_cache_hit(was_cached_token))

    def test_resource_limit_limited_different_category(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "insert into resources (org_id, num_private_experiment_row_actions, num_private_experiment_row_actions_calendar_months) values (%s, %s, %s)",
                    (self.org_id, (7, 10), (1, 10)),
                )

        project = self._checked_request("post", make_url("project"), json=dict(name="p")).json()

        # It should never be cached no matter how many times we insert.
        for i in range(10):
            was_cached_token = str(uuid4())
            self._checked_request(
                "post",
                make_url(f"project_logs/{project['id']}/insert"),
                headers={BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
                json=dict(
                    events=[
                        dict(),
                    ]
                ),
            )
            self.assertFalse(self._check_cache_hit(was_cached_token))

    def test_resource_limit_cache_failure(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "insert into resources (org_id, num_production_log_row_actions, num_production_log_row_actions_calendar_months) values (%s, %s, %s)",
                    (self.org_id, (7, 10), (1, 10)),
                )

        project = self._checked_request("post", make_url("project"), json=dict(name="p")).json()

        # Initial inserts should not be cached.
        PROJECT_LOGS_URL = make_url(f"project_logs/{project['id']}/insert")
        for i in range(2):
            was_cached_token = str(uuid4())
            self._checked_request(
                "post",
                PROJECT_LOGS_URL,
                headers={BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
                json=dict(events=[dict() for j in range(5)]),
            )
            self.assertFalse(self._check_cache_hit(was_cached_token))

        # The first failure should not be cached.
        was_cached_token = str(uuid4())
        resp = requests.post(
            PROJECT_LOGS_URL,
            headers={**self.get_request_headers(), BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
            json=dict(events=[dict()]),
        )
        self.assertFalse(resp.ok)
        self.assertFalse(self._check_cache_hit(was_cached_token))

        # But the second failure should.
        was_cached_token = str(uuid4())
        resp = requests.post(
            PROJECT_LOGS_URL,
            headers={**self.get_request_headers(), BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
            json=dict(events=[dict()]),
        )
        self.assertFalse(resp.ok)
        self.assertTrue(self._check_cache_hit(was_cached_token))

    def test_autoflush_object_cache(self):
        project = self._checked_request("post", make_url("project"), json=dict(name="p")).json()
        experiment = self._checked_request(
            "post", make_url("experiment"), json=dict(name="e", project_id=project["id"])
        ).json()

        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        EXPECT_ERROR = True

        def user_fetch_experiment(expect_error):
            self.run_request(
                "get",
                make_url(f"experiment/{experiment['id']}/fetch"),
                headers={"Authorization": f"Bearer {user_api_key}"},
                expect_error=expect_error,
            )

        def anon_fetch_experiment(expect_error):
            self.run_request(
                "get", make_url(f"experiment/{experiment['id']}/fetch"), headers={}, expect_error=expect_error
            )

        # At first they cannot access the experiment.
        user_fetch_experiment(EXPECT_ERROR)

        # Create an empty group and an empty role. Grant that ACL to the
        # experiment.
        group = self._checked_request("post", make_url("group"), json=dict(name="test-group")).json()
        role = self._checked_request("post", make_url("role"), json=dict(name="test-role")).json()
        self.grant_acl(
            dict(object_type="experiment", object_id=experiment["id"], group_id=group["id"], role_id=role["id"]),
        )

        # If we directly grant them access to the experiment (at any hierarchy
        # level), they should get access. Removing that ACL should then revoke
        # access. Try all relevant endpoints.

        for endpoint in ["/api/acl/add_remove_multi", "/api/acl/batch_update", "/v1/acl/batch_update"]:
            self._checked_request(
                "post",
                f"{LOCAL_API_URL}{endpoint}",
                json=dict(
                    add_acls=[
                        dict(object_type="project", object_id=project["id"], user_id=user_id, permission="read")
                    ],
                ),
            )
            user_fetch_experiment(not EXPECT_ERROR)
            self._checked_request(
                "post",
                f"{LOCAL_API_URL}{endpoint}",
                json=dict(
                    remove_acls=[
                        dict(object_type="project", object_id=project["id"], user_id=user_id, permission="read")
                    ],
                ),
            )
            user_fetch_experiment(EXPECT_ERROR)

        acl = self._checked_request(
            "post",
            f"{LOCAL_API_URL}/api/acl/register",
            json=dict(object_type="experiment", object_id=experiment["id"], user_id=user_id, permission="read"),
        ).json()["acl"]
        user_fetch_experiment(not EXPECT_ERROR)
        self._checked_request("post", f"{LOCAL_API_URL}/api/acl/delete_id", json=dict(id=acl["id"]))
        user_fetch_experiment(EXPECT_ERROR)

        self._checked_request(
            "post",
            f"{LOCAL_API_URL}/api/acl/register",
            json=dict(object_type="experiment", object_id=experiment["id"], user_id=user_id, permission="read"),
        )
        user_fetch_experiment(not EXPECT_ERROR)
        self._checked_request(
            "delete",
            f"{LOCAL_API_URL}/v1/acl",
            json=dict(object_type="experiment", object_id=experiment["id"], user_id=user_id, permission="read"),
        )
        user_fetch_experiment(EXPECT_ERROR)

        self._checked_request(
            "post", f"{LOCAL_API_URL}/api/group/patch_id", json=dict(id=group["id"], add_member_users=[user_id])
        )
        self._checked_request(
            "post",
            f"{LOCAL_API_URL}/api/role/patch_id",
            json=dict(id=role["id"], add_member_permissions=[dict(permission="read")]),
        )
        user_fetch_experiment(not EXPECT_ERROR)
        self._checked_request(
            "post", f"{LOCAL_API_URL}/api/group/patch_id", json=dict(id=group["id"], remove_member_users=[user_id])
        )
        user_fetch_experiment(EXPECT_ERROR)
        self._checked_request(
            "post", f"{LOCAL_API_URL}/api/group/patch_id", json=dict(id=group["id"], add_member_users=[user_id])
        )
        user_fetch_experiment(not EXPECT_ERROR)
        self._checked_request(
            "post",
            f"{LOCAL_API_URL}/api/role/patch_id",
            json=dict(id=role["id"], remove_member_permissions=[dict(permission="read")]),
        )
        user_fetch_experiment(EXPECT_ERROR)
        self._checked_request(
            "post", f"{LOCAL_API_URL}/api/group/patch_id", json=dict(id=group["id"], remove_member_users=[user_id])
        )
        user_fetch_experiment(EXPECT_ERROR)

        anon_fetch_experiment(EXPECT_ERROR)
        self._checked_request(
            "post", f"{LOCAL_API_URL}/api/experiment/patch_id", json=dict(id=experiment["id"], public=True)
        )
        anon_fetch_experiment(not EXPECT_ERROR)
        self._checked_request(
            "post", f"{LOCAL_API_URL}/api/experiment/patch_id", json=dict(id=experiment["id"], public=False)
        )
        anon_fetch_experiment(EXPECT_ERROR)

        acl = self._checked_request(
            "post",
            make_url("acl"),
            json=dict(object_type="experiment", object_id=experiment["id"], user_id=user_id, permission="read"),
        ).json()
        user_fetch_experiment(not EXPECT_ERROR)
        acl = self._checked_request("delete", make_url(f"acl/{acl['id']}"))
        user_fetch_experiment(EXPECT_ERROR)

        self._checked_request("patch", make_url(f"group/{group['id']}"), json=dict(add_member_users=[user_id]))
        self._checked_request(
            "patch", make_url(f"role/{role['id']}"), json=dict(add_member_permissions=[dict(permission="read")])
        )
        user_fetch_experiment(not EXPECT_ERROR)
        self._checked_request("patch", make_url(f"group/{group['id']}"), json=dict(remove_member_users=[user_id]))
        user_fetch_experiment(EXPECT_ERROR)
        self._checked_request("patch", make_url(f"group/{group['id']}"), json=dict(add_member_users=[user_id]))
        user_fetch_experiment(not EXPECT_ERROR)
        self._checked_request(
            "patch", make_url(f"role/{role['id']}"), json=dict(remove_member_permissions=[dict(permission="read")])
        )
        user_fetch_experiment(EXPECT_ERROR)
        self._checked_request("patch", make_url(f"group/{group['id']}"), json=dict(remove_member_users=[user_id]))
        user_fetch_experiment(EXPECT_ERROR)

        anon_fetch_experiment(EXPECT_ERROR)
        self._checked_request("patch", make_url(f"experiment/{experiment['id']}"), json=dict(public=True))
        anon_fetch_experiment(not EXPECT_ERROR)
        self._checked_request("patch", make_url(f"experiment/{experiment['id']}"), json=dict(public=False))
        anon_fetch_experiment(EXPECT_ERROR)

    def test_async_scoring_config(self):
        project0 = self._checked_request("post", make_url("project"), json=dict(name="p0")).json()
        project1 = self._checked_request("post", make_url("project"), json=dict(name="p1")).json()

        def register_config(project_id):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/project_score/register",
                json=dict(
                    project_id=project_id,
                    project_score_name=f"online_config",
                    score_type="online",
                    config=dict(
                        online=dict(
                            apply_to_root_span=True,
                            sampling_rate=1.0,
                            scorers=[dict(type="global", name="ExactMatch")],
                        )
                    ),
                ),
            ).json()["project_score"]

        project0_config = register_config(project0["id"])

        def insert_event(project_id, was_cached_token):
            self._checked_request(
                "post",
                make_url(f"project_logs/{project_id}/insert"),
                headers={BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER: was_cached_token},
                json=dict(
                    events=[
                        dict(),
                    ]
                ),
            )

        def check_insert_event_cached(project_id):
            was_cached_token = str(uuid4())
            insert_event(project_id, was_cached_token)
            return self._check_cache_hit(was_cached_token)

        # Inserting against both projects for the first time should not be
        # cached.
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertFalse(check_insert_event_cached(project1["id"]))

        # But the second time, it should be cached.
        self.assertTrue(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project1["id"]))

        # Test auto-flushing against the various project score endpoints.

        # Get should not trigger an auto-flush.
        self.run_request(
            "get",
            make_url(f"project_score"),
            params=dict(
                project_id=project0["id"],
            ),
        )
        self.assertTrue(check_insert_event_cached(project0["id"]))
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/project_score/get",
            json=dict(
                project_id=project0["id"],
            ),
        )
        self.assertTrue(check_insert_event_cached(project0["id"]))
        self.run_request("get", make_url(f"project_score/{project0_config['id']}"))
        self.assertTrue(check_insert_event_cached(project0["id"]))

        # Patch should trigger an auto-flush.
        self.run_request(
            "patch",
            make_url(f"project_score/{project0_config['id']}"),
            json=dict(
                name="foobar",
            ),
        )
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project0["id"]))
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/project_score/patch_id",
            json=dict(
                id=project0_config["id"],
                name="foobar2",
            ),
        )
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project0["id"]))

        # Delete should also trigger an auto-flush.
        self.run_request("delete", make_url(f"project_score/{project0_config['id']}"))
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project0["id"]))

        project0_config2 = self.run_request(
            "post",
            make_url("project_score"),
            json=dict(
                project_id=project0["id"],
                name=f"goober",
                score_type="slider",
            ),
        ).json()
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project0["id"]))
        project0_config3 = self.run_request(
            "put",
            make_url("project_score"),
            json=dict(
                project_id=project0["id"],
                name=f"goober",
                score_type="online",
            ),
        ).json()
        self.assertEqual(project0_config2["id"], project0_config3["id"])
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project0["id"]))

        # Try the other delete method.
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/project_score/delete_id",
            json=dict(
                id=project0_config2["id"],
            ),
        )
        self.assertFalse(check_insert_event_cached(project0["id"]))
        self.assertTrue(check_insert_event_cached(project0["id"]))

        # Should also auto-flush for the other project.
        project1_config = register_config(project1["id"])
        self.assertFalse(check_insert_event_cached(project1["id"]))
        self.assertTrue(check_insert_event_cached(project1["id"]))
