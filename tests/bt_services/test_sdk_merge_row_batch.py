import unittest

from braintrust.db_fields import IS_MERGE_FIELD, PARENT_ID_FIELD
from braintrust.merge_row_batch import batch_items, merge_row_batch


class MergeRowBatchTest(unittest.TestCase):
    def test_basic(self):
        rows = [
            # These rows should get merged together, ending up as a merge.
            dict(
                experiment_id="e0",
                id="x",
                inputs=dict(a=12),
                **{IS_MERGE_FIELD: True},
            ),
            dict(
                experiment_id="e0",
                id="x",
                inputs=dict(b=10),
                **{IS_MERGE_FIELD: True},
            ),
            dict(
                experiment_id="e0",
                id="x",
                inputs=dict(c="hello"),
                **{IS_MERGE_FIELD: True},
            ),
            # The first row should be clobbered by the second, but the third
            # merged with the second, ending up as a replacement.
            dict(
                experiment_id="e0",
                id="y",
                inputs=dict(a="hello"),
            ),
            dict(
                experiment_id="e0",
                id="y",
                inputs=dict(b=10),
            ),
            dict(
                experiment_id="e0",
                id="y",
                inputs=dict(c=12),
                **{IS_MERGE_FIELD: True},
            ),
            # These rows should be clobbered separately from the last batch.
            dict(
                dataset_id="d0",
                id="y",
                inputs=dict(a="hello"),
            ),
            dict(
                dataset_id="d0",
                id="y",
                inputs=dict(b=10),
            ),
            dict(
                dataset_id="d0",
                id="y",
                inputs=dict(c=12),
            ),
        ]

        # Every row should be in its own bucket, since there are no parent ID
        # references. So we just flatten the list.
        merged_rows = [x for l in merge_row_batch(rows) for x in l]
        key_to_rows = {(row.get("experiment_id"), row.get("dataset_id"), row.get("id")): row for row in merged_rows}
        self.assertEqual(
            {
                ("e0", None, "x"): dict(
                    experiment_id="e0",
                    id="x",
                    inputs=dict(a=12, b=10, c="hello"),
                    **{IS_MERGE_FIELD: True},
                ),
                ("e0", None, "y"): dict(
                    experiment_id="e0",
                    id="y",
                    inputs=dict(b=10, c=12),
                ),
                (None, "d0", "y"): dict(
                    dataset_id="d0",
                    id="y",
                    inputs=dict(c=12),
                ),
            },
            key_to_rows,
        )

    def test_ordered_bucketing(self):
        rows = [
            # These rows form a chain of references, as one bucket.
            dict(experiment_id="e", id="a"),
            dict(experiment_id="e", id="b", **{PARENT_ID_FIELD: "a"}),
            dict(experiment_id="e", id="c", **{PARENT_ID_FIELD: "b"}),
            # Same ids, different object. Should form a separate chain.
            dict(experiment_id="e2", id="a"),
            dict(experiment_id="e2", id="b", **{PARENT_ID_FIELD: "a"}),
            dict(experiment_id="e2", id="c", **{PARENT_ID_FIELD: "b"}),
        ]

        merged_row_buckets = merge_row_batch(rows)
        self.assertEqual(len(merged_row_buckets), 2, merged_row_buckets)
        bucket0, bucket1 = merged_row_buckets
        if any(x["experiment_id"] == "e" for x in bucket1):
            bucket1, bucket0 = bucket0, bucket1
        self.assertEqual(bucket0, rows[0:3])
        self.assertEqual(bucket1, rows[3:6])

    def test_skip_fields(self):
        rows = [
            # These rows should get merged together, ending up as a merge. But
            # the original fields should be retained, regardless of whether we
            # populated them or not.
            dict(
                experiment_id="e0",
                id="x",
                inputs=dict(a=12),
                **{IS_MERGE_FIELD: True},
                created=123,
                root_span_id="abc",
                _parent_id="baz",
                span_parents=["foo", "bar"],
            ),
            dict(
                experiment_id="e0",
                id="x",
                inputs=dict(b=10),
                **{IS_MERGE_FIELD: True},
                created=456,
                span_id="foo",
                root_span_id="bar",
                _parent_id="boop",
                span_parents=[],
            ),
        ]

        # Every row should be in its own bucket, since there are no parent ID
        # references. So we just flatten the list.
        merged_rows = [x for l in merge_row_batch(rows) for x in l]
        self.assertEqual(
            merged_rows,
            [
                dict(
                    experiment_id="e0",
                    id="x",
                    inputs=dict(a=12, b=10),
                    **{IS_MERGE_FIELD: True},
                    created=123,
                    root_span_id="abc",
                    _parent_id="baz",
                    span_parents=["foo", "bar"],
                ),
            ],
        )


class BatchItemsTest(unittest.TestCase):
    def test_basic(self):
        a = "x" * 1
        b = "x" * 2
        c = "x" * 4
        d = "y" * 1
        e = "y" * 2
        f = "y" * 4

        items = [[a, b, c], [f, e, d]]

        # No limits.
        output = batch_items(items)
        self.assertEqual(output, [[[x for bucket in items for x in bucket]]])

        # Num items limit.
        output = batch_items(items, batch_max_num_items=2)
        self.assertEqual(output, [[[a, b], [f, e]], [[c, d]]])

        # Num bytes limit.
        output = batch_items(items, batch_max_num_bytes=2)
        self.assertEqual(output, [[[a], [f]], [[b], [e]], [[c], [d]]])

        # Both items and num bytes limit.
        output = batch_items(items, batch_max_num_items=2, batch_max_num_bytes=5)
        self.assertEqual(output, [[[a, b], [f]], [[c], [e, d]]])
