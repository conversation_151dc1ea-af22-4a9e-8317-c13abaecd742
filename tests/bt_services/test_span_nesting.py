import asyncio

import braintrust
from braintrust_local.api_db_util import get_object_json

from tests.braintrust_app_test_base import BraintrustAppTestBase


def track_span_ids(id_map, span_name, span):
    id_map[span_name] = span.id


def get_span_id(row):
    return row["span_id"]


def get_span_name(row):
    return row["span_attributes"]["name"]


def get_span_parents(row):
    return row.get("span_parents")


def get_is_root(row):
    return row["is_root"]


def get_output(row):
    return row["output"]


class SpanNestingTest(BraintrustAppTestBase):
    def check_test_basic(self, rows, id_map):
        # Check no duplicate values in id_map.
        id_map_rev = {key: name for name, key in id_map.items()}
        for name, key in id_map.items():
            self.assertEqual(name, id_map_rev[key])

        key_to_row = {row["id"]: row for row in rows}
        self.assertEqual(len(key_to_row), 5)
        row = key_to_row[id_map["root"]]
        self.assertEqual(get_span_name(row), "root")
        self.assertIsNone(get_span_parents(row))
        self.assertTrue(get_is_root(row))
        self.assertEqual("root_output", get_output(row))
        row = key_to_row[id_map["a0"]]
        self.assertEqual(get_span_name(row), "a0_custom_name")
        self.assertEqual(get_span_parents(row), [get_span_id(key_to_row[id_map["root"]])])
        self.assertFalse(get_is_root(row))
        self.assertEqual("a0_output", get_output(row))
        row = key_to_row[id_map["a00"]]
        self.assertEqual(get_span_name(row), "a00")
        self.assertEqual(get_span_parents(row), [get_span_id(key_to_row[id_map["a0"]])])
        self.assertFalse(get_is_root(row))
        self.assertEqual("a00_output", get_output(row))
        row = key_to_row[id_map["a01"]]
        self.assertEqual(get_span_name(row), "do_a01")
        self.assertEqual(get_span_parents(row), [get_span_id(key_to_row[id_map["a0"]])])
        self.assertFalse(get_is_root(row))
        self.assertEqual("a01_output", get_output(row))
        row = key_to_row[id_map["a1"]]
        self.assertEqual(get_span_name(row), "a1_subspan")
        self.assertEqual(get_span_parents(row), [get_span_id(key_to_row[id_map["root"]])])
        self.assertFalse(get_is_root(row))
        self.assertEqual("a1_output", get_output(row))

    def test_basic_sync(self):
        @braintrust.traced
        def a00(id_map):
            track_span_ids(id_map, "a00", braintrust.current_span())
            braintrust.current_span().log(output="a00_output")

        @braintrust.traced(name="do_a01")
        def a01(id_map):
            track_span_ids(id_map, "a01", braintrust.current_span())
            braintrust.current_span().log(output="a01_output")

        @braintrust.traced("a0_custom_name")
        def a0(id_map):
            track_span_ids(id_map, "a0", braintrust.current_span())
            a00(id_map)
            a01(id_map)
            braintrust.current_span().log(output="a0_output")

        def a1(id_map, span):
            track_span_ids(id_map, "a1", span)
            span.log(output="a1_output")

        experiment = braintrust.init(project="p")
        id_map = {}
        with experiment.start_span() as root_span:
            track_span_ids(id_map, "root", root_span)
            a0(id_map)
            with root_span.start_span(name="a1_subspan") as subspan:
                a1(id_map, subspan)
            root_span.log(output="root_output")
        experiment.summarize()
        rows = get_object_json("experiment", experiment.id)
        self.check_test_basic(rows, id_map)

    def test_basic_async(self):
        @braintrust.traced
        async def a00(id_map):
            track_span_ids(id_map, "a00", braintrust.current_span())
            await asyncio.sleep(0.1)
            braintrust.current_span().log(output="a00_output")

        @braintrust.traced(name="do_a01")
        async def a01(id_map):
            track_span_ids(id_map, "a01", braintrust.current_span())
            await asyncio.sleep(0.1)
            braintrust.current_span().log(output="a01_output")

        @braintrust.traced("a0_custom_name")
        async def a0(id_map):
            track_span_ids(id_map, "a0", braintrust.current_span())
            await asyncio.sleep(0.1)
            a00_task = a00(id_map)
            await asyncio.sleep(0.1)
            a01_task = a01(id_map)
            await asyncio.sleep(0.1)
            await a01_task
            await asyncio.sleep(0.1)
            await a00_task
            await asyncio.sleep(0.1)
            braintrust.current_span().log(output="a0_output")

        async def a1(id_map, span):
            track_span_ids(id_map, "a1", span)
            await asyncio.sleep(0.1)
            span.log(output="a1_output")

        async def run_experiment():
            experiment = braintrust.init(project="p")
            id_map = {}
            with experiment.start_span() as root_span:
                track_span_ids(id_map, "root", root_span)
                a0_task = a0(id_map)
                with root_span.start_span(name="a1_subspan") as subspan:
                    await a1(id_map, subspan)
                root_span.log(output="root_output")
                await a0_task
            experiment.summarize()

            return experiment.id, id_map

        experiment_id, id_map = asyncio.run(run_experiment())
        rows = get_object_json("experiment", experiment_id)
        self.check_test_basic(rows, id_map)
