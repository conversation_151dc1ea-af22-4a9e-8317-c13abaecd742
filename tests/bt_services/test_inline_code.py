import sseclient
from braintrust.functions.stream import BraintrustStream
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase

LANGUAGES = ["python", "typescript"]
LANGUAGE_TO_RUNTIME = {
    "python": {"runtime": "python", "version": "3.12"},
    "typescript": {"runtime": "node", "version": "20"},
}
DEFAULT_TIMEOUT_MS = 10000

# NOTE: To run this on the lambda functions, uncomment the lambda credentials
# in api-ts/.env.development.


class InlineCodeTest(BraintrustAppTestBase):
    def _run_cases(self, runtime, code, cases, timeout_ms=None):
        for case in cases:
            response = self.run_request(
                "post",
                f"{LOCAL_API_URL}/function/invoke",
                json={
                    "inline_context": runtime,
                    "code": code,
                    "input": case["input"],
                    "timeout_ms": timeout_ms or DEFAULT_TIMEOUT_MS,
                },
                expect_error=case.get("expect_error", False),
                # Function invocation can return 500 errors, so we allow them
                # here.
                allow_500_errors=True,
            )
            if not case.get("expect_error", False):
                result = response.json()
                self.assertEqual(result, case["expected"])

    def test_basic_function(self):
        cases = [
            {"input": {"arg": "hello"}, "expected": {"score": 0.5, "metadata": {"arg": "hello"}}},
            {"input": {"arg": None}, "expected": {"score": 0.5, "metadata": {"arg": None}}},
            {"input": None, "expected": {"score": 0.5, "metadata": None}},
        ]

        for code in [
            """\
function handler(arg: any) {
    console.log(arg);
    return {
      score: 0.5,
      metadata: arg,
    };
}
""",
            """\
async function handler(arg: any) {
    console.log(arg);
    return {
      score: 0.5,
      metadata: arg,
    };
}
""",
            """\
const handler = (arg: any) => {
    console.log(arg);
    return {
      score: 0.5,
      metadata: arg,
    };
}
""",
            """\
const handler = (arg: string /* wrong type but who cares */) => {
    console.log(arg);
    return {
      score: 0.5,
      metadata: arg,
    };
}
""",
        ]:
            runtime = LANGUAGE_TO_RUNTIME["typescript"]
            self._run_cases(runtime, code, cases)

        cases[2]["expected"]["metadata"] = {}
        for code in [
            """\
def handler(**kwargs):
    return {
        "score": 0.5,
        "metadata": kwargs,
    }
""",
        ]:
            runtime = LANGUAGE_TO_RUNTIME["python"]
            self._run_cases(runtime, code, cases)

        cases[2]["expect_error"] = True
        for code in [
            """\
def handler(arg):
    return {
        "score": 0.5,
        "metadata": {"arg": arg},
    }
            """,
        ]:
            runtime = LANGUAGE_TO_RUNTIME["python"]
            self._run_cases(runtime, code, cases)

    def test_js_corner_cases(self):
        runtime = LANGUAGE_TO_RUNTIME["typescript"]
        for code in [
            """\
function handler(arg) {
    throw new Error("error");
}""",
            "syntax error",
            "foo = 1",
            """\
function non_handler(arg) {
    return {
        "score": 0.5,
        "metadata": arg,
    }
}""",
        ]:
            self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expect_error": True}])

        for code in [
            """
function handler(arg) {
 const x = 1;
}""",
            """
function handler(arg) {
    return null;
}
""",
            """
function handler(arg) {
    return undefined;
}
""",
        ]:
            self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expected": None}])

    def test_py_corner_cases(self):
        runtime = LANGUAGE_TO_RUNTIME["python"]
        for code in [
            """\
def handler(arg):
    return {
        "score": 0.5,
        "metadata": arg,
    }
""",
            """\
def handler(arg: str) -> dict:
    return {
        "score": 0.5,
        "metadata": arg,
    }
""",
        ]:
            cases = [
                {"input": {"arg": "hello"}, "expected": {"score": 0.5, "metadata": "hello"}},
                {"input": {"arg": None}, "expected": {"score": 0.5, "metadata": None}},
                {"input": {"arg2": "asdf"}, "expected": {"score": 0.5, "metadata": "asdf"}},
                {"input": None, "expect_error": True},
            ]
            self._run_cases(runtime, code, cases)

        for code in [
            """\
def handler(arg):
    raise Exception("error")
""",
            """\
import os
def handler(arg):
    return os.rm
""",
            """\
def non_handler(arg):
    return {
        "score": 0.5,
        "metadata": arg,
    }
""",
        ]:
            self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expect_error": True}])

        for code in [
            """
def handler(arg):
    x = 1
""",
            """
def handler(arg):
    return None
""",
            """
def handler(arg):
    pass
""",
        ]:
            self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expected": None}])

    def test_py_infinite_loop(self):
        runtime = LANGUAGE_TO_RUNTIME["python"]
        code = """\
def handler(arg):
    while True:
        pass
    """
        self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expect_error": True}])

    def test_js_infinite_loop(self):
        runtime = LANGUAGE_TO_RUNTIME["typescript"]
        code = """\
function handler(arg) {
    while (true) { }
}
"""
        self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expect_error": True}])

    def test_py_keywords(self):
        runtime = LANGUAGE_TO_RUNTIME["python"]
        code = """\
def handler(arg):
    x = any([True, False])
"""
        self._run_cases(runtime, code, [{"input": {"arg": "hello"}, "expected": None}])

    def test_py_async_handler(self):
        runtime = LANGUAGE_TO_RUNTIME["python"]
        # Test async handler with sleep
        code = """\
async def handler(output: str) -> int:
    import asyncio
    await asyncio.sleep(0.1)
    return 1 if "a" in output else 0
"""
        cases = [
            {"input": {"output": "abc"}, "expected": 1},
            {"input": {"output": "xyz"}, "expected": 0},
            {"input": {"output": "apple"}, "expected": 1},
        ]
        self._run_cases(runtime, code, cases)

        # Test async handler with dict return
        code = """\
async def handler(**kwargs) -> dict:
    import asyncio
    await asyncio.sleep(0.05)
    return {
        "score": 0.8,
        "metadata": kwargs,
    }
"""
        cases = [
            {"input": {"arg": "test"}, "expected": {"score": 0.8, "metadata": {"arg": "test"}}},
            {"input": {"foo": "bar", "baz": 123}, "expected": {"score": 0.8, "metadata": {"foo": "bar", "baz": 123}}},
        ]
        self._run_cases(runtime, code, cases)

        # Test async handler with kwargs (previously in error cases)
        code = """\
async def handler(**kwargs):
    return {
        "score": 0.5,
        "metadata": kwargs,
    }
"""
        cases = [
            {"input": {"arg": "hello"}, "expected": {"score": 0.5, "metadata": {"arg": "hello"}}},
            {"input": None, "expected": {"score": 0.5, "metadata": {}}},
        ]
        self._run_cases(runtime, code, cases)

    @parameterized.expand([(lang,) for lang in ["python", "typescript"]])
    def test_stdout_stderr(self, lang):
        runtime = LANGUAGE_TO_RUNTIME[lang]
        code = (
            """\
import sys
def handler(arg):
    print("foo")
    print("bar", file=sys.stderr)
    # This does not work right now so will not print anything
    sys.stdout.write("bing\\n")
    sys.stderr.write("baz\\n")
    return 1
"""
            if lang == "python"
            else """\
function handler(arg) {
console.log("foo");
console.error("bar");
process.stdout.write("bing\\n");
process.stderr.write("baz\\n");
return 1;
}
"""
        )

        # First validate that without streaming, we get nothing
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/invoke",
            json={
                "inline_context": runtime,
                "code": code,
                "input": {"arg": "hello"},
                "timeout_ms": DEFAULT_TIMEOUT_MS,
            },
        )
        self.assertEqual(response.json(), 1)

        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/invoke",
            json={
                "inline_context": runtime,
                "code": code,
                "input": {"arg": "hello"},
                "timeout_ms": DEFAULT_TIMEOUT_MS,
                "stream": True,
            },
        )
        stream = BraintrustStream(sseclient.SSEClient(response))
        stdout_words = set()
        stderr_words = set()
        for event in stream:
            if event.type != "console":
                continue
            if event.stream == "stdout":
                stdout_words.add(event.message.strip())
            elif event.stream == "stderr":
                stderr_words.add(event.message.strip())
        self.assertEqual(stdout_words, {"foo"} if lang == "python" else {"foo", "bing"})
        self.assertEqual(stderr_words, {"bar"} if lang == "python" else {"bar", "baz"})
