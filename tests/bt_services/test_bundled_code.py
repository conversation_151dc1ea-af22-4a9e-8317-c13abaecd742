import json
import os
import subprocess
import tempfile
import unittest
from pprint import pprint

import braintrust
import requests
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, TEST_ARGS, BraintrustAppTestBase

TEST_DIR = os.path.dirname(os.path.abspath(os.path.realpath(__file__)))


class BundledCodeTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.proxy_url = braintrust.logger._state.proxy_url

        # All of these tests depend on code-bundle uploading through S3 being
        # set up.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("S3 is not available")

    def tearDown(self):
        if TEST_ARGS.get("update"):
            requests.get(f"{self.proxy_url}/proxy/dump-cache")
        super().tearDown()

    def test_bundled_code(self):
        task_file = os.path.join(TEST_DIR, "expect_tests", "simple_factuality.eval.ts")
        process = subprocess.Popen(
            ["npx", "braintrust", "eval", task_file, "--bundle", "--jsonl"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode

        print("--- STDOUT")
        print("stdout", stdout)
        print("--- STDERR")
        print("stderr", stderr)
        print("--- END")
        if ret != 0:
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # Store stdout in a variable for further processing if needed
        output = stdout
        summary = json.loads(output)

        project_id = summary["projectId"]
        experiment_id = summary["experimentId"]

        # Find the functions corresponding to the experiment
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                  from: project_functions('{project_id}')
                | select: *
                | filter: origin.object_type='experiment' AND origin.object_id='{experiment_id}' """
            },
        )
        functions = resp.json()["data"]
        assert len(functions) == 2, functions

        task_function = [f for f in functions if f["function_data"]["data"]["location"]["position"]["type"] == "task"][
            0
        ]
        eval_function = [
            f for f in functions if f["function_data"]["data"]["location"]["position"]["type"] == "scorer"
        ][0]

        self.assertEqual(task_function["function_type"], "task")
        self.assertEqual(task_function["origin"]["internal"], True)
        self.assertEqual(eval_function["function_type"], "scorer")
        self.assertEqual(eval_function["origin"]["internal"], True)

        # try some really basic inputs on the eval function
        result = braintrust.invoke(
            project_name="simple-factuality",
            function_id=task_function["id"],
            input="return just the following string: a123",
        )
        self.assertEqual(result, "a123")

        result = braintrust.invoke(
            project_name="simple-factuality",
            function_id=eval_function["id"],
            input={"input": "what is 1+1", "output": "2", "expected": "2"},
        )
        self.assertEqual(result["score"], 1)

        # Specify an org name explicitly (and use the REST API to do that)
        result = self.run_request(
            "post",
            f"{self.proxy_url}/function/invoke",
            json={
                "project_name": "simple-factuality",
                "function_id": task_function["id"],
                "input": "return just the following string: a123",
            },
            headers={"x-bt-org-name": self.org_name, "Authorization": f"Bearer {self.org_api_key}"},
        )
        result_json = result.json()
        self.assertEqual(result_json, "a123")

        # Install a dataset, and then run an eval through the REST API
        dataset = braintrust.init_dataset(project="simple-factuality", name="foo")
        dataset.insert(input="what is 1+1", expected="2")
        dataset.insert(input="what is 1+2", expected="3")
        dataset.flush()

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": project_id,
                "data": {
                    "dataset_id": dataset.id,
                },
                "task": {"function_id": task_function["id"]},
                "scores": [{"function_id": eval_function["id"]}],
            },
        )
        result = resp.json()
        self.assertGreater(result["scores"]["Factuality"]["score"], 0)

    def test_bundled_code_latest(self):
        task_file = os.path.join(TEST_DIR, "expect_tests", "simple_factuality.eval.ts")
        process = subprocess.Popen(
            ["npx", "braintrust", "eval", task_file, "--bundle", "--jsonl", "--push"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode

        print("stdout", stdout)
        print("stderr", stderr)
        if ret != 0:
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # Store stdout in a variable for further processing if needed
        output = stdout
        summary = json.loads(output)

        project_id = summary["projectId"]
        experiment_id = summary["experimentId"]

        # Find the functions corresponding to the experiment
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                  from: project_functions('{project_id}')
                | select: *
                | filter: origin.object_type='experiment' AND origin.object_id='{experiment_id}' """
            },
        )
        functions = resp.json()["data"]
        assert len(functions) == 2, functions

        task_function = [f for f in functions if f["function_data"]["data"]["location"]["position"]["type"] == "task"][
            0
        ]
        eval_function = [
            f for f in functions if f["function_data"]["data"]["location"]["position"]["type"] == "scorer"
        ][0]

        self.assertEqual(task_function["function_type"], "task")
        self.assertEqual(task_function["origin"]["internal"], False)
        self.assertEqual(eval_function["function_type"], "scorer")
        self.assertEqual(eval_function["origin"]["internal"], False)

    @parameterized.expand([(pkg,) for pkg in ["autoevals", "requests", "openai"]])
    def test_push_py_default_packages(self, pkg):
        # It is prudent to run this test with lambda execution enabled.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        with tempfile.NamedTemporaryFile("w", dir=TEST_DIR, suffix=".py") as f:
            f.write(
                """\
import braintrust
import pydantic
import %s

project = braintrust.projects.create(name="tool")


class Input(pydantic.BaseModel):
    a: int
    b: int


project.tools.create(
    handler=lambda a, b: a + b,
    name="Adder",
    slug="adder",
    description="A simple adder",
    parameters=Input,
)
"""
                % (pkg,)
            )
            f.flush()
            process = subprocess.Popen(
                ["braintrust", "push", f.name],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                print("--- STDOUT")
                print("stdout", stdout)
                print("--- STDERR")
                print("stderr", stderr)
                print("--- END")
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        resp = braintrust.invoke(
            project_name="tool",
            slug="adder",
            input={"a": 1, "b": 1},
        )
        self.assertEqual(resp, 2)

    @parameterized.expand([(filename, lang) for filename in ["code_scorer", "llm_scorer"] for lang in ["ts", "py"]])
    def test_push_scorer(self, filename, lang):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        task_file = os.path.join(TEST_DIR, f"{filename}.{lang}")
        if lang == "ts":
            subprocess.run(
                ["npx", "tsc", task_file, "--strict", "--noEmit", "--isolatedModules", "--skipLibCheck"], check=True
            )
            process = subprocess.Popen(
                ["npx", "braintrust", "push", task_file],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
        else:
            process = subprocess.Popen(
                ["braintrust", "push", task_file],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )

        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            print("--- STDOUT")
            print("stdout", stdout)
            print("--- STDERR")
            print("stderr", stderr)
            print("--- END")
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        resp = braintrust.invoke(
            project_name="scorer",
            slug="equality-scorer",
            input={"output": "hello", "expected": "hello"},
        )
        if isinstance(resp, dict):
            resp = resp["score"]
        self.assertEqual(resp, 1)

    def test_push_scorer_py_bundled(self):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        task_file = os.path.join(TEST_DIR, "scorer_nltk.py")
        sdk_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(braintrust.__file__))))
        with tempfile.TemporaryDirectory() as td:
            req_path = os.path.join(td, "requirements.txt")
            with open(req_path, "w") as f:
                f.write(
                    f"""\
annotated-types==0.7.0
braintrust @ file://{sdk_path}
braintrust_core==0.0.54
certifi==2024.8.30
charset-normalizer==3.4.0
chevron==0.14.0
click==8.1.7
exceptiongroup==1.2.2
gitdb==4.0.11
GitPython==3.1.43
idna==3.10
joblib==1.4.2
nltk==3.9.1
pydantic==2.10.3
pydantic_core==2.27.1
python-dotenv==1.0.1
python-slugify==8.0.4
regex==2024.11.6
requests==2.32.3
smmap==5.0.1
sseclient-py==1.8.0
text-unidecode==1.3
tqdm==4.67.1
typing_extensions==4.12.2
urllib3==2.2.3
"""
                )
            process = subprocess.Popen(
                ["braintrust", "push", task_file, "--requirements", req_path],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )

            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                print("--- STDOUT")
                print("stdout", stdout)
                print("--- STDERR")
                print("stderr", stderr)
                print("--- END")
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        resp = braintrust.invoke(
            project_name="scorer",
            slug="edit-distance-scorer",
            input={"output": "hello", "expected": "world"},
        )
        self.assertAlmostEqual(resp, 0.2, delta=0.001)

    def test_push_async_python_scorer(self):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("S3 is not available")

        # Write a temporary Python scorer file with an async handler
        with tempfile.NamedTemporaryFile("w", dir=TEST_DIR, suffix=".py") as f:
            f.write(
                """\
import braintrust
import asyncio
from pydantic import BaseModel

project = braintrust.projects.create(name="async_scorer")

class Input(BaseModel):
    output: str
    expected: str

async def handler(output: str, expected: str) -> float:
    await asyncio.sleep(0.01)
    return 1.0 if output == expected else 0.0

project.scorers.create(
    handler=handler,
    name="Async Equality Scorer",
    slug="async-equality-scorer",
    parameters=Input,
)
"""
            )
            f.flush()
            # Push the scorer (this uses the bundled code path)
            process = subprocess.Popen(
                ["braintrust", "push", f.name],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                print("--- STDOUT")
                print("stdout", stdout)
                print("--- STDERR")
                print("stderr", stderr)
                print("--- END")
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # Now invoke the scorer and check the result
        resp = braintrust.invoke(
            project_name="async_scorer",
            slug="async-equality-scorer",
            input={"output": "foo", "expected": "foo"},
        )
        self.assertEqual(resp, 1.0)
        resp = braintrust.invoke(
            project_name="async_scorer",
            slug="async-equality-scorer",
            input={"output": "foo", "expected": "bar"},
        )
        self.assertEqual(resp, 0.0)

    @parameterized.expand(
        [
            ("scorer-nocompile-tools", ".ts"),
            ("scorer-nocompile-params", ".ts"),
            ("scorer-nocompile-mix-args", ".py"),
            ("scorer-nocompile-mix-llm", ".py"),
            ("scorer-nocompile-incomplete-llm", ".py"),
            ("prompt-nocompile-no-prompt", ".py"),
            ("prompt-nocompile-prompt-and-messages", ".py"),
            ("prompt-nocompile-bad-params", ".py"),
        ]
    )
    def test_nocompile(self, filename, ext):
        path = os.path.join(TEST_DIR, filename + ext)
        if ext == ".ts":
            args = ["npx", "tsc", path, "--strict", "--noEmit", "--isolatedModules", "--skipLibCheck"]
        else:
            args = ["npx", "pyright", "--level", "error", path]
        ret = subprocess.run(
            args,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )
        # Temporarily remove the assertion since this is observed to be flaky on CI.
        # self.assertNotEqual(ret.returncode, 0)

    def test_push_with_imports(self):
        task_file = os.path.join(TEST_DIR, "prompt_import_tools_project", "subdir", "prompt_import_tools.py")
        process = subprocess.Popen(
            ["braintrust", "push", task_file],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            print("--- STDOUT")
            print("stdout", stdout)
            print("--- STDERR")
            print("stderr", stderr)
            print("--- END")
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        def check_formula_tool(formula, tool, value):
            resp = braintrust.invoke(
                project_name="prompt with tools",
                slug="calculator",
                input={"formula": formula},
                stream=True,
            )
            resp_copy = resp.copy()
            seen_tool = False
            for chunk in resp:
                if chunk.type == "progress" and chunk.name == tool:
                    seen_tool = True

            self.assertTrue(seen_tool)
            self.assertEqual(resp_copy.final_value(), value)

        check_formula_tool("2+3", "adder", "5")
        check_formula_tool("2*3", "multiplier", "6")
        check_formula_tool("2^3", "exponentiator", "8")
