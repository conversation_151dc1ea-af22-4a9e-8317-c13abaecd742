import braintrust
import requests

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url


def get_request_headers(api_key):
    return dict(Authorization=f"Bearer {api_key}")


class AclPermissionsTest(BraintrustAppTestBase):
    def _checked_request(self, verb, api_key, *args, expect_error=False, **kwargs):
        return self.run_request(
            verb, *args, expect_error=expect_error, **{"headers": get_request_headers(api_key), **kwargs}
        )

    def test_create_project_acl(self):
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()

        # A user with no create permissions should not be able to create a
        # project.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self._checked_request("post", user_api_key, make_v1_url("project"), expect_error=True, json=dict(name="test2"))

        # But if we grant them a create ACL, it should work out.
        acl_obj = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="create"),
        ).json()
        self.assertEqual(acl_obj["object_type"], "organization")
        self.assertEqual(acl_obj["object_id"], self.org_id)
        self.assertEqual(acl_obj["user_id"], user_id)
        self.assertEqual(acl_obj["permission"], "create")
        self.assertEqual(acl_obj["_object_org_id"], self.org_id)

        project2 = self._checked_request("post", user_api_key, make_v1_url("project"), json=dict(name="test2")).json()

        # They should be an owner of the project, which means they should be
        # able to read, update, and delete it.
        project2_id_url = make_v1_url(f"project/{project2['id']}")
        project2_ret = self._checked_request("get", user_api_key, project2_id_url).json()
        self.assertEqual(project2["id"], project2_ret["id"])
        project2_ret = self._checked_request("patch", user_api_key, project2_id_url, json=dict(name="Why then")).json()
        self.assertEqual(project2_ret["name"], "Why then")
        project2_ret = self._checked_request("delete", user_api_key, project2_id_url).json()
        self.assertIsNotNone(project2_ret["deleted_at"])

        # They should also be able to CRD ACLs on their own project.
        project2 = self._checked_request("post", user_api_key, make_v1_url("project"), json=dict(name="test2")).json()
        project2_id_url = make_v1_url(f"project/{project2['id']}")
        project2 = self._checked_request("get", user_api_key, project2_id_url).json()
        user2_id, _, user2_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self._checked_request("get", user2_api_key, project2_id_url, expect_error=True)
        acl_obj = self._checked_request(
            "post",
            user_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project2["id"], user_id=user2_id, permission="read"),
        ).json()
        acl_obj = self._checked_request("get", user_api_key, make_v1_url(f"acl/{acl_obj['id']}")).json()
        project2_from_user2 = self._checked_request("get", user2_api_key, project2_id_url).json()
        self.assertEqual(project2, project2_from_user2)
        self._checked_request("delete", user_api_key, make_v1_url(f"acl/{acl_obj['id']}"))
        self._checked_request("get", user2_api_key, project2_id_url, expect_error=True)

    def test_create_dataset_acl(self):
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()

        # A user with no create permissions should not be able to create a
        # dataset within the project.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self._checked_request(
            "post",
            user_api_key,
            make_v1_url("dataset"),
            expect_error=True,
            json=dict(name="test2", project_id=project["id"]),
        )

        # Try to request against the API directly so that we can register by
        # project name instead of id, thus requiring a call to
        # get_or_create_project.
        #
        # This should require both a create grant on datasets AND a read grant
        # on the project.
        self.grant_acl(
            dict(
                object_type="project",
                object_id=project["id"],
                user_id=user_id,
                permission="create",
                restrict_object_type="dataset",
            ),
        )
        self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/api/dataset/register",
            json=dict(dataset_name="test2", project_name=project["name"]),
            expect_error=True,
        )
        self.grant_acl(
            dict(
                object_type="project",
                object_id=project["id"],
                user_id=user_id,
                permission="read",
                restrict_object_type="project",
            ),
        )
        dataset = self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/api/dataset/register",
            json=dict(dataset_name="test2", project_name=project["name"]),
        ).json()["dataset"]
        self.assertEqual(dataset["name"], "test2")
        self.assertEqual(dataset["user_id"], user_id)

    def test_get_acls(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user_id2, _, user2_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Create some ACLs on the org for the user.
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="read"),
        )
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="update"),
        )

        # We should be able to list them with limits and pagination. Update
        # should come first, then read.
        acl_obj0 = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl"),
            params=dict(object_type="organization", object_id=self.org_id, limit=1),
        ).json()["objects"][0]
        acl_obj1 = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl"),
            params=dict(object_type="organization", object_id=self.org_id, limit=1, starting_after=acl_obj0["id"]),
        ).json()["objects"][0]
        self.assertEqual(acl_obj0["permission"], "update")
        self.assertEqual(acl_obj1["permission"], "read")

        # Test filtering by user_id
        filtered_by_user = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl"),
            params=dict(object_type="organization", object_id=self.org_id, user_id=user_id),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_user), 2)
        self.assertTrue(all(acl["user_id"] == user_id for acl in filtered_by_user))

        filtered_by_user = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl"),
            params=dict(object_type="organization", object_id=self.org_id, user_id=user_id2),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_user), 0)

        # Test filtering by permission
        filtered_by_permission = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl"),
            params=dict(object_type="organization", object_id=self.org_id, permission="read"),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_permission), 1)
        self.assertEqual(filtered_by_permission[0]["permission"], "read")

        # The extra user should not be able to read acls, until we grant them
        # that permission.
        user_acl_objects = self._checked_request(
            "get", user_api_key, make_v1_url("acl"), params=dict(object_type="organization", object_id=self.org_id)
        ).json()
        self.assertEqual(user_acl_objects, dict(objects=[]))
        new_acl_obj = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="read_acls"),
        ).json()
        new_acl_obj = self._checked_request("get", self.org_api_key, make_v1_url(f"acl/{new_acl_obj['id']}")).json()

        user_acl_objects = self._checked_request(
            "get", user_api_key, make_v1_url("acl"), params=dict(object_type="organization", object_id=self.org_id)
        ).json()["objects"]
        self.assertEqual(
            [x for x in user_acl_objects if x.get("user_id") == user_id], [new_acl_obj, acl_obj0, acl_obj1]
        )

    def test_create_delete_acls(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # Create an ACL on the org for the user.
        acl_obj0 = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="create_acls"),
        ).json()
        acl_obj0 = self._checked_request("get", self.org_api_key, make_v1_url(f"acl/{acl_obj0['id']}")).json()
        # They can't delete the ACL without the delete_acls permission.
        self._checked_request(
            "delete",
            user_api_key,
            make_v1_url(f"acl/{acl_obj0['id']}"),
            expect_error=True,
        )
        # But they can give themselves the delete_acls permission :)
        self._checked_request(
            "post",
            user_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="delete_acls"),
        )
        deleted_acl_obj0 = self._checked_request(
            "delete",
            user_api_key,
            make_v1_url(f"acl/{acl_obj0['id']}"),
        ).json()
        self.assertEqual(acl_obj0, deleted_acl_obj0)

    def test_rud(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()
        dataset = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("dataset"),
            json=dict(name="test", description="why then", project_id=project["id"]),
        ).json()
        dataset = self._checked_request("get", self.org_api_key, make_v1_url(f"dataset/{dataset['id']}")).json()

        # Can't read the dataset until we grant them read permissions. Grant it
        # on the organization to check the inheritance logic.
        self._checked_request("get", user_api_key, make_v1_url(f"dataset/{dataset['id']}"), expect_error=True)
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="read"),
        )
        user_dataset = self._checked_request("get", user_api_key, make_v1_url(f"dataset/{dataset['id']}")).json()
        self.assertEqual(dataset, user_dataset)

        # Can't update the dataset until we grant them permission. Grant it on
        # the project to check the inheritance logic.
        self._checked_request(
            "patch",
            user_api_key,
            make_v1_url(f"dataset/{dataset['id']}"),
            json=dict(description="who's there"),
            expect_error=True,
        )
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project["id"], user_id=user_id, permission="update"),
        )
        user_dataset = self._checked_request(
            "patch", user_api_key, make_v1_url(f"dataset/{dataset['id']}"), json=dict(description="who's there")
        ).json()
        self.assertEqual(user_dataset["description"], "who's there")

        # Can't delete the dataset until we grant them permission. Grant it on
        # the dataset.
        self._checked_request("delete", user_api_key, make_v1_url(f"dataset/{dataset['id']}"), expect_error=True)
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project["id"], user_id=user_id, permission="delete"),
        )
        user_dataset = self._checked_request("delete", user_api_key, make_v1_url(f"dataset/{dataset['id']}")).json()
        self.assertIsNotNone(user_dataset["deleted_at"])

    def test_restrict_object_type(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()
        experiment = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("experiment"),
            json=dict(name="test_exp", description="hey you", project_id=project["id"]),
        ).json()
        experiment = self._checked_request(
            "get", self.org_api_key, make_v1_url(f"experiment/{experiment['id']}")
        ).json()
        dataset = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("dataset"),
            json=dict(name="test_dat", description="why then", project_id=project["id"]),
        ).json()
        dataset = self._checked_request("get", self.org_api_key, make_v1_url(f"dataset/{dataset['id']}")).json()

        # Give the user read permission to everything in the project, but update
        # permission only on datasets.
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project["id"], user_id=user_id, permission="read"),
        )
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(
                object_type="project",
                object_id=project["id"],
                user_id=user_id,
                permission="update",
                restrict_object_type="dataset",
            ),
        )
        user_experiment = self._checked_request(
            "get", user_api_key, make_v1_url(f"experiment/{experiment['id']}")
        ).json()
        self.assertEqual(experiment, user_experiment)
        user_dataset = self._checked_request("get", user_api_key, make_v1_url(f"dataset/{dataset['id']}")).json()
        self.assertEqual(dataset, user_dataset)

        with self.assertRaises(Exception):
            self._checked_request(
                "patch",
                user_api_key,
                make_v1_url(f"experiment/{experiment['id']}"),
                json=dict(description="knock knock"),
            )
        new_experiment = self._checked_request(
            "get", self.org_api_key, make_v1_url(f"experiment/{experiment['id']}")
        ).json()
        self.assertEqual(new_experiment["description"], experiment["description"])

        self._checked_request(
            "patch", user_api_key, make_v1_url(f"dataset/{dataset['id']}"), json=dict(description="knock knock")
        )
        new_dataset = self._checked_request("get", self.org_api_key, make_v1_url(f"dataset/{dataset['id']}")).json()
        self.assertEqual(new_dataset["description"], "knock knock")

    def test_fetch_insert_permissions(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()

        # Without any permissions, the user should not be able to insert
        # anything.
        with self.assertRaises(Exception):
            self._checked_request(
                "post",
                user_api_key,
                make_v1_url(f"project_logs/{project['id']}/insert"),
                json=dict(
                    events=[
                        dict(
                            input="foo",
                        ),
                    ],
                ),
            )

        # Give the user update permission on the project.
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project["id"], user_id=user_id, permission="update"),
        )

        # Now the write should succeed.
        inserted_ids = self._checked_request(
            "post",
            user_api_key,
            make_v1_url(f"project_logs/{project['id']}/insert"),
            json=dict(
                events=[
                    dict(
                        input="foo",
                    ),
                ],
            ),
        ).json()
        self.assertEqual(len(inserted_ids["row_ids"]), 1)
        row_id = inserted_ids["row_ids"][0]

        # We should also be able to merge-update.
        updated_ids = self._checked_request(
            "post",
            user_api_key,
            make_v1_url(f"project_logs/{project['id']}/insert"),
            json=dict(
                events=[
                    dict(
                        id=row_id,
                        input="bar",
                        _is_merge=True,
                    ),
                ],
            ),
        ).json()
        self.assertEqual(inserted_ids, updated_ids)

        # We shouldn't be able to fetch until we get "read" permissions.
        with self.assertRaises(Exception):
            self._checked_request("get", user_api_key, make_v1_url(f"project_logs/{project['id']}/fetch"))
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project["id"], user_id=user_id, permission="read"),
        )

        events = self._checked_request("get", user_api_key, make_v1_url(f"project_logs/{project['id']}/fetch")).json()[
            "events"
        ]
        self.assertEqual(events[0]["id"], row_id)
        self.assertEqual(events[0]["input"], "bar")

        # We shouldn't be able to delete the row until we get "delete"
        # permissions.
        with self.assertRaises(Exception):
            self._checked_request(
                "post",
                user_api_key,
                make_v1_url(f"project_logs/{project['id']}/insert"),
                json=dict(
                    events=[
                        dict(
                            id=row_id,
                            _object_delete=True,
                        ),
                    ],
                ),
            )

        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="project_log", object_id=project["id"], user_id=user_id, permission="delete"),
        )
        self._checked_request(
            "post",
            user_api_key,
            make_v1_url(f"project_logs/{project['id']}/insert"),
            json=dict(
                events=[
                    dict(
                        id=row_id,
                        _object_delete=True,
                    ),
                ],
            ),
        )

        # Now fetching should return an empty result.
        events = self._checked_request("get", user_api_key, make_v1_url(f"project_logs/{project['id']}/fetch")).json()[
            "events"
        ]
        self.assertEqual(events, [])

    def test_org_projects(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # The original org user can create projects and experiments.
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()
        experiment = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("experiment"),
            json=dict(name="test_exp", project_id=project["id"]),
        ).json()

        # But the new user cannot.
        self._checked_request("post", user_api_key, make_v1_url("project"), json=dict(name="test"), expect_error=True)

        # If the user has permissions through 'org_project', they should be able
        # to do anything to any project-or-subobject. But they cannot do things
        # to sibling objects like groups.
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="org_project", object_id=self.org_id, user_id=user_id, permission="create"),
        )
        self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(object_type="org_project", object_id=self.org_id, user_id=user_id, permission="read"),
        )

        obj = self._checked_request("get", user_api_key, make_v1_url(f"project/{project['id']}")).json()
        self.assertEqual(obj["id"], project["id"])
        obj = self._checked_request("get", user_api_key, make_v1_url(f"experiment/{experiment['id']}")).json()
        self.assertEqual(obj["id"], experiment["id"])
        self._checked_request("delete", user_api_key, make_v1_url(f"project/{project['id']}"), expect_error=True)
        self._checked_request("delete", user_api_key, make_v1_url(f"experiment/{experiment['id']}"), expect_error=True)

        user_project = self._checked_request(
            "post", user_api_key, make_v1_url("project"), json=dict(name="test2")
        ).json()
        self.assertEqual(user_project["name"], "test2")
        user_experiment = self._checked_request(
            "post", user_api_key, make_v1_url("experiment"), json=dict(name="test_exp2", project_id=project["id"])
        ).json()
        self.assertEqual(user_experiment["name"], "test_exp2")
        self.assertEqual(user_experiment["project_id"], project["id"])

    def test_batch_update(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select get_group_id(%s, 'Owners'), get_owner_role_id()", (self.org_id,))
                org_owners_group_id, owner_role_id = cursor.fetchone()

        group_read_acl = dict(
            object_type="organization",
            object_id=self.org_id,
            group_id=org_owners_group_id,
            permission="read",
            restrict_object_type="experiment",
        )
        user_owner_acl = dict(
            object_type="organization",
            object_id=self.org_id,
            user_id=self.user_id,
            role_id=owner_role_id,
        )

        # Register multiple ACLs in one request.
        resp = self._checked_request(
            "post",
            self.org_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(add_acls=[group_read_acl, user_owner_acl]),
        )
        register_body = resp.json()
        body_acls_cleaned = sorted(
            [
                {k: v for k, v in acl.items() if k not in ["id", "_object_org_id", "created"] and v}
                for acl in register_body["added_acls"]
            ],
            key=lambda x: x.get("restrict_object_type") == "experiment",
        )
        self.assertEqual(
            body_acls_cleaned,
            [user_owner_acl, group_read_acl],
        )
        self.assertEqual(register_body["removed_acls"], [])

        # Unprivileged user cannot register ACLs.
        self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=[group_read_acl],
            ),
            expect_error=True,
        )

        # Unprivileged user cannot delete ACLs.
        self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(remove_acls=[group_read_acl]),
            expect_error=True,
        )

        # But the privileged user can.
        resp = self._checked_request(
            "post",
            self.org_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                remove_acls=[group_read_acl, user_owner_acl],
            ),
        )
        self.assertEqual(resp.json()["added_acls"], [])
        self.assertEqual(resp.json()["removed_acls"], register_body["added_acls"])

        # Empty request returns empty result for both kinds of user.
        resp = self._checked_request(
            "post",
            self.org_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(add_acls=[]),
        )
        self.assertIsNone(resp.json())
        resp = self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(remove_acls=[]),
        )
        self.assertIsNone(resp.json())

        # Grant the unprivileged user access to create acls only.
        user_create_acls_acl = dict(
            object_type="organization", object_id=self.org_id, user_id=user_id, permission="create_acls"
        )
        self.grant_acl(user_create_acls_acl)

        # The unprivileged user should now be able to create an ACL. Try
        # creating it on a different object to exercise the multi-object
        # modification.
        org_project_group_read_acl = {
            **group_read_acl,
            "object_type": "org_project",
        }
        org_project_user_owner_acl = {
            **user_owner_acl,
            "object_type": "org_project",
        }
        resp = self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(add_acls=[org_project_group_read_acl]),
        )
        self.assertEqual(len(resp.json()["added_acls"]), 1)
        self.assertEqual(resp.json()["removed_acls"], [])

        # But if they try to create and remove an ACL in the same operation,
        # nothing will happen.
        self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=[user_owner_acl],
                remove_acls=[org_project_group_read_acl],
            ),
            expect_error=True,
        )

        # The privileged user can do this though.
        resp = self._checked_request(
            "post",
            self.org_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=[org_project_user_owner_acl],
                remove_acls=[user_create_acls_acl],
            ),
        )
        self.assertEqual(len(resp.json()["added_acls"]), 1)
        self.assertEqual(resp.json()["added_acls"][0]["object_type"], "org_project")
        self.assertEqual(len(resp.json()["removed_acls"]), 1)

        # Grant the unprivileged user access to delete acls only.
        user_delete_acls_acl = dict(
            object_type="organization", object_id=self.org_id, user_id=user_id, permission="delete_acls"
        )
        self.grant_acl(user_delete_acls_acl)

        # The unprivileged user should now be able to delete an ACL.
        resp = self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(remove_acls=[org_project_group_read_acl]),
        )
        self.assertEqual(resp.json()["added_acls"], [])
        self.assertEqual(len(resp.json()["removed_acls"]), 1)
        self.assertEqual(resp.json()["removed_acls"][0]["object_type"], "org_project")

        # They still cannot do both operations.
        self._checked_request(
            "post",
            user_api_key,
            f"{LOCAL_API_URL}/v1/acl/batch_update",
            json=dict(
                add_acls=[org_project_user_owner_acl],
                remove_acls=[group_read_acl],
            ),
            expect_error=True,
        )

    def test_read_project_prompts(self):
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()
        prompt0 = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("prompt"),
            json=dict(name="example", project_id=project["id"], slug="my_prompt0"),
        ).json()
        prompt1 = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("prompt"),
            json=dict(name="example", project_id=project["id"], slug="my_prompt1"),
        ).json()

        # The privileged user should be able to read all the project prompts.
        REQUEST_URL = f"{LOCAL_API_URL}/btql"
        QUERY_STR = f"select: * from: project_prompts('{project['id']}')"
        resp = self.run_request("post", REQUEST_URL, json=dict(query=QUERY_STR)).json()
        self.assertEqual(set(x["id"] for x in resp["data"]), {prompt0["id"], prompt1["id"]})

        # The unprivileged user should not.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user_request_headers = {"Authorization": f"Bearer {user_api_key}"}
        resp = self.run_request(
            "post", REQUEST_URL, headers=user_request_headers, json=dict(query=QUERY_STR), expect_error=True
        )

        # Even if we grant them restricted read permission on the project (but
        # not the prompts).
        self.grant_acl(
            dict(
                object_type="project",
                object_id=project["id"],
                user_id=user_id,
                permission="read",
                restrict_object_type="project",
            ),
        )
        resp = self.run_request(
            "post", REQUEST_URL, headers=user_request_headers, json=dict(query=QUERY_STR), expect_error=True
        )

        # But if they get restricted read permission on the prompts, it should
        # be okay.
        self.grant_acl(
            dict(
                object_type="project",
                object_id=project["id"],
                user_id=user_id,
                permission="read",
                restrict_object_type="prompt",
            ),
        )
        resp = self.run_request("post", REQUEST_URL, headers=user_request_headers, json=dict(query=QUERY_STR)).json()
        self.assertEqual(set(x["id"] for x in resp["data"]), {prompt0["id"], prompt1["id"]})

    def test_delete_acl(self):
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()

        # Initially the user lacks update permissions on the project.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self._checked_request(
            "patch", user_api_key, make_v1_url(f"project/{project['id']}"), expect_error=True, json=dict(name="test2")
        )

        # If we grant them update permission, it should work out.
        self.grant_acl(
            dict(object_type="project", object_id=project["id"], user_id=user_id, permission="update"),
        )
        project = self._checked_request(
            "patch", user_api_key, make_v1_url(f"project/{project['id']}"), json=dict(name="test2")
        ).json()
        self.assertEqual(project["name"], "test2")

        # If we revoke that update permission, it should fail again.
        self.run_request(
            "delete",
            make_v1_url("acl"),
            json=dict(object_type="project", object_id=project["id"], user_id=user_id, permission="update"),
        )
        self._checked_request(
            "patch", user_api_key, make_v1_url(f"project/{project['id']}"), expect_error=True, json=dict(name="test3")
        )

    def test_org_level_queries(self):
        project0 = self._checked_request("post", self.org_api_key, make_v1_url("project"), json=dict(name="p0")).json()
        project1 = self._checked_request("post", self.org_api_key, make_v1_url("project"), json=dict(name="p1")).json()

        def make_prompt(project, slug):
            return self._checked_request(
                "post",
                self.org_api_key,
                make_v1_url("prompt"),
                json=dict(project_id=project["id"], name="example", slug=slug),
            )

        def make_log(project, log_id):
            return self._checked_request(
                "post",
                self.org_api_key,
                make_v1_url(f"project_logs/{project['id']}/insert"),
                json=dict(
                    events=[
                        dict(
                            id=log_id,
                            input="foo",
                        ),
                    ]
                ),
            )

        prompt0_id = make_prompt(project0, "prompt0").json()["id"]
        prompt1_id = make_prompt(project1, "prompt1").json()["id"]
        log0_id = make_log(project0, "log0").json()["row_ids"][0]
        log1_id = make_log(project1, "log1").json()["row_ids"][0]

        def query_org_prompts(api_key, expect_error=False):
            return self._checked_request(
                "post",
                api_key,
                f"{LOCAL_API_URL}/btql",
                json=dict(query=f'select: * from: org_prompts("{self.org_id}")'),
                expect_error=expect_error,
            )

        def query_all_project_logs(api_key, expect_error=False):
            return self._checked_request(
                "post",
                api_key,
                f"{LOCAL_API_URL}/btql",
                json=dict(query=f'select: * from: project_logs("{project0["id"]}", "{project1["id"]}")'),
                expect_error=expect_error,
            )

        def test_queries(api_key, expect_error=False, override_expect_error_project_logs=None):
            org_prompts = query_org_prompts(api_key, expect_error=expect_error)
            all_project_logs = query_all_project_logs(
                api_key,
                expect_error=(
                    override_expect_error_project_logs
                    if override_expect_error_project_logs is not None
                    else expect_error
                ),
            )
            if not expect_error:
                self.assertEqual(set(r["id"] for r in org_prompts.json()["data"]), {prompt0_id, prompt1_id})
                self.assertEqual(set(r["id"] for r in all_project_logs.json()["data"]), {log0_id, log1_id})

        test_queries(self.org_api_key)

        # Should also work with another user who is granted read permission at
        # the org-project level for project_log and prompts.
        org_project_user_id, _, org_project_user_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )
        self.grant_acl(
            dict(
                object_type="org_project",
                object_id=self.org_id,
                user_id=org_project_user_id,
                permission="read",
                restrict_object_type="prompt",
            ),
        )
        self.grant_acl(
            dict(
                object_type="org_project",
                object_id=self.org_id,
                user_id=org_project_user_id,
                permission="read",
                restrict_object_type="project_log",
            ),
        )
        test_queries(org_project_user_api_key)

        # Should not work if they only have individual project-level access.
        project_user_id, _, project_user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.grant_acl(
            dict(object_type="project", object_id=project0["id"], user_id=project_user_id, permission="read"),
        )
        self.grant_acl(
            dict(object_type="project", object_id=project1["id"], user_id=project_user_id, permission="read"),
        )
        # But actually the project-logs query should still work.
        test_queries(project_user_api_key, expect_error=True, override_expect_error_project_logs=False)

    def test_list_org_acls(self):
        # Get all org ACLs
        all_acls = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
        ).json()["objects"]

        # Verify we have the default ACLs
        self.assertEqual(len(all_acls), 3)
        # They should all have a group_id and role_id.
        for acl in all_acls:
            self.assertIn("group_id", acl)
            self.assertIn("role_id", acl)

        # Test filtering by group_id
        filtered_by_group = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(group_id=all_acls[0]["group_id"]),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_group), 1)
        self.assertEqual(filtered_by_group[0]["id"], all_acls[0]["id"])

        # Test filtering by role_id
        filtered_by_role = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(role_id=all_acls[0]["role_id"]),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_role), 1)
        self.assertEqual(filtered_by_role[0]["id"], all_acls[0]["id"])

        # Test filtering by ids.
        filtered_by_ids = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(ids=[all_acls[0]["id"], all_acls[1]["id"]]),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_ids), 2)
        self.assertEqual(set(x["id"] for x in filtered_by_ids), {all_acls[0]["id"], all_acls[1]["id"]})

        # Create a user with read_acls permission
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        read_acls_acl = self._checked_request(
            "post",
            self.org_api_key,
            make_v1_url("acl"),
            json=dict(
                object_type="organization",
                object_id=self.org_id,
                user_id=user_id,
                permission="read_acls",
                restrict_object_type="organization",
            ),
        ).json()

        # User should not be able to list all ACLs
        unprivileged_request = self._checked_request(
            "get",
            user_api_key,
            make_v1_url("acl/list_org"),
        ).json()["objects"]
        self.assertEqual(unprivileged_request, [])

        # But we should be able to see their ACL when filtering.
        filtered_by_user = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(user_id=user_id),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_user), 1)
        self.assertEqual(filtered_by_user[0]["id"], read_acls_acl["id"])

        # Should also be able to see it when filtering by permission
        filtered_by_permission = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(permission="read_acls"),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_permission), 1)
        self.assertEqual(filtered_by_permission[0]["id"], read_acls_acl["id"])

        # And when filtering by restrict_object_type
        filtered_by_type = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(restrict_object_type="organization"),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_type), 1)
        self.assertEqual(filtered_by_type[0]["id"], read_acls_acl["id"])

        # Create a project and grant ACLs. This should create an owner ACL on
        # the creating user on that project.
        project = self._checked_request(
            "post", self.org_api_key, make_v1_url("project"), json=dict(name="test")
        ).json()

        # Test filtering by object_type
        filtered_by_object_type = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(object_type="project"),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_object_type), 1)
        self.assertEqual(filtered_by_object_type[0]["user_id"], self.user_id)
        self.assertIn("role_id", filtered_by_object_type[0])
        self.assertIsNone(filtered_by_object_type[0].get("restrict_object_type"))

        # Test filtering by object_id
        filtered_by_object_id = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(object_id=project["id"]),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_object_id), 1)
        self.assertEqual(filtered_by_object_id[0]["id"], filtered_by_object_type[0]["id"])

        # Test filtering by user_id for project ACL
        filtered_by_project_user = self._checked_request(
            "get",
            self.org_api_key,
            make_v1_url("acl/list_org"),
            params=dict(user_id=self.user_id),
        ).json()["objects"]
        self.assertEqual(len(filtered_by_project_user), 1)
        self.assertEqual(filtered_by_project_user[0]["id"], filtered_by_object_type[0]["id"])
