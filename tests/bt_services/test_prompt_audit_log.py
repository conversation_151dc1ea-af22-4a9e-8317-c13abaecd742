import braintrust
import requests
from braintrust.db_fields import TRANSACTION_ID_FIELD
from braintrust.xact_ids import prettify_xact

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url


class PromptAuditLogTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.logger = braintrust.init_logger(project="prompt audit log test")
        self.project = self.logger.project

    def _get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def _insert_prompt(self, **kwargs):
        resp = requests.post(make_v1_url("prompt"), headers=self._get_request_headers(), json=kwargs)
        if not resp.ok:
            raise ValueError(resp.text)
        else:
            return resp.json()

    def _update_prompt(self, id, **kwargs):
        resp = requests.patch(make_v1_url("prompt", id), headers=self._get_request_headers(), json=kwargs)
        if not resp.ok:
            raise ValueError(resp.text)
        else:
            return resp.json()

    def test_get_prompt_versions_basic(self):
        """Test that get_prompt_versions returns transaction IDs for prompt versions"""
        slug = "audit-test-prompt"

        # Create initial prompt
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="audit test prompt",
            slug=slug,
        )
        prompt_id = prompt_record["id"]

        # Edit the prompt a few times
        self._update_prompt(
            prompt_id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 2+2"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
        )
        self._update_prompt(
            prompt_id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 3+3"}],
                },
                "options": {
                    "model": "gpt-4",
                },
            },
        )

        # Get audit log entries
        audit_log_entries = braintrust.get_prompt_versions(project_id=self.project.id, prompt_id=prompt_id)

        # Should have 3 entries (1 create + 2 updates)
        self.assertEqual(len(audit_log_entries), 3)

        # All entries should be transaction IDs (strings)
        for entry in audit_log_entries:
            self.assertIsInstance(entry, str)
            # Transaction IDs should be non-empty
            self.assertTrue(len(entry) > 0)

        # Test loading specific versions using the returned transaction IDs
        # Load each version and verify we can find the expected content variations
        loaded_versions = []
        for xact_id in audit_log_entries:
            prompt = braintrust.load_prompt(project=self.project.name, slug=slug, version=xact_id)
            self.assertIsNotNone(prompt)
            self.assertEqual(prompt.id, prompt_id)
            built = prompt.build()
            loaded_versions.append(
                {"content": built["messages"][0]["content"], "model": built["model"], "xact_id": xact_id}
            )

        # Verify we have all expected versions
        expected_versions = [
            {"content": "What is 1+1", "model": "gpt-3.5-turbo"},
            {"content": "What is 2+2", "model": "gpt-3.5-turbo"},
            {"content": "What is 3+3", "model": "gpt-4"},
        ]

        actual_content_model_pairs = [{"content": v["content"], "model": v["model"]} for v in loaded_versions]
        self.assertCountEqual(actual_content_model_pairs, expected_versions)

        # Also test loading the prompt without version (should get latest)
        prompt_default = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertIsNotNone(prompt_default)
        self.assertEqual(prompt_default.id, prompt_id)
        default_built = prompt_default.build()
        self.assertEqual(default_built["messages"][0]["content"], "What is 3+3")
        self.assertEqual(default_built["model"], "gpt-4")

    def test_get_prompt_versions_nonexistent_prompt(self):
        """Test that get_prompt_versions handles nonexistent prompts gracefully"""
        # Try to get audit log for a non-existent prompt
        audit_log_entries = braintrust.get_prompt_versions(
            project_id=self.project.id, prompt_id="nonexistent-prompt-id"
        )

        # Should return an empty list
        self.assertEqual(audit_log_entries, [])

    def test_get_prompt_versions_invalid_project(self):
        """Test that get_prompt_versions handles invalid project IDs gracefully"""
        # Try to get audit log with invalid project ID should raise an error
        with self.assertRaises(Exception) as context:
            braintrust.get_prompt_versions(project_id="invalid-project-id", prompt_id="any-function-id")

        # Should be a ForbiddenError
        self.assertIn("ForbiddenError", str(context.exception))
