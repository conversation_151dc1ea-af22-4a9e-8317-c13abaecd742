// This file is not a standalone test, but is called from test_function_tools.py
// to test bundling.
import braintrust from "braintrust";

const project = braintrust.projects.create({
  name: "function test",
});

project.tools.create({
  handler: async () => {
    return await braintrust.traced(async (span) => {
      span.log({ metadata: { foo: "bar" } });
      return "hello";
    });
  },
  name: "Tracing Tool",
  slug: "tracing-tool",
  ifExists: "replace",
});
