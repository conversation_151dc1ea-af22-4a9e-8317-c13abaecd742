import asyncio
import time
import unittest
from typing import Dict, Type

import autoevals
import braintrust
from braintrust import Eval, framework

from tests.braintrust_app_test_base import BraintrustAppTestBase


async def _slow_task_async(input):
    await asyncio.sleep(100)
    return input * 2


def _slow_task_sync(input):
    # The Python runtime will not exit the process until this task completes.
    # We set a smaller value so that the unit test remains fast.
    # If you are using this functionality, make sure to explicitly call os._exit from main.
    time.sleep(2)
    return input * 2


class FrameworkTest(unittest.IsolatedAsyncioTestCase):
    async def test_run_evaluator_async_task(self):
        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[{"input": 1, "expected": 2}],
            task=_slow_task_async,
            scores=[autoevals.NumericDiff],
            experiment_name=None,
            metadata=None,
            timeout=1,
        )
        with self.assertRaises(TimeoutError):
            await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

    async def test_run_evaluator_sync_task(self):
        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[{"input": 1, "expected": 2}],
            task=_slow_task_sync,
            scores=[autoevals.NumericDiff],
            experiment_name=None,
            metadata=None,
            timeout=1,
        )
        with self.assertRaises(TimeoutError):
            await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

    async def test_hooks_meta(self):
        metadata = {"bar": "baz", "foo": "bar"}

        def task(input: int, hooks: framework.EvalHooks):
            hooks.metadata["foo"] = "barbar"
            return input * 2

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[{"input": 1, "expected": 2, "metadata": metadata}],
            task=task,
            experiment_name=None,
            metadata=None,
            scores=[autoevals.NumericDiff],
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].metadata, {"bar": "baz", "foo": "barbar"})

    async def test_hooks_metadata(self):
        metadata = {"bar": "baz", "foo": "bar"}

        def task(input: int, hooks: framework.EvalHooks):
            hooks.metadata["foo"] = "barbar"
            return input * 2

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[{"input": 1, "expected": 2, "metadata": metadata}],
            task=task,
            experiment_name=None,
            metadata=None,
            scores=[autoevals.NumericDiff],
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].metadata, {"bar": "baz", "foo": "barbar"})

    async def test_hooks_expected(self):
        expected = {"bar": 1, "foo": 2}

        def task(input: int, hooks: framework.EvalHooks[Dict[str, int]]) -> Dict[str, int]:
            if hooks.expected is not None:
                hooks.expected["foo"] = 3

            return {"foo": input * 2}

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1, expected=expected)],
            task=task,
            metadata=None,
            scores=[],
            experiment_name=None,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].expected, {"bar": 1, "foo": 3})

    async def test_error_scoring_no_scores(self):
        def error_task(input):
            raise Exception("test error")

        def not_logged_scorer():
            return 1

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1)],
            task=error_task,
            metadata=None,
            scores=[not_logged_scorer],
            experiment_name=None,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].scores, {})

    async def test_error_handler_task_error(self):
        def error_task(input):
            raise Exception("test error")

        def unhandled_scorer():
            return 1

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1)],
            task=error_task,
            metadata=None,
            scores=[unhandled_scorer],
            experiment_name=None,
            error_score_handler=framework.default_error_score_handler,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].scores, {"unhandled_scorer": 0})

    async def test_error_handler_failing_scorers(self):
        def valid_task(input):
            return input

        def valid_scorer():
            return 1

        def will_fail_scorer():
            raise Exception("test error")

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1)],
            task=valid_task,
            metadata=None,
            scores=[valid_scorer, will_fail_scorer],
            experiment_name=None,
            error_score_handler=framework.default_error_score_handler,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].scores, {"valid_scorer": 1, "will_fail_scorer": 0})

    async def test_error_handler_noop(self):
        def error_task(input):
            raise Exception("test error")

        def not_logged_scorer():
            return 1

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1)],
            task=error_task,
            metadata=None,
            scores=[not_logged_scorer],
            experiment_name=None,
            error_score_handler=lambda root_span, data, unhandled_scores: None,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].scores, {})

    async def test_error_handler_arbitrary_score(self):
        def error_task(input):
            raise Exception("test error")

        def not_logged_scorer():
            return 1

        def arbitrary_score_handler(root_span, data, unhandled_scores):
            scores = {"error_score": 1}
            root_span.log(scores=scores)
            return scores

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1)],
            task=error_task,
            metadata=None,
            scores=[not_logged_scorer],
            experiment_name=None,
            error_score_handler=arbitrary_score_handler,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].scores, {"error_score": 1})

    async def test_position_arg_scorer(self):
        def scorer(*args, **kwargs):
            input = kwargs["input"]
            expected = kwargs["expected"]
            output = kwargs["output"]
            return 1 if input == 1 and expected == 2 and output == 3 else 0

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1, expected=2)],
            task=lambda input: 3,
            scores=[scorer],
            experiment_name=None,
            metadata=None,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])
        self.assertEqual(out.results[0].scores, {"scorer": 1})

    async def test_hooks_trial_index(self):
        trial_indices = []

        def task(input: int, hooks: framework.EvalHooks):
            trial_indices.append(hooks.trial_index)
            return input * 2

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[framework.EvalCase(input=1, expected=2)],
            task=task,
            experiment_name=None,
            metadata=None,
            scores=[],
            trial_count=3,
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        # Should have 3 trials with indices 0, 1, 2
        self.assertEqual(len(trial_indices), 3)
        self.assertEqual(sorted(trial_indices), [0, 1, 2])

    async def test_hooks_tags(self):
        tags = ["chocolate", "vanilla", "strawberry"]

        def task(input: int, hooks: framework.EvalHooks):
            hooks.tags = tags
            return input * 2

        e = framework.Evaluator(
            project_name="proj",
            eval_name="eval",
            data=[{"input": 1, "expected": 2}],
            task=task,
            experiment_name=None,
            metadata=None,
            scores=[autoevals.NumericDiff],
        )

        out = await framework.run_evaluator(experiment=None, evaluator=e, position=0, filters=[])

        self.assertEqual(out.results[0].tags, tags)


class TestEval(BraintrustAppTestBase):
    def test_project_id(self):
        logger = braintrust.init_logger(project="P1")
        project_id = logger.project.id

        eval_result = Eval("P2", data=[], task=lambda: "1", scores=[])
        self.assertEqual(eval_result.summary.project_name, "P2")

        eval_result = Eval("another project", data=[], task=lambda: "1", scores=[], project_id=project_id)
        self.assertEqual(eval_result.summary.project_name, "P1")
