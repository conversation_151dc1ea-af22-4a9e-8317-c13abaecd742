import json
from uuid import uuid4

import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def make_url(suffix):
    return f"{LOCAL_API_URL}/v1/{suffix}"


def get_decrypted_secret(id):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute("select decrypted_secret from secrets.decrypted_org_secrets where id=%s", (id,))
            return cursor.fetchone()[0]


class UserOrgSecretsTest(BraintrustAppTestBase):
    def test_basic(self):
        SECRET_VALUE = "bizBeans"
        ai_secret = self.run_request(
            "post",
            make_url("ai_secret"),
            json=dict(
                name="foobar",
                type="good",
                metadata={"foo": "bar"},
                secret=SECRET_VALUE,
            ),
        ).json()
        self.assertEqual(get_decrypted_secret(ai_secret["id"]), SECRET_VALUE)

        # Patch some params with/without the secret.
        for patch_param, patch_value in [("name", "goop"), ("type", "floop")]:
            ai_secret = self.run_request(
                "patch",
                make_url(f"ai_secret/{ai_secret['id']}"),
                json={patch_param: patch_value},
            ).json()
            self.assertEqual(get_decrypted_secret(ai_secret["id"]), SECRET_VALUE)
            self.assertEqual(ai_secret[patch_param], patch_value)

            SECRET_VALUE = SECRET_VALUE + "2"
            ai_secret = self.run_request(
                "patch",
                make_url(f"ai_secret/{ai_secret['id']}"),
                json={
                    patch_param: patch_value,
                    "secret": SECRET_VALUE,
                },
            ).json()
            self.assertEqual(get_decrypted_secret(ai_secret["id"]), SECRET_VALUE)
            self.assertEqual(ai_secret[patch_param], patch_value)

        # Deep-merge metadata.
        ai_secret = self.run_request(
            "patch",
            make_url(f"ai_secret/{ai_secret['id']}"),
            json=dict(
                metadata=dict(
                    beans="good",
                ),
            ),
        ).json()
        self.assertEqual(get_decrypted_secret(ai_secret["id"]), SECRET_VALUE)
        self.assertEqual(ai_secret["metadata"], dict(foo="bar", beans="good"))

    def test_unprivileged(self):
        ai_secret = self.run_request(
            "post",
            make_url("ai_secret"),
            json=dict(
                name="foobar",
                type="good",
                metadata={"foo": "bar"},
                secret="glue",
            ),
        ).json()

        # Another org member can read the secrets, but the cannot update them.
        unprivileged_user_id, _, self.unprivileged_user_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )
        unprivileged_headers = dict(Authorization=f"Bearer {self.unprivileged_user_api_key}")
        preview_secret = self.run_request(
            "get",
            make_url("ai_secret"),
            headers=unprivileged_headers,
            params=dict(ai_secret_name="foobar"),
        ).json()
        self.assertEqual(preview_secret["objects"][0]["id"], ai_secret["id"])
        self.run_request(
            "patch",
            make_url(f"ai_secret/{ai_secret['id']}"),
            headers=unprivileged_headers,
            json=dict(type="bad"),
            expect_error=True,
        )
        self.run_request(
            "delete",
            make_url(f"ai_secret/{ai_secret['id']}"),
            headers=unprivileged_headers,
            expect_error=True,
        )
