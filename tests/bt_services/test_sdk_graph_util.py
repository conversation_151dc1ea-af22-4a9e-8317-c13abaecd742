import unittest

from braintrust.graph_util import (
    UndirectedGraph,
    depth_first_search,
    topological_sort,
    undirected_connected_components,
)


class DFSVisitationCounter:
    def __init__(self, unittest_obj):
        self.unittest_obj = unittest_obj

        self.counter = 0
        self.first_visit_ord = {}
        self.last_visit_ord = {}

    def first_visit_f(self, vertex, parent_vertex, **kwargs):
        self.unittest_obj.assertNotIn(vertex, self.first_visit_ord)
        if parent_vertex is not None:
            self.unittest_obj.assertIn(parent_vertex, self.first_visit_ord)
        self.first_visit_ord[vertex] = self.counter
        self.counter += 1

    def last_visit_f(self, vertex, **kwargs):
        self.unittest_obj.assertNotIn(vertex, self.last_visit_ord)
        self.last_visit_ord[vertex] = self.counter
        self.counter += 1


class GraphUtilTest(unittest.TestCase):
    def test_depth_first_search(self):
        graph = {
            # Diamond with cycle.
            0: {1, 2},
            1: {3},
            2: {3},
            3: {0},
            # Disconnected vertex.
            4: {},
        }

        counter = DFSVisitationCounter(unittest_obj=self)
        depth_first_search(
            graph=graph, first_visit_f=counter.first_visit_f, last_visit_f=counter.last_visit_f, visitation_order=[3]
        )

        for vertex in [0, 1, 2]:
            self.assertLess(counter.first_visit_ord[3], counter.first_visit_ord[vertex], str(vertex))
            self.assertGreater(counter.last_visit_ord[3], counter.last_visit_ord[vertex], str(vertex))

        for vertex in [1, 2]:
            self.assertLess(counter.first_visit_ord[0], counter.first_visit_ord[vertex], str(vertex))
            self.assertGreater(counter.last_visit_ord[0], counter.last_visit_ord[vertex], str(vertex))

        self.assertNotIn(4, counter.first_visit_ord)
        self.assertNotIn(4, counter.last_visit_ord)

        counter = DFSVisitationCounter(unittest_obj=self)
        depth_first_search(graph=graph, first_visit_f=counter.first_visit_f)
        for vertex in graph.keys():
            self.assertIn(vertex, counter.first_visit_ord)

    def test_undirected_connected_components(self):
        graph = UndirectedGraph(
            vertices={0, 1, 2, 3, 4, 5, 6},
            edges={(0, 1), (0, 2), (3, 4), (3, 5), (2, 4)},
        )

        output = undirected_connected_components(graph)
        self.assertEqual(
            frozenset(frozenset(x) for x in output), frozenset([frozenset([0, 1, 2, 3, 4, 5]), frozenset([6])])
        )

    def test_topological_sort(self):
        graph = {
            # A central node connected to two diamonds.
            0: {1, 2},
            1: {3, 4},
            2: {5, 6},
            3: {7},
            4: {7},
            5: {8},
            6: {8},
            7: {},
            8: {},
        }

        visitation_order = list(graph.keys())
        for i in range(len(visitation_order)):
            # Rotate the ordering.
            visitation_order = visitation_order[1:] + [visitation_order[0]]
            output = topological_sort(graph, visitation_order)
            vertex_to_ind = {v: i for i, v in enumerate(output)}

            for k, vs in graph.items():
                for v in vs:
                    self.assertLess(vertex_to_ind[k], vertex_to_ind[v], f"k = {k}, v = {v}")
