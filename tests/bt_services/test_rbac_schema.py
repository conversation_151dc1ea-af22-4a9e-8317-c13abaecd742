import psycopg2

from tests.braintrust_app_test_base import BraintrustAppTestBase


def connect_app_db():
    conn = BraintrustAppTestBase.connect_app_db()
    conn.autocommit = True
    return conn


ACL_COLUMN_STRING = "(object_type, object_id, user_object_type, user_id, group_id, grant_object_type, permission, role_id, restrict_object_type, _object_org_id)"
ACL_TUPLE_STRING = "(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"


def make_acl_tuple(
    object_type=None,
    object_id=None,
    user_object_type=None,
    user_id=None,
    group_id=None,
    grant_object_type=None,
    permission=None,
    role_id=None,
    restrict_object_type=None,
    _object_org_id=None,
):
    return (
        object_type,
        object_id,
        user_object_type,
        user_id,
        group_id,
        grant_object_type,
        permission,
        role_id,
        restrict_object_type,
        _object_org_id,
    )


class RBACSchemaTest(BraintrustAppTestBase):
    def _cleanup(self):
        if hasattr(self, "r0_id"):
            with connect_app_db() as conn:
                with conn.cursor() as cursor:
                    # Delete the unit testing system role.
                    cursor.execute("delete from roles where id = %s", (self.r0_id,))
        super().tearDown()

    def setUp(self):
        super().setUp()
        self.users = {}

        try:
            self.org2_id = self.createOrg()[0]
            # Create a few additional users.
            self.users["alice"], _, __ = self.createUserInOrg(self.org2_id)
            self.users["bob"], _, __ = self.createUserInOrg(self.org2_id)
            with connect_app_db() as conn:
                with conn.cursor() as cursor:
                    # Remove any acls and system groups for testing.
                    cursor.execute("delete from acls where _object_org_id in (%s, %s)", (self.org_id, self.org2_id))
                    cursor.execute(
                        "delete from groups where org_id in (%s, %s)",
                        (self.org_id, self.org2_id),
                    )

                    # Create a few groups.
                    cursor.execute(
                        "insert into groups(org_id, user_id, name) values (%s, %s, %s), (%s, %s, %s), (%s, %s, %s) returning id",
                        (
                            self.org_id,
                            self.user_id,
                            "t1",
                            self.org2_id,
                            self.users["alice"],
                            "t2",
                            self.org2_id,
                            self.users["bob"],
                            "t3",
                        ),
                    )
                    self.t1_id, self.t2_id, self.t3_id = [x[0] for x in cursor.fetchall()]

                    # Create a few roles.
                    cursor.execute(
                        "insert into roles(org_id, user_id, name) values (%s, %s, uuid_generate_v4()), (%s, %s, %s), (%s, %s, %s), (%s, %s, %s) returning id",
                        (
                            None,
                            self.user_id,
                            self.org_id,
                            self.user_id,
                            "r1",
                            self.org2_id,
                            self.users["alice"],
                            "r2",
                            self.org2_id,
                            self.users["bob"],
                            "r3",
                        ),
                    )
                    self.r0_id, self.r1_id, self.r2_id, self.r3_id = [x[0] for x in cursor.fetchall()]

                    # Create a project in one org and a dataset in the other.
                    cursor.execute(
                        "insert into projects(org_id, name, user_id) values (%s, %s, %s), (%s, %s, %s) returning id",
                        (
                            self.org_id,
                            "p1",
                            self.user_id,
                            self.org2_id,
                            "p2",
                            self.users["alice"],
                        ),
                    )
                    self.p1_id, self.p2_id = [x[0] for x in cursor.fetchall()]
                    cursor.execute(
                        "insert into datasets(project_id, name, user_id) values (%s, %s, %s) returning id",
                        (
                            self.p2_id,
                            "d2",
                            self.users["bob"],
                        ),
                    )
                    self.d2_id = cursor.fetchone()[0]
        except:
            self._cleanup()
            raise

    def tearDown(self):
        self._cleanup()

    def test_group_no_change_org(self):
        with connect_app_db().cursor() as cursor:
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update groups set org_id=%s where org_id=%s", (self.org2_id, self.org_id))

    def test_group_no_add_nonorg_user(self):
        with connect_app_db().cursor() as cursor:
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("insert into group_users values (%s, %s)", (self.t1_id, self.users["alice"]))

            # Also shouldn't permit changing the group org user to
            # something invalid.
            cursor.execute("insert into group_users values (%s, %s)", (self.t1_id, self.user_id))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update group_users set group_id=%s where group_id=%s", (self.t2_id, self.t1_id))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update group_users set user_id=%s where group_id=%s", (self.users["alice"], self.t1_id)
                )

    def test_group_no_inherit_different_org_group(self):
        with connect_app_db().cursor() as cursor:
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("insert into group_members values (%s, %s)", (self.t2_id, self.t1_id))

            # Also shouldn't permit changing the group inheritance to
            # something invalid.
            cursor.execute("insert into group_members values (%s, %s)", (self.t3_id, self.t2_id))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update group_members set member_group_id=%s where group_id=%s", (self.t1_id, self.t3_id)
                )
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update group_members set group_id=%s where member_group_id=%s", (self.t1_id, self.t2_id)
                )

    def test_expanded_group_members(self):
        with connect_app_db().cursor() as cursor:
            select_all_group_members = lambda: cursor.execute(
                "select * from _expanded_group_members where _group_org_id in (%s, %s)", (self.org_id, self.org2_id)
            )

            # At the start, it should just contain the tautological members.
            TAUTOLOGICAL_MEMBERS = {
                (self.t1_id, "group", self.t1_id, self.org_id),
                (self.t2_id, "group", self.t2_id, self.org2_id),
                (self.t3_id, "group", self.t3_id, self.org2_id),
            }

            select_all_group_members()
            self.assertEqual(set(cursor.fetchall()), TAUTOLOGICAL_MEMBERS)

            # If we add a user to a group, it should update to include them.
            cursor.execute("insert into group_users values (%s, %s)", (self.t1_id, self.user_id))
            select_all_group_members()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.t1_id, "user", self.user_id, self.org_id),
                },
            )
            # Test update
            cursor.execute(
                "update group_users set group_id=%s, user_id=%s where _group_org_id=%s",
                (self.t2_id, self.users["alice"], self.org_id),
            )
            select_all_group_members()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.t2_id, "user", self.users["alice"], self.org2_id),
                },
            )
            # Delete
            cursor.execute("delete from group_users where _group_org_id=%s", (self.org2_id,))
            select_all_group_members()
            self.assertEqual(set(cursor.fetchall()), TAUTOLOGICAL_MEMBERS)
            # Bulk insert.
            cursor.execute(
                "insert into group_users values (%s, %s), (%s, %s)",
                (self.t1_id, self.user_id, self.t3_id, self.users["alice"]),
            )
            select_all_group_members()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.t1_id, "user", self.user_id, self.org_id),
                    (self.t3_id, "user", self.users["alice"], self.org2_id),
                },
            )
            cursor.execute("delete from group_users where group_id=%s", (self.t1_id,))

            # Simple inheritance.
            cursor.execute("insert into group_users values (%s, %s)", (self.t2_id, self.users["bob"]))
            cursor.execute("insert into group_members values (%s, %s)", (self.t3_id, self.t2_id))
            select_all_group_members()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.t2_id, "user", self.users["bob"], self.org2_id),
                    (self.t3_id, "user", self.users["bob"], self.org2_id),
                    (self.t3_id, "user", self.users["alice"], self.org2_id),
                    (self.t3_id, "group", self.t2_id, self.org2_id),
                },
            )

            # Cyclical inheritance.
            cursor.execute("insert into group_members values (%s, %s)", (self.t2_id, self.t3_id))
            select_all_group_members()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.t2_id, "user", self.users["bob"], self.org2_id),
                    (self.t2_id, "user", self.users["alice"], self.org2_id),
                    (self.t3_id, "user", self.users["bob"], self.org2_id),
                    (self.t3_id, "user", self.users["alice"], self.org2_id),
                    (self.t2_id, "group", self.t3_id, self.org2_id),
                    (self.t3_id, "group", self.t2_id, self.org2_id),
                },
            )

            # Marking a group as deleted should remove it from the expansion.
            cursor.execute("update groups set deleted_at=now() where id=%s", (self.t2_id,))
            select_all_group_members()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *{s for s in TAUTOLOGICAL_MEMBERS if s[0] != self.t2_id},
                    (self.t3_id, "user", self.users["alice"], self.org2_id),
                },
            )

    def test_role_no_change_org(self):
        with connect_app_db().cursor() as cursor:
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update roles set org_id=%s where org_id=%s", (self.org2_id, self.org_id))

    def test_role_no_inherit_different_org_role(self):
        with connect_app_db().cursor() as cursor:
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("insert into role_members values (%s, %s)", (self.r2_id, self.r1_id))

            # Also shouldn't permit changing the role inheritance to
            # something invalid.
            cursor.execute("insert into role_members values (%s, %s)", (self.r3_id, self.r2_id))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update role_members set member_role_id=%s where member_role_id=%s", (self.r1_id, self.r2_id)
                )
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update role_members set role_id=%s where member_role_id=%s", (self.r1_id, self.r2_id))

            # It's okay to change the base role id to a system role, and the
            # inheritor to anybody.
            cursor.execute(
                "update role_members set member_role_id=%s where member_role_id=%s", (self.r0_id, self.r2_id)
            )
            for role_id in [self.r0_id, self.r1_id, self.r2_id, self.r3_id]:
                cursor.execute("update role_members set role_id=%s where member_role_id=%s", (role_id, self.r0_id))

            # But it's not okay for a system role to be the inheritor of a
            # non-system role.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update role_members set member_role_id=%s, role_id=%s where member_role_id=%s",
                    (self.r1_id, self.r0_id, self.r0_id),
                )

    def test_expanded_role_permissions(self):
        with connect_app_db().cursor() as cursor:
            select_all_role_permissions = lambda: cursor.execute(
                """select role_id, grant_object_type, permission, restrict_object_type, member_role_id, _role_org_id
                from _expanded_role_permissions where role_id = %s or _role_org_id in (%s, %s)""",
                (self.r0_id, self.org_id, self.org2_id),
            )

            # At the start, it should just contain the tautological members.
            TAUTOLOGICAL_MEMBERS = {
                (self.r0_id, "role", None, None, self.r0_id, None),
                (self.r1_id, "role", None, None, self.r1_id, self.org_id),
                (self.r2_id, "role", None, None, self.r2_id, self.org2_id),
                (self.r3_id, "role", None, None, self.r3_id, self.org2_id),
            }

            # At the start, it should be empty.
            select_all_role_permissions()
            self.assertEqual(set(cursor.fetchall()), TAUTOLOGICAL_MEMBERS)

            # If we add a grant to a role, it should update to include them.
            cursor.execute("insert into role_permissions values (%s, %s, %s)", (self.r1_id, "create", "experiment"))
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.r1_id, "permission", "create", "experiment", None, self.org_id),
                },
            )
            # Test update
            cursor.execute("update role_permissions set role_id=%s where role_id=%s", (self.r2_id, self.r1_id))
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.r2_id, "permission", "create", "experiment", None, self.org2_id),
                },
            )
            # Delete
            cursor.execute("delete from role_permissions where role_id=%s", (self.r2_id,))
            select_all_role_permissions()
            self.assertEqual(set(cursor.fetchall()), TAUTOLOGICAL_MEMBERS)
            # Bulk insert.
            cursor.execute(
                "insert into role_permissions values (%s, %s, %s), (%s, %s, %s)",
                (self.r1_id, "create", None, self.r3_id, "create", "dataset"),
            )
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.r1_id, "permission", "create", None, None, self.org_id),
                    (self.r3_id, "permission", "create", "dataset", None, self.org2_id),
                },
            )
            cursor.execute("delete from role_permissions where role_id=%s", (self.r1_id,))

            # Simple inheritance.
            cursor.execute("insert into role_permissions values (%s, %s, %s)", (self.r2_id, "delete", "prompt"))
            cursor.execute("insert into role_members values (%s, %s)", (self.r3_id, self.r2_id))
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.r3_id, "permission", "create", "dataset", None, self.org2_id),
                    (self.r3_id, "permission", "delete", "prompt", None, self.org2_id),
                    (self.r3_id, "role", None, None, self.r2_id, self.org2_id),
                    (self.r2_id, "permission", "delete", "prompt", None, self.org2_id),
                },
            )

            # Inheritance from system roles propagates to custom roles.
            cursor.execute("insert into role_permissions values (%s, %s, %s)", (self.r0_id, "read", None))
            cursor.execute("insert into role_members values (%s, %s)", (self.r2_id, self.r0_id))
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.r3_id, "permission", "create", "dataset", None, self.org2_id),
                    (self.r3_id, "permission", "delete", "prompt", None, self.org2_id),
                    (self.r3_id, "permission", "read", None, None, self.org2_id),
                    (self.r3_id, "role", None, None, self.r2_id, self.org2_id),
                    (self.r3_id, "role", None, None, self.r0_id, self.org2_id),
                    (self.r2_id, "permission", "delete", "prompt", None, self.org2_id),
                    (self.r2_id, "permission", "read", None, None, self.org2_id),
                    (self.r2_id, "role", None, None, self.r0_id, self.org2_id),
                    (self.r0_id, "permission", "read", None, None, None),
                },
            )

            # Cyclical inheritance.
            cursor.execute("insert into role_members values (%s, %s)", (self.r2_id, self.r3_id))
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *TAUTOLOGICAL_MEMBERS,
                    (self.r3_id, "permission", "create", "dataset", None, self.org2_id),
                    (self.r3_id, "permission", "delete", "prompt", None, self.org2_id),
                    (self.r3_id, "permission", "read", None, None, self.org2_id),
                    (self.r3_id, "role", None, None, self.r2_id, self.org2_id),
                    (self.r3_id, "role", None, None, self.r0_id, self.org2_id),
                    (self.r2_id, "permission", "create", "dataset", None, self.org2_id),
                    (self.r2_id, "permission", "delete", "prompt", None, self.org2_id),
                    (self.r2_id, "permission", "read", None, None, self.org2_id),
                    (self.r2_id, "role", None, None, self.r3_id, self.org2_id),
                    (self.r2_id, "role", None, None, self.r0_id, self.org2_id),
                    (self.r0_id, "permission", "read", None, None, None),
                },
            )

            # Marking a role as deleted should remove it from the expansion.
            cursor.execute("update roles set deleted_at=now() where id=%s", (self.r2_id,))
            select_all_role_permissions()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    *[x for x in TAUTOLOGICAL_MEMBERS if x[0] != self.r2_id],
                    (self.r3_id, "permission", "create", "dataset", None, self.org2_id),
                    (self.r0_id, "permission", "read", None, None, None),
                },
            )

    def test_acl_validation(self):
        with connect_app_db().cursor() as cursor:
            # Test deriving the org ID.
            acl_tuple_grant_kwargs = dict(
                grant_object_type="permission",
                permission="create",
            )
            acl_tuple_orig_user_kwargs = dict(user_object_type="user", user_id=self.user_id, **acl_tuple_grant_kwargs)
            acl_tuple_alice_kwargs = dict(
                user_object_type="user", user_id=self.users["alice"], **acl_tuple_grant_kwargs
            )
            cursor.execute(
                f"insert into acls{ACL_COLUMN_STRING} values {ACL_TUPLE_STRING}, {ACL_TUPLE_STRING}, {ACL_TUPLE_STRING} returning _object_org_id",
                (
                    *make_acl_tuple("organization", self.org_id, **acl_tuple_orig_user_kwargs),
                    *make_acl_tuple("project", self.p2_id, **acl_tuple_alice_kwargs),
                    *make_acl_tuple("dataset", self.d2_id, **acl_tuple_alice_kwargs),
                ),
            )
            self.assertEqual([x[0] for x in cursor.fetchall()], [self.org_id, self.org2_id, self.org2_id])
            # Leave just one row.
            cursor.execute("delete from acls where _object_org_id = %s", (self.org2_id,))

            # Bad insert should fail (supply mismatching _object_org_id).
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    f"insert into acls{ACL_COLUMN_STRING} values {ACL_TUPLE_STRING}",
                    make_acl_tuple("project", self.p1_id, **acl_tuple_orig_user_kwargs, _object_org_id=self.org2_id),
                )

            # Mismatching object vs id.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set object_id=%s where _object_org_id=%s", (self.p1_id, self.org_id))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set acl_object='project' where _object_org_id=%s", (self.org_id,))

            # Mismatching user_object_type.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set user_object_type='group' where _object_org_id=%s", (self.org_id,))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update acls set user_id=null, group_id=%s where _object_org_id=%s", (self.t1_id, self.org_id)
                )
            # Multiple settings for user_object_type.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set group_id=%s where _object_org_id=%s", (self.t1_id, self.org_id))

            # Setting a valid group should be fine.
            cursor.execute(
                "update acls set user_object_type='group', user_id=null, group_id=%s where _object_org_id=%s",
                (self.t1_id, self.org_id),
            )
            # But setting a group from a different org should fail.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set group_id=%s where _object_org_id=%s", (self.t2_id, self.org_id))

            # Mismatching grant_object_type.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set grant_object_type='role' where _object_org_id=%s", (self.org_id,))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update acls set permission=null, role_id=%s where _object_org_id=%s", (self.r1_id, self.org_id)
                )
            # Multiple settings for grant_object_type.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set role_id=%s where _object_org_id=%s", (self.r1_id, self.org_id))
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute(
                    "update acls set grant_object_type='role', role_id=%s where _object_org_id=%s",
                    (self.r1_id, self.org_id),
                )

            # Setting a valid role should be fine.
            cursor.execute(
                "update acls set grant_object_type='role', permission=null, role_id=%s where _object_org_id=%s",
                (self.r1_id, self.org_id),
            )
            cursor.execute("update acls set role_id=%s where _object_org_id=%s", (self.r0_id, self.org_id))
            # But setting a role from a different org should fail.
            with self.assertRaises(psycopg2.errors.Error):
                cursor.execute("update acls set role_id=%s where _object_org_id=%s", (self.r2_id, self.org_id))

    def test_expanded_acls(self):
        with connect_app_db().cursor() as cursor:
            select_all_acls = lambda: cursor.execute(
                "select object_type, object_id, user_object_type, user_group_id, permission, restrict_object_type, _object_org_id from _expanded_acls where _object_org_id in (%s, %s)",
                (self.org_id, self.org2_id),
            )

            acl_tuple_grant_kwargs = dict(
                grant_object_type="permission",
                permission="create",
            )
            acl_tuple_orig_user_kwargs0 = dict(
                user_object_type="user",
                user_id=self.user_id,
                **acl_tuple_grant_kwargs,
                restrict_object_type="dataset",
            )
            acl_tuple_orig_user_kwargs1 = dict(user_object_type="user", user_id=self.user_id, **acl_tuple_grant_kwargs)
            acl_tuple_alice_kwargs = dict(
                user_object_type="user", user_id=self.users["alice"], **acl_tuple_grant_kwargs
            )
            cursor.execute(
                f"insert into acls{ACL_COLUMN_STRING} values {ACL_TUPLE_STRING}, {ACL_TUPLE_STRING}, {ACL_TUPLE_STRING}",
                (
                    *make_acl_tuple("organization", self.org_id, **acl_tuple_orig_user_kwargs0),
                    *make_acl_tuple("organization", self.org_id, **acl_tuple_orig_user_kwargs1),
                    *make_acl_tuple("dataset", self.d2_id, **acl_tuple_alice_kwargs),
                ),
            )

            # Inserting does not automatically trigger a refresh of the ACLs, so
            # we should still be empty.
            select_all_acls()
            self.assertEqual(set(cursor.fetchall()), set())

            # But after triggering an update, they should show up.
            cursor.execute("select refresh_expanded_acls_by_object('organization', %s)", (self.org_id,))
            cursor.execute("select refresh_expanded_acls_by_object('dataset', %s)", (self.d2_id,))
            select_all_acls()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    ("organization", self.org_id, "user", self.user_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", None, self.org_id),
                    ("dataset", self.d2_id, "user", self.users["alice"], "create", None, self.org2_id),
                },
            )

            # Changing to an empty group should swap out the entry with a group
            # entry.
            cursor.execute(
                "update acls set user_object_type='group', user_id=null, group_id=%s where _object_org_id=%s",
                (self.t1_id, self.org_id),
            )
            select_all_acls()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    ("organization", self.org_id, "group", self.t1_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "group", self.t1_id, "create", None, self.org_id),
                    ("dataset", self.d2_id, "user", self.users["alice"], "create", None, self.org2_id),
                },
            )
            # Changing to an empty role should clear those out.
            cursor.execute(
                "update acls set grant_object_type='role', permission=null, role_id=%s where _object_org_id=%s",
                (self.r2_id, self.org2_id),
            )
            select_all_acls()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    ("organization", self.org_id, "group", self.t1_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "group", self.t1_id, "create", None, self.org_id),
                },
            )

            # Adding entries to the group/role should re-populate them.
            cursor.execute("insert into group_users values (%s, %s)", (self.t1_id, self.user_id))
            select_all_acls()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    ("organization", self.org_id, "group", self.t1_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "group", self.t1_id, "create", None, self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", None, self.org_id),
                },
            )
            cursor.execute("insert into role_permissions values (%s, %s)", (self.r2_id, "create"))
            select_all_acls()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    ("organization", self.org_id, "group", self.t1_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "group", self.t1_id, "create", None, self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", None, self.org_id),
                    ("dataset", self.d2_id, "user", self.users["alice"], "create", None, self.org2_id),
                },
            )

            # Adding inherited groups/roles should expand our set of acls to
            # those users/grants.

            # Bob needs to be in org1 to get into group1. Keep them out of org
            # owners because the group doesn't exist!
            self.addUserToOrg(self.users["bob"], self.org_id, remove_from_org_owners=True, cursor=cursor)
            cursor.execute(
                "insert into groups(org_id, user_id, name) values (%s, %s, %s) returning id",
                (self.org_id, self.users["bob"], "t1_bob"),
            )
            t1_bob_id = cursor.fetchone()[0]
            cursor.execute("insert into group_users values (%s, %s)", (t1_bob_id, self.users["bob"]))
            cursor.execute("insert into group_members values (%s, %s)", (self.t1_id, t1_bob_id))
            # Add some grants to an inherited role.
            cursor.execute("insert into role_permissions values (%s, %s)", (self.r3_id, "delete"))
            cursor.execute("insert into role_members values (%s, %s)", (self.r2_id, self.r3_id))
            select_all_acls()
            self.assertEqual(
                set(cursor.fetchall()),
                {
                    ("organization", self.org_id, "group", self.t1_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "group", self.t1_id, "create", None, self.org_id),
                    ("organization", self.org_id, "group", t1_bob_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "group", t1_bob_id, "create", None, self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", "dataset", self.org_id),
                    ("organization", self.org_id, "user", self.user_id, "create", None, self.org_id),
                    ("organization", self.org_id, "user", self.users["bob"], "create", "dataset", self.org_id),
                    ("organization", self.org_id, "user", self.users["bob"], "create", None, self.org_id),
                    ("dataset", self.d2_id, "user", self.users["alice"], "create", None, self.org2_id),
                    ("dataset", self.d2_id, "user", self.users["alice"], "delete", None, self.org2_id),
                },
            )

            # Deleting the group/role should delete them again.
            cursor.execute("update groups set deleted_at=now() where id=%s", (self.t1_id,))
            cursor.execute("update roles set deleted_at=now() where id=%s", (self.r2_id,))
            select_all_acls()
            self.assertEqual(set(cursor.fetchall()), set())

    def test_expanded_acls_multiple_sources(self):
        with connect_app_db().cursor() as cursor:
            # Add alice to one of the groups.
            cursor.execute("insert into group_users values (%s, %s)", (self.t2_id, self.users["alice"]))

            # Grant an ACL to alice both directly and through the group.
            cursor.execute(
                """
                select
                    register_acl_unchecked(_object_type => %s, _object_id => %s, _user_id => %s, _permission => %s) acl0,
                    register_acl_unchecked(_object_type => %s, _object_id => %s, _group_id => %s, _permission => %s) acl1
                """,
                (
                    "organization",
                    self.org2_id,
                    self.users["alice"],
                    "read",
                    "organization",
                    self.org2_id,
                    self.t2_id,
                    "read",
                ),
            )
            cursor_output = cursor.fetchone()
            base_acl_ids = set(r["acl"]["id"] for r in [cursor_output[0], cursor_output[1]])
            self.assertEqual(len(base_acl_ids), 2)

            cursor.execute(
                """
                select acl_id from _expanded_acls
                where
                    object_type = %s and object_id = %s
                    and user_object_type = 'user' and user_group_id = %s
                    and permission = %s
                """,
                ("organization", self.org2_id, self.users["alice"], "read"),
            )
            self.assertEqual(base_acl_ids, set(r[0] for r in cursor.fetchall()))
