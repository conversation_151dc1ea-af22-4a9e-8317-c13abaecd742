// This is used from test_function_tools.py and is not a standalone test.

import braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({ name: "prompt with tools" });

const calculatorTool = project.tools.create({
  name: "<PERSON>der",
  description: "A simple adder",
  parameters: z.object({
    a: z.number(),
    b: z.number(),
  }),
  handler: async ({ a, b }) => {
    return a + b;
  },
});

project.prompts.create({
  name: "Calculator",
  slug: "calculator",
  description: "A simple calculator",
  model: "gpt-4o",
  messages: [
    {
      role: "user",
      content: "What is {{formula}}? Just return the number, nothing else.",
    },
  ],
  tools: [calculatorTool],
});
