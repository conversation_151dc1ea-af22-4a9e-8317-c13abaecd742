import uuid

from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase

TEST_DATA_PLANE_SERVICE_TOKEN_NAME = "test__bt_data_plane_service_token"


class DataPlaneManagerTest(BraintrustAppTestBase):
    def setUp(self):
        super(DataPlaneManagerTest, self).setUp()

        sysadmin_user_id = self.ensureSysadminUserExists()

        # Create a user who is part of the org but without any
        # permissions.
        self.unpriviliged_user_id, _, self.unprivileged_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )

        # Create a user who is not part of the org.
        self.other_org_id, _ = self.createOrg()
        self.non_org_user_id, _, self.non_org_api_key = self.createUserInOrg(self.other_org_id)

        # Add the sysadmin user to the other org, so that we can generate an API
        # key that can be deleted later.
        self.addUserToOrg(sysadmin_user_id, self.other_org_id, remove_from_org_owners=True)
        self.sysadmin_api_key = self.createUserOrgApiKey(sysadmin_user_id, self.other_org_id)

    @parameterized.expand(
        [
            ("bt-hosted", "org owner", False),
            ("bt-hosted", "org unprivileged", False),
            ("bt-hosted", "non-org member", False),
            ("bt-hosted", "sysadmin", True),
            ("self-hosted", "org unprivileged", False),
            ("self-hosted", "non-org member", False),
            ("self-hosted", "sysadmin", False),
            ("self-hosted", "org owner", True),
        ]
    )
    def test_data_plane_manager(self, data_plane_mode, privilege_mode, allowed_to_provision):
        self_hosted = data_plane_mode == "self-hosted"

        org1_id, org1_name = self.createOrg()
        self.addUserToOrg(self.user_id, org1_id)
        org1_api_key = self.createUserOrgApiKey(self.user_id, org1_id)
        org2_id, org2_name = self.createOrg()
        self.addUserToOrg(self.user_id, org2_id)
        org2_api_key = self.createUserOrgApiKey(self.user_id, org2_id)

        org_ids = [self.org_id, org1_id, org2_id]
        org_names = [self.org_name, org1_name, org2_name]
        api_keys = [self.org_api_key, org1_api_key, org2_api_key]
        project_ids = []
        for org_name, api_key in zip(org_names, api_keys):
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/project/register",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(project_name="p-" + org_name),
            ).json()
            project_ids.append(resp["project"]["id"])

        if privilege_mode == "org owner":
            request_api_key = self.org_api_key
        elif privilege_mode == "org unprivileged":
            request_api_key = self.unprivileged_api_key
        elif privilege_mode == "non-org member":
            request_api_key = self.non_org_api_key
        elif privilege_mode == "sysadmin":
            request_api_key = self.sysadmin_api_key
        else:
            assert False, "Invalid privilege mode"

        def request_fn(force_recreate_token=False, expect_error=False):
            body = dict(
                org_id=self.org_id,
                name=TEST_DATA_PLANE_SERVICE_TOKEN_NAME,
                force_recreate_token=force_recreate_token,
            )
            # Add testing flag to convince control plane that the data plane is "self-hosted"
            if self_hosted:
                body["_bt_test_provision_as_self_hosted_data_plane"] = True

            resp = self.run_request(
                "post",
                f"{LOCAL_APP_URL}/api/service_token/provision_data_plane_manager",
                headers=dict(Authorization=f"Bearer {request_api_key}"),
                json=body,
                expect_error=expect_error,
            )
            return resp

        with self.subTest(
            data_plane_mode=data_plane_mode, user=privilege_mode, allowed_to_provision=allowed_to_provision
        ):
            resp = request_fn(force_recreate_token=True, expect_error=not allowed_to_provision)
            if not allowed_to_provision:
                assert resp.status_code == 401
                assert resp.text.startswith("Unauthorized")
                return

            assert resp.status_code == 200
            data = resp.json()
            assert data["data_plane_manager"]["id"] is not None
            assert (
                data["data_plane_manager"]["name"] == "bt_data_plane_manager"
                if not self_hosted
                else "data_plane_manager"
            )
            assert data["data_plane_service_token"]["name"] == TEST_DATA_PLANE_SERVICE_TOKEN_NAME
            assert data["data_plane_service_token"]["key"] is not None
            assert data["data_plane_service_token"]["created"] is True

            manager_id = data["data_plane_manager"]["id"]
            service_token_id = data["data_plane_service_token"]["id"]
            service_token = data["data_plane_service_token"]["key"]
            if not self_hosted:
                # check that provisioned data plane manager is a sysadmin
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/api/self/me",
                    headers=dict(Authorization=f"Bearer {service_token}"),
                    json=dict(check_sysadmin=True),
                ).json()
                assert resp["id"] == manager_id
                assert resp["is_sysadmin"] is True
                # should be expected format for email
                assert resp["email"] == "bt::sp::bt_data_plane_manager-sysadmin"
                assert "@" not in resp["email"]
            else:
                # check that provisioned data plane manager is not a sysadmin but has org_project:read permissions
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/api/self/me",
                    headers=dict(Authorization=f"Bearer {service_token}"),
                    json=dict(check_sysadmin=True),
                ).json()
                assert resp["id"] == manager_id
                assert resp["is_sysadmin"] is False
                # should be expected format for email
                assert resp["email"].startswith("bt::sp::custom::data_plane_manager-")
                assert "@" not in resp["email"]

                # check for org_project:read acl
                acls = self.run_request(
                    "get",
                    f"{LOCAL_API_URL}/v1/acl",
                    headers=dict(Authorization=f"Bearer {self.org_api_key}"),
                    params=dict(object_type="org_project", object_id=self.org_id, user_id=manager_id),
                ).json()
                assert len(acls["objects"]) == 1
                assert acls["objects"][0]["object_type"] == "org_project"
                assert acls["objects"][0]["object_id"] == self.org_id
                assert acls["objects"][0]["permission"] == "read"

            # check that service token is unscoped
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("select id, name, user_id, org_id from api_keys where id = %s", (service_token_id,))
                    assert cursor.rowcount == 1
                    assert cursor.fetchone() == (
                        service_token_id,
                        TEST_DATA_PLANE_SERVICE_TOKEN_NAME,
                        manager_id,
                        None,
                    )

            # run get_object_info on various orgs
            if self_hosted:
                # only works on first org until the manager is invited to the other orgs
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/api/self/get_object_info",
                    headers=dict(Authorization=f"Bearer {service_token}"),
                    json=dict(
                        object_type="project",
                        object_ids=project_ids,
                        allow_sysadmin_roles=["sysadmin"] if not self_hosted else [],
                    ),
                )
                assert resp.status_code == 200
                data = resp.json()
                assert len(data) == 1
                assert data[0]["object_id"] == project_ids[0]
                assert data[0]["parent_cols"]["organization"]["id"] == self.org_id
                assert "is_allowed_sysadmin" not in data[0]

                # invite the manager to the orgs
                self.addUserToOrg(manager_id, org1_id)
                self.addUserToOrg(manager_id, org2_id)

            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                headers=dict(Authorization=f"Bearer {service_token}"),
                json=dict(
                    object_type="project",
                    object_ids=project_ids,
                    allow_sysadmin_roles=["sysadmin"] if not self_hosted else [],
                ),
            )
            assert resp.status_code == 200
            data = resp.json()
            assert {
                (o["object_id"], o["parent_cols"]["organization"]["id"], o.get("is_allowed_sysadmin", None))
                for o in data
            } == {
                (project_id, org_id, True if not self_hosted else None)
                for project_id, org_id in zip(project_ids, org_ids)
            }

            # rerunning should return existing resources
            old_service_token_id = service_token_id
            old_service_token = service_token
            resp = request_fn(force_recreate_token=True)
            assert resp.status_code == 200
            data = resp.json()
            assert data["data_plane_manager"]["id"] == manager_id
            assert data["data_plane_service_token"]["name"] == TEST_DATA_PLANE_SERVICE_TOKEN_NAME
            service_token_id = data["data_plane_service_token"]["id"]
            service_token = data["data_plane_service_token"]["key"]
            assert service_token_id != old_service_token_id
            assert service_token is not None
            assert service_token != old_service_token
            assert data["data_plane_service_token"]["created"] is True

            # new token provisioned
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        "select id, name, user_id, org_id from api_keys where name = %s and user_id = %s",
                        (TEST_DATA_PLANE_SERVICE_TOKEN_NAME, manager_id),
                    )
                    assert cursor.rowcount == 1
                    assert cursor.fetchone() == (
                        service_token_id,
                        TEST_DATA_PLANE_SERVICE_TOKEN_NAME,
                        manager_id,
                        None,
                    )

            # service token can be saved to data plane
            headers = dict(Authorization=f"Bearer {request_api_key}")
            # Add testing flag to convince control plane that the data plane is "self-hosted"
            if self_hosted:
                headers["x-bt-test_as_self_hosted_data_plane"] = "true"
                headers["x-bt-test_with_primary_org_name"] = self.org_name
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/service-token/upsert",
                headers=headers,
                json=dict(name=TEST_DATA_PLANE_SERVICE_TOKEN_NAME, service_token=service_token),
            )
            assert resp.status_code == 200
            assert resp.json()["success"] is True

            resp = self.run_request(
                "head",
                f"{LOCAL_API_URL}/service-token/{TEST_DATA_PLANE_SERVICE_TOKEN_NAME}",
                headers=headers,
            )
            assert resp.ok

    @parameterized.expand(["bt-hosted", "self-hosted"])
    def test_data_plane_service_token_endpoints(self, data_plane_mode):
        service_token_name = "test-token"
        service_token_value = "test-token-value"

        self_hosted = data_plane_mode == "self-hosted"

        headers = {}
        # Add testing flag to convince control plane that the data plane is "self-hosted"
        if self_hosted:
            headers["x-bt-test_as_self_hosted_data_plane"] = "true"
            headers["x-bt-test_with_primary_org_name"] = self.org_name

        # On bt-hosted data planes none can access service token endpoints
        # ON self-hosted data planes only org owner can access service token endpoints
        for api_key in [self.org_api_key, self.unprivileged_api_key, self.non_org_api_key]:
            print(f"Testing {api_key}")
            headers["Authorization"] = f"Bearer {api_key}"

            expect_error = (not self_hosted) or (self_hosted and (api_key != self.org_api_key))

            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/service-token/upsert",
                headers=headers,
                json=dict(name=service_token_name, service_token=service_token_value),
                expect_error=expect_error,
            )
            assert resp.status_code == 200 if not expect_error else 403

            resp = self.run_request(
                "head",
                f"{LOCAL_API_URL}/service-token/{service_token_name}",
                headers=headers,
                expect_error=expect_error,
            )
            assert resp.status_code == 200 if not expect_error else 403

            resp = self.run_request(
                "get",
                f"{LOCAL_API_URL}/service-token/{service_token_name}",
                headers=headers,
                expect_error=expect_error,
            )
            assert resp.status_code == 200 if not expect_error else 403

        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/provision_data_plane_manager",
            headers=dict(Authorization=f"Bearer {self.sysadmin_api_key}"),
            json=dict(
                org_id=self.org_id,
                name=TEST_DATA_PLANE_SERVICE_TOKEN_NAME,
                force_recreate_token=True,
            ),
        ).json()
        dp_manager_token = resp["data_plane_service_token"]["key"]

        # Data plane manager should only be able to hit service token endpoints if
        # on the bt data plane
        headers["Authorization"] = f"Bearer {dp_manager_token}"
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/service-token/upsert",
            headers=headers,
            json=dict(name=service_token_name, service_token=service_token_value),
            expect_error=self_hosted,
        )
        assert resp.status_code == 200 if not self_hosted else 403

        resp = self.run_request(
            "head", f"{LOCAL_API_URL}/service-token/{service_token_name}", headers=headers, expect_error=self_hosted
        )
        assert resp.status_code == 200 if not self_hosted else 403

        resp = self.run_request(
            "get", f"{LOCAL_API_URL}/service-token/{service_token_name}", headers=headers, expect_error=self_hosted
        )
        assert resp.status_code == 200 if not self_hosted else 403
        assert resp.json()["service_token"] == service_token_value if not self_hosted else True

        working_key = dp_manager_token if not self_hosted else self.org_api_key
        headers["Authorization"] = f"Bearer {working_key}"
        # Non-existent service token should return 404
        resp = self.run_request(
            "head",
            f"{LOCAL_API_URL}/service-token/{service_token_name}-{uuid.uuid4()}",
            headers=headers,
            expect_error=True,
        )
        assert resp.status_code == 404

        # Sysadmin can also hit service token endpoints on the bt data plane
        # but not self-hosted
        headers["Authorization"] = f"Bearer {self.sysadmin_api_key}"
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/service-token/upsert",
            headers=headers,
            json=dict(name=service_token_name, service_token=service_token_value),
            expect_error=self_hosted,
        )
        assert resp.status_code == 200 if not self_hosted else 403

        resp = self.run_request(
            "head",
            f"{LOCAL_API_URL}/service-token/{service_token_name}",
            headers=headers,
            expect_error=self_hosted,
        )
        assert resp.status_code == 200 if not self_hosted else 403

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/service-token/{service_token_name}",
            headers=headers,
            expect_error=self_hosted,
        )
        assert resp.status_code == 200 if not self_hosted else 403
        assert resp.json()["service_token"] == service_token_value if not self_hosted else True
