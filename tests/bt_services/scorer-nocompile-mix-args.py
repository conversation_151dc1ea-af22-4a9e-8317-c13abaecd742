import braintrust

project = braintrust.projects.create(name="scorer")

# Provides both handler and LLM args.
project.scorers.create(
    name="Equality LLM scorer",
    slug="equality-scorer",
    description="An equality LLM scorer",
    handler=lambda x: x,
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=False,
    choice_scores={"A": 1, "B": 0},
)
