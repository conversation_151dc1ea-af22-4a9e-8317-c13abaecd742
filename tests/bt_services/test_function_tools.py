import json
import os
import subprocess
import tempfile
import unittest

import braintrust
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
from tests.bt_services.test_bundled_code import TEST_DIR
from tests.bt_services.test_functions import CALCULATOR_PARAMS, FunctionTestBase
from tests.bt_services.test_inline_code import LANGUAGE_TO_RUNTIME


class FunctionToolsTest(FunctionTestBase):
    def _invoke(self, **kwargs):
        result = braintrust.invoke(**kwargs)
        if isinstance(result, braintrust.BraintrustStream):
            return result.final_value()
        else:
            return result

    @parameterized.expand([(stream, lang) for stream in [False, True] for lang in ["typescript", "python"]])
    def test_basic_tool_call(self, stream, lang):
        function_record = self._insert_calculator(lang)

        # Try running the function with some basic arguments
        resp = self._invoke(
            project_name=self.project.name, slug="calculator", input={"op": "add", "a": 1, "b": 2}, stream=stream
        )
        self.assertEqual(resp, 3)

        # Try running a prompt with the tool definition, but without the tool call
        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [
                    {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                ],
                "tools": json.dumps(
                    [
                        {
                            "type": "function",
                            "function": {
                                "name": "calculator",
                                "description": "A simple calculator",
                                "parameters": CALCULATOR_PARAMS,
                            },
                        }
                    ]
                ),
            },
            "options": {
                "model": "gpt-4o",
            },
        }

        def _reset_prompt():
            return self._insert_function(
                project_id=self.project.id,
                function_data={
                    "type": "prompt",
                },
                prompt_data=prompt_data,
                name="Calculator prompt",
                slug="calculator-prompt",
            )

        prompt_record = _reset_prompt()
        resp = self._invoke(
            project_name=self.project.name, slug="calculator-prompt", input={"formula": "1+2"}, stream=stream
        )
        self.assertEqual(resp, {"op": "add", "a": 1, "b": 2})

        # Now try installing the structured tool instead of the traditional tool
        prompt_data["prompt"].pop("tools")
        prompt_data["tool_functions"] = [{"type": "function", "id": function_record["id"]}]
        prompt_record = _reset_prompt()

        with self.logger.start_span("test_basic_tool_call") as span:
            resp = self._invoke(
                project_name=self.project.name,
                slug="calculator-prompt",
                input={"formula": "1+2"},
                stream=stream,
                parent=span.export(),
            )
            self.assertEqual(resp, "3")

            span_id = span.id

        self.logger.flush()
        BraintrustAppTestBase.flush_proxy_promises()

        # Fetch the log records associated with the invocation
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={"query": f"from: project_logs('{self.project.id}') traces | select: * | filter: id='{span_id}'"},
        )
        spans = resp.json()["data"]

        # There should be 6 spans:
        # - 1 root span
        # - 2 function calls
        # - 2 chat completions (1 per function call)
        # - 1 tool call
        self.assertEqual(len(spans), 6)

        # Now try to add a traditional tool and a structured tool
        prompt_data["prompt"]["messages"] = [
            {
                "role": "user",
                "content": """I will give you a {{formula}}. Compute it, and tell me whether it corresponds to a winning lottery ticket.""",
            }
        ]
        prompt_data["prompt"]["tools"] = json.dumps(
            [
                {
                    "type": "function",
                    "function": {
                        "name": "lottery_checker",
                        "description": "Call this when you want to check if a number is a winning lottery ticket",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "number": {"type": "number"},
                            },
                        },
                    },
                }
            ]
        )
        prompt_record = _reset_prompt()

        resp = self._invoke(
            project_name=self.project.name,
            slug="calculator-prompt",
            input={"formula": "1+2"},
            stream=stream,
            mode="auto",
        )
        self.assertEqual(resp, {"number": 3})

        # If you do parallel mode, then gpt-4o is smart enough to call both tools, which should fail
        with self.assertRaises(braintrust.BraintrustInvokeError):
            resp = self._invoke(
                project_name=self.project.name,
                slug="calculator-prompt",
                input={"formula": "1+2"},
                stream=stream,
                mode="parallel",
            )

    @parameterized.expand(
        [
            (stream, lang, tool_choice)
            for stream in [False, True]
            for lang in ["typescript", "python"]
            for tool_choice in ["auto", "required", {"type": "function", "function": {"name": "calculator"}}]
        ]
    )
    def test_tool_choice_required(self, stream, lang, tool_choice):
        function_record = self._insert_calculator(lang)

        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [
                    {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                ],
            },
            "options": {
                "model": "gpt-4o",
                "params": {
                    "tool_choice": tool_choice,
                },
            },
            "tool_functions": [{"type": "function", "id": function_record["id"]}],
        }

        self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data=prompt_data,
            name="Calculator prompt",
            slug="calculator-prompt",
        )
        resp = self._invoke(
            project_name=self.project.name, slug="calculator-prompt", input={"formula": "1+2"}, stream=stream
        )
        self.assertEqual(resp, "3")

    @parameterized.expand([(lang,) for lang in ["python", "typescript"]])
    def test_secrets(self, lang):
        skip_s3 = BraintrustAppTestBase.skip_s3()

        secret_value = "foo"
        runtime_context = LANGUAGE_TO_RUNTIME[lang]
        if lang == "python":
            code = """\
import os

def handler(input=None):
    if os.environ.get("SECRET"):
        return os.environ["SECRET"]
    else:
        raise Exception("SECRET environment variable not set")"""
        else:
            code = """\
async function handler() {
    if (process.env.SECRET) {
        return process.env.SECRET;
    } else {
        throw new Error("SECRET environment variable not set");
    }
}"""

        self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "code",
                "data": {
                    "type": "inline",
                    "runtime_context": runtime_context,
                    "code": code,
                },
            },
            name="Secret checker",
            slug="secret-checker",
        )

        if not skip_s3:
            if lang == "typescript":
                task_file = os.path.join(TEST_DIR, "secrets-checker-test.ts")
                process = subprocess.Popen(
                    ["npx", "braintrust", "push", task_file],
                    env=self._get_testenv(),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                )
            else:
                task_file = os.path.join(TEST_DIR, "secrets_checker_test.py")
                process = subprocess.Popen(
                    ["braintrust", "push", task_file],
                    env=self._get_testenv(),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                )
            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                print("--- STDOUT")
                print("stdout", stdout)
                print("--- STDERR")
                print("stderr", stderr)
                print("--- END")
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # First try invoking without the secret set.
        with self.assertRaises(braintrust.BraintrustInvokeError):
            self._invoke(
                project_name=self.project.name,
                slug="secret-checker",
                input={},
            )

        if not skip_s3:
            with self.assertRaises(braintrust.BraintrustInvokeError):
                self._invoke(
                    project_name=self.project.name,
                    slug="secret-checker-bundled",
                    input={},
                )

        # Now set the secret and try again
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/env_var",
            json={"name": "SECRET", "value": secret_value, "object_id": self.project.id, "object_type": "project"},
        )

        resp = self._invoke(
            project_name=self.project.name,
            slug="secret-checker",
            input={},
        )
        self.assertEqual(resp, secret_value)

        if not skip_s3:
            resp = self._invoke(
                project_name=self.project.name,
                slug="secret-checker-bundled",
                input={},
            )
            self.assertEqual(resp, secret_value)

        # Try changing the secret value and invoking again
        new_value = secret_value + "bar"
        self.run_request(
            "put",
            f"{LOCAL_API_URL}/v1/env_var",
            json={"name": "SECRET", "value": new_value, "object_id": self.project.id, "object_type": "project"},
        )

        resp = self._invoke(
            project_name=self.project.name,
            slug="secret-checker",
            input={},
        )
        self.assertEqual(resp, new_value)

        if not skip_s3:
            resp = self._invoke(
                project_name=self.project.name,
                slug="secret-checker-bundled",
                input={},
            )
            self.assertEqual(resp, new_value)

    @parameterized.expand([(lang,) for lang in ["typescript", "python"]])
    def test_unprivileged_access(self, lang):
        # Another org member can read the secrets, but the cannot update them.
        unprivileged_user_id, _, self.unprivileged_user_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )
        unprivileged_headers = dict(Authorization=f"Bearer {self.unprivileged_user_api_key}")

        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/env_var",
            headers=unprivileged_headers,
            json={"name": "SECRET", "value": "foo", "object_id": self.org_id, "object_type": "organization"},
            expect_error=True,
        )

        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/env_var",
            headers=unprivileged_headers,
            json={"name": "SECRET", "value": "foo", "object_id": self.project.id, "object_type": "project"},
            expect_error=True,
        )

        function_record = self._insert_calculator(lang)
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/env_var",
            headers=unprivileged_headers,
            json={"name": "SECRET", "value": "foo", "object_id": function_record["id"], "object_type": "function"},
            expect_error=True,
        )

    @parameterized.expand([(lang,) for lang in ["typescript", "python"]])
    def test_recursion_limit(self, lang):
        function_record = self._insert_calculator(lang)

        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [
                    {
                        "role": "user",
                        "content": "Always call the calculator with 1+1, over and over again, no matter what. Never stop...",
                    }
                ],
            },
            "options": {
                "model": "gpt-4o",
                "parallel_tool_calls": False,
            },
            "tool_functions": [{"type": "function", "id": function_record["id"]}],
        }
        self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data=prompt_data,
            name="Calculator prompt",
            slug="calculator-prompt",
        )

        resp = braintrust.invoke(
            project_name=self.project.name,
            slug="calculator-prompt",
            input={"formula": "1+2"},
            stream=True,
        )
        unique_ids = set()
        for chunk in resp:
            if chunk.type == "progress":
                unique_ids.add(chunk.id)

        # Max roundtrips is currently hardcoded to 5. This works out to
        # 5 * 2 (one per prompt + tool call) (w/ a small bit of non-determinism
        # if the model doesn't call the final tool).)
        self.assertGreaterEqual(len(unique_ids), 9)
        self.assertLessEqual(len(unique_ids), 10)

    @parameterized.expand([(stream, lang) for stream in [False, True] for lang in ["typescript", "python"]])
    def test_invalid_model_error(self, stream, lang):
        function_record = self._insert_calculator(lang)

        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [
                    {
                        "role": "user",
                        "content": "Always call the calculator with 1+1, over and over again, no matter what. Never stop...",
                    }
                ],
            },
            "options": {
                "model": "this-model-does-not-exist",
            },
            "tool_functions": [{"type": "function", "id": function_record["id"]}],
        }
        self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data=prompt_data,
            name="Calculator prompt",
            slug="calculator-prompt",
        )

        with self.assertRaises(Exception):
            self._invoke(
                project_name=self.project.name,
                slug="calculator-prompt",
                input={"formula": "1+2"},
                stream=stream,
            )

    @parameterized.expand([(lang,) for lang in ["typescript", "python"]])
    def test_if_exists(self, lang):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("")

        def rerun_push(if_exists=None):
            env = self._get_testenv()
            if if_exists is not None:
                env["IF_EXISTS"] = if_exists

            if lang == "typescript":
                task_file = os.path.join(TEST_DIR, "secrets-checker-test.ts")
                process = subprocess.Popen(
                    ["npx", "braintrust", "push", task_file],
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                )
            else:
                task_file = os.path.join(TEST_DIR, "secrets_checker_test.py")
                process = subprocess.Popen(
                    ["braintrust", "push", task_file],
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                )

            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                print("--- STDOUT")
                print("stdout", stdout)
                print("--- STDERR")
                print("stderr", stderr)
                print("--- END")
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        rerun_push()

        def fetch_version():
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={
                    "query": f"from: project_functions('{self.project.id}') | select: * | filter: slug='secret-checker-bundled'"
                },
            )

            function_record = resp.json()["data"][0]
            self.assertEqual(function_record["slug"], "secret-checker-bundled")
            return function_record["_xact_id"]

        original_version = fetch_version()

        with self.assertRaises(Exception):
            rerun_push()
        with self.assertRaises(Exception):
            rerun_push("error")

        version = fetch_version()
        self.assertEqual(version, original_version)

        rerun_push("ignore")
        version = fetch_version()
        self.assertEqual(version, original_version)

        rerun_push("replace")
        version = fetch_version()
        self.assertGreater(version, original_version)

    @parameterized.expand([(lang,) for lang in ["typescript", "python"]])
    def test_push_tool_with_prompt(self, lang):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        if lang == "typescript":
            task_file = os.path.join(TEST_DIR, "prompt-with-tool.ts")
            subprocess.run(
                ["npx", "tsc", task_file, "--noEmit", "--isolatedModules", "--skipLibCheck"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                check=True,
            )
            process = subprocess.Popen(
                ["npx", "braintrust", "push", task_file],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
        else:
            task_file = os.path.join(TEST_DIR, "prompt_with_tool.py")
            subprocess.run(
                ["npx", "pyright", "--level", "error", task_file],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                check=True,
            )
            process = subprocess.Popen(
                ["braintrust", "push", task_file],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )

        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            print("--- STDOUT")
            print("stdout", stdout)
            print("--- STDERR")
            print("stderr", stderr)
            print("--- END")
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # Invoke the prompt. It should call the tool
        resp = braintrust.invoke(
            project_name="prompt with tools",
            slug="calculator",
            input={"formula": "2+45"},
            stream=True,
        )
        resp_copy = resp.copy()

        seen_adder = False
        for chunk in resp:
            if chunk.type == "progress" and chunk.name == "adder":
                seen_adder = True

        self.assertTrue(seen_adder)
        self.assertEqual(resp_copy.final_value(), "47")

    def test_push_tool_with_prompt_py_requirements(self):
        # Also tests pydantic 1
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        task_file = os.path.join(TEST_DIR, "prompt_with_tool_np.py")
        sdk_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(braintrust.__file__))))
        with tempfile.TemporaryDirectory() as td:
            req_path = os.path.join(td, "requirements.txt")
            with open(req_path, "w") as f:
                f.write(
                    f"""braintrust @ file://{sdk_path}
braintrust_core==0.0.54
certifi==2024.8.30
charset-normalizer==3.4.0
chevron==0.14.0
exceptiongroup==1.2.0
gitdb==4.0.11
GitPython==3.1.43
idna==3.10
numpy==2.1.3
pydantic==1.10.0
python-dotenv==1.0.1
python-slugify==8.0.4
requests==2.32.3
smmap==5.0.1
sseclient-py==1.8.0
text-unidecode==1.3
tqdm==4.66.6
typing_extensions==4.12.2
urllib3==2.2.3
"""
                )
            process = subprocess.Popen(
                ["braintrust", "push", task_file, "--requirements", req_path],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                print("--- STDOUT")
                print("stdout", stdout)
                print("--- STDERR")
                print("stderr", stderr)
                print("--- END")
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # Invoke the prompt. It should call the tool
        resp = braintrust.invoke(
            project_name="prompt with tools",
            slug="calculator",
            input={"formula": "2+45"},
            stream=True,
        )
        resp_copy = resp.copy()

        seen_adder = False
        for chunk in resp:
            if chunk.type == "progress" and chunk.name == "adder":
                seen_adder = True

        self.assertTrue(seen_adder)
        self.assertEqual(resp_copy.final_value(), "47")

    def test_py_tool_no_params(self):
        """Test pushing a tool with no parameters."""
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        task_file = os.path.join(TEST_DIR, "tool_no_params.py")
        process = subprocess.Popen(
            ["braintrust", "push", task_file],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        _, stderr = process.communicate()
        ret = process.returncode

        self.assertNotEqual(ret, 0)
        self.assertIn("parameters", stderr.lower())

    @parameterized.expand([(lang,) for lang in ["typescript", "python"]])
    def test_tracing_tool(self, lang):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        if lang == "typescript":
            task_file = os.path.join(TEST_DIR, "tracing-tool.ts")
            process = subprocess.Popen(
                ["npx", "braintrust", "push", task_file],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
        else:
            task_file = os.path.join(TEST_DIR, "tracing_tool.py")
            process = subprocess.Popen(
                ["braintrust", "push", task_file],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )

        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            print("--- STDOUT")
            print("stdout", stdout)
            print("--- STDERR")
            print("stderr", stderr)
            print("--- END")
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        with self.logger.start_span("test_basic_tool_call") as span:
            resp = self._invoke(
                project_name=self.project.name,
                slug="tracing-tool",
                input={},
                stream=True,
                parent=span.export(),
            )
            self.assertEqual(resp, "hello")

        self.logger.flush()
        BraintrustAppTestBase.flush_proxy_promises()

        # Fetch the log records associated with the invocation
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"from: project_logs('{self.project.id}') traces | select: * | filter: id='{span.id}'",
            },
        )
        spans = resp.json()["data"]

        self.assertEqual(len(spans), 4)
        types = set([span["span_attributes"].get("type") for span in spans])
        self.assertEqual(types, {"tool", "task", None, "function"})

    @parameterized.expand([(stream,) for stream in [False, True]])
    def test_tool_call_max_tokens(self, stream):
        self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                    ],
                    "tools": json.dumps(
                        [
                            {
                                "type": "function",
                                "function": {
                                    "name": "calculator",
                                    "description": "A simple calculator",
                                    "parameters": CALCULATOR_PARAMS,
                                },
                            }
                        ]
                    ),
                },
                "options": {
                    "model": "gpt-4o",
                    "params": {
                        "max_tokens": 10,
                    },
                },
            },
            name="Calculator prompt",
            slug="calculator-prompt",
        )
        with self.assertRaisesRegex(braintrust.BraintrustInvokeError, "(?i)max completion tokens reached"):
            self._invoke(
                project_name=self.project.name,
                slug="calculator-prompt",
                input={"formula": "1+2"},
                stream=stream,
            )

    @parameterized.expand([(stream,) for stream in [False, True]])
    def test_tool_call_max_tokens_managed_tool(self, stream):
        func = self._insert_calculator("python")
        self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                    ],
                },
                "tool_functions": [{"type": "function", "id": func["id"]}],
                "options": {
                    "model": "gpt-4o",
                    "params": {
                        "max_tokens": 10,
                    },
                },
            },
            name="Calculator prompt",
            slug="calculator-prompt",
        )
        with self.assertRaisesRegex(braintrust.BraintrustInvokeError, "(?i)max completion tokens reached"):
            self._invoke(
                project_name=self.project.name,
                slug="calculator-prompt",
                input={"formula": "1+2"},
                stream=stream,
            )

    @parameterized.expand([(stream,) for stream in [False, True]])
    def test_tool_call_max_tokens_choice_scores(self, stream):
        self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "Always pick choice A"}]},
                "options": {
                    "model": "gpt-4o",
                    "params": {
                        "max_tokens": 10,
                    },
                },
                "parser": {
                    "type": "llm_classifier",
                    "use_cot": True,
                    "choice_scores": {
                        "A": 1,
                        "B": 0,
                    },
                },
            },
            name="LLM scorer",
            slug="llm-scorer",
            function_data={"type": "prompt"},
            function_type="scorer",
        )
        with self.assertRaisesRegex(braintrust.BraintrustInvokeError, "(?i)max completion tokens reached"):
            self._invoke(
                project_name=self.project.name,
                slug="llm-scorer",
                input={"output": "foo", "expected": "bar"},
                stream=stream,
            )

    @parameterized.expand([(stream,) for stream in [False, True]])
    def test_managed_tool_with_response_format(self, stream):
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest()

        task_file = os.path.join(TEST_DIR, "managed_tool_with_response_format.py")
        process = subprocess.Popen(
            ["braintrust", "push", task_file],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            print("--- STDOUT")
            print("stdout", stdout)
            print("--- STDERR")
            print("stderr", stderr)
            print("--- END")
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        resp = self._invoke(
            project_name="managed_tool_with_response_format",
            slug="get-load-data-prompt",
            stream=stream,
        )
        self.assertEqual(resp, {"status": "in progress"})
