import uuid

from tests.braintrust_app_test_base import LOCAL_API_URL
from tests.bt_services.test_automations import AutomationTestBase


class FetchRetentionPoliciesTest(AutomationTestBase):
    """
    This test ensures that both implementations of retention policy lookups produce identical results:
    - /retention/policies (TypeScript implementation)
    - /brainstore/retention/policies (Brainstore implementation via TypeScript passthrough)
    """

    def setUp(self):
        super().setUp()
        self.bt_data_plane_service_token = self.ensureDataPlaneServiceTokenExists()

    def _make_brainstore_request(self, objects):
        """Make request to brainstore via TypeScript API passthrough /brainstore/retention/policies endpoint"""
        json_data = dict(objects=objects)
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/brainstore/retention/fetch-policies",
            json=json_data,
            headers={"Authorization": f"Bearer {self.bt_data_plane_service_token}"},
        )
        return response

    def _make_typescript_request(self, objects):
        """Make request to data plane API /retention/policies endpoint (TypeScript implementation)"""
        json_data = dict(objects=objects)
        response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/retention/fetch-policies",
            json=json_data,
            headers={"Authorization": f"Bearer {self.bt_data_plane_service_token}"},
        )
        return response

    def _compare_policy_responses(self, ts_data, bs_data, test_name):
        assert ts_data == bs_data, f"{test_name}: deep equality expected"

    def verify_response_equivalence(self, ts_response, bs_response, test_name):
        if hasattr(ts_response, "status_code") and hasattr(bs_response, "status_code"):
            self.assertEqual(
                ts_response.status_code, bs_response.status_code, f"{test_name}: HTTP status codes should match"
            )

        if ts_response.status_code == 200 and bs_response.status_code == 200:
            # For successful responses, compare the actual policy data.
            try:
                ts_data = ts_response.json() if hasattr(ts_response, "json") else ts_response
                bs_data = bs_response.json() if hasattr(bs_response, "json") else bs_response
            except Exception as e:
                self.fail(f"{test_name}: Failed to parse successful JSON responses: {e}")
            assert ts_data == bs_data, f"{test_name}: Policy data mismatch!\napi-ts: {ts_data}\nBrainstore: {bs_data}"
        else:
            # For error responses, we only verify they both failed with same status code,
            # since error message formats can differ between the APIs.
            self.assertEqual(
                ts_response.status_code,
                bs_response.status_code,
                f"{test_name}: Both APIs should return the same error status code",
            )

    def test_basic_policy_resolution_comparison(self):
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        objects = [{"object_type": "project_logs", "object_id": project["id"]}]
        self._create_automation(
            project["id"],
            dict(
                name="test-retention",
                description="Test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        )

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)
        self.verify_response_equivalence(ts_response, bs_response, "basic_policy_resolution")

    def test_multiple_object_types_comparison(self):
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()
        experiment = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json={"project_id": project["id"], "name": "test-experiment"}
        ).json()
        dataset = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/dataset", json={"project_id": project["id"], "name": "test-dataset"}
        ).json()

        # Create automations and store them by object type
        automations_by_type = {}
        automations_by_type["experiment"] = self._create_automation(
            project["id"],
            dict(
                name="experiment-policy",
                description="Experiment retention policy",
                config=dict(
                    event_type="retention",
                    object_type="experiment",
                    retention_days=30,
                ),
            ),
        ).json()
        automations_by_type["dataset"] = self._create_automation(
            project["id"],
            dict(
                name="dataset-policy",
                description="Dataset retention policy",
                config=dict(
                    event_type="retention",
                    object_type="dataset",
                    retention_days=90,
                ),
            ),
        ).json()

        nonexistent_object_id = str(uuid.uuid4())

        # Create test objects organized by ID
        test_objects = {
            f"experiment:{experiment['id']}": {
                "object_type": "experiment",
                "object_id": experiment["id"],
                "expected_retention_days": 30,
            },
            f"dataset:{dataset['id']}": {
                "object_type": "dataset",
                "object_id": dataset["id"],
                "expected_retention_days": 90,
            },
            f"project_logs:{project['id']}": {
                "object_type": "project_logs",
                "object_id": project["id"],
                "expected_retention_days": None,
            },
            f"experiment:{nonexistent_object_id}": {
                "object_type": "experiment",
                "object_id": nonexistent_object_id,
                "expected_retention_days": None,
            },
        }

        # Delete experiment from control plane (required for retention policy queries)
        self.run_request("delete", f"{LOCAL_API_URL}/v1/experiment/{experiment['id']}")

        objects = [{"object_type": obj["object_type"], "object_id": obj["object_id"]} for obj in test_objects.values()]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)
        self.verify_response_equivalence(ts_response, bs_response, "multiple_object_types")

        # In the new format, only objects WITH policies are included in the response
        # So we expect 2 objects (experiment and dataset), not 4
        self.assertEqual(len(ts_response.json()), 2)
        self.assertEqual(len(bs_response.json()), 2)

        # Verify against the known automations we created
        ts_by_id = ts_response.json()

        # Verify each object has the expected policy
        for obj_id, expected in test_objects.items():
            policy = ts_by_id.get(obj_id)

            if expected["expected_retention_days"] is None:
                # Objects without policies are omitted from the response entirely
                self.assertIsNone(policy, f"Object {obj_id} should be omitted from response (no policy)")
            else:
                # Objects with policies should be present in the response
                self.assertIsNotNone(policy, f"Object {obj_id} should be in response with policy")
                self.assertIsNotNone(policy["automation_id"], f"Object {obj_id} should have an automation ID")
                self.assertEqual(
                    policy["retention_days"],
                    expected["expected_retention_days"],
                    f"Object {obj_id} should have {expected['expected_retention_days']}-day retention",
                )

    def test_cross_project_isolation(self):
        """Test that retention policies are isolated across projects"""
        # Create two projects
        project1 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "project-1"}).json()
        project2 = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "project-2"}).json()

        # Create policy only for project1
        automation = self._create_automation(
            project1["id"],
            dict(
                name="project1-policy",
                description="Project 1 retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        ).json()

        # Test objects from both projects
        objects = [
            {"object_type": "project_logs", "object_id": project1["id"]},  # Should have policy
            {"object_type": "project_logs", "object_id": project2["id"]},  # Should NOT have policy
        ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "cross_project_isolation")

        # Verify isolation: exp1 has policy, exp2 doesn't.
        ts_data = ts_response.json()

        # In the new format, only objects WITH policies are included
        # So we expect only project1 to be in the response, project2 should be omitted
        project1_key = f"project_logs:{project1['id']}"
        project2_key = f"project_logs:{project2['id']}"

        self.assertIn(project1_key, ts_data, "project1 should have a policy and be in response")
        self.assertNotIn(project2_key, ts_data, "project2 should have no policy and be omitted from response")

        # Verify project1's policy details
        project1_policy = ts_data[project1_key]
        self.assertIsNotNone(project1_policy, "project1 should have a policy")
        self.assertEqual(project1_policy["retention_days"], 30)

    def test_cross_org_boundary(self):
        """Test that retention policies from one org don't affect objects in another org"""
        # Create two orgs
        org1_id, org1_name = self.createOrg()
        org2_id, org2_name = self.createOrg()

        # Add user to both orgs
        self.addUserToOrg(self.user_id, org1_id)
        self.addUserToOrg(self.user_id, org2_id)

        # Create API keys for both orgs
        org1_api_key = self.createUserOrgApiKey(self.user_id, org1_id)
        org2_api_key = self.createUserOrgApiKey(self.user_id, org2_id)

        # Create projects in both orgs
        project1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project",
            json={"name": "project-org1", "org_name": org1_name},
            headers={"Authorization": f"Bearer {org1_api_key}"},
        ).json()

        project2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/project",
            json={"name": "project-org2", "org_name": org2_name},
            headers={"Authorization": f"Bearer {org2_api_key}"},
        ).json()

        # Create retention policy only in org1
        self._create_automation(
            project1["id"],
            dict(
                name="org1-retention",
                description="Org1 retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
            api_key=org1_api_key,
        )

        # Query both projects
        objects = [
            {"object_type": "project_logs", "object_id": project1["id"]},
            {"object_type": "project_logs", "object_id": project2["id"]},
        ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "cross_org_boundary")

        # Verify that only project1 has a policy
        ts_data = ts_response.json()

        # In the new format, only objects WITH policies are included
        project1_key = f"project_logs:{project1['id']}"
        project2_key = f"project_logs:{project2['id']}"

        self.assertIn(project1_key, ts_data, "Project1 should have retention policy from org1")
        self.assertNotIn(project2_key, ts_data, "Project2 should NOT have retention policy from org1 and be omitted")

        # Verify project1's policy details
        project1_policy = ts_data[project1_key]
        self.assertIsNotNone(project1_policy, "project1 should have a policy")
        self.assertEqual(project1_policy["retention_days"], 30)

        # Create experiments in both projects
        exp1 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/experiment",
            json={"project_id": project1["id"], "name": "exp-org1"},
            headers={"Authorization": f"Bearer {org1_api_key}"},
        ).json()

        exp2 = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/experiment",
            json={"project_id": project2["id"], "name": "exp-org2"},
            headers={"Authorization": f"Bearer {org2_api_key}"},
        ).json()

        # Create retention policy only in org1
        self._create_automation(
            project1["id"],
            dict(
                name="org1-experiment-retention",
                description="Org1 retention policy",
                config=dict(
                    event_type="retention",
                    object_type="experiment",
                    retention_days=90,
                ),
            ),
            api_key=org1_api_key,
        ).json()

        # Delete experiments from control plane (required for retention policy queries)
        self.run_request(
            "delete",
            f"{LOCAL_API_URL}/v1/experiment/{exp1['id']}",
            headers={"Authorization": f"Bearer {org1_api_key}"},
        )
        self.run_request(
            "delete",
            f"{LOCAL_API_URL}/v1/experiment/{exp2['id']}",
            headers={"Authorization": f"Bearer {org2_api_key}"},
        )

        # Query both experiments
        objects = [
            {"object_type": "experiment", "object_id": exp1["id"]},
            {"object_type": "experiment", "object_id": exp2["id"]},
        ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "cross_org_boundary")

        # Verify that only project1 has a policy
        bs_data = bs_response.json()

        exp1_key = f"experiment:{exp1['id']}"
        exp2_key = f"experiment:{exp2['id']}"

        # Verify project1's policy details
        exp1_policy = bs_data[exp1_key]
        self.assertIsNotNone(exp1_policy, "exp1 should have a policy")
        self.assertEqual(exp1_policy["retention_days"], 90)

        self.assertNotIn(exp2_key, bs_data, "exp2 should NOT have a policy and be omitted from response")

    def test_deleted_automations_not_returned(self):
        """Test that deleted retention automations are not returned in policy lookups"""
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        # Create a retention automation
        first_automation = self._create_automation(
            project["id"],
            dict(
                name="first-policy",
                description="First retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        ).json()

        # Verify it works
        objects = [{"object_type": "project_logs", "object_id": project["id"]}]
        ts_response = self._make_typescript_request(objects)
        exp_key = f"project_logs:{project['id']}"
        self.assertIn(exp_key, ts_response.json())
        self.assertEqual(ts_response.json()[exp_key]["retention_days"], 30)

        # Delete the automation
        self.run_request(
            "delete",
            f"{LOCAL_API_URL}/v1/project_automation/{first_automation['project_automation']['id']}",
        )

        # Create another retention automation that should now be used
        fallback_automation = self._create_automation(
            project["id"],
            dict(
                name="fallback-policy",
                description="Fallback retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=60,
                ),
            ),
        ).json()

        # Verify the new automation is used
        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "deleted_automations")

        # Verify that the deleted automation is not used, fallback is used instead
        ts_data = ts_response.json()
        self.assertEqual(len(ts_data), 1)

        exp_key = f"project_logs:{project['id']}"
        self.assertIn(exp_key, ts_data)
        policy = ts_data[exp_key]
        self.assertIsNotNone(policy)
        self.assertEqual(policy["retention_days"], 60, "Should use the fallback automation's retention days")
        self.assertEqual(
            policy["automation_id"],
            fallback_automation["project_automation"]["id"],
            "Should use the fallback automation's ID",
        )

    def test_empty_request_array(self):
        """Test behavior with empty request array"""
        objects = []

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "empty_request")

        # Should return empty dict (not array)
        self.assertEqual(ts_response.json(), {})
        self.assertEqual(bs_response.json(), {})

    def test_duplicate_objects_in_request(self):
        """Test behavior when the same object is requested multiple times"""
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        self._create_automation(
            project["id"],
            dict(
                name="test-retention",
                description="Test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        )

        # Request the same project multiple times
        objects = [
            {"object_type": "project_logs", "object_id": project["id"]},
            {"object_type": "project_logs", "object_id": project["id"]},
            {"object_type": "project_logs", "object_id": project["id"]},
        ]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "duplicate_objects")

        # In the new format, duplicates are automatically deduplicated in the mapping
        # So we expect only 1 entry for the project
        ts_data = ts_response.json()
        self.assertEqual(len(ts_data), 1, "Should deduplicate to 1 unique entry")

        # Verify the project has the expected policy
        exp_key = f"project_logs:{project['id']}"
        self.assertIn(exp_key, ts_data)
        policy = ts_data[exp_key]
        self.assertIsNotNone(policy)
        self.assertEqual(policy["retention_days"], 30)

    def test_deleted_objects(self):
        # Create and then delete a project
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        # Create retention policy
        self._create_automation(
            project["id"],
            dict(
                name="test-retention",
                description="Test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=30,
                ),
            ),
        )

        # Delete the project
        self.run_request("delete", f"{LOCAL_API_URL}/v1/project/{project['id']}")

        # Request policy for deleted project
        objects = [{"object_type": "project_logs", "object_id": project["id"]}]

        ts_response = self._make_typescript_request(objects)
        bs_response = self._make_brainstore_request(objects)

        self.verify_response_equivalence(ts_response, bs_response, "deleted_objects")

        # The retention policy should be deleted because the project was deleted
        bs_data = bs_response.json()
        exp_key = f"project_logs:{project['id']}"
        self.assertNotIn(exp_key, bs_data, "Policy should not exist for deleted project")

    def test_brainstore_retention_requires_prior_control_plane_deletion(self):
        """
        Brainstore should only resolve retention policies for experiments that have been deleted in the control plane.
        For other object types, whether the object has been deleted in the control plane is ignored.
        """
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()

        object_type_to_retention_days = {
            "project_logs": 30,
            "experiment": 90,
            "dataset": 365,
        }
        object_type_requires_prior_control_plane_deletion = {
            "project_logs": False,
            "experiment": True,
            "dataset": False,
        }

        for object_type, retention_days in object_type_to_retention_days.items():
            requires_prior_control_plane_deletion = object_type_requires_prior_control_plane_deletion[object_type]

            self._create_automation(
                project["id"],
                dict(
                    name=f"test-retention-{object_type}",
                    description="Test retention policy",
                    config=dict(
                        event_type="retention",
                        object_type=object_type,
                        retention_days=retention_days,
                    ),
                ),
            )

            if object_type == "project_logs":
                obj_id = project["id"]
                obj_key = f"{object_type}:{project['id']}"
            else:
                # Create the object.
                obj = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/v1/{object_type}",
                    json={"project_id": project["id"], "name": f"test-{object_type}"},
                ).json()
                obj_id = obj["id"]
                obj_key = f"{object_type}:{obj['id']}"

            # Request the policy from brainstore and check it's only returned if prior control plane
            # deletion is not required.
            objects = [{"object_type": object_type, "object_id": obj_id}]
            bs_response = self._make_brainstore_request(objects)
            if requires_prior_control_plane_deletion:
                self.assertNotIn(
                    obj_key,
                    bs_response.json(),
                    f"Object {obj_key} should not be returned since object type {object_type} requires prior control plane deletion",
                )
            else:
                self.assertIn(obj_key, bs_response.json())
                self.assertEqual(bs_response.json()[obj_key]["retention_days"], retention_days)

            # For experiments and datasets, verify that the policy is visible post-control-plane-deletion.
            if object_type in ["experiment", "dataset"]:
                # Delete the object in the control plane.
                self.run_request("delete", f"{LOCAL_API_URL}/v1/{object_type}/{obj_id}")

                # Now the policy should be returned regardless of object type.
                bs_response = self._make_brainstore_request(objects)
                self.assertIn(obj_key, bs_response.json())
                self.assertEqual(bs_response.json()[obj_key]["retention_days"], retention_days)
