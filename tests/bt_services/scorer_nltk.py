import braintrust
import nltk
import pydantic

project = braintrust.projects.create(name="scorer")


class Input(pydantic.BaseModel):
    output: str
    expected: str


def handler(output: str, expected: str) -> float:
    max_len = max(len(output), len(expected))
    score: float = 1
    if max_len > 0:
        score = 1 - (float(nltk.edit_distance(output, expected)) / max_len)  # type: ignore
    return score


project.scorers.create(
    name="Edit distance scorer",
    slug="edit-distance-scorer",
    description="An edit distance scorer",
    parameters=Input,
    handler=handler,
)
