import braintrust

project = braintrust.projects.create(name="scorer")

# Incomplete LLM scorer: no choice scores.
project.scorers.create(
    name="Equality LLM scorer",
    slug="equality-scorer",
    description="An equality LLM scorer",
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=False,
)
