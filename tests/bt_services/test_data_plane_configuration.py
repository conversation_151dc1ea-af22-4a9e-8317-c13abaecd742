import requests
from braintrust_local.constants import ANON_USER_ID

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase


class DataPlaneConfigurationTest(BraintrustAppTestBase):
    def test_org_name_env_restriction(self):
        # The default data plane setup should allow users and users of arbitrary
        # orgs to ping the data plane.

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={},
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], ANON_USER_ID)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], self.user_id)

        other_org_id, _ = self.createOrg()
        other_user_id, _, other_user_api_key = self.createUserInOrg(other_org_id)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={"Authorization": f"Bearer {other_user_api_key}"},
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], other_user_id)

        # Also fine if we explicitly set the org name to "*" (with extra spaces
        # to test trimming).
        OVERRIDE_ORG_NAME_HEADER = {"x-bt-override-org-name": "* "}
        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers=OVERRIDE_ORG_NAME_HEADER,
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], ANON_USER_ID)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={"Authorization": f"Bearer {self.org_api_key}", **OVERRIDE_ORG_NAME_HEADER},
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], self.user_id)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={"Authorization": f"Bearer {other_user_api_key}", **OVERRIDE_ORG_NAME_HEADER},
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], other_user_id)

        # But if we lock down the data plane to a specific org, only users of that
        # org should be able to ping the data plane.
        OVERRIDE_ORG_NAME_HEADER = {"x-bt-override-org-name": self.org_name}

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers=OVERRIDE_ORG_NAME_HEADER,
            expect_error=True,
        )
        self.assertEqual(resp.status_code, 403)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={**OVERRIDE_ORG_NAME_HEADER, "Authorization": f"Bearer {self.org_api_key}"},
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], self.user_id)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={**OVERRIDE_ORG_NAME_HEADER, "Authorization": f"Bearer {other_user_api_key}"},
            expect_error=True,
        )
        self.assertEqual(resp.status_code, 403)

        # But a sysadmin user should still be able to hit ping.
        sysadmin_user_id = self.ensureSysadminUserExists()
        self.addUserToOrg(sysadmin_user_id, other_org_id, remove_from_org_owners=True)
        sysadmin_api_key = self.createUserOrgApiKey(sysadmin_user_id, other_org_id)

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/ping",
            headers={**OVERRIDE_ORG_NAME_HEADER, "Authorization": f"Bearer {sysadmin_api_key}"},
        )
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()["id"], sysadmin_user_id)
