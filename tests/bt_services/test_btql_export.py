import gzip
import json
import time
import uuid

import boto3
import braintrust
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase

GRACE_TIME = 60


class BtqlExportTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        # Create S3 client for testing
        self.s3_client = boto3.client(
            "s3",
            region_name="us-east-1",  # Minio is hardcoded to this region
            endpoint_url="http://localhost:10000",
            aws_access_key_id="minio_root_user",
            aws_secret_access_key="minio_root_password",
        )
        try:
            self.s3_client.create_bucket(Bucket="btql-export-test")
        except Exception:
            # Bucket already exists
            pass

        self.automation_ids = []

        # Create project using braintrust logger (same pattern as test_automations.py)
        self.logger = braintrust.init_logger("btql_export_test")
        self.project_id = self.logger.project.id

    def tearDown(self):
        for automation_id in self.automation_ids:
            self.run_request("delete", f"{LOCAL_API_URL}/v1/project_automation/{automation_id}")
        super().tearDown()

    def _create_automation(self, batch_size=1000, export_definition=None):
        """Create a test automation and return its ID."""

        if export_definition is None:
            export_definition = {
                "type": "log_traces",
            }

        prefix = uuid.uuid4().hex
        automation_config = {
            "name": "test_btql_export_automation",
            "description": "Test automation for BTQL export",
            "config": {
                "event_type": "btql_export",
                "export_definition": export_definition,
                "export_path": f"s3://btql-export-test/exports/{prefix}/",
                "format": "jsonl",
                "interval_seconds": 1,
                "credentials": {
                    "type": "aws_iam",
                    "role_arn": "arn:aws:iam::123456789012:role/test-role",
                    "external_id": "test-external-id",
                },
                "batch_size": batch_size,
            },
        }

        resp = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/project_automation", json={"project_id": self.project_id, **automation_config}
        )
        automation_id = resp.json()["id"]
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/automation/cron",
            json={
                "automation_id": automation_id,
                "cron": {
                    "type": "btql_export",
                },
                "service_token": self.org_api_key,
            },
        )

        self.automation_ids.append(automation_id)
        return automation_id, prefix

    def _force_cron_run(self):
        self.run_request("post", f"{LOCAL_API_URL}/automation/cron-force-run")

    @parameterized.expand(
        [
            (export_type,)
            for export_type in [
                "log_traces",
                "log_spans",
                "btql_query",
            ]
        ]
    )
    def test_btql_export_sanity(self, export_type):
        if export_type == "btql_query":
            export_definition = {
                "type": "btql_query",
                "btql_query": f"select: * | from: project_logs('{self.project_id}') | filter: input.text = 'test log 0'",
            }
        else:
            export_definition = {
                "type": export_type,
            }

        # Create automation using API endpoint (same pattern as test_automations.py)
        automation_id, prefix = self._create_automation(export_definition=export_definition)

        # Add some logs to the project, and make sure they are exported
        for i in range(10):
            with self.logger.start_span() as span:
                span.log(input={"text": f"test log {i}"})
                with span.start_span() as span2:
                    span2.log(input={"text": f"inner span {i}"})
        self.logger.flush()

        self._force_cron_run()

        # Check if the export files are present
        response = None
        status = None
        for i in range(GRACE_TIME):
            response = self.s3_client.list_objects_v2(Bucket="btql-export-test", Prefix=f"exports/{prefix}/")
            status = self.run_request("get", f"{LOCAL_API_URL}/automation/cron/{automation_id}/status").json()
            if (
                response.get("Contents")
                and len(response.get("Contents")) > 0
                and status.get("last_executed") is not None
            ):
                break

            time.sleep(1)

        self.assertIsNotNone(response)
        self.assertIsNotNone(status)

        # These assertions ensure type checker knows these are not None
        assert response is not None
        assert status is not None

        if export_type == "log_traces":
            expected_rows = 10
        elif export_type == "log_spans":
            expected_rows = 20
        elif export_type == "btql_query":
            expected_rows = 1
        else:
            raise ValueError(f"Unknown export definition: {export_definition}")

        self.assertIsNotNone(status.get("state"))
        self.assertIsNotNone(status["state"].get("all_executions"))
        self.assertEqual(status["state"]["all_executions"]["rows"], expected_rows)

        # Check if the export files are present
        response_contents = response.get("Contents")
        self.assertIsNotNone(response_contents)
        self.assertEqual(len(response_contents), 1)
        file_key = response_contents[0].get("Key")
        self.assertTrue(file_key.endswith(".jsonl.gz"))
        self.assertTrue(file_key.startswith(f"exports/{prefix}/"))

        # Check if the export file is valid and contains the expected number of rows
        s3_response = self.s3_client.get_object(Bucket="btql-export-test", Key=file_key)

        # Un-gzip the file content
        gzipped_content = s3_response["Body"].read()
        unzipped_content = gzip.decompress(gzipped_content).decode("utf-8")

        # Count the number of lines (each line should be a JSON object)
        lines = [line.strip() for line in unzipped_content.strip().split("\n") if line.strip()]
        self.assertEqual(len(lines), expected_rows)

        # Verify each line is valid JSON
        for line in lines:
            json_obj = json.loads(line)
            self.assertIsInstance(json_obj, dict)

        # Now write more rows, and make sure that they are exported
        for i in range(10):
            with self.logger.start_span() as span:
                span.log(input={"text": f"test log {i}"})
                with span.start_span() as span2:
                    span2.log(input={"text": f"inner span {i}"})
        self.logger.flush()

        self._force_cron_run()

        # Check for the second export file and updated status
        second_response = None
        second_status = None
        for i in range(GRACE_TIME):
            second_response = self.s3_client.list_objects_v2(Bucket="btql-export-test", Prefix=f"exports/{prefix}/")
            second_status = self.run_request("get", f"{LOCAL_API_URL}/automation/cron/{automation_id}/status").json()
            if (
                second_response.get("Contents")
                and len(second_response.get("Contents")) >= 2
                and second_status.get("last_executed") is not None
                and second_status.get("state", {}).get("all_executions", {}).get("rows", 0) >= expected_rows * 2
            ):
                break

            time.sleep(1)

        self.assertIsNotNone(second_response)
        self.assertIsNotNone(second_status)

        # These assertions ensure type checker knows these are not None
        assert second_response is not None
        assert second_status is not None

        # Check that we now have 2 export files
        second_response_contents = second_response.get("Contents")
        self.assertIsNotNone(second_response_contents)
        self.assertEqual(len(second_response_contents), 2)

        self.assertIsNotNone(second_status.get("state"))
        self.assertIsNotNone(second_status["state"].get("all_executions"))
        self.assertEqual(second_status["state"]["all_executions"]["rows"], expected_rows * 2)

        # Find the new file by comparing with the first response
        first_file_key = response_contents[0].get("Key")
        new_file_key = None
        for file_obj in second_response_contents:
            file_key = file_obj.get("Key")
            if file_key != first_file_key:
                new_file_key = file_key
                break

        self.assertIsNotNone(new_file_key)

        # Assert for type checker
        assert new_file_key is not None

        self.assertTrue(new_file_key.endswith(".jsonl.gz"))
        self.assertTrue(new_file_key.startswith(f"exports/{prefix}/"))

        # Download and verify only the new file has the expected number of rows
        s3_response = self.s3_client.get_object(Bucket="btql-export-test", Key=new_file_key)
        gzipped_content = s3_response["Body"].read()
        unzipped_content = gzip.decompress(gzipped_content).decode("utf-8")

        lines = [line.strip() for line in unzipped_content.strip().split("\n") if line.strip()]
        self.assertEqual(len(lines), expected_rows)

        # Verify each line is valid JSON
        for line in lines:
            json_obj = json.loads(line)
            self.assertIsInstance(json_obj, dict)

    def test_btql_export_batch_size_one(self):
        # Create automation with batch_size=1
        automation_id, prefix = self._create_automation(batch_size=1)

        # Log 5 entries, flushing after each to ensure unique transaction IDs
        for i in range(5):
            self.logger.log(input={"text": f"batch size 1 test log {i}"})
            self.logger.flush()

        self._force_cron_run()

        # Check if export files are present
        response = None
        status = None
        for i in range(GRACE_TIME):
            response = self.s3_client.list_objects_v2(Bucket="btql-export-test", Prefix=f"exports/{prefix}/")
            status = self.run_request("get", f"{LOCAL_API_URL}/automation/cron/{automation_id}/status").json()
            if (
                response.get("Contents")
                and len(response.get("Contents")) > 0
                and status.get("last_executed") is not None
                and status.get("state", {}).get("all_executions", {}).get("rows", 0) >= 5
            ):
                break

            time.sleep(1)

        self.assertIsNotNone(response)
        self.assertIsNotNone(status)

        # These assertions ensure type checker knows these are not None
        assert response is not None
        assert status is not None

        # Check that files were created
        response_contents = response.get("Contents")
        self.assertIsNotNone(response_contents)
        self.assertGreater(len(response_contents), 0)

        # Check that the total row count is 5
        self.assertIsNotNone(status.get("state"))
        self.assertIsNotNone(status["state"].get("all_executions"))
        self.assertEqual(status["state"]["all_executions"]["rows"], 5)

        # Verify all files are properly formatted
        total_rows = 0
        for file_obj in response_contents:
            file_key = file_obj.get("Key")
            self.assertTrue(file_key.endswith(".jsonl.gz"))
            self.assertTrue(file_key.startswith(f"exports/{prefix}/"))

            # Download and verify each file
            s3_response = self.s3_client.get_object(Bucket="btql-export-test", Key=file_key)
            gzipped_content = s3_response["Body"].read()
            unzipped_content = gzip.decompress(gzipped_content).decode("utf-8")

            lines = [line.strip() for line in unzipped_content.strip().split("\n") if line.strip()]
            total_rows += len(lines)

            # Verify each line is valid JSON
            for line in lines:
                json_obj = json.loads(line)
                self.assertIsInstance(json_obj, dict)

        # Verify total rows across all files equals 5
        self.assertEqual(total_rows, 5)

    def test_btql_export_batch_size_one_same_transaction(self):
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True

            # Create automation with batch_size=1
            automation_id, prefix = self._create_automation(batch_size=1)

            # Log 5 entries without flushing between them (same transaction ID)
            for i in range(5):
                self.logger.log(input={"text": f"same transaction test log {i}"})
            # Only flush once at the end to keep all entries in the same transaction
            self.logger.flush()

            self._force_cron_run()

            # Check if export files are present
            response = None
            status = None
            for i in range(GRACE_TIME):
                response = self.s3_client.list_objects_v2(Bucket="btql-export-test", Prefix=f"exports/{prefix}/")
                status = self.run_request("get", f"{LOCAL_API_URL}/automation/cron/{automation_id}/status").json()
                if (
                    response.get("Contents")
                    and len(response.get("Contents")) > 0
                    and status.get("last_executed") is not None
                    and status.get("state", {}).get("all_executions", {}).get("rows", 0) >= 5
                ):
                    break

                time.sleep(1)

            self.assertIsNotNone(response)
            self.assertIsNotNone(status)

            # These assertions ensure type checker knows these are not None
            assert response is not None
            assert status is not None

            # Check that only one file was created (entries combined due to same transaction ID)
            response_contents = response.get("Contents")
            self.assertIsNotNone(response_contents)
            self.assertEqual(
                len(response_contents), 1, "Expected exactly one file since all entries have the same transaction ID"
            )

            # Check that the total row count is 5
            self.assertIsNotNone(status.get("state"))
            self.assertIsNotNone(status["state"].get("all_executions"))
            self.assertEqual(status["state"]["all_executions"]["rows"], 5)

            # Verify the single file contains all 5 rows
            file_obj = response_contents[0]
            file_key = file_obj.get("Key")
            self.assertTrue(file_key.endswith(".jsonl.gz"))
            self.assertTrue(file_key.startswith(f"exports/{prefix}/"))

            # Download and verify the file has exactly 5 rows
            s3_response = self.s3_client.get_object(Bucket="btql-export-test", Key=file_key)
            gzipped_content = s3_response["Body"].read()
            unzipped_content = gzip.decompress(gzipped_content).decode("utf-8")

            lines = [line.strip() for line in unzipped_content.strip().split("\n") if line.strip()]
            self.assertEqual(len(lines), 5, "Expected exactly 5 rows in the single export file")

            # Verify each line is valid JSON and collect transaction IDs
            xact_ids = []
            for line in lines:
                json_obj = json.loads(line)
                self.assertIsInstance(json_obj, dict)

                # Verify _xact_id is present and collect it
                self.assertIn("_xact_id", json_obj, "Each log entry should have a _xact_id field")
                xact_ids.append(json_obj["_xact_id"])

            # Verify all entries have the same transaction ID
            self.assertEqual(
                len(set(xact_ids)),
                1,
                "All entries should have the same _xact_id since they were logged without intermediate flushes",
            )
            self.assertIsNotNone(xact_ids[0], "Transaction ID should not be None")
