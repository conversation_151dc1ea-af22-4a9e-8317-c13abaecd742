import braintrust
from pydantic import BaseModel


class GetLoadDataInput(BaseModel):
    load_id: str
    bearer_token: str


def get_load_data(load_id: str, bearer_token: str):
    return {
        "load_id": load_id,
        "bearer_token": bearer_token,
        "type": "load",
        "contents": "load contents",
        "status": "in progress",
    }


project = braintrust.projects.create(name="managed_tool_with_response_format")

tool = project.tools.create(
    name="Get Load Data",
    description="Get load data for a given load id and bearer token.",
    handler=get_load_data,
    parameters=GetLoadDataInput,
    if_exists="replace",
)

prompt = project.prompts.create(
    name="Get Load Data Prompt",
    slug="get-load-data-prompt",
    messages=[
        {
            "role": "system",
            "content": """You are AI Assistant. Your goal is to answer to any questions about the load.

Load Id: PD-21934
Bearer Token: elEKoriShqjsXdMbJ7cc6L9I9DTGv3Dz2RGXIi+hTs0juDEp1htKZTayePxU2FjF""",
        },
        {
            "role": "user",
            "content": "What's the status of load?",
        },
    ],
    tools=[tool],
    model="gpt-4o",
    params={
        "response_format": {
            "type": "json_schema",
            "json_schema": {
                "name": "tool_test_response",
                "schema": {
                    "type": "object",
                    "properties": {"status": {"type": "string"}},
                    "required": ["status"],
                    "additionalProperties": False,
                },
                "strict": True,
            },
        },
    },
    if_exists="replace",
)
