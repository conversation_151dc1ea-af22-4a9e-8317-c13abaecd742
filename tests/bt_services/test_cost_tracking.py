import time
from dataclasses import dataclass
from typing import List, Optional

import braintrust
import redis
import requests
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, make_v1_url
from tests.bt_services.experiment_test_base import ExperimentTestBase

MODEL_COSTS = {
    "gpt-4o": {
        "format": "openai",
        "flavor": "chat",
        "multimodal": True,
        "input_cost_per_mil_tokens": 2.5,
        "output_cost_per_mil_tokens": 10,
        "input_cache_read_cost_per_mil_tokens": 1.25,
        "displayName": "GPT-4o",
    },
    "claude-sonnet-4-20250514": {
        "format": "anthropic",
        "flavor": "chat",
        "multimodal": True,
        "input_cost_per_mil_tokens": 3,
        "output_cost_per_mil_tokens": 15,
        "input_cache_read_cost_per_mil_tokens": 0.3,
        "input_cache_write_cost_per_mil_tokens": 3.75,
        "displayName": "Claude 4 Sonnet",
    },
}


@dataclass
class ModelCall:
    model: str
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    prompt_cached_tokens: Optional[int] = None
    prompt_cache_creation_tokens: Optional[int] = None

    def get_logging_info(self):
        metrics = {}
        if self.prompt_tokens is not None:
            metrics["prompt_tokens"] = self.prompt_tokens
        if self.prompt_cached_tokens is not None:
            metrics["prompt_cached_tokens"] = self.prompt_cached_tokens
        if self.prompt_cache_creation_tokens is not None:
            metrics["prompt_cache_creation_tokens"] = self.prompt_cache_creation_tokens
        if self.completion_tokens is not None:
            metrics["completion_tokens"] = self.completion_tokens
        return {
            "metadata": {
                "model": self.model,
            },
            "metrics": metrics,
        }

    def get_cost(self) -> float:
        model_cost = MODEL_COSTS[self.model]
        prompt_tokens = self.prompt_tokens or 0
        prompt_cached_tokens = self.prompt_cached_tokens or 0
        prompt_cache_creation_tokens = self.prompt_cache_creation_tokens or 0
        completion_tokens = self.completion_tokens or 0
        return (
            (prompt_tokens - prompt_cached_tokens - prompt_cache_creation_tokens)
            * model_cost["input_cost_per_mil_tokens"]
            / 1000000
            + (
                prompt_cached_tokens
                * model_cost.get("input_cache_read_cost_per_mil_tokens", model_cost["input_cost_per_mil_tokens"])
                / 1000000
            )
            + (
                prompt_cache_creation_tokens
                * model_cost.get("input_cache_write_cost_per_mil_tokens", model_cost["input_cost_per_mil_tokens"])
                / 1000000
            )
            + (completion_tokens * model_cost["output_cost_per_mil_tokens"] / 1000000)
        )


class CostTest(ExperimentTestBase):
    def setUp(self):
        super().setUp()

    def run_cost_test(self, calls: List[ModelCall]):
        experiment = braintrust.init_experiment("p")

        with experiment.start_span("call") as span:
            for call in calls:
                with span.start_span(type="llm") as llm_call:
                    llm_call.log(input="a", output="b", **call.get_logging_info())

        summary = experiment.summarize()
        expected_cost = sum(call.get_cost() for call in calls)
        if "estimated_cost" not in summary.metrics:
            self.assertEqual(expected_cost, 0)
        else:
            self.assertAlmostEqual(summary.metrics["estimated_cost"].metric, expected_cost)

    def test_costs(self):
        # Test a variety of scenarios with different models and token configurations
        test_cases = [
            # Basic cases with different models
            [ModelCall(model="gpt-4o", prompt_tokens=1000, completion_tokens=200)],
            [ModelCall(model="claude-sonnet-4-20250514", prompt_tokens=500, completion_tokens=100)],
            # Test None vs 0 values
            [ModelCall(model="gpt-4o", prompt_tokens=None, completion_tokens=50)],
            [ModelCall(model="gpt-4o", prompt_tokens=0, completion_tokens=50)],
            [ModelCall(model="claude-sonnet-4-20250514", prompt_tokens=200, completion_tokens=None)],
            [ModelCall(model="claude-sonnet-4-20250514", prompt_tokens=200, completion_tokens=0)],
            # Test cache scenarios
            [ModelCall(model="gpt-4o", prompt_tokens=1000, prompt_cached_tokens=500, completion_tokens=100)],
            [ModelCall(model="gpt-4o", prompt_tokens=1000, prompt_cache_creation_tokens=300, completion_tokens=100)],
            # Multiple calls in a single test
            [
                ModelCall(model="gpt-4o", prompt_tokens=300, completion_tokens=50),
                ModelCall(model="gpt-4o", prompt_tokens=200, completion_tokens=30),
                ModelCall(model="claude-sonnet-4-20250514", prompt_tokens=400, completion_tokens=80),
            ],
            # Complex mixed scenario
            [
                ModelCall(model="gpt-4o", prompt_tokens=800, prompt_cached_tokens=200, completion_tokens=150),
                ModelCall(model="gpt-4o", prompt_tokens=600, prompt_cache_creation_tokens=100, completion_tokens=120),
                ModelCall(model="claude-sonnet-4-20250514", prompt_tokens=300, completion_tokens=50),
            ],
        ]

        for test_case in test_cases:
            self.run_cost_test(test_case)
