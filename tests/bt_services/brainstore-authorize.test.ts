import { expect, test } from "vitest";
import { LOCAL_APP_URL } from "./setup";
import { withAppDbClient } from "./setup";
import * as ed from "@noble/ed25519";
import pg from "pg";

// TODO(sachin): figure out how to factor this out into @braintrust/local/dev.
// It also needs to be tested.
export async function createBrainstoreLicense(
  db: pg.PoolClient,
  privateKey: Buffer,
  orgId: string,
) {
  const payload = {
    licenseKeyVersion: 1,
    orgId,
    created: new Date().toISOString(),
  };
  const payloadBytes = Buffer.from(
    JSON.stringify(payload, Object.keys(payload).sort()),
  );

  const signature = Buffer.from(
    await ed.signAsync(payloadBytes, privateKey),
  ).toString("base64");

  const license = Buffer.from(
    JSON.stringify({ ...payload, signature }),
  ).toString("base64");

  await db.query(
    "insert into brainstore_licenses (license, org_id) values ($1, $2)",
    [license, orgId],
  );
  return `brainstore-${license}`;
}

test("brainstore authorization", async ({ orgId }) => {
  const nonce = Buffer.from(
    crypto.getRandomValues(new Uint8Array(32)),
  ).toString("base64");

  // Invalid version.
  let resp = await fetch(`${LOCAL_APP_URL}/api/brainstore/authorize`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      version: 2,
      nonce,
      license: "brainstore-test",
    }),
  });
  expect(resp.status).toBe(400);

  // Malformed license.
  resp = await fetch(`${LOCAL_APP_URL}/api/brainstore/authorize`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      version: 1,
      nonce,
      license: "test",
    }),
  });
  expect(resp.status).toBe(401);

  // Invalid license.
  resp = await fetch(`${LOCAL_APP_URL}/api/brainstore/authorize`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      version: 1,
      nonce,
      license:
        "brainstore-eyJsaWNlbnNlS2V5VmVyc2lvbiI6MSwib3JnSWQiOiJhYmI5ZjNlNC03ZmRkLTRjY2MtYWY0MC1mN2U4OTRmZDQxMjUiLCJjcmVhdGVkIjoiMjAyNS0wMS0yMlQwNDoyOToxNS4wNzhaIiwic2lnbmF0dXJlIjoiWUYyNTh2MkhvTjJzcCswRDVUeHZ4SEVtQytaVFkrVGxoVThtdTBaTzd3LzZZQlJtNWcyOU94SUpTM1BFcHlGNUZCeFNNeTg2Z1pHOXYxZnE2NGs1QXc9PSJ9",
    }),
  });
  expect(resp.status).toBe(401);

  const license: string = await withAppDbClient(async (client) =>
    createBrainstoreLicense(
      client,
      Buffer.from(await ed.utils.randomPrivateKey()),
      orgId,
    ),
  );

  // Valid license.
  resp = await fetch(`${LOCAL_APP_URL}/api/brainstore/authorize`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      version: 1,
      nonce,
      license,
    }),
  });

  expect(resp.status).toBe(200);
  const { nonce: respNonce, authorized, signature } = await resp.json();
  expect(respNonce).toEqual(nonce);
  expect(authorized).toEqual(true);

  // Verify signature.
  const payload = Buffer.from(
    JSON.stringify(
      { nonce, authorized },
      Object.keys({ nonce, authorized }).sort(),
    ),
  );
  // Pairs with BRAINSTORE_AUTHORIZE_PRIVATE_KEY in .env.development.
  const publicKey = Buffer.from(
    "Ma1yKVjfNc11djy7S7DA5J+Dd+OcuAH9UJyLHfEiLvI=",
    "base64",
  );
  expect(
    await ed.verifyAsync(Buffer.from(signature, "base64"), payload, publicKey),
  ).toBe(true);
});
