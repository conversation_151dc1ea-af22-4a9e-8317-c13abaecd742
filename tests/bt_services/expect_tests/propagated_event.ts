// Mirrors 'test_propagated_event' in tests/test_distributed_tracing.py.
import { init, traced } from "braintrust";

async function main() {
  const experiment = init({ project: "p" });

  const { spanASlug, spanBSlug } = await experiment.traced(
    async (spanA) => {
      const spanASlug = await spanA.export();
      const spanBSlug = await spanA.traced(
        async (spanB) => {
          return await spanB.export();
        },
        { name: "b" },
      );
      spanA.traced(() => {}, {
        name: "c",
        propagatedEvent: { metrics: { foo: 0.2 } },
      });
      return { spanASlug, spanBSlug };
    },
    { name: "a", propagatedEvent: { metrics: { foo: 0.1 } } },
  );

  traced(
    () => {
      traced(
        () => {
          traced(() => {}, {
            name: "f",
            parent: spanBSlug,
            propagatedEvent: {},
          });
        },
        { name: "e" },
      );
    },
    { name: "d", parent: spanASlug },
  );

  console.log((await experiment.summarize()).experimentUrl);
}

main();
