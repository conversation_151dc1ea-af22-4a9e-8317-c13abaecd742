from braintrust import Eva<PERSON>, current_span, traced

from autoevals import LevenshteinScorer


def basic_task(input, hooks):
    return "a"


def create_foo(cls):
    def foo(expected, output):
        if cls != expected:
            return None
        return 1 if expected == output else 0

    return foo


Eval(
    "basic-error",
    data=lambda: [dict(input="a", expected="b")],
    task=basic_task,
    scores=[create_foo("a")],
    experiment_name="My basic eval",
)
