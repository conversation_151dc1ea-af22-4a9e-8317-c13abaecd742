import isEqual from "lodash.isequal";
import { v4 as uuidv4 } from "uuid";
import { View } from "@braintrust/typespecs";
import { initLogger } from "braintrust";
import { viewSchema } from "@braintrust/typespecs";

const logger = initLogger({ projectName: `calculator-${uuidv4()}` });
const API_URL = "http://127.0.0.1:8000";

type Method = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

async function assertThrows(f: () => Promise<void>) {
  let didThrow = false;
  try {
    await f();
  } catch {
    didThrow = true;
  }
  if (!didThrow) {
    throw new Error("Function did not throw");
  }
}

const comparableViewSchema = viewSchema
  .omit({
    created: true,
    deleted_at: true,
    user_id: true,
  })
  .strip();

function areViewsEquivalent({
  actual,
  expected,
  ignoreId,
}: {
  actual: Partial<View>;
  expected: Partial<View>;
  ignoreId?: boolean;
}): boolean {
  const schema = ignoreId
    ? comparableViewSchema.omit({ id: true })
    : comparableViewSchema;
  const actualC = schema.parse(actual);
  const expectedC = schema.parse(expected);
  return isEqual(actualC, expectedC);
}

async function main() {
  const project = await logger.project;
  const orgId = await logger.org_id;
  const idToken = process.env.BRAINTRUST_API_KEY ?? null;

  const apiFetch = async ({
    method,
    url,
    data,
  }: {
    method: Method;
    url: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    data?: any;
  }) => {
    return await fetch(url, {
      method,
      ...(data ? { body: JSON.stringify(data) } : {}),
      mode: "cors",
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      credentials: "include",
    }).then((res) => res.json());
  };

  // Define and insert a view named `birds`.
  const birds: Omit<View, "id"> = {
    object_type: "project",
    object_id: project.id,
    view_type: "logs",
    name: "birds",
    view_data: {
      search: {
        filter: ["input ILIKE '%hawk%'", "output ILIKE '%gpt%'"],
        tag: ["+raven", "-hawk"],
      },
    },
    options: {
      columnSizing: { "0": 100, "1": 50 },
    },
  };
  const birdsPost = await apiFetch({
    method: "POST",
    url: `${API_URL}/v1/view`,
    data: birds,
  });
  const { id: birdsId } = birdsPost;
  if (
    !areViewsEquivalent({ actual: birdsPost, expected: birds, ignoreId: true })
  ) {
    throw new Error("birds POST response does not match original object");
  }
  const birdsGet = await apiFetch({
    method: "GET",
    url:
      `${API_URL}/v1/view/${birdsId}?` +
      new URLSearchParams({
        object_type: "project",
        object_id: project.id,
      }),
  });
  if (!areViewsEquivalent({ actual: birdsGet, expected: birdsPost })) {
    throw new Error("birds GET response does not match POSTed object");
  }

  // Try to insert another view with the same (object_type, object_id, view_type, name).
  // Should conflict and do nothing.
  const birdsNew: Omit<View, "id"> = {
    ...birds,
    view_data: {
      search: {
        filter: ["metadata.num_wings = 2"],
      },
    },
    options: {
      columnOrder: ["1", "0"],
    },
  };
  const birdsNewPostResult = await apiFetch({
    method: "POST",
    url: `${API_URL}/v1/view`,
    data: birdsNew,
  });
  if (
    !areViewsEquivalent({ actual: birdsNewPostResult, expected: birdsPost })
  ) {
    throw new Error(
      "birdsNew POST should have conflicted and returned the original view",
    );
  }
  const birdsNewPutResult = await apiFetch({
    method: "PUT",
    url: `${API_URL}/v1/view`,
    data: birdsNew,
  });
  if (
    !areViewsEquivalent({
      actual: birdsNewPutResult,
      expected: birdsNew,
      ignoreId: true,
    })
  ) {
    throw new Error("birdsNew PUT should have conflicted and updated the view");
  }

  // Retrieve all views now to make sure `viewA` is still the only view.
  let projectLogsViews: View[] = (
    await apiFetch({
      method: "GET",
      url:
        `${API_URL}/v1/view?` +
        new URLSearchParams({
          object_type: "project",
          object_id: project.id,
          view_type: "logs",
        }),
    })
  ).objects;
  if (projectLogsViews.length !== 1) {
    throw new Error("Expected only one view to be present");
  }
  if (
    !areViewsEquivalent({
      actual: projectLogsViews[0],
      expected: { id: birdsId, ...birdsNew },
    })
  ) {
    throw new Error("Expected to fetch view 'birds'");
  }

  // Update the view with a PATCH to `/v1/view/:id`
  const birds2 = {
    ...birds,
    view_data: {
      search: {
        match: ["hissing"],
      },
    },
    options: {
      columnSizing: { "0": 124, "1": 30 },
    },
  };
  const birds2Patch = await apiFetch({
    method: "PATCH",
    url: `${API_URL}/v1/view/${birdsId}`,
    data: birds2,
  });
  if (
    !areViewsEquivalent({
      actual: birds2Patch,
      expected: birds2,
      ignoreId: true,
    })
  ) {
    throw new Error("Birds2 PATCH response does not match original object");
  }
  const birds2Get = await apiFetch({
    method: "GET",
    url:
      `${API_URL}/v1/view/${birdsId}?` +
      new URLSearchParams({
        object_type: "project",
        object_id: project.id,
      }),
  });
  if (!areViewsEquivalent({ actual: birds2Get, expected: birds2Patch })) {
    throw new Error("Birds2 GET does not match PATCH response");
  }

  // Insert an organization-scoped view with the same name.
  const orgBirds: Omit<View, "id"> = {
    object_type: "org_project",
    object_id: orgId,
    view_type: "projects",
    name: "birds",
    view_data: null,
    options: null,
  };
  const orgBirdsPost = await apiFetch({
    method: "POST",
    url: `${API_URL}/v1/view`,
    data: orgBirds,
  });
  if (
    !areViewsEquivalent({
      actual: orgBirdsPost,
      expected: orgBirds,
      ignoreId: true,
    })
  ) {
    throw new Error("orgBirds POST response does not match original object");
  }

  // Verify that the org view appears when fetching from the org
  const orgLogsViews: View[] = (
    await apiFetch({
      method: "GET",
      url:
        `${API_URL}/v1/view?` +
        new URLSearchParams({
          object_type: "org_project",
          object_id: orgId,
          view_type: "projects",
        }),
    })
  ).objects;
  if (orgLogsViews.length !== 1) {
    throw new Error("Expected exactly 1 org logs view after orgBirds POST");
  }
  if (
    !areViewsEquivalent({ actual: orgLogsViews[0], expected: orgBirdsPost })
  ) {
    throw new Error("Fetched org logs view does not match orgBirds POST");
  }

  // Verify that the org view is not returned when fetching from the project.
  projectLogsViews = (
    await apiFetch({
      method: "GET",
      url:
        `${API_URL}/v1/view?` +
        new URLSearchParams({
          object_type: "project",
          object_id: project.id,
          view_type: "logs",
        }),
    })
  ).objects;
  if (projectLogsViews.length !== 1) {
    throw new Error("Expected only one view to be present");
  }
  // if (!deepEqual(projectLogsViews[0], birdsPost)) {
  if (
    !areViewsEquivalent({ actual: projectLogsViews[0], expected: birds2Patch })
  ) {
    throw new Error("Fetched project logs view does not match birds POST");
  }

  // Insert a new project-scoped view.
  const insects: Omit<View, "id"> = {
    object_type: "project",
    object_id: project.id,
    view_type: "logs",
    name: "insects",
    view_data: {
      search: {
        filter: ["num_legs = 6"],
        sort: ["size DESC"],
      },
    },
    options: {
      columnVisibility: { "0": false, "1": true },
    },
  };
  const insectsPost = await apiFetch({
    method: "POST",
    url: `${API_URL}/v1/view`,
    data: insects,
  });
  const { id: insectsId } = insectsPost;

  // Verify that the new view appears when fetching from the project.
  projectLogsViews = (
    await apiFetch({
      method: "GET",
      url:
        `${API_URL}/v1/view?` +
        new URLSearchParams({
          object_type: "project",
          object_id: project.id,
          view_type: "logs",
        }),
    })
  ).objects;
  if (projectLogsViews.length !== 2) {
    throw new Error("Expected two project views");
  }
  let projectLogsViewsById = Object.fromEntries(
    projectLogsViews.map((v) => [v.id, v]),
  );
  if (
    !areViewsEquivalent({
      actual: projectLogsViewsById[birdsId],
      expected: birds2Patch,
    })
  ) {
    throw new Error("Expected to fetch view 'birds'");
  }
  if (
    !areViewsEquivalent({
      actual: projectLogsViewsById[insectsId],
      expected: insectsPost,
    })
  ) {
    throw new Error("insects POST response does not match original object");
  }

  // Should error if we try to rename "snakes" to "birds" because it conflicts with
  // an existing view
  await assertThrows(async () => {
    await apiFetch({
      method: "PATCH",
      url: `${API_URL}/v1/view/${insectsId}`,
      data: {
        object_type: "project",
        object_id: project.id,
        name: "birds",
      },
    });
  });

  // Delete 'insects'.
  const insectsDelete = await apiFetch({
    method: "DELETE",
    url: `${API_URL}/v1/view/${insectsId}`,
    data: {
      object_type: "project",
      object_id: project.id,
    },
  });
  // Confirm that the response matches the original object.
  if (!areViewsEquivalent({ actual: insectsDelete, expected: insectsPost })) {
    throw new Error("insects DELETE response does not match POST");
  }
  // Confirm that only 'birds' is left
  projectLogsViews = (
    await apiFetch({
      method: "GET",
      url:
        `${API_URL}/v1/view?` +
        new URLSearchParams({
          object_type: "project",
          object_id: project.id,
          view_type: "logs",
        }),
    })
  ).objects;
  if (projectLogsViews.length !== 1) {
    throw new Error(
      "Expected only 'birds' to be present after 'insects' DELETE",
    );
  }
  if (
    !areViewsEquivalent({ actual: projectLogsViews[0], expected: birds2Patch })
  ) {
    throw new Error("Expected view 'birds' to match");
  }

  // Recreating `insects` should not cause a conflict because the original `viewA`
  // was previously deleted.
  const insects2: Omit<View, "id"> = {
    object_type: "project",
    object_id: project.id,
    view_type: "logs",
    name: "insects",
    options: {},
  };
  const insects2Post = await apiFetch({
    method: "POST",
    url: `${API_URL}/v1/view`,
    data: insects2,
  });
  const { id: insects2Id } = insects2Post;
  projectLogsViews = (
    await apiFetch({
      method: "GET",
      url:
        `${API_URL}/v1/view?` +
        new URLSearchParams({
          object_type: "project",
          object_id: project.id,
          view_type: "logs",
        }),
    })
  ).objects;
  if (projectLogsViews.length !== 2) {
    throw new Error(
      "Expected both 'birds' and 'insects' to be present after insects2 POST",
    );
  }
  projectLogsViewsById = Object.fromEntries(
    projectLogsViews.map((v) => [v.id, v]),
  );
  if (
    !areViewsEquivalent({
      actual: projectLogsViewsById[birdsId],
      expected: birds2Patch,
    })
  ) {
    throw new Error("Expected to fetch view 'birds'");
  }
  if (
    !areViewsEquivalent({
      actual: projectLogsViewsById[insects2Id],
      expected: insects2Post,
    })
  ) {
    throw new Error("Expected to fetch view 'insects'");
  }
}

main();
