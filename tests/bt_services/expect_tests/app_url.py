import os

import braintrust
from braintrust import Eval

braintrust.login()
api_url = braintrust.logger._state.api_url
braintrust.login(app_url=api_url, force_login=True)


def exact_match(input, expected, output):
    return 1 if output == expected else 0


summary = Eval(
    "My Evaluation",
    data=[dict(input="Which country has the highest population?", expected="China")],
    task=lambda input: "China",
    scores=[exact_match],
)

# This is a cheap way of validating that the API url is in the summary, which implies that
# it was used as the APP_URL. Note in practice, we recommend users to set BRAINTRUST_APP_PUBLIC_URL
# to be the webapp URL.
assert api_url in summary.summary.experiment_url, summary.summary.experiment_url
