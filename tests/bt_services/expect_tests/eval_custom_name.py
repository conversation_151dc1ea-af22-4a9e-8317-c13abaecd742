from braintrust import Eval, Score

from autoevals import JSONDiff


def my_scorer(output, expected):
    return J<PERSON><PERSON>iff()(output, expected)


def my_handrolled_scorer(output, expected):
    return Score(name="Custom scorer", score=0.5)


def make_scorer():
    def my_generated_scorer(output, expected):
        return 0.5

    return my_generated_scorer


Eval(
    "Custom scorer",
    data=[{"input": "a", "expected": "b"}],
    task=lambda input: input,
    scores=[my_scorer, my_handrolled_scorer, make_scorer()],
)
