[{"context": {}, "expected": 1, "id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "input": "1", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "span_parents": null, "tags": null}, {"context": {}, "expected": 1, "id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "input": "1", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "81c806912e924c55c731a069106ae058036ee12ff35d52eac2a57516cc229ff5", "input": "1", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "81c806912e924c55c731a069106ae058036ee12ff35d52eac2a57516cc229ff5", "span_parents": ["1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01"], "tags": null}, {"context": {}, "expected": null, "id": "81c806912e924c55c731a069106ae058036ee12ff35d52eac2a57516cc229ff5", "input": "1", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "81c806912e924c55c731a069106ae058036ee12ff35d52eac2a57516cc229ff5", "span_parents": ["1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01"], "tags": null}, {"context": {}, "expected": null, "id": "e5a181faba6b71e2d69770508b1296084367aa7523caf90f1c24aa1e59454b79", "input": {"expected": 1, "input": "1", "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e5a181faba6b71e2d69770508b1296084367aa7523caf90f1c24aa1e59454b79", "span_parents": ["1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01"], "tags": null}, {"context": {}, "expected": null, "id": "e5a181faba6b71e2d69770508b1296084367aa7523caf90f1c24aa1e59454b79", "input": {"expected": 1, "input": "1", "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e5a181faba6b71e2d69770508b1296084367aa7523caf90f1c24aa1e59454b79", "span_parents": ["1a66868c887e80cfafd56d1254324edae07706d2fa0eb01038d7cbf3a79b2f01"], "tags": null}]