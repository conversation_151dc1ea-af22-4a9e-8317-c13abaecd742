[{"context": {}, "expected": {"content_type": "text/plain", "filename": "greeting.txt", "type": "braintrust_attachment"}, "id": "b3bb18a8c5737a97053d9fdbde8a10ef91cc400ae36ec9c098269f2eb7eee649", "input": {"attachment": [{"file": {"content_type": "text/plain", "filename": "greeting.txt", "type": "braintrust_attachment"}}], "image": {"content_type": "image/jpeg", "filename": "attachment.jpg", "type": "braintrust_attachment"}, "image2": {"content_type": "image/jpeg", "filename": "user_input.jpg", "type": "braintrust_attachment"}, "text": "hello world"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"content_type": "text/plain", "filename": "greeting.txt", "type": "braintrust_attachment"}, "root_span_id": "b3bb18a8c5737a97053d9fdbde8a10ef91cc400ae36ec9c098269f2eb7eee649", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "b3bb18a8c5737a97053d9fdbde8a10ef91cc400ae36ec9c098269f2eb7eee649", "span_parents": null, "tags": null}, {"expected": {"content_type": "image/jpeg", "filename": "attachment.jpg", "type": "braintrust_attachment"}, "id": "0fd9d2709a38f4d77532f4c360a321d0659f839bd6d2ab2aec286fbe158121d0", "input": {"content_type": "image/jpeg", "filename": "user_input.jpg", "type": "braintrust_attachment"}, "is_root": true, "metadata": {"attachment": {"content_type": "text/plain", "filename": "greeting.txt", "type": "braintrust_attachment"}}, "origin": null, "root_span_id": "0fd9d2709a38f4d77532f4c360a321d0659f839bd6d2ab2aec286fbe158121d0", "span_id": "0fd9d2709a38f4d77532f4c360a321d0659f839bd6d2ab2aec286fbe158121d0", "tags": null}, {"context": {}, "expected": {"content_type": "audio/wav", "filename": "model response.wav", "type": "braintrust_attachment"}, "id": "fc4669d1ba298b26fde47d2b017b20e9d0554a2efdd3c395427b144bc2c1aa92", "input": {"context": {"content_type": "image/jpeg", "filename": "attachment.jpg", "type": "braintrust_attachment"}, "question": "What is this?"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"audio": {"content_type": "audio/wav", "filename": "model response.wav", "type": "braintrust_attachment"}, "manyAttachments": [{"content_type": "image/jpeg", "filename": "attachment.jpg", "type": "braintrust_attachment"}, {"content_type": "image/jpeg", "filename": "user_input.jpg", "type": "braintrust_attachment"}, {"content_type": "audio/wav", "filename": "model response.wav", "type": "braintrust_attachment"}, {"content_type": "text/plain", "filename": "greeting.txt", "type": "braintrust_attachment"}, null, null, "string", 42], "text": "text response"}, "root_span_id": "fc4669d1ba298b26fde47d2b017b20e9d0554a2efdd3c395427b144bc2c1aa92", "scores": {"metric1": 0.5, "metric2": 1}, "span_attributes": {"name": "root", "type": "task"}, "span_id": "fc4669d1ba298b26fde47d2b017b20e9d0554a2efdd3c395427b144bc2c1aa92", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8567041ad4c8f5d332188cd22bd915da9d421dea8fd1da5437e54765a93462ef", "input": {"count": 1}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"attachment": {"content_type": "text/plain", "filename": "greeting.txt", "type": "braintrust_attachment"}}, "root_span_id": "8567041ad4c8f5d332188cd22bd915da9d421dea8fd1da5437e54765a93462ef", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "8567041ad4c8f5d332188cd22bd915da9d421dea8fd1da5437e54765a93462ef", "span_parents": null, "tags": null}]