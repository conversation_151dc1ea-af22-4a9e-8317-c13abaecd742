import asyncio
import json
import os
from typing import Any, Dict, List, Optional

import httpx
from anthropic import Async<PERSON>nthropic as Anthropic
from anthropic.types import Usage
from braintrust import init_experiment, wrap_anthropic, wrap_openai
from openai import AsyncOpenAI  # type: ignore
from openai.types.chat import ChatCompletionMessageParam
from openai.types.completion_usage import CompletionUsage
from pydantic import BaseModel
from typing_extensions import TypedDict


def deep_equals(a: Any, b: Any) -> bool:
    if hasattr(a, "model_dump"):
        a = a.model_dump()
    if hasattr(b, "model_dump"):
        b = b.model_dump()
    return json.dumps(a, sort_keys=True) == json.dumps(b, sort_keys=True)


# Since this test involves the prompt cache, change this NONCE to a new subject when updating.
NONCE1 = "zebras"
NONCE2 = "elephants"


class BraintrustMetrics(BaseModel):
    completion_tokens: Optional[int] = None
    prompt_cache_creation_tokens: Optional[int] = None
    prompt_cached_tokens: Optional[int] = None
    prompt_tokens: Optional[int] = None
    tokens: Optional[int] = None


async def get_experiment_metrics(experiment_id: str) -> List[BraintrustMetrics]:
    async with httpx.AsyncClient() as client:
        results = await client.post(
            "http://localhost:8000/btql",
            headers={
                "Authorization": f"Bearer {os.environ.get('BRAINTRUST_API_KEY')}",
            },
            json={
                "query": f"select: metrics | from: experiment('{experiment_id}') spans | filter: span_attributes.type='llm' | sort: created",
            },
        )
    if not results.is_success:
        raise Exception(f"Failed to get experiment metrics: {results.reason_phrase}")

    data = results.json()
    return [BraintrustMetrics.parse_obj(d["metrics"]) for d in data["data"]]


def anthropic_usage_equals_braintrust_metrics(
    anthropic_usage,
    bt_metrics,
) -> bool:
    if anthropic_usage.output_tokens != bt_metrics.completion_tokens:
        raise Exception(
            f"Output tokens do not match: {anthropic_usage.output_tokens} !== {bt_metrics.completion_tokens}"
        )
    if (
        anthropic_usage.input_tokens
        + (anthropic_usage.cache_read_input_tokens or 0)
        + (anthropic_usage.cache_creation_input_tokens or 0)
        != bt_metrics.prompt_tokens
    ):
        raise Exception(
            f"Prompt tokens do not match: {anthropic_usage.input_tokens} + {anthropic_usage.cache_read_input_tokens} + {anthropic_usage.cache_creation_input_tokens} !== {bt_metrics.prompt_tokens}"
        )
    if (anthropic_usage.cache_read_input_tokens or 0) != bt_metrics.prompt_cached_tokens:
        raise Exception(
            f"Cache read input tokens do not match: {anthropic_usage.cache_read_input_tokens} + {anthropic_usage.cache_creation_input_tokens} !== {bt_metrics.prompt_cached_tokens}"
        )
    if (anthropic_usage.cache_creation_input_tokens or 0) != bt_metrics.prompt_cache_creation_tokens:
        raise Exception(
            f"Cache creation input tokens do not match: {anthropic_usage.cache_read_input_tokens} + {anthropic_usage.cache_creation_input_tokens} !== {bt_metrics.prompt_cache_creation_tokens}"
        )
    if (anthropic_usage.cache_read_input_tokens or 0) + (
        anthropic_usage.cache_creation_input_tokens or 0
    ) + anthropic_usage.input_tokens + anthropic_usage.output_tokens != bt_metrics.tokens:
        raise Exception(
            f"Total tokens do not match: {anthropic_usage.cache_read_input_tokens} + {anthropic_usage.cache_creation_input_tokens} + {anthropic_usage.input_tokens} + {anthropic_usage.output_tokens} !== {bt_metrics.tokens}"
        )
    return True


class RunAnthropicMessagesReturn(TypedDict):
    non_streaming_usage: Usage
    streaming_usage: Usage
    non_streaming_bt_metrics: BraintrustMetrics
    streaming_bt_metrics: BraintrustMetrics


async def run_anthropic_messages(
    anthropic: Anthropic,
    messages: List[Dict[str, Any]],  # Anthropic.Messages.MessageParam is not directly usable here
) -> RunAnthropicMessagesReturn:
    experiment = init_experiment("wrap_anthropic")

    async def non_streaming_call():
        response = await anthropic.messages.create(
            model="claude-3-5-sonnet-latest",
            messages=messages,  # type: ignore
            max_tokens=100,
            stream=False,
            temperature=0,
        )
        return response.usage

    with experiment.start_span():
        non_streaming_usage = await non_streaming_call()  # type: ignore

    await asyncio.sleep(0.05)  # Ensure created timestamps line up

    async def streaming_call():
        response = await anthropic.messages.create(
            model="claude-3-5-sonnet-latest",
            messages=messages,  # type: ignore
            max_tokens=100,
            stream=True,
            temperature=0,
        )
        all_usage: Dict[str, Any] = {}
        async for chunk in response:
            if chunk.type == "message_start":
                all_usage.update(
                    {
                        k: v
                        for k, v in (chunk.message.usage.model_dump() if chunk.message.usage else {}).items()
                        if v is not None
                    }
                )
            elif chunk.type == "message_delta":
                # anthropic.types.MessageDeltaUsage is not directly usable here
                all_usage.update(
                    {k: v for k, v in (chunk.usage.model_dump() if chunk.usage else {}).items() if v is not None}
                )
        return Usage(**all_usage)  # type: ignore

    with experiment.start_span():
        streaming_usage = await streaming_call()  # type: ignore

    experiment.flush()

    non_streaming_bt_metrics, streaming_bt_metrics = await get_experiment_metrics(experiment.id)

    return {
        "non_streaming_usage": non_streaming_usage,
        "streaming_usage": streaming_usage,
        "non_streaming_bt_metrics": non_streaming_bt_metrics,
        "streaming_bt_metrics": streaming_bt_metrics,
    }


async def sdk_test():
    anthropic = wrap_anthropic(Anthropic())

    # First test: simple message
    result1 = await run_anthropic_messages(
        anthropic,
        [{"role": "user", "content": f"Repeat the following word: {NONCE1}"}],
    )

    if not deep_equals(result1["non_streaming_usage"], result1["streaming_usage"]):
        raise Exception(
            f"Mismatch in usage (simple): {result1['non_streaming_usage']} vs {result1['streaming_usage']}"
        )

    anthropic_usage_equals_braintrust_metrics(
        result1["non_streaming_usage"],
        result1["non_streaming_bt_metrics"],
    )
    anthropic_usage_equals_braintrust_metrics(
        result1["streaming_usage"],
        result1["streaming_bt_metrics"],
    )

    # Second test: cache breakpoints
    base_text = f"Yo yo please echo the following word: {NONCE2}. " * 100

    # Create cache breakpoints
    await run_anthropic_messages(
        anthropic,
        [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": base_text,
                        "cache_control": {
                            "type": "ephemeral",
                        },
                    },
                ],
            },
        ],
    )

    await asyncio.sleep(0.1)

    # Run again with a follow-up message
    result2 = await run_anthropic_messages(
        anthropic,
        [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": base_text,
                        "cache_control": {
                            "type": "ephemeral",
                        },
                    },
                ],
            },
            {
                "role": "assistant",
                "content": NONCE2,
            },
            {
                "role": "user",
                "content": f"Repeat the following word: hi",
            },
        ],
    )

    if not deep_equals(result2["non_streaming_usage"], result2["streaming_usage"]):
        raise Exception(f"Mismatch in usage (cache): {result2['non_streaming_usage']} vs {result2['streaming_usage']}")

    anthropic_usage_equals_braintrust_metrics(
        result2["non_streaming_usage"],
        result2["non_streaming_bt_metrics"],
    )
    anthropic_usage_equals_braintrust_metrics(
        result2["streaming_usage"],
        result2["streaming_bt_metrics"],
    )

    # TODO: Anthropic Python SDK does not support cache_control, so cache_read_input_tokens will be 0
    if (result2["non_streaming_usage"].cache_read_input_tokens or 0) == 0:
        raise Exception("Cache read input tokens should not be 0")


class PromptTokensDetails(TypedDict, total=False):
    cached_tokens: Optional[int]
    cache_creation_tokens: Optional[int]


class ExtendedUsage(CompletionUsage):
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    prompt_tokens_details: Optional[PromptTokensDetails] = None


class RunOpenAIMessagesReturn(TypedDict):
    non_streaming_usage: ExtendedUsage
    streaming_usage: ExtendedUsage
    non_streaming_bt_metrics: BraintrustMetrics
    streaming_bt_metrics: BraintrustMetrics


async def run_openai_messages(
    openai: AsyncOpenAI,
    messages: List[ChatCompletionMessageParam],
) -> RunOpenAIMessagesReturn:
    experiment = init_experiment("wrap_anthropic")  # Reusing experiment name from original TS

    async def non_streaming_call():
        response = await openai.chat.completions.create(
            model="claude-3-5-sonnet-latest",  # This seems to be a copy-paste from Anthropic part in TS
            messages=messages,
            max_tokens=100,
            stream=False,
            temperature=0,
        )
        usage_dict = response.usage.model_dump() if response.usage else {}
        if "prompt_tokens_details" not in usage_dict:
            usage_dict["prompt_tokens_details"] = {}
        return ExtendedUsage(**usage_dict)  # type: ignore

    with experiment.start_span():
        non_streaming_usage = await non_streaming_call()  # type: ignore

    await asyncio.sleep(0.05)

    async def streaming_call():
        response = await openai.chat.completions.create(
            model="claude-3-5-sonnet-latest",  # This seems to be a copy-paste from Anthropic part in TS
            messages=messages,
            max_tokens=100,
            stream=True,
            temperature=0,
        )
        all_usage: Dict[str, Any] = {"prompt_tokens_details": {}}
        async for chunk in response:
            if hasattr(chunk, "usage") and chunk.usage:
                # We need to merge, not just update, especially for prompt_tokens_details
                current_usage_dict = chunk.usage.model_dump()
                for key, value in current_usage_dict.items():
                    if key == "prompt_tokens_details" and isinstance(value, dict):
                        all_usage["prompt_tokens_details"].update(value)
                    else:
                        all_usage[key] = value
            elif (
                hasattr(chunk, "x_groq") and chunk.x_groq and hasattr(chunk.x_groq, "usage") and chunk.x_groq.usage
            ):  # Groq specific, but TS was generic
                current_usage_dict = chunk.x_groq.usage.model_dump()
                for key, value in current_usage_dict.items():
                    if key == "prompt_tokens_details" and isinstance(value, dict):
                        all_usage["prompt_tokens_details"].update(value)
                    else:
                        all_usage[key] = value

        # Ensure prompt_tokens_details exists even if empty
        if "prompt_tokens_details" not in all_usage:
            all_usage["prompt_tokens_details"] = {}

        return ExtendedUsage(**all_usage)  # type: ignore

    with experiment.start_span():
        streaming_usage = await streaming_call()  # type: ignore

    experiment.flush()

    non_streaming_bt_metrics, streaming_bt_metrics = await get_experiment_metrics(experiment.id)

    return {
        "non_streaming_usage": non_streaming_usage,
        "streaming_usage": streaming_usage,
        "non_streaming_bt_metrics": non_streaming_bt_metrics,
        "streaming_bt_metrics": streaming_bt_metrics,
    }


def openai_usage_equals_braintrust_metrics(
    openai_usage: ExtendedUsage,
    bt_metrics: BraintrustMetrics,
) -> bool:
    if openai_usage.completion_tokens != bt_metrics.completion_tokens:
        raise Exception(
            f"Output tokens do not match: {openai_usage.completion_tokens} !== {bt_metrics.completion_tokens}"
        )
    if openai_usage.prompt_tokens != bt_metrics.prompt_tokens:
        raise Exception(f"Prompt tokens do not match: {openai_usage.prompt_tokens} !== {bt_metrics.prompt_tokens}")

    prompt_tokens_details = openai_usage.prompt_tokens_details
    if (prompt_tokens_details.get("cached_tokens") or 0) != bt_metrics.prompt_cached_tokens:
        raise Exception(
            f"Cache read input tokens do not match: {prompt_tokens_details.get('cached_tokens')} !== {bt_metrics.prompt_cached_tokens}"
        )
    if (prompt_tokens_details.get("cache_creation_tokens") or 0) != bt_metrics.prompt_cache_creation_tokens:
        raise Exception(
            f"Cache creation input tokens do not match: {prompt_tokens_details.get('cache_creation_tokens')} !== {bt_metrics.prompt_cache_creation_tokens}"
        )
    if (openai_usage.prompt_tokens or 0) + (openai_usage.completion_tokens or 0) != bt_metrics.tokens:
        raise Exception(
            f"Total tokens do not match: {(openai_usage.prompt_tokens or 0)} + {(openai_usage.completion_tokens or 0)} !== {bt_metrics.tokens}"
        )
    return True


async def openai_test():
    openai = wrap_openai(AsyncOpenAI())

    result1 = await run_openai_messages(
        openai,
        [{"role": "user", "content": f"Repeat the following word: {NONCE1}"}],  # type: ignore
    )

    print(result1["non_streaming_usage"])
    print(result1["streaming_usage"])

    if not deep_equals(result1["non_streaming_usage"], result1["streaming_usage"]):
        # Temporary print for debugging if there is a mismatch
        raise Exception(
            f"Mismatch in OpenAI usage (simple): {result1['non_streaming_usage']} vs {result1['streaming_usage']}"
        )

    openai_usage_equals_braintrust_metrics(
        result1["non_streaming_usage"],
        result1["non_streaming_bt_metrics"],
    )
    openai_usage_equals_braintrust_metrics(result1["streaming_usage"], result1["streaming_bt_metrics"])

    # Second test: cache breakpoints
    base_text = f"Yo yo please echo the following word: {NONCE2}. " * 100

    # Create cache breakpoints
    # The TS code uses a specific structure for content when `cache_control` is present.
    # OpenAI Python SDK's `ChatCompletionMessageParam` allows for a list of content parts.
    await run_openai_messages(
        openai,
        [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": base_text,
                        "cache_control": {  # Not standard OpenAI; might be Braintrust-specific for future
                            "type": "ephemeral",
                        },
                    },
                ],
            },
        ],  # type: ignore
    )

    await asyncio.sleep(0.1)

    # Run again with a follow-up message
    result2 = await run_openai_messages(
        openai,
        [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": base_text,
                        "cache_control": {  # Not standard OpenAI
                            "type": "ephemeral",
                        },
                    },
                ],
            },
            {
                "role": "assistant",
                "content": NONCE2,
            },
            {
                "role": "user",
                "content": f"Repeat the following word: hi",
            },
        ],
    )

    if not deep_equals(result2["non_streaming_usage"], result2["streaming_usage"]):
        raise Exception(
            f"Mismatch in OpenAI usage (cache): {result2['non_streaming_usage']} vs {result2['streaming_usage']}"
        )

    openai_usage_equals_braintrust_metrics(
        result2["non_streaming_usage"],
        result2["non_streaming_bt_metrics"],
    )
    openai_usage_equals_braintrust_metrics(result2["streaming_usage"], result2["streaming_bt_metrics"])

    if (result2["non_streaming_usage"].prompt_tokens_details.get("cached_tokens") or 0) == 0:
        raise Exception("Cache read input tokens should not be 0 for OpenAI")


async def main():
    await sdk_test()
    await openai_test()


if __name__ == "__main__":
    asyncio.run(main())
