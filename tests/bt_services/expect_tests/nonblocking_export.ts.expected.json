[{"context": {}, "expected": null, "id": "f12ac9724c46860322ff9e0d837058b995a3cd15db26a5f88dbdc6d2b2cd11dd", "input": "0", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "f12ac9724c46860322ff9e0d837058b995a3cd15db26a5f88dbdc6d2b2cd11dd", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "f12ac9724c46860322ff9e0d837058b995a3cd15db26a5f88dbdc6d2b2cd11dd", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "f12ac9724c46860322ff9e0d837058b995a3cd15db26a5f88dbdc6d2b2cd11dd", "input": "0", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "f12ac9724c46860322ff9e0d837058b995a3cd15db26a5f88dbdc6d2b2cd11dd", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "f12ac9724c46860322ff9e0d837058b995a3cd15db26a5f88dbdc6d2b2cd11dd", "span_parents": null, "tags": null}]