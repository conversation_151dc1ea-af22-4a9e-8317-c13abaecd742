from braintrust import <PERSON><PERSON>

from autoevals import Levenshtein

experiment_name = "update_eval"


Eval(
    name="p",
    experiment_name=experiment_name,
    data=[{"input": "a", "expected": "b"}],
    task=lambda x: x,
    scores=[<PERSON><PERSON>htein],
)
Eval(
    name="p",
    experiment_name=experiment_name,
    data=[{"input": "a", "expected": "b"}],
    task=lambda x: x,
    scores=[<PERSON><PERSON>htein],
    update=True,
)
Eval(
    name="p",
    experiment_name=experiment_name,
    data=[{"input": "a", "expected": "b"}],
    task=lambda x: x,
    scores=[<PERSON><PERSON>htein],
)
