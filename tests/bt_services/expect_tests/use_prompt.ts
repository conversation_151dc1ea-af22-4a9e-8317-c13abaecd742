import { OpenAI } from "openai";
import { initLogger, loadPrompt, wrapOpenAI } from "braintrust";

const logger = initLogger({ projectName: "calculator" });
const API_URL = "http://127.0.0.1:8000";

async function simplePrompt() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content: "You are a calculator. Return results in JSON",
            },
            { role: "user", content: "{{formula}}" },
          ],
        },
        options: {
          params: {
            response_format: { type: "json_object" },
          },
        },
      },
      name: "calculator",
      slug: "calculator",
    }),
  }).then((res) => res.json());

  const prompt = await loadPrompt({
    projectName: "calculator",
    slug: "calculator",
    defaults: {
      model: "gpt-3.5-turbo",
    },
  });

  const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));
  const builtPrompt = prompt.build({ formula: "1+1" });
  console.log(await client.chat.completions.create(builtPrompt));

  console.log(
    await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are a calculator" },
        { role: "user", content: "1+1" },
      ],
    }),
  );
}

async function withTools() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            { role: "system", content: "You are a calculator" },
            { role: "user", content: "{{formula}}" },
          ],
          tools: `
          [{
            "type": "function",
            "function": {
              "description": "Returns the sum of two numbers.",
              "name": "add",
              "parameters": {
                "type": "object",
                "properties": {
                  "a": {
                    "type": "number",
                    "description": "The first number"
                  },
                  "b": {
                    "type": "number",
                    "description": "The second number"
                  }
                },
                "required": ["a", "b"]
              }
            }
          }]
          `,
        },
      },
      name: "calculator-tools",
      slug: "calculator-tools",
    }),
  }).then((res) => res.json());

  const prompt = await loadPrompt({
    projectName: "calculator",
    slug: "calculator-tools",
    defaults: {
      model: "gpt-3.5-turbo",
    },
  });

  const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));
  const builtPrompt = prompt.build({ formula: "1+1" });
  console.log(await client.chat.completions.create(builtPrompt));

  console.log(
    await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are a calculator" },
        { role: "user", content: "1+1" },
      ],
    }),
  );
}

async function withEmptyTools() {
  const buildEmptyToolsPrompt = async (tools: string) => {
    const project = await logger.project;
    const slug = "empty-tools";
    await fetch(`${API_URL}/v1/prompt`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        project_id: project.id,
        prompt_data: {
          prompt: {
            type: "chat",
            messages: [
              { role: "system", content: "You are a calculator" },
              { role: "user", content: "{{formula}}" },
            ],
            tools,
          },
        },
        name: slug,
        slug,
      }),
    }).then((res) => res.json());

    const prompt = await loadPrompt({
      projectName: "calculator",
      slug,
      defaults: {
        model: "gpt-3.5-turbo",
      },
    });

    const client = wrapOpenAI(
      new OpenAI({ apiKey: process.env.OPENAI_API_KEY }),
    );
    const builtPrompt = prompt.build({ formula: "1+1" });
    console.log(await client.chat.completions.create(builtPrompt));
  };

  await buildEmptyToolsPrompt("");
  await buildEmptyToolsPrompt(" ");
}

async function flatInput() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            { role: "system", content: "You are a calculator" },
            { role: "user", content: "{{input}}" },
          ],
        },
      },
      name: "calculator-tools",
      slug: "flat-input",
    }),
  }).then((res) => res.json());

  const prompt = await loadPrompt({
    projectName: "calculator",
    slug: "flat-input",
    defaults: {
      model: "gpt-3.5-turbo",
    },
  });

  const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));
  const builtPrompt = prompt.build("1+1");
  const results = await client.chat.completions.create({
    ...builtPrompt,
    stream: true,
    stream_options: {
      include_usage: true,
    },
  });
  for await (const result of results) {
    console.log(result);
  }
}

async function main() {
  await simplePrompt();
  await withTools();
  await withEmptyTools();
  await flatInput();
}

main();
