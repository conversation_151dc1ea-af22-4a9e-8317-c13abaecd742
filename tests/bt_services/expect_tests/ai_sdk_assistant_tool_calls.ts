import { generateText } from "ai";
import { createOpenAI } from "@ai-sdk/openai";
import { initLogger, wrapAISDKModel } from "braintrust";
import { getTestProxyServerConfig } from "@braintrust/local/dev";

async function main() {
  initLogger({
    projectName: "ai-sdk-assistant-tool-calls",
  });
  const { host, port } = await getTestProxyServerConfig(process.cwd());
  await generateText({
    model: wrapAISDKModel(
      createOpenAI({
        baseURL: `http://${host}:${port}/v1`,
      })("gpt-4o"),
    ),
    messages: [
      { role: "system", content: "Hi!" },
      { role: "assistant", content: "Hello, how can I help?" },
      { role: "user", content: "What is the capital of France?" },
    ],
  });
}

main().catch(console.error);
