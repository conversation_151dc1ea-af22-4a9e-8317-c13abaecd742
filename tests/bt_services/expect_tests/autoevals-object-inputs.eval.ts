import { Summary } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";

// Type assertions below are needed because Summary expects string inputs.
// However, real log inputs can be arbitrary objects.
Eval("autoevals-object-inputs", {
  data: [
    {
      // @ts-expect-error -- summary expects string inputs but we're testing object inputs.
      input: { text: "What's the capital of France?" },
    },
    {
      // @ts-expect-error -- summary expects string inputs but we're testing object inputs.
      input: { text: "Who wrote <PERSON> and <PERSON>?" },
    },
  ],
  task: (x) => x,
  scores: [Summary],
});
