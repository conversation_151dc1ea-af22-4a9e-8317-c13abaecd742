import * as braintrust from "braintrust";

const logger = braintrust.initLogger({ projectName: "bad_scores_shape" });

async function testBadScoresShape() {
  const badScoresShape = [0.5, 0.8, 0.9];
  const span = logger.startSpan();

  let error: unknown = undefined;
  try {
    // Despite the fact that TypeScript demands that scores be an object,
    // we've seen arrays sneak in there.
    // @ts-ignore
    span.log({ input: "a", output: "b", scores: badScoresShape });
    await logger.flush();
  } catch (e) {
    error = e;
  }
  if (error === undefined) {
    throw new Error("Expected error. Scores must be an object, not an array.");
  }
}

async function main() {
  await testBadScoresShape();
}

main();
