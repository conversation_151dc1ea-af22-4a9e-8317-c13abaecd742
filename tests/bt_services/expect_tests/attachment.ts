// This test exercises all paths that we could log an attachment.
//
// Also, unlike the Python attachment expect test, this file also tests that we
// can download the attachments. In Python this is handled by a separate unit
// test.

import {
  Attachment,
  ReadonlyAttachment,
  initDataset,
  initLogger,
} from "braintrust";
import * as braintrust from "braintrust";
import assert from "node:assert";

const projectName = "attachments expect tests";

const logger = initLogger({
  projectName,
  asyncFlush: true,
});

async function main() {
  const image = new Attachment({
    data: "examples/attachment/chaos.jpg",
    filename: "attachment.jpg",
    contentType: "image/jpeg",
  });

  const image2 = new Attachment({
    data: "examples/attachment/landscape.jpg",
    filename: "user_input.jpg",
    contentType: "image/jpeg",
  });

  const audio = new Attachment({
    data: "examples/attachment/strawberry.wav",
    filename: "model response.wav",
    contentType: "audio/wav",
  });

  const text = new Attachment({
    data: new Blob(["hello world"]),
    filename: "greeting.txt",
    contentType: "text/plain",
  });

  assert.strictEqual(await (await text.data()).text(), "hello world");

  // Basic log statement.
  const id = logger.log({
    input: {
      question: "What is this?",
      context: image,
    },
    output: {
      text: "text response",
      audio: audio,
      manyAttachments: [
        image,
        image2,
        audio,
        text,
        null,
        undefined,
        "string",
        42,
      ],
    },
  });

  await braintrust.flush();

  // Test feedback.
  logger.logFeedback({
    id,
    scores: {
      metric1: 0.5,
      metric2: 1.0,
    },
    expected: audio,
  });

  // Test span.
  const span = logger.startSpan();
  span.log({
    input: {
      text: "hello world",
      image,
    },
  });
  span.log({
    input: { image2 },
  });
  // Log nested attachment.
  span.log({
    input: {
      attachment: [{ file: text }],
    },
  });
  await braintrust.flush();

  span.log({ output: text });
  span.logFeedback({ expected: text });
  span.close();

  // Test dataset.
  const dataset = initDataset(projectName, { dataset: "dataset" });

  const datasetId = dataset.insert({
    input: image,
    expected: image2,
  });
  dataset.update({
    id: datasetId,
    input: image,
    expected: image,
  });
  await braintrust.flush();
  dataset.update({
    id: datasetId,
    input: image2,
    expected: image,
    metadata: { attachment: text },
  });
  await braintrust.flush();

  // Test reading dataset.
  const datasetForReading = initDataset(projectName, { dataset: "dataset" });
  let count = 0;

  for await (const record of datasetForReading) {
    count++;
    assert.strictEqual(record.id, datasetId);

    const attachment = record.metadata.attachment;
    assert.ok(attachment instanceof ReadonlyAttachment);

    // Must be different instances.
    assert.notStrictEqual(attachment, text);

    assert.strictEqual(await (await attachment.data()).text(), "hello world");
    assert.deepEqual(await attachment.status(), { upload_status: "done" });
    assert.deepEqual(attachment.reference, text.reference);

    // Log the ReadonlyAttachment to make sure it shows up in the logs.
    logger.log(
      { input: { count }, output: { attachment } },
      { allowConcurrentWithSpans: true },
    );
  }

  assert.strictEqual(count, 1);
}

main();
