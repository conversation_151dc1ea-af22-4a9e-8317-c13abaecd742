[{"context": {}, "expected": null, "id": "66382f251299bd5df19d4b48ca943ff748830e7ca2ac38f27080b78767a5c42d", "input": [{"content": [{"text": "What is the capital of France?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "messages", "model": "gpt-3.5-turbo"}, "metrics": {"completion_tokens": 7, "prompt_tokens": 14, "tokens": 21}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "message": {"content": "The capital of France is Paris.", "role": "assistant"}}], "root_span_id": "66382f251299bd5df19d4b48ca943ff748830e7ca2ac38f27080b78767a5c42d", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "66382f251299bd5df19d4b48ca943ff748830e7ca2ac38f27080b78767a5c42d", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "66382f251299bd5df19d4b48ca943ff748830e7ca2ac38f27080b78767a5c42d", "input": [{"content": [{"text": "What is the capital of France?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "messages", "model": "gpt-3.5-turbo"}, "metrics": {"completion_tokens": 7, "prompt_tokens": 14, "tokens": 21}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "message": {"content": "The capital of France is Paris.", "role": "assistant"}}], "root_span_id": "66382f251299bd5df19d4b48ca943ff748830e7ca2ac38f27080b78767a5c42d", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "66382f251299bd5df19d4b48ca943ff748830e7ca2ac38f27080b78767a5c42d", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "11b708ec04b51e4f34cc4678d574f5e6e4ee02ee2c4af978165d30c7f8009e2e", "input": [{"content": [{"text": "What is the weather in San Francisco and what attractions should I visit?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "prompt", "model": "gpt-3.5-turbo", "temperature": 0, "tools": [{"function": {"description": "Get the weather in a location", "name": "weather", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"location": {"description": "The location to get the weather for", "type": "string"}}, "required": ["location"], "type": "object"}}, "type": "function"}, {"function": {"name": "cityAttractions", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"city": {"type": "string"}}, "required": ["city"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 45, "prompt_tokens": 83, "tokens": 128}, "origin": null, "output": [{"finish_reason": "tool-calls", "index": 0, "message": {"content": "", "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"location\": \"San Francisco\"}", "name": "weather"}, "id": "call_qvCK5KWgqaomAIT8uIHsloCM", "type": "function"}, {"function": {"arguments": "{\"city\": \"San Francisco\"}", "name": "cityAttractions"}, "id": "call_wchq5kXITLOItDGGLkj38w10", "type": "function"}]}}], "root_span_id": "11b708ec04b51e4f34cc4678d574f5e6e4ee02ee2c4af978165d30c7f8009e2e", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "11b708ec04b51e4f34cc4678d574f5e6e4ee02ee2c4af978165d30c7f8009e2e", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "5c603a576afaa33a011b7318a95e9777c8e9992d00613b6da3840cd7c54c9920", "input": [{"content": [{"text": "What is the weather in San Francisco and what attractions should I visit?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "prompt", "model": "gpt-3.5-turbo", "temperature": 0, "tools": [{"function": {"description": "Get the weather in a location", "name": "weather", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"location": {"description": "The location to get the weather for", "type": "string"}}, "required": ["location"], "type": "object"}}, "type": "function"}, {"function": {"name": "cityAttractions", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"city": {"type": "string"}}, "required": ["city"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 45, "prompt_tokens": 83, "tokens": 128}, "origin": null, "output": [{"finish_reason": "tool-calls", "index": 0, "message": {"content": "", "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"location\": \"San Francisco\"}", "name": "weather"}, "id": "call_0U2XcM4r5LJrsdcGoDtH2Acb", "type": "function"}, {"function": {"arguments": "{\"city\": \"San Francisco\"}", "name": "cityAttractions"}, "id": "call_EZqQYgvGX4pkrxyOMvQ2aSXF", "type": "function"}]}}], "root_span_id": "5c603a576afaa33a011b7318a95e9777c8e9992d00613b6da3840cd7c54c9920", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "5c603a576afaa33a011b7318a95e9777c8e9992d00613b6da3840cd7c54c9920", "span_parents": null, "tags": null}]