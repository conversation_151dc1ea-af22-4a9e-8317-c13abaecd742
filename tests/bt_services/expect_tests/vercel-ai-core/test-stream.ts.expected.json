[{"context": {}, "expected": null, "id": "3323d0c3a0c8f440af10b34f764f985b94bb556c3f9754312d4b56bfb19619a6", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "parallel_tool_calls": false, "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}, "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 17, "prompt_tokens": 65, "tokens": 82}, "origin": null, "output": [{"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":4,\"b\":1}", "name": "add"}, "id": "call_kAkbSAmFjCkF0m0ngxoKmYBA", "index": 0, "type": "function"}]}}], "root_span_id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "3323d0c3a0c8f440af10b34f764f985b94bb556c3f9754312d4b56bfb19619a6", "span_parents": ["b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c"], "tags": null}, {"context": {}, "expected": null, "id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "f330c29626d34b00a88a46838275d6ecf3de2f455d801ac31943fc1bc6ca2f1e", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "f330c29626d34b00a88a46838275d6ecf3de2f455d801ac31943fc1bc6ca2f1e", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "f330c29626d34b00a88a46838275d6ecf3de2f455d801ac31943fc1bc6ca2f1e", "scores": null, "span_attributes": {"name": "stream span", "type": "task"}, "span_id": "f330c29626d34b00a88a46838275d6ecf3de2f455d801ac31943fc1bc6ca2f1e", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "f330c29626d34b00a88a46838275d6ecf3de2f455d801ac31943fc1bc6ca2f1e", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["f330c29626d34b00a88a46838275d6ecf3de2f455d801ac31943fc1bc6ca2f1e"], "tags": null}, {"context": {}, "expected": null, "id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "input": {"formula": "4 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"a": 4, "b": 1}, "root_span_id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "scores": null, "span_attributes": {"name": "calculator-tools", "remote": true, "slug": "calculator-tools", "type": "function"}, "span_id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "span_parents": null, "tags": null}]