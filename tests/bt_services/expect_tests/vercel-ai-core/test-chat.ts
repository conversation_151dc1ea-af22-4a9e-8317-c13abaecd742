import { createOpenAI } from "@ai-sdk/openai";
import { generateText, streamText } from "ai";
import { initLogger, wrapAISDKModel } from "braintrust";
import { z } from "zod";

const openai = createOpenAI({
  baseURL: process.env.OPENAI_BASE_URL || "https://api.braintrust.dev/v1/proxy",
  apiKey: process.env.OPENAI_API_KEY,
  compatibility: "strict",
});

initLogger({ projectName: "vercel-ai-core" });

async function main() {
  const model = wrapAISDKModel(openai.chat("gpt-3.5-turbo"));
  await model.doGenerate({
    inputFormat: "messages",
    mode: {
      type: "regular",
    },
    prompt: [
      {
        role: "user",
        content: [{ type: "text", text: "What is the capital of France?" }],
      },
    ],
  });

  await generateText({
    model,
    tools: {
      weather: {
        description: "Get the weather in a location",
        parameters: z.object({
          location: z.string().describe("The location to get the weather for"),
        }),
        execute: async ({ location }: { location: string }) => ({
          location,
          temperature: 72 + Math.floor(Math.random() * 21) - 10,
        }),
      },
      cityAttractions: {
        parameters: z.object({ city: z.string() }),
      },
    },
    prompt:
      "What is the weather in San Francisco and what attractions should I visit?",
  });

  const streamResult = await model.doStream({
    inputFormat: "messages",
    mode: {
      type: "regular",
    },
    prompt: [
      {
        role: "user",
        content: [{ type: "text", text: "What is the capital of France?" }],
      },
    ],
  });

  const reader = streamResult.stream.getReader();
  try {
    while (true) {
      // Read from the stream
      const { done } = await reader.read();
      // Exit if we're done
      if (done) break;
      // Else yield the chunk
    }
  } finally {
    reader.releaseLock();
  }

  const streamToolResult = await streamText({
    model,
    tools: {
      weather: {
        description: "Get the weather in a location",
        parameters: z.object({
          location: z.string().describe("The location to get the weather for"),
        }),
        execute: async ({ location }: { location: string }) => ({
          location,
          temperature: 72 + Math.floor(Math.random() * 21) - 10,
        }),
      },
      cityAttractions: {
        parameters: z.object({ city: z.string() }),
      },
    },
    prompt:
      "What is the weather in San Francisco and what attractions should I visit?",
  });

  const toolReader = streamToolResult.fullStream.getReader();
  try {
    while (true) {
      // Read from the stream
      const { done } = await toolReader.read();
      // Exit if we're done
      if (done) break;
      // Else yield the chunk
    }
  } finally {
    toolReader.releaseLock();
  }
}

main();
