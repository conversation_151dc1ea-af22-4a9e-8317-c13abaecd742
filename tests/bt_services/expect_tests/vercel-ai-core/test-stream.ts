import { initLogger, invoke } from "braintrust";
import { BraintrustAdapter } from "@braintrust/vercel-ai-sdk";

const logger = initLogger({ projectName: "use-vercel-stream" });
const API_URL = "http://127.0.0.1:8000";
const decoder = new TextDecoder();

async function loggingSanity() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content:
                "You are a calculator. Return just the answer, nothing else.",
            },
            { role: "user", content: "{{formula}}" },
          ],
        },
        options: {
          model: "gpt-4o",
        },
      },
      name: "calculator",
      slug: "calculator",
    }),
  }).then((res) => res.json());

  const vercelStream = await logger.traced(
    async () => {
      const streamResult = await invoke({
        projectName: project.name,
        slug: "calculator",
        input: {
          formula: "4 + 1",
        },
        stream: true,
      });

      return BraintrustAdapter.toAIStream(streamResult);
    },
    {
      name: "stream span",
    },
  );

  for await (const chunk of vercelStream) {
    console.log(decoder.decode(chunk, { stream: true }));
  }
}

async function toolsSanity() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            { role: "system", content: "You are a calculator" },
            { role: "user", content: "{{formula}}" },
          ],
          tools: `
          [{
            "type": "function",
            "function": {
              "description": "Returns the sum of two numbers.",
              "name": "add",
              "parameters": {
                "type": "object",
                "properties": {
                  "a": {
                    "type": "number",
                    "description": "The first number"
                  },
                  "b": {
                    "type": "number",
                    "description": "The second number"
                  }
                },
                "required": ["a", "b"]
              }
            }
          }]
          `,
        },
        options: {
          model: "gpt-4o",
        },
      },
      name: "calculator-tools",
      slug: "calculator-tools",
    }),
  }).then((res) => res.json());

  const resultStreaming = await invoke({
    projectName: project.name,
    slug: "calculator-tools",
    input: {
      formula: "4 + 1",
    },
    stream: true,
  });
  const vercelStream = BraintrustAdapter.toAIStream(resultStreaming);
  for await (const chunk of vercelStream) {
    console.log(decoder.decode(chunk, { stream: true }));
  }
}

async function main() {
  await loggingSanity();
  await toolsSanity();
}

main();
