import { <PERSON><PERSON>, <PERSON> } from "braintrust";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function numberScore({ input, expected, output }: any) {
  return 0.5;
}

Eval("basic-fail", {
  data: () => [{ input: "a", expected: "b" }],
  task: (input) => {
    throw new Error("This is an error");
  },
  scores: [numberScore],
});

Reporter("allow anything", {
  reportEval() {
    console.log("I don't care");
  },
  reportRun() {
    return true;
  },
});
