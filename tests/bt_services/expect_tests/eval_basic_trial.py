from threading import Lock

from braintrust import Eva<PERSON>

from autoevals import Levenshtein

TRIALS = 4

INPUTS = list(range(5))
OUTPUTS = [[j for j in range(TRIALS)] for _ in range(len(INPUTS))]
EXPECTED = list(range(len(INPUTS)))
lock = Lock()


def get_output(input):
    with lock:
        return str(OUTPUTS[input].pop())


def empty_outputs(output, expected):
    # This is a hack, but it's how we can ensure that the outputs are empty
    # after the trials run
    for outputs in OUTPUTS:
        assert len(outputs) == 0

    return 1


Eval(
    name="basic_trial",
    data=[{"input": input, "expected": str(expected)} for input, expected in zip(INPUTS, EXPECTED)],
    task=get_output,
    scores=[Levenshtein, empty_outputs],
    trial_count=TRIALS,
)
