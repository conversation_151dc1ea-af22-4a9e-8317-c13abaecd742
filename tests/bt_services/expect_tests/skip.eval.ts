import { Eval } from "braintrust";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function nullScore({ input, expected, output }: any) {
  return null;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function numberScore({ input, expected, output }: any) {
  return 0.5;
}

Eval("basic-error", {
  data: () => [{ input: "a", expected: "b" }],
  task: (input) => input,
  scores: [nullScore, numberScore],
});
