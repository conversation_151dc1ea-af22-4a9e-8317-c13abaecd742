import { Eval, initLogger } from "braintrust";

async function main() {
  const logger = initLogger({ projectName: "P1" });
  const projectId = (await logger.project).id;

  let evalResult = await <PERSON>l("P2", {
    data: [],
    task: () => "1",
    scores: [],
  });

  if (evalResult.summary.projectName !== "P2") {
    throw new Error("Project ID mismatch");
  }

  evalResult = await <PERSON>l("another project", {
    data: [],
    task: () => "1",
    scores: [],
    projectId,
  });

  if (evalResult.summary.projectName !== "P1") {
    throw new Error("Project ID mismatch (projectId specified)");
  }
}

main();
