import { Eval } from "braintrust";
import { <PERSON><PERSON>hteinScorer } from "autoevals";

const TRIALS = 4;

const INPUTS = [...Array(5).keys()];
const OUTPUTS = INPUTS.map(() => [...Array(TRIALS).keys()]);
const EXPECTED = [...Array(INPUTS.length).keys()];

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function getOutput(input: any) {
  return `${OUTPUTS[input].pop()}`;
}

function emptyOutputs({}: {
  input: number;
  output: string;
  expected?: string;
}) {
  for (const outputs of OUTPUTS) {
    if (outputs.length > 0) {
      throw new Error("Outputs were not empty");
    }
  }

  return { name: "empty_outputs", score: 1 };
}

Eval("basic_trial", {
  data: () => INPUTS.map((input, i) => ({ input, expected: `${EXPECTED[i]}` })),
  task: getOutput,
  scores: [LevenshteinScorer, emptyOutputs],
  trialCount: TRIALS,
});
