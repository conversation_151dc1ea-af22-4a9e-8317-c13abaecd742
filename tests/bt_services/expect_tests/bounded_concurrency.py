import asyncio

from braintrust import EvalAs<PERSON>

from autoevals import Levenshtein

MAX_TASKS = 5
active_tasks = 0


async def task(input):
    global active_tasks
    try:
        active_tasks += 1
        if active_tasks > MAX_TASKS:
            raise Exception("Too many tasks")
        await asyncio.sleep(0.1)
    finally:
        active_tasks -= 1
    return "foo"


async def main():
    # Test without concurrency bound
    summary = await <PERSON>lAs<PERSON>(
        "bounded concurrency",
        task=task,
        data=[{"input": "foo", "expected": "foo"} for _ in range(50)],
        scores=[Lev<PERSON>htein],
    )
    errors = sum(1 for result in summary.results if result.error)
    assert errors > 0, "Expected some errors without concurrency bound"

    # Test with concurrency bound
    summary = await EvalAsync(
        "bounded concurrency",
        task=task,
        data=[{"input": "foo", "expected": "foo"} for _ in range(50)],
        scores=[Lev<PERSON>htein],
        max_concurrency=MAX_TASKS,
    )
    errors = sum(1 for result in summary.results if result.error)
    assert errors == 0, "Expected no errors with concurrency bound"

    # Test with zero records
    summary = await <PERSON><PERSON>As<PERSON>(
        "bounded concurrency",
        task=task,
        data=[],
        scores=[<PERSON><PERSON><PERSON><PERSON>],
        max_concurrency=MAX_TASKS,
    )
    errors = sum(1 for result in summary.results if result.error)
    assert errors == 0, "Expected no errors with concurrency bound"


if __name__ == "__main__":
    asyncio.run(main())
