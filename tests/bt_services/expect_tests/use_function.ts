import { initLogger, invoke } from "braintrust";
import { z } from "zod";

const logger = initLogger({ projectName: "use-function" });
const API_URL = "http://127.0.0.1:8000";

async function loggingSanity() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content:
                "You are a calculator. Return just the answer, nothing else.",
            },
            { role: "user", content: "{{formula}}" },
          ],
        },
        options: {
          model: "gpt-4o",
        },
      },
      name: "calculator",
      slug: "calculator",
    }),
  }).then((res) => res.json());

  // This should be logged
  const result = await invoke({
    projectName: "use-function",
    slug: "calculator",
    input: {
      formula: "1 + 1",
    },
  });
  if (result !== "2") {
    throw new Error(`Expected 2, got ${result}`);
  }

  // So should this
  await invoke({
    projectName: "use-function",
    slug: "calculator",
    input: {
      formula: "2 + 1",
    },
    parent: logger,
  });

  // This should be logged in a subspan
  await logger.traced(
    async () => {
      await invoke({
        projectName: "use-function",
        slug: "calculator",
        input: {
          formula: "3 + 1",
        },
      });
    },
    {
      name: "parent span",
    },
  );

  // Now try adding an extra message
  await logger.traced(
    async (span) => {
      await invoke({
        projectName: "use-function",
        slug: "calculator",
        input: {
          formula: "4 + 1",
        },
        messages: [
          {
            role: "assistant",
            content: "8",
          },
          {
            role: "user",
            content: "Are you sure?",
          },
        ],
      });
    },
    {
      name: "extra message span",
    },
  );

  // Now try streaming the result. The stream's final output should
  // be logged as a span
  await logger.traced(
    async (span) => {
      const streamResult = await invoke({
        projectName: "use-function",
        slug: "calculator",
        input: {
          formula: "4 + 1",
        },
        stream: true,
      });
      span.log({ input: "A span!", output: streamResult });
      const finalResult = z.string().parse(await streamResult.finalValue());

      if (finalResult !== "5") {
        throw new Error(`Expected 5, got ${finalResult}`);
      }

      if (z.string().parse(await streamResult.finalValue()) !== "5") {
        throw new Error(`(2) Expected 5, got ${finalResult}`);
      }
    },
    {
      name: "stream span",
    },
  );

  // Try iterating over the stream
  await logger.traced(
    async (span) => {
      const streamResult = await invoke({
        projectName: "use-function",
        slug: "calculator",
        input: {
          formula: "4 + 1",
        },
        stream: true,
      });
      span.log({ input: "A span!", output: streamResult });
      let finalResult: string = "";
      for await (const chunk of streamResult) {
        finalResult += chunk.data;
      }

      if (finalResult !== "5") {
        throw new Error(`Expected 5, got ${finalResult}`);
      }
    },
    {
      name: "stream span",
    },
  );
}

async function toolsSanity() {
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            { role: "system", content: "You are a calculator" },
            { role: "user", content: "{{formula}}" },
          ],
          tools: `
          [{
            "type": "function",
            "function": {
              "description": "Returns the sum of two numbers.",
              "name": "add",
              "parameters": {
                "type": "object",
                "properties": {
                  "a": {
                    "type": "number",
                    "description": "The first number"
                  },
                  "b": {
                    "type": "number",
                    "description": "The second number"
                  }
                },
                "required": ["a", "b"]
              }
            }
          }]
          `,
        },
        options: {
          model: "gpt-4o",
        },
      },
      name: "calculator-tools",
      slug: "calculator-tools",
    }),
  }).then((res) => res.json());

  const schema = z.strictObject({ a: z.number(), b: z.number() });
  const result = await invoke({
    projectName: "use-function",
    slug: "calculator-tools",
    input: {
      formula: "2 + 1",
    },
    schema,
  });
  if (result.a !== 2 || result.b !== 1) {
    throw new Error(`Expected { a: 2, b: 1 }, got ${JSON.stringify(result)}`);
  }

  const resultStreaming = await invoke({
    projectName: "use-function",
    slug: "calculator-tools",
    input: {
      formula: "4 + 1",
    },
    stream: true,
  });
  const finalResult = schema.parse(await resultStreaming.finalValue());
  if (finalResult.a !== 4 || finalResult.b !== 1) {
    throw new Error(
      `Expected { a: 4, b: 1 }, got ${JSON.stringify(finalResult)}`,
    );
  }
}

async function main() {
  await loggingSanity();
  await toolsSanity();
}

main();
