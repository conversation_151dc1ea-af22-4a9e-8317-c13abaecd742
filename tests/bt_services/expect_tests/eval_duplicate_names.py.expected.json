[{"context": {}, "expected": "b", "id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "span_parents": null, "tags": null}, {"context": {}, "expected": "b", "id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "span_parents": null, "tags": null}, {"context": {}, "expected": "b", "id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "span_parents": null, "tags": null}, {"context": {}, "expected": "b", "id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b59dd81345441ff354dce0b89331494512995a72343a398d7c7739ca904c5a06", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.333333}, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": {"Levenshtein": 0.333333}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.333333}, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": {"Levenshtein": 0.333333}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.333333}, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": {"Levenshtein": 0.333333}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.333333}, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": {"Levenshtein": 0.333333}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}]