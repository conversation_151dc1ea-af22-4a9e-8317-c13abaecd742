import { OpenAI } from "openai";
import { initDataset, initLogger, loadPrompt, wrapOpenAI } from "braintrust";

const projectName = "multimodal_ts";
const logger = initLogger({ projectName });
const API_URL = "http://127.0.0.1:8000";

async function main() {
  const project = await logger.project;

  const userMessage = {
    role: "user",
    content: [
      {
        type: "text",
        text: "Tell a brief story...\n\nabout the provided image(s).",
      },
      {
        type: "image_url",
        image_url: {
          url: "{{ imageA }}",
        },
      },
      {
        type: "image_url",
        image_url: {
          url: "{{ imageB }}",
        },
      },
      {
        type: "image_url",
        image_url: {
          url: "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
        },
      },
    ],
  };
  const prompts = [
    {
      project_id: project.id,
      name: "pirate",
      slug: "pirate",
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            { role: "system", content: "Speak like a pirate. Arrrr." },
            userMessage,
          ],
        },
        options: {
          model: "gpt-4-vision-preview",
        },
      },
    },
    {
      project_id: project.id,
      name: "french",
      slug: "french",
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            { role: "system", content: "Speak in French." },
            userMessage,
          ],
        },
        options: {
          model: "gpt-4-vision-preview",
        },
      },
    },
  ];

  for (const prompt of prompts) {
    const prompt_data = await fetch(`${API_URL}/v1/prompt`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(prompt),
    }).then((res) => res.json());
    console.log(prompt_data);
  }

  const dataset = initDataset(project.name, {
    dataset: "multimodal_ts",
    useOutput: false,
  });
  dataset.insert({
    input: {
      imageA: pokemonA,
      imageB: pokemonB,
    },
  });
  dataset.insert({
    input: {
      imageA: dogA,
      imageB: dogB,
    },
  });
  await dataset.flush();

  const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));

  for (const slug of prompts.map((p) => p.slug)) {
    const prompt = await loadPrompt({ projectName, slug });
    for await (const data of dataset) {
      const builtPrompt = await prompt.buildWithAttachments(data.input);
      const completion = await client.chat.completions.create(builtPrompt);
      console.log(`'${slug}' completion: `, completion);
      console.log(`...message: `, completion.choices[0].message);
    }
  }
}

main();

const pokemonA =
  "data:image/jpeg;base64,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";
const pokemonB =
  "data:image/png;base64,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";
const dogA =
  "data:image/jpeg;base64,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";
const dogB =
  "data:image/jpeg;base64,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";
