[{"context": {}, "expected": null, "id": "4f92d57bb74128b0249a5d039b6cbd9beb49e12a082570c805184c6feafa89b6", "input": [{"content": "Speak in French.", "role": "system"}, {"content": [{"text": "Tell a brief story...\n\nabout the provided image(s).", "type": "text"}, {"image_url": {"url": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}, "type": "image_url"}], "role": "user"}], "is_root": true, "metadata": {"model": "gpt-4-vision-preview", "prompt": {"variables": {"imageA": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}, "imageB": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}}, "provider": "openai"}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 351, "prompt_tokens": 791, "tokens": 1142}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Dans une petite ville pittoresque, vivait un chien joyeux et enjoué nommé <PERSON>. Léo était un golden retriever à la fourrure dorée et au sourire contagieux. Tous les jours, il courait au parc, sautant après les feuilles qui voltigeaient et saluant tous les passants d'un aboiement amical.\n\nUn jour, en explorant le marché local, Léo rencontra un petit chiot nommé Max. Max était à côté d'un bol rempli de myrtilles et semblait confus, essayant de comprendre si ces petites baies rondes étaient des jouets ou de la nourriture. Léo s'approcha, flaira le bol et décida de partager la joie de la découverte avec Max, en lui montrant comment les myrtilles pouvaient être à la fois un délice et un amusement.\n\nAprès s'être régalés, ils reprirent leur promenade et tombèrent nez à nez avec un dessin animé d'une peau de banane souriante placardé sur le mur d'une épicerie. Les deux amis se mirent à imaginer que c'était un super-héros de leur propre création, \"Banane Smile\", qui glissait sur les méchants pour défendre les chiens et chiots de la ville.\n\nEt c'est ainsi que Léo et Max devinrent les meilleurs amis, partageant des aventures quotidiennes et créant des histoires fabuleuses autour des choses les plus simples de la vie.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "4f92d57bb74128b0249a5d039b6cbd9beb49e12a082570c805184c6feafa89b6", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "4f92d57bb74128b0249a5d039b6cbd9beb49e12a082570c805184c6feafa89b6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "a354e23a136dd29cde4e5252c7bbe62a5b95f1efae2c6c3ed7c82a10f2e3549d", "input": [{"content": "Speak in French.", "role": "system"}, {"content": [{"text": "Tell a brief story...\n\nabout the provided image(s).", "type": "text"}, {"image_url": {"url": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": {"content_type": "image/png", "filename": "file.png", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}, "type": "image_url"}], "role": "user"}], "is_root": true, "metadata": {"model": "gpt-4-vision-preview", "prompt": {"variables": {"imageA": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}, "imageB": {"content_type": "image/png", "filename": "file.png", "type": "braintrust_attachment"}}}, "provider": "openai"}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 486, "prompt_tokens": 791, "tokens": 1277}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Dans un monde coloré et enchanteur, vivait un duo de créatures extraordinaires. D'un côté, un petit être verdoyant avec une grande feuille sur le dos, aux yeux grands et expressifs, connu pour sa sérénité et son affinité avec la nature. De l'autre, un compagnon enflammé, rayonnant d'énergie et de chaleur, avec une flamme vacillante au bout de sa queue, symbole de sa vitalité et de sa force.\n\nUn jour, alors qu'ils exploraient les vastes étendues de leur monde magique, ils rencontrèrent un drôle d'habitant jaune et souriant. C'était une banane joyeuse, qui avait le don étrange de faire rire quiconque croisait son regard facétieux. La banane les a informés d'un défi qui obsédait tous les habitants du royaume : la grande course de la Clairière Enchantée.\n\nAnimés par le désir d’aventure et stimulés par l’espièglerie de leur nouvel ami, le duo décida de participer à la course. Ils savaient que l'un était rapide et agile tandis que l'autre, plein d'énergie et de puissance, pouvait surmonter n’importe quel obstacle.\n\nLa course fut un événement festif, plein de rires et de camaraderie. Les deux compagnons, avec l'encouragement de la banane, ont travaillé ensemble, utilisant leurs talents uniques pour naviguer à travers les défis. Leur synergie était si parfaite qu'ils atteignirent la ligne d'arrivée côte à côte, sous les acclamations de la foule.\n\nCe jour-là, ils n’ont pas seulement gagné une course; ils avaient tissé un lien d'amitié plus fort que jamais et avaient appris la valeur du travail d'équipe et de la joie. La banane, par sa seule présence, leur avait enseigné qu'avec un sourire et un esprit positif, chaque jour pouvait être rempli de bonheur et de merveilles.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "a354e23a136dd29cde4e5252c7bbe62a5b95f1efae2c6c3ed7c82a10f2e3549d", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "a354e23a136dd29cde4e5252c7bbe62a5b95f1efae2c6c3ed7c82a10f2e3549d", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "78e696247e7ae0e766b15d9df965f270bdfdd484a98ac1509ddaa2377403467b", "input": [{"content": "Speak like a pirate. Arrrr.", "role": "system"}, {"content": [{"text": "Tell a brief story...\n\nabout the provided image(s).", "type": "text"}, {"image_url": {"url": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}, "type": "image_url"}], "role": "user"}], "is_root": true, "metadata": {"model": "gpt-4-vision-preview", "prompt": {"variables": {"imageA": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}, "imageB": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}}, "provider": "openai"}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 589, "prompt_tokens": 795, "tokens": 1384}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Arrr, gather 'round me hearties and pay heed to the tale o' two sea dogs and a mysterious yellow fruit with a smile.\n\nIn the first image, we behold a cheery golden-furred buccaneer, a most loyal matey named Captain <PERSON>. His jolly demeanor be known far 'n wide across the seven seas, and his smile could lighten the darkest o' deck. Captain <PERSON> spent his days searchin' for adventure and a game of fetch with any kind-hearted soul who'd throw a driftwood stick.\n\nThe second image reveals a young blue-coated scallywag, by the name of First <PERSON>, who's got a curious gaze fixed upon a bowl o' mysterious black pearls. These shiny objects had 'im puzzled, 'twas his first encounter with such treasures. With a tentative lick, First <PERSON><PERSON> wonders if he's stumbled upon the fabled food of the merfolk or just a mere salty snack for a wee pup.\n\nAnd then, there's the third chart, an image o' a banana peel with a delightful twist—a grinnin' face 'tis drawn upon it! 'Tis no ordinary peel, but a magical charm from the Isles of Perpetual Peelies. This whimsical fruit skin be known to slip up unsuspecting landlubbers and scurvy pirates alike, always with a smirk 'n a smile.\n\nAye, so the legend goes, Captain <PERSON> 'n First <PERSON> once embarked on a grand quest to uncover the secret o' the smilin' banana. They roamed high 'n low, from the crow's nest to the briny deep, yet by a twist o' fate, it be the banana that found them. One morn, as the sun climbed o'er the horizon, our furry heroes woke to find the peelin' layin' upon their deck, smilin' as if it 'ad heard the finest shanty ever sung.\n\nThey knew not whence it came, nor why it chose them, but 'twas clear their lives were now entwined with this peculiar charm. From that day on, the pair o' dogs be known to be the luckiest pups to sail the seas, all thanks to their fruity, smilin' comrade—Banana Peel Billy, the happiest of charms. And on calm nights, when the waves be still and the stars be bright, one could almost hear a soft, peel-like chuckle ridin' the sea breeze, hintin' at the magic that binds this trio together in their seafarin' tales.\n\nSo, weigh anchor 'n hoist the mizzen! There be more adventures to come with Captain Barkbeard, First Mate Growlbeard, and their jolly sidekick, Banana Peel Billy!", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "78e696247e7ae0e766b15d9df965f270bdfdd484a98ac1509ddaa2377403467b", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "78e696247e7ae0e766b15d9df965f270bdfdd484a98ac1509ddaa2377403467b", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "60ddeb1f3a69cd3a4c58003f489ebbcfcaa167af25a1c907c6b4bd48150821d6", "input": [{"content": "Speak like a pirate. Arrrr.", "role": "system"}, {"content": [{"text": "Tell a brief story...\n\nabout the provided image(s).", "type": "text"}, {"image_url": {"url": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": {"content_type": "image/png", "filename": "file.png", "type": "braintrust_attachment"}}, "type": "image_url"}, {"image_url": {"url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}, "type": "image_url"}], "role": "user"}], "is_root": true, "metadata": {"model": "gpt-4-vision-preview", "prompt": {"variables": {"imageA": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}, "imageB": {"content_type": "image/png", "filename": "file.png", "type": "braintrust_attachment"}}}, "provider": "openai"}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 370, "prompt_tokens": 795, "tokens": 1165}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Arrr, let me regale ye with a seafarer's yarn 'bout these colorful creatures and their fruity matey!\n\nIn a land far, far away, a green-shelled creature named <PERSON><PERSON><PERSON><PERSON><PERSON>, and his fiery comrade, <PERSON><PERSON><PERSON>, set sail across the seven seas searching for the legendary Golden Banana, a mystical fruit with the power to grant any wish to the one who finds it. Ye see, this Banana was no ordinary piece of loot; it had a jolly little face and a knack for slippin' away from even the wildest of buccaneers.\n\nOur brave <PERSON><PERSON><PERSON><PERSON><PERSON>, bein' a sturdy and steadfast voyager, led the way through briny depths and monstrous storms with a heart full of courage. <PERSON><PERSON><PERSON>, with a tail alight with flame, kept their spirits high with tales of adventure 'n' treasure.\n\nOne starry night, as they be charting their course, a glint in the water caught <PERSON><PERSON><PERSON>'s eye. \"Shiver me timbers,\" he bellowed, \"It be the twinkle of the Golden Banana!\" Without a moment to spare, they dropped anchor and plunged into the depths of <PERSON>' locker, only to discover 'twas but a mere reflection of their lantern light.\n\nAlas, the true Golden Banana remained elusive, always seeming to slip just out of reach, as if it were ridin' the waves itself. But <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> be not easily discouraged. With a vow to roam the seas until the end of their days, they continue their quest, certain that the Golden Banana awaits them, ready to be snatched by the most daring of pirates. Arrrr, what a tale it be, full of hope, fire, and the magic of the chase!", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "60ddeb1f3a69cd3a4c58003f489ebbcfcaa167af25a1c907c6b4bd48150821d6", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "60ddeb1f3a69cd3a4c58003f489ebbcfcaa167af25a1c907c6b4bd48150821d6", "span_parents": null, "tags": null}, {"expected": null, "id": "51d9e68a979760b3a9c94d357420ae143bf2bdc73da48991574d779c1f464066", "input": {"imageA": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}, "imageB": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}}, "is_root": true, "metadata": null, "origin": null, "root_span_id": "51d9e68a979760b3a9c94d357420ae143bf2bdc73da48991574d779c1f464066", "span_id": "51d9e68a979760b3a9c94d357420ae143bf2bdc73da48991574d779c1f464066", "tags": null}, {"expected": null, "id": "6505abf9d38b14902e1265b9b7b537f806dea47eb3bdabdc4e8a22ca4e82c6d1", "input": {"imageA": {"content_type": "image/jpeg", "filename": "file.jpeg", "type": "braintrust_attachment"}, "imageB": {"content_type": "image/png", "filename": "file.png", "type": "braintrust_attachment"}}, "is_root": true, "metadata": null, "origin": null, "root_span_id": "6505abf9d38b14902e1265b9b7b537f806dea47eb3bdabdc4e8a22ca4e82c6d1", "span_id": "6505abf9d38b14902e1265b9b7b537f806dea47eb3bdabdc4e8a22ca4e82c6d1", "tags": null}]