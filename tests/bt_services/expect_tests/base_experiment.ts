import { BaseEx<PERSON><PERSON>, <PERSON>l, Evaluator } from "braintrust";

const projectName = "Base experiment test";

async function main() {
  const baseEvaluator: Evaluator<string, string, string | undefined> = {
    data: [{ input: "a" }],
    task: (input) => `foo = ${input}`,
    scores: [],
  };

  const { summary: base } = await <PERSON>l(projectName, baseEvaluator);
  const { experimentName: baseExperimentName, experimentId: baseExperimentId } =
    base ?? {};
  if (!baseExperimentName || !baseExperimentId) {
    throw new Error("Project name or base experiment name/id not found");
  }

  // Should respect the specified base experiment name.
  const { summary: usingBaseName } = await <PERSON><PERSON>(projectName, {
    ...baseEvaluator,
    baseExperimentName,
  });
  if (usingBaseName.comparisonExperimentName !== baseExperimentName) {
    throw new Error("Base experiment was not set to the named base");
  }

  // If baseExperimentId is specified, should use that instead of baseExperimentName.
  const { summary: usingBaseId } = await <PERSON><PERSON>(projectName, {
    ...baseEvaluator,
    baseExperimentName: usingBaseName.experimentName,
    baseExperimentId,
  });
  if (usingBaseId.comparisonExperimentName !== baseExperimentName) {
    throw new Error("Base experiment was not set to the base id");
  }

  // If baseExperimentId is specified, should use that instead of baseExperimentName.
  const { summary: usingBaseNameWithData } = await Eval<
    string,
    string,
    string | undefined
  >(projectName, {
    ...baseEvaluator,
    data: BaseExperiment({ name: usingBaseId.experimentName }),
    baseExperimentName,
  });
  if (usingBaseNameWithData.comparisonExperimentName !== baseExperimentName) {
    throw new Error("Base experiment was not set to the named base");
  }

  // An experiment used as data should not be used as the base experiment if an
  // explicit base is specified.
  const { summary: usingBaseIdWithData } = await Eval<
    string,
    string,
    string | undefined
  >(projectName, {
    ...baseEvaluator,
    data: BaseExperiment({ name: usingBaseId.experimentName }),
    baseExperimentId,
  });
  if (usingBaseIdWithData.comparisonExperimentName !== baseExperimentName) {
    throw new Error("Base experiment was not set to the base id");
  }
}

main();
