import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { BaseExperiment, Eval, init } from "braintrust";

async function main() {
  const firstEval = await <PERSON>l<number, string, string | undefined>(
    "Hill climbing",
    {
      data: [{ input: 1 }, { input: 2 }, { input: 3 }],
      task: (input) => `${input + 1}`,
      scores: [],
    },
  );

  const { summary } = firstEval;

  const { projectName, experimentName } = summary || {};
  if (!projectName || !experimentName) {
    throw new Error("Project name or experiment name not found");
  }

  await <PERSON>l("Hill climbing", {
    data: init(projectName, {
      experiment: experimentName,
      open: true,
    }).asDataset<number, string>(),
    task: (input) => `${input + 1}`,
    scores: [Levenshtein],
  });

  await <PERSON>l<number, string, string>("Hill climbing", {
    data: BaseExperiment,
    task: (input) => `${input + 1}`,
    scores: [Levenshtein],
  });

  await <PERSON>l("Hill climbing", {
    data: BaseExperiment<number, string>(),
    task: (input) => `${input + 1}`,
    scores: [<PERSON><PERSON>htein],
  });

  await <PERSON><PERSON>("Hill climbing", {
    data: [{ input: 5 }, { input: 6 }, { input: 7 }],
    task: (input) => `${input + 1}`,
    scores: [Levenshtein],
  });

  // Should use the first experiment as data, not the one above.
  const lastEval = await Eval<number, string, string>("Hill climbing", {
    data: BaseExperiment({ name: experimentName }),
    task: (input) => `${input + 1}`,
    scores: [Levenshtein],
  });

  if (lastEval.summary.comparisonExperimentName !== experimentName) {
    throw new Error(
      "Hill climbing base was not set to the BaseExperiment used as data",
    );
  }
}

main();
