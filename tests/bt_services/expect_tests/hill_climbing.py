import asyncio

from braintrust import BaseExperiment, EvalAsync, init

from autoevals import Levenshtein


async def main():
    first_eval = await <PERSON>lAsync(
        "Hill climbing",
        data=[{"input": 1, "expected": 2}, {"input": 2, "expected": 3}, {"input": 3, "expected": 4}],
        task=lambda input: str(input + 1),
        scores=[<PERSON><PERSON>htein],
    )
    project_name, experiment_name = first_eval.summary.project_name, first_eval.summary.experiment_name

    await <PERSON>lAsync(
        "Hill climbing",
        data=init(project=project_name, experiment=experiment_name, open=True).as_dataset(),
        task=lambda input: str(input + 1),
        scores=[Lev<PERSON>htein],
    )

    await <PERSON>lAsync(
        "Hill climbing",
        data=BaseExperiment,
        task=lambda input: str(input + 1),
        scores=[Lev<PERSON>htein],
    )

    await <PERSON>lAsync(
        "Hill climbing",
        data=BaseExperiment(),
        task=lambda input: str(input + 1),
        scores=[<PERSON><PERSON>htein],
    )

    await <PERSON><PERSON><PERSON><PERSON>(
        "Hill climbing",
        data=[{"input": 5, "expected": 6}, {"input": 6, "expected": 7}, {"input": 7, "expected": 8}],
        task=lambda input: str(input + 1),
        scores=[Levenshtein],
    )

    # Should use the first experiment as data, not the one above.
    last_eval = await EvalAsync(
        "Hill climbing",
        data=BaseExperiment(name=experiment_name),
        task=lambda input: str(input + 1),
        scores=[Levenshtein],
    )

    if last_eval.summary.comparison_experiment_name != experiment_name:
        raise Exception("Hill climbing base was not set to the BaseExperiment used as data")


if __name__ == "__main__":
    asyncio.run(main())
