import { loadPrompt } from "braintrust";

const API_URL = "http://127.0.0.1:8000";
async function main() {
  const project = await fetch(`${API_URL}/v1/project`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name: "quick prompt",
    }),
  }).then((res) => res.json());

  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content: "You are a calculator. Return results in JSON",
            },
            { role: "user", content: "{{formula}}" },
          ],
        },
        options: {
          params: {
            response_format: { type: "json_object" },
          },
        },
      },
      name: "calculator",
      slug: "calculator",
    }),
  }).then((res) => res.json());

  const prompt = await loadPrompt({
    projectName: project.name,
    slug: "calculator",
  });
  console.log(prompt);
}

main();
