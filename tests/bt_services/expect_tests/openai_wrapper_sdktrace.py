import asyncio
import os

import braintrust
import openai
from braintrust.oai import wrap_openai


def make_query(prompt, **kwargs):
    out = dict(
        model="gpt-3.5-turbo",
        messages=[
            dict(role="system", content="You are a helpful assistant"),
            dict(role="user", content=prompt),
        ],
        span_info=dict(
            name="chat completion query",
            span_attributes=dict(extra="hello"),
        ),
    )
    out.update(**kwargs)
    return out


def sync_query(experiment):
    client = wrap_openai(openai.OpenAI())

    with experiment.start_span("sync_query0") as root_span:
        PROMPT = "Give me a list of ten children's toys"
        resp = client.chat.completions.create(**make_query(PROMPT))
        root_span.log(input=PROMPT, output=resp.choices[0].model_dump())

    with experiment.start_span("sync_query1") as root_span:
        PROMPT = "Give a random number between 1 and 2000"
        resp_gen = client.chat.completions.create(**make_query(PROMPT, stream=True))
        num_responses = len(list(resp_gen))
        root_span.log(input=PROMPT, output=dict(num_responses=num_responses))

    client.chat.completions.create(**make_query("Give me all your money"))


async def async_query(experiment):
    client = wrap_openai(openai.AsyncOpenAI())

    with experiment.start_span("async_query0") as root_span:
        PROMPT = "Give me a list of twelve baby names"
        resp = await client.chat.completions.create(**make_query(PROMPT))
        root_span.log(input=PROMPT, output=resp.choices[0].model_dump())

    with experiment.start_span("async_query1") as root_span:
        PROMPT = "Give a random number between 1 and 2000"
        resp_gen = await client.chat.completions.create(**make_query(PROMPT, stream=True))
        num_responses = 0
        async for _ in resp_gen:
            num_responses += 1
        root_span.log(input=PROMPT, output=dict(num_responses=num_responses))

    with experiment.start_span("function_call_nonstreaming") as root_span:
        PROMPT = "Give a random number between 1 and 2000"
        resp_gen = await client.chat.completions.create(
            **{
                **make_query(PROMPT),
                "tools": [
                    {
                        "type": "function",
                        "function": {
                            "description": "Returns the sum of two numbers.",
                            "name": "add",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "a": {"type": "number", "description": "The first number"},
                                    "b": {"type": "number", "description": "The second number"},
                                },
                                "required": ["a", "b"],
                            },
                        },
                    }
                ],
            }
        )
        root_span.log(input=PROMPT, output=resp.choices[0].model_dump())

    with experiment.start_span("function_call_streaming") as root_span:
        PROMPT = "Give a random number between 1 and 2000"
        resp_gen = await client.chat.completions.create(
            **{
                **make_query(PROMPT, stream=True),
                "tools": [
                    {
                        "type": "function",
                        "function": {
                            "description": "Returns the sum of two numbers.",
                            "name": "add",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "a": {"type": "number", "description": "The first number"},
                                    "b": {"type": "number", "description": "The second number"},
                                },
                                "required": ["a", "b"],
                            },
                        },
                    }
                ],
            }
        )
        num_responses = 0
        async for _ in resp_gen:
            num_responses += 1
        root_span.log(input=PROMPT, output=dict(num_responses=num_responses))

    await client.chat.completions.create(**make_query("Give me all your money"))


if __name__ == "__main__":
    experiment = braintrust.init("openai_wrapper_nonproxy")
    sync_query(experiment)
    asyncio.run(async_query(experiment))
    print(experiment.summarize())
