from braintrust import <PERSON><PERSON>

from autoevals import LevenshteinScorer, Score

TRIALS = 4

INPUTS = list(range(5))
OUTPUTS = [[i for i in range(TRIALS)] for _ in range(5)]
EXPECTED = list(range(5))


def get_output(input):
    return f"{OUTPUTS[input].pop()}"


def empty_outputs(input, output, expected, metadata):
    for outputs in OUTPUTS:
        if outputs:
            raise Exception("Outputs were not empty")

    return Score(
        name="empty_outputs",
        score=metadata["score"],
    )


Eval(
    "basic_trial",
    data=lambda: [
        {"input": input, "expected": f"{EXPECTED[i]}", "metadata": {"score": 0.5}} for i, input in enumerate(INPUTS)
    ],
    task=get_output,
    scores=[LevenshteinScorer, empty_outputs],
    trial_count=TRIALS,
)
