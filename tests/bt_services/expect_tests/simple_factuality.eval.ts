import { Factuality } from "autoevals";
import { Eval, wrapOpenAI } from "braintrust";
import OpenAI from "openai";

const client = wrapOpenAI(new OpenAI({}));

Eval("simple-factuality", {
  data: [
    {
      input: "Which city is the capital of France?",
      expected: "Paris",
    },
  ],
  task: async (input) => {
    const response = await client.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: input,
        },
      ],
    });
    return response.choices[0].message.content ?? "";
  },
  scores: [Factuality],
});
