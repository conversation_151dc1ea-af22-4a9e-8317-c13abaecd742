import { Eval, initDataset } from "braintrust";
import { NumericDiff } from "autoevals";

async function main() {
  // The point is this runs outside of the Eval, so that initDataset below works.
  await (async () => {
    const ds = initDataset("dataset_in_eval", { dataset: "data" });
    ds.insert({ id: "a", input: { a: 1, b: 2 }, expected: 3 });
    ds.insert({ id: "b", input: { a: 2, b: 2 }, expected: 4 });
    await ds.flush();
  })();

  Eval("dataset_in_eval", {
    data: initDataset("dataset_in_eval", { dataset: "data" }),
    task: (input) => input.a + input.b,
    scores: [NumericDiff],
  });
}

main();
