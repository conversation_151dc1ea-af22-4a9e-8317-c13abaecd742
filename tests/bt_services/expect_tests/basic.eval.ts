import { <PERSON><PERSON>, <PERSON>, currentSpan, traced } from "braintrust";
import { LevenshteinScorer } from "autoevals";

Eval("basic", {
  data: async () => {
    return [
      {
        input: "a",
        expected: "b",
      },
    ];
  },
  task: (_, { span }) => {
    span.log({ metadata: { message: "In task" } });
    return traced(
      () => {
        currentSpan().log({ metadata: { message: "In foo" } });
        return "bar";
      },
      { name: "foo" },
    );
  },
  scores: [LevenshteinScorer],
  experimentName: "My basic eval",
  metadata: {
    foo: "bar",
  },
});

Eval("basic", {
  data: async () => {
    return [
      {
        input: "c",
        expected: "d",
      },
    ];
  },
  task: (_, { span }) => {
    span.log({ metadata: { message: "In task" } });
    return traced(
      () => {
        currentSpan().log({ metadata: { message: "In foo" } });
        return "bar";
      },
      { name: "foo" },
    );
  },
  scores: [LevenshteinScorer],
  experimentName: "My other basic eval",
  metadata: {
    foo: "baz",
  },
});

Eval("env var", {
  data: async () => {
    return [
      {
        input: "e",
        expected: "f",
      },
    ];
  },
  task: () => {
    if (!process.env.MY_ENV_VAR) {
      throw new Error("MY_ENV_VAR not set");
    } else {
      return "f";
    }
  },
  scores: [LevenshteinScorer],
});

Reporter("verify scores set", {
  reportEval(_, { results }) {
    if (
      results[0].scores.Levenshtein === undefined ||
      results[0].scores.Levenshtein === null
    ) {
      throw new Error("Levenshtein score not set");
    }
  },
  reportRun() {
    return true;
  },
});
