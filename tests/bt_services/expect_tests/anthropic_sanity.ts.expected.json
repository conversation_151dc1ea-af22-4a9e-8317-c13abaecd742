[{"context": {}, "expected": null, "id": "2faa6f6c25adb862c1ec187b2d7c910958cfd779039b455b29e7ac48138e44ca", "input": [{"content": "What is 1+1", "role": "user"}], "is_root": false, "metadata": {"model": "claude-3-haiku-20240307", "provider": "openai"}, "metrics": {"completion_tokens": 14, "prompt_tokens": 13, "tokens": 27}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "1 + 1 = 2.", "role": "assistant"}}], "root_span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "2faa6f6c25adb862c1ec187b2d7c910958cfd779039b455b29e7ac48138e44ca", "span_parents": ["020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a"], "tags": null}, {"context": {}, "expected": null, "id": "41e861493b0d41d173b3d23b7caede1314732233962c3bb69b06a80651759bd3", "input": [{"content": "What is 1+1", "role": "user"}], "is_root": false, "metadata": {"model": "claude-3-haiku-20240307", "provider": "openai", "stream": true}, "metrics": {}, "origin": null, "output": [{"index": 0, "logprobs": null, "message": {"content": "1 + 1 = 2", "role": "assistant"}}], "root_span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "41e861493b0d41d173b3d23b7caede1314732233962c3bb69b06a80651759bd3", "span_parents": ["020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a"], "tags": null}, {"context": {}, "expected": null, "id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "span_parents": null, "tags": null}]