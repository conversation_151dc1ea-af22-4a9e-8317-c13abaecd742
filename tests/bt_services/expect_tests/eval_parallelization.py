import time
from threading import Condition

from braintrust import Eval
from braintrust.framework import set_thread_pool_max_workers

from autoevals import Score

MAX_WORKERS = 8
set_thread_pool_max_workers(MAX_WORKERS)
TIMEOUT_SECONDS = 5


class SnapshotMaxCounter:
    def __init__(self):
        self.counter = 0
        self.cv = Condition()
        self.max_counter = self.counter

    def incr(self):
        with self.cv:
            self.counter += 1
            self.max_counter = max(self.max_counter, self.counter)
            if self.max_counter == MAX_WORKERS:
                self.cv.notify_all()

    def decr(self):
        with self.cv:
            self.counter -= 1

    def get_max_counter(self):
        with self.cv:
            return self.max_counter


score_counter = SnapshotMaxCounter()


def max_counter_scorer(input, output, expected):
    score_counter.incr()
    try:
        if score_counter.get_max_counter() != MAX_WORKERS:
            # Wait for all the other threads to increment their counters up to
            # MAX_WORKERS.
            with score_counter.cv:
                score_counter.cv.wait(timeout=TIMEOUT_SECONDS)
        max_counter = score_counter.get_max_counter()
        return Score(name="max_counter_scorer", score=max_counter / MAX_WORKERS)
    finally:
        score_counter.decr()


task_counter = SnapshotMaxCounter()


def max_counter_task(input, hooks):
    task_counter.incr()
    try:
        if task_counter.get_max_counter() != MAX_WORKERS:
            # Wait for all the other threads to increment their counters up to
            # MAX_WORKERS.
            with task_counter.cv:
                task_counter.cv.wait(timeout=TIMEOUT_SECONDS)
        max_counter = task_counter.get_max_counter()
        return max_counter
    finally:
        task_counter.decr()


Eval(
    "parallel",
    data=lambda: [dict(input=i) for i in range(4)],
    task=max_counter_task,
    scores=[max_counter_scorer],
    experiment_name="parallel one",
)

Eval(
    "parallel",
    data=lambda: [dict(input=i) for i in range(4)],
    task=max_counter_task,
    scores=[max_counter_scorer],
    experiment_name="parallel two",
)
