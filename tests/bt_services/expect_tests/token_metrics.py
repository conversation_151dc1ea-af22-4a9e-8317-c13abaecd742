from braintrust import Eva<PERSON>, wrap_openai
from openai import OpenAI

from autoevals import Factuality

result = Eval(
    "test_token_metrics",
    data=[{"input": "a", "expected": "b"}],
    task=lambda input: "b",
    scores=[Factuality],
)

assert result.summary.metrics.get("prompt_tokens").metric == 0
assert result.summary.metrics.get("completion_tokens").metric == 0
assert result.summary.metrics.get("total_tokens").metric == 0
assert result.summary.metrics.get("llm_duration") is None

client = wrap_openai(OpenAI())


def task1(input):
    return (
        client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": "next letter after " + input}],
        )
        .choices[0]
        .message.content
    )


result = Eval("test_token_metrics", data=[{"input": "a", "expected": "b"}], task=task1, scores=[Factuality])

print(result)
prompt_tokens_1 = result.summary.metrics.get("prompt_tokens").metric
assert prompt_tokens_1 > 0


def task2(input):
    return (
        client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": "print the next letter after " + input}],
        )
        .choices[0]
        .message.content
    )


result = Eval("test_token_metrics", data=[{"input": "a", "expected": "b"}], task=task2, scores=[Factuality])

print(result)
prompt_tokens_2 = result.summary.metrics.get("prompt_tokens").metric
assert prompt_tokens_2 > prompt_tokens_1

assert result.summary.metrics.get("completion_tokens").metric > 0
assert result.summary.metrics.get("total_tokens").metric > 0
