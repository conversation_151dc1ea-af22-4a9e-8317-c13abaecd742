import { initLogger, wrap<PERSON>penA<PERSON> } from "braintrust";
import OpenAI from "openai";

initLogger({ projectName: "wrap_openai_with_response" });

const openai = wrapOpenAI(new OpenAI());

async function run() {
  const promise = openai.chat.completions.create({
    model: "gpt-4.1",
    messages: [
      { role: "user", content: "Stream a very long response and then stop." },
    ],
    stream: true,
    stream_options: { include_usage: true },
  });

  const { data, response } = await promise.withResponse();

  for await (const chunk of data) {
    console.log(chunk?.choices?.[0]?.delta?.content);
  }
  process.stdout.write("\n");

  const headers = response.headers;
  console.log(headers);
}

run();
