from braintrust import Eval, current_span, traced

from autoevals import LevenshteinScorer


def basic_task(input, hooks):
    return input


def null_score(output, expected):
    return None


def number_score(output, expected):
    return 0.5


Eval(
    "basic-error",
    data=lambda: [dict(input="a", expected="b")],
    task=basic_task,
    scores=[null_score, number_score],
    experiment_name="My basic eval",
)
