import {
  AnswerCorrectness,
  AnswerRelevancy,
  AnswerSimilarity,
  ContextPrecision,
  ContextRecall,
  ContextRelevancy,
  Faithfulness,
} from "autoevals";

async function main() {
  const apiKey = process.env.OPENAI_API_KEY;
  process.env.OPENAI_API_KEY = "<bogus>";
  const contextRelevancyScore = await ContextRelevancy({
    input: "What is the capital of France?",
    output: "The capital of France is Paris.",
    context: "The capital of France is Paris.",
    expected: "The capital of France is Paris.",
    openAiApiKey: apiKey,
  });
  console.log(contextRelevancyScore);
  const answerRelevancyScore = await AnswerRelevancy({
    input: "What is the capital of France?",
    output: "The capital of France is Paris.",
    context: "The capital of France is Paris.",
    openAiApiKey: apiKey,
  });
  console.log(answerRelevancyScore);
  const contextRecallScore = await ContextRecall({
    input: "What is the capital of France?",
    output: "The capital of France is Paris.",
    context: "Paris is the capital and most populous city of France.",
    expected: "The capital of France is Paris.",
    openAiApiKey: apiKey,
  });
  console.log(contextRecallScore);

  const contextPrecisionScore = await ContextPrecision({
    input: "What is the capital of France?",
    output: "The capital of France is Paris.",
    context: "Paris is the capital and most populous city of France.",
    expected: "The capital of France is Paris.",
    openAiApiKey: apiKey,
  });
  console.log(contextPrecisionScore);

  const faithfulnessScore = await Faithfulness({
    input: "What is the capital of France?",
    output: "The capital of France is Paris.",
    context: "Paris is the capital and most populous city of France.",
    expected: "The capital of France is Paris.",
    openAiApiKey: apiKey,
  });
  console.log(faithfulnessScore);

  const answerSimilarityScore = await AnswerSimilarity({
    output: "The capital of France is Paris.",
    expected: "Paris is the capital city of France.",
    openAiApiKey: apiKey,
  });
  console.log(answerSimilarityScore);

  const answerCorrectnessScore = await AnswerCorrectness({
    input: "What is the capital of France?",
    output: "The capital of France is Paris.",
    expected: "Paris is the capital and largest city of France.",
    openAiApiKey: apiKey,
  });
  console.log(answerCorrectnessScore);
}

main();
