import braintrust

logger = braintrust.init_logger("test project")


@braintrust.traced
def some_llm(input):
    return input + " foo!"


@braintrust.traced(notrace_io=True)
def my_request_handler(input):
    result = some_llm(input)

    return {"result": result, "request_id": braintrust.current_span().export()}


def my_feedback_handler(request_id, score):
    with logger.start_span("feedback", parent=request_id) as span:
        if score < 0.5:
            # This should be recommended approach in the docs, because then the "External user"
            # creates the span.
            span.log_feedback(scores={"foo": score}, comment="This sucks!")
        else:
            span.log(scores={"foo": score})
            span.log_feedback(comment="This is great!")


if __name__ == "__main__":
    with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
        for flush_between in [False, True]:
            if not flush_between:
                custom_bg_logger.sync_flush = True

            response = my_request_handler("hello")

            if flush_between:
                logger.flush()

            for i in range(5):
                my_feedback_handler(response["request_id"], i / 5)

            if not flush_between:
                custom_bg_logger.sync_flush = False
