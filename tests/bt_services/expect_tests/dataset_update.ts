import { initDataset } from "braintrust";

console.log("Creating dataset");

async function main() {
  const dataset = initDataset("dataset_update_ts");
  const n = 2;

  for (let i = 0; i < n; i++) {
    dataset.insert({
      input: i,
      output: { result: i + 1, error: null },
      metadata: {
        foo: i % 2,
        bar: "baz",
      },
      tags: ["foo", "bar"],
      id: i.toString(),
    });
  }
  await dataset.flush();

  for (let i = 0; i < n; i++) {
    dataset.update({
      input: i,
      metadata: {
        baz: "UPDATE",
      },
      id: i.toString(),
    });
  }

  await dataset.flush();
  for await (const data of dataset) {
    console.log(data);
  }

  const summary = await dataset.summarize();
  console.log("Summary: ", summary);
  console.log("Total records: ", summary.dataSummary?.totalRecords);
}

main();
