[{"context": {}, "expected": "0", "id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "span_parents": ["18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984"], "tags": null}, {"context": {}, "expected": null, "id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "span_parents": ["80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20"], "tags": null}, {"context": {}, "expected": null, "id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "span_parents": ["c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37"], "tags": null}, {"context": {}, "expected": null, "id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "span_parents": ["6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd"], "tags": null}, {"context": {}, "expected": "1", "id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d"], "tags": null}, {"context": {}, "expected": null, "id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "span_parents": ["68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786"], "tags": null}, {"context": {}, "expected": null, "id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "span_parents": ["db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203"], "tags": null}, {"context": {}, "expected": null, "id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "span_parents": ["75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085"], "tags": null}, {"context": {}, "expected": "2", "id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "span_parents": ["2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa"], "tags": null}, {"context": {}, "expected": null, "id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "span_parents": ["44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915"], "tags": null}, {"context": {}, "expected": null, "id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "span_parents": ["db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162"], "tags": null}, {"context": {}, "expected": "3", "id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "span_parents": ["1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322"], "tags": null}, {"context": {}, "expected": null, "id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "span_parents": ["6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db"], "tags": null}, {"context": {}, "expected": null, "id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "span_parents": ["5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71"], "tags": null}, {"context": {}, "expected": null, "id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "span_parents": ["6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b"], "tags": null}, {"context": {}, "expected": "4", "id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "span_parents": ["d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e"], "tags": null}, {"context": {}, "expected": null, "id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "span_parents": ["7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae"], "tags": null}, {"context": {}, "expected": null, "id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "span_parents": ["eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35"], "tags": null}, {"context": {}, "expected": null, "id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "span_parents": ["9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393"], "tags": null}, {"context": {}, "expected": null, "id": "5e805381d01a90e1ea57c70133895637f2e5fa6b5332f998295ec8ab3fdd6ec2", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "5e805381d01a90e1ea57c70133895637f2e5fa6b5332f998295ec8ab3fdd6ec2", "span_parents": ["6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd"], "tags": null}, {"context": {}, "expected": null, "id": "a724ca0f2374ffe80621a73a85b10abaafc974e6c53ba791d7e1a8c4f26acff0", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "a724ca0f2374ffe80621a73a85b10abaafc974e6c53ba791d7e1a8c4f26acff0", "span_parents": ["6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd"], "tags": null}, {"context": {}, "expected": null, "id": "984d6165f57fece557633c12db27d6583b5c673575ad8c76849f379a5afe9fd6", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "984d6165f57fece557633c12db27d6583b5c673575ad8c76849f379a5afe9fd6", "span_parents": ["18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984"], "tags": null}, {"context": {}, "expected": null, "id": "dc9919f6f07fed76b763ae7250c7e6faaf0f2cc174ee9d04cb8bfd9c3aba8780", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "dc9919f6f07fed76b763ae7250c7e6faaf0f2cc174ee9d04cb8bfd9c3aba8780", "span_parents": ["18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984"], "tags": null}, {"context": {}, "expected": null, "id": "8698f8e8299b9562c26ad46ac6f6bacac160181a1907a48c2930759a132ce908", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "8698f8e8299b9562c26ad46ac6f6bacac160181a1907a48c2930759a132ce908", "span_parents": ["80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20"], "tags": null}, {"context": {}, "expected": null, "id": "904c7be31b61a4a0082e0a5e7c76446cd91145ff56918b8c3f90ab0b2f98a9de", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "904c7be31b61a4a0082e0a5e7c76446cd91145ff56918b8c3f90ab0b2f98a9de", "span_parents": ["80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20"], "tags": null}, {"context": {}, "expected": null, "id": "9e26d41d3b623bccf8e2a86236831687958af4b4e64374850f25b44e953b566d", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "9e26d41d3b623bccf8e2a86236831687958af4b4e64374850f25b44e953b566d", "span_parents": ["c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37"], "tags": null}, {"context": {}, "expected": null, "id": "dfe82212d0c2eed11916480b1ccf1e16de8163a09627dca87a3ac6c47e4dc2e7", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "dfe82212d0c2eed11916480b1ccf1e16de8163a09627dca87a3ac6c47e4dc2e7", "span_parents": ["c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37"], "tags": null}, {"context": {}, "expected": null, "id": "62bf885319d019fee1da45a8c4e9baf68e3d50952db7a1682ddb8ccc9cc170c6", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "62bf885319d019fee1da45a8c4e9baf68e3d50952db7a1682ddb8ccc9cc170c6", "span_parents": ["75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085"], "tags": null}, {"context": {}, "expected": null, "id": "e67a703c9a3c1ee44ab0a50d3ddca8957b6641eacf39655075f391d110f26e05", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e67a703c9a3c1ee44ab0a50d3ddca8957b6641eacf39655075f391d110f26e05", "span_parents": ["75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085"], "tags": null}, {"context": {}, "expected": null, "id": "4347230e83c32921501236728ad4eae361abae760ba7b0de4f15674039dfcae2", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "4347230e83c32921501236728ad4eae361abae760ba7b0de4f15674039dfcae2", "span_parents": ["db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203"], "tags": null}, {"context": {}, "expected": null, "id": "c59d6466ab34f2c457ded9171784dd670a0b4b182f3c6ee1dd8419bc236e63b4", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "c59d6466ab34f2c457ded9171784dd670a0b4b182f3c6ee1dd8419bc236e63b4", "span_parents": ["db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203"], "tags": null}, {"context": {}, "expected": null, "id": "3077b2f403ecaf4be2f72ce5c5c050d776439b494fce6d445cbff50ff0d77575", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "3077b2f403ecaf4be2f72ce5c5c050d776439b494fce6d445cbff50ff0d77575", "span_parents": ["1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d"], "tags": null}, {"context": {}, "expected": null, "id": "d5a3e8aa9150de3a04c3329203ffc6f1fd32a6808b90bdeb04da58608279afd5", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "d5a3e8aa9150de3a04c3329203ffc6f1fd32a6808b90bdeb04da58608279afd5", "span_parents": ["1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d"], "tags": null}, {"context": {}, "expected": null, "id": "b23c4701a39500d1466c7a96b0066baf3fd6660e7c664c648c8f292e5ff1db79", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "b23c4701a39500d1466c7a96b0066baf3fd6660e7c664c648c8f292e5ff1db79", "span_parents": ["68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786"], "tags": null}, {"context": {}, "expected": null, "id": "b8a3763d78d7578755c9f82e78524268405021370c7b06a13860df94feed3bb0", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "b8a3763d78d7578755c9f82e78524268405021370c7b06a13860df94feed3bb0", "span_parents": ["68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786"], "tags": null}, {"context": {}, "expected": null, "id": "1f2f0ec14d7a010a8698506583d0150557791487f4ba88ff960ab5c6427d0327", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1f2f0ec14d7a010a8698506583d0150557791487f4ba88ff960ab5c6427d0327", "span_parents": ["2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a"], "tags": null}, {"context": {}, "expected": null, "id": "66decc56636e28b5335f409e20ed606c63324bad2172f203c6c7f8b021082803", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "66decc56636e28b5335f409e20ed606c63324bad2172f203c6c7f8b021082803", "span_parents": ["2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a"], "tags": null}, {"context": {}, "expected": null, "id": "68eb150147c7230c405b2b2781bca1172d667c6de5f29312a196a1b9a4aa9d79", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "68eb150147c7230c405b2b2781bca1172d667c6de5f29312a196a1b9a4aa9d79", "span_parents": ["db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162"], "tags": null}, {"context": {}, "expected": null, "id": "f847e654b70da383fd5045c52e247a70bba7314b8aec4e8e2c4c2304e11235ba", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "f847e654b70da383fd5045c52e247a70bba7314b8aec4e8e2c4c2304e11235ba", "span_parents": ["db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162"], "tags": null}, {"context": {}, "expected": null, "id": "060862acebd788ef112f996f520f695fd846f6184c1ffb829f1c1624c84a1be1", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "060862acebd788ef112f996f520f695fd846f6184c1ffb829f1c1624c84a1be1", "span_parents": ["44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915"], "tags": null}, {"context": {}, "expected": null, "id": "e4a4a67fa85c151438e56bf6d84b092f848fb01662360bbcfe7321fe10ce2753", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e4a4a67fa85c151438e56bf6d84b092f848fb01662360bbcfe7321fe10ce2753", "span_parents": ["44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915"], "tags": null}, {"context": {}, "expected": null, "id": "0a496b8cc091039fa841fe1e93b667b8686866584733fe0fd7aefba0f5ef58d3", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0a496b8cc091039fa841fe1e93b667b8686866584733fe0fd7aefba0f5ef58d3", "span_parents": ["3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa"], "tags": null}, {"context": {}, "expected": null, "id": "d07a72204d0b4cf498560c5086dc878488ed07b912d6cdbe8800d37cef140c2a", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "d07a72204d0b4cf498560c5086dc878488ed07b912d6cdbe8800d37cef140c2a", "span_parents": ["3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa"], "tags": null}, {"context": {}, "expected": null, "id": "2864786d72bf3e3811a37462698bc943c8eb4f78a8e2eb4c387e458497d51785", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2864786d72bf3e3811a37462698bc943c8eb4f78a8e2eb4c387e458497d51785", "span_parents": ["6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db"], "tags": null}, {"context": {}, "expected": null, "id": "9cf4c56fe74180dd536a28497f2c1eb4db93e82c772fb693dd49f2b580cc8451", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "9cf4c56fe74180dd536a28497f2c1eb4db93e82c772fb693dd49f2b580cc8451", "span_parents": ["6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db"], "tags": null}, {"context": {}, "expected": null, "id": "ba08687bd7815df0549f1a49e2695eb3a1d77ca9f4491ec7ba3a2098ad73f74b", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ba08687bd7815df0549f1a49e2695eb3a1d77ca9f4491ec7ba3a2098ad73f74b", "span_parents": ["6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b"], "tags": null}, {"context": {}, "expected": null, "id": "edd0f3bfdd5223e10570772d9b4ee76c8e52caeaa8b26b375a1143e1c98152df", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "edd0f3bfdd5223e10570772d9b4ee76c8e52caeaa8b26b375a1143e1c98152df", "span_parents": ["6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b"], "tags": null}, {"context": {}, "expected": null, "id": "10a5e375f5bd461bee5fab16c2c6e588443937f674e47afc81fe24076a9247ea", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "10a5e375f5bd461bee5fab16c2c6e588443937f674e47afc81fe24076a9247ea", "span_parents": ["1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322"], "tags": null}, {"context": {}, "expected": null, "id": "40b6db16b440d1ee239f69aa13552a4209b4a32c6f2b96aedde343b9bfca5aed", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "40b6db16b440d1ee239f69aa13552a4209b4a32c6f2b96aedde343b9bfca5aed", "span_parents": ["1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322"], "tags": null}, {"context": {}, "expected": null, "id": "12d312be197bf066c13701fdb89e71e72daafdc66b0ec0b2abb153308b5dd8b5", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "12d312be197bf066c13701fdb89e71e72daafdc66b0ec0b2abb153308b5dd8b5", "span_parents": ["5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71"], "tags": null}, {"context": {}, "expected": null, "id": "804dbf596ccbc09d78fcb3363c1686ab704d8fd459a1fc48072f67b143ec7001", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "804dbf596ccbc09d78fcb3363c1686ab704d8fd459a1fc48072f67b143ec7001", "span_parents": ["5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71"], "tags": null}, {"context": {}, "expected": null, "id": "1b7dc3a915dc48371d8d44bb10b4fdefafc12424e1ddde91093f7096f03d9ff8", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "1b7dc3a915dc48371d8d44bb10b4fdefafc12424e1ddde91093f7096f03d9ff8", "span_parents": ["9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393"], "tags": null}, {"context": {}, "expected": null, "id": "20284c53d3cf020e39094d2c25316147cdd9bbda5efbd9248d85711883a180db", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "20284c53d3cf020e39094d2c25316147cdd9bbda5efbd9248d85711883a180db", "span_parents": ["9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393"], "tags": null}, {"context": {}, "expected": null, "id": "9812d07b5207f15e13829d51ee230856f83d0eae1373aa2f1417a044cc2878c4", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "9812d07b5207f15e13829d51ee230856f83d0eae1373aa2f1417a044cc2878c4", "span_parents": ["d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e"], "tags": null}, {"context": {}, "expected": null, "id": "fdb6dab92e698b513c2595e728824f7bfb4c6122d2c82b4e25a5f78c0f1c4bfe", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "fdb6dab92e698b513c2595e728824f7bfb4c6122d2c82b4e25a5f78c0f1c4bfe", "span_parents": ["d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e"], "tags": null}, {"context": {}, "expected": null, "id": "6e18d900c65c26d44ec27b623926d8fb84d516620389ff90353f3a19f0b526b0", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "6e18d900c65c26d44ec27b623926d8fb84d516620389ff90353f3a19f0b526b0", "span_parents": ["7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae"], "tags": null}, {"context": {}, "expected": null, "id": "78df30ef5754fc6a1b2aa12da3f944ffb64ca3f2fef49f823c6fff136425b369", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "78df30ef5754fc6a1b2aa12da3f944ffb64ca3f2fef49f823c6fff136425b369", "span_parents": ["7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae"], "tags": null}, {"context": {}, "expected": null, "id": "8b2be0629bf58cebc803cff797f6243f737b168e1dd11aa8840453755cdaae57", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "8b2be0629bf58cebc803cff797f6243f737b168e1dd11aa8840453755cdaae57", "span_parents": ["eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35"], "tags": null}, {"context": {}, "expected": null, "id": "8cc06f148edd1bc299173048fd52e7786c2874a134e6f8647dcb8a801ece8452", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "8cc06f148edd1bc299173048fd52e7786c2874a134e6f8647dcb8a801ece8452", "span_parents": ["eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35"], "tags": null}]