import { wrapOpenA<PERSON>, init } from "braintrust";
import { OpenAI } from "openai";

const client = wrapOpenAI(new OpenAI());

async function main() {
  const experiment = init("openai_wrapper_streaming");

  const chatCompletion = await client.chat.completions.create({
    messages: [{ role: "user", content: "Say this is a test" }],
    model: "gpt-3.5-turbo",
    stream: true,
  });
  const stream = chatCompletion.toReadableStream();
  // @ts-ignore
  // calling toReadableStream on this example also fails typechecking
  // https://github.com/openai/openai-node/blob/6175eca426b15990be5e5cdb0e8497e547f87d8a/README.md#streaming-responses
  for await (const chunk of stream) {
    // Do something with each 'chunk'
    const utf8decoder = new TextDecoder();
    console.log(utf8decoder.decode(chunk));
  }

  console.log(JSON.stringify(chatCompletion));

  console.log(await experiment.summarize());
}

main();
